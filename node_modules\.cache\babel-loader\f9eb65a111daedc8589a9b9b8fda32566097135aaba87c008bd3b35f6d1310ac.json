{"ast": null, "code": "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { ENGINE } from '../engine';\nimport { LogicalNot } from '../kernel_names';\nimport { convertToTensor } from '../tensor_util_env';\nimport { op } from './operation';\n/**\n * Returns the truth value of `NOT x` element-wise.\n *\n * ```js\n * const a = tf.tensor1d([false, true], 'bool');\n *\n * a.logicalNot().print();\n * ```\n *\n * @param x The input tensor. Must be of dtype 'bool'.\n *\n * @doc {heading: 'Operations', subheading: 'Logical'}\n */\nfunction logicalNot_(x) {\n  const $x = convertToTensor(x, 'x', 'logicalNot', 'bool');\n  const inputs = {\n    x: $x\n  };\n  return ENGINE.runKernel(LogicalNot, inputs);\n}\nexport const logicalNot = /* @__PURE__ */op({\n  logicalNot_\n});", "map": {"version": 3, "names": ["ENGINE", "LogicalNot", "convertToTensor", "op", "logicalNot_", "x", "$x", "inputs", "runKernel", "logicalNot"], "sources": ["C:\\tfjs-core\\src\\ops\\logical_not.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {ENGINE} from '../engine';\nimport {LogicalNot, LogicalNotInputs} from '../kernel_names';\nimport {Tensor} from '../tensor';\nimport {NamedTensorMap} from '../tensor_types';\nimport {convertToTensor} from '../tensor_util_env';\nimport {TensorLike} from '../types';\nimport {op} from './operation';\n\n/**\n * Returns the truth value of `NOT x` element-wise.\n *\n * ```js\n * const a = tf.tensor1d([false, true], 'bool');\n *\n * a.logicalNot().print();\n * ```\n *\n * @param x The input tensor. Must be of dtype 'bool'.\n *\n * @doc {heading: 'Operations', subheading: 'Logical'}\n */\nfunction logicalNot_<T extends Tensor>(x: T|TensorLike): T {\n  const $x = convertToTensor(x, 'x', 'logicalNot', 'bool');\n  const inputs: LogicalNotInputs = {x: $x};\n  return ENGINE.runKernel(LogicalNot, inputs as unknown as NamedTensorMap);\n}\n\nexport const logicalNot = /* @__PURE__ */ op({logicalNot_});\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAiBA,SAAQA,MAAM,QAAO,WAAW;AAChC,SAAQC,UAAU,QAAyB,iBAAiB;AAG5D,SAAQC,eAAe,QAAO,oBAAoB;AAElD,SAAQC,EAAE,QAAO,aAAa;AAE9B;;;;;;;;;;;;;AAaA,SAASC,WAAWA,CAAmBC,CAAe;EACpD,MAAMC,EAAE,GAAGJ,eAAe,CAACG,CAAC,EAAE,GAAG,EAAE,YAAY,EAAE,MAAM,CAAC;EACxD,MAAME,MAAM,GAAqB;IAACF,CAAC,EAAEC;EAAE,CAAC;EACxC,OAAON,MAAM,CAACQ,SAAS,CAACP,UAAU,EAAEM,MAAmC,CAAC;AAC1E;AAEA,OAAO,MAAME,UAAU,GAAG,eAAgBN,EAAE,CAAC;EAACC;AAAW,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}