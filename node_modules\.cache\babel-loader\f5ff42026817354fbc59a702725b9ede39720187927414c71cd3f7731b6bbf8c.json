{"ast": null, "code": "/*\nCopyright (c) 2014, <PERSON><PERSON><PERSON> is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in\nall copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\nTHE SOFTWARE.\n*/\n\n/*\n * Spec for the French Porter Stemmer can be found at:\n * http://snowball.tartarus.org/algorithms/french/stemmer.html\n */\n\n'use strict';\n\nconst Stemmer = require('./stemmer_fr');\nconst PorterStemmer = new Stemmer();\nmodule.exports = PorterStemmer;\n\n// Export\nPorterStemmer.stem = stem;\n\n// Exports for test purpose\nPorterStemmer.prelude = prelude;\nPorterStemmer.regions = regions;\nPorterStemmer.endsinArr = endsinArr;\n\n/**\n * Stem a word thanks to Porter Stemmer rules\n * @param  {String} token Word to be stemmed\n * @return {String}       Stemmed word\n */\nfunction stem(token) {\n  token = prelude(token.toLowerCase());\n  if (token.length === 1) {\n    return token;\n  }\n  const regs = regions(token);\n  let r1txt, r2txt, rvtxt;\n  r1txt = token.substring(regs.r1);\n  r2txt = token.substring(regs.r2);\n  rvtxt = token.substring(regs.rv);\n\n  // Step 1\n  const beforeStep1 = token;\n  let suf, letterBefore, letter2Before, i;\n  let doStep2a = false;\n  if ((suf = endsinArr(r2txt, ['ance', 'iqUe', 'isme', 'able', 'iste', 'eux', 'ances', 'iqUes', 'ismes', 'ables', 'istes'])) !== '') {\n    token = token.slice(0, -suf.length); // delete\n  } else if ((suf = endsinArr(token, ['icatrice', 'icateur', 'ication', 'icatrices', 'icateurs', 'ications'])) !== '') {\n    if (endsinArr(r2txt, ['icatrice', 'icateur', 'ication', 'icatrices', 'icateurs', 'ications']) !== '') {\n      token = token.slice(0, -suf.length); // delete\n    } else {\n      token = token.slice(0, -suf.length) + 'iqU'; // replace by iqU\n    }\n  } else if ((suf = endsinArr(r2txt, ['atrice', 'ateur', 'ation', 'atrices', 'ateurs', 'ations'])) !== '') {\n    token = token.slice(0, -suf.length); // delete\n  } else if ((suf = endsinArr(r2txt, ['logie', 'logies'])) !== '') {\n    token = token.slice(0, -suf.length) + 'log'; // replace with log\n  } else if ((suf = endsinArr(r2txt, ['usion', 'ution', 'usions', 'utions'])) !== '') {\n    token = token.slice(0, -suf.length) + 'u'; // replace with u\n  } else if ((suf = endsinArr(r2txt, ['ence', 'ences'])) !== '') {\n    token = token.slice(0, -suf.length) + 'ent'; // replace with ent\n  } else if ((suf = endsinArr(r1txt, ['issement', 'issements'])) !== '') {\n    if (!isVowel(token[token.length - suf.length - 1])) {\n      token = token.slice(0, -suf.length); // delete\n      r1txt = token.substring(regs.r1);\n      r2txt = token.substring(regs.r2);\n      rvtxt = token.substring(regs.rv);\n    }\n  } else if ((suf = endsinArr(r2txt, ['ativement', 'ativements'])) !== '') {\n    token = token.slice(0, -suf.length); // delete\n  } else if ((suf = endsinArr(r2txt, ['ivement', 'ivements'])) !== '') {\n    token = token.slice(0, -suf.length); // delete\n  } else if ((suf = endsinArr(token, ['eusement', 'eusements'])) !== '') {\n    if ((suf = endsinArr(r2txt, ['eusement', 'eusements'])) !== '') {\n      token = token.slice(0, -suf.length);\n    } else if ((suf = endsinArr(r1txt, ['eusement', 'eusements'])) !== '') {\n      token = token.slice(0, -suf.length) + 'eux';\n    } else if ((suf = endsinArr(rvtxt, ['ement', 'ements'])) !== '') {\n      token = token.slice(0, -suf.length);\n    } // delete\n  } else if ((suf = endsinArr(r2txt, ['ablement', 'ablements', 'iqUement', 'iqUements'])) !== '') {\n    token = token.slice(0, -suf.length); // delete\n  } else if ((suf = endsinArr(rvtxt, ['ièrement', 'ièrements', 'Ièrement', 'Ièrements'])) !== '') {\n    token = token.slice(0, -suf.length) + 'i'; // replace by i\n  } else if ((suf = endsinArr(rvtxt, ['ement', 'ements'])) !== '') {\n    token = token.slice(0, -suf.length); // delete\n  } else if ((suf = endsinArr(token, ['icité', 'icités'])) !== '') {\n    if (endsinArr(r2txt, ['icité', 'icités']) !== '') {\n      token = token.slice(0, -suf.length);\n    } else {\n      token = token.slice(0, -suf.length) + 'iqU';\n    }\n  } else if ((suf = endsinArr(token, ['abilité', 'abilités'])) !== '') {\n    if (endsinArr(r2txt, ['abilité', 'abilités']) !== '') {\n      token = token.slice(0, -suf.length);\n    } else {\n      token = token.slice(0, -suf.length) + 'abl';\n    }\n  } else if ((suf = endsinArr(r2txt, ['ité', 'ités'])) !== '') {\n    token = token.slice(0, -suf.length); // delete if in R2\n  } else if ((suf = endsinArr(token, ['icatif', 'icative', 'icatifs', 'icatives'])) !== '') {\n    if ((suf = endsinArr(r2txt, ['icatif', 'icative', 'icatifs', 'icatives'])) !== '') {\n      token = token.slice(0, -suf.length); // delete\n      r2txt = token.substring(regs.r2);\n      rvtxt = token.substring(regs.rv);\n    }\n    if ((suf = endsinArr(r2txt, ['atif', 'ative', 'atifs', 'atives'])) !== '') {\n      token = token.slice(0, -suf.length - 2) + 'iqU'; // replace with iqU\n      r2txt = token.substring(regs.r2);\n      rvtxt = token.substring(regs.rv);\n    }\n  } else if ((suf = endsinArr(r2txt, ['atif', 'ative', 'atifs', 'atives'])) !== '') {\n    token = token.slice(0, -suf.length); // delete\n  } else if ((suf = endsinArr(r2txt, ['if', 'ive', 'ifs', 'ives'])) !== '') {\n    token = token.slice(0, -suf.length); // delete\n  } else if ((suf = endsinArr(token, ['eaux'])) !== '') {\n    token = token.slice(0, -suf.length) + 'eau'; // replace by eau\n  } else if ((suf = endsinArr(r1txt, ['aux'])) !== '') {\n    token = token.slice(0, -suf.length) + 'al'; // replace by al\n  } else if ((suf = endsinArr(r2txt, ['euse', 'euses'])) !== '') {\n    token = token.slice(0, -suf.length); // delete\n  } else if ((suf = endsinArr(r1txt, ['euse', 'euses'])) !== '') {\n    token = token.slice(0, -suf.length) + 'eux'; // replace by eux\n  } else if ((suf = endsinArr(rvtxt, ['amment'])) !== '') {\n    token = token.slice(0, -suf.length) + 'ant'; // replace by ant\n    doStep2a = true;\n  } else if ((suf = endsinArr(rvtxt, ['emment'])) !== '') {\n    token = token.slice(0, -suf.length) + 'ent'; // replace by ent\n    doStep2a = true;\n  } else if ((suf = endsinArr(rvtxt, ['ment', 'ments'])) !== '') {\n    // letter before must be a vowel in RV\n    letterBefore = token[token.length - suf.length - 1];\n    if (isVowel(letterBefore) && endsin(rvtxt, letterBefore + suf)) {\n      token = token.slice(0, -suf.length); // delete\n      doStep2a = true;\n    }\n  }\n\n  // re compute regions\n  r1txt = token.substring(regs.r1);\n  r2txt = token.substring(regs.r2);\n  rvtxt = token.substring(regs.rv);\n\n  // Step 2a\n  const beforeStep2a = token;\n  let step2aDone = false;\n  if (beforeStep1 === token || doStep2a) {\n    step2aDone = true;\n    if ((suf = endsinArr(rvtxt, ['îmes', 'ît', 'îtes', 'i', 'ie', 'Ie', 'ies', 'ir', 'ira', 'irai', 'iraIent', 'irais', 'irait', 'iras', 'irent', 'irez', 'iriez', 'irions', 'irons', 'iront', 'is', 'issaIent', 'issais', 'issait', 'issant', 'issante', 'issantes', 'issants', 'isse', 'issent', 'isses', 'issez', 'issiez', 'issions', 'issons', 'it'])) !== '') {\n      letterBefore = token[token.length - suf.length - 1];\n      if (!isVowel(letterBefore) && endsin(rvtxt, letterBefore + suf)) {\n        token = token.slice(0, -suf.length);\n      } // delete\n    }\n  }\n\n  // Step 2b\n  if (step2aDone && token === beforeStep2a) {\n    if ((suf = endsinArr(rvtxt, ['é', 'ée', 'ées', 'és', 'èrent', 'er', 'era', 'erai', 'eraIent', 'erais', 'erait', 'eras', 'erez', 'eriez', 'erions', 'erons', 'eront', 'ez', 'iez', 'Iez'])) !== '') {\n      token = token.slice(0, -suf.length); // delete\n      r2txt = token.substring(regs.r2);\n      rvtxt = token.substring(regs.rv);\n    } else if ((suf = endsinArr(rvtxt, ['ions'])) !== '' && endsinArr(r2txt, ['ions'])) {\n      token = token.slice(0, -suf.length); // delete\n      r2txt = token.substring(regs.r2);\n      rvtxt = token.substring(regs.rv);\n    } else if ((suf = endsinArr(rvtxt, ['âmes', 'ât', 'âtes', 'a', 'ai', 'aIent', 'ais', 'ait', 'ant', 'ante', 'antes', 'ants', 'as', 'asse', 'assent', 'asses', 'assiez', 'assions'])) !== '') {\n      token = token.slice(0, -suf.length); // delete\n\n      letterBefore = token[token.length - 1];\n      if (letterBefore === 'e' && endsin(rvtxt, 'e' + suf)) {\n        token = token.slice(0, -1);\n      }\n      r2txt = token.substring(regs.r2);\n      rvtxt = token.substring(regs.rv);\n    }\n  }\n\n  // Step 3\n  if (!(token === beforeStep1)) {\n    if (token[token.length - 1] === 'Y') {\n      token = token.slice(0, -1) + 'i';\n    }\n    if (token[token.length - 1] === 'ç') {\n      token = token.slice(0, -1) + 'c';\n    }\n  } else {\n    // Step 4\n    letterBefore = token[token.length - 1];\n    letter2Before = token[token.length - 2];\n    if (letterBefore === 's' && ['a', 'i', 'o', 'u', 'è', 's'].indexOf(letter2Before) === -1) {\n      token = token.slice(0, -1);\n      r1txt = token.substring(regs.r1);\n      r2txt = token.substring(regs.r2);\n      rvtxt = token.substring(regs.rv);\n    }\n    if ((suf = endsinArr(r2txt, ['ion'])) !== '') {\n      letterBefore = token[token.length - suf.length - 1];\n      if (letterBefore === 's' || letterBefore === 't') {\n        token = token.slice(0, -suf.length); // delete\n        r1txt = token.substring(regs.r1);\n        r2txt = token.substring(regs.r2);\n        rvtxt = token.substring(regs.rv);\n      }\n    }\n    if ((suf = endsinArr(rvtxt, ['ier', 'ière', 'Ier', 'Ière'])) !== '') {\n      token = token.slice(0, -suf.length) + 'i'; // replace by i\n      r1txt = token.substring(regs.r1);\n      r2txt = token.substring(regs.r2);\n      rvtxt = token.substring(regs.rv);\n    }\n    if ((suf = endsinArr(rvtxt, 'e')) !== '') {\n      token = token.slice(0, -suf.length); // delete\n      r1txt = token.substring(regs.r1);\n      r2txt = token.substring(regs.r2);\n      rvtxt = token.substring(regs.rv);\n    }\n    if ((suf = endsinArr(rvtxt, 'ë')) !== '') {\n      if (token.slice(token.length - 3, -1) === 'gu') {\n        token = token.slice(0, -suf.length);\n      } // delete\n    }\n  }\n\n  // Step 5\n  if ((suf = endsinArr(token, ['enn', 'onn', 'ett', 'ell', 'eill'])) !== '') {\n    token = token.slice(0, -1); // delete last letter\n  }\n\n  // Step 6\n  i = token.length - 1;\n  while (i > 0) {\n    if (!isVowel(token[i])) {\n      i--;\n    } else if (i !== token.length - 1 && (token[i] === 'é' || token[i] === 'è')) {\n      token = token.substring(0, i) + 'e' + token.substring(i + 1, token.length);\n      break;\n    } else {\n      break;\n    }\n  }\n  return token.toLowerCase();\n}\n;\n\n/**\n * Compute r1, r2, rv regions as required by french porter stemmer algorithm\n * @param  {String} token Word to compute regions on\n * @return {Object}       Regions r1, r2, rv as offsets from the begining of the word\n */\nfunction regions(token) {\n  let r1, r2, rv, len;\n  // var i\n\n  r1 = r2 = rv = len = token.length;\n\n  // R1 is the region after the first non-vowel following a vowel,\n  for (let i = 0; i < len - 1 && r1 === len; i++) {\n    if (isVowel(token[i]) && !isVowel(token[i + 1])) {\n      r1 = i + 2;\n    }\n  }\n  // Or is the null region at the end of the word if there is no such non-vowel.\n\n  // R2 is the region after the first non-vowel following a vowel in R1\n  for (let i = r1; i < len - 1 && r2 === len; i++) {\n    if (isVowel(token[i]) && !isVowel(token[i + 1])) {\n      r2 = i + 2;\n    }\n  }\n  // Or is the null region at the end of the word if there is no such non-vowel.\n\n  // RV region\n  const three = token.slice(0, 3);\n  if (isVowel(token[0]) && isVowel(token[1])) {\n    rv = 3;\n  }\n  if (three === 'par' || three === 'col' || three === 'tap') {\n    rv = 3;\n  } else {\n    // the region after the first vowel not at the beginning of the word or null\n    for (let i = 1; i < len - 1 && rv === len; i++) {\n      if (isVowel(token[i])) {\n        rv = i + 1;\n      }\n    }\n  }\n  return {\n    r1: r1,\n    r2: r2,\n    rv: rv\n  };\n}\n;\n\n/**\n * Pre-process/prepare words as required by french porter stemmer algorithm\n * @param  {String} token Word to be prepared\n * @return {String}       Prepared word\n */\nfunction prelude(token) {\n  token = token.toLowerCase();\n  let result = '';\n  let i = 0;\n\n  // special case for i = 0 to avoid '-1' index\n  if (token[i] === 'y' && isVowel(token[i + 1])) {\n    result += token[i].toUpperCase();\n  } else {\n    result += token[i];\n  }\n  for (i = 1; i < token.length; i++) {\n    if ((token[i] === 'u' || token[i] === 'i') && isVowel(token[i - 1]) && isVowel(token[i + 1])) {\n      result += token[i].toUpperCase();\n    } else if (token[i] === 'y' && (isVowel(token[i - 1]) || isVowel(token[i + 1]))) {\n      result += token[i].toUpperCase();\n    } else if (token[i] === 'u' && token[i - 1] === 'q') {\n      result += token[i].toUpperCase();\n    } else {\n      result += token[i];\n    }\n  }\n  return result;\n}\n;\n\n/**\n * Return longest matching suffixes for a token or '' if no suffix match\n * @param  {String} token    Word to find matching suffix\n * @param  {Array} suffixes  Array of suffixes to test matching\n * @return {String}          Longest found matching suffix or ''\n */\nfunction endsinArr(token, suffixes) {\n  let i;\n  let longest = '';\n  for (i = 0; i < suffixes.length; i++) {\n    if (endsin(token, suffixes[i]) && suffixes[i].length > longest.length) {\n      longest = suffixes[i];\n    }\n  }\n  return longest;\n}\n;\nfunction isVowel(letter) {\n  return letter === 'a' || letter === 'e' || letter === 'i' || letter === 'o' || letter === 'u' || letter === 'y' || letter === 'â' || letter === 'à' || letter === 'ë' || letter === 'é' || letter === 'ê' || letter === 'è' || letter === 'ï' || letter === 'î' || letter === 'ô' || letter === 'û' || letter === 'ù';\n}\n;\nfunction endsin(token, suffix) {\n  if (token.length < suffix.length) return false;\n  return token.slice(-suffix.length) === suffix;\n}\n;", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON>", "require", "PorterStemmer", "module", "exports", "stem", "prelude", "regions", "endsinArr", "token", "toLowerCase", "length", "regs", "r1txt", "r2txt", "rvtxt", "substring", "r1", "r2", "rv", "beforeStep1", "suf", "letterBefore", "letter2Before", "i", "doStep2a", "slice", "isVowel", "endsin", "beforeStep2a", "step2aDone", "indexOf", "len", "three", "result", "toUpperCase", "suffixes", "longest", "letter", "suffix"], "sources": ["C:/tmsft/node_modules/natural/lib/natural/stemmers/porter_stemmer_fr.js"], "sourcesContent": ["/*\nCopyright (c) 2014, <PERSON><PERSON><PERSON> is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in\nall copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\nTHE SOFTWARE.\n*/\n\n/*\n * Spec for the French Porter Stemmer can be found at:\n * http://snowball.tartarus.org/algorithms/french/stemmer.html\n */\n\n'use strict'\n\nconst Stemmer = require('./stemmer_fr')\n\nconst PorterStemmer = new Stemmer()\nmodule.exports = PorterStemmer\n\n// Export\nPorterStemmer.stem = stem\n\n// Exports for test purpose\nPorterStemmer.prelude = prelude\nPorterStemmer.regions = regions\nPorterStemmer.endsinArr = endsinArr\n\n/**\n * Stem a word thanks to Porter Stemmer rules\n * @param  {String} token Word to be stemmed\n * @return {String}       Stemmed word\n */\nfunction stem (token) {\n  token = prelude(token.toLowerCase())\n\n  if (token.length === 1) { return token }\n\n  const regs = regions(token)\n\n  let r1txt, r2txt, rvtxt\n  r1txt = token.substring(regs.r1)\n  r2txt = token.substring(regs.r2)\n  rvtxt = token.substring(regs.rv)\n\n  // Step 1\n  const beforeStep1 = token\n  let suf, letterBefore, letter2Before, i\n  let doStep2a = false\n\n  if ((suf = endsinArr(r2txt, ['ance', 'iqUe', 'isme', 'able', 'iste', 'eux', 'ances', 'iqUes', 'ismes', 'ables', 'istes'])) !== '') {\n    token = token.slice(0, -suf.length) // delete\n  } else if ((suf = endsinArr(token, ['icatrice', 'icateur', 'ication', 'icatrices', 'icateurs', 'ications'])) !== '') {\n    if (endsinArr(r2txt, ['icatrice', 'icateur', 'ication', 'icatrices', 'icateurs', 'ications']) !== '') {\n      token = token.slice(0, -suf.length) // delete\n    } else {\n      token = token.slice(0, -suf.length) + 'iqU' // replace by iqU\n    }\n  } else if ((suf = endsinArr(r2txt, ['atrice', 'ateur', 'ation', 'atrices', 'ateurs', 'ations'])) !== '') {\n    token = token.slice(0, -suf.length) // delete\n  } else if ((suf = endsinArr(r2txt, ['logie', 'logies'])) !== '') {\n    token = token.slice(0, -suf.length) + 'log' // replace with log\n  } else if ((suf = endsinArr(r2txt, ['usion', 'ution', 'usions', 'utions'])) !== '') {\n    token = token.slice(0, -suf.length) + 'u' // replace with u\n  } else if ((suf = endsinArr(r2txt, ['ence', 'ences'])) !== '') {\n    token = token.slice(0, -suf.length) + 'ent' // replace with ent\n  } else if ((suf = endsinArr(r1txt, ['issement', 'issements'])) !== '') {\n    if (!isVowel(token[token.length - suf.length - 1])) {\n      token = token.slice(0, -suf.length) // delete\n      r1txt = token.substring(regs.r1)\n      r2txt = token.substring(regs.r2)\n      rvtxt = token.substring(regs.rv)\n    }\n  } else if ((suf = endsinArr(r2txt, ['ativement', 'ativements'])) !== '') {\n    token = token.slice(0, -suf.length) // delete\n  } else if ((suf = endsinArr(r2txt, ['ivement', 'ivements'])) !== '') {\n    token = token.slice(0, -suf.length) // delete\n  } else if ((suf = endsinArr(token, ['eusement', 'eusements'])) !== '') {\n    if ((suf = endsinArr(r2txt, ['eusement', 'eusements'])) !== '') {\n      token = token.slice(0, -suf.length)\n    } else if ((suf = endsinArr(r1txt, ['eusement', 'eusements'])) !== '') {\n      token = token.slice(0, -suf.length) + 'eux'\n    } else if ((suf = endsinArr(rvtxt, ['ement', 'ements'])) !== '') {\n      token = token.slice(0, -suf.length)\n    } // delete\n  } else if ((suf = endsinArr(r2txt, ['ablement', 'ablements', 'iqUement', 'iqUements'])) !== '') {\n    token = token.slice(0, -suf.length) // delete\n  } else if ((suf = endsinArr(rvtxt, ['ièrement', 'ièrements', 'Ièrement', 'Ièrements'])) !== '') {\n    token = token.slice(0, -suf.length) + 'i' // replace by i\n  } else if ((suf = endsinArr(rvtxt, ['ement', 'ements'])) !== '') {\n    token = token.slice(0, -suf.length) // delete\n  } else if ((suf = endsinArr(token, ['icité', 'icités'])) !== '') {\n    if (endsinArr(r2txt, ['icité', 'icités']) !== '') {\n      token = token.slice(0, -suf.length)\n    } else {\n      token = token.slice(0, -suf.length) + 'iqU'\n    }\n  } else if ((suf = endsinArr(token, ['abilité', 'abilités'])) !== '') {\n    if (endsinArr(r2txt, ['abilité', 'abilités']) !== '') {\n      token = token.slice(0, -suf.length)\n    } else {\n      token = token.slice(0, -suf.length) + 'abl'\n    }\n  } else if ((suf = endsinArr(r2txt, ['ité', 'ités'])) !== '') {\n    token = token.slice(0, -suf.length) // delete if in R2\n  } else if ((suf = endsinArr(token, ['icatif', 'icative', 'icatifs', 'icatives'])) !== '') {\n    if ((suf = endsinArr(r2txt, ['icatif', 'icative', 'icatifs', 'icatives'])) !== '') {\n      token = token.slice(0, -suf.length) // delete\n      r2txt = token.substring(regs.r2)\n      rvtxt = token.substring(regs.rv)\n    }\n    if ((suf = endsinArr(r2txt, ['atif', 'ative', 'atifs', 'atives'])) !== '') {\n      token = token.slice(0, -suf.length - 2) + 'iqU' // replace with iqU\n      r2txt = token.substring(regs.r2)\n      rvtxt = token.substring(regs.rv)\n    }\n  } else if ((suf = endsinArr(r2txt, ['atif', 'ative', 'atifs', 'atives'])) !== '') {\n    token = token.slice(0, -suf.length) // delete\n  } else if ((suf = endsinArr(r2txt, ['if', 'ive', 'ifs', 'ives'])) !== '') {\n    token = token.slice(0, -suf.length) // delete\n  } else if ((suf = endsinArr(token, ['eaux'])) !== '') {\n    token = token.slice(0, -suf.length) + 'eau' // replace by eau\n  } else if ((suf = endsinArr(r1txt, ['aux'])) !== '') {\n    token = token.slice(0, -suf.length) + 'al' // replace by al\n  } else if ((suf = endsinArr(r2txt, ['euse', 'euses'])) !== '') {\n    token = token.slice(0, -suf.length) // delete\n  } else if ((suf = endsinArr(r1txt, ['euse', 'euses'])) !== '') {\n    token = token.slice(0, -suf.length) + 'eux' // replace by eux\n  } else if ((suf = endsinArr(rvtxt, ['amment'])) !== '') {\n    token = token.slice(0, -suf.length) + 'ant' // replace by ant\n    doStep2a = true\n  } else if ((suf = endsinArr(rvtxt, ['emment'])) !== '') {\n    token = token.slice(0, -suf.length) + 'ent' // replace by ent\n    doStep2a = true\n  } else if ((suf = endsinArr(rvtxt, ['ment', 'ments'])) !== '') {\n    // letter before must be a vowel in RV\n    letterBefore = token[token.length - suf.length - 1]\n    if (isVowel(letterBefore) && endsin(rvtxt, letterBefore + suf)) {\n      token = token.slice(0, -suf.length) // delete\n      doStep2a = true\n    }\n  }\n\n  // re compute regions\n  r1txt = token.substring(regs.r1)\n  r2txt = token.substring(regs.r2)\n  rvtxt = token.substring(regs.rv)\n\n  // Step 2a\n  const beforeStep2a = token\n  let step2aDone = false\n  if (beforeStep1 === token || doStep2a) {\n    step2aDone = true\n    if ((suf = endsinArr(rvtxt, ['îmes', 'ît', 'îtes', 'i', 'ie', 'Ie', 'ies', 'ir', 'ira', 'irai', 'iraIent', 'irais', 'irait', 'iras', 'irent', 'irez', 'iriez', 'irions', 'irons', 'iront', 'is', 'issaIent', 'issais', 'issait', 'issant', 'issante', 'issantes', 'issants', 'isse', 'issent', 'isses', 'issez', 'issiez', 'issions', 'issons', 'it'])) !== '') {\n      letterBefore = token[token.length - suf.length - 1]\n      if (!isVowel(letterBefore) && endsin(rvtxt, letterBefore + suf)) { token = token.slice(0, -suf.length) } // delete\n    }\n  }\n\n  // Step 2b\n  if (step2aDone && token === beforeStep2a) {\n    if ((suf = endsinArr(rvtxt, ['é', 'ée', 'ées', 'és', 'èrent', 'er', 'era', 'erai', 'eraIent', 'erais', 'erait', 'eras', 'erez', 'eriez', 'erions', 'erons', 'eront', 'ez', 'iez', 'Iez'])) !== '') {\n      token = token.slice(0, -suf.length) // delete\n      r2txt = token.substring(regs.r2)\n      rvtxt = token.substring(regs.rv)\n    } else if ((suf = endsinArr(rvtxt, ['ions'])) !== '' && endsinArr(r2txt, ['ions'])) {\n      token = token.slice(0, -suf.length) // delete\n      r2txt = token.substring(regs.r2)\n      rvtxt = token.substring(regs.rv)\n    } else if ((suf = endsinArr(rvtxt, ['âmes', 'ât', 'âtes', 'a', 'ai', 'aIent', 'ais', 'ait', 'ant', 'ante', 'antes', 'ants', 'as', 'asse', 'assent', 'asses', 'assiez', 'assions'])) !== '') {\n      token = token.slice(0, -suf.length) // delete\n\n      letterBefore = token[token.length - 1]\n      if (letterBefore === 'e' && endsin(rvtxt, 'e' + suf)) { token = token.slice(0, -1) }\n\n      r2txt = token.substring(regs.r2)\n      rvtxt = token.substring(regs.rv)\n    }\n  }\n\n  // Step 3\n  if (!(token === beforeStep1)) {\n    if (token[token.length - 1] === 'Y') { token = token.slice(0, -1) + 'i' }\n    if (token[token.length - 1] === 'ç') { token = token.slice(0, -1) + 'c' }\n  } else {\n    // Step 4\n    letterBefore = token[token.length - 1]\n    letter2Before = token[token.length - 2]\n\n    if (letterBefore === 's' && ['a', 'i', 'o', 'u', 'è', 's'].indexOf(letter2Before) === -1) {\n      token = token.slice(0, -1)\n      r1txt = token.substring(regs.r1)\n      r2txt = token.substring(regs.r2)\n      rvtxt = token.substring(regs.rv)\n    }\n\n    if ((suf = endsinArr(r2txt, ['ion'])) !== '') {\n      letterBefore = token[token.length - suf.length - 1]\n      if (letterBefore === 's' || letterBefore === 't') {\n        token = token.slice(0, -suf.length) // delete\n        r1txt = token.substring(regs.r1)\n        r2txt = token.substring(regs.r2)\n        rvtxt = token.substring(regs.rv)\n      }\n    }\n\n    if ((suf = endsinArr(rvtxt, ['ier', 'ière', 'Ier', 'Ière'])) !== '') {\n      token = token.slice(0, -suf.length) + 'i' // replace by i\n      r1txt = token.substring(regs.r1)\n      r2txt = token.substring(regs.r2)\n      rvtxt = token.substring(regs.rv)\n    }\n    if ((suf = endsinArr(rvtxt, 'e')) !== '') {\n      token = token.slice(0, -suf.length) // delete\n      r1txt = token.substring(regs.r1)\n      r2txt = token.substring(regs.r2)\n      rvtxt = token.substring(regs.rv)\n    }\n    if ((suf = endsinArr(rvtxt, 'ë')) !== '') {\n      if (token.slice(token.length - 3, -1) === 'gu') { token = token.slice(0, -suf.length) } // delete\n    }\n  }\n\n  // Step 5\n  if ((suf = endsinArr(token, ['enn', 'onn', 'ett', 'ell', 'eill'])) !== '') {\n    token = token.slice(0, -1) // delete last letter\n  }\n\n  // Step 6\n  i = token.length - 1\n  while (i > 0) {\n    if (!isVowel(token[i])) {\n      i--\n    } else if (i !== token.length - 1 && (token[i] === 'é' || token[i] === 'è')) {\n      token = token.substring(0, i) + 'e' + token.substring(i + 1, token.length)\n      break\n    } else {\n      break\n    }\n  }\n\n  return token.toLowerCase()\n};\n\n/**\n * Compute r1, r2, rv regions as required by french porter stemmer algorithm\n * @param  {String} token Word to compute regions on\n * @return {Object}       Regions r1, r2, rv as offsets from the begining of the word\n */\nfunction regions (token) {\n  let r1, r2, rv, len\n  // var i\n\n  r1 = r2 = rv = len = token.length\n\n  // R1 is the region after the first non-vowel following a vowel,\n  for (let i = 0; i < len - 1 && r1 === len; i++) {\n    if (isVowel(token[i]) && !isVowel(token[i + 1])) {\n      r1 = i + 2\n    }\n  }\n  // Or is the null region at the end of the word if there is no such non-vowel.\n\n  // R2 is the region after the first non-vowel following a vowel in R1\n  for (let i = r1; i < len - 1 && r2 === len; i++) {\n    if (isVowel(token[i]) && !isVowel(token[i + 1])) {\n      r2 = i + 2\n    }\n  }\n  // Or is the null region at the end of the word if there is no such non-vowel.\n\n  // RV region\n  const three = token.slice(0, 3)\n  if (isVowel(token[0]) && isVowel(token[1])) {\n    rv = 3\n  }\n  if (three === 'par' || three === 'col' || three === 'tap') {\n    rv = 3\n  } else {\n  // the region after the first vowel not at the beginning of the word or null\n    for (let i = 1; i < len - 1 && rv === len; i++) {\n      if (isVowel(token[i])) {\n        rv = i + 1\n      }\n    }\n  }\n\n  return {\n    r1: r1,\n    r2: r2,\n    rv: rv\n  }\n};\n\n/**\n * Pre-process/prepare words as required by french porter stemmer algorithm\n * @param  {String} token Word to be prepared\n * @return {String}       Prepared word\n */\nfunction prelude (token) {\n  token = token.toLowerCase()\n\n  let result = ''\n  let i = 0\n\n  // special case for i = 0 to avoid '-1' index\n  if (token[i] === 'y' && isVowel(token[i + 1])) {\n    result += token[i].toUpperCase()\n  } else {\n    result += token[i]\n  }\n\n  for (i = 1; i < token.length; i++) {\n    if ((token[i] === 'u' || token[i] === 'i') && isVowel(token[i - 1]) && isVowel(token[i + 1])) {\n      result += token[i].toUpperCase()\n    } else if (token[i] === 'y' && (isVowel(token[i - 1]) || isVowel(token[i + 1]))) {\n      result += token[i].toUpperCase()\n    } else if (token[i] === 'u' && token[i - 1] === 'q') {\n      result += token[i].toUpperCase()\n    } else {\n      result += token[i]\n    }\n  }\n\n  return result\n};\n\n/**\n * Return longest matching suffixes for a token or '' if no suffix match\n * @param  {String} token    Word to find matching suffix\n * @param  {Array} suffixes  Array of suffixes to test matching\n * @return {String}          Longest found matching suffix or ''\n */\nfunction endsinArr (token, suffixes) {\n  let i; let longest = ''\n  for (i = 0; i < suffixes.length; i++) {\n    if (endsin(token, suffixes[i]) && suffixes[i].length > longest.length) { longest = suffixes[i] }\n  }\n\n  return longest\n};\n\nfunction isVowel (letter) {\n  return (letter === 'a' || letter === 'e' || letter === 'i' || letter === 'o' || letter === 'u' || letter === 'y' || letter === 'â' || letter === 'à' || letter === 'ë' ||\n    letter === 'é' || letter === 'ê' || letter === 'è' || letter === 'ï' || letter === 'î' || letter === 'ô' || letter === 'û' || letter === 'ù')\n};\n\nfunction endsin (token, suffix) {\n  if (token.length < suffix.length) return false\n  return (token.slice(-suffix.length) === suffix)\n};\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA,YAAY;;AAEZ,MAAMA,OAAO,GAAGC,OAAO,CAAC,cAAc,CAAC;AAEvC,MAAMC,aAAa,GAAG,IAAIF,OAAO,CAAC,CAAC;AACnCG,MAAM,CAACC,OAAO,GAAGF,aAAa;;AAE9B;AACAA,aAAa,CAACG,IAAI,GAAGA,IAAI;;AAEzB;AACAH,aAAa,CAACI,OAAO,GAAGA,OAAO;AAC/BJ,aAAa,CAACK,OAAO,GAAGA,OAAO;AAC/BL,aAAa,CAACM,SAAS,GAAGA,SAAS;;AAEnC;AACA;AACA;AACA;AACA;AACA,SAASH,IAAIA,CAAEI,KAAK,EAAE;EACpBA,KAAK,GAAGH,OAAO,CAACG,KAAK,CAACC,WAAW,CAAC,CAAC,CAAC;EAEpC,IAAID,KAAK,CAACE,MAAM,KAAK,CAAC,EAAE;IAAE,OAAOF,KAAK;EAAC;EAEvC,MAAMG,IAAI,GAAGL,OAAO,CAACE,KAAK,CAAC;EAE3B,IAAII,KAAK,EAAEC,KAAK,EAAEC,KAAK;EACvBF,KAAK,GAAGJ,KAAK,CAACO,SAAS,CAACJ,IAAI,CAACK,EAAE,CAAC;EAChCH,KAAK,GAAGL,KAAK,CAACO,SAAS,CAACJ,IAAI,CAACM,EAAE,CAAC;EAChCH,KAAK,GAAGN,KAAK,CAACO,SAAS,CAACJ,IAAI,CAACO,EAAE,CAAC;;EAEhC;EACA,MAAMC,WAAW,GAAGX,KAAK;EACzB,IAAIY,GAAG,EAAEC,YAAY,EAAEC,aAAa,EAAEC,CAAC;EACvC,IAAIC,QAAQ,GAAG,KAAK;EAEpB,IAAI,CAACJ,GAAG,GAAGb,SAAS,CAACM,KAAK,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC,MAAM,EAAE,EAAE;IACjIL,KAAK,GAAGA,KAAK,CAACiB,KAAK,CAAC,CAAC,EAAE,CAACL,GAAG,CAACV,MAAM,CAAC,EAAC;EACtC,CAAC,MAAM,IAAI,CAACU,GAAG,GAAGb,SAAS,CAACC,KAAK,EAAE,CAAC,UAAU,EAAE,SAAS,EAAE,SAAS,EAAE,WAAW,EAAE,UAAU,EAAE,UAAU,CAAC,CAAC,MAAM,EAAE,EAAE;IACnH,IAAID,SAAS,CAACM,KAAK,EAAE,CAAC,UAAU,EAAE,SAAS,EAAE,SAAS,EAAE,WAAW,EAAE,UAAU,EAAE,UAAU,CAAC,CAAC,KAAK,EAAE,EAAE;MACpGL,KAAK,GAAGA,KAAK,CAACiB,KAAK,CAAC,CAAC,EAAE,CAACL,GAAG,CAACV,MAAM,CAAC,EAAC;IACtC,CAAC,MAAM;MACLF,KAAK,GAAGA,KAAK,CAACiB,KAAK,CAAC,CAAC,EAAE,CAACL,GAAG,CAACV,MAAM,CAAC,GAAG,KAAK,EAAC;IAC9C;EACF,CAAC,MAAM,IAAI,CAACU,GAAG,GAAGb,SAAS,CAACM,KAAK,EAAE,CAAC,QAAQ,EAAE,OAAO,EAAE,OAAO,EAAE,SAAS,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAAC,MAAM,EAAE,EAAE;IACvGL,KAAK,GAAGA,KAAK,CAACiB,KAAK,CAAC,CAAC,EAAE,CAACL,GAAG,CAACV,MAAM,CAAC,EAAC;EACtC,CAAC,MAAM,IAAI,CAACU,GAAG,GAAGb,SAAS,CAACM,KAAK,EAAE,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC,MAAM,EAAE,EAAE;IAC/DL,KAAK,GAAGA,KAAK,CAACiB,KAAK,CAAC,CAAC,EAAE,CAACL,GAAG,CAACV,MAAM,CAAC,GAAG,KAAK,EAAC;EAC9C,CAAC,MAAM,IAAI,CAACU,GAAG,GAAGb,SAAS,CAACM,KAAK,EAAE,CAAC,OAAO,EAAE,OAAO,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAAC,MAAM,EAAE,EAAE;IAClFL,KAAK,GAAGA,KAAK,CAACiB,KAAK,CAAC,CAAC,EAAE,CAACL,GAAG,CAACV,MAAM,CAAC,GAAG,GAAG,EAAC;EAC5C,CAAC,MAAM,IAAI,CAACU,GAAG,GAAGb,SAAS,CAACM,KAAK,EAAE,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC,MAAM,EAAE,EAAE;IAC7DL,KAAK,GAAGA,KAAK,CAACiB,KAAK,CAAC,CAAC,EAAE,CAACL,GAAG,CAACV,MAAM,CAAC,GAAG,KAAK,EAAC;EAC9C,CAAC,MAAM,IAAI,CAACU,GAAG,GAAGb,SAAS,CAACK,KAAK,EAAE,CAAC,UAAU,EAAE,WAAW,CAAC,CAAC,MAAM,EAAE,EAAE;IACrE,IAAI,CAACc,OAAO,CAAClB,KAAK,CAACA,KAAK,CAACE,MAAM,GAAGU,GAAG,CAACV,MAAM,GAAG,CAAC,CAAC,CAAC,EAAE;MAClDF,KAAK,GAAGA,KAAK,CAACiB,KAAK,CAAC,CAAC,EAAE,CAACL,GAAG,CAACV,MAAM,CAAC,EAAC;MACpCE,KAAK,GAAGJ,KAAK,CAACO,SAAS,CAACJ,IAAI,CAACK,EAAE,CAAC;MAChCH,KAAK,GAAGL,KAAK,CAACO,SAAS,CAACJ,IAAI,CAACM,EAAE,CAAC;MAChCH,KAAK,GAAGN,KAAK,CAACO,SAAS,CAACJ,IAAI,CAACO,EAAE,CAAC;IAClC;EACF,CAAC,MAAM,IAAI,CAACE,GAAG,GAAGb,SAAS,CAACM,KAAK,EAAE,CAAC,WAAW,EAAE,YAAY,CAAC,CAAC,MAAM,EAAE,EAAE;IACvEL,KAAK,GAAGA,KAAK,CAACiB,KAAK,CAAC,CAAC,EAAE,CAACL,GAAG,CAACV,MAAM,CAAC,EAAC;EACtC,CAAC,MAAM,IAAI,CAACU,GAAG,GAAGb,SAAS,CAACM,KAAK,EAAE,CAAC,SAAS,EAAE,UAAU,CAAC,CAAC,MAAM,EAAE,EAAE;IACnEL,KAAK,GAAGA,KAAK,CAACiB,KAAK,CAAC,CAAC,EAAE,CAACL,GAAG,CAACV,MAAM,CAAC,EAAC;EACtC,CAAC,MAAM,IAAI,CAACU,GAAG,GAAGb,SAAS,CAACC,KAAK,EAAE,CAAC,UAAU,EAAE,WAAW,CAAC,CAAC,MAAM,EAAE,EAAE;IACrE,IAAI,CAACY,GAAG,GAAGb,SAAS,CAACM,KAAK,EAAE,CAAC,UAAU,EAAE,WAAW,CAAC,CAAC,MAAM,EAAE,EAAE;MAC9DL,KAAK,GAAGA,KAAK,CAACiB,KAAK,CAAC,CAAC,EAAE,CAACL,GAAG,CAACV,MAAM,CAAC;IACrC,CAAC,MAAM,IAAI,CAACU,GAAG,GAAGb,SAAS,CAACK,KAAK,EAAE,CAAC,UAAU,EAAE,WAAW,CAAC,CAAC,MAAM,EAAE,EAAE;MACrEJ,KAAK,GAAGA,KAAK,CAACiB,KAAK,CAAC,CAAC,EAAE,CAACL,GAAG,CAACV,MAAM,CAAC,GAAG,KAAK;IAC7C,CAAC,MAAM,IAAI,CAACU,GAAG,GAAGb,SAAS,CAACO,KAAK,EAAE,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC,MAAM,EAAE,EAAE;MAC/DN,KAAK,GAAGA,KAAK,CAACiB,KAAK,CAAC,CAAC,EAAE,CAACL,GAAG,CAACV,MAAM,CAAC;IACrC,CAAC,CAAC;EACJ,CAAC,MAAM,IAAI,CAACU,GAAG,GAAGb,SAAS,CAACM,KAAK,EAAE,CAAC,UAAU,EAAE,WAAW,EAAE,UAAU,EAAE,WAAW,CAAC,CAAC,MAAM,EAAE,EAAE;IAC9FL,KAAK,GAAGA,KAAK,CAACiB,KAAK,CAAC,CAAC,EAAE,CAACL,GAAG,CAACV,MAAM,CAAC,EAAC;EACtC,CAAC,MAAM,IAAI,CAACU,GAAG,GAAGb,SAAS,CAACO,KAAK,EAAE,CAAC,UAAU,EAAE,WAAW,EAAE,UAAU,EAAE,WAAW,CAAC,CAAC,MAAM,EAAE,EAAE;IAC9FN,KAAK,GAAGA,KAAK,CAACiB,KAAK,CAAC,CAAC,EAAE,CAACL,GAAG,CAACV,MAAM,CAAC,GAAG,GAAG,EAAC;EAC5C,CAAC,MAAM,IAAI,CAACU,GAAG,GAAGb,SAAS,CAACO,KAAK,EAAE,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC,MAAM,EAAE,EAAE;IAC/DN,KAAK,GAAGA,KAAK,CAACiB,KAAK,CAAC,CAAC,EAAE,CAACL,GAAG,CAACV,MAAM,CAAC,EAAC;EACtC,CAAC,MAAM,IAAI,CAACU,GAAG,GAAGb,SAAS,CAACC,KAAK,EAAE,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC,MAAM,EAAE,EAAE;IAC/D,IAAID,SAAS,CAACM,KAAK,EAAE,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC,KAAK,EAAE,EAAE;MAChDL,KAAK,GAAGA,KAAK,CAACiB,KAAK,CAAC,CAAC,EAAE,CAACL,GAAG,CAACV,MAAM,CAAC;IACrC,CAAC,MAAM;MACLF,KAAK,GAAGA,KAAK,CAACiB,KAAK,CAAC,CAAC,EAAE,CAACL,GAAG,CAACV,MAAM,CAAC,GAAG,KAAK;IAC7C;EACF,CAAC,MAAM,IAAI,CAACU,GAAG,GAAGb,SAAS,CAACC,KAAK,EAAE,CAAC,SAAS,EAAE,UAAU,CAAC,CAAC,MAAM,EAAE,EAAE;IACnE,IAAID,SAAS,CAACM,KAAK,EAAE,CAAC,SAAS,EAAE,UAAU,CAAC,CAAC,KAAK,EAAE,EAAE;MACpDL,KAAK,GAAGA,KAAK,CAACiB,KAAK,CAAC,CAAC,EAAE,CAACL,GAAG,CAACV,MAAM,CAAC;IACrC,CAAC,MAAM;MACLF,KAAK,GAAGA,KAAK,CAACiB,KAAK,CAAC,CAAC,EAAE,CAACL,GAAG,CAACV,MAAM,CAAC,GAAG,KAAK;IAC7C;EACF,CAAC,MAAM,IAAI,CAACU,GAAG,GAAGb,SAAS,CAACM,KAAK,EAAE,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC,MAAM,EAAE,EAAE;IAC3DL,KAAK,GAAGA,KAAK,CAACiB,KAAK,CAAC,CAAC,EAAE,CAACL,GAAG,CAACV,MAAM,CAAC,EAAC;EACtC,CAAC,MAAM,IAAI,CAACU,GAAG,GAAGb,SAAS,CAACC,KAAK,EAAE,CAAC,QAAQ,EAAE,SAAS,EAAE,SAAS,EAAE,UAAU,CAAC,CAAC,MAAM,EAAE,EAAE;IACxF,IAAI,CAACY,GAAG,GAAGb,SAAS,CAACM,KAAK,EAAE,CAAC,QAAQ,EAAE,SAAS,EAAE,SAAS,EAAE,UAAU,CAAC,CAAC,MAAM,EAAE,EAAE;MACjFL,KAAK,GAAGA,KAAK,CAACiB,KAAK,CAAC,CAAC,EAAE,CAACL,GAAG,CAACV,MAAM,CAAC,EAAC;MACpCG,KAAK,GAAGL,KAAK,CAACO,SAAS,CAACJ,IAAI,CAACM,EAAE,CAAC;MAChCH,KAAK,GAAGN,KAAK,CAACO,SAAS,CAACJ,IAAI,CAACO,EAAE,CAAC;IAClC;IACA,IAAI,CAACE,GAAG,GAAGb,SAAS,CAACM,KAAK,EAAE,CAAC,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,QAAQ,CAAC,CAAC,MAAM,EAAE,EAAE;MACzEL,KAAK,GAAGA,KAAK,CAACiB,KAAK,CAAC,CAAC,EAAE,CAACL,GAAG,CAACV,MAAM,GAAG,CAAC,CAAC,GAAG,KAAK,EAAC;MAChDG,KAAK,GAAGL,KAAK,CAACO,SAAS,CAACJ,IAAI,CAACM,EAAE,CAAC;MAChCH,KAAK,GAAGN,KAAK,CAACO,SAAS,CAACJ,IAAI,CAACO,EAAE,CAAC;IAClC;EACF,CAAC,MAAM,IAAI,CAACE,GAAG,GAAGb,SAAS,CAACM,KAAK,EAAE,CAAC,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,QAAQ,CAAC,CAAC,MAAM,EAAE,EAAE;IAChFL,KAAK,GAAGA,KAAK,CAACiB,KAAK,CAAC,CAAC,EAAE,CAACL,GAAG,CAACV,MAAM,CAAC,EAAC;EACtC,CAAC,MAAM,IAAI,CAACU,GAAG,GAAGb,SAAS,CAACM,KAAK,EAAE,CAAC,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC,MAAM,EAAE,EAAE;IACxEL,KAAK,GAAGA,KAAK,CAACiB,KAAK,CAAC,CAAC,EAAE,CAACL,GAAG,CAACV,MAAM,CAAC,EAAC;EACtC,CAAC,MAAM,IAAI,CAACU,GAAG,GAAGb,SAAS,CAACC,KAAK,EAAE,CAAC,MAAM,CAAC,CAAC,MAAM,EAAE,EAAE;IACpDA,KAAK,GAAGA,KAAK,CAACiB,KAAK,CAAC,CAAC,EAAE,CAACL,GAAG,CAACV,MAAM,CAAC,GAAG,KAAK,EAAC;EAC9C,CAAC,MAAM,IAAI,CAACU,GAAG,GAAGb,SAAS,CAACK,KAAK,EAAE,CAAC,KAAK,CAAC,CAAC,MAAM,EAAE,EAAE;IACnDJ,KAAK,GAAGA,KAAK,CAACiB,KAAK,CAAC,CAAC,EAAE,CAACL,GAAG,CAACV,MAAM,CAAC,GAAG,IAAI,EAAC;EAC7C,CAAC,MAAM,IAAI,CAACU,GAAG,GAAGb,SAAS,CAACM,KAAK,EAAE,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC,MAAM,EAAE,EAAE;IAC7DL,KAAK,GAAGA,KAAK,CAACiB,KAAK,CAAC,CAAC,EAAE,CAACL,GAAG,CAACV,MAAM,CAAC,EAAC;EACtC,CAAC,MAAM,IAAI,CAACU,GAAG,GAAGb,SAAS,CAACK,KAAK,EAAE,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC,MAAM,EAAE,EAAE;IAC7DJ,KAAK,GAAGA,KAAK,CAACiB,KAAK,CAAC,CAAC,EAAE,CAACL,GAAG,CAACV,MAAM,CAAC,GAAG,KAAK,EAAC;EAC9C,CAAC,MAAM,IAAI,CAACU,GAAG,GAAGb,SAAS,CAACO,KAAK,EAAE,CAAC,QAAQ,CAAC,CAAC,MAAM,EAAE,EAAE;IACtDN,KAAK,GAAGA,KAAK,CAACiB,KAAK,CAAC,CAAC,EAAE,CAACL,GAAG,CAACV,MAAM,CAAC,GAAG,KAAK,EAAC;IAC5Cc,QAAQ,GAAG,IAAI;EACjB,CAAC,MAAM,IAAI,CAACJ,GAAG,GAAGb,SAAS,CAACO,KAAK,EAAE,CAAC,QAAQ,CAAC,CAAC,MAAM,EAAE,EAAE;IACtDN,KAAK,GAAGA,KAAK,CAACiB,KAAK,CAAC,CAAC,EAAE,CAACL,GAAG,CAACV,MAAM,CAAC,GAAG,KAAK,EAAC;IAC5Cc,QAAQ,GAAG,IAAI;EACjB,CAAC,MAAM,IAAI,CAACJ,GAAG,GAAGb,SAAS,CAACO,KAAK,EAAE,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC,MAAM,EAAE,EAAE;IAC7D;IACAO,YAAY,GAAGb,KAAK,CAACA,KAAK,CAACE,MAAM,GAAGU,GAAG,CAACV,MAAM,GAAG,CAAC,CAAC;IACnD,IAAIgB,OAAO,CAACL,YAAY,CAAC,IAAIM,MAAM,CAACb,KAAK,EAAEO,YAAY,GAAGD,GAAG,CAAC,EAAE;MAC9DZ,KAAK,GAAGA,KAAK,CAACiB,KAAK,CAAC,CAAC,EAAE,CAACL,GAAG,CAACV,MAAM,CAAC,EAAC;MACpCc,QAAQ,GAAG,IAAI;IACjB;EACF;;EAEA;EACAZ,KAAK,GAAGJ,KAAK,CAACO,SAAS,CAACJ,IAAI,CAACK,EAAE,CAAC;EAChCH,KAAK,GAAGL,KAAK,CAACO,SAAS,CAACJ,IAAI,CAACM,EAAE,CAAC;EAChCH,KAAK,GAAGN,KAAK,CAACO,SAAS,CAACJ,IAAI,CAACO,EAAE,CAAC;;EAEhC;EACA,MAAMU,YAAY,GAAGpB,KAAK;EAC1B,IAAIqB,UAAU,GAAG,KAAK;EACtB,IAAIV,WAAW,KAAKX,KAAK,IAAIgB,QAAQ,EAAE;IACrCK,UAAU,GAAG,IAAI;IACjB,IAAI,CAACT,GAAG,GAAGb,SAAS,CAACO,KAAK,EAAE,CAAC,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK,EAAE,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,QAAQ,EAAE,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,SAAS,EAAE,UAAU,EAAE,SAAS,EAAE,MAAM,EAAE,QAAQ,EAAE,OAAO,EAAE,OAAO,EAAE,QAAQ,EAAE,SAAS,EAAE,QAAQ,EAAE,IAAI,CAAC,CAAC,MAAM,EAAE,EAAE;MAC9VO,YAAY,GAAGb,KAAK,CAACA,KAAK,CAACE,MAAM,GAAGU,GAAG,CAACV,MAAM,GAAG,CAAC,CAAC;MACnD,IAAI,CAACgB,OAAO,CAACL,YAAY,CAAC,IAAIM,MAAM,CAACb,KAAK,EAAEO,YAAY,GAAGD,GAAG,CAAC,EAAE;QAAEZ,KAAK,GAAGA,KAAK,CAACiB,KAAK,CAAC,CAAC,EAAE,CAACL,GAAG,CAACV,MAAM,CAAC;MAAC,CAAC,CAAC;IAC3G;EACF;;EAEA;EACA,IAAImB,UAAU,IAAIrB,KAAK,KAAKoB,YAAY,EAAE;IACxC,IAAI,CAACR,GAAG,GAAGb,SAAS,CAACO,KAAK,EAAE,CAAC,GAAG,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,KAAK,EAAE,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,QAAQ,EAAE,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC,MAAM,EAAE,EAAE;MACjMN,KAAK,GAAGA,KAAK,CAACiB,KAAK,CAAC,CAAC,EAAE,CAACL,GAAG,CAACV,MAAM,CAAC,EAAC;MACpCG,KAAK,GAAGL,KAAK,CAACO,SAAS,CAACJ,IAAI,CAACM,EAAE,CAAC;MAChCH,KAAK,GAAGN,KAAK,CAACO,SAAS,CAACJ,IAAI,CAACO,EAAE,CAAC;IAClC,CAAC,MAAM,IAAI,CAACE,GAAG,GAAGb,SAAS,CAACO,KAAK,EAAE,CAAC,MAAM,CAAC,CAAC,MAAM,EAAE,IAAIP,SAAS,CAACM,KAAK,EAAE,CAAC,MAAM,CAAC,CAAC,EAAE;MAClFL,KAAK,GAAGA,KAAK,CAACiB,KAAK,CAAC,CAAC,EAAE,CAACL,GAAG,CAACV,MAAM,CAAC,EAAC;MACpCG,KAAK,GAAGL,KAAK,CAACO,SAAS,CAACJ,IAAI,CAACM,EAAE,CAAC;MAChCH,KAAK,GAAGN,KAAK,CAACO,SAAS,CAACJ,IAAI,CAACO,EAAE,CAAC;IAClC,CAAC,MAAM,IAAI,CAACE,GAAG,GAAGb,SAAS,CAACO,KAAK,EAAE,CAAC,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,EAAE,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,OAAO,EAAE,QAAQ,EAAE,SAAS,CAAC,CAAC,MAAM,EAAE,EAAE;MAC1LN,KAAK,GAAGA,KAAK,CAACiB,KAAK,CAAC,CAAC,EAAE,CAACL,GAAG,CAACV,MAAM,CAAC,EAAC;;MAEpCW,YAAY,GAAGb,KAAK,CAACA,KAAK,CAACE,MAAM,GAAG,CAAC,CAAC;MACtC,IAAIW,YAAY,KAAK,GAAG,IAAIM,MAAM,CAACb,KAAK,EAAE,GAAG,GAAGM,GAAG,CAAC,EAAE;QAAEZ,KAAK,GAAGA,KAAK,CAACiB,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MAAC;MAEnFZ,KAAK,GAAGL,KAAK,CAACO,SAAS,CAACJ,IAAI,CAACM,EAAE,CAAC;MAChCH,KAAK,GAAGN,KAAK,CAACO,SAAS,CAACJ,IAAI,CAACO,EAAE,CAAC;IAClC;EACF;;EAEA;EACA,IAAI,EAAEV,KAAK,KAAKW,WAAW,CAAC,EAAE;IAC5B,IAAIX,KAAK,CAACA,KAAK,CAACE,MAAM,GAAG,CAAC,CAAC,KAAK,GAAG,EAAE;MAAEF,KAAK,GAAGA,KAAK,CAACiB,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,GAAG;IAAC;IACxE,IAAIjB,KAAK,CAACA,KAAK,CAACE,MAAM,GAAG,CAAC,CAAC,KAAK,GAAG,EAAE;MAAEF,KAAK,GAAGA,KAAK,CAACiB,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,GAAG;IAAC;EAC1E,CAAC,MAAM;IACL;IACAJ,YAAY,GAAGb,KAAK,CAACA,KAAK,CAACE,MAAM,GAAG,CAAC,CAAC;IACtCY,aAAa,GAAGd,KAAK,CAACA,KAAK,CAACE,MAAM,GAAG,CAAC,CAAC;IAEvC,IAAIW,YAAY,KAAK,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAACS,OAAO,CAACR,aAAa,CAAC,KAAK,CAAC,CAAC,EAAE;MACxFd,KAAK,GAAGA,KAAK,CAACiB,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MAC1Bb,KAAK,GAAGJ,KAAK,CAACO,SAAS,CAACJ,IAAI,CAACK,EAAE,CAAC;MAChCH,KAAK,GAAGL,KAAK,CAACO,SAAS,CAACJ,IAAI,CAACM,EAAE,CAAC;MAChCH,KAAK,GAAGN,KAAK,CAACO,SAAS,CAACJ,IAAI,CAACO,EAAE,CAAC;IAClC;IAEA,IAAI,CAACE,GAAG,GAAGb,SAAS,CAACM,KAAK,EAAE,CAAC,KAAK,CAAC,CAAC,MAAM,EAAE,EAAE;MAC5CQ,YAAY,GAAGb,KAAK,CAACA,KAAK,CAACE,MAAM,GAAGU,GAAG,CAACV,MAAM,GAAG,CAAC,CAAC;MACnD,IAAIW,YAAY,KAAK,GAAG,IAAIA,YAAY,KAAK,GAAG,EAAE;QAChDb,KAAK,GAAGA,KAAK,CAACiB,KAAK,CAAC,CAAC,EAAE,CAACL,GAAG,CAACV,MAAM,CAAC,EAAC;QACpCE,KAAK,GAAGJ,KAAK,CAACO,SAAS,CAACJ,IAAI,CAACK,EAAE,CAAC;QAChCH,KAAK,GAAGL,KAAK,CAACO,SAAS,CAACJ,IAAI,CAACM,EAAE,CAAC;QAChCH,KAAK,GAAGN,KAAK,CAACO,SAAS,CAACJ,IAAI,CAACO,EAAE,CAAC;MAClC;IACF;IAEA,IAAI,CAACE,GAAG,GAAGb,SAAS,CAACO,KAAK,EAAE,CAAC,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC,MAAM,EAAE,EAAE;MACnEN,KAAK,GAAGA,KAAK,CAACiB,KAAK,CAAC,CAAC,EAAE,CAACL,GAAG,CAACV,MAAM,CAAC,GAAG,GAAG,EAAC;MAC1CE,KAAK,GAAGJ,KAAK,CAACO,SAAS,CAACJ,IAAI,CAACK,EAAE,CAAC;MAChCH,KAAK,GAAGL,KAAK,CAACO,SAAS,CAACJ,IAAI,CAACM,EAAE,CAAC;MAChCH,KAAK,GAAGN,KAAK,CAACO,SAAS,CAACJ,IAAI,CAACO,EAAE,CAAC;IAClC;IACA,IAAI,CAACE,GAAG,GAAGb,SAAS,CAACO,KAAK,EAAE,GAAG,CAAC,MAAM,EAAE,EAAE;MACxCN,KAAK,GAAGA,KAAK,CAACiB,KAAK,CAAC,CAAC,EAAE,CAACL,GAAG,CAACV,MAAM,CAAC,EAAC;MACpCE,KAAK,GAAGJ,KAAK,CAACO,SAAS,CAACJ,IAAI,CAACK,EAAE,CAAC;MAChCH,KAAK,GAAGL,KAAK,CAACO,SAAS,CAACJ,IAAI,CAACM,EAAE,CAAC;MAChCH,KAAK,GAAGN,KAAK,CAACO,SAAS,CAACJ,IAAI,CAACO,EAAE,CAAC;IAClC;IACA,IAAI,CAACE,GAAG,GAAGb,SAAS,CAACO,KAAK,EAAE,GAAG,CAAC,MAAM,EAAE,EAAE;MACxC,IAAIN,KAAK,CAACiB,KAAK,CAACjB,KAAK,CAACE,MAAM,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,KAAK,IAAI,EAAE;QAAEF,KAAK,GAAGA,KAAK,CAACiB,KAAK,CAAC,CAAC,EAAE,CAACL,GAAG,CAACV,MAAM,CAAC;MAAC,CAAC,CAAC;IAC1F;EACF;;EAEA;EACA,IAAI,CAACU,GAAG,GAAGb,SAAS,CAACC,KAAK,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC,MAAM,EAAE,EAAE;IACzEA,KAAK,GAAGA,KAAK,CAACiB,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAC;EAC7B;;EAEA;EACAF,CAAC,GAAGf,KAAK,CAACE,MAAM,GAAG,CAAC;EACpB,OAAOa,CAAC,GAAG,CAAC,EAAE;IACZ,IAAI,CAACG,OAAO,CAAClB,KAAK,CAACe,CAAC,CAAC,CAAC,EAAE;MACtBA,CAAC,EAAE;IACL,CAAC,MAAM,IAAIA,CAAC,KAAKf,KAAK,CAACE,MAAM,GAAG,CAAC,KAAKF,KAAK,CAACe,CAAC,CAAC,KAAK,GAAG,IAAIf,KAAK,CAACe,CAAC,CAAC,KAAK,GAAG,CAAC,EAAE;MAC3Ef,KAAK,GAAGA,KAAK,CAACO,SAAS,CAAC,CAAC,EAAEQ,CAAC,CAAC,GAAG,GAAG,GAAGf,KAAK,CAACO,SAAS,CAACQ,CAAC,GAAG,CAAC,EAAEf,KAAK,CAACE,MAAM,CAAC;MAC1E;IACF,CAAC,MAAM;MACL;IACF;EACF;EAEA,OAAOF,KAAK,CAACC,WAAW,CAAC,CAAC;AAC5B;AAAC;;AAED;AACA;AACA;AACA;AACA;AACA,SAASH,OAAOA,CAAEE,KAAK,EAAE;EACvB,IAAIQ,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEa,GAAG;EACnB;;EAEAf,EAAE,GAAGC,EAAE,GAAGC,EAAE,GAAGa,GAAG,GAAGvB,KAAK,CAACE,MAAM;;EAEjC;EACA,KAAK,IAAIa,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGQ,GAAG,GAAG,CAAC,IAAIf,EAAE,KAAKe,GAAG,EAAER,CAAC,EAAE,EAAE;IAC9C,IAAIG,OAAO,CAAClB,KAAK,CAACe,CAAC,CAAC,CAAC,IAAI,CAACG,OAAO,CAAClB,KAAK,CAACe,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE;MAC/CP,EAAE,GAAGO,CAAC,GAAG,CAAC;IACZ;EACF;EACA;;EAEA;EACA,KAAK,IAAIA,CAAC,GAAGP,EAAE,EAAEO,CAAC,GAAGQ,GAAG,GAAG,CAAC,IAAId,EAAE,KAAKc,GAAG,EAAER,CAAC,EAAE,EAAE;IAC/C,IAAIG,OAAO,CAAClB,KAAK,CAACe,CAAC,CAAC,CAAC,IAAI,CAACG,OAAO,CAAClB,KAAK,CAACe,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE;MAC/CN,EAAE,GAAGM,CAAC,GAAG,CAAC;IACZ;EACF;EACA;;EAEA;EACA,MAAMS,KAAK,GAAGxB,KAAK,CAACiB,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;EAC/B,IAAIC,OAAO,CAAClB,KAAK,CAAC,CAAC,CAAC,CAAC,IAAIkB,OAAO,CAAClB,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE;IAC1CU,EAAE,GAAG,CAAC;EACR;EACA,IAAIc,KAAK,KAAK,KAAK,IAAIA,KAAK,KAAK,KAAK,IAAIA,KAAK,KAAK,KAAK,EAAE;IACzDd,EAAE,GAAG,CAAC;EACR,CAAC,MAAM;IACP;IACE,KAAK,IAAIK,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGQ,GAAG,GAAG,CAAC,IAAIb,EAAE,KAAKa,GAAG,EAAER,CAAC,EAAE,EAAE;MAC9C,IAAIG,OAAO,CAAClB,KAAK,CAACe,CAAC,CAAC,CAAC,EAAE;QACrBL,EAAE,GAAGK,CAAC,GAAG,CAAC;MACZ;IACF;EACF;EAEA,OAAO;IACLP,EAAE,EAAEA,EAAE;IACNC,EAAE,EAAEA,EAAE;IACNC,EAAE,EAAEA;EACN,CAAC;AACH;AAAC;;AAED;AACA;AACA;AACA;AACA;AACA,SAASb,OAAOA,CAAEG,KAAK,EAAE;EACvBA,KAAK,GAAGA,KAAK,CAACC,WAAW,CAAC,CAAC;EAE3B,IAAIwB,MAAM,GAAG,EAAE;EACf,IAAIV,CAAC,GAAG,CAAC;;EAET;EACA,IAAIf,KAAK,CAACe,CAAC,CAAC,KAAK,GAAG,IAAIG,OAAO,CAAClB,KAAK,CAACe,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE;IAC7CU,MAAM,IAAIzB,KAAK,CAACe,CAAC,CAAC,CAACW,WAAW,CAAC,CAAC;EAClC,CAAC,MAAM;IACLD,MAAM,IAAIzB,KAAK,CAACe,CAAC,CAAC;EACpB;EAEA,KAAKA,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGf,KAAK,CAACE,MAAM,EAAEa,CAAC,EAAE,EAAE;IACjC,IAAI,CAACf,KAAK,CAACe,CAAC,CAAC,KAAK,GAAG,IAAIf,KAAK,CAACe,CAAC,CAAC,KAAK,GAAG,KAAKG,OAAO,CAAClB,KAAK,CAACe,CAAC,GAAG,CAAC,CAAC,CAAC,IAAIG,OAAO,CAAClB,KAAK,CAACe,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE;MAC5FU,MAAM,IAAIzB,KAAK,CAACe,CAAC,CAAC,CAACW,WAAW,CAAC,CAAC;IAClC,CAAC,MAAM,IAAI1B,KAAK,CAACe,CAAC,CAAC,KAAK,GAAG,KAAKG,OAAO,CAAClB,KAAK,CAACe,CAAC,GAAG,CAAC,CAAC,CAAC,IAAIG,OAAO,CAAClB,KAAK,CAACe,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE;MAC/EU,MAAM,IAAIzB,KAAK,CAACe,CAAC,CAAC,CAACW,WAAW,CAAC,CAAC;IAClC,CAAC,MAAM,IAAI1B,KAAK,CAACe,CAAC,CAAC,KAAK,GAAG,IAAIf,KAAK,CAACe,CAAC,GAAG,CAAC,CAAC,KAAK,GAAG,EAAE;MACnDU,MAAM,IAAIzB,KAAK,CAACe,CAAC,CAAC,CAACW,WAAW,CAAC,CAAC;IAClC,CAAC,MAAM;MACLD,MAAM,IAAIzB,KAAK,CAACe,CAAC,CAAC;IACpB;EACF;EAEA,OAAOU,MAAM;AACf;AAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA,SAAS1B,SAASA,CAAEC,KAAK,EAAE2B,QAAQ,EAAE;EACnC,IAAIZ,CAAC;EAAE,IAAIa,OAAO,GAAG,EAAE;EACvB,KAAKb,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGY,QAAQ,CAACzB,MAAM,EAAEa,CAAC,EAAE,EAAE;IACpC,IAAII,MAAM,CAACnB,KAAK,EAAE2B,QAAQ,CAACZ,CAAC,CAAC,CAAC,IAAIY,QAAQ,CAACZ,CAAC,CAAC,CAACb,MAAM,GAAG0B,OAAO,CAAC1B,MAAM,EAAE;MAAE0B,OAAO,GAAGD,QAAQ,CAACZ,CAAC,CAAC;IAAC;EACjG;EAEA,OAAOa,OAAO;AAChB;AAAC;AAED,SAASV,OAAOA,CAAEW,MAAM,EAAE;EACxB,OAAQA,MAAM,KAAK,GAAG,IAAIA,MAAM,KAAK,GAAG,IAAIA,MAAM,KAAK,GAAG,IAAIA,MAAM,KAAK,GAAG,IAAIA,MAAM,KAAK,GAAG,IAAIA,MAAM,KAAK,GAAG,IAAIA,MAAM,KAAK,GAAG,IAAIA,MAAM,KAAK,GAAG,IAAIA,MAAM,KAAK,GAAG,IACpKA,MAAM,KAAK,GAAG,IAAIA,MAAM,KAAK,GAAG,IAAIA,MAAM,KAAK,GAAG,IAAIA,MAAM,KAAK,GAAG,IAAIA,MAAM,KAAK,GAAG,IAAIA,MAAM,KAAK,GAAG,IAAIA,MAAM,KAAK,GAAG,IAAIA,MAAM,KAAK,GAAG;AAChJ;AAAC;AAED,SAASV,MAAMA,CAAEnB,KAAK,EAAE8B,MAAM,EAAE;EAC9B,IAAI9B,KAAK,CAACE,MAAM,GAAG4B,MAAM,CAAC5B,MAAM,EAAE,OAAO,KAAK;EAC9C,OAAQF,KAAK,CAACiB,KAAK,CAAC,CAACa,MAAM,CAAC5B,MAAM,CAAC,KAAK4B,MAAM;AAChD;AAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}