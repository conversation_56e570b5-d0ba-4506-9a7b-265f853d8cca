{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.transformReply = exports.transformArguments = exports.IS_READ_ONLY = exports.FIRST_KEY_INDEX = void 0;\nexports.FIRST_KEY_INDEX = 1;\nexports.IS_READ_ONLY = true;\nfunction transformArguments(key) {\n  return ['BF.INFO', key];\n}\nexports.transformArguments = transformArguments;\nfunction transformReply(reply) {\n  return {\n    capacity: reply[1],\n    size: reply[3],\n    numberOfFilters: reply[5],\n    numberOfInsertedItems: reply[7],\n    expansionRate: reply[9]\n  };\n}\nexports.transformReply = transformReply;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "transformReply", "transformArguments", "IS_READ_ONLY", "FIRST_KEY_INDEX", "key", "reply", "capacity", "size", "numberOfFilters", "numberOfInsertedItems", "expansionRate"], "sources": ["C:/tmsft/node_modules/@redis/bloom/dist/commands/bloom/INFO.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.transformReply = exports.transformArguments = exports.IS_READ_ONLY = exports.FIRST_KEY_INDEX = void 0;\nexports.FIRST_KEY_INDEX = 1;\nexports.IS_READ_ONLY = true;\nfunction transformArguments(key) {\n    return ['BF.INFO', key];\n}\nexports.transformArguments = transformArguments;\nfunction transformReply(reply) {\n    return {\n        capacity: reply[1],\n        size: reply[3],\n        numberOfFilters: reply[5],\n        numberOfInsertedItems: reply[7],\n        expansionRate: reply[9]\n    };\n}\nexports.transformReply = transformReply;\n"], "mappings": "AAAA,YAAY;;AACZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEC,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7DD,OAAO,CAACE,cAAc,GAAGF,OAAO,CAACG,kBAAkB,GAAGH,OAAO,CAACI,YAAY,GAAGJ,OAAO,CAACK,eAAe,GAAG,KAAK,CAAC;AAC7GL,OAAO,CAACK,eAAe,GAAG,CAAC;AAC3BL,OAAO,CAACI,YAAY,GAAG,IAAI;AAC3B,SAASD,kBAAkBA,CAACG,GAAG,EAAE;EAC7B,OAAO,CAAC,SAAS,EAAEA,GAAG,CAAC;AAC3B;AACAN,OAAO,CAACG,kBAAkB,GAAGA,kBAAkB;AAC/C,SAASD,cAAcA,CAACK,KAAK,EAAE;EAC3B,OAAO;IACHC,QAAQ,EAAED,KAAK,CAAC,CAAC,CAAC;IAClBE,IAAI,EAAEF,KAAK,CAAC,CAAC,CAAC;IACdG,eAAe,EAAEH,KAAK,CAAC,CAAC,CAAC;IACzBI,qBAAqB,EAAEJ,KAAK,CAAC,CAAC,CAAC;IAC/BK,aAAa,EAAEL,KAAK,CAAC,CAAC;EAC1B,CAAC;AACL;AACAP,OAAO,CAACE,cAAc,GAAGA,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}