{"ast": null, "code": "/**\n * @license\n * Copyright 2018 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n// tslint:disable-next-line: no-imports-from-dist\nimport * as tfOps from '@tensorflow/tfjs-core/dist/ops/ops_for_converter';\nimport { getParamValue } from './utils';\nexport const executeOp = function (node, tensorMap, context) {\n  let ops = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : tfOps;\n  switch (node.op) {\n    case 'BatchMatMul':\n    case 'BatchMatMulV2':\n    case 'MatMul':\n      return [ops.matMul(getParamValue('a', node, tensorMap, context), getParamValue('b', node, tensorMap, context), getParamValue('transposeA', node, tensorMap, context), getParamValue('transposeB', node, tensorMap, context))];\n    case 'Einsum':\n      return [ops.einsum(getParamValue('equation', node, tensorMap, context), ...getParamValue('tensors', node, tensorMap, context))];\n    case 'Transpose':\n      return [ops.transpose(getParamValue('x', node, tensorMap, context), getParamValue('perm', node, tensorMap, context))];\n    case '_FusedMatMul':\n      const [extraOp, activationFunc] = getParamValue('fusedOps', node, tensorMap, context);\n      const isBiasAdd = extraOp === 'biasadd';\n      const isPrelu = activationFunc === 'prelu';\n      const numArgs = getParamValue('numArgs', node, tensorMap, context);\n      const leakyreluAlpha = getParamValue('leakyreluAlpha', node, tensorMap, context);\n      if (isBiasAdd) {\n        if (isPrelu && numArgs !== 2) {\n          throw new Error('Fused MatMul with BiasAdd and Prelu must have two ' + 'extra arguments: bias and alpha.');\n        }\n        if (!isPrelu && numArgs !== 1) {\n          throw new Error('Fused MatMul with BiasAdd must have one extra argument: bias.');\n        }\n      }\n      const [biasArg, preluArg] = getParamValue('args', node, tensorMap, context);\n      return [ops.fused.matMul({\n        a: getParamValue('a', node, tensorMap, context),\n        b: getParamValue('b', node, tensorMap, context),\n        transposeA: getParamValue('transposeA', node, tensorMap, context),\n        transposeB: getParamValue('transposeB', node, tensorMap, context),\n        bias: biasArg,\n        activation: activationFunc,\n        preluActivationWeights: preluArg,\n        leakyreluAlpha\n      })];\n    case 'MatrixBandPart':\n      return [ops.linalg.bandPart(getParamValue('a', node, tensorMap, context), getParamValue('numLower', node, tensorMap, context), getParamValue('numUpper', node, tensorMap, context))];\n    default:\n      throw TypeError(\"Node type \".concat(node.op, \" is not implemented\"));\n  }\n};\nexport const CATEGORY = 'matrices';", "map": {"version": 3, "names": ["tfOps", "getParamValue", "executeOp", "node", "tensorMap", "context", "ops", "arguments", "length", "undefined", "op", "<PERSON><PERSON><PERSON>", "einsum", "transpose", "extraOp", "activationFunc", "isBiasAdd", "isPrelu", "numArgs", "leakyreluAlpha", "Error", "biasArg", "preluArg", "fused", "a", "b", "transposeA", "transposeB", "bias", "activation", "preluActivationWeights", "l<PERSON>g", "bandPart", "TypeError", "concat", "CATEGORY"], "sources": ["C:\\tfjs-converter\\src\\operations\\executors\\matrices_executor.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2018 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {Scalar, Tensor, Tensor2D} from '@tensorflow/tfjs-core';\n// tslint:disable-next-line: no-imports-from-dist\nimport * as tfOps from '@tensorflow/tfjs-core/dist/ops/ops_for_converter';\n\nimport {NamedTensorsMap} from '../../data/types';\nimport {ExecutionContext} from '../../executor/execution_context';\nimport {InternalOpExecutor, Node} from '../types';\n\nimport {getParamValue} from './utils';\n\nexport const executeOp: InternalOpExecutor =\n    (node: Node, tensorMap: NamedTensorsMap, context: ExecutionContext,\n     ops = tfOps): Tensor[] => {\n      switch (node.op) {\n        case 'BatchMatMul':\n        case 'BatchMatMulV2':\n        case 'MatMul':\n          return [ops.matMul(\n              getParamValue('a', node, tensorMap, context) as Tensor2D,\n              getParamValue('b', node, tensorMap, context) as Tensor2D,\n              getParamValue('transposeA', node, tensorMap, context) as boolean,\n              getParamValue('transposeB', node, tensorMap, context) as\n                  boolean)];\n\n        case 'Einsum':\n          return [ops.einsum(\n              getParamValue('equation', node, tensorMap, context) as string,\n              ...getParamValue('tensors', node, tensorMap, context) as\n                  Tensor[])];\n\n        case 'Transpose':\n          return [ops.transpose(\n              getParamValue('x', node, tensorMap, context) as Tensor,\n              getParamValue('perm', node, tensorMap, context) as number[])];\n\n        case '_FusedMatMul':\n          const [extraOp, activationFunc] =\n              (getParamValue('fusedOps', node, tensorMap, context) as string[]);\n\n          const isBiasAdd = extraOp === 'biasadd';\n          const isPrelu = activationFunc === 'prelu';\n\n          const numArgs =\n              (getParamValue('numArgs', node, tensorMap, context) as number);\n          const leakyreluAlpha =\n              getParamValue('leakyreluAlpha', node, tensorMap, context) as\n              number;\n\n          if (isBiasAdd) {\n            if (isPrelu && numArgs !== 2) {\n              throw new Error(\n                  'Fused MatMul with BiasAdd and Prelu must have two ' +\n                  'extra arguments: bias and alpha.');\n            }\n            if (!isPrelu && numArgs !== 1) {\n              throw new Error(\n                  'Fused MatMul with BiasAdd must have one extra argument: bias.');\n            }\n          }\n          const [biasArg, preluArg] =\n              getParamValue('args', node, tensorMap, context) as Tensor[];\n          return [ops.fused.matMul({\n            a: getParamValue('a', node, tensorMap, context) as Tensor2D,\n            b: getParamValue('b', node, tensorMap, context) as Tensor2D,\n            transposeA: getParamValue('transposeA', node, tensorMap, context) as\n                boolean,\n            transposeB: getParamValue('transposeB', node, tensorMap, context) as\n                boolean,\n            bias: biasArg,\n            activation: activationFunc as tfOps.fused.Activation,\n            preluActivationWeights: preluArg,\n            leakyreluAlpha\n          })];\n\n        case 'MatrixBandPart':\n          return [ops.linalg.bandPart(\n              getParamValue('a', node, tensorMap, context) as Tensor2D,\n              getParamValue('numLower', node, tensorMap, context) as Scalar,\n              getParamValue('numUpper', node, tensorMap, context) as Scalar)];\n\n        default:\n          throw TypeError(`Node type ${node.op} is not implemented`);\n      }\n    };\n\nexport const CATEGORY = 'matrices';\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAkBA;AACA,OAAO,KAAKA,KAAK,MAAM,kDAAkD;AAMzE,SAAQC,aAAa,QAAO,SAAS;AAErC,OAAO,MAAMC,SAAS,GAClB,SAAAA,CAACC,IAAU,EAAEC,SAA0B,EAAEC,OAAyB,EACxC;EAAA,IAAzBC,GAAG,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAGP,KAAK;EACV,QAAQG,IAAI,CAACO,EAAE;IACb,KAAK,aAAa;IAClB,KAAK,eAAe;IACpB,KAAK,QAAQ;MACX,OAAO,CAACJ,GAAG,CAACK,MAAM,CACdV,aAAa,CAAC,GAAG,EAAEE,IAAI,EAAEC,SAAS,EAAEC,OAAO,CAAa,EACxDJ,aAAa,CAAC,GAAG,EAAEE,IAAI,EAAEC,SAAS,EAAEC,OAAO,CAAa,EACxDJ,aAAa,CAAC,YAAY,EAAEE,IAAI,EAAEC,SAAS,EAAEC,OAAO,CAAY,EAChEJ,aAAa,CAAC,YAAY,EAAEE,IAAI,EAAEC,SAAS,EAAEC,OAAO,CACzC,CAAC,CAAC;IAEnB,KAAK,QAAQ;MACX,OAAO,CAACC,GAAG,CAACM,MAAM,CACdX,aAAa,CAAC,UAAU,EAAEE,IAAI,EAAEC,SAAS,EAAEC,OAAO,CAAW,EAC7D,GAAGJ,aAAa,CAAC,SAAS,EAAEE,IAAI,EAAEC,SAAS,EAAEC,OAAO,CACxC,CAAC,CAAC;IAEpB,KAAK,WAAW;MACd,OAAO,CAACC,GAAG,CAACO,SAAS,CACjBZ,aAAa,CAAC,GAAG,EAAEE,IAAI,EAAEC,SAAS,EAAEC,OAAO,CAAW,EACtDJ,aAAa,CAAC,MAAM,EAAEE,IAAI,EAAEC,SAAS,EAAEC,OAAO,CAAa,CAAC,CAAC;IAEnE,KAAK,cAAc;MACjB,MAAM,CAACS,OAAO,EAAEC,cAAc,CAAC,GAC1Bd,aAAa,CAAC,UAAU,EAAEE,IAAI,EAAEC,SAAS,EAAEC,OAAO,CAAc;MAErE,MAAMW,SAAS,GAAGF,OAAO,KAAK,SAAS;MACvC,MAAMG,OAAO,GAAGF,cAAc,KAAK,OAAO;MAE1C,MAAMG,OAAO,GACRjB,aAAa,CAAC,SAAS,EAAEE,IAAI,EAAEC,SAAS,EAAEC,OAAO,CAAY;MAClE,MAAMc,cAAc,GAChBlB,aAAa,CAAC,gBAAgB,EAAEE,IAAI,EAAEC,SAAS,EAAEC,OAAO,CAClD;MAEV,IAAIW,SAAS,EAAE;QACb,IAAIC,OAAO,IAAIC,OAAO,KAAK,CAAC,EAAE;UAC5B,MAAM,IAAIE,KAAK,CACX,oDAAoD,GACpD,kCAAkC,CAAC;;QAEzC,IAAI,CAACH,OAAO,IAAIC,OAAO,KAAK,CAAC,EAAE;UAC7B,MAAM,IAAIE,KAAK,CACX,+DAA+D,CAAC;;;MAGxE,MAAM,CAACC,OAAO,EAAEC,QAAQ,CAAC,GACrBrB,aAAa,CAAC,MAAM,EAAEE,IAAI,EAAEC,SAAS,EAAEC,OAAO,CAAa;MAC/D,OAAO,CAACC,GAAG,CAACiB,KAAK,CAACZ,MAAM,CAAC;QACvBa,CAAC,EAAEvB,aAAa,CAAC,GAAG,EAAEE,IAAI,EAAEC,SAAS,EAAEC,OAAO,CAAa;QAC3DoB,CAAC,EAAExB,aAAa,CAAC,GAAG,EAAEE,IAAI,EAAEC,SAAS,EAAEC,OAAO,CAAa;QAC3DqB,UAAU,EAAEzB,aAAa,CAAC,YAAY,EAAEE,IAAI,EAAEC,SAAS,EAAEC,OAAO,CACrD;QACXsB,UAAU,EAAE1B,aAAa,CAAC,YAAY,EAAEE,IAAI,EAAEC,SAAS,EAAEC,OAAO,CACrD;QACXuB,IAAI,EAAEP,OAAO;QACbQ,UAAU,EAAEd,cAAwC;QACpDe,sBAAsB,EAAER,QAAQ;QAChCH;OACD,CAAC,CAAC;IAEL,KAAK,gBAAgB;MACnB,OAAO,CAACb,GAAG,CAACyB,MAAM,CAACC,QAAQ,CACvB/B,aAAa,CAAC,GAAG,EAAEE,IAAI,EAAEC,SAAS,EAAEC,OAAO,CAAa,EACxDJ,aAAa,CAAC,UAAU,EAAEE,IAAI,EAAEC,SAAS,EAAEC,OAAO,CAAW,EAC7DJ,aAAa,CAAC,UAAU,EAAEE,IAAI,EAAEC,SAAS,EAAEC,OAAO,CAAW,CAAC,CAAC;IAErE;MACE,MAAM4B,SAAS,cAAAC,MAAA,CAAc/B,IAAI,CAACO,EAAE,wBAAqB,CAAC;;AAEhE,CAAC;AAEL,OAAO,MAAMyB,QAAQ,GAAG,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}