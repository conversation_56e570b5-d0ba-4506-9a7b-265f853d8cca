{"ast": null, "code": "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the License);\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an AS IS BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { Tan } from '@tensorflow/tfjs-core';\nimport { unaryKernelFunc } from '../utils/unary_utils';\nexport const tan = unaryKernelFunc(Tan, xi => Math.tan(xi));\nexport const tanConfig = {\n  kernelName: Tan,\n  backendName: 'cpu',\n  kernelFunc: tan\n};", "map": {"version": 3, "names": ["<PERSON>", "unaryKernelFunc", "tan", "xi", "Math", "tanConfig", "kernelName", "backendName", "kernelFunc"], "sources": ["C:\\tfjs-backend-cpu\\src\\kernels\\Tan.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the License);\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an AS IS BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {KernelConfig, Tan} from '@tensorflow/tfjs-core';\n\nimport {unaryKernelFunc} from '../utils/unary_utils';\n\nexport const tan = unaryKernelFunc(Tan, (xi) => Math.tan(xi));\n\nexport const tanConfig: KernelConfig = {\n  kernelName: Tan,\n  backendName: 'cpu',\n  kernelFunc: tan,\n};\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAiBA,SAAsBA,GAAG,QAAO,uBAAuB;AAEvD,SAAQC,eAAe,QAAO,sBAAsB;AAEpD,OAAO,MAAMC,GAAG,GAAGD,eAAe,CAACD,GAAG,EAAGG,EAAE,IAAKC,IAAI,CAACF,GAAG,CAACC,EAAE,CAAC,CAAC;AAE7D,OAAO,MAAME,SAAS,GAAiB;EACrCC,UAAU,EAAEN,GAAG;EACfO,WAAW,EAAE,KAAK;EAClBC,UAAU,EAAEN;CACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}