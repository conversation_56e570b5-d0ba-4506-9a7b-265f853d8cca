{"ast": null, "code": "import { categorizationService } from './categorizationService';\nclass MLCategorizationService {\n  constructor() {\n    this.config = {\n      modelName: 'qwen3:32b',\n      ollamaEndpoint: 'http://localhost:11434',\n      confidenceThreshold: 0.7,\n      maxRetries: 3,\n      timeout: 30000,\n      batchSize: 10,\n      trainingDataPath: 'treasury_ml_training_data'\n    };\n    this.isOllamaAvailable = false;\n    this.modelLoaded = false;\n    this.checkOllamaAvailability();\n  }\n\n  // Check if Ollama is running and Qwen3:32b is available\n  async checkOllamaAvailability() {\n    try {\n      const controller = new AbortController();\n      const timeoutId = setTimeout(() => controller.abort(), 5000);\n      const response = await fetch(`${this.config.ollamaEndpoint}/api/tags`, {\n        signal: controller.signal\n      });\n      clearTimeout(timeoutId);\n      if (response.ok) {\n        const data = await response.json();\n        const models = data.models || [];\n        this.modelLoaded = models.some(model => {\n          var _model$name;\n          return model.name === this.config.modelName || ((_model$name = model.name) === null || _model$name === void 0 ? void 0 : _model$name.startsWith('qwen3:32b'));\n        });\n        this.isOllamaAvailable = true;\n        if (!this.modelLoaded) {\n          console.warn('Qwen3:32b model not found. Please run: ollama pull qwen3:32b');\n        }\n        return this.isOllamaAvailable && this.modelLoaded;\n      }\n    } catch (error) {\n      console.warn('Ollama not available:', error);\n      this.isOllamaAvailable = false;\n      this.modelLoaded = false;\n    }\n    return false;\n  }\n\n  // Generate categorization prompt for the ML model\n  generateCategorizationPrompt(transaction, categories) {\n    const amount = transaction.debitAmount ? `-${transaction.debitAmount}` : `+${transaction.creditAmount}`;\n    const categoryList = categories.map(cat => {\n      var _cat$keywords;\n      return `${cat.id}: ${cat.name} - ${cat.description} (keywords: ${((_cat$keywords = cat.keywords) === null || _cat$keywords === void 0 ? void 0 : _cat$keywords.join(', ')) || 'none'})`;\n    }).join('\\n');\n    return `You are a financial transaction categorization expert. Analyze the following transaction and categorize it.\n\nTransaction Details:\n- Description: \"${transaction.description}\"\n- Amount: ${amount}\n- Date: ${transaction.date}\n- Reference: ${transaction.reference || 'N/A'}\n\nAvailable Categories:\n${categoryList}\n\nInstructions:\n1. Analyze the transaction description, amount, and context\n2. Select the most appropriate category ID from the list above\n3. Provide a confidence score between 0.0 and 1.0\n4. Explain your reasoning in 1-2 sentences\n5. Suggest up to 2 alternative categories if applicable\n\nRespond in the following JSON format only:\n{\n  \"categoryId\": \"selected_category_id\",\n  \"confidence\": 0.85,\n  \"reasoning\": \"Brief explanation of why this category was chosen\",\n  \"alternativeCategories\": [\n    {\"categoryId\": \"alt_category_id\", \"confidence\": 0.65}\n  ]\n}`;\n  }\n\n  // Call Ollama API for categorization\n  async callOllamaAPI(prompt) {\n    if (!this.isOllamaAvailable || !this.modelLoaded) {\n      await this.checkOllamaAvailability();\n      if (!this.isOllamaAvailable || !this.modelLoaded) {\n        throw new Error('Ollama service is not available or Qwen3:32b model is not loaded');\n      }\n    }\n    const controller = new AbortController();\n    const timeoutId = setTimeout(() => controller.abort(), this.config.timeout);\n    try {\n      const response = await fetch(`${this.config.ollamaEndpoint}/api/generate`, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify({\n          model: this.config.modelName,\n          prompt: prompt,\n          stream: false,\n          options: {\n            temperature: 0.1,\n            // Low temperature for consistent results\n            top_k: 10,\n            top_p: 0.9,\n            num_predict: 500\n          }\n        }),\n        signal: controller.signal\n      });\n      clearTimeout(timeoutId);\n      if (!response.ok) {\n        throw new Error(`Ollama API error: ${response.status} ${response.statusText}`);\n      }\n      const data = await response.json();\n      const responseText = data.response;\n\n      // Parse JSON response from the model\n      try {\n        const jsonMatch = responseText.match(/\\{[\\s\\S]*\\}/);\n        if (jsonMatch) {\n          const result = JSON.parse(jsonMatch[0]);\n\n          // Validate the response structure\n          if (result.categoryId && typeof result.confidence === 'number') {\n            return {\n              categoryId: result.categoryId,\n              confidence: Math.max(0, Math.min(1, result.confidence)),\n              reasoning: result.reasoning || 'No reasoning provided',\n              alternativeCategories: result.alternativeCategories || []\n            };\n          }\n        }\n        throw new Error('Invalid response format from ML model');\n      } catch (parseError) {\n        console.error('Failed to parse ML response:', parseError);\n        console.error('Raw response:', responseText);\n        return null;\n      }\n    } catch (error) {\n      clearTimeout(timeoutId);\n      if (error instanceof Error && error.name === 'AbortError') {\n        throw new Error('ML categorization request timed out');\n      }\n      throw error;\n    }\n  }\n\n  // Categorize a single transaction using ML\n  async categorizeTransaction(transaction) {\n    try {\n      const categories = categorizationService.getAllCategories();\n      if (categories.length === 0) {\n        throw new Error('No categories available for ML categorization');\n      }\n      const prompt = this.generateCategorizationPrompt(transaction, categories);\n      const result = await this.callOllamaAPI(prompt);\n      if (result) {\n        // Validate that the returned category ID exists\n        const categoryExists = categories.some(cat => cat.id === result.categoryId);\n        if (!categoryExists) {\n          console.warn(`ML model returned unknown category ID: ${result.categoryId}`);\n          // Fallback to uncategorized\n          result.categoryId = 'cat_uncategorized';\n          result.confidence = 0.1;\n          result.reasoning = 'ML model returned unknown category, defaulted to uncategorized';\n        }\n\n        // Apply categorization if confidence is above threshold\n        if (result.confidence >= this.config.confidenceThreshold) {\n          categorizationService.categorizeTransaction(transaction.id, result.categoryId, 'ml', result.confidence, result.reasoning);\n        }\n      }\n      return result;\n    } catch (error) {\n      console.error('ML categorization failed:', error);\n      return null;\n    }\n  }\n\n  // Categorize multiple transactions in batches\n  async categorizeTransactionsBatch(transactions) {\n    const results = [];\n\n    // Process in batches to avoid overwhelming the system\n    for (let i = 0; i < transactions.length; i += this.config.batchSize) {\n      const batch = transactions.slice(i, i + this.config.batchSize);\n      const batchPromises = batch.map(async transaction => {\n        try {\n          const result = await this.categorizeTransaction(transaction);\n          return {\n            transaction,\n            result\n          };\n        } catch (error) {\n          return {\n            transaction,\n            result: null,\n            error: error instanceof Error ? error.message : 'Unknown error'\n          };\n        }\n      });\n      const batchResults = await Promise.all(batchPromises);\n      results.push(...batchResults);\n\n      // Small delay between batches to prevent overwhelming Ollama\n      if (i + this.config.batchSize < transactions.length) {\n        await new Promise(resolve => setTimeout(resolve, 1000));\n      }\n    }\n    return results;\n  }\n\n  // Train the model with historical data (prepare training data)\n  async prepareTrainingData() {\n    try {\n      // Get all categorized transactions for training\n      const categorizations = categorizationService.getAllCategorizations();\n      const categories = categorizationService.getAllCategories();\n\n      // This is a simplified training data preparation\n      // In a full implementation, you might export this data for fine-tuning\n      const trainingData = {\n        transactions: [],\n        // Would be populated with transaction data\n        categories: categories,\n        lastTrainingDate: new Date().toISOString(),\n        modelVersion: this.config.modelName,\n        accuracy: 0.0 // Would be calculated from validation data\n      };\n\n      // Store training data for potential future use\n      localStorage.setItem(this.config.trainingDataPath, JSON.stringify(trainingData));\n      return trainingData;\n    } catch (error) {\n      console.error('Failed to prepare training data:', error);\n      return null;\n    }\n  }\n\n  // Get model status and configuration\n  getModelStatus() {\n    return {\n      isAvailable: this.isOllamaAvailable,\n      modelLoaded: this.modelLoaded,\n      config: this.config,\n      lastCheck: new Date().toISOString()\n    };\n  }\n\n  // Update configuration\n  updateConfig(updates) {\n    this.config = {\n      ...this.config,\n      ...updates\n    };\n\n    // Re-check availability if endpoint changed\n    if (updates.ollamaEndpoint) {\n      this.checkOllamaAvailability();\n    }\n  }\n\n  // Test the ML categorization with a sample transaction\n  async testCategorization() {\n    const startTime = Date.now();\n    try {\n      const testTransaction = {\n        id: 'test_transaction',\n        description: 'Monthly office rent payment',\n        date: new Date().toISOString(),\n        debitAmount: 2500.00,\n        creditAmount: 0,\n        balance: 10000.00\n      };\n      const result = await this.categorizeTransaction(testTransaction);\n      const latency = Date.now() - startTime;\n      return {\n        success: true,\n        result: result || undefined,\n        latency\n      };\n    } catch (error) {\n      return {\n        success: false,\n        error: error instanceof Error ? error.message : 'Unknown error',\n        latency: Date.now() - startTime\n      };\n    }\n  }\n}\nexport const mlCategorizationService = new MLCategorizationService();", "map": {"version": 3, "names": ["categorizationService", "MLCategorizationService", "constructor", "config", "modelName", "ollamaEndpoint", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "maxRetries", "timeout", "batchSize", "trainingDataPath", "isOllamaAvailable", "modelLoaded", "checkOllamaAvailability", "controller", "AbortController", "timeoutId", "setTimeout", "abort", "response", "fetch", "signal", "clearTimeout", "ok", "data", "json", "models", "some", "model", "_model$name", "name", "startsWith", "console", "warn", "error", "generateCategorizationPrompt", "transaction", "categories", "amount", "debitAmount", "creditAmount", "categoryList", "map", "cat", "_cat$keywords", "id", "description", "keywords", "join", "date", "reference", "callOllamaAPI", "prompt", "Error", "method", "headers", "body", "JSON", "stringify", "stream", "options", "temperature", "top_k", "top_p", "num_predict", "status", "statusText", "responseText", "jsonMatch", "match", "result", "parse", "categoryId", "confidence", "Math", "max", "min", "reasoning", "alternativeCategories", "parseError", "categorizeTransaction", "getAllCategories", "length", "categoryExists", "categorizeTransactionsBatch", "transactions", "results", "i", "batch", "slice", "batchPromises", "message", "batchResults", "Promise", "all", "push", "resolve", "prepareTrainingData", "categorizations", "getAllCategorizations", "trainingData", "lastTrainingDate", "Date", "toISOString", "modelVersion", "accuracy", "localStorage", "setItem", "getModelStatus", "isAvailable", "<PERSON><PERSON><PERSON><PERSON>", "updateConfig", "updates", "testCategorization", "startTime", "now", "testTransaction", "balance", "latency", "success", "undefined", "mlCategorizationService"], "sources": ["C:/tmsft/src/services/mlCategorizationService.ts"], "sourcesContent": ["import { Transaction, TransactionCategory, MLCategorizationResult, MLCategorizationConfig, TrainingData } from '../types';\r\nimport { categorizationService } from './categorizationService';\r\n\r\nclass MLCategorizationService {\r\n  private config: MLCategorizationConfig = {\r\n    modelName: 'qwen3:32b',\r\n    ollamaEndpoint: 'http://localhost:11434',\r\n    confidenceThreshold: 0.7,\r\n    maxRetries: 3,\r\n    timeout: 30000,\r\n    batchSize: 10,\r\n    trainingDataPath: 'treasury_ml_training_data'\r\n  };\r\n\r\n  private isOllamaAvailable = false;\r\n  private modelLoaded = false;\r\n\r\n  constructor() {\r\n    this.checkOllamaAvailability();\r\n  }\r\n\r\n  // Check if Ollama is running and Qwen3:32b is available\r\n  async checkOllamaAvailability(): Promise<boolean> {\r\n    try {\r\n      const controller = new AbortController();\r\n      const timeoutId = setTimeout(() => controller.abort(), 5000);\r\n\r\n      const response = await fetch(`${this.config.ollamaEndpoint}/api/tags`, {\r\n        signal: controller.signal\r\n      });\r\n      \r\n      clearTimeout(timeoutId);\r\n\r\n      if (response.ok) {\r\n        const data = await response.json();\r\n        const models = data.models || [];\r\n        this.modelLoaded = models.some((model: Record<string, unknown>) => \r\n          model.name === this.config.modelName || \r\n          (model.name as string)?.startsWith('qwen3:32b')\r\n        );\r\n        this.isOllamaAvailable = true;\r\n        \r\n        if (!this.modelLoaded) {\r\n          console.warn('Qwen3:32b model not found. Please run: ollama pull qwen3:32b');\r\n        }\r\n        \r\n        return this.isOllamaAvailable && this.modelLoaded;\r\n      }\r\n    } catch (error) {\r\n      console.warn('Ollama not available:', error);\r\n      this.isOllamaAvailable = false;\r\n      this.modelLoaded = false;\r\n    }\r\n    \r\n    return false;\r\n  }\r\n\r\n  // Generate categorization prompt for the ML model\r\n  private generateCategorizationPrompt(\r\n    transaction: Transaction, \r\n    categories: TransactionCategory[]\r\n  ): string {\r\n    const amount = transaction.debitAmount ? `-${transaction.debitAmount}` : `+${transaction.creditAmount}`;\r\n    const categoryList = categories.map(cat => \r\n      `${cat.id}: ${cat.name} - ${cat.description} (keywords: ${cat.keywords?.join(', ') || 'none'})`\r\n    ).join('\\n');\r\n\r\n    return `You are a financial transaction categorization expert. Analyze the following transaction and categorize it.\r\n\r\nTransaction Details:\r\n- Description: \"${transaction.description}\"\r\n- Amount: ${amount}\r\n- Date: ${transaction.date}\r\n- Reference: ${transaction.reference || 'N/A'}\r\n\r\nAvailable Categories:\r\n${categoryList}\r\n\r\nInstructions:\r\n1. Analyze the transaction description, amount, and context\r\n2. Select the most appropriate category ID from the list above\r\n3. Provide a confidence score between 0.0 and 1.0\r\n4. Explain your reasoning in 1-2 sentences\r\n5. Suggest up to 2 alternative categories if applicable\r\n\r\nRespond in the following JSON format only:\r\n{\r\n  \"categoryId\": \"selected_category_id\",\r\n  \"confidence\": 0.85,\r\n  \"reasoning\": \"Brief explanation of why this category was chosen\",\r\n  \"alternativeCategories\": [\r\n    {\"categoryId\": \"alt_category_id\", \"confidence\": 0.65}\r\n  ]\r\n}`;\r\n  }\r\n\r\n  // Call Ollama API for categorization\r\n  private async callOllamaAPI(prompt: string): Promise<MLCategorizationResult | null> {\r\n    if (!this.isOllamaAvailable || !this.modelLoaded) {\r\n      await this.checkOllamaAvailability();\r\n      if (!this.isOllamaAvailable || !this.modelLoaded) {\r\n        throw new Error('Ollama service is not available or Qwen3:32b model is not loaded');\r\n      }\r\n    }\r\n\r\n    const controller = new AbortController();\r\n    const timeoutId = setTimeout(() => controller.abort(), this.config.timeout);\r\n\r\n    try {\r\n      const response = await fetch(`${this.config.ollamaEndpoint}/api/generate`, {\r\n        method: 'POST',\r\n        headers: {\r\n          'Content-Type': 'application/json',\r\n        },\r\n        body: JSON.stringify({\r\n          model: this.config.modelName,\r\n          prompt: prompt,\r\n          stream: false,\r\n          options: {\r\n            temperature: 0.1, // Low temperature for consistent results\r\n            top_k: 10,\r\n            top_p: 0.9,\r\n            num_predict: 500\r\n          }\r\n        }),\r\n        signal: controller.signal\r\n      });\r\n\r\n      clearTimeout(timeoutId);\r\n\r\n      if (!response.ok) {\r\n        throw new Error(`Ollama API error: ${response.status} ${response.statusText}`);\r\n      }\r\n\r\n      const data = await response.json();\r\n      const responseText = data.response;\r\n\r\n      // Parse JSON response from the model\r\n      try {\r\n        const jsonMatch = responseText.match(/\\{[\\s\\S]*\\}/);\r\n        if (jsonMatch) {\r\n          const result = JSON.parse(jsonMatch[0]);\r\n          \r\n          // Validate the response structure\r\n          if (result.categoryId && typeof result.confidence === 'number') {\r\n            return {\r\n              categoryId: result.categoryId,\r\n              confidence: Math.max(0, Math.min(1, result.confidence)),\r\n              reasoning: result.reasoning || 'No reasoning provided',\r\n              alternativeCategories: result.alternativeCategories || []\r\n            };\r\n          }\r\n        }\r\n        throw new Error('Invalid response format from ML model');\r\n      } catch (parseError) {\r\n        console.error('Failed to parse ML response:', parseError);\r\n        console.error('Raw response:', responseText);\r\n        return null;\r\n      }\r\n    } catch (error) {\r\n      clearTimeout(timeoutId);\r\n      if (error instanceof Error && error.name === 'AbortError') {\r\n        throw new Error('ML categorization request timed out');\r\n      }\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  // Categorize a single transaction using ML\r\n  async categorizeTransaction(transaction: Transaction): Promise<MLCategorizationResult | null> {\r\n    try {\r\n      const categories = categorizationService.getAllCategories();\r\n      if (categories.length === 0) {\r\n        throw new Error('No categories available for ML categorization');\r\n      }\r\n\r\n      const prompt = this.generateCategorizationPrompt(transaction, categories);\r\n      const result = await this.callOllamaAPI(prompt);\r\n\r\n      if (result) {\r\n        // Validate that the returned category ID exists\r\n        const categoryExists = categories.some(cat => cat.id === result.categoryId);\r\n        if (!categoryExists) {\r\n          console.warn(`ML model returned unknown category ID: ${result.categoryId}`);\r\n          // Fallback to uncategorized\r\n          result.categoryId = 'cat_uncategorized';\r\n          result.confidence = 0.1;\r\n          result.reasoning = 'ML model returned unknown category, defaulted to uncategorized';\r\n        }\r\n\r\n        // Apply categorization if confidence is above threshold\r\n        if (result.confidence >= this.config.confidenceThreshold) {\r\n          categorizationService.categorizeTransaction(\r\n            transaction.id,\r\n            result.categoryId,\r\n            'ml',\r\n            result.confidence,\r\n            result.reasoning\r\n          );\r\n        }\r\n      }\r\n\r\n      return result;\r\n    } catch (error) {\r\n      console.error('ML categorization failed:', error);\r\n      return null;\r\n    }\r\n  }\r\n\r\n  // Categorize multiple transactions in batches\r\n  async categorizeTransactionsBatch(transactions: Transaction[]): Promise<Array<{\r\n    transaction: Transaction;\r\n    result: MLCategorizationResult | null;\r\n    error?: string;\r\n  }>> {\r\n    const results: Array<{\r\n      transaction: Transaction;\r\n      result: MLCategorizationResult | null;\r\n      error?: string;\r\n    }> = [];\r\n\r\n    // Process in batches to avoid overwhelming the system\r\n    for (let i = 0; i < transactions.length; i += this.config.batchSize) {\r\n      const batch = transactions.slice(i, i + this.config.batchSize);\r\n      \r\n      const batchPromises = batch.map(async (transaction) => {\r\n        try {\r\n          const result = await this.categorizeTransaction(transaction);\r\n          return { transaction, result };\r\n        } catch (error) {\r\n          return { \r\n            transaction, \r\n            result: null, \r\n            error: error instanceof Error ? error.message : 'Unknown error' \r\n          };\r\n        }\r\n      });\r\n\r\n      const batchResults = await Promise.all(batchPromises);\r\n      results.push(...batchResults);\r\n\r\n      // Small delay between batches to prevent overwhelming Ollama\r\n      if (i + this.config.batchSize < transactions.length) {\r\n        await new Promise(resolve => setTimeout(resolve, 1000));\r\n      }\r\n    }\r\n\r\n    return results;\r\n  }\r\n\r\n  // Train the model with historical data (prepare training data)\r\n  async prepareTrainingData(): Promise<TrainingData | null> {\r\n    try {\r\n      // Get all categorized transactions for training\r\n      const categorizations = categorizationService.getAllCategorizations();\r\n      const categories = categorizationService.getAllCategories();\r\n      \r\n      // This is a simplified training data preparation\r\n      // In a full implementation, you might export this data for fine-tuning\r\n      const trainingData: TrainingData = {\r\n        transactions: [], // Would be populated with transaction data\r\n        categories: categories,\r\n        lastTrainingDate: new Date().toISOString(),\r\n        modelVersion: this.config.modelName,\r\n        accuracy: 0.0 // Would be calculated from validation data\r\n      };\r\n\r\n      // Store training data for potential future use\r\n      localStorage.setItem(this.config.trainingDataPath!, JSON.stringify(trainingData));\r\n      \r\n      return trainingData;\r\n    } catch (error) {\r\n      console.error('Failed to prepare training data:', error);\r\n      return null;\r\n    }\r\n  }\r\n\r\n  // Get model status and configuration\r\n  getModelStatus(): {\r\n    isAvailable: boolean;\r\n    modelLoaded: boolean;\r\n    config: MLCategorizationConfig;\r\n    lastCheck: string;\r\n  } {\r\n    return {\r\n      isAvailable: this.isOllamaAvailable,\r\n      modelLoaded: this.modelLoaded,\r\n      config: this.config,\r\n      lastCheck: new Date().toISOString()\r\n    };\r\n  }\r\n\r\n  // Update configuration\r\n  updateConfig(updates: Partial<MLCategorizationConfig>): void {\r\n    this.config = { ...this.config, ...updates };\r\n    \r\n    // Re-check availability if endpoint changed\r\n    if (updates.ollamaEndpoint) {\r\n      this.checkOllamaAvailability();\r\n    }\r\n  }\r\n\r\n  // Test the ML categorization with a sample transaction\r\n  async testCategorization(): Promise<{\r\n    success: boolean;\r\n    result?: MLCategorizationResult;\r\n    error?: string;\r\n    latency?: number;\r\n  }> {\r\n    const startTime = Date.now();\r\n    \r\n    try {\r\n      const testTransaction: Transaction = {\r\n        id: 'test_transaction',\r\n        description: 'Monthly office rent payment',\r\n        date: new Date().toISOString(),\r\n        debitAmount: 2500.00,\r\n        creditAmount: 0,\r\n        balance: 10000.00\r\n      };\r\n\r\n      const result = await this.categorizeTransaction(testTransaction);\r\n      const latency = Date.now() - startTime;\r\n\r\n      return {\r\n        success: true,\r\n        result: result || undefined,\r\n        latency\r\n      };\r\n    } catch (error) {\r\n      return {\r\n        success: false,\r\n        error: error instanceof Error ? error.message : 'Unknown error',\r\n        latency: Date.now() - startTime\r\n      };\r\n    }\r\n  }\r\n}\r\n\r\nexport const mlCategorizationService = new MLCategorizationService(); "], "mappings": "AACA,SAASA,qBAAqB,QAAQ,yBAAyB;AAE/D,MAAMC,uBAAuB,CAAC;EAc5BC,WAAWA,CAAA,EAAG;IAAA,KAbNC,MAAM,GAA2B;MACvCC,SAAS,EAAE,WAAW;MACtBC,cAAc,EAAE,wBAAwB;MACxCC,mBAAmB,EAAE,GAAG;MACxBC,UAAU,EAAE,CAAC;MACbC,OAAO,EAAE,KAAK;MACdC,SAAS,EAAE,EAAE;MACbC,gBAAgB,EAAE;IACpB,CAAC;IAAA,KAEOC,iBAAiB,GAAG,KAAK;IAAA,KACzBC,WAAW,GAAG,KAAK;IAGzB,IAAI,CAACC,uBAAuB,CAAC,CAAC;EAChC;;EAEA;EACA,MAAMA,uBAAuBA,CAAA,EAAqB;IAChD,IAAI;MACF,MAAMC,UAAU,GAAG,IAAIC,eAAe,CAAC,CAAC;MACxC,MAAMC,SAAS,GAAGC,UAAU,CAAC,MAAMH,UAAU,CAACI,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC;MAE5D,MAAMC,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAG,IAAI,CAACjB,MAAM,CAACE,cAAc,WAAW,EAAE;QACrEgB,MAAM,EAAEP,UAAU,CAACO;MACrB,CAAC,CAAC;MAEFC,YAAY,CAACN,SAAS,CAAC;MAEvB,IAAIG,QAAQ,CAACI,EAAE,EAAE;QACf,MAAMC,IAAI,GAAG,MAAML,QAAQ,CAACM,IAAI,CAAC,CAAC;QAClC,MAAMC,MAAM,GAAGF,IAAI,CAACE,MAAM,IAAI,EAAE;QAChC,IAAI,CAACd,WAAW,GAAGc,MAAM,CAACC,IAAI,CAAEC,KAA8B;UAAA,IAAAC,WAAA;UAAA,OAC5DD,KAAK,CAACE,IAAI,KAAK,IAAI,CAAC3B,MAAM,CAACC,SAAS,MAAAyB,WAAA,GACnCD,KAAK,CAACE,IAAI,cAAAD,WAAA,uBAAXA,WAAA,CAAwBE,UAAU,CAAC,WAAW,CAAC;QAAA,CACjD,CAAC;QACD,IAAI,CAACpB,iBAAiB,GAAG,IAAI;QAE7B,IAAI,CAAC,IAAI,CAACC,WAAW,EAAE;UACrBoB,OAAO,CAACC,IAAI,CAAC,8DAA8D,CAAC;QAC9E;QAEA,OAAO,IAAI,CAACtB,iBAAiB,IAAI,IAAI,CAACC,WAAW;MACnD;IACF,CAAC,CAAC,OAAOsB,KAAK,EAAE;MACdF,OAAO,CAACC,IAAI,CAAC,uBAAuB,EAAEC,KAAK,CAAC;MAC5C,IAAI,CAACvB,iBAAiB,GAAG,KAAK;MAC9B,IAAI,CAACC,WAAW,GAAG,KAAK;IAC1B;IAEA,OAAO,KAAK;EACd;;EAEA;EACQuB,4BAA4BA,CAClCC,WAAwB,EACxBC,UAAiC,EACzB;IACR,MAAMC,MAAM,GAAGF,WAAW,CAACG,WAAW,GAAG,IAAIH,WAAW,CAACG,WAAW,EAAE,GAAG,IAAIH,WAAW,CAACI,YAAY,EAAE;IACvG,MAAMC,YAAY,GAAGJ,UAAU,CAACK,GAAG,CAACC,GAAG;MAAA,IAAAC,aAAA;MAAA,OACrC,GAAGD,GAAG,CAACE,EAAE,KAAKF,GAAG,CAACb,IAAI,MAAMa,GAAG,CAACG,WAAW,eAAe,EAAAF,aAAA,GAAAD,GAAG,CAACI,QAAQ,cAAAH,aAAA,uBAAZA,aAAA,CAAcI,IAAI,CAAC,IAAI,CAAC,KAAI,MAAM,GAAG;IAAA,CACjG,CAAC,CAACA,IAAI,CAAC,IAAI,CAAC;IAEZ,OAAO;AACX;AACA;AACA,kBAAkBZ,WAAW,CAACU,WAAW;AACzC,YAAYR,MAAM;AAClB,UAAUF,WAAW,CAACa,IAAI;AAC1B,eAAeb,WAAW,CAACc,SAAS,IAAI,KAAK;AAC7C;AACA;AACA,EAAET,YAAY;AACd;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE;EACA;;EAEA;EACA,MAAcU,aAAaA,CAACC,MAAc,EAA0C;IAClF,IAAI,CAAC,IAAI,CAACzC,iBAAiB,IAAI,CAAC,IAAI,CAACC,WAAW,EAAE;MAChD,MAAM,IAAI,CAACC,uBAAuB,CAAC,CAAC;MACpC,IAAI,CAAC,IAAI,CAACF,iBAAiB,IAAI,CAAC,IAAI,CAACC,WAAW,EAAE;QAChD,MAAM,IAAIyC,KAAK,CAAC,kEAAkE,CAAC;MACrF;IACF;IAEA,MAAMvC,UAAU,GAAG,IAAIC,eAAe,CAAC,CAAC;IACxC,MAAMC,SAAS,GAAGC,UAAU,CAAC,MAAMH,UAAU,CAACI,KAAK,CAAC,CAAC,EAAE,IAAI,CAACf,MAAM,CAACK,OAAO,CAAC;IAE3E,IAAI;MACF,MAAMW,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAG,IAAI,CAACjB,MAAM,CAACE,cAAc,eAAe,EAAE;QACzEiD,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UACP,cAAc,EAAE;QAClB,CAAC;QACDC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;UACnB9B,KAAK,EAAE,IAAI,CAACzB,MAAM,CAACC,SAAS;UAC5BgD,MAAM,EAAEA,MAAM;UACdO,MAAM,EAAE,KAAK;UACbC,OAAO,EAAE;YACPC,WAAW,EAAE,GAAG;YAAE;YAClBC,KAAK,EAAE,EAAE;YACTC,KAAK,EAAE,GAAG;YACVC,WAAW,EAAE;UACf;QACF,CAAC,CAAC;QACF3C,MAAM,EAAEP,UAAU,CAACO;MACrB,CAAC,CAAC;MAEFC,YAAY,CAACN,SAAS,CAAC;MAEvB,IAAI,CAACG,QAAQ,CAACI,EAAE,EAAE;QAChB,MAAM,IAAI8B,KAAK,CAAC,qBAAqBlC,QAAQ,CAAC8C,MAAM,IAAI9C,QAAQ,CAAC+C,UAAU,EAAE,CAAC;MAChF;MAEA,MAAM1C,IAAI,GAAG,MAAML,QAAQ,CAACM,IAAI,CAAC,CAAC;MAClC,MAAM0C,YAAY,GAAG3C,IAAI,CAACL,QAAQ;;MAElC;MACA,IAAI;QACF,MAAMiD,SAAS,GAAGD,YAAY,CAACE,KAAK,CAAC,aAAa,CAAC;QACnD,IAAID,SAAS,EAAE;UACb,MAAME,MAAM,GAAGb,IAAI,CAACc,KAAK,CAACH,SAAS,CAAC,CAAC,CAAC,CAAC;;UAEvC;UACA,IAAIE,MAAM,CAACE,UAAU,IAAI,OAAOF,MAAM,CAACG,UAAU,KAAK,QAAQ,EAAE;YAC9D,OAAO;cACLD,UAAU,EAAEF,MAAM,CAACE,UAAU;cAC7BC,UAAU,EAAEC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAED,IAAI,CAACE,GAAG,CAAC,CAAC,EAAEN,MAAM,CAACG,UAAU,CAAC,CAAC;cACvDI,SAAS,EAAEP,MAAM,CAACO,SAAS,IAAI,uBAAuB;cACtDC,qBAAqB,EAAER,MAAM,CAACQ,qBAAqB,IAAI;YACzD,CAAC;UACH;QACF;QACA,MAAM,IAAIzB,KAAK,CAAC,uCAAuC,CAAC;MAC1D,CAAC,CAAC,OAAO0B,UAAU,EAAE;QACnB/C,OAAO,CAACE,KAAK,CAAC,8BAA8B,EAAE6C,UAAU,CAAC;QACzD/C,OAAO,CAACE,KAAK,CAAC,eAAe,EAAEiC,YAAY,CAAC;QAC5C,OAAO,IAAI;MACb;IACF,CAAC,CAAC,OAAOjC,KAAK,EAAE;MACdZ,YAAY,CAACN,SAAS,CAAC;MACvB,IAAIkB,KAAK,YAAYmB,KAAK,IAAInB,KAAK,CAACJ,IAAI,KAAK,YAAY,EAAE;QACzD,MAAM,IAAIuB,KAAK,CAAC,qCAAqC,CAAC;MACxD;MACA,MAAMnB,KAAK;IACb;EACF;;EAEA;EACA,MAAM8C,qBAAqBA,CAAC5C,WAAwB,EAA0C;IAC5F,IAAI;MACF,MAAMC,UAAU,GAAGrC,qBAAqB,CAACiF,gBAAgB,CAAC,CAAC;MAC3D,IAAI5C,UAAU,CAAC6C,MAAM,KAAK,CAAC,EAAE;QAC3B,MAAM,IAAI7B,KAAK,CAAC,+CAA+C,CAAC;MAClE;MAEA,MAAMD,MAAM,GAAG,IAAI,CAACjB,4BAA4B,CAACC,WAAW,EAAEC,UAAU,CAAC;MACzE,MAAMiC,MAAM,GAAG,MAAM,IAAI,CAACnB,aAAa,CAACC,MAAM,CAAC;MAE/C,IAAIkB,MAAM,EAAE;QACV;QACA,MAAMa,cAAc,GAAG9C,UAAU,CAACV,IAAI,CAACgB,GAAG,IAAIA,GAAG,CAACE,EAAE,KAAKyB,MAAM,CAACE,UAAU,CAAC;QAC3E,IAAI,CAACW,cAAc,EAAE;UACnBnD,OAAO,CAACC,IAAI,CAAC,0CAA0CqC,MAAM,CAACE,UAAU,EAAE,CAAC;UAC3E;UACAF,MAAM,CAACE,UAAU,GAAG,mBAAmB;UACvCF,MAAM,CAACG,UAAU,GAAG,GAAG;UACvBH,MAAM,CAACO,SAAS,GAAG,gEAAgE;QACrF;;QAEA;QACA,IAAIP,MAAM,CAACG,UAAU,IAAI,IAAI,CAACtE,MAAM,CAACG,mBAAmB,EAAE;UACxDN,qBAAqB,CAACgF,qBAAqB,CACzC5C,WAAW,CAACS,EAAE,EACdyB,MAAM,CAACE,UAAU,EACjB,IAAI,EACJF,MAAM,CAACG,UAAU,EACjBH,MAAM,CAACO,SACT,CAAC;QACH;MACF;MAEA,OAAOP,MAAM;IACf,CAAC,CAAC,OAAOpC,KAAK,EAAE;MACdF,OAAO,CAACE,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACjD,OAAO,IAAI;IACb;EACF;;EAEA;EACA,MAAMkD,2BAA2BA,CAACC,YAA2B,EAIzD;IACF,MAAMC,OAIJ,GAAG,EAAE;;IAEP;IACA,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,YAAY,CAACH,MAAM,EAAEK,CAAC,IAAI,IAAI,CAACpF,MAAM,CAACM,SAAS,EAAE;MACnE,MAAM+E,KAAK,GAAGH,YAAY,CAACI,KAAK,CAACF,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACpF,MAAM,CAACM,SAAS,CAAC;MAE9D,MAAMiF,aAAa,GAAGF,KAAK,CAAC9C,GAAG,CAAC,MAAON,WAAW,IAAK;QACrD,IAAI;UACF,MAAMkC,MAAM,GAAG,MAAM,IAAI,CAACU,qBAAqB,CAAC5C,WAAW,CAAC;UAC5D,OAAO;YAAEA,WAAW;YAAEkC;UAAO,CAAC;QAChC,CAAC,CAAC,OAAOpC,KAAK,EAAE;UACd,OAAO;YACLE,WAAW;YACXkC,MAAM,EAAE,IAAI;YACZpC,KAAK,EAAEA,KAAK,YAAYmB,KAAK,GAAGnB,KAAK,CAACyD,OAAO,GAAG;UAClD,CAAC;QACH;MACF,CAAC,CAAC;MAEF,MAAMC,YAAY,GAAG,MAAMC,OAAO,CAACC,GAAG,CAACJ,aAAa,CAAC;MACrDJ,OAAO,CAACS,IAAI,CAAC,GAAGH,YAAY,CAAC;;MAE7B;MACA,IAAIL,CAAC,GAAG,IAAI,CAACpF,MAAM,CAACM,SAAS,GAAG4E,YAAY,CAACH,MAAM,EAAE;QACnD,MAAM,IAAIW,OAAO,CAACG,OAAO,IAAI/E,UAAU,CAAC+E,OAAO,EAAE,IAAI,CAAC,CAAC;MACzD;IACF;IAEA,OAAOV,OAAO;EAChB;;EAEA;EACA,MAAMW,mBAAmBA,CAAA,EAAiC;IACxD,IAAI;MACF;MACA,MAAMC,eAAe,GAAGlG,qBAAqB,CAACmG,qBAAqB,CAAC,CAAC;MACrE,MAAM9D,UAAU,GAAGrC,qBAAqB,CAACiF,gBAAgB,CAAC,CAAC;;MAE3D;MACA;MACA,MAAMmB,YAA0B,GAAG;QACjCf,YAAY,EAAE,EAAE;QAAE;QAClBhD,UAAU,EAAEA,UAAU;QACtBgE,gBAAgB,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;QAC1CC,YAAY,EAAE,IAAI,CAACrG,MAAM,CAACC,SAAS;QACnCqG,QAAQ,EAAE,GAAG,CAAC;MAChB,CAAC;;MAED;MACAC,YAAY,CAACC,OAAO,CAAC,IAAI,CAACxG,MAAM,CAACO,gBAAgB,EAAG+C,IAAI,CAACC,SAAS,CAAC0C,YAAY,CAAC,CAAC;MAEjF,OAAOA,YAAY;IACrB,CAAC,CAAC,OAAOlE,KAAK,EAAE;MACdF,OAAO,CAACE,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;MACxD,OAAO,IAAI;IACb;EACF;;EAEA;EACA0E,cAAcA,CAAA,EAKZ;IACA,OAAO;MACLC,WAAW,EAAE,IAAI,CAAClG,iBAAiB;MACnCC,WAAW,EAAE,IAAI,CAACA,WAAW;MAC7BT,MAAM,EAAE,IAAI,CAACA,MAAM;MACnB2G,SAAS,EAAE,IAAIR,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;IACpC,CAAC;EACH;;EAEA;EACAQ,YAAYA,CAACC,OAAwC,EAAQ;IAC3D,IAAI,CAAC7G,MAAM,GAAG;MAAE,GAAG,IAAI,CAACA,MAAM;MAAE,GAAG6G;IAAQ,CAAC;;IAE5C;IACA,IAAIA,OAAO,CAAC3G,cAAc,EAAE;MAC1B,IAAI,CAACQ,uBAAuB,CAAC,CAAC;IAChC;EACF;;EAEA;EACA,MAAMoG,kBAAkBA,CAAA,EAKrB;IACD,MAAMC,SAAS,GAAGZ,IAAI,CAACa,GAAG,CAAC,CAAC;IAE5B,IAAI;MACF,MAAMC,eAA4B,GAAG;QACnCvE,EAAE,EAAE,kBAAkB;QACtBC,WAAW,EAAE,6BAA6B;QAC1CG,IAAI,EAAE,IAAIqD,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;QAC9BhE,WAAW,EAAE,OAAO;QACpBC,YAAY,EAAE,CAAC;QACf6E,OAAO,EAAE;MACX,CAAC;MAED,MAAM/C,MAAM,GAAG,MAAM,IAAI,CAACU,qBAAqB,CAACoC,eAAe,CAAC;MAChE,MAAME,OAAO,GAAGhB,IAAI,CAACa,GAAG,CAAC,CAAC,GAAGD,SAAS;MAEtC,OAAO;QACLK,OAAO,EAAE,IAAI;QACbjD,MAAM,EAAEA,MAAM,IAAIkD,SAAS;QAC3BF;MACF,CAAC;IACH,CAAC,CAAC,OAAOpF,KAAK,EAAE;MACd,OAAO;QACLqF,OAAO,EAAE,KAAK;QACdrF,KAAK,EAAEA,KAAK,YAAYmB,KAAK,GAAGnB,KAAK,CAACyD,OAAO,GAAG,eAAe;QAC/D2B,OAAO,EAAEhB,IAAI,CAACa,GAAG,CAAC,CAAC,GAAGD;MACxB,CAAC;IACH;EACF;AACF;AAEA,OAAO,MAAMO,uBAAuB,GAAG,IAAIxH,uBAAuB,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}