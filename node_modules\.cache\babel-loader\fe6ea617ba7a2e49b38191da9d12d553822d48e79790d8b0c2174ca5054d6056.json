{"ast": null, "code": "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { backend_util, MaxPool3D, util } from '@tensorflow/tfjs-core';\nimport { assertNotComplex } from '../cpu_util';\nimport { pool3d } from '../utils/pool_utils';\nexport function maxPool3D(args) {\n  const {\n    inputs,\n    backend,\n    attrs\n  } = args;\n  const {\n    x\n  } = inputs;\n  const {\n    filterSize,\n    strides,\n    pad,\n    dimRoundingMode,\n    dataFormat\n  } = attrs;\n  assertNotComplex(x, 'maxPool3d');\n  const convInfo = backend_util.computePool3DInfo(x.shape, filterSize, strides, 1 /* dilations */, pad, dimRoundingMode, dataFormat);\n  const xValues = backend.data.get(x.dataId).values;\n  const outBuf = pool3d(xValues, x.shape, x.dtype, util.computeStrides(x.shape), convInfo, 'max');\n  return backend.makeTensorInfo(outBuf.shape, 'float32', outBuf.values);\n}\nexport const maxPool3DConfig = {\n  kernelName: MaxPool3D,\n  backendName: 'cpu',\n  kernelFunc: maxPool3D\n};", "map": {"version": 3, "names": ["backend_util", "MaxPool3D", "util", "assertNotComplex", "pool3d", "maxPool3D", "args", "inputs", "backend", "attrs", "x", "filterSize", "strides", "pad", "dimRoundingMode", "dataFormat", "convInfo", "computePool3DInfo", "shape", "xValues", "data", "get", "dataId", "values", "outBuf", "dtype", "computeStrides", "makeTensorInfo", "maxPool3DConfig", "kernelName", "backendName", "kernelFunc"], "sources": ["C:\\tfjs-backend-cpu\\src\\kernels\\MaxPool3D.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {backend_util, KernelConfig, KernelFunc, MaxPool3D, MaxPool3DAttrs, MaxPool3DInputs, TensorInfo, TypedArray, util} from '@tensorflow/tfjs-core';\n\nimport {MathBackendCPU} from '../backend_cpu';\nimport {assertNotComplex} from '../cpu_util';\nimport {pool3d} from '../utils/pool_utils';\n\nexport function maxPool3D(args: {\n  inputs: MaxPool3DInputs,\n  backend: MathBackendCPU,\n  attrs: MaxPool3DAttrs\n}): TensorInfo {\n  const {inputs, backend, attrs} = args;\n  const {x} = inputs;\n  const {filterSize, strides, pad, dimRoundingMode, dataFormat} = attrs;\n\n  assertNotComplex(x, 'maxPool3d');\n\n  const convInfo = backend_util.computePool3DInfo(\n      x.shape as [number, number, number, number, number], filterSize, strides,\n      1 /* dilations */, pad, dimRoundingMode, dataFormat);\n\n  const xValues = backend.data.get(x.dataId).values as TypedArray;\n  const outBuf = pool3d(\n      xValues, x.shape, x.dtype, util.computeStrides(x.shape), convInfo, 'max');\n\n  return backend.makeTensorInfo(outBuf.shape, 'float32', outBuf.values);\n}\n\nexport const maxPool3DConfig: KernelConfig = {\n  kernelName: MaxPool3D,\n  backendName: 'cpu',\n  kernelFunc: maxPool3D as unknown as KernelFunc\n};\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAiBA,SAAQA,YAAY,EAA4BC,SAAS,EAA2DC,IAAI,QAAO,uBAAuB;AAGtJ,SAAQC,gBAAgB,QAAO,aAAa;AAC5C,SAAQC,MAAM,QAAO,qBAAqB;AAE1C,OAAM,SAAUC,SAASA,CAACC,IAIzB;EACC,MAAM;IAACC,MAAM;IAAEC,OAAO;IAAEC;EAAK,CAAC,GAAGH,IAAI;EACrC,MAAM;IAACI;EAAC,CAAC,GAAGH,MAAM;EAClB,MAAM;IAACI,UAAU;IAAEC,OAAO;IAAEC,GAAG;IAAEC,eAAe;IAAEC;EAAU,CAAC,GAAGN,KAAK;EAErEN,gBAAgB,CAACO,CAAC,EAAE,WAAW,CAAC;EAEhC,MAAMM,QAAQ,GAAGhB,YAAY,CAACiB,iBAAiB,CAC3CP,CAAC,CAACQ,KAAiD,EAAEP,UAAU,EAAEC,OAAO,EACxE,CAAC,CAAC,iBAAiBC,GAAG,EAAEC,eAAe,EAAEC,UAAU,CAAC;EAExD,MAAMI,OAAO,GAAGX,OAAO,CAACY,IAAI,CAACC,GAAG,CAACX,CAAC,CAACY,MAAM,CAAC,CAACC,MAAoB;EAC/D,MAAMC,MAAM,GAAGpB,MAAM,CACjBe,OAAO,EAAET,CAAC,CAACQ,KAAK,EAAER,CAAC,CAACe,KAAK,EAAEvB,IAAI,CAACwB,cAAc,CAAChB,CAAC,CAACQ,KAAK,CAAC,EAAEF,QAAQ,EAAE,KAAK,CAAC;EAE7E,OAAOR,OAAO,CAACmB,cAAc,CAACH,MAAM,CAACN,KAAK,EAAE,SAAS,EAAEM,MAAM,CAACD,MAAM,CAAC;AACvE;AAEA,OAAO,MAAMK,eAAe,GAAiB;EAC3CC,UAAU,EAAE5B,SAAS;EACrB6B,WAAW,EAAE,KAAK;EAClBC,UAAU,EAAE1B;CACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}