{"ast": null, "code": "import restArguments from './restArguments.js';\nimport flatten from './_flatten.js';\nimport bind from './bind.js';\n\n// Bind a number of an object's methods to that object. Remaining arguments\n// are the method names to be bound. Useful for ensuring that all callbacks\n// defined on an object belong to it.\nexport default restArguments(function (obj, keys) {\n  keys = flatten(keys, false, false);\n  var index = keys.length;\n  if (index < 1) throw new Error('bindAll must be passed function names');\n  while (index--) {\n    var key = keys[index];\n    obj[key] = bind(obj[key], obj);\n  }\n  return obj;\n});", "map": {"version": 3, "names": ["restArguments", "flatten", "bind", "obj", "keys", "index", "length", "Error", "key"], "sources": ["C:/tmsft/node_modules/underscore/modules/bindAll.js"], "sourcesContent": ["import restArguments from './restArguments.js';\nimport flatten from './_flatten.js';\nimport bind from './bind.js';\n\n// Bind a number of an object's methods to that object. Remaining arguments\n// are the method names to be bound. Useful for ensuring that all callbacks\n// defined on an object belong to it.\nexport default restArguments(function(obj, keys) {\n  keys = flatten(keys, false, false);\n  var index = keys.length;\n  if (index < 1) throw new Error('bindAll must be passed function names');\n  while (index--) {\n    var key = keys[index];\n    obj[key] = bind(obj[key], obj);\n  }\n  return obj;\n});\n"], "mappings": "AAAA,OAAOA,aAAa,MAAM,oBAAoB;AAC9C,OAAOC,OAAO,MAAM,eAAe;AACnC,OAAOC,IAAI,MAAM,WAAW;;AAE5B;AACA;AACA;AACA,eAAeF,aAAa,CAAC,UAASG,GAAG,EAAEC,IAAI,EAAE;EAC/CA,IAAI,GAAGH,OAAO,CAACG,IAAI,EAAE,KAAK,EAAE,KAAK,CAAC;EAClC,IAAIC,KAAK,GAAGD,IAAI,CAACE,MAAM;EACvB,IAAID,KAAK,GAAG,CAAC,EAAE,MAAM,IAAIE,KAAK,CAAC,uCAAuC,CAAC;EACvE,OAAOF,KAAK,EAAE,EAAE;IACd,IAAIG,GAAG,GAAGJ,IAAI,CAACC,KAAK,CAAC;IACrBF,GAAG,CAACK,GAAG,CAAC,GAAGN,IAAI,CAACC,GAAG,CAACK,GAAG,CAAC,EAAEL,GAAG,CAAC;EAChC;EACA,OAAOA,GAAG;AACZ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}