{"ast": null, "code": "/*\nSimple Example Element class\nCopyright (C) 2018 Hugo <PERSON>t\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in\nall copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\nTHE SOFTWARE.\n*/\n\n'use strict';\n\nconst Element = require('../Element');\nconst Feature = require('../Feature');\nclass SEElement extends Element {\n  generateFeatures(featureSet) {\n    function isZero(x) {\n      if (x.a === 'x' && x.b.data === '0') {\n        return 1;\n      }\n      return 0;\n    }\n    featureSet.addFeature(new Feature(isZero, 'isZero', ['0']));\n    function isOne(x) {\n      if (x.a === 'y' && x.b.data === '1') {\n        return 1;\n      }\n      return 0;\n    }\n    featureSet.addFeature(new Feature(isOne, 'isOne', ['1']));\n  }\n}\nmodule.exports = SEElement;", "map": {"version": 3, "names": ["Element", "require", "Feature", "SEElement", "generateFeatures", "featureSet", "isZero", "x", "a", "b", "data", "addFeature", "isOne", "module", "exports"], "sources": ["C:/tmsft/node_modules/natural/lib/natural/classifiers/maxent/SimpleExample/SE_Element.js"], "sourcesContent": ["/*\nSimple Example Element class\nCopyright (C) 2018 Hugo <PERSON>t\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in\nall copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\nTHE SOFTWARE.\n*/\n\n'use strict'\n\nconst Element = require('../Element')\nconst Feature = require('../Feature')\n\nclass SEElement extends Element {\n  generateFeatures (featureSet) {\n    function isZero (x) {\n      if ((x.a === 'x') && (x.b.data === '0')) {\n        return 1\n      }\n      return 0\n    }\n    featureSet.addFeature(new Feature(isZero, 'isZero', ['0']))\n\n    function isOne (x) {\n      if ((x.a === 'y') && (x.b.data === '1')) {\n        return 1\n      }\n      return 0\n    }\n    featureSet.addFeature(new Feature(isOne, 'isOne', ['1']))\n  }\n}\n\nmodule.exports = SEElement\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,YAAY;;AAEZ,MAAMA,OAAO,GAAGC,OAAO,CAAC,YAAY,CAAC;AACrC,MAAMC,OAAO,GAAGD,OAAO,CAAC,YAAY,CAAC;AAErC,MAAME,SAAS,SAASH,OAAO,CAAC;EAC9BI,gBAAgBA,CAAEC,UAAU,EAAE;IAC5B,SAASC,MAAMA,CAAEC,CAAC,EAAE;MAClB,IAAKA,CAAC,CAACC,CAAC,KAAK,GAAG,IAAMD,CAAC,CAACE,CAAC,CAACC,IAAI,KAAK,GAAI,EAAE;QACvC,OAAO,CAAC;MACV;MACA,OAAO,CAAC;IACV;IACAL,UAAU,CAACM,UAAU,CAAC,IAAIT,OAAO,CAACI,MAAM,EAAE,QAAQ,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC;IAE3D,SAASM,KAAKA,CAAEL,CAAC,EAAE;MACjB,IAAKA,CAAC,CAACC,CAAC,KAAK,GAAG,IAAMD,CAAC,CAACE,CAAC,CAACC,IAAI,KAAK,GAAI,EAAE;QACvC,OAAO,CAAC;MACV;MACA,OAAO,CAAC;IACV;IACAL,UAAU,CAACM,UAAU,CAAC,IAAIT,OAAO,CAACU,KAAK,EAAE,OAAO,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC;EAC3D;AACF;AAEAC,MAAM,CAACC,OAAO,GAAGX,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}