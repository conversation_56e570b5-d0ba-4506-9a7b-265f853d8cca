{"ast": null, "code": "\"use strict\";\n\nvar __createBinding = this && this.__createBinding || (Object.create ? function (o, m, k, k2) {\n  if (k2 === undefined) k2 = k;\n  var desc = Object.getOwnPropertyDescriptor(m, k);\n  if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\n    desc = {\n      enumerable: true,\n      get: function () {\n        return m[k];\n      }\n    };\n  }\n  Object.defineProperty(o, k2, desc);\n} : function (o, m, k, k2) {\n  if (k2 === undefined) k2 = k;\n  o[k2] = m[k];\n});\nvar __exportStar = this && this.__exportStar || function (m, exports) {\n  for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);\n};\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.createCluster = exports.createClient = void 0;\nconst client_1 = require(\"@redis/client\");\nconst bloom_1 = require(\"@redis/bloom\");\nconst graph_1 = require(\"@redis/graph\");\nconst json_1 = require(\"@redis/json\");\nconst search_1 = require(\"@redis/search\");\nconst time_series_1 = require(\"@redis/time-series\");\n__exportStar(require(\"@redis/client\"), exports);\n__exportStar(require(\"@redis/bloom\"), exports);\n__exportStar(require(\"@redis/graph\"), exports);\n__exportStar(require(\"@redis/json\"), exports);\n__exportStar(require(\"@redis/search\"), exports);\n__exportStar(require(\"@redis/time-series\"), exports);\nconst modules = {\n  ...bloom_1.default,\n  graph: graph_1.default,\n  json: json_1.default,\n  ft: search_1.default,\n  ts: time_series_1.default\n};\nfunction createClient(options) {\n  return (0, client_1.createClient)({\n    ...options,\n    modules: {\n      ...modules,\n      ...options?.modules\n    }\n  });\n}\nexports.createClient = createClient;\nfunction createCluster(options) {\n  return (0, client_1.createCluster)({\n    ...options,\n    modules: {\n      ...modules,\n      ...options?.modules\n    }\n  });\n}\nexports.createCluster = createCluster;", "map": {"version": 3, "names": ["__createBinding", "Object", "create", "o", "m", "k", "k2", "undefined", "desc", "getOwnPropertyDescriptor", "__esModule", "writable", "configurable", "enumerable", "get", "defineProperty", "__exportStar", "exports", "p", "prototype", "hasOwnProperty", "call", "value", "createCluster", "createClient", "client_1", "require", "bloom_1", "graph_1", "json_1", "search_1", "time_series_1", "modules", "default", "graph", "json", "ft", "ts", "options"], "sources": ["C:/tmsft/node_modules/redis/dist/index.js"], "sourcesContent": ["\"use strict\";\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    var desc = Object.getOwnPropertyDescriptor(m, k);\n    if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\n      desc = { enumerable: true, get: function() { return m[k]; } };\n    }\n    Object.defineProperty(o, k2, desc);\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __exportStar = (this && this.__exportStar) || function(m, exports) {\n    for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.createCluster = exports.createClient = void 0;\nconst client_1 = require(\"@redis/client\");\nconst bloom_1 = require(\"@redis/bloom\");\nconst graph_1 = require(\"@redis/graph\");\nconst json_1 = require(\"@redis/json\");\nconst search_1 = require(\"@redis/search\");\nconst time_series_1 = require(\"@redis/time-series\");\n__exportStar(require(\"@redis/client\"), exports);\n__exportStar(require(\"@redis/bloom\"), exports);\n__exportStar(require(\"@redis/graph\"), exports);\n__exportStar(require(\"@redis/json\"), exports);\n__exportStar(require(\"@redis/search\"), exports);\n__exportStar(require(\"@redis/time-series\"), exports);\nconst modules = {\n    ...bloom_1.default,\n    graph: graph_1.default,\n    json: json_1.default,\n    ft: search_1.default,\n    ts: time_series_1.default\n};\nfunction createClient(options) {\n    return (0, client_1.createClient)({\n        ...options,\n        modules: {\n            ...modules,\n            ...options?.modules\n        }\n    });\n}\nexports.createClient = createClient;\nfunction createCluster(options) {\n    return (0, client_1.createCluster)({\n        ...options,\n        modules: {\n            ...modules,\n            ...options?.modules\n        }\n    });\n}\nexports.createCluster = createCluster;\n"], "mappings": "AAAA,YAAY;;AACZ,IAAIA,eAAe,GAAI,IAAI,IAAI,IAAI,CAACA,eAAe,KAAMC,MAAM,CAACC,MAAM,GAAI,UAASC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEC,EAAE,EAAE;EAC5F,IAAIA,EAAE,KAAKC,SAAS,EAAED,EAAE,GAAGD,CAAC;EAC5B,IAAIG,IAAI,GAAGP,MAAM,CAACQ,wBAAwB,CAACL,CAAC,EAAEC,CAAC,CAAC;EAChD,IAAI,CAACG,IAAI,KAAK,KAAK,IAAIA,IAAI,GAAG,CAACJ,CAAC,CAACM,UAAU,GAAGF,IAAI,CAACG,QAAQ,IAAIH,IAAI,CAACI,YAAY,CAAC,EAAE;IACjFJ,IAAI,GAAG;MAAEK,UAAU,EAAE,IAAI;MAAEC,GAAG,EAAE,SAAAA,CAAA,EAAW;QAAE,OAAOV,CAAC,CAACC,CAAC,CAAC;MAAE;IAAE,CAAC;EAC/D;EACAJ,MAAM,CAACc,cAAc,CAACZ,CAAC,EAAEG,EAAE,EAAEE,IAAI,CAAC;AACtC,CAAC,GAAK,UAASL,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEC,EAAE,EAAE;EACxB,IAAIA,EAAE,KAAKC,SAAS,EAAED,EAAE,GAAGD,CAAC;EAC5BF,CAAC,CAACG,EAAE,CAAC,GAAGF,CAAC,CAACC,CAAC,CAAC;AAChB,CAAE,CAAC;AACH,IAAIW,YAAY,GAAI,IAAI,IAAI,IAAI,CAACA,YAAY,IAAK,UAASZ,CAAC,EAAEa,OAAO,EAAE;EACnE,KAAK,IAAIC,CAAC,IAAId,CAAC,EAAE,IAAIc,CAAC,KAAK,SAAS,IAAI,CAACjB,MAAM,CAACkB,SAAS,CAACC,cAAc,CAACC,IAAI,CAACJ,OAAO,EAAEC,CAAC,CAAC,EAAElB,eAAe,CAACiB,OAAO,EAAEb,CAAC,EAAEc,CAAC,CAAC;AAC7H,CAAC;AACDjB,MAAM,CAACc,cAAc,CAACE,OAAO,EAAE,YAAY,EAAE;EAAEK,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7DL,OAAO,CAACM,aAAa,GAAGN,OAAO,CAACO,YAAY,GAAG,KAAK,CAAC;AACrD,MAAMC,QAAQ,GAAGC,OAAO,CAAC,eAAe,CAAC;AACzC,MAAMC,OAAO,GAAGD,OAAO,CAAC,cAAc,CAAC;AACvC,MAAME,OAAO,GAAGF,OAAO,CAAC,cAAc,CAAC;AACvC,MAAMG,MAAM,GAAGH,OAAO,CAAC,aAAa,CAAC;AACrC,MAAMI,QAAQ,GAAGJ,OAAO,CAAC,eAAe,CAAC;AACzC,MAAMK,aAAa,GAAGL,OAAO,CAAC,oBAAoB,CAAC;AACnDV,YAAY,CAACU,OAAO,CAAC,eAAe,CAAC,EAAET,OAAO,CAAC;AAC/CD,YAAY,CAACU,OAAO,CAAC,cAAc,CAAC,EAAET,OAAO,CAAC;AAC9CD,YAAY,CAACU,OAAO,CAAC,cAAc,CAAC,EAAET,OAAO,CAAC;AAC9CD,YAAY,CAACU,OAAO,CAAC,aAAa,CAAC,EAAET,OAAO,CAAC;AAC7CD,YAAY,CAACU,OAAO,CAAC,eAAe,CAAC,EAAET,OAAO,CAAC;AAC/CD,YAAY,CAACU,OAAO,CAAC,oBAAoB,CAAC,EAAET,OAAO,CAAC;AACpD,MAAMe,OAAO,GAAG;EACZ,GAAGL,OAAO,CAACM,OAAO;EAClBC,KAAK,EAAEN,OAAO,CAACK,OAAO;EACtBE,IAAI,EAAEN,MAAM,CAACI,OAAO;EACpBG,EAAE,EAAEN,QAAQ,CAACG,OAAO;EACpBI,EAAE,EAAEN,aAAa,CAACE;AACtB,CAAC;AACD,SAAST,YAAYA,CAACc,OAAO,EAAE;EAC3B,OAAO,CAAC,CAAC,EAAEb,QAAQ,CAACD,YAAY,EAAE;IAC9B,GAAGc,OAAO;IACVN,OAAO,EAAE;MACL,GAAGA,OAAO;MACV,GAAGM,OAAO,EAAEN;IAChB;EACJ,CAAC,CAAC;AACN;AACAf,OAAO,CAACO,YAAY,GAAGA,YAAY;AACnC,SAASD,aAAaA,CAACe,OAAO,EAAE;EAC5B,OAAO,CAAC,CAAC,EAAEb,QAAQ,CAACF,aAAa,EAAE;IAC/B,GAAGe,OAAO;IACVN,OAAO,EAAE;MACL,GAAGA,OAAO;MACV,GAAGM,OAAO,EAAEN;IAChB;EACJ,CAAC,CAAC;AACN;AACAf,OAAO,CAACM,aAAa,GAAGA,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}