{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.transformArguments = exports.FIRST_KEY_INDEX = exports.HASH_EXPIRATION = void 0;\nconst generic_transformers_1 = require(\"./generic-transformers\");\n/**\n * @readonly\n * @enum {number}\n */\nexports.HASH_EXPIRATION = {\n  /** @property {number} */\n  /** The field does not exist */\n  FIELD_NOT_EXISTS: -2,\n  /** @property {number} */\n  /** Specified NX | XX | GT | LT condition not met */\n  CONDITION_NOT_MET: 0,\n  /** @property {number} */\n  /** Expiration time was set or updated */\n  UPDATED: 1,\n  /** @property {number} */\n  /** Field deleted because the specified expiration time is in the past */\n  DELETED: 2\n};\nexports.FIRST_KEY_INDEX = 1;\nfunction transformArguments(key, fields, seconds, mode) {\n  const args = ['HEXPIRE', key, seconds.toString()];\n  if (mode) {\n    args.push(mode);\n  }\n  args.push('FIELDS');\n  return (0, generic_transformers_1.pushVerdictArgument)(args, fields);\n}\nexports.transformArguments = transformArguments;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "transformArguments", "FIRST_KEY_INDEX", "HASH_EXPIRATION", "generic_transformers_1", "require", "FIELD_NOT_EXISTS", "CONDITION_NOT_MET", "UPDATED", "DELETED", "key", "fields", "seconds", "mode", "args", "toString", "push", "pushVerdictArgument"], "sources": ["C:/tmsft/node_modules/@redis/client/dist/lib/commands/HEXPIRE.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.transformArguments = exports.FIRST_KEY_INDEX = exports.HASH_EXPIRATION = void 0;\nconst generic_transformers_1 = require(\"./generic-transformers\");\n/**\n * @readonly\n * @enum {number}\n */\nexports.HASH_EXPIRATION = {\n    /** @property {number} */\n    /** The field does not exist */\n    FIELD_NOT_EXISTS: -2,\n    /** @property {number} */\n    /** Specified NX | XX | GT | LT condition not met */\n    CONDITION_NOT_MET: 0,\n    /** @property {number} */\n    /** Expiration time was set or updated */\n    UPDATED: 1,\n    /** @property {number} */\n    /** Field deleted because the specified expiration time is in the past */\n    DELETED: 2\n};\nexports.FIRST_KEY_INDEX = 1;\nfunction transformArguments(key, fields, seconds, mode) {\n    const args = ['HEXPIRE', key, seconds.toString()];\n    if (mode) {\n        args.push(mode);\n    }\n    args.push('FIELDS');\n    return (0, generic_transformers_1.pushVerdictArgument)(args, fields);\n}\nexports.transformArguments = transformArguments;\n"], "mappings": "AAAA,YAAY;;AACZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEC,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7DD,OAAO,CAACE,kBAAkB,GAAGF,OAAO,CAACG,eAAe,GAAGH,OAAO,CAACI,eAAe,GAAG,KAAK,CAAC;AACvF,MAAMC,sBAAsB,GAAGC,OAAO,CAAC,wBAAwB,CAAC;AAChE;AACA;AACA;AACA;AACAN,OAAO,CAACI,eAAe,GAAG;EACtB;EACA;EACAG,gBAAgB,EAAE,CAAC,CAAC;EACpB;EACA;EACAC,iBAAiB,EAAE,CAAC;EACpB;EACA;EACAC,OAAO,EAAE,CAAC;EACV;EACA;EACAC,OAAO,EAAE;AACb,CAAC;AACDV,OAAO,CAACG,eAAe,GAAG,CAAC;AAC3B,SAASD,kBAAkBA,CAACS,GAAG,EAAEC,MAAM,EAAEC,OAAO,EAAEC,IAAI,EAAE;EACpD,MAAMC,IAAI,GAAG,CAAC,SAAS,EAAEJ,GAAG,EAAEE,OAAO,CAACG,QAAQ,CAAC,CAAC,CAAC;EACjD,IAAIF,IAAI,EAAE;IACNC,IAAI,CAACE,IAAI,CAACH,IAAI,CAAC;EACnB;EACAC,IAAI,CAACE,IAAI,CAAC,QAAQ,CAAC;EACnB,OAAO,CAAC,CAAC,EAAEZ,sBAAsB,CAACa,mBAAmB,EAAEH,IAAI,EAAEH,MAAM,CAAC;AACxE;AACAZ,OAAO,CAACE,kBAAkB,GAAGA,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}