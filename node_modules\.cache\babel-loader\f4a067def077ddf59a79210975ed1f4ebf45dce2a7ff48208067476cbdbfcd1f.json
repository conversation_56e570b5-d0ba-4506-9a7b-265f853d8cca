{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.transformReply = exports.transformArguments = exports.FIRST_KEY_INDEX = void 0;\nconst XAUTOCLAIM_1 = require(\"./XAUTOCLAIM\");\nvar XAUTOCLAIM_2 = require(\"./XAUTOCLAIM\");\nObject.defineProperty(exports, \"FIRST_KEY_INDEX\", {\n  enumerable: true,\n  get: function () {\n    return XAUTOCLAIM_2.FIRST_KEY_INDEX;\n  }\n});\nfunction transformArguments(...args) {\n  return [...(0, XAUTOCLAIM_1.transformArguments)(...args), 'JUSTID'];\n}\nexports.transformArguments = transformArguments;\nfunction transformReply(reply) {\n  return {\n    nextId: reply[0],\n    messages: reply[1]\n  };\n}\nexports.transformReply = transformReply;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "transformReply", "transformArguments", "FIRST_KEY_INDEX", "XAUTOCLAIM_1", "require", "XAUTOCLAIM_2", "enumerable", "get", "args", "reply", "nextId", "messages"], "sources": ["C:/tmsft/node_modules/@redis/client/dist/lib/commands/XAUTOCLAIM_JUSTID.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.transformReply = exports.transformArguments = exports.FIRST_KEY_INDEX = void 0;\nconst XAUTOCLAIM_1 = require(\"./XAUTOCLAIM\");\nvar XAUTOCLAIM_2 = require(\"./XAUTOCLAIM\");\nObject.defineProperty(exports, \"FIRST_KEY_INDEX\", { enumerable: true, get: function () { return XAUTOCLAIM_2.FIRST_KEY_INDEX; } });\nfunction transformArguments(...args) {\n    return [\n        ...(0, XAUTOCLAIM_1.transformArguments)(...args),\n        'JUSTID'\n    ];\n}\nexports.transformArguments = transformArguments;\nfunction transformReply(reply) {\n    return {\n        nextId: reply[0],\n        messages: reply[1]\n    };\n}\nexports.transformReply = transformReply;\n"], "mappings": "AAAA,YAAY;;AACZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEC,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7DD,OAAO,CAACE,cAAc,GAAGF,OAAO,CAACG,kBAAkB,GAAGH,OAAO,CAACI,eAAe,GAAG,KAAK,CAAC;AACtF,MAAMC,YAAY,GAAGC,OAAO,CAAC,cAAc,CAAC;AAC5C,IAAIC,YAAY,GAAGD,OAAO,CAAC,cAAc,CAAC;AAC1CR,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,iBAAiB,EAAE;EAAEQ,UAAU,EAAE,IAAI;EAAEC,GAAG,EAAE,SAAAA,CAAA,EAAY;IAAE,OAAOF,YAAY,CAACH,eAAe;EAAE;AAAE,CAAC,CAAC;AAClI,SAASD,kBAAkBA,CAAC,GAAGO,IAAI,EAAE;EACjC,OAAO,CACH,GAAG,CAAC,CAAC,EAAEL,YAAY,CAACF,kBAAkB,EAAE,GAAGO,IAAI,CAAC,EAChD,QAAQ,CACX;AACL;AACAV,OAAO,CAACG,kBAAkB,GAAGA,kBAAkB;AAC/C,SAASD,cAAcA,CAACS,KAAK,EAAE;EAC3B,OAAO;IACHC,MAAM,EAAED,KAAK,CAAC,CAAC,CAAC;IAChBE,QAAQ,EAAEF,KAAK,CAAC,CAAC;EACrB,CAAC;AACL;AACAX,OAAO,CAACE,cAAc,GAAGA,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}