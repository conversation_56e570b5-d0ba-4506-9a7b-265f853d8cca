[{"C:\\tmsft\\src\\index.tsx": "1", "C:\\tmsft\\src\\App.tsx": "2", "C:\\tmsft\\src\\components\\DataHub.tsx": "3", "C:\\tmsft\\src\\components\\BankStatementImport.tsx": "4", "C:\\tmsft\\src\\components\\FileUpload.tsx": "5", "C:\\tmsft\\src\\services\\csvProcessingService.ts": "6", "C:\\tmsft\\src\\services\\bankAccountService.ts": "7", "C:\\tmsft\\src\\components\\BankAccountManager.tsx": "8", "C:\\tmsft\\src\\services\\balanceManagementService.ts": "9", "C:\\tmsft\\src\\services\\importHistoryService.ts": "10", "C:\\tmsft\\src\\services\\transactionStorageService.ts": "11", "C:\\tmsft\\src\\components\\BalanceValidationDialog.tsx": "12", "C:\\tmsft\\src\\services\\fileStorageService.ts": "13", "C:\\tmsft\\src\\components\\Transactions.tsx": "14", "C:\\tmsft\\src\\services\\duplicateDetectionService.ts": "15", "C:\\tmsft\\src\\components\\FileManager.tsx": "16", "C:\\tmsft\\src\\services\\mlCategorizationService.ts": "17", "C:\\tmsft\\src\\components\\TransactionCategorization.tsx": "18", "C:\\tmsft\\src\\components\\BankBalance.tsx": "19", "C:\\tmsft\\src\\services\\bankBalanceService.ts": "20", "C:\\tmsft\\src\\services\\categorizationService.ts": "21"}, {"size": 264, "mtime": *************, "results": "22", "hashOfConfig": "23"}, {"size": 767, "mtime": *************, "results": "24", "hashOfConfig": "23"}, {"size": 9428, "mtime": *************, "results": "25", "hashOfConfig": "23"}, {"size": 22329, "mtime": *************, "results": "26", "hashOfConfig": "23"}, {"size": 7576, "mtime": *************, "results": "27", "hashOfConfig": "23"}, {"size": 15481, "mtime": *************, "results": "28", "hashOfConfig": "23"}, {"size": 3403, "mtime": *************, "results": "29", "hashOfConfig": "23"}, {"size": 26319, "mtime": *************, "results": "30", "hashOfConfig": "23"}, {"size": 11563, "mtime": *************, "results": "31", "hashOfConfig": "23"}, {"size": 2631, "mtime": *************, "results": "32", "hashOfConfig": "23"}, {"size": 16314, "mtime": *************, "results": "33", "hashOfConfig": "23"}, {"size": 6644, "mtime": *************, "results": "34", "hashOfConfig": "23"}, {"size": 14277, "mtime": 1749874689701, "results": "35", "hashOfConfig": "23"}, {"size": 34561, "mtime": 1749874213122, "results": "36", "hashOfConfig": "23"}, {"size": 8560, "mtime": 1749813804439, "results": "37", "hashOfConfig": "23"}, {"size": 15701, "mtime": 1749874691735, "results": "38", "hashOfConfig": "23"}, {"size": 11231, "mtime": 1749910427570, "results": "39", "hashOfConfig": "23"}, {"size": 21103, "mtime": 1749910916820, "results": "40", "hashOfConfig": "23"}, {"size": 20338, "mtime": 1749846360276, "results": "41", "hashOfConfig": "23"}, {"size": 9725, "mtime": 1749846235395, "results": "42", "hashOfConfig": "23"}, {"size": 9989, "mtime": 1749910066279, "results": "43", "hashOfConfig": "23"}, {"filePath": "44", "messages": "45", "suppressedMessages": "46", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1ip1gr4", {"filePath": "47", "messages": "48", "suppressedMessages": "49", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "50", "messages": "51", "suppressedMessages": "52", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "53", "messages": "54", "suppressedMessages": "55", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "56", "messages": "57", "suppressedMessages": "58", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "59", "messages": "60", "suppressedMessages": "61", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "62", "messages": "63", "suppressedMessages": "64", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "65", "messages": "66", "suppressedMessages": "67", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "68", "messages": "69", "suppressedMessages": "70", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "71", "messages": "72", "suppressedMessages": "73", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "74", "messages": "75", "suppressedMessages": "76", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "77", "messages": "78", "suppressedMessages": "79", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "80", "messages": "81", "suppressedMessages": "82", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "83", "messages": "84", "suppressedMessages": "85", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "86", "messages": "87", "suppressedMessages": "88", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "89", "messages": "90", "suppressedMessages": "91", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "92", "messages": "93", "suppressedMessages": "94", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "95", "messages": "96", "suppressedMessages": "97", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "98", "messages": "99", "suppressedMessages": "100", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "101", "messages": "102", "suppressedMessages": "103", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "104", "messages": "105", "suppressedMessages": "106", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\tmsft\\src\\index.tsx", [], [], "C:\\tmsft\\src\\App.tsx", [], [], "C:\\tmsft\\src\\components\\DataHub.tsx", [], [], "C:\\tmsft\\src\\components\\BankStatementImport.tsx", [], [], "C:\\tmsft\\src\\components\\FileUpload.tsx", [], [], "C:\\tmsft\\src\\services\\csvProcessingService.ts", [], [], "C:\\tmsft\\src\\services\\bankAccountService.ts", [], [], "C:\\tmsft\\src\\components\\BankAccountManager.tsx", [], [], "C:\\tmsft\\src\\services\\balanceManagementService.ts", [], [], "C:\\tmsft\\src\\services\\importHistoryService.ts", [], [], "C:\\tmsft\\src\\services\\transactionStorageService.ts", [], [], "C:\\tmsft\\src\\components\\BalanceValidationDialog.tsx", [], [], "C:\\tmsft\\src\\services\\fileStorageService.ts", [], [], "C:\\tmsft\\src\\components\\Transactions.tsx", ["107"], [], "C:\\tmsft\\src\\services\\duplicateDetectionService.ts", [], [], "C:\\tmsft\\src\\components\\FileManager.tsx", [], [], "C:\\tmsft\\src\\services\\mlCategorizationService.ts", [], [], "C:\\tmsft\\src\\components\\TransactionCategorization.tsx", [], [], "C:\\tmsft\\src\\components\\BankBalance.tsx", ["108"], ["109"], "C:\\tmsft\\src\\services\\bankBalanceService.ts", ["110"], [], "C:\\tmsft\\src\\services\\categorizationService.ts", [], [], {"ruleId": "111", "severity": 1, "message": "112", "line": 185, "column": 6, "nodeType": "113", "endLine": 185, "endColumn": 57, "suggestions": "114"}, {"ruleId": "111", "severity": 1, "message": "115", "line": 73, "column": 6, "nodeType": "113", "endLine": 73, "endColumn": 22, "suggestions": "116"}, {"ruleId": "111", "severity": 1, "message": "115", "line": 78, "column": 6, "nodeType": "113", "endLine": 78, "endColumn": 8, "suggestions": "117", "suppressions": "118"}, {"ruleId": "119", "severity": 1, "message": "120", "line": 98, "column": 13, "nodeType": "121", "messageId": "122", "endLine": 98, "endColumn": 29}, "react-hooks/exhaustive-deps", "React Hook useCallback has a missing dependency: 'transactions.length'. Either include it or remove the dependency array.", "ArrayExpression", ["123"], "React Hook useEffect has a missing dependency: 'loadData'. Either include it or remove the dependency array.", ["124"], ["125"], ["126"], "@typescript-eslint/no-unused-vars", "'firstTransaction' is assigned a value but never used.", "Identifier", "unusedVar", {"desc": "127", "fix": "128"}, {"desc": "129", "fix": "130"}, {"desc": "131", "fix": "132"}, {"kind": "133", "justification": "134"}, "Update the dependencies array to be: [transactions.length, findDuplicatesInTransactions, onTransactionUpdate]", {"range": "135", "text": "136"}, "Update the dependencies array to be: [loadData, refreshTrigger]", {"range": "137", "text": "138"}, "Update the dependencies array to be: [loadData]", {"range": "139", "text": "140"}, "directive", "", [6499, 6550], "[transactions.length, findDuplicatesInTransactions, onTransactionUpdate]", [2464, 2480], "[loadData, refreshTrigger]", [2612, 2614], "[loadData]"]