{"ast": null, "code": "var _jsxFileName = \"C:\\\\tmsft\\\\src\\\\components\\\\MLTrainingInterface.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useCallback } from 'react';\nimport { simpleMlService } from '../services/simpleMlService';\nimport { categoryService } from '../services/categoryService';\nimport { transactionStorageService } from '../services/transactionStorageService';\nimport './MLTrainingInterface.css';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nexport const MLTrainingInterface = ({\n  onClose\n}) => {\n  _s();\n  // State management\n  const [activeTab, setActiveTab] = useState('overview');\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n\n  // ML state\n\n  const [activeModel, setActiveModel] = useState(null);\n  const [trainingData, setTrainingData] = useState([]);\n  const [categories, setCategories] = useState([]);\n\n  // Training state\n  const [trainingProgress, setTrainingProgress] = useState({\n    isTraining: false,\n    currentEpoch: 0,\n    totalEpochs: 100,\n    currentLoss: 0,\n    currentAccuracy: 0,\n    estimatedTimeRemaining: 0\n  });\n\n  // Data management state\n\n  const [dataFilters, setDataFilters] = useState({\n    categoryId: '',\n    confidence: 'all',\n    searchTerm: ''\n  });\n\n  // Performance metrics state\n  const [modelMetrics, setModelMetrics] = useState(null);\n\n  // Load data\n  const loadData = useCallback(async () => {\n    try {\n      setLoading(true);\n      setError(null);\n\n      // Load models and stats\n      const modelStats = simpleMlService.getModelStats();\n      setActiveModel(modelStats.activeModel);\n\n      // Load training data and categories\n      const trainingData = simpleMlService.getTrainingData();\n      const categories = categoryService.getAllCategories();\n      setTrainingData(trainingData);\n      setCategories(categories);\n\n      // Load performance metrics if active model exists\n      if (modelStats.activeModel) {\n        await loadModelMetrics(modelStats.activeModel);\n      }\n    } catch (err) {\n      setError(err instanceof Error ? err.message : 'Failed to load ML data');\n    } finally {\n      setLoading(false);\n    }\n  }, []);\n  useEffect(() => {\n    loadData();\n  }, [loadData]);\n\n  // Load model performance metrics\n  const loadModelMetrics = async model => {\n    // In a real implementation, this would evaluate the model against test data\n    // For now, we'll simulate metrics based on the model's accuracy\n    const simulatedMetrics = {\n      accuracy: model.accuracy,\n      precision: model.accuracy * 0.95,\n      recall: model.accuracy * 0.92,\n      f1Score: model.accuracy * 0.93,\n      confusionMatrix: generateSimulatedConfusionMatrix(categories.length),\n      categoryPerformance: categories.map(category => ({\n        categoryId: category.id,\n        categoryName: category.name,\n        precision: model.accuracy * (0.8 + Math.random() * 0.2),\n        recall: model.accuracy * (0.8 + Math.random() * 0.2),\n        f1Score: model.accuracy * (0.8 + Math.random() * 0.2),\n        support: Math.floor(Math.random() * 50) + 10\n      }))\n    };\n    setModelMetrics(simulatedMetrics);\n  };\n\n  // Generate simulated confusion matrix\n  const generateSimulatedConfusionMatrix = size => {\n    const matrix = [];\n    for (let i = 0; i < size; i++) {\n      matrix[i] = [];\n      for (let j = 0; j < size; j++) {\n        if (i === j) {\n          // Diagonal (correct predictions) - higher values\n          matrix[i][j] = Math.floor(Math.random() * 50) + 20;\n        } else {\n          // Off-diagonal (incorrect predictions) - lower values\n          matrix[i][j] = Math.floor(Math.random() * 10);\n        }\n      }\n    }\n    return matrix;\n  };\n\n  // Training functions\n  const handleStartTraining = async () => {\n    if (trainingData.length < 10) {\n      alert('Need at least 10 training examples to train a model.');\n      return;\n    }\n    setTrainingProgress(prev => ({\n      ...prev,\n      isTraining: true,\n      currentEpoch: 0,\n      currentLoss: 1.0,\n      currentAccuracy: 0.1\n    }));\n    try {\n      // Simulate training progress\n      const startTime = Date.now();\n      const totalEpochs = 100;\n      for (let epoch = 1; epoch <= totalEpochs; epoch++) {\n        // Simulate epoch delay\n        await new Promise(resolve => setTimeout(resolve, 100));\n        const progress = epoch / totalEpochs;\n        const currentLoss = 1.0 * Math.exp(-progress * 3) + Math.random() * 0.1;\n        const currentAccuracy = Math.min(0.95, 0.1 + progress * 0.8 + Math.random() * 0.1);\n        const elapsedTime = Date.now() - startTime;\n        const estimatedTotal = elapsedTime / progress;\n        const estimatedTimeRemaining = Math.max(0, estimatedTotal - elapsedTime);\n        setTrainingProgress({\n          isTraining: true,\n          currentEpoch: epoch,\n          totalEpochs,\n          currentLoss,\n          currentAccuracy,\n          estimatedTimeRemaining\n        });\n\n        // Break if user closed or component unmounted\n        if (!trainingProgress.isTraining) break;\n      }\n\n      // Actually train the model\n      const result = await mlCategorizationService.trainModel(trainingData);\n\n      // Update state\n      await loadData();\n      alert(`Training completed! Final accuracy: ${(result.accuracy * 100).toFixed(1)}%`);\n    } catch (error) {\n      console.error('Training failed:', error);\n      alert('Training failed. Please check the console for details.');\n    } finally {\n      setTrainingProgress(prev => ({\n        ...prev,\n        isTraining: false\n      }));\n    }\n  };\n  const handleStopTraining = () => {\n    setTrainingProgress(prev => ({\n      ...prev,\n      isTraining: false\n    }));\n  };\n  const handleClearTrainingData = async () => {\n    const confirmed = window.confirm('Are you sure you want to clear all training data? This cannot be undone.');\n    if (confirmed) {\n      mlCategorizationService.clearTrainingData();\n      await loadData();\n    }\n  };\n  const handleGenerateTrainingData = async () => {\n    // Generate training data from manually categorized transactions\n    const stats = transactionStorageService.getCategorizationStats();\n    if (stats.manuallyCateged === 0) {\n      alert('No manually categorized transactions found. Please categorize some transactions first.');\n      return;\n    }\n    let addedCount = 0;\n\n    // Get all transactions and find manually categorized ones\n    categories.forEach(category => {\n      const categoryTransactions = transactionStorageService.getTransactionsByCategory(category.id);\n      const manuallyCateged = categoryTransactions.filter(t => t.manualCategoryId);\n      manuallyCateged.forEach(transaction => {\n        const amount = (transaction.creditAmount || 0) - (transaction.debitAmount || 0);\n\n        // Check if this data already exists\n        const exists = trainingData.some(td => td.description === transaction.description && td.amount === amount && td.categoryId === category.id);\n        if (!exists) {\n          mlCategorizationService.addTrainingData(transaction.description, amount, category.id);\n          addedCount++;\n        }\n      });\n    });\n    if (addedCount > 0) {\n      await loadData();\n      alert(`Added ${addedCount} new training examples from manually categorized transactions.`);\n    } else {\n      alert('No new training data could be generated. All manually categorized transactions are already in the training set.');\n    }\n  };\n\n  // Data management functions\n\n  // Helper functions\n  const getCategoryName = categoryId => {\n    const category = categories.find(c => c.id === categoryId);\n    return (category === null || category === void 0 ? void 0 : category.name) || 'Unknown Category';\n  };\n  const formatDuration = ms => {\n    const seconds = Math.floor(ms / 1000);\n    const minutes = Math.floor(seconds / 60);\n    const hours = Math.floor(minutes / 60);\n    if (hours > 0) return `${hours}h ${minutes % 60}m`;\n    if (minutes > 0) return `${minutes}m ${seconds % 60}s`;\n    return `${seconds}s`;\n  };\n  const getDataQualityScore = () => {\n    if (trainingData.length === 0) return 0;\n\n    // Calculate based on data distribution across categories\n    const categoryDistribution = categories.map(category => ({\n      categoryId: category.id,\n      count: trainingData.filter(td => td.categoryId === category.id).length\n    }));\n    const totalData = trainingData.length;\n    const avgPerCategory = totalData / categories.length;\n    const variance = categoryDistribution.reduce((acc, cat) => {\n      return acc + Math.pow(cat.count - avgPerCategory, 2);\n    }, 0) / categories.length;\n\n    // Score based on balance (lower variance = better balance = higher score)\n    const maxVariance = Math.pow(avgPerCategory, 2);\n    const balanceScore = Math.max(0, 1 - variance / maxVariance);\n\n    // Score based on total amount of data\n    const dataAmountScore = Math.min(1, totalData / 100); // 100 examples = perfect score\n\n    return (balanceScore * 0.6 + dataAmountScore * 0.4) * 100;\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"ml-training-loading\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"loading-spinner\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 311,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"Loading ML training interface...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 312,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 310,\n      columnNumber: 7\n    }, this);\n  }\n  if (error) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"ml-training-error\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"error-icon\",\n        children: \"\\u26A0\\uFE0F\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 320,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n        children: \"Error Loading ML Interface\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 321,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: error\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 322,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: loadData,\n        className: \"btn btn-primary\",\n        children: \"Try Again\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 323,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 319,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"ml-training-interface\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"ml-training-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"header-title\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          children: \"Machine Learning Training Center\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 335,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Train and manage AI models for transaction categorization\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 336,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 334,\n        columnNumber: 9\n      }, this), onClose && /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: onClose,\n        className: \"close-btn\",\n        children: /*#__PURE__*/_jsxDEV(\"svg\", {\n          width: \"20\",\n          height: \"20\",\n          viewBox: \"0 0 24 24\",\n          fill: \"none\",\n          stroke: \"currentColor\",\n          strokeWidth: \"2\",\n          children: [/*#__PURE__*/_jsxDEV(\"line\", {\n            x1: \"18\",\n            y1: \"6\",\n            x2: \"6\",\n            y2: \"18\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 341,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"line\", {\n            x1: \"6\",\n            y1: \"6\",\n            x2: \"18\",\n            y2: \"18\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 342,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 340,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 339,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 333,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"ml-training-tabs\",\n      children: [{\n        id: 'overview',\n        label: 'Overview',\n        icon: '📊'\n      }, {\n        id: 'training',\n        label: 'Training',\n        icon: '🧠'\n      }, {\n        id: 'data',\n        label: 'Training Data',\n        icon: '📁'\n      }, {\n        id: 'performance',\n        label: 'Performance',\n        icon: '📈'\n      }].map(tab => /*#__PURE__*/_jsxDEV(\"button\", {\n        className: `tab-btn ${activeTab === tab.id ? 'active' : ''}`,\n        onClick: () => setActiveTab(tab.id),\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"tab-icon\",\n          children: tab.icon\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 361,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"tab-label\",\n          children: tab.label\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 362,\n          columnNumber: 13\n        }, this)]\n      }, tab.id, true, {\n        fileName: _jsxFileName,\n        lineNumber: 356,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 349,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"ml-training-content\",\n      children: [activeTab === 'overview' && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"overview-tab\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"overview-cards\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"overview-card\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"card-header\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                children: \"Model Status\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 375,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: `status-badge ${activeModel ? 'active' : 'inactive'}`,\n                children: activeModel ? 'Active' : 'No Model'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 376,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 374,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"card-content\",\n              children: activeModel ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"metric\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"metric-label\",\n                    children: \"Accuracy\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 384,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"metric-value\",\n                    children: [(activeModel.accuracy * 100).toFixed(1), \"%\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 385,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 383,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"metric\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"metric-label\",\n                    children: \"Training Size\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 388,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"metric-value\",\n                    children: activeModel.trainingSize\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 389,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 387,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"metric\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"metric-label\",\n                    children: \"Trained\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 392,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"metric-value\",\n                    children: new Date(activeModel.trainingDate).toLocaleDateString()\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 393,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 391,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true) : /*#__PURE__*/_jsxDEV(\"p\", {\n                children: \"No trained model available. Train a model to enable AI categorization.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 399,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 380,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 373,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"overview-card\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"card-header\",\n              children: /*#__PURE__*/_jsxDEV(\"h3\", {\n                children: \"Training Data\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 406,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 405,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"card-content\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"metric\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"metric-label\",\n                  children: \"Total Examples\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 410,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"metric-value\",\n                  children: trainingData.length\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 411,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 409,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"metric\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"metric-label\",\n                  children: \"Categories Covered\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 414,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"metric-value\",\n                  children: [new Set(trainingData.map(td => td.categoryId)).size, \" / \", categories.length]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 415,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 413,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"metric\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"metric-label\",\n                  children: \"Data Quality\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 420,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"metric-value\",\n                  children: [getDataQualityScore().toFixed(1), \"%\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 421,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 419,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 408,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 404,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"overview-card\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"card-header\",\n              children: /*#__PURE__*/_jsxDEV(\"h3\", {\n                children: \"Quick Actions\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 428,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 427,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"card-content\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"action-buttons\",\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: handleGenerateTrainingData,\n                  className: \"btn btn-secondary\",\n                  children: \"Generate Training Data\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 432,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: handleStartTraining,\n                  disabled: trainingData.length < 10 || trainingProgress.isTraining,\n                  className: \"btn btn-primary\",\n                  children: trainingProgress.isTraining ? 'Training...' : 'Train Model'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 438,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 431,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 430,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 426,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 372,\n          columnNumber: 13\n        }, this), trainingProgress.isTraining && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"training-progress-card\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"progress-header\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              children: \"Training in Progress\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 454,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: handleStopTraining,\n              className: \"btn btn-secondary btn-sm\",\n              children: \"Stop Training\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 455,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 453,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"progress-content\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"progress-metrics\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"progress-metric\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"metric-label\",\n                  children: \"Epoch\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 462,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"metric-value\",\n                  children: [trainingProgress.currentEpoch, \" / \", trainingProgress.totalEpochs]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 463,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 461,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"progress-metric\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"metric-label\",\n                  children: \"Loss\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 468,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"metric-value\",\n                  children: trainingProgress.currentLoss.toFixed(4)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 469,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 467,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"progress-metric\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"metric-label\",\n                  children: \"Accuracy\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 472,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"metric-value\",\n                  children: [(trainingProgress.currentAccuracy * 100).toFixed(1), \"%\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 473,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 471,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"progress-metric\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"metric-label\",\n                  children: \"Time Remaining\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 478,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"metric-value\",\n                  children: formatDuration(trainingProgress.estimatedTimeRemaining)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 479,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 477,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 460,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"progress-bar\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"progress-fill\",\n                style: {\n                  width: `${trainingProgress.currentEpoch / trainingProgress.totalEpochs * 100}%`\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 485,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 484,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 459,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 452,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 371,\n        columnNumber: 11\n      }, this), activeTab === 'training' && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"training-tab\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"training-controls\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"training-info\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              children: \"Model Training\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 503,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"Train a neural network model to automatically categorize transactions based on description and amount patterns.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 504,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 502,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"training-settings\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"setting-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Training Data Status\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 509,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"data-status\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"data-count\",\n                  children: [trainingData.length, \" examples\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 511,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: `data-quality ${getDataQualityScore() > 70 ? 'good' : getDataQualityScore() > 40 ? 'fair' : 'poor'}`,\n                  children: [\"Quality: \", getDataQualityScore().toFixed(0), \"%\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 512,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 510,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 508,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"setting-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Data Distribution\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 519,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"category-distribution\",\n                children: [categories.slice(0, 5).map(category => {\n                  const count = trainingData.filter(td => td.categoryId === category.id).length;\n                  const percentage = trainingData.length > 0 ? count / trainingData.length * 100 : 0;\n                  return /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"distribution-item\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"category-name\",\n                      children: category.name\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 526,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"distribution-bar\",\n                      children: /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"distribution-fill\",\n                        style: {\n                          width: `${percentage}%`,\n                          backgroundColor: category.color\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 528,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 527,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"distribution-count\",\n                      children: count\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 536,\n                      columnNumber: 27\n                    }, this)]\n                  }, category.id, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 525,\n                    columnNumber: 25\n                  }, this);\n                }), categories.length > 5 && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"distribution-more\",\n                  children: [\"+\", categories.length - 5, \" more categories\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 541,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 520,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 518,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 507,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"training-actions\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: handleGenerateTrainingData,\n              className: \"btn btn-secondary\",\n              children: \"Generate Training Data\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 550,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: handleStartTraining,\n              disabled: trainingData.length < 10 || trainingProgress.isTraining,\n              className: \"btn btn-primary\",\n              children: trainingProgress.isTraining ? 'Training in Progress...' : 'Start Training'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 557,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 549,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 501,\n          columnNumber: 13\n        }, this), trainingData.length < 10 && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"training-warning\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"warning-icon\",\n            children: \"\\u26A0\\uFE0F\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 569,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"warning-content\",\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              children: \"Insufficient Training Data\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 571,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: [\"You need at least 10 training examples to train a model. Currently you have \", trainingData.length, \" examples.\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 572,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Recommendation:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 577,\n                columnNumber: 21\n              }, this), \" Use \\\"Generate Training Data\\\" to automatically create training examples from your manually categorized transactions.\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 576,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 570,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 568,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 500,\n        columnNumber: 11\n      }, this), activeTab === 'data' && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"data-tab\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"data-controls\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"data-filters\",\n            children: [/*#__PURE__*/_jsxDEV(\"select\", {\n              value: dataFilters.categoryId,\n              onChange: e => setDataFilters(prev => ({\n                ...prev,\n                categoryId: e.target.value\n              })),\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"\",\n                children: \"All Categories\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 595,\n                columnNumber: 19\n              }, this), categories.map(category => /*#__PURE__*/_jsxDEV(\"option\", {\n                value: category.id,\n                children: category.name\n              }, category.id, false, {\n                fileName: _jsxFileName,\n                lineNumber: 597,\n                columnNumber: 21\n              }, this))]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 591,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              placeholder: \"Search descriptions...\",\n              value: dataFilters.searchTerm,\n              onChange: e => setDataFilters(prev => ({\n                ...prev,\n                searchTerm: e.target.value\n              }))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 603,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 590,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"data-actions\",\n            children: /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: handleClearTrainingData,\n              className: \"btn btn-danger btn-sm\",\n              children: \"Clear All Data\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 612,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 611,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 589,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"training-data-table-container\",\n          children: /*#__PURE__*/_jsxDEV(\"table\", {\n            className: \"training-data-table\",\n            children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n              children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                  children: \"Description\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 622,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  children: \"Amount\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 623,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  children: \"Category\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 624,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  children: \"Created\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 625,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 621,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 620,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n              children: trainingData.filter(td => {\n                if (dataFilters.categoryId && td.categoryId !== dataFilters.categoryId) return false;\n                if (dataFilters.searchTerm && !td.description.toLowerCase().includes(dataFilters.searchTerm.toLowerCase())) return false;\n                return true;\n              }).map(data => {\n                var _categories$find;\n                return /*#__PURE__*/_jsxDEV(\"tr\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                    className: \"description-cell\",\n                    children: data.description\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 637,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    className: data.amount >= 0 ? 'amount-credit' : 'amount-debit',\n                    children: [\"$\", Math.abs(data.amount).toFixed(2)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 638,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"category-badge\",\n                      style: {\n                        backgroundColor: (_categories$find = categories.find(c => c.id === data.categoryId)) === null || _categories$find === void 0 ? void 0 : _categories$find.color\n                      },\n                      children: getCategoryName(data.categoryId)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 642,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 641,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: new Date(data.createdDate).toLocaleDateString()\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 649,\n                    columnNumber: 25\n                  }, this)]\n                }, data.id, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 636,\n                  columnNumber: 23\n                }, this);\n              })\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 628,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 619,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 618,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 588,\n        columnNumber: 11\n      }, this), activeTab === 'performance' && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"performance-tab\",\n        children: activeModel && modelMetrics ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"performance-content\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"performance-overview\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"metric-card\",\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                children: \"Overall Accuracy\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 665,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"metric-value large\",\n                children: [(modelMetrics.accuracy * 100).toFixed(1), \"%\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 666,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 664,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"metric-card\",\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                children: \"Precision\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 669,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"metric-value large\",\n                children: [(modelMetrics.precision * 100).toFixed(1), \"%\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 670,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 668,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"metric-card\",\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                children: \"Recall\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 673,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"metric-value large\",\n                children: [(modelMetrics.recall * 100).toFixed(1), \"%\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 674,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 672,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"metric-card\",\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                children: \"F1 Score\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 677,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"metric-value large\",\n                children: [(modelMetrics.f1Score * 100).toFixed(1), \"%\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 678,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 676,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 663,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"category-performance\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              children: \"Performance by Category\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 683,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"category-performance-table-container\",\n              children: /*#__PURE__*/_jsxDEV(\"table\", {\n                className: \"category-performance-table\",\n                children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n                  children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                      children: \"Category\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 688,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                      children: \"Precision\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 689,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                      children: \"Recall\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 690,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                      children: \"F1 Score\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 691,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                      children: \"Support\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 692,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 687,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 686,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n                  children: modelMetrics.categoryPerformance.map(cp => {\n                    var _categories$find2;\n                    return /*#__PURE__*/_jsxDEV(\"tr\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                        children: /*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"category-badge\",\n                          style: {\n                            backgroundColor: (_categories$find2 = categories.find(c => c.id === cp.categoryId)) === null || _categories$find2 === void 0 ? void 0 : _categories$find2.color\n                          },\n                          children: cp.categoryName\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 699,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 698,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                        children: [(cp.precision * 100).toFixed(1), \"%\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 706,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                        children: [(cp.recall * 100).toFixed(1), \"%\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 707,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                        children: [(cp.f1Score * 100).toFixed(1), \"%\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 708,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                        children: cp.support\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 709,\n                        columnNumber: 29\n                      }, this)]\n                    }, cp.categoryId, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 697,\n                      columnNumber: 27\n                    }, this);\n                  })\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 695,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 685,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 684,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 682,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 662,\n          columnNumber: 15\n        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"no-performance-data\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"empty-state-icon\",\n            children: \"\\uD83D\\uDCC8\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 719,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"No Performance Data Available\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 720,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"Train a model to view performance metrics and analysis.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 721,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setActiveTab('training'),\n            className: \"btn btn-primary\",\n            children: \"Go to Training\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 722,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 718,\n          columnNumber: 15\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 660,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 368,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 331,\n    columnNumber: 5\n  }, this);\n};\n_s(MLTrainingInterface, \"Y0G/r5+94i1B6Afm3H3zGbIPJKk=\");\n_c = MLTrainingInterface;\nvar _c;\n$RefreshReg$(_c, \"MLTrainingInterface\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useCallback", "simpleMlService", "categoryService", "transactionStorageService", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "MLTrainingInterface", "onClose", "_s", "activeTab", "setActiveTab", "loading", "setLoading", "error", "setError", "activeModel", "setActiveModel", "trainingData", "setTrainingData", "categories", "setCategories", "trainingProgress", "setTrainingProgress", "isTraining", "currentEpoch", "totalEpochs", "currentLoss", "currentAccuracy", "estimatedTimeRemaining", "dataFilters", "setDataFilters", "categoryId", "confidence", "searchTerm", "modelMetrics", "setModelMetrics", "loadData", "modelStats", "getModelStats", "getTrainingData", "getAllCategories", "loadModelMetrics", "err", "Error", "message", "model", "simulatedMetrics", "accuracy", "precision", "recall", "f1Score", "confusionMatrix", "generateSimulatedConfusionMatrix", "length", "categoryPerformance", "map", "category", "id", "categoryName", "name", "Math", "random", "support", "floor", "size", "matrix", "i", "j", "handleStartTraining", "alert", "prev", "startTime", "Date", "now", "epoch", "Promise", "resolve", "setTimeout", "progress", "exp", "min", "elapsedTime", "estimatedTotal", "max", "result", "mlCategorizationService", "trainModel", "toFixed", "console", "handleStopTraining", "handleClearTrainingData", "confirmed", "window", "confirm", "clearTrainingData", "handleGenerateTrainingData", "stats", "getCategorizationStats", "manuallyCateged", "addedCount", "for<PERSON>ach", "categoryTransactions", "getTransactionsByCategory", "filter", "t", "manualCategoryId", "transaction", "amount", "creditAmount", "debitAmount", "exists", "some", "td", "description", "addTrainingData", "getCategoryName", "find", "c", "formatDuration", "ms", "seconds", "minutes", "hours", "getDataQualityScore", "categoryDistribution", "count", "totalData", "avgPerCategory", "variance", "reduce", "acc", "cat", "pow", "max<PERSON><PERSON>ce", "balanceScore", "dataAmountScore", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "width", "height", "viewBox", "fill", "stroke", "strokeWidth", "x1", "y1", "x2", "y2", "label", "icon", "tab", "trainingSize", "trainingDate", "toLocaleDateString", "Set", "disabled", "style", "slice", "percentage", "backgroundColor", "color", "value", "onChange", "e", "target", "type", "placeholder", "toLowerCase", "includes", "data", "_categories$find", "abs", "createdDate", "cp", "_categories$find2", "_c", "$RefreshReg$"], "sources": ["C:/tmsft/src/components/MLTrainingInterface.tsx"], "sourcesContent": ["import React, { useState, useEffect, useCallback } from 'react';\r\nimport { simpleMlService } from '../services/simpleMlService';\r\nimport { categoryService } from '../services/categoryService';\r\nimport { transactionStorageService } from '../services/transactionStorageService';\r\nimport { MLModel, TrainingData, TransactionCategory } from '../types';\r\nimport './MLTrainingInterface.css';\r\n\r\ninterface MLTrainingInterfaceProps {\r\n  onClose?: () => void;\r\n}\r\n\r\ninterface TrainingProgress {\r\n  isTraining: boolean;\r\n  currentEpoch: number;\r\n  totalEpochs: number;\r\n  currentLoss: number;\r\n  currentAccuracy: number;\r\n  estimatedTimeRemaining: number;\r\n}\r\n\r\ninterface ModelMetrics {\r\n  accuracy: number;\r\n  precision: number;\r\n  recall: number;\r\n  f1Score: number;\r\n  confusionMatrix: number[][];\r\n  categoryPerformance: {\r\n    categoryId: string;\r\n    categoryName: string;\r\n    precision: number;\r\n    recall: number;\r\n    f1Score: number;\r\n    support: number;\r\n  }[];\r\n}\r\n\r\nexport const MLTrainingInterface: React.FC<MLTrainingInterfaceProps> = ({ onClose }) => {\r\n  // State management\r\n  const [activeTab, setActiveTab] = useState<'overview' | 'training' | 'data' | 'performance'>('overview');\r\n  const [loading, setLoading] = useState(true);\r\n  const [error, setError] = useState<string | null>(null);\r\n  \r\n  // ML state\r\n\r\n  const [activeModel, setActiveModel] = useState<MLModel | null>(null);\r\n  const [trainingData, setTrainingData] = useState<TrainingData[]>([]);\r\n  const [categories, setCategories] = useState<TransactionCategory[]>([]);\r\n\r\n  \r\n  // Training state\r\n  const [trainingProgress, setTrainingProgress] = useState<TrainingProgress>({\r\n    isTraining: false,\r\n    currentEpoch: 0,\r\n    totalEpochs: 100,\r\n    currentLoss: 0,\r\n    currentAccuracy: 0,\r\n    estimatedTimeRemaining: 0\r\n  });\r\n  \r\n  // Data management state\r\n\r\n  const [dataFilters, setDataFilters] = useState({\r\n    categoryId: '',\r\n    confidence: 'all',\r\n    searchTerm: ''\r\n  });\r\n  \r\n  // Performance metrics state\r\n  const [modelMetrics, setModelMetrics] = useState<ModelMetrics | null>(null);\r\n\r\n  // Load data\r\n  const loadData = useCallback(async () => {\r\n    try {\r\n      setLoading(true);\r\n      setError(null);\r\n      \r\n      // Load models and stats\r\n      const modelStats = simpleMlService.getModelStats();\r\n      setActiveModel(modelStats.activeModel);\r\n      \r\n      // Load training data and categories\r\n      const trainingData = simpleMlService.getTrainingData();\r\n      const categories = categoryService.getAllCategories();\r\n      setTrainingData(trainingData);\r\n      setCategories(categories);\r\n      \r\n      // Load performance metrics if active model exists\r\n      if (modelStats.activeModel) {\r\n        await loadModelMetrics(modelStats.activeModel);\r\n      }\r\n    } catch (err) {\r\n      setError(err instanceof Error ? err.message : 'Failed to load ML data');\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  }, []);\r\n\r\n  useEffect(() => {\r\n    loadData();\r\n  }, [loadData]);\r\n\r\n  // Load model performance metrics\r\n  const loadModelMetrics = async (model: MLModel) => {\r\n    // In a real implementation, this would evaluate the model against test data\r\n    // For now, we'll simulate metrics based on the model's accuracy\r\n    const simulatedMetrics: ModelMetrics = {\r\n      accuracy: model.accuracy,\r\n      precision: model.accuracy * 0.95,\r\n      recall: model.accuracy * 0.92,\r\n      f1Score: model.accuracy * 0.93,\r\n      confusionMatrix: generateSimulatedConfusionMatrix(categories.length),\r\n      categoryPerformance: categories.map(category => ({\r\n        categoryId: category.id,\r\n        categoryName: category.name,\r\n        precision: model.accuracy * (0.8 + Math.random() * 0.2),\r\n        recall: model.accuracy * (0.8 + Math.random() * 0.2),\r\n        f1Score: model.accuracy * (0.8 + Math.random() * 0.2),\r\n        support: Math.floor(Math.random() * 50) + 10\r\n      }))\r\n    };\r\n    \r\n    setModelMetrics(simulatedMetrics);\r\n  };\r\n\r\n  // Generate simulated confusion matrix\r\n  const generateSimulatedConfusionMatrix = (size: number): number[][] => {\r\n    const matrix: number[][] = [];\r\n    for (let i = 0; i < size; i++) {\r\n      matrix[i] = [];\r\n      for (let j = 0; j < size; j++) {\r\n        if (i === j) {\r\n          // Diagonal (correct predictions) - higher values\r\n          matrix[i][j] = Math.floor(Math.random() * 50) + 20;\r\n        } else {\r\n          // Off-diagonal (incorrect predictions) - lower values\r\n          matrix[i][j] = Math.floor(Math.random() * 10);\r\n        }\r\n      }\r\n    }\r\n    return matrix;\r\n  };\r\n\r\n  // Training functions\r\n  const handleStartTraining = async () => {\r\n    if (trainingData.length < 10) {\r\n      alert('Need at least 10 training examples to train a model.');\r\n      return;\r\n    }\r\n\r\n    setTrainingProgress(prev => ({\r\n      ...prev,\r\n      isTraining: true,\r\n      currentEpoch: 0,\r\n      currentLoss: 1.0,\r\n      currentAccuracy: 0.1\r\n    }));\r\n\r\n    try {\r\n      // Simulate training progress\r\n      const startTime = Date.now();\r\n      const totalEpochs = 100;\r\n      \r\n      for (let epoch = 1; epoch <= totalEpochs; epoch++) {\r\n        // Simulate epoch delay\r\n        await new Promise(resolve => setTimeout(resolve, 100));\r\n        \r\n        const progress = epoch / totalEpochs;\r\n        const currentLoss = 1.0 * Math.exp(-progress * 3) + Math.random() * 0.1;\r\n        const currentAccuracy = Math.min(0.95, 0.1 + progress * 0.8 + Math.random() * 0.1);\r\n        const elapsedTime = Date.now() - startTime;\r\n        const estimatedTotal = elapsedTime / progress;\r\n        const estimatedTimeRemaining = Math.max(0, estimatedTotal - elapsedTime);\r\n        \r\n        setTrainingProgress({\r\n          isTraining: true,\r\n          currentEpoch: epoch,\r\n          totalEpochs,\r\n          currentLoss,\r\n          currentAccuracy,\r\n          estimatedTimeRemaining\r\n        });\r\n        \r\n        // Break if user closed or component unmounted\r\n        if (!trainingProgress.isTraining) break;\r\n      }\r\n\r\n      // Actually train the model\r\n      const result = await mlCategorizationService.trainModel(trainingData);\r\n      \r\n      // Update state\r\n      await loadData();\r\n      \r\n      alert(`Training completed! Final accuracy: ${(result.accuracy * 100).toFixed(1)}%`);\r\n    } catch (error) {\r\n      console.error('Training failed:', error);\r\n      alert('Training failed. Please check the console for details.');\r\n    } finally {\r\n      setTrainingProgress(prev => ({\r\n        ...prev,\r\n        isTraining: false\r\n      }));\r\n    }\r\n  };\r\n\r\n  const handleStopTraining = () => {\r\n    setTrainingProgress(prev => ({\r\n      ...prev,\r\n      isTraining: false\r\n    }));\r\n  };\r\n\r\n  const handleClearTrainingData = async () => {\r\n    const confirmed = window.confirm('Are you sure you want to clear all training data? This cannot be undone.');\r\n    if (confirmed) {\r\n      mlCategorizationService.clearTrainingData();\r\n      await loadData();\r\n    }\r\n  };\r\n\r\n  const handleGenerateTrainingData = async () => {\r\n    // Generate training data from manually categorized transactions\r\n    const stats = transactionStorageService.getCategorizationStats();\r\n    if (stats.manuallyCateged === 0) {\r\n      alert('No manually categorized transactions found. Please categorize some transactions first.');\r\n      return;\r\n    }\r\n\r\n    let addedCount = 0;\r\n    \r\n    // Get all transactions and find manually categorized ones\r\n    categories.forEach(category => {\r\n      const categoryTransactions = transactionStorageService.getTransactionsByCategory(category.id);\r\n      const manuallyCateged = categoryTransactions.filter(t => t.manualCategoryId);\r\n      \r\n      manuallyCateged.forEach(transaction => {\r\n        const amount = (transaction.creditAmount || 0) - (transaction.debitAmount || 0);\r\n        \r\n        // Check if this data already exists\r\n        const exists = trainingData.some(td => \r\n          td.description === transaction.description && \r\n          td.amount === amount && \r\n          td.categoryId === category.id\r\n        );\r\n        \r\n        if (!exists) {\r\n          mlCategorizationService.addTrainingData(\r\n            transaction.description,\r\n            amount,\r\n            category.id\r\n          );\r\n          addedCount++;\r\n        }\r\n      });\r\n    });\r\n\r\n    if (addedCount > 0) {\r\n      await loadData();\r\n      alert(`Added ${addedCount} new training examples from manually categorized transactions.`);\r\n    } else {\r\n      alert('No new training data could be generated. All manually categorized transactions are already in the training set.');\r\n    }\r\n  };\r\n\r\n  // Data management functions\r\n\r\n\r\n  // Helper functions\r\n  const getCategoryName = (categoryId: string): string => {\r\n    const category = categories.find(c => c.id === categoryId);\r\n    return category?.name || 'Unknown Category';\r\n  };\r\n\r\n  const formatDuration = (ms: number): string => {\r\n    const seconds = Math.floor(ms / 1000);\r\n    const minutes = Math.floor(seconds / 60);\r\n    const hours = Math.floor(minutes / 60);\r\n    \r\n    if (hours > 0) return `${hours}h ${minutes % 60}m`;\r\n    if (minutes > 0) return `${minutes}m ${seconds % 60}s`;\r\n    return `${seconds}s`;\r\n  };\r\n\r\n  const getDataQualityScore = (): number => {\r\n    if (trainingData.length === 0) return 0;\r\n    \r\n    // Calculate based on data distribution across categories\r\n    const categoryDistribution = categories.map(category => ({\r\n      categoryId: category.id,\r\n      count: trainingData.filter(td => td.categoryId === category.id).length\r\n    }));\r\n    \r\n    const totalData = trainingData.length;\r\n    const avgPerCategory = totalData / categories.length;\r\n    const variance = categoryDistribution.reduce((acc, cat) => {\r\n      return acc + Math.pow(cat.count - avgPerCategory, 2);\r\n    }, 0) / categories.length;\r\n    \r\n    // Score based on balance (lower variance = better balance = higher score)\r\n    const maxVariance = Math.pow(avgPerCategory, 2);\r\n    const balanceScore = Math.max(0, 1 - (variance / maxVariance));\r\n    \r\n    // Score based on total amount of data\r\n    const dataAmountScore = Math.min(1, totalData / 100); // 100 examples = perfect score\r\n    \r\n    return (balanceScore * 0.6 + dataAmountScore * 0.4) * 100;\r\n  };\r\n\r\n  if (loading) {\r\n    return (\r\n      <div className=\"ml-training-loading\">\r\n        <div className=\"loading-spinner\"></div>\r\n        <p>Loading ML training interface...</p>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  if (error) {\r\n    return (\r\n      <div className=\"ml-training-error\">\r\n        <div className=\"error-icon\">⚠️</div>\r\n        <h3>Error Loading ML Interface</h3>\r\n        <p>{error}</p>\r\n        <button onClick={loadData} className=\"btn btn-primary\">\r\n          Try Again\r\n        </button>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className=\"ml-training-interface\">\r\n      {/* Header */}\r\n      <div className=\"ml-training-header\">\r\n        <div className=\"header-title\">\r\n          <h2>Machine Learning Training Center</h2>\r\n          <p>Train and manage AI models for transaction categorization</p>\r\n        </div>\r\n        {onClose && (\r\n          <button onClick={onClose} className=\"close-btn\">\r\n            <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\">\r\n              <line x1=\"18\" y1=\"6\" x2=\"6\" y2=\"18\"></line>\r\n              <line x1=\"6\" y1=\"6\" x2=\"18\" y2=\"18\"></line>\r\n            </svg>\r\n          </button>\r\n        )}\r\n      </div>\r\n\r\n      {/* Tab Navigation */}\r\n      <div className=\"ml-training-tabs\">\r\n        {[\r\n          { id: 'overview', label: 'Overview', icon: '📊' },\r\n          { id: 'training', label: 'Training', icon: '🧠' },\r\n          { id: 'data', label: 'Training Data', icon: '📁' },\r\n          { id: 'performance', label: 'Performance', icon: '📈' }\r\n        ].map(tab => (\r\n          <button\r\n            key={tab.id}\r\n            className={`tab-btn ${activeTab === tab.id ? 'active' : ''}`}\r\n            onClick={() => setActiveTab(tab.id as any)}\r\n          >\r\n            <span className=\"tab-icon\">{tab.icon}</span>\r\n            <span className=\"tab-label\">{tab.label}</span>\r\n          </button>\r\n        ))}\r\n      </div>\r\n\r\n      {/* Tab Content */}\r\n      <div className=\"ml-training-content\">\r\n        {/* Overview Tab */}\r\n        {activeTab === 'overview' && (\r\n          <div className=\"overview-tab\">\r\n            <div className=\"overview-cards\">\r\n              <div className=\"overview-card\">\r\n                <div className=\"card-header\">\r\n                  <h3>Model Status</h3>\r\n                  <span className={`status-badge ${activeModel ? 'active' : 'inactive'}`}>\r\n                    {activeModel ? 'Active' : 'No Model'}\r\n                  </span>\r\n                </div>\r\n                <div className=\"card-content\">\r\n                  {activeModel ? (\r\n                    <>\r\n                      <div className=\"metric\">\r\n                        <span className=\"metric-label\">Accuracy</span>\r\n                        <span className=\"metric-value\">{(activeModel.accuracy * 100).toFixed(1)}%</span>\r\n                      </div>\r\n                      <div className=\"metric\">\r\n                        <span className=\"metric-label\">Training Size</span>\r\n                        <span className=\"metric-value\">{activeModel.trainingSize}</span>\r\n                      </div>\r\n                      <div className=\"metric\">\r\n                        <span className=\"metric-label\">Trained</span>\r\n                        <span className=\"metric-value\">\r\n                          {new Date(activeModel.trainingDate).toLocaleDateString()}\r\n                        </span>\r\n                      </div>\r\n                    </>\r\n                  ) : (\r\n                    <p>No trained model available. Train a model to enable AI categorization.</p>\r\n                  )}\r\n                </div>\r\n              </div>\r\n\r\n              <div className=\"overview-card\">\r\n                <div className=\"card-header\">\r\n                  <h3>Training Data</h3>\r\n                </div>\r\n                <div className=\"card-content\">\r\n                  <div className=\"metric\">\r\n                    <span className=\"metric-label\">Total Examples</span>\r\n                    <span className=\"metric-value\">{trainingData.length}</span>\r\n                  </div>\r\n                  <div className=\"metric\">\r\n                    <span className=\"metric-label\">Categories Covered</span>\r\n                    <span className=\"metric-value\">\r\n                      {new Set(trainingData.map(td => td.categoryId)).size} / {categories.length}\r\n                    </span>\r\n                  </div>\r\n                  <div className=\"metric\">\r\n                    <span className=\"metric-label\">Data Quality</span>\r\n                    <span className=\"metric-value\">{getDataQualityScore().toFixed(1)}%</span>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n\r\n              <div className=\"overview-card\">\r\n                <div className=\"card-header\">\r\n                  <h3>Quick Actions</h3>\r\n                </div>\r\n                <div className=\"card-content\">\r\n                  <div className=\"action-buttons\">\r\n                    <button \r\n                      onClick={handleGenerateTrainingData}\r\n                      className=\"btn btn-secondary\"\r\n                    >\r\n                      Generate Training Data\r\n                    </button>\r\n                    <button \r\n                      onClick={handleStartTraining}\r\n                      disabled={trainingData.length < 10 || trainingProgress.isTraining}\r\n                      className=\"btn btn-primary\"\r\n                    >\r\n                      {trainingProgress.isTraining ? 'Training...' : 'Train Model'}\r\n                    </button>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            {/* Training Progress */}\r\n            {trainingProgress.isTraining && (\r\n              <div className=\"training-progress-card\">\r\n                <div className=\"progress-header\">\r\n                  <h3>Training in Progress</h3>\r\n                  <button onClick={handleStopTraining} className=\"btn btn-secondary btn-sm\">\r\n                    Stop Training\r\n                  </button>\r\n                </div>\r\n                <div className=\"progress-content\">\r\n                  <div className=\"progress-metrics\">\r\n                    <div className=\"progress-metric\">\r\n                      <span className=\"metric-label\">Epoch</span>\r\n                      <span className=\"metric-value\">\r\n                        {trainingProgress.currentEpoch} / {trainingProgress.totalEpochs}\r\n                      </span>\r\n                    </div>\r\n                    <div className=\"progress-metric\">\r\n                      <span className=\"metric-label\">Loss</span>\r\n                      <span className=\"metric-value\">{trainingProgress.currentLoss.toFixed(4)}</span>\r\n                    </div>\r\n                    <div className=\"progress-metric\">\r\n                      <span className=\"metric-label\">Accuracy</span>\r\n                      <span className=\"metric-value\">\r\n                        {(trainingProgress.currentAccuracy * 100).toFixed(1)}%\r\n                      </span>\r\n                    </div>\r\n                    <div className=\"progress-metric\">\r\n                      <span className=\"metric-label\">Time Remaining</span>\r\n                      <span className=\"metric-value\">\r\n                        {formatDuration(trainingProgress.estimatedTimeRemaining)}\r\n                      </span>\r\n                    </div>\r\n                  </div>\r\n                  <div className=\"progress-bar\">\r\n                    <div \r\n                      className=\"progress-fill\"\r\n                      style={{ \r\n                        width: `${(trainingProgress.currentEpoch / trainingProgress.totalEpochs) * 100}%` \r\n                      }}\r\n                    ></div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            )}\r\n          </div>\r\n        )}\r\n\r\n        {/* Training Tab */}\r\n        {activeTab === 'training' && (\r\n          <div className=\"training-tab\">\r\n            <div className=\"training-controls\">\r\n              <div className=\"training-info\">\r\n                <h3>Model Training</h3>\r\n                <p>Train a neural network model to automatically categorize transactions based on description and amount patterns.</p>\r\n              </div>\r\n\r\n              <div className=\"training-settings\">\r\n                <div className=\"setting-group\">\r\n                  <label>Training Data Status</label>\r\n                  <div className=\"data-status\">\r\n                    <span className=\"data-count\">{trainingData.length} examples</span>\r\n                    <span className={`data-quality ${getDataQualityScore() > 70 ? 'good' : getDataQualityScore() > 40 ? 'fair' : 'poor'}`}>\r\n                      Quality: {getDataQualityScore().toFixed(0)}%\r\n                    </span>\r\n                  </div>\r\n                </div>\r\n\r\n                <div className=\"setting-group\">\r\n                  <label>Data Distribution</label>\r\n                  <div className=\"category-distribution\">\r\n                    {categories.slice(0, 5).map(category => {\r\n                      const count = trainingData.filter(td => td.categoryId === category.id).length;\r\n                      const percentage = trainingData.length > 0 ? (count / trainingData.length) * 100 : 0;\r\n                      return (\r\n                        <div key={category.id} className=\"distribution-item\">\r\n                          <span className=\"category-name\">{category.name}</span>\r\n                          <div className=\"distribution-bar\">\r\n                            <div \r\n                              className=\"distribution-fill\" \r\n                              style={{ \r\n                                width: `${percentage}%`,\r\n                                backgroundColor: category.color \r\n                              }}\r\n                            ></div>\r\n                          </div>\r\n                          <span className=\"distribution-count\">{count}</span>\r\n                        </div>\r\n                      );\r\n                    })}\r\n                    {categories.length > 5 && (\r\n                      <div className=\"distribution-more\">\r\n                        +{categories.length - 5} more categories\r\n                      </div>\r\n                    )}\r\n                  </div>\r\n                </div>\r\n              </div>\r\n\r\n              <div className=\"training-actions\">\r\n                <button \r\n                  onClick={handleGenerateTrainingData}\r\n                  className=\"btn btn-secondary\"\r\n                >\r\n                  Generate Training Data\r\n                </button>\r\n\r\n                <button \r\n                  onClick={handleStartTraining}\r\n                  disabled={trainingData.length < 10 || trainingProgress.isTraining}\r\n                  className=\"btn btn-primary\"\r\n                >\r\n                  {trainingProgress.isTraining ? 'Training in Progress...' : 'Start Training'}\r\n                </button>\r\n              </div>\r\n            </div>\r\n\r\n            {trainingData.length < 10 && (\r\n              <div className=\"training-warning\">\r\n                <div className=\"warning-icon\">⚠️</div>\r\n                <div className=\"warning-content\">\r\n                  <h4>Insufficient Training Data</h4>\r\n                  <p>\r\n                    You need at least 10 training examples to train a model. \r\n                    Currently you have {trainingData.length} examples.\r\n                  </p>\r\n                  <p>\r\n                    <strong>Recommendation:</strong> Use \"Generate Training Data\" to automatically \r\n                    create training examples from your manually categorized transactions.\r\n                  </p>\r\n                </div>\r\n              </div>\r\n            )}\r\n          </div>\r\n        )}\r\n\r\n        {/* Training Data Tab */}\r\n        {activeTab === 'data' && (\r\n          <div className=\"data-tab\">\r\n            <div className=\"data-controls\">\r\n              <div className=\"data-filters\">\r\n                <select\r\n                  value={dataFilters.categoryId}\r\n                  onChange={(e) => setDataFilters(prev => ({ ...prev, categoryId: e.target.value }))}\r\n                >\r\n                  <option value=\"\">All Categories</option>\r\n                  {categories.map(category => (\r\n                    <option key={category.id} value={category.id}>\r\n                      {category.name}\r\n                    </option>\r\n                  ))}\r\n                </select>\r\n\r\n                <input\r\n                  type=\"text\"\r\n                  placeholder=\"Search descriptions...\"\r\n                  value={dataFilters.searchTerm}\r\n                  onChange={(e) => setDataFilters(prev => ({ ...prev, searchTerm: e.target.value }))}\r\n                />\r\n              </div>\r\n\r\n              <div className=\"data-actions\">\r\n                <button onClick={handleClearTrainingData} className=\"btn btn-danger btn-sm\">\r\n                  Clear All Data\r\n                </button>\r\n              </div>\r\n            </div>\r\n\r\n            <div className=\"training-data-table-container\">\r\n              <table className=\"training-data-table\">\r\n                <thead>\r\n                  <tr>\r\n                    <th>Description</th>\r\n                    <th>Amount</th>\r\n                    <th>Category</th>\r\n                    <th>Created</th>\r\n                  </tr>\r\n                </thead>\r\n                <tbody>\r\n                  {trainingData\r\n                    .filter(td => {\r\n                      if (dataFilters.categoryId && td.categoryId !== dataFilters.categoryId) return false;\r\n                      if (dataFilters.searchTerm && !td.description.toLowerCase().includes(dataFilters.searchTerm.toLowerCase())) return false;\r\n                      return true;\r\n                    })\r\n                    .map(data => (\r\n                      <tr key={data.id}>\r\n                        <td className=\"description-cell\">{data.description}</td>\r\n                        <td className={data.amount >= 0 ? 'amount-credit' : 'amount-debit'}>\r\n                          ${Math.abs(data.amount).toFixed(2)}\r\n                        </td>\r\n                        <td>\r\n                          <span \r\n                            className=\"category-badge\"\r\n                            style={{ backgroundColor: categories.find(c => c.id === data.categoryId)?.color }}\r\n                          >\r\n                            {getCategoryName(data.categoryId)}\r\n                          </span>\r\n                        </td>\r\n                        <td>{new Date(data.createdDate).toLocaleDateString()}</td>\r\n                      </tr>\r\n                    ))}\r\n                </tbody>\r\n              </table>\r\n            </div>\r\n          </div>\r\n        )}\r\n\r\n        {/* Performance Tab */}\r\n        {activeTab === 'performance' && (\r\n          <div className=\"performance-tab\">\r\n            {activeModel && modelMetrics ? (\r\n              <div className=\"performance-content\">\r\n                <div className=\"performance-overview\">\r\n                  <div className=\"metric-card\">\r\n                    <h4>Overall Accuracy</h4>\r\n                    <div className=\"metric-value large\">{(modelMetrics.accuracy * 100).toFixed(1)}%</div>\r\n                  </div>\r\n                  <div className=\"metric-card\">\r\n                    <h4>Precision</h4>\r\n                    <div className=\"metric-value large\">{(modelMetrics.precision * 100).toFixed(1)}%</div>\r\n                  </div>\r\n                  <div className=\"metric-card\">\r\n                    <h4>Recall</h4>\r\n                    <div className=\"metric-value large\">{(modelMetrics.recall * 100).toFixed(1)}%</div>\r\n                  </div>\r\n                  <div className=\"metric-card\">\r\n                    <h4>F1 Score</h4>\r\n                    <div className=\"metric-value large\">{(modelMetrics.f1Score * 100).toFixed(1)}%</div>\r\n                  </div>\r\n                </div>\r\n\r\n                <div className=\"category-performance\">\r\n                  <h3>Performance by Category</h3>\r\n                  <div className=\"category-performance-table-container\">\r\n                    <table className=\"category-performance-table\">\r\n                      <thead>\r\n                        <tr>\r\n                          <th>Category</th>\r\n                          <th>Precision</th>\r\n                          <th>Recall</th>\r\n                          <th>F1 Score</th>\r\n                          <th>Support</th>\r\n                        </tr>\r\n                      </thead>\r\n                      <tbody>\r\n                        {modelMetrics.categoryPerformance.map(cp => (\r\n                          <tr key={cp.categoryId}>\r\n                            <td>\r\n                              <span \r\n                                className=\"category-badge\"\r\n                                style={{ backgroundColor: categories.find(c => c.id === cp.categoryId)?.color }}\r\n                              >\r\n                                {cp.categoryName}\r\n                              </span>\r\n                            </td>\r\n                            <td>{(cp.precision * 100).toFixed(1)}%</td>\r\n                            <td>{(cp.recall * 100).toFixed(1)}%</td>\r\n                            <td>{(cp.f1Score * 100).toFixed(1)}%</td>\r\n                            <td>{cp.support}</td>\r\n                          </tr>\r\n                        ))}\r\n                      </tbody>\r\n                    </table>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            ) : (\r\n              <div className=\"no-performance-data\">\r\n                <div className=\"empty-state-icon\">📈</div>\r\n                <h3>No Performance Data Available</h3>\r\n                <p>Train a model to view performance metrics and analysis.</p>\r\n                <button \r\n                  onClick={() => setActiveTab('training')}\r\n                  className=\"btn btn-primary\"\r\n                >\r\n                  Go to Training\r\n                </button>\r\n              </div>\r\n            )}\r\n          </div>\r\n        )}\r\n      </div>\r\n    </div>\r\n  );\r\n}; "], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,QAAQ,OAAO;AAC/D,SAASC,eAAe,QAAQ,6BAA6B;AAC7D,SAASC,eAAe,QAAQ,6BAA6B;AAC7D,SAASC,yBAAyB,QAAQ,uCAAuC;AAEjF,OAAO,2BAA2B;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AA+BnC,OAAO,MAAMC,mBAAuD,GAAGA,CAAC;EAAEC;AAAQ,CAAC,KAAK;EAAAC,EAAA;EACtF;EACA,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGd,QAAQ,CAAmD,UAAU,CAAC;EACxG,MAAM,CAACe,OAAO,EAAEC,UAAU,CAAC,GAAGhB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACiB,KAAK,EAAEC,QAAQ,CAAC,GAAGlB,QAAQ,CAAgB,IAAI,CAAC;;EAEvD;;EAEA,MAAM,CAACmB,WAAW,EAAEC,cAAc,CAAC,GAAGpB,QAAQ,CAAiB,IAAI,CAAC;EACpE,MAAM,CAACqB,YAAY,EAAEC,eAAe,CAAC,GAAGtB,QAAQ,CAAiB,EAAE,CAAC;EACpE,MAAM,CAACuB,UAAU,EAAEC,aAAa,CAAC,GAAGxB,QAAQ,CAAwB,EAAE,CAAC;;EAGvE;EACA,MAAM,CAACyB,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG1B,QAAQ,CAAmB;IACzE2B,UAAU,EAAE,KAAK;IACjBC,YAAY,EAAE,CAAC;IACfC,WAAW,EAAE,GAAG;IAChBC,WAAW,EAAE,CAAC;IACdC,eAAe,EAAE,CAAC;IAClBC,sBAAsB,EAAE;EAC1B,CAAC,CAAC;;EAEF;;EAEA,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGlC,QAAQ,CAAC;IAC7CmC,UAAU,EAAE,EAAE;IACdC,UAAU,EAAE,KAAK;IACjBC,UAAU,EAAE;EACd,CAAC,CAAC;;EAEF;EACA,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGvC,QAAQ,CAAsB,IAAI,CAAC;;EAE3E;EACA,MAAMwC,QAAQ,GAAGtC,WAAW,CAAC,YAAY;IACvC,IAAI;MACFc,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAAC,IAAI,CAAC;;MAEd;MACA,MAAMuB,UAAU,GAAGtC,eAAe,CAACuC,aAAa,CAAC,CAAC;MAClDtB,cAAc,CAACqB,UAAU,CAACtB,WAAW,CAAC;;MAEtC;MACA,MAAME,YAAY,GAAGlB,eAAe,CAACwC,eAAe,CAAC,CAAC;MACtD,MAAMpB,UAAU,GAAGnB,eAAe,CAACwC,gBAAgB,CAAC,CAAC;MACrDtB,eAAe,CAACD,YAAY,CAAC;MAC7BG,aAAa,CAACD,UAAU,CAAC;;MAEzB;MACA,IAAIkB,UAAU,CAACtB,WAAW,EAAE;QAC1B,MAAM0B,gBAAgB,CAACJ,UAAU,CAACtB,WAAW,CAAC;MAChD;IACF,CAAC,CAAC,OAAO2B,GAAG,EAAE;MACZ5B,QAAQ,CAAC4B,GAAG,YAAYC,KAAK,GAAGD,GAAG,CAACE,OAAO,GAAG,wBAAwB,CAAC;IACzE,CAAC,SAAS;MACRhC,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC,EAAE,EAAE,CAAC;EAENf,SAAS,CAAC,MAAM;IACduC,QAAQ,CAAC,CAAC;EACZ,CAAC,EAAE,CAACA,QAAQ,CAAC,CAAC;;EAEd;EACA,MAAMK,gBAAgB,GAAG,MAAOI,KAAc,IAAK;IACjD;IACA;IACA,MAAMC,gBAA8B,GAAG;MACrCC,QAAQ,EAAEF,KAAK,CAACE,QAAQ;MACxBC,SAAS,EAAEH,KAAK,CAACE,QAAQ,GAAG,IAAI;MAChCE,MAAM,EAAEJ,KAAK,CAACE,QAAQ,GAAG,IAAI;MAC7BG,OAAO,EAAEL,KAAK,CAACE,QAAQ,GAAG,IAAI;MAC9BI,eAAe,EAAEC,gCAAgC,CAACjC,UAAU,CAACkC,MAAM,CAAC;MACpEC,mBAAmB,EAAEnC,UAAU,CAACoC,GAAG,CAACC,QAAQ,KAAK;QAC/CzB,UAAU,EAAEyB,QAAQ,CAACC,EAAE;QACvBC,YAAY,EAAEF,QAAQ,CAACG,IAAI;QAC3BX,SAAS,EAAEH,KAAK,CAACE,QAAQ,IAAI,GAAG,GAAGa,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG,CAAC;QACvDZ,MAAM,EAAEJ,KAAK,CAACE,QAAQ,IAAI,GAAG,GAAGa,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG,CAAC;QACpDX,OAAO,EAAEL,KAAK,CAACE,QAAQ,IAAI,GAAG,GAAGa,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG,CAAC;QACrDC,OAAO,EAAEF,IAAI,CAACG,KAAK,CAACH,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG;MAC5C,CAAC,CAAC;IACJ,CAAC;IAED1B,eAAe,CAACW,gBAAgB,CAAC;EACnC,CAAC;;EAED;EACA,MAAMM,gCAAgC,GAAIY,IAAY,IAAiB;IACrE,MAAMC,MAAkB,GAAG,EAAE;IAC7B,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,IAAI,EAAEE,CAAC,EAAE,EAAE;MAC7BD,MAAM,CAACC,CAAC,CAAC,GAAG,EAAE;MACd,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,IAAI,EAAEG,CAAC,EAAE,EAAE;QAC7B,IAAID,CAAC,KAAKC,CAAC,EAAE;UACX;UACAF,MAAM,CAACC,CAAC,CAAC,CAACC,CAAC,CAAC,GAAGP,IAAI,CAACG,KAAK,CAACH,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE;QACpD,CAAC,MAAM;UACL;UACAI,MAAM,CAACC,CAAC,CAAC,CAACC,CAAC,CAAC,GAAGP,IAAI,CAACG,KAAK,CAACH,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC;QAC/C;MACF;IACF;IACA,OAAOI,MAAM;EACf,CAAC;;EAED;EACA,MAAMG,mBAAmB,GAAG,MAAAA,CAAA,KAAY;IACtC,IAAInD,YAAY,CAACoC,MAAM,GAAG,EAAE,EAAE;MAC5BgB,KAAK,CAAC,sDAAsD,CAAC;MAC7D;IACF;IAEA/C,mBAAmB,CAACgD,IAAI,KAAK;MAC3B,GAAGA,IAAI;MACP/C,UAAU,EAAE,IAAI;MAChBC,YAAY,EAAE,CAAC;MACfE,WAAW,EAAE,GAAG;MAChBC,eAAe,EAAE;IACnB,CAAC,CAAC,CAAC;IAEH,IAAI;MACF;MACA,MAAM4C,SAAS,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC;MAC5B,MAAMhD,WAAW,GAAG,GAAG;MAEvB,KAAK,IAAIiD,KAAK,GAAG,CAAC,EAAEA,KAAK,IAAIjD,WAAW,EAAEiD,KAAK,EAAE,EAAE;QACjD;QACA,MAAM,IAAIC,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,GAAG,CAAC,CAAC;QAEtD,MAAME,QAAQ,GAAGJ,KAAK,GAAGjD,WAAW;QACpC,MAAMC,WAAW,GAAG,GAAG,GAAGkC,IAAI,CAACmB,GAAG,CAAC,CAACD,QAAQ,GAAG,CAAC,CAAC,GAAGlB,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG;QACvE,MAAMlC,eAAe,GAAGiC,IAAI,CAACoB,GAAG,CAAC,IAAI,EAAE,GAAG,GAAGF,QAAQ,GAAG,GAAG,GAAGlB,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG,CAAC;QAClF,MAAMoB,WAAW,GAAGT,IAAI,CAACC,GAAG,CAAC,CAAC,GAAGF,SAAS;QAC1C,MAAMW,cAAc,GAAGD,WAAW,GAAGH,QAAQ;QAC7C,MAAMlD,sBAAsB,GAAGgC,IAAI,CAACuB,GAAG,CAAC,CAAC,EAAED,cAAc,GAAGD,WAAW,CAAC;QAExE3D,mBAAmB,CAAC;UAClBC,UAAU,EAAE,IAAI;UAChBC,YAAY,EAAEkD,KAAK;UACnBjD,WAAW;UACXC,WAAW;UACXC,eAAe;UACfC;QACF,CAAC,CAAC;;QAEF;QACA,IAAI,CAACP,gBAAgB,CAACE,UAAU,EAAE;MACpC;;MAEA;MACA,MAAM6D,MAAM,GAAG,MAAMC,uBAAuB,CAACC,UAAU,CAACrE,YAAY,CAAC;;MAErE;MACA,MAAMmB,QAAQ,CAAC,CAAC;MAEhBiC,KAAK,CAAC,uCAAuC,CAACe,MAAM,CAACrC,QAAQ,GAAG,GAAG,EAAEwC,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC;IACrF,CAAC,CAAC,OAAO1E,KAAK,EAAE;MACd2E,OAAO,CAAC3E,KAAK,CAAC,kBAAkB,EAAEA,KAAK,CAAC;MACxCwD,KAAK,CAAC,wDAAwD,CAAC;IACjE,CAAC,SAAS;MACR/C,mBAAmB,CAACgD,IAAI,KAAK;QAC3B,GAAGA,IAAI;QACP/C,UAAU,EAAE;MACd,CAAC,CAAC,CAAC;IACL;EACF,CAAC;EAED,MAAMkE,kBAAkB,GAAGA,CAAA,KAAM;IAC/BnE,mBAAmB,CAACgD,IAAI,KAAK;MAC3B,GAAGA,IAAI;MACP/C,UAAU,EAAE;IACd,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAMmE,uBAAuB,GAAG,MAAAA,CAAA,KAAY;IAC1C,MAAMC,SAAS,GAAGC,MAAM,CAACC,OAAO,CAAC,0EAA0E,CAAC;IAC5G,IAAIF,SAAS,EAAE;MACbN,uBAAuB,CAACS,iBAAiB,CAAC,CAAC;MAC3C,MAAM1D,QAAQ,CAAC,CAAC;IAClB;EACF,CAAC;EAED,MAAM2D,0BAA0B,GAAG,MAAAA,CAAA,KAAY;IAC7C;IACA,MAAMC,KAAK,GAAG/F,yBAAyB,CAACgG,sBAAsB,CAAC,CAAC;IAChE,IAAID,KAAK,CAACE,eAAe,KAAK,CAAC,EAAE;MAC/B7B,KAAK,CAAC,wFAAwF,CAAC;MAC/F;IACF;IAEA,IAAI8B,UAAU,GAAG,CAAC;;IAElB;IACAhF,UAAU,CAACiF,OAAO,CAAC5C,QAAQ,IAAI;MAC7B,MAAM6C,oBAAoB,GAAGpG,yBAAyB,CAACqG,yBAAyB,CAAC9C,QAAQ,CAACC,EAAE,CAAC;MAC7F,MAAMyC,eAAe,GAAGG,oBAAoB,CAACE,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACC,gBAAgB,CAAC;MAE5EP,eAAe,CAACE,OAAO,CAACM,WAAW,IAAI;QACrC,MAAMC,MAAM,GAAG,CAACD,WAAW,CAACE,YAAY,IAAI,CAAC,KAAKF,WAAW,CAACG,WAAW,IAAI,CAAC,CAAC;;QAE/E;QACA,MAAMC,MAAM,GAAG7F,YAAY,CAAC8F,IAAI,CAACC,EAAE,IACjCA,EAAE,CAACC,WAAW,KAAKP,WAAW,CAACO,WAAW,IAC1CD,EAAE,CAACL,MAAM,KAAKA,MAAM,IACpBK,EAAE,CAACjF,UAAU,KAAKyB,QAAQ,CAACC,EAC7B,CAAC;QAED,IAAI,CAACqD,MAAM,EAAE;UACXzB,uBAAuB,CAAC6B,eAAe,CACrCR,WAAW,CAACO,WAAW,EACvBN,MAAM,EACNnD,QAAQ,CAACC,EACX,CAAC;UACD0C,UAAU,EAAE;QACd;MACF,CAAC,CAAC;IACJ,CAAC,CAAC;IAEF,IAAIA,UAAU,GAAG,CAAC,EAAE;MAClB,MAAM/D,QAAQ,CAAC,CAAC;MAChBiC,KAAK,CAAC,SAAS8B,UAAU,gEAAgE,CAAC;IAC5F,CAAC,MAAM;MACL9B,KAAK,CAAC,iHAAiH,CAAC;IAC1H;EACF,CAAC;;EAED;;EAGA;EACA,MAAM8C,eAAe,GAAIpF,UAAkB,IAAa;IACtD,MAAMyB,QAAQ,GAAGrC,UAAU,CAACiG,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAC5D,EAAE,KAAK1B,UAAU,CAAC;IAC1D,OAAO,CAAAyB,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEG,IAAI,KAAI,kBAAkB;EAC7C,CAAC;EAED,MAAM2D,cAAc,GAAIC,EAAU,IAAa;IAC7C,MAAMC,OAAO,GAAG5D,IAAI,CAACG,KAAK,CAACwD,EAAE,GAAG,IAAI,CAAC;IACrC,MAAME,OAAO,GAAG7D,IAAI,CAACG,KAAK,CAACyD,OAAO,GAAG,EAAE,CAAC;IACxC,MAAME,KAAK,GAAG9D,IAAI,CAACG,KAAK,CAAC0D,OAAO,GAAG,EAAE,CAAC;IAEtC,IAAIC,KAAK,GAAG,CAAC,EAAE,OAAO,GAAGA,KAAK,KAAKD,OAAO,GAAG,EAAE,GAAG;IAClD,IAAIA,OAAO,GAAG,CAAC,EAAE,OAAO,GAAGA,OAAO,KAAKD,OAAO,GAAG,EAAE,GAAG;IACtD,OAAO,GAAGA,OAAO,GAAG;EACtB,CAAC;EAED,MAAMG,mBAAmB,GAAGA,CAAA,KAAc;IACxC,IAAI1G,YAAY,CAACoC,MAAM,KAAK,CAAC,EAAE,OAAO,CAAC;;IAEvC;IACA,MAAMuE,oBAAoB,GAAGzG,UAAU,CAACoC,GAAG,CAACC,QAAQ,KAAK;MACvDzB,UAAU,EAAEyB,QAAQ,CAACC,EAAE;MACvBoE,KAAK,EAAE5G,YAAY,CAACsF,MAAM,CAACS,EAAE,IAAIA,EAAE,CAACjF,UAAU,KAAKyB,QAAQ,CAACC,EAAE,CAAC,CAACJ;IAClE,CAAC,CAAC,CAAC;IAEH,MAAMyE,SAAS,GAAG7G,YAAY,CAACoC,MAAM;IACrC,MAAM0E,cAAc,GAAGD,SAAS,GAAG3G,UAAU,CAACkC,MAAM;IACpD,MAAM2E,QAAQ,GAAGJ,oBAAoB,CAACK,MAAM,CAAC,CAACC,GAAG,EAAEC,GAAG,KAAK;MACzD,OAAOD,GAAG,GAAGtE,IAAI,CAACwE,GAAG,CAACD,GAAG,CAACN,KAAK,GAAGE,cAAc,EAAE,CAAC,CAAC;IACtD,CAAC,EAAE,CAAC,CAAC,GAAG5G,UAAU,CAACkC,MAAM;;IAEzB;IACA,MAAMgF,WAAW,GAAGzE,IAAI,CAACwE,GAAG,CAACL,cAAc,EAAE,CAAC,CAAC;IAC/C,MAAMO,YAAY,GAAG1E,IAAI,CAACuB,GAAG,CAAC,CAAC,EAAE,CAAC,GAAI6C,QAAQ,GAAGK,WAAY,CAAC;;IAE9D;IACA,MAAME,eAAe,GAAG3E,IAAI,CAACoB,GAAG,CAAC,CAAC,EAAE8C,SAAS,GAAG,GAAG,CAAC,CAAC,CAAC;;IAEtD,OAAO,CAACQ,YAAY,GAAG,GAAG,GAAGC,eAAe,GAAG,GAAG,IAAI,GAAG;EAC3D,CAAC;EAED,IAAI5H,OAAO,EAAE;IACX,oBACER,OAAA;MAAKqI,SAAS,EAAC,qBAAqB;MAAAC,QAAA,gBAClCtI,OAAA;QAAKqI,SAAS,EAAC;MAAiB;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACvC1I,OAAA;QAAAsI,QAAA,EAAG;MAAgC;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACpC,CAAC;EAEV;EAEA,IAAIhI,KAAK,EAAE;IACT,oBACEV,OAAA;MAAKqI,SAAS,EAAC,mBAAmB;MAAAC,QAAA,gBAChCtI,OAAA;QAAKqI,SAAS,EAAC,YAAY;QAAAC,QAAA,EAAC;MAAE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eACpC1I,OAAA;QAAAsI,QAAA,EAAI;MAA0B;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACnC1I,OAAA;QAAAsI,QAAA,EAAI5H;MAAK;QAAA6H,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACd1I,OAAA;QAAQ2I,OAAO,EAAE1G,QAAS;QAACoG,SAAS,EAAC,iBAAiB;QAAAC,QAAA,EAAC;MAEvD;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAEV;EAEA,oBACE1I,OAAA;IAAKqI,SAAS,EAAC,uBAAuB;IAAAC,QAAA,gBAEpCtI,OAAA;MAAKqI,SAAS,EAAC,oBAAoB;MAAAC,QAAA,gBACjCtI,OAAA;QAAKqI,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC3BtI,OAAA;UAAAsI,QAAA,EAAI;QAAgC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACzC1I,OAAA;UAAAsI,QAAA,EAAG;QAAyD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7D,CAAC,EACLtI,OAAO,iBACNJ,OAAA;QAAQ2I,OAAO,EAAEvI,OAAQ;QAACiI,SAAS,EAAC,WAAW;QAAAC,QAAA,eAC7CtI,OAAA;UAAK4I,KAAK,EAAC,IAAI;UAACC,MAAM,EAAC,IAAI;UAACC,OAAO,EAAC,WAAW;UAACC,IAAI,EAAC,MAAM;UAACC,MAAM,EAAC,cAAc;UAACC,WAAW,EAAC,GAAG;UAAAX,QAAA,gBAC/FtI,OAAA;YAAMkJ,EAAE,EAAC,IAAI;YAACC,EAAE,EAAC,GAAG;YAACC,EAAE,EAAC,GAAG;YAACC,EAAE,EAAC;UAAI;YAAAd,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAC3C1I,OAAA;YAAMkJ,EAAE,EAAC,GAAG;YAACC,EAAE,EAAC,GAAG;YAACC,EAAE,EAAC,IAAI;YAACC,EAAE,EAAC;UAAI;YAAAd,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CACT;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAGN1I,OAAA;MAAKqI,SAAS,EAAC,kBAAkB;MAAAC,QAAA,EAC9B,CACC;QAAEhF,EAAE,EAAE,UAAU;QAAEgG,KAAK,EAAE,UAAU;QAAEC,IAAI,EAAE;MAAK,CAAC,EACjD;QAAEjG,EAAE,EAAE,UAAU;QAAEgG,KAAK,EAAE,UAAU;QAAEC,IAAI,EAAE;MAAK,CAAC,EACjD;QAAEjG,EAAE,EAAE,MAAM;QAAEgG,KAAK,EAAE,eAAe;QAAEC,IAAI,EAAE;MAAK,CAAC,EAClD;QAAEjG,EAAE,EAAE,aAAa;QAAEgG,KAAK,EAAE,aAAa;QAAEC,IAAI,EAAE;MAAK,CAAC,CACxD,CAACnG,GAAG,CAACoG,GAAG,iBACPxJ,OAAA;QAEEqI,SAAS,EAAE,WAAW/H,SAAS,KAAKkJ,GAAG,CAAClG,EAAE,GAAG,QAAQ,GAAG,EAAE,EAAG;QAC7DqF,OAAO,EAAEA,CAAA,KAAMpI,YAAY,CAACiJ,GAAG,CAAClG,EAAS,CAAE;QAAAgF,QAAA,gBAE3CtI,OAAA;UAAMqI,SAAS,EAAC,UAAU;UAAAC,QAAA,EAAEkB,GAAG,CAACD;QAAI;UAAAhB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eAC5C1I,OAAA;UAAMqI,SAAS,EAAC,WAAW;UAAAC,QAAA,EAAEkB,GAAG,CAACF;QAAK;UAAAf,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA,GALzCc,GAAG,CAAClG,EAAE;QAAAiF,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAML,CACT;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGN1I,OAAA;MAAKqI,SAAS,EAAC,qBAAqB;MAAAC,QAAA,GAEjChI,SAAS,KAAK,UAAU,iBACvBN,OAAA;QAAKqI,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC3BtI,OAAA;UAAKqI,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAC7BtI,OAAA;YAAKqI,SAAS,EAAC,eAAe;YAAAC,QAAA,gBAC5BtI,OAAA;cAAKqI,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1BtI,OAAA;gBAAAsI,QAAA,EAAI;cAAY;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACrB1I,OAAA;gBAAMqI,SAAS,EAAE,gBAAgBzH,WAAW,GAAG,QAAQ,GAAG,UAAU,EAAG;gBAAA0H,QAAA,EACpE1H,WAAW,GAAG,QAAQ,GAAG;cAAU;gBAAA2H,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eACN1I,OAAA;cAAKqI,SAAS,EAAC,cAAc;cAAAC,QAAA,EAC1B1H,WAAW,gBACVZ,OAAA,CAAAE,SAAA;gBAAAoI,QAAA,gBACEtI,OAAA;kBAAKqI,SAAS,EAAC,QAAQ;kBAAAC,QAAA,gBACrBtI,OAAA;oBAAMqI,SAAS,EAAC,cAAc;oBAAAC,QAAA,EAAC;kBAAQ;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAC9C1I,OAAA;oBAAMqI,SAAS,EAAC,cAAc;oBAAAC,QAAA,GAAE,CAAC1H,WAAW,CAACgC,QAAQ,GAAG,GAAG,EAAEwC,OAAO,CAAC,CAAC,CAAC,EAAC,GAAC;kBAAA;oBAAAmD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7E,CAAC,eACN1I,OAAA;kBAAKqI,SAAS,EAAC,QAAQ;kBAAAC,QAAA,gBACrBtI,OAAA;oBAAMqI,SAAS,EAAC,cAAc;oBAAAC,QAAA,EAAC;kBAAa;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACnD1I,OAAA;oBAAMqI,SAAS,EAAC,cAAc;oBAAAC,QAAA,EAAE1H,WAAW,CAAC6I;kBAAY;oBAAAlB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7D,CAAC,eACN1I,OAAA;kBAAKqI,SAAS,EAAC,QAAQ;kBAAAC,QAAA,gBACrBtI,OAAA;oBAAMqI,SAAS,EAAC,cAAc;oBAAAC,QAAA,EAAC;kBAAO;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAC7C1I,OAAA;oBAAMqI,SAAS,EAAC,cAAc;oBAAAC,QAAA,EAC3B,IAAIjE,IAAI,CAACzD,WAAW,CAAC8I,YAAY,CAAC,CAACC,kBAAkB,CAAC;kBAAC;oBAAApB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACpD,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC;cAAA,eACN,CAAC,gBAEH1I,OAAA;gBAAAsI,QAAA,EAAG;cAAsE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG;YAC7E;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEN1I,OAAA;YAAKqI,SAAS,EAAC,eAAe;YAAAC,QAAA,gBAC5BtI,OAAA;cAAKqI,SAAS,EAAC,aAAa;cAAAC,QAAA,eAC1BtI,OAAA;gBAAAsI,QAAA,EAAI;cAAa;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnB,CAAC,eACN1I,OAAA;cAAKqI,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC3BtI,OAAA;gBAAKqI,SAAS,EAAC,QAAQ;gBAAAC,QAAA,gBACrBtI,OAAA;kBAAMqI,SAAS,EAAC,cAAc;kBAAAC,QAAA,EAAC;gBAAc;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACpD1I,OAAA;kBAAMqI,SAAS,EAAC,cAAc;kBAAAC,QAAA,EAAExH,YAAY,CAACoC;gBAAM;kBAAAqF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxD,CAAC,eACN1I,OAAA;gBAAKqI,SAAS,EAAC,QAAQ;gBAAAC,QAAA,gBACrBtI,OAAA;kBAAMqI,SAAS,EAAC,cAAc;kBAAAC,QAAA,EAAC;gBAAkB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACxD1I,OAAA;kBAAMqI,SAAS,EAAC,cAAc;kBAAAC,QAAA,GAC3B,IAAIsB,GAAG,CAAC9I,YAAY,CAACsC,GAAG,CAACyD,EAAE,IAAIA,EAAE,CAACjF,UAAU,CAAC,CAAC,CAACiC,IAAI,EAAC,KAAG,EAAC7C,UAAU,CAACkC,MAAM;gBAAA;kBAAAqF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACN1I,OAAA;gBAAKqI,SAAS,EAAC,QAAQ;gBAAAC,QAAA,gBACrBtI,OAAA;kBAAMqI,SAAS,EAAC,cAAc;kBAAAC,QAAA,EAAC;gBAAY;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAClD1I,OAAA;kBAAMqI,SAAS,EAAC,cAAc;kBAAAC,QAAA,GAAEd,mBAAmB,CAAC,CAAC,CAACpC,OAAO,CAAC,CAAC,CAAC,EAAC,GAAC;gBAAA;kBAAAmD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEN1I,OAAA;YAAKqI,SAAS,EAAC,eAAe;YAAAC,QAAA,gBAC5BtI,OAAA;cAAKqI,SAAS,EAAC,aAAa;cAAAC,QAAA,eAC1BtI,OAAA;gBAAAsI,QAAA,EAAI;cAAa;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnB,CAAC,eACN1I,OAAA;cAAKqI,SAAS,EAAC,cAAc;cAAAC,QAAA,eAC3BtI,OAAA;gBAAKqI,SAAS,EAAC,gBAAgB;gBAAAC,QAAA,gBAC7BtI,OAAA;kBACE2I,OAAO,EAAE/C,0BAA2B;kBACpCyC,SAAS,EAAC,mBAAmB;kBAAAC,QAAA,EAC9B;gBAED;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACT1I,OAAA;kBACE2I,OAAO,EAAE1E,mBAAoB;kBAC7B4F,QAAQ,EAAE/I,YAAY,CAACoC,MAAM,GAAG,EAAE,IAAIhC,gBAAgB,CAACE,UAAW;kBAClEiH,SAAS,EAAC,iBAAiB;kBAAAC,QAAA,EAE1BpH,gBAAgB,CAACE,UAAU,GAAG,aAAa,GAAG;gBAAa;kBAAAmH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,EAGLxH,gBAAgB,CAACE,UAAU,iBAC1BpB,OAAA;UAAKqI,SAAS,EAAC,wBAAwB;UAAAC,QAAA,gBACrCtI,OAAA;YAAKqI,SAAS,EAAC,iBAAiB;YAAAC,QAAA,gBAC9BtI,OAAA;cAAAsI,QAAA,EAAI;YAAoB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC7B1I,OAAA;cAAQ2I,OAAO,EAAErD,kBAAmB;cAAC+C,SAAS,EAAC,0BAA0B;cAAAC,QAAA,EAAC;YAE1E;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eACN1I,OAAA;YAAKqI,SAAS,EAAC,kBAAkB;YAAAC,QAAA,gBAC/BtI,OAAA;cAAKqI,SAAS,EAAC,kBAAkB;cAAAC,QAAA,gBAC/BtI,OAAA;gBAAKqI,SAAS,EAAC,iBAAiB;gBAAAC,QAAA,gBAC9BtI,OAAA;kBAAMqI,SAAS,EAAC,cAAc;kBAAAC,QAAA,EAAC;gBAAK;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC3C1I,OAAA;kBAAMqI,SAAS,EAAC,cAAc;kBAAAC,QAAA,GAC3BpH,gBAAgB,CAACG,YAAY,EAAC,KAAG,EAACH,gBAAgB,CAACI,WAAW;gBAAA;kBAAAiH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3D,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACN1I,OAAA;gBAAKqI,SAAS,EAAC,iBAAiB;gBAAAC,QAAA,gBAC9BtI,OAAA;kBAAMqI,SAAS,EAAC,cAAc;kBAAAC,QAAA,EAAC;gBAAI;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC1C1I,OAAA;kBAAMqI,SAAS,EAAC,cAAc;kBAAAC,QAAA,EAAEpH,gBAAgB,CAACK,WAAW,CAAC6D,OAAO,CAAC,CAAC;gBAAC;kBAAAmD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5E,CAAC,eACN1I,OAAA;gBAAKqI,SAAS,EAAC,iBAAiB;gBAAAC,QAAA,gBAC9BtI,OAAA;kBAAMqI,SAAS,EAAC,cAAc;kBAAAC,QAAA,EAAC;gBAAQ;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC9C1I,OAAA;kBAAMqI,SAAS,EAAC,cAAc;kBAAAC,QAAA,GAC3B,CAACpH,gBAAgB,CAACM,eAAe,GAAG,GAAG,EAAE4D,OAAO,CAAC,CAAC,CAAC,EAAC,GACvD;gBAAA;kBAAAmD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACN1I,OAAA;gBAAKqI,SAAS,EAAC,iBAAiB;gBAAAC,QAAA,gBAC9BtI,OAAA;kBAAMqI,SAAS,EAAC,cAAc;kBAAAC,QAAA,EAAC;gBAAc;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACpD1I,OAAA;kBAAMqI,SAAS,EAAC,cAAc;kBAAAC,QAAA,EAC3BnB,cAAc,CAACjG,gBAAgB,CAACO,sBAAsB;gBAAC;kBAAA8G,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACN1I,OAAA;cAAKqI,SAAS,EAAC,cAAc;cAAAC,QAAA,eAC3BtI,OAAA;gBACEqI,SAAS,EAAC,eAAe;gBACzByB,KAAK,EAAE;kBACLlB,KAAK,EAAE,GAAI1H,gBAAgB,CAACG,YAAY,GAAGH,gBAAgB,CAACI,WAAW,GAAI,GAAG;gBAChF;cAAE;gBAAAiH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CACN,EAGApI,SAAS,KAAK,UAAU,iBACvBN,OAAA;QAAKqI,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC3BtI,OAAA;UAAKqI,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChCtI,OAAA;YAAKqI,SAAS,EAAC,eAAe;YAAAC,QAAA,gBAC5BtI,OAAA;cAAAsI,QAAA,EAAI;YAAc;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACvB1I,OAAA;cAAAsI,QAAA,EAAG;YAA+G;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnH,CAAC,eAEN1I,OAAA;YAAKqI,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBAChCtI,OAAA;cAAKqI,SAAS,EAAC,eAAe;cAAAC,QAAA,gBAC5BtI,OAAA;gBAAAsI,QAAA,EAAO;cAAoB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACnC1I,OAAA;gBAAKqI,SAAS,EAAC,aAAa;gBAAAC,QAAA,gBAC1BtI,OAAA;kBAAMqI,SAAS,EAAC,YAAY;kBAAAC,QAAA,GAAExH,YAAY,CAACoC,MAAM,EAAC,WAAS;gBAAA;kBAAAqF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAClE1I,OAAA;kBAAMqI,SAAS,EAAE,gBAAgBb,mBAAmB,CAAC,CAAC,GAAG,EAAE,GAAG,MAAM,GAAGA,mBAAmB,CAAC,CAAC,GAAG,EAAE,GAAG,MAAM,GAAG,MAAM,EAAG;kBAAAc,QAAA,GAAC,WAC5G,EAACd,mBAAmB,CAAC,CAAC,CAACpC,OAAO,CAAC,CAAC,CAAC,EAAC,GAC7C;gBAAA;kBAAAmD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAEN1I,OAAA;cAAKqI,SAAS,EAAC,eAAe;cAAAC,QAAA,gBAC5BtI,OAAA;gBAAAsI,QAAA,EAAO;cAAiB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAChC1I,OAAA;gBAAKqI,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,GACnCtH,UAAU,CAAC+I,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC3G,GAAG,CAACC,QAAQ,IAAI;kBACtC,MAAMqE,KAAK,GAAG5G,YAAY,CAACsF,MAAM,CAACS,EAAE,IAAIA,EAAE,CAACjF,UAAU,KAAKyB,QAAQ,CAACC,EAAE,CAAC,CAACJ,MAAM;kBAC7E,MAAM8G,UAAU,GAAGlJ,YAAY,CAACoC,MAAM,GAAG,CAAC,GAAIwE,KAAK,GAAG5G,YAAY,CAACoC,MAAM,GAAI,GAAG,GAAG,CAAC;kBACpF,oBACElD,OAAA;oBAAuBqI,SAAS,EAAC,mBAAmB;oBAAAC,QAAA,gBAClDtI,OAAA;sBAAMqI,SAAS,EAAC,eAAe;sBAAAC,QAAA,EAAEjF,QAAQ,CAACG;oBAAI;sBAAA+E,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC,eACtD1I,OAAA;sBAAKqI,SAAS,EAAC,kBAAkB;sBAAAC,QAAA,eAC/BtI,OAAA;wBACEqI,SAAS,EAAC,mBAAmB;wBAC7ByB,KAAK,EAAE;0BACLlB,KAAK,EAAE,GAAGoB,UAAU,GAAG;0BACvBC,eAAe,EAAE5G,QAAQ,CAAC6G;wBAC5B;sBAAE;wBAAA3B,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACJ,CAAC,eACN1I,OAAA;sBAAMqI,SAAS,EAAC,oBAAoB;sBAAAC,QAAA,EAAEZ;oBAAK;sBAAAa,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC;kBAAA,GAX3CrF,QAAQ,CAACC,EAAE;oBAAAiF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAYhB,CAAC;gBAEV,CAAC,CAAC,EACD1H,UAAU,CAACkC,MAAM,GAAG,CAAC,iBACpBlD,OAAA;kBAAKqI,SAAS,EAAC,mBAAmB;kBAAAC,QAAA,GAAC,GAChC,EAACtH,UAAU,CAACkC,MAAM,GAAG,CAAC,EAAC,kBAC1B;gBAAA;kBAAAqF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CACN;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEN1I,OAAA;YAAKqI,SAAS,EAAC,kBAAkB;YAAAC,QAAA,gBAC/BtI,OAAA;cACE2I,OAAO,EAAE/C,0BAA2B;cACpCyC,SAAS,EAAC,mBAAmB;cAAAC,QAAA,EAC9B;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAET1I,OAAA;cACE2I,OAAO,EAAE1E,mBAAoB;cAC7B4F,QAAQ,EAAE/I,YAAY,CAACoC,MAAM,GAAG,EAAE,IAAIhC,gBAAgB,CAACE,UAAW;cAClEiH,SAAS,EAAC,iBAAiB;cAAAC,QAAA,EAE1BpH,gBAAgB,CAACE,UAAU,GAAG,yBAAyB,GAAG;YAAgB;cAAAmH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,EAEL5H,YAAY,CAACoC,MAAM,GAAG,EAAE,iBACvBlD,OAAA;UAAKqI,SAAS,EAAC,kBAAkB;UAAAC,QAAA,gBAC/BtI,OAAA;YAAKqI,SAAS,EAAC,cAAc;YAAAC,QAAA,EAAC;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACtC1I,OAAA;YAAKqI,SAAS,EAAC,iBAAiB;YAAAC,QAAA,gBAC9BtI,OAAA;cAAAsI,QAAA,EAAI;YAA0B;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACnC1I,OAAA;cAAAsI,QAAA,GAAG,8EAEkB,EAACxH,YAAY,CAACoC,MAAM,EAAC,YAC1C;YAAA;cAAAqF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACJ1I,OAAA;cAAAsI,QAAA,gBACEtI,OAAA;gBAAAsI,QAAA,EAAQ;cAAe;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,0HAElC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CACN,EAGApI,SAAS,KAAK,MAAM,iBACnBN,OAAA;QAAKqI,SAAS,EAAC,UAAU;QAAAC,QAAA,gBACvBtI,OAAA;UAAKqI,SAAS,EAAC,eAAe;UAAAC,QAAA,gBAC5BtI,OAAA;YAAKqI,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC3BtI,OAAA;cACEmK,KAAK,EAAEzI,WAAW,CAACE,UAAW;cAC9BwI,QAAQ,EAAGC,CAAC,IAAK1I,cAAc,CAACwC,IAAI,KAAK;gBAAE,GAAGA,IAAI;gBAAEvC,UAAU,EAAEyI,CAAC,CAACC,MAAM,CAACH;cAAM,CAAC,CAAC,CAAE;cAAA7B,QAAA,gBAEnFtI,OAAA;gBAAQmK,KAAK,EAAC,EAAE;gBAAA7B,QAAA,EAAC;cAAc;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,EACvC1H,UAAU,CAACoC,GAAG,CAACC,QAAQ,iBACtBrD,OAAA;gBAA0BmK,KAAK,EAAE9G,QAAQ,CAACC,EAAG;gBAAAgF,QAAA,EAC1CjF,QAAQ,CAACG;cAAI,GADHH,QAAQ,CAACC,EAAE;gBAAAiF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAEhB,CACT,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC,eAET1I,OAAA;cACEuK,IAAI,EAAC,MAAM;cACXC,WAAW,EAAC,wBAAwB;cACpCL,KAAK,EAAEzI,WAAW,CAACI,UAAW;cAC9BsI,QAAQ,EAAGC,CAAC,IAAK1I,cAAc,CAACwC,IAAI,KAAK;gBAAE,GAAGA,IAAI;gBAAErC,UAAU,EAAEuI,CAAC,CAACC,MAAM,CAACH;cAAM,CAAC,CAAC;YAAE;cAAA5B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpF,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAEN1I,OAAA;YAAKqI,SAAS,EAAC,cAAc;YAAAC,QAAA,eAC3BtI,OAAA;cAAQ2I,OAAO,EAAEpD,uBAAwB;cAAC8C,SAAS,EAAC,uBAAuB;cAAAC,QAAA,EAAC;YAE5E;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEN1I,OAAA;UAAKqI,SAAS,EAAC,+BAA+B;UAAAC,QAAA,eAC5CtI,OAAA;YAAOqI,SAAS,EAAC,qBAAqB;YAAAC,QAAA,gBACpCtI,OAAA;cAAAsI,QAAA,eACEtI,OAAA;gBAAAsI,QAAA,gBACEtI,OAAA;kBAAAsI,QAAA,EAAI;gBAAW;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACpB1I,OAAA;kBAAAsI,QAAA,EAAI;gBAAM;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACf1I,OAAA;kBAAAsI,QAAA,EAAI;gBAAQ;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACjB1I,OAAA;kBAAAsI,QAAA,EAAI;gBAAO;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACd;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC,eACR1I,OAAA;cAAAsI,QAAA,EACGxH,YAAY,CACVsF,MAAM,CAACS,EAAE,IAAI;gBACZ,IAAInF,WAAW,CAACE,UAAU,IAAIiF,EAAE,CAACjF,UAAU,KAAKF,WAAW,CAACE,UAAU,EAAE,OAAO,KAAK;gBACpF,IAAIF,WAAW,CAACI,UAAU,IAAI,CAAC+E,EAAE,CAACC,WAAW,CAAC2D,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAChJ,WAAW,CAACI,UAAU,CAAC2I,WAAW,CAAC,CAAC,CAAC,EAAE,OAAO,KAAK;gBACxH,OAAO,IAAI;cACb,CAAC,CAAC,CACDrH,GAAG,CAACuH,IAAI;gBAAA,IAAAC,gBAAA;gBAAA,oBACP5K,OAAA;kBAAAsI,QAAA,gBACEtI,OAAA;oBAAIqI,SAAS,EAAC,kBAAkB;oBAAAC,QAAA,EAAEqC,IAAI,CAAC7D;kBAAW;oBAAAyB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACxD1I,OAAA;oBAAIqI,SAAS,EAAEsC,IAAI,CAACnE,MAAM,IAAI,CAAC,GAAG,eAAe,GAAG,cAAe;oBAAA8B,QAAA,GAAC,GACjE,EAAC7E,IAAI,CAACoH,GAAG,CAACF,IAAI,CAACnE,MAAM,CAAC,CAACpB,OAAO,CAAC,CAAC,CAAC;kBAAA;oBAAAmD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChC,CAAC,eACL1I,OAAA;oBAAAsI,QAAA,eACEtI,OAAA;sBACEqI,SAAS,EAAC,gBAAgB;sBAC1ByB,KAAK,EAAE;wBAAEG,eAAe,GAAAW,gBAAA,GAAE5J,UAAU,CAACiG,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAC5D,EAAE,KAAKqH,IAAI,CAAC/I,UAAU,CAAC,cAAAgJ,gBAAA,uBAA9CA,gBAAA,CAAgDV;sBAAM,CAAE;sBAAA5B,QAAA,EAEjFtB,eAAe,CAAC2D,IAAI,CAAC/I,UAAU;oBAAC;sBAAA2G,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC7B;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACL,CAAC,eACL1I,OAAA;oBAAAsI,QAAA,EAAK,IAAIjE,IAAI,CAACsG,IAAI,CAACG,WAAW,CAAC,CAACnB,kBAAkB,CAAC;kBAAC;oBAAApB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC;gBAAA,GAbnDiC,IAAI,CAACrH,EAAE;kBAAAiF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAcZ,CAAC;cAAA,CACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,EAGApI,SAAS,KAAK,aAAa,iBAC1BN,OAAA;QAAKqI,SAAS,EAAC,iBAAiB;QAAAC,QAAA,EAC7B1H,WAAW,IAAImB,YAAY,gBAC1B/B,OAAA;UAAKqI,SAAS,EAAC,qBAAqB;UAAAC,QAAA,gBAClCtI,OAAA;YAAKqI,SAAS,EAAC,sBAAsB;YAAAC,QAAA,gBACnCtI,OAAA;cAAKqI,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1BtI,OAAA;gBAAAsI,QAAA,EAAI;cAAgB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACzB1I,OAAA;gBAAKqI,SAAS,EAAC,oBAAoB;gBAAAC,QAAA,GAAE,CAACvG,YAAY,CAACa,QAAQ,GAAG,GAAG,EAAEwC,OAAO,CAAC,CAAC,CAAC,EAAC,GAAC;cAAA;gBAAAmD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClF,CAAC,eACN1I,OAAA;cAAKqI,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1BtI,OAAA;gBAAAsI,QAAA,EAAI;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAClB1I,OAAA;gBAAKqI,SAAS,EAAC,oBAAoB;gBAAAC,QAAA,GAAE,CAACvG,YAAY,CAACc,SAAS,GAAG,GAAG,EAAEuC,OAAO,CAAC,CAAC,CAAC,EAAC,GAAC;cAAA;gBAAAmD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnF,CAAC,eACN1I,OAAA;cAAKqI,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1BtI,OAAA;gBAAAsI,QAAA,EAAI;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACf1I,OAAA;gBAAKqI,SAAS,EAAC,oBAAoB;gBAAAC,QAAA,GAAE,CAACvG,YAAY,CAACe,MAAM,GAAG,GAAG,EAAEsC,OAAO,CAAC,CAAC,CAAC,EAAC,GAAC;cAAA;gBAAAmD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChF,CAAC,eACN1I,OAAA;cAAKqI,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1BtI,OAAA;gBAAAsI,QAAA,EAAI;cAAQ;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACjB1I,OAAA;gBAAKqI,SAAS,EAAC,oBAAoB;gBAAAC,QAAA,GAAE,CAACvG,YAAY,CAACgB,OAAO,GAAG,GAAG,EAAEqC,OAAO,CAAC,CAAC,CAAC,EAAC,GAAC;cAAA;gBAAAmD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjF,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEN1I,OAAA;YAAKqI,SAAS,EAAC,sBAAsB;YAAAC,QAAA,gBACnCtI,OAAA;cAAAsI,QAAA,EAAI;YAAuB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAChC1I,OAAA;cAAKqI,SAAS,EAAC,sCAAsC;cAAAC,QAAA,eACnDtI,OAAA;gBAAOqI,SAAS,EAAC,4BAA4B;gBAAAC,QAAA,gBAC3CtI,OAAA;kBAAAsI,QAAA,eACEtI,OAAA;oBAAAsI,QAAA,gBACEtI,OAAA;sBAAAsI,QAAA,EAAI;oBAAQ;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACjB1I,OAAA;sBAAAsI,QAAA,EAAI;oBAAS;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eAClB1I,OAAA;sBAAAsI,QAAA,EAAI;oBAAM;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACf1I,OAAA;sBAAAsI,QAAA,EAAI;oBAAQ;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACjB1I,OAAA;sBAAAsI,QAAA,EAAI;oBAAO;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACd;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACA,CAAC,eACR1I,OAAA;kBAAAsI,QAAA,EACGvG,YAAY,CAACoB,mBAAmB,CAACC,GAAG,CAAC2H,EAAE;oBAAA,IAAAC,iBAAA;oBAAA,oBACtChL,OAAA;sBAAAsI,QAAA,gBACEtI,OAAA;wBAAAsI,QAAA,eACEtI,OAAA;0BACEqI,SAAS,EAAC,gBAAgB;0BAC1ByB,KAAK,EAAE;4BAAEG,eAAe,GAAAe,iBAAA,GAAEhK,UAAU,CAACiG,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAC5D,EAAE,KAAKyH,EAAE,CAACnJ,UAAU,CAAC,cAAAoJ,iBAAA,uBAA5CA,iBAAA,CAA8Cd;0BAAM,CAAE;0BAAA5B,QAAA,EAE/EyC,EAAE,CAACxH;wBAAY;0BAAAgF,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACZ;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACL,CAAC,eACL1I,OAAA;wBAAAsI,QAAA,GAAK,CAACyC,EAAE,CAAClI,SAAS,GAAG,GAAG,EAAEuC,OAAO,CAAC,CAAC,CAAC,EAAC,GAAC;sBAAA;wBAAAmD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eAC3C1I,OAAA;wBAAAsI,QAAA,GAAK,CAACyC,EAAE,CAACjI,MAAM,GAAG,GAAG,EAAEsC,OAAO,CAAC,CAAC,CAAC,EAAC,GAAC;sBAAA;wBAAAmD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eACxC1I,OAAA;wBAAAsI,QAAA,GAAK,CAACyC,EAAE,CAAChI,OAAO,GAAG,GAAG,EAAEqC,OAAO,CAAC,CAAC,CAAC,EAAC,GAAC;sBAAA;wBAAAmD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eACzC1I,OAAA;wBAAAsI,QAAA,EAAKyC,EAAE,CAACpH;sBAAO;wBAAA4E,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC;oBAAA,GAZdqC,EAAE,CAACnJ,UAAU;sBAAA2G,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAalB,CAAC;kBAAA,CACN;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,gBAEN1I,OAAA;UAAKqI,SAAS,EAAC,qBAAqB;UAAAC,QAAA,gBAClCtI,OAAA;YAAKqI,SAAS,EAAC,kBAAkB;YAAAC,QAAA,EAAC;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAC1C1I,OAAA;YAAAsI,QAAA,EAAI;UAA6B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACtC1I,OAAA;YAAAsI,QAAA,EAAG;UAAuD;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAC9D1I,OAAA;YACE2I,OAAO,EAAEA,CAAA,KAAMpI,YAAY,CAAC,UAAU,CAAE;YACxC8H,SAAS,EAAC,iBAAiB;YAAAC,QAAA,EAC5B;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN;MACN;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACrI,EAAA,CA1rBWF,mBAAuD;AAAA8K,EAAA,GAAvD9K,mBAAuD;AAAA,IAAA8K,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}