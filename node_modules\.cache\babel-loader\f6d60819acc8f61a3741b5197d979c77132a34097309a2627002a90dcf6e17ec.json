{"ast": null, "code": "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { env } from '@tensorflow/tfjs-core';\nimport { transposeImplCPU } from '../kernel_utils/shared';\nimport { TransposeProgram } from '../transpose_gpu';\nimport { TransposePackedProgram } from '../transpose_packed_gpu';\nexport function transposeImpl(x, perm, backend) {\n  const program = env().getBool('WEBGL_PACK_ARRAY_OPERATIONS') ? new TransposePackedProgram(x.shape, perm) : new TransposeProgram(x.shape, perm);\n  return backend.runWebGLProgram(program, [x], x.dtype);\n}\nexport { transposeImplCPU };", "map": {"version": 3, "names": ["env", "transposeImplCPU", "TransposeProgram", "TransposePackedProgram", "transposeImpl", "x", "perm", "backend", "program", "getBool", "shape", "runWebGLProgram", "dtype"], "sources": ["C:\\tfjs-backend-webgl\\src\\kernels\\Transpose_impl.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {env, TensorInfo} from '@tensorflow/tfjs-core';\n\nimport {MathBackendWebGL} from '../backend_webgl';\nimport {transposeImplCPU} from '../kernel_utils/shared';\nimport {TransposeProgram} from '../transpose_gpu';\nimport {TransposePackedProgram} from '../transpose_packed_gpu';\n\nexport function transposeImpl(\n    x: TensorInfo, perm: number[], backend: MathBackendWebGL): TensorInfo {\n  const program = env().getBool('WEBGL_PACK_ARRAY_OPERATIONS') ?\n      new TransposePackedProgram(x.shape, perm) :\n      new TransposeProgram(x.shape, perm);\n  return backend.runWebGLProgram(program, [x], x.dtype);\n}\n\nexport {transposeImplCPU};\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAiBA,SAAQA,GAAG,QAAmB,uBAAuB;AAGrD,SAAQC,gBAAgB,QAAO,wBAAwB;AACvD,SAAQC,gBAAgB,QAAO,kBAAkB;AACjD,SAAQC,sBAAsB,QAAO,yBAAyB;AAE9D,OAAM,SAAUC,aAAaA,CACzBC,CAAa,EAAEC,IAAc,EAAEC,OAAyB;EAC1D,MAAMC,OAAO,GAAGR,GAAG,EAAE,CAACS,OAAO,CAAC,6BAA6B,CAAC,GACxD,IAAIN,sBAAsB,CAACE,CAAC,CAACK,KAAK,EAAEJ,IAAI,CAAC,GACzC,IAAIJ,gBAAgB,CAACG,CAAC,CAACK,KAAK,EAAEJ,IAAI,CAAC;EACvC,OAAOC,OAAO,CAACI,eAAe,CAACH,OAAO,EAAE,CAACH,CAAC,CAAC,EAAEA,CAAC,CAACO,KAAK,CAAC;AACvD;AAEA,SAAQX,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}