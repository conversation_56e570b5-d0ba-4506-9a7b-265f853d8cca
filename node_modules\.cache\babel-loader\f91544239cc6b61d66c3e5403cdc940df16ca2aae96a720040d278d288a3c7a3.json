{"ast": null, "code": "/**\n * @license\n * Copyright 2018 Google LLC\n *\n * Use of this source code is governed by an MIT-style\n * license that can be found in the LICENSE file or at\n * https://opensource.org/licenses/MIT.\n * =============================================================================\n */\n/* Original source: keras/contraints.py */\nimport * as tfc from '@tensorflow/tfjs-core';\nimport { serialization, tidy } from '@tensorflow/tfjs-core';\nimport { epsilon } from './backend/common';\nimport { deserializeKerasObject, serializeKerasObject } from './utils/generic_utils';\n/**\n * Helper function used by many of the Constraints to find the L2Norms.\n */\nfunction calcL2Norms(w, axis) {\n  return tidy(() => tfc.sqrt(tfc.sum(tfc.mul(w, w), axis, true)));\n}\n/**\n * Base class for functions that impose constraints on weight values\n *\n * @doc {\n *   heading: 'Constraints',\n *   subheading: 'Classes',\n *   namespace: 'constraints'\n * }\n */\nexport class Constraint extends serialization.Serializable {\n  getConfig() {\n    return {};\n  }\n}\nclass MaxNorm extends Constraint {\n  constructor(args) {\n    super();\n    this.defaultMaxValue = 2;\n    this.defaultAxis = 0;\n    this.maxValue = args.maxValue != null ? args.maxValue : this.defaultMaxValue;\n    this.axis = args.axis != null ? args.axis : this.defaultAxis;\n  }\n  apply(w) {\n    return tidy(() => {\n      const norms = calcL2Norms(w, this.axis);\n      const desired = tfc.clipByValue(norms, 0, this.maxValue);\n      return tfc.mul(w, tfc.div(desired, tfc.add(epsilon(), norms)));\n    });\n  }\n  getConfig() {\n    return {\n      maxValue: this.maxValue,\n      axis: this.axis\n    };\n  }\n}\n/** @nocollapse */\nMaxNorm.className = 'MaxNorm';\nexport { MaxNorm };\nserialization.registerClass(MaxNorm);\nclass UnitNorm extends Constraint {\n  constructor(args) {\n    super();\n    this.defaultAxis = 0;\n    this.axis = args.axis != null ? args.axis : this.defaultAxis;\n  }\n  apply(w) {\n    return tidy(() => tfc.div(w, tfc.add(epsilon(), calcL2Norms(w, this.axis))));\n  }\n  getConfig() {\n    return {\n      axis: this.axis\n    };\n  }\n}\n/** @nocollapse */\nUnitNorm.className = 'UnitNorm';\nexport { UnitNorm };\nserialization.registerClass(UnitNorm);\nclass NonNeg extends Constraint {\n  apply(w) {\n    return tfc.relu(w);\n  }\n}\n/** @nocollapse */\nNonNeg.className = 'NonNeg';\nexport { NonNeg };\nserialization.registerClass(NonNeg);\nclass MinMaxNorm extends Constraint {\n  constructor(args) {\n    super();\n    this.defaultMinValue = 0.0;\n    this.defaultMaxValue = 1.0;\n    this.defaultRate = 1.0;\n    this.defaultAxis = 0;\n    this.minValue = args.minValue != null ? args.minValue : this.defaultMinValue;\n    this.maxValue = args.maxValue != null ? args.maxValue : this.defaultMaxValue;\n    this.rate = args.rate != null ? args.rate : this.defaultRate;\n    this.axis = args.axis != null ? args.axis : this.defaultAxis;\n  }\n  apply(w) {\n    return tidy(() => {\n      const norms = calcL2Norms(w, this.axis);\n      const desired = tfc.add(tfc.mul(this.rate, tfc.clipByValue(norms, this.minValue, this.maxValue)), tfc.mul(1.0 - this.rate, norms));\n      return tfc.mul(w, tfc.div(desired, tfc.add(epsilon(), norms)));\n    });\n  }\n  getConfig() {\n    return {\n      minValue: this.minValue,\n      maxValue: this.maxValue,\n      rate: this.rate,\n      axis: this.axis\n    };\n  }\n}\n/** @nocollapse */\nMinMaxNorm.className = 'MinMaxNorm';\nexport { MinMaxNorm };\nserialization.registerClass(MinMaxNorm);\n// Maps the JavaScript-like identifier keys to the corresponding registry\n// symbols.\nexport const CONSTRAINT_IDENTIFIER_REGISTRY_SYMBOL_MAP = {\n  'maxNorm': 'MaxNorm',\n  'minMaxNorm': 'MinMaxNorm',\n  'nonNeg': 'NonNeg',\n  'unitNorm': 'UnitNorm'\n};\nexport function serializeConstraint(constraint) {\n  return serializeKerasObject(constraint);\n}\nexport function deserializeConstraint(config) {\n  let customObjects = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n  return deserializeKerasObject(config, serialization.SerializationMap.getMap().classNameMap, customObjects, 'constraint');\n}\nexport function getConstraint(identifier) {\n  if (identifier == null) {\n    return null;\n  }\n  if (typeof identifier === 'string') {\n    const className = identifier in CONSTRAINT_IDENTIFIER_REGISTRY_SYMBOL_MAP ? CONSTRAINT_IDENTIFIER_REGISTRY_SYMBOL_MAP[identifier] : identifier;\n    const config = {\n      className,\n      config: {}\n    };\n    return deserializeConstraint(config);\n  } else if (identifier instanceof Constraint) {\n    return identifier;\n  } else {\n    return deserializeConstraint(identifier);\n  }\n}", "map": {"version": 3, "names": ["tfc", "serialization", "tidy", "epsilon", "deserializeKerasObject", "serializeKerasObject", "calcL2Norms", "w", "axis", "sqrt", "sum", "mul", "Constraint", "Serializable", "getConfig", "MaxNorm", "constructor", "args", "defaultMaxValue", "defaultAxis", "maxValue", "apply", "norms", "desired", "clipByValue", "div", "add", "className", "registerClass", "UnitNorm", "NonNeg", "relu", "MinMaxNorm", "defaultMinValue", "defaultRate", "minValue", "rate", "CONSTRAINT_IDENTIFIER_REGISTRY_SYMBOL_MAP", "serializeConstraint", "constraint", "deserializeConstraint", "config", "customObjects", "arguments", "length", "undefined", "SerializationMap", "getMap", "classNameMap", "getConstraint", "identifier"], "sources": ["C:\\tfjs-layers\\src\\constraints.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2018 Google LLC\n *\n * Use of this source code is governed by an MIT-style\n * license that can be found in the LICENSE file or at\n * https://opensource.org/licenses/MIT.\n * =============================================================================\n */\n\n/* Original source: keras/contraints.py */\n\nimport * as tfc from '@tensorflow/tfjs-core';\nimport {serialization, Tensor, tidy} from '@tensorflow/tfjs-core';\nimport {epsilon} from './backend/common';\nimport {deserializeKerasObject, serializeKerasObject} from './utils/generic_utils';\n\n/**\n * Helper function used by many of the Constraints to find the L2Norms.\n */\nfunction calcL2Norms(w: Tensor, axis: number): Tensor {\n  return tidy(() => tfc.sqrt(tfc.sum(tfc.mul(w, w), axis, true)));\n}\n\n/**\n * Base class for functions that impose constraints on weight values\n *\n * @doc {\n *   heading: 'Constraints',\n *   subheading: 'Classes',\n *   namespace: 'constraints'\n * }\n */\nexport abstract class Constraint extends serialization.Serializable {\n  /* Porting note: was __call__, apply chosen to match other similar choices */\n  abstract apply(w: Tensor): Tensor;\n  getConfig(): serialization.ConfigDict {\n    return {};\n  }\n}\n\nexport interface MaxNormArgs {\n  /**\n   * Maximum norm for incoming weights\n   */\n  maxValue?: number;\n  /**\n   * Axis along which to calculate norms.\n   *\n   *  For instance, in a `Dense` layer the weight matrix\n   *  has shape `[inputDim, outputDim]`,\n   *  set `axis` to `0` to constrain each weight vector\n   *  of length `[inputDim,]`.\n   *  In a `Conv2D` layer with `dataFormat=\"channels_last\"`,\n   *  the weight tensor has shape\n   *  `[rows, cols, inputDepth, outputDepth]`,\n   *  set `axis` to `[0, 1, 2]`\n   *  to constrain the weights of each filter tensor of size\n   *  `[rows, cols, inputDepth]`.\n   */\n  axis?: number;\n}\n\nexport class MaxNorm extends Constraint {\n  /** @nocollapse */\n  static readonly className = 'MaxNorm';\n  private maxValue: number;\n  private axis: number;\n  private readonly defaultMaxValue = 2;\n  private readonly defaultAxis = 0;\n\n  constructor(args: MaxNormArgs) {\n    super();\n    this.maxValue =\n        args.maxValue != null ? args.maxValue : this.defaultMaxValue;\n    this.axis = args.axis != null ? args.axis : this.defaultAxis;\n  }\n\n  apply(w: Tensor): Tensor {\n    return tidy(() => {\n      const norms = calcL2Norms(w, this.axis);\n      const desired = tfc.clipByValue(norms, 0, this.maxValue);\n      return tfc.mul(w, tfc.div(desired, tfc.add(epsilon(), norms)));\n    });\n  }\n\n  override getConfig(): serialization.ConfigDict {\n    return {maxValue: this.maxValue, axis: this.axis};\n  }\n}\nserialization.registerClass(MaxNorm);\n\nexport interface UnitNormArgs {\n  /**\n   * Axis along which to calculate norms.\n   *\n   * For instance, in a `Dense` layer the weight matrix\n   * has shape `[inputDim, outputDim]`,\n   * set `axis` to `0` to constrain each weight vector\n   * of length `[inputDim,]`.\n   * In a `Conv2D` layer with `dataFormat=\"channels_last\"`,\n   * the weight tensor has shape\n   * `[rows, cols, inputDepth, outputDepth]`,\n   * set `axis` to `[0, 1, 2]`\n   * to constrain the weights of each filter tensor of size\n   * `[rows, cols, inputDepth]`.\n   */\n  axis?: number;\n}\n\nexport class UnitNorm extends Constraint {\n  /** @nocollapse */\n  static readonly className = 'UnitNorm';\n  private axis: number;\n  private readonly defaultAxis = 0;\n  constructor(args: UnitNormArgs) {\n    super();\n    this.axis = args.axis != null ? args.axis : this.defaultAxis;\n  }\n\n  apply(w: Tensor): Tensor {\n    return tidy(\n        () => tfc.div(w, tfc.add(epsilon(), calcL2Norms(w, this.axis))));\n  }\n\n  override getConfig(): serialization.ConfigDict {\n    return {axis: this.axis};\n  }\n}\nserialization.registerClass(UnitNorm);\n\nexport class NonNeg extends Constraint {\n  /** @nocollapse */\n  static readonly className = 'NonNeg';\n\n  apply(w: Tensor): Tensor {\n    return tfc.relu(w);\n  }\n}\nserialization.registerClass(NonNeg);\n\nexport interface MinMaxNormArgs {\n  /**\n   * Minimum norm for incoming weights\n   */\n  minValue?: number;\n  /**\n   * Maximum norm for incoming weights\n   */\n  maxValue?: number;\n  /**\n   * Axis along which to calculate norms.\n   * For instance, in a `Dense` layer the weight matrix\n   * has shape `[inputDim, outputDim]`,\n   * set `axis` to `0` to constrain each weight vector\n   * of length `[inputDim,]`.\n   * In a `Conv2D` layer with `dataFormat=\"channels_last\"`,\n   * the weight tensor has shape\n   * `[rows, cols, inputDepth, outputDepth]`,\n   * set `axis` to `[0, 1, 2]`\n   * to constrain the weights of each filter tensor of size\n   * `[rows, cols, inputDepth]`.\n   */\n  axis?: number;\n  /**\n   * Rate for enforcing the constraint: weights will be rescaled to yield:\n   * `(1 - rate) * norm + rate * norm.clip(minValue, maxValue)`.\n   * Effectively, this means that rate=1.0 stands for strict\n   * enforcement of the constraint, while rate<1.0 means that\n   * weights will be rescaled at each step to slowly move\n   * towards a value inside the desired interval.\n   */\n  rate?: number;\n}\n\nexport class MinMaxNorm extends Constraint {\n  /** @nocollapse */\n  static readonly className = 'MinMaxNorm';\n  private minValue: number;\n  private maxValue: number;\n  private rate: number;\n  private axis: number;\n  private readonly defaultMinValue = 0.0;\n  private readonly defaultMaxValue = 1.0;\n  private readonly defaultRate = 1.0;\n  private readonly defaultAxis = 0;\n\n  constructor(args: MinMaxNormArgs) {\n    super();\n    this.minValue =\n        args.minValue != null ? args.minValue : this.defaultMinValue;\n    this.maxValue =\n        args.maxValue != null ? args.maxValue : this.defaultMaxValue;\n    this.rate = args.rate != null ? args.rate : this.defaultRate;\n    this.axis = args.axis != null ? args.axis : this.defaultAxis;\n  }\n\n  apply(w: Tensor): Tensor {\n    return tidy(() => {\n      const norms = calcL2Norms(w, this.axis);\n      const desired = tfc.add(\n          tfc.mul(\n              this.rate, tfc.clipByValue(norms, this.minValue, this.maxValue)),\n          tfc.mul(1.0 - this.rate, norms));\n      return tfc.mul(w, tfc.div(desired, tfc.add(epsilon(), norms)));\n    });\n  }\n\n  override getConfig(): serialization.ConfigDict {\n    return {\n      minValue: this.minValue,\n      maxValue: this.maxValue,\n      rate: this.rate,\n      axis: this.axis\n    };\n  }\n}\nserialization.registerClass(MinMaxNorm);\n\n/** @docinline */\nexport type ConstraintIdentifier =\n    'maxNorm'|'minMaxNorm'|'nonNeg'|'unitNorm'|string;\n\n// Maps the JavaScript-like identifier keys to the corresponding registry\n// symbols.\nexport const CONSTRAINT_IDENTIFIER_REGISTRY_SYMBOL_MAP:\n    {[identifier in ConstraintIdentifier]: string} = {\n      'maxNorm': 'MaxNorm',\n      'minMaxNorm': 'MinMaxNorm',\n      'nonNeg': 'NonNeg',\n      'unitNorm': 'UnitNorm'\n    };\n\nexport function serializeConstraint(constraint: Constraint):\n    serialization.ConfigDictValue {\n  return serializeKerasObject(constraint);\n}\n\nexport function deserializeConstraint(\n    config: serialization.ConfigDict,\n    customObjects: serialization.ConfigDict = {}): Constraint {\n  return deserializeKerasObject(\n      config, serialization.SerializationMap.getMap().classNameMap,\n      customObjects, 'constraint');\n}\n\nexport function getConstraint(identifier: ConstraintIdentifier|\n                              serialization.ConfigDict|Constraint): Constraint {\n  if (identifier == null) {\n    return null;\n  }\n  if (typeof identifier === 'string') {\n    const className = identifier in CONSTRAINT_IDENTIFIER_REGISTRY_SYMBOL_MAP ?\n        CONSTRAINT_IDENTIFIER_REGISTRY_SYMBOL_MAP[identifier] :\n        identifier;\n    const config = {className, config: {}};\n    return deserializeConstraint(config);\n  } else if (identifier instanceof Constraint) {\n    return identifier;\n  } else {\n    return deserializeConstraint(identifier);\n  }\n}\n"], "mappings": "AAAA;;;;;;;;;AAUA;AAEA,OAAO,KAAKA,GAAG,MAAM,uBAAuB;AAC5C,SAAQC,aAAa,EAAUC,IAAI,QAAO,uBAAuB;AACjE,SAAQC,OAAO,QAAO,kBAAkB;AACxC,SAAQC,sBAAsB,EAAEC,oBAAoB,QAAO,uBAAuB;AAElF;;;AAGA,SAASC,WAAWA,CAACC,CAAS,EAAEC,IAAY;EAC1C,OAAON,IAAI,CAAC,MAAMF,GAAG,CAACS,IAAI,CAACT,GAAG,CAACU,GAAG,CAACV,GAAG,CAACW,GAAG,CAACJ,CAAC,EAAEA,CAAC,CAAC,EAAEC,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC;AACjE;AAEA;;;;;;;;;AASA,OAAM,MAAgBI,UAAW,SAAQX,aAAa,CAACY,YAAY;EAGjEC,SAASA,CAAA;IACP,OAAO,EAAE;EACX;;AAyBF,MAAaC,OAAQ,SAAQH,UAAU;EAQrCI,YAAYC,IAAiB;IAC3B,KAAK,EAAE;IAJQ,KAAAC,eAAe,GAAG,CAAC;IACnB,KAAAC,WAAW,GAAG,CAAC;IAI9B,IAAI,CAACC,QAAQ,GACTH,IAAI,CAACG,QAAQ,IAAI,IAAI,GAAGH,IAAI,CAACG,QAAQ,GAAG,IAAI,CAACF,eAAe;IAChE,IAAI,CAACV,IAAI,GAAGS,IAAI,CAACT,IAAI,IAAI,IAAI,GAAGS,IAAI,CAACT,IAAI,GAAG,IAAI,CAACW,WAAW;EAC9D;EAEAE,KAAKA,CAACd,CAAS;IACb,OAAOL,IAAI,CAAC,MAAK;MACf,MAAMoB,KAAK,GAAGhB,WAAW,CAACC,CAAC,EAAE,IAAI,CAACC,IAAI,CAAC;MACvC,MAAMe,OAAO,GAAGvB,GAAG,CAACwB,WAAW,CAACF,KAAK,EAAE,CAAC,EAAE,IAAI,CAACF,QAAQ,CAAC;MACxD,OAAOpB,GAAG,CAACW,GAAG,CAACJ,CAAC,EAAEP,GAAG,CAACyB,GAAG,CAACF,OAAO,EAAEvB,GAAG,CAAC0B,GAAG,CAACvB,OAAO,EAAE,EAAEmB,KAAK,CAAC,CAAC,CAAC;IAChE,CAAC,CAAC;EACJ;EAESR,SAASA,CAAA;IAChB,OAAO;MAACM,QAAQ,EAAE,IAAI,CAACA,QAAQ;MAAEZ,IAAI,EAAE,IAAI,CAACA;IAAI,CAAC;EACnD;;AAxBA;AACgBO,OAAA,CAAAY,SAAS,GAAG,SAAS;SAF1BZ,OAAO;AA2BpBd,aAAa,CAAC2B,aAAa,CAACb,OAAO,CAAC;AAoBpC,MAAac,QAAS,SAAQjB,UAAU;EAKtCI,YAAYC,IAAkB;IAC5B,KAAK,EAAE;IAFQ,KAAAE,WAAW,GAAG,CAAC;IAG9B,IAAI,CAACX,IAAI,GAAGS,IAAI,CAACT,IAAI,IAAI,IAAI,GAAGS,IAAI,CAACT,IAAI,GAAG,IAAI,CAACW,WAAW;EAC9D;EAEAE,KAAKA,CAACd,CAAS;IACb,OAAOL,IAAI,CACP,MAAMF,GAAG,CAACyB,GAAG,CAAClB,CAAC,EAAEP,GAAG,CAAC0B,GAAG,CAACvB,OAAO,EAAE,EAAEG,WAAW,CAACC,CAAC,EAAE,IAAI,CAACC,IAAI,CAAC,CAAC,CAAC,CAAC;EACtE;EAESM,SAASA,CAAA;IAChB,OAAO;MAACN,IAAI,EAAE,IAAI,CAACA;IAAI,CAAC;EAC1B;;AAhBA;AACgBqB,QAAA,CAAAF,SAAS,GAAG,UAAU;SAF3BE,QAAQ;AAmBrB5B,aAAa,CAAC2B,aAAa,CAACC,QAAQ,CAAC;AAErC,MAAaC,MAAO,SAAQlB,UAAU;EAIpCS,KAAKA,CAACd,CAAS;IACb,OAAOP,GAAG,CAAC+B,IAAI,CAACxB,CAAC,CAAC;EACpB;;AALA;AACgBuB,MAAA,CAAAH,SAAS,GAAG,QAAQ;SAFzBG,MAAM;AAQnB7B,aAAa,CAAC2B,aAAa,CAACE,MAAM,CAAC;AAoCnC,MAAaE,UAAW,SAAQpB,UAAU;EAYxCI,YAAYC,IAAoB;IAC9B,KAAK,EAAE;IANQ,KAAAgB,eAAe,GAAG,GAAG;IACrB,KAAAf,eAAe,GAAG,GAAG;IACrB,KAAAgB,WAAW,GAAG,GAAG;IACjB,KAAAf,WAAW,GAAG,CAAC;IAI9B,IAAI,CAACgB,QAAQ,GACTlB,IAAI,CAACkB,QAAQ,IAAI,IAAI,GAAGlB,IAAI,CAACkB,QAAQ,GAAG,IAAI,CAACF,eAAe;IAChE,IAAI,CAACb,QAAQ,GACTH,IAAI,CAACG,QAAQ,IAAI,IAAI,GAAGH,IAAI,CAACG,QAAQ,GAAG,IAAI,CAACF,eAAe;IAChE,IAAI,CAACkB,IAAI,GAAGnB,IAAI,CAACmB,IAAI,IAAI,IAAI,GAAGnB,IAAI,CAACmB,IAAI,GAAG,IAAI,CAACF,WAAW;IAC5D,IAAI,CAAC1B,IAAI,GAAGS,IAAI,CAACT,IAAI,IAAI,IAAI,GAAGS,IAAI,CAACT,IAAI,GAAG,IAAI,CAACW,WAAW;EAC9D;EAEAE,KAAKA,CAACd,CAAS;IACb,OAAOL,IAAI,CAAC,MAAK;MACf,MAAMoB,KAAK,GAAGhB,WAAW,CAACC,CAAC,EAAE,IAAI,CAACC,IAAI,CAAC;MACvC,MAAMe,OAAO,GAAGvB,GAAG,CAAC0B,GAAG,CACnB1B,GAAG,CAACW,GAAG,CACH,IAAI,CAACyB,IAAI,EAAEpC,GAAG,CAACwB,WAAW,CAACF,KAAK,EAAE,IAAI,CAACa,QAAQ,EAAE,IAAI,CAACf,QAAQ,CAAC,CAAC,EACpEpB,GAAG,CAACW,GAAG,CAAC,GAAG,GAAG,IAAI,CAACyB,IAAI,EAAEd,KAAK,CAAC,CAAC;MACpC,OAAOtB,GAAG,CAACW,GAAG,CAACJ,CAAC,EAAEP,GAAG,CAACyB,GAAG,CAACF,OAAO,EAAEvB,GAAG,CAAC0B,GAAG,CAACvB,OAAO,EAAE,EAAEmB,KAAK,CAAC,CAAC,CAAC;IAChE,CAAC,CAAC;EACJ;EAESR,SAASA,CAAA;IAChB,OAAO;MACLqB,QAAQ,EAAE,IAAI,CAACA,QAAQ;MACvBf,QAAQ,EAAE,IAAI,CAACA,QAAQ;MACvBgB,IAAI,EAAE,IAAI,CAACA,IAAI;MACf5B,IAAI,EAAE,IAAI,CAACA;KACZ;EACH;;AAvCA;AACgBwB,UAAA,CAAAL,SAAS,GAAG,YAAY;SAF7BK,UAAU;AA0CvB/B,aAAa,CAAC2B,aAAa,CAACI,UAAU,CAAC;AAMvC;AACA;AACA,OAAO,MAAMK,yCAAyC,GACD;EAC/C,SAAS,EAAE,SAAS;EACpB,YAAY,EAAE,YAAY;EAC1B,QAAQ,EAAE,QAAQ;EAClB,UAAU,EAAE;CACb;AAEL,OAAM,SAAUC,mBAAmBA,CAACC,UAAsB;EAExD,OAAOlC,oBAAoB,CAACkC,UAAU,CAAC;AACzC;AAEA,OAAM,SAAUC,qBAAqBA,CACjCC,MAAgC,EACY;EAAA,IAA5CC,aAAA,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAA0C,EAAE;EAC9C,OAAOvC,sBAAsB,CACzBqC,MAAM,EAAExC,aAAa,CAAC6C,gBAAgB,CAACC,MAAM,EAAE,CAACC,YAAY,EAC5DN,aAAa,EAAE,YAAY,CAAC;AAClC;AAEA,OAAM,SAAUO,aAAaA,CAACC,UACmC;EAC/D,IAAIA,UAAU,IAAI,IAAI,EAAE;IACtB,OAAO,IAAI;;EAEb,IAAI,OAAOA,UAAU,KAAK,QAAQ,EAAE;IAClC,MAAMvB,SAAS,GAAGuB,UAAU,IAAIb,yCAAyC,GACrEA,yCAAyC,CAACa,UAAU,CAAC,GACrDA,UAAU;IACd,MAAMT,MAAM,GAAG;MAACd,SAAS;MAAEc,MAAM,EAAE;IAAE,CAAC;IACtC,OAAOD,qBAAqB,CAACC,MAAM,CAAC;GACrC,MAAM,IAAIS,UAAU,YAAYtC,UAAU,EAAE;IAC3C,OAAOsC,UAAU;GAClB,MAAM;IACL,OAAOV,qBAAqB,CAACU,UAAU,CAAC;;AAE5C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}