{"ast": null, "code": "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { Slice } from '../kernel_names';\nimport { pad } from '../ops/pad';\nimport { parseSliceParams } from '../ops/slice_util';\nexport const sliceGradConfig = {\n  kernelName: Slice,\n  inputsToSave: ['x'],\n  gradFunc: (dy, saved, attrs) => {\n    const [x] = saved;\n    const {\n      begin,\n      size\n    } = attrs;\n    const inputShape = x.shape;\n    const [begin_, size_] = parseSliceParams(x, begin, size);\n    // Create an Nx2 padding where the first column represents how many\n    // zeros are prepended (at start) for each dimension, and the second\n    // column indicates how many zeros are appended (at end).\n    // The number of zeros to append is the shape of the input\n    // elementwise-subtracted by both the begin vector and sizes vector.\n    const paddings = [];\n    for (let i = 0; i < dy.rank; i++) {\n      paddings.push([begin_[i], inputShape[i] - begin_[i] - size_[i]]);\n    }\n    return {\n      x: () => pad(dy, paddings)\n    };\n  }\n};", "map": {"version": 3, "names": ["Slice", "pad", "parseSliceParams", "sliceGradConfig", "kernelName", "inputsToSave", "grad<PERSON>unc", "dy", "saved", "attrs", "x", "begin", "size", "inputShape", "shape", "begin_", "size_", "paddings", "i", "rank", "push"], "sources": ["C:\\tfjs-core\\src\\gradients\\Slice_grad.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {Slice, SliceAttrs} from '../kernel_names';\nimport {GradConfig, NamedAttrMap} from '../kernel_registry';\nimport {pad} from '../ops/pad';\nimport {parseSliceParams} from '../ops/slice_util';\nimport {Tensor} from '../tensor';\n\nexport const sliceGradConfig: GradConfig = {\n  kernelName: Slice,\n  inputsToSave: ['x'],\n  gradFunc: (dy: Tensor, saved: Tensor[], attrs: NamedAttrMap) => {\n    const [x] = saved;\n    const {begin, size} = attrs as unknown as SliceAttrs;\n\n    const inputShape = x.shape;\n    const [begin_, size_] = parseSliceParams(x, begin, size);\n\n    // Create an Nx2 padding where the first column represents how many\n    // zeros are prepended (at start) for each dimension, and the second\n    // column indicates how many zeros are appended (at end).\n\n    // The number of zeros to append is the shape of the input\n    // elementwise-subtracted by both the begin vector and sizes vector.\n    const paddings: Array<[number, number]> = [];\n    for (let i = 0; i < dy.rank; i++) {\n      paddings.push([begin_[i], inputShape[i] - begin_[i] - size_[i]]);\n    }\n    return {x: () => pad(dy, paddings)};\n  }\n};\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAiBA,SAAQA,KAAK,QAAmB,iBAAiB;AAEjD,SAAQC,GAAG,QAAO,YAAY;AAC9B,SAAQC,gBAAgB,QAAO,mBAAmB;AAGlD,OAAO,MAAMC,eAAe,GAAe;EACzCC,UAAU,EAAEJ,KAAK;EACjBK,YAAY,EAAE,CAAC,GAAG,CAAC;EACnBC,QAAQ,EAAEA,CAACC,EAAU,EAAEC,KAAe,EAAEC,KAAmB,KAAI;IAC7D,MAAM,CAACC,CAAC,CAAC,GAAGF,KAAK;IACjB,MAAM;MAACG,KAAK;MAAEC;IAAI,CAAC,GAAGH,KAA8B;IAEpD,MAAMI,UAAU,GAAGH,CAAC,CAACI,KAAK;IAC1B,MAAM,CAACC,MAAM,EAAEC,KAAK,CAAC,GAAGd,gBAAgB,CAACQ,CAAC,EAAEC,KAAK,EAAEC,IAAI,CAAC;IAExD;IACA;IACA;IAEA;IACA;IACA,MAAMK,QAAQ,GAA4B,EAAE;IAC5C,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGX,EAAE,CAACY,IAAI,EAAED,CAAC,EAAE,EAAE;MAChCD,QAAQ,CAACG,IAAI,CAAC,CAACL,MAAM,CAACG,CAAC,CAAC,EAAEL,UAAU,CAACK,CAAC,CAAC,GAAGH,MAAM,CAACG,CAAC,CAAC,GAAGF,KAAK,CAACE,CAAC,CAAC,CAAC,CAAC;;IAElE,OAAO;MAACR,CAAC,EAAEA,CAAA,KAAMT,GAAG,CAACM,EAAE,EAAEU,QAAQ;IAAC,CAAC;EACrC;CACD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}