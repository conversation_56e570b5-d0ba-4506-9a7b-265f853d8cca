{"ast": null, "code": "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { expandDims } from '../../ops/expand_dims';\nimport { getGlobalTensorClass } from '../../tensor';\ngetGlobalTensorClass().prototype.expandDims = function (axis) {\n  this.throwIfDisposed();\n  return expandDims(this, axis);\n};", "map": {"version": 3, "names": ["expandDims", "getGlobalTensorClass", "prototype", "axis", "throwIfDisposed"], "sources": ["C:\\tfjs-core\\src\\public\\chained_ops\\expand_dims.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport {expandDims} from '../../ops/expand_dims';\nimport {getGlobalTensorClass, Tensor} from '../../tensor';\nimport {Rank} from '../../types';\n\ndeclare module '../../tensor' {\n  interface Tensor<R extends Rank = Rank> {\n    expandDims<T extends Tensor>(axis?: number): T;\n  }\n}\n\ngetGlobalTensorClass().prototype.expandDims = function<T extends Tensor>(\n    axis?: number): T {\n  this.throwIfDisposed();\n  return expandDims(this, axis);\n};\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAgBA,SAAQA,UAAU,QAAO,uBAAuB;AAChD,SAAQC,oBAAoB,QAAe,cAAc;AASzDA,oBAAoB,EAAE,CAACC,SAAS,CAACF,UAAU,GAAG,UAC1CG,IAAa;EACf,IAAI,CAACC,eAAe,EAAE;EACtB,OAAOJ,UAAU,CAAC,IAAI,EAAEG,IAAI,CAAC;AAC/B,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}