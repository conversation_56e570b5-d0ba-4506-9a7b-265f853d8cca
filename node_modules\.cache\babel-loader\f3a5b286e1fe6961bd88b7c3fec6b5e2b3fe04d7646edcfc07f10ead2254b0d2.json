{"ast": null, "code": "/**\n * @license\n * Copyright 2023 Google LLC.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { op } from './operation';\nimport { randomUniform } from './random_uniform';\n/**\n * Creates a `tf.Tensor` with integers sampled from a uniform distribution.\n *\n * The generated values are uniform integers in the range [minval, maxval). The\n * lower bound minval is included in the range, while the upper bound maxval is\n * excluded.\n *\n * ```js\n * tf.randomUniformInt([2, 2], 0, 10).print();\n * ```\n *\n * @param shape An array of integers defining the output tensor shape.\n * @param minval Inclusive lower bound on the generated integers.\n * @param maxval Exclusive upper bound on the generated integers.\n * @param seed An optional int. Defaults to 0. If seed is set to be non-zero,\n *   the random number generator is seeded by the given seed. Otherwise, it is\n *   seeded by a random seed.\n *\n * @doc {heading: 'Tensors', subheading: 'Random'}\n */\nfunction randomUniformInt_(shape, minval, maxval, seed) {\n  // TODO(mattsoulanille): Handle optional seed2 input.\n  return randomUniform(shape, minval, maxval, 'int32', seed);\n}\nexport const randomUniformInt = /* @__PURE__ */op({\n  randomUniformInt_\n});", "map": {"version": 3, "names": ["op", "randomUniform", "randomUniformInt_", "shape", "min<PERSON>", "maxval", "seed", "randomUniformInt"], "sources": ["C:\\tfjs-core\\src\\ops\\random_uniform_int.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2023 Google LLC.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {Tensor} from '../tensor';\nimport {Rank, ShapeMap} from '../types';\nimport {op} from './operation';\nimport {randomUniform} from './random_uniform';\n\n/**\n * Creates a `tf.Tensor` with integers sampled from a uniform distribution.\n *\n * The generated values are uniform integers in the range [minval, maxval). The\n * lower bound minval is included in the range, while the upper bound maxval is\n * excluded.\n *\n * ```js\n * tf.randomUniformInt([2, 2], 0, 10).print();\n * ```\n *\n * @param shape An array of integers defining the output tensor shape.\n * @param minval Inclusive lower bound on the generated integers.\n * @param maxval Exclusive upper bound on the generated integers.\n * @param seed An optional int. Defaults to 0. If seed is set to be non-zero,\n *   the random number generator is seeded by the given seed. Otherwise, it is\n *   seeded by a random seed.\n *\n * @doc {heading: 'Tensors', subheading: 'Random'}\n */\nfunction randomUniformInt_<R extends Rank>(\n  shape: ShapeMap[R], minval: number, maxval: number,\n    seed?: number|string): Tensor<R> {\n  // TODO(mattsoulanille): Handle optional seed2 input.\n  return randomUniform(shape, minval, maxval, 'int32', seed);\n}\n\nexport const randomUniformInt = /* @__PURE__ */ op({randomUniformInt_});\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAmBA,SAAQA,EAAE,QAAO,aAAa;AAC9B,SAAQC,aAAa,QAAO,kBAAkB;AAE9C;;;;;;;;;;;;;;;;;;;;AAoBA,SAASC,iBAAiBA,CACxBC,KAAkB,EAAEC,MAAc,EAAEC,MAAc,EAChDC,IAAoB;EACtB;EACA,OAAOL,aAAa,CAACE,KAAK,EAAEC,MAAM,EAAEC,MAAM,EAAE,OAAO,EAAEC,IAAI,CAAC;AAC5D;AAEA,OAAO,MAAMC,gBAAgB,GAAG,eAAgBP,EAAE,CAAC;EAACE;AAAiB,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}