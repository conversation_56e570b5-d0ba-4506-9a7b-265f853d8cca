{"ast": null, "code": "'use strict';\n\nconst stopwords = require('../util/stopwords_uk');\nconst Tokenizer = require('../tokenizers/aggressive_tokenizer_uk');\nmodule.exports = function () {\n  const stemmer = this;\n  stemmer.stem = function (token) {\n    return token;\n  };\n  stemmer.tokenizeAndStem = function (text, keepStops) {\n    const stemmedTokens = [];\n    new Tokenizer().tokenize(text).forEach(function (token) {\n      if (keepStops || stopwords.words.indexOf(token) === -1) {\n        let resultToken = token.toLowerCase();\n        if (resultToken.match(/[а-яґєії0-9]+/gi)) {\n          resultToken = stemmer.stem(resultToken);\n        }\n        stemmedTokens.push(resultToken);\n      }\n    });\n    return stemmedTokens;\n  };\n};", "map": {"version": 3, "names": ["stopwords", "require", "Tokenizer", "module", "exports", "stemmer", "stem", "token", "tokenizeAndStem", "text", "keepStops", "stemmedTokens", "tokenize", "for<PERSON>ach", "words", "indexOf", "resultToken", "toLowerCase", "match", "push"], "sources": ["C:/tmsft/node_modules/natural/lib/natural/stemmers/stemmer_uk.js"], "sourcesContent": ["'use strict'\n\nconst stopwords = require('../util/stopwords_uk')\nconst Tokenizer = require('../tokenizers/aggressive_tokenizer_uk')\n\nmodule.exports = function () {\n  const stemmer = this\n\n  stemmer.stem = function (token) {\n    return token\n  }\n\n  stemmer.tokenizeAndStem = function (text, keepStops) {\n    const stemmedTokens = []\n\n    new Tokenizer().tokenize(text).forEach(function (token) {\n      if (keepStops || stopwords.words.indexOf(token) === -1) {\n        let resultToken = token.toLowerCase()\n        if (resultToken.match(/[а-яґєії0-9]+/gi)) {\n          resultToken = stemmer.stem(resultToken)\n        }\n        stemmedTokens.push(resultToken)\n      }\n    })\n\n    return stemmedTokens\n  }\n}\n"], "mappings": "AAAA,YAAY;;AAEZ,MAAMA,SAAS,GAAGC,OAAO,CAAC,sBAAsB,CAAC;AACjD,MAAMC,SAAS,GAAGD,OAAO,CAAC,uCAAuC,CAAC;AAElEE,MAAM,CAACC,OAAO,GAAG,YAAY;EAC3B,MAAMC,OAAO,GAAG,IAAI;EAEpBA,OAAO,CAACC,IAAI,GAAG,UAAUC,KAAK,EAAE;IAC9B,OAAOA,KAAK;EACd,CAAC;EAEDF,OAAO,CAACG,eAAe,GAAG,UAAUC,IAAI,EAAEC,SAAS,EAAE;IACnD,MAAMC,aAAa,GAAG,EAAE;IAExB,IAAIT,SAAS,CAAC,CAAC,CAACU,QAAQ,CAACH,IAAI,CAAC,CAACI,OAAO,CAAC,UAAUN,KAAK,EAAE;MACtD,IAAIG,SAAS,IAAIV,SAAS,CAACc,KAAK,CAACC,OAAO,CAACR,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE;QACtD,IAAIS,WAAW,GAAGT,KAAK,CAACU,WAAW,CAAC,CAAC;QACrC,IAAID,WAAW,CAACE,KAAK,CAAC,iBAAiB,CAAC,EAAE;UACxCF,WAAW,GAAGX,OAAO,CAACC,IAAI,CAACU,WAAW,CAAC;QACzC;QACAL,aAAa,CAACQ,IAAI,CAACH,WAAW,CAAC;MACjC;IACF,CAAC,CAAC;IAEF,OAAOL,aAAa;EACtB,CAAC;AACH,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}