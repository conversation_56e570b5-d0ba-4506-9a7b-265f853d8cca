{"ast": null, "code": "/*\nCopyright (c) 2011, 2018 <PERSON>, <PERSON>, <PERSON>t\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in\nall copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\nTHE SOFTWARE.\n*/\n\n'use strict';\n\nconst _ = require('underscore');\nconst Tokenizer = require('../tokenizers/regexp_tokenizer').WordTokenizer;\nlet tokenizer = new Tokenizer();\nlet frequencies = {};\nlet nrOfNgrams = 0;\nexports.setTokenizer = function (t) {\n  if (!_.isFunction(t.tokenize)) {\n    throw new Error('Expected a valid Tokenizer');\n  }\n  tokenizer = t;\n};\nexports.ngrams = function (sequence, n, startSymbol, endSymbol, stats) {\n  return ngrams(sequence, n, startSymbol, endSymbol, stats);\n};\nexports.bigrams = function (sequence, startSymbol, endSymbol, stats) {\n  return ngrams(sequence, 2, startSymbol, endSymbol, stats);\n};\nexports.trigrams = function (sequence, startSymbol, endSymbol, stats) {\n  return ngrams(sequence, 3, startSymbol, endSymbol, stats);\n};\nexports.multrigrams = function (sequence, n, startSymbol, endSymbol, stats) {\n  return ngrams(sequence, n, startSymbol, endSymbol, stats);\n};\n\n// Calculates a key (string) that can be used for a map\nfunction arrayToKey(arr) {\n  let result = '(';\n  arr.forEach(function (x) {\n    result += x + ', ';\n  });\n  result = result.substr(0, result.length - 2) + ')';\n  return result;\n}\n;\n\n// Updates the statistics for the new ngram\nfunction countNgrams(ngram) {\n  nrOfNgrams++;\n  const key = arrayToKey(ngram);\n  if (!frequencies[key]) {\n    frequencies[key] = 0;\n  }\n  frequencies[key]++;\n}\n\n// If stats is true, statistics will be returned\nconst ngrams = function (sequence, n, startSymbol, endSymbol, stats) {\n  const result = [];\n  frequencies = {};\n  nrOfNgrams = 0;\n  if (!_.isArray(sequence)) {\n    sequence = tokenizer.tokenize(sequence);\n  }\n  const count = _.max([0, sequence.length - n + 1]);\n\n  // Check for left padding\n  if (typeof startSymbol !== 'undefined' && startSymbol !== null) {\n    // Create an array of (n) start symbols\n    const blanks = [];\n    for (let i = 0; i < n; i++) {\n      blanks.push(startSymbol);\n    }\n\n    // Create the left padding\n    for (let p = n - 1; p > 0; p--) {\n      // Create a tuple of (p) start symbols and (n - p) words\n      const ngram = blanks.slice(0, p).concat(sequence.slice(0, n - p));\n      result.push(ngram);\n      if (stats) {\n        countNgrams(ngram);\n      }\n    }\n  }\n\n  // Build the complete ngrams\n  for (let i = 0; i < count; i++) {\n    const ngram = sequence.slice(i, i + n);\n    result.push(ngram);\n    if (stats) {\n      countNgrams(ngram);\n    }\n  }\n\n  // Check for right padding\n  if (typeof endSymbol !== 'undefined' && endSymbol !== null) {\n    // Create an array of (n) end symbols\n    const blanks = [];\n    for (let i = 0; i < n; i++) {\n      blanks.push(endSymbol);\n    }\n\n    // create the right padding\n    for (let p = n - 1; p > 0; p--) {\n      // Create a tuple of (p) start symbols and (n - p) words\n      const ngram = sequence.slice(sequence.length - p, sequence.length).concat(blanks.slice(0, n - p));\n      result.push(ngram);\n      if (stats) {\n        countNgrams(ngram);\n      }\n    }\n  }\n  if (stats) {\n    // Count frequencies\n    const Nr = {};\n    Object.keys(frequencies).forEach(function (key) {\n      if (!Nr[frequencies[key]]) {\n        Nr[frequencies[key]] = 0;\n      }\n      Nr[frequencies[key]]++;\n    });\n\n    // Return the ngrams AND statistics\n    return {\n      ngrams: result,\n      frequencies: frequencies,\n      Nr: Nr,\n      numberOfNgrams: nrOfNgrams\n    };\n  } else {\n    // Do not break existing API of this module\n    return result;\n  }\n};", "map": {"version": 3, "names": ["_", "require", "Tokenizer", "WordTokenizer", "tokenizer", "frequencies", "nrOfNgrams", "exports", "setTokenizer", "t", "isFunction", "tokenize", "Error", "ngrams", "sequence", "n", "startSymbol", "endSymbol", "stats", "bigrams", "trigrams", "multrigrams", "arrayToKey", "arr", "result", "for<PERSON>ach", "x", "substr", "length", "countNgrams", "ngram", "key", "isArray", "count", "max", "blanks", "i", "push", "p", "slice", "concat", "Nr", "Object", "keys", "numberOfNgrams"], "sources": ["C:/tmsft/node_modules/natural/lib/natural/ngrams/ngrams.js"], "sourcesContent": ["/*\nCopyright (c) 2011, 2018 <PERSON>, <PERSON>, <PERSON>t\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in\nall copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\nTHE SOFTWARE.\n*/\n\n'use strict'\n\nconst _ = require('underscore')\nconst Tokenizer = require('../tokenizers/regexp_tokenizer').WordTokenizer\nlet tokenizer = new Tokenizer()\nlet frequencies = {}\nlet nrOfNgrams = 0\n\nexports.setTokenizer = function (t) {\n  if (!_.isFunction(t.tokenize)) { throw new Error('Expected a valid Tokenizer') }\n  tokenizer = t\n}\n\nexports.ngrams = function (sequence, n, startSymbol, endSymbol, stats) {\n  return ngrams(sequence, n, startSymbol, endSymbol, stats)\n}\n\nexports.bigrams = function (sequence, startSymbol, endSymbol, stats) {\n  return ngrams(sequence, 2, startSymbol, endSymbol, stats)\n}\n\nexports.trigrams = function (sequence, startSymbol, endSymbol, stats) {\n  return ngrams(sequence, 3, startSymbol, endSymbol, stats)\n}\n\nexports.multrigrams = function (sequence, n, startSymbol, endSymbol, stats) {\n  return ngrams(sequence, n, startSymbol, endSymbol, stats)\n}\n\n// Calculates a key (string) that can be used for a map\nfunction arrayToKey (arr) {\n  let result = '('\n  arr.forEach(function (x) {\n    result += x + ', '\n  })\n  result = result.substr(0, result.length - 2) + ')'\n  return result\n};\n\n// Updates the statistics for the new ngram\nfunction countNgrams (ngram) {\n  nrOfNgrams++\n  const key = arrayToKey(ngram)\n  if (!frequencies[key]) {\n    frequencies[key] = 0\n  }\n  frequencies[key]++\n}\n\n// If stats is true, statistics will be returned\nconst ngrams = function (sequence, n, startSymbol, endSymbol, stats) {\n  const result = []\n  frequencies = {}\n  nrOfNgrams = 0\n\n  if (!_.isArray(sequence)) {\n    sequence = tokenizer.tokenize(sequence)\n  }\n\n  const count = _.max([0, sequence.length - n + 1])\n\n  // Check for left padding\n  if (typeof startSymbol !== 'undefined' && startSymbol !== null) {\n    // Create an array of (n) start symbols\n    const blanks = []\n    for (let i = 0; i < n; i++) {\n      blanks.push(startSymbol)\n    }\n\n    // Create the left padding\n    for (let p = n - 1; p > 0; p--) {\n      // Create a tuple of (p) start symbols and (n - p) words\n      const ngram = blanks.slice(0, p).concat(sequence.slice(0, n - p))\n      result.push(ngram)\n      if (stats) {\n        countNgrams(ngram)\n      }\n    }\n  }\n\n  // Build the complete ngrams\n  for (let i = 0; i < count; i++) {\n    const ngram = sequence.slice(i, i + n)\n    result.push(ngram)\n    if (stats) {\n      countNgrams(ngram)\n    }\n  }\n\n  // Check for right padding\n  if (typeof endSymbol !== 'undefined' && endSymbol !== null) {\n    // Create an array of (n) end symbols\n    const blanks = []\n    for (let i = 0; i < n; i++) {\n      blanks.push(endSymbol)\n    }\n\n    // create the right padding\n    for (let p = n - 1; p > 0; p--) {\n      // Create a tuple of (p) start symbols and (n - p) words\n      const ngram = sequence.slice(sequence.length - p, sequence.length).concat(blanks.slice(0, n - p))\n      result.push(ngram)\n      if (stats) {\n        countNgrams(ngram)\n      }\n    }\n  }\n\n  if (stats) {\n    // Count frequencies\n    const Nr = {}\n    Object.keys(frequencies).forEach(function (key) {\n      if (!Nr[frequencies[key]]) {\n        Nr[frequencies[key]] = 0\n      }\n      Nr[frequencies[key]]++\n    })\n\n    // Return the ngrams AND statistics\n    return {\n      ngrams: result,\n      frequencies: frequencies,\n      Nr: Nr,\n      numberOfNgrams: nrOfNgrams\n    }\n  } else { // Do not break existing API of this module\n    return result\n  }\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,YAAY;;AAEZ,MAAMA,CAAC,GAAGC,OAAO,CAAC,YAAY,CAAC;AAC/B,MAAMC,SAAS,GAAGD,OAAO,CAAC,gCAAgC,CAAC,CAACE,aAAa;AACzE,IAAIC,SAAS,GAAG,IAAIF,SAAS,CAAC,CAAC;AAC/B,IAAIG,WAAW,GAAG,CAAC,CAAC;AACpB,IAAIC,UAAU,GAAG,CAAC;AAElBC,OAAO,CAACC,YAAY,GAAG,UAAUC,CAAC,EAAE;EAClC,IAAI,CAACT,CAAC,CAACU,UAAU,CAACD,CAAC,CAACE,QAAQ,CAAC,EAAE;IAAE,MAAM,IAAIC,KAAK,CAAC,4BAA4B,CAAC;EAAC;EAC/ER,SAAS,GAAGK,CAAC;AACf,CAAC;AAEDF,OAAO,CAACM,MAAM,GAAG,UAAUC,QAAQ,EAAEC,CAAC,EAAEC,WAAW,EAAEC,SAAS,EAAEC,KAAK,EAAE;EACrE,OAAOL,MAAM,CAACC,QAAQ,EAAEC,CAAC,EAAEC,WAAW,EAAEC,SAAS,EAAEC,KAAK,CAAC;AAC3D,CAAC;AAEDX,OAAO,CAACY,OAAO,GAAG,UAAUL,QAAQ,EAAEE,WAAW,EAAEC,SAAS,EAAEC,KAAK,EAAE;EACnE,OAAOL,MAAM,CAACC,QAAQ,EAAE,CAAC,EAAEE,WAAW,EAAEC,SAAS,EAAEC,KAAK,CAAC;AAC3D,CAAC;AAEDX,OAAO,CAACa,QAAQ,GAAG,UAAUN,QAAQ,EAAEE,WAAW,EAAEC,SAAS,EAAEC,KAAK,EAAE;EACpE,OAAOL,MAAM,CAACC,QAAQ,EAAE,CAAC,EAAEE,WAAW,EAAEC,SAAS,EAAEC,KAAK,CAAC;AAC3D,CAAC;AAEDX,OAAO,CAACc,WAAW,GAAG,UAAUP,QAAQ,EAAEC,CAAC,EAAEC,WAAW,EAAEC,SAAS,EAAEC,KAAK,EAAE;EAC1E,OAAOL,MAAM,CAACC,QAAQ,EAAEC,CAAC,EAAEC,WAAW,EAAEC,SAAS,EAAEC,KAAK,CAAC;AAC3D,CAAC;;AAED;AACA,SAASI,UAAUA,CAAEC,GAAG,EAAE;EACxB,IAAIC,MAAM,GAAG,GAAG;EAChBD,GAAG,CAACE,OAAO,CAAC,UAAUC,CAAC,EAAE;IACvBF,MAAM,IAAIE,CAAC,GAAG,IAAI;EACpB,CAAC,CAAC;EACFF,MAAM,GAAGA,MAAM,CAACG,MAAM,CAAC,CAAC,EAAEH,MAAM,CAACI,MAAM,GAAG,CAAC,CAAC,GAAG,GAAG;EAClD,OAAOJ,MAAM;AACf;AAAC;;AAED;AACA,SAASK,WAAWA,CAAEC,KAAK,EAAE;EAC3BxB,UAAU,EAAE;EACZ,MAAMyB,GAAG,GAAGT,UAAU,CAACQ,KAAK,CAAC;EAC7B,IAAI,CAACzB,WAAW,CAAC0B,GAAG,CAAC,EAAE;IACrB1B,WAAW,CAAC0B,GAAG,CAAC,GAAG,CAAC;EACtB;EACA1B,WAAW,CAAC0B,GAAG,CAAC,EAAE;AACpB;;AAEA;AACA,MAAMlB,MAAM,GAAG,SAAAA,CAAUC,QAAQ,EAAEC,CAAC,EAAEC,WAAW,EAAEC,SAAS,EAAEC,KAAK,EAAE;EACnE,MAAMM,MAAM,GAAG,EAAE;EACjBnB,WAAW,GAAG,CAAC,CAAC;EAChBC,UAAU,GAAG,CAAC;EAEd,IAAI,CAACN,CAAC,CAACgC,OAAO,CAAClB,QAAQ,CAAC,EAAE;IACxBA,QAAQ,GAAGV,SAAS,CAACO,QAAQ,CAACG,QAAQ,CAAC;EACzC;EAEA,MAAMmB,KAAK,GAAGjC,CAAC,CAACkC,GAAG,CAAC,CAAC,CAAC,EAAEpB,QAAQ,CAACc,MAAM,GAAGb,CAAC,GAAG,CAAC,CAAC,CAAC;;EAEjD;EACA,IAAI,OAAOC,WAAW,KAAK,WAAW,IAAIA,WAAW,KAAK,IAAI,EAAE;IAC9D;IACA,MAAMmB,MAAM,GAAG,EAAE;IACjB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGrB,CAAC,EAAEqB,CAAC,EAAE,EAAE;MAC1BD,MAAM,CAACE,IAAI,CAACrB,WAAW,CAAC;IAC1B;;IAEA;IACA,KAAK,IAAIsB,CAAC,GAAGvB,CAAC,GAAG,CAAC,EAAEuB,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAE,EAAE;MAC9B;MACA,MAAMR,KAAK,GAAGK,MAAM,CAACI,KAAK,CAAC,CAAC,EAAED,CAAC,CAAC,CAACE,MAAM,CAAC1B,QAAQ,CAACyB,KAAK,CAAC,CAAC,EAAExB,CAAC,GAAGuB,CAAC,CAAC,CAAC;MACjEd,MAAM,CAACa,IAAI,CAACP,KAAK,CAAC;MAClB,IAAIZ,KAAK,EAAE;QACTW,WAAW,CAACC,KAAK,CAAC;MACpB;IACF;EACF;;EAEA;EACA,KAAK,IAAIM,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,KAAK,EAAEG,CAAC,EAAE,EAAE;IAC9B,MAAMN,KAAK,GAAGhB,QAAQ,CAACyB,KAAK,CAACH,CAAC,EAAEA,CAAC,GAAGrB,CAAC,CAAC;IACtCS,MAAM,CAACa,IAAI,CAACP,KAAK,CAAC;IAClB,IAAIZ,KAAK,EAAE;MACTW,WAAW,CAACC,KAAK,CAAC;IACpB;EACF;;EAEA;EACA,IAAI,OAAOb,SAAS,KAAK,WAAW,IAAIA,SAAS,KAAK,IAAI,EAAE;IAC1D;IACA,MAAMkB,MAAM,GAAG,EAAE;IACjB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGrB,CAAC,EAAEqB,CAAC,EAAE,EAAE;MAC1BD,MAAM,CAACE,IAAI,CAACpB,SAAS,CAAC;IACxB;;IAEA;IACA,KAAK,IAAIqB,CAAC,GAAGvB,CAAC,GAAG,CAAC,EAAEuB,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAE,EAAE;MAC9B;MACA,MAAMR,KAAK,GAAGhB,QAAQ,CAACyB,KAAK,CAACzB,QAAQ,CAACc,MAAM,GAAGU,CAAC,EAAExB,QAAQ,CAACc,MAAM,CAAC,CAACY,MAAM,CAACL,MAAM,CAACI,KAAK,CAAC,CAAC,EAAExB,CAAC,GAAGuB,CAAC,CAAC,CAAC;MACjGd,MAAM,CAACa,IAAI,CAACP,KAAK,CAAC;MAClB,IAAIZ,KAAK,EAAE;QACTW,WAAW,CAACC,KAAK,CAAC;MACpB;IACF;EACF;EAEA,IAAIZ,KAAK,EAAE;IACT;IACA,MAAMuB,EAAE,GAAG,CAAC,CAAC;IACbC,MAAM,CAACC,IAAI,CAACtC,WAAW,CAAC,CAACoB,OAAO,CAAC,UAAUM,GAAG,EAAE;MAC9C,IAAI,CAACU,EAAE,CAACpC,WAAW,CAAC0B,GAAG,CAAC,CAAC,EAAE;QACzBU,EAAE,CAACpC,WAAW,CAAC0B,GAAG,CAAC,CAAC,GAAG,CAAC;MAC1B;MACAU,EAAE,CAACpC,WAAW,CAAC0B,GAAG,CAAC,CAAC,EAAE;IACxB,CAAC,CAAC;;IAEF;IACA,OAAO;MACLlB,MAAM,EAAEW,MAAM;MACdnB,WAAW,EAAEA,WAAW;MACxBoC,EAAE,EAAEA,EAAE;MACNG,cAAc,EAAEtC;IAClB,CAAC;EACH,CAAC,MAAM;IAAE;IACP,OAAOkB,MAAM;EACf;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}