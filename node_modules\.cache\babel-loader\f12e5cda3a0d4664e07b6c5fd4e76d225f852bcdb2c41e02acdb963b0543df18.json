{"ast": null, "code": "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { AddN } from '../kernel_names';\nexport const addNGradConfig = {\n  kernelName: AddN,\n  saveAllInputs: true,\n  gradFunc: (dy, saved) => {\n    const ders = {};\n    saved.forEach((_, i) => {\n      ders[i] = () => dy.clone();\n    });\n    return ders;\n  }\n};", "map": {"version": 3, "names": ["AddN", "addNGradConfig", "kernelName", "saveAllInputs", "grad<PERSON>unc", "dy", "saved", "ders", "for<PERSON>ach", "_", "i", "clone"], "sources": ["C:\\tfjs-core\\src\\gradients\\AddN_grad.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {AddN} from '../kernel_names';\nimport {GradConfig} from '../kernel_registry';\nimport {Tensor} from '../tensor';\n\nexport const addNGradConfig: GradConfig = {\n  kernelName: AddN,\n  saveAllInputs: true,\n  gradFunc: (dy: Tensor, saved: Tensor[]) => {\n    const ders: {[key: string]: () => Tensor} = {};\n    saved.forEach((_, i) => {\n      ders[i] = () => dy.clone();\n    });\n    return ders;\n  }\n};\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAiBA,SAAQA,IAAI,QAAO,iBAAiB;AAIpC,OAAO,MAAMC,cAAc,GAAe;EACxCC,UAAU,EAAEF,IAAI;EAChBG,aAAa,EAAE,IAAI;EACnBC,QAAQ,EAAEA,CAACC,EAAU,EAAEC,KAAe,KAAI;IACxC,MAAMC,IAAI,GAAkC,EAAE;IAC9CD,KAAK,CAACE,OAAO,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAI;MACrBH,IAAI,CAACG,CAAC,CAAC,GAAG,MAAML,EAAE,CAACM,KAAK,EAAE;IAC5B,CAAC,CAAC;IACF,OAAOJ,IAAI;EACb;CACD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}