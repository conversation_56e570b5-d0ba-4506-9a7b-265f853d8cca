{"ast": null, "code": "import filter from './filter.js';\nimport matcher from './matcher.js';\n\n// Convenience version of a common use case of `_.filter`: selecting only\n// objects containing specific `key:value` pairs.\nexport default function where(obj, attrs) {\n  return filter(obj, matcher(attrs));\n}", "map": {"version": 3, "names": ["filter", "matcher", "where", "obj", "attrs"], "sources": ["C:/tmsft/node_modules/underscore/modules/where.js"], "sourcesContent": ["import filter from './filter.js';\nimport matcher from './matcher.js';\n\n// Convenience version of a common use case of `_.filter`: selecting only\n// objects containing specific `key:value` pairs.\nexport default function where(obj, attrs) {\n  return filter(obj, matcher(attrs));\n}\n"], "mappings": "AAAA,OAAOA,MAAM,MAAM,aAAa;AAChC,OAAOC,OAAO,MAAM,cAAc;;AAElC;AACA;AACA,eAAe,SAASC,KAAKA,CAACC,GAAG,EAAEC,KAAK,EAAE;EACxC,OAAOJ,MAAM,CAACG,GAAG,EAAEF,OAAO,CAACG,KAAK,CAAC,CAAC;AACpC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}