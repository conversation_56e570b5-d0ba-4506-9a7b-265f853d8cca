{"ast": null, "code": "/**\n * @license\n * Copyright 2018 Google LLC\n *\n * Use of this source code is governed by an MIT-style\n * license that can be found in the LICENSE file or at\n * https://opensource.org/licenses/MIT.\n * =============================================================================\n */\n/**\n * TensorFlow.js Layers: Merge Layers.\n */\nimport * as tfc from '@tensorflow/tfjs-core';\nimport { serialization, tidy, util } from '@tensorflow/tfjs-core';\nimport * as K from '../backend/tfjs_backend';\nimport { Layer } from '../engine/topology';\nimport { NotImplementedError, ValueError } from '../errors';\nimport { l2Normalize } from '../losses';\nimport * as generic_utils from '../utils/generic_utils';\nimport * as mathUtils from '../utils/math_utils';\nimport { getExactlyOneShape } from '../utils/types_utils';\n/**\n * Generic Merge layer for element-wise merge functions.\n *\n * Used to implement `Sum`, `Average`, `Concatenate`, etc.\n */\nexport class Merge extends Layer {\n  constructor(args) {\n    super(args || {});\n    this.supportsMasking = true;\n  }\n  /**\n   * Logic for merging multiple tensors, to be overridden by subclasses.\n   * @param inputs\n   */\n  mergeFunction(inputs) {\n    throw new NotImplementedError();\n  }\n  /**\n   * Computes the shape of the result of an elementwise operation.\n   *\n   * @param shape1: Shape of the first tensor.\n   * @param shape2: Shape of the second tensor.\n   * @returns Expected output shape when an elementwise operation is carried\n   *   out on 2 tensors with shapes `shape1` and `shape2`.\n   * @throws ValueError: If `shape1` and `shape2` are not compatible for\n   *   element-wise operations.\n   */\n  computeElementwiseOpOutputShape(shape1, shape2) {\n    if (shape1 == null || shape2 == null) {\n      return null;\n    } else if (shape1.length < shape2.length) {\n      return this.computeElementwiseOpOutputShape(shape2, shape1);\n    } else if (shape2.length === 0) {\n      return shape1;\n    }\n    const outputShape = shape1.slice(0, shape1.length - shape2.length);\n    for (let k = 0; k < shape2.length; ++k) {\n      const i = shape1[shape1.length - shape2.length + k];\n      const j = shape2[k];\n      if (i == null || j == null || i < 0 || j < 0) {\n        outputShape.push(null);\n      } else if (i === 1) {\n        outputShape.push(j);\n      } else if (j === 1) {\n        outputShape.push(i);\n      } else {\n        if (i !== j) {\n          throw new ValueError('Operands could not be broadcast together with shapes ' + JSON.stringify(shape1) + ' ' + JSON.stringify(shape2));\n        }\n        outputShape.push(i);\n      }\n    }\n    return outputShape;\n  }\n  build(inputShape) {\n    // Used purely for shape validation.\n    if (Array.isArray(inputShape) && !Array.isArray(inputShape[0])) {\n      // Make sure that inputShape is an Array of shape.\n      inputShape = [getExactlyOneShape(inputShape)];\n    }\n    inputShape = inputShape;\n    if (inputShape.length < 2) {\n      throw new ValueError('A merge layer should be called on an Array of at least 2 inputs.' + ` Got ${inputShape.length} input(s).`);\n    }\n    // Make sure that there is at most one unique batch size among the input\n    // shapes.\n    let batchSizes = [];\n    for (const shape of inputShape) {\n      if (shape != null && shape[0] !== null) {\n        batchSizes.push(shape[0]);\n      }\n    }\n    batchSizes = generic_utils.unique(batchSizes);\n    if (batchSizes.length > 1) {\n      throw new ValueError(`Can not merge tensors with different batch sizes. ` + `Got tensors with shapes: ${JSON.stringify(inputShape)}.`);\n    }\n    let outputShape = inputShape[0] == null ? null : inputShape[0].slice(1);\n    for (let i = 1; i < inputShape.length; ++i) {\n      const shape = inputShape[i] == null ? null : inputShape[i].slice(1);\n      outputShape = this.computeElementwiseOpOutputShape(outputShape, shape);\n    }\n    // If the inputs have different ranks, we have to reshape them to make them\n    // broadcastable.\n    const allRanks = inputShape.map(shape => shape.length);\n    if (inputShape.indexOf(null) === -1 && generic_utils.unique(allRanks).length === 1) {\n      this.reshapeRequired = false;\n    } else {\n      this.reshapeRequired = true;\n    }\n  }\n  call(inputs, kwargs) {\n    return tidy(() => {\n      inputs = inputs;\n      if (this.reshapeRequired) {\n        const reshapedInputs = [];\n        const inputDims = inputs.map(input => input.rank);\n        if (inputDims.indexOf(null) === -1) {\n          // If ranks of all inputs are available, we simply expand each of them\n          // at axis=1 until all of them have the same rank.\n          const maxNDim = mathUtils.max(inputDims);\n          for (let x of inputs) {\n            const xNDim = x.rank;\n            for (let k = 0; k < maxNDim - xNDim; ++k) {\n              x = K.expandDims(x, 1);\n            }\n            reshapedInputs.push(x);\n          }\n          return this.mergeFunction(reshapedInputs);\n        } else {\n          // Transpose all inputs so that batch size is the last dimension.\n          // [batchSize, dim1, dim2, ...] -> [dim1, dim2, ..., batchSize]\n          let transposed = false;\n          for (const x of inputs) {\n            const xNDim = x.rank;\n            if (xNDim == null) {\n              const xShape = x.shape;\n              const batchSize = xShape[0];\n              const newShape = xShape.slice(1).concat([batchSize]);\n              let xTransposed = tfc.reshape(x, [batchSize].concat(mathUtils.arrayProd(xShape.slice(1))));\n              xTransposed = tfc.transpose(xTransposed, [1, 0]);\n              xTransposed = tfc.reshape(xTransposed, newShape);\n              reshapedInputs.push(xTransposed);\n              transposed = true;\n            } else if (xNDim > 1) {\n              const dims = mathUtils.range(1, xNDim).concat([0]);\n              reshapedInputs.push(tfc.transpose(x, dims));\n              transposed = true;\n            } else {\n              // We don't transpose inputs if they are 1D vectors or scalars.\n              reshapedInputs.push(x);\n            }\n          }\n          let y = this.mergeFunction(reshapedInputs);\n          const yNDim = y.rank;\n          if (transposed) {\n            // If inputs have been transposed, we have to transpose the output\n            // too.\n            if (yNDim == null) {\n              const yShape = y.shape;\n              const yNDim = yShape.length;\n              const batchSize = yShape[yNDim - 1];\n              const newShape = [batchSize].concat(yShape.slice(0, yShape.length - 1));\n              y = tfc.reshape(tfc.transpose(tfc.reshape(y, [-1, batchSize]), [1, 0]), newShape);\n            } else if (yNDim > 1) {\n              const dims = [yNDim - 1].concat(mathUtils.range(0, yNDim - 1));\n              y = tfc.transpose(y, dims);\n            }\n          }\n          return y;\n        }\n      } else {\n        return this.mergeFunction(inputs);\n      }\n    });\n  }\n  computeOutputShape(inputShape) {\n    inputShape = inputShape;\n    let outputShape;\n    if (inputShape[0] == null) {\n      outputShape = null;\n    } else {\n      outputShape = inputShape[0].slice(1);\n    }\n    for (let i = 1; i < inputShape.length; ++i) {\n      const shape = inputShape[i] == null ? null : inputShape[i].slice(1);\n      outputShape = this.computeElementwiseOpOutputShape(outputShape, shape);\n    }\n    let batchSizes = [];\n    for (const shape of inputShape) {\n      if (shape != null && shape[0] !== null) {\n        batchSizes.push(shape[0]);\n      }\n    }\n    batchSizes = generic_utils.unique(batchSizes);\n    if (batchSizes.length === 1) {\n      outputShape = batchSizes.concat(outputShape);\n    } else {\n      outputShape = [null].concat(outputShape);\n    }\n    return outputShape;\n  }\n  computeMask(inputs, mask) {\n    return tfc.tidy(() => {\n      if (mask == null) {\n        return null;\n      }\n      if (!Array.isArray(mask)) {\n        throw new ValueError('`mask` should be an Array');\n      }\n      if (!Array.isArray(inputs)) {\n        throw new ValueError('`inputs` should be an Array');\n      }\n      if (mask.length !== inputs.length) {\n        throw new ValueError(`The Array 'inputs' and 'mask' are expected to have the same ` + `length, but have different lengths ` + `(${inputs.length} vs ${mask.length})`);\n      }\n      if (mask.every(m => m == null)) {\n        return null;\n      }\n      mask = mask.map(m => m == null ? m : tfc.expandDims(m, 0));\n      let output = mask[0];\n      for (let i = 1; i < mask.length - 1; ++i) {\n        output = tfc.logicalAnd(output, mask[i]);\n      }\n      return output;\n    });\n  }\n}\nclass Add extends Merge {\n  constructor(args) {\n    super(args);\n  }\n  mergeFunction(inputs) {\n    return tidy(() => {\n      let output = inputs[0].clone();\n      for (let i = 1; i < inputs.length; ++i) {\n        output = tfc.add(output, inputs[i]);\n      }\n      return output;\n    });\n  }\n}\n/** @nocollapse */\nAdd.className = 'Add';\nexport { Add };\nserialization.registerClass(Add);\n/**\n * Calculate the element-wise sum of inputs, which all have the same shape.\n *\n * This function can be invoked in three ways.\n *\n * 1. Construct an instance of `Add` layer, by using no input argument\n *    or a single configuration argument. The resultant `Add` layer can then\n *    be used on `tf.SymbolicTensor`s or `tf.Tensor`s. For example:\n *\n * ```js\n * const addLayer = tf.layers.add();\n *\n * // The layer can be applied to inputs.\n * const input1 = tf.input({shape: [2, 2]});\n * const input2 = tf.input({shape: [2, 2]});\n * const output = addLayer.apply([input1, input2]);\n * console.log(output.shape);\n * // You get [null, 2, 2], with the first dimension as the undetermined batch\n * // dimension.\n * ```\n *\n * 2. Invoke directly on an `Array` of `tf.SymbolicTensor`s. This constructs\n *    an `Layer` object internally and calls its `apply` method on the inputs,\n *    generating a new `tf.SymbolicTensor`. For example:\n *\n * ```js\n * const input1 = tf.input({shape: [2, 2]});\n * const input2 = tf.input({shape: [2, 2]});\n * const output = tf.layers.add([input1, input2]);\n * console.log(output.shape);\n * // You get [null, 2, 2], with the first dimension as the undetermined batch\n * // dimension.\n * ```\n *\n * 3. Invoke directly on `tf.Tensor`s, i.e., concrete values. This constructs\n *    an `Layer` object internally and calls its `apply` method on the inputs,\n *    generating a new `tf.Tensor` as the result of the computation. For\n * example:\n *\n * ```js\n * const input1 = tf.tensor2d([1, 2, 3, 4], [2, 2]);\n * const input2 = tf.tensor2d([10, 20, 30, 40], [2, 2]);\n * tf.layers.add([input1, input2]).print();\n * // Gives [[11, 22], [33, 44]].\n *\n */\nexport function add(config) {\n  if (Array.isArray(config)) {\n    const layer = new Add({});\n    return layer.apply(config);\n  } else {\n    return new Add(config);\n  }\n}\nclass Multiply extends Merge {\n  constructor(args) {\n    super(args);\n  }\n  mergeFunction(inputs) {\n    return tidy(() => {\n      let output = inputs[0].clone();\n      for (let i = 1; i < inputs.length; ++i) {\n        output = tfc.mul(output, inputs[i]);\n      }\n      return output;\n    });\n  }\n}\n/** @nocollapse */\nMultiply.className = 'Multiply';\nexport { Multiply };\nserialization.registerClass(Multiply);\n/**\n * Calculate the element-wise product of inputs, which all have the same shape.\n *\n * This function can be invoked in three ways.\n *\n * 1. Construct an instance of `Multiply` layer, by using no input argument\n *    or a single configuration argument. The resultant `Multiply` layer can\n *    then be used on `tf.SymbolicTensor`s or `tf.Tensor`s. For example:\n *\n * ```js\n * const multiplyLayer = tf.layers.multiply();\n *\n * // The layer can be applied to inputs.\n * const input1 = tf.input({shape: [2, 2]});\n * const input2 = tf.input({shape: [2, 2]});\n * const output = multiplyLayer.apply([input1, input2]);\n * console.log(output.shape);\n * // You get [null, 2, 2], with the first dimension as the undetermined batch\n * // dimension.\n * ```\n *\n * 2. Invoke directly on an `Array` of `tf.SymbolicTensor`s. This constructs\n *    an `Layer` object internally and calls its `apply` method on the inputs,\n *    generating a new `tf.SymbolicTensor`. For example:\n *\n * ```js\n * const input1 = tf.input({shape: [2, 2]});\n * const input2 = tf.input({shape: [2, 2]});\n * const output = tf.layers.multiply([input1, input2]);\n * console.log(output.shape);\n * // You get [null, 2, 2], with the first dimension as the undetermined batch\n * // dimension.\n * ```\n *\n * 3. Invoke directly on `tf.Tensor`s, i.e., concrete values. This constructs\n *    an `Layer` object internally and calls its `apply` method on the inputs,\n *    generating a new `tf.Tensor` as the result of the computation. For\n * example:\n *\n * ```js\n * const input1 = tf.tensor2d([1, 2, 3, 4], [2, 2]);\n * const input2 = tf.tensor2d([10, 20, 30, 40], [2, 2]);\n * tf.layers.multiply([input1, input2]).print();\n * // Gives [[10, 40], [90, 160]].\n *\n */\nexport function multiply(config) {\n  if (Array.isArray(config)) {\n    const layer = new Multiply({});\n    return layer.apply(config);\n  } else {\n    return new Multiply(config);\n  }\n}\nclass Average extends Merge {\n  constructor(args) {\n    super(args);\n  }\n  mergeFunction(inputs) {\n    return tidy(() => {\n      let output = inputs[0].clone();\n      for (let i = 1; i < inputs.length; ++i) {\n        output = tfc.add(output, inputs[i]);\n      }\n      return tfc.mul(1 / inputs.length, output);\n    });\n  }\n}\n/** @nocollapse */\nAverage.className = 'Average';\nexport { Average };\nserialization.registerClass(Average);\n/**\n * Calculate the element-wise arithmetic mean of inputs, which all have the same\n * shape.\n *\n * This function can be invoked in three ways.\n *\n * 1. Construct an instance of `Average` layer, by using no input argument\n *    or a single configuration argument. The resultant `Average` layer can then\n *    be used on `tf.SymbolicTensor`s or `tf.Tensor`s. For example:\n *\n * ```js\n * const averageLayer = tf.layers.average();\n *\n * // The layer can be applied to inputs.\n * const input1 = tf.input({shape: [2, 2]});\n * const input2 = tf.input({shape: [2, 2]});\n * const output = averageLayer.apply([input1, input2]);\n * console.log(output.shape);\n * // You get [null, 2, 2], with the first dimension as the undetermined batch\n * // dimension.\n * ```\n *\n * 2. Invoke directly on an `Array` of `tf.SymbolicTensor`s. This constructs\n *    an `Layer` object internally and calls its `apply` method on the inputs,\n *    generating a new `tf.SymbolicTensor`. For example:\n *\n * ```js\n * const input1 = tf.input({shape: [2, 2]});\n * const input2 = tf.input({shape: [2, 2]});\n * const output = tf.layers.average([input1, input2]);\n * console.log(output.shape);\n * // You get [null, 2, 2], with the first dimension as the undetermined batch\n * // dimension.\n * ```\n *\n * 3. Invoke directly on `tf.Tensor`s, i.e., concrete values. This constructs\n *    an `Layer` object internally and calls its `apply` method on the inputs,\n *    generating a new `tf.Tensor` as the result of the computation. For\n * example:\n *\n * ```js\n * const input1 = tf.tensor2d([1, 2, 3, 4], [2, 2]);\n * const input2 = tf.tensor2d([10, 20, 30, 40], [2, 2]);\n * tf.layers.average([input1, input2]).print();\n * // Gives [[5.5, 11], [16.5, 22]].\n *\n */\nexport function average(config) {\n  if (Array.isArray(config)) {\n    const layer = new Average({});\n    return layer.apply(config);\n  } else {\n    return new Average(config);\n  }\n}\nclass Maximum extends Merge {\n  constructor(args) {\n    super(args);\n  }\n  mergeFunction(inputs) {\n    return tidy(() => {\n      let output = inputs[0];\n      for (let i = 1; i < inputs.length; ++i) {\n        output = tfc.maximum(output, inputs[i]);\n      }\n      return output;\n    });\n  }\n}\n/** @nocollapse */\nMaximum.className = 'Maximum';\nexport { Maximum };\nserialization.registerClass(Maximum);\n/**\n * Calculate the element-wise maximum of inputs, which all have the same shape.\n *\n * This function can be invoked in three ways.\n *\n * 1. Construct an instance of `Maximum` layer, by using no input argument\n *    or a single configuration argument. The resultant `Maximum` layer can then\n *    be used on `tf.SymbolicTensor`s or `tf.Tensor`s. For example:\n *\n * ```js\n * const maximumLayer = tf.layers.maximum();\n *\n * // The layer can be applied to inputs.\n * const input1 = tf.input({shape: [2, 2]});\n * const input2 = tf.input({shape: [2, 2]});\n * const output = maximumLayer.apply([input1, input2]);\n * console.log(output.shape);\n * // You get [null, 2, 2], with the first dimension as the undetermined batch\n * // dimension.\n * ```\n *\n * 2. Invoke directly on an `Array` of `tf.SymbolicTensor`s. This constructs\n *    an `Layer` object internally and calls its `apply` method on the inputs,\n *    generating a new `tf.SymbolicTensor`. For example:\n *\n * ```js\n * const input1 = tf.input({shape: [2, 2]});\n * const input2 = tf.input({shape: [2, 2]});\n * const output = tf.layers.maximum([input1, input2]);\n * console.log(output.shape);\n * // You get [null, 2, 2], with the first dimension as the undetermined batch\n * // dimension.\n * ```\n *\n * 3. Invoke directly on `tf.Tensor`s, i.e., concrete values. This constructs\n *    an `Layer` object internally and calls its `apply` method on the inputs,\n *    generating a new `tf.Tensor` as the result of the computation. For\n * example:\n *\n * ```js\n * const input1 = tf.tensor2d([1, 20, 3, 40], [2, 2]);\n * const input2 = tf.tensor2d([10, 2, 30, 4], [2, 2]);\n * tf.layers.maximum([input1, input2]).print();\n * // Gives [[10, 20], [30, 40]].\n *\n */\nexport function maximum(config) {\n  if (Array.isArray(config)) {\n    const layer = new Maximum({});\n    return layer.apply(config);\n  } else {\n    return new Maximum(config);\n  }\n}\nclass Minimum extends Merge {\n  constructor(args) {\n    super(args);\n  }\n  mergeFunction(inputs) {\n    return tidy(() => {\n      let output = inputs[0];\n      for (let i = 1; i < inputs.length; ++i) {\n        output = tfc.minimum(output, inputs[i]);\n      }\n      return output;\n    });\n  }\n}\n/** @nocollapse */\nMinimum.className = 'Minimum';\nexport { Minimum };\nserialization.registerClass(Minimum);\n/**\n * Calculate the element-wise minimum of inputs, which all have the same shape.\n *\n * This function can be invoked in three ways.\n *\n * 1. Construct an instance of `Minimum` layer, by using no input argument\n *    or a single configuration argument. The resultant `Minimum` layer can then\n *    be used on `tf.SymbolicTensor`s or `tf.Tensor`s. For example:\n *\n * ```js\n * const minimumLayer = tf.layers.minimum();\n *\n * // The layer can be applied to inputs.\n * const input1 = tf.input({shape: [2, 2]});\n * const input2 = tf.input({shape: [2, 2]});\n * const output = minimumLayer.apply([input1, input2]);\n * console.log(output.shape);\n * // You get [null, 2, 2], with the first dimension as the undetermined batch\n * // dimension.\n * ```\n *\n * 2. Invoke directly on an `Array` of `tf.SymbolicTensor`s. This constructs\n *    an `Layer` object internally and calls its `apply` method on the inputs,\n *    generating a new `tf.SymbolicTensor`. For example:\n *\n * ```js\n * const input1 = tf.input({shape: [2, 2]});\n * const input2 = tf.input({shape: [2, 2]});\n * const output = tf.layers.minimum([input1, input2]);\n * console.log(output.shape);\n * // You get [null, 2, 2], with the first dimension as the undetermined batch\n * // dimension.\n * ```\n *\n * 3. Invoke directly on `tf.Tensor`s, i.e., concrete values. This constructs\n *    an `Layer` object internally and calls its `apply` method on the inputs,\n *    generating a new `tf.Tensor` as the result of the computation. For\n * example:\n *\n * ```js\n * const input1 = tf.tensor2d([1, 20, 3, 40], [2, 2]);\n * const input2 = tf.tensor2d([10, 2, 30, 4], [2, 2]);\n * tf.layers.minimum([input1, input2]).print();\n * // Gives [[1, 2], [3, 4]].\n *\n */\nexport function minimum(config) {\n  if (Array.isArray(config)) {\n    const layer = new Minimum({});\n    return layer.apply(config);\n  } else {\n    return new Minimum(config);\n  }\n}\nclass Concatenate extends Merge {\n  constructor(args) {\n    super(args);\n    this.DEFAULT_AXIS = -1;\n    if (args == null) {\n      args = {};\n    }\n    this.axis = args.axis == null ? this.DEFAULT_AXIS : args.axis;\n    this.supportsMasking = true;\n    this.reshapeRequired = false;\n  }\n  build(inputShape) {\n    // Used purely for shape validation.]\n    if (!(Array.isArray(inputShape) && Array.isArray(inputShape[0])) || inputShape.length === 1) {\n      throw new ValueError('A `Concatenate` layer should be called on a list of at least 2 ' + 'inputs');\n    }\n    inputShape = inputShape;\n    let allNoneShape = true;\n    for (const shape of inputShape) {\n      if (shape != null) {\n        allNoneShape = false;\n        break;\n      }\n    }\n    if (allNoneShape) {\n      return;\n    }\n    const shapeSet = [];\n    for (let i = 0; i < inputShape.length; ++i) {\n      const shapeWithoutConcatAxis = inputShape[i].slice();\n      shapeWithoutConcatAxis.splice(this.axis, 1);\n      let exists = false;\n      for (const shape of shapeSet) {\n        if (util.arraysEqual(shape, shapeWithoutConcatAxis)) {\n          exists = true;\n          break;\n        }\n      }\n      if (!exists) {\n        shapeSet.push(shapeWithoutConcatAxis);\n      }\n    }\n    if (shapeSet.length > 1) {\n      throw new ValueError('A `Concatenate` layer requires inputs with matching shapes ' + 'except for the concat axis. Got input shapes: ' + JSON.stringify(inputShape));\n    }\n  }\n  mergeFunction(inputs) {\n    return tidy(() => {\n      return K.concatenate(inputs, this.axis);\n    });\n  }\n  computeOutputShape(inputShape) {\n    if (!(Array.isArray(inputShape) && Array.isArray(inputShape[0]))) {\n      throw new ValueError('A `Concatenate` layer should be called on a list of inputs.');\n    }\n    const inputShapes = inputShape;\n    const outputShape = inputShapes[0].slice();\n    const axis = this.axis < 0 ? outputShape.length + this.axis : this.axis;\n    // Porting Note: the line above is because TypeScript doesn't support\n    //   negative indices.\n    for (const shape of inputShapes.slice(1)) {\n      if (outputShape[axis] == null || shape[axis] == null) {\n        outputShape[axis] = null;\n        break;\n      }\n      outputShape[axis] += shape[axis];\n    }\n    return outputShape;\n  }\n  computeMask(inputs, mask) {\n    if (mask == null) {\n      return null;\n    }\n    if (!Array.isArray(mask)) {\n      throw new ValueError('`mask` should be an array for Concatenate');\n    }\n    if (!Array.isArray(inputs)) {\n      throw new ValueError('`inputs` should be an array for Concatenate');\n    }\n    if (mask.length !== inputs.length) {\n      throw new ValueError(`Mismatch in the length of mask (${mask.length}) ` + `and the legnth of inputs (${inputs.length})`);\n    }\n    return tfc.tidy(() => {\n      let allNullMasks = true;\n      mask.forEach(m => {\n        if (m != null) {\n          allNullMasks = false;\n          return;\n        }\n      });\n      if (allNullMasks) {\n        return null;\n      }\n      const outputMasks = [];\n      for (let i = 0; i < inputs.length; ++i) {\n        if (mask[i] == null) {\n          // Input is unmasked. Append all 1's to masks.\n          outputMasks.push(tfc.cast(tfc.onesLike(inputs[i]), 'bool'));\n        } else if (mask[i].rank < inputs[i].rank) {\n          // Mask is smaller than the input, expand it.\n          outputMasks.push(tfc.expandDims(mask[i], -1));\n        } else {\n          outputMasks.push(mask[i]);\n        }\n      }\n      const concatenatedMasks = tfc.concat(outputMasks, this.axis);\n      return tfc.all(concatenatedMasks, -1, false);\n    });\n  }\n  getConfig() {\n    const config = {\n      'axis': this.axis\n    };\n    const baseConfig = super.getConfig();\n    Object.assign(config, baseConfig);\n    return config;\n  }\n}\n/** @nocollapse */\nConcatenate.className = 'Concatenate';\nexport { Concatenate };\nserialization.registerClass(Concatenate);\n/**\n * Concatenate an `Array` of inputs.\n *\n * This function can be invoked in three ways.\n *\n * 1. Construct an instance of `Concatenate` layer, by using no input argument\n *    or a single configuration argument. The resultant `Concatenate` layer can\n *    then be used on `tf.SymbolicTensor`s or `tf.Tensor`s. For example:\n *\n * ```js\n * const concatLayer = tf.layers.concatenate();\n *\n * // The layer can be applied to inputs.\n * const input1 = tf.input({shape: [2, 3]});\n * const input2 = tf.input({shape: [2, 4]});\n * const output = concatLayer.apply([input1, input2]);\n * console.log(output.shape);\n * // You get [null, 2, 7], with the first dimension as the undetermined batch\n * // dimension and the last dimension as the result of concatenating the\n * // last dimensions of the two inputs.\n * ```\n *\n * 2. Invoke directly on an `Array` of `tf.SymbolicTensor`s. This constructs\n *    an `Layer` object internally and calls its `apply` method on the inputs,\n *    generating a new `tf.SymbolicTensor`. For example:\n *\n * ```js\n * const input1 = tf.input({shape: [2, 3]});\n * const input2 = tf.input({shape: [2, 4]});\n * const output = tf.layers.concatenate([input1, input2]);\n * console.log(output.shape);\n * // You get [null, 2, 2], with the first dimension as the undetermined batch\n * // dimension and the last dimension as the result of concatenating the\n * // last dimensions of the two inputs.\n * ```\n *\n * 3. Invoke directly on `tf.Tensor`s, i.e., concrete values. This constructs\n *    an `Layer` object internally and calls its `apply` method on the inputs,\n *    generating a new `tf.Tensor` as the result of the computation. For\n * example:\n *\n * ```js\n * const input1 = tf.tensor2d([[1, 2], [3, 4]], [2, 2]);\n * const input2 = tf.tensor2d([[10, 20], [30, 40]], [2, 2]);\n * tf.layers.concatenate([input1, input2]).print();\n * // Gives [[1, 2, 10, 20], [3, 4, 30, 40]].\n *\n */\nexport function concatenate(config) {\n  if (Array.isArray(config)) {\n    const layer = new Concatenate({});\n    return layer.apply(config);\n  } else {\n    return new Concatenate(config);\n  }\n}\n/**\n * Interpretable potentially negative axis index.\n *\n * For example, given axis = -1, and dim = 3, this function will return 2.\n *\n * @param axis The axis index, may be a positive, zero or negative integer.\n * @param dim Total number of dimensions, a positive integer.\n * @returns A non-negative axis index equivalent to the input `axis`.\n */\nfunction interpretAxis(axis, dim) {\n  while (axis < 0) {\n    axis += dim;\n  }\n  return axis;\n}\nfunction batchDot(x, y, axes) {\n  if (x.shape.length > 3 || y.shape.length > 3) {\n    throw new NotImplementedError('batchDot is not implemented for tensors of 4D or higher rank yet');\n  }\n  tfc.util.assert(x.shape.length >= 2, () => `batchDot requires the rank of x to be >= 2, ` + `but got ${x.shape.length}`);\n  tfc.util.assert(x.shape.length >= 2, () => `batchDot requires the rank of y to be >= 2, ` + `but got ${y.shape.length}`);\n  if (typeof axes === 'number') {\n    axes = [axes, axes];\n  }\n  if (x.dtype === 'complex64' || y.dtype === 'complex64') {\n    throw new NotImplementedError('batchDot is not implemented for complex64-type Tensors yet.');\n  }\n  const xNDim = x.shape.length;\n  const yNDim = y.shape.length;\n  if (axes == null) {\n    // Behave like batchMatmul by default.\n    axes = [xNDim - 1, yNDim - 2];\n  }\n  const axesArray = axes;\n  return tfc.tidy(() => {\n    let diff;\n    if (xNDim > yNDim) {\n      diff = xNDim - yNDim;\n      const diffShape = [];\n      for (let i = 0; i < diff; ++i) {\n        diffShape.push(1);\n      }\n      y = tfc.reshape(y, y.shape.concat(diffShape));\n    } else if (yNDim > xNDim) {\n      diff = yNDim - xNDim;\n      const diffShape = [];\n      for (let i = 0; i < diff; ++i) {\n        diffShape.push(1);\n      }\n      x = tfc.reshape(x, x.shape.concat(diffShape));\n    } else {\n      diff = 0;\n    }\n    let out;\n    if (x.shape.length === 2 && y.shape.length === 2) {\n      if (axesArray[0] === axesArray[1]) {\n        out = tfc.sum(tfc.mul(x, y), axesArray[0]);\n      } else {\n        out = tfc.sum(tfc.mul(tfc.transpose(x, [1, 0]), y), axesArray[1]);\n      }\n    } else {\n      const adjX = axesArray[0] !== x.shape.length - 1;\n      const adjY = axesArray[1] === y.shape.length - 1;\n      out = tfc.matMul(x, y, adjX, adjY);\n    }\n    if (diff > 0) {\n      let idx;\n      if (xNDim > yNDim) {\n        idx = xNDim + yNDim - 3;\n      } else {\n        idx = xNDim - 1;\n      }\n      const squeezeAxes = [];\n      for (let i = idx; i < idx + diff; ++i) {\n        squeezeAxes.push(i);\n      }\n      out = tfc.squeeze(out, squeezeAxes);\n    }\n    if (out.shape.length === 1) {\n      out = tfc.expandDims(out, 1);\n    }\n    return out;\n  });\n}\nclass Dot extends Merge {\n  constructor(args) {\n    super(args);\n    this.axes = args.axes;\n    this.normalize = args.normalize == null ? false : args.normalize;\n    this.supportsMasking = true;\n    this.reshapeRequired = false;\n  }\n  build(inputShape) {\n    tfc.util.assert(Array.isArray(inputShape) && inputShape.length === 2 && Array.isArray(inputShape[0]) && Array.isArray(inputShape[1]), () => 'A `Dot` layer should be called on a list of exactly 2 inputs.');\n    const shape1 = inputShape[0];\n    const shape2 = inputShape[1];\n    if (shape1.length > 3 || shape2.length > 3) {\n      throw new NotImplementedError('Dot layer does not support tensors of 4D or higher rank yet.');\n    }\n    const axes = this.interpretAxes(shape1, shape2);\n    if (shape1[axes[0]] !== shape2[axes[1]]) {\n      throw new ValueError(`Dimension incompatibility: ` + `${shape1[axes[0]]} !== ${shape2[axes[1]]}`);\n    }\n  }\n  mergeFunction(inputs) {\n    if (inputs.length !== 2) {\n      throw new ValueError('A `Dot` layer must be called on exactly 2 inputs, ' + `but received ${inputs.length} input(s).`);\n    }\n    let x1 = inputs[0];\n    let x2 = inputs[1];\n    let axes;\n    if (!Array.isArray(this.axes)) {\n      axes = [interpretAxis(this.axes, x1.shape.length), interpretAxis(this.axes, x2.shape.length)];\n    } else {\n      axes = this.axes.map((axis, i) => interpretAxis(axis, inputs[i].shape.length));\n    }\n    if (this.normalize) {\n      x1 = l2Normalize(x1, axes[0]);\n      x2 = l2Normalize(x2, axes[1]);\n    }\n    return batchDot(x1, x2, axes);\n  }\n  interpretAxes(shape1, shape2) {\n    let axes;\n    if (!Array.isArray(this.axes)) {\n      // `this.axes` is a single integer.\n      axes = [interpretAxis(this.axes, shape1.length), interpretAxis(this.axes, shape2.length)];\n    } else {\n      // `this.axes` is an Array of integers.\n      axes = this.axes;\n    }\n    return axes;\n  }\n  computeOutputShape(inputShape) {\n    tfc.util.assert(Array.isArray(inputShape) && inputShape.length === 2 && Array.isArray(inputShape[0]) && Array.isArray(inputShape[1]), () => 'A `Dot` layer should be called on a list of exactly 2 inputs.');\n    const shape1 = inputShape[0].slice();\n    const shape2 = inputShape[1].slice();\n    if (shape1.length > 3 || shape2.length > 3) {\n      throw new NotImplementedError('Dot layer does not support tensors of 4D or higher rank yet.');\n    }\n    const axes = this.interpretAxes(shape1, shape2);\n    shape1.splice(axes[0], 1);\n    shape2.splice(axes[1], 1);\n    shape2.splice(0, 1);\n    const outputShape = shape1.concat(shape2);\n    if (outputShape.length === 1) {\n      outputShape.push(1);\n    }\n    return outputShape;\n  }\n  computeMask(inputs, mask) {\n    return null;\n  }\n  getConfig() {\n    const config = {\n      'axes': this.axes,\n      'normalize': this.normalize\n    };\n    const baseConfig = super.getConfig();\n    Object.assign(config, baseConfig);\n    return config;\n  }\n}\n/** @nocollapse */\nDot.className = 'Dot';\nexport { Dot };\nserialization.registerClass(Dot);\n// TODO(cais): Add functional interfaces for the merge layers.", "map": {"version": 3, "names": ["tfc", "serialization", "tidy", "util", "K", "Layer", "NotImplementedError", "ValueError", "l2Normalize", "generic_utils", "mathUtils", "getExactlyOneShape", "<PERSON><PERSON>", "constructor", "args", "supportsMasking", "mergeFunction", "inputs", "computeElementwiseOpOutputShape", "shape1", "shape2", "length", "outputShape", "slice", "k", "i", "j", "push", "JSON", "stringify", "build", "inputShape", "Array", "isArray", "batchSizes", "shape", "unique", "allRanks", "map", "indexOf", "reshapeRequired", "call", "kwargs", "reshapedInputs", "inputDims", "input", "rank", "max<PERSON><PERSON>", "max", "x", "xNDim", "expandDims", "transposed", "xShape", "batchSize", "newShape", "concat", "xTransposed", "reshape", "arrayProd", "transpose", "dims", "range", "y", "y<PERSON><PERSON>", "yShape", "computeOutputShape", "computeMask", "mask", "every", "m", "output", "logicalAnd", "Add", "clone", "add", "className", "registerClass", "config", "layer", "apply", "Multiply", "mul", "multiply", "Average", "average", "Maximum", "maximum", "Minimum", "minimum", "Concatenate", "DEFAULT_AXIS", "axis", "allNoneShape", "shapeSet", "shapeWithoutConcatAxis", "splice", "exists", "arraysEqual", "concatenate", "inputShapes", "allNullMasks", "for<PERSON>ach", "outputMasks", "cast", "onesLike", "concatenatedMasks", "all", "getConfig", "baseConfig", "Object", "assign", "interpretAxis", "dim", "batchDot", "axes", "assert", "dtype", "axesArray", "diff", "diffShape", "out", "sum", "adjX", "adjY", "<PERSON><PERSON><PERSON>", "idx", "squeezeAxes", "squeeze", "Dot", "normalize", "interpretAxes", "x1", "x2"], "sources": ["C:\\tfjs-layers\\src\\layers\\merge.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2018 Google LLC\n *\n * Use of this source code is governed by an MIT-style\n * license that can be found in the LICENSE file or at\n * https://opensource.org/licenses/MIT.\n * =============================================================================\n */\n\n/**\n * TensorFlow.js Layers: Merge Layers.\n */\n\nimport * as tfc from '@tensorflow/tfjs-core';\nimport {serialization, Tensor, tidy, util} from '@tensorflow/tfjs-core';\nimport * as K from '../backend/tfjs_backend';\nimport {Layer, LayerArgs, SymbolicTensor} from '../engine/topology';\nimport {NotImplementedError, ValueError} from '../errors';\nimport {Shape} from '../keras_format/common';\nimport {l2Normalize} from '../losses';\nimport {Kwargs} from '../types';\nimport * as generic_utils from '../utils/generic_utils';\nimport * as mathUtils from '../utils/math_utils';\nimport {getExactlyOneShape} from '../utils/types_utils';\n\n/**\n * Generic Merge layer for element-wise merge functions.\n *\n * Used to implement `Sum`, `Average`, `Concatenate`, etc.\n */\nexport abstract class Merge extends Layer {\n  protected reshapeRequired: boolean;\n\n  constructor(args?: LayerArgs) {\n    super(args || {});\n    this.supportsMasking = true;\n  }\n\n  /**\n   * Logic for merging multiple tensors, to be overridden by subclasses.\n   * @param inputs\n   */\n  protected mergeFunction(inputs: Tensor[]): Tensor {\n    throw new NotImplementedError();\n  }\n\n  /**\n   * Computes the shape of the result of an elementwise operation.\n   *\n   * @param shape1: Shape of the first tensor.\n   * @param shape2: Shape of the second tensor.\n   * @returns Expected output shape when an elementwise operation is carried\n   *   out on 2 tensors with shapes `shape1` and `shape2`.\n   * @throws ValueError: If `shape1` and `shape2` are not compatible for\n   *   element-wise operations.\n   */\n  private computeElementwiseOpOutputShape(shape1: Shape, shape2: Shape): Shape {\n    if (shape1 == null || shape2 == null) {\n      return null;\n    } else if (shape1.length < shape2.length) {\n      return this.computeElementwiseOpOutputShape(shape2, shape1);\n    } else if (shape2.length === 0) {\n      return shape1;\n    }\n    const outputShape: Shape = shape1.slice(0, shape1.length - shape2.length);\n    for (let k = 0; k < shape2.length; ++k) {\n      const i = shape1[shape1.length - shape2.length + k];\n      const j = shape2[k];\n      if (i == null || j == null || i < 0 || j < 0) {\n        outputShape.push(null);\n      } else if (i === 1) {\n        outputShape.push(j);\n      } else if (j === 1) {\n        outputShape.push(i);\n      } else {\n        if (i !== j) {\n          throw new ValueError(\n              'Operands could not be broadcast together with shapes ' +\n              JSON.stringify(shape1) + ' ' + JSON.stringify(shape2));\n        }\n        outputShape.push(i);\n      }\n    }\n    return outputShape;\n  }\n\n  override build(inputShape: Shape|Shape[]): void {\n    // Used purely for shape validation.\n    if (Array.isArray(inputShape) && !Array.isArray(inputShape[0])) {\n      // Make sure that inputShape is an Array of shape.\n      inputShape = [getExactlyOneShape(inputShape)];\n    }\n    inputShape = inputShape as Shape[];\n    if (inputShape.length < 2) {\n      throw new ValueError(\n          'A merge layer should be called on an Array of at least 2 inputs.' +\n          ` Got ${inputShape.length} input(s).`);\n    }\n\n    // Make sure that there is at most one unique batch size among the input\n    // shapes.\n    let batchSizes: number[] = [];\n    for (const shape of inputShape) {\n      if (shape != null && shape[0] !== null) {\n        batchSizes.push(shape[0]);\n      }\n    }\n    batchSizes = generic_utils.unique(batchSizes);\n    if (batchSizes.length > 1) {\n      throw new ValueError(\n          `Can not merge tensors with different batch sizes. ` +\n          `Got tensors with shapes: ${JSON.stringify(inputShape)}.`);\n    }\n\n    let outputShape: Shape =\n        inputShape[0] == null ? null : inputShape[0].slice(1);\n    for (let i = 1; i < inputShape.length; ++i) {\n      const shape = inputShape[i] == null ? null : inputShape[i].slice(1);\n      outputShape = this.computeElementwiseOpOutputShape(outputShape, shape);\n    }\n    // If the inputs have different ranks, we have to reshape them to make them\n    // broadcastable.\n    const allRanks = inputShape.map(shape => shape.length);\n    if (inputShape.indexOf(null) === -1 &&\n        generic_utils.unique(allRanks).length === 1) {\n      this.reshapeRequired = false;\n    } else {\n      this.reshapeRequired = true;\n    }\n  }\n\n  override call(inputs: Tensor|Tensor[], kwargs: Kwargs): Tensor|Tensor[] {\n    return tidy(() => {\n      inputs = inputs as Tensor[];\n      if (this.reshapeRequired) {\n        const reshapedInputs: Tensor[] = [];\n        const inputDims = inputs.map(input => input.rank);\n        if (inputDims.indexOf(null) === -1) {\n          // If ranks of all inputs are available, we simply expand each of them\n          // at axis=1 until all of them have the same rank.\n          const maxNDim = mathUtils.max(inputDims);\n          for (let x of inputs) {\n            const xNDim = x.rank;\n            for (let k = 0; k < maxNDim - xNDim; ++k) {\n              x = K.expandDims(x, 1);\n            }\n            reshapedInputs.push(x);\n          }\n          return this.mergeFunction(reshapedInputs);\n        } else {\n          // Transpose all inputs so that batch size is the last dimension.\n          // [batchSize, dim1, dim2, ...] -> [dim1, dim2, ..., batchSize]\n          let transposed = false;\n          for (const x of inputs) {\n            const xNDim = x.rank;\n            if (xNDim == null) {\n              const xShape = x.shape;\n              const batchSize = xShape[0];\n              const newShape = xShape.slice(1).concat([batchSize]);\n              let xTransposed = tfc.reshape(\n                  x, [batchSize].concat(mathUtils.arrayProd(xShape.slice(1))));\n              xTransposed = tfc.transpose(xTransposed, [1, 0]);\n              xTransposed = tfc.reshape(xTransposed, newShape);\n              reshapedInputs.push(xTransposed);\n              transposed = true;\n            } else if (xNDim > 1) {\n              const dims = mathUtils.range(1, xNDim).concat([0]);\n              reshapedInputs.push(tfc.transpose(x, dims));\n              transposed = true;\n            } else {\n              // We don't transpose inputs if they are 1D vectors or scalars.\n              reshapedInputs.push(x);\n            }\n          }\n          let y = this.mergeFunction(reshapedInputs);\n          const yNDim = y.rank;\n          if (transposed) {\n            // If inputs have been transposed, we have to transpose the output\n            // too.\n            if (yNDim == null) {\n              const yShape = y.shape;\n              const yNDim = yShape.length;\n              const batchSize = yShape[yNDim - 1];\n              const newShape =\n                  [batchSize].concat(yShape.slice(0, yShape.length - 1));\n              y = tfc.reshape(\n                  tfc.transpose(tfc.reshape(y, [-1, batchSize]), [1, 0]),\n                  newShape);\n            } else if (yNDim > 1) {\n              const dims = [yNDim - 1].concat(mathUtils.range(0, yNDim - 1));\n              y = tfc.transpose(y, dims);\n            }\n          }\n          return y;\n        }\n      } else {\n        return this.mergeFunction(inputs);\n      }\n    });\n  }\n\n  override computeOutputShape(inputShape: Shape|Shape[]): Shape|Shape[] {\n    inputShape = inputShape as Shape[];\n    let outputShape: Shape;\n    if (inputShape[0] == null) {\n      outputShape = null;\n    } else {\n      outputShape = inputShape[0].slice(1);\n    }\n    for (let i = 1; i < inputShape.length; ++i) {\n      const shape = inputShape[i] == null ? null : inputShape[i].slice(1);\n      outputShape = this.computeElementwiseOpOutputShape(outputShape, shape);\n    }\n\n    let batchSizes: number[] = [];\n    for (const shape of inputShape) {\n      if (shape != null && shape[0] !== null) {\n        batchSizes.push(shape[0]);\n      }\n    }\n    batchSizes = generic_utils.unique(batchSizes);\n    if (batchSizes.length === 1) {\n      outputShape = batchSizes.concat(outputShape);\n    } else {\n      outputShape = [null].concat(outputShape);\n    }\n    return outputShape;\n  }\n\n  override computeMask(inputs: Tensor|Tensor[], mask?: Tensor|Tensor[]):\n      Tensor {\n    return tfc.tidy(() => {\n      if (mask == null) {\n        return null;\n      }\n      if (!Array.isArray(mask)) {\n        throw new ValueError('`mask` should be an Array');\n      }\n      if (!Array.isArray(inputs)) {\n        throw new ValueError('`inputs` should be an Array');\n      }\n      if (mask.length !== inputs.length) {\n        throw new ValueError(\n            `The Array 'inputs' and 'mask' are expected to have the same ` +\n            `length, but have different lengths ` +\n            `(${inputs.length} vs ${mask.length})`);\n      }\n      if (mask.every(m => m == null)) {\n        return null;\n      }\n      mask = mask.map(m => m == null ? m : tfc.expandDims(m, 0));\n      let output = mask[0];\n      for (let i = 1; i < mask.length - 1; ++i) {\n        output = tfc.logicalAnd(output, mask[i]);\n      }\n      return output;\n    });\n  }\n}\n\nexport class Add extends Merge {\n  /** @nocollapse */\n  static className = 'Add';\n  constructor(args?: LayerArgs) {\n    super(args);\n  }\n\n  protected override mergeFunction(inputs: Tensor[]): Tensor {\n    return tidy(() => {\n      let output = inputs[0].clone();\n      for (let i = 1; i < inputs.length; ++i) {\n        output = tfc.add(output, inputs[i]);\n      }\n      return output;\n    });\n  }\n}\nserialization.registerClass(Add);\n\n/**\n * Calculate the element-wise sum of inputs, which all have the same shape.\n *\n * This function can be invoked in three ways.\n *\n * 1. Construct an instance of `Add` layer, by using no input argument\n *    or a single configuration argument. The resultant `Add` layer can then\n *    be used on `tf.SymbolicTensor`s or `tf.Tensor`s. For example:\n *\n * ```js\n * const addLayer = tf.layers.add();\n *\n * // The layer can be applied to inputs.\n * const input1 = tf.input({shape: [2, 2]});\n * const input2 = tf.input({shape: [2, 2]});\n * const output = addLayer.apply([input1, input2]);\n * console.log(output.shape);\n * // You get [null, 2, 2], with the first dimension as the undetermined batch\n * // dimension.\n * ```\n *\n * 2. Invoke directly on an `Array` of `tf.SymbolicTensor`s. This constructs\n *    an `Layer` object internally and calls its `apply` method on the inputs,\n *    generating a new `tf.SymbolicTensor`. For example:\n *\n * ```js\n * const input1 = tf.input({shape: [2, 2]});\n * const input2 = tf.input({shape: [2, 2]});\n * const output = tf.layers.add([input1, input2]);\n * console.log(output.shape);\n * // You get [null, 2, 2], with the first dimension as the undetermined batch\n * // dimension.\n * ```\n *\n * 3. Invoke directly on `tf.Tensor`s, i.e., concrete values. This constructs\n *    an `Layer` object internally and calls its `apply` method on the inputs,\n *    generating a new `tf.Tensor` as the result of the computation. For\n * example:\n *\n * ```js\n * const input1 = tf.tensor2d([1, 2, 3, 4], [2, 2]);\n * const input2 = tf.tensor2d([10, 20, 30, 40], [2, 2]);\n * tf.layers.add([input1, input2]).print();\n * // Gives [[11, 22], [33, 44]].\n *\n */\nexport function add(config?: SymbolicTensor[]|Tensor[]|LayerArgs): Layer|\n    SymbolicTensor|Tensor {\n  if (Array.isArray(config)) {\n    const layer = new Add({});\n    return layer.apply(config) as SymbolicTensor | Tensor;\n  } else {\n    return new Add(config);\n  }\n}\n\nexport class Multiply extends Merge {\n  /** @nocollapse */\n  static className = 'Multiply';\n  constructor(args?: LayerArgs) {\n    super(args);\n  }\n\n  protected override mergeFunction(inputs: Tensor[]): Tensor {\n    return tidy(() => {\n      let output = inputs[0].clone();\n      for (let i = 1; i < inputs.length; ++i) {\n        output = tfc.mul(output, inputs[i]);\n      }\n      return output;\n    });\n  }\n}\nserialization.registerClass(Multiply);\n\n/**\n * Calculate the element-wise product of inputs, which all have the same shape.\n *\n * This function can be invoked in three ways.\n *\n * 1. Construct an instance of `Multiply` layer, by using no input argument\n *    or a single configuration argument. The resultant `Multiply` layer can\n *    then be used on `tf.SymbolicTensor`s or `tf.Tensor`s. For example:\n *\n * ```js\n * const multiplyLayer = tf.layers.multiply();\n *\n * // The layer can be applied to inputs.\n * const input1 = tf.input({shape: [2, 2]});\n * const input2 = tf.input({shape: [2, 2]});\n * const output = multiplyLayer.apply([input1, input2]);\n * console.log(output.shape);\n * // You get [null, 2, 2], with the first dimension as the undetermined batch\n * // dimension.\n * ```\n *\n * 2. Invoke directly on an `Array` of `tf.SymbolicTensor`s. This constructs\n *    an `Layer` object internally and calls its `apply` method on the inputs,\n *    generating a new `tf.SymbolicTensor`. For example:\n *\n * ```js\n * const input1 = tf.input({shape: [2, 2]});\n * const input2 = tf.input({shape: [2, 2]});\n * const output = tf.layers.multiply([input1, input2]);\n * console.log(output.shape);\n * // You get [null, 2, 2], with the first dimension as the undetermined batch\n * // dimension.\n * ```\n *\n * 3. Invoke directly on `tf.Tensor`s, i.e., concrete values. This constructs\n *    an `Layer` object internally and calls its `apply` method on the inputs,\n *    generating a new `tf.Tensor` as the result of the computation. For\n * example:\n *\n * ```js\n * const input1 = tf.tensor2d([1, 2, 3, 4], [2, 2]);\n * const input2 = tf.tensor2d([10, 20, 30, 40], [2, 2]);\n * tf.layers.multiply([input1, input2]).print();\n * // Gives [[10, 40], [90, 160]].\n *\n */\nexport function multiply(config?: SymbolicTensor[]|Tensor[]|LayerArgs): Layer|\n    SymbolicTensor|Tensor {\n  if (Array.isArray(config)) {\n    const layer = new Multiply({});\n    return layer.apply(config) as SymbolicTensor | Tensor;\n  } else {\n    return new Multiply(config);\n  }\n}\n\nexport class Average extends Merge {\n  /** @nocollapse */\n  static className = 'Average';\n  constructor(args?: LayerArgs) {\n    super(args);\n  }\n\n  protected override mergeFunction(inputs: Tensor[]): Tensor {\n    return tidy(() => {\n      let output = inputs[0].clone();\n      for (let i = 1; i < inputs.length; ++i) {\n        output = tfc.add(output, inputs[i]);\n      }\n      return tfc.mul(1 / inputs.length, output);\n    });\n  }\n}\nserialization.registerClass(Average);\n\n/**\n * Calculate the element-wise arithmetic mean of inputs, which all have the same\n * shape.\n *\n * This function can be invoked in three ways.\n *\n * 1. Construct an instance of `Average` layer, by using no input argument\n *    or a single configuration argument. The resultant `Average` layer can then\n *    be used on `tf.SymbolicTensor`s or `tf.Tensor`s. For example:\n *\n * ```js\n * const averageLayer = tf.layers.average();\n *\n * // The layer can be applied to inputs.\n * const input1 = tf.input({shape: [2, 2]});\n * const input2 = tf.input({shape: [2, 2]});\n * const output = averageLayer.apply([input1, input2]);\n * console.log(output.shape);\n * // You get [null, 2, 2], with the first dimension as the undetermined batch\n * // dimension.\n * ```\n *\n * 2. Invoke directly on an `Array` of `tf.SymbolicTensor`s. This constructs\n *    an `Layer` object internally and calls its `apply` method on the inputs,\n *    generating a new `tf.SymbolicTensor`. For example:\n *\n * ```js\n * const input1 = tf.input({shape: [2, 2]});\n * const input2 = tf.input({shape: [2, 2]});\n * const output = tf.layers.average([input1, input2]);\n * console.log(output.shape);\n * // You get [null, 2, 2], with the first dimension as the undetermined batch\n * // dimension.\n * ```\n *\n * 3. Invoke directly on `tf.Tensor`s, i.e., concrete values. This constructs\n *    an `Layer` object internally and calls its `apply` method on the inputs,\n *    generating a new `tf.Tensor` as the result of the computation. For\n * example:\n *\n * ```js\n * const input1 = tf.tensor2d([1, 2, 3, 4], [2, 2]);\n * const input2 = tf.tensor2d([10, 20, 30, 40], [2, 2]);\n * tf.layers.average([input1, input2]).print();\n * // Gives [[5.5, 11], [16.5, 22]].\n *\n */\nexport function average(config?: SymbolicTensor[]|Tensor[]|LayerArgs): Layer|\n    SymbolicTensor|Tensor {\n  if (Array.isArray(config)) {\n    const layer = new Average({});\n    return layer.apply(config) as SymbolicTensor | Tensor;\n  } else {\n    return new Average(config);\n  }\n}\n\nexport class Maximum extends Merge {\n  /** @nocollapse */\n  static className = 'Maximum';\n  constructor(args?: LayerArgs) {\n    super(args);\n  }\n\n  protected override mergeFunction(inputs: Tensor[]): Tensor {\n    return tidy(() => {\n      let output = inputs[0];\n      for (let i = 1; i < inputs.length; ++i) {\n        output = tfc.maximum(output, inputs[i]);\n      }\n      return output;\n    });\n  }\n}\nserialization.registerClass(Maximum);\n\n/**\n * Calculate the element-wise maximum of inputs, which all have the same shape.\n *\n * This function can be invoked in three ways.\n *\n * 1. Construct an instance of `Maximum` layer, by using no input argument\n *    or a single configuration argument. The resultant `Maximum` layer can then\n *    be used on `tf.SymbolicTensor`s or `tf.Tensor`s. For example:\n *\n * ```js\n * const maximumLayer = tf.layers.maximum();\n *\n * // The layer can be applied to inputs.\n * const input1 = tf.input({shape: [2, 2]});\n * const input2 = tf.input({shape: [2, 2]});\n * const output = maximumLayer.apply([input1, input2]);\n * console.log(output.shape);\n * // You get [null, 2, 2], with the first dimension as the undetermined batch\n * // dimension.\n * ```\n *\n * 2. Invoke directly on an `Array` of `tf.SymbolicTensor`s. This constructs\n *    an `Layer` object internally and calls its `apply` method on the inputs,\n *    generating a new `tf.SymbolicTensor`. For example:\n *\n * ```js\n * const input1 = tf.input({shape: [2, 2]});\n * const input2 = tf.input({shape: [2, 2]});\n * const output = tf.layers.maximum([input1, input2]);\n * console.log(output.shape);\n * // You get [null, 2, 2], with the first dimension as the undetermined batch\n * // dimension.\n * ```\n *\n * 3. Invoke directly on `tf.Tensor`s, i.e., concrete values. This constructs\n *    an `Layer` object internally and calls its `apply` method on the inputs,\n *    generating a new `tf.Tensor` as the result of the computation. For\n * example:\n *\n * ```js\n * const input1 = tf.tensor2d([1, 20, 3, 40], [2, 2]);\n * const input2 = tf.tensor2d([10, 2, 30, 4], [2, 2]);\n * tf.layers.maximum([input1, input2]).print();\n * // Gives [[10, 20], [30, 40]].\n *\n */\nexport function maximum(config?: SymbolicTensor[]|Tensor[]|LayerArgs): Layer|\n    SymbolicTensor|Tensor {\n  if (Array.isArray(config)) {\n    const layer = new Maximum({});\n    return layer.apply(config) as SymbolicTensor | Tensor;\n  } else {\n    return new Maximum(config);\n  }\n}\n\nexport class Minimum extends Merge {\n  /** @nocollapse */\n  static className = 'Minimum';\n  constructor(args?: LayerArgs) {\n    super(args);\n  }\n\n  protected override mergeFunction(inputs: Tensor[]): Tensor {\n    return tidy(() => {\n      let output = inputs[0];\n      for (let i = 1; i < inputs.length; ++i) {\n        output = tfc.minimum(output, inputs[i]);\n      }\n      return output;\n    });\n  }\n}\nserialization.registerClass(Minimum);\n\n/**\n * Calculate the element-wise minimum of inputs, which all have the same shape.\n *\n * This function can be invoked in three ways.\n *\n * 1. Construct an instance of `Minimum` layer, by using no input argument\n *    or a single configuration argument. The resultant `Minimum` layer can then\n *    be used on `tf.SymbolicTensor`s or `tf.Tensor`s. For example:\n *\n * ```js\n * const minimumLayer = tf.layers.minimum();\n *\n * // The layer can be applied to inputs.\n * const input1 = tf.input({shape: [2, 2]});\n * const input2 = tf.input({shape: [2, 2]});\n * const output = minimumLayer.apply([input1, input2]);\n * console.log(output.shape);\n * // You get [null, 2, 2], with the first dimension as the undetermined batch\n * // dimension.\n * ```\n *\n * 2. Invoke directly on an `Array` of `tf.SymbolicTensor`s. This constructs\n *    an `Layer` object internally and calls its `apply` method on the inputs,\n *    generating a new `tf.SymbolicTensor`. For example:\n *\n * ```js\n * const input1 = tf.input({shape: [2, 2]});\n * const input2 = tf.input({shape: [2, 2]});\n * const output = tf.layers.minimum([input1, input2]);\n * console.log(output.shape);\n * // You get [null, 2, 2], with the first dimension as the undetermined batch\n * // dimension.\n * ```\n *\n * 3. Invoke directly on `tf.Tensor`s, i.e., concrete values. This constructs\n *    an `Layer` object internally and calls its `apply` method on the inputs,\n *    generating a new `tf.Tensor` as the result of the computation. For\n * example:\n *\n * ```js\n * const input1 = tf.tensor2d([1, 20, 3, 40], [2, 2]);\n * const input2 = tf.tensor2d([10, 2, 30, 4], [2, 2]);\n * tf.layers.minimum([input1, input2]).print();\n * // Gives [[1, 2], [3, 4]].\n *\n */\nexport function minimum(config?: SymbolicTensor[]|Tensor[]|LayerArgs): Layer|\n    SymbolicTensor|Tensor {\n  if (Array.isArray(config)) {\n    const layer = new Minimum({});\n    return layer.apply(config) as SymbolicTensor | Tensor;\n  } else {\n    return new Minimum(config);\n  }\n}\n\nexport declare interface ConcatenateLayerArgs extends LayerArgs {\n  /**\n   * Axis along which to concatenate.\n   */\n  axis?: number;\n}\n\nexport class Concatenate extends Merge {\n  /** @nocollapse */\n  static className = 'Concatenate';\n  readonly DEFAULT_AXIS = -1;\n  private readonly axis: number;\n\n  constructor(args?: ConcatenateLayerArgs) {\n    super(args);\n    if (args == null) {\n      args = {};\n    }\n    this.axis = args.axis == null ? this.DEFAULT_AXIS : args.axis;\n    this.supportsMasking = true;\n    this.reshapeRequired = false;\n  }\n\n  override build(inputShape: Shape|Shape[]): void {\n    // Used purely for shape validation.]\n    if (!(Array.isArray(inputShape) && Array.isArray(inputShape[0])) ||\n        inputShape.length === 1) {\n      throw new ValueError(\n          'A `Concatenate` layer should be called on a list of at least 2 ' +\n          'inputs');\n    }\n    inputShape = inputShape as Shape[];\n\n    let allNoneShape = true;\n    for (const shape of inputShape) {\n      if (shape != null) {\n        allNoneShape = false;\n        break;\n      }\n    }\n    if (allNoneShape) {\n      return;\n    }\n\n    const shapeSet: Shape[] = [];\n    for (let i = 0; i < inputShape.length; ++i) {\n      const shapeWithoutConcatAxis = inputShape[i].slice();\n      shapeWithoutConcatAxis.splice(this.axis, 1);\n      let exists = false;\n      for (const shape of shapeSet) {\n        if (util.arraysEqual(shape, shapeWithoutConcatAxis)) {\n          exists = true;\n          break;\n        }\n      }\n      if (!exists) {\n        shapeSet.push(shapeWithoutConcatAxis);\n      }\n    }\n    if (shapeSet.length > 1) {\n      throw new ValueError(\n          'A `Concatenate` layer requires inputs with matching shapes ' +\n          'except for the concat axis. Got input shapes: ' +\n          JSON.stringify(inputShape));\n    }\n  }\n\n  protected override mergeFunction(inputs: Tensor[]): Tensor {\n    return tidy(() => {\n      return K.concatenate(inputs, this.axis);\n    });\n  }\n\n  override computeOutputShape(inputShape: Shape|Shape[]): Shape|Shape[] {\n    if (!(Array.isArray(inputShape) && Array.isArray(inputShape[0]))) {\n      throw new ValueError(\n          'A `Concatenate` layer should be called on a list of inputs.');\n    }\n    const inputShapes = inputShape as Shape[];\n    const outputShape = inputShapes[0].slice();\n    const axis = this.axis < 0 ? outputShape.length + this.axis : this.axis;\n    // Porting Note: the line above is because TypeScript doesn't support\n    //   negative indices.\n    for (const shape of inputShapes.slice(1)) {\n      if (outputShape[axis] == null || shape[axis] == null) {\n        outputShape[axis] = null;\n        break;\n      }\n      outputShape[axis] += shape[axis];\n    }\n    return outputShape;\n  }\n\n  override computeMask(inputs: Tensor|Tensor[], mask?: Tensor|Tensor[]):\n      Tensor {\n    if (mask == null) {\n      return null;\n    }\n    if (!Array.isArray(mask)) {\n      throw new ValueError('`mask` should be an array for Concatenate');\n    }\n    if (!Array.isArray(inputs)) {\n      throw new ValueError('`inputs` should be an array for Concatenate');\n    }\n    if (mask.length !== inputs.length) {\n      throw new ValueError(\n          `Mismatch in the length of mask (${mask.length}) ` +\n          `and the legnth of inputs (${inputs.length})`);\n    }\n    return tfc.tidy(() => {\n      let allNullMasks = true;\n      mask.forEach(m => {\n        if (m != null) {\n          allNullMasks = false;\n          return;\n        }\n      });\n      if (allNullMasks) {\n        return null;\n      }\n      const outputMasks: Tensor[] = [];\n      for (let i = 0; i < inputs.length; ++i) {\n        if (mask[i] == null) {\n          // Input is unmasked. Append all 1's to masks.\n          outputMasks.push(tfc.cast(tfc.onesLike(inputs[i]), 'bool'));\n        } else if (mask[i].rank < inputs[i].rank) {\n          // Mask is smaller than the input, expand it.\n          outputMasks.push(tfc.expandDims(mask[i], -1));\n        } else {\n          outputMasks.push(mask[i]);\n        }\n      }\n      const concatenatedMasks = tfc.concat(outputMasks, this.axis);\n      return tfc.all(concatenatedMasks, -1, false);\n    });\n  }\n\n  override getConfig(): serialization.ConfigDict {\n    const config: serialization.ConfigDict = {\n      'axis': this.axis,\n    };\n    const baseConfig = super.getConfig();\n    Object.assign(config, baseConfig);\n    return config;\n  }\n}\nserialization.registerClass(Concatenate);\n\n/**\n * Concatenate an `Array` of inputs.\n *\n * This function can be invoked in three ways.\n *\n * 1. Construct an instance of `Concatenate` layer, by using no input argument\n *    or a single configuration argument. The resultant `Concatenate` layer can\n *    then be used on `tf.SymbolicTensor`s or `tf.Tensor`s. For example:\n *\n * ```js\n * const concatLayer = tf.layers.concatenate();\n *\n * // The layer can be applied to inputs.\n * const input1 = tf.input({shape: [2, 3]});\n * const input2 = tf.input({shape: [2, 4]});\n * const output = concatLayer.apply([input1, input2]);\n * console.log(output.shape);\n * // You get [null, 2, 7], with the first dimension as the undetermined batch\n * // dimension and the last dimension as the result of concatenating the\n * // last dimensions of the two inputs.\n * ```\n *\n * 2. Invoke directly on an `Array` of `tf.SymbolicTensor`s. This constructs\n *    an `Layer` object internally and calls its `apply` method on the inputs,\n *    generating a new `tf.SymbolicTensor`. For example:\n *\n * ```js\n * const input1 = tf.input({shape: [2, 3]});\n * const input2 = tf.input({shape: [2, 4]});\n * const output = tf.layers.concatenate([input1, input2]);\n * console.log(output.shape);\n * // You get [null, 2, 2], with the first dimension as the undetermined batch\n * // dimension and the last dimension as the result of concatenating the\n * // last dimensions of the two inputs.\n * ```\n *\n * 3. Invoke directly on `tf.Tensor`s, i.e., concrete values. This constructs\n *    an `Layer` object internally and calls its `apply` method on the inputs,\n *    generating a new `tf.Tensor` as the result of the computation. For\n * example:\n *\n * ```js\n * const input1 = tf.tensor2d([[1, 2], [3, 4]], [2, 2]);\n * const input2 = tf.tensor2d([[10, 20], [30, 40]], [2, 2]);\n * tf.layers.concatenate([input1, input2]).print();\n * // Gives [[1, 2, 10, 20], [3, 4, 30, 40]].\n *\n */\nexport function concatenate(config?: SymbolicTensor[]|Tensor[]|\n                            ConcatenateLayerArgs): Layer|SymbolicTensor|Tensor {\n  if (Array.isArray(config)) {\n    const layer = new Concatenate({});\n    return layer.apply(config) as SymbolicTensor | Tensor;\n  } else {\n    return new Concatenate(config);\n  }\n}\n\nexport declare interface DotLayerArgs extends LayerArgs {\n  /**\n   * Axis or axes along which the dot product will be taken.\n   *\n   * Integer or an Array of integers.\n   */\n  axes: number|[number, number];\n\n  /**\n   * Whether to L2-normalize samples along the dot product axis\n   * before taking the dot product.\n   *\n   * If set to `true`, the output of the dot product is the cosine\n   * proximity between the two samples.\n   */\n  normalize?: boolean;\n}\n\n/**\n * Interpretable potentially negative axis index.\n *\n * For example, given axis = -1, and dim = 3, this function will return 2.\n *\n * @param axis The axis index, may be a positive, zero or negative integer.\n * @param dim Total number of dimensions, a positive integer.\n * @returns A non-negative axis index equivalent to the input `axis`.\n */\nfunction interpretAxis(axis: number, dim: number): number {\n  while (axis < 0) {\n    axis += dim;\n  }\n  return axis;\n}\n\nfunction batchDot(x: Tensor, y: Tensor, axes: number|[number, number]): Tensor {\n  if (x.shape.length > 3 || y.shape.length > 3) {\n    throw new NotImplementedError(\n        'batchDot is not implemented for tensors of 4D or higher rank yet');\n  }\n  tfc.util.assert(\n      x.shape.length >= 2,\n      () => `batchDot requires the rank of x to be >= 2, ` +\n          `but got ${x.shape.length}`);\n  tfc.util.assert(\n      x.shape.length >= 2,\n      () => `batchDot requires the rank of y to be >= 2, ` +\n          `but got ${y.shape.length}`);\n\n  if (typeof axes === 'number') {\n    axes = [axes, axes];\n  }\n\n  if (x.dtype === 'complex64' || y.dtype === 'complex64') {\n    throw new NotImplementedError(\n        'batchDot is not implemented for complex64-type Tensors yet.');\n  }\n\n  const xNDim = x.shape.length;\n  const yNDim = y.shape.length;\n  if (axes == null) {\n    // Behave like batchMatmul by default.\n    axes = [xNDim - 1, yNDim - 2];\n  }\n  const axesArray = axes as [number, number];\n\n  return tfc.tidy(() => {\n    let diff: number;\n    if (xNDim > yNDim) {\n      diff = xNDim - yNDim;\n      const diffShape: Shape = [];\n      for (let i = 0; i < diff; ++i) {\n        diffShape.push(1);\n      }\n      y = tfc.reshape(y, y.shape.concat(diffShape));\n    } else if (yNDim > xNDim) {\n      diff = yNDim - xNDim;\n      const diffShape: Shape = [];\n      for (let i = 0; i < diff; ++i) {\n        diffShape.push(1);\n      }\n      x = tfc.reshape(x, x.shape.concat(diffShape));\n    } else {\n      diff = 0;\n    }\n\n    let out: Tensor;\n    if (x.shape.length === 2 && y.shape.length === 2) {\n      if (axesArray[0] === axesArray[1]) {\n        out = tfc.sum(tfc.mul(x, y), axesArray[0]);\n      } else {\n        out = tfc.sum(tfc.mul(tfc.transpose(x, [1, 0]), y), axesArray[1]);\n      }\n    } else {\n      const adjX = axesArray[0] !== x.shape.length - 1;\n      const adjY = axesArray[1] === y.shape.length - 1;\n      out = tfc.matMul(x, y, adjX, adjY);\n    }\n\n    if (diff > 0) {\n      let idx: number;\n      if (xNDim > yNDim) {\n        idx = xNDim + yNDim - 3;\n      } else {\n        idx = xNDim - 1;\n      }\n      const squeezeAxes: number[] = [];\n      for (let i = idx; i < idx + diff; ++i) {\n        squeezeAxes.push(i);\n      }\n      out = tfc.squeeze(out, squeezeAxes);\n    }\n    if (out.shape.length === 1) {\n      out = tfc.expandDims(out, 1);\n    }\n    return out;\n  });\n}\n\nexport class Dot extends Merge {\n  /** @nocollapse */\n  static className = 'Dot';\n\n  private axes: number|[number, number];\n  private normalize: boolean;\n\n  constructor(args: DotLayerArgs) {\n    super(args);\n    this.axes = args.axes;\n    this.normalize = args.normalize == null ? false : args.normalize;\n    this.supportsMasking = true;\n    this.reshapeRequired = false;\n  }\n\n  override build(inputShape: Shape|Shape[]): void {\n    tfc.util.assert(\n        Array.isArray(inputShape) && inputShape.length === 2 &&\n            Array.isArray(inputShape[0]) && Array.isArray(inputShape[1]),\n        () => 'A `Dot` layer should be called on a list of exactly 2 inputs.');\n    const shape1 = inputShape[0] as Shape;\n    const shape2 = inputShape[1] as Shape;\n    if (shape1.length > 3 || shape2.length > 3) {\n      throw new NotImplementedError(\n          'Dot layer does not support tensors of 4D or higher rank yet.');\n    }\n\n    const axes = this.interpretAxes(shape1, shape2);\n    if (shape1[axes[0]] !== shape2[axes[1]]) {\n      throw new ValueError(\n          `Dimension incompatibility: ` +\n          `${shape1[axes[0]]} !== ${shape2[axes[1]]}`);\n    }\n  }\n\n  protected override mergeFunction(inputs: Tensor[]): Tensor {\n    if (inputs.length !== 2) {\n      throw new ValueError(\n          'A `Dot` layer must be called on exactly 2 inputs, ' +\n          `but received ${inputs.length} input(s).`);\n    }\n\n    let x1 = inputs[0];\n    let x2 = inputs[1];\n    let axes: [number, number];\n    if (!Array.isArray(this.axes)) {\n      axes = [\n        interpretAxis(this.axes, x1.shape.length),\n        interpretAxis(this.axes, x2.shape.length)\n      ];\n    } else {\n      axes = this.axes.map(\n                 (axis, i) => interpretAxis(\n                     axis, inputs[i].shape.length)) as [number, number];\n    }\n    if (this.normalize) {\n      x1 = l2Normalize(x1, axes[0]);\n      x2 = l2Normalize(x2, axes[1]);\n    }\n    return batchDot(x1, x2, axes);\n  }\n\n  private interpretAxes(shape1: Shape, shape2: Shape): number[] {\n    let axes: number[];\n    if (!Array.isArray(this.axes)) {\n      // `this.axes` is a single integer.\n      axes = [\n        interpretAxis(this.axes, shape1.length),\n        interpretAxis(this.axes, shape2.length)\n      ];\n    } else {\n      // `this.axes` is an Array of integers.\n      axes = this.axes;\n    }\n    return axes;\n  }\n\n  override computeOutputShape(inputShape: Shape|Shape[]): Shape|Shape[] {\n    tfc.util.assert(\n        Array.isArray(inputShape) && inputShape.length === 2 &&\n            Array.isArray(inputShape[0]) && Array.isArray(inputShape[1]),\n        () => 'A `Dot` layer should be called on a list of exactly 2 inputs.');\n    const shape1 = (inputShape[0] as Shape).slice();\n    const shape2 = (inputShape[1] as Shape).slice();\n    if (shape1.length > 3 || shape2.length > 3) {\n      throw new NotImplementedError(\n          'Dot layer does not support tensors of 4D or higher rank yet.');\n    }\n\n    const axes = this.interpretAxes(shape1, shape2);\n    shape1.splice(axes[0], 1);\n    shape2.splice(axes[1], 1);\n    shape2.splice(0, 1);\n    const outputShape = shape1.concat(shape2);\n    if (outputShape.length === 1) {\n      outputShape.push(1);\n    }\n    return outputShape;\n  }\n\n  override computeMask(inputs: Tensor|Tensor[], mask?: Tensor|Tensor[]):\n      Tensor {\n    return null;\n  }\n\n  override getConfig(): serialization.ConfigDict {\n    const config: serialization.ConfigDict = {\n      'axes': this.axes,\n      'normalize': this.normalize\n    };\n    const baseConfig = super.getConfig();\n    Object.assign(config, baseConfig);\n    return config;\n  }\n}\nserialization.registerClass(Dot);\n\n// TODO(cais): Add functional interfaces for the merge layers.\n"], "mappings": "AAAA;;;;;;;;;AAUA;;;AAIA,OAAO,KAAKA,GAAG,MAAM,uBAAuB;AAC5C,SAAQC,aAAa,EAAUC,IAAI,EAAEC,IAAI,QAAO,uBAAuB;AACvE,OAAO,KAAKC,CAAC,MAAM,yBAAyB;AAC5C,SAAQC,KAAK,QAAkC,oBAAoB;AACnE,SAAQC,mBAAmB,EAAEC,UAAU,QAAO,WAAW;AAEzD,SAAQC,WAAW,QAAO,WAAW;AAErC,OAAO,KAAKC,aAAa,MAAM,wBAAwB;AACvD,OAAO,KAAKC,SAAS,MAAM,qBAAqB;AAChD,SAAQC,kBAAkB,QAAO,sBAAsB;AAEvD;;;;;AAKA,OAAM,MAAgBC,KAAM,SAAQP,KAAK;EAGvCQ,YAAYC,IAAgB;IAC1B,KAAK,CAACA,IAAI,IAAI,EAAE,CAAC;IACjB,IAAI,CAACC,eAAe,GAAG,IAAI;EAC7B;EAEA;;;;EAIUC,aAAaA,CAACC,MAAgB;IACtC,MAAM,IAAIX,mBAAmB,EAAE;EACjC;EAEA;;;;;;;;;;EAUQY,+BAA+BA,CAACC,MAAa,EAAEC,MAAa;IAClE,IAAID,MAAM,IAAI,IAAI,IAAIC,MAAM,IAAI,IAAI,EAAE;MACpC,OAAO,IAAI;KACZ,MAAM,IAAID,MAAM,CAACE,MAAM,GAAGD,MAAM,CAACC,MAAM,EAAE;MACxC,OAAO,IAAI,CAACH,+BAA+B,CAACE,MAAM,EAAED,MAAM,CAAC;KAC5D,MAAM,IAAIC,MAAM,CAACC,MAAM,KAAK,CAAC,EAAE;MAC9B,OAAOF,MAAM;;IAEf,MAAMG,WAAW,GAAUH,MAAM,CAACI,KAAK,CAAC,CAAC,EAAEJ,MAAM,CAACE,MAAM,GAAGD,MAAM,CAACC,MAAM,CAAC;IACzE,KAAK,IAAIG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,MAAM,CAACC,MAAM,EAAE,EAAEG,CAAC,EAAE;MACtC,MAAMC,CAAC,GAAGN,MAAM,CAACA,MAAM,CAACE,MAAM,GAAGD,MAAM,CAACC,MAAM,GAAGG,CAAC,CAAC;MACnD,MAAME,CAAC,GAAGN,MAAM,CAACI,CAAC,CAAC;MACnB,IAAIC,CAAC,IAAI,IAAI,IAAIC,CAAC,IAAI,IAAI,IAAID,CAAC,GAAG,CAAC,IAAIC,CAAC,GAAG,CAAC,EAAE;QAC5CJ,WAAW,CAACK,IAAI,CAAC,IAAI,CAAC;OACvB,MAAM,IAAIF,CAAC,KAAK,CAAC,EAAE;QAClBH,WAAW,CAACK,IAAI,CAACD,CAAC,CAAC;OACpB,MAAM,IAAIA,CAAC,KAAK,CAAC,EAAE;QAClBJ,WAAW,CAACK,IAAI,CAACF,CAAC,CAAC;OACpB,MAAM;QACL,IAAIA,CAAC,KAAKC,CAAC,EAAE;UACX,MAAM,IAAInB,UAAU,CAChB,uDAAuD,GACvDqB,IAAI,CAACC,SAAS,CAACV,MAAM,CAAC,GAAG,GAAG,GAAGS,IAAI,CAACC,SAAS,CAACT,MAAM,CAAC,CAAC;;QAE5DE,WAAW,CAACK,IAAI,CAACF,CAAC,CAAC;;;IAGvB,OAAOH,WAAW;EACpB;EAESQ,KAAKA,CAACC,UAAyB;IACtC;IACA,IAAIC,KAAK,CAACC,OAAO,CAACF,UAAU,CAAC,IAAI,CAACC,KAAK,CAACC,OAAO,CAACF,UAAU,CAAC,CAAC,CAAC,CAAC,EAAE;MAC9D;MACAA,UAAU,GAAG,CAACpB,kBAAkB,CAACoB,UAAU,CAAC,CAAC;;IAE/CA,UAAU,GAAGA,UAAqB;IAClC,IAAIA,UAAU,CAACV,MAAM,GAAG,CAAC,EAAE;MACzB,MAAM,IAAId,UAAU,CAChB,kEAAkE,GAClE,QAAQwB,UAAU,CAACV,MAAM,YAAY,CAAC;;IAG5C;IACA;IACA,IAAIa,UAAU,GAAa,EAAE;IAC7B,KAAK,MAAMC,KAAK,IAAIJ,UAAU,EAAE;MAC9B,IAAII,KAAK,IAAI,IAAI,IAAIA,KAAK,CAAC,CAAC,CAAC,KAAK,IAAI,EAAE;QACtCD,UAAU,CAACP,IAAI,CAACQ,KAAK,CAAC,CAAC,CAAC,CAAC;;;IAG7BD,UAAU,GAAGzB,aAAa,CAAC2B,MAAM,CAACF,UAAU,CAAC;IAC7C,IAAIA,UAAU,CAACb,MAAM,GAAG,CAAC,EAAE;MACzB,MAAM,IAAId,UAAU,CAChB,oDAAoD,GACpD,4BAA4BqB,IAAI,CAACC,SAAS,CAACE,UAAU,CAAC,GAAG,CAAC;;IAGhE,IAAIT,WAAW,GACXS,UAAU,CAAC,CAAC,CAAC,IAAI,IAAI,GAAG,IAAI,GAAGA,UAAU,CAAC,CAAC,CAAC,CAACR,KAAK,CAAC,CAAC,CAAC;IACzD,KAAK,IAAIE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGM,UAAU,CAACV,MAAM,EAAE,EAAEI,CAAC,EAAE;MAC1C,MAAMU,KAAK,GAAGJ,UAAU,CAACN,CAAC,CAAC,IAAI,IAAI,GAAG,IAAI,GAAGM,UAAU,CAACN,CAAC,CAAC,CAACF,KAAK,CAAC,CAAC,CAAC;MACnED,WAAW,GAAG,IAAI,CAACJ,+BAA+B,CAACI,WAAW,EAAEa,KAAK,CAAC;;IAExE;IACA;IACA,MAAME,QAAQ,GAAGN,UAAU,CAACO,GAAG,CAACH,KAAK,IAAIA,KAAK,CAACd,MAAM,CAAC;IACtD,IAAIU,UAAU,CAACQ,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,IAC/B9B,aAAa,CAAC2B,MAAM,CAACC,QAAQ,CAAC,CAAChB,MAAM,KAAK,CAAC,EAAE;MAC/C,IAAI,CAACmB,eAAe,GAAG,KAAK;KAC7B,MAAM;MACL,IAAI,CAACA,eAAe,GAAG,IAAI;;EAE/B;EAESC,IAAIA,CAACxB,MAAuB,EAAEyB,MAAc;IACnD,OAAOxC,IAAI,CAAC,MAAK;MACfe,MAAM,GAAGA,MAAkB;MAC3B,IAAI,IAAI,CAACuB,eAAe,EAAE;QACxB,MAAMG,cAAc,GAAa,EAAE;QACnC,MAAMC,SAAS,GAAG3B,MAAM,CAACqB,GAAG,CAACO,KAAK,IAAIA,KAAK,CAACC,IAAI,CAAC;QACjD,IAAIF,SAAS,CAACL,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE;UAClC;UACA;UACA,MAAMQ,OAAO,GAAGrC,SAAS,CAACsC,GAAG,CAACJ,SAAS,CAAC;UACxC,KAAK,IAAIK,CAAC,IAAIhC,MAAM,EAAE;YACpB,MAAMiC,KAAK,GAAGD,CAAC,CAACH,IAAI;YACpB,KAAK,IAAItB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGuB,OAAO,GAAGG,KAAK,EAAE,EAAE1B,CAAC,EAAE;cACxCyB,CAAC,GAAG7C,CAAC,CAAC+C,UAAU,CAACF,CAAC,EAAE,CAAC,CAAC;;YAExBN,cAAc,CAAChB,IAAI,CAACsB,CAAC,CAAC;;UAExB,OAAO,IAAI,CAACjC,aAAa,CAAC2B,cAAc,CAAC;SAC1C,MAAM;UACL;UACA;UACA,IAAIS,UAAU,GAAG,KAAK;UACtB,KAAK,MAAMH,CAAC,IAAIhC,MAAM,EAAE;YACtB,MAAMiC,KAAK,GAAGD,CAAC,CAACH,IAAI;YACpB,IAAII,KAAK,IAAI,IAAI,EAAE;cACjB,MAAMG,MAAM,GAAGJ,CAAC,CAACd,KAAK;cACtB,MAAMmB,SAAS,GAAGD,MAAM,CAAC,CAAC,CAAC;cAC3B,MAAME,QAAQ,GAAGF,MAAM,CAAC9B,KAAK,CAAC,CAAC,CAAC,CAACiC,MAAM,CAAC,CAACF,SAAS,CAAC,CAAC;cACpD,IAAIG,WAAW,GAAGzD,GAAG,CAAC0D,OAAO,CACzBT,CAAC,EAAE,CAACK,SAAS,CAAC,CAACE,MAAM,CAAC9C,SAAS,CAACiD,SAAS,CAACN,MAAM,CAAC9B,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAChEkC,WAAW,GAAGzD,GAAG,CAAC4D,SAAS,CAACH,WAAW,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;cAChDA,WAAW,GAAGzD,GAAG,CAAC0D,OAAO,CAACD,WAAW,EAAEF,QAAQ,CAAC;cAChDZ,cAAc,CAAChB,IAAI,CAAC8B,WAAW,CAAC;cAChCL,UAAU,GAAG,IAAI;aAClB,MAAM,IAAIF,KAAK,GAAG,CAAC,EAAE;cACpB,MAAMW,IAAI,GAAGnD,SAAS,CAACoD,KAAK,CAAC,CAAC,EAAEZ,KAAK,CAAC,CAACM,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;cAClDb,cAAc,CAAChB,IAAI,CAAC3B,GAAG,CAAC4D,SAAS,CAACX,CAAC,EAAEY,IAAI,CAAC,CAAC;cAC3CT,UAAU,GAAG,IAAI;aAClB,MAAM;cACL;cACAT,cAAc,CAAChB,IAAI,CAACsB,CAAC,CAAC;;;UAG1B,IAAIc,CAAC,GAAG,IAAI,CAAC/C,aAAa,CAAC2B,cAAc,CAAC;UAC1C,MAAMqB,KAAK,GAAGD,CAAC,CAACjB,IAAI;UACpB,IAAIM,UAAU,EAAE;YACd;YACA;YACA,IAAIY,KAAK,IAAI,IAAI,EAAE;cACjB,MAAMC,MAAM,GAAGF,CAAC,CAAC5B,KAAK;cACtB,MAAM6B,KAAK,GAAGC,MAAM,CAAC5C,MAAM;cAC3B,MAAMiC,SAAS,GAAGW,MAAM,CAACD,KAAK,GAAG,CAAC,CAAC;cACnC,MAAMT,QAAQ,GACV,CAACD,SAAS,CAAC,CAACE,MAAM,CAACS,MAAM,CAAC1C,KAAK,CAAC,CAAC,EAAE0C,MAAM,CAAC5C,MAAM,GAAG,CAAC,CAAC,CAAC;cAC1D0C,CAAC,GAAG/D,GAAG,CAAC0D,OAAO,CACX1D,GAAG,CAAC4D,SAAS,CAAC5D,GAAG,CAAC0D,OAAO,CAACK,CAAC,EAAE,CAAC,CAAC,CAAC,EAAET,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EACtDC,QAAQ,CAAC;aACd,MAAM,IAAIS,KAAK,GAAG,CAAC,EAAE;cACpB,MAAMH,IAAI,GAAG,CAACG,KAAK,GAAG,CAAC,CAAC,CAACR,MAAM,CAAC9C,SAAS,CAACoD,KAAK,CAAC,CAAC,EAAEE,KAAK,GAAG,CAAC,CAAC,CAAC;cAC9DD,CAAC,GAAG/D,GAAG,CAAC4D,SAAS,CAACG,CAAC,EAAEF,IAAI,CAAC;;;UAG9B,OAAOE,CAAC;;OAEX,MAAM;QACL,OAAO,IAAI,CAAC/C,aAAa,CAACC,MAAM,CAAC;;IAErC,CAAC,CAAC;EACJ;EAESiD,kBAAkBA,CAACnC,UAAyB;IACnDA,UAAU,GAAGA,UAAqB;IAClC,IAAIT,WAAkB;IACtB,IAAIS,UAAU,CAAC,CAAC,CAAC,IAAI,IAAI,EAAE;MACzBT,WAAW,GAAG,IAAI;KACnB,MAAM;MACLA,WAAW,GAAGS,UAAU,CAAC,CAAC,CAAC,CAACR,KAAK,CAAC,CAAC,CAAC;;IAEtC,KAAK,IAAIE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGM,UAAU,CAACV,MAAM,EAAE,EAAEI,CAAC,EAAE;MAC1C,MAAMU,KAAK,GAAGJ,UAAU,CAACN,CAAC,CAAC,IAAI,IAAI,GAAG,IAAI,GAAGM,UAAU,CAACN,CAAC,CAAC,CAACF,KAAK,CAAC,CAAC,CAAC;MACnED,WAAW,GAAG,IAAI,CAACJ,+BAA+B,CAACI,WAAW,EAAEa,KAAK,CAAC;;IAGxE,IAAID,UAAU,GAAa,EAAE;IAC7B,KAAK,MAAMC,KAAK,IAAIJ,UAAU,EAAE;MAC9B,IAAII,KAAK,IAAI,IAAI,IAAIA,KAAK,CAAC,CAAC,CAAC,KAAK,IAAI,EAAE;QACtCD,UAAU,CAACP,IAAI,CAACQ,KAAK,CAAC,CAAC,CAAC,CAAC;;;IAG7BD,UAAU,GAAGzB,aAAa,CAAC2B,MAAM,CAACF,UAAU,CAAC;IAC7C,IAAIA,UAAU,CAACb,MAAM,KAAK,CAAC,EAAE;MAC3BC,WAAW,GAAGY,UAAU,CAACsB,MAAM,CAAClC,WAAW,CAAC;KAC7C,MAAM;MACLA,WAAW,GAAG,CAAC,IAAI,CAAC,CAACkC,MAAM,CAAClC,WAAW,CAAC;;IAE1C,OAAOA,WAAW;EACpB;EAES6C,WAAWA,CAAClD,MAAuB,EAAEmD,IAAsB;IAElE,OAAOpE,GAAG,CAACE,IAAI,CAAC,MAAK;MACnB,IAAIkE,IAAI,IAAI,IAAI,EAAE;QAChB,OAAO,IAAI;;MAEb,IAAI,CAACpC,KAAK,CAACC,OAAO,CAACmC,IAAI,CAAC,EAAE;QACxB,MAAM,IAAI7D,UAAU,CAAC,2BAA2B,CAAC;;MAEnD,IAAI,CAACyB,KAAK,CAACC,OAAO,CAAChB,MAAM,CAAC,EAAE;QAC1B,MAAM,IAAIV,UAAU,CAAC,6BAA6B,CAAC;;MAErD,IAAI6D,IAAI,CAAC/C,MAAM,KAAKJ,MAAM,CAACI,MAAM,EAAE;QACjC,MAAM,IAAId,UAAU,CAChB,8DAA8D,GAC9D,qCAAqC,GACrC,IAAIU,MAAM,CAACI,MAAM,OAAO+C,IAAI,CAAC/C,MAAM,GAAG,CAAC;;MAE7C,IAAI+C,IAAI,CAACC,KAAK,CAACC,CAAC,IAAIA,CAAC,IAAI,IAAI,CAAC,EAAE;QAC9B,OAAO,IAAI;;MAEbF,IAAI,GAAGA,IAAI,CAAC9B,GAAG,CAACgC,CAAC,IAAIA,CAAC,IAAI,IAAI,GAAGA,CAAC,GAAGtE,GAAG,CAACmD,UAAU,CAACmB,CAAC,EAAE,CAAC,CAAC,CAAC;MAC1D,IAAIC,MAAM,GAAGH,IAAI,CAAC,CAAC,CAAC;MACpB,KAAK,IAAI3C,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG2C,IAAI,CAAC/C,MAAM,GAAG,CAAC,EAAE,EAAEI,CAAC,EAAE;QACxC8C,MAAM,GAAGvE,GAAG,CAACwE,UAAU,CAACD,MAAM,EAAEH,IAAI,CAAC3C,CAAC,CAAC,CAAC;;MAE1C,OAAO8C,MAAM;IACf,CAAC,CAAC;EACJ;;AAGF,MAAaE,GAAI,SAAQ7D,KAAK;EAG5BC,YAAYC,IAAgB;IAC1B,KAAK,CAACA,IAAI,CAAC;EACb;EAEmBE,aAAaA,CAACC,MAAgB;IAC/C,OAAOf,IAAI,CAAC,MAAK;MACf,IAAIqE,MAAM,GAAGtD,MAAM,CAAC,CAAC,CAAC,CAACyD,KAAK,EAAE;MAC9B,KAAK,IAAIjD,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGR,MAAM,CAACI,MAAM,EAAE,EAAEI,CAAC,EAAE;QACtC8C,MAAM,GAAGvE,GAAG,CAAC2E,GAAG,CAACJ,MAAM,EAAEtD,MAAM,CAACQ,CAAC,CAAC,CAAC;;MAErC,OAAO8C,MAAM;IACf,CAAC,CAAC;EACJ;;AAdA;AACOE,GAAA,CAAAG,SAAS,GAAG,KAAK;SAFbH,GAAG;AAiBhBxE,aAAa,CAAC4E,aAAa,CAACJ,GAAG,CAAC;AAEhC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8CA,OAAM,SAAUE,GAAGA,CAACG,MAA4C;EAE9D,IAAI9C,KAAK,CAACC,OAAO,CAAC6C,MAAM,CAAC,EAAE;IACzB,MAAMC,KAAK,GAAG,IAAIN,GAAG,CAAC,EAAE,CAAC;IACzB,OAAOM,KAAK,CAACC,KAAK,CAACF,MAAM,CAA4B;GACtD,MAAM;IACL,OAAO,IAAIL,GAAG,CAACK,MAAM,CAAC;;AAE1B;AAEA,MAAaG,QAAS,SAAQrE,KAAK;EAGjCC,YAAYC,IAAgB;IAC1B,KAAK,CAACA,IAAI,CAAC;EACb;EAEmBE,aAAaA,CAACC,MAAgB;IAC/C,OAAOf,IAAI,CAAC,MAAK;MACf,IAAIqE,MAAM,GAAGtD,MAAM,CAAC,CAAC,CAAC,CAACyD,KAAK,EAAE;MAC9B,KAAK,IAAIjD,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGR,MAAM,CAACI,MAAM,EAAE,EAAEI,CAAC,EAAE;QACtC8C,MAAM,GAAGvE,GAAG,CAACkF,GAAG,CAACX,MAAM,EAAEtD,MAAM,CAACQ,CAAC,CAAC,CAAC;;MAErC,OAAO8C,MAAM;IACf,CAAC,CAAC;EACJ;;AAdA;AACOU,QAAA,CAAAL,SAAS,GAAG,UAAU;SAFlBK,QAAQ;AAiBrBhF,aAAa,CAAC4E,aAAa,CAACI,QAAQ,CAAC;AAErC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8CA,OAAM,SAAUE,QAAQA,CAACL,MAA4C;EAEnE,IAAI9C,KAAK,CAACC,OAAO,CAAC6C,MAAM,CAAC,EAAE;IACzB,MAAMC,KAAK,GAAG,IAAIE,QAAQ,CAAC,EAAE,CAAC;IAC9B,OAAOF,KAAK,CAACC,KAAK,CAACF,MAAM,CAA4B;GACtD,MAAM;IACL,OAAO,IAAIG,QAAQ,CAACH,MAAM,CAAC;;AAE/B;AAEA,MAAaM,OAAQ,SAAQxE,KAAK;EAGhCC,YAAYC,IAAgB;IAC1B,KAAK,CAACA,IAAI,CAAC;EACb;EAEmBE,aAAaA,CAACC,MAAgB;IAC/C,OAAOf,IAAI,CAAC,MAAK;MACf,IAAIqE,MAAM,GAAGtD,MAAM,CAAC,CAAC,CAAC,CAACyD,KAAK,EAAE;MAC9B,KAAK,IAAIjD,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGR,MAAM,CAACI,MAAM,EAAE,EAAEI,CAAC,EAAE;QACtC8C,MAAM,GAAGvE,GAAG,CAAC2E,GAAG,CAACJ,MAAM,EAAEtD,MAAM,CAACQ,CAAC,CAAC,CAAC;;MAErC,OAAOzB,GAAG,CAACkF,GAAG,CAAC,CAAC,GAAGjE,MAAM,CAACI,MAAM,EAAEkD,MAAM,CAAC;IAC3C,CAAC,CAAC;EACJ;;AAdA;AACOa,OAAA,CAAAR,SAAS,GAAG,SAAS;SAFjBQ,OAAO;AAiBpBnF,aAAa,CAAC4E,aAAa,CAACO,OAAO,CAAC;AAEpC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA+CA,OAAM,SAAUC,OAAOA,CAACP,MAA4C;EAElE,IAAI9C,KAAK,CAACC,OAAO,CAAC6C,MAAM,CAAC,EAAE;IACzB,MAAMC,KAAK,GAAG,IAAIK,OAAO,CAAC,EAAE,CAAC;IAC7B,OAAOL,KAAK,CAACC,KAAK,CAACF,MAAM,CAA4B;GACtD,MAAM;IACL,OAAO,IAAIM,OAAO,CAACN,MAAM,CAAC;;AAE9B;AAEA,MAAaQ,OAAQ,SAAQ1E,KAAK;EAGhCC,YAAYC,IAAgB;IAC1B,KAAK,CAACA,IAAI,CAAC;EACb;EAEmBE,aAAaA,CAACC,MAAgB;IAC/C,OAAOf,IAAI,CAAC,MAAK;MACf,IAAIqE,MAAM,GAAGtD,MAAM,CAAC,CAAC,CAAC;MACtB,KAAK,IAAIQ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGR,MAAM,CAACI,MAAM,EAAE,EAAEI,CAAC,EAAE;QACtC8C,MAAM,GAAGvE,GAAG,CAACuF,OAAO,CAAChB,MAAM,EAAEtD,MAAM,CAACQ,CAAC,CAAC,CAAC;;MAEzC,OAAO8C,MAAM;IACf,CAAC,CAAC;EACJ;;AAdA;AACOe,OAAA,CAAAV,SAAS,GAAG,SAAS;SAFjBU,OAAO;AAiBpBrF,aAAa,CAAC4E,aAAa,CAACS,OAAO,CAAC;AAEpC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8CA,OAAM,SAAUC,OAAOA,CAACT,MAA4C;EAElE,IAAI9C,KAAK,CAACC,OAAO,CAAC6C,MAAM,CAAC,EAAE;IACzB,MAAMC,KAAK,GAAG,IAAIO,OAAO,CAAC,EAAE,CAAC;IAC7B,OAAOP,KAAK,CAACC,KAAK,CAACF,MAAM,CAA4B;GACtD,MAAM;IACL,OAAO,IAAIQ,OAAO,CAACR,MAAM,CAAC;;AAE9B;AAEA,MAAaU,OAAQ,SAAQ5E,KAAK;EAGhCC,YAAYC,IAAgB;IAC1B,KAAK,CAACA,IAAI,CAAC;EACb;EAEmBE,aAAaA,CAACC,MAAgB;IAC/C,OAAOf,IAAI,CAAC,MAAK;MACf,IAAIqE,MAAM,GAAGtD,MAAM,CAAC,CAAC,CAAC;MACtB,KAAK,IAAIQ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGR,MAAM,CAACI,MAAM,EAAE,EAAEI,CAAC,EAAE;QACtC8C,MAAM,GAAGvE,GAAG,CAACyF,OAAO,CAAClB,MAAM,EAAEtD,MAAM,CAACQ,CAAC,CAAC,CAAC;;MAEzC,OAAO8C,MAAM;IACf,CAAC,CAAC;EACJ;;AAdA;AACOiB,OAAA,CAAAZ,SAAS,GAAG,SAAS;SAFjBY,OAAO;AAiBpBvF,aAAa,CAAC4E,aAAa,CAACW,OAAO,CAAC;AAEpC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8CA,OAAM,SAAUC,OAAOA,CAACX,MAA4C;EAElE,IAAI9C,KAAK,CAACC,OAAO,CAAC6C,MAAM,CAAC,EAAE;IACzB,MAAMC,KAAK,GAAG,IAAIS,OAAO,CAAC,EAAE,CAAC;IAC7B,OAAOT,KAAK,CAACC,KAAK,CAACF,MAAM,CAA4B;GACtD,MAAM;IACL,OAAO,IAAIU,OAAO,CAACV,MAAM,CAAC;;AAE9B;AASA,MAAaY,WAAY,SAAQ9E,KAAK;EAMpCC,YAAYC,IAA2B;IACrC,KAAK,CAACA,IAAI,CAAC;IAJJ,KAAA6E,YAAY,GAAG,CAAC,CAAC;IAKxB,IAAI7E,IAAI,IAAI,IAAI,EAAE;MAChBA,IAAI,GAAG,EAAE;;IAEX,IAAI,CAAC8E,IAAI,GAAG9E,IAAI,CAAC8E,IAAI,IAAI,IAAI,GAAG,IAAI,CAACD,YAAY,GAAG7E,IAAI,CAAC8E,IAAI;IAC7D,IAAI,CAAC7E,eAAe,GAAG,IAAI;IAC3B,IAAI,CAACyB,eAAe,GAAG,KAAK;EAC9B;EAESV,KAAKA,CAACC,UAAyB;IACtC;IACA,IAAI,EAAEC,KAAK,CAACC,OAAO,CAACF,UAAU,CAAC,IAAIC,KAAK,CAACC,OAAO,CAACF,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,IAC5DA,UAAU,CAACV,MAAM,KAAK,CAAC,EAAE;MAC3B,MAAM,IAAId,UAAU,CAChB,iEAAiE,GACjE,QAAQ,CAAC;;IAEfwB,UAAU,GAAGA,UAAqB;IAElC,IAAI8D,YAAY,GAAG,IAAI;IACvB,KAAK,MAAM1D,KAAK,IAAIJ,UAAU,EAAE;MAC9B,IAAII,KAAK,IAAI,IAAI,EAAE;QACjB0D,YAAY,GAAG,KAAK;QACpB;;;IAGJ,IAAIA,YAAY,EAAE;MAChB;;IAGF,MAAMC,QAAQ,GAAY,EAAE;IAC5B,KAAK,IAAIrE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGM,UAAU,CAACV,MAAM,EAAE,EAAEI,CAAC,EAAE;MAC1C,MAAMsE,sBAAsB,GAAGhE,UAAU,CAACN,CAAC,CAAC,CAACF,KAAK,EAAE;MACpDwE,sBAAsB,CAACC,MAAM,CAAC,IAAI,CAACJ,IAAI,EAAE,CAAC,CAAC;MAC3C,IAAIK,MAAM,GAAG,KAAK;MAClB,KAAK,MAAM9D,KAAK,IAAI2D,QAAQ,EAAE;QAC5B,IAAI3F,IAAI,CAAC+F,WAAW,CAAC/D,KAAK,EAAE4D,sBAAsB,CAAC,EAAE;UACnDE,MAAM,GAAG,IAAI;UACb;;;MAGJ,IAAI,CAACA,MAAM,EAAE;QACXH,QAAQ,CAACnE,IAAI,CAACoE,sBAAsB,CAAC;;;IAGzC,IAAID,QAAQ,CAACzE,MAAM,GAAG,CAAC,EAAE;MACvB,MAAM,IAAId,UAAU,CAChB,6DAA6D,GAC7D,gDAAgD,GAChDqB,IAAI,CAACC,SAAS,CAACE,UAAU,CAAC,CAAC;;EAEnC;EAEmBf,aAAaA,CAACC,MAAgB;IAC/C,OAAOf,IAAI,CAAC,MAAK;MACf,OAAOE,CAAC,CAAC+F,WAAW,CAAClF,MAAM,EAAE,IAAI,CAAC2E,IAAI,CAAC;IACzC,CAAC,CAAC;EACJ;EAES1B,kBAAkBA,CAACnC,UAAyB;IACnD,IAAI,EAAEC,KAAK,CAACC,OAAO,CAACF,UAAU,CAAC,IAAIC,KAAK,CAACC,OAAO,CAACF,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MAChE,MAAM,IAAIxB,UAAU,CAChB,6DAA6D,CAAC;;IAEpE,MAAM6F,WAAW,GAAGrE,UAAqB;IACzC,MAAMT,WAAW,GAAG8E,WAAW,CAAC,CAAC,CAAC,CAAC7E,KAAK,EAAE;IAC1C,MAAMqE,IAAI,GAAG,IAAI,CAACA,IAAI,GAAG,CAAC,GAAGtE,WAAW,CAACD,MAAM,GAAG,IAAI,CAACuE,IAAI,GAAG,IAAI,CAACA,IAAI;IACvE;IACA;IACA,KAAK,MAAMzD,KAAK,IAAIiE,WAAW,CAAC7E,KAAK,CAAC,CAAC,CAAC,EAAE;MACxC,IAAID,WAAW,CAACsE,IAAI,CAAC,IAAI,IAAI,IAAIzD,KAAK,CAACyD,IAAI,CAAC,IAAI,IAAI,EAAE;QACpDtE,WAAW,CAACsE,IAAI,CAAC,GAAG,IAAI;QACxB;;MAEFtE,WAAW,CAACsE,IAAI,CAAC,IAAIzD,KAAK,CAACyD,IAAI,CAAC;;IAElC,OAAOtE,WAAW;EACpB;EAES6C,WAAWA,CAAClD,MAAuB,EAAEmD,IAAsB;IAElE,IAAIA,IAAI,IAAI,IAAI,EAAE;MAChB,OAAO,IAAI;;IAEb,IAAI,CAACpC,KAAK,CAACC,OAAO,CAACmC,IAAI,CAAC,EAAE;MACxB,MAAM,IAAI7D,UAAU,CAAC,2CAA2C,CAAC;;IAEnE,IAAI,CAACyB,KAAK,CAACC,OAAO,CAAChB,MAAM,CAAC,EAAE;MAC1B,MAAM,IAAIV,UAAU,CAAC,6CAA6C,CAAC;;IAErE,IAAI6D,IAAI,CAAC/C,MAAM,KAAKJ,MAAM,CAACI,MAAM,EAAE;MACjC,MAAM,IAAId,UAAU,CAChB,mCAAmC6D,IAAI,CAAC/C,MAAM,IAAI,GAClD,6BAA6BJ,MAAM,CAACI,MAAM,GAAG,CAAC;;IAEpD,OAAOrB,GAAG,CAACE,IAAI,CAAC,MAAK;MACnB,IAAImG,YAAY,GAAG,IAAI;MACvBjC,IAAI,CAACkC,OAAO,CAAChC,CAAC,IAAG;QACf,IAAIA,CAAC,IAAI,IAAI,EAAE;UACb+B,YAAY,GAAG,KAAK;UACpB;;MAEJ,CAAC,CAAC;MACF,IAAIA,YAAY,EAAE;QAChB,OAAO,IAAI;;MAEb,MAAME,WAAW,GAAa,EAAE;MAChC,KAAK,IAAI9E,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGR,MAAM,CAACI,MAAM,EAAE,EAAEI,CAAC,EAAE;QACtC,IAAI2C,IAAI,CAAC3C,CAAC,CAAC,IAAI,IAAI,EAAE;UACnB;UACA8E,WAAW,CAAC5E,IAAI,CAAC3B,GAAG,CAACwG,IAAI,CAACxG,GAAG,CAACyG,QAAQ,CAACxF,MAAM,CAACQ,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC;SAC5D,MAAM,IAAI2C,IAAI,CAAC3C,CAAC,CAAC,CAACqB,IAAI,GAAG7B,MAAM,CAACQ,CAAC,CAAC,CAACqB,IAAI,EAAE;UACxC;UACAyD,WAAW,CAAC5E,IAAI,CAAC3B,GAAG,CAACmD,UAAU,CAACiB,IAAI,CAAC3C,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;SAC9C,MAAM;UACL8E,WAAW,CAAC5E,IAAI,CAACyC,IAAI,CAAC3C,CAAC,CAAC,CAAC;;;MAG7B,MAAMiF,iBAAiB,GAAG1G,GAAG,CAACwD,MAAM,CAAC+C,WAAW,EAAE,IAAI,CAACX,IAAI,CAAC;MAC5D,OAAO5F,GAAG,CAAC2G,GAAG,CAACD,iBAAiB,EAAE,CAAC,CAAC,EAAE,KAAK,CAAC;IAC9C,CAAC,CAAC;EACJ;EAESE,SAASA,CAAA;IAChB,MAAM9B,MAAM,GAA6B;MACvC,MAAM,EAAE,IAAI,CAACc;KACd;IACD,MAAMiB,UAAU,GAAG,KAAK,CAACD,SAAS,EAAE;IACpCE,MAAM,CAACC,MAAM,CAACjC,MAAM,EAAE+B,UAAU,CAAC;IACjC,OAAO/B,MAAM;EACf;;AAxIA;AACOY,WAAA,CAAAd,SAAS,GAAG,aAAa;SAFrBc,WAAW;AA2IxBzF,aAAa,CAAC4E,aAAa,CAACa,WAAW,CAAC;AAExC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgDA,OAAM,SAAUS,WAAWA,CAACrB,MACoB;EAC9C,IAAI9C,KAAK,CAACC,OAAO,CAAC6C,MAAM,CAAC,EAAE;IACzB,MAAMC,KAAK,GAAG,IAAIW,WAAW,CAAC,EAAE,CAAC;IACjC,OAAOX,KAAK,CAACC,KAAK,CAACF,MAAM,CAA4B;GACtD,MAAM;IACL,OAAO,IAAIY,WAAW,CAACZ,MAAM,CAAC;;AAElC;AAoBA;;;;;;;;;AASA,SAASkC,aAAaA,CAACpB,IAAY,EAAEqB,GAAW;EAC9C,OAAOrB,IAAI,GAAG,CAAC,EAAE;IACfA,IAAI,IAAIqB,GAAG;;EAEb,OAAOrB,IAAI;AACb;AAEA,SAASsB,QAAQA,CAACjE,CAAS,EAAEc,CAAS,EAAEoD,IAA6B;EACnE,IAAIlE,CAAC,CAACd,KAAK,CAACd,MAAM,GAAG,CAAC,IAAI0C,CAAC,CAAC5B,KAAK,CAACd,MAAM,GAAG,CAAC,EAAE;IAC5C,MAAM,IAAIf,mBAAmB,CACzB,kEAAkE,CAAC;;EAEzEN,GAAG,CAACG,IAAI,CAACiH,MAAM,CACXnE,CAAC,CAACd,KAAK,CAACd,MAAM,IAAI,CAAC,EACnB,MAAM,8CAA8C,GAChD,WAAW4B,CAAC,CAACd,KAAK,CAACd,MAAM,EAAE,CAAC;EACpCrB,GAAG,CAACG,IAAI,CAACiH,MAAM,CACXnE,CAAC,CAACd,KAAK,CAACd,MAAM,IAAI,CAAC,EACnB,MAAM,8CAA8C,GAChD,WAAW0C,CAAC,CAAC5B,KAAK,CAACd,MAAM,EAAE,CAAC;EAEpC,IAAI,OAAO8F,IAAI,KAAK,QAAQ,EAAE;IAC5BA,IAAI,GAAG,CAACA,IAAI,EAAEA,IAAI,CAAC;;EAGrB,IAAIlE,CAAC,CAACoE,KAAK,KAAK,WAAW,IAAItD,CAAC,CAACsD,KAAK,KAAK,WAAW,EAAE;IACtD,MAAM,IAAI/G,mBAAmB,CACzB,6DAA6D,CAAC;;EAGpE,MAAM4C,KAAK,GAAGD,CAAC,CAACd,KAAK,CAACd,MAAM;EAC5B,MAAM2C,KAAK,GAAGD,CAAC,CAAC5B,KAAK,CAACd,MAAM;EAC5B,IAAI8F,IAAI,IAAI,IAAI,EAAE;IAChB;IACAA,IAAI,GAAG,CAACjE,KAAK,GAAG,CAAC,EAAEc,KAAK,GAAG,CAAC,CAAC;;EAE/B,MAAMsD,SAAS,GAAGH,IAAwB;EAE1C,OAAOnH,GAAG,CAACE,IAAI,CAAC,MAAK;IACnB,IAAIqH,IAAY;IAChB,IAAIrE,KAAK,GAAGc,KAAK,EAAE;MACjBuD,IAAI,GAAGrE,KAAK,GAAGc,KAAK;MACpB,MAAMwD,SAAS,GAAU,EAAE;MAC3B,KAAK,IAAI/F,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG8F,IAAI,EAAE,EAAE9F,CAAC,EAAE;QAC7B+F,SAAS,CAAC7F,IAAI,CAAC,CAAC,CAAC;;MAEnBoC,CAAC,GAAG/D,GAAG,CAAC0D,OAAO,CAACK,CAAC,EAAEA,CAAC,CAAC5B,KAAK,CAACqB,MAAM,CAACgE,SAAS,CAAC,CAAC;KAC9C,MAAM,IAAIxD,KAAK,GAAGd,KAAK,EAAE;MACxBqE,IAAI,GAAGvD,KAAK,GAAGd,KAAK;MACpB,MAAMsE,SAAS,GAAU,EAAE;MAC3B,KAAK,IAAI/F,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG8F,IAAI,EAAE,EAAE9F,CAAC,EAAE;QAC7B+F,SAAS,CAAC7F,IAAI,CAAC,CAAC,CAAC;;MAEnBsB,CAAC,GAAGjD,GAAG,CAAC0D,OAAO,CAACT,CAAC,EAAEA,CAAC,CAACd,KAAK,CAACqB,MAAM,CAACgE,SAAS,CAAC,CAAC;KAC9C,MAAM;MACLD,IAAI,GAAG,CAAC;;IAGV,IAAIE,GAAW;IACf,IAAIxE,CAAC,CAACd,KAAK,CAACd,MAAM,KAAK,CAAC,IAAI0C,CAAC,CAAC5B,KAAK,CAACd,MAAM,KAAK,CAAC,EAAE;MAChD,IAAIiG,SAAS,CAAC,CAAC,CAAC,KAAKA,SAAS,CAAC,CAAC,CAAC,EAAE;QACjCG,GAAG,GAAGzH,GAAG,CAAC0H,GAAG,CAAC1H,GAAG,CAACkF,GAAG,CAACjC,CAAC,EAAEc,CAAC,CAAC,EAAEuD,SAAS,CAAC,CAAC,CAAC,CAAC;OAC3C,MAAM;QACLG,GAAG,GAAGzH,GAAG,CAAC0H,GAAG,CAAC1H,GAAG,CAACkF,GAAG,CAAClF,GAAG,CAAC4D,SAAS,CAACX,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAEc,CAAC,CAAC,EAAEuD,SAAS,CAAC,CAAC,CAAC,CAAC;;KAEpE,MAAM;MACL,MAAMK,IAAI,GAAGL,SAAS,CAAC,CAAC,CAAC,KAAKrE,CAAC,CAACd,KAAK,CAACd,MAAM,GAAG,CAAC;MAChD,MAAMuG,IAAI,GAAGN,SAAS,CAAC,CAAC,CAAC,KAAKvD,CAAC,CAAC5B,KAAK,CAACd,MAAM,GAAG,CAAC;MAChDoG,GAAG,GAAGzH,GAAG,CAAC6H,MAAM,CAAC5E,CAAC,EAAEc,CAAC,EAAE4D,IAAI,EAAEC,IAAI,CAAC;;IAGpC,IAAIL,IAAI,GAAG,CAAC,EAAE;MACZ,IAAIO,GAAW;MACf,IAAI5E,KAAK,GAAGc,KAAK,EAAE;QACjB8D,GAAG,GAAG5E,KAAK,GAAGc,KAAK,GAAG,CAAC;OACxB,MAAM;QACL8D,GAAG,GAAG5E,KAAK,GAAG,CAAC;;MAEjB,MAAM6E,WAAW,GAAa,EAAE;MAChC,KAAK,IAAItG,CAAC,GAAGqG,GAAG,EAAErG,CAAC,GAAGqG,GAAG,GAAGP,IAAI,EAAE,EAAE9F,CAAC,EAAE;QACrCsG,WAAW,CAACpG,IAAI,CAACF,CAAC,CAAC;;MAErBgG,GAAG,GAAGzH,GAAG,CAACgI,OAAO,CAACP,GAAG,EAAEM,WAAW,CAAC;;IAErC,IAAIN,GAAG,CAACtF,KAAK,CAACd,MAAM,KAAK,CAAC,EAAE;MAC1BoG,GAAG,GAAGzH,GAAG,CAACmD,UAAU,CAACsE,GAAG,EAAE,CAAC,CAAC;;IAE9B,OAAOA,GAAG;EACZ,CAAC,CAAC;AACJ;AAEA,MAAaQ,GAAI,SAAQrH,KAAK;EAO5BC,YAAYC,IAAkB;IAC5B,KAAK,CAACA,IAAI,CAAC;IACX,IAAI,CAACqG,IAAI,GAAGrG,IAAI,CAACqG,IAAI;IACrB,IAAI,CAACe,SAAS,GAAGpH,IAAI,CAACoH,SAAS,IAAI,IAAI,GAAG,KAAK,GAAGpH,IAAI,CAACoH,SAAS;IAChE,IAAI,CAACnH,eAAe,GAAG,IAAI;IAC3B,IAAI,CAACyB,eAAe,GAAG,KAAK;EAC9B;EAESV,KAAKA,CAACC,UAAyB;IACtC/B,GAAG,CAACG,IAAI,CAACiH,MAAM,CACXpF,KAAK,CAACC,OAAO,CAACF,UAAU,CAAC,IAAIA,UAAU,CAACV,MAAM,KAAK,CAAC,IAChDW,KAAK,CAACC,OAAO,CAACF,UAAU,CAAC,CAAC,CAAC,CAAC,IAAIC,KAAK,CAACC,OAAO,CAACF,UAAU,CAAC,CAAC,CAAC,CAAC,EAChE,MAAM,+DAA+D,CAAC;IAC1E,MAAMZ,MAAM,GAAGY,UAAU,CAAC,CAAC,CAAU;IACrC,MAAMX,MAAM,GAAGW,UAAU,CAAC,CAAC,CAAU;IACrC,IAAIZ,MAAM,CAACE,MAAM,GAAG,CAAC,IAAID,MAAM,CAACC,MAAM,GAAG,CAAC,EAAE;MAC1C,MAAM,IAAIf,mBAAmB,CACzB,8DAA8D,CAAC;;IAGrE,MAAM6G,IAAI,GAAG,IAAI,CAACgB,aAAa,CAAChH,MAAM,EAAEC,MAAM,CAAC;IAC/C,IAAID,MAAM,CAACgG,IAAI,CAAC,CAAC,CAAC,CAAC,KAAK/F,MAAM,CAAC+F,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE;MACvC,MAAM,IAAI5G,UAAU,CAChB,6BAA6B,GAC7B,GAAGY,MAAM,CAACgG,IAAI,CAAC,CAAC,CAAC,CAAC,QAAQ/F,MAAM,CAAC+F,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;;EAEpD;EAEmBnG,aAAaA,CAACC,MAAgB;IAC/C,IAAIA,MAAM,CAACI,MAAM,KAAK,CAAC,EAAE;MACvB,MAAM,IAAId,UAAU,CAChB,oDAAoD,GACpD,gBAAgBU,MAAM,CAACI,MAAM,YAAY,CAAC;;IAGhD,IAAI+G,EAAE,GAAGnH,MAAM,CAAC,CAAC,CAAC;IAClB,IAAIoH,EAAE,GAAGpH,MAAM,CAAC,CAAC,CAAC;IAClB,IAAIkG,IAAsB;IAC1B,IAAI,CAACnF,KAAK,CAACC,OAAO,CAAC,IAAI,CAACkF,IAAI,CAAC,EAAE;MAC7BA,IAAI,GAAG,CACLH,aAAa,CAAC,IAAI,CAACG,IAAI,EAAEiB,EAAE,CAACjG,KAAK,CAACd,MAAM,CAAC,EACzC2F,aAAa,CAAC,IAAI,CAACG,IAAI,EAAEkB,EAAE,CAAClG,KAAK,CAACd,MAAM,CAAC,CAC1C;KACF,MAAM;MACL8F,IAAI,GAAG,IAAI,CAACA,IAAI,CAAC7E,GAAG,CACT,CAACsD,IAAI,EAAEnE,CAAC,KAAKuF,aAAa,CACtBpB,IAAI,EAAE3E,MAAM,CAACQ,CAAC,CAAC,CAACU,KAAK,CAACd,MAAM,CAAC,CAAqB;;IAEnE,IAAI,IAAI,CAAC6G,SAAS,EAAE;MAClBE,EAAE,GAAG5H,WAAW,CAAC4H,EAAE,EAAEjB,IAAI,CAAC,CAAC,CAAC,CAAC;MAC7BkB,EAAE,GAAG7H,WAAW,CAAC6H,EAAE,EAAElB,IAAI,CAAC,CAAC,CAAC,CAAC;;IAE/B,OAAOD,QAAQ,CAACkB,EAAE,EAAEC,EAAE,EAAElB,IAAI,CAAC;EAC/B;EAEQgB,aAAaA,CAAChH,MAAa,EAAEC,MAAa;IAChD,IAAI+F,IAAc;IAClB,IAAI,CAACnF,KAAK,CAACC,OAAO,CAAC,IAAI,CAACkF,IAAI,CAAC,EAAE;MAC7B;MACAA,IAAI,GAAG,CACLH,aAAa,CAAC,IAAI,CAACG,IAAI,EAAEhG,MAAM,CAACE,MAAM,CAAC,EACvC2F,aAAa,CAAC,IAAI,CAACG,IAAI,EAAE/F,MAAM,CAACC,MAAM,CAAC,CACxC;KACF,MAAM;MACL;MACA8F,IAAI,GAAG,IAAI,CAACA,IAAI;;IAElB,OAAOA,IAAI;EACb;EAESjD,kBAAkBA,CAACnC,UAAyB;IACnD/B,GAAG,CAACG,IAAI,CAACiH,MAAM,CACXpF,KAAK,CAACC,OAAO,CAACF,UAAU,CAAC,IAAIA,UAAU,CAACV,MAAM,KAAK,CAAC,IAChDW,KAAK,CAACC,OAAO,CAACF,UAAU,CAAC,CAAC,CAAC,CAAC,IAAIC,KAAK,CAACC,OAAO,CAACF,UAAU,CAAC,CAAC,CAAC,CAAC,EAChE,MAAM,+DAA+D,CAAC;IAC1E,MAAMZ,MAAM,GAAIY,UAAU,CAAC,CAAC,CAAW,CAACR,KAAK,EAAE;IAC/C,MAAMH,MAAM,GAAIW,UAAU,CAAC,CAAC,CAAW,CAACR,KAAK,EAAE;IAC/C,IAAIJ,MAAM,CAACE,MAAM,GAAG,CAAC,IAAID,MAAM,CAACC,MAAM,GAAG,CAAC,EAAE;MAC1C,MAAM,IAAIf,mBAAmB,CACzB,8DAA8D,CAAC;;IAGrE,MAAM6G,IAAI,GAAG,IAAI,CAACgB,aAAa,CAAChH,MAAM,EAAEC,MAAM,CAAC;IAC/CD,MAAM,CAAC6E,MAAM,CAACmB,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;IACzB/F,MAAM,CAAC4E,MAAM,CAACmB,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;IACzB/F,MAAM,CAAC4E,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC;IACnB,MAAM1E,WAAW,GAAGH,MAAM,CAACqC,MAAM,CAACpC,MAAM,CAAC;IACzC,IAAIE,WAAW,CAACD,MAAM,KAAK,CAAC,EAAE;MAC5BC,WAAW,CAACK,IAAI,CAAC,CAAC,CAAC;;IAErB,OAAOL,WAAW;EACpB;EAES6C,WAAWA,CAAClD,MAAuB,EAAEmD,IAAsB;IAElE,OAAO,IAAI;EACb;EAESwC,SAASA,CAAA;IAChB,MAAM9B,MAAM,GAA6B;MACvC,MAAM,EAAE,IAAI,CAACqC,IAAI;MACjB,WAAW,EAAE,IAAI,CAACe;KACnB;IACD,MAAMrB,UAAU,GAAG,KAAK,CAACD,SAAS,EAAE;IACpCE,MAAM,CAACC,MAAM,CAACjC,MAAM,EAAE+B,UAAU,CAAC;IACjC,OAAO/B,MAAM;EACf;;AAhHA;AACOmD,GAAA,CAAArD,SAAS,GAAG,KAAK;SAFbqD,GAAG;AAmHhBhI,aAAa,CAAC4E,aAAa,CAACoD,GAAG,CAAC;AAEhC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}