{"ast": null, "code": "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { LogicalAnd } from '@tensorflow/tfjs-core';\nimport { binaryKernelFunc } from '../kernel_utils/kernel_funcs_utils';\nconst LOGICAL_AND = \"return float(a >= 1.0 && b >= 1.0);\";\nconst LOGICAL_AND_PACKED = \"\\n  return vec4(\\n    vec4(greaterThanEqual(a, vec4(1.0))) *\\n    vec4(greaterThanEqual(b, vec4(1.0))));\\n\";\nexport const logicalAnd = binaryKernelFunc({\n  opSnippet: LOGICAL_AND,\n  packedOpSnippet: LOGICAL_AND_PACKED,\n  dtype: 'bool'\n});\nexport const logicalAndConfig = {\n  kernelName: LogicalAnd,\n  backendName: 'webgl',\n  kernelFunc: logicalAnd\n};", "map": {"version": 3, "names": ["LogicalAnd", "binaryKernelFunc", "LOGICAL_AND", "LOGICAL_AND_PACKED", "logicalAnd", "opSnippet", "packedOpSnippet", "dtype", "logicalAndConfig", "kernelName", "backendName", "kernelFunc"], "sources": ["C:\\tfjs-backend-webgl\\src\\kernels\\LogicalAnd.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {KernelConfig, KernelFunc, LogicalAnd} from '@tensorflow/tfjs-core';\n\nimport {binaryKernelFunc} from '../kernel_utils/kernel_funcs_utils';\n\nconst LOGICAL_AND = `return float(a >= 1.0 && b >= 1.0);`;\nconst LOGICAL_AND_PACKED = `\n  return vec4(\n    vec4(greaterThanEqual(a, vec4(1.0))) *\n    vec4(greaterThanEqual(b, vec4(1.0))));\n`;\n\nexport const logicalAnd = binaryKernelFunc({\n  opSnippet: LOGICAL_AND,\n  packedOpSnippet: LOGICAL_AND_PACKED,\n  dtype: 'bool'\n});\n\nexport const logicalAndConfig: KernelConfig = {\n  kernelName: LogicalAnd,\n  backendName: 'webgl',\n  kernelFunc: logicalAnd as unknown as KernelFunc\n};\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAiBA,SAAkCA,UAAU,QAAO,uBAAuB;AAE1E,SAAQC,gBAAgB,QAAO,oCAAoC;AAEnE,MAAMC,WAAW,wCAAwC;AACzD,MAAMC,kBAAkB,+GAIvB;AAED,OAAO,MAAMC,UAAU,GAAGH,gBAAgB,CAAC;EACzCI,SAAS,EAAEH,WAAW;EACtBI,eAAe,EAAEH,kBAAkB;EACnCI,KAAK,EAAE;CACR,CAAC;AAEF,OAAO,MAAMC,gBAAgB,GAAiB;EAC5CC,UAAU,EAAET,UAAU;EACtBU,WAAW,EAAE,OAAO;EACpBC,UAAU,EAAEP;CACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}