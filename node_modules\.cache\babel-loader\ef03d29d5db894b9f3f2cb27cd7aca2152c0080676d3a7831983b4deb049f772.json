{"ast": null, "code": "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n/*\n * base.ts contains all the exports from tfjs-backend-cpu\n * without auto-kernel registration\n */\nimport { registerBackend } from '@tensorflow/tfjs-core';\nimport { MathBackendCPU } from './backend_cpu';\nimport * as shared from './shared';\nexport { MathBackendCPU } from './backend_cpu';\nexport { version as version_cpu } from './version';\nexport { shared };\n// Side effects for default initialization of MathBackendCPU\nregisterBackend('cpu', () => new MathBackendCPU(), 1 /* priority */);", "map": {"version": 3, "names": ["registerBackend", "MathBackendCPU", "shared", "version", "version_cpu"], "sources": ["C:\\tfjs-backend-cpu\\src\\base.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\n/*\n * base.ts contains all the exports from tfjs-backend-cpu\n * without auto-kernel registration\n */\nimport {registerBackend} from '@tensorflow/tfjs-core';\nimport {MathBackendCPU} from './backend_cpu';\nimport * as shared from './shared';\n\nexport {MathBackendCPU} from './backend_cpu';\nexport {version as version_cpu} from './version';\nexport {shared};\n\n// Side effects for default initialization of MathBackendCPU\nregisterBackend('cpu', () => new MathBackendCPU(), 1 /* priority */);\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAiBA;;;;AAIA,SAAQA,eAAe,QAAO,uBAAuB;AACrD,SAAQC,cAAc,QAAO,eAAe;AAC5C,OAAO,KAAKC,MAAM,MAAM,UAAU;AAElC,SAAQD,cAAc,QAAO,eAAe;AAC5C,SAAQE,OAAO,IAAIC,WAAW,QAAO,WAAW;AAChD,SAAQF,MAAM;AAEd;AACAF,eAAe,CAAC,KAAK,EAAE,MAAM,IAAIC,cAAc,EAAE,EAAE,CAAC,CAAC,cAAc,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}