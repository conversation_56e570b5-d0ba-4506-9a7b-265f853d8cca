{"ast": null, "code": "'use strict';\n\nmodule.exports = function parseBytea(input) {\n  if (/^\\\\x/.test(input)) {\n    // new 'hex' style response (pg >9.0)\n    return new Buffer(input.substr(2), 'hex');\n  }\n  var output = '';\n  var i = 0;\n  while (i < input.length) {\n    if (input[i] !== '\\\\') {\n      output += input[i];\n      ++i;\n    } else {\n      if (/[0-7]{3}/.test(input.substr(i + 1, 3))) {\n        output += String.fromCharCode(parseInt(input.substr(i + 1, 3), 8));\n        i += 4;\n      } else {\n        var backslashes = 1;\n        while (i + backslashes < input.length && input[i + backslashes] === '\\\\') {\n          backslashes++;\n        }\n        for (var k = 0; k < Math.floor(backslashes / 2); ++k) {\n          output += '\\\\';\n        }\n        i += Math.floor(backslashes / 2) * 2;\n      }\n    }\n  }\n  return new Buffer(output, 'binary');\n};", "map": {"version": 3, "names": ["module", "exports", "parseBytea", "input", "test", "<PERSON><PERSON><PERSON>", "substr", "output", "i", "length", "String", "fromCharCode", "parseInt", "backslashes", "k", "Math", "floor"], "sources": ["C:/tmsft/node_modules/postgres-bytea/index.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = function parseBytea (input) {\n  if (/^\\\\x/.test(input)) {\n    // new 'hex' style response (pg >9.0)\n    return new Buffer(input.substr(2), 'hex')\n  }\n  var output = ''\n  var i = 0\n  while (i < input.length) {\n    if (input[i] !== '\\\\') {\n      output += input[i]\n      ++i\n    } else {\n      if (/[0-7]{3}/.test(input.substr(i + 1, 3))) {\n        output += String.fromCharCode(parseInt(input.substr(i + 1, 3), 8))\n        i += 4\n      } else {\n        var backslashes = 1\n        while (i + backslashes < input.length && input[i + backslashes] === '\\\\') {\n          backslashes++\n        }\n        for (var k = 0; k < Math.floor(backslashes / 2); ++k) {\n          output += '\\\\'\n        }\n        i += Math.floor(backslashes / 2) * 2\n      }\n    }\n  }\n  return new Buffer(output, 'binary')\n}\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,OAAO,GAAG,SAASC,UAAUA,CAAEC,KAAK,EAAE;EAC3C,IAAI,MAAM,CAACC,IAAI,CAACD,KAAK,CAAC,EAAE;IACtB;IACA,OAAO,IAAIE,MAAM,CAACF,KAAK,CAACG,MAAM,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC;EAC3C;EACA,IAAIC,MAAM,GAAG,EAAE;EACf,IAAIC,CAAC,GAAG,CAAC;EACT,OAAOA,CAAC,GAAGL,KAAK,CAACM,MAAM,EAAE;IACvB,IAAIN,KAAK,CAACK,CAAC,CAAC,KAAK,IAAI,EAAE;MACrBD,MAAM,IAAIJ,KAAK,CAACK,CAAC,CAAC;MAClB,EAAEA,CAAC;IACL,CAAC,MAAM;MACL,IAAI,UAAU,CAACJ,IAAI,CAACD,KAAK,CAACG,MAAM,CAACE,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE;QAC3CD,MAAM,IAAIG,MAAM,CAACC,YAAY,CAACC,QAAQ,CAACT,KAAK,CAACG,MAAM,CAACE,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAClEA,CAAC,IAAI,CAAC;MACR,CAAC,MAAM;QACL,IAAIK,WAAW,GAAG,CAAC;QACnB,OAAOL,CAAC,GAAGK,WAAW,GAAGV,KAAK,CAACM,MAAM,IAAIN,KAAK,CAACK,CAAC,GAAGK,WAAW,CAAC,KAAK,IAAI,EAAE;UACxEA,WAAW,EAAE;QACf;QACA,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGC,IAAI,CAACC,KAAK,CAACH,WAAW,GAAG,CAAC,CAAC,EAAE,EAAEC,CAAC,EAAE;UACpDP,MAAM,IAAI,IAAI;QAChB;QACAC,CAAC,IAAIO,IAAI,CAACC,KAAK,CAACH,WAAW,GAAG,CAAC,CAAC,GAAG,CAAC;MACtC;IACF;EACF;EACA,OAAO,IAAIR,MAAM,CAACE,MAAM,EAAE,QAAQ,CAAC;AACrC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}