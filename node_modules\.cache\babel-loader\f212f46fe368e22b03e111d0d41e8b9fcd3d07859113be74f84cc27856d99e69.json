{"ast": null, "code": "import restArguments from './restArguments.js';\n\n// Delays a function for the given number of milliseconds, and then calls\n// it with the arguments supplied.\nexport default restArguments(function (func, wait, args) {\n  return setTimeout(function () {\n    return func.apply(null, args);\n  }, wait);\n});", "map": {"version": 3, "names": ["restArguments", "func", "wait", "args", "setTimeout", "apply"], "sources": ["C:/tmsft/node_modules/underscore/modules/delay.js"], "sourcesContent": ["import restArguments from './restArguments.js';\n\n// Delays a function for the given number of milliseconds, and then calls\n// it with the arguments supplied.\nexport default restArguments(function(func, wait, args) {\n  return setTimeout(function() {\n    return func.apply(null, args);\n  }, wait);\n});\n"], "mappings": "AAAA,OAAOA,aAAa,MAAM,oBAAoB;;AAE9C;AACA;AACA,eAAeA,aAAa,CAAC,UAASC,IAAI,EAAEC,IAAI,EAAEC,IAAI,EAAE;EACtD,OAAOC,UAAU,CAAC,YAAW;IAC3B,OAAOH,IAAI,CAACI,KAAK,CAAC,IAAI,EAAEF,IAAI,CAAC;EAC/B,CAAC,EAAED,IAAI,CAAC;AACV,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}