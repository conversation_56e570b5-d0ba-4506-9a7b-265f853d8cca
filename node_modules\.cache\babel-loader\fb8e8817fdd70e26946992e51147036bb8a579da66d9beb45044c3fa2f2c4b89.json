{"ast": null, "code": "/**\n * @license\n * Copyright 2018 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { dispose } from '../globals';\nimport { variableGrads } from '../gradients';\nimport { scalar } from '../ops/ops';\nimport { Serializable } from '../serialization';\n/** @doc {heading: 'Training', subheading: 'Classes', namespace: 'train'} */\nexport class Optimizer extends Serializable {\n  /**\n   * Executes `f()` and minimizes the scalar output of `f()` by computing\n   * gradients of y with respect to the list of trainable variables provided by\n   * `varList`. If no list is provided, it defaults to all trainable variables.\n   *\n   * @param f The function to execute and whose output to minimize.\n   * @param returnCost Whether to return the scalar cost value produced by\n   * executing `f()`.\n   * @param varList An optional list of variables to update. If specified, only\n   * the trainable variables in varList will be updated by minimize. Defaults to\n   * all trainable variables.\n   *\n   * @doc {heading: 'Training', subheading: 'Optimizers'}\n   */\n  minimize(f, returnCost = false, varList) {\n    const {\n      value,\n      grads\n    } = this.computeGradients(f, varList);\n    if (varList != null) {\n      const gradArray = varList.map(v => ({\n        name: v.name,\n        tensor: grads[v.name]\n      }));\n      this.applyGradients(gradArray);\n    } else {\n      this.applyGradients(grads);\n    }\n    // Dispose gradients.\n    dispose(grads);\n    if (returnCost) {\n      return value;\n    } else {\n      value.dispose();\n      return null;\n    }\n  }\n  /**\n   * The number of iterations that this optimizer instance has been invoked for.\n   */\n  get iterations() {\n    if (this.iterations_ == null) {\n      this.iterations_ = 0;\n    }\n    return this.iterations_;\n  }\n  incrementIterations() {\n    this.iterations_ = this.iterations + 1;\n  }\n  /**\n   * Executes f() and computes the gradient of the scalar output of f() with\n   * respect to the list of trainable variables provided by `varList`. If no\n   * list is provided, it defaults to all trainable variables.\n   *\n   * @param f The function to execute and whose output to use for computing\n   * gradients with respect to variables.\n   * @param varList An optional list of variables to compute gradients with\n   * respect to. If specified, only the trainable variables in varList will have\n   * gradients computed with respect to. Defaults to all trainable variables.\n   *\n   * @doc {heading: 'Training', subheading: 'Optimizers'}\n   */\n  computeGradients(f, varList) {\n    return variableGrads(f, varList);\n  }\n  /**\n   * Dispose the variables (if any) owned by this optimizer instance.\n   */\n  dispose() {\n    if (this.iterations_ != null) {\n      dispose(this.iterations_);\n    }\n  }\n  async saveIterations() {\n    if (this.iterations_ == null) {\n      this.iterations_ = 0;\n    }\n    return {\n      name: 'iter',\n      // TODO(cais): Use 'int64' type when available.\n      tensor: scalar(this.iterations_, 'int32')\n    };\n  }\n  async getWeights() {\n    throw new Error('getWeights() is not implemented for this optimizer yet.');\n  }\n  async setWeights(weightValues) {\n    throw new Error(`setWeights() is not implemented for this optimizer class ` + `${this.getClassName()}`);\n  }\n  /**\n   * Extract the first element of the weight values and set it\n   * as the iterations counter variable of this instance of optimizer.\n   *\n   * @param weightValues\n   * @returns Weight values with the first element consumed and excluded.\n   */\n  async extractIterations(weightValues) {\n    this.iterations_ = (await weightValues[0].tensor.data())[0];\n    return weightValues.slice(1);\n  }\n}\nObject.defineProperty(Optimizer, Symbol.hasInstance, {\n  value: instance => {\n    return instance.minimize != null && instance.computeGradients != null && instance.applyGradients != null;\n  }\n});", "map": {"version": 3, "names": ["dispose", "variableGrads", "scalar", "Serializable", "Optimizer", "minimize", "f", "returnCost", "varList", "value", "grads", "computeGradients", "gradArray", "map", "v", "name", "tensor", "applyGradients", "iterations", "iterations_", "incrementIterations", "saveIterations", "getWeights", "Error", "setWeights", "weightValues", "getClassName", "extractIterations", "data", "slice", "Object", "defineProperty", "Symbol", "hasInstance", "instance"], "sources": ["C:\\tfjs-core\\src\\optimizers\\optimizer.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2018 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {dispose} from '../globals';\nimport {variableGrads} from '../gradients';\nimport {scalar} from '../ops/ops';\nimport {Serializable} from '../serialization';\nimport {Scalar, Variable} from '../tensor';\nimport {NamedTensor, NamedTensorMap} from '../tensor_types';\n\n/**\n * A variable that belongs to an optimizer.\n *\n * The `originalName` field is required for keeping track of the canonical\n * name of the variable, which is usually the name of the model weight that\n * the variable is related to plus a suffix, e.g., 'dense1/kernel/momentum'.\n * The name of the `Variable` object itself cannot be used directly due to\n * possible deduplication: Every `Variable` must have a unique name but more\n * than one optimizer objects of the same type may be created for the same model\n * or the same `Variable`.\n */\nexport interface OptimizerVariable {\n  originalName: string;\n  variable: Variable;\n}\n\n/** @doc {heading: 'Training', subheading: 'Classes', namespace: 'train'} */\nexport abstract class Optimizer extends Serializable {\n  protected iterations_: number;\n\n  /**\n   * Executes `f()` and minimizes the scalar output of `f()` by computing\n   * gradients of y with respect to the list of trainable variables provided by\n   * `varList`. If no list is provided, it defaults to all trainable variables.\n   *\n   * @param f The function to execute and whose output to minimize.\n   * @param returnCost Whether to return the scalar cost value produced by\n   * executing `f()`.\n   * @param varList An optional list of variables to update. If specified, only\n   * the trainable variables in varList will be updated by minimize. Defaults to\n   * all trainable variables.\n   *\n   * @doc {heading: 'Training', subheading: 'Optimizers'}\n   */\n  minimize(f: () => Scalar, returnCost = false, varList?: Variable[]): Scalar\n      |null {\n    const {value, grads} = this.computeGradients(f, varList);\n\n    if (varList != null) {\n      const gradArray: NamedTensor[] =\n          varList.map(v => ({name: v.name, tensor: grads[v.name]}));\n      this.applyGradients(gradArray);\n    } else {\n      this.applyGradients(grads);\n    }\n\n    // Dispose gradients.\n    dispose(grads);\n\n    if (returnCost) {\n      return value;\n    } else {\n      value.dispose();\n      return null;\n    }\n  }\n\n  /**\n   * The number of iterations that this optimizer instance has been invoked for.\n   */\n  get iterations(): number {\n    if (this.iterations_ == null) {\n      this.iterations_ = 0;\n    }\n    return this.iterations_;\n  }\n\n  protected incrementIterations() {\n    this.iterations_ = this.iterations + 1;\n  }\n\n  /**\n   * Executes f() and computes the gradient of the scalar output of f() with\n   * respect to the list of trainable variables provided by `varList`. If no\n   * list is provided, it defaults to all trainable variables.\n   *\n   * @param f The function to execute and whose output to use for computing\n   * gradients with respect to variables.\n   * @param varList An optional list of variables to compute gradients with\n   * respect to. If specified, only the trainable variables in varList will have\n   * gradients computed with respect to. Defaults to all trainable variables.\n   *\n   * @doc {heading: 'Training', subheading: 'Optimizers'}\n   */\n  computeGradients(f: () => Scalar, varList?: Variable[]):\n      {value: Scalar, grads: NamedTensorMap} {\n    return variableGrads(f, varList);\n  }\n\n  /**\n   * Updates variables by using the computed gradients.\n   *\n   * @param variableGradients A mapping of variable name to its gradient value.\n   *\n   * @doc {heading: 'Training', subheading: 'Optimizers'}\n   */\n  abstract applyGradients(variableGradients: NamedTensorMap|\n                          NamedTensor[]): void;\n\n  /**\n   * Dispose the variables (if any) owned by this optimizer instance.\n   */\n  dispose(): void {\n    if (this.iterations_ != null) {\n      dispose(this.iterations_);\n    }\n  }\n\n  async saveIterations(): Promise<NamedTensor> {\n    if (this.iterations_ == null) {\n      this.iterations_ = 0;\n    }\n    return {\n      name: 'iter',  // Named for Python compatibility.\n      // TODO(cais): Use 'int64' type when available.\n      tensor: scalar(this.iterations_, 'int32')\n    };\n  }\n\n  async getWeights(): Promise<NamedTensor[]> {\n    throw new Error('getWeights() is not implemented for this optimizer yet.');\n  }\n\n  async setWeights(weightValues: NamedTensor[]): Promise<void> {\n    throw new Error(\n        `setWeights() is not implemented for this optimizer class ` +\n        `${this.getClassName()}`);\n  }\n\n  /**\n   * Extract the first element of the weight values and set it\n   * as the iterations counter variable of this instance of optimizer.\n   *\n   * @param weightValues\n   * @returns Weight values with the first element consumed and excluded.\n   */\n  protected async extractIterations(weightValues: NamedTensor[]):\n      Promise<NamedTensor[]> {\n    this.iterations_ = (await weightValues[0].tensor.data())[0];\n    return weightValues.slice(1);\n  }\n}\n\nObject.defineProperty(Optimizer, Symbol.hasInstance, {\n  value: (instance: Optimizer) => {\n    return instance.minimize != null && instance.computeGradients != null &&\n        instance.applyGradients != null;\n  }\n});\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAiBA,SAAQA,OAAO,QAAO,YAAY;AAClC,SAAQC,aAAa,QAAO,cAAc;AAC1C,SAAQC,MAAM,QAAO,YAAY;AACjC,SAAQC,YAAY,QAAO,kBAAkB;AAoB7C;AACA,OAAM,MAAgBC,SAAU,SAAQD,YAAY;EAGlD;;;;;;;;;;;;;;EAcAE,QAAQA,CAACC,CAAe,EAAEC,UAAU,GAAG,KAAK,EAAEC,OAAoB;IAEhE,MAAM;MAACC,KAAK;MAAEC;IAAK,CAAC,GAAG,IAAI,CAACC,gBAAgB,CAACL,CAAC,EAAEE,OAAO,CAAC;IAExD,IAAIA,OAAO,IAAI,IAAI,EAAE;MACnB,MAAMI,SAAS,GACXJ,OAAO,CAACK,GAAG,CAACC,CAAC,KAAK;QAACC,IAAI,EAAED,CAAC,CAACC,IAAI;QAAEC,MAAM,EAAEN,KAAK,CAACI,CAAC,CAACC,IAAI;MAAC,CAAC,CAAC,CAAC;MAC7D,IAAI,CAACE,cAAc,CAACL,SAAS,CAAC;KAC/B,MAAM;MACL,IAAI,CAACK,cAAc,CAACP,KAAK,CAAC;;IAG5B;IACAV,OAAO,CAACU,KAAK,CAAC;IAEd,IAAIH,UAAU,EAAE;MACd,OAAOE,KAAK;KACb,MAAM;MACLA,KAAK,CAACT,OAAO,EAAE;MACf,OAAO,IAAI;;EAEf;EAEA;;;EAGA,IAAIkB,UAAUA,CAAA;IACZ,IAAI,IAAI,CAACC,WAAW,IAAI,IAAI,EAAE;MAC5B,IAAI,CAACA,WAAW,GAAG,CAAC;;IAEtB,OAAO,IAAI,CAACA,WAAW;EACzB;EAEUC,mBAAmBA,CAAA;IAC3B,IAAI,CAACD,WAAW,GAAG,IAAI,CAACD,UAAU,GAAG,CAAC;EACxC;EAEA;;;;;;;;;;;;;EAaAP,gBAAgBA,CAACL,CAAe,EAAEE,OAAoB;IAEpD,OAAOP,aAAa,CAACK,CAAC,EAAEE,OAAO,CAAC;EAClC;EAYA;;;EAGAR,OAAOA,CAAA;IACL,IAAI,IAAI,CAACmB,WAAW,IAAI,IAAI,EAAE;MAC5BnB,OAAO,CAAC,IAAI,CAACmB,WAAW,CAAC;;EAE7B;EAEA,MAAME,cAAcA,CAAA;IAClB,IAAI,IAAI,CAACF,WAAW,IAAI,IAAI,EAAE;MAC5B,IAAI,CAACA,WAAW,GAAG,CAAC;;IAEtB,OAAO;MACLJ,IAAI,EAAE,MAAM;MACZ;MACAC,MAAM,EAAEd,MAAM,CAAC,IAAI,CAACiB,WAAW,EAAE,OAAO;KACzC;EACH;EAEA,MAAMG,UAAUA,CAAA;IACd,MAAM,IAAIC,KAAK,CAAC,yDAAyD,CAAC;EAC5E;EAEA,MAAMC,UAAUA,CAACC,YAA2B;IAC1C,MAAM,IAAIF,KAAK,CACX,2DAA2D,GAC3D,GAAG,IAAI,CAACG,YAAY,EAAE,EAAE,CAAC;EAC/B;EAEA;;;;;;;EAOU,MAAMC,iBAAiBA,CAACF,YAA2B;IAE3D,IAAI,CAACN,WAAW,GAAG,CAAC,MAAMM,YAAY,CAAC,CAAC,CAAC,CAACT,MAAM,CAACY,IAAI,EAAE,EAAE,CAAC,CAAC;IAC3D,OAAOH,YAAY,CAACI,KAAK,CAAC,CAAC,CAAC;EAC9B;;AAGFC,MAAM,CAACC,cAAc,CAAC3B,SAAS,EAAE4B,MAAM,CAACC,WAAW,EAAE;EACnDxB,KAAK,EAAGyB,QAAmB,IAAI;IAC7B,OAAOA,QAAQ,CAAC7B,QAAQ,IAAI,IAAI,IAAI6B,QAAQ,CAACvB,gBAAgB,IAAI,IAAI,IACjEuB,QAAQ,CAACjB,cAAc,IAAI,IAAI;EACrC;CACD,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}