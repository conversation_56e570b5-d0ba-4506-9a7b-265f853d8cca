{"ast": null, "code": "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { Expm1 } from '../kernel_names';\nimport { exp } from '../ops/exp';\nimport { mul } from '../ops/mul';\nexport const expm1GradConfig = {\n  kernelName: Expm1,\n  inputsToSave: ['x'],\n  gradFunc: (dy, saved) => {\n    const [x] = saved;\n    return {\n      x: () => mul(dy, exp(x))\n    };\n  }\n};", "map": {"version": 3, "names": ["Expm1", "exp", "mul", "expm1GradConfig", "kernelName", "inputsToSave", "grad<PERSON>unc", "dy", "saved", "x"], "sources": ["C:\\tfjs-core\\src\\gradients\\Expm1_grad.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {Expm1} from '../kernel_names';\nimport {GradConfig} from '../kernel_registry';\nimport {exp} from '../ops/exp';\nimport {mul} from '../ops/mul';\nimport {Tensor} from '../tensor';\n\nexport const expm1GradConfig: GradConfig = {\n  kernelName: Expm1,\n  inputsToSave: ['x'],\n  gradFunc: (dy: Tensor, saved: Tensor[]) => {\n    const [x] = saved;\n    return {x: () => mul(dy, exp(x))};\n  }\n};\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAiBA,SAAQA,KAAK,QAAO,iBAAiB;AAErC,SAAQC,GAAG,QAAO,YAAY;AAC9B,SAAQC,GAAG,QAAO,YAAY;AAG9B,OAAO,MAAMC,eAAe,GAAe;EACzCC,UAAU,EAAEJ,KAAK;EACjBK,YAAY,EAAE,CAAC,GAAG,CAAC;EACnBC,QAAQ,EAAEA,CAACC,EAAU,EAAEC,KAAe,KAAI;IACxC,MAAM,CAACC,CAAC,CAAC,GAAGD,KAAK;IACjB,OAAO;MAACC,CAAC,EAAEA,CAAA,KAAMP,GAAG,CAACK,EAAE,EAAEN,GAAG,CAACQ,CAAC,CAAC;IAAC,CAAC;EACnC;CACD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}