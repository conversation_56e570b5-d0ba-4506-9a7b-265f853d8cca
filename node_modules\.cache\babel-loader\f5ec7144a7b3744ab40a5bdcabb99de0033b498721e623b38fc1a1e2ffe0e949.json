{"ast": null, "code": "/**\n * @license\n * Copyright 2018 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n *\n * =============================================================================\n */\n// inspired by https://github.com/maxogden/filereader-stream\nimport { env, util } from '@tensorflow/tfjs-core';\nimport { ByteChunkIterator } from './byte_chunk_iterator';\n/**\n * Provide a stream of chunks from a File, Blob, or Uint8Array.\n * @param file The source File, Blob or Uint8Array.\n * @param options Optional settings controlling file reading.\n * @returns a lazy Iterator of Uint8Arrays containing sequential chunks of the\n *   input File, Blob or Uint8Array.\n */\nexport class FileChunkIterator extends ByteChunkIterator {\n  constructor(file, options = {}) {\n    super();\n    this.file = file;\n    this.options = options;\n    util.assert(file instanceof Uint8Array || (env().get('IS_BROWSER') ? file instanceof File || file instanceof Blob : false), () => 'FileChunkIterator only supports File, Blob and Uint8Array ' + 'right now.');\n    this.offset = options.offset || 0;\n    // default 1MB chunk has tolerable perf on large files\n    this.chunkSize = options.chunkSize || 1024 * 1024;\n  }\n  summary() {\n    return `FileChunks ${this.file}`;\n  }\n  async next() {\n    if (this.offset >= (this.file instanceof Uint8Array ? this.file.byteLength : this.file.size)) {\n      return {\n        value: null,\n        done: true\n      };\n    }\n    const chunk = new Promise((resolve, reject) => {\n      const end = this.offset + this.chunkSize;\n      if (this.file instanceof Uint8Array) {\n        // Note if end > this.uint8Array.byteLength, we just get a small last\n        // chunk.\n        resolve(new Uint8Array(this.file.slice(this.offset, end)));\n      } else {\n        // This branch assumes that this.file type is File or Blob, which\n        // means it is in the browser environment.\n        // TODO(soergel): is this a performance issue?\n        const fileReader = new FileReader();\n        fileReader.onload = event => {\n          let data = fileReader.result;\n          // Not sure we can trust the return type of\n          // FileReader.readAsArrayBuffer See e.g.\n          // https://github.com/node-file-api/FileReader/issues/2\n          if (data instanceof ArrayBuffer) {\n            data = new Uint8Array(data);\n          }\n          if (!(data instanceof Uint8Array)) {\n            return reject(new TypeError('FileReader returned unknown type.'));\n          }\n          resolve(data);\n        };\n        fileReader.onabort = event => {\n          return reject(new Error('Aborted'));\n        };\n        fileReader.onerror = event => {\n          return reject(new Error(event.type));\n        };\n        // TODO(soergel): better handle onabort, onerror\n        // Note if end > this.file.size, we just get a small last chunk.\n        const slice = this.file.slice(this.offset, end);\n        // We can't use readAsText here (even if we know the file is text)\n        // because the slice boundary may fall within a multi-byte character.\n        fileReader.readAsArrayBuffer(slice);\n      }\n      this.offset = end;\n    });\n    return {\n      value: await chunk,\n      done: false\n    };\n  }\n}", "map": {"version": 3, "names": ["env", "util", "ByteChunkIterator", "FileChunkIterator", "constructor", "file", "options", "assert", "Uint8Array", "get", "File", "Blob", "offset", "chunkSize", "summary", "next", "byteLength", "size", "value", "done", "chunk", "Promise", "resolve", "reject", "end", "slice", "fileReader", "FileReader", "onload", "event", "data", "result", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "TypeError", "<PERSON>ab<PERSON>", "Error", "onerror", "type", "readAsA<PERSON>y<PERSON><PERSON>er"], "sources": ["C:\\tfjs-data\\src\\iterators\\file_chunk_iterator.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2018 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n *\n * =============================================================================\n */\n\n// inspired by https://github.com/maxogden/filereader-stream\nimport {env, util} from '@tensorflow/tfjs-core';\nimport {FileElement} from '../types';\nimport {ByteChunkIterator} from './byte_chunk_iterator';\n\nexport interface FileChunkIteratorOptions {\n  /** The byte offset at which to begin reading the File or Blob. Default 0. */\n  offset?: number;\n  /** The number of bytes to read at a time. Default 1MB. */\n  chunkSize?: number;\n}\n\n/**\n * Provide a stream of chunks from a File, Blob, or Uint8Array.\n * @param file The source File, Blob or Uint8Array.\n * @param options Optional settings controlling file reading.\n * @returns a lazy Iterator of Uint8Arrays containing sequential chunks of the\n *   input File, Blob or Uint8Array.\n */\nexport class FileChunkIterator extends ByteChunkIterator {\n  offset: number;\n  chunkSize: number;\n\n  constructor(\n      protected file: FileElement,\n      protected options: FileChunkIteratorOptions = {}) {\n    super();\n    util.assert(\n        (file instanceof Uint8Array) ||\n            (env().get('IS_BROWSER') ?\n                 (file instanceof File || file instanceof Blob) :\n                 false),\n        () => 'FileChunkIterator only supports File, Blob and Uint8Array ' +\n            'right now.');\n    this.offset = options.offset || 0;\n    // default 1MB chunk has tolerable perf on large files\n    this.chunkSize = options.chunkSize || 1024 * 1024;\n  }\n\n  summary() {\n    return `FileChunks ${this.file}`;\n  }\n\n  async next(): Promise<IteratorResult<Uint8Array>> {\n    if (this.offset >= ((this.file instanceof Uint8Array) ?\n                            this.file.byteLength :\n                            this.file.size)) {\n      return {value: null, done: true};\n    }\n    const chunk = new Promise<Uint8Array>((resolve, reject) => {\n      const end = this.offset + this.chunkSize;\n      if (this.file instanceof Uint8Array) {\n        // Note if end > this.uint8Array.byteLength, we just get a small last\n        // chunk.\n        resolve(new Uint8Array(this.file.slice(this.offset, end)));\n      } else {\n        // This branch assumes that this.file type is File or Blob, which\n        // means it is in the browser environment.\n\n        // TODO(soergel): is this a performance issue?\n        const fileReader = new FileReader();\n        fileReader.onload = (event) => {\n          let data: string|ArrayBuffer|Uint8Array = fileReader.result;\n          // Not sure we can trust the return type of\n          // FileReader.readAsArrayBuffer See e.g.\n          // https://github.com/node-file-api/FileReader/issues/2\n          if (data instanceof ArrayBuffer) {\n            data = new Uint8Array(data);\n          }\n          if (!(data instanceof Uint8Array)) {\n            return reject(new TypeError('FileReader returned unknown type.'));\n          }\n          resolve(data);\n        };\n        fileReader.onabort = (event) => {\n          return reject(new Error('Aborted'));\n        };\n        fileReader.onerror = (event) => {\n          return reject(new Error(event.type));\n        };\n        // TODO(soergel): better handle onabort, onerror\n        // Note if end > this.file.size, we just get a small last chunk.\n        const slice = this.file.slice(this.offset, end);\n        // We can't use readAsText here (even if we know the file is text)\n        // because the slice boundary may fall within a multi-byte character.\n        fileReader.readAsArrayBuffer(slice);\n      }\n      this.offset = end;\n    });\n    return {value: (await chunk), done: false};\n  }\n}\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;;AAkBA;AACA,SAAQA,GAAG,EAAEC,IAAI,QAAO,uBAAuB;AAE/C,SAAQC,iBAAiB,QAAO,uBAAuB;AASvD;;;;;;;AAOA,OAAM,MAAOC,iBAAkB,SAAQD,iBAAiB;EAItDE,YACcC,IAAiB,EACjBC,OAAA,GAAoC,EAAE;IAClD,KAAK,EAAE;IAFK,KAAAD,IAAI,GAAJA,IAAI;IACJ,KAAAC,OAAO,GAAPA,OAAO;IAEnBL,IAAI,CAACM,MAAM,CACNF,IAAI,YAAYG,UAAU,KACtBR,GAAG,EAAE,CAACS,GAAG,CAAC,YAAY,CAAC,GAClBJ,IAAI,YAAYK,IAAI,IAAIL,IAAI,YAAYM,IAAI,GAC7C,KAAK,CAAC,EACf,MAAM,4DAA4D,GAC9D,YAAY,CAAC;IACrB,IAAI,CAACC,MAAM,GAAGN,OAAO,CAACM,MAAM,IAAI,CAAC;IACjC;IACA,IAAI,CAACC,SAAS,GAAGP,OAAO,CAACO,SAAS,IAAI,IAAI,GAAG,IAAI;EACnD;EAEAC,OAAOA,CAAA;IACL,OAAO,cAAc,IAAI,CAACT,IAAI,EAAE;EAClC;EAEA,MAAMU,IAAIA,CAAA;IACR,IAAI,IAAI,CAACH,MAAM,KAAM,IAAI,CAACP,IAAI,YAAYG,UAAU,GAC5B,IAAI,CAACH,IAAI,CAACW,UAAU,GACpB,IAAI,CAACX,IAAI,CAACY,IAAI,CAAC,EAAE;MACvC,OAAO;QAACC,KAAK,EAAE,IAAI;QAAEC,IAAI,EAAE;MAAI,CAAC;;IAElC,MAAMC,KAAK,GAAG,IAAIC,OAAO,CAAa,CAACC,OAAO,EAAEC,MAAM,KAAI;MACxD,MAAMC,GAAG,GAAG,IAAI,CAACZ,MAAM,GAAG,IAAI,CAACC,SAAS;MACxC,IAAI,IAAI,CAACR,IAAI,YAAYG,UAAU,EAAE;QACnC;QACA;QACAc,OAAO,CAAC,IAAId,UAAU,CAAC,IAAI,CAACH,IAAI,CAACoB,KAAK,CAAC,IAAI,CAACb,MAAM,EAAEY,GAAG,CAAC,CAAC,CAAC;OAC3D,MAAM;QACL;QACA;QAEA;QACA,MAAME,UAAU,GAAG,IAAIC,UAAU,EAAE;QACnCD,UAAU,CAACE,MAAM,GAAIC,KAAK,IAAI;UAC5B,IAAIC,IAAI,GAAkCJ,UAAU,CAACK,MAAM;UAC3D;UACA;UACA;UACA,IAAID,IAAI,YAAYE,WAAW,EAAE;YAC/BF,IAAI,GAAG,IAAItB,UAAU,CAACsB,IAAI,CAAC;;UAE7B,IAAI,EAAEA,IAAI,YAAYtB,UAAU,CAAC,EAAE;YACjC,OAAOe,MAAM,CAAC,IAAIU,SAAS,CAAC,mCAAmC,CAAC,CAAC;;UAEnEX,OAAO,CAACQ,IAAI,CAAC;QACf,CAAC;QACDJ,UAAU,CAACQ,OAAO,GAAIL,KAAK,IAAI;UAC7B,OAAON,MAAM,CAAC,IAAIY,KAAK,CAAC,SAAS,CAAC,CAAC;QACrC,CAAC;QACDT,UAAU,CAACU,OAAO,GAAIP,KAAK,IAAI;UAC7B,OAAON,MAAM,CAAC,IAAIY,KAAK,CAACN,KAAK,CAACQ,IAAI,CAAC,CAAC;QACtC,CAAC;QACD;QACA;QACA,MAAMZ,KAAK,GAAG,IAAI,CAACpB,IAAI,CAACoB,KAAK,CAAC,IAAI,CAACb,MAAM,EAAEY,GAAG,CAAC;QAC/C;QACA;QACAE,UAAU,CAACY,iBAAiB,CAACb,KAAK,CAAC;;MAErC,IAAI,CAACb,MAAM,GAAGY,GAAG;IACnB,CAAC,CAAC;IACF,OAAO;MAACN,KAAK,EAAG,MAAME,KAAM;MAAED,IAAI,EAAE;IAAK,CAAC;EAC5C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}