{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.DatabaseError = exports.serialize = exports.parse = void 0;\nconst messages_1 = require(\"./messages\");\nObject.defineProperty(exports, \"DatabaseError\", {\n  enumerable: true,\n  get: function () {\n    return messages_1.DatabaseError;\n  }\n});\nconst serializer_1 = require(\"./serializer\");\nObject.defineProperty(exports, \"serialize\", {\n  enumerable: true,\n  get: function () {\n    return serializer_1.serialize;\n  }\n});\nconst parser_1 = require(\"./parser\");\nfunction parse(stream, callback) {\n  const parser = new parser_1.Parser();\n  stream.on('data', buffer => parser.parse(buffer, callback));\n  return new Promise(resolve => stream.on('end', () => resolve()));\n}\nexports.parse = parse;", "map": {"version": 3, "names": ["messages_1", "require", "Object", "defineProperty", "exports", "enumerable", "get", "DatabaseError", "serializer_1", "serialize", "parser_1", "parse", "stream", "callback", "parser", "<PERSON><PERSON><PERSON>", "on", "buffer", "Promise", "resolve"], "sources": ["C:\\tmsft\\node_modules\\pg-protocol\\src\\index.ts"], "sourcesContent": ["import { DatabaseError } from './messages'\nimport { serialize } from './serializer'\nimport { Parser, MessageCallback } from './parser'\n\nexport function parse(stream: NodeJS.ReadableStream, callback: MessageCallback): Promise<void> {\n  const parser = new Parser()\n  stream.on('data', (buffer: Buffer) => parser.parse(buffer, callback))\n  return new Promise((resolve) => stream.on('end', () => resolve()))\n}\n\nexport { serialize, DatabaseError }\n"], "mappings": ";;;;;;AAAA,MAAAA,UAAA,GAAAC,OAAA;AAUoBC,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,UAAA;EAAAC,GAAA,WAAAA,CAAA;IAAA,OAVXN,UAAA,CAAAO,aAAa;EAAA;AAAA;AACtB,MAAAC,YAAA,GAAAP,OAAA;AASSC,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,UAAA;EAAAC,GAAA,WAAAA,CAAA;IAAA,OATAE,YAAA,CAAAC,SAAS;EAAA;AAAA;AAClB,MAAAC,QAAA,GAAAT,OAAA;AAEA,SAAgBU,KAAKA,CAACC,MAA6B,EAAEC,QAAyB;EAC5E,MAAMC,MAAM,GAAG,IAAIJ,QAAA,CAAAK,MAAM,EAAE;EAC3BH,MAAM,CAACI,EAAE,CAAC,MAAM,EAAGC,MAAc,IAAKH,MAAM,CAACH,KAAK,CAACM,MAAM,EAAEJ,QAAQ,CAAC,CAAC;EACrE,OAAO,IAAIK,OAAO,CAAEC,OAAO,IAAKP,MAAM,CAACI,EAAE,CAAC,KAAK,EAAE,MAAMG,OAAO,EAAE,CAAC,CAAC;AACpE;AAJAf,OAAA,CAAAO,KAAA,GAAAA,KAAA", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}