{"ast": null, "code": "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n// Modularized ops.\nexport { abs } from './abs';\nexport { acos } from './acos';\nexport { acosh } from './acosh';\nexport { add } from './add';\nexport { addN } from './add_n';\nexport { all } from './all';\nexport { any } from './any';\nexport { argMax } from './arg_max';\nexport { argMin } from './arg_min';\nexport { asin } from './asin';\nexport { asinh } from './asinh';\nexport { atan } from './atan';\nexport { atan2 } from './atan2';\nexport { atanh } from './atanh';\nexport { avgPool } from './avg_pool';\nexport { avgPool3d } from './avg_pool_3d';\nexport { basicLSTMCell } from './basic_lstm_cell';\nexport { batchToSpaceND } from './batch_to_space_nd';\nexport { batchNorm } from './batchnorm';\nexport { batchNorm2d } from './batchnorm2d';\nexport { batchNorm3d } from './batchnorm3d';\nexport { batchNorm4d } from './batchnorm4d';\nexport { bincount } from './bincount';\nexport { bitwiseAnd } from './bitwise_and';\nexport { broadcastArgs } from './broadcast_args';\nexport { broadcastTo } from './broadcast_to';\nexport { buffer } from './buffer';\nexport { cast } from './cast';\nexport { ceil } from './ceil';\nexport { clipByValue } from './clip_by_value';\nexport { clone } from './clone';\nexport { complex } from './complex';\nexport { concat } from './concat';\nexport { concat1d } from './concat_1d';\nexport { concat2d } from './concat_2d';\nexport { concat3d } from './concat_3d';\nexport { concat4d } from './concat_4d';\nexport { conv1d } from './conv1d';\nexport { conv2d } from './conv2d';\nexport { conv2dTranspose } from './conv2d_transpose';\nexport { conv3d } from './conv3d';\nexport { conv3dTranspose } from './conv3d_transpose';\nexport { cos } from './cos';\nexport { cosh } from './cosh';\nexport { cumprod } from './cumprod';\nexport { cumsum } from './cumsum';\nexport { denseBincount } from './dense_bincount';\nexport { depthToSpace } from './depth_to_space';\nexport { depthwiseConv2d } from './depthwise_conv2d';\nexport { diag } from './diag';\nexport { dilation2d } from './dilation2d';\nexport { div } from './div';\nexport { divNoNan } from './div_no_nan';\nexport { dot } from './dot';\nexport { einsum } from './einsum';\nexport { elu } from './elu';\nexport { ensureShape } from './ensure_shape';\nexport { equal } from './equal';\nexport { erf } from './erf';\nexport { euclideanNorm } from './euclidean_norm';\nexport { exp } from './exp';\nexport { expandDims } from './expand_dims';\nexport { expm1 } from './expm1';\nexport { eye } from './eye';\nexport { fill } from './fill';\nexport { floor } from './floor';\nexport { floorDiv } from './floorDiv';\nexport { gather } from './gather';\nexport { greater } from './greater';\nexport { greaterEqual } from './greater_equal';\nexport { imag } from './imag';\nexport { isFinite } from './is_finite';\nexport { isInf } from './is_inf';\nexport { isNaN } from './is_nan';\nexport { leakyRelu } from './leaky_relu';\nexport { less } from './less';\nexport { lessEqual } from './less_equal';\nexport { linspace } from './linspace';\nexport { localResponseNormalization } from './local_response_normalization';\nexport { log } from './log';\nexport { log1p } from './log1p';\nexport { logSigmoid } from './log_sigmoid';\nexport { logSoftmax } from './log_softmax';\nexport { logSumExp } from './log_sum_exp';\nexport { logicalAnd } from './logical_and';\nexport { logicalNot } from './logical_not';\nexport { logicalOr } from './logical_or';\nexport { logicalXor } from './logical_xor';\nexport { lowerBound } from './lower_bound';\nexport { matMul } from './mat_mul';\nexport { max } from './max';\nexport { maxPool } from './max_pool';\nexport { maxPool3d } from './max_pool_3d';\nexport { maxPoolWithArgmax } from './max_pool_with_argmax';\nexport { maximum } from './maximum';\nexport { mean } from './mean';\nexport { meshgrid } from './meshgrid';\nexport { min } from './min';\nexport { minimum } from './minimum';\nexport { mirrorPad } from './mirror_pad';\nexport { mod } from './mod';\nexport { moments } from './moments';\nexport { mul } from './mul';\nexport { multiRNNCell } from './multi_rnn_cell';\nexport { multinomial } from './multinomial';\nexport { neg } from './neg';\nexport { notEqual } from './not_equal';\nexport { oneHot } from './one_hot';\nexport { ones } from './ones';\nexport { onesLike } from './ones_like';\nexport { outerProduct } from './outer_product';\nexport { pad } from './pad';\nexport { pad1d } from './pad1d';\nexport { pad2d } from './pad2d';\nexport { pad3d } from './pad3d';\nexport { pad4d } from './pad4d';\nexport { pool } from './pool';\nexport { pow } from './pow';\nexport { prelu } from './prelu';\nexport { print } from './print';\nexport { prod } from './prod';\nexport { raggedGather } from './ragged_gather';\nexport { raggedRange } from './ragged_range';\nexport { raggedTensorToTensor } from './ragged_tensor_to_tensor';\nexport { rand } from './rand';\nexport { randomGamma } from './random_gamma';\nexport { randomNormal } from './random_normal';\nexport { randomStandardNormal } from './random_standard_normal';\nexport { randomUniform } from './random_uniform';\nexport { randomUniformInt } from './random_uniform_int';\nexport { range } from './range';\nexport { real } from './real';\nexport { reciprocal } from './reciprocal';\nexport { relu } from './relu';\nexport { relu6 } from './relu6';\nexport { reshape } from './reshape';\nexport { reverse } from './reverse';\nexport { reverse1d } from './reverse_1d';\nexport { reverse2d } from './reverse_2d';\nexport { reverse3d } from './reverse_3d';\nexport { reverse4d } from './reverse_4d';\nexport { round } from './round';\nexport { rsqrt } from './rsqrt';\nexport { scalar } from './scalar';\nexport { selu } from './selu';\nexport { separableConv2d } from './separable_conv2d';\nexport { setdiff1dAsync } from './setdiff1d_async';\nexport { sigmoid } from './sigmoid';\nexport { sign } from './sign';\nexport { sin } from './sin';\nexport { sinh } from './sinh';\nexport { slice } from './slice';\nexport { slice1d } from './slice1d';\nexport { slice2d } from './slice2d';\nexport { slice3d } from './slice3d';\nexport { slice4d } from './slice4d';\nexport { softmax } from './softmax';\nexport { softplus } from './softplus';\nexport { spaceToBatchND } from './space_to_batch_nd';\nexport { fft } from './spectral/fft';\nexport { ifft } from './spectral/ifft';\nexport { irfft } from './spectral/irfft';\nexport { rfft } from './spectral/rfft';\nexport { split } from './split';\nexport { sqrt } from './sqrt';\nexport { square } from './square';\nexport { squaredDifference } from './squared_difference';\nexport { squeeze } from './squeeze';\nexport { stack } from './stack';\nexport { step } from './step';\nexport { stridedSlice } from './strided_slice';\nexport { sub } from './sub';\nexport { sum } from './sum';\nexport { tan } from './tan';\nexport { tanh } from './tanh';\nexport { tensor } from './tensor';\nexport { tensor1d } from './tensor1d';\nexport { tensor2d } from './tensor2d';\nexport { tensor3d } from './tensor3d';\nexport { tensor4d } from './tensor4d';\nexport { tensor5d } from './tensor5d';\nexport { tensor6d } from './tensor6d';\nexport { tensorScatterUpdate } from './tensor_scatter_update';\nexport { tile } from './tile';\nexport { topk } from './topk';\nexport { truncatedNormal } from './truncated_normal';\nexport { unique } from './unique';\nexport { unsortedSegmentSum } from './unsorted_segment_sum';\nexport { unstack } from './unstack';\nexport { upperBound } from './upper_bound';\nexport { variable } from './variable';\nexport { where } from './where';\nexport { whereAsync } from './where_async';\nexport { zeros } from './zeros';\nexport { zerosLike } from './zeros_like';\nexport * from './boolean_mask';\nexport * from './transpose';\nexport * from './norm';\nexport * from './moving_average';\nexport * from './scatter_nd';\nexport * from './search_sorted';\nexport * from './sparse_to_dense';\nexport * from './gather_nd';\nexport * from './dropout';\nexport * from './signal_ops_util';\nexport * from './in_top_k';\nexport { op, OP_SCOPE_SUFFIX } from './operation';\nimport { rfft } from './spectral/rfft';\nimport { fft } from './spectral/fft';\nimport { ifft } from './spectral/ifft';\nimport { irfft } from './spectral/irfft';\nconst spectral = {\n  fft,\n  ifft,\n  rfft,\n  irfft\n};\nimport * as fused from './fused_ops';\nimport { hammingWindow } from './signal/hamming_window';\nimport { hannWindow } from './signal/hann_window';\nimport { frame } from './signal/frame';\nimport { stft } from './signal/stft';\nconst signal = {\n  hammingWindow,\n  hannWindow,\n  frame,\n  stft\n};\n// Image Ops namespace\nimport { cropAndResize } from './image/crop_and_resize';\nimport { flipLeftRight } from './image/flip_left_right';\nimport { grayscaleToRGB } from './image/grayscale_to_rgb';\nimport { rgbToGrayscale } from './image/rgb_to_grayscale';\nimport { rotateWithOffset } from './image/rotate_with_offset';\nimport { nonMaxSuppression } from './image/non_max_suppression';\nimport { nonMaxSuppressionAsync } from './image/non_max_suppression_async';\nimport { nonMaxSuppressionWithScore } from './image/non_max_suppression_with_score';\nimport { nonMaxSuppressionWithScoreAsync } from './image/non_max_suppression_with_score_async';\nimport { nonMaxSuppressionPadded } from './image/non_max_suppression_padded';\nimport { nonMaxSuppressionPaddedAsync } from './image/non_max_suppression_padded_async';\nimport { resizeBilinear } from './image/resize_bilinear';\nimport { resizeNearestNeighbor } from './image/resize_nearest_neighbor';\nimport { threshold } from './image/threshold';\nimport { transform } from './image/transform';\nconst image = {\n  flipLeftRight,\n  grayscaleToRGB,\n  resizeNearestNeighbor,\n  resizeBilinear,\n  rgbToGrayscale,\n  rotateWithOffset,\n  cropAndResize,\n  nonMaxSuppression,\n  nonMaxSuppressionAsync,\n  nonMaxSuppressionWithScore,\n  nonMaxSuppressionWithScoreAsync,\n  nonMaxSuppressionPadded,\n  nonMaxSuppressionPaddedAsync,\n  threshold,\n  transform\n};\n// linalg namespace\nimport { bandPart } from './linalg/band_part';\nimport { gramSchmidt } from './linalg/gram_schmidt';\nimport { qr } from './linalg/qr';\nconst linalg = {\n  bandPart,\n  gramSchmidt,\n  qr\n};\n// losses namespace;\nimport { absoluteDifference } from './losses/absolute_difference';\nimport { computeWeightedLoss } from './losses/compute_weighted_loss';\nimport { cosineDistance } from './losses/cosine_distance';\nimport { hingeLoss } from './losses/hinge_loss';\nimport { huberLoss } from './losses/huber_loss';\nimport { logLoss } from './losses/log_loss';\nimport { meanSquaredError } from './losses/mean_squared_error';\nimport { sigmoidCrossEntropy } from './losses/sigmoid_cross_entropy';\nimport { softmaxCrossEntropy } from './losses/softmax_cross_entropy';\nconst losses = {\n  absoluteDifference,\n  computeWeightedLoss,\n  cosineDistance,\n  hingeLoss,\n  huberLoss,\n  logLoss,\n  meanSquaredError,\n  sigmoidCrossEntropy,\n  softmaxCrossEntropy\n};\nimport { sparseFillEmptyRows } from './sparse/sparse_fill_empty_rows';\nimport { sparseReshape } from './sparse/sparse_reshape';\nimport { sparseSegmentMean } from './sparse/sparse_segment_mean';\nimport { sparseSegmentSum } from './sparse/sparse_segment_sum';\nconst sparse = {\n  sparseFillEmptyRows,\n  sparseReshape,\n  sparseSegmentMean,\n  sparseSegmentSum\n};\nimport { stringNGrams } from './string/string_n_grams';\nimport { stringSplit } from './string/string_split';\nimport { stringToHashBucketFast } from './string/string_to_hash_bucket_fast';\nimport { staticRegexReplace } from './string/static_regex_replace';\n// tslint:disable-next-line:variable-name\nconst string = {\n  stringNGrams,\n  stringSplit,\n  stringToHashBucketFast,\n  staticRegexReplace\n};\n// Second level exports.\nexport { image, linalg, losses, spectral, fused, signal, sparse, string };", "map": {"version": 3, "names": ["abs", "acos", "acosh", "add", "addN", "all", "any", "argMax", "arg<PERSON>in", "asin", "asinh", "atan", "atan2", "atanh", "avgPool", "avgPool3d", "basicLSTMCell", "batchToSpaceND", "batchNorm", "batchNorm2d", "batchNorm3d", "batchNorm4d", "bincount", "bitwiseAnd", "broadcastArgs", "broadcastTo", "buffer", "cast", "ceil", "clipByValue", "clone", "complex", "concat", "concat1d", "concat2d", "concat3d", "concat4d", "conv1d", "conv2d", "conv2dTranspose", "conv3d", "conv3dTranspose", "cos", "cosh", "cump<PERSON>", "cumsum", "denseBincount", "depthToSpace", "depthwiseConv2d", "diag", "dilation2d", "div", "divNoNan", "dot", "einsum", "elu", "ensureShape", "equal", "erf", "euclideanNorm", "exp", "expandDims", "expm1", "eye", "fill", "floor", "floorDiv", "gather", "greater", "greaterEqual", "imag", "isFinite", "isInf", "isNaN", "leakyRelu", "less", "lessEqual", "linspace", "localResponseNormalization", "log", "log1p", "logSigmoid", "logSoftmax", "logSumExp", "logicalAnd", "logicalNot", "logicalOr", "logicalXor", "lowerBound", "<PERSON><PERSON><PERSON>", "max", "maxPool", "maxPool3d", "maxPoolWithArgmax", "maximum", "mean", "meshgrid", "min", "minimum", "mirrorPad", "mod", "moments", "mul", "multiRNNCell", "multinomial", "neg", "notEqual", "oneHot", "ones", "onesLike", "outerProduct", "pad", "pad1d", "pad2d", "pad3d", "pad4d", "pool", "pow", "prelu", "print", "prod", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "raggedTensorToTensor", "rand", "<PERSON><PERSON><PERSON><PERSON>", "randomNormal", "randomStandardNormal", "randomUniform", "randomUniformInt", "range", "real", "reciprocal", "relu", "relu6", "reshape", "reverse", "reverse1d", "reverse2d", "reverse3d", "reverse4d", "round", "rsqrt", "scalar", "selu", "separableConv2d", "setdiff1dAsync", "sigmoid", "sign", "sin", "sinh", "slice", "slice1d", "slice2d", "slice3d", "slice4d", "softmax", "softplus", "spaceToBatchND", "fft", "ifft", "irfft", "rfft", "split", "sqrt", "square", "squaredDifference", "squeeze", "stack", "step", "stridedSlice", "sub", "sum", "tan", "tanh", "tensor", "tensor1d", "tensor2d", "tensor3d", "tensor4d", "tensor5d", "tensor6d", "tensorScatterUpdate", "tile", "topk", "truncatedNormal", "unique", "unsortedSegmentSum", "unstack", "upperBound", "variable", "where", "whereAsync", "zeros", "zerosLike", "op", "OP_SCOPE_SUFFIX", "spectral", "fused", "hammingWindow", "hann<PERSON><PERSON><PERSON>", "frame", "stft", "signal", "cropAndResize", "flipLeftRight", "grayscaleToRGB", "rgbToGrayscale", "rotateWithOffset", "nonMaxSuppression", "nonMaxSuppressionAsync", "nonMaxSuppressionWithScore", "nonMaxSuppressionWithScoreAsync", "nonMaxSuppressionPadded", "nonMaxSuppressionPaddedAsync", "resizeBilinear", "resizeNearestNeighbor", "threshold", "transform", "image", "bandPart", "gramSchmidt", "qr", "l<PERSON>g", "absoluteDifference", "computeWeightedLoss", "cosineDistance", "hi<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "logLoss", "meanSquaredError", "sigmoidCrossEntropy", "softmaxCrossEntropy", "losses", "sparseFillEmptyRows", "sparseReshape", "sparseSegmentMean", "sparseSegmentSum", "sparse", "stringNGrams", "stringSplit", "stringToHashBucketFast", "staticRegexReplace", "string"], "sources": ["C:\\tfjs-core\\src\\ops\\ops.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\n// Modularized ops.\nexport {abs} from './abs';\nexport {acos} from './acos';\nexport {acosh} from './acosh';\nexport {add} from './add';\nexport {addN} from './add_n';\nexport {all} from './all';\nexport {any} from './any';\nexport {argMax} from './arg_max';\nexport {argMin} from './arg_min';\nexport {asin} from './asin';\nexport {asinh} from './asinh';\nexport {atan} from './atan';\nexport {atan2} from './atan2';\nexport {atanh} from './atanh';\nexport {avgPool} from './avg_pool';\nexport {avgPool3d} from './avg_pool_3d';\nexport {basicLSTMCell} from './basic_lstm_cell';\nexport {batchToSpaceND} from './batch_to_space_nd';\nexport {batchNorm} from './batchnorm';\nexport {batchNorm2d} from './batchnorm2d';\nexport {batchNorm3d} from './batchnorm3d';\nexport {batchNorm4d} from './batchnorm4d';\nexport {bincount} from './bincount';\nexport {bitwiseAnd} from './bitwise_and';\nexport {broadcastArgs} from './broadcast_args';\nexport {broadcastTo} from './broadcast_to';\nexport {buffer} from './buffer';\nexport {cast} from './cast';\nexport {ceil} from './ceil';\nexport {clipByValue} from './clip_by_value';\nexport {clone} from './clone';\nexport {complex} from './complex';\nexport {concat} from './concat';\nexport {concat1d} from './concat_1d';\nexport {concat2d} from './concat_2d';\nexport {concat3d} from './concat_3d';\nexport {concat4d} from './concat_4d';\nexport {conv1d} from './conv1d';\nexport {conv2d} from './conv2d';\nexport {conv2dTranspose} from './conv2d_transpose';\nexport {conv3d} from './conv3d';\nexport {conv3dTranspose} from './conv3d_transpose';\nexport {cos} from './cos';\nexport {cosh} from './cosh';\nexport {cumprod} from './cumprod';\nexport {cumsum} from './cumsum';\nexport {denseBincount} from './dense_bincount';\nexport {depthToSpace} from './depth_to_space';\nexport {depthwiseConv2d} from './depthwise_conv2d';\nexport {diag} from './diag';\nexport {dilation2d} from './dilation2d';\nexport {div} from './div';\nexport {divNoNan} from './div_no_nan';\nexport {dot} from './dot';\nexport {einsum} from './einsum';\nexport {elu} from './elu';\nexport {ensureShape} from './ensure_shape';\nexport {equal} from './equal';\nexport {erf} from './erf';\nexport {euclideanNorm} from './euclidean_norm';\nexport {exp} from './exp';\nexport {expandDims} from './expand_dims';\nexport {expm1} from './expm1';\nexport {eye} from './eye';\nexport {fill} from './fill';\nexport {floor} from './floor';\nexport {floorDiv} from './floorDiv';\nexport {gather} from './gather';\nexport {greater} from './greater';\nexport {greaterEqual} from './greater_equal';\nexport {imag} from './imag';\nexport {isFinite} from './is_finite';\nexport {isInf} from './is_inf';\nexport {isNaN} from './is_nan';\nexport {leakyRelu} from './leaky_relu';\nexport {less} from './less';\nexport {lessEqual} from './less_equal';\nexport {linspace} from './linspace';\nexport {localResponseNormalization} from './local_response_normalization';\nexport {log} from './log';\nexport {log1p} from './log1p';\nexport {logSigmoid} from './log_sigmoid';\nexport {logSoftmax} from './log_softmax';\nexport {logSumExp} from './log_sum_exp';\nexport {logicalAnd} from './logical_and';\nexport {logicalNot} from './logical_not';\nexport {logicalOr} from './logical_or';\nexport {logicalXor} from './logical_xor';\nexport {lowerBound} from './lower_bound';\nexport {matMul} from './mat_mul';\nexport {max} from './max';\nexport {maxPool} from './max_pool';\nexport {maxPool3d} from './max_pool_3d';\nexport {maxPoolWithArgmax} from './max_pool_with_argmax';\nexport {maximum} from './maximum';\nexport {mean} from './mean';\nexport {meshgrid} from './meshgrid';\nexport {min} from './min';\nexport {minimum} from './minimum';\nexport {mirrorPad} from './mirror_pad';\nexport {mod} from './mod';\nexport {moments} from './moments';\nexport {mul} from './mul';\nexport {LSTMCellFunc, multiRNNCell} from './multi_rnn_cell';\nexport {multinomial} from './multinomial';\nexport {neg} from './neg';\nexport {notEqual} from './not_equal';\nexport {oneHot} from './one_hot';\nexport {ones} from './ones';\nexport {onesLike} from './ones_like';\nexport {outerProduct} from './outer_product';\nexport {pad} from './pad';\nexport {pad1d} from './pad1d';\nexport {pad2d} from './pad2d';\nexport {pad3d} from './pad3d';\nexport {pad4d} from './pad4d';\nexport {pool} from './pool';\nexport {pow} from './pow';\nexport {prelu} from './prelu';\nexport {print} from './print';\nexport {prod} from './prod';\nexport {raggedGather} from './ragged_gather';\nexport {raggedRange} from './ragged_range';\nexport {raggedTensorToTensor} from './ragged_tensor_to_tensor';\nexport {rand} from './rand';\nexport {randomGamma} from './random_gamma';\nexport {randomNormal} from './random_normal';\nexport {randomStandardNormal} from './random_standard_normal';\nexport {randomUniform} from './random_uniform';\nexport {randomUniformInt} from './random_uniform_int';\nexport {range} from './range';\nexport {real} from './real';\nexport {reciprocal} from './reciprocal';\nexport {relu} from './relu';\nexport {relu6} from './relu6';\nexport {reshape} from './reshape';\nexport {reverse} from './reverse';\nexport {reverse1d} from './reverse_1d';\nexport {reverse2d} from './reverse_2d';\nexport {reverse3d} from './reverse_3d';\nexport {reverse4d} from './reverse_4d';\nexport {round} from './round';\nexport {rsqrt} from './rsqrt';\nexport {scalar} from './scalar';\nexport {selu} from './selu';\nexport {separableConv2d} from './separable_conv2d';\nexport {setdiff1dAsync} from './setdiff1d_async';\nexport {sigmoid} from './sigmoid';\nexport {sign} from './sign';\nexport {sin} from './sin';\nexport {sinh} from './sinh';\nexport {slice} from './slice';\nexport {slice1d} from './slice1d';\nexport {slice2d} from './slice2d';\nexport {slice3d} from './slice3d';\nexport {slice4d} from './slice4d';\nexport {softmax} from './softmax';\nexport {softplus} from './softplus';\nexport {spaceToBatchND} from './space_to_batch_nd';\nexport {fft} from './spectral/fft';\nexport {ifft} from './spectral/ifft';\nexport {irfft} from './spectral/irfft';\nexport {rfft} from './spectral/rfft';\nexport {split} from './split';\nexport {sqrt} from './sqrt';\nexport {square} from './square';\nexport {squaredDifference} from './squared_difference';\nexport {squeeze} from './squeeze';\nexport {stack} from './stack';\nexport {step} from './step';\nexport {stridedSlice} from './strided_slice';\nexport {sub} from './sub';\nexport {sum} from './sum';\nexport {tan} from './tan';\nexport {tanh} from './tanh';\nexport {tensor} from './tensor';\nexport {tensor1d} from './tensor1d';\nexport {tensor2d} from './tensor2d';\nexport {tensor3d} from './tensor3d';\nexport {tensor4d} from './tensor4d';\nexport {tensor5d} from './tensor5d';\nexport {tensor6d} from './tensor6d';\nexport {tensorScatterUpdate} from './tensor_scatter_update';\nexport {tile} from './tile';\nexport {topk} from './topk';\nexport {truncatedNormal} from './truncated_normal';\nexport {unique} from './unique';\nexport {unsortedSegmentSum} from './unsorted_segment_sum';\nexport {unstack} from './unstack';\nexport {upperBound} from './upper_bound';\nexport {variable} from './variable';\nexport {where} from './where';\nexport {whereAsync} from './where_async';\nexport {zeros} from './zeros';\nexport {zerosLike} from './zeros_like';\n\nexport * from './boolean_mask';\nexport * from './transpose';\nexport * from './norm';\nexport * from './moving_average';\nexport * from './scatter_nd';\nexport * from './search_sorted';\nexport * from './sparse_to_dense';\nexport * from './gather_nd';\nexport * from './dropout';\nexport * from './signal_ops_util';\nexport * from './in_top_k';\n\nexport {op, OP_SCOPE_SUFFIX} from './operation';\n\nimport {rfft} from './spectral/rfft';\nimport {fft} from './spectral/fft';\nimport {ifft} from './spectral/ifft';\nimport {irfft} from './spectral/irfft';\nconst spectral = {\n  fft,\n  ifft,\n  rfft,\n  irfft\n};\n\nimport * as fused from './fused_ops';\n\nimport {hammingWindow} from './signal/hamming_window';\nimport {hannWindow} from './signal/hann_window';\nimport {frame} from './signal/frame';\nimport {stft} from './signal/stft';\nconst signal = {\n  hammingWindow,\n  hannWindow,\n  frame,\n  stft,\n};\n\n// Image Ops namespace\nimport {cropAndResize} from './image/crop_and_resize';\nimport {flipLeftRight} from './image/flip_left_right';\nimport {grayscaleToRGB} from './image/grayscale_to_rgb';\nimport {rgbToGrayscale} from './image/rgb_to_grayscale';\nimport {rotateWithOffset} from './image/rotate_with_offset';\nimport {nonMaxSuppression} from './image/non_max_suppression';\nimport {nonMaxSuppressionAsync} from './image/non_max_suppression_async';\nimport {nonMaxSuppressionWithScore} from './image/non_max_suppression_with_score';\nimport {nonMaxSuppressionWithScoreAsync} from './image/non_max_suppression_with_score_async';\nimport {nonMaxSuppressionPadded} from './image/non_max_suppression_padded';\nimport {nonMaxSuppressionPaddedAsync} from './image/non_max_suppression_padded_async';\nimport {resizeBilinear} from './image/resize_bilinear';\nimport {resizeNearestNeighbor} from './image/resize_nearest_neighbor';\nimport {threshold} from './image/threshold';\nimport {transform} from './image/transform';\nconst image = {\n  flipLeftRight,\n  grayscaleToRGB,\n  resizeNearestNeighbor,\n  resizeBilinear,\n  rgbToGrayscale,\n  rotateWithOffset,\n  cropAndResize,\n  nonMaxSuppression,\n  nonMaxSuppressionAsync,\n  nonMaxSuppressionWithScore,\n  nonMaxSuppressionWithScoreAsync,\n  nonMaxSuppressionPadded,\n  nonMaxSuppressionPaddedAsync,\n  threshold,\n  transform\n};\n\n// linalg namespace\nimport {bandPart} from './linalg/band_part';\nimport {gramSchmidt} from './linalg/gram_schmidt';\nimport {qr} from './linalg/qr';\nconst linalg = {\n  bandPart,\n  gramSchmidt,\n  qr\n};\n\n// losses namespace;\nimport {absoluteDifference} from './losses/absolute_difference';\nimport {computeWeightedLoss} from './losses/compute_weighted_loss';\nimport {cosineDistance} from './losses/cosine_distance';\nimport {hingeLoss} from './losses/hinge_loss';\nimport {huberLoss} from './losses/huber_loss';\nimport {logLoss} from './losses/log_loss';\nimport {meanSquaredError} from './losses/mean_squared_error';\nimport {sigmoidCrossEntropy} from './losses/sigmoid_cross_entropy';\nimport {softmaxCrossEntropy} from './losses/softmax_cross_entropy';\nconst losses = {\n  absoluteDifference,\n  computeWeightedLoss,\n  cosineDistance,\n  hingeLoss,\n  huberLoss,\n  logLoss,\n  meanSquaredError,\n  sigmoidCrossEntropy,\n  softmaxCrossEntropy\n};\n\nimport {sparseFillEmptyRows} from './sparse/sparse_fill_empty_rows';\nimport {sparseReshape} from './sparse/sparse_reshape';\nimport {sparseSegmentMean} from './sparse/sparse_segment_mean';\nimport {sparseSegmentSum} from './sparse/sparse_segment_sum';\nconst sparse = {\n  sparseFillEmptyRows,\n  sparseReshape,\n  sparseSegmentMean,\n  sparseSegmentSum\n};\n\nimport {stringNGrams} from './string/string_n_grams';\nimport {stringSplit} from './string/string_split';\nimport {stringToHashBucketFast} from './string/string_to_hash_bucket_fast';\nimport {staticRegexReplace} from './string/static_regex_replace';\n// tslint:disable-next-line:variable-name\nconst string = {\n  stringNGrams,\n  stringSplit,\n  stringToHashBucketFast,\n  staticRegexReplace,\n};\n\n// Second level exports.\nexport {image, linalg, losses, spectral, fused, signal, sparse, string};\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAiBA;AACA,SAAQA,GAAG,QAAO,OAAO;AACzB,SAAQC,IAAI,QAAO,QAAQ;AAC3B,SAAQC,KAAK,QAAO,SAAS;AAC7B,SAAQC,GAAG,QAAO,OAAO;AACzB,SAAQC,IAAI,QAAO,SAAS;AAC5B,SAAQC,GAAG,QAAO,OAAO;AACzB,SAAQC,GAAG,QAAO,OAAO;AACzB,SAAQC,MAAM,QAAO,WAAW;AAChC,SAAQC,MAAM,QAAO,WAAW;AAChC,SAAQC,IAAI,QAAO,QAAQ;AAC3B,SAAQC,KAAK,QAAO,SAAS;AAC7B,SAAQC,IAAI,QAAO,QAAQ;AAC3B,SAAQC,KAAK,QAAO,SAAS;AAC7B,SAAQC,KAAK,QAAO,SAAS;AAC7B,SAAQC,OAAO,QAAO,YAAY;AAClC,SAAQC,SAAS,QAAO,eAAe;AACvC,SAAQC,aAAa,QAAO,mBAAmB;AAC/C,SAAQC,cAAc,QAAO,qBAAqB;AAClD,SAAQC,SAAS,QAAO,aAAa;AACrC,SAAQC,WAAW,QAAO,eAAe;AACzC,SAAQC,WAAW,QAAO,eAAe;AACzC,SAAQC,WAAW,QAAO,eAAe;AACzC,SAAQC,QAAQ,QAAO,YAAY;AACnC,SAAQC,UAAU,QAAO,eAAe;AACxC,SAAQC,aAAa,QAAO,kBAAkB;AAC9C,SAAQC,WAAW,QAAO,gBAAgB;AAC1C,SAAQC,MAAM,QAAO,UAAU;AAC/B,SAAQC,IAAI,QAAO,QAAQ;AAC3B,SAAQC,IAAI,QAAO,QAAQ;AAC3B,SAAQC,WAAW,QAAO,iBAAiB;AAC3C,SAAQC,KAAK,QAAO,SAAS;AAC7B,SAAQC,OAAO,QAAO,WAAW;AACjC,SAAQC,MAAM,QAAO,UAAU;AAC/B,SAAQC,QAAQ,QAAO,aAAa;AACpC,SAAQC,QAAQ,QAAO,aAAa;AACpC,SAAQC,QAAQ,QAAO,aAAa;AACpC,SAAQC,QAAQ,QAAO,aAAa;AACpC,SAAQC,MAAM,QAAO,UAAU;AAC/B,SAAQC,MAAM,QAAO,UAAU;AAC/B,SAAQC,eAAe,QAAO,oBAAoB;AAClD,SAAQC,MAAM,QAAO,UAAU;AAC/B,SAAQC,eAAe,QAAO,oBAAoB;AAClD,SAAQC,GAAG,QAAO,OAAO;AACzB,SAAQC,IAAI,QAAO,QAAQ;AAC3B,SAAQC,OAAO,QAAO,WAAW;AACjC,SAAQC,MAAM,QAAO,UAAU;AAC/B,SAAQC,aAAa,QAAO,kBAAkB;AAC9C,SAAQC,YAAY,QAAO,kBAAkB;AAC7C,SAAQC,eAAe,QAAO,oBAAoB;AAClD,SAAQC,IAAI,QAAO,QAAQ;AAC3B,SAAQC,UAAU,QAAO,cAAc;AACvC,SAAQC,GAAG,QAAO,OAAO;AACzB,SAAQC,QAAQ,QAAO,cAAc;AACrC,SAAQC,GAAG,QAAO,OAAO;AACzB,SAAQC,MAAM,QAAO,UAAU;AAC/B,SAAQC,GAAG,QAAO,OAAO;AACzB,SAAQC,WAAW,QAAO,gBAAgB;AAC1C,SAAQC,KAAK,QAAO,SAAS;AAC7B,SAAQC,GAAG,QAAO,OAAO;AACzB,SAAQC,aAAa,QAAO,kBAAkB;AAC9C,SAAQC,GAAG,QAAO,OAAO;AACzB,SAAQC,UAAU,QAAO,eAAe;AACxC,SAAQC,KAAK,QAAO,SAAS;AAC7B,SAAQC,GAAG,QAAO,OAAO;AACzB,SAAQC,IAAI,QAAO,QAAQ;AAC3B,SAAQC,KAAK,QAAO,SAAS;AAC7B,SAAQC,QAAQ,QAAO,YAAY;AACnC,SAAQC,MAAM,QAAO,UAAU;AAC/B,SAAQC,OAAO,QAAO,WAAW;AACjC,SAAQC,YAAY,QAAO,iBAAiB;AAC5C,SAAQC,IAAI,QAAO,QAAQ;AAC3B,SAAQC,QAAQ,QAAO,aAAa;AACpC,SAAQC,KAAK,QAAO,UAAU;AAC9B,SAAQC,KAAK,QAAO,UAAU;AAC9B,SAAQC,SAAS,QAAO,cAAc;AACtC,SAAQC,IAAI,QAAO,QAAQ;AAC3B,SAAQC,SAAS,QAAO,cAAc;AACtC,SAAQC,QAAQ,QAAO,YAAY;AACnC,SAAQC,0BAA0B,QAAO,gCAAgC;AACzE,SAAQC,GAAG,QAAO,OAAO;AACzB,SAAQC,KAAK,QAAO,SAAS;AAC7B,SAAQC,UAAU,QAAO,eAAe;AACxC,SAAQC,UAAU,QAAO,eAAe;AACxC,SAAQC,SAAS,QAAO,eAAe;AACvC,SAAQC,UAAU,QAAO,eAAe;AACxC,SAAQC,UAAU,QAAO,eAAe;AACxC,SAAQC,SAAS,QAAO,cAAc;AACtC,SAAQC,UAAU,QAAO,eAAe;AACxC,SAAQC,UAAU,QAAO,eAAe;AACxC,SAAQC,MAAM,QAAO,WAAW;AAChC,SAAQC,GAAG,QAAO,OAAO;AACzB,SAAQC,OAAO,QAAO,YAAY;AAClC,SAAQC,SAAS,QAAO,eAAe;AACvC,SAAQC,iBAAiB,QAAO,wBAAwB;AACxD,SAAQC,OAAO,QAAO,WAAW;AACjC,SAAQC,IAAI,QAAO,QAAQ;AAC3B,SAAQC,QAAQ,QAAO,YAAY;AACnC,SAAQC,GAAG,QAAO,OAAO;AACzB,SAAQC,OAAO,QAAO,WAAW;AACjC,SAAQC,SAAS,QAAO,cAAc;AACtC,SAAQC,GAAG,QAAO,OAAO;AACzB,SAAQC,OAAO,QAAO,WAAW;AACjC,SAAQC,GAAG,QAAO,OAAO;AACzB,SAAsBC,YAAY,QAAO,kBAAkB;AAC3D,SAAQC,WAAW,QAAO,eAAe;AACzC,SAAQC,GAAG,QAAO,OAAO;AACzB,SAAQC,QAAQ,QAAO,aAAa;AACpC,SAAQC,MAAM,QAAO,WAAW;AAChC,SAAQC,IAAI,QAAO,QAAQ;AAC3B,SAAQC,QAAQ,QAAO,aAAa;AACpC,SAAQC,YAAY,QAAO,iBAAiB;AAC5C,SAAQC,GAAG,QAAO,OAAO;AACzB,SAAQC,KAAK,QAAO,SAAS;AAC7B,SAAQC,KAAK,QAAO,SAAS;AAC7B,SAAQC,KAAK,QAAO,SAAS;AAC7B,SAAQC,KAAK,QAAO,SAAS;AAC7B,SAAQC,IAAI,QAAO,QAAQ;AAC3B,SAAQC,GAAG,QAAO,OAAO;AACzB,SAAQC,KAAK,QAAO,SAAS;AAC7B,SAAQC,KAAK,QAAO,SAAS;AAC7B,SAAQC,IAAI,QAAO,QAAQ;AAC3B,SAAQC,YAAY,QAAO,iBAAiB;AAC5C,SAAQC,WAAW,QAAO,gBAAgB;AAC1C,SAAQC,oBAAoB,QAAO,2BAA2B;AAC9D,SAAQC,IAAI,QAAO,QAAQ;AAC3B,SAAQC,WAAW,QAAO,gBAAgB;AAC1C,SAAQC,YAAY,QAAO,iBAAiB;AAC5C,SAAQC,oBAAoB,QAAO,0BAA0B;AAC7D,SAAQC,aAAa,QAAO,kBAAkB;AAC9C,SAAQC,gBAAgB,QAAO,sBAAsB;AACrD,SAAQC,KAAK,QAAO,SAAS;AAC7B,SAAQC,IAAI,QAAO,QAAQ;AAC3B,SAAQC,UAAU,QAAO,cAAc;AACvC,SAAQC,IAAI,QAAO,QAAQ;AAC3B,SAAQC,KAAK,QAAO,SAAS;AAC7B,SAAQC,OAAO,QAAO,WAAW;AACjC,SAAQC,OAAO,QAAO,WAAW;AACjC,SAAQC,SAAS,QAAO,cAAc;AACtC,SAAQC,SAAS,QAAO,cAAc;AACtC,SAAQC,SAAS,QAAO,cAAc;AACtC,SAAQC,SAAS,QAAO,cAAc;AACtC,SAAQC,KAAK,QAAO,SAAS;AAC7B,SAAQC,KAAK,QAAO,SAAS;AAC7B,SAAQC,MAAM,QAAO,UAAU;AAC/B,SAAQC,IAAI,QAAO,QAAQ;AAC3B,SAAQC,eAAe,QAAO,oBAAoB;AAClD,SAAQC,cAAc,QAAO,mBAAmB;AAChD,SAAQC,OAAO,QAAO,WAAW;AACjC,SAAQC,IAAI,QAAO,QAAQ;AAC3B,SAAQC,GAAG,QAAO,OAAO;AACzB,SAAQC,IAAI,QAAO,QAAQ;AAC3B,SAAQC,KAAK,QAAO,SAAS;AAC7B,SAAQC,OAAO,QAAO,WAAW;AACjC,SAAQC,OAAO,QAAO,WAAW;AACjC,SAAQC,OAAO,QAAO,WAAW;AACjC,SAAQC,OAAO,QAAO,WAAW;AACjC,SAAQC,OAAO,QAAO,WAAW;AACjC,SAAQC,QAAQ,QAAO,YAAY;AACnC,SAAQC,cAAc,QAAO,qBAAqB;AAClD,SAAQC,GAAG,QAAO,gBAAgB;AAClC,SAAQC,IAAI,QAAO,iBAAiB;AACpC,SAAQC,KAAK,QAAO,kBAAkB;AACtC,SAAQC,IAAI,QAAO,iBAAiB;AACpC,SAAQC,KAAK,QAAO,SAAS;AAC7B,SAAQC,IAAI,QAAO,QAAQ;AAC3B,SAAQC,MAAM,QAAO,UAAU;AAC/B,SAAQC,iBAAiB,QAAO,sBAAsB;AACtD,SAAQC,OAAO,QAAO,WAAW;AACjC,SAAQC,KAAK,QAAO,SAAS;AAC7B,SAAQC,IAAI,QAAO,QAAQ;AAC3B,SAAQC,YAAY,QAAO,iBAAiB;AAC5C,SAAQC,GAAG,QAAO,OAAO;AACzB,SAAQC,GAAG,QAAO,OAAO;AACzB,SAAQC,GAAG,QAAO,OAAO;AACzB,SAAQC,IAAI,QAAO,QAAQ;AAC3B,SAAQC,MAAM,QAAO,UAAU;AAC/B,SAAQC,QAAQ,QAAO,YAAY;AACnC,SAAQC,QAAQ,QAAO,YAAY;AACnC,SAAQC,QAAQ,QAAO,YAAY;AACnC,SAAQC,QAAQ,QAAO,YAAY;AACnC,SAAQC,QAAQ,QAAO,YAAY;AACnC,SAAQC,QAAQ,QAAO,YAAY;AACnC,SAAQC,mBAAmB,QAAO,yBAAyB;AAC3D,SAAQC,IAAI,QAAO,QAAQ;AAC3B,SAAQC,IAAI,QAAO,QAAQ;AAC3B,SAAQC,eAAe,QAAO,oBAAoB;AAClD,SAAQC,MAAM,QAAO,UAAU;AAC/B,SAAQC,kBAAkB,QAAO,wBAAwB;AACzD,SAAQC,OAAO,QAAO,WAAW;AACjC,SAAQC,UAAU,QAAO,eAAe;AACxC,SAAQC,QAAQ,QAAO,YAAY;AACnC,SAAQC,KAAK,QAAO,SAAS;AAC7B,SAAQC,UAAU,QAAO,eAAe;AACxC,SAAQC,KAAK,QAAO,SAAS;AAC7B,SAAQC,SAAS,QAAO,cAAc;AAEtC,cAAc,gBAAgB;AAC9B,cAAc,aAAa;AAC3B,cAAc,QAAQ;AACtB,cAAc,kBAAkB;AAChC,cAAc,cAAc;AAC5B,cAAc,iBAAiB;AAC/B,cAAc,mBAAmB;AACjC,cAAc,aAAa;AAC3B,cAAc,WAAW;AACzB,cAAc,mBAAmB;AACjC,cAAc,YAAY;AAE1B,SAAQC,EAAE,EAAEC,eAAe,QAAO,aAAa;AAE/C,SAAQlC,IAAI,QAAO,iBAAiB;AACpC,SAAQH,GAAG,QAAO,gBAAgB;AAClC,SAAQC,IAAI,QAAO,iBAAiB;AACpC,SAAQC,KAAK,QAAO,kBAAkB;AACtC,MAAMoC,QAAQ,GAAG;EACftC,GAAG;EACHC,IAAI;EACJE,IAAI;EACJD;CACD;AAED,OAAO,KAAKqC,KAAK,MAAM,aAAa;AAEpC,SAAQC,aAAa,QAAO,yBAAyB;AACrD,SAAQC,UAAU,QAAO,sBAAsB;AAC/C,SAAQC,KAAK,QAAO,gBAAgB;AACpC,SAAQC,IAAI,QAAO,eAAe;AAClC,MAAMC,MAAM,GAAG;EACbJ,aAAa;EACbC,UAAU;EACVC,KAAK;EACLC;CACD;AAED;AACA,SAAQE,aAAa,QAAO,yBAAyB;AACrD,SAAQC,aAAa,QAAO,yBAAyB;AACrD,SAAQC,cAAc,QAAO,0BAA0B;AACvD,SAAQC,cAAc,QAAO,0BAA0B;AACvD,SAAQC,gBAAgB,QAAO,4BAA4B;AAC3D,SAAQC,iBAAiB,QAAO,6BAA6B;AAC7D,SAAQC,sBAAsB,QAAO,mCAAmC;AACxE,SAAQC,0BAA0B,QAAO,wCAAwC;AACjF,SAAQC,+BAA+B,QAAO,8CAA8C;AAC5F,SAAQC,uBAAuB,QAAO,oCAAoC;AAC1E,SAAQC,4BAA4B,QAAO,0CAA0C;AACrF,SAAQC,cAAc,QAAO,yBAAyB;AACtD,SAAQC,qBAAqB,QAAO,iCAAiC;AACrE,SAAQC,SAAS,QAAO,mBAAmB;AAC3C,SAAQC,SAAS,QAAO,mBAAmB;AAC3C,MAAMC,KAAK,GAAG;EACZd,aAAa;EACbC,cAAc;EACdU,qBAAqB;EACrBD,cAAc;EACdR,cAAc;EACdC,gBAAgB;EAChBJ,aAAa;EACbK,iBAAiB;EACjBC,sBAAsB;EACtBC,0BAA0B;EAC1BC,+BAA+B;EAC/BC,uBAAuB;EACvBC,4BAA4B;EAC5BG,SAAS;EACTC;CACD;AAED;AACA,SAAQE,QAAQ,QAAO,oBAAoB;AAC3C,SAAQC,WAAW,QAAO,uBAAuB;AACjD,SAAQC,EAAE,QAAO,aAAa;AAC9B,MAAMC,MAAM,GAAG;EACbH,QAAQ;EACRC,WAAW;EACXC;CACD;AAED;AACA,SAAQE,kBAAkB,QAAO,8BAA8B;AAC/D,SAAQC,mBAAmB,QAAO,gCAAgC;AAClE,SAAQC,cAAc,QAAO,0BAA0B;AACvD,SAAQC,SAAS,QAAO,qBAAqB;AAC7C,SAAQC,SAAS,QAAO,qBAAqB;AAC7C,SAAQC,OAAO,QAAO,mBAAmB;AACzC,SAAQC,gBAAgB,QAAO,6BAA6B;AAC5D,SAAQC,mBAAmB,QAAO,gCAAgC;AAClE,SAAQC,mBAAmB,QAAO,gCAAgC;AAClE,MAAMC,MAAM,GAAG;EACbT,kBAAkB;EAClBC,mBAAmB;EACnBC,cAAc;EACdC,SAAS;EACTC,SAAS;EACTC,OAAO;EACPC,gBAAgB;EAChBC,mBAAmB;EACnBC;CACD;AAED,SAAQE,mBAAmB,QAAO,iCAAiC;AACnE,SAAQC,aAAa,QAAO,yBAAyB;AACrD,SAAQC,iBAAiB,QAAO,8BAA8B;AAC9D,SAAQC,gBAAgB,QAAO,6BAA6B;AAC5D,MAAMC,MAAM,GAAG;EACbJ,mBAAmB;EACnBC,aAAa;EACbC,iBAAiB;EACjBC;CACD;AAED,SAAQE,YAAY,QAAO,yBAAyB;AACpD,SAAQC,WAAW,QAAO,uBAAuB;AACjD,SAAQC,sBAAsB,QAAO,qCAAqC;AAC1E,SAAQC,kBAAkB,QAAO,+BAA+B;AAChE;AACA,MAAMC,MAAM,GAAG;EACbJ,YAAY;EACZC,WAAW;EACXC,sBAAsB;EACtBC;CACD;AAED;AACA,SAAQvB,KAAK,EAAEI,MAAM,EAAEU,MAAM,EAAEpC,QAAQ,EAAEC,KAAK,EAAEK,MAAM,EAAEmC,MAAM,EAAEK,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}