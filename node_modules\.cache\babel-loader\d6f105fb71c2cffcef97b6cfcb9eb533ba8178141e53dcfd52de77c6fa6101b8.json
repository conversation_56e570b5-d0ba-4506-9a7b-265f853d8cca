{"ast": null, "code": "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { oneHot } from '../../ops/one_hot';\nimport { getGlobalTensorClass } from '../../tensor';\ngetGlobalTensorClass().prototype.oneHot = function (depth) {\n  let onValue = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 1;\n  let offValue = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 0;\n  this.throwIfDisposed();\n  return oneHot(this, depth, onValue, offValue);\n};", "map": {"version": 3, "names": ["oneHot", "getGlobalTensorClass", "prototype", "depth", "onValue", "arguments", "length", "undefined", "offValue", "throwIfDisposed"], "sources": ["C:\\tfjs-core\\src\\public\\chained_ops\\one_hot.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {oneHot} from '../../ops/one_hot';\nimport {getGlobalTensorClass, Tensor} from '../../tensor';\nimport {Rank} from '../../types';\n\ndeclare module '../../tensor' {\n  interface Tensor<R extends Rank = Rank> {\n    oneHot(depth: number, onValue: number, offValue: number): Tensor;\n  }\n}\n\ngetGlobalTensorClass().prototype.oneHot = function(\n    depth: number, onValue = 1, offValue = 0): Tensor {\n  this.throwIfDisposed();\n  return oneHot(this, depth, onValue, offValue);\n};\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAiBA,SAAQA,MAAM,QAAO,mBAAmB;AACxC,SAAQC,oBAAoB,QAAe,cAAc;AASzDA,oBAAoB,EAAE,CAACC,SAAS,CAACF,MAAM,GAAG,UACtCG,KAAa,EAA2B;EAAA,IAAzBC,OAAO,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC;EAAA,IAAEG,QAAQ,GAAAH,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC;EAC1C,IAAI,CAACI,eAAe,EAAE;EACtB,OAAOT,MAAM,CAAC,IAAI,EAAEG,KAAK,EAAEC,OAAO,EAAEI,QAAQ,CAAC;AAC/C,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}