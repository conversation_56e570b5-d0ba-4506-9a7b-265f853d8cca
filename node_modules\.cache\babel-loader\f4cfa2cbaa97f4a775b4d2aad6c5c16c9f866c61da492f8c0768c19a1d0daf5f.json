{"ast": null, "code": "/**\n * @license\n * Copyright 2019 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport * as broadcast_util from './broadcast_util';\nimport { elu } from './elu';\nimport { leakyRelu } from './leaky_relu';\nimport { mul } from './mul';\nimport { prelu } from './prelu';\nimport { relu } from './relu';\nimport { relu6 } from './relu6';\nimport { reshape } from './reshape';\nimport { sigmoid } from './sigmoid';\nimport { step } from './step';\nimport { sum } from './sum';\n// Returns gradient for fused activation.\nexport function getFusedDyActivation(dy, y, activation) {\n  if (activation == null || activation === 'linear') {\n    return dy;\n  }\n  if (activation === 'relu') {\n    return mul(dy, step(y));\n  }\n  throw new Error(\"Cannot compute gradient for fused activation \".concat(activation, \".\"));\n}\n// Returns gradient for fused bias.\nexport function getFusedBiasGradient(bias, dyActivation) {\n  let res = dyActivation;\n  const reduceAxes = broadcast_util.getReductionAxes(bias.shape, dyActivation.shape);\n  if (reduceAxes.length > 0) {\n    res = sum(res, reduceAxes);\n  }\n  return reshape(res, bias.shape);\n}\nexport function applyActivation(x, activation, preluActivationWeights, leakyreluAlpha) {\n  if (activation === 'linear') {\n    return x;\n  } else if (activation === 'relu') {\n    return relu(x);\n  } else if (activation === 'elu') {\n    return elu(x);\n  } else if (activation === 'relu6') {\n    return relu6(x);\n  } else if (activation === 'prelu') {\n    return prelu(x, preluActivationWeights);\n  } else if (activation === 'leakyrelu') {\n    return leakyRelu(x, leakyreluAlpha);\n  } else if (activation === 'sigmoid') {\n    return sigmoid(x);\n  }\n  throw new Error(\"Unknown fused activation \".concat(activation, \".\"));\n}\n// Whether we should call fused ops.\nexport const shouldFuse = (gradientDepth, activation) => {\n  const gradientMode = gradientDepth > 0;\n  return !gradientMode || activation === 'linear';\n};", "map": {"version": 3, "names": ["broadcast_util", "elu", "leakyRelu", "mul", "prelu", "relu", "relu6", "reshape", "sigmoid", "step", "sum", "getFusedDyActivation", "dy", "y", "activation", "Error", "concat", "getFusedBiasGradient", "bias", "dyActivation", "res", "reduceAxes", "getReductionAxes", "shape", "length", "applyActivation", "x", "preluActivationWeights", "leakyreluAlpha", "shouldFuse", "gradientDepth", "gradientMode"], "sources": ["C:\\tfjs-core\\src\\ops\\fused_util.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2019 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {Tensor} from '../tensor';\n\nimport * as broadcast_util from './broadcast_util';\nimport {elu} from './elu';\nimport {Activation} from './fused_types';\nimport {leakyRelu} from './leaky_relu';\nimport {mul} from './mul';\nimport {prelu} from './prelu';\nimport {relu} from './relu';\nimport {relu6} from './relu6';\nimport {reshape} from './reshape';\nimport {sigmoid} from './sigmoid';\nimport {step} from './step';\nimport {sum} from './sum';\n\n// Returns gradient for fused activation.\nexport function getFusedDyActivation(\n    dy: Tensor, y: Tensor, activation: Activation): Tensor {\n  if (activation == null || activation === 'linear') {\n    return dy;\n  }\n  if (activation === 'relu') {\n    return mul(dy, step(y));\n  }\n  throw new Error(\n      `Cannot compute gradient for fused activation ${activation}.`);\n}\n\n// Returns gradient for fused bias.\nexport function getFusedBiasGradient(\n    bias: Tensor, dyActivation: Tensor): Tensor {\n  let res = dyActivation;\n  const reduceAxes =\n      broadcast_util.getReductionAxes(bias.shape, dyActivation.shape);\n  if (reduceAxes.length > 0) {\n    res = sum(res, reduceAxes);\n  }\n  return reshape(res, bias.shape);\n}\n\nexport function applyActivation(\n    x: Tensor, activation: Activation, preluActivationWeights?: Tensor,\n    leakyreluAlpha?: number): Tensor {\n  if (activation === 'linear') {\n    return x;\n  } else if (activation === 'relu') {\n    return relu(x);\n  } else if (activation === 'elu') {\n    return elu(x);\n  } else if (activation === 'relu6') {\n    return relu6(x);\n  } else if (activation === 'prelu') {\n    return prelu(x, preluActivationWeights);\n  } else if (activation === 'leakyrelu') {\n    return leakyRelu(x, leakyreluAlpha);\n  } else if (activation === 'sigmoid') {\n    return sigmoid(x);\n  }\n  throw new Error(`Unknown fused activation ${activation}.`);\n}\n\n// Whether we should call fused ops.\nexport const shouldFuse = (gradientDepth: number, activation: Activation) => {\n  const gradientMode = gradientDepth > 0;\n  return !gradientMode || activation === 'linear';\n};\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAmBA,OAAO,KAAKA,cAAc,MAAM,kBAAkB;AAClD,SAAQC,GAAG,QAAO,OAAO;AAEzB,SAAQC,SAAS,QAAO,cAAc;AACtC,SAAQC,GAAG,QAAO,OAAO;AACzB,SAAQC,KAAK,QAAO,SAAS;AAC7B,SAAQC,IAAI,QAAO,QAAQ;AAC3B,SAAQC,KAAK,QAAO,SAAS;AAC7B,SAAQC,OAAO,QAAO,WAAW;AACjC,SAAQC,OAAO,QAAO,WAAW;AACjC,SAAQC,IAAI,QAAO,QAAQ;AAC3B,SAAQC,GAAG,QAAO,OAAO;AAEzB;AACA,OAAM,SAAUC,oBAAoBA,CAChCC,EAAU,EAAEC,CAAS,EAAEC,UAAsB;EAC/C,IAAIA,UAAU,IAAI,IAAI,IAAIA,UAAU,KAAK,QAAQ,EAAE;IACjD,OAAOF,EAAE;;EAEX,IAAIE,UAAU,KAAK,MAAM,EAAE;IACzB,OAAOX,GAAG,CAACS,EAAE,EAAEH,IAAI,CAACI,CAAC,CAAC,CAAC;;EAEzB,MAAM,IAAIE,KAAK,iDAAAC,MAAA,CACqCF,UAAU,MAAG,CAAC;AACpE;AAEA;AACA,OAAM,SAAUG,oBAAoBA,CAChCC,IAAY,EAAEC,YAAoB;EACpC,IAAIC,GAAG,GAAGD,YAAY;EACtB,MAAME,UAAU,GACZrB,cAAc,CAACsB,gBAAgB,CAACJ,IAAI,CAACK,KAAK,EAAEJ,YAAY,CAACI,KAAK,CAAC;EACnE,IAAIF,UAAU,CAACG,MAAM,GAAG,CAAC,EAAE;IACzBJ,GAAG,GAAGV,GAAG,CAACU,GAAG,EAAEC,UAAU,CAAC;;EAE5B,OAAOd,OAAO,CAACa,GAAG,EAAEF,IAAI,CAACK,KAAK,CAAC;AACjC;AAEA,OAAM,SAAUE,eAAeA,CAC3BC,CAAS,EAAEZ,UAAsB,EAAEa,sBAA+B,EAClEC,cAAuB;EACzB,IAAId,UAAU,KAAK,QAAQ,EAAE;IAC3B,OAAOY,CAAC;GACT,MAAM,IAAIZ,UAAU,KAAK,MAAM,EAAE;IAChC,OAAOT,IAAI,CAACqB,CAAC,CAAC;GACf,MAAM,IAAIZ,UAAU,KAAK,KAAK,EAAE;IAC/B,OAAOb,GAAG,CAACyB,CAAC,CAAC;GACd,MAAM,IAAIZ,UAAU,KAAK,OAAO,EAAE;IACjC,OAAOR,KAAK,CAACoB,CAAC,CAAC;GAChB,MAAM,IAAIZ,UAAU,KAAK,OAAO,EAAE;IACjC,OAAOV,KAAK,CAACsB,CAAC,EAAEC,sBAAsB,CAAC;GACxC,MAAM,IAAIb,UAAU,KAAK,WAAW,EAAE;IACrC,OAAOZ,SAAS,CAACwB,CAAC,EAAEE,cAAc,CAAC;GACpC,MAAM,IAAId,UAAU,KAAK,SAAS,EAAE;IACnC,OAAON,OAAO,CAACkB,CAAC,CAAC;;EAEnB,MAAM,IAAIX,KAAK,6BAAAC,MAAA,CAA6BF,UAAU,MAAG,CAAC;AAC5D;AAEA;AACA,OAAO,MAAMe,UAAU,GAAGA,CAACC,aAAqB,EAAEhB,UAAsB,KAAI;EAC1E,MAAMiB,YAAY,GAAGD,aAAa,GAAG,CAAC;EACtC,OAAO,CAACC,YAAY,IAAIjB,UAAU,KAAK,QAAQ;AACjD,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}