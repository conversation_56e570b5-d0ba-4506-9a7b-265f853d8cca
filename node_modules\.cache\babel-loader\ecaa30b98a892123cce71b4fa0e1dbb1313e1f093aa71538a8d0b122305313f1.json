{"ast": null, "code": "/**\n * @license\n * Copyright 2022 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { RaggedTensorToTensor } from '@tensorflow/tfjs-core';\nimport { raggedTensorToTensorImplCPU } from '../kernel_utils/shared';\nexport function raggedTensorToTensor(args) {\n  const {\n    inputs,\n    backend,\n    attrs\n  } = args;\n  const {\n    shape,\n    values,\n    defaultValue,\n    rowPartitionTensors\n  } = inputs;\n  const {\n    rowPartitionTypes\n  } = attrs;\n  const $shape = backend.readSync(shape.dataId);\n  const $values = backend.readSync(values.dataId);\n  const $defaultValue = backend.readSync(defaultValue.dataId);\n  const $rowPartitionValues = rowPartitionTensors.map(t => backend.readSync(t.dataId));\n  const rowPartitionValuesShapes = rowPartitionTensors.map(t => t.shape);\n  const [outputShape, output] = raggedTensorToTensorImplCPU($shape, shape.shape, $values, values.shape, values.dtype, $defaultValue, defaultValue.shape, $rowPartitionValues, rowPartitionValuesShapes, rowPartitionTypes);\n  return backend.makeTensorInfo(outputShape, values.dtype, output);\n}\nexport const raggedTensorToTensorConfig = {\n  kernelName: RaggedTensorToTensor,\n  backendName: 'webgl',\n  kernelFunc: raggedTensorToTensor\n};", "map": {"version": 3, "names": ["RaggedTensorToTensor", "raggedTensorToTensorImplCPU", "raggedTensorToTensor", "args", "inputs", "backend", "attrs", "shape", "values", "defaultValue", "rowPartitionTensors", "rowPartitionTypes", "$shape", "readSync", "dataId", "$values", "$defaultValue", "$rowPartitionValues", "map", "t", "rowPartitionValuesShapes", "outputShape", "output", "dtype", "makeTensorInfo", "raggedTensorToTensorConfig", "kernelName", "backendName", "kernelFunc"], "sources": ["C:\\tfjs-backend-webgl\\src\\kernels\\RaggedTensorToTensor.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2022 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {KernelConfig, KernelFunc, RaggedTensorToTensor, RaggedTensorToTensorAttrs, RaggedTensorToTensorInputs, TensorInfo, TypedArray} from '@tensorflow/tfjs-core';\n\nimport {MathBackendWebGL} from '../backend_webgl';\nimport {raggedTensorToTensorImplCPU} from '../kernel_utils/shared';\n\nexport function raggedTensorToTensor(args: {\n  inputs: RaggedTensorToTensorInputs,\n  backend: MathBackendWebGL,\n  attrs: RaggedTensorToTensorAttrs\n}): TensorInfo {\n  const {inputs, backend, attrs} = args;\n  const {shape, values, defaultValue, rowPartitionTensors} = inputs;\n  const {rowPartitionTypes} = attrs;\n\n  const $shape = backend.readSync(shape.dataId) as TypedArray;\n  const $values = backend.readSync(values.dataId) as TypedArray;\n  const $defaultValue = backend.readSync(defaultValue.dataId) as TypedArray;\n  const $rowPartitionValues =\n      rowPartitionTensors.map(t => backend.readSync(t.dataId) as TypedArray);\n  const rowPartitionValuesShapes = rowPartitionTensors.map(t => t.shape);\n\n  const [outputShape, output] = raggedTensorToTensorImplCPU(\n      $shape, shape.shape, $values, values.shape, values.dtype, $defaultValue,\n      defaultValue.shape, $rowPartitionValues, rowPartitionValuesShapes,\n      rowPartitionTypes);\n  return backend.makeTensorInfo(outputShape, values.dtype, output);\n}\n\nexport const raggedTensorToTensorConfig: KernelConfig = {\n  kernelName: RaggedTensorToTensor,\n  backendName: 'webgl',\n  kernelFunc: raggedTensorToTensor as unknown as KernelFunc,\n};\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAiBA,SAAkCA,oBAAoB,QAAsF,uBAAuB;AAGnK,SAAQC,2BAA2B,QAAO,wBAAwB;AAElE,OAAM,SAAUC,oBAAoBA,CAACC,IAIpC;EACC,MAAM;IAACC,MAAM;IAAEC,OAAO;IAAEC;EAAK,CAAC,GAAGH,IAAI;EACrC,MAAM;IAACI,KAAK;IAAEC,MAAM;IAAEC,YAAY;IAAEC;EAAmB,CAAC,GAAGN,MAAM;EACjE,MAAM;IAACO;EAAiB,CAAC,GAAGL,KAAK;EAEjC,MAAMM,MAAM,GAAGP,OAAO,CAACQ,QAAQ,CAACN,KAAK,CAACO,MAAM,CAAe;EAC3D,MAAMC,OAAO,GAAGV,OAAO,CAACQ,QAAQ,CAACL,MAAM,CAACM,MAAM,CAAe;EAC7D,MAAME,aAAa,GAAGX,OAAO,CAACQ,QAAQ,CAACJ,YAAY,CAACK,MAAM,CAAe;EACzE,MAAMG,mBAAmB,GACrBP,mBAAmB,CAACQ,GAAG,CAACC,CAAC,IAAId,OAAO,CAACQ,QAAQ,CAACM,CAAC,CAACL,MAAM,CAAe,CAAC;EAC1E,MAAMM,wBAAwB,GAAGV,mBAAmB,CAACQ,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACZ,KAAK,CAAC;EAEtE,MAAM,CAACc,WAAW,EAAEC,MAAM,CAAC,GAAGrB,2BAA2B,CACrDW,MAAM,EAAEL,KAAK,CAACA,KAAK,EAAEQ,OAAO,EAAEP,MAAM,CAACD,KAAK,EAAEC,MAAM,CAACe,KAAK,EAAEP,aAAa,EACvEP,YAAY,CAACF,KAAK,EAAEU,mBAAmB,EAAEG,wBAAwB,EACjET,iBAAiB,CAAC;EACtB,OAAON,OAAO,CAACmB,cAAc,CAACH,WAAW,EAAEb,MAAM,CAACe,KAAK,EAAED,MAAM,CAAC;AAClE;AAEA,OAAO,MAAMG,0BAA0B,GAAiB;EACtDC,UAAU,EAAE1B,oBAAoB;EAChC2B,WAAW,EAAE,OAAO;EACpBC,UAAU,EAAE1B;CACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}