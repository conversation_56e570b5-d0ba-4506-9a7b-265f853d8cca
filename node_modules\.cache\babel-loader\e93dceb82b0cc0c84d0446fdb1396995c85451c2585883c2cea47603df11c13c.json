{"ast": null, "code": "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { Add } from '../kernel_names';\nimport * as broadcast_util from '../ops/broadcast_util';\nimport { reshape } from '../ops/reshape';\nimport { sum } from '../ops/sum';\nexport const addGradConfig = {\n  kernelName: Add,\n  inputsToSave: ['a', 'b'],\n  gradFunc: (dy, saved) => {\n    const [a, b] = saved;\n    const outShape = broadcast_util.assertAndGetBroadcastShape(a.shape, b.shape);\n    const derA = () => {\n      let res = dy;\n      const reduceAxes = broadcast_util.getReductionAxes(a.shape, outShape);\n      if (reduceAxes.length > 0) {\n        res = sum(res, reduceAxes);\n      }\n      return reshape(res, a.shape);\n    };\n    const derB = () => {\n      let res = dy;\n      const reduceAxes = broadcast_util.getReductionAxes(b.shape, outShape);\n      if (reduceAxes.length > 0) {\n        res = sum(res, reduceAxes);\n      }\n      return reshape(res, b.shape);\n    };\n    return {\n      a: derA,\n      b: derB\n    };\n  }\n};", "map": {"version": 3, "names": ["Add", "broadcast_util", "reshape", "sum", "addGradConfig", "kernelName", "inputsToSave", "grad<PERSON>unc", "dy", "saved", "a", "b", "outShape", "assertAndGetBroadcastShape", "shape", "derA", "res", "reduceAxes", "getReductionAxes", "length", "derB"], "sources": ["C:\\tfjs-core\\src\\gradients\\Add_grad.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport {Add} from '../kernel_names';\nimport {GradConfig} from '../kernel_registry';\nimport * as broadcast_util from '../ops/broadcast_util';\nimport {reshape} from '../ops/reshape';\nimport {sum} from '../ops/sum';\nimport {Tensor} from '../tensor';\n\nexport const addGradConfig: GradConfig = {\n  kernelName: Add,\n  inputsToSave: ['a', 'b'],\n  gradFunc: (dy: Tensor, saved: Tensor[]) => {\n    const [a, b] = saved;\n    const outShape =\n        broadcast_util.assertAndGetBroadcastShape(a.shape, b.shape);\n\n    const derA = () => {\n      let res = dy;\n      const reduceAxes = broadcast_util.getReductionAxes(a.shape, outShape);\n      if (reduceAxes.length > 0) {\n        res = sum(res, reduceAxes);\n      }\n      return reshape(res, a.shape);\n    };\n    const derB = () => {\n      let res = dy;\n      const reduceAxes = broadcast_util.getReductionAxes(b.shape, outShape);\n      if (reduceAxes.length > 0) {\n        res = sum(res, reduceAxes);\n      }\n      return reshape(res, b.shape);\n    };\n\n    return {a: derA, b: derB};\n  }\n};\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAgBA,SAAQA,GAAG,QAAO,iBAAiB;AAEnC,OAAO,KAAKC,cAAc,MAAM,uBAAuB;AACvD,SAAQC,OAAO,QAAO,gBAAgB;AACtC,SAAQC,GAAG,QAAO,YAAY;AAG9B,OAAO,MAAMC,aAAa,GAAe;EACvCC,UAAU,EAAEL,GAAG;EACfM,YAAY,EAAE,CAAC,GAAG,EAAE,GAAG,CAAC;EACxBC,QAAQ,EAAEA,CAACC,EAAU,EAAEC,KAAe,KAAI;IACxC,MAAM,CAACC,CAAC,EAAEC,CAAC,CAAC,GAAGF,KAAK;IACpB,MAAMG,QAAQ,GACVX,cAAc,CAACY,0BAA0B,CAACH,CAAC,CAACI,KAAK,EAAEH,CAAC,CAACG,KAAK,CAAC;IAE/D,MAAMC,IAAI,GAAGA,CAAA,KAAK;MAChB,IAAIC,GAAG,GAAGR,EAAE;MACZ,MAAMS,UAAU,GAAGhB,cAAc,CAACiB,gBAAgB,CAACR,CAAC,CAACI,KAAK,EAAEF,QAAQ,CAAC;MACrE,IAAIK,UAAU,CAACE,MAAM,GAAG,CAAC,EAAE;QACzBH,GAAG,GAAGb,GAAG,CAACa,GAAG,EAAEC,UAAU,CAAC;;MAE5B,OAAOf,OAAO,CAACc,GAAG,EAAEN,CAAC,CAACI,KAAK,CAAC;IAC9B,CAAC;IACD,MAAMM,IAAI,GAAGA,CAAA,KAAK;MAChB,IAAIJ,GAAG,GAAGR,EAAE;MACZ,MAAMS,UAAU,GAAGhB,cAAc,CAACiB,gBAAgB,CAACP,CAAC,CAACG,KAAK,EAAEF,QAAQ,CAAC;MACrE,IAAIK,UAAU,CAACE,MAAM,GAAG,CAAC,EAAE;QACzBH,GAAG,GAAGb,GAAG,CAACa,GAAG,EAAEC,UAAU,CAAC;;MAE5B,OAAOf,OAAO,CAACc,GAAG,EAAEL,CAAC,CAACG,KAAK,CAAC;IAC9B,CAAC;IAED,OAAO;MAACJ,CAAC,EAAEK,IAAI;MAAEJ,CAAC,EAAES;IAAI,CAAC;EAC3B;CACD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}