{"ast": null, "code": "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the License);\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an AS IS BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { Sqrt } from '@tensorflow/tfjs-core';\nimport { createSimpleUnaryImpl } from '../utils/unary_impl';\nimport { unaryKernelFunc } from '../utils/unary_utils';\nexport const sqrtImpl = createSimpleUnaryImpl(xi => Math.sqrt(xi));\nexport const sqrt = unaryKernelFunc(Sqrt, xi => Math.sqrt(xi));\nexport const sqrtConfig = {\n  kernelName: Sqrt,\n  backendName: 'cpu',\n  kernelFunc: sqrt\n};", "map": {"version": 3, "names": ["Sqrt", "createSimpleUnaryImpl", "unaryKernelFunc", "sqrtImpl", "xi", "Math", "sqrt", "sqrtConfig", "kernelName", "backendName", "kernelFunc"], "sources": ["C:\\tfjs-backend-cpu\\src\\kernels\\Sqrt.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the License);\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an AS IS BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {KernelConfig, Sqrt} from '@tensorflow/tfjs-core';\n\nimport {createSimpleUnaryImpl} from '../utils/unary_impl';\nimport {unaryKernelFunc} from '../utils/unary_utils';\n\nexport const sqrtImpl = createSimpleUnaryImpl((xi) => Math.sqrt(xi));\nexport const sqrt = unaryKernelFunc(Sqrt, (xi) => Math.sqrt(xi));\n\nexport const sqrtConfig: KernelConfig = {\n  kernelName: Sqrt,\n  backendName: 'cpu',\n  kernelFunc: sqrt,\n};\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAiBA,SAAsBA,IAAI,QAAO,uBAAuB;AAExD,SAAQC,qBAAqB,QAAO,qBAAqB;AACzD,SAAQC,eAAe,QAAO,sBAAsB;AAEpD,OAAO,MAAMC,QAAQ,GAAGF,qBAAqB,CAAEG,EAAE,IAAKC,IAAI,CAACC,IAAI,CAACF,EAAE,CAAC,CAAC;AACpE,OAAO,MAAME,IAAI,GAAGJ,eAAe,CAACF,IAAI,EAAGI,EAAE,IAAKC,IAAI,CAACC,IAAI,CAACF,EAAE,CAAC,CAAC;AAEhE,OAAO,MAAMG,UAAU,GAAiB;EACtCC,UAAU,EAAER,IAAI;EAChBS,WAAW,EAAE,KAAK;EAClBC,UAAU,EAAEJ;CACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}