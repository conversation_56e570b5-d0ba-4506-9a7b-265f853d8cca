{"ast": null, "code": "import { toString } from './_setup.js';\n\n// Internal function for creating a `toString`-based type tester.\nexport default function tagTester(name) {\n  var tag = '[object ' + name + ']';\n  return function (obj) {\n    return toString.call(obj) === tag;\n  };\n}", "map": {"version": 3, "names": ["toString", "tagTester", "name", "tag", "obj", "call"], "sources": ["C:/tmsft/node_modules/underscore/modules/_tagTester.js"], "sourcesContent": ["import { toString } from './_setup.js';\n\n// Internal function for creating a `toString`-based type tester.\nexport default function tagTester(name) {\n  var tag = '[object ' + name + ']';\n  return function(obj) {\n    return toString.call(obj) === tag;\n  };\n}\n"], "mappings": "AAAA,SAASA,QAAQ,QAAQ,aAAa;;AAEtC;AACA,eAAe,SAASC,SAASA,CAACC,IAAI,EAAE;EACtC,IAAIC,GAAG,GAAG,UAAU,GAAGD,IAAI,GAAG,GAAG;EACjC,OAAO,UAASE,GAAG,EAAE;IACnB,OAAOJ,QAAQ,CAACK,IAAI,CAACD,GAAG,CAAC,KAAKD,GAAG;EACnC,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}