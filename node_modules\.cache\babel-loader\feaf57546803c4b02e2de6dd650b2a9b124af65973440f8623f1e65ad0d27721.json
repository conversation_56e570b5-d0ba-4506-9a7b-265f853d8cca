{"ast": null, "code": "/**\n * @license\n * Copyright 2021 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { backend_util, Einsum, util } from '@tensorflow/tfjs-core';\nimport { multiply } from './Multiply';\nimport { reshape } from './Reshape';\nimport { sum } from './Sum';\nimport { transpose } from './Transpose';\nexport function einsum(args) {\n  const {\n    inputs,\n    backend,\n    attrs\n  } = args;\n  const {\n    equation\n  } = attrs;\n  const tensors = inputs;\n  const {\n    allDims,\n    summedDims,\n    idDims\n  } = backend_util.decodeEinsumEquation(equation, tensors.length);\n  backend_util.checkEinsumDimSizes(allDims.length, idDims, tensors);\n  const {\n    path,\n    steps\n  } = backend_util.getEinsumComputePath(summedDims, idDims);\n  const nSteps = steps.length;\n  let out = null;\n  let numDimsRemaining = allDims.length;\n  const tensorsToDispose = [];\n  for (let i = 0; i < nSteps; ++i) {\n    for (const idTerm of steps[i]) {\n      const {\n        permutationIndices: perm,\n        expandDims: dimsToExpand\n      } = backend_util.getEinsumPermutation(numDimsRemaining, idDims[idTerm]);\n      let x;\n      if (backend_util.isIdentityPermutation(perm)) {\n        x = tensors[idTerm];\n      } else {\n        x = transpose({\n          inputs: {\n            x: tensors[idTerm]\n          },\n          backend,\n          attrs: {\n            perm\n          }\n        });\n        tensorsToDispose.push(x);\n      }\n      const targetShape = x.shape.slice();\n      for (let k = 0; k < dimsToExpand.length; ++k) {\n        targetShape.splice(dimsToExpand[k], 0, 1);\n      }\n      if (!util.arraysEqual(x.shape, targetShape)) {\n        x = reshape({\n          inputs: {\n            x\n          },\n          backend,\n          attrs: {\n            shape: targetShape\n          }\n        });\n        tensorsToDispose.push(x);\n      }\n      if (out === null) {\n        out = x;\n      } else {\n        // tslint:disable-next-line: no-unnecessary-type-assertion\n        out = multiply({\n          inputs: {\n            a: x,\n            b: out\n          },\n          backend\n        });\n        tensorsToDispose.push(out);\n      }\n    }\n    if (i < nSteps - 1) {\n      if (path[i] >= 0) {\n        out = sum({\n          inputs: {\n            x: out\n          },\n          backend,\n          attrs: {\n            axis: path[i] - (allDims.length - numDimsRemaining),\n            keepDims: false\n          }\n        });\n        tensorsToDispose.push(out);\n      }\n      numDimsRemaining--;\n    }\n  }\n  // Clean up intermediate tensors.\n  for (const tensorInfo of tensorsToDispose) {\n    if (tensorInfo === out) {\n      continue;\n    }\n    backend.disposeIntermediateTensorInfo(tensorInfo);\n  }\n  return out;\n}\nexport const einsumConfig = {\n  kernelName: Einsum,\n  backendName: 'webgl',\n  kernelFunc: einsum\n};", "map": {"version": 3, "names": ["backend_util", "Einsum", "util", "multiply", "reshape", "sum", "transpose", "einsum", "args", "inputs", "backend", "attrs", "equation", "tensors", "allDims", "summedDims", "idDims", "decodeEinsumEquation", "length", "checkEinsumDimSizes", "path", "steps", "getEinsumComputePath", "nSteps", "out", "numDimsRemaining", "tensorsToDispose", "i", "idTerm", "permutationIndices", "perm", "expandDims", "dimsToExpand", "getEinsumPermutation", "x", "isIdentityPermutation", "push", "targetShape", "shape", "slice", "k", "splice", "arraysEqual", "a", "b", "axis", "keepDims", "tensorInfo", "disposeIntermediateTensorInfo", "einsumConfig", "kernelName", "backendName", "kernelFunc"], "sources": ["C:\\tfjs-backend-webgl\\src\\kernels\\Einsum.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2021 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {backend_util, Einsum, EinsumAttrs, EinsumInputs, KernelConfig, KernelFunc, Tensor, TensorInfo, util} from '@tensorflow/tfjs-core';\n\nimport {MathBackendWebGL} from '../backend_webgl';\n\nimport {multiply} from './Multiply';\nimport {reshape} from './Reshape';\nimport {sum} from './Sum';\nimport {transpose} from './Transpose';\n\nexport function einsum(\n    args:\n        {inputs: EinsumInputs, backend: MathBackendWebGL, attrs: EinsumAttrs}):\n    TensorInfo {\n  const {inputs, backend, attrs} = args;\n  const {equation} = attrs;\n  const tensors = inputs as Tensor[];\n\n  const {allDims, summedDims, idDims} =\n      backend_util.decodeEinsumEquation(equation, tensors.length);\n  backend_util.checkEinsumDimSizes(allDims.length, idDims, tensors);\n  const {path, steps} = backend_util.getEinsumComputePath(summedDims, idDims);\n\n  const nSteps = steps.length;\n  let out: TensorInfo|null = null;\n  let numDimsRemaining = allDims.length;\n  const tensorsToDispose: TensorInfo[] = [];\n  for (let i = 0; i < nSteps; ++i) {\n    for (const idTerm of steps[i]) {\n      const {permutationIndices: perm, expandDims: dimsToExpand} =\n          backend_util.getEinsumPermutation(numDimsRemaining, idDims[idTerm]);\n      let x: TensorInfo;\n      if (backend_util.isIdentityPermutation(perm)) {\n        x = tensors[idTerm];\n      } else {\n        x = transpose({inputs: {x: tensors[idTerm]}, backend, attrs: {perm}});\n        tensorsToDispose.push(x);\n      }\n      const targetShape: number[] = x.shape.slice();\n      for (let k = 0; k < dimsToExpand.length; ++k) {\n        targetShape.splice(dimsToExpand[k], 0, 1);\n      }\n\n      if (!util.arraysEqual(x.shape, targetShape)) {\n        x = reshape({inputs: {x}, backend, attrs: {shape: targetShape}});\n        tensorsToDispose.push(x);\n      }\n      if (out === null) {\n        out = x;\n      } else {\n        // tslint:disable-next-line: no-unnecessary-type-assertion\n        out = multiply({inputs: {a: x, b: out}, backend}) as TensorInfo;\n        tensorsToDispose.push(out);\n      }\n    }\n    if (i < nSteps - 1) {\n      if (path[i] >= 0) {\n        out = sum({\n          inputs: {x: out},\n          backend,\n          attrs: {\n            axis: path[i] - (allDims.length - numDimsRemaining),\n            keepDims: false\n          }\n        });\n        tensorsToDispose.push(out);\n      }\n      numDimsRemaining--;\n    }\n  }\n\n  // Clean up intermediate tensors.\n  for (const tensorInfo of tensorsToDispose) {\n    if (tensorInfo === out) {\n      continue;\n    }\n    backend.disposeIntermediateTensorInfo(tensorInfo);\n  }\n\n  return out;\n}\n\nexport const einsumConfig: KernelConfig = {\n  kernelName: Einsum,\n  backendName: 'webgl',\n  kernelFunc: einsum as unknown as KernelFunc\n};\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAiBA,SAAQA,YAAY,EAAEC,MAAM,EAA2EC,IAAI,QAAO,uBAAuB;AAIzI,SAAQC,QAAQ,QAAO,YAAY;AACnC,SAAQC,OAAO,QAAO,WAAW;AACjC,SAAQC,GAAG,QAAO,OAAO;AACzB,SAAQC,SAAS,QAAO,aAAa;AAErC,OAAM,SAAUC,MAAMA,CAClBC,IACyE;EAE3E,MAAM;IAACC,MAAM;IAAEC,OAAO;IAAEC;EAAK,CAAC,GAAGH,IAAI;EACrC,MAAM;IAACI;EAAQ,CAAC,GAAGD,KAAK;EACxB,MAAME,OAAO,GAAGJ,MAAkB;EAElC,MAAM;IAACK,OAAO;IAAEC,UAAU;IAAEC;EAAM,CAAC,GAC/BhB,YAAY,CAACiB,oBAAoB,CAACL,QAAQ,EAAEC,OAAO,CAACK,MAAM,CAAC;EAC/DlB,YAAY,CAACmB,mBAAmB,CAACL,OAAO,CAACI,MAAM,EAAEF,MAAM,EAAEH,OAAO,CAAC;EACjE,MAAM;IAACO,IAAI;IAAEC;EAAK,CAAC,GAAGrB,YAAY,CAACsB,oBAAoB,CAACP,UAAU,EAAEC,MAAM,CAAC;EAE3E,MAAMO,MAAM,GAAGF,KAAK,CAACH,MAAM;EAC3B,IAAIM,GAAG,GAAoB,IAAI;EAC/B,IAAIC,gBAAgB,GAAGX,OAAO,CAACI,MAAM;EACrC,MAAMQ,gBAAgB,GAAiB,EAAE;EACzC,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,MAAM,EAAE,EAAEI,CAAC,EAAE;IAC/B,KAAK,MAAMC,MAAM,IAAIP,KAAK,CAACM,CAAC,CAAC,EAAE;MAC7B,MAAM;QAACE,kBAAkB,EAAEC,IAAI;QAAEC,UAAU,EAAEC;MAAY,CAAC,GACtDhC,YAAY,CAACiC,oBAAoB,CAACR,gBAAgB,EAAET,MAAM,CAACY,MAAM,CAAC,CAAC;MACvE,IAAIM,CAAa;MACjB,IAAIlC,YAAY,CAACmC,qBAAqB,CAACL,IAAI,CAAC,EAAE;QAC5CI,CAAC,GAAGrB,OAAO,CAACe,MAAM,CAAC;OACpB,MAAM;QACLM,CAAC,GAAG5B,SAAS,CAAC;UAACG,MAAM,EAAE;YAACyB,CAAC,EAAErB,OAAO,CAACe,MAAM;UAAC,CAAC;UAAElB,OAAO;UAAEC,KAAK,EAAE;YAACmB;UAAI;QAAC,CAAC,CAAC;QACrEJ,gBAAgB,CAACU,IAAI,CAACF,CAAC,CAAC;;MAE1B,MAAMG,WAAW,GAAaH,CAAC,CAACI,KAAK,CAACC,KAAK,EAAE;MAC7C,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGR,YAAY,CAACd,MAAM,EAAE,EAAEsB,CAAC,EAAE;QAC5CH,WAAW,CAACI,MAAM,CAACT,YAAY,CAACQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;;MAG3C,IAAI,CAACtC,IAAI,CAACwC,WAAW,CAACR,CAAC,CAACI,KAAK,EAAED,WAAW,CAAC,EAAE;QAC3CH,CAAC,GAAG9B,OAAO,CAAC;UAACK,MAAM,EAAE;YAACyB;UAAC,CAAC;UAAExB,OAAO;UAAEC,KAAK,EAAE;YAAC2B,KAAK,EAAED;UAAW;QAAC,CAAC,CAAC;QAChEX,gBAAgB,CAACU,IAAI,CAACF,CAAC,CAAC;;MAE1B,IAAIV,GAAG,KAAK,IAAI,EAAE;QAChBA,GAAG,GAAGU,CAAC;OACR,MAAM;QACL;QACAV,GAAG,GAAGrB,QAAQ,CAAC;UAACM,MAAM,EAAE;YAACkC,CAAC,EAAET,CAAC;YAAEU,CAAC,EAAEpB;UAAG,CAAC;UAAEd;QAAO,CAAC,CAAe;QAC/DgB,gBAAgB,CAACU,IAAI,CAACZ,GAAG,CAAC;;;IAG9B,IAAIG,CAAC,GAAGJ,MAAM,GAAG,CAAC,EAAE;MAClB,IAAIH,IAAI,CAACO,CAAC,CAAC,IAAI,CAAC,EAAE;QAChBH,GAAG,GAAGnB,GAAG,CAAC;UACRI,MAAM,EAAE;YAACyB,CAAC,EAAEV;UAAG,CAAC;UAChBd,OAAO;UACPC,KAAK,EAAE;YACLkC,IAAI,EAAEzB,IAAI,CAACO,CAAC,CAAC,IAAIb,OAAO,CAACI,MAAM,GAAGO,gBAAgB,CAAC;YACnDqB,QAAQ,EAAE;;SAEb,CAAC;QACFpB,gBAAgB,CAACU,IAAI,CAACZ,GAAG,CAAC;;MAE5BC,gBAAgB,EAAE;;;EAItB;EACA,KAAK,MAAMsB,UAAU,IAAIrB,gBAAgB,EAAE;IACzC,IAAIqB,UAAU,KAAKvB,GAAG,EAAE;MACtB;;IAEFd,OAAO,CAACsC,6BAA6B,CAACD,UAAU,CAAC;;EAGnD,OAAOvB,GAAG;AACZ;AAEA,OAAO,MAAMyB,YAAY,GAAiB;EACxCC,UAAU,EAAEjD,MAAM;EAClBkD,WAAW,EAAE,OAAO;EACpBC,UAAU,EAAE7C;CACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}