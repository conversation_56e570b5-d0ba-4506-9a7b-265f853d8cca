{"ast": null, "code": "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { logicalNot } from '../../ops/logical_not';\nimport { getGlobalTensorClass } from '../../tensor';\ngetGlobalTensorClass().prototype.logicalNot = function () {\n  this.throwIfDisposed();\n  return logicalNot(this);\n};", "map": {"version": 3, "names": ["logicalNot", "getGlobalTensorClass", "prototype", "throwIfDisposed"], "sources": ["C:\\tfjs-core\\src\\public\\chained_ops\\logical_not.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport {logicalNot} from '../../ops/logical_not';\nimport {getGlobalTensorClass, Tensor} from '../../tensor';\nimport {Rank} from '../../types';\n\ndeclare module '../../tensor' {\n  interface Tensor<R extends Rank = Rank> {\n    logicalNot<T extends Tensor>(): T;\n  }\n}\n\ngetGlobalTensorClass().prototype.logicalNot = function<T extends Tensor>(): T {\n  this.throwIfDisposed();\n  return logicalNot(this) as T;\n};\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAgBA,SAAQA,UAAU,QAAO,uBAAuB;AAChD,SAAQC,oBAAoB,QAAe,cAAc;AASzDA,oBAAoB,EAAE,CAACC,SAAS,CAACF,UAAU,GAAG;EAC5C,IAAI,CAACG,eAAe,EAAE;EACtB,OAAOH,UAAU,CAAC,IAAI,CAAM;AAC9B,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}