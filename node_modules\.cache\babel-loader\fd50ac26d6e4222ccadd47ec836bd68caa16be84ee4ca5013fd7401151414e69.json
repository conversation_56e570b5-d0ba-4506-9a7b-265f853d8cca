{"ast": null, "code": "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { backend_util, Conv2DBackpropInput, env } from '@tensorflow/tfjs-core';\nimport { Conv2DDerInputProgram } from '../conv_backprop_gpu';\nimport { Conv2DDerInputPackedProgram } from '../conv_backprop_packed_gpu';\nexport function conv2DBackpropInput(args) {\n  const {\n    inputs,\n    backend,\n    attrs\n  } = args;\n  const {\n    dy,\n    filter\n  } = inputs;\n  const {\n    inputShape,\n    strides,\n    pad,\n    dataFormat,\n    dimRoundingMode\n  } = attrs;\n  const $dataFormat = backend_util.convertConv2DDataFormat(dataFormat);\n  const convInfo = backend_util.computeConv2DInfo(inputShape, filter.shape, strides, 1 /* dilations */, pad, dimRoundingMode, false, $dataFormat);\n  if (env().getBool('WEBGL_PACK_CONV2DTRANSPOSE') && $dataFormat === 'channelsLast') {\n    const customValues = [[convInfo.strideHeight, convInfo.strideWidth]];\n    const program = new Conv2DDerInputPackedProgram(convInfo);\n    return backend.runWebGLProgram(program, [dy, filter], 'float32', customValues);\n  } else {\n    const program = new Conv2DDerInputProgram(convInfo);\n    return backend.runWebGLProgram(program, [dy, filter], 'float32');\n  }\n}\nexport const conv2DBackpropInputConfig = {\n  kernelName: Conv2DBackpropInput,\n  backendName: 'webgl',\n  kernelFunc: conv2DBackpropInput\n};", "map": {"version": 3, "names": ["backend_util", "Conv2DBackpropInput", "env", "Conv2DDerInputProgram", "Conv2DDerInputPackedProgram", "conv2DBackpropInput", "args", "inputs", "backend", "attrs", "dy", "filter", "inputShape", "strides", "pad", "dataFormat", "dimRoundingMode", "$dataFormat", "convertConv2DDataFormat", "convInfo", "computeConv2DInfo", "shape", "getBool", "customValues", "strideHeight", "<PERSON><PERSON><PERSON><PERSON>", "program", "runWebGLProgram", "conv2DBackpropInputConfig", "kernelName", "backendName", "kernelFunc"], "sources": ["C:\\tfjs-backend-webgl\\src\\kernels\\Conv2DBackpropInput.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {backend_util, Conv2DBackpropInput, Conv2DBackpropInputAttrs, Conv2DBackpropInputInputs, env, KernelConfig, KernelFunc} from '@tensorflow/tfjs-core';\n\nimport {MathBackendWebGL} from '../backend_webgl';\nimport {Conv2DDerInputProgram} from '../conv_backprop_gpu';\nimport {Conv2DDerInputPackedProgram} from '../conv_backprop_packed_gpu';\n\nexport function conv2DBackpropInput(args: {\n  inputs: Conv2DBackpropInputInputs,\n  attrs: Conv2DBackpropInputAttrs,\n  backend: MathBackendWebGL\n}) {\n  const {inputs, backend, attrs} = args;\n  const {dy, filter} = inputs;\n  const {inputShape, strides, pad, dataFormat, dimRoundingMode} = attrs;\n\n  const $dataFormat = backend_util.convertConv2DDataFormat(dataFormat);\n  const convInfo = backend_util.computeConv2DInfo(\n      inputShape, filter.shape as [number, number, number, number], strides,\n      1 /* dilations */, pad, dimRoundingMode, false, $dataFormat);\n\n  if (env().getBool('WEBGL_PACK_CONV2DTRANSPOSE') &&\n      $dataFormat === 'channelsLast') {\n    const customValues = [\n      [convInfo.strideHeight, convInfo.strideWidth],\n    ];\n    const program = new Conv2DDerInputPackedProgram(convInfo);\n    return backend.runWebGLProgram(\n        program, [dy, filter], 'float32', customValues);\n  } else {\n    const program = new Conv2DDerInputProgram(convInfo);\n    return backend.runWebGLProgram(program, [dy, filter], 'float32');\n  }\n}\n\nexport const conv2DBackpropInputConfig: KernelConfig = {\n  kernelName: Conv2DBackpropInput,\n  backendName: 'webgl',\n  kernelFunc: conv2DBackpropInput as unknown as KernelFunc,\n};\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAiBA,SAAQA,YAAY,EAAEC,mBAAmB,EAAuDC,GAAG,QAAiC,uBAAuB;AAG3J,SAAQC,qBAAqB,QAAO,sBAAsB;AAC1D,SAAQC,2BAA2B,QAAO,6BAA6B;AAEvE,OAAM,SAAUC,mBAAmBA,CAACC,IAInC;EACC,MAAM;IAACC,MAAM;IAAEC,OAAO;IAAEC;EAAK,CAAC,GAAGH,IAAI;EACrC,MAAM;IAACI,EAAE;IAAEC;EAAM,CAAC,GAAGJ,MAAM;EAC3B,MAAM;IAACK,UAAU;IAAEC,OAAO;IAAEC,GAAG;IAAEC,UAAU;IAAEC;EAAe,CAAC,GAAGP,KAAK;EAErE,MAAMQ,WAAW,GAAGjB,YAAY,CAACkB,uBAAuB,CAACH,UAAU,CAAC;EACpE,MAAMI,QAAQ,GAAGnB,YAAY,CAACoB,iBAAiB,CAC3CR,UAAU,EAAED,MAAM,CAACU,KAAyC,EAAER,OAAO,EACrE,CAAC,CAAC,iBAAiBC,GAAG,EAAEE,eAAe,EAAE,KAAK,EAAEC,WAAW,CAAC;EAEhE,IAAIf,GAAG,EAAE,CAACoB,OAAO,CAAC,4BAA4B,CAAC,IAC3CL,WAAW,KAAK,cAAc,EAAE;IAClC,MAAMM,YAAY,GAAG,CACnB,CAACJ,QAAQ,CAACK,YAAY,EAAEL,QAAQ,CAACM,WAAW,CAAC,CAC9C;IACD,MAAMC,OAAO,GAAG,IAAItB,2BAA2B,CAACe,QAAQ,CAAC;IACzD,OAAOX,OAAO,CAACmB,eAAe,CAC1BD,OAAO,EAAE,CAAChB,EAAE,EAAEC,MAAM,CAAC,EAAE,SAAS,EAAEY,YAAY,CAAC;GACpD,MAAM;IACL,MAAMG,OAAO,GAAG,IAAIvB,qBAAqB,CAACgB,QAAQ,CAAC;IACnD,OAAOX,OAAO,CAACmB,eAAe,CAACD,OAAO,EAAE,CAAChB,EAAE,EAAEC,MAAM,CAAC,EAAE,SAAS,CAAC;;AAEpE;AAEA,OAAO,MAAMiB,yBAAyB,GAAiB;EACrDC,UAAU,EAAE5B,mBAAmB;EAC/B6B,WAAW,EAAE,OAAO;EACpBC,UAAU,EAAE1B;CACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}