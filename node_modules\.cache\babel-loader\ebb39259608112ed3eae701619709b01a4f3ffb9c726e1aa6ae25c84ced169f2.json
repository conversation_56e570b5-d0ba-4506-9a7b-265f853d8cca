{"ast": null, "code": "import isObject from './isObject.js';\nimport { hasEnumBug } from './_setup.js';\nimport collectNonEnumProps from './_collectNonEnumProps.js';\n\n// Retrieve all the enumerable property names of an object.\nexport default function allKeys(obj) {\n  if (!isObject(obj)) return [];\n  var keys = [];\n  for (var key in obj) keys.push(key);\n  // Ahem, IE < 9.\n  if (hasEnumBug) collectNonEnumProps(obj, keys);\n  return keys;\n}", "map": {"version": 3, "names": ["isObject", "hasEnumBug", "collectNonEnumProps", "allKeys", "obj", "keys", "key", "push"], "sources": ["C:/tmsft/node_modules/underscore/modules/allKeys.js"], "sourcesContent": ["import isObject from './isObject.js';\nimport { hasEnumBug } from './_setup.js';\nimport collectNonEnumProps from './_collectNonEnumProps.js';\n\n// Retrieve all the enumerable property names of an object.\nexport default function allKeys(obj) {\n  if (!isObject(obj)) return [];\n  var keys = [];\n  for (var key in obj) keys.push(key);\n  // Ahem, IE < 9.\n  if (hasEnumBug) collectNonEnumProps(obj, keys);\n  return keys;\n}\n"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,eAAe;AACpC,SAASC,UAAU,QAAQ,aAAa;AACxC,OAAOC,mBAAmB,MAAM,2BAA2B;;AAE3D;AACA,eAAe,SAASC,OAAOA,CAACC,GAAG,EAAE;EACnC,IAAI,CAACJ,QAAQ,CAACI,GAAG,CAAC,EAAE,OAAO,EAAE;EAC7B,IAAIC,IAAI,GAAG,EAAE;EACb,KAAK,IAAIC,GAAG,IAAIF,GAAG,EAAEC,IAAI,CAACE,IAAI,CAACD,GAAG,CAAC;EACnC;EACA,IAAIL,UAAU,EAAEC,mBAAmB,CAACE,GAAG,EAAEC,IAAI,CAAC;EAC9C,OAAOA,IAAI;AACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}