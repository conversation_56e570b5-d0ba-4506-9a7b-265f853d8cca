{"ast": null, "code": "/**\n * @license\n * Copyright 2019 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { convertToTensor } from '../tensor_util_env';\nimport { assert, assertShapesMatch, getTypedArrayFromDType } from '../util';\nimport { tensor } from './tensor';\n/**\n * Returns whether the targets are in the top K predictions.\n *\n * ```js\n * const predictions = tf.tensor2d([[20, 10, 40, 30], [30, 50, -20, 10]]);\n * const targets = tf.tensor1d([2, 0]);\n * const precision = await tf.inTopKAsync(predictions, targets);\n * precision.print();\n * ```\n * @param predictions 2-D or higher `tf.Tensor` with last dimension being\n *     at least `k`.\n * @param targets 1-D or higher `tf.Tensor`.\n * @param k Optional Number of top elements to look at for computing precision,\n *     default to 1.\n *\n * @doc {heading: 'Operations', subheading: 'Evaluation'}\n */\nasync function inTopKAsync_(predictions, targets) {\n  let k = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 1;\n  const $predictions = convertToTensor(predictions, 'predictions', 'inTopK');\n  const $targets = convertToTensor(targets, 'targets', 'inTopK');\n  assert($predictions.rank > 1, () => 'inTopK() expects the predictions to be of rank 2 or higher, ' + \"but got \".concat($predictions.rank));\n  assert($predictions.rank - 1 === $targets.rank, () => \"predictions rank should be 1 larger than \" + \"targets rank, but got predictions rank \" + \"\".concat($predictions.rank, \" and targets rank \").concat($targets.rank));\n  assertShapesMatch($predictions.shape.slice(0, $predictions.shape.length - 1), $targets.shape, \"predictions's shape should be align with the targets' shape, \" + 'except the last dimension.');\n  const lastDim = $predictions.shape[$predictions.shape.length - 1];\n  assert(k > 0 && k <= lastDim, () => \"'k' passed to inTopK() must be > 0 && <= the predictions last \" + \"dimension (\".concat(lastDim, \"), but got \").concat(k));\n  const predictionsVals = await $predictions.data();\n  const targetsVals = await $targets.data();\n  // Reshape predictionsVals into a 2d tensor [batch, lastDim]\n  // and look up topK along lastDim.\n  const [batch, size] = [predictionsVals.length / lastDim, lastDim];\n  const precision = getTypedArrayFromDType('bool', batch);\n  for (let b = 0; b < batch; b++) {\n    const offset = b * size;\n    const vals = predictionsVals.subarray(offset, offset + size);\n    const valAndInd = [];\n    for (let i = 0; i < vals.length; i++) {\n      valAndInd.push({\n        value: vals[i],\n        index: i\n      });\n    }\n    valAndInd.sort((a, b) => b.value - a.value);\n    precision[b] = 0;\n    for (let i = 0; i < k; i++) {\n      if (valAndInd[i].index === targetsVals[b]) {\n        precision[b] = 1;\n        break;\n      }\n    }\n  }\n  if (predictions !== $predictions) {\n    $predictions.dispose();\n  }\n  if (targets !== $targets) {\n    $targets.dispose();\n  }\n  // Output precision has the same shape as targets.\n  return tensor(precision, $targets.shape, 'bool');\n}\nexport const inTopKAsync = inTopKAsync_;", "map": {"version": 3, "names": ["convertToTensor", "assert", "assertShapesMatch", "getTypedArrayFromDType", "tensor", "inTopKAsync_", "predictions", "targets", "k", "arguments", "length", "undefined", "$predictions", "$targets", "rank", "concat", "shape", "slice", "lastDim", "predictionsVals", "data", "targetsVals", "batch", "size", "precision", "b", "offset", "vals", "subarray", "valAndInd", "i", "push", "value", "index", "sort", "a", "dispose", "inTopKAsync"], "sources": ["C:\\tfjs-core\\src\\ops\\in_top_k.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2019 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {Tensor} from '../tensor';\nimport {convertToTensor} from '../tensor_util_env';\nimport {TensorLike} from '../types';\nimport {assert, assertShapesMatch, getTypedArrayFromDType} from '../util';\nimport {tensor} from './tensor';\n\n/**\n * Returns whether the targets are in the top K predictions.\n *\n * ```js\n * const predictions = tf.tensor2d([[20, 10, 40, 30], [30, 50, -20, 10]]);\n * const targets = tf.tensor1d([2, 0]);\n * const precision = await tf.inTopKAsync(predictions, targets);\n * precision.print();\n * ```\n * @param predictions 2-D or higher `tf.Tensor` with last dimension being\n *     at least `k`.\n * @param targets 1-D or higher `tf.Tensor`.\n * @param k Optional Number of top elements to look at for computing precision,\n *     default to 1.\n *\n * @doc {heading: 'Operations', subheading: 'Evaluation'}\n */\nasync function inTopKAsync_<T extends Tensor, U extends Tensor>(\n    predictions: T|TensorLike, targets: U|TensorLike, k = 1): Promise<U> {\n  const $predictions = convertToTensor(predictions, 'predictions', 'inTopK');\n  const $targets = convertToTensor(targets, 'targets', 'inTopK');\n\n  assert(\n      $predictions.rank > 1,\n      () => 'inTopK() expects the predictions to be of rank 2 or higher, ' +\n          `but got ${$predictions.rank}`);\n  assert(\n      $predictions.rank - 1 === $targets.rank,\n      () => `predictions rank should be 1 larger than ` +\n          `targets rank, but got predictions rank ` +\n          `${$predictions.rank} and targets rank ${$targets.rank}`);\n  assertShapesMatch(\n      $predictions.shape.slice(0, $predictions.shape.length - 1),\n      $targets.shape,\n      `predictions's shape should be align with the targets' shape, ` +\n          'except the last dimension.');\n  const lastDim = $predictions.shape[$predictions.shape.length - 1];\n  assert(\n      k > 0 && k <= lastDim,\n      () => `'k' passed to inTopK() must be > 0 && <= the predictions last ` +\n          `dimension (${lastDim}), but got ${k}`);\n\n  const predictionsVals = await $predictions.data();\n  const targetsVals = await $targets.data();\n\n  // Reshape predictionsVals into a 2d tensor [batch, lastDim]\n  // and look up topK along lastDim.\n  const [batch, size] = [predictionsVals.length / lastDim, lastDim];\n  const precision = getTypedArrayFromDType('bool', batch);\n\n  for (let b = 0; b < batch; b++) {\n    const offset = b * size;\n    const vals = predictionsVals.subarray(offset, offset + size);\n    const valAndInd: Array<{value: number, index: number}> = [];\n    for (let i = 0; i < vals.length; i++) {\n      valAndInd.push({value: vals[i], index: i});\n    }\n    valAndInd.sort((a, b) => b.value - a.value);\n\n    precision[b] = 0;\n    for (let i = 0; i < k; i++) {\n      if (valAndInd[i].index === targetsVals[b]) {\n        precision[b] = 1;\n        break;\n      }\n    }\n  }\n\n  if (predictions !== $predictions) {\n    $predictions.dispose();\n  }\n  if (targets !== $targets) {\n    $targets.dispose();\n  }\n\n  // Output precision has the same shape as targets.\n  return tensor(precision, $targets.shape, 'bool') as U;\n}\n\nexport const inTopKAsync = inTopKAsync_;\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAkBA,SAAQA,eAAe,QAAO,oBAAoB;AAElD,SAAQC,MAAM,EAAEC,iBAAiB,EAAEC,sBAAsB,QAAO,SAAS;AACzE,SAAQC,MAAM,QAAO,UAAU;AAE/B;;;;;;;;;;;;;;;;;AAiBA,eAAeC,YAAYA,CACvBC,WAAyB,EAAEC,OAAqB,EAAO;EAAA,IAALC,CAAC,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC;EACzD,MAAMG,YAAY,GAAGZ,eAAe,CAACM,WAAW,EAAE,aAAa,EAAE,QAAQ,CAAC;EAC1E,MAAMO,QAAQ,GAAGb,eAAe,CAACO,OAAO,EAAE,SAAS,EAAE,QAAQ,CAAC;EAE9DN,MAAM,CACFW,YAAY,CAACE,IAAI,GAAG,CAAC,EACrB,MAAM,8DAA8D,cAAAC,MAAA,CACrDH,YAAY,CAACE,IAAI,CAAE,CAAC;EACvCb,MAAM,CACFW,YAAY,CAACE,IAAI,GAAG,CAAC,KAAKD,QAAQ,CAACC,IAAI,EACvC,MAAM,uFACuC,MAAAC,MAAA,CACtCH,YAAY,CAACE,IAAI,wBAAAC,MAAA,CAAqBF,QAAQ,CAACC,IAAI,CAAE,CAAC;EACjEZ,iBAAiB,CACbU,YAAY,CAACI,KAAK,CAACC,KAAK,CAAC,CAAC,EAAEL,YAAY,CAACI,KAAK,CAACN,MAAM,GAAG,CAAC,CAAC,EAC1DG,QAAQ,CAACG,KAAK,EACd,kEACI,4BAA4B,CAAC;EACrC,MAAME,OAAO,GAAGN,YAAY,CAACI,KAAK,CAACJ,YAAY,CAACI,KAAK,CAACN,MAAM,GAAG,CAAC,CAAC;EACjET,MAAM,CACFO,CAAC,GAAG,CAAC,IAAIA,CAAC,IAAIU,OAAO,EACrB,MAAM,iFAAAH,MAAA,CACYG,OAAO,iBAAAH,MAAA,CAAcP,CAAC,CAAE,CAAC;EAE/C,MAAMW,eAAe,GAAG,MAAMP,YAAY,CAACQ,IAAI,EAAE;EACjD,MAAMC,WAAW,GAAG,MAAMR,QAAQ,CAACO,IAAI,EAAE;EAEzC;EACA;EACA,MAAM,CAACE,KAAK,EAAEC,IAAI,CAAC,GAAG,CAACJ,eAAe,CAACT,MAAM,GAAGQ,OAAO,EAAEA,OAAO,CAAC;EACjE,MAAMM,SAAS,GAAGrB,sBAAsB,CAAC,MAAM,EAAEmB,KAAK,CAAC;EAEvD,KAAK,IAAIG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,KAAK,EAAEG,CAAC,EAAE,EAAE;IAC9B,MAAMC,MAAM,GAAGD,CAAC,GAAGF,IAAI;IACvB,MAAMI,IAAI,GAAGR,eAAe,CAACS,QAAQ,CAACF,MAAM,EAAEA,MAAM,GAAGH,IAAI,CAAC;IAC5D,MAAMM,SAAS,GAA0C,EAAE;IAC3D,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,IAAI,CAACjB,MAAM,EAAEoB,CAAC,EAAE,EAAE;MACpCD,SAAS,CAACE,IAAI,CAAC;QAACC,KAAK,EAAEL,IAAI,CAACG,CAAC,CAAC;QAAEG,KAAK,EAAEH;MAAC,CAAC,CAAC;;IAE5CD,SAAS,CAACK,IAAI,CAAC,CAACC,CAAC,EAAEV,CAAC,KAAKA,CAAC,CAACO,KAAK,GAAGG,CAAC,CAACH,KAAK,CAAC;IAE3CR,SAAS,CAACC,CAAC,CAAC,GAAG,CAAC;IAChB,KAAK,IAAIK,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGtB,CAAC,EAAEsB,CAAC,EAAE,EAAE;MAC1B,IAAID,SAAS,CAACC,CAAC,CAAC,CAACG,KAAK,KAAKZ,WAAW,CAACI,CAAC,CAAC,EAAE;QACzCD,SAAS,CAACC,CAAC,CAAC,GAAG,CAAC;QAChB;;;;EAKN,IAAInB,WAAW,KAAKM,YAAY,EAAE;IAChCA,YAAY,CAACwB,OAAO,EAAE;;EAExB,IAAI7B,OAAO,KAAKM,QAAQ,EAAE;IACxBA,QAAQ,CAACuB,OAAO,EAAE;;EAGpB;EACA,OAAOhC,MAAM,CAACoB,SAAS,EAAEX,QAAQ,CAACG,KAAK,EAAE,MAAM,CAAM;AACvD;AAEA,OAAO,MAAMqB,WAAW,GAAGhC,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}