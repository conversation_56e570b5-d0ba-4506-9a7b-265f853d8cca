{"ast": null, "code": "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { Bincount } from '@tensorflow/tfjs-core';\nimport { bincountImpl } from './Bincount_impl';\nexport function bincount(args) {\n  const {\n    inputs,\n    backend,\n    attrs\n  } = args;\n  const {\n    x,\n    weights\n  } = inputs;\n  const {\n    size\n  } = attrs;\n  const xVals = backend.data.get(x.dataId).values;\n  const weightsVals = backend.data.get(weights.dataId).values;\n  const outVals = bincountImpl(xVals, weightsVals, weights.dtype, weights.shape, size);\n  return backend.makeTensorInfo([size], weights.dtype, outVals);\n}\nexport const bincountConfig = {\n  kernelName: Bincount,\n  backendName: 'cpu',\n  kernelFunc: bincount\n};", "map": {"version": 3, "names": ["Bincount", "bincountImpl", "bincount", "args", "inputs", "backend", "attrs", "x", "weights", "size", "xVals", "data", "get", "dataId", "values", "weightsVals", "outVals", "dtype", "shape", "makeTensorInfo", "bincountConfig", "kernelName", "backendName", "kernelFunc"], "sources": ["C:\\tfjs-backend-cpu\\src\\kernels\\Bincount.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {Bincount, BincountAttrs, BincountInputs, KernelConfig, KernelFunc, TensorInfo, TypedArray} from '@tensorflow/tfjs-core';\n\nimport {MathBackendCPU} from '../backend_cpu';\nimport {bincountImpl} from './Bincount_impl';\n\nexport function bincount(args: {\n  inputs: BincountInputs,\n  backend: MathBackendCPU,\n  attrs: BincountAttrs\n}): TensorInfo {\n  const {inputs, backend, attrs} = args;\n  const {x, weights} = inputs;\n  const {size} = attrs;\n\n  const xVals = backend.data.get(x.dataId).values as TypedArray;\n  const weightsVals = backend.data.get(weights.dataId).values as TypedArray;\n\n  const outVals =\n      bincountImpl(xVals, weightsVals, weights.dtype, weights.shape, size);\n\n  return backend.makeTensorInfo([size], weights.dtype, outVals);\n}\n\nexport const bincountConfig: KernelConfig = {\n  kernelName: Bincount,\n  backendName: 'cpu',\n  kernelFunc: bincount as unknown as KernelFunc\n};\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAiBA,SAAQA,QAAQ,QAAwF,uBAAuB;AAG/H,SAAQC,YAAY,QAAO,iBAAiB;AAE5C,OAAM,SAAUC,QAAQA,CAACC,IAIxB;EACC,MAAM;IAACC,MAAM;IAAEC,OAAO;IAAEC;EAAK,CAAC,GAAGH,IAAI;EACrC,MAAM;IAACI,CAAC;IAAEC;EAAO,CAAC,GAAGJ,MAAM;EAC3B,MAAM;IAACK;EAAI,CAAC,GAAGH,KAAK;EAEpB,MAAMI,KAAK,GAAGL,OAAO,CAACM,IAAI,CAACC,GAAG,CAACL,CAAC,CAACM,MAAM,CAAC,CAACC,MAAoB;EAC7D,MAAMC,WAAW,GAAGV,OAAO,CAACM,IAAI,CAACC,GAAG,CAACJ,OAAO,CAACK,MAAM,CAAC,CAACC,MAAoB;EAEzE,MAAME,OAAO,GACTf,YAAY,CAACS,KAAK,EAAEK,WAAW,EAAEP,OAAO,CAACS,KAAK,EAAET,OAAO,CAACU,KAAK,EAAET,IAAI,CAAC;EAExE,OAAOJ,OAAO,CAACc,cAAc,CAAC,CAACV,IAAI,CAAC,EAAED,OAAO,CAACS,KAAK,EAAED,OAAO,CAAC;AAC/D;AAEA,OAAO,MAAMI,cAAc,GAAiB;EAC1CC,UAAU,EAAErB,QAAQ;EACpBsB,WAAW,EAAE,KAAK;EAClBC,UAAU,EAAErB;CACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}