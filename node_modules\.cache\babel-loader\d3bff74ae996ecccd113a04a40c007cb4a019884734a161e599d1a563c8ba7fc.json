{"ast": null, "code": "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { Tile } from '@tensorflow/tfjs-core';\nimport { assertNotComplex } from '../cpu_util';\nimport { tileImpl } from './Tile_impl';\nexport function tile(args) {\n  const {\n    inputs,\n    backend,\n    attrs\n  } = args;\n  const {\n    x\n  } = inputs;\n  const {\n    reps\n  } = attrs;\n  assertNotComplex(x, 'tile');\n  const outBuf = tileImpl(backend.bufferSync(x), reps);\n  return backend.makeTensorInfo(outBuf.shape, outBuf.dtype, outBuf.values);\n}\nexport const tileConfig = {\n  kernelName: Tile,\n  backendName: 'cpu',\n  kernelFunc: tile\n};", "map": {"version": 3, "names": ["Tile", "assertNotComplex", "tileImpl", "tile", "args", "inputs", "backend", "attrs", "x", "reps", "outBuf", "bufferSync", "makeTensorInfo", "shape", "dtype", "values", "tileConfig", "kernelName", "backendName", "kernelFunc"], "sources": ["C:\\tfjs-backend-cpu\\src\\kernels\\Tile.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {KernelConfig, KernelFunc, TensorInfo, Tile, TileAttrs, TileInputs} from '@tensorflow/tfjs-core';\n\nimport {MathBackendCPU} from '../backend_cpu';\nimport {assertNotComplex} from '../cpu_util';\nimport {tileImpl} from './Tile_impl';\n\nexport function tile(\n    args: {inputs: TileInputs, backend: MathBackendCPU, attrs: TileAttrs}):\n    TensorInfo {\n  const {inputs, backend, attrs} = args;\n  const {x} = inputs;\n  const {reps} = attrs;\n\n  assertNotComplex(x, 'tile');\n  const outBuf = tileImpl(backend.bufferSync(x), reps);\n\n  return backend.makeTensorInfo(outBuf.shape, outBuf.dtype, outBuf.values);\n}\n\nexport const tileConfig: KernelConfig = {\n  kernelName: Tile,\n  backendName: 'cpu',\n  kernelFunc: tile as unknown as KernelFunc\n};\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAiBA,SAA8CA,IAAI,QAA8B,uBAAuB;AAGvG,SAAQC,gBAAgB,QAAO,aAAa;AAC5C,SAAQC,QAAQ,QAAO,aAAa;AAEpC,OAAM,SAAUC,IAAIA,CAChBC,IAAqE;EAEvE,MAAM;IAACC,MAAM;IAAEC,OAAO;IAAEC;EAAK,CAAC,GAAGH,IAAI;EACrC,MAAM;IAACI;EAAC,CAAC,GAAGH,MAAM;EAClB,MAAM;IAACI;EAAI,CAAC,GAAGF,KAAK;EAEpBN,gBAAgB,CAACO,CAAC,EAAE,MAAM,CAAC;EAC3B,MAAME,MAAM,GAAGR,QAAQ,CAACI,OAAO,CAACK,UAAU,CAACH,CAAC,CAAC,EAAEC,IAAI,CAAC;EAEpD,OAAOH,OAAO,CAACM,cAAc,CAACF,MAAM,CAACG,KAAK,EAAEH,MAAM,CAACI,KAAK,EAAEJ,MAAM,CAACK,MAAM,CAAC;AAC1E;AAEA,OAAO,MAAMC,UAAU,GAAiB;EACtCC,UAAU,EAAEjB,IAAI;EAChBkB,WAAW,EAAE,KAAK;EAClBC,UAAU,EAAEhB;CACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}