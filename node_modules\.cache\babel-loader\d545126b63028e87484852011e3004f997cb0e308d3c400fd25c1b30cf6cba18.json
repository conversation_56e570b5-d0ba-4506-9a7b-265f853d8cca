{"ast": null, "code": "import baseCreate from './_baseCreate.js';\nimport extendOwn from './extendOwn.js';\n\n// Creates an object that inherits from the given prototype object.\n// If additional properties are provided then they will be added to the\n// created object.\nexport default function create(prototype, props) {\n  var result = baseCreate(prototype);\n  if (props) extendOwn(result, props);\n  return result;\n}", "map": {"version": 3, "names": ["baseCreate", "extendOwn", "create", "prototype", "props", "result"], "sources": ["C:/tmsft/node_modules/underscore/modules/create.js"], "sourcesContent": ["import baseCreate from './_baseCreate.js';\nimport extendOwn from './extendOwn.js';\n\n// Creates an object that inherits from the given prototype object.\n// If additional properties are provided then they will be added to the\n// created object.\nexport default function create(prototype, props) {\n  var result = baseCreate(prototype);\n  if (props) extendOwn(result, props);\n  return result;\n}\n"], "mappings": "AAAA,OAAOA,UAAU,MAAM,kBAAkB;AACzC,OAAOC,SAAS,MAAM,gBAAgB;;AAEtC;AACA;AACA;AACA,eAAe,SAASC,MAAMA,CAACC,SAAS,EAAEC,KAAK,EAAE;EAC/C,IAAIC,MAAM,GAAGL,UAAU,CAACG,SAAS,CAAC;EAClC,IAAIC,KAAK,EAAEH,SAAS,CAACI,MAAM,EAAED,KAAK,CAAC;EACnC,OAAOC,MAAM;AACf", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}