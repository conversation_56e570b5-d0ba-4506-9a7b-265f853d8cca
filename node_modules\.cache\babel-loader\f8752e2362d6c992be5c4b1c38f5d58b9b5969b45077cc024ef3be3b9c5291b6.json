{"ast": null, "code": "/**\n * @license\n * Copyright 2019 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { backend_util } from '@tensorflow/tfjs-core';\nimport { getChannels } from './packing_util';\nimport { getCoordsDataType } from './shader_compiler';\nexport class ConcatPackedProgram {\n  constructor(shapes, axis) {\n    this.packedInputs = true;\n    this.packedOutput = true;\n    this.outputShape = [];\n    this.outputShape = backend_util.computeOutShape(shapes, axis);\n    const shape = this.outputShape;\n    const rank = shape.length;\n    const dtype = getCoordsDataType(rank);\n    const coords = getChannels('coords', rank);\n    const channels = ['x', 'y', 'z', 'w', 'u', 'v'].slice(0, rank);\n    this.variableNames = shapes.map((_, i) => \"T\".concat(i));\n    const offsets = new Array(shapes.length - 1);\n    offsets[0] = shapes[0][axis];\n    for (let i = 1; i < offsets.length; i++) {\n      offsets[i] = offsets[i - 1] + shapes[i][axis];\n    }\n    const channel = channels[axis];\n    const lastChannels = channels.slice(-2);\n    const allChannels = channels.join();\n    let getValueSnippet = \"if (\".concat(channel, \" < \").concat(offsets[0], \") {\\n        return getChannel(\\n            getT0(\").concat(allChannels, \"), vec2(\").concat(lastChannels.join(), \"));\\n        }\");\n    for (let i = 1; i < offsets.length; i++) {\n      const shift = offsets[i - 1];\n      // Note: the >= comparison below may seem unnecessary given the check\n      // above but is needed to workaround branch execution issues on some\n      // devices. It makes all the conditions exclusive without relying on\n      // execution order.\n      getValueSnippet += \"\\n        if (\".concat(channel, \" < \").concat(offsets[i], \"  && \").concat(channel, \" >= \").concat(offsets[i - 1], \") {\\n          return getChannel(\\n            getT\").concat(i, \"(\").concat(shiftedChannels(channels, channel, shift), \"),\\n            vec2(\").concat(shiftedChannels(lastChannels, channel, shift), \"));\\n        }\");\n    }\n    const lastIndex = offsets.length;\n    const shift = offsets[offsets.length - 1];\n    getValueSnippet += \"\\n        return getChannel(\\n          getT\".concat(lastIndex, \"(\").concat(shiftedChannels(channels, channel, shift), \"),\\n          vec2(\").concat(shiftedChannels(lastChannels, channel, shift), \"));\");\n    this.userCode = \"\\n      float getValue(\".concat(channels.map(x => 'int ' + x), \") {\\n        \").concat(getValueSnippet, \"\\n      }\\n\\n      void main() {\\n        \").concat(dtype, \" coords = getOutputCoords();\\n        vec4 result = vec4(getValue(\").concat(coords, \"), 0., 0., 0.);\\n\\n        \").concat(coords[rank - 1], \" = \").concat(coords[rank - 1], \" + 1;\\n        if (\").concat(coords[rank - 1], \" < \").concat(shape[rank - 1], \") {\\n          result.g = getValue(\").concat(coords, \");\\n        }\\n\\n        \").concat(coords[rank - 2], \" = \").concat(coords[rank - 2], \" + 1;\\n        if (\").concat(coords[rank - 2], \" < \").concat(shape[rank - 2], \") {\\n          result.a = getValue(\").concat(coords, \");\\n        }\\n\\n        \").concat(coords[rank - 1], \" = \").concat(coords[rank - 1], \" - 1;\\n        if (\").concat(coords[rank - 2], \" < \").concat(shape[rank - 2], \" &&\\n            \").concat(coords[rank - 1], \" < \").concat(shape[rank - 1], \") {\\n          result.b = getValue(\").concat(coords, \");\\n        }\\n        setOutput(result);\\n      }\\n    \");\n  }\n}\n/**\n * Return an expression for coordinates into a vector where a given channel\n * will be offset by [shift].\n *\n * @param channels the channels to consider\n * @param channel the channel we want shifted\n * @param shift  the amount to subtract from the channel.\n *\n * @returns a string of the form 'x, y-[shift], z' where any one channel can\n * have the shift applied.\n */\nfunction shiftedChannels(channels, channel, shift) {\n  const channelIdx = channels.indexOf(channel);\n  const res = channels.map((c, idx) => {\n    if (idx === channelIdx) {\n      return \"\".concat(c, \" - \").concat(shift);\n    } else {\n      return c;\n    }\n  });\n  return res.join();\n}", "map": {"version": 3, "names": ["backend_util", "getChannels", "getCoordsDataType", "ConcatPackedProgram", "constructor", "shapes", "axis", "packedInputs", "packedOutput", "outputShape", "computeOutShape", "shape", "rank", "length", "dtype", "coords", "channels", "slice", "variableNames", "map", "_", "i", "concat", "offsets", "Array", "channel", "lastChannels", "allChannels", "join", "getValueSnippet", "shift", "shiftedChannels", "lastIndex", "userCode", "x", "channelIdx", "indexOf", "res", "c", "idx"], "sources": ["C:\\tfjs-backend-webgl\\src\\concat_packed_gpu.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2019 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {backend_util} from '@tensorflow/tfjs-core';\n\nimport {GPGPUProgram} from './gpgpu_math';\nimport {getChannels} from './packing_util';\nimport {getCoordsDataType} from './shader_compiler';\n\nexport class ConcatPackedProgram implements GPGPUProgram {\n  variableNames: string[];\n  packedInputs = true;\n  packedOutput = true;\n  outputShape: number[] = [];\n  userCode: string;\n\n  constructor(shapes: number[][], axis: number) {\n    this.outputShape = backend_util.computeOutShape(shapes, axis);\n    const shape = this.outputShape;\n    const rank = shape.length;\n    const dtype = getCoordsDataType(rank);\n    const coords = getChannels('coords', rank);\n    const channels = ['x', 'y', 'z', 'w', 'u', 'v'].slice(0, rank);\n    this.variableNames = shapes.map((_, i) => `T${i}`);\n\n    const offsets: number[] = new Array(shapes.length - 1);\n    offsets[0] = shapes[0][axis];\n    for (let i = 1; i < offsets.length; i++) {\n      offsets[i] = offsets[i - 1] + shapes[i][axis];\n    }\n\n    const channel = channels[axis];\n    const lastChannels = channels.slice(-2);\n    const allChannels = channels.join();\n\n    let getValueSnippet = `if (${channel} < ${offsets[0]}) {\n        return getChannel(\n            getT0(${allChannels}), vec2(${lastChannels.join()}));\n        }`;\n    for (let i = 1; i < offsets.length; i++) {\n      const shift = offsets[i - 1];\n      // Note: the >= comparison below may seem unnecessary given the check\n      // above but is needed to workaround branch execution issues on some\n      // devices. It makes all the conditions exclusive without relying on\n      // execution order.\n      getValueSnippet += `\n        if (${channel} < ${offsets[i]}  && ${channel} >= ${offsets[i - 1]}) {\n          return getChannel(\n            getT${i}(${shiftedChannels(channels, channel, shift)}),\n            vec2(${shiftedChannels(lastChannels, channel, shift)}));\n        }`;\n    }\n    const lastIndex = offsets.length;\n    const shift = offsets[offsets.length - 1];\n    getValueSnippet += `\n        return getChannel(\n          getT${lastIndex}(${shiftedChannels(channels, channel, shift)}),\n          vec2(${shiftedChannels(lastChannels, channel, shift)}));`;\n\n    this.userCode = `\n      float getValue(${channels.map(x => 'int ' + x)}) {\n        ${getValueSnippet}\n      }\n\n      void main() {\n        ${dtype} coords = getOutputCoords();\n        vec4 result = vec4(getValue(${coords}), 0., 0., 0.);\n\n        ${coords[rank - 1]} = ${coords[rank - 1]} + 1;\n        if (${coords[rank - 1]} < ${shape[rank - 1]}) {\n          result.g = getValue(${coords});\n        }\n\n        ${coords[rank - 2]} = ${coords[rank - 2]} + 1;\n        if (${coords[rank - 2]} < ${shape[rank - 2]}) {\n          result.a = getValue(${coords});\n        }\n\n        ${coords[rank - 1]} = ${coords[rank - 1]} - 1;\n        if (${coords[rank - 2]} < ${shape[rank - 2]} &&\n            ${coords[rank - 1]} < ${shape[rank - 1]}) {\n          result.b = getValue(${coords});\n        }\n        setOutput(result);\n      }\n    `;\n  }\n}\n\n/**\n * Return an expression for coordinates into a vector where a given channel\n * will be offset by [shift].\n *\n * @param channels the channels to consider\n * @param channel the channel we want shifted\n * @param shift  the amount to subtract from the channel.\n *\n * @returns a string of the form 'x, y-[shift], z' where any one channel can\n * have the shift applied.\n */\nfunction shiftedChannels(channels: string[], channel: string, shift: number) {\n  const channelIdx = channels.indexOf(channel);\n  const res = channels.map((c, idx) => {\n    if (idx === channelIdx) {\n      return `${c} - ${shift}`;\n    } else {\n      return c;\n    }\n  });\n  return res.join();\n}\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAiBA,SAAQA,YAAY,QAAO,uBAAuB;AAGlD,SAAQC,WAAW,QAAO,gBAAgB;AAC1C,SAAQC,iBAAiB,QAAO,mBAAmB;AAEnD,OAAM,MAAOC,mBAAmB;EAO9BC,YAAYC,MAAkB,EAAEC,IAAY;IAL5C,KAAAC,YAAY,GAAG,IAAI;IACnB,KAAAC,YAAY,GAAG,IAAI;IACnB,KAAAC,WAAW,GAAa,EAAE;IAIxB,IAAI,CAACA,WAAW,GAAGT,YAAY,CAACU,eAAe,CAACL,MAAM,EAAEC,IAAI,CAAC;IAC7D,MAAMK,KAAK,GAAG,IAAI,CAACF,WAAW;IAC9B,MAAMG,IAAI,GAAGD,KAAK,CAACE,MAAM;IACzB,MAAMC,KAAK,GAAGZ,iBAAiB,CAACU,IAAI,CAAC;IACrC,MAAMG,MAAM,GAAGd,WAAW,CAAC,QAAQ,EAAEW,IAAI,CAAC;IAC1C,MAAMI,QAAQ,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAACC,KAAK,CAAC,CAAC,EAAEL,IAAI,CAAC;IAC9D,IAAI,CAACM,aAAa,GAAGb,MAAM,CAACc,GAAG,CAAC,CAACC,CAAC,EAAEC,CAAC,SAAAC,MAAA,CAASD,CAAC,CAAE,CAAC;IAElD,MAAME,OAAO,GAAa,IAAIC,KAAK,CAACnB,MAAM,CAACQ,MAAM,GAAG,CAAC,CAAC;IACtDU,OAAO,CAAC,CAAC,CAAC,GAAGlB,MAAM,CAAC,CAAC,CAAC,CAACC,IAAI,CAAC;IAC5B,KAAK,IAAIe,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGE,OAAO,CAACV,MAAM,EAAEQ,CAAC,EAAE,EAAE;MACvCE,OAAO,CAACF,CAAC,CAAC,GAAGE,OAAO,CAACF,CAAC,GAAG,CAAC,CAAC,GAAGhB,MAAM,CAACgB,CAAC,CAAC,CAACf,IAAI,CAAC;;IAG/C,MAAMmB,OAAO,GAAGT,QAAQ,CAACV,IAAI,CAAC;IAC9B,MAAMoB,YAAY,GAAGV,QAAQ,CAACC,KAAK,CAAC,CAAC,CAAC,CAAC;IACvC,MAAMU,WAAW,GAAGX,QAAQ,CAACY,IAAI,EAAE;IAEnC,IAAIC,eAAe,UAAAP,MAAA,CAAUG,OAAO,SAAAH,MAAA,CAAMC,OAAO,CAAC,CAAC,CAAC,yDAAAD,MAAA,CAEpCK,WAAW,cAAAL,MAAA,CAAWI,YAAY,CAACE,IAAI,EAAE,mBACnD;IACN,KAAK,IAAIP,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGE,OAAO,CAACV,MAAM,EAAEQ,CAAC,EAAE,EAAE;MACvC,MAAMS,KAAK,GAAGP,OAAO,CAACF,CAAC,GAAG,CAAC,CAAC;MAC5B;MACA;MACA;MACA;MACAQ,eAAe,qBAAAP,MAAA,CACPG,OAAO,SAAAH,MAAA,CAAMC,OAAO,CAACF,CAAC,CAAC,WAAAC,MAAA,CAAQG,OAAO,UAAAH,MAAA,CAAOC,OAAO,CAACF,CAAC,GAAG,CAAC,CAAC,yDAAAC,MAAA,CAEvDD,CAAC,OAAAC,MAAA,CAAIS,eAAe,CAACf,QAAQ,EAAES,OAAO,EAAEK,KAAK,CAAC,2BAAAR,MAAA,CAC7CS,eAAe,CAACL,YAAY,EAAED,OAAO,EAAEK,KAAK,CAAC,mBACtD;;IAEN,MAAME,SAAS,GAAGT,OAAO,CAACV,MAAM;IAChC,MAAMiB,KAAK,GAAGP,OAAO,CAACA,OAAO,CAACV,MAAM,GAAG,CAAC,CAAC;IACzCgB,eAAe,mDAAAP,MAAA,CAEHU,SAAS,OAAAV,MAAA,CAAIS,eAAe,CAACf,QAAQ,EAAES,OAAO,EAAEK,KAAK,CAAC,yBAAAR,MAAA,CACrDS,eAAe,CAACL,YAAY,EAAED,OAAO,EAAEK,KAAK,CAAC,QAAK;IAE/D,IAAI,CAACG,QAAQ,6BAAAX,MAAA,CACMN,QAAQ,CAACG,GAAG,CAACe,CAAC,IAAI,MAAM,GAAGA,CAAC,CAAC,mBAAAZ,MAAA,CAC1CO,eAAe,gDAAAP,MAAA,CAIfR,KAAK,wEAAAQ,MAAA,CACuBP,MAAM,iCAAAO,MAAA,CAElCP,MAAM,CAACH,IAAI,GAAG,CAAC,CAAC,SAAAU,MAAA,CAAMP,MAAM,CAACH,IAAI,GAAG,CAAC,CAAC,yBAAAU,MAAA,CAClCP,MAAM,CAACH,IAAI,GAAG,CAAC,CAAC,SAAAU,MAAA,CAAMX,KAAK,CAACC,IAAI,GAAG,CAAC,CAAC,yCAAAU,MAAA,CACnBP,MAAM,+BAAAO,MAAA,CAG5BP,MAAM,CAACH,IAAI,GAAG,CAAC,CAAC,SAAAU,MAAA,CAAMP,MAAM,CAACH,IAAI,GAAG,CAAC,CAAC,yBAAAU,MAAA,CAClCP,MAAM,CAACH,IAAI,GAAG,CAAC,CAAC,SAAAU,MAAA,CAAMX,KAAK,CAACC,IAAI,GAAG,CAAC,CAAC,yCAAAU,MAAA,CACnBP,MAAM,+BAAAO,MAAA,CAG5BP,MAAM,CAACH,IAAI,GAAG,CAAC,CAAC,SAAAU,MAAA,CAAMP,MAAM,CAACH,IAAI,GAAG,CAAC,CAAC,yBAAAU,MAAA,CAClCP,MAAM,CAACH,IAAI,GAAG,CAAC,CAAC,SAAAU,MAAA,CAAMX,KAAK,CAACC,IAAI,GAAG,CAAC,CAAC,uBAAAU,MAAA,CACrCP,MAAM,CAACH,IAAI,GAAG,CAAC,CAAC,SAAAU,MAAA,CAAMX,KAAK,CAACC,IAAI,GAAG,CAAC,CAAC,yCAAAU,MAAA,CACnBP,MAAM,6DAIjC;EACH;;AAGF;;;;;;;;;;;AAWA,SAASgB,eAAeA,CAACf,QAAkB,EAAES,OAAe,EAAEK,KAAa;EACzE,MAAMK,UAAU,GAAGnB,QAAQ,CAACoB,OAAO,CAACX,OAAO,CAAC;EAC5C,MAAMY,GAAG,GAAGrB,QAAQ,CAACG,GAAG,CAAC,CAACmB,CAAC,EAAEC,GAAG,KAAI;IAClC,IAAIA,GAAG,KAAKJ,UAAU,EAAE;MACtB,UAAAb,MAAA,CAAUgB,CAAC,SAAAhB,MAAA,CAAMQ,KAAK;KACvB,MAAM;MACL,OAAOQ,CAAC;;EAEZ,CAAC,CAAC;EACF,OAAOD,GAAG,CAACT,IAAI,EAAE;AACnB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}