{"ast": null, "code": "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { OneHot, util } from '@tensorflow/tfjs-core';\nimport { assertNotComplex } from '../cpu_util';\nexport function oneHot(args) {\n  const {\n    inputs,\n    backend,\n    attrs\n  } = args;\n  const {\n    indices\n  } = inputs;\n  const {\n    dtype,\n    depth,\n    onValue,\n    offValue\n  } = attrs;\n  assertNotComplex(indices, 'oneHot');\n  const indicesSize = util.sizeFromShape(indices.shape);\n  const res = new Float32Array(indicesSize * depth);\n  res.fill(offValue);\n  const indicesVal = backend.data.get(indices.dataId).values;\n  for (let event = 0; event < indicesSize; ++event) {\n    if (indicesVal[event] >= 0 && indicesVal[event] < depth) {\n      res[event * depth + indicesVal[event]] = onValue;\n    }\n  }\n  return backend.makeTensorInfo([...indices.shape, depth], dtype, res);\n}\nexport const oneHotConfig = {\n  kernelName: OneHot,\n  backendName: 'cpu',\n  kernelFunc: oneHot\n};", "map": {"version": 3, "names": ["OneHot", "util", "assertNotComplex", "oneHot", "args", "inputs", "backend", "attrs", "indices", "dtype", "depth", "onValue", "offValue", "indicesSize", "sizeFromShape", "shape", "res", "Float32Array", "fill", "indicesVal", "data", "get", "dataId", "values", "event", "makeTensorInfo", "oneHotConfig", "kernelName", "backendName", "kernelFunc"], "sources": ["C:\\tfjs-backend-cpu\\src\\kernels\\OneHot.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {KernelConfig, KernelFunc, OneHot, OneHotAttrs, OneHotInputs, TensorInfo, TypedArray, util} from '@tensorflow/tfjs-core';\n\nimport {MathBackendCPU} from '../backend_cpu';\nimport {assertNotComplex} from '../cpu_util';\n\nexport function oneHot(\n    args: {inputs: OneHotInputs, backend: MathBackendCPU, attrs: OneHotAttrs}):\n    TensorInfo {\n  const {inputs, backend, attrs} = args;\n  const {indices} = inputs;\n  const {dtype, depth, onValue, offValue} = attrs;\n\n  assertNotComplex(indices, 'oneHot');\n\n  const indicesSize = util.sizeFromShape(indices.shape);\n\n  const res = new Float32Array(indicesSize * depth);\n  res.fill(offValue);\n  const indicesVal = backend.data.get(indices.dataId).values as TypedArray;\n\n  for (let event = 0; event < indicesSize; ++event) {\n    if (indicesVal[event] >= 0 && indicesVal[event] < depth) {\n      res[event * depth + indicesVal[event]] = onValue;\n    }\n  }\n\n  return backend.makeTensorInfo([...indices.shape, depth], dtype, res);\n}\n\nexport const oneHotConfig: KernelConfig = {\n  kernelName: OneHot,\n  backendName: 'cpu',\n  kernelFunc: oneHot as unknown as KernelFunc\n};\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAiBA,SAAkCA,MAAM,EAAqDC,IAAI,QAAO,uBAAuB;AAG/H,SAAQC,gBAAgB,QAAO,aAAa;AAE5C,OAAM,SAAUC,MAAMA,CAClBC,IAAyE;EAE3E,MAAM;IAACC,MAAM;IAAEC,OAAO;IAAEC;EAAK,CAAC,GAAGH,IAAI;EACrC,MAAM;IAACI;EAAO,CAAC,GAAGH,MAAM;EACxB,MAAM;IAACI,KAAK;IAAEC,KAAK;IAAEC,OAAO;IAAEC;EAAQ,CAAC,GAAGL,KAAK;EAE/CL,gBAAgB,CAACM,OAAO,EAAE,QAAQ,CAAC;EAEnC,MAAMK,WAAW,GAAGZ,IAAI,CAACa,aAAa,CAACN,OAAO,CAACO,KAAK,CAAC;EAErD,MAAMC,GAAG,GAAG,IAAIC,YAAY,CAACJ,WAAW,GAAGH,KAAK,CAAC;EACjDM,GAAG,CAACE,IAAI,CAACN,QAAQ,CAAC;EAClB,MAAMO,UAAU,GAAGb,OAAO,CAACc,IAAI,CAACC,GAAG,CAACb,OAAO,CAACc,MAAM,CAAC,CAACC,MAAoB;EAExE,KAAK,IAAIC,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAGX,WAAW,EAAE,EAAEW,KAAK,EAAE;IAChD,IAAIL,UAAU,CAACK,KAAK,CAAC,IAAI,CAAC,IAAIL,UAAU,CAACK,KAAK,CAAC,GAAGd,KAAK,EAAE;MACvDM,GAAG,CAACQ,KAAK,GAAGd,KAAK,GAAGS,UAAU,CAACK,KAAK,CAAC,CAAC,GAAGb,OAAO;;;EAIpD,OAAOL,OAAO,CAACmB,cAAc,CAAC,CAAC,GAAGjB,OAAO,CAACO,KAAK,EAAEL,KAAK,CAAC,EAAED,KAAK,EAAEO,GAAG,CAAC;AACtE;AAEA,OAAO,MAAMU,YAAY,GAAiB;EACxCC,UAAU,EAAE3B,MAAM;EAClB4B,WAAW,EAAE,KAAK;EAClBC,UAAU,EAAE1B;CACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}