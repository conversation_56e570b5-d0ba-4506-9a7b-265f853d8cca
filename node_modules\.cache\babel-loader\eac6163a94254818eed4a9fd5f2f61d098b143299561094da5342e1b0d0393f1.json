{"ast": null, "code": "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n// TODO update import path once op is modularized.\nimport { cast } from '../../ops/ops';\nimport { getGlobalTensorClass } from '../../tensor';\n/**\n * Casts the array to type `int32`\n *\n * @doc {heading: 'Tensors', subheading: 'Classes'}\n */\ngetGlobalTensorClass().prototype.toInt = function () {\n  this.throwIfDisposed();\n  return cast(this, 'int32');\n};", "map": {"version": 3, "names": ["cast", "getGlobalTensorClass", "prototype", "toInt", "throwIfDisposed"], "sources": ["C:\\tfjs-core\\src\\public\\chained_ops\\to_int.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\n// TODO update import path once op is modularized.\nimport {cast} from '../../ops/ops';\nimport {getGlobalTensorClass, Tensor} from '../../tensor';\nimport {Rank} from '../../types';\n\ndeclare module '../../tensor' {\n  interface Tensor<R extends Rank = Rank> {\n    toInt<T extends Tensor>(this: T): T;\n  }\n}\n\n/**\n * Casts the array to type `int32`\n *\n * @doc {heading: 'Tensors', subheading: 'Classes'}\n */\ngetGlobalTensorClass().prototype.toInt = function<T extends Tensor>(this: T):\n    T {\n  this.throwIfDisposed();\n  return cast<T>(this, 'int32');\n};\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAiBA;AACA,SAAQA,IAAI,QAAO,eAAe;AAClC,SAAQC,oBAAoB,QAAe,cAAc;AASzD;;;;;AAKAA,oBAAoB,EAAE,CAACC,SAAS,CAACC,KAAK,GAAG;EAEvC,IAAI,CAACC,eAAe,EAAE;EACtB,OAAOJ,IAAI,CAAI,IAAI,EAAE,OAAO,CAAC;AAC/B,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}