{"ast": null, "code": "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { mean } from '../../ops/mean';\nimport { getGlobalTensorClass } from '../../tensor';\ngetGlobalTensorClass().prototype.mean = function (axis, keepDims) {\n  this.throwIfDisposed();\n  return mean(this, axis, keepDims);\n};", "map": {"version": 3, "names": ["mean", "getGlobalTensorClass", "prototype", "axis", "keepDims", "throwIfDisposed"], "sources": ["C:\\tfjs-core\\src\\public\\chained_ops\\mean.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport {mean} from '../../ops/mean';\nimport {getGlobalTensorClass, Tensor} from '../../tensor';\nimport {Rank} from '../../types';\n\ndeclare module '../../tensor' {\n  interface Tensor<R extends Rank = Rank> {\n    mean<T extends Tensor>(axis?: number|number[], keepDims?: boolean): T;\n  }\n}\n\ngetGlobalTensorClass().prototype.mean = function<T extends Tensor>(\n    axis?: number|number[], keepDims?: boolean): T {\n  this.throwIfDisposed();\n  return mean(this, axis, keepDims);\n};\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAgBA,SAAQA,IAAI,QAAO,gBAAgB;AACnC,SAAQC,oBAAoB,QAAe,cAAc;AASzDA,oBAAoB,EAAE,CAACC,SAAS,CAACF,IAAI,GAAG,UACpCG,IAAsB,EAAEC,QAAkB;EAC5C,IAAI,CAACC,eAAe,EAAE;EACtB,OAAOL,IAAI,CAAC,IAAI,EAAEG,IAAI,EAAEC,QAAQ,CAAC;AACnC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}