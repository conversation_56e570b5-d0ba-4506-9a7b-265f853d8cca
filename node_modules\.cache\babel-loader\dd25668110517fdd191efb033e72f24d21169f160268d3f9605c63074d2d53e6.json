{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.transformReply = exports.transformArguments = exports.IS_READ_ONLY = void 0;\nconst _1 = require(\".\");\nexports.IS_READ_ONLY = true;\nfunction transformArguments(fromTimestamp, toTimestamp, filters, options) {\n  return (0, _1.pushMRangeArguments)(['TS.MRANGE'], fromTimestamp, toTimestamp, filters, options);\n}\nexports.transformArguments = transformArguments;\nvar _2 = require(\".\");\nObject.defineProperty(exports, \"transformReply\", {\n  enumerable: true,\n  get: function () {\n    return _2.transformMRangeReply;\n  }\n});", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "transformReply", "transformArguments", "IS_READ_ONLY", "_1", "require", "fromTimestamp", "toTimestamp", "filters", "options", "pushMRangeArguments", "_2", "enumerable", "get", "transformMRangeReply"], "sources": ["C:/tmsft/node_modules/@redis/time-series/dist/commands/MRANGE.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.transformReply = exports.transformArguments = exports.IS_READ_ONLY = void 0;\nconst _1 = require(\".\");\nexports.IS_READ_ONLY = true;\nfunction transformArguments(fromTimestamp, toTimestamp, filters, options) {\n    return (0, _1.pushMRangeArguments)(['TS.MRANGE'], fromTimestamp, toTimestamp, filters, options);\n}\nexports.transformArguments = transformArguments;\nvar _2 = require(\".\");\nObject.defineProperty(exports, \"transformReply\", { enumerable: true, get: function () { return _2.transformMRangeReply; } });\n"], "mappings": "AAAA,YAAY;;AACZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEC,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7DD,OAAO,CAACE,cAAc,GAAGF,OAAO,CAACG,kBAAkB,GAAGH,OAAO,CAACI,YAAY,GAAG,KAAK,CAAC;AACnF,MAAMC,EAAE,GAAGC,OAAO,CAAC,GAAG,CAAC;AACvBN,OAAO,CAACI,YAAY,GAAG,IAAI;AAC3B,SAASD,kBAAkBA,CAACI,aAAa,EAAEC,WAAW,EAAEC,OAAO,EAAEC,OAAO,EAAE;EACtE,OAAO,CAAC,CAAC,EAAEL,EAAE,CAACM,mBAAmB,EAAE,CAAC,WAAW,CAAC,EAAEJ,aAAa,EAAEC,WAAW,EAAEC,OAAO,EAAEC,OAAO,CAAC;AACnG;AACAV,OAAO,CAACG,kBAAkB,GAAGA,kBAAkB;AAC/C,IAAIS,EAAE,GAAGN,OAAO,CAAC,GAAG,CAAC;AACrBR,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,gBAAgB,EAAE;EAAEa,UAAU,EAAE,IAAI;EAAEC,GAAG,EAAE,SAAAA,CAAA,EAAY;IAAE,OAAOF,EAAE,CAACG,oBAAoB;EAAE;AAAE,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}