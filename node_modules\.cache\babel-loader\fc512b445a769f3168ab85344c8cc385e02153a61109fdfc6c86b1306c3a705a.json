{"ast": null, "code": "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { All, backend_util, util } from '@tensorflow/tfjs-core';\nimport { assertNotComplex } from '../cpu_util';\nimport { reshape } from './Reshape';\nimport { transpose } from './Transpose';\nexport function all(args) {\n  const {\n    inputs,\n    backend,\n    attrs\n  } = args;\n  const {\n    x\n  } = inputs;\n  const {\n    axis,\n    keepDims\n  } = attrs;\n  assertNotComplex(x, 'all');\n  const origAxes = util.parseAxisParam(axis, x.shape);\n  let axes = origAxes;\n  const permutedAxes = backend_util.getAxesPermutation(axes, x.shape.length);\n  let $x = x;\n  if (permutedAxes != null) {\n    $x = transpose({\n      inputs: {\n        x\n      },\n      backend,\n      attrs: {\n        perm: permutedAxes\n      }\n    });\n    axes = backend_util.getInnerMostAxes(axes.length, x.shape.length);\n  }\n  backend_util.assertAxesAreInnerMostDims('all', axes, $x.shape.length);\n  const [outShape, reduceShape] = backend_util.computeOutAndReduceShapes($x.shape, axes);\n  const reduceSize = util.sizeFromShape(reduceShape);\n  const vals = util.makeZerosTypedArray(util.sizeFromShape(outShape), $x.dtype);\n  const aVals = backend.data.get($x.dataId).values;\n  for (let i = 0; i < vals.length; ++i) {\n    const offset = i * reduceSize;\n    let all = aVals[offset];\n    for (let j = 0; j < reduceSize; ++j) {\n      const value = aVals[offset + j];\n      all = all && value;\n    }\n    vals[i] = all;\n  }\n  if (permutedAxes != null) {\n    backend.disposeIntermediateTensorInfo($x);\n  }\n  const result = backend.makeTensorInfo(outShape, $x.dtype, vals);\n  if (keepDims) {\n    const expandedShape = backend_util.expandShapeToKeepDim(outShape, origAxes);\n    const reshapedResult = reshape({\n      inputs: {\n        x: result\n      },\n      backend,\n      attrs: {\n        shape: expandedShape\n      }\n    });\n    backend.disposeIntermediateTensorInfo(result);\n    return reshapedResult;\n  }\n  return result;\n}\nexport const allConfig = {\n  kernelName: All,\n  backendName: 'cpu',\n  kernelFunc: all\n};", "map": {"version": 3, "names": ["All", "backend_util", "util", "assertNotComplex", "reshape", "transpose", "all", "args", "inputs", "backend", "attrs", "x", "axis", "keepDims", "origAxes", "parseAxisParam", "shape", "axes", "permutedAxes", "getAxesPermutation", "length", "$x", "perm", "getInnerMostAxes", "assertAxesAreInnerMostDims", "outShape", "reduceShape", "computeOutAndReduceShapes", "reduceSize", "sizeFromShape", "vals", "makeZerosTypedArray", "dtype", "aVals", "data", "get", "dataId", "values", "i", "offset", "j", "value", "disposeIntermediateTensorInfo", "result", "makeTensorInfo", "expandedShape", "expandShapeToKeepDim", "reshapedResult", "allConfig", "kernelName", "backendName", "kernelFunc"], "sources": ["C:\\tfjs-backend-cpu\\src\\kernels\\All.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {All, AllAttrs, AllInputs, backend_util, KernelConfig, KernelFunc, TensorInfo, TypedArray, util} from '@tensorflow/tfjs-core';\n\nimport {MathBackendCPU} from '../backend_cpu';\nimport {assertNotComplex} from '../cpu_util';\nimport {reshape} from './Reshape';\nimport {transpose} from './Transpose';\n\nexport function all(\n    args: {inputs: AllInputs, backend: MathBackendCPU, attrs: AllAttrs}):\n    TensorInfo {\n  const {inputs, backend, attrs} = args;\n  const {x} = inputs;\n  const {axis, keepDims} = attrs;\n\n  assertNotComplex(x, 'all');\n\n  const origAxes = util.parseAxisParam(axis, x.shape);\n  let axes = origAxes;\n  const permutedAxes = backend_util.getAxesPermutation(axes, x.shape.length);\n  let $x = x;\n  if (permutedAxes != null) {\n    $x = transpose({inputs: {x}, backend, attrs: {perm: permutedAxes}});\n    axes = backend_util.getInnerMostAxes(axes.length, x.shape.length);\n  }\n\n  backend_util.assertAxesAreInnerMostDims('all', axes, $x.shape.length);\n  const [outShape, reduceShape] =\n      backend_util.computeOutAndReduceShapes($x.shape, axes);\n  const reduceSize = util.sizeFromShape(reduceShape);\n  const vals = util.makeZerosTypedArray(util.sizeFromShape(outShape), $x.dtype);\n\n  const aVals = backend.data.get($x.dataId).values as TypedArray;\n  for (let i = 0; i < vals.length; ++i) {\n    const offset = i * reduceSize;\n    let all = aVals[offset];\n    for (let j = 0; j < reduceSize; ++j) {\n      const value = aVals[offset + j];\n      all = all && value;\n    }\n    vals[i] = all;\n  }\n\n  if (permutedAxes != null) {\n    backend.disposeIntermediateTensorInfo($x);\n  }\n\n  const result = backend.makeTensorInfo(outShape, $x.dtype, vals);\n\n  if (keepDims) {\n    const expandedShape = backend_util.expandShapeToKeepDim(outShape, origAxes);\n    const reshapedResult =\n        reshape({inputs: {x: result}, backend, attrs: {shape: expandedShape}});\n\n    backend.disposeIntermediateTensorInfo(result);\n\n    return reshapedResult;\n  }\n\n  return result;\n}\n\nexport const allConfig: KernelConfig = {\n  kernelName: All,\n  backendName: 'cpu',\n  kernelFunc: all as unknown as KernelFunc\n};\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAiBA,SAAQA,GAAG,EAAuBC,YAAY,EAAoDC,IAAI,QAAO,uBAAuB;AAGpI,SAAQC,gBAAgB,QAAO,aAAa;AAC5C,SAAQC,OAAO,QAAO,WAAW;AACjC,SAAQC,SAAS,QAAO,aAAa;AAErC,OAAM,SAAUC,GAAGA,CACfC,IAAmE;EAErE,MAAM;IAACC,MAAM;IAAEC,OAAO;IAAEC;EAAK,CAAC,GAAGH,IAAI;EACrC,MAAM;IAACI;EAAC,CAAC,GAAGH,MAAM;EAClB,MAAM;IAACI,IAAI;IAAEC;EAAQ,CAAC,GAAGH,KAAK;EAE9BP,gBAAgB,CAACQ,CAAC,EAAE,KAAK,CAAC;EAE1B,MAAMG,QAAQ,GAAGZ,IAAI,CAACa,cAAc,CAACH,IAAI,EAAED,CAAC,CAACK,KAAK,CAAC;EACnD,IAAIC,IAAI,GAAGH,QAAQ;EACnB,MAAMI,YAAY,GAAGjB,YAAY,CAACkB,kBAAkB,CAACF,IAAI,EAAEN,CAAC,CAACK,KAAK,CAACI,MAAM,CAAC;EAC1E,IAAIC,EAAE,GAAGV,CAAC;EACV,IAAIO,YAAY,IAAI,IAAI,EAAE;IACxBG,EAAE,GAAGhB,SAAS,CAAC;MAACG,MAAM,EAAE;QAACG;MAAC,CAAC;MAAEF,OAAO;MAAEC,KAAK,EAAE;QAACY,IAAI,EAAEJ;MAAY;IAAC,CAAC,CAAC;IACnED,IAAI,GAAGhB,YAAY,CAACsB,gBAAgB,CAACN,IAAI,CAACG,MAAM,EAAET,CAAC,CAACK,KAAK,CAACI,MAAM,CAAC;;EAGnEnB,YAAY,CAACuB,0BAA0B,CAAC,KAAK,EAAEP,IAAI,EAAEI,EAAE,CAACL,KAAK,CAACI,MAAM,CAAC;EACrE,MAAM,CAACK,QAAQ,EAAEC,WAAW,CAAC,GACzBzB,YAAY,CAAC0B,yBAAyB,CAACN,EAAE,CAACL,KAAK,EAAEC,IAAI,CAAC;EAC1D,MAAMW,UAAU,GAAG1B,IAAI,CAAC2B,aAAa,CAACH,WAAW,CAAC;EAClD,MAAMI,IAAI,GAAG5B,IAAI,CAAC6B,mBAAmB,CAAC7B,IAAI,CAAC2B,aAAa,CAACJ,QAAQ,CAAC,EAAEJ,EAAE,CAACW,KAAK,CAAC;EAE7E,MAAMC,KAAK,GAAGxB,OAAO,CAACyB,IAAI,CAACC,GAAG,CAACd,EAAE,CAACe,MAAM,CAAC,CAACC,MAAoB;EAC9D,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGR,IAAI,CAACV,MAAM,EAAE,EAAEkB,CAAC,EAAE;IACpC,MAAMC,MAAM,GAAGD,CAAC,GAAGV,UAAU;IAC7B,IAAItB,GAAG,GAAG2B,KAAK,CAACM,MAAM,CAAC;IACvB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGZ,UAAU,EAAE,EAAEY,CAAC,EAAE;MACnC,MAAMC,KAAK,GAAGR,KAAK,CAACM,MAAM,GAAGC,CAAC,CAAC;MAC/BlC,GAAG,GAAGA,GAAG,IAAImC,KAAK;;IAEpBX,IAAI,CAACQ,CAAC,CAAC,GAAGhC,GAAG;;EAGf,IAAIY,YAAY,IAAI,IAAI,EAAE;IACxBT,OAAO,CAACiC,6BAA6B,CAACrB,EAAE,CAAC;;EAG3C,MAAMsB,MAAM,GAAGlC,OAAO,CAACmC,cAAc,CAACnB,QAAQ,EAAEJ,EAAE,CAACW,KAAK,EAAEF,IAAI,CAAC;EAE/D,IAAIjB,QAAQ,EAAE;IACZ,MAAMgC,aAAa,GAAG5C,YAAY,CAAC6C,oBAAoB,CAACrB,QAAQ,EAAEX,QAAQ,CAAC;IAC3E,MAAMiC,cAAc,GAChB3C,OAAO,CAAC;MAACI,MAAM,EAAE;QAACG,CAAC,EAAEgC;MAAM,CAAC;MAAElC,OAAO;MAAEC,KAAK,EAAE;QAACM,KAAK,EAAE6B;MAAa;IAAC,CAAC,CAAC;IAE1EpC,OAAO,CAACiC,6BAA6B,CAACC,MAAM,CAAC;IAE7C,OAAOI,cAAc;;EAGvB,OAAOJ,MAAM;AACf;AAEA,OAAO,MAAMK,SAAS,GAAiB;EACrCC,UAAU,EAAEjD,GAAG;EACfkD,WAAW,EAAE,KAAK;EAClBC,UAAU,EAAE7C;CACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}