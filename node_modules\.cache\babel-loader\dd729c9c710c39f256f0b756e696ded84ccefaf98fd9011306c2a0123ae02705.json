{"ast": null, "code": "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the License);\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an AS IS BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { _FusedMatMul } from '@tensorflow/tfjs-core';\nimport { applyActivation } from '../utils/fused_utils';\nimport { add } from './Add';\nimport { batchMatMul } from './BatchMatMul';\nexport function _fusedMatMul(args) {\n  const {\n    inputs,\n    backend,\n    attrs\n  } = args;\n  const {\n    a,\n    b,\n    bias,\n    preluActivationWeights\n  } = inputs;\n  const {\n    transposeA,\n    transposeB,\n    activation,\n    leakyreluAlpha\n  } = attrs;\n  let current;\n  let addRes;\n  let activationRes;\n  const intermediates = [];\n  const matMulRes = batchMatMul({\n    inputs: {\n      a,\n      b\n    },\n    attrs: {\n      transposeA,\n      transposeB\n    },\n    backend\n  });\n  current = matMulRes;\n  if (bias) {\n    addRes = add({\n      inputs: {\n        a: current,\n        b: bias\n      },\n      backend\n    });\n    intermediates.push(current);\n    current = addRes;\n  }\n  if (activation) {\n    activationRes = applyActivation(backend, current, activation, preluActivationWeights, leakyreluAlpha);\n    intermediates.push(current);\n    current = activationRes;\n  }\n  for (const i of intermediates) {\n    backend.disposeIntermediateTensorInfo(i);\n  }\n  return current;\n}\nexport const _fusedMatMulConfig = {\n  kernelName: _FusedMatMul,\n  backendName: 'cpu',\n  kernelFunc: _fusedMatMul\n};", "map": {"version": 3, "names": ["_FusedMatMul", "applyActivation", "add", "batchMatMul", "_fusedMatMul", "args", "inputs", "backend", "attrs", "a", "b", "bias", "preluActivationWeights", "transposeA", "transposeB", "activation", "leakyreluAlpha", "current", "addRes", "activationRes", "intermediates", "matMulRes", "push", "i", "disposeIntermediateTensorInfo", "_fusedMatMulConfig", "kernelName", "backendName", "kernelFunc"], "sources": ["C:\\tfjs-backend-cpu\\src\\kernels\\_FusedMatMul.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the License);\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an AS IS BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {_FusedMatMul, _FusedMatMulAttrs, _FusedMatMulInputs, KernelConfig, KernelFunc, TensorInfo} from '@tensorflow/tfjs-core';\n\nimport {MathBackendCPU} from '../backend_cpu';\nimport {applyActivation} from '../utils/fused_utils';\n\nimport {add} from './Add';\nimport {batchMatMul} from './BatchMatMul';\n\nexport function _fusedMatMul(args: {\n  inputs: _FusedMatMulInputs,\n  attrs: _FusedMatMulAttrs,\n  backend: MathBackendCPU\n}) {\n  const {inputs, backend, attrs} = args;\n  const {a, b, bias, preluActivationWeights} = inputs;\n  const {transposeA, transposeB, activation, leakyreluAlpha} = attrs;\n\n  let current;\n  let addRes;\n  let activationRes;\n\n  const intermediates: TensorInfo[] = [];\n\n  const matMulRes =\n      batchMatMul({inputs: {a, b}, attrs: {transposeA, transposeB}, backend});\n  current = matMulRes;\n\n  if (bias) {\n    addRes = add({inputs: {a: current, b: bias}, backend}) as TensorInfo;\n    intermediates.push(current);\n    current = addRes;\n  }\n  if (activation) {\n    activationRes = applyActivation(\n        backend, current, activation, preluActivationWeights, leakyreluAlpha);\n    intermediates.push(current);\n    current = activationRes;\n  }\n\n  for (const i of intermediates) {\n    backend.disposeIntermediateTensorInfo(i);\n  }\n\n  return current;\n}\n\nexport const _fusedMatMulConfig: KernelConfig = {\n  kernelName: _FusedMatMul,\n  backendName: 'cpu',\n  kernelFunc: _fusedMatMul as unknown as KernelFunc,\n};\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAiBA,SAAQA,YAAY,QAAoF,uBAAuB;AAG/H,SAAQC,eAAe,QAAO,sBAAsB;AAEpD,SAAQC,GAAG,QAAO,OAAO;AACzB,SAAQC,WAAW,QAAO,eAAe;AAEzC,OAAM,SAAUC,YAAYA,CAACC,IAI5B;EACC,MAAM;IAACC,MAAM;IAAEC,OAAO;IAAEC;EAAK,CAAC,GAAGH,IAAI;EACrC,MAAM;IAACI,CAAC;IAAEC,CAAC;IAAEC,IAAI;IAAEC;EAAsB,CAAC,GAAGN,MAAM;EACnD,MAAM;IAACO,UAAU;IAAEC,UAAU;IAAEC,UAAU;IAAEC;EAAc,CAAC,GAAGR,KAAK;EAElE,IAAIS,OAAO;EACX,IAAIC,MAAM;EACV,IAAIC,aAAa;EAEjB,MAAMC,aAAa,GAAiB,EAAE;EAEtC,MAAMC,SAAS,GACXlB,WAAW,CAAC;IAACG,MAAM,EAAE;MAACG,CAAC;MAAEC;IAAC,CAAC;IAAEF,KAAK,EAAE;MAACK,UAAU;MAAEC;IAAU,CAAC;IAAEP;EAAO,CAAC,CAAC;EAC3EU,OAAO,GAAGI,SAAS;EAEnB,IAAIV,IAAI,EAAE;IACRO,MAAM,GAAGhB,GAAG,CAAC;MAACI,MAAM,EAAE;QAACG,CAAC,EAAEQ,OAAO;QAAEP,CAAC,EAAEC;MAAI,CAAC;MAAEJ;IAAO,CAAC,CAAe;IACpEa,aAAa,CAACE,IAAI,CAACL,OAAO,CAAC;IAC3BA,OAAO,GAAGC,MAAM;;EAElB,IAAIH,UAAU,EAAE;IACdI,aAAa,GAAGlB,eAAe,CAC3BM,OAAO,EAAEU,OAAO,EAAEF,UAAU,EAAEH,sBAAsB,EAAEI,cAAc,CAAC;IACzEI,aAAa,CAACE,IAAI,CAACL,OAAO,CAAC;IAC3BA,OAAO,GAAGE,aAAa;;EAGzB,KAAK,MAAMI,CAAC,IAAIH,aAAa,EAAE;IAC7Bb,OAAO,CAACiB,6BAA6B,CAACD,CAAC,CAAC;;EAG1C,OAAON,OAAO;AAChB;AAEA,OAAO,MAAMQ,kBAAkB,GAAiB;EAC9CC,UAAU,EAAE1B,YAAY;EACxB2B,WAAW,EAAE,KAAK;EAClBC,UAAU,EAAExB;CACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}