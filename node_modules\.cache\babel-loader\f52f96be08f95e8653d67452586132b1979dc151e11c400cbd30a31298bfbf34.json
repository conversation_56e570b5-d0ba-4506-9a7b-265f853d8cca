{"ast": null, "code": "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { FFT } from '@tensorflow/tfjs-core';\nimport { fftImpl } from './FFT_impl';\nexport function fft(args) {\n  const {\n    inputs,\n    backend\n  } = args;\n  const {\n    input\n  } = inputs;\n  return fftImpl(input, false /* inverse */, backend);\n}\nexport const fftConfig = {\n  kernelName: FFT,\n  backendName: 'webgl',\n  kernelFunc: fft\n};", "map": {"version": 3, "names": ["FFT", "fftImpl", "fft", "args", "inputs", "backend", "input", "fftConfig", "kernelName", "backendName", "kernelFunc"], "sources": ["C:\\tfjs-backend-webgl\\src\\kernels\\FFT.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {FFT, FFTInputs, KernelConfig, TensorInfo} from '@tensorflow/tfjs-core';\n\nimport {MathBackendWebGL} from '../backend_webgl';\n\nimport {fftImpl} from './FFT_impl';\n\nexport function fft(args: {inputs: FFTInputs, backend: MathBackendWebGL}):\n    TensorInfo {\n  const {inputs, backend} = args;\n  const {input} = inputs;\n\n  return fftImpl(input, false /* inverse */, backend);\n}\n\nexport const fftConfig: KernelConfig = {\n  kernelName: FFT,\n  backendName: 'webgl',\n  kernelFunc: fft\n};\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAiBA,SAAQA,GAAG,QAA4C,uBAAuB;AAI9E,SAAQC,OAAO,QAAO,YAAY;AAElC,OAAM,SAAUC,GAAGA,CAACC,IAAoD;EAEtE,MAAM;IAACC,MAAM;IAAEC;EAAO,CAAC,GAAGF,IAAI;EAC9B,MAAM;IAACG;EAAK,CAAC,GAAGF,MAAM;EAEtB,OAAOH,OAAO,CAACK,KAAK,EAAE,KAAK,CAAC,eAAeD,OAAO,CAAC;AACrD;AAEA,OAAO,MAAME,SAAS,GAAiB;EACrCC,UAAU,EAAER,GAAG;EACfS,WAAW,EAAE,OAAO;EACpBC,UAAU,EAAER;CACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}