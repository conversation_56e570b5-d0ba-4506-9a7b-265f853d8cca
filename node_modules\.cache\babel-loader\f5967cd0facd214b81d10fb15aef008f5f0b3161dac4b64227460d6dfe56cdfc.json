{"ast": null, "code": "/**\n * @license\n * Copyright 2018 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { ENGINE } from '../engine';\nimport { LinSpace } from '../kernel_names';\n/**\n * Return an evenly spaced sequence of numbers over the given interval.\n *\n * ```js\n * tf.linspace(0, 9, 10).print();\n * ```\n * @param start The start value of the sequence.\n * @param stop The end value of the sequence.\n * @param num The number of values to generate.\n *\n * @doc {heading: 'Tensors', subheading: 'Creation'}\n */\nexport function linspace(start, stop, num) {\n  if (num <= 0) {\n    throw new Error('The number of values should be positive.');\n  }\n  const attrs = {\n    start,\n    stop,\n    num\n  };\n  return ENGINE.runKernel(LinSpace, {}, attrs);\n}", "map": {"version": 3, "names": ["ENGINE", "LinSpace", "linspace", "start", "stop", "num", "Error", "attrs", "runKernel"], "sources": ["C:\\tfjs-core\\src\\ops\\linspace.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2018 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {ENGINE} from '../engine';\nimport {LinSpace, LinSpaceAttrs} from '../kernel_names';\nimport {NamedAttrMap} from '../kernel_registry';\nimport {Tensor1D} from '../tensor';\n\n/**\n * Return an evenly spaced sequence of numbers over the given interval.\n *\n * ```js\n * tf.linspace(0, 9, 10).print();\n * ```\n * @param start The start value of the sequence.\n * @param stop The end value of the sequence.\n * @param num The number of values to generate.\n *\n * @doc {heading: 'Tensors', subheading: 'Creation'}\n */\nexport function linspace(start: number, stop: number, num: number): Tensor1D {\n  if (num <= 0) {\n    throw new Error('The number of values should be positive.');\n  }\n\n  const attrs: LinSpaceAttrs = {start, stop, num};\n  return ENGINE.runKernel(LinSpace, {}, attrs as unknown as NamedAttrMap);\n}\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAiBA,SAAQA,MAAM,QAAO,WAAW;AAChC,SAAQC,QAAQ,QAAsB,iBAAiB;AAIvD;;;;;;;;;;;;AAYA,OAAM,SAAUC,QAAQA,CAACC,KAAa,EAAEC,IAAY,EAAEC,GAAW;EAC/D,IAAIA,GAAG,IAAI,CAAC,EAAE;IACZ,MAAM,IAAIC,KAAK,CAAC,0CAA0C,CAAC;;EAG7D,MAAMC,KAAK,GAAkB;IAACJ,KAAK;IAAEC,IAAI;IAAEC;EAAG,CAAC;EAC/C,OAAOL,MAAM,CAACQ,SAAS,CAACP,QAAQ,EAAE,EAAE,EAAEM,KAAgC,CAAC;AACzE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}