{"ast": null, "code": "/**\n * @license\n * Copyright 2017 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nexport class ResizeBilinearProgram {\n  constructor(inputShape, newHeight, newWidth, alignCorners, halfPixelCenters) {\n    this.variableNames = ['A'];\n    this.outputShape = [];\n    const [batch, oldHeight, oldWidth, depth] = inputShape;\n    this.outputShape = [batch, newHeight, newWidth, depth];\n    const effectiveInSize = [alignCorners && newHeight > 1 ? oldHeight - 1 : oldHeight, alignCorners && newWidth > 1 ? oldWidth - 1 : oldWidth];\n    const effectiveOutSize = [alignCorners && newHeight > 1 ? newHeight - 1 : newHeight, alignCorners && newWidth > 1 ? newWidth - 1 : newWidth];\n    let sourceFracIndexRC;\n    if (halfPixelCenters) {\n      sourceFracIndexRC = \"(vec2(yRC) + vec2(0.5)) * effectiveInputOverOutputRatioRC\" + \" - vec2(0.5)\";\n    } else {\n      sourceFracIndexRC = \"vec2(yRC) * effectiveInputOverOutputRatioRC\";\n    }\n    this.userCode = \"\\n      const vec2 effectiveInputOverOutputRatioRC = vec2(\\n          \".concat(effectiveInSize[0] / effectiveOutSize[0], \",\\n          \").concat(effectiveInSize[1] / effectiveOutSize[1], \");\\n      const vec2 inputShapeRC = vec2(\").concat(oldHeight, \".0, \").concat(oldWidth, \".0);\\n\\n      void main() {\\n        ivec4 coords = getOutputCoords();\\n        int b = coords[0];\\n        int d = coords[3];\\n        ivec2 yRC = coords.yz;\\n\\n        // Fractional source index.\\n        vec2 sourceFracIndexRC = \").concat(sourceFracIndexRC, \";\\n\\n        // Compute the four integer indices.\\n        ivec2 sourceFloorRC = ivec2(max(sourceFracIndexRC, vec2(0.0)));\\n        ivec2 sourceCeilRC = ivec2(\\n          min(inputShapeRC - 1.0, ceil(sourceFracIndexRC)));\\n\\n        float topLeft = getA(b, sourceFloorRC.x, sourceFloorRC.y, d);\\n        float bottomLeft = getA(b, sourceCeilRC.x, sourceFloorRC.y, d);\\n        float topRight = getA(b, sourceFloorRC.x, sourceCeilRC.y, d);\\n        float bottomRight = getA(b, sourceCeilRC.x, sourceCeilRC.y, d);\\n\\n        vec2 fracRC = sourceFracIndexRC - vec2(sourceFloorRC);\\n\\n        float top = topLeft + (topRight - topLeft) * fracRC.y;\\n        float bottom = bottomLeft + (bottomRight - bottomLeft) * fracRC.y;\\n        float newValue = top + (bottom - top) * fracRC.x;\\n\\n        setOutput(newValue);\\n      }\\n    \");\n  }\n}", "map": {"version": 3, "names": ["ResizeBilinearProgram", "constructor", "inputShape", "newHeight", "newWidth", "alignCorners", "halfPixelCenters", "variableNames", "outputShape", "batch", "oldHeight", "oldWidth", "depth", "effectiveInSize", "effectiveOutSize", "sourceFracIndexRC", "userCode", "concat"], "sources": ["C:\\tfjs-backend-webgl\\src\\resize_bilinear_gpu.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2017 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {GPGPUProgram} from './gpgpu_math';\n\nexport class ResizeBilinearProgram implements GPGPUProgram {\n  variableNames = ['A'];\n  outputShape: number[] = [];\n  userCode: string;\n\n  constructor(\n      inputShape: [number, number, number, number], newHeight: number,\n      newWidth: number, alignCorners: boolean, halfPixelCenters: boolean) {\n    const [batch, oldHeight, oldWidth, depth] = inputShape;\n    this.outputShape = [batch, newHeight, newWidth, depth];\n\n    const effectiveInSize: [number, number] = [\n      (alignCorners && newHeight > 1) ? oldHeight - 1 : oldHeight,\n      (alignCorners && newWidth > 1) ? oldWidth - 1 : oldWidth\n    ];\n\n    const effectiveOutSize: [number, number] = [\n      (alignCorners && newHeight > 1) ? newHeight - 1 : newHeight,\n      (alignCorners && newWidth > 1) ? newWidth - 1 : newWidth\n    ];\n\n    let sourceFracIndexRC: string;\n    if (halfPixelCenters) {\n      sourceFracIndexRC =\n          `(vec2(yRC) + vec2(0.5)) * effectiveInputOverOutputRatioRC` +\n          ` - vec2(0.5)`;\n    } else {\n      sourceFracIndexRC = `vec2(yRC) * effectiveInputOverOutputRatioRC`;\n    }\n\n    this.userCode = `\n      const vec2 effectiveInputOverOutputRatioRC = vec2(\n          ${effectiveInSize[0] / effectiveOutSize[0]},\n          ${effectiveInSize[1] / effectiveOutSize[1]});\n      const vec2 inputShapeRC = vec2(${oldHeight}.0, ${oldWidth}.0);\n\n      void main() {\n        ivec4 coords = getOutputCoords();\n        int b = coords[0];\n        int d = coords[3];\n        ivec2 yRC = coords.yz;\n\n        // Fractional source index.\n        vec2 sourceFracIndexRC = ${sourceFracIndexRC};\n\n        // Compute the four integer indices.\n        ivec2 sourceFloorRC = ivec2(max(sourceFracIndexRC, vec2(0.0)));\n        ivec2 sourceCeilRC = ivec2(\n          min(inputShapeRC - 1.0, ceil(sourceFracIndexRC)));\n\n        float topLeft = getA(b, sourceFloorRC.x, sourceFloorRC.y, d);\n        float bottomLeft = getA(b, sourceCeilRC.x, sourceFloorRC.y, d);\n        float topRight = getA(b, sourceFloorRC.x, sourceCeilRC.y, d);\n        float bottomRight = getA(b, sourceCeilRC.x, sourceCeilRC.y, d);\n\n        vec2 fracRC = sourceFracIndexRC - vec2(sourceFloorRC);\n\n        float top = topLeft + (topRight - topLeft) * fracRC.y;\n        float bottom = bottomLeft + (bottomRight - bottomLeft) * fracRC.y;\n        float newValue = top + (bottom - top) * fracRC.x;\n\n        setOutput(newValue);\n      }\n    `;\n  }\n}\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAmBA,OAAM,MAAOA,qBAAqB;EAKhCC,YACIC,UAA4C,EAAEC,SAAiB,EAC/DC,QAAgB,EAAEC,YAAqB,EAAEC,gBAAyB;IANtE,KAAAC,aAAa,GAAG,CAAC,GAAG,CAAC;IACrB,KAAAC,WAAW,GAAa,EAAE;IAMxB,MAAM,CAACC,KAAK,EAAEC,SAAS,EAAEC,QAAQ,EAAEC,KAAK,CAAC,GAAGV,UAAU;IACtD,IAAI,CAACM,WAAW,GAAG,CAACC,KAAK,EAAEN,SAAS,EAAEC,QAAQ,EAAEQ,KAAK,CAAC;IAEtD,MAAMC,eAAe,GAAqB,CACvCR,YAAY,IAAIF,SAAS,GAAG,CAAC,GAAIO,SAAS,GAAG,CAAC,GAAGA,SAAS,EAC1DL,YAAY,IAAID,QAAQ,GAAG,CAAC,GAAIO,QAAQ,GAAG,CAAC,GAAGA,QAAQ,CACzD;IAED,MAAMG,gBAAgB,GAAqB,CACxCT,YAAY,IAAIF,SAAS,GAAG,CAAC,GAAIA,SAAS,GAAG,CAAC,GAAGA,SAAS,EAC1DE,YAAY,IAAID,QAAQ,GAAG,CAAC,GAAIA,QAAQ,GAAG,CAAC,GAAGA,QAAQ,CACzD;IAED,IAAIW,iBAAyB;IAC7B,IAAIT,gBAAgB,EAAE;MACpBS,iBAAiB,GACb,4EACc;KACnB,MAAM;MACLA,iBAAiB,gDAAgD;;IAGnE,IAAI,CAACC,QAAQ,4EAAAC,MAAA,CAELJ,eAAe,CAAC,CAAC,CAAC,GAAGC,gBAAgB,CAAC,CAAC,CAAC,mBAAAG,MAAA,CACxCJ,eAAe,CAAC,CAAC,CAAC,GAAGC,gBAAgB,CAAC,CAAC,CAAC,+CAAAG,MAAA,CACbP,SAAS,UAAAO,MAAA,CAAON,QAAQ,8OAAAM,MAAA,CAS5BF,iBAAiB,8zBAoB/C;EACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}