{"ast": null, "code": "/**\n * @license\n * Copyright 2023 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nexport const json = [{\n  'tfOpName': 'EmptyTensorList',\n  'category': 'control',\n  'inputs': [{\n    'start': 0,\n    'name': 'elementShape',\n    'type': 'shape'\n  }, {\n    'start': 1,\n    'name': 'maxNumElements',\n    'type': 'number'\n  }],\n  'attrs': [{\n    'tfName': 'element_dtype',\n    'name': 'elementDType',\n    'type': 'dtype'\n  }]\n}, {\n  'tfOpName': 'LoopCond',\n  'category': 'control',\n  'inputs': [{\n    'start': 0,\n    'name': 'pred',\n    'type': 'tensor'\n  }]\n}, {\n  'tfOpName': 'Switch',\n  'category': 'control',\n  'inputs': [{\n    'start': 0,\n    'name': 'data',\n    'type': 'tensor'\n  }, {\n    'start': 1,\n    'name': 'pred',\n    'type': 'tensor'\n  }]\n}, {\n  'tfOpName': 'Merge',\n  'category': 'control',\n  'inputs': [{\n    'start': 0,\n    'end': 0,\n    'name': 'tensors',\n    'type': 'tensors'\n  }]\n}, {\n  'tfOpName': 'Enter',\n  'category': 'control',\n  'inputs': [{\n    'start': 0,\n    'name': 'tensor',\n    'type': 'tensor'\n  }],\n  'attrs': [{\n    'tfName': 'T',\n    'name': 'dtype',\n    'type': 'dtype',\n    'notSupported': true\n  }, {\n    'tfName': 'frame_name',\n    'name': 'frameName',\n    'type': 'string'\n  }, {\n    'tfName': 'is_constant',\n    'name': 'isConstant',\n    'type': 'bool'\n  }]\n}, {\n  'tfOpName': 'Exit',\n  'category': 'control',\n  'inputs': [{\n    'start': 0,\n    'name': 'tensor',\n    'type': 'tensor'\n  }],\n  'attrs': [{\n    'tfName': 'T',\n    'name': 'dtype',\n    'type': 'dtype',\n    'notSupported': true\n  }]\n}, {\n  'tfOpName': 'NextIteration',\n  'category': 'control',\n  'inputs': [{\n    'start': 0,\n    'name': 'tensor',\n    'type': 'tensor'\n  }],\n  'attrs': [{\n    'tfName': 'T',\n    'name': 'dtype',\n    'type': 'dtype',\n    'notSupported': true\n  }]\n}, {\n  'tfOpName': 'TensorArrayV3',\n  'category': 'control',\n  'inputs': [{\n    'start': 0,\n    'name': 'size',\n    'type': 'number'\n  }],\n  'attrs': [{\n    'tfName': 'dtype',\n    'name': 'dtype',\n    'type': 'dtype'\n  }, {\n    'tfName': 'element_shape',\n    'name': 'elementShape',\n    'type': 'shape'\n  }, {\n    'tfName': 'dynamic_size',\n    'name': 'dynamicSize',\n    'type': 'bool'\n  }, {\n    'tfName': 'clear_after_read',\n    'name': 'clearAfterRead',\n    'type': 'bool'\n  }, {\n    'tfName': 'identical_element_shapes',\n    'name': 'identicalElementShapes',\n    'type': 'bool'\n  }, {\n    'tfName': 'tensor_array_name',\n    'name': 'name',\n    'type': 'string'\n  }]\n}, {\n  'tfOpName': 'TensorArrayWriteV3',\n  'category': 'control',\n  'inputs': [{\n    'start': 0,\n    'name': 'tensorArrayId',\n    'type': 'tensor'\n  }, {\n    'start': 1,\n    'name': 'index',\n    'type': 'number'\n  }, {\n    'start': 2,\n    'name': 'tensor',\n    'type': 'tensor'\n  }, {\n    'start': 3,\n    'name': 'flowIn',\n    'type': 'number'\n  }],\n  'attrs': [{\n    'tfName': 'T',\n    'name': 'dtype',\n    'type': 'dtype',\n    'notSupported': true\n  }]\n}, {\n  'tfOpName': 'TensorArrayReadV3',\n  'category': 'control',\n  'inputs': [{\n    'start': 0,\n    'name': 'tensorArrayId',\n    'type': 'tensor'\n  }, {\n    'start': 1,\n    'name': 'index',\n    'type': 'number'\n  }, {\n    'start': 2,\n    'name': 'flowIn',\n    'type': 'number'\n  }],\n  'attrs': [{\n    'tfName': 'dtype',\n    'name': 'dtype',\n    'type': 'dtype',\n    'notSupported': true\n  }]\n}, {\n  'tfOpName': 'TensorArrayGatherV3',\n  'category': 'control',\n  'inputs': [{\n    'start': 0,\n    'name': 'tensorArrayId',\n    'type': 'tensor'\n  }, {\n    'start': 1,\n    'name': 'indices',\n    'type': 'number[]'\n  }, {\n    'start': 2,\n    'name': 'flowIn',\n    'type': 'number'\n  }],\n  'attrs': [{\n    'tfName': 'dtype',\n    'name': 'dtype',\n    'type': 'dtype'\n  }, {\n    'tfName': 'element_shape',\n    'name': 'elementShape',\n    'type': 'shape'\n  }]\n}, {\n  'tfOpName': 'TensorArrayScatterV3',\n  'category': 'control',\n  'inputs': [{\n    'start': 0,\n    'name': 'tensorArrayId',\n    'type': 'tensor'\n  }, {\n    'start': 1,\n    'name': 'indices',\n    'type': 'number[]'\n  }, {\n    'start': 2,\n    'name': 'tensor',\n    'type': 'tensor'\n  }, {\n    'start': 3,\n    'name': 'flowIn',\n    'type': 'number'\n  }],\n  'attrs': [{\n    'tfName': 'T',\n    'name': 'dtype',\n    'type': 'dtype'\n  }]\n}, {\n  'tfOpName': 'TensorArrayConcatV3',\n  'category': 'control',\n  'inputs': [{\n    'start': 0,\n    'name': 'tensorArrayId',\n    'type': 'tensor'\n  }, {\n    'start': 1,\n    'name': 'flowIn',\n    'type': 'number'\n  }],\n  'attrs': [{\n    'tfName': 'dtype',\n    'name': 'dtype',\n    'type': 'dtype'\n  }, {\n    'tfName': 'element_shape_except0',\n    'name': 'elementShapeExcept0',\n    'type': 'shape',\n    'notSupported': true\n  }]\n}, {\n  'tfOpName': 'TensorArraySplitV3',\n  'category': 'control',\n  'inputs': [{\n    'start': 0,\n    'name': 'tensorArrayId',\n    'type': 'tensor'\n  }, {\n    'start': 1,\n    'name': 'tensor',\n    'type': 'tensor'\n  }, {\n    'start': 2,\n    'name': 'lengths',\n    'type': 'number[]'\n  }, {\n    'start': 3,\n    'name': 'flowIn',\n    'type': 'number'\n  }],\n  'attrs': [{\n    'tfName': 'T',\n    'name': 'dtype',\n    'type': 'dtype'\n  }]\n}, {\n  'tfOpName': 'TensorArraySizeV3',\n  'category': 'control',\n  'inputs': [{\n    'start': 0,\n    'name': 'tensorArrayId',\n    'type': 'tensor'\n  }, {\n    'start': 1,\n    'name': 'flowIn',\n    'type': 'number'\n  }]\n}, {\n  'tfOpName': 'TensorArrayCloseV3',\n  'category': 'control',\n  'inputs': [{\n    'start': 0,\n    'name': 'tensorArrayId',\n    'type': 'tensor'\n  }]\n}, {\n  'tfOpName': 'StatelessIf',\n  'category': 'control',\n  'inputs': [{\n    'start': 0,\n    'name': 'cond',\n    'type': 'tensor'\n  }, {\n    'start': 1,\n    'end': 0,\n    'name': 'args',\n    'type': 'tensors'\n  }],\n  'attrs': [{\n    'tfName': 'then_branch',\n    'name': 'thenBranch',\n    'type': 'func'\n  }, {\n    'tfName': 'else_branch',\n    'name': 'elseBranch',\n    'type': 'func'\n  }]\n}, {\n  'tfOpName': 'If',\n  'category': 'control',\n  'inputs': [{\n    'start': 0,\n    'name': 'cond',\n    'type': 'tensor'\n  }, {\n    'start': 1,\n    'end': 0,\n    'name': 'args',\n    'type': 'tensors'\n  }],\n  'attrs': [{\n    'tfName': 'then_branch',\n    'name': 'thenBranch',\n    'type': 'func'\n  }, {\n    'tfName': 'else_branch',\n    'name': 'elseBranch',\n    'type': 'func'\n  }]\n}, {\n  'tfOpName': 'StatelessWhile',\n  'category': 'control',\n  'inputs': [{\n    'start': 0,\n    'end': 0,\n    'name': 'args',\n    'type': 'tensors'\n  }],\n  'attrs': [{\n    'tfName': 'cond',\n    'name': 'cond',\n    'type': 'func'\n  }, {\n    'tfName': 'body',\n    'name': 'body',\n    'type': 'func'\n  }]\n}, {\n  'tfOpName': 'While',\n  'category': 'control',\n  'inputs': [{\n    'start': 0,\n    'end': 0,\n    'name': 'args',\n    'type': 'tensors'\n  }],\n  'attrs': [{\n    'tfName': 'cond',\n    'name': 'cond',\n    'type': 'func'\n  }, {\n    'tfName': 'body',\n    'name': 'body',\n    'type': 'func'\n  }]\n}, {\n  'tfOpName': 'TensorListScatter',\n  'category': 'control',\n  'inputs': [{\n    'start': 0,\n    'name': 'tensor',\n    'type': 'tensor'\n  }, {\n    'start': 1,\n    'name': 'indices',\n    'type': 'number[]'\n  }, {\n    'start': 2,\n    'name': 'elementShape',\n    'type': 'shape'\n  }],\n  'attrs': [{\n    'tfName': 'element_dtype',\n    'name': 'elementDType',\n    'type': 'dtype'\n  }]\n}, {\n  'tfOpName': 'TensorListScatterV2',\n  'category': 'control',\n  'inputs': [{\n    'start': 0,\n    'name': 'tensor',\n    'type': 'tensor'\n  }, {\n    'start': 1,\n    'name': 'indices',\n    'type': 'number[]'\n  }, {\n    'start': 2,\n    'name': 'elementShape',\n    'type': 'shape'\n  }, {\n    'start': 3,\n    'name': 'numElements',\n    'type': 'number'\n  }],\n  'attrs': [{\n    'tfName': 'element_dtype',\n    'name': 'elementDType',\n    'type': 'dtype'\n  }]\n}, {\n  'tfOpName': 'TensorListGather',\n  'category': 'control',\n  'inputs': [{\n    'start': 0,\n    'name': 'tensorListId',\n    'type': 'tensor'\n  }, {\n    'start': 1,\n    'name': 'indices',\n    'type': 'number[]'\n  }, {\n    'start': 2,\n    'name': 'elementShape',\n    'type': 'shape'\n  }],\n  'attrs': [{\n    'tfName': 'element_dtype',\n    'name': 'elementDType',\n    'type': 'dtype'\n  }]\n}, {\n  'tfOpName': 'TensorListGetItem',\n  'category': 'control',\n  'inputs': [{\n    'start': 0,\n    'name': 'tensorListId',\n    'type': 'tensor'\n  }, {\n    'start': 1,\n    'name': 'index',\n    'type': 'number'\n  }, {\n    'start': 2,\n    'name': 'elementShape',\n    'type': 'shape'\n  }],\n  'attrs': [{\n    'tfName': 'element_dtype',\n    'name': 'elementDType',\n    'type': 'dtype'\n  }]\n}, {\n  'tfOpName': 'TensorListSetItem',\n  'category': 'control',\n  'inputs': [{\n    'start': 0,\n    'name': 'tensorListId',\n    'type': 'tensor'\n  }, {\n    'start': 1,\n    'name': 'index',\n    'type': 'number'\n  }, {\n    'start': 2,\n    'name': 'tensor',\n    'type': 'tensor'\n  }],\n  'attrs': [{\n    'tfName': 'element_dtype',\n    'name': 'elementDType',\n    'type': 'dtype'\n  }]\n}, {\n  'tfOpName': 'TensorListReserve',\n  'category': 'control',\n  'inputs': [{\n    'start': 0,\n    'name': 'elementShape',\n    'type': 'shape'\n  }, {\n    'start': 1,\n    'name': 'numElements',\n    'type': 'number'\n  }],\n  'attrs': [{\n    'tfName': 'element_dtype',\n    'name': 'elementDType',\n    'type': 'dtype'\n  }]\n}, {\n  'tfOpName': 'TensorListFromTensor',\n  'category': 'control',\n  'inputs': [{\n    'start': 0,\n    'name': 'tensor',\n    'type': 'tensor'\n  }, {\n    'start': 1,\n    'name': 'elementShape',\n    'type': 'shape'\n  }],\n  'attrs': [{\n    'tfName': 'element_dtype',\n    'name': 'elementDType',\n    'type': 'dtype'\n  }]\n}, {\n  'tfOpName': 'TensorListStack',\n  'category': 'control',\n  'inputs': [{\n    'start': 0,\n    'name': 'tensorListId',\n    'type': 'tensor'\n  }, {\n    'start': 1,\n    'name': 'elementShape',\n    'type': 'shape'\n  }],\n  'attrs': [{\n    'tfName': 'element_dtype',\n    'name': 'elementDType',\n    'type': 'dtype'\n  }, {\n    'tfName': 'num_elements',\n    'name': 'numElements',\n    'type': 'dtype'\n  }]\n}, {\n  'tfOpName': 'TensorListSplit',\n  'category': 'control',\n  'inputs': [{\n    'start': 0,\n    'name': 'tensor',\n    'type': 'tensor'\n  }, {\n    'start': 1,\n    'name': 'elementShape',\n    'type': 'shape'\n  }, {\n    'start': 2,\n    'name': 'lengths',\n    'type': 'number[]'\n  }],\n  'attrs': [{\n    'tfName': 'element_dtype',\n    'name': 'elementDType',\n    'type': 'dtype'\n  }]\n}, {\n  'tfOpName': 'TensorListConcat',\n  'category': 'control',\n  'inputs': [{\n    'start': 0,\n    'name': 'tensorListId',\n    'type': 'tensor'\n  }],\n  'attrs': [{\n    'tfName': 'element_shape',\n    'name': 'elementShape',\n    'type': 'shape'\n  }, {\n    'tfName': 'element_dtype',\n    'name': 'elementDType',\n    'type': 'dtype'\n  }]\n}, {\n  'tfOpName': 'TensorListConcatV2',\n  'category': 'control',\n  'inputs': [{\n    'start': 0,\n    'name': 'tensorListId',\n    'type': 'tensor'\n  }],\n  'attrs': [{\n    'tfName': 'element_shape',\n    'name': 'elementShape',\n    'type': 'shape'\n  }, {\n    'tfName': 'element_dtype',\n    'name': 'elementDType',\n    'type': 'dtype'\n  }]\n}, {\n  'tfOpName': 'TensorListPopBack',\n  'category': 'control',\n  'inputs': [{\n    'start': 0,\n    'name': 'tensorListId',\n    'type': 'tensor'\n  }, {\n    'start': 1,\n    'name': 'elementShape',\n    'type': 'shape'\n  }],\n  'attrs': [{\n    'tfName': 'element_dtype',\n    'name': 'elementDType',\n    'type': 'dtype'\n  }]\n}, {\n  'tfOpName': 'TensorListPushBack',\n  'category': 'control',\n  'inputs': [{\n    'start': 0,\n    'name': 'tensorListId',\n    'type': 'tensor'\n  }, {\n    'start': 1,\n    'name': 'tensor',\n    'type': 'tensor'\n  }],\n  'attrs': [{\n    'tfName': 'element_dtype',\n    'name': 'elementDType',\n    'type': 'dtype'\n  }]\n}, {\n  'tfOpName': 'TensorListLength',\n  'category': 'control',\n  'inputs': [{\n    'start': 0,\n    'name': 'tensorListId',\n    'type': 'tensor'\n  }]\n}, {\n  'tfOpName': 'TensorListResize',\n  'category': 'control',\n  'inputs': [{\n    'start': 0,\n    'name': 'tensorListId',\n    'type': 'tensor'\n  }, {\n    'start': 1,\n    'name': 'size',\n    'type': 'number'\n  }]\n}];", "map": {"version": 3, "names": ["json"], "sources": ["C:\\tfjs-converter\\src\\operations\\op_list\\control.ts"], "sourcesContent": ["\n/**\n * @license\n * Copyright 2023 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {OpMapper} from '../types';\n\nexport const json: OpMapper[] = [\n  {\n    'tfOpName': 'EmptyTensorList',\n    'category': 'control',\n    'inputs': [\n      {\n        'start': 0,\n        'name': 'elementShape',\n        'type': 'shape'\n      },\n      {\n        'start': 1,\n        'name': 'maxNumElements',\n        'type': 'number'\n      }\n    ],\n    'attrs': [\n      {\n        'tfName': 'element_dtype',\n        'name': 'elementDType',\n        'type': 'dtype'\n      }\n    ]\n  },\n  {\n    'tfOpName': 'LoopCond',\n    'category': 'control',\n    'inputs': [\n      {\n        'start': 0,\n        'name': 'pred',\n        'type': 'tensor'\n      }\n    ]\n  },\n  {\n    'tfOpName': 'Switch',\n    'category': 'control',\n    'inputs': [\n      {\n        'start': 0,\n        'name': 'data',\n        'type': 'tensor'\n      },\n      {\n        'start': 1,\n        'name': 'pred',\n        'type': 'tensor'\n      }\n    ]\n  },\n  {\n    'tfOpName': 'Merge',\n    'category': 'control',\n    'inputs': [\n      {\n        'start': 0,\n        'end': 0,\n        'name': 'tensors',\n        'type': 'tensors'\n      }\n    ]\n  },\n  {\n    'tfOpName': 'Enter',\n    'category': 'control',\n    'inputs': [\n      {\n        'start': 0,\n        'name': 'tensor',\n        'type': 'tensor'\n      }\n    ],\n    'attrs': [\n      {\n        'tfName': 'T',\n        'name': 'dtype',\n        'type': 'dtype',\n        'notSupported': true\n      },\n      {\n        'tfName': 'frame_name',\n        'name': 'frameName',\n        'type': 'string'\n      },\n      {\n        'tfName': 'is_constant',\n        'name': 'isConstant',\n        'type': 'bool'\n      }\n    ]\n  },\n  {\n    'tfOpName': 'Exit',\n    'category': 'control',\n    'inputs': [\n      {\n        'start': 0,\n        'name': 'tensor',\n        'type': 'tensor'\n      }\n    ],\n    'attrs': [\n      {\n        'tfName': 'T',\n        'name': 'dtype',\n        'type': 'dtype',\n        'notSupported': true\n      }\n    ]\n  },\n  {\n    'tfOpName': 'NextIteration',\n    'category': 'control',\n    'inputs': [\n      {\n        'start': 0,\n        'name': 'tensor',\n        'type': 'tensor'\n      }\n    ],\n    'attrs': [\n      {\n        'tfName': 'T',\n        'name': 'dtype',\n        'type': 'dtype',\n        'notSupported': true\n      }\n    ]\n  },\n  {\n    'tfOpName': 'TensorArrayV3',\n    'category': 'control',\n    'inputs': [\n      {\n        'start': 0,\n        'name': 'size',\n        'type': 'number'\n      }\n    ],\n    'attrs': [\n      {\n        'tfName': 'dtype',\n        'name': 'dtype',\n        'type': 'dtype'\n      },\n      {\n        'tfName': 'element_shape',\n        'name': 'elementShape',\n        'type': 'shape'\n      },\n      {\n        'tfName': 'dynamic_size',\n        'name': 'dynamicSize',\n        'type': 'bool'\n      },\n      {\n        'tfName': 'clear_after_read',\n        'name': 'clearAfterRead',\n        'type': 'bool'\n      },\n      {\n        'tfName': 'identical_element_shapes',\n        'name': 'identicalElementShapes',\n        'type': 'bool'\n      },\n      {\n        'tfName': 'tensor_array_name',\n        'name': 'name',\n        'type': 'string'\n      }\n    ]\n  },\n  {\n    'tfOpName': 'TensorArrayWriteV3',\n    'category': 'control',\n    'inputs': [\n      {\n        'start': 0,\n        'name': 'tensorArrayId',\n        'type': 'tensor'\n      },\n      {\n        'start': 1,\n        'name': 'index',\n        'type': 'number'\n      },\n      {\n        'start': 2,\n        'name': 'tensor',\n        'type': 'tensor'\n      },\n      {\n        'start': 3,\n        'name': 'flowIn',\n        'type': 'number'\n      }\n    ],\n    'attrs': [\n      {\n        'tfName': 'T',\n        'name': 'dtype',\n        'type': 'dtype',\n        'notSupported': true\n      }\n    ]\n  },\n  {\n    'tfOpName': 'TensorArrayReadV3',\n    'category': 'control',\n    'inputs': [\n      {\n        'start': 0,\n        'name': 'tensorArrayId',\n        'type': 'tensor'\n      },\n      {\n        'start': 1,\n        'name': 'index',\n        'type': 'number'\n      },\n      {\n        'start': 2,\n        'name': 'flowIn',\n        'type': 'number'\n      }\n    ],\n    'attrs': [\n      {\n        'tfName': 'dtype',\n        'name': 'dtype',\n        'type': 'dtype',\n        'notSupported': true\n      }\n    ]\n  },\n  {\n    'tfOpName': 'TensorArrayGatherV3',\n    'category': 'control',\n    'inputs': [\n      {\n        'start': 0,\n        'name': 'tensorArrayId',\n        'type': 'tensor'\n      },\n      {\n        'start': 1,\n        'name': 'indices',\n        'type': 'number[]'\n      },\n      {\n        'start': 2,\n        'name': 'flowIn',\n        'type': 'number'\n      }\n    ],\n    'attrs': [\n      {\n        'tfName': 'dtype',\n        'name': 'dtype',\n        'type': 'dtype'\n      },\n      {\n        'tfName': 'element_shape',\n        'name': 'elementShape',\n        'type': 'shape'\n      }\n    ]\n  },\n  {\n    'tfOpName': 'TensorArrayScatterV3',\n    'category': 'control',\n    'inputs': [\n      {\n        'start': 0,\n        'name': 'tensorArrayId',\n        'type': 'tensor'\n      },\n      {\n        'start': 1,\n        'name': 'indices',\n        'type': 'number[]'\n      },\n      {\n        'start': 2,\n        'name': 'tensor',\n        'type': 'tensor'\n      },\n      {\n        'start': 3,\n        'name': 'flowIn',\n        'type': 'number'\n      }\n    ],\n    'attrs': [\n      {\n        'tfName': 'T',\n        'name': 'dtype',\n        'type': 'dtype'\n      }\n    ]\n  },\n  {\n    'tfOpName': 'TensorArrayConcatV3',\n    'category': 'control',\n    'inputs': [\n      {\n        'start': 0,\n        'name': 'tensorArrayId',\n        'type': 'tensor'\n      },\n      {\n        'start': 1,\n        'name': 'flowIn',\n        'type': 'number'\n      }\n    ],\n    'attrs': [\n      {\n        'tfName': 'dtype',\n        'name': 'dtype',\n        'type': 'dtype'\n      },\n      {\n        'tfName': 'element_shape_except0',\n        'name': 'elementShapeExcept0',\n        'type': 'shape',\n        'notSupported': true\n      }\n    ]\n  },\n  {\n    'tfOpName': 'TensorArraySplitV3',\n    'category': 'control',\n    'inputs': [\n      {\n        'start': 0,\n        'name': 'tensorArrayId',\n        'type': 'tensor'\n      },\n      {\n        'start': 1,\n        'name': 'tensor',\n        'type': 'tensor'\n      },\n      {\n        'start': 2,\n        'name': 'lengths',\n        'type': 'number[]'\n      },\n      {\n        'start': 3,\n        'name': 'flowIn',\n        'type': 'number'\n      }\n    ],\n    'attrs': [\n      {\n        'tfName': 'T',\n        'name': 'dtype',\n        'type': 'dtype'\n      }\n    ]\n  },\n  {\n    'tfOpName': 'TensorArraySizeV3',\n    'category': 'control',\n    'inputs': [\n      {\n        'start': 0,\n        'name': 'tensorArrayId',\n        'type': 'tensor'\n      },\n      {\n        'start': 1,\n        'name': 'flowIn',\n        'type': 'number'\n      }\n    ]\n  },\n  {\n    'tfOpName': 'TensorArrayCloseV3',\n    'category': 'control',\n    'inputs': [\n      {\n        'start': 0,\n        'name': 'tensorArrayId',\n        'type': 'tensor'\n      }\n    ]\n  },\n  {\n    'tfOpName': 'StatelessIf',\n    'category': 'control',\n    'inputs': [\n      {\n        'start': 0,\n        'name': 'cond',\n        'type': 'tensor'\n      },\n      {\n        'start': 1,\n        'end': 0,\n        'name': 'args',\n        'type': 'tensors'\n      }\n    ],\n    'attrs': [\n      {\n        'tfName': 'then_branch',\n        'name': 'thenBranch',\n        'type': 'func'\n      },\n      {\n        'tfName': 'else_branch',\n        'name': 'elseBranch',\n        'type': 'func'\n      }\n    ]\n  },\n  {\n    'tfOpName': 'If',\n    'category': 'control',\n    'inputs': [\n      {\n        'start': 0,\n        'name': 'cond',\n        'type': 'tensor'\n      },\n      {\n        'start': 1,\n        'end': 0,\n        'name': 'args',\n        'type': 'tensors'\n      }\n    ],\n    'attrs': [\n      {\n        'tfName': 'then_branch',\n        'name': 'thenBranch',\n        'type': 'func'\n      },\n      {\n        'tfName': 'else_branch',\n        'name': 'elseBranch',\n        'type': 'func'\n      }\n    ]\n  },\n  {\n    'tfOpName': 'StatelessWhile',\n    'category': 'control',\n    'inputs': [\n      {\n        'start': 0,\n        'end': 0,\n        'name': 'args',\n        'type': 'tensors'\n      }\n    ],\n    'attrs': [\n      {\n        'tfName': 'cond',\n        'name': 'cond',\n        'type': 'func'\n      },\n      {\n        'tfName': 'body',\n        'name': 'body',\n        'type': 'func'\n      }\n    ]\n  },\n  {\n    'tfOpName': 'While',\n    'category': 'control',\n    'inputs': [\n      {\n        'start': 0,\n        'end': 0,\n        'name': 'args',\n        'type': 'tensors'\n      }\n    ],\n    'attrs': [\n      {\n        'tfName': 'cond',\n        'name': 'cond',\n        'type': 'func'\n      },\n      {\n        'tfName': 'body',\n        'name': 'body',\n        'type': 'func'\n      }\n    ]\n  },\n  {\n    'tfOpName': 'TensorListScatter',\n    'category': 'control',\n    'inputs': [\n      {\n        'start': 0,\n        'name': 'tensor',\n        'type': 'tensor'\n      },\n      {\n        'start': 1,\n        'name': 'indices',\n        'type': 'number[]'\n      },\n      {\n        'start': 2,\n        'name': 'elementShape',\n        'type': 'shape'\n      }\n    ],\n    'attrs': [\n      {\n        'tfName': 'element_dtype',\n        'name': 'elementDType',\n        'type': 'dtype'\n      }\n    ]\n  },\n  {\n    'tfOpName': 'TensorListScatterV2',\n    'category': 'control',\n    'inputs': [\n      {\n        'start': 0,\n        'name': 'tensor',\n        'type': 'tensor'\n      },\n      {\n        'start': 1,\n        'name': 'indices',\n        'type': 'number[]'\n      },\n      {\n        'start': 2,\n        'name': 'elementShape',\n        'type': 'shape'\n      },\n      {\n        'start': 3,\n        'name': 'numElements',\n        'type': 'number'\n      }\n    ],\n    'attrs': [\n      {\n        'tfName': 'element_dtype',\n        'name': 'elementDType',\n        'type': 'dtype'\n      }\n    ]\n  },\n  {\n    'tfOpName': 'TensorListGather',\n    'category': 'control',\n    'inputs': [\n      {\n        'start': 0,\n        'name': 'tensorListId',\n        'type': 'tensor'\n      },\n      {\n        'start': 1,\n        'name': 'indices',\n        'type': 'number[]'\n      },\n      {\n        'start': 2,\n        'name': 'elementShape',\n        'type': 'shape'\n      }\n    ],\n    'attrs': [\n      {\n        'tfName': 'element_dtype',\n        'name': 'elementDType',\n        'type': 'dtype'\n      }\n    ]\n  },\n  {\n    'tfOpName': 'TensorListGetItem',\n    'category': 'control',\n    'inputs': [\n      {\n        'start': 0,\n        'name': 'tensorListId',\n        'type': 'tensor'\n      },\n      {\n        'start': 1,\n        'name': 'index',\n        'type': 'number'\n      },\n      {\n        'start': 2,\n        'name': 'elementShape',\n        'type': 'shape'\n      }\n    ],\n    'attrs': [\n      {\n        'tfName': 'element_dtype',\n        'name': 'elementDType',\n        'type': 'dtype'\n      }\n    ]\n  },\n  {\n    'tfOpName': 'TensorListSetItem',\n    'category': 'control',\n    'inputs': [\n      {\n        'start': 0,\n        'name': 'tensorListId',\n        'type': 'tensor'\n      },\n      {\n        'start': 1,\n        'name': 'index',\n        'type': 'number'\n      },\n      {\n        'start': 2,\n        'name': 'tensor',\n        'type': 'tensor'\n      }\n    ],\n    'attrs': [\n      {\n        'tfName': 'element_dtype',\n        'name': 'elementDType',\n        'type': 'dtype'\n      }\n    ]\n  },\n  {\n    'tfOpName': 'TensorListReserve',\n    'category': 'control',\n    'inputs': [\n      {\n        'start': 0,\n        'name': 'elementShape',\n        'type': 'shape'\n      },\n      {\n        'start': 1,\n        'name': 'numElements',\n        'type': 'number'\n      }\n    ],\n    'attrs': [\n      {\n        'tfName': 'element_dtype',\n        'name': 'elementDType',\n        'type': 'dtype'\n      }\n    ]\n  },\n  {\n    'tfOpName': 'TensorListFromTensor',\n    'category': 'control',\n    'inputs': [\n      {\n        'start': 0,\n        'name': 'tensor',\n        'type': 'tensor'\n      },\n      {\n        'start': 1,\n        'name': 'elementShape',\n        'type': 'shape'\n      }\n    ],\n    'attrs': [\n      {\n        'tfName': 'element_dtype',\n        'name': 'elementDType',\n        'type': 'dtype'\n      }\n    ]\n  },\n  {\n    'tfOpName': 'TensorListStack',\n    'category': 'control',\n    'inputs': [\n      {\n        'start': 0,\n        'name': 'tensorListId',\n        'type': 'tensor'\n      },\n      {\n        'start': 1,\n        'name': 'elementShape',\n        'type': 'shape'\n      }\n    ],\n    'attrs': [\n      {\n        'tfName': 'element_dtype',\n        'name': 'elementDType',\n        'type': 'dtype'\n      },\n      {\n        'tfName': 'num_elements',\n        'name': 'numElements',\n        'type': 'dtype'\n      }\n    ]\n  },\n  {\n    'tfOpName': 'TensorListSplit',\n    'category': 'control',\n    'inputs': [\n      {\n        'start': 0,\n        'name': 'tensor',\n        'type': 'tensor'\n      },\n      {\n        'start': 1,\n        'name': 'elementShape',\n        'type': 'shape'\n      },\n      {\n        'start': 2,\n        'name': 'lengths',\n        'type': 'number[]'\n      }\n    ],\n    'attrs': [\n      {\n        'tfName': 'element_dtype',\n        'name': 'elementDType',\n        'type': 'dtype'\n      }\n    ]\n  },\n  {\n    'tfOpName': 'TensorListConcat',\n    'category': 'control',\n    'inputs': [\n      {\n        'start': 0,\n        'name': 'tensorListId',\n        'type': 'tensor'\n      }\n    ],\n    'attrs': [\n      {\n        'tfName': 'element_shape',\n        'name': 'elementShape',\n        'type': 'shape'\n      },\n      {\n        'tfName': 'element_dtype',\n        'name': 'elementDType',\n        'type': 'dtype'\n      }\n    ]\n  },\n  {\n    'tfOpName': 'TensorListConcatV2',\n    'category': 'control',\n    'inputs': [\n      {\n        'start': 0,\n        'name': 'tensorListId',\n        'type': 'tensor'\n      }\n    ],\n    'attrs': [\n      {\n        'tfName': 'element_shape',\n        'name': 'elementShape',\n        'type': 'shape'\n      },\n      {\n        'tfName': 'element_dtype',\n        'name': 'elementDType',\n        'type': 'dtype'\n      }\n    ]\n  },\n  {\n    'tfOpName': 'TensorListPopBack',\n    'category': 'control',\n    'inputs': [\n      {\n        'start': 0,\n        'name': 'tensorListId',\n        'type': 'tensor'\n      },\n      {\n        'start': 1,\n        'name': 'elementShape',\n        'type': 'shape'\n      }\n    ],\n    'attrs': [\n      {\n        'tfName': 'element_dtype',\n        'name': 'elementDType',\n        'type': 'dtype'\n      }\n    ]\n  },\n  {\n    'tfOpName': 'TensorListPushBack',\n    'category': 'control',\n    'inputs': [\n      {\n        'start': 0,\n        'name': 'tensorListId',\n        'type': 'tensor'\n      },\n      {\n        'start': 1,\n        'name': 'tensor',\n        'type': 'tensor'\n      }\n    ],\n    'attrs': [\n      {\n        'tfName': 'element_dtype',\n        'name': 'elementDType',\n        'type': 'dtype'\n      }\n    ]\n  },\n  {\n    'tfOpName': 'TensorListLength',\n    'category': 'control',\n    'inputs': [\n      {\n        'start': 0,\n        'name': 'tensorListId',\n        'type': 'tensor'\n      }\n    ]\n  },\n  {\n    'tfOpName': 'TensorListResize',\n    'category': 'control',\n    'inputs': [\n      {\n        'start': 0,\n        'name': 'tensorListId',\n        'type': 'tensor'\n      },\n      {\n        'start': 1,\n        'name': 'size',\n        'type': 'number'\n      }\n    ]\n  }\n]\n;\n"], "mappings": "AACA;;;;;;;;;;;;;;;;AAmBA,OAAO,MAAMA,IAAI,GAAe,CAC9B;EACE,UAAU,EAAE,iBAAiB;EAC7B,UAAU,EAAE,SAAS;EACrB,QAAQ,EAAE,CACR;IACE,OAAO,EAAE,CAAC;IACV,MAAM,EAAE,cAAc;IACtB,MAAM,EAAE;GACT,EACD;IACE,OAAO,EAAE,CAAC;IACV,MAAM,EAAE,gBAAgB;IACxB,MAAM,EAAE;GACT,CACF;EACD,OAAO,EAAE,CACP;IACE,QAAQ,EAAE,eAAe;IACzB,MAAM,EAAE,cAAc;IACtB,MAAM,EAAE;GACT;CAEJ,EACD;EACE,UAAU,EAAE,UAAU;EACtB,UAAU,EAAE,SAAS;EACrB,QAAQ,EAAE,CACR;IACE,OAAO,EAAE,CAAC;IACV,MAAM,EAAE,MAAM;IACd,MAAM,EAAE;GACT;CAEJ,EACD;EACE,UAAU,EAAE,QAAQ;EACpB,UAAU,EAAE,SAAS;EACrB,QAAQ,EAAE,CACR;IACE,OAAO,EAAE,CAAC;IACV,MAAM,EAAE,MAAM;IACd,MAAM,EAAE;GACT,EACD;IACE,OAAO,EAAE,CAAC;IACV,MAAM,EAAE,MAAM;IACd,MAAM,EAAE;GACT;CAEJ,EACD;EACE,UAAU,EAAE,OAAO;EACnB,UAAU,EAAE,SAAS;EACrB,QAAQ,EAAE,CACR;IACE,OAAO,EAAE,CAAC;IACV,KAAK,EAAE,CAAC;IACR,MAAM,EAAE,SAAS;IACjB,MAAM,EAAE;GACT;CAEJ,EACD;EACE,UAAU,EAAE,OAAO;EACnB,UAAU,EAAE,SAAS;EACrB,QAAQ,EAAE,CACR;IACE,OAAO,EAAE,CAAC;IACV,MAAM,EAAE,QAAQ;IAChB,MAAM,EAAE;GACT,CACF;EACD,OAAO,EAAE,CACP;IACE,QAAQ,EAAE,GAAG;IACb,MAAM,EAAE,OAAO;IACf,MAAM,EAAE,OAAO;IACf,cAAc,EAAE;GACjB,EACD;IACE,QAAQ,EAAE,YAAY;IACtB,MAAM,EAAE,WAAW;IACnB,MAAM,EAAE;GACT,EACD;IACE,QAAQ,EAAE,aAAa;IACvB,MAAM,EAAE,YAAY;IACpB,MAAM,EAAE;GACT;CAEJ,EACD;EACE,UAAU,EAAE,MAAM;EAClB,UAAU,EAAE,SAAS;EACrB,QAAQ,EAAE,CACR;IACE,OAAO,EAAE,CAAC;IACV,MAAM,EAAE,QAAQ;IAChB,MAAM,EAAE;GACT,CACF;EACD,OAAO,EAAE,CACP;IACE,QAAQ,EAAE,GAAG;IACb,MAAM,EAAE,OAAO;IACf,MAAM,EAAE,OAAO;IACf,cAAc,EAAE;GACjB;CAEJ,EACD;EACE,UAAU,EAAE,eAAe;EAC3B,UAAU,EAAE,SAAS;EACrB,QAAQ,EAAE,CACR;IACE,OAAO,EAAE,CAAC;IACV,MAAM,EAAE,QAAQ;IAChB,MAAM,EAAE;GACT,CACF;EACD,OAAO,EAAE,CACP;IACE,QAAQ,EAAE,GAAG;IACb,MAAM,EAAE,OAAO;IACf,MAAM,EAAE,OAAO;IACf,cAAc,EAAE;GACjB;CAEJ,EACD;EACE,UAAU,EAAE,eAAe;EAC3B,UAAU,EAAE,SAAS;EACrB,QAAQ,EAAE,CACR;IACE,OAAO,EAAE,CAAC;IACV,MAAM,EAAE,MAAM;IACd,MAAM,EAAE;GACT,CACF;EACD,OAAO,EAAE,CACP;IACE,QAAQ,EAAE,OAAO;IACjB,MAAM,EAAE,OAAO;IACf,MAAM,EAAE;GACT,EACD;IACE,QAAQ,EAAE,eAAe;IACzB,MAAM,EAAE,cAAc;IACtB,MAAM,EAAE;GACT,EACD;IACE,QAAQ,EAAE,cAAc;IACxB,MAAM,EAAE,aAAa;IACrB,MAAM,EAAE;GACT,EACD;IACE,QAAQ,EAAE,kBAAkB;IAC5B,MAAM,EAAE,gBAAgB;IACxB,MAAM,EAAE;GACT,EACD;IACE,QAAQ,EAAE,0BAA0B;IACpC,MAAM,EAAE,wBAAwB;IAChC,MAAM,EAAE;GACT,EACD;IACE,QAAQ,EAAE,mBAAmB;IAC7B,MAAM,EAAE,MAAM;IACd,MAAM,EAAE;GACT;CAEJ,EACD;EACE,UAAU,EAAE,oBAAoB;EAChC,UAAU,EAAE,SAAS;EACrB,QAAQ,EAAE,CACR;IACE,OAAO,EAAE,CAAC;IACV,MAAM,EAAE,eAAe;IACvB,MAAM,EAAE;GACT,EACD;IACE,OAAO,EAAE,CAAC;IACV,MAAM,EAAE,OAAO;IACf,MAAM,EAAE;GACT,EACD;IACE,OAAO,EAAE,CAAC;IACV,MAAM,EAAE,QAAQ;IAChB,MAAM,EAAE;GACT,EACD;IACE,OAAO,EAAE,CAAC;IACV,MAAM,EAAE,QAAQ;IAChB,MAAM,EAAE;GACT,CACF;EACD,OAAO,EAAE,CACP;IACE,QAAQ,EAAE,GAAG;IACb,MAAM,EAAE,OAAO;IACf,MAAM,EAAE,OAAO;IACf,cAAc,EAAE;GACjB;CAEJ,EACD;EACE,UAAU,EAAE,mBAAmB;EAC/B,UAAU,EAAE,SAAS;EACrB,QAAQ,EAAE,CACR;IACE,OAAO,EAAE,CAAC;IACV,MAAM,EAAE,eAAe;IACvB,MAAM,EAAE;GACT,EACD;IACE,OAAO,EAAE,CAAC;IACV,MAAM,EAAE,OAAO;IACf,MAAM,EAAE;GACT,EACD;IACE,OAAO,EAAE,CAAC;IACV,MAAM,EAAE,QAAQ;IAChB,MAAM,EAAE;GACT,CACF;EACD,OAAO,EAAE,CACP;IACE,QAAQ,EAAE,OAAO;IACjB,MAAM,EAAE,OAAO;IACf,MAAM,EAAE,OAAO;IACf,cAAc,EAAE;GACjB;CAEJ,EACD;EACE,UAAU,EAAE,qBAAqB;EACjC,UAAU,EAAE,SAAS;EACrB,QAAQ,EAAE,CACR;IACE,OAAO,EAAE,CAAC;IACV,MAAM,EAAE,eAAe;IACvB,MAAM,EAAE;GACT,EACD;IACE,OAAO,EAAE,CAAC;IACV,MAAM,EAAE,SAAS;IACjB,MAAM,EAAE;GACT,EACD;IACE,OAAO,EAAE,CAAC;IACV,MAAM,EAAE,QAAQ;IAChB,MAAM,EAAE;GACT,CACF;EACD,OAAO,EAAE,CACP;IACE,QAAQ,EAAE,OAAO;IACjB,MAAM,EAAE,OAAO;IACf,MAAM,EAAE;GACT,EACD;IACE,QAAQ,EAAE,eAAe;IACzB,MAAM,EAAE,cAAc;IACtB,MAAM,EAAE;GACT;CAEJ,EACD;EACE,UAAU,EAAE,sBAAsB;EAClC,UAAU,EAAE,SAAS;EACrB,QAAQ,EAAE,CACR;IACE,OAAO,EAAE,CAAC;IACV,MAAM,EAAE,eAAe;IACvB,MAAM,EAAE;GACT,EACD;IACE,OAAO,EAAE,CAAC;IACV,MAAM,EAAE,SAAS;IACjB,MAAM,EAAE;GACT,EACD;IACE,OAAO,EAAE,CAAC;IACV,MAAM,EAAE,QAAQ;IAChB,MAAM,EAAE;GACT,EACD;IACE,OAAO,EAAE,CAAC;IACV,MAAM,EAAE,QAAQ;IAChB,MAAM,EAAE;GACT,CACF;EACD,OAAO,EAAE,CACP;IACE,QAAQ,EAAE,GAAG;IACb,MAAM,EAAE,OAAO;IACf,MAAM,EAAE;GACT;CAEJ,EACD;EACE,UAAU,EAAE,qBAAqB;EACjC,UAAU,EAAE,SAAS;EACrB,QAAQ,EAAE,CACR;IACE,OAAO,EAAE,CAAC;IACV,MAAM,EAAE,eAAe;IACvB,MAAM,EAAE;GACT,EACD;IACE,OAAO,EAAE,CAAC;IACV,MAAM,EAAE,QAAQ;IAChB,MAAM,EAAE;GACT,CACF;EACD,OAAO,EAAE,CACP;IACE,QAAQ,EAAE,OAAO;IACjB,MAAM,EAAE,OAAO;IACf,MAAM,EAAE;GACT,EACD;IACE,QAAQ,EAAE,uBAAuB;IACjC,MAAM,EAAE,qBAAqB;IAC7B,MAAM,EAAE,OAAO;IACf,cAAc,EAAE;GACjB;CAEJ,EACD;EACE,UAAU,EAAE,oBAAoB;EAChC,UAAU,EAAE,SAAS;EACrB,QAAQ,EAAE,CACR;IACE,OAAO,EAAE,CAAC;IACV,MAAM,EAAE,eAAe;IACvB,MAAM,EAAE;GACT,EACD;IACE,OAAO,EAAE,CAAC;IACV,MAAM,EAAE,QAAQ;IAChB,MAAM,EAAE;GACT,EACD;IACE,OAAO,EAAE,CAAC;IACV,MAAM,EAAE,SAAS;IACjB,MAAM,EAAE;GACT,EACD;IACE,OAAO,EAAE,CAAC;IACV,MAAM,EAAE,QAAQ;IAChB,MAAM,EAAE;GACT,CACF;EACD,OAAO,EAAE,CACP;IACE,QAAQ,EAAE,GAAG;IACb,MAAM,EAAE,OAAO;IACf,MAAM,EAAE;GACT;CAEJ,EACD;EACE,UAAU,EAAE,mBAAmB;EAC/B,UAAU,EAAE,SAAS;EACrB,QAAQ,EAAE,CACR;IACE,OAAO,EAAE,CAAC;IACV,MAAM,EAAE,eAAe;IACvB,MAAM,EAAE;GACT,EACD;IACE,OAAO,EAAE,CAAC;IACV,MAAM,EAAE,QAAQ;IAChB,MAAM,EAAE;GACT;CAEJ,EACD;EACE,UAAU,EAAE,oBAAoB;EAChC,UAAU,EAAE,SAAS;EACrB,QAAQ,EAAE,CACR;IACE,OAAO,EAAE,CAAC;IACV,MAAM,EAAE,eAAe;IACvB,MAAM,EAAE;GACT;CAEJ,EACD;EACE,UAAU,EAAE,aAAa;EACzB,UAAU,EAAE,SAAS;EACrB,QAAQ,EAAE,CACR;IACE,OAAO,EAAE,CAAC;IACV,MAAM,EAAE,MAAM;IACd,MAAM,EAAE;GACT,EACD;IACE,OAAO,EAAE,CAAC;IACV,KAAK,EAAE,CAAC;IACR,MAAM,EAAE,MAAM;IACd,MAAM,EAAE;GACT,CACF;EACD,OAAO,EAAE,CACP;IACE,QAAQ,EAAE,aAAa;IACvB,MAAM,EAAE,YAAY;IACpB,MAAM,EAAE;GACT,EACD;IACE,QAAQ,EAAE,aAAa;IACvB,MAAM,EAAE,YAAY;IACpB,MAAM,EAAE;GACT;CAEJ,EACD;EACE,UAAU,EAAE,IAAI;EAChB,UAAU,EAAE,SAAS;EACrB,QAAQ,EAAE,CACR;IACE,OAAO,EAAE,CAAC;IACV,MAAM,EAAE,MAAM;IACd,MAAM,EAAE;GACT,EACD;IACE,OAAO,EAAE,CAAC;IACV,KAAK,EAAE,CAAC;IACR,MAAM,EAAE,MAAM;IACd,MAAM,EAAE;GACT,CACF;EACD,OAAO,EAAE,CACP;IACE,QAAQ,EAAE,aAAa;IACvB,MAAM,EAAE,YAAY;IACpB,MAAM,EAAE;GACT,EACD;IACE,QAAQ,EAAE,aAAa;IACvB,MAAM,EAAE,YAAY;IACpB,MAAM,EAAE;GACT;CAEJ,EACD;EACE,UAAU,EAAE,gBAAgB;EAC5B,UAAU,EAAE,SAAS;EACrB,QAAQ,EAAE,CACR;IACE,OAAO,EAAE,CAAC;IACV,KAAK,EAAE,CAAC;IACR,MAAM,EAAE,MAAM;IACd,MAAM,EAAE;GACT,CACF;EACD,OAAO,EAAE,CACP;IACE,QAAQ,EAAE,MAAM;IAChB,MAAM,EAAE,MAAM;IACd,MAAM,EAAE;GACT,EACD;IACE,QAAQ,EAAE,MAAM;IAChB,MAAM,EAAE,MAAM;IACd,MAAM,EAAE;GACT;CAEJ,EACD;EACE,UAAU,EAAE,OAAO;EACnB,UAAU,EAAE,SAAS;EACrB,QAAQ,EAAE,CACR;IACE,OAAO,EAAE,CAAC;IACV,KAAK,EAAE,CAAC;IACR,MAAM,EAAE,MAAM;IACd,MAAM,EAAE;GACT,CACF;EACD,OAAO,EAAE,CACP;IACE,QAAQ,EAAE,MAAM;IAChB,MAAM,EAAE,MAAM;IACd,MAAM,EAAE;GACT,EACD;IACE,QAAQ,EAAE,MAAM;IAChB,MAAM,EAAE,MAAM;IACd,MAAM,EAAE;GACT;CAEJ,EACD;EACE,UAAU,EAAE,mBAAmB;EAC/B,UAAU,EAAE,SAAS;EACrB,QAAQ,EAAE,CACR;IACE,OAAO,EAAE,CAAC;IACV,MAAM,EAAE,QAAQ;IAChB,MAAM,EAAE;GACT,EACD;IACE,OAAO,EAAE,CAAC;IACV,MAAM,EAAE,SAAS;IACjB,MAAM,EAAE;GACT,EACD;IACE,OAAO,EAAE,CAAC;IACV,MAAM,EAAE,cAAc;IACtB,MAAM,EAAE;GACT,CACF;EACD,OAAO,EAAE,CACP;IACE,QAAQ,EAAE,eAAe;IACzB,MAAM,EAAE,cAAc;IACtB,MAAM,EAAE;GACT;CAEJ,EACD;EACE,UAAU,EAAE,qBAAqB;EACjC,UAAU,EAAE,SAAS;EACrB,QAAQ,EAAE,CACR;IACE,OAAO,EAAE,CAAC;IACV,MAAM,EAAE,QAAQ;IAChB,MAAM,EAAE;GACT,EACD;IACE,OAAO,EAAE,CAAC;IACV,MAAM,EAAE,SAAS;IACjB,MAAM,EAAE;GACT,EACD;IACE,OAAO,EAAE,CAAC;IACV,MAAM,EAAE,cAAc;IACtB,MAAM,EAAE;GACT,EACD;IACE,OAAO,EAAE,CAAC;IACV,MAAM,EAAE,aAAa;IACrB,MAAM,EAAE;GACT,CACF;EACD,OAAO,EAAE,CACP;IACE,QAAQ,EAAE,eAAe;IACzB,MAAM,EAAE,cAAc;IACtB,MAAM,EAAE;GACT;CAEJ,EACD;EACE,UAAU,EAAE,kBAAkB;EAC9B,UAAU,EAAE,SAAS;EACrB,QAAQ,EAAE,CACR;IACE,OAAO,EAAE,CAAC;IACV,MAAM,EAAE,cAAc;IACtB,MAAM,EAAE;GACT,EACD;IACE,OAAO,EAAE,CAAC;IACV,MAAM,EAAE,SAAS;IACjB,MAAM,EAAE;GACT,EACD;IACE,OAAO,EAAE,CAAC;IACV,MAAM,EAAE,cAAc;IACtB,MAAM,EAAE;GACT,CACF;EACD,OAAO,EAAE,CACP;IACE,QAAQ,EAAE,eAAe;IACzB,MAAM,EAAE,cAAc;IACtB,MAAM,EAAE;GACT;CAEJ,EACD;EACE,UAAU,EAAE,mBAAmB;EAC/B,UAAU,EAAE,SAAS;EACrB,QAAQ,EAAE,CACR;IACE,OAAO,EAAE,CAAC;IACV,MAAM,EAAE,cAAc;IACtB,MAAM,EAAE;GACT,EACD;IACE,OAAO,EAAE,CAAC;IACV,MAAM,EAAE,OAAO;IACf,MAAM,EAAE;GACT,EACD;IACE,OAAO,EAAE,CAAC;IACV,MAAM,EAAE,cAAc;IACtB,MAAM,EAAE;GACT,CACF;EACD,OAAO,EAAE,CACP;IACE,QAAQ,EAAE,eAAe;IACzB,MAAM,EAAE,cAAc;IACtB,MAAM,EAAE;GACT;CAEJ,EACD;EACE,UAAU,EAAE,mBAAmB;EAC/B,UAAU,EAAE,SAAS;EACrB,QAAQ,EAAE,CACR;IACE,OAAO,EAAE,CAAC;IACV,MAAM,EAAE,cAAc;IACtB,MAAM,EAAE;GACT,EACD;IACE,OAAO,EAAE,CAAC;IACV,MAAM,EAAE,OAAO;IACf,MAAM,EAAE;GACT,EACD;IACE,OAAO,EAAE,CAAC;IACV,MAAM,EAAE,QAAQ;IAChB,MAAM,EAAE;GACT,CACF;EACD,OAAO,EAAE,CACP;IACE,QAAQ,EAAE,eAAe;IACzB,MAAM,EAAE,cAAc;IACtB,MAAM,EAAE;GACT;CAEJ,EACD;EACE,UAAU,EAAE,mBAAmB;EAC/B,UAAU,EAAE,SAAS;EACrB,QAAQ,EAAE,CACR;IACE,OAAO,EAAE,CAAC;IACV,MAAM,EAAE,cAAc;IACtB,MAAM,EAAE;GACT,EACD;IACE,OAAO,EAAE,CAAC;IACV,MAAM,EAAE,aAAa;IACrB,MAAM,EAAE;GACT,CACF;EACD,OAAO,EAAE,CACP;IACE,QAAQ,EAAE,eAAe;IACzB,MAAM,EAAE,cAAc;IACtB,MAAM,EAAE;GACT;CAEJ,EACD;EACE,UAAU,EAAE,sBAAsB;EAClC,UAAU,EAAE,SAAS;EACrB,QAAQ,EAAE,CACR;IACE,OAAO,EAAE,CAAC;IACV,MAAM,EAAE,QAAQ;IAChB,MAAM,EAAE;GACT,EACD;IACE,OAAO,EAAE,CAAC;IACV,MAAM,EAAE,cAAc;IACtB,MAAM,EAAE;GACT,CACF;EACD,OAAO,EAAE,CACP;IACE,QAAQ,EAAE,eAAe;IACzB,MAAM,EAAE,cAAc;IACtB,MAAM,EAAE;GACT;CAEJ,EACD;EACE,UAAU,EAAE,iBAAiB;EAC7B,UAAU,EAAE,SAAS;EACrB,QAAQ,EAAE,CACR;IACE,OAAO,EAAE,CAAC;IACV,MAAM,EAAE,cAAc;IACtB,MAAM,EAAE;GACT,EACD;IACE,OAAO,EAAE,CAAC;IACV,MAAM,EAAE,cAAc;IACtB,MAAM,EAAE;GACT,CACF;EACD,OAAO,EAAE,CACP;IACE,QAAQ,EAAE,eAAe;IACzB,MAAM,EAAE,cAAc;IACtB,MAAM,EAAE;GACT,EACD;IACE,QAAQ,EAAE,cAAc;IACxB,MAAM,EAAE,aAAa;IACrB,MAAM,EAAE;GACT;CAEJ,EACD;EACE,UAAU,EAAE,iBAAiB;EAC7B,UAAU,EAAE,SAAS;EACrB,QAAQ,EAAE,CACR;IACE,OAAO,EAAE,CAAC;IACV,MAAM,EAAE,QAAQ;IAChB,MAAM,EAAE;GACT,EACD;IACE,OAAO,EAAE,CAAC;IACV,MAAM,EAAE,cAAc;IACtB,MAAM,EAAE;GACT,EACD;IACE,OAAO,EAAE,CAAC;IACV,MAAM,EAAE,SAAS;IACjB,MAAM,EAAE;GACT,CACF;EACD,OAAO,EAAE,CACP;IACE,QAAQ,EAAE,eAAe;IACzB,MAAM,EAAE,cAAc;IACtB,MAAM,EAAE;GACT;CAEJ,EACD;EACE,UAAU,EAAE,kBAAkB;EAC9B,UAAU,EAAE,SAAS;EACrB,QAAQ,EAAE,CACR;IACE,OAAO,EAAE,CAAC;IACV,MAAM,EAAE,cAAc;IACtB,MAAM,EAAE;GACT,CACF;EACD,OAAO,EAAE,CACP;IACE,QAAQ,EAAE,eAAe;IACzB,MAAM,EAAE,cAAc;IACtB,MAAM,EAAE;GACT,EACD;IACE,QAAQ,EAAE,eAAe;IACzB,MAAM,EAAE,cAAc;IACtB,MAAM,EAAE;GACT;CAEJ,EACD;EACE,UAAU,EAAE,oBAAoB;EAChC,UAAU,EAAE,SAAS;EACrB,QAAQ,EAAE,CACR;IACE,OAAO,EAAE,CAAC;IACV,MAAM,EAAE,cAAc;IACtB,MAAM,EAAE;GACT,CACF;EACD,OAAO,EAAE,CACP;IACE,QAAQ,EAAE,eAAe;IACzB,MAAM,EAAE,cAAc;IACtB,MAAM,EAAE;GACT,EACD;IACE,QAAQ,EAAE,eAAe;IACzB,MAAM,EAAE,cAAc;IACtB,MAAM,EAAE;GACT;CAEJ,EACD;EACE,UAAU,EAAE,mBAAmB;EAC/B,UAAU,EAAE,SAAS;EACrB,QAAQ,EAAE,CACR;IACE,OAAO,EAAE,CAAC;IACV,MAAM,EAAE,cAAc;IACtB,MAAM,EAAE;GACT,EACD;IACE,OAAO,EAAE,CAAC;IACV,MAAM,EAAE,cAAc;IACtB,MAAM,EAAE;GACT,CACF;EACD,OAAO,EAAE,CACP;IACE,QAAQ,EAAE,eAAe;IACzB,MAAM,EAAE,cAAc;IACtB,MAAM,EAAE;GACT;CAEJ,EACD;EACE,UAAU,EAAE,oBAAoB;EAChC,UAAU,EAAE,SAAS;EACrB,QAAQ,EAAE,CACR;IACE,OAAO,EAAE,CAAC;IACV,MAAM,EAAE,cAAc;IACtB,MAAM,EAAE;GACT,EACD;IACE,OAAO,EAAE,CAAC;IACV,MAAM,EAAE,QAAQ;IAChB,MAAM,EAAE;GACT,CACF;EACD,OAAO,EAAE,CACP;IACE,QAAQ,EAAE,eAAe;IACzB,MAAM,EAAE,cAAc;IACtB,MAAM,EAAE;GACT;CAEJ,EACD;EACE,UAAU,EAAE,kBAAkB;EAC9B,UAAU,EAAE,SAAS;EACrB,QAAQ,EAAE,CACR;IACE,OAAO,EAAE,CAAC;IACV,MAAM,EAAE,cAAc;IACtB,MAAM,EAAE;GACT;CAEJ,EACD;EACE,UAAU,EAAE,kBAAkB;EAC9B,UAAU,EAAE,SAAS;EACrB,QAAQ,EAAE,CACR;IACE,OAAO,EAAE,CAAC;IACV,MAAM,EAAE,cAAc;IACtB,MAAM,EAAE;GACT,EACD;IACE,OAAO,EAAE,CAAC;IACV,MAAM,EAAE,MAAM;IACd,MAAM,EAAE;GACT;CAEJ,CACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}