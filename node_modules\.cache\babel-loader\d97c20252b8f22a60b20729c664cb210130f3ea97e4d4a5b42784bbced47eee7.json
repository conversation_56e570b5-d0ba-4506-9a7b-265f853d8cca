{"ast": null, "code": "/**\n * @license\n * Copyright 2017 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { getCoordsDataType } from './shader_compiler';\nexport class TransposeProgram {\n  constructor(aShape, newDim) {\n    this.variableNames = ['A'];\n    const outputShape = new Array(aShape.length);\n    for (let i = 0; i < outputShape.length; i++) {\n      outputShape[i] = aShape[newDim[i]];\n    }\n    this.outputShape = outputShape;\n    this.rank = outputShape.length;\n    const dtype = getCoordsDataType(this.rank);\n    const switched = getSwitchedCoords(newDim);\n    this.userCode = \"\\n    void main() {\\n      \".concat(dtype, \" resRC = getOutputCoords();\\n      setOutput(getA(\").concat(switched, \"));\\n    }\\n    \");\n  }\n}\nfunction getSwitchedCoords(newDim) {\n  const rank = newDim.length;\n  if (rank > 6) {\n    throw Error(\"Transpose for rank \".concat(rank, \" is not yet supported\"));\n  }\n  const originalOrder = ['resRC.x', 'resRC.y', 'resRC.z', 'resRC.w', 'resRC.u', 'resRC.v'];\n  const switchedCoords = new Array(rank);\n  for (let i = 0; i < newDim.length; i++) {\n    switchedCoords[newDim[i]] = originalOrder[i];\n  }\n  return switchedCoords.join();\n}", "map": {"version": 3, "names": ["getCoordsDataType", "TransposeProgram", "constructor", "aShape", "new<PERSON><PERSON>", "variableNames", "outputShape", "Array", "length", "i", "rank", "dtype", "switched", "getSwitchedCoords", "userCode", "concat", "Error", "originalOrder", "switchedCoords", "join"], "sources": ["C:\\tfjs-backend-webgl\\src\\transpose_gpu.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2017 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {GPGPUProgram} from './gpgpu_math';\nimport {getCoordsDataType} from './shader_compiler';\n\nexport class TransposeProgram implements GPGPUProgram {\n  variableNames = ['A'];\n  outputShape: number[];\n  userCode: string;\n  rank: number;\n\n  constructor(aShape: number[], newDim: number[]) {\n    const outputShape: number[] = new Array(aShape.length);\n    for (let i = 0; i < outputShape.length; i++) {\n      outputShape[i] = aShape[newDim[i]];\n    }\n    this.outputShape = outputShape;\n    this.rank = outputShape.length;\n    const dtype = getCoordsDataType(this.rank);\n    const switched = getSwitchedCoords(newDim);\n\n    this.userCode = `\n    void main() {\n      ${dtype} resRC = getOutputCoords();\n      setOutput(getA(${switched}));\n    }\n    `;\n  }\n}\n\nfunction getSwitchedCoords(newDim: number[]): string {\n  const rank = newDim.length;\n  if (rank > 6) {\n    throw Error(`Transpose for rank ${rank} is not yet supported`);\n  }\n  const originalOrder =\n      ['resRC.x', 'resRC.y', 'resRC.z', 'resRC.w', 'resRC.u', 'resRC.v'];\n  const switchedCoords = new Array(rank);\n  for (let i = 0; i < newDim.length; i++) {\n    switchedCoords[newDim[i]] = originalOrder[i];\n  }\n  return switchedCoords.join();\n}\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAkBA,SAAQA,iBAAiB,QAAO,mBAAmB;AAEnD,OAAM,MAAOC,gBAAgB;EAM3BC,YAAYC,MAAgB,EAAEC,MAAgB;IAL9C,KAAAC,aAAa,GAAG,CAAC,GAAG,CAAC;IAMnB,MAAMC,WAAW,GAAa,IAAIC,KAAK,CAACJ,MAAM,CAACK,MAAM,CAAC;IACtD,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,WAAW,CAACE,MAAM,EAAEC,CAAC,EAAE,EAAE;MAC3CH,WAAW,CAACG,CAAC,CAAC,GAAGN,MAAM,CAACC,MAAM,CAACK,CAAC,CAAC,CAAC;;IAEpC,IAAI,CAACH,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACI,IAAI,GAAGJ,WAAW,CAACE,MAAM;IAC9B,MAAMG,KAAK,GAAGX,iBAAiB,CAAC,IAAI,CAACU,IAAI,CAAC;IAC1C,MAAME,QAAQ,GAAGC,iBAAiB,CAACT,MAAM,CAAC;IAE1C,IAAI,CAACU,QAAQ,iCAAAC,MAAA,CAETJ,KAAK,wDAAAI,MAAA,CACUH,QAAQ,qBAE1B;EACH;;AAGF,SAASC,iBAAiBA,CAACT,MAAgB;EACzC,MAAMM,IAAI,GAAGN,MAAM,CAACI,MAAM;EAC1B,IAAIE,IAAI,GAAG,CAAC,EAAE;IACZ,MAAMM,KAAK,uBAAAD,MAAA,CAAuBL,IAAI,0BAAuB,CAAC;;EAEhE,MAAMO,aAAa,GACf,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC;EACtE,MAAMC,cAAc,GAAG,IAAIX,KAAK,CAACG,IAAI,CAAC;EACtC,KAAK,IAAID,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGL,MAAM,CAACI,MAAM,EAAEC,CAAC,EAAE,EAAE;IACtCS,cAAc,CAACd,MAAM,CAACK,CAAC,CAAC,CAAC,GAAGQ,aAAa,CAACR,CAAC,CAAC;;EAE9C,OAAOS,cAAc,CAACC,IAAI,EAAE;AAC9B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}