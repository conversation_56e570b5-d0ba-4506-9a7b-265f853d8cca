{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.isCommandOptions = exports.commandOptions = void 0;\nconst symbol = Symbol('Command Options');\nfunction commandOptions(options) {\n  options[symbol] = true;\n  return options;\n}\nexports.commandOptions = commandOptions;\nfunction isCommandOptions(options) {\n  return options?.[symbol] === true;\n}\nexports.isCommandOptions = isCommandOptions;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "isCommandOptions", "commandOptions", "symbol", "Symbol", "options"], "sources": ["C:/tmsft/node_modules/@redis/client/dist/lib/command-options.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.isCommandOptions = exports.commandOptions = void 0;\nconst symbol = Symbol('Command Options');\nfunction commandOptions(options) {\n    options[symbol] = true;\n    return options;\n}\nexports.commandOptions = commandOptions;\nfunction isCommandOptions(options) {\n    return options?.[symbol] === true;\n}\nexports.isCommandOptions = isCommandOptions;\n"], "mappings": "AAAA,YAAY;;AACZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEC,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7DD,OAAO,CAACE,gBAAgB,GAAGF,OAAO,CAACG,cAAc,GAAG,KAAK,CAAC;AAC1D,MAAMC,MAAM,GAAGC,MAAM,CAAC,iBAAiB,CAAC;AACxC,SAASF,cAAcA,CAACG,OAAO,EAAE;EAC7BA,OAAO,CAACF,MAAM,CAAC,GAAG,IAAI;EACtB,OAAOE,OAAO;AAClB;AACAN,OAAO,CAACG,cAAc,GAAGA,cAAc;AACvC,SAASD,gBAAgBA,CAACI,OAAO,EAAE;EAC/B,OAAOA,OAAO,GAAGF,MAAM,CAAC,KAAK,IAAI;AACrC;AACAJ,OAAO,CAACE,gBAAgB,GAAGA,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}