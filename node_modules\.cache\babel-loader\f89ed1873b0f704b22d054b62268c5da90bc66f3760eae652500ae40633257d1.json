{"ast": null, "code": "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { backend_util, env, ScatterNd } from '@tensorflow/tfjs-core';\nimport { ScatterProgram } from '../scatter_gpu';\nimport { ScatterPackedProgram } from '../scatter_packed_gpu';\nimport { reshape } from './Reshape';\nexport function scatterNd(args) {\n  const {\n    inputs,\n    backend,\n    attrs\n  } = args;\n  const {\n    indices,\n    updates\n  } = inputs;\n  const {\n    shape\n  } = attrs;\n  const {\n    sliceRank,\n    numUpdates,\n    sliceSize,\n    strides,\n    outputSize\n  } = backend_util.calculateShapes(updates, indices, shape);\n  const flattenShape = [outputSize / sliceSize, sliceSize];\n  if (outputSize === 0) {\n    return backend.makeTensorInfo(shape, indices.dtype);\n  }\n  const flattenIndices = reshape({\n    inputs: {\n      x: indices\n    },\n    backend,\n    attrs: {\n      shape: [numUpdates, sliceRank]\n    }\n  });\n  const flattenX = reshape({\n    inputs: {\n      x: updates\n    },\n    backend,\n    attrs: {\n      shape: [numUpdates, sliceSize]\n    }\n  });\n  const defaultValue = backend.makeTensorInfo([], 'float32', new Float32Array([0])); // scalar(0)\n  let program;\n  if (env().getBool('WEBGL_PACK')) {\n    program = new ScatterPackedProgram(numUpdates, sliceRank, flattenIndices.shape.length, flattenX.shape.length, strides, flattenShape);\n  } else {\n    program = new ScatterProgram(numUpdates, sliceRank, flattenIndices.shape.length, flattenX.shape.length, strides, flattenShape);\n  }\n  const res = backend.runWebGLProgram(program, [flattenX, flattenIndices, defaultValue], flattenX.dtype);\n  const reshaped = reshape({\n    inputs: {\n      x: res\n    },\n    backend,\n    attrs: {\n      shape\n    }\n  });\n  backend.disposeIntermediateTensorInfo(flattenIndices);\n  backend.disposeIntermediateTensorInfo(flattenX);\n  backend.disposeIntermediateTensorInfo(res);\n  backend.disposeIntermediateTensorInfo(defaultValue);\n  return reshaped;\n}\nexport const scatterNdConfig = {\n  kernelName: ScatterNd,\n  backendName: 'webgl',\n  kernelFunc: scatterNd\n};", "map": {"version": 3, "names": ["backend_util", "env", "ScatterNd", "ScatterProgram", "ScatterPackedProgram", "reshape", "scatterNd", "args", "inputs", "backend", "attrs", "indices", "updates", "shape", "sliceRank", "numUpdates", "sliceSize", "strides", "outputSize", "calculateShapes", "flattenShape", "makeTensorInfo", "dtype", "flattenIndices", "x", "flattenX", "defaultValue", "Float32Array", "program", "getBool", "length", "res", "runWebGLProgram", "reshaped", "disposeIntermediateTensorInfo", "scatterNdConfig", "kernelName", "backendName", "kernelFunc"], "sources": ["C:\\tfjs-backend-webgl\\src\\kernels\\ScatterNd.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {backend_util, env, KernelConfig, KernelFunc, ScatterNd, ScatterNdAttrs, ScatterNdInputs, TensorInfo} from '@tensorflow/tfjs-core';\n\nimport {MathBackendWebGL} from '../backend_webgl';\nimport {ScatterProgram} from '../scatter_gpu';\nimport {ScatterPackedProgram} from '../scatter_packed_gpu';\nimport {reshape} from './Reshape';\n\nexport function scatterNd(args: {\n  inputs: ScatterNdInputs,\n  backend: MathBackendWebGL,\n  attrs: ScatterNdAttrs\n}): TensorInfo {\n  const {inputs, backend, attrs} = args;\n  const {indices, updates} = inputs;\n  const {shape} = attrs;\n\n  const {sliceRank, numUpdates, sliceSize, strides, outputSize} =\n      backend_util.calculateShapes(updates, indices, shape);\n\n  const flattenShape = [outputSize / sliceSize, sliceSize];\n\n  if (outputSize === 0) {\n    return backend.makeTensorInfo(shape, indices.dtype);\n  }\n\n  const flattenIndices = reshape(\n      {inputs: {x: indices}, backend, attrs: {shape: [numUpdates, sliceRank]}});\n  const flattenX = reshape(\n      {inputs: {x: updates}, backend, attrs: {shape: [numUpdates, sliceSize]}});\n\n  const defaultValue = backend.makeTensorInfo(\n      [], 'float32', new Float32Array([0]));  // scalar(0)\n  let program;\n  if (env().getBool('WEBGL_PACK')) {\n    program = new ScatterPackedProgram(\n        numUpdates, sliceRank, flattenIndices.shape.length,\n        flattenX.shape.length, strides, flattenShape);\n  } else {\n    program = new ScatterProgram(\n        numUpdates, sliceRank, flattenIndices.shape.length,\n        flattenX.shape.length, strides, flattenShape);\n  }\n  const res = backend.runWebGLProgram(\n      program, [flattenX, flattenIndices, defaultValue], flattenX.dtype);\n\n  const reshaped = reshape({inputs: {x: res}, backend, attrs: {shape}});\n\n  backend.disposeIntermediateTensorInfo(flattenIndices);\n  backend.disposeIntermediateTensorInfo(flattenX);\n  backend.disposeIntermediateTensorInfo(res);\n  backend.disposeIntermediateTensorInfo(defaultValue);\n\n  return reshaped;\n}\n\nexport const scatterNdConfig: KernelConfig = {\n  kernelName: ScatterNd,\n  backendName: 'webgl',\n  kernelFunc: scatterNd as unknown as KernelFunc\n};\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAiBA,SAAQA,YAAY,EAAEC,GAAG,EAA4BC,SAAS,QAAoD,uBAAuB;AAGzI,SAAQC,cAAc,QAAO,gBAAgB;AAC7C,SAAQC,oBAAoB,QAAO,uBAAuB;AAC1D,SAAQC,OAAO,QAAO,WAAW;AAEjC,OAAM,SAAUC,SAASA,CAACC,IAIzB;EACC,MAAM;IAACC,MAAM;IAAEC,OAAO;IAAEC;EAAK,CAAC,GAAGH,IAAI;EACrC,MAAM;IAACI,OAAO;IAAEC;EAAO,CAAC,GAAGJ,MAAM;EACjC,MAAM;IAACK;EAAK,CAAC,GAAGH,KAAK;EAErB,MAAM;IAACI,SAAS;IAAEC,UAAU;IAAEC,SAAS;IAAEC,OAAO;IAAEC;EAAU,CAAC,GACzDlB,YAAY,CAACmB,eAAe,CAACP,OAAO,EAAED,OAAO,EAAEE,KAAK,CAAC;EAEzD,MAAMO,YAAY,GAAG,CAACF,UAAU,GAAGF,SAAS,EAAEA,SAAS,CAAC;EAExD,IAAIE,UAAU,KAAK,CAAC,EAAE;IACpB,OAAOT,OAAO,CAACY,cAAc,CAACR,KAAK,EAAEF,OAAO,CAACW,KAAK,CAAC;;EAGrD,MAAMC,cAAc,GAAGlB,OAAO,CAC1B;IAACG,MAAM,EAAE;MAACgB,CAAC,EAAEb;IAAO,CAAC;IAAEF,OAAO;IAAEC,KAAK,EAAE;MAACG,KAAK,EAAE,CAACE,UAAU,EAAED,SAAS;IAAC;EAAC,CAAC,CAAC;EAC7E,MAAMW,QAAQ,GAAGpB,OAAO,CACpB;IAACG,MAAM,EAAE;MAACgB,CAAC,EAAEZ;IAAO,CAAC;IAAEH,OAAO;IAAEC,KAAK,EAAE;MAACG,KAAK,EAAE,CAACE,UAAU,EAAEC,SAAS;IAAC;EAAC,CAAC,CAAC;EAE7E,MAAMU,YAAY,GAAGjB,OAAO,CAACY,cAAc,CACvC,EAAE,EAAE,SAAS,EAAE,IAAIM,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAE;EAC5C,IAAIC,OAAO;EACX,IAAI3B,GAAG,EAAE,CAAC4B,OAAO,CAAC,YAAY,CAAC,EAAE;IAC/BD,OAAO,GAAG,IAAIxB,oBAAoB,CAC9BW,UAAU,EAAED,SAAS,EAAES,cAAc,CAACV,KAAK,CAACiB,MAAM,EAClDL,QAAQ,CAACZ,KAAK,CAACiB,MAAM,EAAEb,OAAO,EAAEG,YAAY,CAAC;GAClD,MAAM;IACLQ,OAAO,GAAG,IAAIzB,cAAc,CACxBY,UAAU,EAAED,SAAS,EAAES,cAAc,CAACV,KAAK,CAACiB,MAAM,EAClDL,QAAQ,CAACZ,KAAK,CAACiB,MAAM,EAAEb,OAAO,EAAEG,YAAY,CAAC;;EAEnD,MAAMW,GAAG,GAAGtB,OAAO,CAACuB,eAAe,CAC/BJ,OAAO,EAAE,CAACH,QAAQ,EAAEF,cAAc,EAAEG,YAAY,CAAC,EAAED,QAAQ,CAACH,KAAK,CAAC;EAEtE,MAAMW,QAAQ,GAAG5B,OAAO,CAAC;IAACG,MAAM,EAAE;MAACgB,CAAC,EAAEO;IAAG,CAAC;IAAEtB,OAAO;IAAEC,KAAK,EAAE;MAACG;IAAK;EAAC,CAAC,CAAC;EAErEJ,OAAO,CAACyB,6BAA6B,CAACX,cAAc,CAAC;EACrDd,OAAO,CAACyB,6BAA6B,CAACT,QAAQ,CAAC;EAC/ChB,OAAO,CAACyB,6BAA6B,CAACH,GAAG,CAAC;EAC1CtB,OAAO,CAACyB,6BAA6B,CAACR,YAAY,CAAC;EAEnD,OAAOO,QAAQ;AACjB;AAEA,OAAO,MAAME,eAAe,GAAiB;EAC3CC,UAAU,EAAElC,SAAS;EACrBmC,WAAW,EAAE,OAAO;EACpBC,UAAU,EAAEhC;CACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}