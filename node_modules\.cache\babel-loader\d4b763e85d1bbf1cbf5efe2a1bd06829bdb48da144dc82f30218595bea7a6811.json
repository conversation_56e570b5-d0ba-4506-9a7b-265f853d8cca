{"ast": null, "code": "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { Asinh } from '@tensorflow/tfjs-core';\nimport { unaryKernelFunc } from '../kernel_utils/kernel_funcs_utils';\nimport { CHECK_NAN_SNIPPET } from '../unaryop_gpu';\nconst ASINH = CHECK_NAN_SNIPPET + `return log(x + sqrt(x * x + 1.0));`;\nexport const asinh = unaryKernelFunc({\n  opSnippet: ASINH\n});\nexport const asinhConfig = {\n  kernelName: Asinh,\n  backendName: 'webgl',\n  kernelFunc: asinh\n};", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON>", "unaryKernelFunc", "CHECK_NAN_SNIPPET", "ASINH", "asinh", "opSnippet", "asinhConfig", "kernelName", "backendName", "kernelFunc"], "sources": ["C:\\tfjs-backend-webgl\\src\\kernels\\Asinh.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {Asinh, KernelConfig} from '@tensorflow/tfjs-core';\n\nimport {unaryKernelFunc} from '../kernel_utils/kernel_funcs_utils';\nimport {CHECK_NAN_SNIPPET} from '../unaryop_gpu';\n\nconst ASINH = CHECK_NAN_SNIPPET + `return log(x + sqrt(x * x + 1.0));`;\n\nexport const asinh = unaryKernelFunc({opSnippet: ASINH});\n\nexport const asinhConfig: KernelConfig = {\n  kernelName: Asinh,\n  backendName: 'webgl',\n  kernelFunc: asinh,\n};\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAiBA,SAAQA,KAAK,QAAqB,uBAAuB;AAEzD,SAAQC,eAAe,QAAO,oCAAoC;AAClE,SAAQC,iBAAiB,QAAO,gBAAgB;AAEhD,MAAMC,KAAK,GAAGD,iBAAiB,GAAG,oCAAoC;AAEtE,OAAO,MAAME,KAAK,GAAGH,eAAe,CAAC;EAACI,SAAS,EAAEF;AAAK,CAAC,CAAC;AAExD,OAAO,MAAMG,WAAW,GAAiB;EACvCC,UAAU,EAAEP,KAAK;EACjBQ,WAAW,EAAE,OAAO;EACpBC,UAAU,EAAEL;CACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}