{"ast": null, "code": "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { Sum } from '@tensorflow/tfjs-core';\nimport { sumImpl } from './Sum_impl';\nexport function sum(args) {\n  const {\n    inputs,\n    backend,\n    attrs\n  } = args;\n  const {\n    x\n  } = inputs;\n  const {\n    axis,\n    keepDims\n  } = attrs;\n  return sumImpl(x, axis, keepDims, backend);\n}\nexport const sumConfig = {\n  kernelName: Sum,\n  backendName: 'webgl',\n  kernelFunc: sum\n};", "map": {"version": 3, "names": ["Sum", "sumImpl", "sum", "args", "inputs", "backend", "attrs", "x", "axis", "keepDims", "sumConfig", "kernelName", "backendName", "kernelFunc"], "sources": ["C:\\tfjs-backend-webgl\\src\\kernels\\Sum.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {KernelConfig, KernelFunc, Sum, SumAttrs, SumInputs} from '@tensorflow/tfjs-core';\n\nimport {MathBackendWebGL} from '../backend_webgl';\n\nimport {sumImpl} from './Sum_impl';\n\nexport function sum(\n    args: {inputs: SumInputs, attrs: SumAttrs, backend: MathBackendWebGL}) {\n  const {inputs, backend, attrs} = args;\n\n  const {x} = inputs;\n  const {axis, keepDims} = attrs;\n\n  return sumImpl(x, axis, keepDims, backend);\n}\n\nexport const sumConfig: KernelConfig = {\n  kernelName: Sum,\n  backendName: 'webgl',\n  kernelFunc: sum as unknown as KernelFunc\n};\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAiBA,SAAkCA,GAAG,QAA4B,uBAAuB;AAIxF,SAAQC,OAAO,QAAO,YAAY;AAElC,OAAM,SAAUC,GAAGA,CACfC,IAAqE;EACvE,MAAM;IAACC,MAAM;IAAEC,OAAO;IAAEC;EAAK,CAAC,GAAGH,IAAI;EAErC,MAAM;IAACI;EAAC,CAAC,GAAGH,MAAM;EAClB,MAAM;IAACI,IAAI;IAAEC;EAAQ,CAAC,GAAGH,KAAK;EAE9B,OAAOL,OAAO,CAACM,CAAC,EAAEC,IAAI,EAAEC,QAAQ,EAAEJ,OAAO,CAAC;AAC5C;AAEA,OAAO,MAAMK,SAAS,GAAiB;EACrCC,UAAU,EAAEX,GAAG;EACfY,WAAW,EAAE,OAAO;EACpBC,UAAU,EAAEX;CACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}