{"ast": null, "code": "/**\n * @license\n * Copyright 2018 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { Tensor } from '../tensor';\nimport { convertToTensor } from '../tensor_util_env';\nimport * as util from '../util';\nimport { add } from './add';\nimport { div } from './div';\nimport { getNoiseShape } from './dropout_util';\nimport { floor } from './floor';\nimport { mul } from './mul';\nimport { op } from './operation';\nimport { randomUniform } from './random_uniform';\n/**\n * Computes dropout.\n *\n * ```js\n * const x = tf.tensor1d([1, 2, 2, 1]);\n * const rate = 0.75;\n * const output = tf.dropout(x, rate);\n * output.print();\n * ```\n *\n * @param x A floating point Tensor or TensorLike.\n * @param rate A float in the range [0, 1). The probability that each element\n *   of x is discarded.\n * @param noiseShape An array of numbers of type int32, representing the\n * shape for randomly generated keep/drop flags. If the noiseShape has null\n * value, it will be automatically replaced with the x's relative dimension\n * size. Optional.\n * @param seed Used to create random seeds. Optional.\n * @returns A Tensor of the same shape of x.\n *\n * @doc {heading: 'Operations', subheading: 'Dropout'}\n */\nfunction dropout_(x, rate, noiseShape, seed) {\n  const $x = convertToTensor(x, 'x', 'dropout');\n  util.assert($x.dtype === 'float32', () => \"x has to be a floating point tensor since it's going to be \" + \"scaled, but got a \".concat($x.dtype, \" tensor instead.\"));\n  util.assert(rate >= 0 && rate < 1, () => \"rate must be a float in the range [0, 1), but got \".concat(rate, \".\"));\n  if (rate === 0) {\n    return x instanceof Tensor ? $x.clone() : $x;\n  }\n  const $noiseShape = getNoiseShape($x, noiseShape);\n  const keepProb = 1 - rate;\n  const multiplier = div(floor(add(randomUniform($noiseShape, 0, 1, 'float32', seed), keepProb)), keepProb);\n  return mul($x, multiplier);\n}\nexport const dropout = /* @__PURE__ */op({\n  dropout_\n});", "map": {"version": 3, "names": ["Tensor", "convertToTensor", "util", "add", "div", "getNoiseShape", "floor", "mul", "op", "randomUniform", "dropout_", "x", "rate", "noiseShape", "seed", "$x", "assert", "dtype", "concat", "clone", "$noiseShape", "keepProb", "multiplier", "dropout"], "sources": ["C:\\tfjs-core\\src\\ops\\dropout.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2018 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {Tensor} from '../tensor';\nimport {convertToTensor} from '../tensor_util_env';\nimport {TensorLike} from '../types';\nimport * as util from '../util';\n\nimport {add} from './add';\nimport {div} from './div';\nimport {getNoiseShape} from './dropout_util';\nimport {floor} from './floor';\nimport {mul} from './mul';\nimport {op} from './operation';\nimport {randomUniform} from './random_uniform';\n\n/**\n * Computes dropout.\n *\n * ```js\n * const x = tf.tensor1d([1, 2, 2, 1]);\n * const rate = 0.75;\n * const output = tf.dropout(x, rate);\n * output.print();\n * ```\n *\n * @param x A floating point Tensor or TensorLike.\n * @param rate A float in the range [0, 1). The probability that each element\n *   of x is discarded.\n * @param noiseShape An array of numbers of type int32, representing the\n * shape for randomly generated keep/drop flags. If the noiseShape has null\n * value, it will be automatically replaced with the x's relative dimension\n * size. Optional.\n * @param seed Used to create random seeds. Optional.\n * @returns A Tensor of the same shape of x.\n *\n * @doc {heading: 'Operations', subheading: 'Dropout'}\n */\nfunction dropout_(\n    x: Tensor|TensorLike, rate: number, noiseShape?: number[],\n    seed?: number|string): Tensor {\n  const $x = convertToTensor(x, 'x', 'dropout');\n\n  util.assert(\n      $x.dtype === 'float32',\n      () => `x has to be a floating point tensor since it's going to be ` +\n          `scaled, but got a ${$x.dtype} tensor instead.`);\n  util.assert(\n      rate >= 0 && rate < 1,\n      () => `rate must be a float in the range [0, 1), but got ${rate}.`);\n\n  if (rate === 0) {\n    return x instanceof Tensor ? $x.clone() : $x;\n  }\n\n  const $noiseShape = getNoiseShape($x, noiseShape);\n  const keepProb = 1 - rate;\n  const multiplier = div(\n      floor(add(randomUniform($noiseShape, 0, 1, 'float32', seed), keepProb)),\n      keepProb);\n\n  return mul($x, multiplier);\n}\n\nexport const dropout = /* @__PURE__ */ op({dropout_});\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAiBA,SAAQA,MAAM,QAAO,WAAW;AAChC,SAAQC,eAAe,QAAO,oBAAoB;AAElD,OAAO,KAAKC,IAAI,MAAM,SAAS;AAE/B,SAAQC,GAAG,QAAO,OAAO;AACzB,SAAQC,GAAG,QAAO,OAAO;AACzB,SAAQC,aAAa,QAAO,gBAAgB;AAC5C,SAAQC,KAAK,QAAO,SAAS;AAC7B,SAAQC,GAAG,QAAO,OAAO;AACzB,SAAQC,EAAE,QAAO,aAAa;AAC9B,SAAQC,aAAa,QAAO,kBAAkB;AAE9C;;;;;;;;;;;;;;;;;;;;;;AAsBA,SAASC,QAAQA,CACbC,CAAoB,EAAEC,IAAY,EAAEC,UAAqB,EACzDC,IAAoB;EACtB,MAAMC,EAAE,GAAGd,eAAe,CAACU,CAAC,EAAE,GAAG,EAAE,SAAS,CAAC;EAE7CT,IAAI,CAACc,MAAM,CACPD,EAAE,CAACE,KAAK,KAAK,SAAS,EACtB,MAAM,qFAAAC,MAAA,CACmBH,EAAE,CAACE,KAAK,qBAAkB,CAAC;EACxDf,IAAI,CAACc,MAAM,CACPJ,IAAI,IAAI,CAAC,IAAIA,IAAI,GAAG,CAAC,EACrB,2DAAAM,MAAA,CAA2DN,IAAI,MAAG,CAAC;EAEvE,IAAIA,IAAI,KAAK,CAAC,EAAE;IACd,OAAOD,CAAC,YAAYX,MAAM,GAAGe,EAAE,CAACI,KAAK,EAAE,GAAGJ,EAAE;;EAG9C,MAAMK,WAAW,GAAGf,aAAa,CAACU,EAAE,EAAEF,UAAU,CAAC;EACjD,MAAMQ,QAAQ,GAAG,CAAC,GAAGT,IAAI;EACzB,MAAMU,UAAU,GAAGlB,GAAG,CAClBE,KAAK,CAACH,GAAG,CAACM,aAAa,CAACW,WAAW,EAAE,CAAC,EAAE,CAAC,EAAE,SAAS,EAAEN,IAAI,CAAC,EAAEO,QAAQ,CAAC,CAAC,EACvEA,QAAQ,CAAC;EAEb,OAAOd,GAAG,CAACQ,EAAE,EAAEO,UAAU,CAAC;AAC5B;AAEA,OAAO,MAAMC,OAAO,GAAG,eAAgBf,EAAE,CAAC;EAACE;AAAQ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}