{"ast": null, "code": "// Predicate-generating function. Often useful outside of Underscore.\nexport default function constant(value) {\n  return function () {\n    return value;\n  };\n}", "map": {"version": 3, "names": ["constant", "value"], "sources": ["C:/tmsft/node_modules/underscore/modules/constant.js"], "sourcesContent": ["// Predicate-generating function. Often useful outside of Underscore.\nexport default function constant(value) {\n  return function() {\n    return value;\n  };\n}\n"], "mappings": "AAAA;AACA,eAAe,SAASA,QAAQA,CAACC,KAAK,EAAE;EACtC,OAAO,YAAW;IAChB,OAAOA,KAAK;EACd,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}