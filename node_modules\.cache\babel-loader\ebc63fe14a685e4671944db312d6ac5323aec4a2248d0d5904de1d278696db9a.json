{"ast": null, "code": "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { Cosh } from '../kernel_names';\nimport { cast } from '../ops/cast';\nimport { mul } from '../ops/mul';\nimport { sinh } from '../ops/sinh';\nexport const coshGradConfig = {\n  kernelName: Cosh,\n  inputsToSave: ['x'],\n  gradFunc: (dy, saved) => {\n    const [x] = saved;\n    return {\n      x: () => mul(sinh(cast(x, 'float32')), dy)\n    };\n  }\n};", "map": {"version": 3, "names": ["<PERSON><PERSON>", "cast", "mul", "sinh", "coshGradConfig", "kernelName", "inputsToSave", "grad<PERSON>unc", "dy", "saved", "x"], "sources": ["C:\\tfjs-core\\src\\gradients\\Cosh_grad.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {Cosh} from '../kernel_names';\nimport {GradConfig} from '../kernel_registry';\nimport {cast} from '../ops/cast';\nimport {mul} from '../ops/mul';\nimport {sinh} from '../ops/sinh';\nimport {Tensor} from '../tensor';\n\nexport const coshGradConfig: GradConfig = {\n  kernelName: Cosh,\n  inputsToSave: ['x'],\n  gradFunc: (dy: Tensor, saved: Tensor[]) => {\n    const [x] = saved;\n\n    return {x: () => mul(sinh(cast(x, 'float32')), dy)};\n  }\n};\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAiBA,SAAQA,IAAI,QAAO,iBAAiB;AAEpC,SAAQC,IAAI,QAAO,aAAa;AAChC,SAAQC,GAAG,QAAO,YAAY;AAC9B,SAAQC,IAAI,QAAO,aAAa;AAGhC,OAAO,MAAMC,cAAc,GAAe;EACxCC,UAAU,EAAEL,IAAI;EAChBM,YAAY,EAAE,CAAC,GAAG,CAAC;EACnBC,QAAQ,EAAEA,CAACC,EAAU,EAAEC,KAAe,KAAI;IACxC,MAAM,CAACC,CAAC,CAAC,GAAGD,KAAK;IAEjB,OAAO;MAACC,CAAC,EAAEA,CAAA,KAAMR,GAAG,CAACC,IAAI,CAACF,IAAI,CAACS,CAAC,EAAE,SAAS,CAAC,CAAC,EAAEF,EAAE;IAAC,CAAC;EACrD;CACD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}