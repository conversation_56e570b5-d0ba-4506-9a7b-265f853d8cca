{"ast": null, "code": "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { convertToTensor } from '../../tensor_util_env';\nimport { assertShapesMatch } from '../../util';\nimport { abs } from '../abs';\nimport { Reduction } from '../loss_ops_utils';\nimport { op } from '../operation';\nimport { sub } from '../sub';\nimport { computeWeightedLoss } from './compute_weighted_loss';\n/**\n * Computes the absolute difference loss between two tensors.\n *\n * @param labels The ground truth output tensor, same dimensions as\n *    'predictions'.\n * @param predictions The predicted outputs.\n * @param weights Tensor whose rank is either 0, or the same rank as\n *    `labels`, and must be broadcastable to `labels` (i.e., all dimensions\n *    must be either `1`, or the same as the corresponding `losses`\n *    dimension).\n * @param reduction Type of reduction to apply to loss. Should be of type\n *    `Reduction`\n *\n * @doc {heading: 'Training', subheading: 'Losses', namespace: 'losses'}\n */\nfunction absoluteDifference_(labels, predictions, weights) {\n  let reduction = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : Reduction.SUM_BY_NONZERO_WEIGHTS;\n  const $labels = convertToTensor(labels, 'labels', 'absoluteDifference');\n  const $predictions = convertToTensor(predictions, 'predictions', 'absoluteDifference');\n  let $weights = null;\n  if (weights != null) {\n    $weights = convertToTensor(weights, 'weights', 'absoluteDifference');\n  }\n  assertShapesMatch($labels.shape, $predictions.shape, 'Error in absoluteDifference: ');\n  const losses = abs(sub($labels, $predictions));\n  return computeWeightedLoss(losses, $weights, reduction);\n}\nexport const absoluteDifference = /* @__PURE__ */op({\n  absoluteDifference_\n});", "map": {"version": 3, "names": ["convertToTensor", "assertShapesMatch", "abs", "Reduction", "op", "sub", "computeWeightedLoss", "absoluteDifference_", "labels", "predictions", "weights", "reduction", "arguments", "length", "undefined", "SUM_BY_NONZERO_WEIGHTS", "$labels", "$predictions", "$weights", "shape", "losses", "absoluteDifference"], "sources": ["C:\\tfjs-core\\src\\ops\\losses\\absolute_difference.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {Tensor} from '../../tensor';\nimport {convertToTensor} from '../../tensor_util_env';\nimport {TensorLike} from '../../types';\nimport {assertShapesMatch} from '../../util';\nimport {abs} from '../abs';\nimport {Reduction} from '../loss_ops_utils';\nimport {op} from '../operation';\nimport {sub} from '../sub';\n\nimport {computeWeightedLoss} from './compute_weighted_loss';\n\n/**\n * Computes the absolute difference loss between two tensors.\n *\n * @param labels The ground truth output tensor, same dimensions as\n *    'predictions'.\n * @param predictions The predicted outputs.\n * @param weights Tensor whose rank is either 0, or the same rank as\n *    `labels`, and must be broadcastable to `labels` (i.e., all dimensions\n *    must be either `1`, or the same as the corresponding `losses`\n *    dimension).\n * @param reduction Type of reduction to apply to loss. Should be of type\n *    `Reduction`\n *\n * @doc {heading: 'Training', subheading: 'Losses', namespace: 'losses'}\n */\nfunction absoluteDifference_<T extends Tensor, O extends Tensor>(\n    labels: T|TensorLike, predictions: T|TensorLike,\n    weights?: Tensor|TensorLike,\n    reduction = Reduction.SUM_BY_NONZERO_WEIGHTS): O {\n  const $labels = convertToTensor(labels, 'labels', 'absoluteDifference');\n  const $predictions =\n      convertToTensor(predictions, 'predictions', 'absoluteDifference');\n  let $weights: Tensor = null;\n  if (weights != null) {\n    $weights = convertToTensor(weights, 'weights', 'absoluteDifference');\n  }\n  assertShapesMatch(\n      $labels.shape, $predictions.shape, 'Error in absoluteDifference: ');\n\n  const losses = abs(sub($labels, $predictions));\n  return computeWeightedLoss(losses, $weights, reduction);\n}\n\nexport const absoluteDifference = /* @__PURE__ */ op({absoluteDifference_});\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAkBA,SAAQA,eAAe,QAAO,uBAAuB;AAErD,SAAQC,iBAAiB,QAAO,YAAY;AAC5C,SAAQC,GAAG,QAAO,QAAQ;AAC1B,SAAQC,SAAS,QAAO,mBAAmB;AAC3C,SAAQC,EAAE,QAAO,cAAc;AAC/B,SAAQC,GAAG,QAAO,QAAQ;AAE1B,SAAQC,mBAAmB,QAAO,yBAAyB;AAE3D;;;;;;;;;;;;;;;AAeA,SAASC,mBAAmBA,CACxBC,MAAoB,EAAEC,WAAyB,EAC/CC,OAA2B,EACiB;EAAA,IAA5CC,SAAS,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAGT,SAAS,CAACY,sBAAsB;EAC9C,MAAMC,OAAO,GAAGhB,eAAe,CAACQ,MAAM,EAAE,QAAQ,EAAE,oBAAoB,CAAC;EACvE,MAAMS,YAAY,GACdjB,eAAe,CAACS,WAAW,EAAE,aAAa,EAAE,oBAAoB,CAAC;EACrE,IAAIS,QAAQ,GAAW,IAAI;EAC3B,IAAIR,OAAO,IAAI,IAAI,EAAE;IACnBQ,QAAQ,GAAGlB,eAAe,CAACU,OAAO,EAAE,SAAS,EAAE,oBAAoB,CAAC;;EAEtET,iBAAiB,CACbe,OAAO,CAACG,KAAK,EAAEF,YAAY,CAACE,KAAK,EAAE,+BAA+B,CAAC;EAEvE,MAAMC,MAAM,GAAGlB,GAAG,CAACG,GAAG,CAACW,OAAO,EAAEC,YAAY,CAAC,CAAC;EAC9C,OAAOX,mBAAmB,CAACc,MAAM,EAAEF,QAAQ,EAAEP,SAAS,CAAC;AACzD;AAEA,OAAO,MAAMU,kBAAkB,GAAG,eAAgBjB,EAAE,CAAC;EAACG;AAAmB,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}