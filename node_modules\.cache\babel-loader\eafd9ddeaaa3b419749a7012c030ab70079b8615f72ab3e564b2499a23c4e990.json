{"ast": null, "code": "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { HashTable } from '../../executor/hash_table';\nimport { getParamValue } from './utils';\nexport const executeOp = async (node, tensorMap, context, resourceManager) => {\n  switch (node.op) {\n    case 'HashTable':\n    case 'HashTableV2':\n      {\n        const existingTableHandle = resourceManager.getHashTableHandleByName(node.name);\n        // Table is shared with initializer.\n        if (existingTableHandle != null) {\n          return [existingTableHandle];\n        } else {\n          const keyDType = getParamValue('keyDType', node, tensorMap, context);\n          const valueDType = getParamValue('valueDType', node, tensorMap, context);\n          const hashTable = new HashTable(keyDType, valueDType);\n          resourceManager.addHashTable(node.name, hashTable);\n          return [hashTable.handle];\n        }\n      }\n    case 'InitializeTable':\n    case 'InitializeTableV2':\n    case 'LookupTableImport':\n    case 'LookupTableImportV2':\n      {\n        const handle = getParamValue('tableHandle', node, tensorMap, context, resourceManager);\n        const keys = getParamValue('keys', node, tensorMap, context);\n        const values = getParamValue('values', node, tensorMap, context);\n        const hashTable = resourceManager.getHashTableById(handle.id);\n        return [await hashTable.import(keys, values)];\n      }\n    case 'LookupTableFind':\n    case 'LookupTableFindV2':\n      {\n        const handle = getParamValue('tableHandle', node, tensorMap, context, resourceManager);\n        const keys = getParamValue('keys', node, tensorMap, context);\n        const defaultValue = getParamValue('defaultValue', node, tensorMap, context);\n        const hashTable = resourceManager.getHashTableById(handle.id);\n        return [await hashTable.find(keys, defaultValue)];\n      }\n    case 'LookupTableSize':\n    case 'LookupTableSizeV2':\n      {\n        const handle = getParamValue('tableHandle', node, tensorMap, context, resourceManager);\n        const hashTable = resourceManager.getHashTableById(handle.id);\n        return [hashTable.tensorSize()];\n      }\n    default:\n      throw TypeError(`Node type ${node.op} is not implemented`);\n  }\n};\nexport const CATEGORY = 'hash_table';", "map": {"version": 3, "names": ["HashTable", "getParamValue", "executeOp", "node", "tensorMap", "context", "resourceManager", "op", "existingTableHandle", "getHashTableHandleByName", "name", "keyDType", "valueDType", "hashTable", "addHashTable", "handle", "keys", "values", "getHashTableById", "id", "import", "defaultValue", "find", "tensorSize", "TypeError", "CATEGORY"], "sources": ["C:\\tfjs-converter\\src\\operations\\executors\\hash_table_executor.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {DataType, Tensor} from '@tensorflow/tfjs-core';\n\nimport {NamedTensorsMap} from '../../data/types';\nimport {ExecutionContext} from '../../executor/execution_context';\nimport {HashTable} from '../../executor/hash_table';\nimport {ResourceManager} from '../../executor/resource_manager';\nimport {InternalOpAsyncExecutor, Node} from '../types';\n\nimport {getParamValue} from './utils';\n\nexport const executeOp: InternalOpAsyncExecutor = async(\n    node: Node, tensorMap: NamedTensorsMap, context: ExecutionContext,\n    resourceManager: ResourceManager): Promise<Tensor[]> => {\n  switch (node.op) {\n    case 'HashTable':\n    case 'HashTableV2': {\n      const existingTableHandle =\n          resourceManager.getHashTableHandleByName(node.name);\n      // Table is shared with initializer.\n      if (existingTableHandle != null) {\n        return [existingTableHandle];\n      } else {\n        const keyDType =\n            getParamValue('keyDType', node, tensorMap, context) as DataType;\n        const valueDType =\n            getParamValue('valueDType', node, tensorMap, context) as DataType;\n\n        const hashTable = new HashTable(keyDType, valueDType);\n        resourceManager.addHashTable(node.name, hashTable);\n        return [hashTable.handle];\n      }\n    }\n    case 'InitializeTable':\n    case 'InitializeTableV2':\n    case 'LookupTableImport':\n    case 'LookupTableImportV2': {\n      const handle = getParamValue(\n                         'tableHandle', node, tensorMap, context,\n                         resourceManager) as Tensor;\n      const keys = getParamValue('keys', node, tensorMap, context) as Tensor;\n      const values =\n          getParamValue('values', node, tensorMap, context) as Tensor;\n\n      const hashTable = resourceManager.getHashTableById(handle.id);\n\n      return [await hashTable.import(keys, values)];\n    }\n    case 'LookupTableFind':\n    case 'LookupTableFindV2': {\n      const handle = getParamValue(\n                         'tableHandle', node, tensorMap, context,\n                         resourceManager) as Tensor;\n      const keys = getParamValue('keys', node, tensorMap, context) as Tensor;\n      const defaultValue =\n          getParamValue('defaultValue', node, tensorMap, context) as Tensor;\n\n      const hashTable = resourceManager.getHashTableById(handle.id);\n      return [await hashTable.find(keys, defaultValue)];\n    }\n    case 'LookupTableSize':\n    case 'LookupTableSizeV2': {\n      const handle = getParamValue(\n                         'tableHandle', node, tensorMap, context,\n                         resourceManager) as Tensor;\n\n      const hashTable = resourceManager.getHashTableById(handle.id);\n      return [hashTable.tensorSize()];\n    }\n    default:\n      throw TypeError(`Node type ${node.op} is not implemented`);\n  }\n};\n\nexport const CATEGORY = 'hash_table';\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAqBA,SAAQA,SAAS,QAAO,2BAA2B;AAInD,SAAQC,aAAa,QAAO,SAAS;AAErC,OAAO,MAAMC,SAAS,GAA4B,MAAAA,CAC9CC,IAAU,EAAEC,SAA0B,EAAEC,OAAyB,EACjEC,eAAgC,KAAuB;EACzD,QAAQH,IAAI,CAACI,EAAE;IACb,KAAK,WAAW;IAChB,KAAK,aAAa;MAAE;QAClB,MAAMC,mBAAmB,GACrBF,eAAe,CAACG,wBAAwB,CAACN,IAAI,CAACO,IAAI,CAAC;QACvD;QACA,IAAIF,mBAAmB,IAAI,IAAI,EAAE;UAC/B,OAAO,CAACA,mBAAmB,CAAC;SAC7B,MAAM;UACL,MAAMG,QAAQ,GACVV,aAAa,CAAC,UAAU,EAAEE,IAAI,EAAEC,SAAS,EAAEC,OAAO,CAAa;UACnE,MAAMO,UAAU,GACZX,aAAa,CAAC,YAAY,EAAEE,IAAI,EAAEC,SAAS,EAAEC,OAAO,CAAa;UAErE,MAAMQ,SAAS,GAAG,IAAIb,SAAS,CAACW,QAAQ,EAAEC,UAAU,CAAC;UACrDN,eAAe,CAACQ,YAAY,CAACX,IAAI,CAACO,IAAI,EAAEG,SAAS,CAAC;UAClD,OAAO,CAACA,SAAS,CAACE,MAAM,CAAC;;;IAG7B,KAAK,iBAAiB;IACtB,KAAK,mBAAmB;IACxB,KAAK,mBAAmB;IACxB,KAAK,qBAAqB;MAAE;QAC1B,MAAMA,MAAM,GAAGd,aAAa,CACT,aAAa,EAAEE,IAAI,EAAEC,SAAS,EAAEC,OAAO,EACvCC,eAAe,CAAW;QAC7C,MAAMU,IAAI,GAAGf,aAAa,CAAC,MAAM,EAAEE,IAAI,EAAEC,SAAS,EAAEC,OAAO,CAAW;QACtE,MAAMY,MAAM,GACRhB,aAAa,CAAC,QAAQ,EAAEE,IAAI,EAAEC,SAAS,EAAEC,OAAO,CAAW;QAE/D,MAAMQ,SAAS,GAAGP,eAAe,CAACY,gBAAgB,CAACH,MAAM,CAACI,EAAE,CAAC;QAE7D,OAAO,CAAC,MAAMN,SAAS,CAACO,MAAM,CAACJ,IAAI,EAAEC,MAAM,CAAC,CAAC;;IAE/C,KAAK,iBAAiB;IACtB,KAAK,mBAAmB;MAAE;QACxB,MAAMF,MAAM,GAAGd,aAAa,CACT,aAAa,EAAEE,IAAI,EAAEC,SAAS,EAAEC,OAAO,EACvCC,eAAe,CAAW;QAC7C,MAAMU,IAAI,GAAGf,aAAa,CAAC,MAAM,EAAEE,IAAI,EAAEC,SAAS,EAAEC,OAAO,CAAW;QACtE,MAAMgB,YAAY,GACdpB,aAAa,CAAC,cAAc,EAAEE,IAAI,EAAEC,SAAS,EAAEC,OAAO,CAAW;QAErE,MAAMQ,SAAS,GAAGP,eAAe,CAACY,gBAAgB,CAACH,MAAM,CAACI,EAAE,CAAC;QAC7D,OAAO,CAAC,MAAMN,SAAS,CAACS,IAAI,CAACN,IAAI,EAAEK,YAAY,CAAC,CAAC;;IAEnD,KAAK,iBAAiB;IACtB,KAAK,mBAAmB;MAAE;QACxB,MAAMN,MAAM,GAAGd,aAAa,CACT,aAAa,EAAEE,IAAI,EAAEC,SAAS,EAAEC,OAAO,EACvCC,eAAe,CAAW;QAE7C,MAAMO,SAAS,GAAGP,eAAe,CAACY,gBAAgB,CAACH,MAAM,CAACI,EAAE,CAAC;QAC7D,OAAO,CAACN,SAAS,CAACU,UAAU,EAAE,CAAC;;IAEjC;MACE,MAAMC,SAAS,CAAC,aAAarB,IAAI,CAACI,EAAE,qBAAqB,CAAC;;AAEhE,CAAC;AAED,OAAO,MAAMkB,QAAQ,GAAG,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}