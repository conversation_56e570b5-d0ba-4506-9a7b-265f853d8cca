{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.serialize = void 0;\nconst buffer_writer_1 = require(\"./buffer-writer\");\nconst writer = new buffer_writer_1.Writer();\nconst startup = opts => {\n  // protocol version\n  writer.addInt16(3).addInt16(0);\n  for (const key of Object.keys(opts)) {\n    writer.addCString(key).addCString(opts[key]);\n  }\n  writer.addCString('client_encoding').addCString('UTF8');\n  const bodyBuffer = writer.addCString('').flush();\n  // this message is sent without a code\n  const length = bodyBuffer.length + 4;\n  return new buffer_writer_1.Writer().addInt32(length).add(bodyBuffer).flush();\n};\nconst requestSsl = () => {\n  const response = Buffer.allocUnsafe(8);\n  response.writeInt32BE(8, 0);\n  response.writeInt32BE(80877103, 4);\n  return response;\n};\nconst password = password => {\n  return writer.addCString(password).flush(112 /* code.startup */);\n};\nconst sendSASLInitialResponseMessage = function (mechanism, initialResponse) {\n  // 0x70 = 'p'\n  writer.addCString(mechanism).addInt32(Buffer.byteLength(initialResponse)).addString(initialResponse);\n  return writer.flush(112 /* code.startup */);\n};\nconst sendSCRAMClientFinalMessage = function (additionalData) {\n  return writer.addString(additionalData).flush(112 /* code.startup */);\n};\nconst query = text => {\n  return writer.addCString(text).flush(81 /* code.query */);\n};\nconst emptyArray = [];\nconst parse = query => {\n  // expect something like this:\n  // { name: 'queryName',\n  //   text: 'select * from blah',\n  //   types: ['int8', 'bool'] }\n  // normalize missing query names to allow for null\n  const name = query.name || '';\n  if (name.length > 63) {\n    console.error('Warning! Postgres only supports 63 characters for query names.');\n    console.error('You supplied %s (%s)', name, name.length);\n    console.error('This can cause conflicts and silent errors executing queries');\n  }\n  const types = query.types || emptyArray;\n  const len = types.length;\n  const buffer = writer.addCString(name) // name of query\n  .addCString(query.text) // actual query text\n  .addInt16(len);\n  for (let i = 0; i < len; i++) {\n    buffer.addInt32(types[i]);\n  }\n  return writer.flush(80 /* code.parse */);\n};\nconst paramWriter = new buffer_writer_1.Writer();\nconst writeValues = function (values, valueMapper) {\n  for (let i = 0; i < values.length; i++) {\n    const mappedVal = valueMapper ? valueMapper(values[i], i) : values[i];\n    if (mappedVal == null) {\n      // add the param type (string) to the writer\n      writer.addInt16(0 /* ParamType.STRING */);\n      // write -1 to the param writer to indicate null\n      paramWriter.addInt32(-1);\n    } else if (mappedVal instanceof Buffer) {\n      // add the param type (binary) to the writer\n      writer.addInt16(1 /* ParamType.BINARY */);\n      // add the buffer to the param writer\n      paramWriter.addInt32(mappedVal.length);\n      paramWriter.add(mappedVal);\n    } else {\n      // add the param type (string) to the writer\n      writer.addInt16(0 /* ParamType.STRING */);\n      paramWriter.addInt32(Buffer.byteLength(mappedVal));\n      paramWriter.addString(mappedVal);\n    }\n  }\n};\nconst bind = (config = {}) => {\n  // normalize config\n  const portal = config.portal || '';\n  const statement = config.statement || '';\n  const binary = config.binary || false;\n  const values = config.values || emptyArray;\n  const len = values.length;\n  writer.addCString(portal).addCString(statement);\n  writer.addInt16(len);\n  writeValues(values, config.valueMapper);\n  writer.addInt16(len);\n  writer.add(paramWriter.flush());\n  // format code\n  writer.addInt16(binary ? 1 /* ParamType.BINARY */ : 0 /* ParamType.STRING */);\n  return writer.flush(66 /* code.bind */);\n};\nconst emptyExecute = Buffer.from([69 /* code.execute */, 0x00, 0x00, 0x00, 0x09, 0x00, 0x00, 0x00, 0x00, 0x00]);\nconst execute = config => {\n  // this is the happy path for most queries\n  if (!config || !config.portal && !config.rows) {\n    return emptyExecute;\n  }\n  const portal = config.portal || '';\n  const rows = config.rows || 0;\n  const portalLength = Buffer.byteLength(portal);\n  const len = 4 + portalLength + 1 + 4;\n  // one extra bit for code\n  const buff = Buffer.allocUnsafe(1 + len);\n  buff[0] = 69 /* code.execute */;\n  buff.writeInt32BE(len, 1);\n  buff.write(portal, 5, 'utf-8');\n  buff[portalLength + 5] = 0; // null terminate portal cString\n  buff.writeUInt32BE(rows, buff.length - 4);\n  return buff;\n};\nconst cancel = (processID, secretKey) => {\n  const buffer = Buffer.allocUnsafe(16);\n  buffer.writeInt32BE(16, 0);\n  buffer.writeInt16BE(1234, 4);\n  buffer.writeInt16BE(5678, 6);\n  buffer.writeInt32BE(processID, 8);\n  buffer.writeInt32BE(secretKey, 12);\n  return buffer;\n};\nconst cstringMessage = (code, string) => {\n  const stringLen = Buffer.byteLength(string);\n  const len = 4 + stringLen + 1;\n  // one extra bit for code\n  const buffer = Buffer.allocUnsafe(1 + len);\n  buffer[0] = code;\n  buffer.writeInt32BE(len, 1);\n  buffer.write(string, 5, 'utf-8');\n  buffer[len] = 0; // null terminate cString\n  return buffer;\n};\nconst emptyDescribePortal = writer.addCString('P').flush(68 /* code.describe */);\nconst emptyDescribeStatement = writer.addCString('S').flush(68 /* code.describe */);\nconst describe = msg => {\n  return msg.name ? cstringMessage(68 /* code.describe */, `${msg.type}${msg.name || ''}`) : msg.type === 'P' ? emptyDescribePortal : emptyDescribeStatement;\n};\nconst close = msg => {\n  const text = `${msg.type}${msg.name || ''}`;\n  return cstringMessage(67 /* code.close */, text);\n};\nconst copyData = chunk => {\n  return writer.add(chunk).flush(100 /* code.copyFromChunk */);\n};\nconst copyFail = message => {\n  return cstringMessage(102 /* code.copyFail */, message);\n};\nconst codeOnlyBuffer = code => Buffer.from([code, 0x00, 0x00, 0x00, 0x04]);\nconst flushBuffer = codeOnlyBuffer(72 /* code.flush */);\nconst syncBuffer = codeOnlyBuffer(83 /* code.sync */);\nconst endBuffer = codeOnlyBuffer(88 /* code.end */);\nconst copyDoneBuffer = codeOnlyBuffer(99 /* code.copyDone */);\nconst serialize = {\n  startup,\n  password,\n  requestSsl,\n  sendSASLInitialResponseMessage,\n  sendSCRAMClientFinalMessage,\n  query,\n  parse,\n  bind,\n  execute,\n  describe,\n  close,\n  flush: () => flushBuffer,\n  sync: () => syncBuffer,\n  end: () => endBuffer,\n  copyData,\n  copyDone: () => copyDoneBuffer,\n  copyFail,\n  cancel\n};\nexports.serialize = serialize;", "map": {"version": 3, "names": ["buffer_writer_1", "require", "writer", "Writer", "startup", "opts", "addInt16", "key", "Object", "keys", "addCString", "bodyBuffer", "flush", "length", "addInt32", "add", "requestSsl", "response", "<PERSON><PERSON><PERSON>", "allocUnsafe", "writeInt32BE", "password", "sendSASLInitialResponseMessage", "mechanism", "initialResponse", "byteLength", "addString", "sendSCRAMClientFinalMessage", "additionalData", "query", "text", "emptyArray", "parse", "name", "console", "error", "types", "len", "buffer", "i", "paramWriter", "writeValues", "values", "valueMapper", "mappedVal", "bind", "config", "portal", "statement", "binary", "emptyExecute", "from", "execute", "rows", "portalLength", "buff", "write", "writeUInt32BE", "cancel", "processID", "secret<PERSON>ey", "writeInt16BE", "cstringMessage", "code", "string", "stringLen", "emptyDescribePortal", "emptyDescribeStatement", "describe", "msg", "type", "close", "copyData", "chunk", "copyFail", "message", "code<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "flushBuffer", "syncBuffer", "end<PERSON><PERSON><PERSON>", "copyDoneBuffer", "serialize", "sync", "end", "copyDone", "exports"], "sources": ["C:\\tmsft\\node_modules\\pg-protocol\\src\\serializer.ts"], "sourcesContent": ["import { Writer } from './buffer-writer'\n\nconst enum code {\n  startup = 0x70,\n  query = 0x51,\n  parse = 0x50,\n  bind = 0x42,\n  execute = 0x45,\n  flush = 0x48,\n  sync = 0x53,\n  end = 0x58,\n  close = 0x43,\n  describe = 0x44,\n  copyFromChunk = 0x64,\n  copyDone = 0x63,\n  copyFail = 0x66,\n}\n\nconst writer = new Writer()\n\nconst startup = (opts: Record<string, string>): Buffer => {\n  // protocol version\n  writer.addInt16(3).addInt16(0)\n  for (const key of Object.keys(opts)) {\n    writer.addCString(key).addCString(opts[key])\n  }\n\n  writer.addCString('client_encoding').addCString('UTF8')\n\n  const bodyBuffer = writer.addCString('').flush()\n  // this message is sent without a code\n\n  const length = bodyBuffer.length + 4\n\n  return new Writer().addInt32(length).add(bodyBuffer).flush()\n}\n\nconst requestSsl = (): Buffer => {\n  const response = Buffer.allocUnsafe(8)\n  response.writeInt32BE(8, 0)\n  response.writeInt32BE(80877103, 4)\n  return response\n}\n\nconst password = (password: string): Buffer => {\n  return writer.addCString(password).flush(code.startup)\n}\n\nconst sendSASLInitialResponseMessage = function (mechanism: string, initialResponse: string): Buffer {\n  // 0x70 = 'p'\n  writer.addCString(mechanism).addInt32(Buffer.byteLength(initialResponse)).addString(initialResponse)\n\n  return writer.flush(code.startup)\n}\n\nconst sendSCRAMClientFinalMessage = function (additionalData: string): Buffer {\n  return writer.addString(additionalData).flush(code.startup)\n}\n\nconst query = (text: string): Buffer => {\n  return writer.addCString(text).flush(code.query)\n}\n\ntype ParseOpts = {\n  name?: string\n  types?: number[]\n  text: string\n}\n\nconst emptyArray: any[] = []\n\nconst parse = (query: ParseOpts): Buffer => {\n  // expect something like this:\n  // { name: 'queryName',\n  //   text: 'select * from blah',\n  //   types: ['int8', 'bool'] }\n\n  // normalize missing query names to allow for null\n  const name = query.name || ''\n  if (name.length > 63) {\n    console.error('Warning! Postgres only supports 63 characters for query names.')\n    console.error('You supplied %s (%s)', name, name.length)\n    console.error('This can cause conflicts and silent errors executing queries')\n  }\n\n  const types = query.types || emptyArray\n\n  const len = types.length\n\n  const buffer = writer\n    .addCString(name) // name of query\n    .addCString(query.text) // actual query text\n    .addInt16(len)\n\n  for (let i = 0; i < len; i++) {\n    buffer.addInt32(types[i])\n  }\n\n  return writer.flush(code.parse)\n}\n\ntype ValueMapper = (param: any, index: number) => any\n\ntype BindOpts = {\n  portal?: string\n  binary?: boolean\n  statement?: string\n  values?: any[]\n  // optional map from JS value to postgres value per parameter\n  valueMapper?: ValueMapper\n}\n\nconst paramWriter = new Writer()\n\n// make this a const enum so typescript will inline the value\nconst enum ParamType {\n  STRING = 0,\n  BINARY = 1,\n}\n\nconst writeValues = function (values: any[], valueMapper?: ValueMapper): void {\n  for (let i = 0; i < values.length; i++) {\n    const mappedVal = valueMapper ? valueMapper(values[i], i) : values[i]\n    if (mappedVal == null) {\n      // add the param type (string) to the writer\n      writer.addInt16(ParamType.STRING)\n      // write -1 to the param writer to indicate null\n      paramWriter.addInt32(-1)\n    } else if (mappedVal instanceof Buffer) {\n      // add the param type (binary) to the writer\n      writer.addInt16(ParamType.BINARY)\n      // add the buffer to the param writer\n      paramWriter.addInt32(mappedVal.length)\n      paramWriter.add(mappedVal)\n    } else {\n      // add the param type (string) to the writer\n      writer.addInt16(ParamType.STRING)\n      paramWriter.addInt32(Buffer.byteLength(mappedVal))\n      paramWriter.addString(mappedVal)\n    }\n  }\n}\n\nconst bind = (config: BindOpts = {}): Buffer => {\n  // normalize config\n  const portal = config.portal || ''\n  const statement = config.statement || ''\n  const binary = config.binary || false\n  const values = config.values || emptyArray\n  const len = values.length\n\n  writer.addCString(portal).addCString(statement)\n  writer.addInt16(len)\n\n  writeValues(values, config.valueMapper)\n\n  writer.addInt16(len)\n  writer.add(paramWriter.flush())\n\n  // format code\n  writer.addInt16(binary ? ParamType.BINARY : ParamType.STRING)\n  return writer.flush(code.bind)\n}\n\ntype ExecOpts = {\n  portal?: string\n  rows?: number\n}\n\nconst emptyExecute = Buffer.from([code.execute, 0x00, 0x00, 0x00, 0x09, 0x00, 0x00, 0x00, 0x00, 0x00])\n\nconst execute = (config?: ExecOpts): Buffer => {\n  // this is the happy path for most queries\n  if (!config || (!config.portal && !config.rows)) {\n    return emptyExecute\n  }\n\n  const portal = config.portal || ''\n  const rows = config.rows || 0\n\n  const portalLength = Buffer.byteLength(portal)\n  const len = 4 + portalLength + 1 + 4\n  // one extra bit for code\n  const buff = Buffer.allocUnsafe(1 + len)\n  buff[0] = code.execute\n  buff.writeInt32BE(len, 1)\n  buff.write(portal, 5, 'utf-8')\n  buff[portalLength + 5] = 0 // null terminate portal cString\n  buff.writeUInt32BE(rows, buff.length - 4)\n  return buff\n}\n\nconst cancel = (processID: number, secretKey: number): Buffer => {\n  const buffer = Buffer.allocUnsafe(16)\n  buffer.writeInt32BE(16, 0)\n  buffer.writeInt16BE(1234, 4)\n  buffer.writeInt16BE(5678, 6)\n  buffer.writeInt32BE(processID, 8)\n  buffer.writeInt32BE(secretKey, 12)\n  return buffer\n}\n\ntype PortalOpts = {\n  type: 'S' | 'P'\n  name?: string\n}\n\nconst cstringMessage = (code: code, string: string): Buffer => {\n  const stringLen = Buffer.byteLength(string)\n  const len = 4 + stringLen + 1\n  // one extra bit for code\n  const buffer = Buffer.allocUnsafe(1 + len)\n  buffer[0] = code\n  buffer.writeInt32BE(len, 1)\n  buffer.write(string, 5, 'utf-8')\n  buffer[len] = 0 // null terminate cString\n  return buffer\n}\n\nconst emptyDescribePortal = writer.addCString('P').flush(code.describe)\nconst emptyDescribeStatement = writer.addCString('S').flush(code.describe)\n\nconst describe = (msg: PortalOpts): Buffer => {\n  return msg.name\n    ? cstringMessage(code.describe, `${msg.type}${msg.name || ''}`)\n    : msg.type === 'P'\n    ? emptyDescribePortal\n    : emptyDescribeStatement\n}\n\nconst close = (msg: PortalOpts): Buffer => {\n  const text = `${msg.type}${msg.name || ''}`\n  return cstringMessage(code.close, text)\n}\n\nconst copyData = (chunk: Buffer): Buffer => {\n  return writer.add(chunk).flush(code.copyFromChunk)\n}\n\nconst copyFail = (message: string): Buffer => {\n  return cstringMessage(code.copyFail, message)\n}\n\nconst codeOnlyBuffer = (code: code): Buffer => Buffer.from([code, 0x00, 0x00, 0x00, 0x04])\n\nconst flushBuffer = codeOnlyBuffer(code.flush)\nconst syncBuffer = codeOnlyBuffer(code.sync)\nconst endBuffer = codeOnlyBuffer(code.end)\nconst copyDoneBuffer = codeOnlyBuffer(code.copyDone)\n\nconst serialize = {\n  startup,\n  password,\n  requestSsl,\n  sendSASLInitialResponseMessage,\n  sendSCRAMClientFinalMessage,\n  query,\n  parse,\n  bind,\n  execute,\n  describe,\n  close,\n  flush: () => flushBuffer,\n  sync: () => syncBuffer,\n  end: () => endBuffer,\n  copyData,\n  copyDone: () => copyDoneBuffer,\n  copyFail,\n  cancel,\n}\n\nexport { serialize }\n"], "mappings": ";;;;;;AAAA,MAAAA,eAAA,GAAAC,OAAA;AAkBA,MAAMC,MAAM,GAAG,IAAIF,eAAA,CAAAG,MAAM,EAAE;AAE3B,MAAMC,OAAO,GAAIC,IAA4B,IAAY;EACvD;EACAH,MAAM,CAACI,QAAQ,CAAC,CAAC,CAAC,CAACA,QAAQ,CAAC,CAAC,CAAC;EAC9B,KAAK,MAAMC,GAAG,IAAIC,MAAM,CAACC,IAAI,CAACJ,IAAI,CAAC,EAAE;IACnCH,MAAM,CAACQ,UAAU,CAACH,GAAG,CAAC,CAACG,UAAU,CAACL,IAAI,CAACE,GAAG,CAAC,CAAC;;EAG9CL,MAAM,CAACQ,UAAU,CAAC,iBAAiB,CAAC,CAACA,UAAU,CAAC,MAAM,CAAC;EAEvD,MAAMC,UAAU,GAAGT,MAAM,CAACQ,UAAU,CAAC,EAAE,CAAC,CAACE,KAAK,EAAE;EAChD;EAEA,MAAMC,MAAM,GAAGF,UAAU,CAACE,MAAM,GAAG,CAAC;EAEpC,OAAO,IAAIb,eAAA,CAAAG,MAAM,EAAE,CAACW,QAAQ,CAACD,MAAM,CAAC,CAACE,GAAG,CAACJ,UAAU,CAAC,CAACC,KAAK,EAAE;AAC9D,CAAC;AAED,MAAMI,UAAU,GAAGA,CAAA,KAAa;EAC9B,MAAMC,QAAQ,GAAGC,MAAM,CAACC,WAAW,CAAC,CAAC,CAAC;EACtCF,QAAQ,CAACG,YAAY,CAAC,CAAC,EAAE,CAAC,CAAC;EAC3BH,QAAQ,CAACG,YAAY,CAAC,QAAQ,EAAE,CAAC,CAAC;EAClC,OAAOH,QAAQ;AACjB,CAAC;AAED,MAAMI,QAAQ,GAAIA,QAAgB,IAAY;EAC5C,OAAOnB,MAAM,CAACQ,UAAU,CAACW,QAAQ,CAAC,CAACT,KAAK,wBAAc;AACxD,CAAC;AAED,MAAMU,8BAA8B,GAAG,SAAAA,CAAUC,SAAiB,EAAEC,eAAuB;EACzF;EACAtB,MAAM,CAACQ,UAAU,CAACa,SAAS,CAAC,CAACT,QAAQ,CAACI,MAAM,CAACO,UAAU,CAACD,eAAe,CAAC,CAAC,CAACE,SAAS,CAACF,eAAe,CAAC;EAEpG,OAAOtB,MAAM,CAACU,KAAK,wBAAc;AACnC,CAAC;AAED,MAAMe,2BAA2B,GAAG,SAAAA,CAAUC,cAAsB;EAClE,OAAO1B,MAAM,CAACwB,SAAS,CAACE,cAAc,CAAC,CAAChB,KAAK,wBAAc;AAC7D,CAAC;AAED,MAAMiB,KAAK,GAAIC,IAAY,IAAY;EACrC,OAAO5B,MAAM,CAACQ,UAAU,CAACoB,IAAI,CAAC,CAAClB,KAAK,qBAAY;AAClD,CAAC;AAQD,MAAMmB,UAAU,GAAU,EAAE;AAE5B,MAAMC,KAAK,GAAIH,KAAgB,IAAY;EACzC;EACA;EACA;EACA;EAEA;EACA,MAAMI,IAAI,GAAGJ,KAAK,CAACI,IAAI,IAAI,EAAE;EAC7B,IAAIA,IAAI,CAACpB,MAAM,GAAG,EAAE,EAAE;IACpBqB,OAAO,CAACC,KAAK,CAAC,gEAAgE,CAAC;IAC/ED,OAAO,CAACC,KAAK,CAAC,sBAAsB,EAAEF,IAAI,EAAEA,IAAI,CAACpB,MAAM,CAAC;IACxDqB,OAAO,CAACC,KAAK,CAAC,8DAA8D,CAAC;;EAG/E,MAAMC,KAAK,GAAGP,KAAK,CAACO,KAAK,IAAIL,UAAU;EAEvC,MAAMM,GAAG,GAAGD,KAAK,CAACvB,MAAM;EAExB,MAAMyB,MAAM,GAAGpC,MAAM,CAClBQ,UAAU,CAACuB,IAAI,CAAC,CAAC;EAAA,CACjBvB,UAAU,CAACmB,KAAK,CAACC,IAAI,CAAC,CAAC;EAAA,CACvBxB,QAAQ,CAAC+B,GAAG,CAAC;EAEhB,KAAK,IAAIE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,GAAG,EAAEE,CAAC,EAAE,EAAE;IAC5BD,MAAM,CAACxB,QAAQ,CAACsB,KAAK,CAACG,CAAC,CAAC,CAAC;;EAG3B,OAAOrC,MAAM,CAACU,KAAK,qBAAY;AACjC,CAAC;AAaD,MAAM4B,WAAW,GAAG,IAAIxC,eAAA,CAAAG,MAAM,EAAE;AAQhC,MAAMsC,WAAW,GAAG,SAAAA,CAAUC,MAAa,EAAEC,WAAyB;EACpE,KAAK,IAAIJ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGG,MAAM,CAAC7B,MAAM,EAAE0B,CAAC,EAAE,EAAE;IACtC,MAAMK,SAAS,GAAGD,WAAW,GAAGA,WAAW,CAACD,MAAM,CAACH,CAAC,CAAC,EAAEA,CAAC,CAAC,GAAGG,MAAM,CAACH,CAAC,CAAC;IACrE,IAAIK,SAAS,IAAI,IAAI,EAAE;MACrB;MACA1C,MAAM,CAACI,QAAQ,0BAAkB;MACjC;MACAkC,WAAW,CAAC1B,QAAQ,CAAC,CAAC,CAAC,CAAC;KACzB,MAAM,IAAI8B,SAAS,YAAY1B,MAAM,EAAE;MACtC;MACAhB,MAAM,CAACI,QAAQ,0BAAkB;MACjC;MACAkC,WAAW,CAAC1B,QAAQ,CAAC8B,SAAS,CAAC/B,MAAM,CAAC;MACtC2B,WAAW,CAACzB,GAAG,CAAC6B,SAAS,CAAC;KAC3B,MAAM;MACL;MACA1C,MAAM,CAACI,QAAQ,0BAAkB;MACjCkC,WAAW,CAAC1B,QAAQ,CAACI,MAAM,CAACO,UAAU,CAACmB,SAAS,CAAC,CAAC;MAClDJ,WAAW,CAACd,SAAS,CAACkB,SAAS,CAAC;;;AAGtC,CAAC;AAED,MAAMC,IAAI,GAAGA,CAACC,MAAA,GAAmB,EAAE,KAAY;EAC7C;EACA,MAAMC,MAAM,GAAGD,MAAM,CAACC,MAAM,IAAI,EAAE;EAClC,MAAMC,SAAS,GAAGF,MAAM,CAACE,SAAS,IAAI,EAAE;EACxC,MAAMC,MAAM,GAAGH,MAAM,CAACG,MAAM,IAAI,KAAK;EACrC,MAAMP,MAAM,GAAGI,MAAM,CAACJ,MAAM,IAAIX,UAAU;EAC1C,MAAMM,GAAG,GAAGK,MAAM,CAAC7B,MAAM;EAEzBX,MAAM,CAACQ,UAAU,CAACqC,MAAM,CAAC,CAACrC,UAAU,CAACsC,SAAS,CAAC;EAC/C9C,MAAM,CAACI,QAAQ,CAAC+B,GAAG,CAAC;EAEpBI,WAAW,CAACC,MAAM,EAAEI,MAAM,CAACH,WAAW,CAAC;EAEvCzC,MAAM,CAACI,QAAQ,CAAC+B,GAAG,CAAC;EACpBnC,MAAM,CAACa,GAAG,CAACyB,WAAW,CAAC5B,KAAK,EAAE,CAAC;EAE/B;EACAV,MAAM,CAACI,QAAQ,CAAC2C,MAAM,GAAE,2BAAmB,wBAAiB,CAAC;EAC7D,OAAO/C,MAAM,CAACU,KAAK,oBAAW;AAChC,CAAC;AAOD,MAAMsC,YAAY,GAAGhC,MAAM,CAACiC,IAAI,CAAC,wBAAe,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;AAEtG,MAAMC,OAAO,GAAIN,MAAiB,IAAY;EAC5C;EACA,IAAI,CAACA,MAAM,IAAK,CAACA,MAAM,CAACC,MAAM,IAAI,CAACD,MAAM,CAACO,IAAK,EAAE;IAC/C,OAAOH,YAAY;;EAGrB,MAAMH,MAAM,GAAGD,MAAM,CAACC,MAAM,IAAI,EAAE;EAClC,MAAMM,IAAI,GAAGP,MAAM,CAACO,IAAI,IAAI,CAAC;EAE7B,MAAMC,YAAY,GAAGpC,MAAM,CAACO,UAAU,CAACsB,MAAM,CAAC;EAC9C,MAAMV,GAAG,GAAG,CAAC,GAAGiB,YAAY,GAAG,CAAC,GAAG,CAAC;EACpC;EACA,MAAMC,IAAI,GAAGrC,MAAM,CAACC,WAAW,CAAC,CAAC,GAAGkB,GAAG,CAAC;EACxCkB,IAAI,CAAC,CAAC,CAAC;EACPA,IAAI,CAACnC,YAAY,CAACiB,GAAG,EAAE,CAAC,CAAC;EACzBkB,IAAI,CAACC,KAAK,CAACT,MAAM,EAAE,CAAC,EAAE,OAAO,CAAC;EAC9BQ,IAAI,CAACD,YAAY,GAAG,CAAC,CAAC,GAAG,CAAC,EAAC;EAC3BC,IAAI,CAACE,aAAa,CAACJ,IAAI,EAAEE,IAAI,CAAC1C,MAAM,GAAG,CAAC,CAAC;EACzC,OAAO0C,IAAI;AACb,CAAC;AAED,MAAMG,MAAM,GAAGA,CAACC,SAAiB,EAAEC,SAAiB,KAAY;EAC9D,MAAMtB,MAAM,GAAGpB,MAAM,CAACC,WAAW,CAAC,EAAE,CAAC;EACrCmB,MAAM,CAAClB,YAAY,CAAC,EAAE,EAAE,CAAC,CAAC;EAC1BkB,MAAM,CAACuB,YAAY,CAAC,IAAI,EAAE,CAAC,CAAC;EAC5BvB,MAAM,CAACuB,YAAY,CAAC,IAAI,EAAE,CAAC,CAAC;EAC5BvB,MAAM,CAAClB,YAAY,CAACuC,SAAS,EAAE,CAAC,CAAC;EACjCrB,MAAM,CAAClB,YAAY,CAACwC,SAAS,EAAE,EAAE,CAAC;EAClC,OAAOtB,MAAM;AACf,CAAC;AAOD,MAAMwB,cAAc,GAAGA,CAACC,IAAU,EAAEC,MAAc,KAAY;EAC5D,MAAMC,SAAS,GAAG/C,MAAM,CAACO,UAAU,CAACuC,MAAM,CAAC;EAC3C,MAAM3B,GAAG,GAAG,CAAC,GAAG4B,SAAS,GAAG,CAAC;EAC7B;EACA,MAAM3B,MAAM,GAAGpB,MAAM,CAACC,WAAW,CAAC,CAAC,GAAGkB,GAAG,CAAC;EAC1CC,MAAM,CAAC,CAAC,CAAC,GAAGyB,IAAI;EAChBzB,MAAM,CAAClB,YAAY,CAACiB,GAAG,EAAE,CAAC,CAAC;EAC3BC,MAAM,CAACkB,KAAK,CAACQ,MAAM,EAAE,CAAC,EAAE,OAAO,CAAC;EAChC1B,MAAM,CAACD,GAAG,CAAC,GAAG,CAAC,EAAC;EAChB,OAAOC,MAAM;AACf,CAAC;AAED,MAAM4B,mBAAmB,GAAGhE,MAAM,CAACQ,UAAU,CAAC,GAAG,CAAC,CAACE,KAAK,wBAAe;AACvE,MAAMuD,sBAAsB,GAAGjE,MAAM,CAACQ,UAAU,CAAC,GAAG,CAAC,CAACE,KAAK,wBAAe;AAE1E,MAAMwD,QAAQ,GAAIC,GAAe,IAAY;EAC3C,OAAOA,GAAG,CAACpC,IAAI,GACX6B,cAAc,yBAAgB,GAAGO,GAAG,CAACC,IAAI,GAAGD,GAAG,CAACpC,IAAI,IAAI,EAAE,EAAE,CAAC,GAC7DoC,GAAG,CAACC,IAAI,KAAK,GAAG,GAChBJ,mBAAmB,GACnBC,sBAAsB;AAC5B,CAAC;AAED,MAAMI,KAAK,GAAIF,GAAe,IAAY;EACxC,MAAMvC,IAAI,GAAG,GAAGuC,GAAG,CAACC,IAAI,GAAGD,GAAG,CAACpC,IAAI,IAAI,EAAE,EAAE;EAC3C,OAAO6B,cAAc,sBAAahC,IAAI,CAAC;AACzC,CAAC;AAED,MAAM0C,QAAQ,GAAIC,KAAa,IAAY;EACzC,OAAOvE,MAAM,CAACa,GAAG,CAAC0D,KAAK,CAAC,CAAC7D,KAAK,8BAAoB;AACpD,CAAC;AAED,MAAM8D,QAAQ,GAAIC,OAAe,IAAY;EAC3C,OAAOb,cAAc,0BAAgBa,OAAO,CAAC;AAC/C,CAAC;AAED,MAAMC,cAAc,GAAIb,IAAU,IAAa7C,MAAM,CAACiC,IAAI,CAAC,CAACY,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;AAE1F,MAAMc,WAAW,GAAGD,cAAc,qBAAY;AAC9C,MAAME,UAAU,GAAGF,cAAc,oBAAW;AAC5C,MAAMG,SAAS,GAAGH,cAAc,mBAAU;AAC1C,MAAMI,cAAc,GAAGJ,cAAc,wBAAe;AAEpD,MAAMK,SAAS,GAAG;EAChB7E,OAAO;EACPiB,QAAQ;EACRL,UAAU;EACVM,8BAA8B;EAC9BK,2BAA2B;EAC3BE,KAAK;EACLG,KAAK;EACLa,IAAI;EACJO,OAAO;EACPgB,QAAQ;EACRG,KAAK;EACL3D,KAAK,EAAEA,CAAA,KAAMiE,WAAW;EACxBK,IAAI,EAAEA,CAAA,KAAMJ,UAAU;EACtBK,GAAG,EAAEA,CAAA,KAAMJ,SAAS;EACpBP,QAAQ;EACRY,QAAQ,EAAEA,CAAA,KAAMJ,cAAc;EAC9BN,QAAQ;EACRhB;CACD;AAEQ2B,OAAA,CAAAJ,SAAA,GAAAA,SAAA", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}