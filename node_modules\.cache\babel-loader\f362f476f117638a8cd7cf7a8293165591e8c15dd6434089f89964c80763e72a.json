{"ast": null, "code": "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { backend_util } from '@tensorflow/tfjs-core';\nimport { RotateWithOffset } from '@tensorflow/tfjs-core';\nimport { RotateProgram } from '../rotate_gpu';\nexport const rotateWithOffsetConfig = {\n  kernelName: RotateWithOffset,\n  backendName: 'webgl',\n  kernelFunc: ({\n    inputs,\n    attrs,\n    backend\n  }) => {\n    const {\n      image\n    } = inputs;\n    const {\n      radians,\n      fillValue,\n      center\n    } = attrs;\n    const webglBackend = backend;\n    const program = new RotateProgram(image.shape, fillValue);\n    const [centerX, centerY] = backend_util.getImageCenter(center, image.shape[1], image.shape[2]);\n    const customValues = [[centerX, centerY, Math.sin(radians), Math.cos(radians)]];\n    const output = webglBackend.runWebGLProgram(program, [image], image.dtype, customValues);\n    return output;\n  }\n};", "map": {"version": 3, "names": ["backend_util", "RotateWithOffset", "RotateProgram", "rotateWithOffsetConfig", "kernelName", "backendName", "kernelFunc", "inputs", "attrs", "backend", "image", "radians", "fillValue", "center", "webglBackend", "program", "shape", "centerX", "centerY", "getImageCenter", "customValues", "Math", "sin", "cos", "output", "runWebGLProgram", "dtype"], "sources": ["C:\\tfjs-backend-webgl\\src\\kernels\\RotateWithOffset.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {backend_util, KernelConfig, Tensor4D} from '@tensorflow/tfjs-core';\nimport {RotateWithOffset, RotateWithOffsetAttrs, RotateWithOffsetInputs} from '@tensorflow/tfjs-core';\n\nimport {MathBackendWebGL} from '../backend_webgl';\nimport {RotateProgram} from '../rotate_gpu';\n\nexport const rotateWithOffsetConfig: KernelConfig = {\n  kernelName: RotateWithOffset,\n  backendName: 'webgl',\n  kernelFunc: ({inputs, attrs, backend}) => {\n    const {image} = inputs as RotateWithOffsetInputs;\n    const {radians, fillValue, center} =\n        attrs as unknown as RotateWithOffsetAttrs;\n    const webglBackend = backend as MathBackendWebGL;\n\n    const program = new RotateProgram((image as Tensor4D).shape, fillValue);\n    const [centerX, centerY] =\n        backend_util.getImageCenter(center, image.shape[1], image.shape[2]);\n    const customValues =\n        [[centerX, centerY, Math.sin(radians), Math.cos(radians)]];\n    const output = webglBackend.runWebGLProgram(\n        program, [image], image.dtype, customValues);\n    return output;\n  }\n};\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAiBA,SAAQA,YAAY,QAA+B,uBAAuB;AAC1E,SAAQC,gBAAgB,QAAsD,uBAAuB;AAGrG,SAAQC,aAAa,QAAO,eAAe;AAE3C,OAAO,MAAMC,sBAAsB,GAAiB;EAClDC,UAAU,EAAEH,gBAAgB;EAC5BI,WAAW,EAAE,OAAO;EACpBC,UAAU,EAAEA,CAAC;IAACC,MAAM;IAAEC,KAAK;IAAEC;EAAO,CAAC,KAAI;IACvC,MAAM;MAACC;IAAK,CAAC,GAAGH,MAAgC;IAChD,MAAM;MAACI,OAAO;MAAEC,SAAS;MAAEC;IAAM,CAAC,GAC9BL,KAAyC;IAC7C,MAAMM,YAAY,GAAGL,OAA2B;IAEhD,MAAMM,OAAO,GAAG,IAAIb,aAAa,CAAEQ,KAAkB,CAACM,KAAK,EAAEJ,SAAS,CAAC;IACvE,MAAM,CAACK,OAAO,EAAEC,OAAO,CAAC,GACpBlB,YAAY,CAACmB,cAAc,CAACN,MAAM,EAAEH,KAAK,CAACM,KAAK,CAAC,CAAC,CAAC,EAAEN,KAAK,CAACM,KAAK,CAAC,CAAC,CAAC,CAAC;IACvE,MAAMI,YAAY,GACd,CAAC,CAACH,OAAO,EAAEC,OAAO,EAAEG,IAAI,CAACC,GAAG,CAACX,OAAO,CAAC,EAAEU,IAAI,CAACE,GAAG,CAACZ,OAAO,CAAC,CAAC,CAAC;IAC9D,MAAMa,MAAM,GAAGV,YAAY,CAACW,eAAe,CACvCV,OAAO,EAAE,CAACL,KAAK,CAAC,EAAEA,KAAK,CAACgB,KAAK,EAAEN,YAAY,CAAC;IAChD,OAAOI,MAAM;EACf;CACD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}