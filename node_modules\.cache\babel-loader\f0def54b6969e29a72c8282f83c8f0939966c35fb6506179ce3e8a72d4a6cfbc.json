{"ast": null, "code": "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { IFFT, util } from '@tensorflow/tfjs-core';\nimport { fftBatch } from '../utils/fft_utils';\nimport { reshape } from './Reshape';\nexport function ifft(args) {\n  const {\n    inputs,\n    backend\n  } = args;\n  const {\n    input\n  } = inputs;\n  const inputSize = util.sizeFromShape(input.shape);\n  // Collapse all outer dimensions to a single batch dimension.\n  const innerDimensionSize = input.shape[input.shape.length - 1];\n  const batch = inputSize / innerDimensionSize;\n  const input2D = reshape({\n    inputs: {\n      x: input\n    },\n    backend,\n    attrs: {\n      shape: [batch, innerDimensionSize]\n    }\n  });\n  const result = fftBatch(input2D, true, backend);\n  const resultReshaped = reshape({\n    inputs: {\n      x: result\n    },\n    backend,\n    attrs: {\n      shape: input.shape\n    }\n  });\n  backend.disposeIntermediateTensorInfo(input2D);\n  backend.disposeIntermediateTensorInfo(result);\n  return resultReshaped;\n}\nexport const ifftConfig = {\n  kernelName: IFFT,\n  backendName: 'cpu',\n  kernelFunc: ifft\n};", "map": {"version": 3, "names": ["IFFT", "util", "fftBatch", "reshape", "ifft", "args", "inputs", "backend", "input", "inputSize", "sizeFromShape", "shape", "innerDimensionSize", "length", "batch", "input2D", "x", "attrs", "result", "result<PERSON><PERSON><PERSON><PERSON>", "disposeIntermediateTensorInfo", "ifftConfig", "kernelName", "backendName", "kernelFunc"], "sources": ["C:\\tfjs-backend-cpu\\src\\kernels\\IFFT.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {IFFT, IFFTInputs, KernelConfig, KernelFunc, TensorInfo, util} from '@tensorflow/tfjs-core';\n\nimport {MathBackendCPU} from '../backend_cpu';\nimport {fftBatch} from '../utils/fft_utils';\nimport {reshape} from './Reshape';\n\nexport function ifft(args: {inputs: IFFTInputs, backend: MathBackendCPU}):\n    TensorInfo {\n  const {inputs, backend} = args;\n  const {input} = inputs;\n\n  const inputSize = util.sizeFromShape(input.shape);\n\n  // Collapse all outer dimensions to a single batch dimension.\n  const innerDimensionSize = input.shape[input.shape.length - 1];\n  const batch = inputSize / innerDimensionSize;\n\n  const input2D = reshape({\n    inputs: {x: input},\n    backend,\n    attrs: {shape: [batch, innerDimensionSize]}\n  });\n\n  const result = fftBatch(input2D, true, backend);\n\n  const resultReshaped =\n      reshape({inputs: {x: result}, backend, attrs: {shape: input.shape}});\n\n  backend.disposeIntermediateTensorInfo(input2D);\n  backend.disposeIntermediateTensorInfo(result);\n\n  return resultReshaped;\n}\n\nexport const ifftConfig: KernelConfig = {\n  kernelName: IFFT,\n  backendName: 'cpu',\n  kernelFunc: ifft as unknown as KernelFunc\n};\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAiBA,SAAQA,IAAI,EAAoDC,IAAI,QAAO,uBAAuB;AAGlG,SAAQC,QAAQ,QAAO,oBAAoB;AAC3C,SAAQC,OAAO,QAAO,WAAW;AAEjC,OAAM,SAAUC,IAAIA,CAACC,IAAmD;EAEtE,MAAM;IAACC,MAAM;IAAEC;EAAO,CAAC,GAAGF,IAAI;EAC9B,MAAM;IAACG;EAAK,CAAC,GAAGF,MAAM;EAEtB,MAAMG,SAAS,GAAGR,IAAI,CAACS,aAAa,CAACF,KAAK,CAACG,KAAK,CAAC;EAEjD;EACA,MAAMC,kBAAkB,GAAGJ,KAAK,CAACG,KAAK,CAACH,KAAK,CAACG,KAAK,CAACE,MAAM,GAAG,CAAC,CAAC;EAC9D,MAAMC,KAAK,GAAGL,SAAS,GAAGG,kBAAkB;EAE5C,MAAMG,OAAO,GAAGZ,OAAO,CAAC;IACtBG,MAAM,EAAE;MAACU,CAAC,EAAER;IAAK,CAAC;IAClBD,OAAO;IACPU,KAAK,EAAE;MAACN,KAAK,EAAE,CAACG,KAAK,EAAEF,kBAAkB;IAAC;GAC3C,CAAC;EAEF,MAAMM,MAAM,GAAGhB,QAAQ,CAACa,OAAO,EAAE,IAAI,EAAER,OAAO,CAAC;EAE/C,MAAMY,cAAc,GAChBhB,OAAO,CAAC;IAACG,MAAM,EAAE;MAACU,CAAC,EAAEE;IAAM,CAAC;IAAEX,OAAO;IAAEU,KAAK,EAAE;MAACN,KAAK,EAAEH,KAAK,CAACG;IAAK;EAAC,CAAC,CAAC;EAExEJ,OAAO,CAACa,6BAA6B,CAACL,OAAO,CAAC;EAC9CR,OAAO,CAACa,6BAA6B,CAACF,MAAM,CAAC;EAE7C,OAAOC,cAAc;AACvB;AAEA,OAAO,MAAME,UAAU,GAAiB;EACtCC,UAAU,EAAEtB,IAAI;EAChBuB,WAAW,EAAE,KAAK;EAClBC,UAAU,EAAEpB;CACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}