{"ast": null, "code": "import { convertToTensor } from '../tensor_util_env';\nimport * as util from '../util';\nimport { matMul } from './mat_mul';\nimport { op } from './operation';\nimport { reshape } from './reshape';\n/**\n * Computes the outer product of two vectors, `v1` and `v2`.\n *\n * ```js\n * const a = tf.tensor1d([1, 2, 3]);\n * const b = tf.tensor1d([3, 4, 5]);\n *\n * tf.outerProduct(a, b).print();\n * ```\n * @param v1 The first vector in the outer product operation.\n * @param v2 The second vector in the outer product operation.\n *\n * @doc {heading: 'Operations', subheading: 'Matrices'}\n */\nfunction outerProduct_(v1, v2) {\n  const $v1 = convertToTensor(v1, 'v1', 'outerProduct');\n  const $v2 = convertToTensor(v2, 'v2', 'outerProduct');\n  util.assert($v1.rank === 1 && $v2.rank === 1, () => `Error in outerProduct: inputs must be rank 1, but got ranks ` + `${$v1.rank} and ${$v2.rank}.`);\n  const v12D = reshape($v1, [-1, 1]);\n  const v22D = reshape($v2, [1, -1]);\n  return matMul(v12D, v22D);\n}\nexport const outerProduct = /* @__PURE__ */op({\n  outerProduct_\n});", "map": {"version": 3, "names": ["convertToTensor", "util", "<PERSON><PERSON><PERSON>", "op", "reshape", "outerProduct_", "v1", "v2", "$v1", "$v2", "assert", "rank", "v12D", "v22D", "outerProduct"], "sources": ["C:\\tfjs-core\\src\\ops\\outer_product.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport {Tensor1D, Tensor2D} from '../tensor';\nimport {convertToTensor} from '../tensor_util_env';\nimport {TensorLike} from '../types';\nimport * as util from '../util';\n\nimport {matMul} from './mat_mul';\nimport {op} from './operation';\nimport {reshape} from './reshape';\n\n/**\n * Computes the outer product of two vectors, `v1` and `v2`.\n *\n * ```js\n * const a = tf.tensor1d([1, 2, 3]);\n * const b = tf.tensor1d([3, 4, 5]);\n *\n * tf.outerProduct(a, b).print();\n * ```\n * @param v1 The first vector in the outer product operation.\n * @param v2 The second vector in the outer product operation.\n *\n * @doc {heading: 'Operations', subheading: 'Matrices'}\n */\nfunction outerProduct_(\n    v1: Tensor1D|TensorLike, v2: Tensor1D|TensorLike): Tensor2D {\n  const $v1 = convertToTensor(v1, 'v1', 'outerProduct');\n  const $v2 = convertToTensor(v2, 'v2', 'outerProduct');\n\n  util.assert(\n      $v1.rank === 1 && $v2.rank === 1,\n      () => `Error in outerProduct: inputs must be rank 1, but got ranks ` +\n          `${$v1.rank} and ${$v2.rank}.`);\n\n  const v12D = reshape($v1, [-1, 1]);\n  const v22D = reshape($v2, [1, -1]);\n  return matMul(v12D, v22D);\n}\n\nexport const outerProduct = /* @__PURE__ */ op({outerProduct_});\n"], "mappings": "AAiBA,SAAQA,eAAe,QAAO,oBAAoB;AAElD,OAAO,KAAKC,IAAI,MAAM,SAAS;AAE/B,SAAQC,MAAM,QAAO,WAAW;AAChC,SAAQC,EAAE,QAAO,aAAa;AAC9B,SAAQC,OAAO,QAAO,WAAW;AAEjC;;;;;;;;;;;;;;AAcA,SAASC,aAAaA,CAClBC,EAAuB,EAAEC,EAAuB;EAClD,MAAMC,GAAG,GAAGR,eAAe,CAACM,EAAE,EAAE,IAAI,EAAE,cAAc,CAAC;EACrD,MAAMG,GAAG,GAAGT,eAAe,CAACO,EAAE,EAAE,IAAI,EAAE,cAAc,CAAC;EAErDN,IAAI,CAACS,MAAM,CACPF,GAAG,CAACG,IAAI,KAAK,CAAC,IAAIF,GAAG,CAACE,IAAI,KAAK,CAAC,EAChC,MAAM,8DAA8D,GAChE,GAAGH,GAAG,CAACG,IAAI,QAAQF,GAAG,CAACE,IAAI,GAAG,CAAC;EAEvC,MAAMC,IAAI,GAAGR,OAAO,CAACI,GAAG,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EAClC,MAAMK,IAAI,GAAGT,OAAO,CAACK,GAAG,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EAClC,OAAOP,MAAM,CAACU,IAAI,EAAEC,IAAI,CAAC;AAC3B;AAEA,OAAO,MAAMC,YAAY,GAAG,eAAgBX,EAAE,CAAC;EAACE;AAAa,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}