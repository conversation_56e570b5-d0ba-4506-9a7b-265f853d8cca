{"ast": null, "code": "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { ENGINE } from '../engine';\nimport { ResizeNearestNeighbor, ResizeNearestNeighborGrad } from '../kernel_names';\nexport const resizeNearestNeighborGradConfig = {\n  kernelName: ResizeNearestNeighbor,\n  inputsToSave: ['images'],\n  gradFunc: (dy, saved, attrs) => {\n    const [images] = saved;\n    const inputs = {\n      dy,\n      images\n    };\n    const imagesDer = () =>\n    // tslint:disable-next-line: no-unnecessary-type-assertion\n    ENGINE.runKernel(ResizeNearestNeighborGrad, inputs, attrs);\n    return {\n      images: imagesDer\n    };\n  }\n};", "map": {"version": 3, "names": ["ENGINE", "ResizeNearestNeighbor", "ResizeNearestNeighborGrad", "resizeNearestNeighborGradConfig", "kernelName", "inputsToSave", "grad<PERSON>unc", "dy", "saved", "attrs", "images", "inputs", "imagesDer", "runKernel"], "sources": ["C:\\tfjs-core\\src\\gradients\\ResizeNearestNeighbor_grad.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport {ENGINE} from '../engine';\nimport {ResizeNearestNeighbor, ResizeNearestNeighborGrad, ResizeNearestNeighborGradInputs} from '../kernel_names';\nimport {GradConfig} from '../kernel_registry';\nimport {NamedAttrMap} from '../kernel_registry';\nimport {Tensor, Tensor4D} from '../tensor';\nimport {NamedTensorMap} from '../tensor_types';\n\nexport const resizeNearestNeighborGradConfig: GradConfig = {\n  kernelName: ResizeNearestNeighbor,\n  inputsToSave: ['images'],\n  gradFunc: (dy: Tensor4D, saved: Tensor[], attrs: NamedAttrMap) => {\n    const [images] = saved;\n\n    const inputs: ResizeNearestNeighborGradInputs = {dy, images};\n    const imagesDer = () =>\n        // tslint:disable-next-line: no-unnecessary-type-assertion\n        ENGINE.runKernel(\n            ResizeNearestNeighborGrad, inputs as unknown as NamedTensorMap,\n            attrs) as Tensor4D;\n\n    return {images: imagesDer};\n  }\n};\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAgBA,SAAQA,MAAM,QAAO,WAAW;AAChC,SAAQC,qBAAqB,EAAEC,yBAAyB,QAAwC,iBAAiB;AAMjH,OAAO,MAAMC,+BAA+B,GAAe;EACzDC,UAAU,EAAEH,qBAAqB;EACjCI,YAAY,EAAE,CAAC,QAAQ,CAAC;EACxBC,QAAQ,EAAEA,CAACC,EAAY,EAAEC,KAAe,EAAEC,KAAmB,KAAI;IAC/D,MAAM,CAACC,MAAM,CAAC,GAAGF,KAAK;IAEtB,MAAMG,MAAM,GAAoC;MAACJ,EAAE;MAAEG;IAAM,CAAC;IAC5D,MAAME,SAAS,GAAGA,CAAA;IACd;IACAZ,MAAM,CAACa,SAAS,CACZX,yBAAyB,EAAES,MAAmC,EAC9DF,KAAK,CAAa;IAE1B,OAAO;MAACC,MAAM,EAAEE;IAAS,CAAC;EAC5B;CACD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}