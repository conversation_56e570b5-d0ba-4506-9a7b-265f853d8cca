{"ast": null, "code": "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { ENGINE } from '../engine';\nimport { MaxPool } from '../kernel_names';\nimport { convertToTensor } from '../tensor_util_env';\nimport * as util from '../util';\nimport * as conv_util from './conv_util';\nimport { op } from './operation';\nimport { reshape } from './reshape';\n/**\n * Computes the 2D max pooling of an image.\n *\n * @param x The input tensor, of rank 4 or rank 3 of shape\n *     `[batch, height, width, inChannels]`. If rank 3, batch of 1 is assumed.\n * @param filterSize The filter size: `[filterHeight, filterWidth]`. If\n *     `filterSize` is a single number, then `filterHeight == filterWidth`.\n * @param strides The strides of the pooling: `[strideHeight, strideWidth]`. If\n *     `strides` is a single number, then `strideHeight == strideWidth`.\n * @param dilations The dilation rates: `[dilationHeight, dilationWidth]`\n *     in which we sample input values across the height and width dimensions\n *     in dilated pooling. Defaults to `[1, 1]`. If `dilations` is a single\n *     number, then `dilationHeight == dilationWidth`. If it is greater than\n *     1, then all values of `strides` must be 1.\n * @param pad The type of padding algorithm.\n *    - `same` and stride 1: output will be of same size as input,\n *       regardless of filter size.\n *    - `valid`: output will be smaller than input if filter is larger\n *       than 1x1.\n *    - For more info, see this guide:\n *     [https://www.tensorflow.org/api_docs/python/tf/nn/convolution](\n *          https://www.tensorflow.org/api_docs/python/tf/nn/convolution)\n * @param dimRoundingMode A string from: 'ceil', 'round', 'floor'. If none is\n *     provided, it will default to truncate.\n */\nfunction maxPool_(x, filterSize, strides, pad, dimRoundingMode) {\n  const $x = convertToTensor(x, 'x', 'maxPool');\n  const dilations = 1;\n  let x4D = $x;\n  let reshapedTo4D = false;\n  if ($x.rank === 3) {\n    reshapedTo4D = true;\n    x4D = reshape($x, [1, $x.shape[0], $x.shape[1], $x.shape[2]]);\n  }\n  util.assert(x4D.rank === 4, () => `Error in maxPool: input must be rank 4 but got rank ${x4D.rank}.`);\n  util.assert(conv_util.eitherStridesOrDilationsAreOne(strides, dilations), () => 'Error in maxPool: Either strides or dilations must be 1. ' + `Got strides ${strides} and dilations '${dilations}'`);\n  conv_util.checkPadOnDimRoundingMode('maxPool', pad, dimRoundingMode);\n  const inputs = {\n    x: x4D\n  };\n  const attrs = {\n    filterSize,\n    strides,\n    pad,\n    dimRoundingMode\n  };\n  // tslint:disable-next-line: no-unnecessary-type-assertion\n  const res = ENGINE.runKernel(MaxPool, inputs, attrs);\n  if (reshapedTo4D) {\n    return reshape(res, [res.shape[1], res.shape[2], res.shape[3]]);\n  }\n  return res;\n}\nexport const maxPool = /* @__PURE__ */op({\n  maxPool_\n});", "map": {"version": 3, "names": ["ENGINE", "MaxPool", "convertToTensor", "util", "conv_util", "op", "reshape", "maxPool_", "x", "filterSize", "strides", "pad", "dimRoundingMode", "$x", "dilations", "x4D", "reshapedTo4D", "rank", "shape", "assert", "eitherStridesOrDilationsAreOne", "checkPadOnDimRoundingMode", "inputs", "attrs", "res", "runKernel", "maxPool"], "sources": ["C:\\tfjs-core\\src\\ops\\max_pool.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {ENGINE} from '../engine';\nimport {MaxPool, MaxPoolAttrs, MaxPoolInputs} from '../kernel_names';\nimport {NamedAttrMap} from '../kernel_registry';\nimport {Tensor3D, Tensor4D} from '../tensor';\nimport {NamedTensorMap} from '../tensor_types';\nimport {convertToTensor} from '../tensor_util_env';\nimport {TensorLike} from '../types';\nimport * as util from '../util';\n\nimport * as conv_util from './conv_util';\nimport {op} from './operation';\nimport {reshape} from './reshape';\n\n/**\n * Computes the 2D max pooling of an image.\n *\n * @param x The input tensor, of rank 4 or rank 3 of shape\n *     `[batch, height, width, inChannels]`. If rank 3, batch of 1 is assumed.\n * @param filterSize The filter size: `[filterHeight, filterWidth]`. If\n *     `filterSize` is a single number, then `filterHeight == filterWidth`.\n * @param strides The strides of the pooling: `[strideHeight, strideWidth]`. If\n *     `strides` is a single number, then `strideHeight == strideWidth`.\n * @param dilations The dilation rates: `[dilationHeight, dilationWidth]`\n *     in which we sample input values across the height and width dimensions\n *     in dilated pooling. Defaults to `[1, 1]`. If `dilations` is a single\n *     number, then `dilationHeight == dilationWidth`. If it is greater than\n *     1, then all values of `strides` must be 1.\n * @param pad The type of padding algorithm.\n *    - `same` and stride 1: output will be of same size as input,\n *       regardless of filter size.\n *    - `valid`: output will be smaller than input if filter is larger\n *       than 1x1.\n *    - For more info, see this guide:\n *     [https://www.tensorflow.org/api_docs/python/tf/nn/convolution](\n *          https://www.tensorflow.org/api_docs/python/tf/nn/convolution)\n * @param dimRoundingMode A string from: 'ceil', 'round', 'floor'. If none is\n *     provided, it will default to truncate.\n */\nfunction maxPool_<T extends Tensor3D|Tensor4D>(\n    x: T|TensorLike, filterSize: [number, number]|number,\n    strides: [number, number]|number,\n    pad: 'valid'|'same'|number|conv_util.ExplicitPadding,\n    dimRoundingMode?: 'floor'|'round'|'ceil'): T {\n  const $x = convertToTensor(x, 'x', 'maxPool');\n  const dilations = 1;\n\n  let x4D = $x as Tensor4D;\n  let reshapedTo4D = false;\n  if ($x.rank === 3) {\n    reshapedTo4D = true;\n    x4D = reshape($x, [1, $x.shape[0], $x.shape[1], $x.shape[2]]);\n  }\n\n  util.assert(\n      x4D.rank === 4,\n      () => `Error in maxPool: input must be rank 4 but got rank ${x4D.rank}.`);\n  util.assert(\n      conv_util.eitherStridesOrDilationsAreOne(strides, dilations),\n      () => 'Error in maxPool: Either strides or dilations must be 1. ' +\n          `Got strides ${strides} and dilations '${dilations}'`);\n  conv_util.checkPadOnDimRoundingMode('maxPool', pad, dimRoundingMode);\n  const inputs: MaxPoolInputs = {x: x4D};\n  const attrs: MaxPoolAttrs = {filterSize, strides, pad, dimRoundingMode};\n\n  // tslint:disable-next-line: no-unnecessary-type-assertion\n  const res = ENGINE.runKernel(\n                  MaxPool, inputs as unknown as NamedTensorMap,\n                  attrs as unknown as NamedAttrMap) as T;\n\n  if (reshapedTo4D) {\n    return reshape(res, [res.shape[1], res.shape[2], res.shape[3]]) as T;\n  }\n  return res;\n}\n\nexport const maxPool = /* @__PURE__ */ op({maxPool_});\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAiBA,SAAQA,MAAM,QAAO,WAAW;AAChC,SAAQC,OAAO,QAAoC,iBAAiB;AAIpE,SAAQC,eAAe,QAAO,oBAAoB;AAElD,OAAO,KAAKC,IAAI,MAAM,SAAS;AAE/B,OAAO,KAAKC,SAAS,MAAM,aAAa;AACxC,SAAQC,EAAE,QAAO,aAAa;AAC9B,SAAQC,OAAO,QAAO,WAAW;AAEjC;;;;;;;;;;;;;;;;;;;;;;;;;AAyBA,SAASC,QAAQA,CACbC,CAAe,EAAEC,UAAmC,EACpDC,OAAgC,EAChCC,GAAoD,EACpDC,eAAwC;EAC1C,MAAMC,EAAE,GAAGX,eAAe,CAACM,CAAC,EAAE,GAAG,EAAE,SAAS,CAAC;EAC7C,MAAMM,SAAS,GAAG,CAAC;EAEnB,IAAIC,GAAG,GAAGF,EAAc;EACxB,IAAIG,YAAY,GAAG,KAAK;EACxB,IAAIH,EAAE,CAACI,IAAI,KAAK,CAAC,EAAE;IACjBD,YAAY,GAAG,IAAI;IACnBD,GAAG,GAAGT,OAAO,CAACO,EAAE,EAAE,CAAC,CAAC,EAAEA,EAAE,CAACK,KAAK,CAAC,CAAC,CAAC,EAAEL,EAAE,CAACK,KAAK,CAAC,CAAC,CAAC,EAAEL,EAAE,CAACK,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;;EAG/Df,IAAI,CAACgB,MAAM,CACPJ,GAAG,CAACE,IAAI,KAAK,CAAC,EACd,MAAM,uDAAuDF,GAAG,CAACE,IAAI,GAAG,CAAC;EAC7Ed,IAAI,CAACgB,MAAM,CACPf,SAAS,CAACgB,8BAA8B,CAACV,OAAO,EAAEI,SAAS,CAAC,EAC5D,MAAM,2DAA2D,GAC7D,eAAeJ,OAAO,mBAAmBI,SAAS,GAAG,CAAC;EAC9DV,SAAS,CAACiB,yBAAyB,CAAC,SAAS,EAAEV,GAAG,EAAEC,eAAe,CAAC;EACpE,MAAMU,MAAM,GAAkB;IAACd,CAAC,EAAEO;EAAG,CAAC;EACtC,MAAMQ,KAAK,GAAiB;IAACd,UAAU;IAAEC,OAAO;IAAEC,GAAG;IAAEC;EAAe,CAAC;EAEvE;EACA,MAAMY,GAAG,GAAGxB,MAAM,CAACyB,SAAS,CACZxB,OAAO,EAAEqB,MAAmC,EAC5CC,KAAgC,CAAM;EAEtD,IAAIP,YAAY,EAAE;IAChB,OAAOV,OAAO,CAACkB,GAAG,EAAE,CAACA,GAAG,CAACN,KAAK,CAAC,CAAC,CAAC,EAAEM,GAAG,CAACN,KAAK,CAAC,CAAC,CAAC,EAAEM,GAAG,CAACN,KAAK,CAAC,CAAC,CAAC,CAAC,CAAM;;EAEtE,OAAOM,GAAG;AACZ;AAEA,OAAO,MAAME,OAAO,GAAG,eAAgBrB,EAAE,CAAC;EAACE;AAAQ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}