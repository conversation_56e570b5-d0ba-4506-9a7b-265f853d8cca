{"ast": null, "code": "/*\nCopyright (c) 2011, <PERSON>\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in\nall copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\nTHE SOFTWARE.\n*/\n\n'use strict';\n\nconst fs = require('fs');\nconst EventEmitter = require('events');\nconst PorterStemmer = require('../stemmers/porter_stemmer');\nconst parallelTrainer = require('./classifier_train_parallel');\nclass Classifier extends EventEmitter {\n  constructor(classifier, stemmer) {\n    super();\n    this.classifier = classifier;\n    this.docs = [];\n    this.features = {};\n    this.stemmer = stemmer || PorterStemmer;\n    this.lastAdded = 0;\n\n    // Add methods for parallel training\n    this.Threads = parallelTrainer.Threads;\n    this.trainParallel = parallelTrainer.trainParallel;\n    this.retrainParallel = parallelTrainer.retrainParallel;\n    this.trainParallelBatches = parallelTrainer.trainParallelBatches;\n  }\n  addDocument(text, classification) {\n    // Ignore further processing if classification is undefined\n    if (typeof classification === 'undefined') return;\n\n    // If classification is type of string then make sure it's dosen't have blank space at both end\n    if (typeof classification === 'string') {\n      classification = classification.trim();\n    }\n    if (typeof text === 'string') {\n      text = this.stemmer.tokenizeAndStem(text, this.keepStops);\n    }\n    if (text.length === 0) {\n      // ignore empty documents\n      return;\n    }\n    this.docs.push({\n      label: classification,\n      text\n    });\n    for (let i = 0; i < text.length; i++) {\n      const token = text[i];\n      this.features[token] = (this.features[token] || 0) + 1;\n    }\n  }\n  removeDocument(text, classification) {\n    const docs = this.docs;\n    let doc;\n    let pos;\n    if (typeof text === 'string') {\n      text = this.stemmer.tokenizeAndStem(text, this.keepStops);\n    }\n    for (let i = 0, ii = docs.length; i < ii; i++) {\n      doc = docs[i];\n      if (doc.text.join(' ') === text.join(' ') && doc.label === classification) {\n        pos = i;\n      }\n    }\n\n    // Remove if there's a match\n    if (!isNaN(pos)) {\n      this.docs.splice(pos, 1);\n      for (let i = 0, ii = text.length; i < ii; i++) {\n        delete this.features[text[i]];\n      }\n    }\n  }\n  textToFeatures(observation) {\n    const features = [];\n    if (typeof observation === 'string') {\n      observation = this.stemmer.tokenizeAndStem(observation, this.keepStops);\n    }\n    for (const feature in this.features) {\n      if (observation.indexOf(feature) > -1) {\n        features.push(1);\n      } else {\n        features.push(0);\n      }\n    }\n    return features;\n  }\n  train() {\n    const totalDocs = this.docs.length;\n    for (let i = this.lastAdded; i < totalDocs; i++) {\n      const features = this.textToFeatures(this.docs[i].text);\n      this.classifier.addExample(features, this.docs[i].label);\n      this.emit('trainedWithDocument', {\n        index: i,\n        total: totalDocs,\n        doc: this.docs[i]\n      });\n      this.lastAdded++;\n    }\n    this.classifier.train();\n    this.emit('doneTraining', true);\n  }\n  retrain() {\n    this.classifier = new this.classifier.constructor();\n    this.lastAdded = 0;\n    this.train();\n  }\n  getClassifications(observation) {\n    return this.classifier.getClassifications(this.textToFeatures(observation));\n  }\n  classify(observation) {\n    return this.classifier.classify(this.textToFeatures(observation));\n  }\n  static restore(classifier, stemmer) {\n    classifier.stemmer = stemmer || PorterStemmer;\n    return classifier;\n  }\n  save(filename, callback) {\n    const data = JSON.stringify(this);\n    const classifier = this;\n    fs.writeFile(filename, data, 'utf8', function (err) {\n      if (callback) {\n        callback(err, err ? null : classifier);\n      }\n    });\n  }\n  static load(filename, stemmer, callback) {\n    if (!callback) {\n      return;\n    }\n    fs.readFile(filename, 'utf8', function (err, data) {\n      if (err) {\n        callback(err, null);\n      } else {\n        const classifier = JSON.parse(data);\n        callback(err, Classifier.restore(classifier, stemmer));\n      }\n    });\n  }\n  async saveTo(storageBackend) {\n    return await storageBackend.store(this);\n  }\n  static async loadFrom(key, storageBackend) {\n    const obj = await storageBackend.retrieve(key);\n    Object.setPrototypeOf(obj, Classifier.prototype);\n    return obj;\n  }\n  setOptions(options) {\n    this.keepStops = !!options.keepStops;\n  }\n}\nmodule.exports = Classifier;", "map": {"version": 3, "names": ["fs", "require", "EventEmitter", "PorterStemmer", "parallelTrainer", "Classifier", "constructor", "classifier", "stemmer", "docs", "features", "lastAdded", "Threads", "trainParallel", "retrainParallel", "trainParallelBatches", "addDocument", "text", "classification", "trim", "tokenizeAndStem", "keepStops", "length", "push", "label", "i", "token", "removeDocument", "doc", "pos", "ii", "join", "isNaN", "splice", "textToFeatures", "observation", "feature", "indexOf", "train", "totalDocs", "addExample", "emit", "index", "total", "retrain", "getClassifications", "classify", "restore", "save", "filename", "callback", "data", "JSON", "stringify", "writeFile", "err", "load", "readFile", "parse", "saveTo", "storageBackend", "store", "loadFrom", "key", "obj", "retrieve", "Object", "setPrototypeOf", "prototype", "setOptions", "options", "module", "exports"], "sources": ["C:/tmsft/node_modules/natural/lib/natural/classifiers/classifier.js"], "sourcesContent": ["/*\nCopyright (c) 2011, <PERSON>\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in\nall copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\nTHE SOFTWARE.\n*/\n\n'use strict'\nconst fs = require('fs')\n\nconst EventEmitter = require('events')\nconst PorterStemmer = require('../stemmers/porter_stemmer')\nconst parallelTrainer = require('./classifier_train_parallel')\n\nclass Classifier extends EventEmitter {\n  constructor (classifier, stemmer) {\n    super()\n    this.classifier = classifier\n    this.docs = []\n    this.features = {}\n    this.stemmer = stemmer || PorterStemmer\n    this.lastAdded = 0\n\n    // Add methods for parallel training\n    this.Threads = parallelTrainer.Threads\n    this.trainParallel = parallelTrainer.trainParallel\n    this.retrainParallel = parallelTrainer.retrainParallel\n    this.trainParallelBatches = parallelTrainer.trainParallelBatches\n  }\n\n  addDocument (text, classification) {\n    // Ignore further processing if classification is undefined\n    if (typeof classification === 'undefined') return\n\n    // If classification is type of string then make sure it's dosen't have blank space at both end\n    if (typeof classification === 'string') {\n      classification = classification.trim()\n    }\n\n    if (typeof text === 'string') { text = this.stemmer.tokenizeAndStem(text, this.keepStops) }\n\n    if (text.length === 0) {\n      // ignore empty documents\n      return\n    }\n\n    this.docs.push({\n      label: classification,\n      text\n    })\n\n    for (let i = 0; i < text.length; i++) {\n      const token = text[i]\n      this.features[token] = (this.features[token] || 0) + 1\n    }\n  }\n\n  removeDocument (text, classification) {\n    const docs = this.docs\n    let doc\n    let pos\n\n    if (typeof text === 'string') {\n      text = this.stemmer.tokenizeAndStem(text, this.keepStops)\n    }\n\n    for (let i = 0, ii = docs.length; i < ii; i++) {\n      doc = docs[i]\n      if (doc.text.join(' ') === text.join(' ') &&\n          doc.label === classification) {\n        pos = i\n      }\n    }\n\n    // Remove if there's a match\n    if (!isNaN(pos)) {\n      this.docs.splice(pos, 1)\n\n      for (let i = 0, ii = text.length; i < ii; i++) {\n        delete this.features[text[i]]\n      }\n    }\n  }\n\n  textToFeatures (observation) {\n    const features = []\n\n    if (typeof observation === 'string') { observation = this.stemmer.tokenizeAndStem(observation, this.keepStops) }\n\n    for (const feature in this.features) {\n      if (observation.indexOf(feature) > -1) {\n        features.push(1)\n      } else {\n        features.push(0)\n      }\n    }\n\n    return features\n  }\n\n  train () {\n    const totalDocs = this.docs.length\n    for (let i = this.lastAdded; i < totalDocs; i++) {\n      const features = this.textToFeatures(this.docs[i].text)\n      this.classifier.addExample(features, this.docs[i].label)\n      this.emit('trainedWithDocument', { index: i, total: totalDocs, doc: this.docs[i] })\n      this.lastAdded++\n    }\n    this.classifier.train()\n    this.emit('doneTraining', true)\n  }\n\n  retrain () {\n    this.classifier = new (this.classifier.constructor)()\n    this.lastAdded = 0\n    this.train()\n  }\n\n  getClassifications (observation) {\n    return this.classifier.getClassifications(this.textToFeatures(observation))\n  }\n\n  classify (observation) {\n    return this.classifier.classify(this.textToFeatures(observation))\n  }\n\n  static restore (classifier, stemmer) {\n    classifier.stemmer = stemmer || PorterStemmer\n    return classifier\n  }\n\n  save (filename, callback) {\n    const data = JSON.stringify(this)\n    const classifier = this\n    fs.writeFile(filename, data, 'utf8', function (err) {\n      if (callback) {\n        callback(err, err ? null : classifier)\n      }\n    })\n  }\n\n  static load (filename, stemmer, callback) {\n    if (!callback) {\n      return\n    }\n    fs.readFile(filename, 'utf8', function (err, data) {\n      if (err) {\n        callback(err, null)\n      } else {\n        const classifier = JSON.parse(data)\n        callback(err, Classifier.restore(classifier, stemmer))\n      }\n    })\n  }\n\n  async saveTo (storageBackend) {\n    return await storageBackend.store(this)\n  }\n\n  static async loadFrom (key, storageBackend) {\n    const obj = await storageBackend.retrieve(key)\n    Object.setPrototypeOf(obj, Classifier.prototype)\n    return obj\n  }\n\n  setOptions (options) {\n    this.keepStops = !!(options.keepStops)\n  }\n}\n\nmodule.exports = Classifier\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,YAAY;;AACZ,MAAMA,EAAE,GAAGC,OAAO,CAAC,IAAI,CAAC;AAExB,MAAMC,YAAY,GAAGD,OAAO,CAAC,QAAQ,CAAC;AACtC,MAAME,aAAa,GAAGF,OAAO,CAAC,4BAA4B,CAAC;AAC3D,MAAMG,eAAe,GAAGH,OAAO,CAAC,6BAA6B,CAAC;AAE9D,MAAMI,UAAU,SAASH,YAAY,CAAC;EACpCI,WAAWA,CAAEC,UAAU,EAAEC,OAAO,EAAE;IAChC,KAAK,CAAC,CAAC;IACP,IAAI,CAACD,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACE,IAAI,GAAG,EAAE;IACd,IAAI,CAACC,QAAQ,GAAG,CAAC,CAAC;IAClB,IAAI,CAACF,OAAO,GAAGA,OAAO,IAAIL,aAAa;IACvC,IAAI,CAACQ,SAAS,GAAG,CAAC;;IAElB;IACA,IAAI,CAACC,OAAO,GAAGR,eAAe,CAACQ,OAAO;IACtC,IAAI,CAACC,aAAa,GAAGT,eAAe,CAACS,aAAa;IAClD,IAAI,CAACC,eAAe,GAAGV,eAAe,CAACU,eAAe;IACtD,IAAI,CAACC,oBAAoB,GAAGX,eAAe,CAACW,oBAAoB;EAClE;EAEAC,WAAWA,CAAEC,IAAI,EAAEC,cAAc,EAAE;IACjC;IACA,IAAI,OAAOA,cAAc,KAAK,WAAW,EAAE;;IAE3C;IACA,IAAI,OAAOA,cAAc,KAAK,QAAQ,EAAE;MACtCA,cAAc,GAAGA,cAAc,CAACC,IAAI,CAAC,CAAC;IACxC;IAEA,IAAI,OAAOF,IAAI,KAAK,QAAQ,EAAE;MAAEA,IAAI,GAAG,IAAI,CAACT,OAAO,CAACY,eAAe,CAACH,IAAI,EAAE,IAAI,CAACI,SAAS,CAAC;IAAC;IAE1F,IAAIJ,IAAI,CAACK,MAAM,KAAK,CAAC,EAAE;MACrB;MACA;IACF;IAEA,IAAI,CAACb,IAAI,CAACc,IAAI,CAAC;MACbC,KAAK,EAAEN,cAAc;MACrBD;IACF,CAAC,CAAC;IAEF,KAAK,IAAIQ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGR,IAAI,CAACK,MAAM,EAAEG,CAAC,EAAE,EAAE;MACpC,MAAMC,KAAK,GAAGT,IAAI,CAACQ,CAAC,CAAC;MACrB,IAAI,CAACf,QAAQ,CAACgB,KAAK,CAAC,GAAG,CAAC,IAAI,CAAChB,QAAQ,CAACgB,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC;IACxD;EACF;EAEAC,cAAcA,CAAEV,IAAI,EAAEC,cAAc,EAAE;IACpC,MAAMT,IAAI,GAAG,IAAI,CAACA,IAAI;IACtB,IAAImB,GAAG;IACP,IAAIC,GAAG;IAEP,IAAI,OAAOZ,IAAI,KAAK,QAAQ,EAAE;MAC5BA,IAAI,GAAG,IAAI,CAACT,OAAO,CAACY,eAAe,CAACH,IAAI,EAAE,IAAI,CAACI,SAAS,CAAC;IAC3D;IAEA,KAAK,IAAII,CAAC,GAAG,CAAC,EAAEK,EAAE,GAAGrB,IAAI,CAACa,MAAM,EAAEG,CAAC,GAAGK,EAAE,EAAEL,CAAC,EAAE,EAAE;MAC7CG,GAAG,GAAGnB,IAAI,CAACgB,CAAC,CAAC;MACb,IAAIG,GAAG,CAACX,IAAI,CAACc,IAAI,CAAC,GAAG,CAAC,KAAKd,IAAI,CAACc,IAAI,CAAC,GAAG,CAAC,IACrCH,GAAG,CAACJ,KAAK,KAAKN,cAAc,EAAE;QAChCW,GAAG,GAAGJ,CAAC;MACT;IACF;;IAEA;IACA,IAAI,CAACO,KAAK,CAACH,GAAG,CAAC,EAAE;MACf,IAAI,CAACpB,IAAI,CAACwB,MAAM,CAACJ,GAAG,EAAE,CAAC,CAAC;MAExB,KAAK,IAAIJ,CAAC,GAAG,CAAC,EAAEK,EAAE,GAAGb,IAAI,CAACK,MAAM,EAAEG,CAAC,GAAGK,EAAE,EAAEL,CAAC,EAAE,EAAE;QAC7C,OAAO,IAAI,CAACf,QAAQ,CAACO,IAAI,CAACQ,CAAC,CAAC,CAAC;MAC/B;IACF;EACF;EAEAS,cAAcA,CAAEC,WAAW,EAAE;IAC3B,MAAMzB,QAAQ,GAAG,EAAE;IAEnB,IAAI,OAAOyB,WAAW,KAAK,QAAQ,EAAE;MAAEA,WAAW,GAAG,IAAI,CAAC3B,OAAO,CAACY,eAAe,CAACe,WAAW,EAAE,IAAI,CAACd,SAAS,CAAC;IAAC;IAE/G,KAAK,MAAMe,OAAO,IAAI,IAAI,CAAC1B,QAAQ,EAAE;MACnC,IAAIyB,WAAW,CAACE,OAAO,CAACD,OAAO,CAAC,GAAG,CAAC,CAAC,EAAE;QACrC1B,QAAQ,CAACa,IAAI,CAAC,CAAC,CAAC;MAClB,CAAC,MAAM;QACLb,QAAQ,CAACa,IAAI,CAAC,CAAC,CAAC;MAClB;IACF;IAEA,OAAOb,QAAQ;EACjB;EAEA4B,KAAKA,CAAA,EAAI;IACP,MAAMC,SAAS,GAAG,IAAI,CAAC9B,IAAI,CAACa,MAAM;IAClC,KAAK,IAAIG,CAAC,GAAG,IAAI,CAACd,SAAS,EAAEc,CAAC,GAAGc,SAAS,EAAEd,CAAC,EAAE,EAAE;MAC/C,MAAMf,QAAQ,GAAG,IAAI,CAACwB,cAAc,CAAC,IAAI,CAACzB,IAAI,CAACgB,CAAC,CAAC,CAACR,IAAI,CAAC;MACvD,IAAI,CAACV,UAAU,CAACiC,UAAU,CAAC9B,QAAQ,EAAE,IAAI,CAACD,IAAI,CAACgB,CAAC,CAAC,CAACD,KAAK,CAAC;MACxD,IAAI,CAACiB,IAAI,CAAC,qBAAqB,EAAE;QAAEC,KAAK,EAAEjB,CAAC;QAAEkB,KAAK,EAAEJ,SAAS;QAAEX,GAAG,EAAE,IAAI,CAACnB,IAAI,CAACgB,CAAC;MAAE,CAAC,CAAC;MACnF,IAAI,CAACd,SAAS,EAAE;IAClB;IACA,IAAI,CAACJ,UAAU,CAAC+B,KAAK,CAAC,CAAC;IACvB,IAAI,CAACG,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC;EACjC;EAEAG,OAAOA,CAAA,EAAI;IACT,IAAI,CAACrC,UAAU,GAAG,IAAK,IAAI,CAACA,UAAU,CAACD,WAAW,CAAE,CAAC;IACrD,IAAI,CAACK,SAAS,GAAG,CAAC;IAClB,IAAI,CAAC2B,KAAK,CAAC,CAAC;EACd;EAEAO,kBAAkBA,CAAEV,WAAW,EAAE;IAC/B,OAAO,IAAI,CAAC5B,UAAU,CAACsC,kBAAkB,CAAC,IAAI,CAACX,cAAc,CAACC,WAAW,CAAC,CAAC;EAC7E;EAEAW,QAAQA,CAAEX,WAAW,EAAE;IACrB,OAAO,IAAI,CAAC5B,UAAU,CAACuC,QAAQ,CAAC,IAAI,CAACZ,cAAc,CAACC,WAAW,CAAC,CAAC;EACnE;EAEA,OAAOY,OAAOA,CAAExC,UAAU,EAAEC,OAAO,EAAE;IACnCD,UAAU,CAACC,OAAO,GAAGA,OAAO,IAAIL,aAAa;IAC7C,OAAOI,UAAU;EACnB;EAEAyC,IAAIA,CAAEC,QAAQ,EAAEC,QAAQ,EAAE;IACxB,MAAMC,IAAI,GAAGC,IAAI,CAACC,SAAS,CAAC,IAAI,CAAC;IACjC,MAAM9C,UAAU,GAAG,IAAI;IACvBP,EAAE,CAACsD,SAAS,CAACL,QAAQ,EAAEE,IAAI,EAAE,MAAM,EAAE,UAAUI,GAAG,EAAE;MAClD,IAAIL,QAAQ,EAAE;QACZA,QAAQ,CAACK,GAAG,EAAEA,GAAG,GAAG,IAAI,GAAGhD,UAAU,CAAC;MACxC;IACF,CAAC,CAAC;EACJ;EAEA,OAAOiD,IAAIA,CAAEP,QAAQ,EAAEzC,OAAO,EAAE0C,QAAQ,EAAE;IACxC,IAAI,CAACA,QAAQ,EAAE;MACb;IACF;IACAlD,EAAE,CAACyD,QAAQ,CAACR,QAAQ,EAAE,MAAM,EAAE,UAAUM,GAAG,EAAEJ,IAAI,EAAE;MACjD,IAAII,GAAG,EAAE;QACPL,QAAQ,CAACK,GAAG,EAAE,IAAI,CAAC;MACrB,CAAC,MAAM;QACL,MAAMhD,UAAU,GAAG6C,IAAI,CAACM,KAAK,CAACP,IAAI,CAAC;QACnCD,QAAQ,CAACK,GAAG,EAAElD,UAAU,CAAC0C,OAAO,CAACxC,UAAU,EAAEC,OAAO,CAAC,CAAC;MACxD;IACF,CAAC,CAAC;EACJ;EAEA,MAAMmD,MAAMA,CAAEC,cAAc,EAAE;IAC5B,OAAO,MAAMA,cAAc,CAACC,KAAK,CAAC,IAAI,CAAC;EACzC;EAEA,aAAaC,QAAQA,CAAEC,GAAG,EAAEH,cAAc,EAAE;IAC1C,MAAMI,GAAG,GAAG,MAAMJ,cAAc,CAACK,QAAQ,CAACF,GAAG,CAAC;IAC9CG,MAAM,CAACC,cAAc,CAACH,GAAG,EAAE3D,UAAU,CAAC+D,SAAS,CAAC;IAChD,OAAOJ,GAAG;EACZ;EAEAK,UAAUA,CAAEC,OAAO,EAAE;IACnB,IAAI,CAACjD,SAAS,GAAG,CAAC,CAAEiD,OAAO,CAACjD,SAAU;EACxC;AACF;AAEAkD,MAAM,CAACC,OAAO,GAAGnE,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}