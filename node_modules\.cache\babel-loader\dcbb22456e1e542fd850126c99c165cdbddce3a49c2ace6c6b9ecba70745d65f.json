{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.transformReply = exports.transformArguments = exports.FIRST_KEY_INDEX = void 0;\nconst generic_transformers_1 = require(\"./generic-transformers\");\nexports.FIRST_KEY_INDEX = 1;\nfunction transformArguments(key, timestamp, mode) {\n  const args = ['EXPIREAT', key, (0, generic_transformers_1.transformEXAT)(timestamp)];\n  if (mode) {\n    args.push(mode);\n  }\n  return args;\n}\nexports.transformArguments = transformArguments;\nvar generic_transformers_2 = require(\"./generic-transformers\");\nObject.defineProperty(exports, \"transformReply\", {\n  enumerable: true,\n  get: function () {\n    return generic_transformers_2.transformBooleanReply;\n  }\n});", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "transformReply", "transformArguments", "FIRST_KEY_INDEX", "generic_transformers_1", "require", "key", "timestamp", "mode", "args", "transformEXAT", "push", "generic_transformers_2", "enumerable", "get", "transformBooleanReply"], "sources": ["C:/tmsft/node_modules/@redis/client/dist/lib/commands/EXPIREAT.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.transformReply = exports.transformArguments = exports.FIRST_KEY_INDEX = void 0;\nconst generic_transformers_1 = require(\"./generic-transformers\");\nexports.FIRST_KEY_INDEX = 1;\nfunction transformArguments(key, timestamp, mode) {\n    const args = [\n        'EXPIREAT',\n        key,\n        (0, generic_transformers_1.transformEXAT)(timestamp)\n    ];\n    if (mode) {\n        args.push(mode);\n    }\n    return args;\n}\nexports.transformArguments = transformArguments;\nvar generic_transformers_2 = require(\"./generic-transformers\");\nObject.defineProperty(exports, \"transformReply\", { enumerable: true, get: function () { return generic_transformers_2.transformBooleanReply; } });\n"], "mappings": "AAAA,YAAY;;AACZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEC,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7DD,OAAO,CAACE,cAAc,GAAGF,OAAO,CAACG,kBAAkB,GAAGH,OAAO,CAACI,eAAe,GAAG,KAAK,CAAC;AACtF,MAAMC,sBAAsB,GAAGC,OAAO,CAAC,wBAAwB,CAAC;AAChEN,OAAO,CAACI,eAAe,GAAG,CAAC;AAC3B,SAASD,kBAAkBA,CAACI,GAAG,EAAEC,SAAS,EAAEC,IAAI,EAAE;EAC9C,MAAMC,IAAI,GAAG,CACT,UAAU,EACVH,GAAG,EACH,CAAC,CAAC,EAAEF,sBAAsB,CAACM,aAAa,EAAEH,SAAS,CAAC,CACvD;EACD,IAAIC,IAAI,EAAE;IACNC,IAAI,CAACE,IAAI,CAACH,IAAI,CAAC;EACnB;EACA,OAAOC,IAAI;AACf;AACAV,OAAO,CAACG,kBAAkB,GAAGA,kBAAkB;AAC/C,IAAIU,sBAAsB,GAAGP,OAAO,CAAC,wBAAwB,CAAC;AAC9DR,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,gBAAgB,EAAE;EAAEc,UAAU,EAAE,IAAI;EAAEC,GAAG,EAAE,SAAAA,CAAA,EAAY;IAAE,OAAOF,sBAAsB,CAACG,qBAAqB;EAAE;AAAE,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}