{"ast": null, "code": "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the License);\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an AS IS BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { Prelu } from '@tensorflow/tfjs-core';\nimport { assertNotComplex } from '../cpu_util';\nimport { createSimpleBinaryKernelImpl } from '../utils/binary_impl';\nconst preluImpl = createSimpleBinaryKernelImpl((xValue, aValue) => xValue < 0 ? aValue * xValue : xValue);\nexport function prelu(args) {\n  const {\n    inputs,\n    backend\n  } = args;\n  const {\n    x,\n    alpha\n  } = inputs;\n  assertNotComplex([x, alpha], 'prelu');\n  const aVals = backend.data.get(x.dataId).values;\n  const bVals = backend.data.get(alpha.dataId).values;\n  const [resultData, resultShape] = preluImpl(x.shape, alpha.shape, aVals, bVals, 'float32');\n  return backend.makeTensorInfo(resultShape, 'float32', resultData);\n}\nexport const preluConfig = {\n  kernelName: Prelu,\n  backendName: 'cpu',\n  kernelFunc: prelu\n};", "map": {"version": 3, "names": ["<PERSON><PERSON>", "assertNotComplex", "createSimpleBinaryKernelImpl", "preluImpl", "xValue", "aValue", "prelu", "args", "inputs", "backend", "x", "alpha", "aVals", "data", "get", "dataId", "values", "bVals", "resultData", "resultShape", "shape", "makeTensorInfo", "preluConfig", "kernelName", "backendName", "kernelFunc"], "sources": ["C:\\tfjs-backend-cpu\\src\\kernels\\Prelu.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the License);\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an AS IS BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {KernelConfig, Prelu, PreluInputs, TensorInfo, TypedArray} from '@tensorflow/tfjs-core';\n\nimport {MathBackendCPU} from '../backend_cpu';\nimport {assertNotComplex} from '../cpu_util';\nimport {createSimpleBinaryKernelImpl} from '../utils/binary_impl';\n\nconst preluImpl = createSimpleBinaryKernelImpl(\n    (xValue: number, aValue: number) => xValue < 0 ? aValue * xValue : xValue);\n\nexport function prelu(args: {inputs: PreluInputs, backend: MathBackendCPU}):\n    TensorInfo {\n  const {inputs, backend} = args;\n  const {x, alpha} = inputs;\n\n  assertNotComplex([x, alpha], 'prelu');\n\n  const aVals = backend.data.get(x.dataId).values as TypedArray;\n  const bVals = backend.data.get(alpha.dataId).values as TypedArray;\n\n  const [resultData, resultShape] =\n      preluImpl(x.shape, alpha.shape, aVals, bVals, 'float32');\n\n  return backend.makeTensorInfo(resultShape, 'float32', resultData);\n}\n\nexport const preluConfig: KernelConfig = {\n  kernelName: Prelu,\n  backendName: 'cpu',\n  kernelFunc: prelu,\n};\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAiBA,SAAsBA,KAAK,QAA4C,uBAAuB;AAG9F,SAAQC,gBAAgB,QAAO,aAAa;AAC5C,SAAQC,4BAA4B,QAAO,sBAAsB;AAEjE,MAAMC,SAAS,GAAGD,4BAA4B,CAC1C,CAACE,MAAc,EAAEC,MAAc,KAAKD,MAAM,GAAG,CAAC,GAAGC,MAAM,GAAGD,MAAM,GAAGA,MAAM,CAAC;AAE9E,OAAM,SAAUE,KAAKA,CAACC,IAAoD;EAExE,MAAM;IAACC,MAAM;IAAEC;EAAO,CAAC,GAAGF,IAAI;EAC9B,MAAM;IAACG,CAAC;IAAEC;EAAK,CAAC,GAAGH,MAAM;EAEzBP,gBAAgB,CAAC,CAACS,CAAC,EAAEC,KAAK,CAAC,EAAE,OAAO,CAAC;EAErC,MAAMC,KAAK,GAAGH,OAAO,CAACI,IAAI,CAACC,GAAG,CAACJ,CAAC,CAACK,MAAM,CAAC,CAACC,MAAoB;EAC7D,MAAMC,KAAK,GAAGR,OAAO,CAACI,IAAI,CAACC,GAAG,CAACH,KAAK,CAACI,MAAM,CAAC,CAACC,MAAoB;EAEjE,MAAM,CAACE,UAAU,EAAEC,WAAW,CAAC,GAC3BhB,SAAS,CAACO,CAAC,CAACU,KAAK,EAAET,KAAK,CAACS,KAAK,EAAER,KAAK,EAAEK,KAAK,EAAE,SAAS,CAAC;EAE5D,OAAOR,OAAO,CAACY,cAAc,CAACF,WAAW,EAAE,SAAS,EAAED,UAAU,CAAC;AACnE;AAEA,OAAO,MAAMI,WAAW,GAAiB;EACvCC,UAAU,EAAEvB,KAAK;EACjBwB,WAAW,EAAE,KAAK;EAClBC,UAAU,EAAEnB;CACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}