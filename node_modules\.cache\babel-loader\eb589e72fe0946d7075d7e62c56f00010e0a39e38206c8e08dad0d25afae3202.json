{"ast": null, "code": "/**\n * @license\n * Copyright 2018 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nexport class DepthwiseConv2DDerFilterProgram {\n  constructor(convInfo) {\n    this.variableNames = ['x', 'dy'];\n    this.outputShape = convInfo.filterShape;\n    const strideHeight = convInfo.strideHeight;\n    const strideWidth = convInfo.strideWidth;\n    const padTop = convInfo.padInfo.top;\n    const padLeft = convInfo.padInfo.left;\n    const channelMul = convInfo.outChannels / convInfo.inChannels;\n    this.userCode = \"\\n      void main() {\\n        ivec4 coords = getOutputCoords();\\n        int wR = coords.x;\\n        int wC = coords.y;\\n        int d1 = coords.z;\\n        int dm = coords.w;\\n        int d2 = d1 * \".concat(channelMul, \" + dm;\\n\\n        float dotProd = 0.0;\\n\\n        // TO DO: Vec4 over the batch size\\n        for (int b = 0; b < \").concat(convInfo.batchSize, \"; b++) {\\n          for (int yR = 0; yR < \").concat(convInfo.outHeight, \"; yR++) {\\n            int xR = wR + yR * \").concat(strideHeight, \" - \").concat(padTop, \";\\n\\n            if (xR < 0 || xR >= \").concat(convInfo.inHeight, \") {\\n              continue;\\n            }\\n\\n            for (int yC = 0; yC < \").concat(convInfo.outWidth, \"; yC++) {\\n              int xC = wC + yC * \").concat(strideWidth, \" - \").concat(padLeft, \";\\n\\n              if (xC < 0 || xC >= \").concat(convInfo.inWidth, \") {\\n                continue;\\n              }\\n\\n              float dyValue = getDy(b, yR, yC, d2);\\n              float xValue = getX(b, xR, xC, d1);\\n              dotProd += (xValue * dyValue);\\n            }\\n          }\\n        }\\n        setOutput(dotProd);\\n      }\\n    \");\n  }\n}\nexport class DepthwiseConv2DDerInputProgram {\n  constructor(convInfo) {\n    this.variableNames = ['dy', 'W'];\n    this.outputShape = convInfo.inShape;\n    const filterHeight = convInfo.filterHeight;\n    const filterWidth = convInfo.filterWidth;\n    const strideHeight = convInfo.strideHeight;\n    const strideWidth = convInfo.strideWidth;\n    const padTop = filterHeight - 1 - convInfo.padInfo.top;\n    const padLeft = filterWidth - 1 - convInfo.padInfo.left;\n    const channelMul = convInfo.outChannels / convInfo.inChannels;\n    this.userCode = \"\\n      const ivec2 pads = ivec2(\".concat(padTop, \", \").concat(padLeft, \");\\n\\n      void main() {\\n        ivec4 coords = getOutputCoords();\\n        int batch = coords[0];\\n        int d1 = coords[3];\\n        ivec2 dyCorner = coords.yz - pads;\\n        int dyRCorner = dyCorner.x;\\n        int dyCCorner = dyCorner.y;\\n\\n        float dotProd = 0.0;\\n\\n        for (int wR = 0; wR < \").concat(filterHeight, \"; wR++) {\\n          float dyR = float(dyRCorner + wR) / \").concat(strideHeight, \".0;\\n\\n          if (dyR < 0.0 || dyR >= \").concat(convInfo.outHeight, \".0 || fract(dyR) > 0.0) {\\n            continue;\\n          }\\n          int idyR = int(dyR);\\n\\n          int wRPerm = \").concat(filterHeight, \" - 1 - wR;\\n\\n          for (int wC = 0; wC < \").concat(filterWidth, \"; wC++) {\\n            float dyC = float(dyCCorner + wC) / \").concat(strideWidth, \".0;\\n\\n            if (dyC < 0.0 || dyC >= \").concat(convInfo.outWidth, \".0 ||\\n                fract(dyC) > 0.0) {\\n              continue;\\n            }\\n            int idyC = int(dyC);\\n\\n            int wCPerm = \").concat(filterWidth, \" - 1 - wC;\\n\\n            // TO DO: Vec4 over the channelMul\\n            for (int dm = 0; dm < \").concat(channelMul, \"; dm++) {\\n              int d2 = d1 * \").concat(channelMul, \" + dm;\\n              float xValue = getDy(batch, idyR, idyC, d2);\\n              float wValue = getW(wRPerm, wCPerm, d1, dm);\\n              dotProd += xValue * wValue;\\n            }\\n          }\\n        }\\n        setOutput(dotProd);\\n      }\\n    \");\n  }\n}", "map": {"version": 3, "names": ["DepthwiseConv2DDerFilterProgram", "constructor", "convInfo", "variableNames", "outputShape", "filterShape", "strideHeight", "<PERSON><PERSON><PERSON><PERSON>", "padTop", "padInfo", "top", "padLeft", "left", "channelMul", "outChannels", "inChannels", "userCode", "concat", "batchSize", "outHeight", "inHeight", "outWidth", "inWidth", "DepthwiseConv2DDerInputProgram", "inShape", "filterHeight", "filterWidth"], "sources": ["C:\\tfjs-backend-webgl\\src\\conv_backprop_gpu_depthwise.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2018 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {backend_util} from '@tensorflow/tfjs-core';\nimport {GPGPUProgram} from './gpgpu_math';\n\nexport class DepthwiseConv2DDerFilterProgram implements GPGPUProgram {\n  variableNames = ['x', 'dy'];\n  outputShape: number[];\n  userCode: string;\n\n  constructor(convInfo: backend_util.Conv2DInfo) {\n    this.outputShape = convInfo.filterShape;\n\n    const strideHeight = convInfo.strideHeight;\n    const strideWidth = convInfo.strideWidth;\n    const padTop = convInfo.padInfo.top;\n    const padLeft = convInfo.padInfo.left;\n    const channelMul = convInfo.outChannels / convInfo.inChannels;\n\n    this.userCode = `\n      void main() {\n        ivec4 coords = getOutputCoords();\n        int wR = coords.x;\n        int wC = coords.y;\n        int d1 = coords.z;\n        int dm = coords.w;\n        int d2 = d1 * ${channelMul} + dm;\n\n        float dotProd = 0.0;\n\n        // TO DO: Vec4 over the batch size\n        for (int b = 0; b < ${convInfo.batchSize}; b++) {\n          for (int yR = 0; yR < ${convInfo.outHeight}; yR++) {\n            int xR = wR + yR * ${strideHeight} - ${padTop};\n\n            if (xR < 0 || xR >= ${convInfo.inHeight}) {\n              continue;\n            }\n\n            for (int yC = 0; yC < ${convInfo.outWidth}; yC++) {\n              int xC = wC + yC * ${strideWidth} - ${padLeft};\n\n              if (xC < 0 || xC >= ${convInfo.inWidth}) {\n                continue;\n              }\n\n              float dyValue = getDy(b, yR, yC, d2);\n              float xValue = getX(b, xR, xC, d1);\n              dotProd += (xValue * dyValue);\n            }\n          }\n        }\n        setOutput(dotProd);\n      }\n    `;\n  }\n}\n\nexport class DepthwiseConv2DDerInputProgram implements GPGPUProgram {\n  variableNames = ['dy', 'W'];\n  outputShape: number[];\n  userCode: string;\n\n  constructor(convInfo: backend_util.Conv2DInfo) {\n    this.outputShape = convInfo.inShape;\n\n    const filterHeight = convInfo.filterHeight;\n    const filterWidth = convInfo.filterWidth;\n    const strideHeight = convInfo.strideHeight;\n    const strideWidth = convInfo.strideWidth;\n\n    const padTop = filterHeight - 1 - convInfo.padInfo.top;\n    const padLeft = filterWidth - 1 - convInfo.padInfo.left;\n    const channelMul = convInfo.outChannels / convInfo.inChannels;\n\n    this.userCode = `\n      const ivec2 pads = ivec2(${padTop}, ${padLeft});\n\n      void main() {\n        ivec4 coords = getOutputCoords();\n        int batch = coords[0];\n        int d1 = coords[3];\n        ivec2 dyCorner = coords.yz - pads;\n        int dyRCorner = dyCorner.x;\n        int dyCCorner = dyCorner.y;\n\n        float dotProd = 0.0;\n\n        for (int wR = 0; wR < ${filterHeight}; wR++) {\n          float dyR = float(dyRCorner + wR) / ${strideHeight}.0;\n\n          if (dyR < 0.0 || dyR >= ${convInfo.outHeight}.0 || fract(dyR) > 0.0) {\n            continue;\n          }\n          int idyR = int(dyR);\n\n          int wRPerm = ${filterHeight} - 1 - wR;\n\n          for (int wC = 0; wC < ${filterWidth}; wC++) {\n            float dyC = float(dyCCorner + wC) / ${strideWidth}.0;\n\n            if (dyC < 0.0 || dyC >= ${convInfo.outWidth}.0 ||\n                fract(dyC) > 0.0) {\n              continue;\n            }\n            int idyC = int(dyC);\n\n            int wCPerm = ${filterWidth} - 1 - wC;\n\n            // TO DO: Vec4 over the channelMul\n            for (int dm = 0; dm < ${channelMul}; dm++) {\n              int d2 = d1 * ${channelMul} + dm;\n              float xValue = getDy(batch, idyR, idyC, d2);\n              float wValue = getW(wRPerm, wCPerm, d1, dm);\n              dotProd += xValue * wValue;\n            }\n          }\n        }\n        setOutput(dotProd);\n      }\n    `;\n  }\n}\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAoBA,OAAM,MAAOA,+BAA+B;EAK1CC,YAAYC,QAAiC;IAJ7C,KAAAC,aAAa,GAAG,CAAC,GAAG,EAAE,IAAI,CAAC;IAKzB,IAAI,CAACC,WAAW,GAAGF,QAAQ,CAACG,WAAW;IAEvC,MAAMC,YAAY,GAAGJ,QAAQ,CAACI,YAAY;IAC1C,MAAMC,WAAW,GAAGL,QAAQ,CAACK,WAAW;IACxC,MAAMC,MAAM,GAAGN,QAAQ,CAACO,OAAO,CAACC,GAAG;IACnC,MAAMC,OAAO,GAAGT,QAAQ,CAACO,OAAO,CAACG,IAAI;IACrC,MAAMC,UAAU,GAAGX,QAAQ,CAACY,WAAW,GAAGZ,QAAQ,CAACa,UAAU;IAE7D,IAAI,CAACC,QAAQ,8MAAAC,MAAA,CAOOJ,UAAU,wHAAAI,MAAA,CAKJf,QAAQ,CAACgB,SAAS,gDAAAD,MAAA,CACdf,QAAQ,CAACiB,SAAS,gDAAAF,MAAA,CACnBX,YAAY,SAAAW,MAAA,CAAMT,MAAM,2CAAAS,MAAA,CAEvBf,QAAQ,CAACkB,QAAQ,uFAAAH,MAAA,CAIff,QAAQ,CAACmB,QAAQ,kDAAAJ,MAAA,CAClBV,WAAW,SAAAU,MAAA,CAAMN,OAAO,6CAAAM,MAAA,CAEvBf,QAAQ,CAACoB,OAAO,+RAY/C;EACH;;AAGF,OAAM,MAAOC,8BAA8B;EAKzCtB,YAAYC,QAAiC;IAJ7C,KAAAC,aAAa,GAAG,CAAC,IAAI,EAAE,GAAG,CAAC;IAKzB,IAAI,CAACC,WAAW,GAAGF,QAAQ,CAACsB,OAAO;IAEnC,MAAMC,YAAY,GAAGvB,QAAQ,CAACuB,YAAY;IAC1C,MAAMC,WAAW,GAAGxB,QAAQ,CAACwB,WAAW;IACxC,MAAMpB,YAAY,GAAGJ,QAAQ,CAACI,YAAY;IAC1C,MAAMC,WAAW,GAAGL,QAAQ,CAACK,WAAW;IAExC,MAAMC,MAAM,GAAGiB,YAAY,GAAG,CAAC,GAAGvB,QAAQ,CAACO,OAAO,CAACC,GAAG;IACtD,MAAMC,OAAO,GAAGe,WAAW,GAAG,CAAC,GAAGxB,QAAQ,CAACO,OAAO,CAACG,IAAI;IACvD,MAAMC,UAAU,GAAGX,QAAQ,CAACY,WAAW,GAAGZ,QAAQ,CAACa,UAAU;IAE7D,IAAI,CAACC,QAAQ,uCAAAC,MAAA,CACgBT,MAAM,QAAAS,MAAA,CAAKN,OAAO,+TAAAM,MAAA,CAYnBQ,YAAY,+DAAAR,MAAA,CACIX,YAAY,+CAAAW,MAAA,CAExBf,QAAQ,CAACiB,SAAS,8HAAAF,MAAA,CAK7BQ,YAAY,oDAAAR,MAAA,CAEHS,WAAW,iEAAAT,MAAA,CACKV,WAAW,iDAAAU,MAAA,CAEvBf,QAAQ,CAACmB,QAAQ,uJAAAJ,MAAA,CAM5BS,WAAW,sGAAAT,MAAA,CAGFJ,UAAU,6CAAAI,MAAA,CAChBJ,UAAU,iQASnC;EACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}