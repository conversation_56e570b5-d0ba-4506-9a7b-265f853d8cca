{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.transformArguments = exports.IS_READ_ONLY = void 0;\nconst _1 = require(\".\");\nexports.IS_READ_ONLY = true;\nfunction transformArguments(index, query, options) {\n  const args = ['FT.EXPLAIN', index, query];\n  (0, _1.pushParamsArgs)(args, options?.PARAMS);\n  if (options?.DIALECT) {\n    args.push('DIALECT', options.DIALECT.toString());\n  }\n  return args;\n}\nexports.transformArguments = transformArguments;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "transformArguments", "IS_READ_ONLY", "_1", "require", "index", "query", "options", "args", "pushParamsArgs", "PARAMS", "DIALECT", "push", "toString"], "sources": ["C:/tmsft/node_modules/@redis/search/dist/commands/EXPLAIN.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.transformArguments = exports.IS_READ_ONLY = void 0;\nconst _1 = require(\".\");\nexports.IS_READ_ONLY = true;\nfunction transformArguments(index, query, options) {\n    const args = ['FT.EXPLAIN', index, query];\n    (0, _1.pushParamsArgs)(args, options?.PARAMS);\n    if (options?.DIALECT) {\n        args.push('DIALECT', options.DIALECT.toString());\n    }\n    return args;\n}\nexports.transformArguments = transformArguments;\n"], "mappings": "AAAA,YAAY;;AACZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEC,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7DD,OAAO,CAACE,kBAAkB,GAAGF,OAAO,CAACG,YAAY,GAAG,KAAK,CAAC;AAC1D,MAAMC,EAAE,GAAGC,OAAO,CAAC,GAAG,CAAC;AACvBL,OAAO,CAACG,YAAY,GAAG,IAAI;AAC3B,SAASD,kBAAkBA,CAACI,KAAK,EAAEC,KAAK,EAAEC,OAAO,EAAE;EAC/C,MAAMC,IAAI,GAAG,CAAC,YAAY,EAAEH,KAAK,EAAEC,KAAK,CAAC;EACzC,CAAC,CAAC,EAAEH,EAAE,CAACM,cAAc,EAAED,IAAI,EAAED,OAAO,EAAEG,MAAM,CAAC;EAC7C,IAAIH,OAAO,EAAEI,OAAO,EAAE;IAClBH,IAAI,CAACI,IAAI,CAAC,SAAS,EAAEL,OAAO,CAACI,OAAO,CAACE,QAAQ,CAAC,CAAC,CAAC;EACpD;EACA,OAAOL,IAAI;AACf;AACAT,OAAO,CAACE,kBAAkB,GAAGA,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}