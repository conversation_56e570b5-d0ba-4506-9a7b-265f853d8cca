{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.transformArguments = exports.IS_READ_ONLY = exports.FIRST_KEY_INDEX = void 0;\nconst generic_transformers_1 = require(\"./generic-transformers\");\nexports.FIRST_KEY_INDEX = 1;\nexports.IS_READ_ONLY = true;\nfunction transformArguments(keys) {\n  return (0, generic_transformers_1.pushVerdictArguments)(['SUNION'], keys);\n}\nexports.transformArguments = transformArguments;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "transformArguments", "IS_READ_ONLY", "FIRST_KEY_INDEX", "generic_transformers_1", "require", "keys", "pushVerdictArguments"], "sources": ["C:/tmsft/node_modules/@redis/client/dist/lib/commands/SUNION.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.transformArguments = exports.IS_READ_ONLY = exports.FIRST_KEY_INDEX = void 0;\nconst generic_transformers_1 = require(\"./generic-transformers\");\nexports.FIRST_KEY_INDEX = 1;\nexports.IS_READ_ONLY = true;\nfunction transformArguments(keys) {\n    return (0, generic_transformers_1.pushVerdictArguments)(['SUNION'], keys);\n}\nexports.transformArguments = transformArguments;\n"], "mappings": "AAAA,YAAY;;AACZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEC,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7DD,OAAO,CAACE,kBAAkB,GAAGF,OAAO,CAACG,YAAY,GAAGH,OAAO,CAACI,eAAe,GAAG,KAAK,CAAC;AACpF,MAAMC,sBAAsB,GAAGC,OAAO,CAAC,wBAAwB,CAAC;AAChEN,OAAO,CAACI,eAAe,GAAG,CAAC;AAC3BJ,OAAO,CAACG,YAAY,GAAG,IAAI;AAC3B,SAASD,kBAAkBA,CAACK,IAAI,EAAE;EAC9B,OAAO,CAAC,CAAC,EAAEF,sBAAsB,CAACG,oBAAoB,EAAE,CAAC,QAAQ,CAAC,EAAED,IAAI,CAAC;AAC7E;AACAP,OAAO,CAACE,kBAAkB,GAAGA,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}