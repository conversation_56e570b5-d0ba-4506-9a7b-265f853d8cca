{"ast": null, "code": "/**\n * @license\n * Copyright 2018 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n *\n * =============================================================================\n */\n/**\n * A ring buffer, providing O(1) FIFO, LIFO, and related operations.\n */\nexport class RingBuffer {\n  /**\n   * Constructs a `RingBuffer`.\n   * @param capacity The number of items that the buffer can accomodate.\n   */\n  constructor(capacity) {\n    this.capacity = capacity;\n    // Note we store the indices in the range 0 <= index < 2*capacity.\n    // This allows us to distinguish the full from the empty case.\n    // See https://www.snellman.net/blog/archive/2016-12-13-ring-buffers/\n    this.begin = 0; // inclusive\n    this.end = 0; // exclusive\n    if (capacity == null) {\n      throw new RangeError('Can\\'t create a ring buffer of unknown capacity.');\n    }\n    if (capacity < 1) {\n      throw new RangeError('Can\\'t create ring buffer of capacity < 1.');\n    }\n    this.data = new Array(capacity);\n    this.doubledCapacity = 2 * capacity;\n  }\n  /**\n   * Map any index into the range 0 <= index < 2*capacity.\n   */\n  wrap(index) {\n    // don't trust % on negative numbers\n    while (index < 0) {\n      index += this.doubledCapacity;\n    }\n    return index % this.doubledCapacity;\n  }\n  get(index) {\n    if (index < 0) {\n      throw new RangeError('Can\\'t get item at a negative index.');\n    }\n    return this.data[index % this.capacity];\n  }\n  set(index, value) {\n    if (index < 0) {\n      throw new RangeError('Can\\'t set item at a negative index.');\n    }\n    this.data[index % this.capacity] = value;\n  }\n  /**\n   * Returns the current number of items in the buffer.\n   */\n  length() {\n    let length = this.end - this.begin;\n    if (length < 0) {\n      length = this.doubledCapacity + length;\n    }\n    return length;\n  }\n  /**\n   * Reports whether the buffer is full.\n   * @returns true if the number of items in the buffer equals its capacity, and\n   *   false otherwise.\n   */\n  isFull() {\n    return this.length() === this.capacity;\n  }\n  /**\n   * Reports whether the buffer is empty.\n   * @returns true if the number of items in the buffer equals zero, and\n   *   false otherwise.\n   */\n  isEmpty() {\n    return this.length() === 0;\n  }\n  /**\n   * Adds an item to the end of the buffer.\n   */\n  push(value) {\n    if (this.isFull()) {\n      throw new RangeError('Ring buffer is full.');\n    }\n    this.set(this.end, value);\n    this.end = this.wrap(this.end + 1);\n  }\n  /**\n   * Adds many items to the end of the buffer, in order.\n   */\n  pushAll(values) {\n    for (const value of values) {\n      this.push(value);\n    }\n  }\n  /**\n   * Removes and returns the last item in the buffer.\n   */\n  pop() {\n    if (this.isEmpty()) {\n      throw new RangeError('Ring buffer is empty.');\n    }\n    this.end = this.wrap(this.end - 1);\n    const result = this.get(this.end);\n    this.set(this.end, undefined);\n    return result;\n  }\n  /**\n   * Adds an item to the beginning of the buffer.\n   */\n  unshift(value) {\n    if (this.isFull()) {\n      throw new RangeError('Ring buffer is full.');\n    }\n    this.begin = this.wrap(this.begin - 1);\n    this.set(this.begin, value);\n  }\n  /**\n   * Removes and returns the first item in the buffer.\n   */\n  shift() {\n    if (this.isEmpty()) {\n      throw new RangeError('Ring buffer is empty.');\n    }\n    const result = this.get(this.begin);\n    this.set(this.begin, undefined);\n    this.begin = this.wrap(this.begin + 1);\n    return result;\n  }\n  /**\n   * Removes and returns a specific item in the buffer, and moves the last item\n   * to the vacated slot.  This is useful for implementing a shuffling stream.\n   * Note that this operation necessarily scrambles the original order.\n   *\n   * @param relativeIndex: the index of the item to remove, relative to the\n   *   first item in the buffer (e.g., hiding the ring nature of the underlying\n   *   storage).\n   */\n  shuffleExcise(relativeIndex) {\n    if (this.isEmpty()) {\n      throw new RangeError('Ring buffer is empty.');\n    }\n    const index = this.wrap(this.begin + relativeIndex);\n    const result = this.get(index);\n    this.set(index, this.pop());\n    return result;\n  }\n}", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON>", "constructor", "capacity", "begin", "end", "RangeError", "data", "Array", "doubledCapacity", "wrap", "index", "get", "set", "value", "length", "isFull", "isEmpty", "push", "pushAll", "values", "pop", "result", "undefined", "unshift", "shift", "shuffleExcise", "relativeIndex"], "sources": ["C:\\tfjs-data\\src\\util\\ring_buffer.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2018 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n *\n * =============================================================================\n */\n\n/**\n * A ring buffer, providing O(1) FIFO, LIFO, and related operations.\n */\nexport class RingBuffer<T> {\n  // Note we store the indices in the range 0 <= index < 2*capacity.\n  // This allows us to distinguish the full from the empty case.\n  // See https://www.snellman.net/blog/archive/2016-12-13-ring-buffers/\n  protected begin = 0;  // inclusive\n  protected end = 0;    // exclusive\n  protected doubledCapacity: number;\n\n  protected data: T[];\n\n  /**\n   * Constructs a `RingBuffer`.\n   * @param capacity The number of items that the buffer can accomodate.\n   */\n  constructor(public capacity: number) {\n    if (capacity == null) {\n      throw new RangeError('Can\\'t create a ring buffer of unknown capacity.');\n    }\n    if (capacity < 1) {\n      throw new RangeError('Can\\'t create ring buffer of capacity < 1.');\n    }\n    this.data = new Array<T>(capacity);\n    this.doubledCapacity = 2 * capacity;\n  }\n\n  /**\n   * Map any index into the range 0 <= index < 2*capacity.\n   */\n  protected wrap(index: number) {\n    // don't trust % on negative numbers\n    while (index < 0) {\n      index += this.doubledCapacity;\n    }\n    return index % this.doubledCapacity;\n  }\n\n  protected get(index: number) {\n    if (index < 0) {\n      throw new RangeError('Can\\'t get item at a negative index.');\n    }\n    return this.data[index % this.capacity];\n  }\n\n  protected set(index: number, value: T) {\n    if (index < 0) {\n      throw new RangeError('Can\\'t set item at a negative index.');\n    }\n    this.data[index % this.capacity] = value;\n  }\n\n  /**\n   * Returns the current number of items in the buffer.\n   */\n  length(): number {\n    let length = this.end - this.begin;\n    if (length < 0) {\n      length = this.doubledCapacity + length;\n    }\n    return length;\n  }\n\n  /**\n   * Reports whether the buffer is full.\n   * @returns true if the number of items in the buffer equals its capacity, and\n   *   false otherwise.\n   */\n  isFull() {\n    return this.length() === this.capacity;\n  }\n\n  /**\n   * Reports whether the buffer is empty.\n   * @returns true if the number of items in the buffer equals zero, and\n   *   false otherwise.\n   */\n  isEmpty() {\n    return this.length() === 0;\n  }\n\n  /**\n   * Adds an item to the end of the buffer.\n   */\n  push(value: T) {\n    if (this.isFull()) {\n      throw new RangeError('Ring buffer is full.');\n    }\n    this.set(this.end, value);\n    this.end = this.wrap(this.end + 1);\n  }\n\n  /**\n   * Adds many items to the end of the buffer, in order.\n   */\n  pushAll(values: T[]) {\n    for (const value of values) {\n      this.push(value);\n    }\n  }\n\n  /**\n   * Removes and returns the last item in the buffer.\n   */\n  pop(): T {\n    if (this.isEmpty()) {\n      throw new RangeError('Ring buffer is empty.');\n    }\n    this.end = this.wrap(this.end - 1);\n    const result = this.get(this.end);\n    this.set(this.end, undefined);\n    return result;\n  }\n\n  /**\n   * Adds an item to the beginning of the buffer.\n   */\n  unshift(value: T) {\n    if (this.isFull()) {\n      throw new RangeError('Ring buffer is full.');\n    }\n    this.begin = this.wrap(this.begin - 1);\n    this.set(this.begin, value);\n  }\n\n  /**\n   * Removes and returns the first item in the buffer.\n   */\n  shift(): T {\n    if (this.isEmpty()) {\n      throw new RangeError('Ring buffer is empty.');\n    }\n    const result = this.get(this.begin);\n    this.set(this.begin, undefined);\n    this.begin = this.wrap(this.begin + 1);\n    return result;\n  }\n\n  /**\n   * Removes and returns a specific item in the buffer, and moves the last item\n   * to the vacated slot.  This is useful for implementing a shuffling stream.\n   * Note that this operation necessarily scrambles the original order.\n   *\n   * @param relativeIndex: the index of the item to remove, relative to the\n   *   first item in the buffer (e.g., hiding the ring nature of the underlying\n   *   storage).\n   */\n  shuffleExcise(relativeIndex: number): T {\n    if (this.isEmpty()) {\n      throw new RangeError('Ring buffer is empty.');\n    }\n    const index = this.wrap(this.begin + relativeIndex);\n    const result = this.get(index);\n    this.set(index, this.pop());\n    return result;\n  }\n}\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;;AAkBA;;;AAGA,OAAM,MAAOA,UAAU;EAUrB;;;;EAIAC,YAAmBC,QAAgB;IAAhB,KAAAA,QAAQ,GAARA,QAAQ;IAb3B;IACA;IACA;IACU,KAAAC,KAAK,GAAG,CAAC,CAAC,CAAE;IACZ,KAAAC,GAAG,GAAG,CAAC,CAAC,CAAI;IAUpB,IAAIF,QAAQ,IAAI,IAAI,EAAE;MACpB,MAAM,IAAIG,UAAU,CAAC,kDAAkD,CAAC;;IAE1E,IAAIH,QAAQ,GAAG,CAAC,EAAE;MAChB,MAAM,IAAIG,UAAU,CAAC,4CAA4C,CAAC;;IAEpE,IAAI,CAACC,IAAI,GAAG,IAAIC,KAAK,CAAIL,QAAQ,CAAC;IAClC,IAAI,CAACM,eAAe,GAAG,CAAC,GAAGN,QAAQ;EACrC;EAEA;;;EAGUO,IAAIA,CAACC,KAAa;IAC1B;IACA,OAAOA,KAAK,GAAG,CAAC,EAAE;MAChBA,KAAK,IAAI,IAAI,CAACF,eAAe;;IAE/B,OAAOE,KAAK,GAAG,IAAI,CAACF,eAAe;EACrC;EAEUG,GAAGA,CAACD,KAAa;IACzB,IAAIA,KAAK,GAAG,CAAC,EAAE;MACb,MAAM,IAAIL,UAAU,CAAC,sCAAsC,CAAC;;IAE9D,OAAO,IAAI,CAACC,IAAI,CAACI,KAAK,GAAG,IAAI,CAACR,QAAQ,CAAC;EACzC;EAEUU,GAAGA,CAACF,KAAa,EAAEG,KAAQ;IACnC,IAAIH,KAAK,GAAG,CAAC,EAAE;MACb,MAAM,IAAIL,UAAU,CAAC,sCAAsC,CAAC;;IAE9D,IAAI,CAACC,IAAI,CAACI,KAAK,GAAG,IAAI,CAACR,QAAQ,CAAC,GAAGW,KAAK;EAC1C;EAEA;;;EAGAC,MAAMA,CAAA;IACJ,IAAIA,MAAM,GAAG,IAAI,CAACV,GAAG,GAAG,IAAI,CAACD,KAAK;IAClC,IAAIW,MAAM,GAAG,CAAC,EAAE;MACdA,MAAM,GAAG,IAAI,CAACN,eAAe,GAAGM,MAAM;;IAExC,OAAOA,MAAM;EACf;EAEA;;;;;EAKAC,MAAMA,CAAA;IACJ,OAAO,IAAI,CAACD,MAAM,EAAE,KAAK,IAAI,CAACZ,QAAQ;EACxC;EAEA;;;;;EAKAc,OAAOA,CAAA;IACL,OAAO,IAAI,CAACF,MAAM,EAAE,KAAK,CAAC;EAC5B;EAEA;;;EAGAG,IAAIA,CAACJ,KAAQ;IACX,IAAI,IAAI,CAACE,MAAM,EAAE,EAAE;MACjB,MAAM,IAAIV,UAAU,CAAC,sBAAsB,CAAC;;IAE9C,IAAI,CAACO,GAAG,CAAC,IAAI,CAACR,GAAG,EAAES,KAAK,CAAC;IACzB,IAAI,CAACT,GAAG,GAAG,IAAI,CAACK,IAAI,CAAC,IAAI,CAACL,GAAG,GAAG,CAAC,CAAC;EACpC;EAEA;;;EAGAc,OAAOA,CAACC,MAAW;IACjB,KAAK,MAAMN,KAAK,IAAIM,MAAM,EAAE;MAC1B,IAAI,CAACF,IAAI,CAACJ,KAAK,CAAC;;EAEpB;EAEA;;;EAGAO,GAAGA,CAAA;IACD,IAAI,IAAI,CAACJ,OAAO,EAAE,EAAE;MAClB,MAAM,IAAIX,UAAU,CAAC,uBAAuB,CAAC;;IAE/C,IAAI,CAACD,GAAG,GAAG,IAAI,CAACK,IAAI,CAAC,IAAI,CAACL,GAAG,GAAG,CAAC,CAAC;IAClC,MAAMiB,MAAM,GAAG,IAAI,CAACV,GAAG,CAAC,IAAI,CAACP,GAAG,CAAC;IACjC,IAAI,CAACQ,GAAG,CAAC,IAAI,CAACR,GAAG,EAAEkB,SAAS,CAAC;IAC7B,OAAOD,MAAM;EACf;EAEA;;;EAGAE,OAAOA,CAACV,KAAQ;IACd,IAAI,IAAI,CAACE,MAAM,EAAE,EAAE;MACjB,MAAM,IAAIV,UAAU,CAAC,sBAAsB,CAAC;;IAE9C,IAAI,CAACF,KAAK,GAAG,IAAI,CAACM,IAAI,CAAC,IAAI,CAACN,KAAK,GAAG,CAAC,CAAC;IACtC,IAAI,CAACS,GAAG,CAAC,IAAI,CAACT,KAAK,EAAEU,KAAK,CAAC;EAC7B;EAEA;;;EAGAW,KAAKA,CAAA;IACH,IAAI,IAAI,CAACR,OAAO,EAAE,EAAE;MAClB,MAAM,IAAIX,UAAU,CAAC,uBAAuB,CAAC;;IAE/C,MAAMgB,MAAM,GAAG,IAAI,CAACV,GAAG,CAAC,IAAI,CAACR,KAAK,CAAC;IACnC,IAAI,CAACS,GAAG,CAAC,IAAI,CAACT,KAAK,EAAEmB,SAAS,CAAC;IAC/B,IAAI,CAACnB,KAAK,GAAG,IAAI,CAACM,IAAI,CAAC,IAAI,CAACN,KAAK,GAAG,CAAC,CAAC;IACtC,OAAOkB,MAAM;EACf;EAEA;;;;;;;;;EASAI,aAAaA,CAACC,aAAqB;IACjC,IAAI,IAAI,CAACV,OAAO,EAAE,EAAE;MAClB,MAAM,IAAIX,UAAU,CAAC,uBAAuB,CAAC;;IAE/C,MAAMK,KAAK,GAAG,IAAI,CAACD,IAAI,CAAC,IAAI,CAACN,KAAK,GAAGuB,aAAa,CAAC;IACnD,MAAML,MAAM,GAAG,IAAI,CAACV,GAAG,CAACD,KAAK,CAAC;IAC9B,IAAI,CAACE,GAAG,CAACF,KAAK,EAAE,IAAI,CAACU,GAAG,EAAE,CAAC;IAC3B,OAAOC,MAAM;EACf", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}