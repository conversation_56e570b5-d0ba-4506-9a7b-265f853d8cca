{"ast": null, "code": "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { ENGINE } from '../engine';\nimport { Conv3DBackpropInputV2 } from '../kernel_names';\nimport * as util from '../util';\nimport { op } from './operation';\nimport { reshape } from './reshape';\n/**\n * Computes the derivative of the input of a 3D convolution.\n *\n * @param xShape The shape of the input: [batch, depth, height, width,\n * in_channels]. If length of 4, batch of 1 is assumed.\n * @param dy The derivative of the output, of rank 5 or rank 4 of shape\n *   `[batch, outDepth, outHeight, outWidth, in_channels]`.\n * If rank 4, batch of 1 is assumed.\n * @param filter The filter, rank 5, of shape\n *     `[filterDepth, filterHeight, filterWidth, inDepth, outDepth]`.\n * @param strides The strides of the convolution: `[strideDepth, strideHeight,\n * strideWidth]`.\n * @param pad The type of padding algorithm used:\n *    - `same` and stride 1: output will be of same size as input,\n *       regardless of filter size.\n *    - `valid`: output will be smaller than input if filter is larger\n *       than 1x1.\n */\nfunction conv3DBackpropInput_(xShape, dy, filter, strides, pad) {\n  util.assert(xShape.length === dy.rank, () => \"Length of inShape \" + \"(\".concat(xShape.length, \") and rank of dy (\").concat(dy.rank, \") must match\"));\n  let xShape5D = xShape;\n  let dy5D = dy;\n  let reshapedTo5D = false;\n  if (dy.rank === 4) {\n    reshapedTo5D = true;\n    dy5D = reshape(dy, [1, dy.shape[0], dy.shape[1], dy.shape[2], dy.shape[3]]);\n    xShape5D = [1, xShape[0], xShape[1], xShape[2], xShape[3]];\n  }\n  const inDepth = xShape5D[4];\n  const outDepth = dy5D.shape[4];\n  util.assert(xShape5D.length === 5, () => \"Error in conv3dDerInput: inShape must be length 5, but got length \" + \"\".concat(xShape5D.length, \".\"));\n  util.assert(dy5D.rank === 5, () => \"Error in conv3dDerInput: dy must be rank 5, but got \" + \"rank \".concat(dy5D.rank));\n  util.assert(filter.rank === 5, () => \"Error in conv3dDerInput: filter must be rank 5, but got \" + \"rank \".concat(filter.rank));\n  util.assert(inDepth === filter.shape[3], () => \"Error in conv3dDerInput: depth of input (\".concat(inDepth, \") must \") + \"match input depth for filter \".concat(filter.shape[3], \".\"));\n  util.assert(outDepth === filter.shape[4], () => \"Error in conv3dDerInput: depth of output (\".concat(outDepth, \") must \") + \"match output depth for filter \".concat(filter.shape[4], \".\"));\n  const inputs = {\n    dy: dy5D,\n    filter\n  };\n  const attrs = {\n    pad,\n    strides,\n    inputShape: xShape5D\n  };\n  // tslint:disable-next-line: no-unnecessary-type-assertion\n  const res = ENGINE.runKernel(Conv3DBackpropInputV2, inputs, attrs);\n  if (reshapedTo5D) {\n    return reshape(res, [res.shape[1], res.shape[2], res.shape[3], res.shape[4]]);\n  }\n  return res;\n}\nexport const conv3DBackpropInput = /* @__PURE__ */op({\n  conv3DBackpropInput_\n});", "map": {"version": 3, "names": ["ENGINE", "Conv3DBackpropInputV2", "util", "op", "reshape", "conv3DBackpropInput_", "xShape", "dy", "filter", "strides", "pad", "assert", "length", "rank", "concat", "xShape5D", "dy5D", "reshapedTo5D", "shape", "inDepth", "outDepth", "inputs", "attrs", "inputShape", "res", "runKernel", "conv3DBackpropInput"], "sources": ["C:\\tfjs-core\\src\\ops\\conv3d_backprop_input.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport {ENGINE} from '../engine';\nimport {Conv3DBackpropInputV2, Conv3DBackpropInputV2Attrs, Conv3DBackpropInputV2Inputs} from '../kernel_names';\nimport {NamedAttrMap} from '../kernel_registry';\nimport {Tensor4D, Tensor5D} from '../tensor';\nimport {NamedTensorMap} from '../tensor_types';\nimport * as util from '../util';\n\nimport {op} from './operation';\nimport {reshape} from './reshape';\n\n/**\n * Computes the derivative of the input of a 3D convolution.\n *\n * @param xShape The shape of the input: [batch, depth, height, width,\n * in_channels]. If length of 4, batch of 1 is assumed.\n * @param dy The derivative of the output, of rank 5 or rank 4 of shape\n *   `[batch, outDepth, outHeight, outWidth, in_channels]`.\n * If rank 4, batch of 1 is assumed.\n * @param filter The filter, rank 5, of shape\n *     `[filterDepth, filterHeight, filterWidth, inDepth, outDepth]`.\n * @param strides The strides of the convolution: `[strideDepth, strideHeight,\n * strideWidth]`.\n * @param pad The type of padding algorithm used:\n *    - `same` and stride 1: output will be of same size as input,\n *       regardless of filter size.\n *    - `valid`: output will be smaller than input if filter is larger\n *       than 1x1.\n */\nfunction conv3DBackpropInput_<T extends Tensor4D|Tensor5D>(\n    xShape:\n        [number, number, number, number,\n         number]|[number, number, number, number],\n    dy: T, filter: Tensor5D, strides: [number, number, number]|number,\n    pad: 'valid'|'same'): T {\n  util.assert(\n      xShape.length === dy.rank,\n      () => `Length of inShape ` +\n          `(${xShape.length}) and rank of dy (${dy.rank}) must match`);\n\n  let xShape5D = xShape as [number, number, number, number, number];\n  let dy5D = dy as Tensor5D;\n  let reshapedTo5D = false;\n  if (dy.rank === 4) {\n    reshapedTo5D = true;\n    dy5D = reshape(dy, [1, dy.shape[0], dy.shape[1], dy.shape[2], dy.shape[3]]);\n    xShape5D = [1, xShape[0], xShape[1], xShape[2], xShape[3]];\n  }\n\n  const inDepth = xShape5D[4];\n  const outDepth = dy5D.shape[4];\n  util.assert(\n      xShape5D.length === 5,\n      () =>\n          `Error in conv3dDerInput: inShape must be length 5, but got length ` +\n          `${xShape5D.length}.`);\n  util.assert(\n      dy5D.rank === 5,\n      () => `Error in conv3dDerInput: dy must be rank 5, but got ` +\n          `rank ${dy5D.rank}`);\n  util.assert(\n      filter.rank === 5,\n      () => `Error in conv3dDerInput: filter must be rank 5, but got ` +\n          `rank ${filter.rank}`);\n  util.assert(\n      inDepth === filter.shape[3],\n      () => `Error in conv3dDerInput: depth of input (${inDepth}) must ` +\n          `match input depth for filter ${filter.shape[3]}.`);\n  util.assert(\n      outDepth === filter.shape[4],\n      () => `Error in conv3dDerInput: depth of output (${outDepth}) must ` +\n          `match output depth for filter ${filter.shape[4]}.`);\n\n  const inputs: Conv3DBackpropInputV2Inputs = {dy: dy5D, filter};\n\n  const attrs:\n      Conv3DBackpropInputV2Attrs = {pad, strides, inputShape: xShape5D};\n\n  // tslint:disable-next-line: no-unnecessary-type-assertion\n  const res = ENGINE.runKernel(\n                  Conv3DBackpropInputV2, inputs as unknown as NamedTensorMap,\n                  attrs as unknown as NamedAttrMap) as T;\n\n  if (reshapedTo5D) {\n    return reshape(\n               res, [res.shape[1], res.shape[2], res.shape[3], res.shape[4]]) as\n        T;\n  }\n  return res;\n}\n\nexport const conv3DBackpropInput = /* @__PURE__ */ op({conv3DBackpropInput_});\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAgBA,SAAQA,MAAM,QAAO,WAAW;AAChC,SAAQC,qBAAqB,QAAgE,iBAAiB;AAI9G,OAAO,KAAKC,IAAI,MAAM,SAAS;AAE/B,SAAQC,EAAE,QAAO,aAAa;AAC9B,SAAQC,OAAO,QAAO,WAAW;AAEjC;;;;;;;;;;;;;;;;;;AAkBA,SAASC,oBAAoBA,CACzBC,MAE6C,EAC7CC,EAAK,EAAEC,MAAgB,EAAEC,OAAwC,EACjEC,GAAmB;EACrBR,IAAI,CAACS,MAAM,CACPL,MAAM,CAACM,MAAM,KAAKL,EAAE,CAACM,IAAI,EACzB,MAAM,2BAAAC,MAAA,CACER,MAAM,CAACM,MAAM,wBAAAE,MAAA,CAAqBP,EAAE,CAACM,IAAI,iBAAc,CAAC;EAEpE,IAAIE,QAAQ,GAAGT,MAAkD;EACjE,IAAIU,IAAI,GAAGT,EAAc;EACzB,IAAIU,YAAY,GAAG,KAAK;EACxB,IAAIV,EAAE,CAACM,IAAI,KAAK,CAAC,EAAE;IACjBI,YAAY,GAAG,IAAI;IACnBD,IAAI,GAAGZ,OAAO,CAACG,EAAE,EAAE,CAAC,CAAC,EAAEA,EAAE,CAACW,KAAK,CAAC,CAAC,CAAC,EAAEX,EAAE,CAACW,KAAK,CAAC,CAAC,CAAC,EAAEX,EAAE,CAACW,KAAK,CAAC,CAAC,CAAC,EAAEX,EAAE,CAACW,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;IAC3EH,QAAQ,GAAG,CAAC,CAAC,EAAET,MAAM,CAAC,CAAC,CAAC,EAAEA,MAAM,CAAC,CAAC,CAAC,EAAEA,MAAM,CAAC,CAAC,CAAC,EAAEA,MAAM,CAAC,CAAC,CAAC,CAAC;;EAG5D,MAAMa,OAAO,GAAGJ,QAAQ,CAAC,CAAC,CAAC;EAC3B,MAAMK,QAAQ,GAAGJ,IAAI,CAACE,KAAK,CAAC,CAAC,CAAC;EAC9BhB,IAAI,CAACS,MAAM,CACPI,QAAQ,CAACH,MAAM,KAAK,CAAC,EACrB,MACI,0EAAAE,MAAA,CACGC,QAAQ,CAACH,MAAM,MAAG,CAAC;EAC9BV,IAAI,CAACS,MAAM,CACPK,IAAI,CAACH,IAAI,KAAK,CAAC,EACf,MAAM,iEAAAC,MAAA,CACME,IAAI,CAACH,IAAI,CAAE,CAAC;EAC5BX,IAAI,CAACS,MAAM,CACPH,MAAM,CAACK,IAAI,KAAK,CAAC,EACjB,MAAM,qEAAAC,MAAA,CACMN,MAAM,CAACK,IAAI,CAAE,CAAC;EAC9BX,IAAI,CAACS,MAAM,CACPQ,OAAO,KAAKX,MAAM,CAACU,KAAK,CAAC,CAAC,CAAC,EAC3B,MAAM,4CAAAJ,MAAA,CAA4CK,OAAO,+CAAAL,MAAA,CACrBN,MAAM,CAACU,KAAK,CAAC,CAAC,CAAC,MAAG,CAAC;EAC3DhB,IAAI,CAACS,MAAM,CACPS,QAAQ,KAAKZ,MAAM,CAACU,KAAK,CAAC,CAAC,CAAC,EAC5B,MAAM,6CAAAJ,MAAA,CAA6CM,QAAQ,gDAAAN,MAAA,CACtBN,MAAM,CAACU,KAAK,CAAC,CAAC,CAAC,MAAG,CAAC;EAE5D,MAAMG,MAAM,GAAgC;IAACd,EAAE,EAAES,IAAI;IAAER;EAAM,CAAC;EAE9D,MAAMc,KAAK,GACsB;IAACZ,GAAG;IAAED,OAAO;IAAEc,UAAU,EAAER;EAAQ,CAAC;EAErE;EACA,MAAMS,GAAG,GAAGxB,MAAM,CAACyB,SAAS,CACZxB,qBAAqB,EAAEoB,MAAmC,EAC1DC,KAAgC,CAAM;EAEtD,IAAIL,YAAY,EAAE;IAChB,OAAOb,OAAO,CACHoB,GAAG,EAAE,CAACA,GAAG,CAACN,KAAK,CAAC,CAAC,CAAC,EAAEM,GAAG,CAACN,KAAK,CAAC,CAAC,CAAC,EAAEM,GAAG,CAACN,KAAK,CAAC,CAAC,CAAC,EAAEM,GAAG,CAACN,KAAK,CAAC,CAAC,CAAC,CAAC,CACnE;;EAEP,OAAOM,GAAG;AACZ;AAEA,OAAO,MAAME,mBAAmB,GAAG,eAAgBvB,EAAE,CAAC;EAACE;AAAoB,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}