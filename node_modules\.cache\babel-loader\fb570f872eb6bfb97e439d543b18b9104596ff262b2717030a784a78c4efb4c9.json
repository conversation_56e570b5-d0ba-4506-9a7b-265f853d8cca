{"ast": null, "code": "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { ENGINE } from '../engine';\nimport { SpaceToBatchND } from '../kernel_names';\nimport { convertToTensor } from '../tensor_util_env';\nimport * as util from '../util';\nimport { op } from './operation';\n/**\n * This operation divides \"spatial\" dimensions `[1, ..., M]` of the input into\n * a grid of blocks of shape `blockShape`, and interleaves these blocks with\n * the \"batch\" dimension (0) such that in the output, the spatial\n * dimensions `[1, ..., M]` correspond to the position within the grid,\n * and the batch dimension combines both the position within a spatial block\n * and the original batch position. Prior to division into blocks,\n * the spatial dimensions of the input are optionally zero padded\n * according to `paddings`. See below for a precise description.\n *\n * ```js\n * const x = tf.tensor4d([1, 2, 3, 4], [1, 2, 2, 1]);\n * const blockShape = [2, 2];\n * const paddings = [[0, 0], [0, 0]];\n *\n * x.spaceToBatchND(blockShape, paddings).print();\n * ```\n *\n * @param x A `tf.Tensor`. N-D with `x.shape` = `[batch] + spatialShape +\n * remainingShape`, where spatialShape has `M` dimensions.\n * @param blockShape A 1-D array. Must have shape `[M]`, all values must\n * be >= 1.\n * @param paddings A 2-D array. Must have shape `[M, 2]`, all values must be >=\n *     0. `paddings[i] = [padStart, padEnd]` specifies the amount to zero-pad\n * from input dimension `i + 1`, which corresponds to spatial dimension `i`. It\n * is required that\n * `(inputShape[i + 1] + padStart + padEnd) % blockShape[i] === 0`\n *\n * This operation is equivalent to the following steps:\n *\n * 1. Zero-pad the start and end of dimensions `[1, ..., M]` of the input\n * according to `paddings` to produce `padded` of shape paddedShape.\n *\n * 2. Reshape `padded` to `reshapedPadded` of shape:\n * `[batch] + [paddedShape[1] / blockShape[0], blockShape[0], ...,\n * paddedShape[M] / blockShape[M-1], blockShape[M-1]] + remainingShape`\n *\n * 3. Permute dimensions of `reshapedPadded` to produce `permutedReshapedPadded`\n * of shape: `blockShape + [batch] + [paddedShape[1] / blockShape[0], ...,\n * paddedShape[M] / blockShape[M-1]] + remainingShape`\n *\n * 4. Reshape `permutedReshapedPadded` to flatten `blockShape` into the\n * batch dimension, producing an output tensor of shape:\n * `[batch * prod(blockShape)] + [paddedShape[1] / blockShape[0], ...,\n * paddedShape[M] / blockShape[M-1]] + remainingShape`\n *\n * @doc {heading: 'Tensors', subheading: 'Transformations'}\n */\nfunction spaceToBatchND_(x, blockShape, paddings) {\n  const $x = convertToTensor(x, 'x', 'spaceToBatchND');\n  util.assert($x.rank >= 1 + blockShape.length, () => `input rank ${$x.rank} should be > than [blockShape] ${blockShape.length}`);\n  util.assert(paddings.length === blockShape.length, () => `paddings.shape[0] ${paddings.length} must be equal to [blockShape] ${blockShape.length}`);\n  util.assert($x.shape.reduce((a, b, i) => {\n    if (i > 0 && i <= blockShape.length) {\n      return a && (b + paddings[i - 1][0] + paddings[i - 1][1]) % blockShape[i - 1] === 0;\n    }\n    return a;\n  }, true), () => `input spatial dimensions ${$x.shape.slice(1)} with paddings ${paddings.toString()} must be divisible by blockShapes ${blockShape.toString()}`);\n  const inputs = {\n    x: $x\n  };\n  const attrs = {\n    blockShape,\n    paddings\n  };\n  return ENGINE.runKernel(SpaceToBatchND, inputs, attrs);\n}\nexport const spaceToBatchND = /* @__PURE__ */op({\n  spaceToBatchND_\n});", "map": {"version": 3, "names": ["ENGINE", "SpaceToBatchND", "convertToTensor", "util", "op", "spaceToBatchND_", "x", "blockShape", "paddings", "$x", "assert", "rank", "length", "shape", "reduce", "a", "b", "i", "slice", "toString", "inputs", "attrs", "runKernel", "spaceToBatchND"], "sources": ["C:\\tfjs-core\\src\\ops\\space_to_batch_nd.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {ENGINE} from '../engine';\nimport {SpaceToBatchND, SpaceToBatchNDAttrs, SpaceToBatchNDInputs} from '../kernel_names';\nimport {NamedAttrMap} from '../kernel_registry';\nimport {Tensor} from '../tensor';\nimport {NamedTensorMap} from '../tensor_types';\nimport {convertToTensor} from '../tensor_util_env';\nimport {TensorLike} from '../types';\nimport * as util from '../util';\n\nimport {op} from './operation';\n\n/**\n * This operation divides \"spatial\" dimensions `[1, ..., M]` of the input into\n * a grid of blocks of shape `blockShape`, and interleaves these blocks with\n * the \"batch\" dimension (0) such that in the output, the spatial\n * dimensions `[1, ..., M]` correspond to the position within the grid,\n * and the batch dimension combines both the position within a spatial block\n * and the original batch position. Prior to division into blocks,\n * the spatial dimensions of the input are optionally zero padded\n * according to `paddings`. See below for a precise description.\n *\n * ```js\n * const x = tf.tensor4d([1, 2, 3, 4], [1, 2, 2, 1]);\n * const blockShape = [2, 2];\n * const paddings = [[0, 0], [0, 0]];\n *\n * x.spaceToBatchND(blockShape, paddings).print();\n * ```\n *\n * @param x A `tf.Tensor`. N-D with `x.shape` = `[batch] + spatialShape +\n * remainingShape`, where spatialShape has `M` dimensions.\n * @param blockShape A 1-D array. Must have shape `[M]`, all values must\n * be >= 1.\n * @param paddings A 2-D array. Must have shape `[M, 2]`, all values must be >=\n *     0. `paddings[i] = [padStart, padEnd]` specifies the amount to zero-pad\n * from input dimension `i + 1`, which corresponds to spatial dimension `i`. It\n * is required that\n * `(inputShape[i + 1] + padStart + padEnd) % blockShape[i] === 0`\n *\n * This operation is equivalent to the following steps:\n *\n * 1. Zero-pad the start and end of dimensions `[1, ..., M]` of the input\n * according to `paddings` to produce `padded` of shape paddedShape.\n *\n * 2. Reshape `padded` to `reshapedPadded` of shape:\n * `[batch] + [paddedShape[1] / blockShape[0], blockShape[0], ...,\n * paddedShape[M] / blockShape[M-1], blockShape[M-1]] + remainingShape`\n *\n * 3. Permute dimensions of `reshapedPadded` to produce `permutedReshapedPadded`\n * of shape: `blockShape + [batch] + [paddedShape[1] / blockShape[0], ...,\n * paddedShape[M] / blockShape[M-1]] + remainingShape`\n *\n * 4. Reshape `permutedReshapedPadded` to flatten `blockShape` into the\n * batch dimension, producing an output tensor of shape:\n * `[batch * prod(blockShape)] + [paddedShape[1] / blockShape[0], ...,\n * paddedShape[M] / blockShape[M-1]] + remainingShape`\n *\n * @doc {heading: 'Tensors', subheading: 'Transformations'}\n */\nfunction spaceToBatchND_<T extends Tensor>(\n    x: T|TensorLike, blockShape: number[], paddings: number[][]): T {\n  const $x = convertToTensor(x, 'x', 'spaceToBatchND');\n\n  util.assert(\n      $x.rank >= 1 + blockShape.length,\n      () => `input rank ${$x.rank} should be > than [blockShape] ${\n          blockShape.length}`);\n\n  util.assert(\n      paddings.length === blockShape.length,\n      () => `paddings.shape[0] ${\n          paddings.length} must be equal to [blockShape] ${blockShape.length}`);\n\n  util.assert(\n      $x.shape.reduce(\n          (a, b, i) => {\n            if (i > 0 && i <= blockShape.length) {\n              return a &&\n                  ((b + paddings[i - 1][0] + paddings[i - 1][1]) %\n                       blockShape[i - 1] ===\n                   0);\n            }\n            return a;\n          },\n          true),\n      () => `input spatial dimensions ${$x.shape.slice(1)} with paddings ${\n          paddings.toString()} must be divisible by blockShapes ${\n          blockShape.toString()}`);\n\n  const inputs: SpaceToBatchNDInputs = {x: $x};\n  const attrs: SpaceToBatchNDAttrs = {blockShape, paddings};\n\n  return ENGINE.runKernel(\n      SpaceToBatchND, inputs as unknown as NamedTensorMap,\n      attrs as unknown as NamedAttrMap);\n}\n\nexport const spaceToBatchND = /* @__PURE__ */ op({spaceToBatchND_});\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAiBA,SAAQA,MAAM,QAAO,WAAW;AAChC,SAAQC,cAAc,QAAkD,iBAAiB;AAIzF,SAAQC,eAAe,QAAO,oBAAoB;AAElD,OAAO,KAAKC,IAAI,MAAM,SAAS;AAE/B,SAAQC,EAAE,QAAO,aAAa;AAE9B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgDA,SAASC,eAAeA,CACpBC,CAAe,EAAEC,UAAoB,EAAEC,QAAoB;EAC7D,MAAMC,EAAE,GAAGP,eAAe,CAACI,CAAC,EAAE,GAAG,EAAE,gBAAgB,CAAC;EAEpDH,IAAI,CAACO,MAAM,CACPD,EAAE,CAACE,IAAI,IAAI,CAAC,GAAGJ,UAAU,CAACK,MAAM,EAChC,MAAM,cAAcH,EAAE,CAACE,IAAI,kCACvBJ,UAAU,CAACK,MAAM,EAAE,CAAC;EAE5BT,IAAI,CAACO,MAAM,CACPF,QAAQ,CAACI,MAAM,KAAKL,UAAU,CAACK,MAAM,EACrC,MAAM,qBACFJ,QAAQ,CAACI,MAAM,kCAAkCL,UAAU,CAACK,MAAM,EAAE,CAAC;EAE7ET,IAAI,CAACO,MAAM,CACPD,EAAE,CAACI,KAAK,CAACC,MAAM,CACX,CAACC,CAAC,EAAEC,CAAC,EAAEC,CAAC,KAAI;IACV,IAAIA,CAAC,GAAG,CAAC,IAAIA,CAAC,IAAIV,UAAU,CAACK,MAAM,EAAE;MACnC,OAAOG,CAAC,IACH,CAACC,CAAC,GAAGR,QAAQ,CAACS,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGT,QAAQ,CAACS,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,IACxCV,UAAU,CAACU,CAAC,GAAG,CAAC,CAAC,KACrB,CAAE;;IAET,OAAOF,CAAC;EACV,CAAC,EACD,IAAI,CAAC,EACT,MAAM,4BAA4BN,EAAE,CAACI,KAAK,CAACK,KAAK,CAAC,CAAC,CAAC,kBAC/CV,QAAQ,CAACW,QAAQ,EAAE,qCACnBZ,UAAU,CAACY,QAAQ,EAAE,EAAE,CAAC;EAEhC,MAAMC,MAAM,GAAyB;IAACd,CAAC,EAAEG;EAAE,CAAC;EAC5C,MAAMY,KAAK,GAAwB;IAACd,UAAU;IAAEC;EAAQ,CAAC;EAEzD,OAAOR,MAAM,CAACsB,SAAS,CACnBrB,cAAc,EAAEmB,MAAmC,EACnDC,KAAgC,CAAC;AACvC;AAEA,OAAO,MAAME,cAAc,GAAG,eAAgBnB,EAAE,CAAC;EAACC;AAAe,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}