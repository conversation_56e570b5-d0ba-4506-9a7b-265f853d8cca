{"ast": null, "code": "/**\n * @license\n * Copyright 2022 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { searchSorted } from './search_sorted';\n/**\n * Searches for where a value would go in a sorted sequence.\n *\n * This is not a method for checking containment (like javascript in).\n *\n * The typical use case for this operation is \"binning\", \"bucketing\", or\n * \"discretizing\". The values are assigned to bucket-indices based on the edges\n * listed in 'sortedSequence'. This operation returns the bucket-index for each\n * value.\n *\n * The index returned corresponds to the first edge greater than the value.\n *\n * The axis is not settable for this operation. It always operates on the\n * innermost dimension (axis=-1). The operation will accept any number of outer\n * dimensions.\n *\n * Note: This operation assumes that 'upperBound' is sorted along the\n * innermost axis, maybe using 'sort(..., axis=-1)'. If the sequence is not\n * sorted no error is raised and the content of the returned tensor is not well\n * defined.\n *\n * ```js\n * const seq = tf.tensor1d([0, 3, 9, 10, 10]);\n * const values = tf.tensor1d([0, 4, 10]);\n * const result = tf.upperBound(seq, values);\n * result.print(); // [1, 2, 5]\n * ```\n * @param sortedSequence: N-D. Sorted sequence.\n * @param values: N-D. Search values.\n * @return An N-D int32 tensor the size of values containing the result of\n *     applying upper bound to each value. The result is not a global index to\n *     the entire Tensor, but the index in the last dimension.\n * @doc {heading: 'Operations', subheading: 'Evaluation'}\n */\nexport function upperBound(sortedSequence, values) {\n  return searchSorted(sortedSequence, values, 'right');\n}", "map": {"version": 3, "names": ["searchSorted", "upperBound", "sortedSequence", "values"], "sources": ["C:\\tfjs-core\\src\\ops\\upper_bound.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2022 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {Tensor} from '../tensor';\nimport {TensorLike} from '../types';\nimport {searchSorted} from './search_sorted';\n\n/**\n * Searches for where a value would go in a sorted sequence.\n *\n * This is not a method for checking containment (like javascript in).\n *\n * The typical use case for this operation is \"binning\", \"bucketing\", or\n * \"discretizing\". The values are assigned to bucket-indices based on the edges\n * listed in 'sortedSequence'. This operation returns the bucket-index for each\n * value.\n *\n * The index returned corresponds to the first edge greater than the value.\n *\n * The axis is not settable for this operation. It always operates on the\n * innermost dimension (axis=-1). The operation will accept any number of outer\n * dimensions.\n *\n * Note: This operation assumes that 'upperBound' is sorted along the\n * innermost axis, maybe using 'sort(..., axis=-1)'. If the sequence is not\n * sorted no error is raised and the content of the returned tensor is not well\n * defined.\n *\n * ```js\n * const seq = tf.tensor1d([0, 3, 9, 10, 10]);\n * const values = tf.tensor1d([0, 4, 10]);\n * const result = tf.upperBound(seq, values);\n * result.print(); // [1, 2, 5]\n * ```\n * @param sortedSequence: N-D. Sorted sequence.\n * @param values: N-D. Search values.\n * @return An N-D int32 tensor the size of values containing the result of\n *     applying upper bound to each value. The result is not a global index to\n *     the entire Tensor, but the index in the last dimension.\n * @doc {heading: 'Operations', subheading: 'Evaluation'}\n */\nexport function upperBound(\n    sortedSequence: Tensor|TensorLike, values: Tensor|TensorLike): Tensor {\n  return searchSorted(sortedSequence, values, 'right');\n}\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAmBA,SAAQA,YAAY,QAAO,iBAAiB;AAE5C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkCA,OAAM,SAAUC,UAAUA,CACtBC,cAAiC,EAAEC,MAAyB;EAC9D,OAAOH,YAAY,CAACE,cAAc,EAAEC,MAAM,EAAE,OAAO,CAAC;AACtD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}