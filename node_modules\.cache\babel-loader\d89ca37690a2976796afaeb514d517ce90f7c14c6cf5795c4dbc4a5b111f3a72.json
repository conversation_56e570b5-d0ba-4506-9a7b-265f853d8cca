{"ast": null, "code": "/**\n * @license\n * Copyright 2018 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { getGlslDifferences } from '../../glsl_version';\nexport class FromPixelsPackedProgram {\n  constructor(outputShape) {\n    this.variableNames = ['A'];\n    this.packedInputs = false;\n    this.packedOutput = true;\n    const glsl = getGlslDifferences();\n    const [height, width] = outputShape;\n    this.outputShape = outputShape;\n    this.userCode = `\n      void main() {\n        ivec3 coords = getOutputCoords();\n        int texR = coords[0];\n        int texC = coords[1];\n        int depth = coords[2];\n\n        vec4 result = vec4(0.);\n\n        for(int row=0; row<=1; row++) {\n          for(int col=0; col<=1; col++) {\n            texC = coords[1] + row;\n            depth = coords[2] + col;\n\n            vec2 uv = (vec2(texC, texR) + halfCR) /\n                       vec2(${width}.0, ${height}.0);\n            vec4 values = ${glsl.texture2D}(A, uv);\n            float value;\n            if (depth == 0) {\n              value = values.r;\n            } else if (depth == 1) {\n              value = values.g;\n            } else if (depth == 2) {\n              value = values.b;\n            } else if (depth == 3) {\n              value = values.a;\n            }\n\n            result[row * 2 + col] = floor(value * 255.0 + 0.5);\n          }\n        }\n\n        ${glsl.output} = result;\n      }\n    `;\n  }\n}", "map": {"version": 3, "names": ["getGlslDifferences", "FromPixelsPackedProgram", "constructor", "outputShape", "variableNames", "packedInputs", "packedOutput", "glsl", "height", "width", "userCode", "texture2D", "output"], "sources": ["C:\\tfjs-backend-webgl\\src\\kernels\\FromPixels_utils\\from_pixels_packed_gpu.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2018 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {getGlslDifferences} from '../../glsl_version';\nimport {GPGPUProgram} from '../../gpgpu_math';\n\nexport class FromPixelsPackedProgram implements GPGPUProgram {\n  variableNames = ['A'];\n  userCode: string;\n  outputShape: number[];\n  packedInputs = false;\n  packedOutput = true;\n\n  constructor(outputShape: number[]) {\n    const glsl = getGlslDifferences();\n    const [height, width, ] = outputShape;\n    this.outputShape = outputShape;\n    this.userCode = `\n      void main() {\n        ivec3 coords = getOutputCoords();\n        int texR = coords[0];\n        int texC = coords[1];\n        int depth = coords[2];\n\n        vec4 result = vec4(0.);\n\n        for(int row=0; row<=1; row++) {\n          for(int col=0; col<=1; col++) {\n            texC = coords[1] + row;\n            depth = coords[2] + col;\n\n            vec2 uv = (vec2(texC, texR) + halfCR) /\n                       vec2(${width}.0, ${height}.0);\n            vec4 values = ${glsl.texture2D}(A, uv);\n            float value;\n            if (depth == 0) {\n              value = values.r;\n            } else if (depth == 1) {\n              value = values.g;\n            } else if (depth == 2) {\n              value = values.b;\n            } else if (depth == 3) {\n              value = values.a;\n            }\n\n            result[row * 2 + col] = floor(value * 255.0 + 0.5);\n          }\n        }\n\n        ${glsl.output} = result;\n      }\n    `;\n  }\n}\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAiBA,SAAQA,kBAAkB,QAAO,oBAAoB;AAGrD,OAAM,MAAOC,uBAAuB;EAOlCC,YAAYC,WAAqB;IANjC,KAAAC,aAAa,GAAG,CAAC,GAAG,CAAC;IAGrB,KAAAC,YAAY,GAAG,KAAK;IACpB,KAAAC,YAAY,GAAG,IAAI;IAGjB,MAAMC,IAAI,GAAGP,kBAAkB,EAAE;IACjC,MAAM,CAACQ,MAAM,EAAEC,KAAK,CAAG,GAAGN,WAAW;IACrC,IAAI,CAACA,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACO,QAAQ,GAAG;;;;;;;;;;;;;;;8BAeUD,KAAK,OAAOD,MAAM;4BACpBD,IAAI,CAACI,SAAS;;;;;;;;;;;;;;;;UAgBhCJ,IAAI,CAACK,MAAM;;KAEhB;EACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}