{"ast": null, "code": "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { backend_util, util } from '@tensorflow/tfjs-core';\n/**\n * Template that creates implementation for binary ops. Supports broadcast.\n */\nexport function createSimpleBinaryKernelImpl(op) {\n  return (aShape, bShape, aVals, bVals, dtype) => {\n    const newShape = backend_util.assertAndGetBroadcastShape(aShape, bShape);\n    const resultRank = newShape.length;\n    const resultStrides = util.computeStrides(newShape);\n    const resultSize = util.sizeFromShape(newShape);\n    const result = util.getTypedArrayFromDType(dtype, resultSize);\n    const aRank = aShape.length;\n    const bRank = bShape.length;\n    const aStrides = util.computeStrides(aShape);\n    const bStrides = util.computeStrides(bShape);\n    const aBroadcastDims = backend_util.getBroadcastDims(aShape, newShape);\n    const bBroadcastDims = backend_util.getBroadcastDims(bShape, newShape);\n    if (aBroadcastDims.length + bBroadcastDims.length === 0) {\n      for (let i = 0; i < result.length; ++i) {\n        result[i] = op(aVals[i % aVals.length], bVals[i % bVals.length]);\n      }\n    } else {\n      for (let i = 0; i < result.length; ++i) {\n        const loc = util.indexToLoc(i, resultRank, resultStrides);\n        const aLoc = loc.slice(-aRank);\n        aBroadcastDims.forEach(d => aLoc[d] = 0);\n        const aIndex = util.locToIndex(aLoc, aRank, aStrides);\n        const bLoc = loc.slice(-bRank);\n        bBroadcastDims.forEach(d => bLoc[d] = 0);\n        const bIndex = util.locToIndex(bLoc, bRank, bStrides);\n        result[i] = op(aVals[aIndex], bVals[bIndex]);\n      }\n    }\n    return [result, newShape];\n  };\n}", "map": {"version": 3, "names": ["backend_util", "util", "createSimpleBinaryKernelImpl", "op", "aShape", "bShape", "aVals", "bVals", "dtype", "newShape", "assertAndGetBroadcastShape", "resultRank", "length", "<PERSON><PERSON><PERSON><PERSON>", "computeStrides", "resultSize", "sizeFromShape", "result", "getTypedArrayFromDType", "aRank", "bRank", "a<PERSON><PERSON><PERSON>", "b<PERSON><PERSON><PERSON>", "aBroadcastDims", "getBroadcastDims", "bBroadcastDims", "i", "loc", "indexToLoc", "aLoc", "slice", "for<PERSON>ach", "d", "aIndex", "locToIndex", "bLoc", "bIndex"], "sources": ["C:\\tfjs-backend-cpu\\src\\utils\\binary_impl.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {backend_util, DataType, DataValues, NumericDataType, TypedArray, util} from '@tensorflow/tfjs-core';\n\nimport {SimpleBinaryKernelImpl, SimpleBinaryOperation} from './binary_types';\n\n/**\n * Template that creates implementation for binary ops. Supports broadcast.\n */\nexport function createSimpleBinaryKernelImpl(op: SimpleBinaryOperation):\n    SimpleBinaryKernelImpl {\n  return (aShape: number[], bShape: number[], aVals: DataValues,\n          bVals: DataValues, dtype: DataType): [TypedArray, number[]] => {\n    const newShape = backend_util.assertAndGetBroadcastShape(aShape, bShape);\n\n    const resultRank = newShape.length;\n    const resultStrides = util.computeStrides(newShape);\n    const resultSize = util.sizeFromShape(newShape);\n\n    const result =\n        util.getTypedArrayFromDType(dtype as NumericDataType, resultSize);\n\n    const aRank = aShape.length;\n    const bRank = bShape.length;\n\n    const aStrides = util.computeStrides(aShape);\n    const bStrides = util.computeStrides(bShape);\n\n    const aBroadcastDims = backend_util.getBroadcastDims(aShape, newShape);\n    const bBroadcastDims = backend_util.getBroadcastDims(bShape, newShape);\n\n    if (aBroadcastDims.length + bBroadcastDims.length === 0) {\n      for (let i = 0; i < result.length; ++i) {\n        result[i] = op(aVals[i % aVals.length], bVals[i % bVals.length]);\n      }\n    } else {\n      for (let i = 0; i < result.length; ++i) {\n        const loc = util.indexToLoc(i, resultRank, resultStrides);\n\n        const aLoc = loc.slice(-aRank);\n        aBroadcastDims.forEach(d => aLoc[d] = 0);\n        const aIndex = util.locToIndex(aLoc, aRank, aStrides);\n\n        const bLoc = loc.slice(-bRank);\n        bBroadcastDims.forEach(d => bLoc[d] = 0);\n        const bIndex = util.locToIndex(bLoc, bRank, bStrides);\n\n        result[i] = op(aVals[aIndex], bVals[bIndex]);\n      }\n    }\n\n    return [result, newShape];\n  };\n}\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAiBA,SAAQA,YAAY,EAAqDC,IAAI,QAAO,uBAAuB;AAI3G;;;AAGA,OAAM,SAAUC,4BAA4BA,CAACC,EAAyB;EAEpE,OAAO,CAACC,MAAgB,EAAEC,MAAgB,EAAEC,KAAiB,EACrDC,KAAiB,EAAEC,KAAe,KAA4B;IACpE,MAAMC,QAAQ,GAAGT,YAAY,CAACU,0BAA0B,CAACN,MAAM,EAAEC,MAAM,CAAC;IAExE,MAAMM,UAAU,GAAGF,QAAQ,CAACG,MAAM;IAClC,MAAMC,aAAa,GAAGZ,IAAI,CAACa,cAAc,CAACL,QAAQ,CAAC;IACnD,MAAMM,UAAU,GAAGd,IAAI,CAACe,aAAa,CAACP,QAAQ,CAAC;IAE/C,MAAMQ,MAAM,GACRhB,IAAI,CAACiB,sBAAsB,CAACV,KAAwB,EAAEO,UAAU,CAAC;IAErE,MAAMI,KAAK,GAAGf,MAAM,CAACQ,MAAM;IAC3B,MAAMQ,KAAK,GAAGf,MAAM,CAACO,MAAM;IAE3B,MAAMS,QAAQ,GAAGpB,IAAI,CAACa,cAAc,CAACV,MAAM,CAAC;IAC5C,MAAMkB,QAAQ,GAAGrB,IAAI,CAACa,cAAc,CAACT,MAAM,CAAC;IAE5C,MAAMkB,cAAc,GAAGvB,YAAY,CAACwB,gBAAgB,CAACpB,MAAM,EAAEK,QAAQ,CAAC;IACtE,MAAMgB,cAAc,GAAGzB,YAAY,CAACwB,gBAAgB,CAACnB,MAAM,EAAEI,QAAQ,CAAC;IAEtE,IAAIc,cAAc,CAACX,MAAM,GAAGa,cAAc,CAACb,MAAM,KAAK,CAAC,EAAE;MACvD,KAAK,IAAIc,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGT,MAAM,CAACL,MAAM,EAAE,EAAEc,CAAC,EAAE;QACtCT,MAAM,CAACS,CAAC,CAAC,GAAGvB,EAAE,CAACG,KAAK,CAACoB,CAAC,GAAGpB,KAAK,CAACM,MAAM,CAAC,EAAEL,KAAK,CAACmB,CAAC,GAAGnB,KAAK,CAACK,MAAM,CAAC,CAAC;;KAEnE,MAAM;MACL,KAAK,IAAIc,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGT,MAAM,CAACL,MAAM,EAAE,EAAEc,CAAC,EAAE;QACtC,MAAMC,GAAG,GAAG1B,IAAI,CAAC2B,UAAU,CAACF,CAAC,EAAEf,UAAU,EAAEE,aAAa,CAAC;QAEzD,MAAMgB,IAAI,GAAGF,GAAG,CAACG,KAAK,CAAC,CAACX,KAAK,CAAC;QAC9BI,cAAc,CAACQ,OAAO,CAACC,CAAC,IAAIH,IAAI,CAACG,CAAC,CAAC,GAAG,CAAC,CAAC;QACxC,MAAMC,MAAM,GAAGhC,IAAI,CAACiC,UAAU,CAACL,IAAI,EAAEV,KAAK,EAAEE,QAAQ,CAAC;QAErD,MAAMc,IAAI,GAAGR,GAAG,CAACG,KAAK,CAAC,CAACV,KAAK,CAAC;QAC9BK,cAAc,CAACM,OAAO,CAACC,CAAC,IAAIG,IAAI,CAACH,CAAC,CAAC,GAAG,CAAC,CAAC;QACxC,MAAMI,MAAM,GAAGnC,IAAI,CAACiC,UAAU,CAACC,IAAI,EAAEf,KAAK,EAAEE,QAAQ,CAAC;QAErDL,MAAM,CAACS,CAAC,CAAC,GAAGvB,EAAE,CAACG,KAAK,CAAC2B,MAAM,CAAC,EAAE1B,KAAK,CAAC6B,MAAM,CAAC,CAAC;;;IAIhD,OAAO,CAACnB,MAAM,EAAER,QAAQ,CAAC;EAC3B,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}