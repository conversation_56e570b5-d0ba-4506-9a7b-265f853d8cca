{"ast": null, "code": "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { ENGINE } from '../engine';\nimport { NotEqual } from '../kernel_names';\nimport { makeTypesMatch } from '../tensor_util';\nimport { convertToTensor } from '../tensor_util_env';\nimport { assertAndGetBroadcastShape } from './broadcast_util';\nimport { op } from './operation';\n/**\n * Returns the truth value of (a != b) element-wise. Supports broadcasting.\n *\n * ```js\n * const a = tf.tensor1d([1, 2, 3]);\n * const b = tf.tensor1d([0, 2, 3]);\n *\n * a.notEqual(b).print();\n * ```\n * @param a The first input tensor.\n * @param b The second input tensor. Must have the same dtype as `a`.\n *\n * @doc {heading: 'Operations', subheading: 'Logical'}\n */\nfunction notEqual_(a, b) {\n  let $a = convertToTensor(a, 'a', 'notEqual', 'string_or_numeric');\n  let $b = convertToTensor(b, 'b', 'notEqual', 'string_or_numeric');\n  [$a, $b] = makeTypesMatch($a, $b);\n  assertAndGetBroadcastShape($a.shape, $b.shape);\n  const inputs = {\n    a: $a,\n    b: $b\n  };\n  return ENGINE.runKernel(NotEqual, inputs);\n}\nexport const notEqual = /* @__PURE__ */op({\n  notEqual_\n});", "map": {"version": 3, "names": ["ENGINE", "NotEqual", "makeTypesMatch", "convertToTensor", "assertAndGetBroadcastShape", "op", "notEqual_", "a", "b", "$a", "$b", "shape", "inputs", "runKernel", "notEqual"], "sources": ["C:\\tfjs-core\\src\\ops\\not_equal.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport {ENGINE} from '../engine';\nimport {NotEqual, NotEqualInputs} from '../kernel_names';\nimport {Tensor} from '../tensor';\nimport {NamedTensorMap} from '../tensor_types';\nimport {makeTypesMatch} from '../tensor_util';\nimport {convertToTensor} from '../tensor_util_env';\nimport {TensorLike} from '../types';\n\nimport {assertAndGetBroadcastShape} from './broadcast_util';\nimport {op} from './operation';\n\n/**\n * Returns the truth value of (a != b) element-wise. Supports broadcasting.\n *\n * ```js\n * const a = tf.tensor1d([1, 2, 3]);\n * const b = tf.tensor1d([0, 2, 3]);\n *\n * a.notEqual(b).print();\n * ```\n * @param a The first input tensor.\n * @param b The second input tensor. Must have the same dtype as `a`.\n *\n * @doc {heading: 'Operations', subheading: 'Logical'}\n */\nfunction notEqual_<T extends Tensor>(\n    a: Tensor|TensorLike, b: Tensor|TensorLike): T {\n  let $a = convertToTensor(a, 'a', 'notEqual', 'string_or_numeric');\n  let $b = convertToTensor(b, 'b', 'notEqual', 'string_or_numeric');\n  [$a, $b] = makeTypesMatch($a, $b);\n\n  assertAndGetBroadcastShape($a.shape, $b.shape);\n\n  const inputs: NotEqualInputs = {a: $a, b: $b};\n\n  return ENGINE.runKernel(NotEqual, inputs as unknown as NamedTensorMap);\n}\n\nexport const notEqual = /* @__PURE__ */ op({notEqual_});\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAgBA,SAAQA,MAAM,QAAO,WAAW;AAChC,SAAQC,QAAQ,QAAuB,iBAAiB;AAGxD,SAAQC,cAAc,QAAO,gBAAgB;AAC7C,SAAQC,eAAe,QAAO,oBAAoB;AAGlD,SAAQC,0BAA0B,QAAO,kBAAkB;AAC3D,SAAQC,EAAE,QAAO,aAAa;AAE9B;;;;;;;;;;;;;;AAcA,SAASC,SAASA,CACdC,CAAoB,EAAEC,CAAoB;EAC5C,IAAIC,EAAE,GAAGN,eAAe,CAACI,CAAC,EAAE,GAAG,EAAE,UAAU,EAAE,mBAAmB,CAAC;EACjE,IAAIG,EAAE,GAAGP,eAAe,CAACK,CAAC,EAAE,GAAG,EAAE,UAAU,EAAE,mBAAmB,CAAC;EACjE,CAACC,EAAE,EAAEC,EAAE,CAAC,GAAGR,cAAc,CAACO,EAAE,EAAEC,EAAE,CAAC;EAEjCN,0BAA0B,CAACK,EAAE,CAACE,KAAK,EAAED,EAAE,CAACC,KAAK,CAAC;EAE9C,MAAMC,MAAM,GAAmB;IAACL,CAAC,EAAEE,EAAE;IAAED,CAAC,EAAEE;EAAE,CAAC;EAE7C,OAAOV,MAAM,CAACa,SAAS,CAACZ,QAAQ,EAAEW,MAAmC,CAAC;AACxE;AAEA,OAAO,MAAME,QAAQ,GAAG,eAAgBT,EAAE,CAAC;EAACC;AAAS,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}