{"ast": null, "code": "/**\n * @license\n * Copyright 2017 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nexport class Conv2DProgram {\n  constructor(convInfo, addBias = false, activation = null, hasPreluActivationWeights = false, hasLeakyreluAlpha = false) {\n    this.variableNames = ['x', 'W'];\n    this.outputShape = convInfo.outShape;\n    const padTop = convInfo.padInfo.top;\n    const padLeft = convInfo.padInfo.left;\n    const strideHeight = convInfo.strideHeight;\n    const strideWidth = convInfo.strideWidth;\n    const dilationHeight = convInfo.dilationHeight;\n    const dilationWidth = convInfo.dilationWidth;\n    const filterHeight = convInfo.filterHeight;\n    const filterWidth = convInfo.filterWidth;\n    const inputDepthNearestVec4 = Math.floor(convInfo.inChannels / 4) * 4;\n    const inputDepthVec4Remainder = convInfo.inChannels % 4;\n    const isChannelsLast = convInfo.dataFormat === 'channelsLast';\n    const rowDim = isChannelsLast ? 1 : 2;\n    const colDim = isChannelsLast ? 2 : 3;\n    const channelDim = isChannelsLast ? 3 : 1;\n    let activationSnippet = '',\n      applyActivationSnippet = '';\n    if (activation) {\n      if (hasPreluActivationWeights) {\n        activationSnippet = `float activation(float a) {\n          float b = getPreluActivationWeightsAtOutCoords();\n          ${activation}\n        }`;\n      } else if (hasLeakyreluAlpha) {\n        activationSnippet = `float activation(float a) {\n          float b = getLeakyreluAlphaAtOutCoords();\n          ${activation}\n        }`;\n      } else {\n        activationSnippet = `\n          float activation(float x) {\n            ${activation}\n          }\n        `;\n      }\n      applyActivationSnippet = `result = activation(result);`;\n    }\n    const addBiasSnippet = addBias ? 'result += getBiasAtOutCoords();' : '';\n    if (addBias) {\n      this.variableNames.push('bias');\n    }\n    if (hasPreluActivationWeights) {\n      this.variableNames.push('preluActivationWeights');\n    }\n    if (hasLeakyreluAlpha) {\n      this.variableNames.push('leakyreluAlpha');\n    }\n    this.userCode = `\n      ${activationSnippet}\n\n      const ivec2 strides = ivec2(${strideHeight}, ${strideWidth});\n      const ivec2 pads = ivec2(${padTop}, ${padLeft});\n\n      void main() {\n        ivec4 coords = getOutputCoords();\n        int batch = coords[0];\n        int d2 = coords[${channelDim}];\n\n        ivec2 xRCCorner =\n            ivec2(coords[${rowDim}], coords[${colDim}]) * strides - pads;\n        int xRCorner = xRCCorner.x;\n        int xCCorner = xRCCorner.y;\n\n        // Convolve x(?, ?, d1) with w(:, :, d1, d2) to get y(yR, yC, d2).\n        // ? = to be determined. : = across all values in that axis.\n        float dotProd = 0.0;\n        for (int wR = 0; wR < ${filterHeight}; wR++) {\n          int xR = xRCorner + wR * ${dilationHeight};\n\n          if (xR < 0 || xR >= ${convInfo.inHeight}) {\n            continue;\n          }\n\n          for (int wC = 0; wC < ${filterWidth}; wC++) {\n            int xC = xCCorner + wC * ${dilationWidth};\n\n            if (xC < 0 || xC >= ${convInfo.inWidth}) {\n              continue;\n            }\n\n            for (int d1 = 0; d1 < ${inputDepthNearestVec4}; d1 += 4) {\n              vec4 wValues = vec4(\n                getW(wR, wC, d1, d2),\n                getW(wR, wC, d1 + 1, d2),\n                getW(wR, wC, d1 + 2, d2),\n                getW(wR, wC, d1 + 3, d2)\n              );\n\n              if (${isChannelsLast}) {\n                vec4 xValues = vec4(\n                  getX(batch, xR, xC, d1),\n                  getX(batch, xR, xC, d1 + 1),\n                  getX(batch, xR, xC, d1 + 2),\n                  getX(batch, xR, xC, d1 + 3)\n                );\n                dotProd += dot(xValues, wValues);\n              } else {\n                vec4 xValues = vec4(\n                  getX(batch, d1, xR, xC),\n                  getX(batch, d1 + 1, xR, xC),\n                  getX(batch, d1 + 2, xR, xC),\n                  getX(batch, d1 + 3, xR, xC)\n                );\n                dotProd += dot(xValues, wValues);\n              }\n            }\n\n            if (${inputDepthVec4Remainder === 1}) {\n\n              if (${isChannelsLast}) {\n                dotProd +=\n                    getX(batch, xR, xC, ${inputDepthNearestVec4}) *\n                    getW(wR, wC, ${inputDepthNearestVec4}, d2);\n              } else {\n                dotProd +=\n                    getX(batch, ${inputDepthNearestVec4}, xR, xC) *\n                    getW(wR, wC, ${inputDepthNearestVec4}, d2);\n              }\n\n            } else if (${inputDepthVec4Remainder === 2}) {\n              vec2 wValues = vec2(\n                getW(wR, wC, ${inputDepthNearestVec4}, d2),\n                getW(wR, wC, ${inputDepthNearestVec4} + 1, d2)\n              );\n\n              if (${isChannelsLast}) {\n                vec2 xValues = vec2(\n                  getX(batch, xR, xC, ${inputDepthNearestVec4}),\n                  getX(batch, xR, xC, ${inputDepthNearestVec4} + 1)\n                );\n                dotProd += dot(xValues, wValues);\n              } else {\n                vec2 xValues = vec2(\n                  getX(batch, ${inputDepthNearestVec4}, xR, xC),\n                  getX(batch, ${inputDepthNearestVec4} + 1, xR, xC)\n                );\n                dotProd += dot(xValues, wValues);\n              }\n\n            } else if (${inputDepthVec4Remainder === 3}) {\n              vec3 wValues = vec3(\n                getW(wR, wC, ${inputDepthNearestVec4}, d2),\n                getW(wR, wC, ${inputDepthNearestVec4} + 1, d2),\n                getW(wR, wC, ${inputDepthNearestVec4} + 2, d2)\n              );\n\n              if (${isChannelsLast}) {\n                vec3 xValues = vec3(\n                  getX(batch, xR, xC, ${inputDepthNearestVec4}),\n                  getX(batch, xR, xC, ${inputDepthNearestVec4} + 1),\n                  getX(batch, xR, xC, ${inputDepthNearestVec4} + 2)\n                );\n                dotProd += dot(xValues, wValues);\n              } else {\n                vec3 xValues = vec3(\n                  getX(batch, ${inputDepthNearestVec4}, xR, xC),\n                  getX(batch, ${inputDepthNearestVec4} + 1, xR, xC),\n                  getX(batch, ${inputDepthNearestVec4} + 2, xR, xC)\n                );\n                dotProd += dot(xValues, wValues);\n              }\n\n            }\n          }\n        }\n\n        float result = dotProd;\n        ${addBiasSnippet}\n        ${applyActivationSnippet}\n        setOutput(result);\n      }\n    `;\n  }\n}\nexport class Conv3DProgram {\n  constructor(convInfo) {\n    this.variableNames = ['x', 'W'];\n    this.outputShape = convInfo.outShape;\n    const padFront = convInfo.padInfo.front;\n    const padTop = convInfo.padInfo.top;\n    const padLeft = convInfo.padInfo.left;\n    const strideDepth = convInfo.strideDepth;\n    const strideHeight = convInfo.strideHeight;\n    const strideWidth = convInfo.strideWidth;\n    const dilationDepth = convInfo.dilationDepth;\n    const dilationHeight = convInfo.dilationHeight;\n    const dilationWidth = convInfo.dilationWidth;\n    const filterDepth = convInfo.filterDepth;\n    const filterHeight = convInfo.filterHeight;\n    const filterWidth = convInfo.filterWidth;\n    const inputDepthNearestVec4 = Math.floor(convInfo.inChannels / 4) * 4;\n    const inputDepthVec4Remainder = convInfo.inChannels % 4;\n    this.userCode = `\n      const ivec3 strides = ivec3(${strideDepth}, ${strideHeight}, ${strideWidth});\n      const ivec3 pads = ivec3(${padFront}, ${padTop}, ${padLeft});\n\n      void main() {\n        ivec5 coords = getOutputCoords();\n        int batch = coords.x;\n        int d2 = coords.u;\n\n        ivec3 xFRCCorner = ivec3(coords.y, coords.z, coords.w) * strides - pads;\n        int xFCorner = xFRCCorner.x;\n        int xRCorner = xFRCCorner.y;\n        int xCCorner = xFRCCorner.z;\n\n        // Convolve x(?, ?, ?, d1) with w(:, :, :, d1, d2) to get\n        // y(yF, yR, yC, d2). ? = to be determined. : = across all\n        // values in that axis.\n        float dotProd = 0.0;\n        for (int wF = 0; wF < ${filterDepth}; wF++) {\n          int xF = xFCorner + wF * ${dilationDepth};\n\n          if (xF < 0 || xF >= ${convInfo.inDepth}) {\n            continue;\n          }\n\n          for (int wR = 0; wR < ${filterHeight}; wR++) {\n            int xR = xRCorner + wR * ${dilationHeight};\n\n            if (xR < 0 || xR >= ${convInfo.inHeight}) {\n              continue;\n            }\n\n            for (int wC = 0; wC < ${filterWidth}; wC++) {\n              int xC = xCCorner + wC * ${dilationWidth};\n\n              if (xC < 0 || xC >= ${convInfo.inWidth}) {\n                continue;\n              }\n\n              for (int d1 = 0; d1 < ${inputDepthNearestVec4}; d1 += 4) {\n                vec4 xValues = vec4(\n                  getX(batch, xF, xR, xC, d1),\n                  getX(batch, xF, xR, xC, d1 + 1),\n                  getX(batch, xF, xR, xC, d1 + 2),\n                  getX(batch, xF, xR, xC, d1 + 3)\n                );\n                vec4 wValues = vec4(\n                  getW(wF, wR, wC, d1, d2),\n                  getW(wF, wR, wC, d1 + 1, d2),\n                  getW(wF, wR, wC, d1 + 2, d2),\n                  getW(wF, wR, wC, d1 + 3, d2)\n                );\n\n                dotProd += dot(xValues, wValues);\n              }\n\n              if (${inputDepthVec4Remainder === 1}) {\n                dotProd +=\n                  getX(batch, xF, xR, xC, ${inputDepthNearestVec4}) *\n                  getW(wF, wR, wC, ${inputDepthNearestVec4}, d2);\n              } else if (${inputDepthVec4Remainder === 2}) {\n                vec2 xValues = vec2(\n                  getX(batch, xF, xR, xC, ${inputDepthNearestVec4}),\n                  getX(batch, xF, xR, xC, ${inputDepthNearestVec4} + 1)\n                );\n                vec2 wValues = vec2(\n                  getW(wF, wR, wC, ${inputDepthNearestVec4}, d2),\n                  getW(wF, wR, wC, ${inputDepthNearestVec4} + 1, d2)\n                );\n                dotProd += dot(xValues, wValues);\n              } else if (${inputDepthVec4Remainder === 3}) {\n                vec3 xValues = vec3(\n                  getX(batch, xF, xR, xC, ${inputDepthNearestVec4}),\n                  getX(batch, xF, xR, xC, ${inputDepthNearestVec4} + 1),\n                  getX(batch, xF, xR, xC, ${inputDepthNearestVec4} + 2)\n                );\n                vec3 wValues = vec3(\n                  getW(wF, wR, wC, ${inputDepthNearestVec4}, d2),\n                  getW(wF, wR, wC, ${inputDepthNearestVec4} + 1, d2),\n                  getW(wF, wR, wC, ${inputDepthNearestVec4} + 2, d2)\n                );\n                dotProd += dot(xValues, wValues);\n              }\n            }\n          }\n        }\n        setOutput(dotProd);\n      }\n    `;\n  }\n}", "map": {"version": 3, "names": ["Conv2DProgram", "constructor", "convInfo", "addBias", "activation", "hasPreluActivationWeights", "hasLeakyreluAlpha", "variableNames", "outputShape", "outShape", "padTop", "padInfo", "top", "padLeft", "left", "strideHeight", "<PERSON><PERSON><PERSON><PERSON>", "dilationHeight", "dilationWidth", "filterHeight", "filterWidth", "inputDepthNearestVec4", "Math", "floor", "inChannels", "inputDepthVec4Remainder", "isChannelsLast", "dataFormat", "row<PERSON><PERSON>", "col<PERSON><PERSON>", "channelDim", "activationSnippet", "applyActivationSnippet", "addBiasSnippet", "push", "userCode", "inHeight", "inWidth", "Conv3DProgram", "padFront", "front", "<PERSON><PERSON><PERSON>h", "dilationDepth", "<PERSON><PERSON><PERSON><PERSON>", "inDepth"], "sources": ["C:\\tfjs-backend-webgl\\src\\conv_gpu.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2017 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {backend_util} from '@tensorflow/tfjs-core';\nimport {GPGPUProgram} from './gpgpu_math';\n\nexport class Conv2DProgram implements GPGPUProgram {\n  variableNames = ['x', 'W'];\n  outputShape: number[];\n  userCode: string;\n\n  constructor(\n      convInfo: backend_util.Conv2DInfo, addBias = false,\n      activation: string = null, hasPreluActivationWeights = false,\n      hasLeakyreluAlpha = false) {\n    this.outputShape = convInfo.outShape;\n    const padTop = convInfo.padInfo.top;\n    const padLeft = convInfo.padInfo.left;\n    const strideHeight = convInfo.strideHeight;\n    const strideWidth = convInfo.strideWidth;\n    const dilationHeight = convInfo.dilationHeight;\n    const dilationWidth = convInfo.dilationWidth;\n    const filterHeight = convInfo.filterHeight;\n    const filterWidth = convInfo.filterWidth;\n\n    const inputDepthNearestVec4 = Math.floor(convInfo.inChannels / 4) * 4;\n    const inputDepthVec4Remainder = convInfo.inChannels % 4;\n    const isChannelsLast = convInfo.dataFormat === 'channelsLast';\n\n    const rowDim = isChannelsLast ? 1 : 2;\n    const colDim = isChannelsLast ? 2 : 3;\n    const channelDim = isChannelsLast ? 3 : 1;\n\n    let activationSnippet = '', applyActivationSnippet = '';\n    if (activation) {\n      if (hasPreluActivationWeights) {\n        activationSnippet = `float activation(float a) {\n          float b = getPreluActivationWeightsAtOutCoords();\n          ${activation}\n        }`;\n      } else if (hasLeakyreluAlpha) {\n        activationSnippet = `float activation(float a) {\n          float b = getLeakyreluAlphaAtOutCoords();\n          ${activation}\n        }`;\n      } else {\n        activationSnippet = `\n          float activation(float x) {\n            ${activation}\n          }\n        `;\n      }\n\n      applyActivationSnippet = `result = activation(result);`;\n    }\n\n    const addBiasSnippet = addBias ? 'result += getBiasAtOutCoords();' : '';\n    if (addBias) {\n      this.variableNames.push('bias');\n    }\n\n    if (hasPreluActivationWeights) {\n      this.variableNames.push('preluActivationWeights');\n    }\n\n    if (hasLeakyreluAlpha) {\n      this.variableNames.push('leakyreluAlpha');\n    }\n\n    this.userCode = `\n      ${activationSnippet}\n\n      const ivec2 strides = ivec2(${strideHeight}, ${strideWidth});\n      const ivec2 pads = ivec2(${padTop}, ${padLeft});\n\n      void main() {\n        ivec4 coords = getOutputCoords();\n        int batch = coords[0];\n        int d2 = coords[${channelDim}];\n\n        ivec2 xRCCorner =\n            ivec2(coords[${rowDim}], coords[${colDim}]) * strides - pads;\n        int xRCorner = xRCCorner.x;\n        int xCCorner = xRCCorner.y;\n\n        // Convolve x(?, ?, d1) with w(:, :, d1, d2) to get y(yR, yC, d2).\n        // ? = to be determined. : = across all values in that axis.\n        float dotProd = 0.0;\n        for (int wR = 0; wR < ${filterHeight}; wR++) {\n          int xR = xRCorner + wR * ${dilationHeight};\n\n          if (xR < 0 || xR >= ${convInfo.inHeight}) {\n            continue;\n          }\n\n          for (int wC = 0; wC < ${filterWidth}; wC++) {\n            int xC = xCCorner + wC * ${dilationWidth};\n\n            if (xC < 0 || xC >= ${convInfo.inWidth}) {\n              continue;\n            }\n\n            for (int d1 = 0; d1 < ${inputDepthNearestVec4}; d1 += 4) {\n              vec4 wValues = vec4(\n                getW(wR, wC, d1, d2),\n                getW(wR, wC, d1 + 1, d2),\n                getW(wR, wC, d1 + 2, d2),\n                getW(wR, wC, d1 + 3, d2)\n              );\n\n              if (${isChannelsLast}) {\n                vec4 xValues = vec4(\n                  getX(batch, xR, xC, d1),\n                  getX(batch, xR, xC, d1 + 1),\n                  getX(batch, xR, xC, d1 + 2),\n                  getX(batch, xR, xC, d1 + 3)\n                );\n                dotProd += dot(xValues, wValues);\n              } else {\n                vec4 xValues = vec4(\n                  getX(batch, d1, xR, xC),\n                  getX(batch, d1 + 1, xR, xC),\n                  getX(batch, d1 + 2, xR, xC),\n                  getX(batch, d1 + 3, xR, xC)\n                );\n                dotProd += dot(xValues, wValues);\n              }\n            }\n\n            if (${inputDepthVec4Remainder === 1}) {\n\n              if (${isChannelsLast}) {\n                dotProd +=\n                    getX(batch, xR, xC, ${inputDepthNearestVec4}) *\n                    getW(wR, wC, ${inputDepthNearestVec4}, d2);\n              } else {\n                dotProd +=\n                    getX(batch, ${inputDepthNearestVec4}, xR, xC) *\n                    getW(wR, wC, ${inputDepthNearestVec4}, d2);\n              }\n\n            } else if (${inputDepthVec4Remainder === 2}) {\n              vec2 wValues = vec2(\n                getW(wR, wC, ${inputDepthNearestVec4}, d2),\n                getW(wR, wC, ${inputDepthNearestVec4} + 1, d2)\n              );\n\n              if (${isChannelsLast}) {\n                vec2 xValues = vec2(\n                  getX(batch, xR, xC, ${inputDepthNearestVec4}),\n                  getX(batch, xR, xC, ${inputDepthNearestVec4} + 1)\n                );\n                dotProd += dot(xValues, wValues);\n              } else {\n                vec2 xValues = vec2(\n                  getX(batch, ${inputDepthNearestVec4}, xR, xC),\n                  getX(batch, ${inputDepthNearestVec4} + 1, xR, xC)\n                );\n                dotProd += dot(xValues, wValues);\n              }\n\n            } else if (${inputDepthVec4Remainder === 3}) {\n              vec3 wValues = vec3(\n                getW(wR, wC, ${inputDepthNearestVec4}, d2),\n                getW(wR, wC, ${inputDepthNearestVec4} + 1, d2),\n                getW(wR, wC, ${inputDepthNearestVec4} + 2, d2)\n              );\n\n              if (${isChannelsLast}) {\n                vec3 xValues = vec3(\n                  getX(batch, xR, xC, ${inputDepthNearestVec4}),\n                  getX(batch, xR, xC, ${inputDepthNearestVec4} + 1),\n                  getX(batch, xR, xC, ${inputDepthNearestVec4} + 2)\n                );\n                dotProd += dot(xValues, wValues);\n              } else {\n                vec3 xValues = vec3(\n                  getX(batch, ${inputDepthNearestVec4}, xR, xC),\n                  getX(batch, ${inputDepthNearestVec4} + 1, xR, xC),\n                  getX(batch, ${inputDepthNearestVec4} + 2, xR, xC)\n                );\n                dotProd += dot(xValues, wValues);\n              }\n\n            }\n          }\n        }\n\n        float result = dotProd;\n        ${addBiasSnippet}\n        ${applyActivationSnippet}\n        setOutput(result);\n      }\n    `;\n  }\n}\n\nexport class Conv3DProgram implements GPGPUProgram {\n  variableNames = ['x', 'W'];\n  outputShape: number[];\n  userCode: string;\n\n  constructor(convInfo: backend_util.Conv3DInfo) {\n    this.outputShape = convInfo.outShape;\n    const padFront = convInfo.padInfo.front;\n    const padTop = convInfo.padInfo.top;\n    const padLeft = convInfo.padInfo.left;\n    const strideDepth = convInfo.strideDepth;\n    const strideHeight = convInfo.strideHeight;\n    const strideWidth = convInfo.strideWidth;\n    const dilationDepth = convInfo.dilationDepth;\n    const dilationHeight = convInfo.dilationHeight;\n    const dilationWidth = convInfo.dilationWidth;\n    const filterDepth = convInfo.filterDepth;\n    const filterHeight = convInfo.filterHeight;\n    const filterWidth = convInfo.filterWidth;\n\n    const inputDepthNearestVec4 = Math.floor(convInfo.inChannels / 4) * 4;\n    const inputDepthVec4Remainder = convInfo.inChannels % 4;\n\n    this.userCode = `\n      const ivec3 strides = ivec3(${strideDepth}, ${strideHeight}, ${\n        strideWidth});\n      const ivec3 pads = ivec3(${padFront}, ${padTop}, ${padLeft});\n\n      void main() {\n        ivec5 coords = getOutputCoords();\n        int batch = coords.x;\n        int d2 = coords.u;\n\n        ivec3 xFRCCorner = ivec3(coords.y, coords.z, coords.w) * strides - pads;\n        int xFCorner = xFRCCorner.x;\n        int xRCorner = xFRCCorner.y;\n        int xCCorner = xFRCCorner.z;\n\n        // Convolve x(?, ?, ?, d1) with w(:, :, :, d1, d2) to get\n        // y(yF, yR, yC, d2). ? = to be determined. : = across all\n        // values in that axis.\n        float dotProd = 0.0;\n        for (int wF = 0; wF < ${filterDepth}; wF++) {\n          int xF = xFCorner + wF * ${dilationDepth};\n\n          if (xF < 0 || xF >= ${convInfo.inDepth}) {\n            continue;\n          }\n\n          for (int wR = 0; wR < ${filterHeight}; wR++) {\n            int xR = xRCorner + wR * ${dilationHeight};\n\n            if (xR < 0 || xR >= ${convInfo.inHeight}) {\n              continue;\n            }\n\n            for (int wC = 0; wC < ${filterWidth}; wC++) {\n              int xC = xCCorner + wC * ${dilationWidth};\n\n              if (xC < 0 || xC >= ${convInfo.inWidth}) {\n                continue;\n              }\n\n              for (int d1 = 0; d1 < ${inputDepthNearestVec4}; d1 += 4) {\n                vec4 xValues = vec4(\n                  getX(batch, xF, xR, xC, d1),\n                  getX(batch, xF, xR, xC, d1 + 1),\n                  getX(batch, xF, xR, xC, d1 + 2),\n                  getX(batch, xF, xR, xC, d1 + 3)\n                );\n                vec4 wValues = vec4(\n                  getW(wF, wR, wC, d1, d2),\n                  getW(wF, wR, wC, d1 + 1, d2),\n                  getW(wF, wR, wC, d1 + 2, d2),\n                  getW(wF, wR, wC, d1 + 3, d2)\n                );\n\n                dotProd += dot(xValues, wValues);\n              }\n\n              if (${inputDepthVec4Remainder === 1}) {\n                dotProd +=\n                  getX(batch, xF, xR, xC, ${inputDepthNearestVec4}) *\n                  getW(wF, wR, wC, ${inputDepthNearestVec4}, d2);\n              } else if (${inputDepthVec4Remainder === 2}) {\n                vec2 xValues = vec2(\n                  getX(batch, xF, xR, xC, ${inputDepthNearestVec4}),\n                  getX(batch, xF, xR, xC, ${inputDepthNearestVec4} + 1)\n                );\n                vec2 wValues = vec2(\n                  getW(wF, wR, wC, ${inputDepthNearestVec4}, d2),\n                  getW(wF, wR, wC, ${inputDepthNearestVec4} + 1, d2)\n                );\n                dotProd += dot(xValues, wValues);\n              } else if (${inputDepthVec4Remainder === 3}) {\n                vec3 xValues = vec3(\n                  getX(batch, xF, xR, xC, ${inputDepthNearestVec4}),\n                  getX(batch, xF, xR, xC, ${inputDepthNearestVec4} + 1),\n                  getX(batch, xF, xR, xC, ${inputDepthNearestVec4} + 2)\n                );\n                vec3 wValues = vec3(\n                  getW(wF, wR, wC, ${inputDepthNearestVec4}, d2),\n                  getW(wF, wR, wC, ${inputDepthNearestVec4} + 1, d2),\n                  getW(wF, wR, wC, ${inputDepthNearestVec4} + 2, d2)\n                );\n                dotProd += dot(xValues, wValues);\n              }\n            }\n          }\n        }\n        setOutput(dotProd);\n      }\n    `;\n  }\n}\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAoBA,OAAM,MAAOA,aAAa;EAKxBC,YACIC,QAAiC,EAAEC,OAAO,GAAG,KAAK,EAClDC,UAAA,GAAqB,IAAI,EAAEC,yBAAyB,GAAG,KAAK,EAC5DC,iBAAiB,GAAG,KAAK;IAP7B,KAAAC,aAAa,GAAG,CAAC,GAAG,EAAE,GAAG,CAAC;IAQxB,IAAI,CAACC,WAAW,GAAGN,QAAQ,CAACO,QAAQ;IACpC,MAAMC,MAAM,GAAGR,QAAQ,CAACS,OAAO,CAACC,GAAG;IACnC,MAAMC,OAAO,GAAGX,QAAQ,CAACS,OAAO,CAACG,IAAI;IACrC,MAAMC,YAAY,GAAGb,QAAQ,CAACa,YAAY;IAC1C,MAAMC,WAAW,GAAGd,QAAQ,CAACc,WAAW;IACxC,MAAMC,cAAc,GAAGf,QAAQ,CAACe,cAAc;IAC9C,MAAMC,aAAa,GAAGhB,QAAQ,CAACgB,aAAa;IAC5C,MAAMC,YAAY,GAAGjB,QAAQ,CAACiB,YAAY;IAC1C,MAAMC,WAAW,GAAGlB,QAAQ,CAACkB,WAAW;IAExC,MAAMC,qBAAqB,GAAGC,IAAI,CAACC,KAAK,CAACrB,QAAQ,CAACsB,UAAU,GAAG,CAAC,CAAC,GAAG,CAAC;IACrE,MAAMC,uBAAuB,GAAGvB,QAAQ,CAACsB,UAAU,GAAG,CAAC;IACvD,MAAME,cAAc,GAAGxB,QAAQ,CAACyB,UAAU,KAAK,cAAc;IAE7D,MAAMC,MAAM,GAAGF,cAAc,GAAG,CAAC,GAAG,CAAC;IACrC,MAAMG,MAAM,GAAGH,cAAc,GAAG,CAAC,GAAG,CAAC;IACrC,MAAMI,UAAU,GAAGJ,cAAc,GAAG,CAAC,GAAG,CAAC;IAEzC,IAAIK,iBAAiB,GAAG,EAAE;MAAEC,sBAAsB,GAAG,EAAE;IACvD,IAAI5B,UAAU,EAAE;MACd,IAAIC,yBAAyB,EAAE;QAC7B0B,iBAAiB,GAAG;;YAEhB3B,UAAU;UACZ;OACH,MAAM,IAAIE,iBAAiB,EAAE;QAC5ByB,iBAAiB,GAAG;;YAEhB3B,UAAU;UACZ;OACH,MAAM;QACL2B,iBAAiB,GAAG;;cAEd3B,UAAU;;SAEf;;MAGH4B,sBAAsB,GAAG,8BAA8B;;IAGzD,MAAMC,cAAc,GAAG9B,OAAO,GAAG,iCAAiC,GAAG,EAAE;IACvE,IAAIA,OAAO,EAAE;MACX,IAAI,CAACI,aAAa,CAAC2B,IAAI,CAAC,MAAM,CAAC;;IAGjC,IAAI7B,yBAAyB,EAAE;MAC7B,IAAI,CAACE,aAAa,CAAC2B,IAAI,CAAC,wBAAwB,CAAC;;IAGnD,IAAI5B,iBAAiB,EAAE;MACrB,IAAI,CAACC,aAAa,CAAC2B,IAAI,CAAC,gBAAgB,CAAC;;IAG3C,IAAI,CAACC,QAAQ,GAAG;QACZJ,iBAAiB;;oCAEWhB,YAAY,KAAKC,WAAW;iCAC/BN,MAAM,KAAKG,OAAO;;;;;0BAKzBiB,UAAU;;;2BAGTF,MAAM,aAAaC,MAAM;;;;;;;gCAOpBV,YAAY;qCACPF,cAAc;;gCAEnBf,QAAQ,CAACkC,QAAQ;;;;kCAIfhB,WAAW;uCACNF,aAAa;;kCAElBhB,QAAQ,CAACmC,OAAO;;;;oCAIdhB,qBAAqB;;;;;;;;oBAQrCK,cAAc;;;;;;;;;;;;;;;;;;;kBAmBhBD,uBAAuB,KAAK,CAAC;;oBAE3BC,cAAc;;0CAEQL,qBAAqB;mCAC5BA,qBAAqB;;;kCAGtBA,qBAAqB;mCACpBA,qBAAqB;;;yBAG/BI,uBAAuB,KAAK,CAAC;;+BAEvBJ,qBAAqB;+BACrBA,qBAAqB;;;oBAGhCK,cAAc;;wCAEML,qBAAqB;wCACrBA,qBAAqB;;;;;gCAK7BA,qBAAqB;gCACrBA,qBAAqB;;;;;yBAK5BI,uBAAuB,KAAK,CAAC;;+BAEvBJ,qBAAqB;+BACrBA,qBAAqB;+BACrBA,qBAAqB;;;oBAGhCK,cAAc;;wCAEML,qBAAqB;wCACrBA,qBAAqB;wCACrBA,qBAAqB;;;;;gCAK7BA,qBAAqB;gCACrBA,qBAAqB;gCACrBA,qBAAqB;;;;;;;;;;UAU3CY,cAAc;UACdD,sBAAsB;;;KAG3B;EACH;;AAGF,OAAM,MAAOM,aAAa;EAKxBrC,YAAYC,QAAiC;IAJ7C,KAAAK,aAAa,GAAG,CAAC,GAAG,EAAE,GAAG,CAAC;IAKxB,IAAI,CAACC,WAAW,GAAGN,QAAQ,CAACO,QAAQ;IACpC,MAAM8B,QAAQ,GAAGrC,QAAQ,CAACS,OAAO,CAAC6B,KAAK;IACvC,MAAM9B,MAAM,GAAGR,QAAQ,CAACS,OAAO,CAACC,GAAG;IACnC,MAAMC,OAAO,GAAGX,QAAQ,CAACS,OAAO,CAACG,IAAI;IACrC,MAAM2B,WAAW,GAAGvC,QAAQ,CAACuC,WAAW;IACxC,MAAM1B,YAAY,GAAGb,QAAQ,CAACa,YAAY;IAC1C,MAAMC,WAAW,GAAGd,QAAQ,CAACc,WAAW;IACxC,MAAM0B,aAAa,GAAGxC,QAAQ,CAACwC,aAAa;IAC5C,MAAMzB,cAAc,GAAGf,QAAQ,CAACe,cAAc;IAC9C,MAAMC,aAAa,GAAGhB,QAAQ,CAACgB,aAAa;IAC5C,MAAMyB,WAAW,GAAGzC,QAAQ,CAACyC,WAAW;IACxC,MAAMxB,YAAY,GAAGjB,QAAQ,CAACiB,YAAY;IAC1C,MAAMC,WAAW,GAAGlB,QAAQ,CAACkB,WAAW;IAExC,MAAMC,qBAAqB,GAAGC,IAAI,CAACC,KAAK,CAACrB,QAAQ,CAACsB,UAAU,GAAG,CAAC,CAAC,GAAG,CAAC;IACrE,MAAMC,uBAAuB,GAAGvB,QAAQ,CAACsB,UAAU,GAAG,CAAC;IAEvD,IAAI,CAACW,QAAQ,GAAG;oCACgBM,WAAW,KAAK1B,YAAY,KACxDC,WAAW;iCACcuB,QAAQ,KAAK7B,MAAM,KAAKG,OAAO;;;;;;;;;;;;;;;;gCAgBhC8B,WAAW;qCACND,aAAa;;gCAElBxC,QAAQ,CAAC0C,OAAO;;;;kCAIdzB,YAAY;uCACPF,cAAc;;kCAEnBf,QAAQ,CAACkC,QAAQ;;;;oCAIfhB,WAAW;yCACNF,aAAa;;oCAElBhB,QAAQ,CAACmC,OAAO;;;;sCAIdhB,qBAAqB;;;;;;;;;;;;;;;;;oBAiBvCI,uBAAuB,KAAK,CAAC;;4CAELJ,qBAAqB;qCAC5BA,qBAAqB;2BAC/BI,uBAAuB,KAAK,CAAC;;4CAEZJ,qBAAqB;4CACrBA,qBAAqB;;;qCAG5BA,qBAAqB;qCACrBA,qBAAqB;;;2BAG/BI,uBAAuB,KAAK,CAAC;;4CAEZJ,qBAAqB;4CACrBA,qBAAqB;4CACrBA,qBAAqB;;;qCAG5BA,qBAAqB;qCACrBA,qBAAqB;qCACrBA,qBAAqB;;;;;;;;;KASrD;EACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}