{"ast": null, "code": "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport './abs';\nimport './acos';\nimport './acosh';\nimport './add';\nimport './all';\nimport './any';\nimport './arg_max';\nimport './arg_min';\nimport './as_scalar';\nimport './as_type';\nimport './as1d';\nimport './as2d';\nimport './as3d';\nimport './as4d';\nimport './as5d';\nimport './asin';\nimport './asinh';\nimport './atan';\nimport './atan2';\nimport './atanh';\nimport './avg_pool';\nimport './batch_to_space_nd';\nimport './batchnorm';\nimport './broadcast_to';\nimport './cast';\nimport './ceil';\nimport './clip_by_value';\nimport './concat';\nimport './conv1d';\nimport './conv2d_transpose';\nimport './conv2d';\nimport './cos';\nimport './cosh';\nimport './cumprod';\nimport './cumsum';\nimport './depth_to_space';\nimport './depthwise_conv2d';\nimport './dilation2d';\nimport './div_no_nan';\nimport './div';\nimport './dot';\nimport './elu';\nimport './equal';\nimport './erf';\nimport './euclidean_norm';\nimport './exp';\nimport './expand_dims';\nimport './expm1';\nimport './fft';\nimport './flatten';\nimport './floor';\nimport './floorDiv';\nimport './gather';\nimport './greater_equal';\nimport './greater';\nimport './ifft';\nimport './irfft';\nimport './is_finite';\nimport './is_inf';\nimport './is_nan';\nimport './leaky_relu';\nimport './less_equal';\nimport './less';\nimport './local_response_normalization';\nimport './log_sigmoid';\nimport './log_softmax';\nimport './log_sum_exp';\nimport './log';\nimport './log1p';\nimport './logical_and';\nimport './logical_not';\nimport './logical_or';\nimport './logical_xor';\nimport './mat_mul';\nimport './max_pool';\nimport './max';\nimport './maximum';\nimport './mean';\nimport './min';\nimport './minimum';\nimport './mirror_pad';\nimport './mod';\nimport './mul';\nimport './neg';\nimport './norm';\nimport './not_equal';\nimport './one_hot';\nimport './ones_like';\nimport './pad';\nimport './pool';\nimport './pow';\nimport './prelu';\nimport './prod';\nimport './reciprocal';\nimport './relu';\nimport './relu6';\nimport './reshape_as';\nimport './reshape';\nimport './resize_bilinear';\nimport './resize_nearest_neighbor';\nimport './reverse';\nimport './rfft';\nimport './round';\nimport './rsqrt';\nimport './selu';\nimport './separable_conv2d';\nimport './sigmoid';\nimport './sign';\nimport './sin';\nimport './sinh';\nimport './slice';\nimport './softmax';\nimport './softplus';\nimport './space_to_batch_nd';\nimport './split';\nimport './sqrt';\nimport './square';\nimport './squared_difference';\nimport './squeeze';\nimport './stack';\nimport './step';\nimport './strided_slice';\nimport './sub';\nimport './sum';\nimport './tan';\nimport './tanh';\nimport './tile';\nimport './to_bool';\nimport './to_float';\nimport './to_int';\nimport './topk';\nimport './transpose';\nimport './unique';\nimport './unsorted_segment_sum';\nimport './unstack';\nimport './where';\nimport './zeros_like';", "map": {"version": 3, "names": [], "sources": ["C:\\tfjs-core\\src\\public\\chained_ops\\register_all_chained_ops.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport './abs';\nimport './acos';\nimport './acosh';\nimport './add';\nimport './all';\nimport './any';\nimport './arg_max';\nimport './arg_min';\nimport './as_scalar';\nimport './as_type';\nimport './as1d';\nimport './as2d';\nimport './as3d';\nimport './as4d';\nimport './as5d';\nimport './asin';\nimport './asinh';\nimport './atan';\nimport './atan2';\nimport './atanh';\nimport './avg_pool';\nimport './batch_to_space_nd';\nimport './batchnorm';\nimport './broadcast_to';\nimport './cast';\nimport './ceil';\nimport './clip_by_value';\nimport './concat';\nimport './conv1d';\nimport './conv2d_transpose';\nimport './conv2d';\nimport './cos';\nimport './cosh';\nimport './cumprod';\nimport './cumsum';\nimport './depth_to_space';\nimport './depthwise_conv2d';\nimport './dilation2d';\nimport './div_no_nan';\nimport './div';\nimport './dot';\nimport './elu';\nimport './equal';\nimport './erf';\nimport './euclidean_norm';\nimport './exp';\nimport './expand_dims';\nimport './expm1';\nimport './fft';\nimport './flatten';\nimport './floor';\nimport './floorDiv';\nimport './gather';\nimport './greater_equal';\nimport './greater';\nimport './ifft';\nimport './irfft';\nimport './is_finite';\nimport './is_inf';\nimport './is_nan';\nimport './leaky_relu';\nimport './less_equal';\nimport './less';\nimport './local_response_normalization';\nimport './log_sigmoid';\nimport './log_softmax';\nimport './log_sum_exp';\nimport './log';\nimport './log1p';\nimport './logical_and';\nimport './logical_not';\nimport './logical_or';\nimport './logical_xor';\nimport './mat_mul';\nimport './max_pool';\nimport './max';\nimport './maximum';\nimport './mean';\nimport './min';\nimport './minimum';\nimport './mirror_pad';\nimport './mod';\nimport './mul';\nimport './neg';\nimport './norm';\nimport './not_equal';\nimport './one_hot';\nimport './ones_like';\nimport './pad';\nimport './pool';\nimport './pow';\nimport './prelu';\nimport './prod';\nimport './reciprocal';\nimport './relu';\nimport './relu6';\nimport './reshape_as';\nimport './reshape';\nimport './resize_bilinear';\nimport './resize_nearest_neighbor';\nimport './reverse';\nimport './rfft';\nimport './round';\nimport './rsqrt';\nimport './selu';\nimport './separable_conv2d';\nimport './sigmoid';\nimport './sign';\nimport './sin';\nimport './sinh';\nimport './slice';\nimport './softmax';\nimport './softplus';\nimport './space_to_batch_nd';\nimport './split';\nimport './sqrt';\nimport './square';\nimport './squared_difference';\nimport './squeeze';\nimport './stack';\nimport './step';\nimport './strided_slice';\nimport './sub';\nimport './sum';\nimport './tan';\nimport './tanh';\nimport './tile';\nimport './to_bool';\nimport './to_float';\nimport './to_int';\nimport './topk';\nimport './transpose';\nimport './unique';\nimport './unsorted_segment_sum';\nimport './unstack';\nimport './where';\nimport './zeros_like';\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAiBA,OAAO,OAAO;AACd,OAAO,QAAQ;AACf,OAAO,SAAS;AAChB,OAAO,OAAO;AACd,OAAO,OAAO;AACd,OAAO,OAAO;AACd,OAAO,WAAW;AAClB,OAAO,WAAW;AAClB,OAAO,aAAa;AACpB,OAAO,WAAW;AAClB,OAAO,QAAQ;AACf,OAAO,QAAQ;AACf,OAAO,QAAQ;AACf,OAAO,QAAQ;AACf,OAAO,QAAQ;AACf,OAAO,QAAQ;AACf,OAAO,SAAS;AAChB,OAAO,QAAQ;AACf,OAAO,SAAS;AAChB,OAAO,SAAS;AAChB,OAAO,YAAY;AACnB,OAAO,qBAAqB;AAC5B,OAAO,aAAa;AACpB,OAAO,gBAAgB;AACvB,OAAO,QAAQ;AACf,OAAO,QAAQ;AACf,OAAO,iBAAiB;AACxB,OAAO,UAAU;AACjB,OAAO,UAAU;AACjB,OAAO,oBAAoB;AAC3B,OAAO,UAAU;AACjB,OAAO,OAAO;AACd,OAAO,QAAQ;AACf,OAAO,WAAW;AAClB,OAAO,UAAU;AACjB,OAAO,kBAAkB;AACzB,OAAO,oBAAoB;AAC3B,OAAO,cAAc;AACrB,OAAO,cAAc;AACrB,OAAO,OAAO;AACd,OAAO,OAAO;AACd,OAAO,OAAO;AACd,OAAO,SAAS;AAChB,OAAO,OAAO;AACd,OAAO,kBAAkB;AACzB,OAAO,OAAO;AACd,OAAO,eAAe;AACtB,OAAO,SAAS;AAChB,OAAO,OAAO;AACd,OAAO,WAAW;AAClB,OAAO,SAAS;AAChB,OAAO,YAAY;AACnB,OAAO,UAAU;AACjB,OAAO,iBAAiB;AACxB,OAAO,WAAW;AAClB,OAAO,QAAQ;AACf,OAAO,SAAS;AAChB,OAAO,aAAa;AACpB,OAAO,UAAU;AACjB,OAAO,UAAU;AACjB,OAAO,cAAc;AACrB,OAAO,cAAc;AACrB,OAAO,QAAQ;AACf,OAAO,gCAAgC;AACvC,OAAO,eAAe;AACtB,OAAO,eAAe;AACtB,OAAO,eAAe;AACtB,OAAO,OAAO;AACd,OAAO,SAAS;AAChB,OAAO,eAAe;AACtB,OAAO,eAAe;AACtB,OAAO,cAAc;AACrB,OAAO,eAAe;AACtB,OAAO,WAAW;AAClB,OAAO,YAAY;AACnB,OAAO,OAAO;AACd,OAAO,WAAW;AAClB,OAAO,QAAQ;AACf,OAAO,OAAO;AACd,OAAO,WAAW;AAClB,OAAO,cAAc;AACrB,OAAO,OAAO;AACd,OAAO,OAAO;AACd,OAAO,OAAO;AACd,OAAO,QAAQ;AACf,OAAO,aAAa;AACpB,OAAO,WAAW;AAClB,OAAO,aAAa;AACpB,OAAO,OAAO;AACd,OAAO,QAAQ;AACf,OAAO,OAAO;AACd,OAAO,SAAS;AAChB,OAAO,QAAQ;AACf,OAAO,cAAc;AACrB,OAAO,QAAQ;AACf,OAAO,SAAS;AAChB,OAAO,cAAc;AACrB,OAAO,WAAW;AAClB,OAAO,mBAAmB;AAC1B,OAAO,2BAA2B;AAClC,OAAO,WAAW;AAClB,OAAO,QAAQ;AACf,OAAO,SAAS;AAChB,OAAO,SAAS;AAChB,OAAO,QAAQ;AACf,OAAO,oBAAoB;AAC3B,OAAO,WAAW;AAClB,OAAO,QAAQ;AACf,OAAO,OAAO;AACd,OAAO,QAAQ;AACf,OAAO,SAAS;AAChB,OAAO,WAAW;AAClB,OAAO,YAAY;AACnB,OAAO,qBAAqB;AAC5B,OAAO,SAAS;AAChB,OAAO,QAAQ;AACf,OAAO,UAAU;AACjB,OAAO,sBAAsB;AAC7B,OAAO,WAAW;AAClB,OAAO,SAAS;AAChB,OAAO,QAAQ;AACf,OAAO,iBAAiB;AACxB,OAAO,OAAO;AACd,OAAO,OAAO;AACd,OAAO,OAAO;AACd,OAAO,QAAQ;AACf,OAAO,QAAQ;AACf,OAAO,WAAW;AAClB,OAAO,YAAY;AACnB,OAAO,UAAU;AACjB,OAAO,QAAQ;AACf,OAAO,aAAa;AACpB,OAAO,UAAU;AACjB,OAAO,wBAAwB;AAC/B,OAAO,WAAW;AAClB,OAAO,SAAS;AAChB,OAAO,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}