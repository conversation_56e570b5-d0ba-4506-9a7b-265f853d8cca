{"ast": null, "code": "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { ENGINE } from '../engine';\nimport { DepthwiseConv2dNativeBackpropInput } from '../kernel_names';\nimport { op } from './operation';\nimport { reshape } from './reshape';\nfunction depthwiseConv2dNativeBackpropInput_(xShape, dy, filter, strides, pad) {\n  let dilations = arguments.length > 5 && arguments[5] !== undefined ? arguments[5] : [1, 1];\n  let dimRoundingMode = arguments.length > 6 ? arguments[6] : undefined;\n  let dy4D = dy;\n  let reshapedTo4D = false;\n  if (dy.rank === 3) {\n    reshapedTo4D = true;\n    dy4D = reshape(dy, [1, dy.shape[0], dy.shape[1], dy.shape[2]]);\n  }\n  const inputs = {\n    dy: dy4D,\n    filter\n  };\n  const attrs = {\n    strides,\n    pad,\n    dimRoundingMode,\n    dilations,\n    inputShape: xShape\n  };\n  const res =\n  // tslint:disable-next-line: no-unnecessary-type-assertion\n  ENGINE.runKernel(DepthwiseConv2dNativeBackpropInput, inputs, attrs);\n  if (reshapedTo4D) {\n    return reshape(res, [res.shape[1], res.shape[2], res.shape[3]]);\n  }\n  return res;\n}\nexport const depthwiseConv2dNativeBackpropInput = op({\n  depthwiseConv2dNativeBackpropInput_\n});", "map": {"version": 3, "names": ["ENGINE", "DepthwiseConv2dNativeBackpropInput", "op", "reshape", "depthwiseConv2dNativeBackpropInput_", "xShape", "dy", "filter", "strides", "pad", "dilations", "arguments", "length", "undefined", "dimRoundingMode", "dy4D", "reshapedTo4D", "rank", "shape", "inputs", "attrs", "inputShape", "res", "runKernel", "depthwiseConv2dNativeBackpropInput"], "sources": ["C:\\tfjs-core\\src\\ops\\depthwise_conv2d_native_backprop_input.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport {ENGINE} from '../engine';\nimport {DepthwiseConv2dNativeBackpropInput, DepthwiseConv2dNativeBackpropInputAttrs, DepthwiseConv2dNativeBackpropInputInputs} from '../kernel_names';\nimport {NamedAttrMap} from '../kernel_registry';\nimport {Tensor3D, Tensor4D} from '../tensor';\nimport {NamedTensorMap} from '../tensor_types';\n\nimport {ExplicitPadding} from './conv_util';\nimport {op} from './operation';\nimport {reshape} from './reshape';\n\nfunction depthwiseConv2dNativeBackpropInput_<T extends Tensor3D|Tensor4D>(\n    xShape: [number, number, number, number], dy: T, filter: Tensor4D,\n    strides: [number, number]|number,\n    pad: 'valid'|'same'|number|ExplicitPadding,\n    dilations: [number, number]|number = [1, 1],\n    dimRoundingMode?: 'floor'|'round'|'ceil'): T {\n  let dy4D = dy as Tensor4D;\n  let reshapedTo4D = false;\n  if (dy.rank === 3) {\n    reshapedTo4D = true;\n    dy4D = reshape(dy, [1, dy.shape[0], dy.shape[1], dy.shape[2]]);\n  }\n\n  const inputs: DepthwiseConv2dNativeBackpropInputInputs = {dy: dy4D, filter};\n  const attrs: DepthwiseConv2dNativeBackpropInputAttrs =\n      {strides, pad, dimRoundingMode, dilations, inputShape: xShape};\n\n  const res =\n      // tslint:disable-next-line: no-unnecessary-type-assertion\n      ENGINE.runKernel(\n          DepthwiseConv2dNativeBackpropInput,\n          inputs as unknown as NamedTensorMap,\n          attrs as unknown as NamedAttrMap) as T;\n\n  if (reshapedTo4D) {\n    return reshape(res, [res.shape[1], res.shape[2], res.shape[3]]) as T;\n  }\n  return res;\n}\n\nexport const depthwiseConv2dNativeBackpropInput =\n    op({depthwiseConv2dNativeBackpropInput_});\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAgBA,SAAQA,MAAM,QAAO,WAAW;AAChC,SAAQC,kCAAkC,QAA0F,iBAAiB;AAMrJ,SAAQC,EAAE,QAAO,aAAa;AAC9B,SAAQC,OAAO,QAAO,WAAW;AAEjC,SAASC,mCAAmCA,CACxCC,MAAwC,EAAEC,EAAK,EAAEC,MAAgB,EACjEC,OAAgC,EAChCC,GAA0C,EAEF;EAAA,IADxCC,SAAA,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAqC,CAAC,CAAC,EAAE,CAAC,CAAC;EAAA,IAC3CG,eAAwC,GAAAH,SAAA,CAAAC,MAAA,OAAAD,SAAA,MAAAE,SAAA;EAC1C,IAAIE,IAAI,GAAGT,EAAc;EACzB,IAAIU,YAAY,GAAG,KAAK;EACxB,IAAIV,EAAE,CAACW,IAAI,KAAK,CAAC,EAAE;IACjBD,YAAY,GAAG,IAAI;IACnBD,IAAI,GAAGZ,OAAO,CAACG,EAAE,EAAE,CAAC,CAAC,EAAEA,EAAE,CAACY,KAAK,CAAC,CAAC,CAAC,EAAEZ,EAAE,CAACY,KAAK,CAAC,CAAC,CAAC,EAAEZ,EAAE,CAACY,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;;EAGhE,MAAMC,MAAM,GAA6C;IAACb,EAAE,EAAES,IAAI;IAAER;EAAM,CAAC;EAC3E,MAAMa,KAAK,GACP;IAACZ,OAAO;IAAEC,GAAG;IAAEK,eAAe;IAAEJ,SAAS;IAAEW,UAAU,EAAEhB;EAAM,CAAC;EAElE,MAAMiB,GAAG;EACL;EACAtB,MAAM,CAACuB,SAAS,CACZtB,kCAAkC,EAClCkB,MAAmC,EACnCC,KAAgC,CAAM;EAE9C,IAAIJ,YAAY,EAAE;IAChB,OAAOb,OAAO,CAACmB,GAAG,EAAE,CAACA,GAAG,CAACJ,KAAK,CAAC,CAAC,CAAC,EAAEI,GAAG,CAACJ,KAAK,CAAC,CAAC,CAAC,EAAEI,GAAG,CAACJ,KAAK,CAAC,CAAC,CAAC,CAAC,CAAM;;EAEtE,OAAOI,GAAG;AACZ;AAEA,OAAO,MAAME,kCAAkC,GAC3CtB,EAAE,CAAC;EAACE;AAAmC,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}