{"ast": null, "code": "import group from './_group.js';\n\n// Split a collection into two arrays: one whose elements all pass the given\n// truth test, and one whose elements all do not pass the truth test.\nexport default group(function (result, value, pass) {\n  result[pass ? 0 : 1].push(value);\n}, true);", "map": {"version": 3, "names": ["group", "result", "value", "pass", "push"], "sources": ["C:/tmsft/node_modules/underscore/modules/partition.js"], "sourcesContent": ["import group from './_group.js';\n\n// Split a collection into two arrays: one whose elements all pass the given\n// truth test, and one whose elements all do not pass the truth test.\nexport default group(function(result, value, pass) {\n  result[pass ? 0 : 1].push(value);\n}, true);\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,aAAa;;AAE/B;AACA;AACA,eAAeA,KAAK,CAAC,UAASC,MAAM,EAAEC,KAAK,EAAEC,IAAI,EAAE;EACjDF,MAAM,CAACE,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC,CAACC,IAAI,CAACF,KAAK,CAAC;AAClC,CAAC,EAAE,IAAI,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}