{"ast": null, "code": "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n// Shared functionality among backends.\nexport { simpleAbsImpl } from './kernels/Abs';\nexport { addImpl } from './kernels/Add';\nexport { bincountImpl, bincountReduceImpl } from './kernels/Bincount_impl';\nexport { bitwiseAndImpl } from './kernels/BitwiseAnd';\nexport { castImpl } from './kernels/Cast';\nexport { ceilImpl } from './kernels/Ceil';\nexport { concatImpl } from './kernels/Concat_impl';\nexport { equalImpl } from './kernels/Equal';\nexport { expImpl } from './kernels/Exp';\nexport { expm1Impl } from './kernels/Expm1';\nexport { floorImpl } from './kernels/Floor';\nexport { floorDivImpl } from './kernels/FloorDiv';\nexport { gatherNdImpl } from './kernels/GatherNd_Impl';\nexport { gatherV2Impl } from './kernels/GatherV2_impl';\nexport { greaterImpl } from './kernels/Greater';\nexport { greaterEqualImpl } from './kernels/GreaterEqual';\nexport { lessImpl } from './kernels/Less';\nexport { lessEqualImpl } from './kernels/LessEqual';\nexport { linSpaceImpl } from './kernels/LinSpace_impl';\nexport { logImpl } from './kernels/Log';\nexport { maxImpl } from './kernels/Max_impl';\nexport { maximumImpl } from './kernels/Maximum';\nexport { minimumImpl } from './kernels/Minimum';\nexport { multiplyImpl } from './kernels/Multiply';\nexport { negImpl } from './kernels/Neg';\nexport { notEqualImpl } from './kernels/NotEqual';\nexport { prodImpl } from './kernels/Prod';\nexport { raggedGatherImpl } from './kernels/RaggedGather_impl';\nexport { raggedRangeImpl } from './kernels/RaggedRange_impl';\nexport { raggedTensorToTensorImpl } from './kernels/RaggedTensorToTensor_impl';\nexport { rangeImpl } from './kernels/Range_impl';\nexport { rsqrtImpl } from './kernels/Rsqrt';\nexport { scatterImpl } from './kernels/Scatter_impl';\nexport { sigmoidImpl } from './kernels/Sigmoid';\nexport { sliceImpl } from './kernels/Slice';\nexport { sparseFillEmptyRowsImpl } from './kernels/SparseFillEmptyRows_impl';\nexport { sparseReshapeImpl } from './kernels/SparseReshape_impl';\nexport { sparseSegmentReductionImpl } from './kernels/SparseSegmentReduction_impl';\nexport { sqrtImpl } from './kernels/Sqrt';\nexport { squaredDifferenceImpl } from './kernels/SquaredDifference';\nexport { staticRegexReplaceImpl } from './kernels/StaticRegexReplace';\nexport { stridedSliceImpl } from './kernels/StridedSlice_impl';\nexport { stringNGramsImpl } from './kernels/StringNGrams_impl';\nexport { stringSplitImpl } from './kernels/StringSplit_impl';\nexport { stringToHashBucketFastImpl } from './kernels/StringToHashBucketFast_impl';\nexport { subImpl } from './kernels/Sub';\nexport { tileImpl } from './kernels/Tile_impl';\nexport { topKImpl } from './kernels/TopK_impl';\nexport { transposeImpl } from './kernels/Transpose_impl';\nexport { uniqueImpl } from './kernels/Unique_impl';", "map": {"version": 3, "names": ["simpleAbsImpl", "addImpl", "bincountImpl", "bincountReduceImpl", "bitwiseAndImpl", "castImpl", "ceilImpl", "concatImpl", "equalImpl", "expImpl", "expm1Impl", "floorImpl", "floorDivImpl", "gatherNdImpl", "gatherV2Impl", "greaterImpl", "greaterEqualImpl", "lessImpl", "lessEqualImpl", "linSpaceImpl", "logImpl", "maxImpl", "maximumImpl", "minimumImpl", "multiplyImpl", "negImpl", "notEqualImpl", "prodImpl", "raggedGatherImpl", "raggedRangeImpl", "raggedTensorToTensorImpl", "rangeImpl", "rsqrtImpl", "scatterImpl", "sigmoidImpl", "sliceImpl", "sparseFillEmptyRowsImpl", "sparseReshapeImpl", "sparseSegmentReductionImpl", "sqrtImpl", "squaredDifferenceImpl", "staticRegexReplaceImpl", "stridedSliceImpl", "stringNGramsImpl", "stringSplitImpl", "stringToHashBucketFastImpl", "subImpl", "tileImpl", "topKImpl", "transposeImpl", "uniqueImpl"], "sources": ["C:\\tfjs-backend-cpu\\src\\shared.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\n// Shared functionality among backends.\nexport {simpleAbsImpl} from './kernels/Abs';\nexport {addImpl} from './kernels/Add';\nexport {bincountImpl, bincountReduceImpl} from './kernels/Bincount_impl';\nexport {bitwiseAndImpl} from './kernels/BitwiseAnd';\nexport {castImpl} from './kernels/Cast';\nexport {ceilImpl} from './kernels/Ceil';\nexport {concatImpl} from './kernels/Concat_impl';\nexport {equalImpl} from './kernels/Equal';\nexport {expImpl} from './kernels/Exp';\nexport {expm1Impl} from './kernels/Expm1';\nexport {floorImpl} from './kernels/Floor';\nexport {floorDivImpl} from './kernels/FloorDiv';\nexport {gatherNdImpl} from './kernels/GatherNd_Impl';\nexport {gatherV2Impl} from './kernels/GatherV2_impl';\nexport {greaterImpl} from './kernels/Greater';\nexport {greaterEqualImpl} from './kernels/GreaterEqual';\nexport {lessImpl} from './kernels/Less';\nexport {lessEqualImpl} from './kernels/LessEqual';\nexport {linSpaceImpl} from './kernels/LinSpace_impl';\nexport {logImpl} from './kernels/Log';\nexport {maxImpl} from './kernels/Max_impl';\nexport {maximumImpl} from './kernels/Maximum';\nexport {minimumImpl} from './kernels/Minimum';\nexport {multiplyImpl} from './kernels/Multiply';\nexport {negImpl} from './kernels/Neg';\nexport {notEqualImpl} from './kernels/NotEqual';\nexport {prodImpl} from './kernels/Prod';\nexport {raggedGatherImpl} from './kernels/RaggedGather_impl';\nexport {raggedRangeImpl} from './kernels/RaggedRange_impl';\nexport {raggedTensorToTensorImpl} from './kernels/RaggedTensorToTensor_impl';\nexport {rangeImpl} from './kernels/Range_impl';\nexport {rsqrtImpl} from './kernels/Rsqrt';\nexport {scatterImpl} from './kernels/Scatter_impl';\nexport {sigmoidImpl} from './kernels/Sigmoid';\nexport {sliceImpl} from './kernels/Slice';\nexport {sparseFillEmptyRowsImpl} from './kernels/SparseFillEmptyRows_impl';\nexport {sparseReshapeImpl} from './kernels/SparseReshape_impl';\nexport {sparseSegmentReductionImpl} from './kernels/SparseSegmentReduction_impl';\nexport {sqrtImpl} from './kernels/Sqrt';\nexport {squaredDifferenceImpl} from './kernels/SquaredDifference';\nexport {staticRegexReplaceImpl} from './kernels/StaticRegexReplace';\nexport {stridedSliceImpl} from './kernels/StridedSlice_impl';\nexport {stringNGramsImpl} from './kernels/StringNGrams_impl';\nexport {stringSplitImpl} from './kernels/StringSplit_impl';\nexport {stringToHashBucketFastImpl} from './kernels/StringToHashBucketFast_impl';\nexport {subImpl} from './kernels/Sub';\nexport {tileImpl} from './kernels/Tile_impl';\nexport {topKImpl} from './kernels/TopK_impl';\nexport {transposeImpl} from './kernels/Transpose_impl';\nexport {uniqueImpl} from './kernels/Unique_impl';\nexport {ComplexBinaryKernelImpl, SimpleBinaryKernelImpl} from './utils/binary_types';\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAiBA;AACA,SAAQA,aAAa,QAAO,eAAe;AAC3C,SAAQC,OAAO,QAAO,eAAe;AACrC,SAAQC,YAAY,EAAEC,kBAAkB,QAAO,yBAAyB;AACxE,SAAQC,cAAc,QAAO,sBAAsB;AACnD,SAAQC,QAAQ,QAAO,gBAAgB;AACvC,SAAQC,QAAQ,QAAO,gBAAgB;AACvC,SAAQC,UAAU,QAAO,uBAAuB;AAChD,SAAQC,SAAS,QAAO,iBAAiB;AACzC,SAAQC,OAAO,QAAO,eAAe;AACrC,SAAQC,SAAS,QAAO,iBAAiB;AACzC,SAAQC,SAAS,QAAO,iBAAiB;AACzC,SAAQC,YAAY,QAAO,oBAAoB;AAC/C,SAAQC,YAAY,QAAO,yBAAyB;AACpD,SAAQC,YAAY,QAAO,yBAAyB;AACpD,SAAQC,WAAW,QAAO,mBAAmB;AAC7C,SAAQC,gBAAgB,QAAO,wBAAwB;AACvD,SAAQC,QAAQ,QAAO,gBAAgB;AACvC,SAAQC,aAAa,QAAO,qBAAqB;AACjD,SAAQC,YAAY,QAAO,yBAAyB;AACpD,SAAQC,OAAO,QAAO,eAAe;AACrC,SAAQC,OAAO,QAAO,oBAAoB;AAC1C,SAAQC,WAAW,QAAO,mBAAmB;AAC7C,SAAQC,WAAW,QAAO,mBAAmB;AAC7C,SAAQC,YAAY,QAAO,oBAAoB;AAC/C,SAAQC,OAAO,QAAO,eAAe;AACrC,SAAQC,YAAY,QAAO,oBAAoB;AAC/C,SAAQC,QAAQ,QAAO,gBAAgB;AACvC,SAAQC,gBAAgB,QAAO,6BAA6B;AAC5D,SAAQC,eAAe,QAAO,4BAA4B;AAC1D,SAAQC,wBAAwB,QAAO,qCAAqC;AAC5E,SAAQC,SAAS,QAAO,sBAAsB;AAC9C,SAAQC,SAAS,QAAO,iBAAiB;AACzC,SAAQC,WAAW,QAAO,wBAAwB;AAClD,SAAQC,WAAW,QAAO,mBAAmB;AAC7C,SAAQC,SAAS,QAAO,iBAAiB;AACzC,SAAQC,uBAAuB,QAAO,oCAAoC;AAC1E,SAAQC,iBAAiB,QAAO,8BAA8B;AAC9D,SAAQC,0BAA0B,QAAO,uCAAuC;AAChF,SAAQC,QAAQ,QAAO,gBAAgB;AACvC,SAAQC,qBAAqB,QAAO,6BAA6B;AACjE,SAAQC,sBAAsB,QAAO,8BAA8B;AACnE,SAAQC,gBAAgB,QAAO,6BAA6B;AAC5D,SAAQC,gBAAgB,QAAO,6BAA6B;AAC5D,SAAQC,eAAe,QAAO,4BAA4B;AAC1D,SAAQC,0BAA0B,QAAO,uCAAuC;AAChF,SAAQC,OAAO,QAAO,eAAe;AACrC,SAAQC,QAAQ,QAAO,qBAAqB;AAC5C,SAAQC,QAAQ,QAAO,qBAAqB;AAC5C,SAAQC,aAAa,QAAO,0BAA0B;AACtD,SAAQC,UAAU,QAAO,uBAAuB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}