{"ast": null, "code": "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { Pack } from '../kernel_names';\nimport { unstack } from '../ops/unstack';\nexport const packGradConfig = {\n  kernelName: Pack,\n  saveAllInputs: true,\n  gradFunc: (dy, saved, attrs) => {\n    const {\n      axis\n    } = attrs;\n    const derTensors = unstack(dy, axis);\n    return derTensors.map(t => () => t);\n  }\n};", "map": {"version": 3, "names": ["Pack", "unstack", "packGradConfig", "kernelName", "saveAllInputs", "grad<PERSON>unc", "dy", "saved", "attrs", "axis", "derTensors", "map", "t"], "sources": ["C:\\tfjs-core\\src\\gradients\\Pack_grad.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {Pack, PackAttrs} from '../kernel_names';\nimport {GradConfig, NamedAttrMap} from '../kernel_registry';\nimport {unstack} from '../ops/unstack';\nimport {Tensor} from '../tensor';\n\nexport const packGradConfig: GradConfig = {\n  kernelName: Pack,\n  saveAllInputs: true,\n  gradFunc: (dy: Tensor, saved: Tensor[], attrs: NamedAttrMap) => {\n    const {axis} = attrs as unknown as PackAttrs;\n    const derTensors = unstack(dy, axis);\n    return derTensors.map(t => () => t) as {};\n  }\n};\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAiBA,SAAQA,IAAI,QAAkB,iBAAiB;AAE/C,SAAQC,OAAO,QAAO,gBAAgB;AAGtC,OAAO,MAAMC,cAAc,GAAe;EACxCC,UAAU,EAAEH,IAAI;EAChBI,aAAa,EAAE,IAAI;EACnBC,QAAQ,EAAEA,CAACC,EAAU,EAAEC,KAAe,EAAEC,KAAmB,KAAI;IAC7D,MAAM;MAACC;IAAI,CAAC,GAAGD,KAA6B;IAC5C,MAAME,UAAU,GAAGT,OAAO,CAACK,EAAE,EAAEG,IAAI,CAAC;IACpC,OAAOC,UAAU,CAACC,GAAG,CAACC,CAAC,IAAI,MAAMA,CAAC,CAAO;EAC3C;CACD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}