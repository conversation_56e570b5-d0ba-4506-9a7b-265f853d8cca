{"ast": null, "code": "/*\nCopyright (c) 2011, <PERSON>\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in\nall copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\nTHE SOFTWARE.\n*/\n\n'use strict';\n\nclass CountInflector {\n  nthForm(i) {\n    const teenth = i % 100;\n    if (teenth > 10 && teenth < 14) {\n      return 'th';\n    } else {\n      switch (i % 10) {\n        case 1:\n          return 'st';\n        case 2:\n          return 'nd';\n        case 3:\n          return 'rd';\n        default:\n          return 'th';\n      }\n    }\n  }\n  nth(i) {\n    return i.toString() + this.nthForm(i);\n  }\n}\nmodule.exports = CountInflector;", "map": {"version": 3, "names": ["Count<PERSON><PERSON><PERSON><PERSON>", "nthForm", "i", "teenth", "nth", "toString", "module", "exports"], "sources": ["C:/tmsft/node_modules/natural/lib/natural/inflectors/count_inflector.js"], "sourcesContent": ["/*\nCopyright (c) 2011, <PERSON>\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in\nall copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\nTHE SOFTWARE.\n*/\n\n'use strict'\n\nclass CountInflector {\n  nthForm (i) {\n    const teenth = (i % 100)\n\n    if (teenth > 10 && teenth < 14) { return 'th' } else {\n      switch (i % 10) {\n        case 1:\n          return 'st'\n        case 2:\n          return 'nd'\n        case 3:\n          return 'rd'\n        default:\n          return 'th'\n      }\n    }\n  }\n\n  nth (i) {\n    return i.toString() + this.nthForm(i)\n  }\n}\n\nmodule.exports = CountInflector\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,YAAY;;AAEZ,MAAMA,cAAc,CAAC;EACnBC,OAAOA,CAAEC,CAAC,EAAE;IACV,MAAMC,MAAM,GAAID,CAAC,GAAG,GAAI;IAExB,IAAIC,MAAM,GAAG,EAAE,IAAIA,MAAM,GAAG,EAAE,EAAE;MAAE,OAAO,IAAI;IAAC,CAAC,MAAM;MACnD,QAAQD,CAAC,GAAG,EAAE;QACZ,KAAK,CAAC;UACJ,OAAO,IAAI;QACb,KAAK,CAAC;UACJ,OAAO,IAAI;QACb,KAAK,CAAC;UACJ,OAAO,IAAI;QACb;UACE,OAAO,IAAI;MACf;IACF;EACF;EAEAE,GAAGA,CAAEF,CAAC,EAAE;IACN,OAAOA,CAAC,CAACG,QAAQ,CAAC,CAAC,GAAG,IAAI,CAACJ,OAAO,CAACC,CAAC,CAAC;EACvC;AACF;AAEAI,MAAM,CAACC,OAAO,GAAGP,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}