{"ast": null, "code": "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the License);\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an AS IS BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { Cosh } from '@tensorflow/tfjs-core';\nimport { unaryKernelFunc } from '../utils/unary_utils';\nexport const cosh = unaryKernelFunc(Cosh, xi => Math.cosh(xi));\nexport const coshConfig = {\n  kernelName: Cosh,\n  backendName: 'cpu',\n  kernelFunc: cosh\n};", "map": {"version": 3, "names": ["<PERSON><PERSON>", "unaryKernelFunc", "cosh", "xi", "Math", "coshConfig", "kernelName", "backendName", "kernelFunc"], "sources": ["C:\\tfjs-backend-cpu\\src\\kernels\\Cosh.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the License);\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an AS IS BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {Cosh, KernelConfig} from '@tensorflow/tfjs-core';\n\nimport {unaryKernelFunc} from '../utils/unary_utils';\n\nexport const cosh = unaryKernelFunc(Cosh, (xi) => Math.cosh(xi));\n\nexport const coshConfig: KernelConfig = {\n  kernelName: Cosh,\n  backendName: 'cpu',\n  kernelFunc: cosh,\n};\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAiBA,SAAQA,IAAI,QAAqB,uBAAuB;AAExD,SAAQC,eAAe,QAAO,sBAAsB;AAEpD,OAAO,MAAMC,IAAI,GAAGD,eAAe,CAACD,IAAI,EAAGG,EAAE,IAAKC,IAAI,CAACF,IAAI,CAACC,EAAE,CAAC,CAAC;AAEhE,OAAO,MAAME,UAAU,GAAiB;EACtCC,UAAU,EAAEN,IAAI;EAChBO,WAAW,EAAE,KAAK;EAClBC,UAAU,EAAEN;CACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}