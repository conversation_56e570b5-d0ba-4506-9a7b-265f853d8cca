{"ast": null, "code": "import { fileStorageService } from './fileStorageService';\n\n// Mock bank accounts for demo - in production this would come from a local database\nconst MOCK_BANK_ACCOUNTS = [{\n  id: '1',\n  name: 'Main Operating Account',\n  accountNumber: '**********',\n  bankName: 'First National Bank',\n  currency: 'USD',\n  currentBalance: 0.00 // Start with zero balance - will be updated from imports\n}, {\n  id: '2',\n  name: 'Savings Account',\n  accountNumber: '**********',\n  bankName: 'First National Bank',\n  currency: 'USD',\n  currentBalance: 350000.75\n}, {\n  id: '3',\n  name: 'Investment Account',\n  accountNumber: '**********',\n  bankName: 'Investment Bank Corp',\n  currency: 'USD',\n  currentBalance: 750000.00\n}, {\n  id: '4',\n  name: 'Petty Cash Account',\n  accountNumber: '**********',\n  bankName: 'Community Bank',\n  currency: 'USD',\n  currentBalance: 5000.25\n}];\nclass BankAccountService {\n  constructor() {\n    this.STORAGE_FILENAME = 'bank_accounts';\n    this.accounts = [];\n    this.loadAccounts();\n  }\n  loadAccounts() {\n    // Load from file system instead of localStorage\n    this.accounts = fileStorageService.readData(this.STORAGE_FILENAME, MOCK_BANK_ACCOUNTS);\n\n    // If no accounts exist, initialize with mock data\n    if (this.accounts.length === 0) {\n      this.accounts = [...MOCK_BANK_ACCOUNTS];\n      this.saveAccounts();\n    }\n  }\n  saveAccounts() {\n    const success = fileStorageService.writeData(this.STORAGE_FILENAME, this.accounts);\n    if (!success) {\n      console.error('Failed to save bank accounts to file system');\n    }\n  }\n  getAllAccounts() {\n    return [...this.accounts];\n  }\n  getAccountById(id) {\n    return this.accounts.find(account => account.id === id);\n  }\n  addAccount(account) {\n    const newAccount = {\n      ...account,\n      id: Date.now().toString()\n    };\n    this.accounts.push(newAccount);\n    this.saveAccounts();\n    return newAccount;\n  }\n  updateAccount(id, updates) {\n    const index = this.accounts.findIndex(account => account.id === id);\n    if (index === -1) return null;\n    this.accounts[index] = {\n      ...this.accounts[index],\n      ...updates\n    };\n    this.saveAccounts();\n    return this.accounts[index];\n  }\n  deleteAccount(id) {\n    const index = this.accounts.findIndex(account => account.id === id);\n    if (index === -1) return false;\n    this.accounts.splice(index, 1);\n    this.saveAccounts();\n    return true;\n  }\n  updateBalance(id, newBalance) {\n    const account = this.getAccountById(id);\n    if (!account) return false;\n    const updated = this.updateAccount(id, {\n      currentBalance: newBalance\n    });\n    return updated !== null;\n  }\n\n  // Get data storage location for debugging/info\n  getStorageInfo() {\n    return {\n      location: fileStorageService.getDataDirectory(),\n      filename: `${this.STORAGE_FILENAME}.json`\n    };\n  }\n}\nexport const bankAccountService = new BankAccountService();", "map": {"version": 3, "names": ["fileStorageService", "MOCK_BANK_ACCOUNTS", "id", "name", "accountNumber", "bankName", "currency", "currentBalance", "BankAccountService", "constructor", "STORAGE_FILENAME", "accounts", "loadAccounts", "readData", "length", "saveAccounts", "success", "writeData", "console", "error", "getAllAccounts", "getAccountById", "find", "account", "addAccount", "newAccount", "Date", "now", "toString", "push", "updateAccount", "updates", "index", "findIndex", "deleteAccount", "splice", "updateBalance", "newBalance", "updated", "getStorageInfo", "location", "getDataDirectory", "filename", "bankAccountService"], "sources": ["C:/tmsft/src/services/bankAccountService.ts"], "sourcesContent": ["import { BankAccount } from '../types';\r\nimport { fileStorageService } from './fileStorageService';\r\n\r\n// Mock bank accounts for demo - in production this would come from a local database\r\nconst MOCK_BANK_ACCOUNTS: BankAccount[] = [\r\n  {\r\n    id: '1',\r\n    name: 'Main Operating Account',\r\n    accountNumber: '**********',\r\n    bankName: 'First National Bank',\r\n    currency: 'USD',\r\n    currentBalance: 0.00 // Start with zero balance - will be updated from imports\r\n  },\r\n  {\r\n    id: '2',\r\n    name: 'Savings Account',\r\n    accountNumber: '**********',\r\n    bankName: 'First National Bank',\r\n    currency: 'USD',\r\n    currentBalance: 350000.75\r\n  },\r\n  {\r\n    id: '3',\r\n    name: 'Investment Account',\r\n    accountNumber: '**********',\r\n    bankName: 'Investment Bank Corp',\r\n    currency: 'USD',\r\n    currentBalance: 750000.00\r\n  },\r\n  {\r\n    id: '4',\r\n    name: 'Petty Cash Account',\r\n    accountNumber: '**********',\r\n    bankName: 'Community Bank',\r\n    currency: 'USD',\r\n    currentBalance: 5000.25\r\n  }\r\n];\r\n\r\nclass BankAccountService {\r\n  private readonly STORAGE_FILENAME = 'bank_accounts';\r\n  private accounts: BankAccount[] = [];\r\n\r\n  constructor() {\r\n    this.loadAccounts();\r\n  }\r\n\r\n  private loadAccounts(): void {\r\n    // Load from file system instead of localStorage\r\n    this.accounts = fileStorageService.readData(this.STORAGE_FILENAME, MOCK_BANK_ACCOUNTS);\r\n    \r\n    // If no accounts exist, initialize with mock data\r\n    if (this.accounts.length === 0) {\r\n      this.accounts = [...MOCK_BANK_ACCOUNTS];\r\n      this.saveAccounts();\r\n    }\r\n  }\r\n\r\n  private saveAccounts(): void {\r\n    const success = fileStorageService.writeData(this.STORAGE_FILENAME, this.accounts);\r\n    if (!success) {\r\n      console.error('Failed to save bank accounts to file system');\r\n    }\r\n  }\r\n\r\n  getAllAccounts(): BankAccount[] {\r\n    return [...this.accounts];\r\n  }\r\n\r\n  getAccountById(id: string): BankAccount | undefined {\r\n    return this.accounts.find(account => account.id === id);\r\n  }\r\n\r\n  addAccount(account: Omit<BankAccount, 'id'>): BankAccount {\r\n    const newAccount: BankAccount = {\r\n      ...account,\r\n      id: Date.now().toString()\r\n    };\r\n    this.accounts.push(newAccount);\r\n    this.saveAccounts();\r\n    return newAccount;\r\n  }\r\n\r\n  updateAccount(id: string, updates: Partial<Omit<BankAccount, 'id'>>): BankAccount | null {\r\n    const index = this.accounts.findIndex(account => account.id === id);\r\n    if (index === -1) return null;\r\n\r\n    this.accounts[index] = { ...this.accounts[index], ...updates };\r\n    this.saveAccounts();\r\n    return this.accounts[index];\r\n  }\r\n\r\n  deleteAccount(id: string): boolean {\r\n    const index = this.accounts.findIndex(account => account.id === id);\r\n    if (index === -1) return false;\r\n\r\n    this.accounts.splice(index, 1);\r\n    this.saveAccounts();\r\n    return true;\r\n  }\r\n\r\n  updateBalance(id: string, newBalance: number): boolean {\r\n    const account = this.getAccountById(id);\r\n    if (!account) return false;\r\n\r\n    const updated = this.updateAccount(id, { currentBalance: newBalance });\r\n    return updated !== null;\r\n  }\r\n\r\n  // Get data storage location for debugging/info\r\n  getStorageInfo(): { location: string; filename: string } {\r\n    return {\r\n      location: fileStorageService.getDataDirectory(),\r\n      filename: `${this.STORAGE_FILENAME}.json`\r\n    };\r\n  }\r\n}\r\n\r\nexport const bankAccountService = new BankAccountService(); "], "mappings": "AACA,SAASA,kBAAkB,QAAQ,sBAAsB;;AAEzD;AACA,MAAMC,kBAAiC,GAAG,CACxC;EACEC,EAAE,EAAE,GAAG;EACPC,IAAI,EAAE,wBAAwB;EAC9BC,aAAa,EAAE,YAAY;EAC3BC,QAAQ,EAAE,qBAAqB;EAC/BC,QAAQ,EAAE,KAAK;EACfC,cAAc,EAAE,IAAI,CAAC;AACvB,CAAC,EACD;EACEL,EAAE,EAAE,GAAG;EACPC,IAAI,EAAE,iBAAiB;EACvBC,aAAa,EAAE,YAAY;EAC3BC,QAAQ,EAAE,qBAAqB;EAC/BC,QAAQ,EAAE,KAAK;EACfC,cAAc,EAAE;AAClB,CAAC,EACD;EACEL,EAAE,EAAE,GAAG;EACPC,IAAI,EAAE,oBAAoB;EAC1BC,aAAa,EAAE,YAAY;EAC3BC,QAAQ,EAAE,sBAAsB;EAChCC,QAAQ,EAAE,KAAK;EACfC,cAAc,EAAE;AAClB,CAAC,EACD;EACEL,EAAE,EAAE,GAAG;EACPC,IAAI,EAAE,oBAAoB;EAC1BC,aAAa,EAAE,YAAY;EAC3BC,QAAQ,EAAE,gBAAgB;EAC1BC,QAAQ,EAAE,KAAK;EACfC,cAAc,EAAE;AAClB,CAAC,CACF;AAED,MAAMC,kBAAkB,CAAC;EAIvBC,WAAWA,CAAA,EAAG;IAAA,KAHGC,gBAAgB,GAAG,eAAe;IAAA,KAC3CC,QAAQ,GAAkB,EAAE;IAGlC,IAAI,CAACC,YAAY,CAAC,CAAC;EACrB;EAEQA,YAAYA,CAAA,EAAS;IAC3B;IACA,IAAI,CAACD,QAAQ,GAAGX,kBAAkB,CAACa,QAAQ,CAAC,IAAI,CAACH,gBAAgB,EAAET,kBAAkB,CAAC;;IAEtF;IACA,IAAI,IAAI,CAACU,QAAQ,CAACG,MAAM,KAAK,CAAC,EAAE;MAC9B,IAAI,CAACH,QAAQ,GAAG,CAAC,GAAGV,kBAAkB,CAAC;MACvC,IAAI,CAACc,YAAY,CAAC,CAAC;IACrB;EACF;EAEQA,YAAYA,CAAA,EAAS;IAC3B,MAAMC,OAAO,GAAGhB,kBAAkB,CAACiB,SAAS,CAAC,IAAI,CAACP,gBAAgB,EAAE,IAAI,CAACC,QAAQ,CAAC;IAClF,IAAI,CAACK,OAAO,EAAE;MACZE,OAAO,CAACC,KAAK,CAAC,6CAA6C,CAAC;IAC9D;EACF;EAEAC,cAAcA,CAAA,EAAkB;IAC9B,OAAO,CAAC,GAAG,IAAI,CAACT,QAAQ,CAAC;EAC3B;EAEAU,cAAcA,CAACnB,EAAU,EAA2B;IAClD,OAAO,IAAI,CAACS,QAAQ,CAACW,IAAI,CAACC,OAAO,IAAIA,OAAO,CAACrB,EAAE,KAAKA,EAAE,CAAC;EACzD;EAEAsB,UAAUA,CAACD,OAAgC,EAAe;IACxD,MAAME,UAAuB,GAAG;MAC9B,GAAGF,OAAO;MACVrB,EAAE,EAAEwB,IAAI,CAACC,GAAG,CAAC,CAAC,CAACC,QAAQ,CAAC;IAC1B,CAAC;IACD,IAAI,CAACjB,QAAQ,CAACkB,IAAI,CAACJ,UAAU,CAAC;IAC9B,IAAI,CAACV,YAAY,CAAC,CAAC;IACnB,OAAOU,UAAU;EACnB;EAEAK,aAAaA,CAAC5B,EAAU,EAAE6B,OAAyC,EAAsB;IACvF,MAAMC,KAAK,GAAG,IAAI,CAACrB,QAAQ,CAACsB,SAAS,CAACV,OAAO,IAAIA,OAAO,CAACrB,EAAE,KAAKA,EAAE,CAAC;IACnE,IAAI8B,KAAK,KAAK,CAAC,CAAC,EAAE,OAAO,IAAI;IAE7B,IAAI,CAACrB,QAAQ,CAACqB,KAAK,CAAC,GAAG;MAAE,GAAG,IAAI,CAACrB,QAAQ,CAACqB,KAAK,CAAC;MAAE,GAAGD;IAAQ,CAAC;IAC9D,IAAI,CAAChB,YAAY,CAAC,CAAC;IACnB,OAAO,IAAI,CAACJ,QAAQ,CAACqB,KAAK,CAAC;EAC7B;EAEAE,aAAaA,CAAChC,EAAU,EAAW;IACjC,MAAM8B,KAAK,GAAG,IAAI,CAACrB,QAAQ,CAACsB,SAAS,CAACV,OAAO,IAAIA,OAAO,CAACrB,EAAE,KAAKA,EAAE,CAAC;IACnE,IAAI8B,KAAK,KAAK,CAAC,CAAC,EAAE,OAAO,KAAK;IAE9B,IAAI,CAACrB,QAAQ,CAACwB,MAAM,CAACH,KAAK,EAAE,CAAC,CAAC;IAC9B,IAAI,CAACjB,YAAY,CAAC,CAAC;IACnB,OAAO,IAAI;EACb;EAEAqB,aAAaA,CAAClC,EAAU,EAAEmC,UAAkB,EAAW;IACrD,MAAMd,OAAO,GAAG,IAAI,CAACF,cAAc,CAACnB,EAAE,CAAC;IACvC,IAAI,CAACqB,OAAO,EAAE,OAAO,KAAK;IAE1B,MAAMe,OAAO,GAAG,IAAI,CAACR,aAAa,CAAC5B,EAAE,EAAE;MAAEK,cAAc,EAAE8B;IAAW,CAAC,CAAC;IACtE,OAAOC,OAAO,KAAK,IAAI;EACzB;;EAEA;EACAC,cAAcA,CAAA,EAA2C;IACvD,OAAO;MACLC,QAAQ,EAAExC,kBAAkB,CAACyC,gBAAgB,CAAC,CAAC;MAC/CC,QAAQ,EAAE,GAAG,IAAI,CAAChC,gBAAgB;IACpC,CAAC;EACH;AACF;AAEA,OAAO,MAAMiC,kBAAkB,GAAG,IAAInC,kBAAkB,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}