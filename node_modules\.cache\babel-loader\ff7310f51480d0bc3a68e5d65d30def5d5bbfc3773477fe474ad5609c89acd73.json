{"ast": null, "code": "import partial from './partial.js';\nimport before from './before.js';\n\n// Returns a function that will be executed at most one time, no matter how\n// often you call it. Useful for lazy initialization.\nexport default partial(before, 2);", "map": {"version": 3, "names": ["partial", "before"], "sources": ["C:/tmsft/node_modules/underscore/modules/once.js"], "sourcesContent": ["import partial from './partial.js';\nimport before from './before.js';\n\n// Returns a function that will be executed at most one time, no matter how\n// often you call it. Useful for lazy initialization.\nexport default partial(before, 2);\n"], "mappings": "AAAA,OAAOA,OAAO,MAAM,cAAc;AAClC,OAAOC,MAAM,MAAM,aAAa;;AAEhC;AACA;AACA,eAAeD,OAAO,CAACC,MAAM,EAAE,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}