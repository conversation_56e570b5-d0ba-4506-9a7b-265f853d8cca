{"ast": null, "code": "/**\n * @license\n * Copyright 2018 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { ENGINE } from './engine';\nimport { env } from './environment';\nimport { setDeprecationWarningFn } from './tensor';\nimport { getTensorsInContainer } from './tensor_util';\n/**\n * Enables production mode which disables correctness checks in favor of\n * performance.\n *\n * @doc {heading: 'Environment'}\n */\nexport function enableProdMode() {\n  env().set('PROD', true);\n}\n/**\n * Enables debug mode which will log information about all executed kernels:\n * the elapsed time of the kernel execution, as well as the rank, shape, and\n * size of the output tensor.\n *\n * Debug mode will significantly slow down your application as it will\n * download the result of every operation to the CPU. This should not be used in\n * production. Debug mode does not affect the timing information of the kernel\n * execution as we do not measure download time in the kernel execution time.\n *\n * See also: `tf.profile`, `tf.memory`.\n *\n * @doc {heading: 'Environment'}\n */\nexport function enableDebugMode() {\n  env().set('DEBUG', true);\n}\n/** Globally disables deprecation warnings */\nexport function disableDeprecationWarnings() {\n  env().set('DEPRECATION_WARNINGS_ENABLED', false);\n  console.warn(\"TensorFlow.js deprecation warnings have been disabled.\");\n}\n/** Warn users about deprecated functionality. */\nexport function deprecationWarn(msg) {\n  if (env().getBool('DEPRECATION_WARNINGS_ENABLED')) {\n    console.warn(msg + ' You can disable deprecation warnings with ' + 'tf.disableDeprecationWarnings().');\n  }\n}\nsetDeprecationWarningFn(deprecationWarn);\n/**\n * Dispose all variables kept in backend engine.\n *\n * @doc {heading: 'Environment'}\n */\nexport function disposeVariables() {\n  ENGINE.disposeVariables();\n}\n/**\n * It returns the global engine that keeps track of all tensors and backends.\n *\n * @doc {heading: 'Environment'}\n */\nexport function engine() {\n  return ENGINE;\n}\n/**\n * Returns memory info at the current time in the program. The result is an\n * object with the following properties:\n *\n * - `numBytes`: Number of bytes allocated (undisposed) at this time.\n * - `numTensors`: Number of unique tensors allocated.\n * - `numDataBuffers`: Number of unique data buffers allocated\n *   (undisposed) at this time, which is ≤ the number of tensors\n *   (e.g. `a.reshape(newShape)` makes a new Tensor that shares the same\n *   data buffer with `a`).\n * - `unreliable`: True if the memory usage is unreliable. See `reasons` when\n *    `unreliable` is true.\n * - `reasons`: `string[]`, reasons why the memory is unreliable, present if\n *    `unreliable` is true.\n *\n * WebGL Properties:\n * - `numBytesInGPU`: Number of bytes allocated (undisposed) in the GPU only at\n *     this time.\n *\n * @doc {heading: 'Performance', subheading: 'Memory'}\n */\nexport function memory() {\n  return ENGINE.memory();\n}\n/**\n * Executes the provided function `f()` and returns a promise that resolves\n * with information about the function's memory use:\n * - `newBytes`: the number of new bytes allocated\n * - `newTensors`: the number of new tensors created\n * - `peakBytes`: the peak number of bytes allocated\n * - `kernels`: an array of objects for each kernel involved that reports\n * their input and output shapes, number of bytes used, and number of new\n * tensors created.\n * - `kernelNames`: an array of unique strings with just the names of the\n * kernels in the `kernels` array.\n *\n * ```js\n * const profile = await tf.profile(() => {\n *   const x = tf.tensor1d([1, 2, 3]);\n *   let x2 = x.square();\n *   x2.dispose();\n *   x2 = x.square();\n *   x2.dispose();\n *   return x;\n * });\n *\n * console.log(`newBytes: ${profile.newBytes}`);\n * console.log(`newTensors: ${profile.newTensors}`);\n * console.log(`byte usage over all kernels: ${profile.kernels.map(k =>\n * k.totalBytesSnapshot)}`);\n * ```\n *\n *\n * @doc {heading: 'Performance', subheading: 'Profile'}\n */\nexport function profile(f) {\n  return ENGINE.profile(f);\n}\n/**\n * Executes the provided function `fn` and after it is executed, cleans up all\n * intermediate tensors allocated by `fn` except those returned by `fn`.\n * `fn` must not return a Promise (async functions not allowed). The returned\n * result can be a complex object.\n *\n * Using this method helps avoid memory leaks. In general, wrap calls to\n * operations in `tf.tidy` for automatic memory cleanup.\n *\n * NOTE: Variables do *not* get cleaned up when inside a tidy(). If you want to\n * dispose variables, please use `tf.disposeVariables` or call dispose()\n * directly on variables.\n *\n * ```js\n * // y = 2 ^ 2 + 1\n * const y = tf.tidy(() => {\n *   // a, b, and one will be cleaned up when the tidy ends.\n *   const one = tf.scalar(1);\n *   const a = tf.scalar(2);\n *   const b = a.square();\n *\n *   console.log('numTensors (in tidy): ' + tf.memory().numTensors);\n *\n *   // The value returned inside the tidy function will return\n *   // through the tidy, in this case to the variable y.\n *   return b.add(one);\n * });\n *\n * console.log('numTensors (outside tidy): ' + tf.memory().numTensors);\n * y.print();\n * ```\n *\n * @param nameOrFn The name of the closure, or the function to execute.\n *     If a name is provided, the 2nd argument should be the function.\n *     If debug mode is on, the timing and the memory usage of the function\n *     will be tracked and displayed on the console using the provided name.\n * @param fn The function to execute.\n *\n * @doc {heading: 'Performance', subheading: 'Memory'}\n */\nexport function tidy(nameOrFn, fn) {\n  return ENGINE.tidy(nameOrFn, fn);\n}\n/**\n * Disposes any `tf.Tensor`s found within the provided object.\n *\n * @param container an object that may be a `tf.Tensor` or may directly\n *     contain `tf.Tensor`s, such as a `Tensor[]` or `{key: Tensor, ...}`. If\n *     the object is not a `tf.Tensor` or does not contain `Tensors`, nothing\n *     happens. In general it is safe to pass any object here, except that\n *     `Promise`s are not supported.\n *\n * @doc {heading: 'Performance', subheading: 'Memory'}\n */\nexport function dispose(container) {\n  const tensors = getTensorsInContainer(container);\n  tensors.forEach(tensor => tensor.dispose());\n}\n/**\n * Keeps a `tf.Tensor` generated inside a `tf.tidy` from being disposed\n * automatically.\n *\n * ```js\n * let b;\n * const y = tf.tidy(() => {\n *   const one = tf.scalar(1);\n *   const a = tf.scalar(2);\n *\n *   // b will not be cleaned up by the tidy. a and one will be cleaned up\n *   // when the tidy ends.\n *   b = tf.keep(a.square());\n *\n *   console.log('numTensors (in tidy): ' + tf.memory().numTensors);\n *\n *   // The value returned inside the tidy function will return\n *   // through the tidy, in this case to the variable y.\n *   return b.add(one);\n * });\n *\n * console.log('numTensors (outside tidy): ' + tf.memory().numTensors);\n * console.log('y:');\n * y.print();\n * console.log('b:');\n * b.print();\n * ```\n *\n * @param result The tensor to keep from being disposed.\n *\n * @doc {heading: 'Performance', subheading: 'Memory'}\n */\nexport function keep(result) {\n  return ENGINE.keep(result);\n}\n/**\n * Executes `f()` and returns a promise that resolves with timing\n * information.\n *\n * The result is an object with the following properties:\n *\n * - `wallMs`: Wall execution time.\n * - `kernelMs`: Kernel execution time, ignoring data transfer. If using the\n * WebGL backend and the query timer extension is not available, this will\n * return an error object.\n * - On `WebGL` The following additional properties exist:\n *   - `uploadWaitMs`: CPU blocking time on texture uploads.\n *   - `downloadWaitMs`: CPU blocking time on texture downloads (readPixels).\n *\n * ```js\n * const x = tf.randomNormal([20, 20]);\n * const time = await tf.time(() => x.matMul(x));\n *\n * console.log(`kernelMs: ${time.kernelMs}, wallTimeMs: ${time.wallMs}`);\n * ```\n *\n * @param f The function to execute and time.\n *\n * @doc {heading: 'Performance', subheading: 'Timing'}\n */\nexport function time(f) {\n  return ENGINE.time(f);\n}\n/**\n * Sets the backend (cpu, webgl, wasm, etc) responsible for creating tensors and\n * executing operations on those tensors. Returns a promise that resolves\n * to a boolean if the backend initialization was successful.\n *\n * Note this disposes the current backend, if any, as well as any tensors\n * associated with it. A new backend is initialized, even if it is of the\n * same type as the previous one.\n *\n * @param backendName The name of the backend. Currently supports\n *     `'webgl'|'cpu'` in the browser, `'tensorflow'` under node.js\n *     (requires tfjs-node), and `'wasm'` (requires tfjs-backend-wasm).\n *\n * @doc {heading: 'Backends'}\n */\nexport function setBackend(backendName) {\n  return ENGINE.setBackend(backendName);\n}\n/**\n * Returns a promise that resolves when the currently selected backend (or the\n * highest priority one) has initialized. Await this promise when you are using\n * a backend that has async initialization.\n *\n * @doc {heading: 'Backends'}\n */\nexport function ready() {\n  return ENGINE.ready();\n}\n/**\n * Returns the current backend name (cpu, webgl, etc). The backend is\n * responsible for creating tensors and executing operations on those tensors.\n *\n * @doc {heading: 'Backends'}\n */\nexport function getBackend() {\n  return ENGINE.backendName;\n}\n/**\n * Removes a backend and the registered factory.\n *\n * @doc {heading: 'Backends'}\n */\nexport function removeBackend(name) {\n  ENGINE.removeBackend(name);\n}\n/**\n * Finds the backend registered under the provided name. Returns null if the\n * name is not in the registry, or the registration hasn't finished yet.\n */\nexport function findBackend(name) {\n  return ENGINE.findBackend(name);\n}\n/**\n * Finds the backend factory registered under the provided name. Returns a\n * function that produces a new backend when called. Returns null if the name\n * is not in the registry.\n */\nexport function findBackendFactory(name) {\n  return ENGINE.findBackendFactory(name);\n}\n/**\n * Registers a global backend. The registration should happen when importing\n * a module file (e.g. when importing `backend_webgl.ts`), and is used for\n * modular builds (e.g. custom tfjs bundle with only webgl support).\n *\n * @param factory The backend factory function. When called, it should\n * return a backend instance, or a promise of an instance.\n * @param priority The priority of the backend (higher = more important).\n *     In case multiple backends are registered, the priority is used to find\n *     the best backend. Defaults to 1.\n * @return False if there is already a registered backend under this name, true\n *     if not.\n *\n * @doc {heading: 'Backends'}\n */\nexport function registerBackend(name, factory) {\n  let priority = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 1;\n  return ENGINE.registerBackend(name, factory, priority);\n}\n/**\n * Gets the current backend. If no backends have been initialized, this will\n * attempt to initialize the best backend. Will throw an error if the highest\n * priority backend has async initialization, in which case you should call\n * 'await tf.ready()' before running other code.\n *\n * @doc {heading: 'Backends'}\n */\nexport function backend() {\n  return ENGINE.backend;\n}\n/**\n * Sets the global platform.\n *\n * @param platformName The name of this platform.\n * @param platform A platform implementation.\n */\nexport function setPlatform(platformName, platform) {\n  env().setPlatform(platformName, platform);\n}", "map": {"version": 3, "names": ["ENGINE", "env", "setDeprecationWarningFn", "getTensorsInContainer", "enableProdMode", "set", "enableDebugMode", "disableDeprecationWarnings", "console", "warn", "deprecationWarn", "msg", "getBool", "disposeVariables", "engine", "memory", "profile", "f", "tidy", "nameOrFn", "fn", "dispose", "container", "tensors", "for<PERSON>ach", "tensor", "keep", "result", "time", "setBackend", "backendName", "ready", "getBackend", "removeBackend", "name", "findBackend", "findBackendFactory", "registerBackend", "factory", "priority", "arguments", "length", "undefined", "backend", "setPlatform", "platformName", "platform"], "sources": ["C:\\tfjs-core\\src\\globals.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2018 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {KernelBackend} from './backends/backend';\nimport {ENGINE, Engine, MemoryInfo, ProfileInfo, ScopeFn, TimingInfo} from './engine';\nimport {env} from './environment';\n\nimport {Platform} from './platforms/platform';\nimport {setDeprecationWarningFn, Tensor} from './tensor';\nimport {TensorContainer} from './tensor_types';\nimport {getTensorsInContainer} from './tensor_util';\n\n/**\n * Enables production mode which disables correctness checks in favor of\n * performance.\n *\n * @doc {heading: 'Environment'}\n */\nexport function enableProdMode(): void {\n  env().set('PROD', true);\n}\n\n/**\n * Enables debug mode which will log information about all executed kernels:\n * the elapsed time of the kernel execution, as well as the rank, shape, and\n * size of the output tensor.\n *\n * Debug mode will significantly slow down your application as it will\n * download the result of every operation to the CPU. This should not be used in\n * production. Debug mode does not affect the timing information of the kernel\n * execution as we do not measure download time in the kernel execution time.\n *\n * See also: `tf.profile`, `tf.memory`.\n *\n * @doc {heading: 'Environment'}\n */\nexport function enableDebugMode(): void {\n  env().set('DEBUG', true);\n}\n\n/** Globally disables deprecation warnings */\nexport function disableDeprecationWarnings(): void {\n  env().set('DEPRECATION_WARNINGS_ENABLED', false);\n  console.warn(`TensorFlow.js deprecation warnings have been disabled.`);\n}\n\n/** Warn users about deprecated functionality. */\nexport function deprecationWarn(msg: string) {\n  if (env().getBool('DEPRECATION_WARNINGS_ENABLED')) {\n    console.warn(\n        msg + ' You can disable deprecation warnings with ' +\n        'tf.disableDeprecationWarnings().');\n  }\n}\nsetDeprecationWarningFn(deprecationWarn);\n\n/**\n * Dispose all variables kept in backend engine.\n *\n * @doc {heading: 'Environment'}\n */\nexport function disposeVariables(): void {\n  ENGINE.disposeVariables();\n}\n\n/**\n * It returns the global engine that keeps track of all tensors and backends.\n *\n * @doc {heading: 'Environment'}\n */\nexport function engine(): Engine {\n  return ENGINE;\n}\n\n/**\n * Returns memory info at the current time in the program. The result is an\n * object with the following properties:\n *\n * - `numBytes`: Number of bytes allocated (undisposed) at this time.\n * - `numTensors`: Number of unique tensors allocated.\n * - `numDataBuffers`: Number of unique data buffers allocated\n *   (undisposed) at this time, which is ≤ the number of tensors\n *   (e.g. `a.reshape(newShape)` makes a new Tensor that shares the same\n *   data buffer with `a`).\n * - `unreliable`: True if the memory usage is unreliable. See `reasons` when\n *    `unreliable` is true.\n * - `reasons`: `string[]`, reasons why the memory is unreliable, present if\n *    `unreliable` is true.\n *\n * WebGL Properties:\n * - `numBytesInGPU`: Number of bytes allocated (undisposed) in the GPU only at\n *     this time.\n *\n * @doc {heading: 'Performance', subheading: 'Memory'}\n */\nexport function memory(): MemoryInfo {\n  return ENGINE.memory();\n}\n\n/**\n * Executes the provided function `f()` and returns a promise that resolves\n * with information about the function's memory use:\n * - `newBytes`: the number of new bytes allocated\n * - `newTensors`: the number of new tensors created\n * - `peakBytes`: the peak number of bytes allocated\n * - `kernels`: an array of objects for each kernel involved that reports\n * their input and output shapes, number of bytes used, and number of new\n * tensors created.\n * - `kernelNames`: an array of unique strings with just the names of the\n * kernels in the `kernels` array.\n *\n * ```js\n * const profile = await tf.profile(() => {\n *   const x = tf.tensor1d([1, 2, 3]);\n *   let x2 = x.square();\n *   x2.dispose();\n *   x2 = x.square();\n *   x2.dispose();\n *   return x;\n * });\n *\n * console.log(`newBytes: ${profile.newBytes}`);\n * console.log(`newTensors: ${profile.newTensors}`);\n * console.log(`byte usage over all kernels: ${profile.kernels.map(k =>\n * k.totalBytesSnapshot)}`);\n * ```\n *\n *\n * @doc {heading: 'Performance', subheading: 'Profile'}\n */\nexport function profile(f: () => (TensorContainer | Promise<TensorContainer>)):\n    Promise<ProfileInfo> {\n  return ENGINE.profile(f);\n}\n\n/**\n * Executes the provided function `fn` and after it is executed, cleans up all\n * intermediate tensors allocated by `fn` except those returned by `fn`.\n * `fn` must not return a Promise (async functions not allowed). The returned\n * result can be a complex object.\n *\n * Using this method helps avoid memory leaks. In general, wrap calls to\n * operations in `tf.tidy` for automatic memory cleanup.\n *\n * NOTE: Variables do *not* get cleaned up when inside a tidy(). If you want to\n * dispose variables, please use `tf.disposeVariables` or call dispose()\n * directly on variables.\n *\n * ```js\n * // y = 2 ^ 2 + 1\n * const y = tf.tidy(() => {\n *   // a, b, and one will be cleaned up when the tidy ends.\n *   const one = tf.scalar(1);\n *   const a = tf.scalar(2);\n *   const b = a.square();\n *\n *   console.log('numTensors (in tidy): ' + tf.memory().numTensors);\n *\n *   // The value returned inside the tidy function will return\n *   // through the tidy, in this case to the variable y.\n *   return b.add(one);\n * });\n *\n * console.log('numTensors (outside tidy): ' + tf.memory().numTensors);\n * y.print();\n * ```\n *\n * @param nameOrFn The name of the closure, or the function to execute.\n *     If a name is provided, the 2nd argument should be the function.\n *     If debug mode is on, the timing and the memory usage of the function\n *     will be tracked and displayed on the console using the provided name.\n * @param fn The function to execute.\n *\n * @doc {heading: 'Performance', subheading: 'Memory'}\n */\nexport function tidy<T extends TensorContainer>(\n    nameOrFn: string|ScopeFn<T>, fn?: ScopeFn<T>): T {\n  return ENGINE.tidy(nameOrFn, fn);\n}\n\n/**\n * Disposes any `tf.Tensor`s found within the provided object.\n *\n * @param container an object that may be a `tf.Tensor` or may directly\n *     contain `tf.Tensor`s, such as a `Tensor[]` or `{key: Tensor, ...}`. If\n *     the object is not a `tf.Tensor` or does not contain `Tensors`, nothing\n *     happens. In general it is safe to pass any object here, except that\n *     `Promise`s are not supported.\n *\n * @doc {heading: 'Performance', subheading: 'Memory'}\n */\nexport function dispose(container: TensorContainer) {\n  const tensors = getTensorsInContainer(container);\n  tensors.forEach(tensor => tensor.dispose());\n}\n\n/**\n * Keeps a `tf.Tensor` generated inside a `tf.tidy` from being disposed\n * automatically.\n *\n * ```js\n * let b;\n * const y = tf.tidy(() => {\n *   const one = tf.scalar(1);\n *   const a = tf.scalar(2);\n *\n *   // b will not be cleaned up by the tidy. a and one will be cleaned up\n *   // when the tidy ends.\n *   b = tf.keep(a.square());\n *\n *   console.log('numTensors (in tidy): ' + tf.memory().numTensors);\n *\n *   // The value returned inside the tidy function will return\n *   // through the tidy, in this case to the variable y.\n *   return b.add(one);\n * });\n *\n * console.log('numTensors (outside tidy): ' + tf.memory().numTensors);\n * console.log('y:');\n * y.print();\n * console.log('b:');\n * b.print();\n * ```\n *\n * @param result The tensor to keep from being disposed.\n *\n * @doc {heading: 'Performance', subheading: 'Memory'}\n */\nexport function keep<T extends Tensor>(result: T): T {\n  return ENGINE.keep(result);\n}\n\n/**\n * Executes `f()` and returns a promise that resolves with timing\n * information.\n *\n * The result is an object with the following properties:\n *\n * - `wallMs`: Wall execution time.\n * - `kernelMs`: Kernel execution time, ignoring data transfer. If using the\n * WebGL backend and the query timer extension is not available, this will\n * return an error object.\n * - On `WebGL` The following additional properties exist:\n *   - `uploadWaitMs`: CPU blocking time on texture uploads.\n *   - `downloadWaitMs`: CPU blocking time on texture downloads (readPixels).\n *\n * ```js\n * const x = tf.randomNormal([20, 20]);\n * const time = await tf.time(() => x.matMul(x));\n *\n * console.log(`kernelMs: ${time.kernelMs}, wallTimeMs: ${time.wallMs}`);\n * ```\n *\n * @param f The function to execute and time.\n *\n * @doc {heading: 'Performance', subheading: 'Timing'}\n */\nexport function time(f: () => void): Promise<TimingInfo> {\n  return ENGINE.time(f);\n}\n\n/**\n * Sets the backend (cpu, webgl, wasm, etc) responsible for creating tensors and\n * executing operations on those tensors. Returns a promise that resolves\n * to a boolean if the backend initialization was successful.\n *\n * Note this disposes the current backend, if any, as well as any tensors\n * associated with it. A new backend is initialized, even if it is of the\n * same type as the previous one.\n *\n * @param backendName The name of the backend. Currently supports\n *     `'webgl'|'cpu'` in the browser, `'tensorflow'` under node.js\n *     (requires tfjs-node), and `'wasm'` (requires tfjs-backend-wasm).\n *\n * @doc {heading: 'Backends'}\n */\nexport function setBackend(backendName: string): Promise<boolean> {\n  return ENGINE.setBackend(backendName);\n}\n\n/**\n * Returns a promise that resolves when the currently selected backend (or the\n * highest priority one) has initialized. Await this promise when you are using\n * a backend that has async initialization.\n *\n * @doc {heading: 'Backends'}\n */\nexport function ready(): Promise<void> {\n  return ENGINE.ready();\n}\n\n/**\n * Returns the current backend name (cpu, webgl, etc). The backend is\n * responsible for creating tensors and executing operations on those tensors.\n *\n * @doc {heading: 'Backends'}\n */\nexport function getBackend(): string {\n  return ENGINE.backendName;\n}\n\n/**\n * Removes a backend and the registered factory.\n *\n * @doc {heading: 'Backends'}\n */\nexport function removeBackend(name: string): void {\n  ENGINE.removeBackend(name);\n}\n\n/**\n * Finds the backend registered under the provided name. Returns null if the\n * name is not in the registry, or the registration hasn't finished yet.\n */\nexport function findBackend(name: string): KernelBackend {\n  return ENGINE.findBackend(name);\n}\n\n/**\n * Finds the backend factory registered under the provided name. Returns a\n * function that produces a new backend when called. Returns null if the name\n * is not in the registry.\n */\nexport function findBackendFactory(name: string): () =>\n    KernelBackend | Promise<KernelBackend> {\n  return ENGINE.findBackendFactory(name);\n}\n\n/**\n * Registers a global backend. The registration should happen when importing\n * a module file (e.g. when importing `backend_webgl.ts`), and is used for\n * modular builds (e.g. custom tfjs bundle with only webgl support).\n *\n * @param factory The backend factory function. When called, it should\n * return a backend instance, or a promise of an instance.\n * @param priority The priority of the backend (higher = more important).\n *     In case multiple backends are registered, the priority is used to find\n *     the best backend. Defaults to 1.\n * @return False if there is already a registered backend under this name, true\n *     if not.\n *\n * @doc {heading: 'Backends'}\n */\nexport function registerBackend(\n    name: string, factory: () => KernelBackend | Promise<KernelBackend>,\n    priority = 1): boolean {\n  return ENGINE.registerBackend(name, factory, priority);\n}\n\n/**\n * Gets the current backend. If no backends have been initialized, this will\n * attempt to initialize the best backend. Will throw an error if the highest\n * priority backend has async initialization, in which case you should call\n * 'await tf.ready()' before running other code.\n *\n * @doc {heading: 'Backends'}\n */\nexport function backend(): KernelBackend {\n  return ENGINE.backend;\n}\n\n/**\n * Sets the global platform.\n *\n * @param platformName The name of this platform.\n * @param platform A platform implementation.\n */\nexport function setPlatform(platformName: string, platform: Platform) {\n  env().setPlatform(platformName, platform);\n}\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAkBA,SAAQA,MAAM,QAA6D,UAAU;AACrF,SAAQC,GAAG,QAAO,eAAe;AAGjC,SAAQC,uBAAuB,QAAe,UAAU;AAExD,SAAQC,qBAAqB,QAAO,eAAe;AAEnD;;;;;;AAMA,OAAM,SAAUC,cAAcA,CAAA;EAC5BH,GAAG,EAAE,CAACI,GAAG,CAAC,MAAM,EAAE,IAAI,CAAC;AACzB;AAEA;;;;;;;;;;;;;;AAcA,OAAM,SAAUC,eAAeA,CAAA;EAC7BL,GAAG,EAAE,CAACI,GAAG,CAAC,OAAO,EAAE,IAAI,CAAC;AAC1B;AAEA;AACA,OAAM,SAAUE,0BAA0BA,CAAA;EACxCN,GAAG,EAAE,CAACI,GAAG,CAAC,8BAA8B,EAAE,KAAK,CAAC;EAChDG,OAAO,CAACC,IAAI,yDAAyD,CAAC;AACxE;AAEA;AACA,OAAM,SAAUC,eAAeA,CAACC,GAAW;EACzC,IAAIV,GAAG,EAAE,CAACW,OAAO,CAAC,8BAA8B,CAAC,EAAE;IACjDJ,OAAO,CAACC,IAAI,CACRE,GAAG,GAAG,6CAA6C,GACnD,kCAAkC,CAAC;;AAE3C;AACAT,uBAAuB,CAACQ,eAAe,CAAC;AAExC;;;;;AAKA,OAAM,SAAUG,gBAAgBA,CAAA;EAC9Bb,MAAM,CAACa,gBAAgB,EAAE;AAC3B;AAEA;;;;;AAKA,OAAM,SAAUC,MAAMA,CAAA;EACpB,OAAOd,MAAM;AACf;AAEA;;;;;;;;;;;;;;;;;;;;;AAqBA,OAAM,SAAUe,MAAMA,CAAA;EACpB,OAAOf,MAAM,CAACe,MAAM,EAAE;AACxB;AAEA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA+BA,OAAM,SAAUC,OAAOA,CAACC,CAAqD;EAE3E,OAAOjB,MAAM,CAACgB,OAAO,CAACC,CAAC,CAAC;AAC1B;AAEA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAwCA,OAAM,SAAUC,IAAIA,CAChBC,QAA2B,EAAEC,EAAe;EAC9C,OAAOpB,MAAM,CAACkB,IAAI,CAACC,QAAQ,EAAEC,EAAE,CAAC;AAClC;AAEA;;;;;;;;;;;AAWA,OAAM,SAAUC,OAAOA,CAACC,SAA0B;EAChD,MAAMC,OAAO,GAAGpB,qBAAqB,CAACmB,SAAS,CAAC;EAChDC,OAAO,CAACC,OAAO,CAACC,MAAM,IAAIA,MAAM,CAACJ,OAAO,EAAE,CAAC;AAC7C;AAEA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgCA,OAAM,SAAUK,IAAIA,CAAmBC,MAAS;EAC9C,OAAO3B,MAAM,CAAC0B,IAAI,CAACC,MAAM,CAAC;AAC5B;AAEA;;;;;;;;;;;;;;;;;;;;;;;;;AAyBA,OAAM,SAAUC,IAAIA,CAACX,CAAa;EAChC,OAAOjB,MAAM,CAAC4B,IAAI,CAACX,CAAC,CAAC;AACvB;AAEA;;;;;;;;;;;;;;;AAeA,OAAM,SAAUY,UAAUA,CAACC,WAAmB;EAC5C,OAAO9B,MAAM,CAAC6B,UAAU,CAACC,WAAW,CAAC;AACvC;AAEA;;;;;;;AAOA,OAAM,SAAUC,KAAKA,CAAA;EACnB,OAAO/B,MAAM,CAAC+B,KAAK,EAAE;AACvB;AAEA;;;;;;AAMA,OAAM,SAAUC,UAAUA,CAAA;EACxB,OAAOhC,MAAM,CAAC8B,WAAW;AAC3B;AAEA;;;;;AAKA,OAAM,SAAUG,aAAaA,CAACC,IAAY;EACxClC,MAAM,CAACiC,aAAa,CAACC,IAAI,CAAC;AAC5B;AAEA;;;;AAIA,OAAM,SAAUC,WAAWA,CAACD,IAAY;EACtC,OAAOlC,MAAM,CAACmC,WAAW,CAACD,IAAI,CAAC;AACjC;AAEA;;;;;AAKA,OAAM,SAAUE,kBAAkBA,CAACF,IAAY;EAE7C,OAAOlC,MAAM,CAACoC,kBAAkB,CAACF,IAAI,CAAC;AACxC;AAEA;;;;;;;;;;;;;;;AAeA,OAAM,SAAUG,eAAeA,CAC3BH,IAAY,EAAEI,OAAqD,EACvD;EAAA,IAAZC,QAAQ,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC;EACd,OAAOxC,MAAM,CAACqC,eAAe,CAACH,IAAI,EAAEI,OAAO,EAAEC,QAAQ,CAAC;AACxD;AAEA;;;;;;;;AAQA,OAAM,SAAUI,OAAOA,CAAA;EACrB,OAAO3C,MAAM,CAAC2C,OAAO;AACvB;AAEA;;;;;;AAMA,OAAM,SAAUC,WAAWA,CAACC,YAAoB,EAAEC,QAAkB;EAClE7C,GAAG,EAAE,CAAC2C,WAAW,CAACC,YAAY,EAAEC,QAAQ,CAAC;AAC3C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}