{"ast": null, "code": "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { ENGINE } from '../engine';\nimport { Unique } from '../kernel_names';\nimport { convertToTensor } from '../tensor_util_env';\nimport { assert } from '../util';\nimport { op } from './operation';\n/**\n * Finds unique elements along an axis of a tensor.\n *\n * It returns a tensor `values` containing all of the unique elements along the\n * `axis` of the given tensor `x` in the same order that they occur along the\n * `axis` in `x`; `x` does not need to be sorted. It also returns a tensor\n * `indices` the same size as the number of the elements in `x` along the `axis`\n * dimension. It contains the index in the unique output `values`.\n *\n * ```js\n * // A 1-D tensor\n * const a = tf.tensor1d([1, 1, 2, 4, 4, 4, 7, 8, 8]);\n * const {values, indices} = tf.unique(a);\n * values.print();   // [1, 2, 4, 7, 8,]\n * indices.print();  // [0, 0, 1, 2, 2, 2, 3, 4, 4]\n * ```\n *\n * ```js\n * // A 2-D tensor with axis=0\n * //\n * // 'a' is: [[1, 0, 0],\n * //          [1, 0, 0],\n * //          [2, 0, 0]]\n * const a = tf.tensor2d([[1, 0, 0], [1, 0, 0], [2, 0, 0]]);\n * const {values, indices} = tf.unique(a, 0)\n * values.print();   // [[1, 0, 0],\n *                   //  [2, 0, 0]]\n * indices.print();  // [0, 0, 1]\n * ```\n *\n * ```js\n * // A 2-D tensor with axis=1\n * //\n * // 'a' is: [[1, 0, 0],\n * //          [1, 0, 0],\n * //          [2, 0, 0]]\n * const a = tf.tensor2d([[1, 0, 0], [1, 0, 0], [2, 0, 0]]);\n * const {values, indices} = tf.unique(a, 1)\n * values.print();   // [[1, 0],\n *                   //  [1, 0],\n *                   //  [2, 0]]\n * indices.print();  // [0, 1, 1]\n * ```\n * @param x A tensor (int32, string, bool).\n * @param axis The axis of the tensor to find the unique elements.\n * @returns [uniqueElements, indices] (see above for details)\n *\n * @doc {heading: 'Operations', subheading: 'Evaluation'}\n */\nfunction unique_(x) {\n  let axis = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 0;\n  const $x = convertToTensor(x, 'x', 'unique', 'string_or_numeric');\n  assert($x.rank > 0, () => 'The input tensor must be at least 1D');\n  const inputs = {\n    x: $x\n  };\n  const attrs = {\n    axis\n  };\n  const [values, indices] = ENGINE.runKernel(Unique, inputs, attrs);\n  return {\n    values,\n    indices\n  };\n}\nexport const unique = /* @__PURE__ */op({\n  unique_\n});", "map": {"version": 3, "names": ["ENGINE", "Unique", "convertToTensor", "assert", "op", "unique_", "x", "axis", "arguments", "length", "undefined", "$x", "rank", "inputs", "attrs", "values", "indices", "runKernel", "unique"], "sources": ["C:\\tfjs-core\\src\\ops\\unique.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {ENGINE} from '../engine';\nimport {Unique, UniqueAttrs, UniqueInputs} from '../kernel_names';\nimport {NamedAttrMap} from '../kernel_registry';\nimport {Tensor, Tensor1D} from '../tensor';\nimport {NamedTensorMap} from '../tensor_types';\nimport {convertToTensor} from '../tensor_util_env';\nimport {TensorLike} from '../types';\nimport {assert} from '../util';\n\nimport {op} from './operation';\n\n/**\n * Finds unique elements along an axis of a tensor.\n *\n * It returns a tensor `values` containing all of the unique elements along the\n * `axis` of the given tensor `x` in the same order that they occur along the\n * `axis` in `x`; `x` does not need to be sorted. It also returns a tensor\n * `indices` the same size as the number of the elements in `x` along the `axis`\n * dimension. It contains the index in the unique output `values`.\n *\n * ```js\n * // A 1-D tensor\n * const a = tf.tensor1d([1, 1, 2, 4, 4, 4, 7, 8, 8]);\n * const {values, indices} = tf.unique(a);\n * values.print();   // [1, 2, 4, 7, 8,]\n * indices.print();  // [0, 0, 1, 2, 2, 2, 3, 4, 4]\n * ```\n *\n * ```js\n * // A 2-D tensor with axis=0\n * //\n * // 'a' is: [[1, 0, 0],\n * //          [1, 0, 0],\n * //          [2, 0, 0]]\n * const a = tf.tensor2d([[1, 0, 0], [1, 0, 0], [2, 0, 0]]);\n * const {values, indices} = tf.unique(a, 0)\n * values.print();   // [[1, 0, 0],\n *                   //  [2, 0, 0]]\n * indices.print();  // [0, 0, 1]\n * ```\n *\n * ```js\n * // A 2-D tensor with axis=1\n * //\n * // 'a' is: [[1, 0, 0],\n * //          [1, 0, 0],\n * //          [2, 0, 0]]\n * const a = tf.tensor2d([[1, 0, 0], [1, 0, 0], [2, 0, 0]]);\n * const {values, indices} = tf.unique(a, 1)\n * values.print();   // [[1, 0],\n *                   //  [1, 0],\n *                   //  [2, 0]]\n * indices.print();  // [0, 1, 1]\n * ```\n * @param x A tensor (int32, string, bool).\n * @param axis The axis of the tensor to find the unique elements.\n * @returns [uniqueElements, indices] (see above for details)\n *\n * @doc {heading: 'Operations', subheading: 'Evaluation'}\n */\nfunction unique_<T extends Tensor>(\n    x: T|TensorLike, axis = 0): {values: T, indices: Tensor1D} {\n  const $x = convertToTensor(x, 'x', 'unique', 'string_or_numeric');\n  assert($x.rank > 0, () => 'The input tensor must be at least 1D');\n\n  const inputs: UniqueInputs = {x: $x};\n  const attrs: UniqueAttrs = {axis};\n  const [values, indices] = ENGINE.runKernel(\n      Unique, inputs as unknown as NamedTensorMap,\n      attrs as unknown as NamedAttrMap) as [T, Tensor1D];\n  return {values, indices};\n}\n\nexport const unique = /* @__PURE__ */ op({unique_});\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAiBA,SAAQA,MAAM,QAAO,WAAW;AAChC,SAAQC,MAAM,QAAkC,iBAAiB;AAIjE,SAAQC,eAAe,QAAO,oBAAoB;AAElD,SAAQC,MAAM,QAAO,SAAS;AAE9B,SAAQC,EAAE,QAAO,aAAa;AAE9B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiDA,SAASC,OAAOA,CACZC,CAAe,EAAU;EAAA,IAARC,IAAI,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC;EAC3B,MAAMG,EAAE,GAAGT,eAAe,CAACI,CAAC,EAAE,GAAG,EAAE,QAAQ,EAAE,mBAAmB,CAAC;EACjEH,MAAM,CAACQ,EAAE,CAACC,IAAI,GAAG,CAAC,EAAE,MAAM,sCAAsC,CAAC;EAEjE,MAAMC,MAAM,GAAiB;IAACP,CAAC,EAAEK;EAAE,CAAC;EACpC,MAAMG,KAAK,GAAgB;IAACP;EAAI,CAAC;EACjC,MAAM,CAACQ,MAAM,EAAEC,OAAO,CAAC,GAAGhB,MAAM,CAACiB,SAAS,CACtChB,MAAM,EAAEY,MAAmC,EAC3CC,KAAgC,CAAkB;EACtD,OAAO;IAACC,MAAM;IAAEC;EAAO,CAAC;AAC1B;AAEA,OAAO,MAAME,MAAM,GAAG,eAAgBd,EAAE,CAAC;EAACC;AAAO,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}