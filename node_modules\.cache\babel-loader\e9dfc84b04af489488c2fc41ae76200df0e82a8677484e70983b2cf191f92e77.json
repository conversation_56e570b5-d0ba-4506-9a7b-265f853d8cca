{"ast": null, "code": "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { Ceil } from '../kernel_names';\nimport { zerosLike } from '../ops/zeros_like';\nexport const ceilGradConfig = {\n  kernelName: Ceil,\n  gradFunc: dy => {\n    // TODO(manrajgrover): Return null for gradients when backprop supports it.\n    return {\n      x: () => zerosLike(dy)\n    };\n  }\n};", "map": {"version": 3, "names": ["Ceil", "zerosLike", "ceilGradConfig", "kernelName", "grad<PERSON>unc", "dy", "x"], "sources": ["C:\\tfjs-core\\src\\gradients\\Ceil_grad.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {Ceil} from '../kernel_names';\nimport {GradConfig} from '../kernel_registry';\nimport {zerosLike} from '../ops/zeros_like';\nimport {Tensor} from '../tensor';\n\nexport const ceilGradConfig: GradConfig = {\n  kernelName: Ceil,\n  gradFunc: (dy: Tensor) => {\n    // TODO(manrajgrover): Return null for gradients when backprop supports it.\n    return {x: () => zerosLike(dy)};\n  }\n};\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAiBA,SAAQA,IAAI,QAAO,iBAAiB;AAEpC,SAAQC,SAAS,QAAO,mBAAmB;AAG3C,OAAO,MAAMC,cAAc,GAAe;EACxCC,UAAU,EAAEH,IAAI;EAChBI,QAAQ,EAAGC,EAAU,IAAI;IACvB;IACA,OAAO;MAACC,CAAC,EAAEA,CAAA,KAAML,SAAS,CAACI,EAAE;IAAC,CAAC;EACjC;CACD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}