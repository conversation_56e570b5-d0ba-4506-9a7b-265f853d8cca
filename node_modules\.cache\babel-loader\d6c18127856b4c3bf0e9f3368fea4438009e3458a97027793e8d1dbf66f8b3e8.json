{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.transformReply = exports.transformArguments = exports.IS_READ_ONLY = void 0;\nconst SUGGET_1 = require(\"./SUGGET\");\nvar SUGGET_2 = require(\"./SUGGET\");\nObject.defineProperty(exports, \"IS_READ_ONLY\", {\n  enumerable: true,\n  get: function () {\n    return SUGGET_2.IS_READ_ONLY;\n  }\n});\nfunction transformArguments(key, prefix, options) {\n  return [...(0, SUGGET_1.transformArguments)(key, prefix, options), 'WITHSCORES'];\n}\nexports.transformArguments = transformArguments;\nfunction transformReply(rawReply) {\n  if (rawReply === null) return null;\n  const transformedReply = [];\n  for (let i = 0; i < rawReply.length; i += 2) {\n    transformedReply.push({\n      suggestion: rawReply[i],\n      score: Number(rawReply[i + 1])\n    });\n  }\n  return transformedReply;\n}\nexports.transformReply = transformReply;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "transformReply", "transformArguments", "IS_READ_ONLY", "SUGGET_1", "require", "SUGGET_2", "enumerable", "get", "key", "prefix", "options", "rawReply", "transformedReply", "i", "length", "push", "suggestion", "score", "Number"], "sources": ["C:/tmsft/node_modules/@redis/search/dist/commands/SUGGET_WITHSCORES.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.transformReply = exports.transformArguments = exports.IS_READ_ONLY = void 0;\nconst SUGGET_1 = require(\"./SUGGET\");\nvar SUGGET_2 = require(\"./SUGGET\");\nObject.defineProperty(exports, \"IS_READ_ONLY\", { enumerable: true, get: function () { return SUGGET_2.IS_READ_ONLY; } });\nfunction transformArguments(key, prefix, options) {\n    return [\n        ...(0, SUGGET_1.transformArguments)(key, prefix, options),\n        'WITHSCORES'\n    ];\n}\nexports.transformArguments = transformArguments;\nfunction transformReply(rawReply) {\n    if (rawReply === null)\n        return null;\n    const transformedReply = [];\n    for (let i = 0; i < rawReply.length; i += 2) {\n        transformedReply.push({\n            suggestion: rawReply[i],\n            score: Number(rawReply[i + 1])\n        });\n    }\n    return transformedReply;\n}\nexports.transformReply = transformReply;\n"], "mappings": "AAAA,YAAY;;AACZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEC,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7DD,OAAO,CAACE,cAAc,GAAGF,OAAO,CAACG,kBAAkB,GAAGH,OAAO,CAACI,YAAY,GAAG,KAAK,CAAC;AACnF,MAAMC,QAAQ,GAAGC,OAAO,CAAC,UAAU,CAAC;AACpC,IAAIC,QAAQ,GAAGD,OAAO,CAAC,UAAU,CAAC;AAClCR,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,cAAc,EAAE;EAAEQ,UAAU,EAAE,IAAI;EAAEC,GAAG,EAAE,SAAAA,CAAA,EAAY;IAAE,OAAOF,QAAQ,CAACH,YAAY;EAAE;AAAE,CAAC,CAAC;AACxH,SAASD,kBAAkBA,CAACO,GAAG,EAAEC,MAAM,EAAEC,OAAO,EAAE;EAC9C,OAAO,CACH,GAAG,CAAC,CAAC,EAAEP,QAAQ,CAACF,kBAAkB,EAAEO,GAAG,EAAEC,MAAM,EAAEC,OAAO,CAAC,EACzD,YAAY,CACf;AACL;AACAZ,OAAO,CAACG,kBAAkB,GAAGA,kBAAkB;AAC/C,SAASD,cAAcA,CAACW,QAAQ,EAAE;EAC9B,IAAIA,QAAQ,KAAK,IAAI,EACjB,OAAO,IAAI;EACf,MAAMC,gBAAgB,GAAG,EAAE;EAC3B,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,QAAQ,CAACG,MAAM,EAAED,CAAC,IAAI,CAAC,EAAE;IACzCD,gBAAgB,CAACG,IAAI,CAAC;MAClBC,UAAU,EAAEL,QAAQ,CAACE,CAAC,CAAC;MACvBI,KAAK,EAAEC,MAAM,CAACP,QAAQ,CAACE,CAAC,GAAG,CAAC,CAAC;IACjC,CAAC,CAAC;EACN;EACA,OAAOD,gBAAgB;AAC3B;AACAd,OAAO,CAACE,cAAc,GAAGA,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}