{"ast": null, "code": "/**\n * @license\n * Copyright 2018 Google LLC\n *\n * Use of this source code is governed by an MIT-style\n * license that can be found in the LICENSE file or at\n * https://opensource.org/licenses/MIT.\n * =============================================================================\n */\nimport './flags_layers';\nimport '@tensorflow/tfjs-core';\n// tslint:disable-next-line: no-imports-from-dist\nimport '@tensorflow/tfjs-core/dist/register_all_gradients';\n// This file lists all exports of TensorFlow.js Layers\nimport * as constraints from './exports_constraints';\nimport * as initializers from './exports_initializers';\nimport * as layers from './exports_layers';\nimport * as metrics from './exports_metrics';\nimport * as models from './exports_models';\nimport * as regularizers from './exports_regularizers';\nexport { CallbackList, CustomCallback, History } from './base_callbacks';\nexport { Callback, callbacks, EarlyStopping } from './callbacks';\nexport { InputSpec, SymbolicTensor } from './engine/topology';\nexport { LayersModel } from './engine/training';\nexport { input, loadLayersModel, model, registerCallbackConstructor, sequential } from './exports';\nexport { RNN } from './layers/recurrent';\nexport { Sequential } from './models';\nexport { LayerVariable } from './variables';\nexport { version as version_layers } from './version';\nexport { constraints, initializers, layers, metrics, models, regularizers };", "map": {"version": 3, "names": ["constraints", "initializers", "layers", "metrics", "models", "regularizers", "CallbackList", "CustomCallback", "History", "Callback", "callbacks", "EarlyStopping", "InputSpec", "SymbolicTensor", "LayersModel", "input", "loadLayersModel", "model", "registerCallbackConstructor", "sequential", "RNN", "Sequential", "LayerVariable", "version", "version_layers"], "sources": ["C:\\tfjs-layers\\src\\index.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2018 Google LLC\n *\n * Use of this source code is governed by an MIT-style\n * license that can be found in the LICENSE file or at\n * https://opensource.org/licenses/MIT.\n * =============================================================================\n */\n\nimport './flags_layers';\nimport '@tensorflow/tfjs-core';\n// tslint:disable-next-line: no-imports-from-dist\nimport '@tensorflow/tfjs-core/dist/register_all_gradients';\n\n// This file lists all exports of TensorFlow.js Layers\n\nimport * as constraints from './exports_constraints';\nimport * as initializers from './exports_initializers';\nimport * as layers from './exports_layers';\nimport * as metrics from './exports_metrics';\nimport * as models from './exports_models';\nimport * as regularizers from './exports_regularizers';\n\nexport {CallbackList, CustomCallback, CustomCallbackArgs, History} from './base_callbacks';\nexport {Callback, callbacks, EarlyStopping, EarlyStoppingCallbackArgs} from './callbacks';\nexport {InputSpec, SymbolicTensor} from './engine/topology';\nexport {LayersModel, ModelCompileArgs, ModelEvaluateArgs} from './engine/training';\nexport {ModelFitDatasetArgs} from './engine/training_dataset';\nexport {ModelFitArgs} from './engine/training_tensors';\nexport {ClassWeight, ClassWeightMap} from './engine/training_utils';\nexport {input, loadLayersModel, model, registerCallbackConstructor, sequential} from './exports';\nexport {Shape} from './keras_format/common';\nexport {GRUCellLayerArgs, GRULayerArgs, LSTMCellLayerArgs, LSTMLayerArgs, RNN, RNNLayerArgs, SimpleRNNCellLayerArgs, SimpleRNNLayerArgs} from './layers/recurrent';\nexport {Logs} from './logs';\nexport {ModelAndWeightsConfig, Sequential, SequentialArgs} from './models';\nexport {LayerVariable} from './variables';\nexport {version as version_layers} from './version';\nexport {constraints, initializers, layers, metrics, models, regularizers};\n"], "mappings": "AAAA;;;;;;;;;AAUA,OAAO,gBAAgB;AACvB,OAAO,uBAAuB;AAC9B;AACA,OAAO,mDAAmD;AAE1D;AAEA,OAAO,KAAKA,WAAW,MAAM,uBAAuB;AACpD,OAAO,KAAKC,YAAY,MAAM,wBAAwB;AACtD,OAAO,KAAKC,MAAM,MAAM,kBAAkB;AAC1C,OAAO,KAAKC,OAAO,MAAM,mBAAmB;AAC5C,OAAO,KAAKC,MAAM,MAAM,kBAAkB;AAC1C,OAAO,KAAKC,YAAY,MAAM,wBAAwB;AAEtD,SAAQC,YAAY,EAAEC,cAAc,EAAsBC,OAAO,QAAO,kBAAkB;AAC1F,SAAQC,QAAQ,EAAEC,SAAS,EAAEC,aAAa,QAAkC,aAAa;AACzF,SAAQC,SAAS,EAAEC,cAAc,QAAO,mBAAmB;AAC3D,SAAQC,WAAW,QAA4C,mBAAmB;AAIlF,SAAQC,KAAK,EAAEC,eAAe,EAAEC,KAAK,EAAEC,2BAA2B,EAAEC,UAAU,QAAO,WAAW;AAEhG,SAA0EC,GAAG,QAAiE,oBAAoB;AAElK,SAA+BC,UAAU,QAAuB,UAAU;AAC1E,SAAQC,aAAa,QAAO,aAAa;AACzC,SAAQC,OAAO,IAAIC,cAAc,QAAO,WAAW;AACnD,SAAQxB,WAAW,EAAEC,YAAY,EAAEC,MAAM,EAAEC,OAAO,EAAEC,MAAM,EAAEC,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}