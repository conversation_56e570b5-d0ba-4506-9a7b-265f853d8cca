{"ast": null, "code": "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { convertToTensor } from '../tensor_util_env';\nimport { parseAxisParam } from '../util';\nimport { add } from './add';\nimport { expandShapeToKeepDim } from './axis_util';\nimport { exp } from './exp';\nimport { log } from './log';\nimport { max } from './max';\nimport { op } from './operation';\nimport { reshape } from './reshape';\nimport { sub } from './sub';\nimport { sum } from './sum';\n/**\n * Computes the log(sum(exp(elements across the reduction dimensions))).\n *\n * Reduces the input along the dimensions given in `axis`. Unless `keepDims`\n * is true, the rank of the array is reduced by 1 for each entry in `axis`.\n * If `keepDims` is true, the reduced dimensions are retained with length 1.\n * If `axis` has no entries, all dimensions are reduced, and an array with a\n * single element is returned.\n *\n * ```js\n * const x = tf.tensor1d([1, 2, 3]);\n *\n * x.logSumExp().print();  // or tf.logSumExp(x)\n * ```\n *\n * ```js\n * const x = tf.tensor2d([1, 2, 3, 4], [2, 2]);\n *\n * const axis = 1;\n * x.logSumExp(axis).print();  // or tf.logSumExp(a, axis)\n * ```\n * @param x The input tensor.\n * @param axis The dimension(s) to reduce. If null (the default),\n *     reduces all dimensions.\n * @param keepDims If true, retains reduced dimensions with length\n *     of 1. Defaults to false.\n *\n * @doc {heading: 'Operations', subheading: 'Reduction'}\n */\nfunction logSumExp_(x, axis = null, keepDims = false) {\n  const $x = convertToTensor(x, 'x', 'logSumExp');\n  const axes = parseAxisParam(axis, $x.shape);\n  const xMax = max($x, axes, true /* keepDims */);\n  const a = sub($x, xMax);\n  const b = exp(a);\n  const c = sum(b, axes);\n  const d = log(c);\n  const res = add(reshape(xMax, d.shape), d);\n  if (keepDims) {\n    const newShape = expandShapeToKeepDim(res.shape, axes);\n    return reshape(res, newShape);\n  }\n  return res;\n}\nexport const logSumExp = /* @__PURE__ */op({\n  logSumExp_\n});", "map": {"version": 3, "names": ["convertToTensor", "parseAxisParam", "add", "expandShapeToKeepDim", "exp", "log", "max", "op", "reshape", "sub", "sum", "logSumExp_", "x", "axis", "keepDims", "$x", "axes", "shape", "xMax", "a", "b", "c", "d", "res", "newShape", "logSumExp"], "sources": ["C:\\tfjs-core\\src\\ops\\log_sum_exp.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {Tensor} from '../tensor';\nimport {convertToTensor} from '../tensor_util_env';\nimport {TensorLike} from '../types';\nimport {parseAxisParam} from '../util';\n\nimport {add} from './add';\nimport {expandShapeToKeepDim} from './axis_util';\nimport {exp} from './exp';\nimport {log} from './log';\nimport {max} from './max';\nimport {op} from './operation';\nimport {reshape} from './reshape';\nimport {sub} from './sub';\nimport {sum} from './sum';\n\n/**\n * Computes the log(sum(exp(elements across the reduction dimensions))).\n *\n * Reduces the input along the dimensions given in `axis`. Unless `keepDims`\n * is true, the rank of the array is reduced by 1 for each entry in `axis`.\n * If `keepDims` is true, the reduced dimensions are retained with length 1.\n * If `axis` has no entries, all dimensions are reduced, and an array with a\n * single element is returned.\n *\n * ```js\n * const x = tf.tensor1d([1, 2, 3]);\n *\n * x.logSumExp().print();  // or tf.logSumExp(x)\n * ```\n *\n * ```js\n * const x = tf.tensor2d([1, 2, 3, 4], [2, 2]);\n *\n * const axis = 1;\n * x.logSumExp(axis).print();  // or tf.logSumExp(a, axis)\n * ```\n * @param x The input tensor.\n * @param axis The dimension(s) to reduce. If null (the default),\n *     reduces all dimensions.\n * @param keepDims If true, retains reduced dimensions with length\n *     of 1. Defaults to false.\n *\n * @doc {heading: 'Operations', subheading: 'Reduction'}\n */\nfunction logSumExp_<T extends Tensor>(\n    x: Tensor|TensorLike, axis: number|number[] = null, keepDims = false): T {\n  const $x = convertToTensor(x, 'x', 'logSumExp');\n\n  const axes = parseAxisParam(axis, $x.shape);\n  const xMax = max($x, axes, true /* keepDims */);\n  const a = sub($x, xMax);\n  const b = exp(a);\n  const c = sum(b, axes);\n  const d = log(c);\n  const res = add(reshape(xMax, d.shape), d);\n\n  if (keepDims) {\n    const newShape = expandShapeToKeepDim(res.shape, axes);\n    return reshape(res, newShape) as T;\n  }\n  return res as T;\n}\n\nexport const logSumExp = /* @__PURE__ */ op({logSumExp_});\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAkBA,SAAQA,eAAe,QAAO,oBAAoB;AAElD,SAAQC,cAAc,QAAO,SAAS;AAEtC,SAAQC,GAAG,QAAO,OAAO;AACzB,SAAQC,oBAAoB,QAAO,aAAa;AAChD,SAAQC,GAAG,QAAO,OAAO;AACzB,SAAQC,GAAG,QAAO,OAAO;AACzB,SAAQC,GAAG,QAAO,OAAO;AACzB,SAAQC,EAAE,QAAO,aAAa;AAC9B,SAAQC,OAAO,QAAO,WAAW;AACjC,SAAQC,GAAG,QAAO,OAAO;AACzB,SAAQC,GAAG,QAAO,OAAO;AAEzB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6BA,SAASC,UAAUA,CACfC,CAAoB,EAAEC,IAAA,GAAwB,IAAI,EAAEC,QAAQ,GAAG,KAAK;EACtE,MAAMC,EAAE,GAAGf,eAAe,CAACY,CAAC,EAAE,GAAG,EAAE,WAAW,CAAC;EAE/C,MAAMI,IAAI,GAAGf,cAAc,CAACY,IAAI,EAAEE,EAAE,CAACE,KAAK,CAAC;EAC3C,MAAMC,IAAI,GAAGZ,GAAG,CAACS,EAAE,EAAEC,IAAI,EAAE,IAAI,CAAC,cAAc,CAAC;EAC/C,MAAMG,CAAC,GAAGV,GAAG,CAACM,EAAE,EAAEG,IAAI,CAAC;EACvB,MAAME,CAAC,GAAGhB,GAAG,CAACe,CAAC,CAAC;EAChB,MAAME,CAAC,GAAGX,GAAG,CAACU,CAAC,EAAEJ,IAAI,CAAC;EACtB,MAAMM,CAAC,GAAGjB,GAAG,CAACgB,CAAC,CAAC;EAChB,MAAME,GAAG,GAAGrB,GAAG,CAACM,OAAO,CAACU,IAAI,EAAEI,CAAC,CAACL,KAAK,CAAC,EAAEK,CAAC,CAAC;EAE1C,IAAIR,QAAQ,EAAE;IACZ,MAAMU,QAAQ,GAAGrB,oBAAoB,CAACoB,GAAG,CAACN,KAAK,EAAED,IAAI,CAAC;IACtD,OAAOR,OAAO,CAACe,GAAG,EAAEC,QAAQ,CAAM;;EAEpC,OAAOD,GAAQ;AACjB;AAEA,OAAO,MAAME,SAAS,GAAG,eAAgBlB,EAAE,CAAC;EAACI;AAAU,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}