{"ast": null, "code": "/*\nCopyright (c) 2014, <PERSON>\n\nPer<PERSON> is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in\nall copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\nTHE SOFTWARE.\n*/\n\n'use strict';\n\n// const EdgeWeightedDigraph = require('./edge_weighted_digraph')\nconst Topological = require('./topological');\n\n/**\n  *  The ShortestPathTree represents a data type for solving the\n  *  single-source shortest paths problem in edge-weighted directed\n  *  acyclic graphs (DAGs). The edge weights can be positive, negative, or zero.\n  *  This implementation uses a topological-sort based algorithm.\n  *  the distTo() and hasPathTo() methods take\n  *  constant time and the pathTo() method takes time proportional to the\n  *  number of edges in the longest path returned.\n  */\nclass ShortestPathTree {\n  constructor(digraph, start) {\n    const _this = this;\n    this.edgeTo = [];\n    this.distTo = [];\n    this.distTo[start] = 0.0;\n    this.start = start;\n    this.top = new Topological(digraph);\n    this.top.order().forEach(function (vertex) {\n      _this.relaxVertex(digraph, vertex, _this);\n    });\n  }\n  relaxEdge(e) {\n    const distTo = this.distTo;\n    const edgeTo = this.edgeTo;\n    const v = e.from();\n    const w = e.to();\n    if (distTo[w] > distTo[v] + e.weight) {\n      distTo[w] = distTo[v] + e.weight;\n      edgeTo[w] = e;\n    }\n  }\n\n  /**\n   * relax a vertex v in the specified digraph g\n   * @param {EdgeWeightedDigraph} the apecified digraph\n   * @param {Vertex} v vertex to be relaxed\n   */\n  relaxVertex(digraph, vertex, tree) {\n    const distTo = tree.distTo;\n    const edgeTo = tree.edgeTo;\n    digraph.getAdj(vertex).forEach(function (edge) {\n      const w = edge.to();\n      distTo[w] = /\\d/.test(distTo[w]) ? distTo[w] : Number.MAX_VALUE;\n      distTo[vertex] = distTo[vertex] || 0;\n      if (distTo[w] > distTo[vertex] + edge.weight) {\n        // in case of the result of 0.28+0.34 is 0.62000001\n        distTo[w] = parseFloat((distTo[vertex] + edge.weight).toFixed(2));\n        edgeTo[w] = edge;\n      }\n    });\n  }\n  getDistTo(v) {\n    return this.distTo[v];\n  }\n  hasPathTo(v) {\n    const dist = this.distTo[v];\n    if (v === this.start) return false;\n    return /\\d/.test(dist) ? dist !== Number.MAX_VALUE : false;\n  }\n  pathTo(v) {\n    if (!this.hasPathTo(v) || v === this.start) return [];\n    const path = [];\n    const edgeTo = this.edgeTo;\n    for (let e = edgeTo[v]; e; e = edgeTo[e.from()]) {\n      path.push(e.to());\n    }\n    path.push(this.start);\n    return path.reverse();\n  }\n}\nmodule.exports = ShortestPathTree;", "map": {"version": 3, "names": ["Topological", "require", "ShortestPathTree", "constructor", "digraph", "start", "_this", "edgeTo", "distTo", "top", "order", "for<PERSON>ach", "vertex", "relaxVertex", "relaxEdge", "e", "v", "from", "w", "to", "weight", "tree", "get<PERSON><PERSON><PERSON>", "edge", "test", "Number", "MAX_VALUE", "parseFloat", "toFixed", "getDistTo", "hasPathTo", "dist", "pathTo", "path", "push", "reverse", "module", "exports"], "sources": ["C:/tmsft/node_modules/natural/lib/natural/util/shortest_path_tree.js"], "sourcesContent": ["/*\nCopyright (c) 2014, <PERSON>\n\nPer<PERSON> is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in\nall copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\nTHE SOFTWARE.\n*/\n\n'use strict'\n\n// const EdgeWeightedDigraph = require('./edge_weighted_digraph')\nconst Topological = require('./topological')\n\n/**\n  *  The ShortestPathTree represents a data type for solving the\n  *  single-source shortest paths problem in edge-weighted directed\n  *  acyclic graphs (DAGs). The edge weights can be positive, negative, or zero.\n  *  This implementation uses a topological-sort based algorithm.\n  *  the distTo() and hasPathTo() methods take\n  *  constant time and the pathTo() method takes time proportional to the\n  *  number of edges in the longest path returned.\n  */\nclass ShortestPathTree {\n  constructor (digraph, start) {\n    const _this = this\n    this.edgeTo = []\n    this.distTo = []\n    this.distTo[start] = 0.0\n    this.start = start\n    this.top = new Topological(digraph)\n    this.top.order().forEach(function (vertex) {\n      _this.relaxVertex(digraph, vertex, _this)\n    })\n  }\n\n  relaxEdge (e) {\n    const distTo = this.distTo\n    const edgeTo = this.edgeTo\n    const v = e.from(); const w = e.to()\n    if (distTo[w] > distTo[v] + e.weight) {\n      distTo[w] = distTo[v] + e.weight\n      edgeTo[w] = e\n    }\n  }\n\n  /**\n   * relax a vertex v in the specified digraph g\n   * @param {EdgeWeightedDigraph} the apecified digraph\n   * @param {Vertex} v vertex to be relaxed\n   */\n  relaxVertex (digraph, vertex, tree) {\n    const distTo = tree.distTo\n    const edgeTo = tree.edgeTo\n    digraph.getAdj(vertex).forEach(function (edge) {\n      const w = edge.to()\n      distTo[w] = /\\d/.test(distTo[w]) ? distTo[w] : Number.MAX_VALUE\n      distTo[vertex] = distTo[vertex] || 0\n      if (distTo[w] > distTo[vertex] + edge.weight) {\n        // in case of the result of 0.28+0.34 is 0.62000001\n        distTo[w] = parseFloat((distTo[vertex] + edge.weight).toFixed(2))\n        edgeTo[w] = edge\n      }\n    })\n  }\n\n  getDistTo (v) {\n    return this.distTo[v]\n  }\n\n  hasPathTo (v) {\n    const dist = this.distTo[v]\n    if (v === this.start) return false\n    return /\\d/.test(dist) ? dist !== Number.MAX_VALUE : false\n  }\n\n  pathTo (v) {\n    if (!this.hasPathTo(v) || v === this.start) return []\n    const path = []\n    const edgeTo = this.edgeTo\n    for (let e = edgeTo[v]; e; e = edgeTo[e.from()]) {\n      path.push(e.to())\n    }\n    path.push(this.start)\n    return path.reverse()\n  }\n}\n\nmodule.exports = ShortestPathTree\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,YAAY;;AAEZ;AACA,MAAMA,WAAW,GAAGC,OAAO,CAAC,eAAe,CAAC;;AAE5C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,gBAAgB,CAAC;EACrBC,WAAWA,CAAEC,OAAO,EAAEC,KAAK,EAAE;IAC3B,MAAMC,KAAK,GAAG,IAAI;IAClB,IAAI,CAACC,MAAM,GAAG,EAAE;IAChB,IAAI,CAACC,MAAM,GAAG,EAAE;IAChB,IAAI,CAACA,MAAM,CAACH,KAAK,CAAC,GAAG,GAAG;IACxB,IAAI,CAACA,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACI,GAAG,GAAG,IAAIT,WAAW,CAACI,OAAO,CAAC;IACnC,IAAI,CAACK,GAAG,CAACC,KAAK,CAAC,CAAC,CAACC,OAAO,CAAC,UAAUC,MAAM,EAAE;MACzCN,KAAK,CAACO,WAAW,CAACT,OAAO,EAAEQ,MAAM,EAAEN,KAAK,CAAC;IAC3C,CAAC,CAAC;EACJ;EAEAQ,SAASA,CAAEC,CAAC,EAAE;IACZ,MAAMP,MAAM,GAAG,IAAI,CAACA,MAAM;IAC1B,MAAMD,MAAM,GAAG,IAAI,CAACA,MAAM;IAC1B,MAAMS,CAAC,GAAGD,CAAC,CAACE,IAAI,CAAC,CAAC;IAAE,MAAMC,CAAC,GAAGH,CAAC,CAACI,EAAE,CAAC,CAAC;IACpC,IAAIX,MAAM,CAACU,CAAC,CAAC,GAAGV,MAAM,CAACQ,CAAC,CAAC,GAAGD,CAAC,CAACK,MAAM,EAAE;MACpCZ,MAAM,CAACU,CAAC,CAAC,GAAGV,MAAM,CAACQ,CAAC,CAAC,GAAGD,CAAC,CAACK,MAAM;MAChCb,MAAM,CAACW,CAAC,CAAC,GAAGH,CAAC;IACf;EACF;;EAEA;AACF;AACA;AACA;AACA;EACEF,WAAWA,CAAET,OAAO,EAAEQ,MAAM,EAAES,IAAI,EAAE;IAClC,MAAMb,MAAM,GAAGa,IAAI,CAACb,MAAM;IAC1B,MAAMD,MAAM,GAAGc,IAAI,CAACd,MAAM;IAC1BH,OAAO,CAACkB,MAAM,CAACV,MAAM,CAAC,CAACD,OAAO,CAAC,UAAUY,IAAI,EAAE;MAC7C,MAAML,CAAC,GAAGK,IAAI,CAACJ,EAAE,CAAC,CAAC;MACnBX,MAAM,CAACU,CAAC,CAAC,GAAG,IAAI,CAACM,IAAI,CAAChB,MAAM,CAACU,CAAC,CAAC,CAAC,GAAGV,MAAM,CAACU,CAAC,CAAC,GAAGO,MAAM,CAACC,SAAS;MAC/DlB,MAAM,CAACI,MAAM,CAAC,GAAGJ,MAAM,CAACI,MAAM,CAAC,IAAI,CAAC;MACpC,IAAIJ,MAAM,CAACU,CAAC,CAAC,GAAGV,MAAM,CAACI,MAAM,CAAC,GAAGW,IAAI,CAACH,MAAM,EAAE;QAC5C;QACAZ,MAAM,CAACU,CAAC,CAAC,GAAGS,UAAU,CAAC,CAACnB,MAAM,CAACI,MAAM,CAAC,GAAGW,IAAI,CAACH,MAAM,EAAEQ,OAAO,CAAC,CAAC,CAAC,CAAC;QACjErB,MAAM,CAACW,CAAC,CAAC,GAAGK,IAAI;MAClB;IACF,CAAC,CAAC;EACJ;EAEAM,SAASA,CAAEb,CAAC,EAAE;IACZ,OAAO,IAAI,CAACR,MAAM,CAACQ,CAAC,CAAC;EACvB;EAEAc,SAASA,CAAEd,CAAC,EAAE;IACZ,MAAMe,IAAI,GAAG,IAAI,CAACvB,MAAM,CAACQ,CAAC,CAAC;IAC3B,IAAIA,CAAC,KAAK,IAAI,CAACX,KAAK,EAAE,OAAO,KAAK;IAClC,OAAO,IAAI,CAACmB,IAAI,CAACO,IAAI,CAAC,GAAGA,IAAI,KAAKN,MAAM,CAACC,SAAS,GAAG,KAAK;EAC5D;EAEAM,MAAMA,CAAEhB,CAAC,EAAE;IACT,IAAI,CAAC,IAAI,CAACc,SAAS,CAACd,CAAC,CAAC,IAAIA,CAAC,KAAK,IAAI,CAACX,KAAK,EAAE,OAAO,EAAE;IACrD,MAAM4B,IAAI,GAAG,EAAE;IACf,MAAM1B,MAAM,GAAG,IAAI,CAACA,MAAM;IAC1B,KAAK,IAAIQ,CAAC,GAAGR,MAAM,CAACS,CAAC,CAAC,EAAED,CAAC,EAAEA,CAAC,GAAGR,MAAM,CAACQ,CAAC,CAACE,IAAI,CAAC,CAAC,CAAC,EAAE;MAC/CgB,IAAI,CAACC,IAAI,CAACnB,CAAC,CAACI,EAAE,CAAC,CAAC,CAAC;IACnB;IACAc,IAAI,CAACC,IAAI,CAAC,IAAI,CAAC7B,KAAK,CAAC;IACrB,OAAO4B,IAAI,CAACE,OAAO,CAAC,CAAC;EACvB;AACF;AAEAC,MAAM,CAACC,OAAO,GAAGnC,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}