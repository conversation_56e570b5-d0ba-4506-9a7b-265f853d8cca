{"ast": null, "code": "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { Cos } from '@tensorflow/tfjs-core';\nimport { CHECK_NAN_SNIPPET_PACKED } from '../binaryop_packed_gpu';\nimport { CHECK_NAN_SNIPPET_UNARY, unaryKernelFunc } from '../kernel_utils/kernel_funcs_utils';\nconst COS = CHECK_NAN_SNIPPET_UNARY + \"\\n  return cos(x);\\n\";\nconst COS_PACKED = \"\\n  vec4 result = cos(x);\\n  bvec4 isNaN = isnan(x);\\n  \".concat(CHECK_NAN_SNIPPET_PACKED, \"\\n  return result;\\n\");\nexport const cos = unaryKernelFunc({\n  opSnippet: COS,\n  packedOpSnippet: COS_PACKED\n});\nexport const cosConfig = {\n  kernelName: Cos,\n  backendName: 'webgl',\n  kernelFunc: cos\n};", "map": {"version": 3, "names": ["Cos", "CHECK_NAN_SNIPPET_PACKED", "CHECK_NAN_SNIPPET_UNARY", "unaryKernelFunc", "COS", "COS_PACKED", "concat", "cos", "opSnippet", "packedOpSnippet", "cosConfig", "kernelName", "backendName", "kernelFunc"], "sources": ["C:\\tfjs-backend-webgl\\src\\kernels\\Cos.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {Cos, KernelConfig} from '@tensorflow/tfjs-core';\n\nimport {CHECK_NAN_SNIPPET_PACKED} from '../binaryop_packed_gpu';\nimport {CHECK_NAN_SNIPPET_UNARY, unaryKernelFunc} from '../kernel_utils/kernel_funcs_utils';\n\nconst COS = CHECK_NAN_SNIPPET_UNARY + `\n  return cos(x);\n`;\n\nconst COS_PACKED = `\n  vec4 result = cos(x);\n  bvec4 isNaN = isnan(x);\n  ${CHECK_NAN_SNIPPET_PACKED}\n  return result;\n`;\n\nexport const cos =\n    unaryKernelFunc({opSnippet: COS, packedOpSnippet: COS_PACKED});\n\nexport const cosConfig: KernelConfig = {\n  kernelName: Cos,\n  backendName: 'webgl',\n  kernelFunc: cos,\n};\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAiBA,SAAQA,GAAG,QAAqB,uBAAuB;AAEvD,SAAQC,wBAAwB,QAAO,wBAAwB;AAC/D,SAAQC,uBAAuB,EAAEC,eAAe,QAAO,oCAAoC;AAE3F,MAAMC,GAAG,GAAGF,uBAAuB,yBAElC;AAED,MAAMG,UAAU,8DAAAC,MAAA,CAGZL,wBAAwB,yBAE3B;AAED,OAAO,MAAMM,GAAG,GACZJ,eAAe,CAAC;EAACK,SAAS,EAAEJ,GAAG;EAAEK,eAAe,EAAEJ;AAAU,CAAC,CAAC;AAElE,OAAO,MAAMK,SAAS,GAAiB;EACrCC,UAAU,EAAEX,GAAG;EACfY,WAAW,EAAE,OAAO;EACpBC,UAAU,EAAEN;CACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}