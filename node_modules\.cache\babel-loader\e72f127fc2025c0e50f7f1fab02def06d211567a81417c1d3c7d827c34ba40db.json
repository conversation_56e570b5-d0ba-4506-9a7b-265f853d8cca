{"ast": null, "code": "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { Select, upcastType } from '@tensorflow/tfjs-core';\nimport { SelectProgram } from '../select_gpu';\nexport function select(args) {\n  const {\n    inputs,\n    backend\n  } = args;\n  const {\n    condition,\n    t,\n    e\n  } = inputs;\n  const program = new SelectProgram(condition.shape.length, t.shape, t.shape.length);\n  return backend.runWebGLProgram(program, [condition, t, e], upcastType(t.dtype, e.dtype));\n}\nexport const selectConfig = {\n  kernelName: Select,\n  backendName: 'webgl',\n  kernelFunc: select\n};", "map": {"version": 3, "names": ["Select", "upcastType", "SelectProgram", "select", "args", "inputs", "backend", "condition", "t", "e", "program", "shape", "length", "runWebGLProgram", "dtype", "selectConfig", "kernelName", "backendName", "kernelFunc"], "sources": ["C:\\tfjs-backend-webgl\\src\\kernels\\Select.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {KernelConfig, KernelFunc, Select, SelectInputs, TensorInfo, upcastType} from '@tensorflow/tfjs-core';\n\nimport {MathBackendWebGL} from '../backend_webgl';\nimport {SelectProgram} from '../select_gpu';\n\nexport function select(args: {inputs: SelectInputs, backend: MathBackendWebGL}):\n    TensorInfo {\n  const {inputs, backend} = args;\n  const {condition, t, e} = inputs;\n\n  const program =\n      new SelectProgram(condition.shape.length, t.shape, t.shape.length);\n  return backend.runWebGLProgram(\n      program, [condition, t, e], upcastType(t.dtype, e.dtype));\n}\n\nexport const selectConfig: KernelConfig = {\n  kernelName: Select,\n  backendName: 'webgl',\n  kernelFunc: select as unknown as KernelFunc\n};\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAiBA,SAAkCA,MAAM,EAA4BC,UAAU,QAAO,uBAAuB;AAG5G,SAAQC,aAAa,QAAO,eAAe;AAE3C,OAAM,SAAUC,MAAMA,CAACC,IAAuD;EAE5E,MAAM;IAACC,MAAM;IAAEC;EAAO,CAAC,GAAGF,IAAI;EAC9B,MAAM;IAACG,SAAS;IAAEC,CAAC;IAAEC;EAAC,CAAC,GAAGJ,MAAM;EAEhC,MAAMK,OAAO,GACT,IAAIR,aAAa,CAACK,SAAS,CAACI,KAAK,CAACC,MAAM,EAAEJ,CAAC,CAACG,KAAK,EAAEH,CAAC,CAACG,KAAK,CAACC,MAAM,CAAC;EACtE,OAAON,OAAO,CAACO,eAAe,CAC1BH,OAAO,EAAE,CAACH,SAAS,EAAEC,CAAC,EAAEC,CAAC,CAAC,EAAER,UAAU,CAACO,CAAC,CAACM,KAAK,EAAEL,CAAC,CAACK,KAAK,CAAC,CAAC;AAC/D;AAEA,OAAO,MAAMC,YAAY,GAAiB;EACxCC,UAAU,EAAEhB,MAAM;EAClBiB,WAAW,EAAE,OAAO;EACpBC,UAAU,EAAEf;CACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}