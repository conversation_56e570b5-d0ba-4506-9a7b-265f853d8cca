{"ast": null, "code": "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { notEqual } from '../../ops/not_equal';\nimport { getGlobalTensorClass } from '../../tensor';\ngetGlobalTensorClass().prototype.notEqual = function (b) {\n  this.throwIfDisposed();\n  return notEqual(this, b);\n};", "map": {"version": 3, "names": ["notEqual", "getGlobalTensorClass", "prototype", "b", "throwIfDisposed"], "sources": ["C:\\tfjs-core\\src\\public\\chained_ops\\not_equal.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport {notEqual} from '../../ops/not_equal';\nimport {getGlobalTensorClass, Tensor} from '../../tensor';\nimport {Rank, TensorLike} from '../../types';\n\ndeclare module '../../tensor' {\n  interface Tensor<R extends Rank = Rank> {\n    notEqual<T extends Tensor>(b: Tensor|TensorLike): T;\n  }\n}\n\ngetGlobalTensorClass().prototype.notEqual = function<T extends Tensor>(\n    b: Tensor|TensorLike): T {\n  this.throwIfDisposed();\n  return notEqual(this, b);\n};\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAgBA,SAAQA,QAAQ,QAAO,qBAAqB;AAC5C,SAAQC,oBAAoB,QAAe,cAAc;AASzDA,oBAAoB,EAAE,CAACC,SAAS,CAACF,QAAQ,GAAG,UACxCG,CAAoB;EACtB,IAAI,CAACC,eAAe,EAAE;EACtB,OAAOJ,QAAQ,CAAC,IAAI,EAAEG,CAAC,CAAC;AAC1B,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}