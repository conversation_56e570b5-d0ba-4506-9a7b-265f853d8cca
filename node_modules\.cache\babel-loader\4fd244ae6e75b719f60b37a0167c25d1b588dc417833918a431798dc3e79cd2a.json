{"ast": null, "code": "import React,{useState,useEffect,useCallback,useMemo}from'react';import{transactionStorageService}from'../services/transactionStorageService';import{categorizationService}from'../services/categorizationService';import{mlCategorizationService}from'../services/mlCategorizationService';import'./TransactionCategorization.css';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";export const TransactionCategorization=_ref=>{let{refreshTrigger}=_ref;// State management\nconst[transactions,setTransactions]=useState([]);const[categories,setCategories]=useState([]);const[categorizations,setCategorizations]=useState([]);const[loading,setLoading]=useState(true);const[processingML,setProcessingML]=useState(false);const[mlProgress,setMLProgress]=useState({current:0,total:0});const[selectedTransactions,setSelectedTransactions]=useState(new Set());// View and filter state\nconst[viewMode,setViewMode]=useState('uncategorized');const[sortField,setSortField]=useState('date');const[sortDirection,setSortDirection]=useState('desc');const[categoryFilter]=useState({categoryId:'',method:'all',confidenceMin:0,confidenceMax:1});// ML Configuration state\nconst[mlConfig]=useState({confidenceThreshold:0.7,batchSize:10,autoApplyHighConfidence:true});// Modal state\nconst[,setShowMLConfigModal]=useState(false);const[,setShowCategoryModal]=useState(false);// Load data\nconst loadData=useCallback(async()=>{try{setLoading(true);// Load all data\nconst[allTransactions,allCategories,allCategorizations]=await Promise.all([Promise.resolve(transactionStorageService.getAllTransactions()),Promise.resolve(categorizationService.getAllCategories()),Promise.resolve(categorizationService.getAllCategorizations())]);setTransactions(allTransactions);setCategories(allCategories);setCategorizations(allCategorizations);}catch(error){console.error('Failed to load categorization data:',error);}finally{setLoading(false);}},[]);useEffect(()=>{loadData();},[loadData,refreshTrigger]);// Get categorization data for a transaction\nconst getTransactionCategorization=useCallback(transactionId=>{return categorizations.find(cat=>cat.transactionId===transactionId);},[categorizations]);// Get category by ID\nconst getCategoryById=useCallback(categoryId=>{return categories.find(cat=>cat.id===categoryId);},[categories]);// Filter and sort transactions based on current settings\nconst filteredTransactions=useMemo(()=>{let filtered=[...transactions];// Apply view mode filter\nswitch(viewMode){case'uncategorized':filtered=filtered.filter(t=>!getTransactionCategorization(t.id));break;case'ml-pending':filtered=filtered.filter(t=>{const cat=getTransactionCategorization(t.id);return cat&&cat.method==='ml'&&(cat.confidence||0)<mlConfig.confidenceThreshold;});break;case'low-confidence':filtered=filtered.filter(t=>{const cat=getTransactionCategorization(t.id);return cat&&cat.method==='ml'&&(cat.confidence||0)<0.8;});break;case'all':default:// No additional filtering\nbreak;}// Apply category filter\nif(categoryFilter.categoryId){filtered=filtered.filter(t=>{const cat=getTransactionCategorization(t.id);return cat&&cat.categoryId===categoryFilter.categoryId;});}// Apply method filter\nif(categoryFilter.method!=='all'){filtered=filtered.filter(t=>{const cat=getTransactionCategorization(t.id);return cat&&cat.method===categoryFilter.method;});}// Apply confidence filter\nfiltered=filtered.filter(t=>{const cat=getTransactionCategorization(t.id);if(!cat||cat.method!=='ml')return true;// Include non-ML transactions\nconst confidence=cat.confidence||0;return confidence>=categoryFilter.confidenceMin&&confidence<=categoryFilter.confidenceMax;});// Sort transactions\nfiltered.sort((a,b)=>{var _getTransactionCatego,_getTransactionCatego2;let aValue;let bValue;switch(sortField){case'date':aValue=new Date(a.postDateTime).getTime();bValue=new Date(b.postDateTime).getTime();break;case'amount':aValue=Math.abs(a.debitAmount||a.creditAmount||0);bValue=Math.abs(b.debitAmount||b.creditAmount||0);break;case'confidence':aValue=((_getTransactionCatego=getTransactionCategorization(a.id))===null||_getTransactionCatego===void 0?void 0:_getTransactionCatego.confidence)||0;bValue=((_getTransactionCatego2=getTransactionCategorization(b.id))===null||_getTransactionCatego2===void 0?void 0:_getTransactionCatego2.confidence)||0;break;case'description':default:aValue=a.description.toLowerCase();bValue=b.description.toLowerCase();break;}if(aValue<bValue)return sortDirection==='asc'?-1:1;if(aValue>bValue)return sortDirection==='asc'?1:-1;return 0;});return filtered;},[transactions,viewMode,categoryFilter,sortField,sortDirection,mlConfig.confidenceThreshold,getTransactionCategorization]);// Handle manual categorization\nconst handleManualCategorization=useCallback(async(transactionId,categoryId)=>{try{categorizationService.categorizeTransaction(transactionId,categoryId,'manual');// Refresh categorizations\nconst updatedCategorizations=categorizationService.getAllCategorizations();setCategorizations(updatedCategorizations);// Clear selection\nsetSelectedTransactions(prev=>{const newSet=new Set(prev);newSet.delete(transactionId);return newSet;});}catch(error){console.error('Failed to categorize transaction:',error);}},[]);// Handle batch ML categorization\nconst handleMLCategorization=useCallback(async transactionIds=>{try{setProcessingML(true);const transactionsToProcess=transactionIds?transactions.filter(t=>transactionIds.includes(t.id)):filteredTransactions.filter(t=>!getTransactionCategorization(t.id));setMLProgress({current:0,total:transactionsToProcess.length});// Convert StoredTransaction to Transaction format for ML service\nconst mlTransactions=transactionsToProcess.map(t=>({id:t.id,date:t.postDateTime,description:t.description,debitAmount:t.debitAmount||0,creditAmount:t.creditAmount||0,balance:t.balance,reference:t.reference}));const results=await mlCategorizationService.categorizeTransactionsBatch(mlTransactions);// Update progress and categorizations\nlet processed=0;for(const result of results){processed++;setMLProgress({current:processed,total:transactionsToProcess.length});if(result.result&&result.result.confidence>=mlConfig.confidenceThreshold&&mlConfig.autoApplyHighConfidence){// High confidence results are automatically applied\ncontinue;// Already applied by ML service\n}}// Refresh categorizations\nconst updatedCategorizations=categorizationService.getAllCategorizations();setCategorizations(updatedCategorizations);}catch(error){console.error('ML categorization failed:',error);}finally{setProcessingML(false);setMLProgress({current:0,total:0});}},[transactions,filteredTransactions,mlConfig,getTransactionCategorization]);// Format currency\nconst formatCurrency=useCallback(amount=>{return new Intl.NumberFormat('en-US',{style:'currency',currency:'USD',minimumFractionDigits:2,maximumFractionDigits:2}).format(amount);},[]);// Format confidence as percentage\nconst formatConfidence=useCallback(confidence=>{if(confidence===undefined)return'N/A';return\"\".concat(Math.round(confidence*100),\"%\");},[]);// Get confidence color\nconst getConfidenceColor=useCallback(confidence=>{if(confidence===undefined)return'#6B7280';if(confidence>=0.9)return'#10B981';if(confidence>=0.7)return'#F59E0B';return'#EF4444';},[]);// Statistics\nconst stats=useMemo(()=>{const total=transactions.length;const categorized=categorizations.length;const uncategorized=total-categorized;const mlCategorized=categorizations.filter(c=>c.method==='ml').length;const manualCategorized=categorizations.filter(c=>c.method==='manual').length;const averageMLConfidence=categorizations.filter(c=>c.method==='ml'&&c.confidence!==undefined).reduce((sum,c,_,arr)=>sum+(c.confidence||0)/arr.length,0);return{total,categorized,uncategorized,mlCategorized,manualCategorized,averageMLConfidence,categorizationRate:total>0?categorized/total*100:0};},[transactions,categorizations]);if(loading){return/*#__PURE__*/_jsxs(\"div\",{className:\"categorization-loading\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"loading-spinner\"}),/*#__PURE__*/_jsx(\"p\",{children:\"Loading transaction categorization data...\"})]});}return/*#__PURE__*/_jsxs(\"div\",{className:\"transaction-categorization\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"categorization-header\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"categorization-title\",children:[/*#__PURE__*/_jsx(\"h2\",{children:\"Transaction Categorization\"}),/*#__PURE__*/_jsx(\"p\",{children:\"Categorize transactions manually or use AI-powered categorization with Qwen 3\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"categorization-stats\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"stat-card\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"stat-value\",children:stats.total}),/*#__PURE__*/_jsx(\"div\",{className:\"stat-label\",children:\"Total Transactions\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"stat-card\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"stat-value\",children:stats.categorized}),/*#__PURE__*/_jsx(\"div\",{className:\"stat-label\",children:\"Categorized\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"stat-card\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"stat-value\",children:stats.uncategorized}),/*#__PURE__*/_jsx(\"div\",{className:\"stat-label\",children:\"Uncategorized\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"stat-card\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"stat-value\",children:[stats.categorizationRate.toFixed(1),\"%\"]}),/*#__PURE__*/_jsx(\"div\",{className:\"stat-label\",children:\"Completion Rate\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"stat-card\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"stat-value\",children:formatConfidence(stats.averageMLConfidence)}),/*#__PURE__*/_jsx(\"div\",{className:\"stat-label\",children:\"Avg ML Confidence\"})]})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"categorization-controls\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"view-modes\",children:[/*#__PURE__*/_jsxs(\"button\",{className:\"view-mode-btn \".concat(viewMode==='uncategorized'?'active':''),onClick:()=>setViewMode('uncategorized'),children:[\"Uncategorized (\",stats.uncategorized,\")\"]}),/*#__PURE__*/_jsx(\"button\",{className:\"view-mode-btn \".concat(viewMode==='all'?'active':''),onClick:()=>setViewMode('all'),children:\"All Transactions\"}),/*#__PURE__*/_jsx(\"button\",{className:\"view-mode-btn \".concat(viewMode==='ml-pending'?'active':''),onClick:()=>setViewMode('ml-pending'),children:\"ML Pending Review\"}),/*#__PURE__*/_jsx(\"button\",{className:\"view-mode-btn \".concat(viewMode==='low-confidence'?'active':''),onClick:()=>setViewMode('low-confidence'),children:\"Low Confidence\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"action-buttons\",children:[/*#__PURE__*/_jsx(\"button\",{className:\"btn btn-primary\",onClick:()=>handleMLCategorization(),disabled:processingML,children:processingML?\"Processing \".concat(mlProgress.current,\"/\").concat(mlProgress.total,\"...\"):'Run ML Categorization'}),/*#__PURE__*/_jsx(\"button\",{className:\"btn btn-secondary\",onClick:()=>setShowMLConfigModal(true),children:\"ML Settings\"}),/*#__PURE__*/_jsx(\"button\",{className:\"btn btn-secondary\",onClick:()=>setShowCategoryModal(true),children:\"Manage Categories\"})]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"transactions-table-container\",children:/*#__PURE__*/_jsxs(\"table\",{className:\"transactions-table\",children:[/*#__PURE__*/_jsx(\"thead\",{children:/*#__PURE__*/_jsxs(\"tr\",{children:[/*#__PURE__*/_jsx(\"th\",{children:/*#__PURE__*/_jsx(\"input\",{type:\"checkbox\",onChange:e=>{if(e.target.checked){setSelectedTransactions(new Set(filteredTransactions.map(t=>t.id)));}else{setSelectedTransactions(new Set());}}})}),/*#__PURE__*/_jsx(\"th\",{className:\"sortable \".concat(sortField==='date'?sortDirection:''),onClick:()=>{if(sortField==='date'){setSortDirection(sortDirection==='asc'?'desc':'asc');}else{setSortField('date');setSortDirection('desc');}},children:\"Date\"}),/*#__PURE__*/_jsx(\"th\",{className:\"sortable \".concat(sortField==='description'?sortDirection:''),onClick:()=>{if(sortField==='description'){setSortDirection(sortDirection==='asc'?'desc':'asc');}else{setSortField('description');setSortDirection('asc');}},children:\"Description\"}),/*#__PURE__*/_jsx(\"th\",{className:\"sortable \".concat(sortField==='amount'?sortDirection:''),onClick:()=>{if(sortField==='amount'){setSortDirection(sortDirection==='asc'?'desc':'asc');}else{setSortField('amount');setSortDirection('desc');}},children:\"Amount\"}),/*#__PURE__*/_jsx(\"th\",{children:\"Category\"}),/*#__PURE__*/_jsx(\"th\",{className:\"sortable \".concat(sortField==='confidence'?sortDirection:''),onClick:()=>{if(sortField==='confidence'){setSortDirection(sortDirection==='asc'?'desc':'asc');}else{setSortField('confidence');setSortDirection('desc');}},children:\"Confidence\"}),/*#__PURE__*/_jsx(\"th\",{children:\"Actions\"})]})}),/*#__PURE__*/_jsx(\"tbody\",{children:filteredTransactions.map(transaction=>{const categorization=getTransactionCategorization(transaction.id);const category=categorization?getCategoryById(categorization.categoryId):undefined;const amount=transaction.debitAmount?-transaction.debitAmount:transaction.creditAmount||0;return/*#__PURE__*/_jsxs(\"tr\",{className:selectedTransactions.has(transaction.id)?'selected':'',children:[/*#__PURE__*/_jsx(\"td\",{children:/*#__PURE__*/_jsx(\"input\",{type:\"checkbox\",checked:selectedTransactions.has(transaction.id),onChange:e=>{const newSet=new Set(selectedTransactions);if(e.target.checked){newSet.add(transaction.id);}else{newSet.delete(transaction.id);}setSelectedTransactions(newSet);}})}),/*#__PURE__*/_jsx(\"td\",{children:new Date(transaction.postDateTime).toLocaleDateString()}),/*#__PURE__*/_jsx(\"td\",{className:\"description-cell\",title:transaction.description,children:transaction.description}),/*#__PURE__*/_jsx(\"td\",{className:\"amount-cell \".concat(amount<0?'debit':'credit'),children:formatCurrency(Math.abs(amount))}),/*#__PURE__*/_jsx(\"td\",{children:category?/*#__PURE__*/_jsxs(\"div\",{className:\"category-tag\",style:{backgroundColor:category.color},children:[category.name,(categorization===null||categorization===void 0?void 0:categorization.method)&&/*#__PURE__*/_jsx(\"span\",{className:\"category-method\",children:categorization.method.toUpperCase()})]}):/*#__PURE__*/_jsx(\"span\",{className:\"uncategorized\",children:\"Uncategorized\"})}),/*#__PURE__*/_jsx(\"td\",{children:(categorization===null||categorization===void 0?void 0:categorization.confidence)!==undefined?/*#__PURE__*/_jsx(\"span\",{className:\"confidence-score\",style:{color:getConfidenceColor(categorization.confidence)},children:formatConfidence(categorization.confidence)}):/*#__PURE__*/_jsx(\"span\",{className:\"no-confidence\",children:\"-\"})}),/*#__PURE__*/_jsx(\"td\",{children:/*#__PURE__*/_jsx(\"div\",{className:\"action-buttons\",children:/*#__PURE__*/_jsxs(\"select\",{value:(categorization===null||categorization===void 0?void 0:categorization.categoryId)||'',onChange:e=>{if(e.target.value){handleManualCategorization(transaction.id,e.target.value);}},className:\"category-select\",children:[/*#__PURE__*/_jsx(\"option\",{value:\"\",children:\"Select Category...\"}),categories.map(cat=>/*#__PURE__*/_jsx(\"option\",{value:cat.id,children:cat.name},cat.id))]})})})]},transaction.id);})})]})}),selectedTransactions.size>0&&/*#__PURE__*/_jsxs(\"div\",{className:\"batch-actions\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"batch-info\",children:[selectedTransactions.size,\" transaction(s) selected\"]}),/*#__PURE__*/_jsx(\"button\",{className:\"btn btn-primary\",onClick:()=>handleMLCategorization(Array.from(selectedTransactions)),disabled:processingML,children:\"Categorize Selected with ML\"}),/*#__PURE__*/_jsx(\"button\",{className:\"btn btn-secondary\",onClick:()=>setSelectedTransactions(new Set()),children:\"Clear Selection\"})]})]});};", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useCallback", "useMemo", "transactionStorageService", "categorizationService", "mlCategorizationService", "jsx", "_jsx", "jsxs", "_jsxs", "TransactionCategorization", "_ref", "refreshTrigger", "transactions", "setTransactions", "categories", "setCategories", "categorizations", "setCategorizations", "loading", "setLoading", "processingML", "setProcessingML", "mlProgress", "setMLProgress", "current", "total", "selectedTransactions", "setSelectedTransactions", "Set", "viewMode", "setViewMode", "sortField", "setSortField", "sortDirection", "setSortDirection", "categoryFilter", "categoryId", "method", "confidenceMin", "confidenceMax", "mlConfig", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "batchSize", "autoApplyHighConfidence", "setShowMLConfigModal", "setShowCategoryModal", "loadData", "allTransactions", "allCategories", "allCategorizations", "Promise", "all", "resolve", "getAllTransactions", "getAllCategories", "getAllCategorizations", "error", "console", "getTransactionCategorization", "transactionId", "find", "cat", "getCategoryById", "id", "filteredTransactions", "filtered", "filter", "t", "confidence", "sort", "a", "b", "_getTransactionCatego", "_getTransactionCatego2", "aValue", "bValue", "Date", "postDateTime", "getTime", "Math", "abs", "debitAmount", "creditAmount", "description", "toLowerCase", "handleManualCategorization", "categorizeTransaction", "updatedCategorizations", "prev", "newSet", "delete", "handleMLCategorization", "transactionIds", "transactionsToProcess", "includes", "length", "mlTransactions", "map", "date", "balance", "reference", "results", "categorizeTransactionsBatch", "processed", "result", "formatCurrency", "amount", "Intl", "NumberFormat", "style", "currency", "minimumFractionDigits", "maximumFractionDigits", "format", "formatConfidence", "undefined", "concat", "round", "getConfidenceColor", "stats", "categorized", "uncategorized", "mlCategorized", "c", "manualCategorized", "averageMLConfidence", "reduce", "sum", "_", "arr", "categorizationRate", "className", "children", "toFixed", "onClick", "disabled", "type", "onChange", "e", "target", "checked", "transaction", "categorization", "category", "has", "add", "toLocaleDateString", "title", "backgroundColor", "color", "name", "toUpperCase", "value", "size", "Array", "from"], "sources": ["C:/tmsft/src/components/TransactionCategorization.tsx"], "sourcesContent": ["import React, { useState, useEffect, useCallback, useMemo } from 'react';\r\nimport { Transaction, TransactionCategory, TransactionCategorization as TransactionCategorizationData } from '../types';\r\nimport { transactionStorageService, type StoredTransaction } from '../services/transactionStorageService';\r\nimport { categorizationService } from '../services/categorizationService';\r\nimport { mlCategorizationService } from '../services/mlCategorizationService';\r\nimport './TransactionCategorization.css';\r\n\r\ninterface TransactionCategorizationProps {\r\n  refreshTrigger?: number;\r\n}\r\n\r\ntype ViewMode = 'uncategorized' | 'all' | 'ml-pending' | 'low-confidence';\r\ntype SortField = 'date' | 'amount' | 'confidence' | 'description';\r\ntype SortDirection = 'asc' | 'desc';\r\n\r\ninterface CategoryFilter {\r\n  categoryId: string;\r\n  method: 'all' | 'manual' | 'ml' | 'rule';\r\n  confidenceMin: number;\r\n  confidenceMax: number;\r\n}\r\n\r\nexport const TransactionCategorization: React.FC<TransactionCategorizationProps> = ({ refreshTrigger }) => {\r\n  // State management\r\n  const [transactions, setTransactions] = useState<StoredTransaction[]>([]);\r\n  const [categories, setCategories] = useState<TransactionCategory[]>([]);\r\n  const [categorizations, setCategorizations] = useState<TransactionCategorizationData[]>([]);\r\n  const [loading, setLoading] = useState(true);\r\n  const [processingML, setProcessingML] = useState(false);\r\n  const [mlProgress, setMLProgress] = useState({ current: 0, total: 0 });\r\n  const [selectedTransactions, setSelectedTransactions] = useState<Set<string>>(new Set());\r\n  \r\n  // View and filter state\r\n  const [viewMode, setViewMode] = useState<ViewMode>('uncategorized');\r\n  const [sortField, setSortField] = useState<SortField>('date');\r\n  const [sortDirection, setSortDirection] = useState<SortDirection>('desc');\r\n  const [categoryFilter] = useState<CategoryFilter>({\r\n    categoryId: '',\r\n    method: 'all',\r\n    confidenceMin: 0,\r\n    confidenceMax: 1\r\n  });\r\n\r\n  // ML Configuration state\r\n  const [mlConfig] = useState({\r\n    confidenceThreshold: 0.7,\r\n    batchSize: 10,\r\n    autoApplyHighConfidence: true\r\n  });\r\n\r\n  // Modal state\r\n  const [, setShowMLConfigModal] = useState(false);\r\n  const [, setShowCategoryModal] = useState(false);\r\n\r\n  // Load data\r\n  const loadData = useCallback(async () => {\r\n    try {\r\n      setLoading(true);\r\n      \r\n      // Load all data\r\n      const [allTransactions, allCategories, allCategorizations] = await Promise.all([\r\n        Promise.resolve(transactionStorageService.getAllTransactions()),\r\n        Promise.resolve(categorizationService.getAllCategories()),\r\n        Promise.resolve(categorizationService.getAllCategorizations())\r\n      ]);\r\n\r\n      setTransactions(allTransactions);\r\n      setCategories(allCategories);\r\n      setCategorizations(allCategorizations);\r\n    } catch (error) {\r\n      console.error('Failed to load categorization data:', error);\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  }, []);\r\n\r\n  useEffect(() => {\r\n    loadData();\r\n  }, [loadData, refreshTrigger]);\r\n\r\n  // Get categorization data for a transaction\r\n  const getTransactionCategorization = useCallback((transactionId: string): TransactionCategorizationData | undefined => {\r\n    return categorizations.find(cat => cat.transactionId === transactionId);\r\n  }, [categorizations]);\r\n\r\n  // Get category by ID\r\n  const getCategoryById = useCallback((categoryId: string): TransactionCategory | undefined => {\r\n    return categories.find(cat => cat.id === categoryId);\r\n  }, [categories]);\r\n\r\n  // Filter and sort transactions based on current settings\r\n  const filteredTransactions = useMemo(() => {\r\n    let filtered = [...transactions];\r\n\r\n    // Apply view mode filter\r\n    switch (viewMode) {\r\n      case 'uncategorized':\r\n        filtered = filtered.filter(t => !getTransactionCategorization(t.id));\r\n        break;\r\n      case 'ml-pending':\r\n        filtered = filtered.filter(t => {\r\n          const cat = getTransactionCategorization(t.id);\r\n          return cat && cat.method === 'ml' && (cat.confidence || 0) < mlConfig.confidenceThreshold;\r\n        });\r\n        break;\r\n      case 'low-confidence':\r\n        filtered = filtered.filter(t => {\r\n          const cat = getTransactionCategorization(t.id);\r\n          return cat && cat.method === 'ml' && (cat.confidence || 0) < 0.8;\r\n        });\r\n        break;\r\n      case 'all':\r\n      default:\r\n        // No additional filtering\r\n        break;\r\n    }\r\n\r\n    // Apply category filter\r\n    if (categoryFilter.categoryId) {\r\n      filtered = filtered.filter(t => {\r\n        const cat = getTransactionCategorization(t.id);\r\n        return cat && cat.categoryId === categoryFilter.categoryId;\r\n      });\r\n    }\r\n\r\n    // Apply method filter\r\n    if (categoryFilter.method !== 'all') {\r\n      filtered = filtered.filter(t => {\r\n        const cat = getTransactionCategorization(t.id);\r\n        return cat && cat.method === categoryFilter.method;\r\n      });\r\n    }\r\n\r\n    // Apply confidence filter\r\n    filtered = filtered.filter(t => {\r\n      const cat = getTransactionCategorization(t.id);\r\n      if (!cat || cat.method !== 'ml') return true; // Include non-ML transactions\r\n      const confidence = cat.confidence || 0;\r\n      return confidence >= categoryFilter.confidenceMin && confidence <= categoryFilter.confidenceMax;\r\n    });\r\n\r\n    // Sort transactions\r\n    filtered.sort((a, b) => {\r\n      let aValue: string | number;\r\n      let bValue: string | number;\r\n\r\n      switch (sortField) {\r\n        case 'date':\r\n          aValue = new Date(a.postDateTime).getTime();\r\n          bValue = new Date(b.postDateTime).getTime();\r\n          break;\r\n        case 'amount':\r\n          aValue = Math.abs(a.debitAmount || a.creditAmount || 0);\r\n          bValue = Math.abs(b.debitAmount || b.creditAmount || 0);\r\n          break;\r\n        case 'confidence':\r\n          aValue = getTransactionCategorization(a.id)?.confidence || 0;\r\n          bValue = getTransactionCategorization(b.id)?.confidence || 0;\r\n          break;\r\n        case 'description':\r\n        default:\r\n          aValue = a.description.toLowerCase();\r\n          bValue = b.description.toLowerCase();\r\n          break;\r\n      }\r\n\r\n      if (aValue < bValue) return sortDirection === 'asc' ? -1 : 1;\r\n      if (aValue > bValue) return sortDirection === 'asc' ? 1 : -1;\r\n      return 0;\r\n    });\r\n\r\n    return filtered;\r\n  }, [transactions, viewMode, categoryFilter, sortField, sortDirection, mlConfig.confidenceThreshold, getTransactionCategorization]);\r\n\r\n  // Handle manual categorization\r\n  const handleManualCategorization = useCallback(async (transactionId: string, categoryId: string) => {\r\n    try {\r\n      categorizationService.categorizeTransaction(transactionId, categoryId, 'manual');\r\n      \r\n      // Refresh categorizations\r\n      const updatedCategorizations = categorizationService.getAllCategorizations();\r\n      setCategorizations(updatedCategorizations);\r\n      \r\n      // Clear selection\r\n      setSelectedTransactions(prev => {\r\n        const newSet = new Set(prev);\r\n        newSet.delete(transactionId);\r\n        return newSet;\r\n      });\r\n    } catch (error) {\r\n      console.error('Failed to categorize transaction:', error);\r\n    }\r\n  }, []);\r\n\r\n  // Handle batch ML categorization\r\n  const handleMLCategorization = useCallback(async (transactionIds?: string[]) => {\r\n    try {\r\n      setProcessingML(true);\r\n      \r\n      const transactionsToProcess = transactionIds \r\n        ? transactions.filter(t => transactionIds.includes(t.id))\r\n        : filteredTransactions.filter(t => !getTransactionCategorization(t.id));\r\n\r\n      setMLProgress({ current: 0, total: transactionsToProcess.length });\r\n\r\n      // Convert StoredTransaction to Transaction format for ML service\r\n      const mlTransactions: Transaction[] = transactionsToProcess.map(t => ({\r\n        id: t.id,\r\n        date: t.postDateTime,\r\n        description: t.description,\r\n        debitAmount: t.debitAmount || 0,\r\n        creditAmount: t.creditAmount || 0,\r\n        balance: t.balance,\r\n        reference: t.reference\r\n      }));\r\n\r\n      const results = await mlCategorizationService.categorizeTransactionsBatch(mlTransactions);\r\n      \r\n      // Update progress and categorizations\r\n      let processed = 0;\r\n      for (const result of results) {\r\n        processed++;\r\n        setMLProgress({ current: processed, total: transactionsToProcess.length });\r\n        \r\n        if (result.result && result.result.confidence >= mlConfig.confidenceThreshold && mlConfig.autoApplyHighConfidence) {\r\n          // High confidence results are automatically applied\r\n          continue; // Already applied by ML service\r\n        }\r\n      }\r\n\r\n      // Refresh categorizations\r\n      const updatedCategorizations = categorizationService.getAllCategorizations();\r\n      setCategorizations(updatedCategorizations);\r\n      \r\n    } catch (error) {\r\n      console.error('ML categorization failed:', error);\r\n    } finally {\r\n      setProcessingML(false);\r\n      setMLProgress({ current: 0, total: 0 });\r\n    }\r\n  }, [transactions, filteredTransactions, mlConfig, getTransactionCategorization]);\r\n\r\n  // Format currency\r\n  const formatCurrency = useCallback((amount: number): string => {\r\n    return new Intl.NumberFormat('en-US', {\r\n      style: 'currency',\r\n      currency: 'USD',\r\n      minimumFractionDigits: 2,\r\n      maximumFractionDigits: 2\r\n    }).format(amount);\r\n  }, []);\r\n\r\n  // Format confidence as percentage\r\n  const formatConfidence = useCallback((confidence?: number): string => {\r\n    if (confidence === undefined) return 'N/A';\r\n    return `${Math.round(confidence * 100)}%`;\r\n  }, []);\r\n\r\n  // Get confidence color\r\n  const getConfidenceColor = useCallback((confidence?: number): string => {\r\n    if (confidence === undefined) return '#6B7280';\r\n    if (confidence >= 0.9) return '#10B981';\r\n    if (confidence >= 0.7) return '#F59E0B';\r\n    return '#EF4444';\r\n  }, []);\r\n\r\n  // Statistics\r\n  const stats = useMemo(() => {\r\n    const total = transactions.length;\r\n    const categorized = categorizations.length;\r\n    const uncategorized = total - categorized;\r\n    const mlCategorized = categorizations.filter(c => c.method === 'ml').length;\r\n    const manualCategorized = categorizations.filter(c => c.method === 'manual').length;\r\n    const averageMLConfidence = categorizations\r\n      .filter(c => c.method === 'ml' && c.confidence !== undefined)\r\n      .reduce((sum, c, _, arr) => sum + (c.confidence || 0) / arr.length, 0);\r\n\r\n    return {\r\n      total,\r\n      categorized,\r\n      uncategorized,\r\n      mlCategorized,\r\n      manualCategorized,\r\n      averageMLConfidence,\r\n      categorizationRate: total > 0 ? (categorized / total) * 100 : 0\r\n    };\r\n  }, [transactions, categorizations]);\r\n\r\n  if (loading) {\r\n    return (\r\n      <div className=\"categorization-loading\">\r\n        <div className=\"loading-spinner\"></div>\r\n        <p>Loading transaction categorization data...</p>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className=\"transaction-categorization\">\r\n      <div className=\"categorization-header\">\r\n        <div className=\"categorization-title\">\r\n          <h2>Transaction Categorization</h2>\r\n          <p>Categorize transactions manually or use AI-powered categorization with Qwen 3</p>\r\n        </div>\r\n        \r\n        <div className=\"categorization-stats\">\r\n          <div className=\"stat-card\">\r\n            <div className=\"stat-value\">{stats.total}</div>\r\n            <div className=\"stat-label\">Total Transactions</div>\r\n          </div>\r\n          <div className=\"stat-card\">\r\n            <div className=\"stat-value\">{stats.categorized}</div>\r\n            <div className=\"stat-label\">Categorized</div>\r\n          </div>\r\n          <div className=\"stat-card\">\r\n            <div className=\"stat-value\">{stats.uncategorized}</div>\r\n            <div className=\"stat-label\">Uncategorized</div>\r\n          </div>\r\n          <div className=\"stat-card\">\r\n            <div className=\"stat-value\">{stats.categorizationRate.toFixed(1)}%</div>\r\n            <div className=\"stat-label\">Completion Rate</div>\r\n          </div>\r\n          <div className=\"stat-card\">\r\n            <div className=\"stat-value\">{formatConfidence(stats.averageMLConfidence)}</div>\r\n            <div className=\"stat-label\">Avg ML Confidence</div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <div className=\"categorization-controls\">\r\n        <div className=\"view-modes\">\r\n          <button \r\n            className={`view-mode-btn ${viewMode === 'uncategorized' ? 'active' : ''}`}\r\n            onClick={() => setViewMode('uncategorized')}\r\n          >\r\n            Uncategorized ({stats.uncategorized})\r\n          </button>\r\n          <button \r\n            className={`view-mode-btn ${viewMode === 'all' ? 'active' : ''}`}\r\n            onClick={() => setViewMode('all')}\r\n          >\r\n            All Transactions\r\n          </button>\r\n          <button \r\n            className={`view-mode-btn ${viewMode === 'ml-pending' ? 'active' : ''}`}\r\n            onClick={() => setViewMode('ml-pending')}\r\n          >\r\n            ML Pending Review\r\n          </button>\r\n          <button \r\n            className={`view-mode-btn ${viewMode === 'low-confidence' ? 'active' : ''}`}\r\n            onClick={() => setViewMode('low-confidence')}\r\n          >\r\n            Low Confidence\r\n          </button>\r\n        </div>\r\n\r\n        <div className=\"action-buttons\">\r\n          <button \r\n            className=\"btn btn-primary\"\r\n            onClick={() => handleMLCategorization()}\r\n            disabled={processingML}\r\n          >\r\n            {processingML ? `Processing ${mlProgress.current}/${mlProgress.total}...` : 'Run ML Categorization'}\r\n          </button>\r\n          <button \r\n            className=\"btn btn-secondary\"\r\n            onClick={() => setShowMLConfigModal(true)}\r\n          >\r\n            ML Settings\r\n          </button>\r\n          <button \r\n            className=\"btn btn-secondary\"\r\n            onClick={() => setShowCategoryModal(true)}\r\n          >\r\n            Manage Categories\r\n          </button>\r\n        </div>\r\n      </div>\r\n\r\n      <div className=\"transactions-table-container\">\r\n        <table className=\"transactions-table\">\r\n          <thead>\r\n            <tr>\r\n              <th>\r\n                <input \r\n                  type=\"checkbox\" \r\n                  onChange={(e) => {\r\n                    if (e.target.checked) {\r\n                      setSelectedTransactions(new Set(filteredTransactions.map(t => t.id)));\r\n                    } else {\r\n                      setSelectedTransactions(new Set());\r\n                    }\r\n                  }}\r\n                />\r\n              </th>\r\n              <th \r\n                className={`sortable ${sortField === 'date' ? sortDirection : ''}`}\r\n                onClick={() => {\r\n                  if (sortField === 'date') {\r\n                    setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');\r\n                  } else {\r\n                    setSortField('date');\r\n                    setSortDirection('desc');\r\n                  }\r\n                }}\r\n              >\r\n                Date\r\n              </th>\r\n              <th \r\n                className={`sortable ${sortField === 'description' ? sortDirection : ''}`}\r\n                onClick={() => {\r\n                  if (sortField === 'description') {\r\n                    setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');\r\n                  } else {\r\n                    setSortField('description');\r\n                    setSortDirection('asc');\r\n                  }\r\n                }}\r\n              >\r\n                Description\r\n              </th>\r\n              <th \r\n                className={`sortable ${sortField === 'amount' ? sortDirection : ''}`}\r\n                onClick={() => {\r\n                  if (sortField === 'amount') {\r\n                    setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');\r\n                  } else {\r\n                    setSortField('amount');\r\n                    setSortDirection('desc');\r\n                  }\r\n                }}\r\n              >\r\n                Amount\r\n              </th>\r\n              <th>Category</th>\r\n              <th \r\n                className={`sortable ${sortField === 'confidence' ? sortDirection : ''}`}\r\n                onClick={() => {\r\n                  if (sortField === 'confidence') {\r\n                    setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');\r\n                  } else {\r\n                    setSortField('confidence');\r\n                    setSortDirection('desc');\r\n                  }\r\n                }}\r\n              >\r\n                Confidence\r\n              </th>\r\n              <th>Actions</th>\r\n            </tr>\r\n          </thead>\r\n          <tbody>\r\n            {filteredTransactions.map(transaction => {\r\n              const categorization = getTransactionCategorization(transaction.id);\r\n              const category = categorization ? getCategoryById(categorization.categoryId) : undefined;\r\n              const amount = transaction.debitAmount ? -transaction.debitAmount : (transaction.creditAmount || 0);\r\n              \r\n              return (\r\n                <tr key={transaction.id} className={selectedTransactions.has(transaction.id) ? 'selected' : ''}>\r\n                  <td>\r\n                    <input \r\n                      type=\"checkbox\" \r\n                      checked={selectedTransactions.has(transaction.id)}\r\n                      onChange={(e) => {\r\n                        const newSet = new Set(selectedTransactions);\r\n                        if (e.target.checked) {\r\n                          newSet.add(transaction.id);\r\n                        } else {\r\n                          newSet.delete(transaction.id);\r\n                        }\r\n                        setSelectedTransactions(newSet);\r\n                      }}\r\n                    />\r\n                  </td>\r\n                  <td>{new Date(transaction.postDateTime).toLocaleDateString()}</td>\r\n                  <td className=\"description-cell\" title={transaction.description}>\r\n                    {transaction.description}\r\n                  </td>\r\n                  <td className={`amount-cell ${amount < 0 ? 'debit' : 'credit'}`}>\r\n                    {formatCurrency(Math.abs(amount))}\r\n                  </td>\r\n                  <td>\r\n                    {category ? (\r\n                      <div className=\"category-tag\" style={{ backgroundColor: category.color }}>\r\n                        {category.name}\r\n                        {categorization?.method && (\r\n                          <span className=\"category-method\">{categorization.method.toUpperCase()}</span>\r\n                        )}\r\n                      </div>\r\n                    ) : (\r\n                      <span className=\"uncategorized\">Uncategorized</span>\r\n                    )}\r\n                  </td>\r\n                  <td>\r\n                    {categorization?.confidence !== undefined ? (\r\n                      <span \r\n                        className=\"confidence-score\"\r\n                        style={{ color: getConfidenceColor(categorization.confidence) }}\r\n                      >\r\n                        {formatConfidence(categorization.confidence)}\r\n                      </span>\r\n                    ) : (\r\n                      <span className=\"no-confidence\">-</span>\r\n                    )}\r\n                  </td>\r\n                  <td>\r\n                    <div className=\"action-buttons\">\r\n                      <select \r\n                        value={categorization?.categoryId || ''}\r\n                        onChange={(e) => {\r\n                          if (e.target.value) {\r\n                            handleManualCategorization(transaction.id, e.target.value);\r\n                          }\r\n                        }}\r\n                        className=\"category-select\"\r\n                      >\r\n                        <option value=\"\">Select Category...</option>\r\n                        {categories.map(cat => (\r\n                          <option key={cat.id} value={cat.id}>{cat.name}</option>\r\n                        ))}\r\n                      </select>\r\n                    </div>\r\n                  </td>\r\n                </tr>\r\n              );\r\n            })}\r\n          </tbody>\r\n        </table>\r\n      </div>\r\n\r\n      {selectedTransactions.size > 0 && (\r\n        <div className=\"batch-actions\">\r\n          <div className=\"batch-info\">\r\n            {selectedTransactions.size} transaction(s) selected\r\n          </div>\r\n          <button \r\n            className=\"btn btn-primary\"\r\n            onClick={() => handleMLCategorization(Array.from(selectedTransactions))}\r\n            disabled={processingML}\r\n          >\r\n            Categorize Selected with ML\r\n          </button>\r\n          <button \r\n            className=\"btn btn-secondary\"\r\n            onClick={() => setSelectedTransactions(new Set())}\r\n          >\r\n            Clear Selection\r\n          </button>\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n}; "], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,CAAEC,WAAW,CAAEC,OAAO,KAAQ,OAAO,CAExE,OAASC,yBAAyB,KAAgC,uCAAuC,CACzG,OAASC,qBAAqB,KAAQ,mCAAmC,CACzE,OAASC,uBAAuB,KAAQ,qCAAqC,CAC7E,MAAO,iCAAiC,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAiBzC,MAAO,MAAM,CAAAC,yBAAmE,CAAGC,IAAA,EAAwB,IAAvB,CAAEC,cAAe,CAAC,CAAAD,IAAA,CACpG;AACA,KAAM,CAACE,YAAY,CAAEC,eAAe,CAAC,CAAGf,QAAQ,CAAsB,EAAE,CAAC,CACzE,KAAM,CAACgB,UAAU,CAAEC,aAAa,CAAC,CAAGjB,QAAQ,CAAwB,EAAE,CAAC,CACvE,KAAM,CAACkB,eAAe,CAAEC,kBAAkB,CAAC,CAAGnB,QAAQ,CAAkC,EAAE,CAAC,CAC3F,KAAM,CAACoB,OAAO,CAAEC,UAAU,CAAC,CAAGrB,QAAQ,CAAC,IAAI,CAAC,CAC5C,KAAM,CAACsB,YAAY,CAAEC,eAAe,CAAC,CAAGvB,QAAQ,CAAC,KAAK,CAAC,CACvD,KAAM,CAACwB,UAAU,CAAEC,aAAa,CAAC,CAAGzB,QAAQ,CAAC,CAAE0B,OAAO,CAAE,CAAC,CAAEC,KAAK,CAAE,CAAE,CAAC,CAAC,CACtE,KAAM,CAACC,oBAAoB,CAAEC,uBAAuB,CAAC,CAAG7B,QAAQ,CAAc,GAAI,CAAA8B,GAAG,CAAC,CAAC,CAAC,CAExF;AACA,KAAM,CAACC,QAAQ,CAAEC,WAAW,CAAC,CAAGhC,QAAQ,CAAW,eAAe,CAAC,CACnE,KAAM,CAACiC,SAAS,CAAEC,YAAY,CAAC,CAAGlC,QAAQ,CAAY,MAAM,CAAC,CAC7D,KAAM,CAACmC,aAAa,CAAEC,gBAAgB,CAAC,CAAGpC,QAAQ,CAAgB,MAAM,CAAC,CACzE,KAAM,CAACqC,cAAc,CAAC,CAAGrC,QAAQ,CAAiB,CAChDsC,UAAU,CAAE,EAAE,CACdC,MAAM,CAAE,KAAK,CACbC,aAAa,CAAE,CAAC,CAChBC,aAAa,CAAE,CACjB,CAAC,CAAC,CAEF;AACA,KAAM,CAACC,QAAQ,CAAC,CAAG1C,QAAQ,CAAC,CAC1B2C,mBAAmB,CAAE,GAAG,CACxBC,SAAS,CAAE,EAAE,CACbC,uBAAuB,CAAE,IAC3B,CAAC,CAAC,CAEF;AACA,KAAM,EAAGC,oBAAoB,CAAC,CAAG9C,QAAQ,CAAC,KAAK,CAAC,CAChD,KAAM,EAAG+C,oBAAoB,CAAC,CAAG/C,QAAQ,CAAC,KAAK,CAAC,CAEhD;AACA,KAAM,CAAAgD,QAAQ,CAAG9C,WAAW,CAAC,SAAY,CACvC,GAAI,CACFmB,UAAU,CAAC,IAAI,CAAC,CAEhB;AACA,KAAM,CAAC4B,eAAe,CAAEC,aAAa,CAAEC,kBAAkB,CAAC,CAAG,KAAM,CAAAC,OAAO,CAACC,GAAG,CAAC,CAC7ED,OAAO,CAACE,OAAO,CAAClD,yBAAyB,CAACmD,kBAAkB,CAAC,CAAC,CAAC,CAC/DH,OAAO,CAACE,OAAO,CAACjD,qBAAqB,CAACmD,gBAAgB,CAAC,CAAC,CAAC,CACzDJ,OAAO,CAACE,OAAO,CAACjD,qBAAqB,CAACoD,qBAAqB,CAAC,CAAC,CAAC,CAC/D,CAAC,CAEF1C,eAAe,CAACkC,eAAe,CAAC,CAChChC,aAAa,CAACiC,aAAa,CAAC,CAC5B/B,kBAAkB,CAACgC,kBAAkB,CAAC,CACxC,CAAE,MAAOO,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,qCAAqC,CAAEA,KAAK,CAAC,CAC7D,CAAC,OAAS,CACRrC,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAAE,EAAE,CAAC,CAENpB,SAAS,CAAC,IAAM,CACd+C,QAAQ,CAAC,CAAC,CACZ,CAAC,CAAE,CAACA,QAAQ,CAAEnC,cAAc,CAAC,CAAC,CAE9B;AACA,KAAM,CAAA+C,4BAA4B,CAAG1D,WAAW,CAAE2D,aAAqB,EAAgD,CACrH,MAAO,CAAA3C,eAAe,CAAC4C,IAAI,CAACC,GAAG,EAAIA,GAAG,CAACF,aAAa,GAAKA,aAAa,CAAC,CACzE,CAAC,CAAE,CAAC3C,eAAe,CAAC,CAAC,CAErB;AACA,KAAM,CAAA8C,eAAe,CAAG9D,WAAW,CAAEoC,UAAkB,EAAsC,CAC3F,MAAO,CAAAtB,UAAU,CAAC8C,IAAI,CAACC,GAAG,EAAIA,GAAG,CAACE,EAAE,GAAK3B,UAAU,CAAC,CACtD,CAAC,CAAE,CAACtB,UAAU,CAAC,CAAC,CAEhB;AACA,KAAM,CAAAkD,oBAAoB,CAAG/D,OAAO,CAAC,IAAM,CACzC,GAAI,CAAAgE,QAAQ,CAAG,CAAC,GAAGrD,YAAY,CAAC,CAEhC;AACA,OAAQiB,QAAQ,EACd,IAAK,eAAe,CAClBoC,QAAQ,CAAGA,QAAQ,CAACC,MAAM,CAACC,CAAC,EAAI,CAACT,4BAA4B,CAACS,CAAC,CAACJ,EAAE,CAAC,CAAC,CACpE,MACF,IAAK,YAAY,CACfE,QAAQ,CAAGA,QAAQ,CAACC,MAAM,CAACC,CAAC,EAAI,CAC9B,KAAM,CAAAN,GAAG,CAAGH,4BAA4B,CAACS,CAAC,CAACJ,EAAE,CAAC,CAC9C,MAAO,CAAAF,GAAG,EAAIA,GAAG,CAACxB,MAAM,GAAK,IAAI,EAAI,CAACwB,GAAG,CAACO,UAAU,EAAI,CAAC,EAAI5B,QAAQ,CAACC,mBAAmB,CAC3F,CAAC,CAAC,CACF,MACF,IAAK,gBAAgB,CACnBwB,QAAQ,CAAGA,QAAQ,CAACC,MAAM,CAACC,CAAC,EAAI,CAC9B,KAAM,CAAAN,GAAG,CAAGH,4BAA4B,CAACS,CAAC,CAACJ,EAAE,CAAC,CAC9C,MAAO,CAAAF,GAAG,EAAIA,GAAG,CAACxB,MAAM,GAAK,IAAI,EAAI,CAACwB,GAAG,CAACO,UAAU,EAAI,CAAC,EAAI,GAAG,CAClE,CAAC,CAAC,CACF,MACF,IAAK,KAAK,CACV,QACE;AACA,MACJ,CAEA;AACA,GAAIjC,cAAc,CAACC,UAAU,CAAE,CAC7B6B,QAAQ,CAAGA,QAAQ,CAACC,MAAM,CAACC,CAAC,EAAI,CAC9B,KAAM,CAAAN,GAAG,CAAGH,4BAA4B,CAACS,CAAC,CAACJ,EAAE,CAAC,CAC9C,MAAO,CAAAF,GAAG,EAAIA,GAAG,CAACzB,UAAU,GAAKD,cAAc,CAACC,UAAU,CAC5D,CAAC,CAAC,CACJ,CAEA;AACA,GAAID,cAAc,CAACE,MAAM,GAAK,KAAK,CAAE,CACnC4B,QAAQ,CAAGA,QAAQ,CAACC,MAAM,CAACC,CAAC,EAAI,CAC9B,KAAM,CAAAN,GAAG,CAAGH,4BAA4B,CAACS,CAAC,CAACJ,EAAE,CAAC,CAC9C,MAAO,CAAAF,GAAG,EAAIA,GAAG,CAACxB,MAAM,GAAKF,cAAc,CAACE,MAAM,CACpD,CAAC,CAAC,CACJ,CAEA;AACA4B,QAAQ,CAAGA,QAAQ,CAACC,MAAM,CAACC,CAAC,EAAI,CAC9B,KAAM,CAAAN,GAAG,CAAGH,4BAA4B,CAACS,CAAC,CAACJ,EAAE,CAAC,CAC9C,GAAI,CAACF,GAAG,EAAIA,GAAG,CAACxB,MAAM,GAAK,IAAI,CAAE,MAAO,KAAI,CAAE;AAC9C,KAAM,CAAA+B,UAAU,CAAGP,GAAG,CAACO,UAAU,EAAI,CAAC,CACtC,MAAO,CAAAA,UAAU,EAAIjC,cAAc,CAACG,aAAa,EAAI8B,UAAU,EAAIjC,cAAc,CAACI,aAAa,CACjG,CAAC,CAAC,CAEF;AACA0B,QAAQ,CAACI,IAAI,CAAC,CAACC,CAAC,CAAEC,CAAC,GAAK,KAAAC,qBAAA,CAAAC,sBAAA,CACtB,GAAI,CAAAC,MAAuB,CAC3B,GAAI,CAAAC,MAAuB,CAE3B,OAAQ5C,SAAS,EACf,IAAK,MAAM,CACT2C,MAAM,CAAG,GAAI,CAAAE,IAAI,CAACN,CAAC,CAACO,YAAY,CAAC,CAACC,OAAO,CAAC,CAAC,CAC3CH,MAAM,CAAG,GAAI,CAAAC,IAAI,CAACL,CAAC,CAACM,YAAY,CAAC,CAACC,OAAO,CAAC,CAAC,CAC3C,MACF,IAAK,QAAQ,CACXJ,MAAM,CAAGK,IAAI,CAACC,GAAG,CAACV,CAAC,CAACW,WAAW,EAAIX,CAAC,CAACY,YAAY,EAAI,CAAC,CAAC,CACvDP,MAAM,CAAGI,IAAI,CAACC,GAAG,CAACT,CAAC,CAACU,WAAW,EAAIV,CAAC,CAACW,YAAY,EAAI,CAAC,CAAC,CACvD,MACF,IAAK,YAAY,CACfR,MAAM,CAAG,EAAAF,qBAAA,CAAAd,4BAA4B,CAACY,CAAC,CAACP,EAAE,CAAC,UAAAS,qBAAA,iBAAlCA,qBAAA,CAAoCJ,UAAU,GAAI,CAAC,CAC5DO,MAAM,CAAG,EAAAF,sBAAA,CAAAf,4BAA4B,CAACa,CAAC,CAACR,EAAE,CAAC,UAAAU,sBAAA,iBAAlCA,sBAAA,CAAoCL,UAAU,GAAI,CAAC,CAC5D,MACF,IAAK,aAAa,CAClB,QACEM,MAAM,CAAGJ,CAAC,CAACa,WAAW,CAACC,WAAW,CAAC,CAAC,CACpCT,MAAM,CAAGJ,CAAC,CAACY,WAAW,CAACC,WAAW,CAAC,CAAC,CACpC,MACJ,CAEA,GAAIV,MAAM,CAAGC,MAAM,CAAE,MAAO,CAAA1C,aAAa,GAAK,KAAK,CAAG,CAAC,CAAC,CAAG,CAAC,CAC5D,GAAIyC,MAAM,CAAGC,MAAM,CAAE,MAAO,CAAA1C,aAAa,GAAK,KAAK,CAAG,CAAC,CAAG,CAAC,CAAC,CAC5D,MAAO,EAAC,CACV,CAAC,CAAC,CAEF,MAAO,CAAAgC,QAAQ,CACjB,CAAC,CAAE,CAACrD,YAAY,CAAEiB,QAAQ,CAAEM,cAAc,CAAEJ,SAAS,CAAEE,aAAa,CAAEO,QAAQ,CAACC,mBAAmB,CAAEiB,4BAA4B,CAAC,CAAC,CAElI;AACA,KAAM,CAAA2B,0BAA0B,CAAGrF,WAAW,CAAC,MAAO2D,aAAqB,CAAEvB,UAAkB,GAAK,CAClG,GAAI,CACFjC,qBAAqB,CAACmF,qBAAqB,CAAC3B,aAAa,CAAEvB,UAAU,CAAE,QAAQ,CAAC,CAEhF;AACA,KAAM,CAAAmD,sBAAsB,CAAGpF,qBAAqB,CAACoD,qBAAqB,CAAC,CAAC,CAC5EtC,kBAAkB,CAACsE,sBAAsB,CAAC,CAE1C;AACA5D,uBAAuB,CAAC6D,IAAI,EAAI,CAC9B,KAAM,CAAAC,MAAM,CAAG,GAAI,CAAA7D,GAAG,CAAC4D,IAAI,CAAC,CAC5BC,MAAM,CAACC,MAAM,CAAC/B,aAAa,CAAC,CAC5B,MAAO,CAAA8B,MAAM,CACf,CAAC,CAAC,CACJ,CAAE,MAAOjC,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,mCAAmC,CAAEA,KAAK,CAAC,CAC3D,CACF,CAAC,CAAE,EAAE,CAAC,CAEN;AACA,KAAM,CAAAmC,sBAAsB,CAAG3F,WAAW,CAAC,KAAO,CAAA4F,cAAyB,EAAK,CAC9E,GAAI,CACFvE,eAAe,CAAC,IAAI,CAAC,CAErB,KAAM,CAAAwE,qBAAqB,CAAGD,cAAc,CACxChF,YAAY,CAACsD,MAAM,CAACC,CAAC,EAAIyB,cAAc,CAACE,QAAQ,CAAC3B,CAAC,CAACJ,EAAE,CAAC,CAAC,CACvDC,oBAAoB,CAACE,MAAM,CAACC,CAAC,EAAI,CAACT,4BAA4B,CAACS,CAAC,CAACJ,EAAE,CAAC,CAAC,CAEzExC,aAAa,CAAC,CAAEC,OAAO,CAAE,CAAC,CAAEC,KAAK,CAAEoE,qBAAqB,CAACE,MAAO,CAAC,CAAC,CAElE;AACA,KAAM,CAAAC,cAA6B,CAAGH,qBAAqB,CAACI,GAAG,CAAC9B,CAAC,GAAK,CACpEJ,EAAE,CAAEI,CAAC,CAACJ,EAAE,CACRmC,IAAI,CAAE/B,CAAC,CAACU,YAAY,CACpBM,WAAW,CAAEhB,CAAC,CAACgB,WAAW,CAC1BF,WAAW,CAAEd,CAAC,CAACc,WAAW,EAAI,CAAC,CAC/BC,YAAY,CAAEf,CAAC,CAACe,YAAY,EAAI,CAAC,CACjCiB,OAAO,CAAEhC,CAAC,CAACgC,OAAO,CAClBC,SAAS,CAAEjC,CAAC,CAACiC,SACf,CAAC,CAAC,CAAC,CAEH,KAAM,CAAAC,OAAO,CAAG,KAAM,CAAAjG,uBAAuB,CAACkG,2BAA2B,CAACN,cAAc,CAAC,CAEzF;AACA,GAAI,CAAAO,SAAS,CAAG,CAAC,CACjB,IAAK,KAAM,CAAAC,MAAM,GAAI,CAAAH,OAAO,CAAE,CAC5BE,SAAS,EAAE,CACXhF,aAAa,CAAC,CAAEC,OAAO,CAAE+E,SAAS,CAAE9E,KAAK,CAAEoE,qBAAqB,CAACE,MAAO,CAAC,CAAC,CAE1E,GAAIS,MAAM,CAACA,MAAM,EAAIA,MAAM,CAACA,MAAM,CAACpC,UAAU,EAAI5B,QAAQ,CAACC,mBAAmB,EAAID,QAAQ,CAACG,uBAAuB,CAAE,CACjH;AACA,SAAU;AACZ,CACF,CAEA;AACA,KAAM,CAAA4C,sBAAsB,CAAGpF,qBAAqB,CAACoD,qBAAqB,CAAC,CAAC,CAC5EtC,kBAAkB,CAACsE,sBAAsB,CAAC,CAE5C,CAAE,MAAO/B,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,2BAA2B,CAAEA,KAAK,CAAC,CACnD,CAAC,OAAS,CACRnC,eAAe,CAAC,KAAK,CAAC,CACtBE,aAAa,CAAC,CAAEC,OAAO,CAAE,CAAC,CAAEC,KAAK,CAAE,CAAE,CAAC,CAAC,CACzC,CACF,CAAC,CAAE,CAACb,YAAY,CAAEoD,oBAAoB,CAAExB,QAAQ,CAAEkB,4BAA4B,CAAC,CAAC,CAEhF;AACA,KAAM,CAAA+C,cAAc,CAAGzG,WAAW,CAAE0G,MAAc,EAAa,CAC7D,MAAO,IAAI,CAAAC,IAAI,CAACC,YAAY,CAAC,OAAO,CAAE,CACpCC,KAAK,CAAE,UAAU,CACjBC,QAAQ,CAAE,KAAK,CACfC,qBAAqB,CAAE,CAAC,CACxBC,qBAAqB,CAAE,CACzB,CAAC,CAAC,CAACC,MAAM,CAACP,MAAM,CAAC,CACnB,CAAC,CAAE,EAAE,CAAC,CAEN;AACA,KAAM,CAAAQ,gBAAgB,CAAGlH,WAAW,CAAEoE,UAAmB,EAAa,CACpE,GAAIA,UAAU,GAAK+C,SAAS,CAAE,MAAO,KAAK,CAC1C,SAAAC,MAAA,CAAUrC,IAAI,CAACsC,KAAK,CAACjD,UAAU,CAAG,GAAG,CAAC,MACxC,CAAC,CAAE,EAAE,CAAC,CAEN;AACA,KAAM,CAAAkD,kBAAkB,CAAGtH,WAAW,CAAEoE,UAAmB,EAAa,CACtE,GAAIA,UAAU,GAAK+C,SAAS,CAAE,MAAO,SAAS,CAC9C,GAAI/C,UAAU,EAAI,GAAG,CAAE,MAAO,SAAS,CACvC,GAAIA,UAAU,EAAI,GAAG,CAAE,MAAO,SAAS,CACvC,MAAO,SAAS,CAClB,CAAC,CAAE,EAAE,CAAC,CAEN;AACA,KAAM,CAAAmD,KAAK,CAAGtH,OAAO,CAAC,IAAM,CAC1B,KAAM,CAAAwB,KAAK,CAAGb,YAAY,CAACmF,MAAM,CACjC,KAAM,CAAAyB,WAAW,CAAGxG,eAAe,CAAC+E,MAAM,CAC1C,KAAM,CAAA0B,aAAa,CAAGhG,KAAK,CAAG+F,WAAW,CACzC,KAAM,CAAAE,aAAa,CAAG1G,eAAe,CAACkD,MAAM,CAACyD,CAAC,EAAIA,CAAC,CAACtF,MAAM,GAAK,IAAI,CAAC,CAAC0D,MAAM,CAC3E,KAAM,CAAA6B,iBAAiB,CAAG5G,eAAe,CAACkD,MAAM,CAACyD,CAAC,EAAIA,CAAC,CAACtF,MAAM,GAAK,QAAQ,CAAC,CAAC0D,MAAM,CACnF,KAAM,CAAA8B,mBAAmB,CAAG7G,eAAe,CACxCkD,MAAM,CAACyD,CAAC,EAAIA,CAAC,CAACtF,MAAM,GAAK,IAAI,EAAIsF,CAAC,CAACvD,UAAU,GAAK+C,SAAS,CAAC,CAC5DW,MAAM,CAAC,CAACC,GAAG,CAAEJ,CAAC,CAAEK,CAAC,CAAEC,GAAG,GAAKF,GAAG,CAAG,CAACJ,CAAC,CAACvD,UAAU,EAAI,CAAC,EAAI6D,GAAG,CAAClC,MAAM,CAAE,CAAC,CAAC,CAExE,MAAO,CACLtE,KAAK,CACL+F,WAAW,CACXC,aAAa,CACbC,aAAa,CACbE,iBAAiB,CACjBC,mBAAmB,CACnBK,kBAAkB,CAAEzG,KAAK,CAAG,CAAC,CAAI+F,WAAW,CAAG/F,KAAK,CAAI,GAAG,CAAG,CAChE,CAAC,CACH,CAAC,CAAE,CAACb,YAAY,CAAEI,eAAe,CAAC,CAAC,CAEnC,GAAIE,OAAO,CAAE,CACX,mBACEV,KAAA,QAAK2H,SAAS,CAAC,wBAAwB,CAAAC,QAAA,eACrC9H,IAAA,QAAK6H,SAAS,CAAC,iBAAiB,CAAM,CAAC,cACvC7H,IAAA,MAAA8H,QAAA,CAAG,4CAA0C,CAAG,CAAC,EAC9C,CAAC,CAEV,CAEA,mBACE5H,KAAA,QAAK2H,SAAS,CAAC,4BAA4B,CAAAC,QAAA,eACzC5H,KAAA,QAAK2H,SAAS,CAAC,uBAAuB,CAAAC,QAAA,eACpC5H,KAAA,QAAK2H,SAAS,CAAC,sBAAsB,CAAAC,QAAA,eACnC9H,IAAA,OAAA8H,QAAA,CAAI,4BAA0B,CAAI,CAAC,cACnC9H,IAAA,MAAA8H,QAAA,CAAG,+EAA6E,CAAG,CAAC,EACjF,CAAC,cAEN5H,KAAA,QAAK2H,SAAS,CAAC,sBAAsB,CAAAC,QAAA,eACnC5H,KAAA,QAAK2H,SAAS,CAAC,WAAW,CAAAC,QAAA,eACxB9H,IAAA,QAAK6H,SAAS,CAAC,YAAY,CAAAC,QAAA,CAAEb,KAAK,CAAC9F,KAAK,CAAM,CAAC,cAC/CnB,IAAA,QAAK6H,SAAS,CAAC,YAAY,CAAAC,QAAA,CAAC,oBAAkB,CAAK,CAAC,EACjD,CAAC,cACN5H,KAAA,QAAK2H,SAAS,CAAC,WAAW,CAAAC,QAAA,eACxB9H,IAAA,QAAK6H,SAAS,CAAC,YAAY,CAAAC,QAAA,CAAEb,KAAK,CAACC,WAAW,CAAM,CAAC,cACrDlH,IAAA,QAAK6H,SAAS,CAAC,YAAY,CAAAC,QAAA,CAAC,aAAW,CAAK,CAAC,EAC1C,CAAC,cACN5H,KAAA,QAAK2H,SAAS,CAAC,WAAW,CAAAC,QAAA,eACxB9H,IAAA,QAAK6H,SAAS,CAAC,YAAY,CAAAC,QAAA,CAAEb,KAAK,CAACE,aAAa,CAAM,CAAC,cACvDnH,IAAA,QAAK6H,SAAS,CAAC,YAAY,CAAAC,QAAA,CAAC,eAAa,CAAK,CAAC,EAC5C,CAAC,cACN5H,KAAA,QAAK2H,SAAS,CAAC,WAAW,CAAAC,QAAA,eACxB5H,KAAA,QAAK2H,SAAS,CAAC,YAAY,CAAAC,QAAA,EAAEb,KAAK,CAACW,kBAAkB,CAACG,OAAO,CAAC,CAAC,CAAC,CAAC,GAAC,EAAK,CAAC,cACxE/H,IAAA,QAAK6H,SAAS,CAAC,YAAY,CAAAC,QAAA,CAAC,iBAAe,CAAK,CAAC,EAC9C,CAAC,cACN5H,KAAA,QAAK2H,SAAS,CAAC,WAAW,CAAAC,QAAA,eACxB9H,IAAA,QAAK6H,SAAS,CAAC,YAAY,CAAAC,QAAA,CAAElB,gBAAgB,CAACK,KAAK,CAACM,mBAAmB,CAAC,CAAM,CAAC,cAC/EvH,IAAA,QAAK6H,SAAS,CAAC,YAAY,CAAAC,QAAA,CAAC,mBAAiB,CAAK,CAAC,EAChD,CAAC,EACH,CAAC,EACH,CAAC,cAEN5H,KAAA,QAAK2H,SAAS,CAAC,yBAAyB,CAAAC,QAAA,eACtC5H,KAAA,QAAK2H,SAAS,CAAC,YAAY,CAAAC,QAAA,eACzB5H,KAAA,WACE2H,SAAS,kBAAAf,MAAA,CAAmBvF,QAAQ,GAAK,eAAe,CAAG,QAAQ,CAAG,EAAE,CAAG,CAC3EyG,OAAO,CAAEA,CAAA,GAAMxG,WAAW,CAAC,eAAe,CAAE,CAAAsG,QAAA,EAC7C,iBACgB,CAACb,KAAK,CAACE,aAAa,CAAC,GACtC,EAAQ,CAAC,cACTnH,IAAA,WACE6H,SAAS,kBAAAf,MAAA,CAAmBvF,QAAQ,GAAK,KAAK,CAAG,QAAQ,CAAG,EAAE,CAAG,CACjEyG,OAAO,CAAEA,CAAA,GAAMxG,WAAW,CAAC,KAAK,CAAE,CAAAsG,QAAA,CACnC,kBAED,CAAQ,CAAC,cACT9H,IAAA,WACE6H,SAAS,kBAAAf,MAAA,CAAmBvF,QAAQ,GAAK,YAAY,CAAG,QAAQ,CAAG,EAAE,CAAG,CACxEyG,OAAO,CAAEA,CAAA,GAAMxG,WAAW,CAAC,YAAY,CAAE,CAAAsG,QAAA,CAC1C,mBAED,CAAQ,CAAC,cACT9H,IAAA,WACE6H,SAAS,kBAAAf,MAAA,CAAmBvF,QAAQ,GAAK,gBAAgB,CAAG,QAAQ,CAAG,EAAE,CAAG,CAC5EyG,OAAO,CAAEA,CAAA,GAAMxG,WAAW,CAAC,gBAAgB,CAAE,CAAAsG,QAAA,CAC9C,gBAED,CAAQ,CAAC,EACN,CAAC,cAEN5H,KAAA,QAAK2H,SAAS,CAAC,gBAAgB,CAAAC,QAAA,eAC7B9H,IAAA,WACE6H,SAAS,CAAC,iBAAiB,CAC3BG,OAAO,CAAEA,CAAA,GAAM3C,sBAAsB,CAAC,CAAE,CACxC4C,QAAQ,CAAEnH,YAAa,CAAAgH,QAAA,CAEtBhH,YAAY,eAAAgG,MAAA,CAAiB9F,UAAU,CAACE,OAAO,MAAA4F,MAAA,CAAI9F,UAAU,CAACG,KAAK,QAAQ,uBAAuB,CAC7F,CAAC,cACTnB,IAAA,WACE6H,SAAS,CAAC,mBAAmB,CAC7BG,OAAO,CAAEA,CAAA,GAAM1F,oBAAoB,CAAC,IAAI,CAAE,CAAAwF,QAAA,CAC3C,aAED,CAAQ,CAAC,cACT9H,IAAA,WACE6H,SAAS,CAAC,mBAAmB,CAC7BG,OAAO,CAAEA,CAAA,GAAMzF,oBAAoB,CAAC,IAAI,CAAE,CAAAuF,QAAA,CAC3C,mBAED,CAAQ,CAAC,EACN,CAAC,EACH,CAAC,cAEN9H,IAAA,QAAK6H,SAAS,CAAC,8BAA8B,CAAAC,QAAA,cAC3C5H,KAAA,UAAO2H,SAAS,CAAC,oBAAoB,CAAAC,QAAA,eACnC9H,IAAA,UAAA8H,QAAA,cACE5H,KAAA,OAAA4H,QAAA,eACE9H,IAAA,OAAA8H,QAAA,cACE9H,IAAA,UACEkI,IAAI,CAAC,UAAU,CACfC,QAAQ,CAAGC,CAAC,EAAK,CACf,GAAIA,CAAC,CAACC,MAAM,CAACC,OAAO,CAAE,CACpBjH,uBAAuB,CAAC,GAAI,CAAAC,GAAG,CAACoC,oBAAoB,CAACiC,GAAG,CAAC9B,CAAC,EAAIA,CAAC,CAACJ,EAAE,CAAC,CAAC,CAAC,CACvE,CAAC,IAAM,CACLpC,uBAAuB,CAAC,GAAI,CAAAC,GAAG,CAAC,CAAC,CAAC,CACpC,CACF,CAAE,CACH,CAAC,CACA,CAAC,cACLtB,IAAA,OACE6H,SAAS,aAAAf,MAAA,CAAcrF,SAAS,GAAK,MAAM,CAAGE,aAAa,CAAG,EAAE,CAAG,CACnEqG,OAAO,CAAEA,CAAA,GAAM,CACb,GAAIvG,SAAS,GAAK,MAAM,CAAE,CACxBG,gBAAgB,CAACD,aAAa,GAAK,KAAK,CAAG,MAAM,CAAG,KAAK,CAAC,CAC5D,CAAC,IAAM,CACLD,YAAY,CAAC,MAAM,CAAC,CACpBE,gBAAgB,CAAC,MAAM,CAAC,CAC1B,CACF,CAAE,CAAAkG,QAAA,CACH,MAED,CAAI,CAAC,cACL9H,IAAA,OACE6H,SAAS,aAAAf,MAAA,CAAcrF,SAAS,GAAK,aAAa,CAAGE,aAAa,CAAG,EAAE,CAAG,CAC1EqG,OAAO,CAAEA,CAAA,GAAM,CACb,GAAIvG,SAAS,GAAK,aAAa,CAAE,CAC/BG,gBAAgB,CAACD,aAAa,GAAK,KAAK,CAAG,MAAM,CAAG,KAAK,CAAC,CAC5D,CAAC,IAAM,CACLD,YAAY,CAAC,aAAa,CAAC,CAC3BE,gBAAgB,CAAC,KAAK,CAAC,CACzB,CACF,CAAE,CAAAkG,QAAA,CACH,aAED,CAAI,CAAC,cACL9H,IAAA,OACE6H,SAAS,aAAAf,MAAA,CAAcrF,SAAS,GAAK,QAAQ,CAAGE,aAAa,CAAG,EAAE,CAAG,CACrEqG,OAAO,CAAEA,CAAA,GAAM,CACb,GAAIvG,SAAS,GAAK,QAAQ,CAAE,CAC1BG,gBAAgB,CAACD,aAAa,GAAK,KAAK,CAAG,MAAM,CAAG,KAAK,CAAC,CAC5D,CAAC,IAAM,CACLD,YAAY,CAAC,QAAQ,CAAC,CACtBE,gBAAgB,CAAC,MAAM,CAAC,CAC1B,CACF,CAAE,CAAAkG,QAAA,CACH,QAED,CAAI,CAAC,cACL9H,IAAA,OAAA8H,QAAA,CAAI,UAAQ,CAAI,CAAC,cACjB9H,IAAA,OACE6H,SAAS,aAAAf,MAAA,CAAcrF,SAAS,GAAK,YAAY,CAAGE,aAAa,CAAG,EAAE,CAAG,CACzEqG,OAAO,CAAEA,CAAA,GAAM,CACb,GAAIvG,SAAS,GAAK,YAAY,CAAE,CAC9BG,gBAAgB,CAACD,aAAa,GAAK,KAAK,CAAG,MAAM,CAAG,KAAK,CAAC,CAC5D,CAAC,IAAM,CACLD,YAAY,CAAC,YAAY,CAAC,CAC1BE,gBAAgB,CAAC,MAAM,CAAC,CAC1B,CACF,CAAE,CAAAkG,QAAA,CACH,YAED,CAAI,CAAC,cACL9H,IAAA,OAAA8H,QAAA,CAAI,SAAO,CAAI,CAAC,EACd,CAAC,CACA,CAAC,cACR9H,IAAA,UAAA8H,QAAA,CACGpE,oBAAoB,CAACiC,GAAG,CAAC4C,WAAW,EAAI,CACvC,KAAM,CAAAC,cAAc,CAAGpF,4BAA4B,CAACmF,WAAW,CAAC9E,EAAE,CAAC,CACnE,KAAM,CAAAgF,QAAQ,CAAGD,cAAc,CAAGhF,eAAe,CAACgF,cAAc,CAAC1G,UAAU,CAAC,CAAG+E,SAAS,CACxF,KAAM,CAAAT,MAAM,CAAGmC,WAAW,CAAC5D,WAAW,CAAG,CAAC4D,WAAW,CAAC5D,WAAW,CAAI4D,WAAW,CAAC3D,YAAY,EAAI,CAAE,CAEnG,mBACE1E,KAAA,OAAyB2H,SAAS,CAAEzG,oBAAoB,CAACsH,GAAG,CAACH,WAAW,CAAC9E,EAAE,CAAC,CAAG,UAAU,CAAG,EAAG,CAAAqE,QAAA,eAC7F9H,IAAA,OAAA8H,QAAA,cACE9H,IAAA,UACEkI,IAAI,CAAC,UAAU,CACfI,OAAO,CAAElH,oBAAoB,CAACsH,GAAG,CAACH,WAAW,CAAC9E,EAAE,CAAE,CAClD0E,QAAQ,CAAGC,CAAC,EAAK,CACf,KAAM,CAAAjD,MAAM,CAAG,GAAI,CAAA7D,GAAG,CAACF,oBAAoB,CAAC,CAC5C,GAAIgH,CAAC,CAACC,MAAM,CAACC,OAAO,CAAE,CACpBnD,MAAM,CAACwD,GAAG,CAACJ,WAAW,CAAC9E,EAAE,CAAC,CAC5B,CAAC,IAAM,CACL0B,MAAM,CAACC,MAAM,CAACmD,WAAW,CAAC9E,EAAE,CAAC,CAC/B,CACApC,uBAAuB,CAAC8D,MAAM,CAAC,CACjC,CAAE,CACH,CAAC,CACA,CAAC,cACLnF,IAAA,OAAA8H,QAAA,CAAK,GAAI,CAAAxD,IAAI,CAACiE,WAAW,CAAChE,YAAY,CAAC,CAACqE,kBAAkB,CAAC,CAAC,CAAK,CAAC,cAClE5I,IAAA,OAAI6H,SAAS,CAAC,kBAAkB,CAACgB,KAAK,CAAEN,WAAW,CAAC1D,WAAY,CAAAiD,QAAA,CAC7DS,WAAW,CAAC1D,WAAW,CACtB,CAAC,cACL7E,IAAA,OAAI6H,SAAS,gBAAAf,MAAA,CAAiBV,MAAM,CAAG,CAAC,CAAG,OAAO,CAAG,QAAQ,CAAG,CAAA0B,QAAA,CAC7D3B,cAAc,CAAC1B,IAAI,CAACC,GAAG,CAAC0B,MAAM,CAAC,CAAC,CAC/B,CAAC,cACLpG,IAAA,OAAA8H,QAAA,CACGW,QAAQ,cACPvI,KAAA,QAAK2H,SAAS,CAAC,cAAc,CAACtB,KAAK,CAAE,CAAEuC,eAAe,CAAEL,QAAQ,CAACM,KAAM,CAAE,CAAAjB,QAAA,EACtEW,QAAQ,CAACO,IAAI,CACb,CAAAR,cAAc,SAAdA,cAAc,iBAAdA,cAAc,CAAEzG,MAAM,gBACrB/B,IAAA,SAAM6H,SAAS,CAAC,iBAAiB,CAAAC,QAAA,CAAEU,cAAc,CAACzG,MAAM,CAACkH,WAAW,CAAC,CAAC,CAAO,CAC9E,EACE,CAAC,cAENjJ,IAAA,SAAM6H,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAC,eAAa,CAAM,CACpD,CACC,CAAC,cACL9H,IAAA,OAAA8H,QAAA,CACG,CAAAU,cAAc,SAAdA,cAAc,iBAAdA,cAAc,CAAE1E,UAAU,IAAK+C,SAAS,cACvC7G,IAAA,SACE6H,SAAS,CAAC,kBAAkB,CAC5BtB,KAAK,CAAE,CAAEwC,KAAK,CAAE/B,kBAAkB,CAACwB,cAAc,CAAC1E,UAAU,CAAE,CAAE,CAAAgE,QAAA,CAE/DlB,gBAAgB,CAAC4B,cAAc,CAAC1E,UAAU,CAAC,CACxC,CAAC,cAEP9D,IAAA,SAAM6H,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAC,GAAC,CAAM,CACxC,CACC,CAAC,cACL9H,IAAA,OAAA8H,QAAA,cACE9H,IAAA,QAAK6H,SAAS,CAAC,gBAAgB,CAAAC,QAAA,cAC7B5H,KAAA,WACEgJ,KAAK,CAAE,CAAAV,cAAc,SAAdA,cAAc,iBAAdA,cAAc,CAAE1G,UAAU,GAAI,EAAG,CACxCqG,QAAQ,CAAGC,CAAC,EAAK,CACf,GAAIA,CAAC,CAACC,MAAM,CAACa,KAAK,CAAE,CAClBnE,0BAA0B,CAACwD,WAAW,CAAC9E,EAAE,CAAE2E,CAAC,CAACC,MAAM,CAACa,KAAK,CAAC,CAC5D,CACF,CAAE,CACFrB,SAAS,CAAC,iBAAiB,CAAAC,QAAA,eAE3B9H,IAAA,WAAQkJ,KAAK,CAAC,EAAE,CAAApB,QAAA,CAAC,oBAAkB,CAAQ,CAAC,CAC3CtH,UAAU,CAACmF,GAAG,CAACpC,GAAG,eACjBvD,IAAA,WAAqBkJ,KAAK,CAAE3F,GAAG,CAACE,EAAG,CAAAqE,QAAA,CAAEvE,GAAG,CAACyF,IAAI,EAAhCzF,GAAG,CAACE,EAAqC,CACvD,CAAC,EACI,CAAC,CACN,CAAC,CACJ,CAAC,GAhEE8E,WAAW,CAAC9E,EAiEjB,CAAC,CAET,CAAC,CAAC,CACG,CAAC,EACH,CAAC,CACL,CAAC,CAELrC,oBAAoB,CAAC+H,IAAI,CAAG,CAAC,eAC5BjJ,KAAA,QAAK2H,SAAS,CAAC,eAAe,CAAAC,QAAA,eAC5B5H,KAAA,QAAK2H,SAAS,CAAC,YAAY,CAAAC,QAAA,EACxB1G,oBAAoB,CAAC+H,IAAI,CAAC,0BAC7B,EAAK,CAAC,cACNnJ,IAAA,WACE6H,SAAS,CAAC,iBAAiB,CAC3BG,OAAO,CAAEA,CAAA,GAAM3C,sBAAsB,CAAC+D,KAAK,CAACC,IAAI,CAACjI,oBAAoB,CAAC,CAAE,CACxE6G,QAAQ,CAAEnH,YAAa,CAAAgH,QAAA,CACxB,6BAED,CAAQ,CAAC,cACT9H,IAAA,WACE6H,SAAS,CAAC,mBAAmB,CAC7BG,OAAO,CAAEA,CAAA,GAAM3G,uBAAuB,CAAC,GAAI,CAAAC,GAAG,CAAC,CAAC,CAAE,CAAAwG,QAAA,CACnD,iBAED,CAAQ,CAAC,EACN,CACN,EACE,CAAC,CAEV,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}