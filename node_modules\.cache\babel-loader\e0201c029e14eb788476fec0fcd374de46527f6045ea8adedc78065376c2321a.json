{"ast": null, "code": "/**\n * @license\n * Copyright 2018 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n *\n * =============================================================================\n */\nimport * as tf from '@tensorflow/tfjs-core';\nimport { deepMap, isIterable } from './deep_map';\nexport function deepClone(container) {\n  return deepMap(container, cloneIfTensor);\n}\n// tslint:disable-next-line: no-any\nfunction cloneIfTensor(item) {\n  if (item instanceof tf.Tensor) {\n    return {\n      value: item.clone(),\n      recurse: false\n    };\n  } else if (isIterable(item)) {\n    return {\n      value: null,\n      recurse: true\n    };\n  } else {\n    return {\n      value: item,\n      recurse: false\n    };\n  }\n}", "map": {"version": 3, "names": ["tf", "deepMap", "isIterable", "deepClone", "container", "cloneIfTensor", "item", "Tensor", "value", "clone", "recurse"], "sources": ["C:\\tfjs-data\\src\\util\\deep_clone.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2018 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n *\n * =============================================================================\n */\n\nimport * as tf from '@tensorflow/tfjs-core';\nimport {deepMap, DeepMapResult, isIterable} from './deep_map';\n\nexport function deepClone<T>(container: T): T {\n  return deepMap(container, cloneIfTensor);\n}\n\n// tslint:disable-next-line: no-any\nfunction cloneIfTensor(item: any): DeepMapResult {\n  if (item instanceof tf.Tensor) {\n    return ({value: item.clone(), recurse: false});\n  } else if (isIterable(item)) {\n    return {value: null, recurse: true};\n  } else {\n    return {value: item, recurse: false};\n  }\n}\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;;AAkBA,OAAO,KAAKA,EAAE,MAAM,uBAAuB;AAC3C,SAAQC,OAAO,EAAiBC,UAAU,QAAO,YAAY;AAE7D,OAAM,SAAUC,SAASA,CAAIC,SAAY;EACvC,OAAOH,OAAO,CAACG,SAAS,EAAEC,aAAa,CAAC;AAC1C;AAEA;AACA,SAASA,aAAaA,CAACC,IAAS;EAC9B,IAAIA,IAAI,YAAYN,EAAE,CAACO,MAAM,EAAE;IAC7B,OAAQ;MAACC,KAAK,EAAEF,IAAI,CAACG,KAAK,EAAE;MAAEC,OAAO,EAAE;IAAK,CAAC;GAC9C,MAAM,IAAIR,UAAU,CAACI,IAAI,CAAC,EAAE;IAC3B,OAAO;MAACE,KAAK,EAAE,IAAI;MAAEE,OAAO,EAAE;IAAI,CAAC;GACpC,MAAM;IACL,OAAO;MAACF,KAAK,EAAEF,IAAI;MAAEI,OAAO,EAAE;IAAK,CAAC;;AAExC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}