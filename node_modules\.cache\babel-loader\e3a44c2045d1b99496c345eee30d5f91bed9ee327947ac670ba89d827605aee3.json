{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nconst randomUUID = typeof crypto !== 'undefined' && crypto.randomUUID && crypto.randomUUID.bind(crypto);\nvar _default = {\n  randomUUID\n};\nexports.default = _default;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "default", "randomUUID", "crypto", "bind", "_default"], "sources": ["C:/tmsft/node_modules/natural/node_modules/uuid/dist/commonjs-browser/native.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nconst randomUUID = typeof crypto !== 'undefined' && crypto.randomUUID && crypto.randomUUID.bind(crypto);\nvar _default = {\n  randomUUID\n};\nexports.default = _default;"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,OAAO,GAAG,KAAK,CAAC;AACxB,MAAMC,UAAU,GAAG,OAAOC,MAAM,KAAK,WAAW,IAAIA,MAAM,CAACD,UAAU,IAAIC,MAAM,CAACD,UAAU,CAACE,IAAI,CAACD,MAAM,CAAC;AACvG,IAAIE,QAAQ,GAAG;EACbH;AACF,CAAC;AACDH,OAAO,CAACE,OAAO,GAAGI,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}