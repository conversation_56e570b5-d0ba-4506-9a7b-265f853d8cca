{"ast": null, "code": "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { Acos } from '../kernel_names';\nimport { cast } from '../ops/cast';\nimport { div } from '../ops/div';\nimport { neg } from '../ops/neg';\nimport { scalar } from '../ops/scalar';\nimport { sqrt } from '../ops/sqrt';\nimport { square } from '../ops/square';\nimport { sub } from '../ops/sub';\nexport const acosGradConfig = {\n  kernelName: Acos,\n  inputsToSave: ['x'],\n  gradFunc: (dy, saved) => {\n    const [x] = saved;\n    return {\n      x: () => {\n        const a = square(cast(x, 'float32'));\n        const b = sqrt(sub(scalar(1), a));\n        return neg(div(dy, b));\n      }\n    };\n  }\n};", "map": {"version": 3, "names": ["Acos", "cast", "div", "neg", "scalar", "sqrt", "square", "sub", "acosGradConfig", "kernelName", "inputsToSave", "grad<PERSON>unc", "dy", "saved", "x", "a", "b"], "sources": ["C:\\tfjs-core\\src\\gradients\\Acos_grad.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {Acos} from '../kernel_names';\nimport {GradConfig} from '../kernel_registry';\nimport {cast} from '../ops/cast';\nimport {div} from '../ops/div';\nimport {neg} from '../ops/neg';\nimport {scalar} from '../ops/scalar';\nimport {sqrt} from '../ops/sqrt';\nimport {square} from '../ops/square';\nimport {sub} from '../ops/sub';\nimport {Tensor} from '../tensor';\n\nexport const acosGradConfig: GradConfig = {\n  kernelName: Acos,\n  inputsToSave: ['x'],\n  gradFunc: (dy: Tensor, saved: Tensor[]) => {\n    const [x] = saved;\n\n    return {\n      x: () => {\n        const a = square(cast(x, 'float32'));\n        const b = sqrt(sub(scalar(1), a));\n        return neg(div(dy, b));\n      }\n\n    };\n  }\n};\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAiBA,SAAQA,IAAI,QAAO,iBAAiB;AAEpC,SAAQC,IAAI,QAAO,aAAa;AAChC,SAAQC,GAAG,QAAO,YAAY;AAC9B,SAAQC,GAAG,QAAO,YAAY;AAC9B,SAAQC,MAAM,QAAO,eAAe;AACpC,SAAQC,IAAI,QAAO,aAAa;AAChC,SAAQC,MAAM,QAAO,eAAe;AACpC,SAAQC,GAAG,QAAO,YAAY;AAG9B,OAAO,MAAMC,cAAc,GAAe;EACxCC,UAAU,EAAET,IAAI;EAChBU,YAAY,EAAE,CAAC,GAAG,CAAC;EACnBC,QAAQ,EAAEA,CAACC,EAAU,EAAEC,KAAe,KAAI;IACxC,MAAM,CAACC,CAAC,CAAC,GAAGD,KAAK;IAEjB,OAAO;MACLC,CAAC,EAAEA,CAAA,KAAK;QACN,MAAMC,CAAC,GAAGT,MAAM,CAACL,IAAI,CAACa,CAAC,EAAE,SAAS,CAAC,CAAC;QACpC,MAAME,CAAC,GAAGX,IAAI,CAACE,GAAG,CAACH,MAAM,CAAC,CAAC,CAAC,EAAEW,CAAC,CAAC,CAAC;QACjC,OAAOZ,GAAG,CAACD,GAAG,CAACU,EAAE,EAAEI,CAAC,CAAC,CAAC;MACxB;KAED;EACH;CACD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}