{"ast": null, "code": "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { backend_util, Min, util } from '@tensorflow/tfjs-core';\nimport { reduce } from '../kernel_utils/reduce';\nimport { reshape } from './Reshape';\nimport { transpose } from './Transpose';\nexport function min(args) {\n  const {\n    inputs,\n    backend,\n    attrs\n  } = args;\n  const {\n    x\n  } = inputs;\n  const {\n    axis,\n    keepDims\n  } = attrs;\n  const xRank = x.shape.length;\n  const origAxes = util.parseAxisParam(axis, x.shape);\n  let axes = origAxes;\n  const permutedAxes = backend_util.getAxesPermutation(axes, xRank);\n  let permutedX = x;\n  if (permutedAxes != null) {\n    permutedX = transpose({\n      inputs: {\n        x\n      },\n      backend,\n      attrs: {\n        perm: permutedAxes\n      }\n    });\n    axes = backend_util.getInnerMostAxes(axes.length, x.shape.length);\n  }\n  backend_util.assertAxesAreInnerMostDims('min', axes, xRank);\n  const [outShape, reduceShape] = backend_util.computeOutAndReduceShapes(permutedX.shape, axes);\n  const inSize = util.sizeFromShape(reduceShape);\n  const a2D = reshape({\n    inputs: {\n      x: permutedX\n    },\n    backend,\n    attrs: {\n      shape: [-1, inSize]\n    }\n  });\n  const reduced = reduce(a2D, a2D.dtype, 'min', backend);\n  let res;\n  if (keepDims) {\n    const newShape = backend_util.expandShapeToKeepDim(outShape, origAxes);\n    res = reshape({\n      inputs: {\n        x: reduced\n      },\n      backend,\n      attrs: {\n        shape: newShape\n      }\n    });\n  } else {\n    res = reshape({\n      inputs: {\n        x: reduced\n      },\n      backend,\n      attrs: {\n        shape: outShape\n      }\n    });\n  }\n  backend.disposeIntermediateTensorInfo(a2D);\n  backend.disposeIntermediateTensorInfo(reduced);\n  if (permutedAxes != null) {\n    backend.disposeIntermediateTensorInfo(permutedX);\n  }\n  return res;\n}\nexport const minConfig = {\n  kernelName: Min,\n  backendName: 'webgl',\n  kernelFunc: min\n};", "map": {"version": 3, "names": ["backend_util", "Min", "util", "reduce", "reshape", "transpose", "min", "args", "inputs", "backend", "attrs", "x", "axis", "keepDims", "xRank", "shape", "length", "origAxes", "parseAxisParam", "axes", "permutedAxes", "getAxesPermutation", "permutedX", "perm", "getInnerMostAxes", "assertAxesAreInnerMostDims", "outShape", "reduceShape", "computeOutAndReduceShapes", "inSize", "sizeFromShape", "a2D", "reduced", "dtype", "res", "newShape", "expandShapeToKeepDim", "disposeIntermediateTensorInfo", "minConfig", "kernelName", "backendName", "kernelFunc"], "sources": ["C:\\tfjs-backend-webgl\\src\\kernels\\Min.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {backend_util, KernelConfig, KernelFunc, Min, MinAttrs, MinInputs, TensorInfo, util} from '@tensorflow/tfjs-core';\n\nimport {MathBackendWebGL} from '../backend_webgl';\nimport {reduce} from '../kernel_utils/reduce';\n\nimport {reshape} from './Reshape';\nimport {transpose} from './Transpose';\n\nexport function min(\n    args: {inputs: MinInputs, backend: MathBackendWebGL, attrs: MinAttrs}):\n    TensorInfo {\n  const {inputs, backend, attrs} = args;\n  const {x} = inputs;\n  const {axis, keepDims} = attrs;\n\n  const xRank = x.shape.length;\n\n  const origAxes = util.parseAxisParam(axis, x.shape);\n  let axes = origAxes;\n  const permutedAxes = backend_util.getAxesPermutation(axes, xRank);\n  let permutedX = x;\n  if (permutedAxes != null) {\n    permutedX = transpose({inputs: {x}, backend, attrs: {perm: permutedAxes}});\n    axes = backend_util.getInnerMostAxes(axes.length, x.shape.length);\n  }\n\n  backend_util.assertAxesAreInnerMostDims('min', axes, xRank);\n  const [outShape, reduceShape] =\n      backend_util.computeOutAndReduceShapes(permutedX.shape, axes);\n  const inSize = util.sizeFromShape(reduceShape);\n  const a2D =\n      reshape({inputs: {x: permutedX}, backend, attrs: {shape: [-1, inSize]}});\n  const reduced = reduce(a2D, a2D.dtype, 'min', backend);\n\n  let res;\n  if (keepDims) {\n    const newShape = backend_util.expandShapeToKeepDim(outShape, origAxes);\n    res = reshape({inputs: {x: reduced}, backend, attrs: {shape: newShape}});\n  } else {\n    res = reshape({inputs: {x: reduced}, backend, attrs: {shape: outShape}});\n  }\n\n  backend.disposeIntermediateTensorInfo(a2D);\n  backend.disposeIntermediateTensorInfo(reduced);\n\n  if (permutedAxes != null) {\n    backend.disposeIntermediateTensorInfo(permutedX);\n  }\n\n  return res;\n}\n\nexport const minConfig: KernelConfig = {\n  kernelName: Min,\n  backendName: 'webgl',\n  kernelFunc: min as unknown as KernelFunc\n};\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAiBA,SAAQA,YAAY,EAA4BC,GAAG,EAAmCC,IAAI,QAAO,uBAAuB;AAGxH,SAAQC,MAAM,QAAO,wBAAwB;AAE7C,SAAQC,OAAO,QAAO,WAAW;AACjC,SAAQC,SAAS,QAAO,aAAa;AAErC,OAAM,SAAUC,GAAGA,CACfC,IAAqE;EAEvE,MAAM;IAACC,MAAM;IAAEC,OAAO;IAAEC;EAAK,CAAC,GAAGH,IAAI;EACrC,MAAM;IAACI;EAAC,CAAC,GAAGH,MAAM;EAClB,MAAM;IAACI,IAAI;IAAEC;EAAQ,CAAC,GAAGH,KAAK;EAE9B,MAAMI,KAAK,GAAGH,CAAC,CAACI,KAAK,CAACC,MAAM;EAE5B,MAAMC,QAAQ,GAAGf,IAAI,CAACgB,cAAc,CAACN,IAAI,EAAED,CAAC,CAACI,KAAK,CAAC;EACnD,IAAII,IAAI,GAAGF,QAAQ;EACnB,MAAMG,YAAY,GAAGpB,YAAY,CAACqB,kBAAkB,CAACF,IAAI,EAAEL,KAAK,CAAC;EACjE,IAAIQ,SAAS,GAAGX,CAAC;EACjB,IAAIS,YAAY,IAAI,IAAI,EAAE;IACxBE,SAAS,GAAGjB,SAAS,CAAC;MAACG,MAAM,EAAE;QAACG;MAAC,CAAC;MAAEF,OAAO;MAAEC,KAAK,EAAE;QAACa,IAAI,EAAEH;MAAY;IAAC,CAAC,CAAC;IAC1ED,IAAI,GAAGnB,YAAY,CAACwB,gBAAgB,CAACL,IAAI,CAACH,MAAM,EAAEL,CAAC,CAACI,KAAK,CAACC,MAAM,CAAC;;EAGnEhB,YAAY,CAACyB,0BAA0B,CAAC,KAAK,EAAEN,IAAI,EAAEL,KAAK,CAAC;EAC3D,MAAM,CAACY,QAAQ,EAAEC,WAAW,CAAC,GACzB3B,YAAY,CAAC4B,yBAAyB,CAACN,SAAS,CAACP,KAAK,EAAEI,IAAI,CAAC;EACjE,MAAMU,MAAM,GAAG3B,IAAI,CAAC4B,aAAa,CAACH,WAAW,CAAC;EAC9C,MAAMI,GAAG,GACL3B,OAAO,CAAC;IAACI,MAAM,EAAE;MAACG,CAAC,EAAEW;IAAS,CAAC;IAAEb,OAAO;IAAEC,KAAK,EAAE;MAACK,KAAK,EAAE,CAAC,CAAC,CAAC,EAAEc,MAAM;IAAC;EAAC,CAAC,CAAC;EAC5E,MAAMG,OAAO,GAAG7B,MAAM,CAAC4B,GAAG,EAAEA,GAAG,CAACE,KAAK,EAAE,KAAK,EAAExB,OAAO,CAAC;EAEtD,IAAIyB,GAAG;EACP,IAAIrB,QAAQ,EAAE;IACZ,MAAMsB,QAAQ,GAAGnC,YAAY,CAACoC,oBAAoB,CAACV,QAAQ,EAAET,QAAQ,CAAC;IACtEiB,GAAG,GAAG9B,OAAO,CAAC;MAACI,MAAM,EAAE;QAACG,CAAC,EAAEqB;MAAO,CAAC;MAAEvB,OAAO;MAAEC,KAAK,EAAE;QAACK,KAAK,EAAEoB;MAAQ;IAAC,CAAC,CAAC;GACzE,MAAM;IACLD,GAAG,GAAG9B,OAAO,CAAC;MAACI,MAAM,EAAE;QAACG,CAAC,EAAEqB;MAAO,CAAC;MAAEvB,OAAO;MAAEC,KAAK,EAAE;QAACK,KAAK,EAAEW;MAAQ;IAAC,CAAC,CAAC;;EAG1EjB,OAAO,CAAC4B,6BAA6B,CAACN,GAAG,CAAC;EAC1CtB,OAAO,CAAC4B,6BAA6B,CAACL,OAAO,CAAC;EAE9C,IAAIZ,YAAY,IAAI,IAAI,EAAE;IACxBX,OAAO,CAAC4B,6BAA6B,CAACf,SAAS,CAAC;;EAGlD,OAAOY,GAAG;AACZ;AAEA,OAAO,MAAMI,SAAS,GAAiB;EACrCC,UAAU,EAAEtC,GAAG;EACfuC,WAAW,EAAE,OAAO;EACpBC,UAAU,EAAEnC;CACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}