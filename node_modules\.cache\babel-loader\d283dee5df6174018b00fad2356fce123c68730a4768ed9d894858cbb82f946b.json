{"ast": null, "code": "import cb from './_cb.js';\nimport isArrayLike from './_isArrayLike.js';\nimport keys from './keys.js';\n\n// Determine whether all of the elements pass a truth test.\nexport default function every(obj, predicate, context) {\n  predicate = cb(predicate, context);\n  var _keys = !isArrayLike(obj) && keys(obj),\n    length = (_keys || obj).length;\n  for (var index = 0; index < length; index++) {\n    var currentKey = _keys ? _keys[index] : index;\n    if (!predicate(obj[currentKey], currentKey, obj)) return false;\n  }\n  return true;\n}", "map": {"version": 3, "names": ["cb", "isArrayLike", "keys", "every", "obj", "predicate", "context", "_keys", "length", "index", "current<PERSON><PERSON>"], "sources": ["C:/tmsft/node_modules/underscore/modules/every.js"], "sourcesContent": ["import cb from './_cb.js';\nimport isArrayLike from './_isArrayLike.js';\nimport keys from './keys.js';\n\n// Determine whether all of the elements pass a truth test.\nexport default function every(obj, predicate, context) {\n  predicate = cb(predicate, context);\n  var _keys = !isArrayLike(obj) && keys(obj),\n      length = (_keys || obj).length;\n  for (var index = 0; index < length; index++) {\n    var currentKey = _keys ? _keys[index] : index;\n    if (!predicate(obj[currentKey], currentKey, obj)) return false;\n  }\n  return true;\n}\n"], "mappings": "AAAA,OAAOA,EAAE,MAAM,UAAU;AACzB,OAAOC,WAAW,MAAM,mBAAmB;AAC3C,OAAOC,IAAI,MAAM,WAAW;;AAE5B;AACA,eAAe,SAASC,KAAKA,CAACC,GAAG,EAAEC,SAAS,EAAEC,OAAO,EAAE;EACrDD,SAAS,GAAGL,EAAE,CAACK,SAAS,EAAEC,OAAO,CAAC;EAClC,IAAIC,KAAK,GAAG,CAACN,WAAW,CAACG,GAAG,CAAC,IAAIF,IAAI,CAACE,GAAG,CAAC;IACtCI,MAAM,GAAG,CAACD,KAAK,IAAIH,GAAG,EAAEI,MAAM;EAClC,KAAK,IAAIC,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAGD,MAAM,EAAEC,KAAK,EAAE,EAAE;IAC3C,IAAIC,UAAU,GAAGH,KAAK,GAAGA,KAAK,CAACE,KAAK,CAAC,GAAGA,KAAK;IAC7C,IAAI,CAACJ,SAAS,CAACD,GAAG,CAACM,UAAU,CAAC,EAAEA,UAAU,EAAEN,GAAG,CAAC,EAAE,OAAO,KAAK;EAChE;EACA,OAAO,IAAI;AACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}