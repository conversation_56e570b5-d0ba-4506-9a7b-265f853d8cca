{"ast": null, "code": "export const Abs = 'Abs';\nexport const Acos = 'Acos';\nexport const Acosh = 'Acosh';\nexport const Add = 'Add';\nexport const AddN = 'AddN';\nexport const All = 'All';\nexport const Any = 'Any';\nexport const ArgMax = 'ArgMax';\nexport const ArgMin = 'ArgMin';\nexport const Asin = 'Asin';\nexport const Asinh = 'Asinh';\nexport const Atan = 'Atan';\nexport const Atanh = 'Atanh';\nexport const Atan2 = 'Atan2';\nexport const AvgPool = 'AvgPool';\nexport const AvgPoolGrad = 'AvgPoolGrad';\nexport const AvgPool3D = 'AvgPool3D';\nexport const AvgPool3DGrad = 'AvgPool3DGrad';\nexport const BatchMatMul = 'BatchMatMul';\nexport const BatchToSpaceND = 'BatchToSpaceND';\nexport const Bincount = 'Bincount';\nexport const BitwiseAnd = 'BitwiseAnd';\nexport const BroadcastTo = 'BroadcastTo';\nexport const BroadcastArgs = 'BroadcastArgs';\nexport const Cast = 'Cast';\nexport const Ceil = 'Ceil';\nexport const ClipByValue = 'ClipByValue';\nexport const Complex = 'Complex';\nexport const ComplexAbs = 'ComplexAbs';\nexport const Concat = 'Concat';\nexport const Conv2D = 'Conv2D';\nexport const Conv2DBackpropFilter = 'Conv2DBackpropFilter';\nexport const Conv2DBackpropInput = 'Conv2DBackpropInput';\nexport const Conv3D = 'Conv3D';\nexport const Conv3DBackpropFilterV2 = 'Conv3DBackpropFilterV2';\nexport const Conv3DBackpropInputV2 = 'Conv3DBackpropInputV2';\nexport const Cos = 'Cos';\nexport const Cosh = 'Cosh';\nexport const Cumprod = 'Cumprod';\nexport const Cumsum = 'Cumsum';\nexport const CropAndResize = 'CropAndResize';\nexport const DenseBincount = 'DenseBincount';\nexport const DepthToSpace = 'DepthToSpace';\nexport const DepthwiseConv2dNative = 'DepthwiseConv2dNative';\nexport const DepthwiseConv2dNativeBackpropFilter = 'DepthwiseConv2dNativeBackpropFilter';\nexport const DepthwiseConv2dNativeBackpropInput = 'DepthwiseConv2dNativeBackpropInput';\nexport const Diag = 'Diag';\nexport const Dilation2D = 'Dilation2D';\nexport const Dilation2DBackpropInput = 'Dilation2DBackpropInput';\nexport const Dilation2DBackpropFilter = 'Dilation2DBackpropFilter';\nexport const Draw = 'Draw';\nexport const RealDiv = 'RealDiv';\nexport const Einsum = 'Einsum';\nexport const Elu = 'Elu';\nexport const EluGrad = 'EluGrad';\nexport const Erf = 'Erf';\nexport const Equal = 'Equal';\nexport const Exp = 'Exp';\nexport const ExpandDims = 'ExpandDims';\nexport const Expm1 = 'Expm1';\nexport const FFT = 'FFT';\nexport const Fill = 'Fill';\nexport const FlipLeftRight = 'FlipLeftRight';\nexport const Floor = 'Floor';\nexport const FloorDiv = 'FloorDiv';\nexport const FusedBatchNorm = 'FusedBatchNorm';\nexport const GatherV2 = 'GatherV2';\nexport const GatherNd = 'GatherNd';\nexport const Greater = 'Greater';\nexport const GreaterEqual = 'GreaterEqual';\nexport const Identity = 'Identity';\nexport const IFFT = 'IFFT';\nexport const Imag = 'Imag';\nexport const IsFinite = 'IsFinite';\nexport const IsInf = 'IsInf';\nexport const IsNan = 'IsNan';\nexport const LeakyRelu = 'LeakyRelu';\nexport const Less = 'Less';\nexport const LessEqual = 'LessEqual';\nexport const LinSpace = 'LinSpace';\nexport const Log = 'Log';\nexport const Log1p = 'Log1p';\nexport const LogicalAnd = 'LogicalAnd';\nexport const LogicalNot = 'LogicalNot';\nexport const LogicalOr = 'LogicalOr';\nexport const LogicalXor = 'LogicalXor';\nexport const LogSoftmax = 'LogSoftmax';\nexport const LowerBound = 'LowerBound';\nexport const LRN = 'LRN';\nexport const LRNGrad = 'LRNGrad';\nexport const MatrixBandPart = 'MatrixBandPart';\nexport const Max = 'Max';\nexport const Maximum = 'Maximum';\nexport const MaxPool = 'MaxPool';\nexport const MaxPoolGrad = 'MaxPoolGrad';\nexport const MaxPool3D = 'MaxPool3D';\nexport const MaxPool3DGrad = 'MaxPool3DGrad';\nexport const MaxPoolWithArgmax = 'MaxPoolWithArgmax';\nexport const Mean = 'Mean';\nexport const Min = 'Min';\nexport const Minimum = 'Minimum';\nexport const MirrorPad = 'MirrorPad';\nexport const Mod = 'Mod';\nexport const Multinomial = 'Multinomial';\nexport const Multiply = 'Multiply';\nexport const Neg = 'Neg';\nexport const NotEqual = 'NotEqual';\nexport const NonMaxSuppressionV3 = 'NonMaxSuppressionV3';\nexport const NonMaxSuppressionV4 = 'NonMaxSuppressionV4';\nexport const NonMaxSuppressionV5 = 'NonMaxSuppressionV5';\nexport const OnesLike = 'OnesLike';\nexport const OneHot = 'OneHot';\nexport const Pack = 'Pack';\nexport const PadV2 = 'PadV2';\nexport const Pool = 'Pool';\nexport const Pow = 'Pow';\nexport const Prelu = 'Prelu';\nexport const Prod = 'Prod';\nexport const RaggedGather = 'RaggedGather';\nexport const RaggedRange = 'RaggedRange';\nexport const RaggedTensorToTensor = 'RaggedTensorToTensor';\nexport const Range = 'Range';\nexport const Real = 'Real';\nexport const Reciprocal = 'Reciprocal';\nexport const Relu = 'Relu';\nexport const Reshape = 'Reshape';\nexport const ResizeNearestNeighbor = 'ResizeNearestNeighbor';\nexport const ResizeNearestNeighborGrad = 'ResizeNearestNeighborGrad';\nexport const ResizeBilinear = 'ResizeBilinear';\nexport const ResizeBilinearGrad = 'ResizeBilinearGrad';\nexport const Relu6 = 'Relu6';\nexport const Reverse = 'Reverse';\nexport const Round = 'Round';\nexport const Rsqrt = 'Rsqrt';\nexport const ScatterNd = 'ScatterNd';\nexport const TensorScatterUpdate = 'TensorScatterUpdate';\nexport const SearchSorted = 'SearchSorted';\nexport const Select = 'Select';\nexport const Selu = 'Selu';\nexport const Slice = 'Slice';\nexport const Sin = 'Sin';\nexport const Sinh = 'Sinh';\nexport const Sign = 'Sign';\nexport const Sigmoid = 'Sigmoid';\nexport const Softplus = 'Softplus';\nexport const Sqrt = 'Sqrt';\nexport const Sum = 'Sum';\nexport const SpaceToBatchND = 'SpaceToBatchND';\nexport const SplitV = 'SplitV';\nexport const Softmax = 'Softmax';\nexport const SparseFillEmptyRows = 'SparseFillEmptyRows';\nexport const SparseReshape = 'SparseReshape';\nexport const SparseSegmentMean = 'SparseSegmentMean';\nexport const SparseSegmentSum = 'SparseSegmentSum';\nexport const SparseToDense = 'SparseToDense';\nexport const SquaredDifference = 'SquaredDifference';\nexport const Square = 'Square';\nexport const StaticRegexReplace = 'StaticRegexReplace';\nexport const StridedSlice = 'StridedSlice';\nexport const StringNGrams = 'StringNGrams';\nexport const StringSplit = 'StringSplit';\nexport const StringToHashBucketFast = 'StringToHashBucketFast';\nexport const Sub = 'Sub';\nexport const Tan = 'Tan';\nexport const Tanh = 'Tanh';\nexport const Tile = 'Tile';\nexport const TopK = 'TopK';\nexport const Transform = 'Transform';\nexport const Transpose = 'Transpose';\nexport const Unique = 'Unique';\nexport const Unpack = 'Unpack';\nexport const UnsortedSegmentSum = 'UnsortedSegmentSum';\nexport const UpperBound = 'UpperBound';\nexport const ZerosLike = 'ZerosLike';\n/**\n * TensorFlow.js-only kernels\n */\nexport const Step = 'Step';\nexport const FromPixels = 'FromPixels';\nexport const RotateWithOffset = 'RotateWithOffset';\nexport const _FusedMatMul = '_FusedMatMul';\nexport const FusedConv2D = 'FusedConv2D';\nexport const FusedDepthwiseConv2D = 'FusedDepthwiseConv2D';", "map": {"version": 3, "names": ["Abs", "Acos", "Acosh", "Add", "AddN", "All", "Any", "ArgMax", "<PERSON>rg<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Atan2", "AvgPool", "AvgPoolGrad", "AvgPool3D", "AvgPool3DGrad", "BatchMatMul", "BatchToSpaceND", "Bincount", "BitwiseAnd", "BroadcastTo", "BroadcastArgs", "Cast", "Ceil", "ClipByValue", "Complex", "ComplexAbs", "Concat", "Conv2D", "Conv2DBackpropFilter", "Conv2DBackpropInput", "Conv3D", "Conv3DBackpropFilterV2", "Conv3DBackpropInputV2", "Cos", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Cumsum", "CropAndResize", "DenseBincount", "DepthToSpace", "DepthwiseConv2dNative", "DepthwiseConv2dNativeBackpropFilter", "DepthwiseConv2dNativeBackpropInput", "Diag", "Dilation2D", "Dilation2DBackpropInput", "Dilation2DBackpropFilter", "Draw", "RealDiv", "Einsum", "<PERSON><PERSON>", "EluGrad", "E<PERSON>", "Equal", "Exp", "ExpandDims", "Expm1", "FFT", "Fill", "FlipLeftRight", "Floor", "FloorDiv", "FusedBatchNorm", "GatherV2", "GatherNd", "Greater", "GreaterEqual", "Identity", "IFFT", "Imag", "IsFinite", "IsInf", "IsNan", "LeakyRelu", "Less", "LessEqual", "LinSpace", "Log", "Log1p", "LogicalAnd", "LogicalNot", "LogicalOr", "LogicalXor", "LogSoftmax", "LowerBound", "LRN", "LRNGrad", "MatrixBandPart", "Max", "Maximum", "MaxPool", "MaxPoolGrad", "MaxPool3D", "MaxPool3DGrad", "MaxPoolWithArgmax", "Mean", "Min", "Minimum", "MirrorPad", "Mod", "Multinomial", "Multiply", "Neg", "NotEqual", "NonMaxSuppressionV3", "NonMaxSuppressionV4", "NonMaxSuppressionV5", "OnesLike", "OneHot", "Pack", "PadV2", "Pool", "<PERSON>w", "<PERSON><PERSON>", "Prod", "<PERSON><PERSON><PERSON><PERSON>", "Ra<PERSON><PERSON><PERSON><PERSON>", "RaggedTensorToTensor", "Range", "Real", "Reciprocal", "<PERSON><PERSON>", "Reshape", "ResizeNearestNeighbor", "ResizeNearestNeighborGrad", "ResizeBilinear", "ResizeBilinearGrad", "Relu6", "Reverse", "Round", "Rsqrt", "ScatterNd", "TensorScatterUpdate", "SearchSorted", "Select", "<PERSON><PERSON>", "Slice", "Sin", "Sin<PERSON>", "Sign", "<PERSON><PERSON><PERSON><PERSON>", "Softplus", "Sqrt", "Sum", "SpaceToBatchND", "SplitV", "Softmax", "SparseFillEmptyRows", "SparseReshape", "SparseSegmentMean", "SparseSegmentSum", "SparseToDense", "SquaredDifference", "Square", "StaticRegexReplace", "StridedSlice", "StringNGrams", "StringSplit", "StringToHashBucketFast", "Sub", "<PERSON>", "<PERSON><PERSON>", "Tile", "TopK", "Transform", "Transpose", "Unique", "Unpack", "UnsortedSegmentSum", "UpperBound", "ZerosLike", "Step", "FromPixels", "RotateWithOffset", "_FusedMatMul", "FusedConv2D", "FusedDepthwiseConv2D"], "sources": ["C:\\tfjs-core\\src\\kernel_names.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n// Allow UpperCamelCase variable names\n// tslint:disable: variable-name\n// Unfortunately just enabling PascalCase per file (tslint:enable:\n// allow-pascal-case) doesn't work.\nimport {NamedTensorInfoMap} from './kernel_registry';\nimport {ExplicitPadding} from './ops/conv_util';\nimport {Activation} from './ops/fused_types';\nimport {TensorInfo} from './tensor_info';\nimport {DataType, DrawOptions, PixelData} from './types';\n\nexport const Abs = 'Abs';\nexport type AbsInputs = UnaryInputs;\n\nexport const Acos = 'Acos';\nexport type AcosInputs = UnaryInputs;\n\nexport const Acosh = 'Acosh';\nexport type AcoshInputs = UnaryInputs;\n\nexport const Add = 'Add';\nexport type AddInputs = BinaryInputs;\n\nexport const AddN = 'AddN';\nexport type AddNInputs = TensorInfo[];\n\nexport const All = 'All';\nexport type AllInputs = Pick<NamedTensorInfoMap, 'x'>;\nexport interface AllAttrs {\n  axis: number|number[];\n  keepDims: boolean;\n}\n\nexport const Any = 'Any';\nexport type AnyInputs = Pick<NamedTensorInfoMap, 'x'>;\nexport interface AnyAttrs {\n  axis: number|number[];\n  keepDims: boolean;\n}\n\nexport const ArgMax = 'ArgMax';\nexport type ArgMaxInputs = Pick<NamedTensorInfoMap, 'x'>;\nexport interface ArgMaxAttrs {\n  axis: number;\n}\n\nexport const ArgMin = 'ArgMin';\nexport type ArgMinInputs = Pick<NamedTensorInfoMap, 'x'>;\nexport interface ArgMinAttrs {\n  axis: number;\n}\n\nexport const Asin = 'Asin';\nexport type AsinInputs = UnaryInputs;\n\nexport const Asinh = 'Asinh';\nexport type AsinhInputs = UnaryInputs;\n\nexport const Atan = 'Atan';\nexport type AtanInputs = UnaryInputs;\n\nexport const Atanh = 'Atanh';\nexport type AtanhInputs = UnaryInputs;\n\nexport const Atan2 = 'Atan2';\nexport type Atan2Inputs = BinaryInputs;\n\nexport const AvgPool = 'AvgPool';\nexport type AvgPoolInputs = Pick<NamedTensorInfoMap, 'x'>;\nexport interface AvgPoolAttrs {\n  filterSize: [number, number]|number;\n  strides: [number, number]|number;\n  pad: 'valid'|'same'|number|ExplicitPadding;\n  dimRoundingMode?: 'floor'|'round'|'ceil';\n}\n\nexport const AvgPoolGrad = 'AvgPoolGrad';\nexport type AvgPoolGradInputs = Pick<NamedTensorInfoMap, 'dy'|'input'>;\nexport interface AvgPoolGradAttrs {\n  filterSize: [number, number]|number;\n  strides: [number, number]|number;\n  pad: 'valid'|'same'|number|ExplicitPadding;\n}\n\nexport const AvgPool3D = 'AvgPool3D';\nexport type AvgPool3DInputs = Pick<NamedTensorInfoMap, 'x'>;\nexport interface AvgPool3DAttrs {\n  filterSize: [number, number, number]|number;\n  strides: [number, number, number]|number;\n  pad: 'valid'|'same'|number;\n  dimRoundingMode?: 'floor'|'round'|'ceil';\n  dataFormat: 'NDHWC'|'NCDHW';\n}\n\nexport const AvgPool3DGrad = 'AvgPool3DGrad';\nexport type AvgPool3DGradInputs = Pick<NamedTensorInfoMap, 'dy'|'input'>;\nexport interface AvgPool3DGradAttrs {\n  filterSize: [number, number, number]|number;\n  strides: [number, number, number]|number;\n  pad: 'valid'|'same'|number;\n  dimRoundingMode?: 'floor'|'round'|'ceil';\n}\n\nexport const BatchMatMul = 'BatchMatMul';\nexport type BatchMatMulInputs = Pick<NamedTensorInfoMap, 'a'|'b'>;\nexport interface BatchMatMulAttrs {\n  transposeA: boolean;\n  transposeB: boolean;\n}\n\nexport const BatchToSpaceND = 'BatchToSpaceND';\nexport type BatchToSpaceNDInputs = Pick<NamedTensorInfoMap, 'x'>;\nexport interface BatchToSpaceNDAttrs {\n  blockShape: number[];\n  crops: number[][];\n}\n\nexport type BinaryInputs = Pick<NamedTensorInfoMap, 'a'|'b'>;\n\nexport const Bincount = 'Bincount';\nexport type BincountInputs = Pick<NamedTensorInfoMap, 'x'|'weights'>;\nexport interface BincountAttrs {\n  size: number;\n}\n\nexport const BitwiseAnd = 'BitwiseAnd';\nexport type BitwiseAndInputs = BinaryInputs;\n\nexport const BroadcastTo = 'BroadcastTo';\nexport type BroadcastToInputs = Pick<NamedTensorInfoMap, 'x'>;\nexport interface BroadCastToAttrs {\n  shape: number[];\n  inputShape: number[];  // for gradient\n}\n\nexport const BroadcastArgs = 'BroadcastArgs';\nexport type BroadcastArgsInputs = Pick<NamedTensorInfoMap, 's0'|'s1'>;\n\nexport const Cast = 'Cast';\nexport type CastInputs = UnaryInputs;\nexport interface CastAttrs {\n  dtype: DataType;\n}\n\nexport const Ceil = 'Ceil';\nexport type CeilInputs = UnaryInputs;\n\nexport const ClipByValue = 'ClipByValue';\nexport type ClipByValueInputs = UnaryInputs;\nexport interface ClipByValueAttrs {\n  clipValueMin: number;\n  clipValueMax: number;\n}\n\nexport const Complex = 'Complex';\nexport type ComplexInputs = Pick<NamedTensorInfoMap, 'real'|'imag'>;\n\nexport const ComplexAbs = 'ComplexAbs';\nexport type ComplexAbsInputs = UnaryInputs;\n\nexport const Concat = 'Concat';\nexport type ConcatInputs = TensorInfo[];\nexport interface ConcatAttrs {\n  axis: number;\n}\n\nexport const Conv2D = 'Conv2D';\nexport type Conv2DInputs = Pick<NamedTensorInfoMap, 'x'|'filter'>;\nexport interface Conv2DAttrs {\n  strides: [number, number]|number;\n  pad: 'valid'|'same'|number|ExplicitPadding;\n  dataFormat: 'NHWC'|'NCHW';\n  dilations: [number, number]|number;\n  dimRoundingMode?: 'floor'|'round'|'ceil';\n}\n\nexport const Conv2DBackpropFilter = 'Conv2DBackpropFilter';\nexport type Conv2DBackpropFilterInputs = Pick<NamedTensorInfoMap, 'x'|'dy'>;\nexport interface Conv2DBackpropFilterAttrs {\n  strides: [number, number]|number;\n  pad: 'valid'|'same'|number|ExplicitPadding;\n  dataFormat: 'NHWC'|'NCHW';\n  dimRoundingMode?: 'floor'|'round'|'ceil';\n  filterShape: [number, number, number, number];\n}\n\nexport const Conv2DBackpropInput = 'Conv2DBackpropInput';\nexport type Conv2DBackpropInputInputs = Pick<NamedTensorInfoMap, 'dy'|'filter'>;\nexport interface Conv2DBackpropInputAttrs {\n  strides: [number, number]|number;\n  pad: 'valid'|'same'|number|ExplicitPadding;\n  dataFormat: 'NHWC'|'NCHW';\n  dimRoundingMode?: 'floor'|'round'|'ceil';\n  inputShape: [number, number, number, number];\n}\n\nexport const Conv3D = 'Conv3D';\nexport type Conv3DInputs = Pick<NamedTensorInfoMap, 'x'|'filter'>;\nexport interface Conv3DAttrs {\n  strides: [number, number, number]|number;\n  pad: 'valid'|'same';\n  dataFormat: 'NDHWC'|'NCDHW';\n  dilations: [number, number, number]|number;\n}\n\nexport const Conv3DBackpropFilterV2 = 'Conv3DBackpropFilterV2';\nexport type Conv3DBackpropFilterV2Inputs = Pick<NamedTensorInfoMap, 'x'|'dy'>;\n\nexport interface Conv3DBackpropFilterV2Attrs {\n  strides: [number, number, number]|number;\n  pad: 'valid'|'same';\n  filterShape: [number, number, number, number, number];\n}\n\nexport const Conv3DBackpropInputV2 = 'Conv3DBackpropInputV2';\nexport type Conv3DBackpropInputV2Inputs =\n    Pick<NamedTensorInfoMap, 'dy'|'filter'>;\nexport interface Conv3DBackpropInputV2Attrs {\n  strides: [number, number, number]|number;\n  pad: 'valid'|'same';\n  inputShape: [number, number, number, number, number];\n}\n\nexport const Cos = 'Cos';\nexport type CosInputs = UnaryInputs;\n\nexport const Cosh = 'Cosh';\nexport type CoshInputs = UnaryInputs;\n\nexport const Cumprod = 'Cumprod';\nexport type CumprodInputs = Pick<NamedTensorInfoMap, 'x'>;\nexport interface CumprodAttrs {\n  axis: number;\n  exclusive: boolean;\n  reverse: boolean;\n}\n\nexport const Cumsum = 'Cumsum';\nexport type CumsumInputs = Pick<NamedTensorInfoMap, 'x'>;\nexport interface CumsumAttrs {\n  axis: number;\n  exclusive: boolean;\n  reverse: boolean;\n}\n\nexport const CropAndResize = 'CropAndResize';\nexport type CropAndResizeInputs =\n    Pick<NamedTensorInfoMap, 'image'|'boxes'|'boxInd'>;\nexport interface CropAndResizeAttrs {\n  cropSize: [number, number];\n  method: 'bilinear'|'nearest';\n  extrapolationValue: number;\n}\n\nexport const DenseBincount = 'DenseBincount';\nexport type DenseBincountInputs = Pick<NamedTensorInfoMap, 'x'|'weights'>;\nexport interface DenseBincountAttrs {\n  size: number;\n  binaryOutput?: boolean;\n}\n\nexport const DepthToSpace = 'DepthToSpace';\nexport type DepthToSpaceInputs = Pick<NamedTensorInfoMap, 'x'>;\nexport interface DepthToSpaceAttrs {\n  blockSize: number;\n  dataFormat: 'NHWC'|'NCHW';\n}\n\nexport const DepthwiseConv2dNative = 'DepthwiseConv2dNative';\nexport type DepthwiseConv2dNativeInputs =\n    Pick<NamedTensorInfoMap, 'x'|'filter'>;\nexport interface DepthwiseConv2dNativeAttrs {\n  strides: [number, number]|number;\n  pad: 'valid'|'same'|number|ExplicitPadding;\n  dataFormat: 'NHWC'|'NCHW';\n  dilations: [number, number]|number;\n  dimRoundingMode?: 'floor'|'round'|'ceil';\n}\n\nexport const DepthwiseConv2dNativeBackpropFilter =\n    'DepthwiseConv2dNativeBackpropFilter';\nexport type DepthwiseConv2dNativeBackpropFilterInputs =\n    Pick<NamedTensorInfoMap, 'x'|'dy'>;\nexport interface DepthwiseConv2dNativeBackpropFilterAttrs {\n  strides: [number, number]|number;\n  dilations: [number, number]|number;\n  pad: 'valid'|'same'|number|ExplicitPadding;\n  dimRoundingMode?: 'floor'|'round'|'ceil';\n  filterShape: [number, number, number, number];\n}\n\nexport const DepthwiseConv2dNativeBackpropInput =\n    'DepthwiseConv2dNativeBackpropInput';\nexport type DepthwiseConv2dNativeBackpropInputInputs =\n    Pick<NamedTensorInfoMap, 'dy'|'filter'>;\nexport interface DepthwiseConv2dNativeBackpropInputAttrs {\n  strides: [number, number]|number;\n  dilations: [number, number]|number;\n  pad: 'valid'|'same'|number|ExplicitPadding;\n  dimRoundingMode?: 'floor'|'round'|'ceil';\n  inputShape: [number, number, number, number];\n}\n\nexport const Diag = 'Diag';\nexport type DiagInputs = Pick<NamedTensorInfoMap, 'x'>;\n\nexport const Dilation2D = 'Dilation2D';\nexport type Dilation2DInputs = Pick<NamedTensorInfoMap, 'x'|'filter'>;\nexport interface Dilation2DAttrs {\n  strides: [number, number]|number;\n  pad: 'valid'|'same'|number;\n  dilations: [number, number]|number;\n}\n\nexport const Dilation2DBackpropInput = 'Dilation2DBackpropInput';\nexport type Dilation2DBackpropInputInputs =\n    Pick<NamedTensorInfoMap, 'x'|'filter'|'dy'>;\n\nexport const Dilation2DBackpropFilter = 'Dilation2DBackpropFilter';\nexport type Dilation2DBackpropFilterInputs =\n    Pick<NamedTensorInfoMap, 'x'|'filter'|'dy'>;\n\nexport const Draw = 'Draw';\nexport type DrawInputs = Pick<NamedTensorInfoMap, 'image'>;\nexport interface DrawAttrs {\n  canvas: HTMLCanvasElement;\n  options?: DrawOptions;\n}\n\nexport const RealDiv = 'RealDiv';\nexport type RealDivInputs = BinaryInputs;\n\nexport const Einsum = 'Einsum';\nexport type EinsumInputs = TensorInfo[];\nexport interface EinsumAttrs {\n  equation: string;\n}\n\nexport const Elu = 'Elu';\nexport type EluInputs = Pick<NamedTensorInfoMap, 'x'>;\n\nexport const EluGrad = 'EluGrad';\nexport type EluGradInputs = Pick<NamedTensorInfoMap, 'dy'|'y'>;\n\nexport const Erf = 'Erf';\nexport type ErfInputs = UnaryInputs;\n\nexport const Equal = 'Equal';\nexport type EqualInputs = BinaryInputs;\n\nexport const Exp = 'Exp';\nexport type ExpInputs = UnaryInputs;\n\nexport const ExpandDims = 'ExpandDims';\nexport type ExpandDimsInputs = Pick<NamedTensorInfoMap, 'input'>;\nexport interface ExpandDimsAttrs {\n  dim: number;\n}\n\nexport const Expm1 = 'Expm1';\nexport type Expm1Inputs = UnaryInputs;\n\nexport const FFT = 'FFT';\nexport type FFTInputs = Pick<NamedTensorInfoMap, 'input'>;\n\nexport const Fill = 'Fill';\nexport interface FillAttrs {\n  shape: number[];\n  value: number|string;\n  dtype: DataType;\n}\n\nexport const FlipLeftRight = 'FlipLeftRight';\nexport type FlipLeftRightInputs = Pick<NamedTensorInfoMap, 'image'>;\n\nexport const Floor = 'Floor';\nexport type FloorInputs = UnaryInputs;\n\nexport const FloorDiv = 'FloorDiv';\nexport type FloorDivInputs = BinaryInputs;\n\nexport const FusedBatchNorm = 'FusedBatchNorm';\nexport type FusedBatchNormInputs =\n    Pick<NamedTensorInfoMap, 'x'|'scale'|'offset'|'mean'|'variance'>;\nexport interface FusedBatchNormAttrs {\n  varianceEpsilon: number;\n}\n\nexport const GatherV2 = 'GatherV2';\nexport type GatherV2Inputs = Pick<NamedTensorInfoMap, 'x'|'indices'>;\nexport interface GatherV2Attrs {\n  axis: number;\n  batchDims: number;\n}\n\nexport const GatherNd = 'GatherNd';\nexport type GatherNdInputs = Pick<NamedTensorInfoMap, 'params'|'indices'>;\n\nexport const Greater = 'Greater';\nexport type GreaterInputs = BinaryInputs;\n\nexport const GreaterEqual = 'GreaterEqual';\nexport type GreaterEqualInputs = BinaryInputs;\n\nexport const Identity = 'Identity';\nexport type IdentityInputs = Pick<NamedTensorInfoMap, 'x'>;\n\nexport const IFFT = 'IFFT';\nexport type IFFTInputs = Pick<NamedTensorInfoMap, 'input'>;\n\nexport const Imag = 'Imag';\nexport type ImagInputs = Pick<NamedTensorInfoMap, 'input'>;\n\nexport const IsFinite = 'IsFinite';\nexport type IsFiniteInputs = UnaryInputs;\n\nexport const IsInf = 'IsInf';\nexport type IsInfInputs = UnaryInputs;\n\nexport const IsNan = 'IsNan';\nexport type IsNanInputs = UnaryInputs;\n\nexport const LeakyRelu = 'LeakyRelu';\nexport type LeakyReluInputs = Pick<NamedTensorInfoMap, 'x'>;\nexport interface LeakyReluAttrs {\n  alpha: number;\n}\n\nexport const Less = 'Less';\nexport type LessInputs = BinaryInputs;\n\nexport const LessEqual = 'LessEqual';\nexport type LessEqualInputs = BinaryInputs;\n\nexport const LinSpace = 'LinSpace';\nexport interface LinSpaceAttrs {\n  start: number;\n  stop: number;\n  num: number;\n}\nexport const Log = 'Log';\nexport type LogInputs = UnaryInputs;\n\nexport const Log1p = 'Log1p';\nexport type Log1pInputs = UnaryInputs;\n\nexport const LogicalAnd = 'LogicalAnd';\nexport type LogicalAndInputs = BinaryInputs;\n\nexport const LogicalNot = 'LogicalNot';\nexport type LogicalNotInputs = Pick<NamedTensorInfoMap, 'x'>;\n\nexport const LogicalOr = 'LogicalOr';\nexport type LogicalOrInputs = BinaryInputs;\n\nexport const LogicalXor = 'LogicalXor';\nexport type LogicalXorInputs = BinaryInputs;\n\nexport const LogSoftmax = 'LogSoftmax';\nexport type LogSoftmaxInputs = Pick<NamedTensorInfoMap, 'logits'>;\nexport interface LogSoftmaxAttrs {\n  axis: number;\n}\n\nexport const LowerBound = 'LowerBound';\nexport type LowerBoundInputs =\n    Pick<NamedTensorInfoMap, 'sortedSequence'|'values'>;\n\nexport const LRN = 'LRN';\nexport type LRNInputs = Pick<NamedTensorInfoMap, 'x'>;\nexport interface LRNAttrs {\n  depthRadius: number;\n  bias: number;\n  alpha: number;\n  beta: number;\n}\n\nexport const LRNGrad = 'LRNGrad';\nexport type LRNGradInputs = Pick<NamedTensorInfoMap, 'x'|'y'|'dy'>;\nexport interface LRNGradAttrs {\n  depthRadius: number;\n  bias: number;\n  alpha: number;\n  beta: number;\n}\n\nexport const MatrixBandPart = 'MatrixBandPart';\nexport type MatrixBandPartInputs =\n    Pick<NamedTensorInfoMap, 'input'|'numLower'|'numUpper'>;\nexport interface MatrixBandPartAttrs {}\n\nexport const Max = 'Max';\nexport type MaxInputs = Pick<NamedTensorInfoMap, 'x'>;\nexport interface MaxAttrs {\n  reductionIndices: number|number[];\n  keepDims: boolean;\n}\n\nexport const Maximum = 'Maximum';\nexport type MaximumInputs = BinaryInputs;\n\nexport const MaxPool = 'MaxPool';\nexport type MaxPoolInputs = Pick<NamedTensorInfoMap, 'x'>;\nexport interface MaxPoolAttrs {\n  filterSize: [number, number]|number;\n  strides: [number, number]|number;\n  pad: 'valid'|'same'|number|ExplicitPadding;\n  dimRoundingMode?: 'floor'|'round'|'ceil';\n}\n\nexport const MaxPoolGrad = 'MaxPoolGrad';\nexport type MaxPoolGradInputs = Pick<NamedTensorInfoMap, 'dy'|'input'|'output'>;\nexport interface MaxPoolGradAttrs {\n  filterSize: [number, number]|number;\n  strides: [number, number]|number;\n  pad: 'valid'|'same'|number|ExplicitPadding;\n  dimRoundingMode?: 'floor'|'round'|'ceil';\n}\n\nexport const MaxPool3D = 'MaxPool3D';\nexport type MaxPool3DInputs = Pick<NamedTensorInfoMap, 'x'>;\nexport interface MaxPool3DAttrs {\n  filterSize: [number, number, number]|number;\n  strides: [number, number, number]|number;\n  pad: 'valid'|'same'|number;\n  dataFormat: 'NDHWC'|'NCDHW';\n  dimRoundingMode?: 'floor'|'round'|'ceil';\n}\n\nexport const MaxPool3DGrad = 'MaxPool3DGrad';\nexport type MaxPool3DGradInputs =\n    Pick<NamedTensorInfoMap, 'dy'|'input'|'output'>;\nexport interface MaxPool3DGradAttrs {\n  filterSize: [number, number, number]|number;\n  strides: [number, number, number]|number;\n  pad: 'valid'|'same'|number;\n  dimRoundingMode?: 'floor'|'round'|'ceil';\n}\n\nexport const MaxPoolWithArgmax = 'MaxPoolWithArgmax';\nexport type MaxPoolWithArgmaxInputs = Pick<NamedTensorInfoMap, 'x'>;\nexport interface MaxPoolWithArgmaxAttrs {\n  filterSize: [number, number]|number;\n  strides: [number, number]|number;\n  pad: 'valid'|'same'|number;\n  includeBatchInIndex: boolean;\n}\n\nexport const Mean = 'Mean';\nexport type MeanInputs = Pick<NamedTensorInfoMap, 'x'>;\nexport interface MeanAttrs {\n  axis: number|number[];\n  keepDims: boolean;\n}\n\nexport const Min = 'Min';\nexport type MinInputs = Pick<NamedTensorInfoMap, 'x'>;\nexport interface MinAttrs {\n  axis: number|number[];\n  keepDims: boolean;\n}\n\nexport const Minimum = 'Minimum';\nexport type MinimumInputs = BinaryInputs;\n\nexport const MirrorPad = 'MirrorPad';\nexport type MirrorPadInputs = Pick<NamedTensorInfoMap, 'x'>;\nexport interface MirrorPadAttrs {\n  paddings: Array<[number, number]>;\n  mode: 'reflect'|'symmetric';\n}\n\nexport const Mod = 'Mod';\nexport type ModInputs = BinaryInputs;\n\nexport const Multinomial = 'Multinomial';\nexport type MultinomialInputs = Pick<NamedTensorInfoMap, 'logits'>;\nexport interface MultinomialAttrs {\n  numSamples: number;\n  seed: number;\n  normalized: boolean;\n}\n\nexport const Multiply = 'Multiply';\nexport type MultiplyInputs = BinaryInputs;\n\nexport const Neg = 'Neg';\nexport type NegInputs = UnaryInputs;\n\nexport const NotEqual = 'NotEqual';\nexport type NotEqualInputs = BinaryInputs;\n\nexport const NonMaxSuppressionV3 = 'NonMaxSuppressionV3';\nexport type NonMaxSuppressionV3Inputs =\n    Pick<NamedTensorInfoMap, 'boxes'|'scores'>;\nexport interface NonMaxSuppressionV3Attrs {\n  maxOutputSize: number;\n  iouThreshold: number;\n  scoreThreshold: number;\n}\n\nexport const NonMaxSuppressionV4 = 'NonMaxSuppressionV4';\nexport type NonMaxSuppressionV4Inputs =\n    Pick<NamedTensorInfoMap, 'boxes'|'scores'>;\nexport interface NonMaxSuppressionV4Attrs {\n  maxOutputSize: number;\n  iouThreshold: number;\n  scoreThreshold: number;\n  padToMaxOutputSize: boolean;\n}\n\nexport const NonMaxSuppressionV5 = 'NonMaxSuppressionV5';\nexport type NonMaxSuppressionV5Inputs =\n    Pick<NamedTensorInfoMap, 'boxes'|'scores'>;\nexport interface NonMaxSuppressionV5Attrs {\n  maxOutputSize: number;\n  iouThreshold: number;\n  scoreThreshold: number;\n  softNmsSigma: number;\n}\n\nexport const OnesLike = 'OnesLike';\nexport type OnesLikeInputs = UnaryInputs;\n\nexport const OneHot = 'OneHot';\nexport type OneHotInputs = Pick<NamedTensorInfoMap, 'indices'>;\nexport interface OneHotAttrs {\n  depth: number;\n  onValue: number;\n  offValue: number;\n  dtype: DataType;\n}\n\nexport const Pack = 'Pack';\nexport type PackInputs = TensorInfo[];\nexport interface PackAttrs {\n  axis: number;\n}\n\nexport const PadV2 = 'PadV2';\nexport type PadV2Inputs = Pick<NamedTensorInfoMap, 'x'>;\nexport interface PadV2Attrs {\n  paddings: Array<[number, number]>;\n  constantValue: number;\n}\n\nexport const Pool = 'Pool';\nexport type PoolInputs = Pick<NamedTensorInfoMap, 'input'>;\n\nexport const Pow = 'Pow';\nexport type PowInputs = BinaryInputs;\n\nexport const Prelu = 'Prelu';\nexport type PreluInputs = Pick<NamedTensorInfoMap, 'x'|'alpha'>;\n\nexport const Prod = 'Prod';\nexport type ProdInputs = Pick<NamedTensorInfoMap, 'x'>;\nexport interface ProdAttrs {\n  axis: number|number[];\n  keepDims: boolean;\n}\n\nexport const RaggedGather = 'RaggedGather';\nexport type RaggedGatherInputs = {\n  paramsNestedSplits: TensorInfo[]\n}&Pick<NamedTensorInfoMap, 'paramsDenseValues'|'indices'>;\nexport interface RaggedGatherAttrs {\n  outputRaggedRank: number;\n}\n\nexport const RaggedRange = 'RaggedRange';\nexport type RaggedRangeInputs =\n    Pick<NamedTensorInfoMap, 'starts'|'limits'|'deltas'>;\n\nexport const RaggedTensorToTensor = 'RaggedTensorToTensor';\nexport type RaggedTensorToTensorInputs =\n    Pick<NamedTensorInfoMap, 'shape'|'values'|'defaultValue'>&\n    {rowPartitionTensors: TensorInfo[]};\nexport interface RaggedTensorToTensorAttrs {\n  rowPartitionTypes: string[];\n}\n\nexport const Range = 'Range';\nexport interface RangeAttrs {\n  start: number;\n  stop: number;\n  step: number;\n  dtype: 'float32'|'int32';\n}\n\nexport const Real = 'Real';\nexport type RealInputs = Pick<NamedTensorInfoMap, 'input'>;\n\nexport const Reciprocal = 'Reciprocal';\nexport type ReciprocalInputs = UnaryInputs;\n\nexport const Relu = 'Relu';\nexport type ReluInputs = Pick<NamedTensorInfoMap, 'x'>;\n\nexport const Reshape = 'Reshape';\nexport type ReshapeInputs = Pick<NamedTensorInfoMap, 'x'>;\nexport interface ReshapeAttrs {\n  shape: number[];\n}\n\nexport const ResizeNearestNeighbor = 'ResizeNearestNeighbor';\nexport type ResizeNearestNeighborInputs = Pick<NamedTensorInfoMap, 'images'>;\nexport interface ResizeNearestNeighborAttrs {\n  alignCorners: boolean;\n  halfPixelCenters: boolean;\n  size: [number, number];\n}\n\nexport const ResizeNearestNeighborGrad = 'ResizeNearestNeighborGrad';\nexport type ResizeNearestNeighborGradInputs =\n    Pick<NamedTensorInfoMap, 'images'|'dy'>;\nexport type ResizeNearestNeighborGradAttrs = ResizeNearestNeighborAttrs;\n\nexport const ResizeBilinear = 'ResizeBilinear';\nexport type ResizeBilinearInputs = Pick<NamedTensorInfoMap, 'images'>;\nexport interface ResizeBilinearAttrs {\n  alignCorners: boolean;\n  halfPixelCenters: boolean;\n  size: [number, number];\n}\n\nexport const ResizeBilinearGrad = 'ResizeBilinearGrad';\nexport type ResizeBilinearGradInputs = Pick<NamedTensorInfoMap, 'images'|'dy'>;\nexport type ResizeBilinearGradAttrs = ResizeBilinearAttrs;\n\nexport const Relu6 = 'Relu6';\nexport type Relu6Inputs = Pick<NamedTensorInfoMap, 'x'>;\n\nexport const Reverse = 'Reverse';\nexport type ReverseInputs = Pick<NamedTensorInfoMap, 'x'>;\nexport interface ReverseAttrs {\n  dims: number|number[];\n}\n\nexport const Round = 'Round';\nexport type RoundInputs = UnaryInputs;\n\nexport const Rsqrt = 'Rsqrt';\nexport type RsqrtInputs = UnaryInputs;\n\nexport const ScatterNd = 'ScatterNd';\nexport type ScatterNdInputs = Pick<NamedTensorInfoMap, 'indices'|'updates'>;\nexport interface ScatterNdAttrs {\n  shape: number[];\n}\n\nexport const TensorScatterUpdate = 'TensorScatterUpdate';\nexport type TensorScatterUpdateInputs =\n    Pick<NamedTensorInfoMap, 'tensor'|'indices'|'updates'>;\nexport interface TensorScatterUpdateAttrs {}\n\nexport const SearchSorted = 'SearchSorted';\nexport type SearchSortedInputs =\n    Pick<NamedTensorInfoMap, 'sortedSequence'|'values'>;\nexport interface SearchSortedAttrs {\n  side: 'left'|'right';\n}\n\nexport const Select = 'Select';\nexport type SelectInputs = Pick<NamedTensorInfoMap, 'condition'|'t'|'e'>;\n\nexport const Selu = 'Selu';\nexport type SeluInputs = Pick<NamedTensorInfoMap, 'x'>;\n\nexport const Slice = 'Slice';\nexport type SliceInputs = Pick<NamedTensorInfoMap, 'x'>;\nexport interface SliceAttrs {\n  begin: number|number[];\n  size: number|number[];\n}\nexport const Sin = 'Sin';\nexport type SinInputs = UnaryInputs;\n\nexport const Sinh = 'Sinh';\nexport type SinhInputs = UnaryInputs;\n\nexport const Sign = 'Sign';\nexport type SignInputs = UnaryInputs;\n\nexport const Sigmoid = 'Sigmoid';\nexport type SigmoidInputs = UnaryInputs;\n\nexport const Softplus = 'Softplus';\nexport type SoftplusInputs = UnaryInputs;\n\nexport const Sqrt = 'Sqrt';\nexport type SqrtInputs = UnaryInputs;\n\nexport const Sum = 'Sum';\nexport type SumInputs = Pick<NamedTensorInfoMap, 'x'>;\nexport interface SumAttrs {\n  axis: number|number[];\n  keepDims: boolean;\n}\n\nexport const SpaceToBatchND = 'SpaceToBatchND';\nexport type SpaceToBatchNDInputs = Pick<NamedTensorInfoMap, 'x'>;\nexport interface SpaceToBatchNDAttrs {\n  blockShape: number[];\n  paddings: number[][];\n}\n\nexport const SplitV = 'SplitV';\nexport type SplitVInputs = Pick<NamedTensorInfoMap, 'x'>;\nexport interface SplitVAttrs {\n  numOrSizeSplits: number[]|number;\n  axis: number;\n}\n\nexport const Softmax = 'Softmax';\nexport type SoftmaxInputs = Pick<NamedTensorInfoMap, 'logits'>;\nexport interface SoftmaxAttrs {\n  dim: number;\n}\n\nexport const SparseFillEmptyRows = 'SparseFillEmptyRows';\nexport type SparseFillEmptyRowsInputs =\n    Pick<NamedTensorInfoMap, 'indices'|'values'|'denseShape'|'defaultValue'>;\n\nexport const SparseReshape = 'SparseReshape';\nexport type SparseReshapeInputs =\n    Pick<NamedTensorInfoMap, 'inputIndices'|'inputShape'|'newShape'>;\n\nexport const SparseSegmentMean = 'SparseSegmentMean';\nexport type SparseSegmentMeanInputs =\n    Pick<NamedTensorInfoMap, 'data'|'indices'|'segmentIds'>;\n\nexport const SparseSegmentSum = 'SparseSegmentSum';\nexport type SparseSegmentSumInputs =\n    Pick<NamedTensorInfoMap, 'data'|'indices'|'segmentIds'>;\n\nexport const SparseToDense = 'SparseToDense';\nexport type SparseToDenseInputs =\n    Pick<NamedTensorInfoMap, 'sparseIndices'|'sparseValues'|'defaultValue'>;\nexport interface SparseToDenseAttrs {\n  outputShape: number[];\n}\n\nexport const SquaredDifference = 'SquaredDifference';\nexport type SquaredDifferenceInputs = BinaryInputs;\n\nexport const Square = 'Square';\nexport type SquareInputs = Pick<NamedTensorInfoMap, 'x'>;\n\nexport const StaticRegexReplace = 'StaticRegexReplace';\nexport type StaticRegexReplaceInputs = UnaryInputs;\nexport interface StaticRegexReplaceAttrs {\n  pattern: string;\n  rewrite: string;\n  replaceGlobal: boolean;\n}\n\nexport const StridedSlice = 'StridedSlice';\nexport type StridedSliceInputs = Pick<NamedTensorInfoMap, 'x'>;\nexport interface StridedSliceAttrs {\n  begin: number[];\n  end: number[];\n  strides: number[];\n  beginMask: number;\n  endMask: number;\n  ellipsisMask: number;\n  newAxisMask: number;\n  shrinkAxisMask: number;\n}\n\nexport const StringNGrams = 'StringNGrams';\nexport type StringNGramsInputs = Pick<NamedTensorInfoMap, 'data'|'dataSplits'>;\nexport interface StringNGramsAttrs {\n  separator: string;\n  nGramWidths: number[];\n  leftPad: string;\n  rightPad: string;\n  padWidth: number;\n  preserveShortSequences: boolean;\n}\n\nexport const StringSplit = 'StringSplit';\nexport type StringSplitInputs = Pick<NamedTensorInfoMap, 'input'|'delimiter'>;\nexport interface StringSplitAttrs {\n  skipEmpty: boolean;\n}\n\nexport const StringToHashBucketFast = 'StringToHashBucketFast';\nexport type StringToHashBucketFastInputs = Pick<NamedTensorInfoMap, 'input'>;\nexport interface StringToHashBucketFastAttrs {\n  numBuckets: number;\n}\n\nexport const Sub = 'Sub';\nexport type SubInputs = BinaryInputs;\n\nexport const Tan = 'Tan';\nexport type TanInputs = UnaryInputs;\n\nexport const Tanh = 'Tanh';\nexport type TanhInputs = UnaryInputs;\n\nexport const Tile = 'Tile';\nexport type TileInputs = Pick<NamedTensorInfoMap, 'x'>;\nexport interface TileAttrs {\n  reps: number[];\n}\n\nexport const TopK = 'TopK';\nexport type TopKInputs = Pick<NamedTensorInfoMap, 'x'>;\nexport interface TopKAttrs {\n  k: number;\n  sorted: boolean;\n}\n\nexport const Transform = 'Transform';\nexport type TransformInputs = Pick<NamedTensorInfoMap, 'image'|'transforms'>;\nexport interface TransformAttrs {\n  interpolation: 'nearest'|'bilinear';\n  fillMode: 'constant'|'reflect'|'wrap'|'nearest';\n  fillValue: number;\n  outputShape?: [number, number];\n}\n\nexport const Transpose = 'Transpose';\nexport type TransposeInputs = Pick<NamedTensorInfoMap, 'x'>;\nexport interface TransposeAttrs {\n  perm: number[];\n}\n\nexport const Unique = 'Unique';\nexport type UniqueInputs = Pick<NamedTensorInfoMap, 'x'>;\nexport interface UniqueAttrs {\n  axis: number;\n}\n\nexport type UnaryInputs = Pick<NamedTensorInfoMap, 'x'>;\n\nexport const Unpack = 'Unpack';\nexport type UnpackInputs = Pick<NamedTensorInfoMap, 'value'>;\nexport interface UnpackAttrs {\n  axis: number;\n}\n\nexport const UnsortedSegmentSum = 'UnsortedSegmentSum';\nexport type UnsortedSegmentSumInputs =\n    Pick<NamedTensorInfoMap, 'x'|'segmentIds'>;\nexport interface UnsortedSegmentSumAttrs {\n  numSegments: number;\n}\n\nexport const UpperBound = 'UpperBound';\nexport type UpperBoundInputs =\n    Pick<NamedTensorInfoMap, 'sortedSequence'|'values'>;\n\nexport const ZerosLike = 'ZerosLike';\nexport type ZerosLikeInputs = UnaryInputs;\n\n/**\n * TensorFlow.js-only kernels\n */\nexport const Step = 'Step';\nexport type StepInputs = UnaryInputs;\nexport interface StepAttrs {\n  alpha: number;\n}\n\nexport const FromPixels = 'FromPixels';\nexport interface FromPixelsInputs {\n  pixels: PixelData|ImageData|HTMLImageElement|HTMLCanvasElement|\n      HTMLVideoElement|ImageBitmap;\n}\nexport interface FromPixelsAttrs {\n  numChannels: number;\n}\n\nexport const RotateWithOffset = 'RotateWithOffset';\nexport type RotateWithOffsetInputs = Pick<NamedTensorInfoMap, 'image'>;\nexport interface RotateWithOffsetAttrs {\n  radians: number;\n  fillValue: number|[number, number, number];\n  center: number|[number, number];\n}\n\nexport const _FusedMatMul = '_FusedMatMul';\n// tslint:disable-next-line: class-name\nexport interface _FusedMatMulInputs extends NamedTensorInfoMap {\n  a: TensorInfo;\n  b: TensorInfo;\n  bias?: TensorInfo;\n  preluActivationWeights?: TensorInfo;\n}\n// tslint:disable-next-line: class-name\nexport interface _FusedMatMulAttrs {\n  transposeA: boolean;\n  transposeB: boolean;\n  activation: Activation;\n  leakyreluAlpha?: number;\n}\n\nexport const FusedConv2D = 'FusedConv2D';\nexport interface FusedConv2DInputs extends NamedTensorInfoMap {\n  x: TensorInfo;\n  filter: TensorInfo;\n  bias?: TensorInfo;\n  preluActivationWeights?: TensorInfo;\n}\nexport interface FusedConv2DAttrs {\n  strides: [number, number]|number;\n  pad: 'valid'|'same'|number|ExplicitPadding;\n  dataFormat: 'NHWC'|'NCHW';\n  dilations: [number, number]|number;\n  dimRoundingMode: 'floor'|'round'|'ceil';\n  activation: Activation;\n  leakyreluAlpha?: number;\n}\n\nexport const FusedDepthwiseConv2D = 'FusedDepthwiseConv2D';\nexport interface FusedDepthwiseConv2DInputs extends NamedTensorInfoMap {\n  x: TensorInfo;\n  filter: TensorInfo;\n  bias?: TensorInfo;\n  preluActivationWeights?: TensorInfo;\n}\nexport interface FusedDepthwiseConv2DAttrs {\n  strides: [number, number]|number;\n  pad: 'valid'|'same'|number|ExplicitPadding;\n  dataFormat: 'NHWC'|'NCHW';\n  dilations: [number, number]|number;\n  dimRoundingMode: 'floor'|'round'|'ceil';\n  activation: Activation;\n  leakyreluAlpha?: number;\n}\n"], "mappings": "AA0BA,OAAO,MAAMA,GAAG,GAAG,KAAK;AAGxB,OAAO,MAAMC,IAAI,GAAG,MAAM;AAG1B,OAAO,MAAMC,KAAK,GAAG,OAAO;AAG5B,OAAO,MAAMC,GAAG,GAAG,KAAK;AAGxB,OAAO,MAAMC,IAAI,GAAG,MAAM;AAG1B,OAAO,MAAMC,GAAG,GAAG,KAAK;AAOxB,OAAO,MAAMC,GAAG,GAAG,KAAK;AAOxB,OAAO,MAAMC,MAAM,GAAG,QAAQ;AAM9B,OAAO,MAAMC,MAAM,GAAG,QAAQ;AAM9B,OAAO,MAAMC,IAAI,GAAG,MAAM;AAG1B,OAAO,MAAMC,KAAK,GAAG,OAAO;AAG5B,OAAO,MAAMC,IAAI,GAAG,MAAM;AAG1B,OAAO,MAAMC,KAAK,GAAG,OAAO;AAG5B,OAAO,MAAMC,KAAK,GAAG,OAAO;AAG5B,OAAO,MAAMC,OAAO,GAAG,SAAS;AAShC,OAAO,MAAMC,WAAW,GAAG,aAAa;AAQxC,OAAO,MAAMC,SAAS,GAAG,WAAW;AAUpC,OAAO,MAAMC,aAAa,GAAG,eAAe;AAS5C,OAAO,MAAMC,WAAW,GAAG,aAAa;AAOxC,OAAO,MAAMC,cAAc,GAAG,gBAAgB;AAS9C,OAAO,MAAMC,QAAQ,GAAG,UAAU;AAMlC,OAAO,MAAMC,UAAU,GAAG,YAAY;AAGtC,OAAO,MAAMC,WAAW,GAAG,aAAa;AAOxC,OAAO,MAAMC,aAAa,GAAG,eAAe;AAG5C,OAAO,MAAMC,IAAI,GAAG,MAAM;AAM1B,OAAO,MAAMC,IAAI,GAAG,MAAM;AAG1B,OAAO,MAAMC,WAAW,GAAG,aAAa;AAOxC,OAAO,MAAMC,OAAO,GAAG,SAAS;AAGhC,OAAO,MAAMC,UAAU,GAAG,YAAY;AAGtC,OAAO,MAAMC,MAAM,GAAG,QAAQ;AAM9B,OAAO,MAAMC,MAAM,GAAG,QAAQ;AAU9B,OAAO,MAAMC,oBAAoB,GAAG,sBAAsB;AAU1D,OAAO,MAAMC,mBAAmB,GAAG,qBAAqB;AAUxD,OAAO,MAAMC,MAAM,GAAG,QAAQ;AAS9B,OAAO,MAAMC,sBAAsB,GAAG,wBAAwB;AAS9D,OAAO,MAAMC,qBAAqB,GAAG,uBAAuB;AAS5D,OAAO,MAAMC,GAAG,GAAG,KAAK;AAGxB,OAAO,MAAMC,IAAI,GAAG,MAAM;AAG1B,OAAO,MAAMC,OAAO,GAAG,SAAS;AAQhC,OAAO,MAAMC,MAAM,GAAG,QAAQ;AAQ9B,OAAO,MAAMC,aAAa,GAAG,eAAe;AAS5C,OAAO,MAAMC,aAAa,GAAG,eAAe;AAO5C,OAAO,MAAMC,YAAY,GAAG,cAAc;AAO1C,OAAO,MAAMC,qBAAqB,GAAG,uBAAuB;AAW5D,OAAO,MAAMC,mCAAmC,GAC5C,qCAAqC;AAWzC,OAAO,MAAMC,kCAAkC,GAC3C,oCAAoC;AAWxC,OAAO,MAAMC,IAAI,GAAG,MAAM;AAG1B,OAAO,MAAMC,UAAU,GAAG,YAAY;AAQtC,OAAO,MAAMC,uBAAuB,GAAG,yBAAyB;AAIhE,OAAO,MAAMC,wBAAwB,GAAG,0BAA0B;AAIlE,OAAO,MAAMC,IAAI,GAAG,MAAM;AAO1B,OAAO,MAAMC,OAAO,GAAG,SAAS;AAGhC,OAAO,MAAMC,MAAM,GAAG,QAAQ;AAM9B,OAAO,MAAMC,GAAG,GAAG,KAAK;AAGxB,OAAO,MAAMC,OAAO,GAAG,SAAS;AAGhC,OAAO,MAAMC,GAAG,GAAG,KAAK;AAGxB,OAAO,MAAMC,KAAK,GAAG,OAAO;AAG5B,OAAO,MAAMC,GAAG,GAAG,KAAK;AAGxB,OAAO,MAAMC,UAAU,GAAG,YAAY;AAMtC,OAAO,MAAMC,KAAK,GAAG,OAAO;AAG5B,OAAO,MAAMC,GAAG,GAAG,KAAK;AAGxB,OAAO,MAAMC,IAAI,GAAG,MAAM;AAO1B,OAAO,MAAMC,aAAa,GAAG,eAAe;AAG5C,OAAO,MAAMC,KAAK,GAAG,OAAO;AAG5B,OAAO,MAAMC,QAAQ,GAAG,UAAU;AAGlC,OAAO,MAAMC,cAAc,GAAG,gBAAgB;AAO9C,OAAO,MAAMC,QAAQ,GAAG,UAAU;AAOlC,OAAO,MAAMC,QAAQ,GAAG,UAAU;AAGlC,OAAO,MAAMC,OAAO,GAAG,SAAS;AAGhC,OAAO,MAAMC,YAAY,GAAG,cAAc;AAG1C,OAAO,MAAMC,QAAQ,GAAG,UAAU;AAGlC,OAAO,MAAMC,IAAI,GAAG,MAAM;AAG1B,OAAO,MAAMC,IAAI,GAAG,MAAM;AAG1B,OAAO,MAAMC,QAAQ,GAAG,UAAU;AAGlC,OAAO,MAAMC,KAAK,GAAG,OAAO;AAG5B,OAAO,MAAMC,KAAK,GAAG,OAAO;AAG5B,OAAO,MAAMC,SAAS,GAAG,WAAW;AAMpC,OAAO,MAAMC,IAAI,GAAG,MAAM;AAG1B,OAAO,MAAMC,SAAS,GAAG,WAAW;AAGpC,OAAO,MAAMC,QAAQ,GAAG,UAAU;AAMlC,OAAO,MAAMC,GAAG,GAAG,KAAK;AAGxB,OAAO,MAAMC,KAAK,GAAG,OAAO;AAG5B,OAAO,MAAMC,UAAU,GAAG,YAAY;AAGtC,OAAO,MAAMC,UAAU,GAAG,YAAY;AAGtC,OAAO,MAAMC,SAAS,GAAG,WAAW;AAGpC,OAAO,MAAMC,UAAU,GAAG,YAAY;AAGtC,OAAO,MAAMC,UAAU,GAAG,YAAY;AAMtC,OAAO,MAAMC,UAAU,GAAG,YAAY;AAItC,OAAO,MAAMC,GAAG,GAAG,KAAK;AASxB,OAAO,MAAMC,OAAO,GAAG,SAAS;AAShC,OAAO,MAAMC,cAAc,GAAG,gBAAgB;AAK9C,OAAO,MAAMC,GAAG,GAAG,KAAK;AAOxB,OAAO,MAAMC,OAAO,GAAG,SAAS;AAGhC,OAAO,MAAMC,OAAO,GAAG,SAAS;AAShC,OAAO,MAAMC,WAAW,GAAG,aAAa;AASxC,OAAO,MAAMC,SAAS,GAAG,WAAW;AAUpC,OAAO,MAAMC,aAAa,GAAG,eAAe;AAU5C,OAAO,MAAMC,iBAAiB,GAAG,mBAAmB;AASpD,OAAO,MAAMC,IAAI,GAAG,MAAM;AAO1B,OAAO,MAAMC,GAAG,GAAG,KAAK;AAOxB,OAAO,MAAMC,OAAO,GAAG,SAAS;AAGhC,OAAO,MAAMC,SAAS,GAAG,WAAW;AAOpC,OAAO,MAAMC,GAAG,GAAG,KAAK;AAGxB,OAAO,MAAMC,WAAW,GAAG,aAAa;AAQxC,OAAO,MAAMC,QAAQ,GAAG,UAAU;AAGlC,OAAO,MAAMC,GAAG,GAAG,KAAK;AAGxB,OAAO,MAAMC,QAAQ,GAAG,UAAU;AAGlC,OAAO,MAAMC,mBAAmB,GAAG,qBAAqB;AASxD,OAAO,MAAMC,mBAAmB,GAAG,qBAAqB;AAUxD,OAAO,MAAMC,mBAAmB,GAAG,qBAAqB;AAUxD,OAAO,MAAMC,QAAQ,GAAG,UAAU;AAGlC,OAAO,MAAMC,MAAM,GAAG,QAAQ;AAS9B,OAAO,MAAMC,IAAI,GAAG,MAAM;AAM1B,OAAO,MAAMC,KAAK,GAAG,OAAO;AAO5B,OAAO,MAAMC,IAAI,GAAG,MAAM;AAG1B,OAAO,MAAMC,GAAG,GAAG,KAAK;AAGxB,OAAO,MAAMC,KAAK,GAAG,OAAO;AAG5B,OAAO,MAAMC,IAAI,GAAG,MAAM;AAO1B,OAAO,MAAMC,YAAY,GAAG,cAAc;AAQ1C,OAAO,MAAMC,WAAW,GAAG,aAAa;AAIxC,OAAO,MAAMC,oBAAoB,GAAG,sBAAsB;AAQ1D,OAAO,MAAMC,KAAK,GAAG,OAAO;AAQ5B,OAAO,MAAMC,IAAI,GAAG,MAAM;AAG1B,OAAO,MAAMC,UAAU,GAAG,YAAY;AAGtC,OAAO,MAAMC,IAAI,GAAG,MAAM;AAG1B,OAAO,MAAMC,OAAO,GAAG,SAAS;AAMhC,OAAO,MAAMC,qBAAqB,GAAG,uBAAuB;AAQ5D,OAAO,MAAMC,yBAAyB,GAAG,2BAA2B;AAKpE,OAAO,MAAMC,cAAc,GAAG,gBAAgB;AAQ9C,OAAO,MAAMC,kBAAkB,GAAG,oBAAoB;AAItD,OAAO,MAAMC,KAAK,GAAG,OAAO;AAG5B,OAAO,MAAMC,OAAO,GAAG,SAAS;AAMhC,OAAO,MAAMC,KAAK,GAAG,OAAO;AAG5B,OAAO,MAAMC,KAAK,GAAG,OAAO;AAG5B,OAAO,MAAMC,SAAS,GAAG,WAAW;AAMpC,OAAO,MAAMC,mBAAmB,GAAG,qBAAqB;AAKxD,OAAO,MAAMC,YAAY,GAAG,cAAc;AAO1C,OAAO,MAAMC,MAAM,GAAG,QAAQ;AAG9B,OAAO,MAAMC,IAAI,GAAG,MAAM;AAG1B,OAAO,MAAMC,KAAK,GAAG,OAAO;AAM5B,OAAO,MAAMC,GAAG,GAAG,KAAK;AAGxB,OAAO,MAAMC,IAAI,GAAG,MAAM;AAG1B,OAAO,MAAMC,IAAI,GAAG,MAAM;AAG1B,OAAO,MAAMC,OAAO,GAAG,SAAS;AAGhC,OAAO,MAAMC,QAAQ,GAAG,UAAU;AAGlC,OAAO,MAAMC,IAAI,GAAG,MAAM;AAG1B,OAAO,MAAMC,GAAG,GAAG,KAAK;AAOxB,OAAO,MAAMC,cAAc,GAAG,gBAAgB;AAO9C,OAAO,MAAMC,MAAM,GAAG,QAAQ;AAO9B,OAAO,MAAMC,OAAO,GAAG,SAAS;AAMhC,OAAO,MAAMC,mBAAmB,GAAG,qBAAqB;AAIxD,OAAO,MAAMC,aAAa,GAAG,eAAe;AAI5C,OAAO,MAAMC,iBAAiB,GAAG,mBAAmB;AAIpD,OAAO,MAAMC,gBAAgB,GAAG,kBAAkB;AAIlD,OAAO,MAAMC,aAAa,GAAG,eAAe;AAO5C,OAAO,MAAMC,iBAAiB,GAAG,mBAAmB;AAGpD,OAAO,MAAMC,MAAM,GAAG,QAAQ;AAG9B,OAAO,MAAMC,kBAAkB,GAAG,oBAAoB;AAQtD,OAAO,MAAMC,YAAY,GAAG,cAAc;AAa1C,OAAO,MAAMC,YAAY,GAAG,cAAc;AAW1C,OAAO,MAAMC,WAAW,GAAG,aAAa;AAMxC,OAAO,MAAMC,sBAAsB,GAAG,wBAAwB;AAM9D,OAAO,MAAMC,GAAG,GAAG,KAAK;AAGxB,OAAO,MAAMC,GAAG,GAAG,KAAK;AAGxB,OAAO,MAAMC,IAAI,GAAG,MAAM;AAG1B,OAAO,MAAMC,IAAI,GAAG,MAAM;AAM1B,OAAO,MAAMC,IAAI,GAAG,MAAM;AAO1B,OAAO,MAAMC,SAAS,GAAG,WAAW;AASpC,OAAO,MAAMC,SAAS,GAAG,WAAW;AAMpC,OAAO,MAAMC,MAAM,GAAG,QAAQ;AAQ9B,OAAO,MAAMC,MAAM,GAAG,QAAQ;AAM9B,OAAO,MAAMC,kBAAkB,GAAG,oBAAoB;AAOtD,OAAO,MAAMC,UAAU,GAAG,YAAY;AAItC,OAAO,MAAMC,SAAS,GAAG,WAAW;AAGpC;;;AAGA,OAAO,MAAMC,IAAI,GAAG,MAAM;AAM1B,OAAO,MAAMC,UAAU,GAAG,YAAY;AAStC,OAAO,MAAMC,gBAAgB,GAAG,kBAAkB;AAQlD,OAAO,MAAMC,YAAY,GAAG,cAAc;AAgB1C,OAAO,MAAMC,WAAW,GAAG,aAAa;AAiBxC,OAAO,MAAMC,oBAAoB,GAAG,sBAAsB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}