{"ast": null, "code": "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { Diag, util } from '@tensorflow/tfjs-core';\nimport { DiagProgram } from '../diag_gpu';\nimport { reshape } from './Reshape';\nexport function diag(args) {\n  const {\n    inputs,\n    backend\n  } = args;\n  const {\n    x\n  } = inputs;\n  const outShape = [...x.shape, ...x.shape];\n  const xSize = util.sizeFromShape(x.shape);\n  const flat = reshape({\n    inputs: {\n      x\n    },\n    backend,\n    attrs: {\n      shape: [xSize]\n    }\n  });\n  const program = new DiagProgram(xSize);\n  const res = backend.runWebGLProgram(program, [flat], flat.dtype);\n  const out = reshape({\n    inputs: {\n      x: res\n    },\n    backend,\n    attrs: {\n      shape: outShape\n    }\n  });\n  backend.disposeIntermediateTensorInfo(flat);\n  backend.disposeIntermediateTensorInfo(res);\n  return out;\n}\nexport const diagConfig = {\n  kernelName: Diag,\n  backendName: 'webgl',\n  kernelFunc: diag\n};", "map": {"version": 3, "names": ["Diag", "util", "DiagProgram", "reshape", "diag", "args", "inputs", "backend", "x", "outShape", "shape", "xSize", "sizeFromShape", "flat", "attrs", "program", "res", "runWebGLProgram", "dtype", "out", "disposeIntermediateTensorInfo", "diagConfig", "kernelName", "backendName", "kernelFunc"], "sources": ["C:\\tfjs-backend-webgl\\src\\kernels\\Diag.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {Diag, DiagInputs, KernelConfig, KernelFunc, TensorInfo, util} from '@tensorflow/tfjs-core';\n\nimport {MathBackendWebGL} from '../backend_webgl';\nimport {DiagProgram} from '../diag_gpu';\nimport {reshape} from './Reshape';\n\nexport function diag(args: {inputs: DiagInputs, backend: MathBackendWebGL}):\n    TensorInfo {\n  const {inputs, backend} = args;\n  const {x} = inputs;\n\n  const outShape = [...x.shape, ...x.shape];\n  const xSize = util.sizeFromShape(x.shape);\n\n  const flat = reshape({inputs: {x}, backend, attrs: {shape: [xSize]}});\n\n  const program = new DiagProgram(xSize);\n  const res = backend.runWebGLProgram(program, [flat], flat.dtype);\n\n  const out = reshape({inputs: {x: res}, backend, attrs: {shape: outShape}});\n\n  backend.disposeIntermediateTensorInfo(flat);\n  backend.disposeIntermediateTensorInfo(res);\n\n  return out;\n}\n\nexport const diagConfig: KernelConfig = {\n  kernelName: Diag,\n  backendName: 'webgl',\n  kernelFunc: diag as unknown as KernelFunc\n};\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAiBA,SAAQA,IAAI,EAAoDC,IAAI,QAAO,uBAAuB;AAGlG,SAAQC,WAAW,QAAO,aAAa;AACvC,SAAQC,OAAO,QAAO,WAAW;AAEjC,OAAM,SAAUC,IAAIA,CAACC,IAAqD;EAExE,MAAM;IAACC,MAAM;IAAEC;EAAO,CAAC,GAAGF,IAAI;EAC9B,MAAM;IAACG;EAAC,CAAC,GAAGF,MAAM;EAElB,MAAMG,QAAQ,GAAG,CAAC,GAAGD,CAAC,CAACE,KAAK,EAAE,GAAGF,CAAC,CAACE,KAAK,CAAC;EACzC,MAAMC,KAAK,GAAGV,IAAI,CAACW,aAAa,CAACJ,CAAC,CAACE,KAAK,CAAC;EAEzC,MAAMG,IAAI,GAAGV,OAAO,CAAC;IAACG,MAAM,EAAE;MAACE;IAAC,CAAC;IAAED,OAAO;IAAEO,KAAK,EAAE;MAACJ,KAAK,EAAE,CAACC,KAAK;IAAC;EAAC,CAAC,CAAC;EAErE,MAAMI,OAAO,GAAG,IAAIb,WAAW,CAACS,KAAK,CAAC;EACtC,MAAMK,GAAG,GAAGT,OAAO,CAACU,eAAe,CAACF,OAAO,EAAE,CAACF,IAAI,CAAC,EAAEA,IAAI,CAACK,KAAK,CAAC;EAEhE,MAAMC,GAAG,GAAGhB,OAAO,CAAC;IAACG,MAAM,EAAE;MAACE,CAAC,EAAEQ;IAAG,CAAC;IAAET,OAAO;IAAEO,KAAK,EAAE;MAACJ,KAAK,EAAED;IAAQ;EAAC,CAAC,CAAC;EAE1EF,OAAO,CAACa,6BAA6B,CAACP,IAAI,CAAC;EAC3CN,OAAO,CAACa,6BAA6B,CAACJ,GAAG,CAAC;EAE1C,OAAOG,GAAG;AACZ;AAEA,OAAO,MAAME,UAAU,GAAiB;EACtCC,UAAU,EAAEtB,IAAI;EAChBuB,WAAW,EAAE,OAAO;EACpBC,UAAU,EAAEpB;CACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}