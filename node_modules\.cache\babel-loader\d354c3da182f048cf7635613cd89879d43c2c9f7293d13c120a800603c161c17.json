{"ast": null, "code": "/**\n * @license\n * Copyright 2020 Google Inc. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { ENGINE } from '../engine';\nimport { Mean } from '../kernel_names';\nimport { convertToTensor } from '../tensor_util_env';\nimport { op } from './operation';\n/**\n * Computes the mean of elements across dimensions of a `tf.Tensor`.\n *\n * Reduces `x` along the dimensions given in `axis`. Unless `keepDims` is\n * true, the rank of the `tf.Tensor` is reduced by 1 for each entry in `axis`.\n * If `keepDims` is true, the reduced dimensions are retained with length 1.\n * If `axis` has no entries, all dimensions are reduced, and a `tf.Tensor` with\n * a single element is returned.\n *\n * ```js\n * const x = tf.tensor1d([1, 2, 3]);\n *\n * x.mean().print();  // or tf.mean(a)\n * ```\n *\n * ```js\n * const x = tf.tensor2d([1, 2, 3, 4], [2, 2]);\n *\n * const axis = 1;\n * x.mean(axis).print();  // or tf.mean(x, axis)\n * ```\n *\n * @param x The input tensor.\n * @param axis The dimension(s) to reduce. By default it reduces\n *     all dimensions.\n * @param keepDims If true, retains reduced dimensions with size 1.\n *\n * @doc {heading: 'Operations', subheading: 'Reduction'}\n */\nfunction mean_(x, axis = null, keepDims = false) {\n  const $x = convertToTensor(x, 'x', 'mean');\n  const inputs = {\n    x: $x\n  };\n  const attrs = {\n    axis,\n    keepDims\n  };\n  return ENGINE.runKernel(Mean, inputs, attrs);\n}\nexport const mean = /* @__PURE__ */op({\n  mean_\n});", "map": {"version": 3, "names": ["ENGINE", "Mean", "convertToTensor", "op", "mean_", "x", "axis", "keepDims", "$x", "inputs", "attrs", "runKernel", "mean"], "sources": ["C:\\tfjs-core\\src\\ops\\mean.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google Inc. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {ENGINE} from '../engine';\nimport {Mean, MeanAttrs, MeanInputs} from '../kernel_names';\nimport {NamedAttrMap} from '../kernel_registry';\nimport {Tensor} from '../tensor';\nimport {NamedTensorMap} from '../tensor_types';\nimport {convertToTensor} from '../tensor_util_env';\nimport {TensorLike} from '../types';\n\nimport {op} from './operation';\n\n/**\n * Computes the mean of elements across dimensions of a `tf.Tensor`.\n *\n * Reduces `x` along the dimensions given in `axis`. Unless `keepDims` is\n * true, the rank of the `tf.Tensor` is reduced by 1 for each entry in `axis`.\n * If `keepDims` is true, the reduced dimensions are retained with length 1.\n * If `axis` has no entries, all dimensions are reduced, and a `tf.Tensor` with\n * a single element is returned.\n *\n * ```js\n * const x = tf.tensor1d([1, 2, 3]);\n *\n * x.mean().print();  // or tf.mean(a)\n * ```\n *\n * ```js\n * const x = tf.tensor2d([1, 2, 3, 4], [2, 2]);\n *\n * const axis = 1;\n * x.mean(axis).print();  // or tf.mean(x, axis)\n * ```\n *\n * @param x The input tensor.\n * @param axis The dimension(s) to reduce. By default it reduces\n *     all dimensions.\n * @param keepDims If true, retains reduced dimensions with size 1.\n *\n * @doc {heading: 'Operations', subheading: 'Reduction'}\n */\nfunction mean_<T extends Tensor>(\n    x: Tensor|TensorLike, axis: number|number[] = null, keepDims = false): T {\n  const $x = convertToTensor(x, 'x', 'mean');\n\n  const inputs: MeanInputs = {x: $x};\n  const attrs: MeanAttrs = {axis, keepDims};\n\n  return ENGINE.runKernel(\n      Mean, inputs as unknown as NamedTensorMap,\n      attrs as unknown as NamedAttrMap);\n}\n\nexport const mean = /* @__PURE__ */ op({mean_});\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAiBA,SAAQA,MAAM,QAAO,WAAW;AAChC,SAAQC,IAAI,QAA8B,iBAAiB;AAI3D,SAAQC,eAAe,QAAO,oBAAoB;AAGlD,SAAQC,EAAE,QAAO,aAAa;AAE9B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6BA,SAASC,KAAKA,CACVC,CAAoB,EAAEC,IAAA,GAAwB,IAAI,EAAEC,QAAQ,GAAG,KAAK;EACtE,MAAMC,EAAE,GAAGN,eAAe,CAACG,CAAC,EAAE,GAAG,EAAE,MAAM,CAAC;EAE1C,MAAMI,MAAM,GAAe;IAACJ,CAAC,EAAEG;EAAE,CAAC;EAClC,MAAME,KAAK,GAAc;IAACJ,IAAI;IAAEC;EAAQ,CAAC;EAEzC,OAAOP,MAAM,CAACW,SAAS,CACnBV,IAAI,EAAEQ,MAAmC,EACzCC,KAAgC,CAAC;AACvC;AAEA,OAAO,MAAME,IAAI,GAAG,eAAgBT,EAAE,CAAC;EAACC;AAAK,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}