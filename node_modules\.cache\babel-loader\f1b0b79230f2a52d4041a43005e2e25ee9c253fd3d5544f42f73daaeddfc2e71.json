{"ast": null, "code": "import _ from './underscore.js';\nimport './toPath.js';\n\n// Internal wrapper for `_.toPath` to enable minification.\n// Similar to `cb` for `_.iteratee`.\nexport default function toPath(path) {\n  return _.toPath(path);\n}", "map": {"version": 3, "names": ["_", "to<PERSON><PERSON>", "path"], "sources": ["C:/tmsft/node_modules/underscore/modules/_toPath.js"], "sourcesContent": ["import _ from './underscore.js';\nimport './toPath.js';\n\n// Internal wrapper for `_.toPath` to enable minification.\n// Similar to `cb` for `_.iteratee`.\nexport default function toPath(path) {\n  return _.toPath(path);\n}\n"], "mappings": "AAAA,OAAOA,CAAC,MAAM,iBAAiB;AAC/B,OAAO,aAAa;;AAEpB;AACA;AACA,eAAe,SAASC,MAAMA,CAACC,IAAI,EAAE;EACnC,OAAOF,CAAC,CAACC,MAAM,CAACC,IAAI,CAAC;AACvB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}