{"ast": null, "code": "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { env, ResizeBilinear } from '@tensorflow/tfjs-core';\nimport { ResizeBilinearProgram } from '../resize_bilinear_gpu';\nimport { ResizeBilinearPackedProgram } from '../resize_bilinear_packed_gpu';\nexport function resizeBilinear(args) {\n  const {\n    inputs,\n    backend,\n    attrs\n  } = args;\n  const {\n    images\n  } = inputs;\n  const {\n    alignCorners,\n    halfPixelCenters,\n    size\n  } = attrs;\n  const [newHeight, newWidth] = size;\n  const program = env().getBool('WEBGL_PACK_IMAGE_OPERATIONS') ? new ResizeBilinearPackedProgram(images.shape, newHeight, newWidth, alignCorners, halfPixelCenters) : new ResizeBilinearProgram(images.shape, newHeight, newWidth, alignCorners, halfPixelCenters);\n  return backend.runWebGLProgram(program, [images], 'float32');\n}\nexport const resizeBilinearConfig = {\n  kernelName: ResizeBilinear,\n  backendName: 'webgl',\n  kernelFunc: resizeBilinear\n};", "map": {"version": 3, "names": ["env", "ResizeBilinear", "ResizeBilinearProgram", "ResizeBilinearPackedProgram", "resizeBilinear", "args", "inputs", "backend", "attrs", "images", "alignCorners", "halfPixelCenters", "size", "newHeight", "newWidth", "program", "getBool", "shape", "runWebGLProgram", "resizeBilinearConfig", "kernelName", "backendName", "kernelFunc"], "sources": ["C:\\tfjs-backend-webgl\\src\\kernels\\ResizeBilinear.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {env, KernelConfig, KernelFunc, ResizeBilinear, ResizeBilinearAttrs, ResizeBilinearInputs, TensorInfo} from '@tensorflow/tfjs-core';\n\nimport {MathBackendWebGL} from '../backend_webgl';\nimport {ResizeBilinearProgram} from '../resize_bilinear_gpu';\nimport {ResizeBilinearPackedProgram} from '../resize_bilinear_packed_gpu';\n\nexport function resizeBilinear(args: {\n  inputs: ResizeBilinearInputs,\n  backend: MathBackendWebGL,\n  attrs: ResizeBilinearAttrs\n}): TensorInfo {\n  const {inputs, backend, attrs} = args;\n  const {images} = inputs;\n  const {alignCorners, halfPixelCenters, size} = attrs;\n\n  const [newHeight, newWidth] = size;\n\n  const program = env().getBool('WEBGL_PACK_IMAGE_OPERATIONS') ?\n      new ResizeBilinearPackedProgram(\n          images.shape as [number, number, number, number], newHeight, newWidth,\n          alignCorners, halfPixelCenters) :\n      new ResizeBilinearProgram(\n          images.shape as [number, number, number, number], newHeight, newWidth,\n          alignCorners, halfPixelCenters);\n  return backend.runWebGLProgram(program, [images], 'float32');\n}\n\nexport const resizeBilinearConfig: KernelConfig = {\n  kernelName: ResizeBilinear,\n  backendName: 'webgl',\n  kernelFunc: resizeBilinear as unknown as KernelFunc\n};\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAiBA,SAAQA,GAAG,EAA4BC,cAAc,QAA8D,uBAAuB;AAG1I,SAAQC,qBAAqB,QAAO,wBAAwB;AAC5D,SAAQC,2BAA2B,QAAO,+BAA+B;AAEzE,OAAM,SAAUC,cAAcA,CAACC,IAI9B;EACC,MAAM;IAACC,MAAM;IAAEC,OAAO;IAAEC;EAAK,CAAC,GAAGH,IAAI;EACrC,MAAM;IAACI;EAAM,CAAC,GAAGH,MAAM;EACvB,MAAM;IAACI,YAAY;IAAEC,gBAAgB;IAAEC;EAAI,CAAC,GAAGJ,KAAK;EAEpD,MAAM,CAACK,SAAS,EAAEC,QAAQ,CAAC,GAAGF,IAAI;EAElC,MAAMG,OAAO,GAAGf,GAAG,EAAE,CAACgB,OAAO,CAAC,6BAA6B,CAAC,GACxD,IAAIb,2BAA2B,CAC3BM,MAAM,CAACQ,KAAyC,EAAEJ,SAAS,EAAEC,QAAQ,EACrEJ,YAAY,EAAEC,gBAAgB,CAAC,GACnC,IAAIT,qBAAqB,CACrBO,MAAM,CAACQ,KAAyC,EAAEJ,SAAS,EAAEC,QAAQ,EACrEJ,YAAY,EAAEC,gBAAgB,CAAC;EACvC,OAAOJ,OAAO,CAACW,eAAe,CAACH,OAAO,EAAE,CAACN,MAAM,CAAC,EAAE,SAAS,CAAC;AAC9D;AAEA,OAAO,MAAMU,oBAAoB,GAAiB;EAChDC,UAAU,EAAEnB,cAAc;EAC1BoB,WAAW,EAAE,OAAO;EACpBC,UAAU,EAAElB;CACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}