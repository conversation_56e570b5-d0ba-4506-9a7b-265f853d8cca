{"ast": null, "code": "/**\n * @license\n * Copyright 2018 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { ENGINE } from '../engine';\nimport { GatherV2 } from '../kernel_names';\nimport { convertToTensor } from '../tensor_util_env';\nimport { op } from './operation';\n/**\n * Gather slices from tensor `x`'s axis `axis` according to `indices`.\n *\n * ```js\n * const x = tf.tensor1d([1, 2, 3, 4]);\n * const indices = tf.tensor1d([1, 3, 3], 'int32');\n *\n * x.gather(indices).print();\n * ```\n *\n * ```js\n * const x = tf.tensor2d([1, 2, 3, 4], [2, 2]);\n * const indices = tf.tensor1d([1, 1, 0], 'int32');\n *\n * x.gather(indices).print();\n * ```\n * @param x The input tensor whose slices are to be gathered.\n * @param indices The indices of the values to extract.\n * @param axis The axis over which to select values. Defaults to 0.\n * @param batchDims Optional. The number of batch dimensions. It must be less\n *     than or equal to rank(indices). Defaults to 0.\n *     The output tensor will have shape of\n *     `x.shape[:axis] + indices.shape[batchDims:] + x.shape[axis + 1:]`\n *\n * @doc {heading: 'Tensors', subheading: 'Slicing and Joining'}\n */\nfunction gather_(x, indices) {\n  let axis = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 0;\n  let batchDims = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : 0;\n  const $x = convertToTensor(x, 'x', 'gather');\n  const $indices = convertToTensor(indices, 'indices', 'gather', 'int32');\n  const inputs = {\n    x: $x,\n    indices: $indices\n  };\n  const attrs = {\n    axis,\n    batchDims\n  };\n  return ENGINE.runKernel(GatherV2, inputs, attrs);\n}\nexport const gather = /* @__PURE__ */op({\n  gather_\n});", "map": {"version": 3, "names": ["ENGINE", "GatherV2", "convertToTensor", "op", "gather_", "x", "indices", "axis", "arguments", "length", "undefined", "batchDims", "$x", "$indices", "inputs", "attrs", "runKernel", "gather"], "sources": ["C:\\tfjs-core\\src\\ops\\gather.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2018 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {ENGINE} from '../engine';\nimport {GatherV2, GatherV2Attrs, GatherV2Inputs} from '../kernel_names';\nimport {NamedAttrMap} from '../kernel_registry';\nimport {Tensor} from '../tensor';\nimport {NamedTensorMap} from '../tensor_types';\nimport {convertToTensor} from '../tensor_util_env';\nimport {TensorLike} from '../types';\n\nimport {op} from './operation';\n\n/**\n * Gather slices from tensor `x`'s axis `axis` according to `indices`.\n *\n * ```js\n * const x = tf.tensor1d([1, 2, 3, 4]);\n * const indices = tf.tensor1d([1, 3, 3], 'int32');\n *\n * x.gather(indices).print();\n * ```\n *\n * ```js\n * const x = tf.tensor2d([1, 2, 3, 4], [2, 2]);\n * const indices = tf.tensor1d([1, 1, 0], 'int32');\n *\n * x.gather(indices).print();\n * ```\n * @param x The input tensor whose slices are to be gathered.\n * @param indices The indices of the values to extract.\n * @param axis The axis over which to select values. Defaults to 0.\n * @param batchDims Optional. The number of batch dimensions. It must be less\n *     than or equal to rank(indices). Defaults to 0.\n *     The output tensor will have shape of\n *     `x.shape[:axis] + indices.shape[batchDims:] + x.shape[axis + 1:]`\n *\n * @doc {heading: 'Tensors', subheading: 'Slicing and Joining'}\n */\nfunction gather_<T extends Tensor>(\n    x: T|TensorLike, indices: Tensor|TensorLike, axis = 0, batchDims = 0): T {\n  const $x = convertToTensor(x, 'x', 'gather');\n  const $indices = convertToTensor(indices, 'indices', 'gather', 'int32');\n\n  const inputs: GatherV2Inputs = {x: $x, indices: $indices};\n  const attrs: GatherV2Attrs = {axis, batchDims};\n\n  return ENGINE.runKernel(\n      GatherV2, inputs as unknown as NamedTensorMap,\n      attrs as unknown as NamedAttrMap);\n}\n\nexport const gather = /* @__PURE__ */ op({gather_});\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAiBA,SAAQA,MAAM,QAAO,WAAW;AAChC,SAAQC,QAAQ,QAAsC,iBAAiB;AAIvE,SAAQC,eAAe,QAAO,oBAAoB;AAGlD,SAAQC,EAAE,QAAO,aAAa;AAE9B;;;;;;;;;;;;;;;;;;;;;;;;;;AA0BA,SAASC,OAAOA,CACZC,CAAe,EAAEC,OAA0B,EAAyB;EAAA,IAAvBC,IAAI,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC;EAAA,IAAEG,SAAS,GAAAH,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC;EACtE,MAAMI,EAAE,GAAGV,eAAe,CAACG,CAAC,EAAE,GAAG,EAAE,QAAQ,CAAC;EAC5C,MAAMQ,QAAQ,GAAGX,eAAe,CAACI,OAAO,EAAE,SAAS,EAAE,QAAQ,EAAE,OAAO,CAAC;EAEvE,MAAMQ,MAAM,GAAmB;IAACT,CAAC,EAAEO,EAAE;IAAEN,OAAO,EAAEO;EAAQ,CAAC;EACzD,MAAME,KAAK,GAAkB;IAACR,IAAI;IAAEI;EAAS,CAAC;EAE9C,OAAOX,MAAM,CAACgB,SAAS,CACnBf,QAAQ,EAAEa,MAAmC,EAC7CC,KAAgC,CAAC;AACvC;AAEA,OAAO,MAAME,MAAM,GAAG,eAAgBd,EAAE,CAAC;EAACC;AAAO,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}