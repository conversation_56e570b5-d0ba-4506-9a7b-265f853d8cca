{"ast": null, "code": "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { Acosh } from '../kernel_names';\nimport { cast } from '../ops/cast';\nimport { div } from '../ops/div';\nimport { sqrt } from '../ops/sqrt';\nimport { square } from '../ops/square';\nimport { sub } from '../ops/sub';\nexport const acoshGradConfig = {\n  kernelName: Acosh,\n  inputsToSave: ['x'],\n  gradFunc: (dy, saved) => {\n    const [x] = saved;\n    return {\n      x: () => {\n        const a = sqrt(sub(square(cast(x, 'float32')), 1));\n        return div(dy, a);\n      }\n    };\n  }\n};", "map": {"version": 3, "names": ["Acosh", "cast", "div", "sqrt", "square", "sub", "acoshGradConfig", "kernelName", "inputsToSave", "grad<PERSON>unc", "dy", "saved", "x", "a"], "sources": ["C:\\tfjs-core\\src\\gradients\\Acosh_grad.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {Acosh} from '../kernel_names';\nimport {GradConfig} from '../kernel_registry';\nimport {cast} from '../ops/cast';\nimport {div} from '../ops/div';\nimport {sqrt} from '../ops/sqrt';\nimport {square} from '../ops/square';\nimport {sub} from '../ops/sub';\nimport {Tensor} from '../tensor';\n\nexport const acoshGradConfig: GradConfig = {\n  kernelName: Acosh,\n  inputsToSave: ['x'],\n  gradFunc: (dy: Tensor, saved: Tensor[]) => {\n    const [x] = saved;\n\n    return {\n      x: () => {\n        const a = sqrt(sub(square(cast(x, 'float32')), 1));\n        return div(dy, a);\n      }\n    };\n  }\n};\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAiBA,SAAQA,KAAK,QAAO,iBAAiB;AAErC,SAAQC,IAAI,QAAO,aAAa;AAChC,SAAQC,GAAG,QAAO,YAAY;AAC9B,SAAQC,IAAI,QAAO,aAAa;AAChC,SAAQC,MAAM,QAAO,eAAe;AACpC,SAAQC,GAAG,QAAO,YAAY;AAG9B,OAAO,MAAMC,eAAe,GAAe;EACzCC,UAAU,EAAEP,KAAK;EACjBQ,YAAY,EAAE,CAAC,GAAG,CAAC;EACnBC,QAAQ,EAAEA,CAACC,EAAU,EAAEC,KAAe,KAAI;IACxC,MAAM,CAACC,CAAC,CAAC,GAAGD,KAAK;IAEjB,OAAO;MACLC,CAAC,EAAEA,CAAA,KAAK;QACN,MAAMC,CAAC,GAAGV,IAAI,CAACE,GAAG,CAACD,MAAM,CAACH,IAAI,CAACW,CAAC,EAAE,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAClD,OAAOV,GAAG,CAACQ,EAAE,EAAEG,CAAC,CAAC;MACnB;KACD;EACH;CACD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}