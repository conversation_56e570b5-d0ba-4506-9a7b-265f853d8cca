{"ast": null, "code": "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { reshape } from '../../ops/reshape';\nimport { getGlobalTensorClass } from '../../tensor';\nimport { assert } from '../../util';\n/**\n * Converts a size-1 `tf.Tensor` to a `tf.Scalar`.\n * @doc {heading: 'Tensors', subheading: 'Classes'}\n */\ngetGlobalTensorClass().prototype.asScalar = function () {\n  this.throwIfDisposed();\n  assert(this.size === 1, () => 'The array must have only 1 element.');\n  return reshape(this, []);\n};", "map": {"version": 3, "names": ["reshape", "getGlobalTensorClass", "assert", "prototype", "asScalar", "throwIfDisposed", "size"], "sources": ["C:\\tfjs-core\\src\\public\\chained_ops\\as_scalar.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {reshape} from '../../ops/reshape';\nimport {getGlobalTensorClass, Scalar, Tensor} from '../../tensor';\nimport {Rank} from '../../types';\nimport {assert} from '../../util';\n\ndeclare module '../../tensor' {\n  interface Tensor<R extends Rank = Rank> {\n    asScalar<T extends Tensor>(): Scalar;\n  }\n}\n\n/**\n * Converts a size-1 `tf.Tensor` to a `tf.Scalar`.\n * @doc {heading: 'Tensors', subheading: 'Classes'}\n */\ngetGlobalTensorClass().prototype.asScalar = function<T extends Tensor>(this: T):\n    Scalar {\n  this.throwIfDisposed();\n  assert(this.size === 1, () => 'The array must have only 1 element.');\n  return reshape(this, []);\n};\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAiBA,SAAQA,OAAO,QAAO,mBAAmB;AACzC,SAAQC,oBAAoB,QAAuB,cAAc;AAEjE,SAAQC,MAAM,QAAO,YAAY;AAQjC;;;;AAIAD,oBAAoB,EAAE,CAACE,SAAS,CAACC,QAAQ,GAAG;EAE1C,IAAI,CAACC,eAAe,EAAE;EACtBH,MAAM,CAAC,IAAI,CAACI,IAAI,KAAK,CAAC,EAAE,MAAM,qCAAqC,CAAC;EACpE,OAAON,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC;AAC1B,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}