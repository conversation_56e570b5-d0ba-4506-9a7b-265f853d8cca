{"ast": null, "code": "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { Sin } from '@tensorflow/tfjs-core';\nimport { CHECK_NAN_SNIPPET_PACKED } from '../binaryop_packed_gpu';\nimport { CHECK_NAN_SNIPPET_UNARY, unaryKernelFunc } from '../kernel_utils/kernel_funcs_utils';\nconst SIN = CHECK_NAN_SNIPPET_UNARY + `\n  return sin(x);\n`;\nconst SIN_PACKED = `\n  vec4 result = sin(x);\n  bvec4 isNaN = isnan(x);\n  ${CHECK_NAN_SNIPPET_PACKED}\n  return result;\n`;\nexport const sin = unaryKernelFunc({\n  opSnippet: SIN,\n  packedOpSnippet: SIN_PACKED\n});\nexport const sinConfig = {\n  kernelName: Sin,\n  backendName: 'webgl',\n  kernelFunc: sin\n};", "map": {"version": 3, "names": ["Sin", "CHECK_NAN_SNIPPET_PACKED", "CHECK_NAN_SNIPPET_UNARY", "unaryKernelFunc", "SIN", "SIN_PACKED", "sin", "opSnippet", "packedOpSnippet", "sinConfig", "kernelName", "backendName", "kernelFunc"], "sources": ["C:\\tfjs-backend-webgl\\src\\kernels\\Sin.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {KernelConfig, Sin} from '@tensorflow/tfjs-core';\n\nimport {CHECK_NAN_SNIPPET_PACKED} from '../binaryop_packed_gpu';\nimport {CHECK_NAN_SNIPPET_UNARY, unaryKernelFunc} from '../kernel_utils/kernel_funcs_utils';\n\nconst SIN = CHECK_NAN_SNIPPET_UNARY + `\n  return sin(x);\n`;\n\nconst SIN_PACKED = `\n  vec4 result = sin(x);\n  bvec4 isNaN = isnan(x);\n  ${CHECK_NAN_SNIPPET_PACKED}\n  return result;\n`;\n\nexport const sin =\n    unaryKernelFunc({opSnippet: SIN, packedOpSnippet: SIN_PACKED});\n\nexport const sinConfig: KernelConfig = {\n  kernelName: Sin,\n  backendName: 'webgl',\n  kernelFunc: sin,\n};\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAiBA,SAAsBA,GAAG,QAAO,uBAAuB;AAEvD,SAAQC,wBAAwB,QAAO,wBAAwB;AAC/D,SAAQC,uBAAuB,EAAEC,eAAe,QAAO,oCAAoC;AAE3F,MAAMC,GAAG,GAAGF,uBAAuB,GAAG;;CAErC;AAED,MAAMG,UAAU,GAAG;;;IAGfJ,wBAAwB;;CAE3B;AAED,OAAO,MAAMK,GAAG,GACZH,eAAe,CAAC;EAACI,SAAS,EAAEH,GAAG;EAAEI,eAAe,EAAEH;AAAU,CAAC,CAAC;AAElE,OAAO,MAAMI,SAAS,GAAiB;EACrCC,UAAU,EAAEV,GAAG;EACfW,WAAW,EAAE,OAAO;EACpBC,UAAU,EAAEN;CACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}