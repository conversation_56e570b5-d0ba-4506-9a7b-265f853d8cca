{"ast": null, "code": "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { conv2dTranspose } from '../../ops/conv2d_transpose';\nimport { getGlobalTensorClass } from '../../tensor';\ngetGlobalTensorClass().prototype.conv2dTranspose = function (filter, outputShape, strides, pad, dimRoundingMode) {\n  this.throwIfDisposed();\n  return conv2dTranspose(this, filter, outputShape, strides, pad, dimRoundingMode);\n};", "map": {"version": 3, "names": ["conv2dTranspose", "getGlobalTensorClass", "prototype", "filter", "outputShape", "strides", "pad", "dimRoundingMode", "throwIfDisposed"], "sources": ["C:\\tfjs-core\\src\\public\\chained_ops\\conv2d_transpose.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport {conv2dTranspose} from '../../ops/conv2d_transpose';\nimport {getGlobalTensorClass, Tensor3D, Tensor4D} from '../../tensor';\nimport {Rank, TensorLike4D} from '../../types';\n\ndeclare module '../../tensor' {\n  interface Tensor<R extends Rank = Rank> {\n    conv2dTranspose<T extends Tensor3D|Tensor4D>(\n        filter: Tensor4D|TensorLike4D,\n        outputShape: [number, number, number, number]|[number, number, number],\n        strides: [number, number]|number, pad: 'valid'|'same'|number,\n        dimRoundingMode?: 'floor'|'round'|'ceil'): T;\n  }\n}\n\ngetGlobalTensorClass().prototype.conv2dTranspose =\n    function<T extends Tensor3D|Tensor4D>(\n        filter: Tensor4D|TensorLike4D,\n        outputShape: [number, number, number, number]|[number, number, number],\n        strides: [number, number]|number, pad: 'valid'|'same'|number,\n        dimRoundingMode?: 'floor'|'round'|'ceil'): T {\n  this.throwIfDisposed();\n  return conv2dTranspose(\n             this, filter, outputShape, strides, pad, dimRoundingMode) as T;\n};\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAgBA,SAAQA,eAAe,QAAO,4BAA4B;AAC1D,SAAQC,oBAAoB,QAA2B,cAAc;AAarEA,oBAAoB,EAAE,CAACC,SAAS,CAACF,eAAe,GAC5C,UACIG,MAA6B,EAC7BC,WAAsE,EACtEC,OAAgC,EAAEC,GAA0B,EAC5DC,eAAwC;EAC9C,IAAI,CAACC,eAAe,EAAE;EACtB,OAAOR,eAAe,CACX,IAAI,EAAEG,MAAM,EAAEC,WAAW,EAAEC,OAAO,EAAEC,GAAG,EAAEC,eAAe,CAAM;AAC3E,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}