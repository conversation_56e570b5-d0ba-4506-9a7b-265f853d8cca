{"ast": null, "code": "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { SpaceToBatchND } from '../kernel_names';\nimport { batchToSpaceND } from '../ops/batch_to_space_nd';\nexport const spaceToBatchNDGradConfig = {\n  kernelName: SpaceToBatchND,\n  gradFunc: (dy, saved, attrs) => {\n    const {\n      blockShape,\n      paddings\n    } = attrs;\n    return {\n      x: () => batchToSpaceND(dy, blockShape, paddings)\n    };\n  }\n};", "map": {"version": 3, "names": ["SpaceToBatchND", "batchToSpaceND", "spaceToBatchNDGradConfig", "kernelName", "grad<PERSON>unc", "dy", "saved", "attrs", "blockShape", "paddings", "x"], "sources": ["C:\\tfjs-core\\src\\gradients\\SpaceToBatchND_grad.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {SpaceToBatchND, SpaceToBatchNDAttrs} from '../kernel_names';\nimport {GradConfig, NamedAttrMap} from '../kernel_registry';\nimport {batchToSpaceND} from '../ops/batch_to_space_nd';\nimport {Tensor} from '../tensor';\n\nexport const spaceToBatchNDGradConfig: GradConfig = {\n  kernelName: SpaceToBatchND,\n  gradFunc: (dy: Tensor, saved: Tensor[], attrs: NamedAttrMap) => {\n    const {blockShape, paddings} = attrs as unknown as SpaceToBatchNDAttrs;\n    return {x: () => batchToSpaceND(dy, blockShape, paddings)};\n  }\n};\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAiBA,SAAQA,cAAc,QAA4B,iBAAiB;AAEnE,SAAQC,cAAc,QAAO,0BAA0B;AAGvD,OAAO,MAAMC,wBAAwB,GAAe;EAClDC,UAAU,EAAEH,cAAc;EAC1BI,QAAQ,EAAEA,CAACC,EAAU,EAAEC,KAAe,EAAEC,KAAmB,KAAI;IAC7D,MAAM;MAACC,UAAU;MAAEC;IAAQ,CAAC,GAAGF,KAAuC;IACtE,OAAO;MAACG,CAAC,EAAEA,CAAA,KAAMT,cAAc,CAACI,EAAE,EAAEG,UAAU,EAAEC,QAAQ;IAAC,CAAC;EAC5D;CACD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}