{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.transformArguments = exports.IS_READ_ONLY = exports.FIRST_KEY_INDEX = void 0;\nexports.FIRST_KEY_INDEX = 1;\nexports.IS_READ_ONLY = true;\nfunction transformArguments(key, element, options) {\n  const args = ['LPOS', key, element];\n  if (typeof options?.RANK === 'number') {\n    args.push('RANK', options.RANK.toString());\n  }\n  if (typeof options?.MAXLEN === 'number') {\n    args.push('MAXLEN', options.MAXLEN.toString());\n  }\n  return args;\n}\nexports.transformArguments = transformArguments;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "transformArguments", "IS_READ_ONLY", "FIRST_KEY_INDEX", "key", "element", "options", "args", "RANK", "push", "toString", "MAXLEN"], "sources": ["C:/tmsft/node_modules/@redis/client/dist/lib/commands/LPOS.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.transformArguments = exports.IS_READ_ONLY = exports.FIRST_KEY_INDEX = void 0;\nexports.FIRST_KEY_INDEX = 1;\nexports.IS_READ_ONLY = true;\nfunction transformArguments(key, element, options) {\n    const args = ['LPOS', key, element];\n    if (typeof options?.RANK === 'number') {\n        args.push('RANK', options.RANK.toString());\n    }\n    if (typeof options?.MAXLEN === 'number') {\n        args.push('MAXLEN', options.MAXLEN.toString());\n    }\n    return args;\n}\nexports.transformArguments = transformArguments;\n"], "mappings": "AAAA,YAAY;;AACZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEC,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7DD,OAAO,CAACE,kBAAkB,GAAGF,OAAO,CAACG,YAAY,GAAGH,OAAO,CAACI,eAAe,GAAG,KAAK,CAAC;AACpFJ,OAAO,CAACI,eAAe,GAAG,CAAC;AAC3BJ,OAAO,CAACG,YAAY,GAAG,IAAI;AAC3B,SAASD,kBAAkBA,CAACG,GAAG,EAAEC,OAAO,EAAEC,OAAO,EAAE;EAC/C,MAAMC,IAAI,GAAG,CAAC,MAAM,EAAEH,GAAG,EAAEC,OAAO,CAAC;EACnC,IAAI,OAAOC,OAAO,EAAEE,IAAI,KAAK,QAAQ,EAAE;IACnCD,IAAI,CAACE,IAAI,CAAC,MAAM,EAAEH,OAAO,CAACE,IAAI,CAACE,QAAQ,CAAC,CAAC,CAAC;EAC9C;EACA,IAAI,OAAOJ,OAAO,EAAEK,MAAM,KAAK,QAAQ,EAAE;IACrCJ,IAAI,CAACE,IAAI,CAAC,QAAQ,EAAEH,OAAO,CAACK,MAAM,CAACD,QAAQ,CAAC,CAAC,CAAC;EAClD;EACA,OAAOH,IAAI;AACf;AACAR,OAAO,CAACE,kBAAkB,GAAGA,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}