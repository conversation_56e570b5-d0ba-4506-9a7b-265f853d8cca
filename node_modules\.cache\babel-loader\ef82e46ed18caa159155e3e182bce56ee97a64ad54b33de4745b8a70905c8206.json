{"ast": null, "code": "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { Tanh } from '@tensorflow/tfjs-core';\nimport { unaryKernelFunc } from '../kernel_utils/kernel_funcs_utils';\nconst TANH = `\n  float e2x = exp(-2.0 * abs(x));\n  return sign(x) * (1.0 - e2x) / (1.0 + e2x);\n`;\nexport const tanh = unaryKernelFunc({\n  opSnippet: TANH\n});\nexport const tanhConfig = {\n  kernelName: Tanh,\n  backendName: 'webgl',\n  kernelFunc: tanh\n};", "map": {"version": 3, "names": ["<PERSON><PERSON>", "unaryKernelFunc", "TANH", "tanh", "opSnippet", "tanhConfig", "kernelName", "backendName", "kernelFunc"], "sources": ["C:\\tfjs-backend-webgl\\src\\kernels\\Tanh.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {KernelConfig, Tanh} from '@tensorflow/tfjs-core';\nimport {unaryKernelFunc} from '../kernel_utils/kernel_funcs_utils';\n\nconst TANH = `\n  float e2x = exp(-2.0 * abs(x));\n  return sign(x) * (1.0 - e2x) / (1.0 + e2x);\n`;\n\nexport const tanh = unaryKernelFunc({opSnippet: TANH});\n\nexport const tanhConfig: KernelConfig = {\n  kernelName: Tanh,\n  backendName: 'webgl',\n  kernelFunc: tanh,\n};\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAiBA,SAAsBA,IAAI,QAAO,uBAAuB;AACxD,SAAQC,eAAe,QAAO,oCAAoC;AAElE,MAAMC,IAAI,GAAG;;;CAGZ;AAED,OAAO,MAAMC,IAAI,GAAGF,eAAe,CAAC;EAACG,SAAS,EAAEF;AAAI,CAAC,CAAC;AAEtD,OAAO,MAAMG,UAAU,GAAiB;EACtCC,UAAU,EAAEN,IAAI;EAChBO,WAAW,EAAE,OAAO;EACpBC,UAAU,EAAEL;CACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}