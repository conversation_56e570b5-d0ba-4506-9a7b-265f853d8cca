{"ast": null, "code": "var _jsxFileName = \"C:\\\\tmsft\\\\src\\\\components\\\\BankAccountManager.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useCallback } from 'react';\nimport { bankAccountService } from '../services/bankAccountService';\nimport { balanceManagementService } from '../services/balanceManagementService';\nimport './BankAccountManager.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport const BankAccountManager = ({\n  onAccountsUpdated\n}) => {\n  _s();\n  const [accounts, setAccounts] = useState(bankAccountService.getAllAccounts());\n  const [isAddingAccount, setIsAddingAccount] = useState(false);\n  const [editingAccount, setEditingAccount] = useState(null);\n  const [adjustingBalanceAccount, setAdjustingBalanceAccount] = useState(null);\n  const [formData, setFormData] = useState({\n    name: '',\n    accountNumber: '',\n    bankName: '',\n    currency: 'USD',\n    currentBalance: '0.00'\n  });\n  const [balanceFormData, setBalanceFormData] = useState({\n    newBalance: '',\n    effectiveDate: new Date().toISOString().split('T')[0],\n    reason: ''\n  });\n  const [errors, setErrors] = useState({});\n  const [balanceErrors, setBalanceErrors] = useState({});\n  const [showBalanceHistory, setShowBalanceHistory] = useState({});\n  const refreshAccounts = useCallback(() => {\n    setAccounts(bankAccountService.getAllAccounts());\n    if (onAccountsUpdated) {\n      onAccountsUpdated();\n    }\n  }, [onAccountsUpdated]);\n  const resetForm = useCallback(() => {\n    setFormData({\n      name: '',\n      accountNumber: '',\n      bankName: '',\n      currency: 'USD',\n      currentBalance: '0.00'\n    });\n    setErrors({});\n    setIsAddingAccount(false);\n    setEditingAccount(null);\n  }, []);\n  const validateForm = useCallback(data => {\n    const newErrors = {};\n    if (!data.name.trim()) {\n      newErrors.name = 'Account name is required';\n    }\n    if (!data.accountNumber.trim()) {\n      newErrors.accountNumber = 'Account number is required';\n    } else if (accounts.some(acc => acc.accountNumber === data.accountNumber.trim() && (!editingAccount || acc.id !== editingAccount.id))) {\n      newErrors.accountNumber = 'Account number already exists';\n    }\n    if (!data.bankName.trim()) {\n      newErrors.bankName = 'Bank name is required';\n    }\n    if (!data.currency.trim()) {\n      newErrors.currency = 'Currency is required';\n    }\n    const balance = parseFloat(data.currentBalance);\n    if (isNaN(balance)) {\n      newErrors.currentBalance = 'Current balance must be a valid number';\n    }\n    return newErrors;\n  }, [accounts, editingAccount]);\n  const handleInputChange = useCallback((field, value) => {\n    setFormData(prev => ({\n      ...prev,\n      [field]: value\n    }));\n    // Clear error for this field when user starts typing\n    if (errors[field]) {\n      setErrors(prev => ({\n        ...prev,\n        [field]: ''\n      }));\n    }\n  }, [errors]);\n  const handleAddAccount = useCallback(() => {\n    setIsAddingAccount(true);\n    setEditingAccount(null);\n    resetForm();\n  }, [resetForm]);\n  const handleEditAccount = useCallback(account => {\n    setEditingAccount(account);\n    setIsAddingAccount(false);\n    setFormData({\n      name: account.name,\n      accountNumber: account.accountNumber,\n      bankName: account.bankName,\n      currency: account.currency,\n      currentBalance: account.currentBalance.toFixed(2)\n    });\n    setErrors({});\n  }, []);\n  const handleSubmit = useCallback(e => {\n    e.preventDefault();\n    const newErrors = validateForm(formData);\n    if (Object.keys(newErrors).length > 0) {\n      setErrors(newErrors);\n      return;\n    }\n    const accountData = {\n      name: formData.name.trim(),\n      accountNumber: formData.accountNumber.trim(),\n      bankName: formData.bankName.trim(),\n      currency: formData.currency.trim(),\n      currentBalance: parseFloat(formData.currentBalance)\n    };\n    try {\n      if (editingAccount) {\n        bankAccountService.updateAccount(editingAccount.id, accountData);\n      } else {\n        // For new accounts, add the required fields\n        const newAccountData = {\n          ...accountData,\n          accountType: 'checking',\n          status: 'active',\n          lastUpdated: new Date().toISOString()\n        };\n        bankAccountService.addAccount(newAccountData);\n      }\n      refreshAccounts();\n      resetForm();\n    } catch (error) {\n      console.error('Error saving account:', error);\n      setErrors({\n        submit: 'Failed to save account. Please try again.'\n      });\n    }\n  }, [formData, validateForm, editingAccount, refreshAccounts, resetForm]);\n  const handleDeleteAccount = useCallback(accountId => {\n    if (window.confirm('Are you sure you want to delete this account? This action cannot be undone.')) {\n      bankAccountService.deleteAccount(accountId);\n      refreshAccounts();\n    }\n  }, [refreshAccounts]);\n\n  // Balance adjustment methods\n  const handleAdjustBalance = useCallback(account => {\n    setAdjustingBalanceAccount(account);\n    setBalanceFormData({\n      newBalance: account.currentBalance.toFixed(2),\n      effectiveDate: new Date().toISOString().split('T')[0],\n      reason: ''\n    });\n    setBalanceErrors({});\n  }, []);\n  const validateBalanceForm = useCallback(data => {\n    const newErrors = {};\n    const balance = parseFloat(data.newBalance);\n    if (isNaN(balance)) {\n      newErrors.newBalance = 'Balance must be a valid number';\n    }\n    if (!data.effectiveDate) {\n      newErrors.effectiveDate = 'Effective date is required';\n    }\n    if (!data.reason.trim()) {\n      newErrors.reason = 'Reason for adjustment is required';\n    }\n    return newErrors;\n  }, []);\n  const handleBalanceSubmit = useCallback(e => {\n    e.preventDefault();\n    if (!adjustingBalanceAccount) return;\n    const newErrors = validateBalanceForm(balanceFormData);\n    if (Object.keys(newErrors).length > 0) {\n      setBalanceErrors(newErrors);\n      return;\n    }\n    try {\n      const newBalance = parseFloat(balanceFormData.newBalance);\n      const updatedAccount = balanceManagementService.updateAccountBalance(adjustingBalanceAccount, newBalance, balanceFormData.effectiveDate, balanceFormData.reason);\n\n      // Update the account in the service\n      bankAccountService.updateAccount(updatedAccount.id, {\n        name: updatedAccount.name,\n        accountNumber: updatedAccount.accountNumber,\n        bankName: updatedAccount.bankName,\n        currency: updatedAccount.currency,\n        currentBalance: updatedAccount.currentBalance\n      });\n      refreshAccounts();\n      setAdjustingBalanceAccount(null);\n      setBalanceFormData({\n        newBalance: '',\n        effectiveDate: new Date().toISOString().split('T')[0],\n        reason: ''\n      });\n    } catch (error) {\n      console.error('Error adjusting balance:', error);\n      setBalanceErrors({\n        submit: 'Failed to adjust balance. Please try again.'\n      });\n    }\n  }, [adjustingBalanceAccount, balanceFormData, validateBalanceForm, refreshAccounts]);\n  const toggleBalanceHistory = useCallback(accountId => {\n    setShowBalanceHistory(prev => ({\n      ...prev,\n      [accountId]: !prev[accountId]\n    }));\n  }, []);\n  const formatCurrency = amount => {\n    return new Intl.NumberFormat('en-US', {\n      style: 'currency',\n      currency: 'USD'\n    }).format(amount);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"bank-account-manager\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"manager-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        children: \"Bank Account Management\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 260,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"Add, edit, and manage your bank accounts\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 261,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 259,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"accounts-section\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"section-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: [\"Your Bank Accounts (\", accounts.length, \")\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 266,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"button\",\n          onClick: handleAddAccount,\n          className: \"btn btn-primary\",\n          children: \"Add New Account\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 267,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 265,\n        columnNumber: 9\n      }, this), accounts.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"empty-state\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"empty-icon\",\n          children: /*#__PURE__*/_jsxDEV(\"svg\", {\n            width: \"48\",\n            height: \"48\",\n            viewBox: \"0 0 24 24\",\n            fill: \"none\",\n            stroke: \"currentColor\",\n            strokeWidth: \"1\",\n            children: [/*#__PURE__*/_jsxDEV(\"rect\", {\n              x: \"2\",\n              y: \"3\",\n              width: \"20\",\n              height: \"14\",\n              rx: \"2\",\n              ry: \"2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 280,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"line\", {\n              x1: \"2\",\n              y1: \"12\",\n              x2: \"22\",\n              y2: \"12\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 281,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 279,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 278,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n          children: \"No Bank Accounts\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 284,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Get started by adding your first bank account\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 285,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 277,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"accounts-grid\",\n        children: accounts.map(account => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"account-card\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"account-header\",\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              children: account.name\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 292,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"account-actions\",\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                type: \"button\",\n                onClick: () => handleAdjustBalance(account),\n                className: \"btn-icon\",\n                title: \"Adjust balance\",\n                children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                  width: \"16\",\n                  height: \"16\",\n                  viewBox: \"0 0 24 24\",\n                  fill: \"none\",\n                  stroke: \"currentColor\",\n                  strokeWidth: \"2\",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    d: \"M12 2v20M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 301,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 300,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 294,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                type: \"button\",\n                onClick: () => toggleBalanceHistory(account.id),\n                className: \"btn-icon\",\n                title: \"View balance history\",\n                children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                  width: \"16\",\n                  height: \"16\",\n                  viewBox: \"0 0 24 24\",\n                  fill: \"none\",\n                  stroke: \"currentColor\",\n                  strokeWidth: \"2\",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    d: \"M3 3v5h5M3 8l4-4 4 4 8-8\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 311,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 310,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 304,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                type: \"button\",\n                onClick: () => handleEditAccount(account),\n                className: \"btn-icon\",\n                title: \"Edit account\",\n                children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                  width: \"16\",\n                  height: \"16\",\n                  viewBox: \"0 0 24 24\",\n                  fill: \"none\",\n                  stroke: \"currentColor\",\n                  strokeWidth: \"2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                    d: \"M12 20h9\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 321,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                    d: \"M16.5 3.5a2.121 2.121 0 0 1 3 3L7 19l-4 1 1-4L16.5 3.5z\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 322,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 320,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 314,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                type: \"button\",\n                onClick: () => handleDeleteAccount(account.id),\n                className: \"btn-icon btn-danger\",\n                title: \"Delete account\",\n                children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                  width: \"16\",\n                  height: \"16\",\n                  viewBox: \"0 0 24 24\",\n                  fill: \"none\",\n                  stroke: \"currentColor\",\n                  strokeWidth: \"2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"polyline\", {\n                    points: \"3,6 5,6 21,6\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 332,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                    d: \"M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 333,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 331,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 325,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 293,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 291,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"account-details\",\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Bank:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 339,\n                columnNumber: 22\n              }, this), \" \", account.bankName]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 339,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Account Number:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 340,\n                columnNumber: 22\n              }, this), \" \", account.accountNumber]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 340,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Currency:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 341,\n                columnNumber: 22\n              }, this), \" \", account.currency]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 341,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Current Balance:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 342,\n                columnNumber: 22\n              }, this), \" \", /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"balance\",\n                children: formatCurrency(account.currentBalance)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 342,\n                columnNumber: 56\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 342,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 338,\n            columnNumber: 17\n          }, this)]\n        }, account.id, true, {\n          fileName: _jsxFileName,\n          lineNumber: 290,\n          columnNumber: 15\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 288,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 264,\n      columnNumber: 7\n    }, this), (isAddingAccount || editingAccount) && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"account-form-section\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-header\",\n        children: /*#__PURE__*/_jsxDEV(\"h3\", {\n          children: editingAccount ? 'Edit Account' : 'Add New Account'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 353,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 352,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n        onSubmit: handleSubmit,\n        className: \"account-form\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-row\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"account-name\",\n              className: \"form-label\",\n              children: \"Account Name *\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 359,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              id: \"account-name\",\n              type: \"text\",\n              value: formData.name,\n              onChange: e => handleInputChange('name', e.target.value),\n              className: `form-input ${errors.name ? 'error' : ''}`,\n              placeholder: \"e.g., Main Operating Account\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 362,\n              columnNumber: 17\n            }, this), errors.name && /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"form-error\",\n              children: errors.name\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 370,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 358,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"account-number\",\n              className: \"form-label\",\n              children: \"Account Number *\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 374,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              id: \"account-number\",\n              type: \"text\",\n              value: formData.accountNumber,\n              onChange: e => handleInputChange('accountNumber', e.target.value),\n              className: `form-input ${errors.accountNumber ? 'error' : ''}`,\n              placeholder: \"e.g., **********\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 377,\n              columnNumber: 17\n            }, this), errors.accountNumber && /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"form-error\",\n              children: errors.accountNumber\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 385,\n              columnNumber: 42\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 373,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 357,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-row\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"bank-name\",\n              className: \"form-label\",\n              children: \"Bank Name *\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 391,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              id: \"bank-name\",\n              type: \"text\",\n              value: formData.bankName,\n              onChange: e => handleInputChange('bankName', e.target.value),\n              className: `form-input ${errors.bankName ? 'error' : ''}`,\n              placeholder: \"e.g., First National Bank\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 394,\n              columnNumber: 17\n            }, this), errors.bankName && /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"form-error\",\n              children: errors.bankName\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 402,\n              columnNumber: 37\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 390,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"currency\",\n              className: \"form-label\",\n              children: \"Currency *\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 406,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n              id: \"currency\",\n              value: formData.currency,\n              onChange: e => handleInputChange('currency', e.target.value),\n              className: `form-select ${errors.currency ? 'error' : ''}`,\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"USD\",\n                children: \"USD - US Dollar\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 415,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"EUR\",\n                children: \"EUR - Euro\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 416,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"GBP\",\n                children: \"GBP - British Pound\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 417,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"CAD\",\n                children: \"CAD - Canadian Dollar\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 418,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"AUD\",\n                children: \"AUD - Australian Dollar\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 419,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 409,\n              columnNumber: 17\n            }, this), errors.currency && /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"form-error\",\n              children: errors.currency\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 421,\n              columnNumber: 37\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 405,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 389,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-row\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"current-balance\",\n              className: \"form-label\",\n              children: \"Current Balance *\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 427,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              id: \"current-balance\",\n              type: \"number\",\n              step: \"0.01\",\n              value: formData.currentBalance,\n              onChange: e => handleInputChange('currentBalance', e.target.value),\n              className: `form-input ${errors.currentBalance ? 'error' : ''}`,\n              placeholder: \"0.00\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 430,\n              columnNumber: 17\n            }, this), errors.currentBalance && /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"form-error\",\n              children: errors.currentBalance\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 439,\n              columnNumber: 43\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 426,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 425,\n          columnNumber: 13\n        }, this), errors.submit && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-error-message\",\n          children: errors.submit\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 444,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-actions\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"button\",\n            onClick: resetForm,\n            className: \"btn btn-secondary\",\n            children: \"Cancel\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 450,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"submit\",\n            className: \"btn btn-primary\",\n            children: editingAccount ? 'Update Account' : 'Add Account'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 457,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 449,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 356,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 351,\n      columnNumber: 9\n    }, this), adjustingBalanceAccount && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"balance-adjustment-section\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: [\"Adjust Balance - \", adjustingBalanceAccount.name]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 472,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Update the account balance with an effective date and reason\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 473,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 471,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n        onSubmit: handleBalanceSubmit,\n        className: \"balance-form\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-row\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"current-balance-display\",\n              className: \"form-label\",\n              children: \"Current Balance\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 479,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              id: \"current-balance-display\",\n              type: \"text\",\n              value: formatCurrency(adjustingBalanceAccount.currentBalance),\n              disabled: true,\n              className: \"form-input disabled\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 482,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 478,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"new-balance\",\n              className: \"form-label\",\n              children: \"New Balance *\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 492,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              id: \"new-balance\",\n              type: \"number\",\n              step: \"0.01\",\n              value: balanceFormData.newBalance,\n              onChange: e => setBalanceFormData(prev => ({\n                ...prev,\n                newBalance: e.target.value\n              })),\n              className: `form-input ${balanceErrors.newBalance ? 'error' : ''}`,\n              placeholder: \"0.00\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 495,\n              columnNumber: 17\n            }, this), balanceErrors.newBalance && /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"form-error\",\n              children: balanceErrors.newBalance\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 504,\n              columnNumber: 46\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 491,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 477,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-row\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"effective-date\",\n              className: \"form-label\",\n              children: \"Effective Date *\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 510,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              id: \"effective-date\",\n              type: \"date\",\n              value: balanceFormData.effectiveDate,\n              onChange: e => setBalanceFormData(prev => ({\n                ...prev,\n                effectiveDate: e.target.value\n              })),\n              className: `form-input ${balanceErrors.effectiveDate ? 'error' : ''}`\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 513,\n              columnNumber: 17\n            }, this), balanceErrors.effectiveDate && /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"form-error\",\n              children: balanceErrors.effectiveDate\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 520,\n              columnNumber: 49\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 509,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"adjustment-reason\",\n              className: \"form-label\",\n              children: \"Reason for Adjustment *\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 524,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n              id: \"adjustment-reason\",\n              value: balanceFormData.reason,\n              onChange: e => setBalanceFormData(prev => ({\n                ...prev,\n                reason: e.target.value\n              })),\n              className: `form-select ${balanceErrors.reason ? 'error' : ''}`,\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"\",\n                children: \"Select a reason...\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 533,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"Bank reconciliation\",\n                children: \"Bank reconciliation\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 534,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"Manual correction\",\n                children: \"Manual correction\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 535,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"Interest adjustment\",\n                children: \"Interest adjustment\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 536,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"Fee adjustment\",\n                children: \"Fee adjustment\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 537,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"Opening balance setup\",\n                children: \"Opening balance setup\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 538,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"Other\",\n                children: \"Other\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 539,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 527,\n              columnNumber: 17\n            }, this), balanceErrors.reason && /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"form-error\",\n              children: balanceErrors.reason\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 541,\n              columnNumber: 42\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 523,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 508,\n          columnNumber: 13\n        }, this), balanceFormData.reason === 'Other' && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-row\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"custom-reason\",\n              className: \"form-label\",\n              children: \"Custom Reason *\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 548,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              id: \"custom-reason\",\n              type: \"text\",\n              placeholder: \"Enter custom reason...\",\n              className: \"form-input\",\n              onChange: e => setBalanceFormData(prev => ({\n                ...prev,\n                reason: e.target.value\n              }))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 551,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 547,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 546,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"adjustment-preview\",\n          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n            children: \"Adjustment Preview\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 563,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"preview-details\",\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Current Balance:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 565,\n                columnNumber: 20\n              }, this), \" \", formatCurrency(adjustingBalanceAccount.currentBalance)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 565,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"New Balance:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 566,\n                columnNumber: 20\n              }, this), \" \", balanceFormData.newBalance ? formatCurrency(parseFloat(balanceFormData.newBalance)) : '$0.00']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 566,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Adjustment Amount:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 567,\n                columnNumber: 20\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: `adjustment-amount ${balanceFormData.newBalance && parseFloat(balanceFormData.newBalance) - adjustingBalanceAccount.currentBalance >= 0 ? 'positive' : 'negative'}`,\n                children: balanceFormData.newBalance ? (parseFloat(balanceFormData.newBalance) - adjustingBalanceAccount.currentBalance >= 0 ? '+' : '') + formatCurrency(parseFloat(balanceFormData.newBalance) - adjustingBalanceAccount.currentBalance) : '$0.00'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 568,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 567,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 564,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 562,\n          columnNumber: 13\n        }, this), balanceErrors.submit && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-error-message\",\n          children: balanceErrors.submit\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 580,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-actions\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"button\",\n            onClick: () => setAdjustingBalanceAccount(null),\n            className: \"btn btn-secondary\",\n            children: \"Cancel\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 586,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"submit\",\n            className: \"btn btn-primary\",\n            children: \"Apply Adjustment\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 593,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 585,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 476,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 470,\n      columnNumber: 9\n    }, this), accounts.map(account => showBalanceHistory[account.id] && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"balance-history-section\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: [\"Balance History - \", account.name]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 609,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"button\",\n          onClick: () => toggleBalanceHistory(account.id),\n          className: \"btn btn-secondary\",\n          children: \"Close\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 610,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 608,\n        columnNumber: 13\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"balance-history\",\n        children: (() => {\n          const history = balanceManagementService.getBalanceHistory(account.id);\n          const adjustments = balanceManagementService.getBalanceAdjustments(account.id);\n          if (history.length === 0 && adjustments.length === 0) {\n            return /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"empty-history\",\n              children: /*#__PURE__*/_jsxDEV(\"p\", {\n                children: \"No balance history available for this account.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 627,\n                columnNumber: 23\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 626,\n              columnNumber: 21\n            }, this);\n          }\n          return /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"history-content\",\n            children: adjustments.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"adjustments-section\",\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                children: \"Balance Adjustments\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 636,\n                columnNumber: 25\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"adjustments-list\",\n                children: adjustments.map(adjustment => /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"adjustment-item\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"adjustment-header\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"adjustment-date\",\n                      children: adjustment.date\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 641,\n                      columnNumber: 33\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: `adjustment-amount ${adjustment.adjustmentAmount >= 0 ? 'positive' : 'negative'}`,\n                      children: [adjustment.adjustmentAmount >= 0 ? '+' : '', formatCurrency(adjustment.adjustmentAmount)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 642,\n                      columnNumber: 33\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 640,\n                    columnNumber: 31\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"adjustment-details\",\n                    children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                        children: \"Reason:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 647,\n                        columnNumber: 36\n                      }, this), \" \", adjustment.reason]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 647,\n                      columnNumber: 33\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                        children: \"Previous Balance:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 648,\n                        columnNumber: 36\n                      }, this), \" \", formatCurrency(adjustment.previousBalance)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 648,\n                      columnNumber: 33\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                        children: \"New Balance:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 649,\n                        columnNumber: 36\n                      }, this), \" \", formatCurrency(adjustment.newBalance)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 649,\n                      columnNumber: 33\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                        children: \"Type:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 650,\n                        columnNumber: 36\n                      }, this), \" \", adjustment.type]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 650,\n                      columnNumber: 33\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 646,\n                    columnNumber: 31\n                  }, this)]\n                }, adjustment.id, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 639,\n                  columnNumber: 29\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 637,\n                columnNumber: 25\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 635,\n              columnNumber: 23\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 633,\n            columnNumber: 19\n          }, this);\n        })()\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 619,\n        columnNumber: 13\n      }, this)]\n    }, `history-${account.id}`, true, {\n      fileName: _jsxFileName,\n      lineNumber: 607,\n      columnNumber: 11\n    }, this))]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 258,\n    columnNumber: 5\n  }, this);\n};\n_s(BankAccountManager, \"CNyKFFGVyXp8i0fAUluPTv0eDYI=\");\n_c = BankAccountManager;\nvar _c;\n$RefreshReg$(_c, \"BankAccountManager\");", "map": {"version": 3, "names": ["React", "useState", "useCallback", "bankAccountService", "balanceManagementService", "jsxDEV", "_jsxDEV", "BankAccountManager", "onAccountsUpdated", "_s", "accounts", "setAccounts", "getAllAccounts", "isAddingAccount", "setIsAddingAccount", "editingAccount", "setEditingAccount", "adjustingBalanceAccount", "setAdjustingBalanceAccount", "formData", "setFormData", "name", "accountNumber", "bankName", "currency", "currentBalance", "balanceFormData", "setBalanceFormData", "newBalance", "effectiveDate", "Date", "toISOString", "split", "reason", "errors", "setErrors", "balanceErrors", "setBalanceErrors", "showBalanceHistory", "setShowBalanceHistory", "refreshAccounts", "resetForm", "validateForm", "data", "newErrors", "trim", "some", "acc", "id", "balance", "parseFloat", "isNaN", "handleInputChange", "field", "value", "prev", "handleAddAccount", "handleEditAccount", "account", "toFixed", "handleSubmit", "e", "preventDefault", "Object", "keys", "length", "accountData", "updateAccount", "newAccountData", "accountType", "status", "lastUpdated", "addAccount", "error", "console", "submit", "handleDeleteAccount", "accountId", "window", "confirm", "deleteAccount", "handleAdjustBalance", "validateBalanceForm", "handleBalanceSubmit", "updatedAccount", "updateAccountBalance", "toggleBalanceHistory", "formatCurrency", "amount", "Intl", "NumberFormat", "style", "format", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "type", "onClick", "width", "height", "viewBox", "fill", "stroke", "strokeWidth", "x", "y", "rx", "ry", "x1", "y1", "x2", "y2", "map", "title", "d", "points", "onSubmit", "htmlFor", "onChange", "target", "placeholder", "step", "disabled", "history", "getBalanceHistory", "adjustments", "getBalanceAdjustments", "adjustment", "date", "adjustmentAmount", "previousBalance", "_c", "$RefreshReg$"], "sources": ["C:/tmsft/src/components/BankAccountManager.tsx"], "sourcesContent": ["import React, { useState, useCallback } from 'react';\r\nimport { BankAccount } from '../types';\r\nimport { bankAccountService } from '../services/bankAccountService';\r\nimport { balanceManagementService } from '../services/balanceManagementService';\r\nimport './BankAccountManager.css';\r\n\r\ninterface BankAccountManagerProps {\r\n  onAccountsUpdated?: () => void;\r\n}\r\n\r\ninterface AccountFormData {\r\n  name: string;\r\n  accountNumber: string;\r\n  bankName: string;\r\n  currency: string;\r\n  currentBalance: string;\r\n}\r\n\r\ninterface BalanceAdjustmentFormData {\r\n  newBalance: string;\r\n  effectiveDate: string;\r\n  reason: string;\r\n}\r\n\r\nexport const BankAccountManager: React.FC<BankAccountManagerProps> = ({ onAccountsUpdated }) => {\r\n  const [accounts, setAccounts] = useState<BankAccount[]>(bankAccountService.getAllAccounts());\r\n  const [isAddingAccount, setIsAddingAccount] = useState(false);\r\n  const [editingAccount, setEditingAccount] = useState<BankAccount | null>(null);\r\n  const [adjustingBalanceAccount, setAdjustingBalanceAccount] = useState<BankAccount | null>(null);\r\n  const [formData, setFormData] = useState<AccountFormData>({\r\n    name: '',\r\n    accountNumber: '',\r\n    bankName: '',\r\n    currency: 'USD',\r\n    currentBalance: '0.00'\r\n  });\r\n  const [balanceFormData, setBalanceFormData] = useState<BalanceAdjustmentFormData>({\r\n    newBalance: '',\r\n    effectiveDate: new Date().toISOString().split('T')[0],\r\n    reason: ''\r\n  });\r\n  const [errors, setErrors] = useState<Record<string, string>>({});\r\n  const [balanceErrors, setBalanceErrors] = useState<Record<string, string>>({});\r\n  const [showBalanceHistory, setShowBalanceHistory] = useState<Record<string, boolean>>({});\r\n\r\n  const refreshAccounts = useCallback(() => {\r\n    setAccounts(bankAccountService.getAllAccounts());\r\n    if (onAccountsUpdated) {\r\n      onAccountsUpdated();\r\n    }\r\n  }, [onAccountsUpdated]);\r\n\r\n  const resetForm = useCallback(() => {\r\n    setFormData({\r\n      name: '',\r\n      accountNumber: '',\r\n      bankName: '',\r\n      currency: 'USD',\r\n      currentBalance: '0.00'\r\n    });\r\n    setErrors({});\r\n    setIsAddingAccount(false);\r\n    setEditingAccount(null);\r\n  }, []);\r\n\r\n  const validateForm = useCallback((data: AccountFormData): Record<string, string> => {\r\n    const newErrors: Record<string, string> = {};\r\n\r\n    if (!data.name.trim()) {\r\n      newErrors.name = 'Account name is required';\r\n    }\r\n\r\n    if (!data.accountNumber.trim()) {\r\n      newErrors.accountNumber = 'Account number is required';\r\n    } else if (accounts.some(acc => \r\n      acc.accountNumber === data.accountNumber.trim() && \r\n      (!editingAccount || acc.id !== editingAccount.id)\r\n    )) {\r\n      newErrors.accountNumber = 'Account number already exists';\r\n    }\r\n\r\n    if (!data.bankName.trim()) {\r\n      newErrors.bankName = 'Bank name is required';\r\n    }\r\n\r\n    if (!data.currency.trim()) {\r\n      newErrors.currency = 'Currency is required';\r\n    }\r\n\r\n    const balance = parseFloat(data.currentBalance);\r\n    if (isNaN(balance)) {\r\n      newErrors.currentBalance = 'Current balance must be a valid number';\r\n    }\r\n\r\n    return newErrors;\r\n  }, [accounts, editingAccount]);\r\n\r\n  const handleInputChange = useCallback((field: keyof AccountFormData, value: string) => {\r\n    setFormData(prev => ({ ...prev, [field]: value }));\r\n    // Clear error for this field when user starts typing\r\n    if (errors[field]) {\r\n      setErrors(prev => ({ ...prev, [field]: '' }));\r\n    }\r\n  }, [errors]);\r\n\r\n  const handleAddAccount = useCallback(() => {\r\n    setIsAddingAccount(true);\r\n    setEditingAccount(null);\r\n    resetForm();\r\n  }, [resetForm]);\r\n\r\n  const handleEditAccount = useCallback((account: BankAccount) => {\r\n    setEditingAccount(account);\r\n    setIsAddingAccount(false);\r\n    setFormData({\r\n      name: account.name,\r\n      accountNumber: account.accountNumber,\r\n      bankName: account.bankName,\r\n      currency: account.currency,\r\n      currentBalance: account.currentBalance.toFixed(2)\r\n    });\r\n    setErrors({});\r\n  }, []);\r\n\r\n  const handleSubmit = useCallback((e: React.FormEvent) => {\r\n    e.preventDefault();\r\n    \r\n    const newErrors = validateForm(formData);\r\n    if (Object.keys(newErrors).length > 0) {\r\n      setErrors(newErrors);\r\n      return;\r\n    }\r\n\r\n    const accountData = {\r\n      name: formData.name.trim(),\r\n      accountNumber: formData.accountNumber.trim(),\r\n      bankName: formData.bankName.trim(),\r\n      currency: formData.currency.trim() as 'SAR' | 'USD' | 'AED',\r\n      currentBalance: parseFloat(formData.currentBalance)\r\n    };\r\n\r\n    try {\r\n      if (editingAccount) {\r\n        bankAccountService.updateAccount(editingAccount.id, accountData);\r\n      } else {\r\n        // For new accounts, add the required fields\r\n        const newAccountData = {\r\n          ...accountData,\r\n          accountType: 'checking' as const,\r\n          status: 'active' as const,\r\n          lastUpdated: new Date().toISOString()\r\n        };\r\n        bankAccountService.addAccount(newAccountData);\r\n      }\r\n      \r\n      refreshAccounts();\r\n      resetForm();\r\n    } catch (error) {\r\n      console.error('Error saving account:', error);\r\n      setErrors({ submit: 'Failed to save account. Please try again.' });\r\n    }\r\n  }, [formData, validateForm, editingAccount, refreshAccounts, resetForm]);\r\n\r\n  const handleDeleteAccount = useCallback((accountId: string) => {\r\n    if (window.confirm('Are you sure you want to delete this account? This action cannot be undone.')) {\r\n      bankAccountService.deleteAccount(accountId);\r\n      refreshAccounts();\r\n    }\r\n  }, [refreshAccounts]);\r\n\r\n  // Balance adjustment methods\r\n  const handleAdjustBalance = useCallback((account: BankAccount) => {\r\n    setAdjustingBalanceAccount(account);\r\n    setBalanceFormData({\r\n      newBalance: account.currentBalance.toFixed(2),\r\n      effectiveDate: new Date().toISOString().split('T')[0],\r\n      reason: ''\r\n    });\r\n    setBalanceErrors({});\r\n  }, []);\r\n\r\n  const validateBalanceForm = useCallback((data: BalanceAdjustmentFormData): Record<string, string> => {\r\n    const newErrors: Record<string, string> = {};\r\n\r\n    const balance = parseFloat(data.newBalance);\r\n    if (isNaN(balance)) {\r\n      newErrors.newBalance = 'Balance must be a valid number';\r\n    }\r\n\r\n    if (!data.effectiveDate) {\r\n      newErrors.effectiveDate = 'Effective date is required';\r\n    }\r\n\r\n    if (!data.reason.trim()) {\r\n      newErrors.reason = 'Reason for adjustment is required';\r\n    }\r\n\r\n    return newErrors;\r\n  }, []);\r\n\r\n  const handleBalanceSubmit = useCallback((e: React.FormEvent) => {\r\n    e.preventDefault();\r\n    \r\n    if (!adjustingBalanceAccount) return;\r\n\r\n    const newErrors = validateBalanceForm(balanceFormData);\r\n    if (Object.keys(newErrors).length > 0) {\r\n      setBalanceErrors(newErrors);\r\n      return;\r\n    }\r\n\r\n    try {\r\n      const newBalance = parseFloat(balanceFormData.newBalance);\r\n      const updatedAccount = balanceManagementService.updateAccountBalance(\r\n        adjustingBalanceAccount,\r\n        newBalance,\r\n        balanceFormData.effectiveDate,\r\n        balanceFormData.reason\r\n      );\r\n\r\n      // Update the account in the service\r\n      bankAccountService.updateAccount(updatedAccount.id, {\r\n        name: updatedAccount.name,\r\n        accountNumber: updatedAccount.accountNumber,\r\n        bankName: updatedAccount.bankName,\r\n        currency: updatedAccount.currency,\r\n        currentBalance: updatedAccount.currentBalance\r\n      });\r\n\r\n      refreshAccounts();\r\n      setAdjustingBalanceAccount(null);\r\n      setBalanceFormData({\r\n        newBalance: '',\r\n        effectiveDate: new Date().toISOString().split('T')[0],\r\n        reason: ''\r\n      });\r\n    } catch (error) {\r\n      console.error('Error adjusting balance:', error);\r\n      setBalanceErrors({ submit: 'Failed to adjust balance. Please try again.' });\r\n    }\r\n  }, [adjustingBalanceAccount, balanceFormData, validateBalanceForm, refreshAccounts]);\r\n\r\n  const toggleBalanceHistory = useCallback((accountId: string) => {\r\n    setShowBalanceHistory(prev => ({\r\n      ...prev,\r\n      [accountId]: !prev[accountId]\r\n    }));\r\n  }, []);\r\n\r\n  const formatCurrency = (amount: number): string => {\r\n    return new Intl.NumberFormat('en-US', {\r\n      style: 'currency',\r\n      currency: 'USD'\r\n    }).format(amount);\r\n  };\r\n\r\n  return (\r\n    <div className=\"bank-account-manager\">\r\n      <div className=\"manager-header\">\r\n        <h2>Bank Account Management</h2>\r\n        <p>Add, edit, and manage your bank accounts</p>\r\n      </div>\r\n\r\n      <div className=\"accounts-section\">\r\n        <div className=\"section-header\">\r\n          <h3>Your Bank Accounts ({accounts.length})</h3>\r\n          <button\r\n            type=\"button\"\r\n            onClick={handleAddAccount}\r\n            className=\"btn btn-primary\"\r\n          >\r\n            Add New Account\r\n          </button>\r\n        </div>\r\n\r\n        {accounts.length === 0 ? (\r\n          <div className=\"empty-state\">\r\n            <div className=\"empty-icon\">\r\n              <svg width=\"48\" height=\"48\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"1\">\r\n                <rect x=\"2\" y=\"3\" width=\"20\" height=\"14\" rx=\"2\" ry=\"2\" />\r\n                <line x1=\"2\" y1=\"12\" x2=\"22\" y2=\"12\" />\r\n              </svg>\r\n            </div>\r\n            <h3>No Bank Accounts</h3>\r\n            <p>Get started by adding your first bank account</p>\r\n          </div>\r\n        ) : (\r\n          <div className=\"accounts-grid\">\r\n            {accounts.map((account) => (\r\n              <div key={account.id} className=\"account-card\">\r\n                <div className=\"account-header\">\r\n                  <h4>{account.name}</h4>\r\n                  <div className=\"account-actions\">\r\n                    <button\r\n                      type=\"button\"\r\n                      onClick={() => handleAdjustBalance(account)}\r\n                      className=\"btn-icon\"\r\n                      title=\"Adjust balance\"\r\n                    >\r\n                      <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\">\r\n                        <path d=\"M12 2v20M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6\" />\r\n                      </svg>\r\n                    </button>\r\n                    <button\r\n                      type=\"button\"\r\n                      onClick={() => toggleBalanceHistory(account.id)}\r\n                      className=\"btn-icon\"\r\n                      title=\"View balance history\"\r\n                    >\r\n                      <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\">\r\n                        <path d=\"M3 3v5h5M3 8l4-4 4 4 8-8\" />\r\n                      </svg>\r\n                    </button>\r\n                    <button\r\n                      type=\"button\"\r\n                      onClick={() => handleEditAccount(account)}\r\n                      className=\"btn-icon\"\r\n                      title=\"Edit account\"\r\n                    >\r\n                      <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\">\r\n                        <path d=\"M12 20h9\" />\r\n                        <path d=\"M16.5 3.5a2.121 2.121 0 0 1 3 3L7 19l-4 1 1-4L16.5 3.5z\" />\r\n                      </svg>\r\n                    </button>\r\n                    <button\r\n                      type=\"button\"\r\n                      onClick={() => handleDeleteAccount(account.id)}\r\n                      className=\"btn-icon btn-danger\"\r\n                      title=\"Delete account\"\r\n                    >\r\n                      <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\">\r\n                        <polyline points=\"3,6 5,6 21,6\" />\r\n                        <path d=\"M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2\" />\r\n                      </svg>\r\n                    </button>\r\n                  </div>\r\n                </div>\r\n                <div className=\"account-details\">\r\n                  <p><strong>Bank:</strong> {account.bankName}</p>\r\n                  <p><strong>Account Number:</strong> {account.accountNumber}</p>\r\n                  <p><strong>Currency:</strong> {account.currency}</p>\r\n                  <p><strong>Current Balance:</strong> <span className=\"balance\">{formatCurrency(account.currentBalance)}</span></p>\r\n                </div>\r\n              </div>\r\n            ))}\r\n          </div>\r\n        )}\r\n      </div>\r\n\r\n      {(isAddingAccount || editingAccount) && (\r\n        <div className=\"account-form-section\">\r\n          <div className=\"form-header\">\r\n            <h3>{editingAccount ? 'Edit Account' : 'Add New Account'}</h3>\r\n          </div>\r\n          \r\n          <form onSubmit={handleSubmit} className=\"account-form\">\r\n            <div className=\"form-row\">\r\n              <div className=\"form-group\">\r\n                <label htmlFor=\"account-name\" className=\"form-label\">\r\n                  Account Name *\r\n                </label>\r\n                <input\r\n                  id=\"account-name\"\r\n                  type=\"text\"\r\n                  value={formData.name}\r\n                  onChange={(e) => handleInputChange('name', e.target.value)}\r\n                  className={`form-input ${errors.name ? 'error' : ''}`}\r\n                  placeholder=\"e.g., Main Operating Account\"\r\n                />\r\n                {errors.name && <span className=\"form-error\">{errors.name}</span>}\r\n              </div>\r\n\r\n              <div className=\"form-group\">\r\n                <label htmlFor=\"account-number\" className=\"form-label\">\r\n                  Account Number *\r\n                </label>\r\n                <input\r\n                  id=\"account-number\"\r\n                  type=\"text\"\r\n                  value={formData.accountNumber}\r\n                  onChange={(e) => handleInputChange('accountNumber', e.target.value)}\r\n                  className={`form-input ${errors.accountNumber ? 'error' : ''}`}\r\n                  placeholder=\"e.g., **********\"\r\n                />\r\n                {errors.accountNumber && <span className=\"form-error\">{errors.accountNumber}</span>}\r\n              </div>\r\n            </div>\r\n\r\n            <div className=\"form-row\">\r\n              <div className=\"form-group\">\r\n                <label htmlFor=\"bank-name\" className=\"form-label\">\r\n                  Bank Name *\r\n                </label>\r\n                <input\r\n                  id=\"bank-name\"\r\n                  type=\"text\"\r\n                  value={formData.bankName}\r\n                  onChange={(e) => handleInputChange('bankName', e.target.value)}\r\n                  className={`form-input ${errors.bankName ? 'error' : ''}`}\r\n                  placeholder=\"e.g., First National Bank\"\r\n                />\r\n                {errors.bankName && <span className=\"form-error\">{errors.bankName}</span>}\r\n              </div>\r\n\r\n              <div className=\"form-group\">\r\n                <label htmlFor=\"currency\" className=\"form-label\">\r\n                  Currency *\r\n                </label>\r\n                <select\r\n                  id=\"currency\"\r\n                  value={formData.currency}\r\n                  onChange={(e) => handleInputChange('currency', e.target.value)}\r\n                  className={`form-select ${errors.currency ? 'error' : ''}`}\r\n                >\r\n                  <option value=\"USD\">USD - US Dollar</option>\r\n                  <option value=\"EUR\">EUR - Euro</option>\r\n                  <option value=\"GBP\">GBP - British Pound</option>\r\n                  <option value=\"CAD\">CAD - Canadian Dollar</option>\r\n                  <option value=\"AUD\">AUD - Australian Dollar</option>\r\n                </select>\r\n                {errors.currency && <span className=\"form-error\">{errors.currency}</span>}\r\n              </div>\r\n            </div>\r\n\r\n            <div className=\"form-row\">\r\n              <div className=\"form-group\">\r\n                <label htmlFor=\"current-balance\" className=\"form-label\">\r\n                  Current Balance *\r\n                </label>\r\n                <input\r\n                  id=\"current-balance\"\r\n                  type=\"number\"\r\n                  step=\"0.01\"\r\n                  value={formData.currentBalance}\r\n                  onChange={(e) => handleInputChange('currentBalance', e.target.value)}\r\n                  className={`form-input ${errors.currentBalance ? 'error' : ''}`}\r\n                  placeholder=\"0.00\"\r\n                />\r\n                {errors.currentBalance && <span className=\"form-error\">{errors.currentBalance}</span>}\r\n              </div>\r\n            </div>\r\n\r\n            {errors.submit && (\r\n              <div className=\"form-error-message\">\r\n                {errors.submit}\r\n              </div>\r\n            )}\r\n\r\n            <div className=\"form-actions\">\r\n              <button\r\n                type=\"button\"\r\n                onClick={resetForm}\r\n                className=\"btn btn-secondary\"\r\n              >\r\n                Cancel\r\n              </button>\r\n              <button\r\n                type=\"submit\"\r\n                className=\"btn btn-primary\"\r\n              >\r\n                {editingAccount ? 'Update Account' : 'Add Account'}\r\n              </button>\r\n            </div>\r\n          </form>\r\n        </div>\r\n      )}\r\n\r\n      {/* Balance Adjustment Form */}\r\n      {adjustingBalanceAccount && (\r\n        <div className=\"balance-adjustment-section\">\r\n          <div className=\"form-header\">\r\n            <h3>Adjust Balance - {adjustingBalanceAccount.name}</h3>\r\n            <p>Update the account balance with an effective date and reason</p>\r\n          </div>\r\n          \r\n          <form onSubmit={handleBalanceSubmit} className=\"balance-form\">\r\n            <div className=\"form-row\">\r\n              <div className=\"form-group\">\r\n                <label htmlFor=\"current-balance-display\" className=\"form-label\">\r\n                  Current Balance\r\n                </label>\r\n                <input\r\n                  id=\"current-balance-display\"\r\n                  type=\"text\"\r\n                  value={formatCurrency(adjustingBalanceAccount.currentBalance)}\r\n                  disabled\r\n                  className=\"form-input disabled\"\r\n                />\r\n              </div>\r\n\r\n              <div className=\"form-group\">\r\n                <label htmlFor=\"new-balance\" className=\"form-label\">\r\n                  New Balance *\r\n                </label>\r\n                <input\r\n                  id=\"new-balance\"\r\n                  type=\"number\"\r\n                  step=\"0.01\"\r\n                  value={balanceFormData.newBalance}\r\n                  onChange={(e) => setBalanceFormData(prev => ({ ...prev, newBalance: e.target.value }))}\r\n                  className={`form-input ${balanceErrors.newBalance ? 'error' : ''}`}\r\n                  placeholder=\"0.00\"\r\n                />\r\n                {balanceErrors.newBalance && <span className=\"form-error\">{balanceErrors.newBalance}</span>}\r\n              </div>\r\n            </div>\r\n\r\n            <div className=\"form-row\">\r\n              <div className=\"form-group\">\r\n                <label htmlFor=\"effective-date\" className=\"form-label\">\r\n                  Effective Date *\r\n                </label>\r\n                <input\r\n                  id=\"effective-date\"\r\n                  type=\"date\"\r\n                  value={balanceFormData.effectiveDate}\r\n                  onChange={(e) => setBalanceFormData(prev => ({ ...prev, effectiveDate: e.target.value }))}\r\n                  className={`form-input ${balanceErrors.effectiveDate ? 'error' : ''}`}\r\n                />\r\n                {balanceErrors.effectiveDate && <span className=\"form-error\">{balanceErrors.effectiveDate}</span>}\r\n              </div>\r\n\r\n              <div className=\"form-group\">\r\n                <label htmlFor=\"adjustment-reason\" className=\"form-label\">\r\n                  Reason for Adjustment *\r\n                </label>\r\n                <select\r\n                  id=\"adjustment-reason\"\r\n                  value={balanceFormData.reason}\r\n                  onChange={(e) => setBalanceFormData(prev => ({ ...prev, reason: e.target.value }))}\r\n                  className={`form-select ${balanceErrors.reason ? 'error' : ''}`}\r\n                >\r\n                  <option value=\"\">Select a reason...</option>\r\n                  <option value=\"Bank reconciliation\">Bank reconciliation</option>\r\n                  <option value=\"Manual correction\">Manual correction</option>\r\n                  <option value=\"Interest adjustment\">Interest adjustment</option>\r\n                  <option value=\"Fee adjustment\">Fee adjustment</option>\r\n                  <option value=\"Opening balance setup\">Opening balance setup</option>\r\n                  <option value=\"Other\">Other</option>\r\n                </select>\r\n                {balanceErrors.reason && <span className=\"form-error\">{balanceErrors.reason}</span>}\r\n              </div>\r\n            </div>\r\n\r\n            {balanceFormData.reason === 'Other' && (\r\n              <div className=\"form-row\">\r\n                <div className=\"form-group\">\r\n                  <label htmlFor=\"custom-reason\" className=\"form-label\">\r\n                    Custom Reason *\r\n                  </label>\r\n                  <input\r\n                    id=\"custom-reason\"\r\n                    type=\"text\"\r\n                    placeholder=\"Enter custom reason...\"\r\n                    className=\"form-input\"\r\n                    onChange={(e) => setBalanceFormData(prev => ({ ...prev, reason: e.target.value }))}\r\n                  />\r\n                </div>\r\n              </div>\r\n            )}\r\n\r\n            <div className=\"adjustment-preview\">\r\n              <h4>Adjustment Preview</h4>\r\n              <div className=\"preview-details\">\r\n                <p><strong>Current Balance:</strong> {formatCurrency(adjustingBalanceAccount.currentBalance)}</p>\r\n                <p><strong>New Balance:</strong> {balanceFormData.newBalance ? formatCurrency(parseFloat(balanceFormData.newBalance)) : '$0.00'}</p>\r\n                <p><strong>Adjustment Amount:</strong> \r\n                  <span className={`adjustment-amount ${balanceFormData.newBalance && parseFloat(balanceFormData.newBalance) - adjustingBalanceAccount.currentBalance >= 0 ? 'positive' : 'negative'}`}>\r\n                    {balanceFormData.newBalance ? \r\n                      (parseFloat(balanceFormData.newBalance) - adjustingBalanceAccount.currentBalance >= 0 ? '+' : '') + \r\n                      formatCurrency(parseFloat(balanceFormData.newBalance) - adjustingBalanceAccount.currentBalance) : \r\n                      '$0.00'\r\n                    }\r\n                  </span>\r\n                </p>\r\n              </div>\r\n            </div>\r\n\r\n            {balanceErrors.submit && (\r\n              <div className=\"form-error-message\">\r\n                {balanceErrors.submit}\r\n              </div>\r\n            )}\r\n\r\n            <div className=\"form-actions\">\r\n              <button\r\n                type=\"button\"\r\n                onClick={() => setAdjustingBalanceAccount(null)}\r\n                className=\"btn btn-secondary\"\r\n              >\r\n                Cancel\r\n              </button>\r\n              <button\r\n                type=\"submit\"\r\n                className=\"btn btn-primary\"\r\n              >\r\n                Apply Adjustment\r\n              </button>\r\n            </div>\r\n          </form>\r\n        </div>\r\n      )}\r\n\r\n      {/* Balance History Display */}\r\n      {accounts.map(account => \r\n        showBalanceHistory[account.id] && (\r\n          <div key={`history-${account.id}`} className=\"balance-history-section\">\r\n            <div className=\"form-header\">\r\n              <h3>Balance History - {account.name}</h3>\r\n              <button\r\n                type=\"button\"\r\n                onClick={() => toggleBalanceHistory(account.id)}\r\n                className=\"btn btn-secondary\"\r\n              >\r\n                Close\r\n              </button>\r\n            </div>\r\n            \r\n            <div className=\"balance-history\">\r\n              {(() => {\r\n                const history = balanceManagementService.getBalanceHistory(account.id);\r\n                const adjustments = balanceManagementService.getBalanceAdjustments(account.id);\r\n                \r\n                if (history.length === 0 && adjustments.length === 0) {\r\n                  return (\r\n                    <div className=\"empty-history\">\r\n                      <p>No balance history available for this account.</p>\r\n                    </div>\r\n                  );\r\n                }\r\n\r\n                return (\r\n                  <div className=\"history-content\">\r\n                    {adjustments.length > 0 && (\r\n                      <div className=\"adjustments-section\">\r\n                        <h4>Balance Adjustments</h4>\r\n                        <div className=\"adjustments-list\">\r\n                          {adjustments.map(adjustment => (\r\n                            <div key={adjustment.id} className=\"adjustment-item\">\r\n                              <div className=\"adjustment-header\">\r\n                                <span className=\"adjustment-date\">{adjustment.date}</span>\r\n                                <span className={`adjustment-amount ${adjustment.adjustmentAmount >= 0 ? 'positive' : 'negative'}`}>\r\n                                  {adjustment.adjustmentAmount >= 0 ? '+' : ''}{formatCurrency(adjustment.adjustmentAmount)}\r\n                                </span>\r\n                              </div>\r\n                              <div className=\"adjustment-details\">\r\n                                <p><strong>Reason:</strong> {adjustment.reason}</p>\r\n                                <p><strong>Previous Balance:</strong> {formatCurrency(adjustment.previousBalance)}</p>\r\n                                <p><strong>New Balance:</strong> {formatCurrency(adjustment.newBalance)}</p>\r\n                                <p><strong>Type:</strong> {adjustment.type}</p>\r\n                              </div>\r\n                            </div>\r\n                          ))}\r\n                        </div>\r\n                      </div>\r\n                    )}\r\n                  </div>\r\n                );\r\n              })()}\r\n            </div>\r\n          </div>\r\n        )\r\n      )}\r\n    </div>\r\n  );\r\n}; "], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,WAAW,QAAQ,OAAO;AAEpD,SAASC,kBAAkB,QAAQ,gCAAgC;AACnE,SAASC,wBAAwB,QAAQ,sCAAsC;AAC/E,OAAO,0BAA0B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAoBlC,OAAO,MAAMC,kBAAqD,GAAGA,CAAC;EAAEC;AAAkB,CAAC,KAAK;EAAAC,EAAA;EAC9F,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGV,QAAQ,CAAgBE,kBAAkB,CAACS,cAAc,CAAC,CAAC,CAAC;EAC5F,MAAM,CAACC,eAAe,EAAEC,kBAAkB,CAAC,GAAGb,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAACc,cAAc,EAAEC,iBAAiB,CAAC,GAAGf,QAAQ,CAAqB,IAAI,CAAC;EAC9E,MAAM,CAACgB,uBAAuB,EAAEC,0BAA0B,CAAC,GAAGjB,QAAQ,CAAqB,IAAI,CAAC;EAChG,MAAM,CAACkB,QAAQ,EAAEC,WAAW,CAAC,GAAGnB,QAAQ,CAAkB;IACxDoB,IAAI,EAAE,EAAE;IACRC,aAAa,EAAE,EAAE;IACjBC,QAAQ,EAAE,EAAE;IACZC,QAAQ,EAAE,KAAK;IACfC,cAAc,EAAE;EAClB,CAAC,CAAC;EACF,MAAM,CAACC,eAAe,EAAEC,kBAAkB,CAAC,GAAG1B,QAAQ,CAA4B;IAChF2B,UAAU,EAAE,EAAE;IACdC,aAAa,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IACrDC,MAAM,EAAE;EACV,CAAC,CAAC;EACF,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAGlC,QAAQ,CAAyB,CAAC,CAAC,CAAC;EAChE,MAAM,CAACmC,aAAa,EAAEC,gBAAgB,CAAC,GAAGpC,QAAQ,CAAyB,CAAC,CAAC,CAAC;EAC9E,MAAM,CAACqC,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGtC,QAAQ,CAA0B,CAAC,CAAC,CAAC;EAEzF,MAAMuC,eAAe,GAAGtC,WAAW,CAAC,MAAM;IACxCS,WAAW,CAACR,kBAAkB,CAACS,cAAc,CAAC,CAAC,CAAC;IAChD,IAAIJ,iBAAiB,EAAE;MACrBA,iBAAiB,CAAC,CAAC;IACrB;EACF,CAAC,EAAE,CAACA,iBAAiB,CAAC,CAAC;EAEvB,MAAMiC,SAAS,GAAGvC,WAAW,CAAC,MAAM;IAClCkB,WAAW,CAAC;MACVC,IAAI,EAAE,EAAE;MACRC,aAAa,EAAE,EAAE;MACjBC,QAAQ,EAAE,EAAE;MACZC,QAAQ,EAAE,KAAK;MACfC,cAAc,EAAE;IAClB,CAAC,CAAC;IACFU,SAAS,CAAC,CAAC,CAAC,CAAC;IACbrB,kBAAkB,CAAC,KAAK,CAAC;IACzBE,iBAAiB,CAAC,IAAI,CAAC;EACzB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAM0B,YAAY,GAAGxC,WAAW,CAAEyC,IAAqB,IAA6B;IAClF,MAAMC,SAAiC,GAAG,CAAC,CAAC;IAE5C,IAAI,CAACD,IAAI,CAACtB,IAAI,CAACwB,IAAI,CAAC,CAAC,EAAE;MACrBD,SAAS,CAACvB,IAAI,GAAG,0BAA0B;IAC7C;IAEA,IAAI,CAACsB,IAAI,CAACrB,aAAa,CAACuB,IAAI,CAAC,CAAC,EAAE;MAC9BD,SAAS,CAACtB,aAAa,GAAG,4BAA4B;IACxD,CAAC,MAAM,IAAIZ,QAAQ,CAACoC,IAAI,CAACC,GAAG,IAC1BA,GAAG,CAACzB,aAAa,KAAKqB,IAAI,CAACrB,aAAa,CAACuB,IAAI,CAAC,CAAC,KAC9C,CAAC9B,cAAc,IAAIgC,GAAG,CAACC,EAAE,KAAKjC,cAAc,CAACiC,EAAE,CAClD,CAAC,EAAE;MACDJ,SAAS,CAACtB,aAAa,GAAG,+BAA+B;IAC3D;IAEA,IAAI,CAACqB,IAAI,CAACpB,QAAQ,CAACsB,IAAI,CAAC,CAAC,EAAE;MACzBD,SAAS,CAACrB,QAAQ,GAAG,uBAAuB;IAC9C;IAEA,IAAI,CAACoB,IAAI,CAACnB,QAAQ,CAACqB,IAAI,CAAC,CAAC,EAAE;MACzBD,SAAS,CAACpB,QAAQ,GAAG,sBAAsB;IAC7C;IAEA,MAAMyB,OAAO,GAAGC,UAAU,CAACP,IAAI,CAAClB,cAAc,CAAC;IAC/C,IAAI0B,KAAK,CAACF,OAAO,CAAC,EAAE;MAClBL,SAAS,CAACnB,cAAc,GAAG,wCAAwC;IACrE;IAEA,OAAOmB,SAAS;EAClB,CAAC,EAAE,CAAClC,QAAQ,EAAEK,cAAc,CAAC,CAAC;EAE9B,MAAMqC,iBAAiB,GAAGlD,WAAW,CAAC,CAACmD,KAA4B,EAAEC,KAAa,KAAK;IACrFlC,WAAW,CAACmC,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE,CAACF,KAAK,GAAGC;IAAM,CAAC,CAAC,CAAC;IAClD;IACA,IAAIpB,MAAM,CAACmB,KAAK,CAAC,EAAE;MACjBlB,SAAS,CAACoB,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAE,CAACF,KAAK,GAAG;MAAG,CAAC,CAAC,CAAC;IAC/C;EACF,CAAC,EAAE,CAACnB,MAAM,CAAC,CAAC;EAEZ,MAAMsB,gBAAgB,GAAGtD,WAAW,CAAC,MAAM;IACzCY,kBAAkB,CAAC,IAAI,CAAC;IACxBE,iBAAiB,CAAC,IAAI,CAAC;IACvByB,SAAS,CAAC,CAAC;EACb,CAAC,EAAE,CAACA,SAAS,CAAC,CAAC;EAEf,MAAMgB,iBAAiB,GAAGvD,WAAW,CAAEwD,OAAoB,IAAK;IAC9D1C,iBAAiB,CAAC0C,OAAO,CAAC;IAC1B5C,kBAAkB,CAAC,KAAK,CAAC;IACzBM,WAAW,CAAC;MACVC,IAAI,EAAEqC,OAAO,CAACrC,IAAI;MAClBC,aAAa,EAAEoC,OAAO,CAACpC,aAAa;MACpCC,QAAQ,EAAEmC,OAAO,CAACnC,QAAQ;MAC1BC,QAAQ,EAAEkC,OAAO,CAAClC,QAAQ;MAC1BC,cAAc,EAAEiC,OAAO,CAACjC,cAAc,CAACkC,OAAO,CAAC,CAAC;IAClD,CAAC,CAAC;IACFxB,SAAS,CAAC,CAAC,CAAC,CAAC;EACf,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMyB,YAAY,GAAG1D,WAAW,CAAE2D,CAAkB,IAAK;IACvDA,CAAC,CAACC,cAAc,CAAC,CAAC;IAElB,MAAMlB,SAAS,GAAGF,YAAY,CAACvB,QAAQ,CAAC;IACxC,IAAI4C,MAAM,CAACC,IAAI,CAACpB,SAAS,CAAC,CAACqB,MAAM,GAAG,CAAC,EAAE;MACrC9B,SAAS,CAACS,SAAS,CAAC;MACpB;IACF;IAEA,MAAMsB,WAAW,GAAG;MAClB7C,IAAI,EAAEF,QAAQ,CAACE,IAAI,CAACwB,IAAI,CAAC,CAAC;MAC1BvB,aAAa,EAAEH,QAAQ,CAACG,aAAa,CAACuB,IAAI,CAAC,CAAC;MAC5CtB,QAAQ,EAAEJ,QAAQ,CAACI,QAAQ,CAACsB,IAAI,CAAC,CAAC;MAClCrB,QAAQ,EAAEL,QAAQ,CAACK,QAAQ,CAACqB,IAAI,CAAC,CAA0B;MAC3DpB,cAAc,EAAEyB,UAAU,CAAC/B,QAAQ,CAACM,cAAc;IACpD,CAAC;IAED,IAAI;MACF,IAAIV,cAAc,EAAE;QAClBZ,kBAAkB,CAACgE,aAAa,CAACpD,cAAc,CAACiC,EAAE,EAAEkB,WAAW,CAAC;MAClE,CAAC,MAAM;QACL;QACA,MAAME,cAAc,GAAG;UACrB,GAAGF,WAAW;UACdG,WAAW,EAAE,UAAmB;UAChCC,MAAM,EAAE,QAAiB;UACzBC,WAAW,EAAE,IAAIzC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;QACtC,CAAC;QACD5B,kBAAkB,CAACqE,UAAU,CAACJ,cAAc,CAAC;MAC/C;MAEA5B,eAAe,CAAC,CAAC;MACjBC,SAAS,CAAC,CAAC;IACb,CAAC,CAAC,OAAOgC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MAC7CtC,SAAS,CAAC;QAAEwC,MAAM,EAAE;MAA4C,CAAC,CAAC;IACpE;EACF,CAAC,EAAE,CAACxD,QAAQ,EAAEuB,YAAY,EAAE3B,cAAc,EAAEyB,eAAe,EAAEC,SAAS,CAAC,CAAC;EAExE,MAAMmC,mBAAmB,GAAG1E,WAAW,CAAE2E,SAAiB,IAAK;IAC7D,IAAIC,MAAM,CAACC,OAAO,CAAC,6EAA6E,CAAC,EAAE;MACjG5E,kBAAkB,CAAC6E,aAAa,CAACH,SAAS,CAAC;MAC3CrC,eAAe,CAAC,CAAC;IACnB;EACF,CAAC,EAAE,CAACA,eAAe,CAAC,CAAC;;EAErB;EACA,MAAMyC,mBAAmB,GAAG/E,WAAW,CAAEwD,OAAoB,IAAK;IAChExC,0BAA0B,CAACwC,OAAO,CAAC;IACnC/B,kBAAkB,CAAC;MACjBC,UAAU,EAAE8B,OAAO,CAACjC,cAAc,CAACkC,OAAO,CAAC,CAAC,CAAC;MAC7C9B,aAAa,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;MACrDC,MAAM,EAAE;IACV,CAAC,CAAC;IACFI,gBAAgB,CAAC,CAAC,CAAC,CAAC;EACtB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAM6C,mBAAmB,GAAGhF,WAAW,CAAEyC,IAA+B,IAA6B;IACnG,MAAMC,SAAiC,GAAG,CAAC,CAAC;IAE5C,MAAMK,OAAO,GAAGC,UAAU,CAACP,IAAI,CAACf,UAAU,CAAC;IAC3C,IAAIuB,KAAK,CAACF,OAAO,CAAC,EAAE;MAClBL,SAAS,CAAChB,UAAU,GAAG,gCAAgC;IACzD;IAEA,IAAI,CAACe,IAAI,CAACd,aAAa,EAAE;MACvBe,SAAS,CAACf,aAAa,GAAG,4BAA4B;IACxD;IAEA,IAAI,CAACc,IAAI,CAACV,MAAM,CAACY,IAAI,CAAC,CAAC,EAAE;MACvBD,SAAS,CAACX,MAAM,GAAG,mCAAmC;IACxD;IAEA,OAAOW,SAAS;EAClB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMuC,mBAAmB,GAAGjF,WAAW,CAAE2D,CAAkB,IAAK;IAC9DA,CAAC,CAACC,cAAc,CAAC,CAAC;IAElB,IAAI,CAAC7C,uBAAuB,EAAE;IAE9B,MAAM2B,SAAS,GAAGsC,mBAAmB,CAACxD,eAAe,CAAC;IACtD,IAAIqC,MAAM,CAACC,IAAI,CAACpB,SAAS,CAAC,CAACqB,MAAM,GAAG,CAAC,EAAE;MACrC5B,gBAAgB,CAACO,SAAS,CAAC;MAC3B;IACF;IAEA,IAAI;MACF,MAAMhB,UAAU,GAAGsB,UAAU,CAACxB,eAAe,CAACE,UAAU,CAAC;MACzD,MAAMwD,cAAc,GAAGhF,wBAAwB,CAACiF,oBAAoB,CAClEpE,uBAAuB,EACvBW,UAAU,EACVF,eAAe,CAACG,aAAa,EAC7BH,eAAe,CAACO,MAClB,CAAC;;MAED;MACA9B,kBAAkB,CAACgE,aAAa,CAACiB,cAAc,CAACpC,EAAE,EAAE;QAClD3B,IAAI,EAAE+D,cAAc,CAAC/D,IAAI;QACzBC,aAAa,EAAE8D,cAAc,CAAC9D,aAAa;QAC3CC,QAAQ,EAAE6D,cAAc,CAAC7D,QAAQ;QACjCC,QAAQ,EAAE4D,cAAc,CAAC5D,QAAQ;QACjCC,cAAc,EAAE2D,cAAc,CAAC3D;MACjC,CAAC,CAAC;MAEFe,eAAe,CAAC,CAAC;MACjBtB,0BAA0B,CAAC,IAAI,CAAC;MAChCS,kBAAkB,CAAC;QACjBC,UAAU,EAAE,EAAE;QACdC,aAAa,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QACrDC,MAAM,EAAE;MACV,CAAC,CAAC;IACJ,CAAC,CAAC,OAAOwC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MAChDpC,gBAAgB,CAAC;QAAEsC,MAAM,EAAE;MAA8C,CAAC,CAAC;IAC7E;EACF,CAAC,EAAE,CAAC1D,uBAAuB,EAAES,eAAe,EAAEwD,mBAAmB,EAAE1C,eAAe,CAAC,CAAC;EAEpF,MAAM8C,oBAAoB,GAAGpF,WAAW,CAAE2E,SAAiB,IAAK;IAC9DtC,qBAAqB,CAACgB,IAAI,KAAK;MAC7B,GAAGA,IAAI;MACP,CAACsB,SAAS,GAAG,CAACtB,IAAI,CAACsB,SAAS;IAC9B,CAAC,CAAC,CAAC;EACL,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMU,cAAc,GAAIC,MAAc,IAAa;IACjD,OAAO,IAAIC,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;MACpCC,KAAK,EAAE,UAAU;MACjBnE,QAAQ,EAAE;IACZ,CAAC,CAAC,CAACoE,MAAM,CAACJ,MAAM,CAAC;EACnB,CAAC;EAED,oBACElF,OAAA;IAAKuF,SAAS,EAAC,sBAAsB;IAAAC,QAAA,gBACnCxF,OAAA;MAAKuF,SAAS,EAAC,gBAAgB;MAAAC,QAAA,gBAC7BxF,OAAA;QAAAwF,QAAA,EAAI;MAAuB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAChC5F,OAAA;QAAAwF,QAAA,EAAG;MAAwC;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC5C,CAAC,eAEN5F,OAAA;MAAKuF,SAAS,EAAC,kBAAkB;MAAAC,QAAA,gBAC/BxF,OAAA;QAAKuF,SAAS,EAAC,gBAAgB;QAAAC,QAAA,gBAC7BxF,OAAA;UAAAwF,QAAA,GAAI,sBAAoB,EAACpF,QAAQ,CAACuD,MAAM,EAAC,GAAC;QAAA;UAAA8B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC/C5F,OAAA;UACE6F,IAAI,EAAC,QAAQ;UACbC,OAAO,EAAE5C,gBAAiB;UAC1BqC,SAAS,EAAC,iBAAiB;UAAAC,QAAA,EAC5B;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,EAELxF,QAAQ,CAACuD,MAAM,KAAK,CAAC,gBACpB3D,OAAA;QAAKuF,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1BxF,OAAA;UAAKuF,SAAS,EAAC,YAAY;UAAAC,QAAA,eACzBxF,OAAA;YAAK+F,KAAK,EAAC,IAAI;YAACC,MAAM,EAAC,IAAI;YAACC,OAAO,EAAC,WAAW;YAACC,IAAI,EAAC,MAAM;YAACC,MAAM,EAAC,cAAc;YAACC,WAAW,EAAC,GAAG;YAAAZ,QAAA,gBAC/FxF,OAAA;cAAMqG,CAAC,EAAC,GAAG;cAACC,CAAC,EAAC,GAAG;cAACP,KAAK,EAAC,IAAI;cAACC,MAAM,EAAC,IAAI;cAACO,EAAE,EAAC,GAAG;cAACC,EAAE,EAAC;YAAG;cAAAf,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACzD5F,OAAA;cAAMyG,EAAE,EAAC,GAAG;cAACC,EAAE,EAAC,IAAI;cAACC,EAAE,EAAC,IAAI;cAACC,EAAE,EAAC;YAAI;cAAAnB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACN5F,OAAA;UAAAwF,QAAA,EAAI;QAAgB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACzB5F,OAAA;UAAAwF,QAAA,EAAG;QAA6C;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjD,CAAC,gBAEN5F,OAAA;QAAKuF,SAAS,EAAC,eAAe;QAAAC,QAAA,EAC3BpF,QAAQ,CAACyG,GAAG,CAAEzD,OAAO,iBACpBpD,OAAA;UAAsBuF,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC5CxF,OAAA;YAAKuF,SAAS,EAAC,gBAAgB;YAAAC,QAAA,gBAC7BxF,OAAA;cAAAwF,QAAA,EAAKpC,OAAO,CAACrC;YAAI;cAAA0E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACvB5F,OAAA;cAAKuF,SAAS,EAAC,iBAAiB;cAAAC,QAAA,gBAC9BxF,OAAA;gBACE6F,IAAI,EAAC,QAAQ;gBACbC,OAAO,EAAEA,CAAA,KAAMnB,mBAAmB,CAACvB,OAAO,CAAE;gBAC5CmC,SAAS,EAAC,UAAU;gBACpBuB,KAAK,EAAC,gBAAgB;gBAAAtB,QAAA,eAEtBxF,OAAA;kBAAK+F,KAAK,EAAC,IAAI;kBAACC,MAAM,EAAC,IAAI;kBAACC,OAAO,EAAC,WAAW;kBAACC,IAAI,EAAC,MAAM;kBAACC,MAAM,EAAC,cAAc;kBAACC,WAAW,EAAC,GAAG;kBAAAZ,QAAA,eAC/FxF,OAAA;oBAAM+G,CAAC,EAAC;kBAA2D;oBAAAtB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA,CAAC,eACT5F,OAAA;gBACE6F,IAAI,EAAC,QAAQ;gBACbC,OAAO,EAAEA,CAAA,KAAMd,oBAAoB,CAAC5B,OAAO,CAACV,EAAE,CAAE;gBAChD6C,SAAS,EAAC,UAAU;gBACpBuB,KAAK,EAAC,sBAAsB;gBAAAtB,QAAA,eAE5BxF,OAAA;kBAAK+F,KAAK,EAAC,IAAI;kBAACC,MAAM,EAAC,IAAI;kBAACC,OAAO,EAAC,WAAW;kBAACC,IAAI,EAAC,MAAM;kBAACC,MAAM,EAAC,cAAc;kBAACC,WAAW,EAAC,GAAG;kBAAAZ,QAAA,eAC/FxF,OAAA;oBAAM+G,CAAC,EAAC;kBAA0B;oBAAAtB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA,CAAC,eACT5F,OAAA;gBACE6F,IAAI,EAAC,QAAQ;gBACbC,OAAO,EAAEA,CAAA,KAAM3C,iBAAiB,CAACC,OAAO,CAAE;gBAC1CmC,SAAS,EAAC,UAAU;gBACpBuB,KAAK,EAAC,cAAc;gBAAAtB,QAAA,eAEpBxF,OAAA;kBAAK+F,KAAK,EAAC,IAAI;kBAACC,MAAM,EAAC,IAAI;kBAACC,OAAO,EAAC,WAAW;kBAACC,IAAI,EAAC,MAAM;kBAACC,MAAM,EAAC,cAAc;kBAACC,WAAW,EAAC,GAAG;kBAAAZ,QAAA,gBAC/FxF,OAAA;oBAAM+G,CAAC,EAAC;kBAAU;oBAAAtB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACrB5F,OAAA;oBAAM+G,CAAC,EAAC;kBAAyD;oBAAAtB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA,CAAC,eACT5F,OAAA;gBACE6F,IAAI,EAAC,QAAQ;gBACbC,OAAO,EAAEA,CAAA,KAAMxB,mBAAmB,CAAClB,OAAO,CAACV,EAAE,CAAE;gBAC/C6C,SAAS,EAAC,qBAAqB;gBAC/BuB,KAAK,EAAC,gBAAgB;gBAAAtB,QAAA,eAEtBxF,OAAA;kBAAK+F,KAAK,EAAC,IAAI;kBAACC,MAAM,EAAC,IAAI;kBAACC,OAAO,EAAC,WAAW;kBAACC,IAAI,EAAC,MAAM;kBAACC,MAAM,EAAC,cAAc;kBAACC,WAAW,EAAC,GAAG;kBAAAZ,QAAA,gBAC/FxF,OAAA;oBAAUgH,MAAM,EAAC;kBAAc;oBAAAvB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAClC5F,OAAA;oBAAM+G,CAAC,EAAC;kBAAgF;oBAAAtB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxF;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACN5F,OAAA;YAAKuF,SAAS,EAAC,iBAAiB;YAAAC,QAAA,gBAC9BxF,OAAA;cAAAwF,QAAA,gBAAGxF,OAAA;gBAAAwF,QAAA,EAAQ;cAAK;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAACxC,OAAO,CAACnC,QAAQ;YAAA;cAAAwE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAChD5F,OAAA;cAAAwF,QAAA,gBAAGxF,OAAA;gBAAAwF,QAAA,EAAQ;cAAe;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAACxC,OAAO,CAACpC,aAAa;YAAA;cAAAyE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC/D5F,OAAA;cAAAwF,QAAA,gBAAGxF,OAAA;gBAAAwF,QAAA,EAAQ;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAACxC,OAAO,CAAClC,QAAQ;YAAA;cAAAuE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACpD5F,OAAA;cAAAwF,QAAA,gBAAGxF,OAAA;gBAAAwF,QAAA,EAAQ;cAAgB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,eAAA5F,OAAA;gBAAMuF,SAAS,EAAC,SAAS;gBAAAC,QAAA,EAAEP,cAAc,CAAC7B,OAAO,CAACjC,cAAc;cAAC;gBAAAsE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/G,CAAC;QAAA,GArDExC,OAAO,CAACV,EAAE;UAAA+C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAsDf,CACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,EAEL,CAACrF,eAAe,IAAIE,cAAc,kBACjCT,OAAA;MAAKuF,SAAS,EAAC,sBAAsB;MAAAC,QAAA,gBACnCxF,OAAA;QAAKuF,SAAS,EAAC,aAAa;QAAAC,QAAA,eAC1BxF,OAAA;UAAAwF,QAAA,EAAK/E,cAAc,GAAG,cAAc,GAAG;QAAiB;UAAAgF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3D,CAAC,eAEN5F,OAAA;QAAMiH,QAAQ,EAAE3D,YAAa;QAACiC,SAAS,EAAC,cAAc;QAAAC,QAAA,gBACpDxF,OAAA;UAAKuF,SAAS,EAAC,UAAU;UAAAC,QAAA,gBACvBxF,OAAA;YAAKuF,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBxF,OAAA;cAAOkH,OAAO,EAAC,cAAc;cAAC3B,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAErD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACR5F,OAAA;cACE0C,EAAE,EAAC,cAAc;cACjBmD,IAAI,EAAC,MAAM;cACX7C,KAAK,EAAEnC,QAAQ,CAACE,IAAK;cACrBoG,QAAQ,EAAG5D,CAAC,IAAKT,iBAAiB,CAAC,MAAM,EAAES,CAAC,CAAC6D,MAAM,CAACpE,KAAK,CAAE;cAC3DuC,SAAS,EAAE,cAAc3D,MAAM,CAACb,IAAI,GAAG,OAAO,GAAG,EAAE,EAAG;cACtDsG,WAAW,EAAC;YAA8B;cAAA5B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3C,CAAC,EACDhE,MAAM,CAACb,IAAI,iBAAIf,OAAA;cAAMuF,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAE5D,MAAM,CAACb;YAAI;cAAA0E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9D,CAAC,eAEN5F,OAAA;YAAKuF,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBxF,OAAA;cAAOkH,OAAO,EAAC,gBAAgB;cAAC3B,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAEvD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACR5F,OAAA;cACE0C,EAAE,EAAC,gBAAgB;cACnBmD,IAAI,EAAC,MAAM;cACX7C,KAAK,EAAEnC,QAAQ,CAACG,aAAc;cAC9BmG,QAAQ,EAAG5D,CAAC,IAAKT,iBAAiB,CAAC,eAAe,EAAES,CAAC,CAAC6D,MAAM,CAACpE,KAAK,CAAE;cACpEuC,SAAS,EAAE,cAAc3D,MAAM,CAACZ,aAAa,GAAG,OAAO,GAAG,EAAE,EAAG;cAC/DqG,WAAW,EAAC;YAAkB;cAAA5B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/B,CAAC,EACDhE,MAAM,CAACZ,aAAa,iBAAIhB,OAAA;cAAMuF,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAE5D,MAAM,CAACZ;YAAa;cAAAyE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEN5F,OAAA;UAAKuF,SAAS,EAAC,UAAU;UAAAC,QAAA,gBACvBxF,OAAA;YAAKuF,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBxF,OAAA;cAAOkH,OAAO,EAAC,WAAW;cAAC3B,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAElD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACR5F,OAAA;cACE0C,EAAE,EAAC,WAAW;cACdmD,IAAI,EAAC,MAAM;cACX7C,KAAK,EAAEnC,QAAQ,CAACI,QAAS;cACzBkG,QAAQ,EAAG5D,CAAC,IAAKT,iBAAiB,CAAC,UAAU,EAAES,CAAC,CAAC6D,MAAM,CAACpE,KAAK,CAAE;cAC/DuC,SAAS,EAAE,cAAc3D,MAAM,CAACX,QAAQ,GAAG,OAAO,GAAG,EAAE,EAAG;cAC1DoG,WAAW,EAAC;YAA2B;cAAA5B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxC,CAAC,EACDhE,MAAM,CAACX,QAAQ,iBAAIjB,OAAA;cAAMuF,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAE5D,MAAM,CAACX;YAAQ;cAAAwE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtE,CAAC,eAEN5F,OAAA;YAAKuF,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBxF,OAAA;cAAOkH,OAAO,EAAC,UAAU;cAAC3B,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAEjD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACR5F,OAAA;cACE0C,EAAE,EAAC,UAAU;cACbM,KAAK,EAAEnC,QAAQ,CAACK,QAAS;cACzBiG,QAAQ,EAAG5D,CAAC,IAAKT,iBAAiB,CAAC,UAAU,EAAES,CAAC,CAAC6D,MAAM,CAACpE,KAAK,CAAE;cAC/DuC,SAAS,EAAE,eAAe3D,MAAM,CAACV,QAAQ,GAAG,OAAO,GAAG,EAAE,EAAG;cAAAsE,QAAA,gBAE3DxF,OAAA;gBAAQgD,KAAK,EAAC,KAAK;gBAAAwC,QAAA,EAAC;cAAe;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC5C5F,OAAA;gBAAQgD,KAAK,EAAC,KAAK;gBAAAwC,QAAA,EAAC;cAAU;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACvC5F,OAAA;gBAAQgD,KAAK,EAAC,KAAK;gBAAAwC,QAAA,EAAC;cAAmB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAChD5F,OAAA;gBAAQgD,KAAK,EAAC,KAAK;gBAAAwC,QAAA,EAAC;cAAqB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAClD5F,OAAA;gBAAQgD,KAAK,EAAC,KAAK;gBAAAwC,QAAA,EAAC;cAAuB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9C,CAAC,EACRhE,MAAM,CAACV,QAAQ,iBAAIlB,OAAA;cAAMuF,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAE5D,MAAM,CAACV;YAAQ;cAAAuE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEN5F,OAAA;UAAKuF,SAAS,EAAC,UAAU;UAAAC,QAAA,eACvBxF,OAAA;YAAKuF,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBxF,OAAA;cAAOkH,OAAO,EAAC,iBAAiB;cAAC3B,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAExD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACR5F,OAAA;cACE0C,EAAE,EAAC,iBAAiB;cACpBmD,IAAI,EAAC,QAAQ;cACbyB,IAAI,EAAC,MAAM;cACXtE,KAAK,EAAEnC,QAAQ,CAACM,cAAe;cAC/BgG,QAAQ,EAAG5D,CAAC,IAAKT,iBAAiB,CAAC,gBAAgB,EAAES,CAAC,CAAC6D,MAAM,CAACpE,KAAK,CAAE;cACrEuC,SAAS,EAAE,cAAc3D,MAAM,CAACT,cAAc,GAAG,OAAO,GAAG,EAAE,EAAG;cAChEkG,WAAW,EAAC;YAAM;cAAA5B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnB,CAAC,EACDhE,MAAM,CAACT,cAAc,iBAAInB,OAAA;cAAMuF,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAE5D,MAAM,CAACT;YAAc;cAAAsE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,EAELhE,MAAM,CAACyC,MAAM,iBACZrE,OAAA;UAAKuF,SAAS,EAAC,oBAAoB;UAAAC,QAAA,EAChC5D,MAAM,CAACyC;QAAM;UAAAoB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACX,CACN,eAED5F,OAAA;UAAKuF,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3BxF,OAAA;YACE6F,IAAI,EAAC,QAAQ;YACbC,OAAO,EAAE3D,SAAU;YACnBoD,SAAS,EAAC,mBAAmB;YAAAC,QAAA,EAC9B;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACT5F,OAAA;YACE6F,IAAI,EAAC,QAAQ;YACbN,SAAS,EAAC,iBAAiB;YAAAC,QAAA,EAE1B/E,cAAc,GAAG,gBAAgB,GAAG;UAAa;YAAAgF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5C,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CACN,EAGAjF,uBAAuB,iBACtBX,OAAA;MAAKuF,SAAS,EAAC,4BAA4B;MAAAC,QAAA,gBACzCxF,OAAA;QAAKuF,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1BxF,OAAA;UAAAwF,QAAA,GAAI,mBAAiB,EAAC7E,uBAAuB,CAACI,IAAI;QAAA;UAAA0E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACxD5F,OAAA;UAAAwF,QAAA,EAAG;QAA4D;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChE,CAAC,eAEN5F,OAAA;QAAMiH,QAAQ,EAAEpC,mBAAoB;QAACU,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC3DxF,OAAA;UAAKuF,SAAS,EAAC,UAAU;UAAAC,QAAA,gBACvBxF,OAAA;YAAKuF,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBxF,OAAA;cAAOkH,OAAO,EAAC,yBAAyB;cAAC3B,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAEhE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACR5F,OAAA;cACE0C,EAAE,EAAC,yBAAyB;cAC5BmD,IAAI,EAAC,MAAM;cACX7C,KAAK,EAAEiC,cAAc,CAACtE,uBAAuB,CAACQ,cAAc,CAAE;cAC9DoG,QAAQ;cACRhC,SAAS,EAAC;YAAqB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAEN5F,OAAA;YAAKuF,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBxF,OAAA;cAAOkH,OAAO,EAAC,aAAa;cAAC3B,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAEpD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACR5F,OAAA;cACE0C,EAAE,EAAC,aAAa;cAChBmD,IAAI,EAAC,QAAQ;cACbyB,IAAI,EAAC,MAAM;cACXtE,KAAK,EAAE5B,eAAe,CAACE,UAAW;cAClC6F,QAAQ,EAAG5D,CAAC,IAAKlC,kBAAkB,CAAC4B,IAAI,KAAK;gBAAE,GAAGA,IAAI;gBAAE3B,UAAU,EAAEiC,CAAC,CAAC6D,MAAM,CAACpE;cAAM,CAAC,CAAC,CAAE;cACvFuC,SAAS,EAAE,cAAczD,aAAa,CAACR,UAAU,GAAG,OAAO,GAAG,EAAE,EAAG;cACnE+F,WAAW,EAAC;YAAM;cAAA5B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnB,CAAC,EACD9D,aAAa,CAACR,UAAU,iBAAItB,OAAA;cAAMuF,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAE1D,aAAa,CAACR;YAAU;cAAAmE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEN5F,OAAA;UAAKuF,SAAS,EAAC,UAAU;UAAAC,QAAA,gBACvBxF,OAAA;YAAKuF,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBxF,OAAA;cAAOkH,OAAO,EAAC,gBAAgB;cAAC3B,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAEvD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACR5F,OAAA;cACE0C,EAAE,EAAC,gBAAgB;cACnBmD,IAAI,EAAC,MAAM;cACX7C,KAAK,EAAE5B,eAAe,CAACG,aAAc;cACrC4F,QAAQ,EAAG5D,CAAC,IAAKlC,kBAAkB,CAAC4B,IAAI,KAAK;gBAAE,GAAGA,IAAI;gBAAE1B,aAAa,EAAEgC,CAAC,CAAC6D,MAAM,CAACpE;cAAM,CAAC,CAAC,CAAE;cAC1FuC,SAAS,EAAE,cAAczD,aAAa,CAACP,aAAa,GAAG,OAAO,GAAG,EAAE;YAAG;cAAAkE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvE,CAAC,EACD9D,aAAa,CAACP,aAAa,iBAAIvB,OAAA;cAAMuF,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAE1D,aAAa,CAACP;YAAa;cAAAkE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9F,CAAC,eAEN5F,OAAA;YAAKuF,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBxF,OAAA;cAAOkH,OAAO,EAAC,mBAAmB;cAAC3B,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAE1D;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACR5F,OAAA;cACE0C,EAAE,EAAC,mBAAmB;cACtBM,KAAK,EAAE5B,eAAe,CAACO,MAAO;cAC9BwF,QAAQ,EAAG5D,CAAC,IAAKlC,kBAAkB,CAAC4B,IAAI,KAAK;gBAAE,GAAGA,IAAI;gBAAEtB,MAAM,EAAE4B,CAAC,CAAC6D,MAAM,CAACpE;cAAM,CAAC,CAAC,CAAE;cACnFuC,SAAS,EAAE,eAAezD,aAAa,CAACH,MAAM,GAAG,OAAO,GAAG,EAAE,EAAG;cAAA6D,QAAA,gBAEhExF,OAAA;gBAAQgD,KAAK,EAAC,EAAE;gBAAAwC,QAAA,EAAC;cAAkB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC5C5F,OAAA;gBAAQgD,KAAK,EAAC,qBAAqB;gBAAAwC,QAAA,EAAC;cAAmB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAChE5F,OAAA;gBAAQgD,KAAK,EAAC,mBAAmB;gBAAAwC,QAAA,EAAC;cAAiB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC5D5F,OAAA;gBAAQgD,KAAK,EAAC,qBAAqB;gBAAAwC,QAAA,EAAC;cAAmB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAChE5F,OAAA;gBAAQgD,KAAK,EAAC,gBAAgB;gBAAAwC,QAAA,EAAC;cAAc;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACtD5F,OAAA;gBAAQgD,KAAK,EAAC,uBAAuB;gBAAAwC,QAAA,EAAC;cAAqB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACpE5F,OAAA;gBAAQgD,KAAK,EAAC,OAAO;gBAAAwC,QAAA,EAAC;cAAK;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9B,CAAC,EACR9D,aAAa,CAACH,MAAM,iBAAI3B,OAAA;cAAMuF,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAE1D,aAAa,CAACH;YAAM;cAAA8D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,EAELxE,eAAe,CAACO,MAAM,KAAK,OAAO,iBACjC3B,OAAA;UAAKuF,SAAS,EAAC,UAAU;UAAAC,QAAA,eACvBxF,OAAA;YAAKuF,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBxF,OAAA;cAAOkH,OAAO,EAAC,eAAe;cAAC3B,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAEtD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACR5F,OAAA;cACE0C,EAAE,EAAC,eAAe;cAClBmD,IAAI,EAAC,MAAM;cACXwB,WAAW,EAAC,wBAAwB;cACpC9B,SAAS,EAAC,YAAY;cACtB4B,QAAQ,EAAG5D,CAAC,IAAKlC,kBAAkB,CAAC4B,IAAI,KAAK;gBAAE,GAAGA,IAAI;gBAAEtB,MAAM,EAAE4B,CAAC,CAAC6D,MAAM,CAACpE;cAAM,CAAC,CAAC;YAAE;cAAAyC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpF,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN,eAED5F,OAAA;UAAKuF,SAAS,EAAC,oBAAoB;UAAAC,QAAA,gBACjCxF,OAAA;YAAAwF,QAAA,EAAI;UAAkB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC3B5F,OAAA;YAAKuF,SAAS,EAAC,iBAAiB;YAAAC,QAAA,gBAC9BxF,OAAA;cAAAwF,QAAA,gBAAGxF,OAAA;gBAAAwF,QAAA,EAAQ;cAAgB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAACX,cAAc,CAACtE,uBAAuB,CAACQ,cAAc,CAAC;YAAA;cAAAsE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACjG5F,OAAA;cAAAwF,QAAA,gBAAGxF,OAAA;gBAAAwF,QAAA,EAAQ;cAAY;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAACxE,eAAe,CAACE,UAAU,GAAG2D,cAAc,CAACrC,UAAU,CAACxB,eAAe,CAACE,UAAU,CAAC,CAAC,GAAG,OAAO;YAAA;cAAAmE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACpI5F,OAAA;cAAAwF,QAAA,gBAAGxF,OAAA;gBAAAwF,QAAA,EAAQ;cAAkB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACpC5F,OAAA;gBAAMuF,SAAS,EAAE,qBAAqBnE,eAAe,CAACE,UAAU,IAAIsB,UAAU,CAACxB,eAAe,CAACE,UAAU,CAAC,GAAGX,uBAAuB,CAACQ,cAAc,IAAI,CAAC,GAAG,UAAU,GAAG,UAAU,EAAG;gBAAAqE,QAAA,EAClLpE,eAAe,CAACE,UAAU,GACzB,CAACsB,UAAU,CAACxB,eAAe,CAACE,UAAU,CAAC,GAAGX,uBAAuB,CAACQ,cAAc,IAAI,CAAC,GAAG,GAAG,GAAG,EAAE,IAChG8D,cAAc,CAACrC,UAAU,CAACxB,eAAe,CAACE,UAAU,CAAC,GAAGX,uBAAuB,CAACQ,cAAc,CAAC,GAC/F;cAAO;gBAAAsE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAEL,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,EAEL9D,aAAa,CAACuC,MAAM,iBACnBrE,OAAA;UAAKuF,SAAS,EAAC,oBAAoB;UAAAC,QAAA,EAChC1D,aAAa,CAACuC;QAAM;UAAAoB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClB,CACN,eAED5F,OAAA;UAAKuF,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3BxF,OAAA;YACE6F,IAAI,EAAC,QAAQ;YACbC,OAAO,EAAEA,CAAA,KAAMlF,0BAA0B,CAAC,IAAI,CAAE;YAChD2E,SAAS,EAAC,mBAAmB;YAAAC,QAAA,EAC9B;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACT5F,OAAA;YACE6F,IAAI,EAAC,QAAQ;YACbN,SAAS,EAAC,iBAAiB;YAAAC,QAAA,EAC5B;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CACN,EAGAxF,QAAQ,CAACyG,GAAG,CAACzD,OAAO,IACnBpB,kBAAkB,CAACoB,OAAO,CAACV,EAAE,CAAC,iBAC5B1C,OAAA;MAAmCuF,SAAS,EAAC,yBAAyB;MAAAC,QAAA,gBACpExF,OAAA;QAAKuF,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1BxF,OAAA;UAAAwF,QAAA,GAAI,oBAAkB,EAACpC,OAAO,CAACrC,IAAI;QAAA;UAAA0E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACzC5F,OAAA;UACE6F,IAAI,EAAC,QAAQ;UACbC,OAAO,EAAEA,CAAA,KAAMd,oBAAoB,CAAC5B,OAAO,CAACV,EAAE,CAAE;UAChD6C,SAAS,EAAC,mBAAmB;UAAAC,QAAA,EAC9B;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAEN5F,OAAA;QAAKuF,SAAS,EAAC,iBAAiB;QAAAC,QAAA,EAC7B,CAAC,MAAM;UACN,MAAMgC,OAAO,GAAG1H,wBAAwB,CAAC2H,iBAAiB,CAACrE,OAAO,CAACV,EAAE,CAAC;UACtE,MAAMgF,WAAW,GAAG5H,wBAAwB,CAAC6H,qBAAqB,CAACvE,OAAO,CAACV,EAAE,CAAC;UAE9E,IAAI8E,OAAO,CAAC7D,MAAM,KAAK,CAAC,IAAI+D,WAAW,CAAC/D,MAAM,KAAK,CAAC,EAAE;YACpD,oBACE3D,OAAA;cAAKuF,SAAS,EAAC,eAAe;cAAAC,QAAA,eAC5BxF,OAAA;gBAAAwF,QAAA,EAAG;cAA8C;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClD,CAAC;UAEV;UAEA,oBACE5F,OAAA;YAAKuF,SAAS,EAAC,iBAAiB;YAAAC,QAAA,EAC7BkC,WAAW,CAAC/D,MAAM,GAAG,CAAC,iBACrB3D,OAAA;cAAKuF,SAAS,EAAC,qBAAqB;cAAAC,QAAA,gBAClCxF,OAAA;gBAAAwF,QAAA,EAAI;cAAmB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC5B5F,OAAA;gBAAKuF,SAAS,EAAC,kBAAkB;gBAAAC,QAAA,EAC9BkC,WAAW,CAACb,GAAG,CAACe,UAAU,iBACzB5H,OAAA;kBAAyBuF,SAAS,EAAC,iBAAiB;kBAAAC,QAAA,gBAClDxF,OAAA;oBAAKuF,SAAS,EAAC,mBAAmB;oBAAAC,QAAA,gBAChCxF,OAAA;sBAAMuF,SAAS,EAAC,iBAAiB;sBAAAC,QAAA,EAAEoC,UAAU,CAACC;oBAAI;sBAAApC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC,eAC1D5F,OAAA;sBAAMuF,SAAS,EAAE,qBAAqBqC,UAAU,CAACE,gBAAgB,IAAI,CAAC,GAAG,UAAU,GAAG,UAAU,EAAG;sBAAAtC,QAAA,GAChGoC,UAAU,CAACE,gBAAgB,IAAI,CAAC,GAAG,GAAG,GAAG,EAAE,EAAE7C,cAAc,CAAC2C,UAAU,CAACE,gBAAgB,CAAC;oBAAA;sBAAArC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACrF,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC,eACN5F,OAAA;oBAAKuF,SAAS,EAAC,oBAAoB;oBAAAC,QAAA,gBACjCxF,OAAA;sBAAAwF,QAAA,gBAAGxF,OAAA;wBAAAwF,QAAA,EAAQ;sBAAO;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,KAAC,EAACgC,UAAU,CAACjG,MAAM;oBAAA;sBAAA8D,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACnD5F,OAAA;sBAAAwF,QAAA,gBAAGxF,OAAA;wBAAAwF,QAAA,EAAQ;sBAAiB;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,KAAC,EAACX,cAAc,CAAC2C,UAAU,CAACG,eAAe,CAAC;oBAAA;sBAAAtC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACtF5F,OAAA;sBAAAwF,QAAA,gBAAGxF,OAAA;wBAAAwF,QAAA,EAAQ;sBAAY;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,KAAC,EAACX,cAAc,CAAC2C,UAAU,CAACtG,UAAU,CAAC;oBAAA;sBAAAmE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eAC5E5F,OAAA;sBAAAwF,QAAA,gBAAGxF,OAAA;wBAAAwF,QAAA,EAAQ;sBAAK;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,KAAC,EAACgC,UAAU,CAAC/B,IAAI;oBAAA;sBAAAJ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5C,CAAC;gBAAA,GAZEgC,UAAU,CAAClF,EAAE;kBAAA+C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAalB,CACN;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UACN;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAEV,CAAC,EAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC;IAAA,GArDE,WAAWxC,OAAO,CAACV,EAAE,EAAE;MAAA+C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAsD5B,CAET,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAACzF,EAAA,CAjoBWF,kBAAqD;AAAA+H,EAAA,GAArD/H,kBAAqD;AAAA,IAAA+H,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}