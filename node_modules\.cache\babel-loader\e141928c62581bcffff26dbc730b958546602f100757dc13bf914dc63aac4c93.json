{"ast": null, "code": "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { FlipLeftRight } from '@tensorflow/tfjs-core';\nimport { FlipLeftRightProgram } from '../flip_left_right_gpu';\nexport const flipLeftRightConfig = {\n  kernelName: FlipLeftRight,\n  backendName: 'webgl',\n  kernelFunc: _ref => {\n    let {\n      inputs,\n      backend\n    } = _ref;\n    const {\n      image\n    } = inputs;\n    const webglBackend = backend;\n    const program = new FlipLeftRightProgram(image.shape);\n    const output = webglBackend.runWebGLProgram(program, [image], image.dtype);\n    return output;\n  }\n};", "map": {"version": 3, "names": ["FlipLeftRight", "FlipLeftRightProgram", "flipLeftRightConfig", "kernelName", "backendName", "kernelFunc", "_ref", "inputs", "backend", "image", "webglBackend", "program", "shape", "output", "runWebGLProgram", "dtype"], "sources": ["C:\\tfjs-backend-webgl\\src\\kernels\\FlipLeftRight.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {KernelConfig, Tensor4D} from '@tensorflow/tfjs-core';\nimport {FlipLeftRight, FlipLeftRightInputs} from '@tensorflow/tfjs-core';\n\nimport {MathBackendWebGL} from '../backend_webgl';\nimport {FlipLeftRightProgram} from '../flip_left_right_gpu';\n\nexport const flipLeftRightConfig: KernelConfig = {\n  kernelName: FlipLeftRight,\n  backendName: 'webgl',\n  kernelFunc: ({inputs, backend}) => {\n    const {image} = inputs as FlipLeftRightInputs;\n    const webglBackend = backend as MathBackendWebGL;\n\n    const program = new FlipLeftRightProgram((image as Tensor4D).shape);\n    const output = webglBackend.runWebGLProgram(program, [image], image.dtype);\n    return output;\n  }\n};\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAkBA,SAAQA,aAAa,QAA4B,uBAAuB;AAGxE,SAAQC,oBAAoB,QAAO,wBAAwB;AAE3D,OAAO,MAAMC,mBAAmB,GAAiB;EAC/CC,UAAU,EAAEH,aAAa;EACzBI,WAAW,EAAE,OAAO;EACpBC,UAAU,EAAEC,IAAA,IAAsB;IAAA,IAArB;MAACC,MAAM;MAAEC;IAAO,CAAC,GAAAF,IAAA;IAC5B,MAAM;MAACG;IAAK,CAAC,GAAGF,MAA6B;IAC7C,MAAMG,YAAY,GAAGF,OAA2B;IAEhD,MAAMG,OAAO,GAAG,IAAIV,oBAAoB,CAAEQ,KAAkB,CAACG,KAAK,CAAC;IACnE,MAAMC,MAAM,GAAGH,YAAY,CAACI,eAAe,CAACH,OAAO,EAAE,CAACF,KAAK,CAAC,EAAEA,KAAK,CAACM,KAAK,CAAC;IAC1E,OAAOF,MAAM;EACf;CACD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}