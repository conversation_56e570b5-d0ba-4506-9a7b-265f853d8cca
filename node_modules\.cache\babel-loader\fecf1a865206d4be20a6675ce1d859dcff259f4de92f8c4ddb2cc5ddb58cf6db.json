{"ast": null, "code": "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { backend_util, Sum, util } from '@tensorflow/tfjs-core';\nimport { assertNotComplex } from '../cpu_util';\nimport { zeros } from '../utils/zeros_impl';\nimport { cast } from './Cast';\nimport { identity } from './Identity';\nimport { reshape } from './Reshape';\nimport { transpose } from './Transpose';\nexport function sum(args) {\n  const {\n    inputs,\n    backend,\n    attrs\n  } = args;\n  const {\n    x\n  } = inputs;\n  const {\n    axis,\n    keepDims\n  } = attrs;\n  assertNotComplex(x, 'sum');\n  let $x;\n  if (x.dtype === 'bool') {\n    $x = cast({\n      inputs: {\n        x\n      },\n      backend,\n      attrs: {\n        dtype: 'int32'\n      }\n    });\n  } else {\n    $x = identity({\n      inputs: {\n        x\n      },\n      backend\n    });\n  }\n  const xRank = $x.shape.length;\n  const axes = util.parseAxisParam(axis, $x.shape);\n  const permutation = backend_util.getAxesPermutation(axes, xRank);\n  let reductionAxes = axes;\n  let permutedX = $x;\n  if (permutation != null) {\n    permutedX = transpose({\n      inputs: {\n        x: $x\n      },\n      backend,\n      attrs: {\n        perm: permutation\n      }\n    });\n    reductionAxes = backend_util.getInnerMostAxes(reductionAxes.length, xRank);\n  }\n  backend_util.assertAxesAreInnerMostDims('sum', reductionAxes, permutedX.shape.length);\n  const [outShape, reduceShape] = backend_util.computeOutAndReduceShapes(permutedX.shape, reductionAxes);\n  const resultDtype = backend_util.upcastType(permutedX.dtype, 'int32');\n  let result = zeros(backend, outShape, resultDtype);\n  const reduceSize = util.sizeFromShape(reduceShape);\n  const vals = backend.data.get(result.dataId).values;\n  const aVals = backend.data.get(permutedX.dataId).values;\n  for (let i = 0; i < vals.length; ++i) {\n    const offset = i * reduceSize;\n    let sum = 0;\n    for (let j = 0; j < reduceSize; ++j) {\n      sum += aVals[offset + j];\n    }\n    vals[i] = sum;\n  }\n  if (keepDims) {\n    const newShape = backend_util.expandShapeToKeepDim(result.shape, axes);\n    const oldResult = result;\n    result = reshape({\n      inputs: {\n        x: result\n      },\n      backend,\n      attrs: {\n        shape: newShape\n      }\n    });\n    backend.disposeIntermediateTensorInfo(oldResult);\n  }\n  backend.disposeIntermediateTensorInfo($x);\n  if (permutation != null) {\n    backend.disposeIntermediateTensorInfo(permutedX);\n  }\n  return result;\n}\nexport const sumConfig = {\n  kernelName: Sum,\n  backendName: 'cpu',\n  kernelFunc: sum\n};", "map": {"version": 3, "names": ["backend_util", "Sum", "util", "assertNotComplex", "zeros", "cast", "identity", "reshape", "transpose", "sum", "args", "inputs", "backend", "attrs", "x", "axis", "keepDims", "$x", "dtype", "xRank", "shape", "length", "axes", "parseAxisParam", "permutation", "getAxesPermutation", "reductionAxes", "permutedX", "perm", "getInnerMostAxes", "assertAxesAreInnerMostDims", "outShape", "reduceShape", "computeOutAndReduceShapes", "resultDtype", "upcastType", "result", "reduceSize", "sizeFromShape", "vals", "data", "get", "dataId", "values", "aVals", "i", "offset", "j", "newShape", "expandShapeToKeepDim", "oldResult", "disposeIntermediateTensorInfo", "sumConfig", "kernelName", "backendName", "kernelFunc"], "sources": ["C:\\tfjs-backend-cpu\\src\\kernels\\Sum.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {backend_util, KernelConfig, KernelFunc, Sum, SumAttrs, SumInputs, TensorInfo, TypedArray, util} from '@tensorflow/tfjs-core';\n\nimport {MathBackendCPU} from '../backend_cpu';\nimport {assertNotComplex} from '../cpu_util';\nimport {zeros} from '../utils/zeros_impl';\nimport {cast} from './Cast';\nimport {identity} from './Identity';\nimport {reshape} from './Reshape';\nimport {transpose} from './Transpose';\n\nexport function sum(\n    args: {inputs: SumInputs, backend: MathBackendCPU, attrs: SumAttrs}):\n    TensorInfo {\n  const {inputs, backend, attrs} = args;\n  const {x} = inputs;\n  const {axis, keepDims} = attrs;\n\n  assertNotComplex(x, 'sum');\n\n  let $x;\n  if (x.dtype === 'bool') {\n    $x = cast({inputs: {x}, backend, attrs: {dtype: 'int32'}});\n  } else {\n    $x = identity({inputs: {x}, backend});\n  }\n\n  const xRank = $x.shape.length;\n  const axes = util.parseAxisParam(axis, $x.shape);\n  const permutation = backend_util.getAxesPermutation(axes, xRank);\n\n  let reductionAxes = axes;\n  let permutedX = $x;\n  if (permutation != null) {\n    permutedX =\n        transpose({inputs: {x: $x}, backend, attrs: {perm: permutation}});\n    reductionAxes = backend_util.getInnerMostAxes(reductionAxes.length, xRank);\n  }\n\n  backend_util.assertAxesAreInnerMostDims(\n      'sum', reductionAxes, permutedX.shape.length);\n\n  const [outShape, reduceShape] =\n      backend_util.computeOutAndReduceShapes(permutedX.shape, reductionAxes);\n  const resultDtype = backend_util.upcastType(permutedX.dtype, 'int32');\n  let result = zeros(backend, outShape, resultDtype);\n  const reduceSize = util.sizeFromShape(reduceShape);\n  const vals = backend.data.get(result.dataId).values as TypedArray;\n\n  const aVals = backend.data.get(permutedX.dataId).values as TypedArray;\n  for (let i = 0; i < vals.length; ++i) {\n    const offset = i * reduceSize;\n    let sum = 0;\n    for (let j = 0; j < reduceSize; ++j) {\n      sum += aVals[offset + j];\n    }\n    vals[i] = sum;\n  }\n\n  if (keepDims) {\n    const newShape = backend_util.expandShapeToKeepDim(result.shape, axes);\n    const oldResult = result;\n    result = reshape({inputs: {x: result}, backend, attrs: {shape: newShape}});\n    backend.disposeIntermediateTensorInfo(oldResult);\n  }\n\n  backend.disposeIntermediateTensorInfo($x);\n\n  if (permutation != null) {\n    backend.disposeIntermediateTensorInfo(permutedX);\n  }\n\n  return result;\n}\n\nexport const sumConfig: KernelConfig = {\n  kernelName: Sum,\n  backendName: 'cpu',\n  kernelFunc: sum as unknown as KernelFunc\n};\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAiBA,SAAQA,YAAY,EAA4BC,GAAG,EAA+CC,IAAI,QAAO,uBAAuB;AAGpI,SAAQC,gBAAgB,QAAO,aAAa;AAC5C,SAAQC,KAAK,QAAO,qBAAqB;AACzC,SAAQC,IAAI,QAAO,QAAQ;AAC3B,SAAQC,QAAQ,QAAO,YAAY;AACnC,SAAQC,OAAO,QAAO,WAAW;AACjC,SAAQC,SAAS,QAAO,aAAa;AAErC,OAAM,SAAUC,GAAGA,CACfC,IAAmE;EAErE,MAAM;IAACC,MAAM;IAAEC,OAAO;IAAEC;EAAK,CAAC,GAAGH,IAAI;EACrC,MAAM;IAACI;EAAC,CAAC,GAAGH,MAAM;EAClB,MAAM;IAACI,IAAI;IAAEC;EAAQ,CAAC,GAAGH,KAAK;EAE9BV,gBAAgB,CAACW,CAAC,EAAE,KAAK,CAAC;EAE1B,IAAIG,EAAE;EACN,IAAIH,CAAC,CAACI,KAAK,KAAK,MAAM,EAAE;IACtBD,EAAE,GAAGZ,IAAI,CAAC;MAACM,MAAM,EAAE;QAACG;MAAC,CAAC;MAAEF,OAAO;MAAEC,KAAK,EAAE;QAACK,KAAK,EAAE;MAAO;IAAC,CAAC,CAAC;GAC3D,MAAM;IACLD,EAAE,GAAGX,QAAQ,CAAC;MAACK,MAAM,EAAE;QAACG;MAAC,CAAC;MAAEF;IAAO,CAAC,CAAC;;EAGvC,MAAMO,KAAK,GAAGF,EAAE,CAACG,KAAK,CAACC,MAAM;EAC7B,MAAMC,IAAI,GAAGpB,IAAI,CAACqB,cAAc,CAACR,IAAI,EAAEE,EAAE,CAACG,KAAK,CAAC;EAChD,MAAMI,WAAW,GAAGxB,YAAY,CAACyB,kBAAkB,CAACH,IAAI,EAAEH,KAAK,CAAC;EAEhE,IAAIO,aAAa,GAAGJ,IAAI;EACxB,IAAIK,SAAS,GAAGV,EAAE;EAClB,IAAIO,WAAW,IAAI,IAAI,EAAE;IACvBG,SAAS,GACLnB,SAAS,CAAC;MAACG,MAAM,EAAE;QAACG,CAAC,EAAEG;MAAE,CAAC;MAAEL,OAAO;MAAEC,KAAK,EAAE;QAACe,IAAI,EAAEJ;MAAW;IAAC,CAAC,CAAC;IACrEE,aAAa,GAAG1B,YAAY,CAAC6B,gBAAgB,CAACH,aAAa,CAACL,MAAM,EAAEF,KAAK,CAAC;;EAG5EnB,YAAY,CAAC8B,0BAA0B,CACnC,KAAK,EAAEJ,aAAa,EAAEC,SAAS,CAACP,KAAK,CAACC,MAAM,CAAC;EAEjD,MAAM,CAACU,QAAQ,EAAEC,WAAW,CAAC,GACzBhC,YAAY,CAACiC,yBAAyB,CAACN,SAAS,CAACP,KAAK,EAAEM,aAAa,CAAC;EAC1E,MAAMQ,WAAW,GAAGlC,YAAY,CAACmC,UAAU,CAACR,SAAS,CAACT,KAAK,EAAE,OAAO,CAAC;EACrE,IAAIkB,MAAM,GAAGhC,KAAK,CAACQ,OAAO,EAAEmB,QAAQ,EAAEG,WAAW,CAAC;EAClD,MAAMG,UAAU,GAAGnC,IAAI,CAACoC,aAAa,CAACN,WAAW,CAAC;EAClD,MAAMO,IAAI,GAAG3B,OAAO,CAAC4B,IAAI,CAACC,GAAG,CAACL,MAAM,CAACM,MAAM,CAAC,CAACC,MAAoB;EAEjE,MAAMC,KAAK,GAAGhC,OAAO,CAAC4B,IAAI,CAACC,GAAG,CAACd,SAAS,CAACe,MAAM,CAAC,CAACC,MAAoB;EACrE,KAAK,IAAIE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGN,IAAI,CAAClB,MAAM,EAAE,EAAEwB,CAAC,EAAE;IACpC,MAAMC,MAAM,GAAGD,CAAC,GAAGR,UAAU;IAC7B,IAAI5B,GAAG,GAAG,CAAC;IACX,KAAK,IAAIsC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGV,UAAU,EAAE,EAAEU,CAAC,EAAE;MACnCtC,GAAG,IAAImC,KAAK,CAACE,MAAM,GAAGC,CAAC,CAAC;;IAE1BR,IAAI,CAACM,CAAC,CAAC,GAAGpC,GAAG;;EAGf,IAAIO,QAAQ,EAAE;IACZ,MAAMgC,QAAQ,GAAGhD,YAAY,CAACiD,oBAAoB,CAACb,MAAM,CAAChB,KAAK,EAAEE,IAAI,CAAC;IACtE,MAAM4B,SAAS,GAAGd,MAAM;IACxBA,MAAM,GAAG7B,OAAO,CAAC;MAACI,MAAM,EAAE;QAACG,CAAC,EAAEsB;MAAM,CAAC;MAAExB,OAAO;MAAEC,KAAK,EAAE;QAACO,KAAK,EAAE4B;MAAQ;IAAC,CAAC,CAAC;IAC1EpC,OAAO,CAACuC,6BAA6B,CAACD,SAAS,CAAC;;EAGlDtC,OAAO,CAACuC,6BAA6B,CAAClC,EAAE,CAAC;EAEzC,IAAIO,WAAW,IAAI,IAAI,EAAE;IACvBZ,OAAO,CAACuC,6BAA6B,CAACxB,SAAS,CAAC;;EAGlD,OAAOS,MAAM;AACf;AAEA,OAAO,MAAMgB,SAAS,GAAiB;EACrCC,UAAU,EAAEpD,GAAG;EACfqD,WAAW,EAAE,KAAK;EAClBC,UAAU,EAAE9C;CACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}