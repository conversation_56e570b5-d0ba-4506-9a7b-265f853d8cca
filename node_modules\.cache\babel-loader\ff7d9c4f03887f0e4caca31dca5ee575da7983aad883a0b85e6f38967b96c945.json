{"ast": null, "code": "/**\n * @license\n * Copyright 2022 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { backend_util, TensorScatterUpdate } from '@tensorflow/tfjs-core';\nimport { ScatterProgram } from '../scatter_gpu';\nimport { reshape } from './Reshape';\nexport function tensorScatterUpdate(args) {\n  const {\n    inputs,\n    backend,\n    attrs\n  } = args;\n  const {\n    tensor,\n    indices,\n    updates\n  } = inputs;\n  const {} = attrs;\n  const {\n    sliceRank,\n    numUpdates,\n    sliceSize,\n    strides,\n    outputSize\n  } = backend_util.calculateShapes(updates, indices, tensor.shape);\n  const flattenShape = [outputSize / sliceSize, sliceSize];\n  if (outputSize === 0) {\n    return backend.makeTensorInfo(tensor.shape, indices.dtype);\n  }\n  const flattenIndices = reshape({\n    inputs: {\n      x: indices\n    },\n    backend,\n    attrs: {\n      shape: [numUpdates, sliceRank]\n    }\n  });\n  const flattenX = reshape({\n    inputs: {\n      x: updates\n    },\n    backend,\n    attrs: {\n      shape: [numUpdates, sliceSize]\n    }\n  });\n  const flattenTensor = reshape({\n    inputs: {\n      x: tensor\n    },\n    backend,\n    attrs: {\n      shape: flattenShape\n    }\n  });\n  const program = new ScatterProgram(numUpdates, sliceRank, flattenIndices.shape.length, flattenX.shape.length, strides, flattenShape, false, true);\n  const res = backend.runWebGLProgram(program, [flattenX, flattenIndices, flattenTensor], flattenTensor.dtype);\n  const reshaped = reshape({\n    inputs: {\n      x: res\n    },\n    backend,\n    attrs: {\n      shape: tensor.shape\n    }\n  });\n  backend.disposeIntermediateTensorInfo(flattenIndices);\n  backend.disposeIntermediateTensorInfo(flattenX);\n  backend.disposeIntermediateTensorInfo(flattenTensor);\n  backend.disposeIntermediateTensorInfo(res);\n  return reshaped;\n}\nexport const tensorScatterUpdateConfig = {\n  kernelName: TensorScatterUpdate,\n  backendName: 'webgl',\n  kernelFunc: tensorScatterUpdate\n};", "map": {"version": 3, "names": ["backend_util", "TensorScatterUpdate", "ScatterProgram", "reshape", "tensorScatterUpdate", "args", "inputs", "backend", "attrs", "tensor", "indices", "updates", "sliceRank", "numUpdates", "sliceSize", "strides", "outputSize", "calculateShapes", "shape", "flattenShape", "makeTensorInfo", "dtype", "flattenIndices", "x", "flattenX", "flattenTensor", "program", "length", "res", "runWebGLProgram", "reshaped", "disposeIntermediateTensorInfo", "tensorScatterUpdateConfig", "kernelName", "backendName", "kernelFunc"], "sources": ["C:\\tfjs-backend-webgl\\src\\kernels\\TensorScatterUpdate.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2022 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {backend_util, KernelConfig, KernelFunc, TensorInfo, TensorScatterUpdate, TensorScatterUpdateAttrs, TensorScatterUpdateInputs} from '@tensorflow/tfjs-core';\n\nimport {MathBackendWebGL} from '../backend_webgl';\nimport {ScatterProgram} from '../scatter_gpu';\n\nimport {reshape} from './Reshape';\n\nexport function tensorScatterUpdate(args: {\n  inputs: TensorScatterUpdateInputs,\n  backend: MathBackendWebGL,\n  attrs: TensorScatterUpdateAttrs\n}): TensorInfo {\n  const {inputs, backend, attrs} = args;\n  const {tensor, indices, updates} = inputs;\n  const {} = attrs;\n\n  const {sliceRank, numUpdates, sliceSize, strides, outputSize} =\n      backend_util.calculateShapes(updates, indices, tensor.shape);\n\n  const flattenShape = [outputSize / sliceSize, sliceSize];\n\n  if (outputSize === 0) {\n    return backend.makeTensorInfo(tensor.shape, indices.dtype);\n  }\n\n  const flattenIndices = reshape(\n      {inputs: {x: indices}, backend, attrs: {shape: [numUpdates, sliceRank]}});\n  const flattenX = reshape(\n      {inputs: {x: updates}, backend, attrs: {shape: [numUpdates, sliceSize]}});\n  const flattenTensor =\n      reshape({inputs: {x: tensor}, backend, attrs: {shape: flattenShape}});\n  const program = new ScatterProgram(\n      numUpdates, sliceRank, flattenIndices.shape.length, flattenX.shape.length,\n      strides, flattenShape, false, true);\n  const res = backend.runWebGLProgram(\n      program, [flattenX, flattenIndices, flattenTensor], flattenTensor.dtype);\n\n  const reshaped =\n      reshape({inputs: {x: res}, backend, attrs: {shape: tensor.shape}});\n\n  backend.disposeIntermediateTensorInfo(flattenIndices);\n  backend.disposeIntermediateTensorInfo(flattenX);\n  backend.disposeIntermediateTensorInfo(flattenTensor);\n  backend.disposeIntermediateTensorInfo(res);\n\n  return reshaped;\n}\n\nexport const tensorScatterUpdateConfig: KernelConfig = {\n  kernelName: TensorScatterUpdate,\n  backendName: 'webgl',\n  kernelFunc: tensorScatterUpdate as unknown as KernelFunc\n};\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAiBA,SAAQA,YAAY,EAAwCC,mBAAmB,QAA4D,uBAAuB;AAGlK,SAAQC,cAAc,QAAO,gBAAgB;AAE7C,SAAQC,OAAO,QAAO,WAAW;AAEjC,OAAM,SAAUC,mBAAmBA,CAACC,IAInC;EACC,MAAM;IAACC,MAAM;IAAEC,OAAO;IAAEC;EAAK,CAAC,GAAGH,IAAI;EACrC,MAAM;IAACI,MAAM;IAAEC,OAAO;IAAEC;EAAO,CAAC,GAAGL,MAAM;EACzC,MAAM,EAAE,GAAGE,KAAK;EAEhB,MAAM;IAACI,SAAS;IAAEC,UAAU;IAAEC,SAAS;IAAEC,OAAO;IAAEC;EAAU,CAAC,GACzDhB,YAAY,CAACiB,eAAe,CAACN,OAAO,EAAED,OAAO,EAAED,MAAM,CAACS,KAAK,CAAC;EAEhE,MAAMC,YAAY,GAAG,CAACH,UAAU,GAAGF,SAAS,EAAEA,SAAS,CAAC;EAExD,IAAIE,UAAU,KAAK,CAAC,EAAE;IACpB,OAAOT,OAAO,CAACa,cAAc,CAACX,MAAM,CAACS,KAAK,EAAER,OAAO,CAACW,KAAK,CAAC;;EAG5D,MAAMC,cAAc,GAAGnB,OAAO,CAC1B;IAACG,MAAM,EAAE;MAACiB,CAAC,EAAEb;IAAO,CAAC;IAAEH,OAAO;IAAEC,KAAK,EAAE;MAACU,KAAK,EAAE,CAACL,UAAU,EAAED,SAAS;IAAC;EAAC,CAAC,CAAC;EAC7E,MAAMY,QAAQ,GAAGrB,OAAO,CACpB;IAACG,MAAM,EAAE;MAACiB,CAAC,EAAEZ;IAAO,CAAC;IAAEJ,OAAO;IAAEC,KAAK,EAAE;MAACU,KAAK,EAAE,CAACL,UAAU,EAAEC,SAAS;IAAC;EAAC,CAAC,CAAC;EAC7E,MAAMW,aAAa,GACftB,OAAO,CAAC;IAACG,MAAM,EAAE;MAACiB,CAAC,EAAEd;IAAM,CAAC;IAAEF,OAAO;IAAEC,KAAK,EAAE;MAACU,KAAK,EAAEC;IAAY;EAAC,CAAC,CAAC;EACzE,MAAMO,OAAO,GAAG,IAAIxB,cAAc,CAC9BW,UAAU,EAAED,SAAS,EAAEU,cAAc,CAACJ,KAAK,CAACS,MAAM,EAAEH,QAAQ,CAACN,KAAK,CAACS,MAAM,EACzEZ,OAAO,EAAEI,YAAY,EAAE,KAAK,EAAE,IAAI,CAAC;EACvC,MAAMS,GAAG,GAAGrB,OAAO,CAACsB,eAAe,CAC/BH,OAAO,EAAE,CAACF,QAAQ,EAAEF,cAAc,EAAEG,aAAa,CAAC,EAAEA,aAAa,CAACJ,KAAK,CAAC;EAE5E,MAAMS,QAAQ,GACV3B,OAAO,CAAC;IAACG,MAAM,EAAE;MAACiB,CAAC,EAAEK;IAAG,CAAC;IAAErB,OAAO;IAAEC,KAAK,EAAE;MAACU,KAAK,EAAET,MAAM,CAACS;IAAK;EAAC,CAAC,CAAC;EAEtEX,OAAO,CAACwB,6BAA6B,CAACT,cAAc,CAAC;EACrDf,OAAO,CAACwB,6BAA6B,CAACP,QAAQ,CAAC;EAC/CjB,OAAO,CAACwB,6BAA6B,CAACN,aAAa,CAAC;EACpDlB,OAAO,CAACwB,6BAA6B,CAACH,GAAG,CAAC;EAE1C,OAAOE,QAAQ;AACjB;AAEA,OAAO,MAAME,yBAAyB,GAAiB;EACrDC,UAAU,EAAEhC,mBAAmB;EAC/BiC,WAAW,EAAE,OAAO;EACpBC,UAAU,EAAE/B;CACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}