{"ast": null, "code": "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { ENGINE } from '../engine';\nimport { Real } from '../kernel_names';\nimport { convertToTensor } from '../tensor_util_env';\nimport { op } from './operation';\n/**\n * Returns the real part of a complex (or real) tensor.\n *\n * Given a tensor input, this operation returns a tensor of type float that is\n * the real part of each element in input considered as a complex number.\n *\n * If the input is real, it simply makes a clone.\n *\n * ```js\n * const x = tf.complex([-2.25, 3.25], [4.75, 5.75]);\n * tf.real(x).print();\n * ```\n *\n * @doc {heading: 'Tensors', subheading: 'Creation'}\n */\nfunction real_(input) {\n  const $input = convertToTensor(input, 'input', 'real');\n  const inputs = {\n    input: $input\n  };\n  return ENGINE.runKernel(Real, inputs);\n}\nexport const real = /* @__PURE__ */op({\n  real_\n});", "map": {"version": 3, "names": ["ENGINE", "Real", "convertToTensor", "op", "real_", "input", "$input", "inputs", "runKernel", "real"], "sources": ["C:\\tfjs-core\\src\\ops\\real.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {ENGINE} from '../engine';\nimport {Real, RealInputs} from '../kernel_names';\nimport {Tensor} from '../tensor';\nimport {NamedTensorMap} from '../tensor_types';\nimport {convertToTensor} from '../tensor_util_env';\nimport {TensorLike} from '../types';\nimport {op} from './operation';\n\n/**\n * Returns the real part of a complex (or real) tensor.\n *\n * Given a tensor input, this operation returns a tensor of type float that is\n * the real part of each element in input considered as a complex number.\n *\n * If the input is real, it simply makes a clone.\n *\n * ```js\n * const x = tf.complex([-2.25, 3.25], [4.75, 5.75]);\n * tf.real(x).print();\n * ```\n *\n * @doc {heading: 'Tensors', subheading: 'Creation'}\n */\nfunction real_<T extends Tensor>(input: T|TensorLike): T {\n  const $input = convertToTensor(input, 'input', 'real');\n\n  const inputs: RealInputs = {input: $input};\n  return ENGINE.runKernel(Real, inputs as unknown as NamedTensorMap);\n}\n\nexport const real = /* @__PURE__ */ op({real_});\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAiBA,SAAQA,MAAM,QAAO,WAAW;AAChC,SAAQC,IAAI,QAAmB,iBAAiB;AAGhD,SAAQC,eAAe,QAAO,oBAAoB;AAElD,SAAQC,EAAE,QAAO,aAAa;AAE9B;;;;;;;;;;;;;;;AAeA,SAASC,KAAKA,CAAmBC,KAAmB;EAClD,MAAMC,MAAM,GAAGJ,eAAe,CAACG,KAAK,EAAE,OAAO,EAAE,MAAM,CAAC;EAEtD,MAAME,MAAM,GAAe;IAACF,KAAK,EAAEC;EAAM,CAAC;EAC1C,OAAON,MAAM,CAACQ,SAAS,CAACP,IAAI,EAAEM,MAAmC,CAAC;AACpE;AAEA,OAAO,MAAME,IAAI,GAAG,eAAgBN,EAAE,CAAC;EAACC;AAAK,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}