{"ast": null, "code": "/**\n * @license\n * Copyright 2021 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { env } from '@tensorflow/tfjs-core';\nconst ENV = env();\n/** Whether to keep intermediate tensors. */\nENV.registerFlag('KEEP_INTERMEDIATE_TENSORS', () => false, debugValue => {\n  if (debugValue) {\n    console.warn('Keep intermediate tensors is ON. This will print the values of all ' + 'intermediate tensors during model inference. Not all models ' + 'support this mode. For details, check e2e/benchmarks/ ' + 'model_config.js. This significantly impacts performance.');\n  }\n});", "map": {"version": 3, "names": ["env", "ENV", "registerFlag", "debugValue", "console", "warn"], "sources": ["C:\\tfjs-converter\\src\\flags.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2021 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {env} from '@tensorflow/tfjs-core';\n\nconst ENV = env();\n\n/** Whether to keep intermediate tensors. */\nENV.registerFlag('KEEP_INTERMEDIATE_TENSORS', () => false, debugValue => {\n  if (debugValue) {\n    console.warn(\n        'Keep intermediate tensors is ON. This will print the values of all ' +\n        'intermediate tensors during model inference. Not all models ' +\n        'support this mode. For details, check e2e/benchmarks/ ' +\n        'model_config.js. This significantly impacts performance.');\n  }\n});\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAiBA,SAAQA,GAAG,QAAO,uBAAuB;AAEzC,MAAMC,GAAG,GAAGD,GAAG,EAAE;AAEjB;AACAC,GAAG,CAACC,YAAY,CAAC,2BAA2B,EAAE,MAAM,KAAK,EAAEC,UAAU,IAAG;EACtE,IAAIA,UAAU,EAAE;IACdC,OAAO,CAACC,IAAI,CACR,qEAAqE,GACrE,8DAA8D,GAC9D,wDAAwD,GACxD,0DAA0D,CAAC;;AAEnE,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}