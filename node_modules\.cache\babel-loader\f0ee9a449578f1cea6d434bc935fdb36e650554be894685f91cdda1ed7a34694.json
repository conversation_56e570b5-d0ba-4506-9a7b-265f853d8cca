{"ast": null, "code": "import restArguments from './restArguments.js';\nimport executeBound from './_executeBound.js';\nimport _ from './underscore.js';\n\n// Partially apply a function by creating a version that has had some of its\n// arguments pre-filled, without changing its dynamic `this` context. `_` acts\n// as a placeholder by default, allowing any combination of arguments to be\n// pre-filled. Set `_.partial.placeholder` for a custom placeholder argument.\nvar partial = restArguments(function (func, boundArgs) {\n  var placeholder = partial.placeholder;\n  var bound = function () {\n    var position = 0,\n      length = boundArgs.length;\n    var args = Array(length);\n    for (var i = 0; i < length; i++) {\n      args[i] = boundArgs[i] === placeholder ? arguments[position++] : boundArgs[i];\n    }\n    while (position < arguments.length) args.push(arguments[position++]);\n    return executeBound(func, bound, this, this, args);\n  };\n  return bound;\n});\npartial.placeholder = _;\nexport default partial;", "map": {"version": 3, "names": ["restArguments", "executeBound", "_", "partial", "func", "boundArgs", "placeholder", "bound", "position", "length", "args", "Array", "i", "arguments", "push"], "sources": ["C:/tmsft/node_modules/underscore/modules/partial.js"], "sourcesContent": ["import restArguments from './restArguments.js';\nimport executeBound from './_executeBound.js';\nimport _ from './underscore.js';\n\n// Partially apply a function by creating a version that has had some of its\n// arguments pre-filled, without changing its dynamic `this` context. `_` acts\n// as a placeholder by default, allowing any combination of arguments to be\n// pre-filled. Set `_.partial.placeholder` for a custom placeholder argument.\nvar partial = restArguments(function(func, boundArgs) {\n  var placeholder = partial.placeholder;\n  var bound = function() {\n    var position = 0, length = boundArgs.length;\n    var args = Array(length);\n    for (var i = 0; i < length; i++) {\n      args[i] = boundArgs[i] === placeholder ? arguments[position++] : boundArgs[i];\n    }\n    while (position < arguments.length) args.push(arguments[position++]);\n    return executeBound(func, bound, this, this, args);\n  };\n  return bound;\n});\n\npartial.placeholder = _;\nexport default partial;\n"], "mappings": "AAAA,OAAOA,aAAa,MAAM,oBAAoB;AAC9C,OAAOC,YAAY,MAAM,oBAAoB;AAC7C,OAAOC,CAAC,MAAM,iBAAiB;;AAE/B;AACA;AACA;AACA;AACA,IAAIC,OAAO,GAAGH,aAAa,CAAC,UAASI,IAAI,EAAEC,SAAS,EAAE;EACpD,IAAIC,WAAW,GAAGH,OAAO,CAACG,WAAW;EACrC,IAAIC,KAAK,GAAG,SAAAA,CAAA,EAAW;IACrB,IAAIC,QAAQ,GAAG,CAAC;MAAEC,MAAM,GAAGJ,SAAS,CAACI,MAAM;IAC3C,IAAIC,IAAI,GAAGC,KAAK,CAACF,MAAM,CAAC;IACxB,KAAK,IAAIG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,MAAM,EAAEG,CAAC,EAAE,EAAE;MAC/BF,IAAI,CAACE,CAAC,CAAC,GAAGP,SAAS,CAACO,CAAC,CAAC,KAAKN,WAAW,GAAGO,SAAS,CAACL,QAAQ,EAAE,CAAC,GAAGH,SAAS,CAACO,CAAC,CAAC;IAC/E;IACA,OAAOJ,QAAQ,GAAGK,SAAS,CAACJ,MAAM,EAAEC,IAAI,CAACI,IAAI,CAACD,SAAS,CAACL,QAAQ,EAAE,CAAC,CAAC;IACpE,OAAOP,YAAY,CAACG,IAAI,EAAEG,KAAK,EAAE,IAAI,EAAE,IAAI,EAAEG,IAAI,CAAC;EACpD,CAAC;EACD,OAAOH,KAAK;AACd,CAAC,CAAC;AAEFJ,OAAO,CAACG,WAAW,GAAGJ,CAAC;AACvB,eAAeC,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}