{"ast": null, "code": "import _objectSpread from\"C:/tmsft/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";import React,{useState,useEffect,useCallback,useMemo}from'react';import{bankBalanceService}from'../services/bankBalanceService';import{bankAccountService}from'../services/bankAccountService';import'./BankBalance.css';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const ITEMS_PER_PAGE_OPTIONS=[25,50,100,200];const DEFAULT_ITEMS_PER_PAGE=50;export const BankBalance=_ref=>{let{refreshTrigger}=_ref;// State management\nconst[balances,setBalances]=useState([]);const[bankAccounts,setBankAccounts]=useState([]);const[loading,setLoading]=useState(true);const[error,setError]=useState(null);const[refreshing,setRefreshing]=useState(false);// Pagination state\nconst[currentPage,setCurrentPage]=useState(1);const[itemsPerPage,setItemsPerPage]=useState(DEFAULT_ITEMS_PER_PAGE);// Sorting state\nconst[sortField,setSortField]=useState('date');const[sortDirection,setSortDirection]=useState('desc');// Filtering state\nconst[filters,setFilters]=useState({accountId:'',dateFrom:'',dateTo:'',balanceFrom:'',balanceTo:'',movementFrom:'',movementTo:''});// Load data on component mount and refresh\nconst loadData=useCallback(async()=>{try{// Show refreshing indicator if not initial load\nif(balances.length>0){setRefreshing(true);}else{setLoading(true);}setError(null);// Load bank accounts\nconst accounts=bankAccountService.getAllAccounts();setBankAccounts(accounts);// Load daily balances\nconst dailyBalances=bankBalanceService.getDailyBalances();setBalances(dailyBalances);}catch(err){setError(err instanceof Error?err.message:'Failed to load bank balances');}finally{setLoading(false);setRefreshing(false);}},[balances.length]);useEffect(()=>{loadData();},[refreshTrigger]);// Only depend on refreshTrigger to avoid infinite loops\n// Initial load effect\nuseEffect(()=>{loadData();},[]);// eslint-disable-line react-hooks/exhaustive-deps\n// Filtered and sorted balances\nconst filteredAndSortedBalances=useMemo(()=>{if(!balances||balances.length===0){return[];}// Apply filters\nconst filtered=bankBalanceService.filterBalances(balances,filters);// Apply sorting\nconst sorted=[...filtered].sort((a,b)=>{let aValue;let bValue;switch(sortField){case'date':aValue=a.date;bValue=b.date;break;case'accountName':aValue=a.accountName;bValue=b.accountName;break;case'closingBalance':aValue=a.closingBalance;bValue=b.closingBalance;break;case'openingBalance':aValue=a.openingBalance;bValue=b.openingBalance;break;case'dailyMovement':aValue=a.dailyMovement;bValue=b.dailyMovement;break;case'transactionCount':aValue=a.transactionCount;bValue=b.transactionCount;break;default:aValue=a.date;bValue=b.date;}let comparison=0;if(typeof aValue==='string'&&typeof bValue==='string'){comparison=aValue.localeCompare(bValue);}else{comparison=Number(aValue)-Number(bValue);}return sortDirection==='desc'?-comparison:comparison;});return sorted;},[balances,filters,sortField,sortDirection]);// Paginated data\nconst paginatedBalances=useMemo(()=>{const startIndex=(currentPage-1)*itemsPerPage;const endIndex=startIndex+itemsPerPage;return filteredAndSortedBalances.slice(startIndex,endIndex);},[filteredAndSortedBalances,currentPage,itemsPerPage]);// Statistics\nconst stats=useMemo(()=>{return bankBalanceService.getBalanceStats(filteredAndSortedBalances);},[filteredAndSortedBalances]);// Pagination calculations\nconst totalPages=Math.ceil(filteredAndSortedBalances.length/itemsPerPage);const startItem=(currentPage-1)*itemsPerPage+1;const endItem=Math.min(currentPage*itemsPerPage,filteredAndSortedBalances.length);// Event handlers\nconst handleSort=field=>{if(sortField===field){setSortDirection(prev=>prev==='asc'?'desc':'asc');}else{setSortField(field);setSortDirection('desc');}setCurrentPage(1);};const handleFilterChange=(key,value)=>{setFilters(prev=>_objectSpread(_objectSpread({},prev),{},{[key]:value}));setCurrentPage(1);};const clearFilters=()=>{setFilters({accountId:'',dateFrom:'',dateTo:'',balanceFrom:'',balanceTo:'',movementFrom:'',movementTo:''});setCurrentPage(1);};const handleExport=()=>{try{const csvContent=bankBalanceService.exportToCSV(filteredAndSortedBalances);const blob=new Blob([csvContent],{type:'text/csv;charset=utf-8;'});const link=document.createElement('a');if(link.download!==undefined){const url=URL.createObjectURL(blob);link.setAttribute('href',url);link.setAttribute('download',\"bank_balances_\".concat(new Date().toISOString().split('T')[0],\".csv\"));link.style.visibility='hidden';document.body.appendChild(link);link.click();document.body.removeChild(link);}}catch(err){console.error('Export failed:',err);setError('Failed to export data');}};// Utility functions\nconst formatCurrency=function(amount){let currency=arguments.length>1&&arguments[1]!==undefined?arguments[1]:'USD';return new Intl.NumberFormat('en-US',{style:'currency',currency:currency,minimumFractionDigits:2,maximumFractionDigits:2}).format(amount);};const formatDate=dateString=>{const date=new Date(dateString);return date.toLocaleDateString('en-GB',{day:'2-digit',month:'2-digit',year:'numeric'});};const getAmountClass=amount=>{if(amount>0)return'amount-positive';if(amount<0)return'amount-negative';return'amount-zero';};const getSortClass=field=>{if(sortField!==field)return'sortable';return\"sortable sorted-\".concat(sortDirection);};// Render loading state\nif(loading){return/*#__PURE__*/_jsx(\"div\",{className:\"bank-balance-container\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"bank-balance-loading\",children:[/*#__PURE__*/_jsx(\"svg\",{width:\"32\",height:\"32\",viewBox:\"0 0 24 24\",fill:\"none\",stroke:\"currentColor\",strokeWidth:\"2\",children:/*#__PURE__*/_jsx(\"path\",{d:\"M21 12a9 9 0 11-6.219-8.56\"})}),/*#__PURE__*/_jsx(\"p\",{children:\"Loading bank balances...\"})]})});}// Render error state\nif(error){return/*#__PURE__*/_jsx(\"div\",{className:\"bank-balance-container\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"bank-balance-error\",children:[/*#__PURE__*/_jsxs(\"svg\",{width:\"48\",height:\"48\",viewBox:\"0 0 24 24\",fill:\"none\",stroke:\"currentColor\",strokeWidth:\"2\",children:[/*#__PURE__*/_jsx(\"circle\",{cx:\"12\",cy:\"12\",r:\"10\"}),/*#__PURE__*/_jsx(\"line\",{x1:\"15\",y1:\"9\",x2:\"9\",y2:\"15\"}),/*#__PURE__*/_jsx(\"line\",{x1:\"9\",y1:\"9\",x2:\"15\",y2:\"15\"})]}),/*#__PURE__*/_jsx(\"h3\",{children:\"Error Loading Data\"}),/*#__PURE__*/_jsx(\"p\",{children:error}),/*#__PURE__*/_jsxs(\"button\",{onClick:()=>{setError(null);loadData();},className:\"bank-balance-refresh-btn\",style:{marginTop:'16px'},children:[/*#__PURE__*/_jsxs(\"svg\",{width:\"16\",height:\"16\",viewBox:\"0 0 24 24\",fill:\"none\",stroke:\"currentColor\",strokeWidth:\"2\",children:[/*#__PURE__*/_jsx(\"polyline\",{points:\"23 4 23 10 17 10\"}),/*#__PURE__*/_jsx(\"polyline\",{points:\"1 20 1 14 7 14\"}),/*#__PURE__*/_jsx(\"path\",{d:\"M20.49 9A9 9 0 0 0 5.64 5.64L1 10m22 4l-4.64 4.36A9 9 0 0 1 3.51 15\"})]}),\"Retry\"]})]})});}return/*#__PURE__*/_jsxs(\"div\",{className:\"bank-balance-container\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"bank-balance-header\",children:[/*#__PURE__*/_jsxs(\"h1\",{className:\"bank-balance-title\",children:[/*#__PURE__*/_jsxs(\"svg\",{width:\"24\",height:\"24\",viewBox:\"0 0 24 24\",fill:\"none\",stroke:\"currentColor\",strokeWidth:\"2\",children:[/*#__PURE__*/_jsx(\"rect\",{x:\"2\",y:\"6\",width:\"20\",height:\"12\",rx:\"2\"}),/*#__PURE__*/_jsx(\"circle\",{cx:\"12\",cy:\"12\",r:\"2\"}),/*#__PURE__*/_jsx(\"path\",{d:\"M6 12h.01M18 12h.01\"})]}),\"Bank Balance\"]}),/*#__PURE__*/_jsxs(\"div\",{className:\"bank-balance-actions\",children:[/*#__PURE__*/_jsxs(\"button\",{onClick:loadData,disabled:refreshing,className:\"bank-balance-refresh-btn\",children:[/*#__PURE__*/_jsxs(\"svg\",{width:\"16\",height:\"16\",viewBox:\"0 0 24 24\",fill:\"none\",stroke:\"currentColor\",strokeWidth:\"2\",children:[/*#__PURE__*/_jsx(\"polyline\",{points:\"23 4 23 10 17 10\"}),/*#__PURE__*/_jsx(\"polyline\",{points:\"1 20 1 14 7 14\"}),/*#__PURE__*/_jsx(\"path\",{d:\"M20.49 9A9 9 0 0 0 5.64 5.64L1 10m22 4l-4.64 4.36A9 9 0 0 1 3.51 15\"})]}),refreshing?'Refreshing...':'Refresh']}),/*#__PURE__*/_jsxs(\"button\",{onClick:handleExport,className:\"bank-balance-export-btn\",disabled:filteredAndSortedBalances.length===0,children:[/*#__PURE__*/_jsxs(\"svg\",{width:\"16\",height:\"16\",viewBox:\"0 0 24 24\",fill:\"none\",stroke:\"currentColor\",strokeWidth:\"2\",children:[/*#__PURE__*/_jsx(\"path\",{d:\"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4\"}),/*#__PURE__*/_jsx(\"polyline\",{points:\"7 10 12 15 17 10\"}),/*#__PURE__*/_jsx(\"line\",{x1:\"12\",y1:\"15\",x2:\"12\",y2:\"3\"})]}),\"Export CSV\"]})]})]}),filteredAndSortedBalances.length>0&&/*#__PURE__*/_jsxs(\"div\",{className:\"bank-balance-stats\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"balance-stat-card\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"balance-stat-label\",children:\"Total Days\"}),/*#__PURE__*/_jsx(\"div\",{className:\"balance-stat-value\",children:stats.totalDays.toLocaleString()})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"balance-stat-card\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"balance-stat-label\",children:\"Total Accounts\"}),/*#__PURE__*/_jsx(\"div\",{className:\"balance-stat-value\",children:stats.totalAccounts})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"balance-stat-card\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"balance-stat-label\",children:\"Average Balance\"}),/*#__PURE__*/_jsx(\"div\",{className:\"balance-stat-value\",children:formatCurrency(stats.averageBalance)})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"balance-stat-card\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"balance-stat-label\",children:\"Highest Balance\"}),/*#__PURE__*/_jsx(\"div\",{className:\"balance-stat-value\",children:formatCurrency(stats.highestBalance)})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"balance-stat-card\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"balance-stat-label\",children:\"Lowest Balance\"}),/*#__PURE__*/_jsx(\"div\",{className:\"balance-stat-value\",children:formatCurrency(stats.lowestBalance)})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"balance-stat-card\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"balance-stat-label\",children:\"Date Range\"}),/*#__PURE__*/_jsx(\"div\",{className:\"balance-stat-value\",children:stats.dateRange.from?\"\".concat(formatDate(stats.dateRange.from),\" - \").concat(formatDate(stats.dateRange.to)):'N/A'})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"bank-balance-filters\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"bank-balance-filters-header\",children:[/*#__PURE__*/_jsx(\"h3\",{className:\"bank-balance-filters-title\",children:\"Filters\"}),/*#__PURE__*/_jsx(\"button\",{onClick:clearFilters,className:\"bank-balance-clear-filters\",children:\"Clear All\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"bank-balance-filters-grid\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"filter-group\",children:[/*#__PURE__*/_jsx(\"label\",{children:\"Account\"}),/*#__PURE__*/_jsxs(\"select\",{value:filters.accountId,onChange:e=>handleFilterChange('accountId',e.target.value),children:[/*#__PURE__*/_jsx(\"option\",{value:\"\",children:\"All Accounts\"}),bankAccounts.map(account=>/*#__PURE__*/_jsxs(\"option\",{value:account.id,children:[account.name,\" (\",account.accountNumber,\")\"]},account.id))]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"filter-group\",children:[/*#__PURE__*/_jsx(\"label\",{children:\"Date From\"}),/*#__PURE__*/_jsx(\"input\",{type:\"date\",value:filters.dateFrom,onChange:e=>handleFilterChange('dateFrom',e.target.value)})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"filter-group\",children:[/*#__PURE__*/_jsx(\"label\",{children:\"Date To\"}),/*#__PURE__*/_jsx(\"input\",{type:\"date\",value:filters.dateTo,onChange:e=>handleFilterChange('dateTo',e.target.value)})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"filter-group\",children:[/*#__PURE__*/_jsx(\"label\",{children:\"Min Balance\"}),/*#__PURE__*/_jsx(\"input\",{type:\"number\",step:\"0.01\",placeholder:\"0.00\",value:filters.balanceFrom,onChange:e=>handleFilterChange('balanceFrom',e.target.value)})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"filter-group\",children:[/*#__PURE__*/_jsx(\"label\",{children:\"Max Balance\"}),/*#__PURE__*/_jsx(\"input\",{type:\"number\",step:\"0.01\",placeholder:\"0.00\",value:filters.balanceTo,onChange:e=>handleFilterChange('balanceTo',e.target.value)})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"filter-group\",children:[/*#__PURE__*/_jsx(\"label\",{children:\"Min Movement\"}),/*#__PURE__*/_jsx(\"input\",{type:\"number\",step:\"0.01\",placeholder:\"0.00\",value:filters.movementFrom,onChange:e=>handleFilterChange('movementFrom',e.target.value)})]})]})]}),filteredAndSortedBalances.length===0?/*#__PURE__*/_jsxs(\"div\",{className:\"bank-balance-empty\",children:[/*#__PURE__*/_jsxs(\"svg\",{width:\"48\",height:\"48\",viewBox:\"0 0 24 24\",fill:\"none\",stroke:\"currentColor\",strokeWidth:\"2\",children:[/*#__PURE__*/_jsx(\"rect\",{x:\"2\",y:\"6\",width:\"20\",height:\"12\",rx:\"2\"}),/*#__PURE__*/_jsx(\"circle\",{cx:\"12\",cy:\"12\",r:\"2\"}),/*#__PURE__*/_jsx(\"path\",{d:\"M6 12h.01M18 12h.01\"})]}),/*#__PURE__*/_jsx(\"h3\",{children:\"No Balance Data\"}),/*#__PURE__*/_jsx(\"p\",{children:\"No daily balance records found. Import bank statements to see balance history.\"})]}):/*#__PURE__*/_jsxs(\"div\",{className:\"bank-balance-table-container\",children:[/*#__PURE__*/_jsxs(\"table\",{className:\"bank-balance-table\",children:[/*#__PURE__*/_jsx(\"thead\",{children:/*#__PURE__*/_jsxs(\"tr\",{children:[/*#__PURE__*/_jsx(\"th\",{className:getSortClass('date'),onClick:()=>handleSort('date'),children:\"Date\"}),/*#__PURE__*/_jsx(\"th\",{className:getSortClass('accountName'),onClick:()=>handleSort('accountName'),children:\"Account\"}),/*#__PURE__*/_jsx(\"th\",{className:getSortClass('openingBalance'),onClick:()=>handleSort('openingBalance'),children:\"Opening Balance\"}),/*#__PURE__*/_jsx(\"th\",{className:getSortClass('closingBalance'),onClick:()=>handleSort('closingBalance'),children:\"Closing Balance\"}),/*#__PURE__*/_jsx(\"th\",{className:getSortClass('dailyMovement'),onClick:()=>handleSort('dailyMovement'),children:\"Daily Movement\"}),/*#__PURE__*/_jsx(\"th\",{className:getSortClass('transactionCount'),onClick:()=>handleSort('transactionCount'),children:\"Transactions\"}),/*#__PURE__*/_jsx(\"th\",{children:\"Last Transaction Time\"})]})}),/*#__PURE__*/_jsx(\"tbody\",{children:paginatedBalances.map(balance=>/*#__PURE__*/_jsxs(\"tr\",{children:[/*#__PURE__*/_jsx(\"td\",{className:\"date-cell\",children:formatDate(balance.date)}),/*#__PURE__*/_jsx(\"td\",{children:/*#__PURE__*/_jsxs(\"div\",{className:\"account-info\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"account-name\",children:balance.accountName}),/*#__PURE__*/_jsxs(\"div\",{className:\"account-details\",children:[balance.accountNumber,\" \\u2022 \",balance.bankName]})]})}),/*#__PURE__*/_jsx(\"td\",{className:\"currency-value\",children:formatCurrency(balance.openingBalance,balance.currency)}),/*#__PURE__*/_jsx(\"td\",{className:\"currency-value\",children:formatCurrency(balance.closingBalance,balance.currency)}),/*#__PURE__*/_jsx(\"td\",{className:\"currency-value \".concat(getAmountClass(balance.dailyMovement)),children:formatCurrency(balance.dailyMovement,balance.currency)}),/*#__PURE__*/_jsx(\"td\",{style:{textAlign:'center'},children:balance.transactionCount}),/*#__PURE__*/_jsx(\"td\",{style:{textAlign:'center',fontFamily:'monospace'},children:balance.lastTransactionTime})]},balance.id))})]}),totalPages>1&&/*#__PURE__*/_jsxs(\"div\",{className:\"bank-balance-pagination\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"pagination-info\",children:[\"Showing \",startItem.toLocaleString(),\" to \",endItem.toLocaleString(),\" of \",filteredAndSortedBalances.length.toLocaleString(),\" records\"]}),/*#__PURE__*/_jsxs(\"div\",{className:\"pagination-controls\",children:[/*#__PURE__*/_jsx(\"select\",{value:itemsPerPage,onChange:e=>{setItemsPerPage(Number(e.target.value));setCurrentPage(1);},className:\"pagination-select\",children:ITEMS_PER_PAGE_OPTIONS.map(size=>/*#__PURE__*/_jsxs(\"option\",{value:size,children:[size,\" per page\"]},size))}),/*#__PURE__*/_jsxs(\"div\",{className:\"pagination-buttons\",children:[/*#__PURE__*/_jsx(\"button\",{onClick:()=>setCurrentPage(1),disabled:currentPage===1,className:\"pagination-btn\",children:\"First\"}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>setCurrentPage(prev=>Math.max(1,prev-1)),disabled:currentPage===1,className:\"pagination-btn\",children:\"Previous\"}),/*#__PURE__*/_jsxs(\"span\",{className:\"pagination-btn active\",children:[currentPage,\" of \",totalPages]}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>setCurrentPage(prev=>Math.min(totalPages,prev+1)),disabled:currentPage===totalPages,className:\"pagination-btn\",children:\"Next\"}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>setCurrentPage(totalPages),disabled:currentPage===totalPages,className:\"pagination-btn\",children:\"Last\"})]})]})]})]})]});};", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useCallback", "useMemo", "bankBalanceService", "bankAccountService", "jsx", "_jsx", "jsxs", "_jsxs", "ITEMS_PER_PAGE_OPTIONS", "DEFAULT_ITEMS_PER_PAGE", "BankBalance", "_ref", "refreshTrigger", "balances", "setBalances", "bankAccounts", "setBankAccounts", "loading", "setLoading", "error", "setError", "refreshing", "setRefreshing", "currentPage", "setCurrentPage", "itemsPerPage", "setItemsPerPage", "sortField", "setSortField", "sortDirection", "setSortDirection", "filters", "setFilters", "accountId", "dateFrom", "dateTo", "balanceFrom", "balanceTo", "movementFrom", "movementTo", "loadData", "length", "accounts", "getAllAccounts", "dailyBalances", "getDailyBalances", "err", "Error", "message", "filteredAndSortedBalances", "filtered", "filterBalances", "sorted", "sort", "a", "b", "aValue", "bValue", "date", "accountName", "closingBalance", "openingBalance", "dailyMovement", "transactionCount", "comparison", "localeCompare", "Number", "paginatedBalances", "startIndex", "endIndex", "slice", "stats", "getBalanceStats", "totalPages", "Math", "ceil", "startItem", "endItem", "min", "handleSort", "field", "prev", "handleFilterChange", "key", "value", "_objectSpread", "clearFilters", "handleExport", "csv<PERSON><PERSON>nt", "exportToCSV", "blob", "Blob", "type", "link", "document", "createElement", "download", "undefined", "url", "URL", "createObjectURL", "setAttribute", "concat", "Date", "toISOString", "split", "style", "visibility", "body", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "console", "formatCurrency", "amount", "currency", "arguments", "Intl", "NumberFormat", "minimumFractionDigits", "maximumFractionDigits", "format", "formatDate", "dateString", "toLocaleDateString", "day", "month", "year", "getAmountClass", "getSortClass", "className", "children", "width", "height", "viewBox", "fill", "stroke", "strokeWidth", "d", "cx", "cy", "r", "x1", "y1", "x2", "y2", "onClick", "marginTop", "points", "x", "y", "rx", "disabled", "totalDays", "toLocaleString", "totalAccounts", "averageBalance", "highestBalance", "lowestBalance", "date<PERSON><PERSON><PERSON>", "from", "to", "onChange", "e", "target", "map", "account", "id", "name", "accountNumber", "step", "placeholder", "balance", "bankName", "textAlign", "fontFamily", "lastTransactionTime", "size", "max"], "sources": ["C:/tmsft/src/components/BankBalance.tsx"], "sourcesContent": ["import React, { useState, useEffect, useCallback, useMemo } from 'react';\r\nimport { bankBalanceService, type DailyBalance, type BalanceFilters, type BalanceStats } from '../services/bankBalanceService';\r\nimport { bankAccountService } from '../services/bankAccountService';\r\nimport { BankAccount } from '../types';\r\nimport './BankBalance.css';\r\n\r\ninterface BankBalanceProps {\r\n  refreshTrigger?: number;\r\n}\r\n\r\ntype SortField = 'date' | 'accountName' | 'closingBalance' | 'openingBalance' | 'dailyMovement' | 'transactionCount';\r\ntype SortDirection = 'asc' | 'desc';\r\n\r\nconst ITEMS_PER_PAGE_OPTIONS = [25, 50, 100, 200];\r\nconst DEFAULT_ITEMS_PER_PAGE = 50;\r\n\r\nexport const BankBalance: React.FC<BankBalanceProps> = ({ refreshTrigger }) => {\r\n  // State management\r\n  const [balances, setBalances] = useState<DailyBalance[]>([]);\r\n  const [bankAccounts, setBankAccounts] = useState<BankAccount[]>([]);\r\n  const [loading, setLoading] = useState(true);\r\n  const [error, setError] = useState<string | null>(null);\r\n  const [refreshing, setRefreshing] = useState(false);\r\n  \r\n  // Pagination state\r\n  const [currentPage, setCurrentPage] = useState(1);\r\n  const [itemsPerPage, setItemsPerPage] = useState(DEFAULT_ITEMS_PER_PAGE);\r\n  \r\n  // Sorting state\r\n  const [sortField, setSortField] = useState<SortField>('date');\r\n  const [sortDirection, setSortDirection] = useState<SortDirection>('desc');\r\n  \r\n  // Filtering state\r\n  const [filters, setFilters] = useState<BalanceFilters>({\r\n    accountId: '',\r\n    dateFrom: '',\r\n    dateTo: '',\r\n    balanceFrom: '',\r\n    balanceTo: '',\r\n    movementFrom: '',\r\n    movementTo: ''\r\n  });\r\n\r\n  // Load data on component mount and refresh\r\n  const loadData = useCallback(async () => {\r\n    try {\r\n      // Show refreshing indicator if not initial load\r\n      if (balances.length > 0) {\r\n        setRefreshing(true);\r\n      } else {\r\n        setLoading(true);\r\n      }\r\n      setError(null);\r\n      \r\n      // Load bank accounts\r\n      const accounts = bankAccountService.getAllAccounts();\r\n      setBankAccounts(accounts);\r\n      \r\n      // Load daily balances\r\n      const dailyBalances = bankBalanceService.getDailyBalances();\r\n      setBalances(dailyBalances);\r\n      \r\n    } catch (err) {\r\n      setError(err instanceof Error ? err.message : 'Failed to load bank balances');\r\n    } finally {\r\n      setLoading(false);\r\n      setRefreshing(false);\r\n    }\r\n  }, [balances.length]);\r\n\r\n  useEffect(() => {\r\n    loadData();\r\n  }, [refreshTrigger]); // Only depend on refreshTrigger to avoid infinite loops\r\n\r\n  // Initial load effect\r\n  useEffect(() => {\r\n    loadData();\r\n  }, []); // eslint-disable-line react-hooks/exhaustive-deps\r\n\r\n  // Filtered and sorted balances\r\n  const filteredAndSortedBalances = useMemo(() => {\r\n    if (!balances || balances.length === 0) {\r\n      return [];\r\n    }\r\n    \r\n    // Apply filters\r\n    const filtered = bankBalanceService.filterBalances(balances, filters);\r\n    \r\n    // Apply sorting\r\n    const sorted = [...filtered].sort((a, b) => {\r\n      let aValue: string | number;\r\n      let bValue: string | number;\r\n      \r\n      switch (sortField) {\r\n        case 'date':\r\n          aValue = a.date;\r\n          bValue = b.date;\r\n          break;\r\n        case 'accountName':\r\n          aValue = a.accountName;\r\n          bValue = b.accountName;\r\n          break;\r\n        case 'closingBalance':\r\n          aValue = a.closingBalance;\r\n          bValue = b.closingBalance;\r\n          break;\r\n        case 'openingBalance':\r\n          aValue = a.openingBalance;\r\n          bValue = b.openingBalance;\r\n          break;\r\n        case 'dailyMovement':\r\n          aValue = a.dailyMovement;\r\n          bValue = b.dailyMovement;\r\n          break;\r\n        case 'transactionCount':\r\n          aValue = a.transactionCount;\r\n          bValue = b.transactionCount;\r\n          break;\r\n        default:\r\n          aValue = a.date;\r\n          bValue = b.date;\r\n      }\r\n      \r\n      let comparison = 0;\r\n      if (typeof aValue === 'string' && typeof bValue === 'string') {\r\n        comparison = aValue.localeCompare(bValue);\r\n      } else {\r\n        comparison = Number(aValue) - Number(bValue);\r\n      }\r\n      \r\n      return sortDirection === 'desc' ? -comparison : comparison;\r\n    });\r\n    \r\n    return sorted;\r\n  }, [balances, filters, sortField, sortDirection]);\r\n\r\n  // Paginated data\r\n  const paginatedBalances = useMemo(() => {\r\n    const startIndex = (currentPage - 1) * itemsPerPage;\r\n    const endIndex = startIndex + itemsPerPage;\r\n    return filteredAndSortedBalances.slice(startIndex, endIndex);\r\n  }, [filteredAndSortedBalances, currentPage, itemsPerPage]);\r\n\r\n  // Statistics\r\n  const stats: BalanceStats = useMemo(() => {\r\n    return bankBalanceService.getBalanceStats(filteredAndSortedBalances);\r\n  }, [filteredAndSortedBalances]);\r\n\r\n  // Pagination calculations\r\n  const totalPages = Math.ceil(filteredAndSortedBalances.length / itemsPerPage);\r\n  const startItem = (currentPage - 1) * itemsPerPage + 1;\r\n  const endItem = Math.min(currentPage * itemsPerPage, filteredAndSortedBalances.length);\r\n\r\n  // Event handlers\r\n  const handleSort = (field: SortField) => {\r\n    if (sortField === field) {\r\n      setSortDirection(prev => prev === 'asc' ? 'desc' : 'asc');\r\n    } else {\r\n      setSortField(field);\r\n      setSortDirection('desc');\r\n    }\r\n    setCurrentPage(1);\r\n  };\r\n\r\n  const handleFilterChange = (key: keyof BalanceFilters, value: string) => {\r\n    setFilters(prev => ({ ...prev, [key]: value }));\r\n    setCurrentPage(1);\r\n  };\r\n\r\n  const clearFilters = () => {\r\n    setFilters({\r\n      accountId: '',\r\n      dateFrom: '',\r\n      dateTo: '',\r\n      balanceFrom: '',\r\n      balanceTo: '',\r\n      movementFrom: '',\r\n      movementTo: ''\r\n    });\r\n    setCurrentPage(1);\r\n  };\r\n\r\n  const handleExport = () => {\r\n    try {\r\n      const csvContent = bankBalanceService.exportToCSV(filteredAndSortedBalances);\r\n      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });\r\n      const link = document.createElement('a');\r\n      \r\n      if (link.download !== undefined) {\r\n        const url = URL.createObjectURL(blob);\r\n        link.setAttribute('href', url);\r\n        link.setAttribute('download', `bank_balances_${new Date().toISOString().split('T')[0]}.csv`);\r\n        link.style.visibility = 'hidden';\r\n        document.body.appendChild(link);\r\n        link.click();\r\n        document.body.removeChild(link);\r\n      }\r\n    } catch (err) {\r\n      console.error('Export failed:', err);\r\n      setError('Failed to export data');\r\n    }\r\n  };\r\n\r\n  // Utility functions\r\n  const formatCurrency = (amount: number, currency: string = 'USD'): string => {\r\n    return new Intl.NumberFormat('en-US', {\r\n      style: 'currency',\r\n      currency: currency,\r\n      minimumFractionDigits: 2,\r\n      maximumFractionDigits: 2\r\n    }).format(amount);\r\n  };\r\n\r\n  const formatDate = (dateString: string): string => {\r\n    const date = new Date(dateString);\r\n    return date.toLocaleDateString('en-GB', {\r\n      day: '2-digit',\r\n      month: '2-digit',\r\n      year: 'numeric'\r\n    });\r\n  };\r\n\r\n  const getAmountClass = (amount: number): string => {\r\n    if (amount > 0) return 'amount-positive';\r\n    if (amount < 0) return 'amount-negative';\r\n    return 'amount-zero';\r\n  };\r\n\r\n  const getSortClass = (field: SortField): string => {\r\n    if (sortField !== field) return 'sortable';\r\n    return `sortable sorted-${sortDirection}`;\r\n  };\r\n\r\n  // Render loading state\r\n  if (loading) {\r\n    return (\r\n      <div className=\"bank-balance-container\">\r\n        <div className=\"bank-balance-loading\">\r\n          <svg width=\"32\" height=\"32\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\">\r\n            <path d=\"M21 12a9 9 0 11-6.219-8.56\" />\r\n          </svg>\r\n          <p>Loading bank balances...</p>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  // Render error state\r\n  if (error) {\r\n    return (\r\n      <div className=\"bank-balance-container\">\r\n        <div className=\"bank-balance-error\">\r\n          <svg width=\"48\" height=\"48\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\">\r\n            <circle cx=\"12\" cy=\"12\" r=\"10\" />\r\n            <line x1=\"15\" y1=\"9\" x2=\"9\" y2=\"15\" />\r\n            <line x1=\"9\" y1=\"9\" x2=\"15\" y2=\"15\" />\r\n          </svg>\r\n          <h3>Error Loading Data</h3>\r\n          <p>{error}</p>\r\n          <button\r\n            onClick={() => {\r\n              setError(null);\r\n              loadData();\r\n            }}\r\n            className=\"bank-balance-refresh-btn\"\r\n            style={{ marginTop: '16px' }}\r\n          >\r\n            <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\">\r\n              <polyline points=\"23 4 23 10 17 10\" />\r\n              <polyline points=\"1 20 1 14 7 14\" />\r\n              <path d=\"M20.49 9A9 9 0 0 0 5.64 5.64L1 10m22 4l-4.64 4.36A9 9 0 0 1 3.51 15\" />\r\n            </svg>\r\n            Retry\r\n          </button>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className=\"bank-balance-container\">\r\n      {/* Header */}\r\n      <div className=\"bank-balance-header\">\r\n        <h1 className=\"bank-balance-title\">\r\n          <svg width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\">\r\n            <rect x=\"2\" y=\"6\" width=\"20\" height=\"12\" rx=\"2\" />\r\n            <circle cx=\"12\" cy=\"12\" r=\"2\" />\r\n            <path d=\"M6 12h.01M18 12h.01\" />\r\n          </svg>\r\n          Bank Balance\r\n        </h1>\r\n        <div className=\"bank-balance-actions\">\r\n          <button\r\n            onClick={loadData}\r\n            disabled={refreshing}\r\n            className=\"bank-balance-refresh-btn\"\r\n          >\r\n            <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\">\r\n              <polyline points=\"23 4 23 10 17 10\" />\r\n              <polyline points=\"1 20 1 14 7 14\" />\r\n              <path d=\"M20.49 9A9 9 0 0 0 5.64 5.64L1 10m22 4l-4.64 4.36A9 9 0 0 1 3.51 15\" />\r\n            </svg>\r\n            {refreshing ? 'Refreshing...' : 'Refresh'}\r\n          </button>\r\n          <button\r\n            onClick={handleExport}\r\n            className=\"bank-balance-export-btn\"\r\n            disabled={filteredAndSortedBalances.length === 0}\r\n          >\r\n            <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\">\r\n              <path d=\"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4\" />\r\n              <polyline points=\"7 10 12 15 17 10\" />\r\n              <line x1=\"12\" y1=\"15\" x2=\"12\" y2=\"3\" />\r\n            </svg>\r\n            Export CSV\r\n          </button>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Statistics */}\r\n      {filteredAndSortedBalances.length > 0 && (\r\n        <div className=\"bank-balance-stats\">\r\n          <div className=\"balance-stat-card\">\r\n            <div className=\"balance-stat-label\">Total Days</div>\r\n            <div className=\"balance-stat-value\">{stats.totalDays.toLocaleString()}</div>\r\n          </div>\r\n          <div className=\"balance-stat-card\">\r\n            <div className=\"balance-stat-label\">Total Accounts</div>\r\n            <div className=\"balance-stat-value\">{stats.totalAccounts}</div>\r\n          </div>\r\n          <div className=\"balance-stat-card\">\r\n            <div className=\"balance-stat-label\">Average Balance</div>\r\n            <div className=\"balance-stat-value\">{formatCurrency(stats.averageBalance)}</div>\r\n          </div>\r\n          <div className=\"balance-stat-card\">\r\n            <div className=\"balance-stat-label\">Highest Balance</div>\r\n            <div className=\"balance-stat-value\">{formatCurrency(stats.highestBalance)}</div>\r\n          </div>\r\n          <div className=\"balance-stat-card\">\r\n            <div className=\"balance-stat-label\">Lowest Balance</div>\r\n            <div className=\"balance-stat-value\">{formatCurrency(stats.lowestBalance)}</div>\r\n          </div>\r\n          <div className=\"balance-stat-card\">\r\n            <div className=\"balance-stat-label\">Date Range</div>\r\n            <div className=\"balance-stat-value\">\r\n              {stats.dateRange.from ? `${formatDate(stats.dateRange.from)} - ${formatDate(stats.dateRange.to)}` : 'N/A'}\r\n            </div>\r\n          </div>\r\n        </div>\r\n      )}\r\n\r\n      {/* Filters */}\r\n      <div className=\"bank-balance-filters\">\r\n        <div className=\"bank-balance-filters-header\">\r\n          <h3 className=\"bank-balance-filters-title\">Filters</h3>\r\n          <button onClick={clearFilters} className=\"bank-balance-clear-filters\">\r\n            Clear All\r\n          </button>\r\n        </div>\r\n        <div className=\"bank-balance-filters-grid\">\r\n          <div className=\"filter-group\">\r\n            <label>Account</label>\r\n            <select\r\n              value={filters.accountId}\r\n              onChange={(e) => handleFilterChange('accountId', e.target.value)}\r\n            >\r\n              <option value=\"\">All Accounts</option>\r\n              {bankAccounts.map(account => (\r\n                <option key={account.id} value={account.id}>\r\n                  {account.name} ({account.accountNumber})\r\n                </option>\r\n              ))}\r\n            </select>\r\n          </div>\r\n          <div className=\"filter-group\">\r\n            <label>Date From</label>\r\n            <input\r\n              type=\"date\"\r\n              value={filters.dateFrom}\r\n              onChange={(e) => handleFilterChange('dateFrom', e.target.value)}\r\n            />\r\n          </div>\r\n          <div className=\"filter-group\">\r\n            <label>Date To</label>\r\n            <input\r\n              type=\"date\"\r\n              value={filters.dateTo}\r\n              onChange={(e) => handleFilterChange('dateTo', e.target.value)}\r\n            />\r\n          </div>\r\n          <div className=\"filter-group\">\r\n            <label>Min Balance</label>\r\n            <input\r\n              type=\"number\"\r\n              step=\"0.01\"\r\n              placeholder=\"0.00\"\r\n              value={filters.balanceFrom}\r\n              onChange={(e) => handleFilterChange('balanceFrom', e.target.value)}\r\n            />\r\n          </div>\r\n          <div className=\"filter-group\">\r\n            <label>Max Balance</label>\r\n            <input\r\n              type=\"number\"\r\n              step=\"0.01\"\r\n              placeholder=\"0.00\"\r\n              value={filters.balanceTo}\r\n              onChange={(e) => handleFilterChange('balanceTo', e.target.value)}\r\n            />\r\n          </div>\r\n          <div className=\"filter-group\">\r\n            <label>Min Movement</label>\r\n            <input\r\n              type=\"number\"\r\n              step=\"0.01\"\r\n              placeholder=\"0.00\"\r\n              value={filters.movementFrom}\r\n              onChange={(e) => handleFilterChange('movementFrom', e.target.value)}\r\n            />\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Table */}\r\n      {filteredAndSortedBalances.length === 0 ? (\r\n        <div className=\"bank-balance-empty\">\r\n          <svg width=\"48\" height=\"48\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\">\r\n            <rect x=\"2\" y=\"6\" width=\"20\" height=\"12\" rx=\"2\" />\r\n            <circle cx=\"12\" cy=\"12\" r=\"2\" />\r\n            <path d=\"M6 12h.01M18 12h.01\" />\r\n          </svg>\r\n          <h3>No Balance Data</h3>\r\n          <p>No daily balance records found. Import bank statements to see balance history.</p>\r\n        </div>\r\n      ) : (\r\n        <div className=\"bank-balance-table-container\">\r\n          <table className=\"bank-balance-table\">\r\n            <thead>\r\n              <tr>\r\n                <th className={getSortClass('date')} onClick={() => handleSort('date')}>\r\n                  Date\r\n                </th>\r\n                <th className={getSortClass('accountName')} onClick={() => handleSort('accountName')}>\r\n                  Account\r\n                </th>\r\n                <th className={getSortClass('openingBalance')} onClick={() => handleSort('openingBalance')}>\r\n                  Opening Balance\r\n                </th>\r\n                <th className={getSortClass('closingBalance')} onClick={() => handleSort('closingBalance')}>\r\n                  Closing Balance\r\n                </th>\r\n                <th className={getSortClass('dailyMovement')} onClick={() => handleSort('dailyMovement')}>\r\n                  Daily Movement\r\n                </th>\r\n                <th className={getSortClass('transactionCount')} onClick={() => handleSort('transactionCount')}>\r\n                  Transactions\r\n                </th>\r\n                <th>Last Transaction Time</th>\r\n              </tr>\r\n            </thead>\r\n            <tbody>\r\n              {paginatedBalances.map((balance) => (\r\n                <tr key={balance.id}>\r\n                  <td className=\"date-cell\">{formatDate(balance.date)}</td>\r\n                  <td>\r\n                    <div className=\"account-info\">\r\n                      <div className=\"account-name\">{balance.accountName}</div>\r\n                      <div className=\"account-details\">\r\n                        {balance.accountNumber} • {balance.bankName}\r\n                      </div>\r\n                    </div>\r\n                  </td>\r\n                  <td className=\"currency-value\">\r\n                    {formatCurrency(balance.openingBalance, balance.currency)}\r\n                  </td>\r\n                  <td className=\"currency-value\">\r\n                    {formatCurrency(balance.closingBalance, balance.currency)}\r\n                  </td>\r\n                  <td className={`currency-value ${getAmountClass(balance.dailyMovement)}`}>\r\n                    {formatCurrency(balance.dailyMovement, balance.currency)}\r\n                  </td>\r\n                  <td style={{ textAlign: 'center' }}>\r\n                    {balance.transactionCount}\r\n                  </td>\r\n                  <td style={{ textAlign: 'center', fontFamily: 'monospace' }}>\r\n                    {balance.lastTransactionTime}\r\n                  </td>\r\n                </tr>\r\n              ))}\r\n            </tbody>\r\n          </table>\r\n\r\n          {/* Pagination */}\r\n          {totalPages > 1 && (\r\n            <div className=\"bank-balance-pagination\">\r\n              <div className=\"pagination-info\">\r\n                Showing {startItem.toLocaleString()} to {endItem.toLocaleString()} of {filteredAndSortedBalances.length.toLocaleString()} records\r\n              </div>\r\n              <div className=\"pagination-controls\">\r\n                <select\r\n                  value={itemsPerPage}\r\n                  onChange={(e) => {\r\n                    setItemsPerPage(Number(e.target.value));\r\n                    setCurrentPage(1);\r\n                  }}\r\n                  className=\"pagination-select\"\r\n                >\r\n                  {ITEMS_PER_PAGE_OPTIONS.map(size => (\r\n                    <option key={size} value={size}>{size} per page</option>\r\n                  ))}\r\n                </select>\r\n                <div className=\"pagination-buttons\">\r\n                  <button\r\n                    onClick={() => setCurrentPage(1)}\r\n                    disabled={currentPage === 1}\r\n                    className=\"pagination-btn\"\r\n                  >\r\n                    First\r\n                  </button>\r\n                  <button\r\n                    onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}\r\n                    disabled={currentPage === 1}\r\n                    className=\"pagination-btn\"\r\n                  >\r\n                    Previous\r\n                  </button>\r\n                  <span className=\"pagination-btn active\">\r\n                    {currentPage} of {totalPages}\r\n                  </span>\r\n                  <button\r\n                    onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}\r\n                    disabled={currentPage === totalPages}\r\n                    className=\"pagination-btn\"\r\n                  >\r\n                    Next\r\n                  </button>\r\n                  <button\r\n                    onClick={() => setCurrentPage(totalPages)}\r\n                    disabled={currentPage === totalPages}\r\n                    className=\"pagination-btn\"\r\n                  >\r\n                    Last\r\n                  </button>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          )}\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n}; "], "mappings": "6FAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,CAAEC,WAAW,CAAEC,OAAO,KAAQ,OAAO,CACxE,OAASC,kBAAkB,KAAmE,gCAAgC,CAC9H,OAASC,kBAAkB,KAAQ,gCAAgC,CAEnE,MAAO,mBAAmB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAS3B,KAAM,CAAAC,sBAAsB,CAAG,CAAC,EAAE,CAAE,EAAE,CAAE,GAAG,CAAE,GAAG,CAAC,CACjD,KAAM,CAAAC,sBAAsB,CAAG,EAAE,CAEjC,MAAO,MAAM,CAAAC,WAAuC,CAAGC,IAAA,EAAwB,IAAvB,CAAEC,cAAe,CAAC,CAAAD,IAAA,CACxE;AACA,KAAM,CAACE,QAAQ,CAAEC,WAAW,CAAC,CAAGhB,QAAQ,CAAiB,EAAE,CAAC,CAC5D,KAAM,CAACiB,YAAY,CAAEC,eAAe,CAAC,CAAGlB,QAAQ,CAAgB,EAAE,CAAC,CACnE,KAAM,CAACmB,OAAO,CAAEC,UAAU,CAAC,CAAGpB,QAAQ,CAAC,IAAI,CAAC,CAC5C,KAAM,CAACqB,KAAK,CAAEC,QAAQ,CAAC,CAAGtB,QAAQ,CAAgB,IAAI,CAAC,CACvD,KAAM,CAACuB,UAAU,CAAEC,aAAa,CAAC,CAAGxB,QAAQ,CAAC,KAAK,CAAC,CAEnD;AACA,KAAM,CAACyB,WAAW,CAAEC,cAAc,CAAC,CAAG1B,QAAQ,CAAC,CAAC,CAAC,CACjD,KAAM,CAAC2B,YAAY,CAAEC,eAAe,CAAC,CAAG5B,QAAQ,CAACW,sBAAsB,CAAC,CAExE;AACA,KAAM,CAACkB,SAAS,CAAEC,YAAY,CAAC,CAAG9B,QAAQ,CAAY,MAAM,CAAC,CAC7D,KAAM,CAAC+B,aAAa,CAAEC,gBAAgB,CAAC,CAAGhC,QAAQ,CAAgB,MAAM,CAAC,CAEzE;AACA,KAAM,CAACiC,OAAO,CAAEC,UAAU,CAAC,CAAGlC,QAAQ,CAAiB,CACrDmC,SAAS,CAAE,EAAE,CACbC,QAAQ,CAAE,EAAE,CACZC,MAAM,CAAE,EAAE,CACVC,WAAW,CAAE,EAAE,CACfC,SAAS,CAAE,EAAE,CACbC,YAAY,CAAE,EAAE,CAChBC,UAAU,CAAE,EACd,CAAC,CAAC,CAEF;AACA,KAAM,CAAAC,QAAQ,CAAGxC,WAAW,CAAC,SAAY,CACvC,GAAI,CACF;AACA,GAAIa,QAAQ,CAAC4B,MAAM,CAAG,CAAC,CAAE,CACvBnB,aAAa,CAAC,IAAI,CAAC,CACrB,CAAC,IAAM,CACLJ,UAAU,CAAC,IAAI,CAAC,CAClB,CACAE,QAAQ,CAAC,IAAI,CAAC,CAEd;AACA,KAAM,CAAAsB,QAAQ,CAAGvC,kBAAkB,CAACwC,cAAc,CAAC,CAAC,CACpD3B,eAAe,CAAC0B,QAAQ,CAAC,CAEzB;AACA,KAAM,CAAAE,aAAa,CAAG1C,kBAAkB,CAAC2C,gBAAgB,CAAC,CAAC,CAC3D/B,WAAW,CAAC8B,aAAa,CAAC,CAE5B,CAAE,MAAOE,GAAG,CAAE,CACZ1B,QAAQ,CAAC0B,GAAG,WAAY,CAAAC,KAAK,CAAGD,GAAG,CAACE,OAAO,CAAG,8BAA8B,CAAC,CAC/E,CAAC,OAAS,CACR9B,UAAU,CAAC,KAAK,CAAC,CACjBI,aAAa,CAAC,KAAK,CAAC,CACtB,CACF,CAAC,CAAE,CAACT,QAAQ,CAAC4B,MAAM,CAAC,CAAC,CAErB1C,SAAS,CAAC,IAAM,CACdyC,QAAQ,CAAC,CAAC,CACZ,CAAC,CAAE,CAAC5B,cAAc,CAAC,CAAC,CAAE;AAEtB;AACAb,SAAS,CAAC,IAAM,CACdyC,QAAQ,CAAC,CAAC,CACZ,CAAC,CAAE,EAAE,CAAC,CAAE;AAER;AACA,KAAM,CAAAS,yBAAyB,CAAGhD,OAAO,CAAC,IAAM,CAC9C,GAAI,CAACY,QAAQ,EAAIA,QAAQ,CAAC4B,MAAM,GAAK,CAAC,CAAE,CACtC,MAAO,EAAE,CACX,CAEA;AACA,KAAM,CAAAS,QAAQ,CAAGhD,kBAAkB,CAACiD,cAAc,CAACtC,QAAQ,CAAEkB,OAAO,CAAC,CAErE;AACA,KAAM,CAAAqB,MAAM,CAAG,CAAC,GAAGF,QAAQ,CAAC,CAACG,IAAI,CAAC,CAACC,CAAC,CAAEC,CAAC,GAAK,CAC1C,GAAI,CAAAC,MAAuB,CAC3B,GAAI,CAAAC,MAAuB,CAE3B,OAAQ9B,SAAS,EACf,IAAK,MAAM,CACT6B,MAAM,CAAGF,CAAC,CAACI,IAAI,CACfD,MAAM,CAAGF,CAAC,CAACG,IAAI,CACf,MACF,IAAK,aAAa,CAChBF,MAAM,CAAGF,CAAC,CAACK,WAAW,CACtBF,MAAM,CAAGF,CAAC,CAACI,WAAW,CACtB,MACF,IAAK,gBAAgB,CACnBH,MAAM,CAAGF,CAAC,CAACM,cAAc,CACzBH,MAAM,CAAGF,CAAC,CAACK,cAAc,CACzB,MACF,IAAK,gBAAgB,CACnBJ,MAAM,CAAGF,CAAC,CAACO,cAAc,CACzBJ,MAAM,CAAGF,CAAC,CAACM,cAAc,CACzB,MACF,IAAK,eAAe,CAClBL,MAAM,CAAGF,CAAC,CAACQ,aAAa,CACxBL,MAAM,CAAGF,CAAC,CAACO,aAAa,CACxB,MACF,IAAK,kBAAkB,CACrBN,MAAM,CAAGF,CAAC,CAACS,gBAAgB,CAC3BN,MAAM,CAAGF,CAAC,CAACQ,gBAAgB,CAC3B,MACF,QACEP,MAAM,CAAGF,CAAC,CAACI,IAAI,CACfD,MAAM,CAAGF,CAAC,CAACG,IAAI,CACnB,CAEA,GAAI,CAAAM,UAAU,CAAG,CAAC,CAClB,GAAI,MAAO,CAAAR,MAAM,GAAK,QAAQ,EAAI,MAAO,CAAAC,MAAM,GAAK,QAAQ,CAAE,CAC5DO,UAAU,CAAGR,MAAM,CAACS,aAAa,CAACR,MAAM,CAAC,CAC3C,CAAC,IAAM,CACLO,UAAU,CAAGE,MAAM,CAACV,MAAM,CAAC,CAAGU,MAAM,CAACT,MAAM,CAAC,CAC9C,CAEA,MAAO,CAAA5B,aAAa,GAAK,MAAM,CAAG,CAACmC,UAAU,CAAGA,UAAU,CAC5D,CAAC,CAAC,CAEF,MAAO,CAAAZ,MAAM,CACf,CAAC,CAAE,CAACvC,QAAQ,CAAEkB,OAAO,CAAEJ,SAAS,CAAEE,aAAa,CAAC,CAAC,CAEjD;AACA,KAAM,CAAAsC,iBAAiB,CAAGlE,OAAO,CAAC,IAAM,CACtC,KAAM,CAAAmE,UAAU,CAAG,CAAC7C,WAAW,CAAG,CAAC,EAAIE,YAAY,CACnD,KAAM,CAAA4C,QAAQ,CAAGD,UAAU,CAAG3C,YAAY,CAC1C,MAAO,CAAAwB,yBAAyB,CAACqB,KAAK,CAACF,UAAU,CAAEC,QAAQ,CAAC,CAC9D,CAAC,CAAE,CAACpB,yBAAyB,CAAE1B,WAAW,CAAEE,YAAY,CAAC,CAAC,CAE1D;AACA,KAAM,CAAA8C,KAAmB,CAAGtE,OAAO,CAAC,IAAM,CACxC,MAAO,CAAAC,kBAAkB,CAACsE,eAAe,CAACvB,yBAAyB,CAAC,CACtE,CAAC,CAAE,CAACA,yBAAyB,CAAC,CAAC,CAE/B;AACA,KAAM,CAAAwB,UAAU,CAAGC,IAAI,CAACC,IAAI,CAAC1B,yBAAyB,CAACR,MAAM,CAAGhB,YAAY,CAAC,CAC7E,KAAM,CAAAmD,SAAS,CAAG,CAACrD,WAAW,CAAG,CAAC,EAAIE,YAAY,CAAG,CAAC,CACtD,KAAM,CAAAoD,OAAO,CAAGH,IAAI,CAACI,GAAG,CAACvD,WAAW,CAAGE,YAAY,CAAEwB,yBAAyB,CAACR,MAAM,CAAC,CAEtF;AACA,KAAM,CAAAsC,UAAU,CAAIC,KAAgB,EAAK,CACvC,GAAIrD,SAAS,GAAKqD,KAAK,CAAE,CACvBlD,gBAAgB,CAACmD,IAAI,EAAIA,IAAI,GAAK,KAAK,CAAG,MAAM,CAAG,KAAK,CAAC,CAC3D,CAAC,IAAM,CACLrD,YAAY,CAACoD,KAAK,CAAC,CACnBlD,gBAAgB,CAAC,MAAM,CAAC,CAC1B,CACAN,cAAc,CAAC,CAAC,CAAC,CACnB,CAAC,CAED,KAAM,CAAA0D,kBAAkB,CAAGA,CAACC,GAAyB,CAAEC,KAAa,GAAK,CACvEpD,UAAU,CAACiD,IAAI,EAAAI,aAAA,CAAAA,aAAA,IAAUJ,IAAI,MAAE,CAACE,GAAG,EAAGC,KAAK,EAAG,CAAC,CAC/C5D,cAAc,CAAC,CAAC,CAAC,CACnB,CAAC,CAED,KAAM,CAAA8D,YAAY,CAAGA,CAAA,GAAM,CACzBtD,UAAU,CAAC,CACTC,SAAS,CAAE,EAAE,CACbC,QAAQ,CAAE,EAAE,CACZC,MAAM,CAAE,EAAE,CACVC,WAAW,CAAE,EAAE,CACfC,SAAS,CAAE,EAAE,CACbC,YAAY,CAAE,EAAE,CAChBC,UAAU,CAAE,EACd,CAAC,CAAC,CACFf,cAAc,CAAC,CAAC,CAAC,CACnB,CAAC,CAED,KAAM,CAAA+D,YAAY,CAAGA,CAAA,GAAM,CACzB,GAAI,CACF,KAAM,CAAAC,UAAU,CAAGtF,kBAAkB,CAACuF,WAAW,CAACxC,yBAAyB,CAAC,CAC5E,KAAM,CAAAyC,IAAI,CAAG,GAAI,CAAAC,IAAI,CAAC,CAACH,UAAU,CAAC,CAAE,CAAEI,IAAI,CAAE,yBAA0B,CAAC,CAAC,CACxE,KAAM,CAAAC,IAAI,CAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC,CAExC,GAAIF,IAAI,CAACG,QAAQ,GAAKC,SAAS,CAAE,CAC/B,KAAM,CAAAC,GAAG,CAAGC,GAAG,CAACC,eAAe,CAACV,IAAI,CAAC,CACrCG,IAAI,CAACQ,YAAY,CAAC,MAAM,CAAEH,GAAG,CAAC,CAC9BL,IAAI,CAACQ,YAAY,CAAC,UAAU,kBAAAC,MAAA,CAAmB,GAAI,CAAAC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,QAAM,CAAC,CAC5FZ,IAAI,CAACa,KAAK,CAACC,UAAU,CAAG,QAAQ,CAChCb,QAAQ,CAACc,IAAI,CAACC,WAAW,CAAChB,IAAI,CAAC,CAC/BA,IAAI,CAACiB,KAAK,CAAC,CAAC,CACZhB,QAAQ,CAACc,IAAI,CAACG,WAAW,CAAClB,IAAI,CAAC,CACjC,CACF,CAAE,MAAO/C,GAAG,CAAE,CACZkE,OAAO,CAAC7F,KAAK,CAAC,gBAAgB,CAAE2B,GAAG,CAAC,CACpC1B,QAAQ,CAAC,uBAAuB,CAAC,CACnC,CACF,CAAC,CAED;AACA,KAAM,CAAA6F,cAAc,CAAG,QAAAA,CAACC,MAAc,CAAuC,IAArC,CAAAC,QAAgB,CAAAC,SAAA,CAAA3E,MAAA,IAAA2E,SAAA,MAAAnB,SAAA,CAAAmB,SAAA,IAAG,KAAK,CAC9D,MAAO,IAAI,CAAAC,IAAI,CAACC,YAAY,CAAC,OAAO,CAAE,CACpCZ,KAAK,CAAE,UAAU,CACjBS,QAAQ,CAAEA,QAAQ,CAClBI,qBAAqB,CAAE,CAAC,CACxBC,qBAAqB,CAAE,CACzB,CAAC,CAAC,CAACC,MAAM,CAACP,MAAM,CAAC,CACnB,CAAC,CAED,KAAM,CAAAQ,UAAU,CAAIC,UAAkB,EAAa,CACjD,KAAM,CAAAjE,IAAI,CAAG,GAAI,CAAA6C,IAAI,CAACoB,UAAU,CAAC,CACjC,MAAO,CAAAjE,IAAI,CAACkE,kBAAkB,CAAC,OAAO,CAAE,CACtCC,GAAG,CAAE,SAAS,CACdC,KAAK,CAAE,SAAS,CAChBC,IAAI,CAAE,SACR,CAAC,CAAC,CACJ,CAAC,CAED,KAAM,CAAAC,cAAc,CAAId,MAAc,EAAa,CACjD,GAAIA,MAAM,CAAG,CAAC,CAAE,MAAO,iBAAiB,CACxC,GAAIA,MAAM,CAAG,CAAC,CAAE,MAAO,iBAAiB,CACxC,MAAO,aAAa,CACtB,CAAC,CAED,KAAM,CAAAe,YAAY,CAAIjD,KAAgB,EAAa,CACjD,GAAIrD,SAAS,GAAKqD,KAAK,CAAE,MAAO,UAAU,CAC1C,yBAAAsB,MAAA,CAA0BzE,aAAa,EACzC,CAAC,CAED;AACA,GAAIZ,OAAO,CAAE,CACX,mBACEZ,IAAA,QAAK6H,SAAS,CAAC,wBAAwB,CAAAC,QAAA,cACrC5H,KAAA,QAAK2H,SAAS,CAAC,sBAAsB,CAAAC,QAAA,eACnC9H,IAAA,QAAK+H,KAAK,CAAC,IAAI,CAACC,MAAM,CAAC,IAAI,CAACC,OAAO,CAAC,WAAW,CAACC,IAAI,CAAC,MAAM,CAACC,MAAM,CAAC,cAAc,CAACC,WAAW,CAAC,GAAG,CAAAN,QAAA,cAC/F9H,IAAA,SAAMqI,CAAC,CAAC,4BAA4B,CAAE,CAAC,CACpC,CAAC,cACNrI,IAAA,MAAA8H,QAAA,CAAG,0BAAwB,CAAG,CAAC,EAC5B,CAAC,CACH,CAAC,CAEV,CAEA;AACA,GAAIhH,KAAK,CAAE,CACT,mBACEd,IAAA,QAAK6H,SAAS,CAAC,wBAAwB,CAAAC,QAAA,cACrC5H,KAAA,QAAK2H,SAAS,CAAC,oBAAoB,CAAAC,QAAA,eACjC5H,KAAA,QAAK6H,KAAK,CAAC,IAAI,CAACC,MAAM,CAAC,IAAI,CAACC,OAAO,CAAC,WAAW,CAACC,IAAI,CAAC,MAAM,CAACC,MAAM,CAAC,cAAc,CAACC,WAAW,CAAC,GAAG,CAAAN,QAAA,eAC/F9H,IAAA,WAAQsI,EAAE,CAAC,IAAI,CAACC,EAAE,CAAC,IAAI,CAACC,CAAC,CAAC,IAAI,CAAE,CAAC,cACjCxI,IAAA,SAAMyI,EAAE,CAAC,IAAI,CAACC,EAAE,CAAC,GAAG,CAACC,EAAE,CAAC,GAAG,CAACC,EAAE,CAAC,IAAI,CAAE,CAAC,cACtC5I,IAAA,SAAMyI,EAAE,CAAC,GAAG,CAACC,EAAE,CAAC,GAAG,CAACC,EAAE,CAAC,IAAI,CAACC,EAAE,CAAC,IAAI,CAAE,CAAC,EACnC,CAAC,cACN5I,IAAA,OAAA8H,QAAA,CAAI,oBAAkB,CAAI,CAAC,cAC3B9H,IAAA,MAAA8H,QAAA,CAAIhH,KAAK,CAAI,CAAC,cACdZ,KAAA,WACE2I,OAAO,CAAEA,CAAA,GAAM,CACb9H,QAAQ,CAAC,IAAI,CAAC,CACdoB,QAAQ,CAAC,CAAC,CACZ,CAAE,CACF0F,SAAS,CAAC,0BAA0B,CACpCxB,KAAK,CAAE,CAAEyC,SAAS,CAAE,MAAO,CAAE,CAAAhB,QAAA,eAE7B5H,KAAA,QAAK6H,KAAK,CAAC,IAAI,CAACC,MAAM,CAAC,IAAI,CAACC,OAAO,CAAC,WAAW,CAACC,IAAI,CAAC,MAAM,CAACC,MAAM,CAAC,cAAc,CAACC,WAAW,CAAC,GAAG,CAAAN,QAAA,eAC/F9H,IAAA,aAAU+I,MAAM,CAAC,kBAAkB,CAAE,CAAC,cACtC/I,IAAA,aAAU+I,MAAM,CAAC,gBAAgB,CAAE,CAAC,cACpC/I,IAAA,SAAMqI,CAAC,CAAC,qEAAqE,CAAE,CAAC,EAC7E,CAAC,QAER,EAAQ,CAAC,EACN,CAAC,CACH,CAAC,CAEV,CAEA,mBACEnI,KAAA,QAAK2H,SAAS,CAAC,wBAAwB,CAAAC,QAAA,eAErC5H,KAAA,QAAK2H,SAAS,CAAC,qBAAqB,CAAAC,QAAA,eAClC5H,KAAA,OAAI2H,SAAS,CAAC,oBAAoB,CAAAC,QAAA,eAChC5H,KAAA,QAAK6H,KAAK,CAAC,IAAI,CAACC,MAAM,CAAC,IAAI,CAACC,OAAO,CAAC,WAAW,CAACC,IAAI,CAAC,MAAM,CAACC,MAAM,CAAC,cAAc,CAACC,WAAW,CAAC,GAAG,CAAAN,QAAA,eAC/F9H,IAAA,SAAMgJ,CAAC,CAAC,GAAG,CAACC,CAAC,CAAC,GAAG,CAAClB,KAAK,CAAC,IAAI,CAACC,MAAM,CAAC,IAAI,CAACkB,EAAE,CAAC,GAAG,CAAE,CAAC,cAClDlJ,IAAA,WAAQsI,EAAE,CAAC,IAAI,CAACC,EAAE,CAAC,IAAI,CAACC,CAAC,CAAC,GAAG,CAAE,CAAC,cAChCxI,IAAA,SAAMqI,CAAC,CAAC,qBAAqB,CAAE,CAAC,EAC7B,CAAC,eAER,EAAI,CAAC,cACLnI,KAAA,QAAK2H,SAAS,CAAC,sBAAsB,CAAAC,QAAA,eACnC5H,KAAA,WACE2I,OAAO,CAAE1G,QAAS,CAClBgH,QAAQ,CAAEnI,UAAW,CACrB6G,SAAS,CAAC,0BAA0B,CAAAC,QAAA,eAEpC5H,KAAA,QAAK6H,KAAK,CAAC,IAAI,CAACC,MAAM,CAAC,IAAI,CAACC,OAAO,CAAC,WAAW,CAACC,IAAI,CAAC,MAAM,CAACC,MAAM,CAAC,cAAc,CAACC,WAAW,CAAC,GAAG,CAAAN,QAAA,eAC/F9H,IAAA,aAAU+I,MAAM,CAAC,kBAAkB,CAAE,CAAC,cACtC/I,IAAA,aAAU+I,MAAM,CAAC,gBAAgB,CAAE,CAAC,cACpC/I,IAAA,SAAMqI,CAAC,CAAC,qEAAqE,CAAE,CAAC,EAC7E,CAAC,CACLrH,UAAU,CAAG,eAAe,CAAG,SAAS,EACnC,CAAC,cACTd,KAAA,WACE2I,OAAO,CAAE3D,YAAa,CACtB2C,SAAS,CAAC,yBAAyB,CACnCsB,QAAQ,CAAEvG,yBAAyB,CAACR,MAAM,GAAK,CAAE,CAAA0F,QAAA,eAEjD5H,KAAA,QAAK6H,KAAK,CAAC,IAAI,CAACC,MAAM,CAAC,IAAI,CAACC,OAAO,CAAC,WAAW,CAACC,IAAI,CAAC,MAAM,CAACC,MAAM,CAAC,cAAc,CAACC,WAAW,CAAC,GAAG,CAAAN,QAAA,eAC/F9H,IAAA,SAAMqI,CAAC,CAAC,2CAA2C,CAAE,CAAC,cACtDrI,IAAA,aAAU+I,MAAM,CAAC,kBAAkB,CAAE,CAAC,cACtC/I,IAAA,SAAMyI,EAAE,CAAC,IAAI,CAACC,EAAE,CAAC,IAAI,CAACC,EAAE,CAAC,IAAI,CAACC,EAAE,CAAC,GAAG,CAAE,CAAC,EACpC,CAAC,aAER,EAAQ,CAAC,EACN,CAAC,EACH,CAAC,CAGLhG,yBAAyB,CAACR,MAAM,CAAG,CAAC,eACnClC,KAAA,QAAK2H,SAAS,CAAC,oBAAoB,CAAAC,QAAA,eACjC5H,KAAA,QAAK2H,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAChC9H,IAAA,QAAK6H,SAAS,CAAC,oBAAoB,CAAAC,QAAA,CAAC,YAAU,CAAK,CAAC,cACpD9H,IAAA,QAAK6H,SAAS,CAAC,oBAAoB,CAAAC,QAAA,CAAE5D,KAAK,CAACkF,SAAS,CAACC,cAAc,CAAC,CAAC,CAAM,CAAC,EACzE,CAAC,cACNnJ,KAAA,QAAK2H,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAChC9H,IAAA,QAAK6H,SAAS,CAAC,oBAAoB,CAAAC,QAAA,CAAC,gBAAc,CAAK,CAAC,cACxD9H,IAAA,QAAK6H,SAAS,CAAC,oBAAoB,CAAAC,QAAA,CAAE5D,KAAK,CAACoF,aAAa,CAAM,CAAC,EAC5D,CAAC,cACNpJ,KAAA,QAAK2H,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAChC9H,IAAA,QAAK6H,SAAS,CAAC,oBAAoB,CAAAC,QAAA,CAAC,iBAAe,CAAK,CAAC,cACzD9H,IAAA,QAAK6H,SAAS,CAAC,oBAAoB,CAAAC,QAAA,CAAElB,cAAc,CAAC1C,KAAK,CAACqF,cAAc,CAAC,CAAM,CAAC,EAC7E,CAAC,cACNrJ,KAAA,QAAK2H,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAChC9H,IAAA,QAAK6H,SAAS,CAAC,oBAAoB,CAAAC,QAAA,CAAC,iBAAe,CAAK,CAAC,cACzD9H,IAAA,QAAK6H,SAAS,CAAC,oBAAoB,CAAAC,QAAA,CAAElB,cAAc,CAAC1C,KAAK,CAACsF,cAAc,CAAC,CAAM,CAAC,EAC7E,CAAC,cACNtJ,KAAA,QAAK2H,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAChC9H,IAAA,QAAK6H,SAAS,CAAC,oBAAoB,CAAAC,QAAA,CAAC,gBAAc,CAAK,CAAC,cACxD9H,IAAA,QAAK6H,SAAS,CAAC,oBAAoB,CAAAC,QAAA,CAAElB,cAAc,CAAC1C,KAAK,CAACuF,aAAa,CAAC,CAAM,CAAC,EAC5E,CAAC,cACNvJ,KAAA,QAAK2H,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAChC9H,IAAA,QAAK6H,SAAS,CAAC,oBAAoB,CAAAC,QAAA,CAAC,YAAU,CAAK,CAAC,cACpD9H,IAAA,QAAK6H,SAAS,CAAC,oBAAoB,CAAAC,QAAA,CAChC5D,KAAK,CAACwF,SAAS,CAACC,IAAI,IAAA1D,MAAA,CAAMoB,UAAU,CAACnD,KAAK,CAACwF,SAAS,CAACC,IAAI,CAAC,QAAA1D,MAAA,CAAMoB,UAAU,CAACnD,KAAK,CAACwF,SAAS,CAACE,EAAE,CAAC,EAAK,KAAK,CACtG,CAAC,EACH,CAAC,EACH,CACN,cAGD1J,KAAA,QAAK2H,SAAS,CAAC,sBAAsB,CAAAC,QAAA,eACnC5H,KAAA,QAAK2H,SAAS,CAAC,6BAA6B,CAAAC,QAAA,eAC1C9H,IAAA,OAAI6H,SAAS,CAAC,4BAA4B,CAAAC,QAAA,CAAC,SAAO,CAAI,CAAC,cACvD9H,IAAA,WAAQ6I,OAAO,CAAE5D,YAAa,CAAC4C,SAAS,CAAC,4BAA4B,CAAAC,QAAA,CAAC,WAEtE,CAAQ,CAAC,EACN,CAAC,cACN5H,KAAA,QAAK2H,SAAS,CAAC,2BAA2B,CAAAC,QAAA,eACxC5H,KAAA,QAAK2H,SAAS,CAAC,cAAc,CAAAC,QAAA,eAC3B9H,IAAA,UAAA8H,QAAA,CAAO,SAAO,CAAO,CAAC,cACtB5H,KAAA,WACE6E,KAAK,CAAErD,OAAO,CAACE,SAAU,CACzBiI,QAAQ,CAAGC,CAAC,EAAKjF,kBAAkB,CAAC,WAAW,CAAEiF,CAAC,CAACC,MAAM,CAAChF,KAAK,CAAE,CAAA+C,QAAA,eAEjE9H,IAAA,WAAQ+E,KAAK,CAAC,EAAE,CAAA+C,QAAA,CAAC,cAAY,CAAQ,CAAC,CACrCpH,YAAY,CAACsJ,GAAG,CAACC,OAAO,eACvB/J,KAAA,WAAyB6E,KAAK,CAAEkF,OAAO,CAACC,EAAG,CAAApC,QAAA,EACxCmC,OAAO,CAACE,IAAI,CAAC,IAAE,CAACF,OAAO,CAACG,aAAa,CAAC,GACzC,GAFaH,OAAO,CAACC,EAEb,CACT,CAAC,EACI,CAAC,EACN,CAAC,cACNhK,KAAA,QAAK2H,SAAS,CAAC,cAAc,CAAAC,QAAA,eAC3B9H,IAAA,UAAA8H,QAAA,CAAO,WAAS,CAAO,CAAC,cACxB9H,IAAA,UACEuF,IAAI,CAAC,MAAM,CACXR,KAAK,CAAErD,OAAO,CAACG,QAAS,CACxBgI,QAAQ,CAAGC,CAAC,EAAKjF,kBAAkB,CAAC,UAAU,CAAEiF,CAAC,CAACC,MAAM,CAAChF,KAAK,CAAE,CACjE,CAAC,EACC,CAAC,cACN7E,KAAA,QAAK2H,SAAS,CAAC,cAAc,CAAAC,QAAA,eAC3B9H,IAAA,UAAA8H,QAAA,CAAO,SAAO,CAAO,CAAC,cACtB9H,IAAA,UACEuF,IAAI,CAAC,MAAM,CACXR,KAAK,CAAErD,OAAO,CAACI,MAAO,CACtB+H,QAAQ,CAAGC,CAAC,EAAKjF,kBAAkB,CAAC,QAAQ,CAAEiF,CAAC,CAACC,MAAM,CAAChF,KAAK,CAAE,CAC/D,CAAC,EACC,CAAC,cACN7E,KAAA,QAAK2H,SAAS,CAAC,cAAc,CAAAC,QAAA,eAC3B9H,IAAA,UAAA8H,QAAA,CAAO,aAAW,CAAO,CAAC,cAC1B9H,IAAA,UACEuF,IAAI,CAAC,QAAQ,CACb8E,IAAI,CAAC,MAAM,CACXC,WAAW,CAAC,MAAM,CAClBvF,KAAK,CAAErD,OAAO,CAACK,WAAY,CAC3B8H,QAAQ,CAAGC,CAAC,EAAKjF,kBAAkB,CAAC,aAAa,CAAEiF,CAAC,CAACC,MAAM,CAAChF,KAAK,CAAE,CACpE,CAAC,EACC,CAAC,cACN7E,KAAA,QAAK2H,SAAS,CAAC,cAAc,CAAAC,QAAA,eAC3B9H,IAAA,UAAA8H,QAAA,CAAO,aAAW,CAAO,CAAC,cAC1B9H,IAAA,UACEuF,IAAI,CAAC,QAAQ,CACb8E,IAAI,CAAC,MAAM,CACXC,WAAW,CAAC,MAAM,CAClBvF,KAAK,CAAErD,OAAO,CAACM,SAAU,CACzB6H,QAAQ,CAAGC,CAAC,EAAKjF,kBAAkB,CAAC,WAAW,CAAEiF,CAAC,CAACC,MAAM,CAAChF,KAAK,CAAE,CAClE,CAAC,EACC,CAAC,cACN7E,KAAA,QAAK2H,SAAS,CAAC,cAAc,CAAAC,QAAA,eAC3B9H,IAAA,UAAA8H,QAAA,CAAO,cAAY,CAAO,CAAC,cAC3B9H,IAAA,UACEuF,IAAI,CAAC,QAAQ,CACb8E,IAAI,CAAC,MAAM,CACXC,WAAW,CAAC,MAAM,CAClBvF,KAAK,CAAErD,OAAO,CAACO,YAAa,CAC5B4H,QAAQ,CAAGC,CAAC,EAAKjF,kBAAkB,CAAC,cAAc,CAAEiF,CAAC,CAACC,MAAM,CAAChF,KAAK,CAAE,CACrE,CAAC,EACC,CAAC,EACH,CAAC,EACH,CAAC,CAGLnC,yBAAyB,CAACR,MAAM,GAAK,CAAC,cACrClC,KAAA,QAAK2H,SAAS,CAAC,oBAAoB,CAAAC,QAAA,eACjC5H,KAAA,QAAK6H,KAAK,CAAC,IAAI,CAACC,MAAM,CAAC,IAAI,CAACC,OAAO,CAAC,WAAW,CAACC,IAAI,CAAC,MAAM,CAACC,MAAM,CAAC,cAAc,CAACC,WAAW,CAAC,GAAG,CAAAN,QAAA,eAC/F9H,IAAA,SAAMgJ,CAAC,CAAC,GAAG,CAACC,CAAC,CAAC,GAAG,CAAClB,KAAK,CAAC,IAAI,CAACC,MAAM,CAAC,IAAI,CAACkB,EAAE,CAAC,GAAG,CAAE,CAAC,cAClDlJ,IAAA,WAAQsI,EAAE,CAAC,IAAI,CAACC,EAAE,CAAC,IAAI,CAACC,CAAC,CAAC,GAAG,CAAE,CAAC,cAChCxI,IAAA,SAAMqI,CAAC,CAAC,qBAAqB,CAAE,CAAC,EAC7B,CAAC,cACNrI,IAAA,OAAA8H,QAAA,CAAI,iBAAe,CAAI,CAAC,cACxB9H,IAAA,MAAA8H,QAAA,CAAG,gFAA8E,CAAG,CAAC,EAClF,CAAC,cAEN5H,KAAA,QAAK2H,SAAS,CAAC,8BAA8B,CAAAC,QAAA,eAC3C5H,KAAA,UAAO2H,SAAS,CAAC,oBAAoB,CAAAC,QAAA,eACnC9H,IAAA,UAAA8H,QAAA,cACE5H,KAAA,OAAA4H,QAAA,eACE9H,IAAA,OAAI6H,SAAS,CAAED,YAAY,CAAC,MAAM,CAAE,CAACiB,OAAO,CAAEA,CAAA,GAAMnE,UAAU,CAAC,MAAM,CAAE,CAAAoD,QAAA,CAAC,MAExE,CAAI,CAAC,cACL9H,IAAA,OAAI6H,SAAS,CAAED,YAAY,CAAC,aAAa,CAAE,CAACiB,OAAO,CAAEA,CAAA,GAAMnE,UAAU,CAAC,aAAa,CAAE,CAAAoD,QAAA,CAAC,SAEtF,CAAI,CAAC,cACL9H,IAAA,OAAI6H,SAAS,CAAED,YAAY,CAAC,gBAAgB,CAAE,CAACiB,OAAO,CAAEA,CAAA,GAAMnE,UAAU,CAAC,gBAAgB,CAAE,CAAAoD,QAAA,CAAC,iBAE5F,CAAI,CAAC,cACL9H,IAAA,OAAI6H,SAAS,CAAED,YAAY,CAAC,gBAAgB,CAAE,CAACiB,OAAO,CAAEA,CAAA,GAAMnE,UAAU,CAAC,gBAAgB,CAAE,CAAAoD,QAAA,CAAC,iBAE5F,CAAI,CAAC,cACL9H,IAAA,OAAI6H,SAAS,CAAED,YAAY,CAAC,eAAe,CAAE,CAACiB,OAAO,CAAEA,CAAA,GAAMnE,UAAU,CAAC,eAAe,CAAE,CAAAoD,QAAA,CAAC,gBAE1F,CAAI,CAAC,cACL9H,IAAA,OAAI6H,SAAS,CAAED,YAAY,CAAC,kBAAkB,CAAE,CAACiB,OAAO,CAAEA,CAAA,GAAMnE,UAAU,CAAC,kBAAkB,CAAE,CAAAoD,QAAA,CAAC,cAEhG,CAAI,CAAC,cACL9H,IAAA,OAAA8H,QAAA,CAAI,uBAAqB,CAAI,CAAC,EAC5B,CAAC,CACA,CAAC,cACR9H,IAAA,UAAA8H,QAAA,CACGhE,iBAAiB,CAACkG,GAAG,CAAEO,OAAO,eAC7BrK,KAAA,OAAA4H,QAAA,eACE9H,IAAA,OAAI6H,SAAS,CAAC,WAAW,CAAAC,QAAA,CAAET,UAAU,CAACkD,OAAO,CAAClH,IAAI,CAAC,CAAK,CAAC,cACzDrD,IAAA,OAAA8H,QAAA,cACE5H,KAAA,QAAK2H,SAAS,CAAC,cAAc,CAAAC,QAAA,eAC3B9H,IAAA,QAAK6H,SAAS,CAAC,cAAc,CAAAC,QAAA,CAAEyC,OAAO,CAACjH,WAAW,CAAM,CAAC,cACzDpD,KAAA,QAAK2H,SAAS,CAAC,iBAAiB,CAAAC,QAAA,EAC7ByC,OAAO,CAACH,aAAa,CAAC,UAAG,CAACG,OAAO,CAACC,QAAQ,EACxC,CAAC,EACH,CAAC,CACJ,CAAC,cACLxK,IAAA,OAAI6H,SAAS,CAAC,gBAAgB,CAAAC,QAAA,CAC3BlB,cAAc,CAAC2D,OAAO,CAAC/G,cAAc,CAAE+G,OAAO,CAACzD,QAAQ,CAAC,CACvD,CAAC,cACL9G,IAAA,OAAI6H,SAAS,CAAC,gBAAgB,CAAAC,QAAA,CAC3BlB,cAAc,CAAC2D,OAAO,CAAChH,cAAc,CAAEgH,OAAO,CAACzD,QAAQ,CAAC,CACvD,CAAC,cACL9G,IAAA,OAAI6H,SAAS,mBAAA5B,MAAA,CAAoB0B,cAAc,CAAC4C,OAAO,CAAC9G,aAAa,CAAC,CAAG,CAAAqE,QAAA,CACtElB,cAAc,CAAC2D,OAAO,CAAC9G,aAAa,CAAE8G,OAAO,CAACzD,QAAQ,CAAC,CACtD,CAAC,cACL9G,IAAA,OAAIqG,KAAK,CAAE,CAAEoE,SAAS,CAAE,QAAS,CAAE,CAAA3C,QAAA,CAChCyC,OAAO,CAAC7G,gBAAgB,CACvB,CAAC,cACL1D,IAAA,OAAIqG,KAAK,CAAE,CAAEoE,SAAS,CAAE,QAAQ,CAAEC,UAAU,CAAE,WAAY,CAAE,CAAA5C,QAAA,CACzDyC,OAAO,CAACI,mBAAmB,CAC1B,CAAC,GAxBEJ,OAAO,CAACL,EAyBb,CACL,CAAC,CACG,CAAC,EACH,CAAC,CAGP9F,UAAU,CAAG,CAAC,eACblE,KAAA,QAAK2H,SAAS,CAAC,yBAAyB,CAAAC,QAAA,eACtC5H,KAAA,QAAK2H,SAAS,CAAC,iBAAiB,CAAAC,QAAA,EAAC,UACvB,CAACvD,SAAS,CAAC8E,cAAc,CAAC,CAAC,CAAC,MAAI,CAAC7E,OAAO,CAAC6E,cAAc,CAAC,CAAC,CAAC,MAAI,CAACzG,yBAAyB,CAACR,MAAM,CAACiH,cAAc,CAAC,CAAC,CAAC,UAC3H,EAAK,CAAC,cACNnJ,KAAA,QAAK2H,SAAS,CAAC,qBAAqB,CAAAC,QAAA,eAClC9H,IAAA,WACE+E,KAAK,CAAE3D,YAAa,CACpByI,QAAQ,CAAGC,CAAC,EAAK,CACfzI,eAAe,CAACwC,MAAM,CAACiG,CAAC,CAACC,MAAM,CAAChF,KAAK,CAAC,CAAC,CACvC5D,cAAc,CAAC,CAAC,CAAC,CACnB,CAAE,CACF0G,SAAS,CAAC,mBAAmB,CAAAC,QAAA,CAE5B3H,sBAAsB,CAAC6J,GAAG,CAACY,IAAI,eAC9B1K,KAAA,WAAmB6E,KAAK,CAAE6F,IAAK,CAAA9C,QAAA,EAAE8C,IAAI,CAAC,WAAS,GAAlCA,IAA0C,CACxD,CAAC,CACI,CAAC,cACT1K,KAAA,QAAK2H,SAAS,CAAC,oBAAoB,CAAAC,QAAA,eACjC9H,IAAA,WACE6I,OAAO,CAAEA,CAAA,GAAM1H,cAAc,CAAC,CAAC,CAAE,CACjCgI,QAAQ,CAAEjI,WAAW,GAAK,CAAE,CAC5B2G,SAAS,CAAC,gBAAgB,CAAAC,QAAA,CAC3B,OAED,CAAQ,CAAC,cACT9H,IAAA,WACE6I,OAAO,CAAEA,CAAA,GAAM1H,cAAc,CAACyD,IAAI,EAAIP,IAAI,CAACwG,GAAG,CAAC,CAAC,CAAEjG,IAAI,CAAG,CAAC,CAAC,CAAE,CAC7DuE,QAAQ,CAAEjI,WAAW,GAAK,CAAE,CAC5B2G,SAAS,CAAC,gBAAgB,CAAAC,QAAA,CAC3B,UAED,CAAQ,CAAC,cACT5H,KAAA,SAAM2H,SAAS,CAAC,uBAAuB,CAAAC,QAAA,EACpC5G,WAAW,CAAC,MAAI,CAACkD,UAAU,EACxB,CAAC,cACPpE,IAAA,WACE6I,OAAO,CAAEA,CAAA,GAAM1H,cAAc,CAACyD,IAAI,EAAIP,IAAI,CAACI,GAAG,CAACL,UAAU,CAAEQ,IAAI,CAAG,CAAC,CAAC,CAAE,CACtEuE,QAAQ,CAAEjI,WAAW,GAAKkD,UAAW,CACrCyD,SAAS,CAAC,gBAAgB,CAAAC,QAAA,CAC3B,MAED,CAAQ,CAAC,cACT9H,IAAA,WACE6I,OAAO,CAAEA,CAAA,GAAM1H,cAAc,CAACiD,UAAU,CAAE,CAC1C+E,QAAQ,CAAEjI,WAAW,GAAKkD,UAAW,CACrCyD,SAAS,CAAC,gBAAgB,CAAAC,QAAA,CAC3B,MAED,CAAQ,CAAC,EACN,CAAC,EACH,CAAC,EACH,CACN,EACE,CACN,EACE,CAAC,CAEV,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}