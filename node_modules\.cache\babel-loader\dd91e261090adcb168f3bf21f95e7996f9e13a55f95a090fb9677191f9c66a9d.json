{"ast": null, "code": "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { AvgPool3DGrad, backend_util, buffer } from '@tensorflow/tfjs-core';\nimport { assertNotComplex } from '../cpu_util';\nexport function avgPool3DGrad(args) {\n  const {\n    inputs,\n    backend,\n    attrs\n  } = args;\n  const {\n    dy,\n    input\n  } = inputs;\n  const {\n    filterSize,\n    strides,\n    pad,\n    dimRoundingMode\n  } = attrs;\n  assertNotComplex([dy, input], 'avgPool3DGrad');\n  const convInfo = backend_util.computePool3DInfo(input.shape, filterSize, strides, 1 /* dilations */, pad, dimRoundingMode);\n  const strideDepth = convInfo.strideDepth;\n  const strideHeight = convInfo.strideHeight;\n  const strideWidth = convInfo.strideWidth;\n  const filterDepth = convInfo.filterDepth;\n  const filterHeight = convInfo.filterHeight;\n  const filterWidth = convInfo.filterWidth;\n  const dilationDepth = convInfo.dilationDepth;\n  const dilationHeight = convInfo.dilationHeight;\n  const dilationWidth = convInfo.dilationWidth;\n  const effectiveFilterDepth = convInfo.effectiveFilterDepth;\n  const effectiveFilterHeight = convInfo.effectiveFilterHeight;\n  const effectiveFilterWidth = convInfo.effectiveFilterWidth;\n  const padFront = effectiveFilterDepth - 1 - convInfo.padInfo.front;\n  const padLeft = effectiveFilterWidth - 1 - convInfo.padInfo.left;\n  const padTop = effectiveFilterHeight - 1 - convInfo.padInfo.top;\n  const dx = buffer(input.shape, 'float32');\n  const avgMultiplier = 1 / (filterDepth * filterHeight * filterWidth);\n  const dyBuf = backend.bufferSync(dy);\n  for (let batch = 0; batch < convInfo.batchSize; ++batch) {\n    for (let channel = 0; channel < convInfo.inChannels; ++channel) {\n      for (let dxDepth = 0; dxDepth < convInfo.inDepth; ++dxDepth) {\n        for (let dxRow = 0; dxRow < convInfo.inHeight; ++dxRow) {\n          for (let dxCol = 0; dxCol < convInfo.inWidth; ++dxCol) {\n            // Shader code begins.\n            const dyDepthCorner = dxDepth - padFront;\n            const dyRowCorner = dxRow - padTop;\n            const dyColCorner = dxCol - padLeft;\n            let dotProd = 0;\n            for (let wDepth = 0; wDepth < effectiveFilterDepth; wDepth += dilationDepth) {\n              const dyDepth = (dyDepthCorner + wDepth) / strideDepth;\n              if (dyDepth < 0 || dyDepth >= convInfo.outDepth || Math.floor(dyDepth) !== dyDepth) {\n                continue;\n              }\n              for (let wRow = 0; wRow < effectiveFilterHeight; wRow += dilationHeight) {\n                const dyRow = (dyRowCorner + wRow) / strideHeight;\n                if (dyRow < 0 || dyRow >= convInfo.outHeight || Math.floor(dyRow) !== dyRow) {\n                  continue;\n                }\n                for (let wCol = 0; wCol < effectiveFilterWidth; wCol += dilationWidth) {\n                  const dyCol = (dyColCorner + wCol) / strideWidth;\n                  if (dyCol < 0 || dyCol >= convInfo.outWidth || Math.floor(dyCol) !== dyCol) {\n                    continue;\n                  }\n                  const pixel = dyBuf.get(batch, dyDepth, dyRow, dyCol, channel);\n                  dotProd += pixel;\n                }\n              }\n            }\n            dx.set(dotProd * avgMultiplier, batch, dxDepth, dxRow, dxCol, channel);\n          }\n        }\n      }\n    }\n  }\n  return backend.makeTensorInfo(dx.shape, dx.dtype, dx.values);\n}\nexport const avgPool3DGradConfig = {\n  kernelName: AvgPool3DGrad,\n  backendName: 'cpu',\n  kernelFunc: avgPool3DGrad\n};", "map": {"version": 3, "names": ["AvgPool3DGrad", "backend_util", "buffer", "assertNotComplex", "avgPool3DGrad", "args", "inputs", "backend", "attrs", "dy", "input", "filterSize", "strides", "pad", "dimRoundingMode", "convInfo", "computePool3DInfo", "shape", "<PERSON><PERSON><PERSON>h", "strideHeight", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "filterHeight", "filterWidth", "dilationDepth", "dilationHeight", "dilationWidth", "effectiveFilterDepth", "effectiveFilterHeight", "effectiveFilterWidth", "padFront", "padInfo", "front", "padLeft", "left", "padTop", "top", "dx", "avgMultiplier", "dyBuf", "bufferSync", "batch", "batchSize", "channel", "inChannels", "dx<PERSON><PERSON>h", "inDepth", "dxRow", "inHeight", "dxCol", "inWidth", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "d<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dy<PERSON><PERSON><PERSON><PERSON><PERSON>", "dotProd", "wDepth", "d<PERSON><PERSON><PERSON><PERSON>", "outDepth", "Math", "floor", "wRow", "dyRow", "outHeight", "wCol", "dyCol", "outWidth", "pixel", "get", "set", "makeTensorInfo", "dtype", "values", "avgPool3DGradConfig", "kernelName", "backendName", "kernelFunc"], "sources": ["C:\\tfjs-backend-cpu\\src\\kernels\\AvgPool3DGrad.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {AvgPool3DGrad, AvgPool3DGradAttrs, AvgPool3DGradInputs, backend_util, buffer, KernelConfig, KernelFunc, Rank, TensorInfo} from '@tensorflow/tfjs-core';\n\nimport {MathBackendCPU} from '../backend_cpu';\nimport {assertNotComplex} from '../cpu_util';\n\nexport function avgPool3DGrad(args: {\n  inputs: AvgPool3DGradInputs,\n  backend: MathBackendCPU,\n  attrs: AvgPool3DGradAttrs\n}): TensorInfo {\n  const {inputs, backend, attrs} = args;\n  const {dy, input} = inputs;\n  const {filterSize, strides, pad, dimRoundingMode} = attrs;\n\n  assertNotComplex([dy, input], 'avgPool3DGrad');\n\n  const convInfo = backend_util.computePool3DInfo(\n      input.shape as [number, number, number, number, number], filterSize,\n      strides, 1 /* dilations */, pad, dimRoundingMode);\n\n  const strideDepth = convInfo.strideDepth;\n  const strideHeight = convInfo.strideHeight;\n  const strideWidth = convInfo.strideWidth;\n  const filterDepth = convInfo.filterDepth;\n  const filterHeight = convInfo.filterHeight;\n  const filterWidth = convInfo.filterWidth;\n  const dilationDepth = convInfo.dilationDepth;\n  const dilationHeight = convInfo.dilationHeight;\n  const dilationWidth = convInfo.dilationWidth;\n  const effectiveFilterDepth = convInfo.effectiveFilterDepth;\n  const effectiveFilterHeight = convInfo.effectiveFilterHeight;\n  const effectiveFilterWidth = convInfo.effectiveFilterWidth;\n  const padFront = effectiveFilterDepth - 1 - convInfo.padInfo.front;\n  const padLeft = effectiveFilterWidth - 1 - convInfo.padInfo.left;\n  const padTop = effectiveFilterHeight - 1 - convInfo.padInfo.top;\n  const dx = buffer(input.shape, 'float32');\n\n  const avgMultiplier = 1 / (filterDepth * filterHeight * filterWidth);\n\n  const dyBuf = backend.bufferSync<Rank, 'float32'>(dy);\n\n  for (let batch = 0; batch < convInfo.batchSize; ++batch) {\n    for (let channel = 0; channel < convInfo.inChannels; ++channel) {\n      for (let dxDepth = 0; dxDepth < convInfo.inDepth; ++dxDepth) {\n        for (let dxRow = 0; dxRow < convInfo.inHeight; ++dxRow) {\n          for (let dxCol = 0; dxCol < convInfo.inWidth; ++dxCol) {\n            // Shader code begins.\n            const dyDepthCorner = dxDepth - padFront;\n            const dyRowCorner = dxRow - padTop;\n            const dyColCorner = dxCol - padLeft;\n            let dotProd = 0;\n            for (let wDepth = 0; wDepth < effectiveFilterDepth;\n                 wDepth += dilationDepth) {\n              const dyDepth = (dyDepthCorner + wDepth) / strideDepth;\n              if (dyDepth < 0 || dyDepth >= convInfo.outDepth ||\n                  Math.floor(dyDepth) !== dyDepth) {\n                continue;\n              }\n              for (let wRow = 0; wRow < effectiveFilterHeight;\n                   wRow += dilationHeight) {\n                const dyRow = (dyRowCorner + wRow) / strideHeight;\n                if (dyRow < 0 || dyRow >= convInfo.outHeight ||\n                    Math.floor(dyRow) !== dyRow) {\n                  continue;\n                }\n                for (let wCol = 0; wCol < effectiveFilterWidth;\n                     wCol += dilationWidth) {\n                  const dyCol = (dyColCorner + wCol) / strideWidth;\n                  if (dyCol < 0 || dyCol >= convInfo.outWidth ||\n                      Math.floor(dyCol) !== dyCol) {\n                    continue;\n                  }\n\n                  const pixel =\n                      dyBuf.get(batch, dyDepth, dyRow, dyCol, channel);\n                  dotProd += pixel;\n                }\n              }\n            }\n            dx.set(\n                dotProd * avgMultiplier, batch, dxDepth, dxRow, dxCol, channel);\n          }\n        }\n      }\n    }\n  }\n\n  return backend.makeTensorInfo(dx.shape, dx.dtype, dx.values);\n}\n\nexport const avgPool3DGradConfig: KernelConfig = {\n  kernelName: AvgPool3DGrad,\n  backendName: 'cpu',\n  kernelFunc: avgPool3DGrad as unknown as KernelFunc\n};\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAiBA,SAAQA,aAAa,EAA2CC,YAAY,EAAEC,MAAM,QAAmD,uBAAuB;AAG9J,SAAQC,gBAAgB,QAAO,aAAa;AAE5C,OAAM,SAAUC,aAAaA,CAACC,IAI7B;EACC,MAAM;IAACC,MAAM;IAAEC,OAAO;IAAEC;EAAK,CAAC,GAAGH,IAAI;EACrC,MAAM;IAACI,EAAE;IAAEC;EAAK,CAAC,GAAGJ,MAAM;EAC1B,MAAM;IAACK,UAAU;IAAEC,OAAO;IAAEC,GAAG;IAAEC;EAAe,CAAC,GAAGN,KAAK;EAEzDL,gBAAgB,CAAC,CAACM,EAAE,EAAEC,KAAK,CAAC,EAAE,eAAe,CAAC;EAE9C,MAAMK,QAAQ,GAAGd,YAAY,CAACe,iBAAiB,CAC3CN,KAAK,CAACO,KAAiD,EAAEN,UAAU,EACnEC,OAAO,EAAE,CAAC,CAAC,iBAAiBC,GAAG,EAAEC,eAAe,CAAC;EAErD,MAAMI,WAAW,GAAGH,QAAQ,CAACG,WAAW;EACxC,MAAMC,YAAY,GAAGJ,QAAQ,CAACI,YAAY;EAC1C,MAAMC,WAAW,GAAGL,QAAQ,CAACK,WAAW;EACxC,MAAMC,WAAW,GAAGN,QAAQ,CAACM,WAAW;EACxC,MAAMC,YAAY,GAAGP,QAAQ,CAACO,YAAY;EAC1C,MAAMC,WAAW,GAAGR,QAAQ,CAACQ,WAAW;EACxC,MAAMC,aAAa,GAAGT,QAAQ,CAACS,aAAa;EAC5C,MAAMC,cAAc,GAAGV,QAAQ,CAACU,cAAc;EAC9C,MAAMC,aAAa,GAAGX,QAAQ,CAACW,aAAa;EAC5C,MAAMC,oBAAoB,GAAGZ,QAAQ,CAACY,oBAAoB;EAC1D,MAAMC,qBAAqB,GAAGb,QAAQ,CAACa,qBAAqB;EAC5D,MAAMC,oBAAoB,GAAGd,QAAQ,CAACc,oBAAoB;EAC1D,MAAMC,QAAQ,GAAGH,oBAAoB,GAAG,CAAC,GAAGZ,QAAQ,CAACgB,OAAO,CAACC,KAAK;EAClE,MAAMC,OAAO,GAAGJ,oBAAoB,GAAG,CAAC,GAAGd,QAAQ,CAACgB,OAAO,CAACG,IAAI;EAChE,MAAMC,MAAM,GAAGP,qBAAqB,GAAG,CAAC,GAAGb,QAAQ,CAACgB,OAAO,CAACK,GAAG;EAC/D,MAAMC,EAAE,GAAGnC,MAAM,CAACQ,KAAK,CAACO,KAAK,EAAE,SAAS,CAAC;EAEzC,MAAMqB,aAAa,GAAG,CAAC,IAAIjB,WAAW,GAAGC,YAAY,GAAGC,WAAW,CAAC;EAEpE,MAAMgB,KAAK,GAAGhC,OAAO,CAACiC,UAAU,CAAkB/B,EAAE,CAAC;EAErD,KAAK,IAAIgC,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAG1B,QAAQ,CAAC2B,SAAS,EAAE,EAAED,KAAK,EAAE;IACvD,KAAK,IAAIE,OAAO,GAAG,CAAC,EAAEA,OAAO,GAAG5B,QAAQ,CAAC6B,UAAU,EAAE,EAAED,OAAO,EAAE;MAC9D,KAAK,IAAIE,OAAO,GAAG,CAAC,EAAEA,OAAO,GAAG9B,QAAQ,CAAC+B,OAAO,EAAE,EAAED,OAAO,EAAE;QAC3D,KAAK,IAAIE,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAGhC,QAAQ,CAACiC,QAAQ,EAAE,EAAED,KAAK,EAAE;UACtD,KAAK,IAAIE,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAGlC,QAAQ,CAACmC,OAAO,EAAE,EAAED,KAAK,EAAE;YACrD;YACA,MAAME,aAAa,GAAGN,OAAO,GAAGf,QAAQ;YACxC,MAAMsB,WAAW,GAAGL,KAAK,GAAGZ,MAAM;YAClC,MAAMkB,WAAW,GAAGJ,KAAK,GAAGhB,OAAO;YACnC,IAAIqB,OAAO,GAAG,CAAC;YACf,KAAK,IAAIC,MAAM,GAAG,CAAC,EAAEA,MAAM,GAAG5B,oBAAoB,EAC7C4B,MAAM,IAAI/B,aAAa,EAAE;cAC5B,MAAMgC,OAAO,GAAG,CAACL,aAAa,GAAGI,MAAM,IAAIrC,WAAW;cACtD,IAAIsC,OAAO,GAAG,CAAC,IAAIA,OAAO,IAAIzC,QAAQ,CAAC0C,QAAQ,IAC3CC,IAAI,CAACC,KAAK,CAACH,OAAO,CAAC,KAAKA,OAAO,EAAE;gBACnC;;cAEF,KAAK,IAAII,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAGhC,qBAAqB,EAC1CgC,IAAI,IAAInC,cAAc,EAAE;gBAC3B,MAAMoC,KAAK,GAAG,CAACT,WAAW,GAAGQ,IAAI,IAAIzC,YAAY;gBACjD,IAAI0C,KAAK,GAAG,CAAC,IAAIA,KAAK,IAAI9C,QAAQ,CAAC+C,SAAS,IACxCJ,IAAI,CAACC,KAAK,CAACE,KAAK,CAAC,KAAKA,KAAK,EAAE;kBAC/B;;gBAEF,KAAK,IAAIE,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAGlC,oBAAoB,EACzCkC,IAAI,IAAIrC,aAAa,EAAE;kBAC1B,MAAMsC,KAAK,GAAG,CAACX,WAAW,GAAGU,IAAI,IAAI3C,WAAW;kBAChD,IAAI4C,KAAK,GAAG,CAAC,IAAIA,KAAK,IAAIjD,QAAQ,CAACkD,QAAQ,IACvCP,IAAI,CAACC,KAAK,CAACK,KAAK,CAAC,KAAKA,KAAK,EAAE;oBAC/B;;kBAGF,MAAME,KAAK,GACP3B,KAAK,CAAC4B,GAAG,CAAC1B,KAAK,EAAEe,OAAO,EAAEK,KAAK,EAAEG,KAAK,EAAErB,OAAO,CAAC;kBACpDW,OAAO,IAAIY,KAAK;;;;YAItB7B,EAAE,CAAC+B,GAAG,CACFd,OAAO,GAAGhB,aAAa,EAAEG,KAAK,EAAEI,OAAO,EAAEE,KAAK,EAAEE,KAAK,EAAEN,OAAO,CAAC;;;;;;EAO7E,OAAOpC,OAAO,CAAC8D,cAAc,CAAChC,EAAE,CAACpB,KAAK,EAAEoB,EAAE,CAACiC,KAAK,EAAEjC,EAAE,CAACkC,MAAM,CAAC;AAC9D;AAEA,OAAO,MAAMC,mBAAmB,GAAiB;EAC/CC,UAAU,EAAEzE,aAAa;EACzB0E,WAAW,EAAE,KAAK;EAClBC,UAAU,EAAEvE;CACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}