{"ast": null, "code": "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { IsFinite } from '../kernel_names';\nimport { zerosLike } from '../ops/zeros_like';\nexport const isFiniteGradConfig = {\n  kernelName: IsFinite,\n  gradFunc: dy => {\n    // TODO(nsthorat): Let gradients be null for cases where we want to stop\n    // backpropgation.\n    return {\n      x: () => zerosLike(dy)\n    };\n  }\n};", "map": {"version": 3, "names": ["IsFinite", "zerosLike", "isFiniteGradConfig", "kernelName", "grad<PERSON>unc", "dy", "x"], "sources": ["C:\\tfjs-core\\src\\gradients\\IsFinite_grad.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {IsFinite} from '../kernel_names';\nimport {GradConfig} from '../kernel_registry';\nimport {zerosLike} from '../ops/zeros_like';\nimport {Tensor} from '../tensor';\n\nexport const isFiniteGradConfig: GradConfig = {\n  kernelName: IsFinite,\n  gradFunc: (dy: Tensor) => {\n    // TODO(nsthorat): Let gradients be null for cases where we want to stop\n    // backpropgation.\n    return {x: () => zerosLike(dy)};\n  }\n};\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAiBA,SAAQA,QAAQ,QAAO,iBAAiB;AAExC,SAAQC,SAAS,QAAO,mBAAmB;AAG3C,OAAO,MAAMC,kBAAkB,GAAe;EAC5CC,UAAU,EAAEH,QAAQ;EACpBI,QAAQ,EAAGC,EAAU,IAAI;IACvB;IACA;IACA,OAAO;MAACC,CAAC,EAAEA,CAAA,KAAML,SAAS,CAACI,EAAE;IAAC,CAAC;EACjC;CACD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}