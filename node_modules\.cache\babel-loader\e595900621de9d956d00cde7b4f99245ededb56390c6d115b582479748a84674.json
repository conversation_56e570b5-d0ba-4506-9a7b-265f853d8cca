{"ast": null, "code": "/**\n * @license\n * Copyright 2022 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { RaggedTensorToTensor } from '@tensorflow/tfjs-core';\nimport { raggedTensorToTensorImpl } from './RaggedTensorToTensor_impl';\nexport function raggedTensorToTensor(args) {\n  const {\n    inputs,\n    backend,\n    attrs\n  } = args;\n  const {\n    shape,\n    values,\n    defaultValue,\n    rowPartitionTensors\n  } = inputs;\n  const {\n    rowPartitionTypes\n  } = attrs;\n  const $shape = backend.data.get(shape.dataId).values;\n  const $values = backend.data.get(values.dataId).values;\n  const $defaultValue = backend.data.get(defaultValue.dataId).values;\n  const $rowPartitionValues = rowPartitionTensors.map(t => backend.data.get(t.dataId).values);\n  const rowPartitionValuesShapes = rowPartitionTensors.map(t => t.shape);\n  const [outputShape, output] = raggedTensorToTensorImpl($shape, shape.shape, $values, values.shape, values.dtype, $defaultValue, defaultValue.shape, $rowPartitionValues, rowPartitionValuesShapes, rowPartitionTypes);\n  return backend.makeTensorInfo(outputShape, values.dtype, output);\n}\nexport const raggedTensorToTensorConfig = {\n  kernelName: RaggedTensorToTensor,\n  backendName: 'cpu',\n  kernelFunc: raggedTensorToTensor\n};", "map": {"version": 3, "names": ["RaggedTensorToTensor", "raggedTensorToTensorImpl", "raggedTensorToTensor", "args", "inputs", "backend", "attrs", "shape", "values", "defaultValue", "rowPartitionTensors", "rowPartitionTypes", "$shape", "data", "get", "dataId", "$values", "$defaultValue", "$rowPartitionValues", "map", "t", "rowPartitionValuesShapes", "outputShape", "output", "dtype", "makeTensorInfo", "raggedTensorToTensorConfig", "kernelName", "backendName", "kernelFunc"], "sources": ["C:\\tfjs-backend-cpu\\src\\kernels\\RaggedTensorToTensor.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2022 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {KernelConfig, KernelFunc, RaggedTensorToTensor, RaggedTensorToTensorAttrs, RaggedTensorToTensorInputs, TensorInfo, TypedArray} from '@tensorflow/tfjs-core';\n\nimport {MathBackendCPU} from '../backend_cpu';\n\nimport {raggedTensorToTensorImpl} from './RaggedTensorToTensor_impl';\n\nexport function raggedTensorToTensor(args: {\n  inputs: RaggedTensorToTensorInputs,\n  backend: MathBackendCPU,\n  attrs: RaggedTensorToTensorAttrs\n}): TensorInfo {\n  const {inputs, backend, attrs} = args;\n  const {shape, values, defaultValue, rowPartitionTensors} = inputs;\n  const {rowPartitionTypes} = attrs;\n\n  const $shape = backend.data.get(shape.dataId).values as TypedArray;\n  const $values = backend.data.get(values.dataId).values as TypedArray;\n  const $defaultValue =\n      backend.data.get(defaultValue.dataId).values as TypedArray;\n  const $rowPartitionValues = rowPartitionTensors.map(\n      t => backend.data.get(t.dataId).values as TypedArray);\n  const rowPartitionValuesShapes = rowPartitionTensors.map(t => t.shape);\n\n  const [outputShape, output] = raggedTensorToTensorImpl(\n      $shape, shape.shape, $values, values.shape, values.dtype, $defaultValue,\n      defaultValue.shape, $rowPartitionValues, rowPartitionValuesShapes,\n      rowPartitionTypes);\n  return backend.makeTensorInfo(outputShape, values.dtype, output);\n}\n\nexport const raggedTensorToTensorConfig: KernelConfig = {\n  kernelName: RaggedTensorToTensor,\n  backendName: 'cpu',\n  kernelFunc: raggedTensorToTensor as unknown as KernelFunc,\n};\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAiBA,SAAkCA,oBAAoB,QAAsF,uBAAuB;AAInK,SAAQC,wBAAwB,QAAO,6BAA6B;AAEpE,OAAM,SAAUC,oBAAoBA,CAACC,IAIpC;EACC,MAAM;IAACC,MAAM;IAAEC,OAAO;IAAEC;EAAK,CAAC,GAAGH,IAAI;EACrC,MAAM;IAACI,KAAK;IAAEC,MAAM;IAAEC,YAAY;IAAEC;EAAmB,CAAC,GAAGN,MAAM;EACjE,MAAM;IAACO;EAAiB,CAAC,GAAGL,KAAK;EAEjC,MAAMM,MAAM,GAAGP,OAAO,CAACQ,IAAI,CAACC,GAAG,CAACP,KAAK,CAACQ,MAAM,CAAC,CAACP,MAAoB;EAClE,MAAMQ,OAAO,GAAGX,OAAO,CAACQ,IAAI,CAACC,GAAG,CAACN,MAAM,CAACO,MAAM,CAAC,CAACP,MAAoB;EACpE,MAAMS,aAAa,GACfZ,OAAO,CAACQ,IAAI,CAACC,GAAG,CAACL,YAAY,CAACM,MAAM,CAAC,CAACP,MAAoB;EAC9D,MAAMU,mBAAmB,GAAGR,mBAAmB,CAACS,GAAG,CAC/CC,CAAC,IAAIf,OAAO,CAACQ,IAAI,CAACC,GAAG,CAACM,CAAC,CAACL,MAAM,CAAC,CAACP,MAAoB,CAAC;EACzD,MAAMa,wBAAwB,GAAGX,mBAAmB,CAACS,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACb,KAAK,CAAC;EAEtE,MAAM,CAACe,WAAW,EAAEC,MAAM,CAAC,GAAGtB,wBAAwB,CAClDW,MAAM,EAAEL,KAAK,CAACA,KAAK,EAAES,OAAO,EAAER,MAAM,CAACD,KAAK,EAAEC,MAAM,CAACgB,KAAK,EAAEP,aAAa,EACvER,YAAY,CAACF,KAAK,EAAEW,mBAAmB,EAAEG,wBAAwB,EACjEV,iBAAiB,CAAC;EACtB,OAAON,OAAO,CAACoB,cAAc,CAACH,WAAW,EAAEd,MAAM,CAACgB,KAAK,EAAED,MAAM,CAAC;AAClE;AAEA,OAAO,MAAMG,0BAA0B,GAAiB;EACtDC,UAAU,EAAE3B,oBAAoB;EAChC4B,WAAW,EAAE,KAAK;EAClBC,UAAU,EAAE3B;CACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}