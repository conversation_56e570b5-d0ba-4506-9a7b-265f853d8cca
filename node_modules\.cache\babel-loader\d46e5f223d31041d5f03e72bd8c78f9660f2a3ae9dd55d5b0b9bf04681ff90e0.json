{"ast": null, "code": "/**\n * @license\n * Copyright 2019 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { ENGINE } from '../../engine';\nimport { customGrad } from '../../gradients';\nimport { _FusedMatMul } from '../../kernel_names';\nimport { makeTypesMatch } from '../../tensor_util';\nimport { convertToTensor } from '../../tensor_util_env';\nimport * as util from '../../util';\nimport { add } from '../add';\nimport * as broadcast_util from '../broadcast_util';\nimport { applyActivation, getFusedBiasGradient, getFusedDyActivation, shouldFuse } from '../fused_util';\nimport { matMul as unfusedMatMul } from '../mat_mul';\nimport { op } from '../operation';\nimport { reshape } from '../reshape';\n/**\n * Computes the dot product of two matrices with optional activation and bias.\n *\n * ```js\n * const a = tf.tensor2d([-1, -2], [1, 2]);\n * const b = tf.tensor2d([1, 2, 3, 4], [2, 2]);\n * const bias = tf.tensor2d([1, 2], [1, 2]);\n *\n * tf.fused.matMul({a, b, bias, activation: 'relu'}).print();\n * ```\n *\n * @param obj An object with the following properties:\n * - `a` First matrix in dot product operation.\n * - `b` Second matrix in dot product operation.\n * - `transposeA` If true, `a` is transposed before multiplication.\n * - `transposeB` If true, `b` is transposed before multiplication.\n * - `bias` Matrix to be added to the result.\n * - `activation` Name of activation kernel (defaults to `linear`).\n * - `preluActivationWeights` Tensor of prelu weights.\n * - `leakyreluAlpha` Alpha of leakyrelu.\n */\nfunction fusedMatMul_(_ref) {\n  let {\n    a,\n    b,\n    transposeA = false,\n    transposeB = false,\n    bias,\n    activation = 'linear',\n    preluActivationWeights,\n    leakyreluAlpha = 0.2\n  } = _ref;\n  if (shouldFuse(ENGINE.state.gradientDepth, activation) === false) {\n    let result = unfusedMatMul(a, b, transposeA, transposeB);\n    if (bias != null) {\n      result = add(result, bias);\n    }\n    return applyActivation(result, activation, preluActivationWeights, leakyreluAlpha);\n  }\n  let $a = convertToTensor(a, 'a', 'fused matMul');\n  let $b = convertToTensor(b, 'b', 'fused matMul');\n  [$a, $b] = makeTypesMatch($a, $b);\n  const innerShapeA = transposeA ? $a.shape[$a.rank - 2] : $a.shape[$a.rank - 1];\n  const innerShapeB = transposeB ? $b.shape[$b.rank - 1] : $b.shape[$b.rank - 2];\n  const outerShapeA = transposeA ? $a.shape[$a.rank - 1] : $a.shape[$a.rank - 2];\n  const outerShapeB = transposeB ? $b.shape[$b.rank - 2] : $b.shape[$b.rank - 1];\n  const outerDimsA = $a.shape.slice(0, -2);\n  const outerDimsB = $b.shape.slice(0, -2);\n  const batchDimA = util.sizeFromShape(outerDimsA);\n  const batchDimB = util.sizeFromShape(outerDimsB);\n  util.assert(innerShapeA === innerShapeB, () => \"Error in fused matMul: inner shapes (\".concat(innerShapeA, \") and (\") + \"\".concat(innerShapeB, \") of Tensors with shapes \").concat($a.shape, \" and \") + \"\".concat($b.shape, \" and transposeA=\").concat(transposeA) + \" and transposeB=\".concat(transposeB, \" must match.\"));\n  const outShapeOuterDims = broadcast_util.assertAndGetBroadcastShape($a.shape.slice(0, -2), $b.shape.slice(0, -2));\n  const outShape = outShapeOuterDims.concat([outerShapeA, outerShapeB]);\n  const a3D = transposeA ? reshape($a, [batchDimA, innerShapeA, outerShapeA]) : reshape($a, [batchDimA, outerShapeA, innerShapeA]);\n  const b3D = transposeB ? reshape($b, [batchDimB, outerShapeB, innerShapeB]) : reshape($b, [batchDimB, innerShapeB, outerShapeB]);\n  let $bias;\n  if (bias != null) {\n    $bias = convertToTensor(bias, 'bias', 'fused matMul');\n    [$bias] = makeTypesMatch($bias, $a);\n    broadcast_util.assertAndGetBroadcastShape(outShape, $bias.shape);\n  }\n  let $preluActivationWeights;\n  if (preluActivationWeights != null) {\n    $preluActivationWeights = convertToTensor(preluActivationWeights, 'prelu weights', 'fused matMul');\n  }\n  const grad = (dy, saved) => {\n    const [a3D, b3D, y, $bias] = saved;\n    // we reshape dy because the result of the forward is not\n    // necessarily going to be a 3d tensor due to a reshape done at the end of\n    // the customOp.\n    const dyActivation = getFusedDyActivation(reshape(dy, y.shape), y, activation);\n    let aDer;\n    let bDer;\n    if (!transposeA && !transposeB) {\n      aDer = unfusedMatMul(dyActivation, b3D, false, true);\n      bDer = unfusedMatMul(a3D, dyActivation, true, false);\n    } else if (!transposeA && transposeB) {\n      aDer = unfusedMatMul(dyActivation, b3D, false, false);\n      bDer = unfusedMatMul(dyActivation, a3D, true, false);\n    } else if (transposeA && !transposeB) {\n      aDer = unfusedMatMul(b3D, dyActivation, false, true);\n      bDer = unfusedMatMul(a3D, dyActivation, false, false);\n    } else {\n      aDer = unfusedMatMul(b3D, dyActivation, true, true);\n      bDer = unfusedMatMul(dyActivation, a3D, true, true);\n    }\n    if (bias != null) {\n      const biasDer = getFusedBiasGradient($bias, dyActivation);\n      return [aDer, bDer, biasDer];\n    } else {\n      return [aDer, bDer];\n    }\n  };\n  const inputs = {\n    a: a3D,\n    b: b3D,\n    bias: $bias,\n    preluActivationWeights: $preluActivationWeights\n  };\n  const attrs = {\n    transposeA,\n    transposeB,\n    activation,\n    leakyreluAlpha\n  };\n  // Depending on the the params passed in we will have different number of\n  // inputs and thus a a different number of elements in the gradient.\n  if (bias == null) {\n    const customOp = customGrad((a3D, b3D, save) => {\n      const res =\n      // tslint:disable-next-line: no-unnecessary-type-assertion\n      ENGINE.runKernel(_FusedMatMul, inputs, attrs);\n      save([a3D, b3D, res]);\n      return {\n        value: reshape(res, outShape),\n        gradFunc: grad\n      };\n    });\n    return customOp(a3D, b3D);\n  } else {\n    const customOpWithBias = customGrad((a3D, b3D, $bias, save) => {\n      const res =\n      // tslint:disable-next-line: no-unnecessary-type-assertion\n      ENGINE.runKernel(_FusedMatMul, inputs, attrs);\n      save([a3D, b3D, res, $bias]);\n      return {\n        value: reshape(res, outShape),\n        gradFunc: grad\n      };\n    });\n    return customOpWithBias(a3D, b3D, $bias);\n  }\n}\nexport const matMul = /* @__PURE__ */op({\n  fusedMatMul_\n});", "map": {"version": 3, "names": ["ENGINE", "customGrad", "_FusedMatMul", "makeTypesMatch", "convertToTensor", "util", "add", "broadcast_util", "applyActivation", "getFusedBiasGradient", "getFusedDyActivation", "shouldFuse", "<PERSON><PERSON><PERSON>", "unfusedMatMul", "op", "reshape", "fusedMatMul_", "_ref", "a", "b", "transposeA", "transposeB", "bias", "activation", "preluActivationWeights", "leakyreluAlpha", "state", "gradientDepth", "result", "$a", "$b", "innerShapeA", "shape", "rank", "innerShapeB", "outerShapeA", "outerShapeB", "outerDimsA", "slice", "outerDimsB", "batchDimA", "sizeFromShape", "batchDimB", "assert", "concat", "outShapeOuterDims", "assertAndGetBroadcastShape", "outShape", "a3D", "b3D", "$bias", "$preluActivationWeights", "grad", "dy", "saved", "y", "dyActivation", "aDer", "bDer", "bias<PERSON>er", "inputs", "attrs", "customOp", "save", "res", "runKernel", "value", "grad<PERSON>unc", "customOpWithBias"], "sources": ["C:\\tfjs-core\\src\\ops\\fused\\mat_mul.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2019 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {ENGINE} from '../../engine';\nimport {customGrad} from '../../gradients';\nimport {_FusedMatMul, _FusedMatMulAttrs, _FusedMatMulInputs} from '../../kernel_names';\nimport {NamedAttrMap} from '../../kernel_registry';\nimport {Tensor, Tensor3D} from '../../tensor';\nimport {GradSaveFunc, NamedTensorMap} from '../../tensor_types';\nimport {makeTypesMatch} from '../../tensor_util';\nimport {convertToTensor} from '../../tensor_util_env';\nimport {TensorLike} from '../../types';\nimport * as util from '../../util';\n\nimport {add} from '../add';\nimport * as broadcast_util from '../broadcast_util';\nimport {Activation} from '../fused_types';\nimport {applyActivation, getFusedBiasGradient, getFusedDyActivation, shouldFuse} from '../fused_util';\nimport {matMul as unfusedMatMul} from '../mat_mul';\nimport {op} from '../operation';\nimport {reshape} from '../reshape';\n\n/**\n * Computes the dot product of two matrices with optional activation and bias.\n *\n * ```js\n * const a = tf.tensor2d([-1, -2], [1, 2]);\n * const b = tf.tensor2d([1, 2, 3, 4], [2, 2]);\n * const bias = tf.tensor2d([1, 2], [1, 2]);\n *\n * tf.fused.matMul({a, b, bias, activation: 'relu'}).print();\n * ```\n *\n * @param obj An object with the following properties:\n * - `a` First matrix in dot product operation.\n * - `b` Second matrix in dot product operation.\n * - `transposeA` If true, `a` is transposed before multiplication.\n * - `transposeB` If true, `b` is transposed before multiplication.\n * - `bias` Matrix to be added to the result.\n * - `activation` Name of activation kernel (defaults to `linear`).\n * - `preluActivationWeights` Tensor of prelu weights.\n * - `leakyreluAlpha` Alpha of leakyrelu.\n */\nfunction fusedMatMul_({\n  a,\n  b,\n  transposeA = false,\n  transposeB = false,\n  bias,\n  activation = 'linear',\n  preluActivationWeights,\n  leakyreluAlpha = 0.2,\n}: {\n  a: Tensor|TensorLike,\n  b: Tensor|TensorLike,\n  transposeA?: boolean,\n  transposeB?: boolean,\n  bias?: Tensor|TensorLike,\n  activation?: Activation,\n  preluActivationWeights?: Tensor\n  leakyreluAlpha?: number\n}): Tensor {\n    if (shouldFuse(ENGINE.state.gradientDepth, activation) === false) {\n      let result = unfusedMatMul(a, b, transposeA, transposeB);\n      if (bias != null) {\n        result = add(result, bias);\n      }\n\n      return applyActivation(\n                 result, activation, preluActivationWeights, leakyreluAlpha);\n    }\n\n    let $a = convertToTensor(a, 'a', 'fused matMul');\n    let $b = convertToTensor(b, 'b', 'fused matMul');\n    [$a, $b] = makeTypesMatch($a, $b);\n\n    const innerShapeA =\n        transposeA ? $a.shape[$a.rank - 2] : $a.shape[$a.rank - 1];\n    const innerShapeB =\n        transposeB ? $b.shape[$b.rank - 1] : $b.shape[$b.rank - 2];\n\n    const outerShapeA =\n        transposeA ? $a.shape[$a.rank - 1] : $a.shape[$a.rank - 2];\n    const outerShapeB =\n        transposeB ? $b.shape[$b.rank - 2] : $b.shape[$b.rank - 1];\n\n    const outerDimsA = $a.shape.slice(0, -2);\n    const outerDimsB = $b.shape.slice(0, -2);\n    const batchDimA = util.sizeFromShape(outerDimsA);\n    const batchDimB = util.sizeFromShape(outerDimsB);\n\n    util.assert(\n        innerShapeA === innerShapeB,\n        () => `Error in fused matMul: inner shapes (${innerShapeA}) and (` +\n            `${innerShapeB}) of Tensors with shapes ${$a.shape} and ` +\n            `${$b.shape} and transposeA=${transposeA}` +\n            ` and transposeB=${transposeB} must match.`);\n\n    const outShapeOuterDims = broadcast_util.assertAndGetBroadcastShape(\n        $a.shape.slice(0, -2), $b.shape.slice(0, -2));\n    const outShape = outShapeOuterDims.concat([outerShapeA, outerShapeB]);\n\n    const a3D: Tensor3D = transposeA ?\n        reshape($a, [batchDimA, innerShapeA, outerShapeA]) :\n        reshape($a, [batchDimA, outerShapeA, innerShapeA]);\n    const b3D: Tensor3D = transposeB ?\n        reshape($b, [batchDimB, outerShapeB, innerShapeB]) :\n        reshape($b, [batchDimB, innerShapeB, outerShapeB]);\n\n    let $bias: Tensor;\n    if (bias != null) {\n      $bias = convertToTensor(bias, 'bias', 'fused matMul');\n      [$bias] = makeTypesMatch($bias, $a);\n\n      broadcast_util.assertAndGetBroadcastShape(outShape, $bias.shape);\n    }\n\n    let $preluActivationWeights: Tensor;\n    if (preluActivationWeights != null) {\n      $preluActivationWeights = convertToTensor(\n          preluActivationWeights, 'prelu weights', 'fused matMul');\n    }\n\n    const grad = (dy: Tensor3D, saved: Tensor[]) => {\n      const [a3D, b3D, y, $bias] = saved;\n      // we reshape dy because the result of the forward is not\n      // necessarily going to be a 3d tensor due to a reshape done at the end of\n      // the customOp.\n      const dyActivation =\n          getFusedDyActivation(reshape(dy, y.shape), y, activation);\n      let aDer: Tensor;\n      let bDer: Tensor;\n\n      if (!transposeA && !transposeB) {\n        aDer = unfusedMatMul(dyActivation, b3D, false, true);\n        bDer = unfusedMatMul(a3D, dyActivation, true, false);\n      } else if (!transposeA && transposeB) {\n        aDer = unfusedMatMul(dyActivation, b3D, false, false);\n        bDer = unfusedMatMul(dyActivation, a3D, true, false);\n      } else if (transposeA && !transposeB) {\n        aDer = unfusedMatMul(b3D, dyActivation, false, true);\n        bDer = unfusedMatMul(a3D, dyActivation, false, false);\n      } else {\n        aDer = unfusedMatMul(b3D, dyActivation, true, true);\n        bDer = unfusedMatMul(dyActivation, a3D, true, true);\n      }\n\n      if (bias != null) {\n        const biasDer = getFusedBiasGradient($bias, dyActivation);\n        return [aDer, bDer, biasDer];\n      } else {\n        return [aDer, bDer];\n      }\n    };\n\n    const inputs: _FusedMatMulInputs = {\n      a: a3D,\n      b: b3D,\n      bias: $bias,\n      preluActivationWeights: $preluActivationWeights\n    };\n    const attrs: _FusedMatMulAttrs =\n        {transposeA, transposeB, activation, leakyreluAlpha};\n\n    // Depending on the the params passed in we will have different number of\n    // inputs and thus a a different number of elements in the gradient.\n    if (bias == null) {\n      const customOp =\n          customGrad((a3D: Tensor3D, b3D: Tensor3D, save: GradSaveFunc) => {\n            const res =\n                // tslint:disable-next-line: no-unnecessary-type-assertion\n                ENGINE.runKernel(\n                    _FusedMatMul, inputs as unknown as NamedTensorMap,\n                    attrs as unknown as NamedAttrMap) as Tensor;\n\n            save([a3D, b3D, res]);\n\n            return {value: reshape(res, outShape), gradFunc: grad};\n          });\n      return customOp(a3D, b3D);\n    } else {\n      const customOpWithBias = customGrad(\n          (a3D: Tensor3D, b3D: Tensor3D, $bias: Tensor, save: GradSaveFunc) => {\n            const res =\n                // tslint:disable-next-line: no-unnecessary-type-assertion\n                ENGINE.runKernel(\n                    _FusedMatMul, inputs as unknown as NamedTensorMap,\n                    attrs as unknown as NamedAttrMap) as Tensor;\n\n            save([a3D, b3D, res, $bias]);\n\n            return {value: reshape(res, outShape), gradFunc: grad};\n          });\n\n      return customOpWithBias(a3D, b3D, $bias);\n    }\n  }\n\n  export const matMul = /* @__PURE__ */ op({fusedMatMul_});\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAiBA,SAAQA,MAAM,QAAO,cAAc;AACnC,SAAQC,UAAU,QAAO,iBAAiB;AAC1C,SAAQC,YAAY,QAA8C,oBAAoB;AAItF,SAAQC,cAAc,QAAO,mBAAmB;AAChD,SAAQC,eAAe,QAAO,uBAAuB;AAErD,OAAO,KAAKC,IAAI,MAAM,YAAY;AAElC,SAAQC,GAAG,QAAO,QAAQ;AAC1B,OAAO,KAAKC,cAAc,MAAM,mBAAmB;AAEnD,SAAQC,eAAe,EAAEC,oBAAoB,EAAEC,oBAAoB,EAAEC,UAAU,QAAO,eAAe;AACrG,SAAQC,MAAM,IAAIC,aAAa,QAAO,YAAY;AAClD,SAAQC,EAAE,QAAO,cAAc;AAC/B,SAAQC,OAAO,QAAO,YAAY;AAElC;;;;;;;;;;;;;;;;;;;;;AAqBA,SAASC,YAAYA,CAAAC,IAAA,EAkBpB;EAAA,IAlBqB;IACpBC,CAAC;IACDC,CAAC;IACDC,UAAU,GAAG,KAAK;IAClBC,UAAU,GAAG,KAAK;IAClBC,IAAI;IACJC,UAAU,GAAG,QAAQ;IACrBC,sBAAsB;IACtBC,cAAc,GAAG;EAAG,CAUrB,GAAAR,IAAA;EACG,IAAIN,UAAU,CAACX,MAAM,CAAC0B,KAAK,CAACC,aAAa,EAAEJ,UAAU,CAAC,KAAK,KAAK,EAAE;IAChE,IAAIK,MAAM,GAAGf,aAAa,CAACK,CAAC,EAAEC,CAAC,EAAEC,UAAU,EAAEC,UAAU,CAAC;IACxD,IAAIC,IAAI,IAAI,IAAI,EAAE;MAChBM,MAAM,GAAGtB,GAAG,CAACsB,MAAM,EAAEN,IAAI,CAAC;;IAG5B,OAAOd,eAAe,CACXoB,MAAM,EAAEL,UAAU,EAAEC,sBAAsB,EAAEC,cAAc,CAAC;;EAGxE,IAAII,EAAE,GAAGzB,eAAe,CAACc,CAAC,EAAE,GAAG,EAAE,cAAc,CAAC;EAChD,IAAIY,EAAE,GAAG1B,eAAe,CAACe,CAAC,EAAE,GAAG,EAAE,cAAc,CAAC;EAChD,CAACU,EAAE,EAAEC,EAAE,CAAC,GAAG3B,cAAc,CAAC0B,EAAE,EAAEC,EAAE,CAAC;EAEjC,MAAMC,WAAW,GACbX,UAAU,GAAGS,EAAE,CAACG,KAAK,CAACH,EAAE,CAACI,IAAI,GAAG,CAAC,CAAC,GAAGJ,EAAE,CAACG,KAAK,CAACH,EAAE,CAACI,IAAI,GAAG,CAAC,CAAC;EAC9D,MAAMC,WAAW,GACbb,UAAU,GAAGS,EAAE,CAACE,KAAK,CAACF,EAAE,CAACG,IAAI,GAAG,CAAC,CAAC,GAAGH,EAAE,CAACE,KAAK,CAACF,EAAE,CAACG,IAAI,GAAG,CAAC,CAAC;EAE9D,MAAME,WAAW,GACbf,UAAU,GAAGS,EAAE,CAACG,KAAK,CAACH,EAAE,CAACI,IAAI,GAAG,CAAC,CAAC,GAAGJ,EAAE,CAACG,KAAK,CAACH,EAAE,CAACI,IAAI,GAAG,CAAC,CAAC;EAC9D,MAAMG,WAAW,GACbf,UAAU,GAAGS,EAAE,CAACE,KAAK,CAACF,EAAE,CAACG,IAAI,GAAG,CAAC,CAAC,GAAGH,EAAE,CAACE,KAAK,CAACF,EAAE,CAACG,IAAI,GAAG,CAAC,CAAC;EAE9D,MAAMI,UAAU,GAAGR,EAAE,CAACG,KAAK,CAACM,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EACxC,MAAMC,UAAU,GAAGT,EAAE,CAACE,KAAK,CAACM,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EACxC,MAAME,SAAS,GAAGnC,IAAI,CAACoC,aAAa,CAACJ,UAAU,CAAC;EAChD,MAAMK,SAAS,GAAGrC,IAAI,CAACoC,aAAa,CAACF,UAAU,CAAC;EAEhDlC,IAAI,CAACsC,MAAM,CACPZ,WAAW,KAAKG,WAAW,EAC3B,MAAM,wCAAAU,MAAA,CAAwCb,WAAW,kBAAAa,MAAA,CAClDV,WAAW,+BAAAU,MAAA,CAA4Bf,EAAE,CAACG,KAAK,UAAO,MAAAY,MAAA,CACtDd,EAAE,CAACE,KAAK,sBAAAY,MAAA,CAAmBxB,UAAU,CAAE,sBAAAwB,MAAA,CACvBvB,UAAU,iBAAc,CAAC;EAEpD,MAAMwB,iBAAiB,GAAGtC,cAAc,CAACuC,0BAA0B,CAC/DjB,EAAE,CAACG,KAAK,CAACM,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAER,EAAE,CAACE,KAAK,CAACM,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACjD,MAAMS,QAAQ,GAAGF,iBAAiB,CAACD,MAAM,CAAC,CAACT,WAAW,EAAEC,WAAW,CAAC,CAAC;EAErE,MAAMY,GAAG,GAAa5B,UAAU,GAC5BL,OAAO,CAACc,EAAE,EAAE,CAACW,SAAS,EAAET,WAAW,EAAEI,WAAW,CAAC,CAAC,GAClDpB,OAAO,CAACc,EAAE,EAAE,CAACW,SAAS,EAAEL,WAAW,EAAEJ,WAAW,CAAC,CAAC;EACtD,MAAMkB,GAAG,GAAa5B,UAAU,GAC5BN,OAAO,CAACe,EAAE,EAAE,CAACY,SAAS,EAAEN,WAAW,EAAEF,WAAW,CAAC,CAAC,GAClDnB,OAAO,CAACe,EAAE,EAAE,CAACY,SAAS,EAAER,WAAW,EAAEE,WAAW,CAAC,CAAC;EAEtD,IAAIc,KAAa;EACjB,IAAI5B,IAAI,IAAI,IAAI,EAAE;IAChB4B,KAAK,GAAG9C,eAAe,CAACkB,IAAI,EAAE,MAAM,EAAE,cAAc,CAAC;IACrD,CAAC4B,KAAK,CAAC,GAAG/C,cAAc,CAAC+C,KAAK,EAAErB,EAAE,CAAC;IAEnCtB,cAAc,CAACuC,0BAA0B,CAACC,QAAQ,EAAEG,KAAK,CAAClB,KAAK,CAAC;;EAGlE,IAAImB,uBAA+B;EACnC,IAAI3B,sBAAsB,IAAI,IAAI,EAAE;IAClC2B,uBAAuB,GAAG/C,eAAe,CACrCoB,sBAAsB,EAAE,eAAe,EAAE,cAAc,CAAC;;EAG9D,MAAM4B,IAAI,GAAGA,CAACC,EAAY,EAAEC,KAAe,KAAI;IAC7C,MAAM,CAACN,GAAG,EAAEC,GAAG,EAAEM,CAAC,EAAEL,KAAK,CAAC,GAAGI,KAAK;IAClC;IACA;IACA;IACA,MAAME,YAAY,GACd9C,oBAAoB,CAACK,OAAO,CAACsC,EAAE,EAAEE,CAAC,CAACvB,KAAK,CAAC,EAAEuB,CAAC,EAAEhC,UAAU,CAAC;IAC7D,IAAIkC,IAAY;IAChB,IAAIC,IAAY;IAEhB,IAAI,CAACtC,UAAU,IAAI,CAACC,UAAU,EAAE;MAC9BoC,IAAI,GAAG5C,aAAa,CAAC2C,YAAY,EAAEP,GAAG,EAAE,KAAK,EAAE,IAAI,CAAC;MACpDS,IAAI,GAAG7C,aAAa,CAACmC,GAAG,EAAEQ,YAAY,EAAE,IAAI,EAAE,KAAK,CAAC;KACrD,MAAM,IAAI,CAACpC,UAAU,IAAIC,UAAU,EAAE;MACpCoC,IAAI,GAAG5C,aAAa,CAAC2C,YAAY,EAAEP,GAAG,EAAE,KAAK,EAAE,KAAK,CAAC;MACrDS,IAAI,GAAG7C,aAAa,CAAC2C,YAAY,EAAER,GAAG,EAAE,IAAI,EAAE,KAAK,CAAC;KACrD,MAAM,IAAI5B,UAAU,IAAI,CAACC,UAAU,EAAE;MACpCoC,IAAI,GAAG5C,aAAa,CAACoC,GAAG,EAAEO,YAAY,EAAE,KAAK,EAAE,IAAI,CAAC;MACpDE,IAAI,GAAG7C,aAAa,CAACmC,GAAG,EAAEQ,YAAY,EAAE,KAAK,EAAE,KAAK,CAAC;KACtD,MAAM;MACLC,IAAI,GAAG5C,aAAa,CAACoC,GAAG,EAAEO,YAAY,EAAE,IAAI,EAAE,IAAI,CAAC;MACnDE,IAAI,GAAG7C,aAAa,CAAC2C,YAAY,EAAER,GAAG,EAAE,IAAI,EAAE,IAAI,CAAC;;IAGrD,IAAI1B,IAAI,IAAI,IAAI,EAAE;MAChB,MAAMqC,OAAO,GAAGlD,oBAAoB,CAACyC,KAAK,EAAEM,YAAY,CAAC;MACzD,OAAO,CAACC,IAAI,EAAEC,IAAI,EAAEC,OAAO,CAAC;KAC7B,MAAM;MACL,OAAO,CAACF,IAAI,EAAEC,IAAI,CAAC;;EAEvB,CAAC;EAED,MAAME,MAAM,GAAuB;IACjC1C,CAAC,EAAE8B,GAAG;IACN7B,CAAC,EAAE8B,GAAG;IACN3B,IAAI,EAAE4B,KAAK;IACX1B,sBAAsB,EAAE2B;GACzB;EACD,MAAMU,KAAK,GACP;IAACzC,UAAU;IAAEC,UAAU;IAAEE,UAAU;IAAEE;EAAc,CAAC;EAExD;EACA;EACA,IAAIH,IAAI,IAAI,IAAI,EAAE;IAChB,MAAMwC,QAAQ,GACV7D,UAAU,CAAC,CAAC+C,GAAa,EAAEC,GAAa,EAAEc,IAAkB,KAAI;MAC9D,MAAMC,GAAG;MACL;MACAhE,MAAM,CAACiE,SAAS,CACZ/D,YAAY,EAAE0D,MAAmC,EACjDC,KAAgC,CAAW;MAEnDE,IAAI,CAAC,CAACf,GAAG,EAAEC,GAAG,EAAEe,GAAG,CAAC,CAAC;MAErB,OAAO;QAACE,KAAK,EAAEnD,OAAO,CAACiD,GAAG,EAAEjB,QAAQ,CAAC;QAAEoB,QAAQ,EAAEf;MAAI,CAAC;IACxD,CAAC,CAAC;IACN,OAAOU,QAAQ,CAACd,GAAG,EAAEC,GAAG,CAAC;GAC1B,MAAM;IACL,MAAMmB,gBAAgB,GAAGnE,UAAU,CAC/B,CAAC+C,GAAa,EAAEC,GAAa,EAAEC,KAAa,EAAEa,IAAkB,KAAI;MAClE,MAAMC,GAAG;MACL;MACAhE,MAAM,CAACiE,SAAS,CACZ/D,YAAY,EAAE0D,MAAmC,EACjDC,KAAgC,CAAW;MAEnDE,IAAI,CAAC,CAACf,GAAG,EAAEC,GAAG,EAAEe,GAAG,EAAEd,KAAK,CAAC,CAAC;MAE5B,OAAO;QAACgB,KAAK,EAAEnD,OAAO,CAACiD,GAAG,EAAEjB,QAAQ,CAAC;QAAEoB,QAAQ,EAAEf;MAAI,CAAC;IACxD,CAAC,CAAC;IAEN,OAAOgB,gBAAgB,CAACpB,GAAG,EAAEC,GAAG,EAAEC,KAAK,CAAC;;AAE5C;AAEA,OAAO,MAAMtC,MAAM,GAAG,eAAgBE,EAAE,CAAC;EAACE;AAAY,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}