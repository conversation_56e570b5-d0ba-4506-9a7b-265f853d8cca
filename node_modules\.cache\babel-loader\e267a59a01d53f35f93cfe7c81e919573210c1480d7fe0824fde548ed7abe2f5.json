{"ast": null, "code": "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { Exp } from '../kernel_names';\nimport { mul } from '../ops/mul';\nexport const expGradConfig = {\n  kernelName: Exp,\n  outputsToSave: [true],\n  gradFunc: (dy, saved) => {\n    const [y] = saved;\n    return {\n      x: () => mul(dy, y)\n    };\n  }\n};", "map": {"version": 3, "names": ["Exp", "mul", "expGradConfig", "kernelName", "outputsToSave", "grad<PERSON>unc", "dy", "saved", "y", "x"], "sources": ["C:\\tfjs-core\\src\\gradients\\Exp_grad.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {Exp} from '../kernel_names';\nimport {GradConfig} from '../kernel_registry';\nimport {mul} from '../ops/mul';\nimport {Tensor} from '../tensor';\n\nexport const expGradConfig: GradConfig = {\n  kernelName: Exp,\n  outputsToSave: [true],\n  gradFunc: (dy: Tensor, saved: Tensor[]) => {\n    const [y] = saved;\n    return {x: () => mul(dy, y)};\n  }\n};\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAiBA,SAAQA,GAAG,QAAO,iBAAiB;AAEnC,SAAQC,GAAG,QAAO,YAAY;AAG9B,OAAO,MAAMC,aAAa,GAAe;EACvCC,UAAU,EAAEH,GAAG;EACfI,aAAa,EAAE,CAAC,IAAI,CAAC;EACrBC,QAAQ,EAAEA,CAACC,EAAU,EAAEC,KAAe,KAAI;IACxC,MAAM,CAACC,CAAC,CAAC,GAAGD,KAAK;IACjB,OAAO;MAACE,CAAC,EAAEA,CAAA,KAAMR,GAAG,CAACK,EAAE,EAAEE,CAAC;IAAC,CAAC;EAC9B;CACD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}