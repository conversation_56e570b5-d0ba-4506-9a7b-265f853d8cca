{"ast": null, "code": "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { LogicalOr } from '@tensorflow/tfjs-core';\nimport { binaryKernelFunc } from '../kernel_utils/kernel_funcs_utils';\nconst LOGICAL_OR = \"return float(a >= 1.0 || b >= 1.0);\";\nconst LOGICAL_OR_PACKED = \"\\n  return min(\\n    vec4(greaterThanEqual(a, vec4(1.0))) +\\n    vec4(greaterThanEqual(b, vec4(1.0))),\\n    vec4(1.0));\\n\";\nexport const logicalOr = binaryKernelFunc({\n  opSnippet: LOGICAL_OR,\n  packedOpSnippet: LOGICAL_OR_PACKED,\n  dtype: 'bool'\n});\nexport const logicalOrConfig = {\n  kernelName: LogicalOr,\n  backendName: 'webgl',\n  kernelFunc: logicalOr\n};", "map": {"version": 3, "names": ["LogicalOr", "binaryKernelFunc", "LOGICAL_OR", "LOGICAL_OR_PACKED", "logicalOr", "opSnippet", "packedOpSnippet", "dtype", "logicalOrConfig", "kernelName", "backendName", "kernelFunc"], "sources": ["C:\\tfjs-backend-webgl\\src\\kernels\\LogicalOr.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {KernelConfig, KernelFunc, LogicalOr} from '@tensorflow/tfjs-core';\n\nimport {binaryKernelFunc} from '../kernel_utils/kernel_funcs_utils';\n\nconst LOGICAL_OR = `return float(a >= 1.0 || b >= 1.0);`;\nconst LOGICAL_OR_PACKED = `\n  return min(\n    vec4(greaterThanEqual(a, vec4(1.0))) +\n    vec4(greaterThanEqual(b, vec4(1.0))),\n    vec4(1.0));\n`;\n\nexport const logicalOr = binaryKernelFunc(\n    {opSnippet: LOGICAL_OR, packedOpSnippet: LOGICAL_OR_PACKED, dtype: 'bool'});\n\nexport const logicalOrConfig: KernelConfig = {\n  kernelName: LogicalOr,\n  backendName: 'webgl',\n  kernelFunc: logicalOr as unknown as KernelFunc\n};\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAiBA,SAAkCA,SAAS,QAAO,uBAAuB;AAEzE,SAAQC,gBAAgB,QAAO,oCAAoC;AAEnE,MAAMC,UAAU,wCAAwC;AACxD,MAAMC,iBAAiB,8HAKtB;AAED,OAAO,MAAMC,SAAS,GAAGH,gBAAgB,CACrC;EAACI,SAAS,EAAEH,UAAU;EAAEI,eAAe,EAAEH,iBAAiB;EAAEI,KAAK,EAAE;AAAM,CAAC,CAAC;AAE/E,OAAO,MAAMC,eAAe,GAAiB;EAC3CC,UAAU,EAAET,SAAS;EACrBU,WAAW,EAAE,OAAO;EACpBC,UAAU,EAAEP;CACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}