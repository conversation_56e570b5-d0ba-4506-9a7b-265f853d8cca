{"ast": null, "code": "/**\n * @license\n * Copyright 2017 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { useShapeUniforms } from './gpgpu_math';\nexport class UnaryOpProgram {\n  constructor(aShape, opSnippet) {\n    this.variableNames = ['A'];\n    this.outputShape = aShape;\n    this.enableShapeUniforms = useShapeUniforms(this.outputShape.length);\n    this.userCode = \"\\n      float unaryOperation(float x) {\\n        \".concat(opSnippet, \"\\n      }\\n\\n      void main() {\\n        float x = getAAtOutCoords();\\n        float y = unaryOperation(x);\\n\\n        setOutput(y);\\n      }\\n    \");\n  }\n}\nexport const CHECK_NAN_SNIPPET = \"if (isnan(x)) return x;\";\nexport const LINEAR = \"return x;\";\nexport const ABS = \"return abs(x);\";\nexport function STEP() {\n  let alpha = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 0.0;\n  return CHECK_NAN_SNIPPET + \"\\n    return x > 0.0 ? 1.0 : float(\".concat(alpha, \");\\n  \");\n}\nexport const ELU = \"return (x >= 0.0) ? x : (exp(x) - 1.0);\";\nexport const RELU = CHECK_NAN_SNIPPET + \"\\n  return (x < 0.0) ? 0.0 : x;\\n\";\nexport const RELU6 = CHECK_NAN_SNIPPET + \"\\n  return (x < 0.0) ? 0.0 : min(6.0, x);\\n\";\nexport const CLONE = 'return x;';\nexport const SIGMOID = \"return 1.0 / (1.0 + exp(-1.0 * x));\";", "map": {"version": 3, "names": ["useShapeUniforms", "UnaryOpProgram", "constructor", "aShape", "opSnippet", "variableNames", "outputShape", "enableShapeUniforms", "length", "userCode", "concat", "CHECK_NAN_SNIPPET", "LINEAR", "ABS", "STEP", "alpha", "arguments", "undefined", "ELU", "RELU", "RELU6", "CLONE", "SIGMOID"], "sources": ["C:\\tfjs-backend-webgl\\src\\unaryop_gpu.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2017 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {GPGPUProgram, useShapeUniforms} from './gpgpu_math';\n\nexport class UnaryOpProgram implements GPGPUProgram {\n  variableNames = ['A'];\n  userCode: string;\n  outputShape: number[];\n  enableShapeUniforms: boolean;\n\n  constructor(aShape: number[], opSnippet: string) {\n    this.outputShape = aShape;\n    this.enableShapeUniforms = useShapeUniforms(this.outputShape.length);\n    this.userCode = `\n      float unaryOperation(float x) {\n        ${opSnippet}\n      }\n\n      void main() {\n        float x = getAAtOutCoords();\n        float y = unaryOperation(x);\n\n        setOutput(y);\n      }\n    `;\n  }\n}\n\nexport const CHECK_NAN_SNIPPET = `if (isnan(x)) return x;`;\n\nexport const LINEAR = `return x;`;\n\nexport const ABS = `return abs(x);`;\n\nexport function STEP(alpha = 0.0) {\n  return CHECK_NAN_SNIPPET + `\n    return x > 0.0 ? 1.0 : float(${alpha});\n  `;\n}\n\nexport const ELU = `return (x >= 0.0) ? x : (exp(x) - 1.0);`;\nexport const RELU = CHECK_NAN_SNIPPET + `\n  return (x < 0.0) ? 0.0 : x;\n`;\n\nexport const RELU6 = CHECK_NAN_SNIPPET + `\n  return (x < 0.0) ? 0.0 : min(6.0, x);\n`;\n\nexport const CLONE = 'return x;';\n\nexport const SIGMOID = `return 1.0 / (1.0 + exp(-1.0 * x));`;\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAiBA,SAAsBA,gBAAgB,QAAO,cAAc;AAE3D,OAAM,MAAOC,cAAc;EAMzBC,YAAYC,MAAgB,EAAEC,SAAiB;IAL/C,KAAAC,aAAa,GAAG,CAAC,GAAG,CAAC;IAMnB,IAAI,CAACC,WAAW,GAAGH,MAAM;IACzB,IAAI,CAACI,mBAAmB,GAAGP,gBAAgB,CAAC,IAAI,CAACM,WAAW,CAACE,MAAM,CAAC;IACpE,IAAI,CAACC,QAAQ,uDAAAC,MAAA,CAEPN,SAAS,yJASd;EACH;;AAGF,OAAO,MAAMO,iBAAiB,4BAA4B;AAE1D,OAAO,MAAMC,MAAM,cAAc;AAEjC,OAAO,MAAMC,GAAG,mBAAmB;AAEnC,OAAM,SAAUC,IAAIA,CAAA,EAAY;EAAA,IAAXC,KAAK,GAAAC,SAAA,CAAAR,MAAA,QAAAQ,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAG,GAAG;EAC9B,OAAOL,iBAAiB,yCAAAD,MAAA,CACSK,KAAK,WACrC;AACH;AAEA,OAAO,MAAMG,GAAG,4CAA4C;AAC5D,OAAO,MAAMC,IAAI,GAAGR,iBAAiB,sCAEpC;AAED,OAAO,MAAMS,KAAK,GAAGT,iBAAiB,gDAErC;AAED,OAAO,MAAMU,KAAK,GAAG,WAAW;AAEhC,OAAO,MAAMC,OAAO,wCAAwC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}