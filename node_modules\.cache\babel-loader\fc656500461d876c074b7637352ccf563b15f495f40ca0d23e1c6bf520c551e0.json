{"ast": null, "code": "import * as tf from '@tensorflow/tfjs';\nimport * as natural from 'natural';\nimport { fileStorageService } from './fileStorageService';\nimport { categoryService } from './categoryService';\n\n// Feature vector interface (for future use)\n// interface FeatureVector {\n//   features: number[];\n//   categoryId: string;\n// }\n\nclass MLCategorizationService {\n  constructor() {\n    this.MODELS_FILENAME = 'ml_models';\n    this.TRAINING_DATA_FILENAME = 'training_data';\n    // private readonly FEATURE_CACHE_FILENAME = 'feature_cache';\n    this.model = null;\n    this.categories = [];\n    this.categoryToIndex = new Map();\n    this.indexToCategory = new Map();\n    // private vocabularySize = 1000;\n    this.embeddingDim = 50;\n    // private maxSequenceLength = 20;\n    this.tokenizer = void 0;\n    this.tokenizer = new natural.WordTokenizer();\n    this.initializeCategories();\n  }\n\n  // Initialize categories mapping\n  initializeCategories() {\n    this.categories = categoryService.getAllCategories();\n    this.categories.forEach((category, index) => {\n      this.categoryToIndex.set(category.id, index);\n      this.indexToCategory.set(index, category.id);\n    });\n  }\n\n  // Extract features from transaction description\n  extractFeatures(description, amount) {\n    const tokens = this.tokenizer.tokenize(description.toLowerCase()) || [];\n\n    // Text features using word embeddings simulation\n    const textFeatures = this.createTextEmbedding(tokens);\n\n    // Amount features\n    const amountFeatures = this.createAmountFeatures(amount);\n\n    // Pattern features\n    const patternFeatures = this.createPatternFeatures(description);\n    return [...textFeatures, ...amountFeatures, ...patternFeatures];\n  }\n\n  // Create text embedding features\n  createTextEmbedding(tokens) {\n    const embedding = new Array(this.embeddingDim).fill(0);\n    if (tokens.length === 0) return embedding;\n\n    // Simple word-based features for common financial terms\n    const financialKeywords = {\n      'salary': [0.9, 0.1, 0.8, 0.2],\n      'wage': [0.9, 0.1, 0.8, 0.2],\n      'transfer': [0.5, 0.5, 0.5, 0.5],\n      'payment': [0.2, 0.8, 0.3, 0.7],\n      'fee': [0.1, 0.9, 0.2, 0.8],\n      'tax': [0.1, 0.9, 0.9, 0.1],\n      'interest': [0.8, 0.2, 0.6, 0.4],\n      'dividend': [0.9, 0.1, 0.7, 0.3],\n      'rent': [0.1, 0.9, 0.4, 0.6],\n      'utility': [0.1, 0.9, 0.3, 0.7],\n      'insurance': [0.1, 0.9, 0.5, 0.5],\n      'loan': [0.3, 0.7, 0.8, 0.2],\n      'deposit': [0.8, 0.2, 0.6, 0.4],\n      'withdrawal': [0.2, 0.8, 0.4, 0.6],\n      'purchase': [0.1, 0.9, 0.3, 0.7],\n      'refund': [0.7, 0.3, 0.5, 0.5]\n    };\n    let keywordMatches = 0;\n    tokens.forEach((token, index) => {\n      if (financialKeywords[token]) {\n        const weights = financialKeywords[token];\n        for (let i = 0; i < Math.min(weights.length, embedding.length); i++) {\n          embedding[i] += weights[i];\n        }\n        keywordMatches++;\n      }\n\n      // Add position-based weighting\n      const positionWeight = 1 - index / tokens.length * 0.5;\n      const hashValue = this.simpleHash(token) % this.embeddingDim;\n      embedding[hashValue] += positionWeight * 0.1;\n    });\n\n    // Normalize by number of tokens\n    if (keywordMatches > 0) {\n      for (let i = 0; i < embedding.length; i++) {\n        embedding[i] /= keywordMatches;\n      }\n    }\n    return embedding;\n  }\n\n  // Create amount-based features\n  createAmountFeatures(amount) {\n    const absAmount = Math.abs(amount);\n    return [amount > 0 ? 1 : 0,\n    // Credit indicator\n    amount < 0 ? 1 : 0,\n    // Debit indicator\n    Math.log(absAmount + 1) / 10,\n    // Log-scaled amount\n    absAmount < 100 ? 1 : 0,\n    // Small amount\n    absAmount >= 100 && absAmount < 1000 ? 1 : 0,\n    // Medium amount\n    absAmount >= 1000 ? 1 : 0,\n    // Large amount\n    absAmount % 1 === 0 ? 1 : 0,\n    // Round number\n    absAmount.toString().includes('00') ? 1 : 0 // Contains 00\n    ];\n  }\n\n  // Create pattern-based features\n  createPatternFeatures(description) {\n    const desc = description.toLowerCase();\n    return [/\\d{4,}/.test(desc) ? 1 : 0,\n    // Contains 4+ digit number\n    /ref|reference/.test(desc) ? 1 : 0,\n    // Contains reference\n    /date|\\/|\\-/.test(desc) ? 1 : 0,\n    // Contains date patterns\n    /^direct|^dd|^standing/.test(desc) ? 1 : 0,\n    // Direct debit/standing order\n    /atm|cash|withdrawal/.test(desc) ? 1 : 0,\n    // ATM/cash transaction\n    /online|internet|web/.test(desc) ? 1 : 0,\n    // Online transaction\n    /card|visa|mastercard/.test(desc) ? 1 : 0,\n    // Card transaction\n    desc.length > 50 ? 1 : 0,\n    // Long description\n    desc.split(' ').length > 10 ? 1 : 0,\n    // Many words\n    /[A-Z]{2,}/.test(description) ? 1 : 0 // Contains uppercase words\n    ];\n  }\n\n  // Simple hash function for string-to-number mapping\n  simpleHash(str) {\n    let hash = 0;\n    for (let i = 0; i < str.length; i++) {\n      const char = str.charCodeAt(i);\n      hash = (hash << 5) - hash + char;\n      hash = hash & hash; // Convert to 32-bit integer\n    }\n    return Math.abs(hash);\n  }\n\n  // Build neural network model\n  buildModel() {\n    const inputSize = this.embeddingDim + 8 + 10; // text + amount + pattern features\n    const numCategories = this.categories.length;\n    const model = tf.sequential({\n      layers: [tf.layers.dense({\n        inputShape: [inputSize],\n        units: 128,\n        activation: 'relu',\n        kernelRegularizer: tf.regularizers.l2({\n          l2: 0.001\n        })\n      }), tf.layers.dropout({\n        rate: 0.3\n      }), tf.layers.dense({\n        units: 64,\n        activation: 'relu',\n        kernelRegularizer: tf.regularizers.l2({\n          l2: 0.001\n        })\n      }), tf.layers.dropout({\n        rate: 0.2\n      }), tf.layers.dense({\n        units: 32,\n        activation: 'relu'\n      }), tf.layers.dense({\n        units: numCategories,\n        activation: 'softmax'\n      })]\n    });\n    model.compile({\n      optimizer: tf.train.adam(0.001),\n      loss: 'categoricalCrossentropy',\n      metrics: ['accuracy']\n    });\n    return model;\n  }\n\n  // Prepare training data\n  prepareTrainingData(trainingData) {\n    const features = [];\n    const labels = [];\n    trainingData.forEach(data => {\n      const featureVector = this.extractFeatures(data.description, data.amount);\n      const categoryIndex = this.categoryToIndex.get(data.categoryId);\n      if (categoryIndex !== undefined) {\n        features.push(featureVector);\n\n        // Create one-hot encoded label\n        const label = new Array(this.categories.length).fill(0);\n        label[categoryIndex] = 1;\n        labels.push(label);\n      }\n    });\n    const xs = tf.tensor2d(features);\n    const ys = tf.tensor2d(labels);\n    return {\n      xs,\n      ys\n    };\n  }\n\n  // Train the model\n  async trainModel(trainingData) {\n    this.initializeCategories();\n    if (trainingData.length < 10) {\n      throw new Error('Insufficient training data. Need at least 10 examples.');\n    }\n\n    // Build new model\n    this.model = this.buildModel();\n\n    // Prepare data\n    const {\n      xs,\n      ys\n    } = this.prepareTrainingData(trainingData);\n    try {\n      // Train model\n      const history = await this.model.fit(xs, ys, {\n        epochs: 100,\n        batchSize: 32,\n        validationSplit: 0.2,\n        shuffle: true,\n        callbacks: {\n          onEpochEnd: async (epoch, logs) => {\n            if (epoch % 10 === 0) {\n              var _logs$loss, _logs$acc;\n              console.log(`Epoch ${epoch}: loss = ${logs === null || logs === void 0 ? void 0 : (_logs$loss = logs.loss) === null || _logs$loss === void 0 ? void 0 : _logs$loss.toFixed(4)}, accuracy = ${logs === null || logs === void 0 ? void 0 : (_logs$acc = logs.acc) === null || _logs$acc === void 0 ? void 0 : _logs$acc.toFixed(4)}`);\n            }\n          }\n        }\n      });\n\n      // Get final metrics\n      const finalAccuracy = history.history.acc[history.history.acc.length - 1];\n      const finalLoss = history.history.loss[history.history.loss.length - 1];\n\n      // Save model\n      await this.saveModel(finalAccuracy, trainingData.length);\n\n      // Clean up tensors\n      xs.dispose();\n      ys.dispose();\n      return {\n        accuracy: finalAccuracy,\n        loss: finalLoss\n      };\n    } catch (error) {\n      xs.dispose();\n      ys.dispose();\n      throw error;\n    }\n  }\n\n  // Categorize a single transaction\n  async categorizeTransaction(description, amount) {\n    if (!this.model) {\n      await this.loadLatestModel();\n    }\n    if (!this.model) {\n      return null;\n    }\n    const features = this.extractFeatures(description, amount);\n    const input = tf.tensor2d([features]);\n    try {\n      const prediction = this.model.predict(input);\n      const probabilities = await prediction.data();\n\n      // Find the category with highest probability\n      let maxProb = 0;\n      let bestCategoryIndex = 0;\n      for (let i = 0; i < probabilities.length; i++) {\n        if (probabilities[i] > maxProb) {\n          maxProb = probabilities[i];\n          bestCategoryIndex = i;\n        }\n      }\n      const categoryId = this.indexToCategory.get(bestCategoryIndex);\n      if (categoryId && maxProb > 0.1) {\n        // Minimum confidence threshold\n        input.dispose();\n        prediction.dispose();\n        return {\n          categoryId,\n          confidence: maxProb,\n          algorithm: 'neural_network',\n          features: ['description', 'amount', 'patterns'],\n          trainingDate: new Date().toISOString()\n        };\n      }\n      input.dispose();\n      prediction.dispose();\n      return null;\n    } catch (error) {\n      input.dispose();\n      console.error('Error during categorization:', error);\n      return null;\n    }\n  }\n\n  // Save model to local storage\n  async saveModel(accuracy, trainingSize) {\n    if (!this.model) return;\n    try {\n      const modelData = await this.model.save(tf.io.withSaveHandler(async artifacts => {\n        const modelJson = JSON.stringify(artifacts.modelTopology);\n        const weightsData = artifacts.weightData ? Array.from(new Uint8Array(artifacts.weightData)) : [];\n        return {\n          modelTopology: modelJson,\n          weightData: weightsData,\n          weightSpecs: artifacts.weightSpecs,\n          modelArtifactsInfo: {\n            dateSaved: new Date(),\n            modelTopologyType: 'JSON'\n          }\n        };\n      }));\n      const mlModel = {\n        id: this.generateId(),\n        name: 'Transaction Categorization Model',\n        version: '1.0',\n        algorithm: 'neural_network',\n        accuracy,\n        trainingDate: new Date().toISOString(),\n        trainingSize,\n        isActive: true,\n        modelData: JSON.stringify(modelData)\n      };\n      const models = this.getAllModels();\n      // Deactivate old models\n      models.forEach(m => m.isActive = false);\n      models.push(mlModel);\n      fileStorageService.writeData(this.MODELS_FILENAME, models);\n    } catch (error) {\n      console.error('Failed to save model:', error);\n    }\n  }\n\n  // Load the latest active model\n  async loadLatestModel() {\n    try {\n      const models = this.getAllModels();\n      const activeModel = models.find(m => m.isActive);\n      if (!activeModel) return false;\n      const modelData = JSON.parse(activeModel.modelData);\n      this.model = await tf.loadLayersModel(tf.io.fromMemory({\n        modelTopology: JSON.parse(modelData.modelTopology),\n        weightData: new Uint8Array(modelData.weightData).buffer,\n        weightSpecs: modelData.weightSpecs\n      }));\n      return true;\n    } catch (error) {\n      console.error('Failed to load model:', error);\n      return false;\n    }\n  }\n\n  // Get all stored models\n  getAllModels() {\n    return fileStorageService.readData(this.MODELS_FILENAME, []);\n  }\n\n  // Add training data\n  addTrainingData(description, amount, categoryId) {\n    const trainingData = this.getTrainingData();\n    const newData = {\n      id: this.generateId(),\n      description,\n      amount,\n      categoryId,\n      features: this.extractFeatures(description, amount),\n      createdDate: new Date().toISOString()\n    };\n    trainingData.push(newData);\n    this.saveTrainingData(trainingData);\n  }\n\n  // Get all training data\n  getTrainingData() {\n    return fileStorageService.readData(this.TRAINING_DATA_FILENAME, []);\n  }\n\n  // Save training data\n  saveTrainingData(data) {\n    fileStorageService.writeData(this.TRAINING_DATA_FILENAME, data);\n  }\n\n  // Clear training data\n  clearTrainingData() {\n    this.saveTrainingData([]);\n  }\n\n  // Get model statistics\n  getModelStats() {\n    const models = this.getAllModels();\n    const activeModel = models.find(m => m.isActive) || null;\n    const trainingDataSize = this.getTrainingData().length;\n    return {\n      totalModels: models.length,\n      activeModel,\n      trainingDataSize\n    };\n  }\n\n  // Generate unique ID\n  generateId() {\n    return `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;\n  }\n\n  // Initialize with sample training data if none exists\n  initializeWithSampleData() {\n    const existingData = this.getTrainingData();\n    if (existingData.length > 0) return;\n\n    // Get uncategorized category ID\n    const uncategorizedCategory = this.categories.find(c => c.name === 'Uncategorized');\n    if (!uncategorizedCategory) return;\n\n    // Add some sample training data\n    const sampleData = [{\n      description: 'SALARY PAYMENT',\n      amount: 5000,\n      categoryId: uncategorizedCategory.id\n    }, {\n      description: 'OFFICE RENT MONTHLY',\n      amount: -1200,\n      categoryId: uncategorizedCategory.id\n    }, {\n      description: 'ELECTRICITY BILL',\n      amount: -150,\n      categoryId: uncategorizedCategory.id\n    }, {\n      description: 'BANK CHARGES',\n      amount: -25,\n      categoryId: uncategorizedCategory.id\n    }, {\n      description: 'CLIENT PAYMENT',\n      amount: 2500,\n      categoryId: uncategorizedCategory.id\n    }];\n    sampleData.forEach(data => {\n      this.addTrainingData(data.description, data.amount, data.categoryId);\n    });\n  }\n}\nexport const mlCategorizationService = new MLCategorizationService();", "map": {"version": 3, "names": ["tf", "natural", "fileStorageService", "categoryService", "MLCategorizationService", "constructor", "MODELS_FILENAME", "TRAINING_DATA_FILENAME", "model", "categories", "categoryToIndex", "Map", "indexToCategory", "embedding<PERSON>im", "tokenizer", "WordTokenizer", "initializeCategories", "getAllCategories", "for<PERSON>ach", "category", "index", "set", "id", "extractFeatures", "description", "amount", "tokens", "tokenize", "toLowerCase", "textFeatures", "createTextEmbedding", "amountFeatures", "createAmountFeatures", "patternFeatures", "createPatternFeatures", "embedding", "Array", "fill", "length", "financialKeywords", "keywordMatches", "token", "weights", "i", "Math", "min", "positionWeight", "hashValue", "simpleHash", "absAmount", "abs", "log", "toString", "includes", "desc", "test", "split", "str", "hash", "char", "charCodeAt", "buildModel", "inputSize", "numCategories", "sequential", "layers", "dense", "inputShape", "units", "activation", "kernelRegularizer", "regularizers", "l2", "dropout", "rate", "compile", "optimizer", "train", "adam", "loss", "metrics", "prepareTrainingData", "trainingData", "features", "labels", "data", "featureVector", "categoryIndex", "get", "categoryId", "undefined", "push", "label", "xs", "tensor2d", "ys", "trainModel", "Error", "history", "fit", "epochs", "batchSize", "validationSplit", "shuffle", "callbacks", "onEpochEnd", "epoch", "logs", "_logs$loss", "_logs$acc", "console", "toFixed", "acc", "finalAccuracy", "finalLoss", "saveModel", "dispose", "accuracy", "error", "categorizeTransaction", "loadLatestModel", "input", "prediction", "predict", "probabilities", "maxProb", "bestCategoryIndex", "confidence", "algorithm", "trainingDate", "Date", "toISOString", "trainingSize", "modelData", "save", "io", "with<PERSON><PERSON><PERSON><PERSON><PERSON>", "artifacts", "<PERSON><PERSON><PERSON>", "JSON", "stringify", "modelTopology", "weightsData", "weightData", "from", "Uint8Array", "weightSpecs", "modelArtifactsInfo", "dateSaved", "modelTopologyType", "mlModel", "generateId", "name", "version", "isActive", "models", "getAllModels", "m", "writeData", "activeModel", "find", "parse", "loadLayersModel", "fromMemory", "buffer", "readData", "addTrainingData", "getTrainingData", "newData", "createdDate", "saveTrainingData", "clearTrainingData", "getModelStats", "trainingDataSize", "totalModels", "now", "random", "substr", "initializeWithSampleData", "existingData", "uncategorizedCategory", "c", "sampleData", "mlCategorizationService"], "sources": ["C:/tmsft/src/services/mlCategorizationService.ts"], "sourcesContent": ["import * as tf from '@tensorflow/tfjs';\r\nimport * as natural from 'natural';\r\nimport { MLModel, TrainingData, MLCategorization, TransactionCategory } from '../types';\r\nimport { fileStorageService } from './fileStorageService';\r\nimport { categoryService } from './categoryService';\r\n\r\n// Feature vector interface (for future use)\r\n// interface FeatureVector {\r\n//   features: number[];\r\n//   categoryId: string;\r\n// }\r\n\r\nclass MLCategorizationService {\r\n  private readonly MODELS_FILENAME = 'ml_models';\r\n  private readonly TRAINING_DATA_FILENAME = 'training_data';\r\n  // private readonly FEATURE_CACHE_FILENAME = 'feature_cache';\r\n  \r\n  private model: tf.LayersModel | null = null;\r\n  private categories: TransactionCategory[] = [];\r\n  private categoryToIndex: Map<string, number> = new Map();\r\n  private indexToCategory: Map<number, string> = new Map();\r\n  // private vocabularySize = 1000;\r\n  private embeddingDim = 50;\r\n  // private maxSequenceLength = 20;\r\n  private tokenizer: natural.WordTokenizer;\r\n\r\n  constructor() {\r\n    this.tokenizer = new natural.WordTokenizer();\r\n    this.initializeCategories();\r\n  }\r\n\r\n  // Initialize categories mapping\r\n  private initializeCategories(): void {\r\n    this.categories = categoryService.getAllCategories();\r\n    this.categories.forEach((category, index) => {\r\n      this.categoryToIndex.set(category.id, index);\r\n      this.indexToCategory.set(index, category.id);\r\n    });\r\n  }\r\n\r\n  // Extract features from transaction description\r\n  private extractFeatures(description: string, amount: number): number[] {\r\n    const tokens = this.tokenizer.tokenize(description.toLowerCase()) || [];\r\n    \r\n    // Text features using word embeddings simulation\r\n    const textFeatures = this.createTextEmbedding(tokens);\r\n    \r\n    // Amount features\r\n    const amountFeatures = this.createAmountFeatures(amount);\r\n    \r\n    // Pattern features\r\n    const patternFeatures = this.createPatternFeatures(description);\r\n    \r\n    return [...textFeatures, ...amountFeatures, ...patternFeatures];\r\n  }\r\n\r\n  // Create text embedding features\r\n  private createTextEmbedding(tokens: string[]): number[] {\r\n    const embedding = new Array(this.embeddingDim).fill(0);\r\n    \r\n    if (tokens.length === 0) return embedding;\r\n    \r\n    // Simple word-based features for common financial terms\r\n    const financialKeywords: Record<string, number[]> = {\r\n      'salary': [0.9, 0.1, 0.8, 0.2],\r\n      'wage': [0.9, 0.1, 0.8, 0.2],\r\n      'transfer': [0.5, 0.5, 0.5, 0.5],\r\n      'payment': [0.2, 0.8, 0.3, 0.7],\r\n      'fee': [0.1, 0.9, 0.2, 0.8],\r\n      'tax': [0.1, 0.9, 0.9, 0.1],\r\n      'interest': [0.8, 0.2, 0.6, 0.4],\r\n      'dividend': [0.9, 0.1, 0.7, 0.3],\r\n      'rent': [0.1, 0.9, 0.4, 0.6],\r\n      'utility': [0.1, 0.9, 0.3, 0.7],\r\n      'insurance': [0.1, 0.9, 0.5, 0.5],\r\n      'loan': [0.3, 0.7, 0.8, 0.2],\r\n      'deposit': [0.8, 0.2, 0.6, 0.4],\r\n      'withdrawal': [0.2, 0.8, 0.4, 0.6],\r\n      'purchase': [0.1, 0.9, 0.3, 0.7],\r\n      'refund': [0.7, 0.3, 0.5, 0.5]\r\n    };\r\n\r\n    let keywordMatches = 0;\r\n    tokens.forEach((token, index) => {\r\n      if (financialKeywords[token]) {\r\n        const weights = financialKeywords[token];\r\n        for (let i = 0; i < Math.min(weights.length, embedding.length); i++) {\r\n          embedding[i] += weights[i];\r\n        }\r\n        keywordMatches++;\r\n      }\r\n      \r\n      // Add position-based weighting\r\n      const positionWeight = 1 - (index / tokens.length) * 0.5;\r\n      const hashValue = this.simpleHash(token) % this.embeddingDim;\r\n      embedding[hashValue] += positionWeight * 0.1;\r\n    });\r\n\r\n    // Normalize by number of tokens\r\n    if (keywordMatches > 0) {\r\n      for (let i = 0; i < embedding.length; i++) {\r\n        embedding[i] /= keywordMatches;\r\n      }\r\n    }\r\n\r\n    return embedding;\r\n  }\r\n\r\n  // Create amount-based features\r\n  private createAmountFeatures(amount: number): number[] {\r\n    const absAmount = Math.abs(amount);\r\n    \r\n    return [\r\n      amount > 0 ? 1 : 0, // Credit indicator\r\n      amount < 0 ? 1 : 0, // Debit indicator\r\n      Math.log(absAmount + 1) / 10, // Log-scaled amount\r\n      absAmount < 100 ? 1 : 0, // Small amount\r\n      absAmount >= 100 && absAmount < 1000 ? 1 : 0, // Medium amount\r\n      absAmount >= 1000 ? 1 : 0, // Large amount\r\n      absAmount % 1 === 0 ? 1 : 0, // Round number\r\n      absAmount.toString().includes('00') ? 1 : 0 // Contains 00\r\n    ];\r\n  }\r\n\r\n  // Create pattern-based features\r\n  private createPatternFeatures(description: string): number[] {\r\n    const desc = description.toLowerCase();\r\n    \r\n    return [\r\n      /\\d{4,}/.test(desc) ? 1 : 0, // Contains 4+ digit number\r\n      /ref|reference/.test(desc) ? 1 : 0, // Contains reference\r\n      /date|\\/|\\-/.test(desc) ? 1 : 0, // Contains date patterns\r\n      /^direct|^dd|^standing/.test(desc) ? 1 : 0, // Direct debit/standing order\r\n      /atm|cash|withdrawal/.test(desc) ? 1 : 0, // ATM/cash transaction\r\n      /online|internet|web/.test(desc) ? 1 : 0, // Online transaction\r\n      /card|visa|mastercard/.test(desc) ? 1 : 0, // Card transaction\r\n      desc.length > 50 ? 1 : 0, // Long description\r\n      desc.split(' ').length > 10 ? 1 : 0, // Many words\r\n      /[A-Z]{2,}/.test(description) ? 1 : 0 // Contains uppercase words\r\n    ];\r\n  }\r\n\r\n  // Simple hash function for string-to-number mapping\r\n  private simpleHash(str: string): number {\r\n    let hash = 0;\r\n    for (let i = 0; i < str.length; i++) {\r\n      const char = str.charCodeAt(i);\r\n      hash = ((hash << 5) - hash) + char;\r\n      hash = hash & hash; // Convert to 32-bit integer\r\n    }\r\n    return Math.abs(hash);\r\n  }\r\n\r\n  // Build neural network model\r\n  private buildModel(): tf.LayersModel {\r\n    const inputSize = this.embeddingDim + 8 + 10; // text + amount + pattern features\r\n    const numCategories = this.categories.length;\r\n\r\n    const model = tf.sequential({\r\n      layers: [\r\n        tf.layers.dense({\r\n          inputShape: [inputSize],\r\n          units: 128,\r\n          activation: 'relu',\r\n          kernelRegularizer: tf.regularizers.l2({ l2: 0.001 })\r\n        }),\r\n        tf.layers.dropout({ rate: 0.3 }),\r\n        tf.layers.dense({\r\n          units: 64,\r\n          activation: 'relu',\r\n          kernelRegularizer: tf.regularizers.l2({ l2: 0.001 })\r\n        }),\r\n        tf.layers.dropout({ rate: 0.2 }),\r\n        tf.layers.dense({\r\n          units: 32,\r\n          activation: 'relu'\r\n        }),\r\n        tf.layers.dense({\r\n          units: numCategories,\r\n          activation: 'softmax'\r\n        })\r\n      ]\r\n    });\r\n\r\n    model.compile({\r\n      optimizer: tf.train.adam(0.001),\r\n      loss: 'categoricalCrossentropy',\r\n      metrics: ['accuracy']\r\n    });\r\n\r\n    return model;\r\n  }\r\n\r\n  // Prepare training data\r\n  private prepareTrainingData(trainingData: TrainingData[]): { xs: tf.Tensor, ys: tf.Tensor } {\r\n    const features: number[][] = [];\r\n    const labels: number[][] = [];\r\n\r\n    trainingData.forEach(data => {\r\n      const featureVector = this.extractFeatures(data.description, data.amount);\r\n      const categoryIndex = this.categoryToIndex.get(data.categoryId);\r\n      \r\n      if (categoryIndex !== undefined) {\r\n        features.push(featureVector);\r\n        \r\n        // Create one-hot encoded label\r\n        const label = new Array(this.categories.length).fill(0);\r\n        label[categoryIndex] = 1;\r\n        labels.push(label);\r\n      }\r\n    });\r\n\r\n    const xs = tf.tensor2d(features);\r\n    const ys = tf.tensor2d(labels);\r\n\r\n    return { xs, ys };\r\n  }\r\n\r\n  // Train the model\r\n  async trainModel(trainingData: TrainingData[]): Promise<{ accuracy: number; loss: number }> {\r\n    this.initializeCategories();\r\n    \r\n    if (trainingData.length < 10) {\r\n      throw new Error('Insufficient training data. Need at least 10 examples.');\r\n    }\r\n\r\n    // Build new model\r\n    this.model = this.buildModel();\r\n    \r\n    // Prepare data\r\n    const { xs, ys } = this.prepareTrainingData(trainingData);\r\n    \r\n    try {\r\n      // Train model\r\n      const history = await this.model.fit(xs, ys, {\r\n        epochs: 100,\r\n        batchSize: 32,\r\n        validationSplit: 0.2,\r\n        shuffle: true,\r\n        callbacks: {\r\n          onEpochEnd: async (epoch, logs) => {\r\n            if (epoch % 10 === 0) {\r\n              console.log(`Epoch ${epoch}: loss = ${logs?.loss?.toFixed(4)}, accuracy = ${logs?.acc?.toFixed(4)}`);\r\n            }\r\n          }\r\n        }\r\n      });\r\n\r\n      // Get final metrics\r\n      const finalAccuracy = history.history.acc[history.history.acc.length - 1] as number;\r\n      const finalLoss = history.history.loss[history.history.loss.length - 1] as number;\r\n\r\n      // Save model\r\n      await this.saveModel(finalAccuracy, trainingData.length);\r\n\r\n      // Clean up tensors\r\n      xs.dispose();\r\n      ys.dispose();\r\n\r\n      return { accuracy: finalAccuracy, loss: finalLoss };\r\n    } catch (error) {\r\n      xs.dispose();\r\n      ys.dispose();\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  // Categorize a single transaction\r\n  async categorizeTransaction(description: string, amount: number): Promise<MLCategorization | null> {\r\n    if (!this.model) {\r\n      await this.loadLatestModel();\r\n    }\r\n\r\n    if (!this.model) {\r\n      return null;\r\n    }\r\n\r\n    const features = this.extractFeatures(description, amount);\r\n    const input = tf.tensor2d([features]);\r\n\r\n    try {\r\n      const prediction = this.model.predict(input) as tf.Tensor;\r\n      const probabilities = await prediction.data();\r\n      \r\n      // Find the category with highest probability\r\n      let maxProb = 0;\r\n      let bestCategoryIndex = 0;\r\n      \r\n      for (let i = 0; i < probabilities.length; i++) {\r\n        if (probabilities[i] > maxProb) {\r\n          maxProb = probabilities[i];\r\n          bestCategoryIndex = i;\r\n        }\r\n      }\r\n\r\n      const categoryId = this.indexToCategory.get(bestCategoryIndex);\r\n      \r\n      if (categoryId && maxProb > 0.1) { // Minimum confidence threshold\r\n        input.dispose();\r\n        prediction.dispose();\r\n        \r\n        return {\r\n          categoryId,\r\n          confidence: maxProb,\r\n          algorithm: 'neural_network',\r\n          features: ['description', 'amount', 'patterns'],\r\n          trainingDate: new Date().toISOString()\r\n        };\r\n      }\r\n\r\n      input.dispose();\r\n      prediction.dispose();\r\n      return null;\r\n    } catch (error) {\r\n      input.dispose();\r\n      console.error('Error during categorization:', error);\r\n      return null;\r\n    }\r\n  }\r\n\r\n  // Save model to local storage\r\n  private async saveModel(accuracy: number, trainingSize: number): Promise<void> {\r\n    if (!this.model) return;\r\n\r\n    try {\r\n      const modelData = await this.model.save(tf.io.withSaveHandler(async (artifacts) => {\r\n        const modelJson = JSON.stringify(artifacts.modelTopology);\r\n        const weightsData = artifacts.weightData ? Array.from(new Uint8Array(artifacts.weightData as ArrayBuffer)) : [];\r\n        \r\n        return {\r\n          modelTopology: modelJson,\r\n          weightData: weightsData,\r\n          weightSpecs: artifacts.weightSpecs,\r\n          modelArtifactsInfo: {\r\n            dateSaved: new Date(),\r\n            modelTopologyType: 'JSON'\r\n          }\r\n        };\r\n      }));\r\n\r\n      const mlModel: MLModel = {\r\n        id: this.generateId(),\r\n        name: 'Transaction Categorization Model',\r\n        version: '1.0',\r\n        algorithm: 'neural_network',\r\n        accuracy,\r\n        trainingDate: new Date().toISOString(),\r\n        trainingSize,\r\n        isActive: true,\r\n        modelData: JSON.stringify(modelData)\r\n      };\r\n\r\n      const models = this.getAllModels();\r\n      // Deactivate old models\r\n      models.forEach(m => m.isActive = false);\r\n      models.push(mlModel);\r\n\r\n      fileStorageService.writeData(this.MODELS_FILENAME, models);\r\n    } catch (error) {\r\n      console.error('Failed to save model:', error);\r\n    }\r\n  }\r\n\r\n  // Load the latest active model\r\n  private async loadLatestModel(): Promise<boolean> {\r\n    try {\r\n      const models = this.getAllModels();\r\n      const activeModel = models.find(m => m.isActive);\r\n      \r\n      if (!activeModel) return false;\r\n\r\n      const modelData = JSON.parse(activeModel.modelData);\r\n      \r\n      this.model = await tf.loadLayersModel(tf.io.fromMemory({\r\n        modelTopology: JSON.parse(modelData.modelTopology),\r\n        weightData: new Uint8Array(modelData.weightData).buffer,\r\n        weightSpecs: modelData.weightSpecs\r\n      }));\r\n\r\n      return true;\r\n    } catch (error) {\r\n      console.error('Failed to load model:', error);\r\n      return false;\r\n    }\r\n  }\r\n\r\n  // Get all stored models\r\n  getAllModels(): MLModel[] {\r\n    return fileStorageService.readData<MLModel[]>(this.MODELS_FILENAME, []);\r\n  }\r\n\r\n  // Add training data\r\n  addTrainingData(description: string, amount: number, categoryId: string): void {\r\n    const trainingData = this.getTrainingData();\r\n    \r\n    const newData: TrainingData = {\r\n      id: this.generateId(),\r\n      description,\r\n      amount,\r\n      categoryId,\r\n      features: this.extractFeatures(description, amount),\r\n      createdDate: new Date().toISOString()\r\n    };\r\n\r\n    trainingData.push(newData);\r\n    this.saveTrainingData(trainingData);\r\n  }\r\n\r\n  // Get all training data\r\n  getTrainingData(): TrainingData[] {\r\n    return fileStorageService.readData<TrainingData[]>(this.TRAINING_DATA_FILENAME, []);\r\n  }\r\n\r\n  // Save training data\r\n  private saveTrainingData(data: TrainingData[]): void {\r\n    fileStorageService.writeData(this.TRAINING_DATA_FILENAME, data);\r\n  }\r\n\r\n  // Clear training data\r\n  clearTrainingData(): void {\r\n    this.saveTrainingData([]);\r\n  }\r\n\r\n  // Get model statistics\r\n  getModelStats(): { totalModels: number; activeModel: MLModel | null; trainingDataSize: number } {\r\n    const models = this.getAllModels();\r\n    const activeModel = models.find(m => m.isActive) || null;\r\n    const trainingDataSize = this.getTrainingData().length;\r\n\r\n    return {\r\n      totalModels: models.length,\r\n      activeModel,\r\n      trainingDataSize\r\n    };\r\n  }\r\n\r\n  // Generate unique ID\r\n  private generateId(): string {\r\n    return `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;\r\n  }\r\n\r\n  // Initialize with sample training data if none exists\r\n  initializeWithSampleData(): void {\r\n    const existingData = this.getTrainingData();\r\n    if (existingData.length > 0) return;\r\n\r\n    // Get uncategorized category ID\r\n    const uncategorizedCategory = this.categories.find(c => c.name === 'Uncategorized');\r\n    if (!uncategorizedCategory) return;\r\n\r\n    // Add some sample training data\r\n    const sampleData = [\r\n      { description: 'SALARY PAYMENT', amount: 5000, categoryId: uncategorizedCategory.id },\r\n      { description: 'OFFICE RENT MONTHLY', amount: -1200, categoryId: uncategorizedCategory.id },\r\n      { description: 'ELECTRICITY BILL', amount: -150, categoryId: uncategorizedCategory.id },\r\n      { description: 'BANK CHARGES', amount: -25, categoryId: uncategorizedCategory.id },\r\n      { description: 'CLIENT PAYMENT', amount: 2500, categoryId: uncategorizedCategory.id }\r\n    ];\r\n\r\n    sampleData.forEach(data => {\r\n      this.addTrainingData(data.description, data.amount, data.categoryId);\r\n    });\r\n  }\r\n}\r\n\r\nexport const mlCategorizationService = new MLCategorizationService(); "], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,kBAAkB;AACtC,OAAO,KAAKC,OAAO,MAAM,SAAS;AAElC,SAASC,kBAAkB,QAAQ,sBAAsB;AACzD,SAASC,eAAe,QAAQ,mBAAmB;;AAEnD;AACA;AACA;AACA;AACA;;AAEA,MAAMC,uBAAuB,CAAC;EAc5BC,WAAWA,CAAA,EAAG;IAAA,KAbGC,eAAe,GAAG,WAAW;IAAA,KAC7BC,sBAAsB,GAAG,eAAe;IACzD;IAAA,KAEQC,KAAK,GAA0B,IAAI;IAAA,KACnCC,UAAU,GAA0B,EAAE;IAAA,KACtCC,eAAe,GAAwB,IAAIC,GAAG,CAAC,CAAC;IAAA,KAChDC,eAAe,GAAwB,IAAID,GAAG,CAAC,CAAC;IACxD;IAAA,KACQE,YAAY,GAAG,EAAE;IACzB;IAAA,KACQC,SAAS;IAGf,IAAI,CAACA,SAAS,GAAG,IAAIb,OAAO,CAACc,aAAa,CAAC,CAAC;IAC5C,IAAI,CAACC,oBAAoB,CAAC,CAAC;EAC7B;;EAEA;EACQA,oBAAoBA,CAAA,EAAS;IACnC,IAAI,CAACP,UAAU,GAAGN,eAAe,CAACc,gBAAgB,CAAC,CAAC;IACpD,IAAI,CAACR,UAAU,CAACS,OAAO,CAAC,CAACC,QAAQ,EAAEC,KAAK,KAAK;MAC3C,IAAI,CAACV,eAAe,CAACW,GAAG,CAACF,QAAQ,CAACG,EAAE,EAAEF,KAAK,CAAC;MAC5C,IAAI,CAACR,eAAe,CAACS,GAAG,CAACD,KAAK,EAAED,QAAQ,CAACG,EAAE,CAAC;IAC9C,CAAC,CAAC;EACJ;;EAEA;EACQC,eAAeA,CAACC,WAAmB,EAAEC,MAAc,EAAY;IACrE,MAAMC,MAAM,GAAG,IAAI,CAACZ,SAAS,CAACa,QAAQ,CAACH,WAAW,CAACI,WAAW,CAAC,CAAC,CAAC,IAAI,EAAE;;IAEvE;IACA,MAAMC,YAAY,GAAG,IAAI,CAACC,mBAAmB,CAACJ,MAAM,CAAC;;IAErD;IACA,MAAMK,cAAc,GAAG,IAAI,CAACC,oBAAoB,CAACP,MAAM,CAAC;;IAExD;IACA,MAAMQ,eAAe,GAAG,IAAI,CAACC,qBAAqB,CAACV,WAAW,CAAC;IAE/D,OAAO,CAAC,GAAGK,YAAY,EAAE,GAAGE,cAAc,EAAE,GAAGE,eAAe,CAAC;EACjE;;EAEA;EACQH,mBAAmBA,CAACJ,MAAgB,EAAY;IACtD,MAAMS,SAAS,GAAG,IAAIC,KAAK,CAAC,IAAI,CAACvB,YAAY,CAAC,CAACwB,IAAI,CAAC,CAAC,CAAC;IAEtD,IAAIX,MAAM,CAACY,MAAM,KAAK,CAAC,EAAE,OAAOH,SAAS;;IAEzC;IACA,MAAMI,iBAA2C,GAAG;MAClD,QAAQ,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;MAC9B,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;MAC5B,UAAU,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;MAChC,SAAS,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;MAC/B,KAAK,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;MAC3B,KAAK,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;MAC3B,UAAU,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;MAChC,UAAU,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;MAChC,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;MAC5B,SAAS,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;MAC/B,WAAW,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;MACjC,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;MAC5B,SAAS,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;MAC/B,YAAY,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;MAClC,UAAU,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;MAChC,QAAQ,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG;IAC/B,CAAC;IAED,IAAIC,cAAc,GAAG,CAAC;IACtBd,MAAM,CAACR,OAAO,CAAC,CAACuB,KAAK,EAAErB,KAAK,KAAK;MAC/B,IAAImB,iBAAiB,CAACE,KAAK,CAAC,EAAE;QAC5B,MAAMC,OAAO,GAAGH,iBAAiB,CAACE,KAAK,CAAC;QACxC,KAAK,IAAIE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGC,IAAI,CAACC,GAAG,CAACH,OAAO,CAACJ,MAAM,EAAEH,SAAS,CAACG,MAAM,CAAC,EAAEK,CAAC,EAAE,EAAE;UACnER,SAAS,CAACQ,CAAC,CAAC,IAAID,OAAO,CAACC,CAAC,CAAC;QAC5B;QACAH,cAAc,EAAE;MAClB;;MAEA;MACA,MAAMM,cAAc,GAAG,CAAC,GAAI1B,KAAK,GAAGM,MAAM,CAACY,MAAM,GAAI,GAAG;MACxD,MAAMS,SAAS,GAAG,IAAI,CAACC,UAAU,CAACP,KAAK,CAAC,GAAG,IAAI,CAAC5B,YAAY;MAC5DsB,SAAS,CAACY,SAAS,CAAC,IAAID,cAAc,GAAG,GAAG;IAC9C,CAAC,CAAC;;IAEF;IACA,IAAIN,cAAc,GAAG,CAAC,EAAE;MACtB,KAAK,IAAIG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGR,SAAS,CAACG,MAAM,EAAEK,CAAC,EAAE,EAAE;QACzCR,SAAS,CAACQ,CAAC,CAAC,IAAIH,cAAc;MAChC;IACF;IAEA,OAAOL,SAAS;EAClB;;EAEA;EACQH,oBAAoBA,CAACP,MAAc,EAAY;IACrD,MAAMwB,SAAS,GAAGL,IAAI,CAACM,GAAG,CAACzB,MAAM,CAAC;IAElC,OAAO,CACLA,MAAM,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC;IAAE;IACpBA,MAAM,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC;IAAE;IACpBmB,IAAI,CAACO,GAAG,CAACF,SAAS,GAAG,CAAC,CAAC,GAAG,EAAE;IAAE;IAC9BA,SAAS,GAAG,GAAG,GAAG,CAAC,GAAG,CAAC;IAAE;IACzBA,SAAS,IAAI,GAAG,IAAIA,SAAS,GAAG,IAAI,GAAG,CAAC,GAAG,CAAC;IAAE;IAC9CA,SAAS,IAAI,IAAI,GAAG,CAAC,GAAG,CAAC;IAAE;IAC3BA,SAAS,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC;IAAE;IAC7BA,SAAS,CAACG,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;IAAA,CAC7C;EACH;;EAEA;EACQnB,qBAAqBA,CAACV,WAAmB,EAAY;IAC3D,MAAM8B,IAAI,GAAG9B,WAAW,CAACI,WAAW,CAAC,CAAC;IAEtC,OAAO,CACL,QAAQ,CAAC2B,IAAI,CAACD,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC;IAAE;IAC7B,eAAe,CAACC,IAAI,CAACD,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC;IAAE;IACpC,YAAY,CAACC,IAAI,CAACD,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC;IAAE;IACjC,uBAAuB,CAACC,IAAI,CAACD,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC;IAAE;IAC5C,qBAAqB,CAACC,IAAI,CAACD,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC;IAAE;IAC1C,qBAAqB,CAACC,IAAI,CAACD,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC;IAAE;IAC1C,sBAAsB,CAACC,IAAI,CAACD,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC;IAAE;IAC3CA,IAAI,CAAChB,MAAM,GAAG,EAAE,GAAG,CAAC,GAAG,CAAC;IAAE;IAC1BgB,IAAI,CAACE,KAAK,CAAC,GAAG,CAAC,CAAClB,MAAM,GAAG,EAAE,GAAG,CAAC,GAAG,CAAC;IAAE;IACrC,WAAW,CAACiB,IAAI,CAAC/B,WAAW,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;IAAA,CACvC;EACH;;EAEA;EACQwB,UAAUA,CAACS,GAAW,EAAU;IACtC,IAAIC,IAAI,GAAG,CAAC;IACZ,KAAK,IAAIf,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGc,GAAG,CAACnB,MAAM,EAAEK,CAAC,EAAE,EAAE;MACnC,MAAMgB,IAAI,GAAGF,GAAG,CAACG,UAAU,CAACjB,CAAC,CAAC;MAC9Be,IAAI,GAAI,CAACA,IAAI,IAAI,CAAC,IAAIA,IAAI,GAAIC,IAAI;MAClCD,IAAI,GAAGA,IAAI,GAAGA,IAAI,CAAC,CAAC;IACtB;IACA,OAAOd,IAAI,CAACM,GAAG,CAACQ,IAAI,CAAC;EACvB;;EAEA;EACQG,UAAUA,CAAA,EAAmB;IACnC,MAAMC,SAAS,GAAG,IAAI,CAACjD,YAAY,GAAG,CAAC,GAAG,EAAE,CAAC,CAAC;IAC9C,MAAMkD,aAAa,GAAG,IAAI,CAACtD,UAAU,CAAC6B,MAAM;IAE5C,MAAM9B,KAAK,GAAGR,EAAE,CAACgE,UAAU,CAAC;MAC1BC,MAAM,EAAE,CACNjE,EAAE,CAACiE,MAAM,CAACC,KAAK,CAAC;QACdC,UAAU,EAAE,CAACL,SAAS,CAAC;QACvBM,KAAK,EAAE,GAAG;QACVC,UAAU,EAAE,MAAM;QAClBC,iBAAiB,EAAEtE,EAAE,CAACuE,YAAY,CAACC,EAAE,CAAC;UAAEA,EAAE,EAAE;QAAM,CAAC;MACrD,CAAC,CAAC,EACFxE,EAAE,CAACiE,MAAM,CAACQ,OAAO,CAAC;QAAEC,IAAI,EAAE;MAAI,CAAC,CAAC,EAChC1E,EAAE,CAACiE,MAAM,CAACC,KAAK,CAAC;QACdE,KAAK,EAAE,EAAE;QACTC,UAAU,EAAE,MAAM;QAClBC,iBAAiB,EAAEtE,EAAE,CAACuE,YAAY,CAACC,EAAE,CAAC;UAAEA,EAAE,EAAE;QAAM,CAAC;MACrD,CAAC,CAAC,EACFxE,EAAE,CAACiE,MAAM,CAACQ,OAAO,CAAC;QAAEC,IAAI,EAAE;MAAI,CAAC,CAAC,EAChC1E,EAAE,CAACiE,MAAM,CAACC,KAAK,CAAC;QACdE,KAAK,EAAE,EAAE;QACTC,UAAU,EAAE;MACd,CAAC,CAAC,EACFrE,EAAE,CAACiE,MAAM,CAACC,KAAK,CAAC;QACdE,KAAK,EAAEL,aAAa;QACpBM,UAAU,EAAE;MACd,CAAC,CAAC;IAEN,CAAC,CAAC;IAEF7D,KAAK,CAACmE,OAAO,CAAC;MACZC,SAAS,EAAE5E,EAAE,CAAC6E,KAAK,CAACC,IAAI,CAAC,KAAK,CAAC;MAC/BC,IAAI,EAAE,yBAAyB;MAC/BC,OAAO,EAAE,CAAC,UAAU;IACtB,CAAC,CAAC;IAEF,OAAOxE,KAAK;EACd;;EAEA;EACQyE,mBAAmBA,CAACC,YAA4B,EAAoC;IAC1F,MAAMC,QAAoB,GAAG,EAAE;IAC/B,MAAMC,MAAkB,GAAG,EAAE;IAE7BF,YAAY,CAAChE,OAAO,CAACmE,IAAI,IAAI;MAC3B,MAAMC,aAAa,GAAG,IAAI,CAAC/D,eAAe,CAAC8D,IAAI,CAAC7D,WAAW,EAAE6D,IAAI,CAAC5D,MAAM,CAAC;MACzE,MAAM8D,aAAa,GAAG,IAAI,CAAC7E,eAAe,CAAC8E,GAAG,CAACH,IAAI,CAACI,UAAU,CAAC;MAE/D,IAAIF,aAAa,KAAKG,SAAS,EAAE;QAC/BP,QAAQ,CAACQ,IAAI,CAACL,aAAa,CAAC;;QAE5B;QACA,MAAMM,KAAK,GAAG,IAAIxD,KAAK,CAAC,IAAI,CAAC3B,UAAU,CAAC6B,MAAM,CAAC,CAACD,IAAI,CAAC,CAAC,CAAC;QACvDuD,KAAK,CAACL,aAAa,CAAC,GAAG,CAAC;QACxBH,MAAM,CAACO,IAAI,CAACC,KAAK,CAAC;MACpB;IACF,CAAC,CAAC;IAEF,MAAMC,EAAE,GAAG7F,EAAE,CAAC8F,QAAQ,CAACX,QAAQ,CAAC;IAChC,MAAMY,EAAE,GAAG/F,EAAE,CAAC8F,QAAQ,CAACV,MAAM,CAAC;IAE9B,OAAO;MAAES,EAAE;MAAEE;IAAG,CAAC;EACnB;;EAEA;EACA,MAAMC,UAAUA,CAACd,YAA4B,EAA+C;IAC1F,IAAI,CAAClE,oBAAoB,CAAC,CAAC;IAE3B,IAAIkE,YAAY,CAAC5C,MAAM,GAAG,EAAE,EAAE;MAC5B,MAAM,IAAI2D,KAAK,CAAC,wDAAwD,CAAC;IAC3E;;IAEA;IACA,IAAI,CAACzF,KAAK,GAAG,IAAI,CAACqD,UAAU,CAAC,CAAC;;IAE9B;IACA,MAAM;MAAEgC,EAAE;MAAEE;IAAG,CAAC,GAAG,IAAI,CAACd,mBAAmB,CAACC,YAAY,CAAC;IAEzD,IAAI;MACF;MACA,MAAMgB,OAAO,GAAG,MAAM,IAAI,CAAC1F,KAAK,CAAC2F,GAAG,CAACN,EAAE,EAAEE,EAAE,EAAE;QAC3CK,MAAM,EAAE,GAAG;QACXC,SAAS,EAAE,EAAE;QACbC,eAAe,EAAE,GAAG;QACpBC,OAAO,EAAE,IAAI;QACbC,SAAS,EAAE;UACTC,UAAU,EAAE,MAAAA,CAAOC,KAAK,EAAEC,IAAI,KAAK;YACjC,IAAID,KAAK,GAAG,EAAE,KAAK,CAAC,EAAE;cAAA,IAAAE,UAAA,EAAAC,SAAA;cACpBC,OAAO,CAAC3D,GAAG,CAAC,SAASuD,KAAK,YAAYC,IAAI,aAAJA,IAAI,wBAAAC,UAAA,GAAJD,IAAI,CAAE5B,IAAI,cAAA6B,UAAA,uBAAVA,UAAA,CAAYG,OAAO,CAAC,CAAC,CAAC,gBAAgBJ,IAAI,aAAJA,IAAI,wBAAAE,SAAA,GAAJF,IAAI,CAAEK,GAAG,cAAAH,SAAA,uBAATA,SAAA,CAAWE,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC;YACtG;UACF;QACF;MACF,CAAC,CAAC;;MAEF;MACA,MAAME,aAAa,GAAGf,OAAO,CAACA,OAAO,CAACc,GAAG,CAACd,OAAO,CAACA,OAAO,CAACc,GAAG,CAAC1E,MAAM,GAAG,CAAC,CAAW;MACnF,MAAM4E,SAAS,GAAGhB,OAAO,CAACA,OAAO,CAACnB,IAAI,CAACmB,OAAO,CAACA,OAAO,CAACnB,IAAI,CAACzC,MAAM,GAAG,CAAC,CAAW;;MAEjF;MACA,MAAM,IAAI,CAAC6E,SAAS,CAACF,aAAa,EAAE/B,YAAY,CAAC5C,MAAM,CAAC;;MAExD;MACAuD,EAAE,CAACuB,OAAO,CAAC,CAAC;MACZrB,EAAE,CAACqB,OAAO,CAAC,CAAC;MAEZ,OAAO;QAAEC,QAAQ,EAAEJ,aAAa;QAAElC,IAAI,EAAEmC;MAAU,CAAC;IACrD,CAAC,CAAC,OAAOI,KAAK,EAAE;MACdzB,EAAE,CAACuB,OAAO,CAAC,CAAC;MACZrB,EAAE,CAACqB,OAAO,CAAC,CAAC;MACZ,MAAME,KAAK;IACb;EACF;;EAEA;EACA,MAAMC,qBAAqBA,CAAC/F,WAAmB,EAAEC,MAAc,EAAoC;IACjG,IAAI,CAAC,IAAI,CAACjB,KAAK,EAAE;MACf,MAAM,IAAI,CAACgH,eAAe,CAAC,CAAC;IAC9B;IAEA,IAAI,CAAC,IAAI,CAAChH,KAAK,EAAE;MACf,OAAO,IAAI;IACb;IAEA,MAAM2E,QAAQ,GAAG,IAAI,CAAC5D,eAAe,CAACC,WAAW,EAAEC,MAAM,CAAC;IAC1D,MAAMgG,KAAK,GAAGzH,EAAE,CAAC8F,QAAQ,CAAC,CAACX,QAAQ,CAAC,CAAC;IAErC,IAAI;MACF,MAAMuC,UAAU,GAAG,IAAI,CAAClH,KAAK,CAACmH,OAAO,CAACF,KAAK,CAAc;MACzD,MAAMG,aAAa,GAAG,MAAMF,UAAU,CAACrC,IAAI,CAAC,CAAC;;MAE7C;MACA,IAAIwC,OAAO,GAAG,CAAC;MACf,IAAIC,iBAAiB,GAAG,CAAC;MAEzB,KAAK,IAAInF,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGiF,aAAa,CAACtF,MAAM,EAAEK,CAAC,EAAE,EAAE;QAC7C,IAAIiF,aAAa,CAACjF,CAAC,CAAC,GAAGkF,OAAO,EAAE;UAC9BA,OAAO,GAAGD,aAAa,CAACjF,CAAC,CAAC;UAC1BmF,iBAAiB,GAAGnF,CAAC;QACvB;MACF;MAEA,MAAM8C,UAAU,GAAG,IAAI,CAAC7E,eAAe,CAAC4E,GAAG,CAACsC,iBAAiB,CAAC;MAE9D,IAAIrC,UAAU,IAAIoC,OAAO,GAAG,GAAG,EAAE;QAAE;QACjCJ,KAAK,CAACL,OAAO,CAAC,CAAC;QACfM,UAAU,CAACN,OAAO,CAAC,CAAC;QAEpB,OAAO;UACL3B,UAAU;UACVsC,UAAU,EAAEF,OAAO;UACnBG,SAAS,EAAE,gBAAgB;UAC3B7C,QAAQ,EAAE,CAAC,aAAa,EAAE,QAAQ,EAAE,UAAU,CAAC;UAC/C8C,YAAY,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;QACvC,CAAC;MACH;MAEAV,KAAK,CAACL,OAAO,CAAC,CAAC;MACfM,UAAU,CAACN,OAAO,CAAC,CAAC;MACpB,OAAO,IAAI;IACb,CAAC,CAAC,OAAOE,KAAK,EAAE;MACdG,KAAK,CAACL,OAAO,CAAC,CAAC;MACfN,OAAO,CAACQ,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACpD,OAAO,IAAI;IACb;EACF;;EAEA;EACA,MAAcH,SAASA,CAACE,QAAgB,EAAEe,YAAoB,EAAiB;IAC7E,IAAI,CAAC,IAAI,CAAC5H,KAAK,EAAE;IAEjB,IAAI;MACF,MAAM6H,SAAS,GAAG,MAAM,IAAI,CAAC7H,KAAK,CAAC8H,IAAI,CAACtI,EAAE,CAACuI,EAAE,CAACC,eAAe,CAAC,MAAOC,SAAS,IAAK;QACjF,MAAMC,SAAS,GAAGC,IAAI,CAACC,SAAS,CAACH,SAAS,CAACI,aAAa,CAAC;QACzD,MAAMC,WAAW,GAAGL,SAAS,CAACM,UAAU,GAAG3G,KAAK,CAAC4G,IAAI,CAAC,IAAIC,UAAU,CAACR,SAAS,CAACM,UAAyB,CAAC,CAAC,GAAG,EAAE;QAE/G,OAAO;UACLF,aAAa,EAAEH,SAAS;UACxBK,UAAU,EAAED,WAAW;UACvBI,WAAW,EAAET,SAAS,CAACS,WAAW;UAClCC,kBAAkB,EAAE;YAClBC,SAAS,EAAE,IAAIlB,IAAI,CAAC,CAAC;YACrBmB,iBAAiB,EAAE;UACrB;QACF,CAAC;MACH,CAAC,CAAC,CAAC;MAEH,MAAMC,OAAgB,GAAG;QACvBhI,EAAE,EAAE,IAAI,CAACiI,UAAU,CAAC,CAAC;QACrBC,IAAI,EAAE,kCAAkC;QACxCC,OAAO,EAAE,KAAK;QACdzB,SAAS,EAAE,gBAAgB;QAC3BX,QAAQ;QACRY,YAAY,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;QACtCC,YAAY;QACZsB,QAAQ,EAAE,IAAI;QACdrB,SAAS,EAAEM,IAAI,CAACC,SAAS,CAACP,SAAS;MACrC,CAAC;MAED,MAAMsB,MAAM,GAAG,IAAI,CAACC,YAAY,CAAC,CAAC;MAClC;MACAD,MAAM,CAACzI,OAAO,CAAC2I,CAAC,IAAIA,CAAC,CAACH,QAAQ,GAAG,KAAK,CAAC;MACvCC,MAAM,CAAChE,IAAI,CAAC2D,OAAO,CAAC;MAEpBpJ,kBAAkB,CAAC4J,SAAS,CAAC,IAAI,CAACxJ,eAAe,EAAEqJ,MAAM,CAAC;IAC5D,CAAC,CAAC,OAAOrC,KAAK,EAAE;MACdR,OAAO,CAACQ,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;IAC/C;EACF;;EAEA;EACA,MAAcE,eAAeA,CAAA,EAAqB;IAChD,IAAI;MACF,MAAMmC,MAAM,GAAG,IAAI,CAACC,YAAY,CAAC,CAAC;MAClC,MAAMG,WAAW,GAAGJ,MAAM,CAACK,IAAI,CAACH,CAAC,IAAIA,CAAC,CAACH,QAAQ,CAAC;MAEhD,IAAI,CAACK,WAAW,EAAE,OAAO,KAAK;MAE9B,MAAM1B,SAAS,GAAGM,IAAI,CAACsB,KAAK,CAACF,WAAW,CAAC1B,SAAS,CAAC;MAEnD,IAAI,CAAC7H,KAAK,GAAG,MAAMR,EAAE,CAACkK,eAAe,CAAClK,EAAE,CAACuI,EAAE,CAAC4B,UAAU,CAAC;QACrDtB,aAAa,EAAEF,IAAI,CAACsB,KAAK,CAAC5B,SAAS,CAACQ,aAAa,CAAC;QAClDE,UAAU,EAAE,IAAIE,UAAU,CAACZ,SAAS,CAACU,UAAU,CAAC,CAACqB,MAAM;QACvDlB,WAAW,EAAEb,SAAS,CAACa;MACzB,CAAC,CAAC,CAAC;MAEH,OAAO,IAAI;IACb,CAAC,CAAC,OAAO5B,KAAK,EAAE;MACdR,OAAO,CAACQ,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MAC7C,OAAO,KAAK;IACd;EACF;;EAEA;EACAsC,YAAYA,CAAA,EAAc;IACxB,OAAO1J,kBAAkB,CAACmK,QAAQ,CAAY,IAAI,CAAC/J,eAAe,EAAE,EAAE,CAAC;EACzE;;EAEA;EACAgK,eAAeA,CAAC9I,WAAmB,EAAEC,MAAc,EAAEgE,UAAkB,EAAQ;IAC7E,MAAMP,YAAY,GAAG,IAAI,CAACqF,eAAe,CAAC,CAAC;IAE3C,MAAMC,OAAqB,GAAG;MAC5BlJ,EAAE,EAAE,IAAI,CAACiI,UAAU,CAAC,CAAC;MACrB/H,WAAW;MACXC,MAAM;MACNgE,UAAU;MACVN,QAAQ,EAAE,IAAI,CAAC5D,eAAe,CAACC,WAAW,EAAEC,MAAM,CAAC;MACnDgJ,WAAW,EAAE,IAAIvC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;IACtC,CAAC;IAEDjD,YAAY,CAACS,IAAI,CAAC6E,OAAO,CAAC;IAC1B,IAAI,CAACE,gBAAgB,CAACxF,YAAY,CAAC;EACrC;;EAEA;EACAqF,eAAeA,CAAA,EAAmB;IAChC,OAAOrK,kBAAkB,CAACmK,QAAQ,CAAiB,IAAI,CAAC9J,sBAAsB,EAAE,EAAE,CAAC;EACrF;;EAEA;EACQmK,gBAAgBA,CAACrF,IAAoB,EAAQ;IACnDnF,kBAAkB,CAAC4J,SAAS,CAAC,IAAI,CAACvJ,sBAAsB,EAAE8E,IAAI,CAAC;EACjE;;EAEA;EACAsF,iBAAiBA,CAAA,EAAS;IACxB,IAAI,CAACD,gBAAgB,CAAC,EAAE,CAAC;EAC3B;;EAEA;EACAE,aAAaA,CAAA,EAAmF;IAC9F,MAAMjB,MAAM,GAAG,IAAI,CAACC,YAAY,CAAC,CAAC;IAClC,MAAMG,WAAW,GAAGJ,MAAM,CAACK,IAAI,CAACH,CAAC,IAAIA,CAAC,CAACH,QAAQ,CAAC,IAAI,IAAI;IACxD,MAAMmB,gBAAgB,GAAG,IAAI,CAACN,eAAe,CAAC,CAAC,CAACjI,MAAM;IAEtD,OAAO;MACLwI,WAAW,EAAEnB,MAAM,CAACrH,MAAM;MAC1ByH,WAAW;MACXc;IACF,CAAC;EACH;;EAEA;EACQtB,UAAUA,CAAA,EAAW;IAC3B,OAAO,GAAGrB,IAAI,CAAC6C,GAAG,CAAC,CAAC,IAAInI,IAAI,CAACoI,MAAM,CAAC,CAAC,CAAC5H,QAAQ,CAAC,EAAE,CAAC,CAAC6H,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;EACnE;;EAEA;EACAC,wBAAwBA,CAAA,EAAS;IAC/B,MAAMC,YAAY,GAAG,IAAI,CAACZ,eAAe,CAAC,CAAC;IAC3C,IAAIY,YAAY,CAAC7I,MAAM,GAAG,CAAC,EAAE;;IAE7B;IACA,MAAM8I,qBAAqB,GAAG,IAAI,CAAC3K,UAAU,CAACuJ,IAAI,CAACqB,CAAC,IAAIA,CAAC,CAAC7B,IAAI,KAAK,eAAe,CAAC;IACnF,IAAI,CAAC4B,qBAAqB,EAAE;;IAE5B;IACA,MAAME,UAAU,GAAG,CACjB;MAAE9J,WAAW,EAAE,gBAAgB;MAAEC,MAAM,EAAE,IAAI;MAAEgE,UAAU,EAAE2F,qBAAqB,CAAC9J;IAAG,CAAC,EACrF;MAAEE,WAAW,EAAE,qBAAqB;MAAEC,MAAM,EAAE,CAAC,IAAI;MAAEgE,UAAU,EAAE2F,qBAAqB,CAAC9J;IAAG,CAAC,EAC3F;MAAEE,WAAW,EAAE,kBAAkB;MAAEC,MAAM,EAAE,CAAC,GAAG;MAAEgE,UAAU,EAAE2F,qBAAqB,CAAC9J;IAAG,CAAC,EACvF;MAAEE,WAAW,EAAE,cAAc;MAAEC,MAAM,EAAE,CAAC,EAAE;MAAEgE,UAAU,EAAE2F,qBAAqB,CAAC9J;IAAG,CAAC,EAClF;MAAEE,WAAW,EAAE,gBAAgB;MAAEC,MAAM,EAAE,IAAI;MAAEgE,UAAU,EAAE2F,qBAAqB,CAAC9J;IAAG,CAAC,CACtF;IAEDgK,UAAU,CAACpK,OAAO,CAACmE,IAAI,IAAI;MACzB,IAAI,CAACiF,eAAe,CAACjF,IAAI,CAAC7D,WAAW,EAAE6D,IAAI,CAAC5D,MAAM,EAAE4D,IAAI,CAACI,UAAU,CAAC;IACtE,CAAC,CAAC;EACJ;AACF;AAEA,OAAO,MAAM8F,uBAAuB,GAAG,IAAInL,uBAAuB,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}