{"ast": null, "code": "/**\n * @license\n * Copyright 2019 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { ENGINE } from '../../engine';\nimport { customGrad } from '../../gradients';\nimport { FusedDepthwiseConv2D } from '../../kernel_names';\nimport { makeTypesMatch } from '../../tensor_util';\nimport { convertToTensor } from '../../tensor_util_env';\nimport * as util from '../../util';\nimport { add } from '../add';\nimport * as broadcast_util from '../broadcast_util';\nimport * as conv_util from '../conv_util';\nimport { depthwiseConv2d as unfusedDepthwiseConv2d } from '../depthwise_conv2d';\nimport { depthwiseConv2dNativeBackpropFilter } from '../depthwise_conv2d_native_backprop_filter';\nimport { depthwiseConv2dNativeBackpropInput } from '../depthwise_conv2d_native_backprop_input';\nimport { applyActivation, getFusedBiasGradient, getFusedDyActivation, shouldFuse } from '../fused_util';\nimport { op } from '../operation';\nimport { reshape } from '../reshape';\n/**\n * Computes depthwise 2D convolution, optionally fused with adding a\n * bias and applying an activation.\n *\n * Given a 4D `input` array and a `filter` array of shape\n * `[filterHeight, filterWidth, inChannels, channelMultiplier]` containing\n * `inChannels` convolutional filters of depth 1, this op applies a\n * different filter to each input channel (expanding from 1 channel to\n * `channelMultiplier` channels for each), then concatenates the results\n * together. The output has `inChannels * channelMultiplier` channels.\n *\n * See\n * [https://www.tensorflow.org/api_docs/python/tf/nn/depthwise_conv2d](\n *     https://www.tensorflow.org/api_docs/python/tf/nn/depthwise_conv2d)\n * for more details.\n *\n * @param obj An object with the following properties:\n * @param x The input tensor, of rank 4 or rank 3, of shape\n *     `[batch, height, width, inChannels]`. If rank 3, batch of 1 is\n * assumed.\n * @param filter The filter tensor, rank 4, of shape\n *     `[filterHeight, filterWidth, inChannels, channelMultiplier]`.\n * @param strides The strides of the convolution: `[strideHeight,\n * strideWidth]`. If strides is a single number, then `strideHeight ==\n * strideWidth`.\n * @param pad The type of padding algorithm.\n *   - `same` and stride 1: output will be of same size as input,\n *       regardless of filter size.\n *   - `valid`: output will be smaller than input if filter is larger\n *       than 1x1.\n *   - For more info, see this guide:\n *     [https://www.tensorflow.org/api_docs/python/tf/nn/convolution](\n *          https://www.tensorflow.org/api_docs/python/tf/nn/convolution)\n * @param dilations The dilation rates: `[dilationHeight, dilationWidth]`\n *     in which we sample input values across the height and width dimensions\n *     in atrous convolution. Defaults to `[1, 1]`. If `rate` is a single\n *     number, then `dilationHeight == dilationWidth`. If it is greater than\n *     1, then all values of `strides` must be 1.\n * @param dataFormat: An optional string from: \"NHWC\", \"NCHW\". Defaults to\n *     \"NHWC\". Specify the data format of the input and output data. With the\n *     default format \"NHWC\", the data is stored in the order of: [batch,\n *     height, width, channels]. Only \"NHWC\" is currently supported.\n * @param dimRoundingMode A string from: 'ceil', 'round', 'floor'. If none is\n *     provided, it will default to truncate.\n * @param bias Tensor to be added to the result.\n * @param activation Name of activation kernel (defaults to `linear`).\n * @param preluActivationWeights Tensor of prelu weights to be applied as part\n *     of a `prelu` activation, typically the same shape as `x`.\n * @param leakyreluAlpha Optional. Alpha to be applied as part of a `leakyrelu`\n *     activation.\n */\nfunction fusedDepthwiseConv2d_(_ref) {\n  let {\n    x,\n    filter,\n    strides,\n    pad,\n    dataFormat = 'NHWC',\n    dilations = [1, 1],\n    dimRoundingMode,\n    bias,\n    activation = 'linear',\n    preluActivationWeights,\n    leakyreluAlpha\n  } = _ref;\n  if (shouldFuse(ENGINE.state.gradientDepth, activation) === false) {\n    let result = unfusedDepthwiseConv2d(x, filter, strides, pad, dataFormat, dilations, dimRoundingMode);\n    if (bias != null) {\n      result = add(result, bias);\n    }\n    return applyActivation(result, activation, preluActivationWeights, leakyreluAlpha);\n  }\n  const $x = convertToTensor(x, 'x', 'depthwiseConv2d', 'float32');\n  const $filter = convertToTensor(filter, 'filter', 'depthwiseConv2d', 'float32');\n  let x4D = $x;\n  let reshapedTo4D = false;\n  if ($x.rank === 3) {\n    reshapedTo4D = true;\n    x4D = reshape($x, [1, $x.shape[0], $x.shape[1], $x.shape[2]]);\n  }\n  util.assert(x4D.rank === 4, () => \"Error in fused depthwiseConv2d: input must be rank 4, but got \" + \"rank \".concat(x4D.rank, \".\"));\n  util.assert($filter.rank === 4, () => \"Error in fused depthwiseConv2d: filter must be rank 4, \" + \"but got rank \".concat($filter.rank, \".\"));\n  util.assert(x4D.shape[3] === $filter.shape[2], () => \"Error in fused depthwiseConv2d: number of input channels \" + \"(\".concat(x4D.shape[3], \") must match the inChannels dimension in \") + \"filter \".concat($filter.shape[2], \".\"));\n  if (dilations == null) {\n    dilations = [1, 1];\n  }\n  util.assert(conv_util.eitherStridesOrDilationsAreOne(strides, dilations), () => 'Error in fused depthwiseConv2d: Either strides or dilations must ' + \"be 1. Got strides \".concat(strides, \" and dilations '\").concat(dilations, \"'\"));\n  conv_util.checkPadOnDimRoundingMode('fused depthwiseConv2d', pad, dimRoundingMode);\n  const convInfo = conv_util.computeConv2DInfo(x4D.shape, $filter.shape, strides, dilations, pad, dimRoundingMode, true /* depthwise */);\n  let $bias;\n  if (bias != null) {\n    $bias = convertToTensor(bias, 'bias', 'fused conv2d');\n    [$bias] = makeTypesMatch($bias, $x);\n    broadcast_util.assertAndGetBroadcastShape(convInfo.outShape, $bias.shape);\n  }\n  let $preluActivationWeights;\n  if (preluActivationWeights != null) {\n    $preluActivationWeights = convertToTensor(preluActivationWeights, 'prelu weights', 'fused depthwiseConv2d');\n  }\n  const grad = (dy, saved) => {\n    util.assert(conv_util.tupleValuesAreOne(dilations), () => 'Error in gradient of fused depthwiseConv2d: dilation rates ' + \"greater than 1 are not yet supported. Got dilations \" + \"'\".concat(dilations, \"'\"));\n    const [$filter, x4D, y, bias] = saved;\n    const dyActivation = getFusedDyActivation(dy, y, activation);\n    const xDer = depthwiseConv2dNativeBackpropInput(x4D.shape, dyActivation, $filter, strides, pad, dilations, dimRoundingMode);\n    const filterDer = depthwiseConv2dNativeBackpropFilter(x4D, dyActivation, $filter.shape, strides, pad, dilations, dimRoundingMode);\n    if (bias != null) {\n      const biasDer = getFusedBiasGradient($bias, dyActivation);\n      return [xDer, filterDer, biasDer];\n    }\n    return [xDer, filterDer];\n  };\n  const inputs = {\n    x: x4D,\n    filter: $filter,\n    bias: $bias,\n    preluActivationWeights: $preluActivationWeights\n  };\n  const attrs = {\n    strides,\n    pad,\n    dataFormat,\n    dilations,\n    dimRoundingMode,\n    activation,\n    leakyreluAlpha\n  };\n  // Depending on the the params passed in we will have different number of\n  // inputs and thus a a different number of elements in the gradient.\n  if (bias == null) {\n    const customOp = customGrad((x4D, filter, save) => {\n      // tslint:disable-next-line: no-unnecessary-type-assertion\n      let res = ENGINE.runKernel(FusedDepthwiseConv2D, inputs, attrs);\n      save([filter, x4D, res]);\n      if (reshapedTo4D) {\n        // tslint:disable-next-line: no-unnecessary-type-assertion\n        res = reshape(res, [res.shape[1], res.shape[2], res.shape[3]]);\n      }\n      return {\n        value: res,\n        gradFunc: grad\n      };\n    });\n    return customOp(x4D, $filter);\n  } else {\n    const customOpWithBias = customGrad((x4D, filter, bias, save) => {\n      // tslint:disable-next-line: no-unnecessary-type-assertion\n      let res = ENGINE.runKernel(FusedDepthwiseConv2D, inputs, attrs);\n      save([filter, x4D, res, bias]);\n      if (reshapedTo4D) {\n        // tslint:disable-next-line: no-unnecessary-type-assertion\n        res = reshape(res, [res.shape[1], res.shape[2], res.shape[3]]);\n      }\n      return {\n        value: res,\n        gradFunc: grad\n      };\n    });\n    return customOpWithBias(x4D, $filter, $bias);\n  }\n}\nexport const depthwiseConv2d = /* @__PURE__ */op({\n  fusedDepthwiseConv2d_\n});", "map": {"version": 3, "names": ["ENGINE", "customGrad", "FusedDepthwiseConv2D", "makeTypesMatch", "convertToTensor", "util", "add", "broadcast_util", "conv_util", "depthwiseConv2d", "unfusedDepthwiseConv2d", "depthwiseConv2dNativeBackpropFilter", "depthwiseConv2dNativeBackpropInput", "applyActivation", "getFusedBiasGradient", "getFusedDyActivation", "shouldFuse", "op", "reshape", "fusedDepthwiseConv2d_", "_ref", "x", "filter", "strides", "pad", "dataFormat", "dilations", "dimRoundingMode", "bias", "activation", "preluActivationWeights", "leakyreluAlpha", "state", "gradientDepth", "result", "$x", "$filter", "x4D", "reshapedTo4D", "rank", "shape", "assert", "concat", "eitherStridesOrDilationsAreOne", "checkPadOnDimRoundingMode", "convInfo", "computeConv2DInfo", "$bias", "assertAndGetBroadcastShape", "outShape", "$preluActivationWeights", "grad", "dy", "saved", "tupleValuesAreOne", "y", "dyActivation", "xDer", "filterDer", "bias<PERSON>er", "inputs", "attrs", "customOp", "save", "res", "runKernel", "value", "grad<PERSON>unc", "customOpWithBias"], "sources": ["C:\\tfjs-core\\src\\ops\\fused\\depthwise_conv2d.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2019 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {ENGINE} from '../../engine';\nimport {customGrad} from '../../gradients';\nimport {FusedDepthwiseConv2D, FusedDepthwiseConv2DAttrs, FusedDepthwiseConv2DInputs} from '../../kernel_names';\nimport {NamedAttrMap} from '../../kernel_registry';\nimport {Tensor, Tensor3D, Tensor4D} from '../../tensor';\nimport {GradSaveFunc, NamedTensorMap} from '../../tensor_types';\nimport {makeTypesMatch} from '../../tensor_util';\nimport {convertToTensor} from '../../tensor_util_env';\nimport {TensorLike} from '../../types';\nimport * as util from '../../util';\nimport {add} from '../add';\nimport * as broadcast_util from '../broadcast_util';\nimport * as conv_util from '../conv_util';\nimport {depthwiseConv2d as unfusedDepthwiseConv2d} from '../depthwise_conv2d';\nimport {depthwiseConv2dNativeBackpropFilter} from '../depthwise_conv2d_native_backprop_filter';\nimport {depthwiseConv2dNativeBackpropInput} from '../depthwise_conv2d_native_backprop_input';\nimport {Activation} from '../fused_types';\nimport {applyActivation, getFusedBiasGradient, getFusedDyActivation, shouldFuse} from '../fused_util';\nimport {op} from '../operation';\nimport {reshape} from '../reshape';\n\n/**\n * Computes depthwise 2D convolution, optionally fused with adding a\n * bias and applying an activation.\n *\n * Given a 4D `input` array and a `filter` array of shape\n * `[filterHeight, filterWidth, inChannels, channelMultiplier]` containing\n * `inChannels` convolutional filters of depth 1, this op applies a\n * different filter to each input channel (expanding from 1 channel to\n * `channelMultiplier` channels for each), then concatenates the results\n * together. The output has `inChannels * channelMultiplier` channels.\n *\n * See\n * [https://www.tensorflow.org/api_docs/python/tf/nn/depthwise_conv2d](\n *     https://www.tensorflow.org/api_docs/python/tf/nn/depthwise_conv2d)\n * for more details.\n *\n * @param obj An object with the following properties:\n * @param x The input tensor, of rank 4 or rank 3, of shape\n *     `[batch, height, width, inChannels]`. If rank 3, batch of 1 is\n * assumed.\n * @param filter The filter tensor, rank 4, of shape\n *     `[filterHeight, filterWidth, inChannels, channelMultiplier]`.\n * @param strides The strides of the convolution: `[strideHeight,\n * strideWidth]`. If strides is a single number, then `strideHeight ==\n * strideWidth`.\n * @param pad The type of padding algorithm.\n *   - `same` and stride 1: output will be of same size as input,\n *       regardless of filter size.\n *   - `valid`: output will be smaller than input if filter is larger\n *       than 1x1.\n *   - For more info, see this guide:\n *     [https://www.tensorflow.org/api_docs/python/tf/nn/convolution](\n *          https://www.tensorflow.org/api_docs/python/tf/nn/convolution)\n * @param dilations The dilation rates: `[dilationHeight, dilationWidth]`\n *     in which we sample input values across the height and width dimensions\n *     in atrous convolution. Defaults to `[1, 1]`. If `rate` is a single\n *     number, then `dilationHeight == dilationWidth`. If it is greater than\n *     1, then all values of `strides` must be 1.\n * @param dataFormat: An optional string from: \"NHWC\", \"NCHW\". Defaults to\n *     \"NHWC\". Specify the data format of the input and output data. With the\n *     default format \"NHWC\", the data is stored in the order of: [batch,\n *     height, width, channels]. Only \"NHWC\" is currently supported.\n * @param dimRoundingMode A string from: 'ceil', 'round', 'floor'. If none is\n *     provided, it will default to truncate.\n * @param bias Tensor to be added to the result.\n * @param activation Name of activation kernel (defaults to `linear`).\n * @param preluActivationWeights Tensor of prelu weights to be applied as part\n *     of a `prelu` activation, typically the same shape as `x`.\n * @param leakyreluAlpha Optional. Alpha to be applied as part of a `leakyrelu`\n *     activation.\n */\nfunction fusedDepthwiseConv2d_<T extends Tensor3D|Tensor4D>({\n  x,\n  filter,\n  strides,\n  pad,\n  dataFormat = 'NHWC',\n  dilations = [1, 1],\n  dimRoundingMode,\n  bias,\n  activation = 'linear',\n  preluActivationWeights,\n  leakyreluAlpha\n}: {\n  x: T|TensorLike,\n  filter: Tensor4D|TensorLike,\n  strides: [number, number]|number,\n  pad: 'valid'|'same'|number,\n  dataFormat?: 'NHWC'|'NCHW',\n  dilations?: [number, number]|number,\n  dimRoundingMode?: 'floor'|'round'|'ceil',\n  bias?: Tensor|TensorLike,\n  activation?: Activation,\n  preluActivationWeights?: Tensor,\n  leakyreluAlpha?: number\n}): T {\n  if (shouldFuse(ENGINE.state.gradientDepth, activation) === false) {\n    let result = unfusedDepthwiseConv2d(\n        x, filter, strides, pad, dataFormat, dilations, dimRoundingMode);\n    if (bias != null) {\n      result = add(result, bias);\n    }\n\n    return applyActivation(\n               result, activation, preluActivationWeights, leakyreluAlpha) as T;\n  }\n\n  const $x = convertToTensor(x, 'x', 'depthwiseConv2d', 'float32');\n  const $filter =\n      convertToTensor(filter, 'filter', 'depthwiseConv2d', 'float32');\n\n  let x4D = $x as Tensor4D;\n  let reshapedTo4D = false;\n  if ($x.rank === 3) {\n    reshapedTo4D = true;\n    x4D = reshape($x, [1, $x.shape[0], $x.shape[1], $x.shape[2]]);\n  }\n  util.assert(\n      x4D.rank === 4,\n      () => `Error in fused depthwiseConv2d: input must be rank 4, but got ` +\n          `rank ${x4D.rank}.`);\n  util.assert(\n      $filter.rank === 4,\n      () => `Error in fused depthwiseConv2d: filter must be rank 4, ` +\n          `but got rank ${$filter.rank}.`);\n  util.assert(\n      x4D.shape[3] === $filter.shape[2],\n      () => `Error in fused depthwiseConv2d: number of input channels ` +\n          `(${x4D.shape[3]}) must match the inChannels dimension in ` +\n          `filter ${$filter.shape[2]}.`);\n  if (dilations == null) {\n    dilations = [1, 1];\n  }\n  util.assert(\n      conv_util.eitherStridesOrDilationsAreOne(strides, dilations),\n      () =>\n          'Error in fused depthwiseConv2d: Either strides or dilations must ' +\n          `be 1. Got strides ${strides} and dilations '${dilations}'`);\n  conv_util.checkPadOnDimRoundingMode(\n      'fused depthwiseConv2d', pad, dimRoundingMode);\n  const convInfo = conv_util.computeConv2DInfo(\n      x4D.shape, $filter.shape, strides, dilations, pad, dimRoundingMode,\n      true /* depthwise */);\n\n  let $bias: Tensor;\n  if (bias != null) {\n    $bias = convertToTensor(bias, 'bias', 'fused conv2d');\n    [$bias] = makeTypesMatch($bias, $x);\n\n    broadcast_util.assertAndGetBroadcastShape(convInfo.outShape, $bias.shape);\n  }\n\n  let $preluActivationWeights: Tensor;\n  if (preluActivationWeights != null) {\n    $preluActivationWeights = convertToTensor(\n        preluActivationWeights, 'prelu weights', 'fused depthwiseConv2d');\n  }\n\n  const grad = (dy: Tensor4D, saved: Tensor[]) => {\n    util.assert(\n        conv_util.tupleValuesAreOne(dilations),\n        () => 'Error in gradient of fused depthwiseConv2d: dilation rates ' +\n            `greater than 1 are not yet supported. Got dilations ` +\n            `'${dilations}'`);\n    const [$filter, x4D, y, bias] = saved;\n\n    const dyActivation = getFusedDyActivation(dy, y, activation) as Tensor4D;\n\n    const xDer = depthwiseConv2dNativeBackpropInput(\n        (x4D as Tensor4D).shape, dyActivation, $filter as Tensor4D, strides,\n        pad, dilations, dimRoundingMode);\n    const filterDer = depthwiseConv2dNativeBackpropFilter(\n        x4D as Tensor4D, dyActivation, ($filter as Tensor4D).shape, strides,\n        pad, dilations, dimRoundingMode);\n\n    if (bias != null) {\n      const biasDer = getFusedBiasGradient($bias, dyActivation);\n      return [xDer, filterDer, biasDer];\n    }\n    return [xDer, filterDer];\n  };\n\n  const inputs: FusedDepthwiseConv2DInputs = {\n    x: x4D,\n    filter: $filter,\n    bias: $bias,\n    preluActivationWeights: $preluActivationWeights\n  };\n  const attrs: FusedDepthwiseConv2DAttrs = {\n    strides,\n    pad,\n    dataFormat,\n    dilations,\n    dimRoundingMode,\n    activation,\n    leakyreluAlpha\n  };\n\n  // Depending on the the params passed in we will have different number of\n  // inputs and thus a a different number of elements in the gradient.\n  if (bias == null) {\n    const customOp =\n        customGrad((x4D: Tensor4D, filter: Tensor4D, save: GradSaveFunc) => {\n          // tslint:disable-next-line: no-unnecessary-type-assertion\n          let res: Tensor4D|Tensor3D = ENGINE.runKernel(\n              FusedDepthwiseConv2D, inputs as unknown as NamedTensorMap,\n              attrs as unknown as NamedAttrMap);\n\n          save([filter, x4D, res]);\n\n          if (reshapedTo4D) {\n            // tslint:disable-next-line: no-unnecessary-type-assertion\n            res = reshape(res, [res.shape[1], res.shape[2], res.shape[3]]) as\n                Tensor3D;\n          }\n\n          return {value: res, gradFunc: grad};\n        });\n    return customOp(x4D, $filter) as T;\n  } else {\n    const customOpWithBias = customGrad(\n        (x4D: Tensor4D, filter: Tensor4D, bias: Tensor, save: GradSaveFunc) => {\n          // tslint:disable-next-line: no-unnecessary-type-assertion\n          let res: Tensor4D|Tensor3D = ENGINE.runKernel(\n              FusedDepthwiseConv2D, inputs as unknown as NamedTensorMap,\n              attrs as unknown as NamedAttrMap);\n\n          save([filter, x4D, res, bias]);\n\n          if (reshapedTo4D) {\n            // tslint:disable-next-line: no-unnecessary-type-assertion\n            res = reshape(res, [res.shape[1], res.shape[2], res.shape[3]]) as\n                Tensor3D;\n          }\n\n          return {value: res, gradFunc: grad};\n        });\n\n    return customOpWithBias(x4D, $filter, $bias) as T;\n  }\n}\nexport const depthwiseConv2d = /* @__PURE__ */ op({fusedDepthwiseConv2d_});\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAiBA,SAAQA,MAAM,QAAO,cAAc;AACnC,SAAQC,UAAU,QAAO,iBAAiB;AAC1C,SAAQC,oBAAoB,QAA8D,oBAAoB;AAI9G,SAAQC,cAAc,QAAO,mBAAmB;AAChD,SAAQC,eAAe,QAAO,uBAAuB;AAErD,OAAO,KAAKC,IAAI,MAAM,YAAY;AAClC,SAAQC,GAAG,QAAO,QAAQ;AAC1B,OAAO,KAAKC,cAAc,MAAM,mBAAmB;AACnD,OAAO,KAAKC,SAAS,MAAM,cAAc;AACzC,SAAQC,eAAe,IAAIC,sBAAsB,QAAO,qBAAqB;AAC7E,SAAQC,mCAAmC,QAAO,4CAA4C;AAC9F,SAAQC,kCAAkC,QAAO,2CAA2C;AAE5F,SAAQC,eAAe,EAAEC,oBAAoB,EAAEC,oBAAoB,EAAEC,UAAU,QAAO,eAAe;AACrG,SAAQC,EAAE,QAAO,cAAc;AAC/B,SAAQC,OAAO,QAAO,YAAY;AAElC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmDA,SAASC,qBAAqBA,CAAAC,IAAA,EAwB7B;EAAA,IAxB2D;IAC1DC,CAAC;IACDC,MAAM;IACNC,OAAO;IACPC,GAAG;IACHC,UAAU,GAAG,MAAM;IACnBC,SAAS,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;IAClBC,eAAe;IACfC,IAAI;IACJC,UAAU,GAAG,QAAQ;IACrBC,sBAAsB;IACtBC;EAAc,CAaf,GAAAX,IAAA;EACC,IAAIJ,UAAU,CAAChB,MAAM,CAACgC,KAAK,CAACC,aAAa,EAAEJ,UAAU,CAAC,KAAK,KAAK,EAAE;IAChE,IAAIK,MAAM,GAAGxB,sBAAsB,CAC/BW,CAAC,EAAEC,MAAM,EAAEC,OAAO,EAAEC,GAAG,EAAEC,UAAU,EAAEC,SAAS,EAAEC,eAAe,CAAC;IACpE,IAAIC,IAAI,IAAI,IAAI,EAAE;MAChBM,MAAM,GAAG5B,GAAG,CAAC4B,MAAM,EAAEN,IAAI,CAAC;;IAG5B,OAAOf,eAAe,CACXqB,MAAM,EAAEL,UAAU,EAAEC,sBAAsB,EAAEC,cAAc,CAAM;;EAG7E,MAAMI,EAAE,GAAG/B,eAAe,CAACiB,CAAC,EAAE,GAAG,EAAE,iBAAiB,EAAE,SAAS,CAAC;EAChE,MAAMe,OAAO,GACThC,eAAe,CAACkB,MAAM,EAAE,QAAQ,EAAE,iBAAiB,EAAE,SAAS,CAAC;EAEnE,IAAIe,GAAG,GAAGF,EAAc;EACxB,IAAIG,YAAY,GAAG,KAAK;EACxB,IAAIH,EAAE,CAACI,IAAI,KAAK,CAAC,EAAE;IACjBD,YAAY,GAAG,IAAI;IACnBD,GAAG,GAAGnB,OAAO,CAACiB,EAAE,EAAE,CAAC,CAAC,EAAEA,EAAE,CAACK,KAAK,CAAC,CAAC,CAAC,EAAEL,EAAE,CAACK,KAAK,CAAC,CAAC,CAAC,EAAEL,EAAE,CAACK,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;;EAE/DnC,IAAI,CAACoC,MAAM,CACPJ,GAAG,CAACE,IAAI,KAAK,CAAC,EACd,MAAM,2EAAAG,MAAA,CACML,GAAG,CAACE,IAAI,MAAG,CAAC;EAC5BlC,IAAI,CAACoC,MAAM,CACPL,OAAO,CAACG,IAAI,KAAK,CAAC,EAClB,MAAM,4EAAAG,MAAA,CACcN,OAAO,CAACG,IAAI,MAAG,CAAC;EACxClC,IAAI,CAACoC,MAAM,CACPJ,GAAG,CAACG,KAAK,CAAC,CAAC,CAAC,KAAKJ,OAAO,CAACI,KAAK,CAAC,CAAC,CAAC,EACjC,MAAM,kEAAAE,MAAA,CACEL,GAAG,CAACG,KAAK,CAAC,CAAC,CAAC,8CAA2C,aAAAE,MAAA,CACjDN,OAAO,CAACI,KAAK,CAAC,CAAC,CAAC,MAAG,CAAC;EACtC,IAAId,SAAS,IAAI,IAAI,EAAE;IACrBA,SAAS,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;;EAEpBrB,IAAI,CAACoC,MAAM,CACPjC,SAAS,CAACmC,8BAA8B,CAACpB,OAAO,EAAEG,SAAS,CAAC,EAC5D,MACI,mEAAmE,wBAAAgB,MAAA,CAC9CnB,OAAO,sBAAAmB,MAAA,CAAmBhB,SAAS,MAAG,CAAC;EACpElB,SAAS,CAACoC,yBAAyB,CAC/B,uBAAuB,EAAEpB,GAAG,EAAEG,eAAe,CAAC;EAClD,MAAMkB,QAAQ,GAAGrC,SAAS,CAACsC,iBAAiB,CACxCT,GAAG,CAACG,KAAK,EAAEJ,OAAO,CAACI,KAAK,EAAEjB,OAAO,EAAEG,SAAS,EAAEF,GAAG,EAAEG,eAAe,EAClE,IAAI,CAAC,eAAe,CAAC;EAEzB,IAAIoB,KAAa;EACjB,IAAInB,IAAI,IAAI,IAAI,EAAE;IAChBmB,KAAK,GAAG3C,eAAe,CAACwB,IAAI,EAAE,MAAM,EAAE,cAAc,CAAC;IACrD,CAACmB,KAAK,CAAC,GAAG5C,cAAc,CAAC4C,KAAK,EAAEZ,EAAE,CAAC;IAEnC5B,cAAc,CAACyC,0BAA0B,CAACH,QAAQ,CAACI,QAAQ,EAAEF,KAAK,CAACP,KAAK,CAAC;;EAG3E,IAAIU,uBAA+B;EACnC,IAAIpB,sBAAsB,IAAI,IAAI,EAAE;IAClCoB,uBAAuB,GAAG9C,eAAe,CACrC0B,sBAAsB,EAAE,eAAe,EAAE,uBAAuB,CAAC;;EAGvE,MAAMqB,IAAI,GAAGA,CAACC,EAAY,EAAEC,KAAe,KAAI;IAC7ChD,IAAI,CAACoC,MAAM,CACPjC,SAAS,CAAC8C,iBAAiB,CAAC5B,SAAS,CAAC,EACtC,MAAM,6DAA6D,yDACT,OAAAgB,MAAA,CAClDhB,SAAS,MAAG,CAAC;IACzB,MAAM,CAACU,OAAO,EAAEC,GAAG,EAAEkB,CAAC,EAAE3B,IAAI,CAAC,GAAGyB,KAAK;IAErC,MAAMG,YAAY,GAAGzC,oBAAoB,CAACqC,EAAE,EAAEG,CAAC,EAAE1B,UAAU,CAAa;IAExE,MAAM4B,IAAI,GAAG7C,kCAAkC,CAC1CyB,GAAgB,CAACG,KAAK,EAAEgB,YAAY,EAAEpB,OAAmB,EAAEb,OAAO,EACnEC,GAAG,EAAEE,SAAS,EAAEC,eAAe,CAAC;IACpC,MAAM+B,SAAS,GAAG/C,mCAAmC,CACjD0B,GAAe,EAAEmB,YAAY,EAAGpB,OAAoB,CAACI,KAAK,EAAEjB,OAAO,EACnEC,GAAG,EAAEE,SAAS,EAAEC,eAAe,CAAC;IAEpC,IAAIC,IAAI,IAAI,IAAI,EAAE;MAChB,MAAM+B,OAAO,GAAG7C,oBAAoB,CAACiC,KAAK,EAAES,YAAY,CAAC;MACzD,OAAO,CAACC,IAAI,EAAEC,SAAS,EAAEC,OAAO,CAAC;;IAEnC,OAAO,CAACF,IAAI,EAAEC,SAAS,CAAC;EAC1B,CAAC;EAED,MAAME,MAAM,GAA+B;IACzCvC,CAAC,EAAEgB,GAAG;IACNf,MAAM,EAAEc,OAAO;IACfR,IAAI,EAAEmB,KAAK;IACXjB,sBAAsB,EAAEoB;GACzB;EACD,MAAMW,KAAK,GAA8B;IACvCtC,OAAO;IACPC,GAAG;IACHC,UAAU;IACVC,SAAS;IACTC,eAAe;IACfE,UAAU;IACVE;GACD;EAED;EACA;EACA,IAAIH,IAAI,IAAI,IAAI,EAAE;IAChB,MAAMkC,QAAQ,GACV7D,UAAU,CAAC,CAACoC,GAAa,EAAEf,MAAgB,EAAEyC,IAAkB,KAAI;MACjE;MACA,IAAIC,GAAG,GAAsBhE,MAAM,CAACiE,SAAS,CACzC/D,oBAAoB,EAAE0D,MAAmC,EACzDC,KAAgC,CAAC;MAErCE,IAAI,CAAC,CAACzC,MAAM,EAAEe,GAAG,EAAE2B,GAAG,CAAC,CAAC;MAExB,IAAI1B,YAAY,EAAE;QAChB;QACA0B,GAAG,GAAG9C,OAAO,CAAC8C,GAAG,EAAE,CAACA,GAAG,CAACxB,KAAK,CAAC,CAAC,CAAC,EAAEwB,GAAG,CAACxB,KAAK,CAAC,CAAC,CAAC,EAAEwB,GAAG,CAACxB,KAAK,CAAC,CAAC,CAAC,CAAC,CACjD;;MAGd,OAAO;QAAC0B,KAAK,EAAEF,GAAG;QAAEG,QAAQ,EAAEhB;MAAI,CAAC;IACrC,CAAC,CAAC;IACN,OAAOW,QAAQ,CAACzB,GAAG,EAAED,OAAO,CAAM;GACnC,MAAM;IACL,MAAMgC,gBAAgB,GAAGnE,UAAU,CAC/B,CAACoC,GAAa,EAAEf,MAAgB,EAAEM,IAAY,EAAEmC,IAAkB,KAAI;MACpE;MACA,IAAIC,GAAG,GAAsBhE,MAAM,CAACiE,SAAS,CACzC/D,oBAAoB,EAAE0D,MAAmC,EACzDC,KAAgC,CAAC;MAErCE,IAAI,CAAC,CAACzC,MAAM,EAAEe,GAAG,EAAE2B,GAAG,EAAEpC,IAAI,CAAC,CAAC;MAE9B,IAAIU,YAAY,EAAE;QAChB;QACA0B,GAAG,GAAG9C,OAAO,CAAC8C,GAAG,EAAE,CAACA,GAAG,CAACxB,KAAK,CAAC,CAAC,CAAC,EAAEwB,GAAG,CAACxB,KAAK,CAAC,CAAC,CAAC,EAAEwB,GAAG,CAACxB,KAAK,CAAC,CAAC,CAAC,CAAC,CACjD;;MAGd,OAAO;QAAC0B,KAAK,EAAEF,GAAG;QAAEG,QAAQ,EAAEhB;MAAI,CAAC;IACrC,CAAC,CAAC;IAEN,OAAOiB,gBAAgB,CAAC/B,GAAG,EAAED,OAAO,EAAEW,KAAK,CAAM;;AAErD;AACA,OAAO,MAAMtC,eAAe,GAAG,eAAgBQ,EAAE,CAAC;EAACE;AAAqB,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}