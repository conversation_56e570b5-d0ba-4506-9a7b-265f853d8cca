{"ast": null, "code": "/**\n * @license\n * Copyright 2021 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { ENGINE } from '../../engine';\nimport { StringSplit } from '../../kernel_names';\nimport { convertToTensor } from '../../tensor_util_env';\nimport { op } from '../operation';\n/**\n * Split elements of `input` based on `delimiter` into a SparseTensor .\n *\n * Let N be the size of source (typically N will be the batch size). Split each\n * element of `input` based on `delimiter` and return a SparseTensor containing\n * the splitted tokens. Empty tokens are ignored if `skipEmpty` is set to True.\n *\n * `delimiter` can be empty, or a string of split characters. If `delimiter` is\n * an empty string, each element of `input` is split into individual\n * character strings. Otherwise every character of `delimiter` is a potential\n * split point.\n *\n * ```js\n * const result = tf.string.stringSplit(['hello world',  'a b c'], ' ');\n * result['indices'].print(); // [[0, 0], [0, 1], [1, 0], [1, 1], [1, 2]]\n * result['values'].print(); // ['hello', 'world', 'a', 'b', 'c']\n * result['shape'].print(); // [2, 3]\n * ```\n * @param input: 1-D. Strings to split.\n * @param delimiter: 0-D. Delimiter characters, or empty string.\n * @param skipEmpty: Optional. If true, skip the empty strings from the result.\n *     Defaults to true.\n * @return A map with the following properties:\n *     - indices: A dense matrix of int32 representing the indices of the sparse\n *       tensor.\n *     - values: A vector of strings corresponding to the splited values.\n *     - shape: a length-2 vector of int32 representing the shape of the sparse\n * tensor, where the first value is N and the second value is the maximum number\n * of tokens in a single input entry.\n *\n * @doc {heading: 'Operations', subheading: 'String'}\n */\nfunction stringSplit_(input, delimiter, skipEmpty = true) {\n  const $input = convertToTensor(input, 'input', 'stringSplit', 'string');\n  const $delimiter = convertToTensor(delimiter, 'delimiter', 'stringSplit', 'string');\n  if ($input.rank !== 1) {\n    throw new Error(`Input should be Tensor1D but received shape ${$input.shape}`);\n  }\n  if ($delimiter.rank !== 0) {\n    throw new Error(`Delimiter should be a scalar but received shape ${$delimiter.shape}`);\n  }\n  const attrs = {\n    skipEmpty\n  };\n  const inputs = {\n    input: $input,\n    delimiter: $delimiter\n  };\n  const result = ENGINE.runKernel(StringSplit, inputs, attrs);\n  return {\n    indices: result[0],\n    values: result[1],\n    shape: result[2]\n  };\n}\nexport const stringSplit = /* @__PURE__ */op({\n  stringSplit_\n});", "map": {"version": 3, "names": ["ENGINE", "StringSplit", "convertToTensor", "op", "stringSplit_", "input", "delimiter", "<PERSON><PERSON><PERSON><PERSON>", "$input", "$delimiter", "rank", "Error", "shape", "attrs", "inputs", "result", "runKernel", "indices", "values", "stringSplit"], "sources": ["C:\\tfjs-core\\src\\ops\\string\\string_split.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2021 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {ENGINE} from '../../engine';\nimport {StringSplit, StringSplitAttrs, StringSplitInputs} from '../../kernel_names';\nimport {Scalar, Tensor, Tensor1D} from '../../tensor';\nimport {NamedTensorMap} from '../../tensor_types';\nimport {convertToTensor} from '../../tensor_util_env';\nimport {ScalarLike, TensorLike} from '../../types';\nimport {op} from '../operation';\n\n/**\n * Split elements of `input` based on `delimiter` into a SparseTensor .\n *\n * Let N be the size of source (typically N will be the batch size). Split each\n * element of `input` based on `delimiter` and return a SparseTensor containing\n * the splitted tokens. Empty tokens are ignored if `skipEmpty` is set to True.\n *\n * `delimiter` can be empty, or a string of split characters. If `delimiter` is\n * an empty string, each element of `input` is split into individual\n * character strings. Otherwise every character of `delimiter` is a potential\n * split point.\n *\n * ```js\n * const result = tf.string.stringSplit(['hello world',  'a b c'], ' ');\n * result['indices'].print(); // [[0, 0], [0, 1], [1, 0], [1, 1], [1, 2]]\n * result['values'].print(); // ['hello', 'world', 'a', 'b', 'c']\n * result['shape'].print(); // [2, 3]\n * ```\n * @param input: 1-D. Strings to split.\n * @param delimiter: 0-D. Delimiter characters, or empty string.\n * @param skipEmpty: Optional. If true, skip the empty strings from the result.\n *     Defaults to true.\n * @return A map with the following properties:\n *     - indices: A dense matrix of int32 representing the indices of the sparse\n *       tensor.\n *     - values: A vector of strings corresponding to the splited values.\n *     - shape: a length-2 vector of int32 representing the shape of the sparse\n * tensor, where the first value is N and the second value is the maximum number\n * of tokens in a single input entry.\n *\n * @doc {heading: 'Operations', subheading: 'String'}\n */\nfunction stringSplit_(\n    input: Tensor1D|TensorLike, delimiter: Scalar|ScalarLike,\n    skipEmpty = true): NamedTensorMap {\n  const $input = convertToTensor(input, 'input', 'stringSplit', 'string');\n  const $delimiter =\n      convertToTensor(delimiter, 'delimiter', 'stringSplit', 'string');\n\n  if ($input.rank !== 1) {\n    throw new Error(\n        `Input should be Tensor1D but received shape ${$input.shape}`);\n  }\n  if ($delimiter.rank !== 0) {\n    throw new Error(\n        `Delimiter should be a scalar but received shape ${$delimiter.shape}`);\n  }\n\n  const attrs: StringSplitAttrs = {skipEmpty};\n  const inputs: StringSplitInputs = {input: $input, delimiter: $delimiter};\n  const result: Tensor[] =\n      ENGINE.runKernel(StringSplit, inputs as {}, attrs as {});\n  return {indices: result[0], values: result[1], shape: result[2]};\n}\n\nexport const stringSplit = /* @__PURE__ */ op({stringSplit_});\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAiBA,SAAQA,MAAM,QAAO,cAAc;AACnC,SAAQC,WAAW,QAA4C,oBAAoB;AAGnF,SAAQC,eAAe,QAAO,uBAAuB;AAErD,SAAQC,EAAE,QAAO,cAAc;AAE/B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgCA,SAASC,YAAYA,CACjBC,KAA0B,EAAEC,SAA4B,EACxDC,SAAS,GAAG,IAAI;EAClB,MAAMC,MAAM,GAAGN,eAAe,CAACG,KAAK,EAAE,OAAO,EAAE,aAAa,EAAE,QAAQ,CAAC;EACvE,MAAMI,UAAU,GACZP,eAAe,CAACI,SAAS,EAAE,WAAW,EAAE,aAAa,EAAE,QAAQ,CAAC;EAEpE,IAAIE,MAAM,CAACE,IAAI,KAAK,CAAC,EAAE;IACrB,MAAM,IAAIC,KAAK,CACX,+CAA+CH,MAAM,CAACI,KAAK,EAAE,CAAC;;EAEpE,IAAIH,UAAU,CAACC,IAAI,KAAK,CAAC,EAAE;IACzB,MAAM,IAAIC,KAAK,CACX,mDAAmDF,UAAU,CAACG,KAAK,EAAE,CAAC;;EAG5E,MAAMC,KAAK,GAAqB;IAACN;EAAS,CAAC;EAC3C,MAAMO,MAAM,GAAsB;IAACT,KAAK,EAAEG,MAAM;IAAEF,SAAS,EAAEG;EAAU,CAAC;EACxE,MAAMM,MAAM,GACRf,MAAM,CAACgB,SAAS,CAACf,WAAW,EAAEa,MAAY,EAAED,KAAW,CAAC;EAC5D,OAAO;IAACI,OAAO,EAAEF,MAAM,CAAC,CAAC,CAAC;IAAEG,MAAM,EAAEH,MAAM,CAAC,CAAC,CAAC;IAAEH,KAAK,EAAEG,MAAM,CAAC,CAAC;EAAC,CAAC;AAClE;AAEA,OAAO,MAAMI,WAAW,GAAG,eAAgBhB,EAAE,CAAC;EAACC;AAAY,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}