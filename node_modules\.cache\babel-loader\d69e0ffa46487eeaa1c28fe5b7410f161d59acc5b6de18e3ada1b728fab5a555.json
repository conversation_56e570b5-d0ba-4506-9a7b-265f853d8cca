{"ast": null, "code": "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { ENGINE } from '../engine';\nimport { PadV2 } from '../kernel_names';\nimport { convertToTensor } from '../tensor_util_env';\nimport { op } from './operation';\n/**\n * Pads a `tf.Tensor` with a given value and paddings.\n *\n * This operation implements `CONSTANT` mode. For `REFLECT` and `SYMMETRIC`,\n * refer to `tf.mirrorPad`.\n *\n * Also available are stricter rank-specific methods with the same signature\n * as this method that assert that `paddings` is of given length.\n *   - `tf.pad1d`\n *   - `tf.pad2d`\n *   - `tf.pad3d`\n *   - `tf.pad4d`\n *\n * ```js\n * const x = tf.tensor1d([1, 2, 3, 4]);\n * x.pad([[1, 2]]).print();\n * ```\n * @param x The tensor to pad.\n * @param paddings An array of length `R` (the rank of the tensor), where\n * each element is a length-2 tuple of ints `[padBefore, padAfter]`,\n * specifying how much to pad along each dimension of the tensor.\n * @param constantValue The pad value to use. Defaults to 0.\n *\n * @doc {heading: 'Tensors', subheading: 'Transformations'}\n */\nfunction pad_(x, paddings, constantValue = 0) {\n  const $x = convertToTensor(x, 'x', 'pad');\n  if ($x.rank === 0) {\n    throw new Error('pad(scalar) is not defined. Pass non-scalar to pad');\n  }\n  const attrs = {\n    paddings,\n    constantValue\n  };\n  const inputs = {\n    x: $x\n  };\n  return ENGINE.runKernel(PadV2, inputs, attrs);\n}\nexport const pad = /* @__PURE__ */op({\n  pad_\n});", "map": {"version": 3, "names": ["ENGINE", "PadV2", "convertToTensor", "op", "pad_", "x", "paddings", "constantV<PERSON>ue", "$x", "rank", "Error", "attrs", "inputs", "runKernel", "pad"], "sources": ["C:\\tfjs-core\\src\\ops\\pad.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {ENGINE} from '../engine';\nimport {PadV2, PadV2Attrs, PadV2Inputs} from '../kernel_names';\nimport {NamedAttrMap} from '../kernel_registry';\nimport {Tensor} from '../tensor';\nimport {NamedTensorMap} from '../tensor_types';\nimport {convertToTensor} from '../tensor_util_env';\nimport {TensorLike} from '../types';\n\nimport {op} from './operation';\n\n/**\n * Pads a `tf.Tensor` with a given value and paddings.\n *\n * This operation implements `CONSTANT` mode. For `REFLECT` and `SYMMETRIC`,\n * refer to `tf.mirrorPad`.\n *\n * Also available are stricter rank-specific methods with the same signature\n * as this method that assert that `paddings` is of given length.\n *   - `tf.pad1d`\n *   - `tf.pad2d`\n *   - `tf.pad3d`\n *   - `tf.pad4d`\n *\n * ```js\n * const x = tf.tensor1d([1, 2, 3, 4]);\n * x.pad([[1, 2]]).print();\n * ```\n * @param x The tensor to pad.\n * @param paddings An array of length `R` (the rank of the tensor), where\n * each element is a length-2 tuple of ints `[padBefore, padAfter]`,\n * specifying how much to pad along each dimension of the tensor.\n * @param constantValue The pad value to use. Defaults to 0.\n *\n * @doc {heading: 'Tensors', subheading: 'Transformations'}\n */\nfunction pad_<T extends Tensor>(\n    x: T|TensorLike, paddings: Array<[number, number]>, constantValue = 0): T {\n  const $x = convertToTensor(x, 'x', 'pad');\n  if ($x.rank === 0) {\n    throw new Error('pad(scalar) is not defined. Pass non-scalar to pad');\n  }\n\n  const attrs: PadV2Attrs = {paddings, constantValue};\n  const inputs: PadV2Inputs = {x: $x};\n  return ENGINE.runKernel(\n      PadV2, inputs as unknown as NamedTensorMap,\n      attrs as unknown as NamedAttrMap);\n}\n\nexport const pad = /* @__PURE__ */ op({pad_});\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAiBA,SAAQA,MAAM,QAAO,WAAW;AAChC,SAAQC,KAAK,QAAgC,iBAAiB;AAI9D,SAAQC,eAAe,QAAO,oBAAoB;AAGlD,SAAQC,EAAE,QAAO,aAAa;AAE9B;;;;;;;;;;;;;;;;;;;;;;;;;AAyBA,SAASC,IAAIA,CACTC,CAAe,EAAEC,QAAiC,EAAEC,aAAa,GAAG,CAAC;EACvE,MAAMC,EAAE,GAAGN,eAAe,CAACG,CAAC,EAAE,GAAG,EAAE,KAAK,CAAC;EACzC,IAAIG,EAAE,CAACC,IAAI,KAAK,CAAC,EAAE;IACjB,MAAM,IAAIC,KAAK,CAAC,oDAAoD,CAAC;;EAGvE,MAAMC,KAAK,GAAe;IAACL,QAAQ;IAAEC;EAAa,CAAC;EACnD,MAAMK,MAAM,GAAgB;IAACP,CAAC,EAAEG;EAAE,CAAC;EACnC,OAAOR,MAAM,CAACa,SAAS,CACnBZ,KAAK,EAAEW,MAAmC,EAC1CD,KAAgC,CAAC;AACvC;AAEA,OAAO,MAAMG,GAAG,GAAG,eAAgBX,EAAE,CAAC;EAACC;AAAI,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}