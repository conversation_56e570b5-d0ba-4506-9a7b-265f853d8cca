{"ast": null, "code": "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { DepthToSpace } from '@tensorflow/tfjs-core';\nimport { DepthToSpaceProgram } from '../depth_to_space_gpu';\nexport function depthToSpace(args) {\n  const {\n    inputs,\n    backend,\n    attrs\n  } = args;\n  const {\n    x\n  } = inputs;\n  const {\n    blockSize,\n    dataFormat\n  } = attrs;\n  const batchSize = x.shape[0];\n  const inputHeight = dataFormat === 'NHWC' ? x.shape[1] : x.shape[2];\n  const inputWidth = dataFormat === 'NHWC' ? x.shape[2] : x.shape[3];\n  const inputDepth = dataFormat === 'NHWC' ? x.shape[3] : x.shape[1];\n  const outputHeight = inputHeight * blockSize;\n  const outputWidth = inputWidth * blockSize;\n  const outputDepth = inputDepth / (blockSize * blockSize);\n  const outputShape = dataFormat === 'NHWC' ? [batchSize, outputHeight, outputWidth, outputDepth] : [batchSize, outputDepth, outputHeight, outputWidth];\n  const program = new DepthToSpaceProgram(outputShape, blockSize, dataFormat);\n  return backend.runWebGLProgram(program, [x], x.dtype);\n}\nexport const depthToSpaceConfig = {\n  kernelName: DepthToSpace,\n  backendName: 'webgl',\n  kernelFunc: depthToSpace\n};", "map": {"version": 3, "names": ["DepthToSpace", "DepthToSpaceProgram", "depthToSpace", "args", "inputs", "backend", "attrs", "x", "blockSize", "dataFormat", "batchSize", "shape", "inputHeight", "inputWidth", "inputDepth", "outputHeight", "outputWidth", "outputDepth", "outputShape", "program", "runWebGLProgram", "dtype", "depthToSpaceConfig", "kernelName", "backendName", "kernelFunc"], "sources": ["C:\\tfjs-backend-webgl\\src\\kernels\\DepthToSpace.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {DepthToSpace, DepthToSpaceAttrs, DepthToSpaceInputs, KernelConfig, KernelFunc, TensorInfo} from '@tensorflow/tfjs-core';\n\nimport {MathBackendWebGL} from '../backend_webgl';\nimport {DepthToSpaceProgram} from '../depth_to_space_gpu';\n\nexport function depthToSpace(args: {\n  inputs: DepthToSpaceInputs,\n  backend: MathBackendWebGL,\n  attrs: DepthToSpaceAttrs\n}): TensorInfo {\n  const {inputs, backend, attrs} = args;\n  const {x} = inputs;\n  const {blockSize, dataFormat} = attrs;\n\n  const batchSize = x.shape[0];\n  const inputHeight = (dataFormat === 'NHWC') ? x.shape[1] : x.shape[2];\n  const inputWidth = (dataFormat === 'NHWC') ? x.shape[2] : x.shape[3];\n  const inputDepth = (dataFormat === 'NHWC') ? x.shape[3] : x.shape[1];\n\n  const outputHeight = inputHeight * blockSize;\n  const outputWidth = inputWidth * blockSize;\n  const outputDepth = inputDepth / (blockSize * blockSize);\n\n  const outputShape = (dataFormat === 'NHWC') ?\n      [batchSize, outputHeight, outputWidth, outputDepth] :\n      [batchSize, outputDepth, outputHeight, outputWidth];\n\n  const program = new DepthToSpaceProgram(outputShape, blockSize, dataFormat);\n  return backend.runWebGLProgram(program, [x], x.dtype);\n}\n\nexport const depthToSpaceConfig: KernelConfig = {\n  kernelName: DepthToSpace,\n  backendName: 'webgl',\n  kernelFunc: depthToSpace as unknown as KernelFunc\n};\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAiBA,SAAQA,YAAY,QAAoF,uBAAuB;AAG/H,SAAQC,mBAAmB,QAAO,uBAAuB;AAEzD,OAAM,SAAUC,YAAYA,CAACC,IAI5B;EACC,MAAM;IAACC,MAAM;IAAEC,OAAO;IAAEC;EAAK,CAAC,GAAGH,IAAI;EACrC,MAAM;IAACI;EAAC,CAAC,GAAGH,MAAM;EAClB,MAAM;IAACI,SAAS;IAAEC;EAAU,CAAC,GAAGH,KAAK;EAErC,MAAMI,SAAS,GAAGH,CAAC,CAACI,KAAK,CAAC,CAAC,CAAC;EAC5B,MAAMC,WAAW,GAAIH,UAAU,KAAK,MAAM,GAAIF,CAAC,CAACI,KAAK,CAAC,CAAC,CAAC,GAAGJ,CAAC,CAACI,KAAK,CAAC,CAAC,CAAC;EACrE,MAAME,UAAU,GAAIJ,UAAU,KAAK,MAAM,GAAIF,CAAC,CAACI,KAAK,CAAC,CAAC,CAAC,GAAGJ,CAAC,CAACI,KAAK,CAAC,CAAC,CAAC;EACpE,MAAMG,UAAU,GAAIL,UAAU,KAAK,MAAM,GAAIF,CAAC,CAACI,KAAK,CAAC,CAAC,CAAC,GAAGJ,CAAC,CAACI,KAAK,CAAC,CAAC,CAAC;EAEpE,MAAMI,YAAY,GAAGH,WAAW,GAAGJ,SAAS;EAC5C,MAAMQ,WAAW,GAAGH,UAAU,GAAGL,SAAS;EAC1C,MAAMS,WAAW,GAAGH,UAAU,IAAIN,SAAS,GAAGA,SAAS,CAAC;EAExD,MAAMU,WAAW,GAAIT,UAAU,KAAK,MAAM,GACtC,CAACC,SAAS,EAAEK,YAAY,EAAEC,WAAW,EAAEC,WAAW,CAAC,GACnD,CAACP,SAAS,EAAEO,WAAW,EAAEF,YAAY,EAAEC,WAAW,CAAC;EAEvD,MAAMG,OAAO,GAAG,IAAIlB,mBAAmB,CAACiB,WAAW,EAAEV,SAAS,EAAEC,UAAU,CAAC;EAC3E,OAAOJ,OAAO,CAACe,eAAe,CAACD,OAAO,EAAE,CAACZ,CAAC,CAAC,EAAEA,CAAC,CAACc,KAAK,CAAC;AACvD;AAEA,OAAO,MAAMC,kBAAkB,GAAiB;EAC9CC,UAAU,EAAEvB,YAAY;EACxBwB,WAAW,EAAE,OAAO;EACpBC,UAAU,EAAEvB;CACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}