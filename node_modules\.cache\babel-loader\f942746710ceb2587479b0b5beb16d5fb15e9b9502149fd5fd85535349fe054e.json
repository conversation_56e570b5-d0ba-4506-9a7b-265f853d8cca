{"ast": null, "code": "/**\n * @license\n * Copyright 2021 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { buffer } from '@tensorflow/tfjs-core';\nexport function gatherNdImpl(indicesData, paramsBuf, dtype, numSlices, sliceRank, sliceSize, strides, paramsShape, paramsSize) {\n  const outBuf = buffer([numSlices, sliceSize], dtype);\n  for (let i = 0; i < numSlices; i++) {\n    const index = [];\n    let flattenIndex = 0;\n    for (let j = 0; j < sliceRank; j++) {\n      const dim = indicesData[i * sliceRank + j];\n      flattenIndex += dim * strides[j];\n      index.push(dim);\n    }\n    if (flattenIndex < 0 || flattenIndex >= paramsSize / sliceSize) {\n      throw new Error(\"Invalid indices: \".concat(index, \" does not index into \").concat(paramsShape));\n    }\n    for (let k = 0; k < sliceSize; k++) {\n      outBuf.values[i * sliceSize + k] = paramsBuf.get(...paramsBuf.indexToLoc(flattenIndex * sliceSize + k));\n    }\n  }\n  return outBuf;\n}", "map": {"version": 3, "names": ["buffer", "gatherNdImpl", "indicesData", "paramsBuf", "dtype", "numSlices", "sliceRank", "sliceSize", "strides", "params<PERSON>hape", "paramsSize", "outBuf", "i", "index", "flattenIndex", "j", "dim", "push", "Error", "concat", "k", "values", "get", "indexToLoc"], "sources": ["C:\\tfjs-backend-cpu\\src\\kernels\\GatherNd_Impl.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2021 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {buffer, DataType, Rank, TensorBuffer, TypedArray} from '@tensorflow/tfjs-core';\n\nexport function gatherNdImpl<R extends Rank>(\n    indicesData: TypedArray, paramsBuf: TensorBuffer<R>, dtype: DataType,\n    numSlices: number, sliceRank: number, sliceSize: number, strides: number[],\n    paramsShape: number[], paramsSize: number): TensorBuffer<R> {\n  const outBuf = buffer([numSlices, sliceSize], dtype);\n\n  for (let i = 0; i < numSlices; i++) {\n    const index = [];\n    let flattenIndex = 0;\n    for (let j = 0; j < sliceRank; j++) {\n      const dim = indicesData[i * sliceRank + j];\n      flattenIndex += dim * strides[j];\n      index.push(dim);\n    }\n    if (flattenIndex < 0 || flattenIndex >= paramsSize / sliceSize) {\n      throw new Error(\n          `Invalid indices: ${index} does not index into ${paramsShape}`);\n    }\n\n    for (let k = 0; k < sliceSize; k++) {\n      outBuf.values[i * sliceSize + k] =\n          paramsBuf.get(...paramsBuf.indexToLoc(flattenIndex * sliceSize + k));\n    }\n  }\n\n  return outBuf as TensorBuffer<R>;\n}\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAiBA,SAAQA,MAAM,QAAiD,uBAAuB;AAEtF,OAAM,SAAUC,YAAYA,CACxBC,WAAuB,EAAEC,SAA0B,EAAEC,KAAe,EACpEC,SAAiB,EAAEC,SAAiB,EAAEC,SAAiB,EAAEC,OAAiB,EAC1EC,WAAqB,EAAEC,UAAkB;EAC3C,MAAMC,MAAM,GAAGX,MAAM,CAAC,CAACK,SAAS,EAAEE,SAAS,CAAC,EAAEH,KAAK,CAAC;EAEpD,KAAK,IAAIQ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGP,SAAS,EAAEO,CAAC,EAAE,EAAE;IAClC,MAAMC,KAAK,GAAG,EAAE;IAChB,IAAIC,YAAY,GAAG,CAAC;IACpB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGT,SAAS,EAAES,CAAC,EAAE,EAAE;MAClC,MAAMC,GAAG,GAAGd,WAAW,CAACU,CAAC,GAAGN,SAAS,GAAGS,CAAC,CAAC;MAC1CD,YAAY,IAAIE,GAAG,GAAGR,OAAO,CAACO,CAAC,CAAC;MAChCF,KAAK,CAACI,IAAI,CAACD,GAAG,CAAC;;IAEjB,IAAIF,YAAY,GAAG,CAAC,IAAIA,YAAY,IAAIJ,UAAU,GAAGH,SAAS,EAAE;MAC9D,MAAM,IAAIW,KAAK,qBAAAC,MAAA,CACSN,KAAK,2BAAAM,MAAA,CAAwBV,WAAW,CAAE,CAAC;;IAGrE,KAAK,IAAIW,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGb,SAAS,EAAEa,CAAC,EAAE,EAAE;MAClCT,MAAM,CAACU,MAAM,CAACT,CAAC,GAAGL,SAAS,GAAGa,CAAC,CAAC,GAC5BjB,SAAS,CAACmB,GAAG,CAAC,GAAGnB,SAAS,CAACoB,UAAU,CAACT,YAAY,GAAGP,SAAS,GAAGa,CAAC,CAAC,CAAC;;;EAI5E,OAAOT,MAAyB;AAClC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}