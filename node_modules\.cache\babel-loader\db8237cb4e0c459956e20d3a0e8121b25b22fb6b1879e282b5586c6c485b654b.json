{"ast": null, "code": "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { ENGINE } from '../engine';\nimport { AvgPool3DGrad } from '../kernel_names';\nimport { convertToTensor } from '../tensor_util_env';\nimport * as util from '../util';\nimport { checkPadOnDimRoundingMode } from './conv_util';\nimport { op } from './operation';\nimport { reshape } from './reshape';\n/**\n * Computes the backprop of a 3d avg pool.\n *\n * @param dy The dy error, of rank 5 of shape\n *     [batchSize, depth, height, width, channels].\n * assumed.\n * @param input The original input image, of rank 5 or rank4 of shape\n *     [batchSize, depth, height, width, channels].\n * @param filterSize The filter size:\n *     `[filterDepth, filterHeight, filterWidth]`.\n *     `filterSize` is a single number,\n *     then `filterDepth == filterHeight == filterWidth`.\n * @param strides The strides of the pooling:\n *     `[strideDepth, strideHeight, strideWidth]`. If\n *     `strides` is a single number, then `strideHeight == strideWidth`.\n * @param pad A string from: 'same', 'valid'. The type of padding algorithm\n *     used in the forward prop of the op.\n * @param dimRoundingMode A string from: 'ceil', 'round', 'floor'. If none is\n *     provided, it will default to truncate.\n */\nfunction avgPool3dGrad_(dy, input, filterSize, strides, pad, dimRoundingMode) {\n  const $dy = convertToTensor(dy, 'dy', 'avgPool3dGrad');\n  const $input = convertToTensor(input, 'input', 'avgPool3dGrad');\n  let dy5D = $dy;\n  let input5D = $input;\n  let reshapedTo5D = false;\n  if ($input.rank === 4) {\n    reshapedTo5D = true;\n    dy5D = reshape($dy, [1, $dy.shape[0], $dy.shape[1], $dy.shape[2], $dy.shape[3]]);\n    input5D = reshape($input, [1, $input.shape[0], $input.shape[1], $input.shape[2], $input.shape[3]]);\n  }\n  util.assert(dy5D.rank === 5, () => \"Error in avgPool3dGrad: dy must be rank 5 but got rank \" + \"\".concat(dy5D.rank, \".\"));\n  util.assert(input5D.rank === 5, () => \"Error in avgPool3dGrad: input must be rank 5 but got rank \" + \"\".concat(input5D.rank, \".\"));\n  checkPadOnDimRoundingMode('avgPool3dGrad', pad, dimRoundingMode);\n  const inputs = {\n    dy: dy5D,\n    input: input5D\n  };\n  const attrs = {\n    filterSize,\n    strides,\n    pad,\n    dimRoundingMode\n  };\n  // tslint:disable-next-line: no-unnecessary-type-assertion\n  const res = ENGINE.runKernel(AvgPool3DGrad, inputs, attrs);\n  if (reshapedTo5D) {\n    return reshape(res, [res.shape[1], res.shape[2], res.shape[3], res.shape[4]]);\n  }\n  return res;\n}\nexport const avgPool3dGrad = /* @__PURE__ */op({\n  avgPool3dGrad_\n});", "map": {"version": 3, "names": ["ENGINE", "AvgPool3DGrad", "convertToTensor", "util", "checkPadOnDimRoundingMode", "op", "reshape", "avgPool3dGrad_", "dy", "input", "filterSize", "strides", "pad", "dimRoundingMode", "$dy", "$input", "dy5D", "input5D", "reshapedTo5D", "rank", "shape", "assert", "concat", "inputs", "attrs", "res", "runKernel", "avgPool3dGrad"], "sources": ["C:\\tfjs-core\\src\\ops\\avg_pool_3d_grad.ts"], "sourcesContent": ["\n/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {ENGINE} from '../engine';\nimport {AvgPool3DGrad, AvgPool3DGradAttrs, AvgPool3DGradInputs} from '../kernel_names';\nimport {NamedAttrMap} from '../kernel_registry';\nimport {Tensor4D, Tensor5D} from '../tensor';\nimport {NamedTensorMap} from '../tensor_types';\nimport {convertToTensor} from '../tensor_util_env';\nimport {TensorLike} from '../types';\nimport * as util from '../util';\n\nimport {checkPadOnDimRoundingMode} from './conv_util';\nimport {op} from './operation';\nimport {reshape} from './reshape';\n\n/**\n * Computes the backprop of a 3d avg pool.\n *\n * @param dy The dy error, of rank 5 of shape\n *     [batchSize, depth, height, width, channels].\n * assumed.\n * @param input The original input image, of rank 5 or rank4 of shape\n *     [batchSize, depth, height, width, channels].\n * @param filterSize The filter size:\n *     `[filterDepth, filterHeight, filterWidth]`.\n *     `filterSize` is a single number,\n *     then `filterDepth == filterHeight == filterWidth`.\n * @param strides The strides of the pooling:\n *     `[strideDepth, strideHeight, strideWidth]`. If\n *     `strides` is a single number, then `strideHeight == strideWidth`.\n * @param pad A string from: 'same', 'valid'. The type of padding algorithm\n *     used in the forward prop of the op.\n * @param dimRoundingMode A string from: 'ceil', 'round', 'floor'. If none is\n *     provided, it will default to truncate.\n */\nfunction avgPool3dGrad_<T extends Tensor4D|Tensor5D>(\n    dy: T|TensorLike, input: T|TensorLike,\n    filterSize: [number, number, number]|number,\n    strides: [number, number, number]|number, pad: 'valid'|'same'|number,\n    dimRoundingMode?: 'floor'|'round'|'ceil'): T {\n  const $dy = convertToTensor(dy, 'dy', 'avgPool3dGrad');\n  const $input = convertToTensor(input, 'input', 'avgPool3dGrad');\n\n  let dy5D = $dy as Tensor5D;\n  let input5D = $input as Tensor5D;\n  let reshapedTo5D = false;\n\n  if ($input.rank === 4) {\n    reshapedTo5D = true;\n    dy5D = reshape(\n        $dy, [1, $dy.shape[0], $dy.shape[1], $dy.shape[2], $dy.shape[3]]);\n    input5D = reshape($input, [\n      1, $input.shape[0], $input.shape[1], $input.shape[2], $input.shape[3]\n    ]);\n  }\n\n  util.assert(\n      dy5D.rank === 5,\n      () => `Error in avgPool3dGrad: dy must be rank 5 but got rank ` +\n          `${dy5D.rank}.`);\n  util.assert(\n      input5D.rank === 5,\n      () => `Error in avgPool3dGrad: input must be rank 5 but got rank ` +\n          `${input5D.rank}.`);\n  checkPadOnDimRoundingMode('avgPool3dGrad', pad, dimRoundingMode);\n  const inputs: AvgPool3DGradInputs = {dy: dy5D, input: input5D};\n  const attrs: AvgPool3DGradAttrs = {filterSize, strides, pad, dimRoundingMode};\n\n  // tslint:disable-next-line: no-unnecessary-type-assertion\n  const res = ENGINE.runKernel(\n                  AvgPool3DGrad, inputs as unknown as NamedTensorMap,\n                  attrs as unknown as NamedAttrMap) as T;\n\n  if (reshapedTo5D) {\n    return reshape(\n               res, [res.shape[1], res.shape[2], res.shape[3], res.shape[4]]) as\n        T;\n  }\n\n  return res;\n}\n\nexport const avgPool3dGrad = /* @__PURE__ */ op({avgPool3dGrad_});\n"], "mappings": "AACA;;;;;;;;;;;;;;;;AAiBA,SAAQA,MAAM,QAAO,WAAW;AAChC,SAAQC,aAAa,QAAgD,iBAAiB;AAItF,SAAQC,eAAe,QAAO,oBAAoB;AAElD,OAAO,KAAKC,IAAI,MAAM,SAAS;AAE/B,SAAQC,yBAAyB,QAAO,aAAa;AACrD,SAAQC,EAAE,QAAO,aAAa;AAC9B,SAAQC,OAAO,QAAO,WAAW;AAEjC;;;;;;;;;;;;;;;;;;;;AAoBA,SAASC,cAAcA,CACnBC,EAAgB,EAAEC,KAAmB,EACrCC,UAA2C,EAC3CC,OAAwC,EAAEC,GAA0B,EACpEC,eAAwC;EAC1C,MAAMC,GAAG,GAAGZ,eAAe,CAACM,EAAE,EAAE,IAAI,EAAE,eAAe,CAAC;EACtD,MAAMO,MAAM,GAAGb,eAAe,CAACO,KAAK,EAAE,OAAO,EAAE,eAAe,CAAC;EAE/D,IAAIO,IAAI,GAAGF,GAAe;EAC1B,IAAIG,OAAO,GAAGF,MAAkB;EAChC,IAAIG,YAAY,GAAG,KAAK;EAExB,IAAIH,MAAM,CAACI,IAAI,KAAK,CAAC,EAAE;IACrBD,YAAY,GAAG,IAAI;IACnBF,IAAI,GAAGV,OAAO,CACVQ,GAAG,EAAE,CAAC,CAAC,EAAEA,GAAG,CAACM,KAAK,CAAC,CAAC,CAAC,EAAEN,GAAG,CAACM,KAAK,CAAC,CAAC,CAAC,EAAEN,GAAG,CAACM,KAAK,CAAC,CAAC,CAAC,EAAEN,GAAG,CAACM,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;IACrEH,OAAO,GAAGX,OAAO,CAACS,MAAM,EAAE,CACxB,CAAC,EAAEA,MAAM,CAACK,KAAK,CAAC,CAAC,CAAC,EAAEL,MAAM,CAACK,KAAK,CAAC,CAAC,CAAC,EAAEL,MAAM,CAACK,KAAK,CAAC,CAAC,CAAC,EAAEL,MAAM,CAACK,KAAK,CAAC,CAAC,CAAC,CACtE,CAAC;;EAGJjB,IAAI,CAACkB,MAAM,CACPL,IAAI,CAACG,IAAI,KAAK,CAAC,EACf,MAAM,+DAAAG,MAAA,CACCN,IAAI,CAACG,IAAI,MAAG,CAAC;EACxBhB,IAAI,CAACkB,MAAM,CACPJ,OAAO,CAACE,IAAI,KAAK,CAAC,EAClB,MAAM,kEAAAG,MAAA,CACCL,OAAO,CAACE,IAAI,MAAG,CAAC;EAC3Bf,yBAAyB,CAAC,eAAe,EAAEQ,GAAG,EAAEC,eAAe,CAAC;EAChE,MAAMU,MAAM,GAAwB;IAACf,EAAE,EAAEQ,IAAI;IAAEP,KAAK,EAAEQ;EAAO,CAAC;EAC9D,MAAMO,KAAK,GAAuB;IAACd,UAAU;IAAEC,OAAO;IAAEC,GAAG;IAAEC;EAAe,CAAC;EAE7E;EACA,MAAMY,GAAG,GAAGzB,MAAM,CAAC0B,SAAS,CACZzB,aAAa,EAAEsB,MAAmC,EAClDC,KAAgC,CAAM;EAEtD,IAAIN,YAAY,EAAE;IAChB,OAAOZ,OAAO,CACHmB,GAAG,EAAE,CAACA,GAAG,CAACL,KAAK,CAAC,CAAC,CAAC,EAAEK,GAAG,CAACL,KAAK,CAAC,CAAC,CAAC,EAAEK,GAAG,CAACL,KAAK,CAAC,CAAC,CAAC,EAAEK,GAAG,CAACL,KAAK,CAAC,CAAC,CAAC,CAAC,CACnE;;EAGP,OAAOK,GAAG;AACZ;AAEA,OAAO,MAAME,aAAa,GAAG,eAAgBtB,EAAE,CAAC;EAACE;AAAc,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}