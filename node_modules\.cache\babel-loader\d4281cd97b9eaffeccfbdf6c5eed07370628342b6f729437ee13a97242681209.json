{"ast": null, "code": "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { backend_util, util } from '@tensorflow/tfjs-core';\nimport { add } from '../kernels/Add';\nimport { complex } from '../kernels/Complex';\nimport { concat } from '../kernels/Concat';\nimport { identity } from '../kernels/Identity';\nimport { imag } from '../kernels/Imag';\nimport { multiply } from '../kernels/Multiply';\nimport { real } from '../kernels/Real';\nimport { realDivConfig } from '../kernels/RealDiv';\nimport { slice } from '../kernels/Slice';\nimport { sub } from '../kernels/Sub';\n/**\n * Calculate FFT of inner most elements of batch tensor.\n */\nexport function fftBatch(input, inverse, cpuBackend) {\n  const inputShape = input.shape;\n  const batch = inputShape[0];\n  const innerDim = inputShape[1];\n  const inputVals = cpuBackend.data.get(input.dataId);\n  const real2D = inputVals.complexTensorInfos.real;\n  const imag2D = inputVals.complexTensorInfos.imag;\n  // Collects real and imaginary values separately.\n  const resultShape = [batch, innerDim];\n  const resultSize = util.sizeFromShape(resultShape);\n  const resultReal = util.getTypedArrayFromDType('float32', resultSize);\n  const resultImag = util.getTypedArrayFromDType('float32', resultSize);\n  for (let b = 0; b < batch; b++) {\n    // TODO: Support slice ops for complex type.\n    const r = slice({\n      inputs: {\n        x: real2D\n      },\n      backend: cpuBackend,\n      attrs: {\n        begin: [b, 0],\n        size: [1, innerDim]\n      }\n    });\n    const i = slice({\n      inputs: {\n        x: imag2D\n      },\n      backend: cpuBackend,\n      attrs: {\n        begin: [b, 0],\n        size: [1, innerDim]\n      }\n    });\n    const input = complex({\n      inputs: {\n        real: r,\n        imag: i\n      },\n      backend: cpuBackend\n    });\n    // Run FFT by batch element.\n    const {\n      real,\n      imag\n    } = fftImpl(input, inverse, cpuBackend);\n    const res = backend_util.mergeRealAndImagArrays(real, imag);\n    for (let d = 0; d < innerDim; d++) {\n      const c = backend_util.getComplexWithIndex(res, d);\n      resultReal[b * innerDim + d] = c.real;\n      resultImag[b * innerDim + d] = c.imag;\n    }\n    cpuBackend.disposeIntermediateTensorInfo(r);\n    cpuBackend.disposeIntermediateTensorInfo(i);\n    cpuBackend.disposeIntermediateTensorInfo(input);\n  }\n  const $realInfo = cpuBackend.makeTensorInfo(resultShape, 'float32', resultReal);\n  const $imagInfo = cpuBackend.makeTensorInfo(resultShape, 'float32', resultImag);\n  const result = complex({\n    inputs: {\n      real: $realInfo,\n      imag: $imagInfo\n    },\n    backend: cpuBackend\n  });\n  cpuBackend.disposeIntermediateTensorInfo($realInfo);\n  cpuBackend.disposeIntermediateTensorInfo($imagInfo);\n  return result;\n}\nexport function fftImpl(input, inverse, cpuBackend) {\n  const inputSize = util.sizeFromShape(input.shape);\n  const inputVals = cpuBackend.data.get(input.dataId);\n  const realVals = cpuBackend.data.get(inputVals.complexTensorInfos.real.dataId).values;\n  const imagVals = cpuBackend.data.get(inputVals.complexTensorInfos.imag.dataId).values;\n  if (isExponentOf2(inputSize)) {\n    const result = fftRadix2(realVals, imagVals, inputSize, inverse, cpuBackend);\n    const resultShape = [input.shape[0], input.shape[1]];\n    if (inverse) {\n      const realInfo = cpuBackend.makeTensorInfo(resultShape, 'float32', result.real);\n      const imagInfo = cpuBackend.makeTensorInfo(resultShape, 'float32', result.imag);\n      const sizeInfo = cpuBackend.makeTensorInfo([], 'float32', util.createScalarValue(inputSize, 'float32'));\n      const sizeInfoCopy = identity({\n        inputs: {\n          x: sizeInfo\n        },\n        backend: cpuBackend\n      });\n      const divRealInfo = realDivConfig.kernelFunc({\n        inputs: {\n          a: realInfo,\n          b: sizeInfo\n        },\n        backend: cpuBackend\n      });\n      const divImagInfo = realDivConfig.kernelFunc({\n        inputs: {\n          a: imagInfo,\n          b: sizeInfoCopy\n        },\n        backend: cpuBackend\n      });\n      const divRealVals = cpuBackend.data.get(divRealInfo.dataId).values;\n      const divImagVals = cpuBackend.data.get(divImagInfo.dataId).values;\n      cpuBackend.disposeIntermediateTensorInfo(realInfo);\n      cpuBackend.disposeIntermediateTensorInfo(imagInfo);\n      cpuBackend.disposeIntermediateTensorInfo(sizeInfo);\n      cpuBackend.disposeIntermediateTensorInfo(sizeInfoCopy);\n      cpuBackend.disposeIntermediateTensorInfo(divRealInfo);\n      cpuBackend.disposeIntermediateTensorInfo(divImagInfo);\n      return {\n        real: divRealVals,\n        imag: divImagVals\n      };\n    }\n    return result;\n  } else {\n    const data = backend_util.mergeRealAndImagArrays(realVals, imagVals);\n    const rawOutput = fourierTransformByMatmul(data, inputSize, inverse);\n    return backend_util.splitRealAndImagArrays(rawOutput);\n  }\n}\nfunction isExponentOf2(size) {\n  return (size & size - 1) === 0;\n}\n// FFT using Cooley-Tukey algorithm on radix 2 dimensional input.\nfunction fftRadix2(realVals, imagVals, size, inverse, cpuBackend) {\n  if (size === 1) {\n    return {\n      real: realVals,\n      imag: imagVals\n    };\n  }\n  const data = backend_util.mergeRealAndImagArrays(realVals, imagVals);\n  const half = size / 2;\n  const evenComplex = backend_util.complexWithEvenIndex(data);\n  const evenRealVals = evenComplex.real;\n  const evenImagVals = evenComplex.imag;\n  const evenShape = [evenRealVals.length];\n  const evenRealInfo = cpuBackend.makeTensorInfo(evenShape, 'float32', evenRealVals);\n  const evenImagInfo = cpuBackend.makeTensorInfo(evenShape, 'float32', evenImagVals);\n  const evenTensorInfo = complex({\n    inputs: {\n      real: evenRealInfo,\n      imag: evenImagInfo\n    },\n    backend: cpuBackend\n  });\n  const oddComplex = backend_util.complexWithOddIndex(data);\n  const oddRealVals = oddComplex.real;\n  const oddImagVals = oddComplex.imag;\n  const oddShape = [oddRealVals.length];\n  const oddRealInfo = cpuBackend.makeTensorInfo(oddShape, 'float32', oddRealVals);\n  const oddImagInfo = cpuBackend.makeTensorInfo(oddShape, 'float32', oddImagVals);\n  const oddTensorInfo = complex({\n    inputs: {\n      real: oddRealInfo,\n      imag: oddImagInfo\n    },\n    backend: cpuBackend\n  });\n  // Recursive call for half part of original input.\n  const $evenComplex = fftRadix2(evenRealVals, evenImagVals, half, inverse, cpuBackend);\n  const $evenRealVals = $evenComplex.real;\n  const $evenImagVals = $evenComplex.imag;\n  const $evenShape = [$evenRealVals.length];\n  const $evenRealInfo = cpuBackend.makeTensorInfo($evenShape, 'float32', $evenRealVals);\n  const $evenImagInfo = cpuBackend.makeTensorInfo($evenShape, 'float32', $evenImagVals);\n  const $evenTensorInfo = complex({\n    inputs: {\n      real: $evenRealInfo,\n      imag: $evenImagInfo\n    },\n    backend: cpuBackend\n  });\n  const $oddComplex = fftRadix2(oddRealVals, oddImagVals, half, inverse, cpuBackend);\n  const $oddRealVals = $oddComplex.real;\n  const $oddImagVals = $oddComplex.imag;\n  const $oddShape = [$oddRealVals.length];\n  const $oddRealInfo = cpuBackend.makeTensorInfo($oddShape, 'float32', $oddRealVals);\n  const $oddImagInfo = cpuBackend.makeTensorInfo($oddShape, 'float32', $oddImagVals);\n  const $oddTensorInfo = complex({\n    inputs: {\n      real: $oddRealInfo,\n      imag: $oddImagInfo\n    },\n    backend: cpuBackend\n  });\n  const e = backend_util.exponents(size, inverse);\n  const eShape = [e.real.length];\n  const eRealInfo = cpuBackend.makeTensorInfo(eShape, 'float32', e.real);\n  const eImagInfo = cpuBackend.makeTensorInfo(eShape, 'float32', e.imag);\n  const complexInfo = complex({\n    inputs: {\n      real: eRealInfo,\n      imag: eImagInfo\n    },\n    backend: cpuBackend\n  });\n  const exponentInfo = multiply({\n    inputs: {\n      a: complexInfo,\n      b: $oddTensorInfo\n    },\n    backend: cpuBackend\n  });\n  const addPart = add({\n    inputs: {\n      a: $evenTensorInfo,\n      b: exponentInfo\n    },\n    backend: cpuBackend\n  });\n  const subPart = sub({\n    inputs: {\n      a: $evenTensorInfo,\n      b: exponentInfo\n    },\n    backend: cpuBackend\n  });\n  const addPartReal = real({\n    inputs: {\n      input: addPart\n    },\n    backend: cpuBackend\n  });\n  const subPartReal = real({\n    inputs: {\n      input: subPart\n    },\n    backend: cpuBackend\n  });\n  const addPartImag = imag({\n    inputs: {\n      input: addPart\n    },\n    backend: cpuBackend\n  });\n  const subPartImag = imag({\n    inputs: {\n      input: subPart\n    },\n    backend: cpuBackend\n  });\n  const $real = concat({\n    inputs: [addPartReal, subPartReal],\n    backend: cpuBackend,\n    attrs: {\n      axis: 0\n    }\n  });\n  const $imag = concat({\n    inputs: [addPartImag, subPartImag],\n    backend: cpuBackend,\n    attrs: {\n      axis: 0\n    }\n  });\n  const $realVals = cpuBackend.data.get($real.dataId).values;\n  const $imagVals = cpuBackend.data.get($imag.dataId).values;\n  cpuBackend.disposeIntermediateTensorInfo(evenRealInfo);\n  cpuBackend.disposeIntermediateTensorInfo(evenImagInfo);\n  cpuBackend.disposeIntermediateTensorInfo(evenTensorInfo);\n  cpuBackend.disposeIntermediateTensorInfo(oddRealInfo);\n  cpuBackend.disposeIntermediateTensorInfo(oddImagInfo);\n  cpuBackend.disposeIntermediateTensorInfo(oddTensorInfo);\n  cpuBackend.disposeIntermediateTensorInfo($evenRealInfo);\n  cpuBackend.disposeIntermediateTensorInfo($evenImagInfo);\n  cpuBackend.disposeIntermediateTensorInfo($evenTensorInfo);\n  cpuBackend.disposeIntermediateTensorInfo($oddRealInfo);\n  cpuBackend.disposeIntermediateTensorInfo($oddImagInfo);\n  cpuBackend.disposeIntermediateTensorInfo($oddTensorInfo);\n  cpuBackend.disposeIntermediateTensorInfo(eRealInfo);\n  cpuBackend.disposeIntermediateTensorInfo(eImagInfo);\n  cpuBackend.disposeIntermediateTensorInfo(complexInfo);\n  cpuBackend.disposeIntermediateTensorInfo(exponentInfo);\n  cpuBackend.disposeIntermediateTensorInfo(addPart);\n  cpuBackend.disposeIntermediateTensorInfo(subPart);\n  cpuBackend.disposeIntermediateTensorInfo(addPartReal);\n  cpuBackend.disposeIntermediateTensorInfo(addPartImag);\n  cpuBackend.disposeIntermediateTensorInfo(subPartReal);\n  cpuBackend.disposeIntermediateTensorInfo(subPartImag);\n  cpuBackend.disposeIntermediateTensorInfo($real);\n  cpuBackend.disposeIntermediateTensorInfo($imag);\n  return {\n    real: $realVals,\n    imag: $imagVals\n  };\n}\n// Calculate fourier transform by multplying sinusoid matrix.\nfunction fourierTransformByMatmul(data, size, inverse) {\n  const ret = new Float32Array(size * 2);\n  // TODO: Use matmul instead once it supports complex64 type.\n  for (let r = 0; r < size; r++) {\n    let real = 0.0;\n    let imag = 0.0;\n    for (let c = 0; c < size; c++) {\n      const e = backend_util.exponent(r * c, size, inverse);\n      const term = backend_util.getComplexWithIndex(data, c);\n      real += term.real * e.real - term.imag * e.imag;\n      imag += term.real * e.imag + term.imag * e.real;\n    }\n    if (inverse) {\n      real /= size;\n      imag /= size;\n    }\n    backend_util.assignToTypedArray(ret, real, imag, r);\n  }\n  return ret;\n}", "map": {"version": 3, "names": ["backend_util", "util", "add", "complex", "concat", "identity", "imag", "multiply", "real", "realDivConfig", "slice", "sub", "fftBatch", "input", "inverse", "cpuBackend", "inputShape", "shape", "batch", "innerDim", "inputVals", "data", "get", "dataId", "real2D", "complexTensorInfos", "imag2D", "resultShape", "resultSize", "sizeFromShape", "resultReal", "getTypedArrayFromDType", "resultImag", "b", "r", "inputs", "x", "backend", "attrs", "begin", "size", "i", "fftImpl", "res", "mergeRealAndImagArrays", "d", "c", "getComplexWithIndex", "disposeIntermediateTensorInfo", "$realInfo", "makeTensorInfo", "$imagInfo", "result", "inputSize", "realVals", "values", "imagVals", "isExponentOf2", "fftRadix2", "realInfo", "imagInfo", "sizeInfo", "createScalarValue", "sizeInfoCopy", "divRealInfo", "kernelFunc", "a", "divImagInfo", "divRealVals", "divImagVals", "rawOutput", "fourierTransformByMatmul", "splitRealAndImagArrays", "half", "evenComplex", "complexWithEvenIndex", "evenRealVals", "evenImagVals", "evenShape", "length", "evenRealInfo", "evenImagInfo", "evenTensorInfo", "oddComplex", "complexWithOddIndex", "oddRealVals", "oddImagVals", "oddShape", "oddRealInfo", "oddImagInfo", "oddTensorInfo", "$evenComplex", "$evenRealVals", "$evenImagVals", "$evenShape", "$evenRealInfo", "$evenImagInfo", "$evenTensorInfo", "$oddComplex", "$oddRealVals", "$oddImagVals", "$oddShape", "$oddRealInfo", "$oddImagInfo", "$oddTensorInfo", "e", "exponents", "eShape", "eRealInfo", "eImagInfo", "complexInfo", "exponentInfo", "addPart", "subPart", "addPartReal", "subPartReal", "addPartImag", "subPartImag", "$real", "axis", "$imag", "$realVals", "$imagVals", "ret", "Float32Array", "exponent", "term", "assignToTypedArray"], "sources": ["C:\\tfjs-backend-cpu\\src\\utils\\fft_utils.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {backend_util, Tensor, TensorInfo, TypedArray, util} from '@tensorflow/tfjs-core';\n\nimport {MathBackendCPU} from '../backend_cpu';\nimport {add} from '../kernels/Add';\nimport {complex} from '../kernels/Complex';\nimport {concat} from '../kernels/Concat';\nimport {identity} from '../kernels/Identity';\nimport {imag} from '../kernels/Imag';\nimport {multiply} from '../kernels/Multiply';\nimport {real} from '../kernels/Real';\nimport {realDivConfig} from '../kernels/RealDiv';\nimport {slice} from '../kernels/Slice';\nimport {sub} from '../kernels/Sub';\n\n/**\n * Calculate FFT of inner most elements of batch tensor.\n */\nexport function fftBatch(\n    input: TensorInfo, inverse: boolean,\n    cpuBackend: MathBackendCPU): TensorInfo {\n  const inputShape = input.shape;\n  const batch = inputShape[0];\n  const innerDim = inputShape[1];\n\n  const inputVals = cpuBackend.data.get(input.dataId);\n\n  const real2D = inputVals.complexTensorInfos.real;\n  const imag2D = inputVals.complexTensorInfos.imag;\n\n  // Collects real and imaginary values separately.\n  const resultShape = [batch, innerDim];\n  const resultSize = util.sizeFromShape(resultShape);\n  const resultReal = util.getTypedArrayFromDType('float32', resultSize);\n  const resultImag = util.getTypedArrayFromDType('float32', resultSize);\n\n  for (let b = 0; b < batch; b++) {\n    // TODO: Support slice ops for complex type.\n    const r = slice({\n      inputs: {x: real2D},\n      backend: cpuBackend,\n      attrs: {begin: [b, 0], size: [1, innerDim]}\n    });\n    const i = slice({\n      inputs: {x: imag2D},\n      backend: cpuBackend,\n      attrs: {begin: [b, 0], size: [1, innerDim]}\n    });\n\n    const input = complex({inputs: {real: r, imag: i}, backend: cpuBackend});\n\n    // Run FFT by batch element.\n    const {real, imag} = fftImpl(input, inverse, cpuBackend);\n    const res = backend_util.mergeRealAndImagArrays(real, imag);\n\n    for (let d = 0; d < innerDim; d++) {\n      const c = backend_util.getComplexWithIndex(res, d);\n      resultReal[b * innerDim + d] = c.real;\n      resultImag[b * innerDim + d] = c.imag;\n    }\n\n    cpuBackend.disposeIntermediateTensorInfo(r);\n    cpuBackend.disposeIntermediateTensorInfo(i);\n    cpuBackend.disposeIntermediateTensorInfo(input);\n  }\n\n  const $realInfo: TensorInfo =\n      cpuBackend.makeTensorInfo(resultShape, 'float32', resultReal);\n  const $imagInfo: TensorInfo =\n      cpuBackend.makeTensorInfo(resultShape, 'float32', resultImag);\n\n  const result = complex(\n      {inputs: {real: $realInfo, imag: $imagInfo}, backend: cpuBackend});\n\n  cpuBackend.disposeIntermediateTensorInfo($realInfo);\n  cpuBackend.disposeIntermediateTensorInfo($imagInfo);\n\n  return result;\n}\n\nexport function fftImpl(\n    input: TensorInfo, inverse: boolean,\n    cpuBackend: MathBackendCPU): {real: Float32Array, imag: Float32Array} {\n  const inputSize = util.sizeFromShape(input.shape);\n\n  const inputVals = cpuBackend.data.get(input.dataId);\n\n  const realVals =\n      cpuBackend.data.get(inputVals.complexTensorInfos.real.dataId).values as\n      Float32Array;\n\n  const imagVals =\n      cpuBackend.data.get(inputVals.complexTensorInfos.imag.dataId).values as\n      Float32Array;\n\n  if (isExponentOf2(inputSize)) {\n    const result =\n        fftRadix2(realVals, imagVals, inputSize, inverse, cpuBackend);\n\n    const resultShape = [input.shape[0], input.shape[1]];\n\n    if (inverse) {\n      const realInfo: TensorInfo =\n          cpuBackend.makeTensorInfo(resultShape, 'float32', result.real);\n      const imagInfo: TensorInfo =\n          cpuBackend.makeTensorInfo(resultShape, 'float32', result.imag);\n\n      const sizeInfo: TensorInfo = cpuBackend.makeTensorInfo(\n          [], 'float32',\n          util.createScalarValue(inputSize as unknown as 'float32', 'float32'));\n      const sizeInfoCopy =\n          identity({inputs: {x: sizeInfo}, backend: cpuBackend});\n\n      const divRealInfo =\n          realDivConfig.kernelFunc(\n              {inputs: {a: realInfo, b: sizeInfo}, backend: cpuBackend}) as\n          TensorInfo;\n      const divImagInfo =\n          realDivConfig.kernelFunc(\n              {inputs: {a: imagInfo, b: sizeInfoCopy}, backend: cpuBackend}) as\n          TensorInfo;\n\n      const divRealVals =\n          cpuBackend.data.get(divRealInfo.dataId).values as Float32Array;\n      const divImagVals =\n          cpuBackend.data.get(divImagInfo.dataId).values as Float32Array;\n\n      cpuBackend.disposeIntermediateTensorInfo(realInfo);\n      cpuBackend.disposeIntermediateTensorInfo(imagInfo);\n      cpuBackend.disposeIntermediateTensorInfo(sizeInfo);\n      cpuBackend.disposeIntermediateTensorInfo(sizeInfoCopy);\n      cpuBackend.disposeIntermediateTensorInfo(divRealInfo);\n      cpuBackend.disposeIntermediateTensorInfo(divImagInfo);\n\n      return {real: divRealVals, imag: divImagVals};\n    }\n\n    return result;\n  } else {\n    const data = backend_util.mergeRealAndImagArrays(realVals, imagVals);\n\n    const rawOutput =\n        fourierTransformByMatmul(data, inputSize, inverse) as Float32Array;\n\n    return backend_util.splitRealAndImagArrays(rawOutput);\n  }\n}\n\nfunction isExponentOf2(size: number): boolean {\n  return (size & size - 1) === 0;\n}\n\n// FFT using Cooley-Tukey algorithm on radix 2 dimensional input.\nfunction fftRadix2(\n    realVals: Float32Array, imagVals: Float32Array, size: number,\n    inverse: boolean,\n    cpuBackend: MathBackendCPU): {real: Float32Array, imag: Float32Array} {\n  if (size === 1) {\n    return {real: realVals, imag: imagVals};\n  }\n\n  const data = backend_util.mergeRealAndImagArrays(realVals, imagVals);\n\n  const half = size / 2;\n\n  const evenComplex = backend_util.complexWithEvenIndex(data);\n\n  const evenRealVals = evenComplex.real;\n  const evenImagVals = evenComplex.imag;\n\n  const evenShape = [evenRealVals.length];\n\n  const evenRealInfo =\n      cpuBackend.makeTensorInfo(evenShape, 'float32', evenRealVals);\n  const evenImagInfo =\n      cpuBackend.makeTensorInfo(evenShape, 'float32', evenImagVals);\n\n  const evenTensorInfo = complex(\n      {inputs: {real: evenRealInfo, imag: evenImagInfo}, backend: cpuBackend});\n\n  const oddComplex = backend_util.complexWithOddIndex(data);\n\n  const oddRealVals = oddComplex.real;\n  const oddImagVals = oddComplex.imag;\n\n  const oddShape = [oddRealVals.length];\n\n  const oddRealInfo =\n      cpuBackend.makeTensorInfo(oddShape, 'float32', oddRealVals);\n  const oddImagInfo =\n      cpuBackend.makeTensorInfo(oddShape, 'float32', oddImagVals);\n\n  const oddTensorInfo = complex(\n      {inputs: {real: oddRealInfo, imag: oddImagInfo}, backend: cpuBackend});\n\n  // Recursive call for half part of original input.\n  const $evenComplex =\n      fftRadix2(evenRealVals, evenImagVals, half, inverse, cpuBackend);\n\n  const $evenRealVals = $evenComplex.real;\n  const $evenImagVals = $evenComplex.imag;\n\n  const $evenShape = [$evenRealVals.length];\n\n  const $evenRealInfo =\n      cpuBackend.makeTensorInfo($evenShape, 'float32', $evenRealVals);\n  const $evenImagInfo =\n      cpuBackend.makeTensorInfo($evenShape, 'float32', $evenImagVals);\n\n  const $evenTensorInfo = complex({\n    inputs: {real: $evenRealInfo, imag: $evenImagInfo},\n    backend: cpuBackend\n  });\n\n  const $oddComplex =\n      fftRadix2(oddRealVals, oddImagVals, half, inverse, cpuBackend);\n\n  const $oddRealVals = $oddComplex.real;\n  const $oddImagVals = $oddComplex.imag;\n\n  const $oddShape = [$oddRealVals.length];\n\n  const $oddRealInfo =\n      cpuBackend.makeTensorInfo($oddShape, 'float32', $oddRealVals);\n  const $oddImagInfo =\n      cpuBackend.makeTensorInfo($oddShape, 'float32', $oddImagVals);\n\n  const $oddTensorInfo = complex(\n      {inputs: {real: $oddRealInfo, imag: $oddImagInfo}, backend: cpuBackend});\n\n  const e = backend_util.exponents(size, inverse);\n  const eShape = [e.real.length];\n\n  const eRealInfo = cpuBackend.makeTensorInfo(eShape, 'float32', e.real);\n  const eImagInfo = cpuBackend.makeTensorInfo(eShape, 'float32', e.imag);\n\n  const complexInfo = complex(\n      {inputs: {real: eRealInfo, imag: eImagInfo}, backend: cpuBackend});\n\n  const exponentInfo =\n      multiply(\n          {inputs: {a: complexInfo, b: $oddTensorInfo}, backend: cpuBackend}) as\n      TensorInfo;\n\n  const addPart = add({\n                    inputs: {a: $evenTensorInfo, b: exponentInfo},\n                    backend: cpuBackend\n                  }) as TensorInfo;\n  const subPart = sub({\n                    inputs: {a: $evenTensorInfo, b: exponentInfo},\n                    backend: cpuBackend\n                  }) as TensorInfo;\n\n  const addPartReal = real({inputs: {input: addPart}, backend: cpuBackend});\n  const subPartReal = real({inputs: {input: subPart}, backend: cpuBackend});\n\n  const addPartImag = imag({inputs: {input: addPart}, backend: cpuBackend});\n  const subPartImag = imag({inputs: {input: subPart}, backend: cpuBackend});\n\n  const $real = concat({\n    inputs: [addPartReal as Tensor, subPartReal as Tensor],\n    backend: cpuBackend,\n    attrs: {axis: 0}\n  });\n  const $imag = concat({\n    inputs: [addPartImag as Tensor, subPartImag as Tensor],\n    backend: cpuBackend,\n    attrs: {axis: 0}\n  });\n\n  const $realVals = cpuBackend.data.get($real.dataId).values as Float32Array;\n  const $imagVals = cpuBackend.data.get($imag.dataId).values as Float32Array;\n\n  cpuBackend.disposeIntermediateTensorInfo(evenRealInfo);\n  cpuBackend.disposeIntermediateTensorInfo(evenImagInfo);\n  cpuBackend.disposeIntermediateTensorInfo(evenTensorInfo);\n  cpuBackend.disposeIntermediateTensorInfo(oddRealInfo);\n  cpuBackend.disposeIntermediateTensorInfo(oddImagInfo);\n  cpuBackend.disposeIntermediateTensorInfo(oddTensorInfo);\n  cpuBackend.disposeIntermediateTensorInfo($evenRealInfo);\n  cpuBackend.disposeIntermediateTensorInfo($evenImagInfo);\n  cpuBackend.disposeIntermediateTensorInfo($evenTensorInfo);\n  cpuBackend.disposeIntermediateTensorInfo($oddRealInfo);\n  cpuBackend.disposeIntermediateTensorInfo($oddImagInfo);\n  cpuBackend.disposeIntermediateTensorInfo($oddTensorInfo);\n  cpuBackend.disposeIntermediateTensorInfo(eRealInfo);\n  cpuBackend.disposeIntermediateTensorInfo(eImagInfo);\n  cpuBackend.disposeIntermediateTensorInfo(complexInfo);\n  cpuBackend.disposeIntermediateTensorInfo(exponentInfo);\n  cpuBackend.disposeIntermediateTensorInfo(addPart);\n  cpuBackend.disposeIntermediateTensorInfo(subPart);\n  cpuBackend.disposeIntermediateTensorInfo(addPartReal);\n  cpuBackend.disposeIntermediateTensorInfo(addPartImag);\n  cpuBackend.disposeIntermediateTensorInfo(subPartReal);\n  cpuBackend.disposeIntermediateTensorInfo(subPartImag);\n  cpuBackend.disposeIntermediateTensorInfo($real);\n  cpuBackend.disposeIntermediateTensorInfo($imag);\n\n  return {real: $realVals, imag: $imagVals};\n}\n\n// Calculate fourier transform by multplying sinusoid matrix.\nfunction fourierTransformByMatmul(\n    data: TypedArray, size: number, inverse: boolean): TypedArray {\n  const ret = new Float32Array(size * 2);\n  // TODO: Use matmul instead once it supports complex64 type.\n  for (let r = 0; r < size; r++) {\n    let real = 0.0;\n    let imag = 0.0;\n    for (let c = 0; c < size; c++) {\n      const e = backend_util.exponent(r * c, size, inverse);\n      const term = backend_util.getComplexWithIndex(data as Float32Array, c);\n      real += term.real * e.real - term.imag * e.imag;\n      imag += term.real * e.imag + term.imag * e.real;\n    }\n    if (inverse) {\n      real /= size;\n      imag /= size;\n    }\n    backend_util.assignToTypedArray(ret, real, imag, r);\n  }\n  return ret;\n}\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAiBA,SAAQA,YAAY,EAAkCC,IAAI,QAAO,uBAAuB;AAGxF,SAAQC,GAAG,QAAO,gBAAgB;AAClC,SAAQC,OAAO,QAAO,oBAAoB;AAC1C,SAAQC,MAAM,QAAO,mBAAmB;AACxC,SAAQC,QAAQ,QAAO,qBAAqB;AAC5C,SAAQC,IAAI,QAAO,iBAAiB;AACpC,SAAQC,QAAQ,QAAO,qBAAqB;AAC5C,SAAQC,IAAI,QAAO,iBAAiB;AACpC,SAAQC,aAAa,QAAO,oBAAoB;AAChD,SAAQC,KAAK,QAAO,kBAAkB;AACtC,SAAQC,GAAG,QAAO,gBAAgB;AAElC;;;AAGA,OAAM,SAAUC,QAAQA,CACpBC,KAAiB,EAAEC,OAAgB,EACnCC,UAA0B;EAC5B,MAAMC,UAAU,GAAGH,KAAK,CAACI,KAAK;EAC9B,MAAMC,KAAK,GAAGF,UAAU,CAAC,CAAC,CAAC;EAC3B,MAAMG,QAAQ,GAAGH,UAAU,CAAC,CAAC,CAAC;EAE9B,MAAMI,SAAS,GAAGL,UAAU,CAACM,IAAI,CAACC,GAAG,CAACT,KAAK,CAACU,MAAM,CAAC;EAEnD,MAAMC,MAAM,GAAGJ,SAAS,CAACK,kBAAkB,CAACjB,IAAI;EAChD,MAAMkB,MAAM,GAAGN,SAAS,CAACK,kBAAkB,CAACnB,IAAI;EAEhD;EACA,MAAMqB,WAAW,GAAG,CAACT,KAAK,EAAEC,QAAQ,CAAC;EACrC,MAAMS,UAAU,GAAG3B,IAAI,CAAC4B,aAAa,CAACF,WAAW,CAAC;EAClD,MAAMG,UAAU,GAAG7B,IAAI,CAAC8B,sBAAsB,CAAC,SAAS,EAAEH,UAAU,CAAC;EACrE,MAAMI,UAAU,GAAG/B,IAAI,CAAC8B,sBAAsB,CAAC,SAAS,EAAEH,UAAU,CAAC;EAErE,KAAK,IAAIK,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGf,KAAK,EAAEe,CAAC,EAAE,EAAE;IAC9B;IACA,MAAMC,CAAC,GAAGxB,KAAK,CAAC;MACdyB,MAAM,EAAE;QAACC,CAAC,EAAEZ;MAAM,CAAC;MACnBa,OAAO,EAAEtB,UAAU;MACnBuB,KAAK,EAAE;QAACC,KAAK,EAAE,CAACN,CAAC,EAAE,CAAC,CAAC;QAAEO,IAAI,EAAE,CAAC,CAAC,EAAErB,QAAQ;MAAC;KAC3C,CAAC;IACF,MAAMsB,CAAC,GAAG/B,KAAK,CAAC;MACdyB,MAAM,EAAE;QAACC,CAAC,EAAEV;MAAM,CAAC;MACnBW,OAAO,EAAEtB,UAAU;MACnBuB,KAAK,EAAE;QAACC,KAAK,EAAE,CAACN,CAAC,EAAE,CAAC,CAAC;QAAEO,IAAI,EAAE,CAAC,CAAC,EAAErB,QAAQ;MAAC;KAC3C,CAAC;IAEF,MAAMN,KAAK,GAAGV,OAAO,CAAC;MAACgC,MAAM,EAAE;QAAC3B,IAAI,EAAE0B,CAAC;QAAE5B,IAAI,EAAEmC;MAAC,CAAC;MAAEJ,OAAO,EAAEtB;IAAU,CAAC,CAAC;IAExE;IACA,MAAM;MAACP,IAAI;MAAEF;IAAI,CAAC,GAAGoC,OAAO,CAAC7B,KAAK,EAAEC,OAAO,EAAEC,UAAU,CAAC;IACxD,MAAM4B,GAAG,GAAG3C,YAAY,CAAC4C,sBAAsB,CAACpC,IAAI,EAAEF,IAAI,CAAC;IAE3D,KAAK,IAAIuC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG1B,QAAQ,EAAE0B,CAAC,EAAE,EAAE;MACjC,MAAMC,CAAC,GAAG9C,YAAY,CAAC+C,mBAAmB,CAACJ,GAAG,EAAEE,CAAC,CAAC;MAClDf,UAAU,CAACG,CAAC,GAAGd,QAAQ,GAAG0B,CAAC,CAAC,GAAGC,CAAC,CAACtC,IAAI;MACrCwB,UAAU,CAACC,CAAC,GAAGd,QAAQ,GAAG0B,CAAC,CAAC,GAAGC,CAAC,CAACxC,IAAI;;IAGvCS,UAAU,CAACiC,6BAA6B,CAACd,CAAC,CAAC;IAC3CnB,UAAU,CAACiC,6BAA6B,CAACP,CAAC,CAAC;IAC3C1B,UAAU,CAACiC,6BAA6B,CAACnC,KAAK,CAAC;;EAGjD,MAAMoC,SAAS,GACXlC,UAAU,CAACmC,cAAc,CAACvB,WAAW,EAAE,SAAS,EAAEG,UAAU,CAAC;EACjE,MAAMqB,SAAS,GACXpC,UAAU,CAACmC,cAAc,CAACvB,WAAW,EAAE,SAAS,EAAEK,UAAU,CAAC;EAEjE,MAAMoB,MAAM,GAAGjD,OAAO,CAClB;IAACgC,MAAM,EAAE;MAAC3B,IAAI,EAAEyC,SAAS;MAAE3C,IAAI,EAAE6C;IAAS,CAAC;IAAEd,OAAO,EAAEtB;EAAU,CAAC,CAAC;EAEtEA,UAAU,CAACiC,6BAA6B,CAACC,SAAS,CAAC;EACnDlC,UAAU,CAACiC,6BAA6B,CAACG,SAAS,CAAC;EAEnD,OAAOC,MAAM;AACf;AAEA,OAAM,SAAUV,OAAOA,CACnB7B,KAAiB,EAAEC,OAAgB,EACnCC,UAA0B;EAC5B,MAAMsC,SAAS,GAAGpD,IAAI,CAAC4B,aAAa,CAAChB,KAAK,CAACI,KAAK,CAAC;EAEjD,MAAMG,SAAS,GAAGL,UAAU,CAACM,IAAI,CAACC,GAAG,CAACT,KAAK,CAACU,MAAM,CAAC;EAEnD,MAAM+B,QAAQ,GACVvC,UAAU,CAACM,IAAI,CAACC,GAAG,CAACF,SAAS,CAACK,kBAAkB,CAACjB,IAAI,CAACe,MAAM,CAAC,CAACgC,MAClD;EAEhB,MAAMC,QAAQ,GACVzC,UAAU,CAACM,IAAI,CAACC,GAAG,CAACF,SAAS,CAACK,kBAAkB,CAACnB,IAAI,CAACiB,MAAM,CAAC,CAACgC,MAClD;EAEhB,IAAIE,aAAa,CAACJ,SAAS,CAAC,EAAE;IAC5B,MAAMD,MAAM,GACRM,SAAS,CAACJ,QAAQ,EAAEE,QAAQ,EAAEH,SAAS,EAAEvC,OAAO,EAAEC,UAAU,CAAC;IAEjE,MAAMY,WAAW,GAAG,CAACd,KAAK,CAACI,KAAK,CAAC,CAAC,CAAC,EAAEJ,KAAK,CAACI,KAAK,CAAC,CAAC,CAAC,CAAC;IAEpD,IAAIH,OAAO,EAAE;MACX,MAAM6C,QAAQ,GACV5C,UAAU,CAACmC,cAAc,CAACvB,WAAW,EAAE,SAAS,EAAEyB,MAAM,CAAC5C,IAAI,CAAC;MAClE,MAAMoD,QAAQ,GACV7C,UAAU,CAACmC,cAAc,CAACvB,WAAW,EAAE,SAAS,EAAEyB,MAAM,CAAC9C,IAAI,CAAC;MAElE,MAAMuD,QAAQ,GAAe9C,UAAU,CAACmC,cAAc,CAClD,EAAE,EAAE,SAAS,EACbjD,IAAI,CAAC6D,iBAAiB,CAACT,SAAiC,EAAE,SAAS,CAAC,CAAC;MACzE,MAAMU,YAAY,GACd1D,QAAQ,CAAC;QAAC8B,MAAM,EAAE;UAACC,CAAC,EAAEyB;QAAQ,CAAC;QAAExB,OAAO,EAAEtB;MAAU,CAAC,CAAC;MAE1D,MAAMiD,WAAW,GACbvD,aAAa,CAACwD,UAAU,CACpB;QAAC9B,MAAM,EAAE;UAAC+B,CAAC,EAAEP,QAAQ;UAAE1B,CAAC,EAAE4B;QAAQ,CAAC;QAAExB,OAAO,EAAEtB;MAAU,CAAC,CACnD;MACd,MAAMoD,WAAW,GACb1D,aAAa,CAACwD,UAAU,CACpB;QAAC9B,MAAM,EAAE;UAAC+B,CAAC,EAAEN,QAAQ;UAAE3B,CAAC,EAAE8B;QAAY,CAAC;QAAE1B,OAAO,EAAEtB;MAAU,CAAC,CACvD;MAEd,MAAMqD,WAAW,GACbrD,UAAU,CAACM,IAAI,CAACC,GAAG,CAAC0C,WAAW,CAACzC,MAAM,CAAC,CAACgC,MAAsB;MAClE,MAAMc,WAAW,GACbtD,UAAU,CAACM,IAAI,CAACC,GAAG,CAAC6C,WAAW,CAAC5C,MAAM,CAAC,CAACgC,MAAsB;MAElExC,UAAU,CAACiC,6BAA6B,CAACW,QAAQ,CAAC;MAClD5C,UAAU,CAACiC,6BAA6B,CAACY,QAAQ,CAAC;MAClD7C,UAAU,CAACiC,6BAA6B,CAACa,QAAQ,CAAC;MAClD9C,UAAU,CAACiC,6BAA6B,CAACe,YAAY,CAAC;MACtDhD,UAAU,CAACiC,6BAA6B,CAACgB,WAAW,CAAC;MACrDjD,UAAU,CAACiC,6BAA6B,CAACmB,WAAW,CAAC;MAErD,OAAO;QAAC3D,IAAI,EAAE4D,WAAW;QAAE9D,IAAI,EAAE+D;MAAW,CAAC;;IAG/C,OAAOjB,MAAM;GACd,MAAM;IACL,MAAM/B,IAAI,GAAGrB,YAAY,CAAC4C,sBAAsB,CAACU,QAAQ,EAAEE,QAAQ,CAAC;IAEpE,MAAMc,SAAS,GACXC,wBAAwB,CAAClD,IAAI,EAAEgC,SAAS,EAAEvC,OAAO,CAAiB;IAEtE,OAAOd,YAAY,CAACwE,sBAAsB,CAACF,SAAS,CAAC;;AAEzD;AAEA,SAASb,aAAaA,CAACjB,IAAY;EACjC,OAAO,CAACA,IAAI,GAAGA,IAAI,GAAG,CAAC,MAAM,CAAC;AAChC;AAEA;AACA,SAASkB,SAASA,CACdJ,QAAsB,EAAEE,QAAsB,EAAEhB,IAAY,EAC5D1B,OAAgB,EAChBC,UAA0B;EAC5B,IAAIyB,IAAI,KAAK,CAAC,EAAE;IACd,OAAO;MAAChC,IAAI,EAAE8C,QAAQ;MAAEhD,IAAI,EAAEkD;IAAQ,CAAC;;EAGzC,MAAMnC,IAAI,GAAGrB,YAAY,CAAC4C,sBAAsB,CAACU,QAAQ,EAAEE,QAAQ,CAAC;EAEpE,MAAMiB,IAAI,GAAGjC,IAAI,GAAG,CAAC;EAErB,MAAMkC,WAAW,GAAG1E,YAAY,CAAC2E,oBAAoB,CAACtD,IAAI,CAAC;EAE3D,MAAMuD,YAAY,GAAGF,WAAW,CAAClE,IAAI;EACrC,MAAMqE,YAAY,GAAGH,WAAW,CAACpE,IAAI;EAErC,MAAMwE,SAAS,GAAG,CAACF,YAAY,CAACG,MAAM,CAAC;EAEvC,MAAMC,YAAY,GACdjE,UAAU,CAACmC,cAAc,CAAC4B,SAAS,EAAE,SAAS,EAAEF,YAAY,CAAC;EACjE,MAAMK,YAAY,GACdlE,UAAU,CAACmC,cAAc,CAAC4B,SAAS,EAAE,SAAS,EAAED,YAAY,CAAC;EAEjE,MAAMK,cAAc,GAAG/E,OAAO,CAC1B;IAACgC,MAAM,EAAE;MAAC3B,IAAI,EAAEwE,YAAY;MAAE1E,IAAI,EAAE2E;IAAY,CAAC;IAAE5C,OAAO,EAAEtB;EAAU,CAAC,CAAC;EAE5E,MAAMoE,UAAU,GAAGnF,YAAY,CAACoF,mBAAmB,CAAC/D,IAAI,CAAC;EAEzD,MAAMgE,WAAW,GAAGF,UAAU,CAAC3E,IAAI;EACnC,MAAM8E,WAAW,GAAGH,UAAU,CAAC7E,IAAI;EAEnC,MAAMiF,QAAQ,GAAG,CAACF,WAAW,CAACN,MAAM,CAAC;EAErC,MAAMS,WAAW,GACbzE,UAAU,CAACmC,cAAc,CAACqC,QAAQ,EAAE,SAAS,EAAEF,WAAW,CAAC;EAC/D,MAAMI,WAAW,GACb1E,UAAU,CAACmC,cAAc,CAACqC,QAAQ,EAAE,SAAS,EAAED,WAAW,CAAC;EAE/D,MAAMI,aAAa,GAAGvF,OAAO,CACzB;IAACgC,MAAM,EAAE;MAAC3B,IAAI,EAAEgF,WAAW;MAAElF,IAAI,EAAEmF;IAAW,CAAC;IAAEpD,OAAO,EAAEtB;EAAU,CAAC,CAAC;EAE1E;EACA,MAAM4E,YAAY,GACdjC,SAAS,CAACkB,YAAY,EAAEC,YAAY,EAAEJ,IAAI,EAAE3D,OAAO,EAAEC,UAAU,CAAC;EAEpE,MAAM6E,aAAa,GAAGD,YAAY,CAACnF,IAAI;EACvC,MAAMqF,aAAa,GAAGF,YAAY,CAACrF,IAAI;EAEvC,MAAMwF,UAAU,GAAG,CAACF,aAAa,CAACb,MAAM,CAAC;EAEzC,MAAMgB,aAAa,GACfhF,UAAU,CAACmC,cAAc,CAAC4C,UAAU,EAAE,SAAS,EAAEF,aAAa,CAAC;EACnE,MAAMI,aAAa,GACfjF,UAAU,CAACmC,cAAc,CAAC4C,UAAU,EAAE,SAAS,EAAED,aAAa,CAAC;EAEnE,MAAMI,eAAe,GAAG9F,OAAO,CAAC;IAC9BgC,MAAM,EAAE;MAAC3B,IAAI,EAAEuF,aAAa;MAAEzF,IAAI,EAAE0F;IAAa,CAAC;IAClD3D,OAAO,EAAEtB;GACV,CAAC;EAEF,MAAMmF,WAAW,GACbxC,SAAS,CAAC2B,WAAW,EAAEC,WAAW,EAAEb,IAAI,EAAE3D,OAAO,EAAEC,UAAU,CAAC;EAElE,MAAMoF,YAAY,GAAGD,WAAW,CAAC1F,IAAI;EACrC,MAAM4F,YAAY,GAAGF,WAAW,CAAC5F,IAAI;EAErC,MAAM+F,SAAS,GAAG,CAACF,YAAY,CAACpB,MAAM,CAAC;EAEvC,MAAMuB,YAAY,GACdvF,UAAU,CAACmC,cAAc,CAACmD,SAAS,EAAE,SAAS,EAAEF,YAAY,CAAC;EACjE,MAAMI,YAAY,GACdxF,UAAU,CAACmC,cAAc,CAACmD,SAAS,EAAE,SAAS,EAAED,YAAY,CAAC;EAEjE,MAAMI,cAAc,GAAGrG,OAAO,CAC1B;IAACgC,MAAM,EAAE;MAAC3B,IAAI,EAAE8F,YAAY;MAAEhG,IAAI,EAAEiG;IAAY,CAAC;IAAElE,OAAO,EAAEtB;EAAU,CAAC,CAAC;EAE5E,MAAM0F,CAAC,GAAGzG,YAAY,CAAC0G,SAAS,CAAClE,IAAI,EAAE1B,OAAO,CAAC;EAC/C,MAAM6F,MAAM,GAAG,CAACF,CAAC,CAACjG,IAAI,CAACuE,MAAM,CAAC;EAE9B,MAAM6B,SAAS,GAAG7F,UAAU,CAACmC,cAAc,CAACyD,MAAM,EAAE,SAAS,EAAEF,CAAC,CAACjG,IAAI,CAAC;EACtE,MAAMqG,SAAS,GAAG9F,UAAU,CAACmC,cAAc,CAACyD,MAAM,EAAE,SAAS,EAAEF,CAAC,CAACnG,IAAI,CAAC;EAEtE,MAAMwG,WAAW,GAAG3G,OAAO,CACvB;IAACgC,MAAM,EAAE;MAAC3B,IAAI,EAAEoG,SAAS;MAAEtG,IAAI,EAAEuG;IAAS,CAAC;IAAExE,OAAO,EAAEtB;EAAU,CAAC,CAAC;EAEtE,MAAMgG,YAAY,GACdxG,QAAQ,CACJ;IAAC4B,MAAM,EAAE;MAAC+B,CAAC,EAAE4C,WAAW;MAAE7E,CAAC,EAAEuE;IAAc,CAAC;IAAEnE,OAAO,EAAEtB;EAAU,CAAC,CAC5D;EAEd,MAAMiG,OAAO,GAAG9G,GAAG,CAAC;IACFiC,MAAM,EAAE;MAAC+B,CAAC,EAAE+B,eAAe;MAAEhE,CAAC,EAAE8E;IAAY,CAAC;IAC7C1E,OAAO,EAAEtB;GACV,CAAe;EAChC,MAAMkG,OAAO,GAAGtG,GAAG,CAAC;IACFwB,MAAM,EAAE;MAAC+B,CAAC,EAAE+B,eAAe;MAAEhE,CAAC,EAAE8E;IAAY,CAAC;IAC7C1E,OAAO,EAAEtB;GACV,CAAe;EAEhC,MAAMmG,WAAW,GAAG1G,IAAI,CAAC;IAAC2B,MAAM,EAAE;MAACtB,KAAK,EAAEmG;IAAO,CAAC;IAAE3E,OAAO,EAAEtB;EAAU,CAAC,CAAC;EACzE,MAAMoG,WAAW,GAAG3G,IAAI,CAAC;IAAC2B,MAAM,EAAE;MAACtB,KAAK,EAAEoG;IAAO,CAAC;IAAE5E,OAAO,EAAEtB;EAAU,CAAC,CAAC;EAEzE,MAAMqG,WAAW,GAAG9G,IAAI,CAAC;IAAC6B,MAAM,EAAE;MAACtB,KAAK,EAAEmG;IAAO,CAAC;IAAE3E,OAAO,EAAEtB;EAAU,CAAC,CAAC;EACzE,MAAMsG,WAAW,GAAG/G,IAAI,CAAC;IAAC6B,MAAM,EAAE;MAACtB,KAAK,EAAEoG;IAAO,CAAC;IAAE5E,OAAO,EAAEtB;EAAU,CAAC,CAAC;EAEzE,MAAMuG,KAAK,GAAGlH,MAAM,CAAC;IACnB+B,MAAM,EAAE,CAAC+E,WAAqB,EAAEC,WAAqB,CAAC;IACtD9E,OAAO,EAAEtB,UAAU;IACnBuB,KAAK,EAAE;MAACiF,IAAI,EAAE;IAAC;GAChB,CAAC;EACF,MAAMC,KAAK,GAAGpH,MAAM,CAAC;IACnB+B,MAAM,EAAE,CAACiF,WAAqB,EAAEC,WAAqB,CAAC;IACtDhF,OAAO,EAAEtB,UAAU;IACnBuB,KAAK,EAAE;MAACiF,IAAI,EAAE;IAAC;GAChB,CAAC;EAEF,MAAME,SAAS,GAAG1G,UAAU,CAACM,IAAI,CAACC,GAAG,CAACgG,KAAK,CAAC/F,MAAM,CAAC,CAACgC,MAAsB;EAC1E,MAAMmE,SAAS,GAAG3G,UAAU,CAACM,IAAI,CAACC,GAAG,CAACkG,KAAK,CAACjG,MAAM,CAAC,CAACgC,MAAsB;EAE1ExC,UAAU,CAACiC,6BAA6B,CAACgC,YAAY,CAAC;EACtDjE,UAAU,CAACiC,6BAA6B,CAACiC,YAAY,CAAC;EACtDlE,UAAU,CAACiC,6BAA6B,CAACkC,cAAc,CAAC;EACxDnE,UAAU,CAACiC,6BAA6B,CAACwC,WAAW,CAAC;EACrDzE,UAAU,CAACiC,6BAA6B,CAACyC,WAAW,CAAC;EACrD1E,UAAU,CAACiC,6BAA6B,CAAC0C,aAAa,CAAC;EACvD3E,UAAU,CAACiC,6BAA6B,CAAC+C,aAAa,CAAC;EACvDhF,UAAU,CAACiC,6BAA6B,CAACgD,aAAa,CAAC;EACvDjF,UAAU,CAACiC,6BAA6B,CAACiD,eAAe,CAAC;EACzDlF,UAAU,CAACiC,6BAA6B,CAACsD,YAAY,CAAC;EACtDvF,UAAU,CAACiC,6BAA6B,CAACuD,YAAY,CAAC;EACtDxF,UAAU,CAACiC,6BAA6B,CAACwD,cAAc,CAAC;EACxDzF,UAAU,CAACiC,6BAA6B,CAAC4D,SAAS,CAAC;EACnD7F,UAAU,CAACiC,6BAA6B,CAAC6D,SAAS,CAAC;EACnD9F,UAAU,CAACiC,6BAA6B,CAAC8D,WAAW,CAAC;EACrD/F,UAAU,CAACiC,6BAA6B,CAAC+D,YAAY,CAAC;EACtDhG,UAAU,CAACiC,6BAA6B,CAACgE,OAAO,CAAC;EACjDjG,UAAU,CAACiC,6BAA6B,CAACiE,OAAO,CAAC;EACjDlG,UAAU,CAACiC,6BAA6B,CAACkE,WAAW,CAAC;EACrDnG,UAAU,CAACiC,6BAA6B,CAACoE,WAAW,CAAC;EACrDrG,UAAU,CAACiC,6BAA6B,CAACmE,WAAW,CAAC;EACrDpG,UAAU,CAACiC,6BAA6B,CAACqE,WAAW,CAAC;EACrDtG,UAAU,CAACiC,6BAA6B,CAACsE,KAAK,CAAC;EAC/CvG,UAAU,CAACiC,6BAA6B,CAACwE,KAAK,CAAC;EAE/C,OAAO;IAAChH,IAAI,EAAEiH,SAAS;IAAEnH,IAAI,EAAEoH;EAAS,CAAC;AAC3C;AAEA;AACA,SAASnD,wBAAwBA,CAC7BlD,IAAgB,EAAEmB,IAAY,EAAE1B,OAAgB;EAClD,MAAM6G,GAAG,GAAG,IAAIC,YAAY,CAACpF,IAAI,GAAG,CAAC,CAAC;EACtC;EACA,KAAK,IAAIN,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGM,IAAI,EAAEN,CAAC,EAAE,EAAE;IAC7B,IAAI1B,IAAI,GAAG,GAAG;IACd,IAAIF,IAAI,GAAG,GAAG;IACd,KAAK,IAAIwC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGN,IAAI,EAAEM,CAAC,EAAE,EAAE;MAC7B,MAAM2D,CAAC,GAAGzG,YAAY,CAAC6H,QAAQ,CAAC3F,CAAC,GAAGY,CAAC,EAAEN,IAAI,EAAE1B,OAAO,CAAC;MACrD,MAAMgH,IAAI,GAAG9H,YAAY,CAAC+C,mBAAmB,CAAC1B,IAAoB,EAAEyB,CAAC,CAAC;MACtEtC,IAAI,IAAIsH,IAAI,CAACtH,IAAI,GAAGiG,CAAC,CAACjG,IAAI,GAAGsH,IAAI,CAACxH,IAAI,GAAGmG,CAAC,CAACnG,IAAI;MAC/CA,IAAI,IAAIwH,IAAI,CAACtH,IAAI,GAAGiG,CAAC,CAACnG,IAAI,GAAGwH,IAAI,CAACxH,IAAI,GAAGmG,CAAC,CAACjG,IAAI;;IAEjD,IAAIM,OAAO,EAAE;MACXN,IAAI,IAAIgC,IAAI;MACZlC,IAAI,IAAIkC,IAAI;;IAEdxC,YAAY,CAAC+H,kBAAkB,CAACJ,GAAG,EAAEnH,IAAI,EAAEF,IAAI,EAAE4B,CAAC,CAAC;;EAErD,OAAOyF,GAAG;AACZ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}