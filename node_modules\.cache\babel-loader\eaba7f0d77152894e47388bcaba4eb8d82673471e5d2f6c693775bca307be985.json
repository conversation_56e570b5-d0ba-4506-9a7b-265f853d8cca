{"ast": null, "code": "/**\n * @license\n * Copyright 2019 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport './engine';\nimport * as device_util from './device_util';\nimport { env } from './environment';\nconst ENV = env();\n/**\n * This file contains environment-related flag registrations.\n */\n/** Whether to enable debug mode. */\nENV.registerFlag('DEBUG', () => false, debugValue => {\n  if (debugValue) {\n    console.warn('Debugging mode is ON. The output of every math call will ' + 'be downloaded to CPU and checked for NaNs. ' + 'This significantly impacts performance.');\n  }\n});\n/** Whether we are in a browser (as versus, say, node.js) environment. */\nENV.registerFlag('IS_BROWSER', () => device_util.isBrowser());\n/** Whether we are in a browser (as versus, say, node.js) environment. */\nENV.registerFlag('IS_NODE', () => typeof process !== 'undefined' && typeof process.versions !== 'undefined' && typeof process.versions.node !== 'undefined');\n/** Whether this browser is Chrome. */\nENV.registerFlag('IS_CHROME', () => typeof navigator !== 'undefined' && navigator != null && navigator.userAgent != null && /Chrome/.test(navigator.userAgent) && /Google Inc/.test(navigator.vendor));\n/** Whether this browser is Safari. */\nENV.registerFlag('IS_SAFARI', () => typeof navigator !== 'undefined' && navigator != null && navigator.userAgent != null && /Safari/.test(navigator.userAgent) && /Apple/.test(navigator.vendor));\n/**\n * True when the environment is \"production\" where we disable safety checks\n * to gain performance.\n */\nENV.registerFlag('PROD', () => false);\n/**\n * Whether to do sanity checks when inferring a shape from user-provided\n * values, used when creating a new tensor.\n */\nENV.registerFlag('TENSORLIKE_CHECK_SHAPE_CONSISTENCY', () => ENV.getBool('DEBUG'));\n/** Whether deprecation warnings are enabled. */\nENV.registerFlag('DEPRECATION_WARNINGS_ENABLED', () => true);\n/** True if running unit tests. */\nENV.registerFlag('IS_TEST', () => false);\n/** Whether to check computation result for errors. */\nENV.registerFlag('CHECK_COMPUTATION_FOR_ERRORS', () => ENV.getBool('DEBUG'));\n/** Whether the backend needs to wrap input to imageBitmap. */\nENV.registerFlag('WRAP_TO_IMAGEBITMAP', () => false);\n/** Whether to enable canvas2d willReadFrequently for GPU backends */\nENV.registerFlag('CANVAS2D_WILL_READ_FREQUENTLY_FOR_GPU', () => false);\n/** Whether to use setTimeoutCustom */\nENV.registerFlag('USE_SETTIMEOUTCUSTOM', () => false);", "map": {"version": 3, "names": ["device_util", "env", "ENV", "registerFlag", "debugValue", "console", "warn", "<PERSON><PERSON><PERSON><PERSON>", "process", "versions", "node", "navigator", "userAgent", "test", "vendor", "getBool"], "sources": ["C:\\tfjs-core\\src\\flags.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2019 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport './engine';\n\nimport * as device_util from './device_util';\nimport {env} from './environment';\n\nconst ENV = env();\n\n/**\n * This file contains environment-related flag registrations.\n */\n\n/** Whether to enable debug mode. */\nENV.registerFlag('DEBUG', () => false, debugValue => {\n  if (debugValue) {\n    console.warn(\n        'Debugging mode is ON. The output of every math call will ' +\n        'be downloaded to CPU and checked for NaNs. ' +\n        'This significantly impacts performance.');\n  }\n});\n\n/** Whether we are in a browser (as versus, say, node.js) environment. */\nENV.registerFlag('IS_BROWSER', () => device_util.isBrowser());\n\n/** Whether we are in a browser (as versus, say, node.js) environment. */\nENV.registerFlag(\n    'IS_NODE',\n    () => (typeof process !== 'undefined') &&\n        (typeof process.versions !== 'undefined') &&\n        (typeof process.versions.node !== 'undefined'));\n\n/** Whether this browser is Chrome. */\nENV.registerFlag(\n    'IS_CHROME',\n    () => typeof navigator !== 'undefined' && navigator != null &&\n        navigator.userAgent != null && /Chrome/.test(navigator.userAgent) &&\n        /Google Inc/.test(navigator.vendor));\n\n/** Whether this browser is Safari. */\nENV.registerFlag(\n    'IS_SAFARI',\n    () => typeof navigator !== 'undefined' && navigator != null &&\n        navigator.userAgent != null && /Safari/.test(navigator.userAgent) &&\n        /Apple/.test(navigator.vendor));\n/**\n * True when the environment is \"production\" where we disable safety checks\n * to gain performance.\n */\nENV.registerFlag('PROD', () => false);\n\n/**\n * Whether to do sanity checks when inferring a shape from user-provided\n * values, used when creating a new tensor.\n */\nENV.registerFlag(\n    'TENSORLIKE_CHECK_SHAPE_CONSISTENCY', () => ENV.getBool('DEBUG'));\n\n/** Whether deprecation warnings are enabled. */\nENV.registerFlag('DEPRECATION_WARNINGS_ENABLED', () => true);\n\n/** True if running unit tests. */\nENV.registerFlag('IS_TEST', () => false);\n\n/** Whether to check computation result for errors. */\nENV.registerFlag('CHECK_COMPUTATION_FOR_ERRORS', () => ENV.getBool('DEBUG'));\n\n/** Whether the backend needs to wrap input to imageBitmap. */\nENV.registerFlag('WRAP_TO_IMAGEBITMAP', () => false);\n\n/** Whether to enable canvas2d willReadFrequently for GPU backends */\nENV.registerFlag('CANVAS2D_WILL_READ_FREQUENTLY_FOR_GPU', () => false);\n\n/** Whether to use setTimeoutCustom */\nENV.registerFlag('USE_SETTIMEOUTCUSTOM', () => false);\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAgBA,OAAO,UAAU;AAEjB,OAAO,KAAKA,WAAW,MAAM,eAAe;AAC5C,SAAQC,GAAG,QAAO,eAAe;AAEjC,MAAMC,GAAG,GAAGD,GAAG,EAAE;AAEjB;;;AAIA;AACAC,GAAG,CAACC,YAAY,CAAC,OAAO,EAAE,MAAM,KAAK,EAAEC,UAAU,IAAG;EAClD,IAAIA,UAAU,EAAE;IACdC,OAAO,CAACC,IAAI,CACR,2DAA2D,GAC3D,6CAA6C,GAC7C,yCAAyC,CAAC;;AAElD,CAAC,CAAC;AAEF;AACAJ,GAAG,CAACC,YAAY,CAAC,YAAY,EAAE,MAAMH,WAAW,CAACO,SAAS,EAAE,CAAC;AAE7D;AACAL,GAAG,CAACC,YAAY,CACZ,SAAS,EACT,MAAO,OAAOK,OAAO,KAAK,WAAW,IAChC,OAAOA,OAAO,CAACC,QAAQ,KAAK,WAAY,IACxC,OAAOD,OAAO,CAACC,QAAQ,CAACC,IAAI,KAAK,WAAY,CAAC;AAEvD;AACAR,GAAG,CAACC,YAAY,CACZ,WAAW,EACX,MAAM,OAAOQ,SAAS,KAAK,WAAW,IAAIA,SAAS,IAAI,IAAI,IACvDA,SAAS,CAACC,SAAS,IAAI,IAAI,IAAI,QAAQ,CAACC,IAAI,CAACF,SAAS,CAACC,SAAS,CAAC,IACjE,YAAY,CAACC,IAAI,CAACF,SAAS,CAACG,MAAM,CAAC,CAAC;AAE5C;AACAZ,GAAG,CAACC,YAAY,CACZ,WAAW,EACX,MAAM,OAAOQ,SAAS,KAAK,WAAW,IAAIA,SAAS,IAAI,IAAI,IACvDA,SAAS,CAACC,SAAS,IAAI,IAAI,IAAI,QAAQ,CAACC,IAAI,CAACF,SAAS,CAACC,SAAS,CAAC,IACjE,OAAO,CAACC,IAAI,CAACF,SAAS,CAACG,MAAM,CAAC,CAAC;AACvC;;;;AAIAZ,GAAG,CAACC,YAAY,CAAC,MAAM,EAAE,MAAM,KAAK,CAAC;AAErC;;;;AAIAD,GAAG,CAACC,YAAY,CACZ,oCAAoC,EAAE,MAAMD,GAAG,CAACa,OAAO,CAAC,OAAO,CAAC,CAAC;AAErE;AACAb,GAAG,CAACC,YAAY,CAAC,8BAA8B,EAAE,MAAM,IAAI,CAAC;AAE5D;AACAD,GAAG,CAACC,YAAY,CAAC,SAAS,EAAE,MAAM,KAAK,CAAC;AAExC;AACAD,GAAG,CAACC,YAAY,CAAC,8BAA8B,EAAE,MAAMD,GAAG,CAACa,OAAO,CAAC,OAAO,CAAC,CAAC;AAE5E;AACAb,GAAG,CAACC,YAAY,CAAC,qBAAqB,EAAE,MAAM,KAAK,CAAC;AAEpD;AACAD,GAAG,CAACC,YAAY,CAAC,uCAAuC,EAAE,MAAM,KAAK,CAAC;AAEtE;AACAD,GAAG,CAACC,YAAY,CAAC,sBAAsB,EAAE,MAAM,KAAK,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}