{"ast": null, "code": "/**\n * @license\n * Copyright 2019 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport * as util from '../util';\n/**\n * Normalize noise shape based on provided tensor and noise shape.\n *\n * @param x Tensor.\n * @param noiseShape The shape for the randomly generated keep/drop flags, as\n *   an array of numbers. Optional.\n * @returns Normalized noise shape.\n */\nexport function getNoiseShape(x, noiseShape) {\n  if (noiseShape == null) {\n    return x.shape.slice();\n  }\n  if (util.arraysEqual(x.shape, noiseShape)) {\n    return noiseShape;\n  }\n  if (x.shape.length === noiseShape.length) {\n    const newDimension = [];\n    for (let i = 0; i < x.shape.length; i++) {\n      if (noiseShape[i] == null && x.shape[i] != null) {\n        newDimension.push(x.shape[i]);\n      } else {\n        newDimension.push(noiseShape[i]);\n      }\n    }\n    return newDimension;\n  }\n  return noiseShape;\n}", "map": {"version": 3, "names": ["util", "getNoiseShape", "x", "noiseShape", "shape", "slice", "arraysEqual", "length", "newDimension", "i", "push"], "sources": ["C:\\tfjs-core\\src\\ops\\dropout_util.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2019 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {Tensor} from '../tensor';\nimport * as util from '../util';\n\n/**\n * Normalize noise shape based on provided tensor and noise shape.\n *\n * @param x Tensor.\n * @param noiseShape The shape for the randomly generated keep/drop flags, as\n *   an array of numbers. Optional.\n * @returns Normalized noise shape.\n */\nexport function getNoiseShape(x: Tensor, noiseShape?: number[]): number[] {\n  if (noiseShape == null) {\n    return x.shape.slice();\n  }\n  if (util.arraysEqual(x.shape, noiseShape)) {\n    return noiseShape;\n  }\n  if (x.shape.length === noiseShape.length) {\n    const newDimension: number[] = [];\n    for (let i = 0; i < x.shape.length; i++) {\n      if (noiseShape[i] == null && x.shape[i] != null) {\n        newDimension.push(x.shape[i]);\n      } else {\n        newDimension.push(noiseShape[i]);\n      }\n    }\n    return newDimension;\n  }\n\n  return noiseShape;\n}\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAkBA,OAAO,KAAKA,IAAI,MAAM,SAAS;AAE/B;;;;;;;;AAQA,OAAM,SAAUC,aAAaA,CAACC,CAAS,EAAEC,UAAqB;EAC5D,IAAIA,UAAU,IAAI,IAAI,EAAE;IACtB,OAAOD,CAAC,CAACE,KAAK,CAACC,KAAK,EAAE;;EAExB,IAAIL,IAAI,CAACM,WAAW,CAACJ,CAAC,CAACE,KAAK,EAAED,UAAU,CAAC,EAAE;IACzC,OAAOA,UAAU;;EAEnB,IAAID,CAAC,CAACE,KAAK,CAACG,MAAM,KAAKJ,UAAU,CAACI,MAAM,EAAE;IACxC,MAAMC,YAAY,GAAa,EAAE;IACjC,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGP,CAAC,CAACE,KAAK,CAACG,MAAM,EAAEE,CAAC,EAAE,EAAE;MACvC,IAAIN,UAAU,CAACM,CAAC,CAAC,IAAI,IAAI,IAAIP,CAAC,CAACE,KAAK,CAACK,CAAC,CAAC,IAAI,IAAI,EAAE;QAC/CD,YAAY,CAACE,IAAI,CAACR,CAAC,CAACE,KAAK,CAACK,CAAC,CAAC,CAAC;OAC9B,MAAM;QACLD,YAAY,CAACE,IAAI,CAACP,UAAU,CAACM,CAAC,CAAC,CAAC;;;IAGpC,OAAOD,YAAY;;EAGrB,OAAOL,UAAU;AACnB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}