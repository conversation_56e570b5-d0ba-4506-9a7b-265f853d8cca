{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.transformReply = exports.transformArguments = void 0;\nfunction transformArguments() {\n  return ['CLIENT', 'TRACKINGINFO'];\n}\nexports.transformArguments = transformArguments;\nfunction transformReply(reply) {\n  return {\n    flags: new Set(reply[1]),\n    redirect: reply[3],\n    prefixes: reply[5]\n  };\n}\nexports.transformReply = transformReply;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "transformReply", "transformArguments", "reply", "flags", "Set", "redirect", "prefixes"], "sources": ["C:/tmsft/node_modules/@redis/client/dist/lib/commands/CLIENT_TRACKINGINFO.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.transformReply = exports.transformArguments = void 0;\nfunction transformArguments() {\n    return ['CLIENT', 'TRACKINGINFO'];\n}\nexports.transformArguments = transformArguments;\nfunction transformReply(reply) {\n    return {\n        flags: new Set(reply[1]),\n        redirect: reply[3],\n        prefixes: reply[5]\n    };\n}\nexports.transformReply = transformReply;\n"], "mappings": "AAAA,YAAY;;AACZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEC,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7DD,OAAO,CAACE,cAAc,GAAGF,OAAO,CAACG,kBAAkB,GAAG,KAAK,CAAC;AAC5D,SAASA,kBAAkBA,CAAA,EAAG;EAC1B,OAAO,CAAC,QAAQ,EAAE,cAAc,CAAC;AACrC;AACAH,OAAO,CAACG,kBAAkB,GAAGA,kBAAkB;AAC/C,SAASD,cAAcA,CAACE,KAAK,EAAE;EAC3B,OAAO;IACHC,KAAK,EAAE,IAAIC,GAAG,CAACF,KAAK,CAAC,CAAC,CAAC,CAAC;IACxBG,QAAQ,EAAEH,KAAK,CAAC,CAAC,CAAC;IAClBI,QAAQ,EAAEJ,KAAK,CAAC,CAAC;EACrB,CAAC;AACL;AACAJ,OAAO,CAACE,cAAc,GAAGA,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}