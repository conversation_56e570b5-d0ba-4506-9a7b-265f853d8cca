{"ast": null, "code": "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { Pow } from '@tensorflow/tfjs-core';\nimport { createSimpleBinaryKernelImpl } from '../utils/binary_impl';\nimport { binaryKernelFunc } from '../utils/binary_utils';\nexport const powImpl = createSimpleBinaryKernelImpl((a, b) => Math.pow(a, b));\nexport const pow = binaryKernelFunc(Pow, powImpl);\nexport const powConfig = {\n  kernelName: Pow,\n  backendName: 'cpu',\n  kernelFunc: pow\n};", "map": {"version": 3, "names": ["<PERSON>w", "createSimpleBinaryKernelImpl", "binaryKernelFunc", "powImpl", "a", "b", "Math", "pow", "powConfig", "kernelName", "backendName", "kernelFunc"], "sources": ["C:\\tfjs-backend-cpu\\src\\kernels\\Pow.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {KernelConfig, Pow} from '@tensorflow/tfjs-core';\n\nimport {createSimpleBinaryKernelImpl} from '../utils/binary_impl';\nimport {binaryKernelFunc} from '../utils/binary_utils';\n\nexport const powImpl =\n    createSimpleBinaryKernelImpl((a: number, b: number) => Math.pow(a, b));\nexport const pow = binaryKernelFunc(Pow, powImpl);\n\nexport const powConfig: KernelConfig = {\n  kernelName: Pow,\n  backendName: 'cpu',\n  kernelFunc: pow\n};\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAiBA,SAAsBA,GAAG,QAAO,uBAAuB;AAEvD,SAAQC,4BAA4B,QAAO,sBAAsB;AACjE,SAAQC,gBAAgB,QAAO,uBAAuB;AAEtD,OAAO,MAAMC,OAAO,GAChBF,4BAA4B,CAAC,CAACG,CAAS,EAAEC,CAAS,KAAKC,IAAI,CAACC,GAAG,CAACH,CAAC,EAAEC,CAAC,CAAC,CAAC;AAC1E,OAAO,MAAME,GAAG,GAAGL,gBAAgB,CAACF,GAAG,EAAEG,OAAO,CAAC;AAEjD,OAAO,MAAMK,SAAS,GAAiB;EACrCC,UAAU,EAAET,GAAG;EACfU,WAAW,EAAE,KAAK;EAClBC,UAAU,EAAEJ;CACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}