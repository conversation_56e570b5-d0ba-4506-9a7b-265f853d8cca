{"ast": null, "code": "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n// Returns the image center in pixels.\nexport function getImageCenter(center, imageHeight, imageWidth) {\n  const centerX = imageWidth * (typeof center === 'number' ? center : center[0]);\n  const centerY = imageHeight * (typeof center === 'number' ? center : center[1]);\n  return [centerX, centerY];\n}", "map": {"version": 3, "names": ["getImageCenter", "center", "imageHeight", "imageWidth", "centerX", "centerY"], "sources": ["C:\\tfjs-core\\src\\ops\\rotate_util.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\n// Returns the image center in pixels.\nexport function getImageCenter(\n    center: number|[number, number], imageHeight: number,\n    imageWidth: number): [number, number] {\n  const centerX =\n      imageWidth * (typeof center === 'number' ? center : center[0]);\n  const centerY =\n      imageHeight * (typeof center === 'number' ? center : center[1]);\n  return [centerX, centerY];\n}\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAiBA;AACA,OAAM,SAAUA,cAAcA,CAC1BC,MAA+B,EAAEC,WAAmB,EACpDC,UAAkB;EACpB,MAAMC,OAAO,GACTD,UAAU,IAAI,OAAOF,MAAM,KAAK,QAAQ,GAAGA,MAAM,GAAGA,MAAM,CAAC,CAAC,CAAC,CAAC;EAClE,MAAMI,OAAO,GACTH,WAAW,IAAI,OAAOD,MAAM,KAAK,QAAQ,GAAGA,MAAM,GAAGA,MAAM,CAAC,CAAC,CAAC,CAAC;EACnE,OAAO,CAACG,OAAO,EAAEC,OAAO,CAAC;AAC3B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}