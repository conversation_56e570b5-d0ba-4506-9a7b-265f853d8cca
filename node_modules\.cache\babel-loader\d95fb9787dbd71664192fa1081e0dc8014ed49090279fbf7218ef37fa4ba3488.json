{"ast": null, "code": "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { Mod } from '../kernel_names';\nimport { assertAndGetBroadcastShape, getReductionAxes } from '../ops/broadcast_util';\nimport { div } from '../ops/div';\nimport { floor } from '../ops/floor';\nimport { mul } from '../ops/mul';\nimport { neg } from '../ops/neg';\nimport { reshape } from '../ops/reshape';\nimport { sum } from '../ops/sum';\nexport const modGradConfig = {\n  kernelName: Mod,\n  inputsToSave: ['a', 'b'],\n  gradFunc: (dy, saved) => {\n    const [a, b] = saved;\n    const outShape = assertAndGetBroadcastShape(a.shape, b.shape);\n    const derA = () => {\n      const reduceAxes = getReductionAxes(a.shape, outShape);\n      if (reduceAxes.length > 0) {\n        return reshape(sum(dy, reduceAxes), a.shape);\n      }\n      return dy;\n    };\n    const derB = () => {\n      const res = mul(dy, neg(floor(div(a, b))));\n      const reduceAxes = getReductionAxes(b.shape, outShape);\n      if (reduceAxes.length > 0) {\n        return reshape(sum(res, reduceAxes), b.shape);\n      }\n      return res;\n    };\n    return {\n      a: derA,\n      b: derB\n    };\n  }\n};", "map": {"version": 3, "names": ["Mod", "assertAndGetBroadcastShape", "getReductionAxes", "div", "floor", "mul", "neg", "reshape", "sum", "modGradConfig", "kernelName", "inputsToSave", "grad<PERSON>unc", "dy", "saved", "a", "b", "outShape", "shape", "derA", "reduceAxes", "length", "derB", "res"], "sources": ["C:\\tfjs-core\\src\\gradients\\Mod_grad.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {Mod} from '../kernel_names';\nimport {GradConfig} from '../kernel_registry';\nimport {assertAndGetBroadcastShape, getReductionAxes} from '../ops/broadcast_util';\nimport {div} from '../ops/div';\nimport {floor} from '../ops/floor';\nimport {mul} from '../ops/mul';\nimport {neg} from '../ops/neg';\nimport {reshape} from '../ops/reshape';\nimport {sum} from '../ops/sum';\nimport {Tensor} from '../tensor';\n\nexport const modGradConfig: GradConfig = {\n  kernelName: Mod,\n  inputsToSave: ['a', 'b'],\n  gradFunc: (dy: Tensor, saved: Tensor[]) => {\n    const [a, b] = saved;\n    const outShape = assertAndGetBroadcastShape(a.shape, b.shape);\n\n    const derA = () => {\n      const reduceAxes = getReductionAxes(a.shape, outShape);\n      if (reduceAxes.length > 0) {\n        return reshape(sum(dy, reduceAxes), a.shape);\n      }\n      return dy;\n    };\n    const derB = () => {\n      const res = mul(dy, neg(floor(div(a, b))));\n      const reduceAxes = getReductionAxes(b.shape, outShape);\n      if (reduceAxes.length > 0) {\n        return reshape(sum(res, reduceAxes), b.shape);\n      }\n      return res;\n    };\n    return {a: derA, b: derB};\n  }\n};\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAiBA,SAAQA,GAAG,QAAO,iBAAiB;AAEnC,SAAQC,0BAA0B,EAAEC,gBAAgB,QAAO,uBAAuB;AAClF,SAAQC,GAAG,QAAO,YAAY;AAC9B,SAAQC,KAAK,QAAO,cAAc;AAClC,SAAQC,GAAG,QAAO,YAAY;AAC9B,SAAQC,GAAG,QAAO,YAAY;AAC9B,SAAQC,OAAO,QAAO,gBAAgB;AACtC,SAAQC,GAAG,QAAO,YAAY;AAG9B,OAAO,MAAMC,aAAa,GAAe;EACvCC,UAAU,EAAEV,GAAG;EACfW,YAAY,EAAE,CAAC,GAAG,EAAE,GAAG,CAAC;EACxBC,QAAQ,EAAEA,CAACC,EAAU,EAAEC,KAAe,KAAI;IACxC,MAAM,CAACC,CAAC,EAAEC,CAAC,CAAC,GAAGF,KAAK;IACpB,MAAMG,QAAQ,GAAGhB,0BAA0B,CAACc,CAAC,CAACG,KAAK,EAAEF,CAAC,CAACE,KAAK,CAAC;IAE7D,MAAMC,IAAI,GAAGA,CAAA,KAAK;MAChB,MAAMC,UAAU,GAAGlB,gBAAgB,CAACa,CAAC,CAACG,KAAK,EAAED,QAAQ,CAAC;MACtD,IAAIG,UAAU,CAACC,MAAM,GAAG,CAAC,EAAE;QACzB,OAAOd,OAAO,CAACC,GAAG,CAACK,EAAE,EAAEO,UAAU,CAAC,EAAEL,CAAC,CAACG,KAAK,CAAC;;MAE9C,OAAOL,EAAE;IACX,CAAC;IACD,MAAMS,IAAI,GAAGA,CAAA,KAAK;MAChB,MAAMC,GAAG,GAAGlB,GAAG,CAACQ,EAAE,EAAEP,GAAG,CAACF,KAAK,CAACD,GAAG,CAACY,CAAC,EAAEC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC1C,MAAMI,UAAU,GAAGlB,gBAAgB,CAACc,CAAC,CAACE,KAAK,EAAED,QAAQ,CAAC;MACtD,IAAIG,UAAU,CAACC,MAAM,GAAG,CAAC,EAAE;QACzB,OAAOd,OAAO,CAACC,GAAG,CAACe,GAAG,EAAEH,UAAU,CAAC,EAAEJ,CAAC,CAACE,KAAK,CAAC;;MAE/C,OAAOK,GAAG;IACZ,CAAC;IACD,OAAO;MAACR,CAAC,EAAEI,IAAI;MAAEH,CAAC,EAAEM;IAAI,CAAC;EAC3B;CACD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}