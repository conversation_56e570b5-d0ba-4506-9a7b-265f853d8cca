{"ast": null, "code": "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { AvgPool3D, backend_util } from '@tensorflow/tfjs-core';\nimport { Pool3DProgram } from '../pool_gpu';\nexport function avgPool3D(args) {\n  const {\n    inputs,\n    backend,\n    attrs\n  } = args;\n  const {\n    x\n  } = inputs;\n  const {\n    filterSize,\n    strides,\n    pad,\n    dimRoundingMode,\n    dataFormat\n  } = attrs;\n  const dilations = [1, 1, 1];\n  const convInfo = backend_util.computePool3DInfo(x.shape, filterSize, strides, dilations, pad, dimRoundingMode, dataFormat);\n  const avgPoolProgram = new Pool3DProgram(convInfo, 'avg', false);\n  return backend.runWebGLProgram(avgPoolProgram, [x], 'float32');\n}\nexport const avgPool3DConfig = {\n  kernelName: AvgPool3D,\n  backendName: 'webgl',\n  kernelFunc: avgPool3D\n};", "map": {"version": 3, "names": ["AvgPool3D", "backend_util", "Pool3DProgram", "avgPool3D", "args", "inputs", "backend", "attrs", "x", "filterSize", "strides", "pad", "dimRoundingMode", "dataFormat", "dilations", "convInfo", "computePool3DInfo", "shape", "avgPoolProgram", "runWebGLProgram", "avgPool3DConfig", "kernelName", "backendName", "kernelFunc"], "sources": ["C:\\tfjs-backend-webgl\\src\\kernels\\AvgPool3D.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport {AvgPool3D, AvgPool3DAttrs, AvgPool3DInputs, backend_util, KernelConfig, KernelFunc, TensorInfo} from '@tensorflow/tfjs-core';\n\nimport {MathBackendWebGL} from '../backend_webgl';\nimport {Pool3DProgram} from '../pool_gpu';\n\nexport function avgPool3D(args: {\n  inputs: AvgPool3DInputs,\n  backend: MathBackendWebGL,\n  attrs: AvgPool3DAttrs\n}): TensorInfo {\n  const {inputs, backend, attrs} = args;\n  const {x} = inputs;\n  const {filterSize, strides, pad, dimRoundingMode, dataFormat} = attrs;\n  const dilations: [number, number, number] = [1, 1, 1];\n\n  const convInfo = backend_util.computePool3DInfo(\n      x.shape as [number, number, number, number, number], filterSize, strides,\n      dilations, pad, dimRoundingMode, dataFormat);\n  const avgPoolProgram = new Pool3DProgram(convInfo, 'avg', false);\n  return backend.runWebGLProgram(avgPoolProgram, [x], 'float32');\n}\n\nexport const avgPool3DConfig: KernelConfig = {\n  kernelName: AvgPool3D,\n  backendName: 'webgl',\n  kernelFunc: avgPool3D as unknown as KernelFunc\n};\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAgBA,SAAQA,SAAS,EAAmCC,YAAY,QAA6C,uBAAuB;AAGpI,SAAQC,aAAa,QAAO,aAAa;AAEzC,OAAM,SAAUC,SAASA,CAACC,IAIzB;EACC,MAAM;IAACC,MAAM;IAAEC,OAAO;IAAEC;EAAK,CAAC,GAAGH,IAAI;EACrC,MAAM;IAACI;EAAC,CAAC,GAAGH,MAAM;EAClB,MAAM;IAACI,UAAU;IAAEC,OAAO;IAAEC,GAAG;IAAEC,eAAe;IAAEC;EAAU,CAAC,GAAGN,KAAK;EACrE,MAAMO,SAAS,GAA6B,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;EAErD,MAAMC,QAAQ,GAAGd,YAAY,CAACe,iBAAiB,CAC3CR,CAAC,CAACS,KAAiD,EAAER,UAAU,EAAEC,OAAO,EACxEI,SAAS,EAAEH,GAAG,EAAEC,eAAe,EAAEC,UAAU,CAAC;EAChD,MAAMK,cAAc,GAAG,IAAIhB,aAAa,CAACa,QAAQ,EAAE,KAAK,EAAE,KAAK,CAAC;EAChE,OAAOT,OAAO,CAACa,eAAe,CAACD,cAAc,EAAE,CAACV,CAAC,CAAC,EAAE,SAAS,CAAC;AAChE;AAEA,OAAO,MAAMY,eAAe,GAAiB;EAC3CC,UAAU,EAAErB,SAAS;EACrBsB,WAAW,EAAE,OAAO;EACpBC,UAAU,EAAEpB;CACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}