{"ast": null, "code": "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { GreaterEqual } from '@tensorflow/tfjs-core';\nimport { createSimpleBinaryKernelImpl } from '../utils/binary_impl';\nimport { binaryKernelFunc } from '../utils/binary_utils';\nexport const greaterEqualImpl = createSimpleBinaryKernelImpl((a, b) => a >= b ? 1 : 0);\nexport const greaterEqual = binaryKernelFunc(GreaterEqual, greaterEqualImpl, null /* complexImpl */, 'bool');\nexport const greaterEqualConfig = {\n  kernelName: GreaterEqual,\n  backendName: 'cpu',\n  kernelFunc: greaterEqual\n};", "map": {"version": 3, "names": ["GreaterEqual", "createSimpleBinaryKernelImpl", "binaryKernelFunc", "greaterEqualImpl", "a", "b", "greaterEqual", "greaterEqualConfig", "kernelName", "backendName", "kernelFunc"], "sources": ["C:\\tfjs-backend-cpu\\src\\kernels\\GreaterEqual.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {GreaterEqual, KernelConfig} from '@tensorflow/tfjs-core';\n\nimport {createSimpleBinaryKernelImpl} from '../utils/binary_impl';\nimport {binaryKernelFunc} from '../utils/binary_utils';\n\nexport const greaterEqualImpl =\n    createSimpleBinaryKernelImpl((a: number, b: number) => (a >= b) ? 1 : 0);\nexport const greaterEqual = binaryKernelFunc(\n    GreaterEqual, greaterEqualImpl, null /* complexImpl */, 'bool');\n\nexport const greaterEqualConfig: KernelConfig = {\n  kernelName: GreaterEqual,\n  backendName: 'cpu',\n  kernelFunc: greaterEqual\n};\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAiBA,SAAQA,YAAY,QAAqB,uBAAuB;AAEhE,SAAQC,4BAA4B,QAAO,sBAAsB;AACjE,SAAQC,gBAAgB,QAAO,uBAAuB;AAEtD,OAAO,MAAMC,gBAAgB,GACzBF,4BAA4B,CAAC,CAACG,CAAS,EAAEC,CAAS,KAAMD,CAAC,IAAIC,CAAC,GAAI,CAAC,GAAG,CAAC,CAAC;AAC5E,OAAO,MAAMC,YAAY,GAAGJ,gBAAgB,CACxCF,YAAY,EAAEG,gBAAgB,EAAE,IAAI,CAAC,mBAAmB,MAAM,CAAC;AAEnE,OAAO,MAAMI,kBAAkB,GAAiB;EAC9CC,UAAU,EAAER,YAAY;EACxBS,WAAW,EAAE,KAAK;EAClBC,UAAU,EAAEJ;CACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}