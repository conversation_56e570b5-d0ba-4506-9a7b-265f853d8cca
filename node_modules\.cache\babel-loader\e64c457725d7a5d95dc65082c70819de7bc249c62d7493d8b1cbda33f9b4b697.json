{"ast": null, "code": "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { ENGINE } from '../engine';\nimport { Dilation2D } from '../kernel_names';\nimport { convertToTensor } from '../tensor_util_env';\nimport * as util from '../util';\nimport { op } from './operation';\nimport { reshape } from './reshape';\n/**\n * Computes the grayscale dilation over the input `x`.\n *\n * @param x The input tensor, rank 3 or rank 4 of shape\n *     `[batch, height, width, depth]`. If rank 3, batch of 1 is assumed.\n * @param filter The filter tensor, rank 3, of shape\n *     `[filterHeight, filterWidth, depth]`.\n * @param strides The strides of the sliding window for each dimension of the\n *     input tensor: `[strideHeight, strideWidth]`.\n *     If `strides` is a single number,\n *     then `strideHeight == strideWidth`.\n * @param pad The type of padding algorithm.\n *    - `same` and stride 1: output will be of same size as input,\n *       regardless of filter size.\n *    - `valid`: output will be smaller than input if filter is larger\n *       than 1*1x1.\n *    - For more info, see this guide:\n *     [https://www.tensorflow.org/api_docs/python/tf/nn/convolution](\n *          https://www.tensorflow.org/api_docs/python/tf/nn/convolution)\n * @param dataFormat Specify the data format of the input and output data.\n *      Defaults to 'NHWC'. Only 'NHWC' is currently supported. With the\n *      default format \"NHWC\", the data is stored in the order of: [batch,\n *      height, width, channels].\n * @param dilations The dilation rates: `[dilationHeight, dilationWidth]`\n *     in which we sample input values across the height and width dimensions\n *     for atrous morphological dilation. Defaults to `[1, 1]`. If `dilations`\n *     is a single number, then `dilationHeight == dilationWidth`. If it is\n *     greater than 1, then all values of `strides` must be 1.\n *\n * @doc {heading: 'Operations', subheading: 'Convolution'}\n */\nfunction dilation2d_(x, filter, strides, pad) {\n  let dilations = arguments.length > 4 && arguments[4] !== undefined ? arguments[4] : [1, 1];\n  let dataFormat = arguments.length > 5 && arguments[5] !== undefined ? arguments[5] : 'NHWC';\n  const $x = convertToTensor(x, 'x', 'dilation2d');\n  const $filter = convertToTensor(filter, 'filter', 'dilation2d');\n  util.assert($x.rank === 3 || $x.rank === 4, () => \"Error in dilation2d: input must be rank 3 or 4, but got rank \" + \"\".concat($x.rank, \".\"));\n  util.assert($filter.rank === 3, () => \"Error in dilation2d: filter must be rank 3, but got rank \" + \"\".concat($filter.rank, \".\"));\n  util.assert(dataFormat === 'NHWC', () => \"Error in dilation2d: Only NHWC is currently supported, \" + \"but got dataFormat of \".concat(dataFormat));\n  let x4D = $x;\n  let reshapedTo4D = false;\n  if ($x.rank === 3) {\n    x4D = reshape($x, [1, $x.shape[0], $x.shape[1], $x.shape[2]]);\n    reshapedTo4D = true;\n  }\n  util.assert(x4D.shape[3] === $filter.shape[2], () => \"Error in dilation2d:  input and filter must have the same depth: \".concat(x4D.shape[3], \" vs \").concat($filter.shape[2]));\n  const inputs = {\n    x: x4D,\n    filter: $filter\n  };\n  const attrs = {\n    strides,\n    pad,\n    dilations\n  };\n  // tslint:disable-next-line: no-unnecessary-type-assertion\n  const res = ENGINE.runKernel(Dilation2D, inputs, attrs);\n  if (reshapedTo4D) {\n    return reshape(res, [res.shape[1], res.shape[2], res.shape[3]]);\n  }\n  return res;\n}\nexport const dilation2d = /* @__PURE__ */op({\n  dilation2d_\n});", "map": {"version": 3, "names": ["ENGINE", "Dilation2D", "convertToTensor", "util", "op", "reshape", "dilation2d_", "x", "filter", "strides", "pad", "dilations", "arguments", "length", "undefined", "dataFormat", "$x", "$filter", "assert", "rank", "concat", "x4D", "reshapedTo4D", "shape", "inputs", "attrs", "res", "runKernel", "dilation2d"], "sources": ["C:\\tfjs-core\\src\\ops\\dilation2d.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {ENGINE} from '../engine';\nimport {Dilation2D, Dilation2DAttrs, Dilation2DInputs} from '../kernel_names';\nimport {NamedAttrMap} from '../kernel_registry';\nimport {Tensor3D, Tensor4D} from '../tensor';\nimport {NamedTensorMap} from '../tensor_types';\nimport {convertToTensor} from '../tensor_util_env';\nimport {TensorLike} from '../types';\nimport * as util from '../util';\n\nimport {op} from './operation';\nimport {reshape} from './reshape';\n\n/**\n * Computes the grayscale dilation over the input `x`.\n *\n * @param x The input tensor, rank 3 or rank 4 of shape\n *     `[batch, height, width, depth]`. If rank 3, batch of 1 is assumed.\n * @param filter The filter tensor, rank 3, of shape\n *     `[filterHeight, filterWidth, depth]`.\n * @param strides The strides of the sliding window for each dimension of the\n *     input tensor: `[strideHeight, strideWidth]`.\n *     If `strides` is a single number,\n *     then `strideHeight == strideWidth`.\n * @param pad The type of padding algorithm.\n *    - `same` and stride 1: output will be of same size as input,\n *       regardless of filter size.\n *    - `valid`: output will be smaller than input if filter is larger\n *       than 1*1x1.\n *    - For more info, see this guide:\n *     [https://www.tensorflow.org/api_docs/python/tf/nn/convolution](\n *          https://www.tensorflow.org/api_docs/python/tf/nn/convolution)\n * @param dataFormat Specify the data format of the input and output data.\n *      Defaults to 'NHWC'. Only 'NHWC' is currently supported. With the\n *      default format \"NHWC\", the data is stored in the order of: [batch,\n *      height, width, channels].\n * @param dilations The dilation rates: `[dilationHeight, dilationWidth]`\n *     in which we sample input values across the height and width dimensions\n *     for atrous morphological dilation. Defaults to `[1, 1]`. If `dilations`\n *     is a single number, then `dilationHeight == dilationWidth`. If it is\n *     greater than 1, then all values of `strides` must be 1.\n *\n * @doc {heading: 'Operations', subheading: 'Convolution'}\n */\nfunction dilation2d_<T extends Tensor3D|Tensor4D>(\n    x: T|TensorLike, filter: Tensor3D|TensorLike,\n    strides: [number, number]|number, pad: 'valid'|'same',\n    dilations: [number, number]|number = [1, 1],\n    dataFormat: 'NHWC' = 'NHWC'): T {\n  const $x = convertToTensor(x, 'x', 'dilation2d');\n  const $filter = convertToTensor(filter, 'filter', 'dilation2d');\n\n  util.assert(\n      $x.rank === 3 || $x.rank === 4,\n      () => `Error in dilation2d: input must be rank 3 or 4, but got rank ` +\n          `${$x.rank}.`);\n  util.assert(\n      $filter.rank === 3,\n      () => `Error in dilation2d: filter must be rank 3, but got rank ` +\n          `${$filter.rank}.`);\n  util.assert(\n      dataFormat === 'NHWC',\n      () => `Error in dilation2d: Only NHWC is currently supported, ` +\n          `but got dataFormat of ${dataFormat}`);\n\n  let x4D = $x as Tensor4D;\n  let reshapedTo4D = false;\n\n  if ($x.rank === 3) {\n    x4D = reshape($x, [1, $x.shape[0], $x.shape[1], $x.shape[2]]);\n    reshapedTo4D = true;\n  }\n\n  util.assert(\n      x4D.shape[3] === $filter.shape[2],\n      () => `Error in dilation2d:  input and filter must have the same depth: ${\n          x4D.shape[3]} vs ${$filter.shape[2]}`);\n\n  const inputs: Dilation2DInputs = {x: x4D, filter: $filter};\n  const attrs: Dilation2DAttrs = {strides, pad, dilations};\n\n  // tslint:disable-next-line: no-unnecessary-type-assertion\n  const res = ENGINE.runKernel(\n                  Dilation2D, inputs as unknown as NamedTensorMap,\n                  attrs as unknown as NamedAttrMap) as T;\n\n  if (reshapedTo4D) {\n    return reshape(res, [res.shape[1], res.shape[2], res.shape[3]]) as T;\n  }\n\n  return res;\n}\n\nexport const dilation2d = /* @__PURE__ */ op({dilation2d_});\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAiBA,SAAQA,MAAM,QAAO,WAAW;AAChC,SAAQC,UAAU,QAA0C,iBAAiB;AAI7E,SAAQC,eAAe,QAAO,oBAAoB;AAElD,OAAO,KAAKC,IAAI,MAAM,SAAS;AAE/B,SAAQC,EAAE,QAAO,aAAa;AAC9B,SAAQC,OAAO,QAAO,WAAW;AAEjC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA+BA,SAASC,WAAWA,CAChBC,CAAe,EAAEC,MAA2B,EAC5CC,OAAgC,EAAEC,GAAmB,EAE1B;EAAA,IAD3BC,SAAA,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAqC,CAAC,CAAC,EAAE,CAAC,CAAC;EAAA,IAC3CG,UAAA,GAAAH,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAqB,MAAM;EAC7B,MAAMI,EAAE,GAAGd,eAAe,CAACK,CAAC,EAAE,GAAG,EAAE,YAAY,CAAC;EAChD,MAAMU,OAAO,GAAGf,eAAe,CAACM,MAAM,EAAE,QAAQ,EAAE,YAAY,CAAC;EAE/DL,IAAI,CAACe,MAAM,CACPF,EAAE,CAACG,IAAI,KAAK,CAAC,IAAIH,EAAE,CAACG,IAAI,KAAK,CAAC,EAC9B,MAAM,qEAAAC,MAAA,CACCJ,EAAE,CAACG,IAAI,MAAG,CAAC;EACtBhB,IAAI,CAACe,MAAM,CACPD,OAAO,CAACE,IAAI,KAAK,CAAC,EAClB,MAAM,iEAAAC,MAAA,CACCH,OAAO,CAACE,IAAI,MAAG,CAAC;EAC3BhB,IAAI,CAACe,MAAM,CACPH,UAAU,KAAK,MAAM,EACrB,MAAM,qFAAAK,MAAA,CACuBL,UAAU,CAAE,CAAC;EAE9C,IAAIM,GAAG,GAAGL,EAAc;EACxB,IAAIM,YAAY,GAAG,KAAK;EAExB,IAAIN,EAAE,CAACG,IAAI,KAAK,CAAC,EAAE;IACjBE,GAAG,GAAGhB,OAAO,CAACW,EAAE,EAAE,CAAC,CAAC,EAAEA,EAAE,CAACO,KAAK,CAAC,CAAC,CAAC,EAAEP,EAAE,CAACO,KAAK,CAAC,CAAC,CAAC,EAAEP,EAAE,CAACO,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;IAC7DD,YAAY,GAAG,IAAI;;EAGrBnB,IAAI,CAACe,MAAM,CACPG,GAAG,CAACE,KAAK,CAAC,CAAC,CAAC,KAAKN,OAAO,CAACM,KAAK,CAAC,CAAC,CAAC,EACjC,0EAAAH,MAAA,CACIC,GAAG,CAACE,KAAK,CAAC,CAAC,CAAC,UAAAH,MAAA,CAAOH,OAAO,CAACM,KAAK,CAAC,CAAC,CAAC,CAAE,CAAC;EAE9C,MAAMC,MAAM,GAAqB;IAACjB,CAAC,EAAEc,GAAG;IAAEb,MAAM,EAAES;EAAO,CAAC;EAC1D,MAAMQ,KAAK,GAAoB;IAAChB,OAAO;IAAEC,GAAG;IAAEC;EAAS,CAAC;EAExD;EACA,MAAMe,GAAG,GAAG1B,MAAM,CAAC2B,SAAS,CACZ1B,UAAU,EAAEuB,MAAmC,EAC/CC,KAAgC,CAAM;EAEtD,IAAIH,YAAY,EAAE;IAChB,OAAOjB,OAAO,CAACqB,GAAG,EAAE,CAACA,GAAG,CAACH,KAAK,CAAC,CAAC,CAAC,EAAEG,GAAG,CAACH,KAAK,CAAC,CAAC,CAAC,EAAEG,GAAG,CAACH,KAAK,CAAC,CAAC,CAAC,CAAC,CAAM;;EAGtE,OAAOG,GAAG;AACZ;AAEA,OAAO,MAAME,UAAU,GAAG,eAAgBxB,EAAE,CAAC;EAACE;AAAW,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}