{"ast": null, "code": "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { nonMaxSuppressionV4Impl } from '../../backends/non_max_suppression_impl';\nimport { convertToTensor } from '../../tensor_util_env';\nimport { nonMaxSuppSanityCheck } from '../nonmax_util';\nimport { scalar } from '../scalar';\nimport { tensor1d } from '../tensor1d';\n/**\n * Asynchronously performs non maximum suppression of bounding boxes based on\n * iou (intersection over union), with an option to pad results.\n *\n * @param boxes a 2d tensor of shape `[numBoxes, 4]`. Each entry is\n *     `[y1, x1, y2, x2]`, where `(y1, x1)` and `(y2, x2)` are the corners of\n *     the bounding box.\n * @param scores a 1d tensor providing the box scores of shape `[numBoxes]`.\n * @param maxOutputSize The maximum number of boxes to be selected.\n * @param iouThreshold A float representing the threshold for deciding whether\n *     boxes overlap too much with respect to IOU. Must be between [0, 1].\n *     Defaults to 0.5 (50% box overlap).\n * @param scoreThreshold A threshold for deciding when to remove boxes based\n *     on score. Defaults to -inf, which means any score is accepted.\n * @param padToMaxOutputSize Defaults to false. If true, size of output\n *     `selectedIndices` is padded to maxOutputSize.\n * @return A map with the following properties:\n *     - selectedIndices: A 1D tensor with the selected box indices.\n *     - validOutputs: A scalar denoting how many elements in `selectedIndices`\n *       are valid. Valid elements occur first, then padding.\n *\n * @doc {heading: 'Operations', subheading: 'Images', namespace: 'image'}\n */\nasync function nonMaxSuppressionPaddedAsync_(boxes, scores, maxOutputSize) {\n  let iouThreshold = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : 0.5;\n  let scoreThreshold = arguments.length > 4 && arguments[4] !== undefined ? arguments[4] : Number.NEGATIVE_INFINITY;\n  let padToMaxOutputSize = arguments.length > 5 && arguments[5] !== undefined ? arguments[5] : false;\n  const $boxes = convertToTensor(boxes, 'boxes', 'nonMaxSuppressionAsync');\n  const $scores = convertToTensor(scores, 'scores', 'nonMaxSuppressionAsync');\n  const params = nonMaxSuppSanityCheck($boxes, $scores, maxOutputSize, iouThreshold, scoreThreshold, null /* softNmsSigma */);\n  const $maxOutputSize = params.maxOutputSize;\n  const $iouThreshold = params.iouThreshold;\n  const $scoreThreshold = params.scoreThreshold;\n  const [boxesVals, scoresVals] = await Promise.all([$boxes.data(), $scores.data()]);\n  // We call a cpu based impl directly with the typedarray data here rather\n  // than a kernel because all kernels are synchronous (and thus cannot await\n  // .data()).\n  const {\n    selectedIndices,\n    validOutputs\n  } = nonMaxSuppressionV4Impl(boxesVals, scoresVals, $maxOutputSize, $iouThreshold, $scoreThreshold, padToMaxOutputSize);\n  if ($boxes !== boxes) {\n    $boxes.dispose();\n  }\n  if ($scores !== scores) {\n    $scores.dispose();\n  }\n  return {\n    selectedIndices: tensor1d(selectedIndices, 'int32'),\n    validOutputs: scalar(validOutputs, 'int32')\n  };\n}\nexport const nonMaxSuppressionPaddedAsync = nonMaxSuppressionPaddedAsync_;", "map": {"version": 3, "names": ["nonMaxSuppressionV4Impl", "convertToTensor", "nonMaxSuppSanityCheck", "scalar", "tensor1d", "nonMaxSuppressionPaddedAsync_", "boxes", "scores", "maxOutputSize", "iouThreshold", "arguments", "length", "undefined", "scoreThreshold", "Number", "NEGATIVE_INFINITY", "padToMaxOutputSize", "$boxes", "$scores", "params", "$maxOutputSize", "$iouThreshold", "$scoreThreshold", "boxesVals", "scoresVals", "Promise", "all", "data", "selectedIndices", "validOutputs", "dispose", "nonMaxSuppressionPaddedAsync"], "sources": ["C:\\tfjs-core\\src\\ops\\image\\non_max_suppression_padded_async.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport {nonMaxSuppressionV4Impl} from '../../backends/non_max_suppression_impl';\nimport {Tensor1D, Tensor2D} from '../../tensor';\nimport {NamedTensorMap} from '../../tensor_types';\nimport {convertToTensor} from '../../tensor_util_env';\nimport {TensorLike} from '../../types';\nimport {nonMaxSuppSanityCheck} from '../nonmax_util';\nimport {scalar} from '../scalar';\nimport {tensor1d} from '../tensor1d';\n\n/**\n * Asynchronously performs non maximum suppression of bounding boxes based on\n * iou (intersection over union), with an option to pad results.\n *\n * @param boxes a 2d tensor of shape `[numBoxes, 4]`. Each entry is\n *     `[y1, x1, y2, x2]`, where `(y1, x1)` and `(y2, x2)` are the corners of\n *     the bounding box.\n * @param scores a 1d tensor providing the box scores of shape `[numBoxes]`.\n * @param maxOutputSize The maximum number of boxes to be selected.\n * @param iouThreshold A float representing the threshold for deciding whether\n *     boxes overlap too much with respect to IOU. Must be between [0, 1].\n *     Defaults to 0.5 (50% box overlap).\n * @param scoreThreshold A threshold for deciding when to remove boxes based\n *     on score. Defaults to -inf, which means any score is accepted.\n * @param padToMaxOutputSize Defaults to false. If true, size of output\n *     `selectedIndices` is padded to maxOutputSize.\n * @return A map with the following properties:\n *     - selectedIndices: A 1D tensor with the selected box indices.\n *     - validOutputs: A scalar denoting how many elements in `selectedIndices`\n *       are valid. Valid elements occur first, then padding.\n *\n * @doc {heading: 'Operations', subheading: 'Images', namespace: 'image'}\n */\nasync function nonMaxSuppressionPaddedAsync_(\n    boxes: Tensor2D|TensorLike, scores: Tensor1D|TensorLike,\n    maxOutputSize: number, iouThreshold = 0.5,\n    scoreThreshold = Number.NEGATIVE_INFINITY,\n    padToMaxOutputSize = false): Promise<NamedTensorMap> {\n  const $boxes = convertToTensor(boxes, 'boxes', 'nonMaxSuppressionAsync');\n  const $scores = convertToTensor(scores, 'scores', 'nonMaxSuppressionAsync');\n\n  const params = nonMaxSuppSanityCheck(\n      $boxes, $scores, maxOutputSize, iouThreshold, scoreThreshold,\n      null /* softNmsSigma */);\n  const $maxOutputSize = params.maxOutputSize;\n  const $iouThreshold = params.iouThreshold;\n  const $scoreThreshold = params.scoreThreshold;\n\n  const [boxesVals, scoresVals] =\n      await Promise.all([$boxes.data(), $scores.data()]);\n\n  // We call a cpu based impl directly with the typedarray data here rather\n  // than a kernel because all kernels are synchronous (and thus cannot await\n  // .data()).\n  const {selectedIndices, validOutputs} = nonMaxSuppressionV4Impl(\n      boxesVals, scoresVals, $maxOutputSize, $iouThreshold, $scoreThreshold,\n      padToMaxOutputSize);\n\n  if ($boxes !== boxes) {\n    $boxes.dispose();\n  }\n  if ($scores !== scores) {\n    $scores.dispose();\n  }\n\n  return {\n    selectedIndices: tensor1d(selectedIndices, 'int32'),\n    validOutputs: scalar(validOutputs, 'int32')\n  };\n}\n\nexport const nonMaxSuppressionPaddedAsync = nonMaxSuppressionPaddedAsync_;\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAgBA,SAAQA,uBAAuB,QAAO,yCAAyC;AAG/E,SAAQC,eAAe,QAAO,uBAAuB;AAErD,SAAQC,qBAAqB,QAAO,gBAAgB;AACpD,SAAQC,MAAM,QAAO,WAAW;AAChC,SAAQC,QAAQ,QAAO,aAAa;AAEpC;;;;;;;;;;;;;;;;;;;;;;;AAuBA,eAAeC,6BAA6BA,CACxCC,KAA0B,EAAEC,MAA2B,EACvDC,aAAqB,EAEK;EAAA,IAFHC,YAAY,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,GAAG;EAAA,IACzCG,cAAc,GAAAH,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAGI,MAAM,CAACC,iBAAiB;EAAA,IACzCC,kBAAkB,GAAAN,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,KAAK;EAC5B,MAAMO,MAAM,GAAGhB,eAAe,CAACK,KAAK,EAAE,OAAO,EAAE,wBAAwB,CAAC;EACxE,MAAMY,OAAO,GAAGjB,eAAe,CAACM,MAAM,EAAE,QAAQ,EAAE,wBAAwB,CAAC;EAE3E,MAAMY,MAAM,GAAGjB,qBAAqB,CAChCe,MAAM,EAAEC,OAAO,EAAEV,aAAa,EAAEC,YAAY,EAAEI,cAAc,EAC5D,IAAI,CAAC,kBAAkB,CAAC;EAC5B,MAAMO,cAAc,GAAGD,MAAM,CAACX,aAAa;EAC3C,MAAMa,aAAa,GAAGF,MAAM,CAACV,YAAY;EACzC,MAAMa,eAAe,GAAGH,MAAM,CAACN,cAAc;EAE7C,MAAM,CAACU,SAAS,EAAEC,UAAU,CAAC,GACzB,MAAMC,OAAO,CAACC,GAAG,CAAC,CAACT,MAAM,CAACU,IAAI,EAAE,EAAET,OAAO,CAACS,IAAI,EAAE,CAAC,CAAC;EAEtD;EACA;EACA;EACA,MAAM;IAACC,eAAe;IAAEC;EAAY,CAAC,GAAG7B,uBAAuB,CAC3DuB,SAAS,EAAEC,UAAU,EAAEJ,cAAc,EAAEC,aAAa,EAAEC,eAAe,EACrEN,kBAAkB,CAAC;EAEvB,IAAIC,MAAM,KAAKX,KAAK,EAAE;IACpBW,MAAM,CAACa,OAAO,EAAE;;EAElB,IAAIZ,OAAO,KAAKX,MAAM,EAAE;IACtBW,OAAO,CAACY,OAAO,EAAE;;EAGnB,OAAO;IACLF,eAAe,EAAExB,QAAQ,CAACwB,eAAe,EAAE,OAAO,CAAC;IACnDC,YAAY,EAAE1B,MAAM,CAAC0B,YAAY,EAAE,OAAO;GAC3C;AACH;AAEA,OAAO,MAAME,4BAA4B,GAAG1B,6BAA6B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}