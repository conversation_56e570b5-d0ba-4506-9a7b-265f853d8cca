{"ast": null, "code": "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { mirrorPad } from '../../ops/mirror_pad';\nimport { getGlobalTensorClass } from '../../tensor';\ngetGlobalTensorClass().prototype.mirrorPad = function (paddings, mode) {\n  this.throwIfDisposed();\n  return mirrorPad(this, paddings, mode);\n};", "map": {"version": 3, "names": ["mirrorPad", "getGlobalTensorClass", "prototype", "paddings", "mode", "throwIfDisposed"], "sources": ["C:\\tfjs-core\\src\\public\\chained_ops\\mirror_pad.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {mirrorPad} from '../../ops/mirror_pad';\nimport {getGlobalTensorClass, Tensor} from '../../tensor';\nimport {Rank} from '../../types';\n\ndeclare module '../../tensor' {\n  interface Tensor<R extends Rank = Rank> {\n    mirrorPad<T extends Tensor>(\n        paddings: Array<[number, number]>, mode: 'reflect'|'symmetric'): T;\n  }\n}\n\ngetGlobalTensorClass().prototype.mirrorPad = function<T extends Tensor>(\n    this: T, paddings: Array<[number, number]>,\n    mode: 'reflect'|'symmetric'): T {\n  this.throwIfDisposed();\n  return mirrorPad(this, paddings, mode);\n};\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAiBA,SAAQA,SAAS,QAAO,sBAAsB;AAC9C,SAAQC,oBAAoB,QAAe,cAAc;AAUzDA,oBAAoB,EAAE,CAACC,SAAS,CAACF,SAAS,GAAG,UAChCG,QAAiC,EAC1CC,IAA2B;EAC7B,IAAI,CAACC,eAAe,EAAE;EACtB,OAAOL,SAAS,CAAC,IAAI,EAAEG,QAAQ,EAAEC,IAAI,CAAC;AACxC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}