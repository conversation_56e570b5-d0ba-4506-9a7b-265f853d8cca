{"ast": null, "code": "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { Mod } from '@tensorflow/tfjs-core';\nimport { CHECK_NAN_SNIPPET_PACKED } from '../binaryop_packed_gpu';\nimport { binaryKernelFunc } from '../kernel_utils/kernel_funcs_utils';\nconst MOD = `if (b == 0.0) return NAN;\n  return mod(a, b);`;\nconst MOD_PACKED = `\n  vec4 result = mod(a, b);\n  bvec4 isNaN = equal(b, vec4(0.0));\n  ` + CHECK_NAN_SNIPPET_PACKED + `\n  return result;\n`;\nexport const mod = binaryKernelFunc({\n  opSnippet: MOD,\n  packedOpSnippet: MOD_PACKED\n});\nexport const modConfig = {\n  kernelName: Mod,\n  backendName: 'webgl',\n  kernelFunc: mod\n};", "map": {"version": 3, "names": ["Mod", "CHECK_NAN_SNIPPET_PACKED", "binaryKernelFunc", "MOD", "MOD_PACKED", "mod", "opSnippet", "packedOpSnippet", "modConfig", "kernelName", "backendName", "kernelFunc"], "sources": ["C:\\tfjs-backend-webgl\\src\\kernels\\Mod.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {KernelConfig, KernelFunc, Mod} from '@tensorflow/tfjs-core';\n\nimport {CHECK_NAN_SNIPPET_PACKED} from '../binaryop_packed_gpu';\nimport {binaryKernelFunc} from '../kernel_utils/kernel_funcs_utils';\n\nconst MOD = `if (b == 0.0) return NAN;\n  return mod(a, b);`;\n\nconst MOD_PACKED = `\n  vec4 result = mod(a, b);\n  bvec4 isNaN = equal(b, vec4(0.0));\n  ` +\n    CHECK_NAN_SNIPPET_PACKED + `\n  return result;\n`;\n\nexport const mod = binaryKernelFunc({\n  opSnippet: MOD,\n  packedOpSnippet: MOD_PACKED,\n});\n\nexport const modConfig: KernelConfig = {\n  kernelName: Mod,\n  backendName: 'webgl',\n  kernelFunc: mod as unknown as KernelFunc\n};\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAiBA,SAAkCA,GAAG,QAAO,uBAAuB;AAEnE,SAAQC,wBAAwB,QAAO,wBAAwB;AAC/D,SAAQC,gBAAgB,QAAO,oCAAoC;AAEnE,MAAMC,GAAG,GAAG;oBACQ;AAEpB,MAAMC,UAAU,GAAG;;;GAGhB,GACCH,wBAAwB,GAAG;;CAE9B;AAED,OAAO,MAAMI,GAAG,GAAGH,gBAAgB,CAAC;EAClCI,SAAS,EAAEH,GAAG;EACdI,eAAe,EAAEH;CAClB,CAAC;AAEF,OAAO,MAAMI,SAAS,GAAiB;EACrCC,UAAU,EAAET,GAAG;EACfU,WAAW,EAAE,OAAO;EACpBC,UAAU,EAAEN;CACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}