{"ast": null, "code": "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { BatchMatMul } from '@tensorflow/tfjs-core';\nimport { batchMatMulImpl } from './BatchMatMul_impl';\nexport function batchMatMul(args) {\n  const {\n    inputs,\n    backend,\n    attrs\n  } = args;\n  const {\n    a,\n    b\n  } = inputs;\n  const {\n    transposeA,\n    transposeB\n  } = attrs;\n  return batchMatMulImpl({\n    a,\n    b,\n    transposeA,\n    transposeB,\n    backend\n  });\n}\nexport const batchMatMulConfig = {\n  kernelName: BatchMatMul,\n  backendName: 'webgl',\n  kernelFunc: batchMatMul\n};", "map": {"version": 3, "names": ["BatchMatMul", "batchMatMulImpl", "batchMatMul", "args", "inputs", "backend", "attrs", "a", "b", "transposeA", "transposeB", "batchMatMulConfig", "kernelName", "backendName", "kernelFunc"], "sources": ["C:\\tfjs-backend-webgl\\src\\kernels\\BatchMatMul.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {BatchMatMul, BatchMatMulAttrs, BatchMatMulInputs, KernelConfig, KernelFunc} from '@tensorflow/tfjs-core';\n\nimport {MathBackendWebGL} from '../backend_webgl';\nimport {batchMatMulImpl} from './BatchMatMul_impl';\n\nexport function batchMatMul(args: {\n  inputs: BatchMatMulInputs,\n  attrs: BatchMatMulAttrs,\n  backend: MathBackendWebGL\n}) {\n  const {inputs, backend, attrs} = args;\n  const {a, b} = inputs;\n  const {transposeA, transposeB} = attrs;\n\n  return batchMatMulImpl({a, b, transposeA, transposeB, backend});\n}\n\nexport const batchMatMulConfig: KernelConfig = {\n  kernelName: BatchMatMul,\n  backendName: 'webgl',\n  kernelFunc: batchMatMul as unknown as KernelFunc,\n};\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAiBA,SAAQA,WAAW,QAAsE,uBAAuB;AAGhH,SAAQC,eAAe,QAAO,oBAAoB;AAElD,OAAM,SAAUC,WAAWA,CAACC,IAI3B;EACC,MAAM;IAACC,MAAM;IAAEC,OAAO;IAAEC;EAAK,CAAC,GAAGH,IAAI;EACrC,MAAM;IAACI,CAAC;IAAEC;EAAC,CAAC,GAAGJ,MAAM;EACrB,MAAM;IAACK,UAAU;IAAEC;EAAU,CAAC,GAAGJ,KAAK;EAEtC,OAAOL,eAAe,CAAC;IAACM,CAAC;IAAEC,CAAC;IAAEC,UAAU;IAAEC,UAAU;IAAEL;EAAO,CAAC,CAAC;AACjE;AAEA,OAAO,MAAMM,iBAAiB,GAAiB;EAC7CC,UAAU,EAAEZ,WAAW;EACvBa,WAAW,EAAE,OAAO;EACpBC,UAAU,EAAEZ;CACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}