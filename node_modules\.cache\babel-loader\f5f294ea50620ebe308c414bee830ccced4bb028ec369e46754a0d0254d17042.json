{"ast": null, "code": "/*\n  Copyright (c) 2019, <PERSON>, <PERSON><PERSON> (based on https://github.com/dmarman/lorca)\n\n  Permission is hereby granted, free of charge, to any person obtaining a copy\n  of this software and associated documentation files (the \"Software\"), to deal\n  in the Software without restriction, including without limitation the rights\n  to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n  copies of the Software, and to permit persons to whom the Software is\n  furnished to do so, subject to the following conditions:\n\n  The above copyright notice and this permission notice shall be included in\n  all copies or substantial portions of the Software.\n\n  THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n  IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n  FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n  AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n  LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n  OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\n  THE SOFTWARE.\n*/\n\n'use strict';\n\nconst DEBUG = false;\n\n// Afinn\nconst englishAfinnVoca = require('afinn-165');\nconst spanishAfinnVoca = require('./Spanish/afinnShortSortedSpanish.json');\nconst portugueseAfinnVoca = require('./Portuguese/afinnShortSortedPortuguese.json');\n\n// Afinn Financial Market News\n// const englishAfinnFinancialMarketNewsVoca = require('afinn-165-financialmarketnews')\n// Use ESM syntax\nconst englishAfinnFinancialMarketNewsVoca = require('afinn-165-financialmarketnews').afinnFinancialMarketNews;\n\n// Senticon\nconst spanishSenticonVoca = require('./Spanish/senticon_es.json');\nconst englishSenticonVoca = require('./English/senticon_en.json');\nconst galicianSenticonVoca = require('./Galician/senticon_gl.json');\nconst catalanSenticonVoca = require('./Catalan/senticon_ca.json');\nconst basqueSenticonVoca = require('./Basque/senticon_eu.json');\n\n// Pattern\nconst dutchPatternVoca = require('./Dutch/pattern-sentiment-nl.json');\nconst italianPatternVoca = require('./Italian/pattern-sentiment-it.json');\nconst englishPatternVoca = require('./English/pattern-sentiment-en.json');\nconst frenchPatternVoca = require('./French/pattern-sentiment-fr.json');\nconst germanPatternVoca = require('./German/pattern-sentiment-de.json');\n\n// Negations\nconst englishNegations = require('./English/negations_en.json').words;\nconst spanishNegations = require('./Spanish/negations_es.json').words;\nconst dutchNegations = require('./Dutch/negations_du.json').words;\nconst portugueseNegations = require('./Portuguese/negations_pt.json').words;\nconst germanNegations = require('./German/negations_de.json').words;\n\n// Mapping from type of vocabulary to language to vocabulary\nconst languageFiles = {\n  afinn: {\n    English: [englishAfinnVoca, englishNegations],\n    Spanish: [spanishAfinnVoca, spanishNegations],\n    Portuguese: [portugueseAfinnVoca, portugueseNegations]\n  },\n  afinnFinancialMarketNews: {\n    English: [englishAfinnFinancialMarketNewsVoca, englishNegations]\n  },\n  senticon: {\n    Spanish: [spanishSenticonVoca, spanishNegations],\n    English: [englishSenticonVoca, englishNegations],\n    Galician: [galicianSenticonVoca, null],\n    Catalan: [catalanSenticonVoca, null],\n    Basque: [basqueSenticonVoca, null]\n  },\n  pattern: {\n    Dutch: [dutchPatternVoca, dutchNegations],\n    Italian: [italianPatternVoca, null],\n    English: [englishPatternVoca, englishNegations],\n    French: [frenchPatternVoca, null],\n    German: [germanPatternVoca, germanNegations]\n  }\n};\nclass SentimentAnalyzer {\n  constructor(language, stemmer, type) {\n    this.language = language;\n    this.stemmer = stemmer;\n\n    // this.vocabulary must be a copy of the languageFiles object\n    // or in subsequent execution the polarity will be undefined\n    // shallow copy - requires ES6\n    if (languageFiles[type]) {\n      if (languageFiles[type][language]) {\n        if (languageFiles[type][language][0]) {\n          this.vocabulary = Object.create(languageFiles[type][language][0]);\n        }\n      } else {\n        throw new Error('Type ' + type + ' for Language ' + language + ' not supported');\n      }\n    } else {\n      throw new Error('Type Language ' + type + ' not supported');\n    }\n    this.vocabulary = Object.assign({}, languageFiles[type][language][0]);\n    Object.setPrototypeOf(this.vocabulary, null);\n    if (type === 'senticon') {\n      Object.keys(this.vocabulary).forEach(word => {\n        this.vocabulary[word] = this.vocabulary[word].pol;\n      });\n    } else {\n      if (type === 'pattern') {\n        Object.keys(this.vocabulary).forEach(word => {\n          this.vocabulary[word] = this.vocabulary[word].polarity;\n        });\n      }\n    }\n    this.negations = [];\n    if (languageFiles[type][language][1] != null) {\n      this.negations = languageFiles[type][language][1];\n    }\n    if (stemmer) {\n      const vocaStemmed = Object.create(null);\n      for (const token in this.vocabulary) {\n        vocaStemmed[stemmer.stem(token)] = this.vocabulary[token];\n      }\n      this.vocabulary = vocaStemmed;\n    }\n  }\n\n  // words is an array of words (strings)\n  getSentiment(words) {\n    let score = 0;\n    let negator = 1;\n    let nrHits = 0;\n    words.forEach(token => {\n      const lowerCased = token.toLowerCase();\n      if (this.negations.indexOf(lowerCased) > -1) {\n        negator = -1;\n        DEBUG && nrHits++;\n      } else {\n        // First try without stemming\n        if (this.vocabulary[lowerCased] !== undefined) {\n          score += negator * this.vocabulary[lowerCased];\n          DEBUG && nrHits++;\n        } else {\n          if (this.stemmer) {\n            const stemmedWord = this.stemmer.stem(lowerCased);\n            if (this.vocabulary[stemmedWord] !== undefined) {\n              score += negator * this.vocabulary[stemmedWord];\n              DEBUG && nrHits++;\n            }\n          }\n        }\n      }\n    });\n    score = score / words.length;\n    DEBUG && console.log('Number of hits: ' + nrHits);\n    return score;\n  }\n}\nmodule.exports = SentimentAnalyzer;", "map": {"version": 3, "names": ["DEBUG", "englishAfinnVoca", "require", "spanishAfinnVoca", "portugueseAfinnVoca", "englishAfinnFinancialMarketNewsVoca", "afinnFinancialMarketNews", "spanishSenticonVoca", "englishSenticonVoca", "galicianSenticonVoca", "catalanSenticonVoca", "basqueSenticonVoca", "dutchPatternVoca", "italianPatternVoca", "englishPatternVoca", "frenchPatternVoca", "germanPatternVoca", "englishNegations", "words", "spanishNegations", "dutchNegations", "portugueseNegations", "germanNegations", "languageFiles", "afinn", "English", "Spanish", "Portuguese", "senticon", "Galician", "Catalan", "Basque", "pattern", "Dutch", "Italian", "French", "German", "SentimentAnalyzer", "constructor", "language", "stemmer", "type", "vocabulary", "Object", "create", "Error", "assign", "setPrototypeOf", "keys", "for<PERSON>ach", "word", "pol", "polarity", "negations", "vocaStemmed", "token", "stem", "getSentiment", "score", "negator", "nrHits", "lowerCased", "toLowerCase", "indexOf", "undefined", "stemmed<PERSON><PERSON>", "length", "console", "log", "module", "exports"], "sources": ["C:/tmsft/node_modules/natural/lib/natural/sentiment/SentimentAnalyzer.js"], "sourcesContent": ["/*\n  Copyright (c) 2019, <PERSON>, <PERSON><PERSON> (based on https://github.com/dmarman/lorca)\n\n  Permission is hereby granted, free of charge, to any person obtaining a copy\n  of this software and associated documentation files (the \"Software\"), to deal\n  in the Software without restriction, including without limitation the rights\n  to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n  copies of the Software, and to permit persons to whom the Software is\n  furnished to do so, subject to the following conditions:\n\n  The above copyright notice and this permission notice shall be included in\n  all copies or substantial portions of the Software.\n\n  THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n  IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n  FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n  AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n  LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n  OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\n  THE SOFTWARE.\n*/\n\n'use strict'\n\nconst DEBUG = false\n\n// Afinn\nconst englishAfinnVoca = require('afinn-165')\nconst spanishAfinnVoca = require('./Spanish/afinnShortSortedSpanish.json')\nconst portugueseAfinnVoca = require('./Portuguese/afinnShortSortedPortuguese.json')\n\n// Afinn Financial Market News\n// const englishAfinnFinancialMarketNewsVoca = require('afinn-165-financialmarketnews')\n// Use ESM syntax\nconst englishAfinnFinancialMarketNewsVoca = require('afinn-165-financialmarketnews').afinnFinancialMarketNews\n\n// Senticon\nconst spanishSenticonVoca = require('./Spanish/senticon_es.json')\nconst englishSenticonVoca = require('./English/senticon_en.json')\nconst galicianSenticonVoca = require('./Galician/senticon_gl.json')\nconst catalanSenticonVoca = require('./Catalan/senticon_ca.json')\nconst basqueSenticonVoca = require('./Basque/senticon_eu.json')\n\n// Pattern\nconst dutchPatternVoca = require('./Dutch/pattern-sentiment-nl.json')\nconst italianPatternVoca = require('./Italian/pattern-sentiment-it.json')\nconst englishPatternVoca = require('./English/pattern-sentiment-en.json')\nconst frenchPatternVoca = require('./French/pattern-sentiment-fr.json')\nconst germanPatternVoca = require('./German/pattern-sentiment-de.json')\n\n// Negations\nconst englishNegations = require('./English/negations_en.json').words\nconst spanishNegations = require('./Spanish/negations_es.json').words\nconst dutchNegations = require('./Dutch/negations_du.json').words\nconst portugueseNegations = require('./Portuguese/negations_pt.json').words\nconst germanNegations = require('./German/negations_de.json').words\n\n// Mapping from type of vocabulary to language to vocabulary\nconst languageFiles = {\n  afinn: {\n    English: [englishAfinnVoca, englishNegations],\n    Spanish: [spanishAfinnVoca, spanishNegations],\n    Portuguese: [portugueseAfinnVoca, portugueseNegations]\n  },\n  afinnFinancialMarketNews: {\n    English: [englishAfinnFinancialMarketNewsVoca, englishNegations]\n  },\n  senticon: {\n    Spanish: [spanishSenticonVoca, spanishNegations],\n    English: [englishSenticonVoca, englishNegations],\n    Galician: [galicianSenticonVoca, null],\n    Catalan: [catalanSenticonVoca, null],\n    Basque: [basqueSenticonVoca, null]\n  },\n  pattern: {\n    Dutch: [dutchPatternVoca, dutchNegations],\n    Italian: [italianPatternVoca, null],\n    English: [englishPatternVoca, englishNegations],\n    French: [frenchPatternVoca, null],\n    German: [germanPatternVoca, germanNegations]\n  }\n}\n\nclass SentimentAnalyzer {\n  constructor (language, stemmer, type) {\n    this.language = language\n    this.stemmer = stemmer\n\n    // this.vocabulary must be a copy of the languageFiles object\n    // or in subsequent execution the polarity will be undefined\n    // shallow copy - requires ES6\n    if (languageFiles[type]) {\n      if (languageFiles[type][language]) {\n        if (languageFiles[type][language][0]) {\n          this.vocabulary = Object.create(languageFiles[type][language][0])\n        }\n      } else {\n        throw new Error('Type ' + type + ' for Language ' + language + ' not supported')\n      }\n    } else {\n      throw new Error('Type Language ' + type + ' not supported')\n    }\n    this.vocabulary = Object.assign({}, languageFiles[type][language][0])\n    Object.setPrototypeOf(this.vocabulary, null)\n    if (type === 'senticon') {\n      Object.keys(this.vocabulary).forEach(word => {\n        this.vocabulary[word] = this.vocabulary[word].pol\n      })\n    } else {\n      if (type === 'pattern') {\n        Object.keys(this.vocabulary).forEach(word => {\n          this.vocabulary[word] = this.vocabulary[word].polarity\n        })\n      }\n    }\n\n    this.negations = []\n    if (languageFiles[type][language][1] != null) {\n      this.negations = languageFiles[type][language][1]\n    }\n\n    if (stemmer) {\n      const vocaStemmed = Object.create(null)\n      for (const token in this.vocabulary) {\n        vocaStemmed[stemmer.stem(token)] = this.vocabulary[token]\n      }\n      this.vocabulary = vocaStemmed\n    }\n  }\n\n  // words is an array of words (strings)\n  getSentiment (words) {\n    let score = 0\n    let negator = 1\n    let nrHits = 0\n\n    words.forEach((token) => {\n      const lowerCased = token.toLowerCase()\n      if (this.negations.indexOf(lowerCased) > -1) {\n        negator = -1\n        DEBUG && nrHits++\n      } else {\n        // First try without stemming\n        if (this.vocabulary[lowerCased] !== undefined) {\n          score += negator * this.vocabulary[lowerCased]\n          DEBUG && nrHits++\n        } else {\n          if (this.stemmer) {\n            const stemmedWord = this.stemmer.stem(lowerCased)\n            if (this.vocabulary[stemmedWord] !== undefined) {\n              score += negator * this.vocabulary[stemmedWord]\n              DEBUG && nrHits++\n            }\n          }\n        }\n      }\n    })\n\n    score = score / words.length\n    DEBUG && console.log('Number of hits: ' + nrHits)\n\n    return score\n  }\n}\n\nmodule.exports = SentimentAnalyzer\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,YAAY;;AAEZ,MAAMA,KAAK,GAAG,KAAK;;AAEnB;AACA,MAAMC,gBAAgB,GAAGC,OAAO,CAAC,WAAW,CAAC;AAC7C,MAAMC,gBAAgB,GAAGD,OAAO,CAAC,wCAAwC,CAAC;AAC1E,MAAME,mBAAmB,GAAGF,OAAO,CAAC,8CAA8C,CAAC;;AAEnF;AACA;AACA;AACA,MAAMG,mCAAmC,GAAGH,OAAO,CAAC,+BAA+B,CAAC,CAACI,wBAAwB;;AAE7G;AACA,MAAMC,mBAAmB,GAAGL,OAAO,CAAC,4BAA4B,CAAC;AACjE,MAAMM,mBAAmB,GAAGN,OAAO,CAAC,4BAA4B,CAAC;AACjE,MAAMO,oBAAoB,GAAGP,OAAO,CAAC,6BAA6B,CAAC;AACnE,MAAMQ,mBAAmB,GAAGR,OAAO,CAAC,4BAA4B,CAAC;AACjE,MAAMS,kBAAkB,GAAGT,OAAO,CAAC,2BAA2B,CAAC;;AAE/D;AACA,MAAMU,gBAAgB,GAAGV,OAAO,CAAC,mCAAmC,CAAC;AACrE,MAAMW,kBAAkB,GAAGX,OAAO,CAAC,qCAAqC,CAAC;AACzE,MAAMY,kBAAkB,GAAGZ,OAAO,CAAC,qCAAqC,CAAC;AACzE,MAAMa,iBAAiB,GAAGb,OAAO,CAAC,oCAAoC,CAAC;AACvE,MAAMc,iBAAiB,GAAGd,OAAO,CAAC,oCAAoC,CAAC;;AAEvE;AACA,MAAMe,gBAAgB,GAAGf,OAAO,CAAC,6BAA6B,CAAC,CAACgB,KAAK;AACrE,MAAMC,gBAAgB,GAAGjB,OAAO,CAAC,6BAA6B,CAAC,CAACgB,KAAK;AACrE,MAAME,cAAc,GAAGlB,OAAO,CAAC,2BAA2B,CAAC,CAACgB,KAAK;AACjE,MAAMG,mBAAmB,GAAGnB,OAAO,CAAC,gCAAgC,CAAC,CAACgB,KAAK;AAC3E,MAAMI,eAAe,GAAGpB,OAAO,CAAC,4BAA4B,CAAC,CAACgB,KAAK;;AAEnE;AACA,MAAMK,aAAa,GAAG;EACpBC,KAAK,EAAE;IACLC,OAAO,EAAE,CAACxB,gBAAgB,EAAEgB,gBAAgB,CAAC;IAC7CS,OAAO,EAAE,CAACvB,gBAAgB,EAAEgB,gBAAgB,CAAC;IAC7CQ,UAAU,EAAE,CAACvB,mBAAmB,EAAEiB,mBAAmB;EACvD,CAAC;EACDf,wBAAwB,EAAE;IACxBmB,OAAO,EAAE,CAACpB,mCAAmC,EAAEY,gBAAgB;EACjE,CAAC;EACDW,QAAQ,EAAE;IACRF,OAAO,EAAE,CAACnB,mBAAmB,EAAEY,gBAAgB,CAAC;IAChDM,OAAO,EAAE,CAACjB,mBAAmB,EAAES,gBAAgB,CAAC;IAChDY,QAAQ,EAAE,CAACpB,oBAAoB,EAAE,IAAI,CAAC;IACtCqB,OAAO,EAAE,CAACpB,mBAAmB,EAAE,IAAI,CAAC;IACpCqB,MAAM,EAAE,CAACpB,kBAAkB,EAAE,IAAI;EACnC,CAAC;EACDqB,OAAO,EAAE;IACPC,KAAK,EAAE,CAACrB,gBAAgB,EAAEQ,cAAc,CAAC;IACzCc,OAAO,EAAE,CAACrB,kBAAkB,EAAE,IAAI,CAAC;IACnCY,OAAO,EAAE,CAACX,kBAAkB,EAAEG,gBAAgB,CAAC;IAC/CkB,MAAM,EAAE,CAACpB,iBAAiB,EAAE,IAAI,CAAC;IACjCqB,MAAM,EAAE,CAACpB,iBAAiB,EAAEM,eAAe;EAC7C;AACF,CAAC;AAED,MAAMe,iBAAiB,CAAC;EACtBC,WAAWA,CAAEC,QAAQ,EAAEC,OAAO,EAAEC,IAAI,EAAE;IACpC,IAAI,CAACF,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,OAAO,GAAGA,OAAO;;IAEtB;IACA;IACA;IACA,IAAIjB,aAAa,CAACkB,IAAI,CAAC,EAAE;MACvB,IAAIlB,aAAa,CAACkB,IAAI,CAAC,CAACF,QAAQ,CAAC,EAAE;QACjC,IAAIhB,aAAa,CAACkB,IAAI,CAAC,CAACF,QAAQ,CAAC,CAAC,CAAC,CAAC,EAAE;UACpC,IAAI,CAACG,UAAU,GAAGC,MAAM,CAACC,MAAM,CAACrB,aAAa,CAACkB,IAAI,CAAC,CAACF,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;QACnE;MACF,CAAC,MAAM;QACL,MAAM,IAAIM,KAAK,CAAC,OAAO,GAAGJ,IAAI,GAAG,gBAAgB,GAAGF,QAAQ,GAAG,gBAAgB,CAAC;MAClF;IACF,CAAC,MAAM;MACL,MAAM,IAAIM,KAAK,CAAC,gBAAgB,GAAGJ,IAAI,GAAG,gBAAgB,CAAC;IAC7D;IACA,IAAI,CAACC,UAAU,GAAGC,MAAM,CAACG,MAAM,CAAC,CAAC,CAAC,EAAEvB,aAAa,CAACkB,IAAI,CAAC,CAACF,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;IACrEI,MAAM,CAACI,cAAc,CAAC,IAAI,CAACL,UAAU,EAAE,IAAI,CAAC;IAC5C,IAAID,IAAI,KAAK,UAAU,EAAE;MACvBE,MAAM,CAACK,IAAI,CAAC,IAAI,CAACN,UAAU,CAAC,CAACO,OAAO,CAACC,IAAI,IAAI;QAC3C,IAAI,CAACR,UAAU,CAACQ,IAAI,CAAC,GAAG,IAAI,CAACR,UAAU,CAACQ,IAAI,CAAC,CAACC,GAAG;MACnD,CAAC,CAAC;IACJ,CAAC,MAAM;MACL,IAAIV,IAAI,KAAK,SAAS,EAAE;QACtBE,MAAM,CAACK,IAAI,CAAC,IAAI,CAACN,UAAU,CAAC,CAACO,OAAO,CAACC,IAAI,IAAI;UAC3C,IAAI,CAACR,UAAU,CAACQ,IAAI,CAAC,GAAG,IAAI,CAACR,UAAU,CAACQ,IAAI,CAAC,CAACE,QAAQ;QACxD,CAAC,CAAC;MACJ;IACF;IAEA,IAAI,CAACC,SAAS,GAAG,EAAE;IACnB,IAAI9B,aAAa,CAACkB,IAAI,CAAC,CAACF,QAAQ,CAAC,CAAC,CAAC,CAAC,IAAI,IAAI,EAAE;MAC5C,IAAI,CAACc,SAAS,GAAG9B,aAAa,CAACkB,IAAI,CAAC,CAACF,QAAQ,CAAC,CAAC,CAAC,CAAC;IACnD;IAEA,IAAIC,OAAO,EAAE;MACX,MAAMc,WAAW,GAAGX,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;MACvC,KAAK,MAAMW,KAAK,IAAI,IAAI,CAACb,UAAU,EAAE;QACnCY,WAAW,CAACd,OAAO,CAACgB,IAAI,CAACD,KAAK,CAAC,CAAC,GAAG,IAAI,CAACb,UAAU,CAACa,KAAK,CAAC;MAC3D;MACA,IAAI,CAACb,UAAU,GAAGY,WAAW;IAC/B;EACF;;EAEA;EACAG,YAAYA,CAAEvC,KAAK,EAAE;IACnB,IAAIwC,KAAK,GAAG,CAAC;IACb,IAAIC,OAAO,GAAG,CAAC;IACf,IAAIC,MAAM,GAAG,CAAC;IAEd1C,KAAK,CAAC+B,OAAO,CAAEM,KAAK,IAAK;MACvB,MAAMM,UAAU,GAAGN,KAAK,CAACO,WAAW,CAAC,CAAC;MACtC,IAAI,IAAI,CAACT,SAAS,CAACU,OAAO,CAACF,UAAU,CAAC,GAAG,CAAC,CAAC,EAAE;QAC3CF,OAAO,GAAG,CAAC,CAAC;QACZ3D,KAAK,IAAI4D,MAAM,EAAE;MACnB,CAAC,MAAM;QACL;QACA,IAAI,IAAI,CAAClB,UAAU,CAACmB,UAAU,CAAC,KAAKG,SAAS,EAAE;UAC7CN,KAAK,IAAIC,OAAO,GAAG,IAAI,CAACjB,UAAU,CAACmB,UAAU,CAAC;UAC9C7D,KAAK,IAAI4D,MAAM,EAAE;QACnB,CAAC,MAAM;UACL,IAAI,IAAI,CAACpB,OAAO,EAAE;YAChB,MAAMyB,WAAW,GAAG,IAAI,CAACzB,OAAO,CAACgB,IAAI,CAACK,UAAU,CAAC;YACjD,IAAI,IAAI,CAACnB,UAAU,CAACuB,WAAW,CAAC,KAAKD,SAAS,EAAE;cAC9CN,KAAK,IAAIC,OAAO,GAAG,IAAI,CAACjB,UAAU,CAACuB,WAAW,CAAC;cAC/CjE,KAAK,IAAI4D,MAAM,EAAE;YACnB;UACF;QACF;MACF;IACF,CAAC,CAAC;IAEFF,KAAK,GAAGA,KAAK,GAAGxC,KAAK,CAACgD,MAAM;IAC5BlE,KAAK,IAAImE,OAAO,CAACC,GAAG,CAAC,kBAAkB,GAAGR,MAAM,CAAC;IAEjD,OAAOF,KAAK;EACd;AACF;AAEAW,MAAM,CAACC,OAAO,GAAGjC,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}