{"ast": null, "code": "import createSizePropertyCheck from './_createSizePropertyCheck.js';\nimport getByteLength from './_getByteLength.js';\n\n// Internal helper to determine whether we should spend extensive checks against\n// `ArrayBuffer` et al.\nexport default createSizePropertyCheck(getByteLength);", "map": {"version": 3, "names": ["createSizePropertyCheck", "getByteLength"], "sources": ["C:/tmsft/node_modules/underscore/modules/_isBufferLike.js"], "sourcesContent": ["import createSizePropertyCheck from './_createSizePropertyCheck.js';\nimport getByteLength from './_getByteLength.js';\n\n// Internal helper to determine whether we should spend extensive checks against\n// `ArrayBuffer` et al.\nexport default createSizePropertyCheck(getByteLength);\n"], "mappings": "AAAA,OAAOA,uBAAuB,MAAM,+BAA+B;AACnE,OAAOC,aAAa,MAAM,qBAAqB;;AAE/C;AACA;AACA,eAAeD,uBAAuB,CAACC,aAAa,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}