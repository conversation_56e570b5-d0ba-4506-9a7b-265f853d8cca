{"ast": null, "code": "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { Reverse, TensorBuffer, util } from '@tensorflow/tfjs-core';\nimport { assertNotComplex } from '../cpu_util';\nimport { identity } from './Identity';\nexport function reverse(args) {\n  const {\n    inputs,\n    backend,\n    attrs\n  } = args;\n  const {\n    x\n  } = inputs;\n  const {\n    dims\n  } = attrs;\n  assertNotComplex(x, 'reverse');\n  const xRank = x.shape.length;\n  const $dims = util.parseAxisParam(dims, x.shape);\n  if (xRank === 0) {\n    return identity({\n      inputs: {\n        x\n      },\n      backend\n    });\n  }\n  const outBuf = new TensorBuffer(x.shape, x.dtype);\n  const xBuf = backend.bufferSync(x);\n  for (let i = 0; i < outBuf.size; i++) {\n    const outLoc = outBuf.indexToLoc(i);\n    const inLoc = outLoc.slice();\n    $dims.forEach(d => inLoc[d] = x.shape[d] - 1 - inLoc[d]);\n    outBuf.set(xBuf.get(...inLoc), ...outLoc);\n  }\n  return backend.makeTensorInfo(outBuf.shape, outBuf.dtype, outBuf.values);\n}\nexport const reverseConfig = {\n  kernelName: Reverse,\n  backendName: 'cpu',\n  kernelFunc: reverse\n};", "map": {"version": 3, "names": ["Reverse", "Tensor<PERSON><PERSON><PERSON>", "util", "assertNotComplex", "identity", "reverse", "args", "inputs", "backend", "attrs", "x", "dims", "xRank", "shape", "length", "$dims", "parseAxisParam", "outBuf", "dtype", "xBuf", "bufferSync", "i", "size", "outLoc", "indexToLoc", "inLoc", "slice", "for<PERSON>ach", "d", "set", "get", "makeTensorInfo", "values", "reverseConfig", "kernelName", "backendName", "kernelFunc"], "sources": ["C:\\tfjs-backend-cpu\\src\\kernels\\Reverse.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {KernelConfig, KernelFunc, Reverse, ReverseAttrs, ReverseInputs, TensorBuffer, TensorInfo, util} from '@tensorflow/tfjs-core';\n\nimport {MathBackendCPU} from '../backend_cpu';\nimport {assertNotComplex} from '../cpu_util';\nimport {identity} from './Identity';\n\nexport function reverse(\n    args:\n        {inputs: ReverseInputs, backend: MathBackendCPU, attrs: ReverseAttrs}):\n    TensorInfo {\n  const {inputs, backend, attrs} = args;\n  const {x} = inputs;\n  const {dims} = attrs;\n\n  assertNotComplex(x, 'reverse');\n\n  const xRank = x.shape.length;\n\n  const $dims = util.parseAxisParam(dims, x.shape);\n  if (xRank === 0) {\n    return identity({inputs: {x}, backend});\n  }\n\n  const outBuf = new TensorBuffer(x.shape, x.dtype);\n  const xBuf = backend.bufferSync(x);\n\n  for (let i = 0; i < outBuf.size; i++) {\n    const outLoc = outBuf.indexToLoc(i);\n    const inLoc = outLoc.slice();\n    $dims.forEach(d => inLoc[d] = x.shape[d] - 1 - inLoc[d]);\n    outBuf.set(xBuf.get(...inLoc), ...outLoc);\n  }\n\n  return backend.makeTensorInfo(outBuf.shape, outBuf.dtype, outBuf.values);\n}\n\nexport const reverseConfig: KernelConfig = {\n  kernelName: Reverse,\n  backendName: 'cpu',\n  kernelFunc: reverse as unknown as KernelFunc\n};\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAiBA,SAAkCA,OAAO,EAA+BC,YAAY,EAAcC,IAAI,QAAO,uBAAuB;AAGpI,SAAQC,gBAAgB,QAAO,aAAa;AAC5C,SAAQC,QAAQ,QAAO,YAAY;AAEnC,OAAM,SAAUC,OAAOA,CACnBC,IACyE;EAE3E,MAAM;IAACC,MAAM;IAAEC,OAAO;IAAEC;EAAK,CAAC,GAAGH,IAAI;EACrC,MAAM;IAACI;EAAC,CAAC,GAAGH,MAAM;EAClB,MAAM;IAACI;EAAI,CAAC,GAAGF,KAAK;EAEpBN,gBAAgB,CAACO,CAAC,EAAE,SAAS,CAAC;EAE9B,MAAME,KAAK,GAAGF,CAAC,CAACG,KAAK,CAACC,MAAM;EAE5B,MAAMC,KAAK,GAAGb,IAAI,CAACc,cAAc,CAACL,IAAI,EAAED,CAAC,CAACG,KAAK,CAAC;EAChD,IAAID,KAAK,KAAK,CAAC,EAAE;IACf,OAAOR,QAAQ,CAAC;MAACG,MAAM,EAAE;QAACG;MAAC,CAAC;MAAEF;IAAO,CAAC,CAAC;;EAGzC,MAAMS,MAAM,GAAG,IAAIhB,YAAY,CAACS,CAAC,CAACG,KAAK,EAAEH,CAAC,CAACQ,KAAK,CAAC;EACjD,MAAMC,IAAI,GAAGX,OAAO,CAACY,UAAU,CAACV,CAAC,CAAC;EAElC,KAAK,IAAIW,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,MAAM,CAACK,IAAI,EAAED,CAAC,EAAE,EAAE;IACpC,MAAME,MAAM,GAAGN,MAAM,CAACO,UAAU,CAACH,CAAC,CAAC;IACnC,MAAMI,KAAK,GAAGF,MAAM,CAACG,KAAK,EAAE;IAC5BX,KAAK,CAACY,OAAO,CAACC,CAAC,IAAIH,KAAK,CAACG,CAAC,CAAC,GAAGlB,CAAC,CAACG,KAAK,CAACe,CAAC,CAAC,GAAG,CAAC,GAAGH,KAAK,CAACG,CAAC,CAAC,CAAC;IACxDX,MAAM,CAACY,GAAG,CAACV,IAAI,CAACW,GAAG,CAAC,GAAGL,KAAK,CAAC,EAAE,GAAGF,MAAM,CAAC;;EAG3C,OAAOf,OAAO,CAACuB,cAAc,CAACd,MAAM,CAACJ,KAAK,EAAEI,MAAM,CAACC,KAAK,EAAED,MAAM,CAACe,MAAM,CAAC;AAC1E;AAEA,OAAO,MAAMC,aAAa,GAAiB;EACzCC,UAAU,EAAElC,OAAO;EACnBmC,WAAW,EAAE,KAAK;EAClBC,UAAU,EAAE/B;CACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}