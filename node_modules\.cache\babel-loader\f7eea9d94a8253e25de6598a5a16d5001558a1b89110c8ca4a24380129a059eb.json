{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.transformArguments = exports.FIRST_KEY_INDEX = void 0;\nconst SRANDMEMBER_1 = require(\"./SRANDMEMBER\");\nvar SRANDMEMBER_2 = require(\"./SRANDMEMBER\");\nObject.defineProperty(exports, \"FIRST_KEY_INDEX\", {\n  enumerable: true,\n  get: function () {\n    return SRANDMEMBER_2.FIRST_KEY_INDEX;\n  }\n});\nfunction transformArguments(key, count) {\n  return [...(0, SRANDMEMBER_1.transformArguments)(key), count.toString()];\n}\nexports.transformArguments = transformArguments;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "transformArguments", "FIRST_KEY_INDEX", "SRANDMEMBER_1", "require", "SRANDMEMBER_2", "enumerable", "get", "key", "count", "toString"], "sources": ["C:/tmsft/node_modules/@redis/client/dist/lib/commands/SRANDMEMBER_COUNT.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.transformArguments = exports.FIRST_KEY_INDEX = void 0;\nconst SRANDMEMBER_1 = require(\"./SRANDMEMBER\");\nvar SRANDMEMBER_2 = require(\"./SRANDMEMBER\");\nObject.defineProperty(exports, \"FIRST_KEY_INDEX\", { enumerable: true, get: function () { return SRANDMEMBER_2.FIRST_KEY_INDEX; } });\nfunction transformArguments(key, count) {\n    return [\n        ...(0, SRANDMEMBER_1.transformArguments)(key),\n        count.toString()\n    ];\n}\nexports.transformArguments = transformArguments;\n"], "mappings": "AAAA,YAAY;;AACZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEC,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7DD,OAAO,CAACE,kBAAkB,GAAGF,OAAO,CAACG,eAAe,GAAG,KAAK,CAAC;AAC7D,MAAMC,aAAa,GAAGC,OAAO,CAAC,eAAe,CAAC;AAC9C,IAAIC,aAAa,GAAGD,OAAO,CAAC,eAAe,CAAC;AAC5CP,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,iBAAiB,EAAE;EAAEO,UAAU,EAAE,IAAI;EAAEC,GAAG,EAAE,SAAAA,CAAA,EAAY;IAAE,OAAOF,aAAa,CAACH,eAAe;EAAE;AAAE,CAAC,CAAC;AACnI,SAASD,kBAAkBA,CAACO,GAAG,EAAEC,KAAK,EAAE;EACpC,OAAO,CACH,GAAG,CAAC,CAAC,EAAEN,aAAa,CAACF,kBAAkB,EAAEO,GAAG,CAAC,EAC7CC,KAAK,CAACC,QAAQ,CAAC,CAAC,CACnB;AACL;AACAX,OAAO,CAACE,kBAAkB,GAAGA,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}