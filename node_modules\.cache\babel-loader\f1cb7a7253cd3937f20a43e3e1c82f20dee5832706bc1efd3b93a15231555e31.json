{"ast": null, "code": "'use strict';\n\n// eslint-disable-next-line\nvar Native;\n// eslint-disable-next-line no-useless-catch\ntry {\n  // Wrap this `require()` in a try-catch to avoid upstream bundlers from complaining that this might not be available since it is an optional import\n  Native = require('pg-native');\n} catch (e) {\n  throw e;\n}\nconst TypeOverrides = require('../type-overrides');\nconst EventEmitter = require('events').EventEmitter;\nconst util = require('util');\nconst ConnectionParameters = require('../connection-parameters');\nconst NativeQuery = require('./query');\nconst Client = module.exports = function (config) {\n  EventEmitter.call(this);\n  config = config || {};\n  this._Promise = config.Promise || global.Promise;\n  this._types = new TypeOverrides(config.types);\n  this.native = new Native({\n    types: this._types\n  });\n  this._queryQueue = [];\n  this._ending = false;\n  this._connecting = false;\n  this._connected = false;\n  this._queryable = true;\n\n  // keep these on the object for legacy reasons\n  // for the time being. TODO: deprecate all this jazz\n  const cp = this.connectionParameters = new ConnectionParameters(config);\n  if (config.nativeConnectionString) cp.nativeConnectionString = config.nativeConnectionString;\n  this.user = cp.user;\n\n  // \"hiding\" the password so it doesn't show up in stack traces\n  // or if the client is console.logged\n  Object.defineProperty(this, 'password', {\n    configurable: true,\n    enumerable: false,\n    writable: true,\n    value: cp.password\n  });\n  this.database = cp.database;\n  this.host = cp.host;\n  this.port = cp.port;\n\n  // a hash to hold named queries\n  this.namedQueries = {};\n};\nClient.Query = NativeQuery;\nutil.inherits(Client, EventEmitter);\nClient.prototype._errorAllQueries = function (err) {\n  const enqueueError = query => {\n    process.nextTick(() => {\n      query.native = this.native;\n      query.handleError(err);\n    });\n  };\n  if (this._hasActiveQuery()) {\n    enqueueError(this._activeQuery);\n    this._activeQuery = null;\n  }\n  this._queryQueue.forEach(enqueueError);\n  this._queryQueue.length = 0;\n};\n\n// connect to the backend\n// pass an optional callback to be called once connected\n// or with an error if there was a connection error\nClient.prototype._connect = function (cb) {\n  const self = this;\n  if (this._connecting) {\n    process.nextTick(() => cb(new Error('Client has already been connected. You cannot reuse a client.')));\n    return;\n  }\n  this._connecting = true;\n  this.connectionParameters.getLibpqConnectionString(function (err, conString) {\n    if (self.connectionParameters.nativeConnectionString) conString = self.connectionParameters.nativeConnectionString;\n    if (err) return cb(err);\n    self.native.connect(conString, function (err) {\n      if (err) {\n        self.native.end();\n        return cb(err);\n      }\n\n      // set internal states to connected\n      self._connected = true;\n\n      // handle connection errors from the native layer\n      self.native.on('error', function (err) {\n        self._queryable = false;\n        self._errorAllQueries(err);\n        self.emit('error', err);\n      });\n      self.native.on('notification', function (msg) {\n        self.emit('notification', {\n          channel: msg.relname,\n          payload: msg.extra\n        });\n      });\n\n      // signal we are connected now\n      self.emit('connect');\n      self._pulseQueryQueue(true);\n      cb();\n    });\n  });\n};\nClient.prototype.connect = function (callback) {\n  if (callback) {\n    this._connect(callback);\n    return;\n  }\n  return new this._Promise((resolve, reject) => {\n    this._connect(error => {\n      if (error) {\n        reject(error);\n      } else {\n        resolve();\n      }\n    });\n  });\n};\n\n// send a query to the server\n// this method is highly overloaded to take\n// 1) string query, optional array of parameters, optional function callback\n// 2) object query with {\n//    string query\n//    optional array values,\n//    optional function callback instead of as a separate parameter\n//    optional string name to name & cache the query plan\n//    optional string rowMode = 'array' for an array of results\n//  }\nClient.prototype.query = function (config, values, callback) {\n  let query;\n  let result;\n  let readTimeout;\n  let readTimeoutTimer;\n  let queryCallback;\n  if (config === null || config === undefined) {\n    throw new TypeError('Client was passed a null or undefined query');\n  } else if (typeof config.submit === 'function') {\n    readTimeout = config.query_timeout || this.connectionParameters.query_timeout;\n    result = query = config;\n    // accept query(new Query(...), (err, res) => { }) style\n    if (typeof values === 'function') {\n      config.callback = values;\n    }\n  } else {\n    readTimeout = config.query_timeout || this.connectionParameters.query_timeout;\n    query = new NativeQuery(config, values, callback);\n    if (!query.callback) {\n      let resolveOut, rejectOut;\n      result = new this._Promise((resolve, reject) => {\n        resolveOut = resolve;\n        rejectOut = reject;\n      }).catch(err => {\n        Error.captureStackTrace(err);\n        throw err;\n      });\n      query.callback = (err, res) => err ? rejectOut(err) : resolveOut(res);\n    }\n  }\n  if (readTimeout) {\n    queryCallback = query.callback;\n    readTimeoutTimer = setTimeout(() => {\n      const error = new Error('Query read timeout');\n      process.nextTick(() => {\n        query.handleError(error, this.connection);\n      });\n      queryCallback(error);\n\n      // we already returned an error,\n      // just do nothing if query completes\n      query.callback = () => {};\n\n      // Remove from queue\n      const index = this._queryQueue.indexOf(query);\n      if (index > -1) {\n        this._queryQueue.splice(index, 1);\n      }\n      this._pulseQueryQueue();\n    }, readTimeout);\n    query.callback = (err, res) => {\n      clearTimeout(readTimeoutTimer);\n      queryCallback(err, res);\n    };\n  }\n  if (!this._queryable) {\n    query.native = this.native;\n    process.nextTick(() => {\n      query.handleError(new Error('Client has encountered a connection error and is not queryable'));\n    });\n    return result;\n  }\n  if (this._ending) {\n    query.native = this.native;\n    process.nextTick(() => {\n      query.handleError(new Error('Client was closed and is not queryable'));\n    });\n    return result;\n  }\n  this._queryQueue.push(query);\n  this._pulseQueryQueue();\n  return result;\n};\n\n// disconnect from the backend server\nClient.prototype.end = function (cb) {\n  const self = this;\n  this._ending = true;\n  if (!this._connected) {\n    this.once('connect', this.end.bind(this, cb));\n  }\n  let result;\n  if (!cb) {\n    result = new this._Promise(function (resolve, reject) {\n      cb = err => err ? reject(err) : resolve();\n    });\n  }\n  this.native.end(function () {\n    self._errorAllQueries(new Error('Connection terminated'));\n    process.nextTick(() => {\n      self.emit('end');\n      if (cb) cb();\n    });\n  });\n  return result;\n};\nClient.prototype._hasActiveQuery = function () {\n  return this._activeQuery && this._activeQuery.state !== 'error' && this._activeQuery.state !== 'end';\n};\nClient.prototype._pulseQueryQueue = function (initialConnection) {\n  if (!this._connected) {\n    return;\n  }\n  if (this._hasActiveQuery()) {\n    return;\n  }\n  const query = this._queryQueue.shift();\n  if (!query) {\n    if (!initialConnection) {\n      this.emit('drain');\n    }\n    return;\n  }\n  this._activeQuery = query;\n  query.submit(this);\n  const self = this;\n  query.once('_done', function () {\n    self._pulseQueryQueue();\n  });\n};\n\n// attempt to cancel an in-progress query\nClient.prototype.cancel = function (query) {\n  if (this._activeQuery === query) {\n    this.native.cancel(function () {});\n  } else if (this._queryQueue.indexOf(query) !== -1) {\n    this._queryQueue.splice(this._queryQueue.indexOf(query), 1);\n  }\n};\nClient.prototype.ref = function () {};\nClient.prototype.unref = function () {};\nClient.prototype.setTypeParser = function (oid, format, parseFn) {\n  return this._types.setTypeParser(oid, format, parseFn);\n};\nClient.prototype.getTypeParser = function (oid, format) {\n  return this._types.getTypeParser(oid, format);\n};", "map": {"version": 3, "names": ["Native", "require", "e", "TypeOverrides", "EventEmitter", "util", "ConnectionParameters", "Native<PERSON><PERSON>y", "Client", "module", "exports", "config", "call", "_Promise", "Promise", "global", "_types", "types", "native", "_queryQueue", "_ending", "_connecting", "_connected", "_queryable", "cp", "connectionParameters", "nativeConnectionString", "user", "Object", "defineProperty", "configurable", "enumerable", "writable", "value", "password", "database", "host", "port", "named<PERSON><PERSON><PERSON>", "Query", "inherits", "prototype", "_errorAllQueries", "err", "enqueue<PERSON><PERSON>r", "query", "process", "nextTick", "handleError", "_hasActiveQuery", "_activeQuery", "for<PERSON>ach", "length", "_connect", "cb", "self", "Error", "getLibpqConnectionString", "conString", "connect", "end", "on", "emit", "msg", "channel", "relname", "payload", "extra", "_pulseQueryQueue", "callback", "resolve", "reject", "error", "values", "result", "readTimeout", "readTimeoutTimer", "query<PERSON><PERSON><PERSON>", "undefined", "TypeError", "submit", "query_timeout", "resolveOut", "rejectOut", "catch", "captureStackTrace", "res", "setTimeout", "connection", "index", "indexOf", "splice", "clearTimeout", "push", "once", "bind", "state", "initialConnection", "shift", "cancel", "ref", "unref", "setType<PERSON><PERSON><PERSON>", "oid", "format", "parseFn", "getType<PERSON><PERSON><PERSON>"], "sources": ["C:/tmsft/node_modules/pg/lib/native/client.js"], "sourcesContent": ["'use strict'\n\n// eslint-disable-next-line\nvar Native\n// eslint-disable-next-line no-useless-catch\ntry {\n  // Wrap this `require()` in a try-catch to avoid upstream bundlers from complaining that this might not be available since it is an optional import\n  Native = require('pg-native')\n} catch (e) {\n  throw e\n}\nconst TypeOverrides = require('../type-overrides')\nconst EventEmitter = require('events').EventEmitter\nconst util = require('util')\nconst ConnectionParameters = require('../connection-parameters')\n\nconst NativeQuery = require('./query')\n\nconst Client = (module.exports = function (config) {\n  EventEmitter.call(this)\n  config = config || {}\n\n  this._Promise = config.Promise || global.Promise\n  this._types = new TypeOverrides(config.types)\n\n  this.native = new Native({\n    types: this._types,\n  })\n\n  this._queryQueue = []\n  this._ending = false\n  this._connecting = false\n  this._connected = false\n  this._queryable = true\n\n  // keep these on the object for legacy reasons\n  // for the time being. TODO: deprecate all this jazz\n  const cp = (this.connectionParameters = new ConnectionParameters(config))\n  if (config.nativeConnectionString) cp.nativeConnectionString = config.nativeConnectionString\n  this.user = cp.user\n\n  // \"hiding\" the password so it doesn't show up in stack traces\n  // or if the client is console.logged\n  Object.defineProperty(this, 'password', {\n    configurable: true,\n    enumerable: false,\n    writable: true,\n    value: cp.password,\n  })\n  this.database = cp.database\n  this.host = cp.host\n  this.port = cp.port\n\n  // a hash to hold named queries\n  this.namedQueries = {}\n})\n\nClient.Query = NativeQuery\n\nutil.inherits(Client, EventEmitter)\n\nClient.prototype._errorAllQueries = function (err) {\n  const enqueueError = (query) => {\n    process.nextTick(() => {\n      query.native = this.native\n      query.handleError(err)\n    })\n  }\n\n  if (this._hasActiveQuery()) {\n    enqueueError(this._activeQuery)\n    this._activeQuery = null\n  }\n\n  this._queryQueue.forEach(enqueueError)\n  this._queryQueue.length = 0\n}\n\n// connect to the backend\n// pass an optional callback to be called once connected\n// or with an error if there was a connection error\nClient.prototype._connect = function (cb) {\n  const self = this\n\n  if (this._connecting) {\n    process.nextTick(() => cb(new Error('Client has already been connected. You cannot reuse a client.')))\n    return\n  }\n\n  this._connecting = true\n\n  this.connectionParameters.getLibpqConnectionString(function (err, conString) {\n    if (self.connectionParameters.nativeConnectionString) conString = self.connectionParameters.nativeConnectionString\n    if (err) return cb(err)\n    self.native.connect(conString, function (err) {\n      if (err) {\n        self.native.end()\n        return cb(err)\n      }\n\n      // set internal states to connected\n      self._connected = true\n\n      // handle connection errors from the native layer\n      self.native.on('error', function (err) {\n        self._queryable = false\n        self._errorAllQueries(err)\n        self.emit('error', err)\n      })\n\n      self.native.on('notification', function (msg) {\n        self.emit('notification', {\n          channel: msg.relname,\n          payload: msg.extra,\n        })\n      })\n\n      // signal we are connected now\n      self.emit('connect')\n      self._pulseQueryQueue(true)\n\n      cb()\n    })\n  })\n}\n\nClient.prototype.connect = function (callback) {\n  if (callback) {\n    this._connect(callback)\n    return\n  }\n\n  return new this._Promise((resolve, reject) => {\n    this._connect((error) => {\n      if (error) {\n        reject(error)\n      } else {\n        resolve()\n      }\n    })\n  })\n}\n\n// send a query to the server\n// this method is highly overloaded to take\n// 1) string query, optional array of parameters, optional function callback\n// 2) object query with {\n//    string query\n//    optional array values,\n//    optional function callback instead of as a separate parameter\n//    optional string name to name & cache the query plan\n//    optional string rowMode = 'array' for an array of results\n//  }\nClient.prototype.query = function (config, values, callback) {\n  let query\n  let result\n  let readTimeout\n  let readTimeoutTimer\n  let queryCallback\n\n  if (config === null || config === undefined) {\n    throw new TypeError('Client was passed a null or undefined query')\n  } else if (typeof config.submit === 'function') {\n    readTimeout = config.query_timeout || this.connectionParameters.query_timeout\n    result = query = config\n    // accept query(new Query(...), (err, res) => { }) style\n    if (typeof values === 'function') {\n      config.callback = values\n    }\n  } else {\n    readTimeout = config.query_timeout || this.connectionParameters.query_timeout\n    query = new NativeQuery(config, values, callback)\n    if (!query.callback) {\n      let resolveOut, rejectOut\n      result = new this._Promise((resolve, reject) => {\n        resolveOut = resolve\n        rejectOut = reject\n      }).catch((err) => {\n        Error.captureStackTrace(err)\n        throw err\n      })\n      query.callback = (err, res) => (err ? rejectOut(err) : resolveOut(res))\n    }\n  }\n\n  if (readTimeout) {\n    queryCallback = query.callback\n\n    readTimeoutTimer = setTimeout(() => {\n      const error = new Error('Query read timeout')\n\n      process.nextTick(() => {\n        query.handleError(error, this.connection)\n      })\n\n      queryCallback(error)\n\n      // we already returned an error,\n      // just do nothing if query completes\n      query.callback = () => {}\n\n      // Remove from queue\n      const index = this._queryQueue.indexOf(query)\n      if (index > -1) {\n        this._queryQueue.splice(index, 1)\n      }\n\n      this._pulseQueryQueue()\n    }, readTimeout)\n\n    query.callback = (err, res) => {\n      clearTimeout(readTimeoutTimer)\n      queryCallback(err, res)\n    }\n  }\n\n  if (!this._queryable) {\n    query.native = this.native\n    process.nextTick(() => {\n      query.handleError(new Error('Client has encountered a connection error and is not queryable'))\n    })\n    return result\n  }\n\n  if (this._ending) {\n    query.native = this.native\n    process.nextTick(() => {\n      query.handleError(new Error('Client was closed and is not queryable'))\n    })\n    return result\n  }\n\n  this._queryQueue.push(query)\n  this._pulseQueryQueue()\n  return result\n}\n\n// disconnect from the backend server\nClient.prototype.end = function (cb) {\n  const self = this\n\n  this._ending = true\n\n  if (!this._connected) {\n    this.once('connect', this.end.bind(this, cb))\n  }\n  let result\n  if (!cb) {\n    result = new this._Promise(function (resolve, reject) {\n      cb = (err) => (err ? reject(err) : resolve())\n    })\n  }\n  this.native.end(function () {\n    self._errorAllQueries(new Error('Connection terminated'))\n\n    process.nextTick(() => {\n      self.emit('end')\n      if (cb) cb()\n    })\n  })\n  return result\n}\n\nClient.prototype._hasActiveQuery = function () {\n  return this._activeQuery && this._activeQuery.state !== 'error' && this._activeQuery.state !== 'end'\n}\n\nClient.prototype._pulseQueryQueue = function (initialConnection) {\n  if (!this._connected) {\n    return\n  }\n  if (this._hasActiveQuery()) {\n    return\n  }\n  const query = this._queryQueue.shift()\n  if (!query) {\n    if (!initialConnection) {\n      this.emit('drain')\n    }\n    return\n  }\n  this._activeQuery = query\n  query.submit(this)\n  const self = this\n  query.once('_done', function () {\n    self._pulseQueryQueue()\n  })\n}\n\n// attempt to cancel an in-progress query\nClient.prototype.cancel = function (query) {\n  if (this._activeQuery === query) {\n    this.native.cancel(function () {})\n  } else if (this._queryQueue.indexOf(query) !== -1) {\n    this._queryQueue.splice(this._queryQueue.indexOf(query), 1)\n  }\n}\n\nClient.prototype.ref = function () {}\nClient.prototype.unref = function () {}\n\nClient.prototype.setTypeParser = function (oid, format, parseFn) {\n  return this._types.setTypeParser(oid, format, parseFn)\n}\n\nClient.prototype.getTypeParser = function (oid, format) {\n  return this._types.getTypeParser(oid, format)\n}\n"], "mappings": "AAAA,YAAY;;AAEZ;AACA,IAAIA,MAAM;AACV;AACA,IAAI;EACF;EACAA,MAAM,GAAGC,OAAO,CAAC,WAAW,CAAC;AAC/B,CAAC,CAAC,OAAOC,CAAC,EAAE;EACV,MAAMA,CAAC;AACT;AACA,MAAMC,aAAa,GAAGF,OAAO,CAAC,mBAAmB,CAAC;AAClD,MAAMG,YAAY,GAAGH,OAAO,CAAC,QAAQ,CAAC,CAACG,YAAY;AACnD,MAAMC,IAAI,GAAGJ,OAAO,CAAC,MAAM,CAAC;AAC5B,MAAMK,oBAAoB,GAAGL,OAAO,CAAC,0BAA0B,CAAC;AAEhE,MAAMM,WAAW,GAAGN,OAAO,CAAC,SAAS,CAAC;AAEtC,MAAMO,MAAM,GAAIC,MAAM,CAACC,OAAO,GAAG,UAAUC,MAAM,EAAE;EACjDP,YAAY,CAACQ,IAAI,CAAC,IAAI,CAAC;EACvBD,MAAM,GAAGA,MAAM,IAAI,CAAC,CAAC;EAErB,IAAI,CAACE,QAAQ,GAAGF,MAAM,CAACG,OAAO,IAAIC,MAAM,CAACD,OAAO;EAChD,IAAI,CAACE,MAAM,GAAG,IAAIb,aAAa,CAACQ,MAAM,CAACM,KAAK,CAAC;EAE7C,IAAI,CAACC,MAAM,GAAG,IAAIlB,MAAM,CAAC;IACvBiB,KAAK,EAAE,IAAI,CAACD;EACd,CAAC,CAAC;EAEF,IAAI,CAACG,WAAW,GAAG,EAAE;EACrB,IAAI,CAACC,OAAO,GAAG,KAAK;EACpB,IAAI,CAACC,WAAW,GAAG,KAAK;EACxB,IAAI,CAACC,UAAU,GAAG,KAAK;EACvB,IAAI,CAACC,UAAU,GAAG,IAAI;;EAEtB;EACA;EACA,MAAMC,EAAE,GAAI,IAAI,CAACC,oBAAoB,GAAG,IAAInB,oBAAoB,CAACK,MAAM,CAAE;EACzE,IAAIA,MAAM,CAACe,sBAAsB,EAAEF,EAAE,CAACE,sBAAsB,GAAGf,MAAM,CAACe,sBAAsB;EAC5F,IAAI,CAACC,IAAI,GAAGH,EAAE,CAACG,IAAI;;EAEnB;EACA;EACAC,MAAM,CAACC,cAAc,CAAC,IAAI,EAAE,UAAU,EAAE;IACtCC,YAAY,EAAE,IAAI;IAClBC,UAAU,EAAE,KAAK;IACjBC,QAAQ,EAAE,IAAI;IACdC,KAAK,EAAET,EAAE,CAACU;EACZ,CAAC,CAAC;EACF,IAAI,CAACC,QAAQ,GAAGX,EAAE,CAACW,QAAQ;EAC3B,IAAI,CAACC,IAAI,GAAGZ,EAAE,CAACY,IAAI;EACnB,IAAI,CAACC,IAAI,GAAGb,EAAE,CAACa,IAAI;;EAEnB;EACA,IAAI,CAACC,YAAY,GAAG,CAAC,CAAC;AACxB,CAAE;AAEF9B,MAAM,CAAC+B,KAAK,GAAGhC,WAAW;AAE1BF,IAAI,CAACmC,QAAQ,CAAChC,MAAM,EAAEJ,YAAY,CAAC;AAEnCI,MAAM,CAACiC,SAAS,CAACC,gBAAgB,GAAG,UAAUC,GAAG,EAAE;EACjD,MAAMC,YAAY,GAAIC,KAAK,IAAK;IAC9BC,OAAO,CAACC,QAAQ,CAAC,MAAM;MACrBF,KAAK,CAAC3B,MAAM,GAAG,IAAI,CAACA,MAAM;MAC1B2B,KAAK,CAACG,WAAW,CAACL,GAAG,CAAC;IACxB,CAAC,CAAC;EACJ,CAAC;EAED,IAAI,IAAI,CAACM,eAAe,CAAC,CAAC,EAAE;IAC1BL,YAAY,CAAC,IAAI,CAACM,YAAY,CAAC;IAC/B,IAAI,CAACA,YAAY,GAAG,IAAI;EAC1B;EAEA,IAAI,CAAC/B,WAAW,CAACgC,OAAO,CAACP,YAAY,CAAC;EACtC,IAAI,CAACzB,WAAW,CAACiC,MAAM,GAAG,CAAC;AAC7B,CAAC;;AAED;AACA;AACA;AACA5C,MAAM,CAACiC,SAAS,CAACY,QAAQ,GAAG,UAAUC,EAAE,EAAE;EACxC,MAAMC,IAAI,GAAG,IAAI;EAEjB,IAAI,IAAI,CAAClC,WAAW,EAAE;IACpByB,OAAO,CAACC,QAAQ,CAAC,MAAMO,EAAE,CAAC,IAAIE,KAAK,CAAC,+DAA+D,CAAC,CAAC,CAAC;IACtG;EACF;EAEA,IAAI,CAACnC,WAAW,GAAG,IAAI;EAEvB,IAAI,CAACI,oBAAoB,CAACgC,wBAAwB,CAAC,UAAUd,GAAG,EAAEe,SAAS,EAAE;IAC3E,IAAIH,IAAI,CAAC9B,oBAAoB,CAACC,sBAAsB,EAAEgC,SAAS,GAAGH,IAAI,CAAC9B,oBAAoB,CAACC,sBAAsB;IAClH,IAAIiB,GAAG,EAAE,OAAOW,EAAE,CAACX,GAAG,CAAC;IACvBY,IAAI,CAACrC,MAAM,CAACyC,OAAO,CAACD,SAAS,EAAE,UAAUf,GAAG,EAAE;MAC5C,IAAIA,GAAG,EAAE;QACPY,IAAI,CAACrC,MAAM,CAAC0C,GAAG,CAAC,CAAC;QACjB,OAAON,EAAE,CAACX,GAAG,CAAC;MAChB;;MAEA;MACAY,IAAI,CAACjC,UAAU,GAAG,IAAI;;MAEtB;MACAiC,IAAI,CAACrC,MAAM,CAAC2C,EAAE,CAAC,OAAO,EAAE,UAAUlB,GAAG,EAAE;QACrCY,IAAI,CAAChC,UAAU,GAAG,KAAK;QACvBgC,IAAI,CAACb,gBAAgB,CAACC,GAAG,CAAC;QAC1BY,IAAI,CAACO,IAAI,CAAC,OAAO,EAAEnB,GAAG,CAAC;MACzB,CAAC,CAAC;MAEFY,IAAI,CAACrC,MAAM,CAAC2C,EAAE,CAAC,cAAc,EAAE,UAAUE,GAAG,EAAE;QAC5CR,IAAI,CAACO,IAAI,CAAC,cAAc,EAAE;UACxBE,OAAO,EAAED,GAAG,CAACE,OAAO;UACpBC,OAAO,EAAEH,GAAG,CAACI;QACf,CAAC,CAAC;MACJ,CAAC,CAAC;;MAEF;MACAZ,IAAI,CAACO,IAAI,CAAC,SAAS,CAAC;MACpBP,IAAI,CAACa,gBAAgB,CAAC,IAAI,CAAC;MAE3Bd,EAAE,CAAC,CAAC;IACN,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ,CAAC;AAED9C,MAAM,CAACiC,SAAS,CAACkB,OAAO,GAAG,UAAUU,QAAQ,EAAE;EAC7C,IAAIA,QAAQ,EAAE;IACZ,IAAI,CAAChB,QAAQ,CAACgB,QAAQ,CAAC;IACvB;EACF;EAEA,OAAO,IAAI,IAAI,CAACxD,QAAQ,CAAC,CAACyD,OAAO,EAAEC,MAAM,KAAK;IAC5C,IAAI,CAAClB,QAAQ,CAAEmB,KAAK,IAAK;MACvB,IAAIA,KAAK,EAAE;QACTD,MAAM,CAACC,KAAK,CAAC;MACf,CAAC,MAAM;QACLF,OAAO,CAAC,CAAC;MACX;IACF,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA9D,MAAM,CAACiC,SAAS,CAACI,KAAK,GAAG,UAAUlC,MAAM,EAAE8D,MAAM,EAAEJ,QAAQ,EAAE;EAC3D,IAAIxB,KAAK;EACT,IAAI6B,MAAM;EACV,IAAIC,WAAW;EACf,IAAIC,gBAAgB;EACpB,IAAIC,aAAa;EAEjB,IAAIlE,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAKmE,SAAS,EAAE;IAC3C,MAAM,IAAIC,SAAS,CAAC,6CAA6C,CAAC;EACpE,CAAC,MAAM,IAAI,OAAOpE,MAAM,CAACqE,MAAM,KAAK,UAAU,EAAE;IAC9CL,WAAW,GAAGhE,MAAM,CAACsE,aAAa,IAAI,IAAI,CAACxD,oBAAoB,CAACwD,aAAa;IAC7EP,MAAM,GAAG7B,KAAK,GAAGlC,MAAM;IACvB;IACA,IAAI,OAAO8D,MAAM,KAAK,UAAU,EAAE;MAChC9D,MAAM,CAAC0D,QAAQ,GAAGI,MAAM;IAC1B;EACF,CAAC,MAAM;IACLE,WAAW,GAAGhE,MAAM,CAACsE,aAAa,IAAI,IAAI,CAACxD,oBAAoB,CAACwD,aAAa;IAC7EpC,KAAK,GAAG,IAAItC,WAAW,CAACI,MAAM,EAAE8D,MAAM,EAAEJ,QAAQ,CAAC;IACjD,IAAI,CAACxB,KAAK,CAACwB,QAAQ,EAAE;MACnB,IAAIa,UAAU,EAAEC,SAAS;MACzBT,MAAM,GAAG,IAAI,IAAI,CAAC7D,QAAQ,CAAC,CAACyD,OAAO,EAAEC,MAAM,KAAK;QAC9CW,UAAU,GAAGZ,OAAO;QACpBa,SAAS,GAAGZ,MAAM;MACpB,CAAC,CAAC,CAACa,KAAK,CAAEzC,GAAG,IAAK;QAChBa,KAAK,CAAC6B,iBAAiB,CAAC1C,GAAG,CAAC;QAC5B,MAAMA,GAAG;MACX,CAAC,CAAC;MACFE,KAAK,CAACwB,QAAQ,GAAG,CAAC1B,GAAG,EAAE2C,GAAG,KAAM3C,GAAG,GAAGwC,SAAS,CAACxC,GAAG,CAAC,GAAGuC,UAAU,CAACI,GAAG,CAAE;IACzE;EACF;EAEA,IAAIX,WAAW,EAAE;IACfE,aAAa,GAAGhC,KAAK,CAACwB,QAAQ;IAE9BO,gBAAgB,GAAGW,UAAU,CAAC,MAAM;MAClC,MAAMf,KAAK,GAAG,IAAIhB,KAAK,CAAC,oBAAoB,CAAC;MAE7CV,OAAO,CAACC,QAAQ,CAAC,MAAM;QACrBF,KAAK,CAACG,WAAW,CAACwB,KAAK,EAAE,IAAI,CAACgB,UAAU,CAAC;MAC3C,CAAC,CAAC;MAEFX,aAAa,CAACL,KAAK,CAAC;;MAEpB;MACA;MACA3B,KAAK,CAACwB,QAAQ,GAAG,MAAM,CAAC,CAAC;;MAEzB;MACA,MAAMoB,KAAK,GAAG,IAAI,CAACtE,WAAW,CAACuE,OAAO,CAAC7C,KAAK,CAAC;MAC7C,IAAI4C,KAAK,GAAG,CAAC,CAAC,EAAE;QACd,IAAI,CAACtE,WAAW,CAACwE,MAAM,CAACF,KAAK,EAAE,CAAC,CAAC;MACnC;MAEA,IAAI,CAACrB,gBAAgB,CAAC,CAAC;IACzB,CAAC,EAAEO,WAAW,CAAC;IAEf9B,KAAK,CAACwB,QAAQ,GAAG,CAAC1B,GAAG,EAAE2C,GAAG,KAAK;MAC7BM,YAAY,CAAChB,gBAAgB,CAAC;MAC9BC,aAAa,CAAClC,GAAG,EAAE2C,GAAG,CAAC;IACzB,CAAC;EACH;EAEA,IAAI,CAAC,IAAI,CAAC/D,UAAU,EAAE;IACpBsB,KAAK,CAAC3B,MAAM,GAAG,IAAI,CAACA,MAAM;IAC1B4B,OAAO,CAACC,QAAQ,CAAC,MAAM;MACrBF,KAAK,CAACG,WAAW,CAAC,IAAIQ,KAAK,CAAC,gEAAgE,CAAC,CAAC;IAChG,CAAC,CAAC;IACF,OAAOkB,MAAM;EACf;EAEA,IAAI,IAAI,CAACtD,OAAO,EAAE;IAChByB,KAAK,CAAC3B,MAAM,GAAG,IAAI,CAACA,MAAM;IAC1B4B,OAAO,CAACC,QAAQ,CAAC,MAAM;MACrBF,KAAK,CAACG,WAAW,CAAC,IAAIQ,KAAK,CAAC,wCAAwC,CAAC,CAAC;IACxE,CAAC,CAAC;IACF,OAAOkB,MAAM;EACf;EAEA,IAAI,CAACvD,WAAW,CAAC0E,IAAI,CAAChD,KAAK,CAAC;EAC5B,IAAI,CAACuB,gBAAgB,CAAC,CAAC;EACvB,OAAOM,MAAM;AACf,CAAC;;AAED;AACAlE,MAAM,CAACiC,SAAS,CAACmB,GAAG,GAAG,UAAUN,EAAE,EAAE;EACnC,MAAMC,IAAI,GAAG,IAAI;EAEjB,IAAI,CAACnC,OAAO,GAAG,IAAI;EAEnB,IAAI,CAAC,IAAI,CAACE,UAAU,EAAE;IACpB,IAAI,CAACwE,IAAI,CAAC,SAAS,EAAE,IAAI,CAAClC,GAAG,CAACmC,IAAI,CAAC,IAAI,EAAEzC,EAAE,CAAC,CAAC;EAC/C;EACA,IAAIoB,MAAM;EACV,IAAI,CAACpB,EAAE,EAAE;IACPoB,MAAM,GAAG,IAAI,IAAI,CAAC7D,QAAQ,CAAC,UAAUyD,OAAO,EAAEC,MAAM,EAAE;MACpDjB,EAAE,GAAIX,GAAG,IAAMA,GAAG,GAAG4B,MAAM,CAAC5B,GAAG,CAAC,GAAG2B,OAAO,CAAC,CAAE;IAC/C,CAAC,CAAC;EACJ;EACA,IAAI,CAACpD,MAAM,CAAC0C,GAAG,CAAC,YAAY;IAC1BL,IAAI,CAACb,gBAAgB,CAAC,IAAIc,KAAK,CAAC,uBAAuB,CAAC,CAAC;IAEzDV,OAAO,CAACC,QAAQ,CAAC,MAAM;MACrBQ,IAAI,CAACO,IAAI,CAAC,KAAK,CAAC;MAChB,IAAIR,EAAE,EAAEA,EAAE,CAAC,CAAC;IACd,CAAC,CAAC;EACJ,CAAC,CAAC;EACF,OAAOoB,MAAM;AACf,CAAC;AAEDlE,MAAM,CAACiC,SAAS,CAACQ,eAAe,GAAG,YAAY;EAC7C,OAAO,IAAI,CAACC,YAAY,IAAI,IAAI,CAACA,YAAY,CAAC8C,KAAK,KAAK,OAAO,IAAI,IAAI,CAAC9C,YAAY,CAAC8C,KAAK,KAAK,KAAK;AACtG,CAAC;AAEDxF,MAAM,CAACiC,SAAS,CAAC2B,gBAAgB,GAAG,UAAU6B,iBAAiB,EAAE;EAC/D,IAAI,CAAC,IAAI,CAAC3E,UAAU,EAAE;IACpB;EACF;EACA,IAAI,IAAI,CAAC2B,eAAe,CAAC,CAAC,EAAE;IAC1B;EACF;EACA,MAAMJ,KAAK,GAAG,IAAI,CAAC1B,WAAW,CAAC+E,KAAK,CAAC,CAAC;EACtC,IAAI,CAACrD,KAAK,EAAE;IACV,IAAI,CAACoD,iBAAiB,EAAE;MACtB,IAAI,CAACnC,IAAI,CAAC,OAAO,CAAC;IACpB;IACA;EACF;EACA,IAAI,CAACZ,YAAY,GAAGL,KAAK;EACzBA,KAAK,CAACmC,MAAM,CAAC,IAAI,CAAC;EAClB,MAAMzB,IAAI,GAAG,IAAI;EACjBV,KAAK,CAACiD,IAAI,CAAC,OAAO,EAAE,YAAY;IAC9BvC,IAAI,CAACa,gBAAgB,CAAC,CAAC;EACzB,CAAC,CAAC;AACJ,CAAC;;AAED;AACA5D,MAAM,CAACiC,SAAS,CAAC0D,MAAM,GAAG,UAAUtD,KAAK,EAAE;EACzC,IAAI,IAAI,CAACK,YAAY,KAAKL,KAAK,EAAE;IAC/B,IAAI,CAAC3B,MAAM,CAACiF,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC;EACpC,CAAC,MAAM,IAAI,IAAI,CAAChF,WAAW,CAACuE,OAAO,CAAC7C,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE;IACjD,IAAI,CAAC1B,WAAW,CAACwE,MAAM,CAAC,IAAI,CAACxE,WAAW,CAACuE,OAAO,CAAC7C,KAAK,CAAC,EAAE,CAAC,CAAC;EAC7D;AACF,CAAC;AAEDrC,MAAM,CAACiC,SAAS,CAAC2D,GAAG,GAAG,YAAY,CAAC,CAAC;AACrC5F,MAAM,CAACiC,SAAS,CAAC4D,KAAK,GAAG,YAAY,CAAC,CAAC;AAEvC7F,MAAM,CAACiC,SAAS,CAAC6D,aAAa,GAAG,UAAUC,GAAG,EAAEC,MAAM,EAAEC,OAAO,EAAE;EAC/D,OAAO,IAAI,CAACzF,MAAM,CAACsF,aAAa,CAACC,GAAG,EAAEC,MAAM,EAAEC,OAAO,CAAC;AACxD,CAAC;AAEDjG,MAAM,CAACiC,SAAS,CAACiE,aAAa,GAAG,UAAUH,GAAG,EAAEC,MAAM,EAAE;EACtD,OAAO,IAAI,CAACxF,MAAM,CAAC0F,aAAa,CAACH,GAAG,EAAEC,MAAM,CAAC;AAC/C,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}