{"ast": null, "code": "/**\n * Following query was used to generate this file:\n\n SELECT json_object_agg(UPPER(PT.typname), PT.oid::int4 ORDER BY pt.oid)\n FROM pg_type PT\n WHERE typnamespace = (SELECT pgn.oid FROM pg_namespace pgn WHERE nspname = 'pg_catalog') -- Take only builting Postgres types with stable OID (extension types are not guaranted to be stable)\n AND typtype = 'b' -- Only basic types\n AND typelem = 0 -- Ignore aliases\n AND typisdefined -- Ignore undefined types\n */\n\nmodule.exports = {\n  BOOL: 16,\n  BYTEA: 17,\n  CHAR: 18,\n  INT8: 20,\n  INT2: 21,\n  INT4: 23,\n  REGPROC: 24,\n  TEXT: 25,\n  OID: 26,\n  TID: 27,\n  XID: 28,\n  CID: 29,\n  JSON: 114,\n  XML: 142,\n  PG_NODE_TREE: 194,\n  SMGR: 210,\n  PATH: 602,\n  POLYGON: 604,\n  CIDR: 650,\n  FLOAT4: 700,\n  FLOAT8: 701,\n  ABSTIME: 702,\n  RELTIME: 703,\n  TINTERVAL: 704,\n  CIRCLE: 718,\n  MACADDR8: 774,\n  MONEY: 790,\n  MACADDR: 829,\n  INET: 869,\n  ACLITEM: 1033,\n  BPCHAR: 1042,\n  VARCHAR: 1043,\n  DATE: 1082,\n  TIME: 1083,\n  TIMESTAMP: 1114,\n  TIMESTAMPTZ: 1184,\n  INTERVAL: 1186,\n  TIMETZ: 1266,\n  BIT: 1560,\n  VARBIT: 1562,\n  NUMERIC: 1700,\n  REFCURSOR: 1790,\n  REGPROCEDURE: 2202,\n  REGOPER: 2203,\n  REGOPERATOR: 2204,\n  REGCLASS: 2205,\n  REGTYPE: 2206,\n  UUID: 2950,\n  TXID_SNAPSHOT: 2970,\n  PG_LSN: 3220,\n  PG_NDISTINCT: 3361,\n  PG_DEPENDENCIES: 3402,\n  TSVECTOR: 3614,\n  TSQUERY: 3615,\n  GTSVECTOR: 3642,\n  REGCONFIG: 3734,\n  REGDICTIONARY: 3769,\n  JSONB: 3802,\n  REGNAMESPACE: 4089,\n  REGROLE: 4096\n};", "map": {"version": 3, "names": ["module", "exports", "BOOL", "BYTEA", "CHAR", "INT8", "INT2", "INT4", "REGPROC", "TEXT", "OID", "TID", "XID", "CID", "JSON", "XML", "PG_NODE_TREE", "SMGR", "PATH", "POLYGON", "CIDR", "FLOAT4", "FLOAT8", "ABSTIME", "RELTIME", "TINTERVAL", "CIRCLE", "MACADDR8", "MONEY", "MACADDR", "INET", "ACLITEM", "BPCHAR", "VARCHAR", "DATE", "TIME", "TIMESTAMP", "TIMESTAMPTZ", "INTERVAL", "TIMETZ", "BIT", "VARBIT", "NUMERIC", "REFCURSOR", "REGPROCEDURE", "REGOPER", "REGOPERATOR", "REGCLASS", "REGTYPE", "UUID", "TXID_SNAPSHOT", "PG_LSN", "PG_NDISTINCT", "PG_DEPENDENCIES", "TSVECTOR", "TSQUERY", "GTSVECTOR", "REGCONFIG", "REGDICTIONARY", "JSONB", "REGNAMESPACE", "REGROLE"], "sources": ["C:/tmsft/node_modules/pg-types/lib/builtins.js"], "sourcesContent": ["/**\n * Following query was used to generate this file:\n\n SELECT json_object_agg(UPPER(PT.typname), PT.oid::int4 ORDER BY pt.oid)\n FROM pg_type PT\n WHERE typnamespace = (SELECT pgn.oid FROM pg_namespace pgn WHERE nspname = 'pg_catalog') -- Take only builting Postgres types with stable OID (extension types are not guaranted to be stable)\n AND typtype = 'b' -- Only basic types\n AND typelem = 0 -- Ignore aliases\n AND typisdefined -- Ignore undefined types\n */\n\nmodule.exports = {\n    BOOL: 16,\n    BYTEA: 17,\n    CHAR: 18,\n    INT8: 20,\n    INT2: 21,\n    INT4: 23,\n    REGPROC: 24,\n    TEXT: 25,\n    OID: 26,\n    TID: 27,\n    XID: 28,\n    CID: 29,\n    JSON: 114,\n    XML: 142,\n    PG_NODE_TREE: 194,\n    SMGR: 210,\n    PATH: 602,\n    POLYGON: 604,\n    CIDR: 650,\n    FLOAT4: 700,\n    FLOAT8: 701,\n    ABSTIME: 702,\n    RELTIME: 703,\n    TINTERVAL: 704,\n    CIRCLE: 718,\n    MACADDR8: 774,\n    MONEY: 790,\n    MACADDR: 829,\n    INET: 869,\n    ACLITEM: 1033,\n    BPCHAR: 1042,\n    VARCHAR: 1043,\n    DATE: 1082,\n    TIME: 1083,\n    TIMESTAMP: 1114,\n    TIMESTAMPTZ: 1184,\n    INTERVAL: 1186,\n    TIMETZ: 1266,\n    BIT: 1560,\n    VARBIT: 1562,\n    NUMERIC: 1700,\n    REFCURSOR: 1790,\n    REGPROCEDURE: 2202,\n    REGOPER: 2203,\n    REGOPERATOR: 2204,\n    REGCLASS: 2205,\n    REGTYPE: 2206,\n    UUID: 2950,\n    TXID_SNAPSHOT: 2970,\n    PG_LSN: 3220,\n    PG_NDISTINCT: 3361,\n    PG_DEPENDENCIES: 3402,\n    TSVECTOR: 3614,\n    TSQUERY: 3615,\n    GTSVECTOR: 3642,\n    REGCONFIG: 3734,\n    REGDICTIONARY: 3769,\n    JSONB: 3802,\n    REGNAMESPACE: 4089,\n    REGROLE: 4096\n};\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEAA,MAAM,CAACC,OAAO,GAAG;EACbC,IAAI,EAAE,EAAE;EACRC,KAAK,EAAE,EAAE;EACTC,IAAI,EAAE,EAAE;EACRC,IAAI,EAAE,EAAE;EACRC,IAAI,EAAE,EAAE;EACRC,IAAI,EAAE,EAAE;EACRC,OAAO,EAAE,EAAE;EACXC,IAAI,EAAE,EAAE;EACRC,GAAG,EAAE,EAAE;EACPC,GAAG,EAAE,EAAE;EACPC,GAAG,EAAE,EAAE;EACPC,GAAG,EAAE,EAAE;EACPC,IAAI,EAAE,GAAG;EACTC,GAAG,EAAE,GAAG;EACRC,YAAY,EAAE,GAAG;EACjBC,IAAI,EAAE,GAAG;EACTC,IAAI,EAAE,GAAG;EACTC,OAAO,EAAE,GAAG;EACZC,IAAI,EAAE,GAAG;EACTC,MAAM,EAAE,GAAG;EACXC,MAAM,EAAE,GAAG;EACXC,OAAO,EAAE,GAAG;EACZC,OAAO,EAAE,GAAG;EACZC,SAAS,EAAE,GAAG;EACdC,MAAM,EAAE,GAAG;EACXC,QAAQ,EAAE,GAAG;EACbC,KAAK,EAAE,GAAG;EACVC,OAAO,EAAE,GAAG;EACZC,IAAI,EAAE,GAAG;EACTC,OAAO,EAAE,IAAI;EACbC,MAAM,EAAE,IAAI;EACZC,OAAO,EAAE,IAAI;EACbC,IAAI,EAAE,IAAI;EACVC,IAAI,EAAE,IAAI;EACVC,SAAS,EAAE,IAAI;EACfC,WAAW,EAAE,IAAI;EACjBC,QAAQ,EAAE,IAAI;EACdC,MAAM,EAAE,IAAI;EACZC,GAAG,EAAE,IAAI;EACTC,MAAM,EAAE,IAAI;EACZC,OAAO,EAAE,IAAI;EACbC,SAAS,EAAE,IAAI;EACfC,YAAY,EAAE,IAAI;EAClBC,OAAO,EAAE,IAAI;EACbC,WAAW,EAAE,IAAI;EACjBC,QAAQ,EAAE,IAAI;EACdC,OAAO,EAAE,IAAI;EACbC,IAAI,EAAE,IAAI;EACVC,aAAa,EAAE,IAAI;EACnBC,MAAM,EAAE,IAAI;EACZC,YAAY,EAAE,IAAI;EAClBC,eAAe,EAAE,IAAI;EACrBC,QAAQ,EAAE,IAAI;EACdC,OAAO,EAAE,IAAI;EACbC,SAAS,EAAE,IAAI;EACfC,SAAS,EAAE,IAAI;EACfC,aAAa,EAAE,IAAI;EACnBC,KAAK,EAAE,IAAI;EACXC,YAAY,EAAE,IAAI;EAClBC,OAAO,EAAE;AACb,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}