{"ast": null, "code": "/**\n * @license\n * Copyright 2018 Google LLC\n *\n * Use of this source code is governed by an MIT-style\n * license that can be found in the LICENSE file or at\n * https://opensource.org/licenses/MIT.\n * =============================================================================\n */\n/**\n * Executor: Evaluates SymbolicTensor based on feeds.\n */\nimport { cast, dispose, memory, util } from '@tensorflow/tfjs-core';\nimport { ValueError } from '../errors';\nimport { LruCache } from '../utils/executor_utils';\nimport { toList } from '../utils/generic_utils';\nimport { InputLayer } from './input_layer';\nimport { SymbolicTensor } from './topology';\n/**\n * Helper function to check the dtype and shape compatibility of a feed value.\n */\nfunction assertFeedCompatibility(key, val) {\n  // Check dtype compatibility.\n  if (key.dtype == null || key.dtype === val.dtype) {\n    //  a.  If types match, return val tensor as is.\n    return val;\n  }\n  try {\n    //  b. Attempt to convert to expected type.\n    return cast(val, key.dtype);\n  } catch (err) {\n    //  c. If conversion fails, return helpful error.\n    throw new ValueError(`The dtype of the feed (${val.dtype}) can not be cast to the dtype ` + `of the key '${key.name}' (${key.dtype}).`);\n  }\n}\n/**\n * FeedDict: A mapping from unique SymbolicTensors to feed values for them.\n * A feed value is a concrete value represented as an `Tensor`.\n */\nexport class FeedDict {\n  /**\n   * Constructor, optionally does copy-construction.\n   * @param feeds An Array of `Feed`s, or another `FeedDict`, in which case\n   *   copy-construction will be performed.\n   */\n  constructor(feeds) {\n    this.id2Value = {};\n    this.id2Mask = {};\n    this.name2Id = {};\n    if (feeds instanceof FeedDict) {\n      for (const id in feeds.id2Value) {\n        this.id2Value[id] = feeds.id2Value[id];\n        if (id in feeds.id2Mask) {\n          this.id2Mask[id] = feeds.id2Mask[id];\n        }\n      }\n    } else {\n      if (feeds == null) {\n        return;\n      }\n      for (const feed of feeds) {\n        this.add(feed.key, feed.value);\n      }\n    }\n  }\n  /**\n   * Add a key-value pair to the FeedDict.\n   *\n   * @param key The key of the feed.\n   * @param value The value of the tensor feed.\n   * @param mask The value of the mask feed (optional).\n   * @returns This `FeedDict`.\n   * @throws ValueError: If the key `SymbolicTensor` already exists in the\n   *   `FeedDict`.\n   */\n  add(key, value, mask) {\n    if (this.id2Value[key.id] == null) {\n      this.id2Value[key.id] = assertFeedCompatibility(key, value);\n      this.name2Id[key.name] = key.id;\n      if (mask != null) {\n        this.id2Mask[key.id] = mask;\n      }\n    } else {\n      throw new ValueError(`Duplicate key: name=${key.name}, id=${key.id}`);\n    }\n    return this;\n  }\n  /**\n   * Add a Feed to the FeedDict.\n   * @param feed The new `Feed` to add.\n   * @returns This `FeedDict`.\n   */\n  addFeed(feed) {\n    this.add(feed.key, feed.value);\n  }\n  /**\n   * Probe whether a key already exists in the FeedDict.\n   * @param key\n   */\n  hasKey(key) {\n    return this.id2Value[key.id] != null;\n  }\n  /**\n   * Get all the SymbolicTensor available in this FeedDict.\n   */\n  names() {\n    return Object.keys(this.name2Id);\n  }\n  /**\n   * Get the feed value for given key.\n   * @param key The SymbolicTensor, or its name (as a string), of which the\n   *     value is sought.\n   * @returns If `key` exists, the corresponding feed value.\n   * @throws ValueError: If `key` does not exist in this `FeedDict`.\n   */\n  getValue(key) {\n    if (key instanceof SymbolicTensor) {\n      if (this.id2Value[key.id] == null) {\n        throw new ValueError(`Nonexistent key: ${key.name}`);\n      } else {\n        return this.id2Value[key.id];\n      }\n    } else {\n      const id = this.name2Id[key];\n      if (id == null) {\n        throw new ValueError(`Feed dict has no SymbolicTensor name: ${key}`);\n      }\n      return this.id2Value[id];\n    }\n  }\n  /**\n   * Get the feed mask for given key.\n   * @param key The SymbolicTensor, or its name (as a string), of which the\n   *     value is sought.\n   * @returns If `key` exists, the corresponding feed mask.\n   * @throws ValueError: If `key` does not exist in this `FeedDict`.\n   */\n  getMask(key) {\n    if (key instanceof SymbolicTensor) {\n      if (this.id2Value[key.id] == null) {\n        throw new ValueError(`Nonexistent key: ${key.name}`);\n      } else {\n        return this.id2Mask[key.id];\n      }\n    } else {\n      const id = this.name2Id[key];\n      if (id == null) {\n        throw new ValueError(`Feed dict has no SymbolicTensor name: ${key}`);\n      }\n      return this.id2Mask[id];\n    }\n  }\n  /** Dispose all mask Tensors held by this object. */\n  disposeMasks() {\n    if (this.id2Mask != null) {\n      dispose(this.id2Mask);\n    }\n  }\n}\n// Cache for topologically sorted SymbolicTensors for given execution\n// targets (i.e., fetches).\nexport const cachedSorted = new LruCache();\n// Cache for recipient count maps for given execution targets (i.e., fetches).\nexport const cachedRecipientCounts = new LruCache();\nexport function updateCacheMaxEntries(maxEntries) {\n  if (cachedSorted != null) {\n    cachedSorted.setMaxEntries(maxEntries);\n  }\n  if (cachedRecipientCounts != null) {\n    cachedRecipientCounts.setMaxEntries(maxEntries);\n  }\n}\n/**\n * Execute a SymbolicTensor by using concrete feed values.\n *\n * A `SymbolicTensor` object is a node in a computation graph of TF.js\n * Layers. The object is backed by a source layer and input\n * `SymbolicTensor`s to the source layer. This method evaluates\n * the `call()` method of the source layer, using concrete values of the\n * inputs obtained from either\n * * `feedDict`, if the input key exists in `feedDict`, or else,\n * * a recursive call to `execute()` itself.\n *\n * @param x: The `SymbolicTensor` to execute.\n * @param feedDict: The feed values, as base condition of the recursion.\n *   execution.\n * @param kwargs: Optional keyword arguments.\n * @param probe: A probe object (of interface `ExecutionProbe`) used for\n *   testing memory footprint of `execute` calls.\n * @returns Result of the execution.\n * @throws ValueError: If any `SymbolicTensor`s from `InputLayer`s\n *   encountered during the execution lacks a feed value in `feedDict`.\n */\nexport function execute(fetches, feedDict, kwargs, probe) {\n  const training = kwargs == null ? false : kwargs['training'];\n  const arrayFetches = Array.isArray(fetches);\n  const fetchArray = arrayFetches ? fetches : [fetches];\n  const outputNames = fetchArray.map(t => t.name);\n  const finalOutputs = [];\n  const feedNames = feedDict.names();\n  for (const outputName of outputNames) {\n    if (feedNames.indexOf(outputName) !== -1) {\n      finalOutputs.push(feedDict.getValue(outputName));\n    } else {\n      finalOutputs.push(null);\n    }\n  }\n  if (probe != null) {\n    // For optional probing of memory footprint during execution.\n    probe.maxNumTensors = -Infinity;\n    probe.minNumTensors = Infinity;\n  }\n  // Check cache.\n  const fetchAndFeedKey = outputNames.join(',') + '|' + feedDict.names().sort().join(',');\n  let sorted = cachedSorted.get(fetchAndFeedKey);\n  let recipientCounts;\n  if (sorted == null) {\n    // Cache doesn't contain the desired combination of fetches. Compute\n    // topological sort for the combination for the first time.\n    const out = getTopologicalSortAndRecipientCounts(fetchArray, feedDict);\n    sorted = out.sorted;\n    recipientCounts = out.recipientCounts;\n    // Store results in cache for future use.\n    cachedSorted.put(fetchAndFeedKey, sorted);\n    cachedRecipientCounts.put(fetchAndFeedKey, recipientCounts);\n  }\n  recipientCounts = {};\n  if (!training) {\n    Object.assign(recipientCounts, cachedRecipientCounts.get(fetchAndFeedKey));\n  }\n  const internalFeedDict = new FeedDict(feedDict);\n  // Start iterative execution on the topologically-sorted SymbolicTensors.\n  for (let i = 0; i < sorted.length; ++i) {\n    if (probe != null) {\n      // For optional probing of memory usage during execution.\n      const numTensors = memory().numTensors;\n      if (numTensors > probe.maxNumTensors) {\n        probe.maxNumTensors = numTensors;\n      }\n      if (numTensors < probe.minNumTensors) {\n        probe.minNumTensors = numTensors;\n      }\n    }\n    const symbolic = sorted[i];\n    const srcLayer = symbolic.sourceLayer;\n    if (srcLayer instanceof InputLayer) {\n      continue;\n    }\n    const inputValues = [];\n    const inputMasks = [];\n    const tensorsToDispose = [];\n    let maskExists = false;\n    for (const input of symbolic.inputs) {\n      const value = internalFeedDict.getValue(input);\n      const mask = internalFeedDict.getMask(input);\n      inputValues.push(value);\n      inputMasks.push(mask);\n      if (mask != null) {\n        maskExists = true;\n      }\n      if (!training) {\n        recipientCounts[input.name]--;\n        if (recipientCounts[input.name] === 0 && !feedDict.hasKey(input) && outputNames.indexOf(input.name) === -1 && !value.isDisposed && input.sourceLayer.stateful !== true) {\n          tensorsToDispose.push(value);\n        }\n      }\n    }\n    if (maskExists) {\n      kwargs = kwargs || {};\n      kwargs['mask'] = inputMasks[0];\n    }\n    const outputTensors = toList(srcLayer.apply(inputValues, kwargs));\n    let outputMask = null;\n    if (srcLayer.supportsMasking) {\n      outputMask = srcLayer.computeMask(inputValues, inputMasks);\n    }\n    const layerOutputs = getNodeOutputs(symbolic);\n    const outputSymbolicTensors = Array.isArray(layerOutputs) ? layerOutputs : [layerOutputs];\n    for (let i = 0; i < outputSymbolicTensors.length; ++i) {\n      if (!internalFeedDict.hasKey(outputSymbolicTensors[i])) {\n        internalFeedDict.add(outputSymbolicTensors[i], outputTensors[i], Array.isArray(outputMask) ? outputMask[0] : outputMask);\n      }\n      const index = outputNames.indexOf(outputSymbolicTensors[i].name);\n      if (index !== -1) {\n        finalOutputs[index] = outputTensors[i];\n      }\n    }\n    if (!training) {\n      // Clean up Tensors that are no longer needed.\n      dispose(tensorsToDispose);\n    }\n  }\n  // NOTE(cais): Unlike intermediate tensors, we don't discard mask\n  // tensors as we go, because these tensors are sometimes passed over a\n  // series of mutliple layers, i.e., not obeying the immediate input\n  // relations in the graph. If this becomes a memory-usage concern,\n  // we can improve this in the future.\n  internalFeedDict.disposeMasks();\n  return arrayFetches ? finalOutputs : finalOutputs[0];\n}\n/**\n * Sort the `SymbolicTensor`s topologically, for an array of fetches.\n *\n * This function calls getTopologicalSortAndRecipientCountsForOneFetch and\n * merges their results.\n *\n * @param fetch The array of fetches requested. Must be a non-empty array.\n * @param feedDict The dictionary of fed values.\n * @returns sorted: Topologically-sorted array of SymbolicTensors.\n *   recipientCounts: Recipient counts for all SymbolicTensors in `sorted`.\n */\nfunction getTopologicalSortAndRecipientCounts(fetches, feedDict) {\n  util.assert(fetches != null && fetches.length > 0, () => `Expected at least one fetch, got none`);\n  let finalSorted = [];\n  let finalRecipientMap = {};\n  if (fetches.length === 1) {\n    // Special-casing 1 fetch for efficiency.\n    const out = getTopologicalSortAndRecipientCountsForOneFetch(fetches[0], feedDict);\n    finalSorted = out.sorted;\n    finalRecipientMap = out.recipientMap;\n  } else {\n    const visited = new Set();\n    for (const fetch of fetches) {\n      const {\n        sorted,\n        recipientMap\n      } = getTopologicalSortAndRecipientCountsForOneFetch(fetch, feedDict);\n      // Merge sorted SymbolicTensor Arrays.\n      for (const symbolicTensor of sorted) {\n        if (!visited.has(symbolicTensor.name)) {\n          finalSorted.push(symbolicTensor);\n          visited.add(symbolicTensor.name);\n        }\n      }\n      // Merge recipient maps.\n      for (const name in recipientMap) {\n        if (finalRecipientMap[name] == null) {\n          finalRecipientMap[name] = new Set();\n        }\n        recipientMap[name].forEach(recipient => finalRecipientMap[name].add(recipient));\n      }\n    }\n  }\n  return {\n    sorted: finalSorted,\n    recipientCounts: recipientMap2Counts(finalRecipientMap)\n  };\n}\nfunction recipientMap2Counts(recipientMap) {\n  const recipientCounts = {};\n  for (const name in recipientMap) {\n    recipientCounts[name] = recipientMap[name].size;\n  }\n  return recipientCounts;\n}\n/**\n * Sort the `SymbolicTensor`s topologically, for a single fetch.\n *\n * This helper function processes the upstream SymbolicTensors of a single\n * fetch.\n *\n * @param fetch The single fetch requested.\n * @param feedDict The dictionary of fed values.\n * @returns sorted: Topologically-sorted array of SymbolicTensors.\n *   recipientMap: Recipient names for all SymbolicTensors in `sorted`.\n */\nexport function getTopologicalSortAndRecipientCountsForOneFetch(fetch, feedDict) {\n  const visited = new Set();\n  const sorted = [];\n  const recipientMap = {};\n  // Put keys of the feedDict into visited first, so they don't have to be\n  // walked. This is needed in case where there are feeds for intermediate\n  // SymbolicTensors of the graph.\n  for (const key of feedDict.names()) {\n    visited.add(key);\n  }\n  const stack = [];\n  const marks = [];\n  // Initial population of stack and marks.\n  stack.push(fetch);\n  while (stack.length > 0) {\n    const top = stack[stack.length - 1];\n    if (visited.has(top.name)) {\n      stack.pop();\n      continue;\n    }\n    const topIsMarked = marks[marks.length - 1] === stack.length - 1;\n    if (top.inputs.length === 0 || topIsMarked) {\n      // Input SymbolicTensor or all children have been visited.\n      stack.pop();\n      sorted.push(top);\n      visited.add(top.name);\n      if (topIsMarked) {\n        marks.pop();\n      }\n    } else {\n      // A non-input SymbolicTensor whose upstream SymbolicTensors haven't\n      // been visited yet. Push them onto the stack.\n      marks.push(stack.length - 1);\n      for (const input of top.inputs) {\n        // Increment the recipient count. Note that this needs to happen\n        // regardless of whether the SymbolicTensor has been visited before.\n        if (recipientMap[input.name] == null) {\n          recipientMap[input.name] = new Set();\n        }\n        recipientMap[input.name].add(top.name);\n        if (visited.has(input.name)) {\n          continue; // Avoid repeated visits to the same SymbolicTensor.\n        }\n        stack.push(input);\n      }\n    }\n  }\n  return {\n    sorted,\n    recipientMap\n  };\n}\n/**\n * Get the symbolic output tensors of the node to which a given fetch belongs.\n * @param fetch The fetched symbolic tensor.\n * @returns The Array of symbolic tensors output by the node to which `fetch`\n *   belongs.\n */\nfunction getNodeOutputs(fetch) {\n  let layerOutputs;\n  if (fetch.sourceLayer.inboundNodes.length === 1) {\n    layerOutputs = fetch.sourceLayer.output;\n  } else {\n    let nodeIndex = null;\n    for (let i = 0; i < fetch.sourceLayer.inboundNodes.length; ++i) {\n      for (const outputTensor of fetch.sourceLayer.inboundNodes[i].outputTensors) {\n        if (outputTensor.id === fetch.id) {\n          nodeIndex = i;\n          break;\n        }\n      }\n    }\n    layerOutputs = fetch.sourceLayer.getOutputAt(nodeIndex);\n  }\n  return layerOutputs;\n}", "map": {"version": 3, "names": ["cast", "dispose", "memory", "util", "ValueError", "<PERSON><PERSON><PERSON><PERSON>", "toList", "InputLayer", "SymbolicTensor", "assertFeedCompatibility", "key", "val", "dtype", "err", "name", "FeedDict", "constructor", "feeds", "id2Value", "id2Mask", "name2Id", "id", "feed", "add", "value", "mask", "addFeed", "<PERSON><PERSON><PERSON>", "names", "Object", "keys", "getValue", "getMask", "disposeMasks", "cachedSorted", "cachedRecipientCounts", "updateCacheMaxEntries", "maxEntries", "setMaxEntries", "execute", "fetches", "feedDict", "kwargs", "probe", "training", "arrayFetches", "Array", "isArray", "fetchArray", "outputNames", "map", "t", "finalOutputs", "feedNames", "outputName", "indexOf", "push", "maxNumTensors", "Infinity", "minNumTensors", "fetchAndFeedKey", "join", "sort", "sorted", "get", "recipientCounts", "out", "getTopologicalSortAndRecipientCounts", "put", "assign", "internalFeedDict", "i", "length", "numTensors", "symbolic", "src<PERSON><PERSON><PERSON>", "sourceLayer", "inputValues", "inputMasks", "tensorsToDispose", "maskExists", "input", "inputs", "isDisposed", "stateful", "outputTensors", "apply", "outputMask", "supportsMasking", "computeMask", "layerOutputs", "getNodeOutputs", "outputSymbolicTensors", "index", "assert", "finalSorted", "finalRecipientMap", "getTopologicalSortAndRecipientCountsForOneFetch", "recipientMap", "visited", "Set", "fetch", "symbolicTensor", "has", "for<PERSON>ach", "recipient", "recipientMap2Counts", "size", "stack", "marks", "top", "pop", "topIsMarked", "inboundNodes", "output", "nodeIndex", "outputTensor", "getOutputAt"], "sources": ["C:\\tfjs-layers\\src\\engine\\executor.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2018 Google LLC\n *\n * Use of this source code is governed by an MIT-style\n * license that can be found in the LICENSE file or at\n * https://opensource.org/licenses/MIT.\n * =============================================================================\n */\n\n/**\n * Executor: Evaluates SymbolicTensor based on feeds.\n */\n\nimport {cast, dispose, memory, Tensor, util} from '@tensorflow/tfjs-core';\n\nimport {ValueError} from '../errors';\nimport {Kwargs} from '../types';\nimport {LruCache} from '../utils/executor_utils';\nimport {toList} from '../utils/generic_utils';\n\nimport {InputLayer} from './input_layer';\nimport {SymbolicTensor} from './topology';\n\n/**\n * Helper function to check the dtype and shape compatibility of a feed value.\n */\nfunction assertFeedCompatibility(key: SymbolicTensor, val: Tensor): Tensor {\n  // Check dtype compatibility.\n  if (key.dtype == null || key.dtype === val.dtype) {\n    //  a.  If types match, return val tensor as is.\n    return val;\n  }\n  try {\n    //  b. Attempt to convert to expected type.\n    return cast(val, key.dtype);\n  } catch (err) {\n    //  c. If conversion fails, return helpful error.\n    throw new ValueError(\n        `The dtype of the feed (${val.dtype}) can not be cast to the dtype ` +\n        `of the key '${key.name}' (${key.dtype}).`);\n  }\n}\n\n/**\n * A concrete Tensor value for a symbolic tensor as the key.\n */\nexport interface Feed {\n  key: SymbolicTensor;\n  value: Tensor;\n}\n\n/**\n * FeedDict: A mapping from unique SymbolicTensors to feed values for them.\n * A feed value is a concrete value represented as an `Tensor`.\n */\nexport class FeedDict {\n  private id2Value: {[id: number]: Tensor} = {};\n  private id2Mask: {[id: number]: Tensor} = {};\n  private name2Id: {[name: string]: number} = {};\n\n  /**\n   * Constructor, optionally does copy-construction.\n   * @param feeds An Array of `Feed`s, or another `FeedDict`, in which case\n   *   copy-construction will be performed.\n   */\n  constructor(feeds?: Feed[]|FeedDict) {\n    if (feeds instanceof FeedDict) {\n      for (const id in feeds.id2Value) {\n        this.id2Value[id] = feeds.id2Value[id];\n        if (id in feeds.id2Mask) {\n          this.id2Mask[id] = feeds.id2Mask[id];\n        }\n      }\n    } else {\n      if (feeds == null) {\n        return;\n      }\n      for (const feed of feeds) {\n        this.add(feed.key, feed.value);\n      }\n    }\n  }\n\n  /**\n   * Add a key-value pair to the FeedDict.\n   *\n   * @param key The key of the feed.\n   * @param value The value of the tensor feed.\n   * @param mask The value of the mask feed (optional).\n   * @returns This `FeedDict`.\n   * @throws ValueError: If the key `SymbolicTensor` already exists in the\n   *   `FeedDict`.\n   */\n  add(key: SymbolicTensor, value: Tensor, mask?: Tensor): FeedDict {\n    if (this.id2Value[key.id] == null) {\n      this.id2Value[key.id] = assertFeedCompatibility(key, value);\n      this.name2Id[key.name] = key.id;\n      if (mask != null) {\n        this.id2Mask[key.id] = mask;\n      }\n    } else {\n      throw new ValueError(`Duplicate key: name=${key.name}, id=${key.id}`);\n    }\n    return this;\n  }\n\n  /**\n   * Add a Feed to the FeedDict.\n   * @param feed The new `Feed` to add.\n   * @returns This `FeedDict`.\n   */\n  addFeed(feed: Feed) {\n    this.add(feed.key, feed.value);\n  }\n\n  /**\n   * Probe whether a key already exists in the FeedDict.\n   * @param key\n   */\n  hasKey(key: SymbolicTensor): boolean {\n    return this.id2Value[key.id] != null;\n  }\n\n  /**\n   * Get all the SymbolicTensor available in this FeedDict.\n   */\n  names(): string[] {\n    return Object.keys(this.name2Id);\n  }\n\n  /**\n   * Get the feed value for given key.\n   * @param key The SymbolicTensor, or its name (as a string), of which the\n   *     value is sought.\n   * @returns If `key` exists, the corresponding feed value.\n   * @throws ValueError: If `key` does not exist in this `FeedDict`.\n   */\n  getValue(key: SymbolicTensor|string): Tensor {\n    if (key instanceof SymbolicTensor) {\n      if (this.id2Value[key.id] == null) {\n        throw new ValueError(`Nonexistent key: ${key.name}`);\n      } else {\n        return this.id2Value[key.id];\n      }\n    } else {\n      const id = this.name2Id[key];\n      if (id == null) {\n        throw new ValueError(`Feed dict has no SymbolicTensor name: ${key}`);\n      }\n      return this.id2Value[id];\n    }\n  }\n\n  /**\n   * Get the feed mask for given key.\n   * @param key The SymbolicTensor, or its name (as a string), of which the\n   *     value is sought.\n   * @returns If `key` exists, the corresponding feed mask.\n   * @throws ValueError: If `key` does not exist in this `FeedDict`.\n   */\n  getMask(key: SymbolicTensor|string): Tensor {\n    if (key instanceof SymbolicTensor) {\n      if (this.id2Value[key.id] == null) {\n        throw new ValueError(`Nonexistent key: ${key.name}`);\n      } else {\n        return this.id2Mask[key.id];\n      }\n    } else {\n      const id = this.name2Id[key];\n      if (id == null) {\n        throw new ValueError(`Feed dict has no SymbolicTensor name: ${key}`);\n      }\n      return this.id2Mask[id];\n    }\n  }\n\n  /** Dispose all mask Tensors held by this object. */\n  disposeMasks() {\n    if (this.id2Mask != null) {\n      dispose(this.id2Mask);\n    }\n  }\n}\n\n// Cache for topologically sorted SymbolicTensors for given execution\n// targets (i.e., fetches).\nexport const cachedSorted: LruCache<SymbolicTensor[]> =\n    new LruCache<SymbolicTensor[]>();\n\n// Cache for recipient count maps for given execution targets (i.e., fetches).\nexport const cachedRecipientCounts: LruCache<RecipientCounts> =\n    new LruCache<RecipientCounts>();\n\nexport function updateCacheMaxEntries(maxEntries: number) {\n  if (cachedSorted != null) {\n    cachedSorted.setMaxEntries(maxEntries);\n  }\n  if (cachedRecipientCounts != null) {\n    cachedRecipientCounts.setMaxEntries(maxEntries);\n  }\n}\n\n/**\n * Interface for the optional object used for probing the memory\n * usage and other statistics during execution.\n */\nexport interface ExecutionProbe {\n  /**\n   * Maximum number of tensors that exist during all steps of the\n   * execution. Tensor counts are measured at the beginning of every\n   * step.\n   */\n  maxNumTensors?: number;\n\n  /**\n   * Minimum number of tensors that exist during all steps of the\n   * execution. Tensor counts are measured at the beginning of every\n   * step.\n   */\n  minNumTensors?: number;\n}\n\n/**\n * Execute a SymbolicTensor by using concrete feed values.\n *\n * A `SymbolicTensor` object is a node in a computation graph of TF.js\n * Layers. The object is backed by a source layer and input\n * `SymbolicTensor`s to the source layer. This method evaluates\n * the `call()` method of the source layer, using concrete values of the\n * inputs obtained from either\n * * `feedDict`, if the input key exists in `feedDict`, or else,\n * * a recursive call to `execute()` itself.\n *\n * @param x: The `SymbolicTensor` to execute.\n * @param feedDict: The feed values, as base condition of the recursion.\n *   execution.\n * @param kwargs: Optional keyword arguments.\n * @param probe: A probe object (of interface `ExecutionProbe`) used for\n *   testing memory footprint of `execute` calls.\n * @returns Result of the execution.\n * @throws ValueError: If any `SymbolicTensor`s from `InputLayer`s\n *   encountered during the execution lacks a feed value in `feedDict`.\n */\nexport function execute(\n    fetches: SymbolicTensor|SymbolicTensor[], feedDict: FeedDict,\n    kwargs?: Kwargs, probe?: ExecutionProbe): Tensor|\n    Tensor[]|[Tensor | Tensor[]] {\n  const training: boolean = kwargs == null ? false : kwargs['training'];\n\n  const arrayFetches = Array.isArray(fetches);\n  const fetchArray: SymbolicTensor[] =\n      arrayFetches ? fetches : [fetches];\n\n  const outputNames = fetchArray.map(t => t.name);\n  const finalOutputs: Tensor[] = [];\n  const feedNames = feedDict.names();\n  for (const outputName of outputNames) {\n    if (feedNames.indexOf(outputName) !== -1) {\n      finalOutputs.push(feedDict.getValue(outputName));\n    } else {\n      finalOutputs.push(null);\n    }\n  }\n\n  if (probe != null) {\n    // For optional probing of memory footprint during execution.\n    probe.maxNumTensors = -Infinity;\n    probe.minNumTensors = Infinity;\n  }\n\n  // Check cache.\n  const fetchAndFeedKey =\n      outputNames.join(',') + '|' + feedDict.names().sort().join(',');\n  let sorted: SymbolicTensor[] = cachedSorted.get(fetchAndFeedKey);\n  let recipientCounts: {[fetchName: string]: number};\n  if (sorted == null) {\n    // Cache doesn't contain the desired combination of fetches. Compute\n    // topological sort for the combination for the first time.\n    const out = getTopologicalSortAndRecipientCounts(fetchArray, feedDict);\n    sorted = out.sorted;\n    recipientCounts = out.recipientCounts;\n\n    // Store results in cache for future use.\n    cachedSorted.put(fetchAndFeedKey, sorted);\n    cachedRecipientCounts.put(fetchAndFeedKey, recipientCounts);\n  }\n  recipientCounts = {};\n  if (!training) {\n    Object.assign(recipientCounts, cachedRecipientCounts.get(fetchAndFeedKey));\n  }\n\n  const internalFeedDict = new FeedDict(feedDict);\n\n  // Start iterative execution on the topologically-sorted SymbolicTensors.\n  for (let i = 0; i < sorted.length; ++i) {\n    if (probe != null) {\n      // For optional probing of memory usage during execution.\n      const numTensors = memory().numTensors;\n      if (numTensors > probe.maxNumTensors) {\n        probe.maxNumTensors = numTensors;\n      }\n      if (numTensors < probe.minNumTensors) {\n        probe.minNumTensors = numTensors;\n      }\n    }\n\n    const symbolic = sorted[i];\n    const srcLayer = symbolic.sourceLayer;\n    if (srcLayer instanceof InputLayer) {\n      continue;\n    }\n    const inputValues: Tensor[] = [];\n    const inputMasks: Tensor[] = [];\n    const tensorsToDispose: Tensor[] = [];\n\n    let maskExists = false;\n    for (const input of symbolic.inputs) {\n      const value = internalFeedDict.getValue(input);\n      const mask = internalFeedDict.getMask(input);\n      inputValues.push(value);\n      inputMasks.push(mask);\n      if (mask != null) {\n        maskExists = true;\n      }\n      if (!training) {\n        recipientCounts[input.name]--;\n        if (recipientCounts[input.name] === 0 && !feedDict.hasKey(input) &&\n            outputNames.indexOf(input.name) === -1 && !value.isDisposed &&\n            input.sourceLayer.stateful !== true) {\n          tensorsToDispose.push(value);\n        }\n      }\n    }\n\n    if (maskExists) {\n      kwargs = kwargs || {};\n      kwargs['mask'] = inputMasks[0];\n    }\n    const outputTensors =\n        toList(srcLayer.apply(inputValues, kwargs)) as Tensor[];\n    let outputMask: Tensor|Tensor[] = null;\n    if (srcLayer.supportsMasking) {\n      outputMask = srcLayer.computeMask(inputValues, inputMasks);\n    }\n    const layerOutputs = getNodeOutputs(symbolic);\n    const outputSymbolicTensors =\n        Array.isArray(layerOutputs) ? layerOutputs : [layerOutputs];\n    for (let i = 0; i < outputSymbolicTensors.length; ++i) {\n      if (!internalFeedDict.hasKey(outputSymbolicTensors[i])) {\n        internalFeedDict.add(\n            outputSymbolicTensors[i], outputTensors[i],\n            Array.isArray(outputMask) ? outputMask[0] : outputMask);\n      }\n      const index = outputNames.indexOf(outputSymbolicTensors[i].name);\n      if (index !== -1) {\n        finalOutputs[index] = outputTensors[i];\n      }\n    }\n\n    if (!training) {\n      // Clean up Tensors that are no longer needed.\n      dispose(tensorsToDispose);\n    }\n  }\n  // NOTE(cais): Unlike intermediate tensors, we don't discard mask\n  // tensors as we go, because these tensors are sometimes passed over a\n  // series of mutliple layers, i.e., not obeying the immediate input\n  // relations in the graph. If this becomes a memory-usage concern,\n  // we can improve this in the future.\n  internalFeedDict.disposeMasks();\n\n  return arrayFetches ? finalOutputs : finalOutputs[0];\n}\n\ntype RecipientCounts = {\n  [fetchName: string]: number\n};\n\nexport type RecipientMap = {\n  [fetchName: string]: Set<string>;\n};\n\n/**\n * Sort the `SymbolicTensor`s topologically, for an array of fetches.\n *\n * This function calls getTopologicalSortAndRecipientCountsForOneFetch and\n * merges their results.\n *\n * @param fetch The array of fetches requested. Must be a non-empty array.\n * @param feedDict The dictionary of fed values.\n * @returns sorted: Topologically-sorted array of SymbolicTensors.\n *   recipientCounts: Recipient counts for all SymbolicTensors in `sorted`.\n */\nfunction getTopologicalSortAndRecipientCounts(\n    fetches: SymbolicTensor[], feedDict: FeedDict):\n    {sorted: SymbolicTensor[], recipientCounts: RecipientCounts} {\n  util.assert(\n      fetches != null && fetches.length > 0,\n      () => `Expected at least one fetch, got none`);\n\n  let finalSorted: SymbolicTensor[] = [];\n  let finalRecipientMap: RecipientMap = {};\n  if (fetches.length === 1) {\n    // Special-casing 1 fetch for efficiency.\n    const out =\n        getTopologicalSortAndRecipientCountsForOneFetch(fetches[0], feedDict);\n    finalSorted = out.sorted;\n    finalRecipientMap = out.recipientMap;\n  } else {\n    const visited = new Set<string>();\n    for (const fetch of fetches) {\n      const {sorted, recipientMap} =\n          getTopologicalSortAndRecipientCountsForOneFetch(fetch, feedDict);\n\n      // Merge sorted SymbolicTensor Arrays.\n      for (const symbolicTensor of sorted) {\n        if (!visited.has(symbolicTensor.name)) {\n          finalSorted.push(symbolicTensor);\n          visited.add(symbolicTensor.name);\n        }\n      }\n\n      // Merge recipient maps.\n      for (const name in recipientMap) {\n        if (finalRecipientMap[name] == null) {\n          finalRecipientMap[name] = new Set<string>();\n        }\n        recipientMap[name].forEach(\n            recipient => finalRecipientMap[name].add(recipient));\n      }\n    }\n  }\n  return {\n    sorted: finalSorted,\n    recipientCounts: recipientMap2Counts(finalRecipientMap)\n  };\n}\n\nfunction recipientMap2Counts(recipientMap: RecipientMap): RecipientCounts {\n  const recipientCounts: RecipientCounts = {};\n  for (const name in recipientMap) {\n    recipientCounts[name] = recipientMap[name].size;\n  }\n  return recipientCounts;\n}\n\n/**\n * Sort the `SymbolicTensor`s topologically, for a single fetch.\n *\n * This helper function processes the upstream SymbolicTensors of a single\n * fetch.\n *\n * @param fetch The single fetch requested.\n * @param feedDict The dictionary of fed values.\n * @returns sorted: Topologically-sorted array of SymbolicTensors.\n *   recipientMap: Recipient names for all SymbolicTensors in `sorted`.\n */\nexport function getTopologicalSortAndRecipientCountsForOneFetch(\n    fetch: SymbolicTensor, feedDict: FeedDict):\n    {sorted: SymbolicTensor[], recipientMap: RecipientMap} {\n  const visited = new Set<string>();\n  const sorted: SymbolicTensor[] = [];\n  const recipientMap: RecipientMap = {};\n\n  // Put keys of the feedDict into visited first, so they don't have to be\n  // walked. This is needed in case where there are feeds for intermediate\n  // SymbolicTensors of the graph.\n  for (const key of feedDict.names()) {\n    visited.add(key);\n  }\n\n  const stack: SymbolicTensor[] = [];\n  const marks: number[] = [];\n\n  // Initial population of stack and marks.\n  stack.push(fetch);\n\n  while (stack.length > 0) {\n    const top = stack[stack.length - 1];\n    if (visited.has(top.name)) {\n      stack.pop();\n      continue;\n    }\n    const topIsMarked = marks[marks.length - 1] === stack.length - 1;\n    if (top.inputs.length === 0 || topIsMarked) {\n      // Input SymbolicTensor or all children have been visited.\n      stack.pop();\n      sorted.push(top);\n      visited.add(top.name);\n      if (topIsMarked) {\n        marks.pop();\n      }\n    } else {\n      // A non-input SymbolicTensor whose upstream SymbolicTensors haven't\n      // been visited yet. Push them onto the stack.\n      marks.push(stack.length - 1);\n      for (const input of top.inputs) {\n        // Increment the recipient count. Note that this needs to happen\n        // regardless of whether the SymbolicTensor has been visited before.\n        if (recipientMap[input.name] == null) {\n          recipientMap[input.name] = new Set<string>();\n        }\n        recipientMap[input.name].add(top.name);\n\n        if (visited.has(input.name)) {\n          continue;  // Avoid repeated visits to the same SymbolicTensor.\n        }\n        stack.push(input);\n      }\n    }\n  }\n  return {sorted, recipientMap};\n}\n\n/**\n * Get the symbolic output tensors of the node to which a given fetch belongs.\n * @param fetch The fetched symbolic tensor.\n * @returns The Array of symbolic tensors output by the node to which `fetch`\n *   belongs.\n */\nfunction getNodeOutputs(fetch: SymbolicTensor): SymbolicTensor|\n    SymbolicTensor[] {\n  let layerOutputs: SymbolicTensor|SymbolicTensor[];\n  if (fetch.sourceLayer.inboundNodes.length === 1) {\n    layerOutputs = fetch.sourceLayer.output;\n  } else {\n    let nodeIndex: number = null;\n    for (let i = 0; i < fetch.sourceLayer.inboundNodes.length; ++i) {\n      for (const outputTensor of fetch.sourceLayer.inboundNodes[i]\n               .outputTensors) {\n        if (outputTensor.id === fetch.id) {\n          nodeIndex = i;\n          break;\n        }\n      }\n    }\n    layerOutputs = fetch.sourceLayer.getOutputAt(nodeIndex);\n  }\n  return layerOutputs;\n}\n"], "mappings": "AAAA;;;;;;;;;AAUA;;;AAIA,SAAQA,IAAI,EAAEC,OAAO,EAAEC,MAAM,EAAUC,IAAI,QAAO,uBAAuB;AAEzE,SAAQC,UAAU,QAAO,WAAW;AAEpC,SAAQC,QAAQ,QAAO,yBAAyB;AAChD,SAAQC,MAAM,QAAO,wBAAwB;AAE7C,SAAQC,UAAU,QAAO,eAAe;AACxC,SAAQC,cAAc,QAAO,YAAY;AAEzC;;;AAGA,SAASC,uBAAuBA,CAACC,GAAmB,EAAEC,GAAW;EAC/D;EACA,IAAID,GAAG,CAACE,KAAK,IAAI,IAAI,IAAIF,GAAG,CAACE,KAAK,KAAKD,GAAG,CAACC,KAAK,EAAE;IAChD;IACA,OAAOD,GAAG;;EAEZ,IAAI;IACF;IACA,OAAOX,IAAI,CAACW,GAAG,EAAED,GAAG,CAACE,KAAK,CAAC;GAC5B,CAAC,OAAOC,GAAG,EAAE;IACZ;IACA,MAAM,IAAIT,UAAU,CAChB,0BAA0BO,GAAG,CAACC,KAAK,iCAAiC,GACpE,eAAeF,GAAG,CAACI,IAAI,MAAMJ,GAAG,CAACE,KAAK,IAAI,CAAC;;AAEnD;AAUA;;;;AAIA,OAAM,MAAOG,QAAQ;EAKnB;;;;;EAKAC,YAAYC,KAAuB;IAT3B,KAAAC,QAAQ,GAA2B,EAAE;IACrC,KAAAC,OAAO,GAA2B,EAAE;IACpC,KAAAC,OAAO,GAA6B,EAAE;IAQ5C,IAAIH,KAAK,YAAYF,QAAQ,EAAE;MAC7B,KAAK,MAAMM,EAAE,IAAIJ,KAAK,CAACC,QAAQ,EAAE;QAC/B,IAAI,CAACA,QAAQ,CAACG,EAAE,CAAC,GAAGJ,KAAK,CAACC,QAAQ,CAACG,EAAE,CAAC;QACtC,IAAIA,EAAE,IAAIJ,KAAK,CAACE,OAAO,EAAE;UACvB,IAAI,CAACA,OAAO,CAACE,EAAE,CAAC,GAAGJ,KAAK,CAACE,OAAO,CAACE,EAAE,CAAC;;;KAGzC,MAAM;MACL,IAAIJ,KAAK,IAAI,IAAI,EAAE;QACjB;;MAEF,KAAK,MAAMK,IAAI,IAAIL,KAAK,EAAE;QACxB,IAAI,CAACM,GAAG,CAACD,IAAI,CAACZ,GAAG,EAAEY,IAAI,CAACE,KAAK,CAAC;;;EAGpC;EAEA;;;;;;;;;;EAUAD,GAAGA,CAACb,GAAmB,EAAEc,KAAa,EAAEC,IAAa;IACnD,IAAI,IAAI,CAACP,QAAQ,CAACR,GAAG,CAACW,EAAE,CAAC,IAAI,IAAI,EAAE;MACjC,IAAI,CAACH,QAAQ,CAACR,GAAG,CAACW,EAAE,CAAC,GAAGZ,uBAAuB,CAACC,GAAG,EAAEc,KAAK,CAAC;MAC3D,IAAI,CAACJ,OAAO,CAACV,GAAG,CAACI,IAAI,CAAC,GAAGJ,GAAG,CAACW,EAAE;MAC/B,IAAII,IAAI,IAAI,IAAI,EAAE;QAChB,IAAI,CAACN,OAAO,CAACT,GAAG,CAACW,EAAE,CAAC,GAAGI,IAAI;;KAE9B,MAAM;MACL,MAAM,IAAIrB,UAAU,CAAC,uBAAuBM,GAAG,CAACI,IAAI,QAAQJ,GAAG,CAACW,EAAE,EAAE,CAAC;;IAEvE,OAAO,IAAI;EACb;EAEA;;;;;EAKAK,OAAOA,CAACJ,IAAU;IAChB,IAAI,CAACC,GAAG,CAACD,IAAI,CAACZ,GAAG,EAAEY,IAAI,CAACE,KAAK,CAAC;EAChC;EAEA;;;;EAIAG,MAAMA,CAACjB,GAAmB;IACxB,OAAO,IAAI,CAACQ,QAAQ,CAACR,GAAG,CAACW,EAAE,CAAC,IAAI,IAAI;EACtC;EAEA;;;EAGAO,KAAKA,CAAA;IACH,OAAOC,MAAM,CAACC,IAAI,CAAC,IAAI,CAACV,OAAO,CAAC;EAClC;EAEA;;;;;;;EAOAW,QAAQA,CAACrB,GAA0B;IACjC,IAAIA,GAAG,YAAYF,cAAc,EAAE;MACjC,IAAI,IAAI,CAACU,QAAQ,CAACR,GAAG,CAACW,EAAE,CAAC,IAAI,IAAI,EAAE;QACjC,MAAM,IAAIjB,UAAU,CAAC,oBAAoBM,GAAG,CAACI,IAAI,EAAE,CAAC;OACrD,MAAM;QACL,OAAO,IAAI,CAACI,QAAQ,CAACR,GAAG,CAACW,EAAE,CAAC;;KAE/B,MAAM;MACL,MAAMA,EAAE,GAAG,IAAI,CAACD,OAAO,CAACV,GAAG,CAAC;MAC5B,IAAIW,EAAE,IAAI,IAAI,EAAE;QACd,MAAM,IAAIjB,UAAU,CAAC,yCAAyCM,GAAG,EAAE,CAAC;;MAEtE,OAAO,IAAI,CAACQ,QAAQ,CAACG,EAAE,CAAC;;EAE5B;EAEA;;;;;;;EAOAW,OAAOA,CAACtB,GAA0B;IAChC,IAAIA,GAAG,YAAYF,cAAc,EAAE;MACjC,IAAI,IAAI,CAACU,QAAQ,CAACR,GAAG,CAACW,EAAE,CAAC,IAAI,IAAI,EAAE;QACjC,MAAM,IAAIjB,UAAU,CAAC,oBAAoBM,GAAG,CAACI,IAAI,EAAE,CAAC;OACrD,MAAM;QACL,OAAO,IAAI,CAACK,OAAO,CAACT,GAAG,CAACW,EAAE,CAAC;;KAE9B,MAAM;MACL,MAAMA,EAAE,GAAG,IAAI,CAACD,OAAO,CAACV,GAAG,CAAC;MAC5B,IAAIW,EAAE,IAAI,IAAI,EAAE;QACd,MAAM,IAAIjB,UAAU,CAAC,yCAAyCM,GAAG,EAAE,CAAC;;MAEtE,OAAO,IAAI,CAACS,OAAO,CAACE,EAAE,CAAC;;EAE3B;EAEA;EACAY,YAAYA,CAAA;IACV,IAAI,IAAI,CAACd,OAAO,IAAI,IAAI,EAAE;MACxBlB,OAAO,CAAC,IAAI,CAACkB,OAAO,CAAC;;EAEzB;;AAGF;AACA;AACA,OAAO,MAAMe,YAAY,GACrB,IAAI7B,QAAQ,EAAoB;AAEpC;AACA,OAAO,MAAM8B,qBAAqB,GAC9B,IAAI9B,QAAQ,EAAmB;AAEnC,OAAM,SAAU+B,qBAAqBA,CAACC,UAAkB;EACtD,IAAIH,YAAY,IAAI,IAAI,EAAE;IACxBA,YAAY,CAACI,aAAa,CAACD,UAAU,CAAC;;EAExC,IAAIF,qBAAqB,IAAI,IAAI,EAAE;IACjCA,qBAAqB,CAACG,aAAa,CAACD,UAAU,CAAC;;AAEnD;AAsBA;;;;;;;;;;;;;;;;;;;;;AAqBA,OAAM,SAAUE,OAAOA,CACnBC,OAAwC,EAAEC,QAAkB,EAC5DC,MAAe,EAAEC,KAAsB;EAEzC,MAAMC,QAAQ,GAAYF,MAAM,IAAI,IAAI,GAAG,KAAK,GAAGA,MAAM,CAAC,UAAU,CAAC;EAErE,MAAMG,YAAY,GAAGC,KAAK,CAACC,OAAO,CAACP,OAAO,CAAC;EAC3C,MAAMQ,UAAU,GACZH,YAAY,GAAGL,OAAO,GAAG,CAACA,OAAO,CAAC;EAEtC,MAAMS,WAAW,GAAGD,UAAU,CAACE,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACrC,IAAI,CAAC;EAC/C,MAAMsC,YAAY,GAAa,EAAE;EACjC,MAAMC,SAAS,GAAGZ,QAAQ,CAACb,KAAK,EAAE;EAClC,KAAK,MAAM0B,UAAU,IAAIL,WAAW,EAAE;IACpC,IAAII,SAAS,CAACE,OAAO,CAACD,UAAU,CAAC,KAAK,CAAC,CAAC,EAAE;MACxCF,YAAY,CAACI,IAAI,CAACf,QAAQ,CAACV,QAAQ,CAACuB,UAAU,CAAC,CAAC;KACjD,MAAM;MACLF,YAAY,CAACI,IAAI,CAAC,IAAI,CAAC;;;EAI3B,IAAIb,KAAK,IAAI,IAAI,EAAE;IACjB;IACAA,KAAK,CAACc,aAAa,GAAG,CAACC,QAAQ;IAC/Bf,KAAK,CAACgB,aAAa,GAAGD,QAAQ;;EAGhC;EACA,MAAME,eAAe,GACjBX,WAAW,CAACY,IAAI,CAAC,GAAG,CAAC,GAAG,GAAG,GAAGpB,QAAQ,CAACb,KAAK,EAAE,CAACkC,IAAI,EAAE,CAACD,IAAI,CAAC,GAAG,CAAC;EACnE,IAAIE,MAAM,GAAqB7B,YAAY,CAAC8B,GAAG,CAACJ,eAAe,CAAC;EAChE,IAAIK,eAA8C;EAClD,IAAIF,MAAM,IAAI,IAAI,EAAE;IAClB;IACA;IACA,MAAMG,GAAG,GAAGC,oCAAoC,CAACnB,UAAU,EAAEP,QAAQ,CAAC;IACtEsB,MAAM,GAAGG,GAAG,CAACH,MAAM;IACnBE,eAAe,GAAGC,GAAG,CAACD,eAAe;IAErC;IACA/B,YAAY,CAACkC,GAAG,CAACR,eAAe,EAAEG,MAAM,CAAC;IACzC5B,qBAAqB,CAACiC,GAAG,CAACR,eAAe,EAAEK,eAAe,CAAC;;EAE7DA,eAAe,GAAG,EAAE;EACpB,IAAI,CAACrB,QAAQ,EAAE;IACbf,MAAM,CAACwC,MAAM,CAACJ,eAAe,EAAE9B,qBAAqB,CAAC6B,GAAG,CAACJ,eAAe,CAAC,CAAC;;EAG5E,MAAMU,gBAAgB,GAAG,IAAIvD,QAAQ,CAAC0B,QAAQ,CAAC;EAE/C;EACA,KAAK,IAAI8B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGR,MAAM,CAACS,MAAM,EAAE,EAAED,CAAC,EAAE;IACtC,IAAI5B,KAAK,IAAI,IAAI,EAAE;MACjB;MACA,MAAM8B,UAAU,GAAGvE,MAAM,EAAE,CAACuE,UAAU;MACtC,IAAIA,UAAU,GAAG9B,KAAK,CAACc,aAAa,EAAE;QACpCd,KAAK,CAACc,aAAa,GAAGgB,UAAU;;MAElC,IAAIA,UAAU,GAAG9B,KAAK,CAACgB,aAAa,EAAE;QACpChB,KAAK,CAACgB,aAAa,GAAGc,UAAU;;;IAIpC,MAAMC,QAAQ,GAAGX,MAAM,CAACQ,CAAC,CAAC;IAC1B,MAAMI,QAAQ,GAAGD,QAAQ,CAACE,WAAW;IACrC,IAAID,QAAQ,YAAYpE,UAAU,EAAE;MAClC;;IAEF,MAAMsE,WAAW,GAAa,EAAE;IAChC,MAAMC,UAAU,GAAa,EAAE;IAC/B,MAAMC,gBAAgB,GAAa,EAAE;IAErC,IAAIC,UAAU,GAAG,KAAK;IACtB,KAAK,MAAMC,KAAK,IAAIP,QAAQ,CAACQ,MAAM,EAAE;MACnC,MAAM1D,KAAK,GAAG8C,gBAAgB,CAACvC,QAAQ,CAACkD,KAAK,CAAC;MAC9C,MAAMxD,IAAI,GAAG6C,gBAAgB,CAACtC,OAAO,CAACiD,KAAK,CAAC;MAC5CJ,WAAW,CAACrB,IAAI,CAAChC,KAAK,CAAC;MACvBsD,UAAU,CAACtB,IAAI,CAAC/B,IAAI,CAAC;MACrB,IAAIA,IAAI,IAAI,IAAI,EAAE;QAChBuD,UAAU,GAAG,IAAI;;MAEnB,IAAI,CAACpC,QAAQ,EAAE;QACbqB,eAAe,CAACgB,KAAK,CAACnE,IAAI,CAAC,EAAE;QAC7B,IAAImD,eAAe,CAACgB,KAAK,CAACnE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC2B,QAAQ,CAACd,MAAM,CAACsD,KAAK,CAAC,IAC5DhC,WAAW,CAACM,OAAO,CAAC0B,KAAK,CAACnE,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,CAACU,KAAK,CAAC2D,UAAU,IAC3DF,KAAK,CAACL,WAAW,CAACQ,QAAQ,KAAK,IAAI,EAAE;UACvCL,gBAAgB,CAACvB,IAAI,CAAChC,KAAK,CAAC;;;;IAKlC,IAAIwD,UAAU,EAAE;MACdtC,MAAM,GAAGA,MAAM,IAAI,EAAE;MACrBA,MAAM,CAAC,MAAM,CAAC,GAAGoC,UAAU,CAAC,CAAC,CAAC;;IAEhC,MAAMO,aAAa,GACf/E,MAAM,CAACqE,QAAQ,CAACW,KAAK,CAACT,WAAW,EAAEnC,MAAM,CAAC,CAAa;IAC3D,IAAI6C,UAAU,GAAoB,IAAI;IACtC,IAAIZ,QAAQ,CAACa,eAAe,EAAE;MAC5BD,UAAU,GAAGZ,QAAQ,CAACc,WAAW,CAACZ,WAAW,EAAEC,UAAU,CAAC;;IAE5D,MAAMY,YAAY,GAAGC,cAAc,CAACjB,QAAQ,CAAC;IAC7C,MAAMkB,qBAAqB,GACvB9C,KAAK,CAACC,OAAO,CAAC2C,YAAY,CAAC,GAAGA,YAAY,GAAG,CAACA,YAAY,CAAC;IAC/D,KAAK,IAAInB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGqB,qBAAqB,CAACpB,MAAM,EAAE,EAAED,CAAC,EAAE;MACrD,IAAI,CAACD,gBAAgB,CAAC3C,MAAM,CAACiE,qBAAqB,CAACrB,CAAC,CAAC,CAAC,EAAE;QACtDD,gBAAgB,CAAC/C,GAAG,CAChBqE,qBAAqB,CAACrB,CAAC,CAAC,EAAEc,aAAa,CAACd,CAAC,CAAC,EAC1CzB,KAAK,CAACC,OAAO,CAACwC,UAAU,CAAC,GAAGA,UAAU,CAAC,CAAC,CAAC,GAAGA,UAAU,CAAC;;MAE7D,MAAMM,KAAK,GAAG5C,WAAW,CAACM,OAAO,CAACqC,qBAAqB,CAACrB,CAAC,CAAC,CAACzD,IAAI,CAAC;MAChE,IAAI+E,KAAK,KAAK,CAAC,CAAC,EAAE;QAChBzC,YAAY,CAACyC,KAAK,CAAC,GAAGR,aAAa,CAACd,CAAC,CAAC;;;IAI1C,IAAI,CAAC3B,QAAQ,EAAE;MACb;MACA3C,OAAO,CAAC8E,gBAAgB,CAAC;;;EAG7B;EACA;EACA;EACA;EACA;EACAT,gBAAgB,CAACrC,YAAY,EAAE;EAE/B,OAAOY,YAAY,GAAGO,YAAY,GAAGA,YAAY,CAAC,CAAC,CAAC;AACtD;AAUA;;;;;;;;;;;AAWA,SAASe,oCAAoCA,CACzC3B,OAAyB,EAAEC,QAAkB;EAE/CtC,IAAI,CAAC2F,MAAM,CACPtD,OAAO,IAAI,IAAI,IAAIA,OAAO,CAACgC,MAAM,GAAG,CAAC,EACrC,MAAM,uCAAuC,CAAC;EAElD,IAAIuB,WAAW,GAAqB,EAAE;EACtC,IAAIC,iBAAiB,GAAiB,EAAE;EACxC,IAAIxD,OAAO,CAACgC,MAAM,KAAK,CAAC,EAAE;IACxB;IACA,MAAMN,GAAG,GACL+B,+CAA+C,CAACzD,OAAO,CAAC,CAAC,CAAC,EAAEC,QAAQ,CAAC;IACzEsD,WAAW,GAAG7B,GAAG,CAACH,MAAM;IACxBiC,iBAAiB,GAAG9B,GAAG,CAACgC,YAAY;GACrC,MAAM;IACL,MAAMC,OAAO,GAAG,IAAIC,GAAG,EAAU;IACjC,KAAK,MAAMC,KAAK,IAAI7D,OAAO,EAAE;MAC3B,MAAM;QAACuB,MAAM;QAAEmC;MAAY,CAAC,GACxBD,+CAA+C,CAACI,KAAK,EAAE5D,QAAQ,CAAC;MAEpE;MACA,KAAK,MAAM6D,cAAc,IAAIvC,MAAM,EAAE;QACnC,IAAI,CAACoC,OAAO,CAACI,GAAG,CAACD,cAAc,CAACxF,IAAI,CAAC,EAAE;UACrCiF,WAAW,CAACvC,IAAI,CAAC8C,cAAc,CAAC;UAChCH,OAAO,CAAC5E,GAAG,CAAC+E,cAAc,CAACxF,IAAI,CAAC;;;MAIpC;MACA,KAAK,MAAMA,IAAI,IAAIoF,YAAY,EAAE;QAC/B,IAAIF,iBAAiB,CAAClF,IAAI,CAAC,IAAI,IAAI,EAAE;UACnCkF,iBAAiB,CAAClF,IAAI,CAAC,GAAG,IAAIsF,GAAG,EAAU;;QAE7CF,YAAY,CAACpF,IAAI,CAAC,CAAC0F,OAAO,CACtBC,SAAS,IAAIT,iBAAiB,CAAClF,IAAI,CAAC,CAACS,GAAG,CAACkF,SAAS,CAAC,CAAC;;;;EAI9D,OAAO;IACL1C,MAAM,EAAEgC,WAAW;IACnB9B,eAAe,EAAEyC,mBAAmB,CAACV,iBAAiB;GACvD;AACH;AAEA,SAASU,mBAAmBA,CAACR,YAA0B;EACrD,MAAMjC,eAAe,GAAoB,EAAE;EAC3C,KAAK,MAAMnD,IAAI,IAAIoF,YAAY,EAAE;IAC/BjC,eAAe,CAACnD,IAAI,CAAC,GAAGoF,YAAY,CAACpF,IAAI,CAAC,CAAC6F,IAAI;;EAEjD,OAAO1C,eAAe;AACxB;AAEA;;;;;;;;;;;AAWA,OAAM,SAAUgC,+CAA+CA,CAC3DI,KAAqB,EAAE5D,QAAkB;EAE3C,MAAM0D,OAAO,GAAG,IAAIC,GAAG,EAAU;EACjC,MAAMrC,MAAM,GAAqB,EAAE;EACnC,MAAMmC,YAAY,GAAiB,EAAE;EAErC;EACA;EACA;EACA,KAAK,MAAMxF,GAAG,IAAI+B,QAAQ,CAACb,KAAK,EAAE,EAAE;IAClCuE,OAAO,CAAC5E,GAAG,CAACb,GAAG,CAAC;;EAGlB,MAAMkG,KAAK,GAAqB,EAAE;EAClC,MAAMC,KAAK,GAAa,EAAE;EAE1B;EACAD,KAAK,CAACpD,IAAI,CAAC6C,KAAK,CAAC;EAEjB,OAAOO,KAAK,CAACpC,MAAM,GAAG,CAAC,EAAE;IACvB,MAAMsC,GAAG,GAAGF,KAAK,CAACA,KAAK,CAACpC,MAAM,GAAG,CAAC,CAAC;IACnC,IAAI2B,OAAO,CAACI,GAAG,CAACO,GAAG,CAAChG,IAAI,CAAC,EAAE;MACzB8F,KAAK,CAACG,GAAG,EAAE;MACX;;IAEF,MAAMC,WAAW,GAAGH,KAAK,CAACA,KAAK,CAACrC,MAAM,GAAG,CAAC,CAAC,KAAKoC,KAAK,CAACpC,MAAM,GAAG,CAAC;IAChE,IAAIsC,GAAG,CAAC5B,MAAM,CAACV,MAAM,KAAK,CAAC,IAAIwC,WAAW,EAAE;MAC1C;MACAJ,KAAK,CAACG,GAAG,EAAE;MACXhD,MAAM,CAACP,IAAI,CAACsD,GAAG,CAAC;MAChBX,OAAO,CAAC5E,GAAG,CAACuF,GAAG,CAAChG,IAAI,CAAC;MACrB,IAAIkG,WAAW,EAAE;QACfH,KAAK,CAACE,GAAG,EAAE;;KAEd,MAAM;MACL;MACA;MACAF,KAAK,CAACrD,IAAI,CAACoD,KAAK,CAACpC,MAAM,GAAG,CAAC,CAAC;MAC5B,KAAK,MAAMS,KAAK,IAAI6B,GAAG,CAAC5B,MAAM,EAAE;QAC9B;QACA;QACA,IAAIgB,YAAY,CAACjB,KAAK,CAACnE,IAAI,CAAC,IAAI,IAAI,EAAE;UACpCoF,YAAY,CAACjB,KAAK,CAACnE,IAAI,CAAC,GAAG,IAAIsF,GAAG,EAAU;;QAE9CF,YAAY,CAACjB,KAAK,CAACnE,IAAI,CAAC,CAACS,GAAG,CAACuF,GAAG,CAAChG,IAAI,CAAC;QAEtC,IAAIqF,OAAO,CAACI,GAAG,CAACtB,KAAK,CAACnE,IAAI,CAAC,EAAE;UAC3B,SAAS,CAAE;;QAEb8F,KAAK,CAACpD,IAAI,CAACyB,KAAK,CAAC;;;;EAIvB,OAAO;IAAClB,MAAM;IAAEmC;EAAY,CAAC;AAC/B;AAEA;;;;;;AAMA,SAASP,cAAcA,CAACU,KAAqB;EAE3C,IAAIX,YAA6C;EACjD,IAAIW,KAAK,CAACzB,WAAW,CAACqC,YAAY,CAACzC,MAAM,KAAK,CAAC,EAAE;IAC/CkB,YAAY,GAAGW,KAAK,CAACzB,WAAW,CAACsC,MAAM;GACxC,MAAM;IACL,IAAIC,SAAS,GAAW,IAAI;IAC5B,KAAK,IAAI5C,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG8B,KAAK,CAACzB,WAAW,CAACqC,YAAY,CAACzC,MAAM,EAAE,EAAED,CAAC,EAAE;MAC9D,KAAK,MAAM6C,YAAY,IAAIf,KAAK,CAACzB,WAAW,CAACqC,YAAY,CAAC1C,CAAC,CAAC,CAClDc,aAAa,EAAE;QACvB,IAAI+B,YAAY,CAAC/F,EAAE,KAAKgF,KAAK,CAAChF,EAAE,EAAE;UAChC8F,SAAS,GAAG5C,CAAC;UACb;;;;IAINmB,YAAY,GAAGW,KAAK,CAACzB,WAAW,CAACyC,WAAW,CAACF,SAAS,CAAC;;EAEzD,OAAOzB,YAAY;AACrB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}