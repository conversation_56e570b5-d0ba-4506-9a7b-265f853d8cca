{"ast": null, "code": "/*\nCopyright (c) 2017, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON> (Reimplemented from https://github.com/sastrawi/sastrawi)\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in\nall copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\nTHE SOFTWARE.\n*/\n\n'use strict';\n\nconst Tokenizer = require('./tokenizer');\nclass AggressiveTokenizer extends Tokenizer {\n  // Remove all non alphanumeric characters except '-'\n  // Replace more than one space character to ' '\n  normalizeText(text) {\n    const result = text.replace(/[^a-z0-9 -]/g, ' ').replace(/( +)/g, ' ');\n    return result;\n  }\n  tokenize(text) {\n    // break a string up into an array of tokens by space\n    text = this.normalizeText(text);\n    return this.trim(text.split(' '));\n  }\n}\nmodule.exports = AggressiveTokenizer;", "map": {"version": 3, "names": ["Tokenizer", "require", "AggressiveTokenizer", "normalizeText", "text", "result", "replace", "tokenize", "trim", "split", "module", "exports"], "sources": ["C:/tmsft/node_modules/natural/lib/natural/tokenizers/aggressive_tokenizer_id.js"], "sourcesContent": ["/*\nCopyright (c) 2017, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON> (Reimplemented from https://github.com/sastrawi/sastrawi)\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in\nall copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\nTHE SOFTWARE.\n*/\n\n'use strict'\n\nconst Tokenizer = require('./tokenizer')\n\nclass AggressiveTokenizer extends Tokenizer {\n  // Remove all non alphanumeric characters except '-'\n  // Replace more than one space character to ' '\n  normalizeText (text) {\n    const result = text.replace(/[^a-z0-9 -]/g, ' ').replace(/( +)/g, ' ')\n    return result\n  }\n\n  tokenize (text) {\n    // break a string up into an array of tokens by space\n    text = this.normalizeText(text)\n    return this.trim(text.split(' '))\n  }\n}\n\nmodule.exports = AggressiveTokenizer\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,YAAY;;AAEZ,MAAMA,SAAS,GAAGC,OAAO,CAAC,aAAa,CAAC;AAExC,MAAMC,mBAAmB,SAASF,SAAS,CAAC;EAC1C;EACA;EACAG,aAAaA,CAAEC,IAAI,EAAE;IACnB,MAAMC,MAAM,GAAGD,IAAI,CAACE,OAAO,CAAC,cAAc,EAAE,GAAG,CAAC,CAACA,OAAO,CAAC,OAAO,EAAE,GAAG,CAAC;IACtE,OAAOD,MAAM;EACf;EAEAE,QAAQA,CAAEH,IAAI,EAAE;IACd;IACAA,IAAI,GAAG,IAAI,CAACD,aAAa,CAACC,IAAI,CAAC;IAC/B,OAAO,IAAI,CAACI,IAAI,CAACJ,IAAI,CAACK,KAAK,CAAC,GAAG,CAAC,CAAC;EACnC;AACF;AAEAC,MAAM,CAACC,OAAO,GAAGT,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}