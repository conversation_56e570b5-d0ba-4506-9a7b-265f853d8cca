{"ast": null, "code": "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { keep, scalar, stack, tidy, unstack, util } from '@tensorflow/tfjs-core';\n// tslint:disable-next-line: no-imports-from-dist\nimport * as tfOps from '@tensorflow/tfjs-core/dist/ops/ops_for_converter';\n/**\n * Hashtable contains a set of tensors, which can be accessed by key.\n */\nexport class HashTable {\n  get id() {\n    return this.handle.id;\n  }\n  /**\n   * Constructor of HashTable. Creates a hash table.\n   *\n   * @param keyDType `dtype` of the table keys.\n   * @param valueDType `dtype` of the table values.\n   */\n  constructor(keyDType, valueDType) {\n    this.keyDType = keyDType;\n    this.valueDType = valueDType;\n    this.handle = scalar(0);\n    // tslint:disable-next-line: no-any\n    this.tensorMap = new Map();\n    keep(this.handle);\n  }\n  /**\n   * Dispose the tensors and handle and clear the hashtable.\n   */\n  clearAndClose() {\n    this.tensorMap.forEach(value => value.dispose());\n    this.tensorMap.clear();\n    this.handle.dispose();\n  }\n  /**\n   * The number of items in the hash table.\n   */\n  size() {\n    return this.tensorMap.size;\n  }\n  /**\n   * The number of items in the hash table as a rank-0 tensor.\n   */\n  tensorSize() {\n    return tfOps.scalar(this.size(), 'int32');\n  }\n  /**\n   * Replaces the contents of the table with the specified keys and values.\n   * @param keys Keys to store in the hashtable.\n   * @param values Values to store in the hashtable.\n   */\n  async import(keys, values) {\n    this.checkKeyAndValueTensor(keys, values);\n    // We only store the primitive values of the keys, this allows lookup\n    // to be O(1).\n    const $keys = await keys.data();\n    // Clear the hashTable before inserting new values.\n    this.tensorMap.forEach(value => value.dispose());\n    this.tensorMap.clear();\n    return tidy(() => {\n      const $values = unstack(values);\n      const keysLength = $keys.length;\n      const valuesLength = $values.length;\n      util.assert(keysLength === valuesLength, () => `The number of elements doesn't match, keys has ` + `${keysLength} elements, the values has ${valuesLength} ` + `elements.`);\n      for (let i = 0; i < keysLength; i++) {\n        const key = $keys[i];\n        const value = $values[i];\n        keep(value);\n        this.tensorMap.set(key, value);\n      }\n      return this.handle;\n    });\n  }\n  /**\n   * Looks up keys in a hash table, outputs the corresponding values.\n   *\n   * Performs batch lookups, for every element in the key tensor, `find`\n   * stacks the corresponding value into the return tensor.\n   *\n   * If an element is not present in the table, the given `defaultValue` is\n   * used.\n   *\n   * @param keys Keys to look up. Must have the same type as the keys of the\n   *     table.\n   * @param defaultValue The scalar `defaultValue` is the value output for keys\n   *     not present in the table. It must also be of the same type as the\n   *     table values.\n   */\n  async find(keys, defaultValue) {\n    this.checkKeyAndValueTensor(keys, defaultValue);\n    const $keys = await keys.data();\n    return tidy(() => {\n      const result = [];\n      for (let i = 0; i < $keys.length; i++) {\n        const key = $keys[i];\n        const value = this.findWithDefault(key, defaultValue);\n        result.push(value);\n      }\n      return stack(result);\n    });\n  }\n  // tslint:disable-next-line: no-any\n  findWithDefault(key, defaultValue) {\n    const result = this.tensorMap.get(key);\n    return result != null ? result : defaultValue;\n  }\n  checkKeyAndValueTensor(key, value) {\n    if (key.dtype !== this.keyDType) {\n      throw new Error(`Expect key dtype ${this.keyDType}, but got ` + `${key.dtype}`);\n    }\n    if (value.dtype !== this.valueDType) {\n      throw new Error(`Expect value dtype ${this.valueDType}, but got ` + `${value.dtype}`);\n    }\n  }\n}", "map": {"version": 3, "names": ["keep", "scalar", "stack", "tidy", "unstack", "util", "tfOps", "HashTable", "id", "handle", "constructor", "keyDType", "valueDType", "tensorMap", "Map", "clearAndClose", "for<PERSON>ach", "value", "dispose", "clear", "size", "tensorSize", "import", "keys", "values", "checkKeyAndValueTensor", "$keys", "data", "$values", "<PERSON><PERSON><PERSON><PERSON>", "length", "valuesLength", "assert", "i", "key", "set", "find", "defaultValue", "result", "findWithDefault", "push", "get", "dtype", "Error"], "sources": ["C:\\tfjs-converter\\src\\executor\\hash_table.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport {DataType, keep, scalar, stack, Tensor, tidy, unstack, util} from '@tensorflow/tfjs-core';\n// tslint:disable-next-line: no-imports-from-dist\nimport * as tfOps from '@tensorflow/tfjs-core/dist/ops/ops_for_converter';\n\n/**\n * Hashtable contains a set of tensors, which can be accessed by key.\n */\nexport class HashTable {\n  readonly handle: Tensor;\n\n  // tslint:disable-next-line: no-any\n  private tensorMap: Map<any, Tensor>;\n\n  get id() {\n    return this.handle.id;\n  }\n\n  /**\n   * Constructor of HashTable. Creates a hash table.\n   *\n   * @param keyDType `dtype` of the table keys.\n   * @param valueDType `dtype` of the table values.\n   */\n  constructor(readonly keyDType: DataType, readonly valueDType: DataType) {\n    this.handle = scalar(0);\n    // tslint:disable-next-line: no-any\n    this.tensorMap = new Map<any, Tensor>();\n\n    keep(this.handle);\n  }\n\n  /**\n   * Dispose the tensors and handle and clear the hashtable.\n   */\n  clearAndClose() {\n    this.tensorMap.forEach(value => value.dispose());\n    this.tensorMap.clear();\n    this.handle.dispose();\n  }\n\n  /**\n   * The number of items in the hash table.\n   */\n  size(): number {\n    return this.tensorMap.size;\n  }\n\n  /**\n   * The number of items in the hash table as a rank-0 tensor.\n   */\n  tensorSize(): Tensor {\n    return tfOps.scalar(this.size(), 'int32');\n  }\n\n  /**\n   * Replaces the contents of the table with the specified keys and values.\n   * @param keys Keys to store in the hashtable.\n   * @param values Values to store in the hashtable.\n   */\n  async import(keys: Tensor, values: Tensor): Promise<Tensor> {\n    this.checkKeyAndValueTensor(keys, values);\n\n    // We only store the primitive values of the keys, this allows lookup\n    // to be O(1).\n    const $keys = await keys.data();\n\n    // Clear the hashTable before inserting new values.\n    this.tensorMap.forEach(value => value.dispose());\n    this.tensorMap.clear();\n\n    return tidy(() => {\n      const $values = unstack(values);\n\n      const keysLength = $keys.length;\n      const valuesLength = $values.length;\n\n      util.assert(\n          keysLength === valuesLength,\n          () => `The number of elements doesn't match, keys has ` +\n              `${keysLength} elements, the values has ${valuesLength} ` +\n              `elements.`);\n\n      for (let i = 0; i < keysLength; i++) {\n        const key = $keys[i];\n        const value = $values[i];\n\n        keep(value);\n        this.tensorMap.set(key, value);\n      }\n\n      return this.handle;\n    });\n  }\n\n  /**\n   * Looks up keys in a hash table, outputs the corresponding values.\n   *\n   * Performs batch lookups, for every element in the key tensor, `find`\n   * stacks the corresponding value into the return tensor.\n   *\n   * If an element is not present in the table, the given `defaultValue` is\n   * used.\n   *\n   * @param keys Keys to look up. Must have the same type as the keys of the\n   *     table.\n   * @param defaultValue The scalar `defaultValue` is the value output for keys\n   *     not present in the table. It must also be of the same type as the\n   *     table values.\n   */\n  async find(keys: Tensor, defaultValue: Tensor): Promise<Tensor> {\n    this.checkKeyAndValueTensor(keys, defaultValue);\n\n    const $keys = await keys.data();\n\n    return tidy(() => {\n      const result: Tensor[] = [];\n\n      for (let i = 0; i < $keys.length; i++) {\n        const key = $keys[i];\n\n        const value = this.findWithDefault(key, defaultValue);\n        result.push(value);\n      }\n\n      return stack(result);\n    });\n  }\n\n  // tslint:disable-next-line: no-any\n  private findWithDefault(key: any, defaultValue: Tensor): Tensor {\n    const result = this.tensorMap.get(key);\n\n    return result != null ? result : defaultValue;\n  }\n\n  private checkKeyAndValueTensor(key: Tensor, value: Tensor) {\n    if (key.dtype !== this.keyDType) {\n      throw new Error(\n          `Expect key dtype ${this.keyDType}, but got ` +\n          `${key.dtype}`);\n    }\n\n    if (value.dtype !== this.valueDType) {\n      throw new Error(\n          `Expect value dtype ${this.valueDType}, but got ` +\n          `${value.dtype}`);\n    }\n  }\n}\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAgBA,SAAkBA,IAAI,EAAEC,MAAM,EAAEC,KAAK,EAAUC,IAAI,EAAEC,OAAO,EAAEC,IAAI,QAAO,uBAAuB;AAChG;AACA,OAAO,KAAKC,KAAK,MAAM,kDAAkD;AAEzE;;;AAGA,OAAM,MAAOC,SAAS;EAMpB,IAAIC,EAAEA,CAAA;IACJ,OAAO,IAAI,CAACC,MAAM,CAACD,EAAE;EACvB;EAEA;;;;;;EAMAE,YAAqBC,QAAkB,EAAWC,UAAoB;IAAjD,KAAAD,QAAQ,GAARA,QAAQ;IAAqB,KAAAC,UAAU,GAAVA,UAAU;IAC1D,IAAI,CAACH,MAAM,GAAGR,MAAM,CAAC,CAAC,CAAC;IACvB;IACA,IAAI,CAACY,SAAS,GAAG,IAAIC,GAAG,EAAe;IAEvCd,IAAI,CAAC,IAAI,CAACS,MAAM,CAAC;EACnB;EAEA;;;EAGAM,aAAaA,CAAA;IACX,IAAI,CAACF,SAAS,CAACG,OAAO,CAACC,KAAK,IAAIA,KAAK,CAACC,OAAO,EAAE,CAAC;IAChD,IAAI,CAACL,SAAS,CAACM,KAAK,EAAE;IACtB,IAAI,CAACV,MAAM,CAACS,OAAO,EAAE;EACvB;EAEA;;;EAGAE,IAAIA,CAAA;IACF,OAAO,IAAI,CAACP,SAAS,CAACO,IAAI;EAC5B;EAEA;;;EAGAC,UAAUA,CAAA;IACR,OAAOf,KAAK,CAACL,MAAM,CAAC,IAAI,CAACmB,IAAI,EAAE,EAAE,OAAO,CAAC;EAC3C;EAEA;;;;;EAKA,MAAME,MAAMA,CAACC,IAAY,EAAEC,MAAc;IACvC,IAAI,CAACC,sBAAsB,CAACF,IAAI,EAAEC,MAAM,CAAC;IAEzC;IACA;IACA,MAAME,KAAK,GAAG,MAAMH,IAAI,CAACI,IAAI,EAAE;IAE/B;IACA,IAAI,CAACd,SAAS,CAACG,OAAO,CAACC,KAAK,IAAIA,KAAK,CAACC,OAAO,EAAE,CAAC;IAChD,IAAI,CAACL,SAAS,CAACM,KAAK,EAAE;IAEtB,OAAOhB,IAAI,CAAC,MAAK;MACf,MAAMyB,OAAO,GAAGxB,OAAO,CAACoB,MAAM,CAAC;MAE/B,MAAMK,UAAU,GAAGH,KAAK,CAACI,MAAM;MAC/B,MAAMC,YAAY,GAAGH,OAAO,CAACE,MAAM;MAEnCzB,IAAI,CAAC2B,MAAM,CACPH,UAAU,KAAKE,YAAY,EAC3B,MAAM,iDAAiD,GACnD,GAAGF,UAAU,6BAA6BE,YAAY,GAAG,GACzD,WAAW,CAAC;MAEpB,KAAK,IAAIE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,UAAU,EAAEI,CAAC,EAAE,EAAE;QACnC,MAAMC,GAAG,GAAGR,KAAK,CAACO,CAAC,CAAC;QACpB,MAAMhB,KAAK,GAAGW,OAAO,CAACK,CAAC,CAAC;QAExBjC,IAAI,CAACiB,KAAK,CAAC;QACX,IAAI,CAACJ,SAAS,CAACsB,GAAG,CAACD,GAAG,EAAEjB,KAAK,CAAC;;MAGhC,OAAO,IAAI,CAACR,MAAM;IACpB,CAAC,CAAC;EACJ;EAEA;;;;;;;;;;;;;;;EAeA,MAAM2B,IAAIA,CAACb,IAAY,EAAEc,YAAoB;IAC3C,IAAI,CAACZ,sBAAsB,CAACF,IAAI,EAAEc,YAAY,CAAC;IAE/C,MAAMX,KAAK,GAAG,MAAMH,IAAI,CAACI,IAAI,EAAE;IAE/B,OAAOxB,IAAI,CAAC,MAAK;MACf,MAAMmC,MAAM,GAAa,EAAE;MAE3B,KAAK,IAAIL,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGP,KAAK,CAACI,MAAM,EAAEG,CAAC,EAAE,EAAE;QACrC,MAAMC,GAAG,GAAGR,KAAK,CAACO,CAAC,CAAC;QAEpB,MAAMhB,KAAK,GAAG,IAAI,CAACsB,eAAe,CAACL,GAAG,EAAEG,YAAY,CAAC;QACrDC,MAAM,CAACE,IAAI,CAACvB,KAAK,CAAC;;MAGpB,OAAOf,KAAK,CAACoC,MAAM,CAAC;IACtB,CAAC,CAAC;EACJ;EAEA;EACQC,eAAeA,CAACL,GAAQ,EAAEG,YAAoB;IACpD,MAAMC,MAAM,GAAG,IAAI,CAACzB,SAAS,CAAC4B,GAAG,CAACP,GAAG,CAAC;IAEtC,OAAOI,MAAM,IAAI,IAAI,GAAGA,MAAM,GAAGD,YAAY;EAC/C;EAEQZ,sBAAsBA,CAACS,GAAW,EAAEjB,KAAa;IACvD,IAAIiB,GAAG,CAACQ,KAAK,KAAK,IAAI,CAAC/B,QAAQ,EAAE;MAC/B,MAAM,IAAIgC,KAAK,CACX,oBAAoB,IAAI,CAAChC,QAAQ,YAAY,GAC7C,GAAGuB,GAAG,CAACQ,KAAK,EAAE,CAAC;;IAGrB,IAAIzB,KAAK,CAACyB,KAAK,KAAK,IAAI,CAAC9B,UAAU,EAAE;MACnC,MAAM,IAAI+B,KAAK,CACX,sBAAsB,IAAI,CAAC/B,UAAU,YAAY,GACjD,GAAGK,KAAK,CAACyB,KAAK,EAAE,CAAC;;EAEzB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}