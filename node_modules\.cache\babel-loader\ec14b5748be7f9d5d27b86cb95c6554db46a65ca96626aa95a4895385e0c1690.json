{"ast": null, "code": "/**\n * @license\n * Copyright 2018 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { inferShape } from '../tensor_util_env';\nimport { assertNonNull } from '../util';\nimport { makeTensor } from './tensor_ops_util';\n/**\n * Creates rank-1 `tf.Tensor` with the provided values, shape and dtype.\n *\n * The same functionality can be achieved with `tf.tensor`, but in general\n * we recommend using `tf.tensor1d` as it makes the code more readable.\n *\n * ```js\n * tf.tensor1d([1, 2, 3]).print();\n * ```\n *\n * @param values The values of the tensor. Can be array of numbers,\n *     or a `TypedArray`.\n * @param dtype The data type.\n *\n * @doc {heading: 'Tensors', subheading: 'Creation'}\n */\nexport function tensor1d(values, dtype) {\n  assertNonNull(values);\n  const inferredShape = inferShape(values, dtype);\n  if (inferredShape.length !== 1) {\n    throw new Error('tensor1d() requires values to be a flat/TypedArray');\n  }\n  const shape = null;\n  return makeTensor(values, shape, inferredShape, dtype);\n}", "map": {"version": 3, "names": ["inferShape", "assertNonNull", "makeTensor", "tensor1d", "values", "dtype", "inferredShape", "length", "Error", "shape"], "sources": ["C:\\tfjs-core\\src\\ops\\tensor1d.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2018 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {Tensor1D} from '../tensor';\nimport {inferShape} from '../tensor_util_env';\nimport {TensorLike1D} from '../types';\nimport {DataType} from '../types';\nimport {assertNonNull} from '../util';\nimport {makeTensor} from './tensor_ops_util';\n\n/**\n * Creates rank-1 `tf.Tensor` with the provided values, shape and dtype.\n *\n * The same functionality can be achieved with `tf.tensor`, but in general\n * we recommend using `tf.tensor1d` as it makes the code more readable.\n *\n * ```js\n * tf.tensor1d([1, 2, 3]).print();\n * ```\n *\n * @param values The values of the tensor. Can be array of numbers,\n *     or a `TypedArray`.\n * @param dtype The data type.\n *\n * @doc {heading: 'Tensors', subheading: 'Creation'}\n */\nexport function tensor1d(values: TensorLike1D, dtype?: DataType): Tensor1D {\n  assertNonNull(values);\n  const inferredShape = inferShape(values, dtype);\n  if (inferredShape.length !== 1) {\n    throw new Error('tensor1d() requires values to be a flat/TypedArray');\n  }\n  const shape: number[] = null;\n  return makeTensor(values, shape, inferredShape, dtype) as Tensor1D;\n}\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAkBA,SAAQA,UAAU,QAAO,oBAAoB;AAG7C,SAAQC,aAAa,QAAO,SAAS;AACrC,SAAQC,UAAU,QAAO,mBAAmB;AAE5C;;;;;;;;;;;;;;;;AAgBA,OAAM,SAAUC,QAAQA,CAACC,MAAoB,EAAEC,KAAgB;EAC7DJ,aAAa,CAACG,MAAM,CAAC;EACrB,MAAME,aAAa,GAAGN,UAAU,CAACI,MAAM,EAAEC,KAAK,CAAC;EAC/C,IAAIC,aAAa,CAACC,MAAM,KAAK,CAAC,EAAE;IAC9B,MAAM,IAAIC,KAAK,CAAC,oDAAoD,CAAC;;EAEvE,MAAMC,KAAK,GAAa,IAAI;EAC5B,OAAOP,UAAU,CAACE,MAAM,EAAEK,KAAK,EAAEH,aAAa,EAAED,KAAK,CAAa;AACpE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}