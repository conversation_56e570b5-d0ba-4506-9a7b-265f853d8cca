{"ast": null, "code": "/**\n * @license\n * Copyright 2018 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { inferShape } from '../tensor_util_env';\nimport { makeTensor } from './tensor_ops_util';\n/**\n * Creates a `tf.Tensor` with the provided values, shape and dtype.\n *\n * ```js\n * // Pass an array of values to create a vector.\n * tf.tensor([1, 2, 3, 4]).print();\n * ```\n *\n * ```js\n * // Pass a nested array of values to make a matrix or a higher\n * // dimensional tensor.\n * tf.tensor([[1, 2], [3, 4]]).print();\n * ```\n *\n * ```js\n * // Pass a flat array and specify a shape yourself.\n * tf.tensor([1, 2, 3, 4], [2, 2]).print();\n * ```\n *\n * ```js\n * // Pass a `WebGLData` object and specify a shape yourself.\n *\n * // This makes it possible for TF.js applications to avoid GPU / CPU sync.\n * // For example, if your application includes a preprocessing step on the GPU,\n * // you could upload the GPU output directly to TF.js, rather than first\n * // downloading the values.\n *\n * // Example for WebGL2:\n * if (tf.findBackend('custom-webgl') == null) {\n *   const customCanvas = document.createElement('canvas');\n *   const customBackend = new tf.MathBackendWebGL(customCanvas);\n *   tf.registerBackend('custom-webgl', () => customBackend);\n * }\n * const savedBackend = tf.getBackend();\n * await tf.setBackend('custom-webgl');\n * const gl = tf.backend().gpgpu.gl;\n * const texture = gl.createTexture();\n * const tex2d = gl.TEXTURE_2D;\n * const width = 2;\n * const height = 2;\n *\n * gl.bindTexture(tex2d, texture);\n * gl.texParameteri(tex2d, gl.TEXTURE_WRAP_S, gl.CLAMP_TO_EDGE);\n * gl.texParameteri(tex2d, gl.TEXTURE_WRAP_T, gl.CLAMP_TO_EDGE);\n * gl.texParameteri(tex2d, gl.TEXTURE_MIN_FILTER, gl.NEAREST);\n * gl.texParameteri(tex2d, gl.TEXTURE_MAG_FILTER, gl.NEAREST);\n * gl.texImage2D(\n *   tex2d, 0, gl.RGBA32F, // internalFormat\n *   width, height, 0,\n *   gl.RGBA, // textureFormat\n *   gl.FLOAT, // textureType\n *   new Float32Array([0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15])\n * );\n *\n * // Currently, the `texture` has 4 pixels:\n * // Pixel0 is {R:0, G:1, B:2, A:3}\n * // Pixel1 is {R:4, G:5, B:6, A:7}\n * // Pixel2 is {R:8, G:9, B:10, A:11}\n * // Pixel3 is {R:12, G:13, B:14, A:15}\n *\n * const logicalShape = [height * width * 2];\n * const a = tf.tensor({texture, height, width, channels: 'BR'}, logicalShape);\n * a.print();\n * // Tensor value will be [2, 0, 6, 4, 10, 8, 14, 12], since [2, 0] is the\n * // values of 'B' and 'R' channels of Pixel0, [6, 4] is the values of 'B' and\n * 'R'\n * // channels of Pixel1...\n *\n * // For postprocessing on the GPU, it's possible to retrieve the texture\n * // backing any tensor by calling the tensor's `dataToGPU` method like\n * // so:\n *\n * const tex = a.dataToGPU();\n * await tf.setBackend(savedBackend);\n * ```\n *\n * ```js\n * // Pass a `WebGPUData` object and specify a shape yourself.\n *\n * // This makes it possible for TF.js applications to avoid GPU / CPU sync.\n * // For example, if your application includes a preprocessing step on the GPU,\n * // you could upload the GPU output directly to TF.js, rather than first\n * // downloading the values. Unlike WebGL, this optionally supports zero copy\n * // by WebGPUData.zeroCopy. When zeroCopy is false or undefined(default), this\n * // passing GPUBuffer can be destroyed after tensor is created. When zeroCopy\n * // is true, this GPUBuffer is bound directly by the tensor, so do not destroy\n * // this GPUBuffer until all access is done.\n *\n * // Example for WebGPU:\n * function createGPUBufferFromData(device, data, dtype) {\n *   const bytesPerElement = 4;\n *   const sizeInBytes = data.length * bytesPerElement;\n *\n *   const gpuWriteBuffer = device.createBuffer({\n *     mappedAtCreation: true,\n *     size: sizeInBytes,\n *     usage: GPUBufferUsage.MAP_WRITE | GPUBufferUsage.COPY_SRC\n *   });\n *   const arrayBuffer = gpuWriteBuffer.getMappedRange();\n *   if (dtype === 'float32') {\n *     new Float32Array(arrayBuffer).set(data);\n *   } else if (dtype === 'int32') {\n *     new Int32Array(arrayBuffer).set(data);\n *   } else {\n *     throw new Error(\n *         `Creating tensor from GPUBuffer only supports` +\n *         `'float32'|'int32' dtype, while the dtype is ${dtype}.`);\n *   }\n *   gpuWriteBuffer.unmap();\n *\n *   const gpuReadBuffer = device.createBuffer({\n *     mappedAtCreation: false,\n *     size: sizeInBytes,\n *     usage: GPUBufferUsage.COPY_DST | GPUBufferUsage.STORAGE |\n *         GPUBufferUsage.COPY_SRC\n *   });\n *\n *   const copyEncoder = device.createCommandEncoder();\n *   copyEncoder.copyBufferToBuffer(\n *       gpuWriteBuffer, 0, gpuReadBuffer, 0, sizeInBytes);\n *   const copyCommands = copyEncoder.finish();\n *   device.queue.submit([copyCommands]);\n *   gpuWriteBuffer.destroy();\n *   return gpuReadBuffer;\n * }\n *\n * const savedBackend = tf.getBackend();\n * await tf.setBackend('webgpu').catch(\n *     () => {throw new Error(\n *         'Failed to use WebGPU backend. Please use Chrome Canary to run.')});\n * const dtype = 'float32';\n * const device = tf.backend().device;\n * const aData = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16];\n * const bData = [1, 2, 3, 4, 1, 2, 3, 4, 1, 2, 3, 4, 1, 2, 3, 4];\n * const expected = [2, 4, 6, 8, 6, 8, 10, 12, 10, 12, 14, 16, 14, 16, 18, 20];\n * const aBuffer = createGPUBufferFromData(device, aData, dtype);\n * const shape = [aData.length];\n * // To use zeroCopy, use {buffer: aBuffer, zeroCopy: true} instead and destroy\n * // aBuffer untill all access is done.\n * const a = tf.tensor({buffer: aBuffer}, shape, dtype);\n * const b = tf.tensor(bData, shape, dtype);\n * const result = tf.add(a, b);\n * result.print();\n * a.dispose();\n * b.dispose();\n * result.dispose();\n * aBuffer.destroy();\n * await tf.setBackend(savedBackend);\n * ```\n * @param values The values of the tensor. Can be nested array of numbers,\n * or a flat array, or a `TypedArray`(At the moment it supports Uint8Array,\n * Uint8ClampedArray, Int32Array, Float32Array) data types, or a `WebGLData`\n * object, or a `WebGPUData` object. If the values are strings, they will be\n * encoded as utf-8 and kept as `Uint8Array[]`. If the values is a `WebGLData`\n * object, the dtype could only be 'float32' or 'int32' and the object has to\n * have: 1. texture, a `WebGLTexture`, the texture must share the same\n * `WebGLRenderingContext` with TFJS's WebGL backend (you could create a custom\n * WebGL backend from your texture's canvas) and the internal texture format\n * for the input texture must be floating point or normalized integer; 2.\n * height, the height of the texture; 3. width, the width of the texture; 4.\n * channels, a non-empty subset of 'RGBA', indicating the values of which\n * channels will be passed to the tensor, such as 'R' or 'BR' (The order of the\n * channels affect the order of tensor values. ). (If the values passed from\n * texture is less than the tensor size, zeros will be padded at the rear.). If\n * the values is a `WebGPUData` object, the dtype could only be 'float32' or\n * 'int32 and the object has to have: buffer, a `GPUBuffer`. The buffer must:\n * 1. share the same `GPUDevice` with TFJS's WebGPU backend; 2. buffer.usage\n * should at least support GPUBufferUsage.STORAGE | GPUBufferUsage.COPY_SRC; 3.\n * buffer.size should not be smaller than the byte size of tensor shape.\n * WebGPUData optionally supports zero copy by flag zeroCopy. When zeroCopy is\n * false or undefined(default),this passing GPUBuffer can be destroyed after\n * tensor is created. When zeroCopy is true, this GPUBuffer is bound directly\n * by the tensor, so do not destroy this GPUBuffer until all access is done.\n * @param shape The shape of the tensor. Optional. If not provided,\n *   it is inferred from `values`.\n * @param dtype The data type.\n *\n * @doc {heading: 'Tensors', subheading: 'Creation'}\n */\nexport function tensor(values, shape, dtype) {\n  const inferredShape = inferShape(values, dtype);\n  return makeTensor(values, shape, inferredShape, dtype);\n}", "map": {"version": 3, "names": ["inferShape", "makeTensor", "tensor", "values", "shape", "dtype", "inferredShape"], "sources": ["C:\\tfjs-core\\src\\ops\\tensor.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2018 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {Tensor} from '../tensor';\nimport {inferShape} from '../tensor_util_env';\nimport {TensorLike} from '../types';\nimport {DataType, Rank, ShapeMap, WebGLData, WebGPUData} from '../types';\n\nimport {makeTensor} from './tensor_ops_util';\n\n/**\n * Creates a `tf.Tensor` with the provided values, shape and dtype.\n *\n * ```js\n * // Pass an array of values to create a vector.\n * tf.tensor([1, 2, 3, 4]).print();\n * ```\n *\n * ```js\n * // Pass a nested array of values to make a matrix or a higher\n * // dimensional tensor.\n * tf.tensor([[1, 2], [3, 4]]).print();\n * ```\n *\n * ```js\n * // Pass a flat array and specify a shape yourself.\n * tf.tensor([1, 2, 3, 4], [2, 2]).print();\n * ```\n *\n * ```js\n * // Pass a `WebGLData` object and specify a shape yourself.\n *\n * // This makes it possible for TF.js applications to avoid GPU / CPU sync.\n * // For example, if your application includes a preprocessing step on the GPU,\n * // you could upload the GPU output directly to TF.js, rather than first\n * // downloading the values.\n *\n * // Example for WebGL2:\n * if (tf.findBackend('custom-webgl') == null) {\n *   const customCanvas = document.createElement('canvas');\n *   const customBackend = new tf.MathBackendWebGL(customCanvas);\n *   tf.registerBackend('custom-webgl', () => customBackend);\n * }\n * const savedBackend = tf.getBackend();\n * await tf.setBackend('custom-webgl');\n * const gl = tf.backend().gpgpu.gl;\n * const texture = gl.createTexture();\n * const tex2d = gl.TEXTURE_2D;\n * const width = 2;\n * const height = 2;\n *\n * gl.bindTexture(tex2d, texture);\n * gl.texParameteri(tex2d, gl.TEXTURE_WRAP_S, gl.CLAMP_TO_EDGE);\n * gl.texParameteri(tex2d, gl.TEXTURE_WRAP_T, gl.CLAMP_TO_EDGE);\n * gl.texParameteri(tex2d, gl.TEXTURE_MIN_FILTER, gl.NEAREST);\n * gl.texParameteri(tex2d, gl.TEXTURE_MAG_FILTER, gl.NEAREST);\n * gl.texImage2D(\n *   tex2d, 0, gl.RGBA32F, // internalFormat\n *   width, height, 0,\n *   gl.RGBA, // textureFormat\n *   gl.FLOAT, // textureType\n *   new Float32Array([0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15])\n * );\n *\n * // Currently, the `texture` has 4 pixels:\n * // Pixel0 is {R:0, G:1, B:2, A:3}\n * // Pixel1 is {R:4, G:5, B:6, A:7}\n * // Pixel2 is {R:8, G:9, B:10, A:11}\n * // Pixel3 is {R:12, G:13, B:14, A:15}\n *\n * const logicalShape = [height * width * 2];\n * const a = tf.tensor({texture, height, width, channels: 'BR'}, logicalShape);\n * a.print();\n * // Tensor value will be [2, 0, 6, 4, 10, 8, 14, 12], since [2, 0] is the\n * // values of 'B' and 'R' channels of Pixel0, [6, 4] is the values of 'B' and\n * 'R'\n * // channels of Pixel1...\n *\n * // For postprocessing on the GPU, it's possible to retrieve the texture\n * // backing any tensor by calling the tensor's `dataToGPU` method like\n * // so:\n *\n * const tex = a.dataToGPU();\n * await tf.setBackend(savedBackend);\n * ```\n *\n * ```js\n * // Pass a `WebGPUData` object and specify a shape yourself.\n *\n * // This makes it possible for TF.js applications to avoid GPU / CPU sync.\n * // For example, if your application includes a preprocessing step on the GPU,\n * // you could upload the GPU output directly to TF.js, rather than first\n * // downloading the values. Unlike WebGL, this optionally supports zero copy\n * // by WebGPUData.zeroCopy. When zeroCopy is false or undefined(default), this\n * // passing GPUBuffer can be destroyed after tensor is created. When zeroCopy\n * // is true, this GPUBuffer is bound directly by the tensor, so do not destroy\n * // this GPUBuffer until all access is done.\n *\n * // Example for WebGPU:\n * function createGPUBufferFromData(device, data, dtype) {\n *   const bytesPerElement = 4;\n *   const sizeInBytes = data.length * bytesPerElement;\n *\n *   const gpuWriteBuffer = device.createBuffer({\n *     mappedAtCreation: true,\n *     size: sizeInBytes,\n *     usage: GPUBufferUsage.MAP_WRITE | GPUBufferUsage.COPY_SRC\n *   });\n *   const arrayBuffer = gpuWriteBuffer.getMappedRange();\n *   if (dtype === 'float32') {\n *     new Float32Array(arrayBuffer).set(data);\n *   } else if (dtype === 'int32') {\n *     new Int32Array(arrayBuffer).set(data);\n *   } else {\n *     throw new Error(\n *         `Creating tensor from GPUBuffer only supports` +\n *         `'float32'|'int32' dtype, while the dtype is ${dtype}.`);\n *   }\n *   gpuWriteBuffer.unmap();\n *\n *   const gpuReadBuffer = device.createBuffer({\n *     mappedAtCreation: false,\n *     size: sizeInBytes,\n *     usage: GPUBufferUsage.COPY_DST | GPUBufferUsage.STORAGE |\n *         GPUBufferUsage.COPY_SRC\n *   });\n *\n *   const copyEncoder = device.createCommandEncoder();\n *   copyEncoder.copyBufferToBuffer(\n *       gpuWriteBuffer, 0, gpuReadBuffer, 0, sizeInBytes);\n *   const copyCommands = copyEncoder.finish();\n *   device.queue.submit([copyCommands]);\n *   gpuWriteBuffer.destroy();\n *   return gpuReadBuffer;\n * }\n *\n * const savedBackend = tf.getBackend();\n * await tf.setBackend('webgpu').catch(\n *     () => {throw new Error(\n *         'Failed to use WebGPU backend. Please use Chrome Canary to run.')});\n * const dtype = 'float32';\n * const device = tf.backend().device;\n * const aData = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16];\n * const bData = [1, 2, 3, 4, 1, 2, 3, 4, 1, 2, 3, 4, 1, 2, 3, 4];\n * const expected = [2, 4, 6, 8, 6, 8, 10, 12, 10, 12, 14, 16, 14, 16, 18, 20];\n * const aBuffer = createGPUBufferFromData(device, aData, dtype);\n * const shape = [aData.length];\n * // To use zeroCopy, use {buffer: aBuffer, zeroCopy: true} instead and destroy\n * // aBuffer untill all access is done.\n * const a = tf.tensor({buffer: aBuffer}, shape, dtype);\n * const b = tf.tensor(bData, shape, dtype);\n * const result = tf.add(a, b);\n * result.print();\n * a.dispose();\n * b.dispose();\n * result.dispose();\n * aBuffer.destroy();\n * await tf.setBackend(savedBackend);\n * ```\n * @param values The values of the tensor. Can be nested array of numbers,\n * or a flat array, or a `TypedArray`(At the moment it supports Uint8Array,\n * Uint8ClampedArray, Int32Array, Float32Array) data types, or a `WebGLData`\n * object, or a `WebGPUData` object. If the values are strings, they will be\n * encoded as utf-8 and kept as `Uint8Array[]`. If the values is a `WebGLData`\n * object, the dtype could only be 'float32' or 'int32' and the object has to\n * have: 1. texture, a `WebGLTexture`, the texture must share the same\n * `WebGLRenderingContext` with TFJS's WebGL backend (you could create a custom\n * WebGL backend from your texture's canvas) and the internal texture format\n * for the input texture must be floating point or normalized integer; 2.\n * height, the height of the texture; 3. width, the width of the texture; 4.\n * channels, a non-empty subset of 'RGBA', indicating the values of which\n * channels will be passed to the tensor, such as 'R' or 'BR' (The order of the \n * channels affect the order of tensor values. ). (If the values passed from \n * texture is less than the tensor size, zeros will be padded at the rear.). If \n * the values is a `WebGPUData` object, the dtype could only be 'float32' or\n * 'int32 and the object has to have: buffer, a `GPUBuffer`. The buffer must:\n * 1. share the same `GPUDevice` with TFJS's WebGPU backend; 2. buffer.usage\n * should at least support GPUBufferUsage.STORAGE | GPUBufferUsage.COPY_SRC; 3.\n * buffer.size should not be smaller than the byte size of tensor shape.\n * WebGPUData optionally supports zero copy by flag zeroCopy. When zeroCopy is\n * false or undefined(default),this passing GPUBuffer can be destroyed after\n * tensor is created. When zeroCopy is true, this GPUBuffer is bound directly\n * by the tensor, so do not destroy this GPUBuffer until all access is done.\n * @param shape The shape of the tensor. Optional. If not provided,\n *   it is inferred from `values`.\n * @param dtype The data type.\n *\n * @doc {heading: 'Tensors', subheading: 'Creation'}\n */\nexport function tensor<R extends Rank>(\n    values: TensorLike|WebGLData|WebGPUData, shape?: ShapeMap[R],\n    dtype?: DataType): Tensor<R> {\n  const inferredShape = inferShape(values, dtype);\n  return makeTensor(values, shape, inferredShape, dtype) as Tensor<R>;\n}"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAkBA,SAAQA,UAAU,QAAO,oBAAoB;AAI7C,SAAQC,UAAU,QAAO,mBAAmB;AAE5C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmLA,OAAM,SAAUC,MAAMA,CAClBC,MAAuC,EAAEC,KAAmB,EAC5DC,KAAgB;EAClB,MAAMC,aAAa,GAAGN,UAAU,CAACG,MAAM,EAAEE,KAAK,CAAC;EAC/C,OAAOJ,UAAU,CAACE,MAAM,EAAEC,KAAK,EAAEE,aAAa,EAAED,KAAK,CAAc;AACrE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}