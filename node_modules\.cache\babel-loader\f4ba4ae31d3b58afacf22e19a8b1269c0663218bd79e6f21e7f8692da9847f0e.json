{"ast": null, "code": "/**\n * @license\n * Copyright 2022 Google LLC.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { AdadeltaOptimizer } from './adadelta_optimizer';\nimport { AdagradOptimizer } from './adagrad_optimizer';\nimport { AdamOptimizer } from './adam_optimizer';\nimport { AdamaxOptimizer } from './adamax_optimizer';\nimport { MomentumOptimizer } from './momentum_optimizer';\nimport { RMSPropOptimizer } from './rmsprop_optimizer';\nimport { SGDOptimizer } from './sgd_optimizer';\nimport { registerClass } from '../serialization';\nconst OPTIMIZERS = [AdadeltaOptimizer, AdagradOptimizer, AdamOptimizer, AdamaxOptimizer, MomentumOptimizer, RMSPropOptimizer, SGDOptimizer];\nexport function registerOptimizers() {\n  for (const optimizer of OPTIMIZERS) {\n    registerClass(optimizer);\n  }\n}", "map": {"version": 3, "names": ["AdadeltaOptimizer", "AdagradOptimizer", "AdamOptimizer", "AdamaxOptimizer", "MomentumOptimizer", "RMSPropOptimizer", "SGDOptimizer", "registerClass", "OPTIMIZERS", "registerOptimizers", "optimizer"], "sources": ["C:\\tfjs-core\\src\\optimizers\\register_optimizers.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2022 Google LLC.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {AdadeltaOptimizer} from './adadelta_optimizer';\nimport {AdagradOptimizer} from './adagrad_optimizer';\nimport {AdamOptimizer} from './adam_optimizer';\nimport {AdamaxOptimizer} from './adamax_optimizer';\nimport {MomentumOptimizer} from './momentum_optimizer';\nimport {RMSPropOptimizer} from './rmsprop_optimizer';\nimport {SGDOptimizer} from './sgd_optimizer';\nimport {registerClass} from '../serialization';\n\nconst OPTIMIZERS = [\n  AdadeltaOptimizer,\n  AdagradOptimizer,\n  AdamOptimizer,\n  AdamaxOptimizer,\n  MomentumOptimizer,\n  RMSPropOptimizer,\n  SGDOptimizer,\n];\n\nexport function registerOptimizers() {\n  for (const optimizer of OPTIMIZERS) {\n    registerClass(optimizer);\n  }\n}\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAiBA,SAAQA,iBAAiB,QAAO,sBAAsB;AACtD,SAAQC,gBAAgB,QAAO,qBAAqB;AACpD,SAAQC,aAAa,QAAO,kBAAkB;AAC9C,SAAQC,eAAe,QAAO,oBAAoB;AAClD,SAAQC,iBAAiB,QAAO,sBAAsB;AACtD,SAAQC,gBAAgB,QAAO,qBAAqB;AACpD,SAAQC,YAAY,QAAO,iBAAiB;AAC5C,SAAQC,aAAa,QAAO,kBAAkB;AAE9C,MAAMC,UAAU,GAAG,CACjBR,iBAAiB,EACjBC,gBAAgB,EAChBC,aAAa,EACbC,eAAe,EACfC,iBAAiB,EACjBC,gBAAgB,EAChBC,YAAY,CACb;AAED,OAAM,SAAUG,kBAAkBA,CAAA;EAChC,KAAK,MAAMC,SAAS,IAAIF,UAAU,EAAE;IAClCD,aAAa,CAACG,SAAS,CAAC;;AAE5B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}