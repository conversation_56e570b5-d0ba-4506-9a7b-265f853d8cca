{"ast": null, "code": "import tagTester from './_tagTester.js';\nimport { isIE11 } from './_stringTagBug.js';\nimport { ie11fingerprint, mapMethods } from './_methodFingerprint.js';\nexport default isIE11 ? ie11fingerprint(mapMethods) : tagTester('Map');", "map": {"version": 3, "names": ["tagTester", "isIE11", "ie11fingerprint", "mapMethods"], "sources": ["C:/tmsft/node_modules/underscore/modules/isMap.js"], "sourcesContent": ["import tagTester from './_tagTester.js';\nimport { isIE11 } from './_stringTagBug.js';\nimport { ie11fingerprint, mapMethods }  from './_methodFingerprint.js';\n\nexport default isIE11 ? ie11fingerprint(mapMethods) : tagTester('Map');\n"], "mappings": "AAAA,OAAOA,SAAS,MAAM,iBAAiB;AACvC,SAASC,MAAM,QAAQ,oBAAoB;AAC3C,SAASC,eAAe,EAAEC,UAAU,QAAS,yBAAyB;AAEtE,eAAeF,MAAM,GAAGC,eAAe,CAACC,UAAU,CAAC,GAAGH,SAAS,CAAC,KAAK,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}