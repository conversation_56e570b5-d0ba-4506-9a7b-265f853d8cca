{"ast": null, "code": "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { util } from '@tensorflow/tfjs-core';\nimport { FFTProgram } from '../fft_gpu';\nimport { complex } from './Complex';\nimport { reshape } from './Reshape';\nexport function fftImpl(x, inverse, backend) {\n  const xData = backend.texData.get(x.dataId);\n  const inputSize = util.sizeFromShape(x.shape);\n  // Collapse all outer dimensions to a single batch dimension.\n  const innerDimensionSize = x.shape[x.shape.length - 1];\n  const batch = inputSize / innerDimensionSize;\n  const input2D = reshape({\n    inputs: {\n      x\n    },\n    backend,\n    attrs: {\n      shape: [batch, innerDimensionSize]\n    }\n  });\n  const xShape = input2D.shape;\n  const realProgram = new FFTProgram('real', xShape, inverse);\n  const imagProgram = new FFTProgram('imag', xShape, inverse);\n  const inputs = [{\n    dataId: xData.complexTensorInfos.real.dataId,\n    dtype: xData.complexTensorInfos.real.dtype,\n    shape: xShape\n  }, {\n    dataId: xData.complexTensorInfos.imag.dataId,\n    dtype: xData.complexTensorInfos.imag.dtype,\n    shape: xShape\n  }];\n  const realPart = backend.runWebGLProgram(realProgram, inputs, 'float32');\n  const imagPart = backend.runWebGLProgram(imagProgram, inputs, 'float32');\n  const complexOutput = complex({\n    inputs: {\n      real: realPart,\n      imag: imagPart\n    },\n    backend\n  });\n  backend.disposeIntermediateTensorInfo(realPart);\n  backend.disposeIntermediateTensorInfo(imagPart);\n  const complexOutputReshaped = reshape({\n    inputs: {\n      x: complexOutput\n    },\n    backend,\n    attrs: {\n      shape: x.shape\n    }\n  });\n  backend.disposeIntermediateTensorInfo(input2D);\n  backend.disposeIntermediateTensorInfo(complexOutput);\n  return complexOutputReshaped;\n}", "map": {"version": 3, "names": ["util", "FFTProgram", "complex", "reshape", "fftImpl", "x", "inverse", "backend", "xData", "texData", "get", "dataId", "inputSize", "sizeFromShape", "shape", "innerDimensionSize", "length", "batch", "input2D", "inputs", "attrs", "xShape", "realProgram", "imagProgram", "complexTensorInfos", "real", "dtype", "imag", "realPart", "runWebGLProgram", "imagPart", "complexOutput", "disposeIntermediateTensorInfo", "complexOutputReshaped"], "sources": ["C:\\tfjs-backend-webgl\\src\\kernels\\FFT_impl.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {TensorInfo, util} from '@tensorflow/tfjs-core';\n\nimport {MathBackendWebGL} from '../backend_webgl';\nimport {FFTProgram} from '../fft_gpu';\n\nimport {complex} from './Complex';\nimport {reshape} from './Reshape';\n\nexport function fftImpl(\n    x: TensorInfo, inverse: boolean, backend: MathBackendWebGL): TensorInfo {\n  const xData = backend.texData.get(x.dataId);\n\n  const inputSize = util.sizeFromShape(x.shape);\n  // Collapse all outer dimensions to a single batch dimension.\n  const innerDimensionSize = x.shape[x.shape.length - 1];\n  const batch = inputSize / innerDimensionSize;\n\n  const input2D = reshape(\n      {inputs: {x}, backend, attrs: {shape: [batch, innerDimensionSize]}});\n\n  const xShape = input2D.shape as [number, number];\n  const realProgram = new FFTProgram('real', xShape, inverse);\n  const imagProgram = new FFTProgram('imag', xShape, inverse);\n\n  const inputs = [\n    {\n      dataId: xData.complexTensorInfos.real.dataId,\n      dtype: xData.complexTensorInfos.real.dtype,\n      shape: xShape\n    },\n    {\n      dataId: xData.complexTensorInfos.imag.dataId,\n      dtype: xData.complexTensorInfos.imag.dtype,\n      shape: xShape\n    }\n  ];\n\n  const realPart = backend.runWebGLProgram(realProgram, inputs, 'float32');\n  const imagPart = backend.runWebGLProgram(imagProgram, inputs, 'float32');\n\n  const complexOutput =\n      complex({inputs: {real: realPart, imag: imagPart}, backend});\n\n  backend.disposeIntermediateTensorInfo(realPart);\n  backend.disposeIntermediateTensorInfo(imagPart);\n\n  const complexOutputReshaped =\n      reshape({inputs: {x: complexOutput}, backend, attrs: {shape: x.shape}});\n\n  backend.disposeIntermediateTensorInfo(input2D);\n  backend.disposeIntermediateTensorInfo(complexOutput);\n  return complexOutputReshaped;\n}\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAiBA,SAAoBA,IAAI,QAAO,uBAAuB;AAGtD,SAAQC,UAAU,QAAO,YAAY;AAErC,SAAQC,OAAO,QAAO,WAAW;AACjC,SAAQC,OAAO,QAAO,WAAW;AAEjC,OAAM,SAAUC,OAAOA,CACnBC,CAAa,EAAEC,OAAgB,EAAEC,OAAyB;EAC5D,MAAMC,KAAK,GAAGD,OAAO,CAACE,OAAO,CAACC,GAAG,CAACL,CAAC,CAACM,MAAM,CAAC;EAE3C,MAAMC,SAAS,GAAGZ,IAAI,CAACa,aAAa,CAACR,CAAC,CAACS,KAAK,CAAC;EAC7C;EACA,MAAMC,kBAAkB,GAAGV,CAAC,CAACS,KAAK,CAACT,CAAC,CAACS,KAAK,CAACE,MAAM,GAAG,CAAC,CAAC;EACtD,MAAMC,KAAK,GAAGL,SAAS,GAAGG,kBAAkB;EAE5C,MAAMG,OAAO,GAAGf,OAAO,CACnB;IAACgB,MAAM,EAAE;MAACd;IAAC,CAAC;IAAEE,OAAO;IAAEa,KAAK,EAAE;MAACN,KAAK,EAAE,CAACG,KAAK,EAAEF,kBAAkB;IAAC;EAAC,CAAC,CAAC;EAExE,MAAMM,MAAM,GAAGH,OAAO,CAACJ,KAAyB;EAChD,MAAMQ,WAAW,GAAG,IAAIrB,UAAU,CAAC,MAAM,EAAEoB,MAAM,EAAEf,OAAO,CAAC;EAC3D,MAAMiB,WAAW,GAAG,IAAItB,UAAU,CAAC,MAAM,EAAEoB,MAAM,EAAEf,OAAO,CAAC;EAE3D,MAAMa,MAAM,GAAG,CACb;IACER,MAAM,EAAEH,KAAK,CAACgB,kBAAkB,CAACC,IAAI,CAACd,MAAM;IAC5Ce,KAAK,EAAElB,KAAK,CAACgB,kBAAkB,CAACC,IAAI,CAACC,KAAK;IAC1CZ,KAAK,EAAEO;GACR,EACD;IACEV,MAAM,EAAEH,KAAK,CAACgB,kBAAkB,CAACG,IAAI,CAAChB,MAAM;IAC5Ce,KAAK,EAAElB,KAAK,CAACgB,kBAAkB,CAACG,IAAI,CAACD,KAAK;IAC1CZ,KAAK,EAAEO;GACR,CACF;EAED,MAAMO,QAAQ,GAAGrB,OAAO,CAACsB,eAAe,CAACP,WAAW,EAAEH,MAAM,EAAE,SAAS,CAAC;EACxE,MAAMW,QAAQ,GAAGvB,OAAO,CAACsB,eAAe,CAACN,WAAW,EAAEJ,MAAM,EAAE,SAAS,CAAC;EAExE,MAAMY,aAAa,GACf7B,OAAO,CAAC;IAACiB,MAAM,EAAE;MAACM,IAAI,EAAEG,QAAQ;MAAED,IAAI,EAAEG;IAAQ,CAAC;IAAEvB;EAAO,CAAC,CAAC;EAEhEA,OAAO,CAACyB,6BAA6B,CAACJ,QAAQ,CAAC;EAC/CrB,OAAO,CAACyB,6BAA6B,CAACF,QAAQ,CAAC;EAE/C,MAAMG,qBAAqB,GACvB9B,OAAO,CAAC;IAACgB,MAAM,EAAE;MAACd,CAAC,EAAE0B;IAAa,CAAC;IAAExB,OAAO;IAAEa,KAAK,EAAE;MAACN,KAAK,EAAET,CAAC,CAACS;IAAK;EAAC,CAAC,CAAC;EAE3EP,OAAO,CAACyB,6BAA6B,CAACd,OAAO,CAAC;EAC9CX,OAAO,CAACyB,6BAA6B,CAACD,aAAa,CAAC;EACpD,OAAOE,qBAAqB;AAC9B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}