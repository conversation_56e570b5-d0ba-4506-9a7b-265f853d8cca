{"ast": null, "code": "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { backend_util, GatherV2, util, env } from '@tensorflow/tfjs-core';\nimport { GatherProgram } from '../gather_gpu';\nimport { gatherV2ImplCPU } from '../kernel_utils/shared';\nimport { reshape } from './Reshape';\nexport function gatherV2(args) {\n  const {\n    inputs,\n    backend,\n    attrs\n  } = args;\n  const {\n    x,\n    indices\n  } = inputs;\n  const {\n    axis,\n    batchDims\n  } = attrs;\n  const parsedAxis = util.parseAxisParam(axis, x.shape)[0];\n  if (env().get('DEBUG')) {\n    // In debug mode, throw error when any index is out of bound.\n    // Otherwise, just fill out of bounds with zeroes.\n    const indicesVals = backend.readSync(indices.dataId);\n    const axisDim = x.shape[parsedAxis];\n    for (let i = 0; i < indicesVals.length; ++i) {\n      const index = indicesVals[i];\n      util.assert(index <= axisDim - 1 && index >= 0, () => \"GatherV2: the index value \".concat(index, \" is not in [0, \").concat(axisDim - 1, \"]\"));\n    }\n  }\n  const shapeInfo = backend_util.segment_util.collectGatherOpShapeInfo(x, indices, parsedAxis, batchDims);\n  const indicesSize = util.sizeFromShape(indices.shape);\n  const toDispose = [];\n  const flattenX = reshape({\n    inputs: {\n      x\n    },\n    backend,\n    attrs: {\n      shape: [shapeInfo.batchSize, shapeInfo.outerSize, shapeInfo.dimSize, shapeInfo.sliceSize]\n    }\n  });\n  const flattenIndex = reshape({\n    inputs: {\n      x: indices\n    },\n    backend,\n    attrs: {\n      shape: [shapeInfo.batchSize, indicesSize / shapeInfo.batchSize]\n    }\n  });\n  toDispose.push(flattenX);\n  toDispose.push(flattenIndex);\n  const flattenOutputShape = [shapeInfo.batchSize, shapeInfo.outerSize, indicesSize / shapeInfo.batchSize, shapeInfo.sliceSize];\n  if (backend.shouldExecuteOnCPU([x, indices]) || x.dtype === 'string') {\n    const indicesBuf = backend.bufferSync(flattenIndex);\n    const xBuf = backend.bufferSync(flattenX);\n    const outBuf = gatherV2ImplCPU(xBuf, indicesBuf, flattenOutputShape);\n    toDispose.forEach(t => backend.disposeIntermediateTensorInfo(t));\n    return backend.makeTensorInfo(shapeInfo.outputShape, outBuf.dtype, outBuf.values);\n  }\n  const program = new GatherProgram(flattenX.shape, flattenOutputShape);\n  const res = backend.runWebGLProgram(program, [flattenX, flattenIndex], flattenX.dtype);\n  toDispose.push(res);\n  const reshaped = reshape({\n    inputs: {\n      x: res\n    },\n    backend,\n    attrs: {\n      shape: shapeInfo.outputShape\n    }\n  });\n  toDispose.forEach(t => backend.disposeIntermediateTensorInfo(t));\n  return reshaped;\n}\nexport const gatherV2Config = {\n  kernelName: GatherV2,\n  backendName: 'webgl',\n  kernelFunc: gatherV2\n};", "map": {"version": 3, "names": ["backend_util", "GatherV2", "util", "env", "GatherProgram", "gatherV2ImplCPU", "reshape", "gatherV2", "args", "inputs", "backend", "attrs", "x", "indices", "axis", "batchDims", "parsedAxis", "parseAxisParam", "shape", "get", "indicesVals", "readSync", "dataId", "axisDim", "i", "length", "index", "assert", "concat", "shapeInfo", "segment_util", "collectGatherOpShapeInfo", "indicesSize", "sizeFromShape", "toDispose", "flattenX", "batchSize", "outerSize", "dimSize", "sliceSize", "flattenIndex", "push", "flattenOutputShape", "shouldExecuteOnCPU", "dtype", "indicesBuf", "bufferSync", "xBuf", "outBuf", "for<PERSON>ach", "t", "disposeIntermediateTensorInfo", "makeTensorInfo", "outputShape", "values", "program", "res", "runWebGLProgram", "reshaped", "gatherV2Config", "kernelName", "backendName", "kernelFunc"], "sources": ["C:\\tfjs-backend-webgl\\src\\kernels\\GatherV2.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {backend_util, GatherV2, GatherV2Attrs, GatherV2Inputs, KernelConfig, KernelFunc, TensorInfo, TypedArray, util, env} from '@tensorflow/tfjs-core';\n\nimport {MathBackendWebGL} from '../backend_webgl';\nimport {GatherProgram, GatherShape} from '../gather_gpu';\nimport {gatherV2ImplCPU} from '../kernel_utils/shared';\n\nimport {reshape} from './Reshape';\n\nexport function gatherV2(args: {\n  inputs: GatherV2Inputs,\n  backend: MathBackendWebGL,\n  attrs: GatherV2Attrs\n}): TensorInfo {\n  const {inputs, backend, attrs} = args;\n  const {x, indices} = inputs;\n  const {axis, batchDims} = attrs;\n\n  const parsedAxis = util.parseAxisParam(axis, x.shape)[0];\n  if (env().get('DEBUG')) {\n    // In debug mode, throw error when any index is out of bound.\n    // Otherwise, just fill out of bounds with zeroes.\n    const indicesVals = backend.readSync(indices.dataId) as TypedArray;\n    const axisDim = x.shape[parsedAxis];\n    for (let i = 0; i < indicesVals.length; ++i) {\n      const index = indicesVals[i];\n      util.assert(\n        index <= axisDim - 1 && index >= 0,\n        () =>\n          `GatherV2: the index value ${index} is not in [0, ${axisDim - 1}]`);\n    }\n  }\n\n  const shapeInfo = backend_util.segment_util.collectGatherOpShapeInfo(\n      x, indices, parsedAxis, batchDims);\n\n  const indicesSize = util.sizeFromShape(indices.shape);\n\n  const toDispose = [];\n\n  const flattenX = reshape({\n    inputs: {x},\n    backend,\n    attrs: {\n      shape: [\n        shapeInfo.batchSize, shapeInfo.outerSize, shapeInfo.dimSize,\n        shapeInfo.sliceSize\n      ]\n    }\n  });\n\n  const flattenIndex = reshape({\n    inputs: {x: indices},\n    backend,\n    attrs: {shape: [shapeInfo.batchSize, indicesSize / shapeInfo.batchSize]}\n  });\n\n  toDispose.push(flattenX);\n  toDispose.push(flattenIndex);\n\n  const flattenOutputShape = [\n    shapeInfo.batchSize, shapeInfo.outerSize, indicesSize / shapeInfo.batchSize,\n    shapeInfo.sliceSize\n  ];\n\n  if (backend.shouldExecuteOnCPU([x, indices]) || x.dtype === 'string') {\n    const indicesBuf = backend.bufferSync(flattenIndex);\n    const xBuf = backend.bufferSync(flattenX);\n    const outBuf = gatherV2ImplCPU(xBuf, indicesBuf, flattenOutputShape);\n\n    toDispose.forEach(t => backend.disposeIntermediateTensorInfo(t));\n\n    return backend.makeTensorInfo(\n        shapeInfo.outputShape, outBuf.dtype, outBuf.values as TypedArray);\n  }\n\n  const program = new GatherProgram(flattenX.shape as GatherShape,\n                                    flattenOutputShape as GatherShape);\n  const res = backend.runWebGLProgram(\n      program, [flattenX, flattenIndex], flattenX.dtype);\n  toDispose.push(res);\n\n  const reshaped = reshape(\n      {inputs: {x: res}, backend, attrs: {shape: shapeInfo.outputShape}});\n  toDispose.forEach(t => backend.disposeIntermediateTensorInfo(t));\n  return reshaped;\n}\n\nexport const gatherV2Config: KernelConfig = {\n  kernelName: GatherV2,\n  backendName: 'webgl',\n  kernelFunc: gatherV2 as unknown as KernelFunc\n};\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAiBA,SAAQA,YAAY,EAAEC,QAAQ,EAAmFC,IAAI,EAAEC,GAAG,QAAO,uBAAuB;AAGxJ,SAAQC,aAAa,QAAoB,eAAe;AACxD,SAAQC,eAAe,QAAO,wBAAwB;AAEtD,SAAQC,OAAO,QAAO,WAAW;AAEjC,OAAM,SAAUC,QAAQA,CAACC,IAIxB;EACC,MAAM;IAACC,MAAM;IAAEC,OAAO;IAAEC;EAAK,CAAC,GAAGH,IAAI;EACrC,MAAM;IAACI,CAAC;IAAEC;EAAO,CAAC,GAAGJ,MAAM;EAC3B,MAAM;IAACK,IAAI;IAAEC;EAAS,CAAC,GAAGJ,KAAK;EAE/B,MAAMK,UAAU,GAAGd,IAAI,CAACe,cAAc,CAACH,IAAI,EAAEF,CAAC,CAACM,KAAK,CAAC,CAAC,CAAC,CAAC;EACxD,IAAIf,GAAG,EAAE,CAACgB,GAAG,CAAC,OAAO,CAAC,EAAE;IACtB;IACA;IACA,MAAMC,WAAW,GAAGV,OAAO,CAACW,QAAQ,CAACR,OAAO,CAACS,MAAM,CAAe;IAClE,MAAMC,OAAO,GAAGX,CAAC,CAACM,KAAK,CAACF,UAAU,CAAC;IACnC,KAAK,IAAIQ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,WAAW,CAACK,MAAM,EAAE,EAAED,CAAC,EAAE;MAC3C,MAAME,KAAK,GAAGN,WAAW,CAACI,CAAC,CAAC;MAC5BtB,IAAI,CAACyB,MAAM,CACTD,KAAK,IAAIH,OAAO,GAAG,CAAC,IAAIG,KAAK,IAAI,CAAC,EAClC,mCAAAE,MAAA,CAC+BF,KAAK,qBAAAE,MAAA,CAAkBL,OAAO,GAAG,CAAC,MAAG,CAAC;;;EAI3E,MAAMM,SAAS,GAAG7B,YAAY,CAAC8B,YAAY,CAACC,wBAAwB,CAChEnB,CAAC,EAAEC,OAAO,EAAEG,UAAU,EAAED,SAAS,CAAC;EAEtC,MAAMiB,WAAW,GAAG9B,IAAI,CAAC+B,aAAa,CAACpB,OAAO,CAACK,KAAK,CAAC;EAErD,MAAMgB,SAAS,GAAG,EAAE;EAEpB,MAAMC,QAAQ,GAAG7B,OAAO,CAAC;IACvBG,MAAM,EAAE;MAACG;IAAC,CAAC;IACXF,OAAO;IACPC,KAAK,EAAE;MACLO,KAAK,EAAE,CACLW,SAAS,CAACO,SAAS,EAAEP,SAAS,CAACQ,SAAS,EAAER,SAAS,CAACS,OAAO,EAC3DT,SAAS,CAACU,SAAS;;GAGxB,CAAC;EAEF,MAAMC,YAAY,GAAGlC,OAAO,CAAC;IAC3BG,MAAM,EAAE;MAACG,CAAC,EAAEC;IAAO,CAAC;IACpBH,OAAO;IACPC,KAAK,EAAE;MAACO,KAAK,EAAE,CAACW,SAAS,CAACO,SAAS,EAAEJ,WAAW,GAAGH,SAAS,CAACO,SAAS;IAAC;GACxE,CAAC;EAEFF,SAAS,CAACO,IAAI,CAACN,QAAQ,CAAC;EACxBD,SAAS,CAACO,IAAI,CAACD,YAAY,CAAC;EAE5B,MAAME,kBAAkB,GAAG,CACzBb,SAAS,CAACO,SAAS,EAAEP,SAAS,CAACQ,SAAS,EAAEL,WAAW,GAAGH,SAAS,CAACO,SAAS,EAC3EP,SAAS,CAACU,SAAS,CACpB;EAED,IAAI7B,OAAO,CAACiC,kBAAkB,CAAC,CAAC/B,CAAC,EAAEC,OAAO,CAAC,CAAC,IAAID,CAAC,CAACgC,KAAK,KAAK,QAAQ,EAAE;IACpE,MAAMC,UAAU,GAAGnC,OAAO,CAACoC,UAAU,CAACN,YAAY,CAAC;IACnD,MAAMO,IAAI,GAAGrC,OAAO,CAACoC,UAAU,CAACX,QAAQ,CAAC;IACzC,MAAMa,MAAM,GAAG3C,eAAe,CAAC0C,IAAI,EAAEF,UAAU,EAAEH,kBAAkB,CAAC;IAEpER,SAAS,CAACe,OAAO,CAACC,CAAC,IAAIxC,OAAO,CAACyC,6BAA6B,CAACD,CAAC,CAAC,CAAC;IAEhE,OAAOxC,OAAO,CAAC0C,cAAc,CACzBvB,SAAS,CAACwB,WAAW,EAAEL,MAAM,CAACJ,KAAK,EAAEI,MAAM,CAACM,MAAoB,CAAC;;EAGvE,MAAMC,OAAO,GAAG,IAAInD,aAAa,CAAC+B,QAAQ,CAACjB,KAAoB,EAC7BwB,kBAAiC,CAAC;EACpE,MAAMc,GAAG,GAAG9C,OAAO,CAAC+C,eAAe,CAC/BF,OAAO,EAAE,CAACpB,QAAQ,EAAEK,YAAY,CAAC,EAAEL,QAAQ,CAACS,KAAK,CAAC;EACtDV,SAAS,CAACO,IAAI,CAACe,GAAG,CAAC;EAEnB,MAAME,QAAQ,GAAGpD,OAAO,CACpB;IAACG,MAAM,EAAE;MAACG,CAAC,EAAE4C;IAAG,CAAC;IAAE9C,OAAO;IAAEC,KAAK,EAAE;MAACO,KAAK,EAAEW,SAAS,CAACwB;IAAW;EAAC,CAAC,CAAC;EACvEnB,SAAS,CAACe,OAAO,CAACC,CAAC,IAAIxC,OAAO,CAACyC,6BAA6B,CAACD,CAAC,CAAC,CAAC;EAChE,OAAOQ,QAAQ;AACjB;AAEA,OAAO,MAAMC,cAAc,GAAiB;EAC1CC,UAAU,EAAE3D,QAAQ;EACpB4D,WAAW,EAAE,OAAO;EACpBC,UAAU,EAAEvD;CACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}