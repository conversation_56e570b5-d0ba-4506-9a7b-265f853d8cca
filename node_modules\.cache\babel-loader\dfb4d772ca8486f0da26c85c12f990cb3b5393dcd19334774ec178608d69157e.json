{"ast": null, "code": "/**\n * @license\n * Copyright 2021 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { util } from '@tensorflow/tfjs-core';\nexport function stringToHashBucketFastImpl(input, numBuckets) {\n  const output = util.getArrayFromDType('int32', input.length);\n  for (let i = 0; i < input.length; ++i) {\n    output[i] = util.fingerPrint64(input[i]).modulo(numBuckets).getLowBitsUnsigned();\n  }\n  return output;\n}", "map": {"version": 3, "names": ["util", "stringToHashBucketFastImpl", "input", "numBuckets", "output", "getArrayFromDType", "length", "i", "fingerPrint64", "modulo", "getLowBitsUnsigned"], "sources": ["C:\\tfjs-backend-cpu\\src\\kernels\\StringToHashBucketFast_impl.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2021 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {TypedArray, util} from '@tensorflow/tfjs-core';\n\nexport function stringToHashBucketFastImpl(\n    input: Uint8Array[], numBuckets: number): TypedArray {\n  const output = util.getArrayFromDType('int32', input.length) as TypedArray;\n\n  for (let i = 0; i < input.length; ++i) {\n    output[i] =\n        util.fingerPrint64(input[i]).modulo(numBuckets).getLowBitsUnsigned();\n  }\n\n  return output;\n}\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAiBA,SAAoBA,IAAI,QAAO,uBAAuB;AAEtD,OAAM,SAAUC,0BAA0BA,CACtCC,KAAmB,EAAEC,UAAkB;EACzC,MAAMC,MAAM,GAAGJ,IAAI,CAACK,iBAAiB,CAAC,OAAO,EAAEH,KAAK,CAACI,MAAM,CAAe;EAE1E,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGL,KAAK,CAACI,MAAM,EAAE,EAAEC,CAAC,EAAE;IACrCH,MAAM,CAACG,CAAC,CAAC,GACLP,IAAI,CAACQ,aAAa,CAACN,KAAK,CAACK,CAAC,CAAC,CAAC,CAACE,MAAM,CAACN,UAAU,CAAC,CAACO,kBAAkB,EAAE;;EAG1E,OAAON,MAAM;AACf", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}