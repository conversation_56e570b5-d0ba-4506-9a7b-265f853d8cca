{"ast": null, "code": "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { ENGINE } from '../engine';\nimport { Relu6 } from '../kernel_names';\nimport { convertToTensor } from '../tensor_util_env';\nimport { op } from './operation';\n/**\n * Computes rectified linear 6 element-wise: `min(max(x, 0), 6)`.\n *\n * ```js\n * const x = tf.tensor1d([-1, 2, -3, 8]);\n *\n * x.relu6().print();  // or tf.relu6(x)\n * ```\n * @param x The input tensor. If the dtype is `bool`, the output dtype will be\n *     `int32`.\n *\n * @doc {heading: 'Operations', subheading: 'Basic math'}\n */\nfunction relu6_(x) {\n  const $x = convertToTensor(x, 'x', 'relu6');\n  const inputs = {\n    x: $x\n  };\n  return ENGINE.runKernel(Relu6, inputs);\n}\nexport const relu6 = /* @__PURE__ */op({\n  relu6_\n});", "map": {"version": 3, "names": ["ENGINE", "Relu6", "convertToTensor", "op", "relu6_", "x", "$x", "inputs", "runKernel", "relu6"], "sources": ["C:\\tfjs-core\\src\\ops\\relu6.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {ENGINE} from '../engine';\nimport {Relu6, Relu6Inputs} from '../kernel_names';\nimport {Tensor} from '../tensor';\nimport {NamedTensorMap} from '../tensor_types';\nimport {convertToTensor} from '../tensor_util_env';\nimport {TensorLike} from '../types';\n\nimport {op} from './operation';\n\n/**\n * Computes rectified linear 6 element-wise: `min(max(x, 0), 6)`.\n *\n * ```js\n * const x = tf.tensor1d([-1, 2, -3, 8]);\n *\n * x.relu6().print();  // or tf.relu6(x)\n * ```\n * @param x The input tensor. If the dtype is `bool`, the output dtype will be\n *     `int32`.\n *\n * @doc {heading: 'Operations', subheading: 'Basic math'}\n */\nfunction relu6_<T extends Tensor>(x: T|TensorLike): T {\n  const $x = convertToTensor(x, 'x', 'relu6');\n\n  const inputs: Relu6Inputs = {x: $x};\n\n  return ENGINE.runKernel(Relu6, inputs as unknown as NamedTensorMap);\n}\n\nexport const relu6 = /* @__PURE__ */ op({relu6_});\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAiBA,SAAQA,MAAM,QAAO,WAAW;AAChC,SAAQC,KAAK,QAAoB,iBAAiB;AAGlD,SAAQC,eAAe,QAAO,oBAAoB;AAGlD,SAAQC,EAAE,QAAO,aAAa;AAE9B;;;;;;;;;;;;;AAaA,SAASC,MAAMA,CAAmBC,CAAe;EAC/C,MAAMC,EAAE,GAAGJ,eAAe,CAACG,CAAC,EAAE,GAAG,EAAE,OAAO,CAAC;EAE3C,MAAME,MAAM,GAAgB;IAACF,CAAC,EAAEC;EAAE,CAAC;EAEnC,OAAON,MAAM,CAACQ,SAAS,CAACP,KAAK,EAAEM,MAAmC,CAAC;AACrE;AAEA,OAAO,MAAME,KAAK,GAAG,eAAgBN,EAAE,CAAC;EAACC;AAAM,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}