{"ast": null, "code": "/*\nCopyright (c) 2011, <PERSON>\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in\nall copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\nTHE SOFTWARE.\n*/\n\n'use strict';\n\nconst Classifier = require('./classifier');\nconst ApparatusBayesClassifier = require('apparatus').BayesClassifier;\nclass BayesClassifier extends Classifier {\n  constructor(stemmer, smoothing) {\n    let abc = new ApparatusBayesClassifier();\n    if (smoothing && isFinite(smoothing)) {\n      abc = new ApparatusBayesClassifier(smoothing);\n    }\n    super(abc, stemmer);\n  }\n  static restore(classifier, stemmer) {\n    classifier = Classifier.restore(classifier, stemmer);\n    // __proto__ is deprecated\n    // classifier.__proto__ = BayesClassifier.prototype\n    Object.setPrototypeOf(classifier, BayesClassifier.prototype);\n    classifier.classifier = ApparatusBayesClassifier.restore(classifier.classifier);\n    return classifier;\n  }\n  static load(filename, stemmer, callback) {\n    Classifier.load(filename, stemmer, function (err, classifier) {\n      if (err) {\n        return callback(err);\n      } else {\n        callback(err, BayesClassifier.restore(classifier, stemmer));\n      }\n    });\n  }\n  static async loadFrom(key, stemmer, storageBackend) {\n    const classifier = await Classifier.loadFrom(key, storageBackend);\n    return BayesClassifier.restore(classifier, stemmer);\n  }\n}\nmodule.exports = BayesClassifier;", "map": {"version": 3, "names": ["Classifier", "require", "ApparatusBayesClassifier", "BayesClassifier", "constructor", "stemmer", "smoothing", "abc", "isFinite", "restore", "classifier", "Object", "setPrototypeOf", "prototype", "load", "filename", "callback", "err", "loadFrom", "key", "storageBackend", "module", "exports"], "sources": ["C:/tmsft/node_modules/natural/lib/natural/classifiers/bayes_classifier.js"], "sourcesContent": ["/*\nCopyright (c) 2011, <PERSON>\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in\nall copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\nTHE SOFTWARE.\n*/\n\n'use strict'\n\nconst Classifier = require('./classifier')\nconst ApparatusBayesClassifier = require('apparatus').BayesClassifier\n\nclass BayesClassifier extends Classifier {\n  constructor (stemmer, smoothing) {\n    let abc = new ApparatusBayesClassifier()\n    if (smoothing && isFinite(smoothing)) {\n      abc = new ApparatusBayesClassifier(smoothing)\n    }\n    super(abc, stemmer)\n  }\n\n  static restore (classifier, stemmer) {\n    classifier = Classifier.restore(classifier, stemmer)\n    // __proto__ is deprecated\n    // classifier.__proto__ = BayesClassifier.prototype\n    Object.setPrototypeOf(classifier, BayesClassifier.prototype)\n    classifier.classifier = ApparatusBayesClassifier.restore(classifier.classifier)\n    return classifier\n  }\n\n  static load (filename, stemmer, callback) {\n    Classifier.load(filename, stemmer, function (err, classifier) {\n      if (err) {\n        return callback(err)\n      } else {\n        callback(err, BayesClassifier.restore(classifier, stemmer))\n      }\n    })\n  }\n\n  static async loadFrom (key, stemmer, storageBackend) {\n    const classifier = await Classifier.loadFrom(key, storageBackend)\n    return BayesClassifier.restore(classifier, stemmer)\n  }\n}\n\nmodule.exports = BayesClassifier\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,YAAY;;AAEZ,MAAMA,UAAU,GAAGC,OAAO,CAAC,cAAc,CAAC;AAC1C,MAAMC,wBAAwB,GAAGD,OAAO,CAAC,WAAW,CAAC,CAACE,eAAe;AAErE,MAAMA,eAAe,SAASH,UAAU,CAAC;EACvCI,WAAWA,CAAEC,OAAO,EAAEC,SAAS,EAAE;IAC/B,IAAIC,GAAG,GAAG,IAAIL,wBAAwB,CAAC,CAAC;IACxC,IAAII,SAAS,IAAIE,QAAQ,CAACF,SAAS,CAAC,EAAE;MACpCC,GAAG,GAAG,IAAIL,wBAAwB,CAACI,SAAS,CAAC;IAC/C;IACA,KAAK,CAACC,GAAG,EAAEF,OAAO,CAAC;EACrB;EAEA,OAAOI,OAAOA,CAAEC,UAAU,EAAEL,OAAO,EAAE;IACnCK,UAAU,GAAGV,UAAU,CAACS,OAAO,CAACC,UAAU,EAAEL,OAAO,CAAC;IACpD;IACA;IACAM,MAAM,CAACC,cAAc,CAACF,UAAU,EAAEP,eAAe,CAACU,SAAS,CAAC;IAC5DH,UAAU,CAACA,UAAU,GAAGR,wBAAwB,CAACO,OAAO,CAACC,UAAU,CAACA,UAAU,CAAC;IAC/E,OAAOA,UAAU;EACnB;EAEA,OAAOI,IAAIA,CAAEC,QAAQ,EAAEV,OAAO,EAAEW,QAAQ,EAAE;IACxChB,UAAU,CAACc,IAAI,CAACC,QAAQ,EAAEV,OAAO,EAAE,UAAUY,GAAG,EAAEP,UAAU,EAAE;MAC5D,IAAIO,GAAG,EAAE;QACP,OAAOD,QAAQ,CAACC,GAAG,CAAC;MACtB,CAAC,MAAM;QACLD,QAAQ,CAACC,GAAG,EAAEd,eAAe,CAACM,OAAO,CAACC,UAAU,EAAEL,OAAO,CAAC,CAAC;MAC7D;IACF,CAAC,CAAC;EACJ;EAEA,aAAaa,QAAQA,CAAEC,GAAG,EAAEd,OAAO,EAAEe,cAAc,EAAE;IACnD,MAAMV,UAAU,GAAG,MAAMV,UAAU,CAACkB,QAAQ,CAACC,GAAG,EAAEC,cAAc,CAAC;IACjE,OAAOjB,eAAe,CAACM,OAAO,CAACC,UAAU,EAAEL,OAAO,CAAC;EACrD;AACF;AAEAgB,MAAM,CAACC,OAAO,GAAGnB,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}