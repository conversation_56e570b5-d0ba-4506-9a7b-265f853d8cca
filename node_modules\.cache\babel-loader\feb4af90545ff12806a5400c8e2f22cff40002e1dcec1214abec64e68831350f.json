{"ast": null, "code": "import*as tf from'@tensorflow/tfjs';import{fileStorageService}from'./fileStorageService';import{categoryService}from'./categoryService';// Simple browser-compatible tokenizer\nclass SimpleTokenizer{tokenize(text){return text.toLowerCase().replace(/[^\\w\\s]/g,' ').split(/\\s+/).filter(token=>token.length>0);}}// Feature vector interface (for future use)\n// interface FeatureVector {\n//   features: number[];\n//   categoryId: string;\n// }\nclass MLCategorizationService{constructor(){this.MODELS_FILENAME='ml_models';this.TRAINING_DATA_FILENAME='training_data';// private readonly FEATURE_CACHE_FILENAME = 'feature_cache';\nthis.model=null;this.categories=[];this.categoryToIndex=new Map();this.indexToCategory=new Map();// private vocabularySize = 1000;\nthis.embeddingDim=50;// private maxSequenceLength = 20;\nthis.tokenizer=void 0;this.tokenizer=new SimpleTokenizer();this.initializeCategories();}// Initialize categories mapping\ninitializeCategories(){this.categories=categoryService.getAllCategories();this.categories.forEach((category,index)=>{this.categoryToIndex.set(category.id,index);this.indexToCategory.set(index,category.id);});}// Extract features from transaction description\nextractFeatures(description,amount){const tokens=this.tokenizer.tokenize(description.toLowerCase())||[];// Text features using word embeddings simulation\nconst textFeatures=this.createTextEmbedding(tokens);// Amount features\nconst amountFeatures=this.createAmountFeatures(amount);// Pattern features\nconst patternFeatures=this.createPatternFeatures(description);return[...textFeatures,...amountFeatures,...patternFeatures];}// Create text embedding features\ncreateTextEmbedding(tokens){const embedding=new Array(this.embeddingDim).fill(0);if(tokens.length===0)return embedding;// Simple word-based features for common financial terms\nconst financialKeywords={'salary':[0.9,0.1,0.8,0.2],'wage':[0.9,0.1,0.8,0.2],'transfer':[0.5,0.5,0.5,0.5],'payment':[0.2,0.8,0.3,0.7],'fee':[0.1,0.9,0.2,0.8],'tax':[0.1,0.9,0.9,0.1],'interest':[0.8,0.2,0.6,0.4],'dividend':[0.9,0.1,0.7,0.3],'rent':[0.1,0.9,0.4,0.6],'utility':[0.1,0.9,0.3,0.7],'insurance':[0.1,0.9,0.5,0.5],'loan':[0.3,0.7,0.8,0.2],'deposit':[0.8,0.2,0.6,0.4],'withdrawal':[0.2,0.8,0.4,0.6],'purchase':[0.1,0.9,0.3,0.7],'refund':[0.7,0.3,0.5,0.5]};let keywordMatches=0;tokens.forEach((token,index)=>{if(financialKeywords[token]){const weights=financialKeywords[token];for(let i=0;i<Math.min(weights.length,embedding.length);i++){embedding[i]+=weights[i];}keywordMatches++;}// Add position-based weighting\nconst positionWeight=1-index/tokens.length*0.5;const hashValue=this.simpleHash(token)%this.embeddingDim;embedding[hashValue]+=positionWeight*0.1;});// Normalize by number of tokens\nif(keywordMatches>0){for(let i=0;i<embedding.length;i++){embedding[i]/=keywordMatches;}}return embedding;}// Create amount-based features\ncreateAmountFeatures(amount){const absAmount=Math.abs(amount);return[amount>0?1:0,// Credit indicator\namount<0?1:0,// Debit indicator\nMath.log(absAmount+1)/10,// Log-scaled amount\nabsAmount<100?1:0,// Small amount\nabsAmount>=100&&absAmount<1000?1:0,// Medium amount\nabsAmount>=1000?1:0,// Large amount\nabsAmount%1===0?1:0,// Round number\nabsAmount.toString().includes('00')?1:0// Contains 00\n];}// Create pattern-based features\ncreatePatternFeatures(description){const desc=description.toLowerCase();return[/\\d{4,}/.test(desc)?1:0,// Contains 4+ digit number\n/ref|reference/.test(desc)?1:0,// Contains reference\n/date|\\/|-/.test(desc)?1:0,// Contains date patterns\n/^direct|^dd|^standing/.test(desc)?1:0,// Direct debit/standing order\n/atm|cash|withdrawal/.test(desc)?1:0,// ATM/cash transaction\n/online|internet|web/.test(desc)?1:0,// Online transaction\n/card|visa|mastercard/.test(desc)?1:0,// Card transaction\ndesc.length>50?1:0,// Long description\ndesc.split(' ').length>10?1:0,// Many words\n/[A-Z]{2,}/.test(description)?1:0// Contains uppercase words\n];}// Simple hash function for string-to-number mapping\nsimpleHash(str){let hash=0;for(let i=0;i<str.length;i++){const char=str.charCodeAt(i);hash=(hash<<5)-hash+char;hash=hash&hash;// Convert to 32-bit integer\n}return Math.abs(hash);}// Build neural network model\nbuildModel(){const inputSize=this.embeddingDim+8+10;// text + amount + pattern features\nconst numCategories=this.categories.length;const model=tf.sequential({layers:[tf.layers.dense({inputShape:[inputSize],units:128,activation:'relu',kernelRegularizer:tf.regularizers.l2({l2:0.001})}),tf.layers.dropout({rate:0.3}),tf.layers.dense({units:64,activation:'relu',kernelRegularizer:tf.regularizers.l2({l2:0.001})}),tf.layers.dropout({rate:0.2}),tf.layers.dense({units:32,activation:'relu'}),tf.layers.dense({units:numCategories,activation:'softmax'})]});model.compile({optimizer:tf.train.adam(0.001),loss:'categoricalCrossentropy',metrics:['accuracy']});return model;}// Prepare training data\nprepareTrainingData(trainingData){const features=[];const labels=[];trainingData.forEach(data=>{const featureVector=this.extractFeatures(data.description,data.amount);const categoryIndex=this.categoryToIndex.get(data.categoryId);if(categoryIndex!==undefined){features.push(featureVector);// Create one-hot encoded label\nconst label=new Array(this.categories.length).fill(0);label[categoryIndex]=1;labels.push(label);}});const xs=tf.tensor2d(features);const ys=tf.tensor2d(labels);return{xs,ys};}// Train the model\nasync trainModel(trainingData){this.initializeCategories();if(trainingData.length<10){throw new Error('Insufficient training data. Need at least 10 examples.');}// Build new model\nthis.model=this.buildModel();// Prepare data\nconst{xs,ys}=this.prepareTrainingData(trainingData);try{// Train model\nconst history=await this.model.fit(xs,ys,{epochs:100,batchSize:32,validationSplit:0.2,shuffle:true,callbacks:{onEpochEnd:async(epoch,logs)=>{if(epoch%10===0){var _logs$loss,_logs$acc;console.log(\"Epoch \".concat(epoch,\": loss = \").concat(logs===null||logs===void 0?void 0:(_logs$loss=logs.loss)===null||_logs$loss===void 0?void 0:_logs$loss.toFixed(4),\", accuracy = \").concat(logs===null||logs===void 0?void 0:(_logs$acc=logs.acc)===null||_logs$acc===void 0?void 0:_logs$acc.toFixed(4)));}}}});// Get final metrics\nconst finalAccuracy=history.history.acc[history.history.acc.length-1];const finalLoss=history.history.loss[history.history.loss.length-1];// Save model\nawait this.saveModel(finalAccuracy,trainingData.length);// Clean up tensors\nxs.dispose();ys.dispose();return{accuracy:finalAccuracy,loss:finalLoss};}catch(error){xs.dispose();ys.dispose();throw error;}}// Categorize a single transaction\nasync categorizeTransaction(description,amount){if(!this.model){await this.loadLatestModel();}if(!this.model){return null;}const features=this.extractFeatures(description,amount);const input=tf.tensor2d([features]);try{const prediction=this.model.predict(input);const probabilities=await prediction.data();// Find the category with highest probability\nlet maxProb=0;let bestCategoryIndex=0;for(let i=0;i<probabilities.length;i++){if(probabilities[i]>maxProb){maxProb=probabilities[i];bestCategoryIndex=i;}}const categoryId=this.indexToCategory.get(bestCategoryIndex);if(categoryId&&maxProb>0.1){// Minimum confidence threshold\ninput.dispose();prediction.dispose();return{categoryId,confidence:maxProb,algorithm:'neural_network',features:['description','amount','patterns'],trainingDate:new Date().toISOString()};}input.dispose();prediction.dispose();return null;}catch(error){input.dispose();console.error('Error during categorization:',error);return null;}}// Save model to local storage\nasync saveModel(accuracy,trainingSize){if(!this.model)return;try{const modelData=await this.model.save(tf.io.withSaveHandler(async artifacts=>{const modelJson=JSON.stringify(artifacts.modelTopology);const weightsData=artifacts.weightData?Array.from(new Uint8Array(artifacts.weightData)):[];return{modelTopology:modelJson,weightData:weightsData,weightSpecs:artifacts.weightSpecs,modelArtifactsInfo:{dateSaved:new Date(),modelTopologyType:'JSON'}};}));const mlModel={id:this.generateId(),name:'Transaction Categorization Model',version:'1.0',algorithm:'neural_network',accuracy,trainingDate:new Date().toISOString(),trainingSize,isActive:true,modelData:JSON.stringify(modelData)};const models=this.getAllModels();// Deactivate old models\nmodels.forEach(m=>m.isActive=false);models.push(mlModel);fileStorageService.writeData(this.MODELS_FILENAME,models);}catch(error){console.error('Failed to save model:',error);}}// Load the latest active model\nasync loadLatestModel(){try{const models=this.getAllModels();const activeModel=models.find(m=>m.isActive);if(!activeModel)return false;const modelData=JSON.parse(activeModel.modelData);this.model=await tf.loadLayersModel(tf.io.fromMemory({modelTopology:JSON.parse(modelData.modelTopology),weightData:new Uint8Array(modelData.weightData).buffer,weightSpecs:modelData.weightSpecs}));return true;}catch(error){console.error('Failed to load model:',error);return false;}}// Get all stored models\ngetAllModels(){return fileStorageService.readData(this.MODELS_FILENAME,[]);}// Add training data\naddTrainingData(description,amount,categoryId){const trainingData=this.getTrainingData();const newData={id:this.generateId(),description,amount,categoryId,features:this.extractFeatures(description,amount),createdDate:new Date().toISOString()};trainingData.push(newData);this.saveTrainingData(trainingData);}// Get all training data\ngetTrainingData(){return fileStorageService.readData(this.TRAINING_DATA_FILENAME,[]);}// Save training data\nsaveTrainingData(data){fileStorageService.writeData(this.TRAINING_DATA_FILENAME,data);}// Clear training data\nclearTrainingData(){this.saveTrainingData([]);}// Get model statistics\ngetModelStats(){const models=this.getAllModels();const activeModel=models.find(m=>m.isActive)||null;const trainingDataSize=this.getTrainingData().length;return{totalModels:models.length,activeModel,trainingDataSize};}// Generate unique ID\ngenerateId(){return\"\".concat(Date.now(),\"-\").concat(Math.random().toString(36).substr(2,9));}// Initialize with sample training data if none exists\ninitializeWithSampleData(){const existingData=this.getTrainingData();if(existingData.length>0)return;// Get uncategorized category ID\nconst uncategorizedCategory=this.categories.find(c=>c.name==='Uncategorized');if(!uncategorizedCategory)return;// Add some sample training data\nconst sampleData=[{description:'SALARY PAYMENT',amount:5000,categoryId:uncategorizedCategory.id},{description:'OFFICE RENT MONTHLY',amount:-1200,categoryId:uncategorizedCategory.id},{description:'ELECTRICITY BILL',amount:-150,categoryId:uncategorizedCategory.id},{description:'BANK CHARGES',amount:-25,categoryId:uncategorizedCategory.id},{description:'CLIENT PAYMENT',amount:2500,categoryId:uncategorizedCategory.id}];sampleData.forEach(data=>{this.addTrainingData(data.description,data.amount,data.categoryId);});}}export const mlCategorizationService=new MLCategorizationService();", "map": {"version": 3, "names": ["tf", "fileStorageService", "categoryService", "SimpleTokenizer", "tokenize", "text", "toLowerCase", "replace", "split", "filter", "token", "length", "MLCategorizationService", "constructor", "MODELS_FILENAME", "TRAINING_DATA_FILENAME", "model", "categories", "categoryToIndex", "Map", "indexToCategory", "embedding<PERSON>im", "tokenizer", "initializeCategories", "getAllCategories", "for<PERSON>ach", "category", "index", "set", "id", "extractFeatures", "description", "amount", "tokens", "textFeatures", "createTextEmbedding", "amountFeatures", "createAmountFeatures", "patternFeatures", "createPatternFeatures", "embedding", "Array", "fill", "financialKeywords", "keywordMatches", "weights", "i", "Math", "min", "positionWeight", "hashValue", "simpleHash", "absAmount", "abs", "log", "toString", "includes", "desc", "test", "str", "hash", "char", "charCodeAt", "buildModel", "inputSize", "numCategories", "sequential", "layers", "dense", "inputShape", "units", "activation", "kernelRegularizer", "regularizers", "l2", "dropout", "rate", "compile", "optimizer", "train", "adam", "loss", "metrics", "prepareTrainingData", "trainingData", "features", "labels", "data", "featureVector", "categoryIndex", "get", "categoryId", "undefined", "push", "label", "xs", "tensor2d", "ys", "trainModel", "Error", "history", "fit", "epochs", "batchSize", "validationSplit", "shuffle", "callbacks", "onEpochEnd", "epoch", "logs", "_logs$loss", "_logs$acc", "console", "concat", "toFixed", "acc", "finalAccuracy", "finalLoss", "saveModel", "dispose", "accuracy", "error", "categorizeTransaction", "loadLatestModel", "input", "prediction", "predict", "probabilities", "maxProb", "bestCategoryIndex", "confidence", "algorithm", "trainingDate", "Date", "toISOString", "trainingSize", "modelData", "save", "io", "with<PERSON><PERSON><PERSON><PERSON><PERSON>", "artifacts", "<PERSON><PERSON><PERSON>", "JSON", "stringify", "modelTopology", "weightsData", "weightData", "from", "Uint8Array", "weightSpecs", "modelArtifactsInfo", "dateSaved", "modelTopologyType", "mlModel", "generateId", "name", "version", "isActive", "models", "getAllModels", "m", "writeData", "activeModel", "find", "parse", "loadLayersModel", "fromMemory", "buffer", "readData", "addTrainingData", "getTrainingData", "newData", "createdDate", "saveTrainingData", "clearTrainingData", "getModelStats", "trainingDataSize", "totalModels", "now", "random", "substr", "initializeWithSampleData", "existingData", "uncategorizedCategory", "c", "sampleData", "mlCategorizationService"], "sources": ["C:/tmsft/src/services/mlCategorizationService.ts"], "sourcesContent": ["import * as tf from '@tensorflow/tfjs';\r\nimport { MLModel, TrainingData, MLCategorization, TransactionCategory } from '../types';\r\nimport { fileStorageService } from './fileStorageService';\r\nimport { categoryService } from './categoryService';\r\n\r\n// Simple browser-compatible tokenizer\r\nclass SimpleTokenizer {\r\n  tokenize(text: string): string[] {\r\n    return text.toLowerCase()\r\n      .replace(/[^\\w\\s]/g, ' ')\r\n      .split(/\\s+/)\r\n      .filter(token => token.length > 0);\r\n  }\r\n}\r\n\r\n// Feature vector interface (for future use)\r\n// interface FeatureVector {\r\n//   features: number[];\r\n//   categoryId: string;\r\n// }\r\n\r\nclass MLCategorizationService {\r\n  private readonly MODELS_FILENAME = 'ml_models';\r\n  private readonly TRAINING_DATA_FILENAME = 'training_data';\r\n  // private readonly FEATURE_CACHE_FILENAME = 'feature_cache';\r\n  \r\n  private model: tf.LayersModel | null = null;\r\n  private categories: TransactionCategory[] = [];\r\n  private categoryToIndex: Map<string, number> = new Map();\r\n  private indexToCategory: Map<number, string> = new Map();\r\n  // private vocabularySize = 1000;\r\n  private embeddingDim = 50;\r\n  // private maxSequenceLength = 20;\r\n  private tokenizer: SimpleTokenizer;\r\n\r\n  constructor() {\r\n    this.tokenizer = new SimpleTokenizer();\r\n    this.initializeCategories();\r\n  }\r\n\r\n  // Initialize categories mapping\r\n  private initializeCategories(): void {\r\n    this.categories = categoryService.getAllCategories();\r\n    this.categories.forEach((category, index) => {\r\n      this.categoryToIndex.set(category.id, index);\r\n      this.indexToCategory.set(index, category.id);\r\n    });\r\n  }\r\n\r\n  // Extract features from transaction description\r\n  private extractFeatures(description: string, amount: number): number[] {\r\n    const tokens = this.tokenizer.tokenize(description.toLowerCase()) || [];\r\n    \r\n    // Text features using word embeddings simulation\r\n    const textFeatures = this.createTextEmbedding(tokens);\r\n    \r\n    // Amount features\r\n    const amountFeatures = this.createAmountFeatures(amount);\r\n    \r\n    // Pattern features\r\n    const patternFeatures = this.createPatternFeatures(description);\r\n    \r\n    return [...textFeatures, ...amountFeatures, ...patternFeatures];\r\n  }\r\n\r\n  // Create text embedding features\r\n  private createTextEmbedding(tokens: string[]): number[] {\r\n    const embedding = new Array(this.embeddingDim).fill(0);\r\n    \r\n    if (tokens.length === 0) return embedding;\r\n    \r\n    // Simple word-based features for common financial terms\r\n    const financialKeywords: Record<string, number[]> = {\r\n      'salary': [0.9, 0.1, 0.8, 0.2],\r\n      'wage': [0.9, 0.1, 0.8, 0.2],\r\n      'transfer': [0.5, 0.5, 0.5, 0.5],\r\n      'payment': [0.2, 0.8, 0.3, 0.7],\r\n      'fee': [0.1, 0.9, 0.2, 0.8],\r\n      'tax': [0.1, 0.9, 0.9, 0.1],\r\n      'interest': [0.8, 0.2, 0.6, 0.4],\r\n      'dividend': [0.9, 0.1, 0.7, 0.3],\r\n      'rent': [0.1, 0.9, 0.4, 0.6],\r\n      'utility': [0.1, 0.9, 0.3, 0.7],\r\n      'insurance': [0.1, 0.9, 0.5, 0.5],\r\n      'loan': [0.3, 0.7, 0.8, 0.2],\r\n      'deposit': [0.8, 0.2, 0.6, 0.4],\r\n      'withdrawal': [0.2, 0.8, 0.4, 0.6],\r\n      'purchase': [0.1, 0.9, 0.3, 0.7],\r\n      'refund': [0.7, 0.3, 0.5, 0.5]\r\n    };\r\n\r\n    let keywordMatches = 0;\r\n    tokens.forEach((token, index) => {\r\n      if (financialKeywords[token]) {\r\n        const weights = financialKeywords[token];\r\n        for (let i = 0; i < Math.min(weights.length, embedding.length); i++) {\r\n          embedding[i] += weights[i];\r\n        }\r\n        keywordMatches++;\r\n      }\r\n      \r\n      // Add position-based weighting\r\n      const positionWeight = 1 - (index / tokens.length) * 0.5;\r\n      const hashValue = this.simpleHash(token) % this.embeddingDim;\r\n      embedding[hashValue] += positionWeight * 0.1;\r\n    });\r\n\r\n    // Normalize by number of tokens\r\n    if (keywordMatches > 0) {\r\n      for (let i = 0; i < embedding.length; i++) {\r\n        embedding[i] /= keywordMatches;\r\n      }\r\n    }\r\n\r\n    return embedding;\r\n  }\r\n\r\n  // Create amount-based features\r\n  private createAmountFeatures(amount: number): number[] {\r\n    const absAmount = Math.abs(amount);\r\n    \r\n    return [\r\n      amount > 0 ? 1 : 0, // Credit indicator\r\n      amount < 0 ? 1 : 0, // Debit indicator\r\n      Math.log(absAmount + 1) / 10, // Log-scaled amount\r\n      absAmount < 100 ? 1 : 0, // Small amount\r\n      absAmount >= 100 && absAmount < 1000 ? 1 : 0, // Medium amount\r\n      absAmount >= 1000 ? 1 : 0, // Large amount\r\n      absAmount % 1 === 0 ? 1 : 0, // Round number\r\n      absAmount.toString().includes('00') ? 1 : 0 // Contains 00\r\n    ];\r\n  }\r\n\r\n  // Create pattern-based features\r\n  private createPatternFeatures(description: string): number[] {\r\n    const desc = description.toLowerCase();\r\n    \r\n    return [\r\n      /\\d{4,}/.test(desc) ? 1 : 0, // Contains 4+ digit number\r\n      /ref|reference/.test(desc) ? 1 : 0, // Contains reference\r\n      /date|\\/|-/.test(desc) ? 1 : 0, // Contains date patterns\r\n      /^direct|^dd|^standing/.test(desc) ? 1 : 0, // Direct debit/standing order\r\n      /atm|cash|withdrawal/.test(desc) ? 1 : 0, // ATM/cash transaction\r\n      /online|internet|web/.test(desc) ? 1 : 0, // Online transaction\r\n      /card|visa|mastercard/.test(desc) ? 1 : 0, // Card transaction\r\n      desc.length > 50 ? 1 : 0, // Long description\r\n      desc.split(' ').length > 10 ? 1 : 0, // Many words\r\n      /[A-Z]{2,}/.test(description) ? 1 : 0 // Contains uppercase words\r\n    ];\r\n  }\r\n\r\n  // Simple hash function for string-to-number mapping\r\n  private simpleHash(str: string): number {\r\n    let hash = 0;\r\n    for (let i = 0; i < str.length; i++) {\r\n      const char = str.charCodeAt(i);\r\n      hash = ((hash << 5) - hash) + char;\r\n      hash = hash & hash; // Convert to 32-bit integer\r\n    }\r\n    return Math.abs(hash);\r\n  }\r\n\r\n  // Build neural network model\r\n  private buildModel(): tf.LayersModel {\r\n    const inputSize = this.embeddingDim + 8 + 10; // text + amount + pattern features\r\n    const numCategories = this.categories.length;\r\n\r\n    const model = tf.sequential({\r\n      layers: [\r\n        tf.layers.dense({\r\n          inputShape: [inputSize],\r\n          units: 128,\r\n          activation: 'relu',\r\n          kernelRegularizer: tf.regularizers.l2({ l2: 0.001 })\r\n        }),\r\n        tf.layers.dropout({ rate: 0.3 }),\r\n        tf.layers.dense({\r\n          units: 64,\r\n          activation: 'relu',\r\n          kernelRegularizer: tf.regularizers.l2({ l2: 0.001 })\r\n        }),\r\n        tf.layers.dropout({ rate: 0.2 }),\r\n        tf.layers.dense({\r\n          units: 32,\r\n          activation: 'relu'\r\n        }),\r\n        tf.layers.dense({\r\n          units: numCategories,\r\n          activation: 'softmax'\r\n        })\r\n      ]\r\n    });\r\n\r\n    model.compile({\r\n      optimizer: tf.train.adam(0.001),\r\n      loss: 'categoricalCrossentropy',\r\n      metrics: ['accuracy']\r\n    });\r\n\r\n    return model;\r\n  }\r\n\r\n  // Prepare training data\r\n  private prepareTrainingData(trainingData: TrainingData[]): { xs: tf.Tensor, ys: tf.Tensor } {\r\n    const features: number[][] = [];\r\n    const labels: number[][] = [];\r\n\r\n    trainingData.forEach(data => {\r\n      const featureVector = this.extractFeatures(data.description, data.amount);\r\n      const categoryIndex = this.categoryToIndex.get(data.categoryId);\r\n      \r\n      if (categoryIndex !== undefined) {\r\n        features.push(featureVector);\r\n        \r\n        // Create one-hot encoded label\r\n        const label = new Array(this.categories.length).fill(0);\r\n        label[categoryIndex] = 1;\r\n        labels.push(label);\r\n      }\r\n    });\r\n\r\n    const xs = tf.tensor2d(features);\r\n    const ys = tf.tensor2d(labels);\r\n\r\n    return { xs, ys };\r\n  }\r\n\r\n  // Train the model\r\n  async trainModel(trainingData: TrainingData[]): Promise<{ accuracy: number; loss: number }> {\r\n    this.initializeCategories();\r\n    \r\n    if (trainingData.length < 10) {\r\n      throw new Error('Insufficient training data. Need at least 10 examples.');\r\n    }\r\n\r\n    // Build new model\r\n    this.model = this.buildModel();\r\n    \r\n    // Prepare data\r\n    const { xs, ys } = this.prepareTrainingData(trainingData);\r\n    \r\n    try {\r\n      // Train model\r\n      const history = await this.model.fit(xs, ys, {\r\n        epochs: 100,\r\n        batchSize: 32,\r\n        validationSplit: 0.2,\r\n        shuffle: true,\r\n        callbacks: {\r\n          onEpochEnd: async (epoch, logs) => {\r\n            if (epoch % 10 === 0) {\r\n              console.log(`Epoch ${epoch}: loss = ${logs?.loss?.toFixed(4)}, accuracy = ${logs?.acc?.toFixed(4)}`);\r\n            }\r\n          }\r\n        }\r\n      });\r\n\r\n      // Get final metrics\r\n      const finalAccuracy = history.history.acc[history.history.acc.length - 1] as number;\r\n      const finalLoss = history.history.loss[history.history.loss.length - 1] as number;\r\n\r\n      // Save model\r\n      await this.saveModel(finalAccuracy, trainingData.length);\r\n\r\n      // Clean up tensors\r\n      xs.dispose();\r\n      ys.dispose();\r\n\r\n      return { accuracy: finalAccuracy, loss: finalLoss };\r\n    } catch (error) {\r\n      xs.dispose();\r\n      ys.dispose();\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  // Categorize a single transaction\r\n  async categorizeTransaction(description: string, amount: number): Promise<MLCategorization | null> {\r\n    if (!this.model) {\r\n      await this.loadLatestModel();\r\n    }\r\n\r\n    if (!this.model) {\r\n      return null;\r\n    }\r\n\r\n    const features = this.extractFeatures(description, amount);\r\n    const input = tf.tensor2d([features]);\r\n\r\n    try {\r\n      const prediction = this.model.predict(input) as tf.Tensor;\r\n      const probabilities = await prediction.data();\r\n      \r\n      // Find the category with highest probability\r\n      let maxProb = 0;\r\n      let bestCategoryIndex = 0;\r\n      \r\n      for (let i = 0; i < probabilities.length; i++) {\r\n        if (probabilities[i] > maxProb) {\r\n          maxProb = probabilities[i];\r\n          bestCategoryIndex = i;\r\n        }\r\n      }\r\n\r\n      const categoryId = this.indexToCategory.get(bestCategoryIndex);\r\n      \r\n      if (categoryId && maxProb > 0.1) { // Minimum confidence threshold\r\n        input.dispose();\r\n        prediction.dispose();\r\n        \r\n        return {\r\n          categoryId,\r\n          confidence: maxProb,\r\n          algorithm: 'neural_network',\r\n          features: ['description', 'amount', 'patterns'],\r\n          trainingDate: new Date().toISOString()\r\n        };\r\n      }\r\n\r\n      input.dispose();\r\n      prediction.dispose();\r\n      return null;\r\n    } catch (error) {\r\n      input.dispose();\r\n      console.error('Error during categorization:', error);\r\n      return null;\r\n    }\r\n  }\r\n\r\n  // Save model to local storage\r\n  private async saveModel(accuracy: number, trainingSize: number): Promise<void> {\r\n    if (!this.model) return;\r\n\r\n    try {\r\n      const modelData = await this.model.save(tf.io.withSaveHandler(async (artifacts) => {\r\n        const modelJson = JSON.stringify(artifacts.modelTopology);\r\n        const weightsData = artifacts.weightData ? Array.from(new Uint8Array(artifacts.weightData as ArrayBuffer)) : [];\r\n        \r\n        return {\r\n          modelTopology: modelJson,\r\n          weightData: weightsData,\r\n          weightSpecs: artifacts.weightSpecs,\r\n          modelArtifactsInfo: {\r\n            dateSaved: new Date(),\r\n            modelTopologyType: 'JSON'\r\n          }\r\n        };\r\n      }));\r\n\r\n      const mlModel: MLModel = {\r\n        id: this.generateId(),\r\n        name: 'Transaction Categorization Model',\r\n        version: '1.0',\r\n        algorithm: 'neural_network',\r\n        accuracy,\r\n        trainingDate: new Date().toISOString(),\r\n        trainingSize,\r\n        isActive: true,\r\n        modelData: JSON.stringify(modelData)\r\n      };\r\n\r\n      const models = this.getAllModels();\r\n      // Deactivate old models\r\n      models.forEach(m => m.isActive = false);\r\n      models.push(mlModel);\r\n\r\n      fileStorageService.writeData(this.MODELS_FILENAME, models);\r\n    } catch (error) {\r\n      console.error('Failed to save model:', error);\r\n    }\r\n  }\r\n\r\n  // Load the latest active model\r\n  private async loadLatestModel(): Promise<boolean> {\r\n    try {\r\n      const models = this.getAllModels();\r\n      const activeModel = models.find(m => m.isActive);\r\n      \r\n      if (!activeModel) return false;\r\n\r\n      const modelData = JSON.parse(activeModel.modelData);\r\n      \r\n      this.model = await tf.loadLayersModel(tf.io.fromMemory({\r\n        modelTopology: JSON.parse(modelData.modelTopology),\r\n        weightData: new Uint8Array(modelData.weightData).buffer,\r\n        weightSpecs: modelData.weightSpecs\r\n      }));\r\n\r\n      return true;\r\n    } catch (error) {\r\n      console.error('Failed to load model:', error);\r\n      return false;\r\n    }\r\n  }\r\n\r\n  // Get all stored models\r\n  getAllModels(): MLModel[] {\r\n    return fileStorageService.readData<MLModel[]>(this.MODELS_FILENAME, []);\r\n  }\r\n\r\n  // Add training data\r\n  addTrainingData(description: string, amount: number, categoryId: string): void {\r\n    const trainingData = this.getTrainingData();\r\n    \r\n    const newData: TrainingData = {\r\n      id: this.generateId(),\r\n      description,\r\n      amount,\r\n      categoryId,\r\n      features: this.extractFeatures(description, amount),\r\n      createdDate: new Date().toISOString()\r\n    };\r\n\r\n    trainingData.push(newData);\r\n    this.saveTrainingData(trainingData);\r\n  }\r\n\r\n  // Get all training data\r\n  getTrainingData(): TrainingData[] {\r\n    return fileStorageService.readData<TrainingData[]>(this.TRAINING_DATA_FILENAME, []);\r\n  }\r\n\r\n  // Save training data\r\n  private saveTrainingData(data: TrainingData[]): void {\r\n    fileStorageService.writeData(this.TRAINING_DATA_FILENAME, data);\r\n  }\r\n\r\n  // Clear training data\r\n  clearTrainingData(): void {\r\n    this.saveTrainingData([]);\r\n  }\r\n\r\n  // Get model statistics\r\n  getModelStats(): { totalModels: number; activeModel: MLModel | null; trainingDataSize: number } {\r\n    const models = this.getAllModels();\r\n    const activeModel = models.find(m => m.isActive) || null;\r\n    const trainingDataSize = this.getTrainingData().length;\r\n\r\n    return {\r\n      totalModels: models.length,\r\n      activeModel,\r\n      trainingDataSize\r\n    };\r\n  }\r\n\r\n  // Generate unique ID\r\n  private generateId(): string {\r\n    return `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;\r\n  }\r\n\r\n  // Initialize with sample training data if none exists\r\n  initializeWithSampleData(): void {\r\n    const existingData = this.getTrainingData();\r\n    if (existingData.length > 0) return;\r\n\r\n    // Get uncategorized category ID\r\n    const uncategorizedCategory = this.categories.find(c => c.name === 'Uncategorized');\r\n    if (!uncategorizedCategory) return;\r\n\r\n    // Add some sample training data\r\n    const sampleData = [\r\n      { description: 'SALARY PAYMENT', amount: 5000, categoryId: uncategorizedCategory.id },\r\n      { description: 'OFFICE RENT MONTHLY', amount: -1200, categoryId: uncategorizedCategory.id },\r\n      { description: 'ELECTRICITY BILL', amount: -150, categoryId: uncategorizedCategory.id },\r\n      { description: 'BANK CHARGES', amount: -25, categoryId: uncategorizedCategory.id },\r\n      { description: 'CLIENT PAYMENT', amount: 2500, categoryId: uncategorizedCategory.id }\r\n    ];\r\n\r\n    sampleData.forEach(data => {\r\n      this.addTrainingData(data.description, data.amount, data.categoryId);\r\n    });\r\n  }\r\n}\r\n\r\nexport const mlCategorizationService = new MLCategorizationService(); "], "mappings": "AAAA,MAAO,GAAK,CAAAA,EAAE,KAAM,kBAAkB,CAEtC,OAASC,kBAAkB,KAAQ,sBAAsB,CACzD,OAASC,eAAe,KAAQ,mBAAmB,CAEnD;AACA,KAAM,CAAAC,eAAgB,CACpBC,QAAQA,CAACC,IAAY,CAAY,CAC/B,MAAO,CAAAA,IAAI,CAACC,WAAW,CAAC,CAAC,CACtBC,OAAO,CAAC,UAAU,CAAE,GAAG,CAAC,CACxBC,KAAK,CAAC,KAAK,CAAC,CACZC,MAAM,CAACC,KAAK,EAAIA,KAAK,CAACC,MAAM,CAAG,CAAC,CAAC,CACtC,CACF,CAEA;AACA;AACA;AACA;AACA;AAEA,KAAM,CAAAC,uBAAwB,CAc5BC,WAAWA,CAAA,CAAG,MAbGC,eAAe,CAAG,WAAW,MAC7BC,sBAAsB,CAAG,eAAe,CACzD;AAAA,KAEQC,KAAK,CAA0B,IAAI,MACnCC,UAAU,CAA0B,EAAE,MACtCC,eAAe,CAAwB,GAAI,CAAAC,GAAG,CAAC,CAAC,MAChDC,eAAe,CAAwB,GAAI,CAAAD,GAAG,CAAC,CAAC,CACxD;AAAA,KACQE,YAAY,CAAG,EAAE,CACzB;AAAA,KACQC,SAAS,QAGf,IAAI,CAACA,SAAS,CAAG,GAAI,CAAAnB,eAAe,CAAC,CAAC,CACtC,IAAI,CAACoB,oBAAoB,CAAC,CAAC,CAC7B,CAEA;AACQA,oBAAoBA,CAAA,CAAS,CACnC,IAAI,CAACN,UAAU,CAAGf,eAAe,CAACsB,gBAAgB,CAAC,CAAC,CACpD,IAAI,CAACP,UAAU,CAACQ,OAAO,CAAC,CAACC,QAAQ,CAAEC,KAAK,GAAK,CAC3C,IAAI,CAACT,eAAe,CAACU,GAAG,CAACF,QAAQ,CAACG,EAAE,CAAEF,KAAK,CAAC,CAC5C,IAAI,CAACP,eAAe,CAACQ,GAAG,CAACD,KAAK,CAAED,QAAQ,CAACG,EAAE,CAAC,CAC9C,CAAC,CAAC,CACJ,CAEA;AACQC,eAAeA,CAACC,WAAmB,CAAEC,MAAc,CAAY,CACrE,KAAM,CAAAC,MAAM,CAAG,IAAI,CAACX,SAAS,CAAClB,QAAQ,CAAC2B,WAAW,CAACzB,WAAW,CAAC,CAAC,CAAC,EAAI,EAAE,CAEvE;AACA,KAAM,CAAA4B,YAAY,CAAG,IAAI,CAACC,mBAAmB,CAACF,MAAM,CAAC,CAErD;AACA,KAAM,CAAAG,cAAc,CAAG,IAAI,CAACC,oBAAoB,CAACL,MAAM,CAAC,CAExD;AACA,KAAM,CAAAM,eAAe,CAAG,IAAI,CAACC,qBAAqB,CAACR,WAAW,CAAC,CAE/D,MAAO,CAAC,GAAGG,YAAY,CAAE,GAAGE,cAAc,CAAE,GAAGE,eAAe,CAAC,CACjE,CAEA;AACQH,mBAAmBA,CAACF,MAAgB,CAAY,CACtD,KAAM,CAAAO,SAAS,CAAG,GAAI,CAAAC,KAAK,CAAC,IAAI,CAACpB,YAAY,CAAC,CAACqB,IAAI,CAAC,CAAC,CAAC,CAEtD,GAAIT,MAAM,CAACtB,MAAM,GAAK,CAAC,CAAE,MAAO,CAAA6B,SAAS,CAEzC;AACA,KAAM,CAAAG,iBAA2C,CAAG,CAClD,QAAQ,CAAE,CAAC,GAAG,CAAE,GAAG,CAAE,GAAG,CAAE,GAAG,CAAC,CAC9B,MAAM,CAAE,CAAC,GAAG,CAAE,GAAG,CAAE,GAAG,CAAE,GAAG,CAAC,CAC5B,UAAU,CAAE,CAAC,GAAG,CAAE,GAAG,CAAE,GAAG,CAAE,GAAG,CAAC,CAChC,SAAS,CAAE,CAAC,GAAG,CAAE,GAAG,CAAE,GAAG,CAAE,GAAG,CAAC,CAC/B,KAAK,CAAE,CAAC,GAAG,CAAE,GAAG,CAAE,GAAG,CAAE,GAAG,CAAC,CAC3B,KAAK,CAAE,CAAC,GAAG,CAAE,GAAG,CAAE,GAAG,CAAE,GAAG,CAAC,CAC3B,UAAU,CAAE,CAAC,GAAG,CAAE,GAAG,CAAE,GAAG,CAAE,GAAG,CAAC,CAChC,UAAU,CAAE,CAAC,GAAG,CAAE,GAAG,CAAE,GAAG,CAAE,GAAG,CAAC,CAChC,MAAM,CAAE,CAAC,GAAG,CAAE,GAAG,CAAE,GAAG,CAAE,GAAG,CAAC,CAC5B,SAAS,CAAE,CAAC,GAAG,CAAE,GAAG,CAAE,GAAG,CAAE,GAAG,CAAC,CAC/B,WAAW,CAAE,CAAC,GAAG,CAAE,GAAG,CAAE,GAAG,CAAE,GAAG,CAAC,CACjC,MAAM,CAAE,CAAC,GAAG,CAAE,GAAG,CAAE,GAAG,CAAE,GAAG,CAAC,CAC5B,SAAS,CAAE,CAAC,GAAG,CAAE,GAAG,CAAE,GAAG,CAAE,GAAG,CAAC,CAC/B,YAAY,CAAE,CAAC,GAAG,CAAE,GAAG,CAAE,GAAG,CAAE,GAAG,CAAC,CAClC,UAAU,CAAE,CAAC,GAAG,CAAE,GAAG,CAAE,GAAG,CAAE,GAAG,CAAC,CAChC,QAAQ,CAAE,CAAC,GAAG,CAAE,GAAG,CAAE,GAAG,CAAE,GAAG,CAC/B,CAAC,CAED,GAAI,CAAAC,cAAc,CAAG,CAAC,CACtBX,MAAM,CAACR,OAAO,CAAC,CAACf,KAAK,CAAEiB,KAAK,GAAK,CAC/B,GAAIgB,iBAAiB,CAACjC,KAAK,CAAC,CAAE,CAC5B,KAAM,CAAAmC,OAAO,CAAGF,iBAAiB,CAACjC,KAAK,CAAC,CACxC,IAAK,GAAI,CAAAoC,CAAC,CAAG,CAAC,CAAEA,CAAC,CAAGC,IAAI,CAACC,GAAG,CAACH,OAAO,CAAClC,MAAM,CAAE6B,SAAS,CAAC7B,MAAM,CAAC,CAAEmC,CAAC,EAAE,CAAE,CACnEN,SAAS,CAACM,CAAC,CAAC,EAAID,OAAO,CAACC,CAAC,CAAC,CAC5B,CACAF,cAAc,EAAE,CAClB,CAEA;AACA,KAAM,CAAAK,cAAc,CAAG,CAAC,CAAItB,KAAK,CAAGM,MAAM,CAACtB,MAAM,CAAI,GAAG,CACxD,KAAM,CAAAuC,SAAS,CAAG,IAAI,CAACC,UAAU,CAACzC,KAAK,CAAC,CAAG,IAAI,CAACW,YAAY,CAC5DmB,SAAS,CAACU,SAAS,CAAC,EAAID,cAAc,CAAG,GAAG,CAC9C,CAAC,CAAC,CAEF;AACA,GAAIL,cAAc,CAAG,CAAC,CAAE,CACtB,IAAK,GAAI,CAAAE,CAAC,CAAG,CAAC,CAAEA,CAAC,CAAGN,SAAS,CAAC7B,MAAM,CAAEmC,CAAC,EAAE,CAAE,CACzCN,SAAS,CAACM,CAAC,CAAC,EAAIF,cAAc,CAChC,CACF,CAEA,MAAO,CAAAJ,SAAS,CAClB,CAEA;AACQH,oBAAoBA,CAACL,MAAc,CAAY,CACrD,KAAM,CAAAoB,SAAS,CAAGL,IAAI,CAACM,GAAG,CAACrB,MAAM,CAAC,CAElC,MAAO,CACLA,MAAM,CAAG,CAAC,CAAG,CAAC,CAAG,CAAC,CAAE;AACpBA,MAAM,CAAG,CAAC,CAAG,CAAC,CAAG,CAAC,CAAE;AACpBe,IAAI,CAACO,GAAG,CAACF,SAAS,CAAG,CAAC,CAAC,CAAG,EAAE,CAAE;AAC9BA,SAAS,CAAG,GAAG,CAAG,CAAC,CAAG,CAAC,CAAE;AACzBA,SAAS,EAAI,GAAG,EAAIA,SAAS,CAAG,IAAI,CAAG,CAAC,CAAG,CAAC,CAAE;AAC9CA,SAAS,EAAI,IAAI,CAAG,CAAC,CAAG,CAAC,CAAE;AAC3BA,SAAS,CAAG,CAAC,GAAK,CAAC,CAAG,CAAC,CAAG,CAAC,CAAE;AAC7BA,SAAS,CAACG,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,IAAI,CAAC,CAAG,CAAC,CAAG,CAAE;AAAA,CAC7C,CACH,CAEA;AACQjB,qBAAqBA,CAACR,WAAmB,CAAY,CAC3D,KAAM,CAAA0B,IAAI,CAAG1B,WAAW,CAACzB,WAAW,CAAC,CAAC,CAEtC,MAAO,CACL,QAAQ,CAACoD,IAAI,CAACD,IAAI,CAAC,CAAG,CAAC,CAAG,CAAC,CAAE;AAC7B,eAAe,CAACC,IAAI,CAACD,IAAI,CAAC,CAAG,CAAC,CAAG,CAAC,CAAE;AACpC,WAAW,CAACC,IAAI,CAACD,IAAI,CAAC,CAAG,CAAC,CAAG,CAAC,CAAE;AAChC,uBAAuB,CAACC,IAAI,CAACD,IAAI,CAAC,CAAG,CAAC,CAAG,CAAC,CAAE;AAC5C,qBAAqB,CAACC,IAAI,CAACD,IAAI,CAAC,CAAG,CAAC,CAAG,CAAC,CAAE;AAC1C,qBAAqB,CAACC,IAAI,CAACD,IAAI,CAAC,CAAG,CAAC,CAAG,CAAC,CAAE;AAC1C,sBAAsB,CAACC,IAAI,CAACD,IAAI,CAAC,CAAG,CAAC,CAAG,CAAC,CAAE;AAC3CA,IAAI,CAAC9C,MAAM,CAAG,EAAE,CAAG,CAAC,CAAG,CAAC,CAAE;AAC1B8C,IAAI,CAACjD,KAAK,CAAC,GAAG,CAAC,CAACG,MAAM,CAAG,EAAE,CAAG,CAAC,CAAG,CAAC,CAAE;AACrC,WAAW,CAAC+C,IAAI,CAAC3B,WAAW,CAAC,CAAG,CAAC,CAAG,CAAE;AAAA,CACvC,CACH,CAEA;AACQoB,UAAUA,CAACQ,GAAW,CAAU,CACtC,GAAI,CAAAC,IAAI,CAAG,CAAC,CACZ,IAAK,GAAI,CAAAd,CAAC,CAAG,CAAC,CAAEA,CAAC,CAAGa,GAAG,CAAChD,MAAM,CAAEmC,CAAC,EAAE,CAAE,CACnC,KAAM,CAAAe,IAAI,CAAGF,GAAG,CAACG,UAAU,CAAChB,CAAC,CAAC,CAC9Bc,IAAI,CAAI,CAACA,IAAI,EAAI,CAAC,EAAIA,IAAI,CAAIC,IAAI,CAClCD,IAAI,CAAGA,IAAI,CAAGA,IAAI,CAAE;AACtB,CACA,MAAO,CAAAb,IAAI,CAACM,GAAG,CAACO,IAAI,CAAC,CACvB,CAEA;AACQG,UAAUA,CAAA,CAAmB,CACnC,KAAM,CAAAC,SAAS,CAAG,IAAI,CAAC3C,YAAY,CAAG,CAAC,CAAG,EAAE,CAAE;AAC9C,KAAM,CAAA4C,aAAa,CAAG,IAAI,CAAChD,UAAU,CAACN,MAAM,CAE5C,KAAM,CAAAK,KAAK,CAAGhB,EAAE,CAACkE,UAAU,CAAC,CAC1BC,MAAM,CAAE,CACNnE,EAAE,CAACmE,MAAM,CAACC,KAAK,CAAC,CACdC,UAAU,CAAE,CAACL,SAAS,CAAC,CACvBM,KAAK,CAAE,GAAG,CACVC,UAAU,CAAE,MAAM,CAClBC,iBAAiB,CAAExE,EAAE,CAACyE,YAAY,CAACC,EAAE,CAAC,CAAEA,EAAE,CAAE,KAAM,CAAC,CACrD,CAAC,CAAC,CACF1E,EAAE,CAACmE,MAAM,CAACQ,OAAO,CAAC,CAAEC,IAAI,CAAE,GAAI,CAAC,CAAC,CAChC5E,EAAE,CAACmE,MAAM,CAACC,KAAK,CAAC,CACdE,KAAK,CAAE,EAAE,CACTC,UAAU,CAAE,MAAM,CAClBC,iBAAiB,CAAExE,EAAE,CAACyE,YAAY,CAACC,EAAE,CAAC,CAAEA,EAAE,CAAE,KAAM,CAAC,CACrD,CAAC,CAAC,CACF1E,EAAE,CAACmE,MAAM,CAACQ,OAAO,CAAC,CAAEC,IAAI,CAAE,GAAI,CAAC,CAAC,CAChC5E,EAAE,CAACmE,MAAM,CAACC,KAAK,CAAC,CACdE,KAAK,CAAE,EAAE,CACTC,UAAU,CAAE,MACd,CAAC,CAAC,CACFvE,EAAE,CAACmE,MAAM,CAACC,KAAK,CAAC,CACdE,KAAK,CAAEL,aAAa,CACpBM,UAAU,CAAE,SACd,CAAC,CAAC,CAEN,CAAC,CAAC,CAEFvD,KAAK,CAAC6D,OAAO,CAAC,CACZC,SAAS,CAAE9E,EAAE,CAAC+E,KAAK,CAACC,IAAI,CAAC,KAAK,CAAC,CAC/BC,IAAI,CAAE,yBAAyB,CAC/BC,OAAO,CAAE,CAAC,UAAU,CACtB,CAAC,CAAC,CAEF,MAAO,CAAAlE,KAAK,CACd,CAEA;AACQmE,mBAAmBA,CAACC,YAA4B,CAAoC,CAC1F,KAAM,CAAAC,QAAoB,CAAG,EAAE,CAC/B,KAAM,CAAAC,MAAkB,CAAG,EAAE,CAE7BF,YAAY,CAAC3D,OAAO,CAAC8D,IAAI,EAAI,CAC3B,KAAM,CAAAC,aAAa,CAAG,IAAI,CAAC1D,eAAe,CAACyD,IAAI,CAACxD,WAAW,CAAEwD,IAAI,CAACvD,MAAM,CAAC,CACzE,KAAM,CAAAyD,aAAa,CAAG,IAAI,CAACvE,eAAe,CAACwE,GAAG,CAACH,IAAI,CAACI,UAAU,CAAC,CAE/D,GAAIF,aAAa,GAAKG,SAAS,CAAE,CAC/BP,QAAQ,CAACQ,IAAI,CAACL,aAAa,CAAC,CAE5B;AACA,KAAM,CAAAM,KAAK,CAAG,GAAI,CAAArD,KAAK,CAAC,IAAI,CAACxB,UAAU,CAACN,MAAM,CAAC,CAAC+B,IAAI,CAAC,CAAC,CAAC,CACvDoD,KAAK,CAACL,aAAa,CAAC,CAAG,CAAC,CACxBH,MAAM,CAACO,IAAI,CAACC,KAAK,CAAC,CACpB,CACF,CAAC,CAAC,CAEF,KAAM,CAAAC,EAAE,CAAG/F,EAAE,CAACgG,QAAQ,CAACX,QAAQ,CAAC,CAChC,KAAM,CAAAY,EAAE,CAAGjG,EAAE,CAACgG,QAAQ,CAACV,MAAM,CAAC,CAE9B,MAAO,CAAES,EAAE,CAAEE,EAAG,CAAC,CACnB,CAEA;AACA,KAAM,CAAAC,UAAUA,CAACd,YAA4B,CAA+C,CAC1F,IAAI,CAAC7D,oBAAoB,CAAC,CAAC,CAE3B,GAAI6D,YAAY,CAACzE,MAAM,CAAG,EAAE,CAAE,CAC5B,KAAM,IAAI,CAAAwF,KAAK,CAAC,wDAAwD,CAAC,CAC3E,CAEA;AACA,IAAI,CAACnF,KAAK,CAAG,IAAI,CAAC+C,UAAU,CAAC,CAAC,CAE9B;AACA,KAAM,CAAEgC,EAAE,CAAEE,EAAG,CAAC,CAAG,IAAI,CAACd,mBAAmB,CAACC,YAAY,CAAC,CAEzD,GAAI,CACF;AACA,KAAM,CAAAgB,OAAO,CAAG,KAAM,KAAI,CAACpF,KAAK,CAACqF,GAAG,CAACN,EAAE,CAAEE,EAAE,CAAE,CAC3CK,MAAM,CAAE,GAAG,CACXC,SAAS,CAAE,EAAE,CACbC,eAAe,CAAE,GAAG,CACpBC,OAAO,CAAE,IAAI,CACbC,SAAS,CAAE,CACTC,UAAU,CAAE,KAAAA,CAAOC,KAAK,CAAEC,IAAI,GAAK,CACjC,GAAID,KAAK,CAAG,EAAE,GAAK,CAAC,CAAE,KAAAE,UAAA,CAAAC,SAAA,CACpBC,OAAO,CAAC1D,GAAG,UAAA2D,MAAA,CAAUL,KAAK,cAAAK,MAAA,CAAYJ,IAAI,SAAJA,IAAI,kBAAAC,UAAA,CAAJD,IAAI,CAAE5B,IAAI,UAAA6B,UAAA,iBAAVA,UAAA,CAAYI,OAAO,CAAC,CAAC,CAAC,kBAAAD,MAAA,CAAgBJ,IAAI,SAAJA,IAAI,kBAAAE,SAAA,CAAJF,IAAI,CAAEM,GAAG,UAAAJ,SAAA,iBAATA,SAAA,CAAWG,OAAO,CAAC,CAAC,CAAC,CAAE,CAAC,CACtG,CACF,CACF,CACF,CAAC,CAAC,CAEF;AACA,KAAM,CAAAE,aAAa,CAAGhB,OAAO,CAACA,OAAO,CAACe,GAAG,CAACf,OAAO,CAACA,OAAO,CAACe,GAAG,CAACxG,MAAM,CAAG,CAAC,CAAW,CACnF,KAAM,CAAA0G,SAAS,CAAGjB,OAAO,CAACA,OAAO,CAACnB,IAAI,CAACmB,OAAO,CAACA,OAAO,CAACnB,IAAI,CAACtE,MAAM,CAAG,CAAC,CAAW,CAEjF;AACA,KAAM,KAAI,CAAC2G,SAAS,CAACF,aAAa,CAAEhC,YAAY,CAACzE,MAAM,CAAC,CAExD;AACAoF,EAAE,CAACwB,OAAO,CAAC,CAAC,CACZtB,EAAE,CAACsB,OAAO,CAAC,CAAC,CAEZ,MAAO,CAAEC,QAAQ,CAAEJ,aAAa,CAAEnC,IAAI,CAAEoC,SAAU,CAAC,CACrD,CAAE,MAAOI,KAAK,CAAE,CACd1B,EAAE,CAACwB,OAAO,CAAC,CAAC,CACZtB,EAAE,CAACsB,OAAO,CAAC,CAAC,CACZ,KAAM,CAAAE,KAAK,CACb,CACF,CAEA;AACA,KAAM,CAAAC,qBAAqBA,CAAC3F,WAAmB,CAAEC,MAAc,CAAoC,CACjG,GAAI,CAAC,IAAI,CAAChB,KAAK,CAAE,CACf,KAAM,KAAI,CAAC2G,eAAe,CAAC,CAAC,CAC9B,CAEA,GAAI,CAAC,IAAI,CAAC3G,KAAK,CAAE,CACf,MAAO,KAAI,CACb,CAEA,KAAM,CAAAqE,QAAQ,CAAG,IAAI,CAACvD,eAAe,CAACC,WAAW,CAAEC,MAAM,CAAC,CAC1D,KAAM,CAAA4F,KAAK,CAAG5H,EAAE,CAACgG,QAAQ,CAAC,CAACX,QAAQ,CAAC,CAAC,CAErC,GAAI,CACF,KAAM,CAAAwC,UAAU,CAAG,IAAI,CAAC7G,KAAK,CAAC8G,OAAO,CAACF,KAAK,CAAc,CACzD,KAAM,CAAAG,aAAa,CAAG,KAAM,CAAAF,UAAU,CAACtC,IAAI,CAAC,CAAC,CAE7C;AACA,GAAI,CAAAyC,OAAO,CAAG,CAAC,CACf,GAAI,CAAAC,iBAAiB,CAAG,CAAC,CAEzB,IAAK,GAAI,CAAAnF,CAAC,CAAG,CAAC,CAAEA,CAAC,CAAGiF,aAAa,CAACpH,MAAM,CAAEmC,CAAC,EAAE,CAAE,CAC7C,GAAIiF,aAAa,CAACjF,CAAC,CAAC,CAAGkF,OAAO,CAAE,CAC9BA,OAAO,CAAGD,aAAa,CAACjF,CAAC,CAAC,CAC1BmF,iBAAiB,CAAGnF,CAAC,CACvB,CACF,CAEA,KAAM,CAAA6C,UAAU,CAAG,IAAI,CAACvE,eAAe,CAACsE,GAAG,CAACuC,iBAAiB,CAAC,CAE9D,GAAItC,UAAU,EAAIqC,OAAO,CAAG,GAAG,CAAE,CAAE;AACjCJ,KAAK,CAACL,OAAO,CAAC,CAAC,CACfM,UAAU,CAACN,OAAO,CAAC,CAAC,CAEpB,MAAO,CACL5B,UAAU,CACVuC,UAAU,CAAEF,OAAO,CACnBG,SAAS,CAAE,gBAAgB,CAC3B9C,QAAQ,CAAE,CAAC,aAAa,CAAE,QAAQ,CAAE,UAAU,CAAC,CAC/C+C,YAAY,CAAE,GAAI,CAAAC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CACvC,CAAC,CACH,CAEAV,KAAK,CAACL,OAAO,CAAC,CAAC,CACfM,UAAU,CAACN,OAAO,CAAC,CAAC,CACpB,MAAO,KAAI,CACb,CAAE,MAAOE,KAAK,CAAE,CACdG,KAAK,CAACL,OAAO,CAAC,CAAC,CACfP,OAAO,CAACS,KAAK,CAAC,8BAA8B,CAAEA,KAAK,CAAC,CACpD,MAAO,KAAI,CACb,CACF,CAEA;AACA,KAAc,CAAAH,SAASA,CAACE,QAAgB,CAAEe,YAAoB,CAAiB,CAC7E,GAAI,CAAC,IAAI,CAACvH,KAAK,CAAE,OAEjB,GAAI,CACF,KAAM,CAAAwH,SAAS,CAAG,KAAM,KAAI,CAACxH,KAAK,CAACyH,IAAI,CAACzI,EAAE,CAAC0I,EAAE,CAACC,eAAe,CAAC,KAAO,CAAAC,SAAS,EAAK,CACjF,KAAM,CAAAC,SAAS,CAAGC,IAAI,CAACC,SAAS,CAACH,SAAS,CAACI,aAAa,CAAC,CACzD,KAAM,CAAAC,WAAW,CAAGL,SAAS,CAACM,UAAU,CAAGzG,KAAK,CAAC0G,IAAI,CAAC,GAAI,CAAAC,UAAU,CAACR,SAAS,CAACM,UAAyB,CAAC,CAAC,CAAG,EAAE,CAE/G,MAAO,CACLF,aAAa,CAAEH,SAAS,CACxBK,UAAU,CAAED,WAAW,CACvBI,WAAW,CAAET,SAAS,CAACS,WAAW,CAClCC,kBAAkB,CAAE,CAClBC,SAAS,CAAE,GAAI,CAAAlB,IAAI,CAAC,CAAC,CACrBmB,iBAAiB,CAAE,MACrB,CACF,CAAC,CACH,CAAC,CAAC,CAAC,CAEH,KAAM,CAAAC,OAAgB,CAAG,CACvB5H,EAAE,CAAE,IAAI,CAAC6H,UAAU,CAAC,CAAC,CACrBC,IAAI,CAAE,kCAAkC,CACxCC,OAAO,CAAE,KAAK,CACdzB,SAAS,CAAE,gBAAgB,CAC3BX,QAAQ,CACRY,YAAY,CAAE,GAAI,CAAAC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CACtCC,YAAY,CACZsB,QAAQ,CAAE,IAAI,CACdrB,SAAS,CAAEM,IAAI,CAACC,SAAS,CAACP,SAAS,CACrC,CAAC,CAED,KAAM,CAAAsB,MAAM,CAAG,IAAI,CAACC,YAAY,CAAC,CAAC,CAClC;AACAD,MAAM,CAACrI,OAAO,CAACuI,CAAC,EAAIA,CAAC,CAACH,QAAQ,CAAG,KAAK,CAAC,CACvCC,MAAM,CAACjE,IAAI,CAAC4D,OAAO,CAAC,CAEpBxJ,kBAAkB,CAACgK,SAAS,CAAC,IAAI,CAACnJ,eAAe,CAAEgJ,MAAM,CAAC,CAC5D,CAAE,MAAOrC,KAAK,CAAE,CACdT,OAAO,CAACS,KAAK,CAAC,uBAAuB,CAAEA,KAAK,CAAC,CAC/C,CACF,CAEA;AACA,KAAc,CAAAE,eAAeA,CAAA,CAAqB,CAChD,GAAI,CACF,KAAM,CAAAmC,MAAM,CAAG,IAAI,CAACC,YAAY,CAAC,CAAC,CAClC,KAAM,CAAAG,WAAW,CAAGJ,MAAM,CAACK,IAAI,CAACH,CAAC,EAAIA,CAAC,CAACH,QAAQ,CAAC,CAEhD,GAAI,CAACK,WAAW,CAAE,MAAO,MAAK,CAE9B,KAAM,CAAA1B,SAAS,CAAGM,IAAI,CAACsB,KAAK,CAACF,WAAW,CAAC1B,SAAS,CAAC,CAEnD,IAAI,CAACxH,KAAK,CAAG,KAAM,CAAAhB,EAAE,CAACqK,eAAe,CAACrK,EAAE,CAAC0I,EAAE,CAAC4B,UAAU,CAAC,CACrDtB,aAAa,CAAEF,IAAI,CAACsB,KAAK,CAAC5B,SAAS,CAACQ,aAAa,CAAC,CAClDE,UAAU,CAAE,GAAI,CAAAE,UAAU,CAACZ,SAAS,CAACU,UAAU,CAAC,CAACqB,MAAM,CACvDlB,WAAW,CAAEb,SAAS,CAACa,WACzB,CAAC,CAAC,CAAC,CAEH,MAAO,KAAI,CACb,CAAE,MAAO5B,KAAK,CAAE,CACdT,OAAO,CAACS,KAAK,CAAC,uBAAuB,CAAEA,KAAK,CAAC,CAC7C,MAAO,MAAK,CACd,CACF,CAEA;AACAsC,YAAYA,CAAA,CAAc,CACxB,MAAO,CAAA9J,kBAAkB,CAACuK,QAAQ,CAAY,IAAI,CAAC1J,eAAe,CAAE,EAAE,CAAC,CACzE,CAEA;AACA2J,eAAeA,CAAC1I,WAAmB,CAAEC,MAAc,CAAE2D,UAAkB,CAAQ,CAC7E,KAAM,CAAAP,YAAY,CAAG,IAAI,CAACsF,eAAe,CAAC,CAAC,CAE3C,KAAM,CAAAC,OAAqB,CAAG,CAC5B9I,EAAE,CAAE,IAAI,CAAC6H,UAAU,CAAC,CAAC,CACrB3H,WAAW,CACXC,MAAM,CACN2D,UAAU,CACVN,QAAQ,CAAE,IAAI,CAACvD,eAAe,CAACC,WAAW,CAAEC,MAAM,CAAC,CACnD4I,WAAW,CAAE,GAAI,CAAAvC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CACtC,CAAC,CAEDlD,YAAY,CAACS,IAAI,CAAC8E,OAAO,CAAC,CAC1B,IAAI,CAACE,gBAAgB,CAACzF,YAAY,CAAC,CACrC,CAEA;AACAsF,eAAeA,CAAA,CAAmB,CAChC,MAAO,CAAAzK,kBAAkB,CAACuK,QAAQ,CAAiB,IAAI,CAACzJ,sBAAsB,CAAE,EAAE,CAAC,CACrF,CAEA;AACQ8J,gBAAgBA,CAACtF,IAAoB,CAAQ,CACnDtF,kBAAkB,CAACgK,SAAS,CAAC,IAAI,CAAClJ,sBAAsB,CAAEwE,IAAI,CAAC,CACjE,CAEA;AACAuF,iBAAiBA,CAAA,CAAS,CACxB,IAAI,CAACD,gBAAgB,CAAC,EAAE,CAAC,CAC3B,CAEA;AACAE,aAAaA,CAAA,CAAmF,CAC9F,KAAM,CAAAjB,MAAM,CAAG,IAAI,CAACC,YAAY,CAAC,CAAC,CAClC,KAAM,CAAAG,WAAW,CAAGJ,MAAM,CAACK,IAAI,CAACH,CAAC,EAAIA,CAAC,CAACH,QAAQ,CAAC,EAAI,IAAI,CACxD,KAAM,CAAAmB,gBAAgB,CAAG,IAAI,CAACN,eAAe,CAAC,CAAC,CAAC/J,MAAM,CAEtD,MAAO,CACLsK,WAAW,CAAEnB,MAAM,CAACnJ,MAAM,CAC1BuJ,WAAW,CACXc,gBACF,CAAC,CACH,CAEA;AACQtB,UAAUA,CAAA,CAAW,CAC3B,SAAAzC,MAAA,CAAUoB,IAAI,CAAC6C,GAAG,CAAC,CAAC,MAAAjE,MAAA,CAAIlE,IAAI,CAACoI,MAAM,CAAC,CAAC,CAAC5H,QAAQ,CAAC,EAAE,CAAC,CAAC6H,MAAM,CAAC,CAAC,CAAE,CAAC,CAAC,EACjE,CAEA;AACAC,wBAAwBA,CAAA,CAAS,CAC/B,KAAM,CAAAC,YAAY,CAAG,IAAI,CAACZ,eAAe,CAAC,CAAC,CAC3C,GAAIY,YAAY,CAAC3K,MAAM,CAAG,CAAC,CAAE,OAE7B;AACA,KAAM,CAAA4K,qBAAqB,CAAG,IAAI,CAACtK,UAAU,CAACkJ,IAAI,CAACqB,CAAC,EAAIA,CAAC,CAAC7B,IAAI,GAAK,eAAe,CAAC,CACnF,GAAI,CAAC4B,qBAAqB,CAAE,OAE5B;AACA,KAAM,CAAAE,UAAU,CAAG,CACjB,CAAE1J,WAAW,CAAE,gBAAgB,CAAEC,MAAM,CAAE,IAAI,CAAE2D,UAAU,CAAE4F,qBAAqB,CAAC1J,EAAG,CAAC,CACrF,CAAEE,WAAW,CAAE,qBAAqB,CAAEC,MAAM,CAAE,CAAC,IAAI,CAAE2D,UAAU,CAAE4F,qBAAqB,CAAC1J,EAAG,CAAC,CAC3F,CAAEE,WAAW,CAAE,kBAAkB,CAAEC,MAAM,CAAE,CAAC,GAAG,CAAE2D,UAAU,CAAE4F,qBAAqB,CAAC1J,EAAG,CAAC,CACvF,CAAEE,WAAW,CAAE,cAAc,CAAEC,MAAM,CAAE,CAAC,EAAE,CAAE2D,UAAU,CAAE4F,qBAAqB,CAAC1J,EAAG,CAAC,CAClF,CAAEE,WAAW,CAAE,gBAAgB,CAAEC,MAAM,CAAE,IAAI,CAAE2D,UAAU,CAAE4F,qBAAqB,CAAC1J,EAAG,CAAC,CACtF,CAED4J,UAAU,CAAChK,OAAO,CAAC8D,IAAI,EAAI,CACzB,IAAI,CAACkF,eAAe,CAAClF,IAAI,CAACxD,WAAW,CAAEwD,IAAI,CAACvD,MAAM,CAAEuD,IAAI,CAACI,UAAU,CAAC,CACtE,CAAC,CAAC,CACJ,CACF,CAEA,MAAO,MAAM,CAAA+F,uBAAuB,CAAG,GAAI,CAAA9K,uBAAuB,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}