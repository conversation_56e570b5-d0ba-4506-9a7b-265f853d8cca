{"ast": null, "code": "/*\n  Brill's POS Testing class\n  Copyright (C) 2017 Hugo <PERSON>\n\n  This program is free software: you can redistribute it and/or modify\n  it under the terms of the GNU General Public License as published by\n  the Free Software Foundation, either version 3 of the License, or\n  (at your option) any later version.\n\n  This program is distributed in the hope that it will be useful,\n  but WITHOUT ANY WARRANTY; without even the implied warranty of\n  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the\n  GNU General Public License for more details.\n\n  You should have received a copy of the GNU General Public License\n  along with this program.  If not, see <http://www.gnu.org/licenses/>.\n*/\n\n'use strict';\n\nclass BrillPOSTester {\n  test(corpus, tagger) {\n    let totalWords = 0;\n    let correctTagsLexicon = 0;\n    let correctTagsAfterRules = 0;\n\n    // Tag the corpus using the tagger\n    corpus.sentences.forEach(function (sentence) {\n      const s = sentence.taggedWords.map(function (token) {\n        return token.token;\n      });\n\n      // Use the lexicon to tag the sentence\n      const taggedSentence = tagger.tagWithLexicon(s);\n      // Count the right tags\n      sentence.taggedWords.forEach(function (token, i) {\n        totalWords++;\n        if (token.tag === taggedSentence.taggedWords[i].tag) {\n          correctTagsLexicon++;\n        }\n      });\n\n      // Use the rule set to tag the sentence\n      const taggedSentenceAfterRules = tagger.applyRules(taggedSentence);\n      // Count the right tags\n      sentence.taggedWords.forEach(function (token, i) {\n        if (token.tag === taggedSentenceAfterRules.taggedWords[i].tag) {\n          correctTagsAfterRules++;\n        }\n      });\n    });\n\n    // Return percentage right\n    return [100 * correctTagsLexicon / totalWords, 100 * correctTagsAfterRules / totalWords];\n  }\n}\nmodule.exports = BrillPOSTester;", "map": {"version": 3, "names": ["BrillPOSTester", "test", "corpus", "tagger", "totalWords", "correctTagsLexicon", "correctTagsAfterRules", "sentences", "for<PERSON>ach", "sentence", "s", "taggedWords", "map", "token", "taggedSentence", "tagWithLexicon", "i", "tag", "taggedSentenceAfterRules", "applyRules", "module", "exports"], "sources": ["C:/tmsft/node_modules/natural/lib/natural/brill_pos_tagger/lib/Brill_POS_Tester.js"], "sourcesContent": ["/*\n  Brill's POS Testing class\n  Copyright (C) 2017 Hugo <PERSON>\n\n  This program is free software: you can redistribute it and/or modify\n  it under the terms of the GNU General Public License as published by\n  the Free Software Foundation, either version 3 of the License, or\n  (at your option) any later version.\n\n  This program is distributed in the hope that it will be useful,\n  but WITHOUT ANY WARRANTY; without even the implied warranty of\n  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the\n  GNU General Public License for more details.\n\n  You should have received a copy of the GNU General Public License\n  along with this program.  If not, see <http://www.gnu.org/licenses/>.\n*/\n\n'use strict'\n\nclass BrillPOSTester {\n  test (corpus, tagger) {\n    let totalWords = 0\n    let correctTagsLexicon = 0\n    let correctTagsAfterRules = 0\n\n    // Tag the corpus using the tagger\n    corpus.sentences.forEach(function (sentence) {\n      const s = sentence.taggedWords.map(function (token) {\n        return token.token\n      })\n\n      // Use the lexicon to tag the sentence\n      const taggedSentence = tagger.tagWithLexicon(s)\n      // Count the right tags\n      sentence.taggedWords.forEach(function (token, i) {\n        totalWords++\n        if (token.tag === taggedSentence.taggedWords[i].tag) {\n          correctTagsLexicon++\n        }\n      })\n\n      // Use the rule set to tag the sentence\n      const taggedSentenceAfterRules = tagger.applyRules(taggedSentence)\n      // Count the right tags\n      sentence.taggedWords.forEach(function (token, i) {\n        if (token.tag === taggedSentenceAfterRules.taggedWords[i].tag) {\n          correctTagsAfterRules++\n        }\n      })\n    })\n\n    // Return percentage right\n    return [100 * correctTagsLexicon / totalWords, 100 * correctTagsAfterRules / totalWords]\n  }\n}\n\nmodule.exports = BrillPOSTester\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,YAAY;;AAEZ,MAAMA,cAAc,CAAC;EACnBC,IAAIA,CAAEC,MAAM,EAAEC,MAAM,EAAE;IACpB,IAAIC,UAAU,GAAG,CAAC;IAClB,IAAIC,kBAAkB,GAAG,CAAC;IAC1B,IAAIC,qBAAqB,GAAG,CAAC;;IAE7B;IACAJ,MAAM,CAACK,SAAS,CAACC,OAAO,CAAC,UAAUC,QAAQ,EAAE;MAC3C,MAAMC,CAAC,GAAGD,QAAQ,CAACE,WAAW,CAACC,GAAG,CAAC,UAAUC,KAAK,EAAE;QAClD,OAAOA,KAAK,CAACA,KAAK;MACpB,CAAC,CAAC;;MAEF;MACA,MAAMC,cAAc,GAAGX,MAAM,CAACY,cAAc,CAACL,CAAC,CAAC;MAC/C;MACAD,QAAQ,CAACE,WAAW,CAACH,OAAO,CAAC,UAAUK,KAAK,EAAEG,CAAC,EAAE;QAC/CZ,UAAU,EAAE;QACZ,IAAIS,KAAK,CAACI,GAAG,KAAKH,cAAc,CAACH,WAAW,CAACK,CAAC,CAAC,CAACC,GAAG,EAAE;UACnDZ,kBAAkB,EAAE;QACtB;MACF,CAAC,CAAC;;MAEF;MACA,MAAMa,wBAAwB,GAAGf,MAAM,CAACgB,UAAU,CAACL,cAAc,CAAC;MAClE;MACAL,QAAQ,CAACE,WAAW,CAACH,OAAO,CAAC,UAAUK,KAAK,EAAEG,CAAC,EAAE;QAC/C,IAAIH,KAAK,CAACI,GAAG,KAAKC,wBAAwB,CAACP,WAAW,CAACK,CAAC,CAAC,CAACC,GAAG,EAAE;UAC7DX,qBAAqB,EAAE;QACzB;MACF,CAAC,CAAC;IACJ,CAAC,CAAC;;IAEF;IACA,OAAO,CAAC,GAAG,GAAGD,kBAAkB,GAAGD,UAAU,EAAE,GAAG,GAAGE,qBAAqB,GAAGF,UAAU,CAAC;EAC1F;AACF;AAEAgB,MAAM,CAACC,OAAO,GAAGrB,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}