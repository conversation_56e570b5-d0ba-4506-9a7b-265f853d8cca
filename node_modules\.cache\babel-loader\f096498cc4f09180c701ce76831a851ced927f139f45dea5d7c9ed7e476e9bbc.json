{"ast": null, "code": "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { Identity } from '@tensorflow/tfjs-core';\nexport function identity(args) {\n  const {\n    inputs,\n    backend\n  } = args;\n  const {\n    x\n  } = inputs;\n  backend.incRef(x.dataId);\n  return {\n    dataId: x.dataId,\n    shape: x.shape,\n    dtype: x.dtype\n  };\n}\nexport const identityConfig = {\n  kernelName: Identity,\n  backendName: 'webgl',\n  kernelFunc: identity\n};", "map": {"version": 3, "names": ["Identity", "identity", "args", "inputs", "backend", "x", "incRef", "dataId", "shape", "dtype", "identityConfig", "kernelName", "backendName", "kernelFunc"], "sources": ["C:\\tfjs-backend-webgl\\src\\kernels\\Identity.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {Identity, IdentityInputs, KernelConfig, KernelFunc, TensorInfo} from '@tensorflow/tfjs-core';\n\nimport {MathBackendWebGL} from '../backend_webgl';\n\nexport function identity(\n    args: {inputs: IdentityInputs, backend: MathBackendWebGL}): TensorInfo {\n  const {inputs, backend} = args;\n  const {x} = inputs;\n\n  backend.incRef(x.dataId);\n\n  return {dataId: x.dataId, shape: x.shape, dtype: x.dtype};\n}\n\nexport const identityConfig: KernelConfig = {\n  kernelName: Identity,\n  backendName: 'webgl',\n  kernelFunc: identity as unknown as KernelFunc\n};\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAiBA,SAAQA,QAAQ,QAA6D,uBAAuB;AAIpG,OAAM,SAAUC,QAAQA,CACpBC,IAAyD;EAC3D,MAAM;IAACC,MAAM;IAAEC;EAAO,CAAC,GAAGF,IAAI;EAC9B,MAAM;IAACG;EAAC,CAAC,GAAGF,MAAM;EAElBC,OAAO,CAACE,MAAM,CAACD,CAAC,CAACE,MAAM,CAAC;EAExB,OAAO;IAACA,MAAM,EAAEF,CAAC,CAACE,MAAM;IAAEC,KAAK,EAAEH,CAAC,CAACG,KAAK;IAAEC,KAAK,EAAEJ,CAAC,CAACI;EAAK,CAAC;AAC3D;AAEA,OAAO,MAAMC,cAAc,GAAiB;EAC1CC,UAAU,EAAEX,QAAQ;EACpBY,WAAW,EAAE,OAAO;EACpBC,UAAU,EAAEZ;CACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}