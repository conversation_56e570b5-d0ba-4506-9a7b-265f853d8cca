{"ast": null, "code": "/**\n * @license\n * Copyright 2018 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nclass PassthroughLoader {\n  constructor(modelArtifacts) {\n    this.modelArtifacts = modelArtifacts;\n  }\n  load() {\n    return this.modelArtifacts;\n  }\n}\nclass PassthroughSaver {\n  constructor(saveHandler) {\n    this.saveHandler = saveHandler;\n  }\n  save(modelArtifacts) {\n    return this.saveHandler(modelArtifacts);\n  }\n}\nclass PassthroughAsync {\n  constructor(handler) {\n    if (handler.load) {\n      this.load = () => Promise.resolve(handler.load());\n    }\n    if (handler.save) {\n      this.save = modelArtifacts => Promise.resolve(handler.save(modelArtifacts));\n    }\n  }\n}\n/**\n * Creates an IOHandler that loads model artifacts from memory.\n *\n * When used in conjunction with `tf.loadLayersModel`, an instance of\n * `tf.LayersModel` (Keras-style) can be constructed from the loaded artifacts.\n *\n * ```js\n * const model = await tf.loadLayersModel(tf.io.fromMemory(\n *     modelTopology, weightSpecs, weightData));\n * ```\n *\n * @param modelArtifacts a object containing model topology (i.e., parsed from\n *   the JSON format).\n * @param weightSpecs An array of `WeightsManifestEntry` objects describing the\n *   names, shapes, types, and quantization of the weight data. Optional.\n * @param weightData A single `ArrayBuffer` containing the weight data,\n *   concatenated in the order described by the weightSpecs. Optional.\n * @param trainingConfig Model training configuration. Optional.\n *\n * @returns A passthrough `IOHandler` that simply loads the provided data.\n */\nexport function fromMemory(modelArtifacts, weightSpecs, weightData, trainingConfig) {\n  const args = arguments;\n  return new PassthroughAsync(fromMemorySync(...args));\n}\n/**\n * Creates an IOHandler that loads model artifacts from memory.\n *\n * When used in conjunction with `tf.loadLayersModel`, an instance of\n * `tf.LayersModel` (Keras-style) can be constructed from the loaded artifacts.\n *\n * ```js\n * const model = await tf.loadLayersModel(tf.io.fromMemory(\n *     modelTopology, weightSpecs, weightData));\n * ```\n *\n * @param modelArtifacts a object containing model topology (i.e., parsed from\n *   the JSON format).\n * @param weightSpecs An array of `WeightsManifestEntry` objects describing the\n *   names, shapes, types, and quantization of the weight data. Optional.\n * @param weightData A single `ArrayBuffer` containing the weight data,\n *   concatenated in the order described by the weightSpecs. Optional.\n * @param trainingConfig Model training configuration. Optional.\n *\n * @returns A passthrough `IOHandlerSync` that simply loads the provided data.\n */\nexport function fromMemorySync(modelArtifacts, weightSpecs, weightData, trainingConfig) {\n  if (arguments.length === 1) {\n    const isModelArtifacts = modelArtifacts.modelTopology != null || modelArtifacts.weightSpecs != null;\n    if (isModelArtifacts) {\n      return new PassthroughLoader(modelArtifacts);\n    } else {\n      // Legacy support: with only modelTopology.\n      // TODO(cais): Remove this deprecated API.\n      console.warn('Please call tf.io.fromMemory() with only one argument. ' + 'The argument should be of type ModelArtifacts. ' + 'The multi-argument signature of tf.io.fromMemory() has been ' + 'deprecated and will be removed in a future release.');\n      return new PassthroughLoader({\n        modelTopology: modelArtifacts\n      });\n    }\n  } else {\n    // Legacy support.\n    // TODO(cais): Remove this deprecated API.\n    console.warn('Please call tf.io.fromMemory() with only one argument. ' + 'The argument should be of type ModelArtifacts. ' + 'The multi-argument signature of tf.io.fromMemory() has been ' + 'deprecated and will be removed in a future release.');\n    return new PassthroughLoader({\n      modelTopology: modelArtifacts,\n      weightSpecs,\n      weightData,\n      trainingConfig\n    });\n  }\n}\n/**\n * Creates an IOHandler that passes saved model artifacts to a callback.\n *\n * ```js\n * function handleSave(artifacts) {\n *   // ... do something with the artifacts ...\n *   return {modelArtifactsInfo: {...}, ...};\n * }\n *\n * const saveResult = model.save(tf.io.withSaveHandler(handleSave));\n * ```\n *\n * @param saveHandler A function that accepts a `ModelArtifacts` and returns a\n *     promise that resolves to a `SaveResult`.\n */\nexport function withSaveHandler(saveHandler) {\n  return new PassthroughSaver(saveHandler);\n}\n/**\n * Creates an IOHandlerSync that passes saved model artifacts to a callback.\n *\n * ```js\n * function handleSave(artifacts) {\n *   // ... do something with the artifacts ...\n *   return {modelArtifactsInfo: {...}, ...};\n * }\n *\n * const saveResult = model.save(tf.io.withSaveHandler(handleSave));\n * ```\n *\n * @param saveHandler A function that accepts a `ModelArtifacts` and returns a\n *     `SaveResult`.\n */\nexport function withSaveHandlerSync(saveHandler) {\n  return new PassthroughSaver(saveHandler);\n}", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "constructor", "modelArtifacts", "load", "PassthroughSaver", "saveHandler", "save", "PassthroughAsync", "handler", "Promise", "resolve", "fromMemory", "weightSpecs", "weightData", "trainingConfig", "args", "arguments", "fromMemorySync", "length", "isModelArtifacts", "modelTopology", "console", "warn", "with<PERSON><PERSON><PERSON><PERSON><PERSON>", "withSaveHandlerSync"], "sources": ["C:\\tfjs-core\\src\\io\\passthrough.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2018 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\n/**\n * IOHandlers that pass through the in-memory ModelArtifacts format.\n */\n\nimport {IOHandler, IOHandlerSync, LoadHandler, ModelArtifacts, SaveHandler, SaveResult, TrainingConfig, WeightData, WeightsManifestEntry} from './types';\n\nclass PassthroughLoader implements IOHandlerSync {\n  constructor(private readonly modelArtifacts?: ModelArtifacts) {}\n\n  load(): ModelArtifacts {\n    return this.modelArtifacts;\n  }\n}\n\nclass PassthroughSaver<R extends SaveResult | Promise<SaveResult>> {\n  constructor(\n    private readonly saveHandler: (artifacts: ModelArtifacts) => R) {}\n\n  save(modelArtifacts: ModelArtifacts): R {\n    return this.saveHandler(modelArtifacts);\n  }\n}\n\nclass PassthroughAsync implements IOHandler {\n  load?: LoadHandler;\n  save?: SaveHandler;\n\n  constructor(handler: IOHandlerSync) {\n    if (handler.load) {\n      this.load = () => Promise.resolve(handler.load());\n    }\n    if (handler.save) {\n      this.save = (modelArtifacts: ModelArtifacts) =>\n        Promise.resolve(handler.save(modelArtifacts));\n    }\n  }\n}\n\n/**\n * Creates an IOHandler that loads model artifacts from memory.\n *\n * When used in conjunction with `tf.loadLayersModel`, an instance of\n * `tf.LayersModel` (Keras-style) can be constructed from the loaded artifacts.\n *\n * ```js\n * const model = await tf.loadLayersModel(tf.io.fromMemory(\n *     modelTopology, weightSpecs, weightData));\n * ```\n *\n * @param modelArtifacts a object containing model topology (i.e., parsed from\n *   the JSON format).\n * @param weightSpecs An array of `WeightsManifestEntry` objects describing the\n *   names, shapes, types, and quantization of the weight data. Optional.\n * @param weightData A single `ArrayBuffer` containing the weight data,\n *   concatenated in the order described by the weightSpecs. Optional.\n * @param trainingConfig Model training configuration. Optional.\n *\n * @returns A passthrough `IOHandler` that simply loads the provided data.\n */\nexport function fromMemory(\n    modelArtifacts: {}|ModelArtifacts, weightSpecs?: WeightsManifestEntry[],\n    weightData?: WeightData, trainingConfig?: TrainingConfig): IOHandler {\n\n  const args = arguments as unknown as Parameters<typeof fromMemory>;\n  return new PassthroughAsync(fromMemorySync(...args));\n}\n\n/**\n * Creates an IOHandler that loads model artifacts from memory.\n *\n * When used in conjunction with `tf.loadLayersModel`, an instance of\n * `tf.LayersModel` (Keras-style) can be constructed from the loaded artifacts.\n *\n * ```js\n * const model = await tf.loadLayersModel(tf.io.fromMemory(\n *     modelTopology, weightSpecs, weightData));\n * ```\n *\n * @param modelArtifacts a object containing model topology (i.e., parsed from\n *   the JSON format).\n * @param weightSpecs An array of `WeightsManifestEntry` objects describing the\n *   names, shapes, types, and quantization of the weight data. Optional.\n * @param weightData A single `ArrayBuffer` containing the weight data,\n *   concatenated in the order described by the weightSpecs. Optional.\n * @param trainingConfig Model training configuration. Optional.\n *\n * @returns A passthrough `IOHandlerSync` that simply loads the provided data.\n */\nexport function fromMemorySync(\n    modelArtifacts: {}|ModelArtifacts, weightSpecs?: WeightsManifestEntry[],\n    weightData?: WeightData, trainingConfig?: TrainingConfig): IOHandlerSync {\n  if (arguments.length === 1) {\n    const isModelArtifacts =\n        (modelArtifacts as ModelArtifacts).modelTopology != null ||\n        (modelArtifacts as ModelArtifacts).weightSpecs != null;\n    if (isModelArtifacts) {\n      return new PassthroughLoader(modelArtifacts as ModelArtifacts);\n    } else {\n      // Legacy support: with only modelTopology.\n      // TODO(cais): Remove this deprecated API.\n      console.warn(\n          'Please call tf.io.fromMemory() with only one argument. ' +\n          'The argument should be of type ModelArtifacts. ' +\n          'The multi-argument signature of tf.io.fromMemory() has been ' +\n          'deprecated and will be removed in a future release.');\n      return new PassthroughLoader({modelTopology: modelArtifacts as {}});\n    }\n  } else {\n    // Legacy support.\n    // TODO(cais): Remove this deprecated API.\n    console.warn(\n        'Please call tf.io.fromMemory() with only one argument. ' +\n        'The argument should be of type ModelArtifacts. ' +\n        'The multi-argument signature of tf.io.fromMemory() has been ' +\n        'deprecated and will be removed in a future release.');\n    return new PassthroughLoader({\n      modelTopology: modelArtifacts as {},\n      weightSpecs,\n      weightData,\n      trainingConfig\n    });\n  }\n}\n\n/**\n * Creates an IOHandler that passes saved model artifacts to a callback.\n *\n * ```js\n * function handleSave(artifacts) {\n *   // ... do something with the artifacts ...\n *   return {modelArtifactsInfo: {...}, ...};\n * }\n *\n * const saveResult = model.save(tf.io.withSaveHandler(handleSave));\n * ```\n *\n * @param saveHandler A function that accepts a `ModelArtifacts` and returns a\n *     promise that resolves to a `SaveResult`.\n */\nexport function withSaveHandler(\n    saveHandler: (artifacts: ModelArtifacts) =>\n        Promise<SaveResult>): IOHandler {\n  return new PassthroughSaver(saveHandler);\n}\n\n/**\n * Creates an IOHandlerSync that passes saved model artifacts to a callback.\n *\n * ```js\n * function handleSave(artifacts) {\n *   // ... do something with the artifacts ...\n *   return {modelArtifactsInfo: {...}, ...};\n * }\n *\n * const saveResult = model.save(tf.io.withSaveHandler(handleSave));\n * ```\n *\n * @param saveHandler A function that accepts a `ModelArtifacts` and returns a\n *     `SaveResult`.\n */\nexport function withSaveHandlerSync(\n    saveHandler: (artifacts: ModelArtifacts) => SaveResult): IOHandlerSync {\n  return new PassthroughSaver<SaveResult>(saveHandler);\n}\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAuBA,MAAMA,iBAAiB;EACrBC,YAA6BC,cAA+B;IAA/B,KAAAA,cAAc,GAAdA,cAAc;EAAoB;EAE/DC,IAAIA,CAAA;IACF,OAAO,IAAI,CAACD,cAAc;EAC5B;;AAGF,MAAME,gBAAgB;EACpBH,YACmBI,WAA6C;IAA7C,KAAAA,WAAW,GAAXA,WAAW;EAAqC;EAEnEC,IAAIA,CAACJ,cAA8B;IACjC,OAAO,IAAI,CAACG,WAAW,CAACH,cAAc,CAAC;EACzC;;AAGF,MAAMK,gBAAgB;EAIpBN,YAAYO,OAAsB;IAChC,IAAIA,OAAO,CAACL,IAAI,EAAE;MAChB,IAAI,CAACA,IAAI,GAAG,MAAMM,OAAO,CAACC,OAAO,CAACF,OAAO,CAACL,IAAI,EAAE,CAAC;;IAEnD,IAAIK,OAAO,CAACF,IAAI,EAAE;MAChB,IAAI,CAACA,IAAI,GAAIJ,cAA8B,IACzCO,OAAO,CAACC,OAAO,CAACF,OAAO,CAACF,IAAI,CAACJ,cAAc,CAAC,CAAC;;EAEnD;;AAGF;;;;;;;;;;;;;;;;;;;;;AAqBA,OAAM,SAAUS,UAAUA,CACtBT,cAAiC,EAAEU,WAAoC,EACvEC,UAAuB,EAAEC,cAA+B;EAE1D,MAAMC,IAAI,GAAGC,SAAqD;EAClE,OAAO,IAAIT,gBAAgB,CAACU,cAAc,CAAC,GAAGF,IAAI,CAAC,CAAC;AACtD;AAEA;;;;;;;;;;;;;;;;;;;;;AAqBA,OAAM,SAAUE,cAAcA,CAC1Bf,cAAiC,EAAEU,WAAoC,EACvEC,UAAuB,EAAEC,cAA+B;EAC1D,IAAIE,SAAS,CAACE,MAAM,KAAK,CAAC,EAAE;IAC1B,MAAMC,gBAAgB,GACjBjB,cAAiC,CAACkB,aAAa,IAAI,IAAI,IACvDlB,cAAiC,CAACU,WAAW,IAAI,IAAI;IAC1D,IAAIO,gBAAgB,EAAE;MACpB,OAAO,IAAInB,iBAAiB,CAACE,cAAgC,CAAC;KAC/D,MAAM;MACL;MACA;MACAmB,OAAO,CAACC,IAAI,CACR,yDAAyD,GACzD,iDAAiD,GACjD,8DAA8D,GAC9D,qDAAqD,CAAC;MAC1D,OAAO,IAAItB,iBAAiB,CAAC;QAACoB,aAAa,EAAElB;MAAoB,CAAC,CAAC;;GAEtE,MAAM;IACL;IACA;IACAmB,OAAO,CAACC,IAAI,CACR,yDAAyD,GACzD,iDAAiD,GACjD,8DAA8D,GAC9D,qDAAqD,CAAC;IAC1D,OAAO,IAAItB,iBAAiB,CAAC;MAC3BoB,aAAa,EAAElB,cAAoB;MACnCU,WAAW;MACXC,UAAU;MACVC;KACD,CAAC;;AAEN;AAEA;;;;;;;;;;;;;;;AAeA,OAAM,SAAUS,eAAeA,CAC3BlB,WACuB;EACzB,OAAO,IAAID,gBAAgB,CAACC,WAAW,CAAC;AAC1C;AAEA;;;;;;;;;;;;;;;AAeA,OAAM,SAAUmB,mBAAmBA,CAC/BnB,WAAsD;EACxD,OAAO,IAAID,gBAAgB,CAAaC,WAAW,CAAC;AACtD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}