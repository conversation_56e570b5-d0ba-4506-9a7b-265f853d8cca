{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.transformArguments = exports.FIRST_KEY_INDEX = void 0;\nexports.FIRST_KEY_INDEX = 1;\nfunction transformArguments(key, start, stop) {\n  return ['LTRIM', key, start.toString(), stop.toString()];\n}\nexports.transformArguments = transformArguments;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "transformArguments", "FIRST_KEY_INDEX", "key", "start", "stop", "toString"], "sources": ["C:/tmsft/node_modules/@redis/client/dist/lib/commands/LTRIM.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.transformArguments = exports.FIRST_KEY_INDEX = void 0;\nexports.FIRST_KEY_INDEX = 1;\nfunction transformArguments(key, start, stop) {\n    return [\n        'LTRIM',\n        key,\n        start.toString(),\n        stop.toString()\n    ];\n}\nexports.transformArguments = transformArguments;\n"], "mappings": "AAAA,YAAY;;AACZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEC,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7DD,OAAO,CAACE,kBAAkB,GAAGF,OAAO,CAACG,eAAe,GAAG,KAAK,CAAC;AAC7DH,OAAO,CAACG,eAAe,GAAG,CAAC;AAC3B,SAASD,kBAAkBA,CAACE,GAAG,EAAEC,KAAK,EAAEC,IAAI,EAAE;EAC1C,OAAO,CACH,OAAO,EACPF,GAAG,EACHC,KAAK,CAACE,QAAQ,CAAC,CAAC,EAChBD,IAAI,CAACC,QAAQ,CAAC,CAAC,CAClB;AACL;AACAP,OAAO,CAACE,kBAAkB,GAAGA,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}