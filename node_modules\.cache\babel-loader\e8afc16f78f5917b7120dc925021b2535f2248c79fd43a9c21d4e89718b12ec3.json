{"ast": null, "code": "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { All, backend_util, util } from '@tensorflow/tfjs-core';\nimport { reduce } from '../kernel_utils/reduce';\nimport { reshape } from './Reshape';\nimport { transpose } from './Transpose';\nexport function all(args) {\n  const {\n    inputs,\n    backend,\n    attrs\n  } = args;\n  const {\n    x\n  } = inputs;\n  const {\n    axis,\n    keepDims\n  } = attrs;\n  const xRank = x.shape.length;\n  const origAxes = util.parseAxisParam(axis, x.shape);\n  let axes = origAxes;\n  const permutedAxes = backend_util.getAxesPermutation(axes, xRank);\n  let permutedX = x;\n  if (permutedAxes != null) {\n    permutedX = transpose({\n      inputs: {\n        x\n      },\n      backend,\n      attrs: {\n        perm: permutedAxes\n      }\n    });\n    axes = backend_util.getInnerMostAxes(axes.length, xRank);\n  }\n  backend_util.assertAxesAreInnerMostDims('all', axes, xRank);\n  const [outShape, reduceShape] = backend_util.computeOutAndReduceShapes(permutedX.shape, axes);\n  const inSize = util.sizeFromShape(reduceShape);\n  const a2D = reshape({\n    inputs: {\n      x: permutedX\n    },\n    backend,\n    attrs: {\n      shape: [-1, inSize]\n    }\n  });\n  const reduced = reduce(a2D, a2D.dtype, 'all', backend);\n  let res;\n  if (keepDims) {\n    const newShape = backend_util.expandShapeToKeepDim(outShape, origAxes);\n    res = reshape({\n      inputs: {\n        x: reduced\n      },\n      backend,\n      attrs: {\n        shape: newShape\n      }\n    });\n  } else {\n    res = reshape({\n      inputs: {\n        x: reduced\n      },\n      backend,\n      attrs: {\n        shape: outShape\n      }\n    });\n  }\n  backend.disposeIntermediateTensorInfo(a2D);\n  backend.disposeIntermediateTensorInfo(reduced);\n  if (permutedAxes != null) {\n    backend.disposeIntermediateTensorInfo(permutedX);\n  }\n  return res;\n}\nexport const allConfig = {\n  kernelName: All,\n  backendName: 'webgl',\n  kernelFunc: all\n};", "map": {"version": 3, "names": ["All", "backend_util", "util", "reduce", "reshape", "transpose", "all", "args", "inputs", "backend", "attrs", "x", "axis", "keepDims", "xRank", "shape", "length", "origAxes", "parseAxisParam", "axes", "permutedAxes", "getAxesPermutation", "permutedX", "perm", "getInnerMostAxes", "assertAxesAreInnerMostDims", "outShape", "reduceShape", "computeOutAndReduceShapes", "inSize", "sizeFromShape", "a2D", "reduced", "dtype", "res", "newShape", "expandShapeToKeepDim", "disposeIntermediateTensorInfo", "allConfig", "kernelName", "backendName", "kernelFunc"], "sources": ["C:\\tfjs-backend-webgl\\src\\kernels\\All.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {All, AllAttrs, AllInputs, backend_util, KernelConfig, KernelFunc, TensorInfo, util} from '@tensorflow/tfjs-core';\n\nimport {MathBackendWebGL} from '../backend_webgl';\nimport {reduce} from '../kernel_utils/reduce';\n\nimport {reshape} from './Reshape';\nimport {transpose} from './Transpose';\n\nexport function all(\n    args: {inputs: AllInputs, backend: MathBackendWebGL, attrs: AllAttrs}):\n    TensorInfo {\n  const {inputs, backend, attrs} = args;\n  const {x} = inputs;\n  const {axis, keepDims} = attrs;\n\n  const xRank = x.shape.length;\n\n  const origAxes = util.parseAxisParam(axis, x.shape);\n  let axes = origAxes;\n  const permutedAxes = backend_util.getAxesPermutation(axes, xRank);\n  let permutedX = x;\n  if (permutedAxes != null) {\n    permutedX = transpose({inputs: {x}, backend, attrs: {perm: permutedAxes}});\n    axes = backend_util.getInnerMostAxes(axes.length, xRank);\n  }\n\n  backend_util.assertAxesAreInnerMostDims('all', axes, xRank);\n  const [outShape, reduceShape] =\n      backend_util.computeOutAndReduceShapes(permutedX.shape, axes);\n  const inSize = util.sizeFromShape(reduceShape);\n\n  const a2D =\n      reshape({inputs: {x: permutedX}, backend, attrs: {shape: [-1, inSize]}});\n  const reduced = reduce(a2D, a2D.dtype, 'all', backend);\n\n  let res;\n  if (keepDims) {\n    const newShape = backend_util.expandShapeToKeepDim(outShape, origAxes);\n    res = reshape({inputs: {x: reduced}, backend, attrs: {shape: newShape}});\n  } else {\n    res = reshape({inputs: {x: reduced}, backend, attrs: {shape: outShape}});\n  }\n\n  backend.disposeIntermediateTensorInfo(a2D);\n  backend.disposeIntermediateTensorInfo(reduced);\n\n  if (permutedAxes != null) {\n    backend.disposeIntermediateTensorInfo(permutedX);\n  }\n\n  return res;\n}\n\nexport const allConfig: KernelConfig = {\n  kernelName: All,\n  backendName: 'webgl',\n  kernelFunc: all as unknown as KernelFunc\n};\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAiBA,SAAQA,GAAG,EAAuBC,YAAY,EAAwCC,IAAI,QAAO,uBAAuB;AAGxH,SAAQC,MAAM,QAAO,wBAAwB;AAE7C,SAAQC,OAAO,QAAO,WAAW;AACjC,SAAQC,SAAS,QAAO,aAAa;AAErC,OAAM,SAAUC,GAAGA,CACfC,IAAqE;EAEvE,MAAM;IAACC,MAAM;IAAEC,OAAO;IAAEC;EAAK,CAAC,GAAGH,IAAI;EACrC,MAAM;IAACI;EAAC,CAAC,GAAGH,MAAM;EAClB,MAAM;IAACI,IAAI;IAAEC;EAAQ,CAAC,GAAGH,KAAK;EAE9B,MAAMI,KAAK,GAAGH,CAAC,CAACI,KAAK,CAACC,MAAM;EAE5B,MAAMC,QAAQ,GAAGf,IAAI,CAACgB,cAAc,CAACN,IAAI,EAAED,CAAC,CAACI,KAAK,CAAC;EACnD,IAAII,IAAI,GAAGF,QAAQ;EACnB,MAAMG,YAAY,GAAGnB,YAAY,CAACoB,kBAAkB,CAACF,IAAI,EAAEL,KAAK,CAAC;EACjE,IAAIQ,SAAS,GAAGX,CAAC;EACjB,IAAIS,YAAY,IAAI,IAAI,EAAE;IACxBE,SAAS,GAAGjB,SAAS,CAAC;MAACG,MAAM,EAAE;QAACG;MAAC,CAAC;MAAEF,OAAO;MAAEC,KAAK,EAAE;QAACa,IAAI,EAAEH;MAAY;IAAC,CAAC,CAAC;IAC1ED,IAAI,GAAGlB,YAAY,CAACuB,gBAAgB,CAACL,IAAI,CAACH,MAAM,EAAEF,KAAK,CAAC;;EAG1Db,YAAY,CAACwB,0BAA0B,CAAC,KAAK,EAAEN,IAAI,EAAEL,KAAK,CAAC;EAC3D,MAAM,CAACY,QAAQ,EAAEC,WAAW,CAAC,GACzB1B,YAAY,CAAC2B,yBAAyB,CAACN,SAAS,CAACP,KAAK,EAAEI,IAAI,CAAC;EACjE,MAAMU,MAAM,GAAG3B,IAAI,CAAC4B,aAAa,CAACH,WAAW,CAAC;EAE9C,MAAMI,GAAG,GACL3B,OAAO,CAAC;IAACI,MAAM,EAAE;MAACG,CAAC,EAAEW;IAAS,CAAC;IAAEb,OAAO;IAAEC,KAAK,EAAE;MAACK,KAAK,EAAE,CAAC,CAAC,CAAC,EAAEc,MAAM;IAAC;EAAC,CAAC,CAAC;EAC5E,MAAMG,OAAO,GAAG7B,MAAM,CAAC4B,GAAG,EAAEA,GAAG,CAACE,KAAK,EAAE,KAAK,EAAExB,OAAO,CAAC;EAEtD,IAAIyB,GAAG;EACP,IAAIrB,QAAQ,EAAE;IACZ,MAAMsB,QAAQ,GAAGlC,YAAY,CAACmC,oBAAoB,CAACV,QAAQ,EAAET,QAAQ,CAAC;IACtEiB,GAAG,GAAG9B,OAAO,CAAC;MAACI,MAAM,EAAE;QAACG,CAAC,EAAEqB;MAAO,CAAC;MAAEvB,OAAO;MAAEC,KAAK,EAAE;QAACK,KAAK,EAAEoB;MAAQ;IAAC,CAAC,CAAC;GACzE,MAAM;IACLD,GAAG,GAAG9B,OAAO,CAAC;MAACI,MAAM,EAAE;QAACG,CAAC,EAAEqB;MAAO,CAAC;MAAEvB,OAAO;MAAEC,KAAK,EAAE;QAACK,KAAK,EAAEW;MAAQ;IAAC,CAAC,CAAC;;EAG1EjB,OAAO,CAAC4B,6BAA6B,CAACN,GAAG,CAAC;EAC1CtB,OAAO,CAAC4B,6BAA6B,CAACL,OAAO,CAAC;EAE9C,IAAIZ,YAAY,IAAI,IAAI,EAAE;IACxBX,OAAO,CAAC4B,6BAA6B,CAACf,SAAS,CAAC;;EAGlD,OAAOY,GAAG;AACZ;AAEA,OAAO,MAAMI,SAAS,GAAiB;EACrCC,UAAU,EAAEvC,GAAG;EACfwC,WAAW,EAAE,OAAO;EACpBC,UAAU,EAAEnC;CACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}