{"ast": null, "code": "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { GreaterEqual } from '@tensorflow/tfjs-core';\nimport { binaryKernelFunc } from '../kernel_utils/kernel_funcs_utils';\nimport { greaterEqualImplCPU } from '../kernel_utils/shared';\nconst GREATER_EQUAL = `return float(a >= b);`;\nconst GREATER_EQUAL_PACKED = `\n  return vec4(greaterThanEqual(a, b));\n`;\nexport const greaterEqual = binaryKernelFunc({\n  opSnippet: GREATER_EQUAL,\n  packedOpSnippet: GREATER_EQUAL_PACKED,\n  dtype: 'bool',\n  cpuKernelImpl: greaterEqualImplCPU\n});\nexport const greaterEqualConfig = {\n  kernelName: GreaterEqual,\n  backendName: 'webgl',\n  kernelFunc: greaterEqual\n};", "map": {"version": 3, "names": ["GreaterEqual", "binaryKernelFunc", "greaterEqualImplCPU", "GREATER_EQUAL", "GREATER_EQUAL_PACKED", "greaterEqual", "opSnippet", "packedOpSnippet", "dtype", "cpuKernelImpl", "greaterEqualConfig", "kernelName", "backendName", "kernelFunc"], "sources": ["C:\\tfjs-backend-webgl\\src\\kernels\\GreaterEqual.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {GreaterEqual, KernelConfig, KernelFunc} from '@tensorflow/tfjs-core';\nimport {binaryKernelFunc} from '../kernel_utils/kernel_funcs_utils';\nimport {greaterEqualImplCPU} from '../kernel_utils/shared';\n\nconst GREATER_EQUAL = `return float(a >= b);`;\nconst GREATER_EQUAL_PACKED = `\n  return vec4(greaterThanEqual(a, b));\n`;\n\nexport const greaterEqual = binaryKernelFunc({\n  opSnippet: GREATER_EQUAL,\n  packedOpSnippet: GREATER_EQUAL_PACKED,\n  dtype: 'bool',\n  cpuKernelImpl: greaterEqualImplCPU\n});\n\nexport const greaterEqualConfig: KernelConfig = {\n  kernelName: GreaterEqual,\n  backendName: 'webgl',\n  kernelFunc: greaterEqual as unknown as KernelFunc\n};\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAiBA,SAAQA,YAAY,QAAiC,uBAAuB;AAC5E,SAAQC,gBAAgB,QAAO,oCAAoC;AACnE,SAAQC,mBAAmB,QAAO,wBAAwB;AAE1D,MAAMC,aAAa,GAAG,uBAAuB;AAC7C,MAAMC,oBAAoB,GAAG;;CAE5B;AAED,OAAO,MAAMC,YAAY,GAAGJ,gBAAgB,CAAC;EAC3CK,SAAS,EAAEH,aAAa;EACxBI,eAAe,EAAEH,oBAAoB;EACrCI,KAAK,EAAE,MAAM;EACbC,aAAa,EAAEP;CAChB,CAAC;AAEF,OAAO,MAAMQ,kBAAkB,GAAiB;EAC9CC,UAAU,EAAEX,YAAY;EACxBY,WAAW,EAAE,OAAO;EACpBC,UAAU,EAAER;CACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}