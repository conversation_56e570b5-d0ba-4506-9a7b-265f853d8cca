{"ast": null, "code": "/*\nCopyright (c) 2017, <PERSON><PERSON>\n\nPer<PERSON> is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in\nall copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\nTHE SOFTWARE.\n*/\n\n'use strict';\n\nconst Stemmer = require('./stemmer_sv');\n\n// Get R1 region\nfunction getRegions(str) {\n  const match = str.match(/[aeiouyäåö][^aeiouyäåö]([a-zåäö]+)/);\n  let r1 = '';\n  if (match && match[1]) {\n    r1 = match[1];\n    if (match.index + 2 < 3) {\n      // Not clear why we need this! Algorithm does not describe this part!\n      r1 = str.slice(3);\n    }\n  }\n  return {\n    r1,\n    rest: str.slice(0, str.length - r1.length)\n  };\n}\nfunction step1a(str, regions = getRegions(str)) {\n  const r1 = regions.r1;\n  if (!r1) {\n    return str;\n  }\n  const regEx = /(heterna|hetens|anden|andes|andet|arens|arnas|ernas|heten|heter|ornas|ande|ades|aren|arna|arne|aste|erna|erns|orna|ade|are|ast|ens|ern|het|ad|ar|as|at|en|er|es|or|a|e)$/;\n  const match = r1.match(regEx);\n  return match ? regions.rest + r1.slice(0, match.index) : str;\n}\nfunction step1b(str, regions = getRegions(str)) {\n  if (regions.r1 && str.match(/(b|c|d|f|g|h|j|k|l|m|n|o|p|r|t|v|y)s$/)) {\n    return str.slice(0, -1);\n  }\n  return str;\n}\nfunction step1(str) {\n  const regions = getRegions(str);\n  const resA = step1a(str, regions);\n  const resB = step1b(str, regions);\n  return resA.length < resB.length ? resA : resB;\n}\nfunction step2(str, regions = getRegions(str)) {\n  const r1 = regions.r1;\n  if (r1 && r1.match(/(dd|gd|nn|dt|gt|kt|tt)$/)) {\n    return str.slice(0, -1);\n  }\n  return str;\n}\nfunction step3(str, regions = getRegions(str)) {\n  const r1 = regions.r1;\n  if (r1) {\n    if (r1.match(/(lös|full)t$/)) {\n      return str.slice(0, -1);\n    }\n    const match = r1.match(/(lig|ig|els)$/);\n    return match ? regions.rest + r1.slice(0, match.index) : str;\n  }\n  return str;\n}\nfunction stem(_str) {\n  const str = _str.toLowerCase();\n  return step3(step2(step1(str)));\n}\nconst PorterStemmer = new Stemmer();\nmodule.exports = PorterStemmer;\n\n// perform full stemming algorithm on a single word\nPorterStemmer.stem = stem;", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON>", "require", "getRegions", "str", "match", "r1", "index", "slice", "rest", "length", "step1a", "regions", "regEx", "step1b", "step1", "resA", "resB", "step2", "step3", "stem", "_str", "toLowerCase", "PorterStemmer", "module", "exports"], "sources": ["C:/tmsft/node_modules/natural/lib/natural/stemmers/porter_stemmer_sv.js"], "sourcesContent": ["/*\nCopyright (c) 2017, <PERSON><PERSON>\n\nPer<PERSON> is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in\nall copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\nTHE SOFTWARE.\n*/\n\n'use strict'\n\nconst Stemmer = require('./stemmer_sv')\n\n// Get R1 region\nfunction getRegions (str) {\n  const match = str.match(/[aeiouyäåö][^aeiouyäåö]([a-zåäö]+)/)\n  let r1 = ''\n  if (match && match[1]) {\n    r1 = match[1]\n    if (match.index + 2 < 3) { // Not clear why we need this! Algorithm does not describe this part!\n      r1 = str.slice(3)\n    }\n  }\n  return {\n    r1,\n    rest: str.slice(0, str.length - r1.length)\n  }\n}\n\nfunction step1a (str, regions = getRegions(str)) {\n  const r1 = regions.r1\n  if (!r1) {\n    return str\n  }\n\n  const regEx = /(heterna|hetens|anden|andes|andet|arens|arnas|ernas|heten|heter|ornas|ande|ades|aren|arna|arne|aste|erna|erns|orna|ade|are|ast|ens|ern|het|ad|ar|as|at|en|er|es|or|a|e)$/\n  const match = r1.match(regEx)\n  return match ? regions.rest + r1.slice(0, match.index) : str\n}\n\nfunction step1b (str, regions = getRegions(str)) {\n  if (regions.r1 && str.match(/(b|c|d|f|g|h|j|k|l|m|n|o|p|r|t|v|y)s$/)) {\n    return str.slice(0, -1)\n  }\n\n  return str\n}\n\nfunction step1 (str) {\n  const regions = getRegions(str)\n  const resA = step1a(str, regions)\n  const resB = step1b(str, regions)\n\n  return resA.length < resB.length ? resA : resB\n}\n\nfunction step2 (str, regions = getRegions(str)) {\n  const r1 = regions.r1\n  if (r1 && r1.match(/(dd|gd|nn|dt|gt|kt|tt)$/)) {\n    return str.slice(0, -1)\n  }\n  return str\n}\n\nfunction step3 (str, regions = getRegions(str)) {\n  const r1 = regions.r1\n  if (r1) {\n    if (r1.match(/(lös|full)t$/)) {\n      return str.slice(0, -1)\n    }\n\n    const match = r1.match(/(lig|ig|els)$/)\n    return match ? regions.rest + r1.slice(0, match.index) : str\n  }\n\n  return str\n}\n\nfunction stem (_str) {\n  const str = _str.toLowerCase()\n  return step3(step2(step1(str)))\n}\n\nconst PorterStemmer = new Stemmer()\nmodule.exports = PorterStemmer\n\n// perform full stemming algorithm on a single word\nPorterStemmer.stem = stem\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,YAAY;;AAEZ,MAAMA,OAAO,GAAGC,OAAO,CAAC,cAAc,CAAC;;AAEvC;AACA,SAASC,UAAUA,CAAEC,GAAG,EAAE;EACxB,MAAMC,KAAK,GAAGD,GAAG,CAACC,KAAK,CAAC,oCAAoC,CAAC;EAC7D,IAAIC,EAAE,GAAG,EAAE;EACX,IAAID,KAAK,IAAIA,KAAK,CAAC,CAAC,CAAC,EAAE;IACrBC,EAAE,GAAGD,KAAK,CAAC,CAAC,CAAC;IACb,IAAIA,KAAK,CAACE,KAAK,GAAG,CAAC,GAAG,CAAC,EAAE;MAAE;MACzBD,EAAE,GAAGF,GAAG,CAACI,KAAK,CAAC,CAAC,CAAC;IACnB;EACF;EACA,OAAO;IACLF,EAAE;IACFG,IAAI,EAAEL,GAAG,CAACI,KAAK,CAAC,CAAC,EAAEJ,GAAG,CAACM,MAAM,GAAGJ,EAAE,CAACI,MAAM;EAC3C,CAAC;AACH;AAEA,SAASC,MAAMA,CAAEP,GAAG,EAAEQ,OAAO,GAAGT,UAAU,CAACC,GAAG,CAAC,EAAE;EAC/C,MAAME,EAAE,GAAGM,OAAO,CAACN,EAAE;EACrB,IAAI,CAACA,EAAE,EAAE;IACP,OAAOF,GAAG;EACZ;EAEA,MAAMS,KAAK,GAAG,0KAA0K;EACxL,MAAMR,KAAK,GAAGC,EAAE,CAACD,KAAK,CAACQ,KAAK,CAAC;EAC7B,OAAOR,KAAK,GAAGO,OAAO,CAACH,IAAI,GAAGH,EAAE,CAACE,KAAK,CAAC,CAAC,EAAEH,KAAK,CAACE,KAAK,CAAC,GAAGH,GAAG;AAC9D;AAEA,SAASU,MAAMA,CAAEV,GAAG,EAAEQ,OAAO,GAAGT,UAAU,CAACC,GAAG,CAAC,EAAE;EAC/C,IAAIQ,OAAO,CAACN,EAAE,IAAIF,GAAG,CAACC,KAAK,CAAC,uCAAuC,CAAC,EAAE;IACpE,OAAOD,GAAG,CAACI,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EACzB;EAEA,OAAOJ,GAAG;AACZ;AAEA,SAASW,KAAKA,CAAEX,GAAG,EAAE;EACnB,MAAMQ,OAAO,GAAGT,UAAU,CAACC,GAAG,CAAC;EAC/B,MAAMY,IAAI,GAAGL,MAAM,CAACP,GAAG,EAAEQ,OAAO,CAAC;EACjC,MAAMK,IAAI,GAAGH,MAAM,CAACV,GAAG,EAAEQ,OAAO,CAAC;EAEjC,OAAOI,IAAI,CAACN,MAAM,GAAGO,IAAI,CAACP,MAAM,GAAGM,IAAI,GAAGC,IAAI;AAChD;AAEA,SAASC,KAAKA,CAAEd,GAAG,EAAEQ,OAAO,GAAGT,UAAU,CAACC,GAAG,CAAC,EAAE;EAC9C,MAAME,EAAE,GAAGM,OAAO,CAACN,EAAE;EACrB,IAAIA,EAAE,IAAIA,EAAE,CAACD,KAAK,CAAC,yBAAyB,CAAC,EAAE;IAC7C,OAAOD,GAAG,CAACI,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EACzB;EACA,OAAOJ,GAAG;AACZ;AAEA,SAASe,KAAKA,CAAEf,GAAG,EAAEQ,OAAO,GAAGT,UAAU,CAACC,GAAG,CAAC,EAAE;EAC9C,MAAME,EAAE,GAAGM,OAAO,CAACN,EAAE;EACrB,IAAIA,EAAE,EAAE;IACN,IAAIA,EAAE,CAACD,KAAK,CAAC,cAAc,CAAC,EAAE;MAC5B,OAAOD,GAAG,CAACI,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACzB;IAEA,MAAMH,KAAK,GAAGC,EAAE,CAACD,KAAK,CAAC,eAAe,CAAC;IACvC,OAAOA,KAAK,GAAGO,OAAO,CAACH,IAAI,GAAGH,EAAE,CAACE,KAAK,CAAC,CAAC,EAAEH,KAAK,CAACE,KAAK,CAAC,GAAGH,GAAG;EAC9D;EAEA,OAAOA,GAAG;AACZ;AAEA,SAASgB,IAAIA,CAAEC,IAAI,EAAE;EACnB,MAAMjB,GAAG,GAAGiB,IAAI,CAACC,WAAW,CAAC,CAAC;EAC9B,OAAOH,KAAK,CAACD,KAAK,CAACH,KAAK,CAACX,GAAG,CAAC,CAAC,CAAC;AACjC;AAEA,MAAMmB,aAAa,GAAG,IAAItB,OAAO,CAAC,CAAC;AACnCuB,MAAM,CAACC,OAAO,GAAGF,aAAa;;AAE9B;AACAA,aAAa,CAACH,IAAI,GAAGA,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}