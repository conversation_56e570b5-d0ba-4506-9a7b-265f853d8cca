{"ast": null, "code": "/**\n * @license\n * Copyright 2021 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { backend_util, BroadcastArgs } from '@tensorflow/tfjs-core';\nexport function broadcastArgs(args) {\n  const {\n    inputs,\n    backend\n  } = args;\n  const {\n    s0,\n    s1\n  } = inputs;\n  const s0Vals = backend.readSync(s0.dataId);\n  const s1Vals = backend.readSync(s1.dataId);\n  const broadcastShape = backend_util.assertAndGetBroadcastShape(Array.from(s0Vals), Array.from(s1Vals));\n  return backend.makeTensorInfo([broadcastShape.length], 'int32', Int32Array.from(broadcastShape));\n}\nexport const broadcastArgsConfig = {\n  kernelName: BroadcastArgs,\n  backendName: 'webgl',\n  kernelFunc: broadcastArgs\n};", "map": {"version": 3, "names": ["backend_util", "BroadcastArgs", "broadcastArgs", "args", "inputs", "backend", "s0", "s1", "s0Vals", "readSync", "dataId", "s1Vals", "broadcastShape", "assertAndGetBroadcastShape", "Array", "from", "makeTensorInfo", "length", "Int32Array", "broadcastArgsConfig", "kernelName", "backendName", "kernelFunc"], "sources": ["C:\\tfjs-backend-webgl\\src\\kernels\\BroadcastArgs.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2021 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {backend_util, BroadcastArgs, BroadcastArgsInputs, KernelConfig, TensorInfo, TypedArray} from '@tensorflow/tfjs-core';\nimport {MathBackendWebGL} from '../backend_webgl';\n\nexport function broadcastArgs(args: {\n  inputs: BroadcastArgsInputs,\n  backend: MathBackendWebGL,\n}): TensorInfo {\n  const {inputs, backend} = args;\n  const {s0, s1} = inputs;\n\n  const s0Vals = backend.readSync(s0.dataId) as TypedArray;\n  const s1Vals = backend.readSync(s1.dataId) as TypedArray;\n\n  const broadcastShape = backend_util.assertAndGetBroadcastShape(\n      Array.from(s0Vals), Array.from(s1Vals));\n\n  return backend.makeTensorInfo(\n      [broadcastShape.length], 'int32', Int32Array.from(broadcastShape));\n}\n\nexport const broadcastArgsConfig: KernelConfig = {\n  kernelName: BroadcastArgs,\n  backendName: 'webgl',\n  kernelFunc: broadcastArgs\n};\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAiBA,SAAQA,YAAY,EAAEC,aAAa,QAAkE,uBAAuB;AAG5H,OAAM,SAAUC,aAAaA,CAACC,IAG7B;EACC,MAAM;IAACC,MAAM;IAAEC;EAAO,CAAC,GAAGF,IAAI;EAC9B,MAAM;IAACG,EAAE;IAAEC;EAAE,CAAC,GAAGH,MAAM;EAEvB,MAAMI,MAAM,GAAGH,OAAO,CAACI,QAAQ,CAACH,EAAE,CAACI,MAAM,CAAe;EACxD,MAAMC,MAAM,GAAGN,OAAO,CAACI,QAAQ,CAACF,EAAE,CAACG,MAAM,CAAe;EAExD,MAAME,cAAc,GAAGZ,YAAY,CAACa,0BAA0B,CAC1DC,KAAK,CAACC,IAAI,CAACP,MAAM,CAAC,EAAEM,KAAK,CAACC,IAAI,CAACJ,MAAM,CAAC,CAAC;EAE3C,OAAON,OAAO,CAACW,cAAc,CACzB,CAACJ,cAAc,CAACK,MAAM,CAAC,EAAE,OAAO,EAAEC,UAAU,CAACH,IAAI,CAACH,cAAc,CAAC,CAAC;AACxE;AAEA,OAAO,MAAMO,mBAAmB,GAAiB;EAC/CC,UAAU,EAAEnB,aAAa;EACzBoB,WAAW,EAAE,OAAO;EACpBC,UAAU,EAAEpB;CACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}