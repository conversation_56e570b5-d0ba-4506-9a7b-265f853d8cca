{"ast": null, "code": "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { atan2 } from '../../ops/atan2';\nimport { getGlobalTensorClass } from '../../tensor';\ngetGlobalTensorClass().prototype.atan2 = function (b) {\n  this.throwIfDisposed();\n  return atan2(this, b);\n};", "map": {"version": 3, "names": ["atan2", "getGlobalTensorClass", "prototype", "b", "throwIfDisposed"], "sources": ["C:\\tfjs-core\\src\\public\\chained_ops\\atan2.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport {atan2} from '../../ops/atan2';\nimport {getGlobalTensorClass, Tensor} from '../../tensor';\nimport {Rank, TensorLike} from '../../types';\n\ndeclare module '../../tensor' {\n  interface Tensor<R extends Rank = Rank> {\n    atan2<T extends Tensor>(b: Tensor|TensorLike): T;\n  }\n}\n\ngetGlobalTensorClass().prototype.atan2 = function<T extends Tensor>(\n    b: Tensor|TensorLike): T {\n  this.throwIfDisposed();\n  return atan2(this, b);\n};\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAgBA,SAAQA,KAAK,QAAO,iBAAiB;AACrC,SAAQC,oBAAoB,QAAe,cAAc;AASzDA,oBAAoB,EAAE,CAACC,SAAS,CAACF,KAAK,GAAG,UACrCG,CAAoB;EACtB,IAAI,CAACC,eAAe,EAAE;EACtB,OAAOJ,KAAK,CAAC,IAAI,EAAEG,CAAC,CAAC;AACvB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}