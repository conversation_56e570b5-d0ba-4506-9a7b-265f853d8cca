{"ast": null, "code": "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { backend_util, buffer, Slice, slice_util, util } from '@tensorflow/tfjs-core';\nimport { assertNotComplex } from '../cpu_util';\nexport function sliceImpl(vals, begin, size, shape, dtype) {\n  const isContinous = slice_util.isSliceContinous(shape, begin, size);\n  const length = util.sizeFromShape(size);\n  const xStrides = util.computeStrides(shape);\n  if (isContinous) {\n    const flatOffset = slice_util.computeFlatOffset(begin, xStrides);\n    if (dtype === 'string') {\n      return vals.slice(flatOffset, flatOffset + length);\n    }\n    return vals.subarray(flatOffset, flatOffset + length);\n  }\n  const decodedData = dtype === 'string' ? backend_util.fromUint8ToStringArray(vals) : vals;\n  const inBuf = buffer(shape, dtype, decodedData);\n  const outBuf = buffer(size, dtype);\n  for (let i = 0; i < outBuf.size; ++i) {\n    const outLoc = outBuf.indexToLoc(i);\n    const inLoc = outLoc.map((idx, j) => idx + begin[j]);\n    outBuf.set(inBuf.get(...inLoc), ...outLoc);\n  }\n  if (dtype === 'string') {\n    return backend_util.fromStringArrayToUint8(outBuf.values);\n  }\n  return outBuf.values;\n}\nexport function slice(args) {\n  const {\n    inputs,\n    backend,\n    attrs\n  } = args;\n  const {\n    x\n  } = inputs;\n  const {\n    begin,\n    size\n  } = attrs;\n  assertNotComplex(x, 'slice');\n  const [$begin, $size] = slice_util.parseSliceParams(x, begin, size);\n  slice_util.assertParamsValid(x, $begin, $size);\n  const vals = backend.data.get(x.dataId).values;\n  const outVals = sliceImpl(vals, $begin, $size, x.shape, x.dtype);\n  return backend.makeTensorInfo($size, x.dtype, outVals);\n}\nexport const sliceConfig = {\n  kernelName: Slice,\n  backendName: 'cpu',\n  kernelFunc: slice\n};", "map": {"version": 3, "names": ["backend_util", "buffer", "Slice", "slice_util", "util", "assertNotComplex", "sliceImpl", "vals", "begin", "size", "shape", "dtype", "isContinous", "isSliceContinous", "length", "sizeFromShape", "xStrides", "computeStrides", "flatOffset", "computeFlatOffset", "slice", "subarray", "decodedData", "fromUint8ToStringArray", "inBuf", "outBuf", "i", "outLoc", "indexToLoc", "inLoc", "map", "idx", "j", "set", "get", "fromStringArrayToUint8", "values", "args", "inputs", "backend", "attrs", "x", "$begin", "$size", "parseSliceParams", "assertP<PERSON><PERSON><PERSON><PERSON><PERSON>", "data", "dataId", "outVals", "makeTensorInfo", "sliceConfig", "kernelName", "backendName", "kernelFunc"], "sources": ["C:\\tfjs-backend-cpu\\src\\kernels\\Slice.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {backend_util, BackendValues, buffer, DataType, KernelConfig, KernelFunc, Slice, slice_util, SliceAttrs, SliceInputs, TensorInfo, TypedArray, util} from '@tensorflow/tfjs-core';\n\nimport {MathBackendCPU} from '../backend_cpu';\nimport {assertNotComplex} from '../cpu_util';\n\nexport function sliceImpl(\n    vals: BackendValues, begin: number[], size: number[], shape: number[],\n    dtype: DataType): BackendValues {\n  const isContinous = slice_util.isSliceContinous(shape, begin, size);\n  const length = util.sizeFromShape(size);\n  const xStrides = util.computeStrides(shape);\n\n  if (isContinous) {\n    const flatOffset = slice_util.computeFlatOffset(begin, xStrides);\n\n    if (dtype === 'string') {\n      return (vals as Uint8Array[]).slice(flatOffset, flatOffset + length);\n    }\n\n    return (vals as TypedArray).subarray(flatOffset, flatOffset + length);\n  }\n\n  const decodedData = dtype === 'string' ?\n      backend_util.fromUint8ToStringArray(vals as Uint8Array[]) :\n      vals as TypedArray;\n\n  const inBuf = buffer(shape, dtype, decodedData);\n  const outBuf = buffer(size, dtype);\n  for (let i = 0; i < outBuf.size; ++i) {\n    const outLoc = outBuf.indexToLoc(i);\n    const inLoc = outLoc.map((idx: number, j) => idx + begin[j]);\n    outBuf.set(inBuf.get(...inLoc), ...outLoc);\n  }\n\n  if (dtype === 'string') {\n    return backend_util.fromStringArrayToUint8(outBuf.values as string[]);\n  }\n  return outBuf.values as TypedArray;\n}\n\nexport function slice(\n    args: {inputs: SliceInputs, backend: MathBackendCPU, attrs: SliceAttrs}):\n    TensorInfo {\n  const {inputs, backend, attrs} = args;\n  const {x} = inputs;\n  const {begin, size} = attrs;\n\n  assertNotComplex(x, 'slice');\n\n  const [$begin, $size] = slice_util.parseSliceParams(x, begin, size);\n  slice_util.assertParamsValid(x, $begin, $size);\n\n  const vals = backend.data.get(x.dataId).values;\n  const outVals = sliceImpl(vals, $begin, $size, x.shape, x.dtype);\n  return backend.makeTensorInfo($size, x.dtype, outVals);\n}\n\nexport const sliceConfig: KernelConfig = {\n  kernelName: Slice,\n  backendName: 'cpu',\n  kernelFunc: slice as unknown as KernelFunc\n};\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAiBA,SAAQA,YAAY,EAAiBC,MAAM,EAAsCC,KAAK,EAAEC,UAAU,EAAmDC,IAAI,QAAO,uBAAuB;AAGvL,SAAQC,gBAAgB,QAAO,aAAa;AAE5C,OAAM,SAAUC,SAASA,CACrBC,IAAmB,EAAEC,KAAe,EAAEC,IAAc,EAAEC,KAAe,EACrEC,KAAe;EACjB,MAAMC,WAAW,GAAGT,UAAU,CAACU,gBAAgB,CAACH,KAAK,EAAEF,KAAK,EAAEC,IAAI,CAAC;EACnE,MAAMK,MAAM,GAAGV,IAAI,CAACW,aAAa,CAACN,IAAI,CAAC;EACvC,MAAMO,QAAQ,GAAGZ,IAAI,CAACa,cAAc,CAACP,KAAK,CAAC;EAE3C,IAAIE,WAAW,EAAE;IACf,MAAMM,UAAU,GAAGf,UAAU,CAACgB,iBAAiB,CAACX,KAAK,EAAEQ,QAAQ,CAAC;IAEhE,IAAIL,KAAK,KAAK,QAAQ,EAAE;MACtB,OAAQJ,IAAqB,CAACa,KAAK,CAACF,UAAU,EAAEA,UAAU,GAAGJ,MAAM,CAAC;;IAGtE,OAAQP,IAAmB,CAACc,QAAQ,CAACH,UAAU,EAAEA,UAAU,GAAGJ,MAAM,CAAC;;EAGvE,MAAMQ,WAAW,GAAGX,KAAK,KAAK,QAAQ,GAClCX,YAAY,CAACuB,sBAAsB,CAAChB,IAAoB,CAAC,GACzDA,IAAkB;EAEtB,MAAMiB,KAAK,GAAGvB,MAAM,CAACS,KAAK,EAAEC,KAAK,EAAEW,WAAW,CAAC;EAC/C,MAAMG,MAAM,GAAGxB,MAAM,CAACQ,IAAI,EAAEE,KAAK,CAAC;EAClC,KAAK,IAAIe,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,MAAM,CAAChB,IAAI,EAAE,EAAEiB,CAAC,EAAE;IACpC,MAAMC,MAAM,GAAGF,MAAM,CAACG,UAAU,CAACF,CAAC,CAAC;IACnC,MAAMG,KAAK,GAAGF,MAAM,CAACG,GAAG,CAAC,CAACC,GAAW,EAAEC,CAAC,KAAKD,GAAG,GAAGvB,KAAK,CAACwB,CAAC,CAAC,CAAC;IAC5DP,MAAM,CAACQ,GAAG,CAACT,KAAK,CAACU,GAAG,CAAC,GAAGL,KAAK,CAAC,EAAE,GAAGF,MAAM,CAAC;;EAG5C,IAAIhB,KAAK,KAAK,QAAQ,EAAE;IACtB,OAAOX,YAAY,CAACmC,sBAAsB,CAACV,MAAM,CAACW,MAAkB,CAAC;;EAEvE,OAAOX,MAAM,CAACW,MAAoB;AACpC;AAEA,OAAM,SAAUhB,KAAKA,CACjBiB,IAAuE;EAEzE,MAAM;IAACC,MAAM;IAAEC,OAAO;IAAEC;EAAK,CAAC,GAAGH,IAAI;EACrC,MAAM;IAACI;EAAC,CAAC,GAAGH,MAAM;EAClB,MAAM;IAAC9B,KAAK;IAAEC;EAAI,CAAC,GAAG+B,KAAK;EAE3BnC,gBAAgB,CAACoC,CAAC,EAAE,OAAO,CAAC;EAE5B,MAAM,CAACC,MAAM,EAAEC,KAAK,CAAC,GAAGxC,UAAU,CAACyC,gBAAgB,CAACH,CAAC,EAAEjC,KAAK,EAAEC,IAAI,CAAC;EACnEN,UAAU,CAAC0C,iBAAiB,CAACJ,CAAC,EAAEC,MAAM,EAAEC,KAAK,CAAC;EAE9C,MAAMpC,IAAI,GAAGgC,OAAO,CAACO,IAAI,CAACZ,GAAG,CAACO,CAAC,CAACM,MAAM,CAAC,CAACX,MAAM;EAC9C,MAAMY,OAAO,GAAG1C,SAAS,CAACC,IAAI,EAAEmC,MAAM,EAAEC,KAAK,EAAEF,CAAC,CAAC/B,KAAK,EAAE+B,CAAC,CAAC9B,KAAK,CAAC;EAChE,OAAO4B,OAAO,CAACU,cAAc,CAACN,KAAK,EAAEF,CAAC,CAAC9B,KAAK,EAAEqC,OAAO,CAAC;AACxD;AAEA,OAAO,MAAME,WAAW,GAAiB;EACvCC,UAAU,EAAEjD,KAAK;EACjBkD,WAAW,EAAE,KAAK;EAClBC,UAAU,EAAEjC;CACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}