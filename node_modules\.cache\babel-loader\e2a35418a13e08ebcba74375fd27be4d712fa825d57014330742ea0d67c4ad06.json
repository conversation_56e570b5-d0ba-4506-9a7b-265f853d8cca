{"ast": null, "code": "var _jsxFileName = \"C:\\\\tmsft\\\\src\\\\components\\\\BankAccountManager.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useCallback } from 'react';\nimport { bankAccountService } from '../services/bankAccountService';\nimport { balanceManagementService } from '../services/balanceManagementService';\nimport './BankAccountManager.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport const BankAccountManager = ({\n  onAccountsUpdated\n}) => {\n  _s();\n  const [accounts, setAccounts] = useState(bankAccountService.getAllAccounts());\n  const [isAddingAccount, setIsAddingAccount] = useState(false);\n  const [editingAccount, setEditingAccount] = useState(null);\n  const [adjustingBalanceAccount, setAdjustingBalanceAccount] = useState(null);\n  const [formData, setFormData] = useState({\n    name: '',\n    accountNumber: '',\n    bankName: '',\n    currency: 'USD',\n    currentBalance: '0.00'\n  });\n  const [balanceFormData, setBalanceFormData] = useState({\n    newBalance: '',\n    effectiveDate: new Date().toISOString().split('T')[0],\n    reason: ''\n  });\n  const [errors, setErrors] = useState({});\n  const [balanceErrors, setBalanceErrors] = useState({});\n  const [showBalanceHistory, setShowBalanceHistory] = useState({});\n  const refreshAccounts = useCallback(() => {\n    setAccounts(bankAccountService.getAllAccounts());\n    if (onAccountsUpdated) {\n      onAccountsUpdated();\n    }\n  }, [onAccountsUpdated]);\n  const resetForm = useCallback(() => {\n    setFormData({\n      name: '',\n      accountNumber: '',\n      bankName: '',\n      currency: 'USD',\n      currentBalance: '0.00'\n    });\n    setErrors({});\n    setIsAddingAccount(false);\n    setEditingAccount(null);\n  }, []);\n  const validateForm = useCallback(data => {\n    const newErrors = {};\n    if (!data.name.trim()) {\n      newErrors.name = 'Account name is required';\n    }\n    if (!data.accountNumber.trim()) {\n      newErrors.accountNumber = 'Account number is required';\n    } else if (accounts.some(acc => acc.accountNumber === data.accountNumber.trim() && (!editingAccount || acc.id !== editingAccount.id))) {\n      newErrors.accountNumber = 'Account number already exists';\n    }\n    if (!data.bankName.trim()) {\n      newErrors.bankName = 'Bank name is required';\n    }\n    if (!data.currency.trim()) {\n      newErrors.currency = 'Currency is required';\n    }\n    const balance = parseFloat(data.currentBalance);\n    if (isNaN(balance)) {\n      newErrors.currentBalance = 'Current balance must be a valid number';\n    }\n    return newErrors;\n  }, [accounts, editingAccount]);\n  const handleInputChange = useCallback((field, value) => {\n    setFormData(prev => ({\n      ...prev,\n      [field]: value\n    }));\n    // Clear error for this field when user starts typing\n    if (errors[field]) {\n      setErrors(prev => ({\n        ...prev,\n        [field]: ''\n      }));\n    }\n  }, [errors]);\n  const handleAddAccount = useCallback(() => {\n    setIsAddingAccount(true);\n    setEditingAccount(null);\n    resetForm();\n  }, [resetForm]);\n  const handleEditAccount = useCallback(account => {\n    setEditingAccount(account);\n    setIsAddingAccount(false);\n    setFormData({\n      name: account.name,\n      accountNumber: account.accountNumber,\n      bankName: account.bankName,\n      currency: account.currency,\n      currentBalance: account.currentBalance.toFixed(2)\n    });\n    setErrors({});\n  }, []);\n  const handleSubmit = useCallback(e => {\n    e.preventDefault();\n    const newErrors = validateForm(formData);\n    if (Object.keys(newErrors).length > 0) {\n      setErrors(newErrors);\n      return;\n    }\n    const accountData = {\n      name: formData.name.trim(),\n      accountNumber: formData.accountNumber.trim(),\n      bankName: formData.bankName.trim(),\n      currency: formData.currency.trim(),\n      currentBalance: parseFloat(formData.currentBalance)\n    };\n    try {\n      if (editingAccount) {\n        bankAccountService.updateAccount(editingAccount.id, accountData);\n      } else {\n        bankAccountService.addAccount(accountData);\n      }\n      refreshAccounts();\n      resetForm();\n    } catch (error) {\n      console.error('Error saving account:', error);\n      setErrors({\n        submit: 'Failed to save account. Please try again.'\n      });\n    }\n  }, [formData, validateForm, editingAccount, refreshAccounts, resetForm]);\n  const handleDeleteAccount = useCallback(accountId => {\n    if (window.confirm('Are you sure you want to delete this account? This action cannot be undone.')) {\n      bankAccountService.deleteAccount(accountId);\n      refreshAccounts();\n    }\n  }, [refreshAccounts]);\n\n  // Balance adjustment methods\n  const handleAdjustBalance = useCallback(account => {\n    setAdjustingBalanceAccount(account);\n    setBalanceFormData({\n      newBalance: account.currentBalance.toFixed(2),\n      effectiveDate: new Date().toISOString().split('T')[0],\n      reason: ''\n    });\n    setBalanceErrors({});\n  }, []);\n  const validateBalanceForm = useCallback(data => {\n    const newErrors = {};\n    const balance = parseFloat(data.newBalance);\n    if (isNaN(balance)) {\n      newErrors.newBalance = 'Balance must be a valid number';\n    }\n    if (!data.effectiveDate) {\n      newErrors.effectiveDate = 'Effective date is required';\n    }\n    if (!data.reason.trim()) {\n      newErrors.reason = 'Reason for adjustment is required';\n    }\n    return newErrors;\n  }, []);\n  const handleBalanceSubmit = useCallback(e => {\n    e.preventDefault();\n    if (!adjustingBalanceAccount) return;\n    const newErrors = validateBalanceForm(balanceFormData);\n    if (Object.keys(newErrors).length > 0) {\n      setBalanceErrors(newErrors);\n      return;\n    }\n    try {\n      const newBalance = parseFloat(balanceFormData.newBalance);\n      const updatedAccount = balanceManagementService.updateAccountBalance(adjustingBalanceAccount, newBalance, balanceFormData.effectiveDate, balanceFormData.reason);\n\n      // Update the account in the service\n      bankAccountService.updateAccount(updatedAccount.id, {\n        name: updatedAccount.name,\n        accountNumber: updatedAccount.accountNumber,\n        bankName: updatedAccount.bankName,\n        currency: updatedAccount.currency,\n        currentBalance: updatedAccount.currentBalance\n      });\n      refreshAccounts();\n      setAdjustingBalanceAccount(null);\n      setBalanceFormData({\n        newBalance: '',\n        effectiveDate: new Date().toISOString().split('T')[0],\n        reason: ''\n      });\n    } catch (error) {\n      console.error('Error adjusting balance:', error);\n      setBalanceErrors({\n        submit: 'Failed to adjust balance. Please try again.'\n      });\n    }\n  }, [adjustingBalanceAccount, balanceFormData, validateBalanceForm, refreshAccounts]);\n  const toggleBalanceHistory = useCallback(accountId => {\n    setShowBalanceHistory(prev => ({\n      ...prev,\n      [accountId]: !prev[accountId]\n    }));\n  }, []);\n  const formatCurrency = amount => {\n    return new Intl.NumberFormat('en-US', {\n      style: 'currency',\n      currency: 'USD'\n    }).format(amount);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"bank-account-manager\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"manager-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        children: \"Bank Account Management\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 253,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"Add, edit, and manage your bank accounts\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 254,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 252,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"accounts-section\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"section-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: [\"Your Bank Accounts (\", accounts.length, \")\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 259,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"button\",\n          onClick: handleAddAccount,\n          className: \"btn btn-primary\",\n          children: \"Add New Account\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 260,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 258,\n        columnNumber: 9\n      }, this), accounts.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"empty-state\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"empty-icon\",\n          children: /*#__PURE__*/_jsxDEV(\"svg\", {\n            width: \"48\",\n            height: \"48\",\n            viewBox: \"0 0 24 24\",\n            fill: \"none\",\n            stroke: \"currentColor\",\n            strokeWidth: \"1\",\n            children: [/*#__PURE__*/_jsxDEV(\"rect\", {\n              x: \"2\",\n              y: \"3\",\n              width: \"20\",\n              height: \"14\",\n              rx: \"2\",\n              ry: \"2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 273,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"line\", {\n              x1: \"2\",\n              y1: \"12\",\n              x2: \"22\",\n              y2: \"12\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 274,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 272,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 271,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n          children: \"No Bank Accounts\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 277,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Get started by adding your first bank account\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 278,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 270,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"accounts-grid\",\n        children: accounts.map(account => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"account-card\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"account-header\",\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              children: account.name\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 285,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"account-actions\",\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                type: \"button\",\n                onClick: () => handleAdjustBalance(account),\n                className: \"btn-icon\",\n                title: \"Adjust balance\",\n                children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                  width: \"16\",\n                  height: \"16\",\n                  viewBox: \"0 0 24 24\",\n                  fill: \"none\",\n                  stroke: \"currentColor\",\n                  strokeWidth: \"2\",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    d: \"M12 2v20M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 294,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 293,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 287,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                type: \"button\",\n                onClick: () => toggleBalanceHistory(account.id),\n                className: \"btn-icon\",\n                title: \"View balance history\",\n                children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                  width: \"16\",\n                  height: \"16\",\n                  viewBox: \"0 0 24 24\",\n                  fill: \"none\",\n                  stroke: \"currentColor\",\n                  strokeWidth: \"2\",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    d: \"M3 3v5h5M3 8l4-4 4 4 8-8\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 304,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 303,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 297,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                type: \"button\",\n                onClick: () => handleEditAccount(account),\n                className: \"btn-icon\",\n                title: \"Edit account\",\n                children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                  width: \"16\",\n                  height: \"16\",\n                  viewBox: \"0 0 24 24\",\n                  fill: \"none\",\n                  stroke: \"currentColor\",\n                  strokeWidth: \"2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                    d: \"M12 20h9\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 314,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                    d: \"M16.5 3.5a2.121 2.121 0 0 1 3 3L7 19l-4 1 1-4L16.5 3.5z\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 315,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 313,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 307,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                type: \"button\",\n                onClick: () => handleDeleteAccount(account.id),\n                className: \"btn-icon btn-danger\",\n                title: \"Delete account\",\n                children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                  width: \"16\",\n                  height: \"16\",\n                  viewBox: \"0 0 24 24\",\n                  fill: \"none\",\n                  stroke: \"currentColor\",\n                  strokeWidth: \"2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"polyline\", {\n                    points: \"3,6 5,6 21,6\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 325,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                    d: \"M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 326,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 324,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 318,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 286,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 284,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"account-details\",\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Bank:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 332,\n                columnNumber: 22\n              }, this), \" \", account.bankName]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 332,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Account Number:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 333,\n                columnNumber: 22\n              }, this), \" \", account.accountNumber]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 333,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Currency:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 334,\n                columnNumber: 22\n              }, this), \" \", account.currency]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 334,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Current Balance:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 335,\n                columnNumber: 22\n              }, this), \" \", /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"balance\",\n                children: formatCurrency(account.currentBalance)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 335,\n                columnNumber: 56\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 335,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 331,\n            columnNumber: 17\n          }, this)]\n        }, account.id, true, {\n          fileName: _jsxFileName,\n          lineNumber: 283,\n          columnNumber: 15\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 281,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 257,\n      columnNumber: 7\n    }, this), (isAddingAccount || editingAccount) && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"account-form-section\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-header\",\n        children: /*#__PURE__*/_jsxDEV(\"h3\", {\n          children: editingAccount ? 'Edit Account' : 'Add New Account'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 346,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 345,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n        onSubmit: handleSubmit,\n        className: \"account-form\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-row\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"account-name\",\n              className: \"form-label\",\n              children: \"Account Name *\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 352,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              id: \"account-name\",\n              type: \"text\",\n              value: formData.name,\n              onChange: e => handleInputChange('name', e.target.value),\n              className: `form-input ${errors.name ? 'error' : ''}`,\n              placeholder: \"e.g., Main Operating Account\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 355,\n              columnNumber: 17\n            }, this), errors.name && /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"form-error\",\n              children: errors.name\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 363,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 351,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"account-number\",\n              className: \"form-label\",\n              children: \"Account Number *\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 367,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              id: \"account-number\",\n              type: \"text\",\n              value: formData.accountNumber,\n              onChange: e => handleInputChange('accountNumber', e.target.value),\n              className: `form-input ${errors.accountNumber ? 'error' : ''}`,\n              placeholder: \"e.g., **********\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 370,\n              columnNumber: 17\n            }, this), errors.accountNumber && /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"form-error\",\n              children: errors.accountNumber\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 378,\n              columnNumber: 42\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 366,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 350,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-row\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"bank-name\",\n              className: \"form-label\",\n              children: \"Bank Name *\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 384,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              id: \"bank-name\",\n              type: \"text\",\n              value: formData.bankName,\n              onChange: e => handleInputChange('bankName', e.target.value),\n              className: `form-input ${errors.bankName ? 'error' : ''}`,\n              placeholder: \"e.g., First National Bank\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 387,\n              columnNumber: 17\n            }, this), errors.bankName && /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"form-error\",\n              children: errors.bankName\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 395,\n              columnNumber: 37\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 383,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"currency\",\n              className: \"form-label\",\n              children: \"Currency *\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 399,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n              id: \"currency\",\n              value: formData.currency,\n              onChange: e => handleInputChange('currency', e.target.value),\n              className: `form-select ${errors.currency ? 'error' : ''}`,\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"USD\",\n                children: \"USD - US Dollar\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 408,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"EUR\",\n                children: \"EUR - Euro\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 409,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"GBP\",\n                children: \"GBP - British Pound\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 410,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"CAD\",\n                children: \"CAD - Canadian Dollar\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 411,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"AUD\",\n                children: \"AUD - Australian Dollar\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 412,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 402,\n              columnNumber: 17\n            }, this), errors.currency && /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"form-error\",\n              children: errors.currency\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 414,\n              columnNumber: 37\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 398,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 382,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-row\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"current-balance\",\n              className: \"form-label\",\n              children: \"Current Balance *\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 420,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              id: \"current-balance\",\n              type: \"number\",\n              step: \"0.01\",\n              value: formData.currentBalance,\n              onChange: e => handleInputChange('currentBalance', e.target.value),\n              className: `form-input ${errors.currentBalance ? 'error' : ''}`,\n              placeholder: \"0.00\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 423,\n              columnNumber: 17\n            }, this), errors.currentBalance && /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"form-error\",\n              children: errors.currentBalance\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 432,\n              columnNumber: 43\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 419,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 418,\n          columnNumber: 13\n        }, this), errors.submit && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-error-message\",\n          children: errors.submit\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 437,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-actions\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"button\",\n            onClick: resetForm,\n            className: \"btn btn-secondary\",\n            children: \"Cancel\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 443,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"submit\",\n            className: \"btn btn-primary\",\n            children: editingAccount ? 'Update Account' : 'Add Account'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 450,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 442,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 349,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 344,\n      columnNumber: 9\n    }, this), adjustingBalanceAccount && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"balance-adjustment-section\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: [\"Adjust Balance - \", adjustingBalanceAccount.name]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 465,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Update the account balance with an effective date and reason\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 466,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 464,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n        onSubmit: handleBalanceSubmit,\n        className: \"balance-form\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-row\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"current-balance-display\",\n              className: \"form-label\",\n              children: \"Current Balance\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 472,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              id: \"current-balance-display\",\n              type: \"text\",\n              value: formatCurrency(adjustingBalanceAccount.currentBalance),\n              disabled: true,\n              className: \"form-input disabled\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 475,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 471,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"new-balance\",\n              className: \"form-label\",\n              children: \"New Balance *\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 485,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              id: \"new-balance\",\n              type: \"number\",\n              step: \"0.01\",\n              value: balanceFormData.newBalance,\n              onChange: e => setBalanceFormData(prev => ({\n                ...prev,\n                newBalance: e.target.value\n              })),\n              className: `form-input ${balanceErrors.newBalance ? 'error' : ''}`,\n              placeholder: \"0.00\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 488,\n              columnNumber: 17\n            }, this), balanceErrors.newBalance && /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"form-error\",\n              children: balanceErrors.newBalance\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 497,\n              columnNumber: 46\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 484,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 470,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-row\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"effective-date\",\n              className: \"form-label\",\n              children: \"Effective Date *\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 503,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              id: \"effective-date\",\n              type: \"date\",\n              value: balanceFormData.effectiveDate,\n              onChange: e => setBalanceFormData(prev => ({\n                ...prev,\n                effectiveDate: e.target.value\n              })),\n              className: `form-input ${balanceErrors.effectiveDate ? 'error' : ''}`\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 506,\n              columnNumber: 17\n            }, this), balanceErrors.effectiveDate && /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"form-error\",\n              children: balanceErrors.effectiveDate\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 513,\n              columnNumber: 49\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 502,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"adjustment-reason\",\n              className: \"form-label\",\n              children: \"Reason for Adjustment *\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 517,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n              id: \"adjustment-reason\",\n              value: balanceFormData.reason,\n              onChange: e => setBalanceFormData(prev => ({\n                ...prev,\n                reason: e.target.value\n              })),\n              className: `form-select ${balanceErrors.reason ? 'error' : ''}`,\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"\",\n                children: \"Select a reason...\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 526,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"Bank reconciliation\",\n                children: \"Bank reconciliation\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 527,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"Manual correction\",\n                children: \"Manual correction\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 528,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"Interest adjustment\",\n                children: \"Interest adjustment\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 529,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"Fee adjustment\",\n                children: \"Fee adjustment\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 530,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"Opening balance setup\",\n                children: \"Opening balance setup\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 531,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"Other\",\n                children: \"Other\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 532,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 520,\n              columnNumber: 17\n            }, this), balanceErrors.reason && /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"form-error\",\n              children: balanceErrors.reason\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 534,\n              columnNumber: 42\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 516,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 501,\n          columnNumber: 13\n        }, this), balanceFormData.reason === 'Other' && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-row\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"custom-reason\",\n              className: \"form-label\",\n              children: \"Custom Reason *\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 541,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              id: \"custom-reason\",\n              type: \"text\",\n              placeholder: \"Enter custom reason...\",\n              className: \"form-input\",\n              onChange: e => setBalanceFormData(prev => ({\n                ...prev,\n                reason: e.target.value\n              }))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 544,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 540,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 539,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"adjustment-preview\",\n          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n            children: \"Adjustment Preview\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 556,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"preview-details\",\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Current Balance:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 558,\n                columnNumber: 20\n              }, this), \" \", formatCurrency(adjustingBalanceAccount.currentBalance)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 558,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"New Balance:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 559,\n                columnNumber: 20\n              }, this), \" \", balanceFormData.newBalance ? formatCurrency(parseFloat(balanceFormData.newBalance)) : '$0.00']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 559,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Adjustment Amount:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 560,\n                columnNumber: 20\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: `adjustment-amount ${balanceFormData.newBalance && parseFloat(balanceFormData.newBalance) - adjustingBalanceAccount.currentBalance >= 0 ? 'positive' : 'negative'}`,\n                children: balanceFormData.newBalance ? (parseFloat(balanceFormData.newBalance) - adjustingBalanceAccount.currentBalance >= 0 ? '+' : '') + formatCurrency(parseFloat(balanceFormData.newBalance) - adjustingBalanceAccount.currentBalance) : '$0.00'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 561,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 560,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 557,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 555,\n          columnNumber: 13\n        }, this), balanceErrors.submit && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-error-message\",\n          children: balanceErrors.submit\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 573,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-actions\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"button\",\n            onClick: () => setAdjustingBalanceAccount(null),\n            className: \"btn btn-secondary\",\n            children: \"Cancel\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 579,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"submit\",\n            className: \"btn btn-primary\",\n            children: \"Apply Adjustment\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 586,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 578,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 469,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 463,\n      columnNumber: 9\n    }, this), accounts.map(account => showBalanceHistory[account.id] && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"balance-history-section\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: [\"Balance History - \", account.name]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 602,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"button\",\n          onClick: () => toggleBalanceHistory(account.id),\n          className: \"btn btn-secondary\",\n          children: \"Close\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 603,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 601,\n        columnNumber: 13\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"balance-history\",\n        children: (() => {\n          const history = balanceManagementService.getBalanceHistory(account.id);\n          const adjustments = balanceManagementService.getBalanceAdjustments(account.id);\n          if (history.length === 0 && adjustments.length === 0) {\n            return /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"empty-history\",\n              children: /*#__PURE__*/_jsxDEV(\"p\", {\n                children: \"No balance history available for this account.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 620,\n                columnNumber: 23\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 619,\n              columnNumber: 21\n            }, this);\n          }\n          return /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"history-content\",\n            children: adjustments.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"adjustments-section\",\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                children: \"Balance Adjustments\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 629,\n                columnNumber: 25\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"adjustments-list\",\n                children: adjustments.map(adjustment => /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"adjustment-item\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"adjustment-header\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"adjustment-date\",\n                      children: adjustment.date\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 634,\n                      columnNumber: 33\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: `adjustment-amount ${adjustment.adjustmentAmount >= 0 ? 'positive' : 'negative'}`,\n                      children: [adjustment.adjustmentAmount >= 0 ? '+' : '', formatCurrency(adjustment.adjustmentAmount)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 635,\n                      columnNumber: 33\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 633,\n                    columnNumber: 31\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"adjustment-details\",\n                    children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                        children: \"Reason:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 640,\n                        columnNumber: 36\n                      }, this), \" \", adjustment.reason]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 640,\n                      columnNumber: 33\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                        children: \"Previous Balance:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 641,\n                        columnNumber: 36\n                      }, this), \" \", formatCurrency(adjustment.previousBalance)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 641,\n                      columnNumber: 33\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                        children: \"New Balance:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 642,\n                        columnNumber: 36\n                      }, this), \" \", formatCurrency(adjustment.newBalance)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 642,\n                      columnNumber: 33\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                        children: \"Type:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 643,\n                        columnNumber: 36\n                      }, this), \" \", adjustment.type]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 643,\n                      columnNumber: 33\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 639,\n                    columnNumber: 31\n                  }, this)]\n                }, adjustment.id, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 632,\n                  columnNumber: 29\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 630,\n                columnNumber: 25\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 628,\n              columnNumber: 23\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 626,\n            columnNumber: 19\n          }, this);\n        })()\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 612,\n        columnNumber: 13\n      }, this)]\n    }, `history-${account.id}`, true, {\n      fileName: _jsxFileName,\n      lineNumber: 600,\n      columnNumber: 11\n    }, this))]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 251,\n    columnNumber: 5\n  }, this);\n};\n_s(BankAccountManager, \"CNyKFFGVyXp8i0fAUluPTv0eDYI=\");\n_c = BankAccountManager;\nvar _c;\n$RefreshReg$(_c, \"BankAccountManager\");", "map": {"version": 3, "names": ["React", "useState", "useCallback", "bankAccountService", "balanceManagementService", "jsxDEV", "_jsxDEV", "BankAccountManager", "onAccountsUpdated", "_s", "accounts", "setAccounts", "getAllAccounts", "isAddingAccount", "setIsAddingAccount", "editingAccount", "setEditingAccount", "adjustingBalanceAccount", "setAdjustingBalanceAccount", "formData", "setFormData", "name", "accountNumber", "bankName", "currency", "currentBalance", "balanceFormData", "setBalanceFormData", "newBalance", "effectiveDate", "Date", "toISOString", "split", "reason", "errors", "setErrors", "balanceErrors", "setBalanceErrors", "showBalanceHistory", "setShowBalanceHistory", "refreshAccounts", "resetForm", "validateForm", "data", "newErrors", "trim", "some", "acc", "id", "balance", "parseFloat", "isNaN", "handleInputChange", "field", "value", "prev", "handleAddAccount", "handleEditAccount", "account", "toFixed", "handleSubmit", "e", "preventDefault", "Object", "keys", "length", "accountData", "updateAccount", "addAccount", "error", "console", "submit", "handleDeleteAccount", "accountId", "window", "confirm", "deleteAccount", "handleAdjustBalance", "validateBalanceForm", "handleBalanceSubmit", "updatedAccount", "updateAccountBalance", "toggleBalanceHistory", "formatCurrency", "amount", "Intl", "NumberFormat", "style", "format", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "type", "onClick", "width", "height", "viewBox", "fill", "stroke", "strokeWidth", "x", "y", "rx", "ry", "x1", "y1", "x2", "y2", "map", "title", "d", "points", "onSubmit", "htmlFor", "onChange", "target", "placeholder", "step", "disabled", "history", "getBalanceHistory", "adjustments", "getBalanceAdjustments", "adjustment", "date", "adjustmentAmount", "previousBalance", "_c", "$RefreshReg$"], "sources": ["C:/tmsft/src/components/BankAccountManager.tsx"], "sourcesContent": ["import React, { useState, useCallback } from 'react';\r\nimport { BankAccount } from '../types';\r\nimport { bankAccountService } from '../services/bankAccountService';\r\nimport { balanceManagementService, BalanceAdjustment } from '../services/balanceManagementService';\r\nimport './BankAccountManager.css';\r\n\r\ninterface BankAccountManagerProps {\r\n  onAccountsUpdated?: () => void;\r\n}\r\n\r\ninterface AccountFormData {\r\n  name: string;\r\n  accountNumber: string;\r\n  bankName: string;\r\n  currency: string;\r\n  currentBalance: string;\r\n}\r\n\r\ninterface BalanceAdjustmentFormData {\r\n  newBalance: string;\r\n  effectiveDate: string;\r\n  reason: string;\r\n}\r\n\r\nexport const BankAccountManager: React.FC<BankAccountManagerProps> = ({ onAccountsUpdated }) => {\r\n  const [accounts, setAccounts] = useState<BankAccount[]>(bankAccountService.getAllAccounts());\r\n  const [isAddingAccount, setIsAddingAccount] = useState(false);\r\n  const [editingAccount, setEditingAccount] = useState<BankAccount | null>(null);\r\n  const [adjustingBalanceAccount, setAdjustingBalanceAccount] = useState<BankAccount | null>(null);\r\n  const [formData, setFormData] = useState<AccountFormData>({\r\n    name: '',\r\n    accountNumber: '',\r\n    bankName: '',\r\n    currency: 'USD',\r\n    currentBalance: '0.00'\r\n  });\r\n  const [balanceFormData, setBalanceFormData] = useState<BalanceAdjustmentFormData>({\r\n    newBalance: '',\r\n    effectiveDate: new Date().toISOString().split('T')[0],\r\n    reason: ''\r\n  });\r\n  const [errors, setErrors] = useState<Record<string, string>>({});\r\n  const [balanceErrors, setBalanceErrors] = useState<Record<string, string>>({});\r\n  const [showBalanceHistory, setShowBalanceHistory] = useState<Record<string, boolean>>({});\r\n\r\n  const refreshAccounts = useCallback(() => {\r\n    setAccounts(bankAccountService.getAllAccounts());\r\n    if (onAccountsUpdated) {\r\n      onAccountsUpdated();\r\n    }\r\n  }, [onAccountsUpdated]);\r\n\r\n  const resetForm = useCallback(() => {\r\n    setFormData({\r\n      name: '',\r\n      accountNumber: '',\r\n      bankName: '',\r\n      currency: 'USD',\r\n      currentBalance: '0.00'\r\n    });\r\n    setErrors({});\r\n    setIsAddingAccount(false);\r\n    setEditingAccount(null);\r\n  }, []);\r\n\r\n  const validateForm = useCallback((data: AccountFormData): Record<string, string> => {\r\n    const newErrors: Record<string, string> = {};\r\n\r\n    if (!data.name.trim()) {\r\n      newErrors.name = 'Account name is required';\r\n    }\r\n\r\n    if (!data.accountNumber.trim()) {\r\n      newErrors.accountNumber = 'Account number is required';\r\n    } else if (accounts.some(acc => \r\n      acc.accountNumber === data.accountNumber.trim() && \r\n      (!editingAccount || acc.id !== editingAccount.id)\r\n    )) {\r\n      newErrors.accountNumber = 'Account number already exists';\r\n    }\r\n\r\n    if (!data.bankName.trim()) {\r\n      newErrors.bankName = 'Bank name is required';\r\n    }\r\n\r\n    if (!data.currency.trim()) {\r\n      newErrors.currency = 'Currency is required';\r\n    }\r\n\r\n    const balance = parseFloat(data.currentBalance);\r\n    if (isNaN(balance)) {\r\n      newErrors.currentBalance = 'Current balance must be a valid number';\r\n    }\r\n\r\n    return newErrors;\r\n  }, [accounts, editingAccount]);\r\n\r\n  const handleInputChange = useCallback((field: keyof AccountFormData, value: string) => {\r\n    setFormData(prev => ({ ...prev, [field]: value }));\r\n    // Clear error for this field when user starts typing\r\n    if (errors[field]) {\r\n      setErrors(prev => ({ ...prev, [field]: '' }));\r\n    }\r\n  }, [errors]);\r\n\r\n  const handleAddAccount = useCallback(() => {\r\n    setIsAddingAccount(true);\r\n    setEditingAccount(null);\r\n    resetForm();\r\n  }, [resetForm]);\r\n\r\n  const handleEditAccount = useCallback((account: BankAccount) => {\r\n    setEditingAccount(account);\r\n    setIsAddingAccount(false);\r\n    setFormData({\r\n      name: account.name,\r\n      accountNumber: account.accountNumber,\r\n      bankName: account.bankName,\r\n      currency: account.currency,\r\n      currentBalance: account.currentBalance.toFixed(2)\r\n    });\r\n    setErrors({});\r\n  }, []);\r\n\r\n  const handleSubmit = useCallback((e: React.FormEvent) => {\r\n    e.preventDefault();\r\n    \r\n    const newErrors = validateForm(formData);\r\n    if (Object.keys(newErrors).length > 0) {\r\n      setErrors(newErrors);\r\n      return;\r\n    }\r\n\r\n    const accountData = {\r\n      name: formData.name.trim(),\r\n      accountNumber: formData.accountNumber.trim(),\r\n      bankName: formData.bankName.trim(),\r\n      currency: formData.currency.trim(),\r\n      currentBalance: parseFloat(formData.currentBalance)\r\n    };\r\n\r\n    try {\r\n      if (editingAccount) {\r\n        bankAccountService.updateAccount(editingAccount.id, accountData);\r\n      } else {\r\n        bankAccountService.addAccount(accountData);\r\n      }\r\n      \r\n      refreshAccounts();\r\n      resetForm();\r\n    } catch (error) {\r\n      console.error('Error saving account:', error);\r\n      setErrors({ submit: 'Failed to save account. Please try again.' });\r\n    }\r\n  }, [formData, validateForm, editingAccount, refreshAccounts, resetForm]);\r\n\r\n  const handleDeleteAccount = useCallback((accountId: string) => {\r\n    if (window.confirm('Are you sure you want to delete this account? This action cannot be undone.')) {\r\n      bankAccountService.deleteAccount(accountId);\r\n      refreshAccounts();\r\n    }\r\n  }, [refreshAccounts]);\r\n\r\n  // Balance adjustment methods\r\n  const handleAdjustBalance = useCallback((account: BankAccount) => {\r\n    setAdjustingBalanceAccount(account);\r\n    setBalanceFormData({\r\n      newBalance: account.currentBalance.toFixed(2),\r\n      effectiveDate: new Date().toISOString().split('T')[0],\r\n      reason: ''\r\n    });\r\n    setBalanceErrors({});\r\n  }, []);\r\n\r\n  const validateBalanceForm = useCallback((data: BalanceAdjustmentFormData): Record<string, string> => {\r\n    const newErrors: Record<string, string> = {};\r\n\r\n    const balance = parseFloat(data.newBalance);\r\n    if (isNaN(balance)) {\r\n      newErrors.newBalance = 'Balance must be a valid number';\r\n    }\r\n\r\n    if (!data.effectiveDate) {\r\n      newErrors.effectiveDate = 'Effective date is required';\r\n    }\r\n\r\n    if (!data.reason.trim()) {\r\n      newErrors.reason = 'Reason for adjustment is required';\r\n    }\r\n\r\n    return newErrors;\r\n  }, []);\r\n\r\n  const handleBalanceSubmit = useCallback((e: React.FormEvent) => {\r\n    e.preventDefault();\r\n    \r\n    if (!adjustingBalanceAccount) return;\r\n\r\n    const newErrors = validateBalanceForm(balanceFormData);\r\n    if (Object.keys(newErrors).length > 0) {\r\n      setBalanceErrors(newErrors);\r\n      return;\r\n    }\r\n\r\n    try {\r\n      const newBalance = parseFloat(balanceFormData.newBalance);\r\n      const updatedAccount = balanceManagementService.updateAccountBalance(\r\n        adjustingBalanceAccount,\r\n        newBalance,\r\n        balanceFormData.effectiveDate,\r\n        balanceFormData.reason\r\n      );\r\n\r\n      // Update the account in the service\r\n      bankAccountService.updateAccount(updatedAccount.id, {\r\n        name: updatedAccount.name,\r\n        accountNumber: updatedAccount.accountNumber,\r\n        bankName: updatedAccount.bankName,\r\n        currency: updatedAccount.currency,\r\n        currentBalance: updatedAccount.currentBalance\r\n      });\r\n\r\n      refreshAccounts();\r\n      setAdjustingBalanceAccount(null);\r\n      setBalanceFormData({\r\n        newBalance: '',\r\n        effectiveDate: new Date().toISOString().split('T')[0],\r\n        reason: ''\r\n      });\r\n    } catch (error) {\r\n      console.error('Error adjusting balance:', error);\r\n      setBalanceErrors({ submit: 'Failed to adjust balance. Please try again.' });\r\n    }\r\n  }, [adjustingBalanceAccount, balanceFormData, validateBalanceForm, refreshAccounts]);\r\n\r\n  const toggleBalanceHistory = useCallback((accountId: string) => {\r\n    setShowBalanceHistory(prev => ({\r\n      ...prev,\r\n      [accountId]: !prev[accountId]\r\n    }));\r\n  }, []);\r\n\r\n  const formatCurrency = (amount: number): string => {\r\n    return new Intl.NumberFormat('en-US', {\r\n      style: 'currency',\r\n      currency: 'USD'\r\n    }).format(amount);\r\n  };\r\n\r\n  return (\r\n    <div className=\"bank-account-manager\">\r\n      <div className=\"manager-header\">\r\n        <h2>Bank Account Management</h2>\r\n        <p>Add, edit, and manage your bank accounts</p>\r\n      </div>\r\n\r\n      <div className=\"accounts-section\">\r\n        <div className=\"section-header\">\r\n          <h3>Your Bank Accounts ({accounts.length})</h3>\r\n          <button\r\n            type=\"button\"\r\n            onClick={handleAddAccount}\r\n            className=\"btn btn-primary\"\r\n          >\r\n            Add New Account\r\n          </button>\r\n        </div>\r\n\r\n        {accounts.length === 0 ? (\r\n          <div className=\"empty-state\">\r\n            <div className=\"empty-icon\">\r\n              <svg width=\"48\" height=\"48\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"1\">\r\n                <rect x=\"2\" y=\"3\" width=\"20\" height=\"14\" rx=\"2\" ry=\"2\" />\r\n                <line x1=\"2\" y1=\"12\" x2=\"22\" y2=\"12\" />\r\n              </svg>\r\n            </div>\r\n            <h3>No Bank Accounts</h3>\r\n            <p>Get started by adding your first bank account</p>\r\n          </div>\r\n        ) : (\r\n          <div className=\"accounts-grid\">\r\n            {accounts.map((account) => (\r\n              <div key={account.id} className=\"account-card\">\r\n                <div className=\"account-header\">\r\n                  <h4>{account.name}</h4>\r\n                  <div className=\"account-actions\">\r\n                    <button\r\n                      type=\"button\"\r\n                      onClick={() => handleAdjustBalance(account)}\r\n                      className=\"btn-icon\"\r\n                      title=\"Adjust balance\"\r\n                    >\r\n                      <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\">\r\n                        <path d=\"M12 2v20M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6\" />\r\n                      </svg>\r\n                    </button>\r\n                    <button\r\n                      type=\"button\"\r\n                      onClick={() => toggleBalanceHistory(account.id)}\r\n                      className=\"btn-icon\"\r\n                      title=\"View balance history\"\r\n                    >\r\n                      <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\">\r\n                        <path d=\"M3 3v5h5M3 8l4-4 4 4 8-8\" />\r\n                      </svg>\r\n                    </button>\r\n                    <button\r\n                      type=\"button\"\r\n                      onClick={() => handleEditAccount(account)}\r\n                      className=\"btn-icon\"\r\n                      title=\"Edit account\"\r\n                    >\r\n                      <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\">\r\n                        <path d=\"M12 20h9\" />\r\n                        <path d=\"M16.5 3.5a2.121 2.121 0 0 1 3 3L7 19l-4 1 1-4L16.5 3.5z\" />\r\n                      </svg>\r\n                    </button>\r\n                    <button\r\n                      type=\"button\"\r\n                      onClick={() => handleDeleteAccount(account.id)}\r\n                      className=\"btn-icon btn-danger\"\r\n                      title=\"Delete account\"\r\n                    >\r\n                      <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\">\r\n                        <polyline points=\"3,6 5,6 21,6\" />\r\n                        <path d=\"M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2\" />\r\n                      </svg>\r\n                    </button>\r\n                  </div>\r\n                </div>\r\n                <div className=\"account-details\">\r\n                  <p><strong>Bank:</strong> {account.bankName}</p>\r\n                  <p><strong>Account Number:</strong> {account.accountNumber}</p>\r\n                  <p><strong>Currency:</strong> {account.currency}</p>\r\n                  <p><strong>Current Balance:</strong> <span className=\"balance\">{formatCurrency(account.currentBalance)}</span></p>\r\n                </div>\r\n              </div>\r\n            ))}\r\n          </div>\r\n        )}\r\n      </div>\r\n\r\n      {(isAddingAccount || editingAccount) && (\r\n        <div className=\"account-form-section\">\r\n          <div className=\"form-header\">\r\n            <h3>{editingAccount ? 'Edit Account' : 'Add New Account'}</h3>\r\n          </div>\r\n          \r\n          <form onSubmit={handleSubmit} className=\"account-form\">\r\n            <div className=\"form-row\">\r\n              <div className=\"form-group\">\r\n                <label htmlFor=\"account-name\" className=\"form-label\">\r\n                  Account Name *\r\n                </label>\r\n                <input\r\n                  id=\"account-name\"\r\n                  type=\"text\"\r\n                  value={formData.name}\r\n                  onChange={(e) => handleInputChange('name', e.target.value)}\r\n                  className={`form-input ${errors.name ? 'error' : ''}`}\r\n                  placeholder=\"e.g., Main Operating Account\"\r\n                />\r\n                {errors.name && <span className=\"form-error\">{errors.name}</span>}\r\n              </div>\r\n\r\n              <div className=\"form-group\">\r\n                <label htmlFor=\"account-number\" className=\"form-label\">\r\n                  Account Number *\r\n                </label>\r\n                <input\r\n                  id=\"account-number\"\r\n                  type=\"text\"\r\n                  value={formData.accountNumber}\r\n                  onChange={(e) => handleInputChange('accountNumber', e.target.value)}\r\n                  className={`form-input ${errors.accountNumber ? 'error' : ''}`}\r\n                  placeholder=\"e.g., **********\"\r\n                />\r\n                {errors.accountNumber && <span className=\"form-error\">{errors.accountNumber}</span>}\r\n              </div>\r\n            </div>\r\n\r\n            <div className=\"form-row\">\r\n              <div className=\"form-group\">\r\n                <label htmlFor=\"bank-name\" className=\"form-label\">\r\n                  Bank Name *\r\n                </label>\r\n                <input\r\n                  id=\"bank-name\"\r\n                  type=\"text\"\r\n                  value={formData.bankName}\r\n                  onChange={(e) => handleInputChange('bankName', e.target.value)}\r\n                  className={`form-input ${errors.bankName ? 'error' : ''}`}\r\n                  placeholder=\"e.g., First National Bank\"\r\n                />\r\n                {errors.bankName && <span className=\"form-error\">{errors.bankName}</span>}\r\n              </div>\r\n\r\n              <div className=\"form-group\">\r\n                <label htmlFor=\"currency\" className=\"form-label\">\r\n                  Currency *\r\n                </label>\r\n                <select\r\n                  id=\"currency\"\r\n                  value={formData.currency}\r\n                  onChange={(e) => handleInputChange('currency', e.target.value)}\r\n                  className={`form-select ${errors.currency ? 'error' : ''}`}\r\n                >\r\n                  <option value=\"USD\">USD - US Dollar</option>\r\n                  <option value=\"EUR\">EUR - Euro</option>\r\n                  <option value=\"GBP\">GBP - British Pound</option>\r\n                  <option value=\"CAD\">CAD - Canadian Dollar</option>\r\n                  <option value=\"AUD\">AUD - Australian Dollar</option>\r\n                </select>\r\n                {errors.currency && <span className=\"form-error\">{errors.currency}</span>}\r\n              </div>\r\n            </div>\r\n\r\n            <div className=\"form-row\">\r\n              <div className=\"form-group\">\r\n                <label htmlFor=\"current-balance\" className=\"form-label\">\r\n                  Current Balance *\r\n                </label>\r\n                <input\r\n                  id=\"current-balance\"\r\n                  type=\"number\"\r\n                  step=\"0.01\"\r\n                  value={formData.currentBalance}\r\n                  onChange={(e) => handleInputChange('currentBalance', e.target.value)}\r\n                  className={`form-input ${errors.currentBalance ? 'error' : ''}`}\r\n                  placeholder=\"0.00\"\r\n                />\r\n                {errors.currentBalance && <span className=\"form-error\">{errors.currentBalance}</span>}\r\n              </div>\r\n            </div>\r\n\r\n            {errors.submit && (\r\n              <div className=\"form-error-message\">\r\n                {errors.submit}\r\n              </div>\r\n            )}\r\n\r\n            <div className=\"form-actions\">\r\n              <button\r\n                type=\"button\"\r\n                onClick={resetForm}\r\n                className=\"btn btn-secondary\"\r\n              >\r\n                Cancel\r\n              </button>\r\n              <button\r\n                type=\"submit\"\r\n                className=\"btn btn-primary\"\r\n              >\r\n                {editingAccount ? 'Update Account' : 'Add Account'}\r\n              </button>\r\n            </div>\r\n          </form>\r\n        </div>\r\n      )}\r\n\r\n      {/* Balance Adjustment Form */}\r\n      {adjustingBalanceAccount && (\r\n        <div className=\"balance-adjustment-section\">\r\n          <div className=\"form-header\">\r\n            <h3>Adjust Balance - {adjustingBalanceAccount.name}</h3>\r\n            <p>Update the account balance with an effective date and reason</p>\r\n          </div>\r\n          \r\n          <form onSubmit={handleBalanceSubmit} className=\"balance-form\">\r\n            <div className=\"form-row\">\r\n              <div className=\"form-group\">\r\n                <label htmlFor=\"current-balance-display\" className=\"form-label\">\r\n                  Current Balance\r\n                </label>\r\n                <input\r\n                  id=\"current-balance-display\"\r\n                  type=\"text\"\r\n                  value={formatCurrency(adjustingBalanceAccount.currentBalance)}\r\n                  disabled\r\n                  className=\"form-input disabled\"\r\n                />\r\n              </div>\r\n\r\n              <div className=\"form-group\">\r\n                <label htmlFor=\"new-balance\" className=\"form-label\">\r\n                  New Balance *\r\n                </label>\r\n                <input\r\n                  id=\"new-balance\"\r\n                  type=\"number\"\r\n                  step=\"0.01\"\r\n                  value={balanceFormData.newBalance}\r\n                  onChange={(e) => setBalanceFormData(prev => ({ ...prev, newBalance: e.target.value }))}\r\n                  className={`form-input ${balanceErrors.newBalance ? 'error' : ''}`}\r\n                  placeholder=\"0.00\"\r\n                />\r\n                {balanceErrors.newBalance && <span className=\"form-error\">{balanceErrors.newBalance}</span>}\r\n              </div>\r\n            </div>\r\n\r\n            <div className=\"form-row\">\r\n              <div className=\"form-group\">\r\n                <label htmlFor=\"effective-date\" className=\"form-label\">\r\n                  Effective Date *\r\n                </label>\r\n                <input\r\n                  id=\"effective-date\"\r\n                  type=\"date\"\r\n                  value={balanceFormData.effectiveDate}\r\n                  onChange={(e) => setBalanceFormData(prev => ({ ...prev, effectiveDate: e.target.value }))}\r\n                  className={`form-input ${balanceErrors.effectiveDate ? 'error' : ''}`}\r\n                />\r\n                {balanceErrors.effectiveDate && <span className=\"form-error\">{balanceErrors.effectiveDate}</span>}\r\n              </div>\r\n\r\n              <div className=\"form-group\">\r\n                <label htmlFor=\"adjustment-reason\" className=\"form-label\">\r\n                  Reason for Adjustment *\r\n                </label>\r\n                <select\r\n                  id=\"adjustment-reason\"\r\n                  value={balanceFormData.reason}\r\n                  onChange={(e) => setBalanceFormData(prev => ({ ...prev, reason: e.target.value }))}\r\n                  className={`form-select ${balanceErrors.reason ? 'error' : ''}`}\r\n                >\r\n                  <option value=\"\">Select a reason...</option>\r\n                  <option value=\"Bank reconciliation\">Bank reconciliation</option>\r\n                  <option value=\"Manual correction\">Manual correction</option>\r\n                  <option value=\"Interest adjustment\">Interest adjustment</option>\r\n                  <option value=\"Fee adjustment\">Fee adjustment</option>\r\n                  <option value=\"Opening balance setup\">Opening balance setup</option>\r\n                  <option value=\"Other\">Other</option>\r\n                </select>\r\n                {balanceErrors.reason && <span className=\"form-error\">{balanceErrors.reason}</span>}\r\n              </div>\r\n            </div>\r\n\r\n            {balanceFormData.reason === 'Other' && (\r\n              <div className=\"form-row\">\r\n                <div className=\"form-group\">\r\n                  <label htmlFor=\"custom-reason\" className=\"form-label\">\r\n                    Custom Reason *\r\n                  </label>\r\n                  <input\r\n                    id=\"custom-reason\"\r\n                    type=\"text\"\r\n                    placeholder=\"Enter custom reason...\"\r\n                    className=\"form-input\"\r\n                    onChange={(e) => setBalanceFormData(prev => ({ ...prev, reason: e.target.value }))}\r\n                  />\r\n                </div>\r\n              </div>\r\n            )}\r\n\r\n            <div className=\"adjustment-preview\">\r\n              <h4>Adjustment Preview</h4>\r\n              <div className=\"preview-details\">\r\n                <p><strong>Current Balance:</strong> {formatCurrency(adjustingBalanceAccount.currentBalance)}</p>\r\n                <p><strong>New Balance:</strong> {balanceFormData.newBalance ? formatCurrency(parseFloat(balanceFormData.newBalance)) : '$0.00'}</p>\r\n                <p><strong>Adjustment Amount:</strong> \r\n                  <span className={`adjustment-amount ${balanceFormData.newBalance && parseFloat(balanceFormData.newBalance) - adjustingBalanceAccount.currentBalance >= 0 ? 'positive' : 'negative'}`}>\r\n                    {balanceFormData.newBalance ? \r\n                      (parseFloat(balanceFormData.newBalance) - adjustingBalanceAccount.currentBalance >= 0 ? '+' : '') + \r\n                      formatCurrency(parseFloat(balanceFormData.newBalance) - adjustingBalanceAccount.currentBalance) : \r\n                      '$0.00'\r\n                    }\r\n                  </span>\r\n                </p>\r\n              </div>\r\n            </div>\r\n\r\n            {balanceErrors.submit && (\r\n              <div className=\"form-error-message\">\r\n                {balanceErrors.submit}\r\n              </div>\r\n            )}\r\n\r\n            <div className=\"form-actions\">\r\n              <button\r\n                type=\"button\"\r\n                onClick={() => setAdjustingBalanceAccount(null)}\r\n                className=\"btn btn-secondary\"\r\n              >\r\n                Cancel\r\n              </button>\r\n              <button\r\n                type=\"submit\"\r\n                className=\"btn btn-primary\"\r\n              >\r\n                Apply Adjustment\r\n              </button>\r\n            </div>\r\n          </form>\r\n        </div>\r\n      )}\r\n\r\n      {/* Balance History Display */}\r\n      {accounts.map(account => \r\n        showBalanceHistory[account.id] && (\r\n          <div key={`history-${account.id}`} className=\"balance-history-section\">\r\n            <div className=\"form-header\">\r\n              <h3>Balance History - {account.name}</h3>\r\n              <button\r\n                type=\"button\"\r\n                onClick={() => toggleBalanceHistory(account.id)}\r\n                className=\"btn btn-secondary\"\r\n              >\r\n                Close\r\n              </button>\r\n            </div>\r\n            \r\n            <div className=\"balance-history\">\r\n              {(() => {\r\n                const history = balanceManagementService.getBalanceHistory(account.id);\r\n                const adjustments = balanceManagementService.getBalanceAdjustments(account.id);\r\n                \r\n                if (history.length === 0 && adjustments.length === 0) {\r\n                  return (\r\n                    <div className=\"empty-history\">\r\n                      <p>No balance history available for this account.</p>\r\n                    </div>\r\n                  );\r\n                }\r\n\r\n                return (\r\n                  <div className=\"history-content\">\r\n                    {adjustments.length > 0 && (\r\n                      <div className=\"adjustments-section\">\r\n                        <h4>Balance Adjustments</h4>\r\n                        <div className=\"adjustments-list\">\r\n                          {adjustments.map(adjustment => (\r\n                            <div key={adjustment.id} className=\"adjustment-item\">\r\n                              <div className=\"adjustment-header\">\r\n                                <span className=\"adjustment-date\">{adjustment.date}</span>\r\n                                <span className={`adjustment-amount ${adjustment.adjustmentAmount >= 0 ? 'positive' : 'negative'}`}>\r\n                                  {adjustment.adjustmentAmount >= 0 ? '+' : ''}{formatCurrency(adjustment.adjustmentAmount)}\r\n                                </span>\r\n                              </div>\r\n                              <div className=\"adjustment-details\">\r\n                                <p><strong>Reason:</strong> {adjustment.reason}</p>\r\n                                <p><strong>Previous Balance:</strong> {formatCurrency(adjustment.previousBalance)}</p>\r\n                                <p><strong>New Balance:</strong> {formatCurrency(adjustment.newBalance)}</p>\r\n                                <p><strong>Type:</strong> {adjustment.type}</p>\r\n                              </div>\r\n                            </div>\r\n                          ))}\r\n                        </div>\r\n                      </div>\r\n                    )}\r\n                  </div>\r\n                );\r\n              })()}\r\n            </div>\r\n          </div>\r\n        )\r\n      )}\r\n    </div>\r\n  );\r\n}; "], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,WAAW,QAAQ,OAAO;AAEpD,SAASC,kBAAkB,QAAQ,gCAAgC;AACnE,SAASC,wBAAwB,QAA2B,sCAAsC;AAClG,OAAO,0BAA0B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAoBlC,OAAO,MAAMC,kBAAqD,GAAGA,CAAC;EAAEC;AAAkB,CAAC,KAAK;EAAAC,EAAA;EAC9F,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGV,QAAQ,CAAgBE,kBAAkB,CAACS,cAAc,CAAC,CAAC,CAAC;EAC5F,MAAM,CAACC,eAAe,EAAEC,kBAAkB,CAAC,GAAGb,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAACc,cAAc,EAAEC,iBAAiB,CAAC,GAAGf,QAAQ,CAAqB,IAAI,CAAC;EAC9E,MAAM,CAACgB,uBAAuB,EAAEC,0BAA0B,CAAC,GAAGjB,QAAQ,CAAqB,IAAI,CAAC;EAChG,MAAM,CAACkB,QAAQ,EAAEC,WAAW,CAAC,GAAGnB,QAAQ,CAAkB;IACxDoB,IAAI,EAAE,EAAE;IACRC,aAAa,EAAE,EAAE;IACjBC,QAAQ,EAAE,EAAE;IACZC,QAAQ,EAAE,KAAK;IACfC,cAAc,EAAE;EAClB,CAAC,CAAC;EACF,MAAM,CAACC,eAAe,EAAEC,kBAAkB,CAAC,GAAG1B,QAAQ,CAA4B;IAChF2B,UAAU,EAAE,EAAE;IACdC,aAAa,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IACrDC,MAAM,EAAE;EACV,CAAC,CAAC;EACF,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAGlC,QAAQ,CAAyB,CAAC,CAAC,CAAC;EAChE,MAAM,CAACmC,aAAa,EAAEC,gBAAgB,CAAC,GAAGpC,QAAQ,CAAyB,CAAC,CAAC,CAAC;EAC9E,MAAM,CAACqC,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGtC,QAAQ,CAA0B,CAAC,CAAC,CAAC;EAEzF,MAAMuC,eAAe,GAAGtC,WAAW,CAAC,MAAM;IACxCS,WAAW,CAACR,kBAAkB,CAACS,cAAc,CAAC,CAAC,CAAC;IAChD,IAAIJ,iBAAiB,EAAE;MACrBA,iBAAiB,CAAC,CAAC;IACrB;EACF,CAAC,EAAE,CAACA,iBAAiB,CAAC,CAAC;EAEvB,MAAMiC,SAAS,GAAGvC,WAAW,CAAC,MAAM;IAClCkB,WAAW,CAAC;MACVC,IAAI,EAAE,EAAE;MACRC,aAAa,EAAE,EAAE;MACjBC,QAAQ,EAAE,EAAE;MACZC,QAAQ,EAAE,KAAK;MACfC,cAAc,EAAE;IAClB,CAAC,CAAC;IACFU,SAAS,CAAC,CAAC,CAAC,CAAC;IACbrB,kBAAkB,CAAC,KAAK,CAAC;IACzBE,iBAAiB,CAAC,IAAI,CAAC;EACzB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAM0B,YAAY,GAAGxC,WAAW,CAAEyC,IAAqB,IAA6B;IAClF,MAAMC,SAAiC,GAAG,CAAC,CAAC;IAE5C,IAAI,CAACD,IAAI,CAACtB,IAAI,CAACwB,IAAI,CAAC,CAAC,EAAE;MACrBD,SAAS,CAACvB,IAAI,GAAG,0BAA0B;IAC7C;IAEA,IAAI,CAACsB,IAAI,CAACrB,aAAa,CAACuB,IAAI,CAAC,CAAC,EAAE;MAC9BD,SAAS,CAACtB,aAAa,GAAG,4BAA4B;IACxD,CAAC,MAAM,IAAIZ,QAAQ,CAACoC,IAAI,CAACC,GAAG,IAC1BA,GAAG,CAACzB,aAAa,KAAKqB,IAAI,CAACrB,aAAa,CAACuB,IAAI,CAAC,CAAC,KAC9C,CAAC9B,cAAc,IAAIgC,GAAG,CAACC,EAAE,KAAKjC,cAAc,CAACiC,EAAE,CAClD,CAAC,EAAE;MACDJ,SAAS,CAACtB,aAAa,GAAG,+BAA+B;IAC3D;IAEA,IAAI,CAACqB,IAAI,CAACpB,QAAQ,CAACsB,IAAI,CAAC,CAAC,EAAE;MACzBD,SAAS,CAACrB,QAAQ,GAAG,uBAAuB;IAC9C;IAEA,IAAI,CAACoB,IAAI,CAACnB,QAAQ,CAACqB,IAAI,CAAC,CAAC,EAAE;MACzBD,SAAS,CAACpB,QAAQ,GAAG,sBAAsB;IAC7C;IAEA,MAAMyB,OAAO,GAAGC,UAAU,CAACP,IAAI,CAAClB,cAAc,CAAC;IAC/C,IAAI0B,KAAK,CAACF,OAAO,CAAC,EAAE;MAClBL,SAAS,CAACnB,cAAc,GAAG,wCAAwC;IACrE;IAEA,OAAOmB,SAAS;EAClB,CAAC,EAAE,CAAClC,QAAQ,EAAEK,cAAc,CAAC,CAAC;EAE9B,MAAMqC,iBAAiB,GAAGlD,WAAW,CAAC,CAACmD,KAA4B,EAAEC,KAAa,KAAK;IACrFlC,WAAW,CAACmC,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE,CAACF,KAAK,GAAGC;IAAM,CAAC,CAAC,CAAC;IAClD;IACA,IAAIpB,MAAM,CAACmB,KAAK,CAAC,EAAE;MACjBlB,SAAS,CAACoB,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAE,CAACF,KAAK,GAAG;MAAG,CAAC,CAAC,CAAC;IAC/C;EACF,CAAC,EAAE,CAACnB,MAAM,CAAC,CAAC;EAEZ,MAAMsB,gBAAgB,GAAGtD,WAAW,CAAC,MAAM;IACzCY,kBAAkB,CAAC,IAAI,CAAC;IACxBE,iBAAiB,CAAC,IAAI,CAAC;IACvByB,SAAS,CAAC,CAAC;EACb,CAAC,EAAE,CAACA,SAAS,CAAC,CAAC;EAEf,MAAMgB,iBAAiB,GAAGvD,WAAW,CAAEwD,OAAoB,IAAK;IAC9D1C,iBAAiB,CAAC0C,OAAO,CAAC;IAC1B5C,kBAAkB,CAAC,KAAK,CAAC;IACzBM,WAAW,CAAC;MACVC,IAAI,EAAEqC,OAAO,CAACrC,IAAI;MAClBC,aAAa,EAAEoC,OAAO,CAACpC,aAAa;MACpCC,QAAQ,EAAEmC,OAAO,CAACnC,QAAQ;MAC1BC,QAAQ,EAAEkC,OAAO,CAAClC,QAAQ;MAC1BC,cAAc,EAAEiC,OAAO,CAACjC,cAAc,CAACkC,OAAO,CAAC,CAAC;IAClD,CAAC,CAAC;IACFxB,SAAS,CAAC,CAAC,CAAC,CAAC;EACf,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMyB,YAAY,GAAG1D,WAAW,CAAE2D,CAAkB,IAAK;IACvDA,CAAC,CAACC,cAAc,CAAC,CAAC;IAElB,MAAMlB,SAAS,GAAGF,YAAY,CAACvB,QAAQ,CAAC;IACxC,IAAI4C,MAAM,CAACC,IAAI,CAACpB,SAAS,CAAC,CAACqB,MAAM,GAAG,CAAC,EAAE;MACrC9B,SAAS,CAACS,SAAS,CAAC;MACpB;IACF;IAEA,MAAMsB,WAAW,GAAG;MAClB7C,IAAI,EAAEF,QAAQ,CAACE,IAAI,CAACwB,IAAI,CAAC,CAAC;MAC1BvB,aAAa,EAAEH,QAAQ,CAACG,aAAa,CAACuB,IAAI,CAAC,CAAC;MAC5CtB,QAAQ,EAAEJ,QAAQ,CAACI,QAAQ,CAACsB,IAAI,CAAC,CAAC;MAClCrB,QAAQ,EAAEL,QAAQ,CAACK,QAAQ,CAACqB,IAAI,CAAC,CAAC;MAClCpB,cAAc,EAAEyB,UAAU,CAAC/B,QAAQ,CAACM,cAAc;IACpD,CAAC;IAED,IAAI;MACF,IAAIV,cAAc,EAAE;QAClBZ,kBAAkB,CAACgE,aAAa,CAACpD,cAAc,CAACiC,EAAE,EAAEkB,WAAW,CAAC;MAClE,CAAC,MAAM;QACL/D,kBAAkB,CAACiE,UAAU,CAACF,WAAW,CAAC;MAC5C;MAEA1B,eAAe,CAAC,CAAC;MACjBC,SAAS,CAAC,CAAC;IACb,CAAC,CAAC,OAAO4B,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MAC7ClC,SAAS,CAAC;QAAEoC,MAAM,EAAE;MAA4C,CAAC,CAAC;IACpE;EACF,CAAC,EAAE,CAACpD,QAAQ,EAAEuB,YAAY,EAAE3B,cAAc,EAAEyB,eAAe,EAAEC,SAAS,CAAC,CAAC;EAExE,MAAM+B,mBAAmB,GAAGtE,WAAW,CAAEuE,SAAiB,IAAK;IAC7D,IAAIC,MAAM,CAACC,OAAO,CAAC,6EAA6E,CAAC,EAAE;MACjGxE,kBAAkB,CAACyE,aAAa,CAACH,SAAS,CAAC;MAC3CjC,eAAe,CAAC,CAAC;IACnB;EACF,CAAC,EAAE,CAACA,eAAe,CAAC,CAAC;;EAErB;EACA,MAAMqC,mBAAmB,GAAG3E,WAAW,CAAEwD,OAAoB,IAAK;IAChExC,0BAA0B,CAACwC,OAAO,CAAC;IACnC/B,kBAAkB,CAAC;MACjBC,UAAU,EAAE8B,OAAO,CAACjC,cAAc,CAACkC,OAAO,CAAC,CAAC,CAAC;MAC7C9B,aAAa,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;MACrDC,MAAM,EAAE;IACV,CAAC,CAAC;IACFI,gBAAgB,CAAC,CAAC,CAAC,CAAC;EACtB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMyC,mBAAmB,GAAG5E,WAAW,CAAEyC,IAA+B,IAA6B;IACnG,MAAMC,SAAiC,GAAG,CAAC,CAAC;IAE5C,MAAMK,OAAO,GAAGC,UAAU,CAACP,IAAI,CAACf,UAAU,CAAC;IAC3C,IAAIuB,KAAK,CAACF,OAAO,CAAC,EAAE;MAClBL,SAAS,CAAChB,UAAU,GAAG,gCAAgC;IACzD;IAEA,IAAI,CAACe,IAAI,CAACd,aAAa,EAAE;MACvBe,SAAS,CAACf,aAAa,GAAG,4BAA4B;IACxD;IAEA,IAAI,CAACc,IAAI,CAACV,MAAM,CAACY,IAAI,CAAC,CAAC,EAAE;MACvBD,SAAS,CAACX,MAAM,GAAG,mCAAmC;IACxD;IAEA,OAAOW,SAAS;EAClB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMmC,mBAAmB,GAAG7E,WAAW,CAAE2D,CAAkB,IAAK;IAC9DA,CAAC,CAACC,cAAc,CAAC,CAAC;IAElB,IAAI,CAAC7C,uBAAuB,EAAE;IAE9B,MAAM2B,SAAS,GAAGkC,mBAAmB,CAACpD,eAAe,CAAC;IACtD,IAAIqC,MAAM,CAACC,IAAI,CAACpB,SAAS,CAAC,CAACqB,MAAM,GAAG,CAAC,EAAE;MACrC5B,gBAAgB,CAACO,SAAS,CAAC;MAC3B;IACF;IAEA,IAAI;MACF,MAAMhB,UAAU,GAAGsB,UAAU,CAACxB,eAAe,CAACE,UAAU,CAAC;MACzD,MAAMoD,cAAc,GAAG5E,wBAAwB,CAAC6E,oBAAoB,CAClEhE,uBAAuB,EACvBW,UAAU,EACVF,eAAe,CAACG,aAAa,EAC7BH,eAAe,CAACO,MAClB,CAAC;;MAED;MACA9B,kBAAkB,CAACgE,aAAa,CAACa,cAAc,CAAChC,EAAE,EAAE;QAClD3B,IAAI,EAAE2D,cAAc,CAAC3D,IAAI;QACzBC,aAAa,EAAE0D,cAAc,CAAC1D,aAAa;QAC3CC,QAAQ,EAAEyD,cAAc,CAACzD,QAAQ;QACjCC,QAAQ,EAAEwD,cAAc,CAACxD,QAAQ;QACjCC,cAAc,EAAEuD,cAAc,CAACvD;MACjC,CAAC,CAAC;MAEFe,eAAe,CAAC,CAAC;MACjBtB,0BAA0B,CAAC,IAAI,CAAC;MAChCS,kBAAkB,CAAC;QACjBC,UAAU,EAAE,EAAE;QACdC,aAAa,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QACrDC,MAAM,EAAE;MACV,CAAC,CAAC;IACJ,CAAC,CAAC,OAAOoC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MAChDhC,gBAAgB,CAAC;QAAEkC,MAAM,EAAE;MAA8C,CAAC,CAAC;IAC7E;EACF,CAAC,EAAE,CAACtD,uBAAuB,EAAES,eAAe,EAAEoD,mBAAmB,EAAEtC,eAAe,CAAC,CAAC;EAEpF,MAAM0C,oBAAoB,GAAGhF,WAAW,CAAEuE,SAAiB,IAAK;IAC9DlC,qBAAqB,CAACgB,IAAI,KAAK;MAC7B,GAAGA,IAAI;MACP,CAACkB,SAAS,GAAG,CAAClB,IAAI,CAACkB,SAAS;IAC9B,CAAC,CAAC,CAAC;EACL,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMU,cAAc,GAAIC,MAAc,IAAa;IACjD,OAAO,IAAIC,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;MACpCC,KAAK,EAAE,UAAU;MACjB/D,QAAQ,EAAE;IACZ,CAAC,CAAC,CAACgE,MAAM,CAACJ,MAAM,CAAC;EACnB,CAAC;EAED,oBACE9E,OAAA;IAAKmF,SAAS,EAAC,sBAAsB;IAAAC,QAAA,gBACnCpF,OAAA;MAAKmF,SAAS,EAAC,gBAAgB;MAAAC,QAAA,gBAC7BpF,OAAA;QAAAoF,QAAA,EAAI;MAAuB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAChCxF,OAAA;QAAAoF,QAAA,EAAG;MAAwC;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC5C,CAAC,eAENxF,OAAA;MAAKmF,SAAS,EAAC,kBAAkB;MAAAC,QAAA,gBAC/BpF,OAAA;QAAKmF,SAAS,EAAC,gBAAgB;QAAAC,QAAA,gBAC7BpF,OAAA;UAAAoF,QAAA,GAAI,sBAAoB,EAAChF,QAAQ,CAACuD,MAAM,EAAC,GAAC;QAAA;UAAA0B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC/CxF,OAAA;UACEyF,IAAI,EAAC,QAAQ;UACbC,OAAO,EAAExC,gBAAiB;UAC1BiC,SAAS,EAAC,iBAAiB;UAAAC,QAAA,EAC5B;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,EAELpF,QAAQ,CAACuD,MAAM,KAAK,CAAC,gBACpB3D,OAAA;QAAKmF,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1BpF,OAAA;UAAKmF,SAAS,EAAC,YAAY;UAAAC,QAAA,eACzBpF,OAAA;YAAK2F,KAAK,EAAC,IAAI;YAACC,MAAM,EAAC,IAAI;YAACC,OAAO,EAAC,WAAW;YAACC,IAAI,EAAC,MAAM;YAACC,MAAM,EAAC,cAAc;YAACC,WAAW,EAAC,GAAG;YAAAZ,QAAA,gBAC/FpF,OAAA;cAAMiG,CAAC,EAAC,GAAG;cAACC,CAAC,EAAC,GAAG;cAACP,KAAK,EAAC,IAAI;cAACC,MAAM,EAAC,IAAI;cAACO,EAAE,EAAC,GAAG;cAACC,EAAE,EAAC;YAAG;cAAAf,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACzDxF,OAAA;cAAMqG,EAAE,EAAC,GAAG;cAACC,EAAE,EAAC,IAAI;cAACC,EAAE,EAAC,IAAI;cAACC,EAAE,EAAC;YAAI;cAAAnB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNxF,OAAA;UAAAoF,QAAA,EAAI;QAAgB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACzBxF,OAAA;UAAAoF,QAAA,EAAG;QAA6C;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjD,CAAC,gBAENxF,OAAA;QAAKmF,SAAS,EAAC,eAAe;QAAAC,QAAA,EAC3BhF,QAAQ,CAACqG,GAAG,CAAErD,OAAO,iBACpBpD,OAAA;UAAsBmF,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC5CpF,OAAA;YAAKmF,SAAS,EAAC,gBAAgB;YAAAC,QAAA,gBAC7BpF,OAAA;cAAAoF,QAAA,EAAKhC,OAAO,CAACrC;YAAI;cAAAsE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACvBxF,OAAA;cAAKmF,SAAS,EAAC,iBAAiB;cAAAC,QAAA,gBAC9BpF,OAAA;gBACEyF,IAAI,EAAC,QAAQ;gBACbC,OAAO,EAAEA,CAAA,KAAMnB,mBAAmB,CAACnB,OAAO,CAAE;gBAC5C+B,SAAS,EAAC,UAAU;gBACpBuB,KAAK,EAAC,gBAAgB;gBAAAtB,QAAA,eAEtBpF,OAAA;kBAAK2F,KAAK,EAAC,IAAI;kBAACC,MAAM,EAAC,IAAI;kBAACC,OAAO,EAAC,WAAW;kBAACC,IAAI,EAAC,MAAM;kBAACC,MAAM,EAAC,cAAc;kBAACC,WAAW,EAAC,GAAG;kBAAAZ,QAAA,eAC/FpF,OAAA;oBAAM2G,CAAC,EAAC;kBAA2D;oBAAAtB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA,CAAC,eACTxF,OAAA;gBACEyF,IAAI,EAAC,QAAQ;gBACbC,OAAO,EAAEA,CAAA,KAAMd,oBAAoB,CAACxB,OAAO,CAACV,EAAE,CAAE;gBAChDyC,SAAS,EAAC,UAAU;gBACpBuB,KAAK,EAAC,sBAAsB;gBAAAtB,QAAA,eAE5BpF,OAAA;kBAAK2F,KAAK,EAAC,IAAI;kBAACC,MAAM,EAAC,IAAI;kBAACC,OAAO,EAAC,WAAW;kBAACC,IAAI,EAAC,MAAM;kBAACC,MAAM,EAAC,cAAc;kBAACC,WAAW,EAAC,GAAG;kBAAAZ,QAAA,eAC/FpF,OAAA;oBAAM2G,CAAC,EAAC;kBAA0B;oBAAAtB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA,CAAC,eACTxF,OAAA;gBACEyF,IAAI,EAAC,QAAQ;gBACbC,OAAO,EAAEA,CAAA,KAAMvC,iBAAiB,CAACC,OAAO,CAAE;gBAC1C+B,SAAS,EAAC,UAAU;gBACpBuB,KAAK,EAAC,cAAc;gBAAAtB,QAAA,eAEpBpF,OAAA;kBAAK2F,KAAK,EAAC,IAAI;kBAACC,MAAM,EAAC,IAAI;kBAACC,OAAO,EAAC,WAAW;kBAACC,IAAI,EAAC,MAAM;kBAACC,MAAM,EAAC,cAAc;kBAACC,WAAW,EAAC,GAAG;kBAAAZ,QAAA,gBAC/FpF,OAAA;oBAAM2G,CAAC,EAAC;kBAAU;oBAAAtB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACrBxF,OAAA;oBAAM2G,CAAC,EAAC;kBAAyD;oBAAAtB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA,CAAC,eACTxF,OAAA;gBACEyF,IAAI,EAAC,QAAQ;gBACbC,OAAO,EAAEA,CAAA,KAAMxB,mBAAmB,CAACd,OAAO,CAACV,EAAE,CAAE;gBAC/CyC,SAAS,EAAC,qBAAqB;gBAC/BuB,KAAK,EAAC,gBAAgB;gBAAAtB,QAAA,eAEtBpF,OAAA;kBAAK2F,KAAK,EAAC,IAAI;kBAACC,MAAM,EAAC,IAAI;kBAACC,OAAO,EAAC,WAAW;kBAACC,IAAI,EAAC,MAAM;kBAACC,MAAM,EAAC,cAAc;kBAACC,WAAW,EAAC,GAAG;kBAAAZ,QAAA,gBAC/FpF,OAAA;oBAAU4G,MAAM,EAAC;kBAAc;oBAAAvB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAClCxF,OAAA;oBAAM2G,CAAC,EAAC;kBAAgF;oBAAAtB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxF;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACNxF,OAAA;YAAKmF,SAAS,EAAC,iBAAiB;YAAAC,QAAA,gBAC9BpF,OAAA;cAAAoF,QAAA,gBAAGpF,OAAA;gBAAAoF,QAAA,EAAQ;cAAK;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAACpC,OAAO,CAACnC,QAAQ;YAAA;cAAAoE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAChDxF,OAAA;cAAAoF,QAAA,gBAAGpF,OAAA;gBAAAoF,QAAA,EAAQ;cAAe;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAACpC,OAAO,CAACpC,aAAa;YAAA;cAAAqE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC/DxF,OAAA;cAAAoF,QAAA,gBAAGpF,OAAA;gBAAAoF,QAAA,EAAQ;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAACpC,OAAO,CAAClC,QAAQ;YAAA;cAAAmE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACpDxF,OAAA;cAAAoF,QAAA,gBAAGpF,OAAA;gBAAAoF,QAAA,EAAQ;cAAgB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,eAAAxF,OAAA;gBAAMmF,SAAS,EAAC,SAAS;gBAAAC,QAAA,EAAEP,cAAc,CAACzB,OAAO,CAACjC,cAAc;cAAC;gBAAAkE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/G,CAAC;QAAA,GArDEpC,OAAO,CAACV,EAAE;UAAA2C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAsDf,CACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,EAEL,CAACjF,eAAe,IAAIE,cAAc,kBACjCT,OAAA;MAAKmF,SAAS,EAAC,sBAAsB;MAAAC,QAAA,gBACnCpF,OAAA;QAAKmF,SAAS,EAAC,aAAa;QAAAC,QAAA,eAC1BpF,OAAA;UAAAoF,QAAA,EAAK3E,cAAc,GAAG,cAAc,GAAG;QAAiB;UAAA4E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3D,CAAC,eAENxF,OAAA;QAAM6G,QAAQ,EAAEvD,YAAa;QAAC6B,SAAS,EAAC,cAAc;QAAAC,QAAA,gBACpDpF,OAAA;UAAKmF,SAAS,EAAC,UAAU;UAAAC,QAAA,gBACvBpF,OAAA;YAAKmF,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBpF,OAAA;cAAO8G,OAAO,EAAC,cAAc;cAAC3B,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAErD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRxF,OAAA;cACE0C,EAAE,EAAC,cAAc;cACjB+C,IAAI,EAAC,MAAM;cACXzC,KAAK,EAAEnC,QAAQ,CAACE,IAAK;cACrBgG,QAAQ,EAAGxD,CAAC,IAAKT,iBAAiB,CAAC,MAAM,EAAES,CAAC,CAACyD,MAAM,CAAChE,KAAK,CAAE;cAC3DmC,SAAS,EAAE,cAAcvD,MAAM,CAACb,IAAI,GAAG,OAAO,GAAG,EAAE,EAAG;cACtDkG,WAAW,EAAC;YAA8B;cAAA5B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3C,CAAC,EACD5D,MAAM,CAACb,IAAI,iBAAIf,OAAA;cAAMmF,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAExD,MAAM,CAACb;YAAI;cAAAsE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9D,CAAC,eAENxF,OAAA;YAAKmF,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBpF,OAAA;cAAO8G,OAAO,EAAC,gBAAgB;cAAC3B,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAEvD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRxF,OAAA;cACE0C,EAAE,EAAC,gBAAgB;cACnB+C,IAAI,EAAC,MAAM;cACXzC,KAAK,EAAEnC,QAAQ,CAACG,aAAc;cAC9B+F,QAAQ,EAAGxD,CAAC,IAAKT,iBAAiB,CAAC,eAAe,EAAES,CAAC,CAACyD,MAAM,CAAChE,KAAK,CAAE;cACpEmC,SAAS,EAAE,cAAcvD,MAAM,CAACZ,aAAa,GAAG,OAAO,GAAG,EAAE,EAAG;cAC/DiG,WAAW,EAAC;YAAkB;cAAA5B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/B,CAAC,EACD5D,MAAM,CAACZ,aAAa,iBAAIhB,OAAA;cAAMmF,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAExD,MAAM,CAACZ;YAAa;cAAAqE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENxF,OAAA;UAAKmF,SAAS,EAAC,UAAU;UAAAC,QAAA,gBACvBpF,OAAA;YAAKmF,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBpF,OAAA;cAAO8G,OAAO,EAAC,WAAW;cAAC3B,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAElD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRxF,OAAA;cACE0C,EAAE,EAAC,WAAW;cACd+C,IAAI,EAAC,MAAM;cACXzC,KAAK,EAAEnC,QAAQ,CAACI,QAAS;cACzB8F,QAAQ,EAAGxD,CAAC,IAAKT,iBAAiB,CAAC,UAAU,EAAES,CAAC,CAACyD,MAAM,CAAChE,KAAK,CAAE;cAC/DmC,SAAS,EAAE,cAAcvD,MAAM,CAACX,QAAQ,GAAG,OAAO,GAAG,EAAE,EAAG;cAC1DgG,WAAW,EAAC;YAA2B;cAAA5B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxC,CAAC,EACD5D,MAAM,CAACX,QAAQ,iBAAIjB,OAAA;cAAMmF,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAExD,MAAM,CAACX;YAAQ;cAAAoE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtE,CAAC,eAENxF,OAAA;YAAKmF,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBpF,OAAA;cAAO8G,OAAO,EAAC,UAAU;cAAC3B,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAEjD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRxF,OAAA;cACE0C,EAAE,EAAC,UAAU;cACbM,KAAK,EAAEnC,QAAQ,CAACK,QAAS;cACzB6F,QAAQ,EAAGxD,CAAC,IAAKT,iBAAiB,CAAC,UAAU,EAAES,CAAC,CAACyD,MAAM,CAAChE,KAAK,CAAE;cAC/DmC,SAAS,EAAE,eAAevD,MAAM,CAACV,QAAQ,GAAG,OAAO,GAAG,EAAE,EAAG;cAAAkE,QAAA,gBAE3DpF,OAAA;gBAAQgD,KAAK,EAAC,KAAK;gBAAAoC,QAAA,EAAC;cAAe;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC5CxF,OAAA;gBAAQgD,KAAK,EAAC,KAAK;gBAAAoC,QAAA,EAAC;cAAU;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACvCxF,OAAA;gBAAQgD,KAAK,EAAC,KAAK;gBAAAoC,QAAA,EAAC;cAAmB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAChDxF,OAAA;gBAAQgD,KAAK,EAAC,KAAK;gBAAAoC,QAAA,EAAC;cAAqB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAClDxF,OAAA;gBAAQgD,KAAK,EAAC,KAAK;gBAAAoC,QAAA,EAAC;cAAuB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9C,CAAC,EACR5D,MAAM,CAACV,QAAQ,iBAAIlB,OAAA;cAAMmF,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAExD,MAAM,CAACV;YAAQ;cAAAmE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENxF,OAAA;UAAKmF,SAAS,EAAC,UAAU;UAAAC,QAAA,eACvBpF,OAAA;YAAKmF,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBpF,OAAA;cAAO8G,OAAO,EAAC,iBAAiB;cAAC3B,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAExD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRxF,OAAA;cACE0C,EAAE,EAAC,iBAAiB;cACpB+C,IAAI,EAAC,QAAQ;cACbyB,IAAI,EAAC,MAAM;cACXlE,KAAK,EAAEnC,QAAQ,CAACM,cAAe;cAC/B4F,QAAQ,EAAGxD,CAAC,IAAKT,iBAAiB,CAAC,gBAAgB,EAAES,CAAC,CAACyD,MAAM,CAAChE,KAAK,CAAE;cACrEmC,SAAS,EAAE,cAAcvD,MAAM,CAACT,cAAc,GAAG,OAAO,GAAG,EAAE,EAAG;cAChE8F,WAAW,EAAC;YAAM;cAAA5B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnB,CAAC,EACD5D,MAAM,CAACT,cAAc,iBAAInB,OAAA;cAAMmF,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAExD,MAAM,CAACT;YAAc;cAAAkE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,EAEL5D,MAAM,CAACqC,MAAM,iBACZjE,OAAA;UAAKmF,SAAS,EAAC,oBAAoB;UAAAC,QAAA,EAChCxD,MAAM,CAACqC;QAAM;UAAAoB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACX,CACN,eAEDxF,OAAA;UAAKmF,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3BpF,OAAA;YACEyF,IAAI,EAAC,QAAQ;YACbC,OAAO,EAAEvD,SAAU;YACnBgD,SAAS,EAAC,mBAAmB;YAAAC,QAAA,EAC9B;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTxF,OAAA;YACEyF,IAAI,EAAC,QAAQ;YACbN,SAAS,EAAC,iBAAiB;YAAAC,QAAA,EAE1B3E,cAAc,GAAG,gBAAgB,GAAG;UAAa;YAAA4E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5C,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CACN,EAGA7E,uBAAuB,iBACtBX,OAAA;MAAKmF,SAAS,EAAC,4BAA4B;MAAAC,QAAA,gBACzCpF,OAAA;QAAKmF,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1BpF,OAAA;UAAAoF,QAAA,GAAI,mBAAiB,EAACzE,uBAAuB,CAACI,IAAI;QAAA;UAAAsE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACxDxF,OAAA;UAAAoF,QAAA,EAAG;QAA4D;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChE,CAAC,eAENxF,OAAA;QAAM6G,QAAQ,EAAEpC,mBAAoB;QAACU,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC3DpF,OAAA;UAAKmF,SAAS,EAAC,UAAU;UAAAC,QAAA,gBACvBpF,OAAA;YAAKmF,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBpF,OAAA;cAAO8G,OAAO,EAAC,yBAAyB;cAAC3B,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAEhE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRxF,OAAA;cACE0C,EAAE,EAAC,yBAAyB;cAC5B+C,IAAI,EAAC,MAAM;cACXzC,KAAK,EAAE6B,cAAc,CAAClE,uBAAuB,CAACQ,cAAc,CAAE;cAC9DgG,QAAQ;cACRhC,SAAS,EAAC;YAAqB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAENxF,OAAA;YAAKmF,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBpF,OAAA;cAAO8G,OAAO,EAAC,aAAa;cAAC3B,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAEpD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRxF,OAAA;cACE0C,EAAE,EAAC,aAAa;cAChB+C,IAAI,EAAC,QAAQ;cACbyB,IAAI,EAAC,MAAM;cACXlE,KAAK,EAAE5B,eAAe,CAACE,UAAW;cAClCyF,QAAQ,EAAGxD,CAAC,IAAKlC,kBAAkB,CAAC4B,IAAI,KAAK;gBAAE,GAAGA,IAAI;gBAAE3B,UAAU,EAAEiC,CAAC,CAACyD,MAAM,CAAChE;cAAM,CAAC,CAAC,CAAE;cACvFmC,SAAS,EAAE,cAAcrD,aAAa,CAACR,UAAU,GAAG,OAAO,GAAG,EAAE,EAAG;cACnE2F,WAAW,EAAC;YAAM;cAAA5B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnB,CAAC,EACD1D,aAAa,CAACR,UAAU,iBAAItB,OAAA;cAAMmF,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAEtD,aAAa,CAACR;YAAU;cAAA+D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENxF,OAAA;UAAKmF,SAAS,EAAC,UAAU;UAAAC,QAAA,gBACvBpF,OAAA;YAAKmF,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBpF,OAAA;cAAO8G,OAAO,EAAC,gBAAgB;cAAC3B,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAEvD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRxF,OAAA;cACE0C,EAAE,EAAC,gBAAgB;cACnB+C,IAAI,EAAC,MAAM;cACXzC,KAAK,EAAE5B,eAAe,CAACG,aAAc;cACrCwF,QAAQ,EAAGxD,CAAC,IAAKlC,kBAAkB,CAAC4B,IAAI,KAAK;gBAAE,GAAGA,IAAI;gBAAE1B,aAAa,EAAEgC,CAAC,CAACyD,MAAM,CAAChE;cAAM,CAAC,CAAC,CAAE;cAC1FmC,SAAS,EAAE,cAAcrD,aAAa,CAACP,aAAa,GAAG,OAAO,GAAG,EAAE;YAAG;cAAA8D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvE,CAAC,EACD1D,aAAa,CAACP,aAAa,iBAAIvB,OAAA;cAAMmF,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAEtD,aAAa,CAACP;YAAa;cAAA8D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9F,CAAC,eAENxF,OAAA;YAAKmF,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBpF,OAAA;cAAO8G,OAAO,EAAC,mBAAmB;cAAC3B,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAE1D;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRxF,OAAA;cACE0C,EAAE,EAAC,mBAAmB;cACtBM,KAAK,EAAE5B,eAAe,CAACO,MAAO;cAC9BoF,QAAQ,EAAGxD,CAAC,IAAKlC,kBAAkB,CAAC4B,IAAI,KAAK;gBAAE,GAAGA,IAAI;gBAAEtB,MAAM,EAAE4B,CAAC,CAACyD,MAAM,CAAChE;cAAM,CAAC,CAAC,CAAE;cACnFmC,SAAS,EAAE,eAAerD,aAAa,CAACH,MAAM,GAAG,OAAO,GAAG,EAAE,EAAG;cAAAyD,QAAA,gBAEhEpF,OAAA;gBAAQgD,KAAK,EAAC,EAAE;gBAAAoC,QAAA,EAAC;cAAkB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC5CxF,OAAA;gBAAQgD,KAAK,EAAC,qBAAqB;gBAAAoC,QAAA,EAAC;cAAmB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAChExF,OAAA;gBAAQgD,KAAK,EAAC,mBAAmB;gBAAAoC,QAAA,EAAC;cAAiB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC5DxF,OAAA;gBAAQgD,KAAK,EAAC,qBAAqB;gBAAAoC,QAAA,EAAC;cAAmB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAChExF,OAAA;gBAAQgD,KAAK,EAAC,gBAAgB;gBAAAoC,QAAA,EAAC;cAAc;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACtDxF,OAAA;gBAAQgD,KAAK,EAAC,uBAAuB;gBAAAoC,QAAA,EAAC;cAAqB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACpExF,OAAA;gBAAQgD,KAAK,EAAC,OAAO;gBAAAoC,QAAA,EAAC;cAAK;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9B,CAAC,EACR1D,aAAa,CAACH,MAAM,iBAAI3B,OAAA;cAAMmF,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAEtD,aAAa,CAACH;YAAM;cAAA0D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,EAELpE,eAAe,CAACO,MAAM,KAAK,OAAO,iBACjC3B,OAAA;UAAKmF,SAAS,EAAC,UAAU;UAAAC,QAAA,eACvBpF,OAAA;YAAKmF,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBpF,OAAA;cAAO8G,OAAO,EAAC,eAAe;cAAC3B,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAEtD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRxF,OAAA;cACE0C,EAAE,EAAC,eAAe;cAClB+C,IAAI,EAAC,MAAM;cACXwB,WAAW,EAAC,wBAAwB;cACpC9B,SAAS,EAAC,YAAY;cACtB4B,QAAQ,EAAGxD,CAAC,IAAKlC,kBAAkB,CAAC4B,IAAI,KAAK;gBAAE,GAAGA,IAAI;gBAAEtB,MAAM,EAAE4B,CAAC,CAACyD,MAAM,CAAChE;cAAM,CAAC,CAAC;YAAE;cAAAqC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpF,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN,eAEDxF,OAAA;UAAKmF,SAAS,EAAC,oBAAoB;UAAAC,QAAA,gBACjCpF,OAAA;YAAAoF,QAAA,EAAI;UAAkB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC3BxF,OAAA;YAAKmF,SAAS,EAAC,iBAAiB;YAAAC,QAAA,gBAC9BpF,OAAA;cAAAoF,QAAA,gBAAGpF,OAAA;gBAAAoF,QAAA,EAAQ;cAAgB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAACX,cAAc,CAAClE,uBAAuB,CAACQ,cAAc,CAAC;YAAA;cAAAkE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACjGxF,OAAA;cAAAoF,QAAA,gBAAGpF,OAAA;gBAAAoF,QAAA,EAAQ;cAAY;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAACpE,eAAe,CAACE,UAAU,GAAGuD,cAAc,CAACjC,UAAU,CAACxB,eAAe,CAACE,UAAU,CAAC,CAAC,GAAG,OAAO;YAAA;cAAA+D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACpIxF,OAAA;cAAAoF,QAAA,gBAAGpF,OAAA;gBAAAoF,QAAA,EAAQ;cAAkB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACpCxF,OAAA;gBAAMmF,SAAS,EAAE,qBAAqB/D,eAAe,CAACE,UAAU,IAAIsB,UAAU,CAACxB,eAAe,CAACE,UAAU,CAAC,GAAGX,uBAAuB,CAACQ,cAAc,IAAI,CAAC,GAAG,UAAU,GAAG,UAAU,EAAG;gBAAAiE,QAAA,EAClLhE,eAAe,CAACE,UAAU,GACzB,CAACsB,UAAU,CAACxB,eAAe,CAACE,UAAU,CAAC,GAAGX,uBAAuB,CAACQ,cAAc,IAAI,CAAC,GAAG,GAAG,GAAG,EAAE,IAChG0D,cAAc,CAACjC,UAAU,CAACxB,eAAe,CAACE,UAAU,CAAC,GAAGX,uBAAuB,CAACQ,cAAc,CAAC,GAC/F;cAAO;gBAAAkE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAEL,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,EAEL1D,aAAa,CAACmC,MAAM,iBACnBjE,OAAA;UAAKmF,SAAS,EAAC,oBAAoB;UAAAC,QAAA,EAChCtD,aAAa,CAACmC;QAAM;UAAAoB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClB,CACN,eAEDxF,OAAA;UAAKmF,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3BpF,OAAA;YACEyF,IAAI,EAAC,QAAQ;YACbC,OAAO,EAAEA,CAAA,KAAM9E,0BAA0B,CAAC,IAAI,CAAE;YAChDuE,SAAS,EAAC,mBAAmB;YAAAC,QAAA,EAC9B;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTxF,OAAA;YACEyF,IAAI,EAAC,QAAQ;YACbN,SAAS,EAAC,iBAAiB;YAAAC,QAAA,EAC5B;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CACN,EAGApF,QAAQ,CAACqG,GAAG,CAACrD,OAAO,IACnBpB,kBAAkB,CAACoB,OAAO,CAACV,EAAE,CAAC,iBAC5B1C,OAAA;MAAmCmF,SAAS,EAAC,yBAAyB;MAAAC,QAAA,gBACpEpF,OAAA;QAAKmF,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1BpF,OAAA;UAAAoF,QAAA,GAAI,oBAAkB,EAAChC,OAAO,CAACrC,IAAI;QAAA;UAAAsE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACzCxF,OAAA;UACEyF,IAAI,EAAC,QAAQ;UACbC,OAAO,EAAEA,CAAA,KAAMd,oBAAoB,CAACxB,OAAO,CAACV,EAAE,CAAE;UAChDyC,SAAS,EAAC,mBAAmB;UAAAC,QAAA,EAC9B;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAENxF,OAAA;QAAKmF,SAAS,EAAC,iBAAiB;QAAAC,QAAA,EAC7B,CAAC,MAAM;UACN,MAAMgC,OAAO,GAAGtH,wBAAwB,CAACuH,iBAAiB,CAACjE,OAAO,CAACV,EAAE,CAAC;UACtE,MAAM4E,WAAW,GAAGxH,wBAAwB,CAACyH,qBAAqB,CAACnE,OAAO,CAACV,EAAE,CAAC;UAE9E,IAAI0E,OAAO,CAACzD,MAAM,KAAK,CAAC,IAAI2D,WAAW,CAAC3D,MAAM,KAAK,CAAC,EAAE;YACpD,oBACE3D,OAAA;cAAKmF,SAAS,EAAC,eAAe;cAAAC,QAAA,eAC5BpF,OAAA;gBAAAoF,QAAA,EAAG;cAA8C;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClD,CAAC;UAEV;UAEA,oBACExF,OAAA;YAAKmF,SAAS,EAAC,iBAAiB;YAAAC,QAAA,EAC7BkC,WAAW,CAAC3D,MAAM,GAAG,CAAC,iBACrB3D,OAAA;cAAKmF,SAAS,EAAC,qBAAqB;cAAAC,QAAA,gBAClCpF,OAAA;gBAAAoF,QAAA,EAAI;cAAmB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC5BxF,OAAA;gBAAKmF,SAAS,EAAC,kBAAkB;gBAAAC,QAAA,EAC9BkC,WAAW,CAACb,GAAG,CAACe,UAAU,iBACzBxH,OAAA;kBAAyBmF,SAAS,EAAC,iBAAiB;kBAAAC,QAAA,gBAClDpF,OAAA;oBAAKmF,SAAS,EAAC,mBAAmB;oBAAAC,QAAA,gBAChCpF,OAAA;sBAAMmF,SAAS,EAAC,iBAAiB;sBAAAC,QAAA,EAAEoC,UAAU,CAACC;oBAAI;sBAAApC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC,eAC1DxF,OAAA;sBAAMmF,SAAS,EAAE,qBAAqBqC,UAAU,CAACE,gBAAgB,IAAI,CAAC,GAAG,UAAU,GAAG,UAAU,EAAG;sBAAAtC,QAAA,GAChGoC,UAAU,CAACE,gBAAgB,IAAI,CAAC,GAAG,GAAG,GAAG,EAAE,EAAE7C,cAAc,CAAC2C,UAAU,CAACE,gBAAgB,CAAC;oBAAA;sBAAArC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACrF,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC,eACNxF,OAAA;oBAAKmF,SAAS,EAAC,oBAAoB;oBAAAC,QAAA,gBACjCpF,OAAA;sBAAAoF,QAAA,gBAAGpF,OAAA;wBAAAoF,QAAA,EAAQ;sBAAO;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,KAAC,EAACgC,UAAU,CAAC7F,MAAM;oBAAA;sBAAA0D,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACnDxF,OAAA;sBAAAoF,QAAA,gBAAGpF,OAAA;wBAAAoF,QAAA,EAAQ;sBAAiB;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,KAAC,EAACX,cAAc,CAAC2C,UAAU,CAACG,eAAe,CAAC;oBAAA;sBAAAtC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACtFxF,OAAA;sBAAAoF,QAAA,gBAAGpF,OAAA;wBAAAoF,QAAA,EAAQ;sBAAY;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,KAAC,EAACX,cAAc,CAAC2C,UAAU,CAAClG,UAAU,CAAC;oBAAA;sBAAA+D,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eAC5ExF,OAAA;sBAAAoF,QAAA,gBAAGpF,OAAA;wBAAAoF,QAAA,EAAQ;sBAAK;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,KAAC,EAACgC,UAAU,CAAC/B,IAAI;oBAAA;sBAAAJ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5C,CAAC;gBAAA,GAZEgC,UAAU,CAAC9E,EAAE;kBAAA2C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAalB,CACN;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UACN;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAEV,CAAC,EAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC;IAAA,GArDE,WAAWpC,OAAO,CAACV,EAAE,EAAE;MAAA2C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAsD5B,CAET,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAACrF,EAAA,CA1nBWF,kBAAqD;AAAA2H,EAAA,GAArD3H,kBAAqD;AAAA,IAAA2H,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}