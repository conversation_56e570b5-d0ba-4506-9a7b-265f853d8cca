{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.transformReply = exports.transformArguments = exports.IS_READ_ONLY = exports.FIRST_KEY_INDEX = void 0;\nconst generic_transformers_1 = require(\"./generic-transformers\");\nexports.FIRST_KEY_INDEX = 1;\nexports.IS_READ_ONLY = true;\nfunction transformArguments(key, member) {\n  return (0, generic_transformers_1.pushVerdictArguments)(['GEOPOS', key], member);\n}\nexports.transformArguments = transformArguments;\nfunction transformReply(reply) {\n  return reply.map(coordinates => coordinates === null ? null : {\n    longitude: coordinates[0],\n    latitude: coordinates[1]\n  });\n}\nexports.transformReply = transformReply;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "transformReply", "transformArguments", "IS_READ_ONLY", "FIRST_KEY_INDEX", "generic_transformers_1", "require", "key", "member", "pushVerdictArguments", "reply", "map", "coordinates", "longitude", "latitude"], "sources": ["C:/tmsft/node_modules/@redis/client/dist/lib/commands/GEOPOS.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.transformReply = exports.transformArguments = exports.IS_READ_ONLY = exports.FIRST_KEY_INDEX = void 0;\nconst generic_transformers_1 = require(\"./generic-transformers\");\nexports.FIRST_KEY_INDEX = 1;\nexports.IS_READ_ONLY = true;\nfunction transformArguments(key, member) {\n    return (0, generic_transformers_1.pushVerdictArguments)(['GEOPOS', key], member);\n}\nexports.transformArguments = transformArguments;\nfunction transformReply(reply) {\n    return reply.map(coordinates => coordinates === null ? null : {\n        longitude: coordinates[0],\n        latitude: coordinates[1]\n    });\n}\nexports.transformReply = transformReply;\n"], "mappings": "AAAA,YAAY;;AACZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEC,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7DD,OAAO,CAACE,cAAc,GAAGF,OAAO,CAACG,kBAAkB,GAAGH,OAAO,CAACI,YAAY,GAAGJ,OAAO,CAACK,eAAe,GAAG,KAAK,CAAC;AAC7G,MAAMC,sBAAsB,GAAGC,OAAO,CAAC,wBAAwB,CAAC;AAChEP,OAAO,CAACK,eAAe,GAAG,CAAC;AAC3BL,OAAO,CAACI,YAAY,GAAG,IAAI;AAC3B,SAASD,kBAAkBA,CAACK,GAAG,EAAEC,MAAM,EAAE;EACrC,OAAO,CAAC,CAAC,EAAEH,sBAAsB,CAACI,oBAAoB,EAAE,CAAC,QAAQ,EAAEF,GAAG,CAAC,EAAEC,MAAM,CAAC;AACpF;AACAT,OAAO,CAACG,kBAAkB,GAAGA,kBAAkB;AAC/C,SAASD,cAAcA,CAACS,KAAK,EAAE;EAC3B,OAAOA,KAAK,CAACC,GAAG,CAACC,WAAW,IAAIA,WAAW,KAAK,IAAI,GAAG,IAAI,GAAG;IAC1DC,SAAS,EAAED,WAAW,CAAC,CAAC,CAAC;IACzBE,QAAQ,EAAEF,WAAW,CAAC,CAAC;EAC3B,CAAC,CAAC;AACN;AACAb,OAAO,CAACE,cAAc,GAAGA,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}