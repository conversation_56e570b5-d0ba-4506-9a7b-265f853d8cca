{"ast": null, "code": "/**\n * @license\n * Copyright 2018 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { ENGINE } from '../engine';\nimport { Log } from '../kernel_names';\nimport { convertToTensor } from '../tensor_util_env';\nimport { op } from './operation';\n/**\n * Computes natural logarithm of the input `tf.Tensor` element-wise: `ln(x)`\n *\n * ```js\n * const x = tf.tensor1d([1, 2, Math.E]);\n *\n * x.log().print();  // or tf.log(x)\n * ```\n * @param x The input tensor.\n *\n * @doc {heading: 'Operations', subheading: 'Basic math'}\n */\nfunction log_(x) {\n  const $x = convertToTensor(x, 'x', 'log', 'float32');\n  const inputs = {\n    x: $x\n  };\n  return ENGINE.runKernel(Log, inputs);\n}\nexport const log = /* @__PURE__ */op({\n  log_\n});", "map": {"version": 3, "names": ["ENGINE", "Log", "convertToTensor", "op", "log_", "x", "$x", "inputs", "runKernel", "log"], "sources": ["C:\\tfjs-core\\src\\ops\\log.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2018 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {ENGINE} from '../engine';\nimport {Log, LogInputs} from '../kernel_names';\nimport {Tensor} from '../tensor';\nimport {NamedTensorMap} from '../tensor_types';\nimport {convertToTensor} from '../tensor_util_env';\nimport {TensorLike} from '../types';\n\nimport {op} from './operation';\n\n/**\n * Computes natural logarithm of the input `tf.Tensor` element-wise: `ln(x)`\n *\n * ```js\n * const x = tf.tensor1d([1, 2, Math.E]);\n *\n * x.log().print();  // or tf.log(x)\n * ```\n * @param x The input tensor.\n *\n * @doc {heading: 'Operations', subheading: 'Basic math'}\n */\nfunction log_<T extends Tensor>(x: T|TensorLike): T {\n  const $x = convertToTensor(x, 'x', 'log', 'float32');\n\n  const inputs: LogInputs = {x: $x};\n  return ENGINE.runKernel(Log, inputs as unknown as NamedTensorMap);\n}\nexport const log = /* @__PURE__ */ op({log_});\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAiBA,SAAQA,MAAM,QAAO,WAAW;AAChC,SAAQC,GAAG,QAAkB,iBAAiB;AAG9C,SAAQC,eAAe,QAAO,oBAAoB;AAGlD,SAAQC,EAAE,QAAO,aAAa;AAE9B;;;;;;;;;;;;AAYA,SAASC,IAAIA,CAAmBC,CAAe;EAC7C,MAAMC,EAAE,GAAGJ,eAAe,CAACG,CAAC,EAAE,GAAG,EAAE,KAAK,EAAE,SAAS,CAAC;EAEpD,MAAME,MAAM,GAAc;IAACF,CAAC,EAAEC;EAAE,CAAC;EACjC,OAAON,MAAM,CAACQ,SAAS,CAACP,GAAG,EAAEM,MAAmC,CAAC;AACnE;AACA,OAAO,MAAME,GAAG,GAAG,eAAgBN,EAAE,CAAC;EAACC;AAAI,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}