{"ast": null, "code": "/**\n * @license\n * Copyright 2017 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n// Import webgl flags.\nimport './flags_webgl';\nimport { backend_util, buffer, DataStorage, engine, env, kernel_impls, KernelBackend, nextFrame, scalar, tidy, util } from '@tensorflow/tfjs-core';\nimport { getWebGLContext } from './canvas_util';\nimport { DecodeMatrixProgram } from './decode_matrix_gpu';\nimport { DecodeMatrixPackedProgram } from './decode_matrix_packed_gpu';\nimport { EncodeFloatProgram } from './encode_float_gpu';\nimport { EncodeFloatPackedProgram } from './encode_float_packed_gpu';\nimport { EncodeMatrixProgram } from './encode_matrix_gpu';\nimport { EncodeMatrixPackedProgram } from './encode_matrix_packed_gpu';\nimport { GPGPUContext } from './gpgpu_context';\nimport * as gpgpu_math from './gpgpu_math';\nimport { getUniformLocations } from './gpgpu_math';\nimport { simpleAbsImplCPU } from './kernel_utils/shared';\nimport { PackProgram } from './pack_gpu';\nimport { ReshapePackedProgram } from './reshape_packed_gpu';\nimport * as tex_util from './tex_util';\nimport { TextureUsage } from './tex_util';\nimport { TextureManager } from './texture_manager';\nimport * as unary_op from './unaryop_gpu';\nimport { UnaryOpProgram } from './unaryop_gpu';\nimport { UnaryOpPackedProgram } from './unaryop_packed_gpu';\nimport { UnpackProgram } from './unpack_gpu';\nimport * as webgl_util from './webgl_util';\nconst whereImpl = kernel_impls.whereImpl;\nexport const EPSILON_FLOAT32 = 1e-7;\nexport const EPSILON_FLOAT16 = 1e-4;\nconst binaryCaches = {};\nexport function getBinaryCache(webGLVersion) {\n  if (webGLVersion in binaryCaches) {\n    return binaryCaches[webGLVersion];\n  }\n  binaryCaches[webGLVersion] = {};\n  return binaryCaches[webGLVersion];\n}\n// Empirically determined constant used to determine size threshold for handing\n// off execution to the CPU.\nconst CPU_HANDOFF_SIZE_THRESHOLD = env().getNumber('CPU_HANDOFF_SIZE_THRESHOLD');\n// Empirically determined constant used to decide the number of MB on GPU\n// before we warn about high memory use. The MB are this constant * screen area\n// * dpi / 1024 / 1024.\nconst BEFORE_PAGING_CONSTANT = 600;\nfunction numMBBeforeWarning() {\n  if (env().global.screen == null) {\n    return 1024; // 1 GB.\n  }\n  return env().global.screen.height * env().global.screen.width * window.devicePixelRatio * BEFORE_PAGING_CONSTANT / 1024 / 1024;\n}\nclass MathBackendWebGL extends KernelBackend {\n  nextDataId() {\n    return MathBackendWebGL.nextDataId++;\n  }\n  constructor(gpuResource) {\n    super();\n    // Maps data ids that have a pending read operation, to list of subscribers.\n    this.pendingRead = new WeakMap();\n    // List of data ids that are scheduled for disposal, but are waiting on a\n    // pending read operation.\n    this.pendingDisposal = new WeakSet();\n    // Used to count the number of 'shallow' sliced tensors that point to the\n    // same data id.\n    this.dataRefCount = new WeakMap();\n    this.numBytesInGPU = 0;\n    // Accumulated time spent (including blocking) in uploading data to webgl.\n    this.uploadWaitMs = 0;\n    // Accumulated time spent (including blocking in downloading data from webgl.\n    this.downloadWaitMs = 0;\n    // record the last manual GL Flush time.\n    this.lastGlFlushTime = 0;\n    this.warnedAboutMemory = false;\n    this.pendingDeletes = 0;\n    this.disposed = false;\n    if (!env().getBool('HAS_WEBGL')) {\n      throw new Error('WebGL is not supported on this device');\n    }\n    let newGPGPU;\n    if (gpuResource != null) {\n      if (gpuResource instanceof GPGPUContext) {\n        newGPGPU = gpuResource;\n      } else {\n        const gl = getWebGLContext(env().getNumber('WEBGL_VERSION'), gpuResource);\n        newGPGPU = new GPGPUContext(gl);\n      }\n      this.binaryCache = {};\n      this.gpgpuCreatedLocally = false;\n    } else {\n      const gl = getWebGLContext(env().getNumber('WEBGL_VERSION'));\n      newGPGPU = new GPGPUContext(gl);\n      this.binaryCache = getBinaryCache(env().getNumber('WEBGL_VERSION'));\n      this.gpgpuCreatedLocally = true;\n    }\n    this.gpgpu = newGPGPU;\n    this.canvas = this.gpgpu.gl.canvas;\n    this.textureManager = new TextureManager(this.gpgpu);\n    this.numMBBeforeWarning = numMBBeforeWarning();\n    this.texData = new DataStorage(this, engine());\n  }\n  numDataIds() {\n    return this.texData.numDataIds() - this.pendingDeletes;\n  }\n  // Writes a new entry to the data store with a WebGL texture, and registers it\n  // to the texture manager.\n  writeTexture(texture, shape, dtype, texHeight, texWidth, channels) {\n    // Temporarily create an tensor info to make the texture compatible with\n    // the runWebGLProgram's input.\n    const input = this.makeTensorInfo(shape, dtype);\n    const inData = this.texData.get(input.dataId);\n    // Even though the input texture could be unpacked or dense packed, it is\n    // always considered as unpacked for EncodeMatrixProgram.\n    inData.isPacked = false;\n    // Bind texture to the input tensor.\n    inData.texture = {\n      texture,\n      texShape: [texHeight, texWidth]\n    };\n    inData.texShape = [texHeight, texWidth];\n    const shapeAs3D = webgl_util.getShapeAs3D(shape);\n    const program = new EncodeMatrixProgram(shapeAs3D, false /* isByteArray */, channels);\n    const output = this.runWebGLProgram(program, [input], dtype, [[texHeight, texWidth]]);\n    output.shape = shape;\n    // Unbind the texture from the input tensor to avoid the texture being\n    // released.\n    inData.texture = null;\n    this.disposeIntermediateTensorInfo(input);\n    return output.dataId;\n  }\n  write(values, shape, dtype) {\n    if (env().getBool('WEBGL_CHECK_NUMERICAL_PROBLEMS') || env().getBool('DEBUG')) {\n      this.checkNumericalProblems(values);\n    }\n    if (dtype === 'complex64' && values != null) {\n      throw new Error(`Cannot write to a complex64 dtype. ` + `Please use tf.complex(real, imag).`);\n    }\n    const dataId = {\n      id: this.nextDataId()\n    };\n    this.texData.set(dataId, {\n      shape,\n      dtype,\n      values,\n      usage: TextureUsage.UPLOAD,\n      refCount: 1\n    });\n    return dataId;\n  }\n  /** Return refCount of a `TensorData`. */\n  refCount(dataId) {\n    if (this.texData.has(dataId)) {\n      const tensorData = this.texData.get(dataId);\n      return tensorData.refCount;\n    }\n    return 0;\n  }\n  /** Increase refCount of a `TextureData`. */\n  incRef(dataId) {\n    const texData = this.texData.get(dataId);\n    texData.refCount++;\n  }\n  /** Decrease refCount of a `TextureData`. */\n  decRef(dataId) {\n    if (this.texData.has(dataId)) {\n      const texData = this.texData.get(dataId);\n      texData.refCount--;\n    }\n  }\n  move(dataId, values, shape, dtype, refCount) {\n    if (env().getBool('DEBUG')) {\n      this.checkNumericalProblems(values);\n    }\n    if (dtype === 'complex64') {\n      throw new Error(`Cannot write to a complex64 dtype. ` + `Please use tf.complex(real, imag).`);\n    }\n    this.texData.set(dataId, {\n      shape,\n      dtype,\n      values,\n      usage: TextureUsage.UPLOAD,\n      refCount\n    });\n  }\n  disposeIntermediateTensorInfo(tensorInfo) {\n    this.disposeData(tensorInfo.dataId);\n  }\n  readSync(dataId) {\n    const texData = this.texData.get(dataId);\n    const {\n      values,\n      dtype,\n      complexTensorInfos,\n      slice,\n      shape,\n      isPacked\n    } = texData;\n    // The presence of `slice` indicates this tensor is a shallow slice of a\n    // different tensor, and is using that original tensor's texture. Run\n    // `clone` in order to copy that texture and read from it.\n    if (slice != null) {\n      let program;\n      if (isPacked) {\n        program = new UnaryOpPackedProgram(shape, unary_op.CLONE);\n      } else {\n        program = new UnaryOpProgram(shape, unary_op.CLONE);\n      }\n      const res = this.runWebGLProgram(program, [{\n        dataId,\n        shape,\n        dtype\n      }], dtype);\n      const data = this.readSync(res.dataId);\n      this.disposeIntermediateTensorInfo(res);\n      return data;\n    }\n    if (values != null) {\n      return this.convertAndCacheOnCPU(dataId);\n    }\n    if (dtype === 'string') {\n      return values;\n    }\n    const shouldTimeProgram = this.activeTimers != null;\n    let start;\n    if (shouldTimeProgram) {\n      start = util.now();\n    }\n    let result;\n    if (dtype === 'complex64') {\n      const realValues = this.readSync(complexTensorInfos.real.dataId);\n      const imagValues = this.readSync(complexTensorInfos.imag.dataId);\n      result = backend_util.mergeRealAndImagArrays(realValues, imagValues);\n    } else {\n      result = this.getValuesFromTexture(dataId);\n    }\n    if (shouldTimeProgram) {\n      this.downloadWaitMs += util.now() - start;\n    }\n    return this.convertAndCacheOnCPU(dataId, result);\n  }\n  async read(dataId) {\n    if (this.pendingRead.has(dataId)) {\n      const subscribers = this.pendingRead.get(dataId);\n      return new Promise(resolve => subscribers.push(resolve));\n    }\n    const texData = this.texData.get(dataId);\n    const {\n      values,\n      shape,\n      slice,\n      dtype,\n      complexTensorInfos,\n      isPacked\n    } = texData;\n    // The presence of `slice` indicates this tensor is a shallow slice of a\n    // different tensor, and is using that original tensor's texture. Run\n    // `clone` in order to copy that texture and read from it.\n    if (slice != null) {\n      let program;\n      if (isPacked) {\n        program = new UnaryOpPackedProgram(shape, unary_op.CLONE);\n      } else {\n        program = new UnaryOpProgram(shape, unary_op.CLONE);\n      }\n      const res = this.runWebGLProgram(program, [{\n        dataId,\n        shape,\n        dtype\n      }], dtype);\n      const data = this.read(res.dataId);\n      this.disposeIntermediateTensorInfo(res);\n      return data;\n    }\n    if (values != null) {\n      return this.convertAndCacheOnCPU(dataId);\n    }\n    if (env().getBool('DEBUG')) {\n      // getBool('WEBGL_DOWNLOAD_FLOAT_ENABLED') caused a blocking GPU call.\n      // For performance reason, only check it for debugging. In production,\n      // it doesn't handle this use case anyway, so behavior is not changed.\n      if (!env().getBool('WEBGL_DOWNLOAD_FLOAT_ENABLED') && env().getNumber('WEBGL_VERSION') === 2) {\n        throw new Error(`tensor.data() with WEBGL_DOWNLOAD_FLOAT_ENABLED=false and ` + `WEBGL_VERSION=2 not yet supported.`);\n      }\n    }\n    let buffer = null;\n    let tmpDownloadTarget;\n    if (dtype !== 'complex64' && env().get('WEBGL_BUFFER_SUPPORTED')) {\n      // Possibly copy the texture into a buffer before inserting a fence.\n      tmpDownloadTarget = this.decode(dataId);\n      const tmpData = this.texData.get(tmpDownloadTarget.dataId);\n      buffer = this.gpgpu.createBufferFromTexture(tmpData.texture.texture, ...tex_util.getDenseTexShape(shape));\n    }\n    this.pendingRead.set(dataId, []);\n    if (dtype !== 'complex64') {\n      // Create a fence and wait for it to resolve.\n      await this.gpgpu.createAndWaitForFence();\n    }\n    // Download the values from the GPU.\n    let vals;\n    if (dtype === 'complex64') {\n      const ps = await Promise.all([this.read(complexTensorInfos.real.dataId), this.read(complexTensorInfos.imag.dataId)]);\n      const realValues = ps[0];\n      const imagValues = ps[1];\n      vals = backend_util.mergeRealAndImagArrays(realValues, imagValues);\n    } else if (buffer == null) {\n      vals = this.getValuesFromTexture(dataId);\n    } else {\n      const size = util.sizeFromShape(shape);\n      vals = this.gpgpu.downloadFloat32MatrixFromBuffer(buffer, size);\n    }\n    if (tmpDownloadTarget != null) {\n      this.disposeIntermediateTensorInfo(tmpDownloadTarget);\n    }\n    if (buffer != null) {\n      const gl = this.gpgpu.gl;\n      webgl_util.callAndCheck(gl, () => gl.deleteBuffer(buffer));\n    }\n    const dTypeVals = this.convertAndCacheOnCPU(dataId, vals);\n    const subscribers = this.pendingRead.get(dataId);\n    this.pendingRead.delete(dataId);\n    // Notify all pending reads.\n    subscribers.forEach(resolve => resolve(dTypeVals));\n    if (this.pendingDisposal.has(dataId)) {\n      this.pendingDisposal.delete(dataId);\n      if (this.disposeData(dataId)) {\n        engine().removeDataId(dataId, this);\n      }\n      this.pendingDeletes--;\n    }\n    return dTypeVals;\n  }\n  /**\n   * Read tensor to a new texture that is densely packed for ease of use.\n   * @param dataId The source tensor.\n   * @param options\n   *     customTexShape: Optional. If set, will use the user defined texture\n   *     shape to create the texture.\n   */\n  readToGPU(dataId, options = {}) {\n    const texData = this.texData.get(dataId);\n    const {\n      values,\n      shape,\n      slice,\n      dtype,\n      isPacked,\n      texture\n    } = texData;\n    if (dtype === 'complex64') {\n      throw new Error('Does not support reading texture for complex64 dtype.');\n    }\n    // The presence of `slice` indicates this tensor is a shallow slice of a\n    // different tensor, and is using that original tensor's texture. Run\n    // `clone` in order to copy that texture and read from it.\n    if (slice != null) {\n      let program;\n      if (isPacked) {\n        program = new UnaryOpPackedProgram(shape, unary_op.CLONE);\n      } else {\n        program = new UnaryOpProgram(shape, unary_op.CLONE);\n      }\n      const res = this.runWebGLProgram(program, [{\n        dataId,\n        shape,\n        dtype\n      }], dtype);\n      const gpuResouorce = this.readToGPU(res, options);\n      this.disposeIntermediateTensorInfo(res);\n      return gpuResouorce;\n    }\n    if (texture == null) {\n      if (values != null) {\n        throw new Error('Data is not on GPU but on CPU.');\n      } else {\n        throw new Error('There is no data on GPU or CPU.');\n      }\n    }\n    // Decode the texture so that it is stored densely (using four channels).\n    const tmpTarget = this.decode(dataId, options.customTexShape);\n    // Make engine track this tensor, so that we can dispose it later.\n    const tensorRef = engine().makeTensorFromTensorInfo(tmpTarget);\n    const tmpData = this.texData.get(tmpTarget.dataId);\n    return Object.assign({\n      tensorRef\n    }, tmpData.texture);\n  }\n  bufferSync(t) {\n    const data = this.readSync(t.dataId);\n    if (t.dtype === 'string') {\n      try {\n        // Decode the bytes into string.\n        const strings = data.map(d => util.decodeString(d));\n        return buffer(t.shape, t.dtype, strings);\n      } catch (_a) {\n        throw new Error('Failed to decode encoded string bytes into utf-8');\n      }\n    }\n    return buffer(t.shape, t.dtype, data);\n  }\n  checkNumericalProblems(values) {\n    if (values == null) {\n      return;\n    }\n    for (let i = 0; i < values.length; i++) {\n      const num = values[i];\n      if (!webgl_util.canBeRepresented(num)) {\n        if (env().getBool('WEBGL_RENDER_FLOAT32_CAPABLE')) {\n          throw Error(`The value ${num} cannot be represented with your ` + `current settings. Consider enabling float32 rendering: ` + `'tf.env().set('WEBGL_RENDER_FLOAT32_ENABLED', true);'`);\n        }\n        throw Error(`The value ${num} cannot be represented on this device.`);\n      }\n    }\n  }\n  getValuesFromTexture(dataId) {\n    const {\n      shape,\n      dtype,\n      isPacked\n    } = this.texData.get(dataId);\n    const size = util.sizeFromShape(shape);\n    if (env().getBool('WEBGL_DOWNLOAD_FLOAT_ENABLED')) {\n      const tmpTarget = this.decode(dataId);\n      const tmpData = this.texData.get(tmpTarget.dataId);\n      const vals = this.gpgpu.downloadMatrixFromPackedTexture(tmpData.texture.texture, ...tex_util.getDenseTexShape(shape)).subarray(0, size);\n      this.disposeIntermediateTensorInfo(tmpTarget);\n      return vals;\n    }\n    const shouldUsePackedProgram = env().getBool('WEBGL_PACK') && isPacked === true;\n    const outputShape = shouldUsePackedProgram ? webgl_util.getShapeAs3D(shape) : shape;\n    const program = shouldUsePackedProgram ? new EncodeFloatPackedProgram(outputShape) : new EncodeFloatProgram(outputShape);\n    const output = this.runWebGLProgram(program, [{\n      shape: outputShape,\n      dtype,\n      dataId\n    }], 'float32');\n    const tmpData = this.texData.get(output.dataId);\n    const vals = this.gpgpu.downloadByteEncodedFloatMatrixFromOutputTexture(tmpData.texture.texture, tmpData.texShape[0], tmpData.texShape[1]).subarray(0, size);\n    this.disposeIntermediateTensorInfo(output);\n    return vals;\n  }\n  timerAvailable() {\n    return env().getNumber('WEBGL_DISJOINT_QUERY_TIMER_EXTENSION_RELIABLE') > 0;\n  }\n  time(f) {\n    const oldActiveTimers = this.activeTimers;\n    const newActiveTimers = [];\n    let outerMostTime = false;\n    if (this.programTimersStack == null) {\n      this.programTimersStack = newActiveTimers;\n      outerMostTime = true;\n    } else {\n      this.activeTimers.push(newActiveTimers);\n    }\n    this.activeTimers = newActiveTimers;\n    f();\n    // needing to split these up because util.flatten only accepts certain types\n    const flattenedActiveTimerQueries = util.flatten(this.activeTimers.map(d => d.query)).filter(d => d != null);\n    const flattenedActiveTimerNames = util.flatten(this.activeTimers.map(d => d.name)).filter(d => d != null);\n    this.activeTimers = oldActiveTimers;\n    if (outerMostTime) {\n      this.programTimersStack = null;\n    }\n    const res = {\n      uploadWaitMs: this.uploadWaitMs,\n      downloadWaitMs: this.downloadWaitMs,\n      kernelMs: null,\n      wallMs: null // will be filled by the engine\n    };\n    return (async () => {\n      if (env().getNumber('WEBGL_DISJOINT_QUERY_TIMER_EXTENSION_RELIABLE') > 0) {\n        const kernelMs = await Promise.all(flattenedActiveTimerQueries);\n        res['kernelMs'] = util.sum(kernelMs);\n        res['getExtraProfileInfo'] = () => kernelMs.map((d, i) => ({\n          name: flattenedActiveTimerNames[i],\n          ms: d\n        })).map(d => `${d.name}: ${d.ms}`).join(', ');\n      } else {\n        res['kernelMs'] = {\n          error: 'WebGL query timers are not supported in this environment.'\n        };\n      }\n      this.uploadWaitMs = 0;\n      this.downloadWaitMs = 0;\n      return res;\n    })();\n  }\n  memory() {\n    return {\n      unreliable: false,\n      numBytesInGPU: this.numBytesInGPU,\n      numBytesInGPUAllocated: this.textureManager.numBytesAllocated,\n      numBytesInGPUFree: this.textureManager.numBytesFree\n    };\n  }\n  startTimer() {\n    if (env().getNumber('WEBGL_DISJOINT_QUERY_TIMER_EXTENSION_RELIABLE') > 0) {\n      return this.gpgpu.beginQuery();\n    }\n    return {\n      startMs: util.now(),\n      endMs: null\n    };\n  }\n  endTimer(query) {\n    if (env().getNumber('WEBGL_DISJOINT_QUERY_TIMER_EXTENSION_RELIABLE') > 0) {\n      this.gpgpu.endQuery();\n      return query;\n    }\n    query.endMs = util.now();\n    return query;\n  }\n  async getQueryTime(query) {\n    if (env().getNumber('WEBGL_DISJOINT_QUERY_TIMER_EXTENSION_RELIABLE') > 0) {\n      return this.gpgpu.waitForQueryAndGetTime(query);\n    }\n    const timerQuery = query;\n    return timerQuery.endMs - timerQuery.startMs;\n  }\n  /**\n   * Decrease the RefCount on the dataId and dispose the memory if the dataId\n   * has 0 refCount. If there are pending read on the data, the disposal would\n   * added to the pending delete queue. Return true if the dataId is removed\n   * from backend or the backend does not contain the dataId, false if the\n   * dataId is not removed. Memory may or may not be released even when dataId\n   * is removed, which also depends on dataRefCount, see `releaseGPU`.\n   * @param dataId\n   * @oaram force Optional, remove the data regardless of refCount\n   */\n  disposeData(dataId, force = false) {\n    if (this.pendingDisposal.has(dataId)) {\n      return false;\n    }\n    // No-op if already disposed.\n    if (!this.texData.has(dataId)) {\n      return true;\n    }\n    // if force flag is set, change refCount to 0, this would ensure disposal\n    // when added to the pendingDisposal queue. Memory may or may not be\n    // released, which also depends on dataRefCount, see `releaseGPU`.\n    if (force) {\n      this.texData.get(dataId).refCount = 0;\n    } else {\n      this.texData.get(dataId).refCount--;\n    }\n    if (!force && this.texData.get(dataId).refCount > 0) {\n      return false;\n    }\n    if (this.pendingRead.has(dataId)) {\n      this.pendingDisposal.add(dataId);\n      this.pendingDeletes++;\n      return false;\n    }\n    this.releaseGPUData(dataId);\n    const {\n      complexTensorInfos\n    } = this.texData.get(dataId);\n    if (complexTensorInfos != null) {\n      this.disposeData(complexTensorInfos.real.dataId, force);\n      this.disposeData(complexTensorInfos.imag.dataId, force);\n    }\n    this.texData.delete(dataId);\n    return true;\n  }\n  releaseGPUData(dataId) {\n    const {\n      texture,\n      dtype,\n      texShape,\n      usage,\n      isPacked,\n      slice\n    } = this.texData.get(dataId);\n    const key = slice && slice.origDataId || dataId;\n    const refCount = this.dataRefCount.get(key);\n    if (refCount > 1) {\n      this.dataRefCount.set(key, refCount - 1);\n    } else {\n      this.dataRefCount.delete(key);\n      if (texture != null) {\n        this.numBytesInGPU -= this.computeBytes(texShape, dtype);\n        this.textureManager.releaseTexture(texture, texShape, usage, isPacked);\n      }\n    }\n    const texData = this.texData.get(dataId);\n    texData.texture = null;\n    texData.texShape = null;\n    texData.isPacked = false;\n    texData.slice = null;\n  }\n  getTexture(dataId) {\n    this.uploadToGPU(dataId);\n    return this.texData.get(dataId).texture.texture;\n  }\n  /**\n   * Returns internal information for the specific data bucket. Used in unit\n   * tests.\n   */\n  getDataInfo(dataId) {\n    return this.texData.get(dataId);\n  }\n  /*\n  Tests whether all the inputs to an op are small and on the CPU. This heuristic\n  determines when it would be faster to execute a kernel on the CPU. WebGL\n  kernels opt into running this check and forwarding when appropriate.\n  TODO(https://github.com/tensorflow/tfjs/issues/872): Develop a more\n  sustainable strategy for optimizing backend execution of ops.\n   */\n  shouldExecuteOnCPU(inputs, sizeThreshold = CPU_HANDOFF_SIZE_THRESHOLD) {\n    return env().getBool('WEBGL_CPU_FORWARD') && inputs.every(input => this.texData.get(input.dataId).texture == null && util.sizeFromShape(input.shape) < sizeThreshold);\n  }\n  getGPGPUContext() {\n    return this.gpgpu;\n  }\n  where(condition) {\n    backend_util.warn('tf.where() in webgl locks the UI thread. ' + 'Call tf.whereAsync() instead');\n    const condVals = condition.dataSync();\n    return whereImpl(condition.shape, condVals);\n  }\n  packedUnaryOp(x, op, dtype) {\n    const program = new UnaryOpPackedProgram(x.shape, op);\n    const outInfo = this.compileAndRun(program, [x], dtype);\n    return engine().makeTensorFromTensorInfo(outInfo);\n  }\n  // TODO(msoulanille) remove this once the backend has been modularized\n  // a copy is needed here to break a circular dependency.\n  // Also remove the op from unary_op.\n  abs(x) {\n    // TODO: handle cases when x is complex.\n    if (this.shouldExecuteOnCPU([x]) && x.dtype !== 'complex64') {\n      const outValues = simpleAbsImplCPU(this.texData.get(x.dataId).values);\n      return this.makeOutput(x.shape, x.dtype, outValues);\n    }\n    if (env().getBool('WEBGL_PACK_UNARY_OPERATIONS')) {\n      return this.packedUnaryOp(x, unary_op.ABS, x.dtype);\n    }\n    const program = new UnaryOpProgram(x.shape, unary_op.ABS);\n    const outInfo = this.compileAndRun(program, [x]);\n    return engine().makeTensorFromTensorInfo(outInfo);\n  }\n  makeTensorInfo(shape, dtype, values) {\n    let dataId;\n    if (dtype === 'string' && values != null && values.length > 0 && util.isString(values[0])) {\n      const encodedValues = values.map(d => util.encodeString(d));\n      dataId = this.write(encodedValues, shape, dtype);\n    } else {\n      dataId = this.write(values, shape, dtype);\n    }\n    this.texData.get(dataId).usage = null;\n    return {\n      dataId,\n      shape,\n      dtype\n    };\n  }\n  makeOutput(shape, dtype, values) {\n    return engine().makeTensorFromTensorInfo(this.makeTensorInfo(shape, dtype, values), this);\n  }\n  unpackTensor(input) {\n    const program = new UnpackProgram(input.shape);\n    return this.runWebGLProgram(program, [input], input.dtype);\n  }\n  packTensor(input) {\n    const program = new PackProgram(input.shape);\n    const preventEagerUnpackingOutput = true;\n    return this.runWebGLProgram(program, [input], input.dtype, null /* customUniformValues */, preventEagerUnpackingOutput);\n  }\n  packedReshape(input, afterShape) {\n    const input3DShape = [webgl_util.getBatchDim(input.shape), ...webgl_util.getRowsCols(input.shape)];\n    const input3D = {\n      dtype: input.dtype,\n      shape: input3DShape,\n      dataId: input.dataId\n    };\n    const afterShapeAs3D = [webgl_util.getBatchDim(afterShape), ...webgl_util.getRowsCols(afterShape)];\n    const program = new ReshapePackedProgram(afterShapeAs3D, input3DShape);\n    const preventEagerUnpackingOfOutput = true;\n    const customValues = [input3DShape];\n    const output = this.runWebGLProgram(program, [input3D], input.dtype, customValues, preventEagerUnpackingOfOutput);\n    return {\n      dataId: output.dataId,\n      shape: afterShape,\n      dtype: output.dtype\n    };\n  }\n  decode(dataId, customTexShape) {\n    const texData = this.texData.get(dataId);\n    const {\n      isPacked,\n      shape,\n      dtype\n    } = texData;\n    if (customTexShape != null) {\n      const size = util.sizeFromShape(shape);\n      const texSize = customTexShape[0] * customTexShape[1] * 4;\n      util.assert(size <= texSize, () => 'customTexShape is too small. ' + 'Row * Column * 4 should be equal or larger than the ' + 'size of the tensor data.');\n    }\n    const shapeAs3D = webgl_util.getShapeAs3D(shape);\n    let program;\n    if (isPacked) {\n      program = new DecodeMatrixPackedProgram(shapeAs3D);\n    } else {\n      program = new DecodeMatrixProgram(shapeAs3D);\n    }\n    const preventEagerUnpackingOfOutput = true;\n    const customValues = [customTexShape != null ? customTexShape : tex_util.getDenseTexShape(shapeAs3D)];\n    const out = this.runWebGLProgram(program, [{\n      shape: shapeAs3D,\n      dtype,\n      dataId\n    }], dtype, customValues, preventEagerUnpackingOfOutput, customTexShape);\n    return {\n      dtype,\n      shape,\n      dataId: out.dataId\n    };\n  }\n  runWebGLProgram(program, inputs, outputDtype, customUniformValues, preventEagerUnpackingOfOutput = false, customTexShape) {\n    const output = this.makeTensorInfo(program.outputShape, outputDtype);\n    const outData = this.texData.get(output.dataId);\n    if (program.packedOutput) {\n      outData.isPacked = true;\n    }\n    if (program.outPackingScheme === tex_util.PackingScheme.DENSE) {\n      const texelShape = customTexShape != null ? customTexShape : tex_util.getDenseTexShape(program.outputShape);\n      // For a densely packed output, we explicitly set texShape\n      // so it doesn't get assigned later according to our typical packing\n      // scheme wherein a single texel can only contain values from adjacent\n      // rows/cols.\n      outData.texShape = texelShape.map(d => d * 2);\n    }\n    if (program.outTexUsage != null) {\n      outData.usage = program.outTexUsage;\n    }\n    if (util.sizeFromShape(output.shape) === 0) {\n      // Short-circuit the computation since the result is empty (has 0 in its\n      // shape).\n      outData.values = util.getTypedArrayFromDType(output.dtype, 0);\n      return output;\n    }\n    const dataToDispose = [];\n    const inputsData = inputs.map(input => {\n      if (input.dtype === 'complex64') {\n        throw new Error(`GPGPUProgram does not support complex64 input. For complex64 ` + `dtypes, please separate the program into real and imaginary ` + `parts.`);\n      }\n      let texData = this.texData.get(input.dataId);\n      if (texData.texture == null) {\n        if (!program.packedInputs && util.sizeFromShape(input.shape) <= env().getNumber('WEBGL_SIZE_UPLOAD_UNIFORM')) {\n          // Upload small tensors that live on the CPU as uniforms, not as\n          // textures. Do this only when the environment supports 32bit floats\n          // due to problems when comparing 16bit floats with 32bit floats.\n          // TODO(https://github.com/tensorflow/tfjs/issues/821): Make it\n          // possible for packed shaders to sample from uniforms.\n          return {\n            shape: input.shape,\n            texData: null,\n            isUniform: true,\n            uniformValues: texData.values\n          };\n        }\n        // This ensures that if a packed program's inputs have not yet been\n        // uploaded to the GPU, they get uploaded as packed right off the bat.\n        if (program.packedInputs) {\n          texData.isPacked = true;\n          texData.shape = input.shape;\n        }\n      }\n      this.uploadToGPU(input.dataId);\n      if (!!texData.isPacked !== !!program.packedInputs) {\n        input = texData.isPacked ? this.unpackTensor(input) : this.packTensor(input);\n        dataToDispose.push(input);\n        texData = this.texData.get(input.dataId);\n      } else if (texData.isPacked && !webgl_util.isReshapeFree(texData.shape, input.shape)) {\n        // This is a special case where a texture exists for a tensor\n        // but the shapes are incompatible (due to packing constraints) because\n        // the tensor did not have a chance to go through the packed reshape\n        // shader. This only happens when we reshape the *same* tensor to form\n        // *distinct* inputs to an op, e.g. dotting a vector with itself. This\n        // case will disappear once packed uploading is the default.\n        const savedInput = input;\n        const targetShape = input.shape;\n        input.shape = texData.shape;\n        input = this.packedReshape(input, targetShape);\n        dataToDispose.push(input);\n        texData = this.texData.get(input.dataId);\n        savedInput.shape = targetShape;\n      }\n      return {\n        shape: input.shape,\n        texData,\n        isUniform: false\n      };\n    });\n    this.uploadToGPU(output.dataId);\n    const outputData = {\n      shape: output.shape,\n      texData: outData,\n      isUniform: false\n    };\n    const key = gpgpu_math.makeShaderKey(program, inputsData, outputData);\n    const binary = this.getAndSaveBinary(key, () => {\n      return gpgpu_math.compileProgram(this.gpgpu, program, inputsData, outputData);\n    });\n    const shouldTimeProgram = this.activeTimers != null;\n    let query;\n    if (shouldTimeProgram) {\n      query = this.startTimer();\n    }\n    if (!env().get('ENGINE_COMPILE_ONLY')) {\n      gpgpu_math.runProgram(this.gpgpu, binary, inputsData, outputData, customUniformValues);\n    }\n    dataToDispose.forEach(info => this.disposeIntermediateTensorInfo(info));\n    if (shouldTimeProgram) {\n      query = this.endTimer(query);\n      this.activeTimers.push({\n        name: program.constructor.name,\n        query: this.getQueryTime(query)\n      });\n    }\n    const glFlushThreshold = env().getNumber('WEBGL_FLUSH_THRESHOLD');\n    // Manually GL flush requested\n    if (glFlushThreshold > 0) {\n      const time = util.now();\n      if (time - this.lastGlFlushTime > glFlushThreshold) {\n        this.gpgpu.gl.flush();\n        this.lastGlFlushTime = time;\n      }\n    }\n    if (!env().getBool('WEBGL_LAZILY_UNPACK') && outData.isPacked && preventEagerUnpackingOfOutput === false) {\n      const unpacked = this.unpackTensor(output);\n      this.disposeIntermediateTensorInfo(output);\n      return unpacked;\n    }\n    return output;\n  }\n  compileAndRun(program, inputs, outputDtype, customUniformValues, preventEagerUnpackingOfOutput = false) {\n    outputDtype = outputDtype || inputs[0].dtype;\n    const outInfo = this.runWebGLProgram(program, inputs, outputDtype, customUniformValues, preventEagerUnpackingOfOutput);\n    return outInfo;\n  }\n  getAndSaveBinary(key, getBinary) {\n    if (!(key in this.binaryCache)) {\n      this.binaryCache[key] = getBinary();\n    }\n    return this.binaryCache[key];\n  }\n  getTextureManager() {\n    return this.textureManager;\n  }\n  dispose() {\n    if (this.disposed) {\n      return;\n    }\n    // Avoid disposing the compiled webgl programs during unit testing because\n    // it slows down test execution.\n    if (!env().getBool('IS_TEST')) {\n      const allKeys = Object.keys(this.binaryCache);\n      allKeys.forEach(key => {\n        this.gpgpu.deleteProgram(this.binaryCache[key].webGLProgram);\n        delete this.binaryCache[key];\n      });\n    }\n    this.textureManager.dispose();\n    if (this.canvas != null && typeof HTMLCanvasElement !== 'undefined' && this.canvas instanceof HTMLCanvasElement) {\n      this.canvas.remove();\n    } else {\n      this.canvas = null;\n    }\n    if (this.gpgpuCreatedLocally) {\n      this.gpgpu.program = null;\n      this.gpgpu.dispose();\n    }\n    this.disposed = true;\n  }\n  floatPrecision() {\n    if (this.floatPrecisionValue == null) {\n      this.floatPrecisionValue = tidy(() => {\n        if (!env().get('WEBGL_RENDER_FLOAT32_ENABLED')) {\n          // Momentarily switching DEBUG flag to false so we don't throw an\n          // error trying to upload a small value.\n          const debugFlag = env().getBool('DEBUG');\n          env().set('DEBUG', false);\n          const underflowCheckValue = this.abs(scalar(1e-8)).dataSync()[0];\n          env().set('DEBUG', debugFlag);\n          if (underflowCheckValue > 0) {\n            return 32;\n          }\n        }\n        return 16;\n      });\n    }\n    return this.floatPrecisionValue;\n  }\n  /** Returns the smallest representable number.  */\n  epsilon() {\n    return this.floatPrecision() === 32 ? EPSILON_FLOAT32 : EPSILON_FLOAT16;\n  }\n  uploadToGPU(dataId) {\n    const texData = this.texData.get(dataId);\n    const {\n      shape,\n      dtype,\n      values,\n      texture,\n      usage,\n      isPacked\n    } = texData;\n    if (texture != null) {\n      // Array is already on GPU. No-op.\n      return;\n    }\n    const shouldTimeProgram = this.activeTimers != null;\n    let start;\n    if (shouldTimeProgram) {\n      start = util.now();\n    }\n    let texShape = texData.texShape;\n    if (texShape == null) {\n      // This texShape may not be the final texture shape. For packed or dense\n      // textures, the texShape will be changed when textures are created.\n      texShape = webgl_util.getTextureShapeFromLogicalShape(shape, isPacked);\n      texData.texShape = texShape;\n    }\n    if (values != null) {\n      const shapeAs3D = webgl_util.getShapeAs3D(shape);\n      let program;\n      let width = texShape[1],\n        height = texShape[0];\n      const isByteArray = values instanceof Uint8Array || values instanceof Uint8ClampedArray;\n      // texture for float array is PhysicalTextureType.PACKED_2X2_FLOAT32, we\n      // need to make sure the upload uses the same packed size\n      if (isPacked || !isByteArray) {\n        [width, height] = tex_util.getPackedMatrixTextureShapeWidthHeight(texShape[0], texShape[1]);\n      }\n      if (isPacked) {\n        program = new EncodeMatrixPackedProgram(shapeAs3D, isByteArray);\n      } else {\n        program = new EncodeMatrixProgram(shapeAs3D, isByteArray);\n      }\n      // TexShape for float array needs to be the original shape, which byte\n      // array needs to be packed size. This allow the data upload shape to be\n      // matched with texture creation logic.\n      const tempDenseInputTexShape = isByteArray ? [height, width] : texShape;\n      const tempDenseInputHandle = this.makeTensorInfo(tempDenseInputTexShape, dtype);\n      const tempDenseInputTexData = this.texData.get(tempDenseInputHandle.dataId);\n      if (isByteArray) {\n        tempDenseInputTexData.usage = TextureUsage.PIXELS;\n      } else {\n        tempDenseInputTexData.usage = TextureUsage.UPLOAD;\n      }\n      tempDenseInputTexData.texShape = tempDenseInputTexShape;\n      this.gpgpu.uploadDenseMatrixToTexture(this.getTexture(tempDenseInputHandle.dataId), width, height, values);\n      const customValues = [[height, width]];\n      // We want the output to remain packed regardless of the value of\n      // WEBGL_PACK.\n      const preventEagerUnpacking = true;\n      const encodedOutputTarget = this.runWebGLProgram(program, [tempDenseInputHandle], dtype, customValues, preventEagerUnpacking);\n      // Have the original texture assume the identity of the encoded output.\n      const outputTexData = this.texData.get(encodedOutputTarget.dataId);\n      texData.texShape = outputTexData.texShape;\n      texData.isPacked = outputTexData.isPacked;\n      texData.usage = outputTexData.usage;\n      if (!env().get('ENGINE_COMPILE_ONLY')) {\n        texData.texture = outputTexData.texture;\n        // Once uploaded, don't store the values on cpu.\n        texData.values = null;\n        this.texData.delete(encodedOutputTarget.dataId);\n      } else {\n        this.disposeData(encodedOutputTarget.dataId);\n      }\n      this.disposeIntermediateTensorInfo(tempDenseInputHandle);\n      if (shouldTimeProgram) {\n        this.uploadWaitMs += util.now() - start;\n      }\n    } else {\n      const newTexture = this.acquireTexture(texShape, usage, dtype, isPacked);\n      texData.texture = newTexture;\n    }\n  }\n  convertAndCacheOnCPU(dataId, float32Values) {\n    const texData = this.texData.get(dataId);\n    const {\n      dtype\n    } = texData;\n    if (float32Values != null) {\n      texData.values = float32ToTypedArray(float32Values, dtype);\n    }\n    return texData.values;\n  }\n  acquireTexture(texShape, texType, dtype, isPacked) {\n    this.numBytesInGPU += this.computeBytes(texShape, dtype);\n    if (!this.warnedAboutMemory && this.numBytesInGPU > this.numMBBeforeWarning * 1024 * 1024) {\n      const mb = (this.numBytesInGPU / 1024 / 1024).toFixed(2);\n      this.warnedAboutMemory = true;\n      console.warn(`High memory usage in GPU: ${mb} MB, ` + `most likely due to a memory leak`);\n    }\n    return this.textureManager.acquireTexture(texShape, texType, isPacked);\n  }\n  computeBytes(shape, dtype) {\n    return shape[0] * shape[1] * util.bytesPerElement(dtype);\n  }\n  checkCompileCompletion() {\n    for (const [, binary] of Object.entries(this.binaryCache)) {\n      this.checkCompletion_(binary);\n    }\n  }\n  async checkCompileCompletionAsync() {\n    const ps = [];\n    if (this.gpgpu.parallelCompilationExtension) {\n      for (const [, binary] of Object.entries(this.binaryCache)) {\n        ps.push(this.checkCompletionAsync_(binary));\n      }\n      return Promise.all(ps);\n    } else {\n      for (const [, binary] of Object.entries(this.binaryCache)) {\n        const p = new Promise(resolve => {\n          try {\n            this.checkCompletion_(binary);\n            resolve(true);\n          } catch (error) {\n            throw error;\n          }\n        });\n        ps.push(p);\n      }\n      return Promise.all(ps);\n    }\n  }\n  async checkCompletionAsync_(binary) {\n    if (this.gpgpu.gl.getProgramParameter(binary.webGLProgram, this.gpgpu.parallelCompilationExtension.COMPLETION_STATUS_KHR)) {\n      return this.checkCompletion_(binary);\n    } else {\n      await nextFrame();\n      return this.checkCompletionAsync_(binary);\n    }\n  }\n  checkCompletion_(binary) {\n    if (this.gpgpu.gl.getProgramParameter(binary.webGLProgram, this.gpgpu.gl.LINK_STATUS) === false) {\n      console.log(this.gpgpu.gl.getProgramInfoLog(binary.webGLProgram));\n      if (this.gpgpu.gl.getShaderParameter(binary.fragmentShader, this.gpgpu.gl.COMPILE_STATUS) === false) {\n        webgl_util.logShaderSourceAndInfoLog(binary.source, this.gpgpu.gl.getShaderInfoLog(binary.fragmentShader));\n        throw new Error('Failed to compile fragment shader.');\n      }\n      throw new Error('Failed to link vertex and fragment shaders.');\n    }\n    return true;\n  }\n  getUniformLocations() {\n    for (const binary of Object.values(this.binaryCache)) {\n      // TODO: Iterating through all binaries to build VAOs is supposed to be in\n      // a seperate function, like 'setVaos'. However, to avoid breaking changes\n      // for the users using parallel compile feature now, buildVao is silently\n      // added here.\n      this.gpgpu.buildVao(binary.webGLProgram);\n      const {\n        variablesLocations,\n        customUniformLocations,\n        infLoc,\n        nanLoc,\n        outShapeLocation,\n        outShapeStridesLocation,\n        outTexShapeLocation\n      } = getUniformLocations(this.gpgpu, binary.program, binary.webGLProgram);\n      binary.variablesLocations = variablesLocations;\n      binary.customUniformLocations = customUniformLocations;\n      binary.infLoc = infLoc;\n      binary.nanLoc = nanLoc;\n      binary.outShapeLocation = outShapeLocation;\n      binary.outShapeStridesLocation = outShapeStridesLocation;\n      binary.outTexShapeLocation = outTexShapeLocation;\n    }\n  }\n  /**\n   * Create a TF.js tensor out of an existing WebGL texture. A new texture will\n   * be created.\n   */\n  createTensorFromGPUData(values, shape, dtype) {\n    values.channels = values.channels || 'RGBA';\n    const {\n      texture,\n      height,\n      width,\n      channels\n    } = values;\n    const backend = engine().backend;\n    // Have to throw an error, otherwise WebGL just warns and returns wrong\n    // values.\n    if (!backend.gpgpu.gl.isTexture(texture)) {\n      throw new Error(`The texture is invalid. Also, please make sure the texture and ` + `the TFJS WebGL backend are using the same canvas. If you want to ` + `use your own custom canvas, you have to create and use the custom ` + `TFJS WebGL backend created from the canvas through ` + `'new tf.MathBackendWebGL(customCanvas)'.`);\n    }\n    const dataId = backend.writeTexture(texture, shape, dtype, height, width, channels);\n    return engine().makeTensorFromDataId(dataId, shape, dtype, backend);\n  }\n}\nMathBackendWebGL.nextDataId = 0;\nexport { MathBackendWebGL };\nfunction float32ToTypedArray(a, dtype) {\n  if (dtype === 'float32' || dtype === 'complex64') {\n    return a;\n  } else if (dtype === 'int32' || dtype === 'bool') {\n    const result = dtype === 'int32' ? new Int32Array(a.length) : new Uint8Array(a.length);\n    for (let i = 0; i < result.length; ++i) {\n      result[i] = Math.round(a[i]);\n    }\n    return result;\n  } else {\n    throw new Error(`Unknown dtype ${dtype}`);\n  }\n}", "map": {"version": 3, "names": ["backend_util", "buffer", "DataStorage", "engine", "env", "kernel_impls", "KernelBackend", "next<PERSON><PERSON><PERSON>", "scalar", "tidy", "util", "getWebGLContext", "DecodeMatrixProgram", "DecodeMatrixPackedProgram", "EncodeFloatProgram", "EncodeFloatPackedProgram", "EncodeMatrixProgram", "EncodeMatrixPackedProgram", "GPGPUContext", "gpgpu_math", "getUniformLocations", "simpleAbsImplCPU", "PackProgram", "ReshapePackedProgram", "tex_util", "TextureUsage", "TextureManager", "unary_op", "UnaryOpProgram", "UnaryOpPackedProgram", "UnpackProgram", "webgl_util", "whereImpl", "EPSILON_FLOAT32", "EPSILON_FLOAT16", "binaryCaches", "getBinaryCache", "webGLVersion", "CPU_HANDOFF_SIZE_THRESHOLD", "getNumber", "BEFORE_PAGING_CONSTANT", "numMBBeforeWarning", "global", "screen", "height", "width", "window", "devicePixelRatio", "MathBackendWebGL", "nextDataId", "constructor", "gpuResource", "pendingRead", "WeakMap", "pendingDisposal", "WeakSet", "dataRefCount", "numBytesInGPU", "uploadWaitMs", "downloadWaitMs", "lastGlFlushTime", "warnedAboutMemory", "pendingDeletes", "disposed", "getBool", "Error", "newGPGPU", "gl", "binaryCache", "gpgpuCreatedLocally", "gpgpu", "canvas", "textureManager", "texData", "numDataIds", "writeTexture", "texture", "shape", "dtype", "texHeight", "tex<PERSON><PERSON><PERSON>", "channels", "input", "makeTensorInfo", "inData", "get", "dataId", "isPacked", "texShape", "shapeAs3D", "getShapeAs3D", "program", "output", "runWebGLProgram", "disposeIntermediateTensorInfo", "write", "values", "checkNumericalProblems", "id", "set", "usage", "UPLOAD", "refCount", "has", "tensorData", "incRef", "decRef", "move", "tensorInfo", "disposeData", "readSync", "complexTensorInfos", "slice", "CLONE", "res", "data", "convertAndCacheOnCPU", "shouldTimeProgram", "activeTimers", "start", "now", "result", "realValues", "real", "imagValues", "imag", "mergeRealAndImagArrays", "getValuesFromTexture", "read", "subscribers", "Promise", "resolve", "push", "tmpDownloadTarget", "decode", "tmpData", "createBufferFromTexture", "getDenseTexShape", "createAndWaitForFence", "vals", "ps", "all", "size", "sizeFromShape", "downloadFloat32MatrixFromBuffer", "callAndCheck", "deleteBuffer", "dTypeVals", "delete", "for<PERSON>ach", "removeDataId", "readToGPU", "options", "gpuResouorce", "tmpTarget", "customTexShape", "tensorRef", "makeTensorFromTensorInfo", "Object", "assign", "bufferSync", "t", "strings", "map", "d", "decodeString", "_a", "i", "length", "num", "canBeRepresented", "downloadMatrixFromPackedTexture", "subarray", "shouldUsePackedProgram", "outputShape", "downloadByteEncodedFloatMatrixFromOutputTexture", "timerAvailable", "time", "f", "oldActiveTimers", "newActiveTimers", "outerMostTime", "programTimersStack", "flattenedActiveTimerQueries", "flatten", "query", "filter", "flattenedActiveTimerNames", "name", "kernelMs", "wallMs", "sum", "ms", "join", "error", "memory", "unreliable", "numBytesInGPUAllocated", "numBytesAllocated", "numBytesInGPUFree", "numBytesFree", "startTimer", "begin<PERSON><PERSON>y", "startMs", "endMs", "endTimer", "<PERSON><PERSON><PERSON><PERSON>", "getQueryTime", "waitForQueryAndGetTime", "timer<PERSON><PERSON><PERSON>", "force", "add", "releaseGPUData", "key", "origDataId", "computeBytes", "releaseTexture", "getTexture", "uploadToGPU", "getDataInfo", "shouldExecuteOnCPU", "inputs", "sizeThreshold", "every", "getGPGPUContext", "where", "condition", "warn", "condVals", "dataSync", "packedUnaryOp", "x", "op", "outInfo", "compileAndRun", "abs", "outValues", "makeOutput", "ABS", "isString", "encodedValues", "encodeString", "unpackTensor", "packTensor", "preventEagerUnpackingOutput", "packedReshape", "afterShape", "input3DShape", "getBatchDim", "getRowsCols", "input3D", "afterShapeAs3D", "preventEagerUnpackingOfOutput", "customValues", "texSize", "assert", "out", "outputDtype", "customUniformValues", "outData", "packedOutput", "outPackingScheme", "PackingScheme", "DENSE", "texelShape", "outTexUsage", "getTypedArrayFromDType", "dataToDispose", "inputsData", "packedInputs", "isUniform", "uniformValues", "isReshapeFree", "savedInput", "targetShape", "outputData", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "binary", "getAndSaveBinary", "compileProgram", "runProgram", "info", "glFlushThreshold", "flush", "unpacked", "getBinary", "getTextureManager", "dispose", "allKeys", "keys", "deleteProgram", "webGLProgram", "HTMLCanvasElement", "remove", "floatPrecision", "floatPrecisionValue", "debugFlag", "underflowCheckValue", "epsilon", "getTextureShapeFromLogicalShape", "isByteArray", "Uint8Array", "Uint8ClampedArray", "getPackedMatrixTextureShapeWidthHeight", "tempDenseInputTexShape", "tempDenseInputHandle", "tempDenseInputTexData", "PIXELS", "uploadDenseMatrixToTexture", "preventEagerUnpacking", "encodedOutputTarget", "outputTexData", "newTexture", "acquireTexture", "float32Values", "float32ToTypedArray", "texType", "mb", "toFixed", "console", "bytesPerElement", "checkCompileCompletion", "entries", "checkCompletion_", "checkCompileCompletionAsync", "parallelCompilationExtension", "checkCompletionAsync_", "p", "getProgramParameter", "COMPLETION_STATUS_KHR", "LINK_STATUS", "log", "getProgramInfoLog", "getShaderParameter", "fragmentShader", "COMPILE_STATUS", "logShaderSourceAndInfoLog", "source", "getShaderInfoLog", "buildVao", "variablesLocations", "customUniformLocations", "infLoc", "nanLoc", "outShapeLocation", "outShapeStridesLocation", "outTexShapeLocation", "createTensorFromGPUData", "backend", "isTexture", "makeTensorFromDataId", "a", "Int32Array", "Math", "round"], "sources": ["C:\\tfjs-backend-webgl\\src\\backend_webgl.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2017 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\n// Import webgl flags.\nimport './flags_webgl';\n\nimport * as tf from '@tensorflow/tfjs-core';\nimport {backend_util, BackendValues, buffer, DataId, DataStorage, DataToGPUWebGLOption, DataType, engine, env, GPUData, kernel_impls, KernelBackend, MemoryInfo, nextFrame, NumericDataType, Rank, RecursiveArray, scalar, ShapeMap, Tensor, Tensor2D, TensorBuffer, TensorInfo, tidy, TimingInfo, TypedArray, util, WebGLData} from '@tensorflow/tfjs-core';\nimport {getWebGLContext} from './canvas_util';\nimport {DecodeMatrixProgram} from './decode_matrix_gpu';\nimport {DecodeMatrixPackedProgram} from './decode_matrix_packed_gpu';\nimport {EncodeFloatProgram} from './encode_float_gpu';\nimport {EncodeFloatPackedProgram} from './encode_float_packed_gpu';\nimport {EncodeMatrixProgram} from './encode_matrix_gpu';\nimport {EncodeMatrixPackedProgram} from './encode_matrix_packed_gpu';\nimport {GPGPUContext} from './gpgpu_context';\nimport * as gpgpu_math from './gpgpu_math';\nimport {getUniformLocations, GPGPUBinary, GPGPUProgram, TensorData} from './gpgpu_math';\nimport {simpleAbsImplCPU} from './kernel_utils/shared';\nimport {PackProgram} from './pack_gpu';\nimport {ReshapePackedProgram} from './reshape_packed_gpu';\nimport * as tex_util from './tex_util';\nimport {Texture, TextureData, TextureUsage} from './tex_util';\nimport {TextureManager} from './texture_manager';\nimport * as unary_op from './unaryop_gpu';\nimport {UnaryOpProgram} from './unaryop_gpu';\nimport {UnaryOpPackedProgram} from './unaryop_packed_gpu';\nimport {UnpackProgram} from './unpack_gpu';\nimport * as webgl_util from './webgl_util';\n\nconst whereImpl = kernel_impls.whereImpl;\n\nexport const EPSILON_FLOAT32 = 1e-7;\nexport const EPSILON_FLOAT16 = 1e-4;\n\ntype KernelInfo = {\n  name: string; query: Promise<number>;\n};\n\nexport type TimerNode = RecursiveArray<KernelInfo>|KernelInfo;\nexport interface CPUTimerQuery {\n  startMs: number;\n  endMs?: number;\n}\n\nexport interface WebGLMemoryInfo extends MemoryInfo {\n  numBytesInGPU: number;\n  // Tracks the total number of bytes allocated on the GPU, accounting for the\n  // physical texture type.\n  numBytesInGPUAllocated: number;\n  // Tracks byte size of textures that were created and then made available for\n  // reuse (disposed).\n  numBytesInGPUFree: number;\n  unreliable: boolean;\n}\n\nexport interface WebGLTimingInfo extends TimingInfo {\n  uploadWaitMs: number;\n  downloadWaitMs: number;\n}\n\nconst binaryCaches: {[webGLVersion: string]: {[key: string]: GPGPUBinary}} = {};\n\nexport function getBinaryCache(webGLVersion: number) {\n  if (webGLVersion in binaryCaches) {\n    return binaryCaches[webGLVersion];\n  }\n  binaryCaches[webGLVersion] = {};\n  return binaryCaches[webGLVersion];\n}\n\n// Empirically determined constant used to determine size threshold for handing\n// off execution to the CPU.\nconst CPU_HANDOFF_SIZE_THRESHOLD =\n    env().getNumber('CPU_HANDOFF_SIZE_THRESHOLD');\n\n// Empirically determined constant used to decide the number of MB on GPU\n// before we warn about high memory use. The MB are this constant * screen area\n// * dpi / 1024 / 1024.\nconst BEFORE_PAGING_CONSTANT = 600;\nfunction numMBBeforeWarning(): number {\n  if (env().global.screen == null) {\n    return 1024;  // 1 GB.\n  }\n  return (env().global.screen.height * env().global.screen.width *\n          window.devicePixelRatio) *\n      BEFORE_PAGING_CONSTANT / 1024 / 1024;\n}\n\nexport class MathBackendWebGL extends KernelBackend {\n  texData: DataStorage<TextureData>;\n  gpgpu: GPGPUContext;\n\n  private static nextDataId = 0;\n  private nextDataId(): number {\n    return MathBackendWebGL.nextDataId++;\n  }\n  // Maps data ids that have a pending read operation, to list of subscribers.\n  private pendingRead = new WeakMap<DataId, Array<(arr: TypedArray) => void>>();\n  // List of data ids that are scheduled for disposal, but are waiting on a\n  // pending read operation.\n  private pendingDisposal = new WeakSet<DataId>();\n\n  // Used to count the number of 'shallow' sliced tensors that point to the\n  // same data id.\n  dataRefCount = new WeakMap<DataId, number>();\n  private numBytesInGPU = 0;\n\n  private canvas: HTMLCanvasElement|OffscreenCanvas;\n\n  private programTimersStack: TimerNode[];\n  private activeTimers: TimerNode[];\n  // Accumulated time spent (including blocking) in uploading data to webgl.\n  private uploadWaitMs = 0;\n  // Accumulated time spent (including blocking in downloading data from webgl.\n  private downloadWaitMs = 0;\n\n  // record the last manual GL Flush time.\n  private lastGlFlushTime = 0;\n\n  // Number of bits of precision of this backend.\n  private floatPrecisionValue: 32|16;\n\n  private textureManager: TextureManager;\n  private binaryCache: {[key: string]: GPGPUBinary};\n  private gpgpuCreatedLocally: boolean;\n  private numMBBeforeWarning: number;\n  private warnedAboutMemory = false;\n\n  constructor(gpuResource?: GPGPUContext|HTMLCanvasElement|OffscreenCanvas) {\n    super();\n    if (!env().getBool('HAS_WEBGL')) {\n      throw new Error('WebGL is not supported on this device');\n    }\n\n    let newGPGPU;\n    if (gpuResource != null) {\n      if (gpuResource instanceof GPGPUContext) {\n        newGPGPU = gpuResource;\n      } else {\n        const gl =\n            getWebGLContext(env().getNumber('WEBGL_VERSION'), gpuResource);\n        newGPGPU = new GPGPUContext(gl);\n      }\n      this.binaryCache = {};\n      this.gpgpuCreatedLocally = false;\n    } else {\n      const gl = getWebGLContext(env().getNumber('WEBGL_VERSION'));\n      newGPGPU = new GPGPUContext(gl);\n      this.binaryCache = getBinaryCache(env().getNumber('WEBGL_VERSION'));\n      this.gpgpuCreatedLocally = true;\n    }\n\n    this.gpgpu = newGPGPU;\n    this.canvas = this.gpgpu.gl.canvas;\n    this.textureManager = new TextureManager(this.gpgpu);\n    this.numMBBeforeWarning = numMBBeforeWarning();\n    this.texData = new DataStorage(this, engine());\n  }\n\n  override numDataIds() {\n    return this.texData.numDataIds() - this.pendingDeletes;\n  }\n\n  // Writes a new entry to the data store with a WebGL texture, and registers it\n  // to the texture manager.\n  writeTexture(\n      texture: WebGLTexture, shape: number[], dtype: DataType,\n      texHeight: number, texWidth: number, channels: string): DataId {\n    // Temporarily create an tensor info to make the texture compatible with\n    // the runWebGLProgram's input.\n    const input = this.makeTensorInfo(shape, dtype);\n    const inData = this.texData.get(input.dataId);\n    // Even though the input texture could be unpacked or dense packed, it is\n    // always considered as unpacked for EncodeMatrixProgram.\n    inData.isPacked = false;\n\n    // Bind texture to the input tensor.\n    inData.texture = {texture, texShape: [texHeight, texWidth]};\n    inData.texShape = [texHeight, texWidth];\n\n    const shapeAs3D = webgl_util.getShapeAs3D(shape);\n    const program =\n        new EncodeMatrixProgram(shapeAs3D, false /* isByteArray */, channels);\n    const output =\n        this.runWebGLProgram(program, [input], dtype, [[texHeight, texWidth]]);\n    output.shape = shape;\n\n    // Unbind the texture from the input tensor to avoid the texture being\n    // released.\n    inData.texture = null;\n    this.disposeIntermediateTensorInfo(input);\n\n    return output.dataId;\n  }\n\n  override write(values: BackendValues, shape: number[], dtype: DataType):\n      DataId {\n    if (env().getBool('WEBGL_CHECK_NUMERICAL_PROBLEMS') ||\n        env().getBool('DEBUG')) {\n      this.checkNumericalProblems(values);\n    }\n    if (dtype === 'complex64' && values != null) {\n      throw new Error(\n          `Cannot write to a complex64 dtype. ` +\n          `Please use tf.complex(real, imag).`);\n    }\n    const dataId = {id: this.nextDataId()};\n    this.texData.set(\n        dataId,\n        {shape, dtype, values, usage: TextureUsage.UPLOAD, refCount: 1});\n    return dataId;\n  }\n\n  /** Return refCount of a `TensorData`. */\n  override refCount(dataId: DataId): number {\n    if (this.texData.has(dataId)) {\n      const tensorData = this.texData.get(dataId);\n      return tensorData.refCount;\n    }\n    return 0;\n  }\n\n  /** Increase refCount of a `TextureData`. */\n  override incRef(dataId: DataId): void {\n    const texData = this.texData.get(dataId);\n    texData.refCount++;\n  }\n\n  /** Decrease refCount of a `TextureData`. */\n  decRef(dataId: DataId): void {\n    if (this.texData.has(dataId)) {\n      const texData = this.texData.get(dataId);\n      texData.refCount--;\n    }\n  }\n\n  override move(\n      dataId: DataId, values: BackendValues, shape: number[], dtype: DataType,\n      refCount: number): void {\n    if (env().getBool('DEBUG')) {\n      this.checkNumericalProblems(values);\n    }\n    if (dtype === 'complex64') {\n      throw new Error(\n          `Cannot write to a complex64 dtype. ` +\n          `Please use tf.complex(real, imag).`);\n    }\n    this.texData.set(\n        dataId, {shape, dtype, values, usage: TextureUsage.UPLOAD, refCount});\n  }\n\n  disposeIntermediateTensorInfo(tensorInfo: TensorInfo): void {\n    this.disposeData(tensorInfo.dataId);\n  }\n\n  override readSync(dataId: DataId): BackendValues {\n    const texData = this.texData.get(dataId);\n    const {values, dtype, complexTensorInfos, slice, shape, isPacked} = texData;\n\n    // The presence of `slice` indicates this tensor is a shallow slice of a\n    // different tensor, and is using that original tensor's texture. Run\n    // `clone` in order to copy that texture and read from it.\n    if (slice != null) {\n      let program;\n      if (isPacked) {\n        program = new UnaryOpPackedProgram(shape, unary_op.CLONE);\n      } else {\n        program = new UnaryOpProgram(shape, unary_op.CLONE);\n      }\n      const res =\n          this.runWebGLProgram(program, [{dataId, shape, dtype}], dtype);\n      const data = this.readSync(res.dataId);\n      this.disposeIntermediateTensorInfo(res);\n      return data;\n    }\n    if (values != null) {\n      return this.convertAndCacheOnCPU(dataId);\n    }\n    if (dtype === 'string') {\n      return values;\n    }\n    const shouldTimeProgram = this.activeTimers != null;\n    let start: number;\n    if (shouldTimeProgram) {\n      start = util.now();\n    }\n\n    let result: Float32Array;\n    if (dtype === 'complex64') {\n      const realValues =\n          this.readSync(complexTensorInfos.real.dataId) as Float32Array;\n      const imagValues =\n          this.readSync(complexTensorInfos.imag.dataId) as Float32Array;\n      result = backend_util.mergeRealAndImagArrays(realValues, imagValues);\n    } else {\n      result = this.getValuesFromTexture(dataId);\n    }\n\n    if (shouldTimeProgram) {\n      this.downloadWaitMs += util.now() - start;\n    }\n    return this.convertAndCacheOnCPU(dataId, result);\n  }\n\n  override async read(dataId: DataId): Promise<BackendValues> {\n    if (this.pendingRead.has(dataId)) {\n      const subscribers = this.pendingRead.get(dataId);\n      return new Promise<TypedArray>(resolve => subscribers.push(resolve));\n    }\n    const texData = this.texData.get(dataId);\n    const {values, shape, slice, dtype, complexTensorInfos, isPacked} = texData;\n\n    // The presence of `slice` indicates this tensor is a shallow slice of a\n    // different tensor, and is using that original tensor's texture. Run\n    // `clone` in order to copy that texture and read from it.\n    if (slice != null) {\n      let program;\n      if (isPacked) {\n        program = new UnaryOpPackedProgram(shape, unary_op.CLONE);\n      } else {\n        program = new UnaryOpProgram(shape, unary_op.CLONE);\n      }\n      const res =\n          this.runWebGLProgram(program, [{dataId, shape, dtype}], dtype);\n      const data = this.read(res.dataId);\n      this.disposeIntermediateTensorInfo(res);\n      return data;\n    }\n\n    if (values != null) {\n      return this.convertAndCacheOnCPU(dataId);\n    }\n\n    if (env().getBool('DEBUG')) {\n      // getBool('WEBGL_DOWNLOAD_FLOAT_ENABLED') caused a blocking GPU call.\n      // For performance reason, only check it for debugging. In production,\n      // it doesn't handle this use case anyway, so behavior is not changed.\n      if (!env().getBool('WEBGL_DOWNLOAD_FLOAT_ENABLED') &&\n          env().getNumber('WEBGL_VERSION') === 2) {\n        throw new Error(\n            `tensor.data() with WEBGL_DOWNLOAD_FLOAT_ENABLED=false and ` +\n            `WEBGL_VERSION=2 not yet supported.`);\n      }\n    }\n\n    let buffer: WebGLBuffer = null;\n    let tmpDownloadTarget: TensorInfo;\n\n    if (dtype !== 'complex64' && env().get('WEBGL_BUFFER_SUPPORTED')) {\n      // Possibly copy the texture into a buffer before inserting a fence.\n      tmpDownloadTarget = this.decode(dataId);\n      const tmpData = this.texData.get(tmpDownloadTarget.dataId);\n\n      buffer = this.gpgpu.createBufferFromTexture(\n          tmpData.texture.texture, ...tex_util.getDenseTexShape(shape));\n    }\n\n    this.pendingRead.set(dataId, []);\n\n    if (dtype !== 'complex64') {\n      // Create a fence and wait for it to resolve.\n      await this.gpgpu.createAndWaitForFence();\n    }\n\n    // Download the values from the GPU.\n    let vals: Float32Array;\n    if (dtype === 'complex64') {\n      const ps = await Promise.all([\n        this.read(complexTensorInfos.real.dataId),\n        this.read(complexTensorInfos.imag.dataId)\n      ]);\n\n      const realValues = ps[0];\n      const imagValues = ps[1];\n      vals = backend_util.mergeRealAndImagArrays(\n          realValues as Float32Array, imagValues as Float32Array);\n    } else if (buffer == null) {\n      vals = this.getValuesFromTexture(dataId);\n    } else {\n      const size = util.sizeFromShape(shape);\n      vals = this.gpgpu.downloadFloat32MatrixFromBuffer(buffer, size);\n    }\n    if (tmpDownloadTarget != null) {\n      this.disposeIntermediateTensorInfo(tmpDownloadTarget);\n    }\n    if (buffer != null) {\n      const gl = this.gpgpu.gl;\n      webgl_util.callAndCheck(gl, () => gl.deleteBuffer(buffer));\n    }\n    const dTypeVals = this.convertAndCacheOnCPU(dataId, vals);\n\n    const subscribers = this.pendingRead.get(dataId);\n    this.pendingRead.delete(dataId);\n\n    // Notify all pending reads.\n    subscribers.forEach(resolve => resolve(dTypeVals));\n    if (this.pendingDisposal.has(dataId)) {\n      this.pendingDisposal.delete(dataId);\n      if (this.disposeData(dataId)) {\n        engine().removeDataId(dataId, this);\n      }\n      this.pendingDeletes--;\n    }\n    return dTypeVals;\n  }\n\n  /**\n   * Read tensor to a new texture that is densely packed for ease of use.\n   * @param dataId The source tensor.\n   * @param options\n   *     customTexShape: Optional. If set, will use the user defined texture\n   *     shape to create the texture.\n   */\n  override readToGPU(dataId: DataId, options: DataToGPUWebGLOption = {}):\n      GPUData {\n    const texData = this.texData.get(dataId);\n    const {values, shape, slice, dtype, isPacked, texture} = texData;\n\n    if (dtype === 'complex64') {\n      throw new Error('Does not support reading texture for complex64 dtype.');\n    }\n\n    // The presence of `slice` indicates this tensor is a shallow slice of a\n    // different tensor, and is using that original tensor's texture. Run\n    // `clone` in order to copy that texture and read from it.\n    if (slice != null) {\n      let program;\n      if (isPacked) {\n        program = new UnaryOpPackedProgram(shape, unary_op.CLONE);\n      } else {\n        program = new UnaryOpProgram(shape, unary_op.CLONE);\n      }\n      const res =\n          this.runWebGLProgram(program, [{dataId, shape, dtype}], dtype);\n      const gpuResouorce = this.readToGPU(res, options);\n      this.disposeIntermediateTensorInfo(res);\n      return gpuResouorce;\n    }\n\n    if (texture == null) {\n      if (values != null) {\n        throw new Error('Data is not on GPU but on CPU.');\n      } else {\n        throw new Error('There is no data on GPU or CPU.');\n      }\n    }\n\n    // Decode the texture so that it is stored densely (using four channels).\n    const tmpTarget = this.decode(dataId, options.customTexShape);\n\n    // Make engine track this tensor, so that we can dispose it later.\n    const tensorRef = engine().makeTensorFromTensorInfo(tmpTarget);\n\n    const tmpData = this.texData.get(tmpTarget.dataId);\n    return {tensorRef, ...tmpData.texture};\n  }\n\n  bufferSync<R extends Rank, D extends DataType>(t: TensorInfo):\n      TensorBuffer<R, D> {\n    const data = this.readSync(t.dataId);\n    if (t.dtype === 'string') {\n      try {\n        // Decode the bytes into string.\n        const strings = (data as Uint8Array[]).map(d => util.decodeString(d));\n        return buffer(t.shape as ShapeMap[R], t.dtype, strings) as\n            TensorBuffer<R, D>;\n      } catch {\n        throw new Error('Failed to decode encoded string bytes into utf-8');\n      }\n    }\n    return buffer(t.shape as ShapeMap[R], t.dtype, data as TypedArray) as\n        TensorBuffer<R, D>;\n  }\n\n  private checkNumericalProblems(values: BackendValues): void {\n    if (values == null) {\n      return;\n    }\n    for (let i = 0; i < values.length; i++) {\n      const num = values[i] as number;\n      if (!webgl_util.canBeRepresented(num)) {\n        if (env().getBool('WEBGL_RENDER_FLOAT32_CAPABLE')) {\n          throw Error(\n              `The value ${num} cannot be represented with your ` +\n              `current settings. Consider enabling float32 rendering: ` +\n              `'tf.env().set('WEBGL_RENDER_FLOAT32_ENABLED', true);'`);\n        }\n        throw Error(`The value ${num} cannot be represented on this device.`);\n      }\n    }\n  }\n\n  private getValuesFromTexture(dataId: DataId): Float32Array {\n    const {shape, dtype, isPacked} = this.texData.get(dataId);\n    const size = util.sizeFromShape(shape);\n    if (env().getBool('WEBGL_DOWNLOAD_FLOAT_ENABLED')) {\n      const tmpTarget = this.decode(dataId);\n      const tmpData = this.texData.get(tmpTarget.dataId);\n      const vals =\n          this.gpgpu\n              .downloadMatrixFromPackedTexture(\n                  tmpData.texture.texture, ...tex_util.getDenseTexShape(shape))\n              .subarray(0, size);\n\n      this.disposeIntermediateTensorInfo(tmpTarget);\n\n      return vals;\n    }\n\n    const shouldUsePackedProgram =\n        env().getBool('WEBGL_PACK') && isPacked === true;\n    const outputShape =\n        shouldUsePackedProgram ? webgl_util.getShapeAs3D(shape) : shape;\n    const program = shouldUsePackedProgram ?\n        new EncodeFloatPackedProgram(outputShape as [number, number, number]) :\n        new EncodeFloatProgram(outputShape);\n    const output = this.runWebGLProgram(\n        program, [{shape: outputShape, dtype, dataId}], 'float32');\n    const tmpData = this.texData.get(output.dataId);\n    const vals = this.gpgpu\n                     .downloadByteEncodedFloatMatrixFromOutputTexture(\n                         tmpData.texture.texture, tmpData.texShape[0],\n                         tmpData.texShape[1])\n                     .subarray(0, size);\n    this.disposeIntermediateTensorInfo(output);\n\n    return vals;\n  }\n\n  override timerAvailable(): boolean {\n    return env().getNumber('WEBGL_DISJOINT_QUERY_TIMER_EXTENSION_RELIABLE') > 0;\n  }\n\n  override time(f: () => void): Promise<WebGLTimingInfo> {\n    const oldActiveTimers = this.activeTimers;\n    const newActiveTimers: TimerNode[] = [];\n\n    let outerMostTime = false;\n    if (this.programTimersStack == null) {\n      this.programTimersStack = newActiveTimers;\n      outerMostTime = true;\n    } else {\n      this.activeTimers.push(newActiveTimers);\n    }\n    this.activeTimers = newActiveTimers;\n\n    f();\n\n    // needing to split these up because util.flatten only accepts certain types\n    const flattenedActiveTimerQueries =\n        util.flatten(this.activeTimers.map((d: KernelInfo) => d.query))\n            .filter(d => d != null);\n    const flattenedActiveTimerNames =\n        util.flatten(this.activeTimers.map((d: KernelInfo) => d.name))\n            .filter(d => d != null);\n\n    this.activeTimers = oldActiveTimers;\n\n    if (outerMostTime) {\n      this.programTimersStack = null;\n    }\n\n    const res: WebGLTimingInfo = {\n      uploadWaitMs: this.uploadWaitMs,\n      downloadWaitMs: this.downloadWaitMs,\n      kernelMs: null,\n      wallMs: null  // will be filled by the engine\n    };\n\n    return (async () => {\n      if (env().getNumber('WEBGL_DISJOINT_QUERY_TIMER_EXTENSION_RELIABLE') >\n          0) {\n        const kernelMs = await Promise.all(flattenedActiveTimerQueries);\n\n        res['kernelMs'] = util.sum(kernelMs);\n        res['getExtraProfileInfo'] = () =>\n            kernelMs\n                .map((d, i) => ({name: flattenedActiveTimerNames[i], ms: d}))\n                .map(d => `${d.name}: ${d.ms}`)\n                .join(', ');\n      } else {\n        res['kernelMs'] = {\n          error: 'WebGL query timers are not supported in this environment.'\n        };\n      }\n\n      this.uploadWaitMs = 0;\n      this.downloadWaitMs = 0;\n      return res;\n    })();\n  }\n  override memory(): WebGLMemoryInfo {\n    return {\n      unreliable: false,\n      numBytesInGPU: this.numBytesInGPU,\n      numBytesInGPUAllocated: this.textureManager.numBytesAllocated,\n      numBytesInGPUFree: this.textureManager.numBytesFree\n    } as WebGLMemoryInfo;\n  }\n\n  private startTimer(): WebGLQuery|CPUTimerQuery {\n    if (env().getNumber('WEBGL_DISJOINT_QUERY_TIMER_EXTENSION_RELIABLE') > 0) {\n      return this.gpgpu.beginQuery();\n    }\n    return {startMs: util.now(), endMs: null};\n  }\n\n  private endTimer(query: WebGLQuery|CPUTimerQuery): WebGLQuery|CPUTimerQuery {\n    if (env().getNumber('WEBGL_DISJOINT_QUERY_TIMER_EXTENSION_RELIABLE') > 0) {\n      this.gpgpu.endQuery();\n      return query;\n    }\n    (query as CPUTimerQuery).endMs = util.now();\n    return query;\n  }\n\n  private async getQueryTime(query: WebGLQuery|CPUTimerQuery): Promise<number> {\n    if (env().getNumber('WEBGL_DISJOINT_QUERY_TIMER_EXTENSION_RELIABLE') > 0) {\n      return this.gpgpu.waitForQueryAndGetTime(query as WebGLQuery);\n    }\n    const timerQuery = query as CPUTimerQuery;\n    return timerQuery.endMs - timerQuery.startMs;\n  }\n\n  private pendingDeletes = 0;\n\n  /**\n   * Decrease the RefCount on the dataId and dispose the memory if the dataId\n   * has 0 refCount. If there are pending read on the data, the disposal would\n   * added to the pending delete queue. Return true if the dataId is removed\n   * from backend or the backend does not contain the dataId, false if the\n   * dataId is not removed. Memory may or may not be released even when dataId\n   * is removed, which also depends on dataRefCount, see `releaseGPU`.\n   * @param dataId\n   * @oaram force Optional, remove the data regardless of refCount\n   */\n  override disposeData(dataId: DataId, force = false): boolean {\n    if (this.pendingDisposal.has(dataId)) {\n      return false;\n    }\n\n    // No-op if already disposed.\n    if (!this.texData.has(dataId)) {\n      return true;\n    }\n\n    // if force flag is set, change refCount to 0, this would ensure disposal\n    // when added to the pendingDisposal queue. Memory may or may not be\n    // released, which also depends on dataRefCount, see `releaseGPU`.\n    if (force) {\n      this.texData.get(dataId).refCount = 0;\n    } else {\n      this.texData.get(dataId).refCount--;\n    }\n\n    if (!force && this.texData.get(dataId).refCount > 0) {\n      return false;\n    }\n\n    if (this.pendingRead.has(dataId)) {\n      this.pendingDisposal.add(dataId);\n      this.pendingDeletes++;\n      return false;\n    }\n\n    this.releaseGPUData(dataId);\n    const {complexTensorInfos} = this.texData.get(dataId);\n    if (complexTensorInfos != null) {\n      this.disposeData(complexTensorInfos.real.dataId, force);\n      this.disposeData(complexTensorInfos.imag.dataId, force);\n    }\n\n    this.texData.delete(dataId);\n\n    return true;\n  }\n\n  private releaseGPUData(dataId: DataId): void {\n    const {texture, dtype, texShape, usage, isPacked, slice} =\n        this.texData.get(dataId);\n    const key = slice && slice.origDataId || dataId;\n    const refCount = this.dataRefCount.get(key);\n\n    if (refCount > 1) {\n      this.dataRefCount.set(key, refCount - 1);\n    } else {\n      this.dataRefCount.delete(key);\n      if (texture != null) {\n        this.numBytesInGPU -= this.computeBytes(texShape, dtype);\n        this.textureManager.releaseTexture(texture, texShape, usage, isPacked);\n      }\n    }\n\n    const texData = this.texData.get(dataId);\n    texData.texture = null;\n    texData.texShape = null;\n    texData.isPacked = false;\n    texData.slice = null;\n  }\n\n  getTexture(dataId: DataId): WebGLTexture {\n    this.uploadToGPU(dataId);\n    return this.texData.get(dataId).texture.texture;\n  }\n\n  /**\n   * Returns internal information for the specific data bucket. Used in unit\n   * tests.\n   */\n  getDataInfo(dataId: DataId): TextureData {\n    return this.texData.get(dataId);\n  }\n\n  /*\n  Tests whether all the inputs to an op are small and on the CPU. This heuristic\n  determines when it would be faster to execute a kernel on the CPU. WebGL\n  kernels opt into running this check and forwarding when appropriate.\n  TODO(https://github.com/tensorflow/tfjs/issues/872): Develop a more\n  sustainable strategy for optimizing backend execution of ops.\n   */\n  shouldExecuteOnCPU(\n      inputs: TensorInfo[],\n      sizeThreshold = CPU_HANDOFF_SIZE_THRESHOLD): boolean {\n    return env().getBool('WEBGL_CPU_FORWARD') &&\n        inputs.every(\n            input => this.texData.get(input.dataId).texture == null &&\n                util.sizeFromShape(input.shape) < sizeThreshold);\n  }\n\n  getGPGPUContext(): GPGPUContext {\n    return this.gpgpu;\n  }\n\n  where(condition: Tensor): Tensor2D {\n    backend_util.warn(\n        'tf.where() in webgl locks the UI thread. ' +\n        'Call tf.whereAsync() instead');\n    const condVals = condition.dataSync();\n    return whereImpl(condition.shape, condVals);\n  }\n\n  private packedUnaryOp(x: TensorInfo, op: string, dtype: DataType) {\n    const program = new UnaryOpPackedProgram(x.shape, op);\n    const outInfo = this.compileAndRun(program, [x], dtype);\n    return engine().makeTensorFromTensorInfo(outInfo);\n  }\n\n  // TODO(msoulanille) remove this once the backend has been modularized\n  // a copy is needed here to break a circular dependency.\n  // Also remove the op from unary_op.\n  abs<T extends Tensor>(x: T): T {\n    // TODO: handle cases when x is complex.\n    if (this.shouldExecuteOnCPU([x]) && x.dtype !== 'complex64') {\n      const outValues =\n          simpleAbsImplCPU(this.texData.get(x.dataId).values as TypedArray);\n      return this.makeOutput(x.shape, x.dtype, outValues);\n    }\n\n    if (env().getBool('WEBGL_PACK_UNARY_OPERATIONS')) {\n      return this.packedUnaryOp(x, unary_op.ABS, x.dtype) as T;\n    }\n\n    const program = new UnaryOpProgram(x.shape, unary_op.ABS);\n    const outInfo = this.compileAndRun(program, [x]);\n    return engine().makeTensorFromTensorInfo(outInfo) as T;\n  }\n\n  makeTensorInfo(\n      shape: number[], dtype: DataType,\n      values?: BackendValues|string[]): TensorInfo {\n    let dataId;\n    if (dtype === 'string' && values != null && values.length > 0 &&\n        util.isString(values[0])) {\n      const encodedValues =\n          (values as unknown as string[]).map(d => util.encodeString(d));\n\n      dataId = this.write(encodedValues, shape, dtype);\n    } else {\n      dataId = this.write(values as TypedArray, shape, dtype);\n    }\n\n    this.texData.get(dataId).usage = null;\n    return {dataId, shape, dtype};\n  }\n\n  private makeOutput<T extends Tensor>(\n      shape: number[], dtype: DataType, values?: BackendValues): T {\n    return engine().makeTensorFromTensorInfo(\n               this.makeTensorInfo(shape, dtype, values), this) as T;\n  }\n\n  unpackTensor(input: TensorInfo): TensorInfo {\n    const program = new UnpackProgram(input.shape);\n    return this.runWebGLProgram(program, [input], input.dtype);\n  }\n\n  packTensor(input: TensorInfo): TensorInfo {\n    const program = new PackProgram(input.shape);\n    const preventEagerUnpackingOutput = true;\n    return this.runWebGLProgram(\n        program, [input], input.dtype, null /* customUniformValues */,\n        preventEagerUnpackingOutput);\n  }\n\n  private packedReshape(input: TensorInfo, afterShape: number[]): TensorInfo {\n    const input3DShape = [\n      webgl_util.getBatchDim(input.shape),\n      ...webgl_util.getRowsCols(input.shape)\n    ] as [number, number, number];\n    const input3D: TensorInfo = {\n      dtype: input.dtype,\n      shape: input3DShape,\n      dataId: input.dataId\n    };\n    const afterShapeAs3D = [\n      webgl_util.getBatchDim(afterShape), ...webgl_util.getRowsCols(afterShape)\n    ] as [number, number, number];\n\n    const program = new ReshapePackedProgram(afterShapeAs3D, input3DShape);\n    const preventEagerUnpackingOfOutput = true;\n    const customValues = [input3DShape];\n    const output = this.runWebGLProgram(\n        program, [input3D], input.dtype, customValues,\n        preventEagerUnpackingOfOutput);\n    return {dataId: output.dataId, shape: afterShape, dtype: output.dtype};\n  }\n\n  private decode(dataId: DataId, customTexShape?: [number, number]):\n      TensorInfo {\n    const texData = this.texData.get(dataId);\n    const {isPacked, shape, dtype} = texData;\n    if (customTexShape != null) {\n      const size = util.sizeFromShape(shape);\n      const texSize = customTexShape[0] * customTexShape[1] * 4;\n      util.assert(\n          size <= texSize,\n          () => 'customTexShape is too small. ' +\n              'Row * Column * 4 should be equal or larger than the ' +\n              'size of the tensor data.');\n    }\n    const shapeAs3D =\n        webgl_util.getShapeAs3D(shape) as [number, number, number];\n    let program;\n    if (isPacked) {\n      program = new DecodeMatrixPackedProgram(shapeAs3D);\n    } else {\n      program = new DecodeMatrixProgram(shapeAs3D);\n    }\n    const preventEagerUnpackingOfOutput = true;\n    const customValues =\n        [customTexShape != null ? customTexShape :\n                                  tex_util.getDenseTexShape(shapeAs3D)];\n    const out = this.runWebGLProgram(\n        program, [{shape: shapeAs3D, dtype, dataId}], dtype, customValues,\n        preventEagerUnpackingOfOutput, customTexShape);\n    return {dtype, shape, dataId: out.dataId};\n  }\n\n  runWebGLProgram(\n      program: GPGPUProgram, inputs: TensorInfo[], outputDtype: DataType,\n      customUniformValues?: number[][], preventEagerUnpackingOfOutput = false,\n      customTexShape?: [number, number]): TensorInfo {\n    const output = this.makeTensorInfo(program.outputShape, outputDtype);\n    const outData = this.texData.get(output.dataId);\n    if (program.packedOutput) {\n      outData.isPacked = true;\n    }\n    if (program.outPackingScheme === tex_util.PackingScheme.DENSE) {\n      const texelShape = customTexShape != null ?\n          customTexShape :\n          tex_util.getDenseTexShape(program.outputShape);\n      // For a densely packed output, we explicitly set texShape\n      // so it doesn't get assigned later according to our typical packing\n      // scheme wherein a single texel can only contain values from adjacent\n      // rows/cols.\n      outData.texShape = texelShape.map(d => d * 2) as [number, number];\n    }\n    if (program.outTexUsage != null) {\n      outData.usage = program.outTexUsage;\n    }\n\n    if (util.sizeFromShape(output.shape) === 0) {\n      // Short-circuit the computation since the result is empty (has 0 in its\n      // shape).\n      outData.values =\n          util.getTypedArrayFromDType(output.dtype as 'float32', 0);\n      return output;\n    }\n\n    const dataToDispose: TensorInfo[] = [];\n    const inputsData: TensorData[] = inputs.map(input => {\n      if (input.dtype === 'complex64') {\n        throw new Error(\n            `GPGPUProgram does not support complex64 input. For complex64 ` +\n            `dtypes, please separate the program into real and imaginary ` +\n            `parts.`);\n      }\n\n      let texData = this.texData.get(input.dataId);\n\n      if (texData.texture == null) {\n        if (!program.packedInputs &&\n            util.sizeFromShape(input.shape) <=\n                env().getNumber('WEBGL_SIZE_UPLOAD_UNIFORM')) {\n          // Upload small tensors that live on the CPU as uniforms, not as\n          // textures. Do this only when the environment supports 32bit floats\n          // due to problems when comparing 16bit floats with 32bit floats.\n          // TODO(https://github.com/tensorflow/tfjs/issues/821): Make it\n          // possible for packed shaders to sample from uniforms.\n          return {\n            shape: input.shape,\n            texData: null,\n            isUniform: true,\n            uniformValues: texData.values as TypedArray\n          };\n        }\n\n        // This ensures that if a packed program's inputs have not yet been\n        // uploaded to the GPU, they get uploaded as packed right off the bat.\n        if (program.packedInputs) {\n          texData.isPacked = true;\n          texData.shape = input.shape;\n        }\n      }\n\n      this.uploadToGPU(input.dataId);\n      if (!!texData.isPacked !== !!program.packedInputs) {\n        input = texData.isPacked ? this.unpackTensor(input) :\n                                   this.packTensor(input);\n        dataToDispose.push(input);\n        texData = this.texData.get(input.dataId);\n      } else if (\n          texData.isPacked &&\n          !webgl_util.isReshapeFree(texData.shape, input.shape)) {\n        // This is a special case where a texture exists for a tensor\n        // but the shapes are incompatible (due to packing constraints) because\n        // the tensor did not have a chance to go through the packed reshape\n        // shader. This only happens when we reshape the *same* tensor to form\n        // *distinct* inputs to an op, e.g. dotting a vector with itself. This\n        // case will disappear once packed uploading is the default.\n\n        const savedInput = input;\n        const targetShape = input.shape;\n\n        input.shape = texData.shape;\n        input = this.packedReshape(input as Tensor, targetShape);\n        dataToDispose.push(input);\n        texData = this.texData.get(input.dataId);\n\n        savedInput.shape = targetShape;\n      }\n\n      return {shape: input.shape, texData, isUniform: false};\n    });\n\n    this.uploadToGPU(output.dataId);\n    const outputData:\n        TensorData = {shape: output.shape, texData: outData, isUniform: false};\n    const key = gpgpu_math.makeShaderKey(program, inputsData, outputData);\n    const binary = this.getAndSaveBinary(key, () => {\n      return gpgpu_math.compileProgram(\n          this.gpgpu, program, inputsData, outputData);\n    });\n    const shouldTimeProgram = this.activeTimers != null;\n    let query: WebGLQuery|CPUTimerQuery;\n    if (shouldTimeProgram) {\n      query = this.startTimer();\n    }\n\n    if (!env().get('ENGINE_COMPILE_ONLY')) {\n      gpgpu_math.runProgram(\n          this.gpgpu, binary, inputsData, outputData, customUniformValues);\n    }\n\n    dataToDispose.forEach(info => this.disposeIntermediateTensorInfo(info));\n\n    if (shouldTimeProgram) {\n      query = this.endTimer(query);\n      this.activeTimers.push(\n          {name: program.constructor.name, query: this.getQueryTime(query)});\n    }\n\n    const glFlushThreshold = env().getNumber('WEBGL_FLUSH_THRESHOLD');\n    // Manually GL flush requested\n    if (glFlushThreshold > 0) {\n      const time = util.now();\n      if ((time - this.lastGlFlushTime) > glFlushThreshold) {\n        this.gpgpu.gl.flush();\n        this.lastGlFlushTime = time;\n      }\n    }\n\n    if (!env().getBool('WEBGL_LAZILY_UNPACK') && outData.isPacked &&\n        preventEagerUnpackingOfOutput === false) {\n      const unpacked = this.unpackTensor(output);\n      this.disposeIntermediateTensorInfo(output);\n      return unpacked;\n    }\n    return output;\n  }\n\n  compileAndRun(\n      program: GPGPUProgram, inputs: TensorInfo[], outputDtype?: DataType,\n      customUniformValues?: number[][],\n      preventEagerUnpackingOfOutput = false): TensorInfo {\n    outputDtype = outputDtype || inputs[0].dtype;\n    const outInfo = this.runWebGLProgram(\n        program, inputs, outputDtype, customUniformValues,\n        preventEagerUnpackingOfOutput);\n    return outInfo;\n  }\n\n  private getAndSaveBinary(key: string, getBinary: () => GPGPUBinary):\n      GPGPUBinary {\n    if (!(key in this.binaryCache)) {\n      this.binaryCache[key] = getBinary();\n    }\n    return this.binaryCache[key];\n  }\n\n  getTextureManager(): TextureManager {\n    return this.textureManager;\n  }\n\n  private disposed = false;\n\n  override dispose() {\n    if (this.disposed) {\n      return;\n    }\n    // Avoid disposing the compiled webgl programs during unit testing because\n    // it slows down test execution.\n    if (!env().getBool('IS_TEST')) {\n      const allKeys = Object.keys(this.binaryCache);\n      allKeys.forEach(key => {\n        this.gpgpu.deleteProgram(this.binaryCache[key].webGLProgram);\n        delete this.binaryCache[key];\n      });\n    }\n    this.textureManager.dispose();\n    if (this.canvas != null &&\n        (typeof (HTMLCanvasElement) !== 'undefined' &&\n         this.canvas instanceof HTMLCanvasElement)) {\n      this.canvas.remove();\n    } else {\n      this.canvas = null;\n    }\n    if (this.gpgpuCreatedLocally) {\n      this.gpgpu.program = null;\n      this.gpgpu.dispose();\n    }\n    this.disposed = true;\n  }\n\n  override floatPrecision(): 16|32 {\n    if (this.floatPrecisionValue == null) {\n      this.floatPrecisionValue = tidy(() => {\n        if (!env().get('WEBGL_RENDER_FLOAT32_ENABLED')) {\n          // Momentarily switching DEBUG flag to false so we don't throw an\n          // error trying to upload a small value.\n          const debugFlag = env().getBool('DEBUG');\n          env().set('DEBUG', false);\n          const underflowCheckValue = this.abs(scalar(1e-8)).dataSync()[0];\n          env().set('DEBUG', debugFlag);\n\n          if (underflowCheckValue > 0) {\n            return 32;\n          }\n        }\n        return 16;\n      });\n    }\n    return this.floatPrecisionValue;\n  }\n\n  /** Returns the smallest representable number.  */\n  override epsilon(): number {\n    return this.floatPrecision() === 32 ? EPSILON_FLOAT32 : EPSILON_FLOAT16;\n  }\n\n  uploadToGPU(dataId: DataId): void {\n    const texData = this.texData.get(dataId);\n    const {shape, dtype, values, texture, usage, isPacked} = texData;\n\n    if (texture != null) {\n      // Array is already on GPU. No-op.\n      return;\n    }\n    const shouldTimeProgram = this.activeTimers != null;\n    let start: number;\n    if (shouldTimeProgram) {\n      start = util.now();\n    }\n\n    let texShape = texData.texShape;\n    if (texShape == null) {\n      // This texShape may not be the final texture shape. For packed or dense\n      // textures, the texShape will be changed when textures are created.\n      texShape = webgl_util.getTextureShapeFromLogicalShape(shape, isPacked);\n      texData.texShape = texShape;\n    }\n\n    if (values != null) {\n      const shapeAs3D = webgl_util.getShapeAs3D(shape);\n\n      let program;\n      let width = texShape[1], height = texShape[0];\n      const isByteArray =\n          values instanceof Uint8Array || values instanceof Uint8ClampedArray;\n\n      // texture for float array is PhysicalTextureType.PACKED_2X2_FLOAT32, we\n      // need to make sure the upload uses the same packed size\n      if (isPacked || !isByteArray) {\n        [width, height] = tex_util.getPackedMatrixTextureShapeWidthHeight(\n            texShape[0], texShape[1]);\n      }\n\n      if (isPacked) {\n        program = new EncodeMatrixPackedProgram(shapeAs3D, isByteArray);\n      } else {\n        program = new EncodeMatrixProgram(shapeAs3D, isByteArray);\n      }\n\n      // TexShape for float array needs to be the original shape, which byte\n      // array needs to be packed size. This allow the data upload shape to be\n      // matched with texture creation logic.\n      const tempDenseInputTexShape: [number, number] =\n          isByteArray ? [height, width] : texShape;\n      const tempDenseInputHandle =\n          this.makeTensorInfo(tempDenseInputTexShape, dtype);\n      const tempDenseInputTexData =\n          this.texData.get(tempDenseInputHandle.dataId);\n      if (isByteArray) {\n        tempDenseInputTexData.usage = TextureUsage.PIXELS;\n      } else {\n        tempDenseInputTexData.usage = TextureUsage.UPLOAD;\n      }\n      tempDenseInputTexData.texShape = tempDenseInputTexShape;\n      this.gpgpu.uploadDenseMatrixToTexture(\n          this.getTexture(tempDenseInputHandle.dataId), width, height,\n          values as TypedArray);\n\n      const customValues = [[height, width]];\n      // We want the output to remain packed regardless of the value of\n      // WEBGL_PACK.\n      const preventEagerUnpacking = true;\n      const encodedOutputTarget = this.runWebGLProgram(\n          program, [tempDenseInputHandle], dtype, customValues,\n          preventEagerUnpacking);\n\n      // Have the original texture assume the identity of the encoded output.\n      const outputTexData = this.texData.get(encodedOutputTarget.dataId);\n      texData.texShape = outputTexData.texShape;\n      texData.isPacked = outputTexData.isPacked;\n      texData.usage = outputTexData.usage;\n\n      if (!env().get('ENGINE_COMPILE_ONLY')) {\n        texData.texture = outputTexData.texture;\n        // Once uploaded, don't store the values on cpu.\n        texData.values = null;\n        this.texData.delete(encodedOutputTarget.dataId);\n      } else {\n        this.disposeData(encodedOutputTarget.dataId);\n      }\n\n      this.disposeIntermediateTensorInfo(tempDenseInputHandle);\n\n      if (shouldTimeProgram) {\n        this.uploadWaitMs += util.now() - start;\n      }\n    } else {\n      const newTexture = this.acquireTexture(texShape, usage, dtype, isPacked);\n      texData.texture = newTexture;\n    }\n  }\n\n  private convertAndCacheOnCPU(dataId: DataId, float32Values?: Float32Array):\n      TypedArray {\n    const texData = this.texData.get(dataId);\n    const {dtype} = texData;\n\n    if (float32Values != null) {\n      texData.values = float32ToTypedArray(float32Values, dtype as 'float32');\n    }\n    return texData.values as TypedArray;\n  }\n\n  private acquireTexture(\n      texShape: [number, number], texType: TextureUsage, dtype: DataType,\n      isPacked: boolean): Texture {\n    this.numBytesInGPU += this.computeBytes(texShape, dtype);\n    if (!this.warnedAboutMemory &&\n        this.numBytesInGPU > this.numMBBeforeWarning * 1024 * 1024) {\n      const mb = (this.numBytesInGPU / 1024 / 1024).toFixed(2);\n      this.warnedAboutMemory = true;\n      console.warn(\n          `High memory usage in GPU: ${mb} MB, ` +\n          `most likely due to a memory leak`);\n    }\n    return this.textureManager.acquireTexture(texShape, texType, isPacked);\n  }\n\n  private computeBytes(shape: [number, number], dtype: DataType) {\n    return shape[0] * shape[1] * util.bytesPerElement(dtype);\n  }\n\n  checkCompileCompletion() {\n    for (const [, binary] of Object.entries(this.binaryCache)) {\n      this.checkCompletion_(binary);\n    }\n  }\n\n  async checkCompileCompletionAsync(): Promise<boolean[]> {\n    const ps = [];\n    if (this.gpgpu.parallelCompilationExtension) {\n      for (const [, binary] of Object.entries(this.binaryCache)) {\n        ps.push(this.checkCompletionAsync_(binary));\n      }\n      return Promise.all(ps);\n    } else {\n      for (const [, binary] of Object.entries(this.binaryCache)) {\n        const p: Promise<boolean> = new Promise((resolve) => {\n          try {\n            this.checkCompletion_(binary);\n            resolve(true);\n          } catch (error) {\n            throw error;\n          }\n        });\n        ps.push(p);\n      }\n      return Promise.all(ps);\n    }\n  }\n\n  private async checkCompletionAsync_(binary: GPGPUBinary): Promise<boolean> {\n    if (this.gpgpu.gl.getProgramParameter(\n            binary.webGLProgram,\n            this.gpgpu.parallelCompilationExtension.COMPLETION_STATUS_KHR)) {\n      return this.checkCompletion_(binary);\n    } else {\n      await nextFrame();\n      return this.checkCompletionAsync_(binary);\n    }\n  }\n\n  private checkCompletion_(binary: GPGPUBinary): boolean {\n    if (this.gpgpu.gl.getProgramParameter(\n            binary.webGLProgram, this.gpgpu.gl.LINK_STATUS) === false) {\n      console.log(this.gpgpu.gl.getProgramInfoLog(binary.webGLProgram));\n      if (this.gpgpu.gl.getShaderParameter(\n              binary.fragmentShader, this.gpgpu.gl.COMPILE_STATUS) === false) {\n        webgl_util.logShaderSourceAndInfoLog(\n            binary.source,\n            this.gpgpu.gl.getShaderInfoLog(binary.fragmentShader));\n        throw new Error('Failed to compile fragment shader.');\n      }\n      throw new Error('Failed to link vertex and fragment shaders.');\n    }\n    return true;\n  }\n\n  getUniformLocations() {\n    for (const binary of Object.values(this.binaryCache)) {\n      // TODO: Iterating through all binaries to build VAOs is supposed to be in\n      // a seperate function, like 'setVaos'. However, to avoid breaking changes\n      // for the users using parallel compile feature now, buildVao is silently\n      // added here.\n      this.gpgpu.buildVao(binary.webGLProgram);\n\n      const {\n        variablesLocations,\n        customUniformLocations,\n        infLoc,\n        nanLoc,\n        outShapeLocation,\n        outShapeStridesLocation,\n        outTexShapeLocation\n      } = getUniformLocations(this.gpgpu, binary.program, binary.webGLProgram);\n      binary.variablesLocations = variablesLocations;\n      binary.customUniformLocations = customUniformLocations;\n      binary.infLoc = infLoc;\n      binary.nanLoc = nanLoc;\n      binary.outShapeLocation = outShapeLocation;\n      binary.outShapeStridesLocation = outShapeStridesLocation;\n      binary.outTexShapeLocation = outTexShapeLocation;\n    }\n  }\n\n  /**\n   * Create a TF.js tensor out of an existing WebGL texture. A new texture will\n   * be created.\n   */\n  override createTensorFromGPUData(\n      values: WebGLData, shape: number[], dtype: DataType): Tensor {\n    values.channels = values.channels || 'RGBA';\n    const {texture, height, width, channels} = values;\n    const backend = engine().backend as MathBackendWebGL;\n\n    // Have to throw an error, otherwise WebGL just warns and returns wrong\n    // values.\n    if (!backend.gpgpu.gl.isTexture(texture)) {\n      throw new Error(\n          `The texture is invalid. Also, please make sure the texture and ` +\n          `the TFJS WebGL backend are using the same canvas. If you want to ` +\n          `use your own custom canvas, you have to create and use the custom ` +\n          `TFJS WebGL backend created from the canvas through ` +\n          `'new tf.MathBackendWebGL(customCanvas)'.`);\n    }\n\n    const dataId =\n        backend.writeTexture(texture, shape, dtype, height, width, channels);\n    return engine().makeTensorFromDataId(dataId, shape, dtype, backend);\n  }\n}\n\nfunction float32ToTypedArray<D extends NumericDataType>(\n    a: Float32Array, dtype: D): tf.DataTypeMap[D] {\n  if (dtype === 'float32' || dtype === 'complex64') {\n    return a as tf.DataTypeMap[D];\n  } else if (dtype === 'int32' || dtype === 'bool') {\n    const result = (dtype === 'int32') ? new Int32Array(a.length) :\n                                         new Uint8Array(a.length);\n    for (let i = 0; i < result.length; ++i) {\n      result[i] = Math.round(a[i]);\n    }\n    return result as tf.DataTypeMap[D];\n  } else {\n    throw new Error(`Unknown dtype ${dtype}`);\n  }\n}\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAiBA;AACA,OAAO,eAAe;AAGtB,SAAQA,YAAY,EAAiBC,MAAM,EAAUC,WAAW,EAAkCC,MAAM,EAAEC,GAAG,EAAWC,YAAY,EAAEC,aAAa,EAAcC,SAAS,EAAyCC,MAAM,EAAwDC,IAAI,EAA0BC,IAAI,QAAkB,uBAAuB;AAC5V,SAAQC,eAAe,QAAO,eAAe;AAC7C,SAAQC,mBAAmB,QAAO,qBAAqB;AACvD,SAAQC,yBAAyB,QAAO,4BAA4B;AACpE,SAAQC,kBAAkB,QAAO,oBAAoB;AACrD,SAAQC,wBAAwB,QAAO,2BAA2B;AAClE,SAAQC,mBAAmB,QAAO,qBAAqB;AACvD,SAAQC,yBAAyB,QAAO,4BAA4B;AACpE,SAAQC,YAAY,QAAO,iBAAiB;AAC5C,OAAO,KAAKC,UAAU,MAAM,cAAc;AAC1C,SAAQC,mBAAmB,QAA8C,cAAc;AACvF,SAAQC,gBAAgB,QAAO,uBAAuB;AACtD,SAAQC,WAAW,QAAO,YAAY;AACtC,SAAQC,oBAAoB,QAAO,sBAAsB;AACzD,OAAO,KAAKC,QAAQ,MAAM,YAAY;AACtC,SAA8BC,YAAY,QAAO,YAAY;AAC7D,SAAQC,cAAc,QAAO,mBAAmB;AAChD,OAAO,KAAKC,QAAQ,MAAM,eAAe;AACzC,SAAQC,cAAc,QAAO,eAAe;AAC5C,SAAQC,oBAAoB,QAAO,sBAAsB;AACzD,SAAQC,aAAa,QAAO,cAAc;AAC1C,OAAO,KAAKC,UAAU,MAAM,cAAc;AAE1C,MAAMC,SAAS,GAAG3B,YAAY,CAAC2B,SAAS;AAExC,OAAO,MAAMC,eAAe,GAAG,IAAI;AACnC,OAAO,MAAMC,eAAe,GAAG,IAAI;AA4BnC,MAAMC,YAAY,GAA2D,EAAE;AAE/E,OAAM,SAAUC,cAAcA,CAACC,YAAoB;EACjD,IAAIA,YAAY,IAAIF,YAAY,EAAE;IAChC,OAAOA,YAAY,CAACE,YAAY,CAAC;;EAEnCF,YAAY,CAACE,YAAY,CAAC,GAAG,EAAE;EAC/B,OAAOF,YAAY,CAACE,YAAY,CAAC;AACnC;AAEA;AACA;AACA,MAAMC,0BAA0B,GAC5BlC,GAAG,EAAE,CAACmC,SAAS,CAAC,4BAA4B,CAAC;AAEjD;AACA;AACA;AACA,MAAMC,sBAAsB,GAAG,GAAG;AAClC,SAASC,kBAAkBA,CAAA;EACzB,IAAIrC,GAAG,EAAE,CAACsC,MAAM,CAACC,MAAM,IAAI,IAAI,EAAE;IAC/B,OAAO,IAAI,CAAC,CAAE;;EAEhB,OAAQvC,GAAG,EAAE,CAACsC,MAAM,CAACC,MAAM,CAACC,MAAM,GAAGxC,GAAG,EAAE,CAACsC,MAAM,CAACC,MAAM,CAACE,KAAK,GACtDC,MAAM,CAACC,gBAAgB,GAC3BP,sBAAsB,GAAG,IAAI,GAAG,IAAI;AAC1C;AAEA,MAAaQ,gBAAiB,SAAQ1C,aAAa;EAKzC2C,UAAUA,CAAA;IAChB,OAAOD,gBAAgB,CAACC,UAAU,EAAE;EACtC;EAiCAC,YAAYC,WAA4D;IACtE,KAAK,EAAE;IAjCT;IACQ,KAAAC,WAAW,GAAG,IAAIC,OAAO,EAA4C;IAC7E;IACA;IACQ,KAAAC,eAAe,GAAG,IAAIC,OAAO,EAAU;IAE/C;IACA;IACA,KAAAC,YAAY,GAAG,IAAIH,OAAO,EAAkB;IACpC,KAAAI,aAAa,GAAG,CAAC;IAMzB;IACQ,KAAAC,YAAY,GAAG,CAAC;IACxB;IACQ,KAAAC,cAAc,GAAG,CAAC;IAE1B;IACQ,KAAAC,eAAe,GAAG,CAAC;IASnB,KAAAC,iBAAiB,GAAG,KAAK;IAkfzB,KAAAC,cAAc,GAAG,CAAC;IAgZlB,KAAAC,QAAQ,GAAG,KAAK;IA93BtB,IAAI,CAAC3D,GAAG,EAAE,CAAC4D,OAAO,CAAC,WAAW,CAAC,EAAE;MAC/B,MAAM,IAAIC,KAAK,CAAC,uCAAuC,CAAC;;IAG1D,IAAIC,QAAQ;IACZ,IAAIf,WAAW,IAAI,IAAI,EAAE;MACvB,IAAIA,WAAW,YAAYjC,YAAY,EAAE;QACvCgD,QAAQ,GAAGf,WAAW;OACvB,MAAM;QACL,MAAMgB,EAAE,GACJxD,eAAe,CAACP,GAAG,EAAE,CAACmC,SAAS,CAAC,eAAe,CAAC,EAAEY,WAAW,CAAC;QAClEe,QAAQ,GAAG,IAAIhD,YAAY,CAACiD,EAAE,CAAC;;MAEjC,IAAI,CAACC,WAAW,GAAG,EAAE;MACrB,IAAI,CAACC,mBAAmB,GAAG,KAAK;KACjC,MAAM;MACL,MAAMF,EAAE,GAAGxD,eAAe,CAACP,GAAG,EAAE,CAACmC,SAAS,CAAC,eAAe,CAAC,CAAC;MAC5D2B,QAAQ,GAAG,IAAIhD,YAAY,CAACiD,EAAE,CAAC;MAC/B,IAAI,CAACC,WAAW,GAAGhC,cAAc,CAAChC,GAAG,EAAE,CAACmC,SAAS,CAAC,eAAe,CAAC,CAAC;MACnE,IAAI,CAAC8B,mBAAmB,GAAG,IAAI;;IAGjC,IAAI,CAACC,KAAK,GAAGJ,QAAQ;IACrB,IAAI,CAACK,MAAM,GAAG,IAAI,CAACD,KAAK,CAACH,EAAE,CAACI,MAAM;IAClC,IAAI,CAACC,cAAc,GAAG,IAAI9C,cAAc,CAAC,IAAI,CAAC4C,KAAK,CAAC;IACpD,IAAI,CAAC7B,kBAAkB,GAAGA,kBAAkB,EAAE;IAC9C,IAAI,CAACgC,OAAO,GAAG,IAAIvE,WAAW,CAAC,IAAI,EAAEC,MAAM,EAAE,CAAC;EAChD;EAESuE,UAAUA,CAAA;IACjB,OAAO,IAAI,CAACD,OAAO,CAACC,UAAU,EAAE,GAAG,IAAI,CAACZ,cAAc;EACxD;EAEA;EACA;EACAa,YAAYA,CACRC,OAAqB,EAAEC,KAAe,EAAEC,KAAe,EACvDC,SAAiB,EAAEC,QAAgB,EAAEC,QAAgB;IACvD;IACA;IACA,MAAMC,KAAK,GAAG,IAAI,CAACC,cAAc,CAACN,KAAK,EAAEC,KAAK,CAAC;IAC/C,MAAMM,MAAM,GAAG,IAAI,CAACX,OAAO,CAACY,GAAG,CAACH,KAAK,CAACI,MAAM,CAAC;IAC7C;IACA;IACAF,MAAM,CAACG,QAAQ,GAAG,KAAK;IAEvB;IACAH,MAAM,CAACR,OAAO,GAAG;MAACA,OAAO;MAAEY,QAAQ,EAAE,CAACT,SAAS,EAAEC,QAAQ;IAAC,CAAC;IAC3DI,MAAM,CAACI,QAAQ,GAAG,CAACT,SAAS,EAAEC,QAAQ,CAAC;IAEvC,MAAMS,SAAS,GAAG1D,UAAU,CAAC2D,YAAY,CAACb,KAAK,CAAC;IAChD,MAAMc,OAAO,GACT,IAAI3E,mBAAmB,CAACyE,SAAS,EAAE,KAAK,CAAC,mBAAmBR,QAAQ,CAAC;IACzE,MAAMW,MAAM,GACR,IAAI,CAACC,eAAe,CAACF,OAAO,EAAE,CAACT,KAAK,CAAC,EAAEJ,KAAK,EAAE,CAAC,CAACC,SAAS,EAAEC,QAAQ,CAAC,CAAC,CAAC;IAC1EY,MAAM,CAACf,KAAK,GAAGA,KAAK;IAEpB;IACA;IACAO,MAAM,CAACR,OAAO,GAAG,IAAI;IACrB,IAAI,CAACkB,6BAA6B,CAACZ,KAAK,CAAC;IAEzC,OAAOU,MAAM,CAACN,MAAM;EACtB;EAESS,KAAKA,CAACC,MAAqB,EAAEnB,KAAe,EAAEC,KAAe;IAEpE,IAAI1E,GAAG,EAAE,CAAC4D,OAAO,CAAC,gCAAgC,CAAC,IAC/C5D,GAAG,EAAE,CAAC4D,OAAO,CAAC,OAAO,CAAC,EAAE;MAC1B,IAAI,CAACiC,sBAAsB,CAACD,MAAM,CAAC;;IAErC,IAAIlB,KAAK,KAAK,WAAW,IAAIkB,MAAM,IAAI,IAAI,EAAE;MAC3C,MAAM,IAAI/B,KAAK,CACX,qCAAqC,GACrC,oCAAoC,CAAC;;IAE3C,MAAMqB,MAAM,GAAG;MAACY,EAAE,EAAE,IAAI,CAACjD,UAAU;IAAE,CAAC;IACtC,IAAI,CAACwB,OAAO,CAAC0B,GAAG,CACZb,MAAM,EACN;MAACT,KAAK;MAAEC,KAAK;MAAEkB,MAAM;MAAEI,KAAK,EAAE3E,YAAY,CAAC4E,MAAM;MAAEC,QAAQ,EAAE;IAAC,CAAC,CAAC;IACpE,OAAOhB,MAAM;EACf;EAEA;EACSgB,QAAQA,CAAChB,MAAc;IAC9B,IAAI,IAAI,CAACb,OAAO,CAAC8B,GAAG,CAACjB,MAAM,CAAC,EAAE;MAC5B,MAAMkB,UAAU,GAAG,IAAI,CAAC/B,OAAO,CAACY,GAAG,CAACC,MAAM,CAAC;MAC3C,OAAOkB,UAAU,CAACF,QAAQ;;IAE5B,OAAO,CAAC;EACV;EAEA;EACSG,MAAMA,CAACnB,MAAc;IAC5B,MAAMb,OAAO,GAAG,IAAI,CAACA,OAAO,CAACY,GAAG,CAACC,MAAM,CAAC;IACxCb,OAAO,CAAC6B,QAAQ,EAAE;EACpB;EAEA;EACAI,MAAMA,CAACpB,MAAc;IACnB,IAAI,IAAI,CAACb,OAAO,CAAC8B,GAAG,CAACjB,MAAM,CAAC,EAAE;MAC5B,MAAMb,OAAO,GAAG,IAAI,CAACA,OAAO,CAACY,GAAG,CAACC,MAAM,CAAC;MACxCb,OAAO,CAAC6B,QAAQ,EAAE;;EAEtB;EAESK,IAAIA,CACTrB,MAAc,EAAEU,MAAqB,EAAEnB,KAAe,EAAEC,KAAe,EACvEwB,QAAgB;IAClB,IAAIlG,GAAG,EAAE,CAAC4D,OAAO,CAAC,OAAO,CAAC,EAAE;MAC1B,IAAI,CAACiC,sBAAsB,CAACD,MAAM,CAAC;;IAErC,IAAIlB,KAAK,KAAK,WAAW,EAAE;MACzB,MAAM,IAAIb,KAAK,CACX,qCAAqC,GACrC,oCAAoC,CAAC;;IAE3C,IAAI,CAACQ,OAAO,CAAC0B,GAAG,CACZb,MAAM,EAAE;MAACT,KAAK;MAAEC,KAAK;MAAEkB,MAAM;MAAEI,KAAK,EAAE3E,YAAY,CAAC4E,MAAM;MAAEC;IAAQ,CAAC,CAAC;EAC3E;EAEAR,6BAA6BA,CAACc,UAAsB;IAClD,IAAI,CAACC,WAAW,CAACD,UAAU,CAACtB,MAAM,CAAC;EACrC;EAESwB,QAAQA,CAACxB,MAAc;IAC9B,MAAMb,OAAO,GAAG,IAAI,CAACA,OAAO,CAACY,GAAG,CAACC,MAAM,CAAC;IACxC,MAAM;MAACU,MAAM;MAAElB,KAAK;MAAEiC,kBAAkB;MAAEC,KAAK;MAAEnC,KAAK;MAAEU;IAAQ,CAAC,GAAGd,OAAO;IAE3E;IACA;IACA;IACA,IAAIuC,KAAK,IAAI,IAAI,EAAE;MACjB,IAAIrB,OAAO;MACX,IAAIJ,QAAQ,EAAE;QACZI,OAAO,GAAG,IAAI9D,oBAAoB,CAACgD,KAAK,EAAElD,QAAQ,CAACsF,KAAK,CAAC;OAC1D,MAAM;QACLtB,OAAO,GAAG,IAAI/D,cAAc,CAACiD,KAAK,EAAElD,QAAQ,CAACsF,KAAK,CAAC;;MAErD,MAAMC,GAAG,GACL,IAAI,CAACrB,eAAe,CAACF,OAAO,EAAE,CAAC;QAACL,MAAM;QAAET,KAAK;QAAEC;MAAK,CAAC,CAAC,EAAEA,KAAK,CAAC;MAClE,MAAMqC,IAAI,GAAG,IAAI,CAACL,QAAQ,CAACI,GAAG,CAAC5B,MAAM,CAAC;MACtC,IAAI,CAACQ,6BAA6B,CAACoB,GAAG,CAAC;MACvC,OAAOC,IAAI;;IAEb,IAAInB,MAAM,IAAI,IAAI,EAAE;MAClB,OAAO,IAAI,CAACoB,oBAAoB,CAAC9B,MAAM,CAAC;;IAE1C,IAAIR,KAAK,KAAK,QAAQ,EAAE;MACtB,OAAOkB,MAAM;;IAEf,MAAMqB,iBAAiB,GAAG,IAAI,CAACC,YAAY,IAAI,IAAI;IACnD,IAAIC,KAAa;IACjB,IAAIF,iBAAiB,EAAE;MACrBE,KAAK,GAAG7G,IAAI,CAAC8G,GAAG,EAAE;;IAGpB,IAAIC,MAAoB;IACxB,IAAI3C,KAAK,KAAK,WAAW,EAAE;MACzB,MAAM4C,UAAU,GACZ,IAAI,CAACZ,QAAQ,CAACC,kBAAkB,CAACY,IAAI,CAACrC,MAAM,CAAiB;MACjE,MAAMsC,UAAU,GACZ,IAAI,CAACd,QAAQ,CAACC,kBAAkB,CAACc,IAAI,CAACvC,MAAM,CAAiB;MACjEmC,MAAM,GAAGzH,YAAY,CAAC8H,sBAAsB,CAACJ,UAAU,EAAEE,UAAU,CAAC;KACrE,MAAM;MACLH,MAAM,GAAG,IAAI,CAACM,oBAAoB,CAACzC,MAAM,CAAC;;IAG5C,IAAI+B,iBAAiB,EAAE;MACrB,IAAI,CAAC1D,cAAc,IAAIjD,IAAI,CAAC8G,GAAG,EAAE,GAAGD,KAAK;;IAE3C,OAAO,IAAI,CAACH,oBAAoB,CAAC9B,MAAM,EAAEmC,MAAM,CAAC;EAClD;EAES,MAAMO,IAAIA,CAAC1C,MAAc;IAChC,IAAI,IAAI,CAAClC,WAAW,CAACmD,GAAG,CAACjB,MAAM,CAAC,EAAE;MAChC,MAAM2C,WAAW,GAAG,IAAI,CAAC7E,WAAW,CAACiC,GAAG,CAACC,MAAM,CAAC;MAChD,OAAO,IAAI4C,OAAO,CAAaC,OAAO,IAAIF,WAAW,CAACG,IAAI,CAACD,OAAO,CAAC,CAAC;;IAEtE,MAAM1D,OAAO,GAAG,IAAI,CAACA,OAAO,CAACY,GAAG,CAACC,MAAM,CAAC;IACxC,MAAM;MAACU,MAAM;MAAEnB,KAAK;MAAEmC,KAAK;MAAElC,KAAK;MAAEiC,kBAAkB;MAAExB;IAAQ,CAAC,GAAGd,OAAO;IAE3E;IACA;IACA;IACA,IAAIuC,KAAK,IAAI,IAAI,EAAE;MACjB,IAAIrB,OAAO;MACX,IAAIJ,QAAQ,EAAE;QACZI,OAAO,GAAG,IAAI9D,oBAAoB,CAACgD,KAAK,EAAElD,QAAQ,CAACsF,KAAK,CAAC;OAC1D,MAAM;QACLtB,OAAO,GAAG,IAAI/D,cAAc,CAACiD,KAAK,EAAElD,QAAQ,CAACsF,KAAK,CAAC;;MAErD,MAAMC,GAAG,GACL,IAAI,CAACrB,eAAe,CAACF,OAAO,EAAE,CAAC;QAACL,MAAM;QAAET,KAAK;QAAEC;MAAK,CAAC,CAAC,EAAEA,KAAK,CAAC;MAClE,MAAMqC,IAAI,GAAG,IAAI,CAACa,IAAI,CAACd,GAAG,CAAC5B,MAAM,CAAC;MAClC,IAAI,CAACQ,6BAA6B,CAACoB,GAAG,CAAC;MACvC,OAAOC,IAAI;;IAGb,IAAInB,MAAM,IAAI,IAAI,EAAE;MAClB,OAAO,IAAI,CAACoB,oBAAoB,CAAC9B,MAAM,CAAC;;IAG1C,IAAIlF,GAAG,EAAE,CAAC4D,OAAO,CAAC,OAAO,CAAC,EAAE;MAC1B;MACA;MACA;MACA,IAAI,CAAC5D,GAAG,EAAE,CAAC4D,OAAO,CAAC,8BAA8B,CAAC,IAC9C5D,GAAG,EAAE,CAACmC,SAAS,CAAC,eAAe,CAAC,KAAK,CAAC,EAAE;QAC1C,MAAM,IAAI0B,KAAK,CACX,4DAA4D,GAC5D,oCAAoC,CAAC;;;IAI7C,IAAIhE,MAAM,GAAgB,IAAI;IAC9B,IAAIoI,iBAA6B;IAEjC,IAAIvD,KAAK,KAAK,WAAW,IAAI1E,GAAG,EAAE,CAACiF,GAAG,CAAC,wBAAwB,CAAC,EAAE;MAChE;MACAgD,iBAAiB,GAAG,IAAI,CAACC,MAAM,CAAChD,MAAM,CAAC;MACvC,MAAMiD,OAAO,GAAG,IAAI,CAAC9D,OAAO,CAACY,GAAG,CAACgD,iBAAiB,CAAC/C,MAAM,CAAC;MAE1DrF,MAAM,GAAG,IAAI,CAACqE,KAAK,CAACkE,uBAAuB,CACvCD,OAAO,CAAC3D,OAAO,CAACA,OAAO,EAAE,GAAGpD,QAAQ,CAACiH,gBAAgB,CAAC5D,KAAK,CAAC,CAAC;;IAGnE,IAAI,CAACzB,WAAW,CAAC+C,GAAG,CAACb,MAAM,EAAE,EAAE,CAAC;IAEhC,IAAIR,KAAK,KAAK,WAAW,EAAE;MACzB;MACA,MAAM,IAAI,CAACR,KAAK,CAACoE,qBAAqB,EAAE;;IAG1C;IACA,IAAIC,IAAkB;IACtB,IAAI7D,KAAK,KAAK,WAAW,EAAE;MACzB,MAAM8D,EAAE,GAAG,MAAMV,OAAO,CAACW,GAAG,CAAC,CAC3B,IAAI,CAACb,IAAI,CAACjB,kBAAkB,CAACY,IAAI,CAACrC,MAAM,CAAC,EACzC,IAAI,CAAC0C,IAAI,CAACjB,kBAAkB,CAACc,IAAI,CAACvC,MAAM,CAAC,CAC1C,CAAC;MAEF,MAAMoC,UAAU,GAAGkB,EAAE,CAAC,CAAC,CAAC;MACxB,MAAMhB,UAAU,GAAGgB,EAAE,CAAC,CAAC,CAAC;MACxBD,IAAI,GAAG3I,YAAY,CAAC8H,sBAAsB,CACtCJ,UAA0B,EAAEE,UAA0B,CAAC;KAC5D,MAAM,IAAI3H,MAAM,IAAI,IAAI,EAAE;MACzB0I,IAAI,GAAG,IAAI,CAACZ,oBAAoB,CAACzC,MAAM,CAAC;KACzC,MAAM;MACL,MAAMwD,IAAI,GAAGpI,IAAI,CAACqI,aAAa,CAAClE,KAAK,CAAC;MACtC8D,IAAI,GAAG,IAAI,CAACrE,KAAK,CAAC0E,+BAA+B,CAAC/I,MAAM,EAAE6I,IAAI,CAAC;;IAEjE,IAAIT,iBAAiB,IAAI,IAAI,EAAE;MAC7B,IAAI,CAACvC,6BAA6B,CAACuC,iBAAiB,CAAC;;IAEvD,IAAIpI,MAAM,IAAI,IAAI,EAAE;MAClB,MAAMkE,EAAE,GAAG,IAAI,CAACG,KAAK,CAACH,EAAE;MACxBpC,UAAU,CAACkH,YAAY,CAAC9E,EAAE,EAAE,MAAMA,EAAE,CAAC+E,YAAY,CAACjJ,MAAM,CAAC,CAAC;;IAE5D,MAAMkJ,SAAS,GAAG,IAAI,CAAC/B,oBAAoB,CAAC9B,MAAM,EAAEqD,IAAI,CAAC;IAEzD,MAAMV,WAAW,GAAG,IAAI,CAAC7E,WAAW,CAACiC,GAAG,CAACC,MAAM,CAAC;IAChD,IAAI,CAAClC,WAAW,CAACgG,MAAM,CAAC9D,MAAM,CAAC;IAE/B;IACA2C,WAAW,CAACoB,OAAO,CAAClB,OAAO,IAAIA,OAAO,CAACgB,SAAS,CAAC,CAAC;IAClD,IAAI,IAAI,CAAC7F,eAAe,CAACiD,GAAG,CAACjB,MAAM,CAAC,EAAE;MACpC,IAAI,CAAChC,eAAe,CAAC8F,MAAM,CAAC9D,MAAM,CAAC;MACnC,IAAI,IAAI,CAACuB,WAAW,CAACvB,MAAM,CAAC,EAAE;QAC5BnF,MAAM,EAAE,CAACmJ,YAAY,CAAChE,MAAM,EAAE,IAAI,CAAC;;MAErC,IAAI,CAACxB,cAAc,EAAE;;IAEvB,OAAOqF,SAAS;EAClB;EAEA;;;;;;;EAOSI,SAASA,CAACjE,MAAc,EAAEkE,OAAA,GAAgC,EAAE;IAEnE,MAAM/E,OAAO,GAAG,IAAI,CAACA,OAAO,CAACY,GAAG,CAACC,MAAM,CAAC;IACxC,MAAM;MAACU,MAAM;MAAEnB,KAAK;MAAEmC,KAAK;MAAElC,KAAK;MAAES,QAAQ;MAAEX;IAAO,CAAC,GAAGH,OAAO;IAEhE,IAAIK,KAAK,KAAK,WAAW,EAAE;MACzB,MAAM,IAAIb,KAAK,CAAC,uDAAuD,CAAC;;IAG1E;IACA;IACA;IACA,IAAI+C,KAAK,IAAI,IAAI,EAAE;MACjB,IAAIrB,OAAO;MACX,IAAIJ,QAAQ,EAAE;QACZI,OAAO,GAAG,IAAI9D,oBAAoB,CAACgD,KAAK,EAAElD,QAAQ,CAACsF,KAAK,CAAC;OAC1D,MAAM;QACLtB,OAAO,GAAG,IAAI/D,cAAc,CAACiD,KAAK,EAAElD,QAAQ,CAACsF,KAAK,CAAC;;MAErD,MAAMC,GAAG,GACL,IAAI,CAACrB,eAAe,CAACF,OAAO,EAAE,CAAC;QAACL,MAAM;QAAET,KAAK;QAAEC;MAAK,CAAC,CAAC,EAAEA,KAAK,CAAC;MAClE,MAAM2E,YAAY,GAAG,IAAI,CAACF,SAAS,CAACrC,GAAG,EAAEsC,OAAO,CAAC;MACjD,IAAI,CAAC1D,6BAA6B,CAACoB,GAAG,CAAC;MACvC,OAAOuC,YAAY;;IAGrB,IAAI7E,OAAO,IAAI,IAAI,EAAE;MACnB,IAAIoB,MAAM,IAAI,IAAI,EAAE;QAClB,MAAM,IAAI/B,KAAK,CAAC,gCAAgC,CAAC;OAClD,MAAM;QACL,MAAM,IAAIA,KAAK,CAAC,iCAAiC,CAAC;;;IAItD;IACA,MAAMyF,SAAS,GAAG,IAAI,CAACpB,MAAM,CAAChD,MAAM,EAAEkE,OAAO,CAACG,cAAc,CAAC;IAE7D;IACA,MAAMC,SAAS,GAAGzJ,MAAM,EAAE,CAAC0J,wBAAwB,CAACH,SAAS,CAAC;IAE9D,MAAMnB,OAAO,GAAG,IAAI,CAAC9D,OAAO,CAACY,GAAG,CAACqE,SAAS,CAACpE,MAAM,CAAC;IAClD,OAAAwE,MAAA,CAAAC,MAAA;MAAQH;IAAS,GAAKrB,OAAO,CAAC3D,OAAO;EACvC;EAEAoF,UAAUA,CAAqCC,CAAa;IAE1D,MAAM9C,IAAI,GAAG,IAAI,CAACL,QAAQ,CAACmD,CAAC,CAAC3E,MAAM,CAAC;IACpC,IAAI2E,CAAC,CAACnF,KAAK,KAAK,QAAQ,EAAE;MACxB,IAAI;QACF;QACA,MAAMoF,OAAO,GAAI/C,IAAqB,CAACgD,GAAG,CAACC,CAAC,IAAI1J,IAAI,CAAC2J,YAAY,CAACD,CAAC,CAAC,CAAC;QACrE,OAAOnK,MAAM,CAACgK,CAAC,CAACpF,KAAoB,EAAEoF,CAAC,CAACnF,KAAK,EAAEoF,OAAO,CAChC;OACvB,CAAC,OAAAI,EAAA,EAAM;QACN,MAAM,IAAIrG,KAAK,CAAC,kDAAkD,CAAC;;;IAGvE,OAAOhE,MAAM,CAACgK,CAAC,CAACpF,KAAoB,EAAEoF,CAAC,CAACnF,KAAK,EAAEqC,IAAkB,CAC3C;EACxB;EAEQlB,sBAAsBA,CAACD,MAAqB;IAClD,IAAIA,MAAM,IAAI,IAAI,EAAE;MAClB;;IAEF,KAAK,IAAIuE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGvE,MAAM,CAACwE,MAAM,EAAED,CAAC,EAAE,EAAE;MACtC,MAAME,GAAG,GAAGzE,MAAM,CAACuE,CAAC,CAAW;MAC/B,IAAI,CAACxI,UAAU,CAAC2I,gBAAgB,CAACD,GAAG,CAAC,EAAE;QACrC,IAAIrK,GAAG,EAAE,CAAC4D,OAAO,CAAC,8BAA8B,CAAC,EAAE;UACjD,MAAMC,KAAK,CACP,aAAawG,GAAG,mCAAmC,GACnD,yDAAyD,GACzD,uDAAuD,CAAC;;QAE9D,MAAMxG,KAAK,CAAC,aAAawG,GAAG,wCAAwC,CAAC;;;EAG3E;EAEQ1C,oBAAoBA,CAACzC,MAAc;IACzC,MAAM;MAACT,KAAK;MAAEC,KAAK;MAAES;IAAQ,CAAC,GAAG,IAAI,CAACd,OAAO,CAACY,GAAG,CAACC,MAAM,CAAC;IACzD,MAAMwD,IAAI,GAAGpI,IAAI,CAACqI,aAAa,CAAClE,KAAK,CAAC;IACtC,IAAIzE,GAAG,EAAE,CAAC4D,OAAO,CAAC,8BAA8B,CAAC,EAAE;MACjD,MAAM0F,SAAS,GAAG,IAAI,CAACpB,MAAM,CAAChD,MAAM,CAAC;MACrC,MAAMiD,OAAO,GAAG,IAAI,CAAC9D,OAAO,CAACY,GAAG,CAACqE,SAAS,CAACpE,MAAM,CAAC;MAClD,MAAMqD,IAAI,GACN,IAAI,CAACrE,KAAK,CACLqG,+BAA+B,CAC5BpC,OAAO,CAAC3D,OAAO,CAACA,OAAO,EAAE,GAAGpD,QAAQ,CAACiH,gBAAgB,CAAC5D,KAAK,CAAC,CAAC,CAChE+F,QAAQ,CAAC,CAAC,EAAE9B,IAAI,CAAC;MAE1B,IAAI,CAAChD,6BAA6B,CAAC4D,SAAS,CAAC;MAE7C,OAAOf,IAAI;;IAGb,MAAMkC,sBAAsB,GACxBzK,GAAG,EAAE,CAAC4D,OAAO,CAAC,YAAY,CAAC,IAAIuB,QAAQ,KAAK,IAAI;IACpD,MAAMuF,WAAW,GACbD,sBAAsB,GAAG9I,UAAU,CAAC2D,YAAY,CAACb,KAAK,CAAC,GAAGA,KAAK;IACnE,MAAMc,OAAO,GAAGkF,sBAAsB,GAClC,IAAI9J,wBAAwB,CAAC+J,WAAuC,CAAC,GACrE,IAAIhK,kBAAkB,CAACgK,WAAW,CAAC;IACvC,MAAMlF,MAAM,GAAG,IAAI,CAACC,eAAe,CAC/BF,OAAO,EAAE,CAAC;MAACd,KAAK,EAAEiG,WAAW;MAAEhG,KAAK;MAAEQ;IAAM,CAAC,CAAC,EAAE,SAAS,CAAC;IAC9D,MAAMiD,OAAO,GAAG,IAAI,CAAC9D,OAAO,CAACY,GAAG,CAACO,MAAM,CAACN,MAAM,CAAC;IAC/C,MAAMqD,IAAI,GAAG,IAAI,CAACrE,KAAK,CACLyG,+CAA+C,CAC5CxC,OAAO,CAAC3D,OAAO,CAACA,OAAO,EAAE2D,OAAO,CAAC/C,QAAQ,CAAC,CAAC,CAAC,EAC5C+C,OAAO,CAAC/C,QAAQ,CAAC,CAAC,CAAC,CAAC,CACvBoF,QAAQ,CAAC,CAAC,EAAE9B,IAAI,CAAC;IACnC,IAAI,CAAChD,6BAA6B,CAACF,MAAM,CAAC;IAE1C,OAAO+C,IAAI;EACb;EAESqC,cAAcA,CAAA;IACrB,OAAO5K,GAAG,EAAE,CAACmC,SAAS,CAAC,+CAA+C,CAAC,GAAG,CAAC;EAC7E;EAES0I,IAAIA,CAACC,CAAa;IACzB,MAAMC,eAAe,GAAG,IAAI,CAAC7D,YAAY;IACzC,MAAM8D,eAAe,GAAgB,EAAE;IAEvC,IAAIC,aAAa,GAAG,KAAK;IACzB,IAAI,IAAI,CAACC,kBAAkB,IAAI,IAAI,EAAE;MACnC,IAAI,CAACA,kBAAkB,GAAGF,eAAe;MACzCC,aAAa,GAAG,IAAI;KACrB,MAAM;MACL,IAAI,CAAC/D,YAAY,CAACc,IAAI,CAACgD,eAAe,CAAC;;IAEzC,IAAI,CAAC9D,YAAY,GAAG8D,eAAe;IAEnCF,CAAC,EAAE;IAEH;IACA,MAAMK,2BAA2B,GAC7B7K,IAAI,CAAC8K,OAAO,CAAC,IAAI,CAAClE,YAAY,CAAC6C,GAAG,CAAEC,CAAa,IAAKA,CAAC,CAACqB,KAAK,CAAC,CAAC,CAC1DC,MAAM,CAACtB,CAAC,IAAIA,CAAC,IAAI,IAAI,CAAC;IAC/B,MAAMuB,yBAAyB,GAC3BjL,IAAI,CAAC8K,OAAO,CAAC,IAAI,CAAClE,YAAY,CAAC6C,GAAG,CAAEC,CAAa,IAAKA,CAAC,CAACwB,IAAI,CAAC,CAAC,CACzDF,MAAM,CAACtB,CAAC,IAAIA,CAAC,IAAI,IAAI,CAAC;IAE/B,IAAI,CAAC9C,YAAY,GAAG6D,eAAe;IAEnC,IAAIE,aAAa,EAAE;MACjB,IAAI,CAACC,kBAAkB,GAAG,IAAI;;IAGhC,MAAMpE,GAAG,GAAoB;MAC3BxD,YAAY,EAAE,IAAI,CAACA,YAAY;MAC/BC,cAAc,EAAE,IAAI,CAACA,cAAc;MACnCkI,QAAQ,EAAE,IAAI;MACdC,MAAM,EAAE,IAAI,CAAE;KACf;IAED,OAAO,CAAC,YAAW;MACjB,IAAI1L,GAAG,EAAE,CAACmC,SAAS,CAAC,+CAA+C,CAAC,GAChE,CAAC,EAAE;QACL,MAAMsJ,QAAQ,GAAG,MAAM3D,OAAO,CAACW,GAAG,CAAC0C,2BAA2B,CAAC;QAE/DrE,GAAG,CAAC,UAAU,CAAC,GAAGxG,IAAI,CAACqL,GAAG,CAACF,QAAQ,CAAC;QACpC3E,GAAG,CAAC,qBAAqB,CAAC,GAAG,MACzB2E,QAAQ,CACH1B,GAAG,CAAC,CAACC,CAAC,EAAEG,CAAC,MAAM;UAACqB,IAAI,EAAED,yBAAyB,CAACpB,CAAC,CAAC;UAAEyB,EAAE,EAAE5B;QAAC,CAAC,CAAC,CAAC,CAC5DD,GAAG,CAACC,CAAC,IAAI,GAAGA,CAAC,CAACwB,IAAI,KAAKxB,CAAC,CAAC4B,EAAE,EAAE,CAAC,CAC9BC,IAAI,CAAC,IAAI,CAAC;OACpB,MAAM;QACL/E,GAAG,CAAC,UAAU,CAAC,GAAG;UAChBgF,KAAK,EAAE;SACR;;MAGH,IAAI,CAACxI,YAAY,GAAG,CAAC;MACrB,IAAI,CAACC,cAAc,GAAG,CAAC;MACvB,OAAOuD,GAAG;IACZ,CAAC,EAAC,CAAE;EACN;EACSiF,MAAMA,CAAA;IACb,OAAO;MACLC,UAAU,EAAE,KAAK;MACjB3I,aAAa,EAAE,IAAI,CAACA,aAAa;MACjC4I,sBAAsB,EAAE,IAAI,CAAC7H,cAAc,CAAC8H,iBAAiB;MAC7DC,iBAAiB,EAAE,IAAI,CAAC/H,cAAc,CAACgI;KACrB;EACtB;EAEQC,UAAUA,CAAA;IAChB,IAAIrM,GAAG,EAAE,CAACmC,SAAS,CAAC,+CAA+C,CAAC,GAAG,CAAC,EAAE;MACxE,OAAO,IAAI,CAAC+B,KAAK,CAACoI,UAAU,EAAE;;IAEhC,OAAO;MAACC,OAAO,EAAEjM,IAAI,CAAC8G,GAAG,EAAE;MAAEoF,KAAK,EAAE;IAAI,CAAC;EAC3C;EAEQC,QAAQA,CAACpB,KAA+B;IAC9C,IAAIrL,GAAG,EAAE,CAACmC,SAAS,CAAC,+CAA+C,CAAC,GAAG,CAAC,EAAE;MACxE,IAAI,CAAC+B,KAAK,CAACwI,QAAQ,EAAE;MACrB,OAAOrB,KAAK;;IAEbA,KAAuB,CAACmB,KAAK,GAAGlM,IAAI,CAAC8G,GAAG,EAAE;IAC3C,OAAOiE,KAAK;EACd;EAEQ,MAAMsB,YAAYA,CAACtB,KAA+B;IACxD,IAAIrL,GAAG,EAAE,CAACmC,SAAS,CAAC,+CAA+C,CAAC,GAAG,CAAC,EAAE;MACxE,OAAO,IAAI,CAAC+B,KAAK,CAAC0I,sBAAsB,CAACvB,KAAmB,CAAC;;IAE/D,MAAMwB,UAAU,GAAGxB,KAAsB;IACzC,OAAOwB,UAAU,CAACL,KAAK,GAAGK,UAAU,CAACN,OAAO;EAC9C;EAIA;;;;;;;;;;EAUS9F,WAAWA,CAACvB,MAAc,EAAE4H,KAAK,GAAG,KAAK;IAChD,IAAI,IAAI,CAAC5J,eAAe,CAACiD,GAAG,CAACjB,MAAM,CAAC,EAAE;MACpC,OAAO,KAAK;;IAGd;IACA,IAAI,CAAC,IAAI,CAACb,OAAO,CAAC8B,GAAG,CAACjB,MAAM,CAAC,EAAE;MAC7B,OAAO,IAAI;;IAGb;IACA;IACA;IACA,IAAI4H,KAAK,EAAE;MACT,IAAI,CAACzI,OAAO,CAACY,GAAG,CAACC,MAAM,CAAC,CAACgB,QAAQ,GAAG,CAAC;KACtC,MAAM;MACL,IAAI,CAAC7B,OAAO,CAACY,GAAG,CAACC,MAAM,CAAC,CAACgB,QAAQ,EAAE;;IAGrC,IAAI,CAAC4G,KAAK,IAAI,IAAI,CAACzI,OAAO,CAACY,GAAG,CAACC,MAAM,CAAC,CAACgB,QAAQ,GAAG,CAAC,EAAE;MACnD,OAAO,KAAK;;IAGd,IAAI,IAAI,CAAClD,WAAW,CAACmD,GAAG,CAACjB,MAAM,CAAC,EAAE;MAChC,IAAI,CAAChC,eAAe,CAAC6J,GAAG,CAAC7H,MAAM,CAAC;MAChC,IAAI,CAACxB,cAAc,EAAE;MACrB,OAAO,KAAK;;IAGd,IAAI,CAACsJ,cAAc,CAAC9H,MAAM,CAAC;IAC3B,MAAM;MAACyB;IAAkB,CAAC,GAAG,IAAI,CAACtC,OAAO,CAACY,GAAG,CAACC,MAAM,CAAC;IACrD,IAAIyB,kBAAkB,IAAI,IAAI,EAAE;MAC9B,IAAI,CAACF,WAAW,CAACE,kBAAkB,CAACY,IAAI,CAACrC,MAAM,EAAE4H,KAAK,CAAC;MACvD,IAAI,CAACrG,WAAW,CAACE,kBAAkB,CAACc,IAAI,CAACvC,MAAM,EAAE4H,KAAK,CAAC;;IAGzD,IAAI,CAACzI,OAAO,CAAC2E,MAAM,CAAC9D,MAAM,CAAC;IAE3B,OAAO,IAAI;EACb;EAEQ8H,cAAcA,CAAC9H,MAAc;IACnC,MAAM;MAACV,OAAO;MAAEE,KAAK;MAAEU,QAAQ;MAAEY,KAAK;MAAEb,QAAQ;MAAEyB;IAAK,CAAC,GACpD,IAAI,CAACvC,OAAO,CAACY,GAAG,CAACC,MAAM,CAAC;IAC5B,MAAM+H,GAAG,GAAGrG,KAAK,IAAIA,KAAK,CAACsG,UAAU,IAAIhI,MAAM;IAC/C,MAAMgB,QAAQ,GAAG,IAAI,CAAC9C,YAAY,CAAC6B,GAAG,CAACgI,GAAG,CAAC;IAE3C,IAAI/G,QAAQ,GAAG,CAAC,EAAE;MAChB,IAAI,CAAC9C,YAAY,CAAC2C,GAAG,CAACkH,GAAG,EAAE/G,QAAQ,GAAG,CAAC,CAAC;KACzC,MAAM;MACL,IAAI,CAAC9C,YAAY,CAAC4F,MAAM,CAACiE,GAAG,CAAC;MAC7B,IAAIzI,OAAO,IAAI,IAAI,EAAE;QACnB,IAAI,CAACnB,aAAa,IAAI,IAAI,CAAC8J,YAAY,CAAC/H,QAAQ,EAAEV,KAAK,CAAC;QACxD,IAAI,CAACN,cAAc,CAACgJ,cAAc,CAAC5I,OAAO,EAAEY,QAAQ,EAAEY,KAAK,EAAEb,QAAQ,CAAC;;;IAI1E,MAAMd,OAAO,GAAG,IAAI,CAACA,OAAO,CAACY,GAAG,CAACC,MAAM,CAAC;IACxCb,OAAO,CAACG,OAAO,GAAG,IAAI;IACtBH,OAAO,CAACe,QAAQ,GAAG,IAAI;IACvBf,OAAO,CAACc,QAAQ,GAAG,KAAK;IACxBd,OAAO,CAACuC,KAAK,GAAG,IAAI;EACtB;EAEAyG,UAAUA,CAACnI,MAAc;IACvB,IAAI,CAACoI,WAAW,CAACpI,MAAM,CAAC;IACxB,OAAO,IAAI,CAACb,OAAO,CAACY,GAAG,CAACC,MAAM,CAAC,CAACV,OAAO,CAACA,OAAO;EACjD;EAEA;;;;EAIA+I,WAAWA,CAACrI,MAAc;IACxB,OAAO,IAAI,CAACb,OAAO,CAACY,GAAG,CAACC,MAAM,CAAC;EACjC;EAEA;;;;;;;EAOAsI,kBAAkBA,CACdC,MAAoB,EACpBC,aAAa,GAAGxL,0BAA0B;IAC5C,OAAOlC,GAAG,EAAE,CAAC4D,OAAO,CAAC,mBAAmB,CAAC,IACrC6J,MAAM,CAACE,KAAK,CACR7I,KAAK,IAAI,IAAI,CAACT,OAAO,CAACY,GAAG,CAACH,KAAK,CAACI,MAAM,CAAC,CAACV,OAAO,IAAI,IAAI,IACnDlE,IAAI,CAACqI,aAAa,CAAC7D,KAAK,CAACL,KAAK,CAAC,GAAGiJ,aAAa,CAAC;EAC9D;EAEAE,eAAeA,CAAA;IACb,OAAO,IAAI,CAAC1J,KAAK;EACnB;EAEA2J,KAAKA,CAACC,SAAiB;IACrBlO,YAAY,CAACmO,IAAI,CACb,2CAA2C,GAC3C,8BAA8B,CAAC;IACnC,MAAMC,QAAQ,GAAGF,SAAS,CAACG,QAAQ,EAAE;IACrC,OAAOrM,SAAS,CAACkM,SAAS,CAACrJ,KAAK,EAAEuJ,QAAQ,CAAC;EAC7C;EAEQE,aAAaA,CAACC,CAAa,EAAEC,EAAU,EAAE1J,KAAe;IAC9D,MAAMa,OAAO,GAAG,IAAI9D,oBAAoB,CAAC0M,CAAC,CAAC1J,KAAK,EAAE2J,EAAE,CAAC;IACrD,MAAMC,OAAO,GAAG,IAAI,CAACC,aAAa,CAAC/I,OAAO,EAAE,CAAC4I,CAAC,CAAC,EAAEzJ,KAAK,CAAC;IACvD,OAAO3E,MAAM,EAAE,CAAC0J,wBAAwB,CAAC4E,OAAO,CAAC;EACnD;EAEA;EACA;EACA;EACAE,GAAGA,CAAmBJ,CAAI;IACxB;IACA,IAAI,IAAI,CAACX,kBAAkB,CAAC,CAACW,CAAC,CAAC,CAAC,IAAIA,CAAC,CAACzJ,KAAK,KAAK,WAAW,EAAE;MAC3D,MAAM8J,SAAS,GACXvN,gBAAgB,CAAC,IAAI,CAACoD,OAAO,CAACY,GAAG,CAACkJ,CAAC,CAACjJ,MAAM,CAAC,CAACU,MAAoB,CAAC;MACrE,OAAO,IAAI,CAAC6I,UAAU,CAACN,CAAC,CAAC1J,KAAK,EAAE0J,CAAC,CAACzJ,KAAK,EAAE8J,SAAS,CAAC;;IAGrD,IAAIxO,GAAG,EAAE,CAAC4D,OAAO,CAAC,6BAA6B,CAAC,EAAE;MAChD,OAAO,IAAI,CAACsK,aAAa,CAACC,CAAC,EAAE5M,QAAQ,CAACmN,GAAG,EAAEP,CAAC,CAACzJ,KAAK,CAAM;;IAG1D,MAAMa,OAAO,GAAG,IAAI/D,cAAc,CAAC2M,CAAC,CAAC1J,KAAK,EAAElD,QAAQ,CAACmN,GAAG,CAAC;IACzD,MAAML,OAAO,GAAG,IAAI,CAACC,aAAa,CAAC/I,OAAO,EAAE,CAAC4I,CAAC,CAAC,CAAC;IAChD,OAAOpO,MAAM,EAAE,CAAC0J,wBAAwB,CAAC4E,OAAO,CAAM;EACxD;EAEAtJ,cAAcA,CACVN,KAAe,EAAEC,KAAe,EAChCkB,MAA+B;IACjC,IAAIV,MAAM;IACV,IAAIR,KAAK,KAAK,QAAQ,IAAIkB,MAAM,IAAI,IAAI,IAAIA,MAAM,CAACwE,MAAM,GAAG,CAAC,IACzD9J,IAAI,CAACqO,QAAQ,CAAC/I,MAAM,CAAC,CAAC,CAAC,CAAC,EAAE;MAC5B,MAAMgJ,aAAa,GACdhJ,MAA8B,CAACmE,GAAG,CAACC,CAAC,IAAI1J,IAAI,CAACuO,YAAY,CAAC7E,CAAC,CAAC,CAAC;MAElE9E,MAAM,GAAG,IAAI,CAACS,KAAK,CAACiJ,aAAa,EAAEnK,KAAK,EAAEC,KAAK,CAAC;KACjD,MAAM;MACLQ,MAAM,GAAG,IAAI,CAACS,KAAK,CAACC,MAAoB,EAAEnB,KAAK,EAAEC,KAAK,CAAC;;IAGzD,IAAI,CAACL,OAAO,CAACY,GAAG,CAACC,MAAM,CAAC,CAACc,KAAK,GAAG,IAAI;IACrC,OAAO;MAACd,MAAM;MAAET,KAAK;MAAEC;IAAK,CAAC;EAC/B;EAEQ+J,UAAUA,CACdhK,KAAe,EAAEC,KAAe,EAAEkB,MAAsB;IAC1D,OAAO7F,MAAM,EAAE,CAAC0J,wBAAwB,CAC7B,IAAI,CAAC1E,cAAc,CAACN,KAAK,EAAEC,KAAK,EAAEkB,MAAM,CAAC,EAAE,IAAI,CAAM;EAClE;EAEAkJ,YAAYA,CAAChK,KAAiB;IAC5B,MAAMS,OAAO,GAAG,IAAI7D,aAAa,CAACoD,KAAK,CAACL,KAAK,CAAC;IAC9C,OAAO,IAAI,CAACgB,eAAe,CAACF,OAAO,EAAE,CAACT,KAAK,CAAC,EAAEA,KAAK,CAACJ,KAAK,CAAC;EAC5D;EAEAqK,UAAUA,CAACjK,KAAiB;IAC1B,MAAMS,OAAO,GAAG,IAAIrE,WAAW,CAAC4D,KAAK,CAACL,KAAK,CAAC;IAC5C,MAAMuK,2BAA2B,GAAG,IAAI;IACxC,OAAO,IAAI,CAACvJ,eAAe,CACvBF,OAAO,EAAE,CAACT,KAAK,CAAC,EAAEA,KAAK,CAACJ,KAAK,EAAE,IAAI,CAAC,2BACpCsK,2BAA2B,CAAC;EAClC;EAEQC,aAAaA,CAACnK,KAAiB,EAAEoK,UAAoB;IAC3D,MAAMC,YAAY,GAAG,CACnBxN,UAAU,CAACyN,WAAW,CAACtK,KAAK,CAACL,KAAK,CAAC,EACnC,GAAG9C,UAAU,CAAC0N,WAAW,CAACvK,KAAK,CAACL,KAAK,CAAC,CACX;IAC7B,MAAM6K,OAAO,GAAe;MAC1B5K,KAAK,EAAEI,KAAK,CAACJ,KAAK;MAClBD,KAAK,EAAE0K,YAAY;MACnBjK,MAAM,EAAEJ,KAAK,CAACI;KACf;IACD,MAAMqK,cAAc,GAAG,CACrB5N,UAAU,CAACyN,WAAW,CAACF,UAAU,CAAC,EAAE,GAAGvN,UAAU,CAAC0N,WAAW,CAACH,UAAU,CAAC,CAC9C;IAE7B,MAAM3J,OAAO,GAAG,IAAIpE,oBAAoB,CAACoO,cAAc,EAAEJ,YAAY,CAAC;IACtE,MAAMK,6BAA6B,GAAG,IAAI;IAC1C,MAAMC,YAAY,GAAG,CAACN,YAAY,CAAC;IACnC,MAAM3J,MAAM,GAAG,IAAI,CAACC,eAAe,CAC/BF,OAAO,EAAE,CAAC+J,OAAO,CAAC,EAAExK,KAAK,CAACJ,KAAK,EAAE+K,YAAY,EAC7CD,6BAA6B,CAAC;IAClC,OAAO;MAACtK,MAAM,EAAEM,MAAM,CAACN,MAAM;MAAET,KAAK,EAAEyK,UAAU;MAAExK,KAAK,EAAEc,MAAM,CAACd;IAAK,CAAC;EACxE;EAEQwD,MAAMA,CAAChD,MAAc,EAAEqE,cAAiC;IAE9D,MAAMlF,OAAO,GAAG,IAAI,CAACA,OAAO,CAACY,GAAG,CAACC,MAAM,CAAC;IACxC,MAAM;MAACC,QAAQ;MAAEV,KAAK;MAAEC;IAAK,CAAC,GAAGL,OAAO;IACxC,IAAIkF,cAAc,IAAI,IAAI,EAAE;MAC1B,MAAMb,IAAI,GAAGpI,IAAI,CAACqI,aAAa,CAAClE,KAAK,CAAC;MACtC,MAAMiL,OAAO,GAAGnG,cAAc,CAAC,CAAC,CAAC,GAAGA,cAAc,CAAC,CAAC,CAAC,GAAG,CAAC;MACzDjJ,IAAI,CAACqP,MAAM,CACPjH,IAAI,IAAIgH,OAAO,EACf,MAAM,+BAA+B,GACjC,sDAAsD,GACtD,0BAA0B,CAAC;;IAErC,MAAMrK,SAAS,GACX1D,UAAU,CAAC2D,YAAY,CAACb,KAAK,CAA6B;IAC9D,IAAIc,OAAO;IACX,IAAIJ,QAAQ,EAAE;MACZI,OAAO,GAAG,IAAI9E,yBAAyB,CAAC4E,SAAS,CAAC;KACnD,MAAM;MACLE,OAAO,GAAG,IAAI/E,mBAAmB,CAAC6E,SAAS,CAAC;;IAE9C,MAAMmK,6BAA6B,GAAG,IAAI;IAC1C,MAAMC,YAAY,GACd,CAAClG,cAAc,IAAI,IAAI,GAAGA,cAAc,GACdnI,QAAQ,CAACiH,gBAAgB,CAAChD,SAAS,CAAC,CAAC;IACnE,MAAMuK,GAAG,GAAG,IAAI,CAACnK,eAAe,CAC5BF,OAAO,EAAE,CAAC;MAACd,KAAK,EAAEY,SAAS;MAAEX,KAAK;MAAEQ;IAAM,CAAC,CAAC,EAAER,KAAK,EAAE+K,YAAY,EACjED,6BAA6B,EAAEjG,cAAc,CAAC;IAClD,OAAO;MAAC7E,KAAK;MAAED,KAAK;MAAES,MAAM,EAAE0K,GAAG,CAAC1K;IAAM,CAAC;EAC3C;EAEAO,eAAeA,CACXF,OAAqB,EAAEkI,MAAoB,EAAEoC,WAAqB,EAClEC,mBAAgC,EAAEN,6BAA6B,GAAG,KAAK,EACvEjG,cAAiC;IACnC,MAAM/D,MAAM,GAAG,IAAI,CAACT,cAAc,CAACQ,OAAO,CAACmF,WAAW,EAAEmF,WAAW,CAAC;IACpE,MAAME,OAAO,GAAG,IAAI,CAAC1L,OAAO,CAACY,GAAG,CAACO,MAAM,CAACN,MAAM,CAAC;IAC/C,IAAIK,OAAO,CAACyK,YAAY,EAAE;MACxBD,OAAO,CAAC5K,QAAQ,GAAG,IAAI;;IAEzB,IAAII,OAAO,CAAC0K,gBAAgB,KAAK7O,QAAQ,CAAC8O,aAAa,CAACC,KAAK,EAAE;MAC7D,MAAMC,UAAU,GAAG7G,cAAc,IAAI,IAAI,GACrCA,cAAc,GACdnI,QAAQ,CAACiH,gBAAgB,CAAC9C,OAAO,CAACmF,WAAW,CAAC;MAClD;MACA;MACA;MACA;MACAqF,OAAO,CAAC3K,QAAQ,GAAGgL,UAAU,CAACrG,GAAG,CAACC,CAAC,IAAIA,CAAC,GAAG,CAAC,CAAqB;;IAEnE,IAAIzE,OAAO,CAAC8K,WAAW,IAAI,IAAI,EAAE;MAC/BN,OAAO,CAAC/J,KAAK,GAAGT,OAAO,CAAC8K,WAAW;;IAGrC,IAAI/P,IAAI,CAACqI,aAAa,CAACnD,MAAM,CAACf,KAAK,CAAC,KAAK,CAAC,EAAE;MAC1C;MACA;MACAsL,OAAO,CAACnK,MAAM,GACVtF,IAAI,CAACgQ,sBAAsB,CAAC9K,MAAM,CAACd,KAAkB,EAAE,CAAC,CAAC;MAC7D,OAAOc,MAAM;;IAGf,MAAM+K,aAAa,GAAiB,EAAE;IACtC,MAAMC,UAAU,GAAiB/C,MAAM,CAAC1D,GAAG,CAACjF,KAAK,IAAG;MAClD,IAAIA,KAAK,CAACJ,KAAK,KAAK,WAAW,EAAE;QAC/B,MAAM,IAAIb,KAAK,CACX,+DAA+D,GAC/D,8DAA8D,GAC9D,QAAQ,CAAC;;MAGf,IAAIQ,OAAO,GAAG,IAAI,CAACA,OAAO,CAACY,GAAG,CAACH,KAAK,CAACI,MAAM,CAAC;MAE5C,IAAIb,OAAO,CAACG,OAAO,IAAI,IAAI,EAAE;QAC3B,IAAI,CAACe,OAAO,CAACkL,YAAY,IACrBnQ,IAAI,CAACqI,aAAa,CAAC7D,KAAK,CAACL,KAAK,CAAC,IAC3BzE,GAAG,EAAE,CAACmC,SAAS,CAAC,2BAA2B,CAAC,EAAE;UACpD;UACA;UACA;UACA;UACA;UACA,OAAO;YACLsC,KAAK,EAAEK,KAAK,CAACL,KAAK;YAClBJ,OAAO,EAAE,IAAI;YACbqM,SAAS,EAAE,IAAI;YACfC,aAAa,EAAEtM,OAAO,CAACuB;WACxB;;QAGH;QACA;QACA,IAAIL,OAAO,CAACkL,YAAY,EAAE;UACxBpM,OAAO,CAACc,QAAQ,GAAG,IAAI;UACvBd,OAAO,CAACI,KAAK,GAAGK,KAAK,CAACL,KAAK;;;MAI/B,IAAI,CAAC6I,WAAW,CAACxI,KAAK,CAACI,MAAM,CAAC;MAC9B,IAAI,CAAC,CAACb,OAAO,CAACc,QAAQ,KAAK,CAAC,CAACI,OAAO,CAACkL,YAAY,EAAE;QACjD3L,KAAK,GAAGT,OAAO,CAACc,QAAQ,GAAG,IAAI,CAAC2J,YAAY,CAAChK,KAAK,CAAC,GACxB,IAAI,CAACiK,UAAU,CAACjK,KAAK,CAAC;QACjDyL,aAAa,CAACvI,IAAI,CAAClD,KAAK,CAAC;QACzBT,OAAO,GAAG,IAAI,CAACA,OAAO,CAACY,GAAG,CAACH,KAAK,CAACI,MAAM,CAAC;OACzC,MAAM,IACHb,OAAO,CAACc,QAAQ,IAChB,CAACxD,UAAU,CAACiP,aAAa,CAACvM,OAAO,CAACI,KAAK,EAAEK,KAAK,CAACL,KAAK,CAAC,EAAE;QACzD;QACA;QACA;QACA;QACA;QACA;QAEA,MAAMoM,UAAU,GAAG/L,KAAK;QACxB,MAAMgM,WAAW,GAAGhM,KAAK,CAACL,KAAK;QAE/BK,KAAK,CAACL,KAAK,GAAGJ,OAAO,CAACI,KAAK;QAC3BK,KAAK,GAAG,IAAI,CAACmK,aAAa,CAACnK,KAAe,EAAEgM,WAAW,CAAC;QACxDP,aAAa,CAACvI,IAAI,CAAClD,KAAK,CAAC;QACzBT,OAAO,GAAG,IAAI,CAACA,OAAO,CAACY,GAAG,CAACH,KAAK,CAACI,MAAM,CAAC;QAExC2L,UAAU,CAACpM,KAAK,GAAGqM,WAAW;;MAGhC,OAAO;QAACrM,KAAK,EAAEK,KAAK,CAACL,KAAK;QAAEJ,OAAO;QAAEqM,SAAS,EAAE;MAAK,CAAC;IACxD,CAAC,CAAC;IAEF,IAAI,CAACpD,WAAW,CAAC9H,MAAM,CAACN,MAAM,CAAC;IAC/B,MAAM6L,UAAU,GACC;MAACtM,KAAK,EAAEe,MAAM,CAACf,KAAK;MAAEJ,OAAO,EAAE0L,OAAO;MAAEW,SAAS,EAAE;IAAK,CAAC;IAC1E,MAAMzD,GAAG,GAAGlM,UAAU,CAACiQ,aAAa,CAACzL,OAAO,EAAEiL,UAAU,EAAEO,UAAU,CAAC;IACrE,MAAME,MAAM,GAAG,IAAI,CAACC,gBAAgB,CAACjE,GAAG,EAAE,MAAK;MAC7C,OAAOlM,UAAU,CAACoQ,cAAc,CAC5B,IAAI,CAACjN,KAAK,EAAEqB,OAAO,EAAEiL,UAAU,EAAEO,UAAU,CAAC;IAClD,CAAC,CAAC;IACF,MAAM9J,iBAAiB,GAAG,IAAI,CAACC,YAAY,IAAI,IAAI;IACnD,IAAImE,KAA+B;IACnC,IAAIpE,iBAAiB,EAAE;MACrBoE,KAAK,GAAG,IAAI,CAACgB,UAAU,EAAE;;IAG3B,IAAI,CAACrM,GAAG,EAAE,CAACiF,GAAG,CAAC,qBAAqB,CAAC,EAAE;MACrClE,UAAU,CAACqQ,UAAU,CACjB,IAAI,CAAClN,KAAK,EAAE+M,MAAM,EAAET,UAAU,EAAEO,UAAU,EAAEjB,mBAAmB,CAAC;;IAGtES,aAAa,CAACtH,OAAO,CAACoI,IAAI,IAAI,IAAI,CAAC3L,6BAA6B,CAAC2L,IAAI,CAAC,CAAC;IAEvE,IAAIpK,iBAAiB,EAAE;MACrBoE,KAAK,GAAG,IAAI,CAACoB,QAAQ,CAACpB,KAAK,CAAC;MAC5B,IAAI,CAACnE,YAAY,CAACc,IAAI,CAClB;QAACwD,IAAI,EAAEjG,OAAO,CAACzC,WAAW,CAAC0I,IAAI;QAAEH,KAAK,EAAE,IAAI,CAACsB,YAAY,CAACtB,KAAK;MAAC,CAAC,CAAC;;IAGxE,MAAMiG,gBAAgB,GAAGtR,GAAG,EAAE,CAACmC,SAAS,CAAC,uBAAuB,CAAC;IACjE;IACA,IAAImP,gBAAgB,GAAG,CAAC,EAAE;MACxB,MAAMzG,IAAI,GAAGvK,IAAI,CAAC8G,GAAG,EAAE;MACvB,IAAKyD,IAAI,GAAG,IAAI,CAACrH,eAAe,GAAI8N,gBAAgB,EAAE;QACpD,IAAI,CAACpN,KAAK,CAACH,EAAE,CAACwN,KAAK,EAAE;QACrB,IAAI,CAAC/N,eAAe,GAAGqH,IAAI;;;IAI/B,IAAI,CAAC7K,GAAG,EAAE,CAAC4D,OAAO,CAAC,qBAAqB,CAAC,IAAImM,OAAO,CAAC5K,QAAQ,IACzDqK,6BAA6B,KAAK,KAAK,EAAE;MAC3C,MAAMgC,QAAQ,GAAG,IAAI,CAAC1C,YAAY,CAACtJ,MAAM,CAAC;MAC1C,IAAI,CAACE,6BAA6B,CAACF,MAAM,CAAC;MAC1C,OAAOgM,QAAQ;;IAEjB,OAAOhM,MAAM;EACf;EAEA8I,aAAaA,CACT/I,OAAqB,EAAEkI,MAAoB,EAAEoC,WAAsB,EACnEC,mBAAgC,EAChCN,6BAA6B,GAAG,KAAK;IACvCK,WAAW,GAAGA,WAAW,IAAIpC,MAAM,CAAC,CAAC,CAAC,CAAC/I,KAAK;IAC5C,MAAM2J,OAAO,GAAG,IAAI,CAAC5I,eAAe,CAChCF,OAAO,EAAEkI,MAAM,EAAEoC,WAAW,EAAEC,mBAAmB,EACjDN,6BAA6B,CAAC;IAClC,OAAOnB,OAAO;EAChB;EAEQ6C,gBAAgBA,CAACjE,GAAW,EAAEwE,SAA4B;IAEhE,IAAI,EAAExE,GAAG,IAAI,IAAI,CAACjJ,WAAW,CAAC,EAAE;MAC9B,IAAI,CAACA,WAAW,CAACiJ,GAAG,CAAC,GAAGwE,SAAS,EAAE;;IAErC,OAAO,IAAI,CAACzN,WAAW,CAACiJ,GAAG,CAAC;EAC9B;EAEAyE,iBAAiBA,CAAA;IACf,OAAO,IAAI,CAACtN,cAAc;EAC5B;EAISuN,OAAOA,CAAA;IACd,IAAI,IAAI,CAAChO,QAAQ,EAAE;MACjB;;IAEF;IACA;IACA,IAAI,CAAC3D,GAAG,EAAE,CAAC4D,OAAO,CAAC,SAAS,CAAC,EAAE;MAC7B,MAAMgO,OAAO,GAAGlI,MAAM,CAACmI,IAAI,CAAC,IAAI,CAAC7N,WAAW,CAAC;MAC7C4N,OAAO,CAAC3I,OAAO,CAACgE,GAAG,IAAG;QACpB,IAAI,CAAC/I,KAAK,CAAC4N,aAAa,CAAC,IAAI,CAAC9N,WAAW,CAACiJ,GAAG,CAAC,CAAC8E,YAAY,CAAC;QAC5D,OAAO,IAAI,CAAC/N,WAAW,CAACiJ,GAAG,CAAC;MAC9B,CAAC,CAAC;;IAEJ,IAAI,CAAC7I,cAAc,CAACuN,OAAO,EAAE;IAC7B,IAAI,IAAI,CAACxN,MAAM,IAAI,IAAI,IAClB,OAAQ6N,iBAAkB,KAAK,WAAW,IAC1C,IAAI,CAAC7N,MAAM,YAAY6N,iBAAkB,EAAE;MAC9C,IAAI,CAAC7N,MAAM,CAAC8N,MAAM,EAAE;KACrB,MAAM;MACL,IAAI,CAAC9N,MAAM,GAAG,IAAI;;IAEpB,IAAI,IAAI,CAACF,mBAAmB,EAAE;MAC5B,IAAI,CAACC,KAAK,CAACqB,OAAO,GAAG,IAAI;MACzB,IAAI,CAACrB,KAAK,CAACyN,OAAO,EAAE;;IAEtB,IAAI,CAAChO,QAAQ,GAAG,IAAI;EACtB;EAESuO,cAAcA,CAAA;IACrB,IAAI,IAAI,CAACC,mBAAmB,IAAI,IAAI,EAAE;MACpC,IAAI,CAACA,mBAAmB,GAAG9R,IAAI,CAAC,MAAK;QACnC,IAAI,CAACL,GAAG,EAAE,CAACiF,GAAG,CAAC,8BAA8B,CAAC,EAAE;UAC9C;UACA;UACA,MAAMmN,SAAS,GAAGpS,GAAG,EAAE,CAAC4D,OAAO,CAAC,OAAO,CAAC;UACxC5D,GAAG,EAAE,CAAC+F,GAAG,CAAC,OAAO,EAAE,KAAK,CAAC;UACzB,MAAMsM,mBAAmB,GAAG,IAAI,CAAC9D,GAAG,CAACnO,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC6N,QAAQ,EAAE,CAAC,CAAC,CAAC;UAChEjO,GAAG,EAAE,CAAC+F,GAAG,CAAC,OAAO,EAAEqM,SAAS,CAAC;UAE7B,IAAIC,mBAAmB,GAAG,CAAC,EAAE;YAC3B,OAAO,EAAE;;;QAGb,OAAO,EAAE;MACX,CAAC,CAAC;;IAEJ,OAAO,IAAI,CAACF,mBAAmB;EACjC;EAEA;EACSG,OAAOA,CAAA;IACd,OAAO,IAAI,CAACJ,cAAc,EAAE,KAAK,EAAE,GAAGrQ,eAAe,GAAGC,eAAe;EACzE;EAEAwL,WAAWA,CAACpI,MAAc;IACxB,MAAMb,OAAO,GAAG,IAAI,CAACA,OAAO,CAACY,GAAG,CAACC,MAAM,CAAC;IACxC,MAAM;MAACT,KAAK;MAAEC,KAAK;MAAEkB,MAAM;MAAEpB,OAAO;MAAEwB,KAAK;MAAEb;IAAQ,CAAC,GAAGd,OAAO;IAEhE,IAAIG,OAAO,IAAI,IAAI,EAAE;MACnB;MACA;;IAEF,MAAMyC,iBAAiB,GAAG,IAAI,CAACC,YAAY,IAAI,IAAI;IACnD,IAAIC,KAAa;IACjB,IAAIF,iBAAiB,EAAE;MACrBE,KAAK,GAAG7G,IAAI,CAAC8G,GAAG,EAAE;;IAGpB,IAAIhC,QAAQ,GAAGf,OAAO,CAACe,QAAQ;IAC/B,IAAIA,QAAQ,IAAI,IAAI,EAAE;MACpB;MACA;MACAA,QAAQ,GAAGzD,UAAU,CAAC4Q,+BAA+B,CAAC9N,KAAK,EAAEU,QAAQ,CAAC;MACtEd,OAAO,CAACe,QAAQ,GAAGA,QAAQ;;IAG7B,IAAIQ,MAAM,IAAI,IAAI,EAAE;MAClB,MAAMP,SAAS,GAAG1D,UAAU,CAAC2D,YAAY,CAACb,KAAK,CAAC;MAEhD,IAAIc,OAAO;MACX,IAAI9C,KAAK,GAAG2C,QAAQ,CAAC,CAAC,CAAC;QAAE5C,MAAM,GAAG4C,QAAQ,CAAC,CAAC,CAAC;MAC7C,MAAMoN,WAAW,GACb5M,MAAM,YAAY6M,UAAU,IAAI7M,MAAM,YAAY8M,iBAAiB;MAEvE;MACA;MACA,IAAIvN,QAAQ,IAAI,CAACqN,WAAW,EAAE;QAC5B,CAAC/P,KAAK,EAAED,MAAM,CAAC,GAAGpB,QAAQ,CAACuR,sCAAsC,CAC7DvN,QAAQ,CAAC,CAAC,CAAC,EAAEA,QAAQ,CAAC,CAAC,CAAC,CAAC;;MAG/B,IAAID,QAAQ,EAAE;QACZI,OAAO,GAAG,IAAI1E,yBAAyB,CAACwE,SAAS,EAAEmN,WAAW,CAAC;OAChE,MAAM;QACLjN,OAAO,GAAG,IAAI3E,mBAAmB,CAACyE,SAAS,EAAEmN,WAAW,CAAC;;MAG3D;MACA;MACA;MACA,MAAMI,sBAAsB,GACxBJ,WAAW,GAAG,CAAChQ,MAAM,EAAEC,KAAK,CAAC,GAAG2C,QAAQ;MAC5C,MAAMyN,oBAAoB,GACtB,IAAI,CAAC9N,cAAc,CAAC6N,sBAAsB,EAAElO,KAAK,CAAC;MACtD,MAAMoO,qBAAqB,GACvB,IAAI,CAACzO,OAAO,CAACY,GAAG,CAAC4N,oBAAoB,CAAC3N,MAAM,CAAC;MACjD,IAAIsN,WAAW,EAAE;QACfM,qBAAqB,CAAC9M,KAAK,GAAG3E,YAAY,CAAC0R,MAAM;OAClD,MAAM;QACLD,qBAAqB,CAAC9M,KAAK,GAAG3E,YAAY,CAAC4E,MAAM;;MAEnD6M,qBAAqB,CAAC1N,QAAQ,GAAGwN,sBAAsB;MACvD,IAAI,CAAC1O,KAAK,CAAC8O,0BAA0B,CACjC,IAAI,CAAC3F,UAAU,CAACwF,oBAAoB,CAAC3N,MAAM,CAAC,EAAEzC,KAAK,EAAED,MAAM,EAC3DoD,MAAoB,CAAC;MAEzB,MAAM6J,YAAY,GAAG,CAAC,CAACjN,MAAM,EAAEC,KAAK,CAAC,CAAC;MACtC;MACA;MACA,MAAMwQ,qBAAqB,GAAG,IAAI;MAClC,MAAMC,mBAAmB,GAAG,IAAI,CAACzN,eAAe,CAC5CF,OAAO,EAAE,CAACsN,oBAAoB,CAAC,EAAEnO,KAAK,EAAE+K,YAAY,EACpDwD,qBAAqB,CAAC;MAE1B;MACA,MAAME,aAAa,GAAG,IAAI,CAAC9O,OAAO,CAACY,GAAG,CAACiO,mBAAmB,CAAChO,MAAM,CAAC;MAClEb,OAAO,CAACe,QAAQ,GAAG+N,aAAa,CAAC/N,QAAQ;MACzCf,OAAO,CAACc,QAAQ,GAAGgO,aAAa,CAAChO,QAAQ;MACzCd,OAAO,CAAC2B,KAAK,GAAGmN,aAAa,CAACnN,KAAK;MAEnC,IAAI,CAAChG,GAAG,EAAE,CAACiF,GAAG,CAAC,qBAAqB,CAAC,EAAE;QACrCZ,OAAO,CAACG,OAAO,GAAG2O,aAAa,CAAC3O,OAAO;QACvC;QACAH,OAAO,CAACuB,MAAM,GAAG,IAAI;QACrB,IAAI,CAACvB,OAAO,CAAC2E,MAAM,CAACkK,mBAAmB,CAAChO,MAAM,CAAC;OAChD,MAAM;QACL,IAAI,CAACuB,WAAW,CAACyM,mBAAmB,CAAChO,MAAM,CAAC;;MAG9C,IAAI,CAACQ,6BAA6B,CAACmN,oBAAoB,CAAC;MAExD,IAAI5L,iBAAiB,EAAE;QACrB,IAAI,CAAC3D,YAAY,IAAIhD,IAAI,CAAC8G,GAAG,EAAE,GAAGD,KAAK;;KAE1C,MAAM;MACL,MAAMiM,UAAU,GAAG,IAAI,CAACC,cAAc,CAACjO,QAAQ,EAAEY,KAAK,EAAEtB,KAAK,EAAES,QAAQ,CAAC;MACxEd,OAAO,CAACG,OAAO,GAAG4O,UAAU;;EAEhC;EAEQpM,oBAAoBA,CAAC9B,MAAc,EAAEoO,aAA4B;IAEvE,MAAMjP,OAAO,GAAG,IAAI,CAACA,OAAO,CAACY,GAAG,CAACC,MAAM,CAAC;IACxC,MAAM;MAACR;IAAK,CAAC,GAAGL,OAAO;IAEvB,IAAIiP,aAAa,IAAI,IAAI,EAAE;MACzBjP,OAAO,CAACuB,MAAM,GAAG2N,mBAAmB,CAACD,aAAa,EAAE5O,KAAkB,CAAC;;IAEzE,OAAOL,OAAO,CAACuB,MAAoB;EACrC;EAEQyN,cAAcA,CAClBjO,QAA0B,EAAEoO,OAAqB,EAAE9O,KAAe,EAClES,QAAiB;IACnB,IAAI,CAAC9B,aAAa,IAAI,IAAI,CAAC8J,YAAY,CAAC/H,QAAQ,EAAEV,KAAK,CAAC;IACxD,IAAI,CAAC,IAAI,CAACjB,iBAAiB,IACvB,IAAI,CAACJ,aAAa,GAAG,IAAI,CAAChB,kBAAkB,GAAG,IAAI,GAAG,IAAI,EAAE;MAC9D,MAAMoR,EAAE,GAAG,CAAC,IAAI,CAACpQ,aAAa,GAAG,IAAI,GAAG,IAAI,EAAEqQ,OAAO,CAAC,CAAC,CAAC;MACxD,IAAI,CAACjQ,iBAAiB,GAAG,IAAI;MAC7BkQ,OAAO,CAAC5F,IAAI,CACR,6BAA6B0F,EAAE,OAAO,GACtC,kCAAkC,CAAC;;IAEzC,OAAO,IAAI,CAACrP,cAAc,CAACiP,cAAc,CAACjO,QAAQ,EAAEoO,OAAO,EAAErO,QAAQ,CAAC;EACxE;EAEQgI,YAAYA,CAAC1I,KAAuB,EAAEC,KAAe;IAC3D,OAAOD,KAAK,CAAC,CAAC,CAAC,GAAGA,KAAK,CAAC,CAAC,CAAC,GAAGnE,IAAI,CAACsT,eAAe,CAAClP,KAAK,CAAC;EAC1D;EAEAmP,sBAAsBA,CAAA;IACpB,KAAK,MAAM,GAAG5C,MAAM,CAAC,IAAIvH,MAAM,CAACoK,OAAO,CAAC,IAAI,CAAC9P,WAAW,CAAC,EAAE;MACzD,IAAI,CAAC+P,gBAAgB,CAAC9C,MAAM,CAAC;;EAEjC;EAEA,MAAM+C,2BAA2BA,CAAA;IAC/B,MAAMxL,EAAE,GAAG,EAAE;IACb,IAAI,IAAI,CAACtE,KAAK,CAAC+P,4BAA4B,EAAE;MAC3C,KAAK,MAAM,GAAGhD,MAAM,CAAC,IAAIvH,MAAM,CAACoK,OAAO,CAAC,IAAI,CAAC9P,WAAW,CAAC,EAAE;QACzDwE,EAAE,CAACR,IAAI,CAAC,IAAI,CAACkM,qBAAqB,CAACjD,MAAM,CAAC,CAAC;;MAE7C,OAAOnJ,OAAO,CAACW,GAAG,CAACD,EAAE,CAAC;KACvB,MAAM;MACL,KAAK,MAAM,GAAGyI,MAAM,CAAC,IAAIvH,MAAM,CAACoK,OAAO,CAAC,IAAI,CAAC9P,WAAW,CAAC,EAAE;QACzD,MAAMmQ,CAAC,GAAqB,IAAIrM,OAAO,CAAEC,OAAO,IAAI;UAClD,IAAI;YACF,IAAI,CAACgM,gBAAgB,CAAC9C,MAAM,CAAC;YAC7BlJ,OAAO,CAAC,IAAI,CAAC;WACd,CAAC,OAAO+D,KAAK,EAAE;YACd,MAAMA,KAAK;;QAEf,CAAC,CAAC;QACFtD,EAAE,CAACR,IAAI,CAACmM,CAAC,CAAC;;MAEZ,OAAOrM,OAAO,CAACW,GAAG,CAACD,EAAE,CAAC;;EAE1B;EAEQ,MAAM0L,qBAAqBA,CAACjD,MAAmB;IACrD,IAAI,IAAI,CAAC/M,KAAK,CAACH,EAAE,CAACqQ,mBAAmB,CAC7BnD,MAAM,CAACc,YAAY,EACnB,IAAI,CAAC7N,KAAK,CAAC+P,4BAA4B,CAACI,qBAAqB,CAAC,EAAE;MACtE,OAAO,IAAI,CAACN,gBAAgB,CAAC9C,MAAM,CAAC;KACrC,MAAM;MACL,MAAM9Q,SAAS,EAAE;MACjB,OAAO,IAAI,CAAC+T,qBAAqB,CAACjD,MAAM,CAAC;;EAE7C;EAEQ8C,gBAAgBA,CAAC9C,MAAmB;IAC1C,IAAI,IAAI,CAAC/M,KAAK,CAACH,EAAE,CAACqQ,mBAAmB,CAC7BnD,MAAM,CAACc,YAAY,EAAE,IAAI,CAAC7N,KAAK,CAACH,EAAE,CAACuQ,WAAW,CAAC,KAAK,KAAK,EAAE;MACjEX,OAAO,CAACY,GAAG,CAAC,IAAI,CAACrQ,KAAK,CAACH,EAAE,CAACyQ,iBAAiB,CAACvD,MAAM,CAACc,YAAY,CAAC,CAAC;MACjE,IAAI,IAAI,CAAC7N,KAAK,CAACH,EAAE,CAAC0Q,kBAAkB,CAC5BxD,MAAM,CAACyD,cAAc,EAAE,IAAI,CAACxQ,KAAK,CAACH,EAAE,CAAC4Q,cAAc,CAAC,KAAK,KAAK,EAAE;QACtEhT,UAAU,CAACiT,yBAAyB,CAChC3D,MAAM,CAAC4D,MAAM,EACb,IAAI,CAAC3Q,KAAK,CAACH,EAAE,CAAC+Q,gBAAgB,CAAC7D,MAAM,CAACyD,cAAc,CAAC,CAAC;QAC1D,MAAM,IAAI7Q,KAAK,CAAC,oCAAoC,CAAC;;MAEvD,MAAM,IAAIA,KAAK,CAAC,6CAA6C,CAAC;;IAEhE,OAAO,IAAI;EACb;EAEA7C,mBAAmBA,CAAA;IACjB,KAAK,MAAMiQ,MAAM,IAAIvH,MAAM,CAAC9D,MAAM,CAAC,IAAI,CAAC5B,WAAW,CAAC,EAAE;MACpD;MACA;MACA;MACA;MACA,IAAI,CAACE,KAAK,CAAC6Q,QAAQ,CAAC9D,MAAM,CAACc,YAAY,CAAC;MAExC,MAAM;QACJiD,kBAAkB;QAClBC,sBAAsB;QACtBC,MAAM;QACNC,MAAM;QACNC,gBAAgB;QAChBC,uBAAuB;QACvBC;MAAmB,CACpB,GAAGtU,mBAAmB,CAAC,IAAI,CAACkD,KAAK,EAAE+M,MAAM,CAAC1L,OAAO,EAAE0L,MAAM,CAACc,YAAY,CAAC;MACxEd,MAAM,CAAC+D,kBAAkB,GAAGA,kBAAkB;MAC9C/D,MAAM,CAACgE,sBAAsB,GAAGA,sBAAsB;MACtDhE,MAAM,CAACiE,MAAM,GAAGA,MAAM;MACtBjE,MAAM,CAACkE,MAAM,GAAGA,MAAM;MACtBlE,MAAM,CAACmE,gBAAgB,GAAGA,gBAAgB;MAC1CnE,MAAM,CAACoE,uBAAuB,GAAGA,uBAAuB;MACxDpE,MAAM,CAACqE,mBAAmB,GAAGA,mBAAmB;;EAEpD;EAEA;;;;EAISC,uBAAuBA,CAC5B3P,MAAiB,EAAEnB,KAAe,EAAEC,KAAe;IACrDkB,MAAM,CAACf,QAAQ,GAAGe,MAAM,CAACf,QAAQ,IAAI,MAAM;IAC3C,MAAM;MAACL,OAAO;MAAEhC,MAAM;MAAEC,KAAK;MAAEoC;IAAQ,CAAC,GAAGe,MAAM;IACjD,MAAM4P,OAAO,GAAGzV,MAAM,EAAE,CAACyV,OAA2B;IAEpD;IACA;IACA,IAAI,CAACA,OAAO,CAACtR,KAAK,CAACH,EAAE,CAAC0R,SAAS,CAACjR,OAAO,CAAC,EAAE;MACxC,MAAM,IAAIX,KAAK,CACX,iEAAiE,GACjE,mEAAmE,GACnE,oEAAoE,GACpE,qDAAqD,GACrD,0CAA0C,CAAC;;IAGjD,MAAMqB,MAAM,GACRsQ,OAAO,CAACjR,YAAY,CAACC,OAAO,EAAEC,KAAK,EAAEC,KAAK,EAAElC,MAAM,EAAEC,KAAK,EAAEoC,QAAQ,CAAC;IACxE,OAAO9E,MAAM,EAAE,CAAC2V,oBAAoB,CAACxQ,MAAM,EAAET,KAAK,EAAEC,KAAK,EAAE8Q,OAAO,CAAC;EACrE;;AArsCe5S,gBAAA,CAAAC,UAAU,GAAG,CAAC;SAJlBD,gBAAgB;AA4sC7B,SAAS2Q,mBAAmBA,CACxBoC,CAAe,EAAEjR,KAAQ;EAC3B,IAAIA,KAAK,KAAK,SAAS,IAAIA,KAAK,KAAK,WAAW,EAAE;IAChD,OAAOiR,CAAsB;GAC9B,MAAM,IAAIjR,KAAK,KAAK,OAAO,IAAIA,KAAK,KAAK,MAAM,EAAE;IAChD,MAAM2C,MAAM,GAAI3C,KAAK,KAAK,OAAO,GAAI,IAAIkR,UAAU,CAACD,CAAC,CAACvL,MAAM,CAAC,GACxB,IAAIqI,UAAU,CAACkD,CAAC,CAACvL,MAAM,CAAC;IAC7D,KAAK,IAAID,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG9C,MAAM,CAAC+C,MAAM,EAAE,EAAED,CAAC,EAAE;MACtC9C,MAAM,CAAC8C,CAAC,CAAC,GAAG0L,IAAI,CAACC,KAAK,CAACH,CAAC,CAACxL,CAAC,CAAC,CAAC;;IAE9B,OAAO9C,MAA2B;GACnC,MAAM;IACL,MAAM,IAAIxD,KAAK,CAAC,iBAAiBa,KAAK,EAAE,CAAC;;AAE7C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}