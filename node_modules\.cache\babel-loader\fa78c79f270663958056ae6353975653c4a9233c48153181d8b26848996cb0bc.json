{"ast": null, "code": "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { ENGINE } from '../engine';\nimport { BatchMatMul } from '../kernel_names';\nimport { makeTypesMatch } from '../tensor_util';\nimport { convertToTensor } from '../tensor_util_env';\nimport { op } from './operation';\n/**\n * Computes the dot product of two matrices, A * B. These must be matrices.\n *\n * ```js\n * const a = tf.tensor2d([1, 2], [1, 2]);\n * const b = tf.tensor2d([1, 2, 3, 4], [2, 2]);\n *\n * a.matMul(b).print();  // or tf.matMul(a, b)\n * ```\n * @param a First matrix in dot product operation.\n * @param b Second matrix in dot product operation.\n * @param transposeA If true, `a` is transposed before multiplication.\n * @param transposeB If true, `b` is transposed before multiplication.\n *\n * @doc {heading: 'Operations', subheading: 'Matrices'}\n */\nfunction matMul_(a, b) {\n  let transposeA = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : false;\n  let transposeB = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : false;\n  let $a = convertToTensor(a, 'a', 'matMul');\n  let $b = convertToTensor(b, 'b', 'matMul');\n  [$a, $b] = makeTypesMatch($a, $b);\n  const inputs = {\n    a: $a,\n    b: $b\n  };\n  const attrs = {\n    transposeA,\n    transposeB\n  };\n  return ENGINE.runKernel(BatchMatMul, inputs, attrs);\n}\nexport const matMul = /* @__PURE__ */op({\n  matMul_\n});", "map": {"version": 3, "names": ["ENGINE", "BatchMatMul", "makeTypesMatch", "convertToTensor", "op", "mat<PERSON><PERSON>_", "a", "b", "transposeA", "arguments", "length", "undefined", "transposeB", "$a", "$b", "inputs", "attrs", "runKernel", "<PERSON><PERSON><PERSON>"], "sources": ["C:\\tfjs-core\\src\\ops\\mat_mul.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport {ENGINE} from '../engine';\nimport {BatchMatMul, BatchMatMulAttrs, BatchMatMulInputs} from '../kernel_names';\nimport {NamedAttrMap} from '../kernel_registry';\nimport {Tensor} from '../tensor';\nimport {NamedTensorMap} from '../tensor_types';\nimport {makeTypesMatch} from '../tensor_util';\nimport {convertToTensor} from '../tensor_util_env';\nimport {TensorLike} from '../types';\n\nimport {op} from './operation';\n\n/**\n * Computes the dot product of two matrices, A * B. These must be matrices.\n *\n * ```js\n * const a = tf.tensor2d([1, 2], [1, 2]);\n * const b = tf.tensor2d([1, 2, 3, 4], [2, 2]);\n *\n * a.matMul(b).print();  // or tf.matMul(a, b)\n * ```\n * @param a First matrix in dot product operation.\n * @param b Second matrix in dot product operation.\n * @param transposeA If true, `a` is transposed before multiplication.\n * @param transposeB If true, `b` is transposed before multiplication.\n *\n * @doc {heading: 'Operations', subheading: 'Matrices'}\n */\nfunction matMul_<T extends Tensor>(\n    a: Tensor|TensorLike, b: Tensor|TensorLike, transposeA = false,\n    transposeB = false): T {\n  let $a = convertToTensor(a, 'a', 'matMul');\n  let $b = convertToTensor(b, 'b', 'matMul');\n  [$a, $b] = makeTypesMatch($a, $b);\n\n  const inputs: BatchMatMulInputs = {a: $a, b: $b};\n  const attrs: BatchMatMulAttrs = {transposeA, transposeB};\n\n  return ENGINE.runKernel(\n      BatchMatMul, inputs as unknown as NamedTensorMap,\n      attrs as unknown as NamedAttrMap);\n}\n\nexport const matMul = /* @__PURE__ */ op({matMul_});\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAgBA,SAAQA,MAAM,QAAO,WAAW;AAChC,SAAQC,WAAW,QAA4C,iBAAiB;AAIhF,SAAQC,cAAc,QAAO,gBAAgB;AAC7C,SAAQC,eAAe,QAAO,oBAAoB;AAGlD,SAAQC,EAAE,QAAO,aAAa;AAE9B;;;;;;;;;;;;;;;;AAgBA,SAASC,OAAOA,CACZC,CAAoB,EAAEC,CAAoB,EACxB;EAAA,IAD0BC,UAAU,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,KAAK;EAAA,IAC9DG,UAAU,GAAAH,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,KAAK;EACpB,IAAII,EAAE,GAAGV,eAAe,CAACG,CAAC,EAAE,GAAG,EAAE,QAAQ,CAAC;EAC1C,IAAIQ,EAAE,GAAGX,eAAe,CAACI,CAAC,EAAE,GAAG,EAAE,QAAQ,CAAC;EAC1C,CAACM,EAAE,EAAEC,EAAE,CAAC,GAAGZ,cAAc,CAACW,EAAE,EAAEC,EAAE,CAAC;EAEjC,MAAMC,MAAM,GAAsB;IAACT,CAAC,EAAEO,EAAE;IAAEN,CAAC,EAAEO;EAAE,CAAC;EAChD,MAAME,KAAK,GAAqB;IAACR,UAAU;IAAEI;EAAU,CAAC;EAExD,OAAOZ,MAAM,CAACiB,SAAS,CACnBhB,WAAW,EAAEc,MAAmC,EAChDC,KAAgC,CAAC;AACvC;AAEA,OAAO,MAAME,MAAM,GAAG,eAAgBd,EAAE,CAAC;EAACC;AAAO,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}