{"ast": null, "code": "/**\n * @license\n * Copyright 2019 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { kernel_impls, NonMaxSuppressionV5 } from '@tensorflow/tfjs-core';\nconst nonMaxSuppressionV5Impl = kernel_impls.nonMaxSuppressionV5Impl;\nimport { assertNotComplex } from '../cpu_util';\nexport function nonMaxSuppressionV5(args) {\n  const {\n    inputs,\n    backend,\n    attrs\n  } = args;\n  const {\n    boxes,\n    scores\n  } = inputs;\n  const {\n    maxOutputSize,\n    iouThreshold,\n    scoreThreshold,\n    softNmsSigma\n  } = attrs;\n  assertNotComplex(boxes, 'NonMaxSuppressionWithScore');\n  const boxesVals = backend.data.get(boxes.dataId).values;\n  const scoresVals = backend.data.get(scores.dataId).values;\n  const maxOutputSizeVal = maxOutputSize;\n  const iouThresholdVal = iouThreshold;\n  const scoreThresholdVal = scoreThreshold;\n  const softNmsSigmaVal = softNmsSigma;\n  const {\n    selectedIndices,\n    selectedScores\n  } = nonMaxSuppressionV5Impl(boxesVals, scoresVals, maxOutputSizeVal, iouThresholdVal, scoreThresholdVal, softNmsSigmaVal);\n  return [backend.makeTensorInfo([selectedIndices.length], 'int32', new Int32Array(selectedIndices)), backend.makeTensorInfo([selectedScores.length], 'float32', new Float32Array(selectedScores))];\n}\nexport const nonMaxSuppressionV5Config = {\n  kernelName: NonMaxSuppressionV5,\n  backendName: 'cpu',\n  kernelFunc: nonMaxSuppressionV5\n};", "map": {"version": 3, "names": ["kernel_impls", "NonMaxSuppressionV5", "nonMaxSuppressionV5Impl", "assertNotComplex", "nonMaxSuppressionV5", "args", "inputs", "backend", "attrs", "boxes", "scores", "maxOutputSize", "iouThreshold", "scoreThreshold", "softNmsSigma", "boxesVals", "data", "get", "dataId", "values", "scoresVals", "maxOutputSizeVal", "iouThresholdVal", "scoreThresholdVal", "softNmsSigmaVal", "selectedIndices", "selectedScores", "makeTensorInfo", "length", "Int32Array", "Float32Array", "nonMaxSuppressionV5Config", "kernelName", "backendName", "kernelFunc"], "sources": ["C:\\tfjs-backend-cpu\\src\\kernels\\NonMaxSuppressionV5.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2019 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {kernel_impls, KernelConfig, KernelFunc, NonMaxSuppressionV5, NonMaxSuppressionV5Attrs, NonMaxSuppressionV5Inputs, TensorInfo, TypedArray} from '@tensorflow/tfjs-core';\n\nconst nonMaxSuppressionV5Impl = kernel_impls.nonMaxSuppressionV5Impl;\nimport {MathBackendCPU} from '../backend_cpu';\nimport {assertNotComplex} from '../cpu_util';\n\nexport function nonMaxSuppressionV5(args: {\n  inputs: NonMaxSuppressionV5Inputs,\n  backend: MathBackendCPU,\n  attrs: NonMaxSuppressionV5Attrs\n}): [TensorInfo, TensorInfo] {\n  const {inputs, backend, attrs} = args;\n  const {boxes, scores} = inputs;\n  const {maxOutputSize, iouThreshold, scoreThreshold, softNmsSigma} = attrs;\n\n  assertNotComplex(boxes, 'NonMaxSuppressionWithScore');\n\n  const boxesVals = backend.data.get(boxes.dataId).values as TypedArray;\n  const scoresVals = backend.data.get(scores.dataId).values as TypedArray;\n\n  const maxOutputSizeVal = maxOutputSize;\n  const iouThresholdVal = iouThreshold;\n  const scoreThresholdVal = scoreThreshold;\n  const softNmsSigmaVal = softNmsSigma;\n\n  const {selectedIndices, selectedScores} = nonMaxSuppressionV5Impl(\n      boxesVals, scoresVals, maxOutputSizeVal, iouThresholdVal,\n      scoreThresholdVal, softNmsSigmaVal);\n\n  return [\n    backend.makeTensorInfo(\n        [selectedIndices.length], 'int32', new Int32Array(selectedIndices)),\n    backend.makeTensorInfo(\n        [selectedScores.length], 'float32', new Float32Array(selectedScores))\n  ];\n}\n\nexport const nonMaxSuppressionV5Config: KernelConfig = {\n  kernelName: NonMaxSuppressionV5,\n  backendName: 'cpu',\n  kernelFunc: nonMaxSuppressionV5 as unknown as KernelFunc\n};\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAiBA,SAAQA,YAAY,EAA4BC,mBAAmB,QAAoF,uBAAuB;AAE9K,MAAMC,uBAAuB,GAAGF,YAAY,CAACE,uBAAuB;AAEpE,SAAQC,gBAAgB,QAAO,aAAa;AAE5C,OAAM,SAAUC,mBAAmBA,CAACC,IAInC;EACC,MAAM;IAACC,MAAM;IAAEC,OAAO;IAAEC;EAAK,CAAC,GAAGH,IAAI;EACrC,MAAM;IAACI,KAAK;IAAEC;EAAM,CAAC,GAAGJ,MAAM;EAC9B,MAAM;IAACK,aAAa;IAAEC,YAAY;IAAEC,cAAc;IAAEC;EAAY,CAAC,GAAGN,KAAK;EAEzEL,gBAAgB,CAACM,KAAK,EAAE,4BAA4B,CAAC;EAErD,MAAMM,SAAS,GAAGR,OAAO,CAACS,IAAI,CAACC,GAAG,CAACR,KAAK,CAACS,MAAM,CAAC,CAACC,MAAoB;EACrE,MAAMC,UAAU,GAAGb,OAAO,CAACS,IAAI,CAACC,GAAG,CAACP,MAAM,CAACQ,MAAM,CAAC,CAACC,MAAoB;EAEvE,MAAME,gBAAgB,GAAGV,aAAa;EACtC,MAAMW,eAAe,GAAGV,YAAY;EACpC,MAAMW,iBAAiB,GAAGV,cAAc;EACxC,MAAMW,eAAe,GAAGV,YAAY;EAEpC,MAAM;IAACW,eAAe;IAAEC;EAAc,CAAC,GAAGxB,uBAAuB,CAC7Da,SAAS,EAAEK,UAAU,EAAEC,gBAAgB,EAAEC,eAAe,EACxDC,iBAAiB,EAAEC,eAAe,CAAC;EAEvC,OAAO,CACLjB,OAAO,CAACoB,cAAc,CAClB,CAACF,eAAe,CAACG,MAAM,CAAC,EAAE,OAAO,EAAE,IAAIC,UAAU,CAACJ,eAAe,CAAC,CAAC,EACvElB,OAAO,CAACoB,cAAc,CAClB,CAACD,cAAc,CAACE,MAAM,CAAC,EAAE,SAAS,EAAE,IAAIE,YAAY,CAACJ,cAAc,CAAC,CAAC,CAC1E;AACH;AAEA,OAAO,MAAMK,yBAAyB,GAAiB;EACrDC,UAAU,EAAE/B,mBAAmB;EAC/BgC,WAAW,EAAE,KAAK;EAClBC,UAAU,EAAE9B;CACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}