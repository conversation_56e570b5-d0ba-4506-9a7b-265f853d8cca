{"ast": null, "code": "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { ENGINE } from '../engine';\nimport { Complex } from '../kernel_names';\nimport { convertToTensor } from '../tensor_util_env';\nimport * as util from '../util';\nimport { op } from './operation';\n/**\n * Converts two real numbers to a complex number.\n *\n * Given a tensor `real` representing the real part of a complex number, and a\n * tensor `imag` representing the imaginary part of a complex number, this\n * operation returns complex numbers elementwise of the form [r0, i0, r1, i1],\n * where r represents the real part and i represents the imag part.\n *\n * The input tensors real and imag must have the same shape.\n *\n * ```js\n * const real = tf.tensor1d([2.25, 3.25]);\n * const imag = tf.tensor1d([4.75, 5.75]);\n * const complex = tf.complex(real, imag);\n *\n * complex.print();\n * ```\n *\n * @doc {heading: 'Tensors', subheading: 'Creation'}\n */\nfunction complex_(real, imag) {\n  const $real = convertToTensor(real, 'real', 'complex');\n  const $imag = convertToTensor(imag, 'imag', 'complex');\n  util.assertShapesMatch($real.shape, $imag.shape, `real and imag shapes, ${$real.shape} and ${$imag.shape}, ` + `must match in call to tf.complex().`);\n  const inputs = {\n    real: $real,\n    imag: $imag\n  };\n  return ENGINE.runKernel(Complex, inputs);\n}\nexport const complex = /* @__PURE__ */op({\n  complex_\n});", "map": {"version": 3, "names": ["ENGINE", "Complex", "convertToTensor", "util", "op", "complex_", "real", "imag", "$real", "$imag", "assertShapesMatch", "shape", "inputs", "runKernel", "complex"], "sources": ["C:\\tfjs-core\\src\\ops\\complex.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport {ENGINE} from '../engine';\nimport {Complex, ComplexInputs} from '../kernel_names';\nimport {Tensor} from '../tensor';\nimport {NamedTensorMap} from '../tensor_types';\nimport {convertToTensor} from '../tensor_util_env';\nimport {TensorLike} from '../types';\nimport * as util from '../util';\n\nimport {op} from './operation';\n\n/**\n * Converts two real numbers to a complex number.\n *\n * Given a tensor `real` representing the real part of a complex number, and a\n * tensor `imag` representing the imaginary part of a complex number, this\n * operation returns complex numbers elementwise of the form [r0, i0, r1, i1],\n * where r represents the real part and i represents the imag part.\n *\n * The input tensors real and imag must have the same shape.\n *\n * ```js\n * const real = tf.tensor1d([2.25, 3.25]);\n * const imag = tf.tensor1d([4.75, 5.75]);\n * const complex = tf.complex(real, imag);\n *\n * complex.print();\n * ```\n *\n * @doc {heading: 'Tensors', subheading: 'Creation'}\n */\nfunction complex_<T extends Tensor>(real: T|TensorLike, imag: T|TensorLike): T {\n  const $real = convertToTensor(real, 'real', 'complex');\n  const $imag = convertToTensor(imag, 'imag', 'complex');\n  util.assertShapesMatch(\n      $real.shape, $imag.shape,\n      `real and imag shapes, ${$real.shape} and ${$imag.shape}, ` +\n          `must match in call to tf.complex().`);\n\n  const inputs: ComplexInputs = {real: $real, imag: $imag};\n  return ENGINE.runKernel(Complex, inputs as unknown as NamedTensorMap);\n}\n\nexport const complex = /* @__PURE__ */ op({complex_});\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAgBA,SAAQA,MAAM,QAAO,WAAW;AAChC,SAAQC,OAAO,QAAsB,iBAAiB;AAGtD,SAAQC,eAAe,QAAO,oBAAoB;AAElD,OAAO,KAAKC,IAAI,MAAM,SAAS;AAE/B,SAAQC,EAAE,QAAO,aAAa;AAE9B;;;;;;;;;;;;;;;;;;;;AAoBA,SAASC,QAAQA,CAAmBC,IAAkB,EAAEC,IAAkB;EACxE,MAAMC,KAAK,GAAGN,eAAe,CAACI,IAAI,EAAE,MAAM,EAAE,SAAS,CAAC;EACtD,MAAMG,KAAK,GAAGP,eAAe,CAACK,IAAI,EAAE,MAAM,EAAE,SAAS,CAAC;EACtDJ,IAAI,CAACO,iBAAiB,CAClBF,KAAK,CAACG,KAAK,EAAEF,KAAK,CAACE,KAAK,EACxB,yBAAyBH,KAAK,CAACG,KAAK,QAAQF,KAAK,CAACE,KAAK,IAAI,GACvD,qCAAqC,CAAC;EAE9C,MAAMC,MAAM,GAAkB;IAACN,IAAI,EAAEE,KAAK;IAAED,IAAI,EAAEE;EAAK,CAAC;EACxD,OAAOT,MAAM,CAACa,SAAS,CAACZ,OAAO,EAAEW,MAAmC,CAAC;AACvE;AAEA,OAAO,MAAME,OAAO,GAAG,eAAgBV,EAAE,CAAC;EAACC;AAAQ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}