{"ast": null, "code": "/**\n * @license\n * Copyright 2018 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { getGlslDifferences } from './glsl_version';\nimport { ENCODE_FLOAT_SNIPPET } from './shader_compiler_util';\nimport { TextureUsage } from './tex_util';\nexport class EncodeFloatPackedProgram {\n  constructor(outputShape) {\n    this.variableNames = ['A'];\n    this.packedInputs = true;\n    this.packedOutput = false;\n    this.outTexUsage = TextureUsage.DOWNLOAD;\n    const glsl = getGlslDifferences();\n    this.outputShape = outputShape;\n    this.userCode = \"\\n      \".concat(ENCODE_FLOAT_SNIPPET, \"\\n\\n      void main() {\\n        ivec3 coords = getOutputCoords();\\n        float x = getChannel(getAAtOutCoords(), vec2(coords.y, coords.z));\\n        \").concat(glsl.output, \" = encode_float(x);\\n      }\\n    \");\n  }\n}", "map": {"version": 3, "names": ["getGlslDifferences", "ENCODE_FLOAT_SNIPPET", "TextureUsage", "EncodeFloatPackedProgram", "constructor", "outputShape", "variableNames", "packedInputs", "packedOutput", "outTexUsage", "DOWNLOAD", "glsl", "userCode", "concat", "output"], "sources": ["C:\\tfjs-backend-webgl\\src\\encode_float_packed_gpu.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2018 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {getGlslDifferences} from './glsl_version';\nimport {GPGPUProgram} from './gpgpu_math';\nimport {ENCODE_FLOAT_SNIPPET} from './shader_compiler_util';\nimport {TextureUsage} from './tex_util';\n\nexport class EncodeFloatPackedProgram implements GPGPUProgram {\n  variableNames = ['A'];\n  userCode: string;\n  outputShape: number[];\n  packedInputs = true;\n  packedOutput = false;\n  outTexUsage = TextureUsage.DOWNLOAD;\n\n  constructor(outputShape: [number, number, number]) {\n    const glsl = getGlslDifferences();\n    this.outputShape = outputShape;\n    this.userCode = `\n      ${ENCODE_FLOAT_SNIPPET}\n\n      void main() {\n        ivec3 coords = getOutputCoords();\n        float x = getChannel(getAAtOutCoords(), vec2(coords.y, coords.z));\n        ${glsl.output} = encode_float(x);\n      }\n    `;\n  }\n}\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAiBA,SAAQA,kBAAkB,QAAO,gBAAgB;AAEjD,SAAQC,oBAAoB,QAAO,wBAAwB;AAC3D,SAAQC,YAAY,QAAO,YAAY;AAEvC,OAAM,MAAOC,wBAAwB;EAQnCC,YAAYC,WAAqC;IAPjD,KAAAC,aAAa,GAAG,CAAC,GAAG,CAAC;IAGrB,KAAAC,YAAY,GAAG,IAAI;IACnB,KAAAC,YAAY,GAAG,KAAK;IACpB,KAAAC,WAAW,GAAGP,YAAY,CAACQ,QAAQ;IAGjC,MAAMC,IAAI,GAAGX,kBAAkB,EAAE;IACjC,IAAI,CAACK,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACO,QAAQ,cAAAC,MAAA,CACTZ,oBAAoB,8JAAAY,MAAA,CAKlBF,IAAI,CAACG,MAAM,uCAEhB;EACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}