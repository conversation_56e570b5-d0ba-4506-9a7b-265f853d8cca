{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.transformArguments = exports.FIRST_KEY_INDEX = void 0;\nexports.FIRST_KEY_INDEX = 2;\nfunction transformArguments(key, group, id, options) {\n  const args = ['XGROUP', 'CREATE', key, group, id];\n  if (options?.MKSTREAM) {\n    args.push('MKSTREAM');\n  }\n  return args;\n}\nexports.transformArguments = transformArguments;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "transformArguments", "FIRST_KEY_INDEX", "key", "group", "id", "options", "args", "MKSTREAM", "push"], "sources": ["C:/tmsft/node_modules/@redis/client/dist/lib/commands/XGROUP_CREATE.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.transformArguments = exports.FIRST_KEY_INDEX = void 0;\nexports.FIRST_KEY_INDEX = 2;\nfunction transformArguments(key, group, id, options) {\n    const args = ['XGROUP', 'CREATE', key, group, id];\n    if (options?.MKSTREAM) {\n        args.push('MKSTREAM');\n    }\n    return args;\n}\nexports.transformArguments = transformArguments;\n"], "mappings": "AAAA,YAAY;;AACZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEC,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7DD,OAAO,CAACE,kBAAkB,GAAGF,OAAO,CAACG,eAAe,GAAG,KAAK,CAAC;AAC7DH,OAAO,CAACG,eAAe,GAAG,CAAC;AAC3B,SAASD,kBAAkBA,CAACE,GAAG,EAAEC,KAAK,EAAEC,EAAE,EAAEC,OAAO,EAAE;EACjD,MAAMC,IAAI,GAAG,CAAC,QAAQ,EAAE,QAAQ,EAAEJ,GAAG,EAAEC,KAAK,EAAEC,EAAE,CAAC;EACjD,IAAIC,OAAO,EAAEE,QAAQ,EAAE;IACnBD,IAAI,CAACE,IAAI,CAAC,UAAU,CAAC;EACzB;EACA,OAAOF,IAAI;AACf;AACAR,OAAO,CAACE,kBAAkB,GAAGA,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}