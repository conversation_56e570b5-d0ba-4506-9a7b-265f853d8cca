{"ast": null, "code": "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { convertToTensor } from '../tensor_util_env';\nimport * as util from '../util';\nimport { op } from './operation';\nimport { reverse } from './reverse';\n/**\n * Reverses a `tf.Tensor4D` along a specified axis.\n *\n * @param x The input tensor.\n * @param axis The set of dimensions to reverse. Must be in the\n *     range [-rank(x), rank(x)). Defaults to all axes.\n */\nfunction reverse4d_(x, axis) {\n  const $x = convertToTensor(x, 'x', 'reverse');\n  util.assert($x.rank === 4, () => `Error in reverse4D: x must be rank 4 but got rank ${$x.rank}.`);\n  return reverse($x, axis);\n}\nexport const reverse4d = /* @__PURE__ */op({\n  reverse4d_\n});", "map": {"version": 3, "names": ["convertToTensor", "util", "op", "reverse", "reverse4d_", "x", "axis", "$x", "assert", "rank", "reverse4d"], "sources": ["C:\\tfjs-core\\src\\ops\\reverse_4d.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {Tensor4D} from '../tensor';\nimport {convertToTensor} from '../tensor_util_env';\nimport {TensorLike} from '../types';\nimport * as util from '../util';\nimport {op} from './operation';\nimport {reverse} from './reverse';\n\n/**\n * Reverses a `tf.Tensor4D` along a specified axis.\n *\n * @param x The input tensor.\n * @param axis The set of dimensions to reverse. Must be in the\n *     range [-rank(x), rank(x)). Defaults to all axes.\n */\nfunction reverse4d_(x: Tensor4D|TensorLike, axis?: number|number[]): Tensor4D {\n  const $x = convertToTensor(x, 'x', 'reverse');\n  util.assert(\n      $x.rank === 4,\n      () => `Error in reverse4D: x must be rank 4 but got rank ${$x.rank}.`);\n  return reverse($x, axis);\n}\n\nexport const reverse4d = /* @__PURE__ */ op({reverse4d_});\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAkBA,SAAQA,eAAe,QAAO,oBAAoB;AAElD,OAAO,KAAKC,IAAI,MAAM,SAAS;AAC/B,SAAQC,EAAE,QAAO,aAAa;AAC9B,SAAQC,OAAO,QAAO,WAAW;AAEjC;;;;;;;AAOA,SAASC,UAAUA,CAACC,CAAsB,EAAEC,IAAsB;EAChE,MAAMC,EAAE,GAAGP,eAAe,CAACK,CAAC,EAAE,GAAG,EAAE,SAAS,CAAC;EAC7CJ,IAAI,CAACO,MAAM,CACPD,EAAE,CAACE,IAAI,KAAK,CAAC,EACb,MAAM,qDAAqDF,EAAE,CAACE,IAAI,GAAG,CAAC;EAC1E,OAAON,OAAO,CAACI,EAAE,EAAED,IAAI,CAAC;AAC1B;AAEA,OAAO,MAAMI,SAAS,GAAG,eAAgBR,EAAE,CAAC;EAACE;AAAU,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}