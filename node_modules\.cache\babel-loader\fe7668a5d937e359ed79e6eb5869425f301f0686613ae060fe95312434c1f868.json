{"ast": null, "code": "/*\n  Predicates for the Brill tagger\n  Copyright (C) 2019 Hugo <PERSON>\n\n  This program is free software: you can redistribute it and/or modify\n  it under the terms of the GNU General Public License as published by\n  the Free Software Foundation, either version 3 of the License, or\n  (at your option) any later version.\n\n  This program is distributed in the hope that it will be useful,\n  but WITHOUT ANY WARRANTY; without even the implied warranty of\n  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the\n  GNU General Public License for more details.\n\n  You should have received a copy of the GNU General Public License\n  along with this program.  If not, see <http://www.gnu.org/licenses/>.\n*/\n\n'use strict';\n\nconst DEBUG = false;\nconst predicates = require('./RuleTemplates');\nDEBUG && console.log('RuleSet loaded predicates: ' + predicates);\nclass Predicate {\n  constructor(name, parameter1, parameter2) {\n    this.name = name;\n    this.meta = predicates[name];\n    if (!this.meta) {\n      this.meta = predicates.DEFAULT;\n    }\n    // if (this.meta.nrParameters > 0) {\n    this.parameter1 = parameter1;\n    // }\n    // if (this.meta.nrParameters > 1) {\n    this.parameter2 = parameter2;\n    // }\n    DEBUG && console.log('Predicate\\n' + JSON.toString(this.meta, null, 2));\n  }\n  evaluate(sentence, position) {\n    DEBUG && console.log('Predicate.evalute: ' + this.name);\n    const predicate = this.meta.function;\n    return predicate(sentence, position, this.parameter1, this.parameter2);\n  }\n}\nmodule.exports = Predicate;", "map": {"version": 3, "names": ["DEBUG", "predicates", "require", "console", "log", "Predicate", "constructor", "name", "parameter1", "parameter2", "meta", "DEFAULT", "JSON", "toString", "evaluate", "sentence", "position", "predicate", "function", "module", "exports"], "sources": ["C:/tmsft/node_modules/natural/lib/natural/brill_pos_tagger/lib/Predicate.js"], "sourcesContent": ["/*\n  Predicates for the Brill tagger\n  Copyright (C) 2019 Hugo <PERSON>\n\n  This program is free software: you can redistribute it and/or modify\n  it under the terms of the GNU General Public License as published by\n  the Free Software Foundation, either version 3 of the License, or\n  (at your option) any later version.\n\n  This program is distributed in the hope that it will be useful,\n  but WITHOUT ANY WARRANTY; without even the implied warranty of\n  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the\n  GNU General Public License for more details.\n\n  You should have received a copy of the GNU General Public License\n  along with this program.  If not, see <http://www.gnu.org/licenses/>.\n*/\n\n'use strict'\n\nconst DEBUG = false\n\nconst predicates = require('./RuleTemplates')\nDEBUG && console.log('RuleSet loaded predicates: ' + predicates)\n\nclass Predicate {\n  constructor (name, parameter1, parameter2) {\n    this.name = name\n    this.meta = predicates[name]\n    if (!this.meta) {\n      this.meta = predicates.DEFAULT\n    }\n    // if (this.meta.nrParameters > 0) {\n    this.parameter1 = parameter1\n    // }\n    // if (this.meta.nrParameters > 1) {\n    this.parameter2 = parameter2\n    // }\n    DEBUG && console.log('Predicate\\n' + JSON.toString(this.meta, null, 2))\n  }\n\n  evaluate (sentence, position) {\n    DEBUG && console.log('Predicate.evalute: ' + this.name)\n    const predicate = this.meta.function\n    return (predicate(sentence, position, this.parameter1, this.parameter2))\n  }\n}\n\nmodule.exports = Predicate\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,YAAY;;AAEZ,MAAMA,KAAK,GAAG,KAAK;AAEnB,MAAMC,UAAU,GAAGC,OAAO,CAAC,iBAAiB,CAAC;AAC7CF,KAAK,IAAIG,OAAO,CAACC,GAAG,CAAC,6BAA6B,GAAGH,UAAU,CAAC;AAEhE,MAAMI,SAAS,CAAC;EACdC,WAAWA,CAAEC,IAAI,EAAEC,UAAU,EAAEC,UAAU,EAAE;IACzC,IAAI,CAACF,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACG,IAAI,GAAGT,UAAU,CAACM,IAAI,CAAC;IAC5B,IAAI,CAAC,IAAI,CAACG,IAAI,EAAE;MACd,IAAI,CAACA,IAAI,GAAGT,UAAU,CAACU,OAAO;IAChC;IACA;IACA,IAAI,CAACH,UAAU,GAAGA,UAAU;IAC5B;IACA;IACA,IAAI,CAACC,UAAU,GAAGA,UAAU;IAC5B;IACAT,KAAK,IAAIG,OAAO,CAACC,GAAG,CAAC,aAAa,GAAGQ,IAAI,CAACC,QAAQ,CAAC,IAAI,CAACH,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;EACzE;EAEAI,QAAQA,CAAEC,QAAQ,EAAEC,QAAQ,EAAE;IAC5BhB,KAAK,IAAIG,OAAO,CAACC,GAAG,CAAC,qBAAqB,GAAG,IAAI,CAACG,IAAI,CAAC;IACvD,MAAMU,SAAS,GAAG,IAAI,CAACP,IAAI,CAACQ,QAAQ;IACpC,OAAQD,SAAS,CAACF,QAAQ,EAAEC,QAAQ,EAAE,IAAI,CAACR,UAAU,EAAE,IAAI,CAACC,UAAU,CAAC;EACzE;AACF;AAEAU,MAAM,CAACC,OAAO,GAAGf,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}