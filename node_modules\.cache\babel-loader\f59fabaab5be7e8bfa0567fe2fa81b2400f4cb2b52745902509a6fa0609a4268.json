{"ast": null, "code": "/**\n * @license\n * Copyright 2018 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { ENGINE } from '../engine';\nimport { dispose, tidy } from '../globals';\nimport { add } from '../ops/add';\nimport { mul } from '../ops/mul';\nimport { scalar } from '../ops/scalar';\nimport { zerosLike } from '../ops/zeros_like';\nimport { SGDOptimizer } from './sgd_optimizer';\n/** @doclink Optimizer */\nexport class MomentumOptimizer extends SGDOptimizer {\n  /** @nocollapse */\n  // Name matters for Python compatibility.\n  static get className() {\n    // Name matters for Python compatibility.\n    // This is a getter instead of a property because when it's a property, it\n    // prevents the entire class from being tree-shaken.\n    return 'Momentum';\n  }\n  constructor(learningRate, momentum, useNesterov = false) {\n    super(learningRate);\n    this.learningRate = learningRate;\n    this.momentum = momentum;\n    this.useNesterov = useNesterov;\n    this.accumulations = [];\n    this.m = scalar(this.momentum);\n  }\n  applyGradients(variableGradients) {\n    const variableNames = Array.isArray(variableGradients) ? variableGradients.map(item => item.name) : Object.keys(variableGradients);\n    variableNames.forEach((name, i) => {\n      const value = ENGINE.registeredVariables[name];\n      if (this.accumulations[i] == null) {\n        const trainable = false;\n        this.accumulations[i] = {\n          originalName: `${name}/momentum`,\n          variable: tidy(() => zerosLike(value).variable(trainable))\n        };\n      }\n      const accumulation = this.accumulations[i].variable;\n      const gradient = Array.isArray(variableGradients) ? variableGradients[i].tensor : variableGradients[name];\n      if (gradient == null) {\n        return;\n      }\n      tidy(() => {\n        let newValue;\n        const newAccumulation = add(mul(this.m, accumulation), gradient);\n        if (this.useNesterov) {\n          newValue = add(mul(this.c, add(gradient, mul(newAccumulation, this.m))), value);\n        } else {\n          newValue = add(mul(this.c, newAccumulation), value);\n        }\n        accumulation.assign(newAccumulation);\n        value.assign(newValue);\n      });\n    });\n    this.incrementIterations();\n  }\n  dispose() {\n    this.m.dispose();\n    if (this.accumulations != null) {\n      dispose(this.accumulations.map(v => v.variable));\n    }\n  }\n  /**\n   * Sets the momentum of the optimizer.\n   *\n   * @param momentum\n   */\n  setMomentum(momentum) {\n    this.momentum = momentum;\n  }\n  async getWeights() {\n    // Order matters for Python compatibility.\n    return [await this.saveIterations()].concat(this.accumulations.map(v => ({\n      name: v.originalName,\n      tensor: v.variable\n    })));\n  }\n  async setWeights(weightValues) {\n    weightValues = await this.extractIterations(weightValues);\n    const trainable = false;\n    this.accumulations = weightValues.map(v => ({\n      originalName: v.name,\n      variable: v.tensor.variable(trainable)\n    }));\n  }\n  getConfig() {\n    return {\n      'learningRate': this.learningRate,\n      'momentum': this.momentum,\n      'useNesterov': this.useNesterov\n    };\n  }\n  /** @nocollapse */\n  static fromConfig(cls, config) {\n    return new cls(config['learningRate'], config['momentum'], config['useNesterov']);\n  }\n}", "map": {"version": 3, "names": ["ENGINE", "dispose", "tidy", "add", "mul", "scalar", "zerosLike", "SGDOptimizer", "MomentumOptimizer", "className", "constructor", "learningRate", "momentum", "<PERSON><PERSON><PERSON><PERSON>", "accumulations", "m", "applyGradients", "variableGradients", "variableNames", "Array", "isArray", "map", "item", "name", "Object", "keys", "for<PERSON>ach", "i", "value", "registeredVariables", "trainable", "originalName", "variable", "accumulation", "gradient", "tensor", "newValue", "newAccumulation", "c", "assign", "incrementIterations", "v", "setMomentum", "getWeights", "saveIterations", "concat", "setWeights", "weightValues", "extractIterations", "getConfig", "fromConfig", "cls", "config"], "sources": ["C:\\tfjs-core\\src\\optimizers\\momentum_optimizer.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2018 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {ENGINE} from '../engine';\nimport {dispose, tidy} from '../globals';\nimport {add} from '../ops/add';\nimport {mul} from '../ops/mul';\nimport {scalar} from '../ops/scalar';\nimport {zerosLike} from '../ops/zeros_like';\nimport {ConfigDict, Serializable, SerializableConstructor} from '../serialization';\nimport {Scalar, Tensor} from '../tensor';\nimport {NamedTensor, NamedVariableMap} from '../tensor_types';\n\nimport {OptimizerVariable} from './optimizer';\nimport {SGDOptimizer} from './sgd_optimizer';\n\n/** @doclink Optimizer */\nexport class MomentumOptimizer extends SGDOptimizer {\n  /** @nocollapse */\n  // Name matters for Python compatibility.\n  static override get className() {\n    // Name matters for Python compatibility.\n    // This is a getter instead of a property because when it's a property, it\n    // prevents the entire class from being tree-shaken.\n    return 'Momentum';\n  }\n  private m: Scalar;\n  private accumulations: OptimizerVariable[] = [];\n\n  constructor(\n      protected override learningRate: number, private momentum: number,\n      private useNesterov = false) {\n    super(learningRate);\n    this.m = scalar(this.momentum);\n  }\n\n  override applyGradients(variableGradients: NamedVariableMap|NamedTensor[]) {\n    const variableNames = Array.isArray(variableGradients) ?\n        variableGradients.map(item => item.name) :\n        Object.keys(variableGradients);\n\n    variableNames.forEach((name, i) => {\n      const value = ENGINE.registeredVariables[name];\n      if (this.accumulations[i] == null) {\n        const trainable = false;\n        this.accumulations[i] = {\n          originalName: `${name}/momentum`,\n          variable: tidy(() => zerosLike(value).variable(trainable))\n        };\n      }\n\n      const accumulation = this.accumulations[i].variable;\n      const gradient = Array.isArray(variableGradients) ?\n          variableGradients[i].tensor :\n          variableGradients[name];\n      if (gradient == null) {\n        return;\n      }\n\n      tidy(() => {\n        let newValue: Tensor;\n        const newAccumulation = add(mul(this.m, accumulation), gradient);\n        if (this.useNesterov) {\n          newValue = add(\n              mul(this.c, add(gradient, mul(newAccumulation, this.m))), value);\n        } else {\n          newValue = add(mul(this.c, newAccumulation), value);\n        }\n        accumulation.assign(newAccumulation);\n        value.assign(newValue);\n      });\n    });\n    this.incrementIterations();\n  }\n\n  override dispose(): void {\n    this.m.dispose();\n    if (this.accumulations != null) {\n      dispose(this.accumulations.map(v => v.variable));\n    }\n  }\n\n  /**\n   * Sets the momentum of the optimizer.\n   *\n   * @param momentum\n   */\n  setMomentum(momentum: number) {\n    this.momentum = momentum;\n  }\n\n  override async getWeights(): Promise<NamedTensor[]> {\n    // Order matters for Python compatibility.\n    return [await this.saveIterations()].concat(this.accumulations.map(\n        v => ({name: v.originalName, tensor: v.variable})));\n  }\n\n  override async setWeights(weightValues: NamedTensor[]): Promise<void> {\n    weightValues = await this.extractIterations(weightValues);\n    const trainable = false;\n    this.accumulations = weightValues.map(\n        v => ({originalName: v.name, variable: v.tensor.variable(trainable)}));\n  }\n\n  override getConfig(): ConfigDict {\n    return {\n      'learningRate': this.learningRate,\n      'momentum': this.momentum,\n      'useNesterov': this.useNesterov\n    };\n  }\n\n  /** @nocollapse */\n  static override fromConfig<T extends Serializable>(\n      cls: SerializableConstructor<T>, config: ConfigDict): T {\n    return new cls(\n        config['learningRate'], config['momentum'], config['useNesterov']);\n  }\n}\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAiBA,SAAQA,MAAM,QAAO,WAAW;AAChC,SAAQC,OAAO,EAAEC,IAAI,QAAO,YAAY;AACxC,SAAQC,GAAG,QAAO,YAAY;AAC9B,SAAQC,GAAG,QAAO,YAAY;AAC9B,SAAQC,MAAM,QAAO,eAAe;AACpC,SAAQC,SAAS,QAAO,mBAAmB;AAM3C,SAAQC,YAAY,QAAO,iBAAiB;AAE5C;AACA,OAAM,MAAOC,iBAAkB,SAAQD,YAAY;EACjD;EACA;EACA,WAAoBE,SAASA,CAAA;IAC3B;IACA;IACA;IACA,OAAO,UAAU;EACnB;EAIAC,YACuBC,YAAoB,EAAUC,QAAgB,EACzDC,WAAA,GAAc,KAAK;IAC7B,KAAK,CAACF,YAAY,CAAC;IAFE,KAAAA,YAAY,GAAZA,YAAY;IAAkB,KAAAC,QAAQ,GAARA,QAAQ;IACjD,KAAAC,WAAW,GAAXA,WAAW;IAJf,KAAAC,aAAa,GAAwB,EAAE;IAM7C,IAAI,CAACC,CAAC,GAAGV,MAAM,CAAC,IAAI,CAACO,QAAQ,CAAC;EAChC;EAESI,cAAcA,CAACC,iBAAiD;IACvE,MAAMC,aAAa,GAAGC,KAAK,CAACC,OAAO,CAACH,iBAAiB,CAAC,GAClDA,iBAAiB,CAACI,GAAG,CAACC,IAAI,IAAIA,IAAI,CAACC,IAAI,CAAC,GACxCC,MAAM,CAACC,IAAI,CAACR,iBAAiB,CAAC;IAElCC,aAAa,CAACQ,OAAO,CAAC,CAACH,IAAI,EAAEI,CAAC,KAAI;MAChC,MAAMC,KAAK,GAAG5B,MAAM,CAAC6B,mBAAmB,CAACN,IAAI,CAAC;MAC9C,IAAI,IAAI,CAACT,aAAa,CAACa,CAAC,CAAC,IAAI,IAAI,EAAE;QACjC,MAAMG,SAAS,GAAG,KAAK;QACvB,IAAI,CAAChB,aAAa,CAACa,CAAC,CAAC,GAAG;UACtBI,YAAY,EAAE,GAAGR,IAAI,WAAW;UAChCS,QAAQ,EAAE9B,IAAI,CAAC,MAAMI,SAAS,CAACsB,KAAK,CAAC,CAACI,QAAQ,CAACF,SAAS,CAAC;SAC1D;;MAGH,MAAMG,YAAY,GAAG,IAAI,CAACnB,aAAa,CAACa,CAAC,CAAC,CAACK,QAAQ;MACnD,MAAME,QAAQ,GAAGf,KAAK,CAACC,OAAO,CAACH,iBAAiB,CAAC,GAC7CA,iBAAiB,CAACU,CAAC,CAAC,CAACQ,MAAM,GAC3BlB,iBAAiB,CAACM,IAAI,CAAC;MAC3B,IAAIW,QAAQ,IAAI,IAAI,EAAE;QACpB;;MAGFhC,IAAI,CAAC,MAAK;QACR,IAAIkC,QAAgB;QACpB,MAAMC,eAAe,GAAGlC,GAAG,CAACC,GAAG,CAAC,IAAI,CAACW,CAAC,EAAEkB,YAAY,CAAC,EAAEC,QAAQ,CAAC;QAChE,IAAI,IAAI,CAACrB,WAAW,EAAE;UACpBuB,QAAQ,GAAGjC,GAAG,CACVC,GAAG,CAAC,IAAI,CAACkC,CAAC,EAAEnC,GAAG,CAAC+B,QAAQ,EAAE9B,GAAG,CAACiC,eAAe,EAAE,IAAI,CAACtB,CAAC,CAAC,CAAC,CAAC,EAAEa,KAAK,CAAC;SACrE,MAAM;UACLQ,QAAQ,GAAGjC,GAAG,CAACC,GAAG,CAAC,IAAI,CAACkC,CAAC,EAAED,eAAe,CAAC,EAAET,KAAK,CAAC;;QAErDK,YAAY,CAACM,MAAM,CAACF,eAAe,CAAC;QACpCT,KAAK,CAACW,MAAM,CAACH,QAAQ,CAAC;MACxB,CAAC,CAAC;IACJ,CAAC,CAAC;IACF,IAAI,CAACI,mBAAmB,EAAE;EAC5B;EAESvC,OAAOA,CAAA;IACd,IAAI,CAACc,CAAC,CAACd,OAAO,EAAE;IAChB,IAAI,IAAI,CAACa,aAAa,IAAI,IAAI,EAAE;MAC9Bb,OAAO,CAAC,IAAI,CAACa,aAAa,CAACO,GAAG,CAACoB,CAAC,IAAIA,CAAC,CAACT,QAAQ,CAAC,CAAC;;EAEpD;EAEA;;;;;EAKAU,WAAWA,CAAC9B,QAAgB;IAC1B,IAAI,CAACA,QAAQ,GAAGA,QAAQ;EAC1B;EAES,MAAM+B,UAAUA,CAAA;IACvB;IACA,OAAO,CAAC,MAAM,IAAI,CAACC,cAAc,EAAE,CAAC,CAACC,MAAM,CAAC,IAAI,CAAC/B,aAAa,CAACO,GAAG,CAC9DoB,CAAC,KAAK;MAAClB,IAAI,EAAEkB,CAAC,CAACV,YAAY;MAAEI,MAAM,EAAEM,CAAC,CAACT;IAAQ,CAAC,CAAC,CAAC,CAAC;EACzD;EAES,MAAMc,UAAUA,CAACC,YAA2B;IACnDA,YAAY,GAAG,MAAM,IAAI,CAACC,iBAAiB,CAACD,YAAY,CAAC;IACzD,MAAMjB,SAAS,GAAG,KAAK;IACvB,IAAI,CAAChB,aAAa,GAAGiC,YAAY,CAAC1B,GAAG,CACjCoB,CAAC,KAAK;MAACV,YAAY,EAAEU,CAAC,CAAClB,IAAI;MAAES,QAAQ,EAAES,CAAC,CAACN,MAAM,CAACH,QAAQ,CAACF,SAAS;IAAC,CAAC,CAAC,CAAC;EAC5E;EAESmB,SAASA,CAAA;IAChB,OAAO;MACL,cAAc,EAAE,IAAI,CAACtC,YAAY;MACjC,UAAU,EAAE,IAAI,CAACC,QAAQ;MACzB,aAAa,EAAE,IAAI,CAACC;KACrB;EACH;EAEA;EACA,OAAgBqC,UAAUA,CACtBC,GAA+B,EAAEC,MAAkB;IACrD,OAAO,IAAID,GAAG,CACVC,MAAM,CAAC,cAAc,CAAC,EAAEA,MAAM,CAAC,UAAU,CAAC,EAAEA,MAAM,CAAC,aAAa,CAAC,CAAC;EACxE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}