{"ast": null, "code": "/**\n * @license\n * Copyright 2018 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { ENGINE } from '../engine';\nimport { Sum } from '../kernel_names';\nimport { convertToTensor } from '../tensor_util_env';\nimport { cast } from './cast';\nimport { op } from './operation';\n/**\n * Computes the sum of elements across dimensions of a `tf.Tensor`.\n *\n * Reduces the input along the dimensions given in `axes`. Unless `keepDims`\n * is true, the rank of the `tf.Tensor` is reduced by 1 for each entry in\n * `axes`. If `keepDims` is true, the reduced dimensions are retained with\n * length 1. If axes has no entries, all dimensions are reduced, and a\n * `tf.Tensor` with a single element is returned.\n *\n * ```js\n * const x = tf.tensor1d([1, 2, 3]);\n *\n * x.sum().print();  // or tf.sum(x)\n * ```\n *\n * ```js\n * const x = tf.tensor2d([1, 2, 3, 4], [2, 2]);\n *\n * const axis = 1;\n * x.sum(axis).print();  // or tf.sum(x, axis)\n * ```\n *\n * @param x The input tensor to compute the sum over. If the dtype is `bool`\n *   it will be converted to `int32` and the output dtype will be `int32`.\n * @param axis The dimension(s) to reduce. By default it reduces\n *     all dimensions.\n * @param keepDims If true, retains reduced dimensions with size 1.\n *\n * @doc {heading: 'Operations', subheading: 'Reduction'}\n */\nfunction sum_(x) {\n  let axis = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : null;\n  let keepDims = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : false;\n  let $x = convertToTensor(x, 'x', 'sum');\n  if ($x.dtype === 'bool') {\n    $x = cast($x, 'int32');\n  }\n  const inputs = {\n    x: $x\n  };\n  const attrs = {\n    axis,\n    keepDims\n  };\n  return ENGINE.runKernel(Sum, inputs, attrs);\n}\nexport const sum = /* @__PURE__ */op({\n  sum_\n});", "map": {"version": 3, "names": ["ENGINE", "Sum", "convertToTensor", "cast", "op", "sum_", "x", "axis", "arguments", "length", "undefined", "keepDims", "$x", "dtype", "inputs", "attrs", "runKernel", "sum"], "sources": ["C:\\tfjs-core\\src\\ops\\sum.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2018 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport {ENGINE} from '../engine';\nimport {Sum, SumAttrs, SumInputs} from '../kernel_names';\nimport {NamedAttrMap} from '../kernel_registry';\nimport {Tensor} from '../tensor';\nimport {NamedTensorMap} from '../tensor_types';\nimport {convertToTensor} from '../tensor_util_env';\nimport {TensorLike} from '../types';\n\nimport {cast} from './cast';\nimport {op} from './operation';\n\n/**\n * Computes the sum of elements across dimensions of a `tf.Tensor`.\n *\n * Reduces the input along the dimensions given in `axes`. Unless `keepDims`\n * is true, the rank of the `tf.Tensor` is reduced by 1 for each entry in\n * `axes`. If `keepDims` is true, the reduced dimensions are retained with\n * length 1. If axes has no entries, all dimensions are reduced, and a\n * `tf.Tensor` with a single element is returned.\n *\n * ```js\n * const x = tf.tensor1d([1, 2, 3]);\n *\n * x.sum().print();  // or tf.sum(x)\n * ```\n *\n * ```js\n * const x = tf.tensor2d([1, 2, 3, 4], [2, 2]);\n *\n * const axis = 1;\n * x.sum(axis).print();  // or tf.sum(x, axis)\n * ```\n *\n * @param x The input tensor to compute the sum over. If the dtype is `bool`\n *   it will be converted to `int32` and the output dtype will be `int32`.\n * @param axis The dimension(s) to reduce. By default it reduces\n *     all dimensions.\n * @param keepDims If true, retains reduced dimensions with size 1.\n *\n * @doc {heading: 'Operations', subheading: 'Reduction'}\n */\nfunction sum_<T extends Tensor>(\n    x: Tensor|TensorLike, axis: number|number[] = null, keepDims = false): T {\n  let $x = convertToTensor(x, 'x', 'sum');\n  if ($x.dtype === 'bool') {\n    $x = cast($x, 'int32');\n  }\n\n  const inputs: SumInputs = {x: $x};\n  const attrs: SumAttrs = {axis, keepDims};\n\n  return ENGINE.runKernel(\n      Sum, inputs as unknown as NamedTensorMap,\n      attrs as unknown as NamedAttrMap);\n}\n\nexport const sum = /* @__PURE__ */ op({sum_});\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAgBA,SAAQA,MAAM,QAAO,WAAW;AAChC,SAAQC,GAAG,QAA4B,iBAAiB;AAIxD,SAAQC,eAAe,QAAO,oBAAoB;AAGlD,SAAQC,IAAI,QAAO,QAAQ;AAC3B,SAAQC,EAAE,QAAO,aAAa;AAE9B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8BA,SAASC,IAAIA,CACTC,CAAoB,EAAgD;EAAA,IAA9CC,IAAA,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAwB,IAAI;EAAA,IAAEG,QAAQ,GAAAH,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,KAAK;EACtE,IAAII,EAAE,GAAGV,eAAe,CAACI,CAAC,EAAE,GAAG,EAAE,KAAK,CAAC;EACvC,IAAIM,EAAE,CAACC,KAAK,KAAK,MAAM,EAAE;IACvBD,EAAE,GAAGT,IAAI,CAACS,EAAE,EAAE,OAAO,CAAC;;EAGxB,MAAME,MAAM,GAAc;IAACR,CAAC,EAAEM;EAAE,CAAC;EACjC,MAAMG,KAAK,GAAa;IAACR,IAAI;IAAEI;EAAQ,CAAC;EAExC,OAAOX,MAAM,CAACgB,SAAS,CACnBf,GAAG,EAAEa,MAAmC,EACxCC,KAAgC,CAAC;AACvC;AAEA,OAAO,MAAME,GAAG,GAAG,eAAgBb,EAAE,CAAC;EAACC;AAAI,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}