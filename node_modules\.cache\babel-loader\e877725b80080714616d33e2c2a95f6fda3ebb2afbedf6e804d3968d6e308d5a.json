{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.transformArguments = exports.IS_READ_ONLY = void 0;\nexports.IS_READ_ONLY = true;\nfunction transformArguments(version, ...optionalArguments) {\n  const args = ['LOLWUT'];\n  if (version) {\n    args.push('VERSION', version.toString(), ...optionalArguments.map(String));\n  }\n  return args;\n}\nexports.transformArguments = transformArguments;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "transformArguments", "IS_READ_ONLY", "version", "optionalArguments", "args", "push", "toString", "map", "String"], "sources": ["C:/tmsft/node_modules/@redis/client/dist/lib/commands/LOLWUT.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.transformArguments = exports.IS_READ_ONLY = void 0;\nexports.IS_READ_ONLY = true;\nfunction transformArguments(version, ...optionalArguments) {\n    const args = ['LOLWUT'];\n    if (version) {\n        args.push('VERSION', version.toString(), ...optionalArguments.map(String));\n    }\n    return args;\n}\nexports.transformArguments = transformArguments;\n"], "mappings": "AAAA,YAAY;;AACZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEC,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7DD,OAAO,CAACE,kBAAkB,GAAGF,OAAO,CAACG,YAAY,GAAG,KAAK,CAAC;AAC1DH,OAAO,CAACG,YAAY,GAAG,IAAI;AAC3B,SAASD,kBAAkBA,CAACE,OAAO,EAAE,GAAGC,iBAAiB,EAAE;EACvD,MAAMC,IAAI,GAAG,CAAC,QAAQ,CAAC;EACvB,IAAIF,OAAO,EAAE;IACTE,IAAI,CAACC,IAAI,CAAC,SAAS,EAAEH,OAAO,CAACI,QAAQ,CAAC,CAAC,EAAE,GAAGH,iBAAiB,CAACI,GAAG,CAACC,MAAM,CAAC,CAAC;EAC9E;EACA,OAAOJ,IAAI;AACf;AACAN,OAAO,CAACE,kBAAkB,GAAGA,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}