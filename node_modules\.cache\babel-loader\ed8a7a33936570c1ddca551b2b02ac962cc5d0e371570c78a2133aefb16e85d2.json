{"ast": null, "code": "/**\n * @license\n * Copyright 2020 Google Inc. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n// base.ts is tfjs-core without auto registration of things like flags,\n// gradients, chained ops or the opHandler. See base_side_effects.ts for parts\n// tfjs core that are required side effects.\n/**\n * @fileoverview\n * @suppress {partialAlias} Optimization disabled due to passing the module\n * object into a function below:\n *\n *   import * as ops from './ops/ops';\n *   setOpHandler(ops);\n */\n// Serialization.\nimport * as io from './io/io';\nimport * as math from './math';\nimport * as broadcast_util from './ops/broadcast_util';\nimport * as browser from './ops/browser';\nimport * as gather_util from './ops/gather_nd_util';\nimport * as scatter_util from './ops/scatter_nd_util';\nimport * as slice_util from './ops/slice_util';\nimport * as serialization from './serialization';\nimport * as tensor_util from './tensor_util';\nimport * as test_util from './test_util';\nimport * as util from './util';\nimport { version } from './version';\nexport { AdadeltaOptimizer } from './optimizers/adadelta_optimizer';\nexport { AdagradOptimizer } from './optimizers/adagrad_optimizer';\nexport { AdamOptimizer } from './optimizers/adam_optimizer';\nexport { AdamaxOptimizer } from './optimizers/adamax_optimizer';\nexport { MomentumOptimizer } from './optimizers/momentum_optimizer';\nexport { Optimizer } from './optimizers/optimizer';\n// Optimizers.\nexport { OptimizerConstructors } from './optimizers/optimizer_constructors';\nexport { RMSPropOptimizer } from './optimizers/rmsprop_optimizer';\nexport { SGDOptimizer } from './optimizers/sgd_optimizer';\nexport { Tensor, TensorBuffer, Variable } from './tensor';\nexport { Rank, sumOutType, upcastType } from './types';\nexport * from './ops/ops';\nexport { Reduction } from './ops/loss_ops_utils';\nexport * from './train';\nexport * from './globals';\nexport * from './kernel_registry';\nexport { customGrad, grad, grads, valueAndGrad, valueAndGrads, variableGrads } from './gradients';\nexport { Environment, env, ENV } from './environment';\nexport { version as version_core };\n// Top-level method exports.\nexport { nextFrame } from './browser_util';\n// Second level exports.\nimport * as backend_util from './backends/backend_util';\nimport * as device_util from './device_util';\nexport { browser, io, math, serialization, test_util, util, backend_util, broadcast_util, tensor_util, slice_util, gather_util, scatter_util, device_util };\nimport * as kernel_impls from './backends/kernel_impls';\nexport { kernel_impls };\n// Backend specific.\nexport { KernelBackend, DataStorage } from './backends/backend';\n// Export all kernel names / info.\nexport * from './kernel_names';", "map": {"version": 3, "names": ["io", "math", "broadcast_util", "browser", "gather_util", "scatter_util", "slice_util", "serialization", "tensor_util", "test_util", "util", "version", "AdadeltaOptimizer", "AdagradOptimizer", "AdamOptimizer", "AdamaxOptimizer", "MomentumOptimizer", "Optimizer", "OptimizerConstructors", "RMSPropOptimizer", "SGDOptimizer", "Tensor", "Tensor<PERSON><PERSON><PERSON>", "Variable", "Rank", "sumOutType", "upcastType", "Reduction", "customGrad", "grad", "grads", "valueAndGrad", "valueAndGrads", "variableGrads", "Environment", "env", "ENV", "version_core", "next<PERSON><PERSON><PERSON>", "backend_util", "device_util", "kernel_impls", "KernelBackend", "DataStorage"], "sources": ["C:\\tfjs-core\\src\\base.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google Inc. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\n// base.ts is tfjs-core without auto registration of things like flags,\n// gradients, chained ops or the opHandler. See base_side_effects.ts for parts\n// tfjs core that are required side effects.\n\n/**\n * @fileoverview\n * @suppress {partialAlias} Optimization disabled due to passing the module\n * object into a function below:\n *\n *   import * as ops from './ops/ops';\n *   setOpHandler(ops);\n */\n\n// Serialization.\nimport * as io from './io/io';\nimport * as math from './math';\nimport * as broadcast_util from './ops/broadcast_util';\nimport * as browser from './ops/browser';\nimport * as gather_util from './ops/gather_nd_util';\nimport * as scatter_util from './ops/scatter_nd_util';\nimport * as slice_util from './ops/slice_util';\nimport * as serialization from './serialization';\nimport * as tensor_util from './tensor_util';\nimport * as test_util from './test_util';\nimport * as util from './util';\nimport {version} from './version';\n\nexport {InferenceModel, MetaGraph, MetaGraphInfo, ModelPredictConfig, ModelTensorInfo, SavedModelTensorInfo, SignatureDef, SignatureDefEntry, SignatureDefInfo} from './model_types';\nexport {AdadeltaOptimizer} from './optimizers/adadelta_optimizer';\nexport {AdagradOptimizer} from './optimizers/adagrad_optimizer';\nexport {AdamOptimizer} from './optimizers/adam_optimizer';\nexport {AdamaxOptimizer} from './optimizers/adamax_optimizer';\nexport {MomentumOptimizer} from './optimizers/momentum_optimizer';\nexport {Optimizer} from './optimizers/optimizer';\n// Optimizers.\nexport {OptimizerConstructors} from './optimizers/optimizer_constructors';\nexport {RMSPropOptimizer} from './optimizers/rmsprop_optimizer';\nexport {SGDOptimizer} from './optimizers/sgd_optimizer';\nexport {DataToGPUOptions, DataToGPUWebGLOption, GPUData, Scalar, Tensor, Tensor1D, Tensor2D, Tensor3D, Tensor4D, Tensor5D, TensorBuffer, Variable} from './tensor';\nexport {GradSaveFunc, NamedTensorMap, TensorContainer, TensorContainerArray, TensorContainerObject} from './tensor_types';\nexport {BackendValues, DataType, DataTypeMap, DataTypeFor, DataValues, NumericDataType, PixelData, Rank, RecursiveArray, ScalarLike, ShapeMap, sumOutType, TensorLike, TypedArray, upcastType, WebGLData, WebGPUData} from './types';\n\nexport * from './ops/ops';\nexport {Reduction} from './ops/loss_ops_utils';\n\nexport * from './train';\nexport * from './globals';\nexport * from './kernel_registry';\nexport {TensorInfo, DataId} from './tensor_info';\nexport {customGrad, grad, grads, valueAndGrad, valueAndGrads, variableGrads} from './gradients';\n\nexport {TimingInfo, MemoryInfo, ForwardFunc} from './engine';\nexport {Environment, env, ENV} from './environment';\nexport {Platform} from './platforms/platform';\n\nexport {version as version_core};\n\n// Top-level method exports.\nexport {nextFrame} from './browser_util';\n\n// Second level exports.\nimport * as backend_util from './backends/backend_util';\nimport * as device_util from './device_util';\nexport {\n  browser,\n  io,\n  math,\n  serialization,\n  test_util,\n  util,\n  backend_util,\n  broadcast_util,\n  tensor_util,\n  slice_util,\n  gather_util,\n  scatter_util,\n  device_util\n};\n\nimport * as kernel_impls from './backends/kernel_impls';\nexport {kernel_impls};\n// Backend specific.\nexport {KernelBackend, BackendTimingInfo, DataMover, DataStorage} from './backends/backend';\n\n// Export all kernel names / info.\nexport * from './kernel_names';\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAiBA;AACA;AACA;AAEA;;;;;;;;AASA;AACA,OAAO,KAAKA,EAAE,MAAM,SAAS;AAC7B,OAAO,KAAKC,IAAI,MAAM,QAAQ;AAC9B,OAAO,KAAKC,cAAc,MAAM,sBAAsB;AACtD,OAAO,KAAKC,OAAO,MAAM,eAAe;AACxC,OAAO,KAAKC,WAAW,MAAM,sBAAsB;AACnD,OAAO,KAAKC,YAAY,MAAM,uBAAuB;AACrD,OAAO,KAAKC,UAAU,MAAM,kBAAkB;AAC9C,OAAO,KAAKC,aAAa,MAAM,iBAAiB;AAChD,OAAO,KAAKC,WAAW,MAAM,eAAe;AAC5C,OAAO,KAAKC,SAAS,MAAM,aAAa;AACxC,OAAO,KAAKC,IAAI,MAAM,QAAQ;AAC9B,SAAQC,OAAO,QAAO,WAAW;AAGjC,SAAQC,iBAAiB,QAAO,iCAAiC;AACjE,SAAQC,gBAAgB,QAAO,gCAAgC;AAC/D,SAAQC,aAAa,QAAO,6BAA6B;AACzD,SAAQC,eAAe,QAAO,+BAA+B;AAC7D,SAAQC,iBAAiB,QAAO,iCAAiC;AACjE,SAAQC,SAAS,QAAO,wBAAwB;AAChD;AACA,SAAQC,qBAAqB,QAAO,qCAAqC;AACzE,SAAQC,gBAAgB,QAAO,gCAAgC;AAC/D,SAAQC,YAAY,QAAO,4BAA4B;AACvD,SAAiEC,MAAM,EAAoDC,YAAY,EAAEC,QAAQ,QAAO,UAAU;AAElK,SAAmGC,IAAI,EAAwCC,UAAU,EAA0BC,UAAU,QAA8B,SAAS;AAEpO,cAAc,WAAW;AACzB,SAAQC,SAAS,QAAO,sBAAsB;AAE9C,cAAc,SAAS;AACvB,cAAc,WAAW;AACzB,cAAc,mBAAmB;AAEjC,SAAQC,UAAU,EAAEC,IAAI,EAAEC,KAAK,EAAEC,YAAY,EAAEC,aAAa,EAAEC,aAAa,QAAO,aAAa;AAG/F,SAAQC,WAAW,EAAEC,GAAG,EAAEC,GAAG,QAAO,eAAe;AAGnD,SAAQzB,OAAO,IAAI0B,YAAY;AAE/B;AACA,SAAQC,SAAS,QAAO,gBAAgB;AAExC;AACA,OAAO,KAAKC,YAAY,MAAM,yBAAyB;AACvD,OAAO,KAAKC,WAAW,MAAM,eAAe;AAC5C,SACErC,OAAO,EACPH,EAAE,EACFC,IAAI,EACJM,aAAa,EACbE,SAAS,EACTC,IAAI,EACJ6B,YAAY,EACZrC,cAAc,EACdM,WAAW,EACXF,UAAU,EACVF,WAAW,EACXC,YAAY,EACZmC,WAAW;AAGb,OAAO,KAAKC,YAAY,MAAM,yBAAyB;AACvD,SAAQA,YAAY;AACpB;AACA,SAAQC,aAAa,EAAgCC,WAAW,QAAO,oBAAoB;AAE3F;AACA,cAAc,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}