{"ast": null, "code": "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { MirrorPad } from '../kernel_names';\nimport { slice } from '../ops/slice';\nexport const mirrorPadGradConfig = {\n  kernelName: MirrorPad,\n  inputsToSave: ['x'],\n  gradFunc: (dy, saved, attrs) => {\n    // Pad introduces values around the original tensor, so the gradient\n    // slices the original shape out of the gradient.\n    const x = saved[0];\n    const {\n      paddings\n    } = attrs;\n    const begin = paddings.map(p => p[0]);\n    return {\n      x: () => slice(dy, begin, x.shape)\n    };\n  }\n};", "map": {"version": 3, "names": ["MirrorPad", "slice", "mirrorPadGradConfig", "kernelName", "inputsToSave", "grad<PERSON>unc", "dy", "saved", "attrs", "x", "paddings", "begin", "map", "p", "shape"], "sources": ["C:\\tfjs-core\\src\\gradients\\MirrorPad_grad.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {MirrorPad, MirrorPadAttrs} from '../kernel_names';\nimport {GradConfig, NamedAttrMap} from '../kernel_registry';\nimport {slice} from '../ops/slice';\nimport {Tensor} from '../tensor';\n\nexport const mirrorPadGradConfig: GradConfig = {\n  kernelName: MirrorPad,\n  inputsToSave: ['x'],\n  gradFunc: (dy: Tensor, saved: Tensor[], attrs: NamedAttrMap) => {\n    // Pad introduces values around the original tensor, so the gradient\n    // slices the original shape out of the gradient.\n    const x = saved[0];\n    const {paddings} = attrs as unknown as MirrorPadAttrs;\n    const begin = paddings.map(p => p[0]);\n    return {x: () => slice(dy, begin, x.shape)};\n  }\n};\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAiBA,SAAQA,SAAS,QAAuB,iBAAiB;AAEzD,SAAQC,KAAK,QAAO,cAAc;AAGlC,OAAO,MAAMC,mBAAmB,GAAe;EAC7CC,UAAU,EAAEH,SAAS;EACrBI,YAAY,EAAE,CAAC,GAAG,CAAC;EACnBC,QAAQ,EAAEA,CAACC,EAAU,EAAEC,KAAe,EAAEC,KAAmB,KAAI;IAC7D;IACA;IACA,MAAMC,CAAC,GAAGF,KAAK,CAAC,CAAC,CAAC;IAClB,MAAM;MAACG;IAAQ,CAAC,GAAGF,KAAkC;IACrD,MAAMG,KAAK,GAAGD,QAAQ,CAACE,GAAG,CAACC,CAAC,IAAIA,CAAC,CAAC,CAAC,CAAC,CAAC;IACrC,OAAO;MAACJ,CAAC,EAAEA,CAAA,KAAMR,KAAK,CAACK,EAAE,EAAEK,KAAK,EAAEF,CAAC,CAACK,KAAK;IAAC,CAAC;EAC7C;CACD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}