{"ast": null, "code": "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the License);\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an AS IS BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { Unique } from '@tensorflow/tfjs-core';\nimport { assertNotComplex } from '../cpu_util';\nimport { uniqueImpl } from './Unique_impl';\nexport function unique(args) {\n  const {\n    inputs,\n    attrs,\n    backend\n  } = args;\n  const {\n    axis\n  } = attrs;\n  const {\n    x\n  } = inputs;\n  assertNotComplex(x, 'unique');\n  const values = backend.data.get(x.dataId).values;\n  const {\n    outputValues,\n    outputShape,\n    indices\n  } = uniqueImpl(values, axis, x.shape, x.dtype);\n  return [backend.makeTensorInfo(outputShape, x.dtype, outputValues), backend.makeTensorInfo([indices.length], 'int32', indices)];\n}\nexport const uniqueConfig = {\n  kernelName: Unique,\n  backendName: 'cpu',\n  kernelFunc: unique\n};", "map": {"version": 3, "names": ["Unique", "assertNotComplex", "uniqueImpl", "unique", "args", "inputs", "attrs", "backend", "axis", "x", "values", "data", "get", "dataId", "outputValues", "outputShape", "indices", "shape", "dtype", "makeTensorInfo", "length", "uniqueConfig", "kernelName", "backendName", "kernelFunc"], "sources": ["C:\\tfjs-backend-cpu\\src\\kernels\\Unique.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the License);\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an AS IS BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {KernelConfig, KernelFunc, TensorInfo, Unique, UniqueAttrs, UniqueInputs} from '@tensorflow/tfjs-core';\n\nimport {MathBackendCPU} from '../backend_cpu';\nimport {assertNotComplex} from '../cpu_util';\n\nimport {uniqueImpl} from './Unique_impl';\n\nexport function unique(\n    args: {inputs: UniqueInputs, attrs: UniqueAttrs, backend: MathBackendCPU}):\n    TensorInfo[] {\n  const {inputs, attrs, backend} = args;\n  const {axis} = attrs;\n  const {x} = inputs;\n  assertNotComplex(x, 'unique');\n\n  const values = backend.data.get(x.dataId).values;\n  const {outputValues, outputShape, indices} =\n      uniqueImpl(values, axis, x.shape, x.dtype);\n  return [\n    backend.makeTensorInfo(outputShape, x.dtype, outputValues),\n    backend.makeTensorInfo([indices.length], 'int32', indices),\n  ];\n}\n\nexport const uniqueConfig: KernelConfig = {\n  kernelName: Unique,\n  backendName: 'cpu',\n  kernelFunc: unique as unknown as KernelFunc,\n};\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAiBA,SAA8CA,MAAM,QAAkC,uBAAuB;AAG7G,SAAQC,gBAAgB,QAAO,aAAa;AAE5C,SAAQC,UAAU,QAAO,eAAe;AAExC,OAAM,SAAUC,MAAMA,CAClBC,IAAyE;EAE3E,MAAM;IAACC,MAAM;IAAEC,KAAK;IAAEC;EAAO,CAAC,GAAGH,IAAI;EACrC,MAAM;IAACI;EAAI,CAAC,GAAGF,KAAK;EACpB,MAAM;IAACG;EAAC,CAAC,GAAGJ,MAAM;EAClBJ,gBAAgB,CAACQ,CAAC,EAAE,QAAQ,CAAC;EAE7B,MAAMC,MAAM,GAAGH,OAAO,CAACI,IAAI,CAACC,GAAG,CAACH,CAAC,CAACI,MAAM,CAAC,CAACH,MAAM;EAChD,MAAM;IAACI,YAAY;IAAEC,WAAW;IAAEC;EAAO,CAAC,GACtCd,UAAU,CAACQ,MAAM,EAAEF,IAAI,EAAEC,CAAC,CAACQ,KAAK,EAAER,CAAC,CAACS,KAAK,CAAC;EAC9C,OAAO,CACLX,OAAO,CAACY,cAAc,CAACJ,WAAW,EAAEN,CAAC,CAACS,KAAK,EAAEJ,YAAY,CAAC,EAC1DP,OAAO,CAACY,cAAc,CAAC,CAACH,OAAO,CAACI,MAAM,CAAC,EAAE,OAAO,EAAEJ,OAAO,CAAC,CAC3D;AACH;AAEA,OAAO,MAAMK,YAAY,GAAiB;EACxCC,UAAU,EAAEtB,MAAM;EAClBuB,WAAW,EAAE,KAAK;EAClBC,UAAU,EAAErB;CACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}