{"ast": null, "code": "/* global __webpack_dev_server_client__ */\n\nimport WebSocketClient from \"./clients/WebSocketClient.js\";\nimport { log } from \"./utils/log.js\";\n\n// this WebsocketClient is here as a default fallback, in case the client is not injected\n/* eslint-disable camelcase */\nvar Client =\n// eslint-disable-next-line no-nested-ternary\ntypeof __webpack_dev_server_client__ !== \"undefined\" ? typeof __webpack_dev_server_client__.default !== \"undefined\" ? __webpack_dev_server_client__.default : __webpack_dev_server_client__ : WebSocketClient;\n/* eslint-enable camelcase */\n\nvar retries = 0;\nvar maxRetries = 10;\n\n// Initialized client is exported so external consumers can utilize the same instance\n// It is mutable to enforce singleton\n// eslint-disable-next-line import/no-mutable-exports\nexport var client = null;\n\n/**\n * @param {string} url\n * @param {{ [handler: string]: (data?: any, params?: any) => any }} handlers\n * @param {number} [reconnect]\n */\nvar socket = function initSocket(url, handlers, reconnect) {\n  client = new Client(url);\n  client.onOpen(function () {\n    retries = 0;\n    if (typeof reconnect !== \"undefined\") {\n      maxRetries = reconnect;\n    }\n  });\n  client.onClose(function () {\n    if (retries === 0) {\n      handlers.close();\n    }\n\n    // Try to reconnect.\n    client = null;\n\n    // After 10 retries stop trying, to prevent logspam.\n    if (retries < maxRetries) {\n      // Exponentially increase timeout to reconnect.\n      // Respectfully copied from the package `got`.\n      // eslint-disable-next-line no-restricted-properties\n      var retryInMs = 1000 * Math.pow(2, retries) + Math.random() * 100;\n      retries += 1;\n      log.info(\"Trying to reconnect...\");\n      setTimeout(function () {\n        socket(url, handlers, reconnect);\n      }, retryInMs);\n    }\n  });\n  client.onMessage(\n  /**\n   * @param {any} data\n   */\n  function (data) {\n    var message = JSON.parse(data);\n    if (handlers[message.type]) {\n      handlers[message.type](message.data, message.params);\n    }\n  });\n};\nexport default socket;", "map": {"version": 3, "names": ["WebSocketClient", "log", "Client", "__webpack_dev_server_client__", "default", "retries", "maxRetries", "client", "socket", "initSocket", "url", "handlers", "reconnect", "onOpen", "onClose", "close", "retryInMs", "Math", "pow", "random", "info", "setTimeout", "onMessage", "data", "message", "JSON", "parse", "type", "params"], "sources": ["C:/tmsft/node_modules/webpack-dev-server/client/socket.js"], "sourcesContent": ["/* global __webpack_dev_server_client__ */\n\nimport WebSocketClient from \"./clients/WebSocketClient.js\";\nimport { log } from \"./utils/log.js\";\n\n// this WebsocketClient is here as a default fallback, in case the client is not injected\n/* eslint-disable camelcase */\nvar Client =\n// eslint-disable-next-line no-nested-ternary\ntypeof __webpack_dev_server_client__ !== \"undefined\" ? typeof __webpack_dev_server_client__.default !== \"undefined\" ? __webpack_dev_server_client__.default : __webpack_dev_server_client__ : WebSocketClient;\n/* eslint-enable camelcase */\n\nvar retries = 0;\nvar maxRetries = 10;\n\n// Initialized client is exported so external consumers can utilize the same instance\n// It is mutable to enforce singleton\n// eslint-disable-next-line import/no-mutable-exports\nexport var client = null;\n\n/**\n * @param {string} url\n * @param {{ [handler: string]: (data?: any, params?: any) => any }} handlers\n * @param {number} [reconnect]\n */\nvar socket = function initSocket(url, handlers, reconnect) {\n  client = new Client(url);\n  client.onOpen(function () {\n    retries = 0;\n    if (typeof reconnect !== \"undefined\") {\n      maxRetries = reconnect;\n    }\n  });\n  client.onClose(function () {\n    if (retries === 0) {\n      handlers.close();\n    }\n\n    // Try to reconnect.\n    client = null;\n\n    // After 10 retries stop trying, to prevent logspam.\n    if (retries < maxRetries) {\n      // Exponentially increase timeout to reconnect.\n      // Respectfully copied from the package `got`.\n      // eslint-disable-next-line no-restricted-properties\n      var retryInMs = 1000 * Math.pow(2, retries) + Math.random() * 100;\n      retries += 1;\n      log.info(\"Trying to reconnect...\");\n      setTimeout(function () {\n        socket(url, handlers, reconnect);\n      }, retryInMs);\n    }\n  });\n  client.onMessage(\n  /**\n   * @param {any} data\n   */\n  function (data) {\n    var message = JSON.parse(data);\n    if (handlers[message.type]) {\n      handlers[message.type](message.data, message.params);\n    }\n  });\n};\nexport default socket;"], "mappings": "AAAA;;AAEA,OAAOA,eAAe,MAAM,8BAA8B;AAC1D,SAASC,GAAG,QAAQ,gBAAgB;;AAEpC;AACA;AACA,IAAIC,MAAM;AACV;AACA,OAAOC,6BAA6B,KAAK,WAAW,GAAG,OAAOA,6BAA6B,CAACC,OAAO,KAAK,WAAW,GAAGD,6BAA6B,CAACC,OAAO,GAAGD,6BAA6B,GAAGH,eAAe;AAC7M;;AAEA,IAAIK,OAAO,GAAG,CAAC;AACf,IAAIC,UAAU,GAAG,EAAE;;AAEnB;AACA;AACA;AACA,OAAO,IAAIC,MAAM,GAAG,IAAI;;AAExB;AACA;AACA;AACA;AACA;AACA,IAAIC,MAAM,GAAG,SAASC,UAAUA,CAACC,GAAG,EAAEC,QAAQ,EAAEC,SAAS,EAAE;EACzDL,MAAM,GAAG,IAAIL,MAAM,CAACQ,GAAG,CAAC;EACxBH,MAAM,CAACM,MAAM,CAAC,YAAY;IACxBR,OAAO,GAAG,CAAC;IACX,IAAI,OAAOO,SAAS,KAAK,WAAW,EAAE;MACpCN,UAAU,GAAGM,SAAS;IACxB;EACF,CAAC,CAAC;EACFL,MAAM,CAACO,OAAO,CAAC,YAAY;IACzB,IAAIT,OAAO,KAAK,CAAC,EAAE;MACjBM,QAAQ,CAACI,KAAK,CAAC,CAAC;IAClB;;IAEA;IACAR,MAAM,GAAG,IAAI;;IAEb;IACA,IAAIF,OAAO,GAAGC,UAAU,EAAE;MACxB;MACA;MACA;MACA,IAAIU,SAAS,GAAG,IAAI,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEb,OAAO,CAAC,GAAGY,IAAI,CAACE,MAAM,CAAC,CAAC,GAAG,GAAG;MACjEd,OAAO,IAAI,CAAC;MACZJ,GAAG,CAACmB,IAAI,CAAC,wBAAwB,CAAC;MAClCC,UAAU,CAAC,YAAY;QACrBb,MAAM,CAACE,GAAG,EAAEC,QAAQ,EAAEC,SAAS,CAAC;MAClC,CAAC,EAAEI,SAAS,CAAC;IACf;EACF,CAAC,CAAC;EACFT,MAAM,CAACe,SAAS;EAChB;AACF;AACA;EACE,UAAUC,IAAI,EAAE;IACd,IAAIC,OAAO,GAAGC,IAAI,CAACC,KAAK,CAACH,IAAI,CAAC;IAC9B,IAAIZ,QAAQ,CAACa,OAAO,CAACG,IAAI,CAAC,EAAE;MAC1BhB,QAAQ,CAACa,OAAO,CAACG,IAAI,CAAC,CAACH,OAAO,CAACD,IAAI,EAAEC,OAAO,CAACI,MAAM,CAAC;IACtD;EACF,CAAC,CAAC;AACJ,CAAC;AACD,eAAepB,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}