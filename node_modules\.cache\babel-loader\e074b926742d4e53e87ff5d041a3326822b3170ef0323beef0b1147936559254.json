{"ast": null, "code": "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { ENGINE } from '../../engine';\nimport { FFT } from '../../kernel_names';\nimport { assert } from '../../util';\nimport { op } from '../operation';\n/**\n * Fast Fourier transform.\n *\n * Computes the 1-dimensional discrete Fourier transform over the inner-most\n * dimension of input.\n *\n * ```js\n * const real = tf.tensor1d([1, 2, 3]);\n * const imag = tf.tensor1d([1, 2, 3]);\n * const x = tf.complex(real, imag);\n *\n * x.fft().print();  // tf.spectral.fft(x).print();\n * ```\n * @param input The complex input to compute an fft over.\n *\n * @doc {heading: 'Operations', subheading: 'Spectral', namespace: 'spectral'}\n */\nfunction fft_(input) {\n  assert(input.dtype === 'complex64', () => `The dtype for tf.spectral.fft() must be complex64 ` + `but got ${input.dtype}.`);\n  const inputs = {\n    input\n  };\n  return ENGINE.runKernel(FFT, inputs);\n}\nexport const fft = /* @__PURE__ */op({\n  fft_\n});", "map": {"version": 3, "names": ["ENGINE", "FFT", "assert", "op", "fft_", "input", "dtype", "inputs", "runKernel", "fft"], "sources": ["C:\\tfjs-core\\src\\ops\\spectral\\fft.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {ENGINE} from '../../engine';\nimport {FFT, FFTInputs} from '../../kernel_names';\nimport {Tensor} from '../../tensor';\nimport {NamedTensorMap} from '../../tensor_types';\nimport {assert} from '../../util';\nimport {op} from '../operation';\n\n/**\n * Fast Fourier transform.\n *\n * Computes the 1-dimensional discrete Fourier transform over the inner-most\n * dimension of input.\n *\n * ```js\n * const real = tf.tensor1d([1, 2, 3]);\n * const imag = tf.tensor1d([1, 2, 3]);\n * const x = tf.complex(real, imag);\n *\n * x.fft().print();  // tf.spectral.fft(x).print();\n * ```\n * @param input The complex input to compute an fft over.\n *\n * @doc {heading: 'Operations', subheading: 'Spectral', namespace: 'spectral'}\n */\nfunction fft_(input: Tensor): Tensor {\n  assert(\n      input.dtype === 'complex64',\n      () => `The dtype for tf.spectral.fft() must be complex64 ` +\n          `but got ${input.dtype}.`);\n\n  const inputs: FFTInputs = {input};\n\n  return ENGINE.runKernel(FFT, inputs as unknown as NamedTensorMap);\n}\n\nexport const fft = /* @__PURE__ */ op({fft_});\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAiBA,SAAQA,MAAM,QAAO,cAAc;AACnC,SAAQC,GAAG,QAAkB,oBAAoB;AAGjD,SAAQC,MAAM,QAAO,YAAY;AACjC,SAAQC,EAAE,QAAO,cAAc;AAE/B;;;;;;;;;;;;;;;;;AAiBA,SAASC,IAAIA,CAACC,KAAa;EACzBH,MAAM,CACFG,KAAK,CAACC,KAAK,KAAK,WAAW,EAC3B,MAAM,oDAAoD,GACtD,WAAWD,KAAK,CAACC,KAAK,GAAG,CAAC;EAElC,MAAMC,MAAM,GAAc;IAACF;EAAK,CAAC;EAEjC,OAAOL,MAAM,CAACQ,SAAS,CAACP,GAAG,EAAEM,MAAmC,CAAC;AACnE;AAEA,OAAO,MAAME,GAAG,GAAG,eAAgBN,EAAE,CAAC;EAACC;AAAI,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}