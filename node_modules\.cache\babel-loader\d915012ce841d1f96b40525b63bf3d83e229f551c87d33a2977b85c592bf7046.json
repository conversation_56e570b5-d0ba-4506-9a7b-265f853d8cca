{"ast": null, "code": "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { Select } from '../kernel_names';\nimport { cast } from '../ops/cast';\nimport { logicalNot } from '../ops/logical_not';\nimport { mul } from '../ops/mul';\nimport { zerosLike } from '../ops/zeros_like';\nexport const selectGradConfig = {\n  kernelName: Select,\n  inputsToSave: ['condition'],\n  gradFunc: (dy, saved) => {\n    const [condition] = saved;\n    return {\n      // TODO(julianoks): Return null for condition gradient\n      // when backprop supports it.\n      condition: () => cast(zerosLike(condition), 'float32'),\n      t: () => mul(dy, cast(condition, dy.dtype)),\n      e: () => mul(dy, cast(logicalNot(condition), dy.dtype))\n    };\n  }\n};", "map": {"version": 3, "names": ["Select", "cast", "logicalNot", "mul", "zerosLike", "selectGradConfig", "kernelName", "inputsToSave", "grad<PERSON>unc", "dy", "saved", "condition", "t", "dtype", "e"], "sources": ["C:\\tfjs-core\\src\\gradients\\Select_grad.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {Select} from '../kernel_names';\nimport {GradConfig} from '../kernel_registry';\nimport {cast} from '../ops/cast';\nimport {logicalNot} from '../ops/logical_not';\nimport {mul} from '../ops/mul';\nimport {zerosLike} from '../ops/zeros_like';\nimport {Tensor} from '../tensor';\n\nexport const selectGradConfig: GradConfig = {\n  kernelName: Select,\n  inputsToSave: ['condition'],\n  gradFunc: (dy: Tensor, saved: Tensor[]) => {\n    const [condition] = saved;\n    return {\n      // TODO(julianoks): Return null for condition gradient\n      // when backprop supports it.\n      condition: () => cast(zerosLike(condition), 'float32'),\n      t: () => mul(dy, cast(condition, dy.dtype)),\n      e: () => mul(dy, cast(logicalNot(condition), dy.dtype))\n    };\n  }\n};\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAiBA,SAAQA,MAAM,QAAO,iBAAiB;AAEtC,SAAQC,IAAI,QAAO,aAAa;AAChC,SAAQC,UAAU,QAAO,oBAAoB;AAC7C,SAAQC,GAAG,QAAO,YAAY;AAC9B,SAAQC,SAAS,QAAO,mBAAmB;AAG3C,OAAO,MAAMC,gBAAgB,GAAe;EAC1CC,UAAU,EAAEN,MAAM;EAClBO,YAAY,EAAE,CAAC,WAAW,CAAC;EAC3BC,QAAQ,EAAEA,CAACC,EAAU,EAAEC,KAAe,KAAI;IACxC,MAAM,CAACC,SAAS,CAAC,GAAGD,KAAK;IACzB,OAAO;MACL;MACA;MACAC,SAAS,EAAEA,CAAA,KAAMV,IAAI,CAACG,SAAS,CAACO,SAAS,CAAC,EAAE,SAAS,CAAC;MACtDC,CAAC,EAAEA,CAAA,KAAMT,GAAG,CAACM,EAAE,EAAER,IAAI,CAACU,SAAS,EAAEF,EAAE,CAACI,KAAK,CAAC,CAAC;MAC3CC,CAAC,EAAEA,CAAA,KAAMX,GAAG,CAACM,EAAE,EAAER,IAAI,CAACC,UAAU,CAACS,SAAS,CAAC,EAAEF,EAAE,CAACI,KAAK,CAAC;KACvD;EACH;CACD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}