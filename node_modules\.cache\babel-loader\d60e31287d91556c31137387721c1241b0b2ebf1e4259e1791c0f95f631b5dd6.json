{"ast": null, "code": "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the License);\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an AS IS BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { Log1p } from '@tensorflow/tfjs-core';\nimport { unaryKernelFunc } from '../utils/unary_utils';\nexport const log1p = unaryKernelFunc(Log1p, xi => Math.log1p(xi));\nexport const log1pConfig = {\n  kernelName: Log1p,\n  backendName: 'cpu',\n  kernelFunc: log1p\n};", "map": {"version": 3, "names": ["Log1p", "unaryKernelFunc", "log1p", "xi", "Math", "log1pConfig", "kernelName", "backendName", "kernelFunc"], "sources": ["C:\\tfjs-backend-cpu\\src\\kernels\\Log1p.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the License);\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an AS IS BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {KernelConfig, Log1p} from '@tensorflow/tfjs-core';\n\nimport {unaryKernelFunc} from '../utils/unary_utils';\n\nexport const log1p = unaryKernelFunc(Log1p, (xi) => Math.log1p(xi));\n\nexport const log1pConfig: KernelConfig = {\n  kernelName: Log1p,\n  backendName: 'cpu',\n  kernelFunc: log1p,\n};\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAiBA,SAAsBA,KAAK,QAAO,uBAAuB;AAEzD,SAAQC,eAAe,QAAO,sBAAsB;AAEpD,OAAO,MAAMC,KAAK,GAAGD,eAAe,CAACD,KAAK,EAAGG,EAAE,IAAKC,IAAI,CAACF,KAAK,CAACC,EAAE,CAAC,CAAC;AAEnE,OAAO,MAAME,WAAW,GAAiB;EACvCC,UAAU,EAAEN,KAAK;EACjBO,WAAW,EAAE,KAAK;EAClBC,UAAU,EAAEN;CACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}