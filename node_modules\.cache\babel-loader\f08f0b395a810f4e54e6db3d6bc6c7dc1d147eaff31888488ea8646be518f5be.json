{"ast": null, "code": "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { kernel_impls, NonMaxSuppressionV4 } from '@tensorflow/tfjs-core';\nconst nonMaxSuppressionV4Impl = kernel_impls.nonMaxSuppressionV4Impl;\nimport { assertNotComplex } from '../cpu_util';\nexport function nonMaxSuppressionV4(args) {\n  const {\n    inputs,\n    backend,\n    attrs\n  } = args;\n  const {\n    boxes,\n    scores\n  } = inputs;\n  const {\n    maxOutputSize,\n    iouThreshold,\n    scoreThreshold,\n    padToMaxOutputSize\n  } = attrs;\n  assertNotComplex(boxes, 'NonMaxSuppressionPadded');\n  const boxesVals = backend.data.get(boxes.dataId).values;\n  const scoresVals = backend.data.get(scores.dataId).values;\n  const {\n    selectedIndices,\n    validOutputs\n  } = nonMaxSuppressionV4Impl(boxesVals, scoresVals, maxOutputSize, iouThreshold, scoreThreshold, padToMaxOutputSize);\n  return [backend.makeTensorInfo([selectedIndices.length], 'int32', new Int32Array(selectedIndices)), backend.makeTensorInfo([], 'int32', new Int32Array([validOutputs]))];\n}\nexport const nonMaxSuppressionV4Config = {\n  kernelName: NonMaxSuppressionV4,\n  backendName: 'cpu',\n  kernelFunc: nonMaxSuppressionV4\n};", "map": {"version": 3, "names": ["kernel_impls", "NonMaxSuppressionV4", "nonMaxSuppressionV4Impl", "assertNotComplex", "nonMaxSuppressionV4", "args", "inputs", "backend", "attrs", "boxes", "scores", "maxOutputSize", "iouThreshold", "scoreThreshold", "padToMaxOutputSize", "boxesVals", "data", "get", "dataId", "values", "scoresVals", "selectedIndices", "validOutputs", "makeTensorInfo", "length", "Int32Array", "nonMaxSuppressionV4Config", "kernelName", "backendName", "kernelFunc"], "sources": ["C:\\tfjs-backend-cpu\\src\\kernels\\NonMaxSuppressionV4.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {kernel_impls, KernelConfig, KernelFunc, NonMaxSuppressionV4, NonMaxSuppressionV4Attrs, NonMaxSuppressionV4Inputs, TensorInfo, TypedArray} from '@tensorflow/tfjs-core';\n\nconst nonMaxSuppressionV4Impl = kernel_impls.nonMaxSuppressionV4Impl;\nimport {MathBackendCPU} from '../backend_cpu';\nimport {assertNotComplex} from '../cpu_util';\n\nexport function nonMaxSuppressionV4(args: {\n  inputs: NonMaxSuppressionV4Inputs,\n  backend: MathBackendCPU,\n  attrs: NonMaxSuppressionV4Attrs\n}): [TensorInfo, TensorInfo] {\n  const {inputs, backend, attrs} = args;\n  const {boxes, scores} = inputs;\n  const {maxOutputSize, iouThreshold, scoreThreshold, padToMaxOutputSize} =\n      attrs;\n\n  assertNotComplex(boxes, 'NonMaxSuppressionPadded');\n\n  const boxesVals = backend.data.get(boxes.dataId).values as TypedArray;\n  const scoresVals = backend.data.get(scores.dataId).values as TypedArray;\n\n  const {selectedIndices, validOutputs} = nonMaxSuppressionV4Impl(\n      boxesVals, scoresVals, maxOutputSize, iouThreshold, scoreThreshold,\n      padToMaxOutputSize);\n\n  return [\n    backend.makeTensorInfo(\n        [selectedIndices.length], 'int32', new Int32Array(selectedIndices)),\n    backend.makeTensorInfo([], 'int32', new Int32Array([validOutputs]))\n  ];\n}\nexport const nonMaxSuppressionV4Config: KernelConfig = {\n  kernelName: NonMaxSuppressionV4,\n  backendName: 'cpu',\n  kernelFunc: nonMaxSuppressionV4 as unknown as KernelFunc\n};\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAiBA,SAAQA,YAAY,EAA4BC,mBAAmB,QAAoF,uBAAuB;AAE9K,MAAMC,uBAAuB,GAAGF,YAAY,CAACE,uBAAuB;AAEpE,SAAQC,gBAAgB,QAAO,aAAa;AAE5C,OAAM,SAAUC,mBAAmBA,CAACC,IAInC;EACC,MAAM;IAACC,MAAM;IAAEC,OAAO;IAAEC;EAAK,CAAC,GAAGH,IAAI;EACrC,MAAM;IAACI,KAAK;IAAEC;EAAM,CAAC,GAAGJ,MAAM;EAC9B,MAAM;IAACK,aAAa;IAAEC,YAAY;IAAEC,cAAc;IAAEC;EAAkB,CAAC,GACnEN,KAAK;EAETL,gBAAgB,CAACM,KAAK,EAAE,yBAAyB,CAAC;EAElD,MAAMM,SAAS,GAAGR,OAAO,CAACS,IAAI,CAACC,GAAG,CAACR,KAAK,CAACS,MAAM,CAAC,CAACC,MAAoB;EACrE,MAAMC,UAAU,GAAGb,OAAO,CAACS,IAAI,CAACC,GAAG,CAACP,MAAM,CAACQ,MAAM,CAAC,CAACC,MAAoB;EAEvE,MAAM;IAACE,eAAe;IAAEC;EAAY,CAAC,GAAGpB,uBAAuB,CAC3Da,SAAS,EAAEK,UAAU,EAAET,aAAa,EAAEC,YAAY,EAAEC,cAAc,EAClEC,kBAAkB,CAAC;EAEvB,OAAO,CACLP,OAAO,CAACgB,cAAc,CAClB,CAACF,eAAe,CAACG,MAAM,CAAC,EAAE,OAAO,EAAE,IAAIC,UAAU,CAACJ,eAAe,CAAC,CAAC,EACvEd,OAAO,CAACgB,cAAc,CAAC,EAAE,EAAE,OAAO,EAAE,IAAIE,UAAU,CAAC,CAACH,YAAY,CAAC,CAAC,CAAC,CACpE;AACH;AACA,OAAO,MAAMI,yBAAyB,GAAiB;EACrDC,UAAU,EAAE1B,mBAAmB;EAC/B2B,WAAW,EAAE,KAAK;EAClBC,UAAU,EAAEzB;CACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}