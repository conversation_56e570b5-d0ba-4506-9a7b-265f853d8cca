{"ast": null, "code": "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { LessEqual } from '@tensorflow/tfjs-core';\nimport { createSimpleBinaryKernelImpl } from '../utils/binary_impl';\nimport { binaryKernelFunc } from '../utils/binary_utils';\nexport const lessEqualImpl = createSimpleBinaryKernelImpl((a, b) => a <= b ? 1 : 0);\nexport const lessEqual = binaryKernelFunc(LessEqual, lessEqualImpl, null /* complexImpl */, 'bool');\nexport const lessEqualConfig = {\n  kernelName: LessEqual,\n  backendName: 'cpu',\n  kernelFunc: lessEqual\n};", "map": {"version": 3, "names": ["LessEqual", "createSimpleBinaryKernelImpl", "binaryKernelFunc", "lessEqualImpl", "a", "b", "lessEqual", "lessEqualConfig", "kernelName", "backendName", "kernelFunc"], "sources": ["C:\\tfjs-backend-cpu\\src\\kernels\\LessEqual.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {KernelConfig, LessEqual} from '@tensorflow/tfjs-core';\n\nimport {createSimpleBinaryKernelImpl} from '../utils/binary_impl';\nimport {binaryKernelFunc} from '../utils/binary_utils';\n\nexport const lessEqualImpl =\n    createSimpleBinaryKernelImpl((a: number, b: number) => (a <= b) ? 1 : 0);\nexport const lessEqual =\n    binaryKernelFunc(LessEqual, lessEqualImpl, null /* complexImpl */, 'bool');\n\nexport const lessEqualConfig: KernelConfig = {\n  kernelName: LessEqual,\n  backendName: 'cpu',\n  kernelFunc: lessEqual\n};\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAiBA,SAAsBA,SAAS,QAAO,uBAAuB;AAE7D,SAAQC,4BAA4B,QAAO,sBAAsB;AACjE,SAAQC,gBAAgB,QAAO,uBAAuB;AAEtD,OAAO,MAAMC,aAAa,GACtBF,4BAA4B,CAAC,CAACG,CAAS,EAAEC,CAAS,KAAMD,CAAC,IAAIC,CAAC,GAAI,CAAC,GAAG,CAAC,CAAC;AAC5E,OAAO,MAAMC,SAAS,GAClBJ,gBAAgB,CAACF,SAAS,EAAEG,aAAa,EAAE,IAAI,CAAC,mBAAmB,MAAM,CAAC;AAE9E,OAAO,MAAMI,eAAe,GAAiB;EAC3CC,UAAU,EAAER,SAAS;EACrBS,WAAW,EAAE,KAAK;EAClBC,UAAU,EAAEJ;CACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}