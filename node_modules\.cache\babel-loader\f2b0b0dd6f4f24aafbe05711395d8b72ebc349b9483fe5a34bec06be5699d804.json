{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.transformReply = exports.transformArguments = exports.IS_READ_ONLY = exports.FIRST_KEY_INDEX = void 0;\nexports.FIRST_KEY_INDEX = 2;\nexports.IS_READ_ONLY = true;\nfunction transformArguments(key) {\n  return ['XINFO', 'GROUPS', key];\n}\nexports.transformArguments = transformArguments;\nfunction transformReply(rawReply) {\n  return rawReply.map(group => ({\n    name: group[1],\n    consumers: group[3],\n    pending: group[5],\n    lastDeliveredId: group[7]\n  }));\n}\nexports.transformReply = transformReply;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "transformReply", "transformArguments", "IS_READ_ONLY", "FIRST_KEY_INDEX", "key", "rawReply", "map", "group", "name", "consumers", "pending", "lastDeliveredId"], "sources": ["C:/tmsft/node_modules/@redis/client/dist/lib/commands/XINFO_GROUPS.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.transformReply = exports.transformArguments = exports.IS_READ_ONLY = exports.FIRST_KEY_INDEX = void 0;\nexports.FIRST_KEY_INDEX = 2;\nexports.IS_READ_ONLY = true;\nfunction transformArguments(key) {\n    return ['XINFO', 'GROUPS', key];\n}\nexports.transformArguments = transformArguments;\nfunction transformReply(rawReply) {\n    return rawReply.map(group => ({\n        name: group[1],\n        consumers: group[3],\n        pending: group[5],\n        lastDeliveredId: group[7]\n    }));\n}\nexports.transformReply = transformReply;\n"], "mappings": "AAAA,YAAY;;AACZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEC,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7DD,OAAO,CAACE,cAAc,GAAGF,OAAO,CAACG,kBAAkB,GAAGH,OAAO,CAACI,YAAY,GAAGJ,OAAO,CAACK,eAAe,GAAG,KAAK,CAAC;AAC7GL,OAAO,CAACK,eAAe,GAAG,CAAC;AAC3BL,OAAO,CAACI,YAAY,GAAG,IAAI;AAC3B,SAASD,kBAAkBA,CAACG,GAAG,EAAE;EAC7B,OAAO,CAAC,OAAO,EAAE,QAAQ,EAAEA,GAAG,CAAC;AACnC;AACAN,OAAO,CAACG,kBAAkB,GAAGA,kBAAkB;AAC/C,SAASD,cAAcA,CAACK,QAAQ,EAAE;EAC9B,OAAOA,QAAQ,CAACC,GAAG,CAACC,KAAK,KAAK;IAC1BC,IAAI,EAAED,KAAK,CAAC,CAAC,CAAC;IACdE,SAAS,EAAEF,KAAK,CAAC,CAAC,CAAC;IACnBG,OAAO,EAAEH,KAAK,CAAC,CAAC,CAAC;IACjBI,eAAe,EAAEJ,KAAK,CAAC,CAAC;EAC5B,CAAC,CAAC,CAAC;AACP;AACAT,OAAO,CAACE,cAAc,GAAGA,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}