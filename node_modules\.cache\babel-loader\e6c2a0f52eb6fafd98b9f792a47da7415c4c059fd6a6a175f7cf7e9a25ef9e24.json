{"ast": null, "code": "/**\n * @license\n * Copyright 2017 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { env, util } from '@tensorflow/tfjs-core';\nexport var PackingScheme;\n(function (PackingScheme) {\n  /**\n   * All values in a single texel are densely packed without any constraints.\n   *\n   * This is how the shader encodes a tensor with shape = [2, 3, 4]\n   * (indices are [batch, row, col]).\n   *\n   * 000|001   010|011   020|021\n   * -------   -------   -------\n   * 002|003   012|013   022|023\n   *\n   * 100|101   110|111   120|121\n   * -------   -------   -------\n   * 102|103   112|113   122|123\n   *\n   */\n  PackingScheme[PackingScheme[\"DENSE\"] = 0] = \"DENSE\";\n  /**\n   * Single texels contain only values from the same batch, and from adjacent\n   * rows and columns.\n   *\n   * This is how the shader encodes a tensor with shape = [2, 3, 5]\n   * (indices are [batch, row, col]).\n   *\n   * 000|001   002|003   004|xxx   020|021   022|023   024|xxx\n   * -------   -------   -------   -------   -------   -------\n   * 010|011   012|013   014|xxx   xxx|xxx   xxx|xxx   xxx|xxx\n   *\n   * 100|101   102|103   104|xxx   120|121   122|123   124|xxx\n   * -------   -------   -------   -------   -------   -------\n   * 110|111   112|113   114|xxx   xxx|xxx   xxx|xxx   xxx|xxx\n   *\n   */\n  PackingScheme[PackingScheme[\"SHARED_BATCH\"] = 1] = \"SHARED_BATCH\";\n})(PackingScheme || (PackingScheme = {}));\nexport var TextureUsage;\n(function (TextureUsage) {\n  TextureUsage[TextureUsage[\"RENDER\"] = 0] = \"RENDER\";\n  TextureUsage[TextureUsage[\"UPLOAD\"] = 1] = \"UPLOAD\";\n  TextureUsage[TextureUsage[\"PIXELS\"] = 2] = \"PIXELS\";\n  TextureUsage[TextureUsage[\"DOWNLOAD\"] = 3] = \"DOWNLOAD\";\n})(TextureUsage || (TextureUsage = {}));\nexport var PhysicalTextureType;\n(function (PhysicalTextureType) {\n  PhysicalTextureType[PhysicalTextureType[\"UNPACKED_FLOAT16\"] = 0] = \"UNPACKED_FLOAT16\";\n  PhysicalTextureType[PhysicalTextureType[\"UNPACKED_FLOAT32\"] = 1] = \"UNPACKED_FLOAT32\";\n  PhysicalTextureType[PhysicalTextureType[\"PACKED_4X1_UNSIGNED_BYTE\"] = 2] = \"PACKED_4X1_UNSIGNED_BYTE\";\n  PhysicalTextureType[PhysicalTextureType[\"PACKED_2X2_FLOAT32\"] = 3] = \"PACKED_2X2_FLOAT32\";\n  PhysicalTextureType[PhysicalTextureType[\"PACKED_2X2_FLOAT16\"] = 4] = \"PACKED_2X2_FLOAT16\";\n})(PhysicalTextureType || (PhysicalTextureType = {}));\nexport function getUnpackedMatrixTextureShapeWidthHeight(rows, columns) {\n  return [columns, rows];\n}\nexport function getUnpackedArraySizeFromMatrixSize(matrixSize, channelsPerTexture) {\n  return matrixSize * channelsPerTexture;\n}\nexport function getColorMatrixTextureShapeWidthHeight(rows, columns) {\n  return [columns * 4, rows];\n}\n/**\n * Get shape for densely packed RGBA texture.\n */\nexport function getDenseTexShape(shape) {\n  const size = util.sizeFromShape(shape);\n  const texelsNeeded = Math.ceil(size / 4);\n  return util.sizeToSquarishShape(texelsNeeded);\n}\nexport function getMatrixSizeFromUnpackedArraySize(unpackedSize, channelsPerTexture) {\n  if (unpackedSize % channelsPerTexture !== 0) {\n    throw new Error(`unpackedSize (${unpackedSize}) must be a multiple of ` + `${channelsPerTexture}`);\n  }\n  return unpackedSize / channelsPerTexture;\n}\nexport function decodeMatrixFromUnpackedColorRGBAArray(unpackedArray, matrix, channels) {\n  const requiredSize = unpackedArray.length * channels / 4;\n  if (matrix.length < requiredSize) {\n    throw new Error(`matrix length (${matrix.length}) must be >= ${requiredSize}`);\n  }\n  let dst = 0;\n  for (let src = 0; src < unpackedArray.length; src += 4) {\n    for (let c = 0; c < channels; c++) {\n      matrix[dst++] = unpackedArray[src + c];\n    }\n  }\n}\nexport function getPackedMatrixTextureShapeWidthHeight(rows, columns) {\n  return [Math.max(1, Math.ceil(columns / 2)), Math.max(1, Math.ceil(rows / 2))];\n}\nexport function getPackedRGBAArraySizeFromMatrixShape(rows, columns) {\n  const [w, h] = getPackedMatrixTextureShapeWidthHeight(rows, columns);\n  return w * h * 4;\n}\nexport function getTextureConfig(\n// tslint:disable-next-line:no-any\ngl, textureHalfFloatExtension) {\n  // tslint:disable-next-line:no-any\n  const glany = gl;\n  let internalFormatFloat;\n  let internalFormatHalfFloat;\n  let internalFormatPackedHalfFloat;\n  let internalFormatPackedFloat;\n  let textureFormatFloat;\n  let downloadTextureFormat;\n  let downloadUnpackNumChannels;\n  let defaultNumChannels;\n  let textureTypeHalfFloat;\n  let textureTypeFloat;\n  if (env().getNumber('WEBGL_VERSION') === 2) {\n    internalFormatFloat = glany.R32F;\n    internalFormatHalfFloat = glany.R16F;\n    internalFormatPackedHalfFloat = glany.RGBA16F;\n    internalFormatPackedFloat = glany.RGBA32F;\n    textureFormatFloat = glany.RED;\n    downloadUnpackNumChannels = 4;\n    defaultNumChannels = 1;\n    textureTypeHalfFloat = glany.HALF_FLOAT;\n    textureTypeFloat = glany.FLOAT;\n    downloadTextureFormat = glany.RGBA8;\n  } else {\n    internalFormatFloat = gl.RGBA;\n    internalFormatHalfFloat = gl.RGBA;\n    internalFormatPackedHalfFloat = gl.RGBA;\n    internalFormatPackedFloat = glany.RGBA;\n    textureFormatFloat = gl.RGBA;\n    downloadUnpackNumChannels = 4;\n    defaultNumChannels = 4;\n    textureTypeHalfFloat = textureHalfFloatExtension != null ? textureHalfFloatExtension.HALF_FLOAT_OES : null;\n    textureTypeFloat = gl.FLOAT;\n    downloadTextureFormat = gl.RGBA;\n  }\n  return {\n    internalFormatFloat,\n    internalFormatHalfFloat,\n    internalFormatPackedHalfFloat,\n    internalFormatPackedFloat,\n    textureFormatFloat,\n    downloadTextureFormat,\n    downloadUnpackNumChannels,\n    defaultNumChannels,\n    textureTypeHalfFloat,\n    textureTypeFloat\n  };\n}", "map": {"version": 3, "names": ["env", "util", "PackingScheme", "TextureUsage", "PhysicalTextureType", "getUnpackedMatrixTextureShapeWidthHeight", "rows", "columns", "getUnpackedArraySizeFromMatrixSize", "matrixSize", "channelsPerTexture", "getColorMatrixTextureShapeWidthHeight", "getDenseTexShape", "shape", "size", "sizeFromShape", "texelsNeeded", "Math", "ceil", "sizeToSquarishShape", "getMatrixSizeFromUnpackedArraySize", "unpackedSize", "Error", "decodeMatrixFromUnpackedColorRGBAArray", "unpackedArray", "matrix", "channels", "requiredSize", "length", "dst", "src", "c", "getPackedMatrixTextureShapeWidthHeight", "max", "getPackedRGBAArraySizeFromMatrixShape", "w", "h", "getTextureConfig", "gl", "textureHalfFloatExtension", "glany", "internalFormatFloat", "internalFormatHalfFloat", "internalFormatPackedHalfFloat", "internalFormatPackedFloat", "textureFormatFloat", "downloadTextureFormat", "downloadUnpackNumChannels", "defaultNumChannels", "textureTypeHalfFloat", "textureTypeFloat", "getNumber", "R32F", "R16F", "RGBA16F", "RGBA32F", "RED", "HALF_FLOAT", "FLOAT", "RGBA8", "RGBA", "HALF_FLOAT_OES"], "sources": ["C:\\tfjs-backend-webgl\\src\\tex_util.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2017 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {backend_util, DataId, DataType, env, TensorInfo, util} from '@tensorflow/tfjs-core';\n\nexport enum PackingScheme {\n  /**\n   * All values in a single texel are densely packed without any constraints.\n   *\n   * This is how the shader encodes a tensor with shape = [2, 3, 4]\n   * (indices are [batch, row, col]).\n   *\n   * 000|001   010|011   020|021\n   * -------   -------   -------\n   * 002|003   012|013   022|023\n   *\n   * 100|101   110|111   120|121\n   * -------   -------   -------\n   * 102|103   112|113   122|123\n   *\n   */\n  DENSE,\n\n  /**\n   * Single texels contain only values from the same batch, and from adjacent\n   * rows and columns.\n   *\n   * This is how the shader encodes a tensor with shape = [2, 3, 5]\n   * (indices are [batch, row, col]).\n   *\n   * 000|001   002|003   004|xxx   020|021   022|023   024|xxx\n   * -------   -------   -------   -------   -------   -------\n   * 010|011   012|013   014|xxx   xxx|xxx   xxx|xxx   xxx|xxx\n   *\n   * 100|101   102|103   104|xxx   120|121   122|123   124|xxx\n   * -------   -------   -------   -------   -------   -------\n   * 110|111   112|113   114|xxx   xxx|xxx   xxx|xxx   xxx|xxx\n   *\n   */\n  SHARED_BATCH\n}\n\nexport enum TextureUsage {\n  RENDER,\n  UPLOAD,\n  PIXELS,\n  DOWNLOAD\n}\n\nexport enum PhysicalTextureType {\n  UNPACKED_FLOAT16,\n  UNPACKED_FLOAT32,\n  PACKED_4X1_UNSIGNED_BYTE,\n  PACKED_2X2_FLOAT32,\n  PACKED_2X2_FLOAT16\n}\n\nexport interface Texture {\n  texture: WebGLTexture;\n  texShape: [number, number];\n}\nexport interface TextureData {\n  // Required.\n  shape: number[];\n  dtype: DataType;\n\n  // Optional.\n  values?: backend_util.BackendValues;\n  texture?: Texture;\n  // For complex numbers, the real and imaginary parts are stored as their own\n  // individual tensorInfos, with a parent joining the two with the\n  // complexTensors field. When this is defined, texture will be null.\n  complexTensorInfos?: {real: TensorInfo, imag: TensorInfo};\n  /** [rows, columns] shape of the texture. */\n  texShape?: [number, number];\n  usage?: TextureUsage;\n  isPacked?: boolean;\n\n  refCount: number;\n\n  // Available when the tensor has been sliced.\n  slice?: {\n    // Offset in the 'flat index' space.\n    flatOffset: number;\n    // Used for counting how many sliced tensors point to the same texture.\n    origDataId: DataId;\n  };\n}\n\nexport function getUnpackedMatrixTextureShapeWidthHeight(\n    rows: number, columns: number): [number, number] {\n  return [columns, rows];\n}\n\nexport function getUnpackedArraySizeFromMatrixSize(\n    matrixSize: number, channelsPerTexture: number): number {\n  return matrixSize * channelsPerTexture;\n}\n\nexport function getColorMatrixTextureShapeWidthHeight(\n    rows: number, columns: number): [number, number] {\n  return [columns * 4, rows];\n}\n\n/**\n * Get shape for densely packed RGBA texture.\n */\nexport function getDenseTexShape(shape: number[]): [number, number] {\n  const size = util.sizeFromShape(shape);\n  const texelsNeeded = Math.ceil(size / 4);\n  return util.sizeToSquarishShape(texelsNeeded);\n}\n\nexport function getMatrixSizeFromUnpackedArraySize(\n    unpackedSize: number, channelsPerTexture: number): number {\n  if (unpackedSize % channelsPerTexture !== 0) {\n    throw new Error(\n        `unpackedSize (${unpackedSize}) must be a multiple of ` +\n        `${channelsPerTexture}`);\n  }\n  return unpackedSize / channelsPerTexture;\n}\n\nexport function decodeMatrixFromUnpackedColorRGBAArray(\n    unpackedArray: Float32Array, matrix: Float32Array, channels: number) {\n  const requiredSize = unpackedArray.length * channels / 4;\n  if (matrix.length < requiredSize) {\n    throw new Error(\n        `matrix length (${matrix.length}) must be >= ${requiredSize}`);\n  }\n  let dst = 0;\n  for (let src = 0; src < unpackedArray.length; src += 4) {\n    for (let c = 0; c < channels; c++) {\n      matrix[dst++] = unpackedArray[src + c];\n    }\n  }\n}\n\nexport function getPackedMatrixTextureShapeWidthHeight(\n    rows: number, columns: number): [number, number] {\n  return [\n    Math.max(1, Math.ceil(columns / 2)), Math.max(1, Math.ceil(rows / 2))\n  ];\n}\n\nexport function getPackedRGBAArraySizeFromMatrixShape(\n    rows: number, columns: number): number {\n  const [w, h] = getPackedMatrixTextureShapeWidthHeight(rows, columns);\n  return w * h * 4;\n}\n\nexport interface TextureConfig {\n  internalFormatFloat: number;\n  textureFormatFloat: number;\n  internalFormatPackedHalfFloat: number;\n  internalFormatHalfFloat: number;\n  internalFormatPackedFloat: number;\n\n  // The format to use during a gl.readPixels call.\n  downloadTextureFormat: number;\n  // How many channels need to be unpacked after a gl.readPixels call.\n  downloadUnpackNumChannels: number;\n\n  defaultNumChannels: number;\n  textureTypeHalfFloat: number;\n  textureTypeFloat: number;\n}\n\nexport function getTextureConfig(\n    // tslint:disable-next-line:no-any\n    gl: WebGLRenderingContext, textureHalfFloatExtension?: any): TextureConfig {\n  // tslint:disable-next-line:no-any\n  const glany = gl as any;\n\n  let internalFormatFloat: number;\n  let internalFormatHalfFloat: number;\n  let internalFormatPackedHalfFloat: number;\n  let internalFormatPackedFloat: number;\n  let textureFormatFloat: number;\n\n  let downloadTextureFormat: number;\n  let downloadUnpackNumChannels: number;\n\n  let defaultNumChannels: number;\n  let textureTypeHalfFloat: number;\n  let textureTypeFloat: number;\n\n  if (env().getNumber('WEBGL_VERSION') === 2) {\n    internalFormatFloat = glany.R32F;\n    internalFormatHalfFloat = glany.R16F;\n    internalFormatPackedHalfFloat = glany.RGBA16F;\n    internalFormatPackedFloat = glany.RGBA32F;\n    textureFormatFloat = glany.RED;\n    downloadUnpackNumChannels = 4;\n    defaultNumChannels = 1;\n    textureTypeHalfFloat = glany.HALF_FLOAT;\n    textureTypeFloat = glany.FLOAT;\n    downloadTextureFormat = glany.RGBA8;\n  } else {\n    internalFormatFloat = gl.RGBA;\n    internalFormatHalfFloat = gl.RGBA;\n    internalFormatPackedHalfFloat = gl.RGBA;\n    internalFormatPackedFloat = glany.RGBA;\n    textureFormatFloat = gl.RGBA;\n    downloadUnpackNumChannels = 4;\n    defaultNumChannels = 4;\n    textureTypeHalfFloat = textureHalfFloatExtension != null ?\n        textureHalfFloatExtension.HALF_FLOAT_OES :\n        null;\n    textureTypeFloat = gl.FLOAT;\n    downloadTextureFormat = gl.RGBA;\n  }\n\n  return {\n    internalFormatFloat,\n    internalFormatHalfFloat,\n    internalFormatPackedHalfFloat,\n    internalFormatPackedFloat,\n    textureFormatFloat,\n    downloadTextureFormat,\n    downloadUnpackNumChannels,\n    defaultNumChannels,\n    textureTypeHalfFloat,\n    textureTypeFloat\n  };\n}\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAiBA,SAAwCA,GAAG,EAAcC,IAAI,QAAO,uBAAuB;AAE3F,WAAYC,aAmCX;AAnCD,WAAYA,aAAa;EACvB;;;;;;;;;;;;;;;EAeAA,aAAA,CAAAA,aAAA,wBAAK;EAEL;;;;;;;;;;;;;;;;EAgBAA,aAAA,CAAAA,aAAA,sCAAY;AACd,CAAC,EAnCWA,aAAa,KAAbA,aAAa;AAqCzB,WAAYC,YAKX;AALD,WAAYA,YAAY;EACtBA,YAAA,CAAAA,YAAA,0BAAM;EACNA,YAAA,CAAAA,YAAA,0BAAM;EACNA,YAAA,CAAAA,YAAA,0BAAM;EACNA,YAAA,CAAAA,YAAA,8BAAQ;AACV,CAAC,EALWA,YAAY,KAAZA,YAAY;AAOxB,WAAYC,mBAMX;AAND,WAAYA,mBAAmB;EAC7BA,mBAAA,CAAAA,mBAAA,8CAAgB;EAChBA,mBAAA,CAAAA,mBAAA,8CAAgB;EAChBA,mBAAA,CAAAA,mBAAA,8DAAwB;EACxBA,mBAAA,CAAAA,mBAAA,kDAAkB;EAClBA,mBAAA,CAAAA,mBAAA,kDAAkB;AACpB,CAAC,EANWA,mBAAmB,KAAnBA,mBAAmB;AAwC/B,OAAM,SAAUC,wCAAwCA,CACpDC,IAAY,EAAEC,OAAe;EAC/B,OAAO,CAACA,OAAO,EAAED,IAAI,CAAC;AACxB;AAEA,OAAM,SAAUE,kCAAkCA,CAC9CC,UAAkB,EAAEC,kBAA0B;EAChD,OAAOD,UAAU,GAAGC,kBAAkB;AACxC;AAEA,OAAM,SAAUC,qCAAqCA,CACjDL,IAAY,EAAEC,OAAe;EAC/B,OAAO,CAACA,OAAO,GAAG,CAAC,EAAED,IAAI,CAAC;AAC5B;AAEA;;;AAGA,OAAM,SAAUM,gBAAgBA,CAACC,KAAe;EAC9C,MAAMC,IAAI,GAAGb,IAAI,CAACc,aAAa,CAACF,KAAK,CAAC;EACtC,MAAMG,YAAY,GAAGC,IAAI,CAACC,IAAI,CAACJ,IAAI,GAAG,CAAC,CAAC;EACxC,OAAOb,IAAI,CAACkB,mBAAmB,CAACH,YAAY,CAAC;AAC/C;AAEA,OAAM,SAAUI,kCAAkCA,CAC9CC,YAAoB,EAAEX,kBAA0B;EAClD,IAAIW,YAAY,GAAGX,kBAAkB,KAAK,CAAC,EAAE;IAC3C,MAAM,IAAIY,KAAK,CACX,iBAAiBD,YAAY,0BAA0B,GACvD,GAAGX,kBAAkB,EAAE,CAAC;;EAE9B,OAAOW,YAAY,GAAGX,kBAAkB;AAC1C;AAEA,OAAM,SAAUa,sCAAsCA,CAClDC,aAA2B,EAAEC,MAAoB,EAAEC,QAAgB;EACrE,MAAMC,YAAY,GAAGH,aAAa,CAACI,MAAM,GAAGF,QAAQ,GAAG,CAAC;EACxD,IAAID,MAAM,CAACG,MAAM,GAAGD,YAAY,EAAE;IAChC,MAAM,IAAIL,KAAK,CACX,kBAAkBG,MAAM,CAACG,MAAM,gBAAgBD,YAAY,EAAE,CAAC;;EAEpE,IAAIE,GAAG,GAAG,CAAC;EACX,KAAK,IAAIC,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAGN,aAAa,CAACI,MAAM,EAAEE,GAAG,IAAI,CAAC,EAAE;IACtD,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGL,QAAQ,EAAEK,CAAC,EAAE,EAAE;MACjCN,MAAM,CAACI,GAAG,EAAE,CAAC,GAAGL,aAAa,CAACM,GAAG,GAAGC,CAAC,CAAC;;;AAG5C;AAEA,OAAM,SAAUC,sCAAsCA,CAClD1B,IAAY,EAAEC,OAAe;EAC/B,OAAO,CACLU,IAAI,CAACgB,GAAG,CAAC,CAAC,EAAEhB,IAAI,CAACC,IAAI,CAACX,OAAO,GAAG,CAAC,CAAC,CAAC,EAAEU,IAAI,CAACgB,GAAG,CAAC,CAAC,EAAEhB,IAAI,CAACC,IAAI,CAACZ,IAAI,GAAG,CAAC,CAAC,CAAC,CACtE;AACH;AAEA,OAAM,SAAU4B,qCAAqCA,CACjD5B,IAAY,EAAEC,OAAe;EAC/B,MAAM,CAAC4B,CAAC,EAAEC,CAAC,CAAC,GAAGJ,sCAAsC,CAAC1B,IAAI,EAAEC,OAAO,CAAC;EACpE,OAAO4B,CAAC,GAAGC,CAAC,GAAG,CAAC;AAClB;AAmBA,OAAM,SAAUC,gBAAgBA;AAC5B;AACAC,EAAyB,EAAEC,yBAA+B;EAC5D;EACA,MAAMC,KAAK,GAAGF,EAAS;EAEvB,IAAIG,mBAA2B;EAC/B,IAAIC,uBAA+B;EACnC,IAAIC,6BAAqC;EACzC,IAAIC,yBAAiC;EACrC,IAAIC,kBAA0B;EAE9B,IAAIC,qBAA6B;EACjC,IAAIC,yBAAiC;EAErC,IAAIC,kBAA0B;EAC9B,IAAIC,oBAA4B;EAChC,IAAIC,gBAAwB;EAE5B,IAAIlD,GAAG,EAAE,CAACmD,SAAS,CAAC,eAAe,CAAC,KAAK,CAAC,EAAE;IAC1CV,mBAAmB,GAAGD,KAAK,CAACY,IAAI;IAChCV,uBAAuB,GAAGF,KAAK,CAACa,IAAI;IACpCV,6BAA6B,GAAGH,KAAK,CAACc,OAAO;IAC7CV,yBAAyB,GAAGJ,KAAK,CAACe,OAAO;IACzCV,kBAAkB,GAAGL,KAAK,CAACgB,GAAG;IAC9BT,yBAAyB,GAAG,CAAC;IAC7BC,kBAAkB,GAAG,CAAC;IACtBC,oBAAoB,GAAGT,KAAK,CAACiB,UAAU;IACvCP,gBAAgB,GAAGV,KAAK,CAACkB,KAAK;IAC9BZ,qBAAqB,GAAGN,KAAK,CAACmB,KAAK;GACpC,MAAM;IACLlB,mBAAmB,GAAGH,EAAE,CAACsB,IAAI;IAC7BlB,uBAAuB,GAAGJ,EAAE,CAACsB,IAAI;IACjCjB,6BAA6B,GAAGL,EAAE,CAACsB,IAAI;IACvChB,yBAAyB,GAAGJ,KAAK,CAACoB,IAAI;IACtCf,kBAAkB,GAAGP,EAAE,CAACsB,IAAI;IAC5Bb,yBAAyB,GAAG,CAAC;IAC7BC,kBAAkB,GAAG,CAAC;IACtBC,oBAAoB,GAAGV,yBAAyB,IAAI,IAAI,GACpDA,yBAAyB,CAACsB,cAAc,GACxC,IAAI;IACRX,gBAAgB,GAAGZ,EAAE,CAACoB,KAAK;IAC3BZ,qBAAqB,GAAGR,EAAE,CAACsB,IAAI;;EAGjC,OAAO;IACLnB,mBAAmB;IACnBC,uBAAuB;IACvBC,6BAA6B;IAC7BC,yBAAyB;IACzBC,kBAAkB;IAClBC,qBAAqB;IACrBC,yBAAyB;IACzBC,kBAAkB;IAClBC,oBAAoB;IACpBC;GACD;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}