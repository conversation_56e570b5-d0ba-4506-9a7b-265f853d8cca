{"ast": null, "code": "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { Less } from '@tensorflow/tfjs-core';\nimport { createSimpleBinaryKernelImpl } from '../utils/binary_impl';\nimport { binaryKernelFunc } from '../utils/binary_utils';\nexport const lessImpl = createSimpleBinaryKernelImpl((a, b) => a < b ? 1 : 0);\nexport const less = binaryKernelFunc(Less, lessImpl, null /* complexImpl */, 'bool');\nexport const lessConfig = {\n  kernelName: Less,\n  backendName: 'cpu',\n  kernelFunc: less\n};", "map": {"version": 3, "names": ["Less", "createSimpleBinaryKernelImpl", "binaryKernelFunc", "lessImpl", "a", "b", "less", "lessConfig", "kernelName", "backendName", "kernelFunc"], "sources": ["C:\\tfjs-backend-cpu\\src\\kernels\\Less.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {KernelConfig, Less} from '@tensorflow/tfjs-core';\n\nimport {createSimpleBinaryKernelImpl} from '../utils/binary_impl';\nimport {binaryKernelFunc} from '../utils/binary_utils';\n\nexport const lessImpl =\n    createSimpleBinaryKernelImpl((a: number, b: number) => (a < b) ? 1 : 0);\nexport const less =\n    binaryKernelFunc(Less, lessImpl, null /* complexImpl */, 'bool');\n\nexport const lessConfig: KernelConfig = {\n  kernelName: Less,\n  backendName: 'cpu',\n  kernelFunc: less\n};\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAiBA,SAAsBA,IAAI,QAAO,uBAAuB;AAExD,SAAQC,4BAA4B,QAAO,sBAAsB;AACjE,SAAQC,gBAAgB,QAAO,uBAAuB;AAEtD,OAAO,MAAMC,QAAQ,GACjBF,4BAA4B,CAAC,CAACG,CAAS,EAAEC,CAAS,KAAMD,CAAC,GAAGC,CAAC,GAAI,CAAC,GAAG,CAAC,CAAC;AAC3E,OAAO,MAAMC,IAAI,GACbJ,gBAAgB,CAACF,IAAI,EAAEG,QAAQ,EAAE,IAAI,CAAC,mBAAmB,MAAM,CAAC;AAEpE,OAAO,MAAMI,UAAU,GAAiB;EACtCC,UAAU,EAAER,IAAI;EAChBS,WAAW,EAAE,KAAK;EAClBC,UAAU,EAAEJ;CACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}