{"ast": null, "code": "var _jsxFileName = \"C:\\\\tmsft\\\\src\\\\components\\\\Transactions.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useCallback, useMemo } from 'react';\nimport { transactionStorageService } from '../services/transactionStorageService';\nimport { bankAccountService } from '../services/bankAccountService';\nimport { categoryService } from '../services/categoryService';\nimport './Transactions.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ITEMS_PER_PAGE_OPTIONS = [10, 25, 50, 100];\nconst DEFAULT_ITEMS_PER_PAGE = 50;\nexport const Transactions = ({\n  onTransactionUpdate,\n  refreshTrigger\n}) => {\n  _s();\n  // State management\n  const [transactions, setTransactions] = useState([]);\n  const [bankAccounts, setBankAccounts] = useState([]);\n  const [duplicateGroups, setDuplicateGroups] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [refreshing, setRefreshing] = useState(false);\n\n  // Pagination state\n  const [currentPage, setCurrentPage] = useState(1);\n  const [itemsPerPage, setItemsPerPage] = useState(DEFAULT_ITEMS_PER_PAGE);\n\n  // Sorting state\n  const [sortField, setSortField] = useState('postDateTime');\n  const [sortDirection, setSortDirection] = useState('desc');\n\n  // Filtering state\n  const [filters, setFilters] = useState({\n    accountId: '',\n    dateFrom: '',\n    dateTo: '',\n    amountFrom: '',\n    amountTo: '',\n    description: '',\n    type: 'all'\n  });\n\n  // Selection state\n  const [selectedTransactions, setSelectedTransactions] = useState(new Set());\n  const [showDuplicatesOnly, setShowDuplicatesOnly] = useState(false);\n\n  // Category state\n\n  // Calculate string similarity\n  /* Temporarily commented out for debugging pagination\n  const calculateStringSimilarity = useCallback((str1: string, str2: string): number => {\n    const longer = str1.length > str2.length ? str1 : str2;\n    const shorter = str1.length > str2.length ? str2 : str1;\n    \n    if (longer.length === 0) return 1.0;\n    \n    // Simple similarity based on common characters\n    const s1 = str1.toLowerCase();\n    const s2 = str2.toLowerCase();\n    \n    let matches = 0;\n    for (let i = 0; i < shorter.length; i++) {\n      if (s1.includes(s2[i])) matches++;\n    }\n    \n    return matches / longer.length;\n  }, []);\n  */\n\n  // Check if two transactions are potential duplicates\n  /* Temporarily commented out for debugging pagination\n  const arePotentialDuplicates = useCallback((t1: StoredTransaction, t2: StoredTransaction): boolean => {\n    // Same account\n    if (t1.accountId !== t2.accountId) return false;\n     // Same date (within 1 day)\n    const date1 = new Date(t1.postDateTime);\n    const date2 = new Date(t2.postDateTime);\n    const daysDiff = Math.abs(date1.getTime() - date2.getTime()) / (1000 * 60 * 60 * 24);\n    if (daysDiff > 1) return false;\n     // Same amounts\n    const sameDebit = Math.abs((t1.debitAmount || 0) - (t2.debitAmount || 0)) < 0.01;\n    const sameCredit = Math.abs((t1.creditAmount || 0) - (t2.creditAmount || 0)) < 0.01;\n    if (!sameDebit || !sameCredit) return false;\n     // Similar description (at least 80% similarity)\n    const similarity = calculateStringSimilarity(t1.description, t2.description);\n    if (similarity < 0.8) return false;\n     return true;\n  }, [calculateStringSimilarity]);\n  */\n\n  // Simple duplicate detection within a single set of transactions\n  const findDuplicatesInTransactions = useCallback(_transactions => {\n    // Temporarily disable complex duplicate detection to debug pagination\n    return [];\n\n    /* Original code commented out for debugging\n    const duplicateGroups: StoredTransaction[][] = [];\n    const processed = new Set<string>();\n     for (let i = 0; i < transactions.length; i++) {\n      if (processed.has(transactions[i].id)) continue;\n       const group: StoredTransaction[] = [transactions[i]];\n      processed.add(transactions[i].id);\n       for (let j = i + 1; j < transactions.length; j++) {\n        if (processed.has(transactions[j].id)) continue;\n         // Check if transactions are potential duplicates\n        if (arePotentialDuplicates(transactions[i], transactions[j])) {\n          group.push(transactions[j]);\n          processed.add(transactions[j].id);\n        }\n      }\n       // Only consider groups with 2 or more transactions as duplicates\n      if (group.length > 1) {\n        duplicateGroups.push(group);\n      }\n    }\n     return duplicateGroups;\n    */\n  }, []); // Empty dependency array since function currently doesn't use any dependencies\n\n  // Load data on component mount\n  const loadData = useCallback(async () => {\n    try {\n      // Show refreshing indicator if not initial load\n      if (transactions.length > 0) {\n        setRefreshing(true);\n      } else {\n        setLoading(true);\n      }\n      setError(null);\n\n      // Load bank accounts\n      const accounts = bankAccountService.getAllAccounts();\n      setBankAccounts(accounts);\n\n      // Load categories (for future use)\n      categoryService.getAllCategories();\n\n      // Load all transactions\n      const allTransactions = [];\n      accounts.forEach(account => {\n        const accountTransactions = transactionStorageService.getTransactionsByAccount(account.id);\n        allTransactions.push(...accountTransactions);\n      });\n      setTransactions(allTransactions);\n\n      // Detect duplicates within the transaction set\n      const duplicates = findDuplicatesInTransactions(allTransactions);\n      setDuplicateGroups(duplicates);\n      if (onTransactionUpdate) {\n        onTransactionUpdate(allTransactions);\n      }\n    } catch (err) {\n      setError(err instanceof Error ? err.message : 'Failed to load transactions');\n    } finally {\n      setLoading(false);\n      setRefreshing(false);\n    }\n  }, [onTransactionUpdate, findDuplicatesInTransactions]);\n  useEffect(() => {\n    loadData();\n  }, [loadData, refreshTrigger]);\n\n  // Filtered and sorted transactions\n  const filteredAndSortedTransactions = useMemo(() => {\n    // Early return if transactions not loaded yet\n    if (!transactions || transactions.length === 0) {\n      return [];\n    }\n    console.log('FILTERING DEBUG: Starting with', transactions.length, 'transactions');\n    let filtered = [...transactions];\n\n    // Apply filters\n    if (filters.accountId) {\n      filtered = filtered.filter(t => t.accountId === filters.accountId);\n      console.log('FILTERING DEBUG: After accountId filter:', filtered.length);\n    }\n    if (filters.dateFrom) {\n      const fromDate = new Date(filters.dateFrom);\n      filtered = filtered.filter(t => new Date(t.postDateTime) >= fromDate);\n      console.log('FILTERING DEBUG: After dateFrom filter:', filtered.length);\n    }\n    if (filters.dateTo) {\n      const toDate = new Date(filters.dateTo);\n      filtered = filtered.filter(t => new Date(t.postDateTime) <= toDate);\n      console.log('FILTERING DEBUG: After dateTo filter:', filtered.length);\n    }\n    if (filters.description) {\n      const searchTerm = filters.description.toLowerCase();\n      filtered = filtered.filter(t => t.description.toLowerCase().includes(searchTerm) || t.reference && t.reference.toLowerCase().includes(searchTerm));\n      console.log('FILTERING DEBUG: After description filter:', filtered.length);\n    }\n    if (filters.amountFrom) {\n      const minAmount = parseFloat(filters.amountFrom);\n      if (!isNaN(minAmount)) {\n        filtered = filtered.filter(t => {\n          const amount = Math.abs((t.debitAmount || 0) + (t.creditAmount || 0));\n          return amount >= minAmount;\n        });\n        console.log('FILTERING DEBUG: After amountFrom filter:', filtered.length);\n      }\n    }\n    if (filters.amountTo) {\n      const maxAmount = parseFloat(filters.amountTo);\n      if (!isNaN(maxAmount)) {\n        filtered = filtered.filter(t => {\n          const amount = Math.abs((t.debitAmount || 0) + (t.creditAmount || 0));\n          return amount <= maxAmount;\n        });\n        console.log('FILTERING DEBUG: After amountTo filter:', filtered.length);\n      }\n    }\n    if (filters.type === 'debits') {\n      filtered = filtered.filter(t => (t.debitAmount || 0) > 0);\n      console.log('FILTERING DEBUG: After debits filter:', filtered.length);\n    } else if (filters.type === 'credits') {\n      filtered = filtered.filter(t => (t.creditAmount || 0) > 0);\n      console.log('FILTERING DEBUG: After credits filter:', filtered.length);\n    }\n\n    // Show duplicates only\n    if (showDuplicatesOnly && duplicateGroups.length > 0) {\n      const duplicateIds = new Set(duplicateGroups.flat().map(t => t.id));\n      filtered = filtered.filter(t => duplicateIds.has(t.id));\n      console.log('FILTERING DEBUG: After duplicates filter:', filtered.length);\n    }\n\n    // Sort transactions\n    if (bankAccounts.length > 0) {\n      filtered.sort((a, b) => {\n        let aValue;\n        let bValue;\n        switch (sortField) {\n          case 'postDateTime':\n            aValue = new Date(a.postDateTime).getTime();\n            bValue = new Date(b.postDateTime).getTime();\n            break;\n          case 'description':\n            aValue = a.description.toLowerCase();\n            bValue = b.description.toLowerCase();\n            break;\n          case 'amount':\n            aValue = Math.abs((a.debitAmount || 0) + (a.creditAmount || 0));\n            bValue = Math.abs((b.debitAmount || 0) + (b.creditAmount || 0));\n            break;\n          case 'balance':\n            aValue = a.balance;\n            bValue = b.balance;\n            break;\n          case 'accountName':\n            const accountA = bankAccounts.find(acc => acc.id === a.accountId);\n            const accountB = bankAccounts.find(acc => acc.id === b.accountId);\n            aValue = (accountA === null || accountA === void 0 ? void 0 : accountA.name.toLowerCase()) || '';\n            bValue = (accountB === null || accountB === void 0 ? void 0 : accountB.name.toLowerCase()) || '';\n            break;\n          default:\n            return 0;\n        }\n        if (sortDirection === 'asc') {\n          return aValue < bValue ? -1 : aValue > bValue ? 1 : 0;\n        } else {\n          return aValue > bValue ? -1 : aValue < bValue ? 1 : 0;\n        }\n      });\n    }\n    console.log('FILTERING DEBUG: Final filtered result:', filtered.length);\n    return filtered;\n  }, [transactions, filters, sortField, sortDirection, showDuplicatesOnly, duplicateGroups, bankAccounts]);\n\n  // Pagination calculations\n  const totalTransactions = filteredAndSortedTransactions.length;\n  const totalPages = Math.ceil(totalTransactions / itemsPerPage);\n  const startIndex = (currentPage - 1) * itemsPerPage;\n  const endIndex = Math.min(startIndex + itemsPerPage, totalTransactions);\n  const currentTransactions = filteredAndSortedTransactions.slice(startIndex, endIndex);\n\n  // Debug pagination issue\n  console.log('PAGINATION DEBUG:', {\n    currentPage,\n    itemsPerPage,\n    totalTransactions,\n    totalPages,\n    startIndex,\n    endIndex,\n    sliceLength: currentTransactions.length,\n    expectedLength: Math.min(itemsPerPage, totalTransactions - startIndex),\n    filteredAndSortedLength: filteredAndSortedTransactions.length,\n    transactionsLength: transactions.length,\n    actualSlice: currentTransactions.slice(0, 3).map(t => ({\n      id: t.id,\n      desc: t.description.substring(0, 20)\n    }))\n  });\n\n  // Check DOM rendering\n  React.useEffect(() => {\n    const tableRows = document.querySelectorAll('.transactions-table tbody tr');\n    console.log('DOM ROWS COUNT:', tableRows.length, 'Expected:', currentTransactions.length);\n  }, [currentTransactions]);\n\n  // Reset to first page when filters change\n  useEffect(() => {\n    setCurrentPage(1);\n  }, [filters, sortField, sortDirection, showDuplicatesOnly, itemsPerPage]);\n\n  // Event handlers\n  const handleSort = field => {\n    if (sortField === field) {\n      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');\n    } else {\n      setSortField(field);\n      setSortDirection('desc');\n    }\n  };\n  const handleFilterChange = (key, value) => {\n    setFilters(prev => ({\n      ...prev,\n      [key]: value\n    }));\n  };\n  const handleSelectTransaction = transactionId => {\n    setSelectedTransactions(prev => {\n      const newSet = new Set(prev);\n      if (newSet.has(transactionId)) {\n        newSet.delete(transactionId);\n      } else {\n        newSet.add(transactionId);\n      }\n      return newSet;\n    });\n  };\n  const handleSelectAll = useCallback(() => {\n    if (selectedTransactions.size === currentTransactions.length) {\n      setSelectedTransactions(new Set());\n    } else {\n      setSelectedTransactions(new Set(currentTransactions.map(t => t.id)));\n    }\n  }, [selectedTransactions.size, currentTransactions]);\n  const clearFilters = () => {\n    setFilters({\n      accountId: '',\n      dateFrom: '',\n      dateTo: '',\n      amountFrom: '',\n      amountTo: '',\n      description: '',\n      type: 'all'\n    });\n    setShowDuplicatesOnly(false);\n  };\n\n  // Keyboard navigation\n  useEffect(() => {\n    const handleKeyPress = e => {\n      if (e.target instanceof HTMLInputElement || e.target instanceof HTMLTextAreaElement) {\n        return; // Don't interfere with input fields\n      }\n      switch (e.key) {\n        case 'ArrowLeft':\n          if (currentPage > 1) {\n            setCurrentPage(currentPage - 1);\n          }\n          break;\n        case 'ArrowRight':\n          if (currentPage < totalPages) {\n            setCurrentPage(currentPage + 1);\n          }\n          break;\n        case 'Home':\n          setCurrentPage(1);\n          break;\n        case 'End':\n          setCurrentPage(totalPages);\n          break;\n        case 'f':\n          if (e.ctrlKey || e.metaKey) {\n            var _document$getElementB;\n            e.preventDefault();\n            (_document$getElementB = document.getElementById('search-input')) === null || _document$getElementB === void 0 ? void 0 : _document$getElementB.focus();\n          }\n          break;\n        case 'r':\n          if (e.ctrlKey || e.metaKey) {\n            e.preventDefault();\n            loadData();\n          }\n          break;\n        case 'a':\n          if (e.ctrlKey || e.metaKey) {\n            e.preventDefault();\n            handleSelectAll();\n          }\n          break;\n        case 'Escape':\n          setSelectedTransactions(new Set());\n          break;\n      }\n    };\n    document.addEventListener('keydown', handleKeyPress);\n    return () => document.removeEventListener('keydown', handleKeyPress);\n  }, [currentPage, totalPages, loadData, handleSelectAll]);\n\n  // Utility functions\n  const formatCurrency = amount => {\n    return new Intl.NumberFormat('en-US', {\n      minimumFractionDigits: 2,\n      maximumFractionDigits: 2\n    }).format(amount);\n  };\n  const formatDate = dateString => {\n    const date = new Date(dateString);\n    return date.toLocaleDateString('en-GB', {\n      day: '2-digit',\n      month: '2-digit',\n      year: 'numeric'\n    });\n  };\n  const getAccountName = accountId => {\n    const account = bankAccounts.find(acc => acc.id === accountId);\n    return (account === null || account === void 0 ? void 0 : account.name) || 'Unknown Account';\n  };\n  const isDuplicate = transaction => {\n    return duplicateGroups.some(group => group.some(t => t.id === transaction.id));\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"transactions-loading\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"loading-spinner\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 477,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"Loading transactions...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 478,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 476,\n      columnNumber: 7\n    }, this);\n  }\n  if (error) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"transactions-error\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"error-icon\",\n        children: \"\\u26A0\\uFE0F\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 486,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n        children: \"Error Loading Transactions\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 487,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: error\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 488,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: loadData,\n        className: \"btn btn-primary\",\n        children: \"Try Again\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 489,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 485,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"transactions\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"transactions-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"transactions-title-section\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"transactions-title\",\n          children: \"Transactions\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 500,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"transactions-stats\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"stat-item\",\n            children: [\"Total: \", /*#__PURE__*/_jsxDEV(\"strong\", {\n              children: transactions.length.toLocaleString()\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 503,\n              columnNumber: 22\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 502,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"stat-item\",\n            children: [\"Filtered: \", /*#__PURE__*/_jsxDEV(\"strong\", {\n              children: filteredAndSortedTransactions.length.toLocaleString()\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 506,\n              columnNumber: 25\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 505,\n            columnNumber: 13\n          }, this), duplicateGroups.length > 0 && /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"stat-item duplicate-stat\",\n            children: [\"Duplicates: \", /*#__PURE__*/_jsxDEV(\"strong\", {\n              children: duplicateGroups.flat().length\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 510,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 509,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 501,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 499,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"transactions-actions\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: loadData,\n          className: \"btn btn-secondary btn-sm\",\n          disabled: refreshing,\n          children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n            width: \"16\",\n            height: \"16\",\n            viewBox: \"0 0 24 24\",\n            fill: \"none\",\n            stroke: \"currentColor\",\n            strokeWidth: \"2\",\n            children: [/*#__PURE__*/_jsxDEV(\"polyline\", {\n              points: \"23 4 23 10 17 10\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 518,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"polyline\", {\n              points: \"1 20 1 14 7 14\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 519,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n              d: \"M20.49 9A9 9 0 0 0 5.64 5.64L1 10m22 4l-4.64 4.36A9 9 0 0 1 3.51 15\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 520,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 517,\n            columnNumber: 13\n          }, this), refreshing ? 'Refreshing...' : 'Refresh']\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 516,\n          columnNumber: 11\n        }, this), refreshing && /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"refresh-indicator\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"refresh-spinner\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 526,\n            columnNumber: 15\n          }, this), \"Data updated - refreshing...\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 525,\n          columnNumber: 13\n        }, this), selectedTransactions.size > 0 && /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"selection-count\",\n          children: [selectedTransactions.size, \" selected\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 531,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 515,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 498,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"transactions-filters\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"filters-row\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"filter-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"filter-label\",\n            children: \"Account\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 542,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n            value: filters.accountId,\n            onChange: e => handleFilterChange('accountId', e.target.value),\n            className: \"filter-select\",\n            children: [/*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"\",\n              children: \"All Accounts\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 548,\n              columnNumber: 15\n            }, this), bankAccounts.map(account => /*#__PURE__*/_jsxDEV(\"option\", {\n              value: account.id,\n              children: [account.name, \" - \", account.accountNumber]\n            }, account.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 550,\n              columnNumber: 17\n            }, this))]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 543,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 541,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"filter-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"filter-label\",\n            children: \"Type\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 558,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n            value: filters.type,\n            onChange: e => handleFilterChange('type', e.target.value),\n            className: \"filter-select\",\n            children: [/*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"all\",\n              children: \"All Types\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 564,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"debits\",\n              children: \"Debits Only\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 565,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"credits\",\n              children: \"Credits Only\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 566,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 559,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 557,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"filter-group\",\n          children: /*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"filter-label\",\n            children: [/*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"checkbox\",\n              checked: showDuplicatesOnly,\n              onChange: e => setShowDuplicatesOnly(e.target.checked),\n              className: \"filter-checkbox\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 572,\n              columnNumber: 15\n            }, this), \"Show Duplicates Only\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 571,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 570,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 540,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"filters-row\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"filter-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"filter-label\",\n            children: \"Search\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 585,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            id: \"search-input\",\n            type: \"text\",\n            value: filters.description,\n            onChange: e => handleFilterChange('description', e.target.value),\n            placeholder: \"Search description or reference...\",\n            className: \"filter-input\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 586,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 584,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"filter-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"filter-label\",\n            children: \"Date From\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 597,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"date\",\n            value: filters.dateFrom,\n            onChange: e => handleFilterChange('dateFrom', e.target.value),\n            className: \"filter-input\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 598,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 596,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"filter-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"filter-label\",\n            children: \"Date To\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 607,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"date\",\n            value: filters.dateTo,\n            onChange: e => handleFilterChange('dateTo', e.target.value),\n            className: \"filter-input\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 608,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 606,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"filter-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"filter-label\",\n            children: \"Amount Range\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 617,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"amount-range\",\n            children: [/*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"number\",\n              value: filters.amountFrom,\n              onChange: e => handleFilterChange('amountFrom', e.target.value),\n              placeholder: \"Min\",\n              className: \"filter-input amount-input\",\n              step: \"0.01\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 619,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"amount-separator\",\n              children: \"-\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 627,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"number\",\n              value: filters.amountTo,\n              onChange: e => handleFilterChange('amountTo', e.target.value),\n              placeholder: \"Max\",\n              className: \"filter-input amount-input\",\n              step: \"0.01\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 628,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 618,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 616,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"filter-group\",\n          children: /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: clearFilters,\n            className: \"btn btn-secondary btn-sm\",\n            children: \"Clear Filters\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 640,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 639,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 583,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 539,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"transactions-table-container\",\n      children: [/*#__PURE__*/_jsxDEV(\"table\", {\n        className: \"transactions-table\",\n        children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n          children: /*#__PURE__*/_jsxDEV(\"tr\", {\n            children: [/*#__PURE__*/_jsxDEV(\"th\", {\n              className: \"checkbox-col\",\n              children: /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"checkbox\",\n                checked: selectedTransactions.size === currentTransactions.length && currentTransactions.length > 0,\n                onChange: handleSelectAll,\n                className: \"table-checkbox\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 653,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 652,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              className: `sortable ${sortField === 'postDateTime' ? 'sorted' : ''}`,\n              onClick: () => handleSort('postDateTime'),\n              children: [\"Date\", sortField === 'postDateTime' && /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"sort-arrow\",\n                children: sortDirection === 'asc' ? '↑' : '↓'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 666,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 660,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              className: `sortable ${sortField === 'accountName' ? 'sorted' : ''}`,\n              onClick: () => handleSort('accountName'),\n              children: [\"Account\", sortField === 'accountName' && /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"sort-arrow\",\n                children: sortDirection === 'asc' ? '↑' : '↓'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 677,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 671,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              className: `sortable ${sortField === 'description' ? 'sorted' : ''}`,\n              onClick: () => handleSort('description'),\n              children: [\"Description\", sortField === 'description' && /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"sort-arrow\",\n                children: sortDirection === 'asc' ? '↑' : '↓'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 688,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 682,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              className: \"amount-col\",\n              children: \"Debit\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 693,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              className: \"amount-col\",\n              children: \"Credit\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 694,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              className: `amount-col sortable ${sortField === 'balance' ? 'sorted' : ''}`,\n              onClick: () => handleSort('balance'),\n              children: [\"Balance\", sortField === 'balance' && /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"sort-arrow\",\n                children: sortDirection === 'asc' ? '↑' : '↓'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 701,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 695,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              children: \"Reference\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 706,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 651,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 650,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n          children: currentTransactions.map((transaction, _index) => /*#__PURE__*/_jsxDEV(\"tr\", {\n            className: `\n                  ${selectedTransactions.has(transaction.id) ? 'selected' : ''}\n                  ${isDuplicate(transaction) ? 'duplicate' : ''}\n                `,\n            title: transaction.description,\n            children: [/*#__PURE__*/_jsxDEV(\"td\", {\n              className: \"checkbox-col\",\n              children: /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"checkbox\",\n                checked: selectedTransactions.has(transaction.id),\n                onChange: () => handleSelectTransaction(transaction.id),\n                className: \"table-checkbox\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 720,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 719,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n              className: \"date-col\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"date-display\",\n                children: [formatDate(transaction.postDateTime), transaction.time && /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"time-display\",\n                  children: transaction.time\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 731,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 728,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 727,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n              className: \"account-col\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"account-info\",\n                children: [getAccountName(transaction.accountId), isDuplicate(transaction) && /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"duplicate-badge\",\n                  children: \"DUPLICATE\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 739,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 736,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 735,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n              className: \"description-col\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"description-content\",\n                children: transaction.description\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 744,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 743,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n              className: \"amount-col debit\",\n              children: transaction.debitAmount ? formatCurrency(transaction.debitAmount) : ''\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 748,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n              className: \"amount-col credit\",\n              children: transaction.creditAmount ? formatCurrency(transaction.creditAmount) : ''\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 751,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n              className: \"amount-col balance\",\n              children: formatCurrency(transaction.balance)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 754,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n              className: \"reference-col\",\n              children: transaction.reference || ''\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 757,\n              columnNumber: 17\n            }, this)]\n          }, transaction.id, true, {\n            fileName: _jsxFileName,\n            lineNumber: 711,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 709,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 649,\n        columnNumber: 9\n      }, this), currentTransactions.length === 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"no-transactions\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"no-transactions-icon\",\n          children: \"\\uD83D\\uDCCA\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 767,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n          children: \"No Transactions Found\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 768,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: filteredAndSortedTransactions.length === 0 && transactions.length === 0 ? 'No transactions have been imported yet.' : 'No transactions match your current filters.'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 769,\n          columnNumber: 13\n        }, this), filteredAndSortedTransactions.length === 0 && transactions.length > 0 && /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: clearFilters,\n          className: \"btn btn-primary\",\n          children: \"Clear Filters\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 775,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 766,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 648,\n      columnNumber: 7\n    }, this), totalPages > 1 && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"transactions-pagination\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"pagination-info\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          children: [\"Showing \", startIndex + 1, \"-\", Math.min(endIndex, filteredAndSortedTransactions.length), \" of \", filteredAndSortedTransactions.length, \" transactions\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 787,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"items-per-page\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            children: \"Items per page:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 791,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n            value: itemsPerPage,\n            onChange: e => setItemsPerPage(Number(e.target.value)),\n            className: \"page-size-select\",\n            children: ITEMS_PER_PAGE_OPTIONS.map(size => /*#__PURE__*/_jsxDEV(\"option\", {\n              value: size,\n              children: size\n            }, size, false, {\n              fileName: _jsxFileName,\n              lineNumber: 798,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 792,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 790,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 786,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"pagination-controls\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setCurrentPage(1),\n          disabled: currentPage === 1,\n          className: \"btn btn-secondary btn-sm\",\n          children: \"First\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 804,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setCurrentPage(currentPage - 1),\n          disabled: currentPage === 1,\n          className: \"btn btn-secondary btn-sm\",\n          children: \"Previous\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 811,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"pagination-pages\",\n          children: Array.from({\n            length: Math.min(5, totalPages)\n          }, (_, i) => {\n            const pageNumber = Math.max(1, Math.min(totalPages - 4, currentPage - 2)) + i;\n            return /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setCurrentPage(pageNumber),\n              className: `btn btn-secondary btn-sm ${currentPage === pageNumber ? 'active' : ''}`,\n              children: pageNumber\n            }, pageNumber, false, {\n              fileName: _jsxFileName,\n              lineNumber: 822,\n              columnNumber: 19\n            }, this);\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 818,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setCurrentPage(currentPage + 1),\n          disabled: currentPage === totalPages,\n          className: \"btn btn-secondary btn-sm\",\n          children: \"Next\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 832,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setCurrentPage(totalPages),\n          disabled: currentPage === totalPages,\n          className: \"btn btn-secondary btn-sm\",\n          children: \"Last\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 839,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 803,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 785,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"keyboard-shortcuts\",\n      children: /*#__PURE__*/_jsxDEV(\"details\", {\n        children: [/*#__PURE__*/_jsxDEV(\"summary\", {\n          children: \"Keyboard Shortcuts\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 853,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"shortcuts-grid\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"shortcut-item\",\n            children: [/*#__PURE__*/_jsxDEV(\"kbd\", {\n              children: \"\\u2190\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 856,\n              columnNumber: 15\n            }, this), \" \", /*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"Previous page\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 856,\n              columnNumber: 28\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 855,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"shortcut-item\",\n            children: [/*#__PURE__*/_jsxDEV(\"kbd\", {\n              children: \"\\u2192\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 859,\n              columnNumber: 15\n            }, this), \" \", /*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"Next page\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 859,\n              columnNumber: 28\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 858,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"shortcut-item\",\n            children: [/*#__PURE__*/_jsxDEV(\"kbd\", {\n              children: \"Home\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 862,\n              columnNumber: 15\n            }, this), \" \", /*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"First page\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 862,\n              columnNumber: 31\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 861,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"shortcut-item\",\n            children: [/*#__PURE__*/_jsxDEV(\"kbd\", {\n              children: \"End\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 865,\n              columnNumber: 15\n            }, this), \" \", /*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"Last page\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 865,\n              columnNumber: 30\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 864,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"shortcut-item\",\n            children: [/*#__PURE__*/_jsxDEV(\"kbd\", {\n              children: \"Ctrl+F\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 868,\n              columnNumber: 15\n            }, this), \" \", /*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"Focus search\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 868,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 867,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"shortcut-item\",\n            children: [/*#__PURE__*/_jsxDEV(\"kbd\", {\n              children: \"Ctrl+R\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 871,\n              columnNumber: 15\n            }, this), \" \", /*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"Refresh\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 871,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 870,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"shortcut-item\",\n            children: [/*#__PURE__*/_jsxDEV(\"kbd\", {\n              children: \"Ctrl+A\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 874,\n              columnNumber: 15\n            }, this), \" \", /*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"Select all\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 874,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 873,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"shortcut-item\",\n            children: [/*#__PURE__*/_jsxDEV(\"kbd\", {\n              children: \"Esc\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 877,\n              columnNumber: 15\n            }, this), \" \", /*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"Clear selection\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 877,\n              columnNumber: 30\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 876,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 854,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 852,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 851,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 497,\n    columnNumber: 5\n  }, this);\n};\n_s(Transactions, \"KLd/isdhiRxpyQq8sy84mH8ASsE=\");\n_c = Transactions;\nvar _c;\n$RefreshReg$(_c, \"Transactions\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useCallback", "useMemo", "transactionStorageService", "bankAccountService", "categoryService", "jsxDEV", "_jsxDEV", "ITEMS_PER_PAGE_OPTIONS", "DEFAULT_ITEMS_PER_PAGE", "Transactions", "onTransactionUpdate", "refreshTrigger", "_s", "transactions", "setTransactions", "bankAccounts", "setBankAccounts", "duplicateGroups", "setDuplicateGroups", "loading", "setLoading", "error", "setError", "refreshing", "setRefreshing", "currentPage", "setCurrentPage", "itemsPerPage", "setItemsPerPage", "sortField", "setSortField", "sortDirection", "setSortDirection", "filters", "setFilters", "accountId", "dateFrom", "dateTo", "amountFrom", "amountTo", "description", "type", "selectedTransactions", "setSelectedTransactions", "Set", "showDuplicatesOnly", "setShowDuplicatesOnly", "findDuplicatesInTransactions", "_transactions", "loadData", "length", "accounts", "getAllAccounts", "getAllCategories", "allTransactions", "for<PERSON>ach", "account", "accountTransactions", "getTransactionsByAccount", "id", "push", "duplicates", "err", "Error", "message", "filteredAndSortedTransactions", "console", "log", "filtered", "filter", "t", "fromDate", "Date", "postDateTime", "toDate", "searchTerm", "toLowerCase", "includes", "reference", "minAmount", "parseFloat", "isNaN", "amount", "Math", "abs", "debitAmount", "creditAmount", "maxAmount", "duplicateIds", "flat", "map", "has", "sort", "a", "b", "aValue", "bValue", "getTime", "balance", "accountA", "find", "acc", "accountB", "name", "totalTransactions", "totalPages", "ceil", "startIndex", "endIndex", "min", "currentTransactions", "slice", "slice<PERSON><PERSON>th", "<PERSON><PERSON><PERSON><PERSON>", "filteredAndSortedLength", "transactionsLength", "actualSlice", "desc", "substring", "tableRows", "document", "querySelectorAll", "handleSort", "field", "handleFilterChange", "key", "value", "prev", "handleSelectTransaction", "transactionId", "newSet", "delete", "add", "handleSelectAll", "size", "clearFilters", "handleKeyPress", "e", "target", "HTMLInputElement", "HTMLTextAreaElement", "ctrl<PERSON>ey", "metaKey", "_document$getElementB", "preventDefault", "getElementById", "focus", "addEventListener", "removeEventListener", "formatCurrency", "Intl", "NumberFormat", "minimumFractionDigits", "maximumFractionDigits", "format", "formatDate", "dateString", "date", "toLocaleDateString", "day", "month", "year", "getAccountName", "isDuplicate", "transaction", "some", "group", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "toLocaleString", "disabled", "width", "height", "viewBox", "fill", "stroke", "strokeWidth", "points", "d", "onChange", "accountNumber", "checked", "placeholder", "step", "_index", "title", "time", "Number", "Array", "from", "_", "i", "pageNumber", "max", "_c", "$RefreshReg$"], "sources": ["C:/tmsft/src/components/Transactions.tsx"], "sourcesContent": ["import React, { useState, useEffect, useCallback, useMemo } from 'react';\nimport { transactionStorageService, type StoredTransaction } from '../services/transactionStorageService';\nimport { bankAccountService } from '../services/bankAccountService';\nimport { categoryService } from '../services/categoryService';\nimport { BankAccount } from '../types';\nimport './Transactions.css';\n\ninterface TransactionsProps {\n  onTransactionUpdate?: (transactions: StoredTransaction[]) => void;\n  refreshTrigger?: number;\n  showCategoryColumn?: boolean;\n}\n\ntype SortField = 'postDateTime' | 'description' | 'amount' | 'balance' | 'accountName';\ntype SortDirection = 'asc' | 'desc';\ntype FilterType = 'all' | 'debits' | 'credits' | 'duplicates';\n\ninterface TransactionFilters {\n  accountId: string;\n  dateFrom: string;\n  dateTo: string;\n  amountFrom: string;\n  amountTo: string;\n  description: string;\n  type: FilterType;\n}\n\nconst ITEMS_PER_PAGE_OPTIONS = [10, 25, 50, 100];\nconst DEFAULT_ITEMS_PER_PAGE = 50;\n\nexport const Transactions: React.FC<TransactionsProps> = ({ onTransactionUpdate, refreshTrigger }) => {\n  // State management\n  const [transactions, setTransactions] = useState<StoredTransaction[]>([]);\n  const [bankAccounts, setBankAccounts] = useState<BankAccount[]>([]);\n  const [duplicateGroups, setDuplicateGroups] = useState<StoredTransaction[][]>([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n  const [refreshing, setRefreshing] = useState(false);\n  \n  // Pagination state\n  const [currentPage, setCurrentPage] = useState(1);\n  const [itemsPerPage, setItemsPerPage] = useState(DEFAULT_ITEMS_PER_PAGE);\n  \n  // Sorting state\n  const [sortField, setSortField] = useState<SortField>('postDateTime');\n  const [sortDirection, setSortDirection] = useState<SortDirection>('desc');\n  \n  // Filtering state\n  const [filters, setFilters] = useState<TransactionFilters>({\n    accountId: '',\n    dateFrom: '',\n    dateTo: '',\n    amountFrom: '',\n    amountTo: '',\n    description: '',\n    type: 'all'\n  });\n  \n  // Selection state\n  const [selectedTransactions, setSelectedTransactions] = useState<Set<string>>(new Set());\n  const [showDuplicatesOnly, setShowDuplicatesOnly] = useState(false);\n  \n  // Category state\n\n\n  // Calculate string similarity\n  /* Temporarily commented out for debugging pagination\n  const calculateStringSimilarity = useCallback((str1: string, str2: string): number => {\n    const longer = str1.length > str2.length ? str1 : str2;\n    const shorter = str1.length > str2.length ? str2 : str1;\n    \n    if (longer.length === 0) return 1.0;\n    \n    // Simple similarity based on common characters\n    const s1 = str1.toLowerCase();\n    const s2 = str2.toLowerCase();\n    \n    let matches = 0;\n    for (let i = 0; i < shorter.length; i++) {\n      if (s1.includes(s2[i])) matches++;\n    }\n    \n    return matches / longer.length;\n  }, []);\n  */\n\n  // Check if two transactions are potential duplicates\n  /* Temporarily commented out for debugging pagination\n  const arePotentialDuplicates = useCallback((t1: StoredTransaction, t2: StoredTransaction): boolean => {\n    // Same account\n    if (t1.accountId !== t2.accountId) return false;\n\n    // Same date (within 1 day)\n    const date1 = new Date(t1.postDateTime);\n    const date2 = new Date(t2.postDateTime);\n    const daysDiff = Math.abs(date1.getTime() - date2.getTime()) / (1000 * 60 * 60 * 24);\n    if (daysDiff > 1) return false;\n\n    // Same amounts\n    const sameDebit = Math.abs((t1.debitAmount || 0) - (t2.debitAmount || 0)) < 0.01;\n    const sameCredit = Math.abs((t1.creditAmount || 0) - (t2.creditAmount || 0)) < 0.01;\n    if (!sameDebit || !sameCredit) return false;\n\n    // Similar description (at least 80% similarity)\n    const similarity = calculateStringSimilarity(t1.description, t2.description);\n    if (similarity < 0.8) return false;\n\n    return true;\n  }, [calculateStringSimilarity]);\n  */\n\n  // Simple duplicate detection within a single set of transactions\n  const findDuplicatesInTransactions = useCallback((_transactions: StoredTransaction[]): StoredTransaction[][] => {\n    // Temporarily disable complex duplicate detection to debug pagination\n    return [];\n    \n    /* Original code commented out for debugging\n    const duplicateGroups: StoredTransaction[][] = [];\n    const processed = new Set<string>();\n\n    for (let i = 0; i < transactions.length; i++) {\n      if (processed.has(transactions[i].id)) continue;\n\n      const group: StoredTransaction[] = [transactions[i]];\n      processed.add(transactions[i].id);\n\n      for (let j = i + 1; j < transactions.length; j++) {\n        if (processed.has(transactions[j].id)) continue;\n\n        // Check if transactions are potential duplicates\n        if (arePotentialDuplicates(transactions[i], transactions[j])) {\n          group.push(transactions[j]);\n          processed.add(transactions[j].id);\n        }\n      }\n\n      // Only consider groups with 2 or more transactions as duplicates\n      if (group.length > 1) {\n        duplicateGroups.push(group);\n      }\n    }\n\n    return duplicateGroups;\n    */\n  }, []); // Empty dependency array since function currently doesn't use any dependencies\n\n  // Load data on component mount\n  const loadData = useCallback(async () => {\n    try {\n      // Show refreshing indicator if not initial load\n      if (transactions.length > 0) {\n        setRefreshing(true);\n      } else {\n        setLoading(true);\n      }\n      setError(null);\n      \n      // Load bank accounts\n      const accounts = bankAccountService.getAllAccounts();\n      setBankAccounts(accounts);\n      \n      // Load categories (for future use)\n      categoryService.getAllCategories();\n      \n      // Load all transactions\n      const allTransactions: StoredTransaction[] = [];\n      accounts.forEach(account => {\n        const accountTransactions = transactionStorageService.getTransactionsByAccount(account.id);\n        allTransactions.push(...accountTransactions);\n      });\n      \n      setTransactions(allTransactions);\n      \n      // Detect duplicates within the transaction set\n      const duplicates = findDuplicatesInTransactions(allTransactions);\n      setDuplicateGroups(duplicates);\n      \n      if (onTransactionUpdate) {\n        onTransactionUpdate(allTransactions);\n      }\n    } catch (err) {\n      setError(err instanceof Error ? err.message : 'Failed to load transactions');\n    } finally {\n      setLoading(false);\n      setRefreshing(false);\n    }\n  }, [onTransactionUpdate, findDuplicatesInTransactions]);\n\n  useEffect(() => {\n    loadData();\n  }, [loadData, refreshTrigger]);\n\n  // Filtered and sorted transactions\n  const filteredAndSortedTransactions = useMemo(() => {\n    // Early return if transactions not loaded yet\n    if (!transactions || transactions.length === 0) {\n      return [];\n    }\n    \n    console.log('FILTERING DEBUG: Starting with', transactions.length, 'transactions');\n    let filtered = [...transactions];\n    \n    // Apply filters\n    if (filters.accountId) {\n      filtered = filtered.filter(t => t.accountId === filters.accountId);\n      console.log('FILTERING DEBUG: After accountId filter:', filtered.length);\n    }\n    \n    if (filters.dateFrom) {\n      const fromDate = new Date(filters.dateFrom);\n      filtered = filtered.filter(t => new Date(t.postDateTime) >= fromDate);\n      console.log('FILTERING DEBUG: After dateFrom filter:', filtered.length);\n    }\n    \n    if (filters.dateTo) {\n      const toDate = new Date(filters.dateTo);\n      filtered = filtered.filter(t => new Date(t.postDateTime) <= toDate);\n      console.log('FILTERING DEBUG: After dateTo filter:', filtered.length);\n    }\n    \n    if (filters.description) {\n      const searchTerm = filters.description.toLowerCase();\n      filtered = filtered.filter(t => \n        t.description.toLowerCase().includes(searchTerm) ||\n        (t.reference && t.reference.toLowerCase().includes(searchTerm))\n      );\n      console.log('FILTERING DEBUG: After description filter:', filtered.length);\n    }\n    \n    if (filters.amountFrom) {\n      const minAmount = parseFloat(filters.amountFrom);\n      if (!isNaN(minAmount)) {\n        filtered = filtered.filter(t => {\n          const amount = Math.abs((t.debitAmount || 0) + (t.creditAmount || 0));\n          return amount >= minAmount;\n        });\n        console.log('FILTERING DEBUG: After amountFrom filter:', filtered.length);\n      }\n    }\n    \n    if (filters.amountTo) {\n      const maxAmount = parseFloat(filters.amountTo);\n      if (!isNaN(maxAmount)) {\n        filtered = filtered.filter(t => {\n          const amount = Math.abs((t.debitAmount || 0) + (t.creditAmount || 0));\n          return amount <= maxAmount;\n        });\n        console.log('FILTERING DEBUG: After amountTo filter:', filtered.length);\n      }\n    }\n    \n    if (filters.type === 'debits') {\n      filtered = filtered.filter(t => (t.debitAmount || 0) > 0);\n      console.log('FILTERING DEBUG: After debits filter:', filtered.length);\n    } else if (filters.type === 'credits') {\n      filtered = filtered.filter(t => (t.creditAmount || 0) > 0);\n      console.log('FILTERING DEBUG: After credits filter:', filtered.length);\n    }\n    \n    // Show duplicates only\n    if (showDuplicatesOnly && duplicateGroups.length > 0) {\n      const duplicateIds = new Set(duplicateGroups.flat().map(t => t.id));\n      filtered = filtered.filter(t => duplicateIds.has(t.id));\n      console.log('FILTERING DEBUG: After duplicates filter:', filtered.length);\n    }\n    \n    // Sort transactions\n    if (bankAccounts.length > 0) {\n      filtered.sort((a, b) => {\n        let aValue: string | number;\n        let bValue: string | number;\n        \n        switch (sortField) {\n          case 'postDateTime':\n            aValue = new Date(a.postDateTime).getTime();\n            bValue = new Date(b.postDateTime).getTime();\n            break;\n          case 'description':\n            aValue = a.description.toLowerCase();\n            bValue = b.description.toLowerCase();\n            break;\n          case 'amount':\n            aValue = Math.abs((a.debitAmount || 0) + (a.creditAmount || 0));\n            bValue = Math.abs((b.debitAmount || 0) + (b.creditAmount || 0));\n            break;\n          case 'balance':\n            aValue = a.balance;\n            bValue = b.balance;\n            break;\n          case 'accountName':\n            const accountA = bankAccounts.find(acc => acc.id === a.accountId);\n            const accountB = bankAccounts.find(acc => acc.id === b.accountId);\n            aValue = accountA?.name.toLowerCase() || '';\n            bValue = accountB?.name.toLowerCase() || '';\n            break;\n          default:\n            return 0;\n        }\n        \n        if (sortDirection === 'asc') {\n          return aValue < bValue ? -1 : aValue > bValue ? 1 : 0;\n        } else {\n          return aValue > bValue ? -1 : aValue < bValue ? 1 : 0;\n        }\n      });\n    }\n    \n    console.log('FILTERING DEBUG: Final filtered result:', filtered.length);\n    return filtered;\n  }, [transactions, filters, sortField, sortDirection, showDuplicatesOnly, duplicateGroups, bankAccounts]);\n\n  // Pagination calculations\n  const totalTransactions = filteredAndSortedTransactions.length;\n  const totalPages = Math.ceil(totalTransactions / itemsPerPage);\n  const startIndex = (currentPage - 1) * itemsPerPage;\n  const endIndex = Math.min(startIndex + itemsPerPage, totalTransactions);\n  const currentTransactions = filteredAndSortedTransactions.slice(startIndex, endIndex);\n\n  // Debug pagination issue\n  console.log('PAGINATION DEBUG:', {\n    currentPage,\n    itemsPerPage,\n    totalTransactions,\n    totalPages,\n    startIndex,\n    endIndex,\n    sliceLength: currentTransactions.length,\n    expectedLength: Math.min(itemsPerPage, totalTransactions - startIndex),\n    filteredAndSortedLength: filteredAndSortedTransactions.length,\n    transactionsLength: transactions.length,\n    actualSlice: currentTransactions.slice(0, 3).map(t => ({ id: t.id, desc: t.description.substring(0, 20) }))\n  });\n\n  // Check DOM rendering\n  React.useEffect(() => {\n    const tableRows = document.querySelectorAll('.transactions-table tbody tr');\n    console.log('DOM ROWS COUNT:', tableRows.length, 'Expected:', currentTransactions.length);\n  }, [currentTransactions]);\n\n\n\n  // Reset to first page when filters change\n  useEffect(() => {\n    setCurrentPage(1);\n  }, [filters, sortField, sortDirection, showDuplicatesOnly, itemsPerPage]);\n\n  // Event handlers\n  const handleSort = (field: SortField) => {\n    if (sortField === field) {\n      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');\n    } else {\n      setSortField(field);\n      setSortDirection('desc');\n    }\n  };\n\n  const handleFilterChange = (key: keyof TransactionFilters, value: string) => {\n    setFilters(prev => ({ ...prev, [key]: value }));\n  };\n\n  const handleSelectTransaction = (transactionId: string) => {\n    setSelectedTransactions(prev => {\n      const newSet = new Set(prev);\n      if (newSet.has(transactionId)) {\n        newSet.delete(transactionId);\n      } else {\n        newSet.add(transactionId);\n      }\n      return newSet;\n    });\n  };\n\n  const handleSelectAll = useCallback(() => {\n    if (selectedTransactions.size === currentTransactions.length) {\n      setSelectedTransactions(new Set());\n    } else {\n      setSelectedTransactions(new Set(currentTransactions.map(t => t.id)));\n    }\n  }, [selectedTransactions.size, currentTransactions]);\n\n  const clearFilters = () => {\n    setFilters({\n      accountId: '',\n      dateFrom: '',\n      dateTo: '',\n      amountFrom: '',\n      amountTo: '',\n      description: '',\n      type: 'all'\n    });\n    setShowDuplicatesOnly(false);\n  };\n\n  // Keyboard navigation\n  useEffect(() => {\n    const handleKeyPress = (e: KeyboardEvent) => {\n      if (e.target instanceof HTMLInputElement || e.target instanceof HTMLTextAreaElement) {\n        return; // Don't interfere with input fields\n      }\n\n      switch (e.key) {\n        case 'ArrowLeft':\n          if (currentPage > 1) {\n            setCurrentPage(currentPage - 1);\n          }\n          break;\n        case 'ArrowRight':\n          if (currentPage < totalPages) {\n            setCurrentPage(currentPage + 1);\n          }\n          break;\n        case 'Home':\n          setCurrentPage(1);\n          break;\n        case 'End':\n          setCurrentPage(totalPages);\n          break;\n        case 'f':\n          if (e.ctrlKey || e.metaKey) {\n            e.preventDefault();\n            document.getElementById('search-input')?.focus();\n          }\n          break;\n        case 'r':\n          if (e.ctrlKey || e.metaKey) {\n            e.preventDefault();\n            loadData();\n          }\n          break;\n        case 'a':\n          if (e.ctrlKey || e.metaKey) {\n            e.preventDefault();\n            handleSelectAll();\n          }\n          break;\n        case 'Escape':\n          setSelectedTransactions(new Set());\n          break;\n      }\n    };\n\n    document.addEventListener('keydown', handleKeyPress);\n    return () => document.removeEventListener('keydown', handleKeyPress);\n  }, [currentPage, totalPages, loadData, handleSelectAll]);\n\n  // Utility functions\n  const formatCurrency = (amount: number): string => {\n    return new Intl.NumberFormat('en-US', {\n      minimumFractionDigits: 2,\n      maximumFractionDigits: 2\n    }).format(amount);\n  };\n\n  const formatDate = (dateString: string): string => {\n    const date = new Date(dateString);\n    return date.toLocaleDateString('en-GB', {\n      day: '2-digit',\n      month: '2-digit',\n      year: 'numeric'\n    });\n  };\n\n  const getAccountName = (accountId: string): string => {\n    const account = bankAccounts.find(acc => acc.id === accountId);\n    return account?.name || 'Unknown Account';\n  };\n\n\n\n  const isDuplicate = (transaction: StoredTransaction): boolean => {\n    return duplicateGroups.some(group => group.some(t => t.id === transaction.id));\n  };\n\n  if (loading) {\n    return (\n      <div className=\"transactions-loading\">\n        <div className=\"loading-spinner\"></div>\n        <p>Loading transactions...</p>\n      </div>\n    );\n  }\n\n  if (error) {\n    return (\n      <div className=\"transactions-error\">\n        <div className=\"error-icon\">⚠️</div>\n        <h3>Error Loading Transactions</h3>\n        <p>{error}</p>\n        <button onClick={loadData} className=\"btn btn-primary\">\n          Try Again\n        </button>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"transactions\">\n      <div className=\"transactions-header\">\n        <div className=\"transactions-title-section\">\n          <h2 className=\"transactions-title\">Transactions</h2>\n          <div className=\"transactions-stats\">\n            <span className=\"stat-item\">\n              Total: <strong>{transactions.length.toLocaleString()}</strong>\n            </span>\n            <span className=\"stat-item\">\n              Filtered: <strong>{filteredAndSortedTransactions.length.toLocaleString()}</strong>\n            </span>\n            {duplicateGroups.length > 0 && (\n              <span className=\"stat-item duplicate-stat\">\n                Duplicates: <strong>{duplicateGroups.flat().length}</strong>\n              </span>\n            )}\n          </div>\n        </div>\n        <div className=\"transactions-actions\">\n          <button onClick={loadData} className=\"btn btn-secondary btn-sm\" disabled={refreshing}>\n            <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\">\n              <polyline points=\"23 4 23 10 17 10\"></polyline>\n              <polyline points=\"1 20 1 14 7 14\"></polyline>\n              <path d=\"M20.49 9A9 9 0 0 0 5.64 5.64L1 10m22 4l-4.64 4.36A9 9 0 0 1 3.51 15\"></path>\n            </svg>\n            {refreshing ? 'Refreshing...' : 'Refresh'}\n          </button>\n          {refreshing && (\n            <span className=\"refresh-indicator\">\n              <div className=\"refresh-spinner\"></div>\n              Data updated - refreshing...\n            </span>\n          )}\n          {selectedTransactions.size > 0 && (\n            <span className=\"selection-count\">\n              {selectedTransactions.size} selected\n            </span>\n          )}\n        </div>\n      </div>\n\n      {/* Filters Section */}\n      <div className=\"transactions-filters\">\n        <div className=\"filters-row\">\n          <div className=\"filter-group\">\n            <label className=\"filter-label\">Account</label>\n            <select\n              value={filters.accountId}\n              onChange={(e) => handleFilterChange('accountId', e.target.value)}\n              className=\"filter-select\"\n            >\n              <option value=\"\">All Accounts</option>\n              {bankAccounts.map(account => (\n                <option key={account.id} value={account.id}>\n                  {account.name} - {account.accountNumber}\n                </option>\n              ))}\n            </select>\n          </div>\n\n          <div className=\"filter-group\">\n            <label className=\"filter-label\">Type</label>\n            <select\n              value={filters.type}\n              onChange={(e) => handleFilterChange('type', e.target.value as FilterType)}\n              className=\"filter-select\"\n            >\n              <option value=\"all\">All Types</option>\n              <option value=\"debits\">Debits Only</option>\n              <option value=\"credits\">Credits Only</option>\n            </select>\n          </div>\n\n          <div className=\"filter-group\">\n            <label className=\"filter-label\">\n              <input\n                type=\"checkbox\"\n                checked={showDuplicatesOnly}\n                onChange={(e) => setShowDuplicatesOnly(e.target.checked)}\n                className=\"filter-checkbox\"\n              />\n              Show Duplicates Only\n            </label>\n          </div>\n        </div>\n\n        <div className=\"filters-row\">\n          <div className=\"filter-group\">\n            <label className=\"filter-label\">Search</label>\n            <input\n              id=\"search-input\"\n              type=\"text\"\n              value={filters.description}\n              onChange={(e) => handleFilterChange('description', e.target.value)}\n              placeholder=\"Search description or reference...\"\n              className=\"filter-input\"\n            />\n          </div>\n\n          <div className=\"filter-group\">\n            <label className=\"filter-label\">Date From</label>\n            <input\n              type=\"date\"\n              value={filters.dateFrom}\n              onChange={(e) => handleFilterChange('dateFrom', e.target.value)}\n              className=\"filter-input\"\n            />\n          </div>\n\n          <div className=\"filter-group\">\n            <label className=\"filter-label\">Date To</label>\n            <input\n              type=\"date\"\n              value={filters.dateTo}\n              onChange={(e) => handleFilterChange('dateTo', e.target.value)}\n              className=\"filter-input\"\n            />\n          </div>\n\n          <div className=\"filter-group\">\n            <label className=\"filter-label\">Amount Range</label>\n            <div className=\"amount-range\">\n              <input\n                type=\"number\"\n                value={filters.amountFrom}\n                onChange={(e) => handleFilterChange('amountFrom', e.target.value)}\n                placeholder=\"Min\"\n                className=\"filter-input amount-input\"\n                step=\"0.01\"\n              />\n              <span className=\"amount-separator\">-</span>\n              <input\n                type=\"number\"\n                value={filters.amountTo}\n                onChange={(e) => handleFilterChange('amountTo', e.target.value)}\n                placeholder=\"Max\"\n                className=\"filter-input amount-input\"\n                step=\"0.01\"\n              />\n            </div>\n          </div>\n\n          <div className=\"filter-group\">\n            <button onClick={clearFilters} className=\"btn btn-secondary btn-sm\">\n              Clear Filters\n            </button>\n          </div>\n        </div>\n      </div>\n\n      {/* Transactions Table */}\n      <div className=\"transactions-table-container\">\n        <table className=\"transactions-table\">\n          <thead>\n            <tr>\n              <th className=\"checkbox-col\">\n                <input\n                  type=\"checkbox\"\n                  checked={selectedTransactions.size === currentTransactions.length && currentTransactions.length > 0}\n                  onChange={handleSelectAll}\n                  className=\"table-checkbox\"\n                />\n              </th>\n              <th \n                className={`sortable ${sortField === 'postDateTime' ? 'sorted' : ''}`}\n                onClick={() => handleSort('postDateTime')}\n              >\n                Date\n                {sortField === 'postDateTime' && (\n                  <span className=\"sort-arrow\">\n                    {sortDirection === 'asc' ? '↑' : '↓'}\n                  </span>\n                )}\n              </th>\n              <th \n                className={`sortable ${sortField === 'accountName' ? 'sorted' : ''}`}\n                onClick={() => handleSort('accountName')}\n              >\n                Account\n                {sortField === 'accountName' && (\n                  <span className=\"sort-arrow\">\n                    {sortDirection === 'asc' ? '↑' : '↓'}\n                  </span>\n                )}\n              </th>\n              <th \n                className={`sortable ${sortField === 'description' ? 'sorted' : ''}`}\n                onClick={() => handleSort('description')}\n              >\n                Description\n                {sortField === 'description' && (\n                  <span className=\"sort-arrow\">\n                    {sortDirection === 'asc' ? '↑' : '↓'}\n                  </span>\n                )}\n              </th>\n              <th className=\"amount-col\">Debit</th>\n              <th className=\"amount-col\">Credit</th>\n              <th \n                className={`amount-col sortable ${sortField === 'balance' ? 'sorted' : ''}`}\n                onClick={() => handleSort('balance')}\n              >\n                Balance\n                {sortField === 'balance' && (\n                  <span className=\"sort-arrow\">\n                    {sortDirection === 'asc' ? '↑' : '↓'}\n                  </span>\n                )}\n              </th>\n              <th>Reference</th>\n            </tr>\n          </thead>\n          <tbody>\n            {currentTransactions.map((transaction, _index) => (\n              <tr \n                key={transaction.id}\n                className={`\n                  ${selectedTransactions.has(transaction.id) ? 'selected' : ''}\n                  ${isDuplicate(transaction) ? 'duplicate' : ''}\n                `}\n                title={transaction.description}\n              >\n                <td className=\"checkbox-col\">\n                  <input\n                    type=\"checkbox\"\n                    checked={selectedTransactions.has(transaction.id)}\n                    onChange={() => handleSelectTransaction(transaction.id)}\n                    className=\"table-checkbox\"\n                  />\n                </td>\n                <td className=\"date-col\">\n                  <div className=\"date-display\">\n                    {formatDate(transaction.postDateTime)}\n                    {transaction.time && (\n                      <span className=\"time-display\">{transaction.time}</span>\n                    )}\n                  </div>\n                </td>\n                <td className=\"account-col\">\n                  <div className=\"account-info\">\n                    {getAccountName(transaction.accountId)}\n                    {isDuplicate(transaction) && (\n                      <span className=\"duplicate-badge\">DUPLICATE</span>\n                    )}\n                  </div>\n                </td>\n                <td className=\"description-col\">\n                  <div className=\"description-content\">\n                    {transaction.description}\n                  </div>\n                </td>\n                <td className=\"amount-col debit\">\n                  {transaction.debitAmount ? formatCurrency(transaction.debitAmount) : ''}\n                </td>\n                <td className=\"amount-col credit\">\n                  {transaction.creditAmount ? formatCurrency(transaction.creditAmount) : ''}\n                </td>\n                <td className=\"amount-col balance\">\n                  {formatCurrency(transaction.balance)}\n                </td>\n                <td className=\"reference-col\">\n                  {transaction.reference || ''}\n                </td>\n              </tr>\n            ))}\n          </tbody>\n        </table>\n\n        {currentTransactions.length === 0 && (\n          <div className=\"no-transactions\">\n            <div className=\"no-transactions-icon\">📊</div>\n            <h3>No Transactions Found</h3>\n            <p>\n              {filteredAndSortedTransactions.length === 0 && transactions.length === 0\n                ? 'No transactions have been imported yet.'\n                : 'No transactions match your current filters.'}\n            </p>\n            {filteredAndSortedTransactions.length === 0 && transactions.length > 0 && (\n              <button onClick={clearFilters} className=\"btn btn-primary\">\n                Clear Filters\n              </button>\n            )}\n          </div>\n        )}\n      </div>\n\n      {/* Pagination */}\n      {totalPages > 1 && (\n        <div className=\"transactions-pagination\">\n          <div className=\"pagination-info\">\n            <span>\n              Showing {startIndex + 1}-{Math.min(endIndex, filteredAndSortedTransactions.length)} of {filteredAndSortedTransactions.length} transactions\n            </span>\n            <div className=\"items-per-page\">\n              <label>Items per page:</label>\n              <select\n                value={itemsPerPage}\n                onChange={(e) => setItemsPerPage(Number(e.target.value))}\n                className=\"page-size-select\"\n              >\n                {ITEMS_PER_PAGE_OPTIONS.map(size => (\n                  <option key={size} value={size}>{size}</option>\n                ))}\n              </select>\n            </div>\n          </div>\n          <div className=\"pagination-controls\">\n            <button\n              onClick={() => setCurrentPage(1)}\n              disabled={currentPage === 1}\n              className=\"btn btn-secondary btn-sm\"\n            >\n              First\n            </button>\n            <button\n              onClick={() => setCurrentPage(currentPage - 1)}\n              disabled={currentPage === 1}\n              className=\"btn btn-secondary btn-sm\"\n            >\n              Previous\n            </button>\n            <div className=\"pagination-pages\">\n              {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {\n                const pageNumber = Math.max(1, Math.min(totalPages - 4, currentPage - 2)) + i;\n                return (\n                  <button\n                    key={pageNumber}\n                    onClick={() => setCurrentPage(pageNumber)}\n                    className={`btn btn-secondary btn-sm ${currentPage === pageNumber ? 'active' : ''}`}\n                  >\n                    {pageNumber}\n                  </button>\n                );\n              })}\n            </div>\n            <button\n              onClick={() => setCurrentPage(currentPage + 1)}\n              disabled={currentPage === totalPages}\n              className=\"btn btn-secondary btn-sm\"\n            >\n              Next\n            </button>\n            <button\n              onClick={() => setCurrentPage(totalPages)}\n              disabled={currentPage === totalPages}\n              className=\"btn btn-secondary btn-sm\"\n            >\n              Last\n            </button>\n          </div>\n        </div>\n      )}\n\n      {/* Keyboard Shortcuts Help */}\n      <div className=\"keyboard-shortcuts\">\n        <details>\n          <summary>Keyboard Shortcuts</summary>\n          <div className=\"shortcuts-grid\">\n            <div className=\"shortcut-item\">\n              <kbd>←</kbd> <span>Previous page</span>\n            </div>\n            <div className=\"shortcut-item\">\n              <kbd>→</kbd> <span>Next page</span>\n            </div>\n            <div className=\"shortcut-item\">\n              <kbd>Home</kbd> <span>First page</span>\n            </div>\n            <div className=\"shortcut-item\">\n              <kbd>End</kbd> <span>Last page</span>\n            </div>\n            <div className=\"shortcut-item\">\n              <kbd>Ctrl+F</kbd> <span>Focus search</span>\n            </div>\n            <div className=\"shortcut-item\">\n              <kbd>Ctrl+R</kbd> <span>Refresh</span>\n            </div>\n            <div className=\"shortcut-item\">\n              <kbd>Ctrl+A</kbd> <span>Select all</span>\n            </div>\n            <div className=\"shortcut-item\">\n              <kbd>Esc</kbd> <span>Clear selection</span>\n            </div>\n          </div>\n        </details>\n      </div>\n    </div>\n  );\n}; "], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,EAAEC,OAAO,QAAQ,OAAO;AACxE,SAASC,yBAAyB,QAAgC,uCAAuC;AACzG,SAASC,kBAAkB,QAAQ,gCAAgC;AACnE,SAASC,eAAe,QAAQ,6BAA6B;AAE7D,OAAO,oBAAoB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAsB5B,MAAMC,sBAAsB,GAAG,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,CAAC;AAChD,MAAMC,sBAAsB,GAAG,EAAE;AAEjC,OAAO,MAAMC,YAAyC,GAAGA,CAAC;EAAEC,mBAAmB;EAAEC;AAAe,CAAC,KAAK;EAAAC,EAAA;EACpG;EACA,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGhB,QAAQ,CAAsB,EAAE,CAAC;EACzE,MAAM,CAACiB,YAAY,EAAEC,eAAe,CAAC,GAAGlB,QAAQ,CAAgB,EAAE,CAAC;EACnE,MAAM,CAACmB,eAAe,EAAEC,kBAAkB,CAAC,GAAGpB,QAAQ,CAAwB,EAAE,CAAC;EACjF,MAAM,CAACqB,OAAO,EAAEC,UAAU,CAAC,GAAGtB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACuB,KAAK,EAAEC,QAAQ,CAAC,GAAGxB,QAAQ,CAAgB,IAAI,CAAC;EACvD,MAAM,CAACyB,UAAU,EAAEC,aAAa,CAAC,GAAG1B,QAAQ,CAAC,KAAK,CAAC;;EAEnD;EACA,MAAM,CAAC2B,WAAW,EAAEC,cAAc,CAAC,GAAG5B,QAAQ,CAAC,CAAC,CAAC;EACjD,MAAM,CAAC6B,YAAY,EAAEC,eAAe,CAAC,GAAG9B,QAAQ,CAACU,sBAAsB,CAAC;;EAExE;EACA,MAAM,CAACqB,SAAS,EAAEC,YAAY,CAAC,GAAGhC,QAAQ,CAAY,cAAc,CAAC;EACrE,MAAM,CAACiC,aAAa,EAAEC,gBAAgB,CAAC,GAAGlC,QAAQ,CAAgB,MAAM,CAAC;;EAEzE;EACA,MAAM,CAACmC,OAAO,EAAEC,UAAU,CAAC,GAAGpC,QAAQ,CAAqB;IACzDqC,SAAS,EAAE,EAAE;IACbC,QAAQ,EAAE,EAAE;IACZC,MAAM,EAAE,EAAE;IACVC,UAAU,EAAE,EAAE;IACdC,QAAQ,EAAE,EAAE;IACZC,WAAW,EAAE,EAAE;IACfC,IAAI,EAAE;EACR,CAAC,CAAC;;EAEF;EACA,MAAM,CAACC,oBAAoB,EAAEC,uBAAuB,CAAC,GAAG7C,QAAQ,CAAc,IAAI8C,GAAG,CAAC,CAAC,CAAC;EACxF,MAAM,CAACC,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGhD,QAAQ,CAAC,KAAK,CAAC;;EAEnE;;EAGA;EACA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;EAEE;EACA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;EAME;EACA,MAAMiD,4BAA4B,GAAG/C,WAAW,CAAEgD,aAAkC,IAA4B;IAC9G;IACA,OAAO,EAAE;;IAET;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EAOE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;;EAER;EACA,MAAMC,QAAQ,GAAGjD,WAAW,CAAC,YAAY;IACvC,IAAI;MACF;MACA,IAAIa,YAAY,CAACqC,MAAM,GAAG,CAAC,EAAE;QAC3B1B,aAAa,CAAC,IAAI,CAAC;MACrB,CAAC,MAAM;QACLJ,UAAU,CAAC,IAAI,CAAC;MAClB;MACAE,QAAQ,CAAC,IAAI,CAAC;;MAEd;MACA,MAAM6B,QAAQ,GAAGhD,kBAAkB,CAACiD,cAAc,CAAC,CAAC;MACpDpC,eAAe,CAACmC,QAAQ,CAAC;;MAEzB;MACA/C,eAAe,CAACiD,gBAAgB,CAAC,CAAC;;MAElC;MACA,MAAMC,eAAoC,GAAG,EAAE;MAC/CH,QAAQ,CAACI,OAAO,CAACC,OAAO,IAAI;QAC1B,MAAMC,mBAAmB,GAAGvD,yBAAyB,CAACwD,wBAAwB,CAACF,OAAO,CAACG,EAAE,CAAC;QAC1FL,eAAe,CAACM,IAAI,CAAC,GAAGH,mBAAmB,CAAC;MAC9C,CAAC,CAAC;MAEF3C,eAAe,CAACwC,eAAe,CAAC;;MAEhC;MACA,MAAMO,UAAU,GAAGd,4BAA4B,CAACO,eAAe,CAAC;MAChEpC,kBAAkB,CAAC2C,UAAU,CAAC;MAE9B,IAAInD,mBAAmB,EAAE;QACvBA,mBAAmB,CAAC4C,eAAe,CAAC;MACtC;IACF,CAAC,CAAC,OAAOQ,GAAG,EAAE;MACZxC,QAAQ,CAACwC,GAAG,YAAYC,KAAK,GAAGD,GAAG,CAACE,OAAO,GAAG,6BAA6B,CAAC;IAC9E,CAAC,SAAS;MACR5C,UAAU,CAAC,KAAK,CAAC;MACjBI,aAAa,CAAC,KAAK,CAAC;IACtB;EACF,CAAC,EAAE,CAACd,mBAAmB,EAAEqC,4BAA4B,CAAC,CAAC;EAEvDhD,SAAS,CAAC,MAAM;IACdkD,QAAQ,CAAC,CAAC;EACZ,CAAC,EAAE,CAACA,QAAQ,EAAEtC,cAAc,CAAC,CAAC;;EAE9B;EACA,MAAMsD,6BAA6B,GAAGhE,OAAO,CAAC,MAAM;IAClD;IACA,IAAI,CAACY,YAAY,IAAIA,YAAY,CAACqC,MAAM,KAAK,CAAC,EAAE;MAC9C,OAAO,EAAE;IACX;IAEAgB,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAEtD,YAAY,CAACqC,MAAM,EAAE,cAAc,CAAC;IAClF,IAAIkB,QAAQ,GAAG,CAAC,GAAGvD,YAAY,CAAC;;IAEhC;IACA,IAAIoB,OAAO,CAACE,SAAS,EAAE;MACrBiC,QAAQ,GAAGA,QAAQ,CAACC,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACnC,SAAS,KAAKF,OAAO,CAACE,SAAS,CAAC;MAClE+B,OAAO,CAACC,GAAG,CAAC,0CAA0C,EAAEC,QAAQ,CAAClB,MAAM,CAAC;IAC1E;IAEA,IAAIjB,OAAO,CAACG,QAAQ,EAAE;MACpB,MAAMmC,QAAQ,GAAG,IAAIC,IAAI,CAACvC,OAAO,CAACG,QAAQ,CAAC;MAC3CgC,QAAQ,GAAGA,QAAQ,CAACC,MAAM,CAACC,CAAC,IAAI,IAAIE,IAAI,CAACF,CAAC,CAACG,YAAY,CAAC,IAAIF,QAAQ,CAAC;MACrEL,OAAO,CAACC,GAAG,CAAC,yCAAyC,EAAEC,QAAQ,CAAClB,MAAM,CAAC;IACzE;IAEA,IAAIjB,OAAO,CAACI,MAAM,EAAE;MAClB,MAAMqC,MAAM,GAAG,IAAIF,IAAI,CAACvC,OAAO,CAACI,MAAM,CAAC;MACvC+B,QAAQ,GAAGA,QAAQ,CAACC,MAAM,CAACC,CAAC,IAAI,IAAIE,IAAI,CAACF,CAAC,CAACG,YAAY,CAAC,IAAIC,MAAM,CAAC;MACnER,OAAO,CAACC,GAAG,CAAC,uCAAuC,EAAEC,QAAQ,CAAClB,MAAM,CAAC;IACvE;IAEA,IAAIjB,OAAO,CAACO,WAAW,EAAE;MACvB,MAAMmC,UAAU,GAAG1C,OAAO,CAACO,WAAW,CAACoC,WAAW,CAAC,CAAC;MACpDR,QAAQ,GAAGA,QAAQ,CAACC,MAAM,CAACC,CAAC,IAC1BA,CAAC,CAAC9B,WAAW,CAACoC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACF,UAAU,CAAC,IAC/CL,CAAC,CAACQ,SAAS,IAAIR,CAAC,CAACQ,SAAS,CAACF,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACF,UAAU,CAC/D,CAAC;MACDT,OAAO,CAACC,GAAG,CAAC,4CAA4C,EAAEC,QAAQ,CAAClB,MAAM,CAAC;IAC5E;IAEA,IAAIjB,OAAO,CAACK,UAAU,EAAE;MACtB,MAAMyC,SAAS,GAAGC,UAAU,CAAC/C,OAAO,CAACK,UAAU,CAAC;MAChD,IAAI,CAAC2C,KAAK,CAACF,SAAS,CAAC,EAAE;QACrBX,QAAQ,GAAGA,QAAQ,CAACC,MAAM,CAACC,CAAC,IAAI;UAC9B,MAAMY,MAAM,GAAGC,IAAI,CAACC,GAAG,CAAC,CAACd,CAAC,CAACe,WAAW,IAAI,CAAC,KAAKf,CAAC,CAACgB,YAAY,IAAI,CAAC,CAAC,CAAC;UACrE,OAAOJ,MAAM,IAAIH,SAAS;QAC5B,CAAC,CAAC;QACFb,OAAO,CAACC,GAAG,CAAC,2CAA2C,EAAEC,QAAQ,CAAClB,MAAM,CAAC;MAC3E;IACF;IAEA,IAAIjB,OAAO,CAACM,QAAQ,EAAE;MACpB,MAAMgD,SAAS,GAAGP,UAAU,CAAC/C,OAAO,CAACM,QAAQ,CAAC;MAC9C,IAAI,CAAC0C,KAAK,CAACM,SAAS,CAAC,EAAE;QACrBnB,QAAQ,GAAGA,QAAQ,CAACC,MAAM,CAACC,CAAC,IAAI;UAC9B,MAAMY,MAAM,GAAGC,IAAI,CAACC,GAAG,CAAC,CAACd,CAAC,CAACe,WAAW,IAAI,CAAC,KAAKf,CAAC,CAACgB,YAAY,IAAI,CAAC,CAAC,CAAC;UACrE,OAAOJ,MAAM,IAAIK,SAAS;QAC5B,CAAC,CAAC;QACFrB,OAAO,CAACC,GAAG,CAAC,yCAAyC,EAAEC,QAAQ,CAAClB,MAAM,CAAC;MACzE;IACF;IAEA,IAAIjB,OAAO,CAACQ,IAAI,KAAK,QAAQ,EAAE;MAC7B2B,QAAQ,GAAGA,QAAQ,CAACC,MAAM,CAACC,CAAC,IAAI,CAACA,CAAC,CAACe,WAAW,IAAI,CAAC,IAAI,CAAC,CAAC;MACzDnB,OAAO,CAACC,GAAG,CAAC,uCAAuC,EAAEC,QAAQ,CAAClB,MAAM,CAAC;IACvE,CAAC,MAAM,IAAIjB,OAAO,CAACQ,IAAI,KAAK,SAAS,EAAE;MACrC2B,QAAQ,GAAGA,QAAQ,CAACC,MAAM,CAACC,CAAC,IAAI,CAACA,CAAC,CAACgB,YAAY,IAAI,CAAC,IAAI,CAAC,CAAC;MAC1DpB,OAAO,CAACC,GAAG,CAAC,wCAAwC,EAAEC,QAAQ,CAAClB,MAAM,CAAC;IACxE;;IAEA;IACA,IAAIL,kBAAkB,IAAI5B,eAAe,CAACiC,MAAM,GAAG,CAAC,EAAE;MACpD,MAAMsC,YAAY,GAAG,IAAI5C,GAAG,CAAC3B,eAAe,CAACwE,IAAI,CAAC,CAAC,CAACC,GAAG,CAACpB,CAAC,IAAIA,CAAC,CAACX,EAAE,CAAC,CAAC;MACnES,QAAQ,GAAGA,QAAQ,CAACC,MAAM,CAACC,CAAC,IAAIkB,YAAY,CAACG,GAAG,CAACrB,CAAC,CAACX,EAAE,CAAC,CAAC;MACvDO,OAAO,CAACC,GAAG,CAAC,2CAA2C,EAAEC,QAAQ,CAAClB,MAAM,CAAC;IAC3E;;IAEA;IACA,IAAInC,YAAY,CAACmC,MAAM,GAAG,CAAC,EAAE;MAC3BkB,QAAQ,CAACwB,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;QACtB,IAAIC,MAAuB;QAC3B,IAAIC,MAAuB;QAE3B,QAAQnE,SAAS;UACf,KAAK,cAAc;YACjBkE,MAAM,GAAG,IAAIvB,IAAI,CAACqB,CAAC,CAACpB,YAAY,CAAC,CAACwB,OAAO,CAAC,CAAC;YAC3CD,MAAM,GAAG,IAAIxB,IAAI,CAACsB,CAAC,CAACrB,YAAY,CAAC,CAACwB,OAAO,CAAC,CAAC;YAC3C;UACF,KAAK,aAAa;YAChBF,MAAM,GAAGF,CAAC,CAACrD,WAAW,CAACoC,WAAW,CAAC,CAAC;YACpCoB,MAAM,GAAGF,CAAC,CAACtD,WAAW,CAACoC,WAAW,CAAC,CAAC;YACpC;UACF,KAAK,QAAQ;YACXmB,MAAM,GAAGZ,IAAI,CAACC,GAAG,CAAC,CAACS,CAAC,CAACR,WAAW,IAAI,CAAC,KAAKQ,CAAC,CAACP,YAAY,IAAI,CAAC,CAAC,CAAC;YAC/DU,MAAM,GAAGb,IAAI,CAACC,GAAG,CAAC,CAACU,CAAC,CAACT,WAAW,IAAI,CAAC,KAAKS,CAAC,CAACR,YAAY,IAAI,CAAC,CAAC,CAAC;YAC/D;UACF,KAAK,SAAS;YACZS,MAAM,GAAGF,CAAC,CAACK,OAAO;YAClBF,MAAM,GAAGF,CAAC,CAACI,OAAO;YAClB;UACF,KAAK,aAAa;YAChB,MAAMC,QAAQ,GAAGpF,YAAY,CAACqF,IAAI,CAACC,GAAG,IAAIA,GAAG,CAAC1C,EAAE,KAAKkC,CAAC,CAAC1D,SAAS,CAAC;YACjE,MAAMmE,QAAQ,GAAGvF,YAAY,CAACqF,IAAI,CAACC,GAAG,IAAIA,GAAG,CAAC1C,EAAE,KAAKmC,CAAC,CAAC3D,SAAS,CAAC;YACjE4D,MAAM,GAAG,CAAAI,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEI,IAAI,CAAC3B,WAAW,CAAC,CAAC,KAAI,EAAE;YAC3CoB,MAAM,GAAG,CAAAM,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEC,IAAI,CAAC3B,WAAW,CAAC,CAAC,KAAI,EAAE;YAC3C;UACF;YACE,OAAO,CAAC;QACZ;QAEA,IAAI7C,aAAa,KAAK,KAAK,EAAE;UAC3B,OAAOgE,MAAM,GAAGC,MAAM,GAAG,CAAC,CAAC,GAAGD,MAAM,GAAGC,MAAM,GAAG,CAAC,GAAG,CAAC;QACvD,CAAC,MAAM;UACL,OAAOD,MAAM,GAAGC,MAAM,GAAG,CAAC,CAAC,GAAGD,MAAM,GAAGC,MAAM,GAAG,CAAC,GAAG,CAAC;QACvD;MACF,CAAC,CAAC;IACJ;IAEA9B,OAAO,CAACC,GAAG,CAAC,yCAAyC,EAAEC,QAAQ,CAAClB,MAAM,CAAC;IACvE,OAAOkB,QAAQ;EACjB,CAAC,EAAE,CAACvD,YAAY,EAAEoB,OAAO,EAAEJ,SAAS,EAAEE,aAAa,EAAEc,kBAAkB,EAAE5B,eAAe,EAAEF,YAAY,CAAC,CAAC;;EAExG;EACA,MAAMyF,iBAAiB,GAAGvC,6BAA6B,CAACf,MAAM;EAC9D,MAAMuD,UAAU,GAAGtB,IAAI,CAACuB,IAAI,CAACF,iBAAiB,GAAG7E,YAAY,CAAC;EAC9D,MAAMgF,UAAU,GAAG,CAAClF,WAAW,GAAG,CAAC,IAAIE,YAAY;EACnD,MAAMiF,QAAQ,GAAGzB,IAAI,CAAC0B,GAAG,CAACF,UAAU,GAAGhF,YAAY,EAAE6E,iBAAiB,CAAC;EACvE,MAAMM,mBAAmB,GAAG7C,6BAA6B,CAAC8C,KAAK,CAACJ,UAAU,EAAEC,QAAQ,CAAC;;EAErF;EACA1C,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAE;IAC/B1C,WAAW;IACXE,YAAY;IACZ6E,iBAAiB;IACjBC,UAAU;IACVE,UAAU;IACVC,QAAQ;IACRI,WAAW,EAAEF,mBAAmB,CAAC5D,MAAM;IACvC+D,cAAc,EAAE9B,IAAI,CAAC0B,GAAG,CAAClF,YAAY,EAAE6E,iBAAiB,GAAGG,UAAU,CAAC;IACtEO,uBAAuB,EAAEjD,6BAA6B,CAACf,MAAM;IAC7DiE,kBAAkB,EAAEtG,YAAY,CAACqC,MAAM;IACvCkE,WAAW,EAAEN,mBAAmB,CAACC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACrB,GAAG,CAACpB,CAAC,KAAK;MAAEX,EAAE,EAAEW,CAAC,CAACX,EAAE;MAAE0D,IAAI,EAAE/C,CAAC,CAAC9B,WAAW,CAAC8E,SAAS,CAAC,CAAC,EAAE,EAAE;IAAE,CAAC,CAAC;EAC5G,CAAC,CAAC;;EAEF;EACAzH,KAAK,CAACE,SAAS,CAAC,MAAM;IACpB,MAAMwH,SAAS,GAAGC,QAAQ,CAACC,gBAAgB,CAAC,8BAA8B,CAAC;IAC3EvD,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEoD,SAAS,CAACrE,MAAM,EAAE,WAAW,EAAE4D,mBAAmB,CAAC5D,MAAM,CAAC;EAC3F,CAAC,EAAE,CAAC4D,mBAAmB,CAAC,CAAC;;EAIzB;EACA/G,SAAS,CAAC,MAAM;IACd2B,cAAc,CAAC,CAAC,CAAC;EACnB,CAAC,EAAE,CAACO,OAAO,EAAEJ,SAAS,EAAEE,aAAa,EAAEc,kBAAkB,EAAElB,YAAY,CAAC,CAAC;;EAEzE;EACA,MAAM+F,UAAU,GAAIC,KAAgB,IAAK;IACvC,IAAI9F,SAAS,KAAK8F,KAAK,EAAE;MACvB3F,gBAAgB,CAACD,aAAa,KAAK,KAAK,GAAG,MAAM,GAAG,KAAK,CAAC;IAC5D,CAAC,MAAM;MACLD,YAAY,CAAC6F,KAAK,CAAC;MACnB3F,gBAAgB,CAAC,MAAM,CAAC;IAC1B;EACF,CAAC;EAED,MAAM4F,kBAAkB,GAAGA,CAACC,GAA6B,EAAEC,KAAa,KAAK;IAC3E5F,UAAU,CAAC6F,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE,CAACF,GAAG,GAAGC;IAAM,CAAC,CAAC,CAAC;EACjD,CAAC;EAED,MAAME,uBAAuB,GAAIC,aAAqB,IAAK;IACzDtF,uBAAuB,CAACoF,IAAI,IAAI;MAC9B,MAAMG,MAAM,GAAG,IAAItF,GAAG,CAACmF,IAAI,CAAC;MAC5B,IAAIG,MAAM,CAACvC,GAAG,CAACsC,aAAa,CAAC,EAAE;QAC7BC,MAAM,CAACC,MAAM,CAACF,aAAa,CAAC;MAC9B,CAAC,MAAM;QACLC,MAAM,CAACE,GAAG,CAACH,aAAa,CAAC;MAC3B;MACA,OAAOC,MAAM;IACf,CAAC,CAAC;EACJ,CAAC;EAED,MAAMG,eAAe,GAAGrI,WAAW,CAAC,MAAM;IACxC,IAAI0C,oBAAoB,CAAC4F,IAAI,KAAKxB,mBAAmB,CAAC5D,MAAM,EAAE;MAC5DP,uBAAuB,CAAC,IAAIC,GAAG,CAAC,CAAC,CAAC;IACpC,CAAC,MAAM;MACLD,uBAAuB,CAAC,IAAIC,GAAG,CAACkE,mBAAmB,CAACpB,GAAG,CAACpB,CAAC,IAAIA,CAAC,CAACX,EAAE,CAAC,CAAC,CAAC;IACtE;EACF,CAAC,EAAE,CAACjB,oBAAoB,CAAC4F,IAAI,EAAExB,mBAAmB,CAAC,CAAC;EAEpD,MAAMyB,YAAY,GAAGA,CAAA,KAAM;IACzBrG,UAAU,CAAC;MACTC,SAAS,EAAE,EAAE;MACbC,QAAQ,EAAE,EAAE;MACZC,MAAM,EAAE,EAAE;MACVC,UAAU,EAAE,EAAE;MACdC,QAAQ,EAAE,EAAE;MACZC,WAAW,EAAE,EAAE;MACfC,IAAI,EAAE;IACR,CAAC,CAAC;IACFK,qBAAqB,CAAC,KAAK,CAAC;EAC9B,CAAC;;EAED;EACA/C,SAAS,CAAC,MAAM;IACd,MAAMyI,cAAc,GAAIC,CAAgB,IAAK;MAC3C,IAAIA,CAAC,CAACC,MAAM,YAAYC,gBAAgB,IAAIF,CAAC,CAACC,MAAM,YAAYE,mBAAmB,EAAE;QACnF,OAAO,CAAC;MACV;MAEA,QAAQH,CAAC,CAACZ,GAAG;QACX,KAAK,WAAW;UACd,IAAIpG,WAAW,GAAG,CAAC,EAAE;YACnBC,cAAc,CAACD,WAAW,GAAG,CAAC,CAAC;UACjC;UACA;QACF,KAAK,YAAY;UACf,IAAIA,WAAW,GAAGgF,UAAU,EAAE;YAC5B/E,cAAc,CAACD,WAAW,GAAG,CAAC,CAAC;UACjC;UACA;QACF,KAAK,MAAM;UACTC,cAAc,CAAC,CAAC,CAAC;UACjB;QACF,KAAK,KAAK;UACRA,cAAc,CAAC+E,UAAU,CAAC;UAC1B;QACF,KAAK,GAAG;UACN,IAAIgC,CAAC,CAACI,OAAO,IAAIJ,CAAC,CAACK,OAAO,EAAE;YAAA,IAAAC,qBAAA;YAC1BN,CAAC,CAACO,cAAc,CAAC,CAAC;YAClB,CAAAD,qBAAA,GAAAvB,QAAQ,CAACyB,cAAc,CAAC,cAAc,CAAC,cAAAF,qBAAA,uBAAvCA,qBAAA,CAAyCG,KAAK,CAAC,CAAC;UAClD;UACA;QACF,KAAK,GAAG;UACN,IAAIT,CAAC,CAACI,OAAO,IAAIJ,CAAC,CAACK,OAAO,EAAE;YAC1BL,CAAC,CAACO,cAAc,CAAC,CAAC;YAClB/F,QAAQ,CAAC,CAAC;UACZ;UACA;QACF,KAAK,GAAG;UACN,IAAIwF,CAAC,CAACI,OAAO,IAAIJ,CAAC,CAACK,OAAO,EAAE;YAC1BL,CAAC,CAACO,cAAc,CAAC,CAAC;YAClBX,eAAe,CAAC,CAAC;UACnB;UACA;QACF,KAAK,QAAQ;UACX1F,uBAAuB,CAAC,IAAIC,GAAG,CAAC,CAAC,CAAC;UAClC;MACJ;IACF,CAAC;IAED4E,QAAQ,CAAC2B,gBAAgB,CAAC,SAAS,EAAEX,cAAc,CAAC;IACpD,OAAO,MAAMhB,QAAQ,CAAC4B,mBAAmB,CAAC,SAAS,EAAEZ,cAAc,CAAC;EACtE,CAAC,EAAE,CAAC/G,WAAW,EAAEgF,UAAU,EAAExD,QAAQ,EAAEoF,eAAe,CAAC,CAAC;;EAExD;EACA,MAAMgB,cAAc,GAAInE,MAAc,IAAa;IACjD,OAAO,IAAIoE,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;MACpCC,qBAAqB,EAAE,CAAC;MACxBC,qBAAqB,EAAE;IACzB,CAAC,CAAC,CAACC,MAAM,CAACxE,MAAM,CAAC;EACnB,CAAC;EAED,MAAMyE,UAAU,GAAIC,UAAkB,IAAa;IACjD,MAAMC,IAAI,GAAG,IAAIrF,IAAI,CAACoF,UAAU,CAAC;IACjC,OAAOC,IAAI,CAACC,kBAAkB,CAAC,OAAO,EAAE;MACtCC,GAAG,EAAE,SAAS;MACdC,KAAK,EAAE,SAAS;MAChBC,IAAI,EAAE;IACR,CAAC,CAAC;EACJ,CAAC;EAED,MAAMC,cAAc,GAAI/H,SAAiB,IAAa;IACpD,MAAMqB,OAAO,GAAGzC,YAAY,CAACqF,IAAI,CAACC,GAAG,IAAIA,GAAG,CAAC1C,EAAE,KAAKxB,SAAS,CAAC;IAC9D,OAAO,CAAAqB,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAE+C,IAAI,KAAI,iBAAiB;EAC3C,CAAC;EAID,MAAM4D,WAAW,GAAIC,WAA8B,IAAc;IAC/D,OAAOnJ,eAAe,CAACoJ,IAAI,CAACC,KAAK,IAAIA,KAAK,CAACD,IAAI,CAAC/F,CAAC,IAAIA,CAAC,CAACX,EAAE,KAAKyG,WAAW,CAACzG,EAAE,CAAC,CAAC;EAChF,CAAC;EAED,IAAIxC,OAAO,EAAE;IACX,oBACEb,OAAA;MAAKiK,SAAS,EAAC,sBAAsB;MAAAC,QAAA,gBACnClK,OAAA;QAAKiK,SAAS,EAAC;MAAiB;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACvCtK,OAAA;QAAAkK,QAAA,EAAG;MAAuB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC3B,CAAC;EAEV;EAEA,IAAIvJ,KAAK,EAAE;IACT,oBACEf,OAAA;MAAKiK,SAAS,EAAC,oBAAoB;MAAAC,QAAA,gBACjClK,OAAA;QAAKiK,SAAS,EAAC,YAAY;QAAAC,QAAA,EAAC;MAAE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eACpCtK,OAAA;QAAAkK,QAAA,EAAI;MAA0B;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACnCtK,OAAA;QAAAkK,QAAA,EAAInJ;MAAK;QAAAoJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACdtK,OAAA;QAAQuK,OAAO,EAAE5H,QAAS;QAACsH,SAAS,EAAC,iBAAiB;QAAAC,QAAA,EAAC;MAEvD;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAEV;EAEA,oBACEtK,OAAA;IAAKiK,SAAS,EAAC,cAAc;IAAAC,QAAA,gBAC3BlK,OAAA;MAAKiK,SAAS,EAAC,qBAAqB;MAAAC,QAAA,gBAClClK,OAAA;QAAKiK,SAAS,EAAC,4BAA4B;QAAAC,QAAA,gBACzClK,OAAA;UAAIiK,SAAS,EAAC,oBAAoB;UAAAC,QAAA,EAAC;QAAY;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACpDtK,OAAA;UAAKiK,SAAS,EAAC,oBAAoB;UAAAC,QAAA,gBACjClK,OAAA;YAAMiK,SAAS,EAAC,WAAW;YAAAC,QAAA,GAAC,SACnB,eAAAlK,OAAA;cAAAkK,QAAA,EAAS3J,YAAY,CAACqC,MAAM,CAAC4H,cAAc,CAAC;YAAC;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAS,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1D,CAAC,eACPtK,OAAA;YAAMiK,SAAS,EAAC,WAAW;YAAAC,QAAA,GAAC,YAChB,eAAAlK,OAAA;cAAAkK,QAAA,EAASvG,6BAA6B,CAACf,MAAM,CAAC4H,cAAc,CAAC;YAAC;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAS,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9E,CAAC,EACN3J,eAAe,CAACiC,MAAM,GAAG,CAAC,iBACzB5C,OAAA;YAAMiK,SAAS,EAAC,0BAA0B;YAAAC,QAAA,GAAC,cAC7B,eAAAlK,OAAA;cAAAkK,QAAA,EAASvJ,eAAe,CAACwE,IAAI,CAAC,CAAC,CAACvC;YAAM;cAAAuH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAS,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxD,CACP;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACNtK,OAAA;QAAKiK,SAAS,EAAC,sBAAsB;QAAAC,QAAA,gBACnClK,OAAA;UAAQuK,OAAO,EAAE5H,QAAS;UAACsH,SAAS,EAAC,0BAA0B;UAACQ,QAAQ,EAAExJ,UAAW;UAAAiJ,QAAA,gBACnFlK,OAAA;YAAK0K,KAAK,EAAC,IAAI;YAACC,MAAM,EAAC,IAAI;YAACC,OAAO,EAAC,WAAW;YAACC,IAAI,EAAC,MAAM;YAACC,MAAM,EAAC,cAAc;YAACC,WAAW,EAAC,GAAG;YAAAb,QAAA,gBAC/FlK,OAAA;cAAUgL,MAAM,EAAC;YAAkB;cAAAb,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eAC/CtK,OAAA;cAAUgL,MAAM,EAAC;YAAgB;cAAAb,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eAC7CtK,OAAA;cAAMiL,CAAC,EAAC;YAAqE;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClF,CAAC,EACLrJ,UAAU,GAAG,eAAe,GAAG,SAAS;QAAA;UAAAkJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnC,CAAC,EACRrJ,UAAU,iBACTjB,OAAA;UAAMiK,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBACjClK,OAAA;YAAKiK,SAAS,EAAC;UAAiB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,gCAEzC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CACP,EACAlI,oBAAoB,CAAC4F,IAAI,GAAG,CAAC,iBAC5BhI,OAAA;UAAMiK,SAAS,EAAC,iBAAiB;UAAAC,QAAA,GAC9B9H,oBAAoB,CAAC4F,IAAI,EAAC,WAC7B;QAAA;UAAAmC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CACP;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNtK,OAAA;MAAKiK,SAAS,EAAC,sBAAsB;MAAAC,QAAA,gBACnClK,OAAA;QAAKiK,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1BlK,OAAA;UAAKiK,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3BlK,OAAA;YAAOiK,SAAS,EAAC,cAAc;YAAAC,QAAA,EAAC;UAAO;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAC/CtK,OAAA;YACEwH,KAAK,EAAE7F,OAAO,CAACE,SAAU;YACzBqJ,QAAQ,EAAG/C,CAAC,IAAKb,kBAAkB,CAAC,WAAW,EAAEa,CAAC,CAACC,MAAM,CAACZ,KAAK,CAAE;YACjEyC,SAAS,EAAC,eAAe;YAAAC,QAAA,gBAEzBlK,OAAA;cAAQwH,KAAK,EAAC,EAAE;cAAA0C,QAAA,EAAC;YAAY;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,EACrC7J,YAAY,CAAC2E,GAAG,CAAClC,OAAO,iBACvBlD,OAAA;cAAyBwH,KAAK,EAAEtE,OAAO,CAACG,EAAG;cAAA6G,QAAA,GACxChH,OAAO,CAAC+C,IAAI,EAAC,KAAG,EAAC/C,OAAO,CAACiI,aAAa;YAAA,GAD5BjI,OAAO,CAACG,EAAE;cAAA8G,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEf,CACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAENtK,OAAA;UAAKiK,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3BlK,OAAA;YAAOiK,SAAS,EAAC,cAAc;YAAAC,QAAA,EAAC;UAAI;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAC5CtK,OAAA;YACEwH,KAAK,EAAE7F,OAAO,CAACQ,IAAK;YACpB+I,QAAQ,EAAG/C,CAAC,IAAKb,kBAAkB,CAAC,MAAM,EAAEa,CAAC,CAACC,MAAM,CAACZ,KAAmB,CAAE;YAC1EyC,SAAS,EAAC,eAAe;YAAAC,QAAA,gBAEzBlK,OAAA;cAAQwH,KAAK,EAAC,KAAK;cAAA0C,QAAA,EAAC;YAAS;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACtCtK,OAAA;cAAQwH,KAAK,EAAC,QAAQ;cAAA0C,QAAA,EAAC;YAAW;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAC3CtK,OAAA;cAAQwH,KAAK,EAAC,SAAS;cAAA0C,QAAA,EAAC;YAAY;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAENtK,OAAA;UAAKiK,SAAS,EAAC,cAAc;UAAAC,QAAA,eAC3BlK,OAAA;YAAOiK,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC7BlK,OAAA;cACEmC,IAAI,EAAC,UAAU;cACfiJ,OAAO,EAAE7I,kBAAmB;cAC5B2I,QAAQ,EAAG/C,CAAC,IAAK3F,qBAAqB,CAAC2F,CAAC,CAACC,MAAM,CAACgD,OAAO,CAAE;cACzDnB,SAAS,EAAC;YAAiB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5B,CAAC,wBAEJ;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENtK,OAAA;QAAKiK,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1BlK,OAAA;UAAKiK,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3BlK,OAAA;YAAOiK,SAAS,EAAC,cAAc;YAAAC,QAAA,EAAC;UAAM;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAC9CtK,OAAA;YACEqD,EAAE,EAAC,cAAc;YACjBlB,IAAI,EAAC,MAAM;YACXqF,KAAK,EAAE7F,OAAO,CAACO,WAAY;YAC3BgJ,QAAQ,EAAG/C,CAAC,IAAKb,kBAAkB,CAAC,aAAa,EAAEa,CAAC,CAACC,MAAM,CAACZ,KAAK,CAAE;YACnE6D,WAAW,EAAC,oCAAoC;YAChDpB,SAAS,EAAC;UAAc;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAENtK,OAAA;UAAKiK,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3BlK,OAAA;YAAOiK,SAAS,EAAC,cAAc;YAAAC,QAAA,EAAC;UAAS;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACjDtK,OAAA;YACEmC,IAAI,EAAC,MAAM;YACXqF,KAAK,EAAE7F,OAAO,CAACG,QAAS;YACxBoJ,QAAQ,EAAG/C,CAAC,IAAKb,kBAAkB,CAAC,UAAU,EAAEa,CAAC,CAACC,MAAM,CAACZ,KAAK,CAAE;YAChEyC,SAAS,EAAC;UAAc;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAENtK,OAAA;UAAKiK,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3BlK,OAAA;YAAOiK,SAAS,EAAC,cAAc;YAAAC,QAAA,EAAC;UAAO;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAC/CtK,OAAA;YACEmC,IAAI,EAAC,MAAM;YACXqF,KAAK,EAAE7F,OAAO,CAACI,MAAO;YACtBmJ,QAAQ,EAAG/C,CAAC,IAAKb,kBAAkB,CAAC,QAAQ,EAAEa,CAAC,CAACC,MAAM,CAACZ,KAAK,CAAE;YAC9DyC,SAAS,EAAC;UAAc;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAENtK,OAAA;UAAKiK,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3BlK,OAAA;YAAOiK,SAAS,EAAC,cAAc;YAAAC,QAAA,EAAC;UAAY;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACpDtK,OAAA;YAAKiK,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC3BlK,OAAA;cACEmC,IAAI,EAAC,QAAQ;cACbqF,KAAK,EAAE7F,OAAO,CAACK,UAAW;cAC1BkJ,QAAQ,EAAG/C,CAAC,IAAKb,kBAAkB,CAAC,YAAY,EAAEa,CAAC,CAACC,MAAM,CAACZ,KAAK,CAAE;cAClE6D,WAAW,EAAC,KAAK;cACjBpB,SAAS,EAAC,2BAA2B;cACrCqB,IAAI,EAAC;YAAM;cAAAnB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACZ,CAAC,eACFtK,OAAA;cAAMiK,SAAS,EAAC,kBAAkB;cAAAC,QAAA,EAAC;YAAC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC3CtK,OAAA;cACEmC,IAAI,EAAC,QAAQ;cACbqF,KAAK,EAAE7F,OAAO,CAACM,QAAS;cACxBiJ,QAAQ,EAAG/C,CAAC,IAAKb,kBAAkB,CAAC,UAAU,EAAEa,CAAC,CAACC,MAAM,CAACZ,KAAK,CAAE;cAChE6D,WAAW,EAAC,KAAK;cACjBpB,SAAS,EAAC,2BAA2B;cACrCqB,IAAI,EAAC;YAAM;cAAAnB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACZ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENtK,OAAA;UAAKiK,SAAS,EAAC,cAAc;UAAAC,QAAA,eAC3BlK,OAAA;YAAQuK,OAAO,EAAEtC,YAAa;YAACgC,SAAS,EAAC,0BAA0B;YAAAC,QAAA,EAAC;UAEpE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNtK,OAAA;MAAKiK,SAAS,EAAC,8BAA8B;MAAAC,QAAA,gBAC3ClK,OAAA;QAAOiK,SAAS,EAAC,oBAAoB;QAAAC,QAAA,gBACnClK,OAAA;UAAAkK,QAAA,eACElK,OAAA;YAAAkK,QAAA,gBACElK,OAAA;cAAIiK,SAAS,EAAC,cAAc;cAAAC,QAAA,eAC1BlK,OAAA;gBACEmC,IAAI,EAAC,UAAU;gBACfiJ,OAAO,EAAEhJ,oBAAoB,CAAC4F,IAAI,KAAKxB,mBAAmB,CAAC5D,MAAM,IAAI4D,mBAAmB,CAAC5D,MAAM,GAAG,CAAE;gBACpGsI,QAAQ,EAAEnD,eAAgB;gBAC1BkC,SAAS,EAAC;cAAgB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC,eACLtK,OAAA;cACEiK,SAAS,EAAE,YAAY1I,SAAS,KAAK,cAAc,GAAG,QAAQ,GAAG,EAAE,EAAG;cACtEgJ,OAAO,EAAEA,CAAA,KAAMnD,UAAU,CAAC,cAAc,CAAE;cAAA8C,QAAA,GAC3C,MAEC,EAAC3I,SAAS,KAAK,cAAc,iBAC3BvB,OAAA;gBAAMiK,SAAS,EAAC,YAAY;gBAAAC,QAAA,EACzBzI,aAAa,KAAK,KAAK,GAAG,GAAG,GAAG;cAAG;gBAAA0I,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChC,CACP;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACLtK,OAAA;cACEiK,SAAS,EAAE,YAAY1I,SAAS,KAAK,aAAa,GAAG,QAAQ,GAAG,EAAE,EAAG;cACrEgJ,OAAO,EAAEA,CAAA,KAAMnD,UAAU,CAAC,aAAa,CAAE;cAAA8C,QAAA,GAC1C,SAEC,EAAC3I,SAAS,KAAK,aAAa,iBAC1BvB,OAAA;gBAAMiK,SAAS,EAAC,YAAY;gBAAAC,QAAA,EACzBzI,aAAa,KAAK,KAAK,GAAG,GAAG,GAAG;cAAG;gBAAA0I,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChC,CACP;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACLtK,OAAA;cACEiK,SAAS,EAAE,YAAY1I,SAAS,KAAK,aAAa,GAAG,QAAQ,GAAG,EAAE,EAAG;cACrEgJ,OAAO,EAAEA,CAAA,KAAMnD,UAAU,CAAC,aAAa,CAAE;cAAA8C,QAAA,GAC1C,aAEC,EAAC3I,SAAS,KAAK,aAAa,iBAC1BvB,OAAA;gBAAMiK,SAAS,EAAC,YAAY;gBAAAC,QAAA,EACzBzI,aAAa,KAAK,KAAK,GAAG,GAAG,GAAG;cAAG;gBAAA0I,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChC,CACP;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACLtK,OAAA;cAAIiK,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAK;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACrCtK,OAAA;cAAIiK,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAM;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACtCtK,OAAA;cACEiK,SAAS,EAAE,uBAAuB1I,SAAS,KAAK,SAAS,GAAG,QAAQ,GAAG,EAAE,EAAG;cAC5EgJ,OAAO,EAAEA,CAAA,KAAMnD,UAAU,CAAC,SAAS,CAAE;cAAA8C,QAAA,GACtC,SAEC,EAAC3I,SAAS,KAAK,SAAS,iBACtBvB,OAAA;gBAAMiK,SAAS,EAAC,YAAY;gBAAAC,QAAA,EACzBzI,aAAa,KAAK,KAAK,GAAG,GAAG,GAAG;cAAG;gBAAA0I,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChC,CACP;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACLtK,OAAA;cAAAkK,QAAA,EAAI;YAAS;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eACRtK,OAAA;UAAAkK,QAAA,EACG1D,mBAAmB,CAACpB,GAAG,CAAC,CAAC0E,WAAW,EAAEyB,MAAM,kBAC3CvL,OAAA;YAEEiK,SAAS,EAAE;AAC3B,oBAAoB7H,oBAAoB,CAACiD,GAAG,CAACyE,WAAW,CAACzG,EAAE,CAAC,GAAG,UAAU,GAAG,EAAE;AAC9E,oBAAoBwG,WAAW,CAACC,WAAW,CAAC,GAAG,WAAW,GAAG,EAAE;AAC/D,iBAAkB;YACF0B,KAAK,EAAE1B,WAAW,CAAC5H,WAAY;YAAAgI,QAAA,gBAE/BlK,OAAA;cAAIiK,SAAS,EAAC,cAAc;cAAAC,QAAA,eAC1BlK,OAAA;gBACEmC,IAAI,EAAC,UAAU;gBACfiJ,OAAO,EAAEhJ,oBAAoB,CAACiD,GAAG,CAACyE,WAAW,CAACzG,EAAE,CAAE;gBAClD6H,QAAQ,EAAEA,CAAA,KAAMxD,uBAAuB,CAACoC,WAAW,CAACzG,EAAE,CAAE;gBACxD4G,SAAS,EAAC;cAAgB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC,eACLtK,OAAA;cAAIiK,SAAS,EAAC,UAAU;cAAAC,QAAA,eACtBlK,OAAA;gBAAKiK,SAAS,EAAC,cAAc;gBAAAC,QAAA,GAC1Bb,UAAU,CAACS,WAAW,CAAC3F,YAAY,CAAC,EACpC2F,WAAW,CAAC2B,IAAI,iBACfzL,OAAA;kBAAMiK,SAAS,EAAC,cAAc;kBAAAC,QAAA,EAAEJ,WAAW,CAAC2B;gBAAI;kBAAAtB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CACxD;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eACLtK,OAAA;cAAIiK,SAAS,EAAC,aAAa;cAAAC,QAAA,eACzBlK,OAAA;gBAAKiK,SAAS,EAAC,cAAc;gBAAAC,QAAA,GAC1BN,cAAc,CAACE,WAAW,CAACjI,SAAS,CAAC,EACrCgI,WAAW,CAACC,WAAW,CAAC,iBACvB9J,OAAA;kBAAMiK,SAAS,EAAC,iBAAiB;kBAAAC,QAAA,EAAC;gBAAS;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAClD;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eACLtK,OAAA;cAAIiK,SAAS,EAAC,iBAAiB;cAAAC,QAAA,eAC7BlK,OAAA;gBAAKiK,SAAS,EAAC,qBAAqB;gBAAAC,QAAA,EACjCJ,WAAW,CAAC5H;cAAW;gBAAAiI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eACLtK,OAAA;cAAIiK,SAAS,EAAC,kBAAkB;cAAAC,QAAA,EAC7BJ,WAAW,CAAC/E,WAAW,GAAGgE,cAAc,CAACe,WAAW,CAAC/E,WAAW,CAAC,GAAG;YAAE;cAAAoF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrE,CAAC,eACLtK,OAAA;cAAIiK,SAAS,EAAC,mBAAmB;cAAAC,QAAA,EAC9BJ,WAAW,CAAC9E,YAAY,GAAG+D,cAAc,CAACe,WAAW,CAAC9E,YAAY,CAAC,GAAG;YAAE;cAAAmF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvE,CAAC,eACLtK,OAAA;cAAIiK,SAAS,EAAC,oBAAoB;cAAAC,QAAA,EAC/BnB,cAAc,CAACe,WAAW,CAAClE,OAAO;YAAC;cAAAuE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClC,CAAC,eACLtK,OAAA;cAAIiK,SAAS,EAAC,eAAe;cAAAC,QAAA,EAC1BJ,WAAW,CAACtF,SAAS,IAAI;YAAE;cAAA2F,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1B,CAAC;UAAA,GA/CAR,WAAW,CAACzG,EAAE;YAAA8G,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAgDjB,CACL;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EAEP9D,mBAAmB,CAAC5D,MAAM,KAAK,CAAC,iBAC/B5C,OAAA;QAAKiK,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAC9BlK,OAAA;UAAKiK,SAAS,EAAC,sBAAsB;UAAAC,QAAA,EAAC;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eAC9CtK,OAAA;UAAAkK,QAAA,EAAI;QAAqB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC9BtK,OAAA;UAAAkK,QAAA,EACGvG,6BAA6B,CAACf,MAAM,KAAK,CAAC,IAAIrC,YAAY,CAACqC,MAAM,KAAK,CAAC,GACpE,yCAAyC,GACzC;QAA6C;UAAAuH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChD,CAAC,EACH3G,6BAA6B,CAACf,MAAM,KAAK,CAAC,IAAIrC,YAAY,CAACqC,MAAM,GAAG,CAAC,iBACpE5C,OAAA;UAAQuK,OAAO,EAAEtC,YAAa;UAACgC,SAAS,EAAC,iBAAiB;UAAAC,QAAA,EAAC;QAE3D;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CACT;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,EAGLnE,UAAU,GAAG,CAAC,iBACbnG,OAAA;MAAKiK,SAAS,EAAC,yBAAyB;MAAAC,QAAA,gBACtClK,OAAA;QAAKiK,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAC9BlK,OAAA;UAAAkK,QAAA,GAAM,UACI,EAAC7D,UAAU,GAAG,CAAC,EAAC,GAAC,EAACxB,IAAI,CAAC0B,GAAG,CAACD,QAAQ,EAAE3C,6BAA6B,CAACf,MAAM,CAAC,EAAC,MAAI,EAACe,6BAA6B,CAACf,MAAM,EAAC,eAC/H;QAAA;UAAAuH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACPtK,OAAA;UAAKiK,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAC7BlK,OAAA;YAAAkK,QAAA,EAAO;UAAe;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAC9BtK,OAAA;YACEwH,KAAK,EAAEnG,YAAa;YACpB6J,QAAQ,EAAG/C,CAAC,IAAK7G,eAAe,CAACoK,MAAM,CAACvD,CAAC,CAACC,MAAM,CAACZ,KAAK,CAAC,CAAE;YACzDyC,SAAS,EAAC,kBAAkB;YAAAC,QAAA,EAE3BjK,sBAAsB,CAACmF,GAAG,CAAC4C,IAAI,iBAC9BhI,OAAA;cAAmBwH,KAAK,EAAEQ,IAAK;cAAAkC,QAAA,EAAElC;YAAI,GAAxBA,IAAI;cAAAmC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAA6B,CAC/C;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACNtK,OAAA;QAAKiK,SAAS,EAAC,qBAAqB;QAAAC,QAAA,gBAClClK,OAAA;UACEuK,OAAO,EAAEA,CAAA,KAAMnJ,cAAc,CAAC,CAAC,CAAE;UACjCqJ,QAAQ,EAAEtJ,WAAW,KAAK,CAAE;UAC5B8I,SAAS,EAAC,0BAA0B;UAAAC,QAAA,EACrC;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTtK,OAAA;UACEuK,OAAO,EAAEA,CAAA,KAAMnJ,cAAc,CAACD,WAAW,GAAG,CAAC,CAAE;UAC/CsJ,QAAQ,EAAEtJ,WAAW,KAAK,CAAE;UAC5B8I,SAAS,EAAC,0BAA0B;UAAAC,QAAA,EACrC;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTtK,OAAA;UAAKiK,SAAS,EAAC,kBAAkB;UAAAC,QAAA,EAC9ByB,KAAK,CAACC,IAAI,CAAC;YAAEhJ,MAAM,EAAEiC,IAAI,CAAC0B,GAAG,CAAC,CAAC,EAAEJ,UAAU;UAAE,CAAC,EAAE,CAAC0F,CAAC,EAAEC,CAAC,KAAK;YACzD,MAAMC,UAAU,GAAGlH,IAAI,CAACmH,GAAG,CAAC,CAAC,EAAEnH,IAAI,CAAC0B,GAAG,CAACJ,UAAU,GAAG,CAAC,EAAEhF,WAAW,GAAG,CAAC,CAAC,CAAC,GAAG2K,CAAC;YAC7E,oBACE9L,OAAA;cAEEuK,OAAO,EAAEA,CAAA,KAAMnJ,cAAc,CAAC2K,UAAU,CAAE;cAC1C9B,SAAS,EAAE,4BAA4B9I,WAAW,KAAK4K,UAAU,GAAG,QAAQ,GAAG,EAAE,EAAG;cAAA7B,QAAA,EAEnF6B;YAAU,GAJNA,UAAU;cAAA5B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAKT,CAAC;UAEb,CAAC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eACNtK,OAAA;UACEuK,OAAO,EAAEA,CAAA,KAAMnJ,cAAc,CAACD,WAAW,GAAG,CAAC,CAAE;UAC/CsJ,QAAQ,EAAEtJ,WAAW,KAAKgF,UAAW;UACrC8D,SAAS,EAAC,0BAA0B;UAAAC,QAAA,EACrC;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTtK,OAAA;UACEuK,OAAO,EAAEA,CAAA,KAAMnJ,cAAc,CAAC+E,UAAU,CAAE;UAC1CsE,QAAQ,EAAEtJ,WAAW,KAAKgF,UAAW;UACrC8D,SAAS,EAAC,0BAA0B;UAAAC,QAAA,EACrC;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,eAGDtK,OAAA;MAAKiK,SAAS,EAAC,oBAAoB;MAAAC,QAAA,eACjClK,OAAA;QAAAkK,QAAA,gBACElK,OAAA;UAAAkK,QAAA,EAAS;QAAkB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAS,CAAC,eACrCtK,OAAA;UAAKiK,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAC7BlK,OAAA;YAAKiK,SAAS,EAAC,eAAe;YAAAC,QAAA,gBAC5BlK,OAAA;cAAAkK,QAAA,EAAK;YAAC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,KAAC,eAAAtK,OAAA;cAAAkK,QAAA,EAAM;YAAa;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpC,CAAC,eACNtK,OAAA;YAAKiK,SAAS,EAAC,eAAe;YAAAC,QAAA,gBAC5BlK,OAAA;cAAAkK,QAAA,EAAK;YAAC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,KAAC,eAAAtK,OAAA;cAAAkK,QAAA,EAAM;YAAS;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChC,CAAC,eACNtK,OAAA;YAAKiK,SAAS,EAAC,eAAe;YAAAC,QAAA,gBAC5BlK,OAAA;cAAAkK,QAAA,EAAK;YAAI;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,KAAC,eAAAtK,OAAA;cAAAkK,QAAA,EAAM;YAAU;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpC,CAAC,eACNtK,OAAA;YAAKiK,SAAS,EAAC,eAAe;YAAAC,QAAA,gBAC5BlK,OAAA;cAAAkK,QAAA,EAAK;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,KAAC,eAAAtK,OAAA;cAAAkK,QAAA,EAAM;YAAS;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC,CAAC,eACNtK,OAAA;YAAKiK,SAAS,EAAC,eAAe;YAAAC,QAAA,gBAC5BlK,OAAA;cAAAkK,QAAA,EAAK;YAAM;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,KAAC,eAAAtK,OAAA;cAAAkK,QAAA,EAAM;YAAY;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxC,CAAC,eACNtK,OAAA;YAAKiK,SAAS,EAAC,eAAe;YAAAC,QAAA,gBAC5BlK,OAAA;cAAAkK,QAAA,EAAK;YAAM;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,KAAC,eAAAtK,OAAA;cAAAkK,QAAA,EAAM;YAAO;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnC,CAAC,eACNtK,OAAA;YAAKiK,SAAS,EAAC,eAAe;YAAAC,QAAA,gBAC5BlK,OAAA;cAAAkK,QAAA,EAAK;YAAM;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,KAAC,eAAAtK,OAAA;cAAAkK,QAAA,EAAM;YAAU;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtC,CAAC,eACNtK,OAAA;YAAKiK,SAAS,EAAC,eAAe;YAAAC,QAAA,gBAC5BlK,OAAA;cAAAkK,QAAA,EAAK;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,KAAC,eAAAtK,OAAA;cAAAkK,QAAA,EAAM;YAAe;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACP,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAChK,EAAA,CAr1BWH,YAAyC;AAAA8L,EAAA,GAAzC9L,YAAyC;AAAA,IAAA8L,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}