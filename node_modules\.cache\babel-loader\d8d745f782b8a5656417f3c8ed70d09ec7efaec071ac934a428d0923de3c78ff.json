{"ast": null, "code": "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { GatherV2 } from '../kernel_names';\nimport { getUndoAxesPermutation } from '../ops/axis_util';\nimport { reshape } from '../ops/reshape';\nimport { stack } from '../ops/stack';\nimport { transpose } from '../ops/transpose';\nimport { unsortedSegmentSum } from '../ops/unsorted_segment_sum';\nimport { parseAxisParam } from '../util';\nexport const gatherGradConfig = {\n  kernelName: GatherV2,\n  inputsToSave: ['x', 'indices'],\n  gradFunc: (dy, saved, attrs) => {\n    const [x, indices] = saved;\n    const {\n      axis,\n      batchDims\n    } = attrs;\n    const parsedAxis = parseAxisParam(axis, x.shape)[0];\n    const derXBatch = (x, indices, dy) => {\n      return () => {\n        const paramsShape = x.shape;\n        const indicesSize = indices.size;\n        const outerShape = paramsShape.slice(0, parsedAxis);\n        const outerDims = outerShape.length;\n        const innerShape = paramsShape.slice(axis, paramsShape.length).slice(1);\n        const innerDims = innerShape.length;\n        const outerAxesIndices = arrayRange(0, outerDims);\n        const innerAxesIndices = arrayRange(outerDims + 1, outerDims + 1 + innerDims);\n        const valuesShape = arrayConcat([outerShape, [indicesSize], innerShape]);\n        const values = reshape(dy, valuesShape);\n        const reshapedIndices = reshape(indices, [indicesSize]);\n        const transposeDims = arrayConcat([[outerDims], outerAxesIndices, innerAxesIndices]);\n        const valuesTranspose = transpose(values, transposeDims);\n        let paramsGrad = unsortedSegmentSum(valuesTranspose, reshapedIndices, x.shape[parsedAxis]);\n        const invertTransposeDims = getUndoAxesPermutation(transposeDims);\n        paramsGrad = transpose(paramsGrad, invertTransposeDims);\n        return paramsGrad;\n      };\n    };\n    if (batchDims === 1) {\n      const batchSize = x.shape[0];\n      const xBatch = x.split(batchSize, 0);\n      const derXBatched = () => {\n        const stacked = stack(xBatch.map((x, i) => {\n          return derXBatch(x, indices.slice(i, 1), dy.slice(i, 1))();\n        }));\n        return stacked.reshape(x.shape);\n      };\n      return {\n        x: derXBatched,\n        indices: () => indices\n      };\n    } else {\n      return {\n        x: derXBatch(x, indices, dy),\n        indices: () => indices\n      };\n    }\n  }\n};\nfunction arrayRange(start, stop) {\n  const result = [];\n  for (let i = start; i < stop; ++i) {\n    result.push(i);\n  }\n  return result;\n}\nfunction arrayConcat(arrays) {\n  const result = [];\n  for (let i = 0; i < arrays.length; ++i) {\n    for (let j = 0; j < arrays[i].length; ++j) {\n      result.push(arrays[i][j]);\n    }\n  }\n  return result;\n}", "map": {"version": 3, "names": ["GatherV2", "getUndoAxesPermutation", "reshape", "stack", "transpose", "unsortedSegmentSum", "parseAxisParam", "gatherGradConfig", "kernelName", "inputsToSave", "grad<PERSON>unc", "dy", "saved", "attrs", "x", "indices", "axis", "batchDims", "parsedAxis", "shape", "derXBatch", "params<PERSON>hape", "indicesSize", "size", "outerShape", "slice", "outerDims", "length", "innerShape", "innerDims", "outerAxesIndices", "arrayRange", "innerAxesIndices", "valuesShape", "arrayConcat", "values", "reshapedIndices", "transposeDims", "valuesTranspose", "paramsGrad", "invertTransposeDims", "batchSize", "xBatch", "split", "derXBatched", "stacked", "map", "i", "start", "stop", "result", "push", "arrays", "j"], "sources": ["C:\\tfjs-core\\src\\gradients\\GatherV2_grad.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {GatherV2, GatherV2Attrs} from '../kernel_names';\nimport {GradConfig, NamedAttrMap} from '../kernel_registry';\nimport {getUndoAxesPermutation} from '../ops/axis_util';\nimport {reshape} from '../ops/reshape';\nimport {stack} from '../ops/stack';\nimport {transpose} from '../ops/transpose';\nimport {unsortedSegmentSum} from '../ops/unsorted_segment_sum';\nimport {Tensor, Tensor1D} from '../tensor';\nimport {parseAxisParam} from '../util';\n\nexport const gatherGradConfig: GradConfig = {\n  kernelName: GatherV2,\n  inputsToSave: ['x', 'indices'],\n  gradFunc: (dy: Tensor, saved: Tensor[], attrs: NamedAttrMap) => {\n    const [x, indices] = saved;\n    const {axis, batchDims} = attrs as unknown as GatherV2Attrs;\n\n    const parsedAxis = parseAxisParam(axis, x.shape)[0];\n\n    const derXBatch = (x: Tensor, indices: Tensor, dy: Tensor) => {\n      return (): Tensor => {\n        const paramsShape = x.shape;\n        const indicesSize = indices.size;\n\n        const outerShape = paramsShape.slice(0, parsedAxis);\n        const outerDims = outerShape.length;\n        const innerShape = paramsShape.slice(axis, paramsShape.length).slice(1);\n        const innerDims = innerShape.length;\n\n        const outerAxesIndices = arrayRange(0, outerDims);\n        const innerAxesIndices =\n            arrayRange(outerDims + 1, outerDims + 1 + innerDims);\n\n        const valuesShape = arrayConcat([outerShape, [indicesSize],\n                                         innerShape]);\n\n        const values = reshape(dy, valuesShape);\n        const reshapedIndices = reshape(indices, [indicesSize]);\n\n        const transposeDims =\n            arrayConcat([[outerDims], outerAxesIndices, innerAxesIndices]);\n        const valuesTranspose = transpose(values, transposeDims);\n        let paramsGrad = unsortedSegmentSum(\n            valuesTranspose, reshapedIndices as Tensor1D, x.shape[parsedAxis]);\n        const invertTransposeDims = getUndoAxesPermutation(transposeDims);\n        paramsGrad = transpose(paramsGrad, invertTransposeDims);\n        return paramsGrad;\n      };\n    };\n\n    if (batchDims === 1) {\n      const batchSize = x.shape[0];\n      const xBatch = x.split(batchSize, 0);\n      const derXBatched = () => {\n        const stacked = stack(\n          xBatch.map((x, i) => {\n            return derXBatch(x, indices.slice(i,1), dy.slice(i,1))();\n          }));\n        return stacked.reshape(x.shape);\n      };\n      return {x: derXBatched, indices: () => indices};\n    } else {\n      return {x: derXBatch(x, indices, dy), indices: () => indices};\n    }\n  }\n};\n\nfunction arrayRange(start: number, stop: number): number[] {\n  const result = [];\n  for (let i = start; i < stop; ++i) {\n    result.push(i);\n  }\n  return result;\n}\n\nfunction arrayConcat(arrays: number[][]): number[] {\n  const result = [];\n  for (let i = 0; i < arrays.length; ++i) {\n    for (let j = 0; j < arrays[i].length; ++j) {\n      result.push(arrays[i][j]);\n    }\n  }\n  return result;\n}\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAiBA,SAAQA,QAAQ,QAAsB,iBAAiB;AAEvD,SAAQC,sBAAsB,QAAO,kBAAkB;AACvD,SAAQC,OAAO,QAAO,gBAAgB;AACtC,SAAQC,KAAK,QAAO,cAAc;AAClC,SAAQC,SAAS,QAAO,kBAAkB;AAC1C,SAAQC,kBAAkB,QAAO,6BAA6B;AAE9D,SAAQC,cAAc,QAAO,SAAS;AAEtC,OAAO,MAAMC,gBAAgB,GAAe;EAC1CC,UAAU,EAAER,QAAQ;EACpBS,YAAY,EAAE,CAAC,GAAG,EAAE,SAAS,CAAC;EAC9BC,QAAQ,EAAEA,CAACC,EAAU,EAAEC,KAAe,EAAEC,KAAmB,KAAI;IAC7D,MAAM,CAACC,CAAC,EAAEC,OAAO,CAAC,GAAGH,KAAK;IAC1B,MAAM;MAACI,IAAI;MAAEC;IAAS,CAAC,GAAGJ,KAAiC;IAE3D,MAAMK,UAAU,GAAGZ,cAAc,CAACU,IAAI,EAAEF,CAAC,CAACK,KAAK,CAAC,CAAC,CAAC,CAAC;IAEnD,MAAMC,SAAS,GAAGA,CAACN,CAAS,EAAEC,OAAe,EAAEJ,EAAU,KAAI;MAC3D,OAAO,MAAa;QAClB,MAAMU,WAAW,GAAGP,CAAC,CAACK,KAAK;QAC3B,MAAMG,WAAW,GAAGP,OAAO,CAACQ,IAAI;QAEhC,MAAMC,UAAU,GAAGH,WAAW,CAACI,KAAK,CAAC,CAAC,EAAEP,UAAU,CAAC;QACnD,MAAMQ,SAAS,GAAGF,UAAU,CAACG,MAAM;QACnC,MAAMC,UAAU,GAAGP,WAAW,CAACI,KAAK,CAACT,IAAI,EAAEK,WAAW,CAACM,MAAM,CAAC,CAACF,KAAK,CAAC,CAAC,CAAC;QACvE,MAAMI,SAAS,GAAGD,UAAU,CAACD,MAAM;QAEnC,MAAMG,gBAAgB,GAAGC,UAAU,CAAC,CAAC,EAAEL,SAAS,CAAC;QACjD,MAAMM,gBAAgB,GAClBD,UAAU,CAACL,SAAS,GAAG,CAAC,EAAEA,SAAS,GAAG,CAAC,GAAGG,SAAS,CAAC;QAExD,MAAMI,WAAW,GAAGC,WAAW,CAAC,CAACV,UAAU,EAAE,CAACF,WAAW,CAAC,EACzBM,UAAU,CAAC,CAAC;QAE7C,MAAMO,MAAM,GAAGjC,OAAO,CAACS,EAAE,EAAEsB,WAAW,CAAC;QACvC,MAAMG,eAAe,GAAGlC,OAAO,CAACa,OAAO,EAAE,CAACO,WAAW,CAAC,CAAC;QAEvD,MAAMe,aAAa,GACfH,WAAW,CAAC,CAAC,CAACR,SAAS,CAAC,EAAEI,gBAAgB,EAAEE,gBAAgB,CAAC,CAAC;QAClE,MAAMM,eAAe,GAAGlC,SAAS,CAAC+B,MAAM,EAAEE,aAAa,CAAC;QACxD,IAAIE,UAAU,GAAGlC,kBAAkB,CAC/BiC,eAAe,EAAEF,eAA2B,EAAEtB,CAAC,CAACK,KAAK,CAACD,UAAU,CAAC,CAAC;QACtE,MAAMsB,mBAAmB,GAAGvC,sBAAsB,CAACoC,aAAa,CAAC;QACjEE,UAAU,GAAGnC,SAAS,CAACmC,UAAU,EAAEC,mBAAmB,CAAC;QACvD,OAAOD,UAAU;MACnB,CAAC;IACH,CAAC;IAED,IAAItB,SAAS,KAAK,CAAC,EAAE;MACnB,MAAMwB,SAAS,GAAG3B,CAAC,CAACK,KAAK,CAAC,CAAC,CAAC;MAC5B,MAAMuB,MAAM,GAAG5B,CAAC,CAAC6B,KAAK,CAACF,SAAS,EAAE,CAAC,CAAC;MACpC,MAAMG,WAAW,GAAGA,CAAA,KAAK;QACvB,MAAMC,OAAO,GAAG1C,KAAK,CACnBuC,MAAM,CAACI,GAAG,CAAC,CAAChC,CAAC,EAAEiC,CAAC,KAAI;UAClB,OAAO3B,SAAS,CAACN,CAAC,EAAEC,OAAO,CAACU,KAAK,CAACsB,CAAC,EAAC,CAAC,CAAC,EAAEpC,EAAE,CAACc,KAAK,CAACsB,CAAC,EAAC,CAAC,CAAC,CAAC,EAAE;QAC1D,CAAC,CAAC,CAAC;QACL,OAAOF,OAAO,CAAC3C,OAAO,CAACY,CAAC,CAACK,KAAK,CAAC;MACjC,CAAC;MACD,OAAO;QAACL,CAAC,EAAE8B,WAAW;QAAE7B,OAAO,EAAEA,CAAA,KAAMA;MAAO,CAAC;KAChD,MAAM;MACL,OAAO;QAACD,CAAC,EAAEM,SAAS,CAACN,CAAC,EAAEC,OAAO,EAAEJ,EAAE,CAAC;QAAEI,OAAO,EAAEA,CAAA,KAAMA;MAAO,CAAC;;EAEjE;CACD;AAED,SAASgB,UAAUA,CAACiB,KAAa,EAAEC,IAAY;EAC7C,MAAMC,MAAM,GAAG,EAAE;EACjB,KAAK,IAAIH,CAAC,GAAGC,KAAK,EAAED,CAAC,GAAGE,IAAI,EAAE,EAAEF,CAAC,EAAE;IACjCG,MAAM,CAACC,IAAI,CAACJ,CAAC,CAAC;;EAEhB,OAAOG,MAAM;AACf;AAEA,SAAShB,WAAWA,CAACkB,MAAkB;EACrC,MAAMF,MAAM,GAAG,EAAE;EACjB,KAAK,IAAIH,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGK,MAAM,CAACzB,MAAM,EAAE,EAAEoB,CAAC,EAAE;IACtC,KAAK,IAAIM,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,MAAM,CAACL,CAAC,CAAC,CAACpB,MAAM,EAAE,EAAE0B,CAAC,EAAE;MACzCH,MAAM,CAACC,IAAI,CAACC,MAAM,CAACL,CAAC,CAAC,CAACM,CAAC,CAAC,CAAC;;;EAG7B,OAAOH,MAAM;AACf", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}