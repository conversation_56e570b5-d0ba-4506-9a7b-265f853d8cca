{"ast": null, "code": "/**\n * @license\n * Copyright 2022 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { op } from './operation';\nimport { randomNormal } from './random_normal';\n/**\n * Creates a `tf.Tensor` with values sampled from a normal distribution.\n *\n * The generated values will have mean 0 and standard deviation 1.\n *\n * ```js\n * tf.randomStandardNormal([2, 2]).print();\n * ```\n *\n * @param shape An array of integers defining the output tensor shape.\n * @param dtype The data type of the output.\n * @param seed The seed for the random number generator.\n *\n * @doc {heading: 'Tensors', subheading: 'Random'}\n */\nfunction randomStandardNormal_(shape, dtype, seed) {\n  if (dtype != null && dtype === 'bool') {\n    throw new Error(\"Unsupported data type \".concat(dtype));\n  }\n  return randomNormal(shape, 0, 1, dtype, seed);\n}\nexport const randomStandardNormal = /* @__PURE__ */op({\n  randomStandardNormal_\n});", "map": {"version": 3, "names": ["op", "randomNormal", "randomStandardNormal_", "shape", "dtype", "seed", "Error", "concat", "randomStandardNormal"], "sources": ["C:\\tfjs-core\\src\\ops\\random_standard_normal.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2022 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {Tensor} from '../tensor';\nimport {DataType, Rank, ShapeMap} from '../types';\n\nimport {op} from './operation';\nimport {randomNormal} from './random_normal';\n\n/**\n * Creates a `tf.Tensor` with values sampled from a normal distribution.\n *\n * The generated values will have mean 0 and standard deviation 1.\n *\n * ```js\n * tf.randomStandardNormal([2, 2]).print();\n * ```\n *\n * @param shape An array of integers defining the output tensor shape.\n * @param dtype The data type of the output.\n * @param seed The seed for the random number generator.\n *\n * @doc {heading: 'Tensors', subheading: 'Random'}\n */\nfunction randomStandardNormal_<R extends Rank>(\n    shape: ShapeMap[R], dtype?: 'float32'|'int32', seed?: number): Tensor<R> {\n  if (dtype != null && (dtype as DataType) === 'bool') {\n    throw new Error(`Unsupported data type ${dtype}`);\n  }\n  return randomNormal(shape, 0, 1, dtype, seed);\n}\n\nexport const randomStandardNormal = /* @__PURE__ */ op({randomStandardNormal_});\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAoBA,SAAQA,EAAE,QAAO,aAAa;AAC9B,SAAQC,YAAY,QAAO,iBAAiB;AAE5C;;;;;;;;;;;;;;;AAeA,SAASC,qBAAqBA,CAC1BC,KAAkB,EAAEC,KAAyB,EAAEC,IAAa;EAC9D,IAAID,KAAK,IAAI,IAAI,IAAKA,KAAkB,KAAK,MAAM,EAAE;IACnD,MAAM,IAAIE,KAAK,0BAAAC,MAAA,CAA0BH,KAAK,CAAE,CAAC;;EAEnD,OAAOH,YAAY,CAACE,KAAK,EAAE,CAAC,EAAE,CAAC,EAAEC,KAAK,EAAEC,IAAI,CAAC;AAC/C;AAEA,OAAO,MAAMG,oBAAoB,GAAG,eAAgBR,EAAE,CAAC;EAACE;AAAqB,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}