{"ast": null, "code": "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { env, TopK, util } from '@tensorflow/tfjs-core';\nimport { topKImplCPU } from '../kernel_utils/shared';\nimport { MergeProgram, SwapProgram } from '../top_k_gpu';\nimport { fill } from './Fill';\nimport { gatherV2 } from './GatherV2';\nimport { reshape } from './Reshape';\nimport { slice } from './Slice';\nfunction disposeIntermediateTensorInfoOrNull(backend, tensorInfo) {\n  if (tensorInfo !== null) {\n    backend.disposeIntermediateTensorInfo(tensorInfo);\n  }\n}\nfunction roundUpToPow2(num) {\n  let pow2 = 1;\n  while (pow2 < num) {\n    pow2 *= 2;\n  }\n  return pow2;\n}\n// Based on Algorithm 2 of Bitonic Top K, ref:\n// https://anilshanbhag.in/static/papers/gputopk_sigmod18.pdf\nexport function topK(args) {\n  const {\n    inputs,\n    backend,\n    attrs\n  } = args;\n  const {\n    x\n  } = inputs;\n  const {\n    k,\n    sorted\n  } = attrs;\n  // Empirically determined constant used to determine last dim threshold for\n  // handing off execution to the CPU.\n  const TOPK_LAST_DIM_CPU_HANDOFF_SIZE_THRESHOLD = env().getNumber('TOPK_LAST_DIM_CPU_HANDOFF_SIZE_THRESHOLD');\n  // Empirically determined constant used to determine k threshold for handing\n  // off execution to the CPU.\n  const TOPK_K_CPU_HANDOFF_THRESHOLD = env().getNumber('TOPK_K_CPU_HANDOFF_THRESHOLD');\n  const xShape = x.shape;\n  const lastDim = xShape[xShape.length - 1];\n  if (backend.shouldExecuteOnCPU([x]) || lastDim < TOPK_LAST_DIM_CPU_HANDOFF_SIZE_THRESHOLD || k > TOPK_K_CPU_HANDOFF_THRESHOLD) {\n    const xVals = backend.readSync(x.dataId);\n    const [allTopKVals, allTopKIndices] = topKImplCPU(xVals, xShape, x.dtype, k, sorted);\n    return [backend.makeTensorInfo(allTopKVals.shape, allTopKVals.dtype, allTopKVals.values), backend.makeTensorInfo(allTopKIndices.shape, allTopKIndices.dtype, allTopKIndices.values)];\n  }\n  if (k === 0) {\n    xShape[xShape.length - 1] = 0;\n    return [backend.makeTensorInfo(xShape, x.dtype, []), backend.makeTensorInfo(xShape, 'int32', [])];\n  }\n  if (lastDim === 1 /* firstPass */) {\n    return [x, fill({\n      attrs: {\n        shape: xShape,\n        dtype: 'int32',\n        value: 0\n      },\n      backend\n    })];\n  }\n  // Eagerly unpack x input since it is passed in to all the shaders which\n  // require unpacked inputs.\n  const xtexData = backend.texData.get(x.dataId);\n  const xIsPacked = xtexData !== null && xtexData.isPacked;\n  const xUnPacked = xIsPacked ? backend.unpackTensor(x) : x;\n  // Reshape into a 2d tensor [batch, lastDim] and compute topk along lastDim.\n  const xSize = util.sizeFromShape(xShape);\n  const batch = xSize / lastDim;\n  const x2D = reshape({\n    inputs: {\n      x: xUnPacked\n    },\n    attrs: {\n      shape: [batch, lastDim]\n    },\n    backend\n  });\n  if (xIsPacked) {\n    disposeIntermediateTensorInfoOrNull(backend, xUnPacked);\n  }\n  const kPow2 = roundUpToPow2(k);\n  const lastDimPow2 = roundUpToPow2(lastDim);\n  // Only the indices containing the top K are kept at every step to reduce\n  // number of outputs in the GPU algorithms, so once the final set of indices\n  // is computed then gather is used to grab the corresponding values\n  // from the original input.\n  let indices = null;\n  // GPU algorithm always takes in an indices input but this input is not used\n  // on the first run of a GPU algorithm, therefore if indices is null we simply\n  // pass in x2D instead of it but the value will not actually be used\n  const getInputs = () => indices === null ? [x2D, x2D] : [x2D, indices];\n  const runSwap = (dir, inc, shape) => {\n    const inputs = getInputs();\n    const program = new SwapProgram(shape);\n    const fistPass = indices === null ? 1 : 0;\n    const customValues = [[lastDim], [fistPass], [Number.NEGATIVE_INFINITY], [dir], [inc]];\n    const prevIndices = indices;\n    indices = backend.runWebGLProgram(program, inputs, 'int32', customValues);\n    disposeIntermediateTensorInfoOrNull(backend, prevIndices);\n  };\n  // Step 1: local sort\n  for (let len = 1; len < kPow2; len *= 2) {\n    const dir = len * 2;\n    for (let inc = len; inc >= 1; inc /= 2) {\n      runSwap(dir, inc, [batch, lastDimPow2]);\n    }\n  }\n  // Step 2: merge\n  for (let indicesSize = lastDimPow2; indicesSize > kPow2; indicesSize /= 2) {\n    const inputs = getInputs();\n    const mergeProgram = new MergeProgram([batch, indicesSize / 2]);\n    const firstPass = indices === null ? 1 : 0;\n    const customValues = [[lastDim], [firstPass], [kPow2]];\n    const prevIndices = indices;\n    indices = backend.runWebGLProgram(mergeProgram, inputs, 'int32', customValues);\n    disposeIntermediateTensorInfoOrNull(backend, prevIndices);\n    // Step 3: rebuild\n    const len = kPow2 / 2;\n    const dir = len * 2;\n    for (let inc = len; inc >= 1; inc /= 2) {\n      runSwap(dir, inc, indices.shape);\n    }\n  }\n  // Keep only the requested top K results instead of kPow2\n  let prevIndices = indices;\n  indices = slice({\n    inputs: {\n      x: indices\n    },\n    backend,\n    attrs: {\n      begin: 0,\n      size: [batch, k]\n    }\n  });\n  disposeIntermediateTensorInfoOrNull(backend, prevIndices);\n  // Gather values on last dimension\n  let values = gatherV2({\n    inputs: {\n      x: x2D,\n      indices\n    },\n    backend,\n    attrs: {\n      axis: 1,\n      batchDims: 1\n    }\n  });\n  disposeIntermediateTensorInfoOrNull(backend, x2D);\n  // Reshape back to the original input shape, except that the last\n  // dimension is k.\n  const newShape = xShape.slice(0, -1);\n  newShape.push(k);\n  prevIndices = indices;\n  indices = reshape({\n    inputs: {\n      x: indices\n    },\n    attrs: {\n      shape: newShape\n    },\n    backend\n  });\n  disposeIntermediateTensorInfoOrNull(backend, prevIndices);\n  const prevValues = values;\n  values = reshape({\n    inputs: {\n      x: values\n    },\n    attrs: {\n      shape: newShape\n    },\n    backend\n  });\n  disposeIntermediateTensorInfoOrNull(backend, prevValues);\n  return [values, indices];\n}\nexport const topKConfig = {\n  kernelName: TopK,\n  backendName: 'webgl',\n  kernelFunc: topK\n};", "map": {"version": 3, "names": ["env", "TopK", "util", "topKImplCPU", "MergeProgram", "SwapProgram", "fill", "gatherV2", "reshape", "slice", "disposeIntermediateTensorInfoOrNull", "backend", "tensorInfo", "disposeIntermediateTensorInfo", "roundUpToPow2", "num", "pow2", "topK", "args", "inputs", "attrs", "x", "k", "sorted", "TOPK_LAST_DIM_CPU_HANDOFF_SIZE_THRESHOLD", "getNumber", "TOPK_K_CPU_HANDOFF_THRESHOLD", "xShape", "shape", "lastDim", "length", "shouldExecuteOnCPU", "xVals", "readSync", "dataId", "allTopKVals", "allTopKIndices", "dtype", "makeTensorInfo", "values", "value", "xtexData", "texData", "get", "xIsPacked", "isPacked", "xUnPacked", "unpackTensor", "xSize", "sizeFromShape", "batch", "x2D", "kPow2", "lastDimPow2", "indices", "getInputs", "runSwap", "dir", "inc", "program", "fistPass", "customValues", "Number", "NEGATIVE_INFINITY", "prevIndices", "runWebGLProgram", "len", "indicesSize", "mergeProgram", "firstPass", "begin", "size", "axis", "batchDims", "newShape", "push", "prevV<PERSON><PERSON>", "topKConfig", "kernelName", "backendName", "kernelFunc"], "sources": ["C:\\tfjs-backend-webgl\\src\\kernels\\TopK.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {env, KernelConfig, KernelFunc, NumericDataType, TensorInfo, TopK, TopKAttrs, TopKInputs, TypedArray, util} from '@tensorflow/tfjs-core';\n\nimport {MathBackendWebGL} from '../backend_webgl';\nimport {topKImplCPU} from '../kernel_utils/shared';\nimport {MergeProgram, SwapProgram} from '../top_k_gpu';\nimport {fill} from './Fill';\nimport {gatherV2} from './GatherV2';\nimport {reshape} from './Reshape';\nimport {slice} from './Slice';\n\nfunction disposeIntermediateTensorInfoOrNull(\n    backend: MathBackendWebGL, tensorInfo: TensorInfo) {\n  if (tensorInfo !== null) {\n    backend.disposeIntermediateTensorInfo(tensorInfo);\n  }\n}\n\nfunction roundUpToPow2(num: number) {\n  let pow2 = 1;\n  while (pow2 < num) {\n    pow2 *= 2;\n  }\n  return pow2;\n}\n\n// Based on Algorithm 2 of Bitonic Top K, ref:\n// https://anilshanbhag.in/static/papers/gputopk_sigmod18.pdf\nexport function topK(\n    args: {inputs: TopKInputs, backend: MathBackendWebGL, attrs: TopKAttrs}):\n    TensorInfo[] {\n  const {inputs, backend, attrs} = args;\n  const {x} = inputs;\n  const {k, sorted} = attrs;\n\n  // Empirically determined constant used to determine last dim threshold for\n  // handing off execution to the CPU.\n  const TOPK_LAST_DIM_CPU_HANDOFF_SIZE_THRESHOLD =\n      env().getNumber('TOPK_LAST_DIM_CPU_HANDOFF_SIZE_THRESHOLD');\n\n  // Empirically determined constant used to determine k threshold for handing\n  // off execution to the CPU.\n  const TOPK_K_CPU_HANDOFF_THRESHOLD =\n      env().getNumber('TOPK_K_CPU_HANDOFF_THRESHOLD');\n\n  const xShape = x.shape;\n  const lastDim = xShape[xShape.length - 1];\n\n  if (backend.shouldExecuteOnCPU([x]) ||\n      lastDim < TOPK_LAST_DIM_CPU_HANDOFF_SIZE_THRESHOLD ||\n      k > TOPK_K_CPU_HANDOFF_THRESHOLD) {\n    const xVals = backend.readSync(x.dataId) as TypedArray;\n    const [allTopKVals, allTopKIndices] =\n        topKImplCPU(xVals, xShape, x.dtype as NumericDataType, k, sorted);\n\n    return [\n      backend.makeTensorInfo(\n          allTopKVals.shape, allTopKVals.dtype, allTopKVals.values),\n      backend.makeTensorInfo(\n          allTopKIndices.shape, allTopKIndices.dtype, allTopKIndices.values)\n    ];\n  }\n\n  if (k === 0) {\n    xShape[xShape.length - 1] = 0;\n    return [\n      backend.makeTensorInfo(xShape, x.dtype, []),\n      backend.makeTensorInfo(xShape, 'int32', [])\n    ];\n  }\n\n  if (lastDim === 1 /* firstPass */) {\n    return [\n      x, fill({attrs: {shape: xShape, dtype: 'int32', value: 0}, backend})\n    ];\n  }\n\n  // Eagerly unpack x input since it is passed in to all the shaders which\n  // require unpacked inputs.\n  const xtexData = backend.texData.get(x.dataId);\n  const xIsPacked = xtexData !== null && xtexData.isPacked;\n  const xUnPacked = xIsPacked ? backend.unpackTensor(x) : x;\n\n  // Reshape into a 2d tensor [batch, lastDim] and compute topk along lastDim.\n  const xSize = util.sizeFromShape(xShape);\n  const batch = xSize / lastDim;\n  const x2D = reshape(\n      {inputs: {x: xUnPacked}, attrs: {shape: [batch, lastDim]}, backend});\n\n  if (xIsPacked) {\n    disposeIntermediateTensorInfoOrNull(backend, xUnPacked);\n  }\n\n  const kPow2 = roundUpToPow2(k);\n  const lastDimPow2 = roundUpToPow2(lastDim);\n\n  // Only the indices containing the top K are kept at every step to reduce\n  // number of outputs in the GPU algorithms, so once the final set of indices\n  // is computed then gather is used to grab the corresponding values\n  // from the original input.\n  let indices: TensorInfo = null;\n\n  // GPU algorithm always takes in an indices input but this input is not used\n  // on the first run of a GPU algorithm, therefore if indices is null we simply\n  // pass in x2D instead of it but the value will not actually be used\n  const getInputs = () => indices === null ? [x2D, x2D] : [x2D, indices];\n\n  const runSwap = (dir: number, inc: number, shape: number[]) => {\n    const inputs = getInputs();\n    const program = new SwapProgram(shape);\n    const fistPass = indices === null ? 1 : 0;\n    const customValues =\n        [[lastDim], [fistPass], [Number.NEGATIVE_INFINITY], [dir], [inc]];\n    const prevIndices = indices;\n    indices = backend.runWebGLProgram(program, inputs, 'int32', customValues);\n    disposeIntermediateTensorInfoOrNull(backend, prevIndices);\n  };\n\n  // Step 1: local sort\n  for (let len = 1; len < kPow2; len *= 2) {\n    const dir = len * 2;\n    for (let inc = len; inc >= 1; inc /= 2) {\n      runSwap(dir, inc, [batch, lastDimPow2]);\n    }\n  }\n\n  // Step 2: merge\n  for (let indicesSize = lastDimPow2; indicesSize > kPow2; indicesSize /= 2) {\n    const inputs = getInputs();\n    const mergeProgram = new MergeProgram([batch, indicesSize / 2]);\n    const firstPass = indices === null ? 1 : 0;\n    const customValues = [[lastDim], [firstPass], [kPow2]];\n    const prevIndices = indices;\n    indices =\n        backend.runWebGLProgram(mergeProgram, inputs, 'int32', customValues);\n    disposeIntermediateTensorInfoOrNull(backend, prevIndices);\n\n    // Step 3: rebuild\n    const len = kPow2 / 2;\n    const dir = len * 2;\n    for (let inc = len; inc >= 1; inc /= 2) {\n      runSwap(dir, inc, indices.shape);\n    }\n  }\n\n  // Keep only the requested top K results instead of kPow2\n  let prevIndices = indices;\n  indices = slice(\n      {inputs: {x: indices}, backend, attrs: {begin: 0, size: [batch, k]}});\n  disposeIntermediateTensorInfoOrNull(backend, prevIndices);\n\n  // Gather values on last dimension\n  let values = gatherV2(\n      {inputs: {x: x2D, indices}, backend, attrs: {axis: 1, batchDims: 1}});\n  disposeIntermediateTensorInfoOrNull(backend, x2D);\n\n  // Reshape back to the original input shape, except that the last\n  // dimension is k.\n  const newShape = xShape.slice(0, -1);\n  newShape.push(k);\n\n  prevIndices = indices;\n  indices = reshape({inputs: {x: indices}, attrs: {shape: newShape}, backend});\n  disposeIntermediateTensorInfoOrNull(backend, prevIndices);\n\n  const prevValues = values;\n  values = reshape({inputs: {x: values}, attrs: {shape: newShape}, backend});\n  disposeIntermediateTensorInfoOrNull(backend, prevValues);\n\n  return [values, indices];\n}\n\nexport const topKConfig: KernelConfig = {\n  kernelName: TopK,\n  backendName: 'webgl',\n  kernelFunc: topK as unknown as KernelFunc\n};\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAiBA,SAAQA,GAAG,EAAyDC,IAAI,EAAqCC,IAAI,QAAO,uBAAuB;AAG/I,SAAQC,WAAW,QAAO,wBAAwB;AAClD,SAAQC,YAAY,EAAEC,WAAW,QAAO,cAAc;AACtD,SAAQC,IAAI,QAAO,QAAQ;AAC3B,SAAQC,QAAQ,QAAO,YAAY;AACnC,SAAQC,OAAO,QAAO,WAAW;AACjC,SAAQC,KAAK,QAAO,SAAS;AAE7B,SAASC,mCAAmCA,CACxCC,OAAyB,EAAEC,UAAsB;EACnD,IAAIA,UAAU,KAAK,IAAI,EAAE;IACvBD,OAAO,CAACE,6BAA6B,CAACD,UAAU,CAAC;;AAErD;AAEA,SAASE,aAAaA,CAACC,GAAW;EAChC,IAAIC,IAAI,GAAG,CAAC;EACZ,OAAOA,IAAI,GAAGD,GAAG,EAAE;IACjBC,IAAI,IAAI,CAAC;;EAEX,OAAOA,IAAI;AACb;AAEA;AACA;AACA,OAAM,SAAUC,IAAIA,CAChBC,IAAuE;EAEzE,MAAM;IAACC,MAAM;IAAER,OAAO;IAAES;EAAK,CAAC,GAAGF,IAAI;EACrC,MAAM;IAACG;EAAC,CAAC,GAAGF,MAAM;EAClB,MAAM;IAACG,CAAC;IAAEC;EAAM,CAAC,GAAGH,KAAK;EAEzB;EACA;EACA,MAAMI,wCAAwC,GAC1CxB,GAAG,EAAE,CAACyB,SAAS,CAAC,0CAA0C,CAAC;EAE/D;EACA;EACA,MAAMC,4BAA4B,GAC9B1B,GAAG,EAAE,CAACyB,SAAS,CAAC,8BAA8B,CAAC;EAEnD,MAAME,MAAM,GAAGN,CAAC,CAACO,KAAK;EACtB,MAAMC,OAAO,GAAGF,MAAM,CAACA,MAAM,CAACG,MAAM,GAAG,CAAC,CAAC;EAEzC,IAAInB,OAAO,CAACoB,kBAAkB,CAAC,CAACV,CAAC,CAAC,CAAC,IAC/BQ,OAAO,GAAGL,wCAAwC,IAClDF,CAAC,GAAGI,4BAA4B,EAAE;IACpC,MAAMM,KAAK,GAAGrB,OAAO,CAACsB,QAAQ,CAACZ,CAAC,CAACa,MAAM,CAAe;IACtD,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAC/BjC,WAAW,CAAC6B,KAAK,EAAEL,MAAM,EAAEN,CAAC,CAACgB,KAAwB,EAAEf,CAAC,EAAEC,MAAM,CAAC;IAErE,OAAO,CACLZ,OAAO,CAAC2B,cAAc,CAClBH,WAAW,CAACP,KAAK,EAAEO,WAAW,CAACE,KAAK,EAAEF,WAAW,CAACI,MAAM,CAAC,EAC7D5B,OAAO,CAAC2B,cAAc,CAClBF,cAAc,CAACR,KAAK,EAAEQ,cAAc,CAACC,KAAK,EAAED,cAAc,CAACG,MAAM,CAAC,CACvE;;EAGH,IAAIjB,CAAC,KAAK,CAAC,EAAE;IACXK,MAAM,CAACA,MAAM,CAACG,MAAM,GAAG,CAAC,CAAC,GAAG,CAAC;IAC7B,OAAO,CACLnB,OAAO,CAAC2B,cAAc,CAACX,MAAM,EAAEN,CAAC,CAACgB,KAAK,EAAE,EAAE,CAAC,EAC3C1B,OAAO,CAAC2B,cAAc,CAACX,MAAM,EAAE,OAAO,EAAE,EAAE,CAAC,CAC5C;;EAGH,IAAIE,OAAO,KAAK,CAAC,CAAC,iBAAiB;IACjC,OAAO,CACLR,CAAC,EAAEf,IAAI,CAAC;MAACc,KAAK,EAAE;QAACQ,KAAK,EAAED,MAAM;QAAEU,KAAK,EAAE,OAAO;QAAEG,KAAK,EAAE;MAAC,CAAC;MAAE7B;IAAO,CAAC,CAAC,CACrE;;EAGH;EACA;EACA,MAAM8B,QAAQ,GAAG9B,OAAO,CAAC+B,OAAO,CAACC,GAAG,CAACtB,CAAC,CAACa,MAAM,CAAC;EAC9C,MAAMU,SAAS,GAAGH,QAAQ,KAAK,IAAI,IAAIA,QAAQ,CAACI,QAAQ;EACxD,MAAMC,SAAS,GAAGF,SAAS,GAAGjC,OAAO,CAACoC,YAAY,CAAC1B,CAAC,CAAC,GAAGA,CAAC;EAEzD;EACA,MAAM2B,KAAK,GAAG9C,IAAI,CAAC+C,aAAa,CAACtB,MAAM,CAAC;EACxC,MAAMuB,KAAK,GAAGF,KAAK,GAAGnB,OAAO;EAC7B,MAAMsB,GAAG,GAAG3C,OAAO,CACf;IAACW,MAAM,EAAE;MAACE,CAAC,EAAEyB;IAAS,CAAC;IAAE1B,KAAK,EAAE;MAACQ,KAAK,EAAE,CAACsB,KAAK,EAAErB,OAAO;IAAC,CAAC;IAAElB;EAAO,CAAC,CAAC;EAExE,IAAIiC,SAAS,EAAE;IACblC,mCAAmC,CAACC,OAAO,EAAEmC,SAAS,CAAC;;EAGzD,MAAMM,KAAK,GAAGtC,aAAa,CAACQ,CAAC,CAAC;EAC9B,MAAM+B,WAAW,GAAGvC,aAAa,CAACe,OAAO,CAAC;EAE1C;EACA;EACA;EACA;EACA,IAAIyB,OAAO,GAAe,IAAI;EAE9B;EACA;EACA;EACA,MAAMC,SAAS,GAAGA,CAAA,KAAMD,OAAO,KAAK,IAAI,GAAG,CAACH,GAAG,EAAEA,GAAG,CAAC,GAAG,CAACA,GAAG,EAAEG,OAAO,CAAC;EAEtE,MAAME,OAAO,GAAGA,CAACC,GAAW,EAAEC,GAAW,EAAE9B,KAAe,KAAI;IAC5D,MAAMT,MAAM,GAAGoC,SAAS,EAAE;IAC1B,MAAMI,OAAO,GAAG,IAAItD,WAAW,CAACuB,KAAK,CAAC;IACtC,MAAMgC,QAAQ,GAAGN,OAAO,KAAK,IAAI,GAAG,CAAC,GAAG,CAAC;IACzC,MAAMO,YAAY,GACd,CAAC,CAAChC,OAAO,CAAC,EAAE,CAAC+B,QAAQ,CAAC,EAAE,CAACE,MAAM,CAACC,iBAAiB,CAAC,EAAE,CAACN,GAAG,CAAC,EAAE,CAACC,GAAG,CAAC,CAAC;IACrE,MAAMM,WAAW,GAAGV,OAAO;IAC3BA,OAAO,GAAG3C,OAAO,CAACsD,eAAe,CAACN,OAAO,EAAExC,MAAM,EAAE,OAAO,EAAE0C,YAAY,CAAC;IACzEnD,mCAAmC,CAACC,OAAO,EAAEqD,WAAW,CAAC;EAC3D,CAAC;EAED;EACA,KAAK,IAAIE,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAGd,KAAK,EAAEc,GAAG,IAAI,CAAC,EAAE;IACvC,MAAMT,GAAG,GAAGS,GAAG,GAAG,CAAC;IACnB,KAAK,IAAIR,GAAG,GAAGQ,GAAG,EAAER,GAAG,IAAI,CAAC,EAAEA,GAAG,IAAI,CAAC,EAAE;MACtCF,OAAO,CAACC,GAAG,EAAEC,GAAG,EAAE,CAACR,KAAK,EAAEG,WAAW,CAAC,CAAC;;;EAI3C;EACA,KAAK,IAAIc,WAAW,GAAGd,WAAW,EAAEc,WAAW,GAAGf,KAAK,EAAEe,WAAW,IAAI,CAAC,EAAE;IACzE,MAAMhD,MAAM,GAAGoC,SAAS,EAAE;IAC1B,MAAMa,YAAY,GAAG,IAAIhE,YAAY,CAAC,CAAC8C,KAAK,EAAEiB,WAAW,GAAG,CAAC,CAAC,CAAC;IAC/D,MAAME,SAAS,GAAGf,OAAO,KAAK,IAAI,GAAG,CAAC,GAAG,CAAC;IAC1C,MAAMO,YAAY,GAAG,CAAC,CAAChC,OAAO,CAAC,EAAE,CAACwC,SAAS,CAAC,EAAE,CAACjB,KAAK,CAAC,CAAC;IACtD,MAAMY,WAAW,GAAGV,OAAO;IAC3BA,OAAO,GACH3C,OAAO,CAACsD,eAAe,CAACG,YAAY,EAAEjD,MAAM,EAAE,OAAO,EAAE0C,YAAY,CAAC;IACxEnD,mCAAmC,CAACC,OAAO,EAAEqD,WAAW,CAAC;IAEzD;IACA,MAAME,GAAG,GAAGd,KAAK,GAAG,CAAC;IACrB,MAAMK,GAAG,GAAGS,GAAG,GAAG,CAAC;IACnB,KAAK,IAAIR,GAAG,GAAGQ,GAAG,EAAER,GAAG,IAAI,CAAC,EAAEA,GAAG,IAAI,CAAC,EAAE;MACtCF,OAAO,CAACC,GAAG,EAAEC,GAAG,EAAEJ,OAAO,CAAC1B,KAAK,CAAC;;;EAIpC;EACA,IAAIoC,WAAW,GAAGV,OAAO;EACzBA,OAAO,GAAG7C,KAAK,CACX;IAACU,MAAM,EAAE;MAACE,CAAC,EAAEiC;IAAO,CAAC;IAAE3C,OAAO;IAAES,KAAK,EAAE;MAACkD,KAAK,EAAE,CAAC;MAAEC,IAAI,EAAE,CAACrB,KAAK,EAAE5B,CAAC;IAAC;EAAC,CAAC,CAAC;EACzEZ,mCAAmC,CAACC,OAAO,EAAEqD,WAAW,CAAC;EAEzD;EACA,IAAIzB,MAAM,GAAGhC,QAAQ,CACjB;IAACY,MAAM,EAAE;MAACE,CAAC,EAAE8B,GAAG;MAAEG;IAAO,CAAC;IAAE3C,OAAO;IAAES,KAAK,EAAE;MAACoD,IAAI,EAAE,CAAC;MAAEC,SAAS,EAAE;IAAC;EAAC,CAAC,CAAC;EACzE/D,mCAAmC,CAACC,OAAO,EAAEwC,GAAG,CAAC;EAEjD;EACA;EACA,MAAMuB,QAAQ,GAAG/C,MAAM,CAAClB,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EACpCiE,QAAQ,CAACC,IAAI,CAACrD,CAAC,CAAC;EAEhB0C,WAAW,GAAGV,OAAO;EACrBA,OAAO,GAAG9C,OAAO,CAAC;IAACW,MAAM,EAAE;MAACE,CAAC,EAAEiC;IAAO,CAAC;IAAElC,KAAK,EAAE;MAACQ,KAAK,EAAE8C;IAAQ,CAAC;IAAE/D;EAAO,CAAC,CAAC;EAC5ED,mCAAmC,CAACC,OAAO,EAAEqD,WAAW,CAAC;EAEzD,MAAMY,UAAU,GAAGrC,MAAM;EACzBA,MAAM,GAAG/B,OAAO,CAAC;IAACW,MAAM,EAAE;MAACE,CAAC,EAAEkB;IAAM,CAAC;IAAEnB,KAAK,EAAE;MAACQ,KAAK,EAAE8C;IAAQ,CAAC;IAAE/D;EAAO,CAAC,CAAC;EAC1ED,mCAAmC,CAACC,OAAO,EAAEiE,UAAU,CAAC;EAExD,OAAO,CAACrC,MAAM,EAAEe,OAAO,CAAC;AAC1B;AAEA,OAAO,MAAMuB,UAAU,GAAiB;EACtCC,UAAU,EAAE7E,IAAI;EAChB8E,WAAW,EAAE,OAAO;EACpBC,UAAU,EAAE/D;CACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}