{"ast": null, "code": "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { backend_util, Dilation2DBackpropInput, util } from '@tensorflow/tfjs-core';\nexport const dilation2DBackpropInputConfig = {\n  kernelName: Dilation2DBackpropInput,\n  backendName: 'cpu',\n  kernelFunc: ({\n    inputs,\n    backend,\n    attrs\n  }) => {\n    const {\n      x,\n      filter,\n      dy\n    } = inputs;\n    const {\n      strides,\n      pad,\n      dilations\n    } = attrs;\n    const cpuBackend = backend;\n    const $x = util.toNestedArray(x.shape, cpuBackend.data.get(x.dataId).values);\n    const $filter = util.toNestedArray(filter.shape, cpuBackend.data.get(filter.dataId).values);\n    const {\n      batchSize,\n      inHeight,\n      inWidth,\n      inChannels,\n      outHeight,\n      outWidth,\n      padInfo,\n      strideHeight,\n      strideWidth,\n      filterHeight,\n      filterWidth,\n      dilationHeight,\n      dilationWidth,\n      outShape\n    } = backend_util.computeDilation2DInfo(x.shape, filter.shape, strides, pad, 'NHWC' /* dataFormat */, dilations);\n    util.assert(dy.rank === outShape.length, () => `Error in ${Dilation2DBackpropInput}, dy ` + `must have the same rank as output ${outShape.length}, but got ` + `${dy.rank}`);\n    const $dy = util.toNestedArray(outShape, cpuBackend.data.get(dy.dataId).values);\n    // The computed gradients has the same dimensions as the input:\n    // [batch, inputHeight, inputCols, inChannel]\n    const gradients = util.makeZerosNestedTypedArray(x.shape, x.dtype);\n    // In the case of multiple argmax branches, we only back-propagate along the\n    // last branch, i.e., the one with largest value of `h * filter_cols + w`,\n    // similarly to the max-pooling backward routines.\n    // This implementation follows the TF c++ implementation:\n    // https://github.com/tensorflow/tensorflow/blob/d9a3a849edc198e90172bc58eb293de457f9d986/tensorflow/core/kernels/dilation_ops.cc\n    for (let b = 0; b < batchSize; ++b) {\n      for (let hOut = 0; hOut < outHeight; ++hOut) {\n        const hBeg = hOut * strideHeight - padInfo.top;\n        for (let wOut = 0; wOut < outWidth; ++wOut) {\n          const wBeg = wOut * strideWidth - padInfo.left;\n          for (let d = 0; d < inChannels; ++d) {\n            let curVal = Number.MIN_SAFE_INTEGER;\n            let hInMax = hBeg < 0 ? 0 : hBeg;\n            let wInMax = wBeg < 0 ? 0 : wBeg;\n            for (let h = 0; h < filterHeight; ++h) {\n              const hIn = hBeg + h * dilationHeight;\n              if (hIn >= 0 && hIn < inHeight) {\n                for (let w = 0; w < filterWidth; ++w) {\n                  const wIn = wBeg + w * dilationWidth;\n                  if (wIn >= 0 && wIn < inWidth) {\n                    const val = $x[b][hIn][wIn][d] + $filter[h][w][d];\n                    if (val > curVal) {\n                      curVal = val;\n                      hInMax = hIn;\n                      wInMax = wIn;\n                    }\n                  }\n                }\n              }\n            }\n            gradients[b][hInMax][wInMax][d] += $dy[b][hOut][wOut][d];\n          }\n        }\n      }\n    }\n    const dataId = cpuBackend.write(util.toTypedArray(gradients, x.dtype), x.shape, x.dtype);\n    return {\n      dataId,\n      shape: x.shape,\n      dtype: x.dtype\n    };\n  }\n};", "map": {"version": 3, "names": ["backend_util", "Dilation2DBackpropInput", "util", "dilation2DBackpropInputConfig", "kernelName", "backendName", "kernelFunc", "inputs", "backend", "attrs", "x", "filter", "dy", "strides", "pad", "dilations", "cpuBackend", "$x", "toNestedArray", "shape", "data", "get", "dataId", "values", "$filter", "batchSize", "inHeight", "inWidth", "inChannels", "outHeight", "outWidth", "padInfo", "strideHeight", "<PERSON><PERSON><PERSON><PERSON>", "filterHeight", "filterWidth", "dilationHeight", "dilationWidth", "outShape", "computeDilation2DInfo", "assert", "rank", "length", "$dy", "gradients", "makeZerosNestedTypedArray", "dtype", "b", "hOut", "hBeg", "top", "wOut", "wBeg", "left", "d", "curVal", "Number", "MIN_SAFE_INTEGER", "hInMax", "wInMax", "h", "hIn", "w", "wIn", "val", "write", "toTypedA<PERSON>y"], "sources": ["C:\\tfjs-backend-cpu\\src\\kernels\\Dilation2DBackpropInput.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {backend_util, Dilation2DAttrs, Dilation2DBackpropInput, Tensor3D, Tensor4D, TypedArray, util} from '@tensorflow/tfjs-core';\nimport {KernelConfig} from '@tensorflow/tfjs-core';\n\nimport {MathBackendCPU} from '../backend_cpu';\n\nexport const dilation2DBackpropInputConfig: KernelConfig = {\n  kernelName: Dilation2DBackpropInput,\n  backendName: 'cpu',\n  kernelFunc: ({inputs, backend, attrs}) => {\n    const {x, filter, dy} =\n        inputs as {x: Tensor4D, filter: Tensor3D, dy: Tensor4D};\n    const {strides, pad, dilations} = attrs as unknown as Dilation2DAttrs;\n    const cpuBackend = backend as MathBackendCPU;\n\n    const $x =\n        util.toNestedArray(\n            x.shape, cpuBackend.data.get(x.dataId).values as TypedArray) as\n        number[][][][];\n\n    const $filter = util.toNestedArray(\n                        filter.shape,\n                        cpuBackend.data.get(filter.dataId).values as\n                            TypedArray) as number[][][];\n\n    const {\n      batchSize,\n      inHeight,\n      inWidth,\n      inChannels,\n      outHeight,\n      outWidth,\n      padInfo,\n      strideHeight,\n      strideWidth,\n      filterHeight,\n      filterWidth,\n      dilationHeight,\n      dilationWidth,\n      outShape\n    } =\n        backend_util.computeDilation2DInfo(\n            x.shape as [number, number, number, number],\n            filter.shape as [number, number, number], strides, pad,\n            'NHWC' /* dataFormat */, dilations);\n\n    util.assert(\n        dy.rank === outShape.length,\n        () => `Error in ${Dilation2DBackpropInput}, dy ` +\n            `must have the same rank as output ${outShape.length}, but got ` +\n            `${dy.rank}`);\n\n    const $dy =\n        util.toNestedArray(\n            outShape, cpuBackend.data.get(dy.dataId).values as TypedArray) as\n        number[][][][];\n\n    // The computed gradients has the same dimensions as the input:\n    // [batch, inputHeight, inputCols, inChannel]\n    const gradients =\n        util.makeZerosNestedTypedArray(x.shape, x.dtype) as number[][][][];\n\n    // In the case of multiple argmax branches, we only back-propagate along the\n    // last branch, i.e., the one with largest value of `h * filter_cols + w`,\n    // similarly to the max-pooling backward routines.\n    // This implementation follows the TF c++ implementation:\n    // https://github.com/tensorflow/tensorflow/blob/d9a3a849edc198e90172bc58eb293de457f9d986/tensorflow/core/kernels/dilation_ops.cc\n    for (let b = 0; b < batchSize; ++b) {\n      for (let hOut = 0; hOut < outHeight; ++hOut) {\n        const hBeg = hOut * strideHeight - padInfo.top;\n        for (let wOut = 0; wOut < outWidth; ++wOut) {\n          const wBeg = wOut * strideWidth - padInfo.left;\n          for (let d = 0; d < inChannels; ++d) {\n            let curVal = Number.MIN_SAFE_INTEGER;\n            let hInMax = (hBeg < 0) ? 0 : hBeg;\n            let wInMax = (wBeg < 0) ? 0 : wBeg;\n            for (let h = 0; h < filterHeight; ++h) {\n              const hIn = hBeg + h * dilationHeight;\n              if (hIn >= 0 && hIn < inHeight) {\n                for (let w = 0; w < filterWidth; ++w) {\n                  const wIn = wBeg + w * dilationWidth;\n                  if (wIn >= 0 && wIn < inWidth) {\n                    const val = $x[b][hIn][wIn][d] + $filter[h][w][d];\n                    if (val > curVal) {\n                      curVal = val;\n                      hInMax = hIn;\n                      wInMax = wIn;\n                    }\n                  }\n                }\n              }\n            }\n            gradients[b][hInMax][wInMax][d] += $dy[b][hOut][wOut][d];\n          }\n        }\n      }\n    }\n\n    const dataId = cpuBackend.write(\n        util.toTypedArray(gradients, x.dtype), x.shape, x.dtype);\n\n    return {dataId, shape: x.shape, dtype: x.dtype};\n  }\n};\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAiBA,SAAQA,YAAY,EAAmBC,uBAAuB,EAAkCC,IAAI,QAAO,uBAAuB;AAKlI,OAAO,MAAMC,6BAA6B,GAAiB;EACzDC,UAAU,EAAEH,uBAAuB;EACnCI,WAAW,EAAE,KAAK;EAClBC,UAAU,EAAEA,CAAC;IAACC,MAAM;IAAEC,OAAO;IAAEC;EAAK,CAAC,KAAI;IACvC,MAAM;MAACC,CAAC;MAAEC,MAAM;MAAEC;IAAE,CAAC,GACjBL,MAAuD;IAC3D,MAAM;MAACM,OAAO;MAAEC,GAAG;MAAEC;IAAS,CAAC,GAAGN,KAAmC;IACrE,MAAMO,UAAU,GAAGR,OAAyB;IAE5C,MAAMS,EAAE,GACJf,IAAI,CAACgB,aAAa,CACdR,CAAC,CAACS,KAAK,EAAEH,UAAU,CAACI,IAAI,CAACC,GAAG,CAACX,CAAC,CAACY,MAAM,CAAC,CAACC,MAAoB,CACjD;IAElB,MAAMC,OAAO,GAAGtB,IAAI,CAACgB,aAAa,CACdP,MAAM,CAACQ,KAAK,EACZH,UAAU,CAACI,IAAI,CAACC,GAAG,CAACV,MAAM,CAACW,MAAM,CAAC,CAACC,MACrB,CAAiB;IAEnD,MAAM;MACJE,SAAS;MACTC,QAAQ;MACRC,OAAO;MACPC,UAAU;MACVC,SAAS;MACTC,QAAQ;MACRC,OAAO;MACPC,YAAY;MACZC,WAAW;MACXC,YAAY;MACZC,WAAW;MACXC,cAAc;MACdC,aAAa;MACbC;IAAQ,CACT,GACGtC,YAAY,CAACuC,qBAAqB,CAC9B7B,CAAC,CAACS,KAAyC,EAC3CR,MAAM,CAACQ,KAAiC,EAAEN,OAAO,EAAEC,GAAG,EACtD,MAAM,CAAC,kBAAkBC,SAAS,CAAC;IAE3Cb,IAAI,CAACsC,MAAM,CACP5B,EAAE,CAAC6B,IAAI,KAAKH,QAAQ,CAACI,MAAM,EAC3B,MAAM,YAAYzC,uBAAuB,OAAO,GAC5C,qCAAqCqC,QAAQ,CAACI,MAAM,YAAY,GAChE,GAAG9B,EAAE,CAAC6B,IAAI,EAAE,CAAC;IAErB,MAAME,GAAG,GACLzC,IAAI,CAACgB,aAAa,CACdoB,QAAQ,EAAEtB,UAAU,CAACI,IAAI,CAACC,GAAG,CAACT,EAAE,CAACU,MAAM,CAAC,CAACC,MAAoB,CACnD;IAElB;IACA;IACA,MAAMqB,SAAS,GACX1C,IAAI,CAAC2C,yBAAyB,CAACnC,CAAC,CAACS,KAAK,EAAET,CAAC,CAACoC,KAAK,CAAmB;IAEtE;IACA;IACA;IACA;IACA;IACA,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGtB,SAAS,EAAE,EAAEsB,CAAC,EAAE;MAClC,KAAK,IAAIC,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAGnB,SAAS,EAAE,EAAEmB,IAAI,EAAE;QAC3C,MAAMC,IAAI,GAAGD,IAAI,GAAGhB,YAAY,GAAGD,OAAO,CAACmB,GAAG;QAC9C,KAAK,IAAIC,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAGrB,QAAQ,EAAE,EAAEqB,IAAI,EAAE;UAC1C,MAAMC,IAAI,GAAGD,IAAI,GAAGlB,WAAW,GAAGF,OAAO,CAACsB,IAAI;UAC9C,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG1B,UAAU,EAAE,EAAE0B,CAAC,EAAE;YACnC,IAAIC,MAAM,GAAGC,MAAM,CAACC,gBAAgB;YACpC,IAAIC,MAAM,GAAIT,IAAI,GAAG,CAAC,GAAI,CAAC,GAAGA,IAAI;YAClC,IAAIU,MAAM,GAAIP,IAAI,GAAG,CAAC,GAAI,CAAC,GAAGA,IAAI;YAClC,KAAK,IAAIQ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG1B,YAAY,EAAE,EAAE0B,CAAC,EAAE;cACrC,MAAMC,GAAG,GAAGZ,IAAI,GAAGW,CAAC,GAAGxB,cAAc;cACrC,IAAIyB,GAAG,IAAI,CAAC,IAAIA,GAAG,GAAGnC,QAAQ,EAAE;gBAC9B,KAAK,IAAIoC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG3B,WAAW,EAAE,EAAE2B,CAAC,EAAE;kBACpC,MAAMC,GAAG,GAAGX,IAAI,GAAGU,CAAC,GAAGzB,aAAa;kBACpC,IAAI0B,GAAG,IAAI,CAAC,IAAIA,GAAG,GAAGpC,OAAO,EAAE;oBAC7B,MAAMqC,GAAG,GAAG/C,EAAE,CAAC8B,CAAC,CAAC,CAACc,GAAG,CAAC,CAACE,GAAG,CAAC,CAACT,CAAC,CAAC,GAAG9B,OAAO,CAACoC,CAAC,CAAC,CAACE,CAAC,CAAC,CAACR,CAAC,CAAC;oBACjD,IAAIU,GAAG,GAAGT,MAAM,EAAE;sBAChBA,MAAM,GAAGS,GAAG;sBACZN,MAAM,GAAGG,GAAG;sBACZF,MAAM,GAAGI,GAAG;;;;;;YAMtBnB,SAAS,CAACG,CAAC,CAAC,CAACW,MAAM,CAAC,CAACC,MAAM,CAAC,CAACL,CAAC,CAAC,IAAIX,GAAG,CAACI,CAAC,CAAC,CAACC,IAAI,CAAC,CAACG,IAAI,CAAC,CAACG,CAAC,CAAC;;;;;IAMhE,MAAMhC,MAAM,GAAGN,UAAU,CAACiD,KAAK,CAC3B/D,IAAI,CAACgE,YAAY,CAACtB,SAAS,EAAElC,CAAC,CAACoC,KAAK,CAAC,EAAEpC,CAAC,CAACS,KAAK,EAAET,CAAC,CAACoC,KAAK,CAAC;IAE5D,OAAO;MAACxB,MAAM;MAAEH,KAAK,EAAET,CAAC,CAACS,KAAK;MAAE2B,KAAK,EAAEpC,CAAC,CAACoC;IAAK,CAAC;EACjD;CACD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}