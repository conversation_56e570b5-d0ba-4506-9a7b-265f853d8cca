{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.transformArguments = exports.FIRST_KEY_INDEX = void 0;\nconst generic_transformers_1 = require(\"./generic-transformers\");\nexports.FIRST_KEY_INDEX = generic_transformers_1.evalFirstKeyIndex;\nfunction transformArguments(fn, options) {\n  return (0, generic_transformers_1.pushEvalArguments)(['FCALL', fn], options);\n}\nexports.transformArguments = transformArguments;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "transformArguments", "FIRST_KEY_INDEX", "generic_transformers_1", "require", "evalFirstKeyIndex", "fn", "options", "pushEvalArguments"], "sources": ["C:/tmsft/node_modules/@redis/client/dist/lib/commands/FCALL.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.transformArguments = exports.FIRST_KEY_INDEX = void 0;\nconst generic_transformers_1 = require(\"./generic-transformers\");\nexports.FIRST_KEY_INDEX = generic_transformers_1.evalFirstKeyIndex;\nfunction transformArguments(fn, options) {\n    return (0, generic_transformers_1.pushEvalArguments)(['FCALL', fn], options);\n}\nexports.transformArguments = transformArguments;\n"], "mappings": "AAAA,YAAY;;AACZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEC,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7DD,OAAO,CAACE,kBAAkB,GAAGF,OAAO,CAACG,eAAe,GAAG,KAAK,CAAC;AAC7D,MAAMC,sBAAsB,GAAGC,OAAO,CAAC,wBAAwB,CAAC;AAChEL,OAAO,CAACG,eAAe,GAAGC,sBAAsB,CAACE,iBAAiB;AAClE,SAASJ,kBAAkBA,CAACK,EAAE,EAAEC,OAAO,EAAE;EACrC,OAAO,CAAC,CAAC,EAAEJ,sBAAsB,CAACK,iBAAiB,EAAE,CAAC,OAAO,EAAEF,EAAE,CAAC,EAAEC,OAAO,CAAC;AAChF;AACAR,OAAO,CAACE,kBAAkB,GAAGA,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}