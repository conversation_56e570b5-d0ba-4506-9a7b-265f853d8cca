{"ast": null, "code": "/**\n * @license\n * Copyright 2017 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { env } from '@tensorflow/tfjs-core';\nimport { getInternalFormatForFloat16MatrixTexture, getInternalFormatForFloat16PackedMatrixTexture, getInternalFormatForFloat32MatrixTexture, getInternalFormatForPackedMatrixTexture, getInternalFormatForUnsignedBytesMatrixTexture } from './gpgpu_util';\nimport { getPackedMatrixTextureShapeWidthHeight, getUnpackedMatrixTextureShapeWidthHeight, PhysicalTextureType, TextureUsage } from './tex_util';\nexport class TextureManager {\n  constructor(gpgpu) {\n    this.gpgpu = gpgpu;\n    this.numUsedTextures = 0;\n    this.numFreeTextures = 0;\n    this._numBytesAllocated = 0;\n    // Number of bytes that have been allocated and available for reuse.\n    this._numBytesFree = 0;\n    this.freeTextures = {};\n    this.usedTextures = {};\n    this.logEnabled = false;\n  }\n  acquireTexture(shapeRC, usage, isPacked) {\n    const physicalTexType = getPhysicalFromLogicalTextureType(usage, isPacked);\n    const shapeKey = getKeyFromTextureShape(shapeRC, physicalTexType, isPacked);\n    if (!(shapeKey in this.freeTextures)) {\n      this.freeTextures[shapeKey] = [];\n    }\n    if (!(shapeKey in this.usedTextures)) {\n      this.usedTextures[shapeKey] = [];\n    }\n    const texBytes = computeBytes(shapeRC, physicalTexType, this.gpgpu.gl, this.gpgpu.textureConfig, isPacked);\n    if (this.freeTextures[shapeKey].length > 0) {\n      this.numFreeTextures--;\n      this.numUsedTextures++;\n      this._numBytesFree -= texBytes;\n      this.log();\n      const newTexture = this.freeTextures[shapeKey].pop();\n      this.usedTextures[shapeKey].push(newTexture);\n      return newTexture;\n    }\n    let newTexture;\n    if (physicalTexType === PhysicalTextureType.PACKED_2X2_FLOAT32) {\n      newTexture = this.gpgpu.createPackedMatrixTexture(shapeRC[0], shapeRC[1]);\n    } else if (physicalTexType === PhysicalTextureType.PACKED_2X2_FLOAT16) {\n      newTexture = this.gpgpu.createFloat16PackedMatrixTexture(shapeRC[0], shapeRC[1]);\n    } else if (physicalTexType === PhysicalTextureType.UNPACKED_FLOAT32) {\n      newTexture = this.gpgpu.createFloat32MatrixTexture(shapeRC[0], shapeRC[1]);\n    } else if (physicalTexType === PhysicalTextureType.UNPACKED_FLOAT16) {\n      newTexture = this.gpgpu.createFloat16MatrixTexture(shapeRC[0], shapeRC[1]);\n    } else if (physicalTexType === PhysicalTextureType.PACKED_4X1_UNSIGNED_BYTE) {\n      newTexture = this.gpgpu.createUnsignedBytesMatrixTexture(shapeRC[0], shapeRC[1]);\n    }\n    this.usedTextures[shapeKey].push(newTexture);\n    this.numUsedTextures++;\n    this._numBytesAllocated += texBytes;\n    this.log();\n    return newTexture;\n  }\n  releaseTexture(texture, shape, logicalTexType, isPacked) {\n    if (this.freeTextures == null) {\n      // Already disposed.\n      return;\n    }\n    const physicalTexType = getPhysicalFromLogicalTextureType(logicalTexType, isPacked);\n    const shapeKey = getKeyFromTextureShape(shape, physicalTexType, isPacked);\n    if (!(shapeKey in this.freeTextures)) {\n      this.freeTextures[shapeKey] = [];\n    }\n    const texBytes = computeBytes(shape, physicalTexType, this.gpgpu.gl, this.gpgpu.textureConfig, isPacked);\n    const deleteTexThreshold = env().getNumber('WEBGL_DELETE_TEXTURE_THRESHOLD');\n    if (deleteTexThreshold !== -1 && this._numBytesAllocated > deleteTexThreshold) {\n      this.gpgpu.deleteMatrixTexture(texture.texture);\n      this._numBytesAllocated -= texBytes;\n    } else {\n      this.freeTextures[shapeKey].push(texture);\n      this.numFreeTextures++;\n      this._numBytesFree += texBytes;\n    }\n    this.numUsedTextures--;\n    const texList = this.usedTextures[shapeKey];\n    const texIndex = texList && texList.indexOf(texture);\n    if (texIndex == null || texIndex < 0) {\n      throw new Error('Cannot release a texture that was never provided by this ' + 'texture manager');\n    }\n    texList[texIndex] = texList[texList.length - 1];\n    texList.pop();\n    this.log();\n  }\n  log() {\n    if (!this.logEnabled) {\n      return;\n    }\n    const total = this.numFreeTextures + this.numUsedTextures;\n    console.log('Free/Used', \"\".concat(this.numFreeTextures, \" / \").concat(this.numUsedTextures), \"(\".concat(total, \")\"));\n    const freeRatio = this._numBytesFree / this._numBytesAllocated;\n    console.log(\"Bytes allocated: \".concat(this._numBytesAllocated));\n    console.log(\"Bytes unused: \".concat(this._numBytesFree, \" (\").concat(Math.round(100 * freeRatio), \"%)\"));\n  }\n  get numBytesAllocated() {\n    return this._numBytesAllocated;\n  }\n  get numBytesFree() {\n    return this._numBytesFree;\n  }\n  getNumUsedTextures() {\n    return this.numUsedTextures;\n  }\n  getNumFreeTextures() {\n    return this.numFreeTextures;\n  }\n  dispose() {\n    if (this.freeTextures == null) {\n      // Already disposed.\n      return;\n    }\n    for (const texShape in this.freeTextures) {\n      this.freeTextures[texShape].forEach(tex => {\n        this.gpgpu.deleteMatrixTexture(tex.texture);\n      });\n    }\n    for (const texShape in this.usedTextures) {\n      this.usedTextures[texShape].forEach(tex => {\n        this.gpgpu.deleteMatrixTexture(tex.texture);\n      });\n    }\n    // TODO: Assign non-null value (empty object) to textures after disposed.\n    this.freeTextures = null;\n    this.usedTextures = null;\n    this.numUsedTextures = 0;\n    this.numFreeTextures = 0;\n    this._numBytesAllocated = 0;\n    this._numBytesFree = 0;\n  }\n}\nfunction numBytesForInternalFormat(gl, internalFormat) {\n  // tslint:disable-next-line:no-any\n  const glany = gl;\n  if (internalFormat === glany.R32F) {\n    return 4;\n  } else if (internalFormat === glany.R16F) {\n    return 2;\n  } else if (internalFormat === glany.RGBA32F) {\n    return 16;\n  } else if (internalFormat === gl.RGBA) {\n    return 16;\n  } else if (internalFormat === glany.RGBA16F) {\n    return 8;\n  } else if (internalFormat === glany.RGBA8) {\n    return 4;\n  }\n  throw new Error(\"Unknown internal format \".concat(internalFormat));\n}\nexport function computeBytes(shape, physicalTexType, gl, textureConfig, isPacked) {\n  // It is not possible to infer packed status from the texture type because\n  // depending on the textureConfig, different  texture types may resolve to the\n  // same internal format (e.g. in WebGL1, the internal format for\n  // UNPACKED_FLOAT16 textures is gl.RGBA). Therefore we pass in `isPacked`\n  // explicitly.\n  const internalFormat = internalFormatForPhysicalTexType(physicalTexType, textureConfig);\n  let numElements;\n  if (isPacked) {\n    const [packedWidth, packedHeight] = getPackedMatrixTextureShapeWidthHeight(shape[0], shape[1]);\n    numElements = packedWidth * packedHeight;\n  } else {\n    const [width, height] = getUnpackedMatrixTextureShapeWidthHeight(shape[0], shape[1]);\n    numElements = width * height;\n  }\n  const bytesPerElement = numBytesForInternalFormat(gl, internalFormat);\n  return numElements * bytesPerElement;\n}\nfunction internalFormatForPhysicalTexType(physicalTexType, textureConfig) {\n  switch (physicalTexType) {\n    case PhysicalTextureType.PACKED_2X2_FLOAT32:\n      return getInternalFormatForPackedMatrixTexture(textureConfig);\n    case PhysicalTextureType.PACKED_2X2_FLOAT16:\n      return getInternalFormatForFloat16PackedMatrixTexture(textureConfig);\n    case PhysicalTextureType.UNPACKED_FLOAT32:\n      return getInternalFormatForFloat32MatrixTexture(textureConfig);\n    case PhysicalTextureType.UNPACKED_FLOAT16:\n      return getInternalFormatForFloat16MatrixTexture(textureConfig);\n    case PhysicalTextureType.PACKED_4X1_UNSIGNED_BYTE:\n      return getInternalFormatForUnsignedBytesMatrixTexture(textureConfig);\n    default:\n      throw new Error(\"Unknown physical texture type \".concat(physicalTexType));\n  }\n}\nfunction getPhysicalTextureForRendering(isPacked) {\n  if (env().getBool('WEBGL_RENDER_FLOAT32_ENABLED')) {\n    if (isPacked) {\n      return PhysicalTextureType.PACKED_2X2_FLOAT32;\n    }\n    return PhysicalTextureType.UNPACKED_FLOAT32;\n  }\n  if (isPacked) {\n    return PhysicalTextureType.PACKED_2X2_FLOAT16;\n  }\n  return PhysicalTextureType.UNPACKED_FLOAT16;\n}\nfunction getPhysicalFromLogicalTextureType(logicalTexType, isPacked) {\n  if (logicalTexType === TextureUsage.UPLOAD) {\n    return PhysicalTextureType.PACKED_2X2_FLOAT32;\n  } else if (logicalTexType === TextureUsage.RENDER || logicalTexType == null) {\n    return getPhysicalTextureForRendering(isPacked);\n  } else if (logicalTexType === TextureUsage.DOWNLOAD || logicalTexType === TextureUsage.PIXELS) {\n    return PhysicalTextureType.PACKED_4X1_UNSIGNED_BYTE;\n  }\n  throw new Error(\"Unknown logical texture type \".concat(logicalTexType));\n}\nfunction getKeyFromTextureShape(shapeRowsCol, physicalTexType, isPacked) {\n  return \"\".concat(shapeRowsCol[0], \"_\").concat(shapeRowsCol[1], \"_\").concat(physicalTexType, \"_\").concat(isPacked);\n}", "map": {"version": 3, "names": ["env", "getInternalFormatForFloat16MatrixTexture", "getInternalFormatForFloat16PackedMatrixTexture", "getInternalFormatForFloat32MatrixTexture", "getInternalFormatForPackedMatrixTexture", "getInternalFormatForUnsignedBytesMatrixTexture", "getPackedMatrixTextureShapeWidthHeight", "getUnpackedMatrixTextureShapeWidthHeight", "PhysicalTextureType", "TextureUsage", "TextureManager", "constructor", "gpgpu", "numUsedTextures", "numFreeTextures", "_numBytesAllocated", "_numBytesFree", "freeTextures", "usedTextures", "logEnabled", "acquireTexture", "shapeRC", "usage", "isPacked", "physicalTexType", "getPhysicalFromLogicalTextureType", "shape<PERSON>ey", "getKeyFromTextureShape", "texBytes", "computeBytes", "gl", "textureConfig", "length", "log", "newTexture", "pop", "push", "PACKED_2X2_FLOAT32", "createPackedMatrixTexture", "PACKED_2X2_FLOAT16", "createFloat16PackedMatrixTexture", "UNPACKED_FLOAT32", "createFloat32MatrixTexture", "UNPACKED_FLOAT16", "createFloat16MatrixTexture", "PACKED_4X1_UNSIGNED_BYTE", "createUnsignedBytesMatrixTexture", "releaseTexture", "texture", "shape", "logicalTexType", "deleteTexThreshold", "getNumber", "deleteMatrixTexture", "texList", "texIndex", "indexOf", "Error", "total", "console", "concat", "freeRatio", "Math", "round", "numBytesAllocated", "numBytesFree", "getNumUsedTextures", "getNumFreeTextures", "dispose", "texShape", "for<PERSON>ach", "tex", "numBytesForInternalFormat", "internalFormat", "glany", "R32F", "R16F", "RGBA32F", "RGBA", "RGBA16F", "RGBA8", "internalFormatForPhysicalTexType", "numElements", "packedWidth", "packedHeight", "width", "height", "bytesPerElement", "getPhysicalTextureForRendering", "getBool", "UPLOAD", "RENDER", "DOWNLOAD", "PIXELS", "shapeRowsCol"], "sources": ["C:\\tfjs-backend-webgl\\src\\texture_manager.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2017 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {env} from '@tensorflow/tfjs-core';\n\nimport {GPGPUContext} from './gpgpu_context';\nimport {getInternalFormatForFloat16MatrixTexture, getInternalFormatForFloat16PackedMatrixTexture, getInternalFormatForFloat32MatrixTexture, getInternalFormatForPackedMatrixTexture, getInternalFormatForUnsignedBytesMatrixTexture} from './gpgpu_util';\nimport {getPackedMatrixTextureShapeWidthHeight, getUnpackedMatrixTextureShapeWidthHeight, PhysicalTextureType, Texture, TextureConfig, TextureUsage} from './tex_util';\n\nexport class TextureManager {\n  private numUsedTextures = 0;\n  private numFreeTextures = 0;\n  private _numBytesAllocated = 0;\n  // Number of bytes that have been allocated and available for reuse.\n  private _numBytesFree = 0;\n  private freeTextures: Record<string, Texture[]> = {};\n  private usedTextures: Record<string, Texture[]> = {};\n  private logEnabled = false;\n\n  constructor(private readonly gpgpu: GPGPUContext) {}\n\n  acquireTexture(\n      shapeRC: [number, number], usage: TextureUsage,\n      isPacked: boolean): Texture {\n    const physicalTexType = getPhysicalFromLogicalTextureType(usage, isPacked);\n\n    const shapeKey = getKeyFromTextureShape(shapeRC, physicalTexType, isPacked);\n    if (!(shapeKey in this.freeTextures)) {\n      this.freeTextures[shapeKey] = [];\n    }\n    if (!(shapeKey in this.usedTextures)) {\n      this.usedTextures[shapeKey] = [];\n    }\n\n    const texBytes = computeBytes(\n        shapeRC, physicalTexType, this.gpgpu.gl, this.gpgpu.textureConfig,\n        isPacked);\n\n    if (this.freeTextures[shapeKey].length > 0) {\n      this.numFreeTextures--;\n      this.numUsedTextures++;\n      this._numBytesFree -= texBytes;\n      this.log();\n      const newTexture = this.freeTextures[shapeKey].pop();\n      this.usedTextures[shapeKey].push(newTexture);\n      return newTexture;\n    }\n\n    let newTexture: Texture;\n    if (physicalTexType === PhysicalTextureType.PACKED_2X2_FLOAT32) {\n      newTexture = this.gpgpu.createPackedMatrixTexture(shapeRC[0], shapeRC[1]);\n    } else if (physicalTexType === PhysicalTextureType.PACKED_2X2_FLOAT16) {\n      newTexture =\n          this.gpgpu.createFloat16PackedMatrixTexture(shapeRC[0], shapeRC[1]);\n    } else if (physicalTexType === PhysicalTextureType.UNPACKED_FLOAT32) {\n      newTexture =\n          this.gpgpu.createFloat32MatrixTexture(shapeRC[0], shapeRC[1]);\n    } else if (physicalTexType === PhysicalTextureType.UNPACKED_FLOAT16) {\n      newTexture =\n          this.gpgpu.createFloat16MatrixTexture(shapeRC[0], shapeRC[1]);\n    } else if (\n        physicalTexType === PhysicalTextureType.PACKED_4X1_UNSIGNED_BYTE) {\n      newTexture =\n          this.gpgpu.createUnsignedBytesMatrixTexture(shapeRC[0], shapeRC[1]);\n    }\n    this.usedTextures[shapeKey].push(newTexture);\n\n    this.numUsedTextures++;\n    this._numBytesAllocated += texBytes;\n    this.log();\n\n    return newTexture;\n  }\n\n  releaseTexture(\n      texture: Texture, shape: [number, number], logicalTexType: TextureUsage,\n      isPacked: boolean): void {\n    if (this.freeTextures == null) {\n      // Already disposed.\n      return;\n    }\n    const physicalTexType =\n        getPhysicalFromLogicalTextureType(logicalTexType, isPacked);\n    const shapeKey = getKeyFromTextureShape(shape, physicalTexType, isPacked);\n    if (!(shapeKey in this.freeTextures)) {\n      this.freeTextures[shapeKey] = [];\n    }\n\n    const texBytes = computeBytes(\n        shape, physicalTexType, this.gpgpu.gl, this.gpgpu.textureConfig,\n        isPacked);\n    const deleteTexThreshold = env()\n        .getNumber('WEBGL_DELETE_TEXTURE_THRESHOLD');\n    if (deleteTexThreshold !== -1 &&\n        this._numBytesAllocated > deleteTexThreshold) {\n      this.gpgpu.deleteMatrixTexture(texture.texture);\n      this._numBytesAllocated -= texBytes;\n    } else {\n      this.freeTextures[shapeKey].push(texture);\n      this.numFreeTextures++;\n      this._numBytesFree += texBytes;\n    }\n\n    this.numUsedTextures--;\n\n    const texList = this.usedTextures[shapeKey];\n    const texIndex = texList && texList.indexOf(texture);\n    if (texIndex == null || texIndex < 0) {\n      throw new Error(\n          'Cannot release a texture that was never provided by this ' +\n          'texture manager');\n    }\n    texList[texIndex] = texList[texList.length - 1];\n    texList.pop();\n    this.log();\n  }\n\n  private log() {\n    if (!this.logEnabled) {\n      return;\n    }\n    const total = this.numFreeTextures + this.numUsedTextures;\n    console.log(\n        'Free/Used', `${this.numFreeTextures} / ${this.numUsedTextures}`,\n        `(${total})`);\n    const freeRatio = this._numBytesFree / this._numBytesAllocated;\n    console.log(`Bytes allocated: ${this._numBytesAllocated}`);\n    console.log(`Bytes unused: ${this._numBytesFree} (${\n        Math.round(100 * freeRatio)}%)`);\n  }\n\n  get numBytesAllocated(): number {\n    return this._numBytesAllocated;\n  }\n\n  get numBytesFree(): number {\n    return this._numBytesFree;\n  }\n\n  getNumUsedTextures(): number {\n    return this.numUsedTextures;\n  }\n\n  getNumFreeTextures(): number {\n    return this.numFreeTextures;\n  }\n\n  dispose() {\n    if (this.freeTextures == null) {\n      // Already disposed.\n      return;\n    }\n    for (const texShape in this.freeTextures) {\n      this.freeTextures[texShape].forEach(tex => {\n        this.gpgpu.deleteMatrixTexture(tex.texture);\n      });\n    }\n    for (const texShape in this.usedTextures) {\n      this.usedTextures[texShape].forEach(tex => {\n        this.gpgpu.deleteMatrixTexture(tex.texture);\n      });\n    }\n    // TODO: Assign non-null value (empty object) to textures after disposed.\n    this.freeTextures = null;\n    this.usedTextures = null;\n    this.numUsedTextures = 0;\n    this.numFreeTextures = 0;\n    this._numBytesAllocated = 0;\n    this._numBytesFree = 0;\n  }\n}\n\nfunction numBytesForInternalFormat(\n    gl: WebGLRenderingContext, internalFormat: number): number {\n  // tslint:disable-next-line:no-any\n  const glany = gl as any;\n  if (internalFormat === glany.R32F) {\n    return 4;\n  } else if (internalFormat === glany.R16F) {\n    return 2;\n  } else if (internalFormat === glany.RGBA32F) {\n    return 16;\n  } else if (internalFormat === gl.RGBA) {\n    return 16;\n  } else if (internalFormat === glany.RGBA16F) {\n    return 8;\n  } else if (internalFormat === glany.RGBA8) {\n    return 4;\n  }\n  throw new Error(`Unknown internal format ${internalFormat}`);\n}\n\nexport function computeBytes(\n    shape: [number, number], physicalTexType: PhysicalTextureType,\n    gl: WebGLRenderingContext, textureConfig: TextureConfig,\n    isPacked: boolean): number {\n  // It is not possible to infer packed status from the texture type because\n  // depending on the textureConfig, different  texture types may resolve to the\n  // same internal format (e.g. in WebGL1, the internal format for\n  // UNPACKED_FLOAT16 textures is gl.RGBA). Therefore we pass in `isPacked`\n  // explicitly.\n  const internalFormat =\n      internalFormatForPhysicalTexType(physicalTexType, textureConfig);\n\n  let numElements: number;\n  if (isPacked) {\n    const [packedWidth, packedHeight] =\n        getPackedMatrixTextureShapeWidthHeight(shape[0], shape[1]);\n    numElements = packedWidth * packedHeight;\n\n  } else {\n    const [width, height] =\n        getUnpackedMatrixTextureShapeWidthHeight(shape[0], shape[1]);\n    numElements = width * height;\n  }\n\n  const bytesPerElement = numBytesForInternalFormat(gl, internalFormat);\n  return numElements * bytesPerElement;\n}\n\nfunction internalFormatForPhysicalTexType(\n    physicalTexType: PhysicalTextureType,\n    textureConfig: TextureConfig): number {\n  switch (physicalTexType) {\n    case PhysicalTextureType.PACKED_2X2_FLOAT32:\n      return getInternalFormatForPackedMatrixTexture(textureConfig);\n    case PhysicalTextureType.PACKED_2X2_FLOAT16:\n      return getInternalFormatForFloat16PackedMatrixTexture(textureConfig);\n    case PhysicalTextureType.UNPACKED_FLOAT32:\n      return getInternalFormatForFloat32MatrixTexture(textureConfig);\n    case PhysicalTextureType.UNPACKED_FLOAT16:\n      return getInternalFormatForFloat16MatrixTexture(textureConfig);\n    case PhysicalTextureType.PACKED_4X1_UNSIGNED_BYTE:\n      return getInternalFormatForUnsignedBytesMatrixTexture(textureConfig);\n    default:\n      throw new Error(`Unknown physical texture type ${physicalTexType}`);\n  }\n}\n\nfunction getPhysicalTextureForRendering(isPacked: boolean):\n    PhysicalTextureType {\n  if (env().getBool('WEBGL_RENDER_FLOAT32_ENABLED')) {\n    if (isPacked) {\n      return PhysicalTextureType.PACKED_2X2_FLOAT32;\n    }\n    return PhysicalTextureType.UNPACKED_FLOAT32;\n  }\n\n  if (isPacked) {\n    return PhysicalTextureType.PACKED_2X2_FLOAT16;\n  }\n  return PhysicalTextureType.UNPACKED_FLOAT16;\n}\n\nfunction getPhysicalFromLogicalTextureType(\n    logicalTexType: TextureUsage, isPacked: boolean): PhysicalTextureType {\n  if (logicalTexType === TextureUsage.UPLOAD) {\n    return PhysicalTextureType.PACKED_2X2_FLOAT32;\n  } else if (logicalTexType === TextureUsage.RENDER || logicalTexType == null) {\n    return getPhysicalTextureForRendering(isPacked);\n  } else if (\n      logicalTexType === TextureUsage.DOWNLOAD ||\n      logicalTexType === TextureUsage.PIXELS) {\n    return PhysicalTextureType.PACKED_4X1_UNSIGNED_BYTE;\n  }\n  throw new Error(`Unknown logical texture type ${logicalTexType}`);\n}\n\nfunction getKeyFromTextureShape(\n    shapeRowsCol: [number, number], physicalTexType: PhysicalTextureType,\n    isPacked: boolean): string {\n  return `${shapeRowsCol[0]}_${shapeRowsCol[1]}_${physicalTexType}_${isPacked}`;\n}\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAiBA,SAAQA,GAAG,QAAO,uBAAuB;AAGzC,SAAQC,wCAAwC,EAAEC,8CAA8C,EAAEC,wCAAwC,EAAEC,uCAAuC,EAAEC,8CAA8C,QAAO,cAAc;AACxP,SAAQC,sCAAsC,EAAEC,wCAAwC,EAAEC,mBAAmB,EAA0BC,YAAY,QAAO,YAAY;AAEtK,OAAM,MAAOC,cAAc;EAUzBC,YAA6BC,KAAmB;IAAnB,KAAAA,KAAK,GAALA,KAAK;IAT1B,KAAAC,eAAe,GAAG,CAAC;IACnB,KAAAC,eAAe,GAAG,CAAC;IACnB,KAAAC,kBAAkB,GAAG,CAAC;IAC9B;IACQ,KAAAC,aAAa,GAAG,CAAC;IACjB,KAAAC,YAAY,GAA8B,EAAE;IAC5C,KAAAC,YAAY,GAA8B,EAAE;IAC5C,KAAAC,UAAU,GAAG,KAAK;EAEyB;EAEnDC,cAAcA,CACVC,OAAyB,EAAEC,KAAmB,EAC9CC,QAAiB;IACnB,MAAMC,eAAe,GAAGC,iCAAiC,CAACH,KAAK,EAAEC,QAAQ,CAAC;IAE1E,MAAMG,QAAQ,GAAGC,sBAAsB,CAACN,OAAO,EAAEG,eAAe,EAAED,QAAQ,CAAC;IAC3E,IAAI,EAAEG,QAAQ,IAAI,IAAI,CAACT,YAAY,CAAC,EAAE;MACpC,IAAI,CAACA,YAAY,CAACS,QAAQ,CAAC,GAAG,EAAE;;IAElC,IAAI,EAAEA,QAAQ,IAAI,IAAI,CAACR,YAAY,CAAC,EAAE;MACpC,IAAI,CAACA,YAAY,CAACQ,QAAQ,CAAC,GAAG,EAAE;;IAGlC,MAAME,QAAQ,GAAGC,YAAY,CACzBR,OAAO,EAAEG,eAAe,EAAE,IAAI,CAACZ,KAAK,CAACkB,EAAE,EAAE,IAAI,CAAClB,KAAK,CAACmB,aAAa,EACjER,QAAQ,CAAC;IAEb,IAAI,IAAI,CAACN,YAAY,CAACS,QAAQ,CAAC,CAACM,MAAM,GAAG,CAAC,EAAE;MAC1C,IAAI,CAAClB,eAAe,EAAE;MACtB,IAAI,CAACD,eAAe,EAAE;MACtB,IAAI,CAACG,aAAa,IAAIY,QAAQ;MAC9B,IAAI,CAACK,GAAG,EAAE;MACV,MAAMC,UAAU,GAAG,IAAI,CAACjB,YAAY,CAACS,QAAQ,CAAC,CAACS,GAAG,EAAE;MACpD,IAAI,CAACjB,YAAY,CAACQ,QAAQ,CAAC,CAACU,IAAI,CAACF,UAAU,CAAC;MAC5C,OAAOA,UAAU;;IAGnB,IAAIA,UAAmB;IACvB,IAAIV,eAAe,KAAKhB,mBAAmB,CAAC6B,kBAAkB,EAAE;MAC9DH,UAAU,GAAG,IAAI,CAACtB,KAAK,CAAC0B,yBAAyB,CAACjB,OAAO,CAAC,CAAC,CAAC,EAAEA,OAAO,CAAC,CAAC,CAAC,CAAC;KAC1E,MAAM,IAAIG,eAAe,KAAKhB,mBAAmB,CAAC+B,kBAAkB,EAAE;MACrEL,UAAU,GACN,IAAI,CAACtB,KAAK,CAAC4B,gCAAgC,CAACnB,OAAO,CAAC,CAAC,CAAC,EAAEA,OAAO,CAAC,CAAC,CAAC,CAAC;KACxE,MAAM,IAAIG,eAAe,KAAKhB,mBAAmB,CAACiC,gBAAgB,EAAE;MACnEP,UAAU,GACN,IAAI,CAACtB,KAAK,CAAC8B,0BAA0B,CAACrB,OAAO,CAAC,CAAC,CAAC,EAAEA,OAAO,CAAC,CAAC,CAAC,CAAC;KAClE,MAAM,IAAIG,eAAe,KAAKhB,mBAAmB,CAACmC,gBAAgB,EAAE;MACnET,UAAU,GACN,IAAI,CAACtB,KAAK,CAACgC,0BAA0B,CAACvB,OAAO,CAAC,CAAC,CAAC,EAAEA,OAAO,CAAC,CAAC,CAAC,CAAC;KAClE,MAAM,IACHG,eAAe,KAAKhB,mBAAmB,CAACqC,wBAAwB,EAAE;MACpEX,UAAU,GACN,IAAI,CAACtB,KAAK,CAACkC,gCAAgC,CAACzB,OAAO,CAAC,CAAC,CAAC,EAAEA,OAAO,CAAC,CAAC,CAAC,CAAC;;IAEzE,IAAI,CAACH,YAAY,CAACQ,QAAQ,CAAC,CAACU,IAAI,CAACF,UAAU,CAAC;IAE5C,IAAI,CAACrB,eAAe,EAAE;IACtB,IAAI,CAACE,kBAAkB,IAAIa,QAAQ;IACnC,IAAI,CAACK,GAAG,EAAE;IAEV,OAAOC,UAAU;EACnB;EAEAa,cAAcA,CACVC,OAAgB,EAAEC,KAAuB,EAAEC,cAA4B,EACvE3B,QAAiB;IACnB,IAAI,IAAI,CAACN,YAAY,IAAI,IAAI,EAAE;MAC7B;MACA;;IAEF,MAAMO,eAAe,GACjBC,iCAAiC,CAACyB,cAAc,EAAE3B,QAAQ,CAAC;IAC/D,MAAMG,QAAQ,GAAGC,sBAAsB,CAACsB,KAAK,EAAEzB,eAAe,EAAED,QAAQ,CAAC;IACzE,IAAI,EAAEG,QAAQ,IAAI,IAAI,CAACT,YAAY,CAAC,EAAE;MACpC,IAAI,CAACA,YAAY,CAACS,QAAQ,CAAC,GAAG,EAAE;;IAGlC,MAAME,QAAQ,GAAGC,YAAY,CACzBoB,KAAK,EAAEzB,eAAe,EAAE,IAAI,CAACZ,KAAK,CAACkB,EAAE,EAAE,IAAI,CAAClB,KAAK,CAACmB,aAAa,EAC/DR,QAAQ,CAAC;IACb,MAAM4B,kBAAkB,GAAGnD,GAAG,EAAE,CAC3BoD,SAAS,CAAC,gCAAgC,CAAC;IAChD,IAAID,kBAAkB,KAAK,CAAC,CAAC,IACzB,IAAI,CAACpC,kBAAkB,GAAGoC,kBAAkB,EAAE;MAChD,IAAI,CAACvC,KAAK,CAACyC,mBAAmB,CAACL,OAAO,CAACA,OAAO,CAAC;MAC/C,IAAI,CAACjC,kBAAkB,IAAIa,QAAQ;KACpC,MAAM;MACL,IAAI,CAACX,YAAY,CAACS,QAAQ,CAAC,CAACU,IAAI,CAACY,OAAO,CAAC;MACzC,IAAI,CAAClC,eAAe,EAAE;MACtB,IAAI,CAACE,aAAa,IAAIY,QAAQ;;IAGhC,IAAI,CAACf,eAAe,EAAE;IAEtB,MAAMyC,OAAO,GAAG,IAAI,CAACpC,YAAY,CAACQ,QAAQ,CAAC;IAC3C,MAAM6B,QAAQ,GAAGD,OAAO,IAAIA,OAAO,CAACE,OAAO,CAACR,OAAO,CAAC;IACpD,IAAIO,QAAQ,IAAI,IAAI,IAAIA,QAAQ,GAAG,CAAC,EAAE;MACpC,MAAM,IAAIE,KAAK,CACX,2DAA2D,GAC3D,iBAAiB,CAAC;;IAExBH,OAAO,CAACC,QAAQ,CAAC,GAAGD,OAAO,CAACA,OAAO,CAACtB,MAAM,GAAG,CAAC,CAAC;IAC/CsB,OAAO,CAACnB,GAAG,EAAE;IACb,IAAI,CAACF,GAAG,EAAE;EACZ;EAEQA,GAAGA,CAAA;IACT,IAAI,CAAC,IAAI,CAACd,UAAU,EAAE;MACpB;;IAEF,MAAMuC,KAAK,GAAG,IAAI,CAAC5C,eAAe,GAAG,IAAI,CAACD,eAAe;IACzD8C,OAAO,CAAC1B,GAAG,CACP,WAAW,KAAA2B,MAAA,CAAK,IAAI,CAAC9C,eAAe,SAAA8C,MAAA,CAAM,IAAI,CAAC/C,eAAe,OAAA+C,MAAA,CAC1DF,KAAK,MAAG,CAAC;IACjB,MAAMG,SAAS,GAAG,IAAI,CAAC7C,aAAa,GAAG,IAAI,CAACD,kBAAkB;IAC9D4C,OAAO,CAAC1B,GAAG,qBAAA2B,MAAA,CAAqB,IAAI,CAAC7C,kBAAkB,CAAE,CAAC;IAC1D4C,OAAO,CAAC1B,GAAG,kBAAA2B,MAAA,CAAkB,IAAI,CAAC5C,aAAa,QAAA4C,MAAA,CAC3CE,IAAI,CAACC,KAAK,CAAC,GAAG,GAAGF,SAAS,CAAC,OAAI,CAAC;EACtC;EAEA,IAAIG,iBAAiBA,CAAA;IACnB,OAAO,IAAI,CAACjD,kBAAkB;EAChC;EAEA,IAAIkD,YAAYA,CAAA;IACd,OAAO,IAAI,CAACjD,aAAa;EAC3B;EAEAkD,kBAAkBA,CAAA;IAChB,OAAO,IAAI,CAACrD,eAAe;EAC7B;EAEAsD,kBAAkBA,CAAA;IAChB,OAAO,IAAI,CAACrD,eAAe;EAC7B;EAEAsD,OAAOA,CAAA;IACL,IAAI,IAAI,CAACnD,YAAY,IAAI,IAAI,EAAE;MAC7B;MACA;;IAEF,KAAK,MAAMoD,QAAQ,IAAI,IAAI,CAACpD,YAAY,EAAE;MACxC,IAAI,CAACA,YAAY,CAACoD,QAAQ,CAAC,CAACC,OAAO,CAACC,GAAG,IAAG;QACxC,IAAI,CAAC3D,KAAK,CAACyC,mBAAmB,CAACkB,GAAG,CAACvB,OAAO,CAAC;MAC7C,CAAC,CAAC;;IAEJ,KAAK,MAAMqB,QAAQ,IAAI,IAAI,CAACnD,YAAY,EAAE;MACxC,IAAI,CAACA,YAAY,CAACmD,QAAQ,CAAC,CAACC,OAAO,CAACC,GAAG,IAAG;QACxC,IAAI,CAAC3D,KAAK,CAACyC,mBAAmB,CAACkB,GAAG,CAACvB,OAAO,CAAC;MAC7C,CAAC,CAAC;;IAEJ;IACA,IAAI,CAAC/B,YAAY,GAAG,IAAI;IACxB,IAAI,CAACC,YAAY,GAAG,IAAI;IACxB,IAAI,CAACL,eAAe,GAAG,CAAC;IACxB,IAAI,CAACC,eAAe,GAAG,CAAC;IACxB,IAAI,CAACC,kBAAkB,GAAG,CAAC;IAC3B,IAAI,CAACC,aAAa,GAAG,CAAC;EACxB;;AAGF,SAASwD,yBAAyBA,CAC9B1C,EAAyB,EAAE2C,cAAsB;EACnD;EACA,MAAMC,KAAK,GAAG5C,EAAS;EACvB,IAAI2C,cAAc,KAAKC,KAAK,CAACC,IAAI,EAAE;IACjC,OAAO,CAAC;GACT,MAAM,IAAIF,cAAc,KAAKC,KAAK,CAACE,IAAI,EAAE;IACxC,OAAO,CAAC;GACT,MAAM,IAAIH,cAAc,KAAKC,KAAK,CAACG,OAAO,EAAE;IAC3C,OAAO,EAAE;GACV,MAAM,IAAIJ,cAAc,KAAK3C,EAAE,CAACgD,IAAI,EAAE;IACrC,OAAO,EAAE;GACV,MAAM,IAAIL,cAAc,KAAKC,KAAK,CAACK,OAAO,EAAE;IAC3C,OAAO,CAAC;GACT,MAAM,IAAIN,cAAc,KAAKC,KAAK,CAACM,KAAK,EAAE;IACzC,OAAO,CAAC;;EAEV,MAAM,IAAIvB,KAAK,4BAAAG,MAAA,CAA4Ba,cAAc,CAAE,CAAC;AAC9D;AAEA,OAAM,SAAU5C,YAAYA,CACxBoB,KAAuB,EAAEzB,eAAoC,EAC7DM,EAAyB,EAAEC,aAA4B,EACvDR,QAAiB;EACnB;EACA;EACA;EACA;EACA;EACA,MAAMkD,cAAc,GAChBQ,gCAAgC,CAACzD,eAAe,EAAEO,aAAa,CAAC;EAEpE,IAAImD,WAAmB;EACvB,IAAI3D,QAAQ,EAAE;IACZ,MAAM,CAAC4D,WAAW,EAAEC,YAAY,CAAC,GAC7B9E,sCAAsC,CAAC2C,KAAK,CAAC,CAAC,CAAC,EAAEA,KAAK,CAAC,CAAC,CAAC,CAAC;IAC9DiC,WAAW,GAAGC,WAAW,GAAGC,YAAY;GAEzC,MAAM;IACL,MAAM,CAACC,KAAK,EAAEC,MAAM,CAAC,GACjB/E,wCAAwC,CAAC0C,KAAK,CAAC,CAAC,CAAC,EAAEA,KAAK,CAAC,CAAC,CAAC,CAAC;IAChEiC,WAAW,GAAGG,KAAK,GAAGC,MAAM;;EAG9B,MAAMC,eAAe,GAAGf,yBAAyB,CAAC1C,EAAE,EAAE2C,cAAc,CAAC;EACrE,OAAOS,WAAW,GAAGK,eAAe;AACtC;AAEA,SAASN,gCAAgCA,CACrCzD,eAAoC,EACpCO,aAA4B;EAC9B,QAAQP,eAAe;IACrB,KAAKhB,mBAAmB,CAAC6B,kBAAkB;MACzC,OAAOjC,uCAAuC,CAAC2B,aAAa,CAAC;IAC/D,KAAKvB,mBAAmB,CAAC+B,kBAAkB;MACzC,OAAOrC,8CAA8C,CAAC6B,aAAa,CAAC;IACtE,KAAKvB,mBAAmB,CAACiC,gBAAgB;MACvC,OAAOtC,wCAAwC,CAAC4B,aAAa,CAAC;IAChE,KAAKvB,mBAAmB,CAACmC,gBAAgB;MACvC,OAAO1C,wCAAwC,CAAC8B,aAAa,CAAC;IAChE,KAAKvB,mBAAmB,CAACqC,wBAAwB;MAC/C,OAAOxC,8CAA8C,CAAC0B,aAAa,CAAC;IACtE;MACE,MAAM,IAAI0B,KAAK,kCAAAG,MAAA,CAAkCpC,eAAe,CAAE,CAAC;;AAEzE;AAEA,SAASgE,8BAA8BA,CAACjE,QAAiB;EAEvD,IAAIvB,GAAG,EAAE,CAACyF,OAAO,CAAC,8BAA8B,CAAC,EAAE;IACjD,IAAIlE,QAAQ,EAAE;MACZ,OAAOf,mBAAmB,CAAC6B,kBAAkB;;IAE/C,OAAO7B,mBAAmB,CAACiC,gBAAgB;;EAG7C,IAAIlB,QAAQ,EAAE;IACZ,OAAOf,mBAAmB,CAAC+B,kBAAkB;;EAE/C,OAAO/B,mBAAmB,CAACmC,gBAAgB;AAC7C;AAEA,SAASlB,iCAAiCA,CACtCyB,cAA4B,EAAE3B,QAAiB;EACjD,IAAI2B,cAAc,KAAKzC,YAAY,CAACiF,MAAM,EAAE;IAC1C,OAAOlF,mBAAmB,CAAC6B,kBAAkB;GAC9C,MAAM,IAAIa,cAAc,KAAKzC,YAAY,CAACkF,MAAM,IAAIzC,cAAc,IAAI,IAAI,EAAE;IAC3E,OAAOsC,8BAA8B,CAACjE,QAAQ,CAAC;GAChD,MAAM,IACH2B,cAAc,KAAKzC,YAAY,CAACmF,QAAQ,IACxC1C,cAAc,KAAKzC,YAAY,CAACoF,MAAM,EAAE;IAC1C,OAAOrF,mBAAmB,CAACqC,wBAAwB;;EAErD,MAAM,IAAIY,KAAK,iCAAAG,MAAA,CAAiCV,cAAc,CAAE,CAAC;AACnE;AAEA,SAASvB,sBAAsBA,CAC3BmE,YAA8B,EAAEtE,eAAoC,EACpED,QAAiB;EACnB,UAAAqC,MAAA,CAAUkC,YAAY,CAAC,CAAC,CAAC,OAAAlC,MAAA,CAAIkC,YAAY,CAAC,CAAC,CAAC,OAAAlC,MAAA,CAAIpC,eAAe,OAAAoC,MAAA,CAAIrC,QAAQ;AAC7E", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}