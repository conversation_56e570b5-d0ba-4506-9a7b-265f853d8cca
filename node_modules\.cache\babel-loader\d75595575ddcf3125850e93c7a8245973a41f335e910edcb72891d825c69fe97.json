{"ast": null, "code": "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { backend_util, SpaceToBatchND, util } from '@tensorflow/tfjs-core';\nimport { padV2 } from './PadV2';\nimport { reshape } from './Reshape';\nimport { transpose } from './Transpose';\nexport const spaceToBatchND = args => {\n  const {\n    inputs,\n    backend,\n    attrs\n  } = args;\n  const {\n    x\n  } = inputs;\n  const {\n    blockShape,\n    paddings\n  } = attrs;\n  util.assert(x.shape.length <= 4, () => 'spaceToBatchND for rank > 4 with a WebGL backend not ' + 'implemented yet');\n  const prod = blockShape.reduce((a, b) => a * b);\n  const completePaddings = [[0, 0]];\n  completePaddings.push(...paddings);\n  for (let i = 1 + blockShape.length; i < x.shape.length; ++i) {\n    completePaddings.push([0, 0]);\n  }\n  const toDispose = [];\n  const paddedX = padV2({\n    inputs: {\n      x\n    },\n    backend,\n    attrs: {\n      paddings: completePaddings,\n      constantValue: 0\n    }\n  });\n  const reshapedPaddedShape = backend_util.getReshaped(paddedX.shape, blockShape, prod, false);\n  const permutedReshapedPaddedPermutation = backend_util.getPermuted(reshapedPaddedShape.length, blockShape.length, false);\n  const flattenShape = backend_util.getReshapedPermuted(paddedX.shape, blockShape, prod, false);\n  const reshapedPaddedX = reshape({\n    inputs: {\n      x: paddedX\n    },\n    backend,\n    attrs: {\n      shape: reshapedPaddedShape\n    }\n  });\n  const paddedXT = transpose({\n    inputs: {\n      x: reshapedPaddedX\n    },\n    backend,\n    attrs: {\n      perm: permutedReshapedPaddedPermutation\n    }\n  });\n  const result = reshape({\n    inputs: {\n      x: paddedXT\n    },\n    backend,\n    attrs: {\n      shape: flattenShape\n    }\n  });\n  toDispose.push(paddedX);\n  toDispose.push(reshapedPaddedX);\n  toDispose.push(paddedXT);\n  toDispose.forEach(t => backend.disposeIntermediateTensorInfo(t));\n  return result;\n};\nexport const spaceToBatchNDConfig = {\n  kernelName: SpaceToBatchND,\n  backendName: 'webgl',\n  kernelFunc: spaceToBatchND\n};", "map": {"version": 3, "names": ["backend_util", "SpaceToBatchND", "util", "padV2", "reshape", "transpose", "spaceToBatchND", "args", "inputs", "backend", "attrs", "x", "blockShape", "paddings", "assert", "shape", "length", "prod", "reduce", "a", "b", "completePaddings", "push", "i", "toDispose", "paddedX", "constantV<PERSON>ue", "reshapedPaddedShape", "getReshaped", "permutedReshapedPaddedPermutation", "getPermuted", "flattenShape", "getReshapedPermuted", "reshapedPaddedX", "paddedXT", "perm", "result", "for<PERSON>ach", "t", "disposeIntermediateTensorInfo", "spaceToBatchNDConfig", "kernelName", "backendName", "kernelFunc"], "sources": ["C:\\tfjs-backend-webgl\\src\\kernels\\SpaceToBatchND.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {backend_util, KernelConfig, KernelFunc, SpaceToBatchND, SpaceToBatchNDAttrs, SpaceToBatchNDInputs, TensorInfo, util} from '@tensorflow/tfjs-core';\n\nimport {MathBackendWebGL} from '../backend_webgl';\n\nimport {padV2} from './PadV2';\nimport {reshape} from './Reshape';\nimport {transpose} from './Transpose';\n\nexport const spaceToBatchND = (args: {\n  inputs: SpaceToBatchNDInputs,\n  backend: MathBackendWebGL,\n  attrs: SpaceToBatchNDAttrs\n}): TensorInfo => {\n  const {inputs, backend, attrs} = args;\n  const {x} = inputs;\n  const {blockShape, paddings} = attrs;\n\n  util.assert(\n      x.shape.length <= 4,\n      () => 'spaceToBatchND for rank > 4 with a WebGL backend not ' +\n          'implemented yet');\n\n  const prod = blockShape.reduce((a, b) => a * b);\n\n  const completePaddings: Array<[number, number]> = [[0, 0]];\n  completePaddings.push(...paddings as Array<[number, number]>);\n  for (let i = 1 + blockShape.length; i < x.shape.length; ++i) {\n    completePaddings.push([0, 0]);\n  }\n\n  const toDispose = [];\n\n  const paddedX = padV2({\n    inputs: {x},\n    backend,\n    attrs: {paddings: completePaddings, constantValue: 0}\n  });\n\n  const reshapedPaddedShape =\n      backend_util.getReshaped(paddedX.shape, blockShape, prod, false);\n\n  const permutedReshapedPaddedPermutation = backend_util.getPermuted(\n      reshapedPaddedShape.length, blockShape.length, false);\n\n  const flattenShape =\n      backend_util.getReshapedPermuted(paddedX.shape, blockShape, prod, false);\n\n  const reshapedPaddedX = reshape(\n      {inputs: {x: paddedX}, backend, attrs: {shape: reshapedPaddedShape}});\n\n  const paddedXT = transpose({\n    inputs: {x: reshapedPaddedX},\n    backend,\n    attrs: {perm: permutedReshapedPaddedPermutation}\n  });\n\n  const result =\n      reshape({inputs: {x: paddedXT}, backend, attrs: {shape: flattenShape}});\n\n  toDispose.push(paddedX);\n  toDispose.push(reshapedPaddedX);\n  toDispose.push(paddedXT);\n\n  toDispose.forEach(t => backend.disposeIntermediateTensorInfo(t));\n\n  return result;\n};\n\nexport const spaceToBatchNDConfig: KernelConfig = {\n  kernelName: SpaceToBatchND,\n  backendName: 'webgl',\n  kernelFunc: spaceToBatchND as unknown as KernelFunc\n};\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAiBA,SAAQA,YAAY,EAA4BC,cAAc,EAAyDC,IAAI,QAAO,uBAAuB;AAIzJ,SAAQC,KAAK,QAAO,SAAS;AAC7B,SAAQC,OAAO,QAAO,WAAW;AACjC,SAAQC,SAAS,QAAO,aAAa;AAErC,OAAO,MAAMC,cAAc,GAAIC,IAI9B,IAAgB;EACf,MAAM;IAACC,MAAM;IAAEC,OAAO;IAAEC;EAAK,CAAC,GAAGH,IAAI;EACrC,MAAM;IAACI;EAAC,CAAC,GAAGH,MAAM;EAClB,MAAM;IAACI,UAAU;IAAEC;EAAQ,CAAC,GAAGH,KAAK;EAEpCR,IAAI,CAACY,MAAM,CACPH,CAAC,CAACI,KAAK,CAACC,MAAM,IAAI,CAAC,EACnB,MAAM,uDAAuD,GACzD,iBAAiB,CAAC;EAE1B,MAAMC,IAAI,GAAGL,UAAU,CAACM,MAAM,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,GAAGC,CAAC,CAAC;EAE/C,MAAMC,gBAAgB,GAA4B,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EAC1DA,gBAAgB,CAACC,IAAI,CAAC,GAAGT,QAAmC,CAAC;EAC7D,KAAK,IAAIU,CAAC,GAAG,CAAC,GAAGX,UAAU,CAACI,MAAM,EAAEO,CAAC,GAAGZ,CAAC,CAACI,KAAK,CAACC,MAAM,EAAE,EAAEO,CAAC,EAAE;IAC3DF,gBAAgB,CAACC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;;EAG/B,MAAME,SAAS,GAAG,EAAE;EAEpB,MAAMC,OAAO,GAAGtB,KAAK,CAAC;IACpBK,MAAM,EAAE;MAACG;IAAC,CAAC;IACXF,OAAO;IACPC,KAAK,EAAE;MAACG,QAAQ,EAAEQ,gBAAgB;MAAEK,aAAa,EAAE;IAAC;GACrD,CAAC;EAEF,MAAMC,mBAAmB,GACrB3B,YAAY,CAAC4B,WAAW,CAACH,OAAO,CAACV,KAAK,EAAEH,UAAU,EAAEK,IAAI,EAAE,KAAK,CAAC;EAEpE,MAAMY,iCAAiC,GAAG7B,YAAY,CAAC8B,WAAW,CAC9DH,mBAAmB,CAACX,MAAM,EAAEJ,UAAU,CAACI,MAAM,EAAE,KAAK,CAAC;EAEzD,MAAMe,YAAY,GACd/B,YAAY,CAACgC,mBAAmB,CAACP,OAAO,CAACV,KAAK,EAAEH,UAAU,EAAEK,IAAI,EAAE,KAAK,CAAC;EAE5E,MAAMgB,eAAe,GAAG7B,OAAO,CAC3B;IAACI,MAAM,EAAE;MAACG,CAAC,EAAEc;IAAO,CAAC;IAAEhB,OAAO;IAAEC,KAAK,EAAE;MAACK,KAAK,EAAEY;IAAmB;EAAC,CAAC,CAAC;EAEzE,MAAMO,QAAQ,GAAG7B,SAAS,CAAC;IACzBG,MAAM,EAAE;MAACG,CAAC,EAAEsB;IAAe,CAAC;IAC5BxB,OAAO;IACPC,KAAK,EAAE;MAACyB,IAAI,EAAEN;IAAiC;GAChD,CAAC;EAEF,MAAMO,MAAM,GACRhC,OAAO,CAAC;IAACI,MAAM,EAAE;MAACG,CAAC,EAAEuB;IAAQ,CAAC;IAAEzB,OAAO;IAAEC,KAAK,EAAE;MAACK,KAAK,EAAEgB;IAAY;EAAC,CAAC,CAAC;EAE3EP,SAAS,CAACF,IAAI,CAACG,OAAO,CAAC;EACvBD,SAAS,CAACF,IAAI,CAACW,eAAe,CAAC;EAC/BT,SAAS,CAACF,IAAI,CAACY,QAAQ,CAAC;EAExBV,SAAS,CAACa,OAAO,CAACC,CAAC,IAAI7B,OAAO,CAAC8B,6BAA6B,CAACD,CAAC,CAAC,CAAC;EAEhE,OAAOF,MAAM;AACf,CAAC;AAED,OAAO,MAAMI,oBAAoB,GAAiB;EAChDC,UAAU,EAAExC,cAAc;EAC1ByC,WAAW,EAAE,OAAO;EACpBC,UAAU,EAAErC;CACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}