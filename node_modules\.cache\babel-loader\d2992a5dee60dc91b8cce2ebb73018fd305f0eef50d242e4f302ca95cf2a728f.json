{"ast": null, "code": "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { LeakyRelu, util } from '@tensorflow/tfjs-core';\nimport { assertNotComplex } from '../cpu_util';\nexport function leakyRelu(args) {\n  const {\n    inputs,\n    backend,\n    attrs\n  } = args;\n  const {\n    x\n  } = inputs;\n  const {\n    alpha\n  } = attrs;\n  assertNotComplex([x], 'leakyRelu');\n  const xSize = util.sizeFromShape(x.shape);\n  const xVals = backend.data.get(x.dataId).values;\n  const outVals = util.getTypedArrayFromDType('float32', xSize);\n  for (let i = 0; i < xVals.length; i++) {\n    outVals[i] = xVals[i] < 0 ? alpha * xVals[i] : xVals[i];\n  }\n  return backend.makeTensorInfo(x.shape, 'float32', outVals);\n}\nexport const leakyReluConfig = {\n  kernelName: LeakyRelu,\n  backendName: 'cpu',\n  kernelFunc: leakyRelu\n};", "map": {"version": 3, "names": ["LeakyRelu", "util", "assertNotComplex", "leakyRelu", "args", "inputs", "backend", "attrs", "x", "alpha", "xSize", "sizeFromShape", "shape", "xVals", "data", "get", "dataId", "values", "outVals", "getTypedArrayFromDType", "i", "length", "makeTensorInfo", "leakyReluConfig", "kernelName", "backendName", "kernelFunc"], "sources": ["C:\\tfjs-backend-cpu\\src\\kernels\\LeakyRelu.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {KernelConfig, KernelFunc, LeakyRelu, LeakyReluAttrs, LeakyReluInputs, TensorInfo, TypedArray, util} from '@tensorflow/tfjs-core';\n\nimport {MathBackendCPU} from '../backend_cpu';\nimport {assertNotComplex} from '../cpu_util';\n\nexport function leakyRelu(args: {\n  inputs: LeakyReluInputs,\n  backend: MathBackendCPU,\n  attrs: LeakyReluAttrs\n}): TensorInfo {\n  const {inputs, backend, attrs} = args;\n  const {x} = inputs;\n  const {alpha} = attrs;\n\n  assertNotComplex([x], 'leakyRelu');\n\n  const xSize = util.sizeFromShape(x.shape);\n  const xVals = backend.data.get(x.dataId).values as TypedArray;\n  const outVals = util.getTypedArrayFromDType('float32', xSize);\n\n  for (let i = 0; i < xVals.length; i++) {\n    outVals[i] = xVals[i] < 0 ? alpha * xVals[i] : xVals[i];\n  }\n\n  return backend.makeTensorInfo(x.shape, 'float32', outVals);\n}\n\nexport const leakyReluConfig: KernelConfig = {\n  kernelName: LeakyRelu,\n  backendName: 'cpu',\n  kernelFunc: leakyRelu as unknown as KernelFunc\n};\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAiBA,SAAkCA,SAAS,EAA2DC,IAAI,QAAO,uBAAuB;AAGxI,SAAQC,gBAAgB,QAAO,aAAa;AAE5C,OAAM,SAAUC,SAASA,CAACC,IAIzB;EACC,MAAM;IAACC,MAAM;IAAEC,OAAO;IAAEC;EAAK,CAAC,GAAGH,IAAI;EACrC,MAAM;IAACI;EAAC,CAAC,GAAGH,MAAM;EAClB,MAAM;IAACI;EAAK,CAAC,GAAGF,KAAK;EAErBL,gBAAgB,CAAC,CAACM,CAAC,CAAC,EAAE,WAAW,CAAC;EAElC,MAAME,KAAK,GAAGT,IAAI,CAACU,aAAa,CAACH,CAAC,CAACI,KAAK,CAAC;EACzC,MAAMC,KAAK,GAAGP,OAAO,CAACQ,IAAI,CAACC,GAAG,CAACP,CAAC,CAACQ,MAAM,CAAC,CAACC,MAAoB;EAC7D,MAAMC,OAAO,GAAGjB,IAAI,CAACkB,sBAAsB,CAAC,SAAS,EAAET,KAAK,CAAC;EAE7D,KAAK,IAAIU,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGP,KAAK,CAACQ,MAAM,EAAED,CAAC,EAAE,EAAE;IACrCF,OAAO,CAACE,CAAC,CAAC,GAAGP,KAAK,CAACO,CAAC,CAAC,GAAG,CAAC,GAAGX,KAAK,GAAGI,KAAK,CAACO,CAAC,CAAC,GAAGP,KAAK,CAACO,CAAC,CAAC;;EAGzD,OAAOd,OAAO,CAACgB,cAAc,CAACd,CAAC,CAACI,KAAK,EAAE,SAAS,EAAEM,OAAO,CAAC;AAC5D;AAEA,OAAO,MAAMK,eAAe,GAAiB;EAC3CC,UAAU,EAAExB,SAAS;EACrByB,WAAW,EAAE,KAAK;EAClBC,UAAU,EAAEvB;CACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}