{"ast": null, "code": "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { ClipByValue, env } from '@tensorflow/tfjs-core';\nimport { ClipProgram } from '../clip_gpu';\nimport { ClipPackedProgram } from '../clip_packed_gpu';\nexport function clipByValue(args) {\n  const {\n    inputs,\n    backend,\n    attrs\n  } = args;\n  const {\n    x\n  } = inputs;\n  const {\n    clipValueMin,\n    clipValueMax\n  } = attrs;\n  let program;\n  if (env().getBool('WEBGL_PACK_CLIP')) {\n    program = new ClipPackedProgram(x.shape);\n  } else {\n    program = new ClipProgram(x.shape);\n  }\n  const customValues = [[clipValueMin], [clipValueMax]];\n  return backend.runWebGLProgram(program, [x], x.dtype, customValues);\n}\nexport const clipByValueConfig = {\n  kernelName: ClipByValue,\n  backendName: 'webgl',\n  kernelFunc: clipByValue\n};", "map": {"version": 3, "names": ["ClipByValue", "env", "ClipProgram", "ClipPackedProgram", "clipByValue", "args", "inputs", "backend", "attrs", "x", "clipValueMin", "clipValueMax", "program", "getBool", "shape", "customValues", "runWebGLProgram", "dtype", "clipByValueConfig", "kernelName", "backendName", "kernelFunc"], "sources": ["C:\\tfjs-backend-webgl\\src\\kernels\\ClipByValue.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {ClipByValue, ClipByValueAttrs, ClipByValueInputs, env, KernelConfig, KernelFunc, TensorInfo} from '@tensorflow/tfjs-core';\n\nimport {MathBackendWebGL} from '../backend_webgl';\nimport {ClipProgram} from '../clip_gpu';\nimport {ClipPackedProgram} from '../clip_packed_gpu';\n\nexport function clipByValue(args: {\n  inputs: ClipByValueInputs,\n  backend: MathBackendWebGL,\n  attrs: ClipByValueAttrs\n}): TensorInfo {\n  const {inputs, backend, attrs} = args;\n  const {x} = inputs;\n  const {clipValueMin, clipValueMax} = attrs;\n\n  let program;\n  if (env().getBool('WEBGL_PACK_CLIP')) {\n    program = new ClipPackedProgram(x.shape);\n  } else {\n    program = new ClipProgram(x.shape);\n  }\n  const customValues = [[clipValueMin], [clipValueMax]];\n  return backend.runWebGLProgram(program, [x], x.dtype, customValues);\n}\n\nexport const clipByValueConfig: KernelConfig = {\n  kernelName: ClipByValue,\n  backendName: 'webgl',\n  kernelFunc: clipByValue as unknown as KernelFunc\n};\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAiBA,SAAQA,WAAW,EAAuCC,GAAG,QAA6C,uBAAuB;AAGjI,SAAQC,WAAW,QAAO,aAAa;AACvC,SAAQC,iBAAiB,QAAO,oBAAoB;AAEpD,OAAM,SAAUC,WAAWA,CAACC,IAI3B;EACC,MAAM;IAACC,MAAM;IAAEC,OAAO;IAAEC;EAAK,CAAC,GAAGH,IAAI;EACrC,MAAM;IAACI;EAAC,CAAC,GAAGH,MAAM;EAClB,MAAM;IAACI,YAAY;IAAEC;EAAY,CAAC,GAAGH,KAAK;EAE1C,IAAII,OAAO;EACX,IAAIX,GAAG,EAAE,CAACY,OAAO,CAAC,iBAAiB,CAAC,EAAE;IACpCD,OAAO,GAAG,IAAIT,iBAAiB,CAACM,CAAC,CAACK,KAAK,CAAC;GACzC,MAAM;IACLF,OAAO,GAAG,IAAIV,WAAW,CAACO,CAAC,CAACK,KAAK,CAAC;;EAEpC,MAAMC,YAAY,GAAG,CAAC,CAACL,YAAY,CAAC,EAAE,CAACC,YAAY,CAAC,CAAC;EACrD,OAAOJ,OAAO,CAACS,eAAe,CAACJ,OAAO,EAAE,CAACH,CAAC,CAAC,EAAEA,CAAC,CAACQ,KAAK,EAAEF,YAAY,CAAC;AACrE;AAEA,OAAO,MAAMG,iBAAiB,GAAiB;EAC7CC,UAAU,EAAEnB,WAAW;EACvBoB,WAAW,EAAE,OAAO;EACpBC,UAAU,EAAEjB;CACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}