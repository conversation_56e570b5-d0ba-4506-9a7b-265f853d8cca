{"ast": null, "code": "import { fileStorageService } from './fileStorageService';\nimport { categoryService } from './categoryService';\n\n// Simple browser-compatible ML service\nclass SimpleMlService {\n  constructor() {\n    this.MODELS_FILENAME = 'simple_ml_models';\n    this.TRAINING_DATA_FILENAME = 'simple_training_data';\n    this.categories = [];\n    this.categoryToIndex = new Map();\n    this.indexToCategory = new Map();\n    this.initializeCategories();\n  }\n\n  // Initialize categories mapping\n  initializeCategories() {\n    this.categories = categoryService.getAllCategories();\n    this.categories.forEach((category, index) => {\n      this.categoryToIndex.set(category.id, index);\n      this.indexToCategory.set(index, category.id);\n    });\n  }\n\n  // Simple tokenizer\n  tokenize(text) {\n    return text.toLowerCase().replace(/[^\\w\\s]/g, ' ').split(/\\s+/).filter(token => token.length > 0);\n  }\n\n  // Extract simple features from transaction\n  extractFeatures(description, amount) {\n    const tokens = this.tokenize(description);\n\n    // Financial keywords with weights\n    const financialKeywords = {\n      'salary': 0.9,\n      'wage': 0.9,\n      'payment': 0.8,\n      'transfer': 0.7,\n      'fee': 0.6,\n      'tax': 0.8,\n      'interest': 0.7,\n      'dividend': 0.8,\n      'rent': 0.6,\n      'utility': 0.5,\n      'insurance': 0.6,\n      'loan': 0.7,\n      'deposit': 0.8,\n      'withdrawal': 0.6,\n      'purchase': 0.5,\n      'refund': 0.7,\n      'bill': 0.5,\n      'invoice': 0.6,\n      'subscription': 0.5,\n      'service': 0.4,\n      'grocery': 0.4,\n      'restaurant': 0.4,\n      'fuel': 0.4,\n      'medical': 0.5\n    };\n\n    // Calculate keyword score\n    let keywordScore = 0;\n    let keywordCount = 0;\n    tokens.forEach(token => {\n      if (financialKeywords[token]) {\n        keywordScore += financialKeywords[token];\n        keywordCount++;\n      }\n    });\n    const avgKeywordScore = keywordCount > 0 ? keywordScore / keywordCount : 0;\n\n    // Amount features\n    const absAmount = Math.abs(amount);\n    const isCredit = amount > 0 ? 1 : 0;\n    const isDebit = amount < 0 ? 1 : 0;\n    const logAmount = Math.log(absAmount + 1) / 10;\n    const isSmallAmount = absAmount < 100 ? 1 : 0;\n    const isMediumAmount = absAmount >= 100 && absAmount < 1000 ? 1 : 0;\n    const isLargeAmount = absAmount >= 1000 ? 1 : 0;\n    const isRoundNumber = absAmount % 1 === 0 ? 1 : 0;\n\n    // Text features\n    const descLength = description.length / 100; // Normalized\n    const wordCount = tokens.length / 10; // Normalized\n    const hasNumbers = /\\d/.test(description) ? 1 : 0;\n    const hasReference = /ref|reference/.test(description.toLowerCase()) ? 1 : 0;\n    const hasDate = /\\d{2}\\/\\d{2}|\\d{4}/.test(description) ? 1 : 0;\n    return [avgKeywordScore, isCredit, isDebit, logAmount, isSmallAmount, isMediumAmount, isLargeAmount, isRoundNumber, descLength, wordCount, hasNumbers, hasReference, hasDate];\n  }\n\n  // Simple pattern-based categorization\n  async categorizeTransaction(description, amount) {\n    const features = this.extractFeatures(description, amount);\n    const trainingData = this.getTrainingData();\n    if (trainingData.length === 0) {\n      return null;\n    }\n\n    // Find the most similar training example using simple distance\n    let bestMatch = null;\n    let bestSimilarity = 0;\n    trainingData.forEach(data => {\n      const similarity = this.calculateSimilarity(features, data.features);\n      if (similarity > bestSimilarity) {\n        bestSimilarity = similarity;\n        bestMatch = data;\n      }\n    });\n    if (bestMatch && bestSimilarity > 0.3) {\n      // Minimum similarity threshold\n      return {\n        categoryId: bestMatch.categoryId,\n        confidence: bestSimilarity,\n        algorithm: 'pattern_matching',\n        features: ['description', 'amount', 'patterns'],\n        trainingDate: new Date().toISOString()\n      };\n    }\n    return null;\n  }\n\n  // Calculate similarity between feature vectors\n  calculateSimilarity(features1, features2) {\n    if (features1.length !== features2.length) return 0;\n    let dotProduct = 0;\n    let norm1 = 0;\n    let norm2 = 0;\n    for (let i = 0; i < features1.length; i++) {\n      dotProduct += features1[i] * features2[i];\n      norm1 += features1[i] * features1[i];\n      norm2 += features2[i] * features2[i];\n    }\n    const magnitude = Math.sqrt(norm1) * Math.sqrt(norm2);\n    return magnitude > 0 ? dotProduct / magnitude : 0;\n  }\n\n  // Train model (simplified - just stores training data)\n  async trainModel(trainingData) {\n    // For this simplified version, we just store the training data\n    // and calculate a mock accuracy based on data quality\n    this.saveTrainingData(trainingData);\n\n    // Calculate mock accuracy based on data distribution\n    const categoryDistribution = new Map();\n    trainingData.forEach(data => {\n      const count = categoryDistribution.get(data.categoryId) || 0;\n      categoryDistribution.set(data.categoryId, count + 1);\n    });\n\n    // Better distribution = higher accuracy\n    const categories = Array.from(categoryDistribution.values());\n    const avgExamplesPerCategory = categories.reduce((a, b) => a + b, 0) / categories.length;\n    const variance = categories.reduce((sum, count) => sum + Math.pow(count - avgExamplesPerCategory, 2), 0) / categories.length;\n    const normalizedVariance = Math.min(variance / avgExamplesPerCategory, 1);\n    const accuracy = Math.max(0.5, 0.95 - normalizedVariance * 0.3);\n\n    // Save a simple model record\n    const model = {\n      id: this.generateId(),\n      name: 'Simple Pattern Matching Model',\n      version: '1.0',\n      algorithm: 'pattern_matching',\n      accuracy,\n      trainingDate: new Date().toISOString(),\n      trainingSize: trainingData.length,\n      isActive: true,\n      modelData: JSON.stringify({\n        trainingDataSize: trainingData.length,\n        accuracy\n      })\n    };\n    const models = this.getAllModels();\n    // Deactivate old models\n    models.forEach(m => m.isActive = false);\n    models.push(model);\n    fileStorageService.writeData(this.MODELS_FILENAME, models);\n    return {\n      accuracy,\n      loss: 1 - accuracy\n    };\n  }\n\n  // Get all stored models\n  getAllModels() {\n    return fileStorageService.readData(this.MODELS_FILENAME, []);\n  }\n\n  // Add training data\n  addTrainingData(description, amount, categoryId) {\n    const trainingData = this.getTrainingData();\n    const newData = {\n      id: this.generateId(),\n      description,\n      amount,\n      categoryId,\n      features: this.extractFeatures(description, amount),\n      createdDate: new Date().toISOString()\n    };\n    trainingData.push(newData);\n    this.saveTrainingData(trainingData);\n  }\n\n  // Get all training data\n  getTrainingData() {\n    return fileStorageService.readData(this.TRAINING_DATA_FILENAME, []);\n  }\n\n  // Save training data\n  saveTrainingData(data) {\n    fileStorageService.writeData(this.TRAINING_DATA_FILENAME, data);\n  }\n\n  // Clear training data\n  clearTrainingData() {\n    this.saveTrainingData([]);\n  }\n\n  // Get model statistics\n  getModelStats() {\n    const models = this.getAllModels();\n    const activeModel = models.find(m => m.isActive) || null;\n    const trainingDataSize = this.getTrainingData().length;\n    return {\n      totalModels: models.length,\n      activeModel,\n      trainingDataSize\n    };\n  }\n\n  // Generate unique ID\n  generateId() {\n    return `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;\n  }\n\n  // Initialize with sample training data if none exists\n  initializeWithSampleData() {\n    const existingData = this.getTrainingData();\n    if (existingData.length > 0) return;\n\n    // Get income category\n    const incomeCategory = this.categories.find(c => c.name.toLowerCase().includes('income'));\n    const expenseCategory = this.categories.find(c => c.name.toLowerCase().includes('expense'));\n    if (!incomeCategory || !expenseCategory) return;\n\n    // Add some sample training data\n    const sampleData = [{\n      description: 'SALARY PAYMENT',\n      amount: 5000,\n      categoryId: incomeCategory.id\n    }, {\n      description: 'OFFICE RENT MONTHLY',\n      amount: -1200,\n      categoryId: expenseCategory.id\n    }, {\n      description: 'ELECTRICITY BILL',\n      amount: -150,\n      categoryId: expenseCategory.id\n    }, {\n      description: 'BANK CHARGES',\n      amount: -25,\n      categoryId: expenseCategory.id\n    }, {\n      description: 'CLIENT PAYMENT',\n      amount: 2500,\n      categoryId: incomeCategory.id\n    }];\n    sampleData.forEach(data => {\n      this.addTrainingData(data.description, data.amount, data.categoryId);\n    });\n  }\n}\nexport const simpleMlService = new SimpleMlService();", "map": {"version": 3, "names": ["fileStorageService", "categoryService", "SimpleMlService", "constructor", "MODELS_FILENAME", "TRAINING_DATA_FILENAME", "categories", "categoryToIndex", "Map", "indexToCategory", "initializeCategories", "getAllCategories", "for<PERSON>ach", "category", "index", "set", "id", "tokenize", "text", "toLowerCase", "replace", "split", "filter", "token", "length", "extractFeatures", "description", "amount", "tokens", "financialKeywords", "keywordScore", "keywordCount", "avgKeywordScore", "absAmount", "Math", "abs", "isCredit", "isDebit", "logAmount", "log", "isSmallAmount", "isMediumAmount", "isLargeAmount", "isRoundNumber", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "wordCount", "hasNumbers", "test", "hasReference", "hasDate", "categorizeTransaction", "features", "trainingData", "getTrainingData", "bestMatch", "bestSimilarity", "data", "similarity", "calculateSimilarity", "categoryId", "confidence", "algorithm", "trainingDate", "Date", "toISOString", "features1", "features2", "dotProduct", "norm1", "norm2", "i", "magnitude", "sqrt", "trainModel", "saveTrainingData", "categoryDistribution", "count", "get", "Array", "from", "values", "avgExamplesPerCategory", "reduce", "a", "b", "variance", "sum", "pow", "normalizedVariance", "min", "accuracy", "max", "model", "generateId", "name", "version", "trainingSize", "isActive", "modelData", "JSON", "stringify", "trainingDataSize", "models", "getAllModels", "m", "push", "writeData", "loss", "readData", "addTrainingData", "newData", "createdDate", "clearTrainingData", "getModelStats", "activeModel", "find", "totalModels", "now", "random", "toString", "substr", "initializeWithSampleData", "existingData", "incomeCategory", "c", "includes", "expenseCategory", "sampleData", "simpleMlService"], "sources": ["C:/tmsft/src/services/simpleMlService.ts"], "sourcesContent": ["import { MLModel, TrainingData, MLCategorization, TransactionCategory } from '../types';\r\nimport { fileStorageService } from './fileStorageService';\r\nimport { categoryService } from './categoryService';\r\n\r\n// Simple browser-compatible ML service\r\nclass SimpleMlService {\r\n  private readonly MODELS_FILENAME = 'simple_ml_models';\r\n  private readonly TRAINING_DATA_FILENAME = 'simple_training_data';\r\n  \r\n  private categories: TransactionCategory[] = [];\r\n  private categoryToIndex: Map<string, number> = new Map();\r\n  private indexToCategory: Map<number, string> = new Map();\r\n\r\n  constructor() {\r\n    this.initializeCategories();\r\n  }\r\n\r\n  // Initialize categories mapping\r\n  private initializeCategories(): void {\r\n    this.categories = categoryService.getAllCategories();\r\n    this.categories.forEach((category, index) => {\r\n      this.categoryToIndex.set(category.id, index);\r\n      this.indexToCategory.set(index, category.id);\r\n    });\r\n  }\r\n\r\n  // Simple tokenizer\r\n  private tokenize(text: string): string[] {\r\n    return text.toLowerCase()\r\n      .replace(/[^\\w\\s]/g, ' ')\r\n      .split(/\\s+/)\r\n      .filter(token => token.length > 0);\r\n  }\r\n\r\n  // Extract simple features from transaction\r\n  private extractFeatures(description: string, amount: number): number[] {\r\n    const tokens = this.tokenize(description);\r\n    \r\n    // Financial keywords with weights\r\n    const financialKeywords: Record<string, number> = {\r\n      'salary': 0.9, 'wage': 0.9, 'payment': 0.8, 'transfer': 0.7,\r\n      'fee': 0.6, 'tax': 0.8, 'interest': 0.7, 'dividend': 0.8,\r\n      'rent': 0.6, 'utility': 0.5, 'insurance': 0.6, 'loan': 0.7,\r\n      'deposit': 0.8, 'withdrawal': 0.6, 'purchase': 0.5, 'refund': 0.7,\r\n      'bill': 0.5, 'invoice': 0.6, 'subscription': 0.5, 'service': 0.4,\r\n      'grocery': 0.4, 'restaurant': 0.4, 'fuel': 0.4, 'medical': 0.5\r\n    };\r\n\r\n    // Calculate keyword score\r\n    let keywordScore = 0;\r\n    let keywordCount = 0;\r\n    tokens.forEach(token => {\r\n      if (financialKeywords[token]) {\r\n        keywordScore += financialKeywords[token];\r\n        keywordCount++;\r\n      }\r\n    });\r\n    \r\n    const avgKeywordScore = keywordCount > 0 ? keywordScore / keywordCount : 0;\r\n\r\n    // Amount features\r\n    const absAmount = Math.abs(amount);\r\n    const isCredit = amount > 0 ? 1 : 0;\r\n    const isDebit = amount < 0 ? 1 : 0;\r\n    const logAmount = Math.log(absAmount + 1) / 10;\r\n    const isSmallAmount = absAmount < 100 ? 1 : 0;\r\n    const isMediumAmount = absAmount >= 100 && absAmount < 1000 ? 1 : 0;\r\n    const isLargeAmount = absAmount >= 1000 ? 1 : 0;\r\n    const isRoundNumber = absAmount % 1 === 0 ? 1 : 0;\r\n\r\n    // Text features\r\n    const descLength = description.length / 100; // Normalized\r\n    const wordCount = tokens.length / 10; // Normalized\r\n    const hasNumbers = /\\d/.test(description) ? 1 : 0;\r\n    const hasReference = /ref|reference/.test(description.toLowerCase()) ? 1 : 0;\r\n    const hasDate = /\\d{2}\\/\\d{2}|\\d{4}/.test(description) ? 1 : 0;\r\n\r\n    return [\r\n      avgKeywordScore,\r\n      isCredit,\r\n      isDebit,\r\n      logAmount,\r\n      isSmallAmount,\r\n      isMediumAmount,\r\n      isLargeAmount,\r\n      isRoundNumber,\r\n      descLength,\r\n      wordCount,\r\n      hasNumbers,\r\n      hasReference,\r\n      hasDate\r\n    ];\r\n  }\r\n\r\n  // Simple pattern-based categorization\r\n  async categorizeTransaction(description: string, amount: number): Promise<MLCategorization | null> {\r\n    const features = this.extractFeatures(description, amount);\r\n    const trainingData = this.getTrainingData();\r\n    \r\n    if (trainingData.length === 0) {\r\n      return null;\r\n    }\r\n\r\n    // Find the most similar training example using simple distance\r\n    let bestMatch: TrainingData | null = null;\r\n    let bestSimilarity = 0;\r\n\r\n    trainingData.forEach(data => {\r\n      const similarity = this.calculateSimilarity(features, data.features);\r\n      if (similarity > bestSimilarity) {\r\n        bestSimilarity = similarity;\r\n        bestMatch = data;\r\n      }\r\n    });\r\n\r\n    if (bestMatch && bestSimilarity > 0.3) { // Minimum similarity threshold\r\n      return {\r\n        categoryId: bestMatch.categoryId,\r\n        confidence: bestSimilarity,\r\n        algorithm: 'pattern_matching',\r\n        features: ['description', 'amount', 'patterns'],\r\n        trainingDate: new Date().toISOString()\r\n      };\r\n    }\r\n\r\n    return null;\r\n  }\r\n\r\n  // Calculate similarity between feature vectors\r\n  private calculateSimilarity(features1: number[], features2: number[]): number {\r\n    if (features1.length !== features2.length) return 0;\r\n    \r\n    let dotProduct = 0;\r\n    let norm1 = 0;\r\n    let norm2 = 0;\r\n    \r\n    for (let i = 0; i < features1.length; i++) {\r\n      dotProduct += features1[i] * features2[i];\r\n      norm1 += features1[i] * features1[i];\r\n      norm2 += features2[i] * features2[i];\r\n    }\r\n    \r\n    const magnitude = Math.sqrt(norm1) * Math.sqrt(norm2);\r\n    return magnitude > 0 ? dotProduct / magnitude : 0;\r\n  }\r\n\r\n  // Train model (simplified - just stores training data)\r\n  async trainModel(trainingData: TrainingData[]): Promise<{ accuracy: number; loss: number }> {\r\n    // For this simplified version, we just store the training data\r\n    // and calculate a mock accuracy based on data quality\r\n    this.saveTrainingData(trainingData);\r\n    \r\n    // Calculate mock accuracy based on data distribution\r\n    const categoryDistribution = new Map<string, number>();\r\n    trainingData.forEach(data => {\r\n      const count = categoryDistribution.get(data.categoryId) || 0;\r\n      categoryDistribution.set(data.categoryId, count + 1);\r\n    });\r\n    \r\n    // Better distribution = higher accuracy\r\n    const categories = Array.from(categoryDistribution.values());\r\n    const avgExamplesPerCategory = categories.reduce((a, b) => a + b, 0) / categories.length;\r\n    const variance = categories.reduce((sum, count) => sum + Math.pow(count - avgExamplesPerCategory, 2), 0) / categories.length;\r\n    const normalizedVariance = Math.min(variance / avgExamplesPerCategory, 1);\r\n    const accuracy = Math.max(0.5, 0.95 - normalizedVariance * 0.3);\r\n    \r\n    // Save a simple model record\r\n    const model: MLModel = {\r\n      id: this.generateId(),\r\n      name: 'Simple Pattern Matching Model',\r\n      version: '1.0',\r\n      algorithm: 'pattern_matching',\r\n      accuracy,\r\n      trainingDate: new Date().toISOString(),\r\n      trainingSize: trainingData.length,\r\n      isActive: true,\r\n      modelData: JSON.stringify({ trainingDataSize: trainingData.length, accuracy })\r\n    };\r\n\r\n    const models = this.getAllModels();\r\n    // Deactivate old models\r\n    models.forEach(m => m.isActive = false);\r\n    models.push(model);\r\n    fileStorageService.writeData(this.MODELS_FILENAME, models);\r\n\r\n    return { accuracy, loss: 1 - accuracy };\r\n  }\r\n\r\n  // Get all stored models\r\n  getAllModels(): MLModel[] {\r\n    return fileStorageService.readData<MLModel[]>(this.MODELS_FILENAME, []);\r\n  }\r\n\r\n  // Add training data\r\n  addTrainingData(description: string, amount: number, categoryId: string): void {\r\n    const trainingData = this.getTrainingData();\r\n    \r\n    const newData: TrainingData = {\r\n      id: this.generateId(),\r\n      description,\r\n      amount,\r\n      categoryId,\r\n      features: this.extractFeatures(description, amount),\r\n      createdDate: new Date().toISOString()\r\n    };\r\n\r\n    trainingData.push(newData);\r\n    this.saveTrainingData(trainingData);\r\n  }\r\n\r\n  // Get all training data\r\n  getTrainingData(): TrainingData[] {\r\n    return fileStorageService.readData<TrainingData[]>(this.TRAINING_DATA_FILENAME, []);\r\n  }\r\n\r\n  // Save training data\r\n  private saveTrainingData(data: TrainingData[]): void {\r\n    fileStorageService.writeData(this.TRAINING_DATA_FILENAME, data);\r\n  }\r\n\r\n  // Clear training data\r\n  clearTrainingData(): void {\r\n    this.saveTrainingData([]);\r\n  }\r\n\r\n  // Get model statistics\r\n  getModelStats(): { totalModels: number; activeModel: MLModel | null; trainingDataSize: number } {\r\n    const models = this.getAllModels();\r\n    const activeModel = models.find(m => m.isActive) || null;\r\n    const trainingDataSize = this.getTrainingData().length;\r\n\r\n    return {\r\n      totalModels: models.length,\r\n      activeModel,\r\n      trainingDataSize\r\n    };\r\n  }\r\n\r\n  // Generate unique ID\r\n  private generateId(): string {\r\n    return `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;\r\n  }\r\n\r\n  // Initialize with sample training data if none exists\r\n  initializeWithSampleData(): void {\r\n    const existingData = this.getTrainingData();\r\n    if (existingData.length > 0) return;\r\n\r\n    // Get income category\r\n    const incomeCategory = this.categories.find(c => c.name.toLowerCase().includes('income'));\r\n    const expenseCategory = this.categories.find(c => c.name.toLowerCase().includes('expense'));\r\n    \r\n    if (!incomeCategory || !expenseCategory) return;\r\n\r\n    // Add some sample training data\r\n    const sampleData = [\r\n      { description: 'SALARY PAYMENT', amount: 5000, categoryId: incomeCategory.id },\r\n      { description: 'OFFICE RENT MONTHLY', amount: -1200, categoryId: expenseCategory.id },\r\n      { description: 'ELECTRICITY BILL', amount: -150, categoryId: expenseCategory.id },\r\n      { description: 'BANK CHARGES', amount: -25, categoryId: expenseCategory.id },\r\n      { description: 'CLIENT PAYMENT', amount: 2500, categoryId: incomeCategory.id }\r\n    ];\r\n\r\n    sampleData.forEach(data => {\r\n      this.addTrainingData(data.description, data.amount, data.categoryId);\r\n    });\r\n  }\r\n}\r\n\r\nexport const simpleMlService = new SimpleMlService(); "], "mappings": "AACA,SAASA,kBAAkB,QAAQ,sBAAsB;AACzD,SAASC,eAAe,QAAQ,mBAAmB;;AAEnD;AACA,MAAMC,eAAe,CAAC;EAQpBC,WAAWA,CAAA,EAAG;IAAA,KAPGC,eAAe,GAAG,kBAAkB;IAAA,KACpCC,sBAAsB,GAAG,sBAAsB;IAAA,KAExDC,UAAU,GAA0B,EAAE;IAAA,KACtCC,eAAe,GAAwB,IAAIC,GAAG,CAAC,CAAC;IAAA,KAChDC,eAAe,GAAwB,IAAID,GAAG,CAAC,CAAC;IAGtD,IAAI,CAACE,oBAAoB,CAAC,CAAC;EAC7B;;EAEA;EACQA,oBAAoBA,CAAA,EAAS;IACnC,IAAI,CAACJ,UAAU,GAAGL,eAAe,CAACU,gBAAgB,CAAC,CAAC;IACpD,IAAI,CAACL,UAAU,CAACM,OAAO,CAAC,CAACC,QAAQ,EAAEC,KAAK,KAAK;MAC3C,IAAI,CAACP,eAAe,CAACQ,GAAG,CAACF,QAAQ,CAACG,EAAE,EAAEF,KAAK,CAAC;MAC5C,IAAI,CAACL,eAAe,CAACM,GAAG,CAACD,KAAK,EAAED,QAAQ,CAACG,EAAE,CAAC;IAC9C,CAAC,CAAC;EACJ;;EAEA;EACQC,QAAQA,CAACC,IAAY,EAAY;IACvC,OAAOA,IAAI,CAACC,WAAW,CAAC,CAAC,CACtBC,OAAO,CAAC,UAAU,EAAE,GAAG,CAAC,CACxBC,KAAK,CAAC,KAAK,CAAC,CACZC,MAAM,CAACC,KAAK,IAAIA,KAAK,CAACC,MAAM,GAAG,CAAC,CAAC;EACtC;;EAEA;EACQC,eAAeA,CAACC,WAAmB,EAAEC,MAAc,EAAY;IACrE,MAAMC,MAAM,GAAG,IAAI,CAACX,QAAQ,CAACS,WAAW,CAAC;;IAEzC;IACA,MAAMG,iBAAyC,GAAG;MAChD,QAAQ,EAAE,GAAG;MAAE,MAAM,EAAE,GAAG;MAAE,SAAS,EAAE,GAAG;MAAE,UAAU,EAAE,GAAG;MAC3D,KAAK,EAAE,GAAG;MAAE,KAAK,EAAE,GAAG;MAAE,UAAU,EAAE,GAAG;MAAE,UAAU,EAAE,GAAG;MACxD,MAAM,EAAE,GAAG;MAAE,SAAS,EAAE,GAAG;MAAE,WAAW,EAAE,GAAG;MAAE,MAAM,EAAE,GAAG;MAC1D,SAAS,EAAE,GAAG;MAAE,YAAY,EAAE,GAAG;MAAE,UAAU,EAAE,GAAG;MAAE,QAAQ,EAAE,GAAG;MACjE,MAAM,EAAE,GAAG;MAAE,SAAS,EAAE,GAAG;MAAE,cAAc,EAAE,GAAG;MAAE,SAAS,EAAE,GAAG;MAChE,SAAS,EAAE,GAAG;MAAE,YAAY,EAAE,GAAG;MAAE,MAAM,EAAE,GAAG;MAAE,SAAS,EAAE;IAC7D,CAAC;;IAED;IACA,IAAIC,YAAY,GAAG,CAAC;IACpB,IAAIC,YAAY,GAAG,CAAC;IACpBH,MAAM,CAAChB,OAAO,CAACW,KAAK,IAAI;MACtB,IAAIM,iBAAiB,CAACN,KAAK,CAAC,EAAE;QAC5BO,YAAY,IAAID,iBAAiB,CAACN,KAAK,CAAC;QACxCQ,YAAY,EAAE;MAChB;IACF,CAAC,CAAC;IAEF,MAAMC,eAAe,GAAGD,YAAY,GAAG,CAAC,GAAGD,YAAY,GAAGC,YAAY,GAAG,CAAC;;IAE1E;IACA,MAAME,SAAS,GAAGC,IAAI,CAACC,GAAG,CAACR,MAAM,CAAC;IAClC,MAAMS,QAAQ,GAAGT,MAAM,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC;IACnC,MAAMU,OAAO,GAAGV,MAAM,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC;IAClC,MAAMW,SAAS,GAAGJ,IAAI,CAACK,GAAG,CAACN,SAAS,GAAG,CAAC,CAAC,GAAG,EAAE;IAC9C,MAAMO,aAAa,GAAGP,SAAS,GAAG,GAAG,GAAG,CAAC,GAAG,CAAC;IAC7C,MAAMQ,cAAc,GAAGR,SAAS,IAAI,GAAG,IAAIA,SAAS,GAAG,IAAI,GAAG,CAAC,GAAG,CAAC;IACnE,MAAMS,aAAa,GAAGT,SAAS,IAAI,IAAI,GAAG,CAAC,GAAG,CAAC;IAC/C,MAAMU,aAAa,GAAGV,SAAS,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC;;IAEjD;IACA,MAAMW,UAAU,GAAGlB,WAAW,CAACF,MAAM,GAAG,GAAG,CAAC,CAAC;IAC7C,MAAMqB,SAAS,GAAGjB,MAAM,CAACJ,MAAM,GAAG,EAAE,CAAC,CAAC;IACtC,MAAMsB,UAAU,GAAG,IAAI,CAACC,IAAI,CAACrB,WAAW,CAAC,GAAG,CAAC,GAAG,CAAC;IACjD,MAAMsB,YAAY,GAAG,eAAe,CAACD,IAAI,CAACrB,WAAW,CAACP,WAAW,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC;IAC5E,MAAM8B,OAAO,GAAG,oBAAoB,CAACF,IAAI,CAACrB,WAAW,CAAC,GAAG,CAAC,GAAG,CAAC;IAE9D,OAAO,CACLM,eAAe,EACfI,QAAQ,EACRC,OAAO,EACPC,SAAS,EACTE,aAAa,EACbC,cAAc,EACdC,aAAa,EACbC,aAAa,EACbC,UAAU,EACVC,SAAS,EACTC,UAAU,EACVE,YAAY,EACZC,OAAO,CACR;EACH;;EAEA;EACA,MAAMC,qBAAqBA,CAACxB,WAAmB,EAAEC,MAAc,EAAoC;IACjG,MAAMwB,QAAQ,GAAG,IAAI,CAAC1B,eAAe,CAACC,WAAW,EAAEC,MAAM,CAAC;IAC1D,MAAMyB,YAAY,GAAG,IAAI,CAACC,eAAe,CAAC,CAAC;IAE3C,IAAID,YAAY,CAAC5B,MAAM,KAAK,CAAC,EAAE;MAC7B,OAAO,IAAI;IACb;;IAEA;IACA,IAAI8B,SAA8B,GAAG,IAAI;IACzC,IAAIC,cAAc,GAAG,CAAC;IAEtBH,YAAY,CAACxC,OAAO,CAAC4C,IAAI,IAAI;MAC3B,MAAMC,UAAU,GAAG,IAAI,CAACC,mBAAmB,CAACP,QAAQ,EAAEK,IAAI,CAACL,QAAQ,CAAC;MACpE,IAAIM,UAAU,GAAGF,cAAc,EAAE;QAC/BA,cAAc,GAAGE,UAAU;QAC3BH,SAAS,GAAGE,IAAI;MAClB;IACF,CAAC,CAAC;IAEF,IAAIF,SAAS,IAAIC,cAAc,GAAG,GAAG,EAAE;MAAE;MACvC,OAAO;QACLI,UAAU,EAAEL,SAAS,CAACK,UAAU;QAChCC,UAAU,EAAEL,cAAc;QAC1BM,SAAS,EAAE,kBAAkB;QAC7BV,QAAQ,EAAE,CAAC,aAAa,EAAE,QAAQ,EAAE,UAAU,CAAC;QAC/CW,YAAY,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;MACvC,CAAC;IACH;IAEA,OAAO,IAAI;EACb;;EAEA;EACQN,mBAAmBA,CAACO,SAAmB,EAAEC,SAAmB,EAAU;IAC5E,IAAID,SAAS,CAACzC,MAAM,KAAK0C,SAAS,CAAC1C,MAAM,EAAE,OAAO,CAAC;IAEnD,IAAI2C,UAAU,GAAG,CAAC;IAClB,IAAIC,KAAK,GAAG,CAAC;IACb,IAAIC,KAAK,GAAG,CAAC;IAEb,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGL,SAAS,CAACzC,MAAM,EAAE8C,CAAC,EAAE,EAAE;MACzCH,UAAU,IAAIF,SAAS,CAACK,CAAC,CAAC,GAAGJ,SAAS,CAACI,CAAC,CAAC;MACzCF,KAAK,IAAIH,SAAS,CAACK,CAAC,CAAC,GAAGL,SAAS,CAACK,CAAC,CAAC;MACpCD,KAAK,IAAIH,SAAS,CAACI,CAAC,CAAC,GAAGJ,SAAS,CAACI,CAAC,CAAC;IACtC;IAEA,MAAMC,SAAS,GAAGrC,IAAI,CAACsC,IAAI,CAACJ,KAAK,CAAC,GAAGlC,IAAI,CAACsC,IAAI,CAACH,KAAK,CAAC;IACrD,OAAOE,SAAS,GAAG,CAAC,GAAGJ,UAAU,GAAGI,SAAS,GAAG,CAAC;EACnD;;EAEA;EACA,MAAME,UAAUA,CAACrB,YAA4B,EAA+C;IAC1F;IACA;IACA,IAAI,CAACsB,gBAAgB,CAACtB,YAAY,CAAC;;IAEnC;IACA,MAAMuB,oBAAoB,GAAG,IAAInE,GAAG,CAAiB,CAAC;IACtD4C,YAAY,CAACxC,OAAO,CAAC4C,IAAI,IAAI;MAC3B,MAAMoB,KAAK,GAAGD,oBAAoB,CAACE,GAAG,CAACrB,IAAI,CAACG,UAAU,CAAC,IAAI,CAAC;MAC5DgB,oBAAoB,CAAC5D,GAAG,CAACyC,IAAI,CAACG,UAAU,EAAEiB,KAAK,GAAG,CAAC,CAAC;IACtD,CAAC,CAAC;;IAEF;IACA,MAAMtE,UAAU,GAAGwE,KAAK,CAACC,IAAI,CAACJ,oBAAoB,CAACK,MAAM,CAAC,CAAC,CAAC;IAC5D,MAAMC,sBAAsB,GAAG3E,UAAU,CAAC4E,MAAM,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,GAAGC,CAAC,EAAE,CAAC,CAAC,GAAG9E,UAAU,CAACkB,MAAM;IACxF,MAAM6D,QAAQ,GAAG/E,UAAU,CAAC4E,MAAM,CAAC,CAACI,GAAG,EAAEV,KAAK,KAAKU,GAAG,GAAGpD,IAAI,CAACqD,GAAG,CAACX,KAAK,GAAGK,sBAAsB,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG3E,UAAU,CAACkB,MAAM;IAC5H,MAAMgE,kBAAkB,GAAGtD,IAAI,CAACuD,GAAG,CAACJ,QAAQ,GAAGJ,sBAAsB,EAAE,CAAC,CAAC;IACzE,MAAMS,QAAQ,GAAGxD,IAAI,CAACyD,GAAG,CAAC,GAAG,EAAE,IAAI,GAAGH,kBAAkB,GAAG,GAAG,CAAC;;IAE/D;IACA,MAAMI,KAAc,GAAG;MACrB5E,EAAE,EAAE,IAAI,CAAC6E,UAAU,CAAC,CAAC;MACrBC,IAAI,EAAE,+BAA+B;MACrCC,OAAO,EAAE,KAAK;MACdlC,SAAS,EAAE,kBAAkB;MAC7B6B,QAAQ;MACR5B,YAAY,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;MACtCgC,YAAY,EAAE5C,YAAY,CAAC5B,MAAM;MACjCyE,QAAQ,EAAE,IAAI;MACdC,SAAS,EAAEC,IAAI,CAACC,SAAS,CAAC;QAAEC,gBAAgB,EAAEjD,YAAY,CAAC5B,MAAM;QAAEkE;MAAS,CAAC;IAC/E,CAAC;IAED,MAAMY,MAAM,GAAG,IAAI,CAACC,YAAY,CAAC,CAAC;IAClC;IACAD,MAAM,CAAC1F,OAAO,CAAC4F,CAAC,IAAIA,CAAC,CAACP,QAAQ,GAAG,KAAK,CAAC;IACvCK,MAAM,CAACG,IAAI,CAACb,KAAK,CAAC;IAClB5F,kBAAkB,CAAC0G,SAAS,CAAC,IAAI,CAACtG,eAAe,EAAEkG,MAAM,CAAC;IAE1D,OAAO;MAAEZ,QAAQ;MAAEiB,IAAI,EAAE,CAAC,GAAGjB;IAAS,CAAC;EACzC;;EAEA;EACAa,YAAYA,CAAA,EAAc;IACxB,OAAOvG,kBAAkB,CAAC4G,QAAQ,CAAY,IAAI,CAACxG,eAAe,EAAE,EAAE,CAAC;EACzE;;EAEA;EACAyG,eAAeA,CAACnF,WAAmB,EAAEC,MAAc,EAAEgC,UAAkB,EAAQ;IAC7E,MAAMP,YAAY,GAAG,IAAI,CAACC,eAAe,CAAC,CAAC;IAE3C,MAAMyD,OAAqB,GAAG;MAC5B9F,EAAE,EAAE,IAAI,CAAC6E,UAAU,CAAC,CAAC;MACrBnE,WAAW;MACXC,MAAM;MACNgC,UAAU;MACVR,QAAQ,EAAE,IAAI,CAAC1B,eAAe,CAACC,WAAW,EAAEC,MAAM,CAAC;MACnDoF,WAAW,EAAE,IAAIhD,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;IACtC,CAAC;IAEDZ,YAAY,CAACqD,IAAI,CAACK,OAAO,CAAC;IAC1B,IAAI,CAACpC,gBAAgB,CAACtB,YAAY,CAAC;EACrC;;EAEA;EACAC,eAAeA,CAAA,EAAmB;IAChC,OAAOrD,kBAAkB,CAAC4G,QAAQ,CAAiB,IAAI,CAACvG,sBAAsB,EAAE,EAAE,CAAC;EACrF;;EAEA;EACQqE,gBAAgBA,CAAClB,IAAoB,EAAQ;IACnDxD,kBAAkB,CAAC0G,SAAS,CAAC,IAAI,CAACrG,sBAAsB,EAAEmD,IAAI,CAAC;EACjE;;EAEA;EACAwD,iBAAiBA,CAAA,EAAS;IACxB,IAAI,CAACtC,gBAAgB,CAAC,EAAE,CAAC;EAC3B;;EAEA;EACAuC,aAAaA,CAAA,EAAmF;IAC9F,MAAMX,MAAM,GAAG,IAAI,CAACC,YAAY,CAAC,CAAC;IAClC,MAAMW,WAAW,GAAGZ,MAAM,CAACa,IAAI,CAACX,CAAC,IAAIA,CAAC,CAACP,QAAQ,CAAC,IAAI,IAAI;IACxD,MAAMI,gBAAgB,GAAG,IAAI,CAAChD,eAAe,CAAC,CAAC,CAAC7B,MAAM;IAEtD,OAAO;MACL4F,WAAW,EAAEd,MAAM,CAAC9E,MAAM;MAC1B0F,WAAW;MACXb;IACF,CAAC;EACH;;EAEA;EACQR,UAAUA,CAAA,EAAW;IAC3B,OAAO,GAAG9B,IAAI,CAACsD,GAAG,CAAC,CAAC,IAAInF,IAAI,CAACoF,MAAM,CAAC,CAAC,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;EACnE;;EAEA;EACAC,wBAAwBA,CAAA,EAAS;IAC/B,MAAMC,YAAY,GAAG,IAAI,CAACrE,eAAe,CAAC,CAAC;IAC3C,IAAIqE,YAAY,CAAClG,MAAM,GAAG,CAAC,EAAE;;IAE7B;IACA,MAAMmG,cAAc,GAAG,IAAI,CAACrH,UAAU,CAAC6G,IAAI,CAACS,CAAC,IAAIA,CAAC,CAAC9B,IAAI,CAAC3E,WAAW,CAAC,CAAC,CAAC0G,QAAQ,CAAC,QAAQ,CAAC,CAAC;IACzF,MAAMC,eAAe,GAAG,IAAI,CAACxH,UAAU,CAAC6G,IAAI,CAACS,CAAC,IAAIA,CAAC,CAAC9B,IAAI,CAAC3E,WAAW,CAAC,CAAC,CAAC0G,QAAQ,CAAC,SAAS,CAAC,CAAC;IAE3F,IAAI,CAACF,cAAc,IAAI,CAACG,eAAe,EAAE;;IAEzC;IACA,MAAMC,UAAU,GAAG,CACjB;MAAErG,WAAW,EAAE,gBAAgB;MAAEC,MAAM,EAAE,IAAI;MAAEgC,UAAU,EAAEgE,cAAc,CAAC3G;IAAG,CAAC,EAC9E;MAAEU,WAAW,EAAE,qBAAqB;MAAEC,MAAM,EAAE,CAAC,IAAI;MAAEgC,UAAU,EAAEmE,eAAe,CAAC9G;IAAG,CAAC,EACrF;MAAEU,WAAW,EAAE,kBAAkB;MAAEC,MAAM,EAAE,CAAC,GAAG;MAAEgC,UAAU,EAAEmE,eAAe,CAAC9G;IAAG,CAAC,EACjF;MAAEU,WAAW,EAAE,cAAc;MAAEC,MAAM,EAAE,CAAC,EAAE;MAAEgC,UAAU,EAAEmE,eAAe,CAAC9G;IAAG,CAAC,EAC5E;MAAEU,WAAW,EAAE,gBAAgB;MAAEC,MAAM,EAAE,IAAI;MAAEgC,UAAU,EAAEgE,cAAc,CAAC3G;IAAG,CAAC,CAC/E;IAED+G,UAAU,CAACnH,OAAO,CAAC4C,IAAI,IAAI;MACzB,IAAI,CAACqD,eAAe,CAACrD,IAAI,CAAC9B,WAAW,EAAE8B,IAAI,CAAC7B,MAAM,EAAE6B,IAAI,CAACG,UAAU,CAAC;IACtE,CAAC,CAAC;EACJ;AACF;AAEA,OAAO,MAAMqE,eAAe,GAAG,IAAI9H,eAAe,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}