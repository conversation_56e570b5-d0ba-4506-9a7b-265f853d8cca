{"ast": null, "code": "/**\n * @license\n * Copyright 2019 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { util } from '@tensorflow/tfjs-core';\nexport function assertNotComplex(tensor, opName) {\n  if (!Array.isArray(tensor)) {\n    tensor = [tensor];\n  }\n  tensor.forEach(t => {\n    if (t != null) {\n      util.assert(t.dtype !== 'complex64', () => `${opName} does not support complex64 tensors in the CPU backend.`);\n    }\n  });\n}", "map": {"version": 3, "names": ["util", "assertNotComplex", "tensor", "opName", "Array", "isArray", "for<PERSON>ach", "t", "assert", "dtype"], "sources": ["C:\\tfjs-backend-cpu\\src\\cpu_util.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2019 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {TensorInfo, util} from '@tensorflow/tfjs-core';\n\nexport function assertNotComplex(\n    tensor: TensorInfo|TensorInfo[], opName: string): void {\n  if (!Array.isArray(tensor)) {\n    tensor = [tensor];\n  }\n  tensor.forEach(t => {\n    if (t != null) {\n      util.assert(\n          t.dtype !== 'complex64',\n          () => `${\n              opName} does not support complex64 tensors in the CPU backend.`);\n    }\n  });\n}\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAiBA,SAAoBA,IAAI,QAAO,uBAAuB;AAEtD,OAAM,SAAUC,gBAAgBA,CAC5BC,MAA+B,EAAEC,MAAc;EACjD,IAAI,CAACC,KAAK,CAACC,OAAO,CAACH,MAAM,CAAC,EAAE;IAC1BA,MAAM,GAAG,CAACA,MAAM,CAAC;;EAEnBA,MAAM,CAACI,OAAO,CAACC,CAAC,IAAG;IACjB,IAAIA,CAAC,IAAI,IAAI,EAAE;MACbP,IAAI,CAACQ,MAAM,CACPD,CAAC,CAACE,KAAK,KAAK,WAAW,EACvB,MAAM,GACFN,MAAM,yDAAyD,CAAC;;EAE5E,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}