{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.transformReply = exports.transformArguments = exports.FIRST_KEY_INDEX = void 0;\nexports.FIRST_KEY_INDEX = 2;\nfunction transformArguments(key, group, consumer) {\n  return ['XGROUP', 'CREATECONSUMER', key, group, consumer];\n}\nexports.transformArguments = transformArguments;\nvar generic_transformers_1 = require(\"./generic-transformers\");\nObject.defineProperty(exports, \"transformReply\", {\n  enumerable: true,\n  get: function () {\n    return generic_transformers_1.transformBooleanReply;\n  }\n});", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "transformReply", "transformArguments", "FIRST_KEY_INDEX", "key", "group", "consumer", "generic_transformers_1", "require", "enumerable", "get", "transformBooleanReply"], "sources": ["C:/tmsft/node_modules/@redis/client/dist/lib/commands/XGROUP_CREATECONSUMER.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.transformReply = exports.transformArguments = exports.FIRST_KEY_INDEX = void 0;\nexports.FIRST_KEY_INDEX = 2;\nfunction transformArguments(key, group, consumer) {\n    return ['XGROUP', 'CREATECONSUMER', key, group, consumer];\n}\nexports.transformArguments = transformArguments;\nvar generic_transformers_1 = require(\"./generic-transformers\");\nObject.defineProperty(exports, \"transformReply\", { enumerable: true, get: function () { return generic_transformers_1.transformBooleanReply; } });\n"], "mappings": "AAAA,YAAY;;AACZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEC,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7DD,OAAO,CAACE,cAAc,GAAGF,OAAO,CAACG,kBAAkB,GAAGH,OAAO,CAACI,eAAe,GAAG,KAAK,CAAC;AACtFJ,OAAO,CAACI,eAAe,GAAG,CAAC;AAC3B,SAASD,kBAAkBA,CAACE,GAAG,EAAEC,KAAK,EAAEC,QAAQ,EAAE;EAC9C,OAAO,CAAC,QAAQ,EAAE,gBAAgB,EAAEF,GAAG,EAAEC,KAAK,EAAEC,QAAQ,CAAC;AAC7D;AACAP,OAAO,CAACG,kBAAkB,GAAGA,kBAAkB;AAC/C,IAAIK,sBAAsB,GAAGC,OAAO,CAAC,wBAAwB,CAAC;AAC9DX,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,gBAAgB,EAAE;EAAEU,UAAU,EAAE,IAAI;EAAEC,GAAG,EAAE,SAAAA,CAAA,EAAY;IAAE,OAAOH,sBAAsB,CAACI,qBAAqB;EAAE;AAAE,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}