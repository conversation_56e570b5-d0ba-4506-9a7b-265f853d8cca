{"ast": null, "code": "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { cumsum } from '../../ops/cumsum';\nimport { getGlobalTensorClass } from '../../tensor';\ngetGlobalTensorClass().prototype.cumsum = function (axis, exclusive, reverse) {\n  this.throwIfDisposed();\n  return cumsum(this, axis, exclusive, reverse);\n};", "map": {"version": 3, "names": ["cumsum", "getGlobalTensorClass", "prototype", "axis", "exclusive", "reverse", "throwIfDisposed"], "sources": ["C:\\tfjs-core\\src\\public\\chained_ops\\cumsum.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport {cumsum} from '../../ops/cumsum';\nimport {getGlobalTensorClass, Tensor} from '../../tensor';\nimport {Rank} from '../../types';\n\ndeclare module '../../tensor' {\n  interface Tensor<R extends Rank = Rank> {\n    cumsum<R extends Rank>(\n        axis?: number, exclusive?: boolean, reverse?: boolean): Tensor<R>;\n  }\n}\n\ngetGlobalTensorClass().prototype.cumsum = function<R extends Rank>(\n    axis?: number, exclusive?: boolean, reverse?: boolean): Tensor<R> {\n  this.throwIfDisposed();\n  return cumsum(this, axis, exclusive, reverse);\n};\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAgBA,SAAQA,MAAM,QAAO,kBAAkB;AACvC,SAAQC,oBAAoB,QAAe,cAAc;AAUzDA,oBAAoB,EAAE,CAACC,SAAS,CAACF,MAAM,GAAG,UACtCG,IAAa,EAAEC,SAAmB,EAAEC,OAAiB;EACvD,IAAI,CAACC,eAAe,EAAE;EACtB,OAAON,MAAM,CAAC,IAAI,EAAEG,IAAI,EAAEC,SAAS,EAAEC,OAAO,CAAC;AAC/C,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}