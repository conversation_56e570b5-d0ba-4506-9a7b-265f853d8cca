{"ast": null, "code": "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { buffer, util } from '@tensorflow/tfjs-core';\nexport function bincountImpl(xVals, weightsVals, weightsDtype, weightsShape, size) {\n  const weightsSize = util.sizeFromShape(weightsShape);\n  const outVals = util.makeZerosTypedArray(size, weightsDtype);\n  for (let i = 0; i < xVals.length; i++) {\n    const value = xVals[i];\n    if (value < 0) {\n      throw new Error('Input x must be non-negative!');\n    }\n    if (value >= size) {\n      continue;\n    }\n    if (weightsSize > 0) {\n      outVals[value] += weightsVals[i];\n    } else {\n      outVals[value] += 1;\n    }\n  }\n  return outVals;\n}\nexport function bincountReduceImpl(xBuf, weightsBuf, size) {\n  let binaryOutput = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : false;\n  const numRows = xBuf.shape[0];\n  const numCols = xBuf.shape[1];\n  const outBuf = buffer([numRows, size], weightsBuf.dtype);\n  for (let i = 0; i < numRows; i++) {\n    for (let j = 0; j < numCols; j++) {\n      const value = xBuf.get(i, j);\n      if (value < 0) {\n        throw new Error('Input x must be non-negative!');\n      }\n      if (value >= size) {\n        continue;\n      }\n      if (binaryOutput) {\n        outBuf.set(1, i, value);\n      } else {\n        if (weightsBuf.size > 0) {\n          outBuf.set(outBuf.get(i, value) + weightsBuf.get(i, j), i, value);\n        } else {\n          outBuf.set(outBuf.get(i, value) + 1, i, value);\n        }\n      }\n    }\n  }\n  return outBuf;\n}", "map": {"version": 3, "names": ["buffer", "util", "bincountImpl", "xVals", "weightsVals", "weightsDtype", "weightsShape", "size", "weightsSize", "sizeFromShape", "outVals", "makeZerosTypedArray", "i", "length", "value", "Error", "bincountReduceImpl", "xBuf", "weightsBuf", "binaryOutput", "arguments", "undefined", "numRows", "shape", "numCols", "outBuf", "dtype", "j", "get", "set"], "sources": ["C:\\tfjs-backend-cpu\\src\\kernels\\Bincount_impl.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {buffer, DataType, Rank, TensorBuffer, TypedArray, util} from '@tensorflow/tfjs-core';\n\nexport function bincountImpl(\n    xVals: TypedArray, weightsVals: TypedArray, weightsDtype: DataType,\n    weightsShape: number[], size: number): TypedArray {\n  const weightsSize = util.sizeFromShape(weightsShape);\n  const outVals = util.makeZerosTypedArray(size, weightsDtype) as TypedArray;\n\n  for (let i = 0; i < xVals.length; i++) {\n    const value = xVals[i];\n    if (value < 0) {\n      throw new Error('Input x must be non-negative!');\n    }\n\n    if (value >= size) {\n      continue;\n    }\n\n    if (weightsSize > 0) {\n      outVals[value] += weightsVals[i];\n    } else {\n      outVals[value] += 1;\n    }\n  }\n\n  return outVals;\n}\n\nexport function bincountReduceImpl<R extends Rank>(\n    xBuf: TensorBuffer<R>, weightsBuf: TensorBuffer<R>, size: number,\n    binaryOutput = false): TensorBuffer<R> {\n  const numRows = xBuf.shape[0];\n  const numCols = xBuf.shape[1];\n\n  const outBuf = buffer([numRows, size], weightsBuf.dtype);\n\n  for (let i = 0; i < numRows; i++) {\n    for (let j = 0; j < numCols; j++) {\n      const value = xBuf.get(i, j);\n      if (value < 0) {\n        throw new Error('Input x must be non-negative!');\n      }\n\n      if (value >= size) {\n        continue;\n      }\n\n      if (binaryOutput) {\n        outBuf.set(1, i, value);\n      } else {\n        if (weightsBuf.size > 0) {\n          outBuf.set(outBuf.get(i, value) + weightsBuf.get(i, j), i, value);\n        } else {\n          outBuf.set(outBuf.get(i, value) + 1, i, value);\n        }\n      }\n    }\n  }\n\n  return outBuf as TensorBuffer<R>;\n}\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAiBA,SAAQA,MAAM,EAA4CC,IAAI,QAAO,uBAAuB;AAE5F,OAAM,SAAUC,YAAYA,CACxBC,KAAiB,EAAEC,WAAuB,EAAEC,YAAsB,EAClEC,YAAsB,EAAEC,IAAY;EACtC,MAAMC,WAAW,GAAGP,IAAI,CAACQ,aAAa,CAACH,YAAY,CAAC;EACpD,MAAMI,OAAO,GAAGT,IAAI,CAACU,mBAAmB,CAACJ,IAAI,EAAEF,YAAY,CAAe;EAE1E,KAAK,IAAIO,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGT,KAAK,CAACU,MAAM,EAAED,CAAC,EAAE,EAAE;IACrC,MAAME,KAAK,GAAGX,KAAK,CAACS,CAAC,CAAC;IACtB,IAAIE,KAAK,GAAG,CAAC,EAAE;MACb,MAAM,IAAIC,KAAK,CAAC,+BAA+B,CAAC;;IAGlD,IAAID,KAAK,IAAIP,IAAI,EAAE;MACjB;;IAGF,IAAIC,WAAW,GAAG,CAAC,EAAE;MACnBE,OAAO,CAACI,KAAK,CAAC,IAAIV,WAAW,CAACQ,CAAC,CAAC;KACjC,MAAM;MACLF,OAAO,CAACI,KAAK,CAAC,IAAI,CAAC;;;EAIvB,OAAOJ,OAAO;AAChB;AAEA,OAAM,SAAUM,kBAAkBA,CAC9BC,IAAqB,EAAEC,UAA2B,EAAEX,IAAY,EAC5C;EAAA,IAApBY,YAAY,GAAAC,SAAA,CAAAP,MAAA,QAAAO,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAG,KAAK;EACtB,MAAME,OAAO,GAAGL,IAAI,CAACM,KAAK,CAAC,CAAC,CAAC;EAC7B,MAAMC,OAAO,GAAGP,IAAI,CAACM,KAAK,CAAC,CAAC,CAAC;EAE7B,MAAME,MAAM,GAAGzB,MAAM,CAAC,CAACsB,OAAO,EAAEf,IAAI,CAAC,EAAEW,UAAU,CAACQ,KAAK,CAAC;EAExD,KAAK,IAAId,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGU,OAAO,EAAEV,CAAC,EAAE,EAAE;IAChC,KAAK,IAAIe,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,OAAO,EAAEG,CAAC,EAAE,EAAE;MAChC,MAAMb,KAAK,GAAGG,IAAI,CAACW,GAAG,CAAChB,CAAC,EAAEe,CAAC,CAAC;MAC5B,IAAIb,KAAK,GAAG,CAAC,EAAE;QACb,MAAM,IAAIC,KAAK,CAAC,+BAA+B,CAAC;;MAGlD,IAAID,KAAK,IAAIP,IAAI,EAAE;QACjB;;MAGF,IAAIY,YAAY,EAAE;QAChBM,MAAM,CAACI,GAAG,CAAC,CAAC,EAAEjB,CAAC,EAAEE,KAAK,CAAC;OACxB,MAAM;QACL,IAAII,UAAU,CAACX,IAAI,GAAG,CAAC,EAAE;UACvBkB,MAAM,CAACI,GAAG,CAACJ,MAAM,CAACG,GAAG,CAAChB,CAAC,EAAEE,KAAK,CAAC,GAAGI,UAAU,CAACU,GAAG,CAAChB,CAAC,EAAEe,CAAC,CAAC,EAAEf,CAAC,EAAEE,KAAK,CAAC;SAClE,MAAM;UACLW,MAAM,CAACI,GAAG,CAACJ,MAAM,CAACG,GAAG,CAAChB,CAAC,EAAEE,KAAK,CAAC,GAAG,CAAC,EAAEF,CAAC,EAAEE,KAAK,CAAC;;;;;EAMtD,OAAOW,MAAyB;AAClC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}