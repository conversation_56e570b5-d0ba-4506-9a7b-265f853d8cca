{"ast": null, "code": "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { ENGINE } from '../engine';\nimport { LRN } from '../kernel_names';\nimport { convertToTensor } from '../tensor_util_env';\nimport * as util from '../util';\nimport { op } from './operation';\nimport { reshape } from './reshape';\n/**\n * Normalizes the activation of a local neighborhood across or within\n * channels.\n *\n * @param x The input tensor. The 4-D input tensor is treated as a 3-D array\n *     of 1D vectors (along the last dimension), and each vector is\n *     normalized independently.\n * @param depthRadius The number of adjacent channels in the 1D normalization\n *     window.\n * @param bias A constant bias term for the basis.\n * @param alpha A scale factor, usually positive.\n * @param beta An exponent.\n *\n * @doc {heading: 'Operations', subheading: 'Normalization'}\n */\nfunction localResponseNormalization_(x) {\n  let depthRadius = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 5;\n  let bias = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 1;\n  let alpha = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : 1;\n  let beta = arguments.length > 4 && arguments[4] !== undefined ? arguments[4] : 0.5;\n  const $x = convertToTensor(x, 'x', 'localResponseNormalization');\n  util.assert($x.rank === 4 || $x.rank === 3, () => \"Error in localResponseNormalization: x must be rank 3 or 4 but got\\n               rank \".concat($x.rank, \".\"));\n  util.assert(util.isInt(depthRadius), () => \"Error in localResponseNormalization: depthRadius must be an \" + \"integer but got depthRadius \".concat(depthRadius, \".\"));\n  let x4D = $x;\n  let reshapedTo4D = false;\n  if ($x.rank === 3) {\n    reshapedTo4D = true;\n    x4D = reshape($x, [1, $x.shape[0], $x.shape[1], $x.shape[2]]);\n  }\n  const inputs = {\n    x: x4D\n  };\n  const attrs = {\n    depthRadius,\n    bias,\n    alpha,\n    beta\n  };\n  // tslint:disable-next-line: no-unnecessary-type-assertion\n  const res = ENGINE.runKernel(LRN, inputs, attrs);\n  if (reshapedTo4D) {\n    return reshape(res, [res.shape[1], res.shape[2], res.shape[3]]);\n  } else {\n    return res;\n  }\n}\nexport const localResponseNormalization = /* @__PURE__ */op({\n  localResponseNormalization_\n});", "map": {"version": 3, "names": ["ENGINE", "LRN", "convertToTensor", "util", "op", "reshape", "localResponseNormalization_", "x", "depthRadius", "arguments", "length", "undefined", "bias", "alpha", "beta", "$x", "assert", "rank", "concat", "isInt", "x4D", "reshapedTo4D", "shape", "inputs", "attrs", "res", "runKernel", "localResponseNormalization"], "sources": ["C:\\tfjs-core\\src\\ops\\local_response_normalization.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {ENGINE} from '../engine';\nimport {LRN, LRNAttrs, LRNInputs} from '../kernel_names';\nimport {NamedAttrMap} from '../kernel_registry';\nimport {Tensor3D, Tensor4D} from '../tensor';\nimport {NamedTensorMap} from '../tensor_types';\nimport {convertToTensor} from '../tensor_util_env';\nimport {TensorLike} from '../types';\nimport * as util from '../util';\n\nimport {op} from './operation';\nimport {reshape} from './reshape';\n\n/**\n * Normalizes the activation of a local neighborhood across or within\n * channels.\n *\n * @param x The input tensor. The 4-D input tensor is treated as a 3-D array\n *     of 1D vectors (along the last dimension), and each vector is\n *     normalized independently.\n * @param depthRadius The number of adjacent channels in the 1D normalization\n *     window.\n * @param bias A constant bias term for the basis.\n * @param alpha A scale factor, usually positive.\n * @param beta An exponent.\n *\n * @doc {heading: 'Operations', subheading: 'Normalization'}\n */\nfunction localResponseNormalization_<T extends Tensor3D|Tensor4D>(\n    x: T|TensorLike, depthRadius = 5, bias = 1, alpha = 1, beta = 0.5): T {\n  const $x = convertToTensor(x, 'x', 'localResponseNormalization');\n  util.assert(\n      $x.rank === 4 || $x.rank === 3,\n      () => `Error in localResponseNormalization: x must be rank 3 or 4 but got\n               rank ${$x.rank}.`);\n  util.assert(\n      util.isInt(depthRadius),\n      () => `Error in localResponseNormalization: depthRadius must be an ` +\n          `integer but got depthRadius ${depthRadius}.`);\n\n  let x4D = $x as Tensor4D;\n  let reshapedTo4D = false;\n  if ($x.rank === 3) {\n    reshapedTo4D = true;\n    x4D = reshape($x, [1, $x.shape[0], $x.shape[1], $x.shape[2]]);\n  }\n\n  const inputs: LRNInputs = {x: x4D};\n\n  const attrs: LRNAttrs = {depthRadius, bias, alpha, beta};\n\n  // tslint:disable-next-line: no-unnecessary-type-assertion\n  const res = ENGINE.runKernel(\n                  LRN, inputs as unknown as NamedTensorMap,\n                  attrs as unknown as NamedAttrMap) as T;\n\n  if (reshapedTo4D) {\n    return reshape(res, [res.shape[1], res.shape[2], res.shape[3]]) as T;\n  } else {\n    return res;\n  }\n}\n\nexport const localResponseNormalization = /* @__PURE__ */ op({localResponseNormalization_});\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAiBA,SAAQA,MAAM,QAAO,WAAW;AAChC,SAAQC,GAAG,QAA4B,iBAAiB;AAIxD,SAAQC,eAAe,QAAO,oBAAoB;AAElD,OAAO,KAAKC,IAAI,MAAM,SAAS;AAE/B,SAAQC,EAAE,QAAO,aAAa;AAC9B,SAAQC,OAAO,QAAO,WAAW;AAEjC;;;;;;;;;;;;;;;AAeA,SAASC,2BAA2BA,CAChCC,CAAe,EAAkD;EAAA,IAAhDC,WAAW,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC;EAAA,IAAEG,IAAI,GAAAH,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC;EAAA,IAAEI,KAAK,GAAAJ,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC;EAAA,IAAEK,IAAI,GAAAL,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,GAAG;EACnE,MAAMM,EAAE,GAAGb,eAAe,CAACK,CAAC,EAAE,GAAG,EAAE,4BAA4B,CAAC;EAChEJ,IAAI,CAACa,MAAM,CACPD,EAAE,CAACE,IAAI,KAAK,CAAC,IAAIF,EAAE,CAACE,IAAI,KAAK,CAAC,EAC9B,iGAAAC,MAAA,CACgBH,EAAE,CAACE,IAAI,MAAG,CAAC;EAC/Bd,IAAI,CAACa,MAAM,CACPb,IAAI,CAACgB,KAAK,CAACX,WAAW,CAAC,EACvB,MAAM,gGAAAU,MAAA,CAC6BV,WAAW,MAAG,CAAC;EAEtD,IAAIY,GAAG,GAAGL,EAAc;EACxB,IAAIM,YAAY,GAAG,KAAK;EACxB,IAAIN,EAAE,CAACE,IAAI,KAAK,CAAC,EAAE;IACjBI,YAAY,GAAG,IAAI;IACnBD,GAAG,GAAGf,OAAO,CAACU,EAAE,EAAE,CAAC,CAAC,EAAEA,EAAE,CAACO,KAAK,CAAC,CAAC,CAAC,EAAEP,EAAE,CAACO,KAAK,CAAC,CAAC,CAAC,EAAEP,EAAE,CAACO,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;;EAG/D,MAAMC,MAAM,GAAc;IAAChB,CAAC,EAAEa;EAAG,CAAC;EAElC,MAAMI,KAAK,GAAa;IAAChB,WAAW;IAAEI,IAAI;IAAEC,KAAK;IAAEC;EAAI,CAAC;EAExD;EACA,MAAMW,GAAG,GAAGzB,MAAM,CAAC0B,SAAS,CACZzB,GAAG,EAAEsB,MAAmC,EACxCC,KAAgC,CAAM;EAEtD,IAAIH,YAAY,EAAE;IAChB,OAAOhB,OAAO,CAACoB,GAAG,EAAE,CAACA,GAAG,CAACH,KAAK,CAAC,CAAC,CAAC,EAAEG,GAAG,CAACH,KAAK,CAAC,CAAC,CAAC,EAAEG,GAAG,CAACH,KAAK,CAAC,CAAC,CAAC,CAAC,CAAM;GACrE,MAAM;IACL,OAAOG,GAAG;;AAEd;AAEA,OAAO,MAAME,0BAA0B,GAAG,eAAgBvB,EAAE,CAAC;EAACE;AAA2B,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}