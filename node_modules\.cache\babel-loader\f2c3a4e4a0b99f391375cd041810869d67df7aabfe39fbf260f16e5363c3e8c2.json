{"ast": null, "code": "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { ENGINE } from '../engine';\nimport { Atan2 } from '../kernel_names';\nimport { makeTypesMatch } from '../tensor_util';\nimport { convertToTensor } from '../tensor_util_env';\nimport { op } from './operation';\n/**\n * Computes arctangent of `tf.Tensor`s a / b element-wise: `atan2(a, b)`.\n * Supports broadcasting.\n *\n * ```js\n * const a = tf.tensor1d([1.0, 1.0, -1.0, .7]);\n * const b = tf.tensor1d([2.0, 13.0, 3.5, .21]);\n *\n * tf.atan2(a, b).print()\n * ```\n *\n * @param a The first tensor.\n * @param b The second tensor. Must have the same dtype as `a`.\n *\n * @doc {heading: 'Operations', subheading: 'Basic math'}\n */\nfunction atan2_(a, b) {\n  let $a = convertToTensor(a, 'a', 'atan2');\n  let $b = convertToTensor(b, 'b', 'atan2');\n  [$a, $b] = makeTypesMatch($a, $b);\n  const inputs = {\n    a: $a,\n    b: $b\n  };\n  return ENGINE.runKernel(Atan2, inputs);\n}\nexport const atan2 = /* @__PURE__ */op({\n  atan2_\n});", "map": {"version": 3, "names": ["ENGINE", "Atan2", "makeTypesMatch", "convertToTensor", "op", "atan2_", "a", "b", "$a", "$b", "inputs", "runKernel", "atan2"], "sources": ["C:\\tfjs-core\\src\\ops\\atan2.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {ENGINE} from '../engine';\nimport {Atan2, Atan2Inputs} from '../kernel_names';\nimport {Tensor} from '../tensor';\nimport {NamedTensorMap} from '../tensor_types';\nimport {makeTypesMatch} from '../tensor_util';\nimport {convertToTensor} from '../tensor_util_env';\nimport {TensorLike} from '../types';\n\nimport {op} from './operation';\n\n/**\n * Computes arctangent of `tf.Tensor`s a / b element-wise: `atan2(a, b)`.\n * Supports broadcasting.\n *\n * ```js\n * const a = tf.tensor1d([1.0, 1.0, -1.0, .7]);\n * const b = tf.tensor1d([2.0, 13.0, 3.5, .21]);\n *\n * tf.atan2(a, b).print()\n * ```\n *\n * @param a The first tensor.\n * @param b The second tensor. Must have the same dtype as `a`.\n *\n * @doc {heading: 'Operations', subheading: 'Basic math'}\n */\nfunction atan2_<T extends Tensor>(\n    a: Tensor|TensorLike, b: Tensor|TensorLike): T {\n  let $a = convertToTensor(a, 'a', 'atan2');\n  let $b = convertToTensor(b, 'b', 'atan2');\n  [$a, $b] = makeTypesMatch($a, $b);\n\n  const inputs: Atan2Inputs = {a: $a, b: $b};\n\n  return ENGINE.runKernel(Atan2, inputs as unknown as NamedTensorMap);\n}\n\nexport const atan2 = /* @__PURE__ */ op({atan2_});\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAiBA,SAAQA,MAAM,QAAO,WAAW;AAChC,SAAQC,KAAK,QAAoB,iBAAiB;AAGlD,SAAQC,cAAc,QAAO,gBAAgB;AAC7C,SAAQC,eAAe,QAAO,oBAAoB;AAGlD,SAAQC,EAAE,QAAO,aAAa;AAE9B;;;;;;;;;;;;;;;;AAgBA,SAASC,MAAMA,CACXC,CAAoB,EAAEC,CAAoB;EAC5C,IAAIC,EAAE,GAAGL,eAAe,CAACG,CAAC,EAAE,GAAG,EAAE,OAAO,CAAC;EACzC,IAAIG,EAAE,GAAGN,eAAe,CAACI,CAAC,EAAE,GAAG,EAAE,OAAO,CAAC;EACzC,CAACC,EAAE,EAAEC,EAAE,CAAC,GAAGP,cAAc,CAACM,EAAE,EAAEC,EAAE,CAAC;EAEjC,MAAMC,MAAM,GAAgB;IAACJ,CAAC,EAAEE,EAAE;IAAED,CAAC,EAAEE;EAAE,CAAC;EAE1C,OAAOT,MAAM,CAACW,SAAS,CAACV,KAAK,EAAES,MAAmC,CAAC;AACrE;AAEA,OAAO,MAAME,KAAK,GAAG,eAAgBR,EAAE,CAAC;EAACC;AAAM,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}