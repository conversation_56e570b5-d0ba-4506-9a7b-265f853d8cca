{"ast": null, "code": "/**\n * @license\n * Copyright 2018 Google LLC\n *\n * Use of this source code is governed by an MIT-style\n * license that can be found in the LICENSE file or at\n * https://opensource.org/licenses/MIT.\n * =============================================================================\n */\n/* Original source: utils/generic_utils.py */\nimport { util } from '@tensorflow/tfjs-core';\nimport { AssertionError, ValueError } from '../errors';\n// tslint:enable\n/**\n * If `value` is an Array, equivalent to Python's `value * numValues`.\n * If `value` is not an Array, equivalent to Python's `[value] * numValues`\n */\n// tslint:disable-next-line:no-any\nexport function pyListRepeat(value, numValues) {\n  if (Array.isArray(value)) {\n    // tslint:disable-next-line:no-any\n    let newArray = [];\n    for (let i = 0; i < numValues; i++) {\n      newArray = newArray.concat(value);\n    }\n    return newArray;\n  } else {\n    const newArray = new Array(numValues);\n    newArray.fill(value);\n    return newArray;\n  }\n}\nexport function assert(val, message) {\n  if (!val) {\n    throw new AssertionError(message);\n  }\n}\n/**\n * Count the number of elements of the `array` that are equal to `reference`.\n */\nexport function count(array, refernce) {\n  let counter = 0;\n  for (const item of array) {\n    if (item === refernce) {\n      counter++;\n    }\n  }\n  return counter;\n}\n/**\n * If an array is of length 1, just return the first element. Otherwise, return\n * the full array.\n * @param tensors\n */\nexport function singletonOrArray(xs) {\n  if (xs.length === 1) {\n    return xs[0];\n  }\n  return xs;\n}\n/**\n * Normalizes a list/tensor into a list.\n *\n * If a tensor is passed, we return\n * a list of size 1 containing the tensor.\n *\n * @param x target object to be normalized.\n */\n// tslint:disable-next-line:no-any\nexport function toList(x) {\n  if (Array.isArray(x)) {\n    return x;\n  }\n  return [x];\n}\n/**\n * Generate a UID for a list\n */\n// tslint:disable-next-line:no-any\nexport function objectListUid(objs) {\n  const objectList = toList(objs);\n  let retVal = '';\n  for (const obj of objectList) {\n    if (obj.id == null) {\n      throw new ValueError(`Object ${obj} passed to objectListUid without an id`);\n    }\n    if (retVal !== '') {\n      retVal = retVal + ', ';\n    }\n    retVal = `${retVal}${Math.abs(obj.id)}`;\n  }\n  return retVal;\n}\n/**\n * Converts string to snake-case.\n * @param name\n */\nexport function toSnakeCase(name) {\n  const intermediate = name.replace(/(.)([A-Z][a-z0-9]+)/g, '$1_$2');\n  const insecure = intermediate.replace(/([a-z])([A-Z])/g, '$1_$2').toLowerCase();\n  /*\n   If the class is private the name starts with \"_\" which is not secure\n   for creating scopes. We prefix the name with \"private\" in this case.\n   */\n  if (insecure[0] !== '_') {\n    return insecure;\n  }\n  return 'private' + insecure;\n}\nexport function toCamelCase(identifier) {\n  // quick return for empty string or single character strings\n  if (identifier.length <= 1) {\n    return identifier;\n  }\n  // Check for the underscore indicating snake_case\n  if (identifier.indexOf('_') === -1) {\n    return identifier;\n  }\n  return identifier.replace(/[_]+(\\w|$)/g, (m, p1) => p1.toUpperCase());\n}\n// tslint:disable-next-line:no-any\nlet _GLOBAL_CUSTOM_OBJECTS = {};\nexport function serializeKerasObject(instance) {\n  if (instance === null || instance === undefined) {\n    return null;\n  }\n  const dict = {};\n  dict['className'] = instance.getClassName();\n  dict['config'] = instance.getConfig();\n  return dict;\n}\n/**\n * Replace ndarray-style scalar objects in serialization objects with numbers.\n *\n * Background: In some versions of tf.keras, certain scalar values in the HDF5\n * model save file can be serialized as: `{'type': 'ndarray', 'value': num}`,\n * where in `num` is a plain number. This method converts such serialization\n * to a `number`.\n *\n * @param config The keras-format serialization object to be processed\n *   (in place).\n */\nfunction convertNDArrayScalarsInConfig(config) {\n  if (config == null || typeof config !== 'object') {\n    return;\n  } else if (Array.isArray(config)) {\n    config.forEach(configItem => convertNDArrayScalarsInConfig(configItem));\n  } else {\n    const fields = Object.keys(config);\n    for (const field of fields) {\n      const value = config[field];\n      if (value != null && typeof value === 'object') {\n        if (!Array.isArray(value) && value['type'] === 'ndarray' && typeof value['value'] === 'number') {\n          config[field] = value['value'];\n        } else {\n          convertNDArrayScalarsInConfig(value);\n        }\n      }\n    }\n  }\n}\n/**\n * Deserialize a saved Keras Object\n * @param identifier either a string ID or a saved Keras dictionary\n * @param moduleObjects a list of Python class names to object constructors\n * @param customObjects a list of Python class names to object constructors\n * @param printableModuleName debug text for the object being reconstituted\n * @param fastWeightInit Optional flag to use fast weight initialization\n *   during deserialization. This is applicable to cases in which\n *   the initialization will be immediately overwritten by loaded weight\n *   values. Default: `false`.\n * @returns a TensorFlow.js Layers object\n */\n// tslint:disable:no-any\nexport function deserializeKerasObject(identifier, moduleObjects = {}, customObjects = {}, printableModuleName = 'object', fastWeightInit = false) {\n  // tslint:enable\n  if (typeof identifier === 'string') {\n    const functionName = identifier;\n    let fn;\n    if (functionName in customObjects) {\n      fn = customObjects[functionName];\n    } else if (functionName in _GLOBAL_CUSTOM_OBJECTS) {\n      fn = _GLOBAL_CUSTOM_OBJECTS[functionName];\n    } else {\n      fn = moduleObjects[functionName];\n      if (fn == null) {\n        throw new ValueError(`Unknown ${printableModuleName}: ${identifier}. ` + `This may be due to one of the following reasons:\\n` + `1. The ${printableModuleName} is defined in Python, in which ` + `case it needs to be ported to TensorFlow.js or your JavaScript ` + `code.\\n` + `2. The custom ${printableModuleName} is defined in JavaScript, ` + `but is not registered properly with ` + `tf.serialization.registerClass().`);\n        // TODO(cais): Add link to tutorial page on custom layers.\n      }\n    }\n    return fn;\n  } else {\n    // In this case we are dealing with a Keras config dictionary.\n    const config = identifier;\n    if (config['className'] == null || config['config'] == null) {\n      throw new ValueError(`${printableModuleName}: Improper config format: ` + `${JSON.stringify(config)}.\\n` + `'className' and 'config' must set.`);\n    }\n    const className = config['className'];\n    let cls, fromConfig;\n    if (className in customObjects) {\n      [cls, fromConfig] = customObjects[className];\n    } else if (className in _GLOBAL_CUSTOM_OBJECTS) {\n      [cls, fromConfig] = _GLOBAL_CUSTOM_OBJECTS['className'];\n    } else if (className in moduleObjects) {\n      [cls, fromConfig] = moduleObjects[className];\n    }\n    if (cls == null) {\n      throw new ValueError(`Unknown ${printableModuleName}: ${className}. ` + `This may be due to one of the following reasons:\\n` + `1. The ${printableModuleName} is defined in Python, in which ` + `case it needs to be ported to TensorFlow.js or your JavaScript ` + `code.\\n` + `2. The custom ${printableModuleName} is defined in JavaScript, ` + `but is not registered properly with ` + `tf.serialization.registerClass().`);\n      // TODO(cais): Add link to tutorial page on custom layers.\n    }\n    if (fromConfig != null) {\n      // Porting notes: Instead of checking to see whether fromConfig accepts\n      // customObjects, we create a customObjects dictionary and tack it on to\n      // config['config'] as config['config'].customObjects. Objects can use it,\n      // if they want.\n      // tslint:disable-next-line:no-any\n      const customObjectsCombined = {};\n      for (const key of Object.keys(_GLOBAL_CUSTOM_OBJECTS)) {\n        customObjectsCombined[key] = _GLOBAL_CUSTOM_OBJECTS[key];\n      }\n      for (const key of Object.keys(customObjects)) {\n        customObjectsCombined[key] = customObjects[key];\n      }\n      // Add the customObjects to config\n      const nestedConfig = config['config'];\n      nestedConfig['customObjects'] = customObjectsCombined;\n      const backupCustomObjects = Object.assign({}, _GLOBAL_CUSTOM_OBJECTS);\n      for (const key of Object.keys(customObjects)) {\n        _GLOBAL_CUSTOM_OBJECTS[key] = customObjects[key];\n      }\n      convertNDArrayScalarsInConfig(config['config']);\n      const returnObj = fromConfig(cls, config['config'], customObjects, fastWeightInit);\n      _GLOBAL_CUSTOM_OBJECTS = Object.assign({}, backupCustomObjects);\n      return returnObj;\n    } else {\n      // Then `cls` may be a function returning a class.\n      // In this case by convention `config` holds\n      // the kwargs of the function.\n      const backupCustomObjects = Object.assign({}, _GLOBAL_CUSTOM_OBJECTS);\n      for (const key of Object.keys(customObjects)) {\n        _GLOBAL_CUSTOM_OBJECTS[key] = customObjects[key];\n      }\n      // In python this is **config['config'], for tfjs-layers we require\n      // classes that use this fall-through construction method to take\n      // a config interface that mimics the expansion of named parameters.\n      const returnObj = new cls(config['config']);\n      _GLOBAL_CUSTOM_OBJECTS = Object.assign({}, backupCustomObjects);\n      return returnObj;\n    }\n  }\n}\n/**\n * Compares two numbers for sorting.\n * @param a\n * @param b\n */\nexport function numberCompare(a, b) {\n  return a < b ? -1 : a > b ? 1 : 0;\n}\n/**\n * Comparison of two numbers for reverse sorting.\n * @param a\n * @param b\n */\nexport function reverseNumberCompare(a, b) {\n  return -1 * numberCompare(a, b);\n}\n/**\n * Convert a string into the corresponding DType.\n * @param dtype\n * @returns An instance of DType.\n */\nexport function stringToDType(dtype) {\n  switch (dtype) {\n    case 'float32':\n      return 'float32';\n    default:\n      throw new ValueError(`Invalid dtype: ${dtype}`);\n  }\n}\n/**\n * Test the element-by-element equality of two Arrays of strings.\n * @param xs First array of strings.\n * @param ys Second array of strings.\n * @returns Wether the two arrays are all equal, element by element.\n */\nexport function stringsEqual(xs, ys) {\n  if (xs == null || ys == null) {\n    return xs === ys;\n  }\n  if (xs.length !== ys.length) {\n    return false;\n  }\n  for (let i = 0; i < xs.length; ++i) {\n    if (xs[i] !== ys[i]) {\n      return false;\n    }\n  }\n  return true;\n}\n/**\n * Get the unique elements of an array.\n * @param xs Array.\n * @returns An Array consisting of the unique elements in `xs`.\n */\nexport function unique(xs) {\n  if (xs == null) {\n    return xs;\n  }\n  const out = [];\n  // TODO(cais): Maybe improve performance by sorting.\n  for (const x of xs) {\n    if (out.indexOf(x) === -1) {\n      out.push(x);\n    }\n  }\n  return out;\n}\n/**\n * Determine if an Object is empty (i.e., does not have own properties).\n * @param obj Object\n * @returns Whether the Object is empty.\n * @throws ValueError: If object is `null` or `undefined`.\n */\nexport function isObjectEmpty(obj) {\n  if (obj == null) {\n    throw new ValueError(`Invalid value in obj: ${JSON.stringify(obj)}`);\n  }\n  for (const key in obj) {\n    if (obj.hasOwnProperty(key)) {\n      return false;\n    }\n  }\n  return true;\n}\n/**\n * Helper function used to build type union/enum run-time checkers.\n * @param values The list of allowed values.\n * @param label A string name for the type\n * @param value The value to test.\n * @throws ValueError: If the value is not in values nor `undefined`/`null`.\n */\nexport function checkStringTypeUnionValue(values, label, value) {\n  if (value == null) {\n    return;\n  }\n  if (values.indexOf(value) < 0) {\n    throw new ValueError(`${value} is not a valid ${label}.  Valid values are ${values} or null/undefined.`);\n  }\n}\n/**\n * Helper function for verifying the types of inputs.\n *\n * Ensures that the elements of `x` are all of type `expectedType`.\n * Also verifies that the length of `x` is within bounds.\n *\n * @param x Object to test.\n * @param expectedType The string expected type of all of the elements in the\n * Array.\n * @param minLength Return false if x.length is less than this.\n * @param maxLength Return false if x.length is greater than this.\n * @returns true if and only if `x` is an `Array<expectedType>` with\n * length >= `minLength` and <= `maxLength`.\n */\n// tslint:disable:no-any\nexport function checkArrayTypeAndLength(x, expectedType, minLength = 0, maxLength = Infinity) {\n  assert(minLength >= 0);\n  assert(maxLength >= minLength);\n  return Array.isArray(x) && x.length >= minLength && x.length <= maxLength && x.every(e => typeof e === expectedType);\n}\n// tslint:enable:no-any\n/**\n * Assert that a value or an array of value are positive integer.\n *\n * @param value The value being asserted on. May be a single number or an array\n *   of numbers.\n * @param name Name of the value, used to make the error message.\n */\nexport function assertPositiveInteger(value, name) {\n  if (Array.isArray(value)) {\n    util.assert(value.length > 0, () => `${name} is unexpectedly an empty array.`);\n    value.forEach((v, i) => assertPositiveInteger(v, `element ${i + 1} of ${name}`));\n  } else {\n    util.assert(Number.isInteger(value) && value > 0, () => `Expected ${name} to be a positive integer, but got ` + `${formatAsFriendlyString(value)}.`);\n  }\n}\n/**\n * Format a value into a display-friendly, human-readable fashion.\n *\n * - `null` is formatted as `'null'`\n * - Strings are formated with flanking pair of quotes.\n * - Arrays are formatted with flanking pair of square brackets.\n *\n * @param value The value to display.\n * @return Formatted string.\n */\n// tslint:disable-next-line:no-any\nexport function formatAsFriendlyString(value) {\n  if (value === null) {\n    return 'null';\n  } else if (Array.isArray(value)) {\n    return '[' + value.map(v => formatAsFriendlyString(v)).join(',') + ']';\n  } else if (typeof value === 'string') {\n    return `\"${value}\"`;\n  } else {\n    return `${value}`;\n  }\n}\n/**\n * Returns a function `f2` (decorator) which wraps the original function\n * `f`. `f2` guarantees that `f` can be called at most once\n * every `waitMs` ms. If `f2` is called more often, it will return\n * the last returned result of `f`.\n *\n * @param f The original function `f` to wrap.\n * @param waitMs The time between two consecutive calls to `f` in ms.\n */\nexport function debounce(f, waitMs, nowFunc) {\n  let lastTime = nowFunc != null ? nowFunc() : util.now();\n  let lastResult;\n  const f2 = (...args) => {\n    const now = nowFunc != null ? nowFunc() : util.now();\n    if (now - lastTime < waitMs) {\n      return lastResult;\n    }\n    lastTime = now;\n    lastResult = f(...args);\n    return lastResult;\n  };\n  return f2;\n}\n/**\n * Returns the fusable activation given a layers identifier.\n *\n * @param activationName The layers identifier string.\n * @return The name of the fusable activation.\n */\nexport function mapActivationToFusedKernel(activationName) {\n  if (activationName === 'relu') {\n    return 'relu';\n  }\n  if (activationName === 'linear') {\n    return 'linear';\n  }\n  if (activationName === 'elu') {\n    return 'elu';\n  }\n  return null;\n}\n/**\n * Returns the cartesian product of sets of values.\n * This works the same as itertools.product in Python.\n *\n * Example:\n *\n * filters = [128, 256, 512]\n * paddings = ['same', 'valid']\n *\n * product = [ [128, 'same'], [128, 'valid'], [256, 'same'], [256, 'valid'],\n * [512, 'same'], [512, 'valid']]\n *\n * @param arrayOfValues List/array of values.\n * @return The cartesian product.\n */\nexport function getCartesianProductOfValues(...arrayOfValues) {\n  assert(arrayOfValues.length > 0, 'arrayOfValues is empty');\n  for (const values of arrayOfValues) {\n    assert(Array.isArray(values), 'one of the values is not an array');\n    assert(values.length > 0, 'one of the values is empty');\n  }\n  return arrayOfValues.reduce((products, values) => {\n    if (products.length === 0) {\n      return values.map(value => [value]);\n    }\n    return values.map(value => {\n      return products.map(prevValue => [...prevValue, value]);\n    }).reduce((flattenedProduct, unflattenedProduct) => {\n      return flattenedProduct.concat(unflattenedProduct);\n    }, []);\n  }, []);\n}", "map": {"version": 3, "names": ["util", "AssertionError", "ValueError", "pyListRepeat", "value", "numValues", "Array", "isArray", "newArray", "i", "concat", "fill", "assert", "val", "message", "count", "array", "refernce", "counter", "item", "singletonOrArray", "xs", "length", "toList", "x", "objectListUid", "objs", "objectList", "retVal", "obj", "id", "Math", "abs", "toSnakeCase", "name", "intermediate", "replace", "insecure", "toLowerCase", "toCamelCase", "identifier", "indexOf", "m", "p1", "toUpperCase", "_GLOBAL_CUSTOM_OBJECTS", "serializeKerasObject", "instance", "undefined", "dict", "getClassName", "getConfig", "convertNDArrayScalarsInConfig", "config", "for<PERSON>ach", "configItem", "fields", "Object", "keys", "field", "deserializeKerasObject", "moduleObjects", "customObjects", "printableModuleName", "fastWeightInit", "functionName", "fn", "JSON", "stringify", "className", "cls", "fromConfig", "customObjectsCombined", "key", "nestedConfig", "backupCustomObjects", "assign", "returnObj", "numberCompare", "a", "b", "reverseNumberCompare", "stringToDType", "dtype", "stringsEqual", "ys", "unique", "out", "push", "isObjectEmpty", "hasOwnProperty", "checkStringTypeUnionValue", "values", "label", "checkArrayTypeAndLength", "expectedType", "<PERSON><PERSON><PERSON><PERSON>", "max<PERSON><PERSON><PERSON>", "Infinity", "every", "e", "assertPositiveInteger", "v", "Number", "isInteger", "formatAsFriendlyString", "map", "join", "debounce", "f", "waitMs", "nowFunc", "lastTime", "now", "lastResult", "f2", "args", "mapActivationToFusedKernel", "activationName", "getCartesianProductOfValues", "arrayOfValues", "reduce", "products", "prevValue", "flattenedProduct", "unflattenedProduct"], "sources": ["C:\\tfjs-layers\\src\\utils\\generic_utils.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2018 Google LLC\n *\n * Use of this source code is governed by an MIT-style\n * license that can be found in the LICENSE file or at\n * https://opensource.org/licenses/MIT.\n * =============================================================================\n */\n\n/* Original source: utils/generic_utils.py */\n\nimport {DataType, fused, serialization, util} from '@tensorflow/tfjs-core';\n\nimport {AssertionError, ValueError} from '../errors';\n\n// tslint:enable\n\n/**\n * If `value` is an Array, equivalent to Python's `value * numValues`.\n * If `value` is not an Array, equivalent to Python's `[value] * numValues`\n */\n// tslint:disable-next-line:no-any\nexport function pyListRepeat(value: any, numValues: number): any[] {\n  if (Array.isArray(value)) {\n    // tslint:disable-next-line:no-any\n    let newArray: any[] = [];\n    for (let i = 0; i < numValues; i++) {\n      newArray = newArray.concat(value);\n    }\n    return newArray;\n  } else {\n    const newArray = new Array(numValues);\n    newArray.fill(value);\n    return newArray;\n  }\n}\n\nexport function assert(val: boolean, message?: string): void {\n  if (!val) {\n    throw new AssertionError(message);\n  }\n}\n\n/**\n * Count the number of elements of the `array` that are equal to `reference`.\n */\nexport function count<T>(array: T[], refernce: T) {\n  let counter = 0;\n  for (const item of array) {\n    if (item === refernce) {\n      counter++;\n    }\n  }\n  return counter;\n}\n\n/**\n * If an array is of length 1, just return the first element. Otherwise, return\n * the full array.\n * @param tensors\n */\nexport function singletonOrArray<T>(xs: T[]): T|T[] {\n  if (xs.length === 1) {\n    return xs[0];\n  }\n  return xs;\n}\n\n/**\n * Normalizes a list/tensor into a list.\n *\n * If a tensor is passed, we return\n * a list of size 1 containing the tensor.\n *\n * @param x target object to be normalized.\n */\n// tslint:disable-next-line:no-any\nexport function toList<T>(x: T|T[]): T[] {\n  if (Array.isArray(x)) {\n    return x;\n  }\n  return [x];\n}\n\n/**\n * Generate a UID for a list\n */\n// tslint:disable-next-line:no-any\nexport function objectListUid(objs: any|any[]): string {\n  const objectList = toList(objs);\n  let retVal = '';\n  for (const obj of objectList) {\n    if (obj.id == null) {\n      throw new ValueError(\n          `Object ${obj} passed to objectListUid without an id`);\n    }\n    if (retVal !== '') {\n      retVal = retVal + ', ';\n    }\n    retVal = `${retVal}${Math.abs(obj.id)}`;\n  }\n  return retVal;\n}\n/**\n * Converts string to snake-case.\n * @param name\n */\nexport function toSnakeCase(name: string): string {\n  const intermediate = name.replace(/(.)([A-Z][a-z0-9]+)/g, '$1_$2');\n  const insecure =\n      intermediate.replace(/([a-z])([A-Z])/g, '$1_$2').toLowerCase();\n  /*\n   If the class is private the name starts with \"_\" which is not secure\n   for creating scopes. We prefix the name with \"private\" in this case.\n   */\n  if (insecure[0] !== '_') {\n    return insecure;\n  }\n  return 'private' + insecure;\n}\n\nexport function toCamelCase(identifier: string): string {\n  // quick return for empty string or single character strings\n  if (identifier.length <= 1) {\n    return identifier;\n  }\n  // Check for the underscore indicating snake_case\n  if (identifier.indexOf('_') === -1) {\n    return identifier;\n  }\n  return identifier.replace(/[_]+(\\w|$)/g, (m, p1) => p1.toUpperCase());\n}\n\n// tslint:disable-next-line:no-any\nlet _GLOBAL_CUSTOM_OBJECTS = {} as {[objName: string]: any};\n\nexport function serializeKerasObject(instance: serialization.Serializable):\n    serialization.ConfigDictValue {\n  if (instance === null || instance === undefined) {\n    return null;\n  }\n  const dict: serialization.ConfigDictValue = {};\n  dict['className'] = instance.getClassName();\n  dict['config'] = instance.getConfig();\n  return dict;\n}\n\n/**\n * Replace ndarray-style scalar objects in serialization objects with numbers.\n *\n * Background: In some versions of tf.keras, certain scalar values in the HDF5\n * model save file can be serialized as: `{'type': 'ndarray', 'value': num}`,\n * where in `num` is a plain number. This method converts such serialization\n * to a `number`.\n *\n * @param config The keras-format serialization object to be processed\n *   (in place).\n */\nfunction convertNDArrayScalarsInConfig(config: serialization.ConfigDictValue):\n    void {\n  if (config == null || typeof config !== 'object') {\n    return;\n  } else if (Array.isArray(config)) {\n    config.forEach(configItem => convertNDArrayScalarsInConfig(configItem));\n  } else {\n    const fields = Object.keys(config);\n    for (const field of fields) {\n      const value = config[field];\n      if (value != null && typeof value === 'object') {\n        if (!Array.isArray(value) && value['type'] === 'ndarray' &&\n            typeof value['value'] === 'number') {\n          config[field] = value['value'];\n        } else {\n          convertNDArrayScalarsInConfig(value as serialization.ConfigDict);\n        }\n      }\n    }\n  }\n}\n\n/**\n * Deserialize a saved Keras Object\n * @param identifier either a string ID or a saved Keras dictionary\n * @param moduleObjects a list of Python class names to object constructors\n * @param customObjects a list of Python class names to object constructors\n * @param printableModuleName debug text for the object being reconstituted\n * @param fastWeightInit Optional flag to use fast weight initialization\n *   during deserialization. This is applicable to cases in which\n *   the initialization will be immediately overwritten by loaded weight\n *   values. Default: `false`.\n * @returns a TensorFlow.js Layers object\n */\n// tslint:disable:no-any\nexport function deserializeKerasObject(\n    identifier: string|serialization.ConfigDict,\n    moduleObjects = {} as {[objName: string]: any},\n    customObjects = {} as {[objName: string]: any},\n    printableModuleName = 'object', fastWeightInit = false): any {\n  // tslint:enable\n  if (typeof identifier === 'string') {\n    const functionName = identifier;\n    let fn;\n    if (functionName in customObjects) {\n      fn = customObjects[functionName];\n    } else if (functionName in _GLOBAL_CUSTOM_OBJECTS) {\n      fn = _GLOBAL_CUSTOM_OBJECTS[functionName];\n    } else {\n      fn = moduleObjects[functionName];\n      if (fn == null) {\n        throw new ValueError(\n            `Unknown ${printableModuleName}: ${identifier}. ` +\n            `This may be due to one of the following reasons:\\n` +\n            `1. The ${printableModuleName} is defined in Python, in which ` +\n            `case it needs to be ported to TensorFlow.js or your JavaScript ` +\n            `code.\\n` +\n            `2. The custom ${printableModuleName} is defined in JavaScript, ` +\n            `but is not registered properly with ` +\n            `tf.serialization.registerClass().`);\n        // TODO(cais): Add link to tutorial page on custom layers.\n      }\n    }\n    return fn;\n  } else {\n    // In this case we are dealing with a Keras config dictionary.\n    const config = identifier;\n    if (config['className'] == null || config['config'] == null) {\n      throw new ValueError(\n          `${printableModuleName}: Improper config format: ` +\n          `${JSON.stringify(config)}.\\n` +\n          `'className' and 'config' must set.`);\n    }\n    const className = config['className'] as string;\n    let cls, fromConfig;\n    if (className in customObjects) {\n      [cls, fromConfig] = customObjects[className];\n    } else if (className in _GLOBAL_CUSTOM_OBJECTS) {\n      [cls, fromConfig] = _GLOBAL_CUSTOM_OBJECTS['className'];\n    } else if (className in moduleObjects) {\n      [cls, fromConfig] = moduleObjects[className];\n    }\n    if (cls == null) {\n      throw new ValueError(\n          `Unknown ${printableModuleName}: ${className}. ` +\n          `This may be due to one of the following reasons:\\n` +\n          `1. The ${printableModuleName} is defined in Python, in which ` +\n          `case it needs to be ported to TensorFlow.js or your JavaScript ` +\n          `code.\\n` +\n          `2. The custom ${printableModuleName} is defined in JavaScript, ` +\n          `but is not registered properly with ` +\n          `tf.serialization.registerClass().`);\n      // TODO(cais): Add link to tutorial page on custom layers.\n    }\n    if (fromConfig != null) {\n      // Porting notes: Instead of checking to see whether fromConfig accepts\n      // customObjects, we create a customObjects dictionary and tack it on to\n      // config['config'] as config['config'].customObjects. Objects can use it,\n      // if they want.\n\n      // tslint:disable-next-line:no-any\n      const customObjectsCombined = {} as {[objName: string]: any};\n      for (const key of Object.keys(_GLOBAL_CUSTOM_OBJECTS)) {\n        customObjectsCombined[key] = _GLOBAL_CUSTOM_OBJECTS[key];\n      }\n      for (const key of Object.keys(customObjects)) {\n        customObjectsCombined[key] = customObjects[key];\n      }\n      // Add the customObjects to config\n      const nestedConfig = config['config'] as serialization.ConfigDict;\n      nestedConfig['customObjects'] = customObjectsCombined;\n\n      const backupCustomObjects = {..._GLOBAL_CUSTOM_OBJECTS};\n      for (const key of Object.keys(customObjects)) {\n        _GLOBAL_CUSTOM_OBJECTS[key] = customObjects[key];\n      }\n      convertNDArrayScalarsInConfig(config['config']);\n      const returnObj =\n          fromConfig(cls, config['config'], customObjects, fastWeightInit);\n      _GLOBAL_CUSTOM_OBJECTS = {...backupCustomObjects};\n\n      return returnObj;\n    } else {\n      // Then `cls` may be a function returning a class.\n      // In this case by convention `config` holds\n      // the kwargs of the function.\n      const backupCustomObjects = {..._GLOBAL_CUSTOM_OBJECTS};\n      for (const key of Object.keys(customObjects)) {\n        _GLOBAL_CUSTOM_OBJECTS[key] = customObjects[key];\n      }\n      // In python this is **config['config'], for tfjs-layers we require\n      // classes that use this fall-through construction method to take\n      // a config interface that mimics the expansion of named parameters.\n      const returnObj = new cls(config['config']);\n      _GLOBAL_CUSTOM_OBJECTS = {...backupCustomObjects};\n      return returnObj;\n    }\n  }\n}\n\n/**\n * Compares two numbers for sorting.\n * @param a\n * @param b\n */\nexport function numberCompare(a: number, b: number) {\n  return (a < b) ? -1 : ((a > b) ? 1 : 0);\n}\n\n/**\n * Comparison of two numbers for reverse sorting.\n * @param a\n * @param b\n */\nexport function reverseNumberCompare(a: number, b: number) {\n  return -1 * numberCompare(a, b);\n}\n\n/**\n * Convert a string into the corresponding DType.\n * @param dtype\n * @returns An instance of DType.\n */\nexport function stringToDType(dtype: string): DataType {\n  switch (dtype) {\n    case 'float32':\n      return 'float32';\n    default:\n      throw new ValueError(`Invalid dtype: ${dtype}`);\n  }\n}\n\n/**\n * Test the element-by-element equality of two Arrays of strings.\n * @param xs First array of strings.\n * @param ys Second array of strings.\n * @returns Wether the two arrays are all equal, element by element.\n */\nexport function stringsEqual(xs: string[], ys: string[]): boolean {\n  if (xs == null || ys == null) {\n    return xs === ys;\n  }\n  if (xs.length !== ys.length) {\n    return false;\n  }\n  for (let i = 0; i < xs.length; ++i) {\n    if (xs[i] !== ys[i]) {\n      return false;\n    }\n  }\n  return true;\n}\n\n/**\n * Get the unique elements of an array.\n * @param xs Array.\n * @returns An Array consisting of the unique elements in `xs`.\n */\nexport function unique<T>(xs: T[]): T[] {\n  if (xs == null) {\n    return xs;\n  }\n  const out: T[] = [];\n  // TODO(cais): Maybe improve performance by sorting.\n  for (const x of xs) {\n    if (out.indexOf(x) === -1) {\n      out.push(x);\n    }\n  }\n  return out;\n}\n\n/**\n * Determine if an Object is empty (i.e., does not have own properties).\n * @param obj Object\n * @returns Whether the Object is empty.\n * @throws ValueError: If object is `null` or `undefined`.\n */\nexport function isObjectEmpty(obj: {}): boolean {\n  if (obj == null) {\n    throw new ValueError(`Invalid value in obj: ${JSON.stringify(obj)}`);\n  }\n  for (const key in obj) {\n    if (obj.hasOwnProperty(key)) {\n      return false;\n    }\n  }\n  return true;\n}\n\n/**\n * Helper function used to build type union/enum run-time checkers.\n * @param values The list of allowed values.\n * @param label A string name for the type\n * @param value The value to test.\n * @throws ValueError: If the value is not in values nor `undefined`/`null`.\n */\nexport function checkStringTypeUnionValue(\n    values: string[], label: string, value: string): void {\n  if (value == null) {\n    return;\n  }\n  if (values.indexOf(value) < 0) {\n    throw new ValueError(`${value} is not a valid ${label}.  Valid values are ${\n        values} or null/undefined.`);\n  }\n}\n\n/**\n * Helper function for verifying the types of inputs.\n *\n * Ensures that the elements of `x` are all of type `expectedType`.\n * Also verifies that the length of `x` is within bounds.\n *\n * @param x Object to test.\n * @param expectedType The string expected type of all of the elements in the\n * Array.\n * @param minLength Return false if x.length is less than this.\n * @param maxLength Return false if x.length is greater than this.\n * @returns true if and only if `x` is an `Array<expectedType>` with\n * length >= `minLength` and <= `maxLength`.\n */\n// tslint:disable:no-any\nexport function checkArrayTypeAndLength(\n    x: any, expectedType: string, minLength = 0,\n    maxLength = Infinity): boolean {\n  assert(minLength >= 0);\n  assert(maxLength >= minLength);\n  return (\n      Array.isArray(x) && x.length >= minLength && x.length <= maxLength &&\n      x.every(e => typeof e === expectedType));\n}\n// tslint:enable:no-any\n\n/**\n * Assert that a value or an array of value are positive integer.\n *\n * @param value The value being asserted on. May be a single number or an array\n *   of numbers.\n * @param name Name of the value, used to make the error message.\n */\nexport function assertPositiveInteger(value: number|number[], name: string) {\n  if (Array.isArray(value)) {\n    util.assert(\n        value.length > 0, () => `${name} is unexpectedly an empty array.`);\n    value.forEach(\n        (v, i) => assertPositiveInteger(v, `element ${i + 1} of ${name}`));\n  } else {\n    util.assert(\n        Number.isInteger(value) && value > 0,\n        () => `Expected ${name} to be a positive integer, but got ` +\n            `${formatAsFriendlyString(value)}.`);\n  }\n}\n\n/**\n * Format a value into a display-friendly, human-readable fashion.\n *\n * - `null` is formatted as `'null'`\n * - Strings are formated with flanking pair of quotes.\n * - Arrays are formatted with flanking pair of square brackets.\n *\n * @param value The value to display.\n * @return Formatted string.\n */\n// tslint:disable-next-line:no-any\nexport function formatAsFriendlyString(value: any): string {\n  if (value === null) {\n    return 'null';\n  } else if (Array.isArray(value)) {\n    return '[' + value.map(v => formatAsFriendlyString(v)).join(',') + ']';\n  } else if (typeof value === 'string') {\n    return `\"${value}\"`;\n  } else {\n    return `${value}`;\n  }\n}\n\n/**\n * Returns a function `f2` (decorator) which wraps the original function\n * `f`. `f2` guarantees that `f` can be called at most once\n * every `waitMs` ms. If `f2` is called more often, it will return\n * the last returned result of `f`.\n *\n * @param f The original function `f` to wrap.\n * @param waitMs The time between two consecutive calls to `f` in ms.\n */\nexport function debounce<T>(\n    f: (...args: Array<{}>) => T, waitMs: number,\n    nowFunc?: Function): (...args: Array<{}>) => T {\n  let lastTime = nowFunc != null ? nowFunc() : util.now();\n  let lastResult: T;\n  const f2 = (...args: Array<{}>) => {\n    const now = nowFunc != null ? nowFunc() : util.now();\n    if (now - lastTime < waitMs) {\n      return lastResult;\n    }\n    lastTime = now;\n    lastResult = f(...args);\n    return lastResult;\n  };\n  return f2;\n}\n\n/**\n * Returns the fusable activation given a layers identifier.\n *\n * @param activationName The layers identifier string.\n * @return The name of the fusable activation.\n */\nexport function mapActivationToFusedKernel(activationName: string):\n    fused.Activation {\n  if (activationName === 'relu') {\n    return 'relu';\n  }\n  if (activationName === 'linear') {\n    return 'linear';\n  }\n  if (activationName === 'elu') {\n    return 'elu';\n  }\n  return null;\n}\n\ntype PossibleValues = Array<Array<boolean|string|number>>;\n\n/**\n * Returns the cartesian product of sets of values.\n * This works the same as itertools.product in Python.\n *\n * Example:\n *\n * filters = [128, 256, 512]\n * paddings = ['same', 'valid']\n *\n * product = [ [128, 'same'], [128, 'valid'], [256, 'same'], [256, 'valid'],\n * [512, 'same'], [512, 'valid']]\n *\n * @param arrayOfValues List/array of values.\n * @return The cartesian product.\n */\nexport function getCartesianProductOfValues(...arrayOfValues: PossibleValues):\n    PossibleValues {\n  assert(arrayOfValues.length > 0, 'arrayOfValues is empty');\n\n  for (const values of arrayOfValues) {\n    assert(Array.isArray(values), 'one of the values is not an array');\n    assert(values.length > 0, 'one of the values is empty');\n  }\n\n  return arrayOfValues.reduce((products, values) => {\n    if (products.length === 0) {\n      return values.map(value => [value]);\n    }\n\n    return values\n        .map(value => {\n          return products.map((prevValue) => [...prevValue, value]);\n        })\n        .reduce((flattenedProduct, unflattenedProduct) => {\n          return flattenedProduct.concat(unflattenedProduct);\n        }, []);\n  }, [] as PossibleValues);\n}\n"], "mappings": "AAAA;;;;;;;;;AAUA;AAEA,SAAwCA,IAAI,QAAO,uBAAuB;AAE1E,SAAQC,cAAc,EAAEC,UAAU,QAAO,WAAW;AAEpD;AAEA;;;;AAIA;AACA,OAAM,SAAUC,YAAYA,CAACC,KAAU,EAAEC,SAAiB;EACxD,IAAIC,KAAK,CAACC,OAAO,CAACH,KAAK,CAAC,EAAE;IACxB;IACA,IAAII,QAAQ,GAAU,EAAE;IACxB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,SAAS,EAAEI,CAAC,EAAE,EAAE;MAClCD,QAAQ,GAAGA,QAAQ,CAACE,MAAM,CAACN,KAAK,CAAC;;IAEnC,OAAOI,QAAQ;GAChB,MAAM;IACL,MAAMA,QAAQ,GAAG,IAAIF,KAAK,CAACD,SAAS,CAAC;IACrCG,QAAQ,CAACG,IAAI,CAACP,KAAK,CAAC;IACpB,OAAOI,QAAQ;;AAEnB;AAEA,OAAM,SAAUI,MAAMA,CAACC,GAAY,EAAEC,OAAgB;EACnD,IAAI,CAACD,GAAG,EAAE;IACR,MAAM,IAAIZ,cAAc,CAACa,OAAO,CAAC;;AAErC;AAEA;;;AAGA,OAAM,SAAUC,KAAKA,CAAIC,KAAU,EAAEC,QAAW;EAC9C,IAAIC,OAAO,GAAG,CAAC;EACf,KAAK,MAAMC,IAAI,IAAIH,KAAK,EAAE;IACxB,IAAIG,IAAI,KAAKF,QAAQ,EAAE;MACrBC,OAAO,EAAE;;;EAGb,OAAOA,OAAO;AAChB;AAEA;;;;;AAKA,OAAM,SAAUE,gBAAgBA,CAAIC,EAAO;EACzC,IAAIA,EAAE,CAACC,MAAM,KAAK,CAAC,EAAE;IACnB,OAAOD,EAAE,CAAC,CAAC,CAAC;;EAEd,OAAOA,EAAE;AACX;AAEA;;;;;;;;AAQA;AACA,OAAM,SAAUE,MAAMA,CAAIC,CAAQ;EAChC,IAAIlB,KAAK,CAACC,OAAO,CAACiB,CAAC,CAAC,EAAE;IACpB,OAAOA,CAAC;;EAEV,OAAO,CAACA,CAAC,CAAC;AACZ;AAEA;;;AAGA;AACA,OAAM,SAAUC,aAAaA,CAACC,IAAe;EAC3C,MAAMC,UAAU,GAAGJ,MAAM,CAACG,IAAI,CAAC;EAC/B,IAAIE,MAAM,GAAG,EAAE;EACf,KAAK,MAAMC,GAAG,IAAIF,UAAU,EAAE;IAC5B,IAAIE,GAAG,CAACC,EAAE,IAAI,IAAI,EAAE;MAClB,MAAM,IAAI5B,UAAU,CAChB,UAAU2B,GAAG,wCAAwC,CAAC;;IAE5D,IAAID,MAAM,KAAK,EAAE,EAAE;MACjBA,MAAM,GAAGA,MAAM,GAAG,IAAI;;IAExBA,MAAM,GAAG,GAAGA,MAAM,GAAGG,IAAI,CAACC,GAAG,CAACH,GAAG,CAACC,EAAE,CAAC,EAAE;;EAEzC,OAAOF,MAAM;AACf;AACA;;;;AAIA,OAAM,SAAUK,WAAWA,CAACC,IAAY;EACtC,MAAMC,YAAY,GAAGD,IAAI,CAACE,OAAO,CAAC,sBAAsB,EAAE,OAAO,CAAC;EAClE,MAAMC,QAAQ,GACVF,YAAY,CAACC,OAAO,CAAC,iBAAiB,EAAE,OAAO,CAAC,CAACE,WAAW,EAAE;EAClE;;;;EAIA,IAAID,QAAQ,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;IACvB,OAAOA,QAAQ;;EAEjB,OAAO,SAAS,GAAGA,QAAQ;AAC7B;AAEA,OAAM,SAAUE,WAAWA,CAACC,UAAkB;EAC5C;EACA,IAAIA,UAAU,CAAClB,MAAM,IAAI,CAAC,EAAE;IAC1B,OAAOkB,UAAU;;EAEnB;EACA,IAAIA,UAAU,CAACC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE;IAClC,OAAOD,UAAU;;EAEnB,OAAOA,UAAU,CAACJ,OAAO,CAAC,aAAa,EAAE,CAACM,CAAC,EAAEC,EAAE,KAAKA,EAAE,CAACC,WAAW,EAAE,CAAC;AACvE;AAEA;AACA,IAAIC,sBAAsB,GAAG,EAA8B;AAE3D,OAAM,SAAUC,oBAAoBA,CAACC,QAAoC;EAEvE,IAAIA,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAKC,SAAS,EAAE;IAC/C,OAAO,IAAI;;EAEb,MAAMC,IAAI,GAAkC,EAAE;EAC9CA,IAAI,CAAC,WAAW,CAAC,GAAGF,QAAQ,CAACG,YAAY,EAAE;EAC3CD,IAAI,CAAC,QAAQ,CAAC,GAAGF,QAAQ,CAACI,SAAS,EAAE;EACrC,OAAOF,IAAI;AACb;AAEA;;;;;;;;;;;AAWA,SAASG,6BAA6BA,CAACC,MAAqC;EAE1E,IAAIA,MAAM,IAAI,IAAI,IAAI,OAAOA,MAAM,KAAK,QAAQ,EAAE;IAChD;GACD,MAAM,IAAI/C,KAAK,CAACC,OAAO,CAAC8C,MAAM,CAAC,EAAE;IAChCA,MAAM,CAACC,OAAO,CAACC,UAAU,IAAIH,6BAA6B,CAACG,UAAU,CAAC,CAAC;GACxE,MAAM;IACL,MAAMC,MAAM,GAAGC,MAAM,CAACC,IAAI,CAACL,MAAM,CAAC;IAClC,KAAK,MAAMM,KAAK,IAAIH,MAAM,EAAE;MAC1B,MAAMpD,KAAK,GAAGiD,MAAM,CAACM,KAAK,CAAC;MAC3B,IAAIvD,KAAK,IAAI,IAAI,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;QAC9C,IAAI,CAACE,KAAK,CAACC,OAAO,CAACH,KAAK,CAAC,IAAIA,KAAK,CAAC,MAAM,CAAC,KAAK,SAAS,IACpD,OAAOA,KAAK,CAAC,OAAO,CAAC,KAAK,QAAQ,EAAE;UACtCiD,MAAM,CAACM,KAAK,CAAC,GAAGvD,KAAK,CAAC,OAAO,CAAC;SAC/B,MAAM;UACLgD,6BAA6B,CAAChD,KAAiC,CAAC;;;;;AAK1E;AAEA;;;;;;;;;;;;AAYA;AACA,OAAM,SAAUwD,sBAAsBA,CAClCpB,UAA2C,EAC3CqB,aAAA,GAAgB,EAA8B,EAC9CC,aAAA,GAAgB,EAA8B,EAC9CC,mBAAmB,GAAG,QAAQ,EAAEC,cAAc,GAAG,KAAK;EACxD;EACA,IAAI,OAAOxB,UAAU,KAAK,QAAQ,EAAE;IAClC,MAAMyB,YAAY,GAAGzB,UAAU;IAC/B,IAAI0B,EAAE;IACN,IAAID,YAAY,IAAIH,aAAa,EAAE;MACjCI,EAAE,GAAGJ,aAAa,CAACG,YAAY,CAAC;KACjC,MAAM,IAAIA,YAAY,IAAIpB,sBAAsB,EAAE;MACjDqB,EAAE,GAAGrB,sBAAsB,CAACoB,YAAY,CAAC;KAC1C,MAAM;MACLC,EAAE,GAAGL,aAAa,CAACI,YAAY,CAAC;MAChC,IAAIC,EAAE,IAAI,IAAI,EAAE;QACd,MAAM,IAAIhE,UAAU,CAChB,WAAW6D,mBAAmB,KAAKvB,UAAU,IAAI,GACjD,oDAAoD,GACpD,UAAUuB,mBAAmB,kCAAkC,GAC/D,iEAAiE,GACjE,SAAS,GACT,iBAAiBA,mBAAmB,6BAA6B,GACjE,sCAAsC,GACtC,mCAAmC,CAAC;QACxC;;;IAGJ,OAAOG,EAAE;GACV,MAAM;IACL;IACA,MAAMb,MAAM,GAAGb,UAAU;IACzB,IAAIa,MAAM,CAAC,WAAW,CAAC,IAAI,IAAI,IAAIA,MAAM,CAAC,QAAQ,CAAC,IAAI,IAAI,EAAE;MAC3D,MAAM,IAAInD,UAAU,CAChB,GAAG6D,mBAAmB,4BAA4B,GAClD,GAAGI,IAAI,CAACC,SAAS,CAACf,MAAM,CAAC,KAAK,GAC9B,oCAAoC,CAAC;;IAE3C,MAAMgB,SAAS,GAAGhB,MAAM,CAAC,WAAW,CAAW;IAC/C,IAAIiB,GAAG,EAAEC,UAAU;IACnB,IAAIF,SAAS,IAAIP,aAAa,EAAE;MAC9B,CAACQ,GAAG,EAAEC,UAAU,CAAC,GAAGT,aAAa,CAACO,SAAS,CAAC;KAC7C,MAAM,IAAIA,SAAS,IAAIxB,sBAAsB,EAAE;MAC9C,CAACyB,GAAG,EAAEC,UAAU,CAAC,GAAG1B,sBAAsB,CAAC,WAAW,CAAC;KACxD,MAAM,IAAIwB,SAAS,IAAIR,aAAa,EAAE;MACrC,CAACS,GAAG,EAAEC,UAAU,CAAC,GAAGV,aAAa,CAACQ,SAAS,CAAC;;IAE9C,IAAIC,GAAG,IAAI,IAAI,EAAE;MACf,MAAM,IAAIpE,UAAU,CAChB,WAAW6D,mBAAmB,KAAKM,SAAS,IAAI,GAChD,oDAAoD,GACpD,UAAUN,mBAAmB,kCAAkC,GAC/D,iEAAiE,GACjE,SAAS,GACT,iBAAiBA,mBAAmB,6BAA6B,GACjE,sCAAsC,GACtC,mCAAmC,CAAC;MACxC;;IAEF,IAAIQ,UAAU,IAAI,IAAI,EAAE;MACtB;MACA;MACA;MACA;MAEA;MACA,MAAMC,qBAAqB,GAAG,EAA8B;MAC5D,KAAK,MAAMC,GAAG,IAAIhB,MAAM,CAACC,IAAI,CAACb,sBAAsB,CAAC,EAAE;QACrD2B,qBAAqB,CAACC,GAAG,CAAC,GAAG5B,sBAAsB,CAAC4B,GAAG,CAAC;;MAE1D,KAAK,MAAMA,GAAG,IAAIhB,MAAM,CAACC,IAAI,CAACI,aAAa,CAAC,EAAE;QAC5CU,qBAAqB,CAACC,GAAG,CAAC,GAAGX,aAAa,CAACW,GAAG,CAAC;;MAEjD;MACA,MAAMC,YAAY,GAAGrB,MAAM,CAAC,QAAQ,CAA6B;MACjEqB,YAAY,CAAC,eAAe,CAAC,GAAGF,qBAAqB;MAErD,MAAMG,mBAAmB,GAAAlB,MAAA,CAAAmB,MAAA,KAAO/B,sBAAsB,CAAC;MACvD,KAAK,MAAM4B,GAAG,IAAIhB,MAAM,CAACC,IAAI,CAACI,aAAa,CAAC,EAAE;QAC5CjB,sBAAsB,CAAC4B,GAAG,CAAC,GAAGX,aAAa,CAACW,GAAG,CAAC;;MAElDrB,6BAA6B,CAACC,MAAM,CAAC,QAAQ,CAAC,CAAC;MAC/C,MAAMwB,SAAS,GACXN,UAAU,CAACD,GAAG,EAAEjB,MAAM,CAAC,QAAQ,CAAC,EAAES,aAAa,EAAEE,cAAc,CAAC;MACpEnB,sBAAsB,GAAAY,MAAA,CAAAmB,MAAA,KAAOD,mBAAmB,CAAC;MAEjD,OAAOE,SAAS;KACjB,MAAM;MACL;MACA;MACA;MACA,MAAMF,mBAAmB,GAAAlB,MAAA,CAAAmB,MAAA,KAAO/B,sBAAsB,CAAC;MACvD,KAAK,MAAM4B,GAAG,IAAIhB,MAAM,CAACC,IAAI,CAACI,aAAa,CAAC,EAAE;QAC5CjB,sBAAsB,CAAC4B,GAAG,CAAC,GAAGX,aAAa,CAACW,GAAG,CAAC;;MAElD;MACA;MACA;MACA,MAAMI,SAAS,GAAG,IAAIP,GAAG,CAACjB,MAAM,CAAC,QAAQ,CAAC,CAAC;MAC3CR,sBAAsB,GAAAY,MAAA,CAAAmB,MAAA,KAAOD,mBAAmB,CAAC;MACjD,OAAOE,SAAS;;;AAGtB;AAEA;;;;;AAKA,OAAM,SAAUC,aAAaA,CAACC,CAAS,EAAEC,CAAS;EAChD,OAAQD,CAAC,GAAGC,CAAC,GAAI,CAAC,CAAC,GAAKD,CAAC,GAAGC,CAAC,GAAI,CAAC,GAAG,CAAE;AACzC;AAEA;;;;;AAKA,OAAM,SAAUC,oBAAoBA,CAACF,CAAS,EAAEC,CAAS;EACvD,OAAO,CAAC,CAAC,GAAGF,aAAa,CAACC,CAAC,EAAEC,CAAC,CAAC;AACjC;AAEA;;;;;AAKA,OAAM,SAAUE,aAAaA,CAACC,KAAa;EACzC,QAAQA,KAAK;IACX,KAAK,SAAS;MACZ,OAAO,SAAS;IAClB;MACE,MAAM,IAAIjF,UAAU,CAAC,kBAAkBiF,KAAK,EAAE,CAAC;;AAErD;AAEA;;;;;;AAMA,OAAM,SAAUC,YAAYA,CAAC/D,EAAY,EAAEgE,EAAY;EACrD,IAAIhE,EAAE,IAAI,IAAI,IAAIgE,EAAE,IAAI,IAAI,EAAE;IAC5B,OAAOhE,EAAE,KAAKgE,EAAE;;EAElB,IAAIhE,EAAE,CAACC,MAAM,KAAK+D,EAAE,CAAC/D,MAAM,EAAE;IAC3B,OAAO,KAAK;;EAEd,KAAK,IAAIb,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGY,EAAE,CAACC,MAAM,EAAE,EAAEb,CAAC,EAAE;IAClC,IAAIY,EAAE,CAACZ,CAAC,CAAC,KAAK4E,EAAE,CAAC5E,CAAC,CAAC,EAAE;MACnB,OAAO,KAAK;;;EAGhB,OAAO,IAAI;AACb;AAEA;;;;;AAKA,OAAM,SAAU6E,MAAMA,CAAIjE,EAAO;EAC/B,IAAIA,EAAE,IAAI,IAAI,EAAE;IACd,OAAOA,EAAE;;EAEX,MAAMkE,GAAG,GAAQ,EAAE;EACnB;EACA,KAAK,MAAM/D,CAAC,IAAIH,EAAE,EAAE;IAClB,IAAIkE,GAAG,CAAC9C,OAAO,CAACjB,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE;MACzB+D,GAAG,CAACC,IAAI,CAAChE,CAAC,CAAC;;;EAGf,OAAO+D,GAAG;AACZ;AAEA;;;;;;AAMA,OAAM,SAAUE,aAAaA,CAAC5D,GAAO;EACnC,IAAIA,GAAG,IAAI,IAAI,EAAE;IACf,MAAM,IAAI3B,UAAU,CAAC,yBAAyBiE,IAAI,CAACC,SAAS,CAACvC,GAAG,CAAC,EAAE,CAAC;;EAEtE,KAAK,MAAM4C,GAAG,IAAI5C,GAAG,EAAE;IACrB,IAAIA,GAAG,CAAC6D,cAAc,CAACjB,GAAG,CAAC,EAAE;MAC3B,OAAO,KAAK;;;EAGhB,OAAO,IAAI;AACb;AAEA;;;;;;;AAOA,OAAM,SAAUkB,yBAAyBA,CACrCC,MAAgB,EAAEC,KAAa,EAAEzF,KAAa;EAChD,IAAIA,KAAK,IAAI,IAAI,EAAE;IACjB;;EAEF,IAAIwF,MAAM,CAACnD,OAAO,CAACrC,KAAK,CAAC,GAAG,CAAC,EAAE;IAC7B,MAAM,IAAIF,UAAU,CAAC,GAAGE,KAAK,mBAAmByF,KAAK,uBACjDD,MAAM,qBAAqB,CAAC;;AAEpC;AAEA;;;;;;;;;;;;;;AAcA;AACA,OAAM,SAAUE,uBAAuBA,CACnCtE,CAAM,EAAEuE,YAAoB,EAAEC,SAAS,GAAG,CAAC,EAC3CC,SAAS,GAAGC,QAAQ;EACtBtF,MAAM,CAACoF,SAAS,IAAI,CAAC,CAAC;EACtBpF,MAAM,CAACqF,SAAS,IAAID,SAAS,CAAC;EAC9B,OACI1F,KAAK,CAACC,OAAO,CAACiB,CAAC,CAAC,IAAIA,CAAC,CAACF,MAAM,IAAI0E,SAAS,IAAIxE,CAAC,CAACF,MAAM,IAAI2E,SAAS,IAClEzE,CAAC,CAAC2E,KAAK,CAACC,CAAC,IAAI,OAAOA,CAAC,KAAKL,YAAY,CAAC;AAC7C;AACA;AAEA;;;;;;;AAOA,OAAM,SAAUM,qBAAqBA,CAACjG,KAAsB,EAAE8B,IAAY;EACxE,IAAI5B,KAAK,CAACC,OAAO,CAACH,KAAK,CAAC,EAAE;IACxBJ,IAAI,CAACY,MAAM,CACPR,KAAK,CAACkB,MAAM,GAAG,CAAC,EAAE,MAAM,GAAGY,IAAI,kCAAkC,CAAC;IACtE9B,KAAK,CAACkD,OAAO,CACT,CAACgD,CAAC,EAAE7F,CAAC,KAAK4F,qBAAqB,CAACC,CAAC,EAAE,WAAW7F,CAAC,GAAG,CAAC,OAAOyB,IAAI,EAAE,CAAC,CAAC;GACvE,MAAM;IACLlC,IAAI,CAACY,MAAM,CACP2F,MAAM,CAACC,SAAS,CAACpG,KAAK,CAAC,IAAIA,KAAK,GAAG,CAAC,EACpC,MAAM,YAAY8B,IAAI,qCAAqC,GACvD,GAAGuE,sBAAsB,CAACrG,KAAK,CAAC,GAAG,CAAC;;AAEhD;AAEA;;;;;;;;;;AAUA;AACA,OAAM,SAAUqG,sBAAsBA,CAACrG,KAAU;EAC/C,IAAIA,KAAK,KAAK,IAAI,EAAE;IAClB,OAAO,MAAM;GACd,MAAM,IAAIE,KAAK,CAACC,OAAO,CAACH,KAAK,CAAC,EAAE;IAC/B,OAAO,GAAG,GAAGA,KAAK,CAACsG,GAAG,CAACJ,CAAC,IAAIG,sBAAsB,CAACH,CAAC,CAAC,CAAC,CAACK,IAAI,CAAC,GAAG,CAAC,GAAG,GAAG;GACvE,MAAM,IAAI,OAAOvG,KAAK,KAAK,QAAQ,EAAE;IACpC,OAAO,IAAIA,KAAK,GAAG;GACpB,MAAM;IACL,OAAO,GAAGA,KAAK,EAAE;;AAErB;AAEA;;;;;;;;;AASA,OAAM,SAAUwG,QAAQA,CACpBC,CAA4B,EAAEC,MAAc,EAC5CC,OAAkB;EACpB,IAAIC,QAAQ,GAAGD,OAAO,IAAI,IAAI,GAAGA,OAAO,EAAE,GAAG/G,IAAI,CAACiH,GAAG,EAAE;EACvD,IAAIC,UAAa;EACjB,MAAMC,EAAE,GAAGA,CAAC,GAAGC,IAAe,KAAI;IAChC,MAAMH,GAAG,GAAGF,OAAO,IAAI,IAAI,GAAGA,OAAO,EAAE,GAAG/G,IAAI,CAACiH,GAAG,EAAE;IACpD,IAAIA,GAAG,GAAGD,QAAQ,GAAGF,MAAM,EAAE;MAC3B,OAAOI,UAAU;;IAEnBF,QAAQ,GAAGC,GAAG;IACdC,UAAU,GAAGL,CAAC,CAAC,GAAGO,IAAI,CAAC;IACvB,OAAOF,UAAU;EACnB,CAAC;EACD,OAAOC,EAAE;AACX;AAEA;;;;;;AAMA,OAAM,SAAUE,0BAA0BA,CAACC,cAAsB;EAE/D,IAAIA,cAAc,KAAK,MAAM,EAAE;IAC7B,OAAO,MAAM;;EAEf,IAAIA,cAAc,KAAK,QAAQ,EAAE;IAC/B,OAAO,QAAQ;;EAEjB,IAAIA,cAAc,KAAK,KAAK,EAAE;IAC5B,OAAO,KAAK;;EAEd,OAAO,IAAI;AACb;AAIA;;;;;;;;;;;;;;;AAeA,OAAM,SAAUC,2BAA2BA,CAAC,GAAGC,aAA6B;EAE1E5G,MAAM,CAAC4G,aAAa,CAAClG,MAAM,GAAG,CAAC,EAAE,wBAAwB,CAAC;EAE1D,KAAK,MAAMsE,MAAM,IAAI4B,aAAa,EAAE;IAClC5G,MAAM,CAACN,KAAK,CAACC,OAAO,CAACqF,MAAM,CAAC,EAAE,mCAAmC,CAAC;IAClEhF,MAAM,CAACgF,MAAM,CAACtE,MAAM,GAAG,CAAC,EAAE,4BAA4B,CAAC;;EAGzD,OAAOkG,aAAa,CAACC,MAAM,CAAC,CAACC,QAAQ,EAAE9B,MAAM,KAAI;IAC/C,IAAI8B,QAAQ,CAACpG,MAAM,KAAK,CAAC,EAAE;MACzB,OAAOsE,MAAM,CAACc,GAAG,CAACtG,KAAK,IAAI,CAACA,KAAK,CAAC,CAAC;;IAGrC,OAAOwF,MAAM,CACRc,GAAG,CAACtG,KAAK,IAAG;MACX,OAAOsH,QAAQ,CAAChB,GAAG,CAAEiB,SAAS,IAAK,CAAC,GAAGA,SAAS,EAAEvH,KAAK,CAAC,CAAC;IAC3D,CAAC,CAAC,CACDqH,MAAM,CAAC,CAACG,gBAAgB,EAAEC,kBAAkB,KAAI;MAC/C,OAAOD,gBAAgB,CAAClH,MAAM,CAACmH,kBAAkB,CAAC;IACpD,CAAC,EAAE,EAAE,CAAC;EACZ,CAAC,EAAE,EAAoB,CAAC;AAC1B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}