{"ast": null, "code": "/**\n * @license\n * Copyright 2018 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { ENGINE } from './engine';\nimport { env } from './environment';\nimport { getGlobalTensorClass } from './tensor';\nimport { isWebGLData, isWebGPUData } from './types';\nimport { assert, flatten, inferDtype, isTypedArray, toTypedArray } from './util';\nimport { bytesPerElement } from './util_base';\nexport function inferShape(val, dtype) {\n  let firstElem = val;\n  if (isTypedArray(val)) {\n    return dtype === 'string' ? [] : [val.length];\n  }\n  if (isWebGLData(val)) {\n    const usedChannels = val.channels || 'RGBA';\n    return [val.height, val.width * usedChannels.length];\n  } else if (isWebGPUData(val)) {\n    return [val.buffer.size / (dtype == null ? 4 : bytesPerElement(dtype))];\n  }\n  if (!Array.isArray(val)) {\n    return []; // Scalar.\n  }\n  const shape = [];\n  while (Array.isArray(firstElem) || isTypedArray(firstElem) && dtype !== 'string') {\n    shape.push(firstElem.length);\n    firstElem = firstElem[0];\n  }\n  if (Array.isArray(val) && env().getBool('TENSORLIKE_CHECK_SHAPE_CONSISTENCY')) {\n    deepAssertShapeConsistency(val, shape, []);\n  }\n  return shape;\n}\nfunction deepAssertShapeConsistency(val, shape, indices) {\n  indices = indices || [];\n  if (!Array.isArray(val) && !isTypedArray(val)) {\n    assert(shape.length === 0, () => `Element arr[${indices.join('][')}] is a primitive, ` + `but should be an array/TypedArray of ${shape[0]} elements`);\n    return;\n  }\n  assert(shape.length > 0, () => `Element arr[${indices.join('][')}] should be a primitive, ` + `but is an array of ${val.length} elements`);\n  assert(val.length === shape[0], () => `Element arr[${indices.join('][')}] should have ${shape[0]} ` + `elements, but has ${val.length} elements`);\n  const subShape = shape.slice(1);\n  for (let i = 0; i < val.length; ++i) {\n    deepAssertShapeConsistency(val[i], subShape, indices.concat(i));\n  }\n}\nfunction assertDtype(expectedDtype, actualDType, argName, functionName) {\n  if (expectedDtype === 'string_or_numeric') {\n    return;\n  }\n  if (expectedDtype == null) {\n    throw new Error(`Expected dtype cannot be null.`);\n  }\n  if (expectedDtype !== 'numeric' && expectedDtype !== actualDType || expectedDtype === 'numeric' && actualDType === 'string') {\n    throw new Error(`Argument '${argName}' passed to '${functionName}' must ` + `be ${expectedDtype} tensor, but got ${actualDType} tensor`);\n  }\n}\nexport function convertToTensor(x, argName, functionName, parseAsDtype = 'numeric') {\n  if (x instanceof getGlobalTensorClass()) {\n    assertDtype(parseAsDtype, x.dtype, argName, functionName);\n    return x;\n  }\n  let inferredDtype = inferDtype(x);\n  // If the user expects a bool/int/float, use that info to update the\n  // inferredDtype when it is not a string.\n  if (inferredDtype !== 'string' && ['bool', 'int32', 'float32'].indexOf(parseAsDtype) >= 0) {\n    inferredDtype = parseAsDtype;\n  }\n  assertDtype(parseAsDtype, inferredDtype, argName, functionName);\n  if (x == null || !isTypedArray(x) && !Array.isArray(x) && typeof x !== 'number' && typeof x !== 'boolean' && typeof x !== 'string') {\n    const type = x == null ? 'null' : x.constructor.name;\n    throw new Error(`Argument '${argName}' passed to '${functionName}' must be a ` + `Tensor or TensorLike, but got '${type}'`);\n  }\n  const inferredShape = inferShape(x, inferredDtype);\n  if (!isTypedArray(x) && !Array.isArray(x)) {\n    x = [x];\n  }\n  const skipTypedArray = true;\n  const values = inferredDtype !== 'string' ? toTypedArray(x, inferredDtype) : flatten(x, [], skipTypedArray);\n  return ENGINE.makeTensor(values, inferredShape, inferredDtype);\n}\nexport function convertToTensorArray(arg, argName, functionName, parseAsDtype = 'numeric') {\n  if (!Array.isArray(arg)) {\n    throw new Error(`Argument ${argName} passed to ${functionName} must be a ` + '`Tensor[]` or `TensorLike[]`');\n  }\n  const tensors = arg;\n  return tensors.map((t, i) => convertToTensor(t, `${argName}[${i}]`, functionName, parseAsDtype));\n}", "map": {"version": 3, "names": ["ENGINE", "env", "getGlobalTensorClass", "isWebGLData", "isWebGPUData", "assert", "flatten", "inferDtype", "isTypedArray", "toTypedA<PERSON>y", "bytesPerElement", "inferShape", "val", "dtype", "firstElem", "length", "usedChannels", "channels", "height", "width", "buffer", "size", "Array", "isArray", "shape", "push", "getBool", "deepAssertShapeConsistency", "indices", "join", "subShape", "slice", "i", "concat", "assertDtype", "expectedDtype", "actualDType", "argName", "functionName", "Error", "convertToTensor", "x", "parseAsDtype", "inferredDtype", "indexOf", "type", "constructor", "name", "inferredShape", "skipTyped<PERSON><PERSON>y", "values", "makeTensor", "convertToTensorArray", "arg", "tensors", "map", "t"], "sources": ["C:\\tfjs-core\\src\\tensor_util_env.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2018 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {ENGINE} from './engine';\nimport {env} from './environment';\nimport {getGlobalTensorClass, Tensor} from './tensor';\nimport {DataType, isWebGLData, isWebGPUData, TensorLike, WebGLData, WebGPUData} from './types';\nimport {assert, flatten, inferDtype, isTypedArray, toTypedArray} from './util';\nimport {bytesPerElement} from './util_base';\n\nexport function inferShape(\n    val: TensorLike|WebGLData|WebGPUData, dtype?: DataType): number[] {\n  let firstElem: typeof val = val;\n\n  if (isTypedArray(val)) {\n    return dtype === 'string' ? [] : [val.length];\n  }\n\n  if (isWebGLData(val)) {\n    const usedChannels = val.channels || 'RGBA';\n    return [val.height, val.width * usedChannels.length];\n  } else if (isWebGPUData(val)) {\n    return [val.buffer.size / (dtype == null ? 4 : bytesPerElement(dtype))];\n  }\n  if (!Array.isArray(val)) {\n    return [];  // Scalar.\n  }\n  const shape: number[] = [];\n\n  while (Array.isArray(firstElem) ||\n         isTypedArray(firstElem) && dtype !== 'string') {\n    shape.push(firstElem.length);\n    firstElem = firstElem[0];\n  }\n  if (Array.isArray(val) &&\n      env().getBool('TENSORLIKE_CHECK_SHAPE_CONSISTENCY')) {\n    deepAssertShapeConsistency(val, shape, []);\n  }\n\n  return shape;\n}\n\nfunction deepAssertShapeConsistency(\n    val: TensorLike, shape: number[], indices: number[]) {\n  indices = indices || [];\n  if (!(Array.isArray(val)) && !isTypedArray(val)) {\n    assert(\n        shape.length === 0,\n        () => `Element arr[${indices.join('][')}] is a primitive, ` +\n            `but should be an array/TypedArray of ${shape[0]} elements`);\n    return;\n  }\n  assert(\n      shape.length > 0,\n      () => `Element arr[${indices.join('][')}] should be a primitive, ` +\n          `but is an array of ${val.length} elements`);\n  assert(\n      val.length === shape[0],\n      () => `Element arr[${indices.join('][')}] should have ${shape[0]} ` +\n          `elements, but has ${val.length} elements`);\n  const subShape = shape.slice(1);\n  for (let i = 0; i < val.length; ++i) {\n    deepAssertShapeConsistency(val[i], subShape, indices.concat(i));\n  }\n}\n\nfunction assertDtype(\n    expectedDtype: DataType|'numeric'|'string_or_numeric',\n    actualDType: DataType, argName: string, functionName: string) {\n  if (expectedDtype === 'string_or_numeric') {\n    return;\n  }\n  if (expectedDtype == null) {\n    throw new Error(`Expected dtype cannot be null.`);\n  }\n  if (expectedDtype !== 'numeric' && expectedDtype !== actualDType ||\n      expectedDtype === 'numeric' && actualDType === 'string') {\n    throw new Error(\n        `Argument '${argName}' passed to '${functionName}' must ` +\n        `be ${expectedDtype} tensor, but got ${actualDType} tensor`);\n  }\n}\n\nexport function convertToTensor<T extends Tensor>(\n    x: T|TensorLike, argName: string, functionName: string,\n    parseAsDtype: DataType|'numeric'|'string_or_numeric' = 'numeric'): T {\n  if (x instanceof getGlobalTensorClass()) {\n    assertDtype(parseAsDtype, x.dtype, argName, functionName);\n    return x;\n  }\n  let inferredDtype = inferDtype(x);\n  // If the user expects a bool/int/float, use that info to update the\n  // inferredDtype when it is not a string.\n  if (inferredDtype !== 'string' &&\n      ['bool', 'int32', 'float32'].indexOf(parseAsDtype) >= 0) {\n    inferredDtype = parseAsDtype as DataType;\n  }\n  assertDtype(parseAsDtype, inferredDtype, argName, functionName);\n\n  if ((x == null) ||\n      (!isTypedArray(x) && !Array.isArray(x) && typeof x !== 'number' &&\n       typeof x !== 'boolean' && typeof x !== 'string')) {\n    const type = x == null ? 'null' : (x as {}).constructor.name;\n    throw new Error(\n        `Argument '${argName}' passed to '${functionName}' must be a ` +\n        `Tensor or TensorLike, but got '${type}'`);\n  }\n  const inferredShape = inferShape(x, inferredDtype);\n  if (!isTypedArray(x) && !Array.isArray(x)) {\n    x = [x] as number[];\n  }\n  const skipTypedArray = true;\n  const values = inferredDtype !== 'string' ?\n      toTypedArray(x, inferredDtype as DataType) :\n      flatten(x as string[], [], skipTypedArray) as string[];\n  return ENGINE.makeTensor(values, inferredShape, inferredDtype) as T;\n}\n\nexport function convertToTensorArray<T extends Tensor>(\n    arg: Array<T|TensorLike>, argName: string, functionName: string,\n    parseAsDtype: DataType|'numeric'|'string_or_numeric' = 'numeric'): T[] {\n  if (!Array.isArray(arg)) {\n    throw new Error(\n        `Argument ${argName} passed to ${functionName} must be a ` +\n        '`Tensor[]` or `TensorLike[]`');\n  }\n  const tensors = arg as T[];\n  return tensors.map(\n      (t, i) =>\n          convertToTensor(t, `${argName}[${i}]`, functionName, parseAsDtype));\n}\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAiBA,SAAQA,MAAM,QAAO,UAAU;AAC/B,SAAQC,GAAG,QAAO,eAAe;AACjC,SAAQC,oBAAoB,QAAe,UAAU;AACrD,SAAkBC,WAAW,EAAEC,YAAY,QAA0C,SAAS;AAC9F,SAAQC,MAAM,EAAEC,OAAO,EAAEC,UAAU,EAAEC,YAAY,EAAEC,YAAY,QAAO,QAAQ;AAC9E,SAAQC,eAAe,QAAO,aAAa;AAE3C,OAAM,SAAUC,UAAUA,CACtBC,GAAoC,EAAEC,KAAgB;EACxD,IAAIC,SAAS,GAAeF,GAAG;EAE/B,IAAIJ,YAAY,CAACI,GAAG,CAAC,EAAE;IACrB,OAAOC,KAAK,KAAK,QAAQ,GAAG,EAAE,GAAG,CAACD,GAAG,CAACG,MAAM,CAAC;;EAG/C,IAAIZ,WAAW,CAACS,GAAG,CAAC,EAAE;IACpB,MAAMI,YAAY,GAAGJ,GAAG,CAACK,QAAQ,IAAI,MAAM;IAC3C,OAAO,CAACL,GAAG,CAACM,MAAM,EAAEN,GAAG,CAACO,KAAK,GAAGH,YAAY,CAACD,MAAM,CAAC;GACrD,MAAM,IAAIX,YAAY,CAACQ,GAAG,CAAC,EAAE;IAC5B,OAAO,CAACA,GAAG,CAACQ,MAAM,CAACC,IAAI,IAAIR,KAAK,IAAI,IAAI,GAAG,CAAC,GAAGH,eAAe,CAACG,KAAK,CAAC,CAAC,CAAC;;EAEzE,IAAI,CAACS,KAAK,CAACC,OAAO,CAACX,GAAG,CAAC,EAAE;IACvB,OAAO,EAAE,CAAC,CAAE;;EAEd,MAAMY,KAAK,GAAa,EAAE;EAE1B,OAAOF,KAAK,CAACC,OAAO,CAACT,SAAS,CAAC,IACxBN,YAAY,CAACM,SAAS,CAAC,IAAID,KAAK,KAAK,QAAQ,EAAE;IACpDW,KAAK,CAACC,IAAI,CAACX,SAAS,CAACC,MAAM,CAAC;IAC5BD,SAAS,GAAGA,SAAS,CAAC,CAAC,CAAC;;EAE1B,IAAIQ,KAAK,CAACC,OAAO,CAACX,GAAG,CAAC,IAClBX,GAAG,EAAE,CAACyB,OAAO,CAAC,oCAAoC,CAAC,EAAE;IACvDC,0BAA0B,CAACf,GAAG,EAAEY,KAAK,EAAE,EAAE,CAAC;;EAG5C,OAAOA,KAAK;AACd;AAEA,SAASG,0BAA0BA,CAC/Bf,GAAe,EAAEY,KAAe,EAAEI,OAAiB;EACrDA,OAAO,GAAGA,OAAO,IAAI,EAAE;EACvB,IAAI,CAAEN,KAAK,CAACC,OAAO,CAACX,GAAG,CAAE,IAAI,CAACJ,YAAY,CAACI,GAAG,CAAC,EAAE;IAC/CP,MAAM,CACFmB,KAAK,CAACT,MAAM,KAAK,CAAC,EAClB,MAAM,eAAea,OAAO,CAACC,IAAI,CAAC,IAAI,CAAC,oBAAoB,GACvD,wCAAwCL,KAAK,CAAC,CAAC,CAAC,WAAW,CAAC;IACpE;;EAEFnB,MAAM,CACFmB,KAAK,CAACT,MAAM,GAAG,CAAC,EAChB,MAAM,eAAea,OAAO,CAACC,IAAI,CAAC,IAAI,CAAC,2BAA2B,GAC9D,sBAAsBjB,GAAG,CAACG,MAAM,WAAW,CAAC;EACpDV,MAAM,CACFO,GAAG,CAACG,MAAM,KAAKS,KAAK,CAAC,CAAC,CAAC,EACvB,MAAM,eAAeI,OAAO,CAACC,IAAI,CAAC,IAAI,CAAC,iBAAiBL,KAAK,CAAC,CAAC,CAAC,GAAG,GAC/D,qBAAqBZ,GAAG,CAACG,MAAM,WAAW,CAAC;EACnD,MAAMe,QAAQ,GAAGN,KAAK,CAACO,KAAK,CAAC,CAAC,CAAC;EAC/B,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGpB,GAAG,CAACG,MAAM,EAAE,EAAEiB,CAAC,EAAE;IACnCL,0BAA0B,CAACf,GAAG,CAACoB,CAAC,CAAC,EAAEF,QAAQ,EAAEF,OAAO,CAACK,MAAM,CAACD,CAAC,CAAC,CAAC;;AAEnE;AAEA,SAASE,WAAWA,CAChBC,aAAqD,EACrDC,WAAqB,EAAEC,OAAe,EAAEC,YAAoB;EAC9D,IAAIH,aAAa,KAAK,mBAAmB,EAAE;IACzC;;EAEF,IAAIA,aAAa,IAAI,IAAI,EAAE;IACzB,MAAM,IAAII,KAAK,CAAC,gCAAgC,CAAC;;EAEnD,IAAIJ,aAAa,KAAK,SAAS,IAAIA,aAAa,KAAKC,WAAW,IAC5DD,aAAa,KAAK,SAAS,IAAIC,WAAW,KAAK,QAAQ,EAAE;IAC3D,MAAM,IAAIG,KAAK,CACX,aAAaF,OAAO,gBAAgBC,YAAY,SAAS,GACzD,MAAMH,aAAa,oBAAoBC,WAAW,SAAS,CAAC;;AAEpE;AAEA,OAAM,SAAUI,eAAeA,CAC3BC,CAAe,EAAEJ,OAAe,EAAEC,YAAoB,EACtDI,YAAA,GAAuD,SAAS;EAClE,IAAID,CAAC,YAAYvC,oBAAoB,EAAE,EAAE;IACvCgC,WAAW,CAACQ,YAAY,EAAED,CAAC,CAAC5B,KAAK,EAAEwB,OAAO,EAAEC,YAAY,CAAC;IACzD,OAAOG,CAAC;;EAEV,IAAIE,aAAa,GAAGpC,UAAU,CAACkC,CAAC,CAAC;EACjC;EACA;EACA,IAAIE,aAAa,KAAK,QAAQ,IAC1B,CAAC,MAAM,EAAE,OAAO,EAAE,SAAS,CAAC,CAACC,OAAO,CAACF,YAAY,CAAC,IAAI,CAAC,EAAE;IAC3DC,aAAa,GAAGD,YAAwB;;EAE1CR,WAAW,CAACQ,YAAY,EAAEC,aAAa,EAAEN,OAAO,EAAEC,YAAY,CAAC;EAE/D,IAAKG,CAAC,IAAI,IAAI,IACT,CAACjC,YAAY,CAACiC,CAAC,CAAC,IAAI,CAACnB,KAAK,CAACC,OAAO,CAACkB,CAAC,CAAC,IAAI,OAAOA,CAAC,KAAK,QAAQ,IAC9D,OAAOA,CAAC,KAAK,SAAS,IAAI,OAAOA,CAAC,KAAK,QAAS,EAAE;IACrD,MAAMI,IAAI,GAAGJ,CAAC,IAAI,IAAI,GAAG,MAAM,GAAIA,CAAQ,CAACK,WAAW,CAACC,IAAI;IAC5D,MAAM,IAAIR,KAAK,CACX,aAAaF,OAAO,gBAAgBC,YAAY,cAAc,GAC9D,kCAAkCO,IAAI,GAAG,CAAC;;EAEhD,MAAMG,aAAa,GAAGrC,UAAU,CAAC8B,CAAC,EAAEE,aAAa,CAAC;EAClD,IAAI,CAACnC,YAAY,CAACiC,CAAC,CAAC,IAAI,CAACnB,KAAK,CAACC,OAAO,CAACkB,CAAC,CAAC,EAAE;IACzCA,CAAC,GAAG,CAACA,CAAC,CAAa;;EAErB,MAAMQ,cAAc,GAAG,IAAI;EAC3B,MAAMC,MAAM,GAAGP,aAAa,KAAK,QAAQ,GACrClC,YAAY,CAACgC,CAAC,EAAEE,aAAyB,CAAC,GAC1CrC,OAAO,CAACmC,CAAa,EAAE,EAAE,EAAEQ,cAAc,CAAa;EAC1D,OAAOjD,MAAM,CAACmD,UAAU,CAACD,MAAM,EAAEF,aAAa,EAAEL,aAAa,CAAM;AACrE;AAEA,OAAM,SAAUS,oBAAoBA,CAChCC,GAAwB,EAAEhB,OAAe,EAAEC,YAAoB,EAC/DI,YAAA,GAAuD,SAAS;EAClE,IAAI,CAACpB,KAAK,CAACC,OAAO,CAAC8B,GAAG,CAAC,EAAE;IACvB,MAAM,IAAId,KAAK,CACX,YAAYF,OAAO,cAAcC,YAAY,aAAa,GAC1D,8BAA8B,CAAC;;EAErC,MAAMgB,OAAO,GAAGD,GAAU;EAC1B,OAAOC,OAAO,CAACC,GAAG,CACd,CAACC,CAAC,EAAExB,CAAC,KACDQ,eAAe,CAACgB,CAAC,EAAE,GAAGnB,OAAO,IAAIL,CAAC,GAAG,EAAEM,YAAY,EAAEI,YAAY,CAAC,CAAC;AAC7E", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}