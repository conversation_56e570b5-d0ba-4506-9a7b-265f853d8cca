{"ast": null, "code": "/*\nCopyright (c) 2011, <PERSON>, <PERSON>\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in\nall copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\nTHE SOFTWARE.\n*/\n\n'use strict';\n\nconst DEBUG = false;\nconst Tokenizer = require('./tokenizer');\nconst _ = require('underscore');\nclass RegexpTokenizer extends Tokenizer {\n  constructor(opts) {\n    super(opts);\n    const options = opts || {};\n    this._pattern = options.pattern || this._pattern;\n    this.discardEmpty = options.discardEmpty || true;\n\n    // Match and split on GAPS not the actual WORDS\n    this._gaps = options.gaps;\n    if (this._gaps === undefined) {\n      this._gaps = true;\n    }\n  }\n  tokenize(s) {\n    let results;\n    if (this._gaps) {\n      results = s.split(this._pattern);\n      return this.discardEmpty ? _.without(results, '', ' ') : results;\n    } else {\n      return s.match(this._pattern);\n    }\n  }\n}\nexports.RegexpTokenizer = RegexpTokenizer;\nconst orthographyMatchers = require('./orthography_matchers');\nclass OrthographyTokenizer {\n  constructor(options) {\n    const pattern = orthographyMatchers[options.language];\n    DEBUG && console.log(pattern);\n    if (!pattern) {\n      this.tokenizer = new WordTokenizer();\n    } else {\n      this.tokenizer = new RegexpTokenizer(options);\n      this.tokenizer._pattern = pattern;\n      DEBUG && console.log(this.tokenizer);\n    }\n  }\n  tokenize(text) {\n    return this.tokenizer.tokenize(text);\n  }\n}\nexports.OrthographyTokenizer = OrthographyTokenizer;\n\n/***\n * A tokenizer that divides a text into sequences of alphabetic and\n * non-alphabetic characters.  E.g.:\n *\n *      >>> WordTokenizer().tokenize(\"She said 'hello'.\")\n *      ['She', 'said', 'hello']\n *\n */\nclass WordTokenizer extends RegexpTokenizer {\n  constructor(options) {\n    super(options);\n    this._pattern = /[^A-Za-zА-Яа-я0-9_]+/;\n  }\n}\nexports.WordTokenizer = WordTokenizer;\n\n/***\n * A tokenizer that divides a text into sequences of alphabetic and\n * non-alphabetic characters.  E.g.:\n *\n *      >>> WordPunctTokenizer().tokenize(\"She said 'hello'.\")\n *      [\"She\",\"said\",\"'\",\"hello\",\"'\",\".\"]\n *\n */\nclass WordPunctTokenizer extends RegexpTokenizer {\n  constructor(options) {\n    if (!options) {\n      options = {};\n    }\n    options.pattern = /([A-Za-zÀ-ÿ-]+|[0-9._]+|.|!|\\?|'|\"|:|;|,|-)/i;\n    super(options);\n  }\n}\nexports.WordPunctTokenizer = WordPunctTokenizer;", "map": {"version": 3, "names": ["DEBUG", "Tokenizer", "require", "_", "RegexpTokenizer", "constructor", "opts", "options", "_pattern", "pattern", "discardEmpty", "_gaps", "gaps", "undefined", "tokenize", "s", "results", "split", "without", "match", "exports", "orthographyMatchers", "OrthographyTokenizer", "language", "console", "log", "tokenizer", "WordTokenizer", "text", "WordPunctTokenizer"], "sources": ["C:/tmsft/node_modules/natural/lib/natural/tokenizers/regexp_tokenizer.js"], "sourcesContent": ["/*\nCopyright (c) 2011, <PERSON>, <PERSON>\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in\nall copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\nTHE SOFTWARE.\n*/\n\n'use strict'\n\nconst DEBUG = false\n\nconst Tokenizer = require('./tokenizer')\nconst _ = require('underscore')\n\nclass RegexpTokenizer extends Tokenizer {\n  constructor (opts) {\n    super(opts)\n    const options = opts || {}\n    this._pattern = options.pattern || this._pattern\n    this.discardEmpty = options.discardEmpty || true\n\n    // Match and split on GAPS not the actual WORDS\n    this._gaps = options.gaps\n\n    if (this._gaps === undefined) {\n      this._gaps = true\n    }\n  }\n\n  tokenize (s) {\n    let results\n\n    if (this._gaps) {\n      results = s.split(this._pattern)\n      return (this.discardEmpty) ? _.without(results, '', ' ') : results\n    } else {\n      return s.match(this._pattern)\n    }\n  }\n}\n\nexports.RegexpTokenizer = RegexpTokenizer\n\nconst orthographyMatchers = require('./orthography_matchers')\n\nclass OrthographyTokenizer {\n  constructor (options) {\n    const pattern = orthographyMatchers[options.language]\n    DEBUG && console.log(pattern)\n\n    if (!pattern) {\n      this.tokenizer = new WordTokenizer()\n    } else {\n      this.tokenizer = new RegexpTokenizer(options)\n      this.tokenizer._pattern = pattern\n      DEBUG && console.log(this.tokenizer)\n    }\n  }\n\n  tokenize (text) {\n    return this.tokenizer.tokenize(text)\n  }\n}\n\nexports.OrthographyTokenizer = OrthographyTokenizer\n\n/***\n * A tokenizer that divides a text into sequences of alphabetic and\n * non-alphabetic characters.  E.g.:\n *\n *      >>> WordTokenizer().tokenize(\"She said 'hello'.\")\n *      ['She', 'said', 'hello']\n *\n */\nclass WordTokenizer extends RegexpTokenizer {\n  constructor (options) {\n    super(options)\n    this._pattern = /[^A-Za-zА-Яа-я0-9_]+/\n  }\n}\n\nexports.WordTokenizer = WordTokenizer\n\n/***\n * A tokenizer that divides a text into sequences of alphabetic and\n * non-alphabetic characters.  E.g.:\n *\n *      >>> WordPunctTokenizer().tokenize(\"She said 'hello'.\")\n *      [\"She\",\"said\",\"'\",\"hello\",\"'\",\".\"]\n *\n */\nclass WordPunctTokenizer extends RegexpTokenizer {\n  constructor (options) {\n    if (!options) {\n      options = {}\n    }\n    options.pattern = /([A-Za-zÀ-ÿ-]+|[0-9._]+|.|!|\\?|'|\"|:|;|,|-)/i\n    super(options)\n  }\n}\n\nexports.WordPunctTokenizer = WordPunctTokenizer\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,YAAY;;AAEZ,MAAMA,KAAK,GAAG,KAAK;AAEnB,MAAMC,SAAS,GAAGC,OAAO,CAAC,aAAa,CAAC;AACxC,MAAMC,CAAC,GAAGD,OAAO,CAAC,YAAY,CAAC;AAE/B,MAAME,eAAe,SAASH,SAAS,CAAC;EACtCI,WAAWA,CAAEC,IAAI,EAAE;IACjB,KAAK,CAACA,IAAI,CAAC;IACX,MAAMC,OAAO,GAAGD,IAAI,IAAI,CAAC,CAAC;IAC1B,IAAI,CAACE,QAAQ,GAAGD,OAAO,CAACE,OAAO,IAAI,IAAI,CAACD,QAAQ;IAChD,IAAI,CAACE,YAAY,GAAGH,OAAO,CAACG,YAAY,IAAI,IAAI;;IAEhD;IACA,IAAI,CAACC,KAAK,GAAGJ,OAAO,CAACK,IAAI;IAEzB,IAAI,IAAI,CAACD,KAAK,KAAKE,SAAS,EAAE;MAC5B,IAAI,CAACF,KAAK,GAAG,IAAI;IACnB;EACF;EAEAG,QAAQA,CAAEC,CAAC,EAAE;IACX,IAAIC,OAAO;IAEX,IAAI,IAAI,CAACL,KAAK,EAAE;MACdK,OAAO,GAAGD,CAAC,CAACE,KAAK,CAAC,IAAI,CAACT,QAAQ,CAAC;MAChC,OAAQ,IAAI,CAACE,YAAY,GAAIP,CAAC,CAACe,OAAO,CAACF,OAAO,EAAE,EAAE,EAAE,GAAG,CAAC,GAAGA,OAAO;IACpE,CAAC,MAAM;MACL,OAAOD,CAAC,CAACI,KAAK,CAAC,IAAI,CAACX,QAAQ,CAAC;IAC/B;EACF;AACF;AAEAY,OAAO,CAAChB,eAAe,GAAGA,eAAe;AAEzC,MAAMiB,mBAAmB,GAAGnB,OAAO,CAAC,wBAAwB,CAAC;AAE7D,MAAMoB,oBAAoB,CAAC;EACzBjB,WAAWA,CAAEE,OAAO,EAAE;IACpB,MAAME,OAAO,GAAGY,mBAAmB,CAACd,OAAO,CAACgB,QAAQ,CAAC;IACrDvB,KAAK,IAAIwB,OAAO,CAACC,GAAG,CAAChB,OAAO,CAAC;IAE7B,IAAI,CAACA,OAAO,EAAE;MACZ,IAAI,CAACiB,SAAS,GAAG,IAAIC,aAAa,CAAC,CAAC;IACtC,CAAC,MAAM;MACL,IAAI,CAACD,SAAS,GAAG,IAAItB,eAAe,CAACG,OAAO,CAAC;MAC7C,IAAI,CAACmB,SAAS,CAAClB,QAAQ,GAAGC,OAAO;MACjCT,KAAK,IAAIwB,OAAO,CAACC,GAAG,CAAC,IAAI,CAACC,SAAS,CAAC;IACtC;EACF;EAEAZ,QAAQA,CAAEc,IAAI,EAAE;IACd,OAAO,IAAI,CAACF,SAAS,CAACZ,QAAQ,CAACc,IAAI,CAAC;EACtC;AACF;AAEAR,OAAO,CAACE,oBAAoB,GAAGA,oBAAoB;;AAEnD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMK,aAAa,SAASvB,eAAe,CAAC;EAC1CC,WAAWA,CAAEE,OAAO,EAAE;IACpB,KAAK,CAACA,OAAO,CAAC;IACd,IAAI,CAACC,QAAQ,GAAG,sBAAsB;EACxC;AACF;AAEAY,OAAO,CAACO,aAAa,GAAGA,aAAa;;AAErC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAME,kBAAkB,SAASzB,eAAe,CAAC;EAC/CC,WAAWA,CAAEE,OAAO,EAAE;IACpB,IAAI,CAACA,OAAO,EAAE;MACZA,OAAO,GAAG,CAAC,CAAC;IACd;IACAA,OAAO,CAACE,OAAO,GAAG,8CAA8C;IAChE,KAAK,CAACF,OAAO,CAAC;EAChB;AACF;AAEAa,OAAO,CAACS,kBAAkB,GAAGA,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}