{"ast": null, "code": "/**\n * @license\n * Copyright 2020 Google Inc. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { Reshape } from '../kernel_names';\nimport { reshape } from '../ops/reshape';\nexport const reshapeGradConfig = {\n  kernelName: Reshape,\n  inputsToSave: ['x'],\n  gradFunc: (dy, saved) => {\n    const [x] = saved;\n    return {\n      x: () => reshape(dy, x.shape)\n    };\n  }\n};", "map": {"version": 3, "names": ["Reshape", "reshape", "reshapeGradConfig", "kernelName", "inputsToSave", "grad<PERSON>unc", "dy", "saved", "x", "shape"], "sources": ["C:\\tfjs-core\\src\\gradients\\Reshape_grad.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google Inc. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport {Reshape} from '../kernel_names';\nimport {GradConfig} from '../kernel_registry';\nimport {reshape} from '../ops/reshape';\nimport {Tensor} from '../tensor';\n\nexport const reshapeGradConfig: GradConfig = {\n  kernelName: Reshape,\n  inputsToSave: ['x'],\n  gradFunc: (dy: Tensor, saved: Tensor[]) => {\n    const [x] = saved;\n    return {x: () => reshape(dy, x.shape)};\n  }\n};\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAgBA,SAAQA,OAAO,QAAO,iBAAiB;AAEvC,SAAQC,OAAO,QAAO,gBAAgB;AAGtC,OAAO,MAAMC,iBAAiB,GAAe;EAC3CC,UAAU,EAAEH,OAAO;EACnBI,YAAY,EAAE,CAAC,GAAG,CAAC;EACnBC,QAAQ,EAAEA,CAACC,EAAU,EAAEC,KAAe,KAAI;IACxC,MAAM,CAACC,CAAC,CAAC,GAAGD,KAAK;IACjB,OAAO;MAACC,CAAC,EAAEA,CAAA,KAAMP,OAAO,CAACK,EAAE,EAAEE,CAAC,CAACC,KAAK;IAAC,CAAC;EACxC;CACD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}