{"ast": null, "code": "import keys from './keys.js';\n\n// Retrieve the values of an object's properties.\nexport default function values(obj) {\n  var _keys = keys(obj);\n  var length = _keys.length;\n  var values = Array(length);\n  for (var i = 0; i < length; i++) {\n    values[i] = obj[_keys[i]];\n  }\n  return values;\n}", "map": {"version": 3, "names": ["keys", "values", "obj", "_keys", "length", "Array", "i"], "sources": ["C:/tmsft/node_modules/underscore/modules/values.js"], "sourcesContent": ["import keys from './keys.js';\n\n// Retrieve the values of an object's properties.\nexport default function values(obj) {\n  var _keys = keys(obj);\n  var length = _keys.length;\n  var values = Array(length);\n  for (var i = 0; i < length; i++) {\n    values[i] = obj[_keys[i]];\n  }\n  return values;\n}\n"], "mappings": "AAAA,OAAOA,IAAI,MAAM,WAAW;;AAE5B;AACA,eAAe,SAASC,MAAMA,CAACC,GAAG,EAAE;EAClC,IAAIC,KAAK,GAAGH,IAAI,CAACE,GAAG,CAAC;EACrB,IAAIE,MAAM,GAAGD,KAAK,CAACC,MAAM;EACzB,IAAIH,MAAM,GAAGI,KAAK,CAACD,MAAM,CAAC;EAC1B,KAAK,IAAIE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,MAAM,EAAEE,CAAC,EAAE,EAAE;IAC/BL,MAAM,CAACK,CAAC,CAAC,GAAGJ,GAAG,CAACC,KAAK,CAACG,CAAC,CAAC,CAAC;EAC3B;EACA,OAAOL,MAAM;AACf", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}