{"ast": null, "code": "/**\n * @license\n * Copyright 2023 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nexport const json = [{\n  'tfOpName': 'Cast',\n  'category': 'transformation',\n  'inputs': [{\n    'start': 0,\n    'name': 'x',\n    'type': 'tensor'\n  }],\n  'attrs': [{\n    'tfName': 'SrcT',\n    'name': 'sdtype',\n    'type': 'dtype',\n    'notSupported': true\n  }, {\n    'tfName': 'DstT',\n    'name': 'dtype',\n    'type': 'dtype'\n  }]\n}, {\n  'tfOpName': 'ExpandDims',\n  'category': 'transformation',\n  'inputs': [{\n    'start': 0,\n    'name': 'x',\n    'type': 'tensor'\n  }, {\n    'start': 1,\n    'name': 'axis',\n    'type': 'number'\n  }]\n}, {\n  'tfOpName': 'MirrorPad',\n  'category': 'transformation',\n  'inputs': [{\n    'start': 0,\n    'name': 'x',\n    'type': 'tensor'\n  }, {\n    'start': 1,\n    'name': 'padding',\n    'type': 'number[]'\n  }],\n  'attrs': [{\n    'tfName': 'mode',\n    'name': 'mode',\n    'type': 'string'\n  }]\n}, {\n  'tfOpName': 'Pad',\n  'category': 'transformation',\n  'inputs': [{\n    'start': 0,\n    'name': 'x',\n    'type': 'tensor'\n  }, {\n    'start': 1,\n    'name': 'padding',\n    'type': 'number[]'\n  }],\n  'attrs': [{\n    'tfName': 'constant_value',\n    'name': 'constantValue',\n    'type': 'number',\n    'defaultValue': 0\n  }]\n}, {\n  'tfOpName': 'PadV2',\n  'category': 'transformation',\n  'inputs': [{\n    'start': 0,\n    'name': 'x',\n    'type': 'tensor'\n  }, {\n    'start': 1,\n    'name': 'padding',\n    'type': 'number[]'\n  }, {\n    'start': 2,\n    'name': 'constantValue',\n    'type': 'number',\n    'defaultValue': 0\n  }]\n}, {\n  'tfOpName': 'Reshape',\n  'category': 'transformation',\n  'inputs': [{\n    'start': 0,\n    'name': 'x',\n    'type': 'tensor'\n  }, {\n    'start': 1,\n    'name': 'shape',\n    'type': 'number[]'\n  }]\n}, {\n  'tfOpName': 'EnsureShape',\n  'category': 'transformation',\n  'inputs': [{\n    'start': 0,\n    'name': 'x',\n    'type': 'tensor'\n  }, {\n    'start': 1,\n    'name': 'shape',\n    'type': 'number[]'\n  }]\n}, {\n  'tfOpName': 'Squeeze',\n  'category': 'transformation',\n  'inputs': [{\n    'start': 0,\n    'name': 'x',\n    'type': 'tensor'\n  }],\n  'attrs': [{\n    'tfName': 'axis',\n    'tfDeprecatedName': 'squeeze_dims',\n    'name': 'axis',\n    'type': 'number[]'\n  }]\n}, {\n  'tfOpName': 'SpaceToBatchND',\n  'category': 'transformation',\n  'inputs': [{\n    'start': 0,\n    'name': 'x',\n    'type': 'tensor'\n  }, {\n    'start': 1,\n    'name': 'blockShape',\n    'type': 'number[]'\n  }, {\n    'start': 2,\n    'name': 'paddings',\n    'type': 'number[]'\n  }]\n}, {\n  'tfOpName': 'BatchToSpaceND',\n  'category': 'transformation',\n  'inputs': [{\n    'start': 0,\n    'name': 'x',\n    'type': 'tensor'\n  }, {\n    'start': 1,\n    'name': 'blockShape',\n    'type': 'number[]'\n  }, {\n    'start': 2,\n    'name': 'crops',\n    'type': 'number[]'\n  }]\n}, {\n  'tfOpName': 'DepthToSpace',\n  'category': 'transformation',\n  'inputs': [{\n    'start': 0,\n    'name': 'x',\n    'type': 'tensor'\n  }],\n  'attrs': [{\n    'tfName': 'block_size',\n    'name': 'blockSize',\n    'type': 'number'\n  }, {\n    'tfName': 'data_format',\n    'name': 'dataFormat',\n    'type': 'string'\n  }]\n}, {\n  'tfOpName': 'BroadcastTo',\n  'category': 'transformation',\n  'inputs': [{\n    'start': 0,\n    'name': 'x',\n    'type': 'tensor'\n  }, {\n    'start': 1,\n    'name': 'shape',\n    'type': 'number[]'\n  }],\n  'attrs': []\n}, {\n  'tfOpName': 'BroadcastArgs',\n  'category': 'transformation',\n  'inputs': [{\n    'start': 0,\n    'name': 's0',\n    'type': 'tensor'\n  }, {\n    'start': 1,\n    'name': 's1',\n    'type': 'tensor'\n  }],\n  'attrs': []\n}];", "map": {"version": 3, "names": ["json"], "sources": ["C:\\tfjs-converter\\src\\operations\\op_list\\transformation.ts"], "sourcesContent": ["\n/**\n * @license\n * Copyright 2023 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {OpMapper} from '../types';\n\nexport const json: OpMapper[] = [\n  {\n    'tfOpName': 'Cast',\n    'category': 'transformation',\n    'inputs': [\n      {\n        'start': 0,\n        'name': 'x',\n        'type': 'tensor'\n      }\n    ],\n    'attrs': [\n      {\n        'tfName': 'SrcT',\n        'name': 'sdtype',\n        'type': 'dtype',\n        'notSupported': true\n      },\n      {\n        'tfName': 'DstT',\n        'name': 'dtype',\n        'type': 'dtype'\n      }\n    ]\n  },\n  {\n    'tfOpName': 'ExpandDims',\n    'category': 'transformation',\n    'inputs': [\n      {\n        'start': 0,\n        'name': 'x',\n        'type': 'tensor'\n      },\n      {\n        'start': 1,\n        'name': 'axis',\n        'type': 'number'\n      }\n    ]\n  },\n  {\n    'tfOpName': 'MirrorPad',\n    'category': 'transformation',\n    'inputs': [\n      {\n        'start': 0,\n        'name': 'x',\n        'type': 'tensor'\n      },\n      {\n        'start': 1,\n        'name': 'padding',\n        'type': 'number[]'\n      }\n    ],\n    'attrs': [\n      {\n        'tfName': 'mode',\n        'name': 'mode',\n        'type': 'string'\n      }\n    ]\n  },\n  {\n    'tfOpName': 'Pad',\n    'category': 'transformation',\n    'inputs': [\n      {\n        'start': 0,\n        'name': 'x',\n        'type': 'tensor'\n      },\n      {\n        'start': 1,\n        'name': 'padding',\n        'type': 'number[]'\n      }\n    ],\n    'attrs': [\n      {\n        'tfName': 'constant_value',\n        'name': 'constantValue',\n        'type': 'number',\n        'defaultValue': 0\n      }\n    ]\n  },\n  {\n    'tfOpName': 'PadV2',\n    'category': 'transformation',\n    'inputs': [\n      {\n        'start': 0,\n        'name': 'x',\n        'type': 'tensor'\n      },\n      {\n        'start': 1,\n        'name': 'padding',\n        'type': 'number[]'\n      },\n      {\n        'start': 2,\n        'name': 'constantValue',\n        'type': 'number',\n        'defaultValue': 0\n      }\n    ]\n  },\n  {\n    'tfOpName': 'Reshape',\n    'category': 'transformation',\n    'inputs': [\n      {\n        'start': 0,\n        'name': 'x',\n        'type': 'tensor'\n      },\n      {\n        'start': 1,\n        'name': 'shape',\n        'type': 'number[]'\n      }\n    ]\n  },\n  {\n    'tfOpName': 'EnsureShape',\n    'category': 'transformation',\n    'inputs': [\n      {\n        'start': 0,\n        'name': 'x',\n        'type': 'tensor'\n      },\n      {\n        'start': 1,\n        'name': 'shape',\n        'type': 'number[]'\n      }\n    ]\n  },\n  {\n    'tfOpName': 'Squeeze',\n    'category': 'transformation',\n    'inputs': [\n      {\n        'start': 0,\n        'name': 'x',\n        'type': 'tensor'\n      }\n    ],\n    'attrs': [\n      {\n        'tfName': 'axis',\n        'tfDeprecatedName': 'squeeze_dims',\n        'name': 'axis',\n        'type': 'number[]'\n      }\n    ]\n  },\n  {\n    'tfOpName': 'SpaceToBatchND',\n    'category': 'transformation',\n    'inputs': [\n      {\n        'start': 0,\n        'name': 'x',\n        'type': 'tensor'\n      },\n      {\n        'start': 1,\n        'name': 'blockShape',\n        'type': 'number[]'\n      },\n      {\n        'start': 2,\n        'name': 'paddings',\n        'type': 'number[]'\n      }\n    ]\n  },\n  {\n    'tfOpName': 'BatchToSpaceND',\n    'category': 'transformation',\n    'inputs': [\n      {\n        'start': 0,\n        'name': 'x',\n        'type': 'tensor'\n      },\n      {\n        'start': 1,\n        'name': 'blockShape',\n        'type': 'number[]'\n      },\n      {\n        'start': 2,\n        'name': 'crops',\n        'type': 'number[]'\n      }\n    ]\n  },\n  {\n    'tfOpName': 'DepthToSpace',\n    'category': 'transformation',\n    'inputs': [\n      {\n        'start': 0,\n        'name': 'x',\n        'type': 'tensor'\n      }\n    ],\n    'attrs': [\n      {\n        'tfName': 'block_size',\n        'name': 'blockSize',\n        'type': 'number'\n      },\n      {\n        'tfName': 'data_format',\n        'name': 'dataFormat',\n        'type': 'string'\n      }\n    ]\n  },\n  {\n    'tfOpName': 'BroadcastTo',\n    'category': 'transformation',\n    'inputs': [\n      {\n        'start': 0,\n        'name': 'x',\n        'type': 'tensor'\n      },\n      {\n        'start': 1,\n        'name': 'shape',\n        'type': 'number[]'\n      }\n    ],\n    'attrs': []\n  },\n  {\n    'tfOpName': 'BroadcastArgs',\n    'category': 'transformation',\n    'inputs': [\n      {\n        'start': 0,\n        'name': 's0',\n        'type': 'tensor'\n      },\n      {\n        'start': 1,\n        'name': 's1',\n        'type': 'tensor'\n      }\n    ],\n    'attrs': []\n  }\n]\n;\n"], "mappings": "AACA;;;;;;;;;;;;;;;;AAmBA,OAAO,MAAMA,IAAI,GAAe,CAC9B;EACE,UAAU,EAAE,MAAM;EAClB,UAAU,EAAE,gBAAgB;EAC5B,QAAQ,EAAE,CACR;IACE,OAAO,EAAE,CAAC;IACV,MAAM,EAAE,GAAG;IACX,MAAM,EAAE;GACT,CACF;EACD,OAAO,EAAE,CACP;IACE,QAAQ,EAAE,MAAM;IAChB,MAAM,EAAE,QAAQ;IAChB,MAAM,EAAE,OAAO;IACf,cAAc,EAAE;GACjB,EACD;IACE,QAAQ,EAAE,MAAM;IAChB,MAAM,EAAE,OAAO;IACf,MAAM,EAAE;GACT;CAEJ,EACD;EACE,UAAU,EAAE,YAAY;EACxB,UAAU,EAAE,gBAAgB;EAC5B,QAAQ,EAAE,CACR;IACE,OAAO,EAAE,CAAC;IACV,MAAM,EAAE,GAAG;IACX,MAAM,EAAE;GACT,EACD;IACE,OAAO,EAAE,CAAC;IACV,MAAM,EAAE,MAAM;IACd,MAAM,EAAE;GACT;CAEJ,EACD;EACE,UAAU,EAAE,WAAW;EACvB,UAAU,EAAE,gBAAgB;EAC5B,QAAQ,EAAE,CACR;IACE,OAAO,EAAE,CAAC;IACV,MAAM,EAAE,GAAG;IACX,MAAM,EAAE;GACT,EACD;IACE,OAAO,EAAE,CAAC;IACV,MAAM,EAAE,SAAS;IACjB,MAAM,EAAE;GACT,CACF;EACD,OAAO,EAAE,CACP;IACE,QAAQ,EAAE,MAAM;IAChB,MAAM,EAAE,MAAM;IACd,MAAM,EAAE;GACT;CAEJ,EACD;EACE,UAAU,EAAE,KAAK;EACjB,UAAU,EAAE,gBAAgB;EAC5B,QAAQ,EAAE,CACR;IACE,OAAO,EAAE,CAAC;IACV,MAAM,EAAE,GAAG;IACX,MAAM,EAAE;GACT,EACD;IACE,OAAO,EAAE,CAAC;IACV,MAAM,EAAE,SAAS;IACjB,MAAM,EAAE;GACT,CACF;EACD,OAAO,EAAE,CACP;IACE,QAAQ,EAAE,gBAAgB;IAC1B,MAAM,EAAE,eAAe;IACvB,MAAM,EAAE,QAAQ;IAChB,cAAc,EAAE;GACjB;CAEJ,EACD;EACE,UAAU,EAAE,OAAO;EACnB,UAAU,EAAE,gBAAgB;EAC5B,QAAQ,EAAE,CACR;IACE,OAAO,EAAE,CAAC;IACV,MAAM,EAAE,GAAG;IACX,MAAM,EAAE;GACT,EACD;IACE,OAAO,EAAE,CAAC;IACV,MAAM,EAAE,SAAS;IACjB,MAAM,EAAE;GACT,EACD;IACE,OAAO,EAAE,CAAC;IACV,MAAM,EAAE,eAAe;IACvB,MAAM,EAAE,QAAQ;IAChB,cAAc,EAAE;GACjB;CAEJ,EACD;EACE,UAAU,EAAE,SAAS;EACrB,UAAU,EAAE,gBAAgB;EAC5B,QAAQ,EAAE,CACR;IACE,OAAO,EAAE,CAAC;IACV,MAAM,EAAE,GAAG;IACX,MAAM,EAAE;GACT,EACD;IACE,OAAO,EAAE,CAAC;IACV,MAAM,EAAE,OAAO;IACf,MAAM,EAAE;GACT;CAEJ,EACD;EACE,UAAU,EAAE,aAAa;EACzB,UAAU,EAAE,gBAAgB;EAC5B,QAAQ,EAAE,CACR;IACE,OAAO,EAAE,CAAC;IACV,MAAM,EAAE,GAAG;IACX,MAAM,EAAE;GACT,EACD;IACE,OAAO,EAAE,CAAC;IACV,MAAM,EAAE,OAAO;IACf,MAAM,EAAE;GACT;CAEJ,EACD;EACE,UAAU,EAAE,SAAS;EACrB,UAAU,EAAE,gBAAgB;EAC5B,QAAQ,EAAE,CACR;IACE,OAAO,EAAE,CAAC;IACV,MAAM,EAAE,GAAG;IACX,MAAM,EAAE;GACT,CACF;EACD,OAAO,EAAE,CACP;IACE,QAAQ,EAAE,MAAM;IAChB,kBAAkB,EAAE,cAAc;IAClC,MAAM,EAAE,MAAM;IACd,MAAM,EAAE;GACT;CAEJ,EACD;EACE,UAAU,EAAE,gBAAgB;EAC5B,UAAU,EAAE,gBAAgB;EAC5B,QAAQ,EAAE,CACR;IACE,OAAO,EAAE,CAAC;IACV,MAAM,EAAE,GAAG;IACX,MAAM,EAAE;GACT,EACD;IACE,OAAO,EAAE,CAAC;IACV,MAAM,EAAE,YAAY;IACpB,MAAM,EAAE;GACT,EACD;IACE,OAAO,EAAE,CAAC;IACV,MAAM,EAAE,UAAU;IAClB,MAAM,EAAE;GACT;CAEJ,EACD;EACE,UAAU,EAAE,gBAAgB;EAC5B,UAAU,EAAE,gBAAgB;EAC5B,QAAQ,EAAE,CACR;IACE,OAAO,EAAE,CAAC;IACV,MAAM,EAAE,GAAG;IACX,MAAM,EAAE;GACT,EACD;IACE,OAAO,EAAE,CAAC;IACV,MAAM,EAAE,YAAY;IACpB,MAAM,EAAE;GACT,EACD;IACE,OAAO,EAAE,CAAC;IACV,MAAM,EAAE,OAAO;IACf,MAAM,EAAE;GACT;CAEJ,EACD;EACE,UAAU,EAAE,cAAc;EAC1B,UAAU,EAAE,gBAAgB;EAC5B,QAAQ,EAAE,CACR;IACE,OAAO,EAAE,CAAC;IACV,MAAM,EAAE,GAAG;IACX,MAAM,EAAE;GACT,CACF;EACD,OAAO,EAAE,CACP;IACE,QAAQ,EAAE,YAAY;IACtB,MAAM,EAAE,WAAW;IACnB,MAAM,EAAE;GACT,EACD;IACE,QAAQ,EAAE,aAAa;IACvB,MAAM,EAAE,YAAY;IACpB,MAAM,EAAE;GACT;CAEJ,EACD;EACE,UAAU,EAAE,aAAa;EACzB,UAAU,EAAE,gBAAgB;EAC5B,QAAQ,EAAE,CACR;IACE,OAAO,EAAE,CAAC;IACV,MAAM,EAAE,GAAG;IACX,MAAM,EAAE;GACT,EACD;IACE,OAAO,EAAE,CAAC;IACV,MAAM,EAAE,OAAO;IACf,MAAM,EAAE;GACT,CACF;EACD,OAAO,EAAE;CACV,EACD;EACE,UAAU,EAAE,eAAe;EAC3B,UAAU,EAAE,gBAAgB;EAC5B,QAAQ,EAAE,CACR;IACE,OAAO,EAAE,CAAC;IACV,MAAM,EAAE,IAAI;IACZ,MAAM,EAAE;GACT,EACD;IACE,OAAO,EAAE,CAAC;IACV,MAAM,EAAE,IAAI;IACZ,MAAM,EAAE;GACT,CACF;EACD,OAAO,EAAE;CACV,CACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}