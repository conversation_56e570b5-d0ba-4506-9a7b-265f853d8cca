{"ast": null, "code": "import _objectSpread from\"C:/tmsft/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";import{categorizationService}from'./categorizationService';class MLCategorizationService{constructor(){this.config={modelName:'qwen3:32b',ollamaEndpoint:'http://localhost:11434',confidenceThreshold:0.7,maxRetries:3,timeout:30000,batchSize:10,trainingDataPath:'treasury_ml_training_data'};this.isOllamaAvailable=false;this.modelLoaded=false;this.checkOllamaAvailability();}// Check if Ollama is running and Qwen3:32b is available\nasync checkOllamaAvailability(){try{const controller=new AbortController();const timeoutId=setTimeout(()=>controller.abort(),5000);const response=await fetch(\"\".concat(this.config.ollamaEndpoint,\"/api/tags\"),{signal:controller.signal});clearTimeout(timeoutId);if(response.ok){const data=await response.json();const models=data.models||[];this.modelLoaded=models.some(model=>{var _model$name;return model.name===this.config.modelName||((_model$name=model.name)===null||_model$name===void 0?void 0:_model$name.startsWith('qwen3:32b'));});this.isOllamaAvailable=true;if(!this.modelLoaded){console.warn('Qwen3:32b model not found. Please run: ollama pull qwen3:32b');}return this.isOllamaAvailable&&this.modelLoaded;}}catch(error){console.warn('Ollama not available:',error);this.isOllamaAvailable=false;this.modelLoaded=false;}return false;}// Generate categorization prompt for the ML model\ngenerateCategorizationPrompt(transaction,categories){const amount=transaction.debitAmount?\"-\".concat(transaction.debitAmount):\"+\".concat(transaction.creditAmount);const categoryList=categories.map(cat=>{var _cat$keywords;return\"\".concat(cat.id,\": \").concat(cat.name,\" - \").concat(cat.description,\" (keywords: \").concat(((_cat$keywords=cat.keywords)===null||_cat$keywords===void 0?void 0:_cat$keywords.join(', '))||'none',\")\");}).join('\\n');return\"You are a financial transaction categorization expert. Analyze the following transaction and categorize it.\\n\\nTransaction Details:\\n- Description: \\\"\".concat(transaction.description,\"\\\"\\n- Amount: \").concat(amount,\"\\n- Date: \").concat(transaction.date,\"\\n- Reference: \").concat(transaction.reference||'N/A',\"\\n\\nAvailable Categories:\\n\").concat(categoryList,\"\\n\\nInstructions:\\n1. Analyze the transaction description, amount, and context\\n2. Select the most appropriate category ID from the list above\\n3. Provide a confidence score between 0.0 and 1.0\\n4. Explain your reasoning in 1-2 sentences\\n5. Suggest up to 2 alternative categories if applicable\\n\\nRespond in the following JSON format only:\\n{\\n  \\\"categoryId\\\": \\\"selected_category_id\\\",\\n  \\\"confidence\\\": 0.85,\\n  \\\"reasoning\\\": \\\"Brief explanation of why this category was chosen\\\",\\n  \\\"alternativeCategories\\\": [\\n    {\\\"categoryId\\\": \\\"alt_category_id\\\", \\\"confidence\\\": 0.65}\\n  ]\\n}\");}// Call Ollama API for categorization\nasync callOllamaAPI(prompt){if(!this.isOllamaAvailable||!this.modelLoaded){await this.checkOllamaAvailability();if(!this.isOllamaAvailable||!this.modelLoaded){throw new Error('Ollama service is not available or Qwen3:32b model is not loaded');}}const controller=new AbortController();const timeoutId=setTimeout(()=>controller.abort(),this.config.timeout);try{const response=await fetch(\"\".concat(this.config.ollamaEndpoint,\"/api/generate\"),{method:'POST',headers:{'Content-Type':'application/json'},body:JSON.stringify({model:this.config.modelName,prompt:prompt,stream:false,options:{temperature:0.1,// Low temperature for consistent results\ntop_k:10,top_p:0.9,num_predict:500}}),signal:controller.signal});clearTimeout(timeoutId);if(!response.ok){throw new Error(\"Ollama API error: \".concat(response.status,\" \").concat(response.statusText));}const data=await response.json();const responseText=data.response;// Parse JSON response from the model\ntry{const jsonMatch=responseText.match(/\\{[\\s\\S]*\\}/);if(jsonMatch){const result=JSON.parse(jsonMatch[0]);// Validate the response structure\nif(result.categoryId&&typeof result.confidence==='number'){return{categoryId:result.categoryId,confidence:Math.max(0,Math.min(1,result.confidence)),reasoning:result.reasoning||'No reasoning provided',alternativeCategories:result.alternativeCategories||[]};}}throw new Error('Invalid response format from ML model');}catch(parseError){console.error('Failed to parse ML response:',parseError);console.error('Raw response:',responseText);return null;}}catch(error){clearTimeout(timeoutId);if(error instanceof Error&&error.name==='AbortError'){throw new Error('ML categorization request timed out');}throw error;}}// Categorize a single transaction using ML\nasync categorizeTransaction(transaction){try{const categories=categorizationService.getAllCategories();if(categories.length===0){throw new Error('No categories available for ML categorization');}const prompt=this.generateCategorizationPrompt(transaction,categories);const result=await this.callOllamaAPI(prompt);if(result){// Validate that the returned category ID exists\nconst categoryExists=categories.some(cat=>cat.id===result.categoryId);if(!categoryExists){console.warn(\"ML model returned unknown category ID: \".concat(result.categoryId));// Fallback to uncategorized\nresult.categoryId='cat_uncategorized';result.confidence=0.1;result.reasoning='ML model returned unknown category, defaulted to uncategorized';}// Apply categorization if confidence is above threshold\nif(result.confidence>=this.config.confidenceThreshold){categorizationService.categorizeTransaction(transaction.id,result.categoryId,'ml',result.confidence,result.reasoning);}}return result;}catch(error){console.error('ML categorization failed:',error);return null;}}// Categorize multiple transactions in batches\nasync categorizeTransactionsBatch(transactions){const results=[];// Process in batches to avoid overwhelming the system\nfor(let i=0;i<transactions.length;i+=this.config.batchSize){const batch=transactions.slice(i,i+this.config.batchSize);const batchPromises=batch.map(async transaction=>{try{const result=await this.categorizeTransaction(transaction);return{transaction,result};}catch(error){return{transaction,result:null,error:error instanceof Error?error.message:'Unknown error'};}});const batchResults=await Promise.all(batchPromises);results.push(...batchResults);// Small delay between batches to prevent overwhelming Ollama\nif(i+this.config.batchSize<transactions.length){await new Promise(resolve=>setTimeout(resolve,1000));}}return results;}// Train the model with historical data (prepare training data)\nasync prepareTrainingData(){try{// Get all categorized transactions for training\ncategorizationService.getAllCategories();// This is a simplified training data preparation\n// In a full implementation, you might export this data for fine-tuning\nconst trainingData={id:'training_'+Date.now(),description:'Training data prepared for ML categorization',amount:0,categoryId:'training',features:[],createdDate:new Date().toISOString()};// Store training data for potential future use\nlocalStorage.setItem(this.config.trainingDataPath,JSON.stringify(trainingData));return trainingData;}catch(error){console.error('Failed to prepare training data:',error);return null;}}// Get model status and configuration\ngetModelStatus(){return{isAvailable:this.isOllamaAvailable,modelLoaded:this.modelLoaded,config:this.config,lastCheck:new Date().toISOString()};}// Update configuration\nupdateConfig(updates){this.config=_objectSpread(_objectSpread({},this.config),updates);// Re-check availability if endpoint changed\nif(updates.ollamaEndpoint){this.checkOllamaAvailability();}}// Test the ML categorization with a sample transaction\nasync testCategorization(){const startTime=Date.now();try{const testTransaction={id:'test_transaction',description:'Monthly office rent payment',date:new Date().toISOString(),debitAmount:2500.00,creditAmount:0,balance:10000.00};const result=await this.categorizeTransaction(testTransaction);const latency=Date.now()-startTime;return{success:true,result:result||undefined,latency};}catch(error){return{success:false,error:error instanceof Error?error.message:'Unknown error',latency:Date.now()-startTime};}}}export const mlCategorizationService=new MLCategorizationService();", "map": {"version": 3, "names": ["categorizationService", "MLCategorizationService", "constructor", "config", "modelName", "ollamaEndpoint", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "maxRetries", "timeout", "batchSize", "trainingDataPath", "isOllamaAvailable", "modelLoaded", "checkOllamaAvailability", "controller", "AbortController", "timeoutId", "setTimeout", "abort", "response", "fetch", "concat", "signal", "clearTimeout", "ok", "data", "json", "models", "some", "model", "_model$name", "name", "startsWith", "console", "warn", "error", "generateCategorizationPrompt", "transaction", "categories", "amount", "debitAmount", "creditAmount", "categoryList", "map", "cat", "_cat$keywords", "id", "description", "keywords", "join", "date", "reference", "callOllamaAPI", "prompt", "Error", "method", "headers", "body", "JSON", "stringify", "stream", "options", "temperature", "top_k", "top_p", "num_predict", "status", "statusText", "responseText", "jsonMatch", "match", "result", "parse", "categoryId", "confidence", "Math", "max", "min", "reasoning", "alternativeCategories", "parseError", "categorizeTransaction", "getAllCategories", "length", "categoryExists", "categorizeTransactionsBatch", "transactions", "results", "i", "batch", "slice", "batchPromises", "message", "batchResults", "Promise", "all", "push", "resolve", "prepareTrainingData", "trainingData", "Date", "now", "features", "createdDate", "toISOString", "localStorage", "setItem", "getModelStatus", "isAvailable", "<PERSON><PERSON><PERSON><PERSON>", "updateConfig", "updates", "_objectSpread", "testCategorization", "startTime", "testTransaction", "balance", "latency", "success", "undefined", "mlCategorizationService"], "sources": ["C:/tmsft/src/services/mlCategorizationService.ts"], "sourcesContent": ["import { Transaction, TransactionCategory, MLCategorizationResult, MLCategorizationConfig, TrainingData } from '../types';\r\nimport { categorizationService } from './categorizationService';\r\n\r\nclass MLCategorizationService {\r\n  private config: MLCategorizationConfig = {\r\n    modelName: 'qwen3:32b',\r\n    ollamaEndpoint: 'http://localhost:11434',\r\n    confidenceThreshold: 0.7,\r\n    maxRetries: 3,\r\n    timeout: 30000,\r\n    batchSize: 10,\r\n    trainingDataPath: 'treasury_ml_training_data'\r\n  };\r\n\r\n  private isOllamaAvailable = false;\r\n  private modelLoaded = false;\r\n\r\n  constructor() {\r\n    this.checkOllamaAvailability();\r\n  }\r\n\r\n  // Check if Ollama is running and Qwen3:32b is available\r\n  async checkOllamaAvailability(): Promise<boolean> {\r\n    try {\r\n      const controller = new AbortController();\r\n      const timeoutId = setTimeout(() => controller.abort(), 5000);\r\n\r\n      const response = await fetch(`${this.config.ollamaEndpoint}/api/tags`, {\r\n        signal: controller.signal\r\n      });\r\n      \r\n      clearTimeout(timeoutId);\r\n\r\n      if (response.ok) {\r\n        const data = await response.json();\r\n        const models = data.models || [];\r\n        this.modelLoaded = models.some((model: Record<string, unknown>) => \r\n          model.name === this.config.modelName || \r\n          (model.name as string)?.startsWith('qwen3:32b')\r\n        );\r\n        this.isOllamaAvailable = true;\r\n        \r\n        if (!this.modelLoaded) {\r\n          console.warn('Qwen3:32b model not found. Please run: ollama pull qwen3:32b');\r\n        }\r\n        \r\n        return this.isOllamaAvailable && this.modelLoaded;\r\n      }\r\n    } catch (error) {\r\n      console.warn('Ollama not available:', error);\r\n      this.isOllamaAvailable = false;\r\n      this.modelLoaded = false;\r\n    }\r\n    \r\n    return false;\r\n  }\r\n\r\n  // Generate categorization prompt for the ML model\r\n  private generateCategorizationPrompt(\r\n    transaction: Transaction, \r\n    categories: TransactionCategory[]\r\n  ): string {\r\n    const amount = transaction.debitAmount ? `-${transaction.debitAmount}` : `+${transaction.creditAmount}`;\r\n    const categoryList = categories.map(cat => \r\n      `${cat.id}: ${cat.name} - ${cat.description} (keywords: ${cat.keywords?.join(', ') || 'none'})`\r\n    ).join('\\n');\r\n\r\n    return `You are a financial transaction categorization expert. Analyze the following transaction and categorize it.\r\n\r\nTransaction Details:\r\n- Description: \"${transaction.description}\"\r\n- Amount: ${amount}\r\n- Date: ${transaction.date}\r\n- Reference: ${transaction.reference || 'N/A'}\r\n\r\nAvailable Categories:\r\n${categoryList}\r\n\r\nInstructions:\r\n1. Analyze the transaction description, amount, and context\r\n2. Select the most appropriate category ID from the list above\r\n3. Provide a confidence score between 0.0 and 1.0\r\n4. Explain your reasoning in 1-2 sentences\r\n5. Suggest up to 2 alternative categories if applicable\r\n\r\nRespond in the following JSON format only:\r\n{\r\n  \"categoryId\": \"selected_category_id\",\r\n  \"confidence\": 0.85,\r\n  \"reasoning\": \"Brief explanation of why this category was chosen\",\r\n  \"alternativeCategories\": [\r\n    {\"categoryId\": \"alt_category_id\", \"confidence\": 0.65}\r\n  ]\r\n}`;\r\n  }\r\n\r\n  // Call Ollama API for categorization\r\n  private async callOllamaAPI(prompt: string): Promise<MLCategorizationResult | null> {\r\n    if (!this.isOllamaAvailable || !this.modelLoaded) {\r\n      await this.checkOllamaAvailability();\r\n      if (!this.isOllamaAvailable || !this.modelLoaded) {\r\n        throw new Error('Ollama service is not available or Qwen3:32b model is not loaded');\r\n      }\r\n    }\r\n\r\n    const controller = new AbortController();\r\n    const timeoutId = setTimeout(() => controller.abort(), this.config.timeout);\r\n\r\n    try {\r\n      const response = await fetch(`${this.config.ollamaEndpoint}/api/generate`, {\r\n        method: 'POST',\r\n        headers: {\r\n          'Content-Type': 'application/json',\r\n        },\r\n        body: JSON.stringify({\r\n          model: this.config.modelName,\r\n          prompt: prompt,\r\n          stream: false,\r\n          options: {\r\n            temperature: 0.1, // Low temperature for consistent results\r\n            top_k: 10,\r\n            top_p: 0.9,\r\n            num_predict: 500\r\n          }\r\n        }),\r\n        signal: controller.signal\r\n      });\r\n\r\n      clearTimeout(timeoutId);\r\n\r\n      if (!response.ok) {\r\n        throw new Error(`Ollama API error: ${response.status} ${response.statusText}`);\r\n      }\r\n\r\n      const data = await response.json();\r\n      const responseText = data.response;\r\n\r\n      // Parse JSON response from the model\r\n      try {\r\n        const jsonMatch = responseText.match(/\\{[\\s\\S]*\\}/);\r\n        if (jsonMatch) {\r\n          const result = JSON.parse(jsonMatch[0]);\r\n          \r\n          // Validate the response structure\r\n          if (result.categoryId && typeof result.confidence === 'number') {\r\n            return {\r\n              categoryId: result.categoryId,\r\n              confidence: Math.max(0, Math.min(1, result.confidence)),\r\n              reasoning: result.reasoning || 'No reasoning provided',\r\n              alternativeCategories: result.alternativeCategories || []\r\n            };\r\n          }\r\n        }\r\n        throw new Error('Invalid response format from ML model');\r\n      } catch (parseError) {\r\n        console.error('Failed to parse ML response:', parseError);\r\n        console.error('Raw response:', responseText);\r\n        return null;\r\n      }\r\n    } catch (error) {\r\n      clearTimeout(timeoutId);\r\n      if (error instanceof Error && error.name === 'AbortError') {\r\n        throw new Error('ML categorization request timed out');\r\n      }\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  // Categorize a single transaction using ML\r\n  async categorizeTransaction(transaction: Transaction): Promise<MLCategorizationResult | null> {\r\n    try {\r\n      const categories = categorizationService.getAllCategories();\r\n      if (categories.length === 0) {\r\n        throw new Error('No categories available for ML categorization');\r\n      }\r\n\r\n      const prompt = this.generateCategorizationPrompt(transaction, categories);\r\n      const result = await this.callOllamaAPI(prompt);\r\n\r\n      if (result) {\r\n        // Validate that the returned category ID exists\r\n        const categoryExists = categories.some(cat => cat.id === result.categoryId);\r\n        if (!categoryExists) {\r\n          console.warn(`ML model returned unknown category ID: ${result.categoryId}`);\r\n          // Fallback to uncategorized\r\n          result.categoryId = 'cat_uncategorized';\r\n          result.confidence = 0.1;\r\n          result.reasoning = 'ML model returned unknown category, defaulted to uncategorized';\r\n        }\r\n\r\n        // Apply categorization if confidence is above threshold\r\n        if (result.confidence >= this.config.confidenceThreshold) {\r\n          categorizationService.categorizeTransaction(\r\n            transaction.id,\r\n            result.categoryId,\r\n            'ml',\r\n            result.confidence,\r\n            result.reasoning\r\n          );\r\n        }\r\n      }\r\n\r\n      return result;\r\n    } catch (error) {\r\n      console.error('ML categorization failed:', error);\r\n      return null;\r\n    }\r\n  }\r\n\r\n  // Categorize multiple transactions in batches\r\n  async categorizeTransactionsBatch(transactions: Transaction[]): Promise<Array<{\r\n    transaction: Transaction;\r\n    result: MLCategorizationResult | null;\r\n    error?: string;\r\n  }>> {\r\n    const results: Array<{\r\n      transaction: Transaction;\r\n      result: MLCategorizationResult | null;\r\n      error?: string;\r\n    }> = [];\r\n\r\n    // Process in batches to avoid overwhelming the system\r\n    for (let i = 0; i < transactions.length; i += this.config.batchSize) {\r\n      const batch = transactions.slice(i, i + this.config.batchSize);\r\n      \r\n      const batchPromises = batch.map(async (transaction) => {\r\n        try {\r\n          const result = await this.categorizeTransaction(transaction);\r\n          return { transaction, result };\r\n        } catch (error) {\r\n          return { \r\n            transaction, \r\n            result: null, \r\n            error: error instanceof Error ? error.message : 'Unknown error' \r\n          };\r\n        }\r\n      });\r\n\r\n      const batchResults = await Promise.all(batchPromises);\r\n      results.push(...batchResults);\r\n\r\n      // Small delay between batches to prevent overwhelming Ollama\r\n      if (i + this.config.batchSize < transactions.length) {\r\n        await new Promise(resolve => setTimeout(resolve, 1000));\r\n      }\r\n    }\r\n\r\n    return results;\r\n  }\r\n\r\n  // Train the model with historical data (prepare training data)\r\n  async prepareTrainingData(): Promise<TrainingData | null> {\r\n    try {\r\n      // Get all categorized transactions for training\r\n      categorizationService.getAllCategories();\r\n      \r\n      // This is a simplified training data preparation\r\n      // In a full implementation, you might export this data for fine-tuning\r\n      const trainingData: TrainingData = {\r\n        id: 'training_' + Date.now(),\r\n        description: 'Training data prepared for ML categorization',\r\n        amount: 0,\r\n        categoryId: 'training',\r\n        features: [],\r\n        createdDate: new Date().toISOString()\r\n      };\r\n\r\n      // Store training data for potential future use\r\n      localStorage.setItem(this.config.trainingDataPath!, JSON.stringify(trainingData));\r\n      \r\n      return trainingData;\r\n    } catch (error) {\r\n      console.error('Failed to prepare training data:', error);\r\n      return null;\r\n    }\r\n  }\r\n\r\n  // Get model status and configuration\r\n  getModelStatus(): {\r\n    isAvailable: boolean;\r\n    modelLoaded: boolean;\r\n    config: MLCategorizationConfig;\r\n    lastCheck: string;\r\n  } {\r\n    return {\r\n      isAvailable: this.isOllamaAvailable,\r\n      modelLoaded: this.modelLoaded,\r\n      config: this.config,\r\n      lastCheck: new Date().toISOString()\r\n    };\r\n  }\r\n\r\n  // Update configuration\r\n  updateConfig(updates: Partial<MLCategorizationConfig>): void {\r\n    this.config = { ...this.config, ...updates };\r\n    \r\n    // Re-check availability if endpoint changed\r\n    if (updates.ollamaEndpoint) {\r\n      this.checkOllamaAvailability();\r\n    }\r\n  }\r\n\r\n  // Test the ML categorization with a sample transaction\r\n  async testCategorization(): Promise<{\r\n    success: boolean;\r\n    result?: MLCategorizationResult;\r\n    error?: string;\r\n    latency?: number;\r\n  }> {\r\n    const startTime = Date.now();\r\n    \r\n    try {\r\n      const testTransaction: Transaction = {\r\n        id: 'test_transaction',\r\n        description: 'Monthly office rent payment',\r\n        date: new Date().toISOString(),\r\n        debitAmount: 2500.00,\r\n        creditAmount: 0,\r\n        balance: 10000.00\r\n      };\r\n\r\n      const result = await this.categorizeTransaction(testTransaction);\r\n      const latency = Date.now() - startTime;\r\n\r\n      return {\r\n        success: true,\r\n        result: result || undefined,\r\n        latency\r\n      };\r\n    } catch (error) {\r\n      return {\r\n        success: false,\r\n        error: error instanceof Error ? error.message : 'Unknown error',\r\n        latency: Date.now() - startTime\r\n      };\r\n    }\r\n  }\r\n}\r\n\r\nexport const mlCategorizationService = new MLCategorizationService(); "], "mappings": "6FACA,OAASA,qBAAqB,KAAQ,yBAAyB,CAE/D,KAAM,CAAAC,uBAAwB,CAc5BC,WAAWA,CAAA,CAAG,MAbNC,MAAM,CAA2B,CACvCC,SAAS,CAAE,WAAW,CACtBC,cAAc,CAAE,wBAAwB,CACxCC,mBAAmB,CAAE,GAAG,CACxBC,UAAU,CAAE,CAAC,CACbC,OAAO,CAAE,KAAK,CACdC,SAAS,CAAE,EAAE,CACbC,gBAAgB,CAAE,2BACpB,CAAC,MAEOC,iBAAiB,CAAG,KAAK,MACzBC,WAAW,CAAG,KAAK,CAGzB,IAAI,CAACC,uBAAuB,CAAC,CAAC,CAChC,CAEA;AACA,KAAM,CAAAA,uBAAuBA,CAAA,CAAqB,CAChD,GAAI,CACF,KAAM,CAAAC,UAAU,CAAG,GAAI,CAAAC,eAAe,CAAC,CAAC,CACxC,KAAM,CAAAC,SAAS,CAAGC,UAAU,CAAC,IAAMH,UAAU,CAACI,KAAK,CAAC,CAAC,CAAE,IAAI,CAAC,CAE5D,KAAM,CAAAC,QAAQ,CAAG,KAAM,CAAAC,KAAK,IAAAC,MAAA,CAAI,IAAI,CAAClB,MAAM,CAACE,cAAc,cAAa,CACrEiB,MAAM,CAAER,UAAU,CAACQ,MACrB,CAAC,CAAC,CAEFC,YAAY,CAACP,SAAS,CAAC,CAEvB,GAAIG,QAAQ,CAACK,EAAE,CAAE,CACf,KAAM,CAAAC,IAAI,CAAG,KAAM,CAAAN,QAAQ,CAACO,IAAI,CAAC,CAAC,CAClC,KAAM,CAAAC,MAAM,CAAGF,IAAI,CAACE,MAAM,EAAI,EAAE,CAChC,IAAI,CAACf,WAAW,CAAGe,MAAM,CAACC,IAAI,CAAEC,KAA8B,OAAAC,WAAA,OAC5D,CAAAD,KAAK,CAACE,IAAI,GAAK,IAAI,CAAC5B,MAAM,CAACC,SAAS,IAAA0B,WAAA,CACnCD,KAAK,CAACE,IAAI,UAAAD,WAAA,iBAAXA,WAAA,CAAwBE,UAAU,CAAC,WAAW,CAAC,GACjD,CAAC,CACD,IAAI,CAACrB,iBAAiB,CAAG,IAAI,CAE7B,GAAI,CAAC,IAAI,CAACC,WAAW,CAAE,CACrBqB,OAAO,CAACC,IAAI,CAAC,8DAA8D,CAAC,CAC9E,CAEA,MAAO,KAAI,CAACvB,iBAAiB,EAAI,IAAI,CAACC,WAAW,CACnD,CACF,CAAE,MAAOuB,KAAK,CAAE,CACdF,OAAO,CAACC,IAAI,CAAC,uBAAuB,CAAEC,KAAK,CAAC,CAC5C,IAAI,CAACxB,iBAAiB,CAAG,KAAK,CAC9B,IAAI,CAACC,WAAW,CAAG,KAAK,CAC1B,CAEA,MAAO,MAAK,CACd,CAEA;AACQwB,4BAA4BA,CAClCC,WAAwB,CACxBC,UAAiC,CACzB,CACR,KAAM,CAAAC,MAAM,CAAGF,WAAW,CAACG,WAAW,KAAAnB,MAAA,CAAOgB,WAAW,CAACG,WAAW,MAAAnB,MAAA,CAASgB,WAAW,CAACI,YAAY,CAAE,CACvG,KAAM,CAAAC,YAAY,CAAGJ,UAAU,CAACK,GAAG,CAACC,GAAG,OAAAC,aAAA,UAAAxB,MAAA,CAClCuB,GAAG,CAACE,EAAE,OAAAzB,MAAA,CAAKuB,GAAG,CAACb,IAAI,QAAAV,MAAA,CAAMuB,GAAG,CAACG,WAAW,iBAAA1B,MAAA,CAAe,EAAAwB,aAAA,CAAAD,GAAG,CAACI,QAAQ,UAAAH,aAAA,iBAAZA,aAAA,CAAcI,IAAI,CAAC,IAAI,CAAC,GAAI,MAAM,OAC9F,CAAC,CAACA,IAAI,CAAC,IAAI,CAAC,CAEZ,+JAAA5B,MAAA,CAGcgB,WAAW,CAACU,WAAW,mBAAA1B,MAAA,CAC7BkB,MAAM,eAAAlB,MAAA,CACRgB,WAAW,CAACa,IAAI,oBAAA7B,MAAA,CACXgB,WAAW,CAACc,SAAS,EAAI,KAAK,gCAAA9B,MAAA,CAG3CqB,YAAY,olBAkBZ,CAEA;AACA,KAAc,CAAAU,aAAaA,CAACC,MAAc,CAA0C,CAClF,GAAI,CAAC,IAAI,CAAC1C,iBAAiB,EAAI,CAAC,IAAI,CAACC,WAAW,CAAE,CAChD,KAAM,KAAI,CAACC,uBAAuB,CAAC,CAAC,CACpC,GAAI,CAAC,IAAI,CAACF,iBAAiB,EAAI,CAAC,IAAI,CAACC,WAAW,CAAE,CAChD,KAAM,IAAI,CAAA0C,KAAK,CAAC,kEAAkE,CAAC,CACrF,CACF,CAEA,KAAM,CAAAxC,UAAU,CAAG,GAAI,CAAAC,eAAe,CAAC,CAAC,CACxC,KAAM,CAAAC,SAAS,CAAGC,UAAU,CAAC,IAAMH,UAAU,CAACI,KAAK,CAAC,CAAC,CAAE,IAAI,CAACf,MAAM,CAACK,OAAO,CAAC,CAE3E,GAAI,CACF,KAAM,CAAAW,QAAQ,CAAG,KAAM,CAAAC,KAAK,IAAAC,MAAA,CAAI,IAAI,CAAClB,MAAM,CAACE,cAAc,kBAAiB,CACzEkD,MAAM,CAAE,MAAM,CACdC,OAAO,CAAE,CACP,cAAc,CAAE,kBAClB,CAAC,CACDC,IAAI,CAAEC,IAAI,CAACC,SAAS,CAAC,CACnB9B,KAAK,CAAE,IAAI,CAAC1B,MAAM,CAACC,SAAS,CAC5BiD,MAAM,CAAEA,MAAM,CACdO,MAAM,CAAE,KAAK,CACbC,OAAO,CAAE,CACPC,WAAW,CAAE,GAAG,CAAE;AAClBC,KAAK,CAAE,EAAE,CACTC,KAAK,CAAE,GAAG,CACVC,WAAW,CAAE,GACf,CACF,CAAC,CAAC,CACF3C,MAAM,CAAER,UAAU,CAACQ,MACrB,CAAC,CAAC,CAEFC,YAAY,CAACP,SAAS,CAAC,CAEvB,GAAI,CAACG,QAAQ,CAACK,EAAE,CAAE,CAChB,KAAM,IAAI,CAAA8B,KAAK,sBAAAjC,MAAA,CAAsBF,QAAQ,CAAC+C,MAAM,MAAA7C,MAAA,CAAIF,QAAQ,CAACgD,UAAU,CAAE,CAAC,CAChF,CAEA,KAAM,CAAA1C,IAAI,CAAG,KAAM,CAAAN,QAAQ,CAACO,IAAI,CAAC,CAAC,CAClC,KAAM,CAAA0C,YAAY,CAAG3C,IAAI,CAACN,QAAQ,CAElC;AACA,GAAI,CACF,KAAM,CAAAkD,SAAS,CAAGD,YAAY,CAACE,KAAK,CAAC,aAAa,CAAC,CACnD,GAAID,SAAS,CAAE,CACb,KAAM,CAAAE,MAAM,CAAGb,IAAI,CAACc,KAAK,CAACH,SAAS,CAAC,CAAC,CAAC,CAAC,CAEvC;AACA,GAAIE,MAAM,CAACE,UAAU,EAAI,MAAO,CAAAF,MAAM,CAACG,UAAU,GAAK,QAAQ,CAAE,CAC9D,MAAO,CACLD,UAAU,CAAEF,MAAM,CAACE,UAAU,CAC7BC,UAAU,CAAEC,IAAI,CAACC,GAAG,CAAC,CAAC,CAAED,IAAI,CAACE,GAAG,CAAC,CAAC,CAAEN,MAAM,CAACG,UAAU,CAAC,CAAC,CACvDI,SAAS,CAAEP,MAAM,CAACO,SAAS,EAAI,uBAAuB,CACtDC,qBAAqB,CAAER,MAAM,CAACQ,qBAAqB,EAAI,EACzD,CAAC,CACH,CACF,CACA,KAAM,IAAI,CAAAzB,KAAK,CAAC,uCAAuC,CAAC,CAC1D,CAAE,MAAO0B,UAAU,CAAE,CACnB/C,OAAO,CAACE,KAAK,CAAC,8BAA8B,CAAE6C,UAAU,CAAC,CACzD/C,OAAO,CAACE,KAAK,CAAC,eAAe,CAAEiC,YAAY,CAAC,CAC5C,MAAO,KAAI,CACb,CACF,CAAE,MAAOjC,KAAK,CAAE,CACdZ,YAAY,CAACP,SAAS,CAAC,CACvB,GAAImB,KAAK,WAAY,CAAAmB,KAAK,EAAInB,KAAK,CAACJ,IAAI,GAAK,YAAY,CAAE,CACzD,KAAM,IAAI,CAAAuB,KAAK,CAAC,qCAAqC,CAAC,CACxD,CACA,KAAM,CAAAnB,KAAK,CACb,CACF,CAEA;AACA,KAAM,CAAA8C,qBAAqBA,CAAC5C,WAAwB,CAA0C,CAC5F,GAAI,CACF,KAAM,CAAAC,UAAU,CAAGtC,qBAAqB,CAACkF,gBAAgB,CAAC,CAAC,CAC3D,GAAI5C,UAAU,CAAC6C,MAAM,GAAK,CAAC,CAAE,CAC3B,KAAM,IAAI,CAAA7B,KAAK,CAAC,+CAA+C,CAAC,CAClE,CAEA,KAAM,CAAAD,MAAM,CAAG,IAAI,CAACjB,4BAA4B,CAACC,WAAW,CAAEC,UAAU,CAAC,CACzE,KAAM,CAAAiC,MAAM,CAAG,KAAM,KAAI,CAACnB,aAAa,CAACC,MAAM,CAAC,CAE/C,GAAIkB,MAAM,CAAE,CACV;AACA,KAAM,CAAAa,cAAc,CAAG9C,UAAU,CAACV,IAAI,CAACgB,GAAG,EAAIA,GAAG,CAACE,EAAE,GAAKyB,MAAM,CAACE,UAAU,CAAC,CAC3E,GAAI,CAACW,cAAc,CAAE,CACnBnD,OAAO,CAACC,IAAI,2CAAAb,MAAA,CAA2CkD,MAAM,CAACE,UAAU,CAAE,CAAC,CAC3E;AACAF,MAAM,CAACE,UAAU,CAAG,mBAAmB,CACvCF,MAAM,CAACG,UAAU,CAAG,GAAG,CACvBH,MAAM,CAACO,SAAS,CAAG,gEAAgE,CACrF,CAEA;AACA,GAAIP,MAAM,CAACG,UAAU,EAAI,IAAI,CAACvE,MAAM,CAACG,mBAAmB,CAAE,CACxDN,qBAAqB,CAACiF,qBAAqB,CACzC5C,WAAW,CAACS,EAAE,CACdyB,MAAM,CAACE,UAAU,CACjB,IAAI,CACJF,MAAM,CAACG,UAAU,CACjBH,MAAM,CAACO,SACT,CAAC,CACH,CACF,CAEA,MAAO,CAAAP,MAAM,CACf,CAAE,MAAOpC,KAAK,CAAE,CACdF,OAAO,CAACE,KAAK,CAAC,2BAA2B,CAAEA,KAAK,CAAC,CACjD,MAAO,KAAI,CACb,CACF,CAEA;AACA,KAAM,CAAAkD,2BAA2BA,CAACC,YAA2B,CAIzD,CACF,KAAM,CAAAC,OAIJ,CAAG,EAAE,CAEP;AACA,IAAK,GAAI,CAAAC,CAAC,CAAG,CAAC,CAAEA,CAAC,CAAGF,YAAY,CAACH,MAAM,CAAEK,CAAC,EAAI,IAAI,CAACrF,MAAM,CAACM,SAAS,CAAE,CACnE,KAAM,CAAAgF,KAAK,CAAGH,YAAY,CAACI,KAAK,CAACF,CAAC,CAAEA,CAAC,CAAG,IAAI,CAACrF,MAAM,CAACM,SAAS,CAAC,CAE9D,KAAM,CAAAkF,aAAa,CAAGF,KAAK,CAAC9C,GAAG,CAAC,KAAO,CAAAN,WAAW,EAAK,CACrD,GAAI,CACF,KAAM,CAAAkC,MAAM,CAAG,KAAM,KAAI,CAACU,qBAAqB,CAAC5C,WAAW,CAAC,CAC5D,MAAO,CAAEA,WAAW,CAAEkC,MAAO,CAAC,CAChC,CAAE,MAAOpC,KAAK,CAAE,CACd,MAAO,CACLE,WAAW,CACXkC,MAAM,CAAE,IAAI,CACZpC,KAAK,CAAEA,KAAK,WAAY,CAAAmB,KAAK,CAAGnB,KAAK,CAACyD,OAAO,CAAG,eAClD,CAAC,CACH,CACF,CAAC,CAAC,CAEF,KAAM,CAAAC,YAAY,CAAG,KAAM,CAAAC,OAAO,CAACC,GAAG,CAACJ,aAAa,CAAC,CACrDJ,OAAO,CAACS,IAAI,CAAC,GAAGH,YAAY,CAAC,CAE7B;AACA,GAAIL,CAAC,CAAG,IAAI,CAACrF,MAAM,CAACM,SAAS,CAAG6E,YAAY,CAACH,MAAM,CAAE,CACnD,KAAM,IAAI,CAAAW,OAAO,CAACG,OAAO,EAAIhF,UAAU,CAACgF,OAAO,CAAE,IAAI,CAAC,CAAC,CACzD,CACF,CAEA,MAAO,CAAAV,OAAO,CAChB,CAEA;AACA,KAAM,CAAAW,mBAAmBA,CAAA,CAAiC,CACxD,GAAI,CACF;AACAlG,qBAAqB,CAACkF,gBAAgB,CAAC,CAAC,CAExC;AACA;AACA,KAAM,CAAAiB,YAA0B,CAAG,CACjCrD,EAAE,CAAE,WAAW,CAAGsD,IAAI,CAACC,GAAG,CAAC,CAAC,CAC5BtD,WAAW,CAAE,8CAA8C,CAC3DR,MAAM,CAAE,CAAC,CACTkC,UAAU,CAAE,UAAU,CACtB6B,QAAQ,CAAE,EAAE,CACZC,WAAW,CAAE,GAAI,CAAAH,IAAI,CAAC,CAAC,CAACI,WAAW,CAAC,CACtC,CAAC,CAED;AACAC,YAAY,CAACC,OAAO,CAAC,IAAI,CAACvG,MAAM,CAACO,gBAAgB,CAAGgD,IAAI,CAACC,SAAS,CAACwC,YAAY,CAAC,CAAC,CAEjF,MAAO,CAAAA,YAAY,CACrB,CAAE,MAAOhE,KAAK,CAAE,CACdF,OAAO,CAACE,KAAK,CAAC,kCAAkC,CAAEA,KAAK,CAAC,CACxD,MAAO,KAAI,CACb,CACF,CAEA;AACAwE,cAAcA,CAAA,CAKZ,CACA,MAAO,CACLC,WAAW,CAAE,IAAI,CAACjG,iBAAiB,CACnCC,WAAW,CAAE,IAAI,CAACA,WAAW,CAC7BT,MAAM,CAAE,IAAI,CAACA,MAAM,CACnB0G,SAAS,CAAE,GAAI,CAAAT,IAAI,CAAC,CAAC,CAACI,WAAW,CAAC,CACpC,CAAC,CACH,CAEA;AACAM,YAAYA,CAACC,OAAwC,CAAQ,CAC3D,IAAI,CAAC5G,MAAM,CAAA6G,aAAA,CAAAA,aAAA,IAAQ,IAAI,CAAC7G,MAAM,EAAK4G,OAAO,CAAE,CAE5C;AACA,GAAIA,OAAO,CAAC1G,cAAc,CAAE,CAC1B,IAAI,CAACQ,uBAAuB,CAAC,CAAC,CAChC,CACF,CAEA;AACA,KAAM,CAAAoG,kBAAkBA,CAAA,CAKrB,CACD,KAAM,CAAAC,SAAS,CAAGd,IAAI,CAACC,GAAG,CAAC,CAAC,CAE5B,GAAI,CACF,KAAM,CAAAc,eAA4B,CAAG,CACnCrE,EAAE,CAAE,kBAAkB,CACtBC,WAAW,CAAE,6BAA6B,CAC1CG,IAAI,CAAE,GAAI,CAAAkD,IAAI,CAAC,CAAC,CAACI,WAAW,CAAC,CAAC,CAC9BhE,WAAW,CAAE,OAAO,CACpBC,YAAY,CAAE,CAAC,CACf2E,OAAO,CAAE,QACX,CAAC,CAED,KAAM,CAAA7C,MAAM,CAAG,KAAM,KAAI,CAACU,qBAAqB,CAACkC,eAAe,CAAC,CAChE,KAAM,CAAAE,OAAO,CAAGjB,IAAI,CAACC,GAAG,CAAC,CAAC,CAAGa,SAAS,CAEtC,MAAO,CACLI,OAAO,CAAE,IAAI,CACb/C,MAAM,CAAEA,MAAM,EAAIgD,SAAS,CAC3BF,OACF,CAAC,CACH,CAAE,MAAOlF,KAAK,CAAE,CACd,MAAO,CACLmF,OAAO,CAAE,KAAK,CACdnF,KAAK,CAAEA,KAAK,WAAY,CAAAmB,KAAK,CAAGnB,KAAK,CAACyD,OAAO,CAAG,eAAe,CAC/DyB,OAAO,CAAEjB,IAAI,CAACC,GAAG,CAAC,CAAC,CAAGa,SACxB,CAAC,CACH,CACF,CACF,CAEA,MAAO,MAAM,CAAAM,uBAAuB,CAAG,GAAI,CAAAvH,uBAAuB,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}