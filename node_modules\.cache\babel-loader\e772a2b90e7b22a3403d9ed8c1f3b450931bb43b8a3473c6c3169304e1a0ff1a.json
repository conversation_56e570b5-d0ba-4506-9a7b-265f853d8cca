{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.transformArguments = exports.FIRST_KEY_INDEX = void 0;\nconst generic_transformers_1 = require(\"./generic-transformers\");\nexports.FIRST_KEY_INDEX = 1;\nfunction transformArguments(key, min, max) {\n  return ['ZREMRANGEBYSCORE', key, (0, generic_transformers_1.transformStringNumberInfinityArgument)(min), (0, generic_transformers_1.transformStringNumberInfinityArgument)(max)];\n}\nexports.transformArguments = transformArguments;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "transformArguments", "FIRST_KEY_INDEX", "generic_transformers_1", "require", "key", "min", "max", "transformStringNumberInfinityArgument"], "sources": ["C:/tmsft/node_modules/@redis/client/dist/lib/commands/ZREMRANGEBYSCORE.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.transformArguments = exports.FIRST_KEY_INDEX = void 0;\nconst generic_transformers_1 = require(\"./generic-transformers\");\nexports.FIRST_KEY_INDEX = 1;\nfunction transformArguments(key, min, max) {\n    return [\n        'ZREMRANGEBYSCORE',\n        key,\n        (0, generic_transformers_1.transformStringNumberInfinityArgument)(min),\n        (0, generic_transformers_1.transformStringNumberInfinityArgument)(max)\n    ];\n}\nexports.transformArguments = transformArguments;\n"], "mappings": "AAAA,YAAY;;AACZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEC,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7DD,OAAO,CAACE,kBAAkB,GAAGF,OAAO,CAACG,eAAe,GAAG,KAAK,CAAC;AAC7D,MAAMC,sBAAsB,GAAGC,OAAO,CAAC,wBAAwB,CAAC;AAChEL,OAAO,CAACG,eAAe,GAAG,CAAC;AAC3B,SAASD,kBAAkBA,CAACI,GAAG,EAAEC,GAAG,EAAEC,GAAG,EAAE;EACvC,OAAO,CACH,kBAAkB,EAClBF,GAAG,EACH,CAAC,CAAC,EAAEF,sBAAsB,CAACK,qCAAqC,EAAEF,GAAG,CAAC,EACtE,CAAC,CAAC,EAAEH,sBAAsB,CAACK,qCAAqC,EAAED,GAAG,CAAC,CACzE;AACL;AACAR,OAAO,CAACE,kBAAkB,GAAGA,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}