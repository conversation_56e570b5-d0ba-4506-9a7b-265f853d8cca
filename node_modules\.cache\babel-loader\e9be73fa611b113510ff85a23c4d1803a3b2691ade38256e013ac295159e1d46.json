{"ast": null, "code": "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { ENGINE } from '../engine';\nimport { Conv3D } from '../kernel_names';\nimport { convertToTensor } from '../tensor_util_env';\nimport * as util from '../util';\nimport { eitherStridesOrDilationsAreOne, stridesOrDilationsArePositive } from './conv_util';\nimport { op } from './operation';\nimport { reshape } from './reshape';\n/**\n * Computes a 3D convolution over the input x.\n *\n * @param x The input tensor, of rank 5 or rank 4, of shape\n *     `[batch, depth, height, width, channels]`. If rank 4,\n * batch of 1 is assumed.\n * @param filter The filter, rank 5, of shape\n *     `[filterDepth, filterHeight, filterWidth, inChannels, outChannels]`.\n *      inChannels must match between input and filter.\n * @param strides The strides of the convolution: `[strideDepth, strideHeight,\n * strideWidth]`.\n * @param pad The type of padding algorithm.\n *    - `same` and stride 1: output will be of same size as input,\n *       regardless of filter size.\n *    - `valid`: output will be smaller than input if filter is larger\n *       than 1x1.\n *   - For more info, see this guide:\n *     [https://www.tensorflow.org/api_docs/python/tf/nn/convolution](\n *          https://www.tensorflow.org/api_docs/python/tf/nn/convolution)\n * @param dataFormat: An optional string from: \"NDHWC\", \"NCDHW\". Defaults to\n *     \"NDHWC\". Specify the data format of the input and output data. With the\n *     default format \"NDHWC\", the data is stored in the order of: [batch,\n *     depth, height, width, channels]. Only \"NDHWC\" is currently supported.\n * @param dilations The dilation rates: `[dilationDepth, dilationHeight,\n *     dilationWidth]` in which we sample input values across the height\n *     and width dimensions in atrous convolution. Defaults to `[1, 1, 1]`.\n *     If `dilations` is a single number, then\n *     `dilationDepth == dilationHeight == dilationWidth`. If it is greater\n *     than 1, then all values of `strides` must be 1.\n *\n * @doc {heading: 'Operations', subheading: 'Convolution'}\n */\nfunction conv3d_(x, filter, strides, pad) {\n  let dataFormat = arguments.length > 4 && arguments[4] !== undefined ? arguments[4] : 'NDHWC';\n  let dilations = arguments.length > 5 && arguments[5] !== undefined ? arguments[5] : [1, 1, 1];\n  const $x = convertToTensor(x, 'x', 'conv3d');\n  const $filter = convertToTensor(filter, 'filter', 'conv3d');\n  let x5D = $x;\n  let reshapedTo5D = false;\n  if ($x.rank === 4) {\n    reshapedTo5D = true;\n    x5D = reshape($x, [1, $x.shape[0], $x.shape[1], $x.shape[2], $x.shape[3]]);\n  }\n  util.assert(x5D.rank === 5, () => \"Error in conv3d: input must be rank 5, but got rank \".concat(x5D.rank, \".\"));\n  util.assert($filter.rank === 5, () => \"Error in conv3d: filter must be rank 5, but got rank \" + \"\".concat($filter.rank, \".\"));\n  util.assert(x5D.shape[4] === $filter.shape[3], () => \"Error in conv3d: depth of input (\".concat(x5D.shape[4], \") must match \") + \"input depth for filter \".concat($filter.shape[3], \".\"));\n  util.assert(eitherStridesOrDilationsAreOne(strides, dilations), () => 'Error in conv3D: Either strides or dilations must be 1. ' + \"Got strides \".concat(strides, \" and dilations '\").concat(dilations, \"'\"));\n  util.assert(dataFormat === 'NDHWC', () => \"Error in conv3d: got dataFormat of \".concat(dataFormat, \" but only NDHWC is currently supported.\"));\n  util.assert(stridesOrDilationsArePositive(dilations), () => 'Error in conv3D: Dilated rates should be larger than 0.');\n  util.assert(stridesOrDilationsArePositive(strides), () => 'Error in conv3D: Strides should be larger than 0.');\n  const inputs = {\n    x: x5D,\n    filter: $filter\n  };\n  const attrs = {\n    strides,\n    pad,\n    dataFormat,\n    dilations\n  };\n  // tslint:disable-next-line: no-unnecessary-type-assertion\n  const res = ENGINE.runKernel(Conv3D, inputs, attrs);\n  if (reshapedTo5D) {\n    return reshape(res, [res.shape[1], res.shape[2], res.shape[3], res.shape[4]]);\n  }\n  return res;\n}\nexport const conv3d = /* @__PURE__ */op({\n  conv3d_\n});", "map": {"version": 3, "names": ["ENGINE", "Conv3D", "convertToTensor", "util", "eitherStridesOrDilationsAreOne", "stridesOrDilationsArePositive", "op", "reshape", "conv3d_", "x", "filter", "strides", "pad", "dataFormat", "arguments", "length", "undefined", "dilations", "$x", "$filter", "x5D", "reshapedTo5D", "rank", "shape", "assert", "concat", "inputs", "attrs", "res", "runKernel", "conv3d"], "sources": ["C:\\tfjs-core\\src\\ops\\conv3d.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport {ENGINE} from '../engine';\nimport {Conv3D, Conv3DAttrs, Conv3DInputs} from '../kernel_names';\nimport {NamedAttrMap} from '../kernel_registry';\nimport {Tensor4D, Tensor5D} from '../tensor';\nimport {NamedTensorMap} from '../tensor_types';\nimport {convertToTensor} from '../tensor_util_env';\nimport {TensorLike} from '../types';\nimport * as util from '../util';\n\nimport {eitherStridesOrDilationsAreOne, stridesOrDilationsArePositive} from './conv_util';\nimport {op} from './operation';\nimport {reshape} from './reshape';\n\n/**\n * Computes a 3D convolution over the input x.\n *\n * @param x The input tensor, of rank 5 or rank 4, of shape\n *     `[batch, depth, height, width, channels]`. If rank 4,\n * batch of 1 is assumed.\n * @param filter The filter, rank 5, of shape\n *     `[filterDepth, filterHeight, filterWidth, inChannels, outChannels]`.\n *      inChannels must match between input and filter.\n * @param strides The strides of the convolution: `[strideDepth, strideHeight,\n * strideWidth]`.\n * @param pad The type of padding algorithm.\n *    - `same` and stride 1: output will be of same size as input,\n *       regardless of filter size.\n *    - `valid`: output will be smaller than input if filter is larger\n *       than 1x1.\n *   - For more info, see this guide:\n *     [https://www.tensorflow.org/api_docs/python/tf/nn/convolution](\n *          https://www.tensorflow.org/api_docs/python/tf/nn/convolution)\n * @param dataFormat: An optional string from: \"NDHWC\", \"NCDHW\". Defaults to\n *     \"NDHWC\". Specify the data format of the input and output data. With the\n *     default format \"NDHWC\", the data is stored in the order of: [batch,\n *     depth, height, width, channels]. Only \"NDHWC\" is currently supported.\n * @param dilations The dilation rates: `[dilationDepth, dilationHeight,\n *     dilationWidth]` in which we sample input values across the height\n *     and width dimensions in atrous convolution. Defaults to `[1, 1, 1]`.\n *     If `dilations` is a single number, then\n *     `dilationDepth == dilationHeight == dilationWidth`. If it is greater\n *     than 1, then all values of `strides` must be 1.\n *\n * @doc {heading: 'Operations', subheading: 'Convolution'}\n */\nfunction conv3d_<T extends Tensor4D|Tensor5D>(\n    x: T|TensorLike, filter: Tensor5D|TensorLike,\n    strides: [number, number, number]|number, pad: 'valid'|'same',\n    dataFormat: 'NDHWC'|'NCDHW' = 'NDHWC',\n    dilations: [number, number, number]|number = [1, 1, 1]): T {\n  const $x = convertToTensor(x, 'x', 'conv3d');\n  const $filter = convertToTensor(filter, 'filter', 'conv3d');\n\n  let x5D = $x as Tensor5D;\n  let reshapedTo5D = false;\n\n  if ($x.rank === 4) {\n    reshapedTo5D = true;\n    x5D = reshape($x, [1, $x.shape[0], $x.shape[1], $x.shape[2], $x.shape[3]]);\n  }\n  util.assert(\n      x5D.rank === 5,\n      () => `Error in conv3d: input must be rank 5, but got rank ${x5D.rank}.`);\n  util.assert(\n      $filter.rank === 5,\n      () => `Error in conv3d: filter must be rank 5, but got rank ` +\n          `${$filter.rank}.`);\n  util.assert(\n      x5D.shape[4] === $filter.shape[3],\n      () => `Error in conv3d: depth of input (${x5D.shape[4]}) must match ` +\n          `input depth for filter ${$filter.shape[3]}.`);\n  util.assert(\n      eitherStridesOrDilationsAreOne(strides, dilations),\n      () => 'Error in conv3D: Either strides or dilations must be 1. ' +\n          `Got strides ${strides} and dilations '${dilations}'`);\n  util.assert(\n      dataFormat === 'NDHWC',\n      () => `Error in conv3d: got dataFormat of ${\n          dataFormat} but only NDHWC is currently supported.`);\n  util.assert(\n      stridesOrDilationsArePositive(dilations),\n      () => 'Error in conv3D: Dilated rates should be larger than 0.');\n  util.assert(\n      stridesOrDilationsArePositive(strides),\n      () => 'Error in conv3D: Strides should be larger than 0.');\n\n  const inputs: Conv3DInputs = {x: x5D, filter: $filter};\n\n  const attrs: Conv3DAttrs = {strides, pad, dataFormat, dilations};\n\n  // tslint:disable-next-line: no-unnecessary-type-assertion\n  const res = ENGINE.runKernel(\n                  Conv3D, inputs as unknown as NamedTensorMap,\n                  attrs as unknown as NamedAttrMap) as T;\n\n  if (reshapedTo5D) {\n    return reshape(\n               res, [res.shape[1], res.shape[2], res.shape[3], res.shape[4]]) as\n        T;\n  }\n  return res;\n}\n\nexport const conv3d = /* @__PURE__ */ op({conv3d_});\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAgBA,SAAQA,MAAM,QAAO,WAAW;AAChC,SAAQC,MAAM,QAAkC,iBAAiB;AAIjE,SAAQC,eAAe,QAAO,oBAAoB;AAElD,OAAO,KAAKC,IAAI,MAAM,SAAS;AAE/B,SAAQC,8BAA8B,EAAEC,6BAA6B,QAAO,aAAa;AACzF,SAAQC,EAAE,QAAO,aAAa;AAC9B,SAAQC,OAAO,QAAO,WAAW;AAEjC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgCA,SAASC,OAAOA,CACZC,CAAe,EAAEC,MAA2B,EAC5CC,OAAwC,EAAEC,GAAmB,EAEP;EAAA,IADtDC,UAAA,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAA8B,OAAO;EAAA,IACrCG,SAAA,GAAAH,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAA6C,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;EACxD,MAAMI,EAAE,GAAGhB,eAAe,CAACO,CAAC,EAAE,GAAG,EAAE,QAAQ,CAAC;EAC5C,MAAMU,OAAO,GAAGjB,eAAe,CAACQ,MAAM,EAAE,QAAQ,EAAE,QAAQ,CAAC;EAE3D,IAAIU,GAAG,GAAGF,EAAc;EACxB,IAAIG,YAAY,GAAG,KAAK;EAExB,IAAIH,EAAE,CAACI,IAAI,KAAK,CAAC,EAAE;IACjBD,YAAY,GAAG,IAAI;IACnBD,GAAG,GAAGb,OAAO,CAACW,EAAE,EAAE,CAAC,CAAC,EAAEA,EAAE,CAACK,KAAK,CAAC,CAAC,CAAC,EAAEL,EAAE,CAACK,KAAK,CAAC,CAAC,CAAC,EAAEL,EAAE,CAACK,KAAK,CAAC,CAAC,CAAC,EAAEL,EAAE,CAACK,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;;EAE5EpB,IAAI,CAACqB,MAAM,CACPJ,GAAG,CAACE,IAAI,KAAK,CAAC,EACd,6DAAAG,MAAA,CAA6DL,GAAG,CAACE,IAAI,MAAG,CAAC;EAC7EnB,IAAI,CAACqB,MAAM,CACPL,OAAO,CAACG,IAAI,KAAK,CAAC,EAClB,MAAM,6DAAAG,MAAA,CACCN,OAAO,CAACG,IAAI,MAAG,CAAC;EAC3BnB,IAAI,CAACqB,MAAM,CACPJ,GAAG,CAACG,KAAK,CAAC,CAAC,CAAC,KAAKJ,OAAO,CAACI,KAAK,CAAC,CAAC,CAAC,EACjC,MAAM,oCAAAE,MAAA,CAAoCL,GAAG,CAACG,KAAK,CAAC,CAAC,CAAC,+CAAAE,MAAA,CACxBN,OAAO,CAACI,KAAK,CAAC,CAAC,CAAC,MAAG,CAAC;EACtDpB,IAAI,CAACqB,MAAM,CACPpB,8BAA8B,CAACO,OAAO,EAAEM,SAAS,CAAC,EAClD,MAAM,0DAA0D,kBAAAQ,MAAA,CAC7Cd,OAAO,sBAAAc,MAAA,CAAmBR,SAAS,MAAG,CAAC;EAC9Dd,IAAI,CAACqB,MAAM,CACPX,UAAU,KAAK,OAAO,EACtB,4CAAAY,MAAA,CACIZ,UAAU,4CAAyC,CAAC;EAC5DV,IAAI,CAACqB,MAAM,CACPnB,6BAA6B,CAACY,SAAS,CAAC,EACxC,MAAM,yDAAyD,CAAC;EACpEd,IAAI,CAACqB,MAAM,CACPnB,6BAA6B,CAACM,OAAO,CAAC,EACtC,MAAM,mDAAmD,CAAC;EAE9D,MAAMe,MAAM,GAAiB;IAACjB,CAAC,EAAEW,GAAG;IAAEV,MAAM,EAAES;EAAO,CAAC;EAEtD,MAAMQ,KAAK,GAAgB;IAAChB,OAAO;IAAEC,GAAG;IAAEC,UAAU;IAAEI;EAAS,CAAC;EAEhE;EACA,MAAMW,GAAG,GAAG5B,MAAM,CAAC6B,SAAS,CACZ5B,MAAM,EAAEyB,MAAmC,EAC3CC,KAAgC,CAAM;EAEtD,IAAIN,YAAY,EAAE;IAChB,OAAOd,OAAO,CACHqB,GAAG,EAAE,CAACA,GAAG,CAACL,KAAK,CAAC,CAAC,CAAC,EAAEK,GAAG,CAACL,KAAK,CAAC,CAAC,CAAC,EAAEK,GAAG,CAACL,KAAK,CAAC,CAAC,CAAC,EAAEK,GAAG,CAACL,KAAK,CAAC,CAAC,CAAC,CAAC,CACnE;;EAEP,OAAOK,GAAG;AACZ;AAEA,OAAO,MAAME,MAAM,GAAG,eAAgBxB,EAAE,CAAC;EAACE;AAAO,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}