{"ast": null, "code": "/**\n * @license\n * Copyright 2018 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { ENGINE } from '../engine';\nimport { Cosh } from '../kernel_names';\nimport { convertToTensor } from '../tensor_util_env';\nimport { op } from './operation';\n/**\n * Computes hyperbolic cos of the input `tf.Tensor` element-wise: `cosh(x)`\n *\n * ```js\n * const x = tf.tensor1d([0, 1, -1, .7]);\n *\n * x.cosh().print();  // or tf.cosh(x)\n * ```\n * @param x The input tensor. Must be float32 type.\n *\n * @doc {heading: 'Operations', subheading: 'Basic math'}\n */\nfunction cosh_(x) {\n  const $x = convertToTensor(x, 'x', 'cosh', 'float32');\n  const inputs = {\n    x: $x\n  };\n  return ENGINE.runKernel(Cosh, inputs);\n}\nexport const cosh = /* @__PURE__ */op({\n  cosh_\n});", "map": {"version": 3, "names": ["ENGINE", "<PERSON><PERSON>", "convertToTensor", "op", "cosh_", "x", "$x", "inputs", "runKernel", "cosh"], "sources": ["C:\\tfjs-core\\src\\ops\\cosh.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2018 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {ENGINE} from '../engine';\nimport {Cosh, CoshInputs} from '../kernel_names';\nimport {Tensor} from '../tensor';\nimport {NamedTensorMap} from '../tensor_types';\nimport {convertToTensor} from '../tensor_util_env';\nimport {TensorLike} from '../types';\n\nimport {op} from './operation';\n\n/**\n * Computes hyperbolic cos of the input `tf.Tensor` element-wise: `cosh(x)`\n *\n * ```js\n * const x = tf.tensor1d([0, 1, -1, .7]);\n *\n * x.cosh().print();  // or tf.cosh(x)\n * ```\n * @param x The input tensor. Must be float32 type.\n *\n * @doc {heading: 'Operations', subheading: 'Basic math'}\n */\nfunction cosh_<T extends Tensor>(x: T|TensorLike): T {\n  const $x = convertToTensor(x, 'x', 'cosh', 'float32');\n  const inputs: CoshInputs = {x: $x};\n\n  return ENGINE.runKernel(Cosh, inputs as unknown as NamedTensorMap);\n}\nexport const cosh = /* @__PURE__ */ op({cosh_});\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAiBA,SAAQA,MAAM,QAAO,WAAW;AAChC,SAAQC,IAAI,QAAmB,iBAAiB;AAGhD,SAAQC,eAAe,QAAO,oBAAoB;AAGlD,SAAQC,EAAE,QAAO,aAAa;AAE9B;;;;;;;;;;;;AAYA,SAASC,KAAKA,CAAmBC,CAAe;EAC9C,MAAMC,EAAE,GAAGJ,eAAe,CAACG,CAAC,EAAE,GAAG,EAAE,MAAM,EAAE,SAAS,CAAC;EACrD,MAAME,MAAM,GAAe;IAACF,CAAC,EAAEC;EAAE,CAAC;EAElC,OAAON,MAAM,CAACQ,SAAS,CAACP,IAAI,EAAEM,MAAmC,CAAC;AACpE;AACA,OAAO,MAAME,IAAI,GAAG,eAAgBN,EAAE,CAAC;EAACC;AAAK,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}