{"ast": null, "code": "/*\nClassifier class that provides functionality for training and\nclassification\nCopyright (C) 2017, 2023 Hugo <PERSON>t\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in\nall copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\nTHE SOFTWARE.\n*/\n\n'use strict';\n\nconst fs = require('fs');\nconst Context = require('./Context');\nconst Element = require('./Element');\nconst Sample = require('./Sample');\nconst Scaler = require('./GISScaler');\nconst FeatureSet = require('./FeatureSet');\nconst DEBUG = false;\nclass Classifier {\n  constructor(features, sample) {\n    if (features) {\n      this.features = features;\n    } else {\n      this.features = new FeatureSet();\n    }\n    this.features = features;\n    if (sample) {\n      this.sample = sample;\n    } else {\n      this.sample = new Sample();\n    }\n  }\n\n  // Loads a classifier from file.\n  // Caveat: feature functions are generated from the sample elements. You need\n  // to create your own specialisation of the Element class that can generate\n  // your own specific feature functions\n  load(filename, ElementClass, callback) {\n    fs.readFile(filename, 'utf8', function (err, data) {\n      if (!err) {\n        const classifierData = JSON.parse(data);\n        const sample = new Sample();\n        classifierData.sample.elements.forEach(function (elementData) {\n          const elt = new ElementClass(elementData.a, new Context(elementData.b.data));\n          sample.addElement(elt);\n        });\n        const featureSet = new FeatureSet();\n        sample.generateFeatures(featureSet);\n        const classifier = new Classifier(featureSet, sample);\n        callback(err, classifier);\n      } else {\n        if (callback) {\n          callback(err);\n        }\n      }\n    });\n  }\n  save(filename, callback) {\n    const data = JSON.stringify(this, null, 2);\n    const classifier = this;\n    fs.writeFile(filename, data, 'utf8', function (err) {\n      if (callback) {\n        DEBUG && console.log('Saved classifier to ' + filename);\n        callback(err, err ? null : classifier);\n      }\n    });\n  }\n  addElement(x) {\n    this.sample.addElement(x);\n  }\n  addDocument(context, classification, ElementClass) {\n    Classifier.prototype.addElement(new ElementClass(classification, context));\n  }\n  train(maxIterations, minImprovement) {\n    this.scaler = new Scaler(this.features, this.sample);\n    this.p = this.scaler.run(maxIterations, minImprovement);\n  }\n  getClassifications(b) {\n    const scores = [];\n    const that = this;\n    this.sample.getClasses().forEach(function (a) {\n      const x = new Element(a, b);\n      scores.push({\n        label: a,\n        value: that.p.calculateAPriori(x)\n      });\n    });\n    return scores;\n  }\n  classify(b) {\n    const scores = this.getClassifications(b);\n    // Sort the scores in an array\n    scores.sort(function (a, b) {\n      return b.value - a.value;\n    });\n    // Check if the classifier discriminates\n    const min = scores[scores.length - 1].value;\n    const max = scores[0].value;\n    if (min === max) {\n      return '';\n    } else {\n      // Return the highest scoring classes\n      return scores[0].label;\n    }\n  }\n}\nmodule.exports = Classifier;", "map": {"version": 3, "names": ["fs", "require", "Context", "Element", "<PERSON><PERSON>", "Scaler", "FeatureSet", "DEBUG", "Classifier", "constructor", "features", "sample", "load", "filename", "ElementClass", "callback", "readFile", "err", "data", "classifierData", "JSON", "parse", "elements", "for<PERSON>ach", "elementData", "elt", "a", "b", "addElement", "featureSet", "generateFeatures", "classifier", "save", "stringify", "writeFile", "console", "log", "x", "addDocument", "context", "classification", "prototype", "train", "maxIterations", "minImprovement", "scaler", "p", "run", "getClassifications", "scores", "that", "getClasses", "push", "label", "value", "calculateAPriori", "classify", "sort", "min", "length", "max", "module", "exports"], "sources": ["C:/tmsft/node_modules/natural/lib/natural/classifiers/maxent/Classifier.js"], "sourcesContent": ["/*\nClassifier class that provides functionality for training and\nclassification\nCopyright (C) 2017, 2023 Hugo <PERSON>t\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in\nall copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\nTHE SOFTWARE.\n*/\n\n'use strict'\n\nconst fs = require('fs')\n\nconst Context = require('./Context')\nconst Element = require('./Element')\nconst Sample = require('./Sample')\nconst Scaler = require('./GISScaler')\nconst FeatureSet = require('./FeatureSet')\n\nconst DEBUG = false\n\nclass Classifier {\n  constructor (features, sample) {\n    if (features) {\n      this.features = features\n    } else {\n      this.features = new FeatureSet()\n    }\n    this.features = features\n    if (sample) {\n      this.sample = sample\n    } else {\n      this.sample = new Sample()\n    }\n  }\n\n  // Loads a classifier from file.\n  // Caveat: feature functions are generated from the sample elements. You need\n  // to create your own specialisation of the Element class that can generate\n  // your own specific feature functions\n  load (filename, ElementClass, callback) {\n    fs.readFile(filename, 'utf8', function (err, data) {\n      if (!err) {\n        const classifierData = JSON.parse(data)\n        const sample = new Sample()\n        classifierData.sample.elements.forEach(function (elementData) {\n          const elt = new ElementClass(elementData.a, new Context(elementData.b.data))\n          sample.addElement(elt)\n        })\n        const featureSet = new FeatureSet()\n        sample.generateFeatures(featureSet)\n        const classifier = new Classifier(featureSet, sample)\n        callback(err, classifier)\n      } else {\n        if (callback) {\n          callback(err)\n        }\n      }\n    })\n  }\n\n  save (filename, callback) {\n    const data = JSON.stringify(this, null, 2)\n    const classifier = this\n    fs.writeFile(filename, data, 'utf8', function (err) {\n      if (callback) {\n        DEBUG && console.log('Saved classifier to ' + filename)\n        callback(err, err ? null : classifier)\n      }\n    })\n  }\n\n  addElement (x) {\n    this.sample.addElement(x)\n  }\n\n  addDocument (context, classification, ElementClass) {\n    Classifier.prototype.addElement(new ElementClass(classification, context))\n  }\n\n  train (maxIterations, minImprovement) {\n    this.scaler = new Scaler(this.features, this.sample)\n    this.p = this.scaler.run(maxIterations, minImprovement)\n  }\n\n  getClassifications (b) {\n    const scores = []\n    const that = this\n    this.sample.getClasses().forEach(function (a) {\n      const x = new Element(a, b)\n      scores.push({\n        label: a,\n        value: that.p.calculateAPriori(x)\n      })\n    })\n    return scores\n  }\n\n  classify (b) {\n    const scores = this.getClassifications(b)\n    // Sort the scores in an array\n    scores.sort(function (a, b) {\n      return b.value - a.value\n    })\n    // Check if the classifier discriminates\n    const min = scores[scores.length - 1].value\n    const max = scores[0].value\n    if (min === max) {\n      return ''\n    } else {\n      // Return the highest scoring classes\n      return scores[0].label\n    }\n  }\n}\n\nmodule.exports = Classifier\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,YAAY;;AAEZ,MAAMA,EAAE,GAAGC,OAAO,CAAC,IAAI,CAAC;AAExB,MAAMC,OAAO,GAAGD,OAAO,CAAC,WAAW,CAAC;AACpC,MAAME,OAAO,GAAGF,OAAO,CAAC,WAAW,CAAC;AACpC,MAAMG,MAAM,GAAGH,OAAO,CAAC,UAAU,CAAC;AAClC,MAAMI,MAAM,GAAGJ,OAAO,CAAC,aAAa,CAAC;AACrC,MAAMK,UAAU,GAAGL,OAAO,CAAC,cAAc,CAAC;AAE1C,MAAMM,KAAK,GAAG,KAAK;AAEnB,MAAMC,UAAU,CAAC;EACfC,WAAWA,CAAEC,QAAQ,EAAEC,MAAM,EAAE;IAC7B,IAAID,QAAQ,EAAE;MACZ,IAAI,CAACA,QAAQ,GAAGA,QAAQ;IAC1B,CAAC,MAAM;MACL,IAAI,CAACA,QAAQ,GAAG,IAAIJ,UAAU,CAAC,CAAC;IAClC;IACA,IAAI,CAACI,QAAQ,GAAGA,QAAQ;IACxB,IAAIC,MAAM,EAAE;MACV,IAAI,CAACA,MAAM,GAAGA,MAAM;IACtB,CAAC,MAAM;MACL,IAAI,CAACA,MAAM,GAAG,IAAIP,MAAM,CAAC,CAAC;IAC5B;EACF;;EAEA;EACA;EACA;EACA;EACAQ,IAAIA,CAAEC,QAAQ,EAAEC,YAAY,EAAEC,QAAQ,EAAE;IACtCf,EAAE,CAACgB,QAAQ,CAACH,QAAQ,EAAE,MAAM,EAAE,UAAUI,GAAG,EAAEC,IAAI,EAAE;MACjD,IAAI,CAACD,GAAG,EAAE;QACR,MAAME,cAAc,GAAGC,IAAI,CAACC,KAAK,CAACH,IAAI,CAAC;QACvC,MAAMP,MAAM,GAAG,IAAIP,MAAM,CAAC,CAAC;QAC3Be,cAAc,CAACR,MAAM,CAACW,QAAQ,CAACC,OAAO,CAAC,UAAUC,WAAW,EAAE;UAC5D,MAAMC,GAAG,GAAG,IAAIX,YAAY,CAACU,WAAW,CAACE,CAAC,EAAE,IAAIxB,OAAO,CAACsB,WAAW,CAACG,CAAC,CAACT,IAAI,CAAC,CAAC;UAC5EP,MAAM,CAACiB,UAAU,CAACH,GAAG,CAAC;QACxB,CAAC,CAAC;QACF,MAAMI,UAAU,GAAG,IAAIvB,UAAU,CAAC,CAAC;QACnCK,MAAM,CAACmB,gBAAgB,CAACD,UAAU,CAAC;QACnC,MAAME,UAAU,GAAG,IAAIvB,UAAU,CAACqB,UAAU,EAAElB,MAAM,CAAC;QACrDI,QAAQ,CAACE,GAAG,EAAEc,UAAU,CAAC;MAC3B,CAAC,MAAM;QACL,IAAIhB,QAAQ,EAAE;UACZA,QAAQ,CAACE,GAAG,CAAC;QACf;MACF;IACF,CAAC,CAAC;EACJ;EAEAe,IAAIA,CAAEnB,QAAQ,EAAEE,QAAQ,EAAE;IACxB,MAAMG,IAAI,GAAGE,IAAI,CAACa,SAAS,CAAC,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC;IAC1C,MAAMF,UAAU,GAAG,IAAI;IACvB/B,EAAE,CAACkC,SAAS,CAACrB,QAAQ,EAAEK,IAAI,EAAE,MAAM,EAAE,UAAUD,GAAG,EAAE;MAClD,IAAIF,QAAQ,EAAE;QACZR,KAAK,IAAI4B,OAAO,CAACC,GAAG,CAAC,sBAAsB,GAAGvB,QAAQ,CAAC;QACvDE,QAAQ,CAACE,GAAG,EAAEA,GAAG,GAAG,IAAI,GAAGc,UAAU,CAAC;MACxC;IACF,CAAC,CAAC;EACJ;EAEAH,UAAUA,CAAES,CAAC,EAAE;IACb,IAAI,CAAC1B,MAAM,CAACiB,UAAU,CAACS,CAAC,CAAC;EAC3B;EAEAC,WAAWA,CAAEC,OAAO,EAAEC,cAAc,EAAE1B,YAAY,EAAE;IAClDN,UAAU,CAACiC,SAAS,CAACb,UAAU,CAAC,IAAId,YAAY,CAAC0B,cAAc,EAAED,OAAO,CAAC,CAAC;EAC5E;EAEAG,KAAKA,CAAEC,aAAa,EAAEC,cAAc,EAAE;IACpC,IAAI,CAACC,MAAM,GAAG,IAAIxC,MAAM,CAAC,IAAI,CAACK,QAAQ,EAAE,IAAI,CAACC,MAAM,CAAC;IACpD,IAAI,CAACmC,CAAC,GAAG,IAAI,CAACD,MAAM,CAACE,GAAG,CAACJ,aAAa,EAAEC,cAAc,CAAC;EACzD;EAEAI,kBAAkBA,CAAErB,CAAC,EAAE;IACrB,MAAMsB,MAAM,GAAG,EAAE;IACjB,MAAMC,IAAI,GAAG,IAAI;IACjB,IAAI,CAACvC,MAAM,CAACwC,UAAU,CAAC,CAAC,CAAC5B,OAAO,CAAC,UAAUG,CAAC,EAAE;MAC5C,MAAMW,CAAC,GAAG,IAAIlC,OAAO,CAACuB,CAAC,EAAEC,CAAC,CAAC;MAC3BsB,MAAM,CAACG,IAAI,CAAC;QACVC,KAAK,EAAE3B,CAAC;QACR4B,KAAK,EAAEJ,IAAI,CAACJ,CAAC,CAACS,gBAAgB,CAAClB,CAAC;MAClC,CAAC,CAAC;IACJ,CAAC,CAAC;IACF,OAAOY,MAAM;EACf;EAEAO,QAAQA,CAAE7B,CAAC,EAAE;IACX,MAAMsB,MAAM,GAAG,IAAI,CAACD,kBAAkB,CAACrB,CAAC,CAAC;IACzC;IACAsB,MAAM,CAACQ,IAAI,CAAC,UAAU/B,CAAC,EAAEC,CAAC,EAAE;MAC1B,OAAOA,CAAC,CAAC2B,KAAK,GAAG5B,CAAC,CAAC4B,KAAK;IAC1B,CAAC,CAAC;IACF;IACA,MAAMI,GAAG,GAAGT,MAAM,CAACA,MAAM,CAACU,MAAM,GAAG,CAAC,CAAC,CAACL,KAAK;IAC3C,MAAMM,GAAG,GAAGX,MAAM,CAAC,CAAC,CAAC,CAACK,KAAK;IAC3B,IAAII,GAAG,KAAKE,GAAG,EAAE;MACf,OAAO,EAAE;IACX,CAAC,MAAM;MACL;MACA,OAAOX,MAAM,CAAC,CAAC,CAAC,CAACI,KAAK;IACxB;EACF;AACF;AAEAQ,MAAM,CAACC,OAAO,GAAGtD,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}