{"ast": null, "code": "/**\n * @license\n * Copyright 2020 Google Inc. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { TensorBuffer } from '../tensor';\nimport * as util from '../util';\n/**\n * Creates an empty `tf.TensorBuffer` with the specified `shape` and `dtype`.\n *\n * The values are stored in CPU as `TypedArray`. Fill the buffer using\n * `buffer.set()`, or by modifying directly `buffer.values`.\n *\n * When done, call `buffer.toTensor()` to get an immutable `tf.Tensor` with\n * those values.\n *\n * ```js\n * // Create a buffer and set values at particular indices.\n * const buffer = tf.buffer([2, 2]);\n * buffer.set(3, 0, 0);\n * buffer.set(5, 1, 0);\n *\n * // Convert the buffer back to a tensor.\n * buffer.toTensor().print();\n * ```\n *\n * @param shape An array of integers defining the output tensor shape.\n * @param dtype The dtype of the buffer. Defaults to 'float32'.\n * @param values The values of the buffer as `TypedArray`. Defaults to\n * zeros.\n *\n * @doc {heading: 'Tensors', subheading: 'Creation'}\n */\nexport function buffer(shape) {\n  let dtype = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 'float32';\n  let values = arguments.length > 2 ? arguments[2] : undefined;\n  dtype = dtype || 'float32';\n  util.assertNonNegativeIntegerDimensions(shape);\n  return new TensorBuffer(shape, dtype, values);\n}", "map": {"version": 3, "names": ["Tensor<PERSON><PERSON><PERSON>", "util", "buffer", "shape", "dtype", "arguments", "length", "undefined", "values", "assertNonNegativeIntegerDimensions"], "sources": ["C:\\tfjs-core\\src\\ops\\buffer.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google Inc. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {TensorBuffer} from '../tensor';\nimport {DataType, DataTypeMap, Rank, ShapeMap} from '../types';\nimport * as util from '../util';\n\n/**\n * Creates an empty `tf.TensorBuffer` with the specified `shape` and `dtype`.\n *\n * The values are stored in CPU as `TypedArray`. Fill the buffer using\n * `buffer.set()`, or by modifying directly `buffer.values`.\n *\n * When done, call `buffer.toTensor()` to get an immutable `tf.Tensor` with\n * those values.\n *\n * ```js\n * // Create a buffer and set values at particular indices.\n * const buffer = tf.buffer([2, 2]);\n * buffer.set(3, 0, 0);\n * buffer.set(5, 1, 0);\n *\n * // Convert the buffer back to a tensor.\n * buffer.toTensor().print();\n * ```\n *\n * @param shape An array of integers defining the output tensor shape.\n * @param dtype The dtype of the buffer. Defaults to 'float32'.\n * @param values The values of the buffer as `TypedArray`. Defaults to\n * zeros.\n *\n * @doc {heading: 'Tensors', subheading: 'Creation'}\n */\nexport function buffer<R extends Rank, D extends DataType = 'float32'>(\n    shape: ShapeMap[R], dtype: D = 'float32' as D,\n    values?: DataTypeMap[D]): TensorBuffer<R, D> {\n  dtype = dtype || 'float32' as D;\n  util.assertNonNegativeIntegerDimensions(shape);\n  return new TensorBuffer<R, D>(shape, dtype, values);\n}\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAiBA,SAAQA,YAAY,QAAO,WAAW;AAEtC,OAAO,KAAKC,IAAI,MAAM,SAAS;AAE/B;;;;;;;;;;;;;;;;;;;;;;;;;;AA0BA,OAAM,SAAUC,MAAMA,CAClBC,KAAkB,EACK;EAAA,IADHC,KAAA,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAW,SAAc;EAAA,IAC7CG,MAAuB,GAAAH,SAAA,CAAAC,MAAA,OAAAD,SAAA,MAAAE,SAAA;EACzBH,KAAK,GAAGA,KAAK,IAAI,SAAc;EAC/BH,IAAI,CAACQ,kCAAkC,CAACN,KAAK,CAAC;EAC9C,OAAO,IAAIH,YAAY,CAAOG,KAAK,EAAEC,KAAK,EAAEI,MAAM,CAAC;AACrD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}