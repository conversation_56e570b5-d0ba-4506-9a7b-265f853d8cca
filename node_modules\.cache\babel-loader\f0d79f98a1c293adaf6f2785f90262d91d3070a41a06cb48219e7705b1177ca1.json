{"ast": null, "code": "/**\n * @license\n * Copyright 2018 Google LLC\n *\n * Use of this source code is governed by an MIT-style\n * license that can be found in the LICENSE file or at\n * https://opensource.org/licenses/MIT.\n * =============================================================================\n */\n/* Original source: keras/engine/topology.py */\nimport { serialization, tidy, util } from '@tensorflow/tfjs-core';\nimport { getNextUniqueTensorId, getUid } from '../backend/state';\nimport { getScopedTensorName, getUniqueTensorName, nameScope } from '../common';\nimport { AttributeError, NotImplementedError, RuntimeError, ValueError } from '../errors';\nimport { getInitializer } from '../initializers';\nimport * as generic_utils from '../utils/generic_utils';\nimport * as types_utils from '../utils/types_utils';\nimport * as variable_utils from '../utils/variable_utils';\nimport { batchGetValue, batchSetValue, LayerVariable } from '../variables';\n/**\n * Specifies the ndim, dtype and shape of every input to a layer.\n *\n * Every layer should expose (if appropriate) an `inputSpec` attribute:\n * a list of instances of InputSpec (one per input tensor).\n *\n * A null entry in a shape is compatible with any dimension,\n * a null shape is compatible with any shape.\n */\nexport class InputSpec {\n  constructor(args) {\n    this.dtype = args.dtype;\n    this.shape = args.shape;\n    /*\n      TODO(michaelterry): Could throw error if ndim and shape are both defined\n        (then backport).\n    */\n    if (args.shape != null) {\n      this.ndim = args.shape.length;\n    } else {\n      this.ndim = args.ndim;\n    }\n    this.maxNDim = args.maxNDim;\n    this.minNDim = args.minNDim;\n    this.axes = args.axes || {};\n  }\n}\n/**\n * `tf.SymbolicTensor` is a placeholder for a Tensor without any concrete value.\n *\n * They are most often encountered when building a graph of `Layer`s for a\n * `tf.LayersModel` and the input data's shape, but not values are known.\n *\n * @doc {heading: 'Models', 'subheading': 'Classes'}\n */\nexport class SymbolicTensor {\n  /**\n   *\n   * @param dtype\n   * @param shape\n   * @param sourceLayer The Layer that produced this symbolic tensor.\n   * @param inputs The inputs passed to sourceLayer's __call__() method.\n   * @param nodeIndex\n   * @param tensorIndex\n   * @param callArgs The keyword arguments passed to the __call__() method.\n   * @param name\n   * @param outputTensorIndex The index of this tensor in the list of outputs\n   *   returned by apply().\n   */\n  constructor(dtype, shape, sourceLayer, inputs, callArgs, name, outputTensorIndex) {\n    this.dtype = dtype;\n    this.shape = shape;\n    this.sourceLayer = sourceLayer;\n    this.inputs = inputs;\n    this.callArgs = callArgs;\n    this.outputTensorIndex = outputTensorIndex;\n    this.id = getNextUniqueTensorId();\n    if (name != null) {\n      this.originalName = getScopedTensorName(name);\n      this.name = getUniqueTensorName(this.originalName);\n    }\n    this.rank = shape.length;\n  }\n}\nlet _nextNodeID = 0;\n/**\n * A `Node` describes the connectivity between two layers.\n *\n * Each time a layer is connected to some new input,\n * a node is added to `layer.inboundNodes`.\n *\n * Each time the output of a layer is used by another layer,\n * a node is added to `layer.outboundNodes`.\n *\n * `nodeIndices` and `tensorIndices` are basically fine-grained coordinates\n * describing the origin of the `inputTensors`, verifying the following:\n *\n * `inputTensors[i] ==\n * inboundLayers[i].inboundNodes[nodeIndices[i]].outputTensors[\n *   tensorIndices[i]]`\n *\n * A node from layer A to layer B is added to:\n *     A.outboundNodes\n *     B.inboundNodes\n */\nexport class Node {\n  constructor(args,\n  // TODO(michaelterry): Define actual type for this.\n  callArgs) {\n    this.callArgs = callArgs;\n    this.id = _nextNodeID++;\n    /*\n      Layer instance (NOT a list).\n      this is the layer that takes a list of input tensors\n      and turns them into a list of output tensors.\n      the current node will be added to\n      the inboundNodes of outboundLayer.\n    */\n    this.outboundLayer = args.outboundLayer;\n    /*\n        The following 3 properties describe where\n        the input tensors come from: which layers,\n        and for each layer, which node and which\n        tensor output of each node.\n    */\n    // List of layer instances.\n    this.inboundLayers = args.inboundLayers;\n    // List of integers, 1:1 mapping with inboundLayers.\n    this.nodeIndices = args.nodeIndices;\n    // List of integers, 1:1 mapping with inboundLayers.\n    this.tensorIndices = args.tensorIndices;\n    /*\n        Following 2 properties:\n        tensor inputs and outputs of outboundLayer.\n    */\n    // List of tensors. 1:1 mapping with inboundLayers.\n    this.inputTensors = args.inputTensors;\n    // List of tensors, created by outboundLayer.call().\n    this.outputTensors = args.outputTensors;\n    /*\n        Following 2 properties: input and output masks.\n        List of tensors, 1:1 mapping with inputTensor.\n    */\n    this.inputMasks = args.inputMasks;\n    // List of tensors, created by outboundLayer.computeMask().\n    this.outputMasks = args.outputMasks;\n    // Following 2 properties: input and output shapes.\n    // List of shape tuples, shapes of inputTensors.\n    this.inputShapes = args.inputShapes;\n    // List of shape tuples, shapes of outputTensors.\n    this.outputShapes = args.outputShapes;\n    // Add nodes to all layers involved.\n    for (const layer of args.inboundLayers) {\n      if (layer != null) {\n        layer.outboundNodes.push(this);\n      }\n    }\n    args.outboundLayer.inboundNodes.push(this);\n  }\n  getConfig() {\n    const inboundNames = [];\n    for (const layer of this.inboundLayers) {\n      if (layer != null) {\n        inboundNames.push(layer.name);\n      } else {\n        inboundNames.push(null);\n      }\n    }\n    return {\n      outboundLayer: this.outboundLayer ? this.outboundLayer.name : null,\n      inboundLayers: inboundNames,\n      nodeIndices: this.nodeIndices,\n      tensorIndices: this.tensorIndices\n    };\n  }\n}\nlet _nextLayerID = 0;\n/**\n * A layer is a grouping of operations and weights that can be composed to\n * create a `tf.LayersModel`.\n *\n * Layers are constructed by using the functions under the\n * [tf.layers](#Layers-Basic) namespace.\n *\n * @doc {heading: 'Layers', subheading: 'Classes', namespace: 'layers'}\n */\nexport class Layer extends serialization.Serializable {\n  constructor(args = {}) {\n    super();\n    this._callHook = null;\n    this._addedWeightNames = [];\n    // Porting Notes: PyKeras does not have this property in this base Layer\n    //   class. Instead lets Layer subclass set it dynamically and checks the\n    //   value with `hasattr`. In tfjs-layers, we let this be a member of this\n    //   base class.\n    this._stateful = false;\n    this.id = _nextLayerID++;\n    this.activityRegularizer = null;\n    this.inputSpec = null;\n    this.supportsMasking = false;\n    // These properties will be set upon call of this.build()\n    this._trainableWeights = [];\n    this._nonTrainableWeights = [];\n    this._losses = [];\n    this._updates = [];\n    this._built = false;\n    /*\n      These lists will be filled via successive calls\n      to this.addInboundNode().\n     */\n    this.inboundNodes = [];\n    this.outboundNodes = [];\n    let name = args.name;\n    if (!name) {\n      const prefix = this.getClassName();\n      name = generic_utils.toSnakeCase(prefix) + '_' + getUid(prefix);\n    }\n    this.name = name;\n    this.trainable_ = args.trainable == null ? true : args.trainable;\n    if (args.inputShape != null || args.batchInputShape != null) {\n      /*\n        In this case we will later create an input layer\n        to insert before the current layer\n       */\n      let batchInputShape;\n      if (args.batchInputShape != null) {\n        batchInputShape = args.batchInputShape;\n      } else if (args.inputShape != null) {\n        let batchSize = null;\n        if (args.batchSize != null) {\n          batchSize = args.batchSize;\n        }\n        batchInputShape = [batchSize].concat(args.inputShape);\n      }\n      this.batchInputShape = batchInputShape;\n      // Set dtype.\n      let dtype = args.dtype;\n      if (dtype == null) {\n        dtype = args.inputDType;\n      }\n      if (dtype == null) {\n        dtype = 'float32';\n      }\n      this.dtype = dtype;\n    }\n    if (args.weights != null) {\n      this.initialWeights = args.weights;\n    } else {\n      this.initialWeights = null;\n    }\n    // The value of `_refCount` is initialized to null. When the layer is used\n    // in a symbolic way for the first time, it will be set to 1.\n    this._refCount = null;\n    this.fastWeightInitDuringBuild = false;\n  }\n  /**\n   * Converts a layer and its index to a unique (immutable type) name.\n   * This function is used internally with `this.containerNodes`.\n   * @param layer The layer.\n   * @param nodeIndex The layer's position (e.g. via enumerate) in a list of\n   *   nodes.\n   *\n   * @returns The unique name.\n   */\n  static nodeKey(layer, nodeIndex) {\n    return layer.name + '_ib-' + nodeIndex.toString();\n  }\n  /**\n   * Returns this.inboundNode at index nodeIndex.\n   *\n   * Porting note: This is a replacement for _get_node_attribute_at_index()\n   * @param nodeIndex\n   * @param attrName The name of the attribute related to request for this node.\n   */\n  getNodeAtIndex(nodeIndex, attrName) {\n    if (this.inboundNodes.length === 0) {\n      throw new RuntimeError('The layer has never been called ' + `and thus has no defined ${attrName}.`);\n    }\n    if (this.inboundNodes.length <= nodeIndex) {\n      throw new ValueError(`Asked to get ${attrName} at node ${nodeIndex}, ` + `but the layer has only ${this.inboundNodes.length} inbound nodes.`);\n    }\n    return this.inboundNodes[nodeIndex];\n  }\n  /**\n   * Retrieves the input tensor(s) of a layer at a given node.\n   *\n   * @param nodeIndex Integer, index of the node from which to retrieve the\n   *   attribute. E.g. `nodeIndex=0` will correspond to the first time the layer\n   *   was called.\n   *\n   * @return A tensor (or list of tensors if the layer has multiple inputs).\n   */\n  getInputAt(nodeIndex) {\n    return generic_utils.singletonOrArray(this.getNodeAtIndex(nodeIndex, 'input').inputTensors);\n  }\n  /**\n   * Retrieves the output tensor(s) of a layer at a given node.\n   *\n   * @param nodeIndex Integer, index of the node from which to retrieve the\n   *   attribute. E.g. `nodeIndex=0` will correspond to the first time the layer\n   *   was called.\n   *\n   * @return A tensor (or list of tensors if the layer has multiple outputs).\n   */\n  getOutputAt(nodeIndex) {\n    return generic_utils.singletonOrArray(this.getNodeAtIndex(nodeIndex, 'output').outputTensors);\n  }\n  // Properties\n  /**\n   * Retrieves the input tensor(s) of a layer.\n   *\n   * Only applicable if the layer has exactly one inbound node,\n   * i.e. if it is connected to one incoming layer.\n   *\n   * @return Input tensor or list of input tensors.\n   *\n   * @exception AttributeError if the layer is connected to more than one\n   *   incoming layers.\n   */\n  get input() {\n    if (this.inboundNodes.length > 1) {\n      throw new AttributeError(`Layer ${this.name}` + ' has multiple inbound nodes, ' + 'hence the notion of \"layer input\" ' + 'is ill-defined. ' + 'Use `getInputAt(nodeIndex)` instead.');\n    } else if (this.inboundNodes.length === 0) {\n      throw new AttributeError(`Layer ${this.name}` + ' is not connected, no input to return.');\n    }\n    return generic_utils.singletonOrArray(this.getNodeAtIndex(0, 'input').inputTensors);\n  }\n  /**\n   * Retrieves the output tensor(s) of a layer.\n   *\n   * Only applicable if the layer has exactly one inbound node,\n   * i.e. if it is connected to one incoming layer.\n   *\n   * @return Output tensor or list of output tensors.\n   *\n   * @exception AttributeError if the layer is connected to more than one\n   *   incoming layers.\n   */\n  get output() {\n    if (this.inboundNodes.length === 0) {\n      throw new AttributeError(`Layer ${this.name}` + ' has no inbound nodes.');\n    }\n    if (this.inboundNodes.length > 1) {\n      throw new AttributeError(`Layer ${this.name}` + ' has multiple inbound nodes, ' + 'hence the notion of \"layer output\" ' + 'is ill-defined. ' + 'Use `getOutputAt(nodeIndex)` instead.');\n    }\n    return generic_utils.singletonOrArray(this.getNodeAtIndex(0, 'output').outputTensors);\n  }\n  get losses() {\n    return this._losses;\n  }\n  /**\n   * Retrieves the Layer's current loss values.\n   *\n   * Used for regularizers during training.\n   */\n  calculateLosses() {\n    // Porting Node: This is an augmentation to Layer.loss in PyKeras.\n    //   In PyKeras, Layer.loss returns symbolic tensors. Here a concrete\n    //   Tensor (specifically Scalar) values are returned. This is due to the\n    //   imperative backend.\n    return this.losses.map(lossFn => lossFn());\n  }\n  get updates() {\n    return this._updates;\n  }\n  get built() {\n    return this._built;\n  }\n  set built(built) {\n    this._built = built;\n  }\n  get trainable() {\n    return this.trainable_;\n  }\n  set trainable(trainable) {\n    this._trainableWeights.forEach(w => w.trainable = trainable);\n    this.trainable_ = trainable;\n  }\n  get trainableWeights() {\n    if (this.trainable_) {\n      return this._trainableWeights.filter(w => w.trainable);\n    } else {\n      return [];\n    }\n  }\n  set trainableWeights(weights) {\n    this._trainableWeights = weights;\n  }\n  get nonTrainableWeights() {\n    if (this.trainable) {\n      return this._trainableWeights.filter(w => !w.trainable).concat(this._nonTrainableWeights);\n    } else {\n      return this._trainableWeights.concat(this._nonTrainableWeights);\n    }\n  }\n  set nonTrainableWeights(weights) {\n    this._nonTrainableWeights = weights;\n  }\n  /**\n   * The concatenation of the lists trainableWeights and nonTrainableWeights\n   * (in this order).\n   */\n  get weights() {\n    return this.trainableWeights.concat(this.nonTrainableWeights);\n  }\n  get stateful() {\n    return this._stateful;\n  }\n  /**\n   * Reset the states of the layer.\n   *\n   * This method of the base Layer class is essentially a no-op.\n   * Subclasses that are stateful (e.g., stateful RNNs) should override this\n   * method.\n   */\n  resetStates() {\n    if (!this.stateful) {\n      throw new Error('Cannot call the resetStates() method of a non-stateful Layer ' + 'object.');\n    }\n  }\n  /**\n   * Checks compatibility between the layer and provided inputs.\n   *\n   * This checks that the tensor(s) `input`\n   * verify the input assumptions of the layer\n   * (if any). If not, exceptions are raised.\n   *\n   * @param inputs Input tensor or list of input tensors.\n   *\n   * @exception ValueError in case of mismatch between\n   *   the provided inputs and the expectations of the layer.\n   */\n  assertInputCompatibility(inputs) {\n    const inputsList = generic_utils.toList(inputs);\n    if (this.inputSpec == null || this.inputSpec.length === 0) {\n      return;\n    }\n    const inputSpec = generic_utils.toList(this.inputSpec);\n    if (inputsList.length !== inputSpec.length) {\n      throw new ValueError(`Layer ${this.name} expects ${inputSpec.length} inputs, ` + `but it received ${inputsList.length} input tensors. ` + `Input received: ${inputs}`);\n    }\n    for (let inputIndex = 0; inputIndex < inputsList.length; inputIndex++) {\n      const x = inputsList[inputIndex];\n      const spec = inputSpec[inputIndex];\n      if (spec == null) {\n        continue;\n      }\n      // Check ndim.\n      const ndim = x.rank;\n      if (spec.ndim != null) {\n        if (ndim !== spec.ndim) {\n          throw new ValueError(`Input ${inputIndex} is incompatible with layer ${this.name}: ` + `expected ndim=${spec.ndim}, found ndim=${ndim}`);\n        }\n      }\n      if (spec.maxNDim != null) {\n        if (ndim > spec.maxNDim) {\n          throw new ValueError(`Input ${inputIndex} is incompatible with layer ${this.name}` + `: expected max_ndim=${spec.maxNDim}, found ndim=${ndim}`);\n        }\n      }\n      if (spec.minNDim != null) {\n        if (ndim < spec.minNDim) {\n          throw new ValueError(`Input ${inputIndex} is incompatible with layer ${this.name}` + `: expected min_ndim=${spec.minNDim}, found ndim=${ndim}.`);\n        }\n      }\n      // Check dtype.\n      if (spec.dtype != null) {\n        if (x.dtype !== spec.dtype) {\n          throw new ValueError(`Input ${inputIndex} is incompatible with layer ${this.name} ` + `: expected dtype=${spec.dtype}, found dtype=${x.dtype}.`);\n        }\n      }\n      // Check specific shape axes.\n      if (spec.axes) {\n        const xShape = x.shape;\n        for (const key in spec.axes) {\n          const axis = Number(key);\n          const value = spec.axes[key];\n          // Perform Python-style slicing in case axis < 0;\n          // TODO(cais): Use https://github.com/alvivi/typescript-underscore to\n          // ensure type safety through Underscore calls.\n          const xShapeAtAxis = axis >= 0 ? xShape[axis] : xShape[xShape.length + axis];\n          if (value != null && [value, null].indexOf(xShapeAtAxis) === -1) {\n            throw new ValueError(`Input ${inputIndex} is incompatible with layer ` + `${this.name}: expected axis ${axis} of input shape to ` + `have value ${value} but got shape ${xShape}.`);\n          }\n        }\n      }\n      // Check shape.\n      if (spec.shape != null) {\n        for (let i = 0; i < spec.shape.length; ++i) {\n          const specDim = spec.shape[i];\n          const dim = x.shape[i];\n          if (specDim != null && dim != null) {\n            if (specDim !== dim) {\n              throw new ValueError(`Input ${inputIndex} is incompatible with layer ` + `${this.name}: expected shape=${spec.shape}, ` + `found shape=${x.shape}.`);\n            }\n          }\n        }\n      }\n    }\n  }\n  /**\n   * This is where the layer's logic lives.\n   *\n   * @param inputs Input tensor, or list/tuple of input tensors.\n   * @param kwargs Additional keyword arguments.\n   *\n   * @return A tensor or list/tuple of tensors.\n   */\n  call(inputs, kwargs) {\n    return inputs;\n  }\n  invokeCallHook(inputs, kwargs) {\n    if (this._callHook != null) {\n      this._callHook(inputs, kwargs);\n    }\n  }\n  /**\n   * Set call hook.\n   * This is currently used for testing only.\n   * @param callHook\n   */\n  setCallHook(callHook) {\n    this._callHook = callHook;\n  }\n  /**\n   * Clear call hook.\n   * This is currently used for testing only.\n   */\n  clearCallHook() {\n    this._callHook = null;\n  }\n  /**\n   * Builds or executes a `Layer`'s logic.\n   *\n   * When called with `tf.Tensor`(s), execute the `Layer`'s computation and\n   * return Tensor(s). For example:\n   *\n   * ```js\n   * const denseLayer = tf.layers.dense({\n   *   units: 1,\n   *   kernelInitializer: 'zeros',\n   *   useBias: false\n   * });\n   *\n   * // Invoke the layer's apply() method with a `tf.Tensor` (with concrete\n   * // numeric values).\n   * const input = tf.ones([2, 2]);\n   * const output = denseLayer.apply(input);\n   *\n   * // The output's value is expected to be [[0], [0]], due to the fact that\n   * // the dense layer has a kernel initialized to all-zeros and does not have\n   * // a bias.\n   * output.print();\n   * ```\n   *\n   * When called with `tf.SymbolicTensor`(s), this will prepare the layer for\n   * future execution.  This entails internal book-keeping on shapes of\n   * expected Tensors, wiring layers together, and initializing weights.\n   *\n   * Calling `apply` with `tf.SymbolicTensor`s are typically used during the\n   * building of non-`tf.Sequential` models. For example:\n   *\n   * ```js\n   * const flattenLayer = tf.layers.flatten();\n   * const denseLayer = tf.layers.dense({units: 1});\n   *\n   * // Use tf.layers.input() to obtain a SymbolicTensor as input to apply().\n   * const input = tf.input({shape: [2, 2]});\n   * const output1 = flattenLayer.apply(input);\n   *\n   * // output1.shape is [null, 4]. The first dimension is the undetermined\n   * // batch size. The second dimension comes from flattening the [2, 2]\n   * // shape.\n   * console.log(JSON.stringify(output1.shape));\n   *\n   * // The output SymbolicTensor of the flatten layer can be used to call\n   * // the apply() of the dense layer:\n   * const output2 = denseLayer.apply(output1);\n   *\n   * // output2.shape is [null, 1]. The first dimension is the undetermined\n   * // batch size. The second dimension matches the number of units of the\n   * // dense layer.\n   * console.log(JSON.stringify(output2.shape));\n   *\n   * // The input and output can be used to construct a model that consists\n   * // of the flatten and dense layers.\n   * const model = tf.model({inputs: input, outputs: output2});\n   * ```\n   *\n   * @param inputs a `tf.Tensor` or `tf.SymbolicTensor` or an Array of them.\n   * @param kwargs Additional keyword arguments to be passed to `call()`.\n   *\n   * @return Output of the layer's `call` method.\n   *\n   * @exception ValueError error in case the layer is missing shape information\n   *   for its `build` call.\n   *\n   * @doc {heading: 'Models', 'subheading': 'Classes'}\n   */\n  // Porting Note: This is a replacement for __call__() in Python.\n  apply(inputs, kwargs) {\n    kwargs = kwargs || {};\n    this.assertNotDisposed();\n    // Ensure inputs are all the same type.\n    const inputsList = generic_utils.toList(inputs);\n    const allAreSymbolic = checkAllSymbolic(inputs);\n    const noneAreSymbolic = checkNoneSymbolic(inputs);\n    if (allAreSymbolic === noneAreSymbolic) {\n      throw new ValueError('Arguments to apply() must be all ' + 'SymbolicTensors or all Tensors');\n    }\n    // TODO(michaelterry): nameScope() may not be necessary.\n    return nameScope(this.name, () => {\n      // Handle laying building (weight creating, input spec locking).\n      if (!this.built) {\n        /*\n          Throw exceptions in case the input is not compatible\n          with the inputSpec specified in the layer constructor.\n         */\n        this.assertInputCompatibility(inputs);\n        // Collect input shapes to build layer.\n        const inputShapes = [];\n        for (const xElem of generic_utils.toList(inputs)) {\n          inputShapes.push(xElem.shape);\n        }\n        this.build(generic_utils.singletonOrArray(inputShapes));\n        this.built = true;\n        // Load weights that were specified at layer instantiation.\n        if (this.initialWeights) {\n          this.setWeights(this.initialWeights);\n        }\n        if (this._refCount === null && noneAreSymbolic) {\n          // The first use of this layer is a non-symbolic call, set ref count\n          // to 1 so the Layer can be properly disposed if its dispose() method\n          // is called.\n          this._refCount = 1;\n        }\n      }\n      /*\n        Throw exceptions in case the input is not compatible\n        with the inputSpec set at build time.\n      */\n      this.assertInputCompatibility(inputs);\n      // Handle mask propagation.\n      // TODO(michaelterry): Mask propagation not currently implemented.\n      // Actually call the layer, collecting output(s), mask(s), and shape(s).\n      if (noneAreSymbolic) {\n        let output = this.call(inputs, kwargs);\n        // Apply masks to the output tensors if the layer supports it.\n        if (this.supportsMasking) {\n          // TODO(mattsoulanille): pass the input tensors' masks to computeMask\n          this.setMaskMetadata(inputs, output);\n        }\n        // If the layer returns tensors from its inputs, unmodified,\n        // we copy them to avoid loss of tensor metadata.\n        const outputList = generic_utils.toList(output);\n        const outputListCopy = [];\n        // TODO(michaelterry): This copying may not be necessary given our eager\n        // backend.\n        for (let x of outputList) {\n          if (inputsList.indexOf(x) !== -1) {\n            x = x.clone();\n          }\n          outputListCopy.push(x);\n        }\n        output = generic_utils.singletonOrArray(outputListCopy);\n        if (this.activityRegularizer != null) {\n          throw new NotImplementedError('Layer invocation in the presence of activity ' + 'regularizer(s) is not supported yet.');\n        }\n        // TODO(michaelterry): Call addInboundNode()?\n        return output;\n      } else {\n        const inputShape = collectInputShape(inputs);\n        const outputShape = this.computeOutputShape(inputShape);\n        let output;\n        const outputDType = guessOutputDType(inputs);\n        this.warnOnIncompatibleInputShape(Array.isArray(inputs) ? inputShape[0] : inputShape);\n        if (outputShape != null && outputShape.length > 0 && Array.isArray(outputShape[0])) {\n          // We have multiple output shapes. Create multiple output tensors.\n          output = outputShape.map((shape, index) => new SymbolicTensor(outputDType, shape, this, generic_utils.toList(inputs), kwargs, this.name, index));\n        } else {\n          output = new SymbolicTensor(outputDType, outputShape, this, generic_utils.toList(inputs), kwargs, this.name);\n        }\n        /*\n          Add an inbound node to the layer, so that it keeps track\n          of the call and of all new variables created during the call.\n          This also updates the layer history of the output tensor(s).\n          If the input tensor(s) had no previous history,\n          this does nothing.\n        */\n        this.addInboundNode(inputs, output, null, null, inputShape, outputShape, kwargs);\n        this._refCount++;\n        if (this.activityRegularizer != null) {\n          throw new NotImplementedError('Layer invocation in the presence of activity ' + 'regularizer(s) is not supported yet.');\n        }\n        return output;\n      }\n    });\n  }\n  /**\n   * Check compatibility between input shape and this layer's batchInputShape.\n   *\n   * Print warning if any incompatibility is found.\n   *\n   * @param inputShape Input shape to be checked.\n   */\n  warnOnIncompatibleInputShape(inputShape) {\n    if (this.batchInputShape == null) {\n      return;\n    } else if (inputShape.length !== this.batchInputShape.length) {\n      console.warn(`The rank of the input tensor provided (shape: ` + `${JSON.stringify(inputShape)}) does not match that of the ` + `batchInputShape (${JSON.stringify(this.batchInputShape)}) ` + `of the layer ${this.name}`);\n    } else {\n      let dimMismatch = false;\n      this.batchInputShape.forEach((dimension, i) => {\n        if (dimension != null && inputShape[i] != null && inputShape[i] !== dimension) {\n          dimMismatch = true;\n        }\n      });\n      if (dimMismatch) {\n        console.warn(`The shape of the input tensor ` + `(${JSON.stringify(inputShape)}) does not ` + `match the expectation of layer ${this.name}: ` + `${JSON.stringify(this.batchInputShape)}`);\n      }\n    }\n  }\n  /**\n   * Retrieves the output shape(s) of a layer.\n   *\n   * Only applicable if the layer has only one inbound node, or if all inbound\n   * nodes have the same output shape.\n   *\n   * @returns Output shape or shapes.\n   * @throws AttributeError: if the layer is connected to more than one incoming\n   *   nodes.\n   *\n   * @doc {heading: 'Models', 'subheading': 'Classes'}\n   */\n  get outputShape() {\n    if (this.inboundNodes == null || this.inboundNodes.length === 0) {\n      throw new AttributeError(`The layer ${this.name} has never been called and thus has no ` + `defined output shape.`);\n    }\n    const allOutputShapes = [];\n    for (const node of this.inboundNodes) {\n      const shapeString = JSON.stringify(node.outputShapes);\n      if (allOutputShapes.indexOf(shapeString) === -1) {\n        allOutputShapes.push(shapeString);\n      }\n    }\n    if (allOutputShapes.length === 1) {\n      const outputShapes = this.inboundNodes[0].outputShapes;\n      if (Array.isArray(outputShapes) && Array.isArray(outputShapes[0]) && outputShapes.length === 1) {\n        return outputShapes[0];\n      } else {\n        return outputShapes;\n      }\n    } else {\n      throw new AttributeError(`The layer ${this.name} has multiple inbound nodes with different ` + `output shapes. Hence the notion of \"output shape\" is ill-defined ` + `for the layer.`);\n      // TODO(cais): Implement getOutputShapeAt().\n    }\n  }\n  /**\n   * Counts the total number of numbers (e.g., float32, int32) in the\n   * weights.\n   *\n   * @returns An integer count.\n   * @throws RuntimeError: If the layer is not built yet (in which case its\n   *   weights are not defined yet.)\n   *\n   * @doc {heading: 'Models', 'subheading': 'Classes'}\n   */\n  countParams() {\n    if (!this.built) {\n      throw new RuntimeError(`You tried to call countParams() on ${this.name}, ` + `but the layer is not built yet. Build it first by calling ` + `build(batchInputShape).`);\n    }\n    return variable_utils.countParamsInWeights(this.weights);\n  }\n  /**\n   * Creates the layer weights.\n   *\n   * Must be implemented on all layers that have weights.\n   *\n   * Called when apply() is called to construct the weights.\n   *\n   * @param inputShape A `Shape` or array of `Shape` (unused).\n   *\n   * @doc {heading: 'Models', 'subheading': 'Classes'}\n   */\n  build(inputShape) {\n    this.built = true;\n  }\n  /**\n   * Returns the current values of the weights of the layer.\n   *\n   * @param trainableOnly Whether to get the values of only trainable weights.\n   * @returns Weight values as an `Array` of `tf.Tensor`s.\n   *\n   * @doc {heading: 'Models', 'subheading': 'Classes'}\n   */\n  getWeights(trainableOnly = false) {\n    return batchGetValue(trainableOnly ? this.trainableWeights : this.weights);\n  }\n  /**\n   * Sets the weights of the layer, from Tensors.\n   *\n   * @param weights a list of Tensors. The number of arrays and their shape\n   *   must match number of the dimensions of the weights of the layer (i.e.\n   *   it should match the output of `getWeights`).\n   *\n   * @exception ValueError If the provided weights list does not match the\n   *   layer's specifications.\n   *\n   * @doc {heading: 'Models', 'subheading': 'Classes'}\n   */\n  setWeights(weights) {\n    tidy(() => {\n      const params = this.weights;\n      if (params.length !== weights.length) {\n        // TODO(cais): Restore the following and use `providedWeights`, instead\n        // of `weights` in the error message, once the deeplearn.js bug is\n        // fixed: https://github.com/PAIR-code/deeplearnjs/issues/498 const\n        // providedWeights = JSON.stringify(weights).slice(0, 50);\n        throw new ValueError(`You called setWeights(weights) on layer \"${this.name}\" ` + `with a weight list of length ${weights.length}, ` + `but the layer was expecting ${params.length} weights. ` + `Provided weights: ${weights}...`);\n      }\n      if (params.length === 0) {\n        return;\n      }\n      const weightValueTuples = [];\n      const paramValues = batchGetValue(params);\n      for (let i = 0; i < paramValues.length; ++i) {\n        const pv = paramValues[i];\n        const p = params[i];\n        const w = weights[i];\n        if (!util.arraysEqual(pv.shape, w.shape)) {\n          throw new ValueError(`Layer weight shape ${pv.shape} ` + `not compatible with provided weight shape ${w.shape}`);\n        }\n        weightValueTuples.push([p, w]);\n      }\n      batchSetValue(weightValueTuples);\n    });\n  }\n  /**\n   * Adds a weight variable to the layer.\n   *\n   * @param name Name of the new weight variable.\n   * @param shape The shape of the weight.\n   * @param dtype The dtype of the weight.\n   * @param initializer An initializer instance.\n   * @param regularizer A regularizer instance.\n   * @param trainable Whether the weight should be trained via backprop or not\n   *   (assuming that the layer itself is also trainable).\n   * @param constraint An optional trainable.\n   * @return The created weight variable.\n   *\n   * @doc {heading: 'Models', 'subheading': 'Classes'}\n   */\n  addWeight(name, shape, dtype, initializer, regularizer, trainable, constraint, getInitializerFunc) {\n    // Reject duplicate weight names.\n    if (this._addedWeightNames.indexOf(name) !== -1) {\n      throw new ValueError(`Duplicate weight name ${name} for layer ${this.name}`);\n    }\n    this._addedWeightNames.push(name);\n    if (dtype == null) {\n      dtype = 'float32';\n    }\n    if (this.fastWeightInitDuringBuild) {\n      initializer = getInitializerFunc != null ? getInitializerFunc() : getInitializer('zeros');\n    }\n    const initValue = initializer.apply(shape, dtype);\n    const weight = new LayerVariable(initValue, dtype, name, trainable, constraint);\n    initValue.dispose();\n    // Request backend not to dispose the weights of the model on scope() exit.\n    if (regularizer != null) {\n      this.addLoss(() => regularizer.apply(weight.read()));\n    }\n    if (trainable == null) {\n      trainable = true;\n    }\n    if (trainable) {\n      this._trainableWeights.push(weight);\n    } else {\n      this._nonTrainableWeights.push(weight);\n    }\n    return weight;\n  }\n  /**\n   * Set the fast-weight-initialization flag.\n   *\n   * In cases where the initialized weight values will be immediately\n   * overwritten by loaded weight values during model loading, setting\n   * the flag to `true` saves unnecessary calls to potentially expensive\n   * initializers and speeds up the loading process.\n   *\n   * @param value Target value of the flag.\n   */\n  setFastWeightInitDuringBuild(value) {\n    this.fastWeightInitDuringBuild = value;\n  }\n  /**\n   * Add losses to the layer.\n   *\n   * The loss may potentially be conditional on some inputs tensors,\n   * for instance activity losses are conditional on the layer's inputs.\n   *\n   * @doc {heading: 'Models', 'subheading': 'Classes'}\n   */\n  addLoss(losses) {\n    if (losses == null || Array.isArray(losses) && losses.length === 0) {\n      return;\n    }\n    // Update this.losses\n    losses = generic_utils.toList(losses);\n    if (this._losses !== undefined && this._losses !== null) {\n      this.losses.push(...losses);\n    }\n  }\n  /**\n   * Computes the output shape of the layer.\n   *\n   * Assumes that the layer will be built to match that input shape provided.\n   *\n   * @param inputShape A shape (tuple of integers) or a list of shape tuples\n   *   (one per output tensor of the layer). Shape tuples can include null for\n   *   free dimensions, instead of an integer.\n   *\n   * @doc {heading: 'Models', 'subheading': 'Classes'}\n   */\n  computeOutputShape(inputShape) {\n    return inputShape;\n  }\n  /**\n   * Computes an output mask tensor.\n   *\n   * @param inputs Tensor or list of tensors.\n   * @param mask Tensor or list of tensors.\n   *\n   * @return null or a tensor (or list of tensors, one per output tensor of the\n   * layer).\n   */\n  computeMask(inputs, mask) {\n    if (!this.supportsMasking) {\n      if (mask != null) {\n        if (Array.isArray(mask)) {\n          mask.forEach(maskElement => {\n            if (maskElement != null) {\n              throw new TypeError(`Layer ${this.name} does not support masking, ` + 'but was passed an inputMask.');\n            }\n          });\n        } else {\n          throw new TypeError(`Layer ${this.name} does not support masking, ` + 'but was passed an inputMask.');\n        }\n      }\n      // masking not explicitly supported: return null as mask\n      return null;\n    }\n    // if masking is explictly supported, by default\n    // carry over the input mask\n    return mask;\n  }\n  setMaskMetadata(inputs, outputs, previousMask) {\n    if (!this.supportsMasking) {\n      return;\n    }\n    const outputMasks = this.computeMask(inputs, previousMask);\n    const outputsList = generic_utils.toList(outputs);\n    const outputMasksList = generic_utils.toList(outputMasks);\n    if (outputsList.length !== outputMasksList.length) {\n      throw new Error(`${this.name} outputs ${outputsList.length} tensors ` + `but ${outputsList.length} masks for those tensors`);\n    }\n    for (let i = 0; i < outputsList.length; i++) {\n      outputsList[i].kerasMask = outputMasksList[i];\n    }\n  }\n  /**\n   * Internal method to create an inbound node for the layer.\n   *\n   * @param inputTensors List of input tensors.\n   * @param outputTensors List of output tensors.\n   * @param inputMasks List of input masks (a mask can be a tensor, or null).\n   * @param outputMasks List of output masks (a mask can be a tensor, or null).\n   * @param inputShapes List of input shape tuples.\n   * @param outputShapes List of output shape tuples.\n   * @param kwargs Dictionary of keyword arguments that were passed to the\n   *   `call` method of the layer at the call that created the node.\n   */\n  addInboundNode(inputTensors, outputTensors, inputMasks, outputMasks, inputShapes, outputShapes, kwargs = null) {\n    const inputTensorList = generic_utils.toList(inputTensors);\n    outputTensors = generic_utils.toList(outputTensors);\n    inputMasks = generic_utils.toList(inputMasks);\n    outputMasks = generic_utils.toList(outputMasks);\n    inputShapes = types_utils.normalizeShapeList(inputShapes);\n    outputShapes = types_utils.normalizeShapeList(outputShapes);\n    // Collect input tensor(s) coordinates.\n    const inboundLayers = [];\n    const nodeIndices = [];\n    const tensorIndices = [];\n    for (const x of inputTensorList) {\n      /*\n       * TODO(michaelterry): Keras adds this value to tensors; it's not\n       * clear whether we'll use this or not.\n       */\n      inboundLayers.push(x.sourceLayer);\n      nodeIndices.push(x.nodeIndex);\n      tensorIndices.push(x.tensorIndex);\n    }\n    // Create node, add it to inbound nodes.\n    // (This call has side effects.)\n    // tslint:disable-next-line:no-unused-expression\n    new Node({\n      outboundLayer: this,\n      inboundLayers,\n      nodeIndices,\n      tensorIndices,\n      inputTensors: inputTensorList,\n      outputTensors,\n      inputMasks,\n      outputMasks,\n      inputShapes,\n      outputShapes\n    }, kwargs);\n    // Update tensor history\n    for (let i = 0; i < outputTensors.length; i++) {\n      // TODO(michaelterry: _uses_learning_phase not tracked.\n      outputTensors[i].sourceLayer = this;\n      outputTensors[i].nodeIndex = this.inboundNodes.length - 1;\n      outputTensors[i].tensorIndex = i;\n    }\n  }\n  /**\n   * Returns the config of the layer.\n   *\n   * A layer config is a TS dictionary (serializable)\n   * containing the configuration of a layer.\n   * The same layer can be reinstantiated later\n   * (without its trained weights) from this configuration.\n   *\n   * The config of a layer does not include connectivity\n   * information, nor the layer class name.  These are handled\n   * by 'Container' (one layer of abstraction above).\n   *\n   * Porting Note: The TS dictionary follows TS naming standards for\n   * keys, and uses tfjs-layers type-safe Enums.  Serialization methods\n   * should use a helper function to convert to the pythonic storage\n   * standard. (see serialization_utils.convertTsToPythonic)\n   *\n   * @returns TS dictionary of configuration.\n   *\n   * @doc {heading: 'Models', 'subheading': 'Classes'}\n   */\n  getConfig() {\n    const config = {\n      name: this.name,\n      trainable: this.trainable\n    };\n    if (this.batchInputShape != null) {\n      config['batchInputShape'] = this.batchInputShape;\n    }\n    if (this.dtype != null) {\n      config['dtype'] = this.dtype;\n    }\n    return config;\n  }\n  /**\n   * Dispose the weight variables that this Layer instance holds.\n   *\n   * @returns {number} Number of disposed variables.\n   */\n  disposeWeights() {\n    this.weights.forEach(weight => weight.dispose());\n    return this.weights.length;\n  }\n  assertNotDisposed() {\n    if (this._refCount === 0) {\n      throw new Error(`Layer '${this.name}' is already disposed.`);\n    }\n  }\n  /**\n   * Attempt to dispose layer's weights.\n   *\n   * This method decreases the reference count of the Layer object by 1.\n   *\n   * A Layer is reference-counted. Its reference count is incremented by 1\n   * the first item its `apply()` method is called and when it becomes a part\n   * of a new `Node` (through calling the `apply()` method on a\n   * `tf.SymbolicTensor`).\n   *\n   * If the reference count of a Layer becomes 0, all the weights will be\n   * disposed and the underlying memory (e.g., the textures allocated in WebGL)\n   * will be freed.\n   *\n   * Note: If the reference count is greater than 0 after the decrement, the\n   * weights of the Layer will *not* be disposed.\n   *\n   * After a Layer is disposed, it cannot be used in calls such as `apply()`,\n   * `getWeights()` or `setWeights()` anymore.\n   *\n   * @returns A DisposeResult Object with the following fields:\n   *   - refCountAfterDispose: The reference count of the Container after this\n   *     `dispose()` call.\n   *   - numDisposedVariables: Number of `tf.Variable`s (i.e., weights) disposed\n   *     during this `dispose()` call.\n   * @throws {Error} If the layer is not built yet, or if the layer has already\n   *   been disposed.\n   *\n   * @doc {heading: 'Models', 'subheading': 'Classes'}\n   */\n  dispose() {\n    if (!this.built) {\n      throw new Error(`Cannot dispose Layer ${this.name} because it has not been ` + `built yet.`);\n    }\n    if (this._refCount === null) {\n      throw new Error(`Cannot dispose Layer ${this.name} because it has not been used ` + `yet.`);\n    }\n    this.assertNotDisposed();\n    let numDisposedVariables = 0;\n    if (--this._refCount === 0) {\n      numDisposedVariables = this.disposeWeights();\n    }\n    return {\n      refCountAfterDispose: this._refCount,\n      numDisposedVariables\n    };\n  }\n}\n/**\n * Collects the input shape(s) of a list of `tf.Tensor`s or\n * `tf.SymbolicTensor`s.\n *\n * TODO(michaelterry): Update PyKeras docs (backport).\n *\n * @param inputTensors List of input tensors (or single input tensor).\n *\n * @return List of shape tuples (or single tuple), one tuple per input.\n */\nfunction collectInputShape(inputTensors) {\n  inputTensors = generic_utils.toList(inputTensors);\n  const shapes = [];\n  for (const x of inputTensors) {\n    shapes.push(x.shape);\n  }\n  return generic_utils.singletonOrArray(shapes);\n}\n/**\n * Guesses output dtype based on inputs.\n *\n * At present, just returns 'float32' for any input.\n *\n * @param inputTensors List of input tensors (or single input tensor).\n *\n * @return The guessed DType. At present, always returns 'float32'.\n */\nfunction guessOutputDType(inputTensors) {\n  return 'float32';\n}\n/**\n * Returns the list of input tensors necessary to compute `tensor`.\n *\n * Output will always be a list of tensors (potentially with 1 element).\n *\n * @param tensor The tensor to start from.\n * @param layer Origin layer of the tensor.\n * @param nodeIndex Origin node index of the tensor.\n *\n * @return Array of input tensors.\n */\nexport function getSourceInputs(tensor, layer, nodeIndex) {\n  if (layer == null || nodeIndex != null && nodeIndex > 0) {\n    layer = tensor.sourceLayer;\n    nodeIndex = tensor.nodeIndex;\n  }\n  if (layer.inboundNodes.length === 0) {\n    return [tensor];\n  } else {\n    const node = layer.inboundNodes[nodeIndex];\n    if (node.inboundLayers.length === 0) {\n      return node.inputTensors;\n    } else {\n      const sourceTensors = [];\n      for (let i = 0; i < node.inboundLayers.length; i++) {\n        const x = node.inputTensors[i];\n        const layer = node.inboundLayers[i];\n        const nodeIndex = node.nodeIndices[i];\n        const previousSources = getSourceInputs(x, layer, nodeIndex);\n        // Avoid input redundancy.\n        for (const x of previousSources) {\n          if (sourceTensors.indexOf(x) === -1) {\n            sourceTensors.push(x);\n          }\n        }\n      }\n      return sourceTensors;\n    }\n  }\n}\nfunction checkAllSymbolic(tensors) {\n  let allAreSymbolic = true;\n  for (const tensor of generic_utils.toList(tensors)) {\n    if (!(tensor instanceof SymbolicTensor)) {\n      allAreSymbolic = false;\n      break;\n    }\n  }\n  return allAreSymbolic;\n}\nfunction checkNoneSymbolic(tensors) {\n  let noneAreSymbolic = true;\n  for (const tensor of generic_utils.toList(tensors)) {\n    if (tensor instanceof SymbolicTensor) {\n      noneAreSymbolic = false;\n      break;\n    }\n  }\n  return noneAreSymbolic;\n}", "map": {"version": 3, "names": ["serialization", "tidy", "util", "getNextUniqueTensorId", "getUid", "getScopedTensorName", "getUniqueTensorName", "nameScope", "AttributeError", "NotImplementedError", "RuntimeError", "ValueError", "getInitializer", "generic_utils", "types_utils", "variable_utils", "batchGetValue", "batchSetValue", "LayerVariable", "InputSpec", "constructor", "args", "dtype", "shape", "ndim", "length", "max<PERSON><PERSON>", "min<PERSON><PERSON>", "axes", "SymbolicTensor", "sourceLayer", "inputs", "callArgs", "name", "outputTensorIndex", "id", "originalName", "rank", "_nextNodeID", "Node", "outboundLayer", "inboundLayers", "nodeIndices", "tensorIndices", "inputTensors", "outputTensors", "inputMasks", "outputMasks", "inputShapes", "outputShapes", "layer", "outboundNodes", "push", "inboundNodes", "getConfig", "inboundNames", "_nextLayerID", "Layer", "Serializable", "_callHook", "_addedWeightNames", "_stateful", "activityRegularizer", "inputSpec", "supportsMasking", "_trainableWeights", "_nonTrainableWeights", "_losses", "_updates", "_built", "prefix", "getClassName", "toSnakeCase", "trainable_", "trainable", "inputShape", "batchInputShape", "batchSize", "concat", "inputDType", "weights", "initialWeights", "_refCount", "fastWeightInitDuringBuild", "nodeKey", "nodeIndex", "toString", "getNodeAtIndex", "attrName", "getInputAt", "singletonOrArray", "getOutputAt", "input", "output", "losses", "calculateLosses", "map", "lossFn", "updates", "built", "for<PERSON>ach", "w", "trainableWeights", "filter", "nonTrainableWeights", "stateful", "resetStates", "Error", "assertInputCompatibility", "inputsList", "toList", "inputIndex", "x", "spec", "xShape", "key", "axis", "Number", "value", "xShapeAtAxis", "indexOf", "i", "specDim", "dim", "call", "kwargs", "invokeCallHook", "setCallHook", "callHook", "clearCallHook", "apply", "assertNotDisposed", "allAreSymbolic", "checkAllSymbolic", "noneAreSymbolic", "checkNoneSymbolic", "xElem", "build", "setWeights", "setMaskMetadata", "outputList", "outputListCopy", "clone", "collectInputShape", "outputShape", "computeOutputShape", "outputDType", "guessOutputDType", "warnOnIncompatibleInputShape", "Array", "isArray", "index", "addInboundNode", "console", "warn", "JSON", "stringify", "dimMismatch", "dimension", "allOutputShapes", "node", "shapeString", "countParams", "countParamsInWeights", "getWeights", "trainableOnly", "params", "weightValueTuples", "paramV<PERSON><PERSON>", "pv", "p", "arraysEqual", "addWeight", "initializer", "regularizer", "constraint", "getInitializerFunc", "initValue", "weight", "dispose", "addLoss", "read", "setFastWeightInitDuringBuild", "undefined", "computeMask", "mask", "maskElement", "TypeError", "outputs", "previousMask", "outputsList", "outputMasksList", "kerasMask", "inputTensorList", "normalizeShapeList", "tensorIndex", "config", "disposeWeights", "numDisposedVariables", "refCountAfterDispose", "shapes", "getSourceInputs", "tensor", "sourceTensors", "previousSources", "tensors"], "sources": ["C:\\tfjs-layers\\src\\engine\\topology.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2018 Google LLC\n *\n * Use of this source code is governed by an MIT-style\n * license that can be found in the LICENSE file or at\n * https://opensource.org/licenses/MIT.\n * =============================================================================\n */\n\n/* Original source: keras/engine/topology.py */\n\nimport {DataType, Scalar, serialization, Tensor, tidy, util} from '@tensorflow/tfjs-core';\n\nimport {getNextUniqueTensorId, getUid} from '../backend/state';\nimport {getScopedTensorName, getUniqueTensorName, nameScope} from '../common';\nimport {Constraint} from '../constraints';\nimport {AttributeError, NotImplementedError, RuntimeError, ValueError} from '../errors';\nimport {getInitializer, Initializer} from '../initializers';\nimport {Shape} from '../keras_format/common';\nimport {Regularizer} from '../regularizers';\nimport {Kwargs, RegularizerFn} from '../types';\nimport * as generic_utils from '../utils/generic_utils';\nimport * as types_utils from '../utils/types_utils';\nimport * as variable_utils from '../utils/variable_utils';\nimport {batchGetValue, batchSetValue, LayerVariable} from '../variables';\n\n// TODO(michaelterry): This is a stub until it's defined.\nexport type Op = (x: LayerVariable) => LayerVariable;\n\n/**\n * Constructor arguments for InputSpec.\n */\nexport interface InputSpecArgs {\n  /** Expected datatype of the input. */\n  dtype?: DataType;\n  /** Expected shape of the input (may include null for unchecked axes). */\n  shape?: Shape;\n  /** Expected rank of the input. */\n  ndim?: number;\n  /** Maximum rank of the input. */\n  maxNDim?: number;\n  /** Minimum rank of the input. */\n  minNDim?: number;\n  /** Dictionary mapping integer axes to a specific dimension value. */\n  axes?: {[axis: number]: number};\n}\n\n/**\n * Specifies the ndim, dtype and shape of every input to a layer.\n *\n * Every layer should expose (if appropriate) an `inputSpec` attribute:\n * a list of instances of InputSpec (one per input tensor).\n *\n * A null entry in a shape is compatible with any dimension,\n * a null shape is compatible with any shape.\n */\nexport class InputSpec {\n  /** Expected datatype of the input. */\n  dtype?: DataType;\n  /** Expected shape of the input (may include null for unchecked axes). */\n  shape?: Shape;\n  /** Expected rank of the input. */\n  ndim?: number;\n  /** Maximum rank of the input. */\n  maxNDim?: number;\n  /** Minimum rank of the input. */\n  minNDim?: number;\n  /** Dictionary mapping integer axes to a specific dimension value. */\n  axes?: {[axis: number]: number};\n\n  constructor(args: InputSpecArgs) {\n    this.dtype = args.dtype;\n    this.shape = args.shape;\n    /*\n      TODO(michaelterry): Could throw error if ndim and shape are both defined\n        (then backport).\n    */\n    if (args.shape != null) {\n      this.ndim = args.shape.length;\n    } else {\n      this.ndim = args.ndim;\n    }\n    this.maxNDim = args.maxNDim;\n    this.minNDim = args.minNDim;\n    this.axes = args.axes || {};\n  }\n}\n\n/**\n * `tf.SymbolicTensor` is a placeholder for a Tensor without any concrete value.\n *\n * They are most often encountered when building a graph of `Layer`s for a\n * `tf.LayersModel` and the input data's shape, but not values are known.\n *\n * @doc {heading: 'Models', 'subheading': 'Classes'}\n */\nexport class SymbolicTensor {\n  /* A unique ID for the tensor to be able to differentiate tensors. */\n  readonly id: number;\n  // The fully scoped name of this Variable, including a unique suffix if needed\n  readonly name: string;\n  // The originally requested fully scoped name of this Variable, not including\n  // any unique suffix.  This may be needed when restoring weights because this\n  // original name is used as a key.\n  readonly originalName?: string;\n  /**\n   * Rank/dimensionality of the tensor.\n   */\n  readonly rank: number;\n  /**\n   * Replacement for _keras_history.\n   */\n  nodeIndex: number;\n  /**\n   * Replacement for _keras_history.\n   */\n  tensorIndex: number;\n\n  /**\n   *\n   * @param dtype\n   * @param shape\n   * @param sourceLayer The Layer that produced this symbolic tensor.\n   * @param inputs The inputs passed to sourceLayer's __call__() method.\n   * @param nodeIndex\n   * @param tensorIndex\n   * @param callArgs The keyword arguments passed to the __call__() method.\n   * @param name\n   * @param outputTensorIndex The index of this tensor in the list of outputs\n   *   returned by apply().\n   */\n  constructor(\n      readonly dtype: DataType, readonly shape: Shape,\n      public sourceLayer: Layer, readonly inputs: SymbolicTensor[],\n      readonly callArgs: Kwargs, name?: string,\n      readonly outputTensorIndex?: number) {\n    this.id = getNextUniqueTensorId();\n    if (name != null) {\n      this.originalName = getScopedTensorName(name);\n      this.name = getUniqueTensorName(this.originalName);\n    }\n    this.rank = shape.length;\n  }\n}\n\n/**\n * Constructor arguments for Node.\n */\nexport interface NodeArgs {\n  /**\n   * The layer that takes `inputTensors` and turns them into `outputTensors`.\n   * (the node gets created when the `call` method of the layer is called).\n   */\n  outboundLayer: Layer;\n  /**\n   * A list of layers, the same length as `inputTensors`, the layers from where\n   * `inputTensors` originate.\n   */\n  inboundLayers: Layer[];\n  /**\n   * A list of integers, the same length as `inboundLayers`. `nodeIndices[i]` is\n   * the origin node of `inputTensors[i]` (necessary since each inbound layer\n   * might have several nodes, e.g. if the layer is being shared with a\n   * different data stream).\n   */\n  nodeIndices: number[];\n  /**\n   * A list of integers, the same length as `inboundLayers`. `tensorIndices[i]`\n   * is the index of `inputTensors[i]` within the output of the inbound layer\n   * (necessary since each inbound layer might have multiple tensor outputs,\n   * with each one being independently manipulable).\n   */\n  tensorIndices: number[];\n  /** List of input tensors. */\n  inputTensors: SymbolicTensor[];\n  /** List of output tensors. */\n  outputTensors: SymbolicTensor[];\n  /** List of input masks (a mask can be a tensor, or null). */\n  inputMasks: Tensor[];\n  /** List of output masks (a mask can be a tensor, or null). */\n  outputMasks: Tensor[];\n  /** List of input shape tuples. */\n  inputShapes: Shape|Shape[];\n  /** List of output shape tuples. */\n  outputShapes: Shape|Shape[];\n}\n\n/**\n * The type of the return value of Layer.dispose() and Container.dispose().\n */\nexport interface DisposeResult {\n  /**\n   * Reference count after the dispose call.\n   */\n  refCountAfterDispose: number;\n\n  /**\n   * Number of variables dispose in this dispose call.\n   */\n  numDisposedVariables: number;\n}\n\nlet _nextNodeID = 0;\n\n/**\n * A `Node` describes the connectivity between two layers.\n *\n * Each time a layer is connected to some new input,\n * a node is added to `layer.inboundNodes`.\n *\n * Each time the output of a layer is used by another layer,\n * a node is added to `layer.outboundNodes`.\n *\n * `nodeIndices` and `tensorIndices` are basically fine-grained coordinates\n * describing the origin of the `inputTensors`, verifying the following:\n *\n * `inputTensors[i] ==\n * inboundLayers[i].inboundNodes[nodeIndices[i]].outputTensors[\n *   tensorIndices[i]]`\n *\n * A node from layer A to layer B is added to:\n *     A.outboundNodes\n *     B.inboundNodes\n */\nexport class Node {\n  /**\n   * The layer that takes `inputTensors` and turns them into `outputTensors`\n   * (the node gets created when the `call` method of the layer is called).\n   */\n  outboundLayer: Layer;\n  /**\n   * A list of layers, the same length as `inputTensors`, the layers from where\n   * `inputTensors` originate.\n   */\n  inboundLayers: Layer[];\n  /**\n   * A list of integers, the same length as `inboundLayers`. `nodeIndices[i]` is\n   * the origin node of `inputTensors[i]` (necessary since each inbound layer\n   * might have several nodes, e.g. if the layer is being shared with a\n   * different data stream).\n   */\n  nodeIndices: number[];\n  /**\n   * A list of integers, the same length as `inboundLayers`. `tensorIndices[i]`\n   * is the index of `inputTensors[i]` within the output of the inbound layer\n   * (necessary since each inbound layer might have multiple tensor outputs,\n   * with each one being independently manipulable).\n   */\n  tensorIndices: number[];\n  /** List of input tensors. */\n  inputTensors: SymbolicTensor[];\n  /** List of output tensors. */\n  outputTensors: SymbolicTensor[];\n  /** List of input masks (a mask can be a tensor, or null). */\n  inputMasks: Tensor[];\n  /** List of output masks (a mask can be a tensor, or null). */\n  outputMasks: Tensor[];\n  /** List of input shape tuples. */\n  inputShapes: Shape|Shape[];\n  /** List of output shape tuples. */\n  outputShapes: Shape|Shape[];\n\n  readonly id: number;\n\n  constructor(\n      args: NodeArgs,\n      // TODO(michaelterry): Define actual type for this.\n      public callArgs?: Kwargs) {\n    this.id = _nextNodeID++;\n    /*\n      Layer instance (NOT a list).\n      this is the layer that takes a list of input tensors\n      and turns them into a list of output tensors.\n      the current node will be added to\n      the inboundNodes of outboundLayer.\n    */\n    this.outboundLayer = args.outboundLayer;\n\n    /*\n        The following 3 properties describe where\n        the input tensors come from: which layers,\n        and for each layer, which node and which\n        tensor output of each node.\n    */\n\n    // List of layer instances.\n    this.inboundLayers = args.inboundLayers;\n    // List of integers, 1:1 mapping with inboundLayers.\n    this.nodeIndices = args.nodeIndices;\n    // List of integers, 1:1 mapping with inboundLayers.\n    this.tensorIndices = args.tensorIndices;\n\n    /*\n        Following 2 properties:\n        tensor inputs and outputs of outboundLayer.\n    */\n\n    // List of tensors. 1:1 mapping with inboundLayers.\n    this.inputTensors = args.inputTensors;\n    // List of tensors, created by outboundLayer.call().\n    this.outputTensors = args.outputTensors;\n\n    /*\n        Following 2 properties: input and output masks.\n        List of tensors, 1:1 mapping with inputTensor.\n    */\n    this.inputMasks = args.inputMasks;\n    // List of tensors, created by outboundLayer.computeMask().\n    this.outputMasks = args.outputMasks;\n\n    // Following 2 properties: input and output shapes.\n\n    // List of shape tuples, shapes of inputTensors.\n    this.inputShapes = args.inputShapes;\n    // List of shape tuples, shapes of outputTensors.\n    this.outputShapes = args.outputShapes;\n\n    // Add nodes to all layers involved.\n    for (const layer of args.inboundLayers) {\n      if (layer != null) {\n        layer.outboundNodes.push(this);\n      }\n    }\n    args.outboundLayer.inboundNodes.push(this);\n  }\n\n  getConfig(): serialization.ConfigDict {\n    const inboundNames: string[] = [];\n    for (const layer of this.inboundLayers) {\n      if (layer != null) {\n        inboundNames.push(layer.name);\n      } else {\n        inboundNames.push(null);\n      }\n    }\n    return {\n      outboundLayer: this.outboundLayer ? this.outboundLayer.name : null,\n      inboundLayers: inboundNames,\n      nodeIndices: this.nodeIndices,\n      tensorIndices: this.tensorIndices\n    };\n  }\n}\n\n/** Constructor arguments for Layer. */\nexport declare interface LayerArgs {\n  /**\n   * If defined, will be used to create an input layer to insert before this\n   * layer. If both `inputShape` and `batchInputShape` are defined,\n   * `batchInputShape` will be used. This argument is only applicable to input\n   * layers (the first layer of a model).\n   */\n  inputShape?: Shape;\n  /**\n   * If defined, will be used to create an input layer to insert before this\n   * layer. If both `inputShape` and `batchInputShape` are defined,\n   * `batchInputShape` will be used. This argument is only applicable to input\n   * layers (the first layer of a model).\n   */\n  batchInputShape?: Shape;\n  /**\n   * If `inputShape` is specified and `batchInputShape` is *not* specified,\n   * `batchSize` is used to construct the `batchInputShape`: `[batchSize,\n   * ...inputShape]`\n   */\n  batchSize?: number;\n  /**\n   * The data-type for this layer. Defaults to 'float32'.\n   * This argument is only applicable to input layers (the first layer of a\n   * model).\n   */\n  dtype?: DataType;\n  /** Name for this layer. */\n  name?: string;\n  /**\n   * Whether the weights of this layer are updatable by `fit`.\n   * Defaults to true.\n   */\n  trainable?: boolean;\n  /**\n   * Initial weight values of the layer.\n   */\n  weights?: Tensor[];\n  /** Legacy support. Do not use for new code. */\n  inputDType?: DataType;\n}\n\n// If necessary, add `output` arguments to the CallHook function.\n// This is currently used for testing only, but may be used for debugger-related\n// purposes in the future.\nexport type CallHook = (inputs: Tensor|Tensor[], kwargs: Kwargs) => void;\n\nlet _nextLayerID = 0;\n\n/**\n * A layer is a grouping of operations and weights that can be composed to\n * create a `tf.LayersModel`.\n *\n * Layers are constructed by using the functions under the\n * [tf.layers](#Layers-Basic) namespace.\n *\n * @doc {heading: 'Layers', subheading: 'Classes', namespace: 'layers'}\n */\nexport abstract class Layer extends serialization.Serializable {\n  /** Name for this layer. Must be unique within a model. */\n  name: string;\n  /**\n   * List of InputSpec class instances.\n   *\n   * Each entry describes one required input:\n   * - ndim\n   * - dtype\n   * A layer with `n` input tensors must have an `inputSpec` of length `n`.\n   */\n  inputSpec: InputSpec[];\n  supportsMasking: boolean;\n  /** Whether the layer weights will be updated during training. */\n  protected trainable_: boolean;\n  batchInputShape: Shape;\n  dtype: DataType;\n  initialWeights: Tensor[];\n\n  inboundNodes: Node[];\n  outboundNodes: Node[];\n\n  activityRegularizer: Regularizer;\n\n  protected _trainableWeights: LayerVariable[];\n  private _nonTrainableWeights: LayerVariable[];\n  private _losses: RegularizerFn[];\n  // TODO(cais): _updates is currently unused.\n  private _updates: Tensor[];\n  private _built: boolean;\n  private _callHook: CallHook = null;\n\n  private _addedWeightNames: string[] = [];\n\n  readonly id: number;\n\n  // Porting Notes: PyKeras does not have this property in this base Layer\n  //   class. Instead lets Layer subclass set it dynamically and checks the\n  //   value with `hasattr`. In tfjs-layers, we let this be a member of this\n  //   base class.\n  protected _stateful = false;\n\n  protected _refCount: number|null;\n\n  // A flag for whether fast (i.e., all-zero) weight initialization is to\n  // be used during `build()` call. This speeds up weight initialization\n  // by saving unnecessary calls to expensive initializers in cases where\n  // the initialized values will be overwritten by loaded weight values\n  // during model loading.\n  private fastWeightInitDuringBuild: boolean;\n\n  constructor(args: LayerArgs = {}) {\n    super();\n    this.id = _nextLayerID++;\n\n    this.activityRegularizer = null;\n\n    this.inputSpec = null;\n    this.supportsMasking = false;\n\n    // These properties will be set upon call of this.build()\n    this._trainableWeights = [];\n    this._nonTrainableWeights = [];\n    this._losses = [];\n    this._updates = [];\n    this._built = false;\n\n    /*\n      These lists will be filled via successive calls\n      to this.addInboundNode().\n     */\n    this.inboundNodes = [];\n    this.outboundNodes = [];\n\n    let name = args.name;\n    if (!name) {\n      const prefix = this.getClassName();\n      name = generic_utils.toSnakeCase(prefix) + '_' + getUid(prefix);\n    }\n    this.name = name;\n\n    this.trainable_ = args.trainable == null ? true : args.trainable;\n\n    if (args.inputShape != null || args.batchInputShape != null) {\n      /*\n        In this case we will later create an input layer\n        to insert before the current layer\n       */\n      let batchInputShape: Shape;\n      if (args.batchInputShape != null) {\n        batchInputShape = args.batchInputShape;\n      } else if (args.inputShape != null) {\n        let batchSize: number = null;\n        if (args.batchSize != null) {\n          batchSize = args.batchSize;\n        }\n        batchInputShape = [batchSize].concat(args.inputShape);\n      }\n      this.batchInputShape = batchInputShape;\n\n      // Set dtype.\n      let dtype = args.dtype;\n      if (dtype == null) {\n        dtype = args.inputDType;\n      }\n      if (dtype == null) {\n        dtype = 'float32';\n      }\n      this.dtype = dtype;\n    }\n\n    if (args.weights != null) {\n      this.initialWeights = args.weights;\n    } else {\n      this.initialWeights = null;\n    }\n\n    // The value of `_refCount` is initialized to null. When the layer is used\n    // in a symbolic way for the first time, it will be set to 1.\n    this._refCount = null;\n\n    this.fastWeightInitDuringBuild = false;\n  }\n\n  /**\n   * Converts a layer and its index to a unique (immutable type) name.\n   * This function is used internally with `this.containerNodes`.\n   * @param layer The layer.\n   * @param nodeIndex The layer's position (e.g. via enumerate) in a list of\n   *   nodes.\n   *\n   * @returns The unique name.\n   */\n  protected static nodeKey(layer: Layer, nodeIndex: number) {\n    return layer.name + '_ib-' + nodeIndex.toString();\n  }\n\n  /**\n   * Returns this.inboundNode at index nodeIndex.\n   *\n   * Porting note: This is a replacement for _get_node_attribute_at_index()\n   * @param nodeIndex\n   * @param attrName The name of the attribute related to request for this node.\n   */\n  private getNodeAtIndex(nodeIndex: number, attrName: string): Node {\n    if (this.inboundNodes.length === 0) {\n      throw new RuntimeError(\n          'The layer has never been called ' +\n          `and thus has no defined ${attrName}.`);\n    }\n    if (this.inboundNodes.length <= nodeIndex) {\n      throw new ValueError(\n          `Asked to get ${attrName} at node ${nodeIndex}, ` +\n          `but the layer has only ${this.inboundNodes.length} inbound nodes.`);\n    }\n    return this.inboundNodes[nodeIndex];\n  }\n\n  /**\n   * Retrieves the input tensor(s) of a layer at a given node.\n   *\n   * @param nodeIndex Integer, index of the node from which to retrieve the\n   *   attribute. E.g. `nodeIndex=0` will correspond to the first time the layer\n   *   was called.\n   *\n   * @return A tensor (or list of tensors if the layer has multiple inputs).\n   */\n  getInputAt(nodeIndex: number): SymbolicTensor|SymbolicTensor[] {\n    return generic_utils.singletonOrArray(\n        this.getNodeAtIndex(nodeIndex, 'input').inputTensors);\n  }\n\n  /**\n   * Retrieves the output tensor(s) of a layer at a given node.\n   *\n   * @param nodeIndex Integer, index of the node from which to retrieve the\n   *   attribute. E.g. `nodeIndex=0` will correspond to the first time the layer\n   *   was called.\n   *\n   * @return A tensor (or list of tensors if the layer has multiple outputs).\n   */\n  getOutputAt(nodeIndex: number): SymbolicTensor|SymbolicTensor[] {\n    return generic_utils.singletonOrArray(\n        this.getNodeAtIndex(nodeIndex, 'output').outputTensors);\n  }\n\n  // Properties\n\n  /**\n   * Retrieves the input tensor(s) of a layer.\n   *\n   * Only applicable if the layer has exactly one inbound node,\n   * i.e. if it is connected to one incoming layer.\n   *\n   * @return Input tensor or list of input tensors.\n   *\n   * @exception AttributeError if the layer is connected to more than one\n   *   incoming layers.\n   */\n  get input(): SymbolicTensor|SymbolicTensor[] {\n    if (this.inboundNodes.length > 1) {\n      throw new AttributeError(\n          `Layer ${this.name}` +\n          ' has multiple inbound nodes, ' +\n          'hence the notion of \"layer input\" ' +\n          'is ill-defined. ' +\n          'Use `getInputAt(nodeIndex)` instead.');\n    } else if (this.inboundNodes.length === 0) {\n      throw new AttributeError(\n          `Layer ${this.name}` +\n          ' is not connected, no input to return.');\n    }\n    return generic_utils.singletonOrArray(\n        this.getNodeAtIndex(0, 'input').inputTensors);\n  }\n\n  /**\n   * Retrieves the output tensor(s) of a layer.\n   *\n   * Only applicable if the layer has exactly one inbound node,\n   * i.e. if it is connected to one incoming layer.\n   *\n   * @return Output tensor or list of output tensors.\n   *\n   * @exception AttributeError if the layer is connected to more than one\n   *   incoming layers.\n   */\n  get output(): SymbolicTensor|SymbolicTensor[] {\n    if (this.inboundNodes.length === 0) {\n      throw new AttributeError(\n          `Layer ${this.name}` +\n          ' has no inbound nodes.');\n    }\n    if (this.inboundNodes.length > 1) {\n      throw new AttributeError(\n          `Layer ${this.name}` +\n          ' has multiple inbound nodes, ' +\n          'hence the notion of \"layer output\" ' +\n          'is ill-defined. ' +\n          'Use `getOutputAt(nodeIndex)` instead.');\n    }\n    return generic_utils.singletonOrArray(\n        this.getNodeAtIndex(0, 'output').outputTensors);\n  }\n\n  get losses(): RegularizerFn[] {\n    return this._losses;\n  }\n\n  /**\n   * Retrieves the Layer's current loss values.\n   *\n   * Used for regularizers during training.\n   */\n  calculateLosses(): Scalar[] {\n    // Porting Node: This is an augmentation to Layer.loss in PyKeras.\n    //   In PyKeras, Layer.loss returns symbolic tensors. Here a concrete\n    //   Tensor (specifically Scalar) values are returned. This is due to the\n    //   imperative backend.\n    return this.losses.map(lossFn => lossFn());\n  }\n\n  get updates(): Tensor[] {\n    return this._updates;\n  }\n\n  get built(): boolean {\n    return this._built;\n  }\n\n  set built(built: boolean) {\n    this._built = built;\n  }\n\n  get trainable(): boolean {\n    return this.trainable_;\n  }\n\n  set trainable(trainable: boolean) {\n    this._trainableWeights.forEach(w => w.trainable = trainable);\n    this.trainable_ = trainable;\n  }\n\n  get trainableWeights(): LayerVariable[] {\n    if (this.trainable_) {\n      return this._trainableWeights.filter(w => w.trainable);\n    } else {\n      return [];\n    }\n  }\n\n  set trainableWeights(weights: LayerVariable[]) {\n    this._trainableWeights = weights;\n  }\n\n  get nonTrainableWeights(): LayerVariable[] {\n    if (this.trainable) {\n      return this._trainableWeights.filter(w => !w.trainable)\n          .concat(this._nonTrainableWeights);\n    } else {\n      return this._trainableWeights.concat(this._nonTrainableWeights);\n    }\n  }\n\n  set nonTrainableWeights(weights: LayerVariable[]) {\n    this._nonTrainableWeights = weights;\n  }\n\n  /**\n   * The concatenation of the lists trainableWeights and nonTrainableWeights\n   * (in this order).\n   */\n  get weights(): LayerVariable[] {\n    return this.trainableWeights.concat(this.nonTrainableWeights);\n  }\n\n  get stateful(): boolean {\n    return this._stateful;\n  }\n\n  /**\n   * Reset the states of the layer.\n   *\n   * This method of the base Layer class is essentially a no-op.\n   * Subclasses that are stateful (e.g., stateful RNNs) should override this\n   * method.\n   */\n  resetStates(): void {\n    if (!this.stateful) {\n      throw new Error(\n          'Cannot call the resetStates() method of a non-stateful Layer ' +\n          'object.');\n    }\n  }\n\n  /**\n   * Checks compatibility between the layer and provided inputs.\n   *\n   * This checks that the tensor(s) `input`\n   * verify the input assumptions of the layer\n   * (if any). If not, exceptions are raised.\n   *\n   * @param inputs Input tensor or list of input tensors.\n   *\n   * @exception ValueError in case of mismatch between\n   *   the provided inputs and the expectations of the layer.\n   */\n  protected assertInputCompatibility(inputs: Tensor|Tensor[]|SymbolicTensor|\n                                     SymbolicTensor[]): void {\n    const inputsList = generic_utils.toList(inputs);\n    if (this.inputSpec == null || this.inputSpec.length === 0) {\n      return;\n    }\n    const inputSpec = generic_utils.toList(this.inputSpec);\n    if (inputsList.length !== inputSpec.length) {\n      throw new ValueError(\n          `Layer ${this.name} expects ${inputSpec.length} inputs, ` +\n          `but it received ${inputsList.length} input tensors. ` +\n          `Input received: ${inputs}`);\n    }\n    for (let inputIndex = 0; inputIndex < inputsList.length; inputIndex++) {\n      const x = inputsList[inputIndex];\n      const spec: InputSpec = inputSpec[inputIndex];\n      if (spec == null) {\n        continue;\n      }\n\n      // Check ndim.\n      const ndim = x.rank;\n      if (spec.ndim != null) {\n        if (ndim !== spec.ndim) {\n          throw new ValueError(\n              `Input ${inputIndex} is incompatible with layer ${this.name}: ` +\n              `expected ndim=${spec.ndim}, found ndim=${ndim}`);\n        }\n      }\n      if (spec.maxNDim != null) {\n        if (ndim > spec.maxNDim) {\n          throw new ValueError(\n              `Input ${inputIndex} is incompatible with layer ${this.name}` +\n              `: expected max_ndim=${spec.maxNDim}, found ndim=${ndim}`);\n        }\n      }\n      if (spec.minNDim != null) {\n        if (ndim < spec.minNDim) {\n          throw new ValueError(\n              `Input ${inputIndex} is incompatible with layer ${this.name}` +\n              `: expected min_ndim=${spec.minNDim}, found ndim=${ndim}.`);\n        }\n      }\n\n      // Check dtype.\n      if (spec.dtype != null) {\n        if (x.dtype !== spec.dtype) {\n          throw new ValueError(\n              `Input ${inputIndex} is incompatible with layer ${this.name} ` +\n              `: expected dtype=${spec.dtype}, found dtype=${x.dtype}.`);\n        }\n      }\n\n      // Check specific shape axes.\n      if (spec.axes) {\n        const xShape = x.shape;\n        for (const key in spec.axes) {\n          const axis = Number(key);\n          const value = spec.axes[key];\n          // Perform Python-style slicing in case axis < 0;\n          // TODO(cais): Use https://github.com/alvivi/typescript-underscore to\n          // ensure type safety through Underscore calls.\n          const xShapeAtAxis =\n              axis >= 0 ? xShape[axis] : xShape[xShape.length + axis];\n          if (value != null && [value, null].indexOf(xShapeAtAxis) === -1) {\n            throw new ValueError(\n                `Input ${inputIndex} is incompatible with layer ` +\n                `${this.name}: expected axis ${axis} of input shape to ` +\n                `have value ${value} but got shape ${xShape}.`);\n          }\n        }\n      }\n\n      // Check shape.\n      if (spec.shape != null) {\n        for (let i = 0; i < spec.shape.length; ++i) {\n          const specDim = spec.shape[i];\n          const dim = x.shape[i];\n          if (specDim != null && dim != null) {\n            if (specDim !== dim) {\n              throw new ValueError(\n                  `Input ${inputIndex} is incompatible with layer ` +\n                  `${this.name}: expected shape=${spec.shape}, ` +\n                  `found shape=${x.shape}.`);\n            }\n          }\n        }\n      }\n    }\n  }\n\n  /**\n   * This is where the layer's logic lives.\n   *\n   * @param inputs Input tensor, or list/tuple of input tensors.\n   * @param kwargs Additional keyword arguments.\n   *\n   * @return A tensor or list/tuple of tensors.\n   */\n  call(inputs: Tensor|Tensor[], kwargs: Kwargs): Tensor|Tensor[] {\n    return inputs;\n  }\n\n  protected invokeCallHook(inputs: Tensor|Tensor[], kwargs: Kwargs) {\n    if (this._callHook != null) {\n      this._callHook(inputs, kwargs);\n    }\n  }\n\n  /**\n   * Set call hook.\n   * This is currently used for testing only.\n   * @param callHook\n   */\n  setCallHook(callHook: CallHook) {\n    this._callHook = callHook;\n  }\n\n  /**\n   * Clear call hook.\n   * This is currently used for testing only.\n   */\n  clearCallHook() {\n    this._callHook = null;\n  }\n\n  /**\n   * Builds or executes a `Layer`'s logic.\n   *\n   * When called with `tf.Tensor`(s), execute the `Layer`'s computation and\n   * return Tensor(s). For example:\n   *\n   * ```js\n   * const denseLayer = tf.layers.dense({\n   *   units: 1,\n   *   kernelInitializer: 'zeros',\n   *   useBias: false\n   * });\n   *\n   * // Invoke the layer's apply() method with a `tf.Tensor` (with concrete\n   * // numeric values).\n   * const input = tf.ones([2, 2]);\n   * const output = denseLayer.apply(input);\n   *\n   * // The output's value is expected to be [[0], [0]], due to the fact that\n   * // the dense layer has a kernel initialized to all-zeros and does not have\n   * // a bias.\n   * output.print();\n   * ```\n   *\n   * When called with `tf.SymbolicTensor`(s), this will prepare the layer for\n   * future execution.  This entails internal book-keeping on shapes of\n   * expected Tensors, wiring layers together, and initializing weights.\n   *\n   * Calling `apply` with `tf.SymbolicTensor`s are typically used during the\n   * building of non-`tf.Sequential` models. For example:\n   *\n   * ```js\n   * const flattenLayer = tf.layers.flatten();\n   * const denseLayer = tf.layers.dense({units: 1});\n   *\n   * // Use tf.layers.input() to obtain a SymbolicTensor as input to apply().\n   * const input = tf.input({shape: [2, 2]});\n   * const output1 = flattenLayer.apply(input);\n   *\n   * // output1.shape is [null, 4]. The first dimension is the undetermined\n   * // batch size. The second dimension comes from flattening the [2, 2]\n   * // shape.\n   * console.log(JSON.stringify(output1.shape));\n   *\n   * // The output SymbolicTensor of the flatten layer can be used to call\n   * // the apply() of the dense layer:\n   * const output2 = denseLayer.apply(output1);\n   *\n   * // output2.shape is [null, 1]. The first dimension is the undetermined\n   * // batch size. The second dimension matches the number of units of the\n   * // dense layer.\n   * console.log(JSON.stringify(output2.shape));\n   *\n   * // The input and output can be used to construct a model that consists\n   * // of the flatten and dense layers.\n   * const model = tf.model({inputs: input, outputs: output2});\n   * ```\n   *\n   * @param inputs a `tf.Tensor` or `tf.SymbolicTensor` or an Array of them.\n   * @param kwargs Additional keyword arguments to be passed to `call()`.\n   *\n   * @return Output of the layer's `call` method.\n   *\n   * @exception ValueError error in case the layer is missing shape information\n   *   for its `build` call.\n   *\n   * @doc {heading: 'Models', 'subheading': 'Classes'}\n   */\n  // Porting Note: This is a replacement for __call__() in Python.\n  apply(\n      inputs: Tensor|Tensor[]|SymbolicTensor|SymbolicTensor[],\n      kwargs?: Kwargs): Tensor|Tensor[]|SymbolicTensor|SymbolicTensor[] {\n    kwargs = kwargs || {};\n\n    this.assertNotDisposed();\n\n    // Ensure inputs are all the same type.\n    const inputsList = generic_utils.toList(inputs);\n\n    const allAreSymbolic = checkAllSymbolic(inputs);\n    const noneAreSymbolic = checkNoneSymbolic(inputs);\n\n    if (allAreSymbolic === noneAreSymbolic) {\n      throw new ValueError(\n          'Arguments to apply() must be all ' +\n          'SymbolicTensors or all Tensors');\n    }\n\n    // TODO(michaelterry): nameScope() may not be necessary.\n    return nameScope(this.name, () => {\n      // Handle laying building (weight creating, input spec locking).\n      if (!this.built) {\n        /*\n          Throw exceptions in case the input is not compatible\n          with the inputSpec specified in the layer constructor.\n         */\n        this.assertInputCompatibility(inputs);\n\n        // Collect input shapes to build layer.\n        const inputShapes: Shape[] = [];\n        for (const xElem of generic_utils.toList(inputs)) {\n          inputShapes.push(xElem.shape);\n        }\n        this.build(generic_utils.singletonOrArray(inputShapes));\n        this.built = true;\n\n        // Load weights that were specified at layer instantiation.\n        if (this.initialWeights) {\n          this.setWeights(this.initialWeights);\n        }\n\n        if (this._refCount === null && noneAreSymbolic) {\n          // The first use of this layer is a non-symbolic call, set ref count\n          // to 1 so the Layer can be properly disposed if its dispose() method\n          // is called.\n          this._refCount = 1;\n        }\n      }\n\n      /*\n        Throw exceptions in case the input is not compatible\n        with the inputSpec set at build time.\n      */\n      this.assertInputCompatibility(inputs);\n\n      // Handle mask propagation.\n      // TODO(michaelterry): Mask propagation not currently implemented.\n\n      // Actually call the layer, collecting output(s), mask(s), and shape(s).\n      if (noneAreSymbolic) {\n        let output = this.call(inputs, kwargs);\n\n        // Apply masks to the output tensors if the layer supports it.\n        if (this.supportsMasking) {\n          // TODO(mattsoulanille): pass the input tensors' masks to computeMask\n          this.setMaskMetadata(inputs, output);\n        }\n\n        // If the layer returns tensors from its inputs, unmodified,\n        // we copy them to avoid loss of tensor metadata.\n        const outputList: Tensor[] = generic_utils.toList(output);\n        const outputListCopy: Tensor[] = [];\n        // TODO(michaelterry): This copying may not be necessary given our eager\n        // backend.\n        for (let x of outputList) {\n          if (inputsList.indexOf(x) !== -1) {\n            x = x.clone();\n          }\n          outputListCopy.push(x);\n        }\n        output = generic_utils.singletonOrArray(outputListCopy);\n\n        if (this.activityRegularizer != null) {\n          throw new NotImplementedError(\n              'Layer invocation in the presence of activity ' +\n              'regularizer(s) is not supported yet.');\n        }\n\n        // TODO(michaelterry): Call addInboundNode()?\n        return output;\n      } else {\n        const inputShape = collectInputShape(inputs);\n        const outputShape = this.computeOutputShape(inputShape);\n        let output: SymbolicTensor|SymbolicTensor[];\n        const outputDType = guessOutputDType(inputs);\n        this.warnOnIncompatibleInputShape(\n            Array.isArray(inputs) ? inputShape[0] as Shape :\n                                    inputShape as Shape);\n\n        if (outputShape != null && outputShape.length > 0 &&\n            Array.isArray(outputShape[0])) {\n          // We have multiple output shapes. Create multiple output tensors.\n          output = (outputShape as Shape[])\n                       .map(\n                           (shape, index) => new SymbolicTensor(\n                               outputDType, shape, this,\n                               generic_utils.toList(inputs), kwargs, this.name,\n                               index));\n        } else {\n          output = new SymbolicTensor(\n              outputDType, outputShape as Shape, this,\n              generic_utils.toList(inputs), kwargs, this.name);\n        }\n\n        /*\n          Add an inbound node to the layer, so that it keeps track\n          of the call and of all new variables created during the call.\n          This also updates the layer history of the output tensor(s).\n          If the input tensor(s) had no previous history,\n          this does nothing.\n        */\n        this.addInboundNode(\n            inputs, output, null, null, inputShape, outputShape, kwargs);\n        this._refCount++;\n\n        if (this.activityRegularizer != null) {\n          throw new NotImplementedError(\n              'Layer invocation in the presence of activity ' +\n              'regularizer(s) is not supported yet.');\n        }\n\n        return output;\n      }\n    });\n  }\n\n  /**\n   * Check compatibility between input shape and this layer's batchInputShape.\n   *\n   * Print warning if any incompatibility is found.\n   *\n   * @param inputShape Input shape to be checked.\n   */\n  protected warnOnIncompatibleInputShape(inputShape: Shape) {\n    if (this.batchInputShape == null) {\n      return;\n    } else if (inputShape.length !== this.batchInputShape.length) {\n      console.warn(\n          `The rank of the input tensor provided (shape: ` +\n          `${JSON.stringify(inputShape)}) does not match that of the ` +\n          `batchInputShape (${JSON.stringify(this.batchInputShape)}) ` +\n          `of the layer ${this.name}`);\n    } else {\n      let dimMismatch = false;\n      this.batchInputShape.forEach((dimension, i) => {\n        if (dimension != null && inputShape[i] != null &&\n            inputShape[i] !== dimension) {\n          dimMismatch = true;\n        }\n      });\n      if (dimMismatch) {\n        console.warn(\n            `The shape of the input tensor ` +\n            `(${JSON.stringify(inputShape)}) does not ` +\n            `match the expectation of layer ${this.name}: ` +\n            `${JSON.stringify(this.batchInputShape)}`);\n      }\n    }\n  }\n\n  /**\n   * Retrieves the output shape(s) of a layer.\n   *\n   * Only applicable if the layer has only one inbound node, or if all inbound\n   * nodes have the same output shape.\n   *\n   * @returns Output shape or shapes.\n   * @throws AttributeError: if the layer is connected to more than one incoming\n   *   nodes.\n   *\n   * @doc {heading: 'Models', 'subheading': 'Classes'}\n   */\n  get outputShape(): Shape|Shape[] {\n    if (this.inboundNodes == null || this.inboundNodes.length === 0) {\n      throw new AttributeError(\n          `The layer ${this.name} has never been called and thus has no ` +\n          `defined output shape.`);\n    }\n    const allOutputShapes: string[] = [];\n    for (const node of this.inboundNodes) {\n      const shapeString = JSON.stringify(node.outputShapes);\n      if (allOutputShapes.indexOf(shapeString) === -1) {\n        allOutputShapes.push(shapeString);\n      }\n    }\n    if (allOutputShapes.length === 1) {\n      const outputShapes = this.inboundNodes[0].outputShapes;\n      if (Array.isArray(outputShapes) && Array.isArray(outputShapes[0]) &&\n          outputShapes.length === 1) {\n        return (outputShapes as Shape[])[0];\n      } else {\n        return outputShapes;\n      }\n\n    } else {\n      throw new AttributeError(\n          `The layer ${this.name} has multiple inbound nodes with different ` +\n          `output shapes. Hence the notion of \"output shape\" is ill-defined ` +\n          `for the layer.`);\n      // TODO(cais): Implement getOutputShapeAt().\n    }\n  }\n\n  /**\n   * Counts the total number of numbers (e.g., float32, int32) in the\n   * weights.\n   *\n   * @returns An integer count.\n   * @throws RuntimeError: If the layer is not built yet (in which case its\n   *   weights are not defined yet.)\n   *\n   * @doc {heading: 'Models', 'subheading': 'Classes'}\n   */\n  countParams(): number {\n    if (!this.built) {\n      throw new RuntimeError(\n          `You tried to call countParams() on ${this.name}, ` +\n          `but the layer is not built yet. Build it first by calling ` +\n          `build(batchInputShape).`);\n    }\n    return variable_utils.countParamsInWeights(this.weights);\n  }\n\n  /**\n   * Creates the layer weights.\n   *\n   * Must be implemented on all layers that have weights.\n   *\n   * Called when apply() is called to construct the weights.\n   *\n   * @param inputShape A `Shape` or array of `Shape` (unused).\n   *\n   * @doc {heading: 'Models', 'subheading': 'Classes'}\n   */\n  build(inputShape: Shape|Shape[]) {\n    this.built = true;\n  }\n\n  /**\n   * Returns the current values of the weights of the layer.\n   *\n   * @param trainableOnly Whether to get the values of only trainable weights.\n   * @returns Weight values as an `Array` of `tf.Tensor`s.\n   *\n   * @doc {heading: 'Models', 'subheading': 'Classes'}\n   */\n  getWeights(trainableOnly = false): Tensor[] {\n    return batchGetValue(trainableOnly ? this.trainableWeights : this.weights);\n  }\n\n  /**\n   * Sets the weights of the layer, from Tensors.\n   *\n   * @param weights a list of Tensors. The number of arrays and their shape\n   *   must match number of the dimensions of the weights of the layer (i.e.\n   *   it should match the output of `getWeights`).\n   *\n   * @exception ValueError If the provided weights list does not match the\n   *   layer's specifications.\n   *\n   * @doc {heading: 'Models', 'subheading': 'Classes'}\n   */\n  setWeights(weights: Tensor[]): void {\n    tidy(() => {\n      const params = this.weights;\n      if (params.length !== weights.length) {\n        // TODO(cais): Restore the following and use `providedWeights`, instead\n        // of `weights` in the error message, once the deeplearn.js bug is\n        // fixed: https://github.com/PAIR-code/deeplearnjs/issues/498 const\n        // providedWeights = JSON.stringify(weights).slice(0, 50);\n        throw new ValueError(\n            `You called setWeights(weights) on layer \"${this.name}\" ` +\n            `with a weight list of length ${weights.length}, ` +\n            `but the layer was expecting ${params.length} weights. ` +\n            `Provided weights: ${weights}...`);\n      }\n      if (params.length === 0) {\n        return;\n      }\n      const weightValueTuples: Array<[LayerVariable, Tensor]> = [];\n      const paramValues = batchGetValue(params);\n      for (let i = 0; i < paramValues.length; ++i) {\n        const pv = paramValues[i];\n        const p = params[i];\n        const w = weights[i];\n        if (!util.arraysEqual(pv.shape, w.shape)) {\n          throw new ValueError(\n              `Layer weight shape ${pv.shape} ` +\n              `not compatible with provided weight shape ${w.shape}`);\n        }\n        weightValueTuples.push([p, w]);\n      }\n      batchSetValue(weightValueTuples);\n    });\n  }\n\n  /**\n   * Adds a weight variable to the layer.\n   *\n   * @param name Name of the new weight variable.\n   * @param shape The shape of the weight.\n   * @param dtype The dtype of the weight.\n   * @param initializer An initializer instance.\n   * @param regularizer A regularizer instance.\n   * @param trainable Whether the weight should be trained via backprop or not\n   *   (assuming that the layer itself is also trainable).\n   * @param constraint An optional trainable.\n   * @return The created weight variable.\n   *\n   * @doc {heading: 'Models', 'subheading': 'Classes'}\n   */\n  protected addWeight(\n      name: string, shape: Shape, dtype?: DataType, initializer?: Initializer,\n      regularizer?: Regularizer, trainable?: boolean, constraint?: Constraint,\n      getInitializerFunc?: Function): LayerVariable {\n    // Reject duplicate weight names.\n    if (this._addedWeightNames.indexOf(name) !== -1) {\n      throw new ValueError(\n          `Duplicate weight name ${name} for layer ${this.name}`);\n    }\n    this._addedWeightNames.push(name);\n\n    if (dtype == null) {\n      dtype = 'float32';\n    }\n\n    if (this.fastWeightInitDuringBuild) {\n      initializer = getInitializerFunc != null ? getInitializerFunc() :\n                                                 getInitializer('zeros');\n    }\n    const initValue = initializer.apply(shape, dtype);\n    const weight =\n        new LayerVariable(initValue, dtype, name, trainable, constraint);\n    initValue.dispose();\n    // Request backend not to dispose the weights of the model on scope() exit.\n    if (regularizer != null) {\n      this.addLoss(() => regularizer.apply(weight.read()));\n    }\n    if (trainable == null) {\n      trainable = true;\n    }\n    if (trainable) {\n      this._trainableWeights.push(weight);\n    } else {\n      this._nonTrainableWeights.push(weight);\n    }\n    return weight;\n  }\n\n  /**\n   * Set the fast-weight-initialization flag.\n   *\n   * In cases where the initialized weight values will be immediately\n   * overwritten by loaded weight values during model loading, setting\n   * the flag to `true` saves unnecessary calls to potentially expensive\n   * initializers and speeds up the loading process.\n   *\n   * @param value Target value of the flag.\n   */\n  setFastWeightInitDuringBuild(value: boolean) {\n    this.fastWeightInitDuringBuild = value;\n  }\n\n  /**\n   * Add losses to the layer.\n   *\n   * The loss may potentially be conditional on some inputs tensors,\n   * for instance activity losses are conditional on the layer's inputs.\n   *\n   * @doc {heading: 'Models', 'subheading': 'Classes'}\n   */\n  addLoss(losses: RegularizerFn|RegularizerFn[]): void {\n    if (losses == null || Array.isArray(losses) && losses.length === 0) {\n      return;\n    }\n    // Update this.losses\n    losses = generic_utils.toList(losses);\n    if (this._losses !== undefined && this._losses !== null) {\n      this.losses.push(...losses);\n    }\n  }\n\n  /**\n   * Computes the output shape of the layer.\n   *\n   * Assumes that the layer will be built to match that input shape provided.\n   *\n   * @param inputShape A shape (tuple of integers) or a list of shape tuples\n   *   (one per output tensor of the layer). Shape tuples can include null for\n   *   free dimensions, instead of an integer.\n   *\n   * @doc {heading: 'Models', 'subheading': 'Classes'}\n   */\n  computeOutputShape(inputShape: Shape|Shape[]): Shape|Shape[] {\n    return inputShape;\n  }\n\n  /**\n   * Computes an output mask tensor.\n   *\n   * @param inputs Tensor or list of tensors.\n   * @param mask Tensor or list of tensors.\n   *\n   * @return null or a tensor (or list of tensors, one per output tensor of the\n   * layer).\n   */\n  computeMask(inputs: Tensor|Tensor[], mask?: Tensor|Tensor[]): Tensor\n      |Tensor[] {\n    if (!this.supportsMasking) {\n      if (mask != null) {\n        if (Array.isArray(mask)) {\n          mask.forEach(maskElement => {\n            if (maskElement != null) {\n              throw new TypeError(\n                  `Layer ${this.name} does not support masking, ` +\n                  'but was passed an inputMask.');\n            }\n          });\n        } else {\n          throw new TypeError(\n              `Layer ${this.name} does not support masking, ` +\n              'but was passed an inputMask.');\n        }\n      }\n      // masking not explicitly supported: return null as mask\n      return null;\n    }\n    // if masking is explictly supported, by default\n    // carry over the input mask\n    return mask;\n  }\n\n  private setMaskMetadata(\n      inputs: Tensor|Tensor[], outputs: Tensor|Tensor[],\n      previousMask?: Tensor|Tensor[]): void {\n    if (!this.supportsMasking) {\n      return;\n    }\n\n    const outputMasks = this.computeMask(inputs, previousMask);\n    const outputsList = generic_utils.toList(outputs);\n    const outputMasksList = generic_utils.toList(outputMasks);\n\n    if (outputsList.length !== outputMasksList.length) {\n      throw new Error(\n          `${this.name} outputs ${outputsList.length} tensors ` +\n          `but ${outputsList.length} masks for those tensors`);\n    }\n    for (let i = 0; i < outputsList.length; i++) {\n      outputsList[i].kerasMask = outputMasksList[i];\n    }\n  }\n\n  /**\n   * Internal method to create an inbound node for the layer.\n   *\n   * @param inputTensors List of input tensors.\n   * @param outputTensors List of output tensors.\n   * @param inputMasks List of input masks (a mask can be a tensor, or null).\n   * @param outputMasks List of output masks (a mask can be a tensor, or null).\n   * @param inputShapes List of input shape tuples.\n   * @param outputShapes List of output shape tuples.\n   * @param kwargs Dictionary of keyword arguments that were passed to the\n   *   `call` method of the layer at the call that created the node.\n   */\n  private addInboundNode(\n      inputTensors: SymbolicTensor|SymbolicTensor[],\n      outputTensors: SymbolicTensor|SymbolicTensor[],\n      inputMasks: Tensor|Tensor[], outputMasks: Tensor|Tensor[],\n      inputShapes: Shape|Shape[], outputShapes: Shape|Shape[],\n      kwargs: {} = null): void {\n    const inputTensorList: SymbolicTensor[] =\n        generic_utils.toList(inputTensors);\n    outputTensors = generic_utils.toList(outputTensors);\n    inputMasks = generic_utils.toList(inputMasks);\n    outputMasks = generic_utils.toList(outputMasks);\n    inputShapes = types_utils.normalizeShapeList(inputShapes);\n    outputShapes = types_utils.normalizeShapeList(outputShapes);\n\n    // Collect input tensor(s) coordinates.\n    const inboundLayers: Layer[] = [];\n    const nodeIndices: number[] = [];\n    const tensorIndices: number[] = [];\n    for (const x of inputTensorList) {\n      /*\n       * TODO(michaelterry): Keras adds this value to tensors; it's not\n       * clear whether we'll use this or not.\n       */\n      inboundLayers.push(x.sourceLayer);\n      nodeIndices.push(x.nodeIndex);\n      tensorIndices.push(x.tensorIndex);\n    }\n\n    // Create node, add it to inbound nodes.\n    // (This call has side effects.)\n    // tslint:disable-next-line:no-unused-expression\n    new Node(\n        {\n          outboundLayer: this,\n          inboundLayers,\n          nodeIndices,\n          tensorIndices,\n          inputTensors: inputTensorList,\n          outputTensors,\n          inputMasks,\n          outputMasks,\n          inputShapes,\n          outputShapes\n        },\n        kwargs);\n\n    // Update tensor history\n    for (let i = 0; i < outputTensors.length; i++) {\n      // TODO(michaelterry: _uses_learning_phase not tracked.\n      outputTensors[i].sourceLayer = this;\n      outputTensors[i].nodeIndex = this.inboundNodes.length - 1;\n      outputTensors[i].tensorIndex = i;\n    }\n  }\n\n  /**\n   * Returns the config of the layer.\n   *\n   * A layer config is a TS dictionary (serializable)\n   * containing the configuration of a layer.\n   * The same layer can be reinstantiated later\n   * (without its trained weights) from this configuration.\n   *\n   * The config of a layer does not include connectivity\n   * information, nor the layer class name.  These are handled\n   * by 'Container' (one layer of abstraction above).\n   *\n   * Porting Note: The TS dictionary follows TS naming standards for\n   * keys, and uses tfjs-layers type-safe Enums.  Serialization methods\n   * should use a helper function to convert to the pythonic storage\n   * standard. (see serialization_utils.convertTsToPythonic)\n   *\n   * @returns TS dictionary of configuration.\n   *\n   * @doc {heading: 'Models', 'subheading': 'Classes'}\n   */\n  getConfig(): serialization.ConfigDict {\n    const config:\n        serialization.ConfigDict = {name: this.name, trainable: this.trainable};\n    if (this.batchInputShape != null) {\n      config['batchInputShape'] = this.batchInputShape;\n    }\n    if (this.dtype != null) {\n      config['dtype'] = this.dtype;\n    }\n    return config;\n  }\n\n  /**\n   * Dispose the weight variables that this Layer instance holds.\n   *\n   * @returns {number} Number of disposed variables.\n   */\n  protected disposeWeights(): number {\n    this.weights.forEach(weight => weight.dispose());\n    return this.weights.length;\n  }\n\n  protected assertNotDisposed() {\n    if (this._refCount === 0) {\n      throw new Error(`Layer '${this.name}' is already disposed.`);\n    }\n  }\n\n  /**\n   * Attempt to dispose layer's weights.\n   *\n   * This method decreases the reference count of the Layer object by 1.\n   *\n   * A Layer is reference-counted. Its reference count is incremented by 1\n   * the first item its `apply()` method is called and when it becomes a part\n   * of a new `Node` (through calling the `apply()` method on a\n   * `tf.SymbolicTensor`).\n   *\n   * If the reference count of a Layer becomes 0, all the weights will be\n   * disposed and the underlying memory (e.g., the textures allocated in WebGL)\n   * will be freed.\n   *\n   * Note: If the reference count is greater than 0 after the decrement, the\n   * weights of the Layer will *not* be disposed.\n   *\n   * After a Layer is disposed, it cannot be used in calls such as `apply()`,\n   * `getWeights()` or `setWeights()` anymore.\n   *\n   * @returns A DisposeResult Object with the following fields:\n   *   - refCountAfterDispose: The reference count of the Container after this\n   *     `dispose()` call.\n   *   - numDisposedVariables: Number of `tf.Variable`s (i.e., weights) disposed\n   *     during this `dispose()` call.\n   * @throws {Error} If the layer is not built yet, or if the layer has already\n   *   been disposed.\n   *\n   * @doc {heading: 'Models', 'subheading': 'Classes'}\n   */\n  dispose(): DisposeResult {\n    if (!this.built) {\n      throw new Error(\n          `Cannot dispose Layer ${this.name} because it has not been ` +\n          `built yet.`);\n    }\n\n    if (this._refCount === null) {\n      throw new Error(\n          `Cannot dispose Layer ${this.name} because it has not been used ` +\n          `yet.`);\n    }\n\n    this.assertNotDisposed();\n\n    let numDisposedVariables = 0;\n    if (--this._refCount === 0) {\n      numDisposedVariables = this.disposeWeights();\n    }\n\n    return {refCountAfterDispose: this._refCount, numDisposedVariables};\n  }\n}\n\n/**\n * Collects the input shape(s) of a list of `tf.Tensor`s or\n * `tf.SymbolicTensor`s.\n *\n * TODO(michaelterry): Update PyKeras docs (backport).\n *\n * @param inputTensors List of input tensors (or single input tensor).\n *\n * @return List of shape tuples (or single tuple), one tuple per input.\n */\nfunction collectInputShape(inputTensors: SymbolicTensor|SymbolicTensor[]|Tensor|\n                           Tensor[]): Shape|Shape[] {\n  inputTensors =\n      generic_utils.toList(inputTensors) as SymbolicTensor[] | Tensor[];\n  const shapes: Shape[] = [];\n  for (const x of inputTensors) {\n    shapes.push(x.shape);\n  }\n  return generic_utils.singletonOrArray(shapes);\n}\n\n/**\n * Guesses output dtype based on inputs.\n *\n * At present, just returns 'float32' for any input.\n *\n * @param inputTensors List of input tensors (or single input tensor).\n *\n * @return The guessed DType. At present, always returns 'float32'.\n */\nfunction guessOutputDType(inputTensors: SymbolicTensor|SymbolicTensor[]|Tensor|\n                          Tensor[]): DataType {\n  return 'float32';\n}\n\n/**\n * Returns the list of input tensors necessary to compute `tensor`.\n *\n * Output will always be a list of tensors (potentially with 1 element).\n *\n * @param tensor The tensor to start from.\n * @param layer Origin layer of the tensor.\n * @param nodeIndex Origin node index of the tensor.\n *\n * @return Array of input tensors.\n */\nexport function getSourceInputs(\n    tensor: SymbolicTensor, layer?: Layer,\n    nodeIndex?: number): SymbolicTensor[] {\n  if (layer == null || (nodeIndex != null && nodeIndex > 0)) {\n    layer = tensor.sourceLayer;\n    nodeIndex = tensor.nodeIndex;\n  }\n  if (layer.inboundNodes.length === 0) {\n    return [tensor];\n  } else {\n    const node = layer.inboundNodes[nodeIndex];\n    if (node.inboundLayers.length === 0) {\n      return node.inputTensors;\n    } else {\n      const sourceTensors: SymbolicTensor[] = [];\n      for (let i = 0; i < node.inboundLayers.length; i++) {\n        const x = node.inputTensors[i];\n        const layer = node.inboundLayers[i];\n        const nodeIndex = node.nodeIndices[i];\n        const previousSources = getSourceInputs(x, layer, nodeIndex);\n        // Avoid input redundancy.\n        for (const x of previousSources) {\n          if (sourceTensors.indexOf(x) === -1) {\n            sourceTensors.push(x);\n          }\n        }\n      }\n      return sourceTensors;\n    }\n  }\n}\n\ntype MaybeSymbolic = SymbolicTensor|Tensor;\n\nfunction checkAllSymbolic(tensors: MaybeSymbolic|MaybeSymbolic[]):\n    tensors is SymbolicTensor|SymbolicTensor[] {\n  let allAreSymbolic = true;\n  for (const tensor of generic_utils.toList(tensors)) {\n    if (!(tensor instanceof SymbolicTensor)) {\n      allAreSymbolic = false;\n      break;\n    }\n  }\n  return allAreSymbolic;\n}\n\nfunction checkNoneSymbolic(tensors: MaybeSymbolic|\n                           MaybeSymbolic[]): tensors is Tensor|Tensor[] {\n  let noneAreSymbolic = true;\n  for (const tensor of generic_utils.toList(tensors)) {\n    if (tensor instanceof SymbolicTensor) {\n      noneAreSymbolic = false;\n      break;\n    }\n  }\n  return noneAreSymbolic;\n}\n"], "mappings": "AAAA;;;;;;;;;AAUA;AAEA,SAA0BA,aAAa,EAAUC,IAAI,EAAEC,IAAI,QAAO,uBAAuB;AAEzF,SAAQC,qBAAqB,EAAEC,MAAM,QAAO,kBAAkB;AAC9D,SAAQC,mBAAmB,EAAEC,mBAAmB,EAAEC,SAAS,QAAO,WAAW;AAE7E,SAAQC,cAAc,EAAEC,mBAAmB,EAAEC,YAAY,EAAEC,UAAU,QAAO,WAAW;AACvF,SAAQC,cAAc,QAAoB,iBAAiB;AAI3D,OAAO,KAAKC,aAAa,MAAM,wBAAwB;AACvD,OAAO,KAAKC,WAAW,MAAM,sBAAsB;AACnD,OAAO,KAAKC,cAAc,MAAM,yBAAyB;AACzD,SAAQC,aAAa,EAAEC,aAAa,EAAEC,aAAa,QAAO,cAAc;AAuBxE;;;;;;;;;AASA,OAAM,MAAOC,SAAS;EAcpBC,YAAYC,IAAmB;IAC7B,IAAI,CAACC,KAAK,GAAGD,IAAI,CAACC,KAAK;IACvB,IAAI,CAACC,KAAK,GAAGF,IAAI,CAACE,KAAK;IACvB;;;;IAIA,IAAIF,IAAI,CAACE,KAAK,IAAI,IAAI,EAAE;MACtB,IAAI,CAACC,IAAI,GAAGH,IAAI,CAACE,KAAK,CAACE,MAAM;KAC9B,MAAM;MACL,IAAI,CAACD,IAAI,GAAGH,IAAI,CAACG,IAAI;;IAEvB,IAAI,CAACE,OAAO,GAAGL,IAAI,CAACK,OAAO;IAC3B,IAAI,CAACC,OAAO,GAAGN,IAAI,CAACM,OAAO;IAC3B,IAAI,CAACC,IAAI,GAAGP,IAAI,CAACO,IAAI,IAAI,EAAE;EAC7B;;AAGF;;;;;;;;AAQA,OAAM,MAAOC,cAAc;EAsBzB;;;;;;;;;;;;;EAaAT,YACaE,KAAe,EAAWC,KAAY,EACxCO,WAAkB,EAAWC,MAAwB,EACnDC,QAAgB,EAAEC,IAAa,EAC/BC,iBAA0B;IAH1B,KAAAZ,KAAK,GAALA,KAAK;IAAqB,KAAAC,KAAK,GAALA,KAAK;IACjC,KAAAO,WAAW,GAAXA,WAAW;IAAkB,KAAAC,MAAM,GAANA,MAAM;IACjC,KAAAC,QAAQ,GAARA,QAAQ;IACR,KAAAE,iBAAiB,GAAjBA,iBAAiB;IAC5B,IAAI,CAACC,EAAE,GAAGhC,qBAAqB,EAAE;IACjC,IAAI8B,IAAI,IAAI,IAAI,EAAE;MAChB,IAAI,CAACG,YAAY,GAAG/B,mBAAmB,CAAC4B,IAAI,CAAC;MAC7C,IAAI,CAACA,IAAI,GAAG3B,mBAAmB,CAAC,IAAI,CAAC8B,YAAY,CAAC;;IAEpD,IAAI,CAACC,IAAI,GAAGd,KAAK,CAACE,MAAM;EAC1B;;AA4DF,IAAIa,WAAW,GAAG,CAAC;AAEnB;;;;;;;;;;;;;;;;;;;;AAoBA,OAAM,MAAOC,IAAI;EAwCfnB,YACIC,IAAc;EACd;EACOW,QAAiB;IAAjB,KAAAA,QAAQ,GAARA,QAAQ;IACjB,IAAI,CAACG,EAAE,GAAGG,WAAW,EAAE;IACvB;;;;;;;IAOA,IAAI,CAACE,aAAa,GAAGnB,IAAI,CAACmB,aAAa;IAEvC;;;;;;IAOA;IACA,IAAI,CAACC,aAAa,GAAGpB,IAAI,CAACoB,aAAa;IACvC;IACA,IAAI,CAACC,WAAW,GAAGrB,IAAI,CAACqB,WAAW;IACnC;IACA,IAAI,CAACC,aAAa,GAAGtB,IAAI,CAACsB,aAAa;IAEvC;;;;IAKA;IACA,IAAI,CAACC,YAAY,GAAGvB,IAAI,CAACuB,YAAY;IACrC;IACA,IAAI,CAACC,aAAa,GAAGxB,IAAI,CAACwB,aAAa;IAEvC;;;;IAIA,IAAI,CAACC,UAAU,GAAGzB,IAAI,CAACyB,UAAU;IACjC;IACA,IAAI,CAACC,WAAW,GAAG1B,IAAI,CAAC0B,WAAW;IAEnC;IAEA;IACA,IAAI,CAACC,WAAW,GAAG3B,IAAI,CAAC2B,WAAW;IACnC;IACA,IAAI,CAACC,YAAY,GAAG5B,IAAI,CAAC4B,YAAY;IAErC;IACA,KAAK,MAAMC,KAAK,IAAI7B,IAAI,CAACoB,aAAa,EAAE;MACtC,IAAIS,KAAK,IAAI,IAAI,EAAE;QACjBA,KAAK,CAACC,aAAa,CAACC,IAAI,CAAC,IAAI,CAAC;;;IAGlC/B,IAAI,CAACmB,aAAa,CAACa,YAAY,CAACD,IAAI,CAAC,IAAI,CAAC;EAC5C;EAEAE,SAASA,CAAA;IACP,MAAMC,YAAY,GAAa,EAAE;IACjC,KAAK,MAAML,KAAK,IAAI,IAAI,CAACT,aAAa,EAAE;MACtC,IAAIS,KAAK,IAAI,IAAI,EAAE;QACjBK,YAAY,CAACH,IAAI,CAACF,KAAK,CAACjB,IAAI,CAAC;OAC9B,MAAM;QACLsB,YAAY,CAACH,IAAI,CAAC,IAAI,CAAC;;;IAG3B,OAAO;MACLZ,aAAa,EAAE,IAAI,CAACA,aAAa,GAAG,IAAI,CAACA,aAAa,CAACP,IAAI,GAAG,IAAI;MAClEQ,aAAa,EAAEc,YAAY;MAC3Bb,WAAW,EAAE,IAAI,CAACA,WAAW;MAC7BC,aAAa,EAAE,IAAI,CAACA;KACrB;EACH;;AAmDF,IAAIa,YAAY,GAAG,CAAC;AAEpB;;;;;;;;;AASA,OAAM,MAAgBC,KAAM,SAAQzD,aAAa,CAAC0D,YAAY;EAmD5DtC,YAAYC,IAAA,GAAkB,EAAE;IAC9B,KAAK,EAAE;IAtBD,KAAAsC,SAAS,GAAa,IAAI;IAE1B,KAAAC,iBAAiB,GAAa,EAAE;IAIxC;IACA;IACA;IACA;IACU,KAAAC,SAAS,GAAG,KAAK;IAazB,IAAI,CAAC1B,EAAE,GAAGqB,YAAY,EAAE;IAExB,IAAI,CAACM,mBAAmB,GAAG,IAAI;IAE/B,IAAI,CAACC,SAAS,GAAG,IAAI;IACrB,IAAI,CAACC,eAAe,GAAG,KAAK;IAE5B;IACA,IAAI,CAACC,iBAAiB,GAAG,EAAE;IAC3B,IAAI,CAACC,oBAAoB,GAAG,EAAE;IAC9B,IAAI,CAACC,OAAO,GAAG,EAAE;IACjB,IAAI,CAACC,QAAQ,GAAG,EAAE;IAClB,IAAI,CAACC,MAAM,GAAG,KAAK;IAEnB;;;;IAIA,IAAI,CAAChB,YAAY,GAAG,EAAE;IACtB,IAAI,CAACF,aAAa,GAAG,EAAE;IAEvB,IAAIlB,IAAI,GAAGZ,IAAI,CAACY,IAAI;IACpB,IAAI,CAACA,IAAI,EAAE;MACT,MAAMqC,MAAM,GAAG,IAAI,CAACC,YAAY,EAAE;MAClCtC,IAAI,GAAGpB,aAAa,CAAC2D,WAAW,CAACF,MAAM,CAAC,GAAG,GAAG,GAAGlE,MAAM,CAACkE,MAAM,CAAC;;IAEjE,IAAI,CAACrC,IAAI,GAAGA,IAAI;IAEhB,IAAI,CAACwC,UAAU,GAAGpD,IAAI,CAACqD,SAAS,IAAI,IAAI,GAAG,IAAI,GAAGrD,IAAI,CAACqD,SAAS;IAEhE,IAAIrD,IAAI,CAACsD,UAAU,IAAI,IAAI,IAAItD,IAAI,CAACuD,eAAe,IAAI,IAAI,EAAE;MAC3D;;;;MAIA,IAAIA,eAAsB;MAC1B,IAAIvD,IAAI,CAACuD,eAAe,IAAI,IAAI,EAAE;QAChCA,eAAe,GAAGvD,IAAI,CAACuD,eAAe;OACvC,MAAM,IAAIvD,IAAI,CAACsD,UAAU,IAAI,IAAI,EAAE;QAClC,IAAIE,SAAS,GAAW,IAAI;QAC5B,IAAIxD,IAAI,CAACwD,SAAS,IAAI,IAAI,EAAE;UAC1BA,SAAS,GAAGxD,IAAI,CAACwD,SAAS;;QAE5BD,eAAe,GAAG,CAACC,SAAS,CAAC,CAACC,MAAM,CAACzD,IAAI,CAACsD,UAAU,CAAC;;MAEvD,IAAI,CAACC,eAAe,GAAGA,eAAe;MAEtC;MACA,IAAItD,KAAK,GAAGD,IAAI,CAACC,KAAK;MACtB,IAAIA,KAAK,IAAI,IAAI,EAAE;QACjBA,KAAK,GAAGD,IAAI,CAAC0D,UAAU;;MAEzB,IAAIzD,KAAK,IAAI,IAAI,EAAE;QACjBA,KAAK,GAAG,SAAS;;MAEnB,IAAI,CAACA,KAAK,GAAGA,KAAK;;IAGpB,IAAID,IAAI,CAAC2D,OAAO,IAAI,IAAI,EAAE;MACxB,IAAI,CAACC,cAAc,GAAG5D,IAAI,CAAC2D,OAAO;KACnC,MAAM;MACL,IAAI,CAACC,cAAc,GAAG,IAAI;;IAG5B;IACA;IACA,IAAI,CAACC,SAAS,GAAG,IAAI;IAErB,IAAI,CAACC,yBAAyB,GAAG,KAAK;EACxC;EAEA;;;;;;;;;EASU,OAAOC,OAAOA,CAAClC,KAAY,EAAEmC,SAAiB;IACtD,OAAOnC,KAAK,CAACjB,IAAI,GAAG,MAAM,GAAGoD,SAAS,CAACC,QAAQ,EAAE;EACnD;EAEA;;;;;;;EAOQC,cAAcA,CAACF,SAAiB,EAAEG,QAAgB;IACxD,IAAI,IAAI,CAACnC,YAAY,CAAC5B,MAAM,KAAK,CAAC,EAAE;MAClC,MAAM,IAAIf,YAAY,CAClB,kCAAkC,GAClC,2BAA2B8E,QAAQ,GAAG,CAAC;;IAE7C,IAAI,IAAI,CAACnC,YAAY,CAAC5B,MAAM,IAAI4D,SAAS,EAAE;MACzC,MAAM,IAAI1E,UAAU,CAChB,gBAAgB6E,QAAQ,YAAYH,SAAS,IAAI,GACjD,0BAA0B,IAAI,CAAChC,YAAY,CAAC5B,MAAM,iBAAiB,CAAC;;IAE1E,OAAO,IAAI,CAAC4B,YAAY,CAACgC,SAAS,CAAC;EACrC;EAEA;;;;;;;;;EASAI,UAAUA,CAACJ,SAAiB;IAC1B,OAAOxE,aAAa,CAAC6E,gBAAgB,CACjC,IAAI,CAACH,cAAc,CAACF,SAAS,EAAE,OAAO,CAAC,CAACzC,YAAY,CAAC;EAC3D;EAEA;;;;;;;;;EASA+C,WAAWA,CAACN,SAAiB;IAC3B,OAAOxE,aAAa,CAAC6E,gBAAgB,CACjC,IAAI,CAACH,cAAc,CAACF,SAAS,EAAE,QAAQ,CAAC,CAACxC,aAAa,CAAC;EAC7D;EAEA;EAEA;;;;;;;;;;;EAWA,IAAI+C,KAAKA,CAAA;IACP,IAAI,IAAI,CAACvC,YAAY,CAAC5B,MAAM,GAAG,CAAC,EAAE;MAChC,MAAM,IAAIjB,cAAc,CACpB,SAAS,IAAI,CAACyB,IAAI,EAAE,GACpB,+BAA+B,GAC/B,oCAAoC,GACpC,kBAAkB,GAClB,sCAAsC,CAAC;KAC5C,MAAM,IAAI,IAAI,CAACoB,YAAY,CAAC5B,MAAM,KAAK,CAAC,EAAE;MACzC,MAAM,IAAIjB,cAAc,CACpB,SAAS,IAAI,CAACyB,IAAI,EAAE,GACpB,wCAAwC,CAAC;;IAE/C,OAAOpB,aAAa,CAAC6E,gBAAgB,CACjC,IAAI,CAACH,cAAc,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC3C,YAAY,CAAC;EACnD;EAEA;;;;;;;;;;;EAWA,IAAIiD,MAAMA,CAAA;IACR,IAAI,IAAI,CAACxC,YAAY,CAAC5B,MAAM,KAAK,CAAC,EAAE;MAClC,MAAM,IAAIjB,cAAc,CACpB,SAAS,IAAI,CAACyB,IAAI,EAAE,GACpB,wBAAwB,CAAC;;IAE/B,IAAI,IAAI,CAACoB,YAAY,CAAC5B,MAAM,GAAG,CAAC,EAAE;MAChC,MAAM,IAAIjB,cAAc,CACpB,SAAS,IAAI,CAACyB,IAAI,EAAE,GACpB,+BAA+B,GAC/B,qCAAqC,GACrC,kBAAkB,GAClB,uCAAuC,CAAC;;IAE9C,OAAOpB,aAAa,CAAC6E,gBAAgB,CACjC,IAAI,CAACH,cAAc,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC1C,aAAa,CAAC;EACrD;EAEA,IAAIiD,MAAMA,CAAA;IACR,OAAO,IAAI,CAAC3B,OAAO;EACrB;EAEA;;;;;EAKA4B,eAAeA,CAAA;IACb;IACA;IACA;IACA;IACA,OAAO,IAAI,CAACD,MAAM,CAACE,GAAG,CAACC,MAAM,IAAIA,MAAM,EAAE,CAAC;EAC5C;EAEA,IAAIC,OAAOA,CAAA;IACT,OAAO,IAAI,CAAC9B,QAAQ;EACtB;EAEA,IAAI+B,KAAKA,CAAA;IACP,OAAO,IAAI,CAAC9B,MAAM;EACpB;EAEA,IAAI8B,KAAKA,CAACA,KAAc;IACtB,IAAI,CAAC9B,MAAM,GAAG8B,KAAK;EACrB;EAEA,IAAIzB,SAASA,CAAA;IACX,OAAO,IAAI,CAACD,UAAU;EACxB;EAEA,IAAIC,SAASA,CAACA,SAAkB;IAC9B,IAAI,CAACT,iBAAiB,CAACmC,OAAO,CAACC,CAAC,IAAIA,CAAC,CAAC3B,SAAS,GAAGA,SAAS,CAAC;IAC5D,IAAI,CAACD,UAAU,GAAGC,SAAS;EAC7B;EAEA,IAAI4B,gBAAgBA,CAAA;IAClB,IAAI,IAAI,CAAC7B,UAAU,EAAE;MACnB,OAAO,IAAI,CAACR,iBAAiB,CAACsC,MAAM,CAACF,CAAC,IAAIA,CAAC,CAAC3B,SAAS,CAAC;KACvD,MAAM;MACL,OAAO,EAAE;;EAEb;EAEA,IAAI4B,gBAAgBA,CAACtB,OAAwB;IAC3C,IAAI,CAACf,iBAAiB,GAAGe,OAAO;EAClC;EAEA,IAAIwB,mBAAmBA,CAAA;IACrB,IAAI,IAAI,CAAC9B,SAAS,EAAE;MAClB,OAAO,IAAI,CAACT,iBAAiB,CAACsC,MAAM,CAACF,CAAC,IAAI,CAACA,CAAC,CAAC3B,SAAS,CAAC,CAClDI,MAAM,CAAC,IAAI,CAACZ,oBAAoB,CAAC;KACvC,MAAM;MACL,OAAO,IAAI,CAACD,iBAAiB,CAACa,MAAM,CAAC,IAAI,CAACZ,oBAAoB,CAAC;;EAEnE;EAEA,IAAIsC,mBAAmBA,CAACxB,OAAwB;IAC9C,IAAI,CAACd,oBAAoB,GAAGc,OAAO;EACrC;EAEA;;;;EAIA,IAAIA,OAAOA,CAAA;IACT,OAAO,IAAI,CAACsB,gBAAgB,CAACxB,MAAM,CAAC,IAAI,CAAC0B,mBAAmB,CAAC;EAC/D;EAEA,IAAIC,QAAQA,CAAA;IACV,OAAO,IAAI,CAAC5C,SAAS;EACvB;EAEA;;;;;;;EAOA6C,WAAWA,CAAA;IACT,IAAI,CAAC,IAAI,CAACD,QAAQ,EAAE;MAClB,MAAM,IAAIE,KAAK,CACX,+DAA+D,GAC/D,SAAS,CAAC;;EAElB;EAEA;;;;;;;;;;;;EAYUC,wBAAwBA,CAAC7E,MACgB;IACjD,MAAM8E,UAAU,GAAGhG,aAAa,CAACiG,MAAM,CAAC/E,MAAM,CAAC;IAC/C,IAAI,IAAI,CAACgC,SAAS,IAAI,IAAI,IAAI,IAAI,CAACA,SAAS,CAACtC,MAAM,KAAK,CAAC,EAAE;MACzD;;IAEF,MAAMsC,SAAS,GAAGlD,aAAa,CAACiG,MAAM,CAAC,IAAI,CAAC/C,SAAS,CAAC;IACtD,IAAI8C,UAAU,CAACpF,MAAM,KAAKsC,SAAS,CAACtC,MAAM,EAAE;MAC1C,MAAM,IAAId,UAAU,CAChB,SAAS,IAAI,CAACsB,IAAI,YAAY8B,SAAS,CAACtC,MAAM,WAAW,GACzD,mBAAmBoF,UAAU,CAACpF,MAAM,kBAAkB,GACtD,mBAAmBM,MAAM,EAAE,CAAC;;IAElC,KAAK,IAAIgF,UAAU,GAAG,CAAC,EAAEA,UAAU,GAAGF,UAAU,CAACpF,MAAM,EAAEsF,UAAU,EAAE,EAAE;MACrE,MAAMC,CAAC,GAAGH,UAAU,CAACE,UAAU,CAAC;MAChC,MAAME,IAAI,GAAclD,SAAS,CAACgD,UAAU,CAAC;MAC7C,IAAIE,IAAI,IAAI,IAAI,EAAE;QAChB;;MAGF;MACA,MAAMzF,IAAI,GAAGwF,CAAC,CAAC3E,IAAI;MACnB,IAAI4E,IAAI,CAACzF,IAAI,IAAI,IAAI,EAAE;QACrB,IAAIA,IAAI,KAAKyF,IAAI,CAACzF,IAAI,EAAE;UACtB,MAAM,IAAIb,UAAU,CAChB,SAASoG,UAAU,+BAA+B,IAAI,CAAC9E,IAAI,IAAI,GAC/D,iBAAiBgF,IAAI,CAACzF,IAAI,gBAAgBA,IAAI,EAAE,CAAC;;;MAGzD,IAAIyF,IAAI,CAACvF,OAAO,IAAI,IAAI,EAAE;QACxB,IAAIF,IAAI,GAAGyF,IAAI,CAACvF,OAAO,EAAE;UACvB,MAAM,IAAIf,UAAU,CAChB,SAASoG,UAAU,+BAA+B,IAAI,CAAC9E,IAAI,EAAE,GAC7D,uBAAuBgF,IAAI,CAACvF,OAAO,gBAAgBF,IAAI,EAAE,CAAC;;;MAGlE,IAAIyF,IAAI,CAACtF,OAAO,IAAI,IAAI,EAAE;QACxB,IAAIH,IAAI,GAAGyF,IAAI,CAACtF,OAAO,EAAE;UACvB,MAAM,IAAIhB,UAAU,CAChB,SAASoG,UAAU,+BAA+B,IAAI,CAAC9E,IAAI,EAAE,GAC7D,uBAAuBgF,IAAI,CAACtF,OAAO,gBAAgBH,IAAI,GAAG,CAAC;;;MAInE;MACA,IAAIyF,IAAI,CAAC3F,KAAK,IAAI,IAAI,EAAE;QACtB,IAAI0F,CAAC,CAAC1F,KAAK,KAAK2F,IAAI,CAAC3F,KAAK,EAAE;UAC1B,MAAM,IAAIX,UAAU,CAChB,SAASoG,UAAU,+BAA+B,IAAI,CAAC9E,IAAI,GAAG,GAC9D,oBAAoBgF,IAAI,CAAC3F,KAAK,iBAAiB0F,CAAC,CAAC1F,KAAK,GAAG,CAAC;;;MAIlE;MACA,IAAI2F,IAAI,CAACrF,IAAI,EAAE;QACb,MAAMsF,MAAM,GAAGF,CAAC,CAACzF,KAAK;QACtB,KAAK,MAAM4F,GAAG,IAAIF,IAAI,CAACrF,IAAI,EAAE;UAC3B,MAAMwF,IAAI,GAAGC,MAAM,CAACF,GAAG,CAAC;UACxB,MAAMG,KAAK,GAAGL,IAAI,CAACrF,IAAI,CAACuF,GAAG,CAAC;UAC5B;UACA;UACA;UACA,MAAMI,YAAY,GACdH,IAAI,IAAI,CAAC,GAAGF,MAAM,CAACE,IAAI,CAAC,GAAGF,MAAM,CAACA,MAAM,CAACzF,MAAM,GAAG2F,IAAI,CAAC;UAC3D,IAAIE,KAAK,IAAI,IAAI,IAAI,CAACA,KAAK,EAAE,IAAI,CAAC,CAACE,OAAO,CAACD,YAAY,CAAC,KAAK,CAAC,CAAC,EAAE;YAC/D,MAAM,IAAI5G,UAAU,CAChB,SAASoG,UAAU,8BAA8B,GACjD,GAAG,IAAI,CAAC9E,IAAI,mBAAmBmF,IAAI,qBAAqB,GACxD,cAAcE,KAAK,kBAAkBJ,MAAM,GAAG,CAAC;;;;MAKzD;MACA,IAAID,IAAI,CAAC1F,KAAK,IAAI,IAAI,EAAE;QACtB,KAAK,IAAIkG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGR,IAAI,CAAC1F,KAAK,CAACE,MAAM,EAAE,EAAEgG,CAAC,EAAE;UAC1C,MAAMC,OAAO,GAAGT,IAAI,CAAC1F,KAAK,CAACkG,CAAC,CAAC;UAC7B,MAAME,GAAG,GAAGX,CAAC,CAACzF,KAAK,CAACkG,CAAC,CAAC;UACtB,IAAIC,OAAO,IAAI,IAAI,IAAIC,GAAG,IAAI,IAAI,EAAE;YAClC,IAAID,OAAO,KAAKC,GAAG,EAAE;cACnB,MAAM,IAAIhH,UAAU,CAChB,SAASoG,UAAU,8BAA8B,GACjD,GAAG,IAAI,CAAC9E,IAAI,oBAAoBgF,IAAI,CAAC1F,KAAK,IAAI,GAC9C,eAAeyF,CAAC,CAACzF,KAAK,GAAG,CAAC;;;;;;EAM1C;EAEA;;;;;;;;EAQAqG,IAAIA,CAAC7F,MAAuB,EAAE8F,MAAc;IAC1C,OAAO9F,MAAM;EACf;EAEU+F,cAAcA,CAAC/F,MAAuB,EAAE8F,MAAc;IAC9D,IAAI,IAAI,CAAClE,SAAS,IAAI,IAAI,EAAE;MAC1B,IAAI,CAACA,SAAS,CAAC5B,MAAM,EAAE8F,MAAM,CAAC;;EAElC;EAEA;;;;;EAKAE,WAAWA,CAACC,QAAkB;IAC5B,IAAI,CAACrE,SAAS,GAAGqE,QAAQ;EAC3B;EAEA;;;;EAIAC,aAAaA,CAAA;IACX,IAAI,CAACtE,SAAS,GAAG,IAAI;EACvB;EAEA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EAoEA;EACAuE,KAAKA,CACDnG,MAAuD,EACvD8F,MAAe;IACjBA,MAAM,GAAGA,MAAM,IAAI,EAAE;IAErB,IAAI,CAACM,iBAAiB,EAAE;IAExB;IACA,MAAMtB,UAAU,GAAGhG,aAAa,CAACiG,MAAM,CAAC/E,MAAM,CAAC;IAE/C,MAAMqG,cAAc,GAAGC,gBAAgB,CAACtG,MAAM,CAAC;IAC/C,MAAMuG,eAAe,GAAGC,iBAAiB,CAACxG,MAAM,CAAC;IAEjD,IAAIqG,cAAc,KAAKE,eAAe,EAAE;MACtC,MAAM,IAAI3H,UAAU,CAChB,mCAAmC,GACnC,gCAAgC,CAAC;;IAGvC;IACA,OAAOJ,SAAS,CAAC,IAAI,CAAC0B,IAAI,EAAE,MAAK;MAC/B;MACA,IAAI,CAAC,IAAI,CAACkE,KAAK,EAAE;QACf;;;;QAIA,IAAI,CAACS,wBAAwB,CAAC7E,MAAM,CAAC;QAErC;QACA,MAAMiB,WAAW,GAAY,EAAE;QAC/B,KAAK,MAAMwF,KAAK,IAAI3H,aAAa,CAACiG,MAAM,CAAC/E,MAAM,CAAC,EAAE;UAChDiB,WAAW,CAACI,IAAI,CAACoF,KAAK,CAACjH,KAAK,CAAC;;QAE/B,IAAI,CAACkH,KAAK,CAAC5H,aAAa,CAAC6E,gBAAgB,CAAC1C,WAAW,CAAC,CAAC;QACvD,IAAI,CAACmD,KAAK,GAAG,IAAI;QAEjB;QACA,IAAI,IAAI,CAAClB,cAAc,EAAE;UACvB,IAAI,CAACyD,UAAU,CAAC,IAAI,CAACzD,cAAc,CAAC;;QAGtC,IAAI,IAAI,CAACC,SAAS,KAAK,IAAI,IAAIoD,eAAe,EAAE;UAC9C;UACA;UACA;UACA,IAAI,CAACpD,SAAS,GAAG,CAAC;;;MAItB;;;;MAIA,IAAI,CAAC0B,wBAAwB,CAAC7E,MAAM,CAAC;MAErC;MACA;MAEA;MACA,IAAIuG,eAAe,EAAE;QACnB,IAAIzC,MAAM,GAAG,IAAI,CAAC+B,IAAI,CAAC7F,MAAM,EAAE8F,MAAM,CAAC;QAEtC;QACA,IAAI,IAAI,CAAC7D,eAAe,EAAE;UACxB;UACA,IAAI,CAAC2E,eAAe,CAAC5G,MAAM,EAAE8D,MAAM,CAAC;;QAGtC;QACA;QACA,MAAM+C,UAAU,GAAa/H,aAAa,CAACiG,MAAM,CAACjB,MAAM,CAAC;QACzD,MAAMgD,cAAc,GAAa,EAAE;QACnC;QACA;QACA,KAAK,IAAI7B,CAAC,IAAI4B,UAAU,EAAE;UACxB,IAAI/B,UAAU,CAACW,OAAO,CAACR,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE;YAChCA,CAAC,GAAGA,CAAC,CAAC8B,KAAK,EAAE;;UAEfD,cAAc,CAACzF,IAAI,CAAC4D,CAAC,CAAC;;QAExBnB,MAAM,GAAGhF,aAAa,CAAC6E,gBAAgB,CAACmD,cAAc,CAAC;QAEvD,IAAI,IAAI,CAAC/E,mBAAmB,IAAI,IAAI,EAAE;UACpC,MAAM,IAAIrD,mBAAmB,CACzB,+CAA+C,GAC/C,sCAAsC,CAAC;;QAG7C;QACA,OAAOoF,MAAM;OACd,MAAM;QACL,MAAMlB,UAAU,GAAGoE,iBAAiB,CAAChH,MAAM,CAAC;QAC5C,MAAMiH,WAAW,GAAG,IAAI,CAACC,kBAAkB,CAACtE,UAAU,CAAC;QACvD,IAAIkB,MAAuC;QAC3C,MAAMqD,WAAW,GAAGC,gBAAgB,CAACpH,MAAM,CAAC;QAC5C,IAAI,CAACqH,4BAA4B,CAC7BC,KAAK,CAACC,OAAO,CAACvH,MAAM,CAAC,GAAG4C,UAAU,CAAC,CAAC,CAAU,GACtBA,UAAmB,CAAC;QAEhD,IAAIqE,WAAW,IAAI,IAAI,IAAIA,WAAW,CAACvH,MAAM,GAAG,CAAC,IAC7C4H,KAAK,CAACC,OAAO,CAACN,WAAW,CAAC,CAAC,CAAC,CAAC,EAAE;UACjC;UACAnD,MAAM,GAAImD,WAAuB,CACnBhD,GAAG,CACA,CAACzE,KAAK,EAAEgI,KAAK,KAAK,IAAI1H,cAAc,CAChCqH,WAAW,EAAE3H,KAAK,EAAE,IAAI,EACxBV,aAAa,CAACiG,MAAM,CAAC/E,MAAM,CAAC,EAAE8F,MAAM,EAAE,IAAI,CAAC5F,IAAI,EAC/CsH,KAAK,CAAC,CAAC;SAC7B,MAAM;UACL1D,MAAM,GAAG,IAAIhE,cAAc,CACvBqH,WAAW,EAAEF,WAAoB,EAAE,IAAI,EACvCnI,aAAa,CAACiG,MAAM,CAAC/E,MAAM,CAAC,EAAE8F,MAAM,EAAE,IAAI,CAAC5F,IAAI,CAAC;;QAGtD;;;;;;;QAOA,IAAI,CAACuH,cAAc,CACfzH,MAAM,EAAE8D,MAAM,EAAE,IAAI,EAAE,IAAI,EAAElB,UAAU,EAAEqE,WAAW,EAAEnB,MAAM,CAAC;QAChE,IAAI,CAAC3C,SAAS,EAAE;QAEhB,IAAI,IAAI,CAACpB,mBAAmB,IAAI,IAAI,EAAE;UACpC,MAAM,IAAIrD,mBAAmB,CACzB,+CAA+C,GAC/C,sCAAsC,CAAC;;QAG7C,OAAOoF,MAAM;;IAEjB,CAAC,CAAC;EACJ;EAEA;;;;;;;EAOUuD,4BAA4BA,CAACzE,UAAiB;IACtD,IAAI,IAAI,CAACC,eAAe,IAAI,IAAI,EAAE;MAChC;KACD,MAAM,IAAID,UAAU,CAAClD,MAAM,KAAK,IAAI,CAACmD,eAAe,CAACnD,MAAM,EAAE;MAC5DgI,OAAO,CAACC,IAAI,CACR,gDAAgD,GAChD,GAAGC,IAAI,CAACC,SAAS,CAACjF,UAAU,CAAC,+BAA+B,GAC5D,oBAAoBgF,IAAI,CAACC,SAAS,CAAC,IAAI,CAAChF,eAAe,CAAC,IAAI,GAC5D,gBAAgB,IAAI,CAAC3C,IAAI,EAAE,CAAC;KACjC,MAAM;MACL,IAAI4H,WAAW,GAAG,KAAK;MACvB,IAAI,CAACjF,eAAe,CAACwB,OAAO,CAAC,CAAC0D,SAAS,EAAErC,CAAC,KAAI;QAC5C,IAAIqC,SAAS,IAAI,IAAI,IAAInF,UAAU,CAAC8C,CAAC,CAAC,IAAI,IAAI,IAC1C9C,UAAU,CAAC8C,CAAC,CAAC,KAAKqC,SAAS,EAAE;UAC/BD,WAAW,GAAG,IAAI;;MAEtB,CAAC,CAAC;MACF,IAAIA,WAAW,EAAE;QACfJ,OAAO,CAACC,IAAI,CACR,gCAAgC,GAChC,IAAIC,IAAI,CAACC,SAAS,CAACjF,UAAU,CAAC,aAAa,GAC3C,kCAAkC,IAAI,CAAC1C,IAAI,IAAI,GAC/C,GAAG0H,IAAI,CAACC,SAAS,CAAC,IAAI,CAAChF,eAAe,CAAC,EAAE,CAAC;;;EAGpD;EAEA;;;;;;;;;;;;EAYA,IAAIoE,WAAWA,CAAA;IACb,IAAI,IAAI,CAAC3F,YAAY,IAAI,IAAI,IAAI,IAAI,CAACA,YAAY,CAAC5B,MAAM,KAAK,CAAC,EAAE;MAC/D,MAAM,IAAIjB,cAAc,CACpB,aAAa,IAAI,CAACyB,IAAI,yCAAyC,GAC/D,uBAAuB,CAAC;;IAE9B,MAAM8H,eAAe,GAAa,EAAE;IACpC,KAAK,MAAMC,IAAI,IAAI,IAAI,CAAC3G,YAAY,EAAE;MACpC,MAAM4G,WAAW,GAAGN,IAAI,CAACC,SAAS,CAACI,IAAI,CAAC/G,YAAY,CAAC;MACrD,IAAI8G,eAAe,CAACvC,OAAO,CAACyC,WAAW,CAAC,KAAK,CAAC,CAAC,EAAE;QAC/CF,eAAe,CAAC3G,IAAI,CAAC6G,WAAW,CAAC;;;IAGrC,IAAIF,eAAe,CAACtI,MAAM,KAAK,CAAC,EAAE;MAChC,MAAMwB,YAAY,GAAG,IAAI,CAACI,YAAY,CAAC,CAAC,CAAC,CAACJ,YAAY;MACtD,IAAIoG,KAAK,CAACC,OAAO,CAACrG,YAAY,CAAC,IAAIoG,KAAK,CAACC,OAAO,CAACrG,YAAY,CAAC,CAAC,CAAC,CAAC,IAC7DA,YAAY,CAACxB,MAAM,KAAK,CAAC,EAAE;QAC7B,OAAQwB,YAAwB,CAAC,CAAC,CAAC;OACpC,MAAM;QACL,OAAOA,YAAY;;KAGtB,MAAM;MACL,MAAM,IAAIzC,cAAc,CACpB,aAAa,IAAI,CAACyB,IAAI,6CAA6C,GACnE,mEAAmE,GACnE,gBAAgB,CAAC;MACrB;;EAEJ;EAEA;;;;;;;;;;EAUAiI,WAAWA,CAAA;IACT,IAAI,CAAC,IAAI,CAAC/D,KAAK,EAAE;MACf,MAAM,IAAIzF,YAAY,CAClB,sCAAsC,IAAI,CAACuB,IAAI,IAAI,GACnD,4DAA4D,GAC5D,yBAAyB,CAAC;;IAEhC,OAAOlB,cAAc,CAACoJ,oBAAoB,CAAC,IAAI,CAACnF,OAAO,CAAC;EAC1D;EAEA;;;;;;;;;;;EAWAyD,KAAKA,CAAC9D,UAAyB;IAC7B,IAAI,CAACwB,KAAK,GAAG,IAAI;EACnB;EAEA;;;;;;;;EAQAiE,UAAUA,CAACC,aAAa,GAAG,KAAK;IAC9B,OAAOrJ,aAAa,CAACqJ,aAAa,GAAG,IAAI,CAAC/D,gBAAgB,GAAG,IAAI,CAACtB,OAAO,CAAC;EAC5E;EAEA;;;;;;;;;;;;EAYA0D,UAAUA,CAAC1D,OAAiB;IAC1B/E,IAAI,CAAC,MAAK;MACR,MAAMqK,MAAM,GAAG,IAAI,CAACtF,OAAO;MAC3B,IAAIsF,MAAM,CAAC7I,MAAM,KAAKuD,OAAO,CAACvD,MAAM,EAAE;QACpC;QACA;QACA;QACA;QACA,MAAM,IAAId,UAAU,CAChB,4CAA4C,IAAI,CAACsB,IAAI,IAAI,GACzD,gCAAgC+C,OAAO,CAACvD,MAAM,IAAI,GAClD,+BAA+B6I,MAAM,CAAC7I,MAAM,YAAY,GACxD,qBAAqBuD,OAAO,KAAK,CAAC;;MAExC,IAAIsF,MAAM,CAAC7I,MAAM,KAAK,CAAC,EAAE;QACvB;;MAEF,MAAM8I,iBAAiB,GAAmC,EAAE;MAC5D,MAAMC,WAAW,GAAGxJ,aAAa,CAACsJ,MAAM,CAAC;MACzC,KAAK,IAAI7C,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG+C,WAAW,CAAC/I,MAAM,EAAE,EAAEgG,CAAC,EAAE;QAC3C,MAAMgD,EAAE,GAAGD,WAAW,CAAC/C,CAAC,CAAC;QACzB,MAAMiD,CAAC,GAAGJ,MAAM,CAAC7C,CAAC,CAAC;QACnB,MAAMpB,CAAC,GAAGrB,OAAO,CAACyC,CAAC,CAAC;QACpB,IAAI,CAACvH,IAAI,CAACyK,WAAW,CAACF,EAAE,CAAClJ,KAAK,EAAE8E,CAAC,CAAC9E,KAAK,CAAC,EAAE;UACxC,MAAM,IAAIZ,UAAU,CAChB,sBAAsB8J,EAAE,CAAClJ,KAAK,GAAG,GACjC,6CAA6C8E,CAAC,CAAC9E,KAAK,EAAE,CAAC;;QAE7DgJ,iBAAiB,CAACnH,IAAI,CAAC,CAACsH,CAAC,EAAErE,CAAC,CAAC,CAAC;;MAEhCpF,aAAa,CAACsJ,iBAAiB,CAAC;IAClC,CAAC,CAAC;EACJ;EAEA;;;;;;;;;;;;;;;EAeUK,SAASA,CACf3I,IAAY,EAAEV,KAAY,EAAED,KAAgB,EAAEuJ,WAAyB,EACvEC,WAAyB,EAAEpG,SAAmB,EAAEqG,UAAuB,EACvEC,kBAA6B;IAC/B;IACA,IAAI,IAAI,CAACpH,iBAAiB,CAAC4D,OAAO,CAACvF,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE;MAC/C,MAAM,IAAItB,UAAU,CAChB,yBAAyBsB,IAAI,cAAc,IAAI,CAACA,IAAI,EAAE,CAAC;;IAE7D,IAAI,CAAC2B,iBAAiB,CAACR,IAAI,CAACnB,IAAI,CAAC;IAEjC,IAAIX,KAAK,IAAI,IAAI,EAAE;MACjBA,KAAK,GAAG,SAAS;;IAGnB,IAAI,IAAI,CAAC6D,yBAAyB,EAAE;MAClC0F,WAAW,GAAGG,kBAAkB,IAAI,IAAI,GAAGA,kBAAkB,EAAE,GACpBpK,cAAc,CAAC,OAAO,CAAC;;IAEpE,MAAMqK,SAAS,GAAGJ,WAAW,CAAC3C,KAAK,CAAC3G,KAAK,EAAED,KAAK,CAAC;IACjD,MAAM4J,MAAM,GACR,IAAIhK,aAAa,CAAC+J,SAAS,EAAE3J,KAAK,EAAEW,IAAI,EAAEyC,SAAS,EAAEqG,UAAU,CAAC;IACpEE,SAAS,CAACE,OAAO,EAAE;IACnB;IACA,IAAIL,WAAW,IAAI,IAAI,EAAE;MACvB,IAAI,CAACM,OAAO,CAAC,MAAMN,WAAW,CAAC5C,KAAK,CAACgD,MAAM,CAACG,IAAI,EAAE,CAAC,CAAC;;IAEtD,IAAI3G,SAAS,IAAI,IAAI,EAAE;MACrBA,SAAS,GAAG,IAAI;;IAElB,IAAIA,SAAS,EAAE;MACb,IAAI,CAACT,iBAAiB,CAACb,IAAI,CAAC8H,MAAM,CAAC;KACpC,MAAM;MACL,IAAI,CAAChH,oBAAoB,CAACd,IAAI,CAAC8H,MAAM,CAAC;;IAExC,OAAOA,MAAM;EACf;EAEA;;;;;;;;;;EAUAI,4BAA4BA,CAAChE,KAAc;IACzC,IAAI,CAACnC,yBAAyB,GAAGmC,KAAK;EACxC;EAEA;;;;;;;;EAQA8D,OAAOA,CAACtF,MAAqC;IAC3C,IAAIA,MAAM,IAAI,IAAI,IAAIuD,KAAK,CAACC,OAAO,CAACxD,MAAM,CAAC,IAAIA,MAAM,CAACrE,MAAM,KAAK,CAAC,EAAE;MAClE;;IAEF;IACAqE,MAAM,GAAGjF,aAAa,CAACiG,MAAM,CAAChB,MAAM,CAAC;IACrC,IAAI,IAAI,CAAC3B,OAAO,KAAKoH,SAAS,IAAI,IAAI,CAACpH,OAAO,KAAK,IAAI,EAAE;MACvD,IAAI,CAAC2B,MAAM,CAAC1C,IAAI,CAAC,GAAG0C,MAAM,CAAC;;EAE/B;EAEA;;;;;;;;;;;EAWAmD,kBAAkBA,CAACtE,UAAyB;IAC1C,OAAOA,UAAU;EACnB;EAEA;;;;;;;;;EASA6G,WAAWA,CAACzJ,MAAuB,EAAE0J,IAAsB;IAEzD,IAAI,CAAC,IAAI,CAACzH,eAAe,EAAE;MACzB,IAAIyH,IAAI,IAAI,IAAI,EAAE;QAChB,IAAIpC,KAAK,CAACC,OAAO,CAACmC,IAAI,CAAC,EAAE;UACvBA,IAAI,CAACrF,OAAO,CAACsF,WAAW,IAAG;YACzB,IAAIA,WAAW,IAAI,IAAI,EAAE;cACvB,MAAM,IAAIC,SAAS,CACf,SAAS,IAAI,CAAC1J,IAAI,6BAA6B,GAC/C,8BAA8B,CAAC;;UAEvC,CAAC,CAAC;SACH,MAAM;UACL,MAAM,IAAI0J,SAAS,CACf,SAAS,IAAI,CAAC1J,IAAI,6BAA6B,GAC/C,8BAA8B,CAAC;;;MAGvC;MACA,OAAO,IAAI;;IAEb;IACA;IACA,OAAOwJ,IAAI;EACb;EAEQ9C,eAAeA,CACnB5G,MAAuB,EAAE6J,OAAwB,EACjDC,YAA8B;IAChC,IAAI,CAAC,IAAI,CAAC7H,eAAe,EAAE;MACzB;;IAGF,MAAMjB,WAAW,GAAG,IAAI,CAACyI,WAAW,CAACzJ,MAAM,EAAE8J,YAAY,CAAC;IAC1D,MAAMC,WAAW,GAAGjL,aAAa,CAACiG,MAAM,CAAC8E,OAAO,CAAC;IACjD,MAAMG,eAAe,GAAGlL,aAAa,CAACiG,MAAM,CAAC/D,WAAW,CAAC;IAEzD,IAAI+I,WAAW,CAACrK,MAAM,KAAKsK,eAAe,CAACtK,MAAM,EAAE;MACjD,MAAM,IAAIkF,KAAK,CACX,GAAG,IAAI,CAAC1E,IAAI,YAAY6J,WAAW,CAACrK,MAAM,WAAW,GACrD,OAAOqK,WAAW,CAACrK,MAAM,0BAA0B,CAAC;;IAE1D,KAAK,IAAIgG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGqE,WAAW,CAACrK,MAAM,EAAEgG,CAAC,EAAE,EAAE;MAC3CqE,WAAW,CAACrE,CAAC,CAAC,CAACuE,SAAS,GAAGD,eAAe,CAACtE,CAAC,CAAC;;EAEjD;EAEA;;;;;;;;;;;;EAYQ+B,cAAcA,CAClB5G,YAA6C,EAC7CC,aAA8C,EAC9CC,UAA2B,EAAEC,WAA4B,EACzDC,WAA0B,EAAEC,YAA2B,EACvD4E,MAAA,GAAa,IAAI;IACnB,MAAMoE,eAAe,GACjBpL,aAAa,CAACiG,MAAM,CAAClE,YAAY,CAAC;IACtCC,aAAa,GAAGhC,aAAa,CAACiG,MAAM,CAACjE,aAAa,CAAC;IACnDC,UAAU,GAAGjC,aAAa,CAACiG,MAAM,CAAChE,UAAU,CAAC;IAC7CC,WAAW,GAAGlC,aAAa,CAACiG,MAAM,CAAC/D,WAAW,CAAC;IAC/CC,WAAW,GAAGlC,WAAW,CAACoL,kBAAkB,CAAClJ,WAAW,CAAC;IACzDC,YAAY,GAAGnC,WAAW,CAACoL,kBAAkB,CAACjJ,YAAY,CAAC;IAE3D;IACA,MAAMR,aAAa,GAAY,EAAE;IACjC,MAAMC,WAAW,GAAa,EAAE;IAChC,MAAMC,aAAa,GAAa,EAAE;IAClC,KAAK,MAAMqE,CAAC,IAAIiF,eAAe,EAAE;MAC/B;;;;MAIAxJ,aAAa,CAACW,IAAI,CAAC4D,CAAC,CAAClF,WAAW,CAAC;MACjCY,WAAW,CAACU,IAAI,CAAC4D,CAAC,CAAC3B,SAAS,CAAC;MAC7B1C,aAAa,CAACS,IAAI,CAAC4D,CAAC,CAACmF,WAAW,CAAC;;IAGnC;IACA;IACA;IACA,IAAI5J,IAAI,CACJ;MACEC,aAAa,EAAE,IAAI;MACnBC,aAAa;MACbC,WAAW;MACXC,aAAa;MACbC,YAAY,EAAEqJ,eAAe;MAC7BpJ,aAAa;MACbC,UAAU;MACVC,WAAW;MACXC,WAAW;MACXC;KACD,EACD4E,MAAM,CAAC;IAEX;IACA,KAAK,IAAIJ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG5E,aAAa,CAACpB,MAAM,EAAEgG,CAAC,EAAE,EAAE;MAC7C;MACA5E,aAAa,CAAC4E,CAAC,CAAC,CAAC3F,WAAW,GAAG,IAAI;MACnCe,aAAa,CAAC4E,CAAC,CAAC,CAACpC,SAAS,GAAG,IAAI,CAAChC,YAAY,CAAC5B,MAAM,GAAG,CAAC;MACzDoB,aAAa,CAAC4E,CAAC,CAAC,CAAC0E,WAAW,GAAG1E,CAAC;;EAEpC;EAEA;;;;;;;;;;;;;;;;;;;;;EAqBAnE,SAASA,CAAA;IACP,MAAM8I,MAAM,GACmB;MAACnK,IAAI,EAAE,IAAI,CAACA,IAAI;MAAEyC,SAAS,EAAE,IAAI,CAACA;IAAS,CAAC;IAC3E,IAAI,IAAI,CAACE,eAAe,IAAI,IAAI,EAAE;MAChCwH,MAAM,CAAC,iBAAiB,CAAC,GAAG,IAAI,CAACxH,eAAe;;IAElD,IAAI,IAAI,CAACtD,KAAK,IAAI,IAAI,EAAE;MACtB8K,MAAM,CAAC,OAAO,CAAC,GAAG,IAAI,CAAC9K,KAAK;;IAE9B,OAAO8K,MAAM;EACf;EAEA;;;;;EAKUC,cAAcA,CAAA;IACtB,IAAI,CAACrH,OAAO,CAACoB,OAAO,CAAC8E,MAAM,IAAIA,MAAM,CAACC,OAAO,EAAE,CAAC;IAChD,OAAO,IAAI,CAACnG,OAAO,CAACvD,MAAM;EAC5B;EAEU0G,iBAAiBA,CAAA;IACzB,IAAI,IAAI,CAACjD,SAAS,KAAK,CAAC,EAAE;MACxB,MAAM,IAAIyB,KAAK,CAAC,UAAU,IAAI,CAAC1E,IAAI,wBAAwB,CAAC;;EAEhE;EAEA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EA8BAkJ,OAAOA,CAAA;IACL,IAAI,CAAC,IAAI,CAAChF,KAAK,EAAE;MACf,MAAM,IAAIQ,KAAK,CACX,wBAAwB,IAAI,CAAC1E,IAAI,2BAA2B,GAC5D,YAAY,CAAC;;IAGnB,IAAI,IAAI,CAACiD,SAAS,KAAK,IAAI,EAAE;MAC3B,MAAM,IAAIyB,KAAK,CACX,wBAAwB,IAAI,CAAC1E,IAAI,gCAAgC,GACjE,MAAM,CAAC;;IAGb,IAAI,CAACkG,iBAAiB,EAAE;IAExB,IAAImE,oBAAoB,GAAG,CAAC;IAC5B,IAAI,EAAE,IAAI,CAACpH,SAAS,KAAK,CAAC,EAAE;MAC1BoH,oBAAoB,GAAG,IAAI,CAACD,cAAc,EAAE;;IAG9C,OAAO;MAACE,oBAAoB,EAAE,IAAI,CAACrH,SAAS;MAAEoH;IAAoB,CAAC;EACrE;;AAGF;;;;;;;;;;AAUA,SAASvD,iBAAiBA,CAACnG,YACQ;EACjCA,YAAY,GACR/B,aAAa,CAACiG,MAAM,CAAClE,YAAY,CAAgC;EACrE,MAAM4J,MAAM,GAAY,EAAE;EAC1B,KAAK,MAAMxF,CAAC,IAAIpE,YAAY,EAAE;IAC5B4J,MAAM,CAACpJ,IAAI,CAAC4D,CAAC,CAACzF,KAAK,CAAC;;EAEtB,OAAOV,aAAa,CAAC6E,gBAAgB,CAAC8G,MAAM,CAAC;AAC/C;AAEA;;;;;;;;;AASA,SAASrD,gBAAgBA,CAACvG,YACQ;EAChC,OAAO,SAAS;AAClB;AAEA;;;;;;;;;;;AAWA,OAAM,SAAU6J,eAAeA,CAC3BC,MAAsB,EAAExJ,KAAa,EACrCmC,SAAkB;EACpB,IAAInC,KAAK,IAAI,IAAI,IAAKmC,SAAS,IAAI,IAAI,IAAIA,SAAS,GAAG,CAAE,EAAE;IACzDnC,KAAK,GAAGwJ,MAAM,CAAC5K,WAAW;IAC1BuD,SAAS,GAAGqH,MAAM,CAACrH,SAAS;;EAE9B,IAAInC,KAAK,CAACG,YAAY,CAAC5B,MAAM,KAAK,CAAC,EAAE;IACnC,OAAO,CAACiL,MAAM,CAAC;GAChB,MAAM;IACL,MAAM1C,IAAI,GAAG9G,KAAK,CAACG,YAAY,CAACgC,SAAS,CAAC;IAC1C,IAAI2E,IAAI,CAACvH,aAAa,CAAChB,MAAM,KAAK,CAAC,EAAE;MACnC,OAAOuI,IAAI,CAACpH,YAAY;KACzB,MAAM;MACL,MAAM+J,aAAa,GAAqB,EAAE;MAC1C,KAAK,IAAIlF,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGuC,IAAI,CAACvH,aAAa,CAAChB,MAAM,EAAEgG,CAAC,EAAE,EAAE;QAClD,MAAMT,CAAC,GAAGgD,IAAI,CAACpH,YAAY,CAAC6E,CAAC,CAAC;QAC9B,MAAMvE,KAAK,GAAG8G,IAAI,CAACvH,aAAa,CAACgF,CAAC,CAAC;QACnC,MAAMpC,SAAS,GAAG2E,IAAI,CAACtH,WAAW,CAAC+E,CAAC,CAAC;QACrC,MAAMmF,eAAe,GAAGH,eAAe,CAACzF,CAAC,EAAE9D,KAAK,EAAEmC,SAAS,CAAC;QAC5D;QACA,KAAK,MAAM2B,CAAC,IAAI4F,eAAe,EAAE;UAC/B,IAAID,aAAa,CAACnF,OAAO,CAACR,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE;YACnC2F,aAAa,CAACvJ,IAAI,CAAC4D,CAAC,CAAC;;;;MAI3B,OAAO2F,aAAa;;;AAG1B;AAIA,SAAStE,gBAAgBA,CAACwE,OAAsC;EAE9D,IAAIzE,cAAc,GAAG,IAAI;EACzB,KAAK,MAAMsE,MAAM,IAAI7L,aAAa,CAACiG,MAAM,CAAC+F,OAAO,CAAC,EAAE;IAClD,IAAI,EAAEH,MAAM,YAAY7K,cAAc,CAAC,EAAE;MACvCuG,cAAc,GAAG,KAAK;MACtB;;;EAGJ,OAAOA,cAAc;AACvB;AAEA,SAASG,iBAAiBA,CAACsE,OACe;EACxC,IAAIvE,eAAe,GAAG,IAAI;EAC1B,KAAK,MAAMoE,MAAM,IAAI7L,aAAa,CAACiG,MAAM,CAAC+F,OAAO,CAAC,EAAE;IAClD,IAAIH,MAAM,YAAY7K,cAAc,EAAE;MACpCyG,eAAe,GAAG,KAAK;MACvB;;;EAGJ,OAAOA,eAAe;AACxB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}