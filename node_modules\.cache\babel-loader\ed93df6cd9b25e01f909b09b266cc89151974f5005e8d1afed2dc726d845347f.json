{"ast": null, "code": "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n// TODO update import path once op is modularized.\nimport { abs } from '../../ops/ops';\nimport { getGlobalTensorClass } from '../../tensor';\ngetGlobalTensorClass().prototype.abs = function () {\n  this.throwIfDisposed();\n  return abs(this);\n};", "map": {"version": 3, "names": ["abs", "getGlobalTensorClass", "prototype", "throwIfDisposed"], "sources": ["C:\\tfjs-core\\src\\public\\chained_ops\\abs.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\n// TODO update import path once op is modularized.\nimport {abs} from '../../ops/ops';\nimport {getGlobalTensorClass, Tensor} from '../../tensor';\nimport {Rank} from '../../types';\n\ndeclare module '../../tensor' {\n  interface Tensor<R extends Rank = Rank> {\n    abs<T extends Tensor>(this: T): T;\n  }\n}\n\ngetGlobalTensorClass().prototype.abs = function<T extends Tensor>(this: T) {\n  this.throwIfDisposed();\n  return abs(this);\n};\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAiBA;AACA,SAAQA,GAAG,QAAO,eAAe;AACjC,SAAQC,oBAAoB,QAAe,cAAc;AASzDA,oBAAoB,EAAE,CAACC,SAAS,CAACF,GAAG,GAAG;EACrC,IAAI,CAACG,eAAe,EAAE;EACtB,OAAOH,GAAG,CAAC,IAAI,CAAC;AAClB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}