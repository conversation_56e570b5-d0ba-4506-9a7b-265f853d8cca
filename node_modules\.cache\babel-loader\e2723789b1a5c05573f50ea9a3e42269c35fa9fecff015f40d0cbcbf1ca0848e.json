{"ast": null, "code": "/**\n * @license\n * Copyright 2018 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n// tslint:disable-next-line: no-imports-from-dist\nimport * as tfOps from '@tensorflow/tfjs-core/dist/ops/ops_for_converter';\nimport { getParamValue } from './utils';\nexport const executeOp = (node, tensorMap, context, ops = tfOps) => {\n  switch (node.op) {\n    case 'ResizeBilinear':\n      {\n        const images = getParamValue('images', node, tensorMap, context);\n        const size = getParamValue('size', node, tensorMap, context);\n        const alignCorners = getParamValue('alignCorners', node, tensorMap, context);\n        const halfPixelCenters = getParamValue('halfPixelCenters', node, tensorMap, context);\n        return [ops.image.resizeBilinear(images, [size[0], size[1]], alignCorners, halfPixelCenters)];\n      }\n    case 'ResizeNearestNeighbor':\n      {\n        const images = getParamValue('images', node, tensorMap, context);\n        const size = getParamValue('size', node, tensorMap, context);\n        const alignCorners = getParamValue('alignCorners', node, tensorMap, context);\n        const halfPixelCenters = getParamValue('halfPixelCenters', node, tensorMap, context);\n        return [ops.image.resizeNearestNeighbor(images, [size[0], size[1]], alignCorners, halfPixelCenters)];\n      }\n    case 'CropAndResize':\n      {\n        const image = getParamValue('image', node, tensorMap, context);\n        const boxes = getParamValue('boxes', node, tensorMap, context);\n        const boxInd = getParamValue('boxInd', node, tensorMap, context);\n        const cropSize = getParamValue('cropSize', node, tensorMap, context);\n        const method = getParamValue('method', node, tensorMap, context);\n        const extrapolationValue = getParamValue('extrapolationValue', node, tensorMap, context);\n        return [ops.image.cropAndResize(image, boxes, boxInd, cropSize, method, extrapolationValue)];\n      }\n    case 'ImageProjectiveTransformV3':\n      {\n        const images = getParamValue('images', node, tensorMap, context);\n        const transforms = getParamValue('transforms', node, tensorMap, context);\n        const outputShape = getParamValue('outputShape', node, tensorMap, context);\n        const fillValue = getParamValue('fillValue', node, tensorMap, context);\n        const interpolation = getParamValue('interpolation', node, tensorMap, context);\n        const fillMode = getParamValue('fillMode', node, tensorMap, context);\n        return [ops.image.transform(images, transforms, interpolation.toLowerCase(), fillMode.toLowerCase(), fillValue, outputShape)];\n      }\n    default:\n      throw TypeError(`Node type ${node.op} is not implemented`);\n  }\n};\nexport const CATEGORY = 'image';", "map": {"version": 3, "names": ["tfOps", "getParamValue", "executeOp", "node", "tensorMap", "context", "ops", "op", "images", "size", "alignCorners", "halfPixelCenters", "image", "resizeBilinear", "resizeNearestNeighbor", "boxes", "boxInd", "cropSize", "method", "extrapolationValue", "cropAndResize", "transforms", "outputShape", "fillValue", "interpolation", "fillMode", "transform", "toLowerCase", "TypeError", "CATEGORY"], "sources": ["C:\\tfjs-converter\\src\\operations\\executors\\image_executor.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2018 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {Tensor, Tensor1D, Tensor2D, Tensor3D, Tensor4D} from '@tensorflow/tfjs-core';\n// tslint:disable-next-line: no-imports-from-dist\nimport * as tfOps from '@tensorflow/tfjs-core/dist/ops/ops_for_converter';\n\nimport {NamedTensorsMap} from '../../data/types';\nimport {ExecutionContext} from '../../executor/execution_context';\nimport {InternalOpExecutor, Node} from '../types';\n\nimport {getParamValue} from './utils';\n\nexport const executeOp: InternalOpExecutor =\n    (node: Node, tensorMap: NamedTensorsMap,\n     context: ExecutionContext, ops = tfOps): Tensor[] => {\n      switch (node.op) {\n        case 'ResizeBilinear': {\n          const images =\n              getParamValue('images', node, tensorMap, context) as Tensor;\n          const size =\n              getParamValue('size', node, tensorMap, context) as number[];\n          const alignCorners =\n              getParamValue('alignCorners', node, tensorMap, context) as\n              boolean;\n          const halfPixelCenters =\n              getParamValue('halfPixelCenters', node, tensorMap, context) as\n              boolean;\n          return [ops.image.resizeBilinear(\n              images as Tensor3D | Tensor4D, [size[0], size[1]], alignCorners,\n              halfPixelCenters)];\n        }\n        case 'ResizeNearestNeighbor': {\n          const images =\n              getParamValue('images', node, tensorMap, context) as Tensor;\n          const size =\n              getParamValue('size', node, tensorMap, context) as number[];\n          const alignCorners =\n              getParamValue('alignCorners', node, tensorMap, context) as\n              boolean;\n          const halfPixelCenters =\n              getParamValue('halfPixelCenters', node, tensorMap, context) as\n              boolean;\n          return [ops.image.resizeNearestNeighbor(\n              images as Tensor3D | Tensor4D, [size[0], size[1]], alignCorners,\n              halfPixelCenters)];\n        }\n        case 'CropAndResize': {\n          const image =\n              getParamValue('image', node, tensorMap, context) as Tensor;\n          const boxes =\n              getParamValue('boxes', node, tensorMap, context) as Tensor;\n          const boxInd =\n              getParamValue('boxInd', node, tensorMap, context) as Tensor;\n          const cropSize =\n              getParamValue('cropSize', node, tensorMap, context) as number[];\n          const method =\n              getParamValue('method', node, tensorMap, context) as string;\n          const extrapolationValue =\n              getParamValue('extrapolationValue', node, tensorMap, context) as\n              number;\n          return [ops.image.cropAndResize(\n              image as Tensor4D, boxes as Tensor2D, boxInd as Tensor1D,\n              cropSize as [number, number], method as 'bilinear' | 'nearest',\n              extrapolationValue)];\n        }\n        case 'ImageProjectiveTransformV3': {\n          const images =\n              getParamValue('images', node, tensorMap, context) as Tensor;\n          const transforms =\n              getParamValue('transforms', node, tensorMap, context) as Tensor;\n          const outputShape =\n              getParamValue('outputShape', node, tensorMap, context) as\n              number[];\n          const fillValue =\n              getParamValue('fillValue', node, tensorMap, context) as number;\n          const interpolation =\n              getParamValue('interpolation', node, tensorMap, context) as\n              string;\n          const fillMode =\n              getParamValue('fillMode', node, tensorMap, context) as string;\n          return [ops.image.transform(\n              images as Tensor4D,\n              transforms as Tensor2D,\n              interpolation.toLowerCase() as 'bilinear' | 'nearest',\n              fillMode.toLowerCase() as 'constant' | 'reflect' | 'wrap' | 'nearest',\n              fillValue,\n              outputShape as [number, number])];\n        }\n        default:\n          throw TypeError(`Node type ${node.op} is not implemented`);\n      }\n    };\n\nexport const CATEGORY = 'image';\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAkBA;AACA,OAAO,KAAKA,KAAK,MAAM,kDAAkD;AAMzE,SAAQC,aAAa,QAAO,SAAS;AAErC,OAAO,MAAMC,SAAS,GAClBA,CAACC,IAAU,EAAEC,SAA0B,EACtCC,OAAyB,EAAEC,GAAG,GAAGN,KAAK,KAAc;EACnD,QAAQG,IAAI,CAACI,EAAE;IACb,KAAK,gBAAgB;MAAE;QACrB,MAAMC,MAAM,GACRP,aAAa,CAAC,QAAQ,EAAEE,IAAI,EAAEC,SAAS,EAAEC,OAAO,CAAW;QAC/D,MAAMI,IAAI,GACNR,aAAa,CAAC,MAAM,EAAEE,IAAI,EAAEC,SAAS,EAAEC,OAAO,CAAa;QAC/D,MAAMK,YAAY,GACdT,aAAa,CAAC,cAAc,EAAEE,IAAI,EAAEC,SAAS,EAAEC,OAAO,CAC/C;QACX,MAAMM,gBAAgB,GAClBV,aAAa,CAAC,kBAAkB,EAAEE,IAAI,EAAEC,SAAS,EAAEC,OAAO,CACnD;QACX,OAAO,CAACC,GAAG,CAACM,KAAK,CAACC,cAAc,CAC5BL,MAA6B,EAAE,CAACC,IAAI,CAAC,CAAC,CAAC,EAAEA,IAAI,CAAC,CAAC,CAAC,CAAC,EAAEC,YAAY,EAC/DC,gBAAgB,CAAC,CAAC;;IAExB,KAAK,uBAAuB;MAAE;QAC5B,MAAMH,MAAM,GACRP,aAAa,CAAC,QAAQ,EAAEE,IAAI,EAAEC,SAAS,EAAEC,OAAO,CAAW;QAC/D,MAAMI,IAAI,GACNR,aAAa,CAAC,MAAM,EAAEE,IAAI,EAAEC,SAAS,EAAEC,OAAO,CAAa;QAC/D,MAAMK,YAAY,GACdT,aAAa,CAAC,cAAc,EAAEE,IAAI,EAAEC,SAAS,EAAEC,OAAO,CAC/C;QACX,MAAMM,gBAAgB,GAClBV,aAAa,CAAC,kBAAkB,EAAEE,IAAI,EAAEC,SAAS,EAAEC,OAAO,CACnD;QACX,OAAO,CAACC,GAAG,CAACM,KAAK,CAACE,qBAAqB,CACnCN,MAA6B,EAAE,CAACC,IAAI,CAAC,CAAC,CAAC,EAAEA,IAAI,CAAC,CAAC,CAAC,CAAC,EAAEC,YAAY,EAC/DC,gBAAgB,CAAC,CAAC;;IAExB,KAAK,eAAe;MAAE;QACpB,MAAMC,KAAK,GACPX,aAAa,CAAC,OAAO,EAAEE,IAAI,EAAEC,SAAS,EAAEC,OAAO,CAAW;QAC9D,MAAMU,KAAK,GACPd,aAAa,CAAC,OAAO,EAAEE,IAAI,EAAEC,SAAS,EAAEC,OAAO,CAAW;QAC9D,MAAMW,MAAM,GACRf,aAAa,CAAC,QAAQ,EAAEE,IAAI,EAAEC,SAAS,EAAEC,OAAO,CAAW;QAC/D,MAAMY,QAAQ,GACVhB,aAAa,CAAC,UAAU,EAAEE,IAAI,EAAEC,SAAS,EAAEC,OAAO,CAAa;QACnE,MAAMa,MAAM,GACRjB,aAAa,CAAC,QAAQ,EAAEE,IAAI,EAAEC,SAAS,EAAEC,OAAO,CAAW;QAC/D,MAAMc,kBAAkB,GACpBlB,aAAa,CAAC,oBAAoB,EAAEE,IAAI,EAAEC,SAAS,EAAEC,OAAO,CACtD;QACV,OAAO,CAACC,GAAG,CAACM,KAAK,CAACQ,aAAa,CAC3BR,KAAiB,EAAEG,KAAiB,EAAEC,MAAkB,EACxDC,QAA4B,EAAEC,MAAgC,EAC9DC,kBAAkB,CAAC,CAAC;;IAE1B,KAAK,4BAA4B;MAAE;QACjC,MAAMX,MAAM,GACRP,aAAa,CAAC,QAAQ,EAAEE,IAAI,EAAEC,SAAS,EAAEC,OAAO,CAAW;QAC/D,MAAMgB,UAAU,GACZpB,aAAa,CAAC,YAAY,EAAEE,IAAI,EAAEC,SAAS,EAAEC,OAAO,CAAW;QACnE,MAAMiB,WAAW,GACbrB,aAAa,CAAC,aAAa,EAAEE,IAAI,EAAEC,SAAS,EAAEC,OAAO,CAC7C;QACZ,MAAMkB,SAAS,GACXtB,aAAa,CAAC,WAAW,EAAEE,IAAI,EAAEC,SAAS,EAAEC,OAAO,CAAW;QAClE,MAAMmB,aAAa,GACfvB,aAAa,CAAC,eAAe,EAAEE,IAAI,EAAEC,SAAS,EAAEC,OAAO,CACjD;QACV,MAAMoB,QAAQ,GACVxB,aAAa,CAAC,UAAU,EAAEE,IAAI,EAAEC,SAAS,EAAEC,OAAO,CAAW;QACjE,OAAO,CAACC,GAAG,CAACM,KAAK,CAACc,SAAS,CACvBlB,MAAkB,EAClBa,UAAsB,EACtBG,aAAa,CAACG,WAAW,EAA4B,EACrDF,QAAQ,CAACE,WAAW,EAAiD,EACrEJ,SAAS,EACTD,WAA+B,CAAC,CAAC;;IAEvC;MACE,MAAMM,SAAS,CAAC,aAAazB,IAAI,CAACI,EAAE,qBAAqB,CAAC;;AAEhE,CAAC;AAEL,OAAO,MAAMsB,QAAQ,GAAG,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}