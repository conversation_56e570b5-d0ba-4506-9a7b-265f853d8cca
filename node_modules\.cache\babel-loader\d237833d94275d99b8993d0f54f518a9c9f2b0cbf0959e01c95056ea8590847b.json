{"ast": null, "code": "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { ENGINE } from '../engine';\nimport { DenseBincount } from '../kernel_names';\nimport { convertToTensor } from '../tensor_util_env';\nimport * as util from '../util';\nimport { op } from './operation';\n/**\n * Outputs a vector with length `size` and the same dtype as `weights`.\n *\n * If `weights` are empty, then index `i` stores the number of times the value\n * `i` is counted in `x`. If `weights` are non-empty, then index `i` stores the\n * sum of the value in `weights` at each index where the corresponding value in\n * `x` is `i`.\n *\n * Values in `x` outside of the range [0, size) are ignored.\n *\n * @param x The input int tensor, rank 1 or rank 2.\n * @param weights The weights tensor, must have the same shape as x, or a\n *     length-0 Tensor, in which case it acts as all weights equal to 1.\n * @param size Non-negative integer.\n * @param binaryOutput Optional. Whether the kernel should count the appearance\n *     or number of occurrences. Defaults to False.\n *\n * @doc {heading: 'Operations', subheading: 'Reduction'}\n */\nfunction denseBincount_(x, weights, size) {\n  let binaryOutput = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : false;\n  const $x = convertToTensor(x, 'x', 'denseBincount');\n  const $weights = convertToTensor(weights, 'weights', 'denseBincount');\n  util.assert($x.dtype === 'int32', () => \"Error in denseBincount: input \" + \"dtype must be int32, but got \".concat($x.dtype));\n  util.assert($x.rank <= 2, () => \"Error in denseBincount: input must be at most rank 2, but got \" + \"rank \".concat($x.rank, \".\"));\n  util.assert(size >= 0, () => \"size must be non-negative, but got \".concat(size, \".\"));\n  util.assert($weights.size === $x.size || $weights.size === 0, () => \"Error in denseBincount: weights must have the same shape as x or \" + \"0-length, but got x shape: \".concat($x.shape, \", weights shape: \") + \"\".concat($weights.shape, \".\"));\n  const inputs = {\n    x: $x,\n    weights: $weights\n  };\n  const attrs = {\n    size,\n    binaryOutput\n  };\n  return ENGINE.runKernel(DenseBincount, inputs, attrs);\n}\nexport const denseBincount = /* @__PURE__ */op({\n  denseBincount_\n});", "map": {"version": 3, "names": ["ENGINE", "DenseBincount", "convertToTensor", "util", "op", "denseBincount_", "x", "weights", "size", "binaryOutput", "arguments", "length", "undefined", "$x", "$weights", "assert", "dtype", "concat", "rank", "shape", "inputs", "attrs", "runKernel", "denseBincount"], "sources": ["C:\\tfjs-core\\src\\ops\\dense_bincount.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {ENGINE} from '../engine';\nimport {DenseBincount, DenseBincountAttrs, DenseBincountInputs} from '../kernel_names';\nimport {NamedAttrMap} from '../kernel_registry';\nimport {Tensor1D, Tensor2D} from '../tensor';\nimport {NamedTensorMap} from '../tensor_types';\nimport {convertToTensor} from '../tensor_util_env';\nimport {TensorLike} from '../types';\nimport * as util from '../util';\n\nimport {op} from './operation';\n\n/**\n * Outputs a vector with length `size` and the same dtype as `weights`.\n *\n * If `weights` are empty, then index `i` stores the number of times the value\n * `i` is counted in `x`. If `weights` are non-empty, then index `i` stores the\n * sum of the value in `weights` at each index where the corresponding value in\n * `x` is `i`.\n *\n * Values in `x` outside of the range [0, size) are ignored.\n *\n * @param x The input int tensor, rank 1 or rank 2.\n * @param weights The weights tensor, must have the same shape as x, or a\n *     length-0 Tensor, in which case it acts as all weights equal to 1.\n * @param size Non-negative integer.\n * @param binaryOutput Optional. Whether the kernel should count the appearance\n *     or number of occurrences. Defaults to False.\n *\n * @doc {heading: 'Operations', subheading: 'Reduction'}\n */\nfunction denseBincount_<T extends Tensor1D|Tensor2D>(\n    x: T|TensorLike, weights: T|TensorLike, size: number,\n    binaryOutput = false): T {\n  const $x = convertToTensor(x, 'x', 'denseBincount');\n  const $weights = convertToTensor(weights, 'weights', 'denseBincount');\n\n  util.assert(\n      $x.dtype === 'int32',\n      () => `Error in denseBincount: input ` +\n          `dtype must be int32, but got ${$x.dtype}`);\n  util.assert(\n      $x.rank <= 2,\n      () => `Error in denseBincount: input must be at most rank 2, but got ` +\n          `rank ${$x.rank}.`);\n  util.assert(size >= 0, () => `size must be non-negative, but got ${size}.`);\n  util.assert(\n      $weights.size === $x.size || $weights.size === 0,\n      () =>\n          `Error in denseBincount: weights must have the same shape as x or ` +\n          `0-length, but got x shape: ${$x.shape}, weights shape: ` +\n          `${$weights.shape}.`);\n\n  const inputs: DenseBincountInputs = {x: $x, weights: $weights};\n  const attrs: DenseBincountAttrs = {size, binaryOutput};\n\n  return ENGINE.runKernel(\n      DenseBincount, inputs as unknown as NamedTensorMap,\n      attrs as unknown as NamedAttrMap);\n}\n\nexport const denseBincount = /* @__PURE__ */ op({denseBincount_});\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAiBA,SAAQA,MAAM,QAAO,WAAW;AAChC,SAAQC,aAAa,QAAgD,iBAAiB;AAItF,SAAQC,eAAe,QAAO,oBAAoB;AAElD,OAAO,KAAKC,IAAI,MAAM,SAAS;AAE/B,SAAQC,EAAE,QAAO,aAAa;AAE9B;;;;;;;;;;;;;;;;;;;AAmBA,SAASC,cAAcA,CACnBC,CAAe,EAAEC,OAAqB,EAAEC,IAAY,EAChC;EAAA,IAApBC,YAAY,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,KAAK;EACtB,MAAMG,EAAE,GAAGX,eAAe,CAACI,CAAC,EAAE,GAAG,EAAE,eAAe,CAAC;EACnD,MAAMQ,QAAQ,GAAGZ,eAAe,CAACK,OAAO,EAAE,SAAS,EAAE,eAAe,CAAC;EAErEJ,IAAI,CAACY,MAAM,CACPF,EAAE,CAACG,KAAK,KAAK,OAAO,EACpB,MAAM,mEAAAC,MAAA,CAC8BJ,EAAE,CAACG,KAAK,CAAE,CAAC;EACnDb,IAAI,CAACY,MAAM,CACPF,EAAE,CAACK,IAAI,IAAI,CAAC,EACZ,MAAM,2EAAAD,MAAA,CACMJ,EAAE,CAACK,IAAI,MAAG,CAAC;EAC3Bf,IAAI,CAACY,MAAM,CAACP,IAAI,IAAI,CAAC,EAAE,4CAAAS,MAAA,CAA4CT,IAAI,MAAG,CAAC;EAC3EL,IAAI,CAACY,MAAM,CACPD,QAAQ,CAACN,IAAI,KAAKK,EAAE,CAACL,IAAI,IAAIM,QAAQ,CAACN,IAAI,KAAK,CAAC,EAChD,MACI,oGAAAS,MAAA,CAC8BJ,EAAE,CAACM,KAAK,sBAAmB,MAAAF,MAAA,CACtDH,QAAQ,CAACK,KAAK,MAAG,CAAC;EAE7B,MAAMC,MAAM,GAAwB;IAACd,CAAC,EAAEO,EAAE;IAAEN,OAAO,EAAEO;EAAQ,CAAC;EAC9D,MAAMO,KAAK,GAAuB;IAACb,IAAI;IAAEC;EAAY,CAAC;EAEtD,OAAOT,MAAM,CAACsB,SAAS,CACnBrB,aAAa,EAAEmB,MAAmC,EAClDC,KAAgC,CAAC;AACvC;AAEA,OAAO,MAAME,aAAa,GAAG,eAAgBnB,EAAE,CAAC;EAACC;AAAc,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}