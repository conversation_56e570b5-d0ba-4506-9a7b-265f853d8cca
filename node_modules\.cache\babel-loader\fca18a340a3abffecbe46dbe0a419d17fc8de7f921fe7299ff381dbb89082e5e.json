{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.transformArguments = void 0;\nconst generic_transformers_1 = require(\"@redis/client/dist/lib/commands/generic-transformers\");\nfunction transformArguments(dictionary, term) {\n  return (0, generic_transformers_1.pushVerdictArguments)(['FT.DICTDEL', dictionary], term);\n}\nexports.transformArguments = transformArguments;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "transformArguments", "generic_transformers_1", "require", "dictionary", "term", "pushVerdictArguments"], "sources": ["C:/tmsft/node_modules/@redis/search/dist/commands/DICTDEL.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.transformArguments = void 0;\nconst generic_transformers_1 = require(\"@redis/client/dist/lib/commands/generic-transformers\");\nfunction transformArguments(dictionary, term) {\n    return (0, generic_transformers_1.pushVerdictArguments)(['FT.DICTDEL', dictionary], term);\n}\nexports.transformArguments = transformArguments;\n"], "mappings": "AAAA,YAAY;;AACZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEC,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7DD,OAAO,CAACE,kBAAkB,GAAG,KAAK,CAAC;AACnC,MAAMC,sBAAsB,GAAGC,OAAO,CAAC,sDAAsD,CAAC;AAC9F,SAASF,kBAAkBA,CAACG,UAAU,EAAEC,IAAI,EAAE;EAC1C,OAAO,CAAC,CAAC,EAAEH,sBAAsB,CAACI,oBAAoB,EAAE,CAAC,YAAY,EAAEF,UAAU,CAAC,EAAEC,IAAI,CAAC;AAC7F;AACAN,OAAO,CAACE,kBAAkB,GAAGA,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}