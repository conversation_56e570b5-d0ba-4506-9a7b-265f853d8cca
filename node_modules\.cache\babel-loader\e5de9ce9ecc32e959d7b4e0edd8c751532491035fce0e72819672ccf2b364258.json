{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.transformReply = exports.transformArguments = exports.IS_READ_ONLY = exports.FIRST_KEY_INDEX = void 0;\nconst ZRANDMEMBER_COUNT_1 = require(\"./ZRANDMEMBER_COUNT\");\nvar ZRANDMEMBER_COUNT_2 = require(\"./ZRANDMEMBER_COUNT\");\nObject.defineProperty(exports, \"FIRST_KEY_INDEX\", {\n  enumerable: true,\n  get: function () {\n    return ZRANDMEMBER_COUNT_2.FIRST_KEY_INDEX;\n  }\n});\nObject.defineProperty(exports, \"IS_READ_ONLY\", {\n  enumerable: true,\n  get: function () {\n    return ZRANDMEMBER_COUNT_2.IS_READ_ONLY;\n  }\n});\nfunction transformArguments(...args) {\n  return [...(0, ZRANDMEMBER_COUNT_1.transformArguments)(...args), 'WITHSCORES'];\n}\nexports.transformArguments = transformArguments;\nvar generic_transformers_1 = require(\"./generic-transformers\");\nObject.defineProperty(exports, \"transformReply\", {\n  enumerable: true,\n  get: function () {\n    return generic_transformers_1.transformSortedSetWithScoresReply;\n  }\n});", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "transformReply", "transformArguments", "IS_READ_ONLY", "FIRST_KEY_INDEX", "ZRANDMEMBER_COUNT_1", "require", "ZRANDMEMBER_COUNT_2", "enumerable", "get", "args", "generic_transformers_1", "transformSortedSetWithScoresReply"], "sources": ["C:/tmsft/node_modules/@redis/client/dist/lib/commands/ZRANDMEMBER_COUNT_WITHSCORES.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.transformReply = exports.transformArguments = exports.IS_READ_ONLY = exports.FIRST_KEY_INDEX = void 0;\nconst ZRANDMEMBER_COUNT_1 = require(\"./ZRANDMEMBER_COUNT\");\nvar ZRANDMEMBER_COUNT_2 = require(\"./ZRANDMEMBER_COUNT\");\nObject.defineProperty(exports, \"FIRST_KEY_INDEX\", { enumerable: true, get: function () { return ZRANDMEMBER_COUNT_2.FIRST_KEY_INDEX; } });\nObject.defineProperty(exports, \"IS_READ_ONLY\", { enumerable: true, get: function () { return ZRANDMEMBER_COUNT_2.IS_READ_ONLY; } });\nfunction transformArguments(...args) {\n    return [\n        ...(0, ZRANDMEMBER_COUNT_1.transformArguments)(...args),\n        'WITHSCORES'\n    ];\n}\nexports.transformArguments = transformArguments;\nvar generic_transformers_1 = require(\"./generic-transformers\");\nObject.defineProperty(exports, \"transformReply\", { enumerable: true, get: function () { return generic_transformers_1.transformSortedSetWithScoresReply; } });\n"], "mappings": "AAAA,YAAY;;AACZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEC,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7DD,OAAO,CAACE,cAAc,GAAGF,OAAO,CAACG,kBAAkB,GAAGH,OAAO,CAACI,YAAY,GAAGJ,OAAO,CAACK,eAAe,GAAG,KAAK,CAAC;AAC7G,MAAMC,mBAAmB,GAAGC,OAAO,CAAC,qBAAqB,CAAC;AAC1D,IAAIC,mBAAmB,GAAGD,OAAO,CAAC,qBAAqB,CAAC;AACxDT,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,iBAAiB,EAAE;EAAES,UAAU,EAAE,IAAI;EAAEC,GAAG,EAAE,SAAAA,CAAA,EAAY;IAAE,OAAOF,mBAAmB,CAACH,eAAe;EAAE;AAAE,CAAC,CAAC;AACzIP,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,cAAc,EAAE;EAAES,UAAU,EAAE,IAAI;EAAEC,GAAG,EAAE,SAAAA,CAAA,EAAY;IAAE,OAAOF,mBAAmB,CAACJ,YAAY;EAAE;AAAE,CAAC,CAAC;AACnI,SAASD,kBAAkBA,CAAC,GAAGQ,IAAI,EAAE;EACjC,OAAO,CACH,GAAG,CAAC,CAAC,EAAEL,mBAAmB,CAACH,kBAAkB,EAAE,GAAGQ,IAAI,CAAC,EACvD,YAAY,CACf;AACL;AACAX,OAAO,CAACG,kBAAkB,GAAGA,kBAAkB;AAC/C,IAAIS,sBAAsB,GAAGL,OAAO,CAAC,wBAAwB,CAAC;AAC9DT,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,gBAAgB,EAAE;EAAES,UAAU,EAAE,IAAI;EAAEC,GAAG,EAAE,SAAAA,CAAA,EAAY;IAAE,OAAOE,sBAAsB,CAACC,iCAAiC;EAAE;AAAE,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}