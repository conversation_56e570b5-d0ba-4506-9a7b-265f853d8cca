{"ast": null, "code": "/**\n * @license\n * Copyright 2023 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nexport const json = [{\n  'tfOpName': 'StaticRegexReplace',\n  'category': 'string',\n  'inputs': [{\n    'start': 0,\n    'name': 'input',\n    'type': 'tensor'\n  }],\n  'attrs': [{\n    'tfName': 'pattern',\n    'name': 'pattern',\n    'type': 'string'\n  }, {\n    'tfName': 'rewrite',\n    'name': 'rewrite',\n    'type': 'string'\n  }, {\n    'tfName': 'replace_global',\n    'name': 'replaceGlobal',\n    'type': 'bool'\n  }]\n}, {\n  'tfOpName': 'StringNGrams',\n  'category': 'string',\n  'inputs': [{\n    'start': 0,\n    'name': 'data',\n    'type': 'tensor'\n  }, {\n    'start': 1,\n    'name': 'dataSplits',\n    'type': 'tensor'\n  }],\n  'attrs': [{\n    'tfName': 'separator',\n    'name': 'separator',\n    'type': 'string'\n  }, {\n    'tfName': 'ngram_widths',\n    'name': 'nGramWidths',\n    'type': 'number[]'\n  }, {\n    'tfName': 'left_pad',\n    'name': 'leftPad',\n    'type': 'string'\n  }, {\n    'tfName': 'right_pad',\n    'name': 'rightPad',\n    'type': 'string'\n  }, {\n    'tfName': 'pad_width',\n    'name': 'padWidth',\n    'type': 'number'\n  }, {\n    'tfName': 'preserve_short_sequences',\n    'name': 'preserveShortSequences',\n    'type': 'bool'\n  }],\n  'outputs': ['ngrams', 'ngrams_splits']\n}, {\n  'tfOpName': 'StringSplit',\n  'category': 'string',\n  'inputs': [{\n    'start': 0,\n    'name': 'input',\n    'type': 'tensor'\n  }, {\n    'start': 1,\n    'name': 'delimiter',\n    'type': 'tensor'\n  }],\n  'attrs': [{\n    'tfName': 'skip_empty',\n    'name': 'skipEmpty',\n    'type': 'bool'\n  }],\n  'outputs': ['indices', 'values', 'shape']\n}, {\n  'tfOpName': 'StringToHashBucketFast',\n  'category': 'string',\n  'inputs': [{\n    'start': 0,\n    'name': 'input',\n    'type': 'tensor'\n  }],\n  'attrs': [{\n    'tfName': 'num_buckets',\n    'name': 'numBuckets',\n    'type': 'number'\n  }]\n}];", "map": {"version": 3, "names": ["json"], "sources": ["C:\\tfjs-converter\\src\\operations\\op_list\\string.ts"], "sourcesContent": ["\n/**\n * @license\n * Copyright 2023 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {OpMapper} from '../types';\n\nexport const json: OpMapper[] = [\n  {\n    'tfOpName': 'StaticRegexReplace',\n    'category': 'string',\n    'inputs': [\n      {\n      'start': 0,\n        'name': 'input',\n        'type': 'tensor'\n      }\n    ],\n    'attrs': [\n      {\n        'tfName': 'pattern',\n        'name': 'pattern',\n        'type': 'string'\n      },\n      {\n        'tfName': 'rewrite',\n        'name': 'rewrite',\n        'type': 'string'\n      },\n      {\n        'tfName': 'replace_global',\n        'name': 'replaceGlobal',\n        'type': 'bool'\n      }\n    ]\n  },\n  {\n    'tfOpName': 'StringNGrams',\n    'category': 'string',\n    'inputs': [\n      {\n        'start': 0,\n        'name': 'data',\n        'type': 'tensor'\n      },\n      {\n        'start': 1,\n        'name': 'dataSplits',\n        'type': 'tensor'\n      }\n    ],\n    'attrs': [\n      {\n        'tfName': 'separator',\n        'name': 'separator',\n        'type': 'string'\n      },\n      {\n        'tfName': 'ngram_widths',\n        'name': 'nGramWidths',\n        'type': 'number[]'\n      },\n      {\n        'tfName': 'left_pad',\n        'name': 'leftPad',\n        'type': 'string'\n      },\n      {\n        'tfName': 'right_pad',\n        'name': 'rightPad',\n        'type': 'string'\n      },\n      {\n        'tfName': 'pad_width',\n        'name': 'padWidth',\n        'type': 'number'\n      },\n      {\n        'tfName': 'preserve_short_sequences',\n        'name': 'preserveShortSequences',\n        'type': 'bool'\n      }\n    ],\n    'outputs': [\n      'ngrams',\n      'ngrams_splits'\n    ]\n  },\n  {\n    'tfOpName': 'StringSplit',\n    'category': 'string',\n    'inputs': [\n      {\n        'start': 0,\n        'name': 'input',\n        'type': 'tensor'\n      },\n      {\n        'start': 1,\n        'name': 'delimiter',\n        'type': 'tensor'\n      }\n    ],\n    'attrs': [\n      {\n        'tfName': 'skip_empty',\n        'name': 'skipEmpty',\n        'type': 'bool'\n      }\n    ],\n    'outputs': [\n      'indices',\n      'values',\n      'shape'\n    ]\n  },\n  {\n    'tfOpName': 'StringToHashBucketFast',\n    'category': 'string',\n    'inputs': [\n      {\n        'start': 0,\n        'name': 'input',\n        'type': 'tensor'\n      }\n    ],\n    'attrs': [\n      {\n        'tfName': 'num_buckets',\n        'name': 'numBuckets',\n        'type': 'number'\n      }\n    ]\n  }\n]\n;\n"], "mappings": "AACA;;;;;;;;;;;;;;;;AAmBA,OAAO,MAAMA,IAAI,GAAe,CAC9B;EACE,UAAU,EAAE,oBAAoB;EAChC,UAAU,EAAE,QAAQ;EACpB,QAAQ,EAAE,CACR;IACA,OAAO,EAAE,CAAC;IACR,MAAM,EAAE,OAAO;IACf,MAAM,EAAE;GACT,CACF;EACD,OAAO,EAAE,CACP;IACE,QAAQ,EAAE,SAAS;IACnB,MAAM,EAAE,SAAS;IACjB,MAAM,EAAE;GACT,EACD;IACE,QAAQ,EAAE,SAAS;IACnB,MAAM,EAAE,SAAS;IACjB,MAAM,EAAE;GACT,EACD;IACE,QAAQ,EAAE,gBAAgB;IAC1B,MAAM,EAAE,eAAe;IACvB,MAAM,EAAE;GACT;CAEJ,EACD;EACE,UAAU,EAAE,cAAc;EAC1B,UAAU,EAAE,QAAQ;EACpB,QAAQ,EAAE,CACR;IACE,OAAO,EAAE,CAAC;IACV,MAAM,EAAE,MAAM;IACd,MAAM,EAAE;GACT,EACD;IACE,OAAO,EAAE,CAAC;IACV,MAAM,EAAE,YAAY;IACpB,MAAM,EAAE;GACT,CACF;EACD,OAAO,EAAE,CACP;IACE,QAAQ,EAAE,WAAW;IACrB,MAAM,EAAE,WAAW;IACnB,MAAM,EAAE;GACT,EACD;IACE,QAAQ,EAAE,cAAc;IACxB,MAAM,EAAE,aAAa;IACrB,MAAM,EAAE;GACT,EACD;IACE,QAAQ,EAAE,UAAU;IACpB,MAAM,EAAE,SAAS;IACjB,MAAM,EAAE;GACT,EACD;IACE,QAAQ,EAAE,WAAW;IACrB,MAAM,EAAE,UAAU;IAClB,MAAM,EAAE;GACT,EACD;IACE,QAAQ,EAAE,WAAW;IACrB,MAAM,EAAE,UAAU;IAClB,MAAM,EAAE;GACT,EACD;IACE,QAAQ,EAAE,0BAA0B;IACpC,MAAM,EAAE,wBAAwB;IAChC,MAAM,EAAE;GACT,CACF;EACD,SAAS,EAAE,CACT,QAAQ,EACR,eAAe;CAElB,EACD;EACE,UAAU,EAAE,aAAa;EACzB,UAAU,EAAE,QAAQ;EACpB,QAAQ,EAAE,CACR;IACE,OAAO,EAAE,CAAC;IACV,MAAM,EAAE,OAAO;IACf,MAAM,EAAE;GACT,EACD;IACE,OAAO,EAAE,CAAC;IACV,MAAM,EAAE,WAAW;IACnB,MAAM,EAAE;GACT,CACF;EACD,OAAO,EAAE,CACP;IACE,QAAQ,EAAE,YAAY;IACtB,MAAM,EAAE,WAAW;IACnB,MAAM,EAAE;GACT,CACF;EACD,SAAS,EAAE,CACT,SAAS,EACT,QAAQ,EACR,OAAO;CAEV,EACD;EACE,UAAU,EAAE,wBAAwB;EACpC,UAAU,EAAE,QAAQ;EACpB,QAAQ,EAAE,CACR;IACE,OAAO,EAAE,CAAC;IACV,MAAM,EAAE,OAAO;IACf,MAAM,EAAE;GACT,CACF;EACD,OAAO,EAAE,CACP;IACE,QAAQ,EAAE,aAAa;IACvB,MAAM,EAAE,YAAY;IACpB,MAAM,EAAE;GACT;CAEJ,CACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}