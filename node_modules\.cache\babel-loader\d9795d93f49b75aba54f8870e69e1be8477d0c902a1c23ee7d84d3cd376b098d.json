{"ast": null, "code": "/**\n * @license\n * Copyright 2018 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { ENGINE } from '../engine';\nimport { Rsqrt } from '../kernel_names';\nimport { convertToTensor } from '../tensor_util_env';\nimport { op } from './operation';\n/**\n * Computes reciprocal of square root of the input `tf.Tensor` element-wise:\n * `y = 1 / sqrt(x)`\n *\n * ```js\n * const x = tf.tensor1d([1, 2, 4, -1]);\n *\n * x.rsqrt().print();  // or tf.rsqrt(x)\n * ```\n * @param x The input tensor.\n *\n * @doc {heading: 'Operations', subheading: 'Basic math'}\n */\nfunction rsqrt_(x) {\n  const $x = convertToTensor(x, 'x', 'rsqrt', 'float32');\n  const inputs = {\n    x: $x\n  };\n  return ENGINE.runKernel(Rsqrt, inputs);\n}\nexport const rsqrt = /* @__PURE__ */op({\n  rsqrt_\n});", "map": {"version": 3, "names": ["ENGINE", "Rsqrt", "convertToTensor", "op", "rsqrt_", "x", "$x", "inputs", "runKernel", "rsqrt"], "sources": ["C:\\tfjs-core\\src\\ops\\rsqrt.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2018 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {ENGINE} from '../engine';\nimport {Rsqrt, RsqrtInputs} from '../kernel_names';\nimport {Tensor} from '../tensor';\nimport {NamedTensorMap} from '../tensor_types';\nimport {convertToTensor} from '../tensor_util_env';\nimport {TensorLike} from '../types';\n\nimport {op} from './operation';\n\n/**\n * Computes reciprocal of square root of the input `tf.Tensor` element-wise:\n * `y = 1 / sqrt(x)`\n *\n * ```js\n * const x = tf.tensor1d([1, 2, 4, -1]);\n *\n * x.rsqrt().print();  // or tf.rsqrt(x)\n * ```\n * @param x The input tensor.\n *\n * @doc {heading: 'Operations', subheading: 'Basic math'}\n */\nfunction rsqrt_<T extends Tensor>(x: T|TensorLike): T {\n  const $x = convertToTensor(x, 'x', 'rsqrt', 'float32');\n\n  const inputs: RsqrtInputs = {x: $x};\n\n  return ENGINE.runKernel(Rsqrt, inputs as unknown as NamedTensorMap);\n}\nexport const rsqrt = /* @__PURE__ */ op({rsqrt_});\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAiBA,SAAQA,MAAM,QAAO,WAAW;AAChC,SAAQC,KAAK,QAAoB,iBAAiB;AAGlD,SAAQC,eAAe,QAAO,oBAAoB;AAGlD,SAAQC,EAAE,QAAO,aAAa;AAE9B;;;;;;;;;;;;;AAaA,SAASC,MAAMA,CAAmBC,CAAe;EAC/C,MAAMC,EAAE,GAAGJ,eAAe,CAACG,CAAC,EAAE,GAAG,EAAE,OAAO,EAAE,SAAS,CAAC;EAEtD,MAAME,MAAM,GAAgB;IAACF,CAAC,EAAEC;EAAE,CAAC;EAEnC,OAAON,MAAM,CAACQ,SAAS,CAACP,KAAK,EAAEM,MAAmC,CAAC;AACrE;AACA,OAAO,MAAME,KAAK,GAAG,eAAgBN,EAAE,CAAC;EAACC;AAAM,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}