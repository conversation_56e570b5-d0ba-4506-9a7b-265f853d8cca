{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.transformArguments = exports.IS_READ_ONLY = void 0;\nexports.IS_READ_ONLY = true;\nfunction transformArguments(key, prefix, options) {\n  const args = ['FT.SUGGET', key, prefix];\n  if (options?.FUZZY) {\n    args.push('FUZZY');\n  }\n  if (options?.MAX) {\n    args.push('MAX', options.MAX.toString());\n  }\n  return args;\n}\nexports.transformArguments = transformArguments;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "transformArguments", "IS_READ_ONLY", "key", "prefix", "options", "args", "FUZZY", "push", "MAX", "toString"], "sources": ["C:/tmsft/node_modules/@redis/search/dist/commands/SUGGET.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.transformArguments = exports.IS_READ_ONLY = void 0;\nexports.IS_READ_ONLY = true;\nfunction transformArguments(key, prefix, options) {\n    const args = ['FT.SUGGET', key, prefix];\n    if (options?.FUZZY) {\n        args.push('FUZZY');\n    }\n    if (options?.MAX) {\n        args.push('MAX', options.MAX.toString());\n    }\n    return args;\n}\nexports.transformArguments = transformArguments;\n"], "mappings": "AAAA,YAAY;;AACZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEC,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7DD,OAAO,CAACE,kBAAkB,GAAGF,OAAO,CAACG,YAAY,GAAG,KAAK,CAAC;AAC1DH,OAAO,CAACG,YAAY,GAAG,IAAI;AAC3B,SAASD,kBAAkBA,CAACE,GAAG,EAAEC,MAAM,EAAEC,OAAO,EAAE;EAC9C,MAAMC,IAAI,GAAG,CAAC,WAAW,EAAEH,GAAG,EAAEC,MAAM,CAAC;EACvC,IAAIC,OAAO,EAAEE,KAAK,EAAE;IAChBD,IAAI,CAACE,IAAI,CAAC,OAAO,CAAC;EACtB;EACA,IAAIH,OAAO,EAAEI,GAAG,EAAE;IACdH,IAAI,CAACE,IAAI,CAAC,KAAK,EAAEH,OAAO,CAACI,GAAG,CAACC,QAAQ,CAAC,CAAC,CAAC;EAC5C;EACA,OAAOJ,IAAI;AACf;AACAP,OAAO,CAACE,kBAAkB,GAAGA,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}