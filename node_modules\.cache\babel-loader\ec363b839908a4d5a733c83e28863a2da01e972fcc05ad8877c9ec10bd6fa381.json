{"ast": null, "code": "import partial from './partial.js';\nimport delay from './delay.js';\nimport _ from './underscore.js';\n\n// Defers a function, scheduling it to run after the current call stack has\n// cleared.\nexport default partial(delay, _, 1);", "map": {"version": 3, "names": ["partial", "delay", "_"], "sources": ["C:/tmsft/node_modules/underscore/modules/defer.js"], "sourcesContent": ["import partial from './partial.js';\nimport delay from './delay.js';\nimport _ from './underscore.js';\n\n// Defers a function, scheduling it to run after the current call stack has\n// cleared.\nexport default partial(delay, _, 1);\n"], "mappings": "AAAA,OAAOA,OAAO,MAAM,cAAc;AAClC,OAAOC,KAAK,MAAM,YAAY;AAC9B,OAAOC,CAAC,MAAM,iBAAiB;;AAE/B;AACA;AACA,eAAeF,OAAO,CAACC,KAAK,EAAEC,CAAC,EAAE,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}