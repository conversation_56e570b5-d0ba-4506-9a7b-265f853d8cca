{"ast": null, "code": "/*\nFeature set class for administrating a set of unique features\nCopyright (C) 2017, 2023 Hugo <PERSON>t\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in\nall copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\nTHE SOFTWARE.\n*/\n\n'use strict';\n\nclass FeatureSet {\n  constructor() {\n    this.features = [];\n    this.map = {};\n  }\n\n  // Returns true if the feature did not exist and was added\n  addFeature(feature) {\n    if (!this.featureExists(feature)) {\n      this.map[feature.name + ' | ' + feature.parametersKey] = true;\n      this.features.push(feature);\n      // console.log(\"FeatureSet.addFeature: feature added: \" + feature.name + \" - \" + feature.parametersKey);\n      return true;\n    } else {\n      return false;\n    }\n  }\n  featureExists(feature) {\n    if (this.map[feature.name + ' | ' + feature.parametersKey]) {\n      // console.log(\"FeatureSet.featureExists: feature already exists: \" +\n      //  feature.name + \" - \" + feature.parameters);\n      return true;\n    } else {\n      return false;\n    }\n  }\n\n  // Returns an array of features\n  // If the available array this.features is up to date it is returned immediately\n  getFeatures() {\n    return this.features;\n  }\n  size() {\n    return this.features.length;\n  }\n  prettyPrint() {\n    let s = '';\n    Object.keys(this.map).forEach(function (key) {\n      s += key + '\\n';\n    });\n    return s;\n  }\n}\nmodule.exports = FeatureSet;", "map": {"version": 3, "names": ["FeatureSet", "constructor", "features", "map", "addFeature", "feature", "featureExists", "name", "<PERSON><PERSON><PERSON>", "push", "getFeatures", "size", "length", "<PERSON><PERSON><PERSON><PERSON>", "s", "Object", "keys", "for<PERSON>ach", "key", "module", "exports"], "sources": ["C:/tmsft/node_modules/natural/lib/natural/classifiers/maxent/FeatureSet.js"], "sourcesContent": ["/*\nFeature set class for administrating a set of unique features\nCopyright (C) 2017, 2023 Hugo <PERSON>t\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in\nall copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\nTHE SOFTWARE.\n*/\n\n'use strict'\n\nclass FeatureSet {\n  constructor () {\n    this.features = []\n    this.map = {}\n  }\n\n  // Returns true if the feature did not exist and was added\n  addFeature (feature) {\n    if (!this.featureExists(feature)) {\n      this.map[feature.name + ' | ' + feature.parametersKey] = true\n      this.features.push(feature)\n      // console.log(\"FeatureSet.addFeature: feature added: \" + feature.name + \" - \" + feature.parametersKey);\n      return true\n    } else {\n      return false\n    }\n  }\n\n  featureExists (feature) {\n    if (this.map[feature.name + ' | ' + feature.parametersKey]) {\n      // console.log(\"FeatureSet.featureExists: feature already exists: \" +\n      //  feature.name + \" - \" + feature.parameters);\n      return true\n    } else {\n      return false\n    }\n  }\n\n  // Returns an array of features\n  // If the available array this.features is up to date it is returned immediately\n  getFeatures () {\n    return this.features\n  }\n\n  size () {\n    return this.features.length\n  }\n\n  prettyPrint () {\n    let s = ''\n    Object.keys(this.map).forEach(function (key) {\n      s += key + '\\n'\n    })\n    return s\n  }\n}\n\nmodule.exports = FeatureSet\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,YAAY;;AAEZ,MAAMA,UAAU,CAAC;EACfC,WAAWA,CAAA,EAAI;IACb,IAAI,CAACC,QAAQ,GAAG,EAAE;IAClB,IAAI,CAACC,GAAG,GAAG,CAAC,CAAC;EACf;;EAEA;EACAC,UAAUA,CAAEC,OAAO,EAAE;IACnB,IAAI,CAAC,IAAI,CAACC,aAAa,CAACD,OAAO,CAAC,EAAE;MAChC,IAAI,CAACF,GAAG,CAACE,OAAO,CAACE,IAAI,GAAG,KAAK,GAAGF,OAAO,CAACG,aAAa,CAAC,GAAG,IAAI;MAC7D,IAAI,CAACN,QAAQ,CAACO,IAAI,CAACJ,OAAO,CAAC;MAC3B;MACA,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,KAAK;IACd;EACF;EAEAC,aAAaA,CAAED,OAAO,EAAE;IACtB,IAAI,IAAI,CAACF,GAAG,CAACE,OAAO,CAACE,IAAI,GAAG,KAAK,GAAGF,OAAO,CAACG,aAAa,CAAC,EAAE;MAC1D;MACA;MACA,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,KAAK;IACd;EACF;;EAEA;EACA;EACAE,WAAWA,CAAA,EAAI;IACb,OAAO,IAAI,CAACR,QAAQ;EACtB;EAEAS,IAAIA,CAAA,EAAI;IACN,OAAO,IAAI,CAACT,QAAQ,CAACU,MAAM;EAC7B;EAEAC,WAAWA,CAAA,EAAI;IACb,IAAIC,CAAC,GAAG,EAAE;IACVC,MAAM,CAACC,IAAI,CAAC,IAAI,CAACb,GAAG,CAAC,CAACc,OAAO,CAAC,UAAUC,GAAG,EAAE;MAC3CJ,CAAC,IAAII,GAAG,GAAG,IAAI;IACjB,CAAC,CAAC;IACF,OAAOJ,CAAC;EACV;AACF;AAEAK,MAAM,CAACC,OAAO,GAAGpB,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}