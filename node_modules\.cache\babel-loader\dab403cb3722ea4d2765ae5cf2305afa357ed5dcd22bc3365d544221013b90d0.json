{"ast": null, "code": "/**\n * @license\n * Copyright 2019 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { mul } from '../mul';\nimport { op } from '../operation';\nimport { enclosingPowerOfTwo } from '../signal_ops_util';\nimport { rfft } from '../spectral/rfft';\nimport { frame } from './frame';\nimport { hannWindow } from './hann_window';\n/**\n * Computes the Short-time Fourier Transform of signals\n * See: https://en.wikipedia.org/wiki/Short-time_Fourier_transform\n *\n * ```js\n * const input = tf.tensor1d([1, 1, 1, 1, 1])\n * tf.signal.stft(input, 3, 1).print();\n * ```\n * @param signal 1-dimensional real value tensor.\n * @param frameLength The window length of samples.\n * @param frameStep The number of samples to step.\n * @param fftLength The size of the FFT to apply.\n * @param windowFn A callable that takes a window length and returns 1-d tensor.\n *\n * @doc {heading: 'Operations', subheading: 'Signal', namespace: 'signal'}\n */\nfunction stft_(signal, frameLength, frameStep, fftLength) {\n  let windowFn = arguments.length > 4 && arguments[4] !== undefined ? arguments[4] : hannWindow;\n  if (fftLength == null) {\n    fftLength = enclosingPowerOfTwo(frameLength);\n  }\n  const framedSignal = frame(signal, frameLength, frameStep);\n  const windowedSignal = mul(framedSignal, windowFn(frameLength));\n  return rfft(windowedSignal, fftLength);\n}\nexport const stft = /* @__PURE__ */op({\n  stft_\n});", "map": {"version": 3, "names": ["mul", "op", "enclosingPowerOfTwo", "rfft", "frame", "hann<PERSON><PERSON><PERSON>", "stft_", "signal", "frameLength", "frameStep", "fftLength", "windowFn", "arguments", "length", "undefined", "framedSignal", "windowedSignal", "stft"], "sources": ["C:\\tfjs-core\\src\\ops\\signal\\stft.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2019 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {Tensor, Tensor1D} from '../../tensor';\nimport {mul} from '../mul';\nimport {op} from '../operation';\nimport {enclosingPowerOfTwo} from '../signal_ops_util';\nimport {rfft} from '../spectral/rfft';\n\nimport {frame} from './frame';\nimport {hannWindow} from './hann_window';\n\n/**\n * Computes the Short-time Fourier Transform of signals\n * See: https://en.wikipedia.org/wiki/Short-time_Fourier_transform\n *\n * ```js\n * const input = tf.tensor1d([1, 1, 1, 1, 1])\n * tf.signal.stft(input, 3, 1).print();\n * ```\n * @param signal 1-dimensional real value tensor.\n * @param frameLength The window length of samples.\n * @param frameStep The number of samples to step.\n * @param fftLength The size of the FFT to apply.\n * @param windowFn A callable that takes a window length and returns 1-d tensor.\n *\n * @doc {heading: 'Operations', subheading: 'Signal', namespace: 'signal'}\n */\nfunction stft_(\n    signal: Tensor1D, frameLength: number, frameStep: number,\n    fftLength?: number,\n    windowFn: (length: number) => Tensor1D = hannWindow): Tensor {\n  if (fftLength == null) {\n    fftLength = enclosingPowerOfTwo(frameLength);\n  }\n  const framedSignal = frame(signal, frameLength, frameStep);\n  const windowedSignal = mul(framedSignal, windowFn(frameLength));\n  return rfft(windowedSignal, fftLength);\n}\nexport const stft = /* @__PURE__ */ op({stft_});\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAkBA,SAAQA,GAAG,QAAO,QAAQ;AAC1B,SAAQC,EAAE,QAAO,cAAc;AAC/B,SAAQC,mBAAmB,QAAO,oBAAoB;AACtD,SAAQC,IAAI,QAAO,kBAAkB;AAErC,SAAQC,KAAK,QAAO,SAAS;AAC7B,SAAQC,UAAU,QAAO,eAAe;AAExC;;;;;;;;;;;;;;;;AAgBA,SAASC,KAAKA,CACVC,MAAgB,EAAEC,WAAmB,EAAEC,SAAiB,EACxDC,SAAkB,EACiC;EAAA,IAAnDC,QAAA,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAyCP,UAAU;EACrD,IAAIK,SAAS,IAAI,IAAI,EAAE;IACrBA,SAAS,GAAGR,mBAAmB,CAACM,WAAW,CAAC;;EAE9C,MAAMO,YAAY,GAAGX,KAAK,CAACG,MAAM,EAAEC,WAAW,EAAEC,SAAS,CAAC;EAC1D,MAAMO,cAAc,GAAGhB,GAAG,CAACe,YAAY,EAAEJ,QAAQ,CAACH,WAAW,CAAC,CAAC;EAC/D,OAAOL,IAAI,CAACa,cAAc,EAAEN,SAAS,CAAC;AACxC;AACA,OAAO,MAAMO,IAAI,GAAG,eAAgBhB,EAAE,CAAC;EAACK;AAAK,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}