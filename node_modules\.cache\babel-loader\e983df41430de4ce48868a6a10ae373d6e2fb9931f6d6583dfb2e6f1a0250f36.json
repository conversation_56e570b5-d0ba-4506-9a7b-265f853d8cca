{"ast": null, "code": "import { fileStorageService } from './fileStorageService';\nclass ARAgingManagementService {\n  constructor() {\n    this.AR_RECORDS_KEY = 'ar_records';\n    this.CUSTOMERS_KEY = 'customers';\n    this.COLLECTION_ACTIONS_KEY = 'collection_actions';\n    this.AR_AGING_REPORTS_KEY = 'ar_aging_reports';\n  }\n  // ============ AR RECORD MANAGEMENT ============\n\n  // Create new AR record\n  createARRecord(arData) {\n    const arRecord = {\n      ...arData,\n      id: this.generateId(),\n      agingDays: this.calculateAgingDays(arData.invoiceDate),\n      reconciliationStatus: 'unmatched',\n      matchedTransactionIds: []\n    };\n\n    // Store AR record\n    const arRecords = this.getAllARRecords();\n    arRecords.push(arRecord);\n    fileStorageService.writeData(this.AR_RECORDS_KEY, arRecords);\n\n    // Update customer if needed\n    this.updateCustomerRecord(arData.customerId, arData.customerName);\n    return arRecord;\n  }\n\n  // Update AR record\n  updateARRecord(arRecordId, updates) {\n    try {\n      const arRecords = this.getAllARRecords();\n      const index = arRecords.findIndex(ar => ar.id === arRecordId);\n      if (index === -1) return false;\n\n      // Recalculate aging days if invoice date changed\n      if (updates.invoiceDate) {\n        updates.agingDays = this.calculateAgingDays(updates.invoiceDate);\n      }\n      arRecords[index] = {\n        ...arRecords[index],\n        ...updates\n      };\n      fileStorageService.writeData(this.AR_RECORDS_KEY, arRecords);\n      return true;\n    } catch (error) {\n      console.error('Error updating AR record:', error);\n      return false;\n    }\n  }\n\n  // Delete AR record\n  deleteARRecord(arRecordId) {\n    try {\n      const arRecords = this.getAllARRecords();\n      const filteredRecords = arRecords.filter(ar => ar.id !== arRecordId);\n      if (filteredRecords.length === arRecords.length) return false;\n      fileStorageService.writeData(this.AR_RECORDS_KEY, filteredRecords);\n      return true;\n    } catch (error) {\n      console.error('Error deleting AR record:', error);\n      return false;\n    }\n  }\n\n  // ============ AGING CALCULATIONS ============\n\n  // Calculate aging days for an invoice\n  calculateAgingDays(invoiceDate) {\n    const invoice = this.parseDate(invoiceDate);\n    const today = new Date();\n    const diffTime = today.getTime() - invoice.getTime();\n    return Math.floor(diffTime / (1000 * 60 * 60 * 24));\n  }\n\n  // Update aging for all AR records\n  updateAllAgingDays() {\n    const arRecords = this.getAllARRecords();\n    let updated = false;\n    arRecords.forEach(ar => {\n      const newAgingDays = this.calculateAgingDays(ar.invoiceDate);\n      if (ar.agingDays !== newAgingDays) {\n        ar.agingDays = newAgingDays;\n        updated = true;\n      }\n    });\n    if (updated) {\n      fileStorageService.writeData(this.AR_RECORDS_KEY, arRecords);\n    }\n  }\n\n  // Calculate aging bucket for amount\n  calculateAgingBucket(agingDays, amount) {\n    if (agingDays <= 30) return {\n      current: amount\n    };\n    if (agingDays <= 60) return {\n      days31to60: amount\n    };\n    if (agingDays <= 90) return {\n      days61to90: amount\n    };\n    if (agingDays <= 120) return {\n      days91to120: amount\n    };\n    return {\n      over120: amount\n    };\n  }\n\n  // ============ CUSTOMER MANAGEMENT ============\n\n  // Update or create customer record\n  updateCustomerRecord(customerId, customerName) {\n    const customers = this.getCustomers();\n    const existingIndex = customers.findIndex(c => c.id === customerId);\n    if (existingIndex === -1) {\n      // Create new customer\n      const newCustomer = {\n        id: customerId,\n        name: customerName,\n        contactInfo: {\n          email: '',\n          phone: '',\n          address: ''\n        },\n        creditLimit: 0,\n        paymentTerms: 30,\n        riskRating: 'medium',\n        isActive: true\n      };\n      customers.push(newCustomer);\n    } else {\n      // Update existing customer name if different\n      if (customers[existingIndex].name !== customerName) {\n        customers[existingIndex].name = customerName;\n      }\n    }\n    fileStorageService.writeData(this.CUSTOMERS_KEY, customers);\n  }\n\n  // Get customer AR summary\n  getCustomerARSummary(customerId) {\n    const customer = this.getCustomers().find(c => c.id === customerId);\n    if (!customer) return null;\n    const customerARs = this.getAllARRecords().filter(ar => ar.customerId === customerId && ar.status === 'outstanding');\n    if (customerARs.length === 0) {\n      return {\n        customerId,\n        customerName: customer.name,\n        totalOutstanding: 0,\n        agingBuckets: {\n          current: 0,\n          days31to60: 0,\n          days61to90: 0,\n          days91to120: 0,\n          over120: 0\n        },\n        oldestInvoiceDate: '',\n        averageAgingDays: 0,\n        collectionRisk: 'low',\n        creditLimit: customer.creditLimit,\n        currency: 'SAR'\n      };\n    }\n\n    // Calculate totals and aging buckets\n    let totalOutstanding = 0;\n    const agingBuckets = {\n      current: 0,\n      days31to60: 0,\n      days61to90: 0,\n      days91to120: 0,\n      over120: 0\n    };\n    let totalAgingDays = 0;\n    let oldestDate = customerARs[0].invoiceDate;\n    customerARs.forEach(ar => {\n      totalOutstanding += ar.amount;\n      totalAgingDays += ar.agingDays;\n      const bucket = this.calculateAgingBucket(ar.agingDays, ar.amount);\n      Object.keys(bucket).forEach(key => {\n        agingBuckets[key] += bucket[key] || 0;\n      });\n      if (this.parseDate(ar.invoiceDate) < this.parseDate(oldestDate)) {\n        oldestDate = ar.invoiceDate;\n      }\n    });\n    const averageAgingDays = totalAgingDays / customerARs.length;\n    const collectionRisk = this.assessCollectionRisk(averageAgingDays, totalOutstanding, customer.creditLimit || 0);\n\n    // Get last payment info\n    const lastPayment = this.getLastPaymentInfo(customerId);\n    return {\n      customerId,\n      customerName: customer.name,\n      totalOutstanding,\n      agingBuckets,\n      oldestInvoiceDate: oldestDate,\n      averageAgingDays,\n      collectionRisk,\n      lastPaymentDate: lastPayment === null || lastPayment === void 0 ? void 0 : lastPayment.date,\n      lastPaymentAmount: lastPayment === null || lastPayment === void 0 ? void 0 : lastPayment.amount,\n      creditLimit: customer.creditLimit,\n      currency: customerARs[0].currency\n    };\n  }\n\n  // ============ AGING REPORTS ============\n\n  // Generate comprehensive AR aging report\n  generateARAgingReport(currency = 'SAR') {\n    this.updateAllAgingDays(); // Ensure aging is current\n\n    const outstandingARs = this.getAllARRecords().filter(ar => ar.status === 'outstanding' && ar.currency === currency);\n\n    // Calculate total aging buckets\n    const totalAgingBuckets = {\n      current: 0,\n      days31to60: 0,\n      days61to90: 0,\n      days91to120: 0,\n      over120: 0\n    };\n    let totalOutstanding = 0;\n    outstandingARs.forEach(ar => {\n      totalOutstanding += ar.amount;\n      const bucket = this.calculateAgingBucket(ar.agingDays, ar.amount);\n      Object.keys(bucket).forEach(key => {\n        totalAgingBuckets[key] += bucket[key] || 0;\n      });\n    });\n\n    // Generate customer summaries\n    const customerIds = [...new Set(outstandingARs.map(ar => ar.customerId))];\n    const customerSummaries = [];\n    customerIds.forEach(customerId => {\n      const summary = this.getCustomerARSummary(customerId);\n      if (summary && summary.totalOutstanding > 0) {\n        customerSummaries.push(summary);\n      }\n    });\n\n    // Calculate risk analysis\n    const riskAnalysis = {\n      lowRisk: customerSummaries.filter(c => c.collectionRisk === 'low').length,\n      mediumRisk: customerSummaries.filter(c => c.collectionRisk === 'medium').length,\n      highRisk: customerSummaries.filter(c => c.collectionRisk === 'high').length,\n      criticalRisk: customerSummaries.filter(c => c.collectionRisk === 'critical').length\n    };\n\n    // Calculate collection metrics\n    const collectionMetrics = this.calculateCollectionMetrics(currency);\n    const report = {\n      reportDate: new Date().toISOString().split('T')[0],\n      currency,\n      totalOutstanding,\n      totalAgingBuckets,\n      customerSummaries: customerSummaries.sort((a, b) => b.totalOutstanding - a.totalOutstanding),\n      riskAnalysis,\n      collectionMetrics\n    };\n\n    // Store report\n    this.storeARAgingReport(report);\n    return report;\n  }\n\n  // ============ COLLECTION ACTIONS ============\n\n  // Create collection action\n  createCollectionAction(actionData) {\n    const action = {\n      ...actionData,\n      id: this.generateId()\n    };\n    const actions = this.getCollectionActions();\n    actions.push(action);\n    fileStorageService.writeData(this.COLLECTION_ACTIONS_KEY, actions);\n    return action;\n  }\n\n  // Update collection action\n  updateCollectionAction(actionId, updates) {\n    try {\n      const actions = this.getCollectionActions();\n      const index = actions.findIndex(a => a.id === actionId);\n      if (index === -1) return false;\n      actions[index] = {\n        ...actions[index],\n        ...updates\n      };\n      fileStorageService.writeData(this.COLLECTION_ACTIONS_KEY, actions);\n      return true;\n    } catch (error) {\n      console.error('Error updating collection action:', error);\n      return false;\n    }\n  }\n\n  // Generate automatic collection actions based on aging\n  generateAutomaticCollectionActions() {\n    const outstandingARs = this.getAllARRecords().filter(ar => ar.status === 'outstanding');\n    const newActions = [];\n    outstandingARs.forEach(ar => {\n      const existingActions = this.getCollectionActions().filter(a => a.arRecordId === ar.id);\n      const lastAction = existingActions.sort((a, b) => this.parseDate(b.actionDate).getTime() - this.parseDate(a.actionDate).getTime())[0];\n\n      // Determine if new action is needed\n      const actionNeeded = this.determineCollectionAction(ar, lastAction);\n      if (actionNeeded) {\n        const action = this.createCollectionAction({\n          arRecordId: ar.id,\n          customerId: ar.customerId,\n          actionType: actionNeeded.type,\n          actionDate: new Date().toISOString().split('T')[0],\n          dueDate: actionNeeded.dueDate,\n          description: actionNeeded.description,\n          assignedTo: 'system',\n          status: 'pending',\n          priority: actionNeeded.priority,\n          notes: []\n        });\n        newActions.push(action);\n      }\n    });\n    return newActions;\n  }\n\n  // ============ RECONCILIATION INTEGRATION ============\n\n  // Mark AR as collected (called from credit transactions service)\n  markARAsCollected(arRecordId, transactionId, amount) {\n    try {\n      const arRecords = this.getAllARRecords();\n      const index = arRecords.findIndex(ar => ar.id === arRecordId);\n      if (index === -1) return false;\n      const ar = arRecords[index];\n\n      // Add transaction to matched list\n      if (!ar.matchedTransactionIds.includes(transactionId)) {\n        ar.matchedTransactionIds.push(transactionId);\n      }\n\n      // Calculate total collected amount\n      const totalCollected = ar.matchedTransactionIds.length * amount; // Simplified - should get actual amounts\n\n      // Update status based on collection\n      if (totalCollected >= ar.amount * 0.99) {\n        ar.status = 'collected';\n        ar.reconciliationStatus = 'verified';\n      } else if (totalCollected > 0) {\n        ar.status = 'partially_collected';\n        ar.reconciliationStatus = 'partially_matched';\n      }\n      arRecords[index] = ar;\n      fileStorageService.writeData(this.AR_RECORDS_KEY, arRecords);\n      return true;\n    } catch (error) {\n      console.error('Error marking AR as collected:', error);\n      return false;\n    }\n  }\n\n  // ============ DATA RETRIEVAL METHODS ============\n\n  getAllARRecords() {\n    return fileStorageService.readData(this.AR_RECORDS_KEY, []);\n  }\n  getOutstandingARRecords() {\n    return this.getAllARRecords().filter(ar => ar.status === 'outstanding');\n  }\n  getOverdueARRecords() {\n    return this.getAllARRecords().filter(ar => ar.status === 'overdue');\n  }\n  getCustomers() {\n    return fileStorageService.readData(this.CUSTOMERS_KEY, []);\n  }\n  getCollectionActions() {\n    return fileStorageService.readData(this.COLLECTION_ACTIONS_KEY, []);\n  }\n  getARAgingReports() {\n    return fileStorageService.readData(this.AR_AGING_REPORTS_KEY, []);\n  }\n\n  // ============ UTILITY METHODS ============\n\n  assessCollectionRisk(averageAgingDays, totalOutstanding, creditLimit) {\n    if (averageAgingDays > 120 || creditLimit > 0 && totalOutstanding > creditLimit * 1.2) {\n      return 'critical';\n    } else if (averageAgingDays > 90 || creditLimit > 0 && totalOutstanding > creditLimit) {\n      return 'high';\n    } else if (averageAgingDays > 60) {\n      return 'medium';\n    } else {\n      return 'low';\n    }\n  }\n  getLastPaymentInfo(customerId) {\n    // This would integrate with transaction history to find last payment\n    // For now, return null - would be implemented with actual transaction data\n    return null;\n  }\n  calculateCollectionMetrics(currency) {\n    const allARs = this.getAllARRecords().filter(ar => ar.currency === currency);\n    const collectedARs = allARs.filter(ar => ar.status === 'collected');\n    const overdueARs = allARs.filter(ar => ar.agingDays > 30 && ar.status === 'outstanding');\n    const averageCollectionDays = collectedARs.length > 0 ? collectedARs.reduce((sum, ar) => sum + ar.agingDays, 0) / collectedARs.length : 0;\n    const collectionEfficiency = allARs.length > 0 ? collectedARs.length / allARs.length * 100 : 0;\n    const overduePercentage = allARs.length > 0 ? overdueARs.length / allARs.length * 100 : 0;\n    return {\n      averageCollectionDays,\n      collectionEfficiency,\n      overduePercentage\n    };\n  }\n  determineCollectionAction(ar, lastAction) {\n    const daysSinceLastAction = lastAction ? Math.floor((Date.now() - this.parseDate(lastAction.actionDate).getTime()) / (1000 * 60 * 60 * 24)) : 999;\n    if (ar.agingDays > 120 && daysSinceLastAction > 30) {\n      return {\n        type: 'legal_notice',\n        dueDate: this.addDays(new Date(), 7),\n        description: `Legal notice required for invoice ${ar.invoiceNumber} - ${ar.agingDays} days overdue`,\n        priority: 'urgent'\n      };\n    } else if (ar.agingDays > 90 && daysSinceLastAction > 14) {\n      return {\n        type: 'follow_up',\n        dueDate: this.addDays(new Date(), 3),\n        description: `Follow-up required for invoice ${ar.invoiceNumber} - ${ar.agingDays} days overdue`,\n        priority: 'high'\n      };\n    } else if (ar.agingDays > 60 && daysSinceLastAction > 7) {\n      return {\n        type: 'reminder',\n        dueDate: this.addDays(new Date(), 2),\n        description: `Payment reminder for invoice ${ar.invoiceNumber} - ${ar.agingDays} days overdue`,\n        priority: 'medium'\n      };\n    }\n    return null;\n  }\n  storeARAgingReport(report) {\n    const reports = this.getARAgingReports();\n    reports.push(report);\n\n    // Keep only last 12 reports\n    if (reports.length > 12) {\n      reports.splice(0, reports.length - 12);\n    }\n    fileStorageService.writeData(this.AR_AGING_REPORTS_KEY, reports);\n  }\n  parseDate(dateStr) {\n    const parts = dateStr.split('/');\n    if (parts.length === 3) {\n      const day = parseInt(parts[0]);\n      const month = parseInt(parts[1]) - 1;\n      const year = parseInt(parts[2]);\n      return new Date(year, month, day);\n    }\n    return new Date(dateStr);\n  }\n  addDays(date, days) {\n    const result = new Date(date);\n    result.setDate(result.getDate() + days);\n    const day = result.getDate().toString().padStart(2, '0');\n    const month = (result.getMonth() + 1).toString().padStart(2, '0');\n    const year = result.getFullYear();\n    return `${day}/${month}/${year}`;\n  }\n  generateId() {\n    return `${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;\n  }\n\n  // ============ VERIFICATION INTEGRATION ============\n\n  // Verify AR aging for a specific date\n  verifyARAging(date, verifiedBy) {\n    try {\n      // Update aging for verification date\n      this.updateAllAgingDays();\n\n      // Generate report for verification\n      const report = this.generateARAgingReport();\n\n      // Create verification record (would integrate with daily verification system)\n      console.log(`AR Aging verified for ${date} by ${verifiedBy}`);\n      return true;\n    } catch (error) {\n      console.error('Error verifying AR aging:', error);\n      return false;\n    }\n  }\n\n  // Get AR statistics for dashboard\n  getARStatistics() {\n    const outstandingARs = this.getOutstandingARRecords();\n    const overdueARs = this.getOverdueARRecords();\n    const totalOutstanding = outstandingARs.reduce((sum, ar) => sum + ar.amount, 0);\n    const totalOverdue = overdueARs.reduce((sum, ar) => sum + ar.amount, 0);\n    const averageAgingDays = outstandingARs.length > 0 ? outstandingARs.reduce((sum, ar) => sum + ar.agingDays, 0) / outstandingARs.length : 0;\n    const collectionMetrics = this.calculateCollectionMetrics('SAR');\n\n    // Calculate risk distribution\n    const customers = [...new Set(outstandingARs.map(ar => ar.customerId))];\n    const riskDistribution = {\n      low: 0,\n      medium: 0,\n      high: 0,\n      critical: 0\n    };\n    customers.forEach(customerId => {\n      const summary = this.getCustomerARSummary(customerId);\n      if (summary) {\n        riskDistribution[summary.collectionRisk]++;\n      }\n    });\n    return {\n      totalOutstanding,\n      totalOverdue,\n      averageAgingDays,\n      collectionEfficiency: collectionMetrics.collectionEfficiency,\n      riskDistribution\n    };\n  }\n}\nexport const arAgingManagementService = new ARAgingManagementService();", "map": {"version": 3, "names": ["fileStorageService", "ARAgingManagementService", "constructor", "AR_RECORDS_KEY", "CUSTOMERS_KEY", "COLLECTION_ACTIONS_KEY", "AR_AGING_REPORTS_KEY", "createARRecord", "arData", "ar<PERSON><PERSON>ord", "id", "generateId", "agingDays", "calculateAgingDays", "invoiceDate", "reconciliationStatus", "matchedTransactionIds", "ar<PERSON><PERSON><PERSON>s", "getAllARRecords", "push", "writeData", "updateCustomerRecord", "customerId", "customerName", "updateARRecord", "arRecordId", "updates", "index", "findIndex", "ar", "error", "console", "deleteARRecord", "filteredRecords", "filter", "length", "invoice", "parseDate", "today", "Date", "diffTime", "getTime", "Math", "floor", "updateAllAgingDays", "updated", "for<PERSON>ach", "newAgingDays", "calculateAgingBucket", "amount", "current", "days31to60", "days61to90", "days91to120", "over120", "customers", "getCustomers", "existingIndex", "c", "newCustomer", "name", "contactInfo", "email", "phone", "address", "creditLimit", "paymentTerms", "riskRating", "isActive", "getCustomerARSummary", "customer", "find", "customerARs", "status", "totalOutstanding", "agingBuckets", "oldestInvoiceDate", "averageAgingDays", "collectionRisk", "currency", "totalAgingDays", "oldestDate", "bucket", "Object", "keys", "key", "assessCollectionRisk", "lastPayment", "getLastPaymentInfo", "lastPaymentDate", "date", "lastPaymentAmount", "generateARAgingReport", "outstandingARs", "totalAgingBuckets", "customerIds", "Set", "map", "customerSummaries", "summary", "riskAnalysis", "lowRisk", "mediumRisk", "highRisk", "criticalRisk", "collectionMetrics", "calculateCollectionMetrics", "report", "reportDate", "toISOString", "split", "sort", "a", "b", "storeARAgingReport", "createCollectionAction", "actionData", "action", "actions", "getCollectionActions", "updateCollectionAction", "actionId", "generateAutomaticCollectionActions", "newActions", "existingActions", "lastAction", "actionDate", "actionNeeded", "determineCollectionAction", "actionType", "type", "dueDate", "description", "assignedTo", "priority", "notes", "markARAsCollected", "transactionId", "includes", "totalCollected", "readData", "getOutstandingARRecords", "getOverdueARRecords", "getARAgingReports", "allARs", "collectedARs", "overdueARs", "averageCollectionDays", "reduce", "sum", "collectionEfficiency", "overduePercentage", "daysSinceLastAction", "now", "addDays", "invoiceNumber", "reports", "splice", "dateStr", "parts", "day", "parseInt", "month", "year", "days", "result", "setDate", "getDate", "toString", "padStart", "getMonth", "getFullYear", "random", "substr", "verifyARAging", "verifiedBy", "log", "getARStatistics", "totalOverdue", "riskDistribution", "low", "medium", "high", "critical", "arAgingManagementService"], "sources": ["C:/tmsft/src/services/arAgingManagementService.ts"], "sourcesContent": ["import { \r\n  ARRecord, \r\n  Customer,\r\n  DailyVerification\r\n} from '../types';\r\nimport { fileStorageService } from './fileStorageService';\r\n\r\ninterface ARAgingBucket {\r\n  current: number;      // 0-30 days\r\n  days31to60: number;   // 31-60 days\r\n  days61to90: number;   // 61-90 days\r\n  days91to120: number;  // 91-120 days\r\n  over120: number;      // Over 120 days\r\n}\r\n\r\ninterface CustomerARSummary {\r\n  customerId: string;\r\n  customerName: string;\r\n  totalOutstanding: number;\r\n  agingBuckets: ARAgingBucket;\r\n  oldestInvoiceDate: string;\r\n  averageAgingDays: number;\r\n  collectionRisk: 'low' | 'medium' | 'high' | 'critical';\r\n  lastPaymentDate?: string;\r\n  lastPaymentAmount?: number;\r\n  creditLimit?: number;\r\n  currency: 'SAR' | 'USD' | 'AED';\r\n}\r\n\r\ninterface ARAgingReport {\r\n  reportDate: string;\r\n  currency: 'SAR' | 'USD' | 'AED';\r\n  totalOutstanding: number;\r\n  totalAgingBuckets: ARAgingBucket;\r\n  customerSummaries: CustomerARSummary[];\r\n  riskAnalysis: {\r\n    lowRisk: number;\r\n    mediumRisk: number;\r\n    highRisk: number;\r\n    criticalRisk: number;\r\n  };\r\n  collectionMetrics: {\r\n    averageCollectionDays: number;\r\n    collectionEfficiency: number;\r\n    overduePercentage: number;\r\n  };\r\n}\r\n\r\ninterface CollectionAction {\r\n  id: string;\r\n  arRecordId: string;\r\n  customerId: string;\r\n  actionType: 'reminder' | 'follow_up' | 'legal_notice' | 'write_off' | 'payment_plan';\r\n  actionDate: string;\r\n  dueDate: string;\r\n  description: string;\r\n  assignedTo: string;\r\n  status: 'pending' | 'completed' | 'overdue';\r\n  priority: 'low' | 'medium' | 'high' | 'urgent';\r\n  notes: string[];\r\n}\r\n\r\nclass ARAgingManagementService {\r\n  private readonly AR_RECORDS_KEY = 'ar_records';\r\n  private readonly CUSTOMERS_KEY = 'customers';\r\n  private readonly COLLECTION_ACTIONS_KEY = 'collection_actions';\r\n  private readonly AR_AGING_REPORTS_KEY = 'ar_aging_reports';\r\n\r\n  // ============ AR RECORD MANAGEMENT ============\r\n\r\n  // Create new AR record\r\n  createARRecord(arData: Omit<ARRecord, 'id' | 'agingDays' | 'reconciliationStatus' | 'matchedTransactionIds'>): ARRecord {\r\n    const arRecord: ARRecord = {\r\n      ...arData,\r\n      id: this.generateId(),\r\n      agingDays: this.calculateAgingDays(arData.invoiceDate),\r\n      reconciliationStatus: 'unmatched',\r\n      matchedTransactionIds: []\r\n    };\r\n\r\n    // Store AR record\r\n    const arRecords = this.getAllARRecords();\r\n    arRecords.push(arRecord);\r\n    fileStorageService.writeData(this.AR_RECORDS_KEY, arRecords);\r\n\r\n    // Update customer if needed\r\n    this.updateCustomerRecord(arData.customerId, arData.customerName);\r\n\r\n    return arRecord;\r\n  }\r\n\r\n  // Update AR record\r\n  updateARRecord(arRecordId: string, updates: Partial<ARRecord>): boolean {\r\n    try {\r\n      const arRecords = this.getAllARRecords();\r\n      const index = arRecords.findIndex(ar => ar.id === arRecordId);\r\n      \r\n      if (index === -1) return false;\r\n\r\n      // Recalculate aging days if invoice date changed\r\n      if (updates.invoiceDate) {\r\n        updates.agingDays = this.calculateAgingDays(updates.invoiceDate);\r\n      }\r\n\r\n      arRecords[index] = { ...arRecords[index], ...updates };\r\n      fileStorageService.writeData(this.AR_RECORDS_KEY, arRecords);\r\n\r\n      return true;\r\n    } catch (error) {\r\n      console.error('Error updating AR record:', error);\r\n      return false;\r\n    }\r\n  }\r\n\r\n  // Delete AR record\r\n  deleteARRecord(arRecordId: string): boolean {\r\n    try {\r\n      const arRecords = this.getAllARRecords();\r\n      const filteredRecords = arRecords.filter(ar => ar.id !== arRecordId);\r\n      \r\n      if (filteredRecords.length === arRecords.length) return false;\r\n\r\n      fileStorageService.writeData(this.AR_RECORDS_KEY, filteredRecords);\r\n      return true;\r\n    } catch (error) {\r\n      console.error('Error deleting AR record:', error);\r\n      return false;\r\n    }\r\n  }\r\n\r\n  // ============ AGING CALCULATIONS ============\r\n\r\n  // Calculate aging days for an invoice\r\n  private calculateAgingDays(invoiceDate: string): number {\r\n    const invoice = this.parseDate(invoiceDate);\r\n    const today = new Date();\r\n    const diffTime = today.getTime() - invoice.getTime();\r\n    return Math.floor(diffTime / (1000 * 60 * 60 * 24));\r\n  }\r\n\r\n  // Update aging for all AR records\r\n  updateAllAgingDays(): void {\r\n    const arRecords = this.getAllARRecords();\r\n    let updated = false;\r\n\r\n    arRecords.forEach(ar => {\r\n      const newAgingDays = this.calculateAgingDays(ar.invoiceDate);\r\n      if (ar.agingDays !== newAgingDays) {\r\n        ar.agingDays = newAgingDays;\r\n        updated = true;\r\n      }\r\n    });\r\n\r\n    if (updated) {\r\n      fileStorageService.writeData(this.AR_RECORDS_KEY, arRecords);\r\n    }\r\n  }\r\n\r\n  // Calculate aging bucket for amount\r\n  private calculateAgingBucket(agingDays: number, amount: number): Partial<ARAgingBucket> {\r\n    if (agingDays <= 30) return { current: amount };\r\n    if (agingDays <= 60) return { days31to60: amount };\r\n    if (agingDays <= 90) return { days61to90: amount };\r\n    if (agingDays <= 120) return { days91to120: amount };\r\n    return { over120: amount };\r\n  }\r\n\r\n  // ============ CUSTOMER MANAGEMENT ============\r\n\r\n  // Update or create customer record\r\n  private updateCustomerRecord(customerId: string, customerName: string): void {\r\n    const customers = this.getCustomers();\r\n    const existingIndex = customers.findIndex(c => c.id === customerId);\r\n\r\n    if (existingIndex === -1) {\r\n      // Create new customer\r\n      const newCustomer: Customer = {\r\n        id: customerId,\r\n        name: customerName,\r\n        contactInfo: {\r\n          email: '',\r\n          phone: '',\r\n          address: ''\r\n        },\r\n        creditLimit: 0,\r\n        paymentTerms: 30,\r\n        riskRating: 'medium',\r\n        isActive: true\r\n      };\r\n      customers.push(newCustomer);\r\n    } else {\r\n      // Update existing customer name if different\r\n      if (customers[existingIndex].name !== customerName) {\r\n        customers[existingIndex].name = customerName;\r\n      }\r\n    }\r\n\r\n    fileStorageService.writeData(this.CUSTOMERS_KEY, customers);\r\n  }\r\n\r\n  // Get customer AR summary\r\n  getCustomerARSummary(customerId: string): CustomerARSummary | null {\r\n    const customer = this.getCustomers().find(c => c.id === customerId);\r\n    if (!customer) return null;\r\n\r\n    const customerARs = this.getAllARRecords().filter(ar => \r\n      ar.customerId === customerId && ar.status === 'outstanding'\r\n    );\r\n\r\n    if (customerARs.length === 0) {\r\n      return {\r\n        customerId,\r\n        customerName: customer.name,\r\n        totalOutstanding: 0,\r\n        agingBuckets: { current: 0, days31to60: 0, days61to90: 0, days91to120: 0, over120: 0 },\r\n        oldestInvoiceDate: '',\r\n        averageAgingDays: 0,\r\n        collectionRisk: 'low',\r\n        creditLimit: customer.creditLimit,\r\n        currency: 'SAR'\r\n      };\r\n    }\r\n\r\n    // Calculate totals and aging buckets\r\n    let totalOutstanding = 0;\r\n    const agingBuckets: ARAgingBucket = { current: 0, days31to60: 0, days61to90: 0, days91to120: 0, over120: 0 };\r\n    let totalAgingDays = 0;\r\n    let oldestDate = customerARs[0].invoiceDate;\r\n\r\n    customerARs.forEach(ar => {\r\n      totalOutstanding += ar.amount;\r\n      totalAgingDays += ar.agingDays;\r\n      \r\n      const bucket = this.calculateAgingBucket(ar.agingDays, ar.amount);\r\n      Object.keys(bucket).forEach(key => {\r\n        agingBuckets[key as keyof ARAgingBucket] += bucket[key as keyof ARAgingBucket] || 0;\r\n      });\r\n\r\n      if (this.parseDate(ar.invoiceDate) < this.parseDate(oldestDate)) {\r\n        oldestDate = ar.invoiceDate;\r\n      }\r\n    });\r\n\r\n    const averageAgingDays = totalAgingDays / customerARs.length;\r\n    const collectionRisk = this.assessCollectionRisk(averageAgingDays, totalOutstanding, customer.creditLimit || 0);\r\n\r\n    // Get last payment info\r\n    const lastPayment = this.getLastPaymentInfo(customerId);\r\n\r\n    return {\r\n      customerId,\r\n      customerName: customer.name,\r\n      totalOutstanding,\r\n      agingBuckets,\r\n      oldestInvoiceDate: oldestDate,\r\n      averageAgingDays,\r\n      collectionRisk,\r\n      lastPaymentDate: lastPayment?.date,\r\n      lastPaymentAmount: lastPayment?.amount,\r\n      creditLimit: customer.creditLimit,\r\n      currency: customerARs[0].currency\r\n    };\r\n  }\r\n\r\n  // ============ AGING REPORTS ============\r\n\r\n  // Generate comprehensive AR aging report\r\n  generateARAgingReport(currency: 'SAR' | 'USD' | 'AED' = 'SAR'): ARAgingReport {\r\n    this.updateAllAgingDays(); // Ensure aging is current\r\n\r\n    const outstandingARs = this.getAllARRecords().filter(ar => \r\n      ar.status === 'outstanding' && ar.currency === currency\r\n    );\r\n\r\n    // Calculate total aging buckets\r\n    const totalAgingBuckets: ARAgingBucket = { current: 0, days31to60: 0, days61to90: 0, days91to120: 0, over120: 0 };\r\n    let totalOutstanding = 0;\r\n\r\n    outstandingARs.forEach(ar => {\r\n      totalOutstanding += ar.amount;\r\n      const bucket = this.calculateAgingBucket(ar.agingDays, ar.amount);\r\n      Object.keys(bucket).forEach(key => {\r\n        totalAgingBuckets[key as keyof ARAgingBucket] += bucket[key as keyof ARAgingBucket] || 0;\r\n      });\r\n    });\r\n\r\n    // Generate customer summaries\r\n    const customerIds = [...new Set(outstandingARs.map(ar => ar.customerId))];\r\n    const customerSummaries: CustomerARSummary[] = [];\r\n\r\n    customerIds.forEach(customerId => {\r\n      const summary = this.getCustomerARSummary(customerId);\r\n      if (summary && summary.totalOutstanding > 0) {\r\n        customerSummaries.push(summary);\r\n      }\r\n    });\r\n\r\n    // Calculate risk analysis\r\n    const riskAnalysis = {\r\n      lowRisk: customerSummaries.filter(c => c.collectionRisk === 'low').length,\r\n      mediumRisk: customerSummaries.filter(c => c.collectionRisk === 'medium').length,\r\n      highRisk: customerSummaries.filter(c => c.collectionRisk === 'high').length,\r\n      criticalRisk: customerSummaries.filter(c => c.collectionRisk === 'critical').length\r\n    };\r\n\r\n    // Calculate collection metrics\r\n    const collectionMetrics = this.calculateCollectionMetrics(currency);\r\n\r\n    const report: ARAgingReport = {\r\n      reportDate: new Date().toISOString().split('T')[0],\r\n      currency,\r\n      totalOutstanding,\r\n      totalAgingBuckets,\r\n      customerSummaries: customerSummaries.sort((a, b) => b.totalOutstanding - a.totalOutstanding),\r\n      riskAnalysis,\r\n      collectionMetrics\r\n    };\r\n\r\n    // Store report\r\n    this.storeARAgingReport(report);\r\n\r\n    return report;\r\n  }\r\n\r\n  // ============ COLLECTION ACTIONS ============\r\n\r\n  // Create collection action\r\n  createCollectionAction(actionData: Omit<CollectionAction, 'id'>): CollectionAction {\r\n    const action: CollectionAction = {\r\n      ...actionData,\r\n      id: this.generateId()\r\n    };\r\n\r\n    const actions = this.getCollectionActions();\r\n    actions.push(action);\r\n    fileStorageService.writeData(this.COLLECTION_ACTIONS_KEY, actions);\r\n\r\n    return action;\r\n  }\r\n\r\n  // Update collection action\r\n  updateCollectionAction(actionId: string, updates: Partial<CollectionAction>): boolean {\r\n    try {\r\n      const actions = this.getCollectionActions();\r\n      const index = actions.findIndex(a => a.id === actionId);\r\n      \r\n      if (index === -1) return false;\r\n\r\n      actions[index] = { ...actions[index], ...updates };\r\n      fileStorageService.writeData(this.COLLECTION_ACTIONS_KEY, actions);\r\n\r\n      return true;\r\n    } catch (error) {\r\n      console.error('Error updating collection action:', error);\r\n      return false;\r\n    }\r\n  }\r\n\r\n  // Generate automatic collection actions based on aging\r\n  generateAutomaticCollectionActions(): CollectionAction[] {\r\n    const outstandingARs = this.getAllARRecords().filter(ar => ar.status === 'outstanding');\r\n    const newActions: CollectionAction[] = [];\r\n\r\n    outstandingARs.forEach(ar => {\r\n      const existingActions = this.getCollectionActions().filter(a => a.arRecordId === ar.id);\r\n      const lastAction = existingActions.sort((a, b) => \r\n        this.parseDate(b.actionDate).getTime() - this.parseDate(a.actionDate).getTime()\r\n      )[0];\r\n\r\n      // Determine if new action is needed\r\n      const actionNeeded = this.determineCollectionAction(ar, lastAction);\r\n      \r\n      if (actionNeeded) {\r\n        const action = this.createCollectionAction({\r\n          arRecordId: ar.id,\r\n          customerId: ar.customerId,\r\n          actionType: actionNeeded.type,\r\n          actionDate: new Date().toISOString().split('T')[0],\r\n          dueDate: actionNeeded.dueDate,\r\n          description: actionNeeded.description,\r\n          assignedTo: 'system',\r\n          status: 'pending',\r\n          priority: actionNeeded.priority,\r\n          notes: []\r\n        });\r\n\r\n        newActions.push(action);\r\n      }\r\n    });\r\n\r\n    return newActions;\r\n  }\r\n\r\n  // ============ RECONCILIATION INTEGRATION ============\r\n\r\n  // Mark AR as collected (called from credit transactions service)\r\n  markARAsCollected(arRecordId: string, transactionId: string, amount: number): boolean {\r\n    try {\r\n      const arRecords = this.getAllARRecords();\r\n      const index = arRecords.findIndex(ar => ar.id === arRecordId);\r\n      \r\n      if (index === -1) return false;\r\n\r\n      const ar = arRecords[index];\r\n      \r\n      // Add transaction to matched list\r\n      if (!ar.matchedTransactionIds.includes(transactionId)) {\r\n        ar.matchedTransactionIds.push(transactionId);\r\n      }\r\n\r\n      // Calculate total collected amount\r\n      const totalCollected = ar.matchedTransactionIds.length * amount; // Simplified - should get actual amounts\r\n      \r\n      // Update status based on collection\r\n      if (totalCollected >= ar.amount * 0.99) {\r\n        ar.status = 'collected';\r\n        ar.reconciliationStatus = 'verified';\r\n      } else if (totalCollected > 0) {\r\n        ar.status = 'partially_collected';\r\n        ar.reconciliationStatus = 'partially_matched';\r\n      }\r\n\r\n      arRecords[index] = ar;\r\n      fileStorageService.writeData(this.AR_RECORDS_KEY, arRecords);\r\n\r\n      return true;\r\n    } catch (error) {\r\n      console.error('Error marking AR as collected:', error);\r\n      return false;\r\n    }\r\n  }\r\n\r\n  // ============ DATA RETRIEVAL METHODS ============\r\n\r\n  getAllARRecords(): ARRecord[] {\r\n    return fileStorageService.readData<ARRecord[]>(this.AR_RECORDS_KEY, []);\r\n  }\r\n\r\n  getOutstandingARRecords(): ARRecord[] {\r\n    return this.getAllARRecords().filter(ar => ar.status === 'outstanding');\r\n  }\r\n\r\n  getOverdueARRecords(): ARRecord[] {\r\n    return this.getAllARRecords().filter(ar => ar.status === 'overdue');\r\n  }\r\n\r\n  getCustomers(): Customer[] {\r\n    return fileStorageService.readData<Customer[]>(this.CUSTOMERS_KEY, []);\r\n  }\r\n\r\n  getCollectionActions(): CollectionAction[] {\r\n    return fileStorageService.readData<CollectionAction[]>(this.COLLECTION_ACTIONS_KEY, []);\r\n  }\r\n\r\n  getARAgingReports(): ARAgingReport[] {\r\n    return fileStorageService.readData<ARAgingReport[]>(this.AR_AGING_REPORTS_KEY, []);\r\n  }\r\n\r\n  // ============ UTILITY METHODS ============\r\n\r\n  private assessCollectionRisk(averageAgingDays: number, totalOutstanding: number, creditLimit: number): 'low' | 'medium' | 'high' | 'critical' {\r\n    if (averageAgingDays > 120 || (creditLimit > 0 && totalOutstanding > creditLimit * 1.2)) {\r\n      return 'critical';\r\n    } else if (averageAgingDays > 90 || (creditLimit > 0 && totalOutstanding > creditLimit)) {\r\n      return 'high';\r\n    } else if (averageAgingDays > 60) {\r\n      return 'medium';\r\n    } else {\r\n      return 'low';\r\n    }\r\n  }\r\n\r\n  private getLastPaymentInfo(customerId: string): { date: string; amount: number } | null {\r\n    // This would integrate with transaction history to find last payment\r\n    // For now, return null - would be implemented with actual transaction data\r\n    return null;\r\n  }\r\n\r\n  private calculateCollectionMetrics(currency: 'SAR' | 'USD' | 'AED'): {\r\n    averageCollectionDays: number;\r\n    collectionEfficiency: number;\r\n    overduePercentage: number;\r\n  } {\r\n    const allARs = this.getAllARRecords().filter(ar => ar.currency === currency);\r\n    const collectedARs = allARs.filter(ar => ar.status === 'collected');\r\n    const overdueARs = allARs.filter(ar => ar.agingDays > 30 && ar.status === 'outstanding');\r\n\r\n    const averageCollectionDays = collectedARs.length > 0 \r\n      ? collectedARs.reduce((sum, ar) => sum + ar.agingDays, 0) / collectedARs.length \r\n      : 0;\r\n\r\n    const collectionEfficiency = allARs.length > 0 \r\n      ? (collectedARs.length / allARs.length) * 100 \r\n      : 0;\r\n\r\n    const overduePercentage = allARs.length > 0 \r\n      ? (overdueARs.length / allARs.length) * 100 \r\n      : 0;\r\n\r\n    return {\r\n      averageCollectionDays,\r\n      collectionEfficiency,\r\n      overduePercentage\r\n    };\r\n  }\r\n\r\n  private determineCollectionAction(ar: ARRecord, lastAction?: CollectionAction): {\r\n    type: CollectionAction['actionType'];\r\n    dueDate: string;\r\n    description: string;\r\n    priority: CollectionAction['priority'];\r\n  } | null {\r\n    \r\n    const daysSinceLastAction = lastAction \r\n      ? Math.floor((Date.now() - this.parseDate(lastAction.actionDate).getTime()) / (1000 * 60 * 60 * 24))\r\n      : 999;\r\n\r\n    if (ar.agingDays > 120 && daysSinceLastAction > 30) {\r\n      return {\r\n        type: 'legal_notice',\r\n        dueDate: this.addDays(new Date(), 7),\r\n        description: `Legal notice required for invoice ${ar.invoiceNumber} - ${ar.agingDays} days overdue`,\r\n        priority: 'urgent'\r\n      };\r\n    } else if (ar.agingDays > 90 && daysSinceLastAction > 14) {\r\n      return {\r\n        type: 'follow_up',\r\n        dueDate: this.addDays(new Date(), 3),\r\n        description: `Follow-up required for invoice ${ar.invoiceNumber} - ${ar.agingDays} days overdue`,\r\n        priority: 'high'\r\n      };\r\n    } else if (ar.agingDays > 60 && daysSinceLastAction > 7) {\r\n      return {\r\n        type: 'reminder',\r\n        dueDate: this.addDays(new Date(), 2),\r\n        description: `Payment reminder for invoice ${ar.invoiceNumber} - ${ar.agingDays} days overdue`,\r\n        priority: 'medium'\r\n      };\r\n    }\r\n\r\n    return null;\r\n  }\r\n\r\n  private storeARAgingReport(report: ARAgingReport): void {\r\n    const reports = this.getARAgingReports();\r\n    reports.push(report);\r\n    \r\n    // Keep only last 12 reports\r\n    if (reports.length > 12) {\r\n      reports.splice(0, reports.length - 12);\r\n    }\r\n    \r\n    fileStorageService.writeData(this.AR_AGING_REPORTS_KEY, reports);\r\n  }\r\n\r\n  private parseDate(dateStr: string): Date {\r\n    const parts = dateStr.split('/');\r\n    if (parts.length === 3) {\r\n      const day = parseInt(parts[0]);\r\n      const month = parseInt(parts[1]) - 1;\r\n      const year = parseInt(parts[2]);\r\n      return new Date(year, month, day);\r\n    }\r\n    return new Date(dateStr);\r\n  }\r\n\r\n  private addDays(date: Date, days: number): string {\r\n    const result = new Date(date);\r\n    result.setDate(result.getDate() + days);\r\n    const day = result.getDate().toString().padStart(2, '0');\r\n    const month = (result.getMonth() + 1).toString().padStart(2, '0');\r\n    const year = result.getFullYear();\r\n    return `${day}/${month}/${year}`;\r\n  }\r\n\r\n  private generateId(): string {\r\n    return `${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;\r\n  }\r\n\r\n  // ============ VERIFICATION INTEGRATION ============\r\n\r\n  // Verify AR aging for a specific date\r\n  verifyARAging(date: string, verifiedBy: string): boolean {\r\n    try {\r\n      // Update aging for verification date\r\n      this.updateAllAgingDays();\r\n      \r\n      // Generate report for verification\r\n      const report = this.generateARAgingReport();\r\n      \r\n      // Create verification record (would integrate with daily verification system)\r\n      console.log(`AR Aging verified for ${date} by ${verifiedBy}`);\r\n      \r\n      return true;\r\n    } catch (error) {\r\n      console.error('Error verifying AR aging:', error);\r\n      return false;\r\n    }\r\n  }\r\n\r\n  // Get AR statistics for dashboard\r\n  getARStatistics(): {\r\n    totalOutstanding: number;\r\n    totalOverdue: number;\r\n    averageAgingDays: number;\r\n    collectionEfficiency: number;\r\n    riskDistribution: { low: number; medium: number; high: number; critical: number };\r\n  } {\r\n    const outstandingARs = this.getOutstandingARRecords();\r\n    const overdueARs = this.getOverdueARRecords();\r\n    \r\n    const totalOutstanding = outstandingARs.reduce((sum, ar) => sum + ar.amount, 0);\r\n    const totalOverdue = overdueARs.reduce((sum, ar) => sum + ar.amount, 0);\r\n    \r\n    const averageAgingDays = outstandingARs.length > 0 \r\n      ? outstandingARs.reduce((sum, ar) => sum + ar.agingDays, 0) / outstandingARs.length \r\n      : 0;\r\n\r\n    const collectionMetrics = this.calculateCollectionMetrics('SAR');\r\n    \r\n    // Calculate risk distribution\r\n    const customers = [...new Set(outstandingARs.map(ar => ar.customerId))];\r\n    const riskDistribution = { low: 0, medium: 0, high: 0, critical: 0 };\r\n    \r\n    customers.forEach(customerId => {\r\n      const summary = this.getCustomerARSummary(customerId);\r\n      if (summary) {\r\n        riskDistribution[summary.collectionRisk]++;\r\n      }\r\n    });\r\n\r\n    return {\r\n      totalOutstanding,\r\n      totalOverdue,\r\n      averageAgingDays,\r\n      collectionEfficiency: collectionMetrics.collectionEfficiency,\r\n      riskDistribution\r\n    };\r\n  }\r\n}\r\n\r\nexport const arAgingManagementService = new ARAgingManagementService(); "], "mappings": "AAKA,SAASA,kBAAkB,QAAQ,sBAAsB;AAyDzD,MAAMC,wBAAwB,CAAC;EAAAC,YAAA;IAAA,KACZC,cAAc,GAAG,YAAY;IAAA,KAC7BC,aAAa,GAAG,WAAW;IAAA,KAC3BC,sBAAsB,GAAG,oBAAoB;IAAA,KAC7CC,oBAAoB,GAAG,kBAAkB;EAAA;EAE1D;;EAEA;EACAC,cAAcA,CAACC,MAA6F,EAAY;IACtH,MAAMC,QAAkB,GAAG;MACzB,GAAGD,MAAM;MACTE,EAAE,EAAE,IAAI,CAACC,UAAU,CAAC,CAAC;MACrBC,SAAS,EAAE,IAAI,CAACC,kBAAkB,CAACL,MAAM,CAACM,WAAW,CAAC;MACtDC,oBAAoB,EAAE,WAAW;MACjCC,qBAAqB,EAAE;IACzB,CAAC;;IAED;IACA,MAAMC,SAAS,GAAG,IAAI,CAACC,eAAe,CAAC,CAAC;IACxCD,SAAS,CAACE,IAAI,CAACV,QAAQ,CAAC;IACxBT,kBAAkB,CAACoB,SAAS,CAAC,IAAI,CAACjB,cAAc,EAAEc,SAAS,CAAC;;IAE5D;IACA,IAAI,CAACI,oBAAoB,CAACb,MAAM,CAACc,UAAU,EAAEd,MAAM,CAACe,YAAY,CAAC;IAEjE,OAAOd,QAAQ;EACjB;;EAEA;EACAe,cAAcA,CAACC,UAAkB,EAAEC,OAA0B,EAAW;IACtE,IAAI;MACF,MAAMT,SAAS,GAAG,IAAI,CAACC,eAAe,CAAC,CAAC;MACxC,MAAMS,KAAK,GAAGV,SAAS,CAACW,SAAS,CAACC,EAAE,IAAIA,EAAE,CAACnB,EAAE,KAAKe,UAAU,CAAC;MAE7D,IAAIE,KAAK,KAAK,CAAC,CAAC,EAAE,OAAO,KAAK;;MAE9B;MACA,IAAID,OAAO,CAACZ,WAAW,EAAE;QACvBY,OAAO,CAACd,SAAS,GAAG,IAAI,CAACC,kBAAkB,CAACa,OAAO,CAACZ,WAAW,CAAC;MAClE;MAEAG,SAAS,CAACU,KAAK,CAAC,GAAG;QAAE,GAAGV,SAAS,CAACU,KAAK,CAAC;QAAE,GAAGD;MAAQ,CAAC;MACtD1B,kBAAkB,CAACoB,SAAS,CAAC,IAAI,CAACjB,cAAc,EAAEc,SAAS,CAAC;MAE5D,OAAO,IAAI;IACb,CAAC,CAAC,OAAOa,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACjD,OAAO,KAAK;IACd;EACF;;EAEA;EACAE,cAAcA,CAACP,UAAkB,EAAW;IAC1C,IAAI;MACF,MAAMR,SAAS,GAAG,IAAI,CAACC,eAAe,CAAC,CAAC;MACxC,MAAMe,eAAe,GAAGhB,SAAS,CAACiB,MAAM,CAACL,EAAE,IAAIA,EAAE,CAACnB,EAAE,KAAKe,UAAU,CAAC;MAEpE,IAAIQ,eAAe,CAACE,MAAM,KAAKlB,SAAS,CAACkB,MAAM,EAAE,OAAO,KAAK;MAE7DnC,kBAAkB,CAACoB,SAAS,CAAC,IAAI,CAACjB,cAAc,EAAE8B,eAAe,CAAC;MAClE,OAAO,IAAI;IACb,CAAC,CAAC,OAAOH,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACjD,OAAO,KAAK;IACd;EACF;;EAEA;;EAEA;EACQjB,kBAAkBA,CAACC,WAAmB,EAAU;IACtD,MAAMsB,OAAO,GAAG,IAAI,CAACC,SAAS,CAACvB,WAAW,CAAC;IAC3C,MAAMwB,KAAK,GAAG,IAAIC,IAAI,CAAC,CAAC;IACxB,MAAMC,QAAQ,GAAGF,KAAK,CAACG,OAAO,CAAC,CAAC,GAAGL,OAAO,CAACK,OAAO,CAAC,CAAC;IACpD,OAAOC,IAAI,CAACC,KAAK,CAACH,QAAQ,IAAI,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;EACrD;;EAEA;EACAI,kBAAkBA,CAAA,EAAS;IACzB,MAAM3B,SAAS,GAAG,IAAI,CAACC,eAAe,CAAC,CAAC;IACxC,IAAI2B,OAAO,GAAG,KAAK;IAEnB5B,SAAS,CAAC6B,OAAO,CAACjB,EAAE,IAAI;MACtB,MAAMkB,YAAY,GAAG,IAAI,CAAClC,kBAAkB,CAACgB,EAAE,CAACf,WAAW,CAAC;MAC5D,IAAIe,EAAE,CAACjB,SAAS,KAAKmC,YAAY,EAAE;QACjClB,EAAE,CAACjB,SAAS,GAAGmC,YAAY;QAC3BF,OAAO,GAAG,IAAI;MAChB;IACF,CAAC,CAAC;IAEF,IAAIA,OAAO,EAAE;MACX7C,kBAAkB,CAACoB,SAAS,CAAC,IAAI,CAACjB,cAAc,EAAEc,SAAS,CAAC;IAC9D;EACF;;EAEA;EACQ+B,oBAAoBA,CAACpC,SAAiB,EAAEqC,MAAc,EAA0B;IACtF,IAAIrC,SAAS,IAAI,EAAE,EAAE,OAAO;MAAEsC,OAAO,EAAED;IAAO,CAAC;IAC/C,IAAIrC,SAAS,IAAI,EAAE,EAAE,OAAO;MAAEuC,UAAU,EAAEF;IAAO,CAAC;IAClD,IAAIrC,SAAS,IAAI,EAAE,EAAE,OAAO;MAAEwC,UAAU,EAAEH;IAAO,CAAC;IAClD,IAAIrC,SAAS,IAAI,GAAG,EAAE,OAAO;MAAEyC,WAAW,EAAEJ;IAAO,CAAC;IACpD,OAAO;MAAEK,OAAO,EAAEL;IAAO,CAAC;EAC5B;;EAEA;;EAEA;EACQ5B,oBAAoBA,CAACC,UAAkB,EAAEC,YAAoB,EAAQ;IAC3E,MAAMgC,SAAS,GAAG,IAAI,CAACC,YAAY,CAAC,CAAC;IACrC,MAAMC,aAAa,GAAGF,SAAS,CAAC3B,SAAS,CAAC8B,CAAC,IAAIA,CAAC,CAAChD,EAAE,KAAKY,UAAU,CAAC;IAEnE,IAAImC,aAAa,KAAK,CAAC,CAAC,EAAE;MACxB;MACA,MAAME,WAAqB,GAAG;QAC5BjD,EAAE,EAAEY,UAAU;QACdsC,IAAI,EAAErC,YAAY;QAClBsC,WAAW,EAAE;UACXC,KAAK,EAAE,EAAE;UACTC,KAAK,EAAE,EAAE;UACTC,OAAO,EAAE;QACX,CAAC;QACDC,WAAW,EAAE,CAAC;QACdC,YAAY,EAAE,EAAE;QAChBC,UAAU,EAAE,QAAQ;QACpBC,QAAQ,EAAE;MACZ,CAAC;MACDb,SAAS,CAACpC,IAAI,CAACwC,WAAW,CAAC;IAC7B,CAAC,MAAM;MACL;MACA,IAAIJ,SAAS,CAACE,aAAa,CAAC,CAACG,IAAI,KAAKrC,YAAY,EAAE;QAClDgC,SAAS,CAACE,aAAa,CAAC,CAACG,IAAI,GAAGrC,YAAY;MAC9C;IACF;IAEAvB,kBAAkB,CAACoB,SAAS,CAAC,IAAI,CAAChB,aAAa,EAAEmD,SAAS,CAAC;EAC7D;;EAEA;EACAc,oBAAoBA,CAAC/C,UAAkB,EAA4B;IACjE,MAAMgD,QAAQ,GAAG,IAAI,CAACd,YAAY,CAAC,CAAC,CAACe,IAAI,CAACb,CAAC,IAAIA,CAAC,CAAChD,EAAE,KAAKY,UAAU,CAAC;IACnE,IAAI,CAACgD,QAAQ,EAAE,OAAO,IAAI;IAE1B,MAAME,WAAW,GAAG,IAAI,CAACtD,eAAe,CAAC,CAAC,CAACgB,MAAM,CAACL,EAAE,IAClDA,EAAE,CAACP,UAAU,KAAKA,UAAU,IAAIO,EAAE,CAAC4C,MAAM,KAAK,aAChD,CAAC;IAED,IAAID,WAAW,CAACrC,MAAM,KAAK,CAAC,EAAE;MAC5B,OAAO;QACLb,UAAU;QACVC,YAAY,EAAE+C,QAAQ,CAACV,IAAI;QAC3Bc,gBAAgB,EAAE,CAAC;QACnBC,YAAY,EAAE;UAAEzB,OAAO,EAAE,CAAC;UAAEC,UAAU,EAAE,CAAC;UAAEC,UAAU,EAAE,CAAC;UAAEC,WAAW,EAAE,CAAC;UAAEC,OAAO,EAAE;QAAE,CAAC;QACtFsB,iBAAiB,EAAE,EAAE;QACrBC,gBAAgB,EAAE,CAAC;QACnBC,cAAc,EAAE,KAAK;QACrBb,WAAW,EAAEK,QAAQ,CAACL,WAAW;QACjCc,QAAQ,EAAE;MACZ,CAAC;IACH;;IAEA;IACA,IAAIL,gBAAgB,GAAG,CAAC;IACxB,MAAMC,YAA2B,GAAG;MAAEzB,OAAO,EAAE,CAAC;MAAEC,UAAU,EAAE,CAAC;MAAEC,UAAU,EAAE,CAAC;MAAEC,WAAW,EAAE,CAAC;MAAEC,OAAO,EAAE;IAAE,CAAC;IAC5G,IAAI0B,cAAc,GAAG,CAAC;IACtB,IAAIC,UAAU,GAAGT,WAAW,CAAC,CAAC,CAAC,CAAC1D,WAAW;IAE3C0D,WAAW,CAAC1B,OAAO,CAACjB,EAAE,IAAI;MACxB6C,gBAAgB,IAAI7C,EAAE,CAACoB,MAAM;MAC7B+B,cAAc,IAAInD,EAAE,CAACjB,SAAS;MAE9B,MAAMsE,MAAM,GAAG,IAAI,CAAClC,oBAAoB,CAACnB,EAAE,CAACjB,SAAS,EAAEiB,EAAE,CAACoB,MAAM,CAAC;MACjEkC,MAAM,CAACC,IAAI,CAACF,MAAM,CAAC,CAACpC,OAAO,CAACuC,GAAG,IAAI;QACjCV,YAAY,CAACU,GAAG,CAAwB,IAAIH,MAAM,CAACG,GAAG,CAAwB,IAAI,CAAC;MACrF,CAAC,CAAC;MAEF,IAAI,IAAI,CAAChD,SAAS,CAACR,EAAE,CAACf,WAAW,CAAC,GAAG,IAAI,CAACuB,SAAS,CAAC4C,UAAU,CAAC,EAAE;QAC/DA,UAAU,GAAGpD,EAAE,CAACf,WAAW;MAC7B;IACF,CAAC,CAAC;IAEF,MAAM+D,gBAAgB,GAAGG,cAAc,GAAGR,WAAW,CAACrC,MAAM;IAC5D,MAAM2C,cAAc,GAAG,IAAI,CAACQ,oBAAoB,CAACT,gBAAgB,EAAEH,gBAAgB,EAAEJ,QAAQ,CAACL,WAAW,IAAI,CAAC,CAAC;;IAE/G;IACA,MAAMsB,WAAW,GAAG,IAAI,CAACC,kBAAkB,CAAClE,UAAU,CAAC;IAEvD,OAAO;MACLA,UAAU;MACVC,YAAY,EAAE+C,QAAQ,CAACV,IAAI;MAC3Bc,gBAAgB;MAChBC,YAAY;MACZC,iBAAiB,EAAEK,UAAU;MAC7BJ,gBAAgB;MAChBC,cAAc;MACdW,eAAe,EAAEF,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEG,IAAI;MAClCC,iBAAiB,EAAEJ,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEtC,MAAM;MACtCgB,WAAW,EAAEK,QAAQ,CAACL,WAAW;MACjCc,QAAQ,EAAEP,WAAW,CAAC,CAAC,CAAC,CAACO;IAC3B,CAAC;EACH;;EAEA;;EAEA;EACAa,qBAAqBA,CAACb,QAA+B,GAAG,KAAK,EAAiB;IAC5E,IAAI,CAACnC,kBAAkB,CAAC,CAAC,CAAC,CAAC;;IAE3B,MAAMiD,cAAc,GAAG,IAAI,CAAC3E,eAAe,CAAC,CAAC,CAACgB,MAAM,CAACL,EAAE,IACrDA,EAAE,CAAC4C,MAAM,KAAK,aAAa,IAAI5C,EAAE,CAACkD,QAAQ,KAAKA,QACjD,CAAC;;IAED;IACA,MAAMe,iBAAgC,GAAG;MAAE5C,OAAO,EAAE,CAAC;MAAEC,UAAU,EAAE,CAAC;MAAEC,UAAU,EAAE,CAAC;MAAEC,WAAW,EAAE,CAAC;MAAEC,OAAO,EAAE;IAAE,CAAC;IACjH,IAAIoB,gBAAgB,GAAG,CAAC;IAExBmB,cAAc,CAAC/C,OAAO,CAACjB,EAAE,IAAI;MAC3B6C,gBAAgB,IAAI7C,EAAE,CAACoB,MAAM;MAC7B,MAAMiC,MAAM,GAAG,IAAI,CAAClC,oBAAoB,CAACnB,EAAE,CAACjB,SAAS,EAAEiB,EAAE,CAACoB,MAAM,CAAC;MACjEkC,MAAM,CAACC,IAAI,CAACF,MAAM,CAAC,CAACpC,OAAO,CAACuC,GAAG,IAAI;QACjCS,iBAAiB,CAACT,GAAG,CAAwB,IAAIH,MAAM,CAACG,GAAG,CAAwB,IAAI,CAAC;MAC1F,CAAC,CAAC;IACJ,CAAC,CAAC;;IAEF;IACA,MAAMU,WAAW,GAAG,CAAC,GAAG,IAAIC,GAAG,CAACH,cAAc,CAACI,GAAG,CAACpE,EAAE,IAAIA,EAAE,CAACP,UAAU,CAAC,CAAC,CAAC;IACzE,MAAM4E,iBAAsC,GAAG,EAAE;IAEjDH,WAAW,CAACjD,OAAO,CAACxB,UAAU,IAAI;MAChC,MAAM6E,OAAO,GAAG,IAAI,CAAC9B,oBAAoB,CAAC/C,UAAU,CAAC;MACrD,IAAI6E,OAAO,IAAIA,OAAO,CAACzB,gBAAgB,GAAG,CAAC,EAAE;QAC3CwB,iBAAiB,CAAC/E,IAAI,CAACgF,OAAO,CAAC;MACjC;IACF,CAAC,CAAC;;IAEF;IACA,MAAMC,YAAY,GAAG;MACnBC,OAAO,EAAEH,iBAAiB,CAAChE,MAAM,CAACwB,CAAC,IAAIA,CAAC,CAACoB,cAAc,KAAK,KAAK,CAAC,CAAC3C,MAAM;MACzEmE,UAAU,EAAEJ,iBAAiB,CAAChE,MAAM,CAACwB,CAAC,IAAIA,CAAC,CAACoB,cAAc,KAAK,QAAQ,CAAC,CAAC3C,MAAM;MAC/EoE,QAAQ,EAAEL,iBAAiB,CAAChE,MAAM,CAACwB,CAAC,IAAIA,CAAC,CAACoB,cAAc,KAAK,MAAM,CAAC,CAAC3C,MAAM;MAC3EqE,YAAY,EAAEN,iBAAiB,CAAChE,MAAM,CAACwB,CAAC,IAAIA,CAAC,CAACoB,cAAc,KAAK,UAAU,CAAC,CAAC3C;IAC/E,CAAC;;IAED;IACA,MAAMsE,iBAAiB,GAAG,IAAI,CAACC,0BAA0B,CAAC3B,QAAQ,CAAC;IAEnE,MAAM4B,MAAqB,GAAG;MAC5BC,UAAU,EAAE,IAAIrE,IAAI,CAAC,CAAC,CAACsE,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;MAClD/B,QAAQ;MACRL,gBAAgB;MAChBoB,iBAAiB;MACjBI,iBAAiB,EAAEA,iBAAiB,CAACa,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKA,CAAC,CAACvC,gBAAgB,GAAGsC,CAAC,CAACtC,gBAAgB,CAAC;MAC5F0B,YAAY;MACZK;IACF,CAAC;;IAED;IACA,IAAI,CAACS,kBAAkB,CAACP,MAAM,CAAC;IAE/B,OAAOA,MAAM;EACf;;EAEA;;EAEA;EACAQ,sBAAsBA,CAACC,UAAwC,EAAoB;IACjF,MAAMC,MAAwB,GAAG;MAC/B,GAAGD,UAAU;MACb1G,EAAE,EAAE,IAAI,CAACC,UAAU,CAAC;IACtB,CAAC;IAED,MAAM2G,OAAO,GAAG,IAAI,CAACC,oBAAoB,CAAC,CAAC;IAC3CD,OAAO,CAACnG,IAAI,CAACkG,MAAM,CAAC;IACpBrH,kBAAkB,CAACoB,SAAS,CAAC,IAAI,CAACf,sBAAsB,EAAEiH,OAAO,CAAC;IAElE,OAAOD,MAAM;EACf;;EAEA;EACAG,sBAAsBA,CAACC,QAAgB,EAAE/F,OAAkC,EAAW;IACpF,IAAI;MACF,MAAM4F,OAAO,GAAG,IAAI,CAACC,oBAAoB,CAAC,CAAC;MAC3C,MAAM5F,KAAK,GAAG2F,OAAO,CAAC1F,SAAS,CAACoF,CAAC,IAAIA,CAAC,CAACtG,EAAE,KAAK+G,QAAQ,CAAC;MAEvD,IAAI9F,KAAK,KAAK,CAAC,CAAC,EAAE,OAAO,KAAK;MAE9B2F,OAAO,CAAC3F,KAAK,CAAC,GAAG;QAAE,GAAG2F,OAAO,CAAC3F,KAAK,CAAC;QAAE,GAAGD;MAAQ,CAAC;MAClD1B,kBAAkB,CAACoB,SAAS,CAAC,IAAI,CAACf,sBAAsB,EAAEiH,OAAO,CAAC;MAElE,OAAO,IAAI;IACb,CAAC,CAAC,OAAOxF,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;MACzD,OAAO,KAAK;IACd;EACF;;EAEA;EACA4F,kCAAkCA,CAAA,EAAuB;IACvD,MAAM7B,cAAc,GAAG,IAAI,CAAC3E,eAAe,CAAC,CAAC,CAACgB,MAAM,CAACL,EAAE,IAAIA,EAAE,CAAC4C,MAAM,KAAK,aAAa,CAAC;IACvF,MAAMkD,UAA8B,GAAG,EAAE;IAEzC9B,cAAc,CAAC/C,OAAO,CAACjB,EAAE,IAAI;MAC3B,MAAM+F,eAAe,GAAG,IAAI,CAACL,oBAAoB,CAAC,CAAC,CAACrF,MAAM,CAAC8E,CAAC,IAAIA,CAAC,CAACvF,UAAU,KAAKI,EAAE,CAACnB,EAAE,CAAC;MACvF,MAAMmH,UAAU,GAAGD,eAAe,CAACb,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAC3C,IAAI,CAAC5E,SAAS,CAAC4E,CAAC,CAACa,UAAU,CAAC,CAACrF,OAAO,CAAC,CAAC,GAAG,IAAI,CAACJ,SAAS,CAAC2E,CAAC,CAACc,UAAU,CAAC,CAACrF,OAAO,CAAC,CAChF,CAAC,CAAC,CAAC,CAAC;;MAEJ;MACA,MAAMsF,YAAY,GAAG,IAAI,CAACC,yBAAyB,CAACnG,EAAE,EAAEgG,UAAU,CAAC;MAEnE,IAAIE,YAAY,EAAE;QAChB,MAAMV,MAAM,GAAG,IAAI,CAACF,sBAAsB,CAAC;UACzC1F,UAAU,EAAEI,EAAE,CAACnB,EAAE;UACjBY,UAAU,EAAEO,EAAE,CAACP,UAAU;UACzB2G,UAAU,EAAEF,YAAY,CAACG,IAAI;UAC7BJ,UAAU,EAAE,IAAIvF,IAAI,CAAC,CAAC,CAACsE,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;UAClDqB,OAAO,EAAEJ,YAAY,CAACI,OAAO;UAC7BC,WAAW,EAAEL,YAAY,CAACK,WAAW;UACrCC,UAAU,EAAE,QAAQ;UACpB5D,MAAM,EAAE,SAAS;UACjB6D,QAAQ,EAAEP,YAAY,CAACO,QAAQ;UAC/BC,KAAK,EAAE;QACT,CAAC,CAAC;QAEFZ,UAAU,CAACxG,IAAI,CAACkG,MAAM,CAAC;MACzB;IACF,CAAC,CAAC;IAEF,OAAOM,UAAU;EACnB;;EAEA;;EAEA;EACAa,iBAAiBA,CAAC/G,UAAkB,EAAEgH,aAAqB,EAAExF,MAAc,EAAW;IACpF,IAAI;MACF,MAAMhC,SAAS,GAAG,IAAI,CAACC,eAAe,CAAC,CAAC;MACxC,MAAMS,KAAK,GAAGV,SAAS,CAACW,SAAS,CAACC,EAAE,IAAIA,EAAE,CAACnB,EAAE,KAAKe,UAAU,CAAC;MAE7D,IAAIE,KAAK,KAAK,CAAC,CAAC,EAAE,OAAO,KAAK;MAE9B,MAAME,EAAE,GAAGZ,SAAS,CAACU,KAAK,CAAC;;MAE3B;MACA,IAAI,CAACE,EAAE,CAACb,qBAAqB,CAAC0H,QAAQ,CAACD,aAAa,CAAC,EAAE;QACrD5G,EAAE,CAACb,qBAAqB,CAACG,IAAI,CAACsH,aAAa,CAAC;MAC9C;;MAEA;MACA,MAAME,cAAc,GAAG9G,EAAE,CAACb,qBAAqB,CAACmB,MAAM,GAAGc,MAAM,CAAC,CAAC;;MAEjE;MACA,IAAI0F,cAAc,IAAI9G,EAAE,CAACoB,MAAM,GAAG,IAAI,EAAE;QACtCpB,EAAE,CAAC4C,MAAM,GAAG,WAAW;QACvB5C,EAAE,CAACd,oBAAoB,GAAG,UAAU;MACtC,CAAC,MAAM,IAAI4H,cAAc,GAAG,CAAC,EAAE;QAC7B9G,EAAE,CAAC4C,MAAM,GAAG,qBAAqB;QACjC5C,EAAE,CAACd,oBAAoB,GAAG,mBAAmB;MAC/C;MAEAE,SAAS,CAACU,KAAK,CAAC,GAAGE,EAAE;MACrB7B,kBAAkB,CAACoB,SAAS,CAAC,IAAI,CAACjB,cAAc,EAAEc,SAAS,CAAC;MAE5D,OAAO,IAAI;IACb,CAAC,CAAC,OAAOa,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;MACtD,OAAO,KAAK;IACd;EACF;;EAEA;;EAEAZ,eAAeA,CAAA,EAAe;IAC5B,OAAOlB,kBAAkB,CAAC4I,QAAQ,CAAa,IAAI,CAACzI,cAAc,EAAE,EAAE,CAAC;EACzE;EAEA0I,uBAAuBA,CAAA,EAAe;IACpC,OAAO,IAAI,CAAC3H,eAAe,CAAC,CAAC,CAACgB,MAAM,CAACL,EAAE,IAAIA,EAAE,CAAC4C,MAAM,KAAK,aAAa,CAAC;EACzE;EAEAqE,mBAAmBA,CAAA,EAAe;IAChC,OAAO,IAAI,CAAC5H,eAAe,CAAC,CAAC,CAACgB,MAAM,CAACL,EAAE,IAAIA,EAAE,CAAC4C,MAAM,KAAK,SAAS,CAAC;EACrE;EAEAjB,YAAYA,CAAA,EAAe;IACzB,OAAOxD,kBAAkB,CAAC4I,QAAQ,CAAa,IAAI,CAACxI,aAAa,EAAE,EAAE,CAAC;EACxE;EAEAmH,oBAAoBA,CAAA,EAAuB;IACzC,OAAOvH,kBAAkB,CAAC4I,QAAQ,CAAqB,IAAI,CAACvI,sBAAsB,EAAE,EAAE,CAAC;EACzF;EAEA0I,iBAAiBA,CAAA,EAAoB;IACnC,OAAO/I,kBAAkB,CAAC4I,QAAQ,CAAkB,IAAI,CAACtI,oBAAoB,EAAE,EAAE,CAAC;EACpF;;EAEA;;EAEQgF,oBAAoBA,CAACT,gBAAwB,EAAEH,gBAAwB,EAAET,WAAmB,EAA0C;IAC5I,IAAIY,gBAAgB,GAAG,GAAG,IAAKZ,WAAW,GAAG,CAAC,IAAIS,gBAAgB,GAAGT,WAAW,GAAG,GAAI,EAAE;MACvF,OAAO,UAAU;IACnB,CAAC,MAAM,IAAIY,gBAAgB,GAAG,EAAE,IAAKZ,WAAW,GAAG,CAAC,IAAIS,gBAAgB,GAAGT,WAAY,EAAE;MACvF,OAAO,MAAM;IACf,CAAC,MAAM,IAAIY,gBAAgB,GAAG,EAAE,EAAE;MAChC,OAAO,QAAQ;IACjB,CAAC,MAAM;MACL,OAAO,KAAK;IACd;EACF;EAEQW,kBAAkBA,CAAClE,UAAkB,EAA2C;IACtF;IACA;IACA,OAAO,IAAI;EACb;EAEQoF,0BAA0BA,CAAC3B,QAA+B,EAIhE;IACA,MAAMiE,MAAM,GAAG,IAAI,CAAC9H,eAAe,CAAC,CAAC,CAACgB,MAAM,CAACL,EAAE,IAAIA,EAAE,CAACkD,QAAQ,KAAKA,QAAQ,CAAC;IAC5E,MAAMkE,YAAY,GAAGD,MAAM,CAAC9G,MAAM,CAACL,EAAE,IAAIA,EAAE,CAAC4C,MAAM,KAAK,WAAW,CAAC;IACnE,MAAMyE,UAAU,GAAGF,MAAM,CAAC9G,MAAM,CAACL,EAAE,IAAIA,EAAE,CAACjB,SAAS,GAAG,EAAE,IAAIiB,EAAE,CAAC4C,MAAM,KAAK,aAAa,CAAC;IAExF,MAAM0E,qBAAqB,GAAGF,YAAY,CAAC9G,MAAM,GAAG,CAAC,GACjD8G,YAAY,CAACG,MAAM,CAAC,CAACC,GAAG,EAAExH,EAAE,KAAKwH,GAAG,GAAGxH,EAAE,CAACjB,SAAS,EAAE,CAAC,CAAC,GAAGqI,YAAY,CAAC9G,MAAM,GAC7E,CAAC;IAEL,MAAMmH,oBAAoB,GAAGN,MAAM,CAAC7G,MAAM,GAAG,CAAC,GACzC8G,YAAY,CAAC9G,MAAM,GAAG6G,MAAM,CAAC7G,MAAM,GAAI,GAAG,GAC3C,CAAC;IAEL,MAAMoH,iBAAiB,GAAGP,MAAM,CAAC7G,MAAM,GAAG,CAAC,GACtC+G,UAAU,CAAC/G,MAAM,GAAG6G,MAAM,CAAC7G,MAAM,GAAI,GAAG,GACzC,CAAC;IAEL,OAAO;MACLgH,qBAAqB;MACrBG,oBAAoB;MACpBC;IACF,CAAC;EACH;EAEQvB,yBAAyBA,CAACnG,EAAY,EAAEgG,UAA6B,EAKpE;IAEP,MAAM2B,mBAAmB,GAAG3B,UAAU,GAClCnF,IAAI,CAACC,KAAK,CAAC,CAACJ,IAAI,CAACkH,GAAG,CAAC,CAAC,GAAG,IAAI,CAACpH,SAAS,CAACwF,UAAU,CAACC,UAAU,CAAC,CAACrF,OAAO,CAAC,CAAC,KAAK,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC,GAClG,GAAG;IAEP,IAAIZ,EAAE,CAACjB,SAAS,GAAG,GAAG,IAAI4I,mBAAmB,GAAG,EAAE,EAAE;MAClD,OAAO;QACLtB,IAAI,EAAE,cAAc;QACpBC,OAAO,EAAE,IAAI,CAACuB,OAAO,CAAC,IAAInH,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;QACpC6F,WAAW,EAAE,qCAAqCvG,EAAE,CAAC8H,aAAa,MAAM9H,EAAE,CAACjB,SAAS,eAAe;QACnG0H,QAAQ,EAAE;MACZ,CAAC;IACH,CAAC,MAAM,IAAIzG,EAAE,CAACjB,SAAS,GAAG,EAAE,IAAI4I,mBAAmB,GAAG,EAAE,EAAE;MACxD,OAAO;QACLtB,IAAI,EAAE,WAAW;QACjBC,OAAO,EAAE,IAAI,CAACuB,OAAO,CAAC,IAAInH,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;QACpC6F,WAAW,EAAE,kCAAkCvG,EAAE,CAAC8H,aAAa,MAAM9H,EAAE,CAACjB,SAAS,eAAe;QAChG0H,QAAQ,EAAE;MACZ,CAAC;IACH,CAAC,MAAM,IAAIzG,EAAE,CAACjB,SAAS,GAAG,EAAE,IAAI4I,mBAAmB,GAAG,CAAC,EAAE;MACvD,OAAO;QACLtB,IAAI,EAAE,UAAU;QAChBC,OAAO,EAAE,IAAI,CAACuB,OAAO,CAAC,IAAInH,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;QACpC6F,WAAW,EAAE,gCAAgCvG,EAAE,CAAC8H,aAAa,MAAM9H,EAAE,CAACjB,SAAS,eAAe;QAC9F0H,QAAQ,EAAE;MACZ,CAAC;IACH;IAEA,OAAO,IAAI;EACb;EAEQpB,kBAAkBA,CAACP,MAAqB,EAAQ;IACtD,MAAMiD,OAAO,GAAG,IAAI,CAACb,iBAAiB,CAAC,CAAC;IACxCa,OAAO,CAACzI,IAAI,CAACwF,MAAM,CAAC;;IAEpB;IACA,IAAIiD,OAAO,CAACzH,MAAM,GAAG,EAAE,EAAE;MACvByH,OAAO,CAACC,MAAM,CAAC,CAAC,EAAED,OAAO,CAACzH,MAAM,GAAG,EAAE,CAAC;IACxC;IAEAnC,kBAAkB,CAACoB,SAAS,CAAC,IAAI,CAACd,oBAAoB,EAAEsJ,OAAO,CAAC;EAClE;EAEQvH,SAASA,CAACyH,OAAe,EAAQ;IACvC,MAAMC,KAAK,GAAGD,OAAO,CAAChD,KAAK,CAAC,GAAG,CAAC;IAChC,IAAIiD,KAAK,CAAC5H,MAAM,KAAK,CAAC,EAAE;MACtB,MAAM6H,GAAG,GAAGC,QAAQ,CAACF,KAAK,CAAC,CAAC,CAAC,CAAC;MAC9B,MAAMG,KAAK,GAAGD,QAAQ,CAACF,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC;MACpC,MAAMI,IAAI,GAAGF,QAAQ,CAACF,KAAK,CAAC,CAAC,CAAC,CAAC;MAC/B,OAAO,IAAIxH,IAAI,CAAC4H,IAAI,EAAED,KAAK,EAAEF,GAAG,CAAC;IACnC;IACA,OAAO,IAAIzH,IAAI,CAACuH,OAAO,CAAC;EAC1B;EAEQJ,OAAOA,CAAChE,IAAU,EAAE0E,IAAY,EAAU;IAChD,MAAMC,MAAM,GAAG,IAAI9H,IAAI,CAACmD,IAAI,CAAC;IAC7B2E,MAAM,CAACC,OAAO,CAACD,MAAM,CAACE,OAAO,CAAC,CAAC,GAAGH,IAAI,CAAC;IACvC,MAAMJ,GAAG,GAAGK,MAAM,CAACE,OAAO,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;IACxD,MAAMP,KAAK,GAAG,CAACG,MAAM,CAACK,QAAQ,CAAC,CAAC,GAAG,CAAC,EAAEF,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;IACjE,MAAMN,IAAI,GAAGE,MAAM,CAACM,WAAW,CAAC,CAAC;IACjC,OAAO,GAAGX,GAAG,IAAIE,KAAK,IAAIC,IAAI,EAAE;EAClC;EAEQxJ,UAAUA,CAAA,EAAW;IAC3B,OAAO,GAAG4B,IAAI,CAACkH,GAAG,CAAC,CAAC,IAAI/G,IAAI,CAACkI,MAAM,CAAC,CAAC,CAACJ,QAAQ,CAAC,EAAE,CAAC,CAACK,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;EACnE;;EAEA;;EAEA;EACAC,aAAaA,CAACpF,IAAY,EAAEqF,UAAkB,EAAW;IACvD,IAAI;MACF;MACA,IAAI,CAACnI,kBAAkB,CAAC,CAAC;;MAEzB;MACA,MAAM+D,MAAM,GAAG,IAAI,CAACf,qBAAqB,CAAC,CAAC;;MAE3C;MACA7D,OAAO,CAACiJ,GAAG,CAAC,yBAAyBtF,IAAI,OAAOqF,UAAU,EAAE,CAAC;MAE7D,OAAO,IAAI;IACb,CAAC,CAAC,OAAOjJ,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACjD,OAAO,KAAK;IACd;EACF;;EAEA;EACAmJ,eAAeA,CAAA,EAMb;IACA,MAAMpF,cAAc,GAAG,IAAI,CAACgD,uBAAuB,CAAC,CAAC;IACrD,MAAMK,UAAU,GAAG,IAAI,CAACJ,mBAAmB,CAAC,CAAC;IAE7C,MAAMpE,gBAAgB,GAAGmB,cAAc,CAACuD,MAAM,CAAC,CAACC,GAAG,EAAExH,EAAE,KAAKwH,GAAG,GAAGxH,EAAE,CAACoB,MAAM,EAAE,CAAC,CAAC;IAC/E,MAAMiI,YAAY,GAAGhC,UAAU,CAACE,MAAM,CAAC,CAACC,GAAG,EAAExH,EAAE,KAAKwH,GAAG,GAAGxH,EAAE,CAACoB,MAAM,EAAE,CAAC,CAAC;IAEvE,MAAM4B,gBAAgB,GAAGgB,cAAc,CAAC1D,MAAM,GAAG,CAAC,GAC9C0D,cAAc,CAACuD,MAAM,CAAC,CAACC,GAAG,EAAExH,EAAE,KAAKwH,GAAG,GAAGxH,EAAE,CAACjB,SAAS,EAAE,CAAC,CAAC,GAAGiF,cAAc,CAAC1D,MAAM,GACjF,CAAC;IAEL,MAAMsE,iBAAiB,GAAG,IAAI,CAACC,0BAA0B,CAAC,KAAK,CAAC;;IAEhE;IACA,MAAMnD,SAAS,GAAG,CAAC,GAAG,IAAIyC,GAAG,CAACH,cAAc,CAACI,GAAG,CAACpE,EAAE,IAAIA,EAAE,CAACP,UAAU,CAAC,CAAC,CAAC;IACvE,MAAM6J,gBAAgB,GAAG;MAAEC,GAAG,EAAE,CAAC;MAAEC,MAAM,EAAE,CAAC;MAAEC,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE;IAAE,CAAC;IAEpEhI,SAAS,CAACT,OAAO,CAACxB,UAAU,IAAI;MAC9B,MAAM6E,OAAO,GAAG,IAAI,CAAC9B,oBAAoB,CAAC/C,UAAU,CAAC;MACrD,IAAI6E,OAAO,EAAE;QACXgF,gBAAgB,CAAChF,OAAO,CAACrB,cAAc,CAAC,EAAE;MAC5C;IACF,CAAC,CAAC;IAEF,OAAO;MACLJ,gBAAgB;MAChBwG,YAAY;MACZrG,gBAAgB;MAChByE,oBAAoB,EAAE7C,iBAAiB,CAAC6C,oBAAoB;MAC5D6B;IACF,CAAC;EACH;AACF;AAEA,OAAO,MAAMK,wBAAwB,GAAG,IAAIvL,wBAAwB,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}