{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.transformArguments = exports.FIRST_KEY_INDEX = void 0;\nconst _1 = require(\".\");\nexports.FIRST_KEY_INDEX = 1;\nfunction transformArguments(key, path, index, ...jsons) {\n  const args = ['JSON.ARRINSERT', key, path, index.toString()];\n  for (const json of jsons) {\n    args.push((0, _1.transformRedisJsonArgument)(json));\n  }\n  return args;\n}\nexports.transformArguments = transformArguments;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "transformArguments", "FIRST_KEY_INDEX", "_1", "require", "key", "path", "index", "jsons", "args", "toString", "json", "push", "transformRedisJsonArgument"], "sources": ["C:/tmsft/node_modules/@redis/json/dist/commands/ARRINSERT.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.transformArguments = exports.FIRST_KEY_INDEX = void 0;\nconst _1 = require(\".\");\nexports.FIRST_KEY_INDEX = 1;\nfunction transformArguments(key, path, index, ...jsons) {\n    const args = ['JSON.ARRINSERT', key, path, index.toString()];\n    for (const json of jsons) {\n        args.push((0, _1.transformRedisJsonArgument)(json));\n    }\n    return args;\n}\nexports.transformArguments = transformArguments;\n"], "mappings": "AAAA,YAAY;;AACZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEC,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7DD,OAAO,CAACE,kBAAkB,GAAGF,OAAO,CAACG,eAAe,GAAG,KAAK,CAAC;AAC7D,MAAMC,EAAE,GAAGC,OAAO,CAAC,GAAG,CAAC;AACvBL,OAAO,CAACG,eAAe,GAAG,CAAC;AAC3B,SAASD,kBAAkBA,CAACI,GAAG,EAAEC,IAAI,EAAEC,KAAK,EAAE,GAAGC,KAAK,EAAE;EACpD,MAAMC,IAAI,GAAG,CAAC,gBAAgB,EAAEJ,GAAG,EAAEC,IAAI,EAAEC,KAAK,CAACG,QAAQ,CAAC,CAAC,CAAC;EAC5D,KAAK,MAAMC,IAAI,IAAIH,KAAK,EAAE;IACtBC,IAAI,CAACG,IAAI,CAAC,CAAC,CAAC,EAAET,EAAE,CAACU,0BAA0B,EAAEF,IAAI,CAAC,CAAC;EACvD;EACA,OAAOF,IAAI;AACf;AACAV,OAAO,CAACE,kBAAkB,GAAGA,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}