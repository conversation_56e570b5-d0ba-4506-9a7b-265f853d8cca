{"ast": null, "code": "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { env, LeakyRelu, util } from '@tensorflow/tfjs-core';\nimport { BinaryOpProgram } from '../binaryop_gpu';\nimport { BinaryOpPackedProgram } from '../binaryop_packed_gpu';\nexport const LEAKYRELU = `return (a < 0.) ? b * a : a;`;\nexport const LEAKYRELU_PACKED = `\n  vec4 aLessThanZero = vec4(lessThan(a, vec4(0.)));\n  return (aLessThanZero * (b * a)) + ((vec4(1.0) - aLessThanZero) * a);\n`;\nexport function leakyRelu(args) {\n  const {\n    inputs,\n    backend,\n    attrs\n  } = args;\n  const {\n    x\n  } = inputs;\n  const {\n    alpha\n  } = attrs;\n  const $alpha = backend.makeTensorInfo([], 'float32', util.createScalarValue(alpha, 'float32'));\n  const program = env().getBool('WEBGL_PACK_BINARY_OPERATIONS') ? new BinaryOpPackedProgram(LEAKYRELU_PACKED, x.shape, $alpha.shape) : new BinaryOpProgram(LEAKYRELU, x.shape, $alpha.shape);\n  const result = backend.runWebGLProgram(program, [x, $alpha], 'float32');\n  backend.disposeIntermediateTensorInfo($alpha);\n  return result;\n}\nexport const leakyReluConfig = {\n  kernelName: LeakyRelu,\n  backendName: 'webgl',\n  kernelFunc: leakyRelu\n};", "map": {"version": 3, "names": ["env", "LeakyRelu", "util", "BinaryOpProgram", "BinaryOpPackedProgram", "LEAKYRELU", "LEAKYRELU_PACKED", "leakyRelu", "args", "inputs", "backend", "attrs", "x", "alpha", "$alpha", "makeTensorInfo", "createScalarValue", "program", "getBool", "shape", "result", "runWebGLProgram", "disposeIntermediateTensorInfo", "leakyReluConfig", "kernelName", "backendName", "kernelFunc"], "sources": ["C:\\tfjs-backend-webgl\\src\\kernels\\LeakyRelu.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {env, KernelConfig, KernelFunc, LeakyRelu, LeakyReluAttrs, LeakyReluInputs, TensorInfo, util} from '@tensorflow/tfjs-core';\nimport {MathBackendWebGL} from '../backend_webgl';\nimport {BinaryOpProgram} from '../binaryop_gpu';\nimport {BinaryOpPackedProgram} from '../binaryop_packed_gpu';\n\nexport const LEAKYRELU = `return (a < 0.) ? b * a : a;`;\nexport const LEAKYRELU_PACKED = `\n  vec4 aLessThanZero = vec4(lessThan(a, vec4(0.)));\n  return (aLessThanZero * (b * a)) + ((vec4(1.0) - aLessThanZero) * a);\n`;\n\nexport function leakyRelu(args: {\n  inputs: LeakyReluInputs,\n  backend: MathBackendWebGL,\n  attrs: LeakyReluAttrs\n}): TensorInfo {\n  const {inputs, backend, attrs} = args;\n  const {x} = inputs;\n  const {alpha} = attrs;\n\n  const $alpha = backend.makeTensorInfo(\n      [], 'float32',\n      util.createScalarValue(alpha as unknown as 'float32', 'float32'));\n\n  const program = env().getBool('WEBGL_PACK_BINARY_OPERATIONS') ?\n      new BinaryOpPackedProgram(LEAKYRELU_PACKED, x.shape, $alpha.shape) :\n      new BinaryOpProgram(LEAKYRELU, x.shape, $alpha.shape);\n  const result = backend.runWebGLProgram(program, [x, $alpha], 'float32');\n\n  backend.disposeIntermediateTensorInfo($alpha);\n\n  return result;\n}\n\nexport const leakyReluConfig: KernelConfig = {\n  kernelName: LeakyRelu,\n  backendName: 'webgl',\n  kernelFunc: leakyRelu as unknown as KernelFunc\n};\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAiBA,SAAQA,GAAG,EAA4BC,SAAS,EAA+CC,IAAI,QAAO,uBAAuB;AAEjI,SAAQC,eAAe,QAAO,iBAAiB;AAC/C,SAAQC,qBAAqB,QAAO,wBAAwB;AAE5D,OAAO,MAAMC,SAAS,GAAG,8BAA8B;AACvD,OAAO,MAAMC,gBAAgB,GAAG;;;CAG/B;AAED,OAAM,SAAUC,SAASA,CAACC,IAIzB;EACC,MAAM;IAACC,MAAM;IAAEC,OAAO;IAAEC;EAAK,CAAC,GAAGH,IAAI;EACrC,MAAM;IAACI;EAAC,CAAC,GAAGH,MAAM;EAClB,MAAM;IAACI;EAAK,CAAC,GAAGF,KAAK;EAErB,MAAMG,MAAM,GAAGJ,OAAO,CAACK,cAAc,CACjC,EAAE,EAAE,SAAS,EACbb,IAAI,CAACc,iBAAiB,CAACH,KAA6B,EAAE,SAAS,CAAC,CAAC;EAErE,MAAMI,OAAO,GAAGjB,GAAG,EAAE,CAACkB,OAAO,CAAC,8BAA8B,CAAC,GACzD,IAAId,qBAAqB,CAACE,gBAAgB,EAAEM,CAAC,CAACO,KAAK,EAAEL,MAAM,CAACK,KAAK,CAAC,GAClE,IAAIhB,eAAe,CAACE,SAAS,EAAEO,CAAC,CAACO,KAAK,EAAEL,MAAM,CAACK,KAAK,CAAC;EACzD,MAAMC,MAAM,GAAGV,OAAO,CAACW,eAAe,CAACJ,OAAO,EAAE,CAACL,CAAC,EAAEE,MAAM,CAAC,EAAE,SAAS,CAAC;EAEvEJ,OAAO,CAACY,6BAA6B,CAACR,MAAM,CAAC;EAE7C,OAAOM,MAAM;AACf;AAEA,OAAO,MAAMG,eAAe,GAAiB;EAC3CC,UAAU,EAAEvB,SAAS;EACrBwB,WAAW,EAAE,OAAO;EACpBC,UAAU,EAAEnB;CACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}