{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.transformArguments = exports.IS_READ_ONLY = exports.FIRST_KEY_INDEX = void 0;\nexports.FIRST_KEY_INDEX = 1;\nexports.IS_READ_ONLY = true;\nfunction transformArguments(key, range) {\n  const args = ['BITCOUNT', key];\n  if (range) {\n    args.push(range.start.toString(), range.end.toString());\n    if (range.mode) {\n      args.push(range.mode);\n    }\n  }\n  return args;\n}\nexports.transformArguments = transformArguments;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "transformArguments", "IS_READ_ONLY", "FIRST_KEY_INDEX", "key", "range", "args", "push", "start", "toString", "end", "mode"], "sources": ["C:/tmsft/node_modules/@redis/client/dist/lib/commands/BITCOUNT.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.transformArguments = exports.IS_READ_ONLY = exports.FIRST_KEY_INDEX = void 0;\nexports.FIRST_KEY_INDEX = 1;\nexports.IS_READ_ONLY = true;\nfunction transformArguments(key, range) {\n    const args = ['BITCOUNT', key];\n    if (range) {\n        args.push(range.start.toString(), range.end.toString());\n        if (range.mode) {\n            args.push(range.mode);\n        }\n    }\n    return args;\n}\nexports.transformArguments = transformArguments;\n"], "mappings": "AAAA,YAAY;;AACZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEC,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7DD,OAAO,CAACE,kBAAkB,GAAGF,OAAO,CAACG,YAAY,GAAGH,OAAO,CAACI,eAAe,GAAG,KAAK,CAAC;AACpFJ,OAAO,CAACI,eAAe,GAAG,CAAC;AAC3BJ,OAAO,CAACG,YAAY,GAAG,IAAI;AAC3B,SAASD,kBAAkBA,CAACG,GAAG,EAAEC,KAAK,EAAE;EACpC,MAAMC,IAAI,GAAG,CAAC,UAAU,EAAEF,GAAG,CAAC;EAC9B,IAAIC,KAAK,EAAE;IACPC,IAAI,CAACC,IAAI,CAACF,KAAK,CAACG,KAAK,CAACC,QAAQ,CAAC,CAAC,EAAEJ,KAAK,CAACK,GAAG,CAACD,QAAQ,CAAC,CAAC,CAAC;IACvD,IAAIJ,KAAK,CAACM,IAAI,EAAE;MACZL,IAAI,CAACC,IAAI,CAACF,KAAK,CAACM,IAAI,CAAC;IACzB;EACJ;EACA,OAAOL,IAAI;AACf;AACAP,OAAO,CAACE,kBAAkB,GAAGA,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}