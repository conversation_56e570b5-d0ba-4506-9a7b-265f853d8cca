{"ast": null, "code": "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nexport class RotateProgram {\n  constructor(imageShape, fillValue) {\n    this.variableNames = ['Image'];\n    this.outputShape = [];\n    this.customUniforms = [{\n      name: 'params',\n      type: 'vec4'\n    }];\n    const imageHeight = imageShape[1];\n    const imageWidth = imageShape[2];\n    this.outputShape = imageShape;\n    let fillSnippet = '';\n    if (typeof fillValue === 'number') {\n      fillSnippet = \"float outputValue = \".concat(fillValue.toFixed(2), \";\");\n    } else {\n      fillSnippet = \"\\n        vec3 fill = vec3(\".concat(fillValue.join(','), \");\\n        float outputValue = fill[coords[3]];\");\n    }\n    this.userCode = \"\\n        void main() {\\n          ivec4 coords = getOutputCoords();\\n          int x = coords[2];\\n          int y = coords[1];\\n          float coordXFloat = (float(x) - params[0]) * params[3] -\\n            (float(y) - params[1]) * params[2];\\n          float coordYFloat = (float(x) - params[0]) * params[2] +\\n            (float(y) - params[1]) * params[3];\\n          int coordX = int(round(coordXFloat + params[0]));\\n          int coordY = int(round(coordYFloat + params[1]));\\n          \".concat(fillSnippet, \"\\n          if(coordX >= 0 && coordX < \").concat(imageWidth, \" && coordY >= 0 && coordY < \").concat(imageHeight, \") {\\n            outputValue = getImage(coords[0], coordY, coordX, coords[3]);\\n          }\\n          setOutput(outputValue);\\n        }\\n    \");\n  }\n}", "map": {"version": 3, "names": ["RotateProgram", "constructor", "imageShape", "fillValue", "variableNames", "outputShape", "customUniforms", "name", "type", "imageHeight", "imageWidth", "fillSnippet", "concat", "toFixed", "join", "userCode"], "sources": ["C:\\tfjs-backend-webgl\\src\\rotate_gpu.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {GPGPUProgram} from './gpgpu_math';\nimport {UniformType} from './shader_compiler';\n\nexport class RotateProgram implements GPGPUProgram {\n  variableNames = ['Image'];\n  outputShape: number[] = [];\n  userCode: string;\n  customUniforms = [{name: 'params', type: 'vec4' as UniformType}];\n  constructor(\n      imageShape: [number, number, number, number],\n      fillValue: number|[number, number, number]) {\n    const imageHeight = imageShape[1];\n    const imageWidth = imageShape[2];\n    this.outputShape = imageShape;\n\n    let fillSnippet = '';\n    if (typeof fillValue === 'number') {\n      fillSnippet = `float outputValue = ${fillValue.toFixed(2)};`;\n    } else {\n      fillSnippet = `\n        vec3 fill = vec3(${fillValue.join(',')});\n        float outputValue = fill[coords[3]];`;\n    }\n\n    this.userCode = `\n        void main() {\n          ivec4 coords = getOutputCoords();\n          int x = coords[2];\n          int y = coords[1];\n          float coordXFloat = (float(x) - params[0]) * params[3] -\n            (float(y) - params[1]) * params[2];\n          float coordYFloat = (float(x) - params[0]) * params[2] +\n            (float(y) - params[1]) * params[3];\n          int coordX = int(round(coordXFloat + params[0]));\n          int coordY = int(round(coordYFloat + params[1]));\n          ${fillSnippet}\n          if(coordX >= 0 && coordX < ${imageWidth} && coordY >= 0 && coordY < ${\n        imageHeight}) {\n            outputValue = getImage(coords[0], coordY, coordX, coords[3]);\n          }\n          setOutput(outputValue);\n        }\n    `;\n  }\n}\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAoBA,OAAM,MAAOA,aAAa;EAKxBC,YACIC,UAA4C,EAC5CC,SAA0C;IAN9C,KAAAC,aAAa,GAAG,CAAC,OAAO,CAAC;IACzB,KAAAC,WAAW,GAAa,EAAE;IAE1B,KAAAC,cAAc,GAAG,CAAC;MAACC,IAAI,EAAE,QAAQ;MAAEC,IAAI,EAAE;IAAqB,CAAC,CAAC;IAI9D,MAAMC,WAAW,GAAGP,UAAU,CAAC,CAAC,CAAC;IACjC,MAAMQ,UAAU,GAAGR,UAAU,CAAC,CAAC,CAAC;IAChC,IAAI,CAACG,WAAW,GAAGH,UAAU;IAE7B,IAAIS,WAAW,GAAG,EAAE;IACpB,IAAI,OAAOR,SAAS,KAAK,QAAQ,EAAE;MACjCQ,WAAW,0BAAAC,MAAA,CAA0BT,SAAS,CAACU,OAAO,CAAC,CAAC,CAAC,MAAG;KAC7D,MAAM;MACLF,WAAW,iCAAAC,MAAA,CACUT,SAAS,CAACW,IAAI,CAAC,GAAG,CAAC,qDACD;;IAGzC,IAAI,CAACC,QAAQ,sfAAAH,MAAA,CAWLD,WAAW,6CAAAC,MAAA,CACgBF,UAAU,kCAAAE,MAAA,CACzCH,WAAW,oJAKd;EACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}