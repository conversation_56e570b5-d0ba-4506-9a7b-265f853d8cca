{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.transformArguments = exports.IS_READ_ONLY = void 0;\nexports.IS_READ_ONLY = true;\nfunction transformArguments() {\n  return ['CLIENT', 'ID'];\n}\nexports.transformArguments = transformArguments;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "transformArguments", "IS_READ_ONLY"], "sources": ["C:/tmsft/node_modules/@redis/client/dist/lib/commands/CLIENT_ID.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.transformArguments = exports.IS_READ_ONLY = void 0;\nexports.IS_READ_ONLY = true;\nfunction transformArguments() {\n    return ['CLIENT', 'ID'];\n}\nexports.transformArguments = transformArguments;\n"], "mappings": "AAAA,YAAY;;AACZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEC,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7DD,OAAO,CAACE,kBAAkB,GAAGF,OAAO,CAACG,YAAY,GAAG,KAAK,CAAC;AAC1DH,OAAO,CAACG,YAAY,GAAG,IAAI;AAC3B,SAASD,kBAAkBA,CAAA,EAAG;EAC1B,OAAO,CAAC,QAAQ,EAAE,IAAI,CAAC;AAC3B;AACAF,OAAO,CAACE,kBAAkB,GAAGA,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}