{"ast": null, "code": "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { Transpose } from '@tensorflow/tfjs-core';\nimport { transposeImpl } from './Transpose_impl';\nimport { transposeImplCPU as cpuTranspose } from './Transpose_impl';\nexport function transpose(args) {\n  const {\n    inputs,\n    backend,\n    attrs\n  } = args;\n  const {\n    x\n  } = inputs;\n  const {\n    perm\n  } = attrs;\n  const webglBackend = backend;\n  const xRank = x.shape.length;\n  const newShape = new Array(xRank);\n  for (let i = 0; i < newShape.length; i++) {\n    newShape[i] = x.shape[perm[i]];\n  }\n  let out;\n  if (webglBackend.shouldExecuteOnCPU([x])) {\n    const xTexData = webglBackend.texData.get(x.dataId);\n    const values = xTexData.values;\n    const outValues = cpuTranspose(values, x.shape, x.dtype, perm, newShape);\n    out = webglBackend.makeTensorInfo(newShape, x.dtype);\n    const outData = webglBackend.texData.get(out.dataId);\n    outData.values = outValues;\n  } else {\n    out = transposeImpl(x, perm, webglBackend);\n  }\n  return out;\n}\nexport const transposeConfig = {\n  kernelName: Transpose,\n  backendName: 'webgl',\n  kernelFunc: transpose\n};", "map": {"version": 3, "names": ["Transpose", "transposeImpl", "transposeImplCPU", "cpuTranspose", "transpose", "args", "inputs", "backend", "attrs", "x", "perm", "webglBackend", "xRank", "shape", "length", "newShape", "Array", "i", "out", "shouldExecuteOnCPU", "xTexData", "texData", "get", "dataId", "values", "outValues", "dtype", "makeTensorInfo", "outData", "transposeConfig", "kernelName", "backendName", "kernelFunc"], "sources": ["C:\\tfjs-backend-webgl\\src\\kernels\\Transpose.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {KernelConfig, KernelFunc, TensorInfo, Transpose, TransposeAttrs, TransposeInputs, TypedArray} from '@tensorflow/tfjs-core';\n\nimport {MathBackendWebGL} from '../backend_webgl';\n\nimport {transposeImpl} from './Transpose_impl';\nimport {transposeImplCPU as cpuTranspose} from './Transpose_impl';\n\nexport function transpose(args: {\n  inputs: TransposeInputs,\n  attrs: TransposeAttrs,\n  backend: MathBackendWebGL\n}) {\n  const {inputs, backend, attrs} = args;\n  const {x} = inputs;\n  const {perm} = attrs;\n  const webglBackend = backend;\n\n  const xRank = x.shape.length;\n\n  const newShape: number[] = new Array(xRank);\n  for (let i = 0; i < newShape.length; i++) {\n    newShape[i] = x.shape[perm[i]];\n  }\n\n  let out: TensorInfo;\n  if (webglBackend.shouldExecuteOnCPU([x])) {\n    const xTexData = webglBackend.texData.get(x.dataId);\n    const values = xTexData.values as TypedArray;\n    const outValues = cpuTranspose(values, x.shape, x.dtype, perm, newShape);\n\n    out = webglBackend.makeTensorInfo(newShape, x.dtype);\n    const outData = webglBackend.texData.get(out.dataId);\n    outData.values = outValues;\n  } else {\n    out = transposeImpl(x, perm, webglBackend);\n  }\n  return out;\n}\n\nexport const transposeConfig: KernelConfig = {\n  kernelName: Transpose,\n  backendName: 'webgl',\n  kernelFunc: transpose as unknown as KernelFunc\n};\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAiBA,SAA8CA,SAAS,QAAoD,uBAAuB;AAIlI,SAAQC,aAAa,QAAO,kBAAkB;AAC9C,SAAQC,gBAAgB,IAAIC,YAAY,QAAO,kBAAkB;AAEjE,OAAM,SAAUC,SAASA,CAACC,IAIzB;EACC,MAAM;IAACC,MAAM;IAAEC,OAAO;IAAEC;EAAK,CAAC,GAAGH,IAAI;EACrC,MAAM;IAACI;EAAC,CAAC,GAAGH,MAAM;EAClB,MAAM;IAACI;EAAI,CAAC,GAAGF,KAAK;EACpB,MAAMG,YAAY,GAAGJ,OAAO;EAE5B,MAAMK,KAAK,GAAGH,CAAC,CAACI,KAAK,CAACC,MAAM;EAE5B,MAAMC,QAAQ,GAAa,IAAIC,KAAK,CAACJ,KAAK,CAAC;EAC3C,KAAK,IAAIK,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,QAAQ,CAACD,MAAM,EAAEG,CAAC,EAAE,EAAE;IACxCF,QAAQ,CAACE,CAAC,CAAC,GAAGR,CAAC,CAACI,KAAK,CAACH,IAAI,CAACO,CAAC,CAAC,CAAC;;EAGhC,IAAIC,GAAe;EACnB,IAAIP,YAAY,CAACQ,kBAAkB,CAAC,CAACV,CAAC,CAAC,CAAC,EAAE;IACxC,MAAMW,QAAQ,GAAGT,YAAY,CAACU,OAAO,CAACC,GAAG,CAACb,CAAC,CAACc,MAAM,CAAC;IACnD,MAAMC,MAAM,GAAGJ,QAAQ,CAACI,MAAoB;IAC5C,MAAMC,SAAS,GAAGtB,YAAY,CAACqB,MAAM,EAAEf,CAAC,CAACI,KAAK,EAAEJ,CAAC,CAACiB,KAAK,EAAEhB,IAAI,EAAEK,QAAQ,CAAC;IAExEG,GAAG,GAAGP,YAAY,CAACgB,cAAc,CAACZ,QAAQ,EAAEN,CAAC,CAACiB,KAAK,CAAC;IACpD,MAAME,OAAO,GAAGjB,YAAY,CAACU,OAAO,CAACC,GAAG,CAACJ,GAAG,CAACK,MAAM,CAAC;IACpDK,OAAO,CAACJ,MAAM,GAAGC,SAAS;GAC3B,MAAM;IACLP,GAAG,GAAGjB,aAAa,CAACQ,CAAC,EAAEC,IAAI,EAAEC,YAAY,CAAC;;EAE5C,OAAOO,GAAG;AACZ;AAEA,OAAO,MAAMW,eAAe,GAAiB;EAC3CC,UAAU,EAAE9B,SAAS;EACrB+B,WAAW,EAAE,OAAO;EACpBC,UAAU,EAAE5B;CACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}