{"ast": null, "code": "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { backend_util, MaxPool, util } from '@tensorflow/tfjs-core';\nimport { assertNotComplex } from '../cpu_util';\nimport { pool } from '../utils/pool_utils';\nimport { identity } from './Identity';\nexport function maxPool(args) {\n  const {\n    inputs,\n    backend,\n    attrs\n  } = args;\n  const {\n    x\n  } = inputs;\n  assertNotComplex(x, 'maxPool');\n  const {\n    filterSize,\n    strides,\n    pad,\n    dimRoundingMode\n  } = attrs;\n  const dilations = 1;\n  util.assert(backend_util.eitherStridesOrDilationsAreOne(strides, dilations), () => 'Error in maxPool: Either strides or dilations must be 1. ' + \"Got strides \".concat(strides, \" and dilations '\").concat(dilations, \"'\"));\n  const convInfo = backend_util.computePool2DInfo(x.shape, filterSize, strides, dilations, pad, dimRoundingMode);\n  let res;\n  if (convInfo.filterWidth === 1 && convInfo.filterHeight === 1 && util.arraysEqual(convInfo.inShape, convInfo.outShape)) {\n    res = identity({\n      inputs: {\n        x\n      },\n      backend\n    });\n  } else {\n    const xValues = backend.data.get(x.dataId).values;\n    const strides = util.computeStrides(x.shape);\n    const buffer = pool(xValues, x.shape, x.dtype, strides, convInfo, 'max');\n    res = backend.makeTensorInfo(convInfo.outShape, x.dtype, buffer.values);\n  }\n  return res;\n}\nexport const maxPoolConfig = {\n  kernelName: MaxPool,\n  backendName: 'cpu',\n  kernelFunc: maxPool\n};", "map": {"version": 3, "names": ["backend_util", "MaxPool", "util", "assertNotComplex", "pool", "identity", "maxPool", "args", "inputs", "backend", "attrs", "x", "filterSize", "strides", "pad", "dimRoundingMode", "dilations", "assert", "eitherStridesOrDilationsAreOne", "concat", "convInfo", "computePool2DInfo", "shape", "res", "filterWidth", "filterHeight", "arraysEqual", "inShape", "outShape", "xValues", "data", "get", "dataId", "values", "computeStrides", "buffer", "dtype", "makeTensorInfo", "maxPoolConfig", "kernelName", "backendName", "kernelFunc"], "sources": ["C:\\tfjs-backend-cpu\\src\\kernels\\MaxPool.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport {backend_util, KernelConfig, KernelFunc, MaxPool, MaxPoolAttrs, MaxPoolInputs, TensorInfo, TypedArray, util} from '@tensorflow/tfjs-core';\n\nimport {MathBackendCPU} from '../backend_cpu';\nimport {assertNotComplex} from '../cpu_util';\nimport {pool} from '../utils/pool_utils';\nimport {identity} from './Identity';\n\nexport function maxPool(\n    args:\n        {inputs: MaxPoolInputs, backend: MathBackendCPU, attrs: MaxPoolAttrs}):\n    TensorInfo {\n  const {inputs, backend, attrs} = args;\n  const {x} = inputs;\n  assertNotComplex(x, 'maxPool');\n  const {filterSize, strides, pad, dimRoundingMode} = attrs;\n  const dilations = 1;\n\n  util.assert(\n      backend_util.eitherStridesOrDilationsAreOne(strides, dilations),\n      () => 'Error in maxPool: Either strides or dilations must be 1. ' +\n          `Got strides ${strides} and dilations '${dilations}'`);\n\n  const convInfo = backend_util.computePool2DInfo(\n      x.shape as [number, number, number, number], filterSize, strides,\n      dilations, pad, dimRoundingMode);\n  let res: TensorInfo;\n\n  if (convInfo.filterWidth === 1 && convInfo.filterHeight === 1 &&\n      util.arraysEqual(convInfo.inShape, convInfo.outShape)) {\n    res = identity({inputs: {x}, backend});\n  } else {\n    const xValues = backend.data.get(x.dataId).values as TypedArray;\n    const strides = util.computeStrides(x.shape);\n    const buffer = pool(xValues, x.shape, x.dtype, strides, convInfo, 'max');\n    res = backend.makeTensorInfo(\n        convInfo.outShape, x.dtype, buffer.values as TypedArray);\n  }\n  return res;\n}\n\nexport const maxPoolConfig: KernelConfig = {\n  kernelName: MaxPool,\n  backendName: 'cpu',\n  kernelFunc: maxPool as unknown as KernelFunc\n};\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAgBA,SAAQA,YAAY,EAA4BC,OAAO,EAAuDC,IAAI,QAAO,uBAAuB;AAGhJ,SAAQC,gBAAgB,QAAO,aAAa;AAC5C,SAAQC,IAAI,QAAO,qBAAqB;AACxC,SAAQC,QAAQ,QAAO,YAAY;AAEnC,OAAM,SAAUC,OAAOA,CACnBC,IACyE;EAE3E,MAAM;IAACC,MAAM;IAAEC,OAAO;IAAEC;EAAK,CAAC,GAAGH,IAAI;EACrC,MAAM;IAACI;EAAC,CAAC,GAAGH,MAAM;EAClBL,gBAAgB,CAACQ,CAAC,EAAE,SAAS,CAAC;EAC9B,MAAM;IAACC,UAAU;IAAEC,OAAO;IAAEC,GAAG;IAAEC;EAAe,CAAC,GAAGL,KAAK;EACzD,MAAMM,SAAS,GAAG,CAAC;EAEnBd,IAAI,CAACe,MAAM,CACPjB,YAAY,CAACkB,8BAA8B,CAACL,OAAO,EAAEG,SAAS,CAAC,EAC/D,MAAM,2DAA2D,kBAAAG,MAAA,CAC9CN,OAAO,sBAAAM,MAAA,CAAmBH,SAAS,MAAG,CAAC;EAE9D,MAAMI,QAAQ,GAAGpB,YAAY,CAACqB,iBAAiB,CAC3CV,CAAC,CAACW,KAAyC,EAAEV,UAAU,EAAEC,OAAO,EAChEG,SAAS,EAAEF,GAAG,EAAEC,eAAe,CAAC;EACpC,IAAIQ,GAAe;EAEnB,IAAIH,QAAQ,CAACI,WAAW,KAAK,CAAC,IAAIJ,QAAQ,CAACK,YAAY,KAAK,CAAC,IACzDvB,IAAI,CAACwB,WAAW,CAACN,QAAQ,CAACO,OAAO,EAAEP,QAAQ,CAACQ,QAAQ,CAAC,EAAE;IACzDL,GAAG,GAAGlB,QAAQ,CAAC;MAACG,MAAM,EAAE;QAACG;MAAC,CAAC;MAAEF;IAAO,CAAC,CAAC;GACvC,MAAM;IACL,MAAMoB,OAAO,GAAGpB,OAAO,CAACqB,IAAI,CAACC,GAAG,CAACpB,CAAC,CAACqB,MAAM,CAAC,CAACC,MAAoB;IAC/D,MAAMpB,OAAO,GAAGX,IAAI,CAACgC,cAAc,CAACvB,CAAC,CAACW,KAAK,CAAC;IAC5C,MAAMa,MAAM,GAAG/B,IAAI,CAACyB,OAAO,EAAElB,CAAC,CAACW,KAAK,EAAEX,CAAC,CAACyB,KAAK,EAAEvB,OAAO,EAAEO,QAAQ,EAAE,KAAK,CAAC;IACxEG,GAAG,GAAGd,OAAO,CAAC4B,cAAc,CACxBjB,QAAQ,CAACQ,QAAQ,EAAEjB,CAAC,CAACyB,KAAK,EAAED,MAAM,CAACF,MAAoB,CAAC;;EAE9D,OAAOV,GAAG;AACZ;AAEA,OAAO,MAAMe,aAAa,GAAiB;EACzCC,UAAU,EAAEtC,OAAO;EACnBuC,WAAW,EAAE,KAAK;EAClBC,UAAU,EAAEnC;CACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}