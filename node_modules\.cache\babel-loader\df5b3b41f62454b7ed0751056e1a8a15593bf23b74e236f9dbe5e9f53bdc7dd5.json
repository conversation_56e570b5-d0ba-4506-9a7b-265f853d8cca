{"ast": null, "code": "/**\n * @license\n * Copyright 2021 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { SparseFillEmptyRows } from '@tensorflow/tfjs-core';\nimport { sparseFillEmptyRowsImplCPU } from '../kernel_utils/shared';\nexport function sparseFillEmptyRows(args) {\n  const {\n    inputs,\n    backend\n  } = args;\n  const {\n    indices,\n    values,\n    denseShape,\n    defaultValue\n  } = inputs;\n  if (denseShape.shape.length !== 1) {\n    throw new Error(`Dense shape must be a vector, saw:\n         ${denseShape.shape}`);\n  }\n  if (indices.shape.length !== 2) {\n    throw new Error(`Indices must be a matrix, saw:\n         ${indices.shape}`);\n  }\n  if (values.shape.length !== 1) {\n    throw new Error(`Values must be a vector, saw:\n         ${values.shape}`);\n  }\n  if (defaultValue.shape.length !== 0) {\n    throw new Error(`Default value must be a scalar, saw:\n        ${defaultValue.shape}`);\n  }\n  const $indices = backend.readSync(indices.dataId);\n  const $values = backend.readSync(values.dataId);\n  const $denseShape = backend.readSync(denseShape.dataId);\n  const $defaultValue = backend.readSync(defaultValue.dataId)[0];\n  const [outputIndices, outputIndicesShape, outputValues, emptyRowIndicator, reverseIndexMap] = sparseFillEmptyRowsImplCPU($indices, indices.shape, indices.dtype, $values, values.dtype, $denseShape, $defaultValue);\n  return [backend.makeTensorInfo(outputIndicesShape, indices.dtype, outputIndices), backend.makeTensorInfo([outputIndicesShape[0]], values.dtype, outputValues), backend.makeTensorInfo([emptyRowIndicator.length], 'bool', new Uint8Array(emptyRowIndicator.map(value => Number(value)))), backend.makeTensorInfo([reverseIndexMap.length], indices.dtype, new Int32Array(reverseIndexMap))];\n}\nexport const sparseFillEmptyRowsConfig = {\n  kernelName: SparseFillEmptyRows,\n  backendName: 'webgl',\n  kernelFunc: sparseFillEmptyRows\n};", "map": {"version": 3, "names": ["SparseFillEmptyRows", "sparseFillEmptyRowsImplCPU", "sparseFillEmptyRows", "args", "inputs", "backend", "indices", "values", "denseShape", "defaultValue", "shape", "length", "Error", "$indices", "readSync", "dataId", "$values", "$denseShape", "$defaultValue", "outputIndices", "outputIndicesShape", "outputValues", "emptyRowIndicator", "reverseIndexMap", "dtype", "makeTensorInfo", "Uint8Array", "map", "value", "Number", "Int32Array", "sparseFillEmptyRowsConfig", "kernelName", "backendName", "kernelFunc"], "sources": ["C:\\tfjs-backend-webgl\\src\\kernels\\SparseFillEmptyRows.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2021 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {KernelConfig, KernelFunc, SparseFillEmptyRows, SparseFillEmptyRowsInputs, TensorInfo, TypedArray} from '@tensorflow/tfjs-core';\n\nimport {MathBackendWebGL} from '../backend_webgl';\nimport {sparseFillEmptyRowsImplCPU} from '../kernel_utils/shared';\n\nexport function sparseFillEmptyRows(args: {\n  inputs: SparseFillEmptyRowsInputs,\n  backend: MathBackendWebGL\n}): [TensorInfo, TensorInfo, TensorInfo, TensorInfo] {\n  const {inputs, backend} = args;\n  const {indices, values, denseShape, defaultValue} = inputs;\n  if (denseShape.shape.length !== 1) {\n    throw new Error(`Dense shape must be a vector, saw:\n         ${denseShape.shape}`);\n  }\n  if (indices.shape.length !== 2) {\n    throw new Error(`Indices must be a matrix, saw:\n         ${indices.shape}`);\n  }\n  if (values.shape.length !== 1) {\n    throw new Error(`Values must be a vector, saw:\n         ${values.shape}`);\n  }\n  if (defaultValue.shape.length !== 0) {\n    throw new Error(`Default value must be a scalar, saw:\n        ${defaultValue.shape}`);\n  }\n\n  const $indices = backend.readSync(indices.dataId) as TypedArray;\n  const $values = backend.readSync(values.dataId) as TypedArray;\n  const $denseShape = backend.readSync(denseShape.dataId) as TypedArray;\n  const $defaultValue =\n      backend.readSync(defaultValue.dataId)[0] as number;\n\n  const [outputIndices, outputIndicesShape, outputValues,\n         emptyRowIndicator, reverseIndexMap] =\n      sparseFillEmptyRowsImplCPU(\n          $indices, indices.shape, indices.dtype, $values, values.dtype,\n          $denseShape, $defaultValue);\n  return [\n    backend.makeTensorInfo(outputIndicesShape, indices.dtype, outputIndices),\n    backend.makeTensorInfo(\n        [outputIndicesShape[0]], values.dtype, outputValues),\n    backend.makeTensorInfo(\n        [emptyRowIndicator.length], 'bool',\n        new Uint8Array(\n            emptyRowIndicator.map((value: boolean) => Number(value)))),\n    backend.makeTensorInfo(\n        [reverseIndexMap.length], indices.dtype,\n        new Int32Array(reverseIndexMap)),\n  ];\n}\n\nexport const sparseFillEmptyRowsConfig: KernelConfig = {\n  kernelName: SparseFillEmptyRows,\n  backendName: 'webgl',\n  kernelFunc: sparseFillEmptyRows as unknown as KernelFunc,\n};\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAiBA,SAAkCA,mBAAmB,QAA0D,uBAAuB;AAGtI,SAAQC,0BAA0B,QAAO,wBAAwB;AAEjE,OAAM,SAAUC,mBAAmBA,CAACC,IAGnC;EACC,MAAM;IAACC,MAAM;IAAEC;EAAO,CAAC,GAAGF,IAAI;EAC9B,MAAM;IAACG,OAAO;IAAEC,MAAM;IAAEC,UAAU;IAAEC;EAAY,CAAC,GAAGL,MAAM;EAC1D,IAAII,UAAU,CAACE,KAAK,CAACC,MAAM,KAAK,CAAC,EAAE;IACjC,MAAM,IAAIC,KAAK,CAAC;WACTJ,UAAU,CAACE,KAAK,EAAE,CAAC;;EAE5B,IAAIJ,OAAO,CAACI,KAAK,CAACC,MAAM,KAAK,CAAC,EAAE;IAC9B,MAAM,IAAIC,KAAK,CAAC;WACTN,OAAO,CAACI,KAAK,EAAE,CAAC;;EAEzB,IAAIH,MAAM,CAACG,KAAK,CAACC,MAAM,KAAK,CAAC,EAAE;IAC7B,MAAM,IAAIC,KAAK,CAAC;WACTL,MAAM,CAACG,KAAK,EAAE,CAAC;;EAExB,IAAID,YAAY,CAACC,KAAK,CAACC,MAAM,KAAK,CAAC,EAAE;IACnC,MAAM,IAAIC,KAAK,CAAC;UACVH,YAAY,CAACC,KAAK,EAAE,CAAC;;EAG7B,MAAMG,QAAQ,GAAGR,OAAO,CAACS,QAAQ,CAACR,OAAO,CAACS,MAAM,CAAe;EAC/D,MAAMC,OAAO,GAAGX,OAAO,CAACS,QAAQ,CAACP,MAAM,CAACQ,MAAM,CAAe;EAC7D,MAAME,WAAW,GAAGZ,OAAO,CAACS,QAAQ,CAACN,UAAU,CAACO,MAAM,CAAe;EACrE,MAAMG,aAAa,GACfb,OAAO,CAACS,QAAQ,CAACL,YAAY,CAACM,MAAM,CAAC,CAAC,CAAC,CAAW;EAEtD,MAAM,CAACI,aAAa,EAAEC,kBAAkB,EAAEC,YAAY,EAC/CC,iBAAiB,EAAEC,eAAe,CAAC,GACtCtB,0BAA0B,CACtBY,QAAQ,EAAEP,OAAO,CAACI,KAAK,EAAEJ,OAAO,CAACkB,KAAK,EAAER,OAAO,EAAET,MAAM,CAACiB,KAAK,EAC7DP,WAAW,EAAEC,aAAa,CAAC;EACnC,OAAO,CACLb,OAAO,CAACoB,cAAc,CAACL,kBAAkB,EAAEd,OAAO,CAACkB,KAAK,EAAEL,aAAa,CAAC,EACxEd,OAAO,CAACoB,cAAc,CAClB,CAACL,kBAAkB,CAAC,CAAC,CAAC,CAAC,EAAEb,MAAM,CAACiB,KAAK,EAAEH,YAAY,CAAC,EACxDhB,OAAO,CAACoB,cAAc,CAClB,CAACH,iBAAiB,CAACX,MAAM,CAAC,EAAE,MAAM,EAClC,IAAIe,UAAU,CACVJ,iBAAiB,CAACK,GAAG,CAAEC,KAAc,IAAKC,MAAM,CAACD,KAAK,CAAC,CAAC,CAAC,CAAC,EAClEvB,OAAO,CAACoB,cAAc,CAClB,CAACF,eAAe,CAACZ,MAAM,CAAC,EAAEL,OAAO,CAACkB,KAAK,EACvC,IAAIM,UAAU,CAACP,eAAe,CAAC,CAAC,CACrC;AACH;AAEA,OAAO,MAAMQ,yBAAyB,GAAiB;EACrDC,UAAU,EAAEhC,mBAAmB;EAC/BiC,WAAW,EAAE,OAAO;EACpBC,UAAU,EAAEhC;CACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}