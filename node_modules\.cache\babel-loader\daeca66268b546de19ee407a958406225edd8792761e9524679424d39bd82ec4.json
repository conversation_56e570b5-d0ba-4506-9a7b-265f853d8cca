{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.transformReply = exports.transformArguments = exports.FIRST_KEY_INDEX = void 0;\nexports.FIRST_KEY_INDEX = 1;\nfunction transformArguments(key, path, by) {\n  return ['JSON.NUMMULTBY', key, path, by.toString()];\n}\nexports.transformArguments = transformArguments;\nvar _1 = require(\".\");\nObject.defineProperty(exports, \"transformReply\", {\n  enumerable: true,\n  get: function () {\n    return _1.transformNumbersReply;\n  }\n});", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "transformReply", "transformArguments", "FIRST_KEY_INDEX", "key", "path", "by", "toString", "_1", "require", "enumerable", "get", "transformNumbersReply"], "sources": ["C:/tmsft/node_modules/@redis/json/dist/commands/NUMMULTBY.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.transformReply = exports.transformArguments = exports.FIRST_KEY_INDEX = void 0;\nexports.FIRST_KEY_INDEX = 1;\nfunction transformArguments(key, path, by) {\n    return ['JSON.NUMMULTBY', key, path, by.toString()];\n}\nexports.transformArguments = transformArguments;\nvar _1 = require(\".\");\nObject.defineProperty(exports, \"transformReply\", { enumerable: true, get: function () { return _1.transformNumbersReply; } });\n"], "mappings": "AAAA,YAAY;;AACZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEC,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7DD,OAAO,CAACE,cAAc,GAAGF,OAAO,CAACG,kBAAkB,GAAGH,OAAO,CAACI,eAAe,GAAG,KAAK,CAAC;AACtFJ,OAAO,CAACI,eAAe,GAAG,CAAC;AAC3B,SAASD,kBAAkBA,CAACE,GAAG,EAAEC,IAAI,EAAEC,EAAE,EAAE;EACvC,OAAO,CAAC,gBAAgB,EAAEF,GAAG,EAAEC,IAAI,EAAEC,EAAE,CAACC,QAAQ,CAAC,CAAC,CAAC;AACvD;AACAR,OAAO,CAACG,kBAAkB,GAAGA,kBAAkB;AAC/C,IAAIM,EAAE,GAAGC,OAAO,CAAC,GAAG,CAAC;AACrBZ,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,gBAAgB,EAAE;EAAEW,UAAU,EAAE,IAAI;EAAEC,GAAG,EAAE,SAAAA,CAAA,EAAY;IAAE,OAAOH,EAAE,CAACI,qBAAqB;EAAE;AAAE,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}