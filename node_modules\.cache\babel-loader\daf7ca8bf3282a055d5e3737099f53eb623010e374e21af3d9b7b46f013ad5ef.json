{"ast": null, "code": "/**\n * @license\n * Copyright 2018 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { ENGINE } from '../engine';\nimport { Step } from '../kernel_names';\nimport { convertToTensor } from '../tensor_util_env';\nimport { op } from './operation';\n/**\n * Computes step of the input `tf.Tensor` element-wise: `x > 0 ? 1 : alpha`\n *\n * ```js\n * const x = tf.tensor1d([0, 2, -1, -3]);\n *\n * x.step(.5).print();  // or tf.step(x, .5)\n * ```\n * @param x The input tensor.\n * @param alpha The gradient when input is negative. Defaults to 0.\n *\n * @doc {heading: 'Operations', subheading: 'Basic math'}\n */\nfunction step_(x) {\n  let alpha = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 0.0;\n  const $x = convertToTensor(x, 'x', 'step');\n  const inputs = {\n    x: $x\n  };\n  const attrs = {\n    alpha\n  };\n  return ENGINE.runKernel(Step, inputs, attrs);\n}\nexport const step = /* @__PURE__ */op({\n  step_\n});", "map": {"version": 3, "names": ["ENGINE", "Step", "convertToTensor", "op", "step_", "x", "alpha", "arguments", "length", "undefined", "$x", "inputs", "attrs", "runKernel", "step"], "sources": ["C:\\tfjs-core\\src\\ops\\step.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2018 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {ENGINE} from '../engine';\nimport {Step, StepAttrs, StepInputs} from '../kernel_names';\nimport {NamedAttrMap} from '../kernel_registry';\nimport {Tensor} from '../tensor';\nimport {NamedTensorMap} from '../tensor_types';\nimport {convertToTensor} from '../tensor_util_env';\nimport {TensorLike} from '../types';\n\nimport {op} from './operation';\n\n/**\n * Computes step of the input `tf.Tensor` element-wise: `x > 0 ? 1 : alpha`\n *\n * ```js\n * const x = tf.tensor1d([0, 2, -1, -3]);\n *\n * x.step(.5).print();  // or tf.step(x, .5)\n * ```\n * @param x The input tensor.\n * @param alpha The gradient when input is negative. Defaults to 0.\n *\n * @doc {heading: 'Operations', subheading: 'Basic math'}\n */\nfunction step_<T extends Tensor>(x: T|TensorLike, alpha = 0.0): T {\n  const $x = convertToTensor(x, 'x', 'step');\n\n  const inputs: StepInputs = {x: $x};\n  const attrs: StepAttrs = {alpha};\n\n  return ENGINE.runKernel(\n      Step, inputs as unknown as NamedTensorMap,\n      attrs as unknown as NamedAttrMap);\n}\nexport const step = /* @__PURE__ */ op({step_});\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAiBA,SAAQA,MAAM,QAAO,WAAW;AAChC,SAAQC,IAAI,QAA8B,iBAAiB;AAI3D,SAAQC,eAAe,QAAO,oBAAoB;AAGlD,SAAQC,EAAE,QAAO,aAAa;AAE9B;;;;;;;;;;;;;AAaA,SAASC,KAAKA,CAAmBC,CAAe,EAAa;EAAA,IAAXC,KAAK,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,GAAG;EAC3D,MAAMG,EAAE,GAAGR,eAAe,CAACG,CAAC,EAAE,GAAG,EAAE,MAAM,CAAC;EAE1C,MAAMM,MAAM,GAAe;IAACN,CAAC,EAAEK;EAAE,CAAC;EAClC,MAAME,KAAK,GAAc;IAACN;EAAK,CAAC;EAEhC,OAAON,MAAM,CAACa,SAAS,CACnBZ,IAAI,EAAEU,MAAmC,EACzCC,KAAgC,CAAC;AACvC;AACA,OAAO,MAAME,IAAI,GAAG,eAAgBX,EAAE,CAAC;EAACC;AAAK,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}