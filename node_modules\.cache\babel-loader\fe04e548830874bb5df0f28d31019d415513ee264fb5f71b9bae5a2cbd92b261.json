{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.NoticeMessage = exports.DataRowMessage = exports.CommandCompleteMessage = exports.ReadyForQueryMessage = exports.NotificationResponseMessage = exports.BackendKeyDataMessage = exports.AuthenticationMD5Password = exports.ParameterStatusMessage = exports.ParameterDescriptionMessage = exports.RowDescriptionMessage = exports.Field = exports.CopyResponse = exports.CopyDataMessage = exports.DatabaseError = exports.copyDone = exports.emptyQuery = exports.replicationStart = exports.portalSuspended = exports.noData = exports.closeComplete = exports.bindComplete = exports.parseComplete = void 0;\nexports.parseComplete = {\n  name: 'parseComplete',\n  length: 5\n};\nexports.bindComplete = {\n  name: 'bindComplete',\n  length: 5\n};\nexports.closeComplete = {\n  name: 'closeComplete',\n  length: 5\n};\nexports.noData = {\n  name: 'noData',\n  length: 5\n};\nexports.portalSuspended = {\n  name: 'portalSuspended',\n  length: 5\n};\nexports.replicationStart = {\n  name: 'replicationStart',\n  length: 4\n};\nexports.emptyQuery = {\n  name: 'emptyQuery',\n  length: 4\n};\nexports.copyDone = {\n  name: 'copyDone',\n  length: 4\n};\nclass DatabaseError extends Error {\n  constructor(message, length, name) {\n    super(message);\n    this.length = length;\n    this.name = name;\n  }\n}\nexports.DatabaseError = DatabaseError;\nclass CopyDataMessage {\n  constructor(length, chunk) {\n    this.length = length;\n    this.chunk = chunk;\n    this.name = 'copyData';\n  }\n}\nexports.CopyDataMessage = CopyDataMessage;\nclass CopyResponse {\n  constructor(length, name, binary, columnCount) {\n    this.length = length;\n    this.name = name;\n    this.binary = binary;\n    this.columnTypes = new Array(columnCount);\n  }\n}\nexports.CopyResponse = CopyResponse;\nclass Field {\n  constructor(name, tableID, columnID, dataTypeID, dataTypeSize, dataTypeModifier, format) {\n    this.name = name;\n    this.tableID = tableID;\n    this.columnID = columnID;\n    this.dataTypeID = dataTypeID;\n    this.dataTypeSize = dataTypeSize;\n    this.dataTypeModifier = dataTypeModifier;\n    this.format = format;\n  }\n}\nexports.Field = Field;\nclass RowDescriptionMessage {\n  constructor(length, fieldCount) {\n    this.length = length;\n    this.fieldCount = fieldCount;\n    this.name = 'rowDescription';\n    this.fields = new Array(this.fieldCount);\n  }\n}\nexports.RowDescriptionMessage = RowDescriptionMessage;\nclass ParameterDescriptionMessage {\n  constructor(length, parameterCount) {\n    this.length = length;\n    this.parameterCount = parameterCount;\n    this.name = 'parameterDescription';\n    this.dataTypeIDs = new Array(this.parameterCount);\n  }\n}\nexports.ParameterDescriptionMessage = ParameterDescriptionMessage;\nclass ParameterStatusMessage {\n  constructor(length, parameterName, parameterValue) {\n    this.length = length;\n    this.parameterName = parameterName;\n    this.parameterValue = parameterValue;\n    this.name = 'parameterStatus';\n  }\n}\nexports.ParameterStatusMessage = ParameterStatusMessage;\nclass AuthenticationMD5Password {\n  constructor(length, salt) {\n    this.length = length;\n    this.salt = salt;\n    this.name = 'authenticationMD5Password';\n  }\n}\nexports.AuthenticationMD5Password = AuthenticationMD5Password;\nclass BackendKeyDataMessage {\n  constructor(length, processID, secretKey) {\n    this.length = length;\n    this.processID = processID;\n    this.secretKey = secretKey;\n    this.name = 'backendKeyData';\n  }\n}\nexports.BackendKeyDataMessage = BackendKeyDataMessage;\nclass NotificationResponseMessage {\n  constructor(length, processId, channel, payload) {\n    this.length = length;\n    this.processId = processId;\n    this.channel = channel;\n    this.payload = payload;\n    this.name = 'notification';\n  }\n}\nexports.NotificationResponseMessage = NotificationResponseMessage;\nclass ReadyForQueryMessage {\n  constructor(length, status) {\n    this.length = length;\n    this.status = status;\n    this.name = 'readyForQuery';\n  }\n}\nexports.ReadyForQueryMessage = ReadyForQueryMessage;\nclass CommandCompleteMessage {\n  constructor(length, text) {\n    this.length = length;\n    this.text = text;\n    this.name = 'commandComplete';\n  }\n}\nexports.CommandCompleteMessage = CommandCompleteMessage;\nclass DataRowMessage {\n  constructor(length, fields) {\n    this.length = length;\n    this.fields = fields;\n    this.name = 'dataRow';\n    this.fieldCount = fields.length;\n  }\n}\nexports.DataRowMessage = DataRowMessage;\nclass NoticeMessage {\n  constructor(length, message) {\n    this.length = length;\n    this.message = message;\n    this.name = 'notice';\n  }\n}\nexports.NoticeMessage = NoticeMessage;", "map": {"version": 3, "names": ["exports", "parseComplete", "name", "length", "bindComplete", "closeComplete", "noData", "portalSuspended", "replicationStart", "emptyQuery", "copyDone", "DatabaseError", "Error", "constructor", "message", "CopyDataMessage", "chunk", "CopyResponse", "binary", "columnCount", "columnTypes", "Array", "Field", "tableID", "columnID", "dataTypeID", "dataTypeSize", "dataTypeModifier", "format", "RowDescriptionMessage", "fieldCount", "fields", "ParameterDescriptionMessage", "parameterCount", "dataTypeIDs", "ParameterStatusMessage", "parameterName", "parameterValue", "AuthenticationMD5Password", "salt", "BackendKeyDataMessage", "processID", "secret<PERSON>ey", "NotificationResponseMessage", "processId", "channel", "payload", "ReadyForQueryMessage", "status", "CommandCompleteMessage", "text", "DataRowMessage", "NoticeMessage"], "sources": ["C:\\tmsft\\node_modules\\pg-protocol\\src\\messages.ts"], "sourcesContent": ["export type Mode = 'text' | 'binary'\n\nexport type MessageName =\n  | 'parseComplete'\n  | 'bindComplete'\n  | 'closeComplete'\n  | 'noData'\n  | 'portalSuspended'\n  | 'replicationStart'\n  | 'emptyQuery'\n  | 'copyDone'\n  | 'copyData'\n  | 'rowDescription'\n  | 'parameterDescription'\n  | 'parameterStatus'\n  | 'backendKeyData'\n  | 'notification'\n  | 'readyForQuery'\n  | 'commandComplete'\n  | 'dataRow'\n  | 'copyInResponse'\n  | 'copyOutResponse'\n  | 'authenticationOk'\n  | 'authenticationMD5Password'\n  | 'authenticationCleartextPassword'\n  | 'authenticationSASL'\n  | 'authenticationSASLContinue'\n  | 'authenticationSASLFinal'\n  | 'error'\n  | 'notice'\n\nexport interface BackendMessage {\n  name: MessageName\n  length: number\n}\n\nexport const parseComplete: BackendMessage = {\n  name: 'parseComplete',\n  length: 5,\n}\n\nexport const bindComplete: BackendMessage = {\n  name: 'bindComplete',\n  length: 5,\n}\n\nexport const closeComplete: BackendMessage = {\n  name: 'closeComplete',\n  length: 5,\n}\n\nexport const noData: BackendMessage = {\n  name: 'noData',\n  length: 5,\n}\n\nexport const portalSuspended: BackendMessage = {\n  name: 'portalSuspended',\n  length: 5,\n}\n\nexport const replicationStart: BackendMessage = {\n  name: 'replicationStart',\n  length: 4,\n}\n\nexport const emptyQuery: BackendMessage = {\n  name: 'emptyQuery',\n  length: 4,\n}\n\nexport const copyDone: BackendMessage = {\n  name: 'copyDone',\n  length: 4,\n}\n\ninterface NoticeOrError {\n  message: string | undefined\n  severity: string | undefined\n  code: string | undefined\n  detail: string | undefined\n  hint: string | undefined\n  position: string | undefined\n  internalPosition: string | undefined\n  internalQuery: string | undefined\n  where: string | undefined\n  schema: string | undefined\n  table: string | undefined\n  column: string | undefined\n  dataType: string | undefined\n  constraint: string | undefined\n  file: string | undefined\n  line: string | undefined\n  routine: string | undefined\n}\n\nexport class DatabaseError extends Error implements NoticeOrError {\n  public severity: string | undefined\n  public code: string | undefined\n  public detail: string | undefined\n  public hint: string | undefined\n  public position: string | undefined\n  public internalPosition: string | undefined\n  public internalQuery: string | undefined\n  public where: string | undefined\n  public schema: string | undefined\n  public table: string | undefined\n  public column: string | undefined\n  public dataType: string | undefined\n  public constraint: string | undefined\n  public file: string | undefined\n  public line: string | undefined\n  public routine: string | undefined\n  constructor(\n    message: string,\n    public readonly length: number,\n    public readonly name: MessageName\n  ) {\n    super(message)\n  }\n}\n\nexport class CopyDataMessage {\n  public readonly name = 'copyData'\n  constructor(\n    public readonly length: number,\n    public readonly chunk: Buffer\n  ) {}\n}\n\nexport class CopyResponse {\n  public readonly columnTypes: number[]\n  constructor(\n    public readonly length: number,\n    public readonly name: MessageName,\n    public readonly binary: boolean,\n    columnCount: number\n  ) {\n    this.columnTypes = new Array(columnCount)\n  }\n}\n\nexport class Field {\n  constructor(\n    public readonly name: string,\n    public readonly tableID: number,\n    public readonly columnID: number,\n    public readonly dataTypeID: number,\n    public readonly dataTypeSize: number,\n    public readonly dataTypeModifier: number,\n    public readonly format: Mode\n  ) {}\n}\n\nexport class RowDescriptionMessage {\n  public readonly name: MessageName = 'rowDescription'\n  public readonly fields: Field[]\n  constructor(\n    public readonly length: number,\n    public readonly fieldCount: number\n  ) {\n    this.fields = new Array(this.fieldCount)\n  }\n}\n\nexport class ParameterDescriptionMessage {\n  public readonly name: MessageName = 'parameterDescription'\n  public readonly dataTypeIDs: number[]\n  constructor(\n    public readonly length: number,\n    public readonly parameterCount: number\n  ) {\n    this.dataTypeIDs = new Array(this.parameterCount)\n  }\n}\n\nexport class ParameterStatusMessage {\n  public readonly name: MessageName = 'parameterStatus'\n  constructor(\n    public readonly length: number,\n    public readonly parameterName: string,\n    public readonly parameterValue: string\n  ) {}\n}\n\nexport class AuthenticationMD5Password implements BackendMessage {\n  public readonly name: MessageName = 'authenticationMD5Password'\n  constructor(\n    public readonly length: number,\n    public readonly salt: Buffer\n  ) {}\n}\n\nexport class BackendKeyDataMessage {\n  public readonly name: MessageName = 'backendKeyData'\n  constructor(\n    public readonly length: number,\n    public readonly processID: number,\n    public readonly secretKey: number\n  ) {}\n}\n\nexport class NotificationResponseMessage {\n  public readonly name: MessageName = 'notification'\n  constructor(\n    public readonly length: number,\n    public readonly processId: number,\n    public readonly channel: string,\n    public readonly payload: string\n  ) {}\n}\n\nexport class ReadyForQueryMessage {\n  public readonly name: MessageName = 'readyForQuery'\n  constructor(\n    public readonly length: number,\n    public readonly status: string\n  ) {}\n}\n\nexport class CommandCompleteMessage {\n  public readonly name: MessageName = 'commandComplete'\n  constructor(\n    public readonly length: number,\n    public readonly text: string\n  ) {}\n}\n\nexport class DataRowMessage {\n  public readonly fieldCount: number\n  public readonly name: MessageName = 'dataRow'\n  constructor(\n    public length: number,\n    public fields: any[]\n  ) {\n    this.fieldCount = fields.length\n  }\n}\n\nexport class NoticeMessage implements BackendMessage, NoticeOrError {\n  constructor(\n    public readonly length: number,\n    public readonly message: string | undefined\n  ) {}\n  public readonly name = 'notice'\n  public severity: string | undefined\n  public code: string | undefined\n  public detail: string | undefined\n  public hint: string | undefined\n  public position: string | undefined\n  public internalPosition: string | undefined\n  public internalQuery: string | undefined\n  public where: string | undefined\n  public schema: string | undefined\n  public table: string | undefined\n  public column: string | undefined\n  public dataType: string | undefined\n  public constraint: string | undefined\n  public file: string | undefined\n  public line: string | undefined\n  public routine: string | undefined\n}\n"], "mappings": ";;;;;;AAoCaA,OAAA,CAAAC,aAAa,GAAmB;EAC3CC,IAAI,EAAE,eAAe;EACrBC,MAAM,EAAE;CACT;AAEYH,OAAA,CAAAI,YAAY,GAAmB;EAC1CF,IAAI,EAAE,cAAc;EACpBC,MAAM,EAAE;CACT;AAEYH,OAAA,CAAAK,aAAa,GAAmB;EAC3CH,IAAI,EAAE,eAAe;EACrBC,MAAM,EAAE;CACT;AAEYH,OAAA,CAAAM,MAAM,GAAmB;EACpCJ,IAAI,EAAE,QAAQ;EACdC,MAAM,EAAE;CACT;AAEYH,OAAA,CAAAO,eAAe,GAAmB;EAC7CL,IAAI,EAAE,iBAAiB;EACvBC,MAAM,EAAE;CACT;AAEYH,OAAA,CAAAQ,gBAAgB,GAAmB;EAC9CN,IAAI,EAAE,kBAAkB;EACxBC,MAAM,EAAE;CACT;AAEYH,OAAA,CAAAS,UAAU,GAAmB;EACxCP,IAAI,EAAE,YAAY;EAClBC,MAAM,EAAE;CACT;AAEYH,OAAA,CAAAU,QAAQ,GAAmB;EACtCR,IAAI,EAAE,UAAU;EAChBC,MAAM,EAAE;CACT;AAsBD,MAAaQ,aAAc,SAAQC,KAAK;EAiBtCC,YACEC,OAAe,EACCX,MAAc,EACdD,IAAiB;IAEjC,KAAK,CAACY,OAAO,CAAC;IAHE,KAAAX,MAAM,GAANA,MAAM;IACN,KAAAD,IAAI,GAAJA,IAAI;EAGtB;;AAvBFF,OAAA,CAAAW,aAAA,GAAAA,aAAA;AA0BA,MAAaI,eAAe;EAE1BF,YACkBV,MAAc,EACda,KAAa;IADb,KAAAb,MAAM,GAANA,MAAM;IACN,KAAAa,KAAK,GAALA,KAAK;IAHP,KAAAd,IAAI,GAAG,UAAU;EAI9B;;AALLF,OAAA,CAAAe,eAAA,GAAAA,eAAA;AAQA,MAAaE,YAAY;EAEvBJ,YACkBV,MAAc,EACdD,IAAiB,EACjBgB,MAAe,EAC/BC,WAAmB;IAHH,KAAAhB,MAAM,GAANA,MAAM;IACN,KAAAD,IAAI,GAAJA,IAAI;IACJ,KAAAgB,MAAM,GAANA,MAAM;IAGtB,IAAI,CAACE,WAAW,GAAG,IAAIC,KAAK,CAACF,WAAW,CAAC;EAC3C;;AATFnB,OAAA,CAAAiB,YAAA,GAAAA,YAAA;AAYA,MAAaK,KAAK;EAChBT,YACkBX,IAAY,EACZqB,OAAe,EACfC,QAAgB,EAChBC,UAAkB,EAClBC,YAAoB,EACpBC,gBAAwB,EACxBC,MAAY;IANZ,KAAA1B,IAAI,GAAJA,IAAI;IACJ,KAAAqB,OAAO,GAAPA,OAAO;IACP,KAAAC,QAAQ,GAARA,QAAQ;IACR,KAAAC,UAAU,GAAVA,UAAU;IACV,KAAAC,YAAY,GAAZA,YAAY;IACZ,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAC,MAAM,GAANA,MAAM;EACrB;;AATL5B,OAAA,CAAAsB,KAAA,GAAAA,KAAA;AAYA,MAAaO,qBAAqB;EAGhChB,YACkBV,MAAc,EACd2B,UAAkB;IADlB,KAAA3B,MAAM,GAANA,MAAM;IACN,KAAA2B,UAAU,GAAVA,UAAU;IAJZ,KAAA5B,IAAI,GAAgB,gBAAgB;IAMlD,IAAI,CAAC6B,MAAM,GAAG,IAAIV,KAAK,CAAC,IAAI,CAACS,UAAU,CAAC;EAC1C;;AARF9B,OAAA,CAAA6B,qBAAA,GAAAA,qBAAA;AAWA,MAAaG,2BAA2B;EAGtCnB,YACkBV,MAAc,EACd8B,cAAsB;IADtB,KAAA9B,MAAM,GAANA,MAAM;IACN,KAAA8B,cAAc,GAAdA,cAAc;IAJhB,KAAA/B,IAAI,GAAgB,sBAAsB;IAMxD,IAAI,CAACgC,WAAW,GAAG,IAAIb,KAAK,CAAC,IAAI,CAACY,cAAc,CAAC;EACnD;;AARFjC,OAAA,CAAAgC,2BAAA,GAAAA,2BAAA;AAWA,MAAaG,sBAAsB;EAEjCtB,YACkBV,MAAc,EACdiC,aAAqB,EACrBC,cAAsB;IAFtB,KAAAlC,MAAM,GAANA,MAAM;IACN,KAAAiC,aAAa,GAAbA,aAAa;IACb,KAAAC,cAAc,GAAdA,cAAc;IAJhB,KAAAnC,IAAI,GAAgB,iBAAiB;EAKlD;;AANLF,OAAA,CAAAmC,sBAAA,GAAAA,sBAAA;AASA,MAAaG,yBAAyB;EAEpCzB,YACkBV,MAAc,EACdoC,IAAY;IADZ,KAAApC,MAAM,GAANA,MAAM;IACN,KAAAoC,IAAI,GAAJA,IAAI;IAHN,KAAArC,IAAI,GAAgB,2BAA2B;EAI5D;;AALLF,OAAA,CAAAsC,yBAAA,GAAAA,yBAAA;AAQA,MAAaE,qBAAqB;EAEhC3B,YACkBV,MAAc,EACdsC,SAAiB,EACjBC,SAAiB;IAFjB,KAAAvC,MAAM,GAANA,MAAM;IACN,KAAAsC,SAAS,GAATA,SAAS;IACT,KAAAC,SAAS,GAATA,SAAS;IAJX,KAAAxC,IAAI,GAAgB,gBAAgB;EAKjD;;AANLF,OAAA,CAAAwC,qBAAA,GAAAA,qBAAA;AASA,MAAaG,2BAA2B;EAEtC9B,YACkBV,MAAc,EACdyC,SAAiB,EACjBC,OAAe,EACfC,OAAe;IAHf,KAAA3C,MAAM,GAANA,MAAM;IACN,KAAAyC,SAAS,GAATA,SAAS;IACT,KAAAC,OAAO,GAAPA,OAAO;IACP,KAAAC,OAAO,GAAPA,OAAO;IALT,KAAA5C,IAAI,GAAgB,cAAc;EAM/C;;AAPLF,OAAA,CAAA2C,2BAAA,GAAAA,2BAAA;AAUA,MAAaI,oBAAoB;EAE/BlC,YACkBV,MAAc,EACd6C,MAAc;IADd,KAAA7C,MAAM,GAANA,MAAM;IACN,KAAA6C,MAAM,GAANA,MAAM;IAHR,KAAA9C,IAAI,GAAgB,eAAe;EAIhD;;AALLF,OAAA,CAAA+C,oBAAA,GAAAA,oBAAA;AAQA,MAAaE,sBAAsB;EAEjCpC,YACkBV,MAAc,EACd+C,IAAY;IADZ,KAAA/C,MAAM,GAANA,MAAM;IACN,KAAA+C,IAAI,GAAJA,IAAI;IAHN,KAAAhD,IAAI,GAAgB,iBAAiB;EAIlD;;AALLF,OAAA,CAAAiD,sBAAA,GAAAA,sBAAA;AAQA,MAAaE,cAAc;EAGzBtC,YACSV,MAAc,EACd4B,MAAa;IADb,KAAA5B,MAAM,GAANA,MAAM;IACN,KAAA4B,MAAM,GAANA,MAAM;IAHC,KAAA7B,IAAI,GAAgB,SAAS;IAK3C,IAAI,CAAC4B,UAAU,GAAGC,MAAM,CAAC5B,MAAM;EACjC;;AARFH,OAAA,CAAAmD,cAAA,GAAAA,cAAA;AAWA,MAAaC,aAAa;EACxBvC,YACkBV,MAAc,EACdW,OAA2B;IAD3B,KAAAX,MAAM,GAANA,MAAM;IACN,KAAAW,OAAO,GAAPA,OAAO;IAET,KAAAZ,IAAI,GAAG,QAAQ;EAD5B;;AAJLF,OAAA,CAAAoD,aAAA,GAAAA,aAAA", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}