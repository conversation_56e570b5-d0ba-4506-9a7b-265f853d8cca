{"ast": null, "code": "/**\n * @license\n * Copyright 2018 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { decodeString, encodeString } from '../util';\n// Utilities needed by backend consumers of tf-core.\nexport * from '../ops/axis_util';\nexport * from '../ops/broadcast_util';\nexport * from '../ops/concat_util';\nexport * from '../ops/conv_util';\nexport * from '../ops/fused_util';\nexport * from '../ops/fused_types';\nexport * from '../ops/ragged_to_dense_util';\nexport * from '../ops/reduce_util';\nimport * as slice_util from '../ops/slice_util';\nexport { slice_util };\nexport { upcastType } from '../types';\nexport * from '../ops/rotate_util';\nexport * from '../ops/array_ops_util';\nexport * from '../ops/gather_nd_util';\nexport * from '../ops/scatter_nd_util';\nexport * from '../ops/selu_util';\nexport * from '../ops/fused_util';\nexport * from '../ops/erf_util';\nexport * from '../log';\nexport * from '../backends/complex_util';\nexport * from '../backends/einsum_util';\nexport * from '../ops/split_util';\nexport * from '../ops/sparse/sparse_fill_empty_rows_util';\nexport * from '../ops/sparse/sparse_reshape_util';\nexport * from '../ops/sparse/sparse_segment_reduction_util';\nimport * as segment_util from '../ops/segment_util';\nexport { segment_util };\nexport function fromUint8ToStringArray(vals) {\n  try {\n    // Decode the bytes into string.\n    return vals.map(val => decodeString(val));\n  } catch (err) {\n    throw new Error(\"Failed to decode encoded string bytes into utf-8, error: \".concat(err));\n  }\n}\nexport function fromStringArrayToUint8(strings) {\n  return strings.map(s => encodeString(s));\n}", "map": {"version": 3, "names": ["decodeString", "encodeString", "slice_util", "upcastType", "segment_util", "fromUint8ToStringArray", "vals", "map", "val", "err", "Error", "concat", "fromStringArrayToUint8", "strings", "s"], "sources": ["C:\\tfjs-core\\src\\backends\\backend_util.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2018 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {decodeString, encodeString} from '../util';\n\n// Utilities needed by backend consumers of tf-core.\nexport * from '../ops/axis_util';\nexport * from '../ops/broadcast_util';\nexport * from '../ops/concat_util';\nexport * from '../ops/conv_util';\nexport * from '../ops/fused_util';\nexport * from '../ops/fused_types';\nexport * from '../ops/ragged_to_dense_util';\nexport * from '../ops/reduce_util';\n\nimport * as slice_util from '../ops/slice_util';\nexport {slice_util};\n\nexport {BackendValues, TypedArray, upcastType, PixelData} from '../types';\nexport {MemoryInfo, TimingInfo} from '../engine';\nexport * from '../ops/rotate_util';\nexport * from '../ops/array_ops_util';\nexport * from '../ops/gather_nd_util';\nexport * from '../ops/scatter_nd_util';\nexport * from '../ops/selu_util';\nexport * from '../ops/fused_util';\nexport * from '../ops/erf_util';\nexport * from '../log';\nexport * from '../backends/complex_util';\nexport * from '../backends/einsum_util';\nexport * from '../ops/split_util';\nexport * from '../ops/sparse/sparse_fill_empty_rows_util';\nexport * from '../ops/sparse/sparse_reshape_util';\nexport * from '../ops/sparse/sparse_segment_reduction_util';\n\nimport * as segment_util from '../ops/segment_util';\nexport {segment_util};\n\nexport function fromUint8ToStringArray(vals: Uint8Array[]) {\n  try {\n    // Decode the bytes into string.\n    return vals.map(val => decodeString(val));\n  } catch (err) {\n    throw new Error(\n        `Failed to decode encoded string bytes into utf-8, error: ${err}`);\n  }\n}\n\nexport function fromStringArrayToUint8(strings: string[]) {\n  return strings.map(s => encodeString(s));\n}\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAiBA,SAAQA,YAAY,EAAEC,YAAY,QAAO,SAAS;AAElD;AACA,cAAc,kBAAkB;AAChC,cAAc,uBAAuB;AACrC,cAAc,oBAAoB;AAClC,cAAc,kBAAkB;AAChC,cAAc,mBAAmB;AACjC,cAAc,oBAAoB;AAClC,cAAc,6BAA6B;AAC3C,cAAc,oBAAoB;AAElC,OAAO,KAAKC,UAAU,MAAM,mBAAmB;AAC/C,SAAQA,UAAU;AAElB,SAAmCC,UAAU,QAAkB,UAAU;AAEzE,cAAc,oBAAoB;AAClC,cAAc,uBAAuB;AACrC,cAAc,uBAAuB;AACrC,cAAc,wBAAwB;AACtC,cAAc,kBAAkB;AAChC,cAAc,mBAAmB;AACjC,cAAc,iBAAiB;AAC/B,cAAc,QAAQ;AACtB,cAAc,0BAA0B;AACxC,cAAc,yBAAyB;AACvC,cAAc,mBAAmB;AACjC,cAAc,2CAA2C;AACzD,cAAc,mCAAmC;AACjD,cAAc,6CAA6C;AAE3D,OAAO,KAAKC,YAAY,MAAM,qBAAqB;AACnD,SAAQA,YAAY;AAEpB,OAAM,SAAUC,sBAAsBA,CAACC,IAAkB;EACvD,IAAI;IACF;IACA,OAAOA,IAAI,CAACC,GAAG,CAACC,GAAG,IAAIR,YAAY,CAACQ,GAAG,CAAC,CAAC;GAC1C,CAAC,OAAOC,GAAG,EAAE;IACZ,MAAM,IAAIC,KAAK,6DAAAC,MAAA,CACiDF,GAAG,CAAE,CAAC;;AAE1E;AAEA,OAAM,SAAUG,sBAAsBA,CAACC,OAAiB;EACtD,OAAOA,OAAO,CAACN,GAAG,CAACO,CAAC,IAAIb,YAAY,CAACa,CAAC,CAAC,CAAC;AAC1C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}