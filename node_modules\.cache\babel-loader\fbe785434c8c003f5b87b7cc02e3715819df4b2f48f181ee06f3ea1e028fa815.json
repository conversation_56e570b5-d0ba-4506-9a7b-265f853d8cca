{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.transformArguments = exports.FIRST_KEY_INDEX = void 0;\nconst generic_transformers_1 = require(\"./generic-transformers\");\nexports.FIRST_KEY_INDEX = 1;\nfunction transformArguments(key, fields, ms, mode) {\n  const args = ['HPEXPIRE', key, ms.toString()];\n  if (mode) {\n    args.push(mode);\n  }\n  args.push('FIELDS');\n  return (0, generic_transformers_1.pushVerdictArgument)(args, fields);\n}\nexports.transformArguments = transformArguments;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "transformArguments", "FIRST_KEY_INDEX", "generic_transformers_1", "require", "key", "fields", "ms", "mode", "args", "toString", "push", "pushVerdictArgument"], "sources": ["C:/tmsft/node_modules/@redis/client/dist/lib/commands/HPEXPIRE.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.transformArguments = exports.FIRST_KEY_INDEX = void 0;\nconst generic_transformers_1 = require(\"./generic-transformers\");\nexports.FIRST_KEY_INDEX = 1;\nfunction transformArguments(key, fields, ms, mode) {\n    const args = ['HPEXPIRE', key, ms.toString()];\n    if (mode) {\n        args.push(mode);\n    }\n    args.push('FIELDS');\n    return (0, generic_transformers_1.pushVerdictArgument)(args, fields);\n}\nexports.transformArguments = transformArguments;\n"], "mappings": "AAAA,YAAY;;AACZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEC,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7DD,OAAO,CAACE,kBAAkB,GAAGF,OAAO,CAACG,eAAe,GAAG,KAAK,CAAC;AAC7D,MAAMC,sBAAsB,GAAGC,OAAO,CAAC,wBAAwB,CAAC;AAChEL,OAAO,CAACG,eAAe,GAAG,CAAC;AAC3B,SAASD,kBAAkBA,CAACI,GAAG,EAAEC,MAAM,EAAEC,EAAE,EAAEC,IAAI,EAAE;EAC/C,MAAMC,IAAI,GAAG,CAAC,UAAU,EAAEJ,GAAG,EAAEE,EAAE,CAACG,QAAQ,CAAC,CAAC,CAAC;EAC7C,IAAIF,IAAI,EAAE;IACNC,IAAI,CAACE,IAAI,CAACH,IAAI,CAAC;EACnB;EACAC,IAAI,CAACE,IAAI,CAAC,QAAQ,CAAC;EACnB,OAAO,CAAC,CAAC,EAAER,sBAAsB,CAACS,mBAAmB,EAAEH,IAAI,EAAEH,MAAM,CAAC;AACxE;AACAP,OAAO,CAACE,kBAAkB,GAAGA,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}