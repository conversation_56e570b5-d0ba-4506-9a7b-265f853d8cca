{"ast": null, "code": "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { ENGINE } from '../engine';\nimport { MaxPool3DGrad } from '../kernel_names';\nimport { convertToTensor } from '../tensor_util_env';\nimport * as util from '../util';\nimport { checkPadOnDimRoundingMode } from './conv_util';\nimport { op } from './operation';\nimport { reshape } from './reshape';\n/**\n * Computes the backprop of a 3d max pool.\n *\n * @param dy The dy error, of rank 5 of shape\n *     [batchSize, depth, height, width, channels].\n * assumed.\n * @param input The original input image, of rank 5 or rank 4 of shape\n *     [batchSize, depth, height, width, channels].\n * @param output The original output image, of rank 5 of shape\n *     [batchSize, outDepth, outHeight, outWidth, channels].\n * @param filterSize The filter size:\n *     `[filterDepth, filterHeight, filterWidth]`.\n *     `filterSize` is a single number,\n *     then `filterDepth == filterHeight == filterWidth`.\n * @param strides The strides of the pooling:\n *     `[strideDepth, strideHeight, strideWidth]`. If\n *     `strides` is a single number, then `strideHeight == strideWidth`.\n * @param pad A string from: 'same', 'valid'. The type of padding algorithm\n *     used in the forward prop of the op.\n * @param dimRoundingMode A string from: 'ceil', 'round', 'floor'. If none is\n *     provided, it will default to truncate.\n */\nfunction maxPool3dGrad_(dy, input, output, filterSize, strides, pad, dimRoundingMode) {\n  const $dy = convertToTensor(dy, 'dy', 'maxPool3dGrad');\n  const $input = convertToTensor(input, 'input', 'maxPool3dGrad');\n  const $output = convertToTensor(output, 'output', 'maxPool3dGrad');\n  let dy5D = $dy;\n  let input5D = $input;\n  let output5D = $output;\n  let reshapedTo5D = false;\n  if ($input.rank === 4) {\n    reshapedTo5D = true;\n    dy5D = reshape($dy, [1, $dy.shape[0], $dy.shape[1], $dy.shape[2], $dy.shape[3]]);\n    input5D = reshape($input, [1, $input.shape[0], $input.shape[1], $input.shape[2], $input.shape[3]]);\n    output5D = reshape($output, [1, $output.shape[0], $output.shape[1], $output.shape[2], $output.shape[3]]);\n  }\n  util.assert(dy5D.rank === 5, () => `Error in maxPool3dGrad: dy must be rank 5 but got rank ` + `${dy5D.rank}.`);\n  util.assert(input5D.rank === 5, () => `Error in maxPool3dGrad: input must be rank 5 but got rank ` + `${input5D.rank}.`);\n  util.assert(output5D.rank === 5, () => `Error in maxPool3dGrad: output must be rank 5 but got rank ` + `${output5D.rank}.`);\n  checkPadOnDimRoundingMode('maxPool3dGrad', pad, dimRoundingMode);\n  const inputs = {\n    dy: dy5D,\n    input: input5D,\n    output: output5D\n  };\n  const attrs = {\n    filterSize,\n    strides,\n    pad,\n    dimRoundingMode\n  };\n  // tslint:disable-next-line: no-unnecessary-type-assertion\n  const res = ENGINE.runKernel(MaxPool3DGrad, inputs, attrs);\n  if (reshapedTo5D) {\n    return reshape(res, [res.shape[1], res.shape[2], res.shape[3], res.shape[4]]);\n  }\n  return res;\n}\nexport const maxPool3dGrad = /* @__PURE__ */op({\n  maxPool3dGrad_\n});", "map": {"version": 3, "names": ["ENGINE", "MaxPool3DGrad", "convertToTensor", "util", "checkPadOnDimRoundingMode", "op", "reshape", "maxPool3dGrad_", "dy", "input", "output", "filterSize", "strides", "pad", "dimRoundingMode", "$dy", "$input", "$output", "dy5D", "input5D", "output5D", "reshapedTo5D", "rank", "shape", "assert", "inputs", "attrs", "res", "runKernel", "maxPool3dGrad"], "sources": ["C:\\tfjs-core\\src\\ops\\max_pool_3d_grad.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {ENGINE} from '../engine';\nimport {MaxPool3DGrad, MaxPool3DGradAttrs, MaxPool3DGradInputs} from '../kernel_names';\nimport {NamedAttrMap} from '../kernel_registry';\nimport {Tensor4D, Tensor5D} from '../tensor';\nimport {NamedTensorMap} from '../tensor_types';\nimport {convertToTensor} from '../tensor_util_env';\nimport {TensorLike} from '../types';\nimport * as util from '../util';\n\nimport {checkPadOnDimRoundingMode} from './conv_util';\nimport {op} from './operation';\nimport {reshape} from './reshape';\n\n/**\n * Computes the backprop of a 3d max pool.\n *\n * @param dy The dy error, of rank 5 of shape\n *     [batchSize, depth, height, width, channels].\n * assumed.\n * @param input The original input image, of rank 5 or rank 4 of shape\n *     [batchSize, depth, height, width, channels].\n * @param output The original output image, of rank 5 of shape\n *     [batchSize, outDepth, outHeight, outWidth, channels].\n * @param filterSize The filter size:\n *     `[filterDepth, filterHeight, filterWidth]`.\n *     `filterSize` is a single number,\n *     then `filterDepth == filterHeight == filterWidth`.\n * @param strides The strides of the pooling:\n *     `[strideDepth, strideHeight, strideWidth]`. If\n *     `strides` is a single number, then `strideHeight == strideWidth`.\n * @param pad A string from: 'same', 'valid'. The type of padding algorithm\n *     used in the forward prop of the op.\n * @param dimRoundingMode A string from: 'ceil', 'round', 'floor'. If none is\n *     provided, it will default to truncate.\n */\nfunction maxPool3dGrad_<T extends Tensor4D|Tensor5D>(\n    dy: T|TensorLike, input: T|TensorLike, output: T|TensorLike,\n    filterSize: [number, number, number]|number,\n    strides: [number, number, number]|number, pad: 'valid'|'same'|number,\n    dimRoundingMode?: 'floor'|'round'|'ceil'): T {\n  const $dy = convertToTensor(dy, 'dy', 'maxPool3dGrad');\n  const $input = convertToTensor(input, 'input', 'maxPool3dGrad');\n  const $output = convertToTensor(output, 'output', 'maxPool3dGrad');\n\n  let dy5D = $dy as Tensor5D;\n  let input5D = $input as Tensor5D;\n  let output5D = $output as Tensor5D;\n  let reshapedTo5D = false;\n\n  if ($input.rank === 4) {\n    reshapedTo5D = true;\n    dy5D = reshape(\n        $dy, [1, $dy.shape[0], $dy.shape[1], $dy.shape[2], $dy.shape[3]]);\n    input5D = reshape($input, [\n      1, $input.shape[0], $input.shape[1], $input.shape[2], $input.shape[3]\n    ]);\n    output5D = reshape($output, [\n      1, $output.shape[0], $output.shape[1], $output.shape[2], $output.shape[3]\n    ]);\n  }\n\n  util.assert(\n      dy5D.rank === 5,\n      () => `Error in maxPool3dGrad: dy must be rank 5 but got rank ` +\n          `${dy5D.rank}.`);\n  util.assert(\n      input5D.rank === 5,\n      () => `Error in maxPool3dGrad: input must be rank 5 but got rank ` +\n          `${input5D.rank}.`);\n  util.assert(\n      output5D.rank === 5,\n      () => `Error in maxPool3dGrad: output must be rank 5 but got rank ` +\n          `${output5D.rank}.`);\n  checkPadOnDimRoundingMode('maxPool3dGrad', pad, dimRoundingMode);\n  const inputs:\n      MaxPool3DGradInputs = {dy: dy5D, input: input5D, output: output5D};\n  const attrs: MaxPool3DGradAttrs = {filterSize, strides, pad, dimRoundingMode};\n\n  // tslint:disable-next-line: no-unnecessary-type-assertion\n  const res = ENGINE.runKernel(\n                  MaxPool3DGrad, inputs as unknown as NamedTensorMap,\n                  attrs as unknown as NamedAttrMap) as T;\n\n  if (reshapedTo5D) {\n    return reshape(\n               res, [res.shape[1], res.shape[2], res.shape[3], res.shape[4]]) as\n        T;\n  }\n\n  return res;\n}\n\nexport const maxPool3dGrad = /* @__PURE__ */ op({maxPool3dGrad_});\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAiBA,SAAQA,MAAM,QAAO,WAAW;AAChC,SAAQC,aAAa,QAAgD,iBAAiB;AAItF,SAAQC,eAAe,QAAO,oBAAoB;AAElD,OAAO,KAAKC,IAAI,MAAM,SAAS;AAE/B,SAAQC,yBAAyB,QAAO,aAAa;AACrD,SAAQC,EAAE,QAAO,aAAa;AAC9B,SAAQC,OAAO,QAAO,WAAW;AAEjC;;;;;;;;;;;;;;;;;;;;;;AAsBA,SAASC,cAAcA,CACnBC,EAAgB,EAAEC,KAAmB,EAAEC,MAAoB,EAC3DC,UAA2C,EAC3CC,OAAwC,EAAEC,GAA0B,EACpEC,eAAwC;EAC1C,MAAMC,GAAG,GAAGb,eAAe,CAACM,EAAE,EAAE,IAAI,EAAE,eAAe,CAAC;EACtD,MAAMQ,MAAM,GAAGd,eAAe,CAACO,KAAK,EAAE,OAAO,EAAE,eAAe,CAAC;EAC/D,MAAMQ,OAAO,GAAGf,eAAe,CAACQ,MAAM,EAAE,QAAQ,EAAE,eAAe,CAAC;EAElE,IAAIQ,IAAI,GAAGH,GAAe;EAC1B,IAAII,OAAO,GAAGH,MAAkB;EAChC,IAAII,QAAQ,GAAGH,OAAmB;EAClC,IAAII,YAAY,GAAG,KAAK;EAExB,IAAIL,MAAM,CAACM,IAAI,KAAK,CAAC,EAAE;IACrBD,YAAY,GAAG,IAAI;IACnBH,IAAI,GAAGZ,OAAO,CACVS,GAAG,EAAE,CAAC,CAAC,EAAEA,GAAG,CAACQ,KAAK,CAAC,CAAC,CAAC,EAAER,GAAG,CAACQ,KAAK,CAAC,CAAC,CAAC,EAAER,GAAG,CAACQ,KAAK,CAAC,CAAC,CAAC,EAAER,GAAG,CAACQ,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;IACrEJ,OAAO,GAAGb,OAAO,CAACU,MAAM,EAAE,CACxB,CAAC,EAAEA,MAAM,CAACO,KAAK,CAAC,CAAC,CAAC,EAAEP,MAAM,CAACO,KAAK,CAAC,CAAC,CAAC,EAAEP,MAAM,CAACO,KAAK,CAAC,CAAC,CAAC,EAAEP,MAAM,CAACO,KAAK,CAAC,CAAC,CAAC,CACtE,CAAC;IACFH,QAAQ,GAAGd,OAAO,CAACW,OAAO,EAAE,CAC1B,CAAC,EAAEA,OAAO,CAACM,KAAK,CAAC,CAAC,CAAC,EAAEN,OAAO,CAACM,KAAK,CAAC,CAAC,CAAC,EAAEN,OAAO,CAACM,KAAK,CAAC,CAAC,CAAC,EAAEN,OAAO,CAACM,KAAK,CAAC,CAAC,CAAC,CAC1E,CAAC;;EAGJpB,IAAI,CAACqB,MAAM,CACPN,IAAI,CAACI,IAAI,KAAK,CAAC,EACf,MAAM,yDAAyD,GAC3D,GAAGJ,IAAI,CAACI,IAAI,GAAG,CAAC;EACxBnB,IAAI,CAACqB,MAAM,CACPL,OAAO,CAACG,IAAI,KAAK,CAAC,EAClB,MAAM,4DAA4D,GAC9D,GAAGH,OAAO,CAACG,IAAI,GAAG,CAAC;EAC3BnB,IAAI,CAACqB,MAAM,CACPJ,QAAQ,CAACE,IAAI,KAAK,CAAC,EACnB,MAAM,6DAA6D,GAC/D,GAAGF,QAAQ,CAACE,IAAI,GAAG,CAAC;EAC5BlB,yBAAyB,CAAC,eAAe,EAAES,GAAG,EAAEC,eAAe,CAAC;EAChE,MAAMW,MAAM,GACc;IAACjB,EAAE,EAAEU,IAAI;IAAET,KAAK,EAAEU,OAAO;IAAET,MAAM,EAAEU;EAAQ,CAAC;EACtE,MAAMM,KAAK,GAAuB;IAACf,UAAU;IAAEC,OAAO;IAAEC,GAAG;IAAEC;EAAe,CAAC;EAE7E;EACA,MAAMa,GAAG,GAAG3B,MAAM,CAAC4B,SAAS,CACZ3B,aAAa,EAAEwB,MAAmC,EAClDC,KAAgC,CAAM;EAEtD,IAAIL,YAAY,EAAE;IAChB,OAAOf,OAAO,CACHqB,GAAG,EAAE,CAACA,GAAG,CAACJ,KAAK,CAAC,CAAC,CAAC,EAAEI,GAAG,CAACJ,KAAK,CAAC,CAAC,CAAC,EAAEI,GAAG,CAACJ,KAAK,CAAC,CAAC,CAAC,EAAEI,GAAG,CAACJ,KAAK,CAAC,CAAC,CAAC,CAAC,CACnE;;EAGP,OAAOI,GAAG;AACZ;AAEA,OAAO,MAAME,aAAa,GAAG,eAAgBxB,EAAE,CAAC;EAACE;AAAc,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}