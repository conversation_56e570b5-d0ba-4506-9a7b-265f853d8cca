{"ast": null, "code": "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { convertToTensor } from '../../tensor_util_env';\nimport { assert } from '../../util';\nimport { greaterEqual } from '../greater_equal';\nimport { less } from '../less';\nimport { lessEqual } from '../less_equal';\nimport { logicalAnd } from '../logical_and';\nimport { minimum } from '../minimum';\nimport { neg } from '../neg';\nimport { op } from '../operation';\nimport { range } from '../range';\nimport { reshape } from '../reshape';\nimport { stack } from '../stack';\nimport { sub } from '../sub';\nimport { unstack } from '../unstack';\nimport { where } from '../where';\nimport { zeros } from '../zeros';\n/**\n * Copy a tensor setting everything outside a central band in each innermost\n * matrix to zero.\n *\n * The band part is computed as follows: Assume input has `k` dimensions\n * `[I, J, K, ..., M, N]`, then the output is a tensor with the same shape where\n * `band[i, j, k, ..., m, n] = in_band(m, n) * input[i, j, k, ..., m, n]`.\n * The indicator function\n * `in_band(m, n) = (num_lower < 0 || (m-n) <= num_lower)`\n * `&& (num_upper < 0 || (n-m) <= num_upper)`\n *\n * ```js\n * const x = tf.tensor2d([[ 0,  1,  2, 3],\n *                        [-1,  0,  1, 2],\n *                        [-2, -1,  0, 1],\n *                        [-3, -2, -1, 0]]);\n * let y = tf.linalg.bandPart(x, 1, -1);\n * y.print(); // [[ 0,  1,  2, 3],\n *            //  [-1,  0,  1, 2],\n *            //  [ 0, -1,  0, 1],\n *            //  [ 0, 0 , -1, 0]]\n * let z = tf.linalg.bandPart(x, 2, 1);\n * z.print(); // [[ 0,  1,  0, 0],\n *            //  [-1,  0,  1, 0],\n *            //  [-2, -1,  0, 1],\n *            //  [ 0, -2, -1, 0]]\n * ```\n *\n * @param x Rank `k` tensor\n * @param numLower Number of subdiagonals to keep.\n *   If negative, keep entire lower triangle.\n * @param numUpper Number of subdiagonals to keep.\n *   If negative, keep entire upper triangle.\n * @returns Rank `k` tensor of the same shape as input.\n *   The extracted banded tensor.\n *\n * @doc {heading:'Operations', subheading:'Linear Algebra', namespace:'linalg'}\n */\nfunction bandPart_(a, numLower, numUpper) {\n  const $a = convertToTensor(a, 'a', 'bandPart');\n  assert($a.rank >= 2, () => `bandPart(): Rank must be at least 2, got ${$a.rank}.`);\n  const shape = $a.shape;\n  const [M, N] = $a.shape.slice(-2);\n  let $numLower;\n  let $numUpper;\n  if (typeof numLower === 'number') {\n    assert(numLower % 1 === 0, () => `bandPart(): numLower must be an integer, got ${numLower}.`);\n    assert(numLower <= M, () => `bandPart(): numLower (${numLower})` + ` must not be greater than the number of rows (${M}).`);\n    $numLower = convertToTensor(numLower < 0 ? M : numLower, 'numLower', 'bandPart');\n  } else {\n    assert(numLower.dtype === 'int32', () => `bandPart(): numLower's dtype must be an int32.`);\n    // If numLower is a Scalar, checking `numLower <= M` could hurt performance,\n    // but minimum(numLower, M) could avoid unexpected results.\n    $numLower = where(less(numLower, 0), M, minimum(numLower, M));\n  }\n  if (typeof numUpper === 'number') {\n    assert(numUpper % 1 === 0, () => `bandPart(): numUpper must be an integer, got ${numUpper}.`);\n    assert(numUpper <= N, () => `bandPart(): numUpper (${numUpper})` + ` must not be greater than the number of columns (${N}).`);\n    $numUpper = convertToTensor(numUpper < 0 ? N : numUpper, 'numUpper', 'bandPart');\n  } else {\n    assert(numUpper.dtype === 'int32', () => `bandPart(): numUpper's dtype must be an int32.`);\n    $numUpper = where(less(numUpper, 0), N, minimum(numUpper, N));\n  }\n  const i = reshape(range(0, M, 1, 'int32'), [-1, 1]);\n  const j = range(0, N, 1, 'int32');\n  const ij = sub(i, j);\n  const inBand = logicalAnd(lessEqual(ij, $numLower), greaterEqual(ij, neg($numUpper)));\n  const zero = zeros([M, N], $a.dtype);\n  return reshape(stack(unstack(reshape($a, [-1, M, N])).map(mat => where(inBand, mat, zero))), shape);\n}\nexport const bandPart = /* @__PURE__ */op({\n  bandPart_\n});", "map": {"version": 3, "names": ["convertToTensor", "assert", "greaterEqual", "less", "lessEqual", "logicalAnd", "minimum", "neg", "op", "range", "reshape", "stack", "sub", "unstack", "where", "zeros", "bandPart_", "a", "numL<PERSON>er", "numUpper", "$a", "rank", "shape", "M", "N", "slice", "$numLower", "$numUpper", "dtype", "i", "j", "ij", "inBand", "zero", "map", "mat", "bandPart"], "sources": ["C:\\tfjs-core\\src\\ops\\linalg\\band_part.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {Scalar, Tensor} from '../../tensor';\nimport {convertToTensor} from '../../tensor_util_env';\nimport {TensorLike} from '../../types';\nimport {assert} from '../../util';\n\nimport {greaterEqual} from '../greater_equal';\nimport {less} from '../less';\nimport {lessEqual} from '../less_equal';\nimport {logicalAnd} from '../logical_and';\nimport {minimum} from '../minimum';\nimport {neg} from '../neg';\nimport {op} from '../operation';\nimport {range} from '../range';\nimport {reshape} from '../reshape';\nimport {stack} from '../stack';\nimport {sub} from '../sub';\nimport {unstack} from '../unstack';\nimport {where} from '../where';\nimport {zeros} from '../zeros';\n\n/**\n * Copy a tensor setting everything outside a central band in each innermost\n * matrix to zero.\n *\n * The band part is computed as follows: Assume input has `k` dimensions\n * `[I, J, K, ..., M, N]`, then the output is a tensor with the same shape where\n * `band[i, j, k, ..., m, n] = in_band(m, n) * input[i, j, k, ..., m, n]`.\n * The indicator function\n * `in_band(m, n) = (num_lower < 0 || (m-n) <= num_lower)`\n * `&& (num_upper < 0 || (n-m) <= num_upper)`\n *\n * ```js\n * const x = tf.tensor2d([[ 0,  1,  2, 3],\n *                        [-1,  0,  1, 2],\n *                        [-2, -1,  0, 1],\n *                        [-3, -2, -1, 0]]);\n * let y = tf.linalg.bandPart(x, 1, -1);\n * y.print(); // [[ 0,  1,  2, 3],\n *            //  [-1,  0,  1, 2],\n *            //  [ 0, -1,  0, 1],\n *            //  [ 0, 0 , -1, 0]]\n * let z = tf.linalg.bandPart(x, 2, 1);\n * z.print(); // [[ 0,  1,  0, 0],\n *            //  [-1,  0,  1, 0],\n *            //  [-2, -1,  0, 1],\n *            //  [ 0, -2, -1, 0]]\n * ```\n *\n * @param x Rank `k` tensor\n * @param numLower Number of subdiagonals to keep.\n *   If negative, keep entire lower triangle.\n * @param numUpper Number of subdiagonals to keep.\n *   If negative, keep entire upper triangle.\n * @returns Rank `k` tensor of the same shape as input.\n *   The extracted banded tensor.\n *\n * @doc {heading:'Operations', subheading:'Linear Algebra', namespace:'linalg'}\n */\nfunction bandPart_<T extends Tensor>(\n    a: T|TensorLike, numLower: number|Scalar, numUpper: number|Scalar): T {\n  const $a = convertToTensor(a, 'a', 'bandPart');\n  assert(\n      $a.rank >= 2,\n      () => `bandPart(): Rank must be at least 2, got ${$a.rank}.`);\n\n  const shape = $a.shape;\n  const [M, N] = $a.shape.slice(-2);\n\n  let $numLower: Scalar;\n  let $numUpper: Scalar;\n  if (typeof numLower === 'number') {\n    assert(\n        numLower % 1 === 0,\n        () => `bandPart(): numLower must be an integer, got ${numLower}.`);\n    assert(\n        numLower <= M,\n        () => `bandPart(): numLower (${numLower})` +\n            ` must not be greater than the number of rows (${M}).`);\n    $numLower =\n        convertToTensor(numLower < 0 ? M : numLower, 'numLower', 'bandPart') as\n        Scalar;\n  } else {\n    assert(\n        numLower.dtype === 'int32',\n        () => `bandPart(): numLower's dtype must be an int32.`);\n    // If numLower is a Scalar, checking `numLower <= M` could hurt performance,\n    // but minimum(numLower, M) could avoid unexpected results.\n    $numLower = where(less(numLower, 0), M, minimum(numLower, M)) as Scalar;\n  }\n\n  if (typeof numUpper === 'number') {\n    assert(\n        numUpper % 1 === 0,\n        () => `bandPart(): numUpper must be an integer, got ${numUpper}.`);\n    assert(\n        numUpper <= N,\n        () => `bandPart(): numUpper (${numUpper})` +\n            ` must not be greater than the number of columns (${N}).`);\n    $numUpper =\n        convertToTensor(numUpper < 0 ? N : numUpper, 'numUpper', 'bandPart') as\n        Scalar;\n  } else {\n    assert(\n        numUpper.dtype === 'int32',\n        () => `bandPart(): numUpper's dtype must be an int32.`);\n    $numUpper = where(less(numUpper, 0), N, minimum(numUpper, N)) as Scalar;\n  }\n\n  const i = reshape(range(0, M, 1, 'int32'), [-1, 1]);\n  const j = range(0, N, 1, 'int32');\n  const ij = sub(i, j);\n\n  const inBand =\n      logicalAnd(lessEqual(ij, $numLower), greaterEqual(ij, neg($numUpper)));\n\n  const zero = zeros([M, N], $a.dtype);\n\n  return reshape(\n             stack(unstack(reshape($a, [-1, M, N]))\n                       .map(mat => where(inBand, mat, zero))),\n             shape) as T;\n}\n\nexport const bandPart = /* @__PURE__ */ op({bandPart_});\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAkBA,SAAQA,eAAe,QAAO,uBAAuB;AAErD,SAAQC,MAAM,QAAO,YAAY;AAEjC,SAAQC,YAAY,QAAO,kBAAkB;AAC7C,SAAQC,IAAI,QAAO,SAAS;AAC5B,SAAQC,SAAS,QAAO,eAAe;AACvC,SAAQC,UAAU,QAAO,gBAAgB;AACzC,SAAQC,OAAO,QAAO,YAAY;AAClC,SAAQC,GAAG,QAAO,QAAQ;AAC1B,SAAQC,EAAE,QAAO,cAAc;AAC/B,SAAQC,KAAK,QAAO,UAAU;AAC9B,SAAQC,OAAO,QAAO,YAAY;AAClC,SAAQC,KAAK,QAAO,UAAU;AAC9B,SAAQC,GAAG,QAAO,QAAQ;AAC1B,SAAQC,OAAO,QAAO,YAAY;AAClC,SAAQC,KAAK,QAAO,UAAU;AAC9B,SAAQC,KAAK,QAAO,UAAU;AAE9B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAsCA,SAASC,SAASA,CACdC,CAAe,EAAEC,QAAuB,EAAEC,QAAuB;EACnE,MAAMC,EAAE,GAAGpB,eAAe,CAACiB,CAAC,EAAE,GAAG,EAAE,UAAU,CAAC;EAC9ChB,MAAM,CACFmB,EAAE,CAACC,IAAI,IAAI,CAAC,EACZ,MAAM,4CAA4CD,EAAE,CAACC,IAAI,GAAG,CAAC;EAEjE,MAAMC,KAAK,GAAGF,EAAE,CAACE,KAAK;EACtB,MAAM,CAACC,CAAC,EAAEC,CAAC,CAAC,GAAGJ,EAAE,CAACE,KAAK,CAACG,KAAK,CAAC,CAAC,CAAC,CAAC;EAEjC,IAAIC,SAAiB;EACrB,IAAIC,SAAiB;EACrB,IAAI,OAAOT,QAAQ,KAAK,QAAQ,EAAE;IAChCjB,MAAM,CACFiB,QAAQ,GAAG,CAAC,KAAK,CAAC,EAClB,MAAM,gDAAgDA,QAAQ,GAAG,CAAC;IACtEjB,MAAM,CACFiB,QAAQ,IAAIK,CAAC,EACb,MAAM,yBAAyBL,QAAQ,GAAG,GACtC,iDAAiDK,CAAC,IAAI,CAAC;IAC/DG,SAAS,GACL1B,eAAe,CAACkB,QAAQ,GAAG,CAAC,GAAGK,CAAC,GAAGL,QAAQ,EAAE,UAAU,EAAE,UAAU,CAC7D;GACX,MAAM;IACLjB,MAAM,CACFiB,QAAQ,CAACU,KAAK,KAAK,OAAO,EAC1B,MAAM,gDAAgD,CAAC;IAC3D;IACA;IACAF,SAAS,GAAGZ,KAAK,CAACX,IAAI,CAACe,QAAQ,EAAE,CAAC,CAAC,EAAEK,CAAC,EAAEjB,OAAO,CAACY,QAAQ,EAAEK,CAAC,CAAC,CAAW;;EAGzE,IAAI,OAAOJ,QAAQ,KAAK,QAAQ,EAAE;IAChClB,MAAM,CACFkB,QAAQ,GAAG,CAAC,KAAK,CAAC,EAClB,MAAM,gDAAgDA,QAAQ,GAAG,CAAC;IACtElB,MAAM,CACFkB,QAAQ,IAAIK,CAAC,EACb,MAAM,yBAAyBL,QAAQ,GAAG,GACtC,oDAAoDK,CAAC,IAAI,CAAC;IAClEG,SAAS,GACL3B,eAAe,CAACmB,QAAQ,GAAG,CAAC,GAAGK,CAAC,GAAGL,QAAQ,EAAE,UAAU,EAAE,UAAU,CAC7D;GACX,MAAM;IACLlB,MAAM,CACFkB,QAAQ,CAACS,KAAK,KAAK,OAAO,EAC1B,MAAM,gDAAgD,CAAC;IAC3DD,SAAS,GAAGb,KAAK,CAACX,IAAI,CAACgB,QAAQ,EAAE,CAAC,CAAC,EAAEK,CAAC,EAAElB,OAAO,CAACa,QAAQ,EAAEK,CAAC,CAAC,CAAW;;EAGzE,MAAMK,CAAC,GAAGnB,OAAO,CAACD,KAAK,CAAC,CAAC,EAAEc,CAAC,EAAE,CAAC,EAAE,OAAO,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EACnD,MAAMO,CAAC,GAAGrB,KAAK,CAAC,CAAC,EAAEe,CAAC,EAAE,CAAC,EAAE,OAAO,CAAC;EACjC,MAAMO,EAAE,GAAGnB,GAAG,CAACiB,CAAC,EAAEC,CAAC,CAAC;EAEpB,MAAME,MAAM,GACR3B,UAAU,CAACD,SAAS,CAAC2B,EAAE,EAAEL,SAAS,CAAC,EAAExB,YAAY,CAAC6B,EAAE,EAAExB,GAAG,CAACoB,SAAS,CAAC,CAAC,CAAC;EAE1E,MAAMM,IAAI,GAAGlB,KAAK,CAAC,CAACQ,CAAC,EAAEC,CAAC,CAAC,EAAEJ,EAAE,CAACQ,KAAK,CAAC;EAEpC,OAAOlB,OAAO,CACHC,KAAK,CAACE,OAAO,CAACH,OAAO,CAACU,EAAE,EAAE,CAAC,CAAC,CAAC,EAAEG,CAAC,EAAEC,CAAC,CAAC,CAAC,CAAC,CAC3BU,GAAG,CAACC,GAAG,IAAIrB,KAAK,CAACkB,MAAM,EAAEG,GAAG,EAAEF,IAAI,CAAC,CAAC,CAAC,EAChDX,KAAK,CAAM;AACxB;AAEA,OAAO,MAAMc,QAAQ,GAAG,eAAgB5B,EAAE,CAAC;EAACQ;AAAS,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}