{"ast": null, "code": "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { Tile } from '../kernel_names';\nimport { add } from '../ops/add';\nimport { slice } from '../ops/slice';\nimport { zerosLike } from '../ops/zeros_like';\nexport const tileGradConfig = {\n  kernelName: Tile,\n  inputsToSave: ['x'],\n  gradFunc: (dy, saved, attrs) => {\n    const [x] = saved;\n    const {\n      reps\n    } = attrs;\n    const derX = () => {\n      let xGrad = zerosLike(x);\n      // TODO(cais): Maybe reduce memory footprint by avoiding repeated\n      // slicing.\n      if (x.rank === 1) {\n        for (let i = 0; i < reps[0]; ++i) {\n          xGrad = add(xGrad, slice(dy, [i * x.shape[0]], [x.shape[0]]));\n        }\n      } else if (x.rank === 2) {\n        for (let i = 0; i < reps[0]; ++i) {\n          for (let j = 0; j < reps[1]; ++j) {\n            xGrad = add(xGrad, slice(dy, [i * x.shape[0], j * x.shape[1]], [x.shape[0], x.shape[1]]));\n          }\n        }\n      } else if (x.rank === 3) {\n        for (let i = 0; i < reps[0]; ++i) {\n          for (let j = 0; j < reps[1]; ++j) {\n            for (let k = 0; k < reps[2]; ++k) {\n              xGrad = add(xGrad, slice(dy, [i * x.shape[0], j * x.shape[1], k * x.shape[2]], [x.shape[0], x.shape[1], x.shape[2]]));\n            }\n          }\n        }\n      } else if (x.rank === 4) {\n        for (let i = 0; i < reps[0]; ++i) {\n          for (let j = 0; j < reps[1]; ++j) {\n            for (let k = 0; k < reps[2]; ++k) {\n              for (let l = 0; l < reps[3]; ++l) {\n                xGrad = add(xGrad, slice(dy, [i * x.shape[0], j * x.shape[1], k * x.shape[2], l * x.shape[3]], [x.shape[0], x.shape[1], x.shape[2], x.shape[3]]));\n              }\n            }\n          }\n        }\n      } else {\n        throw new Error(`Gradient for tile operation is not implemented for rank-` + `${x.rank} tensors yet.`);\n      }\n      return xGrad;\n    };\n    return {\n      x: derX\n    };\n  }\n};", "map": {"version": 3, "names": ["Tile", "add", "slice", "zerosLike", "tileGradConfig", "kernelName", "inputsToSave", "grad<PERSON>unc", "dy", "saved", "attrs", "x", "reps", "derX", "xGrad", "rank", "i", "shape", "j", "k", "l", "Error"], "sources": ["C:\\tfjs-core\\src\\gradients\\Tile_grad.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {Tile, TileAttrs} from '../kernel_names';\nimport {GradConfig, NamedAttrMap} from '../kernel_registry';\nimport {add} from '../ops/add';\nimport {slice} from '../ops/slice';\nimport {zerosLike} from '../ops/zeros_like';\nimport {Tensor} from '../tensor';\n\nexport const tileGradConfig: GradConfig = {\n  kernelName: Tile,\n  inputsToSave: ['x'],\n  gradFunc: (dy: Tensor, saved: Tensor[], attrs: NamedAttrMap) => {\n    const [x] = saved;\n    const {reps} = attrs as unknown as TileAttrs;\n\n    const derX = () => {\n      let xGrad = zerosLike(x);\n      // TODO(cais): Maybe reduce memory footprint by avoiding repeated\n      // slicing.\n      if (x.rank === 1) {\n        for (let i = 0; i < reps[0]; ++i) {\n          xGrad = add(xGrad, slice(dy, [i * x.shape[0]], [x.shape[0]]));\n        }\n      } else if (x.rank === 2) {\n        for (let i = 0; i < reps[0]; ++i) {\n          for (let j = 0; j < reps[1]; ++j) {\n            xGrad = add(xGrad, slice(dy, [i * x.shape[0], j * x.shape[1]], [\n                          x.shape[0], x.shape[1]\n                        ]));\n          }\n        }\n      } else if (x.rank === 3) {\n        for (let i = 0; i < reps[0]; ++i) {\n          for (let j = 0; j < reps[1]; ++j) {\n            for (let k = 0; k < reps[2]; ++k) {\n              xGrad =\n                  add(xGrad,\n                      slice(\n                          dy, [i * x.shape[0], j * x.shape[1], k * x.shape[2]],\n                          [x.shape[0], x.shape[1], x.shape[2]]));\n            }\n          }\n        }\n      } else if (x.rank === 4) {\n        for (let i = 0; i < reps[0]; ++i) {\n          for (let j = 0; j < reps[1]; ++j) {\n            for (let k = 0; k < reps[2]; ++k) {\n              for (let l = 0; l < reps[3]; ++l) {\n                xGrad =\n                    add(xGrad,\n                        slice(\n                            dy,\n                            [\n                              i * x.shape[0], j * x.shape[1], k * x.shape[2],\n                              l * x.shape[3]\n                            ],\n                            [x.shape[0], x.shape[1], x.shape[2], x.shape[3]]));\n              }\n            }\n          }\n        }\n      } else {\n        throw new Error(\n            `Gradient for tile operation is not implemented for rank-` +\n            `${x.rank} tensors yet.`);\n      }\n      return xGrad;\n    };\n    return {x: derX};\n  },\n};\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAiBA,SAAQA,IAAI,QAAkB,iBAAiB;AAE/C,SAAQC,GAAG,QAAO,YAAY;AAC9B,SAAQC,KAAK,QAAO,cAAc;AAClC,SAAQC,SAAS,QAAO,mBAAmB;AAG3C,OAAO,MAAMC,cAAc,GAAe;EACxCC,UAAU,EAAEL,IAAI;EAChBM,YAAY,EAAE,CAAC,GAAG,CAAC;EACnBC,QAAQ,EAAEA,CAACC,EAAU,EAAEC,KAAe,EAAEC,KAAmB,KAAI;IAC7D,MAAM,CAACC,CAAC,CAAC,GAAGF,KAAK;IACjB,MAAM;MAACG;IAAI,CAAC,GAAGF,KAA6B;IAE5C,MAAMG,IAAI,GAAGA,CAAA,KAAK;MAChB,IAAIC,KAAK,GAAGX,SAAS,CAACQ,CAAC,CAAC;MACxB;MACA;MACA,IAAIA,CAAC,CAACI,IAAI,KAAK,CAAC,EAAE;QAChB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,IAAI,CAAC,CAAC,CAAC,EAAE,EAAEI,CAAC,EAAE;UAChCF,KAAK,GAAGb,GAAG,CAACa,KAAK,EAAEZ,KAAK,CAACM,EAAE,EAAE,CAACQ,CAAC,GAAGL,CAAC,CAACM,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,CAACN,CAAC,CAACM,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;OAEhE,MAAM,IAAIN,CAAC,CAACI,IAAI,KAAK,CAAC,EAAE;QACvB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,IAAI,CAAC,CAAC,CAAC,EAAE,EAAEI,CAAC,EAAE;UAChC,KAAK,IAAIE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGN,IAAI,CAAC,CAAC,CAAC,EAAE,EAAEM,CAAC,EAAE;YAChCJ,KAAK,GAAGb,GAAG,CAACa,KAAK,EAAEZ,KAAK,CAACM,EAAE,EAAE,CAACQ,CAAC,GAAGL,CAAC,CAACM,KAAK,CAAC,CAAC,CAAC,EAAEC,CAAC,GAAGP,CAAC,CAACM,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,CACjDN,CAAC,CAACM,KAAK,CAAC,CAAC,CAAC,EAAEN,CAAC,CAACM,KAAK,CAAC,CAAC,CAAC,CACvB,CAAC,CAAC;;;OAGpB,MAAM,IAAIN,CAAC,CAACI,IAAI,KAAK,CAAC,EAAE;QACvB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,IAAI,CAAC,CAAC,CAAC,EAAE,EAAEI,CAAC,EAAE;UAChC,KAAK,IAAIE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGN,IAAI,CAAC,CAAC,CAAC,EAAE,EAAEM,CAAC,EAAE;YAChC,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGP,IAAI,CAAC,CAAC,CAAC,EAAE,EAAEO,CAAC,EAAE;cAChCL,KAAK,GACDb,GAAG,CAACa,KAAK,EACLZ,KAAK,CACDM,EAAE,EAAE,CAACQ,CAAC,GAAGL,CAAC,CAACM,KAAK,CAAC,CAAC,CAAC,EAAEC,CAAC,GAAGP,CAAC,CAACM,KAAK,CAAC,CAAC,CAAC,EAAEE,CAAC,GAAGR,CAAC,CAACM,KAAK,CAAC,CAAC,CAAC,CAAC,EACpD,CAACN,CAAC,CAACM,KAAK,CAAC,CAAC,CAAC,EAAEN,CAAC,CAACM,KAAK,CAAC,CAAC,CAAC,EAAEN,CAAC,CAACM,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;;;OAIzD,MAAM,IAAIN,CAAC,CAACI,IAAI,KAAK,CAAC,EAAE;QACvB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,IAAI,CAAC,CAAC,CAAC,EAAE,EAAEI,CAAC,EAAE;UAChC,KAAK,IAAIE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGN,IAAI,CAAC,CAAC,CAAC,EAAE,EAAEM,CAAC,EAAE;YAChC,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGP,IAAI,CAAC,CAAC,CAAC,EAAE,EAAEO,CAAC,EAAE;cAChC,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGR,IAAI,CAAC,CAAC,CAAC,EAAE,EAAEQ,CAAC,EAAE;gBAChCN,KAAK,GACDb,GAAG,CAACa,KAAK,EACLZ,KAAK,CACDM,EAAE,EACF,CACEQ,CAAC,GAAGL,CAAC,CAACM,KAAK,CAAC,CAAC,CAAC,EAAEC,CAAC,GAAGP,CAAC,CAACM,KAAK,CAAC,CAAC,CAAC,EAAEE,CAAC,GAAGR,CAAC,CAACM,KAAK,CAAC,CAAC,CAAC,EAC9CG,CAAC,GAAGT,CAAC,CAACM,KAAK,CAAC,CAAC,CAAC,CACf,EACD,CAACN,CAAC,CAACM,KAAK,CAAC,CAAC,CAAC,EAAEN,CAAC,CAACM,KAAK,CAAC,CAAC,CAAC,EAAEN,CAAC,CAACM,KAAK,CAAC,CAAC,CAAC,EAAEN,CAAC,CAACM,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;;;;OAKvE,MAAM;QACL,MAAM,IAAII,KAAK,CACX,0DAA0D,GAC1D,GAAGV,CAAC,CAACI,IAAI,eAAe,CAAC;;MAE/B,OAAOD,KAAK;IACd,CAAC;IACD,OAAO;MAACH,CAAC,EAAEE;IAAI,CAAC;EAClB;CACD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}