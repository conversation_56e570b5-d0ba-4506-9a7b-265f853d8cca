{"ast": null, "code": "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { localResponseNormalization } from '../../ops/local_response_normalization';\nimport { getGlobalTensorClass } from '../../tensor';\ngetGlobalTensorClass().prototype.localResponseNormalization = function (depthRadius, bias, alpha, beta) {\n  this.throwIfDisposed();\n  return localResponseNormalization(this, depthRadius, bias, alpha, beta);\n};", "map": {"version": 3, "names": ["localResponseNormalization", "getGlobalTensorClass", "prototype", "depthRadius", "bias", "alpha", "beta", "throwIfDisposed"], "sources": ["C:\\tfjs-core\\src\\public\\chained_ops\\local_response_normalization.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport {localResponseNormalization} from '../../ops/local_response_normalization';\nimport {getGlobalTensorClass, Tensor} from '../../tensor';\nimport {Rank} from '../../types';\n\ndeclare module '../../tensor' {\n  interface Tensor<R extends Rank = Rank> {\n    localResponseNormalization<T extends Tensor>(\n        depthRadius?: number, bias?: number, alpha?: number, beta?: number): T;\n  }\n}\n\ngetGlobalTensorClass().prototype.localResponseNormalization =\n    function<T extends Tensor>(\n        depthRadius?: number, bias?: number, alpha?: number, beta?: number): T {\n  this.throwIfDisposed();\n  return localResponseNormalization(this, depthRadius, bias, alpha, beta) as T;\n};\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAgBA,SAAQA,0BAA0B,QAAO,wCAAwC;AACjF,SAAQC,oBAAoB,QAAe,cAAc;AAUzDA,oBAAoB,EAAE,CAACC,SAAS,CAACF,0BAA0B,GACvD,UACIG,WAAoB,EAAEC,IAAa,EAAEC,KAAc,EAAEC,IAAa;EACxE,IAAI,CAACC,eAAe,EAAE;EACtB,OAAOP,0BAA0B,CAAC,IAAI,EAAEG,WAAW,EAAEC,IAAI,EAAEC,KAAK,EAAEC,IAAI,CAAM;AAC9E,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}