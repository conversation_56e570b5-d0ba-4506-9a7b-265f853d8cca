{"ast": null, "code": "import { fileStorageService } from './fileStorageService';\nimport { categoryService } from './categoryService';\nimport { simpleMlService } from './simpleMlService';\n\n// Re-export for backwards compatibility\n\nclass TransactionStorageService {\n  constructor() {\n    this.STORAGE_FILENAME = 'transactions';\n  }\n  // Get all stored transactions for an account\n  getTransactionsByAccount(accountId) {\n    const allTransactions = this.getAllTransactions();\n    return allTransactions.filter(t => t.accountId === accountId).sort((a, b) => new Date(b.postDateTime).getTime() - new Date(a.postDateTime).getTime());\n  }\n\n  // Get all transactions from storage\n  getAllTransactions() {\n    return fileStorageService.readData(this.STORAGE_FILENAME, []);\n  }\n\n  // Public method to get all transactions (for components that need access)\n  getAllTransactionsPublic() {\n    return this.getAllTransactions();\n  }\n\n  // Save transactions to storage (made public for backup/restore operations)\n  saveTransactions(transactions) {\n    const success = fileStorageService.writeData(this.STORAGE_FILENAME, transactions);\n    if (!success) {\n      console.error('Failed to save transactions to file system');\n    }\n  }\n\n  // Get the most recent transaction for an account (by Post date + Time)\n  getLatestTransaction(accountId) {\n    const transactions = this.getTransactionsByAccount(accountId);\n    return transactions.length > 0 ? transactions[0] : null;\n  }\n\n  // Create Post date + Time string for sorting\n  createPostDateTime(postDate, time) {\n    // Convert DD/MM/YYYY to YYYY-MM-DD format\n    const dateParts = postDate.split('/');\n    if (dateParts.length === 3) {\n      const formattedDate = `${dateParts[2]}-${dateParts[1].padStart(2, '0')}-${dateParts[0].padStart(2, '0')}`;\n\n      // Handle time format\n      let timeString = '00:00:00';\n      if (time && time.trim()) {\n        const cleanTime = time.trim();\n        if (cleanTime.includes(':')) {\n          timeString = cleanTime.length === 5 ? `${cleanTime}:00` : cleanTime;\n        } else if (cleanTime.length === 4) {\n          // Convert HHMM to HH:MM:SS\n          timeString = `${cleanTime.substring(0, 2)}:${cleanTime.substring(2)}:00`;\n        }\n      }\n      return `${formattedDate}T${timeString}`;\n    }\n\n    // Fallback\n    return new Date().toISOString();\n  }\n\n  // Validate balance against stored transactions\n  validateBalance(accountId, newTransactions, currentAccountBalance) {\n    const latestStoredTransaction = this.getLatestTransaction(accountId);\n    if (!latestStoredTransaction) {\n      // No previous transactions - first import\n      const closingBalance = newTransactions.length > 0 ? newTransactions[0].balance : 0;\n      const openingBalance = newTransactions.length > 0 ? newTransactions[newTransactions.length - 1].balance : 0;\n      const dailyMovement = closingBalance - openingBalance;\n      return {\n        isValid: Math.abs(currentAccountBalance - closingBalance) < 0.01,\n        expectedBalance: closingBalance,\n        actualBalance: closingBalance,\n        difference: 0,\n        lastTransactionDate: '',\n        currentBalance: currentAccountBalance,\n        dailyMovement\n      };\n    }\n\n    // Find the most recent new transaction by Post date + Time\n    const sortedNewTransactions = [...newTransactions].sort((a, b) => {\n      const dateTimeA = this.createPostDateTime(a.postDate || a.date, a.time || '00:00');\n      const dateTimeB = this.createPostDateTime(b.postDate || b.date, b.time || '00:00');\n      return new Date(dateTimeB).getTime() - new Date(dateTimeA).getTime();\n    });\n    const latestNewTransaction = sortedNewTransactions[0];\n    const latestNewDateTime = this.createPostDateTime(latestNewTransaction.postDate || latestNewTransaction.date, latestNewTransaction.time || '00:00');\n\n    // Check if new transactions are newer than stored ones\n    const isNewerImport = new Date(latestNewDateTime).getTime() > new Date(latestStoredTransaction.postDateTime).getTime();\n    if (isNewerImport) {\n      // New transactions are newer - validate against current balance + daily movement\n      const dailyMovement = latestNewTransaction.balance - latestStoredTransaction.balance;\n      const expectedBalance = latestStoredTransaction.balance + dailyMovement;\n      const actualBalance = latestNewTransaction.balance;\n      return {\n        isValid: Math.abs(expectedBalance - actualBalance) < 0.01,\n        expectedBalance,\n        actualBalance,\n        difference: actualBalance - expectedBalance,\n        lastTransactionDate: latestStoredTransaction.postDateTime,\n        currentBalance: currentAccountBalance,\n        dailyMovement\n      };\n    } else {\n      // Importing older transactions - different validation logic\n      const oldestNewTransaction = sortedNewTransactions[sortedNewTransactions.length - 1];\n      const dailyMovement = latestNewTransaction.balance - oldestNewTransaction.balance;\n      return {\n        isValid: true,\n        // Assume valid for historical imports\n        expectedBalance: latestNewTransaction.balance,\n        actualBalance: latestNewTransaction.balance,\n        difference: 0,\n        lastTransactionDate: latestStoredTransaction.postDateTime,\n        currentBalance: currentAccountBalance,\n        dailyMovement\n      };\n    }\n  }\n\n  // Store new transactions\n  storeTransactions(accountId, transactions, fileId) {\n    const allTransactions = this.getAllTransactions();\n    const newStoredTransactions = transactions.map(transaction => ({\n      ...transaction,\n      accountId,\n      fileId,\n      // Track which file these transactions came from\n      importDate: new Date().toISOString(),\n      postDateTime: this.createPostDateTime(transaction.postDate || transaction.date, transaction.time || '00:00')\n    }));\n\n    // Remove any existing transactions with the same IDs (for updates)\n    const filteredExisting = allTransactions.filter(existing => !newStoredTransactions.some(newTxn => newTxn.id === existing.id));\n    const updatedTransactions = [...filteredExisting, ...newStoredTransactions];\n    this.saveTransactions(updatedTransactions);\n  }\n\n  // Delete transactions by file ID\n  deleteTransactionsByFileId(fileId) {\n    const allTransactions = this.getAllTransactions();\n    const filteredTransactions = allTransactions.filter(t => t.fileId !== fileId);\n    const deletedCount = allTransactions.length - filteredTransactions.length;\n    this.saveTransactions(filteredTransactions);\n    return deletedCount;\n  }\n\n  // Get orphaned transactions (transactions without file IDs - old data)\n  getOrphanedTransactions() {\n    const allTransactions = this.getAllTransactions();\n    return allTransactions.filter(t => !t.fileId);\n  }\n\n  // Clear orphaned transactions (transactions without file IDs)\n  clearOrphanedTransactions() {\n    const allTransactions = this.getAllTransactions();\n    const orphanedTransactions = allTransactions.filter(t => !t.fileId);\n    const validTransactions = allTransactions.filter(t => t.fileId);\n    this.saveTransactions(validTransactions);\n    return orphanedTransactions.length;\n  }\n\n  // Clear all transactions across all accounts (emergency cleanup)\n  clearAllTransactions() {\n    const allTransactions = this.getAllTransactions();\n    const deletedCount = allTransactions.length;\n    this.saveTransactions([]);\n    return deletedCount;\n  }\n\n  // Get account balance as of a specific date\n  getBalanceAsOfDate(accountId, targetDate) {\n    const transactions = this.getTransactionsByAccount(accountId);\n\n    // Find the first transaction on or before the target date\n    const targetDateTime = new Date(targetDate).getTime();\n    const relevantTransaction = transactions.find(t => new Date(t.postDateTime).getTime() <= targetDateTime);\n    return relevantTransaction ? relevantTransaction.balance : 0;\n  }\n\n  // Clear all transactions for an account\n  clearAccountTransactions(accountId) {\n    const allTransactions = this.getAllTransactions();\n    const filteredTransactions = allTransactions.filter(t => t.accountId !== accountId);\n    this.saveTransactions(filteredTransactions);\n  }\n\n  // Get transaction statistics for an account\n  getAccountStatistics(accountId) {\n    const transactions = this.getTransactionsByAccount(accountId);\n    if (transactions.length === 0) {\n      return {\n        totalTransactions: 0,\n        dateRange: {\n          from: '',\n          to: ''\n        },\n        totalDebits: 0,\n        totalCredits: 0,\n        currentBalance: 0\n      };\n    }\n    const sortedByDate = [...transactions].sort((a, b) => new Date(a.postDateTime).getTime() - new Date(b.postDateTime).getTime());\n    return {\n      totalTransactions: transactions.length,\n      dateRange: {\n        from: sortedByDate[0].date,\n        to: sortedByDate[sortedByDate.length - 1].date\n      },\n      totalDebits: transactions.reduce((sum, t) => sum + t.debitAmount, 0),\n      totalCredits: transactions.reduce((sum, t) => sum + t.creditAmount, 0),\n      currentBalance: transactions[0].balance // Most recent balance\n    };\n  }\n\n  // Get data storage location for debugging/info\n  getStorageInfo() {\n    return {\n      location: fileStorageService.getDataDirectory(),\n      filename: `${this.STORAGE_FILENAME}.json`\n    };\n  }\n\n  // ============ CATEGORIZATION METHODS ============\n\n  // Update transaction category\n  updateTransactionCategory(transactionId, categoryId, isManual = true) {\n    const allTransactions = this.getAllTransactions();\n    const transactionIndex = allTransactions.findIndex(t => t.id === transactionId);\n    if (transactionIndex === -1) return false;\n    const transaction = allTransactions[transactionIndex];\n    const now = new Date().toISOString();\n\n    // Update category history\n    if (!transaction.categoryHistory) {\n      transaction.categoryHistory = [];\n    }\n    transaction.categoryHistory.push({\n      previousCategoryId: transaction.categoryId || transaction.manualCategoryId,\n      changedDate: now,\n      changedBy: isManual ? 'manual' : 'ml'\n    });\n\n    // Update appropriate category field\n    if (isManual) {\n      transaction.manualCategoryId = categoryId;\n      transaction.categoryId = categoryId; // Manual takes precedence\n    } else {\n      transaction.categoryId = categoryId;\n    }\n\n    // Save updated transactions\n    this.saveTransactions(allTransactions);\n\n    // Add to training data if manual categorization\n    if (isManual) {\n      const amount = (transaction.creditAmount || 0) - (transaction.debitAmount || 0);\n      simpleMlService.addTrainingData(transaction.description, amount, categoryId);\n    }\n    return true;\n  }\n\n  // Auto-categorize uncategorized transactions\n  async autoCategorizeTransactions(accountId) {\n    let transactions = this.getAllTransactions();\n    if (accountId) {\n      transactions = transactions.filter(t => t.accountId === accountId);\n    }\n\n    // Filter uncategorized transactions\n    const uncategorizedTransactions = transactions.filter(t => !t.categoryId && !t.manualCategoryId);\n    let categorizedCount = 0;\n    for (const transaction of uncategorizedTransactions) {\n      try {\n        const amount = (transaction.creditAmount || 0) - (transaction.debitAmount || 0);\n\n        // Try rule-based categorization first\n        const ruleCategoryId = categoryService.categorizeByRules(transaction.description, Math.abs(amount), transaction.reference);\n        if (ruleCategoryId) {\n          this.updateTransactionCategory(transaction.id, ruleCategoryId, false);\n          categorizedCount++;\n          continue;\n        }\n\n        // Try ML categorization\n        const mlCategorization = await simpleMlService.categorizeTransaction(transaction.description, amount);\n        if (mlCategorization && mlCategorization.confidence > 0.5) {\n          // Update transaction with ML categorization\n          const allTransactions = this.getAllTransactions();\n          const transactionIndex = allTransactions.findIndex(t => t.id === transaction.id);\n          if (transactionIndex !== -1) {\n            allTransactions[transactionIndex].categoryId = mlCategorization.categoryId;\n            allTransactions[transactionIndex].mlCategorization = mlCategorization;\n            this.saveTransactions(allTransactions);\n            categorizedCount++;\n          }\n        }\n      } catch (error) {\n        console.error(`Error categorizing transaction ${transaction.id}:`, error);\n      }\n    }\n    return categorizedCount;\n  }\n\n  // Get transactions by category\n  getTransactionsByCategory(categoryId, accountId) {\n    let transactions = this.getAllTransactions();\n    if (accountId) {\n      transactions = transactions.filter(t => t.accountId === accountId);\n    }\n    return transactions.filter(t => t.categoryId === categoryId || t.manualCategoryId === categoryId).sort((a, b) => new Date(b.postDateTime).getTime() - new Date(a.postDateTime).getTime());\n  }\n\n  // Get categorization statistics\n  getCategorizationStats(accountId) {\n    let transactions = this.getAllTransactions();\n    if (accountId) {\n      transactions = transactions.filter(t => t.accountId === accountId);\n    }\n    const stats = {\n      total: transactions.length,\n      categorized: 0,\n      uncategorized: 0,\n      manuallyCateged: 0,\n      mlCategorized: 0,\n      ruleCategorized: 0,\n      categoryBreakdown: []\n    };\n    const categoryMap = new Map();\n    transactions.forEach(transaction => {\n      const amount = (transaction.creditAmount || 0) - (transaction.debitAmount || 0);\n      const categoryId = transaction.categoryId || transaction.manualCategoryId;\n      if (categoryId) {\n        stats.categorized++;\n\n        // Check categorization type\n        if (transaction.manualCategoryId) {\n          stats.manuallyCateged++;\n        } else if (transaction.mlCategorization) {\n          stats.mlCategorized++;\n        } else {\n          stats.ruleCategorized++;\n        }\n\n        // Update category breakdown\n        const existing = categoryMap.get(categoryId) || {\n          count: 0,\n          totalAmount: 0\n        };\n        existing.count++;\n        existing.totalAmount += Math.abs(amount);\n        categoryMap.set(categoryId, existing);\n      } else {\n        stats.uncategorized++;\n      }\n    });\n\n    // Convert map to array\n    stats.categoryBreakdown = Array.from(categoryMap.entries()).map(([categoryId, data]) => ({\n      categoryId,\n      ...data\n    }));\n    return stats;\n  }\n\n  // Get uncategorized transactions\n  getUncategorizedTransactions(accountId) {\n    let transactions = this.getAllTransactions();\n    if (accountId) {\n      transactions = transactions.filter(t => t.accountId === accountId);\n    }\n    return transactions.filter(t => !t.categoryId && !t.manualCategoryId).sort((a, b) => new Date(b.postDateTime).getTime() - new Date(a.postDateTime).getTime());\n  }\n\n  // Bulk update categories\n  bulkUpdateCategories(transactionIds, categoryId) {\n    const allTransactions = this.getAllTransactions();\n    let updatedCount = 0;\n    transactionIds.forEach(id => {\n      const transactionIndex = allTransactions.findIndex(t => t.id === id);\n      if (transactionIndex !== -1) {\n        const transaction = allTransactions[transactionIndex];\n        const now = new Date().toISOString();\n\n        // Update category history\n        if (!transaction.categoryHistory) {\n          transaction.categoryHistory = [];\n        }\n        transaction.categoryHistory.push({\n          previousCategoryId: transaction.categoryId || transaction.manualCategoryId,\n          changedDate: now,\n          changedBy: 'manual'\n        });\n        transaction.manualCategoryId = categoryId;\n        transaction.categoryId = categoryId;\n\n        // Add to training data\n        const amount = (transaction.creditAmount || 0) - (transaction.debitAmount || 0);\n        simpleMlService.addTrainingData(transaction.description, amount, categoryId);\n        updatedCount++;\n      }\n    });\n    if (updatedCount > 0) {\n      this.saveTransactions(allTransactions);\n    }\n    return updatedCount;\n  }\n}\nexport const transactionStorageService = new TransactionStorageService();", "map": {"version": 3, "names": ["fileStorageService", "categoryService", "simpleMlService", "TransactionStorageService", "constructor", "STORAGE_FILENAME", "getTransactionsByAccount", "accountId", "allTransactions", "getAllTransactions", "filter", "t", "sort", "a", "b", "Date", "postDateTime", "getTime", "readData", "getAllTransactionsPublic", "saveTransactions", "transactions", "success", "writeData", "console", "error", "getLatestTransaction", "length", "createPostDateTime", "postDate", "time", "dateParts", "split", "formattedDate", "padStart", "timeString", "trim", "cleanTime", "includes", "substring", "toISOString", "validateBalance", "newTransactions", "currentAccountBalance", "latestStoredTransaction", "closingBalance", "balance", "openingBalance", "dailyMovement", "<PERSON><PERSON><PERSON><PERSON>", "Math", "abs", "expectedBalance", "actualBalance", "difference", "lastTransactionDate", "currentBalance", "sortedNewTransactions", "dateTimeA", "date", "dateTimeB", "latestNewTransaction", "latestNewDateTime", "isNewerImport", "oldestNewTransaction", "storeTransactions", "fileId", "newStoredTransactions", "map", "transaction", "importDate", "filteredExisting", "existing", "some", "newTxn", "id", "updatedTransactions", "deleteTransactionsByFileId", "filteredTransactions", "deletedCount", "getOrphanedTransactions", "clearOrphanedTransactions", "orphanedTransactions", "validTransactions", "clearAllTransactions", "getBalanceAsOfDate", "targetDate", "targetDateTime", "relevantTransaction", "find", "clearAccountTransactions", "getAccountStatistics", "totalTransactions", "date<PERSON><PERSON><PERSON>", "from", "to", "totalDebits", "totalCredits", "sortedByDate", "reduce", "sum", "debitAmount", "creditAmount", "getStorageInfo", "location", "getDataDirectory", "filename", "updateTransactionCategory", "transactionId", "categoryId", "<PERSON><PERSON><PERSON><PERSON>", "transactionIndex", "findIndex", "now", "categoryHistory", "push", "previousCategoryId", "manualCategoryId", "changedDate", "changedBy", "amount", "addTrainingData", "description", "autoCategorizeTransactions", "uncategorizedTransactions", "categorizedCount", "ruleCategoryId", "categorizeByRules", "reference", "mlCategorization", "categorizeTransaction", "confidence", "getTransactionsByCategory", "getCategorizationStats", "stats", "total", "categorized", "uncategorized", "manuallyCateged", "mlCategorized", "ruleCategorized", "categoryBreakdown", "categoryMap", "Map", "for<PERSON>ach", "get", "count", "totalAmount", "set", "Array", "entries", "data", "getUncategorizedTransactions", "bulkUpdateCategories", "transactionIds", "updatedCount", "transactionStorageService"], "sources": ["C:/tmsft/src/services/transactionStorageService.ts"], "sourcesContent": ["import { Transaction, StoredTransaction } from '../types';\r\nimport { fileStorageService } from './fileStorageService';\r\nimport { categoryService } from './categoryService';\r\nimport { simpleMlService } from './simpleMlService';\r\n\r\n// Re-export for backwards compatibility\r\nexport type { StoredTransaction };\r\n\r\nexport interface BalanceValidationResult {\r\n  isValid: boolean;\r\n  expectedBalance: number;\r\n  actualBalance: number;\r\n  difference: number;\r\n  lastTransactionDate: string;\r\n  currentBalance: number;\r\n  dailyMovement: number;\r\n}\r\n\r\nclass TransactionStorageService {\r\n  private readonly STORAGE_FILENAME = 'transactions';\r\n\r\n  // Get all stored transactions for an account\r\n  getTransactionsByAccount(accountId: string): StoredTransaction[] {\r\n    const allTransactions = this.getAllTransactions();\r\n    return allTransactions\r\n      .filter(t => t.accountId === accountId)\r\n      .sort((a, b) => new Date(b.postDateTime).getTime() - new Date(a.postDateTime).getTime());\r\n  }\r\n\r\n  // Get all transactions from storage\r\n  private getAllTransactions(): StoredTransaction[] {\r\n    return fileStorageService.readData<StoredTransaction[]>(this.STORAGE_FILENAME, []);\r\n  }\r\n\r\n  // Public method to get all transactions (for components that need access)\r\n  getAllTransactionsPublic(): StoredTransaction[] {\r\n    return this.getAllTransactions();\r\n  }\r\n\r\n  // Save transactions to storage (made public for backup/restore operations)\r\n  saveTransactions(transactions: StoredTransaction[]): void {\r\n    const success = fileStorageService.writeData(this.STORAGE_FILENAME, transactions);\r\n    if (!success) {\r\n      console.error('Failed to save transactions to file system');\r\n    }\r\n  }\r\n\r\n  // Get the most recent transaction for an account (by Post date + Time)\r\n  getLatestTransaction(accountId: string): StoredTransaction | null {\r\n    const transactions = this.getTransactionsByAccount(accountId);\r\n    return transactions.length > 0 ? transactions[0] : null;\r\n  }\r\n\r\n  // Create Post date + Time string for sorting\r\n  private createPostDateTime(postDate: string, time: string): string {\r\n    // Convert DD/MM/YYYY to YYYY-MM-DD format\r\n    const dateParts = postDate.split('/');\r\n    if (dateParts.length === 3) {\r\n      const formattedDate = `${dateParts[2]}-${dateParts[1].padStart(2, '0')}-${dateParts[0].padStart(2, '0')}`;\r\n      \r\n      // Handle time format\r\n      let timeString = '00:00:00';\r\n      if (time && time.trim()) {\r\n        const cleanTime = time.trim();\r\n        if (cleanTime.includes(':')) {\r\n          timeString = cleanTime.length === 5 ? `${cleanTime}:00` : cleanTime;\r\n        } else if (cleanTime.length === 4) {\r\n          // Convert HHMM to HH:MM:SS\r\n          timeString = `${cleanTime.substring(0, 2)}:${cleanTime.substring(2)}:00`;\r\n        }\r\n      }\r\n      \r\n      return `${formattedDate}T${timeString}`;\r\n    }\r\n    \r\n    // Fallback\r\n    return new Date().toISOString();\r\n  }\r\n\r\n  // Validate balance against stored transactions\r\n  validateBalance(accountId: string, newTransactions: Transaction[], currentAccountBalance: number): BalanceValidationResult {\r\n    const latestStoredTransaction = this.getLatestTransaction(accountId);\r\n    \r\n    if (!latestStoredTransaction) {\r\n      // No previous transactions - first import\r\n      const closingBalance = newTransactions.length > 0 ? newTransactions[0].balance : 0;\r\n      const openingBalance = newTransactions.length > 0 ? newTransactions[newTransactions.length - 1].balance : 0;\r\n      const dailyMovement = closingBalance - openingBalance;\r\n      \r\n      return {\r\n        isValid: Math.abs(currentAccountBalance - closingBalance) < 0.01,\r\n        expectedBalance: closingBalance,\r\n        actualBalance: closingBalance,\r\n        difference: 0,\r\n        lastTransactionDate: '',\r\n        currentBalance: currentAccountBalance,\r\n        dailyMovement\r\n      };\r\n    }\r\n\r\n    // Find the most recent new transaction by Post date + Time\r\n    const sortedNewTransactions = [...newTransactions].sort((a, b) => {\r\n      const dateTimeA = this.createPostDateTime(a.postDate || a.date, a.time || '00:00');\r\n      const dateTimeB = this.createPostDateTime(b.postDate || b.date, b.time || '00:00');\r\n      return new Date(dateTimeB).getTime() - new Date(dateTimeA).getTime();\r\n    });\r\n\r\n    const latestNewTransaction = sortedNewTransactions[0];\r\n    const latestNewDateTime = this.createPostDateTime(\r\n      latestNewTransaction.postDate || latestNewTransaction.date,\r\n      latestNewTransaction.time || '00:00'\r\n    );\r\n\r\n    // Check if new transactions are newer than stored ones\r\n    const isNewerImport = new Date(latestNewDateTime).getTime() > new Date(latestStoredTransaction.postDateTime).getTime();\r\n    \r\n    if (isNewerImport) {\r\n      // New transactions are newer - validate against current balance + daily movement\r\n      const dailyMovement = latestNewTransaction.balance - latestStoredTransaction.balance;\r\n      const expectedBalance = latestStoredTransaction.balance + dailyMovement;\r\n      const actualBalance = latestNewTransaction.balance;\r\n      \r\n      return {\r\n        isValid: Math.abs(expectedBalance - actualBalance) < 0.01,\r\n        expectedBalance,\r\n        actualBalance,\r\n        difference: actualBalance - expectedBalance,\r\n        lastTransactionDate: latestStoredTransaction.postDateTime,\r\n        currentBalance: currentAccountBalance,\r\n        dailyMovement\r\n      };\r\n    } else {\r\n      // Importing older transactions - different validation logic\r\n      const oldestNewTransaction = sortedNewTransactions[sortedNewTransactions.length - 1];\r\n      const dailyMovement = latestNewTransaction.balance - oldestNewTransaction.balance;\r\n      \r\n      return {\r\n        isValid: true, // Assume valid for historical imports\r\n        expectedBalance: latestNewTransaction.balance,\r\n        actualBalance: latestNewTransaction.balance,\r\n        difference: 0,\r\n        lastTransactionDate: latestStoredTransaction.postDateTime,\r\n        currentBalance: currentAccountBalance,\r\n        dailyMovement\r\n      };\r\n    }\r\n  }\r\n\r\n  // Store new transactions\r\n  storeTransactions(accountId: string, transactions: Transaction[], fileId?: string): void {\r\n    const allTransactions = this.getAllTransactions();\r\n    \r\n    const newStoredTransactions: StoredTransaction[] = transactions.map(transaction => ({\r\n      ...transaction,\r\n      accountId,\r\n      fileId, // Track which file these transactions came from\r\n      importDate: new Date().toISOString(),\r\n      postDateTime: this.createPostDateTime(\r\n        transaction.postDate || transaction.date,\r\n        transaction.time || '00:00'\r\n      )\r\n    }));\r\n\r\n    // Remove any existing transactions with the same IDs (for updates)\r\n    const filteredExisting = allTransactions.filter(existing => \r\n      !newStoredTransactions.some(newTxn => newTxn.id === existing.id)\r\n    );\r\n\r\n    const updatedTransactions = [...filteredExisting, ...newStoredTransactions];\r\n    this.saveTransactions(updatedTransactions);\r\n  }\r\n\r\n  // Delete transactions by file ID\r\n  deleteTransactionsByFileId(fileId: string): number {\r\n    const allTransactions = this.getAllTransactions();\r\n    const filteredTransactions = allTransactions.filter(t => t.fileId !== fileId);\r\n    const deletedCount = allTransactions.length - filteredTransactions.length;\r\n    \r\n    this.saveTransactions(filteredTransactions);\r\n    return deletedCount;\r\n  }\r\n\r\n  // Get orphaned transactions (transactions without file IDs - old data)\r\n  getOrphanedTransactions(): StoredTransaction[] {\r\n    const allTransactions = this.getAllTransactions();\r\n    return allTransactions.filter(t => !t.fileId);\r\n  }\r\n\r\n  // Clear orphaned transactions (transactions without file IDs)\r\n  clearOrphanedTransactions(): number {\r\n    const allTransactions = this.getAllTransactions();\r\n    const orphanedTransactions = allTransactions.filter(t => !t.fileId);\r\n    const validTransactions = allTransactions.filter(t => t.fileId);\r\n    \r\n    this.saveTransactions(validTransactions);\r\n    return orphanedTransactions.length;\r\n  }\r\n\r\n  // Clear all transactions across all accounts (emergency cleanup)\r\n  clearAllTransactions(): number {\r\n    const allTransactions = this.getAllTransactions();\r\n    const deletedCount = allTransactions.length;\r\n    this.saveTransactions([]);\r\n    return deletedCount;\r\n  }\r\n\r\n  // Get account balance as of a specific date\r\n  getBalanceAsOfDate(accountId: string, targetDate: string): number {\r\n    const transactions = this.getTransactionsByAccount(accountId);\r\n    \r\n    // Find the first transaction on or before the target date\r\n    const targetDateTime = new Date(targetDate).getTime();\r\n    const relevantTransaction = transactions.find(t => \r\n      new Date(t.postDateTime).getTime() <= targetDateTime\r\n    );\r\n    \r\n    return relevantTransaction ? relevantTransaction.balance : 0;\r\n  }\r\n\r\n  // Clear all transactions for an account\r\n  clearAccountTransactions(accountId: string): void {\r\n    const allTransactions = this.getAllTransactions();\r\n    const filteredTransactions = allTransactions.filter(t => t.accountId !== accountId);\r\n    this.saveTransactions(filteredTransactions);\r\n  }\r\n\r\n  // Get transaction statistics for an account\r\n  getAccountStatistics(accountId: string): {\r\n    totalTransactions: number;\r\n    dateRange: { from: string; to: string };\r\n    totalDebits: number;\r\n    totalCredits: number;\r\n    currentBalance: number;\r\n  } {\r\n    const transactions = this.getTransactionsByAccount(accountId);\r\n    \r\n    if (transactions.length === 0) {\r\n      return {\r\n        totalTransactions: 0,\r\n        dateRange: { from: '', to: '' },\r\n        totalDebits: 0,\r\n        totalCredits: 0,\r\n        currentBalance: 0\r\n      };\r\n    }\r\n\r\n    const sortedByDate = [...transactions].sort((a, b) => \r\n      new Date(a.postDateTime).getTime() - new Date(b.postDateTime).getTime()\r\n    );\r\n\r\n    return {\r\n      totalTransactions: transactions.length,\r\n      dateRange: {\r\n        from: sortedByDate[0].date,\r\n        to: sortedByDate[sortedByDate.length - 1].date\r\n      },\r\n      totalDebits: transactions.reduce((sum, t) => sum + t.debitAmount, 0),\r\n      totalCredits: transactions.reduce((sum, t) => sum + t.creditAmount, 0),\r\n      currentBalance: transactions[0].balance // Most recent balance\r\n    };\r\n  }\r\n\r\n  // Get data storage location for debugging/info\r\n  getStorageInfo(): { location: string; filename: string } {\r\n    return {\r\n      location: fileStorageService.getDataDirectory(),\r\n      filename: `${this.STORAGE_FILENAME}.json`\r\n    };\r\n  }\r\n\r\n  // ============ CATEGORIZATION METHODS ============\r\n\r\n  // Update transaction category\r\n  updateTransactionCategory(\r\n    transactionId: string, \r\n    categoryId: string, \r\n    isManual: boolean = true\r\n  ): boolean {\r\n    const allTransactions = this.getAllTransactions();\r\n    const transactionIndex = allTransactions.findIndex(t => t.id === transactionId);\r\n    \r\n    if (transactionIndex === -1) return false;\r\n\r\n    const transaction = allTransactions[transactionIndex];\r\n    const now = new Date().toISOString();\r\n\r\n    // Update category history\r\n    if (!transaction.categoryHistory) {\r\n      transaction.categoryHistory = [];\r\n    }\r\n\r\n    transaction.categoryHistory.push({\r\n      previousCategoryId: transaction.categoryId || transaction.manualCategoryId,\r\n      changedDate: now,\r\n      changedBy: isManual ? 'manual' : 'ml'\r\n    });\r\n\r\n    // Update appropriate category field\r\n    if (isManual) {\r\n      transaction.manualCategoryId = categoryId;\r\n      transaction.categoryId = categoryId; // Manual takes precedence\r\n    } else {\r\n      transaction.categoryId = categoryId;\r\n    }\r\n\r\n    // Save updated transactions\r\n    this.saveTransactions(allTransactions);\r\n\r\n    // Add to training data if manual categorization\r\n    if (isManual) {\r\n      const amount = (transaction.creditAmount || 0) - (transaction.debitAmount || 0);\r\n      simpleMlService.addTrainingData(\r\n        transaction.description,\r\n        amount,\r\n        categoryId\r\n      );\r\n    }\r\n\r\n    return true;\r\n  }\r\n\r\n  // Auto-categorize uncategorized transactions\r\n  async autoCategorizeTransactions(accountId?: string): Promise<number> {\r\n    let transactions = this.getAllTransactions();\r\n    \r\n    if (accountId) {\r\n      transactions = transactions.filter(t => t.accountId === accountId);\r\n    }\r\n\r\n    // Filter uncategorized transactions\r\n    const uncategorizedTransactions = transactions.filter(t => \r\n      !t.categoryId && !t.manualCategoryId\r\n    );\r\n\r\n    let categorizedCount = 0;\r\n\r\n    for (const transaction of uncategorizedTransactions) {\r\n      try {\r\n        const amount = (transaction.creditAmount || 0) - (transaction.debitAmount || 0);\r\n        \r\n        // Try rule-based categorization first\r\n        const ruleCategoryId = categoryService.categorizeByRules(\r\n          transaction.description,\r\n          Math.abs(amount),\r\n          transaction.reference\r\n        );\r\n\r\n        if (ruleCategoryId) {\r\n          this.updateTransactionCategory(transaction.id, ruleCategoryId, false);\r\n          categorizedCount++;\r\n          continue;\r\n        }\r\n\r\n        // Try ML categorization\r\n        const mlCategorization = await simpleMlService.categorizeTransaction(\r\n          transaction.description,\r\n          amount\r\n        );\r\n\r\n        if (mlCategorization && mlCategorization.confidence > 0.5) {\r\n          // Update transaction with ML categorization\r\n          const allTransactions = this.getAllTransactions();\r\n          const transactionIndex = allTransactions.findIndex(t => t.id === transaction.id);\r\n          \r\n          if (transactionIndex !== -1) {\r\n            allTransactions[transactionIndex].categoryId = mlCategorization.categoryId;\r\n            allTransactions[transactionIndex].mlCategorization = mlCategorization;\r\n            this.saveTransactions(allTransactions);\r\n            categorizedCount++;\r\n          }\r\n        }\r\n      } catch (error) {\r\n        console.error(`Error categorizing transaction ${transaction.id}:`, error);\r\n      }\r\n    }\r\n\r\n    return categorizedCount;\r\n  }\r\n\r\n  // Get transactions by category\r\n  getTransactionsByCategory(categoryId: string, accountId?: string): StoredTransaction[] {\r\n    let transactions = this.getAllTransactions();\r\n    \r\n    if (accountId) {\r\n      transactions = transactions.filter(t => t.accountId === accountId);\r\n    }\r\n\r\n    return transactions\r\n      .filter(t => t.categoryId === categoryId || t.manualCategoryId === categoryId)\r\n      .sort((a, b) => new Date(b.postDateTime).getTime() - new Date(a.postDateTime).getTime());\r\n  }\r\n\r\n  // Get categorization statistics\r\n  getCategorizationStats(accountId?: string): {\r\n    total: number;\r\n    categorized: number;\r\n    uncategorized: number;\r\n    manuallyCateged: number;\r\n    mlCategorized: number;\r\n    ruleCategorized: number;\r\n    categoryBreakdown: { categoryId: string; count: number; totalAmount: number }[];\r\n  } {\r\n    let transactions = this.getAllTransactions();\r\n    \r\n    if (accountId) {\r\n      transactions = transactions.filter(t => t.accountId === accountId);\r\n    }\r\n\r\n    const stats = {\r\n      total: transactions.length,\r\n      categorized: 0,\r\n      uncategorized: 0,\r\n      manuallyCateged: 0,\r\n      mlCategorized: 0,\r\n      ruleCategorized: 0,\r\n      categoryBreakdown: [] as { categoryId: string; count: number; totalAmount: number }[]\r\n    };\r\n\r\n    const categoryMap = new Map<string, { count: number; totalAmount: number }>();\r\n\r\n    transactions.forEach(transaction => {\r\n      const amount = (transaction.creditAmount || 0) - (transaction.debitAmount || 0);\r\n      const categoryId = transaction.categoryId || transaction.manualCategoryId;\r\n\r\n      if (categoryId) {\r\n        stats.categorized++;\r\n        \r\n        // Check categorization type\r\n        if (transaction.manualCategoryId) {\r\n          stats.manuallyCateged++;\r\n        } else if (transaction.mlCategorization) {\r\n          stats.mlCategorized++;\r\n        } else {\r\n          stats.ruleCategorized++;\r\n        }\r\n\r\n        // Update category breakdown\r\n        const existing = categoryMap.get(categoryId) || { count: 0, totalAmount: 0 };\r\n        existing.count++;\r\n        existing.totalAmount += Math.abs(amount);\r\n        categoryMap.set(categoryId, existing);\r\n      } else {\r\n        stats.uncategorized++;\r\n      }\r\n    });\r\n\r\n    // Convert map to array\r\n    stats.categoryBreakdown = Array.from(categoryMap.entries()).map(([categoryId, data]) => ({\r\n      categoryId,\r\n      ...data\r\n    }));\r\n\r\n    return stats;\r\n  }\r\n\r\n  // Get uncategorized transactions\r\n  getUncategorizedTransactions(accountId?: string): StoredTransaction[] {\r\n    let transactions = this.getAllTransactions();\r\n    \r\n    if (accountId) {\r\n      transactions = transactions.filter(t => t.accountId === accountId);\r\n    }\r\n\r\n    return transactions\r\n      .filter(t => !t.categoryId && !t.manualCategoryId)\r\n      .sort((a, b) => new Date(b.postDateTime).getTime() - new Date(a.postDateTime).getTime());\r\n  }\r\n\r\n  // Bulk update categories\r\n  bulkUpdateCategories(transactionIds: string[], categoryId: string): number {\r\n    const allTransactions = this.getAllTransactions();\r\n    let updatedCount = 0;\r\n\r\n    transactionIds.forEach(id => {\r\n      const transactionIndex = allTransactions.findIndex(t => t.id === id);\r\n      if (transactionIndex !== -1) {\r\n        const transaction = allTransactions[transactionIndex];\r\n        const now = new Date().toISOString();\r\n\r\n        // Update category history\r\n        if (!transaction.categoryHistory) {\r\n          transaction.categoryHistory = [];\r\n        }\r\n\r\n        transaction.categoryHistory.push({\r\n          previousCategoryId: transaction.categoryId || transaction.manualCategoryId,\r\n          changedDate: now,\r\n          changedBy: 'manual'\r\n        });\r\n\r\n        transaction.manualCategoryId = categoryId;\r\n        transaction.categoryId = categoryId;\r\n\r\n        // Add to training data\r\n        const amount = (transaction.creditAmount || 0) - (transaction.debitAmount || 0);\r\n        simpleMlService.addTrainingData(\r\n          transaction.description,\r\n          amount,\r\n          categoryId\r\n        );\r\n\r\n        updatedCount++;\r\n      }\r\n    });\r\n\r\n    if (updatedCount > 0) {\r\n      this.saveTransactions(allTransactions);\r\n    }\r\n\r\n    return updatedCount;\r\n  }\r\n}\r\n\r\nexport const transactionStorageService = new TransactionStorageService(); "], "mappings": "AACA,SAASA,kBAAkB,QAAQ,sBAAsB;AACzD,SAASC,eAAe,QAAQ,mBAAmB;AACnD,SAASC,eAAe,QAAQ,mBAAmB;;AAEnD;;AAaA,MAAMC,yBAAyB,CAAC;EAAAC,YAAA;IAAA,KACbC,gBAAgB,GAAG,cAAc;EAAA;EAElD;EACAC,wBAAwBA,CAACC,SAAiB,EAAuB;IAC/D,MAAMC,eAAe,GAAG,IAAI,CAACC,kBAAkB,CAAC,CAAC;IACjD,OAAOD,eAAe,CACnBE,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACJ,SAAS,KAAKA,SAAS,CAAC,CACtCK,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK,IAAIC,IAAI,CAACD,CAAC,CAACE,YAAY,CAAC,CAACC,OAAO,CAAC,CAAC,GAAG,IAAIF,IAAI,CAACF,CAAC,CAACG,YAAY,CAAC,CAACC,OAAO,CAAC,CAAC,CAAC;EAC5F;;EAEA;EACQR,kBAAkBA,CAAA,EAAwB;IAChD,OAAOT,kBAAkB,CAACkB,QAAQ,CAAsB,IAAI,CAACb,gBAAgB,EAAE,EAAE,CAAC;EACpF;;EAEA;EACAc,wBAAwBA,CAAA,EAAwB;IAC9C,OAAO,IAAI,CAACV,kBAAkB,CAAC,CAAC;EAClC;;EAEA;EACAW,gBAAgBA,CAACC,YAAiC,EAAQ;IACxD,MAAMC,OAAO,GAAGtB,kBAAkB,CAACuB,SAAS,CAAC,IAAI,CAAClB,gBAAgB,EAAEgB,YAAY,CAAC;IACjF,IAAI,CAACC,OAAO,EAAE;MACZE,OAAO,CAACC,KAAK,CAAC,4CAA4C,CAAC;IAC7D;EACF;;EAEA;EACAC,oBAAoBA,CAACnB,SAAiB,EAA4B;IAChE,MAAMc,YAAY,GAAG,IAAI,CAACf,wBAAwB,CAACC,SAAS,CAAC;IAC7D,OAAOc,YAAY,CAACM,MAAM,GAAG,CAAC,GAAGN,YAAY,CAAC,CAAC,CAAC,GAAG,IAAI;EACzD;;EAEA;EACQO,kBAAkBA,CAACC,QAAgB,EAAEC,IAAY,EAAU;IACjE;IACA,MAAMC,SAAS,GAAGF,QAAQ,CAACG,KAAK,CAAC,GAAG,CAAC;IACrC,IAAID,SAAS,CAACJ,MAAM,KAAK,CAAC,EAAE;MAC1B,MAAMM,aAAa,GAAG,GAAGF,SAAS,CAAC,CAAC,CAAC,IAAIA,SAAS,CAAC,CAAC,CAAC,CAACG,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,IAAIH,SAAS,CAAC,CAAC,CAAC,CAACG,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;;MAEzG;MACA,IAAIC,UAAU,GAAG,UAAU;MAC3B,IAAIL,IAAI,IAAIA,IAAI,CAACM,IAAI,CAAC,CAAC,EAAE;QACvB,MAAMC,SAAS,GAAGP,IAAI,CAACM,IAAI,CAAC,CAAC;QAC7B,IAAIC,SAAS,CAACC,QAAQ,CAAC,GAAG,CAAC,EAAE;UAC3BH,UAAU,GAAGE,SAAS,CAACV,MAAM,KAAK,CAAC,GAAG,GAAGU,SAAS,KAAK,GAAGA,SAAS;QACrE,CAAC,MAAM,IAAIA,SAAS,CAACV,MAAM,KAAK,CAAC,EAAE;UACjC;UACAQ,UAAU,GAAG,GAAGE,SAAS,CAACE,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,IAAIF,SAAS,CAACE,SAAS,CAAC,CAAC,CAAC,KAAK;QAC1E;MACF;MAEA,OAAO,GAAGN,aAAa,IAAIE,UAAU,EAAE;IACzC;;IAEA;IACA,OAAO,IAAIpB,IAAI,CAAC,CAAC,CAACyB,WAAW,CAAC,CAAC;EACjC;;EAEA;EACAC,eAAeA,CAAClC,SAAiB,EAAEmC,eAA8B,EAAEC,qBAA6B,EAA2B;IACzH,MAAMC,uBAAuB,GAAG,IAAI,CAAClB,oBAAoB,CAACnB,SAAS,CAAC;IAEpE,IAAI,CAACqC,uBAAuB,EAAE;MAC5B;MACA,MAAMC,cAAc,GAAGH,eAAe,CAACf,MAAM,GAAG,CAAC,GAAGe,eAAe,CAAC,CAAC,CAAC,CAACI,OAAO,GAAG,CAAC;MAClF,MAAMC,cAAc,GAAGL,eAAe,CAACf,MAAM,GAAG,CAAC,GAAGe,eAAe,CAACA,eAAe,CAACf,MAAM,GAAG,CAAC,CAAC,CAACmB,OAAO,GAAG,CAAC;MAC3G,MAAME,aAAa,GAAGH,cAAc,GAAGE,cAAc;MAErD,OAAO;QACLE,OAAO,EAAEC,IAAI,CAACC,GAAG,CAACR,qBAAqB,GAAGE,cAAc,CAAC,GAAG,IAAI;QAChEO,eAAe,EAAEP,cAAc;QAC/BQ,aAAa,EAAER,cAAc;QAC7BS,UAAU,EAAE,CAAC;QACbC,mBAAmB,EAAE,EAAE;QACvBC,cAAc,EAAEb,qBAAqB;QACrCK;MACF,CAAC;IACH;;IAEA;IACA,MAAMS,qBAAqB,GAAG,CAAC,GAAGf,eAAe,CAAC,CAAC9B,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;MAChE,MAAM4C,SAAS,GAAG,IAAI,CAAC9B,kBAAkB,CAACf,CAAC,CAACgB,QAAQ,IAAIhB,CAAC,CAAC8C,IAAI,EAAE9C,CAAC,CAACiB,IAAI,IAAI,OAAO,CAAC;MAClF,MAAM8B,SAAS,GAAG,IAAI,CAAChC,kBAAkB,CAACd,CAAC,CAACe,QAAQ,IAAIf,CAAC,CAAC6C,IAAI,EAAE7C,CAAC,CAACgB,IAAI,IAAI,OAAO,CAAC;MAClF,OAAO,IAAIf,IAAI,CAAC6C,SAAS,CAAC,CAAC3C,OAAO,CAAC,CAAC,GAAG,IAAIF,IAAI,CAAC2C,SAAS,CAAC,CAACzC,OAAO,CAAC,CAAC;IACtE,CAAC,CAAC;IAEF,MAAM4C,oBAAoB,GAAGJ,qBAAqB,CAAC,CAAC,CAAC;IACrD,MAAMK,iBAAiB,GAAG,IAAI,CAAClC,kBAAkB,CAC/CiC,oBAAoB,CAAChC,QAAQ,IAAIgC,oBAAoB,CAACF,IAAI,EAC1DE,oBAAoB,CAAC/B,IAAI,IAAI,OAC/B,CAAC;;IAED;IACA,MAAMiC,aAAa,GAAG,IAAIhD,IAAI,CAAC+C,iBAAiB,CAAC,CAAC7C,OAAO,CAAC,CAAC,GAAG,IAAIF,IAAI,CAAC6B,uBAAuB,CAAC5B,YAAY,CAAC,CAACC,OAAO,CAAC,CAAC;IAEtH,IAAI8C,aAAa,EAAE;MACjB;MACA,MAAMf,aAAa,GAAGa,oBAAoB,CAACf,OAAO,GAAGF,uBAAuB,CAACE,OAAO;MACpF,MAAMM,eAAe,GAAGR,uBAAuB,CAACE,OAAO,GAAGE,aAAa;MACvE,MAAMK,aAAa,GAAGQ,oBAAoB,CAACf,OAAO;MAElD,OAAO;QACLG,OAAO,EAAEC,IAAI,CAACC,GAAG,CAACC,eAAe,GAAGC,aAAa,CAAC,GAAG,IAAI;QACzDD,eAAe;QACfC,aAAa;QACbC,UAAU,EAAED,aAAa,GAAGD,eAAe;QAC3CG,mBAAmB,EAAEX,uBAAuB,CAAC5B,YAAY;QACzDwC,cAAc,EAAEb,qBAAqB;QACrCK;MACF,CAAC;IACH,CAAC,MAAM;MACL;MACA,MAAMgB,oBAAoB,GAAGP,qBAAqB,CAACA,qBAAqB,CAAC9B,MAAM,GAAG,CAAC,CAAC;MACpF,MAAMqB,aAAa,GAAGa,oBAAoB,CAACf,OAAO,GAAGkB,oBAAoB,CAAClB,OAAO;MAEjF,OAAO;QACLG,OAAO,EAAE,IAAI;QAAE;QACfG,eAAe,EAAES,oBAAoB,CAACf,OAAO;QAC7CO,aAAa,EAAEQ,oBAAoB,CAACf,OAAO;QAC3CQ,UAAU,EAAE,CAAC;QACbC,mBAAmB,EAAEX,uBAAuB,CAAC5B,YAAY;QACzDwC,cAAc,EAAEb,qBAAqB;QACrCK;MACF,CAAC;IACH;EACF;;EAEA;EACAiB,iBAAiBA,CAAC1D,SAAiB,EAAEc,YAA2B,EAAE6C,MAAe,EAAQ;IACvF,MAAM1D,eAAe,GAAG,IAAI,CAACC,kBAAkB,CAAC,CAAC;IAEjD,MAAM0D,qBAA0C,GAAG9C,YAAY,CAAC+C,GAAG,CAACC,WAAW,KAAK;MAClF,GAAGA,WAAW;MACd9D,SAAS;MACT2D,MAAM;MAAE;MACRI,UAAU,EAAE,IAAIvD,IAAI,CAAC,CAAC,CAACyB,WAAW,CAAC,CAAC;MACpCxB,YAAY,EAAE,IAAI,CAACY,kBAAkB,CACnCyC,WAAW,CAACxC,QAAQ,IAAIwC,WAAW,CAACV,IAAI,EACxCU,WAAW,CAACvC,IAAI,IAAI,OACtB;IACF,CAAC,CAAC,CAAC;;IAEH;IACA,MAAMyC,gBAAgB,GAAG/D,eAAe,CAACE,MAAM,CAAC8D,QAAQ,IACtD,CAACL,qBAAqB,CAACM,IAAI,CAACC,MAAM,IAAIA,MAAM,CAACC,EAAE,KAAKH,QAAQ,CAACG,EAAE,CACjE,CAAC;IAED,MAAMC,mBAAmB,GAAG,CAAC,GAAGL,gBAAgB,EAAE,GAAGJ,qBAAqB,CAAC;IAC3E,IAAI,CAAC/C,gBAAgB,CAACwD,mBAAmB,CAAC;EAC5C;;EAEA;EACAC,0BAA0BA,CAACX,MAAc,EAAU;IACjD,MAAM1D,eAAe,GAAG,IAAI,CAACC,kBAAkB,CAAC,CAAC;IACjD,MAAMqE,oBAAoB,GAAGtE,eAAe,CAACE,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACuD,MAAM,KAAKA,MAAM,CAAC;IAC7E,MAAMa,YAAY,GAAGvE,eAAe,CAACmB,MAAM,GAAGmD,oBAAoB,CAACnD,MAAM;IAEzE,IAAI,CAACP,gBAAgB,CAAC0D,oBAAoB,CAAC;IAC3C,OAAOC,YAAY;EACrB;;EAEA;EACAC,uBAAuBA,CAAA,EAAwB;IAC7C,MAAMxE,eAAe,GAAG,IAAI,CAACC,kBAAkB,CAAC,CAAC;IACjD,OAAOD,eAAe,CAACE,MAAM,CAACC,CAAC,IAAI,CAACA,CAAC,CAACuD,MAAM,CAAC;EAC/C;;EAEA;EACAe,yBAAyBA,CAAA,EAAW;IAClC,MAAMzE,eAAe,GAAG,IAAI,CAACC,kBAAkB,CAAC,CAAC;IACjD,MAAMyE,oBAAoB,GAAG1E,eAAe,CAACE,MAAM,CAACC,CAAC,IAAI,CAACA,CAAC,CAACuD,MAAM,CAAC;IACnE,MAAMiB,iBAAiB,GAAG3E,eAAe,CAACE,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACuD,MAAM,CAAC;IAE/D,IAAI,CAAC9C,gBAAgB,CAAC+D,iBAAiB,CAAC;IACxC,OAAOD,oBAAoB,CAACvD,MAAM;EACpC;;EAEA;EACAyD,oBAAoBA,CAAA,EAAW;IAC7B,MAAM5E,eAAe,GAAG,IAAI,CAACC,kBAAkB,CAAC,CAAC;IACjD,MAAMsE,YAAY,GAAGvE,eAAe,CAACmB,MAAM;IAC3C,IAAI,CAACP,gBAAgB,CAAC,EAAE,CAAC;IACzB,OAAO2D,YAAY;EACrB;;EAEA;EACAM,kBAAkBA,CAAC9E,SAAiB,EAAE+E,UAAkB,EAAU;IAChE,MAAMjE,YAAY,GAAG,IAAI,CAACf,wBAAwB,CAACC,SAAS,CAAC;;IAE7D;IACA,MAAMgF,cAAc,GAAG,IAAIxE,IAAI,CAACuE,UAAU,CAAC,CAACrE,OAAO,CAAC,CAAC;IACrD,MAAMuE,mBAAmB,GAAGnE,YAAY,CAACoE,IAAI,CAAC9E,CAAC,IAC7C,IAAII,IAAI,CAACJ,CAAC,CAACK,YAAY,CAAC,CAACC,OAAO,CAAC,CAAC,IAAIsE,cACxC,CAAC;IAED,OAAOC,mBAAmB,GAAGA,mBAAmB,CAAC1C,OAAO,GAAG,CAAC;EAC9D;;EAEA;EACA4C,wBAAwBA,CAACnF,SAAiB,EAAQ;IAChD,MAAMC,eAAe,GAAG,IAAI,CAACC,kBAAkB,CAAC,CAAC;IACjD,MAAMqE,oBAAoB,GAAGtE,eAAe,CAACE,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACJ,SAAS,KAAKA,SAAS,CAAC;IACnF,IAAI,CAACa,gBAAgB,CAAC0D,oBAAoB,CAAC;EAC7C;;EAEA;EACAa,oBAAoBA,CAACpF,SAAiB,EAMpC;IACA,MAAMc,YAAY,GAAG,IAAI,CAACf,wBAAwB,CAACC,SAAS,CAAC;IAE7D,IAAIc,YAAY,CAACM,MAAM,KAAK,CAAC,EAAE;MAC7B,OAAO;QACLiE,iBAAiB,EAAE,CAAC;QACpBC,SAAS,EAAE;UAAEC,IAAI,EAAE,EAAE;UAAEC,EAAE,EAAE;QAAG,CAAC;QAC/BC,WAAW,EAAE,CAAC;QACdC,YAAY,EAAE,CAAC;QACfzC,cAAc,EAAE;MAClB,CAAC;IACH;IAEA,MAAM0C,YAAY,GAAG,CAAC,GAAG7E,YAAY,CAAC,CAACT,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAC/C,IAAIC,IAAI,CAACF,CAAC,CAACG,YAAY,CAAC,CAACC,OAAO,CAAC,CAAC,GAAG,IAAIF,IAAI,CAACD,CAAC,CAACE,YAAY,CAAC,CAACC,OAAO,CAAC,CACxE,CAAC;IAED,OAAO;MACL2E,iBAAiB,EAAEvE,YAAY,CAACM,MAAM;MACtCkE,SAAS,EAAE;QACTC,IAAI,EAAEI,YAAY,CAAC,CAAC,CAAC,CAACvC,IAAI;QAC1BoC,EAAE,EAAEG,YAAY,CAACA,YAAY,CAACvE,MAAM,GAAG,CAAC,CAAC,CAACgC;MAC5C,CAAC;MACDqC,WAAW,EAAE3E,YAAY,CAAC8E,MAAM,CAAC,CAACC,GAAG,EAAEzF,CAAC,KAAKyF,GAAG,GAAGzF,CAAC,CAAC0F,WAAW,EAAE,CAAC,CAAC;MACpEJ,YAAY,EAAE5E,YAAY,CAAC8E,MAAM,CAAC,CAACC,GAAG,EAAEzF,CAAC,KAAKyF,GAAG,GAAGzF,CAAC,CAAC2F,YAAY,EAAE,CAAC,CAAC;MACtE9C,cAAc,EAAEnC,YAAY,CAAC,CAAC,CAAC,CAACyB,OAAO,CAAC;IAC1C,CAAC;EACH;;EAEA;EACAyD,cAAcA,CAAA,EAA2C;IACvD,OAAO;MACLC,QAAQ,EAAExG,kBAAkB,CAACyG,gBAAgB,CAAC,CAAC;MAC/CC,QAAQ,EAAE,GAAG,IAAI,CAACrG,gBAAgB;IACpC,CAAC;EACH;;EAEA;;EAEA;EACAsG,yBAAyBA,CACvBC,aAAqB,EACrBC,UAAkB,EAClBC,QAAiB,GAAG,IAAI,EACf;IACT,MAAMtG,eAAe,GAAG,IAAI,CAACC,kBAAkB,CAAC,CAAC;IACjD,MAAMsG,gBAAgB,GAAGvG,eAAe,CAACwG,SAAS,CAACrG,CAAC,IAAIA,CAAC,CAACgE,EAAE,KAAKiC,aAAa,CAAC;IAE/E,IAAIG,gBAAgB,KAAK,CAAC,CAAC,EAAE,OAAO,KAAK;IAEzC,MAAM1C,WAAW,GAAG7D,eAAe,CAACuG,gBAAgB,CAAC;IACrD,MAAME,GAAG,GAAG,IAAIlG,IAAI,CAAC,CAAC,CAACyB,WAAW,CAAC,CAAC;;IAEpC;IACA,IAAI,CAAC6B,WAAW,CAAC6C,eAAe,EAAE;MAChC7C,WAAW,CAAC6C,eAAe,GAAG,EAAE;IAClC;IAEA7C,WAAW,CAAC6C,eAAe,CAACC,IAAI,CAAC;MAC/BC,kBAAkB,EAAE/C,WAAW,CAACwC,UAAU,IAAIxC,WAAW,CAACgD,gBAAgB;MAC1EC,WAAW,EAAEL,GAAG;MAChBM,SAAS,EAAET,QAAQ,GAAG,QAAQ,GAAG;IACnC,CAAC,CAAC;;IAEF;IACA,IAAIA,QAAQ,EAAE;MACZzC,WAAW,CAACgD,gBAAgB,GAAGR,UAAU;MACzCxC,WAAW,CAACwC,UAAU,GAAGA,UAAU,CAAC,CAAC;IACvC,CAAC,MAAM;MACLxC,WAAW,CAACwC,UAAU,GAAGA,UAAU;IACrC;;IAEA;IACA,IAAI,CAACzF,gBAAgB,CAACZ,eAAe,CAAC;;IAEtC;IACA,IAAIsG,QAAQ,EAAE;MACZ,MAAMU,MAAM,GAAG,CAACnD,WAAW,CAACiC,YAAY,IAAI,CAAC,KAAKjC,WAAW,CAACgC,WAAW,IAAI,CAAC,CAAC;MAC/EnG,eAAe,CAACuH,eAAe,CAC7BpD,WAAW,CAACqD,WAAW,EACvBF,MAAM,EACNX,UACF,CAAC;IACH;IAEA,OAAO,IAAI;EACb;;EAEA;EACA,MAAMc,0BAA0BA,CAACpH,SAAkB,EAAmB;IACpE,IAAIc,YAAY,GAAG,IAAI,CAACZ,kBAAkB,CAAC,CAAC;IAE5C,IAAIF,SAAS,EAAE;MACbc,YAAY,GAAGA,YAAY,CAACX,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACJ,SAAS,KAAKA,SAAS,CAAC;IACpE;;IAEA;IACA,MAAMqH,yBAAyB,GAAGvG,YAAY,CAACX,MAAM,CAACC,CAAC,IACrD,CAACA,CAAC,CAACkG,UAAU,IAAI,CAAClG,CAAC,CAAC0G,gBACtB,CAAC;IAED,IAAIQ,gBAAgB,GAAG,CAAC;IAExB,KAAK,MAAMxD,WAAW,IAAIuD,yBAAyB,EAAE;MACnD,IAAI;QACF,MAAMJ,MAAM,GAAG,CAACnD,WAAW,CAACiC,YAAY,IAAI,CAAC,KAAKjC,WAAW,CAACgC,WAAW,IAAI,CAAC,CAAC;;QAE/E;QACA,MAAMyB,cAAc,GAAG7H,eAAe,CAAC8H,iBAAiB,CACtD1D,WAAW,CAACqD,WAAW,EACvBxE,IAAI,CAACC,GAAG,CAACqE,MAAM,CAAC,EAChBnD,WAAW,CAAC2D,SACd,CAAC;QAED,IAAIF,cAAc,EAAE;UAClB,IAAI,CAACnB,yBAAyB,CAACtC,WAAW,CAACM,EAAE,EAAEmD,cAAc,EAAE,KAAK,CAAC;UACrED,gBAAgB,EAAE;UAClB;QACF;;QAEA;QACA,MAAMI,gBAAgB,GAAG,MAAM/H,eAAe,CAACgI,qBAAqB,CAClE7D,WAAW,CAACqD,WAAW,EACvBF,MACF,CAAC;QAED,IAAIS,gBAAgB,IAAIA,gBAAgB,CAACE,UAAU,GAAG,GAAG,EAAE;UACzD;UACA,MAAM3H,eAAe,GAAG,IAAI,CAACC,kBAAkB,CAAC,CAAC;UACjD,MAAMsG,gBAAgB,GAAGvG,eAAe,CAACwG,SAAS,CAACrG,CAAC,IAAIA,CAAC,CAACgE,EAAE,KAAKN,WAAW,CAACM,EAAE,CAAC;UAEhF,IAAIoC,gBAAgB,KAAK,CAAC,CAAC,EAAE;YAC3BvG,eAAe,CAACuG,gBAAgB,CAAC,CAACF,UAAU,GAAGoB,gBAAgB,CAACpB,UAAU;YAC1ErG,eAAe,CAACuG,gBAAgB,CAAC,CAACkB,gBAAgB,GAAGA,gBAAgB;YACrE,IAAI,CAAC7G,gBAAgB,CAACZ,eAAe,CAAC;YACtCqH,gBAAgB,EAAE;UACpB;QACF;MACF,CAAC,CAAC,OAAOpG,KAAK,EAAE;QACdD,OAAO,CAACC,KAAK,CAAC,kCAAkC4C,WAAW,CAACM,EAAE,GAAG,EAAElD,KAAK,CAAC;MAC3E;IACF;IAEA,OAAOoG,gBAAgB;EACzB;;EAEA;EACAO,yBAAyBA,CAACvB,UAAkB,EAAEtG,SAAkB,EAAuB;IACrF,IAAIc,YAAY,GAAG,IAAI,CAACZ,kBAAkB,CAAC,CAAC;IAE5C,IAAIF,SAAS,EAAE;MACbc,YAAY,GAAGA,YAAY,CAACX,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACJ,SAAS,KAAKA,SAAS,CAAC;IACpE;IAEA,OAAOc,YAAY,CAChBX,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACkG,UAAU,KAAKA,UAAU,IAAIlG,CAAC,CAAC0G,gBAAgB,KAAKR,UAAU,CAAC,CAC7EjG,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK,IAAIC,IAAI,CAACD,CAAC,CAACE,YAAY,CAAC,CAACC,OAAO,CAAC,CAAC,GAAG,IAAIF,IAAI,CAACF,CAAC,CAACG,YAAY,CAAC,CAACC,OAAO,CAAC,CAAC,CAAC;EAC5F;;EAEA;EACAoH,sBAAsBA,CAAC9H,SAAkB,EAQvC;IACA,IAAIc,YAAY,GAAG,IAAI,CAACZ,kBAAkB,CAAC,CAAC;IAE5C,IAAIF,SAAS,EAAE;MACbc,YAAY,GAAGA,YAAY,CAACX,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACJ,SAAS,KAAKA,SAAS,CAAC;IACpE;IAEA,MAAM+H,KAAK,GAAG;MACZC,KAAK,EAAElH,YAAY,CAACM,MAAM;MAC1B6G,WAAW,EAAE,CAAC;MACdC,aAAa,EAAE,CAAC;MAChBC,eAAe,EAAE,CAAC;MAClBC,aAAa,EAAE,CAAC;MAChBC,eAAe,EAAE,CAAC;MAClBC,iBAAiB,EAAE;IACrB,CAAC;IAED,MAAMC,WAAW,GAAG,IAAIC,GAAG,CAAiD,CAAC;IAE7E1H,YAAY,CAAC2H,OAAO,CAAC3E,WAAW,IAAI;MAClC,MAAMmD,MAAM,GAAG,CAACnD,WAAW,CAACiC,YAAY,IAAI,CAAC,KAAKjC,WAAW,CAACgC,WAAW,IAAI,CAAC,CAAC;MAC/E,MAAMQ,UAAU,GAAGxC,WAAW,CAACwC,UAAU,IAAIxC,WAAW,CAACgD,gBAAgB;MAEzE,IAAIR,UAAU,EAAE;QACdyB,KAAK,CAACE,WAAW,EAAE;;QAEnB;QACA,IAAInE,WAAW,CAACgD,gBAAgB,EAAE;UAChCiB,KAAK,CAACI,eAAe,EAAE;QACzB,CAAC,MAAM,IAAIrE,WAAW,CAAC4D,gBAAgB,EAAE;UACvCK,KAAK,CAACK,aAAa,EAAE;QACvB,CAAC,MAAM;UACLL,KAAK,CAACM,eAAe,EAAE;QACzB;;QAEA;QACA,MAAMpE,QAAQ,GAAGsE,WAAW,CAACG,GAAG,CAACpC,UAAU,CAAC,IAAI;UAAEqC,KAAK,EAAE,CAAC;UAAEC,WAAW,EAAE;QAAE,CAAC;QAC5E3E,QAAQ,CAAC0E,KAAK,EAAE;QAChB1E,QAAQ,CAAC2E,WAAW,IAAIjG,IAAI,CAACC,GAAG,CAACqE,MAAM,CAAC;QACxCsB,WAAW,CAACM,GAAG,CAACvC,UAAU,EAAErC,QAAQ,CAAC;MACvC,CAAC,MAAM;QACL8D,KAAK,CAACG,aAAa,EAAE;MACvB;IACF,CAAC,CAAC;;IAEF;IACAH,KAAK,CAACO,iBAAiB,GAAGQ,KAAK,CAACvD,IAAI,CAACgD,WAAW,CAACQ,OAAO,CAAC,CAAC,CAAC,CAAClF,GAAG,CAAC,CAAC,CAACyC,UAAU,EAAE0C,IAAI,CAAC,MAAM;MACvF1C,UAAU;MACV,GAAG0C;IACL,CAAC,CAAC,CAAC;IAEH,OAAOjB,KAAK;EACd;;EAEA;EACAkB,4BAA4BA,CAACjJ,SAAkB,EAAuB;IACpE,IAAIc,YAAY,GAAG,IAAI,CAACZ,kBAAkB,CAAC,CAAC;IAE5C,IAAIF,SAAS,EAAE;MACbc,YAAY,GAAGA,YAAY,CAACX,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACJ,SAAS,KAAKA,SAAS,CAAC;IACpE;IAEA,OAAOc,YAAY,CAChBX,MAAM,CAACC,CAAC,IAAI,CAACA,CAAC,CAACkG,UAAU,IAAI,CAAClG,CAAC,CAAC0G,gBAAgB,CAAC,CACjDzG,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK,IAAIC,IAAI,CAACD,CAAC,CAACE,YAAY,CAAC,CAACC,OAAO,CAAC,CAAC,GAAG,IAAIF,IAAI,CAACF,CAAC,CAACG,YAAY,CAAC,CAACC,OAAO,CAAC,CAAC,CAAC;EAC5F;;EAEA;EACAwI,oBAAoBA,CAACC,cAAwB,EAAE7C,UAAkB,EAAU;IACzE,MAAMrG,eAAe,GAAG,IAAI,CAACC,kBAAkB,CAAC,CAAC;IACjD,IAAIkJ,YAAY,GAAG,CAAC;IAEpBD,cAAc,CAACV,OAAO,CAACrE,EAAE,IAAI;MAC3B,MAAMoC,gBAAgB,GAAGvG,eAAe,CAACwG,SAAS,CAACrG,CAAC,IAAIA,CAAC,CAACgE,EAAE,KAAKA,EAAE,CAAC;MACpE,IAAIoC,gBAAgB,KAAK,CAAC,CAAC,EAAE;QAC3B,MAAM1C,WAAW,GAAG7D,eAAe,CAACuG,gBAAgB,CAAC;QACrD,MAAME,GAAG,GAAG,IAAIlG,IAAI,CAAC,CAAC,CAACyB,WAAW,CAAC,CAAC;;QAEpC;QACA,IAAI,CAAC6B,WAAW,CAAC6C,eAAe,EAAE;UAChC7C,WAAW,CAAC6C,eAAe,GAAG,EAAE;QAClC;QAEA7C,WAAW,CAAC6C,eAAe,CAACC,IAAI,CAAC;UAC/BC,kBAAkB,EAAE/C,WAAW,CAACwC,UAAU,IAAIxC,WAAW,CAACgD,gBAAgB;UAC1EC,WAAW,EAAEL,GAAG;UAChBM,SAAS,EAAE;QACb,CAAC,CAAC;QAEFlD,WAAW,CAACgD,gBAAgB,GAAGR,UAAU;QACzCxC,WAAW,CAACwC,UAAU,GAAGA,UAAU;;QAEnC;QACA,MAAMW,MAAM,GAAG,CAACnD,WAAW,CAACiC,YAAY,IAAI,CAAC,KAAKjC,WAAW,CAACgC,WAAW,IAAI,CAAC,CAAC;QAC/EnG,eAAe,CAACuH,eAAe,CAC7BpD,WAAW,CAACqD,WAAW,EACvBF,MAAM,EACNX,UACF,CAAC;QAED8C,YAAY,EAAE;MAChB;IACF,CAAC,CAAC;IAEF,IAAIA,YAAY,GAAG,CAAC,EAAE;MACpB,IAAI,CAACvI,gBAAgB,CAACZ,eAAe,CAAC;IACxC;IAEA,OAAOmJ,YAAY;EACrB;AACF;AAEA,OAAO,MAAMC,yBAAyB,GAAG,IAAIzJ,yBAAyB,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}