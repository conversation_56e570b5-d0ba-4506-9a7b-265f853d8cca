{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.transformReply = exports.transformArguments = exports.FIRST_KEY_INDEX = void 0;\nexports.FIRST_KEY_INDEX = 1;\nfunction transformArguments(key, item) {\n  return ['CF.ADD', key, item];\n}\nexports.transformArguments = transformArguments;\nvar generic_transformers_1 = require(\"@redis/client/dist/lib/commands/generic-transformers\");\nObject.defineProperty(exports, \"transformReply\", {\n  enumerable: true,\n  get: function () {\n    return generic_transformers_1.transformBooleanReply;\n  }\n});", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "transformReply", "transformArguments", "FIRST_KEY_INDEX", "key", "item", "generic_transformers_1", "require", "enumerable", "get", "transformBooleanReply"], "sources": ["C:/tmsft/node_modules/@redis/bloom/dist/commands/cuckoo/ADD.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.transformReply = exports.transformArguments = exports.FIRST_KEY_INDEX = void 0;\nexports.FIRST_KEY_INDEX = 1;\nfunction transformArguments(key, item) {\n    return ['CF.ADD', key, item];\n}\nexports.transformArguments = transformArguments;\nvar generic_transformers_1 = require(\"@redis/client/dist/lib/commands/generic-transformers\");\nObject.defineProperty(exports, \"transformReply\", { enumerable: true, get: function () { return generic_transformers_1.transformBooleanReply; } });\n"], "mappings": "AAAA,YAAY;;AACZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEC,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7DD,OAAO,CAACE,cAAc,GAAGF,OAAO,CAACG,kBAAkB,GAAGH,OAAO,CAACI,eAAe,GAAG,KAAK,CAAC;AACtFJ,OAAO,CAACI,eAAe,GAAG,CAAC;AAC3B,SAASD,kBAAkBA,CAACE,GAAG,EAAEC,IAAI,EAAE;EACnC,OAAO,CAAC,QAAQ,EAAED,GAAG,EAAEC,IAAI,CAAC;AAChC;AACAN,OAAO,CAACG,kBAAkB,GAAGA,kBAAkB;AAC/C,IAAII,sBAAsB,GAAGC,OAAO,CAAC,sDAAsD,CAAC;AAC5FV,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,gBAAgB,EAAE;EAAES,UAAU,EAAE,IAAI;EAAEC,GAAG,EAAE,SAAAA,CAAA,EAAY;IAAE,OAAOH,sBAAsB,CAACI,qBAAqB;EAAE;AAAE,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}