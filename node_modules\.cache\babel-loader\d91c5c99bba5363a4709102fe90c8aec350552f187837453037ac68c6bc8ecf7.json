{"ast": null, "code": "exports.BayesClassifier = require('./classifier/bayes_classifier');\nexports.LogisticRegressionClassifier = require('./classifier/logistic_regression_classifier');\nexports.KMeans = require('./clusterer/kmeans');", "map": {"version": 3, "names": ["exports", "BayesClassifier", "require", "LogisticRegressionClassifier", "<PERSON><PERSON><PERSON><PERSON>"], "sources": ["C:/tmsft/node_modules/apparatus/lib/apparatus/index.js"], "sourcesContent": ["\nexports.BayesClassifier = require('./classifier/bayes_classifier');\nexports.LogisticRegressionClassifier = require('./classifier/logistic_regression_classifier');\nexports.KMeans = require('./clusterer/kmeans');\n"], "mappings": "AACAA,OAAO,CAACC,eAAe,GAAGC,OAAO,CAAC,+BAA+B,CAAC;AAClEF,OAAO,CAACG,4BAA4B,GAAGD,OAAO,CAAC,6CAA6C,CAAC;AAC7FF,OAAO,CAACI,MAAM,GAAGF,OAAO,CAAC,oBAAoB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}