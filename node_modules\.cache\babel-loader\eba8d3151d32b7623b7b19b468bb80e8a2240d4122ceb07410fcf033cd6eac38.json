{"ast": null, "code": "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { ENGINE } from '../engine';\nimport { Relu } from '../kernel_names';\nimport { convertToTensor } from '../tensor_util_env';\nimport { op } from './operation';\n/**\n * Computes rectified linear element-wise: `max(x, 0)`.\n *\n * ```js\n * const x = tf.tensor1d([-1, 2, -3, 4]);\n *\n * x.relu().print();  // or tf.relu(x)\n * ```\n * @param x The input tensor. If the dtype is `bool`, the output dtype will be\n *     `int32`.\n *\n * @doc {heading: 'Operations', subheading: 'Basic math'}\n */\nfunction relu_(x) {\n  const $x = convertToTensor(x, 'x', 'relu');\n  const inputs = {\n    x: $x\n  };\n  return ENGINE.runKernel(Relu, inputs);\n}\nexport const relu = /* @__PURE__ */op({\n  relu_\n});", "map": {"version": 3, "names": ["ENGINE", "<PERSON><PERSON>", "convertToTensor", "op", "relu_", "x", "$x", "inputs", "runKernel", "relu"], "sources": ["C:\\tfjs-core\\src\\ops\\relu.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {ENGINE} from '../engine';\nimport {Relu, ReluInputs} from '../kernel_names';\nimport {Tensor} from '../tensor';\nimport {NamedTensorMap} from '../tensor_types';\nimport {convertToTensor} from '../tensor_util_env';\nimport {TensorLike} from '../types';\n\nimport {op} from './operation';\n\n/**\n * Computes rectified linear element-wise: `max(x, 0)`.\n *\n * ```js\n * const x = tf.tensor1d([-1, 2, -3, 4]);\n *\n * x.relu().print();  // or tf.relu(x)\n * ```\n * @param x The input tensor. If the dtype is `bool`, the output dtype will be\n *     `int32`.\n *\n * @doc {heading: 'Operations', subheading: 'Basic math'}\n */\nfunction relu_<T extends Tensor>(x: T|TensorLike): T {\n  const $x = convertToTensor(x, 'x', 'relu');\n\n  const inputs: ReluInputs = {x: $x};\n\n  return ENGINE.runKernel(Relu, inputs as unknown as NamedTensorMap);\n}\n\nexport const relu = /* @__PURE__ */ op({relu_});\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAiBA,SAAQA,MAAM,QAAO,WAAW;AAChC,SAAQC,IAAI,QAAmB,iBAAiB;AAGhD,SAAQC,eAAe,QAAO,oBAAoB;AAGlD,SAAQC,EAAE,QAAO,aAAa;AAE9B;;;;;;;;;;;;;AAaA,SAASC,KAAKA,CAAmBC,CAAe;EAC9C,MAAMC,EAAE,GAAGJ,eAAe,CAACG,CAAC,EAAE,GAAG,EAAE,MAAM,CAAC;EAE1C,MAAME,MAAM,GAAe;IAACF,CAAC,EAAEC;EAAE,CAAC;EAElC,OAAON,MAAM,CAACQ,SAAS,CAACP,IAAI,EAAEM,MAAmC,CAAC;AACpE;AAEA,OAAO,MAAME,IAAI,GAAG,eAAgBN,EAAE,CAAC;EAACC;AAAK,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}