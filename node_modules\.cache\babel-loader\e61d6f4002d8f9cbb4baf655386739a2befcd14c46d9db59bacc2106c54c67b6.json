{"ast": null, "code": "/**\n * @license\n * Copyright 2023 Google LLC.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { BitwiseAnd } from '@tensorflow/tfjs-core';\nimport { createSimpleBinaryKernelImpl } from '../utils/binary_impl';\nimport { binaryKernelFunc } from '../utils/binary_utils';\nexport const bitwiseAndImpl = createSimpleBinaryKernelImpl((a, b) => a & b);\nexport const bitwiseAnd = binaryKernelFunc(BitwiseAnd, bitwiseAndImpl);\nexport const bitwiseAndConfig = {\n  kernelName: BitwiseAnd,\n  backendName: 'cpu',\n  kernelFunc: bitwiseAnd\n};", "map": {"version": 3, "names": ["BitwiseAnd", "createSimpleBinaryKernelImpl", "binaryKernelFunc", "bitwiseAndImpl", "a", "b", "bitwiseAnd", "bitwiseAndConfig", "kernelName", "backendName", "kernelFunc"], "sources": ["C:\\tfjs-backend-cpu\\src\\kernels\\BitwiseAnd.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2023 Google LLC.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {BitwiseAnd, KernelConfig} from '@tensorflow/tfjs-core';\n\nimport {createSimpleBinaryKernelImpl} from '../utils/binary_impl';\nimport {binaryKernelFunc} from '../utils/binary_utils';\n\nexport const bitwiseAndImpl =\n    createSimpleBinaryKernelImpl(((a: number, b: number) => a & b));\n\nexport const bitwiseAnd = binaryKernelFunc(BitwiseAnd, bitwiseAndImpl);\n\nexport const bitwiseAndConfig: KernelConfig = {\n  kernelName: BitwiseAnd,\n  backendName: 'cpu',\n  kernelFunc: bitwiseAnd\n};\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAiBA,SAAQA,UAAU,QAAqB,uBAAuB;AAE9D,SAAQC,4BAA4B,QAAO,sBAAsB;AACjE,SAAQC,gBAAgB,QAAO,uBAAuB;AAEtD,OAAO,MAAMC,cAAc,GACvBF,4BAA4B,CAAE,CAACG,CAAS,EAAEC,CAAS,KAAKD,CAAC,GAAGC,CAAE,CAAC;AAEnE,OAAO,MAAMC,UAAU,GAAGJ,gBAAgB,CAACF,UAAU,EAAEG,cAAc,CAAC;AAEtE,OAAO,MAAMI,gBAAgB,GAAiB;EAC5CC,UAAU,EAAER,UAAU;EACtBS,WAAW,EAAE,KAAK;EAClBC,UAAU,EAAEJ;CACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}