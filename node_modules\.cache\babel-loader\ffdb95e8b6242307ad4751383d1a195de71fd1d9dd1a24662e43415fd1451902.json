{"ast": null, "code": "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n/**\n * Shuffles the array in-place using Fisher-Yates algorithm.\n *\n * ```js\n * const a = [1, 2, 3, 4, 5];\n * tf.util.shuffle(a);\n * console.log(a);\n * ```\n *\n * @param array The array to shuffle in-place.\n *\n * @doc {heading: 'Util', namespace: 'util'}\n */\n// tslint:disable-next-line:no-any\nexport function shuffle(array) {\n  let counter = array.length;\n  let index = 0;\n  // While there are elements in the array\n  while (counter > 0) {\n    // Pick a random index\n    index = Math.random() * counter | 0;\n    // Decrease counter by 1\n    counter--;\n    // And swap the last element with it\n    swap(array, counter, index);\n  }\n}\n/**\n * Shuffles two arrays in-place the same way using Fisher-Yates algorithm.\n *\n * ```js\n * const a = [1,2,3,4,5];\n * const b = [11,22,33,44,55];\n * tf.util.shuffleCombo(a, b);\n * console.log(a, b);\n * ```\n *\n * @param array The first array to shuffle in-place.\n * @param array2 The second array to shuffle in-place with the same permutation\n *     as the first array.\n *\n * @doc {heading: 'Util', namespace: 'util'}\n */\nexport function shuffleCombo(\n// tslint:disable-next-line:no-any\narray,\n// tslint:disable-next-line:no-any\narray2) {\n  if (array.length !== array2.length) {\n    throw new Error(\"Array sizes must match to be shuffled together \" + \"First array length was \".concat(array.length) + \"Second array length was \".concat(array2.length));\n  }\n  let counter = array.length;\n  let index = 0;\n  // While there are elements in the array\n  while (counter > 0) {\n    // Pick a random index\n    index = Math.random() * counter | 0;\n    // Decrease counter by 1\n    counter--;\n    // And swap the last element of each array with it\n    swap(array, counter, index);\n    swap(array2, counter, index);\n  }\n}\n/** Clamps a value to a specified range. */\nexport function clamp(min, x, max) {\n  return Math.max(min, Math.min(x, max));\n}\nexport function nearestLargerEven(val) {\n  return val % 2 === 0 ? val : val + 1;\n}\nexport function swap(object, left, right) {\n  const temp = object[left];\n  object[left] = object[right];\n  object[right] = temp;\n}\nexport function sum(arr) {\n  let sum = 0;\n  for (let i = 0; i < arr.length; i++) {\n    sum += arr[i];\n  }\n  return sum;\n}\n/**\n * Returns a sample from a uniform [a, b) distribution.\n *\n * @param a The minimum support (inclusive).\n * @param b The maximum support (exclusive).\n * @return A pseudorandom number on the half-open interval [a,b).\n */\nexport function randUniform(a, b) {\n  const r = Math.random();\n  return b * r + (1 - r) * a;\n}\n/** Returns the squared Euclidean distance between two vectors. */\nexport function distSquared(a, b) {\n  let result = 0;\n  for (let i = 0; i < a.length; i++) {\n    const diff = Number(a[i]) - Number(b[i]);\n    result += diff * diff;\n  }\n  return result;\n}\n/**\n * Asserts that the expression is true. Otherwise throws an error with the\n * provided message.\n *\n * ```js\n * const x = 2;\n * tf.util.assert(x === 2, 'x is not 2');\n * ```\n *\n * @param expr The expression to assert (as a boolean).\n * @param msg A function that returns the message to report when throwing an\n *     error. We use a function for performance reasons.\n *\n * @doc {heading: 'Util', namespace: 'util'}\n */\nexport function assert(expr, msg) {\n  if (!expr) {\n    throw new Error(typeof msg === 'string' ? msg : msg());\n  }\n}\nexport function assertShapesMatch(shapeA, shapeB) {\n  let errorMessagePrefix = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : '';\n  assert(arraysEqual(shapeA, shapeB), () => errorMessagePrefix + \" Shapes \".concat(shapeA, \" and \").concat(shapeB, \" must match\"));\n}\nexport function assertNonNull(a) {\n  assert(a != null, () => \"The input to the tensor constructor must be a non-null value.\");\n}\n/**\n * Returns the size (number of elements) of the tensor given its shape.\n *\n * ```js\n * const shape = [3, 4, 2];\n * const size = tf.util.sizeFromShape(shape);\n * console.log(size);\n * ```\n *\n * @doc {heading: 'Util', namespace: 'util'}\n */\nexport function sizeFromShape(shape) {\n  if (shape.length === 0) {\n    // Scalar.\n    return 1;\n  }\n  let size = shape[0];\n  for (let i = 1; i < shape.length; i++) {\n    size *= shape[i];\n  }\n  return size;\n}\nexport function isScalarShape(shape) {\n  return shape.length === 0;\n}\nexport function arraysEqualWithNull(n1, n2) {\n  if (n1 === n2) {\n    return true;\n  }\n  if (n1 == null || n2 == null) {\n    return false;\n  }\n  if (n1.length !== n2.length) {\n    return false;\n  }\n  for (let i = 0; i < n1.length; i++) {\n    if (n1[i] !== null && n2[i] !== null && n1[i] !== n2[i]) {\n      return false;\n    }\n  }\n  return true;\n}\nexport function arraysEqual(n1, n2) {\n  if (n1 === n2) {\n    return true;\n  }\n  if (n1 == null || n2 == null) {\n    return false;\n  }\n  if (n1.length !== n2.length) {\n    return false;\n  }\n  for (let i = 0; i < n1.length; i++) {\n    if (n1[i] !== n2[i]) {\n      return false;\n    }\n  }\n  return true;\n}\nexport function isInt(a) {\n  return a % 1 === 0;\n}\nexport function tanh(x) {\n  // tslint:disable-next-line:no-any\n  if (Math.tanh != null) {\n    // tslint:disable-next-line:no-any\n    return Math.tanh(x);\n  }\n  if (x === Infinity) {\n    return 1;\n  } else if (x === -Infinity) {\n    return -1;\n  } else {\n    const e2x = Math.exp(2 * x);\n    return (e2x - 1) / (e2x + 1);\n  }\n}\nexport function sizeToSquarishShape(size) {\n  const width = Math.ceil(Math.sqrt(size));\n  return [width, Math.ceil(size / width)];\n}\n/**\n * Creates a new array with randomized indices to a given quantity.\n *\n * ```js\n * const randomTen = tf.util.createShuffledIndices(10);\n * console.log(randomTen);\n * ```\n *\n * @param number Quantity of how many shuffled indices to create.\n *\n * @doc {heading: 'Util', namespace: 'util'}\n */\nexport function createShuffledIndices(n) {\n  const shuffledIndices = new Uint32Array(n);\n  for (let i = 0; i < n; ++i) {\n    shuffledIndices[i] = i;\n  }\n  shuffle(shuffledIndices);\n  return shuffledIndices;\n}\nexport function rightPad(a, size) {\n  if (size <= a.length) {\n    return a;\n  }\n  return a + ' '.repeat(size - a.length);\n}\nexport function repeatedTry(checkFn) {\n  let delayFn = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : counter => 0;\n  let maxCounter = arguments.length > 2 ? arguments[2] : undefined;\n  let scheduleFn = arguments.length > 3 ? arguments[3] : undefined;\n  return new Promise((resolve, reject) => {\n    let tryCount = 0;\n    const tryFn = () => {\n      if (checkFn()) {\n        resolve();\n        return;\n      }\n      tryCount++;\n      const nextBackoff = delayFn(tryCount);\n      if (maxCounter != null && tryCount >= maxCounter) {\n        reject();\n        return;\n      }\n      if (scheduleFn != null) {\n        scheduleFn(tryFn, nextBackoff);\n      } else {\n        // google3 does not allow assigning another variable to setTimeout.\n        // Don't refactor this so scheduleFn has a default value of setTimeout.\n        setTimeout(tryFn, nextBackoff);\n      }\n    };\n    tryFn();\n  });\n}\n/**\n * Given the full size of the array and a shape that may contain -1 as the\n * implicit dimension, returns the inferred shape where -1 is replaced.\n * E.g. For shape=[2, -1, 3] and size=24, it will return [2, 4, 3].\n *\n * @param shape The shape, which may contain -1 in some dimension.\n * @param size The full size (number of elements) of the array.\n * @return The inferred shape where -1 is replaced with the inferred size.\n */\nexport function inferFromImplicitShape(shape, size) {\n  let shapeProd = 1;\n  let implicitIdx = -1;\n  for (let i = 0; i < shape.length; ++i) {\n    if (shape[i] >= 0) {\n      shapeProd *= shape[i];\n    } else if (shape[i] === -1) {\n      if (implicitIdx !== -1) {\n        throw Error(\"Shapes can only have 1 implicit size. \" + \"Found -1 at dim \".concat(implicitIdx, \" and dim \").concat(i));\n      }\n      implicitIdx = i;\n    } else if (shape[i] < 0) {\n      throw Error(\"Shapes can not be < 0. Found \".concat(shape[i], \" at dim \").concat(i));\n    }\n  }\n  if (implicitIdx === -1) {\n    if (size > 0 && size !== shapeProd) {\n      throw Error(\"Size(\".concat(size, \") must match the product of shape \").concat(shape));\n    }\n    return shape;\n  }\n  if (shapeProd === 0) {\n    throw Error(\"Cannot infer the missing size in [\".concat(shape, \"] when \") + \"there are 0 elements\");\n  }\n  if (size % shapeProd !== 0) {\n    throw Error(\"The implicit shape can't be a fractional number. \" + \"Got \".concat(size, \" / \").concat(shapeProd));\n  }\n  const newShape = shape.slice();\n  newShape[implicitIdx] = size / shapeProd;\n  return newShape;\n}\nexport function parseAxisParam(axis, shape) {\n  const rank = shape.length;\n  // Normalize input\n  axis = axis == null ? shape.map((s, i) => i) : [].concat(axis);\n  // Check for valid range\n  assert(axis.every(ax => ax >= -rank && ax < rank), () => \"All values in axis param must be in range [-\".concat(rank, \", \").concat(rank, \") but \") + \"got axis \".concat(axis));\n  // Check for only integers\n  assert(axis.every(ax => isInt(ax)), () => \"All values in axis param must be integers but \" + \"got axis \".concat(axis));\n  // Handle negative axis.\n  return axis.map(a => a < 0 ? rank + a : a);\n}\n/** Reduces the shape by removing all dimensions of shape 1. */\nexport function squeezeShape(shape, axis) {\n  const newShape = [];\n  const keptDims = [];\n  const isEmptyArray = axis != null && Array.isArray(axis) && axis.length === 0;\n  const axes = axis == null || isEmptyArray ? null : parseAxisParam(axis, shape).sort();\n  let j = 0;\n  for (let i = 0; i < shape.length; ++i) {\n    if (axes != null) {\n      if (axes[j] === i && shape[i] !== 1) {\n        throw new Error(\"Can't squeeze axis \".concat(i, \" since its dim '\").concat(shape[i], \"' is not 1\"));\n      }\n      if ((axes[j] == null || axes[j] > i) && shape[i] === 1) {\n        newShape.push(shape[i]);\n        keptDims.push(i);\n      }\n      if (axes[j] <= i) {\n        j++;\n      }\n    }\n    if (shape[i] !== 1) {\n      newShape.push(shape[i]);\n      keptDims.push(i);\n    }\n  }\n  return {\n    newShape,\n    keptDims\n  };\n}\nexport function getTypedArrayFromDType(dtype, size) {\n  return getArrayFromDType(dtype, size);\n}\nexport function getArrayFromDType(dtype, size) {\n  let values = null;\n  if (dtype == null || dtype === 'float32') {\n    values = new Float32Array(size);\n  } else if (dtype === 'int32') {\n    values = new Int32Array(size);\n  } else if (dtype === 'bool') {\n    values = new Uint8Array(size);\n  } else if (dtype === 'string') {\n    values = new Array(size);\n  } else {\n    throw new Error(\"Unknown data type \".concat(dtype));\n  }\n  return values;\n}\nexport function checkConversionForErrors(vals, dtype) {\n  for (let i = 0; i < vals.length; i++) {\n    const num = vals[i];\n    if (isNaN(num) || !isFinite(num)) {\n      throw Error(\"A tensor of type \".concat(dtype, \" being uploaded contains \").concat(num, \".\"));\n    }\n  }\n}\n/** Returns true if the dtype is valid. */\nexport function isValidDtype(dtype) {\n  return dtype === 'bool' || dtype === 'complex64' || dtype === 'float32' || dtype === 'int32' || dtype === 'string';\n}\n/**\n * Returns true if the new type can't encode the old type without loss of\n * precision.\n */\nexport function hasEncodingLoss(oldType, newType) {\n  if (newType === 'complex64') {\n    return false;\n  }\n  if (newType === 'float32' && oldType !== 'complex64') {\n    return false;\n  }\n  if (newType === 'int32' && oldType !== 'float32' && oldType !== 'complex64') {\n    return false;\n  }\n  if (newType === 'bool' && oldType === 'bool') {\n    return false;\n  }\n  return true;\n}\nexport function bytesPerElement(dtype) {\n  if (dtype === 'float32' || dtype === 'int32') {\n    return 4;\n  } else if (dtype === 'complex64') {\n    return 8;\n  } else if (dtype === 'bool') {\n    return 1;\n  } else {\n    throw new Error(\"Unknown dtype \".concat(dtype));\n  }\n}\n/**\n * Returns the approximate number of bytes allocated in the string array - 2\n * bytes per character. Computing the exact bytes for a native string in JS\n * is not possible since it depends on the encoding of the html page that\n * serves the website.\n */\nexport function bytesFromStringArray(arr) {\n  if (arr == null) {\n    return 0;\n  }\n  let bytes = 0;\n  arr.forEach(x => bytes += x.length);\n  return bytes;\n}\n/** Returns true if the value is a string. */\nexport function isString(value) {\n  return typeof value === 'string' || value instanceof String;\n}\nexport function isBoolean(value) {\n  return typeof value === 'boolean';\n}\nexport function isNumber(value) {\n  return typeof value === 'number';\n}\nexport function inferDtype(values) {\n  if (Array.isArray(values)) {\n    return inferDtype(values[0]);\n  }\n  if (values instanceof Float32Array) {\n    return 'float32';\n  } else if (values instanceof Int32Array || values instanceof Uint8Array || values instanceof Uint8ClampedArray) {\n    return 'int32';\n  } else if (isNumber(values)) {\n    return 'float32';\n  } else if (isString(values)) {\n    return 'string';\n  } else if (isBoolean(values)) {\n    return 'bool';\n  }\n  return 'float32';\n}\nexport function isFunction(f) {\n  return !!(f && f.constructor && f.call && f.apply);\n}\nexport function nearestDivisor(size, start) {\n  for (let i = start; i < size; ++i) {\n    if (size % i === 0) {\n      return i;\n    }\n  }\n  return size;\n}\nexport function computeStrides(shape) {\n  const rank = shape.length;\n  if (rank < 2) {\n    return [];\n  }\n  // Last dimension has implicit stride of 1, thus having D-1 (instead of D)\n  // strides.\n  const strides = new Array(rank - 1);\n  strides[rank - 2] = shape[rank - 1];\n  for (let i = rank - 3; i >= 0; --i) {\n    strides[i] = strides[i + 1] * shape[i + 1];\n  }\n  return strides;\n}\nfunction createNestedArray(offset, shape, a) {\n  let isComplex = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : false;\n  const ret = new Array();\n  if (shape.length === 1) {\n    const d = shape[0] * (isComplex ? 2 : 1);\n    for (let i = 0; i < d; i++) {\n      ret[i] = a[offset + i];\n    }\n  } else {\n    const d = shape[0];\n    const rest = shape.slice(1);\n    const len = rest.reduce((acc, c) => acc * c) * (isComplex ? 2 : 1);\n    for (let i = 0; i < d; i++) {\n      ret[i] = createNestedArray(offset + i * len, rest, a, isComplex);\n    }\n  }\n  return ret;\n}\n// Provide a nested array of TypedArray in given shape.\nexport function toNestedArray(shape, a) {\n  let isComplex = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : false;\n  if (shape.length === 0) {\n    // Scalar type should return a single number.\n    return a[0];\n  }\n  const size = shape.reduce((acc, c) => acc * c) * (isComplex ? 2 : 1);\n  if (size === 0) {\n    // A tensor with shape zero should be turned into empty list.\n    return [];\n  }\n  if (size !== a.length) {\n    throw new Error(\"[\".concat(shape, \"] does not match the input size \").concat(a.length).concat(isComplex ? ' for a complex tensor' : '', \".\"));\n  }\n  return createNestedArray(0, shape, a, isComplex);\n}\nexport function convertBackendValuesAndArrayBuffer(data, dtype) {\n  // If is type Uint8Array[], return it directly.\n  if (Array.isArray(data)) {\n    return data;\n  }\n  if (dtype === 'float32') {\n    return data instanceof Float32Array ? data : new Float32Array(data);\n  } else if (dtype === 'int32') {\n    return data instanceof Int32Array ? data : new Int32Array(data);\n  } else if (dtype === 'bool' || dtype === 'string') {\n    return Uint8Array.from(new Int32Array(data));\n  } else {\n    throw new Error(\"Unknown dtype \".concat(dtype));\n  }\n}\nexport function makeOnesTypedArray(size, dtype) {\n  const array = makeZerosTypedArray(size, dtype);\n  for (let i = 0; i < array.length; i++) {\n    array[i] = 1;\n  }\n  return array;\n}\nexport function makeZerosTypedArray(size, dtype) {\n  if (dtype == null || dtype === 'float32' || dtype === 'complex64') {\n    return new Float32Array(size);\n  } else if (dtype === 'int32') {\n    return new Int32Array(size);\n  } else if (dtype === 'bool') {\n    return new Uint8Array(size);\n  } else {\n    throw new Error(\"Unknown data type \".concat(dtype));\n  }\n}\n/**\n * Make nested `TypedArray` filled with zeros.\n * @param shape The shape information for the nested array.\n * @param dtype dtype of the array element.\n */\nexport function makeZerosNestedTypedArray(shape, dtype) {\n  const size = shape.reduce((prev, curr) => prev * curr, 1);\n  if (dtype == null || dtype === 'float32') {\n    return toNestedArray(shape, new Float32Array(size));\n  } else if (dtype === 'int32') {\n    return toNestedArray(shape, new Int32Array(size));\n  } else if (dtype === 'bool') {\n    return toNestedArray(shape, new Uint8Array(size));\n  } else {\n    throw new Error(\"Unknown data type \".concat(dtype));\n  }\n}\nexport function assertNonNegativeIntegerDimensions(shape) {\n  shape.forEach(dimSize => {\n    assert(Number.isInteger(dimSize) && dimSize >= 0, () => \"Tensor must have a shape comprised of positive integers but got \" + \"shape [\".concat(shape, \"].\"));\n  });\n}\n/**\n * Computes flat index for a given location (multidimentionsal index) in a\n * Tensor/multidimensional array.\n *\n * @param locs Location in the tensor.\n * @param rank Rank of the tensor.\n * @param strides Tensor strides.\n */\nexport function locToIndex(locs, rank, strides) {\n  if (rank === 0) {\n    return 0;\n  } else if (rank === 1) {\n    return locs[0];\n  }\n  let index = locs[locs.length - 1];\n  for (let i = 0; i < locs.length - 1; ++i) {\n    index += strides[i] * locs[i];\n  }\n  return index;\n}\n/**\n * Computes the location (multidimensional index) in a\n * tensor/multidimentional array for a given flat index.\n *\n * @param index Index in flat array.\n * @param rank Rank of tensor.\n * @param strides Strides of tensor.\n */\nexport function indexToLoc(index, rank, strides) {\n  if (rank === 0) {\n    return [];\n  } else if (rank === 1) {\n    return [index];\n  }\n  const locs = new Array(rank);\n  for (let i = 0; i < locs.length - 1; ++i) {\n    locs[i] = Math.floor(index / strides[i]);\n    index -= locs[i] * strides[i];\n  }\n  locs[locs.length - 1] = index;\n  return locs;\n}\n/**\n * This method asserts whether an object is a Promise instance.\n * @param object\n */\n// tslint:disable-next-line: no-any\nexport function isPromise(object) {\n  //  We chose to not use 'obj instanceOf Promise' for two reasons:\n  //  1. It only reliably works for es6 Promise, not other Promise\n  //  implementations.\n  //  2. It doesn't work with framework that uses zone.js. zone.js monkey\n  //  patch the async calls, so it is possible the obj (patched) is\n  //  comparing to a pre-patched Promise.\n  return object && object.then && typeof object.then === 'function';\n}", "map": {"version": 3, "names": ["shuffle", "array", "counter", "length", "index", "Math", "random", "swap", "shuffleCombo", "array2", "Error", "concat", "clamp", "min", "x", "max", "nearestLargerEven", "val", "object", "left", "right", "temp", "sum", "arr", "i", "randUniform", "a", "b", "r", "distSquared", "result", "diff", "Number", "assert", "expr", "msg", "assertShapesMatch", "shapeA", "shapeB", "errorMessagePrefix", "arguments", "undefined", "arraysEqual", "assertNonNull", "sizeFromShape", "shape", "size", "isScalarShape", "arraysEqualWithNull", "n1", "n2", "isInt", "tanh", "Infinity", "e2x", "exp", "sizeToSquarishShape", "width", "ceil", "sqrt", "createShuffledIndices", "n", "shuffledIndices", "Uint32Array", "rightPad", "repeat", "repeatedTry", "checkFn", "delayFn", "max<PERSON><PERSON><PERSON>", "scheduleFn", "Promise", "resolve", "reject", "tryCount", "tryFn", "<PERSON><PERSON><PERSON><PERSON>", "setTimeout", "inferFromImplicitShape", "shapeProd", "implicitIdx", "newShape", "slice", "parseAxisParam", "axis", "rank", "map", "s", "every", "ax", "squeezeShape", "keptDims", "isEmptyArray", "Array", "isArray", "axes", "sort", "j", "push", "getTypedArrayFromDType", "dtype", "getArrayFromDType", "values", "Float32Array", "Int32Array", "Uint8Array", "checkConversionForErrors", "vals", "num", "isNaN", "isFinite", "isValidDtype", "hasEncodingLoss", "oldType", "newType", "bytesPerElement", "bytesFromStringArray", "bytes", "for<PERSON>ach", "isString", "value", "String", "isBoolean", "isNumber", "inferDtype", "Uint8ClampedArray", "isFunction", "f", "constructor", "call", "apply", "nearestDivisor", "start", "computeStrides", "strides", "createNestedArray", "offset", "isComplex", "ret", "d", "rest", "len", "reduce", "acc", "c", "toNestedArray", "convertBackendValuesAndArrayBuffer", "data", "from", "makeOnesTypedArray", "makeZerosTypedArray", "makeZerosNestedTypedArray", "prev", "curr", "assertNonNegativeIntegerDimensions", "dimSize", "isInteger", "locToIndex", "locs", "indexToLoc", "floor", "isPromise", "then"], "sources": ["C:\\tfjs-core\\src\\util_base.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {BackendValues, DataType, DataTypeMap, FlatVector, NumericDataType, TensorLike, TypedArray, WebGLData, WebGPUData} from './types';\n\n/**\n * Shuffles the array in-place using Fisher<PERSON><PERSON> algorithm.\n *\n * ```js\n * const a = [1, 2, 3, 4, 5];\n * tf.util.shuffle(a);\n * console.log(a);\n * ```\n *\n * @param array The array to shuffle in-place.\n *\n * @doc {heading: 'Util', namespace: 'util'}\n */\n// tslint:disable-next-line:no-any\nexport function shuffle(array: any[]|Uint32Array|Int32Array|\n                        Float32Array): void {\n  let counter = array.length;\n  let index = 0;\n  // While there are elements in the array\n  while (counter > 0) {\n    // Pick a random index\n    index = (Math.random() * counter) | 0;\n    // Decrease counter by 1\n    counter--;\n    // And swap the last element with it\n    swap(array, counter, index);\n  }\n}\n\n/**\n * Shuffles two arrays in-place the same way using Fisher-Yates algorithm.\n *\n * ```js\n * const a = [1,2,3,4,5];\n * const b = [11,22,33,44,55];\n * tf.util.shuffleCombo(a, b);\n * console.log(a, b);\n * ```\n *\n * @param array The first array to shuffle in-place.\n * @param array2 The second array to shuffle in-place with the same permutation\n *     as the first array.\n *\n * @doc {heading: 'Util', namespace: 'util'}\n */\nexport function shuffleCombo(\n    // tslint:disable-next-line:no-any\n    array: any[]|Uint32Array|Int32Array|Float32Array,\n    // tslint:disable-next-line:no-any\n    array2: any[]|Uint32Array|Int32Array|Float32Array): void {\n  if (array.length !== array2.length) {\n    throw new Error(\n        `Array sizes must match to be shuffled together ` +\n        `First array length was ${array.length}` +\n        `Second array length was ${array2.length}`);\n  }\n  let counter = array.length;\n  let index = 0;\n  // While there are elements in the array\n  while (counter > 0) {\n    // Pick a random index\n    index = (Math.random() * counter) | 0;\n    // Decrease counter by 1\n    counter--;\n    // And swap the last element of each array with it\n    swap(array, counter, index);\n    swap(array2, counter, index);\n  }\n}\n\n/** Clamps a value to a specified range. */\nexport function clamp(min: number, x: number, max: number): number {\n  return Math.max(min, Math.min(x, max));\n}\n\nexport function nearestLargerEven(val: number): number {\n  return val % 2 === 0 ? val : val + 1;\n}\n\nexport function swap<T>(\n    object: {[index: number]: T}, left: number, right: number) {\n  const temp = object[left];\n  object[left] = object[right];\n  object[right] = temp;\n}\n\nexport function sum(arr: number[]): number {\n  let sum = 0;\n  for (let i = 0; i < arr.length; i++) {\n    sum += arr[i];\n  }\n  return sum;\n}\n\n/**\n * Returns a sample from a uniform [a, b) distribution.\n *\n * @param a The minimum support (inclusive).\n * @param b The maximum support (exclusive).\n * @return A pseudorandom number on the half-open interval [a,b).\n */\nexport function randUniform(a: number, b: number) {\n  const r = Math.random();\n  return (b * r) + (1 - r) * a;\n}\n\n/** Returns the squared Euclidean distance between two vectors. */\nexport function distSquared(a: FlatVector, b: FlatVector): number {\n  let result = 0;\n  for (let i = 0; i < a.length; i++) {\n    const diff = Number(a[i]) - Number(b[i]);\n    result += diff * diff;\n  }\n  return result;\n}\n\n/**\n * Asserts that the expression is true. Otherwise throws an error with the\n * provided message.\n *\n * ```js\n * const x = 2;\n * tf.util.assert(x === 2, 'x is not 2');\n * ```\n *\n * @param expr The expression to assert (as a boolean).\n * @param msg A function that returns the message to report when throwing an\n *     error. We use a function for performance reasons.\n *\n * @doc {heading: 'Util', namespace: 'util'}\n */\nexport function assert(expr: boolean, msg: () => string) {\n  if (!expr) {\n    throw new Error(typeof msg === 'string' ? msg : msg());\n  }\n}\n\nexport function assertShapesMatch(\n    shapeA: number[], shapeB: number[], errorMessagePrefix = ''): void {\n  assert(\n      arraysEqual(shapeA, shapeB),\n      () => errorMessagePrefix + ` Shapes ${shapeA} and ${shapeB} must match`);\n}\n\nexport function assertNonNull(a: TensorLike): void {\n  assert(\n      a != null,\n      () => `The input to the tensor constructor must be a non-null value.`);\n}\n\n/**\n * Returns the size (number of elements) of the tensor given its shape.\n *\n * ```js\n * const shape = [3, 4, 2];\n * const size = tf.util.sizeFromShape(shape);\n * console.log(size);\n * ```\n *\n * @doc {heading: 'Util', namespace: 'util'}\n */\nexport function sizeFromShape(shape: number[]): number {\n  if (shape.length === 0) {\n    // Scalar.\n    return 1;\n  }\n  let size = shape[0];\n  for (let i = 1; i < shape.length; i++) {\n    size *= shape[i];\n  }\n  return size;\n}\n\nexport function isScalarShape(shape: number[]): boolean {\n  return shape.length === 0;\n}\n\nexport function arraysEqualWithNull(n1: number[], n2: number[]) {\n  if (n1 === n2) {\n    return true;\n  }\n\n  if (n1 == null || n2 == null) {\n    return false;\n  }\n\n  if (n1.length !== n2.length) {\n    return false;\n  }\n\n  for (let i = 0; i < n1.length; i++) {\n    if (n1[i] !== null && n2[i] !== null && n1[i] !== n2[i]) {\n      return false;\n    }\n  }\n  return true;\n}\n\nexport function arraysEqual(n1: FlatVector, n2: FlatVector) {\n  if (n1 === n2) {\n    return true;\n  }\n  if (n1 == null || n2 == null) {\n    return false;\n  }\n\n  if (n1.length !== n2.length) {\n    return false;\n  }\n  for (let i = 0; i < n1.length; i++) {\n    if (n1[i] !== n2[i]) {\n      return false;\n    }\n  }\n  return true;\n}\n\nexport function isInt(a: number): boolean {\n  return a % 1 === 0;\n}\n\nexport function tanh(x: number): number {\n  // tslint:disable-next-line:no-any\n  if ((Math as any).tanh != null) {\n    // tslint:disable-next-line:no-any\n    return (Math as any).tanh(x);\n  }\n  if (x === Infinity) {\n    return 1;\n  } else if (x === -Infinity) {\n    return -1;\n  } else {\n    const e2x = Math.exp(2 * x);\n    return (e2x - 1) / (e2x + 1);\n  }\n}\n\nexport function sizeToSquarishShape(size: number): [number, number] {\n  const width = Math.ceil(Math.sqrt(size));\n  return [width, Math.ceil(size / width)];\n}\n\n/**\n * Creates a new array with randomized indices to a given quantity.\n *\n * ```js\n * const randomTen = tf.util.createShuffledIndices(10);\n * console.log(randomTen);\n * ```\n *\n * @param number Quantity of how many shuffled indices to create.\n *\n * @doc {heading: 'Util', namespace: 'util'}\n */\nexport function createShuffledIndices(n: number): Uint32Array {\n  const shuffledIndices = new Uint32Array(n);\n  for (let i = 0; i < n; ++i) {\n    shuffledIndices[i] = i;\n  }\n  shuffle(shuffledIndices);\n  return shuffledIndices;\n}\n\nexport function rightPad(a: string, size: number): string {\n  if (size <= a.length) {\n    return a;\n  }\n  return a + ' '.repeat(size - a.length);\n}\n\nexport function repeatedTry(\n    checkFn: () => boolean, delayFn = (counter: number) => 0,\n    maxCounter?: number,\n    scheduleFn?: (functionRef: Function, delay: number) =>\n        void): Promise<void> {\n  return new Promise<void>((resolve, reject) => {\n    let tryCount = 0;\n\n    const tryFn = () => {\n      if (checkFn()) {\n        resolve();\n        return;\n      }\n\n      tryCount++;\n\n      const nextBackoff = delayFn(tryCount);\n\n      if (maxCounter != null && tryCount >= maxCounter) {\n        reject();\n        return;\n      }\n\n      if (scheduleFn != null) {\n        scheduleFn(tryFn, nextBackoff);\n      } else {\n        // google3 does not allow assigning another variable to setTimeout.\n        // Don't refactor this so scheduleFn has a default value of setTimeout.\n        setTimeout(tryFn, nextBackoff);\n      }\n    };\n\n    tryFn();\n  });\n}\n\n/**\n * Given the full size of the array and a shape that may contain -1 as the\n * implicit dimension, returns the inferred shape where -1 is replaced.\n * E.g. For shape=[2, -1, 3] and size=24, it will return [2, 4, 3].\n *\n * @param shape The shape, which may contain -1 in some dimension.\n * @param size The full size (number of elements) of the array.\n * @return The inferred shape where -1 is replaced with the inferred size.\n */\nexport function inferFromImplicitShape(\n    shape: number[], size: number): number[] {\n  let shapeProd = 1;\n  let implicitIdx = -1;\n\n  for (let i = 0; i < shape.length; ++i) {\n    if (shape[i] >= 0) {\n      shapeProd *= shape[i];\n    } else if (shape[i] === -1) {\n      if (implicitIdx !== -1) {\n        throw Error(\n            `Shapes can only have 1 implicit size. ` +\n            `Found -1 at dim ${implicitIdx} and dim ${i}`);\n      }\n      implicitIdx = i;\n    } else if (shape[i] < 0) {\n      throw Error(`Shapes can not be < 0. Found ${shape[i]} at dim ${i}`);\n    }\n  }\n\n  if (implicitIdx === -1) {\n    if (size > 0 && size !== shapeProd) {\n      throw Error(`Size(${size}) must match the product of shape ${shape}`);\n    }\n    return shape;\n  }\n\n  if (shapeProd === 0) {\n    throw Error(\n        `Cannot infer the missing size in [${shape}] when ` +\n        `there are 0 elements`);\n  }\n  if (size % shapeProd !== 0) {\n    throw Error(\n        `The implicit shape can't be a fractional number. ` +\n        `Got ${size} / ${shapeProd}`);\n  }\n\n  const newShape = shape.slice();\n  newShape[implicitIdx] = size / shapeProd;\n  return newShape;\n}\n\nexport function parseAxisParam(\n    axis: number|number[], shape: number[]): number[] {\n  const rank = shape.length;\n\n  // Normalize input\n  axis = axis == null ? shape.map((s, i) => i) : [].concat(axis);\n\n  // Check for valid range\n  assert(\n      axis.every(ax => ax >= -rank && ax < rank),\n      () =>\n          `All values in axis param must be in range [-${rank}, ${rank}) but ` +\n          `got axis ${axis}`);\n\n  // Check for only integers\n  assert(\n      axis.every(ax => isInt(ax)),\n      () => `All values in axis param must be integers but ` +\n          `got axis ${axis}`);\n\n  // Handle negative axis.\n  return axis.map(a => a < 0 ? rank + a : a);\n}\n\n/** Reduces the shape by removing all dimensions of shape 1. */\nexport function squeezeShape(shape: number[], axis?: number[]):\n    {newShape: number[], keptDims: number[]} {\n  const newShape: number[] = [];\n  const keptDims: number[] = [];\n  const isEmptyArray = axis != null && Array.isArray(axis) && axis.length === 0;\n  const axes = (axis == null || isEmptyArray) ?\n      null :\n      parseAxisParam(axis, shape).sort();\n  let j = 0;\n  for (let i = 0; i < shape.length; ++i) {\n    if (axes != null) {\n      if (axes[j] === i && shape[i] !== 1) {\n        throw new Error(\n            `Can't squeeze axis ${i} since its dim '${shape[i]}' is not 1`);\n      }\n      if ((axes[j] == null || axes[j] > i) && shape[i] === 1) {\n        newShape.push(shape[i]);\n        keptDims.push(i);\n      }\n      if (axes[j] <= i) {\n        j++;\n      }\n    }\n    if (shape[i] !== 1) {\n      newShape.push(shape[i]);\n      keptDims.push(i);\n    }\n  }\n  return {newShape, keptDims};\n}\n\nexport function getTypedArrayFromDType<D extends NumericDataType>(\n    dtype: D, size: number): DataTypeMap[D] {\n  return getArrayFromDType<D>(dtype, size);\n}\n\nexport function getArrayFromDType<D extends DataType>(\n    dtype: D, size: number): DataTypeMap[D] {\n  let values = null;\n  if (dtype == null || dtype === 'float32') {\n    values = new Float32Array(size);\n  } else if (dtype === 'int32') {\n    values = new Int32Array(size);\n  } else if (dtype === 'bool') {\n    values = new Uint8Array(size);\n  } else if (dtype === 'string') {\n    values = new Array<string>(size);\n  } else {\n    throw new Error(`Unknown data type ${dtype}`);\n  }\n  return values as DataTypeMap[D];\n}\n\nexport function checkConversionForErrors<D extends DataType>(\n    vals: DataTypeMap[D]|number[], dtype: D): void {\n  for (let i = 0; i < vals.length; i++) {\n    const num = vals[i] as number;\n    if (isNaN(num) || !isFinite(num)) {\n      throw Error(`A tensor of type ${dtype} being uploaded contains ${num}.`);\n    }\n  }\n}\n\n/** Returns true if the dtype is valid. */\nexport function isValidDtype(dtype: DataType): boolean {\n  return dtype === 'bool' || dtype === 'complex64' || dtype === 'float32' ||\n      dtype === 'int32' || dtype === 'string';\n}\n\n/**\n * Returns true if the new type can't encode the old type without loss of\n * precision.\n */\nexport function hasEncodingLoss(oldType: DataType, newType: DataType): boolean {\n  if (newType === 'complex64') {\n    return false;\n  }\n  if (newType === 'float32' && oldType !== 'complex64') {\n    return false;\n  }\n  if (newType === 'int32' && oldType !== 'float32' && oldType !== 'complex64') {\n    return false;\n  }\n  if (newType === 'bool' && oldType === 'bool') {\n    return false;\n  }\n  return true;\n}\n\nexport function bytesPerElement(dtype: DataType): number {\n  if (dtype === 'float32' || dtype === 'int32') {\n    return 4;\n  } else if (dtype === 'complex64') {\n    return 8;\n  } else if (dtype === 'bool') {\n    return 1;\n  } else {\n    throw new Error(`Unknown dtype ${dtype}`);\n  }\n}\n\n/**\n * Returns the approximate number of bytes allocated in the string array - 2\n * bytes per character. Computing the exact bytes for a native string in JS\n * is not possible since it depends on the encoding of the html page that\n * serves the website.\n */\nexport function bytesFromStringArray(arr: Uint8Array[]): number {\n  if (arr == null) {\n    return 0;\n  }\n  let bytes = 0;\n  arr.forEach(x => bytes += x.length);\n  return bytes;\n}\n\n/** Returns true if the value is a string. */\nexport function isString(value: {}): value is string {\n  return typeof value === 'string' || value instanceof String;\n}\n\nexport function isBoolean(value: {}): boolean {\n  return typeof value === 'boolean';\n}\n\nexport function isNumber(value: {}): boolean {\n  return typeof value === 'number';\n}\n\nexport function inferDtype(values: TensorLike|WebGLData|WebGPUData): DataType {\n  if (Array.isArray(values)) {\n    return inferDtype(values[0]);\n  }\n  if (values instanceof Float32Array) {\n    return 'float32';\n  } else if (\n      values instanceof Int32Array || values instanceof Uint8Array ||\n      values instanceof Uint8ClampedArray) {\n    return 'int32';\n  } else if (isNumber(values)) {\n    return 'float32';\n  } else if (isString(values)) {\n    return 'string';\n  } else if (isBoolean(values)) {\n    return 'bool';\n  }\n  return 'float32';\n}\n\nexport function isFunction(f: Function) {\n  return !!(f && f.constructor && f.call && f.apply);\n}\n\nexport function nearestDivisor(size: number, start: number): number {\n  for (let i = start; i < size; ++i) {\n    if (size % i === 0) {\n      return i;\n    }\n  }\n  return size;\n}\n\nexport function computeStrides(shape: number[]): number[] {\n  const rank = shape.length;\n  if (rank < 2) {\n    return [];\n  }\n\n  // Last dimension has implicit stride of 1, thus having D-1 (instead of D)\n  // strides.\n  const strides = new Array(rank - 1);\n  strides[rank - 2] = shape[rank - 1];\n  for (let i = rank - 3; i >= 0; --i) {\n    strides[i] = strides[i + 1] * shape[i + 1];\n  }\n  return strides;\n}\n\nfunction createNestedArray(\n    offset: number, shape: number[], a: TypedArray, isComplex = false) {\n  const ret = new Array();\n  if (shape.length === 1) {\n    const d = shape[0] * (isComplex ? 2 : 1);\n    for (let i = 0; i < d; i++) {\n      ret[i] = a[offset + i];\n    }\n  } else {\n    const d = shape[0];\n    const rest = shape.slice(1);\n    const len = rest.reduce((acc, c) => acc * c) * (isComplex ? 2 : 1);\n    for (let i = 0; i < d; i++) {\n      ret[i] = createNestedArray(offset + i * len, rest, a, isComplex);\n    }\n  }\n  return ret;\n}\n\n// Provide a nested array of TypedArray in given shape.\nexport function toNestedArray(\n    shape: number[], a: TypedArray, isComplex = false) {\n  if (shape.length === 0) {\n    // Scalar type should return a single number.\n    return a[0];\n  }\n  const size = shape.reduce((acc, c) => acc * c) * (isComplex ? 2 : 1);\n  if (size === 0) {\n    // A tensor with shape zero should be turned into empty list.\n    return [];\n  }\n  if (size !== a.length) {\n    throw new Error(`[${shape}] does not match the input size ${a.length}${\n        isComplex ? ' for a complex tensor' : ''}.`);\n  }\n\n  return createNestedArray(0, shape, a, isComplex);\n}\n\nexport function convertBackendValuesAndArrayBuffer(\n    data: BackendValues|ArrayBuffer, dtype: DataType) {\n  // If is type Uint8Array[], return it directly.\n  if (Array.isArray(data)) {\n    return data;\n  }\n  if (dtype === 'float32') {\n    return data instanceof Float32Array ? data : new Float32Array(data);\n  } else if (dtype === 'int32') {\n    return data instanceof Int32Array ? data : new Int32Array(data);\n  } else if (dtype === 'bool' || dtype === 'string') {\n    return Uint8Array.from(new Int32Array(data));\n  } else {\n    throw new Error(`Unknown dtype ${dtype}`);\n  }\n}\n\nexport function makeOnesTypedArray<D extends DataType>(\n    size: number, dtype: D): DataTypeMap[D] {\n  const array = makeZerosTypedArray(size, dtype);\n  for (let i = 0; i < array.length; i++) {\n    array[i] = 1;\n  }\n  return array;\n}\n\nexport function makeZerosTypedArray<D extends DataType>(\n    size: number, dtype: D): DataTypeMap[D] {\n  if (dtype == null || dtype === 'float32' || dtype === 'complex64') {\n    return new Float32Array(size) as DataTypeMap[D];\n  } else if (dtype === 'int32') {\n    return new Int32Array(size) as DataTypeMap[D];\n  } else if (dtype === 'bool') {\n    return new Uint8Array(size) as DataTypeMap[D];\n  } else {\n    throw new Error(`Unknown data type ${dtype}`);\n  }\n}\n\n/**\n * Make nested `TypedArray` filled with zeros.\n * @param shape The shape information for the nested array.\n * @param dtype dtype of the array element.\n */\nexport function makeZerosNestedTypedArray<D extends DataType>(\n    shape: number[], dtype: D) {\n  const size = shape.reduce((prev, curr) => prev * curr, 1);\n  if (dtype == null || dtype === 'float32') {\n    return toNestedArray(shape, new Float32Array(size));\n  } else if (dtype === 'int32') {\n    return toNestedArray(shape, new Int32Array(size));\n  } else if (dtype === 'bool') {\n    return toNestedArray(shape, new Uint8Array(size));\n  } else {\n    throw new Error(`Unknown data type ${dtype}`);\n  }\n}\n\nexport function assertNonNegativeIntegerDimensions(shape: number[]) {\n  shape.forEach(dimSize => {\n    assert(\n        Number.isInteger(dimSize) && dimSize >= 0,\n        () =>\n            `Tensor must have a shape comprised of positive integers but got ` +\n            `shape [${shape}].`);\n  });\n}\n\n/**\n * Computes flat index for a given location (multidimentionsal index) in a\n * Tensor/multidimensional array.\n *\n * @param locs Location in the tensor.\n * @param rank Rank of the tensor.\n * @param strides Tensor strides.\n */\nexport function locToIndex(\n    locs: number[], rank: number, strides: number[]): number {\n  if (rank === 0) {\n    return 0;\n  } else if (rank === 1) {\n    return locs[0];\n  }\n  let index = locs[locs.length - 1];\n  for (let i = 0; i < locs.length - 1; ++i) {\n    index += strides[i] * locs[i];\n  }\n  return index;\n}\n\n/**\n * Computes the location (multidimensional index) in a\n * tensor/multidimentional array for a given flat index.\n *\n * @param index Index in flat array.\n * @param rank Rank of tensor.\n * @param strides Strides of tensor.\n */\nexport function indexToLoc(\n    index: number, rank: number, strides: number[]): number[] {\n  if (rank === 0) {\n    return [];\n  } else if (rank === 1) {\n    return [index];\n  }\n  const locs: number[] = new Array(rank);\n  for (let i = 0; i < locs.length - 1; ++i) {\n    locs[i] = Math.floor(index / strides[i]);\n    index -= locs[i] * strides[i];\n  }\n  locs[locs.length - 1] = index;\n  return locs;\n}\n\n/**\n * This method asserts whether an object is a Promise instance.\n * @param object\n */\n// tslint:disable-next-line: no-any\nexport function isPromise(object: any): object is Promise<unknown> {\n  //  We chose to not use 'obj instanceOf Promise' for two reasons:\n  //  1. It only reliably works for es6 Promise, not other Promise\n  //  implementations.\n  //  2. It doesn't work with framework that uses zone.js. zone.js monkey\n  //  patch the async calls, so it is possible the obj (patched) is\n  //  comparing to a pre-patched Promise.\n  return object && object.then && typeof object.then === 'function';\n}\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAmBA;;;;;;;;;;;;;AAaA;AACA,OAAM,SAAUA,OAAOA,CAACC,KACY;EAClC,IAAIC,OAAO,GAAGD,KAAK,CAACE,MAAM;EAC1B,IAAIC,KAAK,GAAG,CAAC;EACb;EACA,OAAOF,OAAO,GAAG,CAAC,EAAE;IAClB;IACAE,KAAK,GAAIC,IAAI,CAACC,MAAM,EAAE,GAAGJ,OAAO,GAAI,CAAC;IACrC;IACAA,OAAO,EAAE;IACT;IACAK,IAAI,CAACN,KAAK,EAAEC,OAAO,EAAEE,KAAK,CAAC;;AAE/B;AAEA;;;;;;;;;;;;;;;;AAgBA,OAAM,SAAUI,YAAYA;AACxB;AACAP,KAAgD;AAChD;AACAQ,MAAiD;EACnD,IAAIR,KAAK,CAACE,MAAM,KAAKM,MAAM,CAACN,MAAM,EAAE;IAClC,MAAM,IAAIO,KAAK,CACX,8EAAAC,MAAA,CAC0BV,KAAK,CAACE,MAAM,CAAE,8BAAAQ,MAAA,CACbF,MAAM,CAACN,MAAM,CAAE,CAAC;;EAEjD,IAAID,OAAO,GAAGD,KAAK,CAACE,MAAM;EAC1B,IAAIC,KAAK,GAAG,CAAC;EACb;EACA,OAAOF,OAAO,GAAG,CAAC,EAAE;IAClB;IACAE,KAAK,GAAIC,IAAI,CAACC,MAAM,EAAE,GAAGJ,OAAO,GAAI,CAAC;IACrC;IACAA,OAAO,EAAE;IACT;IACAK,IAAI,CAACN,KAAK,EAAEC,OAAO,EAAEE,KAAK,CAAC;IAC3BG,IAAI,CAACE,MAAM,EAAEP,OAAO,EAAEE,KAAK,CAAC;;AAEhC;AAEA;AACA,OAAM,SAAUQ,KAAKA,CAACC,GAAW,EAAEC,CAAS,EAAEC,GAAW;EACvD,OAAOV,IAAI,CAACU,GAAG,CAACF,GAAG,EAAER,IAAI,CAACQ,GAAG,CAACC,CAAC,EAAEC,GAAG,CAAC,CAAC;AACxC;AAEA,OAAM,SAAUC,iBAAiBA,CAACC,GAAW;EAC3C,OAAOA,GAAG,GAAG,CAAC,KAAK,CAAC,GAAGA,GAAG,GAAGA,GAAG,GAAG,CAAC;AACtC;AAEA,OAAM,SAAUV,IAAIA,CAChBW,MAA4B,EAAEC,IAAY,EAAEC,KAAa;EAC3D,MAAMC,IAAI,GAAGH,MAAM,CAACC,IAAI,CAAC;EACzBD,MAAM,CAACC,IAAI,CAAC,GAAGD,MAAM,CAACE,KAAK,CAAC;EAC5BF,MAAM,CAACE,KAAK,CAAC,GAAGC,IAAI;AACtB;AAEA,OAAM,SAAUC,GAAGA,CAACC,GAAa;EAC/B,IAAID,GAAG,GAAG,CAAC;EACX,KAAK,IAAIE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,GAAG,CAACpB,MAAM,EAAEqB,CAAC,EAAE,EAAE;IACnCF,GAAG,IAAIC,GAAG,CAACC,CAAC,CAAC;;EAEf,OAAOF,GAAG;AACZ;AAEA;;;;;;;AAOA,OAAM,SAAUG,WAAWA,CAACC,CAAS,EAAEC,CAAS;EAC9C,MAAMC,CAAC,GAAGvB,IAAI,CAACC,MAAM,EAAE;EACvB,OAAQqB,CAAC,GAAGC,CAAC,GAAI,CAAC,CAAC,GAAGA,CAAC,IAAIF,CAAC;AAC9B;AAEA;AACA,OAAM,SAAUG,WAAWA,CAACH,CAAa,EAAEC,CAAa;EACtD,IAAIG,MAAM,GAAG,CAAC;EACd,KAAK,IAAIN,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGE,CAAC,CAACvB,MAAM,EAAEqB,CAAC,EAAE,EAAE;IACjC,MAAMO,IAAI,GAAGC,MAAM,CAACN,CAAC,CAACF,CAAC,CAAC,CAAC,GAAGQ,MAAM,CAACL,CAAC,CAACH,CAAC,CAAC,CAAC;IACxCM,MAAM,IAAIC,IAAI,GAAGA,IAAI;;EAEvB,OAAOD,MAAM;AACf;AAEA;;;;;;;;;;;;;;;AAeA,OAAM,SAAUG,MAAMA,CAACC,IAAa,EAAEC,GAAiB;EACrD,IAAI,CAACD,IAAI,EAAE;IACT,MAAM,IAAIxB,KAAK,CAAC,OAAOyB,GAAG,KAAK,QAAQ,GAAGA,GAAG,GAAGA,GAAG,EAAE,CAAC;;AAE1D;AAEA,OAAM,SAAUC,iBAAiBA,CAC7BC,MAAgB,EAAEC,MAAgB,EAAyB;EAAA,IAAvBC,kBAAkB,GAAAC,SAAA,CAAArC,MAAA,QAAAqC,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAG,EAAE;EAC7DP,MAAM,CACFS,WAAW,CAACL,MAAM,EAAEC,MAAM,CAAC,EAC3B,MAAMC,kBAAkB,cAAA5B,MAAA,CAAc0B,MAAM,WAAA1B,MAAA,CAAQ2B,MAAM,gBAAa,CAAC;AAC9E;AAEA,OAAM,SAAUK,aAAaA,CAACjB,CAAa;EACzCO,MAAM,CACFP,CAAC,IAAI,IAAI,EACT,qEAAqE,CAAC;AAC5E;AAEA;;;;;;;;;;;AAWA,OAAM,SAAUkB,aAAaA,CAACC,KAAe;EAC3C,IAAIA,KAAK,CAAC1C,MAAM,KAAK,CAAC,EAAE;IACtB;IACA,OAAO,CAAC;;EAEV,IAAI2C,IAAI,GAAGD,KAAK,CAAC,CAAC,CAAC;EACnB,KAAK,IAAIrB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGqB,KAAK,CAAC1C,MAAM,EAAEqB,CAAC,EAAE,EAAE;IACrCsB,IAAI,IAAID,KAAK,CAACrB,CAAC,CAAC;;EAElB,OAAOsB,IAAI;AACb;AAEA,OAAM,SAAUC,aAAaA,CAACF,KAAe;EAC3C,OAAOA,KAAK,CAAC1C,MAAM,KAAK,CAAC;AAC3B;AAEA,OAAM,SAAU6C,mBAAmBA,CAACC,EAAY,EAAEC,EAAY;EAC5D,IAAID,EAAE,KAAKC,EAAE,EAAE;IACb,OAAO,IAAI;;EAGb,IAAID,EAAE,IAAI,IAAI,IAAIC,EAAE,IAAI,IAAI,EAAE;IAC5B,OAAO,KAAK;;EAGd,IAAID,EAAE,CAAC9C,MAAM,KAAK+C,EAAE,CAAC/C,MAAM,EAAE;IAC3B,OAAO,KAAK;;EAGd,KAAK,IAAIqB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGyB,EAAE,CAAC9C,MAAM,EAAEqB,CAAC,EAAE,EAAE;IAClC,IAAIyB,EAAE,CAACzB,CAAC,CAAC,KAAK,IAAI,IAAI0B,EAAE,CAAC1B,CAAC,CAAC,KAAK,IAAI,IAAIyB,EAAE,CAACzB,CAAC,CAAC,KAAK0B,EAAE,CAAC1B,CAAC,CAAC,EAAE;MACvD,OAAO,KAAK;;;EAGhB,OAAO,IAAI;AACb;AAEA,OAAM,SAAUkB,WAAWA,CAACO,EAAc,EAAEC,EAAc;EACxD,IAAID,EAAE,KAAKC,EAAE,EAAE;IACb,OAAO,IAAI;;EAEb,IAAID,EAAE,IAAI,IAAI,IAAIC,EAAE,IAAI,IAAI,EAAE;IAC5B,OAAO,KAAK;;EAGd,IAAID,EAAE,CAAC9C,MAAM,KAAK+C,EAAE,CAAC/C,MAAM,EAAE;IAC3B,OAAO,KAAK;;EAEd,KAAK,IAAIqB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGyB,EAAE,CAAC9C,MAAM,EAAEqB,CAAC,EAAE,EAAE;IAClC,IAAIyB,EAAE,CAACzB,CAAC,CAAC,KAAK0B,EAAE,CAAC1B,CAAC,CAAC,EAAE;MACnB,OAAO,KAAK;;;EAGhB,OAAO,IAAI;AACb;AAEA,OAAM,SAAU2B,KAAKA,CAACzB,CAAS;EAC7B,OAAOA,CAAC,GAAG,CAAC,KAAK,CAAC;AACpB;AAEA,OAAM,SAAU0B,IAAIA,CAACtC,CAAS;EAC5B;EACA,IAAKT,IAAY,CAAC+C,IAAI,IAAI,IAAI,EAAE;IAC9B;IACA,OAAQ/C,IAAY,CAAC+C,IAAI,CAACtC,CAAC,CAAC;;EAE9B,IAAIA,CAAC,KAAKuC,QAAQ,EAAE;IAClB,OAAO,CAAC;GACT,MAAM,IAAIvC,CAAC,KAAK,CAACuC,QAAQ,EAAE;IAC1B,OAAO,CAAC,CAAC;GACV,MAAM;IACL,MAAMC,GAAG,GAAGjD,IAAI,CAACkD,GAAG,CAAC,CAAC,GAAGzC,CAAC,CAAC;IAC3B,OAAO,CAACwC,GAAG,GAAG,CAAC,KAAKA,GAAG,GAAG,CAAC,CAAC;;AAEhC;AAEA,OAAM,SAAUE,mBAAmBA,CAACV,IAAY;EAC9C,MAAMW,KAAK,GAAGpD,IAAI,CAACqD,IAAI,CAACrD,IAAI,CAACsD,IAAI,CAACb,IAAI,CAAC,CAAC;EACxC,OAAO,CAACW,KAAK,EAAEpD,IAAI,CAACqD,IAAI,CAACZ,IAAI,GAAGW,KAAK,CAAC,CAAC;AACzC;AAEA;;;;;;;;;;;;AAYA,OAAM,SAAUG,qBAAqBA,CAACC,CAAS;EAC7C,MAAMC,eAAe,GAAG,IAAIC,WAAW,CAACF,CAAC,CAAC;EAC1C,KAAK,IAAIrC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGqC,CAAC,EAAE,EAAErC,CAAC,EAAE;IAC1BsC,eAAe,CAACtC,CAAC,CAAC,GAAGA,CAAC;;EAExBxB,OAAO,CAAC8D,eAAe,CAAC;EACxB,OAAOA,eAAe;AACxB;AAEA,OAAM,SAAUE,QAAQA,CAACtC,CAAS,EAAEoB,IAAY;EAC9C,IAAIA,IAAI,IAAIpB,CAAC,CAACvB,MAAM,EAAE;IACpB,OAAOuB,CAAC;;EAEV,OAAOA,CAAC,GAAG,GAAG,CAACuC,MAAM,CAACnB,IAAI,GAAGpB,CAAC,CAACvB,MAAM,CAAC;AACxC;AAEA,OAAM,SAAU+D,WAAWA,CACvBC,OAAsB,EAGd;EAAA,IAHgBC,OAAA,GAAA5B,SAAA,CAAArC,MAAA,QAAAqC,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAWtC,OAAe,IAAK,CAAC;EAAA,IACxDmE,UAAmB,GAAA7B,SAAA,CAAArC,MAAA,OAAAqC,SAAA,MAAAC,SAAA;EAAA,IACnB6B,UACQ,GAAA9B,SAAA,CAAArC,MAAA,OAAAqC,SAAA,MAAAC,SAAA;EACV,OAAO,IAAI8B,OAAO,CAAO,CAACC,OAAO,EAAEC,MAAM,KAAI;IAC3C,IAAIC,QAAQ,GAAG,CAAC;IAEhB,MAAMC,KAAK,GAAGA,CAAA,KAAK;MACjB,IAAIR,OAAO,EAAE,EAAE;QACbK,OAAO,EAAE;QACT;;MAGFE,QAAQ,EAAE;MAEV,MAAME,WAAW,GAAGR,OAAO,CAACM,QAAQ,CAAC;MAErC,IAAIL,UAAU,IAAI,IAAI,IAAIK,QAAQ,IAAIL,UAAU,EAAE;QAChDI,MAAM,EAAE;QACR;;MAGF,IAAIH,UAAU,IAAI,IAAI,EAAE;QACtBA,UAAU,CAACK,KAAK,EAAEC,WAAW,CAAC;OAC/B,MAAM;QACL;QACA;QACAC,UAAU,CAACF,KAAK,EAAEC,WAAW,CAAC;;IAElC,CAAC;IAEDD,KAAK,EAAE;EACT,CAAC,CAAC;AACJ;AAEA;;;;;;;;;AASA,OAAM,SAAUG,sBAAsBA,CAClCjC,KAAe,EAAEC,IAAY;EAC/B,IAAIiC,SAAS,GAAG,CAAC;EACjB,IAAIC,WAAW,GAAG,CAAC,CAAC;EAEpB,KAAK,IAAIxD,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGqB,KAAK,CAAC1C,MAAM,EAAE,EAAEqB,CAAC,EAAE;IACrC,IAAIqB,KAAK,CAACrB,CAAC,CAAC,IAAI,CAAC,EAAE;MACjBuD,SAAS,IAAIlC,KAAK,CAACrB,CAAC,CAAC;KACtB,MAAM,IAAIqB,KAAK,CAACrB,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE;MAC1B,IAAIwD,WAAW,KAAK,CAAC,CAAC,EAAE;QACtB,MAAMtE,KAAK,CACP,8DAAAC,MAAA,CACmBqE,WAAW,eAAArE,MAAA,CAAYa,CAAC,CAAE,CAAC;;MAEpDwD,WAAW,GAAGxD,CAAC;KAChB,MAAM,IAAIqB,KAAK,CAACrB,CAAC,CAAC,GAAG,CAAC,EAAE;MACvB,MAAMd,KAAK,iCAAAC,MAAA,CAAiCkC,KAAK,CAACrB,CAAC,CAAC,cAAAb,MAAA,CAAWa,CAAC,CAAE,CAAC;;;EAIvE,IAAIwD,WAAW,KAAK,CAAC,CAAC,EAAE;IACtB,IAAIlC,IAAI,GAAG,CAAC,IAAIA,IAAI,KAAKiC,SAAS,EAAE;MAClC,MAAMrE,KAAK,SAAAC,MAAA,CAASmC,IAAI,wCAAAnC,MAAA,CAAqCkC,KAAK,CAAE,CAAC;;IAEvE,OAAOA,KAAK;;EAGd,IAAIkC,SAAS,KAAK,CAAC,EAAE;IACnB,MAAMrE,KAAK,CACP,qCAAAC,MAAA,CAAqCkC,KAAK,qCACpB,CAAC;;EAE7B,IAAIC,IAAI,GAAGiC,SAAS,KAAK,CAAC,EAAE;IAC1B,MAAMrE,KAAK,CACP,6DAAAC,MAAA,CACOmC,IAAI,SAAAnC,MAAA,CAAMoE,SAAS,CAAE,CAAC;;EAGnC,MAAME,QAAQ,GAAGpC,KAAK,CAACqC,KAAK,EAAE;EAC9BD,QAAQ,CAACD,WAAW,CAAC,GAAGlC,IAAI,GAAGiC,SAAS;EACxC,OAAOE,QAAQ;AACjB;AAEA,OAAM,SAAUE,cAAcA,CAC1BC,IAAqB,EAAEvC,KAAe;EACxC,MAAMwC,IAAI,GAAGxC,KAAK,CAAC1C,MAAM;EAEzB;EACAiF,IAAI,GAAGA,IAAI,IAAI,IAAI,GAAGvC,KAAK,CAACyC,GAAG,CAAC,CAACC,CAAC,EAAE/D,CAAC,KAAKA,CAAC,CAAC,GAAG,EAAE,CAACb,MAAM,CAACyE,IAAI,CAAC;EAE9D;EACAnD,MAAM,CACFmD,IAAI,CAACI,KAAK,CAACC,EAAE,IAAIA,EAAE,IAAI,CAACJ,IAAI,IAAII,EAAE,GAAGJ,IAAI,CAAC,EAC1C,MACI,+CAAA1E,MAAA,CAA+C0E,IAAI,QAAA1E,MAAA,CAAK0E,IAAI,0BAAA1E,MAAA,CAChDyE,IAAI,CAAE,CAAC;EAE3B;EACAnD,MAAM,CACFmD,IAAI,CAACI,KAAK,CAACC,EAAE,IAAItC,KAAK,CAACsC,EAAE,CAAC,CAAC,EAC3B,MAAM,+DAAA9E,MAAA,CACUyE,IAAI,CAAE,CAAC;EAE3B;EACA,OAAOA,IAAI,CAACE,GAAG,CAAC5D,CAAC,IAAIA,CAAC,GAAG,CAAC,GAAG2D,IAAI,GAAG3D,CAAC,GAAGA,CAAC,CAAC;AAC5C;AAEA;AACA,OAAM,SAAUgE,YAAYA,CAAC7C,KAAe,EAAEuC,IAAe;EAE3D,MAAMH,QAAQ,GAAa,EAAE;EAC7B,MAAMU,QAAQ,GAAa,EAAE;EAC7B,MAAMC,YAAY,GAAGR,IAAI,IAAI,IAAI,IAAIS,KAAK,CAACC,OAAO,CAACV,IAAI,CAAC,IAAIA,IAAI,CAACjF,MAAM,KAAK,CAAC;EAC7E,MAAM4F,IAAI,GAAIX,IAAI,IAAI,IAAI,IAAIQ,YAAY,GACtC,IAAI,GACJT,cAAc,CAACC,IAAI,EAAEvC,KAAK,CAAC,CAACmD,IAAI,EAAE;EACtC,IAAIC,CAAC,GAAG,CAAC;EACT,KAAK,IAAIzE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGqB,KAAK,CAAC1C,MAAM,EAAE,EAAEqB,CAAC,EAAE;IACrC,IAAIuE,IAAI,IAAI,IAAI,EAAE;MAChB,IAAIA,IAAI,CAACE,CAAC,CAAC,KAAKzE,CAAC,IAAIqB,KAAK,CAACrB,CAAC,CAAC,KAAK,CAAC,EAAE;QACnC,MAAM,IAAId,KAAK,uBAAAC,MAAA,CACWa,CAAC,sBAAAb,MAAA,CAAmBkC,KAAK,CAACrB,CAAC,CAAC,eAAY,CAAC;;MAErE,IAAI,CAACuE,IAAI,CAACE,CAAC,CAAC,IAAI,IAAI,IAAIF,IAAI,CAACE,CAAC,CAAC,GAAGzE,CAAC,KAAKqB,KAAK,CAACrB,CAAC,CAAC,KAAK,CAAC,EAAE;QACtDyD,QAAQ,CAACiB,IAAI,CAACrD,KAAK,CAACrB,CAAC,CAAC,CAAC;QACvBmE,QAAQ,CAACO,IAAI,CAAC1E,CAAC,CAAC;;MAElB,IAAIuE,IAAI,CAACE,CAAC,CAAC,IAAIzE,CAAC,EAAE;QAChByE,CAAC,EAAE;;;IAGP,IAAIpD,KAAK,CAACrB,CAAC,CAAC,KAAK,CAAC,EAAE;MAClByD,QAAQ,CAACiB,IAAI,CAACrD,KAAK,CAACrB,CAAC,CAAC,CAAC;MACvBmE,QAAQ,CAACO,IAAI,CAAC1E,CAAC,CAAC;;;EAGpB,OAAO;IAACyD,QAAQ;IAAEU;EAAQ,CAAC;AAC7B;AAEA,OAAM,SAAUQ,sBAAsBA,CAClCC,KAAQ,EAAEtD,IAAY;EACxB,OAAOuD,iBAAiB,CAAID,KAAK,EAAEtD,IAAI,CAAC;AAC1C;AAEA,OAAM,SAAUuD,iBAAiBA,CAC7BD,KAAQ,EAAEtD,IAAY;EACxB,IAAIwD,MAAM,GAAG,IAAI;EACjB,IAAIF,KAAK,IAAI,IAAI,IAAIA,KAAK,KAAK,SAAS,EAAE;IACxCE,MAAM,GAAG,IAAIC,YAAY,CAACzD,IAAI,CAAC;GAChC,MAAM,IAAIsD,KAAK,KAAK,OAAO,EAAE;IAC5BE,MAAM,GAAG,IAAIE,UAAU,CAAC1D,IAAI,CAAC;GAC9B,MAAM,IAAIsD,KAAK,KAAK,MAAM,EAAE;IAC3BE,MAAM,GAAG,IAAIG,UAAU,CAAC3D,IAAI,CAAC;GAC9B,MAAM,IAAIsD,KAAK,KAAK,QAAQ,EAAE;IAC7BE,MAAM,GAAG,IAAIT,KAAK,CAAS/C,IAAI,CAAC;GACjC,MAAM;IACL,MAAM,IAAIpC,KAAK,sBAAAC,MAAA,CAAsByF,KAAK,CAAE,CAAC;;EAE/C,OAAOE,MAAwB;AACjC;AAEA,OAAM,SAAUI,wBAAwBA,CACpCC,IAA6B,EAAEP,KAAQ;EACzC,KAAK,IAAI5E,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGmF,IAAI,CAACxG,MAAM,EAAEqB,CAAC,EAAE,EAAE;IACpC,MAAMoF,GAAG,GAAGD,IAAI,CAACnF,CAAC,CAAW;IAC7B,IAAIqF,KAAK,CAACD,GAAG,CAAC,IAAI,CAACE,QAAQ,CAACF,GAAG,CAAC,EAAE;MAChC,MAAMlG,KAAK,qBAAAC,MAAA,CAAqByF,KAAK,+BAAAzF,MAAA,CAA4BiG,GAAG,MAAG,CAAC;;;AAG9E;AAEA;AACA,OAAM,SAAUG,YAAYA,CAACX,KAAe;EAC1C,OAAOA,KAAK,KAAK,MAAM,IAAIA,KAAK,KAAK,WAAW,IAAIA,KAAK,KAAK,SAAS,IACnEA,KAAK,KAAK,OAAO,IAAIA,KAAK,KAAK,QAAQ;AAC7C;AAEA;;;;AAIA,OAAM,SAAUY,eAAeA,CAACC,OAAiB,EAAEC,OAAiB;EAClE,IAAIA,OAAO,KAAK,WAAW,EAAE;IAC3B,OAAO,KAAK;;EAEd,IAAIA,OAAO,KAAK,SAAS,IAAID,OAAO,KAAK,WAAW,EAAE;IACpD,OAAO,KAAK;;EAEd,IAAIC,OAAO,KAAK,OAAO,IAAID,OAAO,KAAK,SAAS,IAAIA,OAAO,KAAK,WAAW,EAAE;IAC3E,OAAO,KAAK;;EAEd,IAAIC,OAAO,KAAK,MAAM,IAAID,OAAO,KAAK,MAAM,EAAE;IAC5C,OAAO,KAAK;;EAEd,OAAO,IAAI;AACb;AAEA,OAAM,SAAUE,eAAeA,CAACf,KAAe;EAC7C,IAAIA,KAAK,KAAK,SAAS,IAAIA,KAAK,KAAK,OAAO,EAAE;IAC5C,OAAO,CAAC;GACT,MAAM,IAAIA,KAAK,KAAK,WAAW,EAAE;IAChC,OAAO,CAAC;GACT,MAAM,IAAIA,KAAK,KAAK,MAAM,EAAE;IAC3B,OAAO,CAAC;GACT,MAAM;IACL,MAAM,IAAI1F,KAAK,kBAAAC,MAAA,CAAkByF,KAAK,CAAE,CAAC;;AAE7C;AAEA;;;;;;AAMA,OAAM,SAAUgB,oBAAoBA,CAAC7F,GAAiB;EACpD,IAAIA,GAAG,IAAI,IAAI,EAAE;IACf,OAAO,CAAC;;EAEV,IAAI8F,KAAK,GAAG,CAAC;EACb9F,GAAG,CAAC+F,OAAO,CAACxG,CAAC,IAAIuG,KAAK,IAAIvG,CAAC,CAACX,MAAM,CAAC;EACnC,OAAOkH,KAAK;AACd;AAEA;AACA,OAAM,SAAUE,QAAQA,CAACC,KAAS;EAChC,OAAO,OAAOA,KAAK,KAAK,QAAQ,IAAIA,KAAK,YAAYC,MAAM;AAC7D;AAEA,OAAM,SAAUC,SAASA,CAACF,KAAS;EACjC,OAAO,OAAOA,KAAK,KAAK,SAAS;AACnC;AAEA,OAAM,SAAUG,QAAQA,CAACH,KAAS;EAChC,OAAO,OAAOA,KAAK,KAAK,QAAQ;AAClC;AAEA,OAAM,SAAUI,UAAUA,CAACtB,MAAuC;EAChE,IAAIT,KAAK,CAACC,OAAO,CAACQ,MAAM,CAAC,EAAE;IACzB,OAAOsB,UAAU,CAACtB,MAAM,CAAC,CAAC,CAAC,CAAC;;EAE9B,IAAIA,MAAM,YAAYC,YAAY,EAAE;IAClC,OAAO,SAAS;GACjB,MAAM,IACHD,MAAM,YAAYE,UAAU,IAAIF,MAAM,YAAYG,UAAU,IAC5DH,MAAM,YAAYuB,iBAAiB,EAAE;IACvC,OAAO,OAAO;GACf,MAAM,IAAIF,QAAQ,CAACrB,MAAM,CAAC,EAAE;IAC3B,OAAO,SAAS;GACjB,MAAM,IAAIiB,QAAQ,CAACjB,MAAM,CAAC,EAAE;IAC3B,OAAO,QAAQ;GAChB,MAAM,IAAIoB,SAAS,CAACpB,MAAM,CAAC,EAAE;IAC5B,OAAO,MAAM;;EAEf,OAAO,SAAS;AAClB;AAEA,OAAM,SAAUwB,UAAUA,CAACC,CAAW;EACpC,OAAO,CAAC,EAAEA,CAAC,IAAIA,CAAC,CAACC,WAAW,IAAID,CAAC,CAACE,IAAI,IAAIF,CAAC,CAACG,KAAK,CAAC;AACpD;AAEA,OAAM,SAAUC,cAAcA,CAACrF,IAAY,EAAEsF,KAAa;EACxD,KAAK,IAAI5G,CAAC,GAAG4G,KAAK,EAAE5G,CAAC,GAAGsB,IAAI,EAAE,EAAEtB,CAAC,EAAE;IACjC,IAAIsB,IAAI,GAAGtB,CAAC,KAAK,CAAC,EAAE;MAClB,OAAOA,CAAC;;;EAGZ,OAAOsB,IAAI;AACb;AAEA,OAAM,SAAUuF,cAAcA,CAACxF,KAAe;EAC5C,MAAMwC,IAAI,GAAGxC,KAAK,CAAC1C,MAAM;EACzB,IAAIkF,IAAI,GAAG,CAAC,EAAE;IACZ,OAAO,EAAE;;EAGX;EACA;EACA,MAAMiD,OAAO,GAAG,IAAIzC,KAAK,CAACR,IAAI,GAAG,CAAC,CAAC;EACnCiD,OAAO,CAACjD,IAAI,GAAG,CAAC,CAAC,GAAGxC,KAAK,CAACwC,IAAI,GAAG,CAAC,CAAC;EACnC,KAAK,IAAI7D,CAAC,GAAG6D,IAAI,GAAG,CAAC,EAAE7D,CAAC,IAAI,CAAC,EAAE,EAAEA,CAAC,EAAE;IAClC8G,OAAO,CAAC9G,CAAC,CAAC,GAAG8G,OAAO,CAAC9G,CAAC,GAAG,CAAC,CAAC,GAAGqB,KAAK,CAACrB,CAAC,GAAG,CAAC,CAAC;;EAE5C,OAAO8G,OAAO;AAChB;AAEA,SAASC,iBAAiBA,CACtBC,MAAc,EAAE3F,KAAe,EAAEnB,CAAa,EAAmB;EAAA,IAAjB+G,SAAS,GAAAjG,SAAA,CAAArC,MAAA,QAAAqC,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAG,KAAK;EACnE,MAAMkG,GAAG,GAAG,IAAI7C,KAAK,EAAE;EACvB,IAAIhD,KAAK,CAAC1C,MAAM,KAAK,CAAC,EAAE;IACtB,MAAMwI,CAAC,GAAG9F,KAAK,CAAC,CAAC,CAAC,IAAI4F,SAAS,GAAG,CAAC,GAAG,CAAC,CAAC;IACxC,KAAK,IAAIjH,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGmH,CAAC,EAAEnH,CAAC,EAAE,EAAE;MAC1BkH,GAAG,CAAClH,CAAC,CAAC,GAAGE,CAAC,CAAC8G,MAAM,GAAGhH,CAAC,CAAC;;GAEzB,MAAM;IACL,MAAMmH,CAAC,GAAG9F,KAAK,CAAC,CAAC,CAAC;IAClB,MAAM+F,IAAI,GAAG/F,KAAK,CAACqC,KAAK,CAAC,CAAC,CAAC;IAC3B,MAAM2D,GAAG,GAAGD,IAAI,CAACE,MAAM,CAAC,CAACC,GAAG,EAAEC,CAAC,KAAKD,GAAG,GAAGC,CAAC,CAAC,IAAIP,SAAS,GAAG,CAAC,GAAG,CAAC,CAAC;IAClE,KAAK,IAAIjH,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGmH,CAAC,EAAEnH,CAAC,EAAE,EAAE;MAC1BkH,GAAG,CAAClH,CAAC,CAAC,GAAG+G,iBAAiB,CAACC,MAAM,GAAGhH,CAAC,GAAGqH,GAAG,EAAED,IAAI,EAAElH,CAAC,EAAE+G,SAAS,CAAC;;;EAGpE,OAAOC,GAAG;AACZ;AAEA;AACA,OAAM,SAAUO,aAAaA,CACzBpG,KAAe,EAAEnB,CAAa,EAAmB;EAAA,IAAjB+G,SAAS,GAAAjG,SAAA,CAAArC,MAAA,QAAAqC,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAG,KAAK;EACnD,IAAIK,KAAK,CAAC1C,MAAM,KAAK,CAAC,EAAE;IACtB;IACA,OAAOuB,CAAC,CAAC,CAAC,CAAC;;EAEb,MAAMoB,IAAI,GAAGD,KAAK,CAACiG,MAAM,CAAC,CAACC,GAAG,EAAEC,CAAC,KAAKD,GAAG,GAAGC,CAAC,CAAC,IAAIP,SAAS,GAAG,CAAC,GAAG,CAAC,CAAC;EACpE,IAAI3F,IAAI,KAAK,CAAC,EAAE;IACd;IACA,OAAO,EAAE;;EAEX,IAAIA,IAAI,KAAKpB,CAAC,CAACvB,MAAM,EAAE;IACrB,MAAM,IAAIO,KAAK,KAAAC,MAAA,CAAKkC,KAAK,sCAAAlC,MAAA,CAAmCe,CAAC,CAACvB,MAAM,EAAAQ,MAAA,CAChE8H,SAAS,GAAG,uBAAuB,GAAG,EAAE,MAAG,CAAC;;EAGlD,OAAOF,iBAAiB,CAAC,CAAC,EAAE1F,KAAK,EAAEnB,CAAC,EAAE+G,SAAS,CAAC;AAClD;AAEA,OAAM,SAAUS,kCAAkCA,CAC9CC,IAA+B,EAAE/C,KAAe;EAClD;EACA,IAAIP,KAAK,CAACC,OAAO,CAACqD,IAAI,CAAC,EAAE;IACvB,OAAOA,IAAI;;EAEb,IAAI/C,KAAK,KAAK,SAAS,EAAE;IACvB,OAAO+C,IAAI,YAAY5C,YAAY,GAAG4C,IAAI,GAAG,IAAI5C,YAAY,CAAC4C,IAAI,CAAC;GACpE,MAAM,IAAI/C,KAAK,KAAK,OAAO,EAAE;IAC5B,OAAO+C,IAAI,YAAY3C,UAAU,GAAG2C,IAAI,GAAG,IAAI3C,UAAU,CAAC2C,IAAI,CAAC;GAChE,MAAM,IAAI/C,KAAK,KAAK,MAAM,IAAIA,KAAK,KAAK,QAAQ,EAAE;IACjD,OAAOK,UAAU,CAAC2C,IAAI,CAAC,IAAI5C,UAAU,CAAC2C,IAAI,CAAC,CAAC;GAC7C,MAAM;IACL,MAAM,IAAIzI,KAAK,kBAAAC,MAAA,CAAkByF,KAAK,CAAE,CAAC;;AAE7C;AAEA,OAAM,SAAUiD,kBAAkBA,CAC9BvG,IAAY,EAAEsD,KAAQ;EACxB,MAAMnG,KAAK,GAAGqJ,mBAAmB,CAACxG,IAAI,EAAEsD,KAAK,CAAC;EAC9C,KAAK,IAAI5E,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGvB,KAAK,CAACE,MAAM,EAAEqB,CAAC,EAAE,EAAE;IACrCvB,KAAK,CAACuB,CAAC,CAAC,GAAG,CAAC;;EAEd,OAAOvB,KAAK;AACd;AAEA,OAAM,SAAUqJ,mBAAmBA,CAC/BxG,IAAY,EAAEsD,KAAQ;EACxB,IAAIA,KAAK,IAAI,IAAI,IAAIA,KAAK,KAAK,SAAS,IAAIA,KAAK,KAAK,WAAW,EAAE;IACjE,OAAO,IAAIG,YAAY,CAACzD,IAAI,CAAmB;GAChD,MAAM,IAAIsD,KAAK,KAAK,OAAO,EAAE;IAC5B,OAAO,IAAII,UAAU,CAAC1D,IAAI,CAAmB;GAC9C,MAAM,IAAIsD,KAAK,KAAK,MAAM,EAAE;IAC3B,OAAO,IAAIK,UAAU,CAAC3D,IAAI,CAAmB;GAC9C,MAAM;IACL,MAAM,IAAIpC,KAAK,sBAAAC,MAAA,CAAsByF,KAAK,CAAE,CAAC;;AAEjD;AAEA;;;;;AAKA,OAAM,SAAUmD,yBAAyBA,CACrC1G,KAAe,EAAEuD,KAAQ;EAC3B,MAAMtD,IAAI,GAAGD,KAAK,CAACiG,MAAM,CAAC,CAACU,IAAI,EAAEC,IAAI,KAAKD,IAAI,GAAGC,IAAI,EAAE,CAAC,CAAC;EACzD,IAAIrD,KAAK,IAAI,IAAI,IAAIA,KAAK,KAAK,SAAS,EAAE;IACxC,OAAO6C,aAAa,CAACpG,KAAK,EAAE,IAAI0D,YAAY,CAACzD,IAAI,CAAC,CAAC;GACpD,MAAM,IAAIsD,KAAK,KAAK,OAAO,EAAE;IAC5B,OAAO6C,aAAa,CAACpG,KAAK,EAAE,IAAI2D,UAAU,CAAC1D,IAAI,CAAC,CAAC;GAClD,MAAM,IAAIsD,KAAK,KAAK,MAAM,EAAE;IAC3B,OAAO6C,aAAa,CAACpG,KAAK,EAAE,IAAI4D,UAAU,CAAC3D,IAAI,CAAC,CAAC;GAClD,MAAM;IACL,MAAM,IAAIpC,KAAK,sBAAAC,MAAA,CAAsByF,KAAK,CAAE,CAAC;;AAEjD;AAEA,OAAM,SAAUsD,kCAAkCA,CAAC7G,KAAe;EAChEA,KAAK,CAACyE,OAAO,CAACqC,OAAO,IAAG;IACtB1H,MAAM,CACFD,MAAM,CAAC4H,SAAS,CAACD,OAAO,CAAC,IAAIA,OAAO,IAAI,CAAC,EACzC,MACI,+EAAAhJ,MAAA,CACUkC,KAAK,OAAI,CAAC;EAC9B,CAAC,CAAC;AACJ;AAEA;;;;;;;;AAQA,OAAM,SAAUgH,UAAUA,CACtBC,IAAc,EAAEzE,IAAY,EAAEiD,OAAiB;EACjD,IAAIjD,IAAI,KAAK,CAAC,EAAE;IACd,OAAO,CAAC;GACT,MAAM,IAAIA,IAAI,KAAK,CAAC,EAAE;IACrB,OAAOyE,IAAI,CAAC,CAAC,CAAC;;EAEhB,IAAI1J,KAAK,GAAG0J,IAAI,CAACA,IAAI,CAAC3J,MAAM,GAAG,CAAC,CAAC;EACjC,KAAK,IAAIqB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGsI,IAAI,CAAC3J,MAAM,GAAG,CAAC,EAAE,EAAEqB,CAAC,EAAE;IACxCpB,KAAK,IAAIkI,OAAO,CAAC9G,CAAC,CAAC,GAAGsI,IAAI,CAACtI,CAAC,CAAC;;EAE/B,OAAOpB,KAAK;AACd;AAEA;;;;;;;;AAQA,OAAM,SAAU2J,UAAUA,CACtB3J,KAAa,EAAEiF,IAAY,EAAEiD,OAAiB;EAChD,IAAIjD,IAAI,KAAK,CAAC,EAAE;IACd,OAAO,EAAE;GACV,MAAM,IAAIA,IAAI,KAAK,CAAC,EAAE;IACrB,OAAO,CAACjF,KAAK,CAAC;;EAEhB,MAAM0J,IAAI,GAAa,IAAIjE,KAAK,CAACR,IAAI,CAAC;EACtC,KAAK,IAAI7D,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGsI,IAAI,CAAC3J,MAAM,GAAG,CAAC,EAAE,EAAEqB,CAAC,EAAE;IACxCsI,IAAI,CAACtI,CAAC,CAAC,GAAGnB,IAAI,CAAC2J,KAAK,CAAC5J,KAAK,GAAGkI,OAAO,CAAC9G,CAAC,CAAC,CAAC;IACxCpB,KAAK,IAAI0J,IAAI,CAACtI,CAAC,CAAC,GAAG8G,OAAO,CAAC9G,CAAC,CAAC;;EAE/BsI,IAAI,CAACA,IAAI,CAAC3J,MAAM,GAAG,CAAC,CAAC,GAAGC,KAAK;EAC7B,OAAO0J,IAAI;AACb;AAEA;;;;AAIA;AACA,OAAM,SAAUG,SAASA,CAAC/I,MAAW;EACnC;EACA;EACA;EACA;EACA;EACA;EACA,OAAOA,MAAM,IAAIA,MAAM,CAACgJ,IAAI,IAAI,OAAOhJ,MAAM,CAACgJ,IAAI,KAAK,UAAU;AACnE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}