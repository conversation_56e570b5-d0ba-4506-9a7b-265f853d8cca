{"ast": null, "code": "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { backend_util, Conv2DBackpropInput, TensorBuffer, util } from '@tensorflow/tfjs-core';\nimport { assertNotComplex } from '../cpu_util';\nexport function conv2DBackpropInput(args) {\n  const {\n    inputs,\n    backend,\n    attrs\n  } = args;\n  const {\n    dy,\n    filter\n  } = inputs;\n  const {\n    inputShape,\n    strides,\n    pad,\n    dataFormat,\n    dimRoundingMode\n  } = attrs;\n  assertNotComplex([dy, filter], 'conv2dBackpropInput');\n  const filterStrides = util.computeStrides(filter.shape);\n  const dyStrides = util.computeStrides(dy.shape);\n  let $dataFormat = backend_util.convertConv2DDataFormat(dataFormat);\n  const convInfo = backend_util.computeConv2DInfo(inputShape, filter.shape, strides, 1 /* dilations */, pad, dimRoundingMode, false, $dataFormat);\n  const dx = new TensorBuffer(convInfo.inShape, 'float32');\n  const dxValues = dx.values;\n  const dyValues = backend.data.get(dy.dataId).values;\n  const fltValues = backend.data.get(filter.dataId).values;\n  const [fltS0, fltS1, fltS2] = filterStrides;\n  const {\n    batchSize,\n    filterHeight,\n    filterWidth,\n    inChannels,\n    inHeight,\n    inWidth,\n    outChannels,\n    outHeight,\n    outWidth,\n    strideHeight,\n    strideWidth\n  } = convInfo;\n  $dataFormat = convInfo.dataFormat;\n  const topPad = filterHeight - 1 - convInfo.padInfo.top;\n  const leftPad = filterWidth - 1 - convInfo.padInfo.left;\n  const isChannelsLast = $dataFormat === 'channelsLast';\n  const xBatchStride = dx.strides[0];\n  const xRowStride = isChannelsLast ? dx.strides[1] : dx.strides[2];\n  const xColStride = isChannelsLast ? dx.strides[2] : 1;\n  const xChannelStride = isChannelsLast ? 1 : dx.strides[1];\n  const yBatchStride = dyStrides[0];\n  const yRowStride = isChannelsLast ? dyStrides[1] : dyStrides[2];\n  const yColStride = isChannelsLast ? dyStrides[2] : 1;\n  const yChannelStride = isChannelsLast ? 1 : dyStrides[1];\n  for (let b = 0; b < batchSize; ++b) {\n    for (let d1 = 0; d1 < inChannels; ++d1) {\n      for (let xR = 0; xR < inHeight; ++xR) {\n        const xRCorner = xR - topPad;\n        const xRMin = Math.max(0, Math.ceil(xRCorner / strideHeight));\n        const yRMax = Math.min(outHeight, (filterHeight + xRCorner) / strideHeight);\n        for (let xC = 0; xC < inWidth; ++xC) {\n          const xCCorner = xC - leftPad;\n          const xCMin = Math.max(0, Math.ceil(xCCorner / strideWidth));\n          const yCMax = Math.min(outWidth, (filterWidth + xCCorner) / strideWidth);\n          let dotProd = 0;\n          for (let yR = xRMin; yR < yRMax; ++yR) {\n            const wR = yR * strideHeight - xRCorner;\n            for (let yC = xCMin; yC < yCMax; ++yC) {\n              const wC = yC * strideWidth - xCCorner;\n              const dyOffset = yBatchStride * b + yRowStride * yR + yColStride * yC;\n              const fltOffset = fltS0 * (filterHeight - 1 - wR) + fltS1 * (filterWidth - 1 - wC) + fltS2 * d1;\n              for (let d2 = 0; d2 < outChannels; ++d2) {\n                const pixel = dyValues[dyOffset + yChannelStride * d2];\n                const weight = fltValues[fltOffset + d2];\n                dotProd += pixel * weight;\n              }\n            }\n          }\n          const dxOffset = xBatchStride * b + xRowStride * xR + xColStride * xC + xChannelStride * d1;\n          dxValues[dxOffset] = dotProd;\n        }\n      }\n    }\n  }\n  return backend.makeTensorInfo(dx.shape, dx.dtype, dx.values);\n}\nexport const conv2DBackpropInputConfig = {\n  kernelName: Conv2DBackpropInput,\n  backendName: 'cpu',\n  kernelFunc: conv2DBackpropInput\n};", "map": {"version": 3, "names": ["backend_util", "Conv2DBackpropInput", "Tensor<PERSON><PERSON><PERSON>", "util", "assertNotComplex", "conv2DBackpropInput", "args", "inputs", "backend", "attrs", "dy", "filter", "inputShape", "strides", "pad", "dataFormat", "dimRoundingMode", "filterStrides", "computeStrides", "shape", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "$dataFormat", "convertConv2DDataFormat", "convInfo", "computeConv2DInfo", "dx", "inShape", "dx<PERSON><PERSON><PERSON>", "values", "dyValues", "data", "get", "dataId", "fltValues", "fltS0", "fltS1", "fltS2", "batchSize", "filterHeight", "filterWidth", "inChannels", "inHeight", "inWidth", "outChannels", "outHeight", "outWidth", "strideHeight", "<PERSON><PERSON><PERSON><PERSON>", "topPad", "padInfo", "top", "leftPad", "left", "isChannelsLast", "xBatchStride", "xRowStride", "xColStride", "xChannelStride", "yBatchStride", "yRowStride", "yColStride", "yChannelStride", "b", "d1", "xR", "<PERSON><PERSON><PERSON><PERSON>", "xRMin", "Math", "max", "ceil", "yRMax", "min", "xC", "x<PERSON><PERSON><PERSON>", "xCMin", "yCMax", "dotProd", "yR", "wR", "yC", "wC", "dyOffset", "fltOffset", "d2", "pixel", "weight", "dxOffset", "makeTensorInfo", "dtype", "conv2DBackpropInputConfig", "kernelName", "backendName", "kernelFunc"], "sources": ["C:\\tfjs-backend-cpu\\src\\kernels\\Conv2DBackpropInput.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {backend_util, Conv2DBackpropInput, Conv2DBackpropInputAttrs, Conv2DBackpropInputInputs, KernelConfig, KernelFunc, TensorBuffer, TensorInfo, TypedArray, util} from '@tensorflow/tfjs-core';\n\nimport {MathBackendCPU} from '../backend_cpu';\nimport {assertNotComplex} from '../cpu_util';\n\nexport function conv2DBackpropInput(args: {\n  inputs: Conv2DBackpropInputInputs,\n  backend: MathBackendCPU,\n  attrs: Conv2DBackpropInputAttrs\n}): TensorInfo {\n  const {inputs, backend, attrs} = args;\n  const {dy, filter} = inputs;\n  const {inputShape, strides, pad, dataFormat, dimRoundingMode} = attrs;\n\n  assertNotComplex([dy, filter], 'conv2dBackpropInput');\n\n  const filterStrides = util.computeStrides(filter.shape);\n  const dyStrides = util.computeStrides(dy.shape);\n\n  let $dataFormat = backend_util.convertConv2DDataFormat(dataFormat);\n  const convInfo = backend_util.computeConv2DInfo(\n      inputShape, filter.shape as [number, number, number, number], strides,\n      1 /* dilations */, pad, dimRoundingMode, false, $dataFormat);\n\n  const dx = new TensorBuffer(convInfo.inShape, 'float32');\n  const dxValues = dx.values;\n  const dyValues = backend.data.get(dy.dataId).values as TypedArray;\n  const fltValues = backend.data.get(filter.dataId).values as TypedArray;\n  const [fltS0, fltS1, fltS2] = filterStrides;\n  const {\n    batchSize,\n    filterHeight,\n    filterWidth,\n    inChannels,\n    inHeight,\n    inWidth,\n    outChannels,\n    outHeight,\n    outWidth,\n    strideHeight,\n    strideWidth\n  } = convInfo;\n  $dataFormat = convInfo.dataFormat;\n  const topPad = filterHeight - 1 - convInfo.padInfo.top;\n  const leftPad = filterWidth - 1 - convInfo.padInfo.left;\n\n  const isChannelsLast = $dataFormat === 'channelsLast';\n  const xBatchStride = dx.strides[0];\n  const xRowStride = isChannelsLast ? dx.strides[1] : dx.strides[2];\n  const xColStride = isChannelsLast ? dx.strides[2] : 1;\n  const xChannelStride = isChannelsLast ? 1 : dx.strides[1];\n  const yBatchStride = dyStrides[0];\n  const yRowStride = isChannelsLast ? dyStrides[1] : dyStrides[2];\n  const yColStride = isChannelsLast ? dyStrides[2] : 1;\n  const yChannelStride = isChannelsLast ? 1 : dyStrides[1];\n\n  for (let b = 0; b < batchSize; ++b) {\n    for (let d1 = 0; d1 < inChannels; ++d1) {\n      for (let xR = 0; xR < inHeight; ++xR) {\n        const xRCorner = xR - topPad;\n        const xRMin = Math.max(0, Math.ceil(xRCorner / strideHeight));\n        const yRMax =\n            Math.min(outHeight, (filterHeight + xRCorner) / strideHeight);\n\n        for (let xC = 0; xC < inWidth; ++xC) {\n          const xCCorner = xC - leftPad;\n          const xCMin = Math.max(0, Math.ceil(xCCorner / strideWidth));\n          const yCMax =\n              Math.min(outWidth, (filterWidth + xCCorner) / strideWidth);\n\n          let dotProd = 0;\n          for (let yR = xRMin; yR < yRMax; ++yR) {\n            const wR = yR * strideHeight - xRCorner;\n\n            for (let yC = xCMin; yC < yCMax; ++yC) {\n              const wC = yC * strideWidth - xCCorner;\n              const dyOffset =\n                  yBatchStride * b + yRowStride * yR + yColStride * yC;\n              const fltOffset = fltS0 * (filterHeight - 1 - wR) +\n                  fltS1 * (filterWidth - 1 - wC) + fltS2 * d1;\n\n              for (let d2 = 0; d2 < outChannels; ++d2) {\n                const pixel = dyValues[dyOffset + yChannelStride * d2];\n                const weight = fltValues[fltOffset + d2];\n                dotProd += pixel * weight;\n              }\n            }\n          }\n          const dxOffset = xBatchStride * b + xRowStride * xR +\n              xColStride * xC + xChannelStride * d1;\n          dxValues[dxOffset] = dotProd;\n        }\n      }\n    }\n  }\n\n  return backend.makeTensorInfo(dx.shape, dx.dtype, dx.values);\n}\n\nexport const conv2DBackpropInputConfig: KernelConfig = {\n  kernelName: Conv2DBackpropInput,\n  backendName: 'cpu',\n  kernelFunc: conv2DBackpropInput as unknown as KernelFunc\n};\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAiBA,SAAQA,YAAY,EAAEC,mBAAmB,EAAiFC,YAAY,EAA0BC,IAAI,QAAO,uBAAuB;AAGlM,SAAQC,gBAAgB,QAAO,aAAa;AAE5C,OAAM,SAAUC,mBAAmBA,CAACC,IAInC;EACC,MAAM;IAACC,MAAM;IAAEC,OAAO;IAAEC;EAAK,CAAC,GAAGH,IAAI;EACrC,MAAM;IAACI,EAAE;IAAEC;EAAM,CAAC,GAAGJ,MAAM;EAC3B,MAAM;IAACK,UAAU;IAAEC,OAAO;IAAEC,GAAG;IAAEC,UAAU;IAAEC;EAAe,CAAC,GAAGP,KAAK;EAErEL,gBAAgB,CAAC,CAACM,EAAE,EAAEC,MAAM,CAAC,EAAE,qBAAqB,CAAC;EAErD,MAAMM,aAAa,GAAGd,IAAI,CAACe,cAAc,CAACP,MAAM,CAACQ,KAAK,CAAC;EACvD,MAAMC,SAAS,GAAGjB,IAAI,CAACe,cAAc,CAACR,EAAE,CAACS,KAAK,CAAC;EAE/C,IAAIE,WAAW,GAAGrB,YAAY,CAACsB,uBAAuB,CAACP,UAAU,CAAC;EAClE,MAAMQ,QAAQ,GAAGvB,YAAY,CAACwB,iBAAiB,CAC3CZ,UAAU,EAAED,MAAM,CAACQ,KAAyC,EAAEN,OAAO,EACrE,CAAC,CAAC,iBAAiBC,GAAG,EAAEE,eAAe,EAAE,KAAK,EAAEK,WAAW,CAAC;EAEhE,MAAMI,EAAE,GAAG,IAAIvB,YAAY,CAACqB,QAAQ,CAACG,OAAO,EAAE,SAAS,CAAC;EACxD,MAAMC,QAAQ,GAAGF,EAAE,CAACG,MAAM;EAC1B,MAAMC,QAAQ,GAAGrB,OAAO,CAACsB,IAAI,CAACC,GAAG,CAACrB,EAAE,CAACsB,MAAM,CAAC,CAACJ,MAAoB;EACjE,MAAMK,SAAS,GAAGzB,OAAO,CAACsB,IAAI,CAACC,GAAG,CAACpB,MAAM,CAACqB,MAAM,CAAC,CAACJ,MAAoB;EACtE,MAAM,CAACM,KAAK,EAAEC,KAAK,EAAEC,KAAK,CAAC,GAAGnB,aAAa;EAC3C,MAAM;IACJoB,SAAS;IACTC,YAAY;IACZC,WAAW;IACXC,UAAU;IACVC,QAAQ;IACRC,OAAO;IACPC,WAAW;IACXC,SAAS;IACTC,QAAQ;IACRC,YAAY;IACZC;EAAW,CACZ,GAAGxB,QAAQ;EACZF,WAAW,GAAGE,QAAQ,CAACR,UAAU;EACjC,MAAMiC,MAAM,GAAGV,YAAY,GAAG,CAAC,GAAGf,QAAQ,CAAC0B,OAAO,CAACC,GAAG;EACtD,MAAMC,OAAO,GAAGZ,WAAW,GAAG,CAAC,GAAGhB,QAAQ,CAAC0B,OAAO,CAACG,IAAI;EAEvD,MAAMC,cAAc,GAAGhC,WAAW,KAAK,cAAc;EACrD,MAAMiC,YAAY,GAAG7B,EAAE,CAACZ,OAAO,CAAC,CAAC,CAAC;EAClC,MAAM0C,UAAU,GAAGF,cAAc,GAAG5B,EAAE,CAACZ,OAAO,CAAC,CAAC,CAAC,GAAGY,EAAE,CAACZ,OAAO,CAAC,CAAC,CAAC;EACjE,MAAM2C,UAAU,GAAGH,cAAc,GAAG5B,EAAE,CAACZ,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC;EACrD,MAAM4C,cAAc,GAAGJ,cAAc,GAAG,CAAC,GAAG5B,EAAE,CAACZ,OAAO,CAAC,CAAC,CAAC;EACzD,MAAM6C,YAAY,GAAGtC,SAAS,CAAC,CAAC,CAAC;EACjC,MAAMuC,UAAU,GAAGN,cAAc,GAAGjC,SAAS,CAAC,CAAC,CAAC,GAAGA,SAAS,CAAC,CAAC,CAAC;EAC/D,MAAMwC,UAAU,GAAGP,cAAc,GAAGjC,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC;EACpD,MAAMyC,cAAc,GAAGR,cAAc,GAAG,CAAC,GAAGjC,SAAS,CAAC,CAAC,CAAC;EAExD,KAAK,IAAI0C,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGzB,SAAS,EAAE,EAAEyB,CAAC,EAAE;IAClC,KAAK,IAAIC,EAAE,GAAG,CAAC,EAAEA,EAAE,GAAGvB,UAAU,EAAE,EAAEuB,EAAE,EAAE;MACtC,KAAK,IAAIC,EAAE,GAAG,CAAC,EAAEA,EAAE,GAAGvB,QAAQ,EAAE,EAAEuB,EAAE,EAAE;QACpC,MAAMC,QAAQ,GAAGD,EAAE,GAAGhB,MAAM;QAC5B,MAAMkB,KAAK,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAED,IAAI,CAACE,IAAI,CAACJ,QAAQ,GAAGnB,YAAY,CAAC,CAAC;QAC7D,MAAMwB,KAAK,GACPH,IAAI,CAACI,GAAG,CAAC3B,SAAS,EAAE,CAACN,YAAY,GAAG2B,QAAQ,IAAInB,YAAY,CAAC;QAEjE,KAAK,IAAI0B,EAAE,GAAG,CAAC,EAAEA,EAAE,GAAG9B,OAAO,EAAE,EAAE8B,EAAE,EAAE;UACnC,MAAMC,QAAQ,GAAGD,EAAE,GAAGrB,OAAO;UAC7B,MAAMuB,KAAK,GAAGP,IAAI,CAACC,GAAG,CAAC,CAAC,EAAED,IAAI,CAACE,IAAI,CAACI,QAAQ,GAAG1B,WAAW,CAAC,CAAC;UAC5D,MAAM4B,KAAK,GACPR,IAAI,CAACI,GAAG,CAAC1B,QAAQ,EAAE,CAACN,WAAW,GAAGkC,QAAQ,IAAI1B,WAAW,CAAC;UAE9D,IAAI6B,OAAO,GAAG,CAAC;UACf,KAAK,IAAIC,EAAE,GAAGX,KAAK,EAAEW,EAAE,GAAGP,KAAK,EAAE,EAAEO,EAAE,EAAE;YACrC,MAAMC,EAAE,GAAGD,EAAE,GAAG/B,YAAY,GAAGmB,QAAQ;YAEvC,KAAK,IAAIc,EAAE,GAAGL,KAAK,EAAEK,EAAE,GAAGJ,KAAK,EAAE,EAAEI,EAAE,EAAE;cACrC,MAAMC,EAAE,GAAGD,EAAE,GAAGhC,WAAW,GAAG0B,QAAQ;cACtC,MAAMQ,QAAQ,GACVvB,YAAY,GAAGI,CAAC,GAAGH,UAAU,GAAGkB,EAAE,GAAGjB,UAAU,GAAGmB,EAAE;cACxD,MAAMG,SAAS,GAAGhD,KAAK,IAAII,YAAY,GAAG,CAAC,GAAGwC,EAAE,CAAC,GAC7C3C,KAAK,IAAII,WAAW,GAAG,CAAC,GAAGyC,EAAE,CAAC,GAAG5C,KAAK,GAAG2B,EAAE;cAE/C,KAAK,IAAIoB,EAAE,GAAG,CAAC,EAAEA,EAAE,GAAGxC,WAAW,EAAE,EAAEwC,EAAE,EAAE;gBACvC,MAAMC,KAAK,GAAGvD,QAAQ,CAACoD,QAAQ,GAAGpB,cAAc,GAAGsB,EAAE,CAAC;gBACtD,MAAME,MAAM,GAAGpD,SAAS,CAACiD,SAAS,GAAGC,EAAE,CAAC;gBACxCP,OAAO,IAAIQ,KAAK,GAAGC,MAAM;;;;UAI/B,MAAMC,QAAQ,GAAGhC,YAAY,GAAGQ,CAAC,GAAGP,UAAU,GAAGS,EAAE,GAC/CR,UAAU,GAAGgB,EAAE,GAAGf,cAAc,GAAGM,EAAE;UACzCpC,QAAQ,CAAC2D,QAAQ,CAAC,GAAGV,OAAO;;;;;EAMpC,OAAOpE,OAAO,CAAC+E,cAAc,CAAC9D,EAAE,CAACN,KAAK,EAAEM,EAAE,CAAC+D,KAAK,EAAE/D,EAAE,CAACG,MAAM,CAAC;AAC9D;AAEA,OAAO,MAAM6D,yBAAyB,GAAiB;EACrDC,UAAU,EAAEzF,mBAAmB;EAC/B0F,WAAW,EAAE,KAAK;EAClBC,UAAU,EAAEvF;CACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}