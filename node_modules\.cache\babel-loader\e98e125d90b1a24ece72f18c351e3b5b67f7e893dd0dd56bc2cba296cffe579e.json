{"ast": null, "code": "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { ENGINE } from '../../engine';\nimport { ResizeNearestNeighbor } from '../../kernel_names';\nimport { convertToTensor } from '../../tensor_util_env';\nimport * as util from '../../util';\nimport { op } from '../operation';\nimport { reshape } from '../reshape';\n/**\n * NearestNeighbor resize a batch of 3D images to a new shape.\n *\n * @param images The images, of rank 4 or rank 3, of shape\n *     `[batch, height, width, inChannels]`. If rank 3, batch of 1 is assumed.\n * @param size The new shape `[newHeight, newWidth]` to resize the\n *     images to. Each channel is resized individually.\n * @param alignCorners Defaults to False. If true, rescale\n *     input by `(new_height - 1) / (height - 1)`, which exactly aligns the 4\n *     corners of images and resized images. If false, rescale by\n *     `new_height / height`. Treat similarly the width dimension.\n * @param halfPixelCenters Defaults to `false`. Whether to assume pixels are of\n *      half the actual dimensions, and yield more accurate resizes. This flag\n *      would also make the floating point coordinates of the top left pixel\n *      0.5, 0.5.\n *\n * @doc {heading: 'Operations', subheading: 'Images', namespace: 'image'}\n */\nfunction resizeNearestNeighbor_(images, size, alignCorners = false, halfPixelCenters = false) {\n  const $images = convertToTensor(images, 'images', 'resizeNearestNeighbor');\n  util.assert($images.rank === 3 || $images.rank === 4, () => `Error in resizeNearestNeighbor: x must be rank 3 or 4, but got ` + `rank ${$images.rank}.`);\n  util.assert(size.length === 2, () => `Error in resizeNearestNeighbor: new shape must 2D, but got shape ` + `${size}.`);\n  util.assert($images.dtype === 'float32' || $images.dtype === 'int32', () => '`images` must have `int32` or `float32` as dtype');\n  util.assert(halfPixelCenters === false || alignCorners === false, () => `Error in resizeNearestNeighbor: If halfPixelCenters is true, ` + `alignCorners must be false.`);\n  let batchImages = $images;\n  let reshapedTo4D = false;\n  if ($images.rank === 3) {\n    reshapedTo4D = true;\n    batchImages = reshape($images, [1, $images.shape[0], $images.shape[1], $images.shape[2]]);\n  }\n  const [] = size;\n  const inputs = {\n    images: batchImages\n  };\n  const attrs = {\n    alignCorners,\n    halfPixelCenters,\n    size\n  };\n  // tslint:disable-next-line: no-unnecessary-type-assertion\n  const res = ENGINE.runKernel(ResizeNearestNeighbor, inputs, attrs);\n  if (reshapedTo4D) {\n    return reshape(res, [res.shape[1], res.shape[2], res.shape[3]]);\n  }\n  return res;\n}\nexport const resizeNearestNeighbor = /* @__PURE__ */op({\n  resizeNearestNeighbor_\n});", "map": {"version": 3, "names": ["ENGINE", "ResizeNearestNeighbor", "convertToTensor", "util", "op", "reshape", "resizeNearestNeighbor_", "images", "size", "alignCorners", "halfPixelCenters", "$images", "assert", "rank", "length", "dtype", "batchImages", "reshapedTo4D", "shape", "inputs", "attrs", "res", "runKernel", "resizeNearestNeighbor"], "sources": ["C:\\tfjs-core\\src\\ops\\image\\resize_nearest_neighbor.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {ENGINE} from '../../engine';\nimport {ResizeNearestNeighbor, ResizeNearestNeighborAttrs, ResizeNearestNeighborInputs} from '../../kernel_names';\nimport {NamedAttrMap} from '../../kernel_registry';\nimport {Tensor3D, Tensor4D} from '../../tensor';\nimport {NamedTensorMap} from '../../tensor_types';\nimport {convertToTensor} from '../../tensor_util_env';\nimport {TensorLike} from '../../types';\nimport * as util from '../../util';\n\nimport {op} from '../operation';\nimport {reshape} from '../reshape';\n\n/**\n * NearestNeighbor resize a batch of 3D images to a new shape.\n *\n * @param images The images, of rank 4 or rank 3, of shape\n *     `[batch, height, width, inChannels]`. If rank 3, batch of 1 is assumed.\n * @param size The new shape `[newHeight, newWidth]` to resize the\n *     images to. Each channel is resized individually.\n * @param alignCorners Defaults to False. If true, rescale\n *     input by `(new_height - 1) / (height - 1)`, which exactly aligns the 4\n *     corners of images and resized images. If false, rescale by\n *     `new_height / height`. Treat similarly the width dimension.\n * @param halfPixelCenters Defaults to `false`. Whether to assume pixels are of\n *      half the actual dimensions, and yield more accurate resizes. This flag\n *      would also make the floating point coordinates of the top left pixel\n *      0.5, 0.5.\n *\n * @doc {heading: 'Operations', subheading: 'Images', namespace: 'image'}\n */\nfunction resizeNearestNeighbor_<T extends Tensor3D|Tensor4D>(\n    images: T|TensorLike, size: [number, number], alignCorners = false,\n    halfPixelCenters = false): T {\n  const $images = convertToTensor(images, 'images', 'resizeNearestNeighbor');\n\n  util.assert(\n      $images.rank === 3 || $images.rank === 4,\n      () => `Error in resizeNearestNeighbor: x must be rank 3 or 4, but got ` +\n          `rank ${$images.rank}.`);\n  util.assert(\n      size.length === 2,\n      () =>\n          `Error in resizeNearestNeighbor: new shape must 2D, but got shape ` +\n          `${size}.`);\n  util.assert(\n      $images.dtype === 'float32' || $images.dtype === 'int32',\n      () => '`images` must have `int32` or `float32` as dtype');\n  util.assert(\n      halfPixelCenters === false || alignCorners === false,\n      () => `Error in resizeNearestNeighbor: If halfPixelCenters is true, ` +\n          `alignCorners must be false.`);\n  let batchImages = $images as Tensor4D;\n  let reshapedTo4D = false;\n  if ($images.rank === 3) {\n    reshapedTo4D = true;\n    batchImages = reshape(\n        $images, [1, $images.shape[0], $images.shape[1], $images.shape[2]]);\n  }\n  const [] = size;\n\n  const inputs: ResizeNearestNeighborInputs = {images: batchImages};\n  const attrs:\n      ResizeNearestNeighborAttrs = {alignCorners, halfPixelCenters, size};\n\n  // tslint:disable-next-line: no-unnecessary-type-assertion\n  const res = ENGINE.runKernel(\n                  ResizeNearestNeighbor, inputs as unknown as NamedTensorMap,\n                  attrs as unknown as NamedAttrMap) as T;\n\n  if (reshapedTo4D) {\n    return reshape(res, [res.shape[1], res.shape[2], res.shape[3]]) as T;\n  }\n  return res;\n}\n\nexport const resizeNearestNeighbor = /* @__PURE__ */ op({resizeNearestNeighbor_});\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAiBA,SAAQA,MAAM,QAAO,cAAc;AACnC,SAAQC,qBAAqB,QAAgE,oBAAoB;AAIjH,SAAQC,eAAe,QAAO,uBAAuB;AAErD,OAAO,KAAKC,IAAI,MAAM,YAAY;AAElC,SAAQC,EAAE,QAAO,cAAc;AAC/B,SAAQC,OAAO,QAAO,YAAY;AAElC;;;;;;;;;;;;;;;;;;AAkBA,SAASC,sBAAsBA,CAC3BC,MAAoB,EAAEC,IAAsB,EAAEC,YAAY,GAAG,KAAK,EAClEC,gBAAgB,GAAG,KAAK;EAC1B,MAAMC,OAAO,GAAGT,eAAe,CAACK,MAAM,EAAE,QAAQ,EAAE,uBAAuB,CAAC;EAE1EJ,IAAI,CAACS,MAAM,CACPD,OAAO,CAACE,IAAI,KAAK,CAAC,IAAIF,OAAO,CAACE,IAAI,KAAK,CAAC,EACxC,MAAM,iEAAiE,GACnE,QAAQF,OAAO,CAACE,IAAI,GAAG,CAAC;EAChCV,IAAI,CAACS,MAAM,CACPJ,IAAI,CAACM,MAAM,KAAK,CAAC,EACjB,MACI,mEAAmE,GACnE,GAAGN,IAAI,GAAG,CAAC;EACnBL,IAAI,CAACS,MAAM,CACPD,OAAO,CAACI,KAAK,KAAK,SAAS,IAAIJ,OAAO,CAACI,KAAK,KAAK,OAAO,EACxD,MAAM,kDAAkD,CAAC;EAC7DZ,IAAI,CAACS,MAAM,CACPF,gBAAgB,KAAK,KAAK,IAAID,YAAY,KAAK,KAAK,EACpD,MAAM,+DAA+D,GACjE,6BAA6B,CAAC;EACtC,IAAIO,WAAW,GAAGL,OAAmB;EACrC,IAAIM,YAAY,GAAG,KAAK;EACxB,IAAIN,OAAO,CAACE,IAAI,KAAK,CAAC,EAAE;IACtBI,YAAY,GAAG,IAAI;IACnBD,WAAW,GAAGX,OAAO,CACjBM,OAAO,EAAE,CAAC,CAAC,EAAEA,OAAO,CAACO,KAAK,CAAC,CAAC,CAAC,EAAEP,OAAO,CAACO,KAAK,CAAC,CAAC,CAAC,EAAEP,OAAO,CAACO,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;;EAEzE,MAAM,EAAE,GAAGV,IAAI;EAEf,MAAMW,MAAM,GAAgC;IAACZ,MAAM,EAAES;EAAW,CAAC;EACjE,MAAMI,KAAK,GACsB;IAACX,YAAY;IAAEC,gBAAgB;IAAEF;EAAI,CAAC;EAEvE;EACA,MAAMa,GAAG,GAAGrB,MAAM,CAACsB,SAAS,CACZrB,qBAAqB,EAAEkB,MAAmC,EAC1DC,KAAgC,CAAM;EAEtD,IAAIH,YAAY,EAAE;IAChB,OAAOZ,OAAO,CAACgB,GAAG,EAAE,CAACA,GAAG,CAACH,KAAK,CAAC,CAAC,CAAC,EAAEG,GAAG,CAACH,KAAK,CAAC,CAAC,CAAC,EAAEG,GAAG,CAACH,KAAK,CAAC,CAAC,CAAC,CAAC,CAAM;;EAEtE,OAAOG,GAAG;AACZ;AAEA,OAAO,MAAME,qBAAqB,GAAG,eAAgBnB,EAAE,CAAC;EAACE;AAAsB,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}