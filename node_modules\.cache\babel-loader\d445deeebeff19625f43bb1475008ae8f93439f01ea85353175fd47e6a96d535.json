{"ast": null, "code": "/*\nCopyright (c) 2011, <PERSON>,<PERSON>\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in\nall copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\nTHE SOFTWARE.\n*/\n\n'use strict';\n\nconst Tokenizer = require('./tokenizer');\nclass AggressiveTokenizer extends Tokenizer {\n  tokenize(text) {\n    // break a string up into an array of tokens by anything non-word\n    return this.trim(text.split(/[^a-zA-Zá-úÁ-ÚñÑüÜ]+/));\n  }\n}\nmodule.exports = AggressiveTokenizer;", "map": {"version": 3, "names": ["Tokenizer", "require", "AggressiveTokenizer", "tokenize", "text", "trim", "split", "module", "exports"], "sources": ["C:/tmsft/node_modules/natural/lib/natural/tokenizers/aggressive_tokenizer_es.js"], "sourcesContent": ["/*\nCopyright (c) 2011, <PERSON>,<PERSON>\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in\nall copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\nTHE SOFTWARE.\n*/\n\n'use strict'\n\nconst Tokenizer = require('./tokenizer')\n\nclass AggressiveTokenizer extends Tokenizer {\n  tokenize (text) {\n    // break a string up into an array of tokens by anything non-word\n    return this.trim(text.split(/[^a-zA-Zá-úÁ-ÚñÑüÜ]+/))\n  }\n}\n\nmodule.exports = AggressiveTokenizer\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,YAAY;;AAEZ,MAAMA,SAAS,GAAGC,OAAO,CAAC,aAAa,CAAC;AAExC,MAAMC,mBAAmB,SAASF,SAAS,CAAC;EAC1CG,QAAQA,CAAEC,IAAI,EAAE;IACd;IACA,OAAO,IAAI,CAACC,IAAI,CAACD,IAAI,CAACE,KAAK,CAAC,sBAAsB,CAAC,CAAC;EACtD;AACF;AAEAC,MAAM,CAACC,OAAO,GAAGN,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}