{"ast": null, "code": "/**\n * @license\n * Copyright 2020 Google Inc. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { Unpack } from '../kernel_names';\nimport { stack } from '../ops/stack';\nexport const unpackGradConfig = {\n  kernelName: Unpack,\n  gradFunc: (dy, saved, attrs) => {\n    const unpackAttrs = attrs;\n    const {\n      axis\n    } = unpackAttrs;\n    return {\n      value: () => stack(dy, axis)\n    };\n  }\n};", "map": {"version": 3, "names": ["Unpack", "stack", "unpackGradConfig", "kernelName", "grad<PERSON>unc", "dy", "saved", "attrs", "unpackAttrs", "axis", "value"], "sources": ["C:\\tfjs-core\\src\\gradients\\Unpack_grad.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google Inc. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport {Unpack, UnpackAttrs} from '../kernel_names';\nimport {GradConfig, NamedAttrMap} from '../kernel_registry';\nimport {stack} from '../ops/stack';\nimport {Tensor} from '../tensor';\n\nexport const unpackGradConfig: GradConfig = {\n  kernelName: Unpack,\n  gradFunc: (dy: Tensor[], saved: Tensor[], attrs: NamedAttrMap) => {\n    const unpackAttrs: UnpackAttrs = attrs as unknown as UnpackAttrs;\n    const {axis} = unpackAttrs;\n    return {value: () => stack(dy, axis)};\n  }\n};\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAgBA,SAAQA,MAAM,QAAoB,iBAAiB;AAEnD,SAAQC,KAAK,QAAO,cAAc;AAGlC,OAAO,MAAMC,gBAAgB,GAAe;EAC1CC,UAAU,EAAEH,MAAM;EAClBI,QAAQ,EAAEA,CAACC,EAAY,EAAEC,KAAe,EAAEC,KAAmB,KAAI;IAC/D,MAAMC,WAAW,GAAgBD,KAA+B;IAChE,MAAM;MAACE;IAAI,CAAC,GAAGD,WAAW;IAC1B,OAAO;MAACE,KAAK,EAAEA,CAAA,KAAMT,KAAK,CAACI,EAAE,EAAEI,IAAI;IAAC,CAAC;EACvC;CACD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}