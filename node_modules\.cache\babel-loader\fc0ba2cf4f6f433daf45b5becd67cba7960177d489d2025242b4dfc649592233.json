{"ast": null, "code": "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { batchNorm } from '../../ops/batchnorm';\nimport { getGlobalTensorClass } from '../../tensor';\ngetGlobalTensorClass().prototype.batchNorm = function (mean, variance, offset, scale, varianceEpsilon) {\n  this.throwIfDisposed();\n  return batchNorm(this, mean, variance, offset, scale, varianceEpsilon);\n};", "map": {"version": 3, "names": ["batchNorm", "getGlobalTensorClass", "prototype", "mean", "variance", "offset", "scale", "varianceEpsilon", "throwIfDisposed"], "sources": ["C:\\tfjs-core\\src\\public\\chained_ops\\batchnorm.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport {batchNorm} from '../../ops/batchnorm';\nimport {getGlobalTensorClass, Tensor, Tensor1D} from '../../tensor';\nimport {Rank, TensorLike} from '../../types';\n\ndeclare module '../../tensor' {\n  interface Tensor<R extends Rank = Rank> {\n    batchNorm<T extends Tensor>(\n        mean: Tensor<R>|Tensor1D|TensorLike,\n        variance: Tensor<R>|Tensor1D|TensorLike,\n        offset?: Tensor<R>|Tensor1D|TensorLike,\n        scale?: Tensor<R>|Tensor1D|TensorLike,\n        varianceEpsilon?: number): Tensor<R>;\n  }\n}\n\ngetGlobalTensorClass().prototype.batchNorm = function<R extends Rank>(\n    mean: Tensor<R>|Tensor1D|TensorLike,\n    variance: Tensor<R>|Tensor1D|TensorLike,\n    offset?: Tensor<R>|Tensor1D|TensorLike,\n    scale?: Tensor<R>|Tensor1D|TensorLike,\n    varianceEpsilon?: number): Tensor<R> {\n  this.throwIfDisposed();\n  return batchNorm(this, mean, variance, offset, scale, varianceEpsilon);\n};\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAgBA,SAAQA,SAAS,QAAO,qBAAqB;AAC7C,SAAQC,oBAAoB,QAAyB,cAAc;AAcnEA,oBAAoB,EAAE,CAACC,SAAS,CAACF,SAAS,GAAG,UACzCG,IAAmC,EACnCC,QAAuC,EACvCC,MAAsC,EACtCC,KAAqC,EACrCC,eAAwB;EAC1B,IAAI,CAACC,eAAe,EAAE;EACtB,OAAOR,SAAS,CAAC,IAAI,EAAEG,IAAI,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,KAAK,EAAEC,eAAe,CAAC;AACxE,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}