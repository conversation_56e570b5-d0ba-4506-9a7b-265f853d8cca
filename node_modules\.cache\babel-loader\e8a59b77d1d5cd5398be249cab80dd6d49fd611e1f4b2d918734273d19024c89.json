{"ast": null, "code": "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { reshape } from '../../ops/reshape';\nimport { getGlobalTensorClass } from '../../tensor';\n/**\n * Reshapes the tensor into the shape of the provided tensor.\n *\n * @param x The tensor of required shape.\n *\n * @doc {heading: 'Tensors', subheading: 'Classes'}\n */\ngetGlobalTensorClass().prototype.reshapeAs = function (x) {\n  this.throwIfDisposed();\n  return reshape(this, x.shape);\n};", "map": {"version": 3, "names": ["reshape", "getGlobalTensorClass", "prototype", "reshapeAs", "x", "throwIfDisposed", "shape"], "sources": ["C:\\tfjs-core\\src\\public\\chained_ops\\reshape_as.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {reshape} from '../../ops/reshape';\nimport {getGlobalTensorClass, Tensor} from '../../tensor';\nimport {Rank} from '../../types';\n\ndeclare module '../../tensor' {\n  interface Tensor<R extends Rank = Rank> {\n    reshapeAs<T extends Tensor>(x: T): T;\n  }\n}\n\n/**\n * Reshapes the tensor into the shape of the provided tensor.\n *\n * @param x The tensor of required shape.\n *\n * @doc {heading: 'Tensors', subheading: 'Classes'}\n */\ngetGlobalTensorClass().prototype.reshapeAs = function<T extends Tensor>(x: T):\n    T {\n  this.throwIfDisposed();\n  return reshape(this, x.shape) as T;\n};\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAiBA,SAAQA,OAAO,QAAO,mBAAmB;AACzC,SAAQC,oBAAoB,QAAe,cAAc;AASzD;;;;;;;AAOAA,oBAAoB,EAAE,CAACC,SAAS,CAACC,SAAS,GAAG,UAA2BC,CAAI;EAE1E,IAAI,CAACC,eAAe,EAAE;EACtB,OAAOL,OAAO,CAAC,IAAI,EAAEI,CAAC,CAACE,KAAK,CAAM;AACpC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}