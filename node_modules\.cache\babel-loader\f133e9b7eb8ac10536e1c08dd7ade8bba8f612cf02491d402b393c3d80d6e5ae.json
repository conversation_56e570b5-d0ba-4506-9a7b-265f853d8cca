{"ast": null, "code": "import baseCreate from './_baseCreate.js';\nimport isObject from './isObject.js';\n\n// Internal function to execute `sourceFunc` bound to `context` with optional\n// `args`. Determines whether to execute a function as a constructor or as a\n// normal function.\nexport default function executeBound(sourceFunc, boundFunc, context, callingContext, args) {\n  if (!(callingContext instanceof boundFunc)) return sourceFunc.apply(context, args);\n  var self = baseCreate(sourceFunc.prototype);\n  var result = sourceFunc.apply(self, args);\n  if (isObject(result)) return result;\n  return self;\n}", "map": {"version": 3, "names": ["baseCreate", "isObject", "executeBound", "sourceFunc", "boundFunc", "context", "callingContext", "args", "apply", "self", "prototype", "result"], "sources": ["C:/tmsft/node_modules/underscore/modules/_executeBound.js"], "sourcesContent": ["import baseCreate from './_baseCreate.js';\nimport isObject from './isObject.js';\n\n// Internal function to execute `sourceFunc` bound to `context` with optional\n// `args`. Determines whether to execute a function as a constructor or as a\n// normal function.\nexport default function executeBound(sourceFunc, boundFunc, context, callingContext, args) {\n  if (!(callingContext instanceof boundFunc)) return sourceFunc.apply(context, args);\n  var self = baseCreate(sourceFunc.prototype);\n  var result = sourceFunc.apply(self, args);\n  if (isObject(result)) return result;\n  return self;\n}\n"], "mappings": "AAAA,OAAOA,UAAU,MAAM,kBAAkB;AACzC,OAAOC,QAAQ,MAAM,eAAe;;AAEpC;AACA;AACA;AACA,eAAe,SAASC,YAAYA,CAACC,UAAU,EAAEC,SAAS,EAAEC,OAAO,EAAEC,cAAc,EAAEC,IAAI,EAAE;EACzF,IAAI,EAAED,cAAc,YAAYF,SAAS,CAAC,EAAE,OAAOD,UAAU,CAACK,KAAK,CAACH,OAAO,EAAEE,IAAI,CAAC;EAClF,IAAIE,IAAI,GAAGT,UAAU,CAACG,UAAU,CAACO,SAAS,CAAC;EAC3C,IAAIC,MAAM,GAAGR,UAAU,CAACK,KAAK,CAACC,IAAI,EAAEF,IAAI,CAAC;EACzC,IAAIN,QAAQ,CAACU,MAAM,CAAC,EAAE,OAAOA,MAAM;EACnC,OAAOF,IAAI;AACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}