{"ast": null, "code": "var _jsxFileName = \"C:\\\\tmsft\\\\src\\\\components\\\\FileManager.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { fileStorageService } from '../services/fileStorageService';\nimport { transactionStorageService } from '../services/transactionStorageService';\nimport './FileManager.css';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nexport const FileManager = ({\n  onFileDeleted\n}) => {\n  _s();\n  const [uploadedFiles, setUploadedFiles] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [deleteConfirmation, setDeleteConfirmation] = useState(null);\n  const [deleting, setDeleting] = useState(null);\n  const [deletionResult, setDeletionResult] = useState(null);\n  const [restoring, setRestoring] = useState(null);\n  const [orphanedCount, setOrphanedCount] = useState(0);\n  const [cleaningOrphaned, setCleaningOrphaned] = useState(false);\n\n  // Load uploaded files\n  const loadFiles = () => {\n    try {\n      setLoading(true);\n      const files = fileStorageService.getAllUploadedFiles();\n      // Sort by upload date (newest first)\n      files.sort((a, b) => new Date(b.uploadDate).getTime() - new Date(a.uploadDate).getTime());\n      setUploadedFiles(files);\n\n      // Count orphaned transactions (old data without file IDs)\n      const orphanedTransactions = transactionStorageService.getOrphanedTransactions();\n      setOrphanedCount(orphanedTransactions.length);\n    } catch (error) {\n      console.error('Error loading files:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n  useEffect(() => {\n    loadFiles();\n  }, []);\n\n  // Format file size\n  const formatFileSize = bytes => {\n    if (bytes === 0) return '0 Bytes';\n    const k = 1024;\n    const sizes = ['Bytes', 'KB', 'MB', 'GB'];\n    const i = Math.floor(Math.log(bytes) / Math.log(k));\n    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\n  };\n\n  // Format date\n  const formatDate = dateString => {\n    const date = new Date(dateString);\n    return date.toLocaleDateString('en-GB', {\n      day: '2-digit',\n      month: '2-digit',\n      year: 'numeric',\n      hour: '2-digit',\n      minute: '2-digit'\n    });\n  };\n\n  // Handle delete initiation\n  const handleDeleteClick = file => {\n    setDeleteConfirmation({\n      fileId: file.id,\n      fileName: file.fileName,\n      step: 1\n    });\n  };\n\n  // Handle first confirmation\n  const handleFirstConfirmation = () => {\n    if (deleteConfirmation) {\n      setDeleteConfirmation({\n        ...deleteConfirmation,\n        step: 2\n      });\n    }\n  };\n\n  // Handle final deletion\n  const handleFinalDeletion = async () => {\n    if (!deleteConfirmation) return;\n    try {\n      setDeleting(deleteConfirmation.fileId);\n      const result = fileStorageService.deleteUploadedFile(deleteConfirmation.fileId);\n      if (result.success) {\n        // Remove from local state\n        setUploadedFiles(prev => prev.filter(f => f.id !== deleteConfirmation.fileId));\n\n        // Show deletion result\n        setDeletionResult({\n          report: result.deletionReport,\n          showRestoreOption: true\n        });\n\n        // Notify parent component (this will refresh transactions)\n        if (onFileDeleted) {\n          onFileDeleted(deleteConfirmation.fileId);\n        }\n        console.log(`Successfully deleted file: ${deleteConfirmation.fileName}`);\n      } else {\n        alert(`Failed to delete file: ${result.deletionReport.error || 'Unknown error'}`);\n      }\n    } catch (error) {\n      console.error('Error deleting file:', error);\n      alert('An error occurred while deleting the file.');\n    } finally {\n      setDeleting(null);\n      setDeleteConfirmation(null);\n    }\n  };\n\n  // Cancel deletion\n  const cancelDeletion = () => {\n    setDeleteConfirmation(null);\n  };\n\n  // Handle restore from backup\n  const handleRestore = async backupKey => {\n    if (!deletionResult) return;\n    try {\n      setRestoring(backupKey);\n      const restoreResult = fileStorageService.restoreFromBackup(backupKey);\n      if (restoreResult.success) {\n        // Reload files to show restored file\n        loadFiles();\n\n        // Notify parent component to refresh transactions\n        if (onFileDeleted) {\n          onFileDeleted('restore-trigger');\n        }\n\n        // Close deletion result dialog\n        setDeletionResult(null);\n        alert(`Successfully restored ${restoreResult.restoredCount} transactions and file record. The Transactions page has been updated.`);\n      } else {\n        alert(`Failed to restore: ${restoreResult.error || 'Unknown error'}`);\n      }\n    } catch (error) {\n      console.error('Error restoring file:', error);\n      alert('An error occurred while restoring the file.');\n    } finally {\n      setRestoring(null);\n    }\n  };\n\n  // Close deletion result dialog\n  const closeDeletionResult = () => {\n    setDeletionResult(null);\n  };\n\n  // Handle cleanup of orphaned transactions\n  const handleCleanupOrphaned = async () => {\n    if (orphanedCount === 0) return;\n    const confirmed = window.confirm(`Are you sure you want to delete ${orphanedCount} orphaned transactions?\\n\\n` + 'These are transactions that were imported before the file tracking system was implemented. ' + 'They cannot be linked to any file and should be cleaned up.\\n\\n' + 'This action cannot be undone.');\n    if (!confirmed) return;\n    try {\n      setCleaningOrphaned(true);\n      const deletedCount = transactionStorageService.clearOrphanedTransactions();\n\n      // Refresh data\n      loadFiles();\n\n      // Notify parent component to refresh transactions\n      if (onFileDeleted) {\n        onFileDeleted('orphaned-cleanup');\n      }\n      alert(`Successfully deleted ${deletedCount} orphaned transactions.`);\n    } catch (error) {\n      console.error('Error cleaning orphaned transactions:', error);\n      alert('Failed to clean orphaned transactions. Please try again.');\n    } finally {\n      setCleaningOrphaned(false);\n    }\n  };\n\n  // Get storage statistics\n  const stats = fileStorageService.getStorageStats();\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"file-manager-loading\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"loading-spinner\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 214,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"Loading uploaded files...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 215,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 213,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"file-manager\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"file-manager-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"file-manager-title-section\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"file-manager-title\",\n          children: \"File Management\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 224,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"file-manager-description\",\n          children: \"Manage your uploaded CSV files and their associated transaction data\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 225,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 223,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"file-manager-stats\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-card\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat-value\",\n            children: stats.totalFiles\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 231,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat-label\",\n            children: \"Files Uploaded\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 232,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 230,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-card\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat-value\",\n            children: stats.totalTransactions.toLocaleString()\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 235,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat-label\",\n            children: \"Total Transactions\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 236,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 234,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-card\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat-value\",\n            children: formatFileSize(stats.totalSize)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 239,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat-label\",\n            children: \"Storage Used\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 240,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 238,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 229,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 222,\n      columnNumber: 7\n    }, this), orphanedCount > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"orphaned-warning\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"warning-content\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"warning-icon\",\n          children: \"\\u26A0\\uFE0F\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 249,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"warning-message\",\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"Orphaned Transaction Data Detected\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 251,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [\"Found \", orphanedCount.toLocaleString(), \" transactions that were imported before the file tracking system was implemented. These transactions cannot be properly managed and should be cleaned up.\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 252,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 250,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"warning-actions\",\n          children: /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: handleCleanupOrphaned,\n            disabled: cleaningOrphaned,\n            className: \"btn btn-warning btn-sm\",\n            children: cleaningOrphaned ? /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"btn-spinner\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 265,\n                columnNumber: 21\n              }, this), \"Cleaning...\"]\n            }, void 0, true) : `Clean Up ${orphanedCount.toLocaleString()} Orphaned Transactions`\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 258,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 257,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 248,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 247,\n      columnNumber: 9\n    }, this), uploadedFiles.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"no-files\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"no-files-icon\",\n        children: \"\\uD83D\\uDCC1\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 279,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n        children: \"No Files Uploaded\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 280,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"Upload your first CSV file to see it listed here.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 281,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 278,\n      columnNumber: 9\n    }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"files-table-container\",\n      children: /*#__PURE__*/_jsxDEV(\"table\", {\n        className: \"files-table\",\n        children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n          children: /*#__PURE__*/_jsxDEV(\"tr\", {\n            children: [/*#__PURE__*/_jsxDEV(\"th\", {\n              children: \"File Name\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 288,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              children: \"Account\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 289,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              children: \"Upload Date\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 290,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              children: \"Transactions\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 291,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              children: \"File Size\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 292,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              children: \"Actions\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 293,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 287,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 286,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n          children: uploadedFiles.map(file => /*#__PURE__*/_jsxDEV(\"tr\", {\n            className: deleting === file.id ? 'deleting' : '',\n            children: [/*#__PURE__*/_jsxDEV(\"td\", {\n              className: \"file-name-col\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"file-info\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"file-icon\",\n                  children: \"\\uD83D\\uDCC4\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 301,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"file-details\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"file-name\",\n                    children: file.fileName\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 303,\n                    columnNumber: 25\n                  }, this), file.checksum && /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"file-checksum\",\n                    children: [\"ID: \", file.checksum]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 305,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 302,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 300,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 299,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n              className: \"account-col\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"account-info\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"account-name\",\n                  children: file.accountName\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 312,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"account-id\",\n                  children: file.accountId\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 313,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 311,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 310,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n              className: \"date-col\",\n              children: formatDate(file.uploadDate)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 316,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n              className: \"transactions-col\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"transaction-count\",\n                children: file.transactionCount.toLocaleString()\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 320,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 319,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n              className: \"size-col\",\n              children: formatFileSize(file.fileSize)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 324,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n              className: \"actions-col\",\n              children: /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => handleDeleteClick(file),\n                disabled: deleting === file.id,\n                className: \"btn btn-danger btn-sm\",\n                title: \"Delete file and all associated transactions\",\n                children: deleting === file.id ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"btn-spinner\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 336,\n                    columnNumber: 27\n                  }, this), \"Deleting...\"]\n                }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n                  children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                    width: \"16\",\n                    height: \"16\",\n                    viewBox: \"0 0 24 24\",\n                    fill: \"none\",\n                    stroke: \"currentColor\",\n                    strokeWidth: \"2\",\n                    children: [/*#__PURE__*/_jsxDEV(\"polyline\", {\n                      points: \"3,6 5,6 21,6\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 342,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                      d: \"M19,6V20a2,2,0,0,1-2,2H7a2,2,0,0,1-2-2V6M8,6V4a2,2,0,0,1,2-2h4a2,2,0,0,1,2,2V6\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 343,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"line\", {\n                      x1: \"10\",\n                      y1: \"11\",\n                      x2: \"10\",\n                      y2: \"17\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 344,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"line\", {\n                      x1: \"14\",\n                      y1: \"11\",\n                      x2: \"14\",\n                      y2: \"17\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 345,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 341,\n                    columnNumber: 27\n                  }, this), \"Delete\"]\n                }, void 0, true)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 328,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 327,\n              columnNumber: 19\n            }, this)]\n          }, file.id, true, {\n            fileName: _jsxFileName,\n            lineNumber: 298,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 296,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 285,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 284,\n      columnNumber: 9\n    }, this), deleteConfirmation && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"modal-overlay\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"modal-content\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"modal-header\",\n          children: /*#__PURE__*/_jsxDEV(\"h3\", {\n            children: deleteConfirmation.step === 1 ? 'Confirm File Deletion' : 'Final Confirmation Required'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 364,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 363,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"modal-body\",\n          children: deleteConfirmation.step === 1 ? /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"warning-icon\",\n              children: \"\\u26A0\\uFE0F\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 371,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: [\"Are you sure you want to delete the file \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                children: [\"\\\"\", deleteConfirmation.fileName, \"\\\"\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 373,\n                columnNumber: 62\n              }, this), \"?\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 372,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"warning-text\",\n              children: \"This action will permanently remove the file record and all associated transactions from storage.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 375,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"danger-icon\",\n              children: \"\\uD83D\\uDEA8\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 381,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"FINAL WARNING:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 383,\n                columnNumber: 21\n              }, this), \" You are about to permanently delete:\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 382,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n              className: \"deletion-details\",\n              children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                children: [\"File: \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: deleteConfirmation.fileName\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 386,\n                  columnNumber: 31\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 386,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"All transactions imported from this file\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 387,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"All associated transaction data\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 388,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 385,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"final-warning\",\n              children: [\"This action \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"CANNOT BE UNDONE\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 391,\n                columnNumber: 33\n              }, this), \". Are you absolutely certain?\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 390,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 368,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"modal-actions\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: cancelDeletion,\n            className: \"btn btn-secondary\",\n            children: \"Cancel\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 397,\n            columnNumber: 15\n          }, this), deleteConfirmation.step === 1 ? /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: handleFirstConfirmation,\n            className: \"btn btn-warning\",\n            children: \"Yes, Continue\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 401,\n            columnNumber: 17\n          }, this) : /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: handleFinalDeletion,\n            className: \"btn btn-danger\",\n            children: \"Delete Permanently\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 405,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 396,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 362,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 361,\n      columnNumber: 9\n    }, this), deletionResult && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"modal-overlay\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"modal-content\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"modal-header\",\n          children: /*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"Deletion Report\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 419,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 418,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"modal-body\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: `status-icon ${deletionResult.report.isVerified ? 'success' : 'warning'}`,\n            children: deletionResult.report.isVerified ? '✅' : '⚠️'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 422,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n            children: [\"File: \", deletionResult.report.fileName]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 426,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"deletion-stats\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"stat-row\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"Expected to delete:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 430,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"strong\", {\n                children: [deletionResult.report.expectedTransactionCount, \" transactions\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 431,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 429,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"stat-row\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"Actually deleted:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 434,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"strong\", {\n                children: [deletionResult.report.actualDeletedCount, \" transactions\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 435,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 433,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"stat-row\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"Total before:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 438,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"strong\", {\n                children: [deletionResult.report.totalTransactionsBefore, \" transactions\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 439,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 437,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"stat-row\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"Total after:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 442,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"strong\", {\n                children: [deletionResult.report.totalTransactionsAfter, \" transactions\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 443,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 441,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"stat-row\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"Backup created:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 446,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"strong\", {\n                children: deletionResult.report.backupCreated ? 'Yes' : 'No'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 447,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 445,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 428,\n            columnNumber: 15\n          }, this), deletionResult.report.isVerified ? /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"success-message\",\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              children: [\"\\u2705 \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Deletion completed successfully!\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 453,\n                columnNumber: 24\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 453,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: [\"All transaction counts match expectations. The file and its \", deletionResult.report.actualDeletedCount, \" transactions have been safely removed.\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 454,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"The Transactions page has been automatically updated\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 455,\n                columnNumber: 22\n              }, this), \" to reflect these changes.\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 455,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 452,\n            columnNumber: 17\n          }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"warning-message\",\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              children: [\"\\u26A0\\uFE0F \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Deletion verification failed!\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 459,\n                columnNumber: 25\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 459,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"The number of deleted transactions doesn't match expectations. This could indicate an issue with the deletion process.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 460,\n              columnNumber: 19\n            }, this), deletionResult.report.backupCreated && /*#__PURE__*/_jsxDEV(\"p\", {\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"A backup was created before deletion.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 462,\n                columnNumber: 24\n              }, this), \" You can restore the file if needed.\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 462,\n              columnNumber: 21\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 458,\n            columnNumber: 17\n          }, this), deletionResult.report.error && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"error-message\",\n            children: /*#__PURE__*/_jsxDEV(\"p\", {\n              children: [\"\\u274C \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Error:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 469,\n                columnNumber: 24\n              }, this), \" \", deletionResult.report.error]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 469,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 468,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 421,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"modal-actions\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: closeDeletionResult,\n            className: \"btn btn-secondary\",\n            children: \"Close\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 475,\n            columnNumber: 15\n          }, this), deletionResult.report.backupCreated && deletionResult.showRestoreOption && /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => handleRestore(deletionResult.report.backupKey),\n            disabled: restoring === deletionResult.report.backupKey,\n            className: \"btn btn-warning\",\n            children: restoring === deletionResult.report.backupKey ? /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"btn-spinner\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 487,\n                columnNumber: 23\n              }, this), \"Restoring...\"]\n            }, void 0, true) : 'Restore File'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 480,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 474,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 417,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 416,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 221,\n    columnNumber: 5\n  }, this);\n};\n_s(FileManager, \"4fb5hVqxuciR/YN2kqdgoHDZ1ok=\");\n_c = FileManager;\nvar _c;\n$RefreshReg$(_c, \"FileManager\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "fileStorageService", "transactionStorageService", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "FileManager", "onFileDeleted", "_s", "uploadedFiles", "setUploadedFiles", "loading", "setLoading", "deleteConfirmation", "setDeleteConfirmation", "deleting", "setDeleting", "deletionResult", "setDeletionResult", "restoring", "setRestoring", "orphanedCount", "setOrphanedCount", "cleaningOrphaned", "setCleaningOrphaned", "loadFiles", "files", "getAllUploadedFiles", "sort", "a", "b", "Date", "uploadDate", "getTime", "orphanedTransactions", "getOrphanedTransactions", "length", "error", "console", "formatFileSize", "bytes", "k", "sizes", "i", "Math", "floor", "log", "parseFloat", "pow", "toFixed", "formatDate", "dateString", "date", "toLocaleDateString", "day", "month", "year", "hour", "minute", "handleDeleteClick", "file", "fileId", "id", "fileName", "step", "handleFirstConfirmation", "handleFinalDeletion", "result", "deleteUploadedFile", "success", "prev", "filter", "f", "report", "deletionReport", "showRestoreOption", "alert", "cancelDeletion", "handleRestore", "<PERSON><PERSON><PERSON>", "restoreResult", "restoreFromBackup", "restoredCount", "closeDeletionResult", "handleCleanupOrphaned", "confirmed", "window", "confirm", "deletedCount", "clearOrphanedTransactions", "stats", "getStorageStats", "className", "children", "_jsxFileName", "lineNumber", "columnNumber", "totalFiles", "totalTransactions", "toLocaleString", "totalSize", "onClick", "disabled", "map", "checksum", "accountName", "accountId", "transactionCount", "fileSize", "title", "width", "height", "viewBox", "fill", "stroke", "strokeWidth", "points", "d", "x1", "y1", "x2", "y2", "isVerified", "expectedTransactionCount", "actualDeletedCount", "totalTransactionsBefore", "totalTransactionsAfter", "backupCreated", "_c", "$RefreshReg$"], "sources": ["C:/tmsft/src/components/FileManager.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\r\nimport { fileStorageService, type UploadedFile, type DeletionReport } from '../services/fileStorageService';\r\nimport { transactionStorageService } from '../services/transactionStorageService';\r\nimport './FileManager.css';\r\n\r\ninterface FileManagerProps {\r\n  onFileDeleted?: (fileId: string) => void;\r\n}\r\n\r\ninterface DeleteConfirmation {\r\n  fileId: string;\r\n  fileName: string;\r\n  step: 1 | 2;\r\n}\r\n\r\ninterface DeletionResult {\r\n  report: DeletionReport;\r\n  showRestoreOption: boolean;\r\n}\r\n\r\nexport const FileManager: React.FC<FileManagerProps> = ({ onFileDeleted }) => {\r\n  const [uploadedFiles, setUploadedFiles] = useState<UploadedFile[]>([]);\r\n  const [loading, setLoading] = useState(true);\r\n  const [deleteConfirmation, setDeleteConfirmation] = useState<DeleteConfirmation | null>(null);\r\n  const [deleting, setDeleting] = useState<string | null>(null);\r\n  const [deletionResult, setDeletionResult] = useState<DeletionResult | null>(null);\r\n  const [restoring, setRestoring] = useState<string | null>(null);\r\n  const [orphanedCount, setOrphanedCount] = useState(0);\r\n  const [cleaningOrphaned, setCleaningOrphaned] = useState(false);\r\n\r\n  // Load uploaded files\r\n  const loadFiles = () => {\r\n    try {\r\n      setLoading(true);\r\n      const files = fileStorageService.getAllUploadedFiles();\r\n      // Sort by upload date (newest first)\r\n      files.sort((a, b) => new Date(b.uploadDate).getTime() - new Date(a.uploadDate).getTime());\r\n      setUploadedFiles(files);\r\n      \r\n      // Count orphaned transactions (old data without file IDs)\r\n      const orphanedTransactions = transactionStorageService.getOrphanedTransactions();\r\n      setOrphanedCount(orphanedTransactions.length);\r\n    } catch (error) {\r\n      console.error('Error loading files:', error);\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    loadFiles();\r\n  }, []);\r\n\r\n  // Format file size\r\n  const formatFileSize = (bytes: number): string => {\r\n    if (bytes === 0) return '0 Bytes';\r\n    const k = 1024;\r\n    const sizes = ['Bytes', 'KB', 'MB', 'GB'];\r\n    const i = Math.floor(Math.log(bytes) / Math.log(k));\r\n    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\r\n  };\r\n\r\n  // Format date\r\n  const formatDate = (dateString: string): string => {\r\n    const date = new Date(dateString);\r\n    return date.toLocaleDateString('en-GB', {\r\n      day: '2-digit',\r\n      month: '2-digit',\r\n      year: 'numeric',\r\n      hour: '2-digit',\r\n      minute: '2-digit'\r\n    });\r\n  };\r\n\r\n  // Handle delete initiation\r\n  const handleDeleteClick = (file: UploadedFile) => {\r\n    setDeleteConfirmation({\r\n      fileId: file.id,\r\n      fileName: file.fileName,\r\n      step: 1\r\n    });\r\n  };\r\n\r\n  // Handle first confirmation\r\n  const handleFirstConfirmation = () => {\r\n    if (deleteConfirmation) {\r\n      setDeleteConfirmation({\r\n        ...deleteConfirmation,\r\n        step: 2\r\n      });\r\n    }\r\n  };\r\n\r\n  // Handle final deletion\r\n  const handleFinalDeletion = async () => {\r\n    if (!deleteConfirmation) return;\r\n\r\n    try {\r\n      setDeleting(deleteConfirmation.fileId);\r\n      \r\n      const result = fileStorageService.deleteUploadedFile(deleteConfirmation.fileId);\r\n      \r\n      if (result.success) {\r\n        // Remove from local state\r\n        setUploadedFiles(prev => prev.filter(f => f.id !== deleteConfirmation.fileId));\r\n        \r\n        // Show deletion result\r\n        setDeletionResult({\r\n          report: result.deletionReport,\r\n          showRestoreOption: true\r\n        });\r\n        \r\n        // Notify parent component (this will refresh transactions)\r\n        if (onFileDeleted) {\r\n          onFileDeleted(deleteConfirmation.fileId);\r\n        }\r\n        \r\n        console.log(`Successfully deleted file: ${deleteConfirmation.fileName}`);\r\n      } else {\r\n        alert(`Failed to delete file: ${result.deletionReport.error || 'Unknown error'}`);\r\n      }\r\n    } catch (error) {\r\n      console.error('Error deleting file:', error);\r\n      alert('An error occurred while deleting the file.');\r\n    } finally {\r\n      setDeleting(null);\r\n      setDeleteConfirmation(null);\r\n    }\r\n  };\r\n\r\n  // Cancel deletion\r\n  const cancelDeletion = () => {\r\n    setDeleteConfirmation(null);\r\n  };\r\n\r\n  // Handle restore from backup\r\n  const handleRestore = async (backupKey: string) => {\r\n    if (!deletionResult) return;\r\n\r\n    try {\r\n      setRestoring(backupKey);\r\n      \r\n      const restoreResult = fileStorageService.restoreFromBackup(backupKey);\r\n      \r\n      if (restoreResult.success) {\r\n        // Reload files to show restored file\r\n        loadFiles();\r\n        \r\n        // Notify parent component to refresh transactions\r\n        if (onFileDeleted) {\r\n          onFileDeleted('restore-trigger');\r\n        }\r\n        \r\n        // Close deletion result dialog\r\n        setDeletionResult(null);\r\n        \r\n        alert(`Successfully restored ${restoreResult.restoredCount} transactions and file record. The Transactions page has been updated.`);\r\n      } else {\r\n        alert(`Failed to restore: ${restoreResult.error || 'Unknown error'}`);\r\n      }\r\n    } catch (error) {\r\n      console.error('Error restoring file:', error);\r\n      alert('An error occurred while restoring the file.');\r\n    } finally {\r\n      setRestoring(null);\r\n    }\r\n  };\r\n\r\n  // Close deletion result dialog\r\n  const closeDeletionResult = () => {\r\n    setDeletionResult(null);\r\n  };\r\n\r\n  // Handle cleanup of orphaned transactions\r\n  const handleCleanupOrphaned = async () => {\r\n    if (orphanedCount === 0) return;\r\n    \r\n    const confirmed = window.confirm(\r\n      `Are you sure you want to delete ${orphanedCount} orphaned transactions?\\n\\n` +\r\n      'These are transactions that were imported before the file tracking system was implemented. ' +\r\n      'They cannot be linked to any file and should be cleaned up.\\n\\n' +\r\n      'This action cannot be undone.'\r\n    );\r\n    \r\n    if (!confirmed) return;\r\n    \r\n    try {\r\n      setCleaningOrphaned(true);\r\n      const deletedCount = transactionStorageService.clearOrphanedTransactions();\r\n      \r\n      // Refresh data\r\n      loadFiles();\r\n      \r\n      // Notify parent component to refresh transactions\r\n      if (onFileDeleted) {\r\n        onFileDeleted('orphaned-cleanup');\r\n      }\r\n      \r\n      alert(`Successfully deleted ${deletedCount} orphaned transactions.`);\r\n    } catch (error) {\r\n      console.error('Error cleaning orphaned transactions:', error);\r\n      alert('Failed to clean orphaned transactions. Please try again.');\r\n    } finally {\r\n      setCleaningOrphaned(false);\r\n    }\r\n  };\r\n\r\n  // Get storage statistics\r\n  const stats = fileStorageService.getStorageStats();\r\n\r\n  if (loading) {\r\n    return (\r\n      <div className=\"file-manager-loading\">\r\n        <div className=\"loading-spinner\"></div>\r\n        <p>Loading uploaded files...</p>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className=\"file-manager\">\r\n      <div className=\"file-manager-header\">\r\n        <div className=\"file-manager-title-section\">\r\n          <h2 className=\"file-manager-title\">File Management</h2>\r\n          <p className=\"file-manager-description\">\r\n            Manage your uploaded CSV files and their associated transaction data\r\n          </p>\r\n        </div>\r\n        <div className=\"file-manager-stats\">\r\n          <div className=\"stat-card\">\r\n            <div className=\"stat-value\">{stats.totalFiles}</div>\r\n            <div className=\"stat-label\">Files Uploaded</div>\r\n          </div>\r\n          <div className=\"stat-card\">\r\n            <div className=\"stat-value\">{stats.totalTransactions.toLocaleString()}</div>\r\n            <div className=\"stat-label\">Total Transactions</div>\r\n          </div>\r\n          <div className=\"stat-card\">\r\n            <div className=\"stat-value\">{formatFileSize(stats.totalSize)}</div>\r\n            <div className=\"stat-label\">Storage Used</div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Orphaned Transactions Warning */}\r\n      {orphanedCount > 0 && (\r\n        <div className=\"orphaned-warning\">\r\n          <div className=\"warning-content\">\r\n            <div className=\"warning-icon\">⚠️</div>\r\n            <div className=\"warning-message\">\r\n              <strong>Orphaned Transaction Data Detected</strong>\r\n              <p>\r\n                Found {orphanedCount.toLocaleString()} transactions that were imported before the file tracking system was implemented. \r\n                These transactions cannot be properly managed and should be cleaned up.\r\n              </p>\r\n            </div>\r\n            <div className=\"warning-actions\">\r\n              <button \r\n                onClick={handleCleanupOrphaned}\r\n                disabled={cleaningOrphaned}\r\n                className=\"btn btn-warning btn-sm\"\r\n              >\r\n                {cleaningOrphaned ? (\r\n                  <>\r\n                    <div className=\"btn-spinner\"></div>\r\n                    Cleaning...\r\n                  </>\r\n                ) : (\r\n                  `Clean Up ${orphanedCount.toLocaleString()} Orphaned Transactions`\r\n                )}\r\n              </button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      )}\r\n\r\n      {uploadedFiles.length === 0 ? (\r\n        <div className=\"no-files\">\r\n          <div className=\"no-files-icon\">📁</div>\r\n          <h3>No Files Uploaded</h3>\r\n          <p>Upload your first CSV file to see it listed here.</p>\r\n        </div>\r\n      ) : (\r\n        <div className=\"files-table-container\">\r\n          <table className=\"files-table\">\r\n            <thead>\r\n              <tr>\r\n                <th>File Name</th>\r\n                <th>Account</th>\r\n                <th>Upload Date</th>\r\n                <th>Transactions</th>\r\n                <th>File Size</th>\r\n                <th>Actions</th>\r\n              </tr>\r\n            </thead>\r\n            <tbody>\r\n              {uploadedFiles.map((file) => (\r\n                <tr key={file.id} className={deleting === file.id ? 'deleting' : ''}>\r\n                  <td className=\"file-name-col\">\r\n                    <div className=\"file-info\">\r\n                      <div className=\"file-icon\">📄</div>\r\n                      <div className=\"file-details\">\r\n                        <div className=\"file-name\">{file.fileName}</div>\r\n                        {file.checksum && (\r\n                          <div className=\"file-checksum\">ID: {file.checksum}</div>\r\n                        )}\r\n                      </div>\r\n                    </div>\r\n                  </td>\r\n                  <td className=\"account-col\">\r\n                    <div className=\"account-info\">\r\n                      <div className=\"account-name\">{file.accountName}</div>\r\n                      <div className=\"account-id\">{file.accountId}</div>\r\n                    </div>\r\n                  </td>\r\n                  <td className=\"date-col\">\r\n                    {formatDate(file.uploadDate)}\r\n                  </td>\r\n                  <td className=\"transactions-col\">\r\n                    <span className=\"transaction-count\">\r\n                      {file.transactionCount.toLocaleString()}\r\n                    </span>\r\n                  </td>\r\n                  <td className=\"size-col\">\r\n                    {formatFileSize(file.fileSize)}\r\n                  </td>\r\n                  <td className=\"actions-col\">\r\n                    <button\r\n                      onClick={() => handleDeleteClick(file)}\r\n                      disabled={deleting === file.id}\r\n                      className=\"btn btn-danger btn-sm\"\r\n                      title=\"Delete file and all associated transactions\"\r\n                    >\r\n                      {deleting === file.id ? (\r\n                        <>\r\n                          <div className=\"btn-spinner\"></div>\r\n                          Deleting...\r\n                        </>\r\n                      ) : (\r\n                        <>\r\n                          <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\">\r\n                            <polyline points=\"3,6 5,6 21,6\"></polyline>\r\n                            <path d=\"M19,6V20a2,2,0,0,1-2,2H7a2,2,0,0,1-2-2V6M8,6V4a2,2,0,0,1,2-2h4a2,2,0,0,1,2,2V6\"></path>\r\n                            <line x1=\"10\" y1=\"11\" x2=\"10\" y2=\"17\"></line>\r\n                            <line x1=\"14\" y1=\"11\" x2=\"14\" y2=\"17\"></line>\r\n                          </svg>\r\n                          Delete\r\n                        </>\r\n                      )}\r\n                    </button>\r\n                  </td>\r\n                </tr>\r\n              ))}\r\n            </tbody>\r\n          </table>\r\n        </div>\r\n      )}\r\n\r\n      {/* Delete Confirmation Modal */}\r\n      {deleteConfirmation && (\r\n        <div className=\"modal-overlay\">\r\n          <div className=\"modal-content\">\r\n            <div className=\"modal-header\">\r\n              <h3>\r\n                {deleteConfirmation.step === 1 ? 'Confirm File Deletion' : 'Final Confirmation Required'}\r\n              </h3>\r\n            </div>\r\n            <div className=\"modal-body\">\r\n              {deleteConfirmation.step === 1 ? (\r\n                <>\r\n                  <div className=\"warning-icon\">⚠️</div>\r\n                  <p>\r\n                    Are you sure you want to delete the file <strong>\"{deleteConfirmation.fileName}\"</strong>?\r\n                  </p>\r\n                  <p className=\"warning-text\">\r\n                    This action will permanently remove the file record and all associated transactions from storage.\r\n                  </p>\r\n                </>\r\n              ) : (\r\n                <>\r\n                  <div className=\"danger-icon\">🚨</div>\r\n                  <p>\r\n                    <strong>FINAL WARNING:</strong> You are about to permanently delete:\r\n                  </p>\r\n                  <ul className=\"deletion-details\">\r\n                    <li>File: <strong>{deleteConfirmation.fileName}</strong></li>\r\n                    <li>All transactions imported from this file</li>\r\n                    <li>All associated transaction data</li>\r\n                  </ul>\r\n                  <p className=\"final-warning\">\r\n                    This action <strong>CANNOT BE UNDONE</strong>. Are you absolutely certain?\r\n                  </p>\r\n                </>\r\n              )}\r\n            </div>\r\n            <div className=\"modal-actions\">\r\n              <button onClick={cancelDeletion} className=\"btn btn-secondary\">\r\n                Cancel\r\n              </button>\r\n              {deleteConfirmation.step === 1 ? (\r\n                <button onClick={handleFirstConfirmation} className=\"btn btn-warning\">\r\n                  Yes, Continue\r\n                </button>\r\n              ) : (\r\n                <button onClick={handleFinalDeletion} className=\"btn btn-danger\">\r\n                  Delete Permanently\r\n                </button>\r\n              )}\r\n            </div>\r\n          </div>\r\n        </div>\r\n      )}\r\n\r\n      {/* Deletion Result Modal */}\r\n      {deletionResult && (\r\n        <div className=\"modal-overlay\">\r\n          <div className=\"modal-content\">\r\n            <div className=\"modal-header\">\r\n              <h3>Deletion Report</h3>\r\n            </div>\r\n            <div className=\"modal-body\">\r\n              <div className={`status-icon ${deletionResult.report.isVerified ? 'success' : 'warning'}`}>\r\n                {deletionResult.report.isVerified ? '✅' : '⚠️'}\r\n              </div>\r\n              \r\n              <h4>File: {deletionResult.report.fileName}</h4>\r\n              \r\n              <div className=\"deletion-stats\">\r\n                <div className=\"stat-row\">\r\n                  <span>Expected to delete:</span>\r\n                  <strong>{deletionResult.report.expectedTransactionCount} transactions</strong>\r\n                </div>\r\n                <div className=\"stat-row\">\r\n                  <span>Actually deleted:</span>\r\n                  <strong>{deletionResult.report.actualDeletedCount} transactions</strong>\r\n                </div>\r\n                <div className=\"stat-row\">\r\n                  <span>Total before:</span>\r\n                  <strong>{deletionResult.report.totalTransactionsBefore} transactions</strong>\r\n                </div>\r\n                <div className=\"stat-row\">\r\n                  <span>Total after:</span>\r\n                  <strong>{deletionResult.report.totalTransactionsAfter} transactions</strong>\r\n                </div>\r\n                <div className=\"stat-row\">\r\n                  <span>Backup created:</span>\r\n                  <strong>{deletionResult.report.backupCreated ? 'Yes' : 'No'}</strong>\r\n                </div>\r\n              </div>\r\n\r\n              {deletionResult.report.isVerified ? (\r\n                <div className=\"success-message\">\r\n                  <p>✅ <strong>Deletion completed successfully!</strong></p>\r\n                  <p>All transaction counts match expectations. The file and its {deletionResult.report.actualDeletedCount} transactions have been safely removed.</p>\r\n                  <p><strong>The Transactions page has been automatically updated</strong> to reflect these changes.</p>\r\n                </div>\r\n              ) : (\r\n                <div className=\"warning-message\">\r\n                  <p>⚠️ <strong>Deletion verification failed!</strong></p>\r\n                  <p>The number of deleted transactions doesn't match expectations. This could indicate an issue with the deletion process.</p>\r\n                  {deletionResult.report.backupCreated && (\r\n                    <p><strong>A backup was created before deletion.</strong> You can restore the file if needed.</p>\r\n                  )}\r\n                </div>\r\n              )}\r\n\r\n              {deletionResult.report.error && (\r\n                <div className=\"error-message\">\r\n                  <p>❌ <strong>Error:</strong> {deletionResult.report.error}</p>\r\n                </div>\r\n              )}\r\n            </div>\r\n            \r\n            <div className=\"modal-actions\">\r\n              <button onClick={closeDeletionResult} className=\"btn btn-secondary\">\r\n                Close\r\n              </button>\r\n              \r\n              {deletionResult.report.backupCreated && deletionResult.showRestoreOption && (\r\n                <button \r\n                  onClick={() => handleRestore(deletionResult.report.backupKey)}\r\n                  disabled={restoring === deletionResult.report.backupKey}\r\n                  className=\"btn btn-warning\"\r\n                >\r\n                  {restoring === deletionResult.report.backupKey ? (\r\n                    <>\r\n                      <div className=\"btn-spinner\"></div>\r\n                      Restoring...\r\n                    </>\r\n                  ) : (\r\n                    'Restore File'\r\n                  )}\r\n                </button>\r\n              )}\r\n            </div>\r\n          </div>\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n}; "], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,kBAAkB,QAAgD,gCAAgC;AAC3G,SAASC,yBAAyB,QAAQ,uCAAuC;AACjF,OAAO,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAiB3B,OAAO,MAAMC,WAAuC,GAAGA,CAAC;EAAEC;AAAc,CAAC,KAAK;EAAAC,EAAA;EAC5E,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAGZ,QAAQ,CAAiB,EAAE,CAAC;EACtE,MAAM,CAACa,OAAO,EAAEC,UAAU,CAAC,GAAGd,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACe,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGhB,QAAQ,CAA4B,IAAI,CAAC;EAC7F,MAAM,CAACiB,QAAQ,EAAEC,WAAW,CAAC,GAAGlB,QAAQ,CAAgB,IAAI,CAAC;EAC7D,MAAM,CAACmB,cAAc,EAAEC,iBAAiB,CAAC,GAAGpB,QAAQ,CAAwB,IAAI,CAAC;EACjF,MAAM,CAACqB,SAAS,EAAEC,YAAY,CAAC,GAAGtB,QAAQ,CAAgB,IAAI,CAAC;EAC/D,MAAM,CAACuB,aAAa,EAAEC,gBAAgB,CAAC,GAAGxB,QAAQ,CAAC,CAAC,CAAC;EACrD,MAAM,CAACyB,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG1B,QAAQ,CAAC,KAAK,CAAC;;EAE/D;EACA,MAAM2B,SAAS,GAAGA,CAAA,KAAM;IACtB,IAAI;MACFb,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMc,KAAK,GAAG1B,kBAAkB,CAAC2B,mBAAmB,CAAC,CAAC;MACtD;MACAD,KAAK,CAACE,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK,IAAIC,IAAI,CAACD,CAAC,CAACE,UAAU,CAAC,CAACC,OAAO,CAAC,CAAC,GAAG,IAAIF,IAAI,CAACF,CAAC,CAACG,UAAU,CAAC,CAACC,OAAO,CAAC,CAAC,CAAC;MACzFvB,gBAAgB,CAACgB,KAAK,CAAC;;MAEvB;MACA,MAAMQ,oBAAoB,GAAGjC,yBAAyB,CAACkC,uBAAuB,CAAC,CAAC;MAChFb,gBAAgB,CAACY,oBAAoB,CAACE,MAAM,CAAC;IAC/C,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;IAC9C,CAAC,SAAS;MACRzB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAEDb,SAAS,CAAC,MAAM;IACd0B,SAAS,CAAC,CAAC;EACb,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMc,cAAc,GAAIC,KAAa,IAAa;IAChD,IAAIA,KAAK,KAAK,CAAC,EAAE,OAAO,SAAS;IACjC,MAAMC,CAAC,GAAG,IAAI;IACd,MAAMC,KAAK,GAAG,CAAC,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;IACzC,MAAMC,CAAC,GAAGC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,GAAG,CAACN,KAAK,CAAC,GAAGI,IAAI,CAACE,GAAG,CAACL,CAAC,CAAC,CAAC;IACnD,OAAOM,UAAU,CAAC,CAACP,KAAK,GAAGI,IAAI,CAACI,GAAG,CAACP,CAAC,EAAEE,CAAC,CAAC,EAAEM,OAAO,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,GAAGP,KAAK,CAACC,CAAC,CAAC;EACzE,CAAC;;EAED;EACA,MAAMO,UAAU,GAAIC,UAAkB,IAAa;IACjD,MAAMC,IAAI,GAAG,IAAIrB,IAAI,CAACoB,UAAU,CAAC;IACjC,OAAOC,IAAI,CAACC,kBAAkB,CAAC,OAAO,EAAE;MACtCC,GAAG,EAAE,SAAS;MACdC,KAAK,EAAE,SAAS;MAChBC,IAAI,EAAE,SAAS;MACfC,IAAI,EAAE,SAAS;MACfC,MAAM,EAAE;IACV,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAMC,iBAAiB,GAAIC,IAAkB,IAAK;IAChD9C,qBAAqB,CAAC;MACpB+C,MAAM,EAAED,IAAI,CAACE,EAAE;MACfC,QAAQ,EAAEH,IAAI,CAACG,QAAQ;MACvBC,IAAI,EAAE;IACR,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAMC,uBAAuB,GAAGA,CAAA,KAAM;IACpC,IAAIpD,kBAAkB,EAAE;MACtBC,qBAAqB,CAAC;QACpB,GAAGD,kBAAkB;QACrBmD,IAAI,EAAE;MACR,CAAC,CAAC;IACJ;EACF,CAAC;;EAED;EACA,MAAME,mBAAmB,GAAG,MAAAA,CAAA,KAAY;IACtC,IAAI,CAACrD,kBAAkB,EAAE;IAEzB,IAAI;MACFG,WAAW,CAACH,kBAAkB,CAACgD,MAAM,CAAC;MAEtC,MAAMM,MAAM,GAAGnE,kBAAkB,CAACoE,kBAAkB,CAACvD,kBAAkB,CAACgD,MAAM,CAAC;MAE/E,IAAIM,MAAM,CAACE,OAAO,EAAE;QAClB;QACA3D,gBAAgB,CAAC4D,IAAI,IAAIA,IAAI,CAACC,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACV,EAAE,KAAKjD,kBAAkB,CAACgD,MAAM,CAAC,CAAC;;QAE9E;QACA3C,iBAAiB,CAAC;UAChBuD,MAAM,EAAEN,MAAM,CAACO,cAAc;UAC7BC,iBAAiB,EAAE;QACrB,CAAC,CAAC;;QAEF;QACA,IAAIpE,aAAa,EAAE;UACjBA,aAAa,CAACM,kBAAkB,CAACgD,MAAM,CAAC;QAC1C;QAEAvB,OAAO,CAACQ,GAAG,CAAC,8BAA8BjC,kBAAkB,CAACkD,QAAQ,EAAE,CAAC;MAC1E,CAAC,MAAM;QACLa,KAAK,CAAC,0BAA0BT,MAAM,CAACO,cAAc,CAACrC,KAAK,IAAI,eAAe,EAAE,CAAC;MACnF;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC5CuC,KAAK,CAAC,4CAA4C,CAAC;IACrD,CAAC,SAAS;MACR5D,WAAW,CAAC,IAAI,CAAC;MACjBF,qBAAqB,CAAC,IAAI,CAAC;IAC7B;EACF,CAAC;;EAED;EACA,MAAM+D,cAAc,GAAGA,CAAA,KAAM;IAC3B/D,qBAAqB,CAAC,IAAI,CAAC;EAC7B,CAAC;;EAED;EACA,MAAMgE,aAAa,GAAG,MAAOC,SAAiB,IAAK;IACjD,IAAI,CAAC9D,cAAc,EAAE;IAErB,IAAI;MACFG,YAAY,CAAC2D,SAAS,CAAC;MAEvB,MAAMC,aAAa,GAAGhF,kBAAkB,CAACiF,iBAAiB,CAACF,SAAS,CAAC;MAErE,IAAIC,aAAa,CAACX,OAAO,EAAE;QACzB;QACA5C,SAAS,CAAC,CAAC;;QAEX;QACA,IAAIlB,aAAa,EAAE;UACjBA,aAAa,CAAC,iBAAiB,CAAC;QAClC;;QAEA;QACAW,iBAAiB,CAAC,IAAI,CAAC;QAEvB0D,KAAK,CAAC,yBAAyBI,aAAa,CAACE,aAAa,wEAAwE,CAAC;MACrI,CAAC,MAAM;QACLN,KAAK,CAAC,sBAAsBI,aAAa,CAAC3C,KAAK,IAAI,eAAe,EAAE,CAAC;MACvE;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MAC7CuC,KAAK,CAAC,6CAA6C,CAAC;IACtD,CAAC,SAAS;MACRxD,YAAY,CAAC,IAAI,CAAC;IACpB;EACF,CAAC;;EAED;EACA,MAAM+D,mBAAmB,GAAGA,CAAA,KAAM;IAChCjE,iBAAiB,CAAC,IAAI,CAAC;EACzB,CAAC;;EAED;EACA,MAAMkE,qBAAqB,GAAG,MAAAA,CAAA,KAAY;IACxC,IAAI/D,aAAa,KAAK,CAAC,EAAE;IAEzB,MAAMgE,SAAS,GAAGC,MAAM,CAACC,OAAO,CAC9B,mCAAmClE,aAAa,6BAA6B,GAC7E,6FAA6F,GAC7F,iEAAiE,GACjE,+BACF,CAAC;IAED,IAAI,CAACgE,SAAS,EAAE;IAEhB,IAAI;MACF7D,mBAAmB,CAAC,IAAI,CAAC;MACzB,MAAMgE,YAAY,GAAGvF,yBAAyB,CAACwF,yBAAyB,CAAC,CAAC;;MAE1E;MACAhE,SAAS,CAAC,CAAC;;MAEX;MACA,IAAIlB,aAAa,EAAE;QACjBA,aAAa,CAAC,kBAAkB,CAAC;MACnC;MAEAqE,KAAK,CAAC,wBAAwBY,YAAY,yBAAyB,CAAC;IACtE,CAAC,CAAC,OAAOnD,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,uCAAuC,EAAEA,KAAK,CAAC;MAC7DuC,KAAK,CAAC,0DAA0D,CAAC;IACnE,CAAC,SAAS;MACRpD,mBAAmB,CAAC,KAAK,CAAC;IAC5B;EACF,CAAC;;EAED;EACA,MAAMkE,KAAK,GAAG1F,kBAAkB,CAAC2F,eAAe,CAAC,CAAC;EAElD,IAAIhF,OAAO,EAAE;IACX,oBACER,OAAA;MAAKyF,SAAS,EAAC,sBAAsB;MAAAC,QAAA,gBACnC1F,OAAA;QAAKyF,SAAS,EAAC;MAAiB;QAAA7B,QAAA,EAAA+B,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACvC7F,OAAA;QAAA0F,QAAA,EAAG;MAAyB;QAAA9B,QAAA,EAAA+B,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAjC,QAAA,EAAA+B,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC7B,CAAC;EAEV;EAEA,oBACE7F,OAAA;IAAKyF,SAAS,EAAC,cAAc;IAAAC,QAAA,gBAC3B1F,OAAA;MAAKyF,SAAS,EAAC,qBAAqB;MAAAC,QAAA,gBAClC1F,OAAA;QAAKyF,SAAS,EAAC,4BAA4B;QAAAC,QAAA,gBACzC1F,OAAA;UAAIyF,SAAS,EAAC,oBAAoB;UAAAC,QAAA,EAAC;QAAe;UAAA9B,QAAA,EAAA+B,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACvD7F,OAAA;UAAGyF,SAAS,EAAC,0BAA0B;UAAAC,QAAA,EAAC;QAExC;UAAA9B,QAAA,EAAA+B,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAjC,QAAA,EAAA+B,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eACN7F,OAAA;QAAKyF,SAAS,EAAC,oBAAoB;QAAAC,QAAA,gBACjC1F,OAAA;UAAKyF,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxB1F,OAAA;YAAKyF,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAEH,KAAK,CAACO;UAAU;YAAAlC,QAAA,EAAA+B,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACpD7F,OAAA;YAAKyF,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAC;UAAc;YAAA9B,QAAA,EAAA+B,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAjC,QAAA,EAAA+B,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7C,CAAC,eACN7F,OAAA;UAAKyF,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxB1F,OAAA;YAAKyF,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAEH,KAAK,CAACQ,iBAAiB,CAACC,cAAc,CAAC;UAAC;YAAApC,QAAA,EAAA+B,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC5E7F,OAAA;YAAKyF,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAC;UAAkB;YAAA9B,QAAA,EAAA+B,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAjC,QAAA,EAAA+B,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjD,CAAC,eACN7F,OAAA;UAAKyF,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxB1F,OAAA;YAAKyF,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAEtD,cAAc,CAACmD,KAAK,CAACU,SAAS;UAAC;YAAArC,QAAA,EAAA+B,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACnE7F,OAAA;YAAKyF,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAC;UAAY;YAAA9B,QAAA,EAAA+B,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAjC,QAAA,EAAA+B,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3C,CAAC;MAAA;QAAAjC,QAAA,EAAA+B,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAjC,QAAA,EAAA+B,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGL3E,aAAa,GAAG,CAAC,iBAChBlB,OAAA;MAAKyF,SAAS,EAAC,kBAAkB;MAAAC,QAAA,eAC/B1F,OAAA;QAAKyF,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAC9B1F,OAAA;UAAKyF,SAAS,EAAC,cAAc;UAAAC,QAAA,EAAC;QAAE;UAAA9B,QAAA,EAAA+B,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACtC7F,OAAA;UAAKyF,SAAS,EAAC,iBAAiB;UAAAC,QAAA,gBAC9B1F,OAAA;YAAA0F,QAAA,EAAQ;UAAkC;YAAA9B,QAAA,EAAA+B,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACnD7F,OAAA;YAAA0F,QAAA,GAAG,QACK,EAACxE,aAAa,CAAC8E,cAAc,CAAC,CAAC,EAAC,2JAExC;UAAA;YAAApC,QAAA,EAAA+B,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAjC,QAAA,EAAA+B,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eACN7F,OAAA;UAAKyF,SAAS,EAAC,iBAAiB;UAAAC,QAAA,eAC9B1F,OAAA;YACEkG,OAAO,EAAEjB,qBAAsB;YAC/BkB,QAAQ,EAAE/E,gBAAiB;YAC3BqE,SAAS,EAAC,wBAAwB;YAAAC,QAAA,EAEjCtE,gBAAgB,gBACfpB,OAAA,CAAAE,SAAA;cAAAwF,QAAA,gBACE1F,OAAA;gBAAKyF,SAAS,EAAC;cAAa;gBAAA7B,QAAA,EAAA+B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAErC;YAAA,eAAE,CAAC,GAEH,YAAY3E,aAAa,CAAC8E,cAAc,CAAC,CAAC;UAC3C;YAAApC,QAAA,EAAA+B,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK;QAAC;UAAAjC,QAAA,EAAA+B,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAjC,QAAA,EAAA+B,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAjC,QAAA,EAAA+B,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,EAEAvF,aAAa,CAAC2B,MAAM,KAAK,CAAC,gBACzBjC,OAAA;MAAKyF,SAAS,EAAC,UAAU;MAAAC,QAAA,gBACvB1F,OAAA;QAAKyF,SAAS,EAAC,eAAe;QAAAC,QAAA,EAAC;MAAE;QAAA9B,QAAA,EAAA+B,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eACvC7F,OAAA;QAAA0F,QAAA,EAAI;MAAiB;QAAA9B,QAAA,EAAA+B,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC1B7F,OAAA;QAAA0F,QAAA,EAAG;MAAiD;QAAA9B,QAAA,EAAA+B,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAjC,QAAA,EAAA+B,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACrD,CAAC,gBAEN7F,OAAA;MAAKyF,SAAS,EAAC,uBAAuB;MAAAC,QAAA,eACpC1F,OAAA;QAAOyF,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC5B1F,OAAA;UAAA0F,QAAA,eACE1F,OAAA;YAAA0F,QAAA,gBACE1F,OAAA;cAAA0F,QAAA,EAAI;YAAS;cAAA9B,QAAA,EAAA+B,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAClB7F,OAAA;cAAA0F,QAAA,EAAI;YAAO;cAAA9B,QAAA,EAAA+B,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAChB7F,OAAA;cAAA0F,QAAA,EAAI;YAAW;cAAA9B,QAAA,EAAA+B,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACpB7F,OAAA;cAAA0F,QAAA,EAAI;YAAY;cAAA9B,QAAA,EAAA+B,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACrB7F,OAAA;cAAA0F,QAAA,EAAI;YAAS;cAAA9B,QAAA,EAAA+B,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAClB7F,OAAA;cAAA0F,QAAA,EAAI;YAAO;cAAA9B,QAAA,EAAA+B,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAjC,QAAA,EAAA+B,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACd;QAAC;UAAAjC,QAAA,EAAA+B,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eACR7F,OAAA;UAAA0F,QAAA,EACGpF,aAAa,CAAC8F,GAAG,CAAE3C,IAAI,iBACtBzD,OAAA;YAAkByF,SAAS,EAAE7E,QAAQ,KAAK6C,IAAI,CAACE,EAAE,GAAG,UAAU,GAAG,EAAG;YAAA+B,QAAA,gBAClE1F,OAAA;cAAIyF,SAAS,EAAC,eAAe;cAAAC,QAAA,eAC3B1F,OAAA;gBAAKyF,SAAS,EAAC,WAAW;gBAAAC,QAAA,gBACxB1F,OAAA;kBAAKyF,SAAS,EAAC,WAAW;kBAAAC,QAAA,EAAC;gBAAE;kBAAA9B,QAAA,EAAA+B,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACnC7F,OAAA;kBAAKyF,SAAS,EAAC,cAAc;kBAAAC,QAAA,gBAC3B1F,OAAA;oBAAKyF,SAAS,EAAC,WAAW;oBAAAC,QAAA,EAAEjC,IAAI,CAACG;kBAAQ;oBAAAA,QAAA,EAAA+B,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,EAC/CpC,IAAI,CAAC4C,QAAQ,iBACZrG,OAAA;oBAAKyF,SAAS,EAAC,eAAe;oBAAAC,QAAA,GAAC,MAAI,EAACjC,IAAI,CAAC4C,QAAQ;kBAAA;oBAAAzC,QAAA,EAAA+B,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CACxD;gBAAA;kBAAAjC,QAAA,EAAA+B,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC;cAAA;gBAAAjC,QAAA,EAAA+B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAjC,QAAA,EAAA+B,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eACL7F,OAAA;cAAIyF,SAAS,EAAC,aAAa;cAAAC,QAAA,eACzB1F,OAAA;gBAAKyF,SAAS,EAAC,cAAc;gBAAAC,QAAA,gBAC3B1F,OAAA;kBAAKyF,SAAS,EAAC,cAAc;kBAAAC,QAAA,EAAEjC,IAAI,CAAC6C;gBAAW;kBAAA1C,QAAA,EAAA+B,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACtD7F,OAAA;kBAAKyF,SAAS,EAAC,YAAY;kBAAAC,QAAA,EAAEjC,IAAI,CAAC8C;gBAAS;kBAAA3C,QAAA,EAAA+B,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAjC,QAAA,EAAA+B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/C;YAAC;cAAAjC,QAAA,EAAA+B,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eACL7F,OAAA;cAAIyF,SAAS,EAAC,UAAU;cAAAC,QAAA,EACrB3C,UAAU,CAACU,IAAI,CAAC5B,UAAU;YAAC;cAAA+B,QAAA,EAAA+B,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1B,CAAC,eACL7F,OAAA;cAAIyF,SAAS,EAAC,kBAAkB;cAAAC,QAAA,eAC9B1F,OAAA;gBAAMyF,SAAS,EAAC,mBAAmB;gBAAAC,QAAA,EAChCjC,IAAI,CAAC+C,gBAAgB,CAACR,cAAc,CAAC;cAAC;gBAAApC,QAAA,EAAA+B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnC;YAAC;cAAAjC,QAAA,EAAA+B,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,eACL7F,OAAA;cAAIyF,SAAS,EAAC,UAAU;cAAAC,QAAA,EACrBtD,cAAc,CAACqB,IAAI,CAACgD,QAAQ;YAAC;cAAA7C,QAAA,EAAA+B,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5B,CAAC,eACL7F,OAAA;cAAIyF,SAAS,EAAC,aAAa;cAAAC,QAAA,eACzB1F,OAAA;gBACEkG,OAAO,EAAEA,CAAA,KAAM1C,iBAAiB,CAACC,IAAI,CAAE;gBACvC0C,QAAQ,EAAEvF,QAAQ,KAAK6C,IAAI,CAACE,EAAG;gBAC/B8B,SAAS,EAAC,uBAAuB;gBACjCiB,KAAK,EAAC,6CAA6C;gBAAAhB,QAAA,EAElD9E,QAAQ,KAAK6C,IAAI,CAACE,EAAE,gBACnB3D,OAAA,CAAAE,SAAA;kBAAAwF,QAAA,gBACE1F,OAAA;oBAAKyF,SAAS,EAAC;kBAAa;oBAAA7B,QAAA,EAAA+B,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAErC;gBAAA,eAAE,CAAC,gBAEH7F,OAAA,CAAAE,SAAA;kBAAAwF,QAAA,gBACE1F,OAAA;oBAAK2G,KAAK,EAAC,IAAI;oBAACC,MAAM,EAAC,IAAI;oBAACC,OAAO,EAAC,WAAW;oBAACC,IAAI,EAAC,MAAM;oBAACC,MAAM,EAAC,cAAc;oBAACC,WAAW,EAAC,GAAG;oBAAAtB,QAAA,gBAC/F1F,OAAA;sBAAUiH,MAAM,EAAC;oBAAc;sBAAArD,QAAA,EAAA+B,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAW,CAAC,eAC3C7F,OAAA;sBAAMkH,CAAC,EAAC;oBAAgF;sBAAAtD,QAAA,EAAA+B,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC,eAChG7F,OAAA;sBAAMmH,EAAE,EAAC,IAAI;sBAACC,EAAE,EAAC,IAAI;sBAACC,EAAE,EAAC,IAAI;sBAACC,EAAE,EAAC;oBAAI;sBAAA1D,QAAA,EAAA+B,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC,eAC7C7F,OAAA;sBAAMmH,EAAE,EAAC,IAAI;sBAACC,EAAE,EAAC,IAAI;sBAACC,EAAE,EAAC,IAAI;sBAACC,EAAE,EAAC;oBAAI;sBAAA1D,QAAA,EAAA+B,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC;kBAAA;oBAAAjC,QAAA,EAAA+B,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1C,CAAC,UAER;gBAAA,eAAE;cACH;gBAAAjC,QAAA,EAAA+B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACK;YAAC;cAAAjC,QAAA,EAAA+B,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACP,CAAC;UAAA,GArDEpC,IAAI,CAACE,EAAE;YAAAC,QAAA,EAAA+B,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAsDZ,CACL;QAAC;UAAAjC,QAAA,EAAA+B,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG,CAAC;MAAA;QAAAjC,QAAA,EAAA+B,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAjC,QAAA,EAAA+B,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CACN,EAGAnF,kBAAkB,iBACjBV,OAAA;MAAKyF,SAAS,EAAC,eAAe;MAAAC,QAAA,eAC5B1F,OAAA;QAAKyF,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAC5B1F,OAAA;UAAKyF,SAAS,EAAC,cAAc;UAAAC,QAAA,eAC3B1F,OAAA;YAAA0F,QAAA,EACGhF,kBAAkB,CAACmD,IAAI,KAAK,CAAC,GAAG,uBAAuB,GAAG;UAA6B;YAAAD,QAAA,EAAA+B,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtF;QAAC;UAAAjC,QAAA,EAAA+B,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eACN7F,OAAA;UAAKyF,SAAS,EAAC,YAAY;UAAAC,QAAA,EACxBhF,kBAAkB,CAACmD,IAAI,KAAK,CAAC,gBAC5B7D,OAAA,CAAAE,SAAA;YAAAwF,QAAA,gBACE1F,OAAA;cAAKyF,SAAS,EAAC,cAAc;cAAAC,QAAA,EAAC;YAAE;cAAA9B,QAAA,EAAA+B,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACtC7F,OAAA;cAAA0F,QAAA,GAAG,2CACwC,eAAA1F,OAAA;gBAAA0F,QAAA,GAAQ,IAAC,EAAChF,kBAAkB,CAACkD,QAAQ,EAAC,IAAC;cAAA;gBAAAA,QAAA,EAAA+B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAC3F;YAAA;cAAAjC,QAAA,EAAA+B,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACJ7F,OAAA;cAAGyF,SAAS,EAAC,cAAc;cAAAC,QAAA,EAAC;YAE5B;cAAA9B,QAAA,EAAA+B,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA,eACJ,CAAC,gBAEH7F,OAAA,CAAAE,SAAA;YAAAwF,QAAA,gBACE1F,OAAA;cAAKyF,SAAS,EAAC,aAAa;cAAAC,QAAA,EAAC;YAAE;cAAA9B,QAAA,EAAA+B,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACrC7F,OAAA;cAAA0F,QAAA,gBACE1F,OAAA;gBAAA0F,QAAA,EAAQ;cAAc;gBAAA9B,QAAA,EAAA+B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,yCACjC;YAAA;cAAAjC,QAAA,EAAA+B,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACJ7F,OAAA;cAAIyF,SAAS,EAAC,kBAAkB;cAAAC,QAAA,gBAC9B1F,OAAA;gBAAA0F,QAAA,GAAI,QAAM,eAAA1F,OAAA;kBAAA0F,QAAA,EAAShF,kBAAkB,CAACkD;gBAAQ;kBAAAA,QAAA,EAAA+B,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAS,CAAC;cAAA;gBAAAjC,QAAA,EAAA+B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC7D7F,OAAA;gBAAA0F,QAAA,EAAI;cAAwC;gBAAA9B,QAAA,EAAA+B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACjD7F,OAAA;gBAAA0F,QAAA,EAAI;cAA+B;gBAAA9B,QAAA,EAAA+B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAjC,QAAA,EAAA+B,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtC,CAAC,eACL7F,OAAA;cAAGyF,SAAS,EAAC,eAAe;cAAAC,QAAA,GAAC,cACf,eAAA1F,OAAA;gBAAA0F,QAAA,EAAQ;cAAgB;gBAAA9B,QAAA,EAAA+B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,iCAC/C;YAAA;cAAAjC,QAAA,EAAA+B,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA,eACJ;QACH;UAAAjC,QAAA,EAAA+B,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eACN7F,OAAA;UAAKyF,SAAS,EAAC,eAAe;UAAAC,QAAA,gBAC5B1F,OAAA;YAAQkG,OAAO,EAAExB,cAAe;YAACe,SAAS,EAAC,mBAAmB;YAAAC,QAAA,EAAC;UAE/D;YAAA9B,QAAA,EAAA+B,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,EACRnF,kBAAkB,CAACmD,IAAI,KAAK,CAAC,gBAC5B7D,OAAA;YAAQkG,OAAO,EAAEpC,uBAAwB;YAAC2B,SAAS,EAAC,iBAAiB;YAAAC,QAAA,EAAC;UAEtE;YAAA9B,QAAA,EAAA+B,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,gBAET7F,OAAA;YAAQkG,OAAO,EAAEnC,mBAAoB;YAAC0B,SAAS,EAAC,gBAAgB;YAAAC,QAAA,EAAC;UAEjE;YAAA9B,QAAA,EAAA+B,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CACT;QAAA;UAAAjC,QAAA,EAAA+B,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAjC,QAAA,EAAA+B,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAjC,QAAA,EAAA+B,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,EAGA/E,cAAc,iBACbd,OAAA;MAAKyF,SAAS,EAAC,eAAe;MAAAC,QAAA,eAC5B1F,OAAA;QAAKyF,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAC5B1F,OAAA;UAAKyF,SAAS,EAAC,cAAc;UAAAC,QAAA,eAC3B1F,OAAA;YAAA0F,QAAA,EAAI;UAAe;YAAA9B,QAAA,EAAA+B,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI;QAAC;UAAAjC,QAAA,EAAA+B,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrB,CAAC,eACN7F,OAAA;UAAKyF,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzB1F,OAAA;YAAKyF,SAAS,EAAE,eAAe3E,cAAc,CAACwD,MAAM,CAACiD,UAAU,GAAG,SAAS,GAAG,SAAS,EAAG;YAAA7B,QAAA,EACvF5E,cAAc,CAACwD,MAAM,CAACiD,UAAU,GAAG,GAAG,GAAG;UAAI;YAAA3D,QAAA,EAAA+B,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3C,CAAC,eAEN7F,OAAA;YAAA0F,QAAA,GAAI,QAAM,EAAC5E,cAAc,CAACwD,MAAM,CAACV,QAAQ;UAAA;YAAAA,QAAA,EAAA+B,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAE/C7F,OAAA;YAAKyF,SAAS,EAAC,gBAAgB;YAAAC,QAAA,gBAC7B1F,OAAA;cAAKyF,SAAS,EAAC,UAAU;cAAAC,QAAA,gBACvB1F,OAAA;gBAAA0F,QAAA,EAAM;cAAmB;gBAAA9B,QAAA,EAAA+B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAChC7F,OAAA;gBAAA0F,QAAA,GAAS5E,cAAc,CAACwD,MAAM,CAACkD,wBAAwB,EAAC,eAAa;cAAA;gBAAA5D,QAAA,EAAA+B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAjC,QAAA,EAAA+B,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3E,CAAC,eACN7F,OAAA;cAAKyF,SAAS,EAAC,UAAU;cAAAC,QAAA,gBACvB1F,OAAA;gBAAA0F,QAAA,EAAM;cAAiB;gBAAA9B,QAAA,EAAA+B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC9B7F,OAAA;gBAAA0F,QAAA,GAAS5E,cAAc,CAACwD,MAAM,CAACmD,kBAAkB,EAAC,eAAa;cAAA;gBAAA7D,QAAA,EAAA+B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAjC,QAAA,EAAA+B,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrE,CAAC,eACN7F,OAAA;cAAKyF,SAAS,EAAC,UAAU;cAAAC,QAAA,gBACvB1F,OAAA;gBAAA0F,QAAA,EAAM;cAAa;gBAAA9B,QAAA,EAAA+B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC1B7F,OAAA;gBAAA0F,QAAA,GAAS5E,cAAc,CAACwD,MAAM,CAACoD,uBAAuB,EAAC,eAAa;cAAA;gBAAA9D,QAAA,EAAA+B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAjC,QAAA,EAAA+B,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1E,CAAC,eACN7F,OAAA;cAAKyF,SAAS,EAAC,UAAU;cAAAC,QAAA,gBACvB1F,OAAA;gBAAA0F,QAAA,EAAM;cAAY;gBAAA9B,QAAA,EAAA+B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACzB7F,OAAA;gBAAA0F,QAAA,GAAS5E,cAAc,CAACwD,MAAM,CAACqD,sBAAsB,EAAC,eAAa;cAAA;gBAAA/D,QAAA,EAAA+B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAjC,QAAA,EAAA+B,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzE,CAAC,eACN7F,OAAA;cAAKyF,SAAS,EAAC,UAAU;cAAAC,QAAA,gBACvB1F,OAAA;gBAAA0F,QAAA,EAAM;cAAe;gBAAA9B,QAAA,EAAA+B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC5B7F,OAAA;gBAAA0F,QAAA,EAAS5E,cAAc,CAACwD,MAAM,CAACsD,aAAa,GAAG,KAAK,GAAG;cAAI;gBAAAhE,QAAA,EAAA+B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAS,CAAC;YAAA;cAAAjC,QAAA,EAAA+B,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClE,CAAC;UAAA;YAAAjC,QAAA,EAAA+B,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,EAEL/E,cAAc,CAACwD,MAAM,CAACiD,UAAU,gBAC/BvH,OAAA;YAAKyF,SAAS,EAAC,iBAAiB;YAAAC,QAAA,gBAC9B1F,OAAA;cAAA0F,QAAA,GAAG,SAAE,eAAA1F,OAAA;gBAAA0F,QAAA,EAAQ;cAAgC;gBAAA9B,QAAA,EAAA+B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAjC,QAAA,EAAA+B,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eAC1D7F,OAAA;cAAA0F,QAAA,GAAG,8DAA4D,EAAC5E,cAAc,CAACwD,MAAM,CAACmD,kBAAkB,EAAC,yCAAuC;YAAA;cAAA7D,QAAA,EAAA+B,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACpJ7F,OAAA;cAAA0F,QAAA,gBAAG1F,OAAA;gBAAA0F,QAAA,EAAQ;cAAoD;gBAAA9B,QAAA,EAAA+B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,8BAA0B;YAAA;cAAAjC,QAAA,EAAA+B,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAjC,QAAA,EAAA+B,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnG,CAAC,gBAEN7F,OAAA;YAAKyF,SAAS,EAAC,iBAAiB;YAAAC,QAAA,gBAC9B1F,OAAA;cAAA0F,QAAA,GAAG,eAAG,eAAA1F,OAAA;gBAAA0F,QAAA,EAAQ;cAA6B;gBAAA9B,QAAA,EAAA+B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAjC,QAAA,EAAA+B,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACxD7F,OAAA;cAAA0F,QAAA,EAAG;YAAsH;cAAA9B,QAAA,EAAA+B,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,EAC5H/E,cAAc,CAACwD,MAAM,CAACsD,aAAa,iBAClC5H,OAAA;cAAA0F,QAAA,gBAAG1F,OAAA;gBAAA0F,QAAA,EAAQ;cAAqC;gBAAA9B,QAAA,EAAA+B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,wCAAoC;YAAA;cAAAjC,QAAA,EAAA+B,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CACjG;UAAA;YAAAjC,QAAA,EAAA+B,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CACN,EAEA/E,cAAc,CAACwD,MAAM,CAACpC,KAAK,iBAC1BlC,OAAA;YAAKyF,SAAS,EAAC,eAAe;YAAAC,QAAA,eAC5B1F,OAAA;cAAA0F,QAAA,GAAG,SAAE,eAAA1F,OAAA;gBAAA0F,QAAA,EAAQ;cAAM;gBAAA9B,QAAA,EAAA+B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAAC/E,cAAc,CAACwD,MAAM,CAACpC,KAAK;YAAA;cAAA0B,QAAA,EAAA+B,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI;UAAC;YAAAjC,QAAA,EAAA+B,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3D,CACN;QAAA;UAAAjC,QAAA,EAAA+B,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAEN7F,OAAA;UAAKyF,SAAS,EAAC,eAAe;UAAAC,QAAA,gBAC5B1F,OAAA;YAAQkG,OAAO,EAAElB,mBAAoB;YAACS,SAAS,EAAC,mBAAmB;YAAAC,QAAA,EAAC;UAEpE;YAAA9B,QAAA,EAAA+B,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,EAER/E,cAAc,CAACwD,MAAM,CAACsD,aAAa,IAAI9G,cAAc,CAAC0D,iBAAiB,iBACtExE,OAAA;YACEkG,OAAO,EAAEA,CAAA,KAAMvB,aAAa,CAAC7D,cAAc,CAACwD,MAAM,CAACM,SAAS,CAAE;YAC9DuB,QAAQ,EAAEnF,SAAS,KAAKF,cAAc,CAACwD,MAAM,CAACM,SAAU;YACxDa,SAAS,EAAC,iBAAiB;YAAAC,QAAA,EAE1B1E,SAAS,KAAKF,cAAc,CAACwD,MAAM,CAACM,SAAS,gBAC5C5E,OAAA,CAAAE,SAAA;cAAAwF,QAAA,gBACE1F,OAAA;gBAAKyF,SAAS,EAAC;cAAa;gBAAA7B,QAAA,EAAA+B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,gBAErC;YAAA,eAAE,CAAC,GAEH;UACD;YAAAjC,QAAA,EAAA+B,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK,CACT;QAAA;UAAAjC,QAAA,EAAA+B,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAjC,QAAA,EAAA+B,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAjC,QAAA,EAAA+B,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN;EAAA;IAAAjC,QAAA,EAAA+B,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAACxF,EAAA,CAheWF,WAAuC;AAAA0H,EAAA,GAAvC1H,WAAuC;AAAA,IAAA0H,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}