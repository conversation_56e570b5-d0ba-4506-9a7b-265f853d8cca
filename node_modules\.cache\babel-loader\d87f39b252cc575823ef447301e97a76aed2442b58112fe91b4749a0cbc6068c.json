{"ast": null, "code": "/**\n * @license\n * Copyright 2017 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { getCoordsDataType } from './shader_compiler';\nexport class SliceProgram {\n  constructor(destSize) {\n    this.variableNames = ['source'];\n    this.outputShape = destSize;\n    this.rank = destSize.length;\n    const dtype = getCoordsDataType(this.rank);\n    this.customUniforms = [{\n      name: 'start',\n      arrayIndex: this.rank,\n      type: 'int'\n    }];\n    const sourceCoords = getCoords(this.rank);\n    let body;\n    const coordSum = destSize.map((_, i) => {\n      return `sourceLoc.${coords[i]} = start[${i}] + coords.${coords[i]};`;\n    });\n    body = `\n        ${dtype} sourceLoc;\n        ${dtype} coords = getOutputCoords();\n        ${coordSum.join('\\n')}\n      `;\n    this.userCode = `\n      void main() {\n        ${body}\n        setOutput(getSource(${sourceCoords}));\n      }\n    `;\n  }\n}\nconst coords = ['x', 'y', 'z', 'w', 'u', 'v'];\nfunction getCoords(rank) {\n  if (rank === 1) {\n    return 'sourceLoc';\n  } else if (rank <= 6) {\n    return coords.slice(0, rank).map(x => 'sourceLoc.' + x).join(',');\n  } else {\n    throw Error(`Slicing for rank ${rank} is not yet supported`);\n  }\n}", "map": {"version": 3, "names": ["getCoordsDataType", "SliceProgram", "constructor", "destSize", "variableNames", "outputShape", "rank", "length", "dtype", "customUniforms", "name", "arrayIndex", "type", "sourceCoords", "getCoords", "body", "coordSum", "map", "_", "i", "coords", "join", "userCode", "slice", "x", "Error"], "sources": ["C:\\tfjs-backend-webgl\\src\\slice_gpu.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2017 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {GPGPUProgram} from './gpgpu_math';\nimport {getCoordsDataType, UniformType} from './shader_compiler';\n\nexport class SliceProgram implements GPGPUProgram {\n  variableNames = ['source'];\n  outputShape: number[];\n  userCode: string;\n  rank: number;\n  customUniforms: Array<{name: string; arrayIndex: number; type: UniformType;}>;\n\n  constructor(destSize: number[]) {\n    this.outputShape = destSize;\n    this.rank = destSize.length;\n\n    const dtype = getCoordsDataType(this.rank);\n    this.customUniforms = [{name: 'start', arrayIndex: this.rank, type: 'int'}];\n    const sourceCoords = getCoords(this.rank);\n\n    let body: string;\n    const coordSum = destSize.map((_, i) => {\n      return `sourceLoc.${coords[i]} = start[${i}] + coords.${coords[i]};`;\n    });\n    body = `\n        ${dtype} sourceLoc;\n        ${dtype} coords = getOutputCoords();\n        ${coordSum.join('\\n')}\n      `;\n    this.userCode = `\n      void main() {\n        ${body}\n        setOutput(getSource(${sourceCoords}));\n      }\n    `;\n  }\n}\n\nconst coords = ['x', 'y', 'z', 'w', 'u', 'v'];\n\nfunction getCoords(rank: number): string {\n  if (rank === 1) {\n    return 'sourceLoc';\n  } else if (rank <= 6) {\n    return coords.slice(0, rank).map(x => 'sourceLoc.' + x).join(',');\n  } else {\n    throw Error(`Slicing for rank ${rank} is not yet supported`);\n  }\n}\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAkBA,SAAQA,iBAAiB,QAAoB,mBAAmB;AAEhE,OAAM,MAAOC,YAAY;EAOvBC,YAAYC,QAAkB;IAN9B,KAAAC,aAAa,GAAG,CAAC,QAAQ,CAAC;IAOxB,IAAI,CAACC,WAAW,GAAGF,QAAQ;IAC3B,IAAI,CAACG,IAAI,GAAGH,QAAQ,CAACI,MAAM;IAE3B,MAAMC,KAAK,GAAGR,iBAAiB,CAAC,IAAI,CAACM,IAAI,CAAC;IAC1C,IAAI,CAACG,cAAc,GAAG,CAAC;MAACC,IAAI,EAAE,OAAO;MAAEC,UAAU,EAAE,IAAI,CAACL,IAAI;MAAEM,IAAI,EAAE;IAAK,CAAC,CAAC;IAC3E,MAAMC,YAAY,GAAGC,SAAS,CAAC,IAAI,CAACR,IAAI,CAAC;IAEzC,IAAIS,IAAY;IAChB,MAAMC,QAAQ,GAAGb,QAAQ,CAACc,GAAG,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAI;MACrC,OAAO,aAAaC,MAAM,CAACD,CAAC,CAAC,YAAYA,CAAC,cAAcC,MAAM,CAACD,CAAC,CAAC,GAAG;IACtE,CAAC,CAAC;IACFJ,IAAI,GAAG;UACDP,KAAK;UACLA,KAAK;UACLQ,QAAQ,CAACK,IAAI,CAAC,IAAI,CAAC;OACtB;IACH,IAAI,CAACC,QAAQ,GAAG;;UAEVP,IAAI;8BACgBF,YAAY;;KAErC;EACH;;AAGF,MAAMO,MAAM,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;AAE7C,SAASN,SAASA,CAACR,IAAY;EAC7B,IAAIA,IAAI,KAAK,CAAC,EAAE;IACd,OAAO,WAAW;GACnB,MAAM,IAAIA,IAAI,IAAI,CAAC,EAAE;IACpB,OAAOc,MAAM,CAACG,KAAK,CAAC,CAAC,EAAEjB,IAAI,CAAC,CAACW,GAAG,CAACO,CAAC,IAAI,YAAY,GAAGA,CAAC,CAAC,CAACH,IAAI,CAAC,GAAG,CAAC;GAClE,MAAM;IACL,MAAMI,KAAK,CAAC,oBAAoBnB,IAAI,uBAAuB,CAAC;;AAEhE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}