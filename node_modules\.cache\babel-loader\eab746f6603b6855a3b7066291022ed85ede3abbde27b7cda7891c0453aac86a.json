{"ast": null, "code": "/**\n * @license\n * Copyright 2018 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nexport function getVecChannels(name, rank) {\n  return ['x', 'y', 'z', 'w', 'u', 'v'].slice(0, rank).map(d => \"\".concat(name, \".\").concat(d));\n}\nexport function getChannels(name, rank) {\n  if (rank === 1) {\n    return [name];\n  }\n  return getVecChannels(name, rank);\n}\nexport function getSourceCoords(rank, dims) {\n  if (rank === 1) {\n    return 'rc';\n  }\n  let coords = '';\n  for (let i = 0; i < rank; i++) {\n    coords += dims[i];\n    if (i < rank - 1) {\n      coords += ',';\n    }\n  }\n  return coords;\n}", "map": {"version": 3, "names": ["getVecChannels", "name", "rank", "slice", "map", "d", "concat", "getChannels", "getSourceCoords", "dims", "coords", "i"], "sources": ["C:\\tfjs-backend-webgl\\src\\packing_util.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2018 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nexport function getVecChannels(name: string, rank: number): string[] {\n  return ['x', 'y', 'z', 'w', 'u', 'v'].slice(0, rank).map(d => `${name}.${d}`);\n}\n\nexport function getChannels(name: string, rank: number): string[] {\n  if (rank === 1) {\n    return [name];\n  }\n  return getVecChannels(name, rank);\n}\n\nexport function getSourceCoords(rank: number, dims: string[]): string {\n  if (rank === 1) {\n    return 'rc';\n  }\n\n  let coords = '';\n  for (let i = 0; i < rank; i++) {\n    coords += dims[i];\n    if (i < rank - 1) {\n      coords += ',';\n    }\n  }\n  return coords;\n}"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAiBA,OAAM,SAAUA,cAAcA,CAACC,IAAY,EAAEC,IAAY;EACvD,OAAO,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAACC,KAAK,CAAC,CAAC,EAAED,IAAI,CAAC,CAACE,GAAG,CAACC,CAAC,OAAAC,MAAA,CAAOL,IAAI,OAAAK,MAAA,CAAID,CAAC,CAAE,CAAC;AAC/E;AAEA,OAAM,SAAUE,WAAWA,CAACN,IAAY,EAAEC,IAAY;EACpD,IAAIA,IAAI,KAAK,CAAC,EAAE;IACd,OAAO,CAACD,IAAI,CAAC;;EAEf,OAAOD,cAAc,CAACC,IAAI,EAAEC,IAAI,CAAC;AACnC;AAEA,OAAM,SAAUM,eAAeA,CAACN,IAAY,EAAEO,IAAc;EAC1D,IAAIP,IAAI,KAAK,CAAC,EAAE;IACd,OAAO,IAAI;;EAGb,IAAIQ,MAAM,GAAG,EAAE;EACf,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGT,IAAI,EAAES,CAAC,EAAE,EAAE;IAC7BD,MAAM,IAAID,IAAI,CAACE,CAAC,CAAC;IACjB,IAAIA,CAAC,GAAGT,IAAI,GAAG,CAAC,EAAE;MAChBQ,MAAM,IAAI,GAAG;;;EAGjB,OAAOA,MAAM;AACf", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}