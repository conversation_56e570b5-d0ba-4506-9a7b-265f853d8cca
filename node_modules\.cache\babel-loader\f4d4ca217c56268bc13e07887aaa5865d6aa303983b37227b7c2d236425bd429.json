{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.transformReply = exports.transformArguments = exports.IS_READ_ONLY = exports.FIRST_KEY_INDEX = void 0;\nconst FIRST_KEY_INDEX = streams => {\n  return Array.isArray(streams) ? streams[0].key : streams.key;\n};\nexports.FIRST_KEY_INDEX = FIRST_KEY_INDEX;\nexports.IS_READ_ONLY = true;\nfunction transformArguments(streams, options) {\n  const args = ['XREAD'];\n  if (options?.COUNT) {\n    args.push('COUNT', options.COUNT.toString());\n  }\n  if (typeof options?.BLOCK === 'number') {\n    args.push('BLOCK', options.BLOCK.toString());\n  }\n  args.push('STREAMS');\n  const streamsArray = Array.isArray(streams) ? streams : [streams],\n    argsLength = args.length;\n  for (let i = 0; i < streamsArray.length; i++) {\n    const stream = streamsArray[i];\n    args[argsLength + i] = stream.key;\n    args[argsLength + streamsArray.length + i] = stream.id;\n  }\n  return args;\n}\nexports.transformArguments = transformArguments;\nvar generic_transformers_1 = require(\"./generic-transformers\");\nObject.defineProperty(exports, \"transformReply\", {\n  enumerable: true,\n  get: function () {\n    return generic_transformers_1.transformStreamsMessagesReply;\n  }\n});", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "transformReply", "transformArguments", "IS_READ_ONLY", "FIRST_KEY_INDEX", "streams", "Array", "isArray", "key", "options", "args", "COUNT", "push", "toString", "BLOCK", "streamsArray", "arg<PERSON><PERSON><PERSON><PERSON>", "length", "i", "stream", "id", "generic_transformers_1", "require", "enumerable", "get", "transformStreamsMessagesReply"], "sources": ["C:/tmsft/node_modules/@redis/client/dist/lib/commands/XREAD.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.transformReply = exports.transformArguments = exports.IS_READ_ONLY = exports.FIRST_KEY_INDEX = void 0;\nconst FIRST_KEY_INDEX = (streams) => {\n    return Array.isArray(streams) ? streams[0].key : streams.key;\n};\nexports.FIRST_KEY_INDEX = FIRST_KEY_INDEX;\nexports.IS_READ_ONLY = true;\nfunction transformArguments(streams, options) {\n    const args = ['XREAD'];\n    if (options?.COUNT) {\n        args.push('COUNT', options.COUNT.toString());\n    }\n    if (typeof options?.BLOCK === 'number') {\n        args.push('BLOCK', options.BLOCK.toString());\n    }\n    args.push('STREAMS');\n    const streamsArray = Array.isArray(streams) ? streams : [streams], argsLength = args.length;\n    for (let i = 0; i < streamsArray.length; i++) {\n        const stream = streamsArray[i];\n        args[argsLength + i] = stream.key;\n        args[argsLength + streamsArray.length + i] = stream.id;\n    }\n    return args;\n}\nexports.transformArguments = transformArguments;\nvar generic_transformers_1 = require(\"./generic-transformers\");\nObject.defineProperty(exports, \"transformReply\", { enumerable: true, get: function () { return generic_transformers_1.transformStreamsMessagesReply; } });\n"], "mappings": "AAAA,YAAY;;AACZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEC,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7DD,OAAO,CAACE,cAAc,GAAGF,OAAO,CAACG,kBAAkB,GAAGH,OAAO,CAACI,YAAY,GAAGJ,OAAO,CAACK,eAAe,GAAG,KAAK,CAAC;AAC7G,MAAMA,eAAe,GAAIC,OAAO,IAAK;EACjC,OAAOC,KAAK,CAACC,OAAO,CAACF,OAAO,CAAC,GAAGA,OAAO,CAAC,CAAC,CAAC,CAACG,GAAG,GAAGH,OAAO,CAACG,GAAG;AAChE,CAAC;AACDT,OAAO,CAACK,eAAe,GAAGA,eAAe;AACzCL,OAAO,CAACI,YAAY,GAAG,IAAI;AAC3B,SAASD,kBAAkBA,CAACG,OAAO,EAAEI,OAAO,EAAE;EAC1C,MAAMC,IAAI,GAAG,CAAC,OAAO,CAAC;EACtB,IAAID,OAAO,EAAEE,KAAK,EAAE;IAChBD,IAAI,CAACE,IAAI,CAAC,OAAO,EAAEH,OAAO,CAACE,KAAK,CAACE,QAAQ,CAAC,CAAC,CAAC;EAChD;EACA,IAAI,OAAOJ,OAAO,EAAEK,KAAK,KAAK,QAAQ,EAAE;IACpCJ,IAAI,CAACE,IAAI,CAAC,OAAO,EAAEH,OAAO,CAACK,KAAK,CAACD,QAAQ,CAAC,CAAC,CAAC;EAChD;EACAH,IAAI,CAACE,IAAI,CAAC,SAAS,CAAC;EACpB,MAAMG,YAAY,GAAGT,KAAK,CAACC,OAAO,CAACF,OAAO,CAAC,GAAGA,OAAO,GAAG,CAACA,OAAO,CAAC;IAAEW,UAAU,GAAGN,IAAI,CAACO,MAAM;EAC3F,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,YAAY,CAACE,MAAM,EAAEC,CAAC,EAAE,EAAE;IAC1C,MAAMC,MAAM,GAAGJ,YAAY,CAACG,CAAC,CAAC;IAC9BR,IAAI,CAACM,UAAU,GAAGE,CAAC,CAAC,GAAGC,MAAM,CAACX,GAAG;IACjCE,IAAI,CAACM,UAAU,GAAGD,YAAY,CAACE,MAAM,GAAGC,CAAC,CAAC,GAAGC,MAAM,CAACC,EAAE;EAC1D;EACA,OAAOV,IAAI;AACf;AACAX,OAAO,CAACG,kBAAkB,GAAGA,kBAAkB;AAC/C,IAAImB,sBAAsB,GAAGC,OAAO,CAAC,wBAAwB,CAAC;AAC9DzB,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,gBAAgB,EAAE;EAAEwB,UAAU,EAAE,IAAI;EAAEC,GAAG,EAAE,SAAAA,CAAA,EAAY;IAAE,OAAOH,sBAAsB,CAACI,6BAA6B;EAAE;AAAE,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}