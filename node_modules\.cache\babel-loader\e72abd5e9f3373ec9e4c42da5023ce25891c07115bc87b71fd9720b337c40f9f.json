{"ast": null, "code": "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { ENGINE } from '../../engine';\nimport { RotateWithOffset } from '../../kernel_names';\nimport { convertToTensor } from '../../tensor_util_env';\nimport * as util from '../../util';\nimport { op } from '../operation';\n/**\n * Rotates the input image tensor counter-clockwise with an optional offset\n * center of rotation. Currently available in the CPU, WebGL, and WASM backends.\n *\n * @param image 4d tensor of shape `[batch, imageHeight, imageWidth, depth]`.\n * @param radians The amount of rotation.\n * @param fillValue The value to fill in the empty space leftover\n *     after rotation. Can be either a single grayscale value (0-255), or an\n *     array of three numbers `[red, green, blue]` specifying the red, green,\n *     and blue channels. Defaults to `0` (black).\n * @param center The center of rotation. Can be either a single value (0-1), or\n *     an array of two numbers `[centerX, centerY]`. Defaults to `0.5` (rotates\n *     the image around its center).\n *\n * @doc {heading: 'Operations', subheading: 'Images', namespace: 'image'}\n */\nfunction rotateWithOffset_(image, radians) {\n  let fillValue = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 0;\n  let center = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : 0.5;\n  const $image = convertToTensor(image, 'image', 'rotateWithOffset', 'float32');\n  util.assert($image.rank === 4, () => 'Error in rotateWithOffset: image must be rank 4,' + \"but got rank \".concat($image.rank, \".\"));\n  const inputs = {\n    image: $image\n  };\n  const attrs = {\n    radians,\n    fillValue,\n    center\n  };\n  const res = ENGINE.runKernel(RotateWithOffset, inputs, attrs);\n  return res;\n}\nexport const rotateWithOffset = /* @__PURE__ */op({\n  rotateWithOffset_\n});", "map": {"version": 3, "names": ["ENGINE", "RotateWithOffset", "convertToTensor", "util", "op", "rotateWithOffset_", "image", "radians", "fillValue", "arguments", "length", "undefined", "center", "$image", "assert", "rank", "concat", "inputs", "attrs", "res", "runKernel", "rotateWithOffset"], "sources": ["C:\\tfjs-core\\src\\ops\\image\\rotate_with_offset.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {ENGINE} from '../../engine';\nimport {RotateWithOffset, RotateWithOffsetAttrs, RotateWithOffsetInputs} from '../../kernel_names';\nimport {NamedAttrMap} from '../../kernel_registry';\nimport {Tensor4D} from '../../tensor';\nimport {NamedTensorMap} from '../../tensor_types';\nimport {convertToTensor} from '../../tensor_util_env';\nimport {TensorLike} from '../../types';\nimport * as util from '../../util';\n\nimport {op} from '../operation';\n\n/**\n * Rotates the input image tensor counter-clockwise with an optional offset\n * center of rotation. Currently available in the CPU, WebGL, and WASM backends.\n *\n * @param image 4d tensor of shape `[batch, imageHeight, imageWidth, depth]`.\n * @param radians The amount of rotation.\n * @param fillValue The value to fill in the empty space leftover\n *     after rotation. Can be either a single grayscale value (0-255), or an\n *     array of three numbers `[red, green, blue]` specifying the red, green,\n *     and blue channels. Defaults to `0` (black).\n * @param center The center of rotation. Can be either a single value (0-1), or\n *     an array of two numbers `[centerX, centerY]`. Defaults to `0.5` (rotates\n *     the image around its center).\n *\n * @doc {heading: 'Operations', subheading: 'Images', namespace: 'image'}\n */\nfunction rotateWithOffset_(\n    image: Tensor4D|TensorLike, radians: number,\n    fillValue: number|[number, number, number] = 0,\n    center: number|[number, number] = 0.5): Tensor4D {\n  const $image = convertToTensor(image, 'image', 'rotateWithOffset', 'float32');\n\n  util.assert(\n      $image.rank === 4,\n      () => 'Error in rotateWithOffset: image must be rank 4,' +\n          `but got rank ${$image.rank}.`);\n\n  const inputs: RotateWithOffsetInputs = {image: $image};\n  const attrs: RotateWithOffsetAttrs = {radians, fillValue, center};\n  const res = ENGINE.runKernel(\n      RotateWithOffset, inputs as unknown as NamedTensorMap,\n      attrs as unknown as NamedAttrMap);\n  return res as Tensor4D;\n}\n\nexport const rotateWithOffset = /* @__PURE__ */ op({rotateWithOffset_});\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAiBA,SAAQA,MAAM,QAAO,cAAc;AACnC,SAAQC,gBAAgB,QAAsD,oBAAoB;AAIlG,SAAQC,eAAe,QAAO,uBAAuB;AAErD,OAAO,KAAKC,IAAI,MAAM,YAAY;AAElC,SAAQC,EAAE,QAAO,cAAc;AAE/B;;;;;;;;;;;;;;;;AAgBA,SAASC,iBAAiBA,CACtBC,KAA0B,EAAEC,OAAe,EAEN;EAAA,IADrCC,SAAA,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAA6C,CAAC;EAAA,IAC9CG,MAAA,GAAAH,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAkC,GAAG;EACvC,MAAMI,MAAM,GAAGX,eAAe,CAACI,KAAK,EAAE,OAAO,EAAE,kBAAkB,EAAE,SAAS,CAAC;EAE7EH,IAAI,CAACW,MAAM,CACPD,MAAM,CAACE,IAAI,KAAK,CAAC,EACjB,MAAM,kDAAkD,mBAAAC,MAAA,CACpCH,MAAM,CAACE,IAAI,MAAG,CAAC;EAEvC,MAAME,MAAM,GAA2B;IAACX,KAAK,EAAEO;EAAM,CAAC;EACtD,MAAMK,KAAK,GAA0B;IAACX,OAAO;IAAEC,SAAS;IAAEI;EAAM,CAAC;EACjE,MAAMO,GAAG,GAAGnB,MAAM,CAACoB,SAAS,CACxBnB,gBAAgB,EAAEgB,MAAmC,EACrDC,KAAgC,CAAC;EACrC,OAAOC,GAAe;AACxB;AAEA,OAAO,MAAME,gBAAgB,GAAG,eAAgBjB,EAAE,CAAC;EAACC;AAAiB,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}