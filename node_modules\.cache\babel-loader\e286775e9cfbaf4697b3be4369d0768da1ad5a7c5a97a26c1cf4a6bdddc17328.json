{"ast": null, "code": "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { MaxPoolWithArgmax } from '@tensorflow/tfjs-core';\nimport { backend_util, util } from '@tensorflow/tfjs-core';\nimport { maxPoolWithArgmaxImpl } from './MaxPoolWithArgmax_impl';\nexport const maxPoolWithArgmaxConfig = {\n  kernelName: MaxPoolWithArgmax,\n  backendName: 'webgl',\n  kernelFunc: ({\n    inputs,\n    attrs,\n    backend\n  }) => {\n    const {\n      x\n    } = inputs;\n    const {\n      filterSize,\n      strides,\n      pad,\n      includeBatchInIndex\n    } = attrs;\n    const webglBackend = backend;\n    util.assert(x.shape.length === 4, () => `Error in maxPool: input must be rank 4 but got rank ${x.shape.length}.`);\n    const dilations = [1, 1];\n    util.assert(backend_util.eitherStridesOrDilationsAreOne(strides, dilations), () => 'Error in maxPool: Either strides or dilations must be 1. ' + `Got strides ${strides} and dilations '${dilations}'`);\n    const convInfo = backend_util.computePool2DInfo(x.shape, filterSize, strides, dilations, pad);\n    const [result, indexes] = maxPoolWithArgmaxImpl(x, includeBatchInIndex, convInfo, webglBackend);\n    return [result, indexes];\n  }\n};", "map": {"version": 3, "names": ["MaxPoolWithArgmax", "backend_util", "util", "maxPoolWithArgmaxImpl", "maxPoolWithArgmaxConfig", "kernelName", "backendName", "kernelFunc", "inputs", "attrs", "backend", "x", "filterSize", "strides", "pad", "includeBatchInIndex", "webglBackend", "assert", "shape", "length", "dilations", "eitherStridesOrDilationsAreOne", "convInfo", "computePool2DInfo", "result", "indexes"], "sources": ["C:\\tfjs-backend-webgl\\src\\kernels\\MaxPoolWithArgmax.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {MaxPoolWithArgmax, MaxPoolWithArgmaxAttrs, MaxPoolWithArgmaxInputs} from '@tensorflow/tfjs-core';\nimport {backend_util, KernelConfig, util} from '@tensorflow/tfjs-core';\n\nimport {MathBackendWebGL} from '../backend_webgl';\n\nimport {maxPoolWithArgmaxImpl} from './MaxPoolWithArgmax_impl';\n\nexport const maxPoolWithArgmaxConfig: KernelConfig = {\n  kernelName: MaxPoolWithArgmax,\n  backendName: 'webgl',\n  kernelFunc: ({inputs, attrs, backend}) => {\n    const {x} = inputs as MaxPoolWithArgmaxInputs;\n    const {filterSize, strides, pad, includeBatchInIndex} =\n        attrs as unknown as MaxPoolWithArgmaxAttrs;\n    const webglBackend = backend as MathBackendWebGL;\n\n    util.assert(\n        x.shape.length === 4,\n        () => `Error in maxPool: input must be rank 4 but got rank ${\n            x.shape.length}.`);\n    const dilations: [number, number] = [1, 1];\n    util.assert(\n        backend_util.eitherStridesOrDilationsAreOne(strides, dilations),\n        () => 'Error in maxPool: Either strides or dilations must be 1. ' +\n            `Got strides ${strides} and dilations '${dilations}'`);\n\n    const convInfo = backend_util.computePool2DInfo(\n        x.shape as [number, number, number, number], filterSize, strides,\n        dilations, pad);\n\n    const [result, indexes] =\n        maxPoolWithArgmaxImpl(x, includeBatchInIndex, convInfo, webglBackend);\n    return [result, indexes];\n  }\n};\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAiBA,SAAQA,iBAAiB,QAAwD,uBAAuB;AACxG,SAAQC,YAAY,EAAgBC,IAAI,QAAO,uBAAuB;AAItE,SAAQC,qBAAqB,QAAO,0BAA0B;AAE9D,OAAO,MAAMC,uBAAuB,GAAiB;EACnDC,UAAU,EAAEL,iBAAiB;EAC7BM,WAAW,EAAE,OAAO;EACpBC,UAAU,EAAEA,CAAC;IAACC,MAAM;IAAEC,KAAK;IAAEC;EAAO,CAAC,KAAI;IACvC,MAAM;MAACC;IAAC,CAAC,GAAGH,MAAiC;IAC7C,MAAM;MAACI,UAAU;MAAEC,OAAO;MAAEC,GAAG;MAAEC;IAAmB,CAAC,GACjDN,KAA0C;IAC9C,MAAMO,YAAY,GAAGN,OAA2B;IAEhDR,IAAI,CAACe,MAAM,CACPN,CAAC,CAACO,KAAK,CAACC,MAAM,KAAK,CAAC,EACpB,MAAM,uDACFR,CAAC,CAACO,KAAK,CAACC,MAAM,GAAG,CAAC;IAC1B,MAAMC,SAAS,GAAqB,CAAC,CAAC,EAAE,CAAC,CAAC;IAC1ClB,IAAI,CAACe,MAAM,CACPhB,YAAY,CAACoB,8BAA8B,CAACR,OAAO,EAAEO,SAAS,CAAC,EAC/D,MAAM,2DAA2D,GAC7D,eAAeP,OAAO,mBAAmBO,SAAS,GAAG,CAAC;IAE9D,MAAME,QAAQ,GAAGrB,YAAY,CAACsB,iBAAiB,CAC3CZ,CAAC,CAACO,KAAyC,EAAEN,UAAU,EAAEC,OAAO,EAChEO,SAAS,EAAEN,GAAG,CAAC;IAEnB,MAAM,CAACU,MAAM,EAAEC,OAAO,CAAC,GACnBtB,qBAAqB,CAACQ,CAAC,EAAEI,mBAAmB,EAAEO,QAAQ,EAAEN,YAAY,CAAC;IACzE,OAAO,CAACQ,MAAM,EAAEC,OAAO,CAAC;EAC1B;CACD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}