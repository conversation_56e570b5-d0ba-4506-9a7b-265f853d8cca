{"ast": null, "code": "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { Conv2DBackpropInput } from '../kernel_names';\nimport { conv2d } from '../ops/conv2d';\nimport { conv2DBackpropFilter } from '../ops/conv2d_backprop_filter';\nexport const conv2DBackpropInputGradConfig = {\n  kernelName: Conv2DBackpropInput,\n  inputsToSave: ['dy', 'filter'],\n  gradFunc: (ddx, saved, attrs) => {\n    const [dy, filter] = saved;\n    const {\n      strides,\n      pad,\n      dataFormat,\n      dimRoundingMode\n    } = attrs;\n    return {\n      dy: () => conv2d(ddx, filter, strides, pad, dataFormat, 1 /* dilations */, dimRoundingMode),\n      filter: () => conv2DBackpropFilter(ddx, dy, filter.shape, strides, pad, dataFormat, dimRoundingMode)\n    };\n  }\n};", "map": {"version": 3, "names": ["Conv2DBackpropInput", "conv2d", "conv2DBackpropFilter", "conv2DBackpropInputGradConfig", "kernelName", "inputsToSave", "grad<PERSON>unc", "ddx", "saved", "attrs", "dy", "filter", "strides", "pad", "dataFormat", "dimRoundingMode", "shape"], "sources": ["C:\\tfjs-core\\src\\gradients\\Conv2DBackpropInput_grad.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport {Conv2DBackpropInput, Conv2DBackpropInputAttrs} from '../kernel_names';\nimport {GradConfig, NamedAttrMap} from '../kernel_registry';\nimport {conv2d} from '../ops/conv2d';\nimport {conv2DBackpropFilter} from '../ops/conv2d_backprop_filter';\nimport {Tensor, Tensor4D} from '../tensor';\n\nexport const conv2DBackpropInputGradConfig: GradConfig = {\n  kernelName: Conv2DBackpropInput,\n  inputsToSave: ['dy', 'filter'],\n  gradFunc: (ddx: Tensor4D, saved: Tensor[], attrs: NamedAttrMap) => {\n    const [dy, filter] = saved as [Tensor4D, Tensor4D];\n\n    const {strides, pad, dataFormat, dimRoundingMode} =\n        attrs as unknown as Conv2DBackpropInputAttrs;\n\n    return {\n      dy: () => conv2d(\n          ddx, filter, strides, pad, dataFormat, 1 /* dilations */,\n          dimRoundingMode),\n      filter: () => conv2DBackpropFilter(\n          ddx, dy, filter.shape, strides, pad, dataFormat, dimRoundingMode)\n    };\n  }\n};\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAgBA,SAAQA,mBAAmB,QAAiC,iBAAiB;AAE7E,SAAQC,MAAM,QAAO,eAAe;AACpC,SAAQC,oBAAoB,QAAO,+BAA+B;AAGlE,OAAO,MAAMC,6BAA6B,GAAe;EACvDC,UAAU,EAAEJ,mBAAmB;EAC/BK,YAAY,EAAE,CAAC,IAAI,EAAE,QAAQ,CAAC;EAC9BC,QAAQ,EAAEA,CAACC,GAAa,EAAEC,KAAe,EAAEC,KAAmB,KAAI;IAChE,MAAM,CAACC,EAAE,EAAEC,MAAM,CAAC,GAAGH,KAA6B;IAElD,MAAM;MAACI,OAAO;MAAEC,GAAG;MAAEC,UAAU;MAAEC;IAAe,CAAC,GAC7CN,KAA4C;IAEhD,OAAO;MACLC,EAAE,EAAEA,CAAA,KAAMT,MAAM,CACZM,GAAG,EAAEI,MAAM,EAAEC,OAAO,EAAEC,GAAG,EAAEC,UAAU,EAAE,CAAC,CAAC,iBACzCC,eAAe,CAAC;MACpBJ,MAAM,EAAEA,CAAA,KAAMT,oBAAoB,CAC9BK,GAAG,EAAEG,EAAE,EAAEC,MAAM,CAACK,KAAK,EAAEJ,OAAO,EAAEC,GAAG,EAAEC,UAAU,EAAEC,eAAe;KACrE;EACH;CACD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}