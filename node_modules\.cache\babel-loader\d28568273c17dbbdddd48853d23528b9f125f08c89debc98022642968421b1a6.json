{"ast": null, "code": "/*\nCopyright (c) 2023, Pluto Rotegott\nTokenizer for Ukrainian\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in\nall copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\nTHE SOFTWARE.\n*/\n\n'use strict';\n\nconst Tokenizer = require('./tokenizer');\nclass AggressiveTokenizer extends Tokenizer {\n  withoutEmpty(array) {\n    return array.filter(function (a) {\n      return a;\n    });\n  }\n  clearText(text) {\n    return text.replace(/[^a-zа-яґєії0-9]/gi, ' ').replace(/[\\s\\n]+/g, ' ').trim();\n  }\n  tokenize(text) {\n    // break a string up into an array of tokens by anything non-word\n    return this.withoutEmpty(this.clearText(text).split(' '));\n  }\n}\nmodule.exports = AggressiveTokenizer;", "map": {"version": 3, "names": ["Tokenizer", "require", "AggressiveTokenizer", "without<PERSON><PERSON>y", "array", "filter", "a", "clearText", "text", "replace", "trim", "tokenize", "split", "module", "exports"], "sources": ["C:/tmsft/node_modules/natural/lib/natural/tokenizers/aggressive_tokenizer_uk.js"], "sourcesContent": ["/*\nCopyright (c) 2023, Pluto Rotegott\nTokenizer for Ukrainian\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in\nall copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\nTHE SOFTWARE.\n*/\n\n'use strict'\n\nconst Tokenizer = require('./tokenizer')\n\nclass AggressiveTokenizer extends Tokenizer {\n  withoutEmpty (array) {\n    return array.filter(function (a) { return a })\n  }\n\n  clearText (text) {\n    return text.replace(/[^a-zа-яґєії0-9]/gi, ' ').replace(/[\\s\\n]+/g, ' ').trim()\n  }\n\n  tokenize (text) {\n    // break a string up into an array of tokens by anything non-word\n    return this.withoutEmpty(this.clearText(text).split(' '))\n  }\n}\n\nmodule.exports = AggressiveTokenizer\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,YAAY;;AAEZ,MAAMA,SAAS,GAAGC,OAAO,CAAC,aAAa,CAAC;AAExC,MAAMC,mBAAmB,SAASF,SAAS,CAAC;EAC1CG,YAAYA,CAAEC,KAAK,EAAE;IACnB,OAAOA,KAAK,CAACC,MAAM,CAAC,UAAUC,CAAC,EAAE;MAAE,OAAOA,CAAC;IAAC,CAAC,CAAC;EAChD;EAEAC,SAASA,CAAEC,IAAI,EAAE;IACf,OAAOA,IAAI,CAACC,OAAO,CAAC,oBAAoB,EAAE,GAAG,CAAC,CAACA,OAAO,CAAC,UAAU,EAAE,GAAG,CAAC,CAACC,IAAI,CAAC,CAAC;EAChF;EAEAC,QAAQA,CAAEH,IAAI,EAAE;IACd;IACA,OAAO,IAAI,CAACL,YAAY,CAAC,IAAI,CAACI,SAAS,CAACC,IAAI,CAAC,CAACI,KAAK,CAAC,GAAG,CAAC,CAAC;EAC3D;AACF;AAEAC,MAAM,CAACC,OAAO,GAAGZ,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}