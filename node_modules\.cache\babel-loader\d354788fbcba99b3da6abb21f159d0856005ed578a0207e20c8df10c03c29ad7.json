{"ast": null, "code": "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { Concat } from '../kernel_names';\nimport { split } from '../ops/split';\nimport { parseAxisParam } from '../util';\nexport const concatGradConfig = {\n  kernelName: Concat,\n  saveAllInputs: true,\n  gradFunc: (dy, saved, attrs) => {\n    const shapes = saved.map(t => t.shape);\n    const {\n      axis\n    } = attrs;\n    const $axis = parseAxisParam(axis, saved[0].shape)[0];\n    const sizeSplits = shapes.map(s => s[$axis]);\n    const derTensors = split(dy, sizeSplits, $axis);\n    return derTensors.map(t => () => t);\n  }\n};", "map": {"version": 3, "names": ["Concat", "split", "parseAxisParam", "concatGradConfig", "kernelName", "saveAllInputs", "grad<PERSON>unc", "dy", "saved", "attrs", "shapes", "map", "t", "shape", "axis", "$axis", "sizeSplits", "s", "derTensors"], "sources": ["C:\\tfjs-core\\src\\gradients\\Concat_grad.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport {Concat, ConcatAttrs} from '../kernel_names';\nimport {GradConfig, NamedAttrMap} from '../kernel_registry';\nimport {split} from '../ops/split';\nimport {Tensor} from '../tensor';\nimport {parseAxisParam} from '../util';\n\nexport const concatGradConfig: GradConfig = {\n  kernelName: Concat,\n  saveAllInputs: true,\n  gradFunc: (dy: Tensor, saved: Tensor[], attrs: NamedAttrMap) => {\n    const shapes = saved.map(t => t.shape);\n    const {axis} = attrs as unknown as ConcatAttrs;\n    const $axis = parseAxisParam(axis, saved[0].shape)[0];\n    const sizeSplits = shapes.map(s => s[$axis]);\n    const derTensors = split(dy, sizeSplits, $axis);\n    return derTensors.map(t => () => t) as {};\n  }\n};\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAgBA,SAAQA,MAAM,QAAoB,iBAAiB;AAEnD,SAAQC,KAAK,QAAO,cAAc;AAElC,SAAQC,cAAc,QAAO,SAAS;AAEtC,OAAO,MAAMC,gBAAgB,GAAe;EAC1CC,UAAU,EAAEJ,MAAM;EAClBK,aAAa,EAAE,IAAI;EACnBC,QAAQ,EAAEA,CAACC,EAAU,EAAEC,KAAe,EAAEC,KAAmB,KAAI;IAC7D,MAAMC,MAAM,GAAGF,KAAK,CAACG,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACC,KAAK,CAAC;IACtC,MAAM;MAACC;IAAI,CAAC,GAAGL,KAA+B;IAC9C,MAAMM,KAAK,GAAGb,cAAc,CAACY,IAAI,EAAEN,KAAK,CAAC,CAAC,CAAC,CAACK,KAAK,CAAC,CAAC,CAAC,CAAC;IACrD,MAAMG,UAAU,GAAGN,MAAM,CAACC,GAAG,CAACM,CAAC,IAAIA,CAAC,CAACF,KAAK,CAAC,CAAC;IAC5C,MAAMG,UAAU,GAAGjB,KAAK,CAACM,EAAE,EAAES,UAAU,EAAED,KAAK,CAAC;IAC/C,OAAOG,UAAU,CAACP,GAAG,CAACC,CAAC,IAAI,MAAMA,CAAC,CAAO;EAC3C;CACD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}