{"ast": null, "code": "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { ENGINE } from '../engine';\nimport { Dilation2D, Dilation2DBackpropFilter, Dilation2DBackpropInput } from '../kernel_names';\nexport const dilation2dGradConfig = {\n  kernelName: Dilation2D,\n  inputsToSave: ['x', 'filter'],\n  gradFunc: (dy, saved, attrs) => {\n    const [x, filter] = saved;\n    const inputInputs = {\n      x,\n      filter,\n      dy\n    };\n    const filterInputs = {\n      x,\n      filter,\n      dy\n    };\n    return {\n      x: () => ENGINE.runKernel(Dilation2DBackpropInput, inputInputs, attrs),\n      filter: () => ENGINE.runKernel(Dilation2DBackpropFilter, filterInputs, attrs)\n    };\n  }\n};", "map": {"version": 3, "names": ["ENGINE", "Dilation2D", "Dilation2DBackpropFilter", "Dilation2DBackpropInput", "dilation2dGradConfig", "kernelName", "inputsToSave", "grad<PERSON>unc", "dy", "saved", "attrs", "x", "filter", "inputInputs", "filterInputs", "runKernel"], "sources": ["C:\\tfjs-core\\src\\gradients\\Dilation2D_grad.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport {ENGINE} from '../engine';\nimport {Dilation2D, Dilation2DBackpropFilter, Dilation2DBackpropFilterInputs, Dilation2DBackpropInput, Dilation2DBackpropInputInputs} from '../kernel_names';\nimport {GradConfig} from '../kernel_registry';\nimport {NamedAttrMap} from '../kernel_registry';\nimport {Tensor, Tensor3D, Tensor4D} from '../tensor';\nimport {NamedTensorMap} from '../tensor_types';\n\nexport const dilation2dGradConfig: GradConfig = {\n  kernelName: Dilation2D,\n  inputsToSave: ['x', 'filter'],\n  gradFunc: (dy: Tensor4D, saved: Tensor[], attrs: NamedAttrMap) => {\n    const [x, filter] = saved as [Tensor4D, Tensor3D];\n\n    const inputInputs: Dilation2DBackpropInputInputs = {x, filter, dy};\n    const filterInputs: Dilation2DBackpropFilterInputs = {x, filter, dy};\n\n    return {\n      x: () => ENGINE.runKernel(\n          Dilation2DBackpropInput,\n          inputInputs as unknown as NamedTensorMap, attrs),\n      filter: () => ENGINE.runKernel(\n          Dilation2DBackpropFilter,\n          filterInputs as unknown as NamedTensorMap, attrs)\n    };\n  }\n};\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAgBA,SAAQA,MAAM,QAAO,WAAW;AAChC,SAAQC,UAAU,EAAEC,wBAAwB,EAAkCC,uBAAuB,QAAsC,iBAAiB;AAM5J,OAAO,MAAMC,oBAAoB,GAAe;EAC9CC,UAAU,EAAEJ,UAAU;EACtBK,YAAY,EAAE,CAAC,GAAG,EAAE,QAAQ,CAAC;EAC7BC,QAAQ,EAAEA,CAACC,EAAY,EAAEC,KAAe,EAAEC,KAAmB,KAAI;IAC/D,MAAM,CAACC,CAAC,EAAEC,MAAM,CAAC,GAAGH,KAA6B;IAEjD,MAAMI,WAAW,GAAkC;MAACF,CAAC;MAAEC,MAAM;MAAEJ;IAAE,CAAC;IAClE,MAAMM,YAAY,GAAmC;MAACH,CAAC;MAAEC,MAAM;MAAEJ;IAAE,CAAC;IAEpE,OAAO;MACLG,CAAC,EAAEA,CAAA,KAAMX,MAAM,CAACe,SAAS,CACrBZ,uBAAuB,EACvBU,WAAwC,EAAEH,KAAK,CAAC;MACpDE,MAAM,EAAEA,CAAA,KAAMZ,MAAM,CAACe,SAAS,CAC1Bb,wBAAwB,EACxBY,YAAyC,EAAEJ,KAAK;KACrD;EACH;CACD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}