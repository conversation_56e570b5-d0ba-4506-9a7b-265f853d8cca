{"ast": null, "code": "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { ENGINE } from '../engine';\nimport { Minimum } from '../kernel_names';\nimport { makeTypesMatch } from '../tensor_util';\nimport { convertToTensor } from '../tensor_util_env';\nimport { assertAndGetBroadcastShape } from './broadcast_util';\nimport { cast } from './cast';\nimport { op } from './operation';\n/**\n * Returns the min of a and b (`a < b ? a : b`) element-wise.\n * Supports broadcasting.\n *\n * We also expose `minimumStrict` which has the same signature as this op and\n * asserts that `a` and `b` are the same shape (does not broadcast).\n *\n * ```js\n * const a = tf.tensor1d([1, 4, 3, 16]);\n * const b = tf.tensor1d([1, 2, 9, 4]);\n *\n * a.minimum(b).print();  // or tf.minimum(a, b)\n * ```\n *\n * ```js\n * // Broadcast minimum a with b.\n * const a = tf.tensor1d([2, 4, 6, 8]);\n * const b = tf.scalar(5);\n *\n * a.minimum(b).print();  // or tf.minimum(a, b)\n * ```\n *\n * @param a The first tensor.\n * @param b The second tensor. Must have the same type as `a`.\n *\n * @doc {heading: 'Operations', subheading: 'Arithmetic'}\n */\nfunction minimum_(a, b) {\n  let $a = convertToTensor(a, 'a', 'minimum');\n  let $b = convertToTensor(b, 'b', 'minimum');\n  [$a, $b] = makeTypesMatch($a, $b);\n  if ($a.dtype === 'bool') {\n    $a = cast($a, 'int32');\n    $b = cast($b, 'int32');\n  }\n  assertAndGetBroadcastShape($a.shape, $b.shape);\n  const inputs = {\n    a: $a,\n    b: $b\n  };\n  return ENGINE.runKernel(Minimum, inputs);\n}\nexport const minimum = /* @__PURE__ */op({\n  minimum_\n});", "map": {"version": 3, "names": ["ENGINE", "Minimum", "makeTypesMatch", "convertToTensor", "assertAndGetBroadcastShape", "cast", "op", "minimum_", "a", "b", "$a", "$b", "dtype", "shape", "inputs", "runKernel", "minimum"], "sources": ["C:\\tfjs-core\\src\\ops\\minimum.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {ENGINE} from '../engine';\nimport {Minimum, MinimumInputs} from '../kernel_names';\nimport {Tensor} from '../tensor';\nimport {NamedTensorMap} from '../tensor_types';\nimport {makeTypesMatch} from '../tensor_util';\nimport {convertToTensor} from '../tensor_util_env';\nimport {TensorLike} from '../types';\n\nimport {assertAndGetBroadcastShape} from './broadcast_util';\nimport {cast} from './cast';\nimport {op} from './operation';\n\n/**\n * Returns the min of a and b (`a < b ? a : b`) element-wise.\n * Supports broadcasting.\n *\n * We also expose `minimumStrict` which has the same signature as this op and\n * asserts that `a` and `b` are the same shape (does not broadcast).\n *\n * ```js\n * const a = tf.tensor1d([1, 4, 3, 16]);\n * const b = tf.tensor1d([1, 2, 9, 4]);\n *\n * a.minimum(b).print();  // or tf.minimum(a, b)\n * ```\n *\n * ```js\n * // Broadcast minimum a with b.\n * const a = tf.tensor1d([2, 4, 6, 8]);\n * const b = tf.scalar(5);\n *\n * a.minimum(b).print();  // or tf.minimum(a, b)\n * ```\n *\n * @param a The first tensor.\n * @param b The second tensor. Must have the same type as `a`.\n *\n * @doc {heading: 'Operations', subheading: 'Arithmetic'}\n */\nfunction minimum_<T extends Tensor>(\n    a: Tensor|TensorLike, b: Tensor|TensorLike): T {\n  let $a = convertToTensor(a, 'a', 'minimum');\n  let $b = convertToTensor(b, 'b', 'minimum');\n  [$a, $b] = makeTypesMatch($a, $b);\n\n  if ($a.dtype === 'bool') {\n    $a = cast($a, 'int32');\n    $b = cast($b, 'int32');\n  }\n\n  assertAndGetBroadcastShape($a.shape, $b.shape);\n\n  const inputs: MinimumInputs = {a: $a, b: $b};\n\n  return ENGINE.runKernel(Minimum, inputs as unknown as NamedTensorMap);\n}\n\nexport const minimum = /* @__PURE__ */ op({minimum_});\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAiBA,SAAQA,MAAM,QAAO,WAAW;AAChC,SAAQC,OAAO,QAAsB,iBAAiB;AAGtD,SAAQC,cAAc,QAAO,gBAAgB;AAC7C,SAAQC,eAAe,QAAO,oBAAoB;AAGlD,SAAQC,0BAA0B,QAAO,kBAAkB;AAC3D,SAAQC,IAAI,QAAO,QAAQ;AAC3B,SAAQC,EAAE,QAAO,aAAa;AAE9B;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2BA,SAASC,QAAQA,CACbC,CAAoB,EAAEC,CAAoB;EAC5C,IAAIC,EAAE,GAAGP,eAAe,CAACK,CAAC,EAAE,GAAG,EAAE,SAAS,CAAC;EAC3C,IAAIG,EAAE,GAAGR,eAAe,CAACM,CAAC,EAAE,GAAG,EAAE,SAAS,CAAC;EAC3C,CAACC,EAAE,EAAEC,EAAE,CAAC,GAAGT,cAAc,CAACQ,EAAE,EAAEC,EAAE,CAAC;EAEjC,IAAID,EAAE,CAACE,KAAK,KAAK,MAAM,EAAE;IACvBF,EAAE,GAAGL,IAAI,CAACK,EAAE,EAAE,OAAO,CAAC;IACtBC,EAAE,GAAGN,IAAI,CAACM,EAAE,EAAE,OAAO,CAAC;;EAGxBP,0BAA0B,CAACM,EAAE,CAACG,KAAK,EAAEF,EAAE,CAACE,KAAK,CAAC;EAE9C,MAAMC,MAAM,GAAkB;IAACN,CAAC,EAAEE,EAAE;IAAED,CAAC,EAAEE;EAAE,CAAC;EAE5C,OAAOX,MAAM,CAACe,SAAS,CAACd,OAAO,EAAEa,MAAmC,CAAC;AACvE;AAEA,OAAO,MAAME,OAAO,GAAG,eAAgBV,EAAE,CAAC;EAACC;AAAQ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}