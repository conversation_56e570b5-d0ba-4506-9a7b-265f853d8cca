{"ast": null, "code": "/**\n * @license\n * Copyright 2018 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { backend_util } from '@tensorflow/tfjs-core';\nexport class BatchNormPackedProgram {\n  constructor(xShape, meanShape, varianceShape, offsetShape, scaleShape, varianceEpsilon) {\n    this.packedInputs = true;\n    this.packedOutput = true;\n    this.variableNames = ['x', 'mean', 'variance'];\n    backend_util.assertAndGetBroadcastShape(xShape, meanShape);\n    backend_util.assertAndGetBroadcastShape(xShape, varianceShape);\n    let offsetSnippet = 'vec4(0.0)';\n    if (offsetShape != null) {\n      backend_util.assertAndGetBroadcastShape(xShape, offsetShape);\n      this.variableNames.push('offset');\n      offsetSnippet = 'getOffsetAtOutCoords()';\n    }\n    let scaleSnippet = 'vec4(1.0)';\n    if (scaleShape != null) {\n      backend_util.assertAndGetBroadcastShape(xShape, scaleShape);\n      this.variableNames.push('scale');\n      scaleSnippet = 'getScaleAtOutCoords()';\n    }\n    this.outputShape = xShape;\n    this.userCode = `\n      void main() {\n        vec4 offset = ${offsetSnippet};\n        vec4 scale = ${scaleSnippet};\n\n        vec4 x = getXAtOutCoords();\n        vec4 mean = getMeanAtOutCoords();\n        vec4 variance = getVarianceAtOutCoords();\n\n        vec4 inv = scale * inversesqrt(variance + vec4(${varianceEpsilon}));\n\n        setOutput((x - mean) * inv + offset);\n      }\n    `;\n  }\n}", "map": {"version": 3, "names": ["backend_util", "BatchNormPackedProgram", "constructor", "xShape", "meanShape", "varianceShape", "offsetShape", "scaleShape", "varianceEpsilon", "packedInputs", "packedOutput", "variableNames", "assertAndGetBroadcastShape", "offsetSnippet", "push", "scaleSnippet", "outputShape", "userCode"], "sources": ["C:\\tfjs-backend-webgl\\src\\batchnorm_packed_gpu.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2018 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {backend_util} from '@tensorflow/tfjs-core';\nimport {GPGPUProgram} from './gpgpu_math';\n\nexport class BatchNormPackedProgram implements GPGPUProgram {\n  variableNames: string[];\n  outputShape: number[];\n  userCode: string;\n  packedInputs = true;\n  packedOutput = true;\n\n  constructor(\n      xShape: number[], meanShape: number[], varianceShape: number[],\n      offsetShape: number[]|null, scaleShape: number[]|null,\n      varianceEpsilon: number) {\n    this.variableNames = ['x', 'mean', 'variance'];\n    backend_util.assertAndGetBroadcastShape(xShape, meanShape);\n    backend_util.assertAndGetBroadcastShape(xShape, varianceShape);\n\n    let offsetSnippet = 'vec4(0.0)';\n    if (offsetShape != null) {\n      backend_util.assertAndGetBroadcastShape(xShape, offsetShape);\n      this.variableNames.push('offset');\n      offsetSnippet = 'getOffsetAtOutCoords()';\n    }\n\n    let scaleSnippet = 'vec4(1.0)';\n    if (scaleShape != null) {\n      backend_util.assertAndGetBroadcastShape(xShape, scaleShape);\n      this.variableNames.push('scale');\n      scaleSnippet = 'getScaleAtOutCoords()';\n    }\n\n    this.outputShape = xShape;\n    this.userCode = `\n      void main() {\n        vec4 offset = ${offsetSnippet};\n        vec4 scale = ${scaleSnippet};\n\n        vec4 x = getXAtOutCoords();\n        vec4 mean = getMeanAtOutCoords();\n        vec4 variance = getVarianceAtOutCoords();\n\n        vec4 inv = scale * inversesqrt(variance + vec4(${varianceEpsilon}));\n\n        setOutput((x - mean) * inv + offset);\n      }\n    `;\n  }\n}\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAiBA,SAAQA,YAAY,QAAO,uBAAuB;AAGlD,OAAM,MAAOC,sBAAsB;EAOjCC,YACIC,MAAgB,EAAEC,SAAmB,EAAEC,aAAuB,EAC9DC,WAA0B,EAAEC,UAAyB,EACrDC,eAAuB;IAN3B,KAAAC,YAAY,GAAG,IAAI;IACnB,KAAAC,YAAY,GAAG,IAAI;IAMjB,IAAI,CAACC,aAAa,GAAG,CAAC,GAAG,EAAE,MAAM,EAAE,UAAU,CAAC;IAC9CX,YAAY,CAACY,0BAA0B,CAACT,MAAM,EAAEC,SAAS,CAAC;IAC1DJ,YAAY,CAACY,0BAA0B,CAACT,MAAM,EAAEE,aAAa,CAAC;IAE9D,IAAIQ,aAAa,GAAG,WAAW;IAC/B,IAAIP,WAAW,IAAI,IAAI,EAAE;MACvBN,YAAY,CAACY,0BAA0B,CAACT,MAAM,EAAEG,WAAW,CAAC;MAC5D,IAAI,CAACK,aAAa,CAACG,IAAI,CAAC,QAAQ,CAAC;MACjCD,aAAa,GAAG,wBAAwB;;IAG1C,IAAIE,YAAY,GAAG,WAAW;IAC9B,IAAIR,UAAU,IAAI,IAAI,EAAE;MACtBP,YAAY,CAACY,0BAA0B,CAACT,MAAM,EAAEI,UAAU,CAAC;MAC3D,IAAI,CAACI,aAAa,CAACG,IAAI,CAAC,OAAO,CAAC;MAChCC,YAAY,GAAG,uBAAuB;;IAGxC,IAAI,CAACC,WAAW,GAAGb,MAAM;IACzB,IAAI,CAACc,QAAQ,GAAG;;wBAEIJ,aAAa;uBACdE,YAAY;;;;;;yDAMsBP,eAAe;;;;KAInE;EACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}