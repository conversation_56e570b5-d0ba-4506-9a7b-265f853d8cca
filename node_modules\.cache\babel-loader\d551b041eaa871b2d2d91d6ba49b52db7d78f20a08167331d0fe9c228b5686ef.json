{"ast": null, "code": "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { ENGINE } from '../engine';\nimport { AvgPool3D } from '../kernel_names';\nimport { convertToTensor } from '../tensor_util_env';\nimport * as util from '../util';\nimport { cast } from './cast';\nimport { checkPadOnDimRoundingMode } from './conv_util';\nimport { op } from './operation';\nimport { reshape } from './reshape';\n/**\n * Computes the 3D average pooling.\n *\n * ```js\n * const x = tf.tensor5d([1, 2, 3, 4, 5, 6, 7, 8], [1, 2, 2, 2, 1]);\n * const result = tf.avgPool3d(x, 2, 1, 'valid');\n * result.print();\n * ```\n *\n * @param x The input tensor, of rank 5 or rank 4 of shape\n *     `[batch, depth, height, width, inChannels]`.\n * @param filterSize The filter size:\n *     `[filterDepth, filterHeight, filterWidth]`.\n *     If `filterSize` is a single number,\n *     then `filterDepth == filterHeight == filterWidth`.\n * @param strides The strides of the pooling:\n *     `[strideDepth, strideHeight, strideWidth]`.\n *     If `strides` is a single number,\n *     then `strideDepth == strideHeight == strideWidth`.\n * @param pad The type of padding algorithm.\n *    - `same` and stride 1: output will be of same size as input,\n *       regardless of filter size.\n *    - `valid`: output will be smaller than input if filter is larger\n *       than 1*1x1.\n *    - For more info, see this guide:\n *     [https://www.tensorflow.org/api_docs/python/tf/nn/convolution](\n *          https://www.tensorflow.org/api_docs/python/tf/nn/convolution)\n * @param dimRoundingMode A string from: 'ceil', 'round', 'floor'. If none is\n *     provided, it will default to truncate.\n * @param dataFormat An optional string from: \"NDHWC\", \"NCDHW\". Defaults to\n *     \"NDHWC\". Specify the data format of the input and output data. With the\n *     default format \"NDHWC\", the data is stored in the order of: [batch,\n *     depth, height, width, channels]. Only \"NDHWC\" is currently supported.\n *\n * @doc {heading: 'Operations', subheading: 'Convolution'}\n */\nfunction avgPool3d_(x, filterSize, strides, pad, dimRoundingMode) {\n  let dataFormat = arguments.length > 5 && arguments[5] !== undefined ? arguments[5] : 'NDHWC';\n  const $x = convertToTensor(x, 'x', 'avgPool3d', 'float32');\n  let x5D = $x;\n  let reshapedTo5D = false;\n  if ($x.rank === 4) {\n    reshapedTo5D = true;\n    x5D = reshape($x, [1, $x.shape[0], $x.shape[1], $x.shape[2], $x.shape[3]]);\n  }\n  util.assert(x5D.rank === 5, () => \"Error in avgPool3d: x must be rank 5 but got rank \".concat(x5D.rank, \".\"));\n  util.assert(dataFormat === 'NDHWC', () => \"Error in avgPool3d: Only NDHWC is currently supported, \" + \"but got dataFormat of \".concat(dataFormat));\n  util.assert(typeof strides === 'number' && strides > 0 || Array.isArray(strides) && strides[0] > 0 && strides[1] > 0 && strides[2] > 0, () => \"Error in avgPool3d: Stride must be > 0, but got '\".concat(strides, \"'\"));\n  checkPadOnDimRoundingMode('avgPool3d', pad, dimRoundingMode);\n  const inputs = {\n    x: x5D\n  };\n  const attrs = {\n    filterSize,\n    strides,\n    pad,\n    dimRoundingMode,\n    dataFormat\n  };\n  // tslint:disable-next-line: no-unnecessary-type-assertion\n  let res = ENGINE.runKernel(AvgPool3D, inputs, attrs);\n  res = cast(res, x5D.dtype);\n  if (reshapedTo5D) {\n    return reshape(res, [res.shape[1], res.shape[2], res.shape[3], res.shape[4]]);\n  }\n  return res;\n}\nexport const avgPool3d = /* @__PURE__ */op({\n  avgPool3d_\n});", "map": {"version": 3, "names": ["ENGINE", "AvgPool3D", "convertToTensor", "util", "cast", "checkPadOnDimRoundingMode", "op", "reshape", "avgPool3d_", "x", "filterSize", "strides", "pad", "dimRoundingMode", "dataFormat", "arguments", "length", "undefined", "$x", "x5D", "reshapedTo5D", "rank", "shape", "assert", "concat", "Array", "isArray", "inputs", "attrs", "res", "runKernel", "dtype", "avgPool3d"], "sources": ["C:\\tfjs-core\\src\\ops\\avg_pool_3d.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {ENGINE} from '../engine';\nimport {AvgPool3D, AvgPool3DAttrs, AvgPool3DInputs} from '../kernel_names';\nimport {NamedAttrMap} from '../kernel_registry';\nimport {Tensor4D, Tensor5D} from '../tensor';\nimport {NamedTensorMap} from '../tensor_types';\nimport {convertToTensor} from '../tensor_util_env';\nimport {TensorLike} from '../types';\nimport * as util from '../util';\n\nimport {cast} from './cast';\nimport {checkPadOnDimRoundingMode} from './conv_util';\nimport {op} from './operation';\nimport {reshape} from './reshape';\n\n/**\n * Computes the 3D average pooling.\n *\n * ```js\n * const x = tf.tensor5d([1, 2, 3, 4, 5, 6, 7, 8], [1, 2, 2, 2, 1]);\n * const result = tf.avgPool3d(x, 2, 1, 'valid');\n * result.print();\n * ```\n *\n * @param x The input tensor, of rank 5 or rank 4 of shape\n *     `[batch, depth, height, width, inChannels]`.\n * @param filterSize The filter size:\n *     `[filterDepth, filterHeight, filterWidth]`.\n *     If `filterSize` is a single number,\n *     then `filterDepth == filterHeight == filterWidth`.\n * @param strides The strides of the pooling:\n *     `[strideDepth, strideHeight, strideWidth]`.\n *     If `strides` is a single number,\n *     then `strideDepth == strideHeight == strideWidth`.\n * @param pad The type of padding algorithm.\n *    - `same` and stride 1: output will be of same size as input,\n *       regardless of filter size.\n *    - `valid`: output will be smaller than input if filter is larger\n *       than 1*1x1.\n *    - For more info, see this guide:\n *     [https://www.tensorflow.org/api_docs/python/tf/nn/convolution](\n *          https://www.tensorflow.org/api_docs/python/tf/nn/convolution)\n * @param dimRoundingMode A string from: 'ceil', 'round', 'floor'. If none is\n *     provided, it will default to truncate.\n * @param dataFormat An optional string from: \"NDHWC\", \"NCDHW\". Defaults to\n *     \"NDHWC\". Specify the data format of the input and output data. With the\n *     default format \"NDHWC\", the data is stored in the order of: [batch,\n *     depth, height, width, channels]. Only \"NDHWC\" is currently supported.\n *\n * @doc {heading: 'Operations', subheading: 'Convolution'}\n */\nfunction avgPool3d_<T extends Tensor4D|Tensor5D>(\n    x: T|TensorLike, filterSize: [number, number, number]|number,\n    strides: [number, number, number]|number, pad: 'valid'|'same'|number,\n    dimRoundingMode?: 'floor'|'round'|'ceil',\n    dataFormat: 'NDHWC'|'NCDHW' = 'NDHWC'): T {\n  const $x = convertToTensor(x, 'x', 'avgPool3d', 'float32');\n\n  let x5D = $x as Tensor5D;\n  let reshapedTo5D = false;\n  if ($x.rank === 4) {\n    reshapedTo5D = true;\n    x5D = reshape($x, [1, $x.shape[0], $x.shape[1], $x.shape[2], $x.shape[3]]);\n  }\n\n  util.assert(\n      x5D.rank === 5,\n      () => `Error in avgPool3d: x must be rank 5 but got rank ${x5D.rank}.`);\n  util.assert(\n      dataFormat === 'NDHWC',\n      () => `Error in avgPool3d: Only NDHWC is currently supported, ` +\n          `but got dataFormat of ${dataFormat}`);\n  util.assert(\n      (typeof strides === 'number' && strides > 0) ||\n          (Array.isArray(strides) && strides[0] > 0 && strides[1] > 0 &&\n           strides[2] > 0),\n      () => `Error in avgPool3d: Stride must be > 0, but got '${strides}'`);\n  checkPadOnDimRoundingMode('avgPool3d', pad, dimRoundingMode);\n  const inputs: AvgPool3DInputs = {x: x5D};\n  const attrs:\n      AvgPool3DAttrs = {filterSize, strides, pad, dimRoundingMode, dataFormat};\n\n  // tslint:disable-next-line: no-unnecessary-type-assertion\n  let res = ENGINE.runKernel(\n                AvgPool3D, inputs as unknown as NamedTensorMap,\n                attrs as unknown as NamedAttrMap) as T;\n\n  res = cast(res, x5D.dtype);\n\n  if (reshapedTo5D) {\n    return reshape(\n               res, [res.shape[1], res.shape[2], res.shape[3], res.shape[4]]) as\n        T;\n  }\n\n  return res;\n}\n\nexport const avgPool3d = /* @__PURE__ */ op({avgPool3d_});\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAiBA,SAAQA,MAAM,QAAO,WAAW;AAChC,SAAQC,SAAS,QAAwC,iBAAiB;AAI1E,SAAQC,eAAe,QAAO,oBAAoB;AAElD,OAAO,KAAKC,IAAI,MAAM,SAAS;AAE/B,SAAQC,IAAI,QAAO,QAAQ;AAC3B,SAAQC,yBAAyB,QAAO,aAAa;AACrD,SAAQC,EAAE,QAAO,aAAa;AAC9B,SAAQC,OAAO,QAAO,WAAW;AAEjC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAoCA,SAASC,UAAUA,CACfC,CAAe,EAAEC,UAA2C,EAC5DC,OAAwC,EAAEC,GAA0B,EACpEC,eAAwC,EACH;EAAA,IAArCC,UAAA,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAA8B,OAAO;EACvC,MAAMG,EAAE,GAAGhB,eAAe,CAACO,CAAC,EAAE,GAAG,EAAE,WAAW,EAAE,SAAS,CAAC;EAE1D,IAAIU,GAAG,GAAGD,EAAc;EACxB,IAAIE,YAAY,GAAG,KAAK;EACxB,IAAIF,EAAE,CAACG,IAAI,KAAK,CAAC,EAAE;IACjBD,YAAY,GAAG,IAAI;IACnBD,GAAG,GAAGZ,OAAO,CAACW,EAAE,EAAE,CAAC,CAAC,EAAEA,EAAE,CAACI,KAAK,CAAC,CAAC,CAAC,EAAEJ,EAAE,CAACI,KAAK,CAAC,CAAC,CAAC,EAAEJ,EAAE,CAACI,KAAK,CAAC,CAAC,CAAC,EAAEJ,EAAE,CAACI,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;;EAG5EnB,IAAI,CAACoB,MAAM,CACPJ,GAAG,CAACE,IAAI,KAAK,CAAC,EACd,2DAAAG,MAAA,CAA2DL,GAAG,CAACE,IAAI,MAAG,CAAC;EAC3ElB,IAAI,CAACoB,MAAM,CACPT,UAAU,KAAK,OAAO,EACtB,MAAM,qFAAAU,MAAA,CACuBV,UAAU,CAAE,CAAC;EAC9CX,IAAI,CAACoB,MAAM,CACN,OAAOZ,OAAO,KAAK,QAAQ,IAAIA,OAAO,GAAG,CAAC,IACtCc,KAAK,CAACC,OAAO,CAACf,OAAO,CAAC,IAAIA,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,IAAIA,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,IAC1DA,OAAO,CAAC,CAAC,CAAC,GAAG,CAAE,EACpB,0DAAAa,MAAA,CAA0Db,OAAO,MAAG,CAAC;EACzEN,yBAAyB,CAAC,WAAW,EAAEO,GAAG,EAAEC,eAAe,CAAC;EAC5D,MAAMc,MAAM,GAAoB;IAAClB,CAAC,EAAEU;EAAG,CAAC;EACxC,MAAMS,KAAK,GACU;IAAClB,UAAU;IAAEC,OAAO;IAAEC,GAAG;IAAEC,eAAe;IAAEC;EAAU,CAAC;EAE5E;EACA,IAAIe,GAAG,GAAG7B,MAAM,CAAC8B,SAAS,CACZ7B,SAAS,EAAE0B,MAAmC,EAC9CC,KAAgC,CAAM;EAEpDC,GAAG,GAAGzB,IAAI,CAACyB,GAAG,EAAEV,GAAG,CAACY,KAAK,CAAC;EAE1B,IAAIX,YAAY,EAAE;IAChB,OAAOb,OAAO,CACHsB,GAAG,EAAE,CAACA,GAAG,CAACP,KAAK,CAAC,CAAC,CAAC,EAAEO,GAAG,CAACP,KAAK,CAAC,CAAC,CAAC,EAAEO,GAAG,CAACP,KAAK,CAAC,CAAC,CAAC,EAAEO,GAAG,CAACP,KAAK,CAAC,CAAC,CAAC,CAAC,CACnE;;EAGP,OAAOO,GAAG;AACZ;AAEA,OAAO,MAAMG,SAAS,GAAG,eAAgB1B,EAAE,CAAC;EAACE;AAAU,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}