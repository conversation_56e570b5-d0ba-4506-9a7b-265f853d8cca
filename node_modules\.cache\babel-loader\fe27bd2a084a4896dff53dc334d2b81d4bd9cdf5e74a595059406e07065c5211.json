{"ast": null, "code": "import isArrayLike from './_isArrayLike.js';\nimport values from './values.js';\nimport indexOf from './indexOf.js';\n\n// Determine if the array or object contains a given item (using `===`).\nexport default function contains(obj, item, fromIndex, guard) {\n  if (!isArrayLike(obj)) obj = values(obj);\n  if (typeof fromIndex != 'number' || guard) fromIndex = 0;\n  return indexOf(obj, item, fromIndex) >= 0;\n}", "map": {"version": 3, "names": ["isArrayLike", "values", "indexOf", "contains", "obj", "item", "fromIndex", "guard"], "sources": ["C:/tmsft/node_modules/underscore/modules/contains.js"], "sourcesContent": ["import isArrayLike from './_isArrayLike.js';\nimport values from './values.js';\nimport indexOf from './indexOf.js';\n\n// Determine if the array or object contains a given item (using `===`).\nexport default function contains(obj, item, fromIndex, guard) {\n  if (!isArrayLike(obj)) obj = values(obj);\n  if (typeof fromIndex != 'number' || guard) fromIndex = 0;\n  return indexOf(obj, item, fromIndex) >= 0;\n}\n"], "mappings": "AAAA,OAAOA,WAAW,MAAM,mBAAmB;AAC3C,OAAOC,MAAM,MAAM,aAAa;AAChC,OAAOC,OAAO,MAAM,cAAc;;AAElC;AACA,eAAe,SAASC,QAAQA,CAACC,GAAG,EAAEC,IAAI,EAAEC,SAAS,EAAEC,KAAK,EAAE;EAC5D,IAAI,CAACP,WAAW,CAACI,GAAG,CAAC,EAAEA,GAAG,GAAGH,MAAM,CAACG,GAAG,CAAC;EACxC,IAAI,OAAOE,SAAS,IAAI,QAAQ,IAAIC,KAAK,EAAED,SAAS,GAAG,CAAC;EACxD,OAAOJ,OAAO,CAACE,GAAG,EAAEC,IAAI,EAAEC,SAAS,CAAC,IAAI,CAAC;AAC3C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}