{"ast": null, "code": "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { ResizeBilinearGrad, util } from '@tensorflow/tfjs-core';\nimport { assertNotComplex } from '../cpu_util';\nexport function resizeBilinearGrad(args) {\n  const {\n    inputs,\n    backend,\n    attrs\n  } = args;\n  const {\n    images,\n    dy\n  } = inputs;\n  const {\n    alignCorners\n  } = attrs;\n  assertNotComplex([dy, images], 'resizeBilinearGrad');\n  const imagesStrides = util.computeStrides(images.shape);\n  const [batch, xHeight, xWidth, depth] = images.shape;\n  const [, yHeight, yWidth] = dy.shape;\n  const output = new Float32Array(batch * xHeight * xWidth * depth);\n  // In the backwards pass, we want to find the pixels that were generated\n  // for each pixel in the input image the forward pass and add the\n  // corresponding coefficient from dy to the gradient (with some\n  // interpolation).\n  const effectiveXSize = [alignCorners && yHeight > 1 ? xHeight - 1 : xHeight, alignCorners && yWidth > 1 ? xWidth - 1 : xWidth];\n  const effectiveYSize = [alignCorners && yHeight > 1 ? yHeight - 1 : yHeight, alignCorners && yWidth > 1 ? yWidth - 1 : yWidth];\n  const heightScale = effectiveXSize[0] / effectiveYSize[0];\n  const widthScale = effectiveXSize[1] / effectiveYSize[1];\n  // Reference implementation\n  // tslint:disable-next-line:max-line-length\n  // https://github.com/tensorflow/tensorflow/blob/3039375c86a5bbc9610c7725dcaa95d635f87ba2/tensorflow/core/kernels/resize_bilinear_op.cc#L275\n  const dyValues = backend.data.get(dy.dataId).values;\n  let offset = 0;\n  for (let b = 0; b < batch; b++) {\n    const bOffset = b * imagesStrides[0];\n    for (let r = 0; r < yHeight; r++) {\n      const dxR = r * heightScale;\n      const topDxRIndex = Math.floor(dxR);\n      const bottomDxRIndex = Math.min(Math.ceil(dxR), xHeight - 1);\n      const topDxROffset = bOffset + topDxRIndex * imagesStrides[1];\n      const bottomDxROffset = bOffset + bottomDxRIndex * imagesStrides[1];\n      const dxRLerp = dxR - topDxRIndex;\n      const inverseDxRLerp = 1.0 - dxRLerp;\n      for (let c = 0; c < yWidth; c++) {\n        const dxC = c * widthScale;\n        const leftDxCIndex = Math.floor(dxC);\n        const rightDxCIndex = Math.min(Math.ceil(dxC), xWidth - 1);\n        const dxCLerp = dxC - leftDxCIndex;\n        const inverseDxCLerp = 1.0 - dxCLerp;\n        const topLeftRCOffset = topDxROffset + leftDxCIndex * imagesStrides[2];\n        const topRightRCOffset = topDxROffset + rightDxCIndex * imagesStrides[2];\n        const bottomLeftRCOffset = bottomDxROffset + leftDxCIndex * imagesStrides[2];\n        const bottomRightRCOffset = bottomDxROffset + rightDxCIndex * imagesStrides[2];\n        const inverseDxRLerpTimesInverseDxCLerp = inverseDxRLerp * inverseDxCLerp;\n        const inverseDxRLerpTimesDxCLerp = inverseDxRLerp * dxCLerp;\n        const dxRLerpTimesInverseDxCLerp = dxRLerp * inverseDxCLerp;\n        const dxRLerpTimesDxCLerp = dxRLerp * dxCLerp;\n        for (let d = 0; d < depth; d++) {\n          const dyVal = dyValues[offset++];\n          output[topLeftRCOffset + d] += dyVal * inverseDxRLerpTimesInverseDxCLerp;\n          output[topRightRCOffset + d] += dyVal * inverseDxRLerpTimesDxCLerp;\n          output[bottomLeftRCOffset + d] += dyVal * dxRLerpTimesInverseDxCLerp;\n          output[bottomRightRCOffset + d] += dyVal * dxRLerpTimesDxCLerp;\n        }\n      }\n    }\n  }\n  return backend.makeTensorInfo([batch, xWidth, xHeight, depth], 'float32', output);\n}\nexport const resizeBilinearGradConfig = {\n  kernelName: ResizeBilinearGrad,\n  backendName: 'cpu',\n  kernelFunc: resizeBilinearGrad\n};", "map": {"version": 3, "names": ["ResizeBilinearGrad", "util", "assertNotComplex", "resizeBilinearGrad", "args", "inputs", "backend", "attrs", "images", "dy", "alignCorners", "<PERSON><PERSON>trides", "computeStrides", "shape", "batch", "xHeight", "xWidth", "depth", "yHeight", "yWidth", "output", "Float32Array", "effectiveXSize", "effectiveYSize", "heightScale", "widthScale", "dyValues", "data", "get", "dataId", "values", "offset", "b", "bOffset", "r", "dxR", "topDxRIndex", "Math", "floor", "bottomDxRIndex", "min", "ceil", "topDxROffset", "bottomDxROffset", "dxRLerp", "inverseDxRLerp", "c", "dxC", "leftDxCIndex", "rightDxCIndex", "dxCLerp", "inverseDxCLerp", "topLeftRCOffset", "topRightRCOffset", "bottomLeftRCOffset", "bottomRightRCOffset", "inverseDxRLerpTimesInverseDxCLerp", "inverseDxRLerpTimesDxCLerp", "dxRLerpTimesInverseDxCLerp", "dxRLerpTimesDxCLerp", "d", "dyVal", "makeTensorInfo", "resizeBilinearGradConfig", "kernelName", "backendName", "kernelFunc"], "sources": ["C:\\tfjs-backend-cpu\\src\\kernels\\ResizeBilinearGrad.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {KernelConfig, KernelFunc, ResizeBilinearGrad, ResizeBilinearGradAttrs, ResizeBilinearGradInputs, TensorInfo, TypedArray, util} from '@tensorflow/tfjs-core';\n\nimport {MathBackendCPU} from '../backend_cpu';\nimport {assertNotComplex} from '../cpu_util';\n\nexport function resizeBilinearGrad(args: {\n  inputs: ResizeBilinearGradInputs,\n  backend: MathBackendCPU,\n  attrs: ResizeBilinearGradAttrs\n}): TensorInfo {\n  const {inputs, backend, attrs} = args;\n  const {images, dy} = inputs;\n  const {alignCorners} = attrs;\n\n  assertNotComplex([dy, images], 'resizeBilinearGrad');\n\n  const imagesStrides = util.computeStrides(images.shape);\n\n  const [batch, xHeight, xWidth, depth] = images.shape;\n  const [, yHeight, yWidth] = dy.shape;\n\n  const output = new Float32Array(batch * xHeight * xWidth * depth);\n\n  // In the backwards pass, we want to find the pixels that were generated\n  // for each pixel in the input image the forward pass and add the\n  // corresponding coefficient from dy to the gradient (with some\n  // interpolation).\n\n  const effectiveXSize: [number, number] = [\n    (alignCorners && yHeight > 1) ? xHeight - 1 : xHeight,\n    (alignCorners && yWidth > 1) ? xWidth - 1 : xWidth\n  ];\n\n  const effectiveYSize: [number, number] = [\n    (alignCorners && yHeight > 1) ? yHeight - 1 : yHeight,\n    (alignCorners && yWidth > 1) ? yWidth - 1 : yWidth\n  ];\n\n  const heightScale = effectiveXSize[0] / effectiveYSize[0];\n  const widthScale = effectiveXSize[1] / effectiveYSize[1];\n\n  // Reference implementation\n  // tslint:disable-next-line:max-line-length\n  // https://github.com/tensorflow/tensorflow/blob/3039375c86a5bbc9610c7725dcaa95d635f87ba2/tensorflow/core/kernels/resize_bilinear_op.cc#L275\n  const dyValues = backend.data.get(dy.dataId).values as TypedArray;\n  let offset = 0;\n  for (let b = 0; b < batch; b++) {\n    const bOffset = b * imagesStrides[0];\n    for (let r = 0; r < yHeight; r++) {\n      const dxR = r * heightScale;\n      const topDxRIndex = Math.floor(dxR);\n      const bottomDxRIndex = Math.min(Math.ceil(dxR), xHeight - 1);\n\n      const topDxROffset = bOffset + topDxRIndex * imagesStrides[1];\n      const bottomDxROffset = bOffset + bottomDxRIndex * imagesStrides[1];\n\n      const dxRLerp = dxR - topDxRIndex;\n      const inverseDxRLerp = 1.0 - dxRLerp;\n      for (let c = 0; c < yWidth; c++) {\n        const dxC = c * widthScale;\n        const leftDxCIndex = Math.floor(dxC);\n        const rightDxCIndex = Math.min(Math.ceil(dxC), xWidth - 1);\n        const dxCLerp = dxC - leftDxCIndex;\n        const inverseDxCLerp = 1.0 - dxCLerp;\n\n        const topLeftRCOffset = topDxROffset + leftDxCIndex * imagesStrides[2];\n        const topRightRCOffset =\n            topDxROffset + rightDxCIndex * imagesStrides[2];\n        const bottomLeftRCOffset =\n            bottomDxROffset + leftDxCIndex * imagesStrides[2];\n        const bottomRightRCOffset =\n            bottomDxROffset + rightDxCIndex * imagesStrides[2];\n\n        const inverseDxRLerpTimesInverseDxCLerp =\n            inverseDxRLerp * inverseDxCLerp;\n        const inverseDxRLerpTimesDxCLerp = inverseDxRLerp * dxCLerp;\n        const dxRLerpTimesInverseDxCLerp = dxRLerp * inverseDxCLerp;\n        const dxRLerpTimesDxCLerp = dxRLerp * dxCLerp;\n        for (let d = 0; d < depth; d++) {\n          const dyVal = dyValues[offset++];\n          output[topLeftRCOffset + d] +=\n              dyVal * inverseDxRLerpTimesInverseDxCLerp;\n          output[topRightRCOffset + d] += dyVal * inverseDxRLerpTimesDxCLerp;\n          output[bottomLeftRCOffset + d] += dyVal * dxRLerpTimesInverseDxCLerp;\n          output[bottomRightRCOffset + d] += dyVal * dxRLerpTimesDxCLerp;\n        }\n      }\n    }\n  }\n\n  return backend.makeTensorInfo(\n      [batch, xWidth, xHeight, depth], 'float32', output);\n}\n\nexport const resizeBilinearGradConfig: KernelConfig = {\n  kernelName: ResizeBilinearGrad,\n  backendName: 'cpu',\n  kernelFunc: resizeBilinearGrad as unknown as KernelFunc\n};\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAiBA,SAAkCA,kBAAkB,EAA6EC,IAAI,QAAO,uBAAuB;AAGnK,SAAQC,gBAAgB,QAAO,aAAa;AAE5C,OAAM,SAAUC,kBAAkBA,CAACC,IAIlC;EACC,MAAM;IAACC,MAAM;IAAEC,OAAO;IAAEC;EAAK,CAAC,GAAGH,IAAI;EACrC,MAAM;IAACI,MAAM;IAAEC;EAAE,CAAC,GAAGJ,MAAM;EAC3B,MAAM;IAACK;EAAY,CAAC,GAAGH,KAAK;EAE5BL,gBAAgB,CAAC,CAACO,EAAE,EAAED,MAAM,CAAC,EAAE,oBAAoB,CAAC;EAEpD,MAAMG,aAAa,GAAGV,IAAI,CAACW,cAAc,CAACJ,MAAM,CAACK,KAAK,CAAC;EAEvD,MAAM,CAACC,KAAK,EAAEC,OAAO,EAAEC,MAAM,EAAEC,KAAK,CAAC,GAAGT,MAAM,CAACK,KAAK;EACpD,MAAM,GAAGK,OAAO,EAAEC,MAAM,CAAC,GAAGV,EAAE,CAACI,KAAK;EAEpC,MAAMO,MAAM,GAAG,IAAIC,YAAY,CAACP,KAAK,GAAGC,OAAO,GAAGC,MAAM,GAAGC,KAAK,CAAC;EAEjE;EACA;EACA;EACA;EAEA,MAAMK,cAAc,GAAqB,CACtCZ,YAAY,IAAIQ,OAAO,GAAG,CAAC,GAAIH,OAAO,GAAG,CAAC,GAAGA,OAAO,EACpDL,YAAY,IAAIS,MAAM,GAAG,CAAC,GAAIH,MAAM,GAAG,CAAC,GAAGA,MAAM,CACnD;EAED,MAAMO,cAAc,GAAqB,CACtCb,YAAY,IAAIQ,OAAO,GAAG,CAAC,GAAIA,OAAO,GAAG,CAAC,GAAGA,OAAO,EACpDR,YAAY,IAAIS,MAAM,GAAG,CAAC,GAAIA,MAAM,GAAG,CAAC,GAAGA,MAAM,CACnD;EAED,MAAMK,WAAW,GAAGF,cAAc,CAAC,CAAC,CAAC,GAAGC,cAAc,CAAC,CAAC,CAAC;EACzD,MAAME,UAAU,GAAGH,cAAc,CAAC,CAAC,CAAC,GAAGC,cAAc,CAAC,CAAC,CAAC;EAExD;EACA;EACA;EACA,MAAMG,QAAQ,GAAGpB,OAAO,CAACqB,IAAI,CAACC,GAAG,CAACnB,EAAE,CAACoB,MAAM,CAAC,CAACC,MAAoB;EACjE,IAAIC,MAAM,GAAG,CAAC;EACd,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGlB,KAAK,EAAEkB,CAAC,EAAE,EAAE;IAC9B,MAAMC,OAAO,GAAGD,CAAC,GAAGrB,aAAa,CAAC,CAAC,CAAC;IACpC,KAAK,IAAIuB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGhB,OAAO,EAAEgB,CAAC,EAAE,EAAE;MAChC,MAAMC,GAAG,GAAGD,CAAC,GAAGV,WAAW;MAC3B,MAAMY,WAAW,GAAGC,IAAI,CAACC,KAAK,CAACH,GAAG,CAAC;MACnC,MAAMI,cAAc,GAAGF,IAAI,CAACG,GAAG,CAACH,IAAI,CAACI,IAAI,CAACN,GAAG,CAAC,EAAEpB,OAAO,GAAG,CAAC,CAAC;MAE5D,MAAM2B,YAAY,GAAGT,OAAO,GAAGG,WAAW,GAAGzB,aAAa,CAAC,CAAC,CAAC;MAC7D,MAAMgC,eAAe,GAAGV,OAAO,GAAGM,cAAc,GAAG5B,aAAa,CAAC,CAAC,CAAC;MAEnE,MAAMiC,OAAO,GAAGT,GAAG,GAAGC,WAAW;MACjC,MAAMS,cAAc,GAAG,GAAG,GAAGD,OAAO;MACpC,KAAK,IAAIE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG3B,MAAM,EAAE2B,CAAC,EAAE,EAAE;QAC/B,MAAMC,GAAG,GAAGD,CAAC,GAAGrB,UAAU;QAC1B,MAAMuB,YAAY,GAAGX,IAAI,CAACC,KAAK,CAACS,GAAG,CAAC;QACpC,MAAME,aAAa,GAAGZ,IAAI,CAACG,GAAG,CAACH,IAAI,CAACI,IAAI,CAACM,GAAG,CAAC,EAAE/B,MAAM,GAAG,CAAC,CAAC;QAC1D,MAAMkC,OAAO,GAAGH,GAAG,GAAGC,YAAY;QAClC,MAAMG,cAAc,GAAG,GAAG,GAAGD,OAAO;QAEpC,MAAME,eAAe,GAAGV,YAAY,GAAGM,YAAY,GAAGrC,aAAa,CAAC,CAAC,CAAC;QACtE,MAAM0C,gBAAgB,GAClBX,YAAY,GAAGO,aAAa,GAAGtC,aAAa,CAAC,CAAC,CAAC;QACnD,MAAM2C,kBAAkB,GACpBX,eAAe,GAAGK,YAAY,GAAGrC,aAAa,CAAC,CAAC,CAAC;QACrD,MAAM4C,mBAAmB,GACrBZ,eAAe,GAAGM,aAAa,GAAGtC,aAAa,CAAC,CAAC,CAAC;QAEtD,MAAM6C,iCAAiC,GACnCX,cAAc,GAAGM,cAAc;QACnC,MAAMM,0BAA0B,GAAGZ,cAAc,GAAGK,OAAO;QAC3D,MAAMQ,0BAA0B,GAAGd,OAAO,GAAGO,cAAc;QAC3D,MAAMQ,mBAAmB,GAAGf,OAAO,GAAGM,OAAO;QAC7C,KAAK,IAAIU,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG3C,KAAK,EAAE2C,CAAC,EAAE,EAAE;UAC9B,MAAMC,KAAK,GAAGnC,QAAQ,CAACK,MAAM,EAAE,CAAC;UAChCX,MAAM,CAACgC,eAAe,GAAGQ,CAAC,CAAC,IACvBC,KAAK,GAAGL,iCAAiC;UAC7CpC,MAAM,CAACiC,gBAAgB,GAAGO,CAAC,CAAC,IAAIC,KAAK,GAAGJ,0BAA0B;UAClErC,MAAM,CAACkC,kBAAkB,GAAGM,CAAC,CAAC,IAAIC,KAAK,GAAGH,0BAA0B;UACpEtC,MAAM,CAACmC,mBAAmB,GAAGK,CAAC,CAAC,IAAIC,KAAK,GAAGF,mBAAmB;;;;;EAMtE,OAAOrD,OAAO,CAACwD,cAAc,CACzB,CAAChD,KAAK,EAAEE,MAAM,EAAED,OAAO,EAAEE,KAAK,CAAC,EAAE,SAAS,EAAEG,MAAM,CAAC;AACzD;AAEA,OAAO,MAAM2C,wBAAwB,GAAiB;EACpDC,UAAU,EAAEhE,kBAAkB;EAC9BiE,WAAW,EAAE,KAAK;EAClBC,UAAU,EAAE/D;CACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}