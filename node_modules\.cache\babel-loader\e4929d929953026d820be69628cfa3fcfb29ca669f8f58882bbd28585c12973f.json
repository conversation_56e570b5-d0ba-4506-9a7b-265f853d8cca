{"ast": null, "code": "/**\n * @license\n * Copyright 2018 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { ENGINE } from '../engine';\nimport { Erf } from '../kernel_names';\nimport { convertToTensor } from '../tensor_util_env';\nimport * as util from '../util';\nimport { cast } from './cast';\nimport { op } from './operation';\n/**\n * Computes Gauss error function of the input `tf.Tensor` element-wise:\n * `erf(x)`\n *\n * ```js\n * const x = tf.tensor1d([0, .1, -.1, .7]);\n *\n * x.erf().print(); // or tf.erf(x);\n * ```\n * @param x The input tensor.\n *\n * @doc {heading: 'Operations', subheading: 'Basic math'}\n */\nfunction erf_(x) {\n  let $x = convertToTensor(x, 'x', 'erf');\n  util.assert($x.dtype === 'int32' || $x.dtype === 'float32', () => 'Input dtype must be `int32` or `float32`.');\n  if ($x.dtype === 'int32') {\n    $x = cast($x, 'float32');\n  }\n  const inputs = {\n    x: $x\n  };\n  return ENGINE.runKernel(Erf, inputs);\n}\nexport const erf = /* @__PURE__ */op({\n  erf_\n});", "map": {"version": 3, "names": ["ENGINE", "E<PERSON>", "convertToTensor", "util", "cast", "op", "erf_", "x", "$x", "assert", "dtype", "inputs", "runKernel", "erf"], "sources": ["C:\\tfjs-core\\src\\ops\\erf.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2018 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {ENGINE} from '../engine';\nimport {Erf, ErfInputs} from '../kernel_names';\nimport {Tensor} from '../tensor';\nimport {NamedTensorMap} from '../tensor_types';\nimport {convertToTensor} from '../tensor_util_env';\nimport {TensorLike} from '../types';\nimport * as util from '../util';\n\nimport {cast} from './cast';\nimport {op} from './operation';\n\n/**\n * Computes Gauss error function of the input `tf.Tensor` element-wise:\n * `erf(x)`\n *\n * ```js\n * const x = tf.tensor1d([0, .1, -.1, .7]);\n *\n * x.erf().print(); // or tf.erf(x);\n * ```\n * @param x The input tensor.\n *\n * @doc {heading: 'Operations', subheading: 'Basic math'}\n */\nfunction erf_<T extends Tensor>(x: T|TensorLike): T {\n  let $x = convertToTensor(x, 'x', 'erf');\n  util.assert(\n      $x.dtype === 'int32' || $x.dtype === 'float32',\n      () => 'Input dtype must be `int32` or `float32`.');\n\n  if ($x.dtype === 'int32') {\n    $x = cast($x, 'float32');\n  }\n\n  const inputs: ErfInputs = {x: $x};\n  return ENGINE.runKernel(Erf, inputs as unknown as NamedTensorMap);\n}\nexport const erf = /* @__PURE__ */ op({erf_});\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAiBA,SAAQA,MAAM,QAAO,WAAW;AAChC,SAAQC,GAAG,QAAkB,iBAAiB;AAG9C,SAAQC,eAAe,QAAO,oBAAoB;AAElD,OAAO,KAAKC,IAAI,MAAM,SAAS;AAE/B,SAAQC,IAAI,QAAO,QAAQ;AAC3B,SAAQC,EAAE,QAAO,aAAa;AAE9B;;;;;;;;;;;;;AAaA,SAASC,IAAIA,CAAmBC,CAAe;EAC7C,IAAIC,EAAE,GAAGN,eAAe,CAACK,CAAC,EAAE,GAAG,EAAE,KAAK,CAAC;EACvCJ,IAAI,CAACM,MAAM,CACPD,EAAE,CAACE,KAAK,KAAK,OAAO,IAAIF,EAAE,CAACE,KAAK,KAAK,SAAS,EAC9C,MAAM,2CAA2C,CAAC;EAEtD,IAAIF,EAAE,CAACE,KAAK,KAAK,OAAO,EAAE;IACxBF,EAAE,GAAGJ,IAAI,CAACI,EAAE,EAAE,SAAS,CAAC;;EAG1B,MAAMG,MAAM,GAAc;IAACJ,CAAC,EAAEC;EAAE,CAAC;EACjC,OAAOR,MAAM,CAACY,SAAS,CAACX,GAAG,EAAEU,MAAmC,CAAC;AACnE;AACA,OAAO,MAAME,GAAG,GAAG,eAAgBR,EAAE,CAAC;EAACC;AAAI,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}