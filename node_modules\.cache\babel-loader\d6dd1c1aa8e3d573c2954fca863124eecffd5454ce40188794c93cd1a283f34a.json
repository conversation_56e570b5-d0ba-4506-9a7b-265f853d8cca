{"ast": null, "code": "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { env, ResizeNearestNeighbor } from '@tensorflow/tfjs-core';\nimport { ResizeNearestNeighborProgram } from '../resize_nearest_neighbor_gpu';\nimport { ResizeNearestNeighborPackedProgram } from '../resize_nearest_neighbor_packed_gpu';\nexport function resizeNearestNeighbor(args) {\n  const {\n    inputs,\n    backend,\n    attrs\n  } = args;\n  const {\n    images\n  } = inputs;\n  const {\n    alignCorners,\n    halfPixelCenters,\n    size\n  } = attrs;\n  const [newHeight, newWidth] = size;\n  const program = env().getBool('WEBGL_PACK_IMAGE_OPERATIONS') ? new ResizeNearestNeighborPackedProgram(images.shape, newHeight, newWidth, alignCorners, halfPixelCenters) : new ResizeNearestNeighborProgram(images.shape, newHeight, newWidth, alignCorners, halfPixelCenters);\n  return backend.runWebGLProgram(program, [images], images.dtype);\n}\nexport const resizeNearestNeighborConfig = {\n  kernelName: ResizeNearestNeighbor,\n  backendName: 'webgl',\n  kernelFunc: resizeNearestNeighbor\n};", "map": {"version": 3, "names": ["env", "ResizeNearestNeighbor", "ResizeNearestNeighborProgram", "ResizeNearestNeighborPackedProgram", "resizeNearestNeighbor", "args", "inputs", "backend", "attrs", "images", "alignCorners", "halfPixelCenters", "size", "newHeight", "newWidth", "program", "getBool", "shape", "runWebGLProgram", "dtype", "resizeNearestNeighborConfig", "kernelName", "backendName", "kernelFunc"], "sources": ["C:\\tfjs-backend-webgl\\src\\kernels\\ResizeNearestNeighbor.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {env, KernelConfig, KernelFunc, ResizeNearestNeighbor, ResizeNearestNeighborAttrs, ResizeNearestNeighborInputs, TensorInfo} from '@tensorflow/tfjs-core';\n\nimport {MathBackendWebGL} from '../backend_webgl';\nimport {ResizeNearestNeighborProgram} from '../resize_nearest_neighbor_gpu';\nimport {ResizeNearestNeighborPackedProgram} from '../resize_nearest_neighbor_packed_gpu';\n\nexport function resizeNearestNeighbor(args: {\n  inputs: ResizeNearestNeighborInputs,\n  backend: MathBackendWebGL,\n  attrs: ResizeNearestNeighborAttrs\n}): TensorInfo {\n  const {inputs, backend, attrs} = args;\n  const {images} = inputs;\n  const {alignCorners, halfPixelCenters, size} = attrs;\n\n  const [newHeight, newWidth] = size;\n\n  const program = env().getBool('WEBGL_PACK_IMAGE_OPERATIONS') ?\n      new ResizeNearestNeighborPackedProgram(\n          images.shape as [number, number, number, number], newHeight, newWidth,\n          alignCorners, halfPixelCenters) :\n      new ResizeNearestNeighborProgram(\n          images.shape as [number, number, number, number], newHeight, newWidth,\n          alignCorners, halfPixelCenters);\n  return backend.runWebGLProgram(program, [images], images.dtype);\n}\n\nexport const resizeNearestNeighborConfig: KernelConfig = {\n  kernelName: ResizeNearestNeighbor,\n  backendName: 'webgl',\n  kernelFunc: resizeNearestNeighbor as unknown as KernelFunc\n};\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAiBA,SAAQA,GAAG,EAA4BC,qBAAqB,QAA4E,uBAAuB;AAG/J,SAAQC,4BAA4B,QAAO,gCAAgC;AAC3E,SAAQC,kCAAkC,QAAO,uCAAuC;AAExF,OAAM,SAAUC,qBAAqBA,CAACC,IAIrC;EACC,MAAM;IAACC,MAAM;IAAEC,OAAO;IAAEC;EAAK,CAAC,GAAGH,IAAI;EACrC,MAAM;IAACI;EAAM,CAAC,GAAGH,MAAM;EACvB,MAAM;IAACI,YAAY;IAAEC,gBAAgB;IAAEC;EAAI,CAAC,GAAGJ,KAAK;EAEpD,MAAM,CAACK,SAAS,EAAEC,QAAQ,CAAC,GAAGF,IAAI;EAElC,MAAMG,OAAO,GAAGf,GAAG,EAAE,CAACgB,OAAO,CAAC,6BAA6B,CAAC,GACxD,IAAIb,kCAAkC,CAClCM,MAAM,CAACQ,KAAyC,EAAEJ,SAAS,EAAEC,QAAQ,EACrEJ,YAAY,EAAEC,gBAAgB,CAAC,GACnC,IAAIT,4BAA4B,CAC5BO,MAAM,CAACQ,KAAyC,EAAEJ,SAAS,EAAEC,QAAQ,EACrEJ,YAAY,EAAEC,gBAAgB,CAAC;EACvC,OAAOJ,OAAO,CAACW,eAAe,CAACH,OAAO,EAAE,CAACN,MAAM,CAAC,EAAEA,MAAM,CAACU,KAAK,CAAC;AACjE;AAEA,OAAO,MAAMC,2BAA2B,GAAiB;EACvDC,UAAU,EAAEpB,qBAAqB;EACjCqB,WAAW,EAAE,OAAO;EACpBC,UAAU,EAAEnB;CACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}