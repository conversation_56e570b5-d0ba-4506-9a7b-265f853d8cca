{"ast": null, "code": "import _has from './_has.js';\nimport toPath from './_toPath.js';\n\n// Shortcut function for checking if an object has a given property directly on\n// itself (in other words, not on a prototype). Unlike the internal `has`\n// function, this public version can also traverse nested properties.\nexport default function has(obj, path) {\n  path = toPath(path);\n  var length = path.length;\n  for (var i = 0; i < length; i++) {\n    var key = path[i];\n    if (!_has(obj, key)) return false;\n    obj = obj[key];\n  }\n  return !!length;\n}", "map": {"version": 3, "names": ["_has", "to<PERSON><PERSON>", "has", "obj", "path", "length", "i", "key"], "sources": ["C:/tmsft/node_modules/underscore/modules/has.js"], "sourcesContent": ["import _has from './_has.js';\nimport toPath from './_toPath.js';\n\n// Shortcut function for checking if an object has a given property directly on\n// itself (in other words, not on a prototype). Unlike the internal `has`\n// function, this public version can also traverse nested properties.\nexport default function has(obj, path) {\n  path = toPath(path);\n  var length = path.length;\n  for (var i = 0; i < length; i++) {\n    var key = path[i];\n    if (!_has(obj, key)) return false;\n    obj = obj[key];\n  }\n  return !!length;\n}\n"], "mappings": "AAAA,OAAOA,IAAI,MAAM,WAAW;AAC5B,OAAOC,MAAM,MAAM,cAAc;;AAEjC;AACA;AACA;AACA,eAAe,SAASC,GAAGA,CAACC,GAAG,EAAEC,IAAI,EAAE;EACrCA,IAAI,GAAGH,MAAM,CAACG,IAAI,CAAC;EACnB,IAAIC,MAAM,GAAGD,IAAI,CAACC,MAAM;EACxB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,MAAM,EAAEC,CAAC,EAAE,EAAE;IAC/B,IAAIC,GAAG,GAAGH,IAAI,CAACE,CAAC,CAAC;IACjB,IAAI,CAACN,IAAI,CAACG,GAAG,EAAEI,GAAG,CAAC,EAAE,OAAO,KAAK;IACjCJ,GAAG,GAAGA,GAAG,CAACI,GAAG,CAAC;EAChB;EACA,OAAO,CAAC,CAACF,MAAM;AACjB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}