{"ast": null, "code": "'use strict';\n\nconst EventEmitter = require('events').EventEmitter;\nconst NOOP = function () {};\nconst removeWhere = (list, predicate) => {\n  const i = list.findIndex(predicate);\n  return i === -1 ? undefined : list.splice(i, 1)[0];\n};\nclass IdleItem {\n  constructor(client, idleListener, timeoutId) {\n    this.client = client;\n    this.idleListener = idleListener;\n    this.timeoutId = timeoutId;\n  }\n}\nclass PendingItem {\n  constructor(callback) {\n    this.callback = callback;\n  }\n}\nfunction throwOnDoubleRelease() {\n  throw new Error('Release called on client which has already been released to the pool.');\n}\nfunction promisify(Promise, callback) {\n  if (callback) {\n    return {\n      callback: callback,\n      result: undefined\n    };\n  }\n  let rej;\n  let res;\n  const cb = function (err, client) {\n    err ? rej(err) : res(client);\n  };\n  const result = new Promise(function (resolve, reject) {\n    res = resolve;\n    rej = reject;\n  }).catch(err => {\n    // replace the stack trace that leads to `TCP.onStreamRead` with one that leads back to the\n    // application that created the query\n    Error.captureStackTrace(err);\n    throw err;\n  });\n  return {\n    callback: cb,\n    result: result\n  };\n}\nfunction makeIdleListener(pool, client) {\n  return function idleListener(err) {\n    err.client = client;\n    client.removeListener('error', idleListener);\n    client.on('error', () => {\n      pool.log('additional client error after disconnection due to error', err);\n    });\n    pool._remove(client);\n    // TODO - document that once the pool emits an error\n    // the client has already been closed & purged and is unusable\n    pool.emit('error', err, client);\n  };\n}\nclass Pool extends EventEmitter {\n  constructor(options, Client) {\n    super();\n    this.options = Object.assign({}, options);\n    if (options != null && 'password' in options) {\n      // \"hiding\" the password so it doesn't show up in stack traces\n      // or if the client is console.logged\n      Object.defineProperty(this.options, 'password', {\n        configurable: true,\n        enumerable: false,\n        writable: true,\n        value: options.password\n      });\n    }\n    if (options != null && options.ssl && options.ssl.key) {\n      // \"hiding\" the ssl->key so it doesn't show up in stack traces\n      // or if the client is console.logged\n      Object.defineProperty(this.options.ssl, 'key', {\n        enumerable: false\n      });\n    }\n    this.options.max = this.options.max || this.options.poolSize || 10;\n    this.options.min = this.options.min || 0;\n    this.options.maxUses = this.options.maxUses || Infinity;\n    this.options.allowExitOnIdle = this.options.allowExitOnIdle || false;\n    this.options.maxLifetimeSeconds = this.options.maxLifetimeSeconds || 0;\n    this.log = this.options.log || function () {};\n    this.Client = this.options.Client || Client || require('pg').Client;\n    this.Promise = this.options.Promise || global.Promise;\n    if (typeof this.options.idleTimeoutMillis === 'undefined') {\n      this.options.idleTimeoutMillis = 10000;\n    }\n    this._clients = [];\n    this._idle = [];\n    this._expired = new WeakSet();\n    this._pendingQueue = [];\n    this._endCallback = undefined;\n    this.ending = false;\n    this.ended = false;\n  }\n  _isFull() {\n    return this._clients.length >= this.options.max;\n  }\n  _isAboveMin() {\n    return this._clients.length > this.options.min;\n  }\n  _pulseQueue() {\n    this.log('pulse queue');\n    if (this.ended) {\n      this.log('pulse queue ended');\n      return;\n    }\n    if (this.ending) {\n      this.log('pulse queue on ending');\n      if (this._idle.length) {\n        this._idle.slice().map(item => {\n          this._remove(item.client);\n        });\n      }\n      if (!this._clients.length) {\n        this.ended = true;\n        this._endCallback();\n      }\n      return;\n    }\n\n    // if we don't have any waiting, do nothing\n    if (!this._pendingQueue.length) {\n      this.log('no queued requests');\n      return;\n    }\n    // if we don't have any idle clients and we have no more room do nothing\n    if (!this._idle.length && this._isFull()) {\n      return;\n    }\n    const pendingItem = this._pendingQueue.shift();\n    if (this._idle.length) {\n      const idleItem = this._idle.pop();\n      clearTimeout(idleItem.timeoutId);\n      const client = idleItem.client;\n      client.ref && client.ref();\n      const idleListener = idleItem.idleListener;\n      return this._acquireClient(client, pendingItem, idleListener, false);\n    }\n    if (!this._isFull()) {\n      return this.newClient(pendingItem);\n    }\n    throw new Error('unexpected condition');\n  }\n  _remove(client) {\n    const removed = removeWhere(this._idle, item => item.client === client);\n    if (removed !== undefined) {\n      clearTimeout(removed.timeoutId);\n    }\n    this._clients = this._clients.filter(c => c !== client);\n    client.end();\n    this.emit('remove', client);\n  }\n  connect(cb) {\n    if (this.ending) {\n      const err = new Error('Cannot use a pool after calling end on the pool');\n      return cb ? cb(err) : this.Promise.reject(err);\n    }\n    const response = promisify(this.Promise, cb);\n    const result = response.result;\n\n    // if we don't have to connect a new client, don't do so\n    if (this._isFull() || this._idle.length) {\n      // if we have idle clients schedule a pulse immediately\n      if (this._idle.length) {\n        process.nextTick(() => this._pulseQueue());\n      }\n      if (!this.options.connectionTimeoutMillis) {\n        this._pendingQueue.push(new PendingItem(response.callback));\n        return result;\n      }\n      const queueCallback = (err, res, done) => {\n        clearTimeout(tid);\n        response.callback(err, res, done);\n      };\n      const pendingItem = new PendingItem(queueCallback);\n\n      // set connection timeout on checking out an existing client\n      const tid = setTimeout(() => {\n        // remove the callback from pending waiters because\n        // we're going to call it with a timeout error\n        removeWhere(this._pendingQueue, i => i.callback === queueCallback);\n        pendingItem.timedOut = true;\n        response.callback(new Error('timeout exceeded when trying to connect'));\n      }, this.options.connectionTimeoutMillis);\n      if (tid.unref) {\n        tid.unref();\n      }\n      this._pendingQueue.push(pendingItem);\n      return result;\n    }\n    this.newClient(new PendingItem(response.callback));\n    return result;\n  }\n  newClient(pendingItem) {\n    const client = new this.Client(this.options);\n    this._clients.push(client);\n    const idleListener = makeIdleListener(this, client);\n    this.log('checking client timeout');\n\n    // connection timeout logic\n    let tid;\n    let timeoutHit = false;\n    if (this.options.connectionTimeoutMillis) {\n      tid = setTimeout(() => {\n        this.log('ending client due to timeout');\n        timeoutHit = true;\n        // force kill the node driver, and let libpq do its teardown\n        client.connection ? client.connection.stream.destroy() : client.end();\n      }, this.options.connectionTimeoutMillis);\n    }\n    this.log('connecting new client');\n    client.connect(err => {\n      if (tid) {\n        clearTimeout(tid);\n      }\n      client.on('error', idleListener);\n      if (err) {\n        this.log('client failed to connect', err);\n        // remove the dead client from our list of clients\n        this._clients = this._clients.filter(c => c !== client);\n        if (timeoutHit) {\n          err = new Error('Connection terminated due to connection timeout', {\n            cause: err\n          });\n        }\n\n        // this client won’t be released, so move on immediately\n        this._pulseQueue();\n        if (!pendingItem.timedOut) {\n          pendingItem.callback(err, undefined, NOOP);\n        }\n      } else {\n        this.log('new client connected');\n        if (this.options.maxLifetimeSeconds !== 0) {\n          const maxLifetimeTimeout = setTimeout(() => {\n            this.log('ending client due to expired lifetime');\n            this._expired.add(client);\n            const idleIndex = this._idle.findIndex(idleItem => idleItem.client === client);\n            if (idleIndex !== -1) {\n              this._acquireClient(client, new PendingItem((err, client, clientRelease) => clientRelease()), idleListener, false);\n            }\n          }, this.options.maxLifetimeSeconds * 1000);\n          maxLifetimeTimeout.unref();\n          client.once('end', () => clearTimeout(maxLifetimeTimeout));\n        }\n        return this._acquireClient(client, pendingItem, idleListener, true);\n      }\n    });\n  }\n\n  // acquire a client for a pending work item\n  _acquireClient(client, pendingItem, idleListener, isNew) {\n    if (isNew) {\n      this.emit('connect', client);\n    }\n    this.emit('acquire', client);\n    client.release = this._releaseOnce(client, idleListener);\n    client.removeListener('error', idleListener);\n    if (!pendingItem.timedOut) {\n      if (isNew && this.options.verify) {\n        this.options.verify(client, err => {\n          if (err) {\n            client.release(err);\n            return pendingItem.callback(err, undefined, NOOP);\n          }\n          pendingItem.callback(undefined, client, client.release);\n        });\n      } else {\n        pendingItem.callback(undefined, client, client.release);\n      }\n    } else {\n      if (isNew && this.options.verify) {\n        this.options.verify(client, client.release);\n      } else {\n        client.release();\n      }\n    }\n  }\n\n  // returns a function that wraps _release and throws if called more than once\n  _releaseOnce(client, idleListener) {\n    let released = false;\n    return err => {\n      if (released) {\n        throwOnDoubleRelease();\n      }\n      released = true;\n      this._release(client, idleListener, err);\n    };\n  }\n\n  // release a client back to the poll, include an error\n  // to remove it from the pool\n  _release(client, idleListener, err) {\n    client.on('error', idleListener);\n    client._poolUseCount = (client._poolUseCount || 0) + 1;\n    this.emit('release', err, client);\n\n    // TODO(bmc): expose a proper, public interface _queryable and _ending\n    if (err || this.ending || !client._queryable || client._ending || client._poolUseCount >= this.options.maxUses) {\n      if (client._poolUseCount >= this.options.maxUses) {\n        this.log('remove expended client');\n      }\n      this._remove(client);\n      this._pulseQueue();\n      return;\n    }\n    const isExpired = this._expired.has(client);\n    if (isExpired) {\n      this.log('remove expired client');\n      this._expired.delete(client);\n      this._remove(client);\n      this._pulseQueue();\n      return;\n    }\n\n    // idle timeout\n    let tid;\n    if (this.options.idleTimeoutMillis && this._isAboveMin()) {\n      tid = setTimeout(() => {\n        this.log('remove idle client');\n        this._remove(client);\n      }, this.options.idleTimeoutMillis);\n      if (this.options.allowExitOnIdle) {\n        // allow Node to exit if this is all that's left\n        tid.unref();\n      }\n    }\n    if (this.options.allowExitOnIdle) {\n      client.unref();\n    }\n    this._idle.push(new IdleItem(client, idleListener, tid));\n    this._pulseQueue();\n  }\n  query(text, values, cb) {\n    // guard clause against passing a function as the first parameter\n    if (typeof text === 'function') {\n      const response = promisify(this.Promise, text);\n      setImmediate(function () {\n        return response.callback(new Error('Passing a function as the first parameter to pool.query is not supported'));\n      });\n      return response.result;\n    }\n\n    // allow plain text query without values\n    if (typeof values === 'function') {\n      cb = values;\n      values = undefined;\n    }\n    const response = promisify(this.Promise, cb);\n    cb = response.callback;\n    this.connect((err, client) => {\n      if (err) {\n        return cb(err);\n      }\n      let clientReleased = false;\n      const onError = err => {\n        if (clientReleased) {\n          return;\n        }\n        clientReleased = true;\n        client.release(err);\n        cb(err);\n      };\n      client.once('error', onError);\n      this.log('dispatching query');\n      try {\n        client.query(text, values, (err, res) => {\n          this.log('query dispatched');\n          client.removeListener('error', onError);\n          if (clientReleased) {\n            return;\n          }\n          clientReleased = true;\n          client.release(err);\n          if (err) {\n            return cb(err);\n          }\n          return cb(undefined, res);\n        });\n      } catch (err) {\n        client.release(err);\n        return cb(err);\n      }\n    });\n    return response.result;\n  }\n  end(cb) {\n    this.log('ending');\n    if (this.ending) {\n      const err = new Error('Called end on pool more than once');\n      return cb ? cb(err) : this.Promise.reject(err);\n    }\n    this.ending = true;\n    const promised = promisify(this.Promise, cb);\n    this._endCallback = promised.callback;\n    this._pulseQueue();\n    return promised.result;\n  }\n  get waitingCount() {\n    return this._pendingQueue.length;\n  }\n  get idleCount() {\n    return this._idle.length;\n  }\n  get expiredCount() {\n    return this._clients.reduce((acc, client) => acc + (this._expired.has(client) ? 1 : 0), 0);\n  }\n  get totalCount() {\n    return this._clients.length;\n  }\n}\nmodule.exports = Pool;", "map": {"version": 3, "names": ["EventEmitter", "require", "NOOP", "removeWhere", "list", "predicate", "i", "findIndex", "undefined", "splice", "IdleItem", "constructor", "client", "idleListener", "timeoutId", "PendingItem", "callback", "throwOnDoubleRelease", "Error", "promisify", "Promise", "result", "rej", "res", "cb", "err", "resolve", "reject", "catch", "captureStackTrace", "makeIdleListener", "pool", "removeListener", "on", "log", "_remove", "emit", "Pool", "options", "Client", "Object", "assign", "defineProperty", "configurable", "enumerable", "writable", "value", "password", "ssl", "key", "max", "poolSize", "min", "maxUses", "Infinity", "allowExitOnIdle", "maxLifetimeSeconds", "global", "idleTimeoutMillis", "_clients", "_idle", "_expired", "WeakSet", "_pendingQueue", "_endCallback", "ending", "ended", "_isFull", "length", "_isAboveMin", "_pulseQueue", "slice", "map", "item", "pendingItem", "shift", "idleItem", "pop", "clearTimeout", "ref", "_acquireClient", "newClient", "removed", "filter", "c", "end", "connect", "response", "process", "nextTick", "connectionTimeoutMillis", "push", "queue<PERSON>allback", "done", "tid", "setTimeout", "timedOut", "unref", "timeoutHit", "connection", "stream", "destroy", "cause", "maxLifetimeTimeout", "add", "idleIndex", "clientRelease", "once", "isNew", "release", "_releaseOnce", "verify", "released", "_release", "_poolUseCount", "_queryable", "_ending", "isExpired", "has", "delete", "query", "text", "values", "setImmediate", "clientReleased", "onError", "promised", "waitingCount", "idleCount", "expiredCount", "reduce", "acc", "totalCount", "module", "exports"], "sources": ["C:/tmsft/node_modules/pg-pool/index.js"], "sourcesContent": ["'use strict'\nconst EventEmitter = require('events').EventEmitter\n\nconst NOOP = function () {}\n\nconst removeWhere = (list, predicate) => {\n  const i = list.findIndex(predicate)\n\n  return i === -1 ? undefined : list.splice(i, 1)[0]\n}\n\nclass IdleItem {\n  constructor(client, idleListener, timeoutId) {\n    this.client = client\n    this.idleListener = idleListener\n    this.timeoutId = timeoutId\n  }\n}\n\nclass PendingItem {\n  constructor(callback) {\n    this.callback = callback\n  }\n}\n\nfunction throwOnDoubleRelease() {\n  throw new Error('Release called on client which has already been released to the pool.')\n}\n\nfunction promisify(Promise, callback) {\n  if (callback) {\n    return { callback: callback, result: undefined }\n  }\n  let rej\n  let res\n  const cb = function (err, client) {\n    err ? rej(err) : res(client)\n  }\n  const result = new Promise(function (resolve, reject) {\n    res = resolve\n    rej = reject\n  }).catch((err) => {\n    // replace the stack trace that leads to `TCP.onStreamRead` with one that leads back to the\n    // application that created the query\n    Error.captureStackTrace(err)\n    throw err\n  })\n  return { callback: cb, result: result }\n}\n\nfunction makeIdleListener(pool, client) {\n  return function idleListener(err) {\n    err.client = client\n\n    client.removeListener('error', idleListener)\n    client.on('error', () => {\n      pool.log('additional client error after disconnection due to error', err)\n    })\n    pool._remove(client)\n    // TODO - document that once the pool emits an error\n    // the client has already been closed & purged and is unusable\n    pool.emit('error', err, client)\n  }\n}\n\nclass Pool extends EventEmitter {\n  constructor(options, Client) {\n    super()\n    this.options = Object.assign({}, options)\n\n    if (options != null && 'password' in options) {\n      // \"hiding\" the password so it doesn't show up in stack traces\n      // or if the client is console.logged\n      Object.defineProperty(this.options, 'password', {\n        configurable: true,\n        enumerable: false,\n        writable: true,\n        value: options.password,\n      })\n    }\n    if (options != null && options.ssl && options.ssl.key) {\n      // \"hiding\" the ssl->key so it doesn't show up in stack traces\n      // or if the client is console.logged\n      Object.defineProperty(this.options.ssl, 'key', {\n        enumerable: false,\n      })\n    }\n\n    this.options.max = this.options.max || this.options.poolSize || 10\n    this.options.min = this.options.min || 0\n    this.options.maxUses = this.options.maxUses || Infinity\n    this.options.allowExitOnIdle = this.options.allowExitOnIdle || false\n    this.options.maxLifetimeSeconds = this.options.maxLifetimeSeconds || 0\n    this.log = this.options.log || function () {}\n    this.Client = this.options.Client || Client || require('pg').Client\n    this.Promise = this.options.Promise || global.Promise\n\n    if (typeof this.options.idleTimeoutMillis === 'undefined') {\n      this.options.idleTimeoutMillis = 10000\n    }\n\n    this._clients = []\n    this._idle = []\n    this._expired = new WeakSet()\n    this._pendingQueue = []\n    this._endCallback = undefined\n    this.ending = false\n    this.ended = false\n  }\n\n  _isFull() {\n    return this._clients.length >= this.options.max\n  }\n\n  _isAboveMin() {\n    return this._clients.length > this.options.min\n  }\n\n  _pulseQueue() {\n    this.log('pulse queue')\n    if (this.ended) {\n      this.log('pulse queue ended')\n      return\n    }\n    if (this.ending) {\n      this.log('pulse queue on ending')\n      if (this._idle.length) {\n        this._idle.slice().map((item) => {\n          this._remove(item.client)\n        })\n      }\n      if (!this._clients.length) {\n        this.ended = true\n        this._endCallback()\n      }\n      return\n    }\n\n    // if we don't have any waiting, do nothing\n    if (!this._pendingQueue.length) {\n      this.log('no queued requests')\n      return\n    }\n    // if we don't have any idle clients and we have no more room do nothing\n    if (!this._idle.length && this._isFull()) {\n      return\n    }\n    const pendingItem = this._pendingQueue.shift()\n    if (this._idle.length) {\n      const idleItem = this._idle.pop()\n      clearTimeout(idleItem.timeoutId)\n      const client = idleItem.client\n      client.ref && client.ref()\n      const idleListener = idleItem.idleListener\n\n      return this._acquireClient(client, pendingItem, idleListener, false)\n    }\n    if (!this._isFull()) {\n      return this.newClient(pendingItem)\n    }\n    throw new Error('unexpected condition')\n  }\n\n  _remove(client) {\n    const removed = removeWhere(this._idle, (item) => item.client === client)\n\n    if (removed !== undefined) {\n      clearTimeout(removed.timeoutId)\n    }\n\n    this._clients = this._clients.filter((c) => c !== client)\n    client.end()\n    this.emit('remove', client)\n  }\n\n  connect(cb) {\n    if (this.ending) {\n      const err = new Error('Cannot use a pool after calling end on the pool')\n      return cb ? cb(err) : this.Promise.reject(err)\n    }\n\n    const response = promisify(this.Promise, cb)\n    const result = response.result\n\n    // if we don't have to connect a new client, don't do so\n    if (this._isFull() || this._idle.length) {\n      // if we have idle clients schedule a pulse immediately\n      if (this._idle.length) {\n        process.nextTick(() => this._pulseQueue())\n      }\n\n      if (!this.options.connectionTimeoutMillis) {\n        this._pendingQueue.push(new PendingItem(response.callback))\n        return result\n      }\n\n      const queueCallback = (err, res, done) => {\n        clearTimeout(tid)\n        response.callback(err, res, done)\n      }\n\n      const pendingItem = new PendingItem(queueCallback)\n\n      // set connection timeout on checking out an existing client\n      const tid = setTimeout(() => {\n        // remove the callback from pending waiters because\n        // we're going to call it with a timeout error\n        removeWhere(this._pendingQueue, (i) => i.callback === queueCallback)\n        pendingItem.timedOut = true\n        response.callback(new Error('timeout exceeded when trying to connect'))\n      }, this.options.connectionTimeoutMillis)\n\n      if (tid.unref) {\n        tid.unref()\n      }\n\n      this._pendingQueue.push(pendingItem)\n      return result\n    }\n\n    this.newClient(new PendingItem(response.callback))\n\n    return result\n  }\n\n  newClient(pendingItem) {\n    const client = new this.Client(this.options)\n    this._clients.push(client)\n    const idleListener = makeIdleListener(this, client)\n\n    this.log('checking client timeout')\n\n    // connection timeout logic\n    let tid\n    let timeoutHit = false\n    if (this.options.connectionTimeoutMillis) {\n      tid = setTimeout(() => {\n        this.log('ending client due to timeout')\n        timeoutHit = true\n        // force kill the node driver, and let libpq do its teardown\n        client.connection ? client.connection.stream.destroy() : client.end()\n      }, this.options.connectionTimeoutMillis)\n    }\n\n    this.log('connecting new client')\n    client.connect((err) => {\n      if (tid) {\n        clearTimeout(tid)\n      }\n      client.on('error', idleListener)\n      if (err) {\n        this.log('client failed to connect', err)\n        // remove the dead client from our list of clients\n        this._clients = this._clients.filter((c) => c !== client)\n        if (timeoutHit) {\n          err = new Error('Connection terminated due to connection timeout', { cause: err })\n        }\n\n        // this client won’t be released, so move on immediately\n        this._pulseQueue()\n\n        if (!pendingItem.timedOut) {\n          pendingItem.callback(err, undefined, NOOP)\n        }\n      } else {\n        this.log('new client connected')\n\n        if (this.options.maxLifetimeSeconds !== 0) {\n          const maxLifetimeTimeout = setTimeout(() => {\n            this.log('ending client due to expired lifetime')\n            this._expired.add(client)\n            const idleIndex = this._idle.findIndex((idleItem) => idleItem.client === client)\n            if (idleIndex !== -1) {\n              this._acquireClient(\n                client,\n                new PendingItem((err, client, clientRelease) => clientRelease()),\n                idleListener,\n                false\n              )\n            }\n          }, this.options.maxLifetimeSeconds * 1000)\n\n          maxLifetimeTimeout.unref()\n          client.once('end', () => clearTimeout(maxLifetimeTimeout))\n        }\n\n        return this._acquireClient(client, pendingItem, idleListener, true)\n      }\n    })\n  }\n\n  // acquire a client for a pending work item\n  _acquireClient(client, pendingItem, idleListener, isNew) {\n    if (isNew) {\n      this.emit('connect', client)\n    }\n\n    this.emit('acquire', client)\n\n    client.release = this._releaseOnce(client, idleListener)\n\n    client.removeListener('error', idleListener)\n\n    if (!pendingItem.timedOut) {\n      if (isNew && this.options.verify) {\n        this.options.verify(client, (err) => {\n          if (err) {\n            client.release(err)\n            return pendingItem.callback(err, undefined, NOOP)\n          }\n\n          pendingItem.callback(undefined, client, client.release)\n        })\n      } else {\n        pendingItem.callback(undefined, client, client.release)\n      }\n    } else {\n      if (isNew && this.options.verify) {\n        this.options.verify(client, client.release)\n      } else {\n        client.release()\n      }\n    }\n  }\n\n  // returns a function that wraps _release and throws if called more than once\n  _releaseOnce(client, idleListener) {\n    let released = false\n\n    return (err) => {\n      if (released) {\n        throwOnDoubleRelease()\n      }\n\n      released = true\n      this._release(client, idleListener, err)\n    }\n  }\n\n  // release a client back to the poll, include an error\n  // to remove it from the pool\n  _release(client, idleListener, err) {\n    client.on('error', idleListener)\n\n    client._poolUseCount = (client._poolUseCount || 0) + 1\n\n    this.emit('release', err, client)\n\n    // TODO(bmc): expose a proper, public interface _queryable and _ending\n    if (err || this.ending || !client._queryable || client._ending || client._poolUseCount >= this.options.maxUses) {\n      if (client._poolUseCount >= this.options.maxUses) {\n        this.log('remove expended client')\n      }\n      this._remove(client)\n      this._pulseQueue()\n      return\n    }\n\n    const isExpired = this._expired.has(client)\n    if (isExpired) {\n      this.log('remove expired client')\n      this._expired.delete(client)\n      this._remove(client)\n      this._pulseQueue()\n      return\n    }\n\n    // idle timeout\n    let tid\n    if (this.options.idleTimeoutMillis && this._isAboveMin()) {\n      tid = setTimeout(() => {\n        this.log('remove idle client')\n        this._remove(client)\n      }, this.options.idleTimeoutMillis)\n\n      if (this.options.allowExitOnIdle) {\n        // allow Node to exit if this is all that's left\n        tid.unref()\n      }\n    }\n\n    if (this.options.allowExitOnIdle) {\n      client.unref()\n    }\n\n    this._idle.push(new IdleItem(client, idleListener, tid))\n    this._pulseQueue()\n  }\n\n  query(text, values, cb) {\n    // guard clause against passing a function as the first parameter\n    if (typeof text === 'function') {\n      const response = promisify(this.Promise, text)\n      setImmediate(function () {\n        return response.callback(new Error('Passing a function as the first parameter to pool.query is not supported'))\n      })\n      return response.result\n    }\n\n    // allow plain text query without values\n    if (typeof values === 'function') {\n      cb = values\n      values = undefined\n    }\n    const response = promisify(this.Promise, cb)\n    cb = response.callback\n\n    this.connect((err, client) => {\n      if (err) {\n        return cb(err)\n      }\n\n      let clientReleased = false\n      const onError = (err) => {\n        if (clientReleased) {\n          return\n        }\n        clientReleased = true\n        client.release(err)\n        cb(err)\n      }\n\n      client.once('error', onError)\n      this.log('dispatching query')\n      try {\n        client.query(text, values, (err, res) => {\n          this.log('query dispatched')\n          client.removeListener('error', onError)\n          if (clientReleased) {\n            return\n          }\n          clientReleased = true\n          client.release(err)\n          if (err) {\n            return cb(err)\n          }\n          return cb(undefined, res)\n        })\n      } catch (err) {\n        client.release(err)\n        return cb(err)\n      }\n    })\n    return response.result\n  }\n\n  end(cb) {\n    this.log('ending')\n    if (this.ending) {\n      const err = new Error('Called end on pool more than once')\n      return cb ? cb(err) : this.Promise.reject(err)\n    }\n    this.ending = true\n    const promised = promisify(this.Promise, cb)\n    this._endCallback = promised.callback\n    this._pulseQueue()\n    return promised.result\n  }\n\n  get waitingCount() {\n    return this._pendingQueue.length\n  }\n\n  get idleCount() {\n    return this._idle.length\n  }\n\n  get expiredCount() {\n    return this._clients.reduce((acc, client) => acc + (this._expired.has(client) ? 1 : 0), 0)\n  }\n\n  get totalCount() {\n    return this._clients.length\n  }\n}\nmodule.exports = Pool\n"], "mappings": "AAAA,YAAY;;AACZ,MAAMA,YAAY,GAAGC,OAAO,CAAC,QAAQ,CAAC,CAACD,YAAY;AAEnD,MAAME,IAAI,GAAG,SAAAA,CAAA,EAAY,CAAC,CAAC;AAE3B,MAAMC,WAAW,GAAGA,CAACC,IAAI,EAAEC,SAAS,KAAK;EACvC,MAAMC,CAAC,GAAGF,IAAI,CAACG,SAAS,CAACF,SAAS,CAAC;EAEnC,OAAOC,CAAC,KAAK,CAAC,CAAC,GAAGE,SAAS,GAAGJ,IAAI,CAACK,MAAM,CAACH,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;AACpD,CAAC;AAED,MAAMI,QAAQ,CAAC;EACbC,WAAWA,CAACC,MAAM,EAAEC,YAAY,EAAEC,SAAS,EAAE;IAC3C,IAAI,CAACF,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACC,YAAY,GAAGA,YAAY;IAChC,IAAI,CAACC,SAAS,GAAGA,SAAS;EAC5B;AACF;AAEA,MAAMC,WAAW,CAAC;EAChBJ,WAAWA,CAACK,QAAQ,EAAE;IACpB,IAAI,CAACA,QAAQ,GAAGA,QAAQ;EAC1B;AACF;AAEA,SAASC,oBAAoBA,CAAA,EAAG;EAC9B,MAAM,IAAIC,KAAK,CAAC,uEAAuE,CAAC;AAC1F;AAEA,SAASC,SAASA,CAACC,OAAO,EAAEJ,QAAQ,EAAE;EACpC,IAAIA,QAAQ,EAAE;IACZ,OAAO;MAAEA,QAAQ,EAAEA,QAAQ;MAAEK,MAAM,EAAEb;IAAU,CAAC;EAClD;EACA,IAAIc,GAAG;EACP,IAAIC,GAAG;EACP,MAAMC,EAAE,GAAG,SAAAA,CAAUC,GAAG,EAAEb,MAAM,EAAE;IAChCa,GAAG,GAAGH,GAAG,CAACG,GAAG,CAAC,GAAGF,GAAG,CAACX,MAAM,CAAC;EAC9B,CAAC;EACD,MAAMS,MAAM,GAAG,IAAID,OAAO,CAAC,UAAUM,OAAO,EAAEC,MAAM,EAAE;IACpDJ,GAAG,GAAGG,OAAO;IACbJ,GAAG,GAAGK,MAAM;EACd,CAAC,CAAC,CAACC,KAAK,CAAEH,GAAG,IAAK;IAChB;IACA;IACAP,KAAK,CAACW,iBAAiB,CAACJ,GAAG,CAAC;IAC5B,MAAMA,GAAG;EACX,CAAC,CAAC;EACF,OAAO;IAAET,QAAQ,EAAEQ,EAAE;IAAEH,MAAM,EAAEA;EAAO,CAAC;AACzC;AAEA,SAASS,gBAAgBA,CAACC,IAAI,EAAEnB,MAAM,EAAE;EACtC,OAAO,SAASC,YAAYA,CAACY,GAAG,EAAE;IAChCA,GAAG,CAACb,MAAM,GAAGA,MAAM;IAEnBA,MAAM,CAACoB,cAAc,CAAC,OAAO,EAAEnB,YAAY,CAAC;IAC5CD,MAAM,CAACqB,EAAE,CAAC,OAAO,EAAE,MAAM;MACvBF,IAAI,CAACG,GAAG,CAAC,0DAA0D,EAAET,GAAG,CAAC;IAC3E,CAAC,CAAC;IACFM,IAAI,CAACI,OAAO,CAACvB,MAAM,CAAC;IACpB;IACA;IACAmB,IAAI,CAACK,IAAI,CAAC,OAAO,EAAEX,GAAG,EAAEb,MAAM,CAAC;EACjC,CAAC;AACH;AAEA,MAAMyB,IAAI,SAASrC,YAAY,CAAC;EAC9BW,WAAWA,CAAC2B,OAAO,EAAEC,MAAM,EAAE;IAC3B,KAAK,CAAC,CAAC;IACP,IAAI,CAACD,OAAO,GAAGE,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEH,OAAO,CAAC;IAEzC,IAAIA,OAAO,IAAI,IAAI,IAAI,UAAU,IAAIA,OAAO,EAAE;MAC5C;MACA;MACAE,MAAM,CAACE,cAAc,CAAC,IAAI,CAACJ,OAAO,EAAE,UAAU,EAAE;QAC9CK,YAAY,EAAE,IAAI;QAClBC,UAAU,EAAE,KAAK;QACjBC,QAAQ,EAAE,IAAI;QACdC,KAAK,EAAER,OAAO,CAACS;MACjB,CAAC,CAAC;IACJ;IACA,IAAIT,OAAO,IAAI,IAAI,IAAIA,OAAO,CAACU,GAAG,IAAIV,OAAO,CAACU,GAAG,CAACC,GAAG,EAAE;MACrD;MACA;MACAT,MAAM,CAACE,cAAc,CAAC,IAAI,CAACJ,OAAO,CAACU,GAAG,EAAE,KAAK,EAAE;QAC7CJ,UAAU,EAAE;MACd,CAAC,CAAC;IACJ;IAEA,IAAI,CAACN,OAAO,CAACY,GAAG,GAAG,IAAI,CAACZ,OAAO,CAACY,GAAG,IAAI,IAAI,CAACZ,OAAO,CAACa,QAAQ,IAAI,EAAE;IAClE,IAAI,CAACb,OAAO,CAACc,GAAG,GAAG,IAAI,CAACd,OAAO,CAACc,GAAG,IAAI,CAAC;IACxC,IAAI,CAACd,OAAO,CAACe,OAAO,GAAG,IAAI,CAACf,OAAO,CAACe,OAAO,IAAIC,QAAQ;IACvD,IAAI,CAAChB,OAAO,CAACiB,eAAe,GAAG,IAAI,CAACjB,OAAO,CAACiB,eAAe,IAAI,KAAK;IACpE,IAAI,CAACjB,OAAO,CAACkB,kBAAkB,GAAG,IAAI,CAAClB,OAAO,CAACkB,kBAAkB,IAAI,CAAC;IACtE,IAAI,CAACtB,GAAG,GAAG,IAAI,CAACI,OAAO,CAACJ,GAAG,IAAI,YAAY,CAAC,CAAC;IAC7C,IAAI,CAACK,MAAM,GAAG,IAAI,CAACD,OAAO,CAACC,MAAM,IAAIA,MAAM,IAAItC,OAAO,CAAC,IAAI,CAAC,CAACsC,MAAM;IACnE,IAAI,CAACnB,OAAO,GAAG,IAAI,CAACkB,OAAO,CAAClB,OAAO,IAAIqC,MAAM,CAACrC,OAAO;IAErD,IAAI,OAAO,IAAI,CAACkB,OAAO,CAACoB,iBAAiB,KAAK,WAAW,EAAE;MACzD,IAAI,CAACpB,OAAO,CAACoB,iBAAiB,GAAG,KAAK;IACxC;IAEA,IAAI,CAACC,QAAQ,GAAG,EAAE;IAClB,IAAI,CAACC,KAAK,GAAG,EAAE;IACf,IAAI,CAACC,QAAQ,GAAG,IAAIC,OAAO,CAAC,CAAC;IAC7B,IAAI,CAACC,aAAa,GAAG,EAAE;IACvB,IAAI,CAACC,YAAY,GAAGxD,SAAS;IAC7B,IAAI,CAACyD,MAAM,GAAG,KAAK;IACnB,IAAI,CAACC,KAAK,GAAG,KAAK;EACpB;EAEAC,OAAOA,CAAA,EAAG;IACR,OAAO,IAAI,CAACR,QAAQ,CAACS,MAAM,IAAI,IAAI,CAAC9B,OAAO,CAACY,GAAG;EACjD;EAEAmB,WAAWA,CAAA,EAAG;IACZ,OAAO,IAAI,CAACV,QAAQ,CAACS,MAAM,GAAG,IAAI,CAAC9B,OAAO,CAACc,GAAG;EAChD;EAEAkB,WAAWA,CAAA,EAAG;IACZ,IAAI,CAACpC,GAAG,CAAC,aAAa,CAAC;IACvB,IAAI,IAAI,CAACgC,KAAK,EAAE;MACd,IAAI,CAAChC,GAAG,CAAC,mBAAmB,CAAC;MAC7B;IACF;IACA,IAAI,IAAI,CAAC+B,MAAM,EAAE;MACf,IAAI,CAAC/B,GAAG,CAAC,uBAAuB,CAAC;MACjC,IAAI,IAAI,CAAC0B,KAAK,CAACQ,MAAM,EAAE;QACrB,IAAI,CAACR,KAAK,CAACW,KAAK,CAAC,CAAC,CAACC,GAAG,CAAEC,IAAI,IAAK;UAC/B,IAAI,CAACtC,OAAO,CAACsC,IAAI,CAAC7D,MAAM,CAAC;QAC3B,CAAC,CAAC;MACJ;MACA,IAAI,CAAC,IAAI,CAAC+C,QAAQ,CAACS,MAAM,EAAE;QACzB,IAAI,CAACF,KAAK,GAAG,IAAI;QACjB,IAAI,CAACF,YAAY,CAAC,CAAC;MACrB;MACA;IACF;;IAEA;IACA,IAAI,CAAC,IAAI,CAACD,aAAa,CAACK,MAAM,EAAE;MAC9B,IAAI,CAAClC,GAAG,CAAC,oBAAoB,CAAC;MAC9B;IACF;IACA;IACA,IAAI,CAAC,IAAI,CAAC0B,KAAK,CAACQ,MAAM,IAAI,IAAI,CAACD,OAAO,CAAC,CAAC,EAAE;MACxC;IACF;IACA,MAAMO,WAAW,GAAG,IAAI,CAACX,aAAa,CAACY,KAAK,CAAC,CAAC;IAC9C,IAAI,IAAI,CAACf,KAAK,CAACQ,MAAM,EAAE;MACrB,MAAMQ,QAAQ,GAAG,IAAI,CAAChB,KAAK,CAACiB,GAAG,CAAC,CAAC;MACjCC,YAAY,CAACF,QAAQ,CAAC9D,SAAS,CAAC;MAChC,MAAMF,MAAM,GAAGgE,QAAQ,CAAChE,MAAM;MAC9BA,MAAM,CAACmE,GAAG,IAAInE,MAAM,CAACmE,GAAG,CAAC,CAAC;MAC1B,MAAMlE,YAAY,GAAG+D,QAAQ,CAAC/D,YAAY;MAE1C,OAAO,IAAI,CAACmE,cAAc,CAACpE,MAAM,EAAE8D,WAAW,EAAE7D,YAAY,EAAE,KAAK,CAAC;IACtE;IACA,IAAI,CAAC,IAAI,CAACsD,OAAO,CAAC,CAAC,EAAE;MACnB,OAAO,IAAI,CAACc,SAAS,CAACP,WAAW,CAAC;IACpC;IACA,MAAM,IAAIxD,KAAK,CAAC,sBAAsB,CAAC;EACzC;EAEAiB,OAAOA,CAACvB,MAAM,EAAE;IACd,MAAMsE,OAAO,GAAG/E,WAAW,CAAC,IAAI,CAACyD,KAAK,EAAGa,IAAI,IAAKA,IAAI,CAAC7D,MAAM,KAAKA,MAAM,CAAC;IAEzE,IAAIsE,OAAO,KAAK1E,SAAS,EAAE;MACzBsE,YAAY,CAACI,OAAO,CAACpE,SAAS,CAAC;IACjC;IAEA,IAAI,CAAC6C,QAAQ,GAAG,IAAI,CAACA,QAAQ,CAACwB,MAAM,CAAEC,CAAC,IAAKA,CAAC,KAAKxE,MAAM,CAAC;IACzDA,MAAM,CAACyE,GAAG,CAAC,CAAC;IACZ,IAAI,CAACjD,IAAI,CAAC,QAAQ,EAAExB,MAAM,CAAC;EAC7B;EAEA0E,OAAOA,CAAC9D,EAAE,EAAE;IACV,IAAI,IAAI,CAACyC,MAAM,EAAE;MACf,MAAMxC,GAAG,GAAG,IAAIP,KAAK,CAAC,iDAAiD,CAAC;MACxE,OAAOM,EAAE,GAAGA,EAAE,CAACC,GAAG,CAAC,GAAG,IAAI,CAACL,OAAO,CAACO,MAAM,CAACF,GAAG,CAAC;IAChD;IAEA,MAAM8D,QAAQ,GAAGpE,SAAS,CAAC,IAAI,CAACC,OAAO,EAAEI,EAAE,CAAC;IAC5C,MAAMH,MAAM,GAAGkE,QAAQ,CAAClE,MAAM;;IAE9B;IACA,IAAI,IAAI,CAAC8C,OAAO,CAAC,CAAC,IAAI,IAAI,CAACP,KAAK,CAACQ,MAAM,EAAE;MACvC;MACA,IAAI,IAAI,CAACR,KAAK,CAACQ,MAAM,EAAE;QACrBoB,OAAO,CAACC,QAAQ,CAAC,MAAM,IAAI,CAACnB,WAAW,CAAC,CAAC,CAAC;MAC5C;MAEA,IAAI,CAAC,IAAI,CAAChC,OAAO,CAACoD,uBAAuB,EAAE;QACzC,IAAI,CAAC3B,aAAa,CAAC4B,IAAI,CAAC,IAAI5E,WAAW,CAACwE,QAAQ,CAACvE,QAAQ,CAAC,CAAC;QAC3D,OAAOK,MAAM;MACf;MAEA,MAAMuE,aAAa,GAAGA,CAACnE,GAAG,EAAEF,GAAG,EAAEsE,IAAI,KAAK;QACxCf,YAAY,CAACgB,GAAG,CAAC;QACjBP,QAAQ,CAACvE,QAAQ,CAACS,GAAG,EAAEF,GAAG,EAAEsE,IAAI,CAAC;MACnC,CAAC;MAED,MAAMnB,WAAW,GAAG,IAAI3D,WAAW,CAAC6E,aAAa,CAAC;;MAElD;MACA,MAAME,GAAG,GAAGC,UAAU,CAAC,MAAM;QAC3B;QACA;QACA5F,WAAW,CAAC,IAAI,CAAC4D,aAAa,EAAGzD,CAAC,IAAKA,CAAC,CAACU,QAAQ,KAAK4E,aAAa,CAAC;QACpElB,WAAW,CAACsB,QAAQ,GAAG,IAAI;QAC3BT,QAAQ,CAACvE,QAAQ,CAAC,IAAIE,KAAK,CAAC,yCAAyC,CAAC,CAAC;MACzE,CAAC,EAAE,IAAI,CAACoB,OAAO,CAACoD,uBAAuB,CAAC;MAExC,IAAII,GAAG,CAACG,KAAK,EAAE;QACbH,GAAG,CAACG,KAAK,CAAC,CAAC;MACb;MAEA,IAAI,CAAClC,aAAa,CAAC4B,IAAI,CAACjB,WAAW,CAAC;MACpC,OAAOrD,MAAM;IACf;IAEA,IAAI,CAAC4D,SAAS,CAAC,IAAIlE,WAAW,CAACwE,QAAQ,CAACvE,QAAQ,CAAC,CAAC;IAElD,OAAOK,MAAM;EACf;EAEA4D,SAASA,CAACP,WAAW,EAAE;IACrB,MAAM9D,MAAM,GAAG,IAAI,IAAI,CAAC2B,MAAM,CAAC,IAAI,CAACD,OAAO,CAAC;IAC5C,IAAI,CAACqB,QAAQ,CAACgC,IAAI,CAAC/E,MAAM,CAAC;IAC1B,MAAMC,YAAY,GAAGiB,gBAAgB,CAAC,IAAI,EAAElB,MAAM,CAAC;IAEnD,IAAI,CAACsB,GAAG,CAAC,yBAAyB,CAAC;;IAEnC;IACA,IAAI4D,GAAG;IACP,IAAII,UAAU,GAAG,KAAK;IACtB,IAAI,IAAI,CAAC5D,OAAO,CAACoD,uBAAuB,EAAE;MACxCI,GAAG,GAAGC,UAAU,CAAC,MAAM;QACrB,IAAI,CAAC7D,GAAG,CAAC,8BAA8B,CAAC;QACxCgE,UAAU,GAAG,IAAI;QACjB;QACAtF,MAAM,CAACuF,UAAU,GAAGvF,MAAM,CAACuF,UAAU,CAACC,MAAM,CAACC,OAAO,CAAC,CAAC,GAAGzF,MAAM,CAACyE,GAAG,CAAC,CAAC;MACvE,CAAC,EAAE,IAAI,CAAC/C,OAAO,CAACoD,uBAAuB,CAAC;IAC1C;IAEA,IAAI,CAACxD,GAAG,CAAC,uBAAuB,CAAC;IACjCtB,MAAM,CAAC0E,OAAO,CAAE7D,GAAG,IAAK;MACtB,IAAIqE,GAAG,EAAE;QACPhB,YAAY,CAACgB,GAAG,CAAC;MACnB;MACAlF,MAAM,CAACqB,EAAE,CAAC,OAAO,EAAEpB,YAAY,CAAC;MAChC,IAAIY,GAAG,EAAE;QACP,IAAI,CAACS,GAAG,CAAC,0BAA0B,EAAET,GAAG,CAAC;QACzC;QACA,IAAI,CAACkC,QAAQ,GAAG,IAAI,CAACA,QAAQ,CAACwB,MAAM,CAAEC,CAAC,IAAKA,CAAC,KAAKxE,MAAM,CAAC;QACzD,IAAIsF,UAAU,EAAE;UACdzE,GAAG,GAAG,IAAIP,KAAK,CAAC,iDAAiD,EAAE;YAAEoF,KAAK,EAAE7E;UAAI,CAAC,CAAC;QACpF;;QAEA;QACA,IAAI,CAAC6C,WAAW,CAAC,CAAC;QAElB,IAAI,CAACI,WAAW,CAACsB,QAAQ,EAAE;UACzBtB,WAAW,CAAC1D,QAAQ,CAACS,GAAG,EAAEjB,SAAS,EAAEN,IAAI,CAAC;QAC5C;MACF,CAAC,MAAM;QACL,IAAI,CAACgC,GAAG,CAAC,sBAAsB,CAAC;QAEhC,IAAI,IAAI,CAACI,OAAO,CAACkB,kBAAkB,KAAK,CAAC,EAAE;UACzC,MAAM+C,kBAAkB,GAAGR,UAAU,CAAC,MAAM;YAC1C,IAAI,CAAC7D,GAAG,CAAC,uCAAuC,CAAC;YACjD,IAAI,CAAC2B,QAAQ,CAAC2C,GAAG,CAAC5F,MAAM,CAAC;YACzB,MAAM6F,SAAS,GAAG,IAAI,CAAC7C,KAAK,CAACrD,SAAS,CAAEqE,QAAQ,IAAKA,QAAQ,CAAChE,MAAM,KAAKA,MAAM,CAAC;YAChF,IAAI6F,SAAS,KAAK,CAAC,CAAC,EAAE;cACpB,IAAI,CAACzB,cAAc,CACjBpE,MAAM,EACN,IAAIG,WAAW,CAAC,CAACU,GAAG,EAAEb,MAAM,EAAE8F,aAAa,KAAKA,aAAa,CAAC,CAAC,CAAC,EAChE7F,YAAY,EACZ,KACF,CAAC;YACH;UACF,CAAC,EAAE,IAAI,CAACyB,OAAO,CAACkB,kBAAkB,GAAG,IAAI,CAAC;UAE1C+C,kBAAkB,CAACN,KAAK,CAAC,CAAC;UAC1BrF,MAAM,CAAC+F,IAAI,CAAC,KAAK,EAAE,MAAM7B,YAAY,CAACyB,kBAAkB,CAAC,CAAC;QAC5D;QAEA,OAAO,IAAI,CAACvB,cAAc,CAACpE,MAAM,EAAE8D,WAAW,EAAE7D,YAAY,EAAE,IAAI,CAAC;MACrE;IACF,CAAC,CAAC;EACJ;;EAEA;EACAmE,cAAcA,CAACpE,MAAM,EAAE8D,WAAW,EAAE7D,YAAY,EAAE+F,KAAK,EAAE;IACvD,IAAIA,KAAK,EAAE;MACT,IAAI,CAACxE,IAAI,CAAC,SAAS,EAAExB,MAAM,CAAC;IAC9B;IAEA,IAAI,CAACwB,IAAI,CAAC,SAAS,EAAExB,MAAM,CAAC;IAE5BA,MAAM,CAACiG,OAAO,GAAG,IAAI,CAACC,YAAY,CAAClG,MAAM,EAAEC,YAAY,CAAC;IAExDD,MAAM,CAACoB,cAAc,CAAC,OAAO,EAAEnB,YAAY,CAAC;IAE5C,IAAI,CAAC6D,WAAW,CAACsB,QAAQ,EAAE;MACzB,IAAIY,KAAK,IAAI,IAAI,CAACtE,OAAO,CAACyE,MAAM,EAAE;QAChC,IAAI,CAACzE,OAAO,CAACyE,MAAM,CAACnG,MAAM,EAAGa,GAAG,IAAK;UACnC,IAAIA,GAAG,EAAE;YACPb,MAAM,CAACiG,OAAO,CAACpF,GAAG,CAAC;YACnB,OAAOiD,WAAW,CAAC1D,QAAQ,CAACS,GAAG,EAAEjB,SAAS,EAAEN,IAAI,CAAC;UACnD;UAEAwE,WAAW,CAAC1D,QAAQ,CAACR,SAAS,EAAEI,MAAM,EAAEA,MAAM,CAACiG,OAAO,CAAC;QACzD,CAAC,CAAC;MACJ,CAAC,MAAM;QACLnC,WAAW,CAAC1D,QAAQ,CAACR,SAAS,EAAEI,MAAM,EAAEA,MAAM,CAACiG,OAAO,CAAC;MACzD;IACF,CAAC,MAAM;MACL,IAAID,KAAK,IAAI,IAAI,CAACtE,OAAO,CAACyE,MAAM,EAAE;QAChC,IAAI,CAACzE,OAAO,CAACyE,MAAM,CAACnG,MAAM,EAAEA,MAAM,CAACiG,OAAO,CAAC;MAC7C,CAAC,MAAM;QACLjG,MAAM,CAACiG,OAAO,CAAC,CAAC;MAClB;IACF;EACF;;EAEA;EACAC,YAAYA,CAAClG,MAAM,EAAEC,YAAY,EAAE;IACjC,IAAImG,QAAQ,GAAG,KAAK;IAEpB,OAAQvF,GAAG,IAAK;MACd,IAAIuF,QAAQ,EAAE;QACZ/F,oBAAoB,CAAC,CAAC;MACxB;MAEA+F,QAAQ,GAAG,IAAI;MACf,IAAI,CAACC,QAAQ,CAACrG,MAAM,EAAEC,YAAY,EAAEY,GAAG,CAAC;IAC1C,CAAC;EACH;;EAEA;EACA;EACAwF,QAAQA,CAACrG,MAAM,EAAEC,YAAY,EAAEY,GAAG,EAAE;IAClCb,MAAM,CAACqB,EAAE,CAAC,OAAO,EAAEpB,YAAY,CAAC;IAEhCD,MAAM,CAACsG,aAAa,GAAG,CAACtG,MAAM,CAACsG,aAAa,IAAI,CAAC,IAAI,CAAC;IAEtD,IAAI,CAAC9E,IAAI,CAAC,SAAS,EAAEX,GAAG,EAAEb,MAAM,CAAC;;IAEjC;IACA,IAAIa,GAAG,IAAI,IAAI,CAACwC,MAAM,IAAI,CAACrD,MAAM,CAACuG,UAAU,IAAIvG,MAAM,CAACwG,OAAO,IAAIxG,MAAM,CAACsG,aAAa,IAAI,IAAI,CAAC5E,OAAO,CAACe,OAAO,EAAE;MAC9G,IAAIzC,MAAM,CAACsG,aAAa,IAAI,IAAI,CAAC5E,OAAO,CAACe,OAAO,EAAE;QAChD,IAAI,CAACnB,GAAG,CAAC,wBAAwB,CAAC;MACpC;MACA,IAAI,CAACC,OAAO,CAACvB,MAAM,CAAC;MACpB,IAAI,CAAC0D,WAAW,CAAC,CAAC;MAClB;IACF;IAEA,MAAM+C,SAAS,GAAG,IAAI,CAACxD,QAAQ,CAACyD,GAAG,CAAC1G,MAAM,CAAC;IAC3C,IAAIyG,SAAS,EAAE;MACb,IAAI,CAACnF,GAAG,CAAC,uBAAuB,CAAC;MACjC,IAAI,CAAC2B,QAAQ,CAAC0D,MAAM,CAAC3G,MAAM,CAAC;MAC5B,IAAI,CAACuB,OAAO,CAACvB,MAAM,CAAC;MACpB,IAAI,CAAC0D,WAAW,CAAC,CAAC;MAClB;IACF;;IAEA;IACA,IAAIwB,GAAG;IACP,IAAI,IAAI,CAACxD,OAAO,CAACoB,iBAAiB,IAAI,IAAI,CAACW,WAAW,CAAC,CAAC,EAAE;MACxDyB,GAAG,GAAGC,UAAU,CAAC,MAAM;QACrB,IAAI,CAAC7D,GAAG,CAAC,oBAAoB,CAAC;QAC9B,IAAI,CAACC,OAAO,CAACvB,MAAM,CAAC;MACtB,CAAC,EAAE,IAAI,CAAC0B,OAAO,CAACoB,iBAAiB,CAAC;MAElC,IAAI,IAAI,CAACpB,OAAO,CAACiB,eAAe,EAAE;QAChC;QACAuC,GAAG,CAACG,KAAK,CAAC,CAAC;MACb;IACF;IAEA,IAAI,IAAI,CAAC3D,OAAO,CAACiB,eAAe,EAAE;MAChC3C,MAAM,CAACqF,KAAK,CAAC,CAAC;IAChB;IAEA,IAAI,CAACrC,KAAK,CAAC+B,IAAI,CAAC,IAAIjF,QAAQ,CAACE,MAAM,EAAEC,YAAY,EAAEiF,GAAG,CAAC,CAAC;IACxD,IAAI,CAACxB,WAAW,CAAC,CAAC;EACpB;EAEAkD,KAAKA,CAACC,IAAI,EAAEC,MAAM,EAAElG,EAAE,EAAE;IACtB;IACA,IAAI,OAAOiG,IAAI,KAAK,UAAU,EAAE;MAC9B,MAAMlC,QAAQ,GAAGpE,SAAS,CAAC,IAAI,CAACC,OAAO,EAAEqG,IAAI,CAAC;MAC9CE,YAAY,CAAC,YAAY;QACvB,OAAOpC,QAAQ,CAACvE,QAAQ,CAAC,IAAIE,KAAK,CAAC,0EAA0E,CAAC,CAAC;MACjH,CAAC,CAAC;MACF,OAAOqE,QAAQ,CAAClE,MAAM;IACxB;;IAEA;IACA,IAAI,OAAOqG,MAAM,KAAK,UAAU,EAAE;MAChClG,EAAE,GAAGkG,MAAM;MACXA,MAAM,GAAGlH,SAAS;IACpB;IACA,MAAM+E,QAAQ,GAAGpE,SAAS,CAAC,IAAI,CAACC,OAAO,EAAEI,EAAE,CAAC;IAC5CA,EAAE,GAAG+D,QAAQ,CAACvE,QAAQ;IAEtB,IAAI,CAACsE,OAAO,CAAC,CAAC7D,GAAG,EAAEb,MAAM,KAAK;MAC5B,IAAIa,GAAG,EAAE;QACP,OAAOD,EAAE,CAACC,GAAG,CAAC;MAChB;MAEA,IAAImG,cAAc,GAAG,KAAK;MAC1B,MAAMC,OAAO,GAAIpG,GAAG,IAAK;QACvB,IAAImG,cAAc,EAAE;UAClB;QACF;QACAA,cAAc,GAAG,IAAI;QACrBhH,MAAM,CAACiG,OAAO,CAACpF,GAAG,CAAC;QACnBD,EAAE,CAACC,GAAG,CAAC;MACT,CAAC;MAEDb,MAAM,CAAC+F,IAAI,CAAC,OAAO,EAAEkB,OAAO,CAAC;MAC7B,IAAI,CAAC3F,GAAG,CAAC,mBAAmB,CAAC;MAC7B,IAAI;QACFtB,MAAM,CAAC4G,KAAK,CAACC,IAAI,EAAEC,MAAM,EAAE,CAACjG,GAAG,EAAEF,GAAG,KAAK;UACvC,IAAI,CAACW,GAAG,CAAC,kBAAkB,CAAC;UAC5BtB,MAAM,CAACoB,cAAc,CAAC,OAAO,EAAE6F,OAAO,CAAC;UACvC,IAAID,cAAc,EAAE;YAClB;UACF;UACAA,cAAc,GAAG,IAAI;UACrBhH,MAAM,CAACiG,OAAO,CAACpF,GAAG,CAAC;UACnB,IAAIA,GAAG,EAAE;YACP,OAAOD,EAAE,CAACC,GAAG,CAAC;UAChB;UACA,OAAOD,EAAE,CAAChB,SAAS,EAAEe,GAAG,CAAC;QAC3B,CAAC,CAAC;MACJ,CAAC,CAAC,OAAOE,GAAG,EAAE;QACZb,MAAM,CAACiG,OAAO,CAACpF,GAAG,CAAC;QACnB,OAAOD,EAAE,CAACC,GAAG,CAAC;MAChB;IACF,CAAC,CAAC;IACF,OAAO8D,QAAQ,CAAClE,MAAM;EACxB;EAEAgE,GAAGA,CAAC7D,EAAE,EAAE;IACN,IAAI,CAACU,GAAG,CAAC,QAAQ,CAAC;IAClB,IAAI,IAAI,CAAC+B,MAAM,EAAE;MACf,MAAMxC,GAAG,GAAG,IAAIP,KAAK,CAAC,mCAAmC,CAAC;MAC1D,OAAOM,EAAE,GAAGA,EAAE,CAACC,GAAG,CAAC,GAAG,IAAI,CAACL,OAAO,CAACO,MAAM,CAACF,GAAG,CAAC;IAChD;IACA,IAAI,CAACwC,MAAM,GAAG,IAAI;IAClB,MAAM6D,QAAQ,GAAG3G,SAAS,CAAC,IAAI,CAACC,OAAO,EAAEI,EAAE,CAAC;IAC5C,IAAI,CAACwC,YAAY,GAAG8D,QAAQ,CAAC9G,QAAQ;IACrC,IAAI,CAACsD,WAAW,CAAC,CAAC;IAClB,OAAOwD,QAAQ,CAACzG,MAAM;EACxB;EAEA,IAAI0G,YAAYA,CAAA,EAAG;IACjB,OAAO,IAAI,CAAChE,aAAa,CAACK,MAAM;EAClC;EAEA,IAAI4D,SAASA,CAAA,EAAG;IACd,OAAO,IAAI,CAACpE,KAAK,CAACQ,MAAM;EAC1B;EAEA,IAAI6D,YAAYA,CAAA,EAAG;IACjB,OAAO,IAAI,CAACtE,QAAQ,CAACuE,MAAM,CAAC,CAACC,GAAG,EAAEvH,MAAM,KAAKuH,GAAG,IAAI,IAAI,CAACtE,QAAQ,CAACyD,GAAG,CAAC1G,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;EAC5F;EAEA,IAAIwH,UAAUA,CAAA,EAAG;IACf,OAAO,IAAI,CAACzE,QAAQ,CAACS,MAAM;EAC7B;AACF;AACAiE,MAAM,CAACC,OAAO,GAAGjG,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}