{"ast": null, "code": "import isArrayLike from './_isArrayLike.js';\nimport values from './values.js';\nimport cb from './_cb.js';\nimport each from './each.js';\n\n// Return the maximum element (or element-based computation).\nexport default function max(obj, iteratee, context) {\n  var result = -Infinity,\n    lastComputed = -Infinity,\n    value,\n    computed;\n  if (iteratee == null || typeof iteratee == 'number' && typeof obj[0] != 'object' && obj != null) {\n    obj = isArrayLike(obj) ? obj : values(obj);\n    for (var i = 0, length = obj.length; i < length; i++) {\n      value = obj[i];\n      if (value != null && value > result) {\n        result = value;\n      }\n    }\n  } else {\n    iteratee = cb(iteratee, context);\n    each(obj, function (v, index, list) {\n      computed = iteratee(v, index, list);\n      if (computed > lastComputed || computed === -Infinity && result === -Infinity) {\n        result = v;\n        lastComputed = computed;\n      }\n    });\n  }\n  return result;\n}", "map": {"version": 3, "names": ["isArrayLike", "values", "cb", "each", "max", "obj", "iteratee", "context", "result", "Infinity", "lastComputed", "value", "computed", "i", "length", "v", "index", "list"], "sources": ["C:/tmsft/node_modules/underscore/modules/max.js"], "sourcesContent": ["import isArrayLike from './_isArrayLike.js';\nimport values from './values.js';\nimport cb from './_cb.js';\nimport each from './each.js';\n\n// Return the maximum element (or element-based computation).\nexport default function max(obj, iteratee, context) {\n  var result = -Infinity, lastComputed = -Infinity,\n      value, computed;\n  if (iteratee == null || typeof iteratee == 'number' && typeof obj[0] != 'object' && obj != null) {\n    obj = isArrayLike(obj) ? obj : values(obj);\n    for (var i = 0, length = obj.length; i < length; i++) {\n      value = obj[i];\n      if (value != null && value > result) {\n        result = value;\n      }\n    }\n  } else {\n    iteratee = cb(iteratee, context);\n    each(obj, function(v, index, list) {\n      computed = iteratee(v, index, list);\n      if (computed > lastComputed || computed === -Infinity && result === -Infinity) {\n        result = v;\n        lastComputed = computed;\n      }\n    });\n  }\n  return result;\n}\n"], "mappings": "AAAA,OAAOA,WAAW,MAAM,mBAAmB;AAC3C,OAAOC,MAAM,MAAM,aAAa;AAChC,OAAOC,EAAE,MAAM,UAAU;AACzB,OAAOC,IAAI,MAAM,WAAW;;AAE5B;AACA,eAAe,SAASC,GAAGA,CAACC,GAAG,EAAEC,QAAQ,EAAEC,OAAO,EAAE;EAClD,IAAIC,MAAM,GAAG,CAACC,QAAQ;IAAEC,YAAY,GAAG,CAACD,QAAQ;IAC5CE,KAAK;IAAEC,QAAQ;EACnB,IAAIN,QAAQ,IAAI,IAAI,IAAI,OAAOA,QAAQ,IAAI,QAAQ,IAAI,OAAOD,GAAG,CAAC,CAAC,CAAC,IAAI,QAAQ,IAAIA,GAAG,IAAI,IAAI,EAAE;IAC/FA,GAAG,GAAGL,WAAW,CAACK,GAAG,CAAC,GAAGA,GAAG,GAAGJ,MAAM,CAACI,GAAG,CAAC;IAC1C,KAAK,IAAIQ,CAAC,GAAG,CAAC,EAAEC,MAAM,GAAGT,GAAG,CAACS,MAAM,EAAED,CAAC,GAAGC,MAAM,EAAED,CAAC,EAAE,EAAE;MACpDF,KAAK,GAAGN,GAAG,CAACQ,CAAC,CAAC;MACd,IAAIF,KAAK,IAAI,IAAI,IAAIA,KAAK,GAAGH,MAAM,EAAE;QACnCA,MAAM,GAAGG,KAAK;MAChB;IACF;EACF,CAAC,MAAM;IACLL,QAAQ,GAAGJ,EAAE,CAACI,QAAQ,EAAEC,OAAO,CAAC;IAChCJ,IAAI,CAACE,GAAG,EAAE,UAASU,CAAC,EAAEC,KAAK,EAAEC,IAAI,EAAE;MACjCL,QAAQ,GAAGN,QAAQ,CAACS,CAAC,EAAEC,KAAK,EAAEC,IAAI,CAAC;MACnC,IAAIL,QAAQ,GAAGF,YAAY,IAAIE,QAAQ,KAAK,CAACH,QAAQ,IAAID,MAAM,KAAK,CAACC,QAAQ,EAAE;QAC7ED,MAAM,GAAGO,CAAC;QACVL,YAAY,GAAGE,QAAQ;MACzB;IACF,CAAC,CAAC;EACJ;EACA,OAAOJ,MAAM;AACf", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}