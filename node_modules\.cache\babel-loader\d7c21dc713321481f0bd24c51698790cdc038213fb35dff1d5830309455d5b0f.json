{"ast": null, "code": "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { ExpandDims, util } from '@tensorflow/tfjs-core';\nimport { reshape } from './Reshape';\nexport function expandDims(args) {\n  const {\n    inputs,\n    backend,\n    attrs\n  } = args;\n  const {\n    input\n  } = inputs;\n  const {\n    dim\n  } = attrs;\n  const inputRank = input.shape.length;\n  const newShape = input.shape.slice();\n  let $dim = dim;\n  if (dim < 0) {\n    // Negative value is counted from the tail of rank.\n    util.assert(-(inputRank + 1) <= dim, () => \"Axis must be in the interval [\".concat(-(inputRank + 1), \", \").concat(inputRank, \"]\"));\n    $dim = inputRank + dim + 1;\n  }\n  newShape.splice($dim, 0, 1);\n  return reshape({\n    inputs: {\n      x: input\n    },\n    backend,\n    attrs: {\n      shape: newShape\n    }\n  });\n}\nexport const expandDimsConfig = {\n  kernelName: ExpandDims,\n  backendName: 'cpu',\n  kernelFunc: expandDims\n};", "map": {"version": 3, "names": ["ExpandDims", "util", "reshape", "expandDims", "args", "inputs", "backend", "attrs", "input", "dim", "inputRank", "shape", "length", "newShape", "slice", "$dim", "assert", "concat", "splice", "x", "expandDimsConfig", "kernelName", "backendName", "kernelFunc"], "sources": ["C:\\tfjs-backend-cpu\\src\\kernels\\ExpandDims.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {ExpandDims, ExpandDimsAttrs, ExpandDimsInputs, KernelConfig, KernelFunc, TensorInfo, util} from '@tensorflow/tfjs-core';\n\nimport {MathBackendCPU} from '../backend_cpu';\nimport {reshape} from './Reshape';\n\nexport function expandDims(args: {\n  inputs: ExpandDimsInputs,\n  backend: MathBackendCPU,\n  attrs: ExpandDimsAttrs\n}): TensorInfo {\n  const {inputs, backend, attrs} = args;\n  const {input} = inputs;\n  const {dim} = attrs;\n\n  const inputRank = input.shape.length;\n  const newShape = input.shape.slice();\n  let $dim = dim;\n  if (dim < 0) {\n    // Negative value is counted from the tail of rank.\n    util.assert(\n        -(inputRank + 1) <= dim,\n        () => `Axis must be in the interval [${- (inputRank + 1)}, ${\n            inputRank}]`);\n    $dim = inputRank + dim + 1;\n  }\n  newShape.splice($dim, 0, 1);\n\n  return reshape({inputs: {x: input}, backend, attrs: {shape: newShape}});\n}\n\nexport const expandDimsConfig: KernelConfig = {\n  kernelName: ExpandDims,\n  backendName: 'cpu',\n  kernelFunc: expandDims as unknown as KernelFunc\n};\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAiBA,SAAQA,UAAU,EAA2EC,IAAI,QAAO,uBAAuB;AAG/H,SAAQC,OAAO,QAAO,WAAW;AAEjC,OAAM,SAAUC,UAAUA,CAACC,IAI1B;EACC,MAAM;IAACC,MAAM;IAAEC,OAAO;IAAEC;EAAK,CAAC,GAAGH,IAAI;EACrC,MAAM;IAACI;EAAK,CAAC,GAAGH,MAAM;EACtB,MAAM;IAACI;EAAG,CAAC,GAAGF,KAAK;EAEnB,MAAMG,SAAS,GAAGF,KAAK,CAACG,KAAK,CAACC,MAAM;EACpC,MAAMC,QAAQ,GAAGL,KAAK,CAACG,KAAK,CAACG,KAAK,EAAE;EACpC,IAAIC,IAAI,GAAGN,GAAG;EACd,IAAIA,GAAG,GAAG,CAAC,EAAE;IACX;IACAR,IAAI,CAACe,MAAM,CACP,EAAEN,SAAS,GAAG,CAAC,CAAC,IAAID,GAAG,EACvB,uCAAAQ,MAAA,CAAuC,EAAGP,SAAS,GAAG,CAAC,CAAC,QAAAO,MAAA,CACpDP,SAAS,MAAG,CAAC;IACrBK,IAAI,GAAGL,SAAS,GAAGD,GAAG,GAAG,CAAC;;EAE5BI,QAAQ,CAACK,MAAM,CAACH,IAAI,EAAE,CAAC,EAAE,CAAC,CAAC;EAE3B,OAAOb,OAAO,CAAC;IAACG,MAAM,EAAE;MAACc,CAAC,EAAEX;IAAK,CAAC;IAAEF,OAAO;IAAEC,KAAK,EAAE;MAACI,KAAK,EAAEE;IAAQ;EAAC,CAAC,CAAC;AACzE;AAEA,OAAO,MAAMO,gBAAgB,GAAiB;EAC5CC,UAAU,EAAErB,UAAU;EACtBsB,WAAW,EAAE,KAAK;EAClBC,UAAU,EAAEpB;CACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}