{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.transformArguments = exports.FIRST_KEY_INDEX = void 0;\nconst _1 = require(\".\");\nexports.FIRST_KEY_INDEX = 1;\nfunction transformArguments(key, value, options) {\n  return (0, _1.transformIncrDecrArguments)('TS.DECRBY', key, value, options);\n}\nexports.transformArguments = transformArguments;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "transformArguments", "FIRST_KEY_INDEX", "_1", "require", "key", "options", "transformIncrDecrArguments"], "sources": ["C:/tmsft/node_modules/@redis/time-series/dist/commands/DECRBY.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.transformArguments = exports.FIRST_KEY_INDEX = void 0;\nconst _1 = require(\".\");\nexports.FIRST_KEY_INDEX = 1;\nfunction transformArguments(key, value, options) {\n    return (0, _1.transformIncrDecrArguments)('TS.DECRBY', key, value, options);\n}\nexports.transformArguments = transformArguments;\n"], "mappings": "AAAA,YAAY;;AACZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEC,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7DD,OAAO,CAACE,kBAAkB,GAAGF,OAAO,CAACG,eAAe,GAAG,KAAK,CAAC;AAC7D,MAAMC,EAAE,GAAGC,OAAO,CAAC,GAAG,CAAC;AACvBL,OAAO,CAACG,eAAe,GAAG,CAAC;AAC3B,SAASD,kBAAkBA,CAACI,GAAG,EAAEL,KAAK,EAAEM,OAAO,EAAE;EAC7C,OAAO,CAAC,CAAC,EAAEH,EAAE,CAACI,0BAA0B,EAAE,WAAW,EAAEF,GAAG,EAAEL,KAAK,EAAEM,OAAO,CAAC;AAC/E;AACAP,OAAO,CAACE,kBAAkB,GAAGA,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}