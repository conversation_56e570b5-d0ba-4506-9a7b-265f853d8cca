{"ast": null, "code": "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { depthToSpace } from '../../ops/depth_to_space';\nimport { getGlobalTensorClass } from '../../tensor';\ngetGlobalTensorClass().prototype.depthToSpace = function (blockSize, dataFormat) {\n  this.throwIfDisposed();\n  return depthToSpace(this, blockSize, dataFormat);\n};", "map": {"version": 3, "names": ["depthToSpace", "getGlobalTensorClass", "prototype", "blockSize", "dataFormat", "throwIfDisposed"], "sources": ["C:\\tfjs-core\\src\\public\\chained_ops\\depth_to_space.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport {depthToSpace} from '../../ops/depth_to_space';\nimport {getGlobalTensorClass, Tensor4D} from '../../tensor';\nimport {Rank} from '../../types';\n\ndeclare module '../../tensor' {\n  interface Tensor<R extends Rank = Rank> {\n    depthToSpace<T extends Tensor4D>(\n        blockSize: number, dataFormat: 'NHWC'|'NCHW'): T;\n  }\n}\n\ngetGlobalTensorClass().prototype.depthToSpace = function<T extends Tensor4D>(\n    blockSize: number, dataFormat: 'NHWC'|'NCHW'): T {\n  this.throwIfDisposed();\n  return depthToSpace(this, blockSize, dataFormat) as T;\n};\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAgBA,SAAQA,YAAY,QAAO,0BAA0B;AACrD,SAAQC,oBAAoB,QAAiB,cAAc;AAU3DA,oBAAoB,EAAE,CAACC,SAAS,CAACF,YAAY,GAAG,UAC5CG,SAAiB,EAAEC,UAAyB;EAC9C,IAAI,CAACC,eAAe,EAAE;EACtB,OAAOL,YAAY,CAAC,IAAI,EAAEG,SAAS,EAAEC,UAAU,CAAM;AACvD,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}