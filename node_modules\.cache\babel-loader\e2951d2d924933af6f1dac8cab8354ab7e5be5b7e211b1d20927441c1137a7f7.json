{"ast": null, "code": "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { ENGINE } from '../engine';\nimport { DepthToSpace } from '../kernel_names';\nimport { convertToTensor } from '../tensor_util_env';\nimport * as util from '../util';\nimport { op } from './operation';\n/**\n * Rearranges data from depth into blocks of spatial data. More specifically,\n * this op outputs a copy of the input tensor where values from the `depth`\n * dimension are moved in spatial blocks to the `height` and `width` dimensions.\n * The attr `blockSize` indicates the input block size and how the data is\n * moved.\n *\n *  - Chunks of data of size `blockSize * blockSize` from depth are rearranged\n * into non-overlapping blocks of size `blockSize x blockSize`\n *\n *  - The width the output tensor is `inputWidth * blockSize`, whereas the\n * height is `inputHeight * blockSize`\n *\n *  - The Y, X coordinates within each block of the output image are determined\n * by the high order component of the input channel index\n *\n *  - The depth of the input tensor must be divisible by `blockSize *\n * blockSize`\n *\n * The `dataFormat` attr specifies the layout of the input and output tensors\n * with the following options: \"NHWC\": [ `batch, height, width, channels` ]\n * \"NCHW\": [ `batch, channels, height, width` ]\n *\n * ```js\n * const x = tf.tensor4d([1, 2, 3, 4], [1, 1, 1, 4]);\n * const blockSize = 2;\n * const dataFormat = \"NHWC\";\n *\n * tf.depthToSpace(x, blockSize, dataFormat).print();\n * ```\n *\n * @param x The input tensor of rank 4\n * @param blockSIze  An `int` that is `>= 2`. The size of the spatial block\n * @param dataFormat An optional string from: \"NHWC\", \"NCHW\". Defaults to \"NHWC\"\n *\n * @doc {heading: 'Tensors', subheading: 'Transformations'}\n */\nfunction depthToSpace_(x, blockSize) {\n  let dataFormat = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 'NHWC';\n  const $x = convertToTensor(x, 'x', 'depthToSpace', 'float32');\n  const inputHeight = dataFormat === 'NHWC' ? $x.shape[1] : $x.shape[2];\n  const inputWidth = dataFormat === 'NHWC' ? $x.shape[2] : $x.shape[3];\n  const inputDepth = dataFormat === 'NHWC' ? $x.shape[3] : $x.shape[1];\n  util.assert(blockSize > 1, () => \"blockSize should be > 1 for depthToSpace, but was: \".concat(blockSize));\n  util.assert(inputHeight * blockSize >= 0, () => \"Negative dimension size caused by overflow when multiplying\\n    \".concat(inputHeight, \" and \").concat(blockSize, \"  for depthToSpace with input shape\\n    \").concat($x.shape));\n  util.assert(inputWidth * blockSize >= 0, () => \"Negative dimension size caused by overflow when multiplying\\n    \".concat(inputWidth, \" and \").concat(blockSize, \" for depthToSpace with input shape\\n        \").concat($x.shape));\n  util.assert(inputDepth % (blockSize * blockSize) === 0, () => \"Dimension size must be evenly divisible by \".concat(blockSize * blockSize, \" but is \").concat(inputDepth, \" for depthToSpace with input shape \").concat($x.shape));\n  const inputs = {\n    x: $x\n  };\n  const attrs = {\n    blockSize,\n    dataFormat\n  };\n  return ENGINE.runKernel(DepthToSpace, inputs, attrs);\n}\nexport const depthToSpace = /* @__PURE__ */op({\n  depthToSpace_\n});", "map": {"version": 3, "names": ["ENGINE", "DepthToSpace", "convertToTensor", "util", "op", "depthToSpace_", "x", "blockSize", "dataFormat", "arguments", "length", "undefined", "$x", "inputHeight", "shape", "inputWidth", "inputDepth", "assert", "concat", "inputs", "attrs", "runKernel", "depthToSpace"], "sources": ["C:\\tfjs-core\\src\\ops\\depth_to_space.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {ENGINE} from '../engine';\nimport {DepthToSpace, DepthToSpaceAttrs, DepthToSpaceInputs} from '../kernel_names';\nimport {NamedAttrMap} from '../kernel_registry';\nimport {Tensor4D} from '../tensor';\nimport {NamedTensorMap} from '../tensor_types';\nimport {convertToTensor} from '../tensor_util_env';\nimport {TensorLike4D} from '../types';\nimport * as util from '../util';\n\nimport {op} from './operation';\n\n/**\n * Rearranges data from depth into blocks of spatial data. More specifically,\n * this op outputs a copy of the input tensor where values from the `depth`\n * dimension are moved in spatial blocks to the `height` and `width` dimensions.\n * The attr `blockSize` indicates the input block size and how the data is\n * moved.\n *\n *  - Chunks of data of size `blockSize * blockSize` from depth are rearranged\n * into non-overlapping blocks of size `blockSize x blockSize`\n *\n *  - The width the output tensor is `inputWidth * blockSize`, whereas the\n * height is `inputHeight * blockSize`\n *\n *  - The Y, X coordinates within each block of the output image are determined\n * by the high order component of the input channel index\n *\n *  - The depth of the input tensor must be divisible by `blockSize *\n * blockSize`\n *\n * The `dataFormat` attr specifies the layout of the input and output tensors\n * with the following options: \"NHWC\": [ `batch, height, width, channels` ]\n * \"NCHW\": [ `batch, channels, height, width` ]\n *\n * ```js\n * const x = tf.tensor4d([1, 2, 3, 4], [1, 1, 1, 4]);\n * const blockSize = 2;\n * const dataFormat = \"NHWC\";\n *\n * tf.depthToSpace(x, blockSize, dataFormat).print();\n * ```\n *\n * @param x The input tensor of rank 4\n * @param blockSIze  An `int` that is `>= 2`. The size of the spatial block\n * @param dataFormat An optional string from: \"NHWC\", \"NCHW\". Defaults to \"NHWC\"\n *\n * @doc {heading: 'Tensors', subheading: 'Transformations'}\n */\nfunction depthToSpace_(\n    x: Tensor4D|TensorLike4D, blockSize: number,\n    dataFormat: 'NHWC'|'NCHW' = 'NHWC'): Tensor4D {\n  const $x = convertToTensor(x, 'x', 'depthToSpace', 'float32') as Tensor4D;\n\n  const inputHeight = (dataFormat === 'NHWC') ? $x.shape[1] : $x.shape[2];\n  const inputWidth = (dataFormat === 'NHWC') ? $x.shape[2] : $x.shape[3];\n  const inputDepth = (dataFormat === 'NHWC') ? $x.shape[3] : $x.shape[1];\n\n  util.assert(\n      blockSize > 1,\n      () => `blockSize should be > 1 for depthToSpace, but was: ${blockSize}`);\n\n  util.assert(\n      inputHeight * blockSize >= 0,\n      () => `Negative dimension size caused by overflow when multiplying\n    ${inputHeight} and ${blockSize}  for depthToSpace with input shape\n    ${$x.shape}`);\n\n  util.assert(\n      inputWidth * blockSize >= 0,\n      () => `Negative dimension size caused by overflow when multiplying\n    ${inputWidth} and ${blockSize} for depthToSpace with input shape\n        ${$x.shape}`);\n\n  util.assert(\n      (inputDepth % (blockSize * blockSize) === 0),\n      () => `Dimension size must be evenly divisible by ${\n          blockSize * blockSize} but is ${\n          inputDepth} for depthToSpace with input shape ${$x.shape}`);\n\n  const inputs: DepthToSpaceInputs = {x: $x};\n  const attrs: DepthToSpaceAttrs = {blockSize, dataFormat};\n\n  return ENGINE.runKernel(\n      DepthToSpace, inputs as unknown as NamedTensorMap,\n      attrs as unknown as NamedAttrMap);\n}\n\nexport const depthToSpace = /* @__PURE__ */ op({depthToSpace_});\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAiBA,SAAQA,MAAM,QAAO,WAAW;AAChC,SAAQC,YAAY,QAA8C,iBAAiB;AAInF,SAAQC,eAAe,QAAO,oBAAoB;AAElD,OAAO,KAAKC,IAAI,MAAM,SAAS;AAE/B,SAAQC,EAAE,QAAO,aAAa;AAE9B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqCA,SAASC,aAAaA,CAClBC,CAAwB,EAAEC,SAAiB,EACT;EAAA,IAAlCC,UAAA,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAA4B,MAAM;EACpC,MAAMG,EAAE,GAAGV,eAAe,CAACI,CAAC,EAAE,GAAG,EAAE,cAAc,EAAE,SAAS,CAAa;EAEzE,MAAMO,WAAW,GAAIL,UAAU,KAAK,MAAM,GAAII,EAAE,CAACE,KAAK,CAAC,CAAC,CAAC,GAAGF,EAAE,CAACE,KAAK,CAAC,CAAC,CAAC;EACvE,MAAMC,UAAU,GAAIP,UAAU,KAAK,MAAM,GAAII,EAAE,CAACE,KAAK,CAAC,CAAC,CAAC,GAAGF,EAAE,CAACE,KAAK,CAAC,CAAC,CAAC;EACtE,MAAME,UAAU,GAAIR,UAAU,KAAK,MAAM,GAAII,EAAE,CAACE,KAAK,CAAC,CAAC,CAAC,GAAGF,EAAE,CAACE,KAAK,CAAC,CAAC,CAAC;EAEtEX,IAAI,CAACc,MAAM,CACPV,SAAS,GAAG,CAAC,EACb,4DAAAW,MAAA,CAA4DX,SAAS,CAAE,CAAC;EAE5EJ,IAAI,CAACc,MAAM,CACPJ,WAAW,GAAGN,SAAS,IAAI,CAAC,EAC5B,0EAAAW,MAAA,CACAL,WAAW,WAAAK,MAAA,CAAQX,SAAS,+CAAAW,MAAA,CAC5BN,EAAE,CAACE,KAAK,CAAE,CAAC;EAEfX,IAAI,CAACc,MAAM,CACPF,UAAU,GAAGR,SAAS,IAAI,CAAC,EAC3B,0EAAAW,MAAA,CACAH,UAAU,WAAAG,MAAA,CAAQX,SAAS,kDAAAW,MAAA,CACvBN,EAAE,CAACE,KAAK,CAAE,CAAC;EAEnBX,IAAI,CAACc,MAAM,CACND,UAAU,IAAIT,SAAS,GAAGA,SAAS,CAAC,KAAK,CAAC,EAC3C,oDAAAW,MAAA,CACIX,SAAS,GAAGA,SAAS,cAAAW,MAAA,CACrBF,UAAU,yCAAAE,MAAA,CAAsCN,EAAE,CAACE,KAAK,CAAE,CAAC;EAEnE,MAAMK,MAAM,GAAuB;IAACb,CAAC,EAAEM;EAAE,CAAC;EAC1C,MAAMQ,KAAK,GAAsB;IAACb,SAAS;IAAEC;EAAU,CAAC;EAExD,OAAOR,MAAM,CAACqB,SAAS,CACnBpB,YAAY,EAAEkB,MAAmC,EACjDC,KAAgC,CAAC;AACvC;AAEA,OAAO,MAAME,YAAY,GAAG,eAAgBlB,EAAE,CAAC;EAACC;AAAa,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}