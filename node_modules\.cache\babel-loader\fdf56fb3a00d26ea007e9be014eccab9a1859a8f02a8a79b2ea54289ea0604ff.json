{"ast": null, "code": "/*\nCopyright (c) 2024, <PERSON><PERSON>\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in\nall copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\nTHE SOFTWARE.\n*/\n\nconst {\n  v4: uuidv4\n} = require('uuid');\nrequire('dotenv').config();\nconst memjs = require('memjs');\nconst memCachedConnectionString = process.env.MEM_HOST + ':' + process.env.MEM_PORT;\nconst memCachedExpiration = process.env.MEM_EXPIRES || 2;\n\n// Class for creating memcached client including store and retrieve methods\nclass MemcachedPlugin {\n  constructor() {\n    this.client = memjs.Client.create(memCachedConnectionString);\n  }\n  async store(object, options) {\n    const key = uuidv4();\n    const data = JSON.stringify(object);\n    const isStored = await this.client.set(key, data, {\n      expires: memCachedExpiration\n    });\n    return isStored ? key : null;\n  }\n  async retrieve(key, options) {\n    const result = await this.client.get(key);\n    return JSON.parse(result.value.toString());\n  }\n}\nmodule.exports = MemcachedPlugin;", "map": {"version": 3, "names": ["v4", "uuidv4", "require", "config", "memjs", "memCachedConnectionString", "process", "env", "MEM_HOST", "MEM_PORT", "memCachedExpiration", "MEM_EXPIRES", "MemcachedPlugin", "constructor", "client", "Client", "create", "store", "object", "options", "key", "data", "JSON", "stringify", "isStored", "set", "expires", "retrieve", "result", "get", "parse", "value", "toString", "module", "exports"], "sources": ["C:/tmsft/node_modules/natural/lib/natural/util/storage/Memcached.js"], "sourcesContent": ["/*\nCopyright (c) 2024, <PERSON><PERSON>\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in\nall copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\nTHE SOFTWARE.\n*/\n\nconst { v4: uuidv4 } = require('uuid')\nrequire('dotenv').config()\nconst memjs = require('memjs')\nconst memCachedConnectionString = process.env.MEM_HOST + ':' +\n  process.env.MEM_PORT\nconst memCachedExpiration = process.env.MEM_EXPIRES || 2\n\n// Class for creating memcached client including store and retrieve methods\nclass MemcachedPlugin {\n  constructor () {\n    this.client = memjs.Client.create(memCachedConnectionString)\n  }\n\n  async store (object, options) {\n    const key = uuidv4()\n    const data = JSON.stringify(object)\n    const isStored = await this.client.set(key, data, { expires: memCachedExpiration })\n    return isStored ? key : null\n  }\n\n  async retrieve (key, options) {\n    const result = await this.client.get(key)\n    return JSON.parse(result.value.toString())\n  }\n}\n\nmodule.exports = MemcachedPlugin\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,MAAM;EAAEA,EAAE,EAAEC;AAAO,CAAC,GAAGC,OAAO,CAAC,MAAM,CAAC;AACtCA,OAAO,CAAC,QAAQ,CAAC,CAACC,MAAM,CAAC,CAAC;AAC1B,MAAMC,KAAK,GAAGF,OAAO,CAAC,OAAO,CAAC;AAC9B,MAAMG,yBAAyB,GAAGC,OAAO,CAACC,GAAG,CAACC,QAAQ,GAAG,GAAG,GAC1DF,OAAO,CAACC,GAAG,CAACE,QAAQ;AACtB,MAAMC,mBAAmB,GAAGJ,OAAO,CAACC,GAAG,CAACI,WAAW,IAAI,CAAC;;AAExD;AACA,MAAMC,eAAe,CAAC;EACpBC,WAAWA,CAAA,EAAI;IACb,IAAI,CAACC,MAAM,GAAGV,KAAK,CAACW,MAAM,CAACC,MAAM,CAACX,yBAAyB,CAAC;EAC9D;EAEA,MAAMY,KAAKA,CAAEC,MAAM,EAAEC,OAAO,EAAE;IAC5B,MAAMC,GAAG,GAAGnB,MAAM,CAAC,CAAC;IACpB,MAAMoB,IAAI,GAAGC,IAAI,CAACC,SAAS,CAACL,MAAM,CAAC;IACnC,MAAMM,QAAQ,GAAG,MAAM,IAAI,CAACV,MAAM,CAACW,GAAG,CAACL,GAAG,EAAEC,IAAI,EAAE;MAAEK,OAAO,EAAEhB;IAAoB,CAAC,CAAC;IACnF,OAAOc,QAAQ,GAAGJ,GAAG,GAAG,IAAI;EAC9B;EAEA,MAAMO,QAAQA,CAAEP,GAAG,EAAED,OAAO,EAAE;IAC5B,MAAMS,MAAM,GAAG,MAAM,IAAI,CAACd,MAAM,CAACe,GAAG,CAACT,GAAG,CAAC;IACzC,OAAOE,IAAI,CAACQ,KAAK,CAACF,MAAM,CAACG,KAAK,CAACC,QAAQ,CAAC,CAAC,CAAC;EAC5C;AACF;AAEAC,MAAM,CAACC,OAAO,GAAGtB,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}