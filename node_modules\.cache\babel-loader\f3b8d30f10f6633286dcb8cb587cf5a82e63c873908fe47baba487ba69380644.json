{"ast": null, "code": "import isFunction from './isFunction.js';\nimport toPath from './_toPath.js';\n\n// Traverses the children of `obj` along `path`. If a child is a function, it\n// is invoked with its parent as context. Returns the value of the final\n// child, or `fallback` if any child is undefined.\nexport default function result(obj, path, fallback) {\n  path = toPath(path);\n  var length = path.length;\n  if (!length) {\n    return isFunction(fallback) ? fallback.call(obj) : fallback;\n  }\n  for (var i = 0; i < length; i++) {\n    var prop = obj == null ? void 0 : obj[path[i]];\n    if (prop === void 0) {\n      prop = fallback;\n      i = length; // Ensure we don't continue iterating.\n    }\n    obj = isFunction(prop) ? prop.call(obj) : prop;\n  }\n  return obj;\n}", "map": {"version": 3, "names": ["isFunction", "to<PERSON><PERSON>", "result", "obj", "path", "fallback", "length", "call", "i", "prop"], "sources": ["C:/tmsft/node_modules/underscore/modules/result.js"], "sourcesContent": ["import isFunction from './isFunction.js';\nimport toPath from './_toPath.js';\n\n// Traverses the children of `obj` along `path`. If a child is a function, it\n// is invoked with its parent as context. Returns the value of the final\n// child, or `fallback` if any child is undefined.\nexport default function result(obj, path, fallback) {\n  path = toPath(path);\n  var length = path.length;\n  if (!length) {\n    return isFunction(fallback) ? fallback.call(obj) : fallback;\n  }\n  for (var i = 0; i < length; i++) {\n    var prop = obj == null ? void 0 : obj[path[i]];\n    if (prop === void 0) {\n      prop = fallback;\n      i = length; // Ensure we don't continue iterating.\n    }\n    obj = isFunction(prop) ? prop.call(obj) : prop;\n  }\n  return obj;\n}\n"], "mappings": "AAAA,OAAOA,UAAU,MAAM,iBAAiB;AACxC,OAAOC,MAAM,MAAM,cAAc;;AAEjC;AACA;AACA;AACA,eAAe,SAASC,MAAMA,CAACC,GAAG,EAAEC,IAAI,EAAEC,QAAQ,EAAE;EAClDD,IAAI,GAAGH,MAAM,CAACG,IAAI,CAAC;EACnB,IAAIE,MAAM,GAAGF,IAAI,CAACE,MAAM;EACxB,IAAI,CAACA,MAAM,EAAE;IACX,OAAON,UAAU,CAACK,QAAQ,CAAC,GAAGA,QAAQ,CAACE,IAAI,CAACJ,GAAG,CAAC,GAAGE,QAAQ;EAC7D;EACA,KAAK,IAAIG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,MAAM,EAAEE,CAAC,EAAE,EAAE;IAC/B,IAAIC,IAAI,GAAGN,GAAG,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,GAAG,CAACC,IAAI,CAACI,CAAC,CAAC,CAAC;IAC9C,IAAIC,IAAI,KAAK,KAAK,CAAC,EAAE;MACnBA,IAAI,GAAGJ,QAAQ;MACfG,CAAC,GAAGF,MAAM,CAAC,CAAC;IACd;IACAH,GAAG,GAAGH,UAAU,CAACS,IAAI,CAAC,GAAGA,IAAI,CAACF,IAAI,CAACJ,GAAG,CAAC,GAAGM,IAAI;EAChD;EACA,OAAON,GAAG;AACZ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}