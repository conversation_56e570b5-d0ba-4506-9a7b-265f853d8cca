{"ast": null, "code": "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { Transpose } from '@tensorflow/tfjs-core';\nimport { assertNotComplex } from '../cpu_util';\nimport { transposeImpl } from './Transpose_impl';\nexport function transpose(args) {\n  const {\n    inputs,\n    attrs,\n    backend\n  } = args;\n  const {\n    x\n  } = inputs;\n  const {\n    perm\n  } = attrs;\n  assertNotComplex(x, 'transpose');\n  const xRank = x.shape.length;\n  const newShape = new Array(xRank);\n  for (let i = 0; i < newShape.length; i++) {\n    newShape[i] = x.shape[perm[i]];\n  }\n  const values = backend.data.get(x.dataId).values;\n  const result = transposeImpl(values, x.shape, x.dtype, perm, newShape);\n  const dataId = backend.write(result, newShape, x.dtype);\n  return {\n    dataId,\n    shape: newShape,\n    dtype: x.dtype\n  };\n}\nexport const transposeConfig = {\n  kernelName: Transpose,\n  backendName: 'cpu',\n  kernelFunc: transpose\n};", "map": {"version": 3, "names": ["Transpose", "assertNotComplex", "transposeImpl", "transpose", "args", "inputs", "attrs", "backend", "x", "perm", "xRank", "shape", "length", "newShape", "Array", "i", "values", "data", "get", "dataId", "result", "dtype", "write", "transposeConfig", "kernelName", "backendName", "kernelFunc"], "sources": ["C:\\tfjs-backend-cpu\\src\\kernels\\Transpose.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {KernelConfig, KernelFunc, TensorInfo, Transpose, TransposeAttrs, TransposeInputs, TypedArray} from '@tensorflow/tfjs-core';\n\nimport {MathBackendCPU} from '../backend_cpu';\nimport {assertNotComplex} from '../cpu_util';\n\nimport {transposeImpl} from './Transpose_impl';\n\nexport function transpose(args: {\n  inputs: TransposeInputs,\n  attrs: TransposeAttrs,\n  backend: MathBackendCPU\n}): TensorInfo {\n  const {inputs, attrs, backend} = args;\n  const {x} = inputs;\n  const {perm} = attrs;\n\n  assertNotComplex(x, 'transpose');\n\n  const xRank = x.shape.length;\n\n  const newShape: number[] = new Array(xRank);\n  for (let i = 0; i < newShape.length; i++) {\n    newShape[i] = x.shape[perm[i]];\n  }\n\n  const values = backend.data.get(x.dataId).values as TypedArray;\n  const result = transposeImpl(values, x.shape, x.dtype, perm, newShape);\n\n  const dataId = backend.write(result, newShape, x.dtype);\n  return {dataId, shape: newShape, dtype: x.dtype};\n}\n\nexport const transposeConfig: KernelConfig = {\n  kernelName: Transpose,\n  backendName: 'cpu',\n  kernelFunc: transpose as unknown as KernelFunc\n};\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAiBA,SAA8CA,SAAS,QAAoD,uBAAuB;AAGlI,SAAQC,gBAAgB,QAAO,aAAa;AAE5C,SAAQC,aAAa,QAAO,kBAAkB;AAE9C,OAAM,SAAUC,SAASA,CAACC,IAIzB;EACC,MAAM;IAACC,MAAM;IAAEC,KAAK;IAAEC;EAAO,CAAC,GAAGH,IAAI;EACrC,MAAM;IAACI;EAAC,CAAC,GAAGH,MAAM;EAClB,MAAM;IAACI;EAAI,CAAC,GAAGH,KAAK;EAEpBL,gBAAgB,CAACO,CAAC,EAAE,WAAW,CAAC;EAEhC,MAAME,KAAK,GAAGF,CAAC,CAACG,KAAK,CAACC,MAAM;EAE5B,MAAMC,QAAQ,GAAa,IAAIC,KAAK,CAACJ,KAAK,CAAC;EAC3C,KAAK,IAAIK,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,QAAQ,CAACD,MAAM,EAAEG,CAAC,EAAE,EAAE;IACxCF,QAAQ,CAACE,CAAC,CAAC,GAAGP,CAAC,CAACG,KAAK,CAACF,IAAI,CAACM,CAAC,CAAC,CAAC;;EAGhC,MAAMC,MAAM,GAAGT,OAAO,CAACU,IAAI,CAACC,GAAG,CAACV,CAAC,CAACW,MAAM,CAAC,CAACH,MAAoB;EAC9D,MAAMI,MAAM,GAAGlB,aAAa,CAACc,MAAM,EAAER,CAAC,CAACG,KAAK,EAAEH,CAAC,CAACa,KAAK,EAAEZ,IAAI,EAAEI,QAAQ,CAAC;EAEtE,MAAMM,MAAM,GAAGZ,OAAO,CAACe,KAAK,CAACF,MAAM,EAAEP,QAAQ,EAAEL,CAAC,CAACa,KAAK,CAAC;EACvD,OAAO;IAACF,MAAM;IAAER,KAAK,EAAEE,QAAQ;IAAEQ,KAAK,EAAEb,CAAC,CAACa;EAAK,CAAC;AAClD;AAEA,OAAO,MAAME,eAAe,GAAiB;EAC3CC,UAAU,EAAExB,SAAS;EACrByB,WAAW,EAAE,KAAK;EAClBC,UAAU,EAAEvB;CACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}