{"ast": null, "code": "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { Max } from '@tensorflow/tfjs-core';\nimport { backend_util } from '@tensorflow/tfjs-core';\nimport { util } from '@tensorflow/tfjs-core';\nimport { assertNotComplex } from '../cpu_util';\nimport { maxImpl } from './Max_impl';\nimport { transposeImpl } from './Transpose_impl';\nexport function max(args) {\n  const {\n    inputs,\n    backend,\n    attrs\n  } = args;\n  const {\n    x\n  } = inputs;\n  const {\n    reductionIndices,\n    keepDims\n  } = attrs;\n  const cpuBackend = backend;\n  let xShape = x.shape;\n  const xRank = xShape.length;\n  const origAxes = util.parseAxisParam(reductionIndices, xShape);\n  let axes = origAxes;\n  const permutedAxes = backend_util.getAxesPermutation(axes, xRank);\n  let xVals = cpuBackend.data.get(x.dataId).values;\n  if (permutedAxes != null) {\n    const newShape = new Array(xRank);\n    for (let i = 0; i < newShape.length; i++) {\n      newShape[i] = xShape[permutedAxes[i]];\n    }\n    xVals = transposeImpl(xVals, xShape, x.dtype, permutedAxes, newShape);\n    axes = backend_util.getInnerMostAxes(axes.length, xRank);\n    xShape = newShape;\n  }\n  assertNotComplex(x, 'max');\n  backend_util.assertAxesAreInnerMostDims('max', axes, xRank);\n  const [maxOutShape, reduceShape] = backend_util.computeOutAndReduceShapes(xShape, axes);\n  const reduceSize = util.sizeFromShape(reduceShape);\n  const result = maxImpl(xVals, reduceSize, maxOutShape, x.dtype);\n  const dataId = cpuBackend.write(result, maxOutShape, x.dtype);\n  let outShape = maxOutShape;\n  if (keepDims) {\n    // reshape\n    const newShape = backend_util.expandShapeToKeepDim(maxOutShape, origAxes);\n    outShape = newShape;\n  }\n  return {\n    dataId,\n    shape: outShape,\n    dtype: x.dtype\n  };\n}\nexport const maxConfig = {\n  kernelName: Max,\n  backendName: 'cpu',\n  kernelFunc: max\n};", "map": {"version": 3, "names": ["Max", "backend_util", "util", "assertNotComplex", "maxImpl", "transposeImpl", "max", "args", "inputs", "backend", "attrs", "x", "reductionIndices", "keepDims", "cpuBackend", "xShape", "shape", "xRank", "length", "origAxes", "parseAxisParam", "axes", "permutedAxes", "getAxesPermutation", "xVals", "data", "get", "dataId", "values", "newShape", "Array", "i", "dtype", "getInnerMostAxes", "assertAxesAreInnerMostDims", "maxOutShape", "reduceShape", "computeOutAndReduceShapes", "reduceSize", "sizeFromShape", "result", "write", "outShape", "expandShapeToKeepDim", "maxConfig", "kernelName", "backendName", "kernelFunc"], "sources": ["C:\\tfjs-backend-cpu\\src\\kernels\\Max.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {KernelFunc, Max, MaxAttrs, MaxInputs, TensorInfo} from '@tensorflow/tfjs-core';\nimport {backend_util, KernelConfig} from '@tensorflow/tfjs-core';\nimport {TypedArray, util} from '@tensorflow/tfjs-core';\n\nimport {MathBackendCPU} from '../backend_cpu';\nimport {assertNotComplex} from '../cpu_util';\n\nimport {maxImpl} from './Max_impl';\nimport {transposeImpl} from './Transpose_impl';\n\nexport function max(\n    args: {inputs: MaxInputs, backend: MathBackendCPU, attrs: MaxAttrs}):\n    TensorInfo {\n  const {inputs, backend, attrs} = args;\n  const {x} = inputs;\n  const {reductionIndices, keepDims} = attrs;\n  const cpuBackend = backend;\n  let xShape = x.shape;\n  const xRank = xShape.length;\n\n  const origAxes = util.parseAxisParam(reductionIndices, xShape);\n  let axes = origAxes;\n  const permutedAxes = backend_util.getAxesPermutation(axes, xRank);\n  let xVals = cpuBackend.data.get(x.dataId).values as TypedArray;\n  if (permutedAxes != null) {\n    const newShape: number[] = new Array(xRank);\n    for (let i = 0; i < newShape.length; i++) {\n      newShape[i] = xShape[permutedAxes[i]];\n    }\n\n    xVals = transposeImpl(xVals, xShape, x.dtype, permutedAxes, newShape);\n    axes = backend_util.getInnerMostAxes(axes.length, xRank);\n\n    xShape = newShape;\n  }\n\n  assertNotComplex(x, 'max');\n  backend_util.assertAxesAreInnerMostDims('max', axes, xRank);\n  const [maxOutShape, reduceShape] =\n      backend_util.computeOutAndReduceShapes(xShape, axes);\n\n  const reduceSize = util.sizeFromShape(reduceShape);\n\n  const result = maxImpl(xVals, reduceSize, maxOutShape, x.dtype);\n  const dataId = cpuBackend.write(result, maxOutShape, x.dtype);\n\n  let outShape = maxOutShape;\n  if (keepDims) {\n    // reshape\n    const newShape = backend_util.expandShapeToKeepDim(maxOutShape, origAxes);\n    outShape = newShape;\n  }\n\n  return {dataId, shape: outShape, dtype: x.dtype};\n}\n\nexport const maxConfig: KernelConfig = {\n  kernelName: Max,\n  backendName: 'cpu',\n  kernelFunc: max as unknown as KernelFunc\n};\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAiBA,SAAoBA,GAAG,QAAwC,uBAAuB;AACtF,SAAQC,YAAY,QAAqB,uBAAuB;AAChE,SAAoBC,IAAI,QAAO,uBAAuB;AAGtD,SAAQC,gBAAgB,QAAO,aAAa;AAE5C,SAAQC,OAAO,QAAO,YAAY;AAClC,SAAQC,aAAa,QAAO,kBAAkB;AAE9C,OAAM,SAAUC,GAAGA,CACfC,IAAmE;EAErE,MAAM;IAACC,MAAM;IAAEC,OAAO;IAAEC;EAAK,CAAC,GAAGH,IAAI;EACrC,MAAM;IAACI;EAAC,CAAC,GAAGH,MAAM;EAClB,MAAM;IAACI,gBAAgB;IAAEC;EAAQ,CAAC,GAAGH,KAAK;EAC1C,MAAMI,UAAU,GAAGL,OAAO;EAC1B,IAAIM,MAAM,GAAGJ,CAAC,CAACK,KAAK;EACpB,MAAMC,KAAK,GAAGF,MAAM,CAACG,MAAM;EAE3B,MAAMC,QAAQ,GAAGjB,IAAI,CAACkB,cAAc,CAACR,gBAAgB,EAAEG,MAAM,CAAC;EAC9D,IAAIM,IAAI,GAAGF,QAAQ;EACnB,MAAMG,YAAY,GAAGrB,YAAY,CAACsB,kBAAkB,CAACF,IAAI,EAAEJ,KAAK,CAAC;EACjE,IAAIO,KAAK,GAAGV,UAAU,CAACW,IAAI,CAACC,GAAG,CAACf,CAAC,CAACgB,MAAM,CAAC,CAACC,MAAoB;EAC9D,IAAIN,YAAY,IAAI,IAAI,EAAE;IACxB,MAAMO,QAAQ,GAAa,IAAIC,KAAK,CAACb,KAAK,CAAC;IAC3C,KAAK,IAAIc,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,QAAQ,CAACX,MAAM,EAAEa,CAAC,EAAE,EAAE;MACxCF,QAAQ,CAACE,CAAC,CAAC,GAAGhB,MAAM,CAACO,YAAY,CAACS,CAAC,CAAC,CAAC;;IAGvCP,KAAK,GAAGnB,aAAa,CAACmB,KAAK,EAAET,MAAM,EAAEJ,CAAC,CAACqB,KAAK,EAAEV,YAAY,EAAEO,QAAQ,CAAC;IACrER,IAAI,GAAGpB,YAAY,CAACgC,gBAAgB,CAACZ,IAAI,CAACH,MAAM,EAAED,KAAK,CAAC;IAExDF,MAAM,GAAGc,QAAQ;;EAGnB1B,gBAAgB,CAACQ,CAAC,EAAE,KAAK,CAAC;EAC1BV,YAAY,CAACiC,0BAA0B,CAAC,KAAK,EAAEb,IAAI,EAAEJ,KAAK,CAAC;EAC3D,MAAM,CAACkB,WAAW,EAAEC,WAAW,CAAC,GAC5BnC,YAAY,CAACoC,yBAAyB,CAACtB,MAAM,EAAEM,IAAI,CAAC;EAExD,MAAMiB,UAAU,GAAGpC,IAAI,CAACqC,aAAa,CAACH,WAAW,CAAC;EAElD,MAAMI,MAAM,GAAGpC,OAAO,CAACoB,KAAK,EAAEc,UAAU,EAAEH,WAAW,EAAExB,CAAC,CAACqB,KAAK,CAAC;EAC/D,MAAML,MAAM,GAAGb,UAAU,CAAC2B,KAAK,CAACD,MAAM,EAAEL,WAAW,EAAExB,CAAC,CAACqB,KAAK,CAAC;EAE7D,IAAIU,QAAQ,GAAGP,WAAW;EAC1B,IAAItB,QAAQ,EAAE;IACZ;IACA,MAAMgB,QAAQ,GAAG5B,YAAY,CAAC0C,oBAAoB,CAACR,WAAW,EAAEhB,QAAQ,CAAC;IACzEuB,QAAQ,GAAGb,QAAQ;;EAGrB,OAAO;IAACF,MAAM;IAAEX,KAAK,EAAE0B,QAAQ;IAAEV,KAAK,EAAErB,CAAC,CAACqB;EAAK,CAAC;AAClD;AAEA,OAAO,MAAMY,SAAS,GAAiB;EACrCC,UAAU,EAAE7C,GAAG;EACf8C,WAAW,EAAE,KAAK;EAClBC,UAAU,EAAEzC;CACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}