{"ast": null, "code": "/**\n * @license\n * Copyright 2019 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { ENGINE } from '../engine';\nimport { env } from '../environment';\nimport { Draw, FromPixels } from '../kernel_names';\nimport { getKernel } from '../kernel_registry';\nimport { Tensor } from '../tensor';\nimport { convertToTensor } from '../tensor_util_env';\nimport { cast } from './cast';\nimport { op } from './operation';\nimport { tensor3d } from './tensor3d';\nlet fromPixels2DContext;\nlet hasToPixelsWarned = false;\n/**\n * Creates a `tf.Tensor` from an image.\n *\n * ```js\n * const image = new ImageData(1, 1);\n * image.data[0] = 100;\n * image.data[1] = 150;\n * image.data[2] = 200;\n * image.data[3] = 255;\n *\n * tf.browser.fromPixels(image).print();\n * ```\n *\n * @param pixels The input image to construct the tensor from. The\n * supported image types are all 4-channel. You can also pass in an image\n * object with following attributes:\n * `{data: Uint8Array; width: number; height: number}`\n * @param numChannels The number of channels of the output tensor. A\n * numChannels value less than 4 allows you to ignore channels. Defaults to\n * 3 (ignores alpha channel of input image).\n *\n * @returns A Tensor3D with the shape `[height, width, numChannels]`.\n *\n * Note: fromPixels can be lossy in some cases, same image may result in\n * slightly different tensor values, if rendered by different rendering\n * engines. This means that results from different browsers, or even same\n * browser with CPU and GPU rendering engines can be different. See discussion\n * in details:\n * https://github.com/tensorflow/tfjs/issues/5482\n *\n * @doc {heading: 'Browser', namespace: 'browser', ignoreCI: true}\n */\nfunction fromPixels_(pixels, numChannels = 3) {\n  // Sanity checks.\n  if (numChannels > 4) {\n    throw new Error('Cannot construct Tensor with more than 4 channels from pixels.');\n  }\n  if (pixels == null) {\n    throw new Error('pixels passed to tf.browser.fromPixels() can not be null');\n  }\n  let isPixelData = false;\n  let isImageData = false;\n  let isVideo = false;\n  let isImage = false;\n  let isCanvasLike = false;\n  let isImageBitmap = false;\n  if (pixels.data instanceof Uint8Array) {\n    isPixelData = true;\n  } else if (typeof ImageData !== 'undefined' && pixels instanceof ImageData) {\n    isImageData = true;\n  } else if (typeof HTMLVideoElement !== 'undefined' && pixels instanceof HTMLVideoElement) {\n    isVideo = true;\n  } else if (typeof HTMLImageElement !== 'undefined' && pixels instanceof HTMLImageElement) {\n    isImage = true;\n    // tslint:disable-next-line: no-any\n  } else if (pixels.getContext != null) {\n    isCanvasLike = true;\n  } else if (typeof ImageBitmap !== 'undefined' && pixels instanceof ImageBitmap) {\n    isImageBitmap = true;\n  } else {\n    throw new Error('pixels passed to tf.browser.fromPixels() must be either an ' + `HTMLVideoElement, HTMLImageElement, HTMLCanvasElement, ImageData ` + `in browser, or OffscreenCanvas, ImageData in webworker` + ` or {data: Uint32Array, width: number, height: number}, ` + `but was ${pixels.constructor.name}`);\n  }\n  // If the current backend has 'FromPixels' registered, it has a more\n  // efficient way of handling pixel uploads, so we call that.\n  const kernel = getKernel(FromPixels, ENGINE.backendName);\n  if (kernel != null) {\n    const inputs = {\n      pixels\n    };\n    const attrs = {\n      numChannels\n    };\n    return ENGINE.runKernel(FromPixels, inputs, attrs);\n  }\n  const [width, height] = isVideo ? [pixels.videoWidth, pixels.videoHeight] : [pixels.width, pixels.height];\n  let vals;\n  if (isCanvasLike) {\n    vals =\n    // tslint:disable-next-line:no-any\n    pixels.getContext('2d').getImageData(0, 0, width, height).data;\n  } else if (isImageData || isPixelData) {\n    vals = pixels.data;\n  } else if (isImage || isVideo || isImageBitmap) {\n    if (fromPixels2DContext == null) {\n      if (typeof document === 'undefined') {\n        if (typeof OffscreenCanvas !== 'undefined' && typeof OffscreenCanvasRenderingContext2D !== 'undefined') {\n          // @ts-ignore\n          fromPixels2DContext = new OffscreenCanvas(1, 1).getContext('2d');\n        } else {\n          throw new Error('Cannot parse input in current context. ' + 'Reason: OffscreenCanvas Context2D rendering is not supported.');\n        }\n      } else {\n        fromPixels2DContext = document.createElement('canvas').getContext('2d', {\n          willReadFrequently: true\n        });\n      }\n    }\n    fromPixels2DContext.canvas.width = width;\n    fromPixels2DContext.canvas.height = height;\n    fromPixels2DContext.drawImage(pixels, 0, 0, width, height);\n    vals = fromPixels2DContext.getImageData(0, 0, width, height).data;\n  }\n  let values;\n  if (numChannels === 4) {\n    values = new Int32Array(vals);\n  } else {\n    const numPixels = width * height;\n    values = new Int32Array(numPixels * numChannels);\n    for (let i = 0; i < numPixels; i++) {\n      for (let channel = 0; channel < numChannels; ++channel) {\n        values[i * numChannels + channel] = vals[i * 4 + channel];\n      }\n    }\n  }\n  const outShape = [height, width, numChannels];\n  return tensor3d(values, outShape, 'int32');\n}\n// Helper functions for |fromPixelsAsync| to check whether the input can\n// be wrapped into imageBitmap.\nfunction isPixelData(pixels) {\n  return pixels != null && pixels.data instanceof Uint8Array;\n}\nfunction isImageBitmapFullySupported() {\n  return typeof window !== 'undefined' && typeof ImageBitmap !== 'undefined' && window.hasOwnProperty('createImageBitmap');\n}\nfunction isNonEmptyPixels(pixels) {\n  return pixels != null && pixels.width !== 0 && pixels.height !== 0;\n}\nfunction canWrapPixelsToImageBitmap(pixels) {\n  return isImageBitmapFullySupported() && !(pixels instanceof ImageBitmap) && isNonEmptyPixels(pixels) && !isPixelData(pixels);\n}\n/**\n * Creates a `tf.Tensor` from an image in async way.\n *\n * ```js\n * const image = new ImageData(1, 1);\n * image.data[0] = 100;\n * image.data[1] = 150;\n * image.data[2] = 200;\n * image.data[3] = 255;\n *\n * (await tf.browser.fromPixelsAsync(image)).print();\n * ```\n * This API is the async version of fromPixels. The API will first\n * check |WRAP_TO_IMAGEBITMAP| flag, and try to wrap the input to\n * imageBitmap if the flag is set to true.\n *\n * @param pixels The input image to construct the tensor from. The\n * supported image types are all 4-channel. You can also pass in an image\n * object with following attributes:\n * `{data: Uint8Array; width: number; height: number}`\n * @param numChannels The number of channels of the output tensor. A\n * numChannels value less than 4 allows you to ignore channels. Defaults to\n * 3 (ignores alpha channel of input image).\n *\n * @doc {heading: 'Browser', namespace: 'browser', ignoreCI: true}\n */\nexport async function fromPixelsAsync(pixels, numChannels = 3) {\n  let inputs = null;\n  // Check whether the backend needs to wrap |pixels| to imageBitmap and\n  // whether |pixels| can be wrapped to imageBitmap.\n  if (env().getBool('WRAP_TO_IMAGEBITMAP') && canWrapPixelsToImageBitmap(pixels)) {\n    // Force the imageBitmap creation to not do any premultiply alpha\n    // ops.\n    let imageBitmap;\n    try {\n      // wrap in try-catch block, because createImageBitmap may not work\n      // properly in some browsers, e.g.\n      // https://bugzilla.mozilla.org/show_bug.cgi?id=1335594\n      // tslint:disable-next-line: no-any\n      imageBitmap = await createImageBitmap(pixels, {\n        premultiplyAlpha: 'none'\n      });\n    } catch (e) {\n      imageBitmap = null;\n    }\n    // createImageBitmap will clip the source size.\n    // In some cases, the input will have larger size than its content.\n    // E.g. new Image(10, 10) but with 1 x 1 content. Using\n    // createImageBitmap will clip the size from 10 x 10 to 1 x 1, which\n    // is not correct. We should avoid wrapping such resouce to\n    // imageBitmap.\n    if (imageBitmap != null && imageBitmap.width === pixels.width && imageBitmap.height === pixels.height) {\n      inputs = imageBitmap;\n    } else {\n      inputs = pixels;\n    }\n  } else {\n    inputs = pixels;\n  }\n  return fromPixels_(inputs, numChannels);\n}\nfunction validateImgTensor(img) {\n  if (img.rank !== 2 && img.rank !== 3) {\n    throw new Error(`toPixels only supports rank 2 or 3 tensors, got rank ${img.rank}.`);\n  }\n  const depth = img.rank === 2 ? 1 : img.shape[2];\n  if (depth > 4 || depth === 2) {\n    throw new Error(`toPixels only supports depth of size ` + `1, 3 or 4 but got ${depth}`);\n  }\n  if (img.dtype !== 'float32' && img.dtype !== 'int32') {\n    throw new Error(`Unsupported type for toPixels: ${img.dtype}.` + ` Please use float32 or int32 tensors.`);\n  }\n}\nfunction validateImageOptions(imageOptions) {\n  const alpha = (imageOptions === null || imageOptions === void 0 ? void 0 : imageOptions.alpha) || 1;\n  if (alpha > 1 || alpha < 0) {\n    throw new Error(`Alpha value ${alpha} is suppoed to be in range [0 - 1].`);\n  }\n}\n/**\n * Draws a `tf.Tensor` of pixel values to a byte array or optionally a\n * canvas.\n *\n * When the dtype of the input is 'float32', we assume values in the range\n * [0-1]. Otherwise, when input is 'int32', we assume values in the range\n * [0-255].\n *\n * Returns a promise that resolves when the canvas has been drawn to.\n *\n * @param img A rank-2 tensor with shape `[height, width]`, or a rank-3 tensor\n * of shape `[height, width, numChannels]`. If rank-2, draws grayscale. If\n * rank-3, must have depth of 1, 3 or 4. When depth of 1, draws\n * grayscale. When depth of 3, we draw with the first three components of\n * the depth dimension corresponding to r, g, b and alpha = 1. When depth of\n * 4, all four components of the depth dimension correspond to r, g, b, a.\n * @param canvas The canvas to draw to.\n *\n * @doc {heading: 'Browser', namespace: 'browser'}\n */\nexport async function toPixels(img, canvas) {\n  let $img = convertToTensor(img, 'img', 'toPixels');\n  if (!(img instanceof Tensor)) {\n    // Assume int32 if user passed a native array.\n    const originalImgTensor = $img;\n    $img = cast(originalImgTensor, 'int32');\n    originalImgTensor.dispose();\n  }\n  validateImgTensor($img);\n  const [height, width] = $img.shape.slice(0, 2);\n  const depth = $img.rank === 2 ? 1 : $img.shape[2];\n  const data = await $img.data();\n  const multiplier = $img.dtype === 'float32' ? 255 : 1;\n  const bytes = new Uint8ClampedArray(width * height * 4);\n  for (let i = 0; i < height * width; ++i) {\n    const rgba = [0, 0, 0, 255];\n    for (let d = 0; d < depth; d++) {\n      const value = data[i * depth + d];\n      if ($img.dtype === 'float32') {\n        if (value < 0 || value > 1) {\n          throw new Error(`Tensor values for a float32 Tensor must be in the ` + `range [0 - 1] but encountered ${value}.`);\n        }\n      } else if ($img.dtype === 'int32') {\n        if (value < 0 || value > 255) {\n          throw new Error(`Tensor values for a int32 Tensor must be in the ` + `range [0 - 255] but encountered ${value}.`);\n        }\n      }\n      if (depth === 1) {\n        rgba[0] = value * multiplier;\n        rgba[1] = value * multiplier;\n        rgba[2] = value * multiplier;\n      } else {\n        rgba[d] = value * multiplier;\n      }\n    }\n    const j = i * 4;\n    bytes[j + 0] = Math.round(rgba[0]);\n    bytes[j + 1] = Math.round(rgba[1]);\n    bytes[j + 2] = Math.round(rgba[2]);\n    bytes[j + 3] = Math.round(rgba[3]);\n  }\n  if (canvas != null) {\n    if (!hasToPixelsWarned) {\n      const kernel = getKernel(Draw, ENGINE.backendName);\n      if (kernel != null) {\n        console.warn('tf.browser.toPixels is not efficient to draw tensor on canvas. ' + 'Please try tf.browser.draw instead.');\n        hasToPixelsWarned = true;\n      }\n    }\n    canvas.width = width;\n    canvas.height = height;\n    const ctx = canvas.getContext('2d');\n    const imageData = new ImageData(bytes, width, height);\n    ctx.putImageData(imageData, 0, 0);\n  }\n  if ($img !== img) {\n    $img.dispose();\n  }\n  return bytes;\n}\n/**\n * Draws a `tf.Tensor` to a canvas.\n *\n * When the dtype of the input is 'float32', we assume values in the range\n * [0-1]. Otherwise, when input is 'int32', we assume values in the range\n * [0-255].\n *\n * @param image The tensor to draw on the canvas. Must match one of\n * these shapes:\n *   - Rank-2 with shape `[height, width`]: Drawn as grayscale.\n *   - Rank-3 with shape `[height, width, 1]`: Drawn as grayscale.\n *   - Rank-3 with shape `[height, width, 3]`: Drawn as RGB with alpha set in\n *     `imageOptions` (defaults to 1, which is opaque).\n *   - Rank-3 with shape `[height, width, 4]`: Drawn as RGBA.\n * @param canvas The canvas to draw to.\n * @param options The configuration arguments for image to be drawn and the\n *     canvas to draw to.\n *\n * @doc {heading: 'Browser', namespace: 'browser'}\n */\nexport function draw(image, canvas, options) {\n  let $img = convertToTensor(image, 'img', 'draw');\n  if (!(image instanceof Tensor)) {\n    // Assume int32 if user passed a native array.\n    const originalImgTensor = $img;\n    $img = cast(originalImgTensor, 'int32');\n    originalImgTensor.dispose();\n  }\n  validateImgTensor($img);\n  validateImageOptions(options === null || options === void 0 ? void 0 : options.imageOptions);\n  const inputs = {\n    image: $img\n  };\n  const attrs = {\n    canvas,\n    options\n  };\n  ENGINE.runKernel(Draw, inputs, attrs);\n}\nexport const fromPixels = /* @__PURE__ */op({\n  fromPixels_\n});", "map": {"version": 3, "names": ["ENGINE", "env", "Draw", "FromPixels", "getKernel", "Tensor", "convertToTensor", "cast", "op", "tensor3d", "fromPixels2DContext", "hasToPixelsWarned", "fromPixels_", "pixels", "numChannels", "Error", "isPixelData", "isImageData", "isVideo", "isImage", "isCanvasLike", "isImageBitmap", "data", "Uint8Array", "ImageData", "HTMLVideoElement", "HTMLImageElement", "getContext", "ImageBitmap", "constructor", "name", "kernel", "backendName", "inputs", "attrs", "runKernel", "width", "height", "videoWidth", "videoHeight", "vals", "getImageData", "document", "OffscreenCanvas", "OffscreenCanvasRenderingContext2D", "createElement", "willReadFrequently", "canvas", "drawImage", "values", "Int32Array", "numPixels", "i", "channel", "outShape", "isImageBitmapFullySupported", "window", "hasOwnProperty", "isNonEmptyPixels", "canWrapPixelsToImageBitmap", "fromPixelsAsync", "getBool", "imageBitmap", "createImageBitmap", "premultiplyAlpha", "e", "validateImgTensor", "img", "rank", "depth", "shape", "dtype", "validateImageOptions", "imageOptions", "alpha", "toPixels", "$img", "originalImgTensor", "dispose", "slice", "multiplier", "bytes", "Uint8ClampedArray", "rgba", "d", "value", "j", "Math", "round", "console", "warn", "ctx", "imageData", "putImageData", "draw", "image", "options", "fromPixels"], "sources": ["C:\\tfjs-core\\src\\ops\\browser.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2019 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {ENGINE} from '../engine';\nimport {env} from '../environment';\nimport {Draw, DrawAttrs, DrawInputs, FromPixels, FromPixelsAttrs, FromPixelsInputs} from '../kernel_names';\nimport {getKernel, NamedAttrMap} from '../kernel_registry';\nimport {Tensor, Tensor2D, Tensor3D} from '../tensor';\nimport {NamedTensorMap} from '../tensor_types';\nimport {convertToTensor} from '../tensor_util_env';\nimport {DrawOptions, ImageOptions, PixelData, TensorLike} from '../types';\n\nimport {cast} from './cast';\nimport {op} from './operation';\nimport {tensor3d} from './tensor3d';\n\nlet fromPixels2DContext: CanvasRenderingContext2D;\nlet hasToPixelsWarned = false;\n\n/**\n * Creates a `tf.Tensor` from an image.\n *\n * ```js\n * const image = new ImageData(1, 1);\n * image.data[0] = 100;\n * image.data[1] = 150;\n * image.data[2] = 200;\n * image.data[3] = 255;\n *\n * tf.browser.fromPixels(image).print();\n * ```\n *\n * @param pixels The input image to construct the tensor from. The\n * supported image types are all 4-channel. You can also pass in an image\n * object with following attributes:\n * `{data: Uint8Array; width: number; height: number}`\n * @param numChannels The number of channels of the output tensor. A\n * numChannels value less than 4 allows you to ignore channels. Defaults to\n * 3 (ignores alpha channel of input image).\n *\n * @returns A Tensor3D with the shape `[height, width, numChannels]`.\n *\n * Note: fromPixels can be lossy in some cases, same image may result in\n * slightly different tensor values, if rendered by different rendering\n * engines. This means that results from different browsers, or even same\n * browser with CPU and GPU rendering engines can be different. See discussion\n * in details:\n * https://github.com/tensorflow/tfjs/issues/5482\n *\n * @doc {heading: 'Browser', namespace: 'browser', ignoreCI: true}\n */\nfunction fromPixels_(\n    pixels: PixelData|ImageData|HTMLImageElement|HTMLCanvasElement|\n    HTMLVideoElement|ImageBitmap,\n    numChannels = 3): Tensor3D {\n  // Sanity checks.\n  if (numChannels > 4) {\n    throw new Error(\n        'Cannot construct Tensor with more than 4 channels from pixels.');\n  }\n  if (pixels == null) {\n    throw new Error('pixels passed to tf.browser.fromPixels() can not be null');\n  }\n  let isPixelData = false;\n  let isImageData = false;\n  let isVideo = false;\n  let isImage = false;\n  let isCanvasLike = false;\n  let isImageBitmap = false;\n  if ((pixels as PixelData).data instanceof Uint8Array) {\n    isPixelData = true;\n  } else if (\n      typeof (ImageData) !== 'undefined' && pixels instanceof ImageData) {\n    isImageData = true;\n  } else if (\n      typeof (HTMLVideoElement) !== 'undefined' &&\n      pixels instanceof HTMLVideoElement) {\n    isVideo = true;\n  } else if (\n      typeof (HTMLImageElement) !== 'undefined' &&\n      pixels instanceof HTMLImageElement) {\n    isImage = true;\n    // tslint:disable-next-line: no-any\n  } else if ((pixels as any).getContext != null) {\n    isCanvasLike = true;\n  } else if (\n      typeof (ImageBitmap) !== 'undefined' && pixels instanceof ImageBitmap) {\n    isImageBitmap = true;\n  } else {\n    throw new Error(\n        'pixels passed to tf.browser.fromPixels() must be either an ' +\n        `HTMLVideoElement, HTMLImageElement, HTMLCanvasElement, ImageData ` +\n        `in browser, or OffscreenCanvas, ImageData in webworker` +\n        ` or {data: Uint32Array, width: number, height: number}, ` +\n        `but was ${(pixels as {}).constructor.name}`);\n  }\n  // If the current backend has 'FromPixels' registered, it has a more\n  // efficient way of handling pixel uploads, so we call that.\n  const kernel = getKernel(FromPixels, ENGINE.backendName);\n  if (kernel != null) {\n    const inputs: FromPixelsInputs = {pixels};\n    const attrs: FromPixelsAttrs = {numChannels};\n    return ENGINE.runKernel(\n        FromPixels, inputs as unknown as NamedTensorMap,\n        attrs as unknown as NamedAttrMap);\n  }\n\n  const [width, height] = isVideo ?\n      [\n        (pixels as HTMLVideoElement).videoWidth,\n        (pixels as HTMLVideoElement).videoHeight\n      ] :\n      [pixels.width, pixels.height];\n  let vals: Uint8ClampedArray|Uint8Array;\n\n  if (isCanvasLike) {\n    vals =\n        // tslint:disable-next-line:no-any\n        (pixels as any).getContext('2d').getImageData(0, 0, width, height).data;\n  } else if (isImageData || isPixelData) {\n    vals = (pixels as PixelData | ImageData).data;\n  } else if (isImage || isVideo || isImageBitmap) {\n    if (fromPixels2DContext == null) {\n      if (typeof document === 'undefined') {\n        if (typeof OffscreenCanvas !== 'undefined' &&\n            typeof OffscreenCanvasRenderingContext2D !== 'undefined') {\n          // @ts-ignore\n          fromPixels2DContext = new OffscreenCanvas(1, 1).getContext('2d');\n        } else {\n          throw new Error(\n              'Cannot parse input in current context. ' +\n              'Reason: OffscreenCanvas Context2D rendering is not supported.');\n        }\n      } else {\n        fromPixels2DContext = document.createElement('canvas').getContext(\n            '2d', {willReadFrequently: true});\n      }\n    }\n    fromPixels2DContext.canvas.width = width;\n    fromPixels2DContext.canvas.height = height;\n    fromPixels2DContext.drawImage(\n        pixels as HTMLVideoElement, 0, 0, width, height);\n    vals = fromPixels2DContext.getImageData(0, 0, width, height).data;\n  }\n  let values: Int32Array;\n  if (numChannels === 4) {\n    values = new Int32Array(vals);\n  } else {\n    const numPixels = width * height;\n    values = new Int32Array(numPixels * numChannels);\n    for (let i = 0; i < numPixels; i++) {\n      for (let channel = 0; channel < numChannels; ++channel) {\n        values[i * numChannels + channel] = vals[i * 4 + channel];\n      }\n    }\n  }\n  const outShape: [number, number, number] = [height, width, numChannels];\n  return tensor3d(values, outShape, 'int32');\n}\n\n// Helper functions for |fromPixelsAsync| to check whether the input can\n// be wrapped into imageBitmap.\nfunction isPixelData(pixels: PixelData|ImageData|HTMLImageElement|\n                     HTMLCanvasElement|HTMLVideoElement|\n                     ImageBitmap): pixels is PixelData {\n  return (pixels != null) && ((pixels as PixelData).data instanceof Uint8Array);\n}\n\nfunction isImageBitmapFullySupported() {\n  return typeof window !== 'undefined' &&\n      typeof (ImageBitmap) !== 'undefined' &&\n      window.hasOwnProperty('createImageBitmap');\n}\n\nfunction isNonEmptyPixels(pixels: PixelData|ImageData|HTMLImageElement|\n                          HTMLCanvasElement|HTMLVideoElement|ImageBitmap) {\n  return pixels != null && pixels.width !== 0 && pixels.height !== 0;\n}\n\nfunction canWrapPixelsToImageBitmap(pixels: PixelData|ImageData|\n                                    HTMLImageElement|HTMLCanvasElement|\n                                    HTMLVideoElement|ImageBitmap) {\n  return isImageBitmapFullySupported() && !(pixels instanceof ImageBitmap) &&\n      isNonEmptyPixels(pixels) && !isPixelData(pixels);\n}\n\n/**\n * Creates a `tf.Tensor` from an image in async way.\n *\n * ```js\n * const image = new ImageData(1, 1);\n * image.data[0] = 100;\n * image.data[1] = 150;\n * image.data[2] = 200;\n * image.data[3] = 255;\n *\n * (await tf.browser.fromPixelsAsync(image)).print();\n * ```\n * This API is the async version of fromPixels. The API will first\n * check |WRAP_TO_IMAGEBITMAP| flag, and try to wrap the input to\n * imageBitmap if the flag is set to true.\n *\n * @param pixels The input image to construct the tensor from. The\n * supported image types are all 4-channel. You can also pass in an image\n * object with following attributes:\n * `{data: Uint8Array; width: number; height: number}`\n * @param numChannels The number of channels of the output tensor. A\n * numChannels value less than 4 allows you to ignore channels. Defaults to\n * 3 (ignores alpha channel of input image).\n *\n * @doc {heading: 'Browser', namespace: 'browser', ignoreCI: true}\n */\nexport async function fromPixelsAsync(\n    pixels: PixelData|ImageData|HTMLImageElement|HTMLCanvasElement|\n    HTMLVideoElement|ImageBitmap,\n    numChannels = 3) {\n  let inputs: PixelData|ImageData|HTMLImageElement|HTMLCanvasElement|\n      HTMLVideoElement|ImageBitmap = null;\n\n  // Check whether the backend needs to wrap |pixels| to imageBitmap and\n  // whether |pixels| can be wrapped to imageBitmap.\n  if (env().getBool('WRAP_TO_IMAGEBITMAP') &&\n      canWrapPixelsToImageBitmap(pixels)) {\n    // Force the imageBitmap creation to not do any premultiply alpha\n    // ops.\n    let imageBitmap;\n\n    try {\n      // wrap in try-catch block, because createImageBitmap may not work\n      // properly in some browsers, e.g.\n      // https://bugzilla.mozilla.org/show_bug.cgi?id=1335594\n      // tslint:disable-next-line: no-any\n      imageBitmap = await (createImageBitmap as any)(\n          pixels as ImageBitmapSource, {premultiplyAlpha: 'none'});\n    } catch (e) {\n      imageBitmap = null;\n    }\n\n    // createImageBitmap will clip the source size.\n    // In some cases, the input will have larger size than its content.\n    // E.g. new Image(10, 10) but with 1 x 1 content. Using\n    // createImageBitmap will clip the size from 10 x 10 to 1 x 1, which\n    // is not correct. We should avoid wrapping such resouce to\n    // imageBitmap.\n    if (imageBitmap != null && imageBitmap.width === pixels.width &&\n        imageBitmap.height === pixels.height) {\n      inputs = imageBitmap;\n    } else {\n      inputs = pixels;\n    }\n  } else {\n    inputs = pixels;\n  }\n\n  return fromPixels_(inputs, numChannels);\n}\n\nfunction validateImgTensor(img: Tensor2D|Tensor3D) {\n  if (img.rank !== 2 && img.rank !== 3) {\n    throw new Error(\n        `toPixels only supports rank 2 or 3 tensors, got rank ${img.rank}.`);\n  }\n  const depth = img.rank === 2 ? 1 : img.shape[2];\n\n  if (depth > 4 || depth === 2) {\n    throw new Error(\n        `toPixels only supports depth of size ` +\n        `1, 3 or 4 but got ${depth}`);\n  }\n\n  if (img.dtype !== 'float32' && img.dtype !== 'int32') {\n    throw new Error(\n        `Unsupported type for toPixels: ${img.dtype}.` +\n        ` Please use float32 or int32 tensors.`);\n  }\n}\n\nfunction validateImageOptions(imageOptions: ImageOptions) {\n  const alpha = imageOptions ?.alpha || 1;\n  if (alpha > 1 || alpha < 0) {\n    throw new Error(`Alpha value ${alpha} is suppoed to be in range [0 - 1].`);\n  }\n}\n\n/**\n * Draws a `tf.Tensor` of pixel values to a byte array or optionally a\n * canvas.\n *\n * When the dtype of the input is 'float32', we assume values in the range\n * [0-1]. Otherwise, when input is 'int32', we assume values in the range\n * [0-255].\n *\n * Returns a promise that resolves when the canvas has been drawn to.\n *\n * @param img A rank-2 tensor with shape `[height, width]`, or a rank-3 tensor\n * of shape `[height, width, numChannels]`. If rank-2, draws grayscale. If\n * rank-3, must have depth of 1, 3 or 4. When depth of 1, draws\n * grayscale. When depth of 3, we draw with the first three components of\n * the depth dimension corresponding to r, g, b and alpha = 1. When depth of\n * 4, all four components of the depth dimension correspond to r, g, b, a.\n * @param canvas The canvas to draw to.\n *\n * @doc {heading: 'Browser', namespace: 'browser'}\n */\nexport async function toPixels(\n    img: Tensor2D|Tensor3D|TensorLike,\n    canvas?: HTMLCanvasElement): Promise<Uint8ClampedArray> {\n  let $img = convertToTensor(img, 'img', 'toPixels');\n  if (!(img instanceof Tensor)) {\n    // Assume int32 if user passed a native array.\n    const originalImgTensor = $img;\n    $img = cast(originalImgTensor, 'int32');\n    originalImgTensor.dispose();\n  }\n  validateImgTensor($img);\n\n  const [height, width] = $img.shape.slice(0, 2);\n  const depth = $img.rank === 2 ? 1 : $img.shape[2];\n  const data = await $img.data();\n  const multiplier = $img.dtype === 'float32' ? 255 : 1;\n  const bytes = new Uint8ClampedArray(width * height * 4);\n\n  for (let i = 0; i < height * width; ++i) {\n    const rgba = [0, 0, 0, 255];\n\n    for (let d = 0; d < depth; d++) {\n      const value = data[i * depth + d];\n\n      if ($img.dtype === 'float32') {\n        if (value < 0 || value > 1) {\n          throw new Error(\n              `Tensor values for a float32 Tensor must be in the ` +\n              `range [0 - 1] but encountered ${value}.`);\n        }\n      } else if ($img.dtype === 'int32') {\n        if (value < 0 || value > 255) {\n          throw new Error(\n              `Tensor values for a int32 Tensor must be in the ` +\n              `range [0 - 255] but encountered ${value}.`);\n        }\n      }\n\n      if (depth === 1) {\n        rgba[0] = value * multiplier;\n        rgba[1] = value * multiplier;\n        rgba[2] = value * multiplier;\n      } else {\n        rgba[d] = value * multiplier;\n      }\n    }\n\n    const j = i * 4;\n    bytes[j + 0] = Math.round(rgba[0]);\n    bytes[j + 1] = Math.round(rgba[1]);\n    bytes[j + 2] = Math.round(rgba[2]);\n    bytes[j + 3] = Math.round(rgba[3]);\n  }\n\n  if (canvas != null) {\n    if (!hasToPixelsWarned) {\n      const kernel = getKernel(Draw, ENGINE.backendName);\n      if (kernel != null) {\n        console.warn(\n            'tf.browser.toPixels is not efficient to draw tensor on canvas. ' +\n            'Please try tf.browser.draw instead.');\n        hasToPixelsWarned = true;\n      }\n    }\n\n    canvas.width = width;\n    canvas.height = height;\n    const ctx = canvas.getContext('2d');\n    const imageData = new ImageData(bytes, width, height);\n    ctx.putImageData(imageData, 0, 0);\n  }\n  if ($img !== img) {\n    $img.dispose();\n  }\n  return bytes;\n}\n\n/**\n * Draws a `tf.Tensor` to a canvas.\n *\n * When the dtype of the input is 'float32', we assume values in the range\n * [0-1]. Otherwise, when input is 'int32', we assume values in the range\n * [0-255].\n *\n * @param image The tensor to draw on the canvas. Must match one of\n * these shapes:\n *   - Rank-2 with shape `[height, width`]: Drawn as grayscale.\n *   - Rank-3 with shape `[height, width, 1]`: Drawn as grayscale.\n *   - Rank-3 with shape `[height, width, 3]`: Drawn as RGB with alpha set in\n *     `imageOptions` (defaults to 1, which is opaque).\n *   - Rank-3 with shape `[height, width, 4]`: Drawn as RGBA.\n * @param canvas The canvas to draw to.\n * @param options The configuration arguments for image to be drawn and the\n *     canvas to draw to.\n *\n * @doc {heading: 'Browser', namespace: 'browser'}\n */\nexport function draw(\n    image: Tensor2D|Tensor3D|TensorLike, canvas: HTMLCanvasElement,\n    options?: DrawOptions): void {\n  let $img = convertToTensor(image, 'img', 'draw');\n  if (!(image instanceof Tensor)) {\n    // Assume int32 if user passed a native array.\n    const originalImgTensor = $img;\n    $img = cast(originalImgTensor, 'int32');\n    originalImgTensor.dispose();\n  }\n  validateImgTensor($img);\n  validateImageOptions(options?.imageOptions);\n\n  const inputs: DrawInputs = {image: $img};\n  const attrs: DrawAttrs = {canvas, options};\n  ENGINE.runKernel(\n      Draw, inputs as unknown as NamedTensorMap,\n      attrs as unknown as NamedAttrMap);\n}\n\nexport const fromPixels = /* @__PURE__ */ op({fromPixels_});\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAiBA,SAAQA,MAAM,QAAO,WAAW;AAChC,SAAQC,GAAG,QAAO,gBAAgB;AAClC,SAAQC,IAAI,EAAyBC,UAAU,QAA0C,iBAAiB;AAC1G,SAAQC,SAAS,QAAqB,oBAAoB;AAC1D,SAAQC,MAAM,QAA2B,WAAW;AAEpD,SAAQC,eAAe,QAAO,oBAAoB;AAGlD,SAAQC,IAAI,QAAO,QAAQ;AAC3B,SAAQC,EAAE,QAAO,aAAa;AAC9B,SAAQC,QAAQ,QAAO,YAAY;AAEnC,IAAIC,mBAA6C;AACjD,IAAIC,iBAAiB,GAAG,KAAK;AAE7B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgCA,SAASC,WAAWA,CAChBC,MAC4B,EAC5BC,WAAW,GAAG,CAAC;EACjB;EACA,IAAIA,WAAW,GAAG,CAAC,EAAE;IACnB,MAAM,IAAIC,KAAK,CACX,gEAAgE,CAAC;;EAEvE,IAAIF,MAAM,IAAI,IAAI,EAAE;IAClB,MAAM,IAAIE,KAAK,CAAC,0DAA0D,CAAC;;EAE7E,IAAIC,WAAW,GAAG,KAAK;EACvB,IAAIC,WAAW,GAAG,KAAK;EACvB,IAAIC,OAAO,GAAG,KAAK;EACnB,IAAIC,OAAO,GAAG,KAAK;EACnB,IAAIC,YAAY,GAAG,KAAK;EACxB,IAAIC,aAAa,GAAG,KAAK;EACzB,IAAKR,MAAoB,CAACS,IAAI,YAAYC,UAAU,EAAE;IACpDP,WAAW,GAAG,IAAI;GACnB,MAAM,IACH,OAAQQ,SAAU,KAAK,WAAW,IAAIX,MAAM,YAAYW,SAAS,EAAE;IACrEP,WAAW,GAAG,IAAI;GACnB,MAAM,IACH,OAAQQ,gBAAiB,KAAK,WAAW,IACzCZ,MAAM,YAAYY,gBAAgB,EAAE;IACtCP,OAAO,GAAG,IAAI;GACf,MAAM,IACH,OAAQQ,gBAAiB,KAAK,WAAW,IACzCb,MAAM,YAAYa,gBAAgB,EAAE;IACtCP,OAAO,GAAG,IAAI;IACd;GACD,MAAM,IAAKN,MAAc,CAACc,UAAU,IAAI,IAAI,EAAE;IAC7CP,YAAY,GAAG,IAAI;GACpB,MAAM,IACH,OAAQQ,WAAY,KAAK,WAAW,IAAIf,MAAM,YAAYe,WAAW,EAAE;IACzEP,aAAa,GAAG,IAAI;GACrB,MAAM;IACL,MAAM,IAAIN,KAAK,CACX,6DAA6D,GAC7D,mEAAmE,GACnE,wDAAwD,GACxD,0DAA0D,GAC1D,WAAYF,MAAa,CAACgB,WAAW,CAACC,IAAI,EAAE,CAAC;;EAEnD;EACA;EACA,MAAMC,MAAM,GAAG3B,SAAS,CAACD,UAAU,EAAEH,MAAM,CAACgC,WAAW,CAAC;EACxD,IAAID,MAAM,IAAI,IAAI,EAAE;IAClB,MAAME,MAAM,GAAqB;MAACpB;IAAM,CAAC;IACzC,MAAMqB,KAAK,GAAoB;MAACpB;IAAW,CAAC;IAC5C,OAAOd,MAAM,CAACmC,SAAS,CACnBhC,UAAU,EAAE8B,MAAmC,EAC/CC,KAAgC,CAAC;;EAGvC,MAAM,CAACE,KAAK,EAAEC,MAAM,CAAC,GAAGnB,OAAO,GAC3B,CACGL,MAA2B,CAACyB,UAAU,EACtCzB,MAA2B,CAAC0B,WAAW,CACzC,GACD,CAAC1B,MAAM,CAACuB,KAAK,EAAEvB,MAAM,CAACwB,MAAM,CAAC;EACjC,IAAIG,IAAkC;EAEtC,IAAIpB,YAAY,EAAE;IAChBoB,IAAI;IACA;IACC3B,MAAc,CAACc,UAAU,CAAC,IAAI,CAAC,CAACc,YAAY,CAAC,CAAC,EAAE,CAAC,EAAEL,KAAK,EAAEC,MAAM,CAAC,CAACf,IAAI;GAC5E,MAAM,IAAIL,WAAW,IAAID,WAAW,EAAE;IACrCwB,IAAI,GAAI3B,MAAgC,CAACS,IAAI;GAC9C,MAAM,IAAIH,OAAO,IAAID,OAAO,IAAIG,aAAa,EAAE;IAC9C,IAAIX,mBAAmB,IAAI,IAAI,EAAE;MAC/B,IAAI,OAAOgC,QAAQ,KAAK,WAAW,EAAE;QACnC,IAAI,OAAOC,eAAe,KAAK,WAAW,IACtC,OAAOC,iCAAiC,KAAK,WAAW,EAAE;UAC5D;UACAlC,mBAAmB,GAAG,IAAIiC,eAAe,CAAC,CAAC,EAAE,CAAC,CAAC,CAAChB,UAAU,CAAC,IAAI,CAAC;SACjE,MAAM;UACL,MAAM,IAAIZ,KAAK,CACX,yCAAyC,GACzC,+DAA+D,CAAC;;OAEvE,MAAM;QACLL,mBAAmB,GAAGgC,QAAQ,CAACG,aAAa,CAAC,QAAQ,CAAC,CAAClB,UAAU,CAC7D,IAAI,EAAE;UAACmB,kBAAkB,EAAE;QAAI,CAAC,CAAC;;;IAGzCpC,mBAAmB,CAACqC,MAAM,CAACX,KAAK,GAAGA,KAAK;IACxC1B,mBAAmB,CAACqC,MAAM,CAACV,MAAM,GAAGA,MAAM;IAC1C3B,mBAAmB,CAACsC,SAAS,CACzBnC,MAA0B,EAAE,CAAC,EAAE,CAAC,EAAEuB,KAAK,EAAEC,MAAM,CAAC;IACpDG,IAAI,GAAG9B,mBAAmB,CAAC+B,YAAY,CAAC,CAAC,EAAE,CAAC,EAAEL,KAAK,EAAEC,MAAM,CAAC,CAACf,IAAI;;EAEnE,IAAI2B,MAAkB;EACtB,IAAInC,WAAW,KAAK,CAAC,EAAE;IACrBmC,MAAM,GAAG,IAAIC,UAAU,CAACV,IAAI,CAAC;GAC9B,MAAM;IACL,MAAMW,SAAS,GAAGf,KAAK,GAAGC,MAAM;IAChCY,MAAM,GAAG,IAAIC,UAAU,CAACC,SAAS,GAAGrC,WAAW,CAAC;IAChD,KAAK,IAAIsC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,SAAS,EAAEC,CAAC,EAAE,EAAE;MAClC,KAAK,IAAIC,OAAO,GAAG,CAAC,EAAEA,OAAO,GAAGvC,WAAW,EAAE,EAAEuC,OAAO,EAAE;QACtDJ,MAAM,CAACG,CAAC,GAAGtC,WAAW,GAAGuC,OAAO,CAAC,GAAGb,IAAI,CAACY,CAAC,GAAG,CAAC,GAAGC,OAAO,CAAC;;;;EAI/D,MAAMC,QAAQ,GAA6B,CAACjB,MAAM,EAAED,KAAK,EAAEtB,WAAW,CAAC;EACvE,OAAOL,QAAQ,CAACwC,MAAM,EAAEK,QAAQ,EAAE,OAAO,CAAC;AAC5C;AAEA;AACA;AACA,SAAStC,WAAWA,CAACH,MAEW;EAC9B,OAAQA,MAAM,IAAI,IAAI,IAAOA,MAAoB,CAACS,IAAI,YAAYC,UAAW;AAC/E;AAEA,SAASgC,2BAA2BA,CAAA;EAClC,OAAO,OAAOC,MAAM,KAAK,WAAW,IAChC,OAAQ5B,WAAY,KAAK,WAAW,IACpC4B,MAAM,CAACC,cAAc,CAAC,mBAAmB,CAAC;AAChD;AAEA,SAASC,gBAAgBA,CAAC7C,MAC8C;EACtE,OAAOA,MAAM,IAAI,IAAI,IAAIA,MAAM,CAACuB,KAAK,KAAK,CAAC,IAAIvB,MAAM,CAACwB,MAAM,KAAK,CAAC;AACpE;AAEA,SAASsB,0BAA0BA,CAAC9C,MAE4B;EAC9D,OAAO0C,2BAA2B,EAAE,IAAI,EAAE1C,MAAM,YAAYe,WAAW,CAAC,IACpE8B,gBAAgB,CAAC7C,MAAM,CAAC,IAAI,CAACG,WAAW,CAACH,MAAM,CAAC;AACtD;AAEA;;;;;;;;;;;;;;;;;;;;;;;;;;AA0BA,OAAO,eAAe+C,eAAeA,CACjC/C,MAC4B,EAC5BC,WAAW,GAAG,CAAC;EACjB,IAAImB,MAAM,GACyB,IAAI;EAEvC;EACA;EACA,IAAIhC,GAAG,EAAE,CAAC4D,OAAO,CAAC,qBAAqB,CAAC,IACpCF,0BAA0B,CAAC9C,MAAM,CAAC,EAAE;IACtC;IACA;IACA,IAAIiD,WAAW;IAEf,IAAI;MACF;MACA;MACA;MACA;MACAA,WAAW,GAAG,MAAOC,iBAAyB,CAC1ClD,MAA2B,EAAE;QAACmD,gBAAgB,EAAE;MAAM,CAAC,CAAC;KAC7D,CAAC,OAAOC,CAAC,EAAE;MACVH,WAAW,GAAG,IAAI;;IAGpB;IACA;IACA;IACA;IACA;IACA;IACA,IAAIA,WAAW,IAAI,IAAI,IAAIA,WAAW,CAAC1B,KAAK,KAAKvB,MAAM,CAACuB,KAAK,IACzD0B,WAAW,CAACzB,MAAM,KAAKxB,MAAM,CAACwB,MAAM,EAAE;MACxCJ,MAAM,GAAG6B,WAAW;KACrB,MAAM;MACL7B,MAAM,GAAGpB,MAAM;;GAElB,MAAM;IACLoB,MAAM,GAAGpB,MAAM;;EAGjB,OAAOD,WAAW,CAACqB,MAAM,EAAEnB,WAAW,CAAC;AACzC;AAEA,SAASoD,iBAAiBA,CAACC,GAAsB;EAC/C,IAAIA,GAAG,CAACC,IAAI,KAAK,CAAC,IAAID,GAAG,CAACC,IAAI,KAAK,CAAC,EAAE;IACpC,MAAM,IAAIrD,KAAK,CACX,wDAAwDoD,GAAG,CAACC,IAAI,GAAG,CAAC;;EAE1E,MAAMC,KAAK,GAAGF,GAAG,CAACC,IAAI,KAAK,CAAC,GAAG,CAAC,GAAGD,GAAG,CAACG,KAAK,CAAC,CAAC,CAAC;EAE/C,IAAID,KAAK,GAAG,CAAC,IAAIA,KAAK,KAAK,CAAC,EAAE;IAC5B,MAAM,IAAItD,KAAK,CACX,uCAAuC,GACvC,qBAAqBsD,KAAK,EAAE,CAAC;;EAGnC,IAAIF,GAAG,CAACI,KAAK,KAAK,SAAS,IAAIJ,GAAG,CAACI,KAAK,KAAK,OAAO,EAAE;IACpD,MAAM,IAAIxD,KAAK,CACX,kCAAkCoD,GAAG,CAACI,KAAK,GAAG,GAC9C,uCAAuC,CAAC;;AAEhD;AAEA,SAASC,oBAAoBA,CAACC,YAA0B;EACtD,MAAMC,KAAK,GAAG,CAAAD,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAGC,KAAK,KAAI,CAAC;EACvC,IAAIA,KAAK,GAAG,CAAC,IAAIA,KAAK,GAAG,CAAC,EAAE;IAC1B,MAAM,IAAI3D,KAAK,CAAC,eAAe2D,KAAK,qCAAqC,CAAC;;AAE9E;AAEA;;;;;;;;;;;;;;;;;;;;AAoBA,OAAO,eAAeC,QAAQA,CAC1BR,GAAiC,EACjCpB,MAA0B;EAC5B,IAAI6B,IAAI,GAAGtE,eAAe,CAAC6D,GAAG,EAAE,KAAK,EAAE,UAAU,CAAC;EAClD,IAAI,EAAEA,GAAG,YAAY9D,MAAM,CAAC,EAAE;IAC5B;IACA,MAAMwE,iBAAiB,GAAGD,IAAI;IAC9BA,IAAI,GAAGrE,IAAI,CAACsE,iBAAiB,EAAE,OAAO,CAAC;IACvCA,iBAAiB,CAACC,OAAO,EAAE;;EAE7BZ,iBAAiB,CAACU,IAAI,CAAC;EAEvB,MAAM,CAACvC,MAAM,EAAED,KAAK,CAAC,GAAGwC,IAAI,CAACN,KAAK,CAACS,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;EAC9C,MAAMV,KAAK,GAAGO,IAAI,CAACR,IAAI,KAAK,CAAC,GAAG,CAAC,GAAGQ,IAAI,CAACN,KAAK,CAAC,CAAC,CAAC;EACjD,MAAMhD,IAAI,GAAG,MAAMsD,IAAI,CAACtD,IAAI,EAAE;EAC9B,MAAM0D,UAAU,GAAGJ,IAAI,CAACL,KAAK,KAAK,SAAS,GAAG,GAAG,GAAG,CAAC;EACrD,MAAMU,KAAK,GAAG,IAAIC,iBAAiB,CAAC9C,KAAK,GAAGC,MAAM,GAAG,CAAC,CAAC;EAEvD,KAAK,IAAIe,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGf,MAAM,GAAGD,KAAK,EAAE,EAAEgB,CAAC,EAAE;IACvC,MAAM+B,IAAI,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,CAAC;IAE3B,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGf,KAAK,EAAEe,CAAC,EAAE,EAAE;MAC9B,MAAMC,KAAK,GAAG/D,IAAI,CAAC8B,CAAC,GAAGiB,KAAK,GAAGe,CAAC,CAAC;MAEjC,IAAIR,IAAI,CAACL,KAAK,KAAK,SAAS,EAAE;QAC5B,IAAIc,KAAK,GAAG,CAAC,IAAIA,KAAK,GAAG,CAAC,EAAE;UAC1B,MAAM,IAAItE,KAAK,CACX,oDAAoD,GACpD,iCAAiCsE,KAAK,GAAG,CAAC;;OAEjD,MAAM,IAAIT,IAAI,CAACL,KAAK,KAAK,OAAO,EAAE;QACjC,IAAIc,KAAK,GAAG,CAAC,IAAIA,KAAK,GAAG,GAAG,EAAE;UAC5B,MAAM,IAAItE,KAAK,CACX,kDAAkD,GAClD,mCAAmCsE,KAAK,GAAG,CAAC;;;MAIpD,IAAIhB,KAAK,KAAK,CAAC,EAAE;QACfc,IAAI,CAAC,CAAC,CAAC,GAAGE,KAAK,GAAGL,UAAU;QAC5BG,IAAI,CAAC,CAAC,CAAC,GAAGE,KAAK,GAAGL,UAAU;QAC5BG,IAAI,CAAC,CAAC,CAAC,GAAGE,KAAK,GAAGL,UAAU;OAC7B,MAAM;QACLG,IAAI,CAACC,CAAC,CAAC,GAAGC,KAAK,GAAGL,UAAU;;;IAIhC,MAAMM,CAAC,GAAGlC,CAAC,GAAG,CAAC;IACf6B,KAAK,CAACK,CAAC,GAAG,CAAC,CAAC,GAAGC,IAAI,CAACC,KAAK,CAACL,IAAI,CAAC,CAAC,CAAC,CAAC;IAClCF,KAAK,CAACK,CAAC,GAAG,CAAC,CAAC,GAAGC,IAAI,CAACC,KAAK,CAACL,IAAI,CAAC,CAAC,CAAC,CAAC;IAClCF,KAAK,CAACK,CAAC,GAAG,CAAC,CAAC,GAAGC,IAAI,CAACC,KAAK,CAACL,IAAI,CAAC,CAAC,CAAC,CAAC;IAClCF,KAAK,CAACK,CAAC,GAAG,CAAC,CAAC,GAAGC,IAAI,CAACC,KAAK,CAACL,IAAI,CAAC,CAAC,CAAC,CAAC;;EAGpC,IAAIpC,MAAM,IAAI,IAAI,EAAE;IAClB,IAAI,CAACpC,iBAAiB,EAAE;MACtB,MAAMoB,MAAM,GAAG3B,SAAS,CAACF,IAAI,EAAEF,MAAM,CAACgC,WAAW,CAAC;MAClD,IAAID,MAAM,IAAI,IAAI,EAAE;QAClB0D,OAAO,CAACC,IAAI,CACR,iEAAiE,GACjE,qCAAqC,CAAC;QAC1C/E,iBAAiB,GAAG,IAAI;;;IAI5BoC,MAAM,CAACX,KAAK,GAAGA,KAAK;IACpBW,MAAM,CAACV,MAAM,GAAGA,MAAM;IACtB,MAAMsD,GAAG,GAAG5C,MAAM,CAACpB,UAAU,CAAC,IAAI,CAAC;IACnC,MAAMiE,SAAS,GAAG,IAAIpE,SAAS,CAACyD,KAAK,EAAE7C,KAAK,EAAEC,MAAM,CAAC;IACrDsD,GAAG,CAACE,YAAY,CAACD,SAAS,EAAE,CAAC,EAAE,CAAC,CAAC;;EAEnC,IAAIhB,IAAI,KAAKT,GAAG,EAAE;IAChBS,IAAI,CAACE,OAAO,EAAE;;EAEhB,OAAOG,KAAK;AACd;AAEA;;;;;;;;;;;;;;;;;;;;AAoBA,OAAM,SAAUa,IAAIA,CAChBC,KAAmC,EAAEhD,MAAyB,EAC9DiD,OAAqB;EACvB,IAAIpB,IAAI,GAAGtE,eAAe,CAACyF,KAAK,EAAE,KAAK,EAAE,MAAM,CAAC;EAChD,IAAI,EAAEA,KAAK,YAAY1F,MAAM,CAAC,EAAE;IAC9B;IACA,MAAMwE,iBAAiB,GAAGD,IAAI;IAC9BA,IAAI,GAAGrE,IAAI,CAACsE,iBAAiB,EAAE,OAAO,CAAC;IACvCA,iBAAiB,CAACC,OAAO,EAAE;;EAE7BZ,iBAAiB,CAACU,IAAI,CAAC;EACvBJ,oBAAoB,CAACwB,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEvB,YAAY,CAAC;EAE3C,MAAMxC,MAAM,GAAe;IAAC8D,KAAK,EAAEnB;EAAI,CAAC;EACxC,MAAM1C,KAAK,GAAc;IAACa,MAAM;IAAEiD;EAAO,CAAC;EAC1ChG,MAAM,CAACmC,SAAS,CACZjC,IAAI,EAAE+B,MAAmC,EACzCC,KAAgC,CAAC;AACvC;AAEA,OAAO,MAAM+D,UAAU,GAAG,eAAgBzF,EAAE,CAAC;EAACI;AAAW,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}