{"ast": null, "code": "/**\n * @license\n * Copyright 2018 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { KernelBackend } from './backends/backend';\nimport { Environment, setEnvironmentGlobal } from './environment';\nimport { getGlobalNamespace } from './global_util';\nimport { Add, Cast, Identity } from './kernel_names';\nimport { getGradient, getKernel, getKernelsForBackend } from './kernel_registry';\nimport * as log from './log';\nimport { Profiler } from './profiler';\nimport { backpropagateGradients, getFilteredNodesXToY } from './tape';\nimport { setTensorTracker, Tensor, Variable } from './tensor';\nimport { getTensorsInContainer } from './tensor_util';\nimport * as util from './util';\nimport { bytesFromStringArray, makeOnesTypedArray, now, sizeFromShape } from './util';\nfunction isRegisteredKernelInvocation(kernelInvocation) {\n  return kernelInvocation.kernelName != null;\n}\nclass EngineState {\n  constructor() {\n    // Public since optimizers will use it.\n    this.registeredVariables = {};\n    this.nextTapeNodeId = 0;\n    this.numBytes = 0;\n    this.numTensors = 0;\n    this.numStringTensors = 0;\n    this.numDataBuffers = 0;\n    // Number of nested tf.grad() statements when computing higher-order\n    // gradients. E.g. `1` for first-order gradients and `2` for second-order\n    // gradients. Used to track if the tape should be removed after a backprop.\n    this.gradientDepth = 0;\n    // Number of nested kernel calls. When kernel depth is greater than 1, we turn\n    // off the tape.\n    this.kernelDepth = 0;\n    this.scopeStack = [];\n    /**\n     * Keeps track of the number of data moves during a kernel execution. We\n     * maintain a stack since kernels can call other kernels, recursively.\n     */\n    this.numDataMovesStack = [];\n    this.nextScopeId = 0;\n    this.tensorInfo = new WeakMap();\n    this.profiling = false;\n    this.activeProfile = {\n      newBytes: 0,\n      newTensors: 0,\n      peakBytes: 0,\n      kernels: [],\n      result: null,\n      get kernelNames() {\n        return Array.from(new Set(this.kernels.map(k => k.name)));\n      }\n    };\n  }\n  dispose() {\n    for (const variableName in this.registeredVariables) {\n      this.registeredVariables[variableName].dispose();\n    }\n  }\n}\nclass Engine {\n  constructor(ENV) {\n    this.ENV = ENV;\n    this.registry = {};\n    this.registryFactory = {};\n    this.pendingBackendInitId = 0;\n    this.state = new EngineState();\n  }\n  async ready() {\n    if (this.pendingBackendInit != null) {\n      return this.pendingBackendInit.then(() => {});\n    }\n    if (this.backendInstance != null) {\n      return;\n    }\n    const sortedBackends = this.getSortedBackends();\n    for (let i = 0; i < sortedBackends.length; i++) {\n      const backendName = sortedBackends[i];\n      const success = await this.initializeBackend(backendName).success;\n      if (success) {\n        await this.setBackend(backendName);\n        return;\n      }\n    }\n    throw new Error(`Could not initialize any backends, all backend initializations ` + `failed.`);\n  }\n  get backend() {\n    if (this.pendingBackendInit != null) {\n      throw new Error(`Backend '${this.backendName}' has not yet been initialized. Make ` + `sure to await tf.ready() or await tf.setBackend() before calling ` + `other methods`);\n    }\n    if (this.backendInstance == null) {\n      const {\n        name,\n        asyncInit\n      } = this.initializeBackendsAndReturnBest();\n      if (asyncInit) {\n        throw new Error(`The highest priority backend '${name}' has not yet been ` + `initialized. Make sure to await tf.ready() or ` + `await tf.setBackend() before calling other methods`);\n      }\n      this.setBackend(name);\n    }\n    return this.backendInstance;\n  }\n  backendNames() {\n    return Object.keys(this.registryFactory);\n  }\n  findBackend(backendName) {\n    if (!(backendName in this.registry)) {\n      // If the backend hasn't been initialized but we have a registry entry for\n      // it, initialize it and return it.\n      if (backendName in this.registryFactory) {\n        const {\n          asyncInit\n        } = this.initializeBackend(backendName);\n        if (asyncInit) {\n          // Backend is not ready yet.\n          return null;\n        }\n      } else {\n        return null;\n      }\n    }\n    return this.registry[backendName];\n  }\n  findBackendFactory(backendName) {\n    if (!(backendName in this.registryFactory)) {\n      return null;\n    }\n    return this.registryFactory[backendName].factory;\n  }\n  registerBackend(backendName, factory, priority = 1) {\n    if (backendName in this.registryFactory) {\n      log.warn(`${backendName} backend was already registered. ` + `Reusing existing backend factory.`);\n      return false;\n    }\n    this.registryFactory[backendName] = {\n      factory,\n      priority\n    };\n    return true;\n  }\n  async setBackend(backendName) {\n    if (this.registryFactory[backendName] == null) {\n      throw new Error(`Backend name '${backendName}' not found in registry`);\n    }\n    this.backendName = backendName;\n    if (this.registry[backendName] == null) {\n      this.backendInstance = null;\n      const {\n        success,\n        asyncInit\n      } = this.initializeBackend(backendName);\n      const result = asyncInit ? await success : success;\n      if (!result) {\n        return false;\n      }\n    }\n    this.backendInstance = this.registry[backendName];\n    this.setupRegisteredKernels();\n    // Reset the profiler.\n    this.profiler = new Profiler(this.backendInstance);\n    return true;\n  }\n  setupRegisteredKernels() {\n    const kernels = getKernelsForBackend(this.backendName);\n    kernels.forEach(kernel => {\n      if (kernel.setupFunc != null) {\n        kernel.setupFunc(this.backendInstance);\n      }\n    });\n  }\n  disposeRegisteredKernels(backendName) {\n    const kernels = getKernelsForBackend(backendName);\n    kernels.forEach(kernel => {\n      if (kernel.disposeFunc != null) {\n        kernel.disposeFunc(this.registry[backendName]);\n      }\n    });\n  }\n  /**\n   * Initializes a backend by looking up the backend name in the factory\n   * registry and calling the factory method. Returns a boolean representing\n   * whether the initialization of the backend succeeded. Throws an error if\n   * there is no backend in the factory registry.\n   */\n  initializeBackend(backendName) {\n    const registryFactoryEntry = this.registryFactory[backendName];\n    if (registryFactoryEntry == null) {\n      throw new Error(`Cannot initialize backend ${backendName}, no registration found.`);\n    }\n    try {\n      const backend = registryFactoryEntry.factory();\n      /* Test if the factory returns a promise.\n      Done in a more liberal way than\n      previous 'Promise.resolve(backend)===backend'\n      as we needed to account for custom Promise\n      implementations (e.g. Angular) */\n      if (backend && !(backend instanceof KernelBackend) && typeof backend.then === 'function') {\n        const promiseId = ++this.pendingBackendInitId;\n        const success = backend.then(backendInstance => {\n          // Outdated promise. Another backend was set in the meantime.\n          if (promiseId < this.pendingBackendInitId) {\n            return false;\n          }\n          this.registry[backendName] = backendInstance;\n          this.pendingBackendInit = null;\n          return true;\n        }).catch(err => {\n          // Outdated promise. Another backend was set in the meantime.\n          if (promiseId < this.pendingBackendInitId) {\n            return false;\n          }\n          this.pendingBackendInit = null;\n          log.warn(`Initialization of backend ${backendName} failed`);\n          log.warn(err.stack || err.message);\n          return false;\n        });\n        this.pendingBackendInit = success;\n        return {\n          success,\n          asyncInit: true\n        };\n      } else {\n        this.registry[backendName] = backend;\n        return {\n          success: true,\n          asyncInit: false\n        };\n      }\n    } catch (err) {\n      log.warn(`Initialization of backend ${backendName} failed`);\n      log.warn(err.stack || err.message);\n      return {\n        success: false,\n        asyncInit: false\n      };\n    }\n  }\n  removeBackend(backendName) {\n    if (!(backendName in this.registryFactory)) {\n      throw new Error(`${backendName} backend not found in registry`);\n    }\n    if (this.backendName === backendName && this.pendingBackendInit != null) {\n      // There is a pending promise of the backend we want to remove. Make it\n      // obsolete.\n      this.pendingBackendInitId++;\n    }\n    if (backendName in this.registry) {\n      this.disposeRegisteredKernels(backendName);\n      this.registry[backendName].dispose();\n      delete this.registry[backendName];\n    }\n    delete this.registryFactory[backendName];\n    // Unset the backend if it is active.\n    if (this.backendName === backendName) {\n      this.pendingBackendInit = null;\n      this.backendName = null;\n      this.backendInstance = null;\n    }\n  }\n  getSortedBackends() {\n    if (Object.keys(this.registryFactory).length === 0) {\n      throw new Error('No backend found in registry.');\n    }\n    return Object.keys(this.registryFactory).sort((a, b) => {\n      // Highest priority comes first.\n      return this.registryFactory[b].priority - this.registryFactory[a].priority;\n    });\n  }\n  initializeBackendsAndReturnBest() {\n    const sortedBackends = this.getSortedBackends();\n    for (let i = 0; i < sortedBackends.length; i++) {\n      const backendName = sortedBackends[i];\n      const {\n        success,\n        asyncInit\n      } = this.initializeBackend(backendName);\n      if (asyncInit || success) {\n        return {\n          name: backendName,\n          asyncInit\n        };\n      }\n    }\n    throw new Error(`Could not initialize any backends, all backend initializations ` + `failed.`);\n  }\n  moveData(backend, dataId) {\n    const info = this.state.tensorInfo.get(dataId);\n    const srcBackend = info.backend;\n    const values = this.readSync(dataId);\n    const refCount = srcBackend.refCount(dataId);\n    // Delete the tensor from the old backend and move it to the new\n    // backend.\n    srcBackend.disposeData(dataId, true);\n    info.backend = backend;\n    backend.move(dataId, values, info.shape, info.dtype, refCount);\n    if (this.shouldCheckForMemLeaks()) {\n      // Track the number of moves during a kernel execution to correctly\n      // detect memory leaks.\n      this.state.numDataMovesStack[this.state.numDataMovesStack.length - 1]++;\n    }\n  }\n  tidy(nameOrFn, fn) {\n    let name = null;\n    if (fn == null) {\n      // Called with only 1 argument.\n      if (typeof nameOrFn !== 'function') {\n        throw new Error('Please provide a function to tidy()');\n      }\n      fn = nameOrFn;\n    } else {\n      // Called with 2 arguments.\n      if (typeof nameOrFn !== 'string' && !(nameOrFn instanceof String)) {\n        throw new Error('When calling with two arguments, the first argument ' + 'to tidy() must be a string');\n      }\n      if (typeof fn !== 'function') {\n        throw new Error('When calling with two arguments, the 2nd argument ' + 'to tidy() must be a function');\n      }\n      name = nameOrFn;\n      // TODO(nsthorat,smilkov): Do operation logging and performance\n      // profiling.\n    }\n    let result;\n    return this.scopedRun(() => this.startScope(name), () => this.endScope(result), () => {\n      result = fn();\n      if (result instanceof Promise) {\n        console.error('Cannot return a Promise inside of tidy.');\n      }\n      return result;\n    });\n  }\n  scopedRun(start, end, f) {\n    start();\n    try {\n      const res = f();\n      end();\n      return res;\n    } catch (ex) {\n      end();\n      throw ex;\n    }\n  }\n  nextTensorId() {\n    return Engine.nextTensorId++;\n  }\n  nextVariableId() {\n    return Engine.nextVariableId++;\n  }\n  /**\n   * This method is called instead of the public-facing tensor.clone() when\n   * saving a tensor for backwards pass. It makes sure to add the clone\n   * operation to the tape regardless of being called inside a kernel\n   * execution.\n   */\n  clone(x) {\n    const y = ENGINE.runKernel(Identity, {\n      x\n    });\n    const inputs = {\n      x\n    };\n    const grad = dy => ({\n      x: () => {\n        const dtype = 'float32';\n        const gradInputs = {\n          x: dy\n        };\n        const attrs = {\n          dtype\n        };\n        return ENGINE.runKernel(Cast, gradInputs,\n        // tslint:disable-next-line: no-unnecessary-type-assertion\n        attrs);\n      }\n    });\n    const saved = [];\n    this.addTapeNode(this.state.activeScope.name, inputs, [y], grad, saved, {});\n    return y;\n  }\n  /**\n   * Execute a kernel with the given name and return the output tensor.\n   *\n   * @param kernelName The name of the kernel to execute.\n   * @param inputs A map of input names to tensors.\n   * @param attrs A map of attribute names to their values. An attribute is a\n   *     primitive (non-tensor) input to the kernel.\n   * @param inputsToSave A list of tensors, inputs to save for the backprop\n   *     computation.\n   * @param outputsToSave A list of booleans, specifying which output to save\n   *     for the backprop computation. These are booleans since the output\n   * tensors are not visible to the user.\n   */\n  runKernel(kernelName, inputs, attrs) {\n    if (this.backendName == null) {\n      // backend has not been initialized yet (backend initialization is lazy\n      // can be deferred until an op/ kernel is run).\n      // The below getter has side effects that will try to initialize the\n      // backend and set properties like this.backendName\n      // tslint:disable-next-line: no-unused-expression\n      this.backend;\n    }\n    const hasKernel = getKernel(kernelName, this.backendName) != null;\n    if (!hasKernel) {\n      throw new Error(`Kernel '${kernelName}' not registered for backend '${this.backendName}'`);\n    }\n    return this.runKernelFunc({\n      kernelName,\n      inputs,\n      attrs\n    });\n  }\n  shouldCheckForMemLeaks() {\n    return this.ENV.getBool('IS_TEST');\n  }\n  checkKernelForMemLeak(kernelName, numDataIdsBefore, outInfos) {\n    const numDataIdsAfter = this.backend.numDataIds();\n    // Count the number of data ids associated with the result of the kernel.\n    let numOutputDataIds = 0;\n    outInfos.forEach(info => {\n      // Complex numbers allocate 3 data ids, one for 'real', one for\n      // 'imaginary', and one for the container that holds the former two.\n      numOutputDataIds += info.dtype === 'complex64' ? 3 : 1;\n    });\n    // Account for the number of moves during kernel execution. A \"data move\"\n    // can happen in the middle of a kernel execution, placing a new (key,value)\n    // pair in the data storage. Since data moves have net zero effect (we\n    // always remove the data from the old backend), we have to cancel them out\n    // when detecting memory leaks.\n    const numMoves = this.state.numDataMovesStack[this.state.numDataMovesStack.length - 1];\n    const dataIdsLeaked = numDataIdsAfter - numDataIdsBefore - numOutputDataIds - numMoves;\n    if (dataIdsLeaked > 0) {\n      throw new Error(`Backend '${this.backendName}' has an internal memory leak ` + `(${dataIdsLeaked} data ids) after running '${kernelName}'`);\n    }\n  }\n  /**\n   * Internal helper method to execute a kernel Func\n   *\n   * Use `runKernel` to execute kernels from outside of engine.\n   */\n  runKernelFunc(kernelParams) {\n    let outputs;\n    let saved = [];\n    const isTapeOn = this.isTapeOn();\n    const startingBytecount = this.state.numBytes;\n    const startingNumTensors = this.state.numTensors;\n    if (this.shouldCheckForMemLeaks()) {\n      this.state.numDataMovesStack.push(0);\n    }\n    let kernelFunc;\n    if (this.backendName == null) {\n      // backend has not been initialized yet (backend initialization is lazy\n      // can be deferred until an op/ kernel is run).\n      // The below getter has side effects that will try to initialize the\n      // backend and set properties like this.backendName\n      // tslint:disable-next-line: no-unused-expression\n      this.backend;\n    }\n    let out;\n    const kernelOrScopeName = isRegisteredKernelInvocation(kernelParams) ? kernelParams.kernelName : this.state.activeScope != null ? this.state.activeScope.name : '';\n    // Create the kernelFunc from either a registered kernel OR passed in\n    // forward/backward functions (used by custom grad). In this context a\n    // kernelFunc wraps a kernel implementation with some bookkeeping.\n    if (isRegisteredKernelInvocation(kernelParams)) {\n      const {\n        kernelName,\n        inputs,\n        attrs\n      } = kernelParams;\n      if (this.backendName == null) {\n        // backend has not been initialized yet (backend initialization is lazy\n        // can be deferred until an op/ kernel is run).\n        // The below getter has side effects that will try to initialize the\n        // backend and set properties like this.backendName\n        // tslint:disable-next-line: no-unused-expression\n        this.backend;\n      }\n      const kernel = getKernel(kernelName, this.backendName);\n      util.assert(kernel != null, () => `Cannot find registered kernel '${kernelName}' for backend '${this.backendName}'`);\n      kernelFunc = () => {\n        const numDataIdsBefore = this.backend.numDataIds();\n        out = kernel.kernelFunc({\n          inputs,\n          attrs,\n          backend: this.backend\n        });\n        const outInfos = Array.isArray(out) ? out : [out];\n        if (this.shouldCheckForMemLeaks()) {\n          this.checkKernelForMemLeak(kernelName, numDataIdsBefore, outInfos);\n        }\n        const outTensors = outInfos.map(outInfo => {\n          // todo (yassogba) remove this option (Tensor) when node backend\n          // methods have been modularized and they all return tensorInfo.\n          // TensorInfos do not have a rank attribute.\n          if (outInfo.rank != null) {\n            return outInfo;\n          }\n          return this.makeTensorFromTensorInfo(outInfo);\n        });\n        // Save any required inputs and outputs.\n        // Do not save unless we are recording to the tape. Otherwise it would\n        // cause a mem leak since there would be no backprop for these tensors\n        // (which would otherwise dispose them).\n        if (isTapeOn) {\n          const tensorsToSave = this.getTensorsForGradient(kernelName, inputs, outTensors);\n          saved = this.saveTensorsForBackwardMode(tensorsToSave);\n        }\n        return outTensors;\n      };\n    } else {\n      const {\n        forwardFunc\n      } = kernelParams;\n      // Running a customGrad op.\n      const saveFunc = tensors => {\n        // Do not save unless we are recording to the tape. Otherwise it would\n        // cause a mem leak since we would never run backprop, which disposes\n        // the kept tensors.\n        if (!isTapeOn) {\n          return;\n        }\n        saved = tensors.map(tensor => this.keep(this.clone(tensor)));\n      };\n      kernelFunc = () => {\n        const numDataIdsBefore = this.backend.numDataIds();\n        out = this.tidy(() => forwardFunc(this.backend, saveFunc));\n        const outs = Array.isArray(out) ? out : [out];\n        if (this.shouldCheckForMemLeaks()) {\n          // Scope name is used to print a more helpful error message if needed.\n          this.checkKernelForMemLeak(kernelOrScopeName, numDataIdsBefore, outs);\n        }\n        return outs;\n      };\n    }\n    //\n    // Run the kernelFunc. Optionally profiling it.\n    //\n    const {\n      inputs,\n      attrs\n    } = kernelParams;\n    const backwardsFunc = isRegisteredKernelInvocation(kernelParams) ? null : kernelParams.backwardsFunc;\n    let kernelProfile;\n    this.scopedRun(\n    // Stop recording to a tape when running a kernel.\n    () => this.state.kernelDepth++, () => this.state.kernelDepth--, () => {\n      if (!this.ENV.getBool('DEBUG') && !this.state.profiling) {\n        outputs = kernelFunc();\n      } else {\n        kernelProfile = this.profiler.profileKernel(kernelOrScopeName, inputs, () => kernelFunc());\n        if (this.ENV.getBool('DEBUG')) {\n          this.profiler.logKernelProfile(kernelProfile);\n        }\n        outputs = kernelProfile.outputs;\n      }\n    });\n    if (isTapeOn) {\n      this.addTapeNode(kernelOrScopeName, inputs, outputs, backwardsFunc, saved, attrs);\n    }\n    if (this.state.profiling) {\n      this.state.activeProfile.kernels.push({\n        name: kernelOrScopeName,\n        bytesAdded: this.state.numBytes - startingBytecount,\n        totalBytesSnapshot: this.state.numBytes,\n        tensorsAdded: this.state.numTensors - startingNumTensors,\n        totalTensorsSnapshot: this.state.numTensors,\n        inputShapes: Object.keys(inputs).map(key => inputs[key] != null ? inputs[key].shape : null),\n        outputShapes: outputs.map(item => item.shape),\n        kernelTimeMs: kernelProfile.timeMs,\n        extraInfo: kernelProfile.extraInfo\n      });\n    }\n    return Array.isArray(out) ? outputs : outputs[0];\n  }\n  /**\n   * Saves tensors used in forward mode for use in backward mode.\n   *\n   * @param tensors the list of tensors to save.\n   */\n  saveTensorsForBackwardMode(tensors) {\n    const saved = tensors.map(tensor => this.keep(this.clone(tensor)));\n    return saved;\n  }\n  /**\n   * Returns a list of tensors to save for a given gradient calculation.\n   *\n   * @param kernelName name of kernel to look up gradient for.\n   * @param inputs a map of input tensors.\n   * @param outputs an array of output tensors from forward mode of kernel.\n   */\n  getTensorsForGradient(kernelName, inputs, outputs) {\n    const gradConfig = getGradient(kernelName);\n    if (gradConfig != null) {\n      const inputsToSave = gradConfig.inputsToSave || [];\n      const outputsToSave = gradConfig.outputsToSave || [];\n      // If saveAllInputs is true, all inputs will be saved. Otherwise, inputs\n      // specified in inputsToSave will be saved.\n      let inputTensorsToSave;\n      if (gradConfig.saveAllInputs) {\n        util.assert(Array.isArray(inputs), () => 'saveAllInputs is true, expected inputs to be an array.');\n        inputTensorsToSave = Object.keys(inputs).map(key => inputs[key]);\n      } else {\n        inputTensorsToSave = inputsToSave.map(inputName => inputs[inputName]);\n      }\n      const outputTensorsToSave = outputs.filter((_, i) => outputsToSave[i]);\n      return inputTensorsToSave.concat(outputTensorsToSave);\n    }\n    // We return an empty list rather than throw an error because the kernel we\n    // are looking up may not actually be relevant to backproping through the\n    // overall function\n    //\n    // See 'does not error if irrelevant (pruned) ops are missing grads' test\n    // in gradients_test.ts for an example.\n    return [];\n  }\n  /**\n   * Internal method used by public APIs for tensor creation. Makes a new\n   * tensor with the provided shape, dtype and values. It always\n   * creates a new data id and writes the values to the underlying backend.\n   */\n  makeTensor(values, shape, dtype, backend) {\n    if (values == null) {\n      throw new Error('Values passed to engine.makeTensor() are null');\n    }\n    dtype = dtype || 'float32';\n    backend = backend || this.backend;\n    let backendVals = values;\n    if (dtype === 'string' && util.isString(values[0])) {\n      backendVals = values.map(d => util.encodeString(d));\n    }\n    const dataId = backend.write(backendVals, shape, dtype);\n    const t = new Tensor(shape, dtype, dataId, this.nextTensorId());\n    this.trackTensor(t, backend);\n    // Count bytes for string tensors.\n    if (dtype === 'string') {\n      const info = this.state.tensorInfo.get(dataId);\n      const newBytes = bytesFromStringArray(backendVals);\n      this.state.numBytes += newBytes - info.bytes;\n      info.bytes = newBytes;\n    }\n    return t;\n  }\n  /**\n   * Internal method used by backends. Makes a new tensor\n   * that is a wrapper around an existing data id. It doesn't create\n   * a new data id, only increments the ref count used in memory tracking.\n   * @deprecated\n   */\n  makeTensorFromDataId(dataId, shape, dtype, backend) {\n    dtype = dtype || 'float32';\n    const tensorInfo = {\n      dataId,\n      shape,\n      dtype\n    };\n    return this.makeTensorFromTensorInfo(tensorInfo, backend);\n  }\n  /**\n   * Internal method used by backends. Makes a new tensor that is a wrapper\n   * around an existing data id in TensorInfo. It doesn't create a new data id,\n   * only increments the ref count used in memory tracking.\n   */\n  makeTensorFromTensorInfo(tensorInfo, backend) {\n    const {\n      dataId,\n      shape,\n      dtype\n    } = tensorInfo;\n    const t = new Tensor(shape, dtype, dataId, this.nextTensorId());\n    this.trackTensor(t, backend);\n    return t;\n  }\n  makeVariable(initialValue, trainable = true, name, dtype) {\n    name = name || this.nextVariableId().toString();\n    if (dtype != null && dtype !== initialValue.dtype) {\n      initialValue = initialValue.cast(dtype);\n    }\n    const v = new Variable(initialValue, trainable, name, this.nextTensorId());\n    if (this.state.registeredVariables[v.name] != null) {\n      throw new Error(`Variable with name ${v.name} was already registered`);\n    }\n    this.state.registeredVariables[v.name] = v;\n    this.incRef(v, this.backend);\n    return v;\n  }\n  trackTensor(a, backend) {\n    this.state.numTensors++;\n    if (a.dtype === 'string') {\n      this.state.numStringTensors++;\n    }\n    // Bytes for complex numbers are counted by their components. Bytes for\n    // string tensors are counted when writing values.\n    let bytes = 0;\n    if (a.dtype !== 'complex64' && a.dtype !== 'string') {\n      bytes = a.size * util.bytesPerElement(a.dtype);\n    }\n    this.state.numBytes += bytes;\n    if (!this.state.tensorInfo.has(a.dataId)) {\n      this.state.numDataBuffers++;\n      this.state.tensorInfo.set(a.dataId, {\n        backend: backend || this.backend,\n        dtype: a.dtype,\n        shape: a.shape,\n        bytes\n      });\n    }\n    if (!(a instanceof Variable)) {\n      this.track(a);\n    }\n  }\n  // Track the tensor by dataId and increase the refCount for the dataId in the\n  // backend.\n  // TODO(pyu10055): This is currently used by makeVariable method, to increase\n  // refCount on the backend for the dataId. It can potentially be replaced with\n  // Identity op indead of calling backend directly.\n  incRef(a, backend) {\n    this.trackTensor(a, backend);\n    this.backend.incRef(a.dataId);\n  }\n  removeDataId(dataId, backend) {\n    if (this.state.tensorInfo.has(dataId) && this.state.tensorInfo.get(dataId).backend === backend) {\n      this.state.tensorInfo.delete(dataId);\n      this.state.numDataBuffers--;\n    }\n  }\n  disposeTensor(a) {\n    if (!this.state.tensorInfo.has(a.dataId)) {\n      return;\n    }\n    const info = this.state.tensorInfo.get(a.dataId);\n    this.state.numTensors--;\n    if (a.dtype === 'string') {\n      this.state.numStringTensors--;\n      this.state.numBytes -= info.bytes;\n    }\n    // Don't count bytes for complex numbers as they are counted by their\n    // components.\n    if (a.dtype !== 'complex64' && a.dtype !== 'string') {\n      const bytes = a.size * util.bytesPerElement(a.dtype);\n      this.state.numBytes -= bytes;\n    }\n    // Remove the reference to dataId if backend dispose the data successfully\n    if (info.backend.disposeData(a.dataId)) {\n      this.removeDataId(a.dataId, info.backend);\n    }\n    // TODO(nsthorat): Construct an error and save the stack trace for\n    // debugging when in debug mode. Creating a stack trace is too expensive\n    // to do unconditionally.\n  }\n  disposeVariables() {\n    for (const varName in this.state.registeredVariables) {\n      const v = this.state.registeredVariables[varName];\n      this.disposeVariable(v);\n    }\n  }\n  disposeVariable(v) {\n    this.disposeTensor(v);\n    if (this.state.registeredVariables[v.name] != null) {\n      delete this.state.registeredVariables[v.name];\n    }\n  }\n  memory() {\n    const info = this.backend.memory();\n    info.numTensors = this.state.numTensors;\n    info.numDataBuffers = this.state.numDataBuffers;\n    info.numBytes = this.state.numBytes;\n    if (this.state.numStringTensors > 0) {\n      info.unreliable = true;\n      if (info.reasons == null) {\n        info.reasons = [];\n      }\n      info.reasons.push('Memory usage by string tensors is approximate ' + '(2 bytes per character)');\n    }\n    return info;\n  }\n  async profile(query) {\n    this.state.profiling = true;\n    const startBytes = this.state.numBytes;\n    const startNumTensors = this.state.numTensors;\n    this.state.activeProfile.kernels = [];\n    this.state.activeProfile.result = await query();\n    this.state.profiling = false;\n    this.state.activeProfile.peakBytes = Math.max(...this.state.activeProfile.kernels.map(d => d.totalBytesSnapshot));\n    this.state.activeProfile.newBytes = this.state.numBytes - startBytes;\n    this.state.activeProfile.newTensors = this.state.numTensors - startNumTensors;\n    for (const kernel of this.state.activeProfile.kernels) {\n      kernel.kernelTimeMs = await kernel.kernelTimeMs;\n      kernel.extraInfo = await kernel.extraInfo;\n    }\n    return this.state.activeProfile;\n  }\n  isTapeOn() {\n    return this.state.gradientDepth > 0 && this.state.kernelDepth === 0;\n  }\n  addTapeNode(kernelName, inputs, outputs, gradientsFunc, saved, attrs) {\n    const tapeNode = {\n      id: this.state.nextTapeNodeId++,\n      kernelName,\n      inputs,\n      outputs,\n      saved\n    };\n    const gradConfig = getGradient(kernelName);\n    if (gradConfig != null) {\n      gradientsFunc = gradConfig.gradFunc;\n    }\n    if (gradientsFunc != null) {\n      tapeNode.gradient = dys => {\n        // TODO(smilkov): To optimize back-prop, pass dys that are not used in\n        // the backprop graph to the user as null instead of zeros\n        dys = dys.map((dy, i) => {\n          if (dy == null) {\n            const output = outputs[i];\n            const vals = util.makeZerosTypedArray(output.size, output.dtype);\n            return this.makeTensor(vals, output.shape, output.dtype);\n          }\n          return dy;\n        });\n        // Grad functions of ops with single outputs expect a dy, while ops\n        // with multiple outputs expect dys (array of dy).\n        return gradientsFunc(dys.length > 1 ? dys : dys[0], saved, attrs);\n      };\n    }\n    this.state.activeTape.push(tapeNode);\n  }\n  keep(result) {\n    result.kept = true;\n    return result;\n  }\n  startTape() {\n    if (this.state.gradientDepth === 0) {\n      this.state.activeTape = [];\n    }\n    this.state.gradientDepth++;\n  }\n  endTape() {\n    this.state.gradientDepth--;\n  }\n  /**\n   * Start a scope. Use this with endScope() to achieve the same functionality\n   * as scope() without the need for a function closure.\n   */\n  startScope(name) {\n    const scopeInfo = {\n      track: [],\n      name: 'unnamed scope',\n      id: this.state.nextScopeId++\n    };\n    if (name) {\n      scopeInfo.name = name;\n    }\n    this.state.scopeStack.push(scopeInfo);\n    this.state.activeScope = scopeInfo;\n  }\n  /**\n   * End a scope. Use this with startScope() to achieve the same functionality\n   * as scope() without the need for a function closure.\n   */\n  endScope(result) {\n    const tensorsToTrackInParent = getTensorsInContainer(result);\n    const tensorsToTrackInParentSet = new Set(tensorsToTrackInParent.map(t => t.id));\n    // Dispose the arrays tracked in this scope.\n    for (let i = 0; i < this.state.activeScope.track.length; i++) {\n      const tensor = this.state.activeScope.track[i];\n      if (!tensor.kept && !tensorsToTrackInParentSet.has(tensor.id)) {\n        tensor.dispose();\n      }\n    }\n    const oldScope = this.state.scopeStack.pop();\n    this.state.activeScope = this.state.scopeStack.length === 0 ? null : this.state.scopeStack[this.state.scopeStack.length - 1];\n    // Track the current result in the parent scope.\n    tensorsToTrackInParent.forEach(tensor => {\n      // Only track the tensor if was allocated in the inner scope and is not\n      // globally kept.\n      if (!tensor.kept && tensor.scopeId === oldScope.id) {\n        this.track(tensor);\n      }\n    });\n  }\n  /**\n   * Returns gradients of `f` with respect to each of the `xs`. The gradients\n   * returned are of the same length as `xs`, but some might be null if `f`\n   * was not a function of that `x`. It also takes optional dy to multiply the\n   * gradient, which defaults to `1`.\n   */\n  gradients(f, xs, dy, allowNoGradients = false) {\n    util.assert(xs.length > 0, () => 'gradients() received an empty list of xs.');\n    if (dy != null && dy.dtype !== 'float32') {\n      throw new Error(`dy must have 'float32' dtype, but has '${dy.dtype}'`);\n    }\n    const y = this.scopedRun(() => this.startTape(), () => this.endTape(), () => this.tidy('forward', f));\n    util.assert(y instanceof Tensor, () => 'The result y returned by f() must be a tensor.');\n    // Filter out the nodes that don't connect x => y.\n    const filteredTape = getFilteredNodesXToY(this.state.activeTape, xs, y);\n    if (!allowNoGradients && filteredTape.length === 0 && xs.length > 0) {\n      throw new Error('Cannot compute gradient of y=f(x) with respect to x. Make sure ' + 'that the f you passed encloses all operations that lead from x ' + 'to y.');\n    }\n    return this.tidy('backward', () => {\n      const accumulatedGradientMap = {};\n      accumulatedGradientMap[y.id] = dy == null ? ones(y.shape) : dy;\n      // Backprop gradients through the filtered nodes.\n      backpropagateGradients(accumulatedGradientMap, filteredTape,\n      // Pass the tidy function to avoid circular dep with `tape.ts`.\n      f => this.tidy(f),\n      // Pass an add function to avoide a circular dep with `tape.ts`.\n      add);\n      const grads = xs.map(x => accumulatedGradientMap[x.id]);\n      if (this.state.gradientDepth === 0) {\n        // This means that we are not computing higher-order gradients\n        // and can clean up the tape.\n        this.state.activeTape.forEach(node => {\n          for (const tensor of node.saved) {\n            tensor.dispose();\n          }\n        });\n        this.state.activeTape = null;\n      }\n      return {\n        value: y,\n        grads\n      };\n    });\n  }\n  customGrad(f) {\n    util.assert(util.isFunction(f), () => 'The f passed in customGrad(f) must be a function.');\n    return (...inputs) => {\n      util.assert(inputs.every(t => t instanceof Tensor), () => 'The args passed in customGrad(f)(x1, x2,...) must all be ' + 'tensors');\n      let res;\n      const inputMap = {};\n      inputs.forEach((input, i) => {\n        inputMap[i] = input;\n      });\n      const forwardFunc = (_, save) => {\n        res = f(...[...inputs, save]);\n        util.assert(res.value instanceof Tensor, () => 'The function f passed in customGrad(f) must return an ' + 'object where `obj.value` is a tensor');\n        util.assert(util.isFunction(res.gradFunc), () => 'The function f passed in customGrad(f) must return an ' + 'object where `obj.gradFunc` is a function.');\n        return res.value;\n      };\n      const backwardsFunc = (dy, saved) => {\n        const gradRes = res.gradFunc(dy, saved);\n        const grads = Array.isArray(gradRes) ? gradRes : [gradRes];\n        util.assert(grads.length === inputs.length, () => 'The function f passed in customGrad(f) must return an ' + 'object where `obj.gradFunc` is a function that returns ' + 'the same number of tensors as inputs passed to f(...).');\n        util.assert(grads.every(t => t instanceof Tensor), () => 'The function f passed in customGrad(f) must return an ' + 'object where `obj.gradFunc` is a function that returns ' + 'a list of only tensors.');\n        const gradMap = {};\n        grads.forEach((grad, i) => {\n          gradMap[i] = () => grad;\n        });\n        return gradMap;\n      };\n      return this.runKernelFunc({\n        forwardFunc,\n        backwardsFunc,\n        inputs: inputMap\n      });\n    };\n  }\n  readSync(dataId) {\n    // Route the read to the correct backend.\n    const info = this.state.tensorInfo.get(dataId);\n    return info.backend.readSync(dataId);\n  }\n  read(dataId) {\n    // Route the read to the correct backend.\n    const info = this.state.tensorInfo.get(dataId);\n    return info.backend.read(dataId);\n  }\n  readToGPU(dataId, options) {\n    // Route the read to the correct backend.\n    const info = this.state.tensorInfo.get(dataId);\n    return info.backend.readToGPU(dataId, options);\n  }\n  async time(query) {\n    const start = now();\n    const timingInfo = await this.backend.time(query);\n    timingInfo.wallMs = now() - start;\n    return timingInfo;\n  }\n  /**\n   * Tracks a Tensor in the current scope to be automatically cleaned up\n   * when the current scope ends, and returns the value.\n   *\n   * @param result The Tensor to track in the current scope.\n   */\n  track(result) {\n    if (this.state.activeScope != null) {\n      result.scopeId = this.state.activeScope.id;\n      this.state.activeScope.track.push(result);\n    }\n    return result;\n  }\n  get registeredVariables() {\n    return this.state.registeredVariables;\n  }\n  /**\n   * Resets the engine state. Removes all backends but does not remove\n   * registered backend factories.\n   */\n  reset() {\n    // Make any pending promise obsolete.\n    this.pendingBackendInitId++;\n    this.state.dispose();\n    this.ENV.reset();\n    this.state = new EngineState();\n    for (const backendName in this.registry) {\n      this.disposeRegisteredKernels(backendName);\n      this.registry[backendName].dispose();\n      delete this.registry[backendName];\n    }\n    this.backendName = null;\n    this.backendInstance = null;\n    this.pendingBackendInit = null;\n  }\n}\nEngine.nextTensorId = 0;\nEngine.nextVariableId = 0;\nexport { Engine };\nfunction ones(shape) {\n  const values = makeOnesTypedArray(sizeFromShape(shape), 'float32');\n  return ENGINE.makeTensor(values, shape, 'float32');\n}\nexport function getOrMakeEngine() {\n  const ns = getGlobalNamespace();\n  if (ns._tfengine == null) {\n    const environment = new Environment(ns);\n    ns._tfengine = new Engine(environment);\n  }\n  setEnvironmentGlobal(ns._tfengine.ENV);\n  // Tell the current tensor interface that the global engine is responsible\n  // for tracking.\n  setTensorTracker(() => ns._tfengine);\n  return ns._tfengine;\n}\nexport const ENGINE = getOrMakeEngine();\n/**\n * A implementation of the add op for use within engine and tape.\n *\n * This allows us to avoid a circular dependency between add.ts and engine.\n * It is exported to be available in tape tests.\n */\nexport function add(a, b) {\n  // We duplicate Add here to avoid a circular dependency with add.ts.\n  const inputs = {\n    a,\n    b\n  };\n  return ENGINE.runKernel(Add, inputs);\n}", "map": {"version": 3, "names": ["KernelBackend", "Environment", "setEnvironmentGlobal", "getGlobalNamespace", "Add", "Cast", "Identity", "getGradient", "getKernel", "getKernelsForBackend", "log", "Profiler", "backpropagateGradients", "getFilteredNodesXToY", "setTensorTracker", "Tensor", "Variable", "getTensorsInContainer", "util", "bytesFromStringArray", "makeOnesTypedArray", "now", "sizeFromShape", "isRegisteredKernelInvocation", "kernelInvocation", "kernelName", "EngineState", "constructor", "registeredVariables", "nextTapeNodeId", "numBytes", "numTensors", "numStringTensors", "numDataBuffers", "gradientDepth", "kernelDepth", "scopeStack", "numDataMovesStack", "nextScopeId", "tensorInfo", "WeakMap", "profiling", "activeProfile", "newBytes", "newTensors", "peakBytes", "kernels", "result", "kernelNames", "Array", "from", "Set", "map", "k", "name", "dispose", "variableName", "Engine", "ENV", "registry", "registryFactory", "pendingBackendInitId", "state", "ready", "pendingBackendInit", "then", "backendInstance", "sortedBackends", "getSortedBackends", "i", "length", "backendName", "success", "initializeBackend", "setBackend", "Error", "backend", "asyncInit", "initializeBackendsAndReturnBest", "backendNames", "Object", "keys", "findBackend", "findBackendFactory", "factory", "registerBackend", "priority", "warn", "setupRegisteredKernels", "profiler", "for<PERSON>ach", "kernel", "setupFunc", "disposeRegisteredKernels", "dispose<PERSON>unc", "registryFactoryEntry", "promiseId", "catch", "err", "stack", "message", "removeBackend", "sort", "a", "b", "moveData", "dataId", "info", "get", "srcBackend", "values", "readSync", "refCount", "disposeData", "move", "shape", "dtype", "shouldCheckForMemLeaks", "tidy", "nameOrFn", "fn", "String", "scopedRun", "startScope", "endScope", "Promise", "console", "error", "start", "end", "f", "res", "ex", "nextTensorId", "nextVariableId", "clone", "x", "y", "ENGINE", "runKernel", "inputs", "grad", "dy", "gradInputs", "attrs", "saved", "addTapeNode", "activeScope", "has<PERSON><PERSON><PERSON>", "runKernelFunc", "getBool", "checkKernelForMemLeak", "numDataIdsBefore", "outInfos", "numDataIdsAfter", "numDataIds", "numOutputDataIds", "numMoves", "dataIdsLeaked", "kernelParams", "outputs", "isTapeOn", "startingBytecount", "startingNumTensors", "push", "kernelFunc", "out", "kernelOrScopeName", "assert", "isArray", "outTensors", "outInfo", "rank", "makeTensorFromTensorInfo", "tensorsToSave", "getTensorsForGradient", "saveTensorsForBackwardMode", "forward<PERSON>un<PERSON>", "saveFunc", "tensors", "tensor", "keep", "outs", "backwardsFunc", "kernelProfile", "<PERSON><PERSON><PERSON><PERSON>", "logKernelProfile", "bytesAdded", "totalBytesSnapshot", "tensorsAdded", "totalTensorsSnapshot", "inputShapes", "key", "outputShapes", "item", "kernelTimeMs", "timeMs", "extraInfo", "gradConfig", "inputsToSave", "outputsToSave", "inputTensorsToSave", "saveAllInputs", "inputName", "outputTensorsToSave", "filter", "_", "concat", "makeTensor", "backendVals", "isString", "d", "encodeString", "write", "t", "trackTensor", "bytes", "makeTensorFromDataId", "makeVariable", "initialValue", "trainable", "toString", "cast", "v", "incRef", "size", "bytesPerElement", "has", "set", "track", "removeDataId", "delete", "disposeTensor", "disposeVariables", "varName", "disposeVariable", "memory", "unreliable", "reasons", "profile", "query", "startBytes", "startNumTensors", "Math", "max", "gradientsFunc", "tapeNode", "id", "grad<PERSON>unc", "gradient", "dys", "output", "vals", "makeZerosTypedArray", "activeTape", "kept", "startTape", "endTape", "scopeInfo", "tensorsToTrackInParent", "tensorsToTrackInParentSet", "oldScope", "pop", "scopeId", "gradients", "xs", "allowNoGradients", "filteredTape", "accumulatedGradientMap", "ones", "add", "grads", "node", "value", "customGrad", "isFunction", "every", "inputMap", "input", "save", "gradRes", "gradMap", "read", "readToGPU", "options", "time", "timingInfo", "wallMs", "reset", "getOrMakeEngine", "ns", "_tfengine", "environment"], "sources": ["C:\\tfjs-core\\src\\engine.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2018 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {BackendTimingInfo, DataMover, KernelBackend} from './backends/backend';\nimport {Environment, setEnvironmentGlobal} from './environment';\nimport {getGlobalNamespace} from './global_util';\nimport {Add, Cast, Identity} from './kernel_names';\nimport { getGradient, getKernel, getK<PERSON>lsForBackend, GradFunc, NamedAttrMap } from './kernel_registry';\nimport { TensorInfo } from './tensor_info';\nimport * as log from './log';\nimport {KernelProfile, Profiler} from './profiler';\nimport {backpropagateGradients, getFilteredNodesXToY, TapeNode} from './tape';\nimport {DataToGPUOptions, GPUData, setTensorTracker, Tensor, TensorTracker, Variable} from './tensor';\nimport {DataId} from './tensor_info';\nimport {GradSaveFunc, NamedTensorMap, NamedVariableMap, TensorContainer} from './tensor_types';\nimport {getTensorsInContainer} from './tensor_util';\nimport {BackendValues, DataType, DataValues} from './types';\nimport * as util from './util';\nimport {bytesFromStringArray, makeOnesTypedArray, now, sizeFromShape} from './util';\n\n/**\n * A function that computes an output. The save function is for saving tensors\n * computed in the forward pass, that we need in the backward pass.\n */\nexport type ForwardFunc<T> = (backend: KernelBackend, save?: GradSaveFunc) => T;\n\n/**\n * @docalias (a: Tensor, b: Tensor,..., save?: Function) => {\n *   value: Tensor,\n *   gradFunc: (dy: Tensor, saved?: NamedTensorMap) => Tensor | Tensor[]\n * }\n */\nexport type CustomGradientFunc<T extends Tensor> =\n    (...inputs: Array<Tensor|GradSaveFunc>) => {\n      value: T;\n      gradFunc: (dy: T, saved: Tensor[]) => Tensor | Tensor[];\n    };\n\nexport type MemoryInfo = {\n  numTensors: number; numDataBuffers: number; numBytes: number;\n  unreliable?: boolean; reasons: string[];\n};\n\ntype KernelInfo = {\n  name: string; bytesAdded: number; totalBytesSnapshot: number;\n  tensorsAdded: number;\n  totalTensorsSnapshot: number;\n  inputShapes: number[][];\n  outputShapes: number[][];\n  kernelTimeMs: number | {error: string} | Promise<number|{error: string}>;\n  extraInfo: string | Promise<string>;\n};\n\nexport type ProfileInfo = {\n  newBytes: number; newTensors: number; peakBytes: number;\n  kernels: KernelInfo[];\n  result: TensorContainer;\n  kernelNames: string[];\n};\n\nexport interface TimingInfo extends BackendTimingInfo {\n  wallMs: number;\n}\n\n/** @docalias Function */\nexport type ScopeFn<T extends TensorContainer> = () => T;\n\ninterface ScopeState {\n  track: Tensor[];\n  name: string;\n  id: number;\n}\n\ninterface RegisteredKernelInvocation<I extends NamedTensorMap> {\n  kernelName: string;\n  inputs: I;\n  attrs?: NamedAttrMap;\n}\n\ninterface CustomGradKernelInvocation<T extends Tensor|Tensor[],\n                                               I extends NamedTensorMap> {\n  forwardFunc: ForwardFunc<T>;\n  backwardsFunc: (dy: T, saved: Tensor[]) => {\n    [P in keyof I]: () => I[P]\n  };\n  inputs: I;\n  attrs?: NamedAttrMap;\n}\n\nfunction isRegisteredKernelInvocation<T extends Tensor|Tensor[],\n                                                I extends NamedTensorMap>(\n    kernelInvocation: RegisteredKernelInvocation<I>|\n    CustomGradKernelInvocation<T, I>):\n    kernelInvocation is RegisteredKernelInvocation<I> {\n  return (kernelInvocation as RegisteredKernelInvocation<I>).kernelName != null;\n}\n\nclass EngineState {\n  // Public since optimizers will use it.\n  registeredVariables: NamedVariableMap = {};\n\n  nextTapeNodeId = 0;\n  numBytes = 0;\n  numTensors = 0;\n  numStringTensors = 0;\n  numDataBuffers = 0;\n\n  activeTape: TapeNode[];\n  // Number of nested tf.grad() statements when computing higher-order\n  // gradients. E.g. `1` for first-order gradients and `2` for second-order\n  // gradients. Used to track if the tape should be removed after a backprop.\n  gradientDepth = 0;\n  // Number of nested kernel calls. When kernel depth is greater than 1, we turn\n  // off the tape.\n  kernelDepth = 0;\n\n  // Keep Tensors that parallel the tapes.\n  activeScope: ScopeState;\n  scopeStack: ScopeState[] = [];\n  /**\n   * Keeps track of the number of data moves during a kernel execution. We\n   * maintain a stack since kernels can call other kernels, recursively.\n   */\n  numDataMovesStack: number[] = [];\n  nextScopeId = 0;\n\n  tensorInfo = new WeakMap<DataId, {\n    backend: KernelBackend,\n    bytes: number,\n    dtype: DataType,\n    shape: number[]\n  }>();\n\n  profiling = false;\n  activeProfile: ProfileInfo = {\n    newBytes: 0,\n    newTensors: 0,\n    peakBytes: 0,\n    kernels: [],\n    result: null,\n    get kernelNames():\n        string[] {\n          return Array.from(new Set(this.kernels.map(k => k.name)));\n        }\n  };\n\n  dispose() {\n    for (const variableName in this.registeredVariables) {\n      this.registeredVariables[variableName].dispose();\n    }\n  }\n}\n\nexport class Engine implements TensorTracker, DataMover {\n  state: EngineState;\n  backendName: string;\n  registry: {[id: string]: KernelBackend} = {};\n  registryFactory: {\n    [id: string]: {\n      factory: () => KernelBackend | Promise<KernelBackend>,\n      priority: number\n    }\n  } = {};\n\n  private profiler: Profiler;\n  private backendInstance: KernelBackend;\n  private pendingBackendInit: Promise<boolean>;\n  private pendingBackendInitId = 0;\n\n  constructor(public ENV: Environment) {\n    this.state = new EngineState();\n  }\n\n  async ready(): Promise<void> {\n    if (this.pendingBackendInit != null) {\n      return this.pendingBackendInit.then(() => {});\n    }\n    if (this.backendInstance != null) {\n      return;\n    }\n    const sortedBackends = this.getSortedBackends();\n\n    for (let i = 0; i < sortedBackends.length; i++) {\n      const backendName = sortedBackends[i];\n      const success = await this.initializeBackend(backendName).success;\n      if (success) {\n        await this.setBackend(backendName);\n        return;\n      }\n    }\n\n    throw new Error(\n        `Could not initialize any backends, all backend initializations ` +\n        `failed.`);\n  }\n\n  get backend(): KernelBackend {\n    if (this.pendingBackendInit != null) {\n      throw new Error(\n          `Backend '${this.backendName}' has not yet been initialized. Make ` +\n          `sure to await tf.ready() or await tf.setBackend() before calling ` +\n          `other methods`);\n    }\n    if (this.backendInstance == null) {\n      const {name, asyncInit} = this.initializeBackendsAndReturnBest();\n      if (asyncInit) {\n        throw new Error(\n            `The highest priority backend '${name}' has not yet been ` +\n            `initialized. Make sure to await tf.ready() or ` +\n            `await tf.setBackend() before calling other methods`);\n      }\n      this.setBackend(name);\n    }\n    return this.backendInstance;\n  }\n\n  backendNames(): string[] {\n    return Object.keys(this.registryFactory);\n  }\n\n  findBackend(backendName: string): KernelBackend {\n    if (!(backendName in this.registry)) {\n      // If the backend hasn't been initialized but we have a registry entry for\n      // it, initialize it and return it.\n      if (backendName in this.registryFactory) {\n        const {asyncInit} = this.initializeBackend(backendName);\n        if (asyncInit) {\n          // Backend is not ready yet.\n          return null;\n        }\n      } else {\n        return null;\n      }\n    }\n    return this.registry[backendName];\n  }\n\n  findBackendFactory(backendName: string):\n      () => KernelBackend | Promise<KernelBackend> {\n    if (!(backendName in this.registryFactory)) {\n      return null;\n    }\n    return this.registryFactory[backendName].factory;\n  }\n\n  registerBackend(\n      backendName: string,\n      factory: () => KernelBackend | Promise<KernelBackend>,\n      priority = 1): boolean {\n    if (backendName in this.registryFactory) {\n      log.warn(\n          `${backendName} backend was already registered. ` +\n          `Reusing existing backend factory.`);\n      return false;\n    }\n    this.registryFactory[backendName] = {factory, priority};\n    return true;\n  }\n\n  async setBackend(backendName: string): Promise<boolean> {\n    if (this.registryFactory[backendName] == null) {\n      throw new Error(`Backend name '${backendName}' not found in registry`);\n    }\n    this.backendName = backendName;\n    if (this.registry[backendName] == null) {\n      this.backendInstance = null;\n      const {success, asyncInit} = this.initializeBackend(backendName);\n      const result = asyncInit ? await success : success;\n      if (!result) {\n        return false;\n      }\n    }\n    this.backendInstance = this.registry[backendName];\n    this.setupRegisteredKernels();\n    // Reset the profiler.\n    this.profiler = new Profiler(this.backendInstance);\n\n    return true;\n  }\n\n  private setupRegisteredKernels(): void {\n    const kernels = getKernelsForBackend(this.backendName);\n    kernels.forEach(kernel => {\n      if (kernel.setupFunc != null) {\n        kernel.setupFunc(this.backendInstance);\n      }\n    });\n  }\n\n  private disposeRegisteredKernels(backendName: string): void {\n    const kernels = getKernelsForBackend(backendName);\n    kernels.forEach(kernel => {\n      if (kernel.disposeFunc != null) {\n        kernel.disposeFunc(this.registry[backendName]);\n      }\n    });\n  }\n\n  /**\n   * Initializes a backend by looking up the backend name in the factory\n   * registry and calling the factory method. Returns a boolean representing\n   * whether the initialization of the backend succeeded. Throws an error if\n   * there is no backend in the factory registry.\n   */\n  private initializeBackend(backendName: string):\n      {success: boolean|Promise<boolean>, asyncInit: boolean} {\n    const registryFactoryEntry = this.registryFactory[backendName];\n    if (registryFactoryEntry == null) {\n      throw new Error(\n          `Cannot initialize backend ${backendName}, no registration found.`);\n    }\n\n    try {\n      const backend = registryFactoryEntry.factory();\n      /* Test if the factory returns a promise.\n      Done in a more liberal way than\n      previous 'Promise.resolve(backend)===backend'\n      as we needed to account for custom Promise\n      implementations (e.g. Angular) */\n      if (backend && !(backend instanceof KernelBackend) &&\n          typeof backend.then === 'function') {\n        const promiseId = ++this.pendingBackendInitId;\n        const success =\n            backend\n                .then(backendInstance => {\n                  // Outdated promise. Another backend was set in the meantime.\n                  if (promiseId < this.pendingBackendInitId) {\n                    return false;\n                  }\n                  this.registry[backendName] = backendInstance;\n                  this.pendingBackendInit = null;\n                  return true;\n                })\n                .catch(err => {\n                  // Outdated promise. Another backend was set in the meantime.\n                  if (promiseId < this.pendingBackendInitId) {\n                    return false;\n                  }\n                  this.pendingBackendInit = null;\n                  log.warn(`Initialization of backend ${backendName} failed`);\n                  log.warn(err.stack || err.message);\n                  return false;\n                });\n        this.pendingBackendInit = success;\n        return {success, asyncInit: true};\n      } else {\n        this.registry[backendName] = backend as KernelBackend;\n        return {success: true, asyncInit: false};\n      }\n    } catch (err) {\n      log.warn(`Initialization of backend ${backendName} failed`);\n      log.warn(err.stack || err.message);\n      return {success: false, asyncInit: false};\n    }\n  }\n\n  removeBackend(backendName: string): void {\n    if (!(backendName in this.registryFactory)) {\n      throw new Error(`${backendName} backend not found in registry`);\n    }\n    if (this.backendName === backendName && this.pendingBackendInit != null) {\n      // There is a pending promise of the backend we want to remove. Make it\n      // obsolete.\n      this.pendingBackendInitId++;\n    }\n\n    if (backendName in this.registry) {\n      this.disposeRegisteredKernels(backendName);\n      this.registry[backendName].dispose();\n      delete this.registry[backendName];\n    }\n\n    delete this.registryFactory[backendName];\n\n    // Unset the backend if it is active.\n    if (this.backendName === backendName) {\n      this.pendingBackendInit = null;\n      this.backendName = null;\n      this.backendInstance = null;\n    }\n  }\n\n  private getSortedBackends(): string[] {\n    if (Object.keys(this.registryFactory).length === 0) {\n      throw new Error('No backend found in registry.');\n    }\n    return Object.keys(this.registryFactory).sort((a: string, b: string) => {\n      // Highest priority comes first.\n      return this.registryFactory[b].priority -\n          this.registryFactory[a].priority;\n    });\n  }\n\n  private initializeBackendsAndReturnBest():\n      {name: string, asyncInit: boolean} {\n    const sortedBackends = this.getSortedBackends();\n\n    for (let i = 0; i < sortedBackends.length; i++) {\n      const backendName = sortedBackends[i];\n      const {success, asyncInit} = this.initializeBackend(backendName);\n      if (asyncInit || success) {\n        return {name: backendName, asyncInit};\n      }\n    }\n    throw new Error(\n        `Could not initialize any backends, all backend initializations ` +\n        `failed.`);\n  }\n\n  moveData(backend: KernelBackend, dataId: DataId) {\n    const info = this.state.tensorInfo.get(dataId);\n    const srcBackend = info.backend;\n    const values = this.readSync(dataId);\n    const refCount = srcBackend.refCount(dataId);\n    // Delete the tensor from the old backend and move it to the new\n    // backend.\n    srcBackend.disposeData(dataId, true);\n    info.backend = backend;\n    backend.move(dataId, values, info.shape, info.dtype, refCount);\n    if (this.shouldCheckForMemLeaks()) {\n      // Track the number of moves during a kernel execution to correctly\n      // detect memory leaks.\n      this.state.numDataMovesStack[this.state.numDataMovesStack.length - 1]++;\n    }\n  }\n\n  tidy<T extends TensorContainer>(nameOrFn: string|ScopeFn<T>, fn?: ScopeFn<T>):\n      T {\n    let name: string = null;\n    if (fn == null) {\n      // Called with only 1 argument.\n      if (typeof nameOrFn !== 'function') {\n        throw new Error('Please provide a function to tidy()');\n      }\n      fn = nameOrFn;\n    } else {\n      // Called with 2 arguments.\n      if (typeof nameOrFn !== 'string' && !(nameOrFn instanceof String)) {\n        throw new Error(\n            'When calling with two arguments, the first argument ' +\n            'to tidy() must be a string');\n      }\n      if (typeof fn !== 'function') {\n        throw new Error(\n            'When calling with two arguments, the 2nd argument ' +\n            'to tidy() must be a function');\n      }\n      name = nameOrFn as string;\n      // TODO(nsthorat,smilkov): Do operation logging and performance\n      // profiling.\n    }\n    let result: T;\n    return this.scopedRun(\n        () => this.startScope(name), () => this.endScope(result), () => {\n          result = fn();\n          if (result instanceof Promise) {\n            console.error('Cannot return a Promise inside of tidy.');\n          }\n          return result;\n        });\n  }\n\n  private scopedRun<T>(start: () => void, end: () => void, f: () => T): T {\n    start();\n    try {\n      const res = f();\n      end();\n      return res;\n    } catch (ex) {\n      end();\n      throw ex;\n    }\n  }\n\n  private static nextTensorId = 0;\n  private nextTensorId(): number {\n    return Engine.nextTensorId++;\n  }\n\n  private static nextVariableId = 0;\n  private nextVariableId(): number {\n    return Engine.nextVariableId++;\n  }\n\n  /**\n   * This method is called instead of the public-facing tensor.clone() when\n   * saving a tensor for backwards pass. It makes sure to add the clone\n   * operation to the tape regardless of being called inside a kernel\n   * execution.\n   */\n  private clone(x: Tensor): Tensor {\n    const y: Tensor = ENGINE.runKernel(Identity,\n                                       {x} as unknown as NamedTensorMap);\n    const inputs = {x};\n    const grad = (dy: Tensor) => ({\n      x: () => {\n        const dtype = 'float32';\n        const gradInputs = {x: dy};\n        const attrs = {dtype};\n\n        return ENGINE.runKernel(\n                   Cast, gradInputs as unknown as NamedTensorMap,\n                   // tslint:disable-next-line: no-unnecessary-type-assertion\n                   attrs as unknown as NamedAttrMap) as Tensor;\n      }\n    });\n    const saved: Tensor[] = [];\n    this.addTapeNode(this.state.activeScope.name, inputs, [y], grad, saved, {});\n    return y;\n  }\n\n  /**\n   * Execute a kernel with the given name and return the output tensor.\n   *\n   * @param kernelName The name of the kernel to execute.\n   * @param inputs A map of input names to tensors.\n   * @param attrs A map of attribute names to their values. An attribute is a\n   *     primitive (non-tensor) input to the kernel.\n   * @param inputsToSave A list of tensors, inputs to save for the backprop\n   *     computation.\n   * @param outputsToSave A list of booleans, specifying which output to save\n   *     for the backprop computation. These are booleans since the output\n   * tensors are not visible to the user.\n   */\n  runKernel<T extends Tensor|Tensor[]>(\n      kernelName: string, inputs: NamedTensorMap, attrs?: NamedAttrMap): T {\n    if (this.backendName == null) {\n      // backend has not been initialized yet (backend initialization is lazy\n      // can be deferred until an op/ kernel is run).\n      // The below getter has side effects that will try to initialize the\n      // backend and set properties like this.backendName\n      // tslint:disable-next-line: no-unused-expression\n      this.backend;\n    }\n    const hasKernel = getKernel(kernelName, this.backendName) != null;\n    if (!hasKernel) {\n      throw new Error(`Kernel '${kernelName}' not registered for backend '${\n          this.backendName}'`);\n    }\n    return this.runKernelFunc({kernelName, inputs, attrs});\n  }\n\n  private shouldCheckForMemLeaks(): boolean {\n    return this.ENV.getBool('IS_TEST');\n  }\n\n  private checkKernelForMemLeak(\n      kernelName: string, numDataIdsBefore: number,\n      outInfos: TensorInfo[]): void {\n    const numDataIdsAfter = this.backend.numDataIds();\n\n    // Count the number of data ids associated with the result of the kernel.\n    let numOutputDataIds = 0;\n    outInfos.forEach(info => {\n      // Complex numbers allocate 3 data ids, one for 'real', one for\n      // 'imaginary', and one for the container that holds the former two.\n      numOutputDataIds += (info.dtype === 'complex64' ? 3 : 1);\n    });\n\n    // Account for the number of moves during kernel execution. A \"data move\"\n    // can happen in the middle of a kernel execution, placing a new (key,value)\n    // pair in the data storage. Since data moves have net zero effect (we\n    // always remove the data from the old backend), we have to cancel them out\n    // when detecting memory leaks.\n    const numMoves =\n        this.state.numDataMovesStack[this.state.numDataMovesStack.length - 1];\n    const dataIdsLeaked =\n        numDataIdsAfter - numDataIdsBefore - numOutputDataIds - numMoves;\n    if (dataIdsLeaked > 0) {\n      throw new Error(\n          `Backend '${this.backendName}' has an internal memory leak ` +\n          `(${dataIdsLeaked} data ids) after running '${kernelName}'`);\n    }\n  }\n\n  /**\n   * Internal helper method to execute a kernel Func\n   *\n   * Use `runKernel` to execute kernels from outside of engine.\n   */\n  private runKernelFunc<T extends Tensor|Tensor[], I extends NamedTensorMap>(\n      kernelParams: RegisteredKernelInvocation<I>|\n      CustomGradKernelInvocation<T, I>): T {\n    let outputs: Tensor[];\n    let saved: Tensor[] = [];\n    const isTapeOn = this.isTapeOn();\n\n    const startingBytecount = this.state.numBytes;\n    const startingNumTensors = this.state.numTensors;\n\n    if (this.shouldCheckForMemLeaks()) {\n      this.state.numDataMovesStack.push(0);\n    }\n\n    let kernelFunc: () => Tensor[];\n    if (this.backendName == null) {\n      // backend has not been initialized yet (backend initialization is lazy\n      // can be deferred until an op/ kernel is run).\n      // The below getter has side effects that will try to initialize the\n      // backend and set properties like this.backendName\n      // tslint:disable-next-line: no-unused-expression\n      this.backend;\n    }\n\n    let out: TensorInfo|TensorInfo[];\n\n    const kernelOrScopeName = isRegisteredKernelInvocation(kernelParams) ?\n        kernelParams.kernelName :\n        this.state.activeScope != null ? this.state.activeScope.name : '';\n\n    // Create the kernelFunc from either a registered kernel OR passed in\n    // forward/backward functions (used by custom grad). In this context a\n    // kernelFunc wraps a kernel implementation with some bookkeeping.\n\n    if (isRegisteredKernelInvocation(kernelParams)) {\n      const {kernelName, inputs, attrs} = kernelParams;\n      if (this.backendName == null) {\n        // backend has not been initialized yet (backend initialization is lazy\n        // can be deferred until an op/ kernel is run).\n        // The below getter has side effects that will try to initialize the\n        // backend and set properties like this.backendName\n        // tslint:disable-next-line: no-unused-expression\n        this.backend;\n      }\n      const kernel = getKernel(kernelName, this.backendName);\n      util.assert(\n          kernel != null,\n          () => `Cannot find registered kernel '${kernelName}' for backend '${\n              this.backendName}'`);\n\n      kernelFunc = () => {\n        const numDataIdsBefore = this.backend.numDataIds();\n        out = kernel.kernelFunc({inputs, attrs, backend: this.backend});\n        const outInfos = Array.isArray(out) ? out : [out];\n        if (this.shouldCheckForMemLeaks()) {\n          this.checkKernelForMemLeak(kernelName, numDataIdsBefore, outInfos);\n        }\n\n        const outTensors = outInfos.map((outInfo: TensorInfo|Tensor) => {\n          // todo (yassogba) remove this option (Tensor) when node backend\n          // methods have been modularized and they all return tensorInfo.\n          // TensorInfos do not have a rank attribute.\n          if ((outInfo as Tensor).rank != null) {\n            return outInfo as Tensor;\n          }\n          return this.makeTensorFromTensorInfo(outInfo);\n        });\n\n        // Save any required inputs and outputs.\n\n        // Do not save unless we are recording to the tape. Otherwise it would\n        // cause a mem leak since there would be no backprop for these tensors\n        // (which would otherwise dispose them).\n        if (isTapeOn) {\n          const tensorsToSave =\n              this.getTensorsForGradient(kernelName, inputs, outTensors);\n          saved = this.saveTensorsForBackwardMode(tensorsToSave);\n        }\n        return outTensors;\n      };\n    } else {\n      const {forwardFunc} = kernelParams;\n      // Running a customGrad op.\n      const saveFunc: GradSaveFunc = (tensors) => {\n        // Do not save unless we are recording to the tape. Otherwise it would\n        // cause a mem leak since we would never run backprop, which disposes\n        // the kept tensors.\n        if (!isTapeOn) {\n          return;\n        }\n        saved = tensors.map(tensor => this.keep(this.clone(tensor)));\n      };\n\n      kernelFunc = () => {\n        const numDataIdsBefore = this.backend.numDataIds();\n        out = this.tidy(() => forwardFunc(this.backend, saveFunc));\n        const outs = (Array.isArray(out) ? out : [out]) as Tensor[];\n        if (this.shouldCheckForMemLeaks()) {\n          // Scope name is used to print a more helpful error message if needed.\n          this.checkKernelForMemLeak(kernelOrScopeName, numDataIdsBefore, outs);\n        }\n        return outs;\n      };\n    }\n\n    //\n    // Run the kernelFunc. Optionally profiling it.\n    //\n    const {inputs, attrs} = kernelParams;\n    const backwardsFunc = isRegisteredKernelInvocation(kernelParams) ?\n        null :\n        kernelParams.backwardsFunc;\n\n    let kernelProfile: KernelProfile;\n    this.scopedRun(\n        // Stop recording to a tape when running a kernel.\n        () => this.state.kernelDepth++, () => this.state.kernelDepth--, () => {\n          if (!this.ENV.getBool('DEBUG') && !this.state.profiling) {\n            outputs = kernelFunc();\n          } else {\n            kernelProfile = this.profiler.profileKernel(\n                kernelOrScopeName, inputs, () => kernelFunc());\n            if (this.ENV.getBool('DEBUG')) {\n              this.profiler.logKernelProfile(kernelProfile);\n            }\n            outputs = kernelProfile.outputs;\n          }\n        });\n\n    if (isTapeOn) {\n      this.addTapeNode(\n          kernelOrScopeName, inputs, outputs, backwardsFunc, saved, attrs);\n    }\n\n    if (this.state.profiling) {\n      this.state.activeProfile.kernels.push({\n        name: kernelOrScopeName,\n        bytesAdded: this.state.numBytes - startingBytecount,\n        totalBytesSnapshot: this.state.numBytes,\n        tensorsAdded: this.state.numTensors - startingNumTensors,\n        totalTensorsSnapshot: this.state.numTensors,\n        inputShapes: Object.keys(inputs).map(\n            key => inputs[key] != null ? inputs[key].shape : null),\n        outputShapes: outputs.map(item => item.shape),\n        kernelTimeMs: kernelProfile.timeMs,\n        extraInfo: kernelProfile.extraInfo\n      });\n    }\n    return (Array.isArray(out) ? outputs : outputs[0]) as T;\n  }\n\n  /**\n   * Saves tensors used in forward mode for use in backward mode.\n   *\n   * @param tensors the list of tensors to save.\n   */\n  private saveTensorsForBackwardMode(tensors: Tensor[]): Tensor[] {\n    const saved = tensors.map(tensor => this.keep(this.clone(tensor)));\n    return saved;\n  }\n\n  /**\n   * Returns a list of tensors to save for a given gradient calculation.\n   *\n   * @param kernelName name of kernel to look up gradient for.\n   * @param inputs a map of input tensors.\n   * @param outputs an array of output tensors from forward mode of kernel.\n   */\n  private getTensorsForGradient(\n      kernelName: string, inputs: NamedTensorMap,\n      outputs: Tensor[]): Tensor[]|null {\n    const gradConfig = getGradient(kernelName);\n    if (gradConfig != null) {\n      const inputsToSave: string[] = gradConfig.inputsToSave || [];\n      const outputsToSave: boolean[] = gradConfig.outputsToSave || [];\n\n      // If saveAllInputs is true, all inputs will be saved. Otherwise, inputs\n      // specified in inputsToSave will be saved.\n      let inputTensorsToSave: Tensor[];\n      if (gradConfig.saveAllInputs) {\n        util.assert(\n            Array.isArray(inputs),\n            () => 'saveAllInputs is true, expected inputs to be an array.');\n\n        inputTensorsToSave = Object.keys(inputs).map((key) => inputs[key]);\n      } else {\n        inputTensorsToSave = inputsToSave.map((inputName) => inputs[inputName]);\n      }\n\n      const outputTensorsToSave: Tensor[] =\n          outputs.filter((_, i) => outputsToSave[i]);\n\n      return inputTensorsToSave.concat(outputTensorsToSave);\n    }\n    // We return an empty list rather than throw an error because the kernel we\n    // are looking up may not actually be relevant to backproping through the\n    // overall function\n    //\n    // See 'does not error if irrelevant (pruned) ops are missing grads' test\n    // in gradients_test.ts for an example.\n    return [];\n  }\n\n  /**\n   * Internal method used by public APIs for tensor creation. Makes a new\n   * tensor with the provided shape, dtype and values. It always\n   * creates a new data id and writes the values to the underlying backend.\n   */\n  makeTensor(\n      values: DataValues, shape: number[], dtype: DataType,\n      backend?: KernelBackend): Tensor {\n    if (values == null) {\n      throw new Error('Values passed to engine.makeTensor() are null');\n    }\n    dtype = dtype || 'float32';\n    backend = backend || this.backend;\n    let backendVals = values as BackendValues;\n    if (dtype === 'string' && util.isString(values[0])) {\n      backendVals = (values as string[]).map(d => util.encodeString(d));\n    }\n    const dataId = backend.write(backendVals, shape, dtype);\n    const t = new Tensor(shape, dtype, dataId, this.nextTensorId());\n    this.trackTensor(t, backend);\n\n    // Count bytes for string tensors.\n    if (dtype === 'string') {\n      const info = this.state.tensorInfo.get(dataId);\n      const newBytes = bytesFromStringArray(backendVals as Uint8Array[]);\n      this.state.numBytes += newBytes - info.bytes;\n      info.bytes = newBytes;\n    }\n    return t;\n  }\n\n  /**\n   * Internal method used by backends. Makes a new tensor\n   * that is a wrapper around an existing data id. It doesn't create\n   * a new data id, only increments the ref count used in memory tracking.\n   * @deprecated\n   */\n  makeTensorFromDataId(\n    dataId: DataId, shape: number[], dtype: DataType,\n    backend?: KernelBackend): Tensor {\n    dtype = dtype || 'float32';\n    const tensorInfo: TensorInfo = {dataId, shape, dtype};\n    return this.makeTensorFromTensorInfo(tensorInfo, backend);\n  }\n\n  /**\n   * Internal method used by backends. Makes a new tensor that is a wrapper\n   * around an existing data id in TensorInfo. It doesn't create a new data id,\n   * only increments the ref count used in memory tracking.\n   */\n  makeTensorFromTensorInfo(tensorInfo: TensorInfo, backend?: KernelBackend):\n      Tensor {\n    const {dataId, shape, dtype} = tensorInfo;\n    const t = new Tensor(shape, dtype, dataId, this.nextTensorId());\n    this.trackTensor(t, backend);\n    return t;\n  }\n\n  makeVariable(\n      initialValue: Tensor, trainable = true, name?: string,\n      dtype?: DataType): Variable {\n    name = name || this.nextVariableId().toString();\n    if (dtype != null && dtype !== initialValue.dtype) {\n      initialValue = initialValue.cast(dtype);\n    }\n    const v = new Variable(initialValue, trainable, name, this.nextTensorId());\n    if (this.state.registeredVariables[v.name] != null) {\n      throw new Error(`Variable with name ${v.name} was already registered`);\n    }\n    this.state.registeredVariables[v.name] = v;\n    this.incRef(v, this.backend);\n    return v;\n  }\n\n  trackTensor(a: Tensor, backend: KernelBackend): void {\n    this.state.numTensors++;\n    if (a.dtype === 'string') {\n      this.state.numStringTensors++;\n    }\n    // Bytes for complex numbers are counted by their components. Bytes for\n    // string tensors are counted when writing values.\n    let bytes = 0;\n    if (a.dtype !== 'complex64' && a.dtype !== 'string') {\n      bytes = a.size * util.bytesPerElement(a.dtype);\n    }\n    this.state.numBytes += bytes;\n\n    if (!this.state.tensorInfo.has(a.dataId)) {\n      this.state.numDataBuffers++;\n      this.state.tensorInfo.set(a.dataId, {\n        backend: backend || this.backend,\n        dtype: a.dtype,\n        shape: a.shape,\n        bytes\n      });\n    }\n\n    if (!(a instanceof Variable)) {\n      this.track(a);\n    }\n  }\n\n  // Track the tensor by dataId and increase the refCount for the dataId in the\n  // backend.\n  // TODO(pyu10055): This is currently used by makeVariable method, to increase\n  // refCount on the backend for the dataId. It can potentially be replaced with\n  // Identity op indead of calling backend directly.\n  incRef(a: Tensor, backend: KernelBackend): void {\n    this.trackTensor(a, backend);\n    this.backend.incRef(a.dataId);\n  }\n\n  removeDataId(dataId: DataId, backend: KernelBackend) {\n    if (this.state.tensorInfo.has(dataId) &&\n        this.state.tensorInfo.get(dataId).backend === backend) {\n      this.state.tensorInfo.delete(dataId);\n      this.state.numDataBuffers--;\n    }\n  }\n  disposeTensor(a: Tensor): void {\n    if (!this.state.tensorInfo.has(a.dataId)) {\n      return;\n    }\n    const info = this.state.tensorInfo.get(a.dataId);\n\n    this.state.numTensors--;\n    if (a.dtype === 'string') {\n      this.state.numStringTensors--;\n      this.state.numBytes -= info.bytes;\n    }\n    // Don't count bytes for complex numbers as they are counted by their\n    // components.\n    if (a.dtype !== 'complex64' && a.dtype !== 'string') {\n      const bytes = a.size * util.bytesPerElement(a.dtype);\n      this.state.numBytes -= bytes;\n    }\n\n    // Remove the reference to dataId if backend dispose the data successfully\n    if (info.backend.disposeData(a.dataId)) {\n      this.removeDataId(a.dataId, info.backend);\n    }\n\n    // TODO(nsthorat): Construct an error and save the stack trace for\n    // debugging when in debug mode. Creating a stack trace is too expensive\n    // to do unconditionally.\n  }\n\n  disposeVariables(): void {\n    for (const varName in this.state.registeredVariables) {\n      const v = this.state.registeredVariables[varName];\n      this.disposeVariable(v);\n    }\n  }\n\n  disposeVariable(v: Variable): void {\n    this.disposeTensor(v);\n    if (this.state.registeredVariables[v.name] != null) {\n      delete this.state.registeredVariables[v.name];\n    }\n  }\n\n  memory(): MemoryInfo {\n    const info = this.backend.memory() as MemoryInfo;\n    info.numTensors = this.state.numTensors;\n    info.numDataBuffers = this.state.numDataBuffers;\n    info.numBytes = this.state.numBytes;\n    if (this.state.numStringTensors > 0) {\n      info.unreliable = true;\n      if (info.reasons == null) {\n        info.reasons = [];\n      }\n      info.reasons.push(\n          'Memory usage by string tensors is approximate ' +\n          '(2 bytes per character)');\n    }\n    return info;\n  }\n\n  async profile(query: () => (TensorContainer | Promise<TensorContainer>)):\n      Promise<ProfileInfo> {\n    this.state.profiling = true;\n\n    const startBytes = this.state.numBytes;\n    const startNumTensors = this.state.numTensors;\n\n    this.state.activeProfile.kernels = [];\n    this.state.activeProfile.result = await query();\n\n    this.state.profiling = false;\n\n    this.state.activeProfile.peakBytes = Math.max(\n        ...this.state.activeProfile.kernels.map(d => d.totalBytesSnapshot));\n    this.state.activeProfile.newBytes = this.state.numBytes - startBytes;\n    this.state.activeProfile.newTensors =\n        this.state.numTensors - startNumTensors;\n    for (const kernel of this.state.activeProfile.kernels) {\n      kernel.kernelTimeMs = await kernel.kernelTimeMs;\n      kernel.extraInfo = await kernel.extraInfo;\n    }\n    return this.state.activeProfile;\n  }\n\n  isTapeOn(): boolean {\n    return this.state.gradientDepth > 0 && this.state.kernelDepth === 0;\n  }\n\n  private addTapeNode(\n      kernelName: string, inputs: NamedTensorMap, outputs: Tensor[],\n      gradientsFunc: GradFunc, saved: Tensor[], attrs: NamedAttrMap): void {\n    const tapeNode: TapeNode =\n        {id: this.state.nextTapeNodeId++, kernelName, inputs, outputs, saved};\n\n    const gradConfig = getGradient(kernelName);\n    if (gradConfig != null) {\n      gradientsFunc = gradConfig.gradFunc;\n    }\n    if (gradientsFunc != null) {\n      tapeNode.gradient = (dys: Tensor[]) => {\n        // TODO(smilkov): To optimize back-prop, pass dys that are not used in\n        // the backprop graph to the user as null instead of zeros\n        dys = dys.map((dy, i) => {\n          if (dy == null) {\n            const output = outputs[i];\n            const vals = util.makeZerosTypedArray(output.size, output.dtype);\n            return this.makeTensor(vals, output.shape, output.dtype);\n          }\n          return dy;\n        });\n        // Grad functions of ops with single outputs expect a dy, while ops\n        // with multiple outputs expect dys (array of dy).\n        return gradientsFunc(dys.length > 1 ? dys : dys[0], saved, attrs);\n      };\n    }\n    this.state.activeTape.push(tapeNode);\n  }\n\n  keep<T extends Tensor>(result: T): T {\n    result.kept = true;\n    return result;\n  }\n\n  private startTape() {\n    if (this.state.gradientDepth === 0) {\n      this.state.activeTape = [];\n    }\n    this.state.gradientDepth++;\n  }\n\n  private endTape() {\n    this.state.gradientDepth--;\n  }\n\n  /**\n   * Start a scope. Use this with endScope() to achieve the same functionality\n   * as scope() without the need for a function closure.\n   */\n  startScope(name?: string) {\n    const scopeInfo: ScopeState = {\n      track: [],\n      name: 'unnamed scope',\n      id: this.state.nextScopeId++\n    };\n    if (name) {\n      scopeInfo.name = name;\n    }\n    this.state.scopeStack.push(scopeInfo);\n    this.state.activeScope = scopeInfo;\n  }\n\n  /**\n   * End a scope. Use this with startScope() to achieve the same functionality\n   * as scope() without the need for a function closure.\n   */\n  endScope(result?: TensorContainer) {\n    const tensorsToTrackInParent = getTensorsInContainer(result);\n    const tensorsToTrackInParentSet =\n        new Set(tensorsToTrackInParent.map(t => t.id));\n\n    // Dispose the arrays tracked in this scope.\n    for (let i = 0; i < this.state.activeScope.track.length; i++) {\n      const tensor = this.state.activeScope.track[i];\n      if (!tensor.kept && !tensorsToTrackInParentSet.has(tensor.id)) {\n        tensor.dispose();\n      }\n    }\n\n    const oldScope = this.state.scopeStack.pop();\n    this.state.activeScope = this.state.scopeStack.length === 0 ?\n        null :\n        this.state.scopeStack[this.state.scopeStack.length - 1];\n\n    // Track the current result in the parent scope.\n    tensorsToTrackInParent.forEach(tensor => {\n      // Only track the tensor if was allocated in the inner scope and is not\n      // globally kept.\n      if (!tensor.kept && tensor.scopeId === oldScope.id) {\n        this.track(tensor);\n      }\n    });\n  }\n\n  /**\n   * Returns gradients of `f` with respect to each of the `xs`. The gradients\n   * returned are of the same length as `xs`, but some might be null if `f`\n   * was not a function of that `x`. It also takes optional dy to multiply the\n   * gradient, which defaults to `1`.\n   */\n  gradients<T extends Tensor>(\n      f: () => T, xs: Tensor[], dy?: T,\n      allowNoGradients = false): {value: T, grads: Tensor[]} {\n    util.assert(\n        xs.length > 0, () => 'gradients() received an empty list of xs.');\n    if (dy != null && dy.dtype !== 'float32') {\n      throw new Error(`dy must have 'float32' dtype, but has '${dy.dtype}'`);\n    }\n\n    const y = this.scopedRun(\n        () => this.startTape(), () => this.endTape(),\n        () => this.tidy('forward', f));\n\n    util.assert(\n        y instanceof Tensor,\n        () => 'The result y returned by f() must be a tensor.');\n    // Filter out the nodes that don't connect x => y.\n    const filteredTape = getFilteredNodesXToY(this.state.activeTape, xs, y);\n    if (!allowNoGradients && filteredTape.length === 0 && xs.length > 0) {\n      throw new Error(\n          'Cannot compute gradient of y=f(x) with respect to x. Make sure ' +\n          'that the f you passed encloses all operations that lead from x ' +\n          'to y.');\n    }\n\n    return this.tidy('backward', () => {\n      const accumulatedGradientMap: {[tensorId: number]: Tensor} = {};\n      accumulatedGradientMap[y.id] = (dy == null) ? ones(y.shape) : dy;\n\n      // Backprop gradients through the filtered nodes.\n      backpropagateGradients(\n          accumulatedGradientMap, filteredTape,\n          // Pass the tidy function to avoid circular dep with `tape.ts`.\n          f => this.tidy(f as ScopeFn<Tensor>),\n          // Pass an add function to avoide a circular dep with `tape.ts`.\n          add);\n      const grads = xs.map(x => accumulatedGradientMap[x.id]);\n\n      if (this.state.gradientDepth === 0) {\n        // This means that we are not computing higher-order gradients\n        // and can clean up the tape.\n        this.state.activeTape.forEach(node => {\n          for (const tensor of node.saved) {\n            tensor.dispose();\n          }\n        });\n        this.state.activeTape = null;\n      }\n      return {value: y, grads};\n    });\n  }\n\n  customGrad<T extends Tensor>(f: CustomGradientFunc<T>):\n      (...args: Array<Tensor|GradSaveFunc>) => T {\n    util.assert(\n        util.isFunction(f),\n        () => 'The f passed in customGrad(f) must be a function.');\n    return (...inputs: Tensor[]): T => {\n      util.assert(\n          inputs.every(t => t instanceof Tensor),\n          () => 'The args passed in customGrad(f)(x1, x2,...) must all be ' +\n              'tensors');\n\n      let res: {\n        value: T,\n        gradFunc: (dy: T, saved: Tensor[]) => Tensor | Tensor[],\n      };\n      const inputMap: NamedTensorMap = {};\n      inputs.forEach((input, i) => {\n        inputMap[i] = input;\n      });\n\n      const forwardFunc: ForwardFunc<T> = (_, save) => {\n        res = f(...[...inputs, save]);\n        util.assert(\n            res.value instanceof Tensor,\n            () => 'The function f passed in customGrad(f) must return an ' +\n                'object where `obj.value` is a tensor');\n        util.assert(\n            util.isFunction(res.gradFunc),\n            () => 'The function f passed in customGrad(f) must return an ' +\n                'object where `obj.gradFunc` is a function.');\n        return res.value;\n      };\n\n      const backwardsFunc = (dy: T, saved: Tensor[]) => {\n        const gradRes = res.gradFunc(dy, saved);\n        const grads: Tensor[] = Array.isArray(gradRes) ? gradRes : [gradRes];\n        util.assert(\n            grads.length === inputs.length,\n            () => 'The function f passed in customGrad(f) must return an ' +\n                'object where `obj.gradFunc` is a function that returns ' +\n                'the same number of tensors as inputs passed to f(...).');\n        util.assert(\n            grads.every(t => t instanceof Tensor),\n            () => 'The function f passed in customGrad(f) must return an ' +\n                'object where `obj.gradFunc` is a function that returns ' +\n                'a list of only tensors.');\n        const gradMap: {[key: string]: () => Tensor} = {};\n        grads.forEach((grad, i) => {\n          gradMap[i] = () => grad;\n        });\n        return gradMap;\n      };\n\n      return this.runKernelFunc({\n        forwardFunc,\n        backwardsFunc,\n        inputs: inputMap,\n      });\n    };\n  }\n\n  readSync(dataId: DataId): BackendValues {\n    // Route the read to the correct backend.\n    const info = this.state.tensorInfo.get(dataId);\n    return info.backend.readSync(dataId);\n  }\n  read(dataId: DataId): Promise<BackendValues> {\n    // Route the read to the correct backend.\n    const info = this.state.tensorInfo.get(dataId);\n    return info.backend.read(dataId);\n  }\n\n  readToGPU(dataId: DataId, options?: DataToGPUOptions): GPUData {\n    // Route the read to the correct backend.\n    const info = this.state.tensorInfo.get(dataId);\n    return info.backend.readToGPU(dataId, options);\n  }\n\n  async time(query: () => void): Promise<TimingInfo> {\n    const start = now();\n    const timingInfo = await this.backend.time(query) as TimingInfo;\n    timingInfo.wallMs = now() - start;\n    return timingInfo;\n  }\n\n  /**\n   * Tracks a Tensor in the current scope to be automatically cleaned up\n   * when the current scope ends, and returns the value.\n   *\n   * @param result The Tensor to track in the current scope.\n   */\n  private track<T extends Tensor>(result: T): T {\n    if (this.state.activeScope != null) {\n      result.scopeId = this.state.activeScope.id;\n      this.state.activeScope.track.push(result);\n    }\n\n    return result;\n  }\n\n  get registeredVariables(): NamedVariableMap {\n    return this.state.registeredVariables;\n  }\n\n  /**\n   * Resets the engine state. Removes all backends but does not remove\n   * registered backend factories.\n   */\n  reset(): void {\n    // Make any pending promise obsolete.\n    this.pendingBackendInitId++;\n\n    this.state.dispose();\n    this.ENV.reset();\n    this.state = new EngineState();\n\n    for (const backendName in this.registry) {\n      this.disposeRegisteredKernels(backendName);\n      this.registry[backendName].dispose();\n      delete this.registry[backendName];\n    }\n    this.backendName = null;\n    this.backendInstance = null;\n    this.pendingBackendInit = null;\n  }\n}\n\nfunction ones(shape: number[]): Tensor {\n  const values = makeOnesTypedArray(sizeFromShape(shape), 'float32');\n  return ENGINE.makeTensor(values, shape, 'float32');\n}\n\nexport function getOrMakeEngine(): Engine {\n  const ns = getGlobalNamespace() as unknown as {_tfengine: Engine};\n  if (ns._tfengine == null) {\n    const environment = new Environment(ns);\n    ns._tfengine = new Engine(environment);\n  }\n  setEnvironmentGlobal(ns._tfengine.ENV);\n\n  // Tell the current tensor interface that the global engine is responsible\n  // for tracking.\n  setTensorTracker(() => ns._tfengine);\n  return ns._tfengine;\n}\n\nexport const ENGINE = getOrMakeEngine();\n\n/**\n * A implementation of the add op for use within engine and tape.\n *\n * This allows us to avoid a circular dependency between add.ts and engine.\n * It is exported to be available in tape tests.\n */\nexport function add(a: Tensor, b: Tensor): Tensor {\n  // We duplicate Add here to avoid a circular dependency with add.ts.\n  const inputs = {a, b};\n  return ENGINE.runKernel(Add, inputs as unknown as NamedTensorMap);\n}\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAiBA,SAAsCA,aAAa,QAAO,oBAAoB;AAC9E,SAAQC,WAAW,EAAEC,oBAAoB,QAAO,eAAe;AAC/D,SAAQC,kBAAkB,QAAO,eAAe;AAChD,SAAQC,GAAG,EAAEC,IAAI,EAAEC,QAAQ,QAAO,gBAAgB;AAClD,SAASC,WAAW,EAAEC,SAAS,EAAEC,oBAAoB,QAAgC,mBAAmB;AAExG,OAAO,KAAKC,GAAG,MAAM,OAAO;AAC5B,SAAuBC,QAAQ,QAAO,YAAY;AAClD,SAAQC,sBAAsB,EAAEC,oBAAoB,QAAiB,QAAQ;AAC7E,SAAmCC,gBAAgB,EAAEC,MAAM,EAAiBC,QAAQ,QAAO,UAAU;AAGrG,SAAQC,qBAAqB,QAAO,eAAe;AAEnD,OAAO,KAAKC,IAAI,MAAM,QAAQ;AAC9B,SAAQC,oBAAoB,EAAEC,kBAAkB,EAAEC,GAAG,EAAEC,aAAa,QAAO,QAAQ;AAuEnF,SAASC,4BAA4BA,CAEjCC,gBACgC;EAElC,OAAQA,gBAAkD,CAACC,UAAU,IAAI,IAAI;AAC/E;AAEA,MAAMC,WAAW;EAAjBC,YAAA;IACE;IACA,KAAAC,mBAAmB,GAAqB,EAAE;IAE1C,KAAAC,cAAc,GAAG,CAAC;IAClB,KAAAC,QAAQ,GAAG,CAAC;IACZ,KAAAC,UAAU,GAAG,CAAC;IACd,KAAAC,gBAAgB,GAAG,CAAC;IACpB,KAAAC,cAAc,GAAG,CAAC;IAGlB;IACA;IACA;IACA,KAAAC,aAAa,GAAG,CAAC;IACjB;IACA;IACA,KAAAC,WAAW,GAAG,CAAC;IAIf,KAAAC,UAAU,GAAiB,EAAE;IAC7B;;;;IAIA,KAAAC,iBAAiB,GAAa,EAAE;IAChC,KAAAC,WAAW,GAAG,CAAC;IAEf,KAAAC,UAAU,GAAG,IAAIC,OAAO,EAKpB;IAEJ,KAAAC,SAAS,GAAG,KAAK;IACjB,KAAAC,aAAa,GAAgB;MAC3BC,QAAQ,EAAE,CAAC;MACXC,UAAU,EAAE,CAAC;MACbC,SAAS,EAAE,CAAC;MACZC,OAAO,EAAE,EAAE;MACXC,MAAM,EAAE,IAAI;MACZ,IAAIC,WAAWA,CAAA;QAET,OAAOC,KAAK,CAACC,IAAI,CAAC,IAAIC,GAAG,CAAC,IAAI,CAACL,OAAO,CAACM,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACC,IAAI,CAAC,CAAC,CAAC;MAC3D;KACL;EAOH;EALEC,OAAOA,CAAA;IACL,KAAK,MAAMC,YAAY,IAAI,IAAI,CAAC5B,mBAAmB,EAAE;MACnD,IAAI,CAACA,mBAAmB,CAAC4B,YAAY,CAAC,CAACD,OAAO,EAAE;;EAEpD;;AAGF,MAAaE,MAAM;EAgBjB9B,YAAmB+B,GAAgB;IAAhB,KAAAA,GAAG,GAAHA,GAAG;IAbtB,KAAAC,QAAQ,GAAkC,EAAE;IAC5C,KAAAC,eAAe,GAKX,EAAE;IAKE,KAAAC,oBAAoB,GAAG,CAAC;IAG9B,IAAI,CAACC,KAAK,GAAG,IAAIpC,WAAW,EAAE;EAChC;EAEA,MAAMqC,KAAKA,CAAA;IACT,IAAI,IAAI,CAACC,kBAAkB,IAAI,IAAI,EAAE;MACnC,OAAO,IAAI,CAACA,kBAAkB,CAACC,IAAI,CAAC,MAAK,CAAE,CAAC,CAAC;;IAE/C,IAAI,IAAI,CAACC,eAAe,IAAI,IAAI,EAAE;MAChC;;IAEF,MAAMC,cAAc,GAAG,IAAI,CAACC,iBAAiB,EAAE;IAE/C,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,cAAc,CAACG,MAAM,EAAED,CAAC,EAAE,EAAE;MAC9C,MAAME,WAAW,GAAGJ,cAAc,CAACE,CAAC,CAAC;MACrC,MAAMG,OAAO,GAAG,MAAM,IAAI,CAACC,iBAAiB,CAACF,WAAW,CAAC,CAACC,OAAO;MACjE,IAAIA,OAAO,EAAE;QACX,MAAM,IAAI,CAACE,UAAU,CAACH,WAAW,CAAC;QAClC;;;IAIJ,MAAM,IAAII,KAAK,CACX,iEAAiE,GACjE,SAAS,CAAC;EAChB;EAEA,IAAIC,OAAOA,CAAA;IACT,IAAI,IAAI,CAACZ,kBAAkB,IAAI,IAAI,EAAE;MACnC,MAAM,IAAIW,KAAK,CACX,YAAY,IAAI,CAACJ,WAAW,uCAAuC,GACnE,mEAAmE,GACnE,eAAe,CAAC;;IAEtB,IAAI,IAAI,CAACL,eAAe,IAAI,IAAI,EAAE;MAChC,MAAM;QAACZ,IAAI;QAAEuB;MAAS,CAAC,GAAG,IAAI,CAACC,+BAA+B,EAAE;MAChE,IAAID,SAAS,EAAE;QACb,MAAM,IAAIF,KAAK,CACX,iCAAiCrB,IAAI,qBAAqB,GAC1D,gDAAgD,GAChD,oDAAoD,CAAC;;MAE3D,IAAI,CAACoB,UAAU,CAACpB,IAAI,CAAC;;IAEvB,OAAO,IAAI,CAACY,eAAe;EAC7B;EAEAa,YAAYA,CAAA;IACV,OAAOC,MAAM,CAACC,IAAI,CAAC,IAAI,CAACrB,eAAe,CAAC;EAC1C;EAEAsB,WAAWA,CAACX,WAAmB;IAC7B,IAAI,EAAEA,WAAW,IAAI,IAAI,CAACZ,QAAQ,CAAC,EAAE;MACnC;MACA;MACA,IAAIY,WAAW,IAAI,IAAI,CAACX,eAAe,EAAE;QACvC,MAAM;UAACiB;QAAS,CAAC,GAAG,IAAI,CAACJ,iBAAiB,CAACF,WAAW,CAAC;QACvD,IAAIM,SAAS,EAAE;UACb;UACA,OAAO,IAAI;;OAEd,MAAM;QACL,OAAO,IAAI;;;IAGf,OAAO,IAAI,CAAClB,QAAQ,CAACY,WAAW,CAAC;EACnC;EAEAY,kBAAkBA,CAACZ,WAAmB;IAEpC,IAAI,EAAEA,WAAW,IAAI,IAAI,CAACX,eAAe,CAAC,EAAE;MAC1C,OAAO,IAAI;;IAEb,OAAO,IAAI,CAACA,eAAe,CAACW,WAAW,CAAC,CAACa,OAAO;EAClD;EAEAC,eAAeA,CACXd,WAAmB,EACnBa,OAAqD,EACrDE,QAAQ,GAAG,CAAC;IACd,IAAIf,WAAW,IAAI,IAAI,CAACX,eAAe,EAAE;MACvClD,GAAG,CAAC6E,IAAI,CACJ,GAAGhB,WAAW,mCAAmC,GACjD,mCAAmC,CAAC;MACxC,OAAO,KAAK;;IAEd,IAAI,CAACX,eAAe,CAACW,WAAW,CAAC,GAAG;MAACa,OAAO;MAAEE;IAAQ,CAAC;IACvD,OAAO,IAAI;EACb;EAEA,MAAMZ,UAAUA,CAACH,WAAmB;IAClC,IAAI,IAAI,CAACX,eAAe,CAACW,WAAW,CAAC,IAAI,IAAI,EAAE;MAC7C,MAAM,IAAII,KAAK,CAAC,iBAAiBJ,WAAW,yBAAyB,CAAC;;IAExE,IAAI,CAACA,WAAW,GAAGA,WAAW;IAC9B,IAAI,IAAI,CAACZ,QAAQ,CAACY,WAAW,CAAC,IAAI,IAAI,EAAE;MACtC,IAAI,CAACL,eAAe,GAAG,IAAI;MAC3B,MAAM;QAACM,OAAO;QAAEK;MAAS,CAAC,GAAG,IAAI,CAACJ,iBAAiB,CAACF,WAAW,CAAC;MAChE,MAAMxB,MAAM,GAAG8B,SAAS,GAAG,MAAML,OAAO,GAAGA,OAAO;MAClD,IAAI,CAACzB,MAAM,EAAE;QACX,OAAO,KAAK;;;IAGhB,IAAI,CAACmB,eAAe,GAAG,IAAI,CAACP,QAAQ,CAACY,WAAW,CAAC;IACjD,IAAI,CAACiB,sBAAsB,EAAE;IAC7B;IACA,IAAI,CAACC,QAAQ,GAAG,IAAI9E,QAAQ,CAAC,IAAI,CAACuD,eAAe,CAAC;IAElD,OAAO,IAAI;EACb;EAEQsB,sBAAsBA,CAAA;IAC5B,MAAM1C,OAAO,GAAGrC,oBAAoB,CAAC,IAAI,CAAC8D,WAAW,CAAC;IACtDzB,OAAO,CAAC4C,OAAO,CAACC,MAAM,IAAG;MACvB,IAAIA,MAAM,CAACC,SAAS,IAAI,IAAI,EAAE;QAC5BD,MAAM,CAACC,SAAS,CAAC,IAAI,CAAC1B,eAAe,CAAC;;IAE1C,CAAC,CAAC;EACJ;EAEQ2B,wBAAwBA,CAACtB,WAAmB;IAClD,MAAMzB,OAAO,GAAGrC,oBAAoB,CAAC8D,WAAW,CAAC;IACjDzB,OAAO,CAAC4C,OAAO,CAACC,MAAM,IAAG;MACvB,IAAIA,MAAM,CAACG,WAAW,IAAI,IAAI,EAAE;QAC9BH,MAAM,CAACG,WAAW,CAAC,IAAI,CAACnC,QAAQ,CAACY,WAAW,CAAC,CAAC;;IAElD,CAAC,CAAC;EACJ;EAEA;;;;;;EAMQE,iBAAiBA,CAACF,WAAmB;IAE3C,MAAMwB,oBAAoB,GAAG,IAAI,CAACnC,eAAe,CAACW,WAAW,CAAC;IAC9D,IAAIwB,oBAAoB,IAAI,IAAI,EAAE;MAChC,MAAM,IAAIpB,KAAK,CACX,6BAA6BJ,WAAW,0BAA0B,CAAC;;IAGzE,IAAI;MACF,MAAMK,OAAO,GAAGmB,oBAAoB,CAACX,OAAO,EAAE;MAC9C;;;;;MAKA,IAAIR,OAAO,IAAI,EAAEA,OAAO,YAAY5E,aAAa,CAAC,IAC9C,OAAO4E,OAAO,CAACX,IAAI,KAAK,UAAU,EAAE;QACtC,MAAM+B,SAAS,GAAG,EAAE,IAAI,CAACnC,oBAAoB;QAC7C,MAAMW,OAAO,GACTI,OAAO,CACFX,IAAI,CAACC,eAAe,IAAG;UACtB;UACA,IAAI8B,SAAS,GAAG,IAAI,CAACnC,oBAAoB,EAAE;YACzC,OAAO,KAAK;;UAEd,IAAI,CAACF,QAAQ,CAACY,WAAW,CAAC,GAAGL,eAAe;UAC5C,IAAI,CAACF,kBAAkB,GAAG,IAAI;UAC9B,OAAO,IAAI;QACb,CAAC,CAAC,CACDiC,KAAK,CAACC,GAAG,IAAG;UACX;UACA,IAAIF,SAAS,GAAG,IAAI,CAACnC,oBAAoB,EAAE;YACzC,OAAO,KAAK;;UAEd,IAAI,CAACG,kBAAkB,GAAG,IAAI;UAC9BtD,GAAG,CAAC6E,IAAI,CAAC,6BAA6BhB,WAAW,SAAS,CAAC;UAC3D7D,GAAG,CAAC6E,IAAI,CAACW,GAAG,CAACC,KAAK,IAAID,GAAG,CAACE,OAAO,CAAC;UAClC,OAAO,KAAK;QACd,CAAC,CAAC;QACV,IAAI,CAACpC,kBAAkB,GAAGQ,OAAO;QACjC,OAAO;UAACA,OAAO;UAAEK,SAAS,EAAE;QAAI,CAAC;OAClC,MAAM;QACL,IAAI,CAAClB,QAAQ,CAACY,WAAW,CAAC,GAAGK,OAAwB;QACrD,OAAO;UAACJ,OAAO,EAAE,IAAI;UAAEK,SAAS,EAAE;QAAK,CAAC;;KAE3C,CAAC,OAAOqB,GAAG,EAAE;MACZxF,GAAG,CAAC6E,IAAI,CAAC,6BAA6BhB,WAAW,SAAS,CAAC;MAC3D7D,GAAG,CAAC6E,IAAI,CAACW,GAAG,CAACC,KAAK,IAAID,GAAG,CAACE,OAAO,CAAC;MAClC,OAAO;QAAC5B,OAAO,EAAE,KAAK;QAAEK,SAAS,EAAE;MAAK,CAAC;;EAE7C;EAEAwB,aAAaA,CAAC9B,WAAmB;IAC/B,IAAI,EAAEA,WAAW,IAAI,IAAI,CAACX,eAAe,CAAC,EAAE;MAC1C,MAAM,IAAIe,KAAK,CAAC,GAAGJ,WAAW,gCAAgC,CAAC;;IAEjE,IAAI,IAAI,CAACA,WAAW,KAAKA,WAAW,IAAI,IAAI,CAACP,kBAAkB,IAAI,IAAI,EAAE;MACvE;MACA;MACA,IAAI,CAACH,oBAAoB,EAAE;;IAG7B,IAAIU,WAAW,IAAI,IAAI,CAACZ,QAAQ,EAAE;MAChC,IAAI,CAACkC,wBAAwB,CAACtB,WAAW,CAAC;MAC1C,IAAI,CAACZ,QAAQ,CAACY,WAAW,CAAC,CAAChB,OAAO,EAAE;MACpC,OAAO,IAAI,CAACI,QAAQ,CAACY,WAAW,CAAC;;IAGnC,OAAO,IAAI,CAACX,eAAe,CAACW,WAAW,CAAC;IAExC;IACA,IAAI,IAAI,CAACA,WAAW,KAAKA,WAAW,EAAE;MACpC,IAAI,CAACP,kBAAkB,GAAG,IAAI;MAC9B,IAAI,CAACO,WAAW,GAAG,IAAI;MACvB,IAAI,CAACL,eAAe,GAAG,IAAI;;EAE/B;EAEQE,iBAAiBA,CAAA;IACvB,IAAIY,MAAM,CAACC,IAAI,CAAC,IAAI,CAACrB,eAAe,CAAC,CAACU,MAAM,KAAK,CAAC,EAAE;MAClD,MAAM,IAAIK,KAAK,CAAC,+BAA+B,CAAC;;IAElD,OAAOK,MAAM,CAACC,IAAI,CAAC,IAAI,CAACrB,eAAe,CAAC,CAAC0C,IAAI,CAAC,CAACC,CAAS,EAAEC,CAAS,KAAI;MACrE;MACA,OAAO,IAAI,CAAC5C,eAAe,CAAC4C,CAAC,CAAC,CAAClB,QAAQ,GACnC,IAAI,CAAC1B,eAAe,CAAC2C,CAAC,CAAC,CAACjB,QAAQ;IACtC,CAAC,CAAC;EACJ;EAEQR,+BAA+BA,CAAA;IAErC,MAAMX,cAAc,GAAG,IAAI,CAACC,iBAAiB,EAAE;IAE/C,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,cAAc,CAACG,MAAM,EAAED,CAAC,EAAE,EAAE;MAC9C,MAAME,WAAW,GAAGJ,cAAc,CAACE,CAAC,CAAC;MACrC,MAAM;QAACG,OAAO;QAAEK;MAAS,CAAC,GAAG,IAAI,CAACJ,iBAAiB,CAACF,WAAW,CAAC;MAChE,IAAIM,SAAS,IAAIL,OAAO,EAAE;QACxB,OAAO;UAAClB,IAAI,EAAEiB,WAAW;UAAEM;QAAS,CAAC;;;IAGzC,MAAM,IAAIF,KAAK,CACX,iEAAiE,GACjE,SAAS,CAAC;EAChB;EAEA8B,QAAQA,CAAC7B,OAAsB,EAAE8B,MAAc;IAC7C,MAAMC,IAAI,GAAG,IAAI,CAAC7C,KAAK,CAACvB,UAAU,CAACqE,GAAG,CAACF,MAAM,CAAC;IAC9C,MAAMG,UAAU,GAAGF,IAAI,CAAC/B,OAAO;IAC/B,MAAMkC,MAAM,GAAG,IAAI,CAACC,QAAQ,CAACL,MAAM,CAAC;IACpC,MAAMM,QAAQ,GAAGH,UAAU,CAACG,QAAQ,CAACN,MAAM,CAAC;IAC5C;IACA;IACAG,UAAU,CAACI,WAAW,CAACP,MAAM,EAAE,IAAI,CAAC;IACpCC,IAAI,CAAC/B,OAAO,GAAGA,OAAO;IACtBA,OAAO,CAACsC,IAAI,CAACR,MAAM,EAAEI,MAAM,EAAEH,IAAI,CAACQ,KAAK,EAAER,IAAI,CAACS,KAAK,EAAEJ,QAAQ,CAAC;IAC9D,IAAI,IAAI,CAACK,sBAAsB,EAAE,EAAE;MACjC;MACA;MACA,IAAI,CAACvD,KAAK,CAACzB,iBAAiB,CAAC,IAAI,CAACyB,KAAK,CAACzB,iBAAiB,CAACiC,MAAM,GAAG,CAAC,CAAC,EAAE;;EAE3E;EAEAgD,IAAIA,CAA4BC,QAA2B,EAAEC,EAAe;IAE1E,IAAIlE,IAAI,GAAW,IAAI;IACvB,IAAIkE,EAAE,IAAI,IAAI,EAAE;MACd;MACA,IAAI,OAAOD,QAAQ,KAAK,UAAU,EAAE;QAClC,MAAM,IAAI5C,KAAK,CAAC,qCAAqC,CAAC;;MAExD6C,EAAE,GAAGD,QAAQ;KACd,MAAM;MACL;MACA,IAAI,OAAOA,QAAQ,KAAK,QAAQ,IAAI,EAAEA,QAAQ,YAAYE,MAAM,CAAC,EAAE;QACjE,MAAM,IAAI9C,KAAK,CACX,sDAAsD,GACtD,4BAA4B,CAAC;;MAEnC,IAAI,OAAO6C,EAAE,KAAK,UAAU,EAAE;QAC5B,MAAM,IAAI7C,KAAK,CACX,oDAAoD,GACpD,8BAA8B,CAAC;;MAErCrB,IAAI,GAAGiE,QAAkB;MACzB;MACA;;IAEF,IAAIxE,MAAS;IACb,OAAO,IAAI,CAAC2E,SAAS,CACjB,MAAM,IAAI,CAACC,UAAU,CAACrE,IAAI,CAAC,EAAE,MAAM,IAAI,CAACsE,QAAQ,CAAC7E,MAAM,CAAC,EAAE,MAAK;MAC7DA,MAAM,GAAGyE,EAAE,EAAE;MACb,IAAIzE,MAAM,YAAY8E,OAAO,EAAE;QAC7BC,OAAO,CAACC,KAAK,CAAC,yCAAyC,CAAC;;MAE1D,OAAOhF,MAAM;IACf,CAAC,CAAC;EACR;EAEQ2E,SAASA,CAAIM,KAAiB,EAAEC,GAAe,EAAEC,CAAU;IACjEF,KAAK,EAAE;IACP,IAAI;MACF,MAAMG,GAAG,GAAGD,CAAC,EAAE;MACfD,GAAG,EAAE;MACL,OAAOE,GAAG;KACX,CAAC,OAAOC,EAAE,EAAE;MACXH,GAAG,EAAE;MACL,MAAMG,EAAE;;EAEZ;EAGQC,YAAYA,CAAA;IAClB,OAAO5E,MAAM,CAAC4E,YAAY,EAAE;EAC9B;EAGQC,cAAcA,CAAA;IACpB,OAAO7E,MAAM,CAAC6E,cAAc,EAAE;EAChC;EAEA;;;;;;EAMQC,KAAKA,CAACC,CAAS;IACrB,MAAMC,CAAC,GAAWC,MAAM,CAACC,SAAS,CAACrI,QAAQ,EACR;MAACkI;IAAC,CAA8B,CAAC;IACpE,MAAMI,MAAM,GAAG;MAACJ;IAAC,CAAC;IAClB,MAAMK,IAAI,GAAIC,EAAU,KAAM;MAC5BN,CAAC,EAAEA,CAAA,KAAK;QACN,MAAMpB,KAAK,GAAG,SAAS;QACvB,MAAM2B,UAAU,GAAG;UAACP,CAAC,EAAEM;QAAE,CAAC;QAC1B,MAAME,KAAK,GAAG;UAAC5B;QAAK,CAAC;QAErB,OAAOsB,MAAM,CAACC,SAAS,CACZtI,IAAI,EAAE0I,UAAuC;QAC7C;QACAC,KAAgC,CAAW;MACxD;KACD,CAAC;IACF,MAAMC,KAAK,GAAa,EAAE;IAC1B,IAAI,CAACC,WAAW,CAAC,IAAI,CAACpF,KAAK,CAACqF,WAAW,CAAC7F,IAAI,EAAEsF,MAAM,EAAE,CAACH,CAAC,CAAC,EAAEI,IAAI,EAAEI,KAAK,EAAE,EAAE,CAAC;IAC3E,OAAOR,CAAC;EACV;EAEA;;;;;;;;;;;;;EAaAE,SAASA,CACLlH,UAAkB,EAAEmH,MAAsB,EAAEI,KAAoB;IAClE,IAAI,IAAI,CAACzE,WAAW,IAAI,IAAI,EAAE;MAC5B;MACA;MACA;MACA;MACA;MACA,IAAI,CAACK,OAAO;;IAEd,MAAMwE,SAAS,GAAG5I,SAAS,CAACiB,UAAU,EAAE,IAAI,CAAC8C,WAAW,CAAC,IAAI,IAAI;IACjE,IAAI,CAAC6E,SAAS,EAAE;MACd,MAAM,IAAIzE,KAAK,CAAC,WAAWlD,UAAU,iCACjC,IAAI,CAAC8C,WAAW,GAAG,CAAC;;IAE1B,OAAO,IAAI,CAAC8E,aAAa,CAAC;MAAC5H,UAAU;MAAEmH,MAAM;MAAEI;IAAK,CAAC,CAAC;EACxD;EAEQ3B,sBAAsBA,CAAA;IAC5B,OAAO,IAAI,CAAC3D,GAAG,CAAC4F,OAAO,CAAC,SAAS,CAAC;EACpC;EAEQC,qBAAqBA,CACzB9H,UAAkB,EAAE+H,gBAAwB,EAC5CC,QAAsB;IACxB,MAAMC,eAAe,GAAG,IAAI,CAAC9E,OAAO,CAAC+E,UAAU,EAAE;IAEjD;IACA,IAAIC,gBAAgB,GAAG,CAAC;IACxBH,QAAQ,CAAC/D,OAAO,CAACiB,IAAI,IAAG;MACtB;MACA;MACAiD,gBAAgB,IAAKjD,IAAI,CAACS,KAAK,KAAK,WAAW,GAAG,CAAC,GAAG,CAAE;IAC1D,CAAC,CAAC;IAEF;IACA;IACA;IACA;IACA;IACA,MAAMyC,QAAQ,GACV,IAAI,CAAC/F,KAAK,CAACzB,iBAAiB,CAAC,IAAI,CAACyB,KAAK,CAACzB,iBAAiB,CAACiC,MAAM,GAAG,CAAC,CAAC;IACzE,MAAMwF,aAAa,GACfJ,eAAe,GAAGF,gBAAgB,GAAGI,gBAAgB,GAAGC,QAAQ;IACpE,IAAIC,aAAa,GAAG,CAAC,EAAE;MACrB,MAAM,IAAInF,KAAK,CACX,YAAY,IAAI,CAACJ,WAAW,gCAAgC,GAC5D,IAAIuF,aAAa,6BAA6BrI,UAAU,GAAG,CAAC;;EAEpE;EAEA;;;;;EAKQ4H,aAAaA,CACjBU,YACgC;IAClC,IAAIC,OAAiB;IACrB,IAAIf,KAAK,GAAa,EAAE;IACxB,MAAMgB,QAAQ,GAAG,IAAI,CAACA,QAAQ,EAAE;IAEhC,MAAMC,iBAAiB,GAAG,IAAI,CAACpG,KAAK,CAAChC,QAAQ;IAC7C,MAAMqI,kBAAkB,GAAG,IAAI,CAACrG,KAAK,CAAC/B,UAAU;IAEhD,IAAI,IAAI,CAACsF,sBAAsB,EAAE,EAAE;MACjC,IAAI,CAACvD,KAAK,CAACzB,iBAAiB,CAAC+H,IAAI,CAAC,CAAC,CAAC;;IAGtC,IAAIC,UAA0B;IAC9B,IAAI,IAAI,CAAC9F,WAAW,IAAI,IAAI,EAAE;MAC5B;MACA;MACA;MACA;MACA;MACA,IAAI,CAACK,OAAO;;IAGd,IAAI0F,GAA4B;IAEhC,MAAMC,iBAAiB,GAAGhJ,4BAA4B,CAACwI,YAAY,CAAC,GAChEA,YAAY,CAACtI,UAAU,GACvB,IAAI,CAACqC,KAAK,CAACqF,WAAW,IAAI,IAAI,GAAG,IAAI,CAACrF,KAAK,CAACqF,WAAW,CAAC7F,IAAI,GAAG,EAAE;IAErE;IACA;IACA;IAEA,IAAI/B,4BAA4B,CAACwI,YAAY,CAAC,EAAE;MAC9C,MAAM;QAACtI,UAAU;QAAEmH,MAAM;QAAEI;MAAK,CAAC,GAAGe,YAAY;MAChD,IAAI,IAAI,CAACxF,WAAW,IAAI,IAAI,EAAE;QAC5B;QACA;QACA;QACA;QACA;QACA,IAAI,CAACK,OAAO;;MAEd,MAAMe,MAAM,GAAGnF,SAAS,CAACiB,UAAU,EAAE,IAAI,CAAC8C,WAAW,CAAC;MACtDrD,IAAI,CAACsJ,MAAM,CACP7E,MAAM,IAAI,IAAI,EACd,MAAM,kCAAkClE,UAAU,kBAC9C,IAAI,CAAC8C,WAAW,GAAG,CAAC;MAE5B8F,UAAU,GAAGA,CAAA,KAAK;QAChB,MAAMb,gBAAgB,GAAG,IAAI,CAAC5E,OAAO,CAAC+E,UAAU,EAAE;QAClDW,GAAG,GAAG3E,MAAM,CAAC0E,UAAU,CAAC;UAACzB,MAAM;UAAEI,KAAK;UAAEpE,OAAO,EAAE,IAAI,CAACA;QAAO,CAAC,CAAC;QAC/D,MAAM6E,QAAQ,GAAGxG,KAAK,CAACwH,OAAO,CAACH,GAAG,CAAC,GAAGA,GAAG,GAAG,CAACA,GAAG,CAAC;QACjD,IAAI,IAAI,CAACjD,sBAAsB,EAAE,EAAE;UACjC,IAAI,CAACkC,qBAAqB,CAAC9H,UAAU,EAAE+H,gBAAgB,EAAEC,QAAQ,CAAC;;QAGpE,MAAMiB,UAAU,GAAGjB,QAAQ,CAACrG,GAAG,CAAEuH,OAA0B,IAAI;UAC7D;UACA;UACA;UACA,IAAKA,OAAkB,CAACC,IAAI,IAAI,IAAI,EAAE;YACpC,OAAOD,OAAiB;;UAE1B,OAAO,IAAI,CAACE,wBAAwB,CAACF,OAAO,CAAC;QAC/C,CAAC,CAAC;QAEF;QAEA;QACA;QACA;QACA,IAAIV,QAAQ,EAAE;UACZ,MAAMa,aAAa,GACf,IAAI,CAACC,qBAAqB,CAACtJ,UAAU,EAAEmH,MAAM,EAAE8B,UAAU,CAAC;UAC9DzB,KAAK,GAAG,IAAI,CAAC+B,0BAA0B,CAACF,aAAa,CAAC;;QAExD,OAAOJ,UAAU;MACnB,CAAC;KACF,MAAM;MACL,MAAM;QAACO;MAAW,CAAC,GAAGlB,YAAY;MAClC;MACA,MAAMmB,QAAQ,GAAkBC,OAAO,IAAI;QACzC;QACA;QACA;QACA,IAAI,CAAClB,QAAQ,EAAE;UACb;;QAEFhB,KAAK,GAAGkC,OAAO,CAAC/H,GAAG,CAACgI,MAAM,IAAI,IAAI,CAACC,IAAI,CAAC,IAAI,CAAC9C,KAAK,CAAC6C,MAAM,CAAC,CAAC,CAAC;MAC9D,CAAC;MAEDf,UAAU,GAAGA,CAAA,KAAK;QAChB,MAAMb,gBAAgB,GAAG,IAAI,CAAC5E,OAAO,CAAC+E,UAAU,EAAE;QAClDW,GAAG,GAAG,IAAI,CAAChD,IAAI,CAAC,MAAM2D,WAAW,CAAC,IAAI,CAACrG,OAAO,EAAEsG,QAAQ,CAAC,CAAC;QAC1D,MAAMI,IAAI,GAAIrI,KAAK,CAACwH,OAAO,CAACH,GAAG,CAAC,GAAGA,GAAG,GAAG,CAACA,GAAG,CAAc;QAC3D,IAAI,IAAI,CAACjD,sBAAsB,EAAE,EAAE;UACjC;UACA,IAAI,CAACkC,qBAAqB,CAACgB,iBAAiB,EAAEf,gBAAgB,EAAE8B,IAAI,CAAC;;QAEvE,OAAOA,IAAI;MACb,CAAC;;IAGH;IACA;IACA;IACA,MAAM;MAAC1C,MAAM;MAAEI;IAAK,CAAC,GAAGe,YAAY;IACpC,MAAMwB,aAAa,GAAGhK,4BAA4B,CAACwI,YAAY,CAAC,GAC5D,IAAI,GACJA,YAAY,CAACwB,aAAa;IAE9B,IAAIC,aAA4B;IAChC,IAAI,CAAC9D,SAAS;IACV;IACA,MAAM,IAAI,CAAC5D,KAAK,CAAC3B,WAAW,EAAE,EAAE,MAAM,IAAI,CAAC2B,KAAK,CAAC3B,WAAW,EAAE,EAAE,MAAK;MACnE,IAAI,CAAC,IAAI,CAACuB,GAAG,CAAC4F,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAACxF,KAAK,CAACrB,SAAS,EAAE;QACvDuH,OAAO,GAAGK,UAAU,EAAE;OACvB,MAAM;QACLmB,aAAa,GAAG,IAAI,CAAC/F,QAAQ,CAACgG,aAAa,CACvClB,iBAAiB,EAAE3B,MAAM,EAAE,MAAMyB,UAAU,EAAE,CAAC;QAClD,IAAI,IAAI,CAAC3G,GAAG,CAAC4F,OAAO,CAAC,OAAO,CAAC,EAAE;UAC7B,IAAI,CAAC7D,QAAQ,CAACiG,gBAAgB,CAACF,aAAa,CAAC;;QAE/CxB,OAAO,GAAGwB,aAAa,CAACxB,OAAO;;IAEnC,CAAC,CAAC;IAEN,IAAIC,QAAQ,EAAE;MACZ,IAAI,CAACf,WAAW,CACZqB,iBAAiB,EAAE3B,MAAM,EAAEoB,OAAO,EAAEuB,aAAa,EAAEtC,KAAK,EAAED,KAAK,CAAC;;IAGtE,IAAI,IAAI,CAAClF,KAAK,CAACrB,SAAS,EAAE;MACxB,IAAI,CAACqB,KAAK,CAACpB,aAAa,CAACI,OAAO,CAACsH,IAAI,CAAC;QACpC9G,IAAI,EAAEiH,iBAAiB;QACvBoB,UAAU,EAAE,IAAI,CAAC7H,KAAK,CAAChC,QAAQ,GAAGoI,iBAAiB;QACnD0B,kBAAkB,EAAE,IAAI,CAAC9H,KAAK,CAAChC,QAAQ;QACvC+J,YAAY,EAAE,IAAI,CAAC/H,KAAK,CAAC/B,UAAU,GAAGoI,kBAAkB;QACxD2B,oBAAoB,EAAE,IAAI,CAAChI,KAAK,CAAC/B,UAAU;QAC3CgK,WAAW,EAAE/G,MAAM,CAACC,IAAI,CAAC2D,MAAM,CAAC,CAACxF,GAAG,CAChC4I,GAAG,IAAIpD,MAAM,CAACoD,GAAG,CAAC,IAAI,IAAI,GAAGpD,MAAM,CAACoD,GAAG,CAAC,CAAC7E,KAAK,GAAG,IAAI,CAAC;QAC1D8E,YAAY,EAAEjC,OAAO,CAAC5G,GAAG,CAAC8I,IAAI,IAAIA,IAAI,CAAC/E,KAAK,CAAC;QAC7CgF,YAAY,EAAEX,aAAa,CAACY,MAAM;QAClCC,SAAS,EAAEb,aAAa,CAACa;OAC1B,CAAC;;IAEJ,OAAQpJ,KAAK,CAACwH,OAAO,CAACH,GAAG,CAAC,GAAGN,OAAO,GAAGA,OAAO,CAAC,CAAC,CAAC;EACnD;EAEA;;;;;EAKQgB,0BAA0BA,CAACG,OAAiB;IAClD,MAAMlC,KAAK,GAAGkC,OAAO,CAAC/H,GAAG,CAACgI,MAAM,IAAI,IAAI,CAACC,IAAI,CAAC,IAAI,CAAC9C,KAAK,CAAC6C,MAAM,CAAC,CAAC,CAAC;IAClE,OAAOnC,KAAK;EACd;EAEA;;;;;;;EAOQ8B,qBAAqBA,CACzBtJ,UAAkB,EAAEmH,MAAsB,EAC1CoB,OAAiB;IACnB,MAAMsC,UAAU,GAAG/L,WAAW,CAACkB,UAAU,CAAC;IAC1C,IAAI6K,UAAU,IAAI,IAAI,EAAE;MACtB,MAAMC,YAAY,GAAaD,UAAU,CAACC,YAAY,IAAI,EAAE;MAC5D,MAAMC,aAAa,GAAcF,UAAU,CAACE,aAAa,IAAI,EAAE;MAE/D;MACA;MACA,IAAIC,kBAA4B;MAChC,IAAIH,UAAU,CAACI,aAAa,EAAE;QAC5BxL,IAAI,CAACsJ,MAAM,CACPvH,KAAK,CAACwH,OAAO,CAAC7B,MAAM,CAAC,EACrB,MAAM,wDAAwD,CAAC;QAEnE6D,kBAAkB,GAAGzH,MAAM,CAACC,IAAI,CAAC2D,MAAM,CAAC,CAACxF,GAAG,CAAE4I,GAAG,IAAKpD,MAAM,CAACoD,GAAG,CAAC,CAAC;OACnE,MAAM;QACLS,kBAAkB,GAAGF,YAAY,CAACnJ,GAAG,CAAEuJ,SAAS,IAAK/D,MAAM,CAAC+D,SAAS,CAAC,CAAC;;MAGzE,MAAMC,mBAAmB,GACrB5C,OAAO,CAAC6C,MAAM,CAAC,CAACC,CAAC,EAAEzI,CAAC,KAAKmI,aAAa,CAACnI,CAAC,CAAC,CAAC;MAE9C,OAAOoI,kBAAkB,CAACM,MAAM,CAACH,mBAAmB,CAAC;;IAEvD;IACA;IACA;IACA;IACA;IACA;IACA,OAAO,EAAE;EACX;EAEA;;;;;EAKAI,UAAUA,CACNlG,MAAkB,EAAEK,KAAe,EAAEC,KAAe,EACpDxC,OAAuB;IACzB,IAAIkC,MAAM,IAAI,IAAI,EAAE;MAClB,MAAM,IAAInC,KAAK,CAAC,+CAA+C,CAAC;;IAElEyC,KAAK,GAAGA,KAAK,IAAI,SAAS;IAC1BxC,OAAO,GAAGA,OAAO,IAAI,IAAI,CAACA,OAAO;IACjC,IAAIqI,WAAW,GAAGnG,MAAuB;IACzC,IAAIM,KAAK,KAAK,QAAQ,IAAIlG,IAAI,CAACgM,QAAQ,CAACpG,MAAM,CAAC,CAAC,CAAC,CAAC,EAAE;MAClDmG,WAAW,GAAInG,MAAmB,CAAC1D,GAAG,CAAC+J,CAAC,IAAIjM,IAAI,CAACkM,YAAY,CAACD,CAAC,CAAC,CAAC;;IAEnE,MAAMzG,MAAM,GAAG9B,OAAO,CAACyI,KAAK,CAACJ,WAAW,EAAE9F,KAAK,EAAEC,KAAK,CAAC;IACvD,MAAMkG,CAAC,GAAG,IAAIvM,MAAM,CAACoG,KAAK,EAAEC,KAAK,EAAEV,MAAM,EAAE,IAAI,CAAC2B,YAAY,EAAE,CAAC;IAC/D,IAAI,CAACkF,WAAW,CAACD,CAAC,EAAE1I,OAAO,CAAC;IAE5B;IACA,IAAIwC,KAAK,KAAK,QAAQ,EAAE;MACtB,MAAMT,IAAI,GAAG,IAAI,CAAC7C,KAAK,CAACvB,UAAU,CAACqE,GAAG,CAACF,MAAM,CAAC;MAC9C,MAAM/D,QAAQ,GAAGxB,oBAAoB,CAAC8L,WAA2B,CAAC;MAClE,IAAI,CAACnJ,KAAK,CAAChC,QAAQ,IAAIa,QAAQ,GAAGgE,IAAI,CAAC6G,KAAK;MAC5C7G,IAAI,CAAC6G,KAAK,GAAG7K,QAAQ;;IAEvB,OAAO2K,CAAC;EACV;EAEA;;;;;;EAMAG,oBAAoBA,CAClB/G,MAAc,EAAES,KAAe,EAAEC,KAAe,EAChDxC,OAAuB;IACvBwC,KAAK,GAAGA,KAAK,IAAI,SAAS;IAC1B,MAAM7E,UAAU,GAAe;MAACmE,MAAM;MAAES,KAAK;MAAEC;IAAK,CAAC;IACrD,OAAO,IAAI,CAACyD,wBAAwB,CAACtI,UAAU,EAAEqC,OAAO,CAAC;EAC3D;EAEA;;;;;EAKAiG,wBAAwBA,CAACtI,UAAsB,EAAEqC,OAAuB;IAEtE,MAAM;MAAC8B,MAAM;MAAES,KAAK;MAAEC;IAAK,CAAC,GAAG7E,UAAU;IACzC,MAAM+K,CAAC,GAAG,IAAIvM,MAAM,CAACoG,KAAK,EAAEC,KAAK,EAAEV,MAAM,EAAE,IAAI,CAAC2B,YAAY,EAAE,CAAC;IAC/D,IAAI,CAACkF,WAAW,CAACD,CAAC,EAAE1I,OAAO,CAAC;IAC5B,OAAO0I,CAAC;EACV;EAEAI,YAAYA,CACRC,YAAoB,EAAEC,SAAS,GAAG,IAAI,EAAEtK,IAAa,EACrD8D,KAAgB;IAClB9D,IAAI,GAAGA,IAAI,IAAI,IAAI,CAACgF,cAAc,EAAE,CAACuF,QAAQ,EAAE;IAC/C,IAAIzG,KAAK,IAAI,IAAI,IAAIA,KAAK,KAAKuG,YAAY,CAACvG,KAAK,EAAE;MACjDuG,YAAY,GAAGA,YAAY,CAACG,IAAI,CAAC1G,KAAK,CAAC;;IAEzC,MAAM2G,CAAC,GAAG,IAAI/M,QAAQ,CAAC2M,YAAY,EAAEC,SAAS,EAAEtK,IAAI,EAAE,IAAI,CAAC+E,YAAY,EAAE,CAAC;IAC1E,IAAI,IAAI,CAACvE,KAAK,CAAClC,mBAAmB,CAACmM,CAAC,CAACzK,IAAI,CAAC,IAAI,IAAI,EAAE;MAClD,MAAM,IAAIqB,KAAK,CAAC,sBAAsBoJ,CAAC,CAACzK,IAAI,yBAAyB,CAAC;;IAExE,IAAI,CAACQ,KAAK,CAAClC,mBAAmB,CAACmM,CAAC,CAACzK,IAAI,CAAC,GAAGyK,CAAC;IAC1C,IAAI,CAACC,MAAM,CAACD,CAAC,EAAE,IAAI,CAACnJ,OAAO,CAAC;IAC5B,OAAOmJ,CAAC;EACV;EAEAR,WAAWA,CAAChH,CAAS,EAAE3B,OAAsB;IAC3C,IAAI,CAACd,KAAK,CAAC/B,UAAU,EAAE;IACvB,IAAIwE,CAAC,CAACa,KAAK,KAAK,QAAQ,EAAE;MACxB,IAAI,CAACtD,KAAK,CAAC9B,gBAAgB,EAAE;;IAE/B;IACA;IACA,IAAIwL,KAAK,GAAG,CAAC;IACb,IAAIjH,CAAC,CAACa,KAAK,KAAK,WAAW,IAAIb,CAAC,CAACa,KAAK,KAAK,QAAQ,EAAE;MACnDoG,KAAK,GAAGjH,CAAC,CAAC0H,IAAI,GAAG/M,IAAI,CAACgN,eAAe,CAAC3H,CAAC,CAACa,KAAK,CAAC;;IAEhD,IAAI,CAACtD,KAAK,CAAChC,QAAQ,IAAI0L,KAAK;IAE5B,IAAI,CAAC,IAAI,CAAC1J,KAAK,CAACvB,UAAU,CAAC4L,GAAG,CAAC5H,CAAC,CAACG,MAAM,CAAC,EAAE;MACxC,IAAI,CAAC5C,KAAK,CAAC7B,cAAc,EAAE;MAC3B,IAAI,CAAC6B,KAAK,CAACvB,UAAU,CAAC6L,GAAG,CAAC7H,CAAC,CAACG,MAAM,EAAE;QAClC9B,OAAO,EAAEA,OAAO,IAAI,IAAI,CAACA,OAAO;QAChCwC,KAAK,EAAEb,CAAC,CAACa,KAAK;QACdD,KAAK,EAAEZ,CAAC,CAACY,KAAK;QACdqG;OACD,CAAC;;IAGJ,IAAI,EAAEjH,CAAC,YAAYvF,QAAQ,CAAC,EAAE;MAC5B,IAAI,CAACqN,KAAK,CAAC9H,CAAC,CAAC;;EAEjB;EAEA;EACA;EACA;EACA;EACA;EACAyH,MAAMA,CAACzH,CAAS,EAAE3B,OAAsB;IACtC,IAAI,CAAC2I,WAAW,CAAChH,CAAC,EAAE3B,OAAO,CAAC;IAC5B,IAAI,CAACA,OAAO,CAACoJ,MAAM,CAACzH,CAAC,CAACG,MAAM,CAAC;EAC/B;EAEA4H,YAAYA,CAAC5H,MAAc,EAAE9B,OAAsB;IACjD,IAAI,IAAI,CAACd,KAAK,CAACvB,UAAU,CAAC4L,GAAG,CAACzH,MAAM,CAAC,IACjC,IAAI,CAAC5C,KAAK,CAACvB,UAAU,CAACqE,GAAG,CAACF,MAAM,CAAC,CAAC9B,OAAO,KAAKA,OAAO,EAAE;MACzD,IAAI,CAACd,KAAK,CAACvB,UAAU,CAACgM,MAAM,CAAC7H,MAAM,CAAC;MACpC,IAAI,CAAC5C,KAAK,CAAC7B,cAAc,EAAE;;EAE/B;EACAuM,aAAaA,CAACjI,CAAS;IACrB,IAAI,CAAC,IAAI,CAACzC,KAAK,CAACvB,UAAU,CAAC4L,GAAG,CAAC5H,CAAC,CAACG,MAAM,CAAC,EAAE;MACxC;;IAEF,MAAMC,IAAI,GAAG,IAAI,CAAC7C,KAAK,CAACvB,UAAU,CAACqE,GAAG,CAACL,CAAC,CAACG,MAAM,CAAC;IAEhD,IAAI,CAAC5C,KAAK,CAAC/B,UAAU,EAAE;IACvB,IAAIwE,CAAC,CAACa,KAAK,KAAK,QAAQ,EAAE;MACxB,IAAI,CAACtD,KAAK,CAAC9B,gBAAgB,EAAE;MAC7B,IAAI,CAAC8B,KAAK,CAAChC,QAAQ,IAAI6E,IAAI,CAAC6G,KAAK;;IAEnC;IACA;IACA,IAAIjH,CAAC,CAACa,KAAK,KAAK,WAAW,IAAIb,CAAC,CAACa,KAAK,KAAK,QAAQ,EAAE;MACnD,MAAMoG,KAAK,GAAGjH,CAAC,CAAC0H,IAAI,GAAG/M,IAAI,CAACgN,eAAe,CAAC3H,CAAC,CAACa,KAAK,CAAC;MACpD,IAAI,CAACtD,KAAK,CAAChC,QAAQ,IAAI0L,KAAK;;IAG9B;IACA,IAAI7G,IAAI,CAAC/B,OAAO,CAACqC,WAAW,CAACV,CAAC,CAACG,MAAM,CAAC,EAAE;MACtC,IAAI,CAAC4H,YAAY,CAAC/H,CAAC,CAACG,MAAM,EAAEC,IAAI,CAAC/B,OAAO,CAAC;;IAG3C;IACA;IACA;EACF;EAEA6J,gBAAgBA,CAAA;IACd,KAAK,MAAMC,OAAO,IAAI,IAAI,CAAC5K,KAAK,CAAClC,mBAAmB,EAAE;MACpD,MAAMmM,CAAC,GAAG,IAAI,CAACjK,KAAK,CAAClC,mBAAmB,CAAC8M,OAAO,CAAC;MACjD,IAAI,CAACC,eAAe,CAACZ,CAAC,CAAC;;EAE3B;EAEAY,eAAeA,CAACZ,CAAW;IACzB,IAAI,CAACS,aAAa,CAACT,CAAC,CAAC;IACrB,IAAI,IAAI,CAACjK,KAAK,CAAClC,mBAAmB,CAACmM,CAAC,CAACzK,IAAI,CAAC,IAAI,IAAI,EAAE;MAClD,OAAO,IAAI,CAACQ,KAAK,CAAClC,mBAAmB,CAACmM,CAAC,CAACzK,IAAI,CAAC;;EAEjD;EAEAsL,MAAMA,CAAA;IACJ,MAAMjI,IAAI,GAAG,IAAI,CAAC/B,OAAO,CAACgK,MAAM,EAAgB;IAChDjI,IAAI,CAAC5E,UAAU,GAAG,IAAI,CAAC+B,KAAK,CAAC/B,UAAU;IACvC4E,IAAI,CAAC1E,cAAc,GAAG,IAAI,CAAC6B,KAAK,CAAC7B,cAAc;IAC/C0E,IAAI,CAAC7E,QAAQ,GAAG,IAAI,CAACgC,KAAK,CAAChC,QAAQ;IACnC,IAAI,IAAI,CAACgC,KAAK,CAAC9B,gBAAgB,GAAG,CAAC,EAAE;MACnC2E,IAAI,CAACkI,UAAU,GAAG,IAAI;MACtB,IAAIlI,IAAI,CAACmI,OAAO,IAAI,IAAI,EAAE;QACxBnI,IAAI,CAACmI,OAAO,GAAG,EAAE;;MAEnBnI,IAAI,CAACmI,OAAO,CAAC1E,IAAI,CACb,gDAAgD,GAChD,yBAAyB,CAAC;;IAEhC,OAAOzD,IAAI;EACb;EAEA,MAAMoI,OAAOA,CAACC,KAAyD;IAErE,IAAI,CAAClL,KAAK,CAACrB,SAAS,GAAG,IAAI;IAE3B,MAAMwM,UAAU,GAAG,IAAI,CAACnL,KAAK,CAAChC,QAAQ;IACtC,MAAMoN,eAAe,GAAG,IAAI,CAACpL,KAAK,CAAC/B,UAAU;IAE7C,IAAI,CAAC+B,KAAK,CAACpB,aAAa,CAACI,OAAO,GAAG,EAAE;IACrC,IAAI,CAACgB,KAAK,CAACpB,aAAa,CAACK,MAAM,GAAG,MAAMiM,KAAK,EAAE;IAE/C,IAAI,CAAClL,KAAK,CAACrB,SAAS,GAAG,KAAK;IAE5B,IAAI,CAACqB,KAAK,CAACpB,aAAa,CAACG,SAAS,GAAGsM,IAAI,CAACC,GAAG,CACzC,GAAG,IAAI,CAACtL,KAAK,CAACpB,aAAa,CAACI,OAAO,CAACM,GAAG,CAAC+J,CAAC,IAAIA,CAAC,CAACvB,kBAAkB,CAAC,CAAC;IACvE,IAAI,CAAC9H,KAAK,CAACpB,aAAa,CAACC,QAAQ,GAAG,IAAI,CAACmB,KAAK,CAAChC,QAAQ,GAAGmN,UAAU;IACpE,IAAI,CAACnL,KAAK,CAACpB,aAAa,CAACE,UAAU,GAC/B,IAAI,CAACkB,KAAK,CAAC/B,UAAU,GAAGmN,eAAe;IAC3C,KAAK,MAAMvJ,MAAM,IAAI,IAAI,CAAC7B,KAAK,CAACpB,aAAa,CAACI,OAAO,EAAE;MACrD6C,MAAM,CAACwG,YAAY,GAAG,MAAMxG,MAAM,CAACwG,YAAY;MAC/CxG,MAAM,CAAC0G,SAAS,GAAG,MAAM1G,MAAM,CAAC0G,SAAS;;IAE3C,OAAO,IAAI,CAACvI,KAAK,CAACpB,aAAa;EACjC;EAEAuH,QAAQA,CAAA;IACN,OAAO,IAAI,CAACnG,KAAK,CAAC5B,aAAa,GAAG,CAAC,IAAI,IAAI,CAAC4B,KAAK,CAAC3B,WAAW,KAAK,CAAC;EACrE;EAEQ+G,WAAWA,CACfzH,UAAkB,EAAEmH,MAAsB,EAAEoB,OAAiB,EAC7DqF,aAAuB,EAAEpG,KAAe,EAAED,KAAmB;IAC/D,MAAMsG,QAAQ,GACV;MAACC,EAAE,EAAE,IAAI,CAACzL,KAAK,CAACjC,cAAc,EAAE;MAAEJ,UAAU;MAAEmH,MAAM;MAAEoB,OAAO;MAAEf;IAAK,CAAC;IAEzE,MAAMqD,UAAU,GAAG/L,WAAW,CAACkB,UAAU,CAAC;IAC1C,IAAI6K,UAAU,IAAI,IAAI,EAAE;MACtB+C,aAAa,GAAG/C,UAAU,CAACkD,QAAQ;;IAErC,IAAIH,aAAa,IAAI,IAAI,EAAE;MACzBC,QAAQ,CAACG,QAAQ,GAAIC,GAAa,IAAI;QACpC;QACA;QACAA,GAAG,GAAGA,GAAG,CAACtM,GAAG,CAAC,CAAC0F,EAAE,EAAEzE,CAAC,KAAI;UACtB,IAAIyE,EAAE,IAAI,IAAI,EAAE;YACd,MAAM6G,MAAM,GAAG3F,OAAO,CAAC3F,CAAC,CAAC;YACzB,MAAMuL,IAAI,GAAG1O,IAAI,CAAC2O,mBAAmB,CAACF,MAAM,CAAC1B,IAAI,EAAE0B,MAAM,CAACvI,KAAK,CAAC;YAChE,OAAO,IAAI,CAAC4F,UAAU,CAAC4C,IAAI,EAAED,MAAM,CAACxI,KAAK,EAAEwI,MAAM,CAACvI,KAAK,CAAC;;UAE1D,OAAO0B,EAAE;QACX,CAAC,CAAC;QACF;QACA;QACA,OAAOuG,aAAa,CAACK,GAAG,CAACpL,MAAM,GAAG,CAAC,GAAGoL,GAAG,GAAGA,GAAG,CAAC,CAAC,CAAC,EAAEzG,KAAK,EAAED,KAAK,CAAC;MACnE,CAAC;;IAEH,IAAI,CAAClF,KAAK,CAACgM,UAAU,CAAC1F,IAAI,CAACkF,QAAQ,CAAC;EACtC;EAEAjE,IAAIA,CAAmBtI,MAAS;IAC9BA,MAAM,CAACgN,IAAI,GAAG,IAAI;IAClB,OAAOhN,MAAM;EACf;EAEQiN,SAASA,CAAA;IACf,IAAI,IAAI,CAAClM,KAAK,CAAC5B,aAAa,KAAK,CAAC,EAAE;MAClC,IAAI,CAAC4B,KAAK,CAACgM,UAAU,GAAG,EAAE;;IAE5B,IAAI,CAAChM,KAAK,CAAC5B,aAAa,EAAE;EAC5B;EAEQ+N,OAAOA,CAAA;IACb,IAAI,CAACnM,KAAK,CAAC5B,aAAa,EAAE;EAC5B;EAEA;;;;EAIAyF,UAAUA,CAACrE,IAAa;IACtB,MAAM4M,SAAS,GAAe;MAC5B7B,KAAK,EAAE,EAAE;MACT/K,IAAI,EAAE,eAAe;MACrBiM,EAAE,EAAE,IAAI,CAACzL,KAAK,CAACxB,WAAW;KAC3B;IACD,IAAIgB,IAAI,EAAE;MACR4M,SAAS,CAAC5M,IAAI,GAAGA,IAAI;;IAEvB,IAAI,CAACQ,KAAK,CAAC1B,UAAU,CAACgI,IAAI,CAAC8F,SAAS,CAAC;IACrC,IAAI,CAACpM,KAAK,CAACqF,WAAW,GAAG+G,SAAS;EACpC;EAEA;;;;EAIAtI,QAAQA,CAAC7E,MAAwB;IAC/B,MAAMoN,sBAAsB,GAAGlP,qBAAqB,CAAC8B,MAAM,CAAC;IAC5D,MAAMqN,yBAAyB,GAC3B,IAAIjN,GAAG,CAACgN,sBAAsB,CAAC/M,GAAG,CAACkK,CAAC,IAAIA,CAAC,CAACiC,EAAE,CAAC,CAAC;IAElD;IACA,KAAK,IAAIlL,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACP,KAAK,CAACqF,WAAW,CAACkF,KAAK,CAAC/J,MAAM,EAAED,CAAC,EAAE,EAAE;MAC5D,MAAM+G,MAAM,GAAG,IAAI,CAACtH,KAAK,CAACqF,WAAW,CAACkF,KAAK,CAAChK,CAAC,CAAC;MAC9C,IAAI,CAAC+G,MAAM,CAAC2E,IAAI,IAAI,CAACK,yBAAyB,CAACjC,GAAG,CAAC/C,MAAM,CAACmE,EAAE,CAAC,EAAE;QAC7DnE,MAAM,CAAC7H,OAAO,EAAE;;;IAIpB,MAAM8M,QAAQ,GAAG,IAAI,CAACvM,KAAK,CAAC1B,UAAU,CAACkO,GAAG,EAAE;IAC5C,IAAI,CAACxM,KAAK,CAACqF,WAAW,GAAG,IAAI,CAACrF,KAAK,CAAC1B,UAAU,CAACkC,MAAM,KAAK,CAAC,GACvD,IAAI,GACJ,IAAI,CAACR,KAAK,CAAC1B,UAAU,CAAC,IAAI,CAAC0B,KAAK,CAAC1B,UAAU,CAACkC,MAAM,GAAG,CAAC,CAAC;IAE3D;IACA6L,sBAAsB,CAACzK,OAAO,CAAC0F,MAAM,IAAG;MACtC;MACA;MACA,IAAI,CAACA,MAAM,CAAC2E,IAAI,IAAI3E,MAAM,CAACmF,OAAO,KAAKF,QAAQ,CAACd,EAAE,EAAE;QAClD,IAAI,CAAClB,KAAK,CAACjD,MAAM,CAAC;;IAEtB,CAAC,CAAC;EACJ;EAEA;;;;;;EAMAoF,SAASA,CACLtI,CAAU,EAAEuI,EAAY,EAAE3H,EAAM,EAChC4H,gBAAgB,GAAG,KAAK;IAC1BxP,IAAI,CAACsJ,MAAM,CACPiG,EAAE,CAACnM,MAAM,GAAG,CAAC,EAAE,MAAM,2CAA2C,CAAC;IACrE,IAAIwE,EAAE,IAAI,IAAI,IAAIA,EAAE,CAAC1B,KAAK,KAAK,SAAS,EAAE;MACxC,MAAM,IAAIzC,KAAK,CAAC,0CAA0CmE,EAAE,CAAC1B,KAAK,GAAG,CAAC;;IAGxE,MAAMqB,CAAC,GAAG,IAAI,CAACf,SAAS,CACpB,MAAM,IAAI,CAACsI,SAAS,EAAE,EAAE,MAAM,IAAI,CAACC,OAAO,EAAE,EAC5C,MAAM,IAAI,CAAC3I,IAAI,CAAC,SAAS,EAAEY,CAAC,CAAC,CAAC;IAElChH,IAAI,CAACsJ,MAAM,CACP/B,CAAC,YAAY1H,MAAM,EACnB,MAAM,gDAAgD,CAAC;IAC3D;IACA,MAAM4P,YAAY,GAAG9P,oBAAoB,CAAC,IAAI,CAACiD,KAAK,CAACgM,UAAU,EAAEW,EAAE,EAAEhI,CAAC,CAAC;IACvE,IAAI,CAACiI,gBAAgB,IAAIC,YAAY,CAACrM,MAAM,KAAK,CAAC,IAAImM,EAAE,CAACnM,MAAM,GAAG,CAAC,EAAE;MACnE,MAAM,IAAIK,KAAK,CACX,iEAAiE,GACjE,iEAAiE,GACjE,OAAO,CAAC;;IAGd,OAAO,IAAI,CAAC2C,IAAI,CAAC,UAAU,EAAE,MAAK;MAChC,MAAMsJ,sBAAsB,GAAiC,EAAE;MAC/DA,sBAAsB,CAACnI,CAAC,CAAC8G,EAAE,CAAC,GAAIzG,EAAE,IAAI,IAAI,GAAI+H,IAAI,CAACpI,CAAC,CAACtB,KAAK,CAAC,GAAG2B,EAAE;MAEhE;MACAlI,sBAAsB,CAClBgQ,sBAAsB,EAAED,YAAY;MACpC;MACAzI,CAAC,IAAI,IAAI,CAACZ,IAAI,CAACY,CAAoB,CAAC;MACpC;MACA4I,GAAG,CAAC;MACR,MAAMC,KAAK,GAAGN,EAAE,CAACrN,GAAG,CAACoF,CAAC,IAAIoI,sBAAsB,CAACpI,CAAC,CAAC+G,EAAE,CAAC,CAAC;MAEvD,IAAI,IAAI,CAACzL,KAAK,CAAC5B,aAAa,KAAK,CAAC,EAAE;QAClC;QACA;QACA,IAAI,CAAC4B,KAAK,CAACgM,UAAU,CAACpK,OAAO,CAACsL,IAAI,IAAG;UACnC,KAAK,MAAM5F,MAAM,IAAI4F,IAAI,CAAC/H,KAAK,EAAE;YAC/BmC,MAAM,CAAC7H,OAAO,EAAE;;QAEpB,CAAC,CAAC;QACF,IAAI,CAACO,KAAK,CAACgM,UAAU,GAAG,IAAI;;MAE9B,OAAO;QAACmB,KAAK,EAAExI,CAAC;QAAEsI;MAAK,CAAC;IAC1B,CAAC,CAAC;EACJ;EAEAG,UAAUA,CAAmBhJ,CAAwB;IAEnDhH,IAAI,CAACsJ,MAAM,CACPtJ,IAAI,CAACiQ,UAAU,CAACjJ,CAAC,CAAC,EAClB,MAAM,mDAAmD,CAAC;IAC9D,OAAO,CAAC,GAAGU,MAAgB,KAAO;MAChC1H,IAAI,CAACsJ,MAAM,CACP5B,MAAM,CAACwI,KAAK,CAAC9D,CAAC,IAAIA,CAAC,YAAYvM,MAAM,CAAC,EACtC,MAAM,2DAA2D,GAC7D,SAAS,CAAC;MAElB,IAAIoH,GAGH;MACD,MAAMkJ,QAAQ,GAAmB,EAAE;MACnCzI,MAAM,CAAClD,OAAO,CAAC,CAAC4L,KAAK,EAAEjN,CAAC,KAAI;QAC1BgN,QAAQ,CAAChN,CAAC,CAAC,GAAGiN,KAAK;MACrB,CAAC,CAAC;MAEF,MAAMrG,WAAW,GAAmBA,CAAC6B,CAAC,EAAEyE,IAAI,KAAI;QAC9CpJ,GAAG,GAAGD,CAAC,CAAC,GAAG,CAAC,GAAGU,MAAM,EAAE2I,IAAI,CAAC,CAAC;QAC7BrQ,IAAI,CAACsJ,MAAM,CACPrC,GAAG,CAAC8I,KAAK,YAAYlQ,MAAM,EAC3B,MAAM,wDAAwD,GAC1D,sCAAsC,CAAC;QAC/CG,IAAI,CAACsJ,MAAM,CACPtJ,IAAI,CAACiQ,UAAU,CAAChJ,GAAG,CAACqH,QAAQ,CAAC,EAC7B,MAAM,wDAAwD,GAC1D,4CAA4C,CAAC;QACrD,OAAOrH,GAAG,CAAC8I,KAAK;MAClB,CAAC;MAED,MAAM1F,aAAa,GAAGA,CAACzC,EAAK,EAAEG,KAAe,KAAI;QAC/C,MAAMuI,OAAO,GAAGrJ,GAAG,CAACqH,QAAQ,CAAC1G,EAAE,EAAEG,KAAK,CAAC;QACvC,MAAM8H,KAAK,GAAa9N,KAAK,CAACwH,OAAO,CAAC+G,OAAO,CAAC,GAAGA,OAAO,GAAG,CAACA,OAAO,CAAC;QACpEtQ,IAAI,CAACsJ,MAAM,CACPuG,KAAK,CAACzM,MAAM,KAAKsE,MAAM,CAACtE,MAAM,EAC9B,MAAM,wDAAwD,GAC1D,yDAAyD,GACzD,wDAAwD,CAAC;QACjEpD,IAAI,CAACsJ,MAAM,CACPuG,KAAK,CAACK,KAAK,CAAC9D,CAAC,IAAIA,CAAC,YAAYvM,MAAM,CAAC,EACrC,MAAM,wDAAwD,GAC1D,yDAAyD,GACzD,yBAAyB,CAAC;QAClC,MAAM0Q,OAAO,GAAkC,EAAE;QACjDV,KAAK,CAACrL,OAAO,CAAC,CAACmD,IAAI,EAAExE,CAAC,KAAI;UACxBoN,OAAO,CAACpN,CAAC,CAAC,GAAG,MAAMwE,IAAI;QACzB,CAAC,CAAC;QACF,OAAO4I,OAAO;MAChB,CAAC;MAED,OAAO,IAAI,CAACpI,aAAa,CAAC;QACxB4B,WAAW;QACXM,aAAa;QACb3C,MAAM,EAAEyI;OACT,CAAC;IACJ,CAAC;EACH;EAEAtK,QAAQA,CAACL,MAAc;IACrB;IACA,MAAMC,IAAI,GAAG,IAAI,CAAC7C,KAAK,CAACvB,UAAU,CAACqE,GAAG,CAACF,MAAM,CAAC;IAC9C,OAAOC,IAAI,CAAC/B,OAAO,CAACmC,QAAQ,CAACL,MAAM,CAAC;EACtC;EACAgL,IAAIA,CAAChL,MAAc;IACjB;IACA,MAAMC,IAAI,GAAG,IAAI,CAAC7C,KAAK,CAACvB,UAAU,CAACqE,GAAG,CAACF,MAAM,CAAC;IAC9C,OAAOC,IAAI,CAAC/B,OAAO,CAAC8M,IAAI,CAAChL,MAAM,CAAC;EAClC;EAEAiL,SAASA,CAACjL,MAAc,EAAEkL,OAA0B;IAClD;IACA,MAAMjL,IAAI,GAAG,IAAI,CAAC7C,KAAK,CAACvB,UAAU,CAACqE,GAAG,CAACF,MAAM,CAAC;IAC9C,OAAOC,IAAI,CAAC/B,OAAO,CAAC+M,SAAS,CAACjL,MAAM,EAAEkL,OAAO,CAAC;EAChD;EAEA,MAAMC,IAAIA,CAAC7C,KAAiB;IAC1B,MAAMhH,KAAK,GAAG3G,GAAG,EAAE;IACnB,MAAMyQ,UAAU,GAAG,MAAM,IAAI,CAAClN,OAAO,CAACiN,IAAI,CAAC7C,KAAK,CAAe;IAC/D8C,UAAU,CAACC,MAAM,GAAG1Q,GAAG,EAAE,GAAG2G,KAAK;IACjC,OAAO8J,UAAU;EACnB;EAEA;;;;;;EAMQzD,KAAKA,CAAmBtL,MAAS;IACvC,IAAI,IAAI,CAACe,KAAK,CAACqF,WAAW,IAAI,IAAI,EAAE;MAClCpG,MAAM,CAACwN,OAAO,GAAG,IAAI,CAACzM,KAAK,CAACqF,WAAW,CAACoG,EAAE;MAC1C,IAAI,CAACzL,KAAK,CAACqF,WAAW,CAACkF,KAAK,CAACjE,IAAI,CAACrH,MAAM,CAAC;;IAG3C,OAAOA,MAAM;EACf;EAEA,IAAInB,mBAAmBA,CAAA;IACrB,OAAO,IAAI,CAACkC,KAAK,CAAClC,mBAAmB;EACvC;EAEA;;;;EAIAoQ,KAAKA,CAAA;IACH;IACA,IAAI,CAACnO,oBAAoB,EAAE;IAE3B,IAAI,CAACC,KAAK,CAACP,OAAO,EAAE;IACpB,IAAI,CAACG,GAAG,CAACsO,KAAK,EAAE;IAChB,IAAI,CAAClO,KAAK,GAAG,IAAIpC,WAAW,EAAE;IAE9B,KAAK,MAAM6C,WAAW,IAAI,IAAI,CAACZ,QAAQ,EAAE;MACvC,IAAI,CAACkC,wBAAwB,CAACtB,WAAW,CAAC;MAC1C,IAAI,CAACZ,QAAQ,CAACY,WAAW,CAAC,CAAChB,OAAO,EAAE;MACpC,OAAO,IAAI,CAACI,QAAQ,CAACY,WAAW,CAAC;;IAEnC,IAAI,CAACA,WAAW,GAAG,IAAI;IACvB,IAAI,CAACL,eAAe,GAAG,IAAI;IAC3B,IAAI,CAACF,kBAAkB,GAAG,IAAI;EAChC;;AAxxBeP,MAAA,CAAA4E,YAAY,GAAG,CAAC;AAKhB5E,MAAA,CAAA6E,cAAc,GAAG,CAAC;SAtUtB7E,MAAM;AA4lCnB,SAASoN,IAAIA,CAAC1J,KAAe;EAC3B,MAAML,MAAM,GAAG1F,kBAAkB,CAACE,aAAa,CAAC6F,KAAK,CAAC,EAAE,SAAS,CAAC;EAClE,OAAOuB,MAAM,CAACsE,UAAU,CAAClG,MAAM,EAAEK,KAAK,EAAE,SAAS,CAAC;AACpD;AAEA,OAAM,SAAU8K,eAAeA,CAAA;EAC7B,MAAMC,EAAE,GAAG/R,kBAAkB,EAAoC;EACjE,IAAI+R,EAAE,CAACC,SAAS,IAAI,IAAI,EAAE;IACxB,MAAMC,WAAW,GAAG,IAAInS,WAAW,CAACiS,EAAE,CAAC;IACvCA,EAAE,CAACC,SAAS,GAAG,IAAI1O,MAAM,CAAC2O,WAAW,CAAC;;EAExClS,oBAAoB,CAACgS,EAAE,CAACC,SAAS,CAACzO,GAAG,CAAC;EAEtC;EACA;EACA5C,gBAAgB,CAAC,MAAMoR,EAAE,CAACC,SAAS,CAAC;EACpC,OAAOD,EAAE,CAACC,SAAS;AACrB;AAEA,OAAO,MAAMzJ,MAAM,GAAGuJ,eAAe,EAAE;AAEvC;;;;;;AAMA,OAAM,SAAUnB,GAAGA,CAACvK,CAAS,EAAEC,CAAS;EACtC;EACA,MAAMoC,MAAM,GAAG;IAACrC,CAAC;IAAEC;EAAC,CAAC;EACrB,OAAOkC,MAAM,CAACC,SAAS,CAACvI,GAAG,EAAEwI,MAAmC,CAAC;AACnE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}