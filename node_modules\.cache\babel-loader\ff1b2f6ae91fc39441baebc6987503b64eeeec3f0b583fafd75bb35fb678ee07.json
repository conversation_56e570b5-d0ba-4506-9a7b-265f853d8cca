{"ast": null, "code": "/**\n * @license\n * Copyright 2021 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { matMul } from './mat_mul';\nimport { ones } from './ones';\nimport { reshape } from './reshape';\nimport { Tensor } from '../tensor';\nimport { convertToTensor } from '../tensor_util_env';\nimport { sizeFromShape } from '../util_base';\n/**\n * Broadcasts parameters for evaluation on an N-D grid.\n *\n * Given N one-dimensional coordinate arrays `*args`, returns a list `outputs`\n * of N-D coordinate arrays for evaluating expressions on an N-D grid.\n *\n * Notes:\n * `meshgrid` supports cartesian ('xy') and matrix ('ij') indexing conventions.\n * When the `indexing` argument is set to 'xy' (the default), the broadcasting\n * instructions for the first two dimensions are swapped.\n * Examples:\n * Calling `const [X, Y] = meshgrid(x, y)` with the tensors\n *\n * ```javascript\n * const x = [1, 2, 3];\n * const y = [4, 5, 6];\n * const [X, Y] = tf.meshgrid(x, y);\n * // X = [[1, 2, 3],\n * //      [1, 2, 3],\n * //      [1, 2, 3]]\n * // Y = [[4, 4, 4],\n * //      [5, 5, 5],\n * //      [6, 6, 6]]\n * ```\n *\n * @param x Tensor with rank geq 1.\n * @param y Tensor with rank geq 1.\n * @param indexing\n *\n * @doc {heading: 'Operations', subheading: 'Slicing and Joining'}\n */\nexport function meshgrid(x, y, {\n  indexing = 'xy'\n} = {}) {\n  if (indexing !== 'xy' && indexing !== 'ij') {\n    throw new TypeError(`${indexing} is not a valid third argument to meshgrid`);\n  }\n  if (x === undefined) {\n    return [];\n  }\n  let $x = convertToTensor(x, 'x', 'meshgrid', x instanceof Tensor ? x.dtype : 'float32');\n  if (y === undefined) {\n    return [$x];\n  }\n  let $y = convertToTensor(y, 'y', 'meshgrid', y instanceof Tensor ? y.dtype : 'float32');\n  const w = sizeFromShape($x.shape);\n  const h = sizeFromShape($y.shape);\n  if (indexing === 'xy') {\n    $x = reshape($x, [1, -1]);\n    $y = reshape($y, [-1, 1]);\n    return [matMul(ones([h, 1], $x.dtype), $x), matMul($y, ones([1, w], $y.dtype))];\n  }\n  $x = reshape($x, [-1, 1]);\n  $y = reshape($y, [1, -1]);\n  return [matMul($x, ones([1, h], $x.dtype)), matMul(ones([w, 1], $y.dtype), $y)];\n}", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON>", "ones", "reshape", "Tensor", "convertToTensor", "sizeFromShape", "meshgrid", "x", "y", "indexing", "TypeError", "undefined", "$x", "dtype", "$y", "w", "shape", "h"], "sources": ["C:\\tfjs-core\\src\\ops\\meshgrid.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2021 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {matMul} from './mat_mul';\nimport {ones} from './ones';\nimport {reshape} from './reshape';\nimport {Tensor} from '../tensor';\nimport {convertToTensor} from '../tensor_util_env';\nimport {TensorLike} from '../types';\nimport {sizeFromShape} from '../util_base';\n\n/**\n * Broadcasts parameters for evaluation on an N-D grid.\n *\n * Given N one-dimensional coordinate arrays `*args`, returns a list `outputs`\n * of N-D coordinate arrays for evaluating expressions on an N-D grid.\n *\n * Notes:\n * `meshgrid` supports cartesian ('xy') and matrix ('ij') indexing conventions.\n * When the `indexing` argument is set to 'xy' (the default), the broadcasting\n * instructions for the first two dimensions are swapped.\n * Examples:\n * Calling `const [X, Y] = meshgrid(x, y)` with the tensors\n *\n * ```javascript\n * const x = [1, 2, 3];\n * const y = [4, 5, 6];\n * const [X, Y] = tf.meshgrid(x, y);\n * // X = [[1, 2, 3],\n * //      [1, 2, 3],\n * //      [1, 2, 3]]\n * // Y = [[4, 4, 4],\n * //      [5, 5, 5],\n * //      [6, 6, 6]]\n * ```\n *\n * @param x Tensor with rank geq 1.\n * @param y Tensor with rank geq 1.\n * @param indexing\n *\n * @doc {heading: 'Operations', subheading: 'Slicing and Joining'}\n */\nexport function meshgrid<T extends Tensor>(\n    x?: T|TensorLike, y?: T|TensorLike, {indexing = 'xy'} = {}): T[] {\n  if (indexing !== 'xy' && indexing !== 'ij') {\n    throw new TypeError(\n        `${indexing} is not a valid third argument to meshgrid`);\n  }\n  if (x === undefined) {\n    return [];\n  }\n  let $x = convertToTensor(\n      x, 'x', 'meshgrid', x instanceof Tensor ? x.dtype : 'float32');\n\n  if (y === undefined) {\n    return [$x];\n  }\n  let $y = convertToTensor(\n      y, 'y', 'meshgrid', y instanceof Tensor ? y.dtype : 'float32');\n\n  const w = sizeFromShape($x.shape);\n  const h = sizeFromShape($y.shape);\n\n  if (indexing === 'xy') {\n    $x = reshape($x, [1, -1]) as T;\n    $y = reshape($y, [-1, 1]) as T;\n    return [\n      matMul(ones([h, 1], $x.dtype), $x),\n      matMul($y, ones([1, w], $y.dtype)),\n    ];\n  }\n\n  $x = reshape($x, [-1, 1]) as T;\n  $y = reshape($y, [1, -1]) as T;\n  return [\n    matMul($x, ones([1, h], $x.dtype)),\n    matMul(ones([w, 1], $y.dtype), $y),\n  ];\n}\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAiBA,SAAQA,MAAM,QAAO,WAAW;AAChC,SAAQC,IAAI,QAAO,QAAQ;AAC3B,SAAQC,OAAO,QAAO,WAAW;AACjC,SAAQC,MAAM,QAAO,WAAW;AAChC,SAAQC,eAAe,QAAO,oBAAoB;AAElD,SAAQC,aAAa,QAAO,cAAc;AAE1C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA+BA,OAAM,SAAUC,QAAQA,CACpBC,CAAgB,EAAEC,CAAgB,EAAE;EAACC,QAAQ,GAAG;AAAI,CAAC,GAAG,EAAE;EAC5D,IAAIA,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,IAAI,EAAE;IAC1C,MAAM,IAAIC,SAAS,CACf,GAAGD,QAAQ,4CAA4C,CAAC;;EAE9D,IAAIF,CAAC,KAAKI,SAAS,EAAE;IACnB,OAAO,EAAE;;EAEX,IAAIC,EAAE,GAAGR,eAAe,CACpBG,CAAC,EAAE,GAAG,EAAE,UAAU,EAAEA,CAAC,YAAYJ,MAAM,GAAGI,CAAC,CAACM,KAAK,GAAG,SAAS,CAAC;EAElE,IAAIL,CAAC,KAAKG,SAAS,EAAE;IACnB,OAAO,CAACC,EAAE,CAAC;;EAEb,IAAIE,EAAE,GAAGV,eAAe,CACpBI,CAAC,EAAE,GAAG,EAAE,UAAU,EAAEA,CAAC,YAAYL,MAAM,GAAGK,CAAC,CAACK,KAAK,GAAG,SAAS,CAAC;EAElE,MAAME,CAAC,GAAGV,aAAa,CAACO,EAAE,CAACI,KAAK,CAAC;EACjC,MAAMC,CAAC,GAAGZ,aAAa,CAACS,EAAE,CAACE,KAAK,CAAC;EAEjC,IAAIP,QAAQ,KAAK,IAAI,EAAE;IACrBG,EAAE,GAAGV,OAAO,CAACU,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAM;IAC9BE,EAAE,GAAGZ,OAAO,CAACY,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAM;IAC9B,OAAO,CACLd,MAAM,CAACC,IAAI,CAAC,CAACgB,CAAC,EAAE,CAAC,CAAC,EAAEL,EAAE,CAACC,KAAK,CAAC,EAAED,EAAE,CAAC,EAClCZ,MAAM,CAACc,EAAE,EAAEb,IAAI,CAAC,CAAC,CAAC,EAAEc,CAAC,CAAC,EAAED,EAAE,CAACD,KAAK,CAAC,CAAC,CACnC;;EAGHD,EAAE,GAAGV,OAAO,CAACU,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAM;EAC9BE,EAAE,GAAGZ,OAAO,CAACY,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAM;EAC9B,OAAO,CACLd,MAAM,CAACY,EAAE,EAAEX,IAAI,CAAC,CAAC,CAAC,EAAEgB,CAAC,CAAC,EAAEL,EAAE,CAACC,KAAK,CAAC,CAAC,EAClCb,MAAM,CAACC,IAAI,CAAC,CAACc,CAAC,EAAE,CAAC,CAAC,EAAED,EAAE,CAACD,KAAK,CAAC,EAAEC,EAAE,CAAC,CACnC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}