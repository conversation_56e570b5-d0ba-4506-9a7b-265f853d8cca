{"ast": null, "code": "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { ENGINE } from '../engine';\nimport { Tile } from '../kernel_names';\nimport { convertToTensor } from '../tensor_util_env';\nimport { assertNonNegativeIntegerDimensions } from '../util_base';\nimport { clone } from './clone';\nimport { op } from './operation';\nimport { reshape } from './reshape';\n/**\n * Broadcast an array to a compatible shape NumPy-style.\n *\n * The tensor's shape is compared to the broadcast shape from end to beginning.\n * Ones are prepended to the tensor's shape until it has the same length as\n * the broadcast shape. If input.shape[i]==shape[i], the (i+1)-th axis is\n * already broadcast-compatible. If input.shape[i]==1 and shape[i]==N, then\n * the input tensor is tiled N times along that axis (using tf.tile).\n *\n * @param input The tensor that is to be broadcasted.\n * @param shape The input is to be broadcast to this shape.\n *\n * @doc {heading: 'Tensors', subheading: 'Transformations'}\n */\nfunction broadcastTo_(x, shape) {\n  let input = convertToTensor(x, 'broadcastTo', 'x');\n  const xShape = input.shape;\n  assertNonNegativeIntegerDimensions(shape);\n  if (shape.length < input.rank) {\n    throw new Error(\"broadcastTo(): shape.length=\".concat(shape.length, \" < input.rank=\").concat(input.rank, \".\"));\n  }\n  if (shape.length > input.rank) {\n    const newShape = input.shape.slice();\n    while (newShape.length < shape.length) {\n      newShape.unshift(1);\n    }\n    input = reshape(input, newShape);\n  }\n  const inputShape = input.shape;\n  const reps = Array.from(shape);\n  for (let i = shape.length - 1; i >= 0; i--) {\n    if (inputShape[i] === shape[i]) {\n      reps[i] = 1;\n    } else if (input.shape[i] !== 1) {\n      throw new Error(\"broadcastTo(): [\".concat(xShape, \"] cannot be broadcast to [\").concat(shape, \"].\"));\n    }\n  }\n  const axes = reps.map((n, i) => n > 1 ? i : -1).filter(i => i >= 0);\n  if (axes.length === 0) {\n    return clone(input);\n  }\n  // TODO call broadcastTo kernel directly once backends implement broadcstTo\n  const inputs = {\n    x: input\n  };\n  const attrs = {\n    reps\n  };\n  return ENGINE.runKernel(Tile, inputs, attrs);\n}\nexport const broadcastTo = /* @__PURE__ */op({\n  broadcastTo_\n});", "map": {"version": 3, "names": ["ENGINE", "Tile", "convertToTensor", "assertNonNegativeIntegerDimensions", "clone", "op", "reshape", "broadcastTo_", "x", "shape", "input", "xShape", "length", "rank", "Error", "concat", "newShape", "slice", "unshift", "inputShape", "reps", "Array", "from", "i", "axes", "map", "n", "filter", "inputs", "attrs", "runKernel", "broadcastTo"], "sources": ["C:\\tfjs-core\\src\\ops\\broadcast_to.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {ENGINE} from '../engine';\nimport {Tile, TileAttrs, TileInputs} from '../kernel_names';\nimport {NamedAttrMap} from '../kernel_registry';\nimport {Tensor} from '../tensor';\nimport {NamedTensorMap} from '../tensor_types';\nimport {convertToTensor} from '../tensor_util_env';\nimport {Rank, ShapeMap, TensorLike} from '../types';\nimport {assertNonNegativeIntegerDimensions} from '../util_base';\n\nimport {clone} from './clone';\nimport {op} from './operation';\nimport {reshape} from './reshape';\n\n/**\n * Broadcast an array to a compatible shape NumPy-style.\n *\n * The tensor's shape is compared to the broadcast shape from end to beginning.\n * Ones are prepended to the tensor's shape until it has the same length as\n * the broadcast shape. If input.shape[i]==shape[i], the (i+1)-th axis is\n * already broadcast-compatible. If input.shape[i]==1 and shape[i]==N, then\n * the input tensor is tiled N times along that axis (using tf.tile).\n *\n * @param input The tensor that is to be broadcasted.\n * @param shape The input is to be broadcast to this shape.\n *\n * @doc {heading: 'Tensors', subheading: 'Transformations'}\n */\nfunction broadcastTo_<R extends Rank>(\n    x: Tensor|TensorLike, shape: ShapeMap[R]): Tensor<R> {\n  let input = convertToTensor(x, 'broadcastTo', 'x');\n  const xShape = input.shape;\n\n  assertNonNegativeIntegerDimensions(shape);\n\n  if (shape.length < input.rank) {\n    throw new Error(`broadcastTo(): shape.length=${shape.length} < input.rank=${\n        input.rank}.`);\n  }\n\n  if (shape.length > input.rank) {\n    const newShape = input.shape.slice();\n    while (newShape.length < shape.length) {\n      newShape.unshift(1);\n    }\n    input = reshape(input, newShape);\n  }\n\n  const inputShape = input.shape;\n  const reps: number[] = Array.from(shape);\n  for (let i = shape.length - 1; i >= 0; i--) {\n    if (inputShape[i] === shape[i]) {\n      reps[i] = 1;\n    } else if (input.shape[i] !== 1) {\n      throw new Error(\n          `broadcastTo(): [${xShape}] cannot be broadcast to [${shape}].`);\n    }\n  }\n  const axes = reps.map((n, i) => n > 1 ? i : -1).filter(i => i >= 0);\n\n  if (axes.length === 0) {\n    return clone(input) as Tensor<R>;\n  }\n\n  // TODO call broadcastTo kernel directly once backends implement broadcstTo\n  const inputs: TileInputs = {x: input};\n  const attrs: TileAttrs = {reps};\n  return ENGINE.runKernel(\n      Tile, inputs as unknown as NamedTensorMap,\n      attrs as unknown as NamedAttrMap);\n}\n\nexport const broadcastTo = /* @__PURE__ */ op({broadcastTo_});\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAiBA,SAAQA,MAAM,QAAO,WAAW;AAChC,SAAQC,IAAI,QAA8B,iBAAiB;AAI3D,SAAQC,eAAe,QAAO,oBAAoB;AAElD,SAAQC,kCAAkC,QAAO,cAAc;AAE/D,SAAQC,KAAK,QAAO,SAAS;AAC7B,SAAQC,EAAE,QAAO,aAAa;AAC9B,SAAQC,OAAO,QAAO,WAAW;AAEjC;;;;;;;;;;;;;;AAcA,SAASC,YAAYA,CACjBC,CAAoB,EAAEC,KAAkB;EAC1C,IAAIC,KAAK,GAAGR,eAAe,CAACM,CAAC,EAAE,aAAa,EAAE,GAAG,CAAC;EAClD,MAAMG,MAAM,GAAGD,KAAK,CAACD,KAAK;EAE1BN,kCAAkC,CAACM,KAAK,CAAC;EAEzC,IAAIA,KAAK,CAACG,MAAM,GAAGF,KAAK,CAACG,IAAI,EAAE;IAC7B,MAAM,IAAIC,KAAK,gCAAAC,MAAA,CAAgCN,KAAK,CAACG,MAAM,oBAAAG,MAAA,CACvDL,KAAK,CAACG,IAAI,MAAG,CAAC;;EAGpB,IAAIJ,KAAK,CAACG,MAAM,GAAGF,KAAK,CAACG,IAAI,EAAE;IAC7B,MAAMG,QAAQ,GAAGN,KAAK,CAACD,KAAK,CAACQ,KAAK,EAAE;IACpC,OAAOD,QAAQ,CAACJ,MAAM,GAAGH,KAAK,CAACG,MAAM,EAAE;MACrCI,QAAQ,CAACE,OAAO,CAAC,CAAC,CAAC;;IAErBR,KAAK,GAAGJ,OAAO,CAACI,KAAK,EAAEM,QAAQ,CAAC;;EAGlC,MAAMG,UAAU,GAAGT,KAAK,CAACD,KAAK;EAC9B,MAAMW,IAAI,GAAaC,KAAK,CAACC,IAAI,CAACb,KAAK,CAAC;EACxC,KAAK,IAAIc,CAAC,GAAGd,KAAK,CAACG,MAAM,GAAG,CAAC,EAAEW,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;IAC1C,IAAIJ,UAAU,CAACI,CAAC,CAAC,KAAKd,KAAK,CAACc,CAAC,CAAC,EAAE;MAC9BH,IAAI,CAACG,CAAC,CAAC,GAAG,CAAC;KACZ,MAAM,IAAIb,KAAK,CAACD,KAAK,CAACc,CAAC,CAAC,KAAK,CAAC,EAAE;MAC/B,MAAM,IAAIT,KAAK,oBAAAC,MAAA,CACQJ,MAAM,gCAAAI,MAAA,CAA6BN,KAAK,OAAI,CAAC;;;EAGxE,MAAMe,IAAI,GAAGJ,IAAI,CAACK,GAAG,CAAC,CAACC,CAAC,EAAEH,CAAC,KAAKG,CAAC,GAAG,CAAC,GAAGH,CAAC,GAAG,CAAC,CAAC,CAAC,CAACI,MAAM,CAACJ,CAAC,IAAIA,CAAC,IAAI,CAAC,CAAC;EAEnE,IAAIC,IAAI,CAACZ,MAAM,KAAK,CAAC,EAAE;IACrB,OAAOR,KAAK,CAACM,KAAK,CAAc;;EAGlC;EACA,MAAMkB,MAAM,GAAe;IAACpB,CAAC,EAAEE;EAAK,CAAC;EACrC,MAAMmB,KAAK,GAAc;IAACT;EAAI,CAAC;EAC/B,OAAOpB,MAAM,CAAC8B,SAAS,CACnB7B,IAAI,EAAE2B,MAAmC,EACzCC,KAAgC,CAAC;AACvC;AAEA,OAAO,MAAME,WAAW,GAAG,eAAgB1B,EAAE,CAAC;EAACE;AAAY,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}