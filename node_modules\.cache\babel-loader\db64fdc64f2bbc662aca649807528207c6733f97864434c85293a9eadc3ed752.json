{"ast": null, "code": "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { ENGINE } from '../engine';\nimport { LRNGrad } from '../kernel_names';\nimport { op } from './operation';\nfunction localResponseNormalizationBackprop_(x, y, dy, depthRadius = 5, bias = 1, alpha = 1, beta = 0.5) {\n  const inputs = {\n    x,\n    y,\n    dy\n  };\n  const attrs = {\n    depthRadius,\n    bias,\n    alpha,\n    beta\n  };\n  return ENGINE.runKernel(LRNGrad, inputs, attrs);\n}\nexport const localResponseNormalizationBackprop = op({\n  localResponseNormalizationBackprop_\n});", "map": {"version": 3, "names": ["ENGINE", "LRNGrad", "op", "localResponseNormalizationBackprop_", "x", "y", "dy", "depthRadius", "bias", "alpha", "beta", "inputs", "attrs", "runKernel", "localResponseNormalizationBackprop"], "sources": ["C:\\tfjs-core\\src\\ops\\local_response_normalization_backprop.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {ENGINE} from '../engine';\nimport {LRNGrad, LRNGradAttrs, LRNGradInputs} from '../kernel_names';\nimport {NamedAttrMap} from '../kernel_registry';\nimport {Tensor4D} from '../tensor';\nimport {NamedTensorMap} from '../tensor_types';\n\nimport {op} from './operation';\n\nfunction localResponseNormalizationBackprop_<T extends Tensor4D>(\n    x: T, y: T, dy: T, depthRadius = 5, bias = 1, alpha = 1, beta = 0.5): T {\n  const inputs: LRNGradInputs = {x, y, dy};\n\n  const attrs: LRNGradAttrs = {depthRadius, bias, alpha, beta};\n\n  return ENGINE.runKernel(\n      LRNGrad, inputs as unknown as NamedTensorMap,\n      attrs as unknown as NamedAttrMap);\n}\n\nexport const localResponseNormalizationBackprop =\n    op({localResponseNormalizationBackprop_});\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAiBA,SAAQA,MAAM,QAAO,WAAW;AAChC,SAAQC,OAAO,QAAoC,iBAAiB;AAKpE,SAAQC,EAAE,QAAO,aAAa;AAE9B,SAASC,mCAAmCA,CACxCC,CAAI,EAAEC,CAAI,EAAEC,EAAK,EAAEC,WAAW,GAAG,CAAC,EAAEC,IAAI,GAAG,CAAC,EAAEC,KAAK,GAAG,CAAC,EAAEC,IAAI,GAAG,GAAG;EACrE,MAAMC,MAAM,GAAkB;IAACP,CAAC;IAAEC,CAAC;IAAEC;EAAE,CAAC;EAExC,MAAMM,KAAK,GAAiB;IAACL,WAAW;IAAEC,IAAI;IAAEC,KAAK;IAAEC;EAAI,CAAC;EAE5D,OAAOV,MAAM,CAACa,SAAS,CACnBZ,OAAO,EAAEU,MAAmC,EAC5CC,KAAgC,CAAC;AACvC;AAEA,OAAO,MAAME,kCAAkC,GAC3CZ,EAAE,CAAC;EAACC;AAAmC,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}