{"ast": null, "code": "import React,{useState}from'react';import{BankStatementImport}from'./BankStatementImport';import{BankAccountManager}from'./BankAccountManager';import{TransactionsHub}from'./TransactionsHub';import{BankBalance}from'./BankBalance';import{FileManager}from'./FileManager';import'./DataHub.css';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";export const DataHub=_ref=>{let{onTransactionImport}=_ref;const[activeTab,setActiveTab]=useState('bankStatement');const[transactionRefreshKey,setTransactionRefreshKey]=useState(0);const handleImportComplete=(transactions,bankAccount)=>{console.log(\"Imported \".concat(transactions.length,\" transactions for \").concat(bankAccount.name));if(onTransactionImport){onTransactionImport(transactions,bankAccount);}// Refresh transactions when new data is imported\nsetTransactionRefreshKey(prev=>prev+1);};const handleFileDeleted=fileId=>{console.log(\"File deleted: \".concat(fileId));// Refresh transactions when a file is deleted\nsetTransactionRefreshKey(prev=>prev+1);};const tabs=[{id:'bankStatement',label:'Bank Statements',icon:/*#__PURE__*/_jsxs(\"svg\",{width:\"20\",height:\"20\",viewBox:\"0 0 24 24\",fill:\"none\",stroke:\"currentColor\",strokeWidth:\"2\",children:[/*#__PURE__*/_jsx(\"rect\",{x:\"2\",y:\"3\",width:\"20\",height:\"14\",rx:\"2\",ry:\"2\"}),/*#__PURE__*/_jsx(\"line\",{x1:\"8\",y1:\"21\",x2:\"16\",y2:\"21\"}),/*#__PURE__*/_jsx(\"line\",{x1:\"12\",y1:\"17\",x2:\"12\",y2:\"21\"})]}),description:'Import and process bank statement CSV files'},{id:'accounts',label:'Bank Accounts',icon:/*#__PURE__*/_jsxs(\"svg\",{width:\"20\",height:\"20\",viewBox:\"0 0 24 24\",fill:\"none\",stroke:\"currentColor\",strokeWidth:\"2\",children:[/*#__PURE__*/_jsx(\"rect\",{x:\"2\",y:\"3\",width:\"20\",height:\"14\",rx:\"2\",ry:\"2\"}),/*#__PURE__*/_jsx(\"line\",{x1:\"2\",y1:\"12\",x2:\"22\",y2:\"12\"})]}),description:'Manage and configure your bank accounts'},{id:'transactions',label:'Transactions',icon:/*#__PURE__*/_jsxs(\"svg\",{width:\"20\",height:\"20\",viewBox:\"0 0 24 24\",fill:\"none\",stroke:\"currentColor\",strokeWidth:\"2\",children:[/*#__PURE__*/_jsx(\"path\",{d:\"M3 6h18\"}),/*#__PURE__*/_jsx(\"path\",{d:\"M3 12h18\"}),/*#__PURE__*/_jsx(\"path\",{d:\"M3 18h18\"}),/*#__PURE__*/_jsx(\"circle\",{cx:\"6\",cy:\"6\",r:\"1\"}),/*#__PURE__*/_jsx(\"circle\",{cx:\"6\",cy:\"12\",r:\"1\"}),/*#__PURE__*/_jsx(\"circle\",{cx:\"6\",cy:\"18\",r:\"1\"})]}),description:'View and manage all imported transactions'},{id:'bankBalance',label:'Bank Balance',icon:/*#__PURE__*/_jsx(\"svg\",{width:\"20\",height:\"20\",viewBox:\"0 0 24 24\",fill:\"none\",stroke:\"currentColor\",strokeWidth:\"2\",children:/*#__PURE__*/_jsx(\"path\",{d:\"M12 2v20M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6\"})}),description:'View daily closing balances for each account'},{id:'fileManager',label:'File Management',icon:/*#__PURE__*/_jsxs(\"svg\",{width:\"20\",height:\"20\",viewBox:\"0 0 24 24\",fill:\"none\",stroke:\"currentColor\",strokeWidth:\"2\",children:[/*#__PURE__*/_jsx(\"path\",{d:\"M13 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V9z\"}),/*#__PURE__*/_jsx(\"polyline\",{points:\"13,2 13,9 20,9\"}),/*#__PURE__*/_jsx(\"path\",{d:\"M8 13h8\"}),/*#__PURE__*/_jsx(\"path\",{d:\"M8 17h8\"})]}),description:'Manage uploaded CSV files and delete file records'},{id:'payroll',label:'Payroll Data',icon:/*#__PURE__*/_jsxs(\"svg\",{width:\"20\",height:\"20\",viewBox:\"0 0 24 24\",fill:\"none\",stroke:\"currentColor\",strokeWidth:\"2\",children:[/*#__PURE__*/_jsx(\"path\",{d:\"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2\"}),/*#__PURE__*/_jsx(\"circle\",{cx:\"9\",cy:\"7\",r:\"4\"}),/*#__PURE__*/_jsx(\"path\",{d:\"M22 21v-2a4 4 0 0 0-3-3.87\"}),/*#__PURE__*/_jsx(\"path\",{d:\"M16 3.13a4 4 0 0 1 0 7.75\"})]}),description:'Process payroll and employee compensation data'},{id:'investments',label:'Investment Data',icon:/*#__PURE__*/_jsx(\"svg\",{width:\"20\",height:\"20\",viewBox:\"0 0 24 24\",fill:\"none\",stroke:\"currentColor\",strokeWidth:\"2\",children:/*#__PURE__*/_jsx(\"polyline\",{points:\"22,12 18,12 15,21 9,3 6,12 2,12\"})}),description:'Import investment portfolio and market data'},{id:'reports',label:'Financial Reports',icon:/*#__PURE__*/_jsxs(\"svg\",{width:\"20\",height:\"20\",viewBox:\"0 0 24 24\",fill:\"none\",stroke:\"currentColor\",strokeWidth:\"2\",children:[/*#__PURE__*/_jsx(\"path\",{d:\"M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z\"}),/*#__PURE__*/_jsx(\"polyline\",{points:\"14,2 14,8 20,8\"}),/*#__PURE__*/_jsx(\"line\",{x1:\"16\",y1:\"13\",x2:\"8\",y2:\"13\"}),/*#__PURE__*/_jsx(\"line\",{x1:\"16\",y1:\"17\",x2:\"8\",y2:\"17\"}),/*#__PURE__*/_jsx(\"polyline\",{points:\"10,9 9,9 8,9\"})]}),description:'Generate and export financial reports'}];const renderTabContent=()=>{switch(activeTab){case'bankStatement':return/*#__PURE__*/_jsx(BankStatementImport,{onImportComplete:handleImportComplete});case'accounts':return/*#__PURE__*/_jsx(BankAccountManager,{});case'transactions':return/*#__PURE__*/_jsx(TransactionsHub,{refreshTrigger:transactionRefreshKey},transactionRefreshKey);case'bankBalance':return/*#__PURE__*/_jsx(BankBalance,{refreshTrigger:transactionRefreshKey},transactionRefreshKey);case'fileManager':return/*#__PURE__*/_jsx(FileManager,{onFileDeleted:handleFileDeleted});case'payroll':return/*#__PURE__*/_jsxs(\"div\",{className:\"tab-placeholder\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"placeholder-icon\",children:/*#__PURE__*/_jsxs(\"svg\",{width:\"64\",height:\"64\",viewBox:\"0 0 24 24\",fill:\"none\",stroke:\"currentColor\",strokeWidth:\"1\",children:[/*#__PURE__*/_jsx(\"path\",{d:\"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2\"}),/*#__PURE__*/_jsx(\"circle\",{cx:\"9\",cy:\"7\",r:\"4\"}),/*#__PURE__*/_jsx(\"path\",{d:\"M22 21v-2a4 4 0 0 0-3-3.87\"}),/*#__PURE__*/_jsx(\"path\",{d:\"M16 3.13a4 4 0 0 1 0 7.75\"})]})}),/*#__PURE__*/_jsx(\"h3\",{children:\"Payroll Data Import\"}),/*#__PURE__*/_jsx(\"p\",{children:\"This section will allow you to import and process payroll data, employee compensation records, and related HR financial information.\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"placeholder-features\",children:[/*#__PURE__*/_jsx(\"h4\",{children:\"Coming Soon:\"}),/*#__PURE__*/_jsxs(\"ul\",{children:[/*#__PURE__*/_jsx(\"li\",{children:\"Employee salary and wage data import\"}),/*#__PURE__*/_jsx(\"li\",{children:\"Benefits and deductions processing\"}),/*#__PURE__*/_jsx(\"li\",{children:\"Tax withholding calculations\"}),/*#__PURE__*/_jsx(\"li\",{children:\"Payroll period reconciliation\"})]})]})]});case'investments':return/*#__PURE__*/_jsxs(\"div\",{className:\"tab-placeholder\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"placeholder-icon\",children:/*#__PURE__*/_jsx(\"svg\",{width:\"64\",height:\"64\",viewBox:\"0 0 24 24\",fill:\"none\",stroke:\"currentColor\",strokeWidth:\"1\",children:/*#__PURE__*/_jsx(\"polyline\",{points:\"22,12 18,12 15,21 9,3 6,12 2,12\"})})}),/*#__PURE__*/_jsx(\"h3\",{children:\"Investment Data Import\"}),/*#__PURE__*/_jsx(\"p\",{children:\"Manage your investment portfolio data, market valuations, and performance tracking.\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"placeholder-features\",children:[/*#__PURE__*/_jsx(\"h4\",{children:\"Coming Soon:\"}),/*#__PURE__*/_jsxs(\"ul\",{children:[/*#__PURE__*/_jsx(\"li\",{children:\"Portfolio holdings import\"}),/*#__PURE__*/_jsx(\"li\",{children:\"Market price updates\"}),/*#__PURE__*/_jsx(\"li\",{children:\"Performance analytics\"}),/*#__PURE__*/_jsx(\"li\",{children:\"Asset allocation tracking\"})]})]})]});case'reports':return/*#__PURE__*/_jsxs(\"div\",{className:\"tab-placeholder\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"placeholder-icon\",children:/*#__PURE__*/_jsxs(\"svg\",{width:\"64\",height:\"64\",viewBox:\"0 0 24 24\",fill:\"none\",stroke:\"currentColor\",strokeWidth:\"1\",children:[/*#__PURE__*/_jsx(\"path\",{d:\"M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z\"}),/*#__PURE__*/_jsx(\"polyline\",{points:\"14,2 14,8 20,8\"}),/*#__PURE__*/_jsx(\"line\",{x1:\"16\",y1:\"13\",x2:\"8\",y2:\"13\"}),/*#__PURE__*/_jsx(\"line\",{x1:\"16\",y1:\"17\",x2:\"8\",y2:\"17\"}),/*#__PURE__*/_jsx(\"polyline\",{points:\"10,9 9,9 8,9\"})]})}),/*#__PURE__*/_jsx(\"h3\",{children:\"Financial Reports\"}),/*#__PURE__*/_jsx(\"p\",{children:\"Generate comprehensive financial reports and export data for analysis and compliance.\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"placeholder-features\",children:[/*#__PURE__*/_jsx(\"h4\",{children:\"Coming Soon:\"}),/*#__PURE__*/_jsxs(\"ul\",{children:[/*#__PURE__*/_jsx(\"li\",{children:\"Balance sheet generation\"}),/*#__PURE__*/_jsx(\"li\",{children:\"Income statement reports\"}),/*#__PURE__*/_jsx(\"li\",{children:\"Cash flow analysis\"}),/*#__PURE__*/_jsx(\"li\",{children:\"Custom report builder\"})]})]})]});default:return null;}};return/*#__PURE__*/_jsxs(\"div\",{className:\"datahub\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"datahub-header\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"container\",children:[/*#__PURE__*/_jsx(\"h1\",{className:\"datahub-title\",children:\"DataHub\"}),/*#__PURE__*/_jsx(\"p\",{className:\"datahub-description\",children:\"Central hub for importing, processing, and managing all your financial data\"})]})}),/*#__PURE__*/_jsx(\"div\",{className:\"datahub-content\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"container\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"datahub-tabs\",children:/*#__PURE__*/_jsx(\"div\",{className:\"tab-list\",children:tabs.map(tab=>/*#__PURE__*/_jsxs(\"button\",{onClick:()=>setActiveTab(tab.id),className:\"tab-button \".concat(activeTab===tab.id?'active':''),children:[/*#__PURE__*/_jsx(\"div\",{className:\"tab-icon\",children:tab.icon}),/*#__PURE__*/_jsxs(\"div\",{className:\"tab-content\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"tab-label\",children:tab.label}),/*#__PURE__*/_jsx(\"div\",{className:\"tab-description\",children:tab.description})]})]},tab.id))})}),/*#__PURE__*/_jsx(\"div\",{className:\"tab-panel\",children:renderTabContent()})]})})]});};", "map": {"version": 3, "names": ["React", "useState", "BankStatementImport", "BankAccountManager", "TransactionsHub", "BankBalance", "FileManager", "jsx", "_jsx", "jsxs", "_jsxs", "DataHub", "_ref", "onTransactionImport", "activeTab", "setActiveTab", "transactionRefreshKey", "setTransactionRefreshKey", "handleImportComplete", "transactions", "bankAccount", "console", "log", "concat", "length", "name", "prev", "handleFileDeleted", "fileId", "tabs", "id", "label", "icon", "width", "height", "viewBox", "fill", "stroke", "strokeWidth", "children", "x", "y", "rx", "ry", "x1", "y1", "x2", "y2", "description", "d", "cx", "cy", "r", "points", "renderTabContent", "onImportComplete", "refreshTrigger", "onFileDeleted", "className", "map", "tab", "onClick"], "sources": ["C:/tmsft/src/components/DataHub.tsx"], "sourcesContent": ["import React, { useState } from 'react';\r\nimport { BankStatementImport } from './BankStatementImport';\r\nimport { BankAccountManager } from './BankAccountManager';\r\nimport { TransactionsHub } from './TransactionsHub';\r\nimport { BankBalance } from './BankBalance';\r\nimport { FileManager } from './FileManager';\r\nimport { Transaction, BankAccount } from '../types';\r\nimport './DataHub.css';\r\n\r\ninterface DataHubProps {\r\n  onTransactionImport?: (transactions: Transaction[], bankAccount: BankAccount) => void;\r\n}\r\n\r\nexport const DataHub: React.FC<DataHubProps> = ({ onTransactionImport }) => {\r\n  const [activeTab, setActiveTab] = useState<'bankStatement' | 'accounts' | 'transactions' | 'bankBalance' | 'fileManager' | 'payroll' | 'investments' | 'reports'>('bankStatement');\r\n  const [transactionRefreshKey, setTransactionRefreshKey] = useState(0);\r\n\r\n  const handleImportComplete = (transactions: Transaction[], bankAccount: BankAccount) => {\r\n    console.log(`Imported ${transactions.length} transactions for ${bankAccount.name}`);\r\n    if (onTransactionImport) {\r\n      onTransactionImport(transactions, bankAccount);\r\n    }\r\n    // Refresh transactions when new data is imported\r\n    setTransactionRefreshKey(prev => prev + 1);\r\n  };\r\n\r\n  const handleFileDeleted = (fileId: string) => {\r\n    console.log(`File deleted: ${fileId}`);\r\n    // Refresh transactions when a file is deleted\r\n    setTransactionRefreshKey(prev => prev + 1);\r\n  };\r\n\r\n  const tabs = [\r\n    {\r\n      id: 'bankStatement' as const,\r\n      label: 'Bank Statements',\r\n      icon: (\r\n        <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\">\r\n          <rect x=\"2\" y=\"3\" width=\"20\" height=\"14\" rx=\"2\" ry=\"2\" />\r\n          <line x1=\"8\" y1=\"21\" x2=\"16\" y2=\"21\" />\r\n          <line x1=\"12\" y1=\"17\" x2=\"12\" y2=\"21\" />\r\n        </svg>\r\n      ),\r\n      description: 'Import and process bank statement CSV files'\r\n    },\r\n    {\r\n      id: 'accounts' as const,\r\n      label: 'Bank Accounts',\r\n      icon: (\r\n        <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\">\r\n          <rect x=\"2\" y=\"3\" width=\"20\" height=\"14\" rx=\"2\" ry=\"2\" />\r\n          <line x1=\"2\" y1=\"12\" x2=\"22\" y2=\"12\" />\r\n        </svg>\r\n      ),\r\n      description: 'Manage and configure your bank accounts'\r\n    },\r\n    {\r\n      id: 'transactions' as const,\r\n      label: 'Transactions',\r\n      icon: (\r\n        <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\">\r\n          <path d=\"M3 6h18\" />\r\n          <path d=\"M3 12h18\" />\r\n          <path d=\"M3 18h18\" />\r\n          <circle cx=\"6\" cy=\"6\" r=\"1\" />\r\n          <circle cx=\"6\" cy=\"12\" r=\"1\" />\r\n          <circle cx=\"6\" cy=\"18\" r=\"1\" />\r\n        </svg>\r\n      ),\r\n      description: 'View and manage all imported transactions'\r\n    },\r\n    {\r\n      id: 'bankBalance' as const,\r\n      label: 'Bank Balance',\r\n      icon: (\r\n        <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\">\r\n          <path d=\"M12 2v20M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6\" />\r\n        </svg>\r\n      ),\r\n      description: 'View daily closing balances for each account'\r\n    },\r\n    {\r\n      id: 'fileManager' as const,\r\n      label: 'File Management',\r\n      icon: (\r\n        <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\">\r\n          <path d=\"M13 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V9z\" />\r\n          <polyline points=\"13,2 13,9 20,9\" />\r\n          <path d=\"M8 13h8\" />\r\n          <path d=\"M8 17h8\" />\r\n        </svg>\r\n      ),\r\n      description: 'Manage uploaded CSV files and delete file records'\r\n    },\r\n    {\r\n      id: 'payroll' as const,\r\n      label: 'Payroll Data',\r\n      icon: (\r\n        <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\">\r\n          <path d=\"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2\" />\r\n          <circle cx=\"9\" cy=\"7\" r=\"4\" />\r\n          <path d=\"M22 21v-2a4 4 0 0 0-3-3.87\" />\r\n          <path d=\"M16 3.13a4 4 0 0 1 0 7.75\" />\r\n        </svg>\r\n      ),\r\n      description: 'Process payroll and employee compensation data'\r\n    },\r\n    {\r\n      id: 'investments' as const,\r\n      label: 'Investment Data',\r\n      icon: (\r\n        <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\">\r\n          <polyline points=\"22,12 18,12 15,21 9,3 6,12 2,12\" />\r\n        </svg>\r\n      ),\r\n      description: 'Import investment portfolio and market data'\r\n    },\r\n    {\r\n      id: 'reports' as const,\r\n      label: 'Financial Reports',\r\n      icon: (\r\n        <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\">\r\n          <path d=\"M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z\" />\r\n          <polyline points=\"14,2 14,8 20,8\" />\r\n          <line x1=\"16\" y1=\"13\" x2=\"8\" y2=\"13\" />\r\n          <line x1=\"16\" y1=\"17\" x2=\"8\" y2=\"17\" />\r\n          <polyline points=\"10,9 9,9 8,9\" />\r\n        </svg>\r\n      ),\r\n      description: 'Generate and export financial reports'\r\n    }\r\n  ];\r\n\r\n  const renderTabContent = () => {\r\n    switch (activeTab) {\r\n      case 'bankStatement':\r\n        return <BankStatementImport onImportComplete={handleImportComplete} />;\r\n      case 'accounts':\r\n        return <BankAccountManager />;\r\n      case 'transactions':\r\n        return <TransactionsHub key={transactionRefreshKey} refreshTrigger={transactionRefreshKey} />;\r\n      case 'bankBalance':\r\n        return <BankBalance key={transactionRefreshKey} refreshTrigger={transactionRefreshKey} />;\r\n      case 'fileManager':\r\n        return <FileManager onFileDeleted={handleFileDeleted} />;\r\n      case 'payroll':\r\n        return (\r\n          <div className=\"tab-placeholder\">\r\n            <div className=\"placeholder-icon\">\r\n              <svg width=\"64\" height=\"64\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"1\">\r\n                <path d=\"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2\" />\r\n                <circle cx=\"9\" cy=\"7\" r=\"4\" />\r\n                <path d=\"M22 21v-2a4 4 0 0 0-3-3.87\" />\r\n                <path d=\"M16 3.13a4 4 0 0 1 0 7.75\" />\r\n              </svg>\r\n            </div>\r\n            <h3>Payroll Data Import</h3>\r\n            <p>This section will allow you to import and process payroll data, employee compensation records, and related HR financial information.</p>\r\n            <div className=\"placeholder-features\">\r\n              <h4>Coming Soon:</h4>\r\n              <ul>\r\n                <li>Employee salary and wage data import</li>\r\n                <li>Benefits and deductions processing</li>\r\n                <li>Tax withholding calculations</li>\r\n                <li>Payroll period reconciliation</li>\r\n              </ul>\r\n            </div>\r\n          </div>\r\n        );\r\n      case 'investments':\r\n        return (\r\n          <div className=\"tab-placeholder\">\r\n            <div className=\"placeholder-icon\">\r\n              <svg width=\"64\" height=\"64\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"1\">\r\n                <polyline points=\"22,12 18,12 15,21 9,3 6,12 2,12\" />\r\n              </svg>\r\n            </div>\r\n            <h3>Investment Data Import</h3>\r\n            <p>Manage your investment portfolio data, market valuations, and performance tracking.</p>\r\n            <div className=\"placeholder-features\">\r\n              <h4>Coming Soon:</h4>\r\n              <ul>\r\n                <li>Portfolio holdings import</li>\r\n                <li>Market price updates</li>\r\n                <li>Performance analytics</li>\r\n                <li>Asset allocation tracking</li>\r\n              </ul>\r\n            </div>\r\n          </div>\r\n        );\r\n      case 'reports':\r\n        return (\r\n          <div className=\"tab-placeholder\">\r\n            <div className=\"placeholder-icon\">\r\n              <svg width=\"64\" height=\"64\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"1\">\r\n                <path d=\"M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z\" />\r\n                <polyline points=\"14,2 14,8 20,8\" />\r\n                <line x1=\"16\" y1=\"13\" x2=\"8\" y2=\"13\" />\r\n                <line x1=\"16\" y1=\"17\" x2=\"8\" y2=\"17\" />\r\n                <polyline points=\"10,9 9,9 8,9\" />\r\n              </svg>\r\n            </div>\r\n            <h3>Financial Reports</h3>\r\n            <p>Generate comprehensive financial reports and export data for analysis and compliance.</p>\r\n            <div className=\"placeholder-features\">\r\n              <h4>Coming Soon:</h4>\r\n              <ul>\r\n                <li>Balance sheet generation</li>\r\n                <li>Income statement reports</li>\r\n                <li>Cash flow analysis</li>\r\n                <li>Custom report builder</li>\r\n              </ul>\r\n            </div>\r\n          </div>\r\n        );\r\n      default:\r\n        return null;\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className=\"datahub\">\r\n      <div className=\"datahub-header\">\r\n        <div className=\"container\">\r\n          <h1 className=\"datahub-title\">DataHub</h1>\r\n          <p className=\"datahub-description\">\r\n            Central hub for importing, processing, and managing all your financial data\r\n          </p>\r\n        </div>\r\n      </div>\r\n\r\n      <div className=\"datahub-content\">\r\n        <div className=\"container\">\r\n          <div className=\"datahub-tabs\">\r\n            <div className=\"tab-list\">\r\n              {tabs.map((tab) => (\r\n                <button\r\n                  key={tab.id}\r\n                  onClick={() => setActiveTab(tab.id)}\r\n                  className={`tab-button ${activeTab === tab.id ? 'active' : ''}`}\r\n                >\r\n                  <div className=\"tab-icon\">{tab.icon}</div>\r\n                  <div className=\"tab-content\">\r\n                    <div className=\"tab-label\">{tab.label}</div>\r\n                    <div className=\"tab-description\">{tab.description}</div>\r\n                  </div>\r\n                </button>\r\n              ))}\r\n            </div>\r\n          </div>\r\n\r\n          <div className=\"tab-panel\">\r\n            {renderTabContent()}\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n}; "], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,KAAQ,OAAO,CACvC,OAASC,mBAAmB,KAAQ,uBAAuB,CAC3D,OAASC,kBAAkB,KAAQ,sBAAsB,CACzD,OAASC,eAAe,KAAQ,mBAAmB,CACnD,OAASC,WAAW,KAAQ,eAAe,CAC3C,OAASC,WAAW,KAAQ,eAAe,CAE3C,MAAO,eAAe,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAMvB,MAAO,MAAM,CAAAC,OAA+B,CAAGC,IAAA,EAA6B,IAA5B,CAAEC,mBAAoB,CAAC,CAAAD,IAAA,CACrE,KAAM,CAACE,SAAS,CAAEC,YAAY,CAAC,CAAGd,QAAQ,CAAwH,eAAe,CAAC,CAClL,KAAM,CAACe,qBAAqB,CAAEC,wBAAwB,CAAC,CAAGhB,QAAQ,CAAC,CAAC,CAAC,CAErE,KAAM,CAAAiB,oBAAoB,CAAGA,CAACC,YAA2B,CAAEC,WAAwB,GAAK,CACtFC,OAAO,CAACC,GAAG,aAAAC,MAAA,CAAaJ,YAAY,CAACK,MAAM,uBAAAD,MAAA,CAAqBH,WAAW,CAACK,IAAI,CAAE,CAAC,CACnF,GAAIZ,mBAAmB,CAAE,CACvBA,mBAAmB,CAACM,YAAY,CAAEC,WAAW,CAAC,CAChD,CACA;AACAH,wBAAwB,CAACS,IAAI,EAAIA,IAAI,CAAG,CAAC,CAAC,CAC5C,CAAC,CAED,KAAM,CAAAC,iBAAiB,CAAIC,MAAc,EAAK,CAC5CP,OAAO,CAACC,GAAG,kBAAAC,MAAA,CAAkBK,MAAM,CAAE,CAAC,CACtC;AACAX,wBAAwB,CAACS,IAAI,EAAIA,IAAI,CAAG,CAAC,CAAC,CAC5C,CAAC,CAED,KAAM,CAAAG,IAAI,CAAG,CACX,CACEC,EAAE,CAAE,eAAwB,CAC5BC,KAAK,CAAE,iBAAiB,CACxBC,IAAI,cACFtB,KAAA,QAAKuB,KAAK,CAAC,IAAI,CAACC,MAAM,CAAC,IAAI,CAACC,OAAO,CAAC,WAAW,CAACC,IAAI,CAAC,MAAM,CAACC,MAAM,CAAC,cAAc,CAACC,WAAW,CAAC,GAAG,CAAAC,QAAA,eAC/F/B,IAAA,SAAMgC,CAAC,CAAC,GAAG,CAACC,CAAC,CAAC,GAAG,CAACR,KAAK,CAAC,IAAI,CAACC,MAAM,CAAC,IAAI,CAACQ,EAAE,CAAC,GAAG,CAACC,EAAE,CAAC,GAAG,CAAE,CAAC,cACzDnC,IAAA,SAAMoC,EAAE,CAAC,GAAG,CAACC,EAAE,CAAC,IAAI,CAACC,EAAE,CAAC,IAAI,CAACC,EAAE,CAAC,IAAI,CAAE,CAAC,cACvCvC,IAAA,SAAMoC,EAAE,CAAC,IAAI,CAACC,EAAE,CAAC,IAAI,CAACC,EAAE,CAAC,IAAI,CAACC,EAAE,CAAC,IAAI,CAAE,CAAC,EACrC,CACN,CACDC,WAAW,CAAE,6CACf,CAAC,CACD,CACElB,EAAE,CAAE,UAAmB,CACvBC,KAAK,CAAE,eAAe,CACtBC,IAAI,cACFtB,KAAA,QAAKuB,KAAK,CAAC,IAAI,CAACC,MAAM,CAAC,IAAI,CAACC,OAAO,CAAC,WAAW,CAACC,IAAI,CAAC,MAAM,CAACC,MAAM,CAAC,cAAc,CAACC,WAAW,CAAC,GAAG,CAAAC,QAAA,eAC/F/B,IAAA,SAAMgC,CAAC,CAAC,GAAG,CAACC,CAAC,CAAC,GAAG,CAACR,KAAK,CAAC,IAAI,CAACC,MAAM,CAAC,IAAI,CAACQ,EAAE,CAAC,GAAG,CAACC,EAAE,CAAC,GAAG,CAAE,CAAC,cACzDnC,IAAA,SAAMoC,EAAE,CAAC,GAAG,CAACC,EAAE,CAAC,IAAI,CAACC,EAAE,CAAC,IAAI,CAACC,EAAE,CAAC,IAAI,CAAE,CAAC,EACpC,CACN,CACDC,WAAW,CAAE,yCACf,CAAC,CACD,CACElB,EAAE,CAAE,cAAuB,CAC3BC,KAAK,CAAE,cAAc,CACrBC,IAAI,cACFtB,KAAA,QAAKuB,KAAK,CAAC,IAAI,CAACC,MAAM,CAAC,IAAI,CAACC,OAAO,CAAC,WAAW,CAACC,IAAI,CAAC,MAAM,CAACC,MAAM,CAAC,cAAc,CAACC,WAAW,CAAC,GAAG,CAAAC,QAAA,eAC/F/B,IAAA,SAAMyC,CAAC,CAAC,SAAS,CAAE,CAAC,cACpBzC,IAAA,SAAMyC,CAAC,CAAC,UAAU,CAAE,CAAC,cACrBzC,IAAA,SAAMyC,CAAC,CAAC,UAAU,CAAE,CAAC,cACrBzC,IAAA,WAAQ0C,EAAE,CAAC,GAAG,CAACC,EAAE,CAAC,GAAG,CAACC,CAAC,CAAC,GAAG,CAAE,CAAC,cAC9B5C,IAAA,WAAQ0C,EAAE,CAAC,GAAG,CAACC,EAAE,CAAC,IAAI,CAACC,CAAC,CAAC,GAAG,CAAE,CAAC,cAC/B5C,IAAA,WAAQ0C,EAAE,CAAC,GAAG,CAACC,EAAE,CAAC,IAAI,CAACC,CAAC,CAAC,GAAG,CAAE,CAAC,EAC5B,CACN,CACDJ,WAAW,CAAE,2CACf,CAAC,CACD,CACElB,EAAE,CAAE,aAAsB,CAC1BC,KAAK,CAAE,cAAc,CACrBC,IAAI,cACFxB,IAAA,QAAKyB,KAAK,CAAC,IAAI,CAACC,MAAM,CAAC,IAAI,CAACC,OAAO,CAAC,WAAW,CAACC,IAAI,CAAC,MAAM,CAACC,MAAM,CAAC,cAAc,CAACC,WAAW,CAAC,GAAG,CAAAC,QAAA,cAC/F/B,IAAA,SAAMyC,CAAC,CAAC,2DAA2D,CAAE,CAAC,CACnE,CACN,CACDD,WAAW,CAAE,8CACf,CAAC,CACD,CACElB,EAAE,CAAE,aAAsB,CAC1BC,KAAK,CAAE,iBAAiB,CACxBC,IAAI,cACFtB,KAAA,QAAKuB,KAAK,CAAC,IAAI,CAACC,MAAM,CAAC,IAAI,CAACC,OAAO,CAAC,WAAW,CAACC,IAAI,CAAC,MAAM,CAACC,MAAM,CAAC,cAAc,CAACC,WAAW,CAAC,GAAG,CAAAC,QAAA,eAC/F/B,IAAA,SAAMyC,CAAC,CAAC,4DAA4D,CAAE,CAAC,cACvEzC,IAAA,aAAU6C,MAAM,CAAC,gBAAgB,CAAE,CAAC,cACpC7C,IAAA,SAAMyC,CAAC,CAAC,SAAS,CAAE,CAAC,cACpBzC,IAAA,SAAMyC,CAAC,CAAC,SAAS,CAAE,CAAC,EACjB,CACN,CACDD,WAAW,CAAE,mDACf,CAAC,CACD,CACElB,EAAE,CAAE,SAAkB,CACtBC,KAAK,CAAE,cAAc,CACrBC,IAAI,cACFtB,KAAA,QAAKuB,KAAK,CAAC,IAAI,CAACC,MAAM,CAAC,IAAI,CAACC,OAAO,CAAC,WAAW,CAACC,IAAI,CAAC,MAAM,CAACC,MAAM,CAAC,cAAc,CAACC,WAAW,CAAC,GAAG,CAAAC,QAAA,eAC/F/B,IAAA,SAAMyC,CAAC,CAAC,2CAA2C,CAAE,CAAC,cACtDzC,IAAA,WAAQ0C,EAAE,CAAC,GAAG,CAACC,EAAE,CAAC,GAAG,CAACC,CAAC,CAAC,GAAG,CAAE,CAAC,cAC9B5C,IAAA,SAAMyC,CAAC,CAAC,4BAA4B,CAAE,CAAC,cACvCzC,IAAA,SAAMyC,CAAC,CAAC,2BAA2B,CAAE,CAAC,EACnC,CACN,CACDD,WAAW,CAAE,gDACf,CAAC,CACD,CACElB,EAAE,CAAE,aAAsB,CAC1BC,KAAK,CAAE,iBAAiB,CACxBC,IAAI,cACFxB,IAAA,QAAKyB,KAAK,CAAC,IAAI,CAACC,MAAM,CAAC,IAAI,CAACC,OAAO,CAAC,WAAW,CAACC,IAAI,CAAC,MAAM,CAACC,MAAM,CAAC,cAAc,CAACC,WAAW,CAAC,GAAG,CAAAC,QAAA,cAC/F/B,IAAA,aAAU6C,MAAM,CAAC,iCAAiC,CAAE,CAAC,CAClD,CACN,CACDL,WAAW,CAAE,6CACf,CAAC,CACD,CACElB,EAAE,CAAE,SAAkB,CACtBC,KAAK,CAAE,mBAAmB,CAC1BC,IAAI,cACFtB,KAAA,QAAKuB,KAAK,CAAC,IAAI,CAACC,MAAM,CAAC,IAAI,CAACC,OAAO,CAAC,WAAW,CAACC,IAAI,CAAC,MAAM,CAACC,MAAM,CAAC,cAAc,CAACC,WAAW,CAAC,GAAG,CAAAC,QAAA,eAC/F/B,IAAA,SAAMyC,CAAC,CAAC,4DAA4D,CAAE,CAAC,cACvEzC,IAAA,aAAU6C,MAAM,CAAC,gBAAgB,CAAE,CAAC,cACpC7C,IAAA,SAAMoC,EAAE,CAAC,IAAI,CAACC,EAAE,CAAC,IAAI,CAACC,EAAE,CAAC,GAAG,CAACC,EAAE,CAAC,IAAI,CAAE,CAAC,cACvCvC,IAAA,SAAMoC,EAAE,CAAC,IAAI,CAACC,EAAE,CAAC,IAAI,CAACC,EAAE,CAAC,GAAG,CAACC,EAAE,CAAC,IAAI,CAAE,CAAC,cACvCvC,IAAA,aAAU6C,MAAM,CAAC,cAAc,CAAE,CAAC,EAC/B,CACN,CACDL,WAAW,CAAE,uCACf,CAAC,CACF,CAED,KAAM,CAAAM,gBAAgB,CAAGA,CAAA,GAAM,CAC7B,OAAQxC,SAAS,EACf,IAAK,eAAe,CAClB,mBAAON,IAAA,CAACN,mBAAmB,EAACqD,gBAAgB,CAAErC,oBAAqB,CAAE,CAAC,CACxE,IAAK,UAAU,CACb,mBAAOV,IAAA,CAACL,kBAAkB,GAAE,CAAC,CAC/B,IAAK,cAAc,CACjB,mBAAOK,IAAA,CAACJ,eAAe,EAA6BoD,cAAc,CAAExC,qBAAsB,EAA7DA,qBAA+D,CAAC,CAC/F,IAAK,aAAa,CAChB,mBAAOR,IAAA,CAACH,WAAW,EAA6BmD,cAAc,CAAExC,qBAAsB,EAA7DA,qBAA+D,CAAC,CAC3F,IAAK,aAAa,CAChB,mBAAOR,IAAA,CAACF,WAAW,EAACmD,aAAa,CAAE9B,iBAAkB,CAAE,CAAC,CAC1D,IAAK,SAAS,CACZ,mBACEjB,KAAA,QAAKgD,SAAS,CAAC,iBAAiB,CAAAnB,QAAA,eAC9B/B,IAAA,QAAKkD,SAAS,CAAC,kBAAkB,CAAAnB,QAAA,cAC/B7B,KAAA,QAAKuB,KAAK,CAAC,IAAI,CAACC,MAAM,CAAC,IAAI,CAACC,OAAO,CAAC,WAAW,CAACC,IAAI,CAAC,MAAM,CAACC,MAAM,CAAC,cAAc,CAACC,WAAW,CAAC,GAAG,CAAAC,QAAA,eAC/F/B,IAAA,SAAMyC,CAAC,CAAC,2CAA2C,CAAE,CAAC,cACtDzC,IAAA,WAAQ0C,EAAE,CAAC,GAAG,CAACC,EAAE,CAAC,GAAG,CAACC,CAAC,CAAC,GAAG,CAAE,CAAC,cAC9B5C,IAAA,SAAMyC,CAAC,CAAC,4BAA4B,CAAE,CAAC,cACvCzC,IAAA,SAAMyC,CAAC,CAAC,2BAA2B,CAAE,CAAC,EACnC,CAAC,CACH,CAAC,cACNzC,IAAA,OAAA+B,QAAA,CAAI,qBAAmB,CAAI,CAAC,cAC5B/B,IAAA,MAAA+B,QAAA,CAAG,sIAAoI,CAAG,CAAC,cAC3I7B,KAAA,QAAKgD,SAAS,CAAC,sBAAsB,CAAAnB,QAAA,eACnC/B,IAAA,OAAA+B,QAAA,CAAI,cAAY,CAAI,CAAC,cACrB7B,KAAA,OAAA6B,QAAA,eACE/B,IAAA,OAAA+B,QAAA,CAAI,sCAAoC,CAAI,CAAC,cAC7C/B,IAAA,OAAA+B,QAAA,CAAI,oCAAkC,CAAI,CAAC,cAC3C/B,IAAA,OAAA+B,QAAA,CAAI,8BAA4B,CAAI,CAAC,cACrC/B,IAAA,OAAA+B,QAAA,CAAI,+BAA6B,CAAI,CAAC,EACpC,CAAC,EACF,CAAC,EACH,CAAC,CAEV,IAAK,aAAa,CAChB,mBACE7B,KAAA,QAAKgD,SAAS,CAAC,iBAAiB,CAAAnB,QAAA,eAC9B/B,IAAA,QAAKkD,SAAS,CAAC,kBAAkB,CAAAnB,QAAA,cAC/B/B,IAAA,QAAKyB,KAAK,CAAC,IAAI,CAACC,MAAM,CAAC,IAAI,CAACC,OAAO,CAAC,WAAW,CAACC,IAAI,CAAC,MAAM,CAACC,MAAM,CAAC,cAAc,CAACC,WAAW,CAAC,GAAG,CAAAC,QAAA,cAC/F/B,IAAA,aAAU6C,MAAM,CAAC,iCAAiC,CAAE,CAAC,CAClD,CAAC,CACH,CAAC,cACN7C,IAAA,OAAA+B,QAAA,CAAI,wBAAsB,CAAI,CAAC,cAC/B/B,IAAA,MAAA+B,QAAA,CAAG,qFAAmF,CAAG,CAAC,cAC1F7B,KAAA,QAAKgD,SAAS,CAAC,sBAAsB,CAAAnB,QAAA,eACnC/B,IAAA,OAAA+B,QAAA,CAAI,cAAY,CAAI,CAAC,cACrB7B,KAAA,OAAA6B,QAAA,eACE/B,IAAA,OAAA+B,QAAA,CAAI,2BAAyB,CAAI,CAAC,cAClC/B,IAAA,OAAA+B,QAAA,CAAI,sBAAoB,CAAI,CAAC,cAC7B/B,IAAA,OAAA+B,QAAA,CAAI,uBAAqB,CAAI,CAAC,cAC9B/B,IAAA,OAAA+B,QAAA,CAAI,2BAAyB,CAAI,CAAC,EAChC,CAAC,EACF,CAAC,EACH,CAAC,CAEV,IAAK,SAAS,CACZ,mBACE7B,KAAA,QAAKgD,SAAS,CAAC,iBAAiB,CAAAnB,QAAA,eAC9B/B,IAAA,QAAKkD,SAAS,CAAC,kBAAkB,CAAAnB,QAAA,cAC/B7B,KAAA,QAAKuB,KAAK,CAAC,IAAI,CAACC,MAAM,CAAC,IAAI,CAACC,OAAO,CAAC,WAAW,CAACC,IAAI,CAAC,MAAM,CAACC,MAAM,CAAC,cAAc,CAACC,WAAW,CAAC,GAAG,CAAAC,QAAA,eAC/F/B,IAAA,SAAMyC,CAAC,CAAC,4DAA4D,CAAE,CAAC,cACvEzC,IAAA,aAAU6C,MAAM,CAAC,gBAAgB,CAAE,CAAC,cACpC7C,IAAA,SAAMoC,EAAE,CAAC,IAAI,CAACC,EAAE,CAAC,IAAI,CAACC,EAAE,CAAC,GAAG,CAACC,EAAE,CAAC,IAAI,CAAE,CAAC,cACvCvC,IAAA,SAAMoC,EAAE,CAAC,IAAI,CAACC,EAAE,CAAC,IAAI,CAACC,EAAE,CAAC,GAAG,CAACC,EAAE,CAAC,IAAI,CAAE,CAAC,cACvCvC,IAAA,aAAU6C,MAAM,CAAC,cAAc,CAAE,CAAC,EAC/B,CAAC,CACH,CAAC,cACN7C,IAAA,OAAA+B,QAAA,CAAI,mBAAiB,CAAI,CAAC,cAC1B/B,IAAA,MAAA+B,QAAA,CAAG,uFAAqF,CAAG,CAAC,cAC5F7B,KAAA,QAAKgD,SAAS,CAAC,sBAAsB,CAAAnB,QAAA,eACnC/B,IAAA,OAAA+B,QAAA,CAAI,cAAY,CAAI,CAAC,cACrB7B,KAAA,OAAA6B,QAAA,eACE/B,IAAA,OAAA+B,QAAA,CAAI,0BAAwB,CAAI,CAAC,cACjC/B,IAAA,OAAA+B,QAAA,CAAI,0BAAwB,CAAI,CAAC,cACjC/B,IAAA,OAAA+B,QAAA,CAAI,oBAAkB,CAAI,CAAC,cAC3B/B,IAAA,OAAA+B,QAAA,CAAI,uBAAqB,CAAI,CAAC,EAC5B,CAAC,EACF,CAAC,EACH,CAAC,CAEV,QACE,MAAO,KAAI,CACf,CACF,CAAC,CAED,mBACE7B,KAAA,QAAKgD,SAAS,CAAC,SAAS,CAAAnB,QAAA,eACtB/B,IAAA,QAAKkD,SAAS,CAAC,gBAAgB,CAAAnB,QAAA,cAC7B7B,KAAA,QAAKgD,SAAS,CAAC,WAAW,CAAAnB,QAAA,eACxB/B,IAAA,OAAIkD,SAAS,CAAC,eAAe,CAAAnB,QAAA,CAAC,SAAO,CAAI,CAAC,cAC1C/B,IAAA,MAAGkD,SAAS,CAAC,qBAAqB,CAAAnB,QAAA,CAAC,6EAEnC,CAAG,CAAC,EACD,CAAC,CACH,CAAC,cAEN/B,IAAA,QAAKkD,SAAS,CAAC,iBAAiB,CAAAnB,QAAA,cAC9B7B,KAAA,QAAKgD,SAAS,CAAC,WAAW,CAAAnB,QAAA,eACxB/B,IAAA,QAAKkD,SAAS,CAAC,cAAc,CAAAnB,QAAA,cAC3B/B,IAAA,QAAKkD,SAAS,CAAC,UAAU,CAAAnB,QAAA,CACtBV,IAAI,CAAC8B,GAAG,CAAEC,GAAG,eACZlD,KAAA,WAEEmD,OAAO,CAAEA,CAAA,GAAM9C,YAAY,CAAC6C,GAAG,CAAC9B,EAAE,CAAE,CACpC4B,SAAS,eAAAnC,MAAA,CAAgBT,SAAS,GAAK8C,GAAG,CAAC9B,EAAE,CAAG,QAAQ,CAAG,EAAE,CAAG,CAAAS,QAAA,eAEhE/B,IAAA,QAAKkD,SAAS,CAAC,UAAU,CAAAnB,QAAA,CAAEqB,GAAG,CAAC5B,IAAI,CAAM,CAAC,cAC1CtB,KAAA,QAAKgD,SAAS,CAAC,aAAa,CAAAnB,QAAA,eAC1B/B,IAAA,QAAKkD,SAAS,CAAC,WAAW,CAAAnB,QAAA,CAAEqB,GAAG,CAAC7B,KAAK,CAAM,CAAC,cAC5CvB,IAAA,QAAKkD,SAAS,CAAC,iBAAiB,CAAAnB,QAAA,CAAEqB,GAAG,CAACZ,WAAW,CAAM,CAAC,EACrD,CAAC,GARDY,GAAG,CAAC9B,EASH,CACT,CAAC,CACC,CAAC,CACH,CAAC,cAENtB,IAAA,QAAKkD,SAAS,CAAC,WAAW,CAAAnB,QAAA,CACvBe,gBAAgB,CAAC,CAAC,CAChB,CAAC,EACH,CAAC,CACH,CAAC,EACH,CAAC,CAEV,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}