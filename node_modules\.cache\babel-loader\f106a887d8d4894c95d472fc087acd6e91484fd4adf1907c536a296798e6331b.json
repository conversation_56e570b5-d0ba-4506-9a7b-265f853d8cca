{"ast": null, "code": "/**\n * @license\n * Copyright 2018 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { ENGINE } from '../engine';\nimport { Asin } from '../kernel_names';\nimport { convertToTensor } from '../tensor_util_env';\nimport { op } from './operation';\n/**\n * Computes asin of the input `tf.Tensor` element-wise: `asin(x)`\n *\n * ```js\n * const x = tf.tensor1d([0, 1, -1, .7]);\n *\n * x.asin().print();  // or tf.asin(x)\n * ```\n * @param x The input tensor.\n * @doc {heading: 'Operations', subheading: 'Basic math'}\n */\nfunction asin_(x) {\n  const $x = convertToTensor(x, 'x', 'asin');\n  const inputs = {\n    x: $x\n  };\n  return ENGINE.runKernel(Asin, inputs);\n}\nexport const asin = /* @__PURE__ */op({\n  asin_\n});", "map": {"version": 3, "names": ["ENGINE", "<PERSON><PERSON>", "convertToTensor", "op", "asin_", "x", "$x", "inputs", "runKernel", "asin"], "sources": ["C:\\tfjs-core\\src\\ops\\asin.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2018 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {ENGINE} from '../engine';\nimport {Asin, AsinInputs} from '../kernel_names';\nimport {Tensor} from '../tensor';\nimport {NamedTensorMap} from '../tensor_types';\nimport {convertToTensor} from '../tensor_util_env';\nimport {TensorLike} from '../types';\n\nimport {op} from './operation';\n\n/**\n * Computes asin of the input `tf.Tensor` element-wise: `asin(x)`\n *\n * ```js\n * const x = tf.tensor1d([0, 1, -1, .7]);\n *\n * x.asin().print();  // or tf.asin(x)\n * ```\n * @param x The input tensor.\n * @doc {heading: 'Operations', subheading: 'Basic math'}\n */\nfunction asin_<T extends Tensor>(x: T|TensorLike): T {\n  const $x = convertToTensor(x, 'x', 'asin');\n  const inputs: AsinInputs = {x: $x};\n\n  return ENGINE.runKernel(Asin, inputs as unknown as NamedTensorMap);\n}\nexport const asin = /* @__PURE__ */ op({asin_});\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAiBA,SAAQA,MAAM,QAAO,WAAW;AAChC,SAAQC,IAAI,QAAmB,iBAAiB;AAGhD,SAAQC,eAAe,QAAO,oBAAoB;AAGlD,SAAQC,EAAE,QAAO,aAAa;AAE9B;;;;;;;;;;;AAWA,SAASC,KAAKA,CAAmBC,CAAe;EAC9C,MAAMC,EAAE,GAAGJ,eAAe,CAACG,CAAC,EAAE,GAAG,EAAE,MAAM,CAAC;EAC1C,MAAME,MAAM,GAAe;IAACF,CAAC,EAAEC;EAAE,CAAC;EAElC,OAAON,MAAM,CAACQ,SAAS,CAACP,IAAI,EAAEM,MAAmC,CAAC;AACpE;AACA,OAAO,MAAME,IAAI,GAAG,eAAgBN,EAAE,CAAC;EAACC;AAAK,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}