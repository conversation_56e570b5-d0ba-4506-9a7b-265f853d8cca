{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.transformArguments = exports.IS_READ_ONLY = exports.FIRST_KEY_INDEX = void 0;\nconst _1 = require(\".\");\nexports.FIRST_KEY_INDEX = 1;\nexports.IS_READ_ONLY = true;\nfunction transformArguments(key, path, json, start, stop) {\n  const args = ['JSON.ARRINDEX', key, path, (0, _1.transformRedisJsonArgument)(json)];\n  if (start !== undefined && start !== null) {\n    args.push(start.toString());\n    if (stop !== undefined && stop !== null) {\n      args.push(stop.toString());\n    }\n  }\n  return args;\n}\nexports.transformArguments = transformArguments;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "transformArguments", "IS_READ_ONLY", "FIRST_KEY_INDEX", "_1", "require", "key", "path", "json", "start", "stop", "args", "transformRedisJsonArgument", "undefined", "push", "toString"], "sources": ["C:/tmsft/node_modules/@redis/json/dist/commands/ARRINDEX.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.transformArguments = exports.IS_READ_ONLY = exports.FIRST_KEY_INDEX = void 0;\nconst _1 = require(\".\");\nexports.FIRST_KEY_INDEX = 1;\nexports.IS_READ_ONLY = true;\nfunction transformArguments(key, path, json, start, stop) {\n    const args = ['JSON.ARRINDEX', key, path, (0, _1.transformRedisJsonArgument)(json)];\n    if (start !== undefined && start !== null) {\n        args.push(start.toString());\n        if (stop !== undefined && stop !== null) {\n            args.push(stop.toString());\n        }\n    }\n    return args;\n}\nexports.transformArguments = transformArguments;\n"], "mappings": "AAAA,YAAY;;AACZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEC,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7DD,OAAO,CAACE,kBAAkB,GAAGF,OAAO,CAACG,YAAY,GAAGH,OAAO,CAACI,eAAe,GAAG,KAAK,CAAC;AACpF,MAAMC,EAAE,GAAGC,OAAO,CAAC,GAAG,CAAC;AACvBN,OAAO,CAACI,eAAe,GAAG,CAAC;AAC3BJ,OAAO,CAACG,YAAY,GAAG,IAAI;AAC3B,SAASD,kBAAkBA,CAACK,GAAG,EAAEC,IAAI,EAAEC,IAAI,EAAEC,KAAK,EAAEC,IAAI,EAAE;EACtD,MAAMC,IAAI,GAAG,CAAC,eAAe,EAAEL,GAAG,EAAEC,IAAI,EAAE,CAAC,CAAC,EAAEH,EAAE,CAACQ,0BAA0B,EAAEJ,IAAI,CAAC,CAAC;EACnF,IAAIC,KAAK,KAAKI,SAAS,IAAIJ,KAAK,KAAK,IAAI,EAAE;IACvCE,IAAI,CAACG,IAAI,CAACL,KAAK,CAACM,QAAQ,CAAC,CAAC,CAAC;IAC3B,IAAIL,IAAI,KAAKG,SAAS,IAAIH,IAAI,KAAK,IAAI,EAAE;MACrCC,IAAI,CAACG,IAAI,CAACJ,IAAI,CAACK,QAAQ,CAAC,CAAC,CAAC;IAC9B;EACJ;EACA,OAAOJ,IAAI;AACf;AACAZ,OAAO,CAACE,kBAAkB,GAAGA,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}