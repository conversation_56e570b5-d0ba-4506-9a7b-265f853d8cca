{"ast": null, "code": "/**\n * @license\n * Copyright 2018 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { ENGINE } from '../engine';\nimport { ScatterNd } from '../kernel_names';\nimport { convertToTensor } from '../tensor_util_env';\nimport { assertNonNegativeIntegerDimensions } from '../util_base';\nimport { op } from './operation';\nimport * as scatter_nd_util from './scatter_nd_util';\n/**\n * Creates a new tensor by applying sparse updates to individual\n * values or slices within a zero tensor of the given shape tensor according to\n * indices. This operator is the inverse of the `tf.gatherND` operator which\n * extracts values or slices from a given tensor.\n *\n * ```js\n * const indices = tf.tensor2d([4, 3, 1, 7], [4, 1], 'int32');\n * const updates = tf.tensor1d([9, 10, 11, 12]);\n * const shape = [8];\n * tf.scatterND(indices, updates, shape).print() //[0, 11, 0, 10, 9, 0, 0, 12]\n * ```\n *\n * @param indices The tensor contains the indices into the output tensor.\n * @param updates The tensor contains the value for the indices.\n * @param shape: The shape of the output tensor.\n *\n * @doc {heading: 'Operations', subheading: 'Slicing and Joining'}\n */\nfunction scatterND_(indices, updates, shape) {\n  assertNonNegativeIntegerDimensions(shape);\n  const $indices = convertToTensor(indices, 'indices', 'scatterND', 'int32');\n  const $updates = convertToTensor(updates, 'updates', 'scatterND');\n  scatter_nd_util.validateInput($updates, $indices, shape);\n  const inputs = {\n    indices: $indices,\n    updates: $updates\n  };\n  const attrs = {\n    shape\n  };\n  // tslint:disable-next-line: no-unnecessary-type-assertion\n  return ENGINE.runKernel(ScatterNd, inputs, attrs);\n}\nexport const scatterND = /* @__PURE__ */op({\n  scatterND_\n});", "map": {"version": 3, "names": ["ENGINE", "ScatterNd", "convertToTensor", "assertNonNegativeIntegerDimensions", "op", "scatter_nd_util", "scatterND_", "indices", "updates", "shape", "$indices", "$updates", "validateInput", "inputs", "attrs", "runKernel", "scatterND"], "sources": ["C:\\tfjs-core\\src\\ops\\scatter_nd.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2018 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {ENGINE} from '../engine';\nimport {ScatterNd, ScatterNdAttrs, ScatterNdInputs} from '../kernel_names';\nimport {NamedAttrMap} from '../kernel_registry';\nimport {Tensor} from '../tensor';\nimport {NamedTensorMap} from '../tensor_types';\nimport {convertToTensor} from '../tensor_util_env';\nimport {Rank, ShapeMap, TensorLike} from '../types';\nimport {assertNonNegativeIntegerDimensions} from '../util_base';\n\nimport {op} from './operation';\nimport * as scatter_nd_util from './scatter_nd_util';\n\n/**\n * Creates a new tensor by applying sparse updates to individual\n * values or slices within a zero tensor of the given shape tensor according to\n * indices. This operator is the inverse of the `tf.gatherND` operator which\n * extracts values or slices from a given tensor.\n *\n * ```js\n * const indices = tf.tensor2d([4, 3, 1, 7], [4, 1], 'int32');\n * const updates = tf.tensor1d([9, 10, 11, 12]);\n * const shape = [8];\n * tf.scatterND(indices, updates, shape).print() //[0, 11, 0, 10, 9, 0, 0, 12]\n * ```\n *\n * @param indices The tensor contains the indices into the output tensor.\n * @param updates The tensor contains the value for the indices.\n * @param shape: The shape of the output tensor.\n *\n * @doc {heading: 'Operations', subheading: 'Slicing and Joining'}\n */\nfunction scatterND_<R extends Rank>(\n    indices: Tensor|TensorLike, updates: Tensor|TensorLike,\n    shape: ShapeMap[R]): Tensor<R> {\n  assertNonNegativeIntegerDimensions(shape);\n  const $indices = convertToTensor(indices, 'indices', 'scatterND', 'int32');\n  const $updates = convertToTensor(updates, 'updates', 'scatterND');\n  scatter_nd_util.validateInput($updates, $indices, shape);\n\n  const inputs: ScatterNdInputs = {indices: $indices, updates: $updates};\n  const attrs: ScatterNdAttrs = {shape};\n\n  // tslint:disable-next-line: no-unnecessary-type-assertion\n  return ENGINE.runKernel(\n             ScatterNd, inputs as unknown as NamedTensorMap,\n             attrs as unknown as NamedAttrMap) as Tensor<R>;\n}\n\nexport const scatterND = /* @__PURE__ */ op({scatterND_});\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAiBA,SAAQA,MAAM,QAAO,WAAW;AAChC,SAAQC,SAAS,QAAwC,iBAAiB;AAI1E,SAAQC,eAAe,QAAO,oBAAoB;AAElD,SAAQC,kCAAkC,QAAO,cAAc;AAE/D,SAAQC,EAAE,QAAO,aAAa;AAC9B,OAAO,KAAKC,eAAe,MAAM,mBAAmB;AAEpD;;;;;;;;;;;;;;;;;;;AAmBA,SAASC,UAAUA,CACfC,OAA0B,EAAEC,OAA0B,EACtDC,KAAkB;EACpBN,kCAAkC,CAACM,KAAK,CAAC;EACzC,MAAMC,QAAQ,GAAGR,eAAe,CAACK,OAAO,EAAE,SAAS,EAAE,WAAW,EAAE,OAAO,CAAC;EAC1E,MAAMI,QAAQ,GAAGT,eAAe,CAACM,OAAO,EAAE,SAAS,EAAE,WAAW,CAAC;EACjEH,eAAe,CAACO,aAAa,CAACD,QAAQ,EAAED,QAAQ,EAAED,KAAK,CAAC;EAExD,MAAMI,MAAM,GAAoB;IAACN,OAAO,EAAEG,QAAQ;IAAEF,OAAO,EAAEG;EAAQ,CAAC;EACtE,MAAMG,KAAK,GAAmB;IAACL;EAAK,CAAC;EAErC;EACA,OAAOT,MAAM,CAACe,SAAS,CACZd,SAAS,EAAEY,MAAmC,EAC9CC,KAAgC,CAAc;AAC3D;AAEA,OAAO,MAAME,SAAS,GAAG,eAAgBZ,EAAE,CAAC;EAACE;AAAU,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}