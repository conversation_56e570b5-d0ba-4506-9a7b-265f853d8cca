{"ast": null, "code": "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { convertToTensor } from '../tensor_util_env';\nimport { assertAndGetBroadcastShape } from './broadcast_util';\nimport { logicalAnd } from './logical_and';\nimport { logicalNot } from './logical_not';\nimport { logicalOr } from './logical_or';\nimport { op } from './operation';\n/**\n * Returns the truth value of `a XOR b` element-wise. Supports broadcasting.\n *\n * ```js\n * const a = tf.tensor1d([false, false, true, true], 'bool');\n * const b = tf.tensor1d([false, true, false, true], 'bool');\n *\n * a.logicalXor(b).print();\n * ```\n *\n * @param a The first input tensor. Must be of dtype bool.\n * @param b The second input tensor. Must be of dtype bool.\n *\n * @doc {heading: 'Operations', subheading: 'Logical'}\n */\nfunction logicalXor_(a, b) {\n  const $a = convertToTensor(a, 'a', 'logicalXor', 'bool');\n  const $b = convertToTensor(b, 'b', 'logicalXor', 'bool');\n  assertAndGetBroadcastShape($a.shape, $b.shape);\n  // x ^ y = (x | y) & ~(x & y)\n  return logicalAnd(logicalOr(a, b), logicalNot(logicalAnd(a, b)));\n}\nexport const logicalXor = /* @__PURE__ */op({\n  logicalXor_\n});", "map": {"version": 3, "names": ["convertToTensor", "assertAndGetBroadcastShape", "logicalAnd", "logicalNot", "logicalOr", "op", "logicalXor_", "a", "b", "$a", "$b", "shape", "logicalXor"], "sources": ["C:\\tfjs-core\\src\\ops\\logical_xor.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {Tensor} from '../tensor';\nimport {convertToTensor} from '../tensor_util_env';\nimport {TensorLike} from '../types';\n\nimport {assertAndGetBroadcastShape} from './broadcast_util';\nimport {logicalAnd} from './logical_and';\nimport {logicalNot} from './logical_not';\nimport {logicalOr} from './logical_or';\nimport {op} from './operation';\n\n/**\n * Returns the truth value of `a XOR b` element-wise. Supports broadcasting.\n *\n * ```js\n * const a = tf.tensor1d([false, false, true, true], 'bool');\n * const b = tf.tensor1d([false, true, false, true], 'bool');\n *\n * a.logicalXor(b).print();\n * ```\n *\n * @param a The first input tensor. Must be of dtype bool.\n * @param b The second input tensor. Must be of dtype bool.\n *\n * @doc {heading: 'Operations', subheading: 'Logical'}\n */\nfunction logicalXor_<T extends Tensor>(\n    a: Tensor|TensorLike, b: Tensor|TensorLike): T {\n  const $a = convertToTensor(a, 'a', 'logicalXor', 'bool');\n  const $b = convertToTensor(b, 'b', 'logicalXor', 'bool');\n  assertAndGetBroadcastShape($a.shape, $b.shape);\n\n  // x ^ y = (x | y) & ~(x & y)\n  return logicalAnd(logicalOr(a, b), logicalNot(logicalAnd(a, b)));\n}\n\nexport const logicalXor = /* @__PURE__ */ op({logicalXor_});\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAkBA,SAAQA,eAAe,QAAO,oBAAoB;AAGlD,SAAQC,0BAA0B,QAAO,kBAAkB;AAC3D,SAAQC,UAAU,QAAO,eAAe;AACxC,SAAQC,UAAU,QAAO,eAAe;AACxC,SAAQC,SAAS,QAAO,cAAc;AACtC,SAAQC,EAAE,QAAO,aAAa;AAE9B;;;;;;;;;;;;;;;AAeA,SAASC,WAAWA,CAChBC,CAAoB,EAAEC,CAAoB;EAC5C,MAAMC,EAAE,GAAGT,eAAe,CAACO,CAAC,EAAE,GAAG,EAAE,YAAY,EAAE,MAAM,CAAC;EACxD,MAAMG,EAAE,GAAGV,eAAe,CAACQ,CAAC,EAAE,GAAG,EAAE,YAAY,EAAE,MAAM,CAAC;EACxDP,0BAA0B,CAACQ,EAAE,CAACE,KAAK,EAAED,EAAE,CAACC,KAAK,CAAC;EAE9C;EACA,OAAOT,UAAU,CAACE,SAAS,CAACG,CAAC,EAAEC,CAAC,CAAC,EAAEL,UAAU,CAACD,UAAU,CAACK,CAAC,EAAEC,CAAC,CAAC,CAAC,CAAC;AAClE;AAEA,OAAO,MAAMI,UAAU,GAAG,eAAgBP,EAAE,CAAC;EAACC;AAAW,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}