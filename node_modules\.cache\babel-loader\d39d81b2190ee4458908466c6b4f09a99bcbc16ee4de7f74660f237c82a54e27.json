{"ast": null, "code": "/**\n * @license\n * Copyright 2021 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n/**\n * Generates sparse fill empty rows indices, dense shape mismatch error message.\n *\n * @param indicesLength The first dimension of indices.\n */\nexport function getSparseFillEmptyRowsIndicesDenseShapeMismatch(indicesLength) {\n  return \"Received SparseTensor with denseShape[0] = 0 but\\n  indices.shape[0] = \".concat(indicesLength);\n}\n/**\n * Generates sparse fill empty rows negative index error message.\n *\n * @param index The index with a negative value.\n * @param value The negative value.\n */\nexport function getSparseFillEmptyRowsNegativeIndexErrorMessage(index, value) {\n  return \"indices(\".concat(index, \", 0) is invalid: \").concat(value, \" < 0\");\n}\n/**\n * Generates sparse fill empty rows out of range index error message.\n *\n * @param index The index with an out of range value.\n * @param value The out of range value.\n * @param limit The upper limit for indices.\n */\nexport function getSparseFillEmptyRowsOutOfRangeIndexErrorMessage(index, value, limit) {\n  return \"indices(\".concat(index, \", 0) is invalid: \").concat(value, \" >= \").concat(limit);\n}", "map": {"version": 3, "names": ["getSparseFillEmptyRowsIndicesDenseShapeMismatch", "indicesLength", "concat", "getSparseFillEmptyRowsNegativeIndexErrorMessage", "index", "value", "getSparseFillEmptyRowsOutOfRangeIndexErrorMessage", "limit"], "sources": ["C:\\tfjs-core\\src\\ops\\sparse\\sparse_fill_empty_rows_util.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2021 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\n/**\n * Generates sparse fill empty rows indices, dense shape mismatch error message.\n *\n * @param indicesLength The first dimension of indices.\n */\nexport function getSparseFillEmptyRowsIndicesDenseShapeMismatch(\n    indicesLength: number) {\n  return `Received SparseTensor with denseShape[0] = 0 but\n  indices.shape[0] = ${indicesLength}`;\n}\n\n/**\n * Generates sparse fill empty rows negative index error message.\n *\n * @param index The index with a negative value.\n * @param value The negative value.\n */\nexport function getSparseFillEmptyRowsNegativeIndexErrorMessage(\n    index: number, value: number) {\n  return `indices(${index}, 0) is invalid: ${value} < 0`;\n}\n\n/**\n * Generates sparse fill empty rows out of range index error message.\n *\n * @param index The index with an out of range value.\n * @param value The out of range value.\n * @param limit The upper limit for indices.\n */\nexport function getSparseFillEmptyRowsOutOfRangeIndexErrorMessage(\n    index: number, value: number, limit: number) {\n  return `indices(${index}, 0) is invalid: ${value} >= ${limit}`;\n}\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAiBA;;;;;AAKA,OAAM,SAAUA,+CAA+CA,CAC3DC,aAAqB;EACvB,iFAAAC,MAAA,CACqBD,aAAa;AACpC;AAEA;;;;;;AAMA,OAAM,SAAUE,+CAA+CA,CAC3DC,KAAa,EAAEC,KAAa;EAC9B,kBAAAH,MAAA,CAAkBE,KAAK,uBAAAF,MAAA,CAAoBG,KAAK;AAClD;AAEA;;;;;;;AAOA,OAAM,SAAUC,iDAAiDA,CAC7DF,KAAa,EAAEC,KAAa,EAAEE,KAAa;EAC7C,kBAAAL,MAAA,CAAkBE,KAAK,uBAAAF,MAAA,CAAoBG,KAAK,UAAAH,MAAA,CAAOK,KAAK;AAC9D", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}