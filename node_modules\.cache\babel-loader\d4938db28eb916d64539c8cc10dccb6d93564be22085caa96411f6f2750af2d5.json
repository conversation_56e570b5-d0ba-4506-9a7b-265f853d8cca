{"ast": null, "code": "var _jsxFileName = \"C:\\\\tmsft\\\\src\\\\components\\\\DataHub.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { BankStatementImport } from './BankStatementImport';\nimport { BankAccountManager } from './BankAccountManager';\nimport { Transactions } from './Transactions';\nimport { FileManager } from './FileManager';\nimport './DataHub.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport const DataHub = ({\n  onTransactionImport\n}) => {\n  _s();\n  const [activeTab, setActiveTab] = useState('bankStatement');\n  const [transactionRefreshKey, setTransactionRefreshKey] = useState(0);\n  const handleImportComplete = (transactions, bankAccount) => {\n    console.log(`Imported ${transactions.length} transactions for ${bankAccount.name}`);\n    if (onTransactionImport) {\n      onTransactionImport(transactions, bankAccount);\n    }\n    // Refresh transactions when new data is imported\n    setTransactionRefreshKey(prev => prev + 1);\n  };\n  const handleFileDeleted = fileId => {\n    console.log(`File deleted: ${fileId}`);\n    // Refresh transactions when a file is deleted\n    setTransactionRefreshKey(prev => prev + 1);\n  };\n  const tabs = [{\n    id: 'bankStatement',\n    label: 'Bank Statements',\n    icon: /*#__PURE__*/_jsxDEV(\"svg\", {\n      width: \"20\",\n      height: \"20\",\n      viewBox: \"0 0 24 24\",\n      fill: \"none\",\n      stroke: \"currentColor\",\n      strokeWidth: \"2\",\n      children: [/*#__PURE__*/_jsxDEV(\"rect\", {\n        x: \"2\",\n        y: \"3\",\n        width: \"20\",\n        height: \"14\",\n        rx: \"2\",\n        ry: \"2\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 38,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"line\", {\n        x1: \"8\",\n        y1: \"21\",\n        x2: \"16\",\n        y2: \"21\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 39,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"line\", {\n        x1: \"12\",\n        y1: \"17\",\n        x2: \"12\",\n        y2: \"21\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 40,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 37,\n      columnNumber: 9\n    }, this),\n    description: 'Import and process bank statement CSV files'\n  }, {\n    id: 'accounts',\n    label: 'Bank Accounts',\n    icon: /*#__PURE__*/_jsxDEV(\"svg\", {\n      width: \"20\",\n      height: \"20\",\n      viewBox: \"0 0 24 24\",\n      fill: \"none\",\n      stroke: \"currentColor\",\n      strokeWidth: \"2\",\n      children: [/*#__PURE__*/_jsxDEV(\"rect\", {\n        x: \"2\",\n        y: \"3\",\n        width: \"20\",\n        height: \"14\",\n        rx: \"2\",\n        ry: \"2\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 50,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"line\", {\n        x1: \"2\",\n        y1: \"12\",\n        x2: \"22\",\n        y2: \"12\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 51,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 49,\n      columnNumber: 9\n    }, this),\n    description: 'Manage and configure your bank accounts'\n  }, {\n    id: 'transactions',\n    label: 'Transactions',\n    icon: /*#__PURE__*/_jsxDEV(\"svg\", {\n      width: \"20\",\n      height: \"20\",\n      viewBox: \"0 0 24 24\",\n      fill: \"none\",\n      stroke: \"currentColor\",\n      strokeWidth: \"2\",\n      children: [/*#__PURE__*/_jsxDEV(\"path\", {\n        d: \"M3 6h18\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 61,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n        d: \"M3 12h18\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 62,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n        d: \"M3 18h18\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 63,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"circle\", {\n        cx: \"6\",\n        cy: \"6\",\n        r: \"1\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 64,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"circle\", {\n        cx: \"6\",\n        cy: \"12\",\n        r: \"1\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 65,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"circle\", {\n        cx: \"6\",\n        cy: \"18\",\n        r: \"1\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 66,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 60,\n      columnNumber: 9\n    }, this),\n    description: 'View and manage all imported transactions'\n  }, {\n    id: 'fileManager',\n    label: 'File Management',\n    icon: /*#__PURE__*/_jsxDEV(\"svg\", {\n      width: \"20\",\n      height: \"20\",\n      viewBox: \"0 0 24 24\",\n      fill: \"none\",\n      stroke: \"currentColor\",\n      strokeWidth: \"2\",\n      children: [/*#__PURE__*/_jsxDEV(\"path\", {\n        d: \"M13 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V9z\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 76,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"polyline\", {\n        points: \"13,2 13,9 20,9\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 77,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n        d: \"M8 13h8\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 78,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n        d: \"M8 17h8\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 79,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 75,\n      columnNumber: 9\n    }, this),\n    description: 'Manage uploaded CSV files and delete file records'\n  }, {\n    id: 'payroll',\n    label: 'Payroll Data',\n    icon: /*#__PURE__*/_jsxDEV(\"svg\", {\n      width: \"20\",\n      height: \"20\",\n      viewBox: \"0 0 24 24\",\n      fill: \"none\",\n      stroke: \"currentColor\",\n      strokeWidth: \"2\",\n      children: [/*#__PURE__*/_jsxDEV(\"path\", {\n        d: \"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 89,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"circle\", {\n        cx: \"9\",\n        cy: \"7\",\n        r: \"4\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 90,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n        d: \"M22 21v-2a4 4 0 0 0-3-3.87\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 91,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n        d: \"M16 3.13a4 4 0 0 1 0 7.75\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 92,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 88,\n      columnNumber: 9\n    }, this),\n    description: 'Process payroll and employee compensation data'\n  }, {\n    id: 'investments',\n    label: 'Investment Data',\n    icon: /*#__PURE__*/_jsxDEV(\"svg\", {\n      width: \"20\",\n      height: \"20\",\n      viewBox: \"0 0 24 24\",\n      fill: \"none\",\n      stroke: \"currentColor\",\n      strokeWidth: \"2\",\n      children: /*#__PURE__*/_jsxDEV(\"polyline\", {\n        points: \"22,12 18,12 15,21 9,3 6,12 2,12\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 102,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 101,\n      columnNumber: 9\n    }, this),\n    description: 'Import investment portfolio and market data'\n  }, {\n    id: 'reports',\n    label: 'Financial Reports',\n    icon: /*#__PURE__*/_jsxDEV(\"svg\", {\n      width: \"20\",\n      height: \"20\",\n      viewBox: \"0 0 24 24\",\n      fill: \"none\",\n      stroke: \"currentColor\",\n      strokeWidth: \"2\",\n      children: [/*#__PURE__*/_jsxDEV(\"path\", {\n        d: \"M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 112,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"polyline\", {\n        points: \"14,2 14,8 20,8\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 113,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"line\", {\n        x1: \"16\",\n        y1: \"13\",\n        x2: \"8\",\n        y2: \"13\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 114,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"line\", {\n        x1: \"16\",\n        y1: \"17\",\n        x2: \"8\",\n        y2: \"17\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 115,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"polyline\", {\n        points: \"10,9 9,9 8,9\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 116,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 111,\n      columnNumber: 9\n    }, this),\n    description: 'Generate and export financial reports'\n  }];\n  const renderTabContent = () => {\n    switch (activeTab) {\n      case 'bankStatement':\n        return /*#__PURE__*/_jsxDEV(BankStatementImport, {\n          onImportComplete: handleImportComplete\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 126,\n          columnNumber: 16\n        }, this);\n      case 'accounts':\n        return /*#__PURE__*/_jsxDEV(BankAccountManager, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 128,\n          columnNumber: 16\n        }, this);\n      case 'transactions':\n        return /*#__PURE__*/_jsxDEV(Transactions, {\n          refreshTrigger: transactionRefreshKey\n        }, transactionRefreshKey, false, {\n          fileName: _jsxFileName,\n          lineNumber: 130,\n          columnNumber: 16\n        }, this);\n      case 'fileManager':\n        return /*#__PURE__*/_jsxDEV(FileManager, {\n          onFileDeleted: handleFileDeleted\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 132,\n          columnNumber: 16\n        }, this);\n      case 'payroll':\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"tab-placeholder\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"placeholder-icon\",\n            children: /*#__PURE__*/_jsxDEV(\"svg\", {\n              width: \"64\",\n              height: \"64\",\n              viewBox: \"0 0 24 24\",\n              fill: \"none\",\n              stroke: \"currentColor\",\n              strokeWidth: \"1\",\n              children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                d: \"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 138,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"circle\", {\n                cx: \"9\",\n                cy: \"7\",\n                r: \"4\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 139,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                d: \"M22 21v-2a4 4 0 0 0-3-3.87\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 140,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                d: \"M16 3.13a4 4 0 0 1 0 7.75\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 141,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 137,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 136,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"Payroll Data Import\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 144,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"This section will allow you to import and process payroll data, employee compensation records, and related HR financial information.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 145,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"placeholder-features\",\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              children: \"Coming Soon:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 147,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n              children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"Employee salary and wage data import\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 149,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"Benefits and deductions processing\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 150,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"Tax withholding calculations\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 151,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"Payroll period reconciliation\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 152,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 148,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 146,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 135,\n          columnNumber: 11\n        }, this);\n      case 'investments':\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"tab-placeholder\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"placeholder-icon\",\n            children: /*#__PURE__*/_jsxDEV(\"svg\", {\n              width: \"64\",\n              height: \"64\",\n              viewBox: \"0 0 24 24\",\n              fill: \"none\",\n              stroke: \"currentColor\",\n              strokeWidth: \"1\",\n              children: /*#__PURE__*/_jsxDEV(\"polyline\", {\n                points: \"22,12 18,12 15,21 9,3 6,12 2,12\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 162,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 161,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 160,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"Investment Data Import\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 165,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"Manage your investment portfolio data, market valuations, and performance tracking.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 166,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"placeholder-features\",\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              children: \"Coming Soon:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 168,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n              children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"Portfolio holdings import\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 170,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"Market price updates\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 171,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"Performance analytics\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 172,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"Asset allocation tracking\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 173,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 169,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 167,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 159,\n          columnNumber: 11\n        }, this);\n      case 'reports':\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"tab-placeholder\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"placeholder-icon\",\n            children: /*#__PURE__*/_jsxDEV(\"svg\", {\n              width: \"64\",\n              height: \"64\",\n              viewBox: \"0 0 24 24\",\n              fill: \"none\",\n              stroke: \"currentColor\",\n              strokeWidth: \"1\",\n              children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                d: \"M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 183,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"polyline\", {\n                points: \"14,2 14,8 20,8\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 184,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"line\", {\n                x1: \"16\",\n                y1: \"13\",\n                x2: \"8\",\n                y2: \"13\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 185,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"line\", {\n                x1: \"16\",\n                y1: \"17\",\n                x2: \"8\",\n                y2: \"17\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 186,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"polyline\", {\n                points: \"10,9 9,9 8,9\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 187,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 182,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 181,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"Financial Reports\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 190,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"Generate comprehensive financial reports and export data for analysis and compliance.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 191,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"placeholder-features\",\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              children: \"Coming Soon:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 193,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n              children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"Balance sheet generation\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 195,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"Income statement reports\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 196,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"Cash flow analysis\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 197,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"Custom report builder\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 198,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 194,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 192,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 180,\n          columnNumber: 11\n        }, this);\n      default:\n        return null;\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"datahub\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"datahub-header\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"datahub-title\",\n          children: \"DataHub\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 212,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"datahub-description\",\n          children: \"Central hub for importing, processing, and managing all your financial data\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 213,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 211,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 210,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"datahub-content\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"datahub-tabs\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"tab-list\",\n            children: tabs.map(tab => /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setActiveTab(tab.id),\n              className: `tab-button ${activeTab === tab.id ? 'active' : ''}`,\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"tab-icon\",\n                children: tab.icon\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 229,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"tab-content\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"tab-label\",\n                  children: tab.label\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 231,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"tab-description\",\n                  children: tab.description\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 232,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 230,\n                columnNumber: 19\n              }, this)]\n            }, tab.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 224,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 222,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 221,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"tab-panel\",\n          children: renderTabContent()\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 239,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 220,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 219,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 209,\n    columnNumber: 5\n  }, this);\n};\n_s(DataHub, \"sO5B+qtnuO+LdDOWQeXRwWA/uAg=\");\n_c = DataHub;\nvar _c;\n$RefreshReg$(_c, \"DataHub\");", "map": {"version": 3, "names": ["React", "useState", "BankStatementImport", "BankAccountManager", "Transactions", "FileManager", "jsxDEV", "_jsxDEV", "DataHub", "onTransactionImport", "_s", "activeTab", "setActiveTab", "transactionRefreshKey", "setTransactionRefreshKey", "handleImportComplete", "transactions", "bankAccount", "console", "log", "length", "name", "prev", "handleFileDeleted", "fileId", "tabs", "id", "label", "icon", "width", "height", "viewBox", "fill", "stroke", "strokeWidth", "children", "x", "y", "rx", "ry", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "x1", "y1", "x2", "y2", "description", "d", "cx", "cy", "r", "points", "renderTabContent", "onImportComplete", "refreshTrigger", "onFileDeleted", "className", "map", "tab", "onClick", "_c", "$RefreshReg$"], "sources": ["C:/tmsft/src/components/DataHub.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { BankStatementImport } from './BankStatementImport';\nimport { BankAccountManager } from './BankAccountManager';\nimport { Transactions } from './Transactions';\nimport { FileManager } from './FileManager';\nimport { Transaction, BankAccount } from '../types';\nimport './DataHub.css';\n\ninterface DataHubProps {\n  onTransactionImport?: (transactions: Transaction[], bankAccount: BankAccount) => void;\n}\n\nexport const DataHub: React.FC<DataHubProps> = ({ onTransactionImport }) => {\n  const [activeTab, setActiveTab] = useState<'bankStatement' | 'accounts' | 'transactions' | 'fileManager' | 'payroll' | 'investments' | 'reports'>('bankStatement');\n  const [transactionRefreshKey, setTransactionRefreshKey] = useState(0);\n\n  const handleImportComplete = (transactions: Transaction[], bankAccount: BankAccount) => {\n    console.log(`Imported ${transactions.length} transactions for ${bankAccount.name}`);\n    if (onTransactionImport) {\n      onTransactionImport(transactions, bankAccount);\n    }\n    // Refresh transactions when new data is imported\n    setTransactionRefreshKey(prev => prev + 1);\n  };\n\n  const handleFileDeleted = (fileId: string) => {\n    console.log(`File deleted: ${fileId}`);\n    // Refresh transactions when a file is deleted\n    setTransactionRefreshKey(prev => prev + 1);\n  };\n\n  const tabs = [\n    {\n      id: 'bankStatement' as const,\n      label: 'Bank Statements',\n      icon: (\n        <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\">\n          <rect x=\"2\" y=\"3\" width=\"20\" height=\"14\" rx=\"2\" ry=\"2\" />\n          <line x1=\"8\" y1=\"21\" x2=\"16\" y2=\"21\" />\n          <line x1=\"12\" y1=\"17\" x2=\"12\" y2=\"21\" />\n        </svg>\n      ),\n      description: 'Import and process bank statement CSV files'\n    },\n    {\n      id: 'accounts' as const,\n      label: 'Bank Accounts',\n      icon: (\n        <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\">\n          <rect x=\"2\" y=\"3\" width=\"20\" height=\"14\" rx=\"2\" ry=\"2\" />\n          <line x1=\"2\" y1=\"12\" x2=\"22\" y2=\"12\" />\n        </svg>\n      ),\n      description: 'Manage and configure your bank accounts'\n    },\n    {\n      id: 'transactions' as const,\n      label: 'Transactions',\n      icon: (\n        <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\">\n          <path d=\"M3 6h18\" />\n          <path d=\"M3 12h18\" />\n          <path d=\"M3 18h18\" />\n          <circle cx=\"6\" cy=\"6\" r=\"1\" />\n          <circle cx=\"6\" cy=\"12\" r=\"1\" />\n          <circle cx=\"6\" cy=\"18\" r=\"1\" />\n        </svg>\n      ),\n      description: 'View and manage all imported transactions'\n    },\n    {\n      id: 'fileManager' as const,\n      label: 'File Management',\n      icon: (\n        <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\">\n          <path d=\"M13 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V9z\" />\n          <polyline points=\"13,2 13,9 20,9\" />\n          <path d=\"M8 13h8\" />\n          <path d=\"M8 17h8\" />\n        </svg>\n      ),\n      description: 'Manage uploaded CSV files and delete file records'\n    },\n    {\n      id: 'payroll' as const,\n      label: 'Payroll Data',\n      icon: (\n        <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\">\n          <path d=\"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2\" />\n          <circle cx=\"9\" cy=\"7\" r=\"4\" />\n          <path d=\"M22 21v-2a4 4 0 0 0-3-3.87\" />\n          <path d=\"M16 3.13a4 4 0 0 1 0 7.75\" />\n        </svg>\n      ),\n      description: 'Process payroll and employee compensation data'\n    },\n    {\n      id: 'investments' as const,\n      label: 'Investment Data',\n      icon: (\n        <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\">\n          <polyline points=\"22,12 18,12 15,21 9,3 6,12 2,12\" />\n        </svg>\n      ),\n      description: 'Import investment portfolio and market data'\n    },\n    {\n      id: 'reports' as const,\n      label: 'Financial Reports',\n      icon: (\n        <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\">\n          <path d=\"M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z\" />\n          <polyline points=\"14,2 14,8 20,8\" />\n          <line x1=\"16\" y1=\"13\" x2=\"8\" y2=\"13\" />\n          <line x1=\"16\" y1=\"17\" x2=\"8\" y2=\"17\" />\n          <polyline points=\"10,9 9,9 8,9\" />\n        </svg>\n      ),\n      description: 'Generate and export financial reports'\n    }\n  ];\n\n  const renderTabContent = () => {\n    switch (activeTab) {\n      case 'bankStatement':\n        return <BankStatementImport onImportComplete={handleImportComplete} />;\n      case 'accounts':\n        return <BankAccountManager />;\n      case 'transactions':\n        return <Transactions key={transactionRefreshKey} refreshTrigger={transactionRefreshKey} />;\n      case 'fileManager':\n        return <FileManager onFileDeleted={handleFileDeleted} />;\n      case 'payroll':\n        return (\n          <div className=\"tab-placeholder\">\n            <div className=\"placeholder-icon\">\n              <svg width=\"64\" height=\"64\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"1\">\n                <path d=\"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2\" />\n                <circle cx=\"9\" cy=\"7\" r=\"4\" />\n                <path d=\"M22 21v-2a4 4 0 0 0-3-3.87\" />\n                <path d=\"M16 3.13a4 4 0 0 1 0 7.75\" />\n              </svg>\n            </div>\n            <h3>Payroll Data Import</h3>\n            <p>This section will allow you to import and process payroll data, employee compensation records, and related HR financial information.</p>\n            <div className=\"placeholder-features\">\n              <h4>Coming Soon:</h4>\n              <ul>\n                <li>Employee salary and wage data import</li>\n                <li>Benefits and deductions processing</li>\n                <li>Tax withholding calculations</li>\n                <li>Payroll period reconciliation</li>\n              </ul>\n            </div>\n          </div>\n        );\n      case 'investments':\n        return (\n          <div className=\"tab-placeholder\">\n            <div className=\"placeholder-icon\">\n              <svg width=\"64\" height=\"64\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"1\">\n                <polyline points=\"22,12 18,12 15,21 9,3 6,12 2,12\" />\n              </svg>\n            </div>\n            <h3>Investment Data Import</h3>\n            <p>Manage your investment portfolio data, market valuations, and performance tracking.</p>\n            <div className=\"placeholder-features\">\n              <h4>Coming Soon:</h4>\n              <ul>\n                <li>Portfolio holdings import</li>\n                <li>Market price updates</li>\n                <li>Performance analytics</li>\n                <li>Asset allocation tracking</li>\n              </ul>\n            </div>\n          </div>\n        );\n      case 'reports':\n        return (\n          <div className=\"tab-placeholder\">\n            <div className=\"placeholder-icon\">\n              <svg width=\"64\" height=\"64\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"1\">\n                <path d=\"M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z\" />\n                <polyline points=\"14,2 14,8 20,8\" />\n                <line x1=\"16\" y1=\"13\" x2=\"8\" y2=\"13\" />\n                <line x1=\"16\" y1=\"17\" x2=\"8\" y2=\"17\" />\n                <polyline points=\"10,9 9,9 8,9\" />\n              </svg>\n            </div>\n            <h3>Financial Reports</h3>\n            <p>Generate comprehensive financial reports and export data for analysis and compliance.</p>\n            <div className=\"placeholder-features\">\n              <h4>Coming Soon:</h4>\n              <ul>\n                <li>Balance sheet generation</li>\n                <li>Income statement reports</li>\n                <li>Cash flow analysis</li>\n                <li>Custom report builder</li>\n              </ul>\n            </div>\n          </div>\n        );\n      default:\n        return null;\n    }\n  };\n\n  return (\n    <div className=\"datahub\">\n      <div className=\"datahub-header\">\n        <div className=\"container\">\n          <h1 className=\"datahub-title\">DataHub</h1>\n          <p className=\"datahub-description\">\n            Central hub for importing, processing, and managing all your financial data\n          </p>\n        </div>\n      </div>\n\n      <div className=\"datahub-content\">\n        <div className=\"container\">\n          <div className=\"datahub-tabs\">\n            <div className=\"tab-list\">\n              {tabs.map((tab) => (\n                <button\n                  key={tab.id}\n                  onClick={() => setActiveTab(tab.id)}\n                  className={`tab-button ${activeTab === tab.id ? 'active' : ''}`}\n                >\n                  <div className=\"tab-icon\">{tab.icon}</div>\n                  <div className=\"tab-content\">\n                    <div className=\"tab-label\">{tab.label}</div>\n                    <div className=\"tab-description\">{tab.description}</div>\n                  </div>\n                </button>\n              ))}\n            </div>\n          </div>\n\n          <div className=\"tab-panel\">\n            {renderTabContent()}\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}; "], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,mBAAmB,QAAQ,uBAAuB;AAC3D,SAASC,kBAAkB,QAAQ,sBAAsB;AACzD,SAASC,YAAY,QAAQ,gBAAgB;AAC7C,SAASC,WAAW,QAAQ,eAAe;AAE3C,OAAO,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAMvB,OAAO,MAAMC,OAA+B,GAAGA,CAAC;EAAEC;AAAoB,CAAC,KAAK;EAAAC,EAAA;EAC1E,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGX,QAAQ,CAAwG,eAAe,CAAC;EAClK,MAAM,CAACY,qBAAqB,EAAEC,wBAAwB,CAAC,GAAGb,QAAQ,CAAC,CAAC,CAAC;EAErE,MAAMc,oBAAoB,GAAGA,CAACC,YAA2B,EAAEC,WAAwB,KAAK;IACtFC,OAAO,CAACC,GAAG,CAAC,YAAYH,YAAY,CAACI,MAAM,qBAAqBH,WAAW,CAACI,IAAI,EAAE,CAAC;IACnF,IAAIZ,mBAAmB,EAAE;MACvBA,mBAAmB,CAACO,YAAY,EAAEC,WAAW,CAAC;IAChD;IACA;IACAH,wBAAwB,CAACQ,IAAI,IAAIA,IAAI,GAAG,CAAC,CAAC;EAC5C,CAAC;EAED,MAAMC,iBAAiB,GAAIC,MAAc,IAAK;IAC5CN,OAAO,CAACC,GAAG,CAAC,iBAAiBK,MAAM,EAAE,CAAC;IACtC;IACAV,wBAAwB,CAACQ,IAAI,IAAIA,IAAI,GAAG,CAAC,CAAC;EAC5C,CAAC;EAED,MAAMG,IAAI,GAAG,CACX;IACEC,EAAE,EAAE,eAAwB;IAC5BC,KAAK,EAAE,iBAAiB;IACxBC,IAAI,eACFrB,OAAA;MAAKsB,KAAK,EAAC,IAAI;MAACC,MAAM,EAAC,IAAI;MAACC,OAAO,EAAC,WAAW;MAACC,IAAI,EAAC,MAAM;MAACC,MAAM,EAAC,cAAc;MAACC,WAAW,EAAC,GAAG;MAAAC,QAAA,gBAC/F5B,OAAA;QAAM6B,CAAC,EAAC,GAAG;QAACC,CAAC,EAAC,GAAG;QAACR,KAAK,EAAC,IAAI;QAACC,MAAM,EAAC,IAAI;QAACQ,EAAE,EAAC,GAAG;QAACC,EAAE,EAAC;MAAG;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACzDpC,OAAA;QAAMqC,EAAE,EAAC,GAAG;QAACC,EAAE,EAAC,IAAI;QAACC,EAAE,EAAC,IAAI;QAACC,EAAE,EAAC;MAAI;QAAAP,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACvCpC,OAAA;QAAMqC,EAAE,EAAC,IAAI;QAACC,EAAE,EAAC,IAAI;QAACC,EAAE,EAAC,IAAI;QAACC,EAAE,EAAC;MAAI;QAAAP,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACrC,CACN;IACDK,WAAW,EAAE;EACf,CAAC,EACD;IACEtB,EAAE,EAAE,UAAmB;IACvBC,KAAK,EAAE,eAAe;IACtBC,IAAI,eACFrB,OAAA;MAAKsB,KAAK,EAAC,IAAI;MAACC,MAAM,EAAC,IAAI;MAACC,OAAO,EAAC,WAAW;MAACC,IAAI,EAAC,MAAM;MAACC,MAAM,EAAC,cAAc;MAACC,WAAW,EAAC,GAAG;MAAAC,QAAA,gBAC/F5B,OAAA;QAAM6B,CAAC,EAAC,GAAG;QAACC,CAAC,EAAC,GAAG;QAACR,KAAK,EAAC,IAAI;QAACC,MAAM,EAAC,IAAI;QAACQ,EAAE,EAAC,GAAG;QAACC,EAAE,EAAC;MAAG;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACzDpC,OAAA;QAAMqC,EAAE,EAAC,GAAG;QAACC,EAAE,EAAC,IAAI;QAACC,EAAE,EAAC,IAAI;QAACC,EAAE,EAAC;MAAI;QAAAP,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACpC,CACN;IACDK,WAAW,EAAE;EACf,CAAC,EACD;IACEtB,EAAE,EAAE,cAAuB;IAC3BC,KAAK,EAAE,cAAc;IACrBC,IAAI,eACFrB,OAAA;MAAKsB,KAAK,EAAC,IAAI;MAACC,MAAM,EAAC,IAAI;MAACC,OAAO,EAAC,WAAW;MAACC,IAAI,EAAC,MAAM;MAACC,MAAM,EAAC,cAAc;MAACC,WAAW,EAAC,GAAG;MAAAC,QAAA,gBAC/F5B,OAAA;QAAM0C,CAAC,EAAC;MAAS;QAAAT,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACpBpC,OAAA;QAAM0C,CAAC,EAAC;MAAU;QAAAT,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACrBpC,OAAA;QAAM0C,CAAC,EAAC;MAAU;QAAAT,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACrBpC,OAAA;QAAQ2C,EAAE,EAAC,GAAG;QAACC,EAAE,EAAC,GAAG;QAACC,CAAC,EAAC;MAAG;QAAAZ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC9BpC,OAAA;QAAQ2C,EAAE,EAAC,GAAG;QAACC,EAAE,EAAC,IAAI;QAACC,CAAC,EAAC;MAAG;QAAAZ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC/BpC,OAAA;QAAQ2C,EAAE,EAAC,GAAG;QAACC,EAAE,EAAC,IAAI;QAACC,CAAC,EAAC;MAAG;QAAAZ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC5B,CACN;IACDK,WAAW,EAAE;EACf,CAAC,EACD;IACEtB,EAAE,EAAE,aAAsB;IAC1BC,KAAK,EAAE,iBAAiB;IACxBC,IAAI,eACFrB,OAAA;MAAKsB,KAAK,EAAC,IAAI;MAACC,MAAM,EAAC,IAAI;MAACC,OAAO,EAAC,WAAW;MAACC,IAAI,EAAC,MAAM;MAACC,MAAM,EAAC,cAAc;MAACC,WAAW,EAAC,GAAG;MAAAC,QAAA,gBAC/F5B,OAAA;QAAM0C,CAAC,EAAC;MAA4D;QAAAT,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACvEpC,OAAA;QAAU8C,MAAM,EAAC;MAAgB;QAAAb,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACpCpC,OAAA;QAAM0C,CAAC,EAAC;MAAS;QAAAT,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACpBpC,OAAA;QAAM0C,CAAC,EAAC;MAAS;QAAAT,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjB,CACN;IACDK,WAAW,EAAE;EACf,CAAC,EACD;IACEtB,EAAE,EAAE,SAAkB;IACtBC,KAAK,EAAE,cAAc;IACrBC,IAAI,eACFrB,OAAA;MAAKsB,KAAK,EAAC,IAAI;MAACC,MAAM,EAAC,IAAI;MAACC,OAAO,EAAC,WAAW;MAACC,IAAI,EAAC,MAAM;MAACC,MAAM,EAAC,cAAc;MAACC,WAAW,EAAC,GAAG;MAAAC,QAAA,gBAC/F5B,OAAA;QAAM0C,CAAC,EAAC;MAA2C;QAAAT,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACtDpC,OAAA;QAAQ2C,EAAE,EAAC,GAAG;QAACC,EAAE,EAAC,GAAG;QAACC,CAAC,EAAC;MAAG;QAAAZ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC9BpC,OAAA;QAAM0C,CAAC,EAAC;MAA4B;QAAAT,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACvCpC,OAAA;QAAM0C,CAAC,EAAC;MAA2B;QAAAT,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACnC,CACN;IACDK,WAAW,EAAE;EACf,CAAC,EACD;IACEtB,EAAE,EAAE,aAAsB;IAC1BC,KAAK,EAAE,iBAAiB;IACxBC,IAAI,eACFrB,OAAA;MAAKsB,KAAK,EAAC,IAAI;MAACC,MAAM,EAAC,IAAI;MAACC,OAAO,EAAC,WAAW;MAACC,IAAI,EAAC,MAAM;MAACC,MAAM,EAAC,cAAc;MAACC,WAAW,EAAC,GAAG;MAAAC,QAAA,eAC/F5B,OAAA;QAAU8C,MAAM,EAAC;MAAiC;QAAAb,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAClD,CACN;IACDK,WAAW,EAAE;EACf,CAAC,EACD;IACEtB,EAAE,EAAE,SAAkB;IACtBC,KAAK,EAAE,mBAAmB;IAC1BC,IAAI,eACFrB,OAAA;MAAKsB,KAAK,EAAC,IAAI;MAACC,MAAM,EAAC,IAAI;MAACC,OAAO,EAAC,WAAW;MAACC,IAAI,EAAC,MAAM;MAACC,MAAM,EAAC,cAAc;MAACC,WAAW,EAAC,GAAG;MAAAC,QAAA,gBAC/F5B,OAAA;QAAM0C,CAAC,EAAC;MAA4D;QAAAT,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACvEpC,OAAA;QAAU8C,MAAM,EAAC;MAAgB;QAAAb,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACpCpC,OAAA;QAAMqC,EAAE,EAAC,IAAI;QAACC,EAAE,EAAC,IAAI;QAACC,EAAE,EAAC,GAAG;QAACC,EAAE,EAAC;MAAI;QAAAP,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACvCpC,OAAA;QAAMqC,EAAE,EAAC,IAAI;QAACC,EAAE,EAAC,IAAI;QAACC,EAAE,EAAC,GAAG;QAACC,EAAE,EAAC;MAAI;QAAAP,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACvCpC,OAAA;QAAU8C,MAAM,EAAC;MAAc;QAAAb,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC/B,CACN;IACDK,WAAW,EAAE;EACf,CAAC,CACF;EAED,MAAMM,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,QAAQ3C,SAAS;MACf,KAAK,eAAe;QAClB,oBAAOJ,OAAA,CAACL,mBAAmB;UAACqD,gBAAgB,EAAExC;QAAqB;UAAAyB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACxE,KAAK,UAAU;QACb,oBAAOpC,OAAA,CAACJ,kBAAkB;UAAAqC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAC/B,KAAK,cAAc;QACjB,oBAAOpC,OAAA,CAACH,YAAY;UAA6BoD,cAAc,EAAE3C;QAAsB,GAA7DA,qBAAqB;UAAA2B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAA0C,CAAC;MAC5F,KAAK,aAAa;QAChB,oBAAOpC,OAAA,CAACF,WAAW;UAACoD,aAAa,EAAElC;QAAkB;UAAAiB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAC1D,KAAK,SAAS;QACZ,oBACEpC,OAAA;UAAKmD,SAAS,EAAC,iBAAiB;UAAAvB,QAAA,gBAC9B5B,OAAA;YAAKmD,SAAS,EAAC,kBAAkB;YAAAvB,QAAA,eAC/B5B,OAAA;cAAKsB,KAAK,EAAC,IAAI;cAACC,MAAM,EAAC,IAAI;cAACC,OAAO,EAAC,WAAW;cAACC,IAAI,EAAC,MAAM;cAACC,MAAM,EAAC,cAAc;cAACC,WAAW,EAAC,GAAG;cAAAC,QAAA,gBAC/F5B,OAAA;gBAAM0C,CAAC,EAAC;cAA2C;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACtDpC,OAAA;gBAAQ2C,EAAE,EAAC,GAAG;gBAACC,EAAE,EAAC,GAAG;gBAACC,CAAC,EAAC;cAAG;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC9BpC,OAAA;gBAAM0C,CAAC,EAAC;cAA4B;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACvCpC,OAAA;gBAAM0C,CAAC,EAAC;cAA2B;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACNpC,OAAA;YAAA4B,QAAA,EAAI;UAAmB;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC5BpC,OAAA;YAAA4B,QAAA,EAAG;UAAoI;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAC3IpC,OAAA;YAAKmD,SAAS,EAAC,sBAAsB;YAAAvB,QAAA,gBACnC5B,OAAA;cAAA4B,QAAA,EAAI;YAAY;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACrBpC,OAAA;cAAA4B,QAAA,gBACE5B,OAAA;gBAAA4B,QAAA,EAAI;cAAoC;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC7CpC,OAAA;gBAAA4B,QAAA,EAAI;cAAkC;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC3CpC,OAAA;gBAAA4B,QAAA,EAAI;cAA4B;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACrCpC,OAAA;gBAAA4B,QAAA,EAAI;cAA6B;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAEV,KAAK,aAAa;QAChB,oBACEpC,OAAA;UAAKmD,SAAS,EAAC,iBAAiB;UAAAvB,QAAA,gBAC9B5B,OAAA;YAAKmD,SAAS,EAAC,kBAAkB;YAAAvB,QAAA,eAC/B5B,OAAA;cAAKsB,KAAK,EAAC,IAAI;cAACC,MAAM,EAAC,IAAI;cAACC,OAAO,EAAC,WAAW;cAACC,IAAI,EAAC,MAAM;cAACC,MAAM,EAAC,cAAc;cAACC,WAAW,EAAC,GAAG;cAAAC,QAAA,eAC/F5B,OAAA;gBAAU8C,MAAM,EAAC;cAAiC;gBAAAb,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACNpC,OAAA;YAAA4B,QAAA,EAAI;UAAsB;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC/BpC,OAAA;YAAA4B,QAAA,EAAG;UAAmF;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAC1FpC,OAAA;YAAKmD,SAAS,EAAC,sBAAsB;YAAAvB,QAAA,gBACnC5B,OAAA;cAAA4B,QAAA,EAAI;YAAY;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACrBpC,OAAA;cAAA4B,QAAA,gBACE5B,OAAA;gBAAA4B,QAAA,EAAI;cAAyB;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAClCpC,OAAA;gBAAA4B,QAAA,EAAI;cAAoB;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC7BpC,OAAA;gBAAA4B,QAAA,EAAI;cAAqB;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC9BpC,OAAA;gBAAA4B,QAAA,EAAI;cAAyB;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAEV,KAAK,SAAS;QACZ,oBACEpC,OAAA;UAAKmD,SAAS,EAAC,iBAAiB;UAAAvB,QAAA,gBAC9B5B,OAAA;YAAKmD,SAAS,EAAC,kBAAkB;YAAAvB,QAAA,eAC/B5B,OAAA;cAAKsB,KAAK,EAAC,IAAI;cAACC,MAAM,EAAC,IAAI;cAACC,OAAO,EAAC,WAAW;cAACC,IAAI,EAAC,MAAM;cAACC,MAAM,EAAC,cAAc;cAACC,WAAW,EAAC,GAAG;cAAAC,QAAA,gBAC/F5B,OAAA;gBAAM0C,CAAC,EAAC;cAA4D;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACvEpC,OAAA;gBAAU8C,MAAM,EAAC;cAAgB;gBAAAb,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACpCpC,OAAA;gBAAMqC,EAAE,EAAC,IAAI;gBAACC,EAAE,EAAC,IAAI;gBAACC,EAAE,EAAC,GAAG;gBAACC,EAAE,EAAC;cAAI;gBAAAP,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACvCpC,OAAA;gBAAMqC,EAAE,EAAC,IAAI;gBAACC,EAAE,EAAC,IAAI;gBAACC,EAAE,EAAC,GAAG;gBAACC,EAAE,EAAC;cAAI;gBAAAP,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACvCpC,OAAA;gBAAU8C,MAAM,EAAC;cAAc;gBAAAb,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/B;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACNpC,OAAA;YAAA4B,QAAA,EAAI;UAAiB;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC1BpC,OAAA;YAAA4B,QAAA,EAAG;UAAqF;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAC5FpC,OAAA;YAAKmD,SAAS,EAAC,sBAAsB;YAAAvB,QAAA,gBACnC5B,OAAA;cAAA4B,QAAA,EAAI;YAAY;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACrBpC,OAAA;cAAA4B,QAAA,gBACE5B,OAAA;gBAAA4B,QAAA,EAAI;cAAwB;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACjCpC,OAAA;gBAAA4B,QAAA,EAAI;cAAwB;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACjCpC,OAAA;gBAAA4B,QAAA,EAAI;cAAkB;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC3BpC,OAAA;gBAAA4B,QAAA,EAAI;cAAqB;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAEV;QACE,OAAO,IAAI;IACf;EACF,CAAC;EAED,oBACEpC,OAAA;IAAKmD,SAAS,EAAC,SAAS;IAAAvB,QAAA,gBACtB5B,OAAA;MAAKmD,SAAS,EAAC,gBAAgB;MAAAvB,QAAA,eAC7B5B,OAAA;QAAKmD,SAAS,EAAC,WAAW;QAAAvB,QAAA,gBACxB5B,OAAA;UAAImD,SAAS,EAAC,eAAe;UAAAvB,QAAA,EAAC;QAAO;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC1CpC,OAAA;UAAGmD,SAAS,EAAC,qBAAqB;UAAAvB,QAAA,EAAC;QAEnC;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENpC,OAAA;MAAKmD,SAAS,EAAC,iBAAiB;MAAAvB,QAAA,eAC9B5B,OAAA;QAAKmD,SAAS,EAAC,WAAW;QAAAvB,QAAA,gBACxB5B,OAAA;UAAKmD,SAAS,EAAC,cAAc;UAAAvB,QAAA,eAC3B5B,OAAA;YAAKmD,SAAS,EAAC,UAAU;YAAAvB,QAAA,EACtBV,IAAI,CAACkC,GAAG,CAAEC,GAAG,iBACZrD,OAAA;cAEEsD,OAAO,EAAEA,CAAA,KAAMjD,YAAY,CAACgD,GAAG,CAAClC,EAAE,CAAE;cACpCgC,SAAS,EAAE,cAAc/C,SAAS,KAAKiD,GAAG,CAAClC,EAAE,GAAG,QAAQ,GAAG,EAAE,EAAG;cAAAS,QAAA,gBAEhE5B,OAAA;gBAAKmD,SAAS,EAAC,UAAU;gBAAAvB,QAAA,EAAEyB,GAAG,CAAChC;cAAI;gBAAAY,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC1CpC,OAAA;gBAAKmD,SAAS,EAAC,aAAa;gBAAAvB,QAAA,gBAC1B5B,OAAA;kBAAKmD,SAAS,EAAC,WAAW;kBAAAvB,QAAA,EAAEyB,GAAG,CAACjC;gBAAK;kBAAAa,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC5CpC,OAAA;kBAAKmD,SAAS,EAAC,iBAAiB;kBAAAvB,QAAA,EAAEyB,GAAG,CAACZ;gBAAW;kBAAAR,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrD,CAAC;YAAA,GARDiB,GAAG,CAAClC,EAAE;cAAAc,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OASL,CACT;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENpC,OAAA;UAAKmD,SAAS,EAAC,WAAW;UAAAvB,QAAA,EACvBmB,gBAAgB,CAAC;QAAC;UAAAd,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACjC,EAAA,CAzOWF,OAA+B;AAAAsD,EAAA,GAA/BtD,OAA+B;AAAA,IAAAsD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}