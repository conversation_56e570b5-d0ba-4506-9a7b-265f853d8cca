{"ast": null, "code": "/**\n * @license\n * Copyright 2017 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nexport class CropAndResizeProgram {\n  constructor(imageShape, boxShape, cropSize, method, extrapolationValue) {\n    this.variableNames = ['Image', 'Boxes', 'BoxInd'];\n    this.outputShape = [];\n    const [batch, imageHeight, imageWidth, depth] = imageShape;\n    const [numBoxes] = boxShape;\n    const [cropHeight, cropWidth] = cropSize;\n    this.outputShape = [numBoxes, cropHeight, cropWidth, depth];\n    const methodId = method === 'bilinear' ? 1 : 0;\n    const [inputHeightFloat, inputWidthFloat] = [\"\".concat(imageHeight - 1, \".0\"), \"\".concat(imageWidth - 1, \".0\")];\n    const [heightRatio, heightScale, inY] = cropHeight > 1 ? [\"\".concat((imageHeight - 1) / (cropHeight - 1)), '(y2-y1) * height_ratio', \"y1*\".concat(inputHeightFloat, \" + float(y)*(height_scale)\")] : ['0.0', '0.0', \"0.5 * (y1+y2) * \".concat(inputHeightFloat)];\n    const [widthRatio, widthScale, inX] = cropWidth > 1 ? [\"\".concat((imageWidth - 1) / (cropWidth - 1)), '(x2-x1) * width_ratio', \"x1*\".concat(inputWidthFloat, \" + float(x)*(width_scale)\")] : ['0.0', '0.0', \"0.5 * (x1+x2) * \".concat(inputWidthFloat)];\n    // Reference implementation\n    // tslint:disable-next-line:max-line-length\n    // https://github.com/tensorflow/tensorflow/blob/master/tensorflow/core/kernels/crop_and_resize_op_gpu.cu.cc\n    this.userCode = \"\\n      const float height_ratio = float(\".concat(heightRatio, \");\\n      const float width_ratio = float(\").concat(widthRatio, \");\\n      void main() {\\n        ivec4 coords = getOutputCoords();\\n        int b = coords[0];\\n        int y = coords[1];\\n        int x = coords[2];\\n        int d = coords[3];\\n\\n        // get box vals\\n        float y1 = getBoxes(b,0);\\n        float x1 = getBoxes(b,1);\\n        float y2 = getBoxes(b,2);\\n        float x2 = getBoxes(b,3);\\n\\n        // get image in batch index\\n        int bInd = round(getBoxInd(b));\\n        if(bInd < 0 || bInd >= \").concat(batch, \") {\\n          return;\\n        }\\n\\n        float height_scale = \").concat(heightScale, \";\\n        float width_scale = \").concat(widthScale, \";\\n\\n        float in_y = \").concat(inY, \";\\n        if( in_y < 0.0 || in_y > \").concat(inputHeightFloat, \" ) {\\n          setOutput(float(\").concat(extrapolationValue, \"));\\n          return;\\n        }\\n        float in_x = \").concat(inX, \";\\n        if( in_x < 0.0 || in_x > \").concat(inputWidthFloat, \" ) {\\n          setOutput(float(\").concat(extrapolationValue, \"));\\n          return;\\n        }\\n\\n        vec2 sourceFracIndexCR = vec2(in_x,in_y);\\n        if(\").concat(methodId, \" == 1) {\\n          // Compute the four integer indices.\\n          ivec2 sourceFloorCR = ivec2(sourceFracIndexCR);\\n          ivec2 sourceCeilCR = ivec2(ceil(sourceFracIndexCR));\\n\\n          float topLeft = getImage(b, sourceFloorCR.y, sourceFloorCR.x, d);\\n          float bottomLeft = getImage(b, sourceCeilCR.y, sourceFloorCR.x, d);\\n          float topRight = getImage(b, sourceFloorCR.y, sourceCeilCR.x, d);\\n          float bottomRight = getImage(b, sourceCeilCR.y, sourceCeilCR.x, d);\\n\\n          vec2 fracCR = sourceFracIndexCR - vec2(sourceFloorCR);\\n\\n          float top = topLeft + (topRight - topLeft) * fracCR.x;\\n          float bottom = bottomLeft + (bottomRight - bottomLeft) * fracCR.x;\\n          float newValue = top + (bottom - top) * fracCR.y;\\n          setOutput(newValue);\\n        } else {\\n          // Compute the coordinators of nearest neighbor point.\\n          ivec2 sourceNearestCR = ivec2(floor(\\n            sourceFracIndexCR + vec2(0.5,0.5)));\\n          float newValue = getImage(b, sourceNearestCR.y, sourceNearestCR.x, d);\\n          setOutput(newValue);\\n        }\\n      }\\n    \");\n  }\n}", "map": {"version": 3, "names": ["CropAndResizeProgram", "constructor", "imageShape", "boxShape", "cropSize", "method", "extrapolationValue", "variableNames", "outputShape", "batch", "imageHeight", "imageWidth", "depth", "numBoxes", "cropHeight", "cropWidth", "methodId", "inputHeightFloat", "inputWidthFloat", "concat", "heightRatio", "heightScale", "inY", "widthRatio", "widthScale", "inX", "userCode"], "sources": ["C:\\tfjs-backend-webgl\\src\\crop_and_resize_gpu.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2017 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {GPGPUProgram} from './gpgpu_math';\n\nexport class CropAndResizeProgram implements GPGPUProgram {\n  variableNames = ['Image', 'Boxes', 'BoxInd'];\n  outputShape: number[] = [];\n  userCode: string;\n\n  constructor(\n      imageShape: [number, number, number, number], boxShape: [number, number],\n      cropSize: [number, number], method: 'bilinear'|'nearest',\n      extrapolationValue: number) {\n    const [batch, imageHeight, imageWidth, depth] = imageShape;\n    const [numBoxes, ] = boxShape;\n    const [cropHeight, cropWidth] = cropSize;\n    this.outputShape = [numBoxes, cropHeight, cropWidth, depth];\n    const methodId = method === 'bilinear' ? 1 : 0;\n\n    const [inputHeightFloat, inputWidthFloat] =\n        [`${imageHeight - 1}.0`, `${imageWidth - 1}.0`];\n\n    const [heightRatio, heightScale, inY] = cropHeight > 1 ?\n        [\n          `${(imageHeight - 1) / (cropHeight - 1)}`,\n          '(y2-y1) * height_ratio',\n          `y1*${inputHeightFloat} + float(y)*(height_scale)`,\n        ] :\n        [\n          '0.0',\n          '0.0',\n          `0.5 * (y1+y2) * ${inputHeightFloat}`,\n        ];\n    const [widthRatio, widthScale, inX] = cropWidth > 1 ?\n        [\n          `${(imageWidth - 1) / (cropWidth - 1)}`,\n          '(x2-x1) * width_ratio',\n          `x1*${inputWidthFloat} + float(x)*(width_scale)`,\n        ] :\n        [\n          '0.0',\n          '0.0',\n          `0.5 * (x1+x2) * ${inputWidthFloat}`,\n        ];\n\n    // Reference implementation\n    // tslint:disable-next-line:max-line-length\n    // https://github.com/tensorflow/tensorflow/blob/master/tensorflow/core/kernels/crop_and_resize_op_gpu.cu.cc\n    this.userCode = `\n      const float height_ratio = float(${heightRatio});\n      const float width_ratio = float(${widthRatio});\n      void main() {\n        ivec4 coords = getOutputCoords();\n        int b = coords[0];\n        int y = coords[1];\n        int x = coords[2];\n        int d = coords[3];\n\n        // get box vals\n        float y1 = getBoxes(b,0);\n        float x1 = getBoxes(b,1);\n        float y2 = getBoxes(b,2);\n        float x2 = getBoxes(b,3);\n\n        // get image in batch index\n        int bInd = round(getBoxInd(b));\n        if(bInd < 0 || bInd >= ${batch}) {\n          return;\n        }\n\n        float height_scale = ${heightScale};\n        float width_scale = ${widthScale};\n\n        float in_y = ${inY};\n        if( in_y < 0.0 || in_y > ${inputHeightFloat} ) {\n          setOutput(float(${extrapolationValue}));\n          return;\n        }\n        float in_x = ${inX};\n        if( in_x < 0.0 || in_x > ${inputWidthFloat} ) {\n          setOutput(float(${extrapolationValue}));\n          return;\n        }\n\n        vec2 sourceFracIndexCR = vec2(in_x,in_y);\n        if(${methodId} == 1) {\n          // Compute the four integer indices.\n          ivec2 sourceFloorCR = ivec2(sourceFracIndexCR);\n          ivec2 sourceCeilCR = ivec2(ceil(sourceFracIndexCR));\n\n          float topLeft = getImage(b, sourceFloorCR.y, sourceFloorCR.x, d);\n          float bottomLeft = getImage(b, sourceCeilCR.y, sourceFloorCR.x, d);\n          float topRight = getImage(b, sourceFloorCR.y, sourceCeilCR.x, d);\n          float bottomRight = getImage(b, sourceCeilCR.y, sourceCeilCR.x, d);\n\n          vec2 fracCR = sourceFracIndexCR - vec2(sourceFloorCR);\n\n          float top = topLeft + (topRight - topLeft) * fracCR.x;\n          float bottom = bottomLeft + (bottomRight - bottomLeft) * fracCR.x;\n          float newValue = top + (bottom - top) * fracCR.y;\n          setOutput(newValue);\n        } else {\n          // Compute the coordinators of nearest neighbor point.\n          ivec2 sourceNearestCR = ivec2(floor(\n            sourceFracIndexCR + vec2(0.5,0.5)));\n          float newValue = getImage(b, sourceNearestCR.y, sourceNearestCR.x, d);\n          setOutput(newValue);\n        }\n      }\n    `;\n  }\n}\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAmBA,OAAM,MAAOA,oBAAoB;EAK/BC,YACIC,UAA4C,EAAEC,QAA0B,EACxEC,QAA0B,EAAEC,MAA4B,EACxDC,kBAA0B;IAP9B,KAAAC,aAAa,GAAG,CAAC,OAAO,EAAE,OAAO,EAAE,QAAQ,CAAC;IAC5C,KAAAC,WAAW,GAAa,EAAE;IAOxB,MAAM,CAACC,KAAK,EAAEC,WAAW,EAAEC,UAAU,EAAEC,KAAK,CAAC,GAAGV,UAAU;IAC1D,MAAM,CAACW,QAAQ,CAAG,GAAGV,QAAQ;IAC7B,MAAM,CAACW,UAAU,EAAEC,SAAS,CAAC,GAAGX,QAAQ;IACxC,IAAI,CAACI,WAAW,GAAG,CAACK,QAAQ,EAAEC,UAAU,EAAEC,SAAS,EAAEH,KAAK,CAAC;IAC3D,MAAMI,QAAQ,GAAGX,MAAM,KAAK,UAAU,GAAG,CAAC,GAAG,CAAC;IAE9C,MAAM,CAACY,gBAAgB,EAAEC,eAAe,CAAC,GACrC,IAAAC,MAAA,CAAIT,WAAW,GAAG,CAAC,YAAAS,MAAA,CAASR,UAAU,GAAG,CAAC,QAAK;IAEnD,MAAM,CAACS,WAAW,EAAEC,WAAW,EAAEC,GAAG,CAAC,GAAGR,UAAU,GAAG,CAAC,GAClD,IAAAK,MAAA,CACK,CAACT,WAAW,GAAG,CAAC,KAAKI,UAAU,GAAG,CAAC,CAAC,GACvC,wBAAwB,QAAAK,MAAA,CAClBF,gBAAgB,gCACvB,GACD,CACE,KAAK,EACL,KAAK,qBAAAE,MAAA,CACcF,gBAAgB,EACpC;IACL,MAAM,CAACM,UAAU,EAAEC,UAAU,EAAEC,GAAG,CAAC,GAAGV,SAAS,GAAG,CAAC,GAC/C,IAAAI,MAAA,CACK,CAACR,UAAU,GAAG,CAAC,KAAKI,SAAS,GAAG,CAAC,CAAC,GACrC,uBAAuB,QAAAI,MAAA,CACjBD,eAAe,+BACtB,GACD,CACE,KAAK,EACL,KAAK,qBAAAC,MAAA,CACcD,eAAe,EACnC;IAEL;IACA;IACA;IACA,IAAI,CAACQ,QAAQ,+CAAAP,MAAA,CACwBC,WAAW,gDAAAD,MAAA,CACZI,UAAU,gdAAAJ,MAAA,CAgBjBV,KAAK,wEAAAU,MAAA,CAIPE,WAAW,qCAAAF,MAAA,CACZK,UAAU,gCAAAL,MAAA,CAEjBG,GAAG,0CAAAH,MAAA,CACSF,gBAAgB,sCAAAE,MAAA,CACvBb,kBAAkB,8DAAAa,MAAA,CAGvBM,GAAG,0CAAAN,MAAA,CACSD,eAAe,sCAAAC,MAAA,CACtBb,kBAAkB,yGAAAa,MAAA,CAKjCH,QAAQ,umCAwBhB;EACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}