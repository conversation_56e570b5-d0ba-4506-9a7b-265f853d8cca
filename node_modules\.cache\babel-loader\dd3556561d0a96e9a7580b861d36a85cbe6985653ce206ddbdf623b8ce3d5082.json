{"ast": null, "code": "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Use of this source code is governed by an MIT-style\n * license that can be found in the LICENSE file or at\n * https://opensource.org/licenses/MIT.\n * =============================================================================\n */\n/** Utility functions related to user-defined metadata. */\n// Maximum recommended serialized size for user-defined metadata.\n// Beyond this limit, a warning message will be printed during model loading and\n// saving.\nexport const MAX_USER_DEFINED_METADATA_SERIALIZED_LENGTH = 1 * 1024 * 1024;\n/**\n * Check validity of user-defined metadata.\n *\n * @param userDefinedMetadata\n * @param modelName Name of the model that the user-defined metadata belongs to.\n *   Used during construction of error messages.\n * @param checkSize Whether to check the size of the metadata is under\n *   recommended limit. Default: `false`. If `true`, will try stringify the\n *   JSON object and print a console warning if the serialzied size is above the\n *   limit.\n * @throws Error if `userDefinedMetadata` is not a plain JSON object.\n */\nexport function checkUserDefinedMetadata(userDefinedMetadata, modelName, checkSize = false) {\n  if (userDefinedMetadata == null || typeof userDefinedMetadata !== 'object' || Object.getPrototypeOf(userDefinedMetadata) !== Object.prototype || !plainObjectCheck(userDefinedMetadata)) {\n    throw new Error('User-defined metadata is expected to be a JSON object, but is not.');\n  }\n  if (checkSize) {\n    const out = JSON.stringify(userDefinedMetadata);\n    if (out.length > MAX_USER_DEFINED_METADATA_SERIALIZED_LENGTH) {\n      console.warn(`User-defined metadata of model \"${modelName}\" is too large in ` + `size (length=${out.length} when serialized). It is not ` + `recommended to store such large objects in user-defined metadata. ` + `Please make sure its serialized length is <= ` + `${MAX_USER_DEFINED_METADATA_SERIALIZED_LENGTH}.`);\n    }\n  }\n}\n/**\n * Check if an input is plain JSON object or any valid subfield of it.\n *\n * @param x The input to be checked.\n * @param assertObject Whether to assert `x` is a JSON object, i.e., reject\n *   cases of arrays and primitives.\n * @return Returns `true` if and only if `x` is a plain JSON object,\n *   a JSON-valid primitive including string, number, boolean and null,\n *   or an array of the said types.\n */\n// tslint:disable-next-line:no-any\nexport function plainObjectCheck(x) {\n  if (x === null) {\n    // Note: typeof `null` is 'object', and `null` is valid in JSON.\n    return true;\n  } else if (typeof x === 'object') {\n    if (Object.getPrototypeOf(x) === Object.prototype) {\n      // `x` is a JavaScript object and its prototype is Object.\n      const keys = Object.keys(x);\n      for (const key of keys) {\n        if (typeof key !== 'string') {\n          // JSON keys must be strings.\n          return false;\n        }\n        if (!plainObjectCheck(x[key])) {\n          // Recursive call.\n          return false;\n        }\n      }\n      return true;\n    } else {\n      // `x` is a JavaScript object but its prototype is not Object.\n      if (Array.isArray(x)) {\n        // `x` is a JavaScript array.\n        for (const item of x) {\n          if (!plainObjectCheck(item)) {\n            // Recursive call.\n            return false;\n          }\n        }\n        return true;\n      } else {\n        // `x` is a JavaScript object and its prototype is not Object,\n        // and it's not an Array. I.e., it's a complex object such as\n        // `Error` and `Date`.\n        return false;\n      }\n    }\n  } else {\n    // `x` is not a JavaScript object or `null`.\n    const xType = typeof x;\n    return xType === 'string' || xType === 'number' || xType === 'boolean';\n  }\n}", "map": {"version": 3, "names": ["MAX_USER_DEFINED_METADATA_SERIALIZED_LENGTH", "checkUserDefinedMetadata", "userDefinedMetadata", "modelName", "checkSize", "Object", "getPrototypeOf", "prototype", "plainObjectCheck", "Error", "out", "JSON", "stringify", "length", "console", "warn", "x", "keys", "key", "Array", "isArray", "item", "xType"], "sources": ["C:\\tfjs-layers\\src\\user_defined_metadata.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Use of this source code is governed by an MIT-style\n * license that can be found in the LICENSE file or at\n * https://opensource.org/licenses/MIT.\n * =============================================================================\n */\n\n/** Utility functions related to user-defined metadata. */\n\n// Maximum recommended serialized size for user-defined metadata.\n// Beyond this limit, a warning message will be printed during model loading and\n// saving.\nexport const MAX_USER_DEFINED_METADATA_SERIALIZED_LENGTH = 1 * 1024 * 1024;\n\n/**\n * Check validity of user-defined metadata.\n *\n * @param userDefinedMetadata\n * @param modelName Name of the model that the user-defined metadata belongs to.\n *   Used during construction of error messages.\n * @param checkSize Whether to check the size of the metadata is under\n *   recommended limit. Default: `false`. If `true`, will try stringify the\n *   JSON object and print a console warning if the serialzied size is above the\n *   limit.\n * @throws Error if `userDefinedMetadata` is not a plain JSON object.\n */\nexport function checkUserDefinedMetadata(\n    userDefinedMetadata: {}, modelName: string, checkSize = false): void {\n  if (userDefinedMetadata == null ||\n      typeof userDefinedMetadata !== 'object' ||\n      Object.getPrototypeOf(userDefinedMetadata) !== Object.prototype ||\n      !plainObjectCheck(userDefinedMetadata)) {\n    throw new Error(\n        'User-defined metadata is expected to be a JSON object, but is not.');\n  }\n\n  if (checkSize) {\n    const out = JSON.stringify(userDefinedMetadata);\n    if (out.length > MAX_USER_DEFINED_METADATA_SERIALIZED_LENGTH) {\n      console.warn(\n          `User-defined metadata of model \"${modelName}\" is too large in ` +\n          `size (length=${out.length} when serialized). It is not ` +\n          `recommended to store such large objects in user-defined metadata. ` +\n          `Please make sure its serialized length is <= ` +\n          `${MAX_USER_DEFINED_METADATA_SERIALIZED_LENGTH}.`);\n    }\n  }\n}\n\n/**\n * Check if an input is plain JSON object or any valid subfield of it.\n *\n * @param x The input to be checked.\n * @param assertObject Whether to assert `x` is a JSON object, i.e., reject\n *   cases of arrays and primitives.\n * @return Returns `true` if and only if `x` is a plain JSON object,\n *   a JSON-valid primitive including string, number, boolean and null,\n *   or an array of the said types.\n */\n// tslint:disable-next-line:no-any\nexport function plainObjectCheck(x: any): boolean {\n  if (x === null) {\n    // Note: typeof `null` is 'object', and `null` is valid in JSON.\n    return true;\n  } else if (typeof x === 'object') {\n    if (Object.getPrototypeOf(x) === Object.prototype) {\n      // `x` is a JavaScript object and its prototype is Object.\n      const keys = Object.keys(x);\n      for (const key of keys) {\n        if (typeof key !== 'string') {\n          // JSON keys must be strings.\n          return false;\n        }\n        if (!plainObjectCheck(x[key])) {  // Recursive call.\n          return false;\n        }\n      }\n      return true;\n    } else {\n      // `x` is a JavaScript object but its prototype is not Object.\n      if (Array.isArray(x)) {\n        // `x` is a JavaScript array.\n        for (const item of x) {\n          if (!plainObjectCheck(item)) {  // Recursive call.\n            return false;\n          }\n        }\n        return true;\n      } else {\n        // `x` is a JavaScript object and its prototype is not Object,\n        // and it's not an Array. I.e., it's a complex object such as\n        // `Error` and `Date`.\n        return false;\n      }\n    }\n  } else {\n    // `x` is not a JavaScript object or `null`.\n    const xType = typeof x;\n    return xType === 'string' || xType === 'number' || xType === 'boolean';\n  }\n}\n"], "mappings": "AAAA;;;;;;;;;AAUA;AAEA;AACA;AACA;AACA,OAAO,MAAMA,2CAA2C,GAAG,CAAC,GAAG,IAAI,GAAG,IAAI;AAE1E;;;;;;;;;;;;AAYA,OAAM,SAAUC,wBAAwBA,CACpCC,mBAAuB,EAAEC,SAAiB,EAAEC,SAAS,GAAG,KAAK;EAC/D,IAAIF,mBAAmB,IAAI,IAAI,IAC3B,OAAOA,mBAAmB,KAAK,QAAQ,IACvCG,MAAM,CAACC,cAAc,CAACJ,mBAAmB,CAAC,KAAKG,MAAM,CAACE,SAAS,IAC/D,CAACC,gBAAgB,CAACN,mBAAmB,CAAC,EAAE;IAC1C,MAAM,IAAIO,KAAK,CACX,oEAAoE,CAAC;;EAG3E,IAAIL,SAAS,EAAE;IACb,MAAMM,GAAG,GAAGC,IAAI,CAACC,SAAS,CAACV,mBAAmB,CAAC;IAC/C,IAAIQ,GAAG,CAACG,MAAM,GAAGb,2CAA2C,EAAE;MAC5Dc,OAAO,CAACC,IAAI,CACR,mCAAmCZ,SAAS,oBAAoB,GAChE,gBAAgBO,GAAG,CAACG,MAAM,+BAA+B,GACzD,oEAAoE,GACpE,+CAA+C,GAC/C,GAAGb,2CAA2C,GAAG,CAAC;;;AAG5D;AAEA;;;;;;;;;;AAUA;AACA,OAAM,SAAUQ,gBAAgBA,CAACQ,CAAM;EACrC,IAAIA,CAAC,KAAK,IAAI,EAAE;IACd;IACA,OAAO,IAAI;GACZ,MAAM,IAAI,OAAOA,CAAC,KAAK,QAAQ,EAAE;IAChC,IAAIX,MAAM,CAACC,cAAc,CAACU,CAAC,CAAC,KAAKX,MAAM,CAACE,SAAS,EAAE;MACjD;MACA,MAAMU,IAAI,GAAGZ,MAAM,CAACY,IAAI,CAACD,CAAC,CAAC;MAC3B,KAAK,MAAME,GAAG,IAAID,IAAI,EAAE;QACtB,IAAI,OAAOC,GAAG,KAAK,QAAQ,EAAE;UAC3B;UACA,OAAO,KAAK;;QAEd,IAAI,CAACV,gBAAgB,CAACQ,CAAC,CAACE,GAAG,CAAC,CAAC,EAAE;UAAG;UAChC,OAAO,KAAK;;;MAGhB,OAAO,IAAI;KACZ,MAAM;MACL;MACA,IAAIC,KAAK,CAACC,OAAO,CAACJ,CAAC,CAAC,EAAE;QACpB;QACA,KAAK,MAAMK,IAAI,IAAIL,CAAC,EAAE;UACpB,IAAI,CAACR,gBAAgB,CAACa,IAAI,CAAC,EAAE;YAAG;YAC9B,OAAO,KAAK;;;QAGhB,OAAO,IAAI;OACZ,MAAM;QACL;QACA;QACA;QACA,OAAO,KAAK;;;GAGjB,MAAM;IACL;IACA,MAAMC,KAAK,GAAG,OAAON,CAAC;IACtB,OAAOM,KAAK,KAAK,QAAQ,IAAIA,KAAK,KAAK,QAAQ,IAAIA,KAAK,KAAK,SAAS;;AAE1E", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}