{"ast": null, "code": "\"use strict\";\n\nconst DoublyLinkedList = require(\"./DoublyLinkedList\");\nconst Deque = require(\"./Deque\");\n\n/**\n * Sort of a internal queue for holding the waiting\n * resource requets for a given \"priority\".\n * Also handles managing timeouts rejections on items (is this the best place for this?)\n * This is the last point where we know which queue a resourceRequest is in\n *\n */\nclass Queue extends Deque {\n  /**\n   * Adds the obj to the end of the list for this slot\n   * we completely override the parent method because we need access to the\n   * node for our rejection handler\n   * @param {any} resourceRequest [description]\n   */\n  push(resourceRequest) {\n    const node = DoublyLinkedList.createNode(resourceRequest);\n    resourceRequest.promise.catch(this._createTimeoutRejectionHandler(node));\n    this._list.insertEnd(node);\n  }\n  _createTimeoutRejectionHandler(node) {\n    return reason => {\n      if (reason.name === \"TimeoutError\") {\n        this._list.remove(node);\n      }\n    };\n  }\n}\nmodule.exports = Queue;", "map": {"version": 3, "names": ["DoublyLinkedList", "require", "<PERSON><PERSON>", "Queue", "push", "resourceRequest", "node", "createNode", "promise", "catch", "_createTimeoutRejectionHandler", "_list", "insertEnd", "reason", "name", "remove", "module", "exports"], "sources": ["C:/tmsft/node_modules/generic-pool/lib/Queue.js"], "sourcesContent": ["\"use strict\";\n\nconst DoublyLinkedList = require(\"./DoublyLinkedList\");\nconst Deque = require(\"./Deque\");\n\n/**\n * Sort of a internal queue for holding the waiting\n * resource requets for a given \"priority\".\n * Also handles managing timeouts rejections on items (is this the best place for this?)\n * This is the last point where we know which queue a resourceRequest is in\n *\n */\nclass Queue extends Deque {\n  /**\n   * Adds the obj to the end of the list for this slot\n   * we completely override the parent method because we need access to the\n   * node for our rejection handler\n   * @param {any} resourceRequest [description]\n   */\n  push(resourceRequest) {\n    const node = DoublyLinkedList.createNode(resourceRequest);\n    resourceRequest.promise.catch(this._createTimeoutRejectionHandler(node));\n    this._list.insertEnd(node);\n  }\n\n  _createTimeoutRejectionHandler(node) {\n    return reason => {\n      if (reason.name === \"TimeoutError\") {\n        this._list.remove(node);\n      }\n    };\n  }\n}\n\nmodule.exports = Queue;\n"], "mappings": "AAAA,YAAY;;AAEZ,MAAMA,gBAAgB,GAAGC,OAAO,CAAC,oBAAoB,CAAC;AACtD,MAAMC,KAAK,GAAGD,OAAO,CAAC,SAAS,CAAC;;AAEhC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAME,KAAK,SAASD,KAAK,CAAC;EACxB;AACF;AACA;AACA;AACA;AACA;EACEE,IAAIA,CAACC,eAAe,EAAE;IACpB,MAAMC,IAAI,GAAGN,gBAAgB,CAACO,UAAU,CAACF,eAAe,CAAC;IACzDA,eAAe,CAACG,OAAO,CAACC,KAAK,CAAC,IAAI,CAACC,8BAA8B,CAACJ,IAAI,CAAC,CAAC;IACxE,IAAI,CAACK,KAAK,CAACC,SAAS,CAACN,IAAI,CAAC;EAC5B;EAEAI,8BAA8BA,CAACJ,IAAI,EAAE;IACnC,OAAOO,MAAM,IAAI;MACf,IAAIA,MAAM,CAACC,IAAI,KAAK,cAAc,EAAE;QAClC,IAAI,CAACH,KAAK,CAACI,MAAM,CAACT,IAAI,CAAC;MACzB;IACF,CAAC;EACH;AACF;AAEAU,MAAM,CAACC,OAAO,GAAGd,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}