{"ast": null, "code": "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { buffer, Diag, util } from '@tensorflow/tfjs-core';\nexport function diag(args) {\n  const {\n    inputs,\n    backend\n  } = args;\n  const {\n    x\n  } = inputs;\n  const xSize = util.sizeFromShape(x.shape);\n  const xVals = backend.data.get(x.dataId).values;\n  const outBuf = buffer([xSize, xSize], x.dtype);\n  const vals = outBuf.values;\n  for (let i = 0; i < xVals.length; i++) {\n    vals[i * xSize + i] = xVals[i];\n  }\n  const outShape = [...x.shape, ...x.shape];\n  return backend.makeTensorInfo(outShape, outBuf.dtype, outBuf.values);\n}\nexport const diagConfig = {\n  kernelName: Diag,\n  backendName: 'cpu',\n  kernelFunc: diag\n};", "map": {"version": 3, "names": ["buffer", "Diag", "util", "diag", "args", "inputs", "backend", "x", "xSize", "sizeFromShape", "shape", "xVals", "data", "get", "dataId", "values", "outBuf", "dtype", "vals", "i", "length", "outShape", "makeTensorInfo", "diagConfig", "kernelName", "backendName", "kernelFunc"], "sources": ["C:\\tfjs-backend-cpu\\src\\kernels\\Diag.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {buffer, Diag, DiagInputs, KernelConfig, KernelFunc, TensorInfo, TypedArray, util} from '@tensorflow/tfjs-core';\n\nimport {MathBackendCPU} from '../backend_cpu';\n\nexport function diag(args: {inputs: DiagInputs, backend: MathBackendCPU}):\n    TensorInfo {\n  const {inputs, backend} = args;\n  const {x} = inputs;\n\n  const xSize = util.sizeFromShape(x.shape);\n\n  const xVals = backend.data.get(x.dataId).values as TypedArray;\n  const outBuf = buffer([xSize, xSize], x.dtype);\n  const vals = outBuf.values;\n  for (let i = 0; i < xVals.length; i++) {\n    vals[i * xSize + i] = xVals[i];\n  }\n\n  const outShape = [...x.shape, ...x.shape];\n\n  return backend.makeTensorInfo(outShape, outBuf.dtype, outBuf.values);\n}\n\nexport const diagConfig: KernelConfig = {\n  kernelName: Diag,\n  backendName: 'cpu',\n  kernelFunc: diag as unknown as KernelFunc\n};\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAiBA,SAAQA,MAAM,EAAEC,IAAI,EAAgEC,IAAI,QAAO,uBAAuB;AAItH,OAAM,SAAUC,IAAIA,CAACC,IAAmD;EAEtE,MAAM;IAACC,MAAM;IAAEC;EAAO,CAAC,GAAGF,IAAI;EAC9B,MAAM;IAACG;EAAC,CAAC,GAAGF,MAAM;EAElB,MAAMG,KAAK,GAAGN,IAAI,CAACO,aAAa,CAACF,CAAC,CAACG,KAAK,CAAC;EAEzC,MAAMC,KAAK,GAAGL,OAAO,CAACM,IAAI,CAACC,GAAG,CAACN,CAAC,CAACO,MAAM,CAAC,CAACC,MAAoB;EAC7D,MAAMC,MAAM,GAAGhB,MAAM,CAAC,CAACQ,KAAK,EAAEA,KAAK,CAAC,EAAED,CAAC,CAACU,KAAK,CAAC;EAC9C,MAAMC,IAAI,GAAGF,MAAM,CAACD,MAAM;EAC1B,KAAK,IAAII,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGR,KAAK,CAACS,MAAM,EAAED,CAAC,EAAE,EAAE;IACrCD,IAAI,CAACC,CAAC,GAAGX,KAAK,GAAGW,CAAC,CAAC,GAAGR,KAAK,CAACQ,CAAC,CAAC;;EAGhC,MAAME,QAAQ,GAAG,CAAC,GAAGd,CAAC,CAACG,KAAK,EAAE,GAAGH,CAAC,CAACG,KAAK,CAAC;EAEzC,OAAOJ,OAAO,CAACgB,cAAc,CAACD,QAAQ,EAAEL,MAAM,CAACC,KAAK,EAAED,MAAM,CAACD,MAAM,CAAC;AACtE;AAEA,OAAO,MAAMQ,UAAU,GAAiB;EACtCC,UAAU,EAAEvB,IAAI;EAChBwB,WAAW,EAAE,KAAK;EAClBC,UAAU,EAAEvB;CACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}