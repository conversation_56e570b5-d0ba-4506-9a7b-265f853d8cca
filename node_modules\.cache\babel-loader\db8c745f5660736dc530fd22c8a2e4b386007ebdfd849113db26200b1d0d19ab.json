{"ast": null, "code": "import React from'react';import{DataHub}from'./components/DataHub';import'./styles/globals.css';import{jsx as _jsx}from\"react/jsx-runtime\";function App(){const handleTransactionImport=(transactions,bankAccount)=>{console.log(\"Successfully imported \".concat(transactions.length,\" transactions for \").concat(bankAccount.name));// Here you would typically:\n// 1. Save transactions to local database\n// 2. Update bank account balance\n// 3. Trigger any necessary business logic\n// 4. Show success notification\n};return/*#__PURE__*/_jsx(\"div\",{className:\"App\",children:/*#__PURE__*/_jsx(DataHub,{onTransactionImport:handleTransactionImport})});}export default App;", "map": {"version": 3, "names": ["React", "DataHub", "jsx", "_jsx", "App", "handleTransactionImport", "transactions", "bankAccount", "console", "log", "concat", "length", "name", "className", "children", "onTransactionImport"], "sources": ["C:/tmsft/src/App.tsx"], "sourcesContent": ["import React from 'react';\r\nimport { DataHub } from './components/DataHub';\r\nimport { Transaction, BankAccount } from './types';\r\nimport './styles/globals.css';\r\n\r\nfunction App(): React.ReactElement {\r\n  const handleTransactionImport = (transactions: Transaction[], bankAccount: BankAccount): void => {\r\n    console.log(`Successfully imported ${transactions.length} transactions for ${bankAccount.name}`);\r\n    // Here you would typically:\r\n    // 1. Save transactions to local database\r\n    // 2. Update bank account balance\r\n    // 3. Trigger any necessary business logic\r\n    // 4. Show success notification\r\n  };\r\n\r\n  return (\r\n    <div className=\"App\">\r\n      <DataHub onTransactionImport={handleTransactionImport} />\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default App; "], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,OAASC,OAAO,KAAQ,sBAAsB,CAE9C,MAAO,sBAAsB,CAAC,OAAAC,GAAA,IAAAC,IAAA,yBAE9B,QAAS,CAAAC,GAAGA,CAAA,CAAuB,CACjC,KAAM,CAAAC,uBAAuB,CAAGA,CAACC,YAA2B,CAAEC,WAAwB,GAAW,CAC/FC,OAAO,CAACC,GAAG,0BAAAC,MAAA,CAA0BJ,YAAY,CAACK,MAAM,uBAAAD,MAAA,CAAqBH,WAAW,CAACK,IAAI,CAAE,CAAC,CAChG;AACA;AACA;AACA;AACA;AACF,CAAC,CAED,mBACET,IAAA,QAAKU,SAAS,CAAC,KAAK,CAAAC,QAAA,cAClBX,IAAA,CAACF,OAAO,EAACc,mBAAmB,CAAEV,uBAAwB,CAAE,CAAC,CACtD,CAAC,CAEV,CAEA,cAAe,CAAAD,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}