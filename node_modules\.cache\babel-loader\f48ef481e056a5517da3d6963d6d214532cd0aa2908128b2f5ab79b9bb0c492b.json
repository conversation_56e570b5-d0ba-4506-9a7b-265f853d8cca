{"ast": null, "code": "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { TopK } from '@tensorflow/tfjs-core';\nimport { assertNotComplex } from '../cpu_util';\nimport { topKImpl } from './TopK_impl';\nexport function topK(args) {\n  const {\n    inputs,\n    backend,\n    attrs\n  } = args;\n  const {\n    x\n  } = inputs;\n  const {\n    k,\n    sorted\n  } = attrs;\n  assertNotComplex(x, 'topk');\n  const xVals = backend.data.get(x.dataId).values;\n  const [allTopKVals, allTopKIndices] = topKImpl(xVals, x.shape, x.dtype, k, sorted);\n  return [backend.makeTensorInfo(allTopKVals.shape, allTopKVals.dtype, allTopKVals.values), backend.makeTensorInfo(allTopKIndices.shape, allTopKIndices.dtype, allTopKIndices.values)];\n}\nexport const topKConfig = {\n  kernelName: TopK,\n  backendName: 'cpu',\n  kernelFunc: topK\n};", "map": {"version": 3, "names": ["TopK", "assertNotComplex", "topKImpl", "topK", "args", "inputs", "backend", "attrs", "x", "k", "sorted", "xVals", "data", "get", "dataId", "values", "allTopKVals", "allTopKIndices", "shape", "dtype", "makeTensorInfo", "topKConfig", "kernelName", "backendName", "kernelFunc"], "sources": ["C:\\tfjs-backend-cpu\\src\\kernels\\TopK.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {KernelConfig, KernelFunc, NumericDataType, TensorInfo, TopK, TopKAttrs, TopKInputs, TypedArray} from '@tensorflow/tfjs-core';\n\nimport {MathBackendCPU} from '../backend_cpu';\nimport {assertNotComplex} from '../cpu_util';\nimport {topKImpl} from './TopK_impl';\n\nexport function topK(\n    args: {inputs: TopKInputs, backend: MathBackendCPU, attrs: TopKAttrs}):\n    [TensorInfo, TensorInfo] {\n  const {inputs, backend, attrs} = args;\n  const {x} = inputs;\n  const {k, sorted} = attrs;\n\n  assertNotComplex(x, 'topk');\n\n  const xVals = backend.data.get(x.dataId).values as TypedArray;\n  const [allTopKVals, allTopKIndices] =\n      topKImpl(xVals, x.shape, x.dtype as NumericDataType, k, sorted);\n\n  return [\n    backend.makeTensorInfo(\n        allTopKVals.shape, allTopKVals.dtype, allTopKVals.values),\n    backend.makeTensorInfo(\n        allTopKIndices.shape, allTopKIndices.dtype, allTopKIndices.values)\n  ];\n}\n\nexport const topKConfig: KernelConfig = {\n  kernelName: TopK,\n  backendName: 'cpu',\n  kernelFunc: topK as unknown as KernelFunc\n};\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAiBA,SAA+DA,IAAI,QAA0C,uBAAuB;AAGpI,SAAQC,gBAAgB,QAAO,aAAa;AAC5C,SAAQC,QAAQ,QAAO,aAAa;AAEpC,OAAM,SAAUC,IAAIA,CAChBC,IAAqE;EAEvE,MAAM;IAACC,MAAM;IAAEC,OAAO;IAAEC;EAAK,CAAC,GAAGH,IAAI;EACrC,MAAM;IAACI;EAAC,CAAC,GAAGH,MAAM;EAClB,MAAM;IAACI,CAAC;IAAEC;EAAM,CAAC,GAAGH,KAAK;EAEzBN,gBAAgB,CAACO,CAAC,EAAE,MAAM,CAAC;EAE3B,MAAMG,KAAK,GAAGL,OAAO,CAACM,IAAI,CAACC,GAAG,CAACL,CAAC,CAACM,MAAM,CAAC,CAACC,MAAoB;EAC7D,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAC/Bf,QAAQ,CAACS,KAAK,EAAEH,CAAC,CAACU,KAAK,EAAEV,CAAC,CAACW,KAAwB,EAAEV,CAAC,EAAEC,MAAM,CAAC;EAEnE,OAAO,CACLJ,OAAO,CAACc,cAAc,CAClBJ,WAAW,CAACE,KAAK,EAAEF,WAAW,CAACG,KAAK,EAAEH,WAAW,CAACD,MAAM,CAAC,EAC7DT,OAAO,CAACc,cAAc,CAClBH,cAAc,CAACC,KAAK,EAAED,cAAc,CAACE,KAAK,EAAEF,cAAc,CAACF,MAAM,CAAC,CACvE;AACH;AAEA,OAAO,MAAMM,UAAU,GAAiB;EACtCC,UAAU,EAAEtB,IAAI;EAChBuB,WAAW,EAAE,KAAK;EAClBC,UAAU,EAAErB;CACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}