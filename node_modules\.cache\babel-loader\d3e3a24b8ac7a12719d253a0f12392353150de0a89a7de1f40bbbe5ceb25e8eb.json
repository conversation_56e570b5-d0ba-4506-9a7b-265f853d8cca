{"ast": null, "code": "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { nonMaxSuppressionV3Impl } from '../../backends/non_max_suppression_impl';\nimport { convertToTensor } from '../../tensor_util_env';\nimport { nonMaxSuppSanityCheck } from '../nonmax_util';\nimport { tensor1d } from '../tensor1d';\n/**\n * Performs non maximum suppression of bounding boxes based on\n * iou (intersection over union).\n *\n * This is the async version of `nonMaxSuppression`\n *\n * @param boxes a 2d tensor of shape `[numBoxes, 4]`. Each entry is\n *     `[y1, x1, y2, x2]`, where `(y1, x1)` and `(y2, x2)` are the corners of\n *     the bounding box.\n * @param scores a 1d tensor providing the box scores of shape `[numBoxes]`.\n * @param maxOutputSize The maximum number of boxes to be selected.\n * @param iouThreshold A float representing the threshold for deciding whether\n *     boxes overlap too much with respect to IOU. Must be between [0, 1].\n *     Defaults to 0.5 (50% box overlap).\n * @param scoreThreshold A threshold for deciding when to remove boxes based\n *     on score. Defaults to -inf, which means any score is accepted.\n * @return A 1D tensor with the selected box indices.\n *\n * @doc {heading: 'Operations', subheading: 'Images', namespace: 'image'}\n */\nasync function nonMaxSuppressionAsync_(boxes, scores, maxOutputSize, iouThreshold = 0.5, scoreThreshold = Number.NEGATIVE_INFINITY) {\n  const $boxes = convertToTensor(boxes, 'boxes', 'nonMaxSuppressionAsync');\n  const $scores = convertToTensor(scores, 'scores', 'nonMaxSuppressionAsync');\n  const inputs = nonMaxSuppSanityCheck($boxes, $scores, maxOutputSize, iouThreshold, scoreThreshold);\n  maxOutputSize = inputs.maxOutputSize;\n  iouThreshold = inputs.iouThreshold;\n  scoreThreshold = inputs.scoreThreshold;\n  const boxesAndScores = await Promise.all([$boxes.data(), $scores.data()]);\n  const boxesVals = boxesAndScores[0];\n  const scoresVals = boxesAndScores[1];\n  // We call a cpu based impl directly with the typedarray data  here rather\n  // than a kernel because all kernels are synchronous (and thus cannot await\n  // .data()).\n  const {\n    selectedIndices\n  } = nonMaxSuppressionV3Impl(boxesVals, scoresVals, maxOutputSize, iouThreshold, scoreThreshold);\n  if ($boxes !== boxes) {\n    $boxes.dispose();\n  }\n  if ($scores !== scores) {\n    $scores.dispose();\n  }\n  return tensor1d(selectedIndices, 'int32');\n}\nexport const nonMaxSuppressionAsync = nonMaxSuppressionAsync_;", "map": {"version": 3, "names": ["nonMaxSuppressionV3Impl", "convertToTensor", "nonMaxSuppSanityCheck", "tensor1d", "nonMaxSuppressionAsync_", "boxes", "scores", "maxOutputSize", "iouThreshold", "scoreThreshold", "Number", "NEGATIVE_INFINITY", "$boxes", "$scores", "inputs", "boxesAndScores", "Promise", "all", "data", "boxesVals", "scoresVals", "selectedIndices", "dispose", "nonMaxSuppressionAsync"], "sources": ["C:\\tfjs-core\\src\\ops\\image\\non_max_suppression_async.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {nonMaxSuppressionV3Impl} from '../../backends/non_max_suppression_impl';\nimport {Tensor1D, Tensor2D} from '../../tensor';\nimport {convertToTensor} from '../../tensor_util_env';\nimport {TensorLike} from '../../types';\nimport {nonMaxSuppSanityCheck} from '../nonmax_util';\nimport {tensor1d} from '../tensor1d';\n\n/**\n * Performs non maximum suppression of bounding boxes based on\n * iou (intersection over union).\n *\n * This is the async version of `nonMaxSuppression`\n *\n * @param boxes a 2d tensor of shape `[numBoxes, 4]`. Each entry is\n *     `[y1, x1, y2, x2]`, where `(y1, x1)` and `(y2, x2)` are the corners of\n *     the bounding box.\n * @param scores a 1d tensor providing the box scores of shape `[numBoxes]`.\n * @param maxOutputSize The maximum number of boxes to be selected.\n * @param iouThreshold A float representing the threshold for deciding whether\n *     boxes overlap too much with respect to IOU. Must be between [0, 1].\n *     Defaults to 0.5 (50% box overlap).\n * @param scoreThreshold A threshold for deciding when to remove boxes based\n *     on score. Defaults to -inf, which means any score is accepted.\n * @return A 1D tensor with the selected box indices.\n *\n * @doc {heading: 'Operations', subheading: 'Images', namespace: 'image'}\n */\nasync function nonMaxSuppressionAsync_(\n    boxes: Tensor2D|TensorLike, scores: Tensor1D|TensorLike,\n    maxOutputSize: number, iouThreshold = 0.5,\n    scoreThreshold = Number.NEGATIVE_INFINITY): Promise<Tensor1D> {\n  const $boxes = convertToTensor(boxes, 'boxes', 'nonMaxSuppressionAsync');\n  const $scores = convertToTensor(scores, 'scores', 'nonMaxSuppressionAsync');\n\n  const inputs = nonMaxSuppSanityCheck(\n      $boxes, $scores, maxOutputSize, iouThreshold, scoreThreshold);\n  maxOutputSize = inputs.maxOutputSize;\n  iouThreshold = inputs.iouThreshold;\n  scoreThreshold = inputs.scoreThreshold;\n\n  const boxesAndScores = await Promise.all([$boxes.data(), $scores.data()]);\n  const boxesVals = boxesAndScores[0];\n  const scoresVals = boxesAndScores[1];\n\n  // We call a cpu based impl directly with the typedarray data  here rather\n  // than a kernel because all kernels are synchronous (and thus cannot await\n  // .data()).\n  const {selectedIndices} = nonMaxSuppressionV3Impl(\n      boxesVals, scoresVals, maxOutputSize, iouThreshold, scoreThreshold);\n  if ($boxes !== boxes) {\n    $boxes.dispose();\n  }\n  if ($scores !== scores) {\n    $scores.dispose();\n  }\n\n  return tensor1d(selectedIndices, 'int32');\n}\n\nexport const nonMaxSuppressionAsync = nonMaxSuppressionAsync_;\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAiBA,SAAQA,uBAAuB,QAAO,yCAAyC;AAE/E,SAAQC,eAAe,QAAO,uBAAuB;AAErD,SAAQC,qBAAqB,QAAO,gBAAgB;AACpD,SAAQC,QAAQ,QAAO,aAAa;AAEpC;;;;;;;;;;;;;;;;;;;;AAoBA,eAAeC,uBAAuBA,CAClCC,KAA0B,EAAEC,MAA2B,EACvDC,aAAqB,EAAEC,YAAY,GAAG,GAAG,EACzCC,cAAc,GAAGC,MAAM,CAACC,iBAAiB;EAC3C,MAAMC,MAAM,GAAGX,eAAe,CAACI,KAAK,EAAE,OAAO,EAAE,wBAAwB,CAAC;EACxE,MAAMQ,OAAO,GAAGZ,eAAe,CAACK,MAAM,EAAE,QAAQ,EAAE,wBAAwB,CAAC;EAE3E,MAAMQ,MAAM,GAAGZ,qBAAqB,CAChCU,MAAM,EAAEC,OAAO,EAAEN,aAAa,EAAEC,YAAY,EAAEC,cAAc,CAAC;EACjEF,aAAa,GAAGO,MAAM,CAACP,aAAa;EACpCC,YAAY,GAAGM,MAAM,CAACN,YAAY;EAClCC,cAAc,GAAGK,MAAM,CAACL,cAAc;EAEtC,MAAMM,cAAc,GAAG,MAAMC,OAAO,CAACC,GAAG,CAAC,CAACL,MAAM,CAACM,IAAI,EAAE,EAAEL,OAAO,CAACK,IAAI,EAAE,CAAC,CAAC;EACzE,MAAMC,SAAS,GAAGJ,cAAc,CAAC,CAAC,CAAC;EACnC,MAAMK,UAAU,GAAGL,cAAc,CAAC,CAAC,CAAC;EAEpC;EACA;EACA;EACA,MAAM;IAACM;EAAe,CAAC,GAAGrB,uBAAuB,CAC7CmB,SAAS,EAAEC,UAAU,EAAEb,aAAa,EAAEC,YAAY,EAAEC,cAAc,CAAC;EACvE,IAAIG,MAAM,KAAKP,KAAK,EAAE;IACpBO,MAAM,CAACU,OAAO,EAAE;;EAElB,IAAIT,OAAO,KAAKP,MAAM,EAAE;IACtBO,OAAO,CAACS,OAAO,EAAE;;EAGnB,OAAOnB,QAAQ,CAACkB,eAAe,EAAE,OAAO,CAAC;AAC3C;AAEA,OAAO,MAAME,sBAAsB,GAAGnB,uBAAuB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}