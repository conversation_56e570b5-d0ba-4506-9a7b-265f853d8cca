{"ast": null, "code": "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { UnaryOpProgram } from '../unaryop_gpu';\nconst TO_INT = `return float(int(x));`;\nexport function int(input, backend) {\n  const program = new UnaryOpProgram(input.shape, TO_INT);\n  const output = backend.runWebGLProgram(program, [input], 'int32');\n  return {\n    dataId: output.dataId,\n    shape: output.shape,\n    dtype: output.dtype\n  };\n}", "map": {"version": 3, "names": ["UnaryOpProgram", "TO_INT", "int", "input", "backend", "program", "shape", "output", "runWebGLProgram", "dataId", "dtype"], "sources": ["C:\\tfjs-backend-webgl\\src\\kernel_utils\\int.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {TensorInfo} from '@tensorflow/tfjs-core';\n\nimport {MathBackendWebGL} from '../backend_webgl';\nimport {UnaryOpProgram} from '../unaryop_gpu';\n\nconst TO_INT = `return float(int(x));`;\n\nexport function int(input: TensorInfo, backend: MathBackendWebGL): TensorInfo {\n  const program = new UnaryOpProgram(input.shape, TO_INT);\n  const output = backend.runWebGLProgram(program, [input], 'int32');\n  return {dataId: output.dataId, shape: output.shape, dtype: output.dtype};\n}\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAoBA,SAAQA,cAAc,QAAO,gBAAgB;AAE7C,MAAMC,MAAM,GAAG,uBAAuB;AAEtC,OAAM,SAAUC,GAAGA,CAACC,KAAiB,EAAEC,OAAyB;EAC9D,MAAMC,OAAO,GAAG,IAAIL,cAAc,CAACG,KAAK,CAACG,KAAK,EAAEL,MAAM,CAAC;EACvD,MAAMM,MAAM,GAAGH,OAAO,CAACI,eAAe,CAACH,OAAO,EAAE,CAACF,KAAK,CAAC,EAAE,OAAO,CAAC;EACjE,OAAO;IAACM,MAAM,EAAEF,MAAM,CAACE,MAAM;IAAEH,KAAK,EAAEC,MAAM,CAACD,KAAK;IAAEI,KAAK,EAAEH,MAAM,CAACG;EAAK,CAAC;AAC1E", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}