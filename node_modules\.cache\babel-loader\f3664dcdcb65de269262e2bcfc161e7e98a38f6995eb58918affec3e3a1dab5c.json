{"ast": null, "code": "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { ENGINE } from '../../engine';\nimport { assert } from '../../util';\nimport { div } from '../div';\nimport { mul } from '../mul';\nimport { norm } from '../norm';\nimport { op } from '../operation';\nimport { split } from '../split';\nimport { squeeze } from '../squeeze';\nimport { stack } from '../stack';\nimport { sub } from '../sub';\nimport { sum } from '../sum';\n/**\n * Gram-Schmidt orthogonalization.\n *\n * ```js\n * const x = tf.tensor2d([[1, 2], [3, 4]]);\n * let y = tf.linalg.gramSchmidt(x);\n * y.print();\n * console.log('Orthogonalized:');\n * y.dot(y.transpose()).print();  // should be nearly the identity matrix.\n * console.log('First row direction maintained:');\n * const data = await y.array();\n * console.log(data[0][1] / data[0][0]);  // should be nearly 2.\n * ```\n *\n * @param xs The vectors to be orthogonalized, in one of the two following\n *   formats:\n *   - An Array of `tf.Tensor1D`.\n *   - A `tf.Tensor2D`, i.e., a matrix, in which case the vectors are the rows\n *     of `xs`.\n *   In each case, all the vectors must have the same length and the length\n *   must be greater than or equal to the number of vectors.\n * @returns The orthogonalized and normalized vectors or matrix.\n *   Orthogonalization means that the vectors or the rows of the matrix\n *   are orthogonal (zero inner products). Normalization means that each\n *   vector or each row of the matrix has an L2 norm that equals `1`.\n *\n * @doc {heading:'Operations', subheading:'Linear Algebra', namespace:'linalg'}\n */\nfunction gramSchmidt_(xs) {\n  let inputIsTensor2D;\n  if (Array.isArray(xs)) {\n    inputIsTensor2D = false;\n    assert(xs != null && xs.length > 0, () => 'Gram-Schmidt process: input must not be null, undefined, or ' + 'empty');\n    const dim = xs[0].shape[0];\n    for (let i = 1; i < xs.length; ++i) {\n      assert(xs[i].shape[0] === dim, () => 'Gram-Schmidt: Non-unique lengths found in the input vectors: ' + \"(\".concat(xs[i].shape[0], \" vs. \").concat(dim, \")\"));\n    }\n  } else {\n    inputIsTensor2D = true;\n    xs = split(xs, xs.shape[0], 0).map(x => squeeze(x, [0]));\n  }\n  assert(xs.length <= xs[0].shape[0], () => \"Gram-Schmidt: Number of vectors (\".concat(xs.length, \") exceeds \") + \"number of dimensions (\".concat(xs[0].shape[0], \").\"));\n  const ys = [];\n  const xs1d = xs;\n  for (let i = 0; i < xs.length; ++i) {\n    ys.push(ENGINE.tidy(() => {\n      let x = xs1d[i];\n      if (i > 0) {\n        for (let j = 0; j < i; ++j) {\n          const proj = mul(sum(mul(ys[j], x)), ys[j]);\n          x = sub(x, proj);\n        }\n      }\n      return div(x, norm(x, 'euclidean'));\n    }));\n  }\n  if (inputIsTensor2D) {\n    return stack(ys, 0);\n  } else {\n    return ys;\n  }\n}\nexport const gramSchmidt = /* @__PURE__ */op({\n  gramSchmidt_\n});", "map": {"version": 3, "names": ["ENGINE", "assert", "div", "mul", "norm", "op", "split", "squeeze", "stack", "sub", "sum", "gramSchmidt_", "xs", "inputIsTensor2D", "Array", "isArray", "length", "dim", "shape", "i", "concat", "map", "x", "ys", "xs1d", "push", "tidy", "j", "proj", "gramSchmidt"], "sources": ["C:\\tfjs-core\\src\\ops\\linalg\\gram_schmidt.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {ENGINE} from '../../engine';\nimport {Tensor1D, Tensor2D} from '../../tensor';\nimport {assert} from '../../util';\n\nimport {div} from '../div';\nimport {mul} from '../mul';\nimport {norm} from '../norm';\nimport {op} from '../operation';\nimport {split} from '../split';\nimport {squeeze} from '../squeeze';\nimport {stack} from '../stack';\nimport {sub} from '../sub';\nimport {sum} from '../sum';\n\n/**\n * Gram-Schmidt orthogonalization.\n *\n * ```js\n * const x = tf.tensor2d([[1, 2], [3, 4]]);\n * let y = tf.linalg.gramSchmidt(x);\n * y.print();\n * console.log('Orthogonalized:');\n * y.dot(y.transpose()).print();  // should be nearly the identity matrix.\n * console.log('First row direction maintained:');\n * const data = await y.array();\n * console.log(data[0][1] / data[0][0]);  // should be nearly 2.\n * ```\n *\n * @param xs The vectors to be orthogonalized, in one of the two following\n *   formats:\n *   - An Array of `tf.Tensor1D`.\n *   - A `tf.Tensor2D`, i.e., a matrix, in which case the vectors are the rows\n *     of `xs`.\n *   In each case, all the vectors must have the same length and the length\n *   must be greater than or equal to the number of vectors.\n * @returns The orthogonalized and normalized vectors or matrix.\n *   Orthogonalization means that the vectors or the rows of the matrix\n *   are orthogonal (zero inner products). Normalization means that each\n *   vector or each row of the matrix has an L2 norm that equals `1`.\n *\n * @doc {heading:'Operations', subheading:'Linear Algebra', namespace:'linalg'}\n */\nfunction gramSchmidt_(xs: Tensor1D[]|Tensor2D): Tensor1D[]|Tensor2D {\n  let inputIsTensor2D: boolean;\n  if (Array.isArray(xs)) {\n    inputIsTensor2D = false;\n    assert(\n        xs != null && xs.length > 0,\n        () => 'Gram-Schmidt process: input must not be null, undefined, or ' +\n            'empty');\n    const dim = xs[0].shape[0];\n    for (let i = 1; i < xs.length; ++i) {\n      assert(\n          xs[i].shape[0] === dim,\n          () =>\n              'Gram-Schmidt: Non-unique lengths found in the input vectors: ' +\n              `(${(xs as Tensor1D[])[i].shape[0]} vs. ${dim})`);\n    }\n  } else {\n    inputIsTensor2D = true;\n    xs = split(xs, xs.shape[0], 0).map(x => squeeze(x, [0]));\n  }\n\n  assert(\n      xs.length <= xs[0].shape[0],\n      () => `Gram-Schmidt: Number of vectors (${\n                (xs as Tensor1D[]).length}) exceeds ` +\n          `number of dimensions (${(xs as Tensor1D[])[0].shape[0]}).`);\n\n  const ys: Tensor1D[] = [];\n  const xs1d = xs;\n  for (let i = 0; i < xs.length; ++i) {\n    ys.push(ENGINE.tidy(() => {\n      let x = xs1d[i];\n      if (i > 0) {\n        for (let j = 0; j < i; ++j) {\n          const proj = mul(sum(mul(ys[j], x)), ys[j]);\n          x = sub(x, proj);\n        }\n      }\n      return div(x, norm(x, 'euclidean'));\n    }));\n  }\n\n  if (inputIsTensor2D) {\n    return stack(ys, 0) as Tensor2D;\n  } else {\n    return ys;\n  }\n}\n\nexport const gramSchmidt = /* @__PURE__ */ op({gramSchmidt_});\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAiBA,SAAQA,MAAM,QAAO,cAAc;AAEnC,SAAQC,MAAM,QAAO,YAAY;AAEjC,SAAQC,GAAG,QAAO,QAAQ;AAC1B,SAAQC,GAAG,QAAO,QAAQ;AAC1B,SAAQC,IAAI,QAAO,SAAS;AAC5B,SAAQC,EAAE,QAAO,cAAc;AAC/B,SAAQC,KAAK,QAAO,UAAU;AAC9B,SAAQC,OAAO,QAAO,YAAY;AAClC,SAAQC,KAAK,QAAO,UAAU;AAC9B,SAAQC,GAAG,QAAO,QAAQ;AAC1B,SAAQC,GAAG,QAAO,QAAQ;AAE1B;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4BA,SAASC,YAAYA,CAACC,EAAuB;EAC3C,IAAIC,eAAwB;EAC5B,IAAIC,KAAK,CAACC,OAAO,CAACH,EAAE,CAAC,EAAE;IACrBC,eAAe,GAAG,KAAK;IACvBZ,MAAM,CACFW,EAAE,IAAI,IAAI,IAAIA,EAAE,CAACI,MAAM,GAAG,CAAC,EAC3B,MAAM,8DAA8D,GAChE,OAAO,CAAC;IAChB,MAAMC,GAAG,GAAGL,EAAE,CAAC,CAAC,CAAC,CAACM,KAAK,CAAC,CAAC,CAAC;IAC1B,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGP,EAAE,CAACI,MAAM,EAAE,EAAEG,CAAC,EAAE;MAClClB,MAAM,CACFW,EAAE,CAACO,CAAC,CAAC,CAACD,KAAK,CAAC,CAAC,CAAC,KAAKD,GAAG,EACtB,MACI,+DAA+D,OAAAG,MAAA,CAC1DR,EAAiB,CAACO,CAAC,CAAC,CAACD,KAAK,CAAC,CAAC,CAAC,WAAAE,MAAA,CAAQH,GAAG,MAAG,CAAC;;GAE5D,MAAM;IACLJ,eAAe,GAAG,IAAI;IACtBD,EAAE,GAAGN,KAAK,CAACM,EAAE,EAAEA,EAAE,CAACM,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAACG,GAAG,CAACC,CAAC,IAAIf,OAAO,CAACe,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;;EAG1DrB,MAAM,CACFW,EAAE,CAACI,MAAM,IAAIJ,EAAE,CAAC,CAAC,CAAC,CAACM,KAAK,CAAC,CAAC,CAAC,EAC3B,MAAM,oCAAAE,MAAA,CACKR,EAAiB,CAACI,MAAM,2CAAAI,MAAA,CACLR,EAAiB,CAAC,CAAC,CAAC,CAACM,KAAK,CAAC,CAAC,CAAC,OAAI,CAAC;EAEpE,MAAMK,EAAE,GAAe,EAAE;EACzB,MAAMC,IAAI,GAAGZ,EAAE;EACf,KAAK,IAAIO,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGP,EAAE,CAACI,MAAM,EAAE,EAAEG,CAAC,EAAE;IAClCI,EAAE,CAACE,IAAI,CAACzB,MAAM,CAAC0B,IAAI,CAAC,MAAK;MACvB,IAAIJ,CAAC,GAAGE,IAAI,CAACL,CAAC,CAAC;MACf,IAAIA,CAAC,GAAG,CAAC,EAAE;QACT,KAAK,IAAIQ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGR,CAAC,EAAE,EAAEQ,CAAC,EAAE;UAC1B,MAAMC,IAAI,GAAGzB,GAAG,CAACO,GAAG,CAACP,GAAG,CAACoB,EAAE,CAACI,CAAC,CAAC,EAAEL,CAAC,CAAC,CAAC,EAAEC,EAAE,CAACI,CAAC,CAAC,CAAC;UAC3CL,CAAC,GAAGb,GAAG,CAACa,CAAC,EAAEM,IAAI,CAAC;;;MAGpB,OAAO1B,GAAG,CAACoB,CAAC,EAAElB,IAAI,CAACkB,CAAC,EAAE,WAAW,CAAC,CAAC;IACrC,CAAC,CAAC,CAAC;;EAGL,IAAIT,eAAe,EAAE;IACnB,OAAOL,KAAK,CAACe,EAAE,EAAE,CAAC,CAAa;GAChC,MAAM;IACL,OAAOA,EAAE;;AAEb;AAEA,OAAO,MAAMM,WAAW,GAAG,eAAgBxB,EAAE,CAAC;EAACM;AAAY,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}