{"ast": null, "code": "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { ENGINE } from '../engine';\nimport { Mod } from '../kernel_names';\nimport { makeTypesMatch } from '../tensor_util';\nimport { convertToTensor } from '../tensor_util_env';\nimport { op } from './operation';\n/**\n * Returns the mod of a and b element-wise.\n * `floor(x / y) * y + mod(x, y) = x`\n * Supports broadcasting.\n *\n * We also expose `tf.modStrict` which has the same signature as this op and\n * asserts that `a` and `b` are the same shape (does not broadcast).\n *\n * ```js\n * const a = tf.tensor1d([1, 4, 3, 16]);\n * const b = tf.tensor1d([1, 2, 9, 4]);\n *\n * a.mod(b).print();  // or tf.mod(a, b)\n * ```\n *\n * ```js\n * // Broadcast a mod b.\n * const a = tf.tensor1d([2, 4, 6, 8]);\n * const b = tf.scalar(5);\n *\n * a.mod(b).print();  // or tf.mod(a, b)\n * ```\n *\n * @param a The first tensor.\n * @param b The second tensor. Must have the same type as `a`.\n *\n * @doc {heading: 'Operations', subheading: 'Arithmetic'}\n */\nfunction mod_(a, b) {\n  let $a = convertToTensor(a, 'a', 'mod');\n  let $b = convertToTensor(b, 'b', 'mod');\n  [$a, $b] = makeTypesMatch($a, $b);\n  const inputs = {\n    a: $a,\n    b: $b\n  };\n  return ENGINE.runKernel(Mod, inputs);\n}\nexport const mod = /* @__PURE__ */op({\n  mod_\n});", "map": {"version": 3, "names": ["ENGINE", "Mod", "makeTypesMatch", "convertToTensor", "op", "mod_", "a", "b", "$a", "$b", "inputs", "runKernel", "mod"], "sources": ["C:\\tfjs-core\\src\\ops\\mod.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {ENGINE} from '../engine';\nimport {Mod, ModInputs} from '../kernel_names';\nimport {Tensor} from '../tensor';\nimport {NamedTensorMap} from '../tensor_types';\nimport {makeTypesMatch} from '../tensor_util';\nimport {convertToTensor} from '../tensor_util_env';\nimport {TensorLike} from '../types';\n\nimport {op} from './operation';\n\n/**\n * Returns the mod of a and b element-wise.\n * `floor(x / y) * y + mod(x, y) = x`\n * Supports broadcasting.\n *\n * We also expose `tf.modStrict` which has the same signature as this op and\n * asserts that `a` and `b` are the same shape (does not broadcast).\n *\n * ```js\n * const a = tf.tensor1d([1, 4, 3, 16]);\n * const b = tf.tensor1d([1, 2, 9, 4]);\n *\n * a.mod(b).print();  // or tf.mod(a, b)\n * ```\n *\n * ```js\n * // Broadcast a mod b.\n * const a = tf.tensor1d([2, 4, 6, 8]);\n * const b = tf.scalar(5);\n *\n * a.mod(b).print();  // or tf.mod(a, b)\n * ```\n *\n * @param a The first tensor.\n * @param b The second tensor. Must have the same type as `a`.\n *\n * @doc {heading: 'Operations', subheading: 'Arithmetic'}\n */\nfunction mod_<T extends Tensor>(a: Tensor|TensorLike, b: Tensor|TensorLike): T {\n  let $a = convertToTensor(a, 'a', 'mod');\n  let $b = convertToTensor(b, 'b', 'mod');\n  [$a, $b] = makeTypesMatch($a, $b);\n\n  const inputs: ModInputs = {a: $a, b: $b};\n\n  return ENGINE.runKernel(Mod, inputs as unknown as NamedTensorMap);\n}\n\nexport const mod = /* @__PURE__ */ op({mod_});\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAiBA,SAAQA,MAAM,QAAO,WAAW;AAChC,SAAQC,GAAG,QAAkB,iBAAiB;AAG9C,SAAQC,cAAc,QAAO,gBAAgB;AAC7C,SAAQC,eAAe,QAAO,oBAAoB;AAGlD,SAAQC,EAAE,QAAO,aAAa;AAE9B;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4BA,SAASC,IAAIA,CAAmBC,CAAoB,EAAEC,CAAoB;EACxE,IAAIC,EAAE,GAAGL,eAAe,CAACG,CAAC,EAAE,GAAG,EAAE,KAAK,CAAC;EACvC,IAAIG,EAAE,GAAGN,eAAe,CAACI,CAAC,EAAE,GAAG,EAAE,KAAK,CAAC;EACvC,CAACC,EAAE,EAAEC,EAAE,CAAC,GAAGP,cAAc,CAACM,EAAE,EAAEC,EAAE,CAAC;EAEjC,MAAMC,MAAM,GAAc;IAACJ,CAAC,EAAEE,EAAE;IAAED,CAAC,EAAEE;EAAE,CAAC;EAExC,OAAOT,MAAM,CAACW,SAAS,CAACV,GAAG,EAAES,MAAmC,CAAC;AACnE;AAEA,OAAO,MAAME,GAAG,GAAG,eAAgBR,EAAE,CAAC;EAACC;AAAI,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}