{"ast": null, "code": "/**\n * @license\n * Copyright 2018 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { ENGINE } from '../engine';\nimport { dispose, tidy } from '../globals';\nimport { add } from '../ops/add';\nimport { div } from '../ops/div';\nimport { fill } from '../ops/fill';\nimport { mul } from '../ops/mul';\nimport { sqrt } from '../ops/sqrt';\nimport { square } from '../ops/square';\nimport { Optimizer } from './optimizer';\n/** @doclink Optimizer */\nexport class AdagradOptimizer extends Optimizer {\n  /** @nocollapse */\n  static get className() {\n    // Name matters for Python compatibility.\n    // This is a getter instead of a property because when it's a property, it\n    // prevents the entire class from being tree-shaken.\n    return 'Adagrad';\n  }\n  constructor(learningRate, initialAccumulatorValue = 0.1) {\n    super();\n    this.learningRate = learningRate;\n    this.initialAccumulatorValue = initialAccumulatorValue;\n    this.accumulatedGrads = [];\n  }\n  applyGradients(variableGradients) {\n    const variableNames = Array.isArray(variableGradients) ? variableGradients.map(item => item.name) : Object.keys(variableGradients);\n    variableNames.forEach((name, i) => {\n      const value = ENGINE.registeredVariables[name];\n      if (this.accumulatedGrads[i] == null) {\n        const trainable = false;\n        this.accumulatedGrads[i] = {\n          originalName: `${name}/accumulator`,\n          variable: tidy(() => fill(value.shape, this.initialAccumulatorValue).variable(trainable))\n        };\n      }\n      const gradient = Array.isArray(variableGradients) ? variableGradients[i].tensor : variableGradients[name];\n      if (gradient == null) {\n        return;\n      }\n      const accumulatedGrad = this.accumulatedGrads[i].variable;\n      tidy(() => {\n        const newAccumulatedGrad = add(accumulatedGrad, square(gradient));\n        accumulatedGrad.assign(newAccumulatedGrad);\n        const newValue = add(mul(div(gradient, sqrt(add(newAccumulatedGrad, ENGINE.backend.epsilon()))), -this.learningRate), value);\n        value.assign(newValue);\n      });\n    });\n    this.incrementIterations();\n  }\n  dispose() {\n    if (this.accumulatedGrads != null) {\n      dispose(this.accumulatedGrads.map(v => v.variable));\n    }\n  }\n  async getWeights() {\n    // Order matters for Python compatibility.\n    return [await this.saveIterations()].concat(this.accumulatedGrads.map(v => ({\n      name: v.originalName,\n      tensor: v.variable\n    })));\n  }\n  async setWeights(weightValues) {\n    weightValues = await this.extractIterations(weightValues);\n    const trainable = false;\n    this.accumulatedGrads = weightValues.map(v => ({\n      originalName: v.name,\n      variable: v.tensor.variable(trainable)\n    }));\n  }\n  getConfig() {\n    return {\n      'learningRate': this.learningRate,\n      'initialAccumulatorValue': this.initialAccumulatorValue\n    };\n  }\n  /** @nocollapse */\n  static fromConfig(cls, config) {\n    return new cls(config['learningRate'], config['initialAccumulatorValue']);\n  }\n}", "map": {"version": 3, "names": ["ENGINE", "dispose", "tidy", "add", "div", "fill", "mul", "sqrt", "square", "Optimizer", "AdagradOptimizer", "className", "constructor", "learningRate", "initialAccumulatorValue", "accumulatedGrads", "applyGradients", "variableGradients", "variableNames", "Array", "isArray", "map", "item", "name", "Object", "keys", "for<PERSON>ach", "i", "value", "registeredVariables", "trainable", "originalName", "variable", "shape", "gradient", "tensor", "accumulatedGrad", "newAccumulatedGrad", "assign", "newValue", "backend", "epsilon", "incrementIterations", "v", "getWeights", "saveIterations", "concat", "setWeights", "weightValues", "extractIterations", "getConfig", "fromConfig", "cls", "config"], "sources": ["C:\\tfjs-core\\src\\optimizers\\adagrad_optimizer.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2018 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {ENGINE} from '../engine';\nimport {dispose, tidy} from '../globals';\nimport {add} from '../ops/add';\nimport {div} from '../ops/div';\nimport {fill} from '../ops/fill';\nimport {mul} from '../ops/mul';\nimport {sqrt} from '../ops/sqrt';\nimport {square} from '../ops/square';\nimport {ConfigDict, Serializable, SerializableConstructor} from '../serialization';\nimport {NamedTensor, NamedVariableMap} from '../tensor_types';\n\nimport {Optimizer, OptimizerVariable} from './optimizer';\n\n/** @doclink Optimizer */\nexport class AdagradOptimizer extends Optimizer {\n  /** @nocollapse */\n  static get className() {\n    // Name matters for Python compatibility.\n    // This is a getter instead of a property because when it's a property, it\n    // prevents the entire class from being tree-shaken.\n    return 'Adagrad';\n  }\n\n  private accumulatedGrads: OptimizerVariable[] = [];\n\n  constructor(\n      protected learningRate: number, private initialAccumulatorValue = 0.1) {\n    super();\n  }\n\n  applyGradients(variableGradients: NamedVariableMap|NamedTensor[]) {\n    const variableNames = Array.isArray(variableGradients) ?\n        variableGradients.map(item => item.name) :\n        Object.keys(variableGradients);\n\n    variableNames.forEach((name, i) => {\n      const value = ENGINE.registeredVariables[name];\n      if (this.accumulatedGrads[i] == null) {\n        const trainable = false;\n        this.accumulatedGrads[i] = {\n          originalName: `${name}/accumulator`,\n          variable: tidy(\n              () => fill(value.shape, this.initialAccumulatorValue)\n                        .variable(trainable))\n        };\n      }\n\n      const gradient = Array.isArray(variableGradients) ?\n          variableGradients[i].tensor :\n          variableGradients[name];\n      if (gradient == null) {\n        return;\n      }\n\n      const accumulatedGrad = this.accumulatedGrads[i].variable;\n\n      tidy(() => {\n        const newAccumulatedGrad = add(accumulatedGrad, square(gradient));\n        accumulatedGrad.assign(newAccumulatedGrad);\n\n        const newValue = add(\n            mul(div(gradient,\n                    sqrt(add(newAccumulatedGrad, ENGINE.backend.epsilon()))),\n                -this.learningRate),\n            value);\n        value.assign(newValue);\n      });\n    });\n    this.incrementIterations();\n  }\n\n  override dispose(): void {\n    if (this.accumulatedGrads != null) {\n      dispose(this.accumulatedGrads.map(v => v.variable));\n    }\n  }\n\n  override async getWeights(): Promise<NamedTensor[]> {\n    // Order matters for Python compatibility.\n    return [await this.saveIterations()].concat(this.accumulatedGrads.map(\n        v => ({name: v.originalName, tensor: v.variable})));\n  }\n\n  override async setWeights(weightValues: NamedTensor[]): Promise<void> {\n    weightValues = await this.extractIterations(weightValues);\n    const trainable = false;\n    this.accumulatedGrads = weightValues.map(\n        v => ({originalName: v.name, variable: v.tensor.variable(trainable)}));\n  }\n\n  getConfig(): ConfigDict {\n    return {\n      'learningRate': this.learningRate,\n      'initialAccumulatorValue': this.initialAccumulatorValue,\n    };\n  }\n\n  /** @nocollapse */\n  static override fromConfig<T extends Serializable>(\n      cls: SerializableConstructor<T>, config: ConfigDict): T {\n    return new cls(config['learningRate'], config['initialAccumulatorValue']);\n  }\n}\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAiBA,SAAQA,MAAM,QAAO,WAAW;AAChC,SAAQC,OAAO,EAAEC,IAAI,QAAO,YAAY;AACxC,SAAQC,GAAG,QAAO,YAAY;AAC9B,SAAQC,GAAG,QAAO,YAAY;AAC9B,SAAQC,IAAI,QAAO,aAAa;AAChC,SAAQC,GAAG,QAAO,YAAY;AAC9B,SAAQC,IAAI,QAAO,aAAa;AAChC,SAAQC,MAAM,QAAO,eAAe;AAIpC,SAAQC,SAAS,QAA0B,aAAa;AAExD;AACA,OAAM,MAAOC,gBAAiB,SAAQD,SAAS;EAC7C;EACA,WAAWE,SAASA,CAAA;IAClB;IACA;IACA;IACA,OAAO,SAAS;EAClB;EAIAC,YACcC,YAAoB,EAAUC,uBAAA,GAA0B,GAAG;IACvE,KAAK,EAAE;IADK,KAAAD,YAAY,GAAZA,YAAY;IAAkB,KAAAC,uBAAuB,GAAvBA,uBAAuB;IAH3D,KAAAC,gBAAgB,GAAwB,EAAE;EAKlD;EAEAC,cAAcA,CAACC,iBAAiD;IAC9D,MAAMC,aAAa,GAAGC,KAAK,CAACC,OAAO,CAACH,iBAAiB,CAAC,GAClDA,iBAAiB,CAACI,GAAG,CAACC,IAAI,IAAIA,IAAI,CAACC,IAAI,CAAC,GACxCC,MAAM,CAACC,IAAI,CAACR,iBAAiB,CAAC;IAElCC,aAAa,CAACQ,OAAO,CAAC,CAACH,IAAI,EAAEI,CAAC,KAAI;MAChC,MAAMC,KAAK,GAAG5B,MAAM,CAAC6B,mBAAmB,CAACN,IAAI,CAAC;MAC9C,IAAI,IAAI,CAACR,gBAAgB,CAACY,CAAC,CAAC,IAAI,IAAI,EAAE;QACpC,MAAMG,SAAS,GAAG,KAAK;QACvB,IAAI,CAACf,gBAAgB,CAACY,CAAC,CAAC,GAAG;UACzBI,YAAY,EAAE,GAAGR,IAAI,cAAc;UACnCS,QAAQ,EAAE9B,IAAI,CACV,MAAMG,IAAI,CAACuB,KAAK,CAACK,KAAK,EAAE,IAAI,CAACnB,uBAAuB,CAAC,CAC1CkB,QAAQ,CAACF,SAAS,CAAC;SACnC;;MAGH,MAAMI,QAAQ,GAAGf,KAAK,CAACC,OAAO,CAACH,iBAAiB,CAAC,GAC7CA,iBAAiB,CAACU,CAAC,CAAC,CAACQ,MAAM,GAC3BlB,iBAAiB,CAACM,IAAI,CAAC;MAC3B,IAAIW,QAAQ,IAAI,IAAI,EAAE;QACpB;;MAGF,MAAME,eAAe,GAAG,IAAI,CAACrB,gBAAgB,CAACY,CAAC,CAAC,CAACK,QAAQ;MAEzD9B,IAAI,CAAC,MAAK;QACR,MAAMmC,kBAAkB,GAAGlC,GAAG,CAACiC,eAAe,EAAE5B,MAAM,CAAC0B,QAAQ,CAAC,CAAC;QACjEE,eAAe,CAACE,MAAM,CAACD,kBAAkB,CAAC;QAE1C,MAAME,QAAQ,GAAGpC,GAAG,CAChBG,GAAG,CAACF,GAAG,CAAC8B,QAAQ,EACR3B,IAAI,CAACJ,GAAG,CAACkC,kBAAkB,EAAErC,MAAM,CAACwC,OAAO,CAACC,OAAO,EAAE,CAAC,CAAC,CAAC,EAC5D,CAAC,IAAI,CAAC5B,YAAY,CAAC,EACvBe,KAAK,CAAC;QACVA,KAAK,CAACU,MAAM,CAACC,QAAQ,CAAC;MACxB,CAAC,CAAC;IACJ,CAAC,CAAC;IACF,IAAI,CAACG,mBAAmB,EAAE;EAC5B;EAESzC,OAAOA,CAAA;IACd,IAAI,IAAI,CAACc,gBAAgB,IAAI,IAAI,EAAE;MACjCd,OAAO,CAAC,IAAI,CAACc,gBAAgB,CAACM,GAAG,CAACsB,CAAC,IAAIA,CAAC,CAACX,QAAQ,CAAC,CAAC;;EAEvD;EAES,MAAMY,UAAUA,CAAA;IACvB;IACA,OAAO,CAAC,MAAM,IAAI,CAACC,cAAc,EAAE,CAAC,CAACC,MAAM,CAAC,IAAI,CAAC/B,gBAAgB,CAACM,GAAG,CACjEsB,CAAC,KAAK;MAACpB,IAAI,EAAEoB,CAAC,CAACZ,YAAY;MAAEI,MAAM,EAAEQ,CAAC,CAACX;IAAQ,CAAC,CAAC,CAAC,CAAC;EACzD;EAES,MAAMe,UAAUA,CAACC,YAA2B;IACnDA,YAAY,GAAG,MAAM,IAAI,CAACC,iBAAiB,CAACD,YAAY,CAAC;IACzD,MAAMlB,SAAS,GAAG,KAAK;IACvB,IAAI,CAACf,gBAAgB,GAAGiC,YAAY,CAAC3B,GAAG,CACpCsB,CAAC,KAAK;MAACZ,YAAY,EAAEY,CAAC,CAACpB,IAAI;MAAES,QAAQ,EAAEW,CAAC,CAACR,MAAM,CAACH,QAAQ,CAACF,SAAS;IAAC,CAAC,CAAC,CAAC;EAC5E;EAEAoB,SAASA,CAAA;IACP,OAAO;MACL,cAAc,EAAE,IAAI,CAACrC,YAAY;MACjC,yBAAyB,EAAE,IAAI,CAACC;KACjC;EACH;EAEA;EACA,OAAgBqC,UAAUA,CACtBC,GAA+B,EAAEC,MAAkB;IACrD,OAAO,IAAID,GAAG,CAACC,MAAM,CAAC,cAAc,CAAC,EAAEA,MAAM,CAAC,yBAAyB,CAAC,CAAC;EAC3E", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}