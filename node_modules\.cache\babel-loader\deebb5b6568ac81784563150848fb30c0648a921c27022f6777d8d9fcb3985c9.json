{"ast": null, "code": "/**\n * @license\n * Copyright 2019 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { env } from '@tensorflow/tfjs-core';\nimport { FromPixels } from '@tensorflow/tfjs-core';\nimport { TextureUsage } from '../tex_util';\nimport { FromPixelsProgram } from './FromPixels_utils/from_pixels_gpu';\nimport { FromPixelsPackedProgram } from './FromPixels_utils/from_pixels_packed_gpu';\nexport const fromPixelsConfig = {\n  kernelName: FromPixels,\n  backendName: 'webgl',\n  kernelFunc: fromPixels\n};\nlet fromPixels2DContext;\nlet willReadFrequently = env().getBool('CANVAS2D_WILL_READ_FREQUENTLY_FOR_GPU');\nfunction fromPixels(args) {\n  const {\n    inputs,\n    backend,\n    attrs\n  } = args;\n  let {\n    pixels\n  } = inputs;\n  const {\n    numChannels\n  } = attrs;\n  const isVideo = typeof HTMLVideoElement !== 'undefined' && pixels instanceof HTMLVideoElement;\n  const isImage = typeof HTMLImageElement !== 'undefined' && pixels instanceof HTMLImageElement;\n  const [width, height] = isVideo ? [pixels.videoWidth, pixels.videoHeight] : [pixels.width, pixels.height];\n  const texShape = [height, width];\n  const outShape = [height, width, numChannels];\n  if (isImage || isVideo) {\n    const newWillReadFrequently = env().getBool('CANVAS2D_WILL_READ_FREQUENTLY_FOR_GPU');\n    if (fromPixels2DContext == null || newWillReadFrequently !== willReadFrequently) {\n      willReadFrequently = newWillReadFrequently;\n      fromPixels2DContext = document.createElement('canvas').getContext('2d', {\n        willReadFrequently\n      });\n    }\n    fromPixels2DContext.canvas.width = width;\n    fromPixels2DContext.canvas.height = height;\n    fromPixels2DContext.drawImage(pixels, 0, 0, width, height);\n    pixels = fromPixels2DContext.canvas;\n  }\n  const tempPixelHandle = backend.makeTensorInfo(texShape, 'int32');\n  // This is a byte texture with pixels.\n  backend.texData.get(tempPixelHandle.dataId).usage = TextureUsage.PIXELS;\n  backend.gpgpu.uploadPixelDataToTexture(backend.getTexture(tempPixelHandle.dataId), pixels);\n  const program = env().getBool('WEBGL_PACK') ? new FromPixelsPackedProgram(outShape) : new FromPixelsProgram(outShape);\n  const res = backend.runWebGLProgram(program, [tempPixelHandle], 'int32');\n  backend.disposeData(tempPixelHandle.dataId);\n  return res;\n}", "map": {"version": 3, "names": ["env", "FromPixels", "TextureUsage", "FromPixelsProgram", "FromPixelsPackedProgram", "fromPixelsConfig", "kernelName", "backendName", "kernelFunc", "fromPixels", "fromPixels2DContext", "willReadFrequently", "getBool", "args", "inputs", "backend", "attrs", "pixels", "numChannels", "isVideo", "HTMLVideoElement", "isImage", "HTMLImageElement", "width", "height", "videoWidth", "videoHeight", "texShape", "outShape", "newWillReadFrequently", "document", "createElement", "getContext", "canvas", "drawImage", "tempPixelHandle", "makeTensorInfo", "texData", "get", "dataId", "usage", "PIXELS", "gpgpu", "uploadPixelDataToTexture", "getTexture", "program", "res", "runWebGLProgram", "disposeData"], "sources": ["C:\\tfjs-backend-webgl\\src\\kernels\\FromPixels.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2019 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {env, KernelConfig, KernelFunc, TensorInfo} from '@tensorflow/tfjs-core';\nimport {FromPixels, FromPixelsAttrs, FromPixelsInputs} from '@tensorflow/tfjs-core';\n\nimport {MathBackendWebGL} from '../backend_webgl';\nimport {TextureUsage} from '../tex_util';\n\nimport {FromPixelsProgram} from './FromPixels_utils/from_pixels_gpu';\nimport {FromPixelsPackedProgram} from './FromPixels_utils/from_pixels_packed_gpu';\n\nexport const fromPixelsConfig: KernelConfig = {\n  kernelName: FromPixels,\n  backendName: 'webgl',\n  kernelFunc: fromPixels as unknown as KernelFunc,\n};\n\nlet fromPixels2DContext: CanvasRenderingContext2D;\nlet willReadFrequently = env().getBool('CANVAS2D_WILL_READ_FREQUENTLY_FOR_GPU');\n\nfunction fromPixels(args: {\n  inputs: FromPixelsInputs,\n  backend: MathBackendWebGL,\n  attrs: FromPixelsAttrs\n}): TensorInfo {\n  const {inputs, backend, attrs} = args;\n  let {pixels} = inputs;\n  const {numChannels} = attrs;\n\n  const isVideo = typeof (HTMLVideoElement) !== 'undefined' &&\n      pixels instanceof HTMLVideoElement;\n  const isImage = typeof (HTMLImageElement) !== 'undefined' &&\n      pixels instanceof HTMLImageElement;\n  const [width, height] = isVideo ?\n      [\n        (pixels as HTMLVideoElement).videoWidth,\n        (pixels as HTMLVideoElement).videoHeight\n      ] :\n      [pixels.width, pixels.height];\n\n  const texShape: [number, number] = [height, width];\n  const outShape = [height, width, numChannels];\n\n  if (isImage || isVideo) {\n    const newWillReadFrequently =\n        env().getBool('CANVAS2D_WILL_READ_FREQUENTLY_FOR_GPU');\n    if (fromPixels2DContext == null ||\n        newWillReadFrequently !== willReadFrequently) {\n      willReadFrequently = newWillReadFrequently;\n      fromPixels2DContext =\n          document.createElement('canvas').getContext(\n              '2d', {willReadFrequently});\n    }\n\n    fromPixels2DContext.canvas.width = width;\n    fromPixels2DContext.canvas.height = height;\n    fromPixels2DContext.drawImage(\n        pixels as HTMLVideoElement | HTMLImageElement | ImageBitmap, 0, 0,\n        width, height);\n    pixels = fromPixels2DContext.canvas;\n  }\n\n  const tempPixelHandle = backend.makeTensorInfo(texShape, 'int32');\n  // This is a byte texture with pixels.\n  backend.texData.get(tempPixelHandle.dataId).usage = TextureUsage.PIXELS;\n  backend.gpgpu.uploadPixelDataToTexture(\n      backend.getTexture(tempPixelHandle.dataId), pixels as ImageData);\n  const program = env().getBool('WEBGL_PACK') ?\n      new FromPixelsPackedProgram(outShape) :\n      new FromPixelsProgram(outShape);\n  const res = backend.runWebGLProgram(program, [tempPixelHandle], 'int32');\n  backend.disposeData(tempPixelHandle.dataId);\n  return res;\n}\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAiBA,SAAQA,GAAG,QAA6C,uBAAuB;AAC/E,SAAQC,UAAU,QAA0C,uBAAuB;AAGnF,SAAQC,YAAY,QAAO,aAAa;AAExC,SAAQC,iBAAiB,QAAO,oCAAoC;AACpE,SAAQC,uBAAuB,QAAO,2CAA2C;AAEjF,OAAO,MAAMC,gBAAgB,GAAiB;EAC5CC,UAAU,EAAEL,UAAU;EACtBM,WAAW,EAAE,OAAO;EACpBC,UAAU,EAAEC;CACb;AAED,IAAIC,mBAA6C;AACjD,IAAIC,kBAAkB,GAAGX,GAAG,EAAE,CAACY,OAAO,CAAC,uCAAuC,CAAC;AAE/E,SAASH,UAAUA,CAACI,IAInB;EACC,MAAM;IAACC,MAAM;IAAEC,OAAO;IAAEC;EAAK,CAAC,GAAGH,IAAI;EACrC,IAAI;IAACI;EAAM,CAAC,GAAGH,MAAM;EACrB,MAAM;IAACI;EAAW,CAAC,GAAGF,KAAK;EAE3B,MAAMG,OAAO,GAAG,OAAQC,gBAAiB,KAAK,WAAW,IACrDH,MAAM,YAAYG,gBAAgB;EACtC,MAAMC,OAAO,GAAG,OAAQC,gBAAiB,KAAK,WAAW,IACrDL,MAAM,YAAYK,gBAAgB;EACtC,MAAM,CAACC,KAAK,EAAEC,MAAM,CAAC,GAAGL,OAAO,GAC3B,CACGF,MAA2B,CAACQ,UAAU,EACtCR,MAA2B,CAACS,WAAW,CACzC,GACD,CAACT,MAAM,CAACM,KAAK,EAAEN,MAAM,CAACO,MAAM,CAAC;EAEjC,MAAMG,QAAQ,GAAqB,CAACH,MAAM,EAAED,KAAK,CAAC;EAClD,MAAMK,QAAQ,GAAG,CAACJ,MAAM,EAAED,KAAK,EAAEL,WAAW,CAAC;EAE7C,IAAIG,OAAO,IAAIF,OAAO,EAAE;IACtB,MAAMU,qBAAqB,GACvB7B,GAAG,EAAE,CAACY,OAAO,CAAC,uCAAuC,CAAC;IAC1D,IAAIF,mBAAmB,IAAI,IAAI,IAC3BmB,qBAAqB,KAAKlB,kBAAkB,EAAE;MAChDA,kBAAkB,GAAGkB,qBAAqB;MAC1CnB,mBAAmB,GACfoB,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC,CAACC,UAAU,CACvC,IAAI,EAAE;QAACrB;MAAkB,CAAC,CAAC;;IAGrCD,mBAAmB,CAACuB,MAAM,CAACV,KAAK,GAAGA,KAAK;IACxCb,mBAAmB,CAACuB,MAAM,CAACT,MAAM,GAAGA,MAAM;IAC1Cd,mBAAmB,CAACwB,SAAS,CACzBjB,MAA2D,EAAE,CAAC,EAAE,CAAC,EACjEM,KAAK,EAAEC,MAAM,CAAC;IAClBP,MAAM,GAAGP,mBAAmB,CAACuB,MAAM;;EAGrC,MAAME,eAAe,GAAGpB,OAAO,CAACqB,cAAc,CAACT,QAAQ,EAAE,OAAO,CAAC;EACjE;EACAZ,OAAO,CAACsB,OAAO,CAACC,GAAG,CAACH,eAAe,CAACI,MAAM,CAAC,CAACC,KAAK,GAAGtC,YAAY,CAACuC,MAAM;EACvE1B,OAAO,CAAC2B,KAAK,CAACC,wBAAwB,CAClC5B,OAAO,CAAC6B,UAAU,CAACT,eAAe,CAACI,MAAM,CAAC,EAAEtB,MAAmB,CAAC;EACpE,MAAM4B,OAAO,GAAG7C,GAAG,EAAE,CAACY,OAAO,CAAC,YAAY,CAAC,GACvC,IAAIR,uBAAuB,CAACwB,QAAQ,CAAC,GACrC,IAAIzB,iBAAiB,CAACyB,QAAQ,CAAC;EACnC,MAAMkB,GAAG,GAAG/B,OAAO,CAACgC,eAAe,CAACF,OAAO,EAAE,CAACV,eAAe,CAAC,EAAE,OAAO,CAAC;EACxEpB,OAAO,CAACiC,WAAW,CAACb,eAAe,CAACI,MAAM,CAAC;EAC3C,OAAOO,GAAG;AACZ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}