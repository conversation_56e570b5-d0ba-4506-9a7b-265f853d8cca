{"ast": null, "code": "/**\n * @license\n * Copyright 2018 Google LLC\n *\n * Use of this source code is governed by an MIT-style\n * license that can be found in the LICENSE file or at\n * https://opensource.org/licenses/MIT.\n * =============================================================================\n */\n/* Original Source layers/__init__.py */\nimport { serialization } from '@tensorflow/tfjs-core';\nimport { deserializeKerasObject } from '../utils/generic_utils';\n/**\n * Instantiate a layer from a config dictionary.\n * @param config dict of the form {class_name: str, config: dict}\n * @param customObjects dict mapping class names (or function names)\n *   of custom (non-Keras) objects to class/functions\n * @param fastWeightInit Optional flag to use fast weight initialization\n *   during deserialization. This is applicable to cases in which\n *   the initialization will be immediately overwritten by loaded weight\n *   values. Default: `false`.\n * @returns Layer instance (may be LayersModel, Sequential, Layer...)\n */\nexport function deserialize(config) {\n  let customObjects = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n  let fastWeightInit = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : false;\n  return deserializeKerasObject(config, serialization.SerializationMap.getMap().classNameMap, customObjects, 'layer', fastWeightInit);\n}", "map": {"version": 3, "names": ["serialization", "deserializeKerasObject", "deserialize", "config", "customObjects", "arguments", "length", "undefined", "fastWeightInit", "SerializationMap", "getMap", "classNameMap"], "sources": ["C:\\tfjs-layers\\src\\layers\\serialization.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2018 Google LLC\n *\n * Use of this source code is governed by an MIT-style\n * license that can be found in the LICENSE file or at\n * https://opensource.org/licenses/MIT.\n * =============================================================================\n */\n\n/* Original Source layers/__init__.py */\nimport {serialization} from '@tensorflow/tfjs-core';\n\nimport {deserializeKerasObject} from '../utils/generic_utils';\n\n/**\n * Instantiate a layer from a config dictionary.\n * @param config dict of the form {class_name: str, config: dict}\n * @param customObjects dict mapping class names (or function names)\n *   of custom (non-Keras) objects to class/functions\n * @param fastWeightInit Optional flag to use fast weight initialization\n *   during deserialization. This is applicable to cases in which\n *   the initialization will be immediately overwritten by loaded weight\n *   values. Default: `false`.\n * @returns Layer instance (may be LayersModel, Sequential, Layer...)\n */\nexport function deserialize(\n    config: serialization.ConfigDict,\n    customObjects = {} as serialization.ConfigDict,\n    fastWeightInit = false): serialization.Serializable {\n  return deserializeKerasObject(\n      config, serialization.SerializationMap.getMap().classNameMap,\n      customObjects, 'layer', fastWeightInit);\n}\n"], "mappings": "AAAA;;;;;;;;;AAUA;AACA,SAAQA,aAAa,QAAO,uBAAuB;AAEnD,SAAQC,sBAAsB,QAAO,wBAAwB;AAE7D;;;;;;;;;;;AAWA,OAAM,SAAUC,WAAWA,CACvBC,MAAgC,EAEV;EAAA,IADtBC,aAAA,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAgB,EAA8B;EAAA,IAC9CG,cAAc,GAAAH,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,KAAK;EACxB,OAAOJ,sBAAsB,CACzBE,MAAM,EAAEH,aAAa,CAACS,gBAAgB,CAACC,MAAM,EAAE,CAACC,YAAY,EAC5DP,aAAa,EAAE,OAAO,EAAEI,cAAc,CAAC;AAC7C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}