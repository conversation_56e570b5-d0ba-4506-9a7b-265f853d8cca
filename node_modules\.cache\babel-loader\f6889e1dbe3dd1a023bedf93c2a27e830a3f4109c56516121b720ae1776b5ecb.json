{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.transformArguments = exports.IS_READ_ONLY = exports.FIRST_KEY_INDEX = void 0;\nconst generic_transformers_1 = require(\"./generic-transformers\");\nexports.FIRST_KEY_INDEX = 2;\nexports.IS_READ_ONLY = true;\nfunction transformArguments(keys, options) {\n  const args = (0, generic_transformers_1.pushVerdictArgument)(['ZINTER'], keys);\n  if (options?.WEIGHTS) {\n    args.push('WEIGHTS', ...options.WEIGHTS.map(weight => weight.toString()));\n  }\n  if (options?.AGGREGATE) {\n    args.push('AGGREGATE', options.AGGREGATE);\n  }\n  return args;\n}\nexports.transformArguments = transformArguments;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "transformArguments", "IS_READ_ONLY", "FIRST_KEY_INDEX", "generic_transformers_1", "require", "keys", "options", "args", "pushVerdictArgument", "WEIGHTS", "push", "map", "weight", "toString", "AGGREGATE"], "sources": ["C:/tmsft/node_modules/@redis/client/dist/lib/commands/ZINTER.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.transformArguments = exports.IS_READ_ONLY = exports.FIRST_KEY_INDEX = void 0;\nconst generic_transformers_1 = require(\"./generic-transformers\");\nexports.FIRST_KEY_INDEX = 2;\nexports.IS_READ_ONLY = true;\nfunction transformArguments(keys, options) {\n    const args = (0, generic_transformers_1.pushVerdictArgument)(['ZINTER'], keys);\n    if (options?.WEIGHTS) {\n        args.push('WEIGHTS', ...options.WEIGHTS.map(weight => weight.toString()));\n    }\n    if (options?.AGGREGATE) {\n        args.push('AGGREGATE', options.AGGREGATE);\n    }\n    return args;\n}\nexports.transformArguments = transformArguments;\n"], "mappings": "AAAA,YAAY;;AACZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEC,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7DD,OAAO,CAACE,kBAAkB,GAAGF,OAAO,CAACG,YAAY,GAAGH,OAAO,CAACI,eAAe,GAAG,KAAK,CAAC;AACpF,MAAMC,sBAAsB,GAAGC,OAAO,CAAC,wBAAwB,CAAC;AAChEN,OAAO,CAACI,eAAe,GAAG,CAAC;AAC3BJ,OAAO,CAACG,YAAY,GAAG,IAAI;AAC3B,SAASD,kBAAkBA,CAACK,IAAI,EAAEC,OAAO,EAAE;EACvC,MAAMC,IAAI,GAAG,CAAC,CAAC,EAAEJ,sBAAsB,CAACK,mBAAmB,EAAE,CAAC,QAAQ,CAAC,EAAEH,IAAI,CAAC;EAC9E,IAAIC,OAAO,EAAEG,OAAO,EAAE;IAClBF,IAAI,CAACG,IAAI,CAAC,SAAS,EAAE,GAAGJ,OAAO,CAACG,OAAO,CAACE,GAAG,CAACC,MAAM,IAAIA,MAAM,CAACC,QAAQ,CAAC,CAAC,CAAC,CAAC;EAC7E;EACA,IAAIP,OAAO,EAAEQ,SAAS,EAAE;IACpBP,IAAI,CAACG,IAAI,CAAC,WAAW,EAAEJ,OAAO,CAACQ,SAAS,CAAC;EAC7C;EACA,OAAOP,IAAI;AACf;AACAT,OAAO,CAACE,kBAAkB,GAAGA,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}