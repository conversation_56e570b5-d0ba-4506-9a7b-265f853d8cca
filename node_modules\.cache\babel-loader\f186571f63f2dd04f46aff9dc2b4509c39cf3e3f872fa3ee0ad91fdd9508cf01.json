{"ast": null, "code": "/**\n * @license\n * Copyright 2017 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { getCoordsDataType } from './shader_compiler';\nexport class StridedSliceProgram {\n  constructor(begin, strides, size) {\n    this.variableNames = ['x'];\n    this.outputShape = size;\n    const rank = size.length;\n    const inputDtype = getCoordsDataType(size.length);\n    const dtype = getCoordsDataType(size.length);\n    let newCoords = '';\n    if (rank === 1) {\n      newCoords = 'coords * strides + begin';\n    } else {\n      let outputAxis = 0;\n      newCoords = size.map((_, i) => {\n        outputAxis++;\n        return size.length === 1 ? `coords * strides[${i}] + begin[${i}]` : `coords[${outputAxis - 1}] * strides[${i}] + begin[${i}]`;\n      }).join(',');\n    }\n    this.userCode = `\n      ${inputDtype} begin = ${inputDtype}(${begin});\n      ${inputDtype} strides = ${inputDtype}(${strides});\n\n      void main() {\n        ${dtype} coords = getOutputCoords();\n        setOutput(getX(${newCoords}));\n      }\n    `;\n  }\n}", "map": {"version": 3, "names": ["getCoordsDataType", "StridedSliceProgram", "constructor", "begin", "strides", "size", "variableNames", "outputShape", "rank", "length", "inputDtype", "dtype", "newCoords", "outputAxis", "map", "_", "i", "join", "userCode"], "sources": ["C:\\tfjs-backend-webgl\\src\\strided_slice_gpu.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2017 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {GPGPUProgram} from './gpgpu_math';\nimport {getCoordsDataType} from './shader_compiler';\n\nexport class StridedSliceProgram implements GPGPUProgram {\n  variableNames = ['x'];\n  outputShape: number[];\n  userCode: string;\n\n  constructor(begin: number[], strides: number[], size: number[]) {\n    this.outputShape = size;\n    const rank = size.length;\n    const inputDtype = getCoordsDataType(size.length);\n    const dtype = getCoordsDataType(size.length);\n\n    let newCoords = '';\n    if (rank === 1) {\n      newCoords = 'coords * strides + begin';\n    } else {\n      let outputAxis = 0;\n      newCoords =\n          size.map((_, i) => {\n                outputAxis++;\n                return size.length === 1 ?\n                    `coords * strides[${i}] + begin[${i}]` :\n                    `coords[${outputAxis - 1}] * strides[${i}] + begin[${i}]`;\n              })\n              .join(',');\n    }\n\n    this.userCode = `\n      ${inputDtype} begin = ${inputDtype}(${begin});\n      ${inputDtype} strides = ${inputDtype}(${strides});\n\n      void main() {\n        ${dtype} coords = getOutputCoords();\n        setOutput(getX(${newCoords}));\n      }\n    `;\n  }\n}\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAkBA,SAAQA,iBAAiB,QAAO,mBAAmB;AAEnD,OAAM,MAAOC,mBAAmB;EAK9BC,YAAYC,KAAe,EAAEC,OAAiB,EAAEC,IAAc;IAJ9D,KAAAC,aAAa,GAAG,CAAC,GAAG,CAAC;IAKnB,IAAI,CAACC,WAAW,GAAGF,IAAI;IACvB,MAAMG,IAAI,GAAGH,IAAI,CAACI,MAAM;IACxB,MAAMC,UAAU,GAAGV,iBAAiB,CAACK,IAAI,CAACI,MAAM,CAAC;IACjD,MAAME,KAAK,GAAGX,iBAAiB,CAACK,IAAI,CAACI,MAAM,CAAC;IAE5C,IAAIG,SAAS,GAAG,EAAE;IAClB,IAAIJ,IAAI,KAAK,CAAC,EAAE;MACdI,SAAS,GAAG,0BAA0B;KACvC,MAAM;MACL,IAAIC,UAAU,GAAG,CAAC;MAClBD,SAAS,GACLP,IAAI,CAACS,GAAG,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAI;QACZH,UAAU,EAAE;QACZ,OAAOR,IAAI,CAACI,MAAM,KAAK,CAAC,GACpB,oBAAoBO,CAAC,aAAaA,CAAC,GAAG,GACtC,UAAUH,UAAU,GAAG,CAAC,eAAeG,CAAC,aAAaA,CAAC,GAAG;MAC/D,CAAC,CAAC,CACDC,IAAI,CAAC,GAAG,CAAC;;IAGpB,IAAI,CAACC,QAAQ,GAAG;QACZR,UAAU,YAAYA,UAAU,IAAIP,KAAK;QACzCO,UAAU,cAAcA,UAAU,IAAIN,OAAO;;;UAG3CO,KAAK;yBACUC,SAAS;;KAE7B;EACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}