{"ast": null, "code": "var _jsxFileName = \"C:\\\\tmsft\\\\src\\\\components\\\\TrainingDataManager.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useCallback } from 'react';\nimport { simpleMlService } from '../services/simpleMlService';\nimport { categoryService } from '../services/categoryService';\nimport { transactionStorageService } from '../services/transactionStorageService';\nimport './TrainingDataManager.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport const TrainingDataManager = ({\n  onClose\n}) => {\n  _s();\n  // State management\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [activeTab, setActiveTab] = useState('overview');\n\n  // Data state\n  const [trainingData, setTrainingData] = useState([]);\n  const [categories, setCategories] = useState([]);\n  const [qualityMetrics, setQualityMetrics] = useState(null);\n\n  // Import state\n  const [importSource, setImportSource] = useState('manual');\n  const [importProgress, setImportProgress] = useState({\n    isImporting: false,\n    progress: 0,\n    currentStep: '',\n    result: null\n  });\n\n  // Management state\n  const [selectedDataIds, setSelectedDataIds] = useState(new Set());\n  const [filters, setFilters] = useState({\n    categoryId: '',\n    searchTerm: '',\n    dateRange: {\n      start: '',\n      end: ''\n    }\n  });\n  const sortConfig = {\n    field: 'createdDate',\n    direction: 'desc'\n  };\n  const [currentPage, setCurrentPage] = useState(1);\n  const [itemsPerPage] = useState(50);\n\n  // Manual entry state\n  const [manualEntry, setManualEntry] = useState({\n    description: '',\n    amount: '',\n    categoryId: ''\n  });\n\n  // Load data\n  const loadData = useCallback(async () => {\n    try {\n      setLoading(true);\n      setError(null);\n      const trainingData = simpleMlService.getTrainingData();\n      const categories = categoryService.getAllCategories();\n      setTrainingData(trainingData);\n      setCategories(categories);\n\n      // Calculate quality metrics\n      const metrics = calculateQualityMetrics(trainingData, categories);\n      setQualityMetrics(metrics);\n    } catch (err) {\n      setError(err instanceof Error ? err.message : 'Failed to load training data');\n    } finally {\n      setLoading(false);\n    }\n  }, []); // eslint-disable-line react-hooks/exhaustive-deps\n\n  useEffect(() => {\n    loadData();\n  }, [loadData]);\n\n  // Calculate data quality metrics\n  const calculateQualityMetrics = (data, cats) => {\n    const totalExamples = data.length;\n\n    // Category distribution\n    const categoryDistribution = cats.map(category => ({\n      categoryId: category.id,\n      count: data.filter(td => td.categoryId === category.id).length\n    }));\n    const categoriesWithData = categoryDistribution.filter(cd => cd.count > 0).length;\n    const averageExamplesPerCategory = totalExamples / Math.max(1, categoriesWithData);\n\n    // Calculate balance score (variance from average)\n    const variance = categoryDistribution.reduce((acc, cat) => {\n      return acc + Math.pow(cat.count - averageExamplesPerCategory, 2);\n    }, 0) / categoriesWithData;\n    const maxVariance = Math.pow(averageExamplesPerCategory, 2);\n    const balanceScore = Math.max(0, 1 - variance / maxVariance) * 100;\n\n    // Find potential duplicates\n    const duplicates = findDuplicateTrainingData(data);\n\n    // Calculate overall quality score\n    const dataAmountScore = Math.min(1, totalExamples / 100) * 100; // 100 examples = perfect\n    const coverageScore = categoriesWithData / cats.length * 100;\n    const qualityScore = dataAmountScore * 0.4 + balanceScore * 0.4 + coverageScore * 0.2;\n\n    // Generate recommendations\n    const recommendations = [];\n    if (totalExamples < 50) {\n      recommendations.push('Import more training examples. Aim for at least 100 examples total.');\n    }\n    if (categoriesWithData < cats.length * 0.8) {\n      recommendations.push('Add training examples for categories with no data.');\n    }\n    if (balanceScore < 60) {\n      recommendations.push('Balance your training data across categories for better performance.');\n    }\n    if (duplicates > totalExamples * 0.1) {\n      recommendations.push('Remove duplicate training examples to improve model quality.');\n    }\n    return {\n      totalExamples,\n      categoriesWithData,\n      averageExamplesPerCategory,\n      balanceScore,\n      qualityScore,\n      duplicates,\n      recommendations\n    };\n  };\n\n  // Find duplicate training data\n  const findDuplicateTrainingData = data => {\n    const seen = new Set();\n    let duplicates = 0;\n    for (const item of data) {\n      const key = `${item.description}-${item.amount}-${item.categoryId}`;\n      if (seen.has(key)) {\n        duplicates++;\n      } else {\n        seen.add(key);\n      }\n    }\n    return duplicates;\n  };\n\n  // Import functions\n  const handleImportFromManual = () => {\n    if (!manualEntry.description.trim() || !manualEntry.categoryId) {\n      alert('Please fill in description and category');\n      return;\n    }\n    try {\n      simpleMlService.addTrainingData(manualEntry.description.trim(), parseFloat(manualEntry.amount) || 0, manualEntry.categoryId);\n      setManualEntry({\n        description: '',\n        amount: '',\n        categoryId: ''\n      });\n      loadData();\n      alert('Training example added successfully');\n    } catch (error) {\n      console.error('Failed to add training data:', error);\n      alert('Failed to add training example');\n    }\n  };\n  const handleImportFromHistorical = async () => {\n    setImportProgress({\n      isImporting: true,\n      progress: 0,\n      currentStep: 'Analyzing historical transactions...',\n      result: null\n    });\n    try {\n      // Get all manually categorized transactions\n      const allTransactions = transactionStorageService.getAllTransactionsPublic();\n      const manuallyCateged = allTransactions.filter(t => t.manualCategoryId);\n      if (manuallyCateged.length === 0) {\n        alert('No manually categorized transactions found. Please categorize some transactions first.');\n        setImportProgress(prev => ({\n          ...prev,\n          isImporting: false\n        }));\n        return;\n      }\n      setImportProgress(prev => ({\n        ...prev,\n        progress: 25,\n        currentStep: 'Processing transactions...'\n      }));\n      let imported = 0;\n      let skipped = 0;\n      const errors = [];\n      for (let i = 0; i < manuallyCateged.length; i++) {\n        const transaction = manuallyCateged[i];\n        const amount = (transaction.creditAmount || 0) - (transaction.debitAmount || 0);\n        try {\n          // Check if this data already exists\n          const exists = trainingData.some(td => td.description === transaction.description && Math.abs(td.amount - amount) < 0.01 && td.categoryId === transaction.manualCategoryId);\n          if (!exists) {\n            simpleMlService.addTrainingData(transaction.description, amount, transaction.manualCategoryId);\n            imported++;\n          } else {\n            skipped++;\n          }\n        } catch (error) {\n          errors.push(`Failed to import \"${transaction.description}\": ${error}`);\n        }\n\n        // Update progress\n        const progress = 25 + (i + 1) / manuallyCateged.length * 75;\n        setImportProgress(prev => ({\n          ...prev,\n          progress,\n          currentStep: `Processing transaction ${i + 1} of ${manuallyCateged.length}...`\n        }));\n      }\n      const result = {\n        totalProcessed: manuallyCateged.length,\n        imported,\n        skipped,\n        errors: errors.length,\n        errorDetails: errors\n      };\n      setImportProgress(prev => ({\n        ...prev,\n        isImporting: false,\n        progress: 100,\n        currentStep: 'Import completed',\n        result\n      }));\n      await loadData();\n    } catch (error) {\n      console.error('Import failed:', error);\n      setImportProgress(prev => ({\n        ...prev,\n        isImporting: false,\n        currentStep: 'Import failed'\n      }));\n      alert('Import failed. Please check the console for details.');\n    }\n  };\n  const handleCsvImport = async event => {\n    var _event$target$files;\n    const file = (_event$target$files = event.target.files) === null || _event$target$files === void 0 ? void 0 : _event$target$files[0];\n    if (!file) return;\n    setImportProgress({\n      isImporting: true,\n      progress: 0,\n      currentStep: 'Reading CSV file...',\n      result: null\n    });\n    try {\n      const text = await file.text();\n      const lines = text.split('\\n').filter(line => line.trim());\n      if (lines.length < 2) {\n        alert('CSV file must contain at least a header row and one data row');\n        setImportProgress(prev => ({\n          ...prev,\n          isImporting: false\n        }));\n        return;\n      }\n      setImportProgress(prev => ({\n        ...prev,\n        progress: 25,\n        currentStep: 'Parsing CSV data...'\n      }));\n      const headers = lines[0].split(',').map(h => h.trim().toLowerCase());\n      const descriptionCol = headers.findIndex(h => h.includes('description'));\n      const amountCol = headers.findIndex(h => h.includes('amount'));\n      const categoryCol = headers.findIndex(h => h.includes('category'));\n      if (descriptionCol === -1 || amountCol === -1 || categoryCol === -1) {\n        alert('CSV must contain columns for description, amount, and category');\n        setImportProgress(prev => ({\n          ...prev,\n          isImporting: false\n        }));\n        return;\n      }\n      let imported = 0;\n      let skipped = 0;\n      const errors = [];\n      for (let i = 1; i < lines.length; i++) {\n        const cols = lines[i].split(',').map(c => c.trim());\n        try {\n          var _cols$descriptionCol, _cols$amountCol, _cols$categoryCol;\n          const description = ((_cols$descriptionCol = cols[descriptionCol]) === null || _cols$descriptionCol === void 0 ? void 0 : _cols$descriptionCol.replace(/\"/g, '')) || '';\n          const amount = parseFloat(((_cols$amountCol = cols[amountCol]) === null || _cols$amountCol === void 0 ? void 0 : _cols$amountCol.replace(/[^-\\d.]/g, '')) || '0');\n          const categoryName = ((_cols$categoryCol = cols[categoryCol]) === null || _cols$categoryCol === void 0 ? void 0 : _cols$categoryCol.replace(/\"/g, '')) || '';\n\n          // Find category by name\n          const category = categories.find(c => c.name.toLowerCase() === categoryName.toLowerCase());\n          if (!category) {\n            errors.push(`Line ${i + 1}: Category \"${categoryName}\" not found`);\n            continue;\n          }\n          if (!description) {\n            errors.push(`Line ${i + 1}: Description is required`);\n            continue;\n          }\n\n          // Check for duplicates\n          const exists = trainingData.some(td => td.description === description && Math.abs(td.amount - amount) < 0.01 && td.categoryId === category.id);\n          if (!exists) {\n            simpleMlService.addTrainingData(description, amount, category.id);\n            imported++;\n          } else {\n            skipped++;\n          }\n        } catch (error) {\n          errors.push(`Line ${i + 1}: ${error}`);\n        }\n\n        // Update progress\n        const progress = 25 + i / (lines.length - 1) * 75;\n        setImportProgress(prev => ({\n          ...prev,\n          progress,\n          currentStep: `Processing row ${i} of ${lines.length - 1}...`\n        }));\n      }\n      const result = {\n        totalProcessed: lines.length - 1,\n        imported,\n        skipped,\n        errors: errors.length,\n        errorDetails: errors\n      };\n      setImportProgress(prev => ({\n        ...prev,\n        isImporting: false,\n        progress: 100,\n        currentStep: 'Import completed',\n        result\n      }));\n      await loadData();\n    } catch (error) {\n      console.error('CSV import failed:', error);\n      setImportProgress(prev => ({\n        ...prev,\n        isImporting: false,\n        currentStep: 'Import failed'\n      }));\n      alert('CSV import failed. Please check the console for details.');\n    }\n\n    // Reset file input\n    event.target.value = '';\n  };\n\n  // Data management functions\n  const handleSelectData = dataId => {\n    setSelectedDataIds(prev => {\n      const newSet = new Set(prev);\n      if (newSet.has(dataId)) {\n        newSet.delete(dataId);\n      } else {\n        newSet.add(dataId);\n      }\n      return newSet;\n    });\n  };\n  const handleSelectAll = () => {\n    const filteredData = getFilteredAndSortedData();\n    if (selectedDataIds.size === filteredData.length) {\n      setSelectedDataIds(new Set());\n    } else {\n      setSelectedDataIds(new Set(filteredData.map(td => td.id)));\n    }\n  };\n  const handleDeleteSelected = () => {\n    if (selectedDataIds.size === 0) return;\n    const confirmed = window.confirm(`Delete ${selectedDataIds.size} training examples? This cannot be undone.`);\n    if (confirmed) {\n      // For now, we'll clear the selection since delete isn't implemented in the service\n      setSelectedDataIds(new Set());\n      alert('Delete functionality needs to be implemented in the ML service');\n    }\n  };\n  const handleClearAllData = () => {\n    const confirmed = window.confirm('Clear all training data? This cannot be undone.');\n    if (confirmed) {\n      simpleMlService.clearTrainingData();\n      loadData();\n    }\n  };\n\n  // Filter and sort data\n  const getFilteredAndSortedData = () => {\n    let filtered = trainingData;\n\n    // Apply filters\n    if (filters.categoryId) {\n      filtered = filtered.filter(td => td.categoryId === filters.categoryId);\n    }\n    if (filters.searchTerm) {\n      const searchLower = filters.searchTerm.toLowerCase();\n      filtered = filtered.filter(td => td.description.toLowerCase().includes(searchLower));\n    }\n    if (filters.dateRange.start || filters.dateRange.end) {\n      filtered = filtered.filter(td => {\n        const date = new Date(td.createdDate);\n        const start = filters.dateRange.start ? new Date(filters.dateRange.start) : null;\n        const end = filters.dateRange.end ? new Date(filters.dateRange.end) : null;\n        if (start && date < start) return false;\n        if (end && date > end) return false;\n        return true;\n      });\n    }\n\n    // Apply sorting\n    filtered.sort((a, b) => {\n      let aValue = a[sortConfig.field];\n      let bValue = b[sortConfig.field];\n      if (sortConfig.field === 'createdDate') {\n        aValue = new Date(aValue).getTime();\n        bValue = new Date(bValue).getTime();\n      }\n      if (aValue < bValue) return sortConfig.direction === 'asc' ? -1 : 1;\n      if (aValue > bValue) return sortConfig.direction === 'asc' ? 1 : -1;\n      return 0;\n    });\n    return filtered;\n  };\n\n  // Pagination\n  const getPaginatedData = () => {\n    const filtered = getFilteredAndSortedData();\n    const startIndex = (currentPage - 1) * itemsPerPage;\n    return filtered.slice(startIndex, startIndex + itemsPerPage);\n  };\n  const getTotalPages = () => {\n    const filteredCount = getFilteredAndSortedData().length;\n    return Math.ceil(filteredCount / itemsPerPage);\n  };\n\n  // Helper functions\n  const getCategoryName = categoryId => {\n    const category = categories.find(c => c.id === categoryId);\n    return (category === null || category === void 0 ? void 0 : category.name) || 'Unknown Category';\n  };\n  const formatAmount = amount => {\n    return new Intl.NumberFormat('en-US', {\n      style: 'currency',\n      currency: 'USD'\n    }).format(amount);\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"training-data-loading\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"loading-spinner\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 518,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"Loading training data manager...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 519,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 517,\n      columnNumber: 7\n    }, this);\n  }\n  if (error) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"training-data-error\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"error-icon\",\n        children: \"\\u26A0\\uFE0F\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 527,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n        children: \"Error Loading Training Data\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 528,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: error\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 529,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: loadData,\n        className: \"btn btn-primary\",\n        children: \"Try Again\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 530,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 526,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"training-data-manager\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"training-data-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"header-title\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          children: \"Training Data Manager\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 542,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Import and manage training data for machine learning models\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 543,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 541,\n        columnNumber: 9\n      }, this), onClose && /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: onClose,\n        className: \"close-btn\",\n        children: /*#__PURE__*/_jsxDEV(\"svg\", {\n          width: \"20\",\n          height: \"20\",\n          viewBox: \"0 0 24 24\",\n          fill: \"none\",\n          stroke: \"currentColor\",\n          strokeWidth: \"2\",\n          children: [/*#__PURE__*/_jsxDEV(\"line\", {\n            x1: \"18\",\n            y1: \"6\",\n            x2: \"6\",\n            y2: \"18\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 548,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"line\", {\n            x1: \"6\",\n            y1: \"6\",\n            x2: \"18\",\n            y2: \"18\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 549,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 547,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 546,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 540,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"training-data-tabs\",\n      children: [{\n        id: 'overview',\n        label: 'Overview',\n        icon: '📊'\n      }, {\n        id: 'import',\n        label: 'Import Data',\n        icon: '📥'\n      }, {\n        id: 'manage',\n        label: 'Manage Data',\n        icon: '📋'\n      }, {\n        id: 'quality',\n        label: 'Data Quality',\n        icon: '🎯'\n      }].map(tab => /*#__PURE__*/_jsxDEV(\"button\", {\n        className: `tab-btn ${activeTab === tab.id ? 'active' : ''}`,\n        onClick: () => setActiveTab(tab.id),\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"tab-icon\",\n          children: tab.icon\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 568,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"tab-label\",\n          children: tab.label\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 569,\n          columnNumber: 13\n        }, this)]\n      }, tab.id, true, {\n        fileName: _jsxFileName,\n        lineNumber: 563,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 556,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"training-data-content\",\n      children: [activeTab === 'overview' && qualityMetrics && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"overview-tab\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"overview-cards\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"overview-card\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"card-header\",\n              children: /*#__PURE__*/_jsxDEV(\"h3\", {\n                children: \"Data Statistics\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 582,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 581,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"card-content\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"metric\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"metric-label\",\n                  children: \"Total Examples\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 586,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"metric-value\",\n                  children: qualityMetrics.totalExamples\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 587,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 585,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"metric\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"metric-label\",\n                  children: \"Categories with Data\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 590,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"metric-value\",\n                  children: [qualityMetrics.categoriesWithData, \" / \", categories.length]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 591,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 589,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"metric\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"metric-label\",\n                  children: \"Avg per Category\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 596,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"metric-value\",\n                  children: qualityMetrics.averageExamplesPerCategory.toFixed(1)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 597,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 595,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 584,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 580,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"overview-card\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"card-header\",\n              children: /*#__PURE__*/_jsxDEV(\"h3\", {\n                children: \"Data Quality\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 604,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 603,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"card-content\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"metric\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"metric-label\",\n                  children: \"Overall Quality\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 608,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: `metric-value quality-${qualityMetrics.qualityScore > 70 ? 'good' : qualityMetrics.qualityScore > 40 ? 'fair' : 'poor'}`,\n                  children: [qualityMetrics.qualityScore.toFixed(0), \"%\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 609,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 607,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"metric\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"metric-label\",\n                  children: \"Balance Score\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 614,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: `metric-value quality-${qualityMetrics.balanceScore > 70 ? 'good' : qualityMetrics.balanceScore > 40 ? 'fair' : 'poor'}`,\n                  children: [qualityMetrics.balanceScore.toFixed(0), \"%\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 615,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 613,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"metric\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"metric-label\",\n                  children: \"Duplicates\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 620,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"metric-value\",\n                  children: qualityMetrics.duplicates\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 621,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 619,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 606,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 602,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"overview-card\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"card-header\",\n              children: /*#__PURE__*/_jsxDEV(\"h3\", {\n                children: \"Quick Actions\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 628,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 627,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"card-content\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"action-buttons\",\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => setActiveTab('import'),\n                  className: \"btn btn-primary\",\n                  children: \"Import More Data\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 632,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => setActiveTab('quality'),\n                  className: \"btn btn-secondary\",\n                  children: \"View Quality Report\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 638,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 631,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 630,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 626,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 579,\n          columnNumber: 13\n        }, this), qualityMetrics.recommendations.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"recommendations-card\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"card-header\",\n            children: /*#__PURE__*/_jsxDEV(\"h3\", {\n              children: \"Recommendations\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 652,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 651,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"card-content\",\n            children: /*#__PURE__*/_jsxDEV(\"ul\", {\n              className: \"recommendations-list\",\n              children: qualityMetrics.recommendations.map((rec, index) => /*#__PURE__*/_jsxDEV(\"li\", {\n                children: rec\n              }, index, false, {\n                fileName: _jsxFileName,\n                lineNumber: 657,\n                columnNumber: 23\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 655,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 654,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 650,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 578,\n        columnNumber: 11\n      }, this), activeTab === 'import' && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"import-tab\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"import-methods\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"method-selector\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              children: \"Import Method\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 671,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"method-options\",\n              children: [{\n                id: 'manual',\n                label: 'Manual Entry',\n                icon: '✏️',\n                description: 'Add individual training examples'\n              }, {\n                id: 'historical',\n                label: 'Historical Data',\n                icon: '📚',\n                description: 'Import from categorized transactions'\n              }, {\n                id: 'csv',\n                label: 'CSV File',\n                icon: '📁',\n                description: 'Upload training data from CSV file'\n              }].map(method => /*#__PURE__*/_jsxDEV(\"button\", {\n                className: `method-option ${importSource === method.id ? 'active' : ''}`,\n                onClick: () => setImportSource(method.id),\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"method-icon\",\n                  children: method.icon\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 683,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"method-info\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"method-label\",\n                    children: method.label\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 685,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"method-description\",\n                    children: method.description\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 686,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 684,\n                  columnNumber: 23\n                }, this)]\n              }, method.id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 678,\n                columnNumber: 21\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 672,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 670,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"import-content\",\n            children: [importSource === 'manual' && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"manual-import\",\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                children: \"Add Training Example\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 697,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  htmlFor: \"manualDescription\",\n                  children: \"Description\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 699,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  id: \"manualDescription\",\n                  type: \"text\",\n                  value: manualEntry.description,\n                  onChange: e => setManualEntry(prev => ({\n                    ...prev,\n                    description: e.target.value\n                  })),\n                  placeholder: \"e.g., Amazon Purchase\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 700,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 698,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-row\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"form-group\",\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    htmlFor: \"manualAmount\",\n                    children: \"Amount\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 710,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    id: \"manualAmount\",\n                    type: \"number\",\n                    step: \"0.01\",\n                    value: manualEntry.amount,\n                    onChange: e => setManualEntry(prev => ({\n                      ...prev,\n                      amount: e.target.value\n                    })),\n                    placeholder: \"0.00\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 711,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 709,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"form-group\",\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    htmlFor: \"manualCategory\",\n                    children: \"Category\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 721,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                    id: \"manualCategory\",\n                    value: manualEntry.categoryId,\n                    onChange: e => setManualEntry(prev => ({\n                      ...prev,\n                      categoryId: e.target.value\n                    })),\n                    children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"\",\n                      children: \"Select category\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 727,\n                      columnNumber: 27\n                    }, this), categories.map(category => /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: category.id,\n                      children: category.name\n                    }, category.id, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 729,\n                      columnNumber: 29\n                    }, this))]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 722,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 720,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 708,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: handleImportFromManual,\n                className: \"btn btn-primary\",\n                disabled: !manualEntry.description.trim() || !manualEntry.categoryId,\n                children: \"Add Training Example\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 736,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 696,\n              columnNumber: 19\n            }, this), importSource === 'historical' && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"historical-import\",\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                children: \"Import from Historical Transactions\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 749,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: \"Import training data from transactions you've manually categorized. This will create training examples based on your existing categorization work.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 750,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: handleImportFromHistorical,\n                disabled: importProgress.isImporting,\n                className: \"btn btn-primary\",\n                children: importProgress.isImporting ? 'Importing...' : 'Start Import'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 754,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 748,\n              columnNumber: 19\n            }, this), importSource === 'csv' && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"csv-import\",\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                children: \"Import from CSV File\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 767,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: \"Upload a CSV file with columns for description, amount, and category. The category names must match existing categories in your system.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 768,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"csv-format-example\",\n                children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n                  children: \"Expected CSV format:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 773,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"pre\", {\n                  children: [\"description,amount,category\", '\\n', \"\\\"Amazon Purchase\\\",-29.99,\\\"Shopping\\\"\", '\\n', \"\\\"Salary Deposit\\\",3000.00,\\\"Income\\\"\", '\\n', \"\\\"Electric Bill\\\",-125.50,\\\"Utilities\\\"\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 774,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 772,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"file-input-container\",\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"file\",\n                  accept: \".csv\",\n                  onChange: handleCsvImport,\n                  disabled: importProgress.isImporting,\n                  className: \"file-input\",\n                  id: \"csvFile\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 782,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                  htmlFor: \"csvFile\",\n                  className: \"file-input-label\",\n                  children: \"Choose CSV File\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 790,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 781,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 766,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 693,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 669,\n          columnNumber: 13\n        }, this), importProgress.isImporting && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"import-progress\",\n          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n            children: \"Import in Progress\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 802,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"progress-info\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"progress-step\",\n              children: importProgress.currentStep\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 804,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"progress-bar\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"progress-fill\",\n                style: {\n                  width: `${importProgress.progress}%`\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 806,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 805,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"progress-percentage\",\n              children: [importProgress.progress.toFixed(0), \"%\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 811,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 803,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 801,\n          columnNumber: 15\n        }, this), importProgress.result && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"import-results\",\n          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n            children: \"Import Results\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 819,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"result-summary\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"result-metric\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"result-label\",\n                children: \"Total Processed\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 822,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"result-value\",\n                children: importProgress.result.totalProcessed\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 823,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 821,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"result-metric\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"result-label\",\n                children: \"Imported\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 826,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"result-value success\",\n                children: importProgress.result.imported\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 827,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 825,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"result-metric\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"result-label\",\n                children: \"Skipped\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 830,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"result-value warning\",\n                children: importProgress.result.skipped\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 831,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 829,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"result-metric\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"result-label\",\n                children: \"Errors\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 834,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"result-value error\",\n                children: importProgress.result.errors\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 835,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 833,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 820,\n            columnNumber: 17\n          }, this), importProgress.result.errorDetails.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"error-details\",\n            children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n              children: \"Error Details\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 841,\n              columnNumber: 21\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"error-list\",\n              children: [importProgress.result.errorDetails.slice(0, 10).map((error, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"error-item\",\n                children: error\n              }, index, false, {\n                fileName: _jsxFileName,\n                lineNumber: 844,\n                columnNumber: 25\n              }, this)), importProgress.result.errorDetails.length > 10 && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"error-more\",\n                children: [\"+\", importProgress.result.errorDetails.length - 10, \" more errors\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 847,\n                columnNumber: 25\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 842,\n              columnNumber: 21\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 840,\n            columnNumber: 19\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 818,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 668,\n        columnNumber: 11\n      }, this), activeTab === 'manage' && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"manage-tab\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"manage-controls\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"filters\",\n            children: [/*#__PURE__*/_jsxDEV(\"select\", {\n              value: filters.categoryId,\n              onChange: e => setFilters(prev => ({\n                ...prev,\n                categoryId: e.target.value\n              })),\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"\",\n                children: \"All Categories\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 868,\n                columnNumber: 19\n              }, this), categories.map(category => /*#__PURE__*/_jsxDEV(\"option\", {\n                value: category.id,\n                children: category.name\n              }, category.id, false, {\n                fileName: _jsxFileName,\n                lineNumber: 870,\n                columnNumber: 21\n              }, this))]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 864,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              placeholder: \"Search descriptions...\",\n              value: filters.searchTerm,\n              onChange: e => setFilters(prev => ({\n                ...prev,\n                searchTerm: e.target.value\n              }))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 876,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"date\",\n              value: filters.dateRange.start,\n              onChange: e => setFilters(prev => ({\n                ...prev,\n                dateRange: {\n                  ...prev.dateRange,\n                  start: e.target.value\n                }\n              }))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 883,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"date\",\n              value: filters.dateRange.end,\n              onChange: e => setFilters(prev => ({\n                ...prev,\n                dateRange: {\n                  ...prev.dateRange,\n                  end: e.target.value\n                }\n              }))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 892,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 863,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"actions\",\n            children: [selectedDataIds.size > 0 && /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: handleDeleteSelected,\n              className: \"btn btn-danger btn-sm\",\n              children: [\"Delete Selected (\", selectedDataIds.size, \")\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 904,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: handleClearAllData,\n              className: \"btn btn-danger btn-sm\",\n              children: \"Clear All Data\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 908,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 902,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 862,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"data-table-container\",\n          children: /*#__PURE__*/_jsxDEV(\"table\", {\n            className: \"data-table\",\n            children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n              children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                  children: /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"checkbox\",\n                    checked: selectedDataIds.size === getPaginatedData().length && getPaginatedData().length > 0,\n                    onChange: handleSelectAll\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 919,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 918,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  children: \"Description\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 925,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  children: \"Amount\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 926,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  children: \"Category\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 927,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  children: \"Created\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 928,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 917,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 916,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n              children: getPaginatedData().map(data => {\n                var _categories$find;\n                return /*#__PURE__*/_jsxDEV(\"tr\", {\n                  className: selectedDataIds.has(data.id) ? 'selected' : '',\n                  children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                    children: /*#__PURE__*/_jsxDEV(\"input\", {\n                      type: \"checkbox\",\n                      checked: selectedDataIds.has(data.id),\n                      onChange: () => handleSelectData(data.id)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 938,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 937,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    className: \"description-cell\",\n                    children: data.description\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 944,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    className: data.amount >= 0 ? 'amount-credit' : 'amount-debit',\n                    children: formatAmount(data.amount)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 945,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"category-badge\",\n                      style: {\n                        backgroundColor: (_categories$find = categories.find(c => c.id === data.categoryId)) === null || _categories$find === void 0 ? void 0 : _categories$find.color\n                      },\n                      children: getCategoryName(data.categoryId)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 949,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 948,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: new Date(data.createdDate).toLocaleDateString()\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 956,\n                    columnNumber: 23\n                  }, this)]\n                }, data.id, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 933,\n                  columnNumber: 21\n                }, this);\n              })\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 931,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 915,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 914,\n          columnNumber: 13\n        }, this), getTotalPages() > 1 && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"pagination\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setCurrentPage(prev => Math.max(1, prev - 1)),\n            disabled: currentPage === 1,\n            className: \"btn btn-sm btn-secondary\",\n            children: \"Previous\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 966,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"page-info\",\n            children: [\"Page \", currentPage, \" of \", getTotalPages()]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 974,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setCurrentPage(prev => Math.min(getTotalPages(), prev + 1)),\n            disabled: currentPage === getTotalPages(),\n            className: \"btn btn-sm btn-secondary\",\n            children: \"Next\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 978,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 965,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 861,\n        columnNumber: 11\n      }, this), activeTab === 'quality' && qualityMetrics && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"quality-tab\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"quality-overview\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"Data Quality Analysis\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 994,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"quality-metrics\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"quality-metric\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"metric-circle\",\n                children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                  viewBox: \"0 0 36 36\",\n                  className: \"circular-chart\",\n                  children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                    className: \"circle-bg\",\n                    d: \"M18 2.0845\\r a 15.9155 15.9155 0 0 1 0 31.831\\r a 15.9155 15.9155 0 0 1 0 -31.831\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1000,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                    className: \"circle\",\n                    strokeDasharray: `${qualityMetrics.qualityScore}, 100`,\n                    d: \"M18 2.0845\\r a 15.9155 15.9155 0 0 1 0 31.831\\r a 15.9155 15.9155 0 0 1 0 -31.831\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1006,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"text\", {\n                    x: \"18\",\n                    y: \"20.35\",\n                    className: \"percentage\",\n                    children: [qualityMetrics.qualityScore.toFixed(0), \"%\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1013,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 999,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 998,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n                children: \"Overall Quality\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1018,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 997,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"quality-breakdown\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"breakdown-item\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"breakdown-label\",\n                  children: \"Data Amount\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1023,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"breakdown-bar\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"breakdown-fill\",\n                    style: {\n                      width: `${Math.min(100, qualityMetrics.totalExamples / 100 * 100)}%`\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1025,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1024,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"breakdown-value\",\n                  children: [qualityMetrics.totalExamples, \"/100\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1030,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1022,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"breakdown-item\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"breakdown-label\",\n                  children: \"Category Coverage\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1034,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"breakdown-bar\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"breakdown-fill\",\n                    style: {\n                      width: `${qualityMetrics.categoriesWithData / categories.length * 100}%`\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1036,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1035,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"breakdown-value\",\n                  children: [qualityMetrics.categoriesWithData, \"/\", categories.length]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1041,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1033,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"breakdown-item\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"breakdown-label\",\n                  children: \"Balance Score\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1047,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"breakdown-bar\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"breakdown-fill\",\n                    style: {\n                      width: `${qualityMetrics.balanceScore}%`\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1049,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1048,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"breakdown-value\",\n                  children: [qualityMetrics.balanceScore.toFixed(0), \"%\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1054,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1046,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1021,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 996,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 993,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"category-distribution-chart\",\n          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n            children: \"Training Data Distribution by Category\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1061,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"distribution-bars\",\n            children: categories.map(category => {\n              const count = trainingData.filter(td => td.categoryId === category.id).length;\n              const percentage = qualityMetrics.totalExamples > 0 ? count / qualityMetrics.totalExamples * 100 : 0;\n              return /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"distribution-bar-item\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"bar-info\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"bar-label\",\n                    children: category.name\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1070,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"bar-count\",\n                    children: count\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1071,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1069,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"bar-container\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"bar-fill\",\n                    style: {\n                      width: `${percentage}%`,\n                      backgroundColor: category.color\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1074,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1073,\n                  columnNumber: 23\n                }, this)]\n              }, category.id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1068,\n                columnNumber: 21\n              }, this);\n            })\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1062,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1060,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 992,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 575,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 538,\n    columnNumber: 5\n  }, this);\n};\n_s(TrainingDataManager, \"02B5St6rINgYIk1/80F7GcnNkRY=\");\n_c = TrainingDataManager;\nvar _c;\n$RefreshReg$(_c, \"TrainingDataManager\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useCallback", "simpleMlService", "categoryService", "transactionStorageService", "jsxDEV", "_jsxDEV", "TrainingDataManager", "onClose", "_s", "loading", "setLoading", "error", "setError", "activeTab", "setActiveTab", "trainingData", "setTrainingData", "categories", "setCategories", "qualityMetrics", "setQualityMetrics", "importSource", "setImportSource", "importProgress", "setImportProgress", "isImporting", "progress", "currentStep", "result", "selectedDataIds", "setSelectedDataIds", "Set", "filters", "setFilters", "categoryId", "searchTerm", "date<PERSON><PERSON><PERSON>", "start", "end", "sortConfig", "field", "direction", "currentPage", "setCurrentPage", "itemsPerPage", "manualEntry", "setManualEntry", "description", "amount", "loadData", "getTrainingData", "getAllCategories", "metrics", "calculateQualityMetrics", "err", "Error", "message", "data", "cats", "totalExamples", "length", "categoryDistribution", "map", "category", "id", "count", "filter", "td", "categoriesWithData", "cd", "averageExamplesPerCategory", "Math", "max", "variance", "reduce", "acc", "cat", "pow", "max<PERSON><PERSON>ce", "balanceScore", "duplicates", "findDuplicateTrainingData", "dataAmountScore", "min", "coverageScore", "qualityScore", "recommendations", "push", "seen", "item", "key", "has", "add", "handleImportFromManual", "trim", "alert", "addTrainingData", "parseFloat", "console", "handleImportFromHistorical", "allTransactions", "getAllTransactionsPublic", "manuallyCateged", "t", "manualCategoryId", "prev", "imported", "skipped", "errors", "i", "transaction", "creditAmount", "debitAmount", "exists", "some", "abs", "totalProcessed", "errorDetails", "handleCsvImport", "event", "_event$target$files", "file", "target", "files", "text", "lines", "split", "line", "headers", "h", "toLowerCase", "descriptionCol", "findIndex", "includes", "amountCol", "categoryCol", "cols", "c", "_cols$descriptionCol", "_cols$amountCol", "_cols$categoryCol", "replace", "categoryName", "find", "name", "value", "handleSelectData", "dataId", "newSet", "delete", "handleSelectAll", "filteredData", "getFilteredAndSortedData", "size", "handleDeleteSelected", "confirmed", "window", "confirm", "handleClearAllData", "clearTrainingData", "filtered", "searchLower", "date", "Date", "createdDate", "sort", "a", "b", "aValue", "bValue", "getTime", "getPaginatedData", "startIndex", "slice", "getTotalPages", "filteredCount", "ceil", "getCategoryName", "formatAmount", "Intl", "NumberFormat", "style", "currency", "format", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "width", "height", "viewBox", "fill", "stroke", "strokeWidth", "x1", "y1", "x2", "y2", "label", "icon", "tab", "toFixed", "rec", "index", "method", "htmlFor", "type", "onChange", "e", "placeholder", "step", "disabled", "accept", "checked", "_categories$find", "backgroundColor", "color", "toLocaleDateString", "d", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "x", "y", "percentage", "_c", "$RefreshReg$"], "sources": ["C:/tmsft/src/components/TrainingDataManager.tsx"], "sourcesContent": ["import React, { useState, useEffect, useCallback } from 'react';\r\nimport { simpleMlService } from '../services/simpleMlService';\r\nimport { categoryService } from '../services/categoryService';\r\nimport { transactionStorageService } from '../services/transactionStorageService';\r\nimport { TrainingData, TransactionCategory } from '../types';\r\nimport './TrainingDataManager.css';\r\n\r\ninterface TrainingDataManagerProps {\r\n  onClose?: () => void;\r\n}\r\n\r\ninterface DataImportResult {\r\n  totalProcessed: number;\r\n  imported: number;\r\n  skipped: number;\r\n  errors: number;\r\n  errorDetails: string[];\r\n}\r\n\r\ninterface DataQualityMetrics {\r\n  totalExamples: number;\r\n  categoriesWithData: number;\r\n  averageExamplesPerCategory: number;\r\n  balanceScore: number;\r\n  qualityScore: number;\r\n  duplicates: number;\r\n  recommendations: string[];\r\n}\r\n\r\nexport const TrainingDataManager: React.FC<TrainingDataManagerProps> = ({ onClose }) => {\r\n  // State management\r\n  const [loading, setLoading] = useState(true);\r\n  const [error, setError] = useState<string | null>(null);\r\n  const [activeTab, setActiveTab] = useState<'overview' | 'import' | 'manage' | 'quality'>('overview');\r\n  \r\n  // Data state\r\n  const [trainingData, setTrainingData] = useState<TrainingData[]>([]);\r\n  const [categories, setCategories] = useState<TransactionCategory[]>([]);\r\n  const [qualityMetrics, setQualityMetrics] = useState<DataQualityMetrics | null>(null);\r\n  \r\n  // Import state\r\n  const [importSource, setImportSource] = useState<'manual' | 'historical' | 'csv'>('manual');\r\n  const [importProgress, setImportProgress] = useState({\r\n    isImporting: false,\r\n    progress: 0,\r\n    currentStep: '',\r\n    result: null as DataImportResult | null\r\n  });\r\n  \r\n  // Management state\r\n  const [selectedDataIds, setSelectedDataIds] = useState<Set<string>>(new Set());\r\n  const [filters, setFilters] = useState({\r\n    categoryId: '',\r\n    searchTerm: '',\r\n    dateRange: { start: '', end: '' }\r\n      });\r\n    const sortConfig = { field: 'createdDate', direction: 'desc' as 'asc' | 'desc' };\r\n    const [currentPage, setCurrentPage] = useState(1);\r\n  const [itemsPerPage] = useState(50);\r\n  \r\n  // Manual entry state\r\n  const [manualEntry, setManualEntry] = useState({\r\n    description: '',\r\n    amount: '',\r\n    categoryId: ''\r\n  });\r\n\r\n  // Load data\r\n  const loadData = useCallback(async () => {\r\n    try {\r\n      setLoading(true);\r\n      setError(null);\r\n      \r\n      const trainingData = simpleMlService.getTrainingData();\r\n      const categories = categoryService.getAllCategories();\r\n      \r\n      setTrainingData(trainingData);\r\n      setCategories(categories);\r\n      \r\n      // Calculate quality metrics\r\n      const metrics = calculateQualityMetrics(trainingData, categories);\r\n      setQualityMetrics(metrics);\r\n    } catch (err) {\r\n      setError(err instanceof Error ? err.message : 'Failed to load training data');\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  }, []); // eslint-disable-line react-hooks/exhaustive-deps\r\n\r\n  useEffect(() => {\r\n    loadData();\r\n  }, [loadData]);\r\n\r\n  // Calculate data quality metrics\r\n  const calculateQualityMetrics = (data: TrainingData[], cats: TransactionCategory[]): DataQualityMetrics => {\r\n    const totalExamples = data.length;\r\n    \r\n    // Category distribution\r\n    const categoryDistribution = cats.map(category => ({\r\n      categoryId: category.id,\r\n      count: data.filter(td => td.categoryId === category.id).length\r\n    }));\r\n    \r\n    const categoriesWithData = categoryDistribution.filter(cd => cd.count > 0).length;\r\n    const averageExamplesPerCategory = totalExamples / Math.max(1, categoriesWithData);\r\n    \r\n    // Calculate balance score (variance from average)\r\n    const variance = categoryDistribution.reduce((acc, cat) => {\r\n      return acc + Math.pow(cat.count - averageExamplesPerCategory, 2);\r\n    }, 0) / categoriesWithData;\r\n    \r\n    const maxVariance = Math.pow(averageExamplesPerCategory, 2);\r\n    const balanceScore = Math.max(0, 1 - (variance / maxVariance)) * 100;\r\n    \r\n    // Find potential duplicates\r\n    const duplicates = findDuplicateTrainingData(data);\r\n    \r\n    // Calculate overall quality score\r\n    const dataAmountScore = Math.min(1, totalExamples / 100) * 100; // 100 examples = perfect\r\n    const coverageScore = (categoriesWithData / cats.length) * 100;\r\n    const qualityScore = (dataAmountScore * 0.4 + balanceScore * 0.4 + coverageScore * 0.2);\r\n    \r\n    // Generate recommendations\r\n    const recommendations: string[] = [];\r\n    if (totalExamples < 50) {\r\n      recommendations.push('Import more training examples. Aim for at least 100 examples total.');\r\n    }\r\n    if (categoriesWithData < cats.length * 0.8) {\r\n      recommendations.push('Add training examples for categories with no data.');\r\n    }\r\n    if (balanceScore < 60) {\r\n      recommendations.push('Balance your training data across categories for better performance.');\r\n    }\r\n    if (duplicates > totalExamples * 0.1) {\r\n      recommendations.push('Remove duplicate training examples to improve model quality.');\r\n    }\r\n    \r\n    return {\r\n      totalExamples,\r\n      categoriesWithData,\r\n      averageExamplesPerCategory,\r\n      balanceScore,\r\n      qualityScore,\r\n      duplicates,\r\n      recommendations\r\n    };\r\n  };\r\n\r\n  // Find duplicate training data\r\n  const findDuplicateTrainingData = (data: TrainingData[]): number => {\r\n    const seen = new Set<string>();\r\n    let duplicates = 0;\r\n    \r\n    for (const item of data) {\r\n      const key = `${item.description}-${item.amount}-${item.categoryId}`;\r\n      if (seen.has(key)) {\r\n        duplicates++;\r\n      } else {\r\n        seen.add(key);\r\n      }\r\n    }\r\n    \r\n    return duplicates;\r\n  };\r\n\r\n  // Import functions\r\n  const handleImportFromManual = () => {\r\n    if (!manualEntry.description.trim() || !manualEntry.categoryId) {\r\n      alert('Please fill in description and category');\r\n      return;\r\n    }\r\n    \r\n    try {\r\n      simpleMlService.addTrainingData(\r\n        manualEntry.description.trim(),\r\n        parseFloat(manualEntry.amount) || 0,\r\n        manualEntry.categoryId\r\n      );\r\n      \r\n      setManualEntry({ description: '', amount: '', categoryId: '' });\r\n      loadData();\r\n      alert('Training example added successfully');\r\n    } catch (error) {\r\n      console.error('Failed to add training data:', error);\r\n      alert('Failed to add training example');\r\n    }\r\n  };\r\n\r\n  const handleImportFromHistorical = async () => {\r\n    setImportProgress({\r\n      isImporting: true,\r\n      progress: 0,\r\n      currentStep: 'Analyzing historical transactions...',\r\n      result: null\r\n    });\r\n\r\n    try {\r\n      // Get all manually categorized transactions\r\n      const allTransactions = transactionStorageService.getAllTransactionsPublic();\r\n      const manuallyCateged = allTransactions.filter(t => t.manualCategoryId);\r\n      \r\n      if (manuallyCateged.length === 0) {\r\n        alert('No manually categorized transactions found. Please categorize some transactions first.');\r\n        setImportProgress(prev => ({ ...prev, isImporting: false }));\r\n        return;\r\n      }\r\n\r\n      setImportProgress(prev => ({\r\n        ...prev,\r\n        progress: 25,\r\n        currentStep: 'Processing transactions...'\r\n      }));\r\n\r\n      let imported = 0;\r\n      let skipped = 0;\r\n      const errors: string[] = [];\r\n\r\n      for (let i = 0; i < manuallyCateged.length; i++) {\r\n        const transaction = manuallyCateged[i];\r\n        const amount = (transaction.creditAmount || 0) - (transaction.debitAmount || 0);\r\n        \r\n        try {\r\n          // Check if this data already exists\r\n          const exists = trainingData.some(td => \r\n            td.description === transaction.description && \r\n            Math.abs(td.amount - amount) < 0.01 && \r\n            td.categoryId === transaction.manualCategoryId\r\n          );\r\n          \r\n          if (!exists) {\r\n            simpleMlService.addTrainingData(\r\n              transaction.description,\r\n              amount,\r\n              transaction.manualCategoryId!\r\n            );\r\n            imported++;\r\n          } else {\r\n            skipped++;\r\n          }\r\n        } catch (error) {\r\n          errors.push(`Failed to import \"${transaction.description}\": ${error}`);\r\n        }\r\n\r\n        // Update progress\r\n        const progress = 25 + ((i + 1) / manuallyCateged.length) * 75;\r\n        setImportProgress(prev => ({\r\n          ...prev,\r\n          progress,\r\n          currentStep: `Processing transaction ${i + 1} of ${manuallyCateged.length}...`\r\n        }));\r\n      }\r\n\r\n      const result: DataImportResult = {\r\n        totalProcessed: manuallyCateged.length,\r\n        imported,\r\n        skipped,\r\n        errors: errors.length,\r\n        errorDetails: errors\r\n      };\r\n\r\n      setImportProgress(prev => ({\r\n        ...prev,\r\n        isImporting: false,\r\n        progress: 100,\r\n        currentStep: 'Import completed',\r\n        result\r\n      }));\r\n\r\n      await loadData();\r\n    } catch (error) {\r\n      console.error('Import failed:', error);\r\n      setImportProgress(prev => ({\r\n        ...prev,\r\n        isImporting: false,\r\n        currentStep: 'Import failed'\r\n      }));\r\n      alert('Import failed. Please check the console for details.');\r\n    }\r\n  };\r\n\r\n  const handleCsvImport = async (event: React.ChangeEvent<HTMLInputElement>) => {\r\n    const file = event.target.files?.[0];\r\n    if (!file) return;\r\n\r\n    setImportProgress({\r\n      isImporting: true,\r\n      progress: 0,\r\n      currentStep: 'Reading CSV file...',\r\n      result: null\r\n    });\r\n\r\n    try {\r\n      const text = await file.text();\r\n      const lines = text.split('\\n').filter(line => line.trim());\r\n      \r\n      if (lines.length < 2) {\r\n        alert('CSV file must contain at least a header row and one data row');\r\n        setImportProgress(prev => ({ ...prev, isImporting: false }));\r\n        return;\r\n      }\r\n\r\n      setImportProgress(prev => ({\r\n        ...prev,\r\n        progress: 25,\r\n        currentStep: 'Parsing CSV data...'\r\n      }));\r\n\r\n      const headers = lines[0].split(',').map(h => h.trim().toLowerCase());\r\n      const descriptionCol = headers.findIndex(h => h.includes('description'));\r\n      const amountCol = headers.findIndex(h => h.includes('amount'));\r\n      const categoryCol = headers.findIndex(h => h.includes('category'));\r\n\r\n      if (descriptionCol === -1 || amountCol === -1 || categoryCol === -1) {\r\n        alert('CSV must contain columns for description, amount, and category');\r\n        setImportProgress(prev => ({ ...prev, isImporting: false }));\r\n        return;\r\n      }\r\n\r\n      let imported = 0;\r\n      let skipped = 0;\r\n      const errors: string[] = [];\r\n\r\n      for (let i = 1; i < lines.length; i++) {\r\n        const cols = lines[i].split(',').map(c => c.trim());\r\n        \r\n        try {\r\n          const description = cols[descriptionCol]?.replace(/\"/g, '') || '';\r\n          const amount = parseFloat(cols[amountCol]?.replace(/[^-\\d.]/g, '') || '0');\r\n          const categoryName = cols[categoryCol]?.replace(/\"/g, '') || '';\r\n          \r\n          // Find category by name\r\n          const category = categories.find(c => \r\n            c.name.toLowerCase() === categoryName.toLowerCase()\r\n          );\r\n          \r\n          if (!category) {\r\n            errors.push(`Line ${i + 1}: Category \"${categoryName}\" not found`);\r\n            continue;\r\n          }\r\n\r\n          if (!description) {\r\n            errors.push(`Line ${i + 1}: Description is required`);\r\n            continue;\r\n          }\r\n\r\n          // Check for duplicates\r\n          const exists = trainingData.some(td => \r\n            td.description === description && \r\n            Math.abs(td.amount - amount) < 0.01 && \r\n            td.categoryId === category.id\r\n          );\r\n          \r\n          if (!exists) {\r\n            simpleMlService.addTrainingData(description, amount, category.id);\r\n            imported++;\r\n          } else {\r\n            skipped++;\r\n          }\r\n        } catch (error) {\r\n          errors.push(`Line ${i + 1}: ${error}`);\r\n        }\r\n\r\n        // Update progress\r\n        const progress = 25 + ((i) / (lines.length - 1)) * 75;\r\n        setImportProgress(prev => ({\r\n          ...prev,\r\n          progress,\r\n          currentStep: `Processing row ${i} of ${lines.length - 1}...`\r\n        }));\r\n      }\r\n\r\n      const result: DataImportResult = {\r\n        totalProcessed: lines.length - 1,\r\n        imported,\r\n        skipped,\r\n        errors: errors.length,\r\n        errorDetails: errors\r\n      };\r\n\r\n      setImportProgress(prev => ({\r\n        ...prev,\r\n        isImporting: false,\r\n        progress: 100,\r\n        currentStep: 'Import completed',\r\n        result\r\n      }));\r\n\r\n      await loadData();\r\n    } catch (error) {\r\n      console.error('CSV import failed:', error);\r\n      setImportProgress(prev => ({\r\n        ...prev,\r\n        isImporting: false,\r\n        currentStep: 'Import failed'\r\n      }));\r\n      alert('CSV import failed. Please check the console for details.');\r\n    }\r\n\r\n    // Reset file input\r\n    event.target.value = '';\r\n  };\r\n\r\n  // Data management functions\r\n  const handleSelectData = (dataId: string) => {\r\n    setSelectedDataIds(prev => {\r\n      const newSet = new Set(prev);\r\n      if (newSet.has(dataId)) {\r\n        newSet.delete(dataId);\r\n      } else {\r\n        newSet.add(dataId);\r\n      }\r\n      return newSet;\r\n    });\r\n  };\r\n\r\n  const handleSelectAll = () => {\r\n    const filteredData = getFilteredAndSortedData();\r\n    if (selectedDataIds.size === filteredData.length) {\r\n      setSelectedDataIds(new Set());\r\n    } else {\r\n      setSelectedDataIds(new Set(filteredData.map(td => td.id)));\r\n    }\r\n  };\r\n\r\n  const handleDeleteSelected = () => {\r\n    if (selectedDataIds.size === 0) return;\r\n    \r\n    const confirmed = window.confirm(`Delete ${selectedDataIds.size} training examples? This cannot be undone.`);\r\n    if (confirmed) {\r\n      // For now, we'll clear the selection since delete isn't implemented in the service\r\n      setSelectedDataIds(new Set());\r\n      alert('Delete functionality needs to be implemented in the ML service');\r\n    }\r\n  };\r\n\r\n  const handleClearAllData = () => {\r\n    const confirmed = window.confirm('Clear all training data? This cannot be undone.');\r\n    if (confirmed) {\r\n      simpleMlService.clearTrainingData();\r\n      loadData();\r\n    }\r\n  };\r\n\r\n  // Filter and sort data\r\n  const getFilteredAndSortedData = (): TrainingData[] => {\r\n    let filtered = trainingData;\r\n    \r\n    // Apply filters\r\n    if (filters.categoryId) {\r\n      filtered = filtered.filter(td => td.categoryId === filters.categoryId);\r\n    }\r\n    \r\n    if (filters.searchTerm) {\r\n      const searchLower = filters.searchTerm.toLowerCase();\r\n      filtered = filtered.filter(td => \r\n        td.description.toLowerCase().includes(searchLower)\r\n      );\r\n    }\r\n    \r\n    if (filters.dateRange.start || filters.dateRange.end) {\r\n      filtered = filtered.filter(td => {\r\n        const date = new Date(td.createdDate);\r\n        const start = filters.dateRange.start ? new Date(filters.dateRange.start) : null;\r\n        const end = filters.dateRange.end ? new Date(filters.dateRange.end) : null;\r\n        \r\n        if (start && date < start) return false;\r\n        if (end && date > end) return false;\r\n        return true;\r\n      });\r\n    }\r\n    \r\n    // Apply sorting\r\n    filtered.sort((a, b) => {\r\n      let aValue: any = a[sortConfig.field as keyof TrainingData];\r\n      let bValue: any = b[sortConfig.field as keyof TrainingData];\r\n      \r\n      if (sortConfig.field === 'createdDate') {\r\n        aValue = new Date(aValue).getTime();\r\n        bValue = new Date(bValue).getTime();\r\n      }\r\n      \r\n      if (aValue < bValue) return sortConfig.direction === 'asc' ? -1 : 1;\r\n      if (aValue > bValue) return sortConfig.direction === 'asc' ? 1 : -1;\r\n      return 0;\r\n    });\r\n    \r\n    return filtered;\r\n  };\r\n\r\n  // Pagination\r\n  const getPaginatedData = (): TrainingData[] => {\r\n    const filtered = getFilteredAndSortedData();\r\n    const startIndex = (currentPage - 1) * itemsPerPage;\r\n    return filtered.slice(startIndex, startIndex + itemsPerPage);\r\n  };\r\n\r\n  const getTotalPages = (): number => {\r\n    const filteredCount = getFilteredAndSortedData().length;\r\n    return Math.ceil(filteredCount / itemsPerPage);\r\n  };\r\n\r\n  // Helper functions\r\n  const getCategoryName = (categoryId: string): string => {\r\n    const category = categories.find(c => c.id === categoryId);\r\n    return category?.name || 'Unknown Category';\r\n  };\r\n\r\n  const formatAmount = (amount: number): string => {\r\n    return new Intl.NumberFormat('en-US', {\r\n      style: 'currency',\r\n      currency: 'USD'\r\n    }).format(amount);\r\n  };\r\n\r\n  if (loading) {\r\n    return (\r\n      <div className=\"training-data-loading\">\r\n        <div className=\"loading-spinner\"></div>\r\n        <p>Loading training data manager...</p>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  if (error) {\r\n    return (\r\n      <div className=\"training-data-error\">\r\n        <div className=\"error-icon\">⚠️</div>\r\n        <h3>Error Loading Training Data</h3>\r\n        <p>{error}</p>\r\n        <button onClick={loadData} className=\"btn btn-primary\">\r\n          Try Again\r\n        </button>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className=\"training-data-manager\">\r\n      {/* Header */}\r\n      <div className=\"training-data-header\">\r\n        <div className=\"header-title\">\r\n          <h2>Training Data Manager</h2>\r\n          <p>Import and manage training data for machine learning models</p>\r\n        </div>\r\n        {onClose && (\r\n          <button onClick={onClose} className=\"close-btn\">\r\n            <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\">\r\n              <line x1=\"18\" y1=\"6\" x2=\"6\" y2=\"18\"></line>\r\n              <line x1=\"6\" y1=\"6\" x2=\"18\" y2=\"18\"></line>\r\n            </svg>\r\n          </button>\r\n        )}\r\n      </div>\r\n\r\n      {/* Tab Navigation */}\r\n      <div className=\"training-data-tabs\">\r\n        {[\r\n          { id: 'overview', label: 'Overview', icon: '📊' },\r\n          { id: 'import', label: 'Import Data', icon: '📥' },\r\n          { id: 'manage', label: 'Manage Data', icon: '📋' },\r\n          { id: 'quality', label: 'Data Quality', icon: '🎯' }\r\n        ].map(tab => (\r\n          <button\r\n            key={tab.id}\r\n            className={`tab-btn ${activeTab === tab.id ? 'active' : ''}`}\r\n            onClick={() => setActiveTab(tab.id as any)}\r\n          >\r\n            <span className=\"tab-icon\">{tab.icon}</span>\r\n            <span className=\"tab-label\">{tab.label}</span>\r\n          </button>\r\n        ))}\r\n      </div>\r\n\r\n      {/* Tab Content */}\r\n      <div className=\"training-data-content\">\r\n        {/* Overview Tab */}\r\n        {activeTab === 'overview' && qualityMetrics && (\r\n          <div className=\"overview-tab\">\r\n            <div className=\"overview-cards\">\r\n              <div className=\"overview-card\">\r\n                <div className=\"card-header\">\r\n                  <h3>Data Statistics</h3>\r\n                </div>\r\n                <div className=\"card-content\">\r\n                  <div className=\"metric\">\r\n                    <span className=\"metric-label\">Total Examples</span>\r\n                    <span className=\"metric-value\">{qualityMetrics.totalExamples}</span>\r\n                  </div>\r\n                  <div className=\"metric\">\r\n                    <span className=\"metric-label\">Categories with Data</span>\r\n                    <span className=\"metric-value\">\r\n                      {qualityMetrics.categoriesWithData} / {categories.length}\r\n                    </span>\r\n                  </div>\r\n                  <div className=\"metric\">\r\n                    <span className=\"metric-label\">Avg per Category</span>\r\n                    <span className=\"metric-value\">{qualityMetrics.averageExamplesPerCategory.toFixed(1)}</span>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n\r\n              <div className=\"overview-card\">\r\n                <div className=\"card-header\">\r\n                  <h3>Data Quality</h3>\r\n                </div>\r\n                <div className=\"card-content\">\r\n                  <div className=\"metric\">\r\n                    <span className=\"metric-label\">Overall Quality</span>\r\n                    <span className={`metric-value quality-${qualityMetrics.qualityScore > 70 ? 'good' : qualityMetrics.qualityScore > 40 ? 'fair' : 'poor'}`}>\r\n                      {qualityMetrics.qualityScore.toFixed(0)}%\r\n                    </span>\r\n                  </div>\r\n                  <div className=\"metric\">\r\n                    <span className=\"metric-label\">Balance Score</span>\r\n                    <span className={`metric-value quality-${qualityMetrics.balanceScore > 70 ? 'good' : qualityMetrics.balanceScore > 40 ? 'fair' : 'poor'}`}>\r\n                      {qualityMetrics.balanceScore.toFixed(0)}%\r\n                    </span>\r\n                  </div>\r\n                  <div className=\"metric\">\r\n                    <span className=\"metric-label\">Duplicates</span>\r\n                    <span className=\"metric-value\">{qualityMetrics.duplicates}</span>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n\r\n              <div className=\"overview-card\">\r\n                <div className=\"card-header\">\r\n                  <h3>Quick Actions</h3>\r\n                </div>\r\n                <div className=\"card-content\">\r\n                  <div className=\"action-buttons\">\r\n                    <button \r\n                      onClick={() => setActiveTab('import')}\r\n                      className=\"btn btn-primary\"\r\n                    >\r\n                      Import More Data\r\n                    </button>\r\n                    <button \r\n                      onClick={() => setActiveTab('quality')}\r\n                      className=\"btn btn-secondary\"\r\n                    >\r\n                      View Quality Report\r\n                    </button>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            {qualityMetrics.recommendations.length > 0 && (\r\n              <div className=\"recommendations-card\">\r\n                <div className=\"card-header\">\r\n                  <h3>Recommendations</h3>\r\n                </div>\r\n                <div className=\"card-content\">\r\n                  <ul className=\"recommendations-list\">\r\n                    {qualityMetrics.recommendations.map((rec, index) => (\r\n                      <li key={index}>{rec}</li>\r\n                    ))}\r\n                  </ul>\r\n                </div>\r\n              </div>\r\n            )}\r\n          </div>\r\n        )}\r\n\r\n        {/* Import Tab */}\r\n        {activeTab === 'import' && (\r\n          <div className=\"import-tab\">\r\n            <div className=\"import-methods\">\r\n              <div className=\"method-selector\">\r\n                <h3>Import Method</h3>\r\n                <div className=\"method-options\">\r\n                  {[\r\n                    { id: 'manual', label: 'Manual Entry', icon: '✏️', description: 'Add individual training examples' },\r\n                    { id: 'historical', label: 'Historical Data', icon: '📚', description: 'Import from categorized transactions' },\r\n                    { id: 'csv', label: 'CSV File', icon: '📁', description: 'Upload training data from CSV file' }\r\n                  ].map(method => (\r\n                    <button\r\n                      key={method.id}\r\n                      className={`method-option ${importSource === method.id ? 'active' : ''}`}\r\n                      onClick={() => setImportSource(method.id as any)}\r\n                    >\r\n                      <span className=\"method-icon\">{method.icon}</span>\r\n                      <div className=\"method-info\">\r\n                        <span className=\"method-label\">{method.label}</span>\r\n                        <span className=\"method-description\">{method.description}</span>\r\n                      </div>\r\n                    </button>\r\n                  ))}\r\n                </div>\r\n              </div>\r\n\r\n              <div className=\"import-content\">\r\n                {/* Manual Entry */}\r\n                {importSource === 'manual' && (\r\n                  <div className=\"manual-import\">\r\n                    <h4>Add Training Example</h4>\r\n                    <div className=\"form-group\">\r\n                      <label htmlFor=\"manualDescription\">Description</label>\r\n                      <input\r\n                        id=\"manualDescription\"\r\n                        type=\"text\"\r\n                        value={manualEntry.description}\r\n                        onChange={(e) => setManualEntry(prev => ({ ...prev, description: e.target.value }))}\r\n                        placeholder=\"e.g., Amazon Purchase\"\r\n                      />\r\n                    </div>\r\n                    <div className=\"form-row\">\r\n                      <div className=\"form-group\">\r\n                        <label htmlFor=\"manualAmount\">Amount</label>\r\n                        <input\r\n                          id=\"manualAmount\"\r\n                          type=\"number\"\r\n                          step=\"0.01\"\r\n                          value={manualEntry.amount}\r\n                          onChange={(e) => setManualEntry(prev => ({ ...prev, amount: e.target.value }))}\r\n                          placeholder=\"0.00\"\r\n                        />\r\n                      </div>\r\n                      <div className=\"form-group\">\r\n                        <label htmlFor=\"manualCategory\">Category</label>\r\n                        <select\r\n                          id=\"manualCategory\"\r\n                          value={manualEntry.categoryId}\r\n                          onChange={(e) => setManualEntry(prev => ({ ...prev, categoryId: e.target.value }))}\r\n                        >\r\n                          <option value=\"\">Select category</option>\r\n                          {categories.map(category => (\r\n                            <option key={category.id} value={category.id}>\r\n                              {category.name}\r\n                            </option>\r\n                          ))}\r\n                        </select>\r\n                      </div>\r\n                    </div>\r\n                    <button \r\n                      onClick={handleImportFromManual}\r\n                      className=\"btn btn-primary\"\r\n                      disabled={!manualEntry.description.trim() || !manualEntry.categoryId}\r\n                    >\r\n                      Add Training Example\r\n                    </button>\r\n                  </div>\r\n                )}\r\n\r\n                {/* Historical Import */}\r\n                {importSource === 'historical' && (\r\n                  <div className=\"historical-import\">\r\n                    <h4>Import from Historical Transactions</h4>\r\n                    <p>\r\n                      Import training data from transactions you've manually categorized. \r\n                      This will create training examples based on your existing categorization work.\r\n                    </p>\r\n                    <button \r\n                      onClick={handleImportFromHistorical}\r\n                      disabled={importProgress.isImporting}\r\n                      className=\"btn btn-primary\"\r\n                    >\r\n                      {importProgress.isImporting ? 'Importing...' : 'Start Import'}\r\n                    </button>\r\n                  </div>\r\n                )}\r\n\r\n                {/* CSV Import */}\r\n                {importSource === 'csv' && (\r\n                  <div className=\"csv-import\">\r\n                    <h4>Import from CSV File</h4>\r\n                    <p>\r\n                      Upload a CSV file with columns for description, amount, and category. \r\n                      The category names must match existing categories in your system.\r\n                    </p>\r\n                    <div className=\"csv-format-example\">\r\n                      <h5>Expected CSV format:</h5>\r\n                      <pre>\r\n                        description,amount,category{'\\n'}\r\n                        \"Amazon Purchase\",-29.99,\"Shopping\"{'\\n'}\r\n                        \"Salary Deposit\",3000.00,\"Income\"{'\\n'}\r\n                        \"Electric Bill\",-125.50,\"Utilities\"\r\n                      </pre>\r\n                    </div>\r\n                    <div className=\"file-input-container\">\r\n                      <input\r\n                        type=\"file\"\r\n                        accept=\".csv\"\r\n                        onChange={handleCsvImport}\r\n                        disabled={importProgress.isImporting}\r\n                        className=\"file-input\"\r\n                        id=\"csvFile\"\r\n                      />\r\n                      <label htmlFor=\"csvFile\" className=\"file-input-label\">\r\n                        Choose CSV File\r\n                      </label>\r\n                    </div>\r\n                  </div>\r\n                )}\r\n              </div>\r\n            </div>\r\n\r\n            {/* Import Progress */}\r\n            {importProgress.isImporting && (\r\n              <div className=\"import-progress\">\r\n                <h4>Import in Progress</h4>\r\n                <div className=\"progress-info\">\r\n                  <div className=\"progress-step\">{importProgress.currentStep}</div>\r\n                  <div className=\"progress-bar\">\r\n                    <div \r\n                      className=\"progress-fill\"\r\n                      style={{ width: `${importProgress.progress}%` }}\r\n                    ></div>\r\n                  </div>\r\n                  <div className=\"progress-percentage\">{importProgress.progress.toFixed(0)}%</div>\r\n                </div>\r\n              </div>\r\n            )}\r\n\r\n            {/* Import Results */}\r\n            {importProgress.result && (\r\n              <div className=\"import-results\">\r\n                <h4>Import Results</h4>\r\n                <div className=\"result-summary\">\r\n                  <div className=\"result-metric\">\r\n                    <span className=\"result-label\">Total Processed</span>\r\n                    <span className=\"result-value\">{importProgress.result.totalProcessed}</span>\r\n                  </div>\r\n                  <div className=\"result-metric\">\r\n                    <span className=\"result-label\">Imported</span>\r\n                    <span className=\"result-value success\">{importProgress.result.imported}</span>\r\n                  </div>\r\n                  <div className=\"result-metric\">\r\n                    <span className=\"result-label\">Skipped</span>\r\n                    <span className=\"result-value warning\">{importProgress.result.skipped}</span>\r\n                  </div>\r\n                  <div className=\"result-metric\">\r\n                    <span className=\"result-label\">Errors</span>\r\n                    <span className=\"result-value error\">{importProgress.result.errors}</span>\r\n                  </div>\r\n                </div>\r\n                \r\n                {importProgress.result.errorDetails.length > 0 && (\r\n                  <div className=\"error-details\">\r\n                    <h5>Error Details</h5>\r\n                    <div className=\"error-list\">\r\n                      {importProgress.result.errorDetails.slice(0, 10).map((error, index) => (\r\n                        <div key={index} className=\"error-item\">{error}</div>\r\n                      ))}\r\n                      {importProgress.result.errorDetails.length > 10 && (\r\n                        <div className=\"error-more\">\r\n                          +{importProgress.result.errorDetails.length - 10} more errors\r\n                        </div>\r\n                      )}\r\n                    </div>\r\n                  </div>\r\n                )}\r\n              </div>\r\n            )}\r\n          </div>\r\n        )}\r\n\r\n        {/* Manage Tab */}\r\n        {activeTab === 'manage' && (\r\n          <div className=\"manage-tab\">\r\n            <div className=\"manage-controls\">\r\n              <div className=\"filters\">\r\n                <select\r\n                  value={filters.categoryId}\r\n                  onChange={(e) => setFilters(prev => ({ ...prev, categoryId: e.target.value }))}\r\n                >\r\n                  <option value=\"\">All Categories</option>\r\n                  {categories.map(category => (\r\n                    <option key={category.id} value={category.id}>\r\n                      {category.name}\r\n                    </option>\r\n                  ))}\r\n                </select>\r\n\r\n                <input\r\n                  type=\"text\"\r\n                  placeholder=\"Search descriptions...\"\r\n                  value={filters.searchTerm}\r\n                  onChange={(e) => setFilters(prev => ({ ...prev, searchTerm: e.target.value }))}\r\n                />\r\n\r\n                <input\r\n                  type=\"date\"\r\n                  value={filters.dateRange.start}\r\n                  onChange={(e) => setFilters(prev => ({\r\n                    ...prev,\r\n                    dateRange: { ...prev.dateRange, start: e.target.value }\r\n                  }))}\r\n                />\r\n\r\n                <input\r\n                  type=\"date\"\r\n                  value={filters.dateRange.end}\r\n                  onChange={(e) => setFilters(prev => ({\r\n                    ...prev,\r\n                    dateRange: { ...prev.dateRange, end: e.target.value }\r\n                  }))}\r\n                />\r\n              </div>\r\n\r\n              <div className=\"actions\">\r\n                {selectedDataIds.size > 0 && (\r\n                  <button onClick={handleDeleteSelected} className=\"btn btn-danger btn-sm\">\r\n                    Delete Selected ({selectedDataIds.size})\r\n                  </button>\r\n                )}\r\n                <button onClick={handleClearAllData} className=\"btn btn-danger btn-sm\">\r\n                  Clear All Data\r\n                </button>\r\n              </div>\r\n            </div>\r\n\r\n            <div className=\"data-table-container\">\r\n              <table className=\"data-table\">\r\n                <thead>\r\n                  <tr>\r\n                    <th>\r\n                      <input\r\n                        type=\"checkbox\"\r\n                        checked={selectedDataIds.size === getPaginatedData().length && getPaginatedData().length > 0}\r\n                        onChange={handleSelectAll}\r\n                      />\r\n                    </th>\r\n                    <th>Description</th>\r\n                    <th>Amount</th>\r\n                    <th>Category</th>\r\n                    <th>Created</th>\r\n                  </tr>\r\n                </thead>\r\n                <tbody>\r\n                  {getPaginatedData().map(data => (\r\n                    <tr \r\n                      key={data.id}\r\n                      className={selectedDataIds.has(data.id) ? 'selected' : ''}\r\n                    >\r\n                      <td>\r\n                        <input\r\n                          type=\"checkbox\"\r\n                          checked={selectedDataIds.has(data.id)}\r\n                          onChange={() => handleSelectData(data.id)}\r\n                        />\r\n                      </td>\r\n                      <td className=\"description-cell\">{data.description}</td>\r\n                      <td className={data.amount >= 0 ? 'amount-credit' : 'amount-debit'}>\r\n                        {formatAmount(data.amount)}\r\n                      </td>\r\n                      <td>\r\n                        <span \r\n                          className=\"category-badge\"\r\n                          style={{ backgroundColor: categories.find(c => c.id === data.categoryId)?.color }}\r\n                        >\r\n                          {getCategoryName(data.categoryId)}\r\n                        </span>\r\n                      </td>\r\n                      <td>{new Date(data.createdDate).toLocaleDateString()}</td>\r\n                    </tr>\r\n                  ))}\r\n                </tbody>\r\n              </table>\r\n            </div>\r\n\r\n            {/* Pagination */}\r\n            {getTotalPages() > 1 && (\r\n              <div className=\"pagination\">\r\n                <button\r\n                  onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}\r\n                  disabled={currentPage === 1}\r\n                  className=\"btn btn-sm btn-secondary\"\r\n                >\r\n                  Previous\r\n                </button>\r\n                \r\n                <span className=\"page-info\">\r\n                  Page {currentPage} of {getTotalPages()}\r\n                </span>\r\n                \r\n                <button\r\n                  onClick={() => setCurrentPage(prev => Math.min(getTotalPages(), prev + 1))}\r\n                  disabled={currentPage === getTotalPages()}\r\n                  className=\"btn btn-sm btn-secondary\"\r\n                >\r\n                  Next\r\n                </button>\r\n              </div>\r\n            )}\r\n          </div>\r\n        )}\r\n\r\n        {/* Quality Tab */}\r\n        {activeTab === 'quality' && qualityMetrics && (\r\n          <div className=\"quality-tab\">\r\n            <div className=\"quality-overview\">\r\n              <h3>Data Quality Analysis</h3>\r\n              \r\n              <div className=\"quality-metrics\">\r\n                <div className=\"quality-metric\">\r\n                  <div className=\"metric-circle\">\r\n                    <svg viewBox=\"0 0 36 36\" className=\"circular-chart\">\r\n                      <path\r\n                        className=\"circle-bg\"\r\n                        d=\"M18 2.0845\r\n                          a 15.9155 15.9155 0 0 1 0 31.831\r\n                          a 15.9155 15.9155 0 0 1 0 -31.831\"\r\n                      />\r\n                      <path\r\n                        className=\"circle\"\r\n                        strokeDasharray={`${qualityMetrics.qualityScore}, 100`}\r\n                        d=\"M18 2.0845\r\n                          a 15.9155 15.9155 0 0 1 0 31.831\r\n                          a 15.9155 15.9155 0 0 1 0 -31.831\"\r\n                      />\r\n                      <text x=\"18\" y=\"20.35\" className=\"percentage\">\r\n                        {qualityMetrics.qualityScore.toFixed(0)}%\r\n                      </text>\r\n                    </svg>\r\n                  </div>\r\n                  <h4>Overall Quality</h4>\r\n                </div>\r\n                \r\n                <div className=\"quality-breakdown\">\r\n                  <div className=\"breakdown-item\">\r\n                    <span className=\"breakdown-label\">Data Amount</span>\r\n                    <div className=\"breakdown-bar\">\r\n                      <div \r\n                        className=\"breakdown-fill\"\r\n                        style={{ width: `${Math.min(100, (qualityMetrics.totalExamples / 100) * 100)}%` }}\r\n                      ></div>\r\n                    </div>\r\n                    <span className=\"breakdown-value\">{qualityMetrics.totalExamples}/100</span>\r\n                  </div>\r\n                  \r\n                  <div className=\"breakdown-item\">\r\n                    <span className=\"breakdown-label\">Category Coverage</span>\r\n                    <div className=\"breakdown-bar\">\r\n                      <div \r\n                        className=\"breakdown-fill\"\r\n                        style={{ width: `${(qualityMetrics.categoriesWithData / categories.length) * 100}%` }}\r\n                      ></div>\r\n                    </div>\r\n                    <span className=\"breakdown-value\">\r\n                      {qualityMetrics.categoriesWithData}/{categories.length}\r\n                    </span>\r\n                  </div>\r\n                  \r\n                  <div className=\"breakdown-item\">\r\n                    <span className=\"breakdown-label\">Balance Score</span>\r\n                    <div className=\"breakdown-bar\">\r\n                      <div \r\n                        className=\"breakdown-fill\"\r\n                        style={{ width: `${qualityMetrics.balanceScore}%` }}\r\n                      ></div>\r\n                    </div>\r\n                    <span className=\"breakdown-value\">{qualityMetrics.balanceScore.toFixed(0)}%</span>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            <div className=\"category-distribution-chart\">\r\n              <h4>Training Data Distribution by Category</h4>\r\n              <div className=\"distribution-bars\">\r\n                {categories.map(category => {\r\n                  const count = trainingData.filter(td => td.categoryId === category.id).length;\r\n                  const percentage = qualityMetrics.totalExamples > 0 ? (count / qualityMetrics.totalExamples) * 100 : 0;\r\n                  \r\n                  return (\r\n                    <div key={category.id} className=\"distribution-bar-item\">\r\n                      <div className=\"bar-info\">\r\n                        <span className=\"bar-label\">{category.name}</span>\r\n                        <span className=\"bar-count\">{count}</span>\r\n                      </div>\r\n                      <div className=\"bar-container\">\r\n                        <div \r\n                          className=\"bar-fill\"\r\n                          style={{ \r\n                            width: `${percentage}%`,\r\n                            backgroundColor: category.color \r\n                          }}\r\n                        ></div>\r\n                      </div>\r\n                    </div>\r\n                  );\r\n                })}\r\n              </div>\r\n            </div>\r\n          </div>\r\n        )}\r\n      </div>\r\n    </div>\r\n  );\r\n};"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,QAAQ,OAAO;AAC/D,SAASC,eAAe,QAAQ,6BAA6B;AAC7D,SAASC,eAAe,QAAQ,6BAA6B;AAC7D,SAASC,yBAAyB,QAAQ,uCAAuC;AAEjF,OAAO,2BAA2B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAwBnC,OAAO,MAAMC,mBAAuD,GAAGA,CAAC;EAAEC;AAAQ,CAAC,KAAK;EAAAC,EAAA;EACtF;EACA,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGZ,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACa,KAAK,EAAEC,QAAQ,CAAC,GAAGd,QAAQ,CAAgB,IAAI,CAAC;EACvD,MAAM,CAACe,SAAS,EAAEC,YAAY,CAAC,GAAGhB,QAAQ,CAA+C,UAAU,CAAC;;EAEpG;EACA,MAAM,CAACiB,YAAY,EAAEC,eAAe,CAAC,GAAGlB,QAAQ,CAAiB,EAAE,CAAC;EACpE,MAAM,CAACmB,UAAU,EAAEC,aAAa,CAAC,GAAGpB,QAAQ,CAAwB,EAAE,CAAC;EACvE,MAAM,CAACqB,cAAc,EAAEC,iBAAiB,CAAC,GAAGtB,QAAQ,CAA4B,IAAI,CAAC;;EAErF;EACA,MAAM,CAACuB,YAAY,EAAEC,eAAe,CAAC,GAAGxB,QAAQ,CAAkC,QAAQ,CAAC;EAC3F,MAAM,CAACyB,cAAc,EAAEC,iBAAiB,CAAC,GAAG1B,QAAQ,CAAC;IACnD2B,WAAW,EAAE,KAAK;IAClBC,QAAQ,EAAE,CAAC;IACXC,WAAW,EAAE,EAAE;IACfC,MAAM,EAAE;EACV,CAAC,CAAC;;EAEF;EACA,MAAM,CAACC,eAAe,EAAEC,kBAAkB,CAAC,GAAGhC,QAAQ,CAAc,IAAIiC,GAAG,CAAC,CAAC,CAAC;EAC9E,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGnC,QAAQ,CAAC;IACrCoC,UAAU,EAAE,EAAE;IACdC,UAAU,EAAE,EAAE;IACdC,SAAS,EAAE;MAAEC,KAAK,EAAE,EAAE;MAAEC,GAAG,EAAE;IAAG;EAC9B,CAAC,CAAC;EACJ,MAAMC,UAAU,GAAG;IAAEC,KAAK,EAAE,aAAa;IAAEC,SAAS,EAAE;EAAyB,CAAC;EAChF,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAG7C,QAAQ,CAAC,CAAC,CAAC;EACnD,MAAM,CAAC8C,YAAY,CAAC,GAAG9C,QAAQ,CAAC,EAAE,CAAC;;EAEnC;EACA,MAAM,CAAC+C,WAAW,EAAEC,cAAc,CAAC,GAAGhD,QAAQ,CAAC;IAC7CiD,WAAW,EAAE,EAAE;IACfC,MAAM,EAAE,EAAE;IACVd,UAAU,EAAE;EACd,CAAC,CAAC;;EAEF;EACA,MAAMe,QAAQ,GAAGjD,WAAW,CAAC,YAAY;IACvC,IAAI;MACFU,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAAC,IAAI,CAAC;MAEd,MAAMG,YAAY,GAAGd,eAAe,CAACiD,eAAe,CAAC,CAAC;MACtD,MAAMjC,UAAU,GAAGf,eAAe,CAACiD,gBAAgB,CAAC,CAAC;MAErDnC,eAAe,CAACD,YAAY,CAAC;MAC7BG,aAAa,CAACD,UAAU,CAAC;;MAEzB;MACA,MAAMmC,OAAO,GAAGC,uBAAuB,CAACtC,YAAY,EAAEE,UAAU,CAAC;MACjEG,iBAAiB,CAACgC,OAAO,CAAC;IAC5B,CAAC,CAAC,OAAOE,GAAG,EAAE;MACZ1C,QAAQ,CAAC0C,GAAG,YAAYC,KAAK,GAAGD,GAAG,CAACE,OAAO,GAAG,8BAA8B,CAAC;IAC/E,CAAC,SAAS;MACR9C,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;;EAERX,SAAS,CAAC,MAAM;IACdkD,QAAQ,CAAC,CAAC;EACZ,CAAC,EAAE,CAACA,QAAQ,CAAC,CAAC;;EAEd;EACA,MAAMI,uBAAuB,GAAGA,CAACI,IAAoB,EAAEC,IAA2B,KAAyB;IACzG,MAAMC,aAAa,GAAGF,IAAI,CAACG,MAAM;;IAEjC;IACA,MAAMC,oBAAoB,GAAGH,IAAI,CAACI,GAAG,CAACC,QAAQ,KAAK;MACjD7B,UAAU,EAAE6B,QAAQ,CAACC,EAAE;MACvBC,KAAK,EAAER,IAAI,CAACS,MAAM,CAACC,EAAE,IAAIA,EAAE,CAACjC,UAAU,KAAK6B,QAAQ,CAACC,EAAE,CAAC,CAACJ;IAC1D,CAAC,CAAC,CAAC;IAEH,MAAMQ,kBAAkB,GAAGP,oBAAoB,CAACK,MAAM,CAACG,EAAE,IAAIA,EAAE,CAACJ,KAAK,GAAG,CAAC,CAAC,CAACL,MAAM;IACjF,MAAMU,0BAA0B,GAAGX,aAAa,GAAGY,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEJ,kBAAkB,CAAC;;IAElF;IACA,MAAMK,QAAQ,GAAGZ,oBAAoB,CAACa,MAAM,CAAC,CAACC,GAAG,EAAEC,GAAG,KAAK;MACzD,OAAOD,GAAG,GAAGJ,IAAI,CAACM,GAAG,CAACD,GAAG,CAACX,KAAK,GAAGK,0BAA0B,EAAE,CAAC,CAAC;IAClE,CAAC,EAAE,CAAC,CAAC,GAAGF,kBAAkB;IAE1B,MAAMU,WAAW,GAAGP,IAAI,CAACM,GAAG,CAACP,0BAA0B,EAAE,CAAC,CAAC;IAC3D,MAAMS,YAAY,GAAGR,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE,CAAC,GAAIC,QAAQ,GAAGK,WAAY,CAAC,GAAG,GAAG;;IAEpE;IACA,MAAME,UAAU,GAAGC,yBAAyB,CAACxB,IAAI,CAAC;;IAElD;IACA,MAAMyB,eAAe,GAAGX,IAAI,CAACY,GAAG,CAAC,CAAC,EAAExB,aAAa,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC;IAChE,MAAMyB,aAAa,GAAIhB,kBAAkB,GAAGV,IAAI,CAACE,MAAM,GAAI,GAAG;IAC9D,MAAMyB,YAAY,GAAIH,eAAe,GAAG,GAAG,GAAGH,YAAY,GAAG,GAAG,GAAGK,aAAa,GAAG,GAAI;;IAEvF;IACA,MAAME,eAAyB,GAAG,EAAE;IACpC,IAAI3B,aAAa,GAAG,EAAE,EAAE;MACtB2B,eAAe,CAACC,IAAI,CAAC,qEAAqE,CAAC;IAC7F;IACA,IAAInB,kBAAkB,GAAGV,IAAI,CAACE,MAAM,GAAG,GAAG,EAAE;MAC1C0B,eAAe,CAACC,IAAI,CAAC,oDAAoD,CAAC;IAC5E;IACA,IAAIR,YAAY,GAAG,EAAE,EAAE;MACrBO,eAAe,CAACC,IAAI,CAAC,sEAAsE,CAAC;IAC9F;IACA,IAAIP,UAAU,GAAGrB,aAAa,GAAG,GAAG,EAAE;MACpC2B,eAAe,CAACC,IAAI,CAAC,8DAA8D,CAAC;IACtF;IAEA,OAAO;MACL5B,aAAa;MACbS,kBAAkB;MAClBE,0BAA0B;MAC1BS,YAAY;MACZM,YAAY;MACZL,UAAU;MACVM;IACF,CAAC;EACH,CAAC;;EAED;EACA,MAAML,yBAAyB,GAAIxB,IAAoB,IAAa;IAClE,MAAM+B,IAAI,GAAG,IAAIzD,GAAG,CAAS,CAAC;IAC9B,IAAIiD,UAAU,GAAG,CAAC;IAElB,KAAK,MAAMS,IAAI,IAAIhC,IAAI,EAAE;MACvB,MAAMiC,GAAG,GAAG,GAAGD,IAAI,CAAC1C,WAAW,IAAI0C,IAAI,CAACzC,MAAM,IAAIyC,IAAI,CAACvD,UAAU,EAAE;MACnE,IAAIsD,IAAI,CAACG,GAAG,CAACD,GAAG,CAAC,EAAE;QACjBV,UAAU,EAAE;MACd,CAAC,MAAM;QACLQ,IAAI,CAACI,GAAG,CAACF,GAAG,CAAC;MACf;IACF;IAEA,OAAOV,UAAU;EACnB,CAAC;;EAED;EACA,MAAMa,sBAAsB,GAAGA,CAAA,KAAM;IACnC,IAAI,CAAChD,WAAW,CAACE,WAAW,CAAC+C,IAAI,CAAC,CAAC,IAAI,CAACjD,WAAW,CAACX,UAAU,EAAE;MAC9D6D,KAAK,CAAC,yCAAyC,CAAC;MAChD;IACF;IAEA,IAAI;MACF9F,eAAe,CAAC+F,eAAe,CAC7BnD,WAAW,CAACE,WAAW,CAAC+C,IAAI,CAAC,CAAC,EAC9BG,UAAU,CAACpD,WAAW,CAACG,MAAM,CAAC,IAAI,CAAC,EACnCH,WAAW,CAACX,UACd,CAAC;MAEDY,cAAc,CAAC;QAAEC,WAAW,EAAE,EAAE;QAAEC,MAAM,EAAE,EAAE;QAAEd,UAAU,EAAE;MAAG,CAAC,CAAC;MAC/De,QAAQ,CAAC,CAAC;MACV8C,KAAK,CAAC,qCAAqC,CAAC;IAC9C,CAAC,CAAC,OAAOpF,KAAK,EAAE;MACduF,OAAO,CAACvF,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACpDoF,KAAK,CAAC,gCAAgC,CAAC;IACzC;EACF,CAAC;EAED,MAAMI,0BAA0B,GAAG,MAAAA,CAAA,KAAY;IAC7C3E,iBAAiB,CAAC;MAChBC,WAAW,EAAE,IAAI;MACjBC,QAAQ,EAAE,CAAC;MACXC,WAAW,EAAE,sCAAsC;MACnDC,MAAM,EAAE;IACV,CAAC,CAAC;IAEF,IAAI;MACF;MACA,MAAMwE,eAAe,GAAGjG,yBAAyB,CAACkG,wBAAwB,CAAC,CAAC;MAC5E,MAAMC,eAAe,GAAGF,eAAe,CAAClC,MAAM,CAACqC,CAAC,IAAIA,CAAC,CAACC,gBAAgB,CAAC;MAEvE,IAAIF,eAAe,CAAC1C,MAAM,KAAK,CAAC,EAAE;QAChCmC,KAAK,CAAC,wFAAwF,CAAC;QAC/FvE,iBAAiB,CAACiF,IAAI,KAAK;UAAE,GAAGA,IAAI;UAAEhF,WAAW,EAAE;QAAM,CAAC,CAAC,CAAC;QAC5D;MACF;MAEAD,iBAAiB,CAACiF,IAAI,KAAK;QACzB,GAAGA,IAAI;QACP/E,QAAQ,EAAE,EAAE;QACZC,WAAW,EAAE;MACf,CAAC,CAAC,CAAC;MAEH,IAAI+E,QAAQ,GAAG,CAAC;MAChB,IAAIC,OAAO,GAAG,CAAC;MACf,MAAMC,MAAgB,GAAG,EAAE;MAE3B,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGP,eAAe,CAAC1C,MAAM,EAAEiD,CAAC,EAAE,EAAE;QAC/C,MAAMC,WAAW,GAAGR,eAAe,CAACO,CAAC,CAAC;QACtC,MAAM7D,MAAM,GAAG,CAAC8D,WAAW,CAACC,YAAY,IAAI,CAAC,KAAKD,WAAW,CAACE,WAAW,IAAI,CAAC,CAAC;QAE/E,IAAI;UACF;UACA,MAAMC,MAAM,GAAGlG,YAAY,CAACmG,IAAI,CAAC/C,EAAE,IACjCA,EAAE,CAACpB,WAAW,KAAK+D,WAAW,CAAC/D,WAAW,IAC1CwB,IAAI,CAAC4C,GAAG,CAAChD,EAAE,CAACnB,MAAM,GAAGA,MAAM,CAAC,GAAG,IAAI,IACnCmB,EAAE,CAACjC,UAAU,KAAK4E,WAAW,CAACN,gBAChC,CAAC;UAED,IAAI,CAACS,MAAM,EAAE;YACXhH,eAAe,CAAC+F,eAAe,CAC7Bc,WAAW,CAAC/D,WAAW,EACvBC,MAAM,EACN8D,WAAW,CAACN,gBACd,CAAC;YACDE,QAAQ,EAAE;UACZ,CAAC,MAAM;YACLC,OAAO,EAAE;UACX;QACF,CAAC,CAAC,OAAOhG,KAAK,EAAE;UACdiG,MAAM,CAACrB,IAAI,CAAC,qBAAqBuB,WAAW,CAAC/D,WAAW,MAAMpC,KAAK,EAAE,CAAC;QACxE;;QAEA;QACA,MAAMe,QAAQ,GAAG,EAAE,GAAI,CAACmF,CAAC,GAAG,CAAC,IAAIP,eAAe,CAAC1C,MAAM,GAAI,EAAE;QAC7DpC,iBAAiB,CAACiF,IAAI,KAAK;UACzB,GAAGA,IAAI;UACP/E,QAAQ;UACRC,WAAW,EAAE,0BAA0BkF,CAAC,GAAG,CAAC,OAAOP,eAAe,CAAC1C,MAAM;QAC3E,CAAC,CAAC,CAAC;MACL;MAEA,MAAMhC,MAAwB,GAAG;QAC/BwF,cAAc,EAAEd,eAAe,CAAC1C,MAAM;QACtC8C,QAAQ;QACRC,OAAO;QACPC,MAAM,EAAEA,MAAM,CAAChD,MAAM;QACrByD,YAAY,EAAET;MAChB,CAAC;MAEDpF,iBAAiB,CAACiF,IAAI,KAAK;QACzB,GAAGA,IAAI;QACPhF,WAAW,EAAE,KAAK;QAClBC,QAAQ,EAAE,GAAG;QACbC,WAAW,EAAE,kBAAkB;QAC/BC;MACF,CAAC,CAAC,CAAC;MAEH,MAAMqB,QAAQ,CAAC,CAAC;IAClB,CAAC,CAAC,OAAOtC,KAAK,EAAE;MACduF,OAAO,CAACvF,KAAK,CAAC,gBAAgB,EAAEA,KAAK,CAAC;MACtCa,iBAAiB,CAACiF,IAAI,KAAK;QACzB,GAAGA,IAAI;QACPhF,WAAW,EAAE,KAAK;QAClBE,WAAW,EAAE;MACf,CAAC,CAAC,CAAC;MACHoE,KAAK,CAAC,sDAAsD,CAAC;IAC/D;EACF,CAAC;EAED,MAAMuB,eAAe,GAAG,MAAOC,KAA0C,IAAK;IAAA,IAAAC,mBAAA;IAC5E,MAAMC,IAAI,IAAAD,mBAAA,GAAGD,KAAK,CAACG,MAAM,CAACC,KAAK,cAAAH,mBAAA,uBAAlBA,mBAAA,CAAqB,CAAC,CAAC;IACpC,IAAI,CAACC,IAAI,EAAE;IAEXjG,iBAAiB,CAAC;MAChBC,WAAW,EAAE,IAAI;MACjBC,QAAQ,EAAE,CAAC;MACXC,WAAW,EAAE,qBAAqB;MAClCC,MAAM,EAAE;IACV,CAAC,CAAC;IAEF,IAAI;MACF,MAAMgG,IAAI,GAAG,MAAMH,IAAI,CAACG,IAAI,CAAC,CAAC;MAC9B,MAAMC,KAAK,GAAGD,IAAI,CAACE,KAAK,CAAC,IAAI,CAAC,CAAC5D,MAAM,CAAC6D,IAAI,IAAIA,IAAI,CAACjC,IAAI,CAAC,CAAC,CAAC;MAE1D,IAAI+B,KAAK,CAACjE,MAAM,GAAG,CAAC,EAAE;QACpBmC,KAAK,CAAC,8DAA8D,CAAC;QACrEvE,iBAAiB,CAACiF,IAAI,KAAK;UAAE,GAAGA,IAAI;UAAEhF,WAAW,EAAE;QAAM,CAAC,CAAC,CAAC;QAC5D;MACF;MAEAD,iBAAiB,CAACiF,IAAI,KAAK;QACzB,GAAGA,IAAI;QACP/E,QAAQ,EAAE,EAAE;QACZC,WAAW,EAAE;MACf,CAAC,CAAC,CAAC;MAEH,MAAMqG,OAAO,GAAGH,KAAK,CAAC,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAChE,GAAG,CAACmE,CAAC,IAAIA,CAAC,CAACnC,IAAI,CAAC,CAAC,CAACoC,WAAW,CAAC,CAAC,CAAC;MACpE,MAAMC,cAAc,GAAGH,OAAO,CAACI,SAAS,CAACH,CAAC,IAAIA,CAAC,CAACI,QAAQ,CAAC,aAAa,CAAC,CAAC;MACxE,MAAMC,SAAS,GAAGN,OAAO,CAACI,SAAS,CAACH,CAAC,IAAIA,CAAC,CAACI,QAAQ,CAAC,QAAQ,CAAC,CAAC;MAC9D,MAAME,WAAW,GAAGP,OAAO,CAACI,SAAS,CAACH,CAAC,IAAIA,CAAC,CAACI,QAAQ,CAAC,UAAU,CAAC,CAAC;MAElE,IAAIF,cAAc,KAAK,CAAC,CAAC,IAAIG,SAAS,KAAK,CAAC,CAAC,IAAIC,WAAW,KAAK,CAAC,CAAC,EAAE;QACnExC,KAAK,CAAC,gEAAgE,CAAC;QACvEvE,iBAAiB,CAACiF,IAAI,KAAK;UAAE,GAAGA,IAAI;UAAEhF,WAAW,EAAE;QAAM,CAAC,CAAC,CAAC;QAC5D;MACF;MAEA,IAAIiF,QAAQ,GAAG,CAAC;MAChB,IAAIC,OAAO,GAAG,CAAC;MACf,MAAMC,MAAgB,GAAG,EAAE;MAE3B,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGgB,KAAK,CAACjE,MAAM,EAAEiD,CAAC,EAAE,EAAE;QACrC,MAAM2B,IAAI,GAAGX,KAAK,CAAChB,CAAC,CAAC,CAACiB,KAAK,CAAC,GAAG,CAAC,CAAChE,GAAG,CAAC2E,CAAC,IAAIA,CAAC,CAAC3C,IAAI,CAAC,CAAC,CAAC;QAEnD,IAAI;UAAA,IAAA4C,oBAAA,EAAAC,eAAA,EAAAC,iBAAA;UACF,MAAM7F,WAAW,GAAG,EAAA2F,oBAAA,GAAAF,IAAI,CAACL,cAAc,CAAC,cAAAO,oBAAA,uBAApBA,oBAAA,CAAsBG,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC,KAAI,EAAE;UACjE,MAAM7F,MAAM,GAAGiD,UAAU,CAAC,EAAA0C,eAAA,GAAAH,IAAI,CAACF,SAAS,CAAC,cAAAK,eAAA,uBAAfA,eAAA,CAAiBE,OAAO,CAAC,UAAU,EAAE,EAAE,CAAC,KAAI,GAAG,CAAC;UAC1E,MAAMC,YAAY,GAAG,EAAAF,iBAAA,GAAAJ,IAAI,CAACD,WAAW,CAAC,cAAAK,iBAAA,uBAAjBA,iBAAA,CAAmBC,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC,KAAI,EAAE;;UAE/D;UACA,MAAM9E,QAAQ,GAAG9C,UAAU,CAAC8H,IAAI,CAACN,CAAC,IAChCA,CAAC,CAACO,IAAI,CAACd,WAAW,CAAC,CAAC,KAAKY,YAAY,CAACZ,WAAW,CAAC,CACpD,CAAC;UAED,IAAI,CAACnE,QAAQ,EAAE;YACb6C,MAAM,CAACrB,IAAI,CAAC,QAAQsB,CAAC,GAAG,CAAC,eAAeiC,YAAY,aAAa,CAAC;YAClE;UACF;UAEA,IAAI,CAAC/F,WAAW,EAAE;YAChB6D,MAAM,CAACrB,IAAI,CAAC,QAAQsB,CAAC,GAAG,CAAC,2BAA2B,CAAC;YACrD;UACF;;UAEA;UACA,MAAMI,MAAM,GAAGlG,YAAY,CAACmG,IAAI,CAAC/C,EAAE,IACjCA,EAAE,CAACpB,WAAW,KAAKA,WAAW,IAC9BwB,IAAI,CAAC4C,GAAG,CAAChD,EAAE,CAACnB,MAAM,GAAGA,MAAM,CAAC,GAAG,IAAI,IACnCmB,EAAE,CAACjC,UAAU,KAAK6B,QAAQ,CAACC,EAC7B,CAAC;UAED,IAAI,CAACiD,MAAM,EAAE;YACXhH,eAAe,CAAC+F,eAAe,CAACjD,WAAW,EAAEC,MAAM,EAAEe,QAAQ,CAACC,EAAE,CAAC;YACjE0C,QAAQ,EAAE;UACZ,CAAC,MAAM;YACLC,OAAO,EAAE;UACX;QACF,CAAC,CAAC,OAAOhG,KAAK,EAAE;UACdiG,MAAM,CAACrB,IAAI,CAAC,QAAQsB,CAAC,GAAG,CAAC,KAAKlG,KAAK,EAAE,CAAC;QACxC;;QAEA;QACA,MAAMe,QAAQ,GAAG,EAAE,GAAKmF,CAAC,IAAKgB,KAAK,CAACjE,MAAM,GAAG,CAAC,CAAC,GAAI,EAAE;QACrDpC,iBAAiB,CAACiF,IAAI,KAAK;UACzB,GAAGA,IAAI;UACP/E,QAAQ;UACRC,WAAW,EAAE,kBAAkBkF,CAAC,OAAOgB,KAAK,CAACjE,MAAM,GAAG,CAAC;QACzD,CAAC,CAAC,CAAC;MACL;MAEA,MAAMhC,MAAwB,GAAG;QAC/BwF,cAAc,EAAES,KAAK,CAACjE,MAAM,GAAG,CAAC;QAChC8C,QAAQ;QACRC,OAAO;QACPC,MAAM,EAAEA,MAAM,CAAChD,MAAM;QACrByD,YAAY,EAAET;MAChB,CAAC;MAEDpF,iBAAiB,CAACiF,IAAI,KAAK;QACzB,GAAGA,IAAI;QACPhF,WAAW,EAAE,KAAK;QAClBC,QAAQ,EAAE,GAAG;QACbC,WAAW,EAAE,kBAAkB;QAC/BC;MACF,CAAC,CAAC,CAAC;MAEH,MAAMqB,QAAQ,CAAC,CAAC;IAClB,CAAC,CAAC,OAAOtC,KAAK,EAAE;MACduF,OAAO,CAACvF,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;MAC1Ca,iBAAiB,CAACiF,IAAI,KAAK;QACzB,GAAGA,IAAI;QACPhF,WAAW,EAAE,KAAK;QAClBE,WAAW,EAAE;MACf,CAAC,CAAC,CAAC;MACHoE,KAAK,CAAC,0DAA0D,CAAC;IACnE;;IAEA;IACAwB,KAAK,CAACG,MAAM,CAACuB,KAAK,GAAG,EAAE;EACzB,CAAC;;EAED;EACA,MAAMC,gBAAgB,GAAIC,MAAc,IAAK;IAC3CrH,kBAAkB,CAAC2E,IAAI,IAAI;MACzB,MAAM2C,MAAM,GAAG,IAAIrH,GAAG,CAAC0E,IAAI,CAAC;MAC5B,IAAI2C,MAAM,CAACzD,GAAG,CAACwD,MAAM,CAAC,EAAE;QACtBC,MAAM,CAACC,MAAM,CAACF,MAAM,CAAC;MACvB,CAAC,MAAM;QACLC,MAAM,CAACxD,GAAG,CAACuD,MAAM,CAAC;MACpB;MACA,OAAOC,MAAM;IACf,CAAC,CAAC;EACJ,CAAC;EAED,MAAME,eAAe,GAAGA,CAAA,KAAM;IAC5B,MAAMC,YAAY,GAAGC,wBAAwB,CAAC,CAAC;IAC/C,IAAI3H,eAAe,CAAC4H,IAAI,KAAKF,YAAY,CAAC3F,MAAM,EAAE;MAChD9B,kBAAkB,CAAC,IAAIC,GAAG,CAAC,CAAC,CAAC;IAC/B,CAAC,MAAM;MACLD,kBAAkB,CAAC,IAAIC,GAAG,CAACwH,YAAY,CAACzF,GAAG,CAACK,EAAE,IAAIA,EAAE,CAACH,EAAE,CAAC,CAAC,CAAC;IAC5D;EACF,CAAC;EAED,MAAM0F,oBAAoB,GAAGA,CAAA,KAAM;IACjC,IAAI7H,eAAe,CAAC4H,IAAI,KAAK,CAAC,EAAE;IAEhC,MAAME,SAAS,GAAGC,MAAM,CAACC,OAAO,CAAC,UAAUhI,eAAe,CAAC4H,IAAI,4CAA4C,CAAC;IAC5G,IAAIE,SAAS,EAAE;MACb;MACA7H,kBAAkB,CAAC,IAAIC,GAAG,CAAC,CAAC,CAAC;MAC7BgE,KAAK,CAAC,gEAAgE,CAAC;IACzE;EACF,CAAC;EAED,MAAM+D,kBAAkB,GAAGA,CAAA,KAAM;IAC/B,MAAMH,SAAS,GAAGC,MAAM,CAACC,OAAO,CAAC,iDAAiD,CAAC;IACnF,IAAIF,SAAS,EAAE;MACb1J,eAAe,CAAC8J,iBAAiB,CAAC,CAAC;MACnC9G,QAAQ,CAAC,CAAC;IACZ;EACF,CAAC;;EAED;EACA,MAAMuG,wBAAwB,GAAGA,CAAA,KAAsB;IACrD,IAAIQ,QAAQ,GAAGjJ,YAAY;;IAE3B;IACA,IAAIiB,OAAO,CAACE,UAAU,EAAE;MACtB8H,QAAQ,GAAGA,QAAQ,CAAC9F,MAAM,CAACC,EAAE,IAAIA,EAAE,CAACjC,UAAU,KAAKF,OAAO,CAACE,UAAU,CAAC;IACxE;IAEA,IAAIF,OAAO,CAACG,UAAU,EAAE;MACtB,MAAM8H,WAAW,GAAGjI,OAAO,CAACG,UAAU,CAAC+F,WAAW,CAAC,CAAC;MACpD8B,QAAQ,GAAGA,QAAQ,CAAC9F,MAAM,CAACC,EAAE,IAC3BA,EAAE,CAACpB,WAAW,CAACmF,WAAW,CAAC,CAAC,CAACG,QAAQ,CAAC4B,WAAW,CACnD,CAAC;IACH;IAEA,IAAIjI,OAAO,CAACI,SAAS,CAACC,KAAK,IAAIL,OAAO,CAACI,SAAS,CAACE,GAAG,EAAE;MACpD0H,QAAQ,GAAGA,QAAQ,CAAC9F,MAAM,CAACC,EAAE,IAAI;QAC/B,MAAM+F,IAAI,GAAG,IAAIC,IAAI,CAAChG,EAAE,CAACiG,WAAW,CAAC;QACrC,MAAM/H,KAAK,GAAGL,OAAO,CAACI,SAAS,CAACC,KAAK,GAAG,IAAI8H,IAAI,CAACnI,OAAO,CAACI,SAAS,CAACC,KAAK,CAAC,GAAG,IAAI;QAChF,MAAMC,GAAG,GAAGN,OAAO,CAACI,SAAS,CAACE,GAAG,GAAG,IAAI6H,IAAI,CAACnI,OAAO,CAACI,SAAS,CAACE,GAAG,CAAC,GAAG,IAAI;QAE1E,IAAID,KAAK,IAAI6H,IAAI,GAAG7H,KAAK,EAAE,OAAO,KAAK;QACvC,IAAIC,GAAG,IAAI4H,IAAI,GAAG5H,GAAG,EAAE,OAAO,KAAK;QACnC,OAAO,IAAI;MACb,CAAC,CAAC;IACJ;;IAEA;IACA0H,QAAQ,CAACK,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;MACtB,IAAIC,MAAW,GAAGF,CAAC,CAAC/H,UAAU,CAACC,KAAK,CAAuB;MAC3D,IAAIiI,MAAW,GAAGF,CAAC,CAAChI,UAAU,CAACC,KAAK,CAAuB;MAE3D,IAAID,UAAU,CAACC,KAAK,KAAK,aAAa,EAAE;QACtCgI,MAAM,GAAG,IAAIL,IAAI,CAACK,MAAM,CAAC,CAACE,OAAO,CAAC,CAAC;QACnCD,MAAM,GAAG,IAAIN,IAAI,CAACM,MAAM,CAAC,CAACC,OAAO,CAAC,CAAC;MACrC;MAEA,IAAIF,MAAM,GAAGC,MAAM,EAAE,OAAOlI,UAAU,CAACE,SAAS,KAAK,KAAK,GAAG,CAAC,CAAC,GAAG,CAAC;MACnE,IAAI+H,MAAM,GAAGC,MAAM,EAAE,OAAOlI,UAAU,CAACE,SAAS,KAAK,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC;MACnE,OAAO,CAAC;IACV,CAAC,CAAC;IAEF,OAAOuH,QAAQ;EACjB,CAAC;;EAED;EACA,MAAMW,gBAAgB,GAAGA,CAAA,KAAsB;IAC7C,MAAMX,QAAQ,GAAGR,wBAAwB,CAAC,CAAC;IAC3C,MAAMoB,UAAU,GAAG,CAAClI,WAAW,GAAG,CAAC,IAAIE,YAAY;IACnD,OAAOoH,QAAQ,CAACa,KAAK,CAACD,UAAU,EAAEA,UAAU,GAAGhI,YAAY,CAAC;EAC9D,CAAC;EAED,MAAMkI,aAAa,GAAGA,CAAA,KAAc;IAClC,MAAMC,aAAa,GAAGvB,wBAAwB,CAAC,CAAC,CAAC5F,MAAM;IACvD,OAAOW,IAAI,CAACyG,IAAI,CAACD,aAAa,GAAGnI,YAAY,CAAC;EAChD,CAAC;;EAED;EACA,MAAMqI,eAAe,GAAI/I,UAAkB,IAAa;IACtD,MAAM6B,QAAQ,GAAG9C,UAAU,CAAC8H,IAAI,CAACN,CAAC,IAAIA,CAAC,CAACzE,EAAE,KAAK9B,UAAU,CAAC;IAC1D,OAAO,CAAA6B,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEiF,IAAI,KAAI,kBAAkB;EAC7C,CAAC;EAED,MAAMkC,YAAY,GAAIlI,MAAc,IAAa;IAC/C,OAAO,IAAImI,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;MACpCC,KAAK,EAAE,UAAU;MACjBC,QAAQ,EAAE;IACZ,CAAC,CAAC,CAACC,MAAM,CAACvI,MAAM,CAAC;EACnB,CAAC;EAED,IAAIvC,OAAO,EAAE;IACX,oBACEJ,OAAA;MAAKmL,SAAS,EAAC,uBAAuB;MAAAC,QAAA,gBACpCpL,OAAA;QAAKmL,SAAS,EAAC;MAAiB;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACvCxL,OAAA;QAAAoL,QAAA,EAAG;MAAgC;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACpC,CAAC;EAEV;EAEA,IAAIlL,KAAK,EAAE;IACT,oBACEN,OAAA;MAAKmL,SAAS,EAAC,qBAAqB;MAAAC,QAAA,gBAClCpL,OAAA;QAAKmL,SAAS,EAAC,YAAY;QAAAC,QAAA,EAAC;MAAE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eACpCxL,OAAA;QAAAoL,QAAA,EAAI;MAA2B;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACpCxL,OAAA;QAAAoL,QAAA,EAAI9K;MAAK;QAAA+K,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACdxL,OAAA;QAAQyL,OAAO,EAAE7I,QAAS;QAACuI,SAAS,EAAC,iBAAiB;QAAAC,QAAA,EAAC;MAEvD;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAEV;EAEA,oBACExL,OAAA;IAAKmL,SAAS,EAAC,uBAAuB;IAAAC,QAAA,gBAEpCpL,OAAA;MAAKmL,SAAS,EAAC,sBAAsB;MAAAC,QAAA,gBACnCpL,OAAA;QAAKmL,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC3BpL,OAAA;UAAAoL,QAAA,EAAI;QAAqB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC9BxL,OAAA;UAAAoL,QAAA,EAAG;QAA2D;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/D,CAAC,EACLtL,OAAO,iBACNF,OAAA;QAAQyL,OAAO,EAAEvL,OAAQ;QAACiL,SAAS,EAAC,WAAW;QAAAC,QAAA,eAC7CpL,OAAA;UAAK0L,KAAK,EAAC,IAAI;UAACC,MAAM,EAAC,IAAI;UAACC,OAAO,EAAC,WAAW;UAACC,IAAI,EAAC,MAAM;UAACC,MAAM,EAAC,cAAc;UAACC,WAAW,EAAC,GAAG;UAAAX,QAAA,gBAC/FpL,OAAA;YAAMgM,EAAE,EAAC,IAAI;YAACC,EAAE,EAAC,GAAG;YAACC,EAAE,EAAC,GAAG;YAACC,EAAE,EAAC;UAAI;YAAAd,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAC3CxL,OAAA;YAAMgM,EAAE,EAAC,GAAG;YAACC,EAAE,EAAC,GAAG;YAACC,EAAE,EAAC,IAAI;YAACC,EAAE,EAAC;UAAI;YAAAd,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CACT;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAGNxL,OAAA;MAAKmL,SAAS,EAAC,oBAAoB;MAAAC,QAAA,EAChC,CACC;QAAEzH,EAAE,EAAE,UAAU;QAAEyI,KAAK,EAAE,UAAU;QAAEC,IAAI,EAAE;MAAK,CAAC,EACjD;QAAE1I,EAAE,EAAE,QAAQ;QAAEyI,KAAK,EAAE,aAAa;QAAEC,IAAI,EAAE;MAAK,CAAC,EAClD;QAAE1I,EAAE,EAAE,QAAQ;QAAEyI,KAAK,EAAE,aAAa;QAAEC,IAAI,EAAE;MAAK,CAAC,EAClD;QAAE1I,EAAE,EAAE,SAAS;QAAEyI,KAAK,EAAE,cAAc;QAAEC,IAAI,EAAE;MAAK,CAAC,CACrD,CAAC5I,GAAG,CAAC6I,GAAG,iBACPtM,OAAA;QAEEmL,SAAS,EAAE,WAAW3K,SAAS,KAAK8L,GAAG,CAAC3I,EAAE,GAAG,QAAQ,GAAG,EAAE,EAAG;QAC7D8H,OAAO,EAAEA,CAAA,KAAMhL,YAAY,CAAC6L,GAAG,CAAC3I,EAAS,CAAE;QAAAyH,QAAA,gBAE3CpL,OAAA;UAAMmL,SAAS,EAAC,UAAU;UAAAC,QAAA,EAAEkB,GAAG,CAACD;QAAI;UAAAhB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eAC5CxL,OAAA;UAAMmL,SAAS,EAAC,WAAW;UAAAC,QAAA,EAAEkB,GAAG,CAACF;QAAK;UAAAf,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA,GALzCc,GAAG,CAAC3I,EAAE;QAAA0H,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAML,CACT;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGNxL,OAAA;MAAKmL,SAAS,EAAC,uBAAuB;MAAAC,QAAA,GAEnC5K,SAAS,KAAK,UAAU,IAAIM,cAAc,iBACzCd,OAAA;QAAKmL,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC3BpL,OAAA;UAAKmL,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAC7BpL,OAAA;YAAKmL,SAAS,EAAC,eAAe;YAAAC,QAAA,gBAC5BpL,OAAA;cAAKmL,SAAS,EAAC,aAAa;cAAAC,QAAA,eAC1BpL,OAAA;gBAAAoL,QAAA,EAAI;cAAe;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrB,CAAC,eACNxL,OAAA;cAAKmL,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC3BpL,OAAA;gBAAKmL,SAAS,EAAC,QAAQ;gBAAAC,QAAA,gBACrBpL,OAAA;kBAAMmL,SAAS,EAAC,cAAc;kBAAAC,QAAA,EAAC;gBAAc;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACpDxL,OAAA;kBAAMmL,SAAS,EAAC,cAAc;kBAAAC,QAAA,EAAEtK,cAAc,CAACwC;gBAAa;kBAAA+H,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjE,CAAC,eACNxL,OAAA;gBAAKmL,SAAS,EAAC,QAAQ;gBAAAC,QAAA,gBACrBpL,OAAA;kBAAMmL,SAAS,EAAC,cAAc;kBAAAC,QAAA,EAAC;gBAAoB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC1DxL,OAAA;kBAAMmL,SAAS,EAAC,cAAc;kBAAAC,QAAA,GAC3BtK,cAAc,CAACiD,kBAAkB,EAAC,KAAG,EAACnD,UAAU,CAAC2C,MAAM;gBAAA;kBAAA8H,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACNxL,OAAA;gBAAKmL,SAAS,EAAC,QAAQ;gBAAAC,QAAA,gBACrBpL,OAAA;kBAAMmL,SAAS,EAAC,cAAc;kBAAAC,QAAA,EAAC;gBAAgB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACtDxL,OAAA;kBAAMmL,SAAS,EAAC,cAAc;kBAAAC,QAAA,EAAEtK,cAAc,CAACmD,0BAA0B,CAACsI,OAAO,CAAC,CAAC;gBAAC;kBAAAlB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENxL,OAAA;YAAKmL,SAAS,EAAC,eAAe;YAAAC,QAAA,gBAC5BpL,OAAA;cAAKmL,SAAS,EAAC,aAAa;cAAAC,QAAA,eAC1BpL,OAAA;gBAAAoL,QAAA,EAAI;cAAY;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClB,CAAC,eACNxL,OAAA;cAAKmL,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC3BpL,OAAA;gBAAKmL,SAAS,EAAC,QAAQ;gBAAAC,QAAA,gBACrBpL,OAAA;kBAAMmL,SAAS,EAAC,cAAc;kBAAAC,QAAA,EAAC;gBAAe;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACrDxL,OAAA;kBAAMmL,SAAS,EAAE,wBAAwBrK,cAAc,CAACkE,YAAY,GAAG,EAAE,GAAG,MAAM,GAAGlE,cAAc,CAACkE,YAAY,GAAG,EAAE,GAAG,MAAM,GAAG,MAAM,EAAG;kBAAAoG,QAAA,GACvItK,cAAc,CAACkE,YAAY,CAACuH,OAAO,CAAC,CAAC,CAAC,EAAC,GAC1C;gBAAA;kBAAAlB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACNxL,OAAA;gBAAKmL,SAAS,EAAC,QAAQ;gBAAAC,QAAA,gBACrBpL,OAAA;kBAAMmL,SAAS,EAAC,cAAc;kBAAAC,QAAA,EAAC;gBAAa;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACnDxL,OAAA;kBAAMmL,SAAS,EAAE,wBAAwBrK,cAAc,CAAC4D,YAAY,GAAG,EAAE,GAAG,MAAM,GAAG5D,cAAc,CAAC4D,YAAY,GAAG,EAAE,GAAG,MAAM,GAAG,MAAM,EAAG;kBAAA0G,QAAA,GACvItK,cAAc,CAAC4D,YAAY,CAAC6H,OAAO,CAAC,CAAC,CAAC,EAAC,GAC1C;gBAAA;kBAAAlB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACNxL,OAAA;gBAAKmL,SAAS,EAAC,QAAQ;gBAAAC,QAAA,gBACrBpL,OAAA;kBAAMmL,SAAS,EAAC,cAAc;kBAAAC,QAAA,EAAC;gBAAU;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAChDxL,OAAA;kBAAMmL,SAAS,EAAC,cAAc;kBAAAC,QAAA,EAAEtK,cAAc,CAAC6D;gBAAU;kBAAA0G,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9D,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENxL,OAAA;YAAKmL,SAAS,EAAC,eAAe;YAAAC,QAAA,gBAC5BpL,OAAA;cAAKmL,SAAS,EAAC,aAAa;cAAAC,QAAA,eAC1BpL,OAAA;gBAAAoL,QAAA,EAAI;cAAa;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnB,CAAC,eACNxL,OAAA;cAAKmL,SAAS,EAAC,cAAc;cAAAC,QAAA,eAC3BpL,OAAA;gBAAKmL,SAAS,EAAC,gBAAgB;gBAAAC,QAAA,gBAC7BpL,OAAA;kBACEyL,OAAO,EAAEA,CAAA,KAAMhL,YAAY,CAAC,QAAQ,CAAE;kBACtC0K,SAAS,EAAC,iBAAiB;kBAAAC,QAAA,EAC5B;gBAED;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACTxL,OAAA;kBACEyL,OAAO,EAAEA,CAAA,KAAMhL,YAAY,CAAC,SAAS,CAAE;kBACvC0K,SAAS,EAAC,mBAAmB;kBAAAC,QAAA,EAC9B;gBAED;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,EAEL1K,cAAc,CAACmE,eAAe,CAAC1B,MAAM,GAAG,CAAC,iBACxCvD,OAAA;UAAKmL,SAAS,EAAC,sBAAsB;UAAAC,QAAA,gBACnCpL,OAAA;YAAKmL,SAAS,EAAC,aAAa;YAAAC,QAAA,eAC1BpL,OAAA;cAAAoL,QAAA,EAAI;YAAe;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrB,CAAC,eACNxL,OAAA;YAAKmL,SAAS,EAAC,cAAc;YAAAC,QAAA,eAC3BpL,OAAA;cAAImL,SAAS,EAAC,sBAAsB;cAAAC,QAAA,EACjCtK,cAAc,CAACmE,eAAe,CAACxB,GAAG,CAAC,CAAC+I,GAAG,EAAEC,KAAK,kBAC7CzM,OAAA;gBAAAoL,QAAA,EAAiBoB;cAAG,GAAXC,KAAK;gBAAApB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAC1B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CACN,EAGAhL,SAAS,KAAK,QAAQ,iBACrBR,OAAA;QAAKmL,SAAS,EAAC,YAAY;QAAAC,QAAA,gBACzBpL,OAAA;UAAKmL,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAC7BpL,OAAA;YAAKmL,SAAS,EAAC,iBAAiB;YAAAC,QAAA,gBAC9BpL,OAAA;cAAAoL,QAAA,EAAI;YAAa;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACtBxL,OAAA;cAAKmL,SAAS,EAAC,gBAAgB;cAAAC,QAAA,EAC5B,CACC;gBAAEzH,EAAE,EAAE,QAAQ;gBAAEyI,KAAK,EAAE,cAAc;gBAAEC,IAAI,EAAE,IAAI;gBAAE3J,WAAW,EAAE;cAAmC,CAAC,EACpG;gBAAEiB,EAAE,EAAE,YAAY;gBAAEyI,KAAK,EAAE,iBAAiB;gBAAEC,IAAI,EAAE,IAAI;gBAAE3J,WAAW,EAAE;cAAuC,CAAC,EAC/G;gBAAEiB,EAAE,EAAE,KAAK;gBAAEyI,KAAK,EAAE,UAAU;gBAAEC,IAAI,EAAE,IAAI;gBAAE3J,WAAW,EAAE;cAAqC,CAAC,CAChG,CAACe,GAAG,CAACiJ,MAAM,iBACV1M,OAAA;gBAEEmL,SAAS,EAAE,iBAAiBnK,YAAY,KAAK0L,MAAM,CAAC/I,EAAE,GAAG,QAAQ,GAAG,EAAE,EAAG;gBACzE8H,OAAO,EAAEA,CAAA,KAAMxK,eAAe,CAACyL,MAAM,CAAC/I,EAAS,CAAE;gBAAAyH,QAAA,gBAEjDpL,OAAA;kBAAMmL,SAAS,EAAC,aAAa;kBAAAC,QAAA,EAAEsB,MAAM,CAACL;gBAAI;kBAAAhB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAClDxL,OAAA;kBAAKmL,SAAS,EAAC,aAAa;kBAAAC,QAAA,gBAC1BpL,OAAA;oBAAMmL,SAAS,EAAC,cAAc;oBAAAC,QAAA,EAAEsB,MAAM,CAACN;kBAAK;oBAAAf,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACpDxL,OAAA;oBAAMmL,SAAS,EAAC,oBAAoB;oBAAAC,QAAA,EAAEsB,MAAM,CAAChK;kBAAW;oBAAA2I,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7D,CAAC;cAAA,GARDkB,MAAM,CAAC/I,EAAE;gBAAA0H,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OASR,CACT;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENxL,OAAA;YAAKmL,SAAS,EAAC,gBAAgB;YAAAC,QAAA,GAE5BpK,YAAY,KAAK,QAAQ,iBACxBhB,OAAA;cAAKmL,SAAS,EAAC,eAAe;cAAAC,QAAA,gBAC5BpL,OAAA;gBAAAoL,QAAA,EAAI;cAAoB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC7BxL,OAAA;gBAAKmL,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBpL,OAAA;kBAAO2M,OAAO,EAAC,mBAAmB;kBAAAvB,QAAA,EAAC;gBAAW;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACtDxL,OAAA;kBACE2D,EAAE,EAAC,mBAAmB;kBACtBiJ,IAAI,EAAC,MAAM;kBACXhE,KAAK,EAAEpG,WAAW,CAACE,WAAY;kBAC/BmK,QAAQ,EAAGC,CAAC,IAAKrK,cAAc,CAAC2D,IAAI,KAAK;oBAAE,GAAGA,IAAI;oBAAE1D,WAAW,EAAEoK,CAAC,CAACzF,MAAM,CAACuB;kBAAM,CAAC,CAAC,CAAE;kBACpFmE,WAAW,EAAC;gBAAuB;kBAAA1B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNxL,OAAA;gBAAKmL,SAAS,EAAC,UAAU;gBAAAC,QAAA,gBACvBpL,OAAA;kBAAKmL,SAAS,EAAC,YAAY;kBAAAC,QAAA,gBACzBpL,OAAA;oBAAO2M,OAAO,EAAC,cAAc;oBAAAvB,QAAA,EAAC;kBAAM;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eAC5CxL,OAAA;oBACE2D,EAAE,EAAC,cAAc;oBACjBiJ,IAAI,EAAC,QAAQ;oBACbI,IAAI,EAAC,MAAM;oBACXpE,KAAK,EAAEpG,WAAW,CAACG,MAAO;oBAC1BkK,QAAQ,EAAGC,CAAC,IAAKrK,cAAc,CAAC2D,IAAI,KAAK;sBAAE,GAAGA,IAAI;sBAAEzD,MAAM,EAAEmK,CAAC,CAACzF,MAAM,CAACuB;oBAAM,CAAC,CAAC,CAAE;oBAC/EmE,WAAW,EAAC;kBAAM;oBAAA1B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnB,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eACNxL,OAAA;kBAAKmL,SAAS,EAAC,YAAY;kBAAAC,QAAA,gBACzBpL,OAAA;oBAAO2M,OAAO,EAAC,gBAAgB;oBAAAvB,QAAA,EAAC;kBAAQ;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eAChDxL,OAAA;oBACE2D,EAAE,EAAC,gBAAgB;oBACnBiF,KAAK,EAAEpG,WAAW,CAACX,UAAW;oBAC9BgL,QAAQ,EAAGC,CAAC,IAAKrK,cAAc,CAAC2D,IAAI,KAAK;sBAAE,GAAGA,IAAI;sBAAEvE,UAAU,EAAEiL,CAAC,CAACzF,MAAM,CAACuB;oBAAM,CAAC,CAAC,CAAE;oBAAAwC,QAAA,gBAEnFpL,OAAA;sBAAQ4I,KAAK,EAAC,EAAE;sBAAAwC,QAAA,EAAC;oBAAe;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,EACxC5K,UAAU,CAAC6C,GAAG,CAACC,QAAQ,iBACtB1D,OAAA;sBAA0B4I,KAAK,EAAElF,QAAQ,CAACC,EAAG;sBAAAyH,QAAA,EAC1C1H,QAAQ,CAACiF;oBAAI,GADHjF,QAAQ,CAACC,EAAE;sBAAA0H,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAEhB,CACT,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNxL,OAAA;gBACEyL,OAAO,EAAEjG,sBAAuB;gBAChC2F,SAAS,EAAC,iBAAiB;gBAC3B8B,QAAQ,EAAE,CAACzK,WAAW,CAACE,WAAW,CAAC+C,IAAI,CAAC,CAAC,IAAI,CAACjD,WAAW,CAACX,UAAW;gBAAAuJ,QAAA,EACtE;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CACN,EAGAxK,YAAY,KAAK,YAAY,iBAC5BhB,OAAA;cAAKmL,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAChCpL,OAAA;gBAAAoL,QAAA,EAAI;cAAmC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC5CxL,OAAA;gBAAAoL,QAAA,EAAG;cAGH;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eACJxL,OAAA;gBACEyL,OAAO,EAAE3F,0BAA2B;gBACpCmH,QAAQ,EAAE/L,cAAc,CAACE,WAAY;gBACrC+J,SAAS,EAAC,iBAAiB;gBAAAC,QAAA,EAE1BlK,cAAc,CAACE,WAAW,GAAG,cAAc,GAAG;cAAc;gBAAAiK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CACN,EAGAxK,YAAY,KAAK,KAAK,iBACrBhB,OAAA;cAAKmL,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzBpL,OAAA;gBAAAoL,QAAA,EAAI;cAAoB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC7BxL,OAAA;gBAAAoL,QAAA,EAAG;cAGH;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eACJxL,OAAA;gBAAKmL,SAAS,EAAC,oBAAoB;gBAAAC,QAAA,gBACjCpL,OAAA;kBAAAoL,QAAA,EAAI;gBAAoB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC7BxL,OAAA;kBAAAoL,QAAA,GAAK,6BACwB,EAAC,IAAI,EAAC,yCACE,EAAC,IAAI,EAAC,uCACR,EAAC,IAAI,EAAC,yCAEzC;gBAAA;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNxL,OAAA;gBAAKmL,SAAS,EAAC,sBAAsB;gBAAAC,QAAA,gBACnCpL,OAAA;kBACE4M,IAAI,EAAC,MAAM;kBACXM,MAAM,EAAC,MAAM;kBACbL,QAAQ,EAAE5F,eAAgB;kBAC1BgG,QAAQ,EAAE/L,cAAc,CAACE,WAAY;kBACrC+J,SAAS,EAAC,YAAY;kBACtBxH,EAAE,EAAC;gBAAS;kBAAA0H,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACb,CAAC,eACFxL,OAAA;kBAAO2M,OAAO,EAAC,SAAS;kBAACxB,SAAS,EAAC,kBAAkB;kBAAAC,QAAA,EAAC;gBAEtD;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,EAGLtK,cAAc,CAACE,WAAW,iBACzBpB,OAAA;UAAKmL,SAAS,EAAC,iBAAiB;UAAAC,QAAA,gBAC9BpL,OAAA;YAAAoL,QAAA,EAAI;UAAkB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC3BxL,OAAA;YAAKmL,SAAS,EAAC,eAAe;YAAAC,QAAA,gBAC5BpL,OAAA;cAAKmL,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAElK,cAAc,CAACI;YAAW;cAAA+J,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACjExL,OAAA;cAAKmL,SAAS,EAAC,cAAc;cAAAC,QAAA,eAC3BpL,OAAA;gBACEmL,SAAS,EAAC,eAAe;gBACzBH,KAAK,EAAE;kBAAEU,KAAK,EAAE,GAAGxK,cAAc,CAACG,QAAQ;gBAAI;cAAE;gBAAAgK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5C;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eACNxL,OAAA;cAAKmL,SAAS,EAAC,qBAAqB;cAAAC,QAAA,GAAElK,cAAc,CAACG,QAAQ,CAACkL,OAAO,CAAC,CAAC,CAAC,EAAC,GAAC;YAAA;cAAAlB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7E,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN,EAGAtK,cAAc,CAACK,MAAM,iBACpBvB,OAAA;UAAKmL,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAC7BpL,OAAA;YAAAoL,QAAA,EAAI;UAAc;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACvBxL,OAAA;YAAKmL,SAAS,EAAC,gBAAgB;YAAAC,QAAA,gBAC7BpL,OAAA;cAAKmL,SAAS,EAAC,eAAe;cAAAC,QAAA,gBAC5BpL,OAAA;gBAAMmL,SAAS,EAAC,cAAc;gBAAAC,QAAA,EAAC;cAAe;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACrDxL,OAAA;gBAAMmL,SAAS,EAAC,cAAc;gBAAAC,QAAA,EAAElK,cAAc,CAACK,MAAM,CAACwF;cAAc;gBAAAsE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzE,CAAC,eACNxL,OAAA;cAAKmL,SAAS,EAAC,eAAe;cAAAC,QAAA,gBAC5BpL,OAAA;gBAAMmL,SAAS,EAAC,cAAc;gBAAAC,QAAA,EAAC;cAAQ;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC9CxL,OAAA;gBAAMmL,SAAS,EAAC,sBAAsB;gBAAAC,QAAA,EAAElK,cAAc,CAACK,MAAM,CAAC8E;cAAQ;gBAAAgF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3E,CAAC,eACNxL,OAAA;cAAKmL,SAAS,EAAC,eAAe;cAAAC,QAAA,gBAC5BpL,OAAA;gBAAMmL,SAAS,EAAC,cAAc;gBAAAC,QAAA,EAAC;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC7CxL,OAAA;gBAAMmL,SAAS,EAAC,sBAAsB;gBAAAC,QAAA,EAAElK,cAAc,CAACK,MAAM,CAAC+E;cAAO;gBAAA+E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1E,CAAC,eACNxL,OAAA;cAAKmL,SAAS,EAAC,eAAe;cAAAC,QAAA,gBAC5BpL,OAAA;gBAAMmL,SAAS,EAAC,cAAc;gBAAAC,QAAA,EAAC;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC5CxL,OAAA;gBAAMmL,SAAS,EAAC,oBAAoB;gBAAAC,QAAA,EAAElK,cAAc,CAACK,MAAM,CAACgF;cAAM;gBAAA8E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,EAELtK,cAAc,CAACK,MAAM,CAACyF,YAAY,CAACzD,MAAM,GAAG,CAAC,iBAC5CvD,OAAA;YAAKmL,SAAS,EAAC,eAAe;YAAAC,QAAA,gBAC5BpL,OAAA;cAAAoL,QAAA,EAAI;YAAa;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACtBxL,OAAA;cAAKmL,SAAS,EAAC,YAAY;cAAAC,QAAA,GACxBlK,cAAc,CAACK,MAAM,CAACyF,YAAY,CAACwD,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC/G,GAAG,CAAC,CAACnD,KAAK,EAAEmM,KAAK,kBAChEzM,OAAA;gBAAiBmL,SAAS,EAAC,YAAY;gBAAAC,QAAA,EAAE9K;cAAK,GAApCmM,KAAK;gBAAApB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAqC,CACrD,CAAC,EACDtK,cAAc,CAACK,MAAM,CAACyF,YAAY,CAACzD,MAAM,GAAG,EAAE,iBAC7CvD,OAAA;gBAAKmL,SAAS,EAAC,YAAY;gBAAAC,QAAA,GAAC,GACzB,EAAClK,cAAc,CAACK,MAAM,CAACyF,YAAY,CAACzD,MAAM,GAAG,EAAE,EAAC,cACnD;cAAA;gBAAA8H,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CACN;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CACN,EAGAhL,SAAS,KAAK,QAAQ,iBACrBR,OAAA;QAAKmL,SAAS,EAAC,YAAY;QAAAC,QAAA,gBACzBpL,OAAA;UAAKmL,SAAS,EAAC,iBAAiB;UAAAC,QAAA,gBAC9BpL,OAAA;YAAKmL,SAAS,EAAC,SAAS;YAAAC,QAAA,gBACtBpL,OAAA;cACE4I,KAAK,EAAEjH,OAAO,CAACE,UAAW;cAC1BgL,QAAQ,EAAGC,CAAC,IAAKlL,UAAU,CAACwE,IAAI,KAAK;gBAAE,GAAGA,IAAI;gBAAEvE,UAAU,EAAEiL,CAAC,CAACzF,MAAM,CAACuB;cAAM,CAAC,CAAC,CAAE;cAAAwC,QAAA,gBAE/EpL,OAAA;gBAAQ4I,KAAK,EAAC,EAAE;gBAAAwC,QAAA,EAAC;cAAc;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,EACvC5K,UAAU,CAAC6C,GAAG,CAACC,QAAQ,iBACtB1D,OAAA;gBAA0B4I,KAAK,EAAElF,QAAQ,CAACC,EAAG;gBAAAyH,QAAA,EAC1C1H,QAAQ,CAACiF;cAAI,GADHjF,QAAQ,CAACC,EAAE;gBAAA0H,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAEhB,CACT,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC,eAETxL,OAAA;cACE4M,IAAI,EAAC,MAAM;cACXG,WAAW,EAAC,wBAAwB;cACpCnE,KAAK,EAAEjH,OAAO,CAACG,UAAW;cAC1B+K,QAAQ,EAAGC,CAAC,IAAKlL,UAAU,CAACwE,IAAI,KAAK;gBAAE,GAAGA,IAAI;gBAAEtE,UAAU,EAAEgL,CAAC,CAACzF,MAAM,CAACuB;cAAM,CAAC,CAAC;YAAE;cAAAyC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChF,CAAC,eAEFxL,OAAA;cACE4M,IAAI,EAAC,MAAM;cACXhE,KAAK,EAAEjH,OAAO,CAACI,SAAS,CAACC,KAAM;cAC/B6K,QAAQ,EAAGC,CAAC,IAAKlL,UAAU,CAACwE,IAAI,KAAK;gBACnC,GAAGA,IAAI;gBACPrE,SAAS,EAAE;kBAAE,GAAGqE,IAAI,CAACrE,SAAS;kBAAEC,KAAK,EAAE8K,CAAC,CAACzF,MAAM,CAACuB;gBAAM;cACxD,CAAC,CAAC;YAAE;cAAAyC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,eAEFxL,OAAA;cACE4M,IAAI,EAAC,MAAM;cACXhE,KAAK,EAAEjH,OAAO,CAACI,SAAS,CAACE,GAAI;cAC7B4K,QAAQ,EAAGC,CAAC,IAAKlL,UAAU,CAACwE,IAAI,KAAK;gBACnC,GAAGA,IAAI;gBACPrE,SAAS,EAAE;kBAAE,GAAGqE,IAAI,CAACrE,SAAS;kBAAEE,GAAG,EAAE6K,CAAC,CAACzF,MAAM,CAACuB;gBAAM;cACtD,CAAC,CAAC;YAAE;cAAAyC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAENxL,OAAA;YAAKmL,SAAS,EAAC,SAAS;YAAAC,QAAA,GACrB5J,eAAe,CAAC4H,IAAI,GAAG,CAAC,iBACvBpJ,OAAA;cAAQyL,OAAO,EAAEpC,oBAAqB;cAAC8B,SAAS,EAAC,uBAAuB;cAAAC,QAAA,GAAC,mBACtD,EAAC5J,eAAe,CAAC4H,IAAI,EAAC,GACzC;YAAA;cAAAiC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CACT,eACDxL,OAAA;cAAQyL,OAAO,EAAEhC,kBAAmB;cAAC0B,SAAS,EAAC,uBAAuB;cAAAC,QAAA,EAAC;YAEvE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENxL,OAAA;UAAKmL,SAAS,EAAC,sBAAsB;UAAAC,QAAA,eACnCpL,OAAA;YAAOmL,SAAS,EAAC,YAAY;YAAAC,QAAA,gBAC3BpL,OAAA;cAAAoL,QAAA,eACEpL,OAAA;gBAAAoL,QAAA,gBACEpL,OAAA;kBAAAoL,QAAA,eACEpL,OAAA;oBACE4M,IAAI,EAAC,UAAU;oBACfO,OAAO,EAAE3L,eAAe,CAAC4H,IAAI,KAAKkB,gBAAgB,CAAC,CAAC,CAAC/G,MAAM,IAAI+G,gBAAgB,CAAC,CAAC,CAAC/G,MAAM,GAAG,CAAE;oBAC7FsJ,QAAQ,EAAE5D;kBAAgB;oBAAAoC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC3B;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACA,CAAC,eACLxL,OAAA;kBAAAoL,QAAA,EAAI;gBAAW;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACpBxL,OAAA;kBAAAoL,QAAA,EAAI;gBAAM;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACfxL,OAAA;kBAAAoL,QAAA,EAAI;gBAAQ;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACjBxL,OAAA;kBAAAoL,QAAA,EAAI;gBAAO;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACd;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC,eACRxL,OAAA;cAAAoL,QAAA,EACGd,gBAAgB,CAAC,CAAC,CAAC7G,GAAG,CAACL,IAAI;gBAAA,IAAAgK,gBAAA;gBAAA,oBAC1BpN,OAAA;kBAEEmL,SAAS,EAAE3J,eAAe,CAAC8D,GAAG,CAAClC,IAAI,CAACO,EAAE,CAAC,GAAG,UAAU,GAAG,EAAG;kBAAAyH,QAAA,gBAE1DpL,OAAA;oBAAAoL,QAAA,eACEpL,OAAA;sBACE4M,IAAI,EAAC,UAAU;sBACfO,OAAO,EAAE3L,eAAe,CAAC8D,GAAG,CAAClC,IAAI,CAACO,EAAE,CAAE;sBACtCkJ,QAAQ,EAAEA,CAAA,KAAMhE,gBAAgB,CAACzF,IAAI,CAACO,EAAE;oBAAE;sBAAA0H,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC3C;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACA,CAAC,eACLxL,OAAA;oBAAImL,SAAS,EAAC,kBAAkB;oBAAAC,QAAA,EAAEhI,IAAI,CAACV;kBAAW;oBAAA2I,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACxDxL,OAAA;oBAAImL,SAAS,EAAE/H,IAAI,CAACT,MAAM,IAAI,CAAC,GAAG,eAAe,GAAG,cAAe;oBAAAyI,QAAA,EAChEP,YAAY,CAACzH,IAAI,CAACT,MAAM;kBAAC;oBAAA0I,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxB,CAAC,eACLxL,OAAA;oBAAAoL,QAAA,eACEpL,OAAA;sBACEmL,SAAS,EAAC,gBAAgB;sBAC1BH,KAAK,EAAE;wBAAEqC,eAAe,GAAAD,gBAAA,GAAExM,UAAU,CAAC8H,IAAI,CAACN,CAAC,IAAIA,CAAC,CAACzE,EAAE,KAAKP,IAAI,CAACvB,UAAU,CAAC,cAAAuL,gBAAA,uBAA9CA,gBAAA,CAAgDE;sBAAM,CAAE;sBAAAlC,QAAA,EAEjFR,eAAe,CAACxH,IAAI,CAACvB,UAAU;oBAAC;sBAAAwJ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC7B;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACL,CAAC,eACLxL,OAAA;oBAAAoL,QAAA,EAAK,IAAItB,IAAI,CAAC1G,IAAI,CAAC2G,WAAW,CAAC,CAACwD,kBAAkB,CAAC;kBAAC;oBAAAlC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC;gBAAA,GAtBrDpI,IAAI,CAACO,EAAE;kBAAA0H,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAuBV,CAAC;cAAA,CACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,EAGLf,aAAa,CAAC,CAAC,GAAG,CAAC,iBAClBzK,OAAA;UAAKmL,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzBpL,OAAA;YACEyL,OAAO,EAAEA,CAAA,KAAMnJ,cAAc,CAAC8D,IAAI,IAAIlC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEiC,IAAI,GAAG,CAAC,CAAC,CAAE;YAC7D6G,QAAQ,EAAE5K,WAAW,KAAK,CAAE;YAC5B8I,SAAS,EAAC,0BAA0B;YAAAC,QAAA,EACrC;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAETxL,OAAA;YAAMmL,SAAS,EAAC,WAAW;YAAAC,QAAA,GAAC,OACrB,EAAC/I,WAAW,EAAC,MAAI,EAACoI,aAAa,CAAC,CAAC;UAAA;YAAAY,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC,CAAC,eAEPxL,OAAA;YACEyL,OAAO,EAAEA,CAAA,KAAMnJ,cAAc,CAAC8D,IAAI,IAAIlC,IAAI,CAACY,GAAG,CAAC2F,aAAa,CAAC,CAAC,EAAErE,IAAI,GAAG,CAAC,CAAC,CAAE;YAC3E6G,QAAQ,EAAE5K,WAAW,KAAKoI,aAAa,CAAC,CAAE;YAC1CU,SAAS,EAAC,0BAA0B;YAAAC,QAAA,EACrC;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CACN,EAGAhL,SAAS,KAAK,SAAS,IAAIM,cAAc,iBACxCd,OAAA;QAAKmL,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1BpL,OAAA;UAAKmL,SAAS,EAAC,kBAAkB;UAAAC,QAAA,gBAC/BpL,OAAA;YAAAoL,QAAA,EAAI;UAAqB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAE9BxL,OAAA;YAAKmL,SAAS,EAAC,iBAAiB;YAAAC,QAAA,gBAC9BpL,OAAA;cAAKmL,SAAS,EAAC,gBAAgB;cAAAC,QAAA,gBAC7BpL,OAAA;gBAAKmL,SAAS,EAAC,eAAe;gBAAAC,QAAA,eAC5BpL,OAAA;kBAAK4L,OAAO,EAAC,WAAW;kBAACT,SAAS,EAAC,gBAAgB;kBAAAC,QAAA,gBACjDpL,OAAA;oBACEmL,SAAS,EAAC,WAAW;oBACrBqC,CAAC,EAAC;kBAEkC;oBAAAnC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACrC,CAAC,eACFxL,OAAA;oBACEmL,SAAS,EAAC,QAAQ;oBAClBsC,eAAe,EAAE,GAAG3M,cAAc,CAACkE,YAAY,OAAQ;oBACvDwI,CAAC,EAAC;kBAEkC;oBAAAnC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACrC,CAAC,eACFxL,OAAA;oBAAM0N,CAAC,EAAC,IAAI;oBAACC,CAAC,EAAC,OAAO;oBAACxC,SAAS,EAAC,YAAY;oBAAAC,QAAA,GAC1CtK,cAAc,CAACkE,YAAY,CAACuH,OAAO,CAAC,CAAC,CAAC,EAAC,GAC1C;kBAAA;oBAAAlB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNxL,OAAA;gBAAAoL,QAAA,EAAI;cAAe;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrB,CAAC,eAENxL,OAAA;cAAKmL,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAChCpL,OAAA;gBAAKmL,SAAS,EAAC,gBAAgB;gBAAAC,QAAA,gBAC7BpL,OAAA;kBAAMmL,SAAS,EAAC,iBAAiB;kBAAAC,QAAA,EAAC;gBAAW;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACpDxL,OAAA;kBAAKmL,SAAS,EAAC,eAAe;kBAAAC,QAAA,eAC5BpL,OAAA;oBACEmL,SAAS,EAAC,gBAAgB;oBAC1BH,KAAK,EAAE;sBAAEU,KAAK,EAAE,GAAGxH,IAAI,CAACY,GAAG,CAAC,GAAG,EAAGhE,cAAc,CAACwC,aAAa,GAAG,GAAG,GAAI,GAAG,CAAC;oBAAI;kBAAE;oBAAA+H,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC9E;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC,eACNxL,OAAA;kBAAMmL,SAAS,EAAC,iBAAiB;kBAAAC,QAAA,GAAEtK,cAAc,CAACwC,aAAa,EAAC,MAAI;gBAAA;kBAAA+H,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxE,CAAC,eAENxL,OAAA;gBAAKmL,SAAS,EAAC,gBAAgB;gBAAAC,QAAA,gBAC7BpL,OAAA;kBAAMmL,SAAS,EAAC,iBAAiB;kBAAAC,QAAA,EAAC;gBAAiB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC1DxL,OAAA;kBAAKmL,SAAS,EAAC,eAAe;kBAAAC,QAAA,eAC5BpL,OAAA;oBACEmL,SAAS,EAAC,gBAAgB;oBAC1BH,KAAK,EAAE;sBAAEU,KAAK,EAAE,GAAI5K,cAAc,CAACiD,kBAAkB,GAAGnD,UAAU,CAAC2C,MAAM,GAAI,GAAG;oBAAI;kBAAE;oBAAA8H,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClF;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC,eACNxL,OAAA;kBAAMmL,SAAS,EAAC,iBAAiB;kBAAAC,QAAA,GAC9BtK,cAAc,CAACiD,kBAAkB,EAAC,GAAC,EAACnD,UAAU,CAAC2C,MAAM;gBAAA;kBAAA8H,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eAENxL,OAAA;gBAAKmL,SAAS,EAAC,gBAAgB;gBAAAC,QAAA,gBAC7BpL,OAAA;kBAAMmL,SAAS,EAAC,iBAAiB;kBAAAC,QAAA,EAAC;gBAAa;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACtDxL,OAAA;kBAAKmL,SAAS,EAAC,eAAe;kBAAAC,QAAA,eAC5BpL,OAAA;oBACEmL,SAAS,EAAC,gBAAgB;oBAC1BH,KAAK,EAAE;sBAAEU,KAAK,EAAE,GAAG5K,cAAc,CAAC4D,YAAY;oBAAI;kBAAE;oBAAA2G,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChD;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC,eACNxL,OAAA;kBAAMmL,SAAS,EAAC,iBAAiB;kBAAAC,QAAA,GAAEtK,cAAc,CAAC4D,YAAY,CAAC6H,OAAO,CAAC,CAAC,CAAC,EAAC,GAAC;gBAAA;kBAAAlB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/E,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENxL,OAAA;UAAKmL,SAAS,EAAC,6BAA6B;UAAAC,QAAA,gBAC1CpL,OAAA;YAAAoL,QAAA,EAAI;UAAsC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC/CxL,OAAA;YAAKmL,SAAS,EAAC,mBAAmB;YAAAC,QAAA,EAC/BxK,UAAU,CAAC6C,GAAG,CAACC,QAAQ,IAAI;cAC1B,MAAME,KAAK,GAAGlD,YAAY,CAACmD,MAAM,CAACC,EAAE,IAAIA,EAAE,CAACjC,UAAU,KAAK6B,QAAQ,CAACC,EAAE,CAAC,CAACJ,MAAM;cAC7E,MAAMqK,UAAU,GAAG9M,cAAc,CAACwC,aAAa,GAAG,CAAC,GAAIM,KAAK,GAAG9C,cAAc,CAACwC,aAAa,GAAI,GAAG,GAAG,CAAC;cAEtG,oBACEtD,OAAA;gBAAuBmL,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,gBACtDpL,OAAA;kBAAKmL,SAAS,EAAC,UAAU;kBAAAC,QAAA,gBACvBpL,OAAA;oBAAMmL,SAAS,EAAC,WAAW;oBAAAC,QAAA,EAAE1H,QAAQ,CAACiF;kBAAI;oBAAA0C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eAClDxL,OAAA;oBAAMmL,SAAS,EAAC,WAAW;oBAAAC,QAAA,EAAExH;kBAAK;oBAAAyH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvC,CAAC,eACNxL,OAAA;kBAAKmL,SAAS,EAAC,eAAe;kBAAAC,QAAA,eAC5BpL,OAAA;oBACEmL,SAAS,EAAC,UAAU;oBACpBH,KAAK,EAAE;sBACLU,KAAK,EAAE,GAAGkC,UAAU,GAAG;sBACvBP,eAAe,EAAE3J,QAAQ,CAAC4J;oBAC5B;kBAAE;oBAAAjC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC;cAAA,GAbE9H,QAAQ,CAACC,EAAE;gBAAA0H,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAchB,CAAC;YAEV,CAAC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACrL,EAAA,CAtiCWF,mBAAuD;AAAA4N,EAAA,GAAvD5N,mBAAuD;AAAA,IAAA4N,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}