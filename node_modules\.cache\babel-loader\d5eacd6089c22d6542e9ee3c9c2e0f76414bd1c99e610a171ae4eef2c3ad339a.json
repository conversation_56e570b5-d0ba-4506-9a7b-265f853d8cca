{"ast": null, "code": "import _objectSpread from\"C:/tmsft/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";import React,{useState,useEffect,useCallback,useMemo}from'react';import{transactionStorageService}from'../services/transactionStorageService';import{bankAccountService}from'../services/bankAccountService';import'./Transactions.css';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const ITEMS_PER_PAGE_OPTIONS=[10,25,50,100];const DEFAULT_ITEMS_PER_PAGE=50;export const Transactions=_ref=>{let{onTransactionUpdate}=_ref;// State management\nconst[transactions,setTransactions]=useState([]);const[bankAccounts,setBankAccounts]=useState([]);const[duplicateGroups,setDuplicateGroups]=useState([]);const[loading,setLoading]=useState(true);const[error,setError]=useState(null);// Pagination state\nconst[currentPage,setCurrentPage]=useState(1);const[itemsPerPage,setItemsPerPage]=useState(DEFAULT_ITEMS_PER_PAGE);// Sorting state\nconst[sortField,setSortField]=useState('postDateTime');const[sortDirection,setSortDirection]=useState('desc');// Filtering state\nconst[filters,setFilters]=useState({accountId:'',dateFrom:'',dateTo:'',amountFrom:'',amountTo:'',description:'',type:'all'});// Selection state\nconst[selectedTransactions,setSelectedTransactions]=useState(new Set());const[showDuplicatesOnly,setShowDuplicatesOnly]=useState(false);// Load data on component mount\nconst loadData=useCallback(async()=>{try{setLoading(true);setError(null);// Load bank accounts\nconst accounts=bankAccountService.getAllAccounts();setBankAccounts(accounts);// Load all transactions\nconst allTransactions=[];accounts.forEach(account=>{const accountTransactions=transactionStorageService.getTransactionsByAccount(account.id);allTransactions.push(...accountTransactions);});setTransactions(allTransactions);// Detect duplicates within the transaction set\nconst duplicates=findDuplicatesInTransactions(allTransactions);setDuplicateGroups(duplicates);if(onTransactionUpdate){onTransactionUpdate(allTransactions);}}catch(err){setError(err instanceof Error?err.message:'Failed to load transactions');}finally{setLoading(false);}},[onTransactionUpdate]);useEffect(()=>{loadData();},[loadData]);// Simple duplicate detection within a single set of transactions\nconst findDuplicatesInTransactions=transactions=>{const duplicateGroups=[];const processed=new Set();for(let i=0;i<transactions.length;i++){if(processed.has(transactions[i].id))continue;const group=[transactions[i]];processed.add(transactions[i].id);for(let j=i+1;j<transactions.length;j++){if(processed.has(transactions[j].id))continue;// Check if transactions are potential duplicates\nif(arePotentialDuplicates(transactions[i],transactions[j])){group.push(transactions[j]);processed.add(transactions[j].id);}}// Only consider groups with 2 or more transactions as duplicates\nif(group.length>1){duplicateGroups.push(group);}}return duplicateGroups;};// Check if two transactions are potential duplicates\nconst arePotentialDuplicates=(t1,t2)=>{// Same account\nif(t1.accountId!==t2.accountId)return false;// Same date (within 1 day)\nconst date1=new Date(t1.postDateTime);const date2=new Date(t2.postDateTime);const daysDiff=Math.abs(date1.getTime()-date2.getTime())/(1000*60*60*24);if(daysDiff>1)return false;// Same amounts\nconst sameDebit=Math.abs((t1.debitAmount||0)-(t2.debitAmount||0))<0.01;const sameCredit=Math.abs((t1.creditAmount||0)-(t2.creditAmount||0))<0.01;if(!sameDebit||!sameCredit)return false;// Similar description (at least 80% similarity)\nconst similarity=calculateStringSimilarity(t1.description,t2.description);if(similarity<0.8)return false;return true;};// Calculate string similarity\nconst calculateStringSimilarity=(str1,str2)=>{const longer=str1.length>str2.length?str1:str2;const shorter=str1.length>str2.length?str2:str1;if(longer.length===0)return 1.0;// Simple similarity based on common characters\nconst s1=str1.toLowerCase();const s2=str2.toLowerCase();let matches=0;for(let i=0;i<shorter.length;i++){if(s1.includes(s2[i]))matches++;}return matches/longer.length;};// Filtered and sorted transactions\nconst filteredAndSortedTransactions=useMemo(()=>{let filtered=[...transactions];// Apply filters\nif(filters.accountId){filtered=filtered.filter(t=>t.accountId===filters.accountId);}if(filters.dateFrom){const fromDate=new Date(filters.dateFrom);filtered=filtered.filter(t=>new Date(t.postDateTime)>=fromDate);}if(filters.dateTo){const toDate=new Date(filters.dateTo);filtered=filtered.filter(t=>new Date(t.postDateTime)<=toDate);}if(filters.description){const searchTerm=filters.description.toLowerCase();filtered=filtered.filter(t=>t.description.toLowerCase().includes(searchTerm)||t.reference&&t.reference.toLowerCase().includes(searchTerm));}if(filters.amountFrom){const minAmount=parseFloat(filters.amountFrom);filtered=filtered.filter(t=>{const amount=Math.abs((t.debitAmount||0)+(t.creditAmount||0));return amount>=minAmount;});}if(filters.amountTo){const maxAmount=parseFloat(filters.amountTo);filtered=filtered.filter(t=>{const amount=Math.abs((t.debitAmount||0)+(t.creditAmount||0));return amount<=maxAmount;});}if(filters.type==='debits'){filtered=filtered.filter(t=>(t.debitAmount||0)>0);}else if(filters.type==='credits'){filtered=filtered.filter(t=>(t.creditAmount||0)>0);}// Show duplicates only\nif(showDuplicatesOnly){const duplicateIds=new Set(duplicateGroups.flat().map(t=>t.id));filtered=filtered.filter(t=>duplicateIds.has(t.id));}// Sort transactions\nfiltered.sort((a,b)=>{let aValue;let bValue;switch(sortField){case'postDateTime':aValue=new Date(a.postDateTime).getTime();bValue=new Date(b.postDateTime).getTime();break;case'description':aValue=a.description.toLowerCase();bValue=b.description.toLowerCase();break;case'amount':aValue=Math.abs((a.debitAmount||0)+(a.creditAmount||0));bValue=Math.abs((b.debitAmount||0)+(b.creditAmount||0));break;case'balance':aValue=a.balance;bValue=b.balance;break;case'accountName':const accountA=bankAccounts.find(acc=>acc.id===a.accountId);const accountB=bankAccounts.find(acc=>acc.id===b.accountId);aValue=(accountA===null||accountA===void 0?void 0:accountA.name.toLowerCase())||'';bValue=(accountB===null||accountB===void 0?void 0:accountB.name.toLowerCase())||'';break;default:return 0;}if(sortDirection==='asc'){return aValue<bValue?-1:aValue>bValue?1:0;}else{return aValue>bValue?-1:aValue<bValue?1:0;}});return filtered;},[transactions,filters,sortField,sortDirection,showDuplicatesOnly,duplicateGroups,bankAccounts]);// Pagination calculations\nconst totalPages=Math.ceil(filteredAndSortedTransactions.length/itemsPerPage);const startIndex=(currentPage-1)*itemsPerPage;const endIndex=startIndex+itemsPerPage;const currentTransactions=filteredAndSortedTransactions.slice(startIndex,endIndex);// Reset to first page when filters change\nuseEffect(()=>{setCurrentPage(1);},[filters,sortField,sortDirection,showDuplicatesOnly,itemsPerPage]);// Event handlers\nconst handleSort=field=>{if(sortField===field){setSortDirection(sortDirection==='asc'?'desc':'asc');}else{setSortField(field);setSortDirection('desc');}};const handleFilterChange=(key,value)=>{setFilters(prev=>_objectSpread(_objectSpread({},prev),{},{[key]:value}));};const handleSelectTransaction=transactionId=>{setSelectedTransactions(prev=>{const newSet=new Set(prev);if(newSet.has(transactionId)){newSet.delete(transactionId);}else{newSet.add(transactionId);}return newSet;});};const handleSelectAll=useCallback(()=>{if(selectedTransactions.size===currentTransactions.length){setSelectedTransactions(new Set());}else{setSelectedTransactions(new Set(currentTransactions.map(t=>t.id)));}},[selectedTransactions.size,currentTransactions]);const clearFilters=()=>{setFilters({accountId:'',dateFrom:'',dateTo:'',amountFrom:'',amountTo:'',description:'',type:'all'});setShowDuplicatesOnly(false);};// Keyboard navigation\nuseEffect(()=>{const handleKeyPress=e=>{if(e.target instanceof HTMLInputElement||e.target instanceof HTMLTextAreaElement){return;// Don't interfere with input fields\n}switch(e.key){case'ArrowLeft':if(currentPage>1){setCurrentPage(currentPage-1);}break;case'ArrowRight':if(currentPage<totalPages){setCurrentPage(currentPage+1);}break;case'Home':setCurrentPage(1);break;case'End':setCurrentPage(totalPages);break;case'f':if(e.ctrlKey||e.metaKey){var _document$getElementB;e.preventDefault();(_document$getElementB=document.getElementById('search-input'))===null||_document$getElementB===void 0?void 0:_document$getElementB.focus();}break;case'r':if(e.ctrlKey||e.metaKey){e.preventDefault();loadData();}break;case'a':if(e.ctrlKey||e.metaKey){e.preventDefault();handleSelectAll();}break;case'Escape':setSelectedTransactions(new Set());break;}};document.addEventListener('keydown',handleKeyPress);return()=>document.removeEventListener('keydown',handleKeyPress);},[currentPage,totalPages,loadData,handleSelectAll]);// Utility functions\nconst formatCurrency=amount=>{return new Intl.NumberFormat('en-US',{minimumFractionDigits:2,maximumFractionDigits:2}).format(amount);};const formatDate=dateString=>{const date=new Date(dateString);return date.toLocaleDateString('en-GB',{day:'2-digit',month:'2-digit',year:'numeric'});};const getAccountName=accountId=>{const account=bankAccounts.find(acc=>acc.id===accountId);return(account===null||account===void 0?void 0:account.name)||'Unknown Account';};const isDuplicate=transaction=>{return duplicateGroups.some(group=>group.some(t=>t.id===transaction.id));};if(loading){return/*#__PURE__*/_jsxs(\"div\",{className:\"transactions-loading\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"loading-spinner\"}),/*#__PURE__*/_jsx(\"p\",{children:\"Loading transactions...\"})]});}if(error){return/*#__PURE__*/_jsxs(\"div\",{className:\"transactions-error\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"error-icon\",children:\"\\u26A0\\uFE0F\"}),/*#__PURE__*/_jsx(\"h3\",{children:\"Error Loading Transactions\"}),/*#__PURE__*/_jsx(\"p\",{children:error}),/*#__PURE__*/_jsx(\"button\",{onClick:loadData,className:\"btn btn-primary\",children:\"Try Again\"})]});}return/*#__PURE__*/_jsxs(\"div\",{className:\"transactions\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"transactions-header\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"transactions-title-section\",children:[/*#__PURE__*/_jsx(\"h2\",{className:\"transactions-title\",children:\"Transactions\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"transactions-stats\",children:[/*#__PURE__*/_jsxs(\"span\",{className:\"stat-item\",children:[\"Total: \",/*#__PURE__*/_jsx(\"strong\",{children:transactions.length.toLocaleString()})]}),/*#__PURE__*/_jsxs(\"span\",{className:\"stat-item\",children:[\"Filtered: \",/*#__PURE__*/_jsx(\"strong\",{children:filteredAndSortedTransactions.length.toLocaleString()})]}),duplicateGroups.length>0&&/*#__PURE__*/_jsxs(\"span\",{className:\"stat-item duplicate-stat\",children:[\"Duplicates: \",/*#__PURE__*/_jsx(\"strong\",{children:duplicateGroups.flat().length})]})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"transactions-actions\",children:[/*#__PURE__*/_jsxs(\"button\",{onClick:loadData,className:\"btn btn-secondary btn-sm\",children:[/*#__PURE__*/_jsxs(\"svg\",{width:\"16\",height:\"16\",viewBox:\"0 0 24 24\",fill:\"none\",stroke:\"currentColor\",strokeWidth:\"2\",children:[/*#__PURE__*/_jsx(\"polyline\",{points:\"23 4 23 10 17 10\"}),/*#__PURE__*/_jsx(\"polyline\",{points:\"1 20 1 14 7 14\"}),/*#__PURE__*/_jsx(\"path\",{d:\"M20.49 9A9 9 0 0 0 5.64 5.64L1 10m22 4l-4.64 4.36A9 9 0 0 1 3.51 15\"})]}),\"Refresh\"]}),selectedTransactions.size>0&&/*#__PURE__*/_jsxs(\"span\",{className:\"selection-count\",children:[selectedTransactions.size,\" selected\"]})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"transactions-filters\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"filters-row\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"filter-group\",children:[/*#__PURE__*/_jsx(\"label\",{className:\"filter-label\",children:\"Account\"}),/*#__PURE__*/_jsxs(\"select\",{value:filters.accountId,onChange:e=>handleFilterChange('accountId',e.target.value),className:\"filter-select\",children:[/*#__PURE__*/_jsx(\"option\",{value:\"\",children:\"All Accounts\"}),bankAccounts.map(account=>/*#__PURE__*/_jsxs(\"option\",{value:account.id,children:[account.name,\" - \",account.accountNumber]},account.id))]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"filter-group\",children:[/*#__PURE__*/_jsx(\"label\",{className:\"filter-label\",children:\"Type\"}),/*#__PURE__*/_jsxs(\"select\",{value:filters.type,onChange:e=>handleFilterChange('type',e.target.value),className:\"filter-select\",children:[/*#__PURE__*/_jsx(\"option\",{value:\"all\",children:\"All Types\"}),/*#__PURE__*/_jsx(\"option\",{value:\"debits\",children:\"Debits Only\"}),/*#__PURE__*/_jsx(\"option\",{value:\"credits\",children:\"Credits Only\"})]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"filter-group\",children:/*#__PURE__*/_jsxs(\"label\",{className:\"filter-label\",children:[/*#__PURE__*/_jsx(\"input\",{type:\"checkbox\",checked:showDuplicatesOnly,onChange:e=>setShowDuplicatesOnly(e.target.checked),className:\"filter-checkbox\"}),\"Show Duplicates Only\"]})})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"filters-row\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"filter-group\",children:[/*#__PURE__*/_jsx(\"label\",{className:\"filter-label\",children:\"Search\"}),/*#__PURE__*/_jsx(\"input\",{id:\"search-input\",type:\"text\",value:filters.description,onChange:e=>handleFilterChange('description',e.target.value),placeholder:\"Search description or reference...\",className:\"filter-input\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"filter-group\",children:[/*#__PURE__*/_jsx(\"label\",{className:\"filter-label\",children:\"Date From\"}),/*#__PURE__*/_jsx(\"input\",{type:\"date\",value:filters.dateFrom,onChange:e=>handleFilterChange('dateFrom',e.target.value),className:\"filter-input\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"filter-group\",children:[/*#__PURE__*/_jsx(\"label\",{className:\"filter-label\",children:\"Date To\"}),/*#__PURE__*/_jsx(\"input\",{type:\"date\",value:filters.dateTo,onChange:e=>handleFilterChange('dateTo',e.target.value),className:\"filter-input\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"filter-group\",children:[/*#__PURE__*/_jsx(\"label\",{className:\"filter-label\",children:\"Amount Range\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"amount-range\",children:[/*#__PURE__*/_jsx(\"input\",{type:\"number\",value:filters.amountFrom,onChange:e=>handleFilterChange('amountFrom',e.target.value),placeholder:\"Min\",className:\"filter-input amount-input\",step:\"0.01\"}),/*#__PURE__*/_jsx(\"span\",{className:\"amount-separator\",children:\"-\"}),/*#__PURE__*/_jsx(\"input\",{type:\"number\",value:filters.amountTo,onChange:e=>handleFilterChange('amountTo',e.target.value),placeholder:\"Max\",className:\"filter-input amount-input\",step:\"0.01\"})]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"filter-group\",children:/*#__PURE__*/_jsx(\"button\",{onClick:clearFilters,className:\"btn btn-secondary btn-sm\",children:\"Clear Filters\"})})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"transactions-table-container\",children:[/*#__PURE__*/_jsxs(\"table\",{className:\"transactions-table\",children:[/*#__PURE__*/_jsx(\"thead\",{children:/*#__PURE__*/_jsxs(\"tr\",{children:[/*#__PURE__*/_jsx(\"th\",{className:\"checkbox-col\",children:/*#__PURE__*/_jsx(\"input\",{type:\"checkbox\",checked:selectedTransactions.size===currentTransactions.length&&currentTransactions.length>0,onChange:handleSelectAll,className:\"table-checkbox\"})}),/*#__PURE__*/_jsxs(\"th\",{className:\"sortable \".concat(sortField==='postDateTime'?'sorted':''),onClick:()=>handleSort('postDateTime'),children:[\"Date\",sortField==='postDateTime'&&/*#__PURE__*/_jsx(\"span\",{className:\"sort-arrow\",children:sortDirection==='asc'?'↑':'↓'})]}),/*#__PURE__*/_jsxs(\"th\",{className:\"sortable \".concat(sortField==='accountName'?'sorted':''),onClick:()=>handleSort('accountName'),children:[\"Account\",sortField==='accountName'&&/*#__PURE__*/_jsx(\"span\",{className:\"sort-arrow\",children:sortDirection==='asc'?'↑':'↓'})]}),/*#__PURE__*/_jsxs(\"th\",{className:\"sortable \".concat(sortField==='description'?'sorted':''),onClick:()=>handleSort('description'),children:[\"Description\",sortField==='description'&&/*#__PURE__*/_jsx(\"span\",{className:\"sort-arrow\",children:sortDirection==='asc'?'↑':'↓'})]}),/*#__PURE__*/_jsx(\"th\",{className:\"amount-col\",children:\"Debit\"}),/*#__PURE__*/_jsx(\"th\",{className:\"amount-col\",children:\"Credit\"}),/*#__PURE__*/_jsxs(\"th\",{className:\"amount-col sortable \".concat(sortField==='balance'?'sorted':''),onClick:()=>handleSort('balance'),children:[\"Balance\",sortField==='balance'&&/*#__PURE__*/_jsx(\"span\",{className:\"sort-arrow\",children:sortDirection==='asc'?'↑':'↓'})]}),/*#__PURE__*/_jsx(\"th\",{children:\"Reference\"})]})}),/*#__PURE__*/_jsx(\"tbody\",{children:currentTransactions.map(transaction=>/*#__PURE__*/_jsxs(\"tr\",{className:\"\\n                  \".concat(selectedTransactions.has(transaction.id)?'selected':'',\"\\n                  \").concat(isDuplicate(transaction)?'duplicate':'',\"\\n                \"),children:[/*#__PURE__*/_jsx(\"td\",{className:\"checkbox-col\",children:/*#__PURE__*/_jsx(\"input\",{type:\"checkbox\",checked:selectedTransactions.has(transaction.id),onChange:()=>handleSelectTransaction(transaction.id),className:\"table-checkbox\"})}),/*#__PURE__*/_jsx(\"td\",{className:\"date-col\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"date-display\",children:[formatDate(transaction.postDateTime),transaction.time&&/*#__PURE__*/_jsx(\"span\",{className:\"time-display\",children:transaction.time})]})}),/*#__PURE__*/_jsx(\"td\",{className:\"account-col\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"account-info\",children:[getAccountName(transaction.accountId),isDuplicate(transaction)&&/*#__PURE__*/_jsx(\"span\",{className:\"duplicate-badge\",children:\"DUPLICATE\"})]})}),/*#__PURE__*/_jsx(\"td\",{className:\"description-col\",children:/*#__PURE__*/_jsx(\"div\",{className:\"description-content\",children:transaction.description})}),/*#__PURE__*/_jsx(\"td\",{className:\"amount-col debit\",children:transaction.debitAmount?formatCurrency(transaction.debitAmount):''}),/*#__PURE__*/_jsx(\"td\",{className:\"amount-col credit\",children:transaction.creditAmount?formatCurrency(transaction.creditAmount):''}),/*#__PURE__*/_jsx(\"td\",{className:\"amount-col balance\",children:formatCurrency(transaction.balance)}),/*#__PURE__*/_jsx(\"td\",{className:\"reference-col\",children:transaction.reference||''})]},transaction.id))})]}),currentTransactions.length===0&&/*#__PURE__*/_jsxs(\"div\",{className:\"no-transactions\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"no-transactions-icon\",children:\"\\uD83D\\uDCCA\"}),/*#__PURE__*/_jsx(\"h3\",{children:\"No Transactions Found\"}),/*#__PURE__*/_jsx(\"p\",{children:filteredAndSortedTransactions.length===0&&transactions.length===0?'No transactions have been imported yet.':'No transactions match your current filters.'}),filteredAndSortedTransactions.length===0&&transactions.length>0&&/*#__PURE__*/_jsx(\"button\",{onClick:clearFilters,className:\"btn btn-primary\",children:\"Clear Filters\"})]})]}),totalPages>1&&/*#__PURE__*/_jsxs(\"div\",{className:\"transactions-pagination\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"pagination-info\",children:[/*#__PURE__*/_jsxs(\"span\",{children:[\"Showing \",startIndex+1,\"-\",Math.min(endIndex,filteredAndSortedTransactions.length),\" of \",filteredAndSortedTransactions.length,\" transactions\"]}),/*#__PURE__*/_jsxs(\"div\",{className:\"items-per-page\",children:[/*#__PURE__*/_jsx(\"label\",{children:\"Items per page:\"}),/*#__PURE__*/_jsx(\"select\",{value:itemsPerPage,onChange:e=>setItemsPerPage(Number(e.target.value)),className:\"page-size-select\",children:ITEMS_PER_PAGE_OPTIONS.map(size=>/*#__PURE__*/_jsx(\"option\",{value:size,children:size},size))})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"pagination-controls\",children:[/*#__PURE__*/_jsx(\"button\",{onClick:()=>setCurrentPage(1),disabled:currentPage===1,className:\"btn btn-secondary btn-sm\",children:\"First\"}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>setCurrentPage(currentPage-1),disabled:currentPage===1,className:\"btn btn-secondary btn-sm\",children:\"Previous\"}),/*#__PURE__*/_jsx(\"div\",{className:\"pagination-pages\",children:Array.from({length:Math.min(5,totalPages)},(_,i)=>{const pageNumber=Math.max(1,Math.min(totalPages-4,currentPage-2))+i;return/*#__PURE__*/_jsx(\"button\",{onClick:()=>setCurrentPage(pageNumber),className:\"btn btn-secondary btn-sm \".concat(currentPage===pageNumber?'active':''),children:pageNumber},pageNumber);})}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>setCurrentPage(currentPage+1),disabled:currentPage===totalPages,className:\"btn btn-secondary btn-sm\",children:\"Next\"}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>setCurrentPage(totalPages),disabled:currentPage===totalPages,className:\"btn btn-secondary btn-sm\",children:\"Last\"})]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"keyboard-shortcuts\",children:/*#__PURE__*/_jsxs(\"details\",{children:[/*#__PURE__*/_jsx(\"summary\",{children:\"Keyboard Shortcuts\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"shortcuts-grid\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"shortcut-item\",children:[/*#__PURE__*/_jsx(\"kbd\",{children:\"\\u2190\"}),\" \",/*#__PURE__*/_jsx(\"span\",{children:\"Previous page\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"shortcut-item\",children:[/*#__PURE__*/_jsx(\"kbd\",{children:\"\\u2192\"}),\" \",/*#__PURE__*/_jsx(\"span\",{children:\"Next page\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"shortcut-item\",children:[/*#__PURE__*/_jsx(\"kbd\",{children:\"Home\"}),\" \",/*#__PURE__*/_jsx(\"span\",{children:\"First page\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"shortcut-item\",children:[/*#__PURE__*/_jsx(\"kbd\",{children:\"End\"}),\" \",/*#__PURE__*/_jsx(\"span\",{children:\"Last page\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"shortcut-item\",children:[/*#__PURE__*/_jsx(\"kbd\",{children:\"Ctrl+F\"}),\" \",/*#__PURE__*/_jsx(\"span\",{children:\"Focus search\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"shortcut-item\",children:[/*#__PURE__*/_jsx(\"kbd\",{children:\"Ctrl+R\"}),\" \",/*#__PURE__*/_jsx(\"span\",{children:\"Refresh\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"shortcut-item\",children:[/*#__PURE__*/_jsx(\"kbd\",{children:\"Ctrl+A\"}),\" \",/*#__PURE__*/_jsx(\"span\",{children:\"Select all\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"shortcut-item\",children:[/*#__PURE__*/_jsx(\"kbd\",{children:\"Esc\"}),\" \",/*#__PURE__*/_jsx(\"span\",{children:\"Clear selection\"})]})]})]})})]});};", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useCallback", "useMemo", "transactionStorageService", "bankAccountService", "jsx", "_jsx", "jsxs", "_jsxs", "ITEMS_PER_PAGE_OPTIONS", "DEFAULT_ITEMS_PER_PAGE", "Transactions", "_ref", "onTransactionUpdate", "transactions", "setTransactions", "bankAccounts", "setBankAccounts", "duplicateGroups", "setDuplicateGroups", "loading", "setLoading", "error", "setError", "currentPage", "setCurrentPage", "itemsPerPage", "setItemsPerPage", "sortField", "setSortField", "sortDirection", "setSortDirection", "filters", "setFilters", "accountId", "dateFrom", "dateTo", "amountFrom", "amountTo", "description", "type", "selectedTransactions", "setSelectedTransactions", "Set", "showDuplicatesOnly", "setShowDuplicatesOnly", "loadData", "accounts", "getAllAccounts", "allTransactions", "for<PERSON>ach", "account", "accountTransactions", "getTransactionsByAccount", "id", "push", "duplicates", "findDuplicatesInTransactions", "err", "Error", "message", "processed", "i", "length", "has", "group", "add", "j", "arePotentialDuplicates", "t1", "t2", "date1", "Date", "postDateTime", "date2", "daysDiff", "Math", "abs", "getTime", "sameDebit", "debitAmount", "sameCredit", "creditAmount", "similarity", "calculateStringSimilarity", "str1", "str2", "longer", "shorter", "s1", "toLowerCase", "s2", "matches", "includes", "filteredAndSortedTransactions", "filtered", "filter", "t", "fromDate", "toDate", "searchTerm", "reference", "minAmount", "parseFloat", "amount", "maxAmount", "duplicateIds", "flat", "map", "sort", "a", "b", "aValue", "bValue", "balance", "accountA", "find", "acc", "accountB", "name", "totalPages", "ceil", "startIndex", "endIndex", "currentTransactions", "slice", "handleSort", "field", "handleFilterChange", "key", "value", "prev", "_objectSpread", "handleSelectTransaction", "transactionId", "newSet", "delete", "handleSelectAll", "size", "clearFilters", "handleKeyPress", "e", "target", "HTMLInputElement", "HTMLTextAreaElement", "ctrl<PERSON>ey", "metaKey", "_document$getElementB", "preventDefault", "document", "getElementById", "focus", "addEventListener", "removeEventListener", "formatCurrency", "Intl", "NumberFormat", "minimumFractionDigits", "maximumFractionDigits", "format", "formatDate", "dateString", "date", "toLocaleDateString", "day", "month", "year", "getAccountName", "isDuplicate", "transaction", "some", "className", "children", "onClick", "toLocaleString", "width", "height", "viewBox", "fill", "stroke", "strokeWidth", "points", "d", "onChange", "accountNumber", "checked", "placeholder", "step", "concat", "time", "min", "Number", "disabled", "Array", "from", "_", "pageNumber", "max"], "sources": ["C:/tmsft/src/components/Transactions.tsx"], "sourcesContent": ["import React, { useState, useEffect, useCallback, useMemo } from 'react';\r\nimport { transactionStorageService, type StoredTransaction } from '../services/transactionStorageService';\r\nimport { bankAccountService } from '../services/bankAccountService';\r\nimport { duplicateDetectionService } from '../services/duplicateDetectionService';\r\nimport { BankAccount } from '../types';\r\nimport './Transactions.css';\r\n\r\ninterface TransactionsProps {\r\n  onTransactionUpdate?: (transactions: StoredTransaction[]) => void;\r\n}\r\n\r\ntype SortField = 'postDateTime' | 'description' | 'amount' | 'balance' | 'accountName';\r\ntype SortDirection = 'asc' | 'desc';\r\ntype FilterType = 'all' | 'debits' | 'credits' | 'duplicates';\r\n\r\ninterface TransactionFilters {\r\n  accountId: string;\r\n  dateFrom: string;\r\n  dateTo: string;\r\n  amountFrom: string;\r\n  amountTo: string;\r\n  description: string;\r\n  type: FilterType;\r\n}\r\n\r\nconst ITEMS_PER_PAGE_OPTIONS = [10, 25, 50, 100];\r\nconst DEFAULT_ITEMS_PER_PAGE = 50;\r\n\r\nexport const Transactions: React.FC<TransactionsProps> = ({ onTransactionUpdate }) => {\r\n  // State management\r\n  const [transactions, setTransactions] = useState<StoredTransaction[]>([]);\r\n  const [bankAccounts, setBankAccounts] = useState<BankAccount[]>([]);\r\n  const [duplicateGroups, setDuplicateGroups] = useState<StoredTransaction[][]>([]);\r\n  const [loading, setLoading] = useState(true);\r\n  const [error, setError] = useState<string | null>(null);\r\n  \r\n  // Pagination state\r\n  const [currentPage, setCurrentPage] = useState(1);\r\n  const [itemsPerPage, setItemsPerPage] = useState(DEFAULT_ITEMS_PER_PAGE);\r\n  \r\n  // Sorting state\r\n  const [sortField, setSortField] = useState<SortField>('postDateTime');\r\n  const [sortDirection, setSortDirection] = useState<SortDirection>('desc');\r\n  \r\n  // Filtering state\r\n  const [filters, setFilters] = useState<TransactionFilters>({\r\n    accountId: '',\r\n    dateFrom: '',\r\n    dateTo: '',\r\n    amountFrom: '',\r\n    amountTo: '',\r\n    description: '',\r\n    type: 'all'\r\n  });\r\n  \r\n  // Selection state\r\n  const [selectedTransactions, setSelectedTransactions] = useState<Set<string>>(new Set());\r\n  const [showDuplicatesOnly, setShowDuplicatesOnly] = useState(false);\r\n\r\n  // Load data on component mount\r\n  const loadData = useCallback(async () => {\r\n    try {\r\n      setLoading(true);\r\n      setError(null);\r\n      \r\n      // Load bank accounts\r\n      const accounts = bankAccountService.getAllAccounts();\r\n      setBankAccounts(accounts);\r\n      \r\n      // Load all transactions\r\n      const allTransactions: StoredTransaction[] = [];\r\n      accounts.forEach(account => {\r\n        const accountTransactions = transactionStorageService.getTransactionsByAccount(account.id);\r\n        allTransactions.push(...accountTransactions);\r\n      });\r\n      \r\n      setTransactions(allTransactions);\r\n      \r\n      // Detect duplicates within the transaction set\r\n      const duplicates = findDuplicatesInTransactions(allTransactions);\r\n      setDuplicateGroups(duplicates);\r\n      \r\n      if (onTransactionUpdate) {\r\n        onTransactionUpdate(allTransactions);\r\n      }\r\n    } catch (err) {\r\n      setError(err instanceof Error ? err.message : 'Failed to load transactions');\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  }, [onTransactionUpdate]);\r\n\r\n  useEffect(() => {\r\n    loadData();\r\n  }, [loadData]);\r\n\r\n  // Simple duplicate detection within a single set of transactions\r\n  const findDuplicatesInTransactions = (transactions: StoredTransaction[]): StoredTransaction[][] => {\r\n    const duplicateGroups: StoredTransaction[][] = [];\r\n    const processed = new Set<string>();\r\n\r\n    for (let i = 0; i < transactions.length; i++) {\r\n      if (processed.has(transactions[i].id)) continue;\r\n\r\n      const group: StoredTransaction[] = [transactions[i]];\r\n      processed.add(transactions[i].id);\r\n\r\n      for (let j = i + 1; j < transactions.length; j++) {\r\n        if (processed.has(transactions[j].id)) continue;\r\n\r\n        // Check if transactions are potential duplicates\r\n        if (arePotentialDuplicates(transactions[i], transactions[j])) {\r\n          group.push(transactions[j]);\r\n          processed.add(transactions[j].id);\r\n        }\r\n      }\r\n\r\n      // Only consider groups with 2 or more transactions as duplicates\r\n      if (group.length > 1) {\r\n        duplicateGroups.push(group);\r\n      }\r\n    }\r\n\r\n    return duplicateGroups;\r\n  };\r\n\r\n  // Check if two transactions are potential duplicates\r\n  const arePotentialDuplicates = (t1: StoredTransaction, t2: StoredTransaction): boolean => {\r\n    // Same account\r\n    if (t1.accountId !== t2.accountId) return false;\r\n\r\n    // Same date (within 1 day)\r\n    const date1 = new Date(t1.postDateTime);\r\n    const date2 = new Date(t2.postDateTime);\r\n    const daysDiff = Math.abs(date1.getTime() - date2.getTime()) / (1000 * 60 * 60 * 24);\r\n    if (daysDiff > 1) return false;\r\n\r\n    // Same amounts\r\n    const sameDebit = Math.abs((t1.debitAmount || 0) - (t2.debitAmount || 0)) < 0.01;\r\n    const sameCredit = Math.abs((t1.creditAmount || 0) - (t2.creditAmount || 0)) < 0.01;\r\n    if (!sameDebit || !sameCredit) return false;\r\n\r\n    // Similar description (at least 80% similarity)\r\n    const similarity = calculateStringSimilarity(t1.description, t2.description);\r\n    if (similarity < 0.8) return false;\r\n\r\n    return true;\r\n  };\r\n\r\n  // Calculate string similarity\r\n  const calculateStringSimilarity = (str1: string, str2: string): number => {\r\n    const longer = str1.length > str2.length ? str1 : str2;\r\n    const shorter = str1.length > str2.length ? str2 : str1;\r\n    \r\n    if (longer.length === 0) return 1.0;\r\n    \r\n    // Simple similarity based on common characters\r\n    const s1 = str1.toLowerCase();\r\n    const s2 = str2.toLowerCase();\r\n    \r\n    let matches = 0;\r\n    for (let i = 0; i < shorter.length; i++) {\r\n      if (s1.includes(s2[i])) matches++;\r\n    }\r\n    \r\n    return matches / longer.length;\r\n  };\r\n\r\n  // Filtered and sorted transactions\r\n  const filteredAndSortedTransactions = useMemo(() => {\r\n    let filtered = [...transactions];\r\n    \r\n    // Apply filters\r\n    if (filters.accountId) {\r\n      filtered = filtered.filter(t => t.accountId === filters.accountId);\r\n    }\r\n    \r\n    if (filters.dateFrom) {\r\n      const fromDate = new Date(filters.dateFrom);\r\n      filtered = filtered.filter(t => new Date(t.postDateTime) >= fromDate);\r\n    }\r\n    \r\n    if (filters.dateTo) {\r\n      const toDate = new Date(filters.dateTo);\r\n      filtered = filtered.filter(t => new Date(t.postDateTime) <= toDate);\r\n    }\r\n    \r\n    if (filters.description) {\r\n      const searchTerm = filters.description.toLowerCase();\r\n      filtered = filtered.filter(t => \r\n        t.description.toLowerCase().includes(searchTerm) ||\r\n        (t.reference && t.reference.toLowerCase().includes(searchTerm))\r\n      );\r\n    }\r\n    \r\n    if (filters.amountFrom) {\r\n      const minAmount = parseFloat(filters.amountFrom);\r\n      filtered = filtered.filter(t => {\r\n        const amount = Math.abs((t.debitAmount || 0) + (t.creditAmount || 0));\r\n        return amount >= minAmount;\r\n      });\r\n    }\r\n    \r\n    if (filters.amountTo) {\r\n      const maxAmount = parseFloat(filters.amountTo);\r\n      filtered = filtered.filter(t => {\r\n        const amount = Math.abs((t.debitAmount || 0) + (t.creditAmount || 0));\r\n        return amount <= maxAmount;\r\n      });\r\n    }\r\n    \r\n    if (filters.type === 'debits') {\r\n      filtered = filtered.filter(t => (t.debitAmount || 0) > 0);\r\n    } else if (filters.type === 'credits') {\r\n      filtered = filtered.filter(t => (t.creditAmount || 0) > 0);\r\n    }\r\n    \r\n    // Show duplicates only\r\n    if (showDuplicatesOnly) {\r\n      const duplicateIds = new Set(duplicateGroups.flat().map(t => t.id));\r\n      filtered = filtered.filter(t => duplicateIds.has(t.id));\r\n    }\r\n    \r\n    // Sort transactions\r\n    filtered.sort((a, b) => {\r\n      let aValue: string | number;\r\n      let bValue: string | number;\r\n      \r\n      switch (sortField) {\r\n        case 'postDateTime':\r\n          aValue = new Date(a.postDateTime).getTime();\r\n          bValue = new Date(b.postDateTime).getTime();\r\n          break;\r\n        case 'description':\r\n          aValue = a.description.toLowerCase();\r\n          bValue = b.description.toLowerCase();\r\n          break;\r\n        case 'amount':\r\n          aValue = Math.abs((a.debitAmount || 0) + (a.creditAmount || 0));\r\n          bValue = Math.abs((b.debitAmount || 0) + (b.creditAmount || 0));\r\n          break;\r\n        case 'balance':\r\n          aValue = a.balance;\r\n          bValue = b.balance;\r\n          break;\r\n        case 'accountName':\r\n          const accountA = bankAccounts.find(acc => acc.id === a.accountId);\r\n          const accountB = bankAccounts.find(acc => acc.id === b.accountId);\r\n          aValue = accountA?.name.toLowerCase() || '';\r\n          bValue = accountB?.name.toLowerCase() || '';\r\n          break;\r\n        default:\r\n          return 0;\r\n      }\r\n      \r\n      if (sortDirection === 'asc') {\r\n        return aValue < bValue ? -1 : aValue > bValue ? 1 : 0;\r\n      } else {\r\n        return aValue > bValue ? -1 : aValue < bValue ? 1 : 0;\r\n      }\r\n    });\r\n    \r\n    return filtered;\r\n  }, [transactions, filters, sortField, sortDirection, showDuplicatesOnly, duplicateGroups, bankAccounts]);\r\n\r\n  // Pagination calculations\r\n  const totalPages = Math.ceil(filteredAndSortedTransactions.length / itemsPerPage);\r\n  const startIndex = (currentPage - 1) * itemsPerPage;\r\n  const endIndex = startIndex + itemsPerPage;\r\n  const currentTransactions = filteredAndSortedTransactions.slice(startIndex, endIndex);\r\n\r\n  // Reset to first page when filters change\r\n  useEffect(() => {\r\n    setCurrentPage(1);\r\n  }, [filters, sortField, sortDirection, showDuplicatesOnly, itemsPerPage]);\r\n\r\n  // Event handlers\r\n  const handleSort = (field: SortField) => {\r\n    if (sortField === field) {\r\n      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');\r\n    } else {\r\n      setSortField(field);\r\n      setSortDirection('desc');\r\n    }\r\n  };\r\n\r\n  const handleFilterChange = (key: keyof TransactionFilters, value: string) => {\r\n    setFilters(prev => ({ ...prev, [key]: value }));\r\n  };\r\n\r\n  const handleSelectTransaction = (transactionId: string) => {\r\n    setSelectedTransactions(prev => {\r\n      const newSet = new Set(prev);\r\n      if (newSet.has(transactionId)) {\r\n        newSet.delete(transactionId);\r\n      } else {\r\n        newSet.add(transactionId);\r\n      }\r\n      return newSet;\r\n    });\r\n  };\r\n\r\n  const handleSelectAll = useCallback(() => {\r\n    if (selectedTransactions.size === currentTransactions.length) {\r\n      setSelectedTransactions(new Set());\r\n    } else {\r\n      setSelectedTransactions(new Set(currentTransactions.map(t => t.id)));\r\n    }\r\n  }, [selectedTransactions.size, currentTransactions]);\r\n\r\n  const clearFilters = () => {\r\n    setFilters({\r\n      accountId: '',\r\n      dateFrom: '',\r\n      dateTo: '',\r\n      amountFrom: '',\r\n      amountTo: '',\r\n      description: '',\r\n      type: 'all'\r\n    });\r\n    setShowDuplicatesOnly(false);\r\n  };\r\n\r\n  // Keyboard navigation\r\n  useEffect(() => {\r\n    const handleKeyPress = (e: KeyboardEvent) => {\r\n      if (e.target instanceof HTMLInputElement || e.target instanceof HTMLTextAreaElement) {\r\n        return; // Don't interfere with input fields\r\n      }\r\n\r\n      switch (e.key) {\r\n        case 'ArrowLeft':\r\n          if (currentPage > 1) {\r\n            setCurrentPage(currentPage - 1);\r\n          }\r\n          break;\r\n        case 'ArrowRight':\r\n          if (currentPage < totalPages) {\r\n            setCurrentPage(currentPage + 1);\r\n          }\r\n          break;\r\n        case 'Home':\r\n          setCurrentPage(1);\r\n          break;\r\n        case 'End':\r\n          setCurrentPage(totalPages);\r\n          break;\r\n        case 'f':\r\n          if (e.ctrlKey || e.metaKey) {\r\n            e.preventDefault();\r\n            document.getElementById('search-input')?.focus();\r\n          }\r\n          break;\r\n        case 'r':\r\n          if (e.ctrlKey || e.metaKey) {\r\n            e.preventDefault();\r\n            loadData();\r\n          }\r\n          break;\r\n        case 'a':\r\n          if (e.ctrlKey || e.metaKey) {\r\n            e.preventDefault();\r\n            handleSelectAll();\r\n          }\r\n          break;\r\n        case 'Escape':\r\n          setSelectedTransactions(new Set());\r\n          break;\r\n      }\r\n    };\r\n\r\n    document.addEventListener('keydown', handleKeyPress);\r\n    return () => document.removeEventListener('keydown', handleKeyPress);\r\n  }, [currentPage, totalPages, loadData, handleSelectAll]);\r\n\r\n  // Utility functions\r\n  const formatCurrency = (amount: number): string => {\r\n    return new Intl.NumberFormat('en-US', {\r\n      minimumFractionDigits: 2,\r\n      maximumFractionDigits: 2\r\n    }).format(amount);\r\n  };\r\n\r\n  const formatDate = (dateString: string): string => {\r\n    const date = new Date(dateString);\r\n    return date.toLocaleDateString('en-GB', {\r\n      day: '2-digit',\r\n      month: '2-digit',\r\n      year: 'numeric'\r\n    });\r\n  };\r\n\r\n  const getAccountName = (accountId: string): string => {\r\n    const account = bankAccounts.find(acc => acc.id === accountId);\r\n    return account?.name || 'Unknown Account';\r\n  };\r\n\r\n  const isDuplicate = (transaction: StoredTransaction): boolean => {\r\n    return duplicateGroups.some(group => group.some(t => t.id === transaction.id));\r\n  };\r\n\r\n  if (loading) {\r\n    return (\r\n      <div className=\"transactions-loading\">\r\n        <div className=\"loading-spinner\"></div>\r\n        <p>Loading transactions...</p>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  if (error) {\r\n    return (\r\n      <div className=\"transactions-error\">\r\n        <div className=\"error-icon\">⚠️</div>\r\n        <h3>Error Loading Transactions</h3>\r\n        <p>{error}</p>\r\n        <button onClick={loadData} className=\"btn btn-primary\">\r\n          Try Again\r\n        </button>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className=\"transactions\">\r\n      <div className=\"transactions-header\">\r\n        <div className=\"transactions-title-section\">\r\n          <h2 className=\"transactions-title\">Transactions</h2>\r\n          <div className=\"transactions-stats\">\r\n            <span className=\"stat-item\">\r\n              Total: <strong>{transactions.length.toLocaleString()}</strong>\r\n            </span>\r\n            <span className=\"stat-item\">\r\n              Filtered: <strong>{filteredAndSortedTransactions.length.toLocaleString()}</strong>\r\n            </span>\r\n            {duplicateGroups.length > 0 && (\r\n              <span className=\"stat-item duplicate-stat\">\r\n                Duplicates: <strong>{duplicateGroups.flat().length}</strong>\r\n              </span>\r\n            )}\r\n          </div>\r\n        </div>\r\n        <div className=\"transactions-actions\">\r\n          <button onClick={loadData} className=\"btn btn-secondary btn-sm\">\r\n            <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\">\r\n              <polyline points=\"23 4 23 10 17 10\"></polyline>\r\n              <polyline points=\"1 20 1 14 7 14\"></polyline>\r\n              <path d=\"M20.49 9A9 9 0 0 0 5.64 5.64L1 10m22 4l-4.64 4.36A9 9 0 0 1 3.51 15\"></path>\r\n            </svg>\r\n            Refresh\r\n          </button>\r\n          {selectedTransactions.size > 0 && (\r\n            <span className=\"selection-count\">\r\n              {selectedTransactions.size} selected\r\n            </span>\r\n          )}\r\n        </div>\r\n      </div>\r\n\r\n      {/* Filters Section */}\r\n      <div className=\"transactions-filters\">\r\n        <div className=\"filters-row\">\r\n          <div className=\"filter-group\">\r\n            <label className=\"filter-label\">Account</label>\r\n            <select\r\n              value={filters.accountId}\r\n              onChange={(e) => handleFilterChange('accountId', e.target.value)}\r\n              className=\"filter-select\"\r\n            >\r\n              <option value=\"\">All Accounts</option>\r\n              {bankAccounts.map(account => (\r\n                <option key={account.id} value={account.id}>\r\n                  {account.name} - {account.accountNumber}\r\n                </option>\r\n              ))}\r\n            </select>\r\n          </div>\r\n\r\n          <div className=\"filter-group\">\r\n            <label className=\"filter-label\">Type</label>\r\n            <select\r\n              value={filters.type}\r\n              onChange={(e) => handleFilterChange('type', e.target.value as FilterType)}\r\n              className=\"filter-select\"\r\n            >\r\n              <option value=\"all\">All Types</option>\r\n              <option value=\"debits\">Debits Only</option>\r\n              <option value=\"credits\">Credits Only</option>\r\n            </select>\r\n          </div>\r\n\r\n          <div className=\"filter-group\">\r\n            <label className=\"filter-label\">\r\n              <input\r\n                type=\"checkbox\"\r\n                checked={showDuplicatesOnly}\r\n                onChange={(e) => setShowDuplicatesOnly(e.target.checked)}\r\n                className=\"filter-checkbox\"\r\n              />\r\n              Show Duplicates Only\r\n            </label>\r\n          </div>\r\n        </div>\r\n\r\n        <div className=\"filters-row\">\r\n          <div className=\"filter-group\">\r\n            <label className=\"filter-label\">Search</label>\r\n            <input\r\n              id=\"search-input\"\r\n              type=\"text\"\r\n              value={filters.description}\r\n              onChange={(e) => handleFilterChange('description', e.target.value)}\r\n              placeholder=\"Search description or reference...\"\r\n              className=\"filter-input\"\r\n            />\r\n          </div>\r\n\r\n          <div className=\"filter-group\">\r\n            <label className=\"filter-label\">Date From</label>\r\n            <input\r\n              type=\"date\"\r\n              value={filters.dateFrom}\r\n              onChange={(e) => handleFilterChange('dateFrom', e.target.value)}\r\n              className=\"filter-input\"\r\n            />\r\n          </div>\r\n\r\n          <div className=\"filter-group\">\r\n            <label className=\"filter-label\">Date To</label>\r\n            <input\r\n              type=\"date\"\r\n              value={filters.dateTo}\r\n              onChange={(e) => handleFilterChange('dateTo', e.target.value)}\r\n              className=\"filter-input\"\r\n            />\r\n          </div>\r\n\r\n          <div className=\"filter-group\">\r\n            <label className=\"filter-label\">Amount Range</label>\r\n            <div className=\"amount-range\">\r\n              <input\r\n                type=\"number\"\r\n                value={filters.amountFrom}\r\n                onChange={(e) => handleFilterChange('amountFrom', e.target.value)}\r\n                placeholder=\"Min\"\r\n                className=\"filter-input amount-input\"\r\n                step=\"0.01\"\r\n              />\r\n              <span className=\"amount-separator\">-</span>\r\n              <input\r\n                type=\"number\"\r\n                value={filters.amountTo}\r\n                onChange={(e) => handleFilterChange('amountTo', e.target.value)}\r\n                placeholder=\"Max\"\r\n                className=\"filter-input amount-input\"\r\n                step=\"0.01\"\r\n              />\r\n            </div>\r\n          </div>\r\n\r\n          <div className=\"filter-group\">\r\n            <button onClick={clearFilters} className=\"btn btn-secondary btn-sm\">\r\n              Clear Filters\r\n            </button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Transactions Table */}\r\n      <div className=\"transactions-table-container\">\r\n        <table className=\"transactions-table\">\r\n          <thead>\r\n            <tr>\r\n              <th className=\"checkbox-col\">\r\n                <input\r\n                  type=\"checkbox\"\r\n                  checked={selectedTransactions.size === currentTransactions.length && currentTransactions.length > 0}\r\n                  onChange={handleSelectAll}\r\n                  className=\"table-checkbox\"\r\n                />\r\n              </th>\r\n              <th \r\n                className={`sortable ${sortField === 'postDateTime' ? 'sorted' : ''}`}\r\n                onClick={() => handleSort('postDateTime')}\r\n              >\r\n                Date\r\n                {sortField === 'postDateTime' && (\r\n                  <span className=\"sort-arrow\">\r\n                    {sortDirection === 'asc' ? '↑' : '↓'}\r\n                  </span>\r\n                )}\r\n              </th>\r\n              <th \r\n                className={`sortable ${sortField === 'accountName' ? 'sorted' : ''}`}\r\n                onClick={() => handleSort('accountName')}\r\n              >\r\n                Account\r\n                {sortField === 'accountName' && (\r\n                  <span className=\"sort-arrow\">\r\n                    {sortDirection === 'asc' ? '↑' : '↓'}\r\n                  </span>\r\n                )}\r\n              </th>\r\n              <th \r\n                className={`sortable ${sortField === 'description' ? 'sorted' : ''}`}\r\n                onClick={() => handleSort('description')}\r\n              >\r\n                Description\r\n                {sortField === 'description' && (\r\n                  <span className=\"sort-arrow\">\r\n                    {sortDirection === 'asc' ? '↑' : '↓'}\r\n                  </span>\r\n                )}\r\n              </th>\r\n              <th className=\"amount-col\">Debit</th>\r\n              <th className=\"amount-col\">Credit</th>\r\n              <th \r\n                className={`amount-col sortable ${sortField === 'balance' ? 'sorted' : ''}`}\r\n                onClick={() => handleSort('balance')}\r\n              >\r\n                Balance\r\n                {sortField === 'balance' && (\r\n                  <span className=\"sort-arrow\">\r\n                    {sortDirection === 'asc' ? '↑' : '↓'}\r\n                  </span>\r\n                )}\r\n              </th>\r\n              <th>Reference</th>\r\n            </tr>\r\n          </thead>\r\n          <tbody>\r\n            {currentTransactions.map((transaction) => (\r\n              <tr \r\n                key={transaction.id}\r\n                className={`\r\n                  ${selectedTransactions.has(transaction.id) ? 'selected' : ''}\r\n                  ${isDuplicate(transaction) ? 'duplicate' : ''}\r\n                `}\r\n              >\r\n                <td className=\"checkbox-col\">\r\n                  <input\r\n                    type=\"checkbox\"\r\n                    checked={selectedTransactions.has(transaction.id)}\r\n                    onChange={() => handleSelectTransaction(transaction.id)}\r\n                    className=\"table-checkbox\"\r\n                  />\r\n                </td>\r\n                <td className=\"date-col\">\r\n                  <div className=\"date-display\">\r\n                    {formatDate(transaction.postDateTime)}\r\n                    {transaction.time && (\r\n                      <span className=\"time-display\">{transaction.time}</span>\r\n                    )}\r\n                  </div>\r\n                </td>\r\n                <td className=\"account-col\">\r\n                  <div className=\"account-info\">\r\n                    {getAccountName(transaction.accountId)}\r\n                    {isDuplicate(transaction) && (\r\n                      <span className=\"duplicate-badge\">DUPLICATE</span>\r\n                    )}\r\n                  </div>\r\n                </td>\r\n                <td className=\"description-col\">\r\n                  <div className=\"description-content\">\r\n                    {transaction.description}\r\n                  </div>\r\n                </td>\r\n                <td className=\"amount-col debit\">\r\n                  {transaction.debitAmount ? formatCurrency(transaction.debitAmount) : ''}\r\n                </td>\r\n                <td className=\"amount-col credit\">\r\n                  {transaction.creditAmount ? formatCurrency(transaction.creditAmount) : ''}\r\n                </td>\r\n                <td className=\"amount-col balance\">\r\n                  {formatCurrency(transaction.balance)}\r\n                </td>\r\n                <td className=\"reference-col\">\r\n                  {transaction.reference || ''}\r\n                </td>\r\n              </tr>\r\n            ))}\r\n          </tbody>\r\n        </table>\r\n\r\n        {currentTransactions.length === 0 && (\r\n          <div className=\"no-transactions\">\r\n            <div className=\"no-transactions-icon\">📊</div>\r\n            <h3>No Transactions Found</h3>\r\n            <p>\r\n              {filteredAndSortedTransactions.length === 0 && transactions.length === 0\r\n                ? 'No transactions have been imported yet.'\r\n                : 'No transactions match your current filters.'}\r\n            </p>\r\n            {filteredAndSortedTransactions.length === 0 && transactions.length > 0 && (\r\n              <button onClick={clearFilters} className=\"btn btn-primary\">\r\n                Clear Filters\r\n              </button>\r\n            )}\r\n          </div>\r\n        )}\r\n      </div>\r\n\r\n      {/* Pagination */}\r\n      {totalPages > 1 && (\r\n        <div className=\"transactions-pagination\">\r\n          <div className=\"pagination-info\">\r\n            <span>\r\n              Showing {startIndex + 1}-{Math.min(endIndex, filteredAndSortedTransactions.length)} of {filteredAndSortedTransactions.length} transactions\r\n            </span>\r\n            <div className=\"items-per-page\">\r\n              <label>Items per page:</label>\r\n              <select\r\n                value={itemsPerPage}\r\n                onChange={(e) => setItemsPerPage(Number(e.target.value))}\r\n                className=\"page-size-select\"\r\n              >\r\n                {ITEMS_PER_PAGE_OPTIONS.map(size => (\r\n                  <option key={size} value={size}>{size}</option>\r\n                ))}\r\n              </select>\r\n            </div>\r\n          </div>\r\n          <div className=\"pagination-controls\">\r\n            <button\r\n              onClick={() => setCurrentPage(1)}\r\n              disabled={currentPage === 1}\r\n              className=\"btn btn-secondary btn-sm\"\r\n            >\r\n              First\r\n            </button>\r\n            <button\r\n              onClick={() => setCurrentPage(currentPage - 1)}\r\n              disabled={currentPage === 1}\r\n              className=\"btn btn-secondary btn-sm\"\r\n            >\r\n              Previous\r\n            </button>\r\n            <div className=\"pagination-pages\">\r\n              {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {\r\n                const pageNumber = Math.max(1, Math.min(totalPages - 4, currentPage - 2)) + i;\r\n                return (\r\n                  <button\r\n                    key={pageNumber}\r\n                    onClick={() => setCurrentPage(pageNumber)}\r\n                    className={`btn btn-secondary btn-sm ${currentPage === pageNumber ? 'active' : ''}`}\r\n                  >\r\n                    {pageNumber}\r\n                  </button>\r\n                );\r\n              })}\r\n            </div>\r\n            <button\r\n              onClick={() => setCurrentPage(currentPage + 1)}\r\n              disabled={currentPage === totalPages}\r\n              className=\"btn btn-secondary btn-sm\"\r\n            >\r\n              Next\r\n            </button>\r\n            <button\r\n              onClick={() => setCurrentPage(totalPages)}\r\n              disabled={currentPage === totalPages}\r\n              className=\"btn btn-secondary btn-sm\"\r\n            >\r\n              Last\r\n            </button>\r\n          </div>\r\n        </div>\r\n      )}\r\n\r\n      {/* Keyboard Shortcuts Help */}\r\n      <div className=\"keyboard-shortcuts\">\r\n        <details>\r\n          <summary>Keyboard Shortcuts</summary>\r\n          <div className=\"shortcuts-grid\">\r\n            <div className=\"shortcut-item\">\r\n              <kbd>←</kbd> <span>Previous page</span>\r\n            </div>\r\n            <div className=\"shortcut-item\">\r\n              <kbd>→</kbd> <span>Next page</span>\r\n            </div>\r\n            <div className=\"shortcut-item\">\r\n              <kbd>Home</kbd> <span>First page</span>\r\n            </div>\r\n            <div className=\"shortcut-item\">\r\n              <kbd>End</kbd> <span>Last page</span>\r\n            </div>\r\n            <div className=\"shortcut-item\">\r\n              <kbd>Ctrl+F</kbd> <span>Focus search</span>\r\n            </div>\r\n            <div className=\"shortcut-item\">\r\n              <kbd>Ctrl+R</kbd> <span>Refresh</span>\r\n            </div>\r\n            <div className=\"shortcut-item\">\r\n              <kbd>Ctrl+A</kbd> <span>Select all</span>\r\n            </div>\r\n            <div className=\"shortcut-item\">\r\n              <kbd>Esc</kbd> <span>Clear selection</span>\r\n            </div>\r\n          </div>\r\n        </details>\r\n      </div>\r\n    </div>\r\n  );\r\n}; "], "mappings": "6FAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,CAAEC,WAAW,CAAEC,OAAO,KAAQ,OAAO,CACxE,OAASC,yBAAyB,KAAgC,uCAAuC,CACzG,OAASC,kBAAkB,KAAQ,gCAAgC,CAGnE,MAAO,oBAAoB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAoB5B,KAAM,CAAAC,sBAAsB,CAAG,CAAC,EAAE,CAAE,EAAE,CAAE,EAAE,CAAE,GAAG,CAAC,CAChD,KAAM,CAAAC,sBAAsB,CAAG,EAAE,CAEjC,MAAO,MAAM,CAAAC,YAAyC,CAAGC,IAAA,EAA6B,IAA5B,CAAEC,mBAAoB,CAAC,CAAAD,IAAA,CAC/E;AACA,KAAM,CAACE,YAAY,CAAEC,eAAe,CAAC,CAAGhB,QAAQ,CAAsB,EAAE,CAAC,CACzE,KAAM,CAACiB,YAAY,CAAEC,eAAe,CAAC,CAAGlB,QAAQ,CAAgB,EAAE,CAAC,CACnE,KAAM,CAACmB,eAAe,CAAEC,kBAAkB,CAAC,CAAGpB,QAAQ,CAAwB,EAAE,CAAC,CACjF,KAAM,CAACqB,OAAO,CAAEC,UAAU,CAAC,CAAGtB,QAAQ,CAAC,IAAI,CAAC,CAC5C,KAAM,CAACuB,KAAK,CAAEC,QAAQ,CAAC,CAAGxB,QAAQ,CAAgB,IAAI,CAAC,CAEvD;AACA,KAAM,CAACyB,WAAW,CAAEC,cAAc,CAAC,CAAG1B,QAAQ,CAAC,CAAC,CAAC,CACjD,KAAM,CAAC2B,YAAY,CAAEC,eAAe,CAAC,CAAG5B,QAAQ,CAACW,sBAAsB,CAAC,CAExE;AACA,KAAM,CAACkB,SAAS,CAAEC,YAAY,CAAC,CAAG9B,QAAQ,CAAY,cAAc,CAAC,CACrE,KAAM,CAAC+B,aAAa,CAAEC,gBAAgB,CAAC,CAAGhC,QAAQ,CAAgB,MAAM,CAAC,CAEzE;AACA,KAAM,CAACiC,OAAO,CAAEC,UAAU,CAAC,CAAGlC,QAAQ,CAAqB,CACzDmC,SAAS,CAAE,EAAE,CACbC,QAAQ,CAAE,EAAE,CACZC,MAAM,CAAE,EAAE,CACVC,UAAU,CAAE,EAAE,CACdC,QAAQ,CAAE,EAAE,CACZC,WAAW,CAAE,EAAE,CACfC,IAAI,CAAE,KACR,CAAC,CAAC,CAEF;AACA,KAAM,CAACC,oBAAoB,CAAEC,uBAAuB,CAAC,CAAG3C,QAAQ,CAAc,GAAI,CAAA4C,GAAG,CAAC,CAAC,CAAC,CACxF,KAAM,CAACC,kBAAkB,CAAEC,qBAAqB,CAAC,CAAG9C,QAAQ,CAAC,KAAK,CAAC,CAEnE;AACA,KAAM,CAAA+C,QAAQ,CAAG7C,WAAW,CAAC,SAAY,CACvC,GAAI,CACFoB,UAAU,CAAC,IAAI,CAAC,CAChBE,QAAQ,CAAC,IAAI,CAAC,CAEd;AACA,KAAM,CAAAwB,QAAQ,CAAG3C,kBAAkB,CAAC4C,cAAc,CAAC,CAAC,CACpD/B,eAAe,CAAC8B,QAAQ,CAAC,CAEzB;AACA,KAAM,CAAAE,eAAoC,CAAG,EAAE,CAC/CF,QAAQ,CAACG,OAAO,CAACC,OAAO,EAAI,CAC1B,KAAM,CAAAC,mBAAmB,CAAGjD,yBAAyB,CAACkD,wBAAwB,CAACF,OAAO,CAACG,EAAE,CAAC,CAC1FL,eAAe,CAACM,IAAI,CAAC,GAAGH,mBAAmB,CAAC,CAC9C,CAAC,CAAC,CAEFrC,eAAe,CAACkC,eAAe,CAAC,CAEhC;AACA,KAAM,CAAAO,UAAU,CAAGC,4BAA4B,CAACR,eAAe,CAAC,CAChE9B,kBAAkB,CAACqC,UAAU,CAAC,CAE9B,GAAI3C,mBAAmB,CAAE,CACvBA,mBAAmB,CAACoC,eAAe,CAAC,CACtC,CACF,CAAE,MAAOS,GAAG,CAAE,CACZnC,QAAQ,CAACmC,GAAG,WAAY,CAAAC,KAAK,CAAGD,GAAG,CAACE,OAAO,CAAG,6BAA6B,CAAC,CAC9E,CAAC,OAAS,CACRvC,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAAE,CAACR,mBAAmB,CAAC,CAAC,CAEzBb,SAAS,CAAC,IAAM,CACd8C,QAAQ,CAAC,CAAC,CACZ,CAAC,CAAE,CAACA,QAAQ,CAAC,CAAC,CAEd;AACA,KAAM,CAAAW,4BAA4B,CAAI3C,YAAiC,EAA4B,CACjG,KAAM,CAAAI,eAAsC,CAAG,EAAE,CACjD,KAAM,CAAA2C,SAAS,CAAG,GAAI,CAAAlB,GAAG,CAAS,CAAC,CAEnC,IAAK,GAAI,CAAAmB,CAAC,CAAG,CAAC,CAAEA,CAAC,CAAGhD,YAAY,CAACiD,MAAM,CAAED,CAAC,EAAE,CAAE,CAC5C,GAAID,SAAS,CAACG,GAAG,CAAClD,YAAY,CAACgD,CAAC,CAAC,CAACR,EAAE,CAAC,CAAE,SAEvC,KAAM,CAAAW,KAA0B,CAAG,CAACnD,YAAY,CAACgD,CAAC,CAAC,CAAC,CACpDD,SAAS,CAACK,GAAG,CAACpD,YAAY,CAACgD,CAAC,CAAC,CAACR,EAAE,CAAC,CAEjC,IAAK,GAAI,CAAAa,CAAC,CAAGL,CAAC,CAAG,CAAC,CAAEK,CAAC,CAAGrD,YAAY,CAACiD,MAAM,CAAEI,CAAC,EAAE,CAAE,CAChD,GAAIN,SAAS,CAACG,GAAG,CAAClD,YAAY,CAACqD,CAAC,CAAC,CAACb,EAAE,CAAC,CAAE,SAEvC;AACA,GAAIc,sBAAsB,CAACtD,YAAY,CAACgD,CAAC,CAAC,CAAEhD,YAAY,CAACqD,CAAC,CAAC,CAAC,CAAE,CAC5DF,KAAK,CAACV,IAAI,CAACzC,YAAY,CAACqD,CAAC,CAAC,CAAC,CAC3BN,SAAS,CAACK,GAAG,CAACpD,YAAY,CAACqD,CAAC,CAAC,CAACb,EAAE,CAAC,CACnC,CACF,CAEA;AACA,GAAIW,KAAK,CAACF,MAAM,CAAG,CAAC,CAAE,CACpB7C,eAAe,CAACqC,IAAI,CAACU,KAAK,CAAC,CAC7B,CACF,CAEA,MAAO,CAAA/C,eAAe,CACxB,CAAC,CAED;AACA,KAAM,CAAAkD,sBAAsB,CAAGA,CAACC,EAAqB,CAAEC,EAAqB,GAAc,CACxF;AACA,GAAID,EAAE,CAACnC,SAAS,GAAKoC,EAAE,CAACpC,SAAS,CAAE,MAAO,MAAK,CAE/C;AACA,KAAM,CAAAqC,KAAK,CAAG,GAAI,CAAAC,IAAI,CAACH,EAAE,CAACI,YAAY,CAAC,CACvC,KAAM,CAAAC,KAAK,CAAG,GAAI,CAAAF,IAAI,CAACF,EAAE,CAACG,YAAY,CAAC,CACvC,KAAM,CAAAE,QAAQ,CAAGC,IAAI,CAACC,GAAG,CAACN,KAAK,CAACO,OAAO,CAAC,CAAC,CAAGJ,KAAK,CAACI,OAAO,CAAC,CAAC,CAAC,EAAI,IAAI,CAAG,EAAE,CAAG,EAAE,CAAG,EAAE,CAAC,CACpF,GAAIH,QAAQ,CAAG,CAAC,CAAE,MAAO,MAAK,CAE9B;AACA,KAAM,CAAAI,SAAS,CAAGH,IAAI,CAACC,GAAG,CAAC,CAACR,EAAE,CAACW,WAAW,EAAI,CAAC,GAAKV,EAAE,CAACU,WAAW,EAAI,CAAC,CAAC,CAAC,CAAG,IAAI,CAChF,KAAM,CAAAC,UAAU,CAAGL,IAAI,CAACC,GAAG,CAAC,CAACR,EAAE,CAACa,YAAY,EAAI,CAAC,GAAKZ,EAAE,CAACY,YAAY,EAAI,CAAC,CAAC,CAAC,CAAG,IAAI,CACnF,GAAI,CAACH,SAAS,EAAI,CAACE,UAAU,CAAE,MAAO,MAAK,CAE3C;AACA,KAAM,CAAAE,UAAU,CAAGC,yBAAyB,CAACf,EAAE,CAAC9B,WAAW,CAAE+B,EAAE,CAAC/B,WAAW,CAAC,CAC5E,GAAI4C,UAAU,CAAG,GAAG,CAAE,MAAO,MAAK,CAElC,MAAO,KAAI,CACb,CAAC,CAED;AACA,KAAM,CAAAC,yBAAyB,CAAGA,CAACC,IAAY,CAAEC,IAAY,GAAa,CACxE,KAAM,CAAAC,MAAM,CAAGF,IAAI,CAACtB,MAAM,CAAGuB,IAAI,CAACvB,MAAM,CAAGsB,IAAI,CAAGC,IAAI,CACtD,KAAM,CAAAE,OAAO,CAAGH,IAAI,CAACtB,MAAM,CAAGuB,IAAI,CAACvB,MAAM,CAAGuB,IAAI,CAAGD,IAAI,CAEvD,GAAIE,MAAM,CAACxB,MAAM,GAAK,CAAC,CAAE,MAAO,IAAG,CAEnC;AACA,KAAM,CAAA0B,EAAE,CAAGJ,IAAI,CAACK,WAAW,CAAC,CAAC,CAC7B,KAAM,CAAAC,EAAE,CAAGL,IAAI,CAACI,WAAW,CAAC,CAAC,CAE7B,GAAI,CAAAE,OAAO,CAAG,CAAC,CACf,IAAK,GAAI,CAAA9B,CAAC,CAAG,CAAC,CAAEA,CAAC,CAAG0B,OAAO,CAACzB,MAAM,CAAED,CAAC,EAAE,CAAE,CACvC,GAAI2B,EAAE,CAACI,QAAQ,CAACF,EAAE,CAAC7B,CAAC,CAAC,CAAC,CAAE8B,OAAO,EAAE,CACnC,CAEA,MAAO,CAAAA,OAAO,CAAGL,MAAM,CAACxB,MAAM,CAChC,CAAC,CAED;AACA,KAAM,CAAA+B,6BAA6B,CAAG5F,OAAO,CAAC,IAAM,CAClD,GAAI,CAAA6F,QAAQ,CAAG,CAAC,GAAGjF,YAAY,CAAC,CAEhC;AACA,GAAIkB,OAAO,CAACE,SAAS,CAAE,CACrB6D,QAAQ,CAAGA,QAAQ,CAACC,MAAM,CAACC,CAAC,EAAIA,CAAC,CAAC/D,SAAS,GAAKF,OAAO,CAACE,SAAS,CAAC,CACpE,CAEA,GAAIF,OAAO,CAACG,QAAQ,CAAE,CACpB,KAAM,CAAA+D,QAAQ,CAAG,GAAI,CAAA1B,IAAI,CAACxC,OAAO,CAACG,QAAQ,CAAC,CAC3C4D,QAAQ,CAAGA,QAAQ,CAACC,MAAM,CAACC,CAAC,EAAI,GAAI,CAAAzB,IAAI,CAACyB,CAAC,CAACxB,YAAY,CAAC,EAAIyB,QAAQ,CAAC,CACvE,CAEA,GAAIlE,OAAO,CAACI,MAAM,CAAE,CAClB,KAAM,CAAA+D,MAAM,CAAG,GAAI,CAAA3B,IAAI,CAACxC,OAAO,CAACI,MAAM,CAAC,CACvC2D,QAAQ,CAAGA,QAAQ,CAACC,MAAM,CAACC,CAAC,EAAI,GAAI,CAAAzB,IAAI,CAACyB,CAAC,CAACxB,YAAY,CAAC,EAAI0B,MAAM,CAAC,CACrE,CAEA,GAAInE,OAAO,CAACO,WAAW,CAAE,CACvB,KAAM,CAAA6D,UAAU,CAAGpE,OAAO,CAACO,WAAW,CAACmD,WAAW,CAAC,CAAC,CACpDK,QAAQ,CAAGA,QAAQ,CAACC,MAAM,CAACC,CAAC,EAC1BA,CAAC,CAAC1D,WAAW,CAACmD,WAAW,CAAC,CAAC,CAACG,QAAQ,CAACO,UAAU,CAAC,EAC/CH,CAAC,CAACI,SAAS,EAAIJ,CAAC,CAACI,SAAS,CAACX,WAAW,CAAC,CAAC,CAACG,QAAQ,CAACO,UAAU,CAC/D,CAAC,CACH,CAEA,GAAIpE,OAAO,CAACK,UAAU,CAAE,CACtB,KAAM,CAAAiE,SAAS,CAAGC,UAAU,CAACvE,OAAO,CAACK,UAAU,CAAC,CAChD0D,QAAQ,CAAGA,QAAQ,CAACC,MAAM,CAACC,CAAC,EAAI,CAC9B,KAAM,CAAAO,MAAM,CAAG5B,IAAI,CAACC,GAAG,CAAC,CAACoB,CAAC,CAACjB,WAAW,EAAI,CAAC,GAAKiB,CAAC,CAACf,YAAY,EAAI,CAAC,CAAC,CAAC,CACrE,MAAO,CAAAsB,MAAM,EAAIF,SAAS,CAC5B,CAAC,CAAC,CACJ,CAEA,GAAItE,OAAO,CAACM,QAAQ,CAAE,CACpB,KAAM,CAAAmE,SAAS,CAAGF,UAAU,CAACvE,OAAO,CAACM,QAAQ,CAAC,CAC9CyD,QAAQ,CAAGA,QAAQ,CAACC,MAAM,CAACC,CAAC,EAAI,CAC9B,KAAM,CAAAO,MAAM,CAAG5B,IAAI,CAACC,GAAG,CAAC,CAACoB,CAAC,CAACjB,WAAW,EAAI,CAAC,GAAKiB,CAAC,CAACf,YAAY,EAAI,CAAC,CAAC,CAAC,CACrE,MAAO,CAAAsB,MAAM,EAAIC,SAAS,CAC5B,CAAC,CAAC,CACJ,CAEA,GAAIzE,OAAO,CAACQ,IAAI,GAAK,QAAQ,CAAE,CAC7BuD,QAAQ,CAAGA,QAAQ,CAACC,MAAM,CAACC,CAAC,EAAI,CAACA,CAAC,CAACjB,WAAW,EAAI,CAAC,EAAI,CAAC,CAAC,CAC3D,CAAC,IAAM,IAAIhD,OAAO,CAACQ,IAAI,GAAK,SAAS,CAAE,CACrCuD,QAAQ,CAAGA,QAAQ,CAACC,MAAM,CAACC,CAAC,EAAI,CAACA,CAAC,CAACf,YAAY,EAAI,CAAC,EAAI,CAAC,CAAC,CAC5D,CAEA;AACA,GAAItC,kBAAkB,CAAE,CACtB,KAAM,CAAA8D,YAAY,CAAG,GAAI,CAAA/D,GAAG,CAACzB,eAAe,CAACyF,IAAI,CAAC,CAAC,CAACC,GAAG,CAACX,CAAC,EAAIA,CAAC,CAAC3C,EAAE,CAAC,CAAC,CACnEyC,QAAQ,CAAGA,QAAQ,CAACC,MAAM,CAACC,CAAC,EAAIS,YAAY,CAAC1C,GAAG,CAACiC,CAAC,CAAC3C,EAAE,CAAC,CAAC,CACzD,CAEA;AACAyC,QAAQ,CAACc,IAAI,CAAC,CAACC,CAAC,CAAEC,CAAC,GAAK,CACtB,GAAI,CAAAC,MAAuB,CAC3B,GAAI,CAAAC,MAAuB,CAE3B,OAAQrF,SAAS,EACf,IAAK,cAAc,CACjBoF,MAAM,CAAG,GAAI,CAAAxC,IAAI,CAACsC,CAAC,CAACrC,YAAY,CAAC,CAACK,OAAO,CAAC,CAAC,CAC3CmC,MAAM,CAAG,GAAI,CAAAzC,IAAI,CAACuC,CAAC,CAACtC,YAAY,CAAC,CAACK,OAAO,CAAC,CAAC,CAC3C,MACF,IAAK,aAAa,CAChBkC,MAAM,CAAGF,CAAC,CAACvE,WAAW,CAACmD,WAAW,CAAC,CAAC,CACpCuB,MAAM,CAAGF,CAAC,CAACxE,WAAW,CAACmD,WAAW,CAAC,CAAC,CACpC,MACF,IAAK,QAAQ,CACXsB,MAAM,CAAGpC,IAAI,CAACC,GAAG,CAAC,CAACiC,CAAC,CAAC9B,WAAW,EAAI,CAAC,GAAK8B,CAAC,CAAC5B,YAAY,EAAI,CAAC,CAAC,CAAC,CAC/D+B,MAAM,CAAGrC,IAAI,CAACC,GAAG,CAAC,CAACkC,CAAC,CAAC/B,WAAW,EAAI,CAAC,GAAK+B,CAAC,CAAC7B,YAAY,EAAI,CAAC,CAAC,CAAC,CAC/D,MACF,IAAK,SAAS,CACZ8B,MAAM,CAAGF,CAAC,CAACI,OAAO,CAClBD,MAAM,CAAGF,CAAC,CAACG,OAAO,CAClB,MACF,IAAK,aAAa,CAChB,KAAM,CAAAC,QAAQ,CAAGnG,YAAY,CAACoG,IAAI,CAACC,GAAG,EAAIA,GAAG,CAAC/D,EAAE,GAAKwD,CAAC,CAAC5E,SAAS,CAAC,CACjE,KAAM,CAAAoF,QAAQ,CAAGtG,YAAY,CAACoG,IAAI,CAACC,GAAG,EAAIA,GAAG,CAAC/D,EAAE,GAAKyD,CAAC,CAAC7E,SAAS,CAAC,CACjE8E,MAAM,CAAG,CAAAG,QAAQ,SAARA,QAAQ,iBAARA,QAAQ,CAAEI,IAAI,CAAC7B,WAAW,CAAC,CAAC,GAAI,EAAE,CAC3CuB,MAAM,CAAG,CAAAK,QAAQ,SAARA,QAAQ,iBAARA,QAAQ,CAAEC,IAAI,CAAC7B,WAAW,CAAC,CAAC,GAAI,EAAE,CAC3C,MACF,QACE,MAAO,EAAC,CACZ,CAEA,GAAI5D,aAAa,GAAK,KAAK,CAAE,CAC3B,MAAO,CAAAkF,MAAM,CAAGC,MAAM,CAAG,CAAC,CAAC,CAAGD,MAAM,CAAGC,MAAM,CAAG,CAAC,CAAG,CAAC,CACvD,CAAC,IAAM,CACL,MAAO,CAAAD,MAAM,CAAGC,MAAM,CAAG,CAAC,CAAC,CAAGD,MAAM,CAAGC,MAAM,CAAG,CAAC,CAAG,CAAC,CACvD,CACF,CAAC,CAAC,CAEF,MAAO,CAAAlB,QAAQ,CACjB,CAAC,CAAE,CAACjF,YAAY,CAAEkB,OAAO,CAAEJ,SAAS,CAAEE,aAAa,CAAEc,kBAAkB,CAAE1B,eAAe,CAAEF,YAAY,CAAC,CAAC,CAExG;AACA,KAAM,CAAAwG,UAAU,CAAG5C,IAAI,CAAC6C,IAAI,CAAC3B,6BAA6B,CAAC/B,MAAM,CAAGrC,YAAY,CAAC,CACjF,KAAM,CAAAgG,UAAU,CAAG,CAAClG,WAAW,CAAG,CAAC,EAAIE,YAAY,CACnD,KAAM,CAAAiG,QAAQ,CAAGD,UAAU,CAAGhG,YAAY,CAC1C,KAAM,CAAAkG,mBAAmB,CAAG9B,6BAA6B,CAAC+B,KAAK,CAACH,UAAU,CAAEC,QAAQ,CAAC,CAErF;AACA3H,SAAS,CAAC,IAAM,CACdyB,cAAc,CAAC,CAAC,CAAC,CACnB,CAAC,CAAE,CAACO,OAAO,CAAEJ,SAAS,CAAEE,aAAa,CAAEc,kBAAkB,CAAElB,YAAY,CAAC,CAAC,CAEzE;AACA,KAAM,CAAAoG,UAAU,CAAIC,KAAgB,EAAK,CACvC,GAAInG,SAAS,GAAKmG,KAAK,CAAE,CACvBhG,gBAAgB,CAACD,aAAa,GAAK,KAAK,CAAG,MAAM,CAAG,KAAK,CAAC,CAC5D,CAAC,IAAM,CACLD,YAAY,CAACkG,KAAK,CAAC,CACnBhG,gBAAgB,CAAC,MAAM,CAAC,CAC1B,CACF,CAAC,CAED,KAAM,CAAAiG,kBAAkB,CAAGA,CAACC,GAA6B,CAAEC,KAAa,GAAK,CAC3EjG,UAAU,CAACkG,IAAI,EAAAC,aAAA,CAAAA,aAAA,IAAUD,IAAI,MAAE,CAACF,GAAG,EAAGC,KAAK,EAAG,CAAC,CACjD,CAAC,CAED,KAAM,CAAAG,uBAAuB,CAAIC,aAAqB,EAAK,CACzD5F,uBAAuB,CAACyF,IAAI,EAAI,CAC9B,KAAM,CAAAI,MAAM,CAAG,GAAI,CAAA5F,GAAG,CAACwF,IAAI,CAAC,CAC5B,GAAII,MAAM,CAACvE,GAAG,CAACsE,aAAa,CAAC,CAAE,CAC7BC,MAAM,CAACC,MAAM,CAACF,aAAa,CAAC,CAC9B,CAAC,IAAM,CACLC,MAAM,CAACrE,GAAG,CAACoE,aAAa,CAAC,CAC3B,CACA,MAAO,CAAAC,MAAM,CACf,CAAC,CAAC,CACJ,CAAC,CAED,KAAM,CAAAE,eAAe,CAAGxI,WAAW,CAAC,IAAM,CACxC,GAAIwC,oBAAoB,CAACiG,IAAI,GAAKd,mBAAmB,CAAC7D,MAAM,CAAE,CAC5DrB,uBAAuB,CAAC,GAAI,CAAAC,GAAG,CAAC,CAAC,CAAC,CACpC,CAAC,IAAM,CACLD,uBAAuB,CAAC,GAAI,CAAAC,GAAG,CAACiF,mBAAmB,CAAChB,GAAG,CAACX,CAAC,EAAIA,CAAC,CAAC3C,EAAE,CAAC,CAAC,CAAC,CACtE,CACF,CAAC,CAAE,CAACb,oBAAoB,CAACiG,IAAI,CAAEd,mBAAmB,CAAC,CAAC,CAEpD,KAAM,CAAAe,YAAY,CAAGA,CAAA,GAAM,CACzB1G,UAAU,CAAC,CACTC,SAAS,CAAE,EAAE,CACbC,QAAQ,CAAE,EAAE,CACZC,MAAM,CAAE,EAAE,CACVC,UAAU,CAAE,EAAE,CACdC,QAAQ,CAAE,EAAE,CACZC,WAAW,CAAE,EAAE,CACfC,IAAI,CAAE,KACR,CAAC,CAAC,CACFK,qBAAqB,CAAC,KAAK,CAAC,CAC9B,CAAC,CAED;AACA7C,SAAS,CAAC,IAAM,CACd,KAAM,CAAA4I,cAAc,CAAIC,CAAgB,EAAK,CAC3C,GAAIA,CAAC,CAACC,MAAM,WAAY,CAAAC,gBAAgB,EAAIF,CAAC,CAACC,MAAM,WAAY,CAAAE,mBAAmB,CAAE,CACnF,OAAQ;AACV,CAEA,OAAQH,CAAC,CAACZ,GAAG,EACX,IAAK,WAAW,CACd,GAAIzG,WAAW,CAAG,CAAC,CAAE,CACnBC,cAAc,CAACD,WAAW,CAAG,CAAC,CAAC,CACjC,CACA,MACF,IAAK,YAAY,CACf,GAAIA,WAAW,CAAGgG,UAAU,CAAE,CAC5B/F,cAAc,CAACD,WAAW,CAAG,CAAC,CAAC,CACjC,CACA,MACF,IAAK,MAAM,CACTC,cAAc,CAAC,CAAC,CAAC,CACjB,MACF,IAAK,KAAK,CACRA,cAAc,CAAC+F,UAAU,CAAC,CAC1B,MACF,IAAK,GAAG,CACN,GAAIqB,CAAC,CAACI,OAAO,EAAIJ,CAAC,CAACK,OAAO,CAAE,KAAAC,qBAAA,CAC1BN,CAAC,CAACO,cAAc,CAAC,CAAC,CAClB,CAAAD,qBAAA,CAAAE,QAAQ,CAACC,cAAc,CAAC,cAAc,CAAC,UAAAH,qBAAA,iBAAvCA,qBAAA,CAAyCI,KAAK,CAAC,CAAC,CAClD,CACA,MACF,IAAK,GAAG,CACN,GAAIV,CAAC,CAACI,OAAO,EAAIJ,CAAC,CAACK,OAAO,CAAE,CAC1BL,CAAC,CAACO,cAAc,CAAC,CAAC,CAClBtG,QAAQ,CAAC,CAAC,CACZ,CACA,MACF,IAAK,GAAG,CACN,GAAI+F,CAAC,CAACI,OAAO,EAAIJ,CAAC,CAACK,OAAO,CAAE,CAC1BL,CAAC,CAACO,cAAc,CAAC,CAAC,CAClBX,eAAe,CAAC,CAAC,CACnB,CACA,MACF,IAAK,QAAQ,CACX/F,uBAAuB,CAAC,GAAI,CAAAC,GAAG,CAAC,CAAC,CAAC,CAClC,MACJ,CACF,CAAC,CAED0G,QAAQ,CAACG,gBAAgB,CAAC,SAAS,CAAEZ,cAAc,CAAC,CACpD,MAAO,IAAMS,QAAQ,CAACI,mBAAmB,CAAC,SAAS,CAAEb,cAAc,CAAC,CACtE,CAAC,CAAE,CAACpH,WAAW,CAAEgG,UAAU,CAAE1E,QAAQ,CAAE2F,eAAe,CAAC,CAAC,CAExD;AACA,KAAM,CAAAiB,cAAc,CAAIlD,MAAc,EAAa,CACjD,MAAO,IAAI,CAAAmD,IAAI,CAACC,YAAY,CAAC,OAAO,CAAE,CACpCC,qBAAqB,CAAE,CAAC,CACxBC,qBAAqB,CAAE,CACzB,CAAC,CAAC,CAACC,MAAM,CAACvD,MAAM,CAAC,CACnB,CAAC,CAED,KAAM,CAAAwD,UAAU,CAAIC,UAAkB,EAAa,CACjD,KAAM,CAAAC,IAAI,CAAG,GAAI,CAAA1F,IAAI,CAACyF,UAAU,CAAC,CACjC,MAAO,CAAAC,IAAI,CAACC,kBAAkB,CAAC,OAAO,CAAE,CACtCC,GAAG,CAAE,SAAS,CACdC,KAAK,CAAE,SAAS,CAChBC,IAAI,CAAE,SACR,CAAC,CAAC,CACJ,CAAC,CAED,KAAM,CAAAC,cAAc,CAAIrI,SAAiB,EAAa,CACpD,KAAM,CAAAiB,OAAO,CAAGnC,YAAY,CAACoG,IAAI,CAACC,GAAG,EAAIA,GAAG,CAAC/D,EAAE,GAAKpB,SAAS,CAAC,CAC9D,MAAO,CAAAiB,OAAO,SAAPA,OAAO,iBAAPA,OAAO,CAAEoE,IAAI,GAAI,iBAAiB,CAC3C,CAAC,CAED,KAAM,CAAAiD,WAAW,CAAIC,WAA8B,EAAc,CAC/D,MAAO,CAAAvJ,eAAe,CAACwJ,IAAI,CAACzG,KAAK,EAAIA,KAAK,CAACyG,IAAI,CAACzE,CAAC,EAAIA,CAAC,CAAC3C,EAAE,GAAKmH,WAAW,CAACnH,EAAE,CAAC,CAAC,CAChF,CAAC,CAED,GAAIlC,OAAO,CAAE,CACX,mBACEZ,KAAA,QAAKmK,SAAS,CAAC,sBAAsB,CAAAC,QAAA,eACnCtK,IAAA,QAAKqK,SAAS,CAAC,iBAAiB,CAAM,CAAC,cACvCrK,IAAA,MAAAsK,QAAA,CAAG,yBAAuB,CAAG,CAAC,EAC3B,CAAC,CAEV,CAEA,GAAItJ,KAAK,CAAE,CACT,mBACEd,KAAA,QAAKmK,SAAS,CAAC,oBAAoB,CAAAC,QAAA,eACjCtK,IAAA,QAAKqK,SAAS,CAAC,YAAY,CAAAC,QAAA,CAAC,cAAE,CAAK,CAAC,cACpCtK,IAAA,OAAAsK,QAAA,CAAI,4BAA0B,CAAI,CAAC,cACnCtK,IAAA,MAAAsK,QAAA,CAAItJ,KAAK,CAAI,CAAC,cACdhB,IAAA,WAAQuK,OAAO,CAAE/H,QAAS,CAAC6H,SAAS,CAAC,iBAAiB,CAAAC,QAAA,CAAC,WAEvD,CAAQ,CAAC,EACN,CAAC,CAEV,CAEA,mBACEpK,KAAA,QAAKmK,SAAS,CAAC,cAAc,CAAAC,QAAA,eAC3BpK,KAAA,QAAKmK,SAAS,CAAC,qBAAqB,CAAAC,QAAA,eAClCpK,KAAA,QAAKmK,SAAS,CAAC,4BAA4B,CAAAC,QAAA,eACzCtK,IAAA,OAAIqK,SAAS,CAAC,oBAAoB,CAAAC,QAAA,CAAC,cAAY,CAAI,CAAC,cACpDpK,KAAA,QAAKmK,SAAS,CAAC,oBAAoB,CAAAC,QAAA,eACjCpK,KAAA,SAAMmK,SAAS,CAAC,WAAW,CAAAC,QAAA,EAAC,SACnB,cAAAtK,IAAA,WAAAsK,QAAA,CAAS9J,YAAY,CAACiD,MAAM,CAAC+G,cAAc,CAAC,CAAC,CAAS,CAAC,EAC1D,CAAC,cACPtK,KAAA,SAAMmK,SAAS,CAAC,WAAW,CAAAC,QAAA,EAAC,YAChB,cAAAtK,IAAA,WAAAsK,QAAA,CAAS9E,6BAA6B,CAAC/B,MAAM,CAAC+G,cAAc,CAAC,CAAC,CAAS,CAAC,EAC9E,CAAC,CACN5J,eAAe,CAAC6C,MAAM,CAAG,CAAC,eACzBvD,KAAA,SAAMmK,SAAS,CAAC,0BAA0B,CAAAC,QAAA,EAAC,cAC7B,cAAAtK,IAAA,WAAAsK,QAAA,CAAS1J,eAAe,CAACyF,IAAI,CAAC,CAAC,CAAC5C,MAAM,CAAS,CAAC,EACxD,CACP,EACE,CAAC,EACH,CAAC,cACNvD,KAAA,QAAKmK,SAAS,CAAC,sBAAsB,CAAAC,QAAA,eACnCpK,KAAA,WAAQqK,OAAO,CAAE/H,QAAS,CAAC6H,SAAS,CAAC,0BAA0B,CAAAC,QAAA,eAC7DpK,KAAA,QAAKuK,KAAK,CAAC,IAAI,CAACC,MAAM,CAAC,IAAI,CAACC,OAAO,CAAC,WAAW,CAACC,IAAI,CAAC,MAAM,CAACC,MAAM,CAAC,cAAc,CAACC,WAAW,CAAC,GAAG,CAAAR,QAAA,eAC/FtK,IAAA,aAAU+K,MAAM,CAAC,kBAAkB,CAAW,CAAC,cAC/C/K,IAAA,aAAU+K,MAAM,CAAC,gBAAgB,CAAW,CAAC,cAC7C/K,IAAA,SAAMgL,CAAC,CAAC,qEAAqE,CAAO,CAAC,EAClF,CAAC,UAER,EAAQ,CAAC,CACR7I,oBAAoB,CAACiG,IAAI,CAAG,CAAC,eAC5BlI,KAAA,SAAMmK,SAAS,CAAC,iBAAiB,CAAAC,QAAA,EAC9BnI,oBAAoB,CAACiG,IAAI,CAAC,WAC7B,EAAM,CACP,EACE,CAAC,EACH,CAAC,cAGNlI,KAAA,QAAKmK,SAAS,CAAC,sBAAsB,CAAAC,QAAA,eACnCpK,KAAA,QAAKmK,SAAS,CAAC,aAAa,CAAAC,QAAA,eAC1BpK,KAAA,QAAKmK,SAAS,CAAC,cAAc,CAAAC,QAAA,eAC3BtK,IAAA,UAAOqK,SAAS,CAAC,cAAc,CAAAC,QAAA,CAAC,SAAO,CAAO,CAAC,cAC/CpK,KAAA,WACE0H,KAAK,CAAElG,OAAO,CAACE,SAAU,CACzBqJ,QAAQ,CAAG1C,CAAC,EAAKb,kBAAkB,CAAC,WAAW,CAAEa,CAAC,CAACC,MAAM,CAACZ,KAAK,CAAE,CACjEyC,SAAS,CAAC,eAAe,CAAAC,QAAA,eAEzBtK,IAAA,WAAQ4H,KAAK,CAAC,EAAE,CAAA0C,QAAA,CAAC,cAAY,CAAQ,CAAC,CACrC5J,YAAY,CAAC4F,GAAG,CAACzD,OAAO,eACvB3C,KAAA,WAAyB0H,KAAK,CAAE/E,OAAO,CAACG,EAAG,CAAAsH,QAAA,EACxCzH,OAAO,CAACoE,IAAI,CAAC,KAAG,CAACpE,OAAO,CAACqI,aAAa,GAD5BrI,OAAO,CAACG,EAEb,CACT,CAAC,EACI,CAAC,EACN,CAAC,cAEN9C,KAAA,QAAKmK,SAAS,CAAC,cAAc,CAAAC,QAAA,eAC3BtK,IAAA,UAAOqK,SAAS,CAAC,cAAc,CAAAC,QAAA,CAAC,MAAI,CAAO,CAAC,cAC5CpK,KAAA,WACE0H,KAAK,CAAElG,OAAO,CAACQ,IAAK,CACpB+I,QAAQ,CAAG1C,CAAC,EAAKb,kBAAkB,CAAC,MAAM,CAAEa,CAAC,CAACC,MAAM,CAACZ,KAAmB,CAAE,CAC1EyC,SAAS,CAAC,eAAe,CAAAC,QAAA,eAEzBtK,IAAA,WAAQ4H,KAAK,CAAC,KAAK,CAAA0C,QAAA,CAAC,WAAS,CAAQ,CAAC,cACtCtK,IAAA,WAAQ4H,KAAK,CAAC,QAAQ,CAAA0C,QAAA,CAAC,aAAW,CAAQ,CAAC,cAC3CtK,IAAA,WAAQ4H,KAAK,CAAC,SAAS,CAAA0C,QAAA,CAAC,cAAY,CAAQ,CAAC,EACvC,CAAC,EACN,CAAC,cAENtK,IAAA,QAAKqK,SAAS,CAAC,cAAc,CAAAC,QAAA,cAC3BpK,KAAA,UAAOmK,SAAS,CAAC,cAAc,CAAAC,QAAA,eAC7BtK,IAAA,UACEkC,IAAI,CAAC,UAAU,CACfiJ,OAAO,CAAE7I,kBAAmB,CAC5B2I,QAAQ,CAAG1C,CAAC,EAAKhG,qBAAqB,CAACgG,CAAC,CAACC,MAAM,CAAC2C,OAAO,CAAE,CACzDd,SAAS,CAAC,iBAAiB,CAC5B,CAAC,uBAEJ,EAAO,CAAC,CACL,CAAC,EACH,CAAC,cAENnK,KAAA,QAAKmK,SAAS,CAAC,aAAa,CAAAC,QAAA,eAC1BpK,KAAA,QAAKmK,SAAS,CAAC,cAAc,CAAAC,QAAA,eAC3BtK,IAAA,UAAOqK,SAAS,CAAC,cAAc,CAAAC,QAAA,CAAC,QAAM,CAAO,CAAC,cAC9CtK,IAAA,UACEgD,EAAE,CAAC,cAAc,CACjBd,IAAI,CAAC,MAAM,CACX0F,KAAK,CAAElG,OAAO,CAACO,WAAY,CAC3BgJ,QAAQ,CAAG1C,CAAC,EAAKb,kBAAkB,CAAC,aAAa,CAAEa,CAAC,CAACC,MAAM,CAACZ,KAAK,CAAE,CACnEwD,WAAW,CAAC,oCAAoC,CAChDf,SAAS,CAAC,cAAc,CACzB,CAAC,EACC,CAAC,cAENnK,KAAA,QAAKmK,SAAS,CAAC,cAAc,CAAAC,QAAA,eAC3BtK,IAAA,UAAOqK,SAAS,CAAC,cAAc,CAAAC,QAAA,CAAC,WAAS,CAAO,CAAC,cACjDtK,IAAA,UACEkC,IAAI,CAAC,MAAM,CACX0F,KAAK,CAAElG,OAAO,CAACG,QAAS,CACxBoJ,QAAQ,CAAG1C,CAAC,EAAKb,kBAAkB,CAAC,UAAU,CAAEa,CAAC,CAACC,MAAM,CAACZ,KAAK,CAAE,CAChEyC,SAAS,CAAC,cAAc,CACzB,CAAC,EACC,CAAC,cAENnK,KAAA,QAAKmK,SAAS,CAAC,cAAc,CAAAC,QAAA,eAC3BtK,IAAA,UAAOqK,SAAS,CAAC,cAAc,CAAAC,QAAA,CAAC,SAAO,CAAO,CAAC,cAC/CtK,IAAA,UACEkC,IAAI,CAAC,MAAM,CACX0F,KAAK,CAAElG,OAAO,CAACI,MAAO,CACtBmJ,QAAQ,CAAG1C,CAAC,EAAKb,kBAAkB,CAAC,QAAQ,CAAEa,CAAC,CAACC,MAAM,CAACZ,KAAK,CAAE,CAC9DyC,SAAS,CAAC,cAAc,CACzB,CAAC,EACC,CAAC,cAENnK,KAAA,QAAKmK,SAAS,CAAC,cAAc,CAAAC,QAAA,eAC3BtK,IAAA,UAAOqK,SAAS,CAAC,cAAc,CAAAC,QAAA,CAAC,cAAY,CAAO,CAAC,cACpDpK,KAAA,QAAKmK,SAAS,CAAC,cAAc,CAAAC,QAAA,eAC3BtK,IAAA,UACEkC,IAAI,CAAC,QAAQ,CACb0F,KAAK,CAAElG,OAAO,CAACK,UAAW,CAC1BkJ,QAAQ,CAAG1C,CAAC,EAAKb,kBAAkB,CAAC,YAAY,CAAEa,CAAC,CAACC,MAAM,CAACZ,KAAK,CAAE,CAClEwD,WAAW,CAAC,KAAK,CACjBf,SAAS,CAAC,2BAA2B,CACrCgB,IAAI,CAAC,MAAM,CACZ,CAAC,cACFrL,IAAA,SAAMqK,SAAS,CAAC,kBAAkB,CAAAC,QAAA,CAAC,GAAC,CAAM,CAAC,cAC3CtK,IAAA,UACEkC,IAAI,CAAC,QAAQ,CACb0F,KAAK,CAAElG,OAAO,CAACM,QAAS,CACxBiJ,QAAQ,CAAG1C,CAAC,EAAKb,kBAAkB,CAAC,UAAU,CAAEa,CAAC,CAACC,MAAM,CAACZ,KAAK,CAAE,CAChEwD,WAAW,CAAC,KAAK,CACjBf,SAAS,CAAC,2BAA2B,CACrCgB,IAAI,CAAC,MAAM,CACZ,CAAC,EACC,CAAC,EACH,CAAC,cAENrL,IAAA,QAAKqK,SAAS,CAAC,cAAc,CAAAC,QAAA,cAC3BtK,IAAA,WAAQuK,OAAO,CAAElC,YAAa,CAACgC,SAAS,CAAC,0BAA0B,CAAAC,QAAA,CAAC,eAEpE,CAAQ,CAAC,CACN,CAAC,EACH,CAAC,EACH,CAAC,cAGNpK,KAAA,QAAKmK,SAAS,CAAC,8BAA8B,CAAAC,QAAA,eAC3CpK,KAAA,UAAOmK,SAAS,CAAC,oBAAoB,CAAAC,QAAA,eACnCtK,IAAA,UAAAsK,QAAA,cACEpK,KAAA,OAAAoK,QAAA,eACEtK,IAAA,OAAIqK,SAAS,CAAC,cAAc,CAAAC,QAAA,cAC1BtK,IAAA,UACEkC,IAAI,CAAC,UAAU,CACfiJ,OAAO,CAAEhJ,oBAAoB,CAACiG,IAAI,GAAKd,mBAAmB,CAAC7D,MAAM,EAAI6D,mBAAmB,CAAC7D,MAAM,CAAG,CAAE,CACpGwH,QAAQ,CAAE9C,eAAgB,CAC1BkC,SAAS,CAAC,gBAAgB,CAC3B,CAAC,CACA,CAAC,cACLnK,KAAA,OACEmK,SAAS,aAAAiB,MAAA,CAAchK,SAAS,GAAK,cAAc,CAAG,QAAQ,CAAG,EAAE,CAAG,CACtEiJ,OAAO,CAAEA,CAAA,GAAM/C,UAAU,CAAC,cAAc,CAAE,CAAA8C,QAAA,EAC3C,MAEC,CAAChJ,SAAS,GAAK,cAAc,eAC3BtB,IAAA,SAAMqK,SAAS,CAAC,YAAY,CAAAC,QAAA,CACzB9I,aAAa,GAAK,KAAK,CAAG,GAAG,CAAG,GAAG,CAChC,CACP,EACC,CAAC,cACLtB,KAAA,OACEmK,SAAS,aAAAiB,MAAA,CAAchK,SAAS,GAAK,aAAa,CAAG,QAAQ,CAAG,EAAE,CAAG,CACrEiJ,OAAO,CAAEA,CAAA,GAAM/C,UAAU,CAAC,aAAa,CAAE,CAAA8C,QAAA,EAC1C,SAEC,CAAChJ,SAAS,GAAK,aAAa,eAC1BtB,IAAA,SAAMqK,SAAS,CAAC,YAAY,CAAAC,QAAA,CACzB9I,aAAa,GAAK,KAAK,CAAG,GAAG,CAAG,GAAG,CAChC,CACP,EACC,CAAC,cACLtB,KAAA,OACEmK,SAAS,aAAAiB,MAAA,CAAchK,SAAS,GAAK,aAAa,CAAG,QAAQ,CAAG,EAAE,CAAG,CACrEiJ,OAAO,CAAEA,CAAA,GAAM/C,UAAU,CAAC,aAAa,CAAE,CAAA8C,QAAA,EAC1C,aAEC,CAAChJ,SAAS,GAAK,aAAa,eAC1BtB,IAAA,SAAMqK,SAAS,CAAC,YAAY,CAAAC,QAAA,CACzB9I,aAAa,GAAK,KAAK,CAAG,GAAG,CAAG,GAAG,CAChC,CACP,EACC,CAAC,cACLxB,IAAA,OAAIqK,SAAS,CAAC,YAAY,CAAAC,QAAA,CAAC,OAAK,CAAI,CAAC,cACrCtK,IAAA,OAAIqK,SAAS,CAAC,YAAY,CAAAC,QAAA,CAAC,QAAM,CAAI,CAAC,cACtCpK,KAAA,OACEmK,SAAS,wBAAAiB,MAAA,CAAyBhK,SAAS,GAAK,SAAS,CAAG,QAAQ,CAAG,EAAE,CAAG,CAC5EiJ,OAAO,CAAEA,CAAA,GAAM/C,UAAU,CAAC,SAAS,CAAE,CAAA8C,QAAA,EACtC,SAEC,CAAChJ,SAAS,GAAK,SAAS,eACtBtB,IAAA,SAAMqK,SAAS,CAAC,YAAY,CAAAC,QAAA,CACzB9I,aAAa,GAAK,KAAK,CAAG,GAAG,CAAG,GAAG,CAChC,CACP,EACC,CAAC,cACLxB,IAAA,OAAAsK,QAAA,CAAI,WAAS,CAAI,CAAC,EAChB,CAAC,CACA,CAAC,cACRtK,IAAA,UAAAsK,QAAA,CACGhD,mBAAmB,CAAChB,GAAG,CAAE6D,WAAW,eACnCjK,KAAA,OAEEmK,SAAS,wBAAAiB,MAAA,CACLnJ,oBAAoB,CAACuB,GAAG,CAACyG,WAAW,CAACnH,EAAE,CAAC,CAAG,UAAU,CAAG,EAAE,yBAAAsI,MAAA,CAC1DpB,WAAW,CAACC,WAAW,CAAC,CAAG,WAAW,CAAG,EAAE,sBAC7C,CAAAG,QAAA,eAEFtK,IAAA,OAAIqK,SAAS,CAAC,cAAc,CAAAC,QAAA,cAC1BtK,IAAA,UACEkC,IAAI,CAAC,UAAU,CACfiJ,OAAO,CAAEhJ,oBAAoB,CAACuB,GAAG,CAACyG,WAAW,CAACnH,EAAE,CAAE,CAClDiI,QAAQ,CAAEA,CAAA,GAAMlD,uBAAuB,CAACoC,WAAW,CAACnH,EAAE,CAAE,CACxDqH,SAAS,CAAC,gBAAgB,CAC3B,CAAC,CACA,CAAC,cACLrK,IAAA,OAAIqK,SAAS,CAAC,UAAU,CAAAC,QAAA,cACtBpK,KAAA,QAAKmK,SAAS,CAAC,cAAc,CAAAC,QAAA,EAC1BZ,UAAU,CAACS,WAAW,CAAChG,YAAY,CAAC,CACpCgG,WAAW,CAACoB,IAAI,eACfvL,IAAA,SAAMqK,SAAS,CAAC,cAAc,CAAAC,QAAA,CAAEH,WAAW,CAACoB,IAAI,CAAO,CACxD,EACE,CAAC,CACJ,CAAC,cACLvL,IAAA,OAAIqK,SAAS,CAAC,aAAa,CAAAC,QAAA,cACzBpK,KAAA,QAAKmK,SAAS,CAAC,cAAc,CAAAC,QAAA,EAC1BL,cAAc,CAACE,WAAW,CAACvI,SAAS,CAAC,CACrCsI,WAAW,CAACC,WAAW,CAAC,eACvBnK,IAAA,SAAMqK,SAAS,CAAC,iBAAiB,CAAAC,QAAA,CAAC,WAAS,CAAM,CAClD,EACE,CAAC,CACJ,CAAC,cACLtK,IAAA,OAAIqK,SAAS,CAAC,iBAAiB,CAAAC,QAAA,cAC7BtK,IAAA,QAAKqK,SAAS,CAAC,qBAAqB,CAAAC,QAAA,CACjCH,WAAW,CAAClI,WAAW,CACrB,CAAC,CACJ,CAAC,cACLjC,IAAA,OAAIqK,SAAS,CAAC,kBAAkB,CAAAC,QAAA,CAC7BH,WAAW,CAACzF,WAAW,CAAG0E,cAAc,CAACe,WAAW,CAACzF,WAAW,CAAC,CAAG,EAAE,CACrE,CAAC,cACL1E,IAAA,OAAIqK,SAAS,CAAC,mBAAmB,CAAAC,QAAA,CAC9BH,WAAW,CAACvF,YAAY,CAAGwE,cAAc,CAACe,WAAW,CAACvF,YAAY,CAAC,CAAG,EAAE,CACvE,CAAC,cACL5E,IAAA,OAAIqK,SAAS,CAAC,oBAAoB,CAAAC,QAAA,CAC/BlB,cAAc,CAACe,WAAW,CAACvD,OAAO,CAAC,CAClC,CAAC,cACL5G,IAAA,OAAIqK,SAAS,CAAC,eAAe,CAAAC,QAAA,CAC1BH,WAAW,CAACpE,SAAS,EAAI,EAAE,CAC1B,CAAC,GA9CAoE,WAAW,CAACnH,EA+Cf,CACL,CAAC,CACG,CAAC,EACH,CAAC,CAEPsE,mBAAmB,CAAC7D,MAAM,GAAK,CAAC,eAC/BvD,KAAA,QAAKmK,SAAS,CAAC,iBAAiB,CAAAC,QAAA,eAC9BtK,IAAA,QAAKqK,SAAS,CAAC,sBAAsB,CAAAC,QAAA,CAAC,cAAE,CAAK,CAAC,cAC9CtK,IAAA,OAAAsK,QAAA,CAAI,uBAAqB,CAAI,CAAC,cAC9BtK,IAAA,MAAAsK,QAAA,CACG9E,6BAA6B,CAAC/B,MAAM,GAAK,CAAC,EAAIjD,YAAY,CAACiD,MAAM,GAAK,CAAC,CACpE,yCAAyC,CACzC,6CAA6C,CAChD,CAAC,CACH+B,6BAA6B,CAAC/B,MAAM,GAAK,CAAC,EAAIjD,YAAY,CAACiD,MAAM,CAAG,CAAC,eACpEzD,IAAA,WAAQuK,OAAO,CAAElC,YAAa,CAACgC,SAAS,CAAC,iBAAiB,CAAAC,QAAA,CAAC,eAE3D,CAAQ,CACT,EACE,CACN,EACE,CAAC,CAGLpD,UAAU,CAAG,CAAC,eACbhH,KAAA,QAAKmK,SAAS,CAAC,yBAAyB,CAAAC,QAAA,eACtCpK,KAAA,QAAKmK,SAAS,CAAC,iBAAiB,CAAAC,QAAA,eAC9BpK,KAAA,SAAAoK,QAAA,EAAM,UACI,CAAClD,UAAU,CAAG,CAAC,CAAC,GAAC,CAAC9C,IAAI,CAACkH,GAAG,CAACnE,QAAQ,CAAE7B,6BAA6B,CAAC/B,MAAM,CAAC,CAAC,MAAI,CAAC+B,6BAA6B,CAAC/B,MAAM,CAAC,eAC/H,EAAM,CAAC,cACPvD,KAAA,QAAKmK,SAAS,CAAC,gBAAgB,CAAAC,QAAA,eAC7BtK,IAAA,UAAAsK,QAAA,CAAO,iBAAe,CAAO,CAAC,cAC9BtK,IAAA,WACE4H,KAAK,CAAExG,YAAa,CACpB6J,QAAQ,CAAG1C,CAAC,EAAKlH,eAAe,CAACoK,MAAM,CAAClD,CAAC,CAACC,MAAM,CAACZ,KAAK,CAAC,CAAE,CACzDyC,SAAS,CAAC,kBAAkB,CAAAC,QAAA,CAE3BnK,sBAAsB,CAACmG,GAAG,CAAC8B,IAAI,eAC9BpI,IAAA,WAAmB4H,KAAK,CAAEQ,IAAK,CAAAkC,QAAA,CAAElC,IAAI,EAAxBA,IAAiC,CAC/C,CAAC,CACI,CAAC,EACN,CAAC,EACH,CAAC,cACNlI,KAAA,QAAKmK,SAAS,CAAC,qBAAqB,CAAAC,QAAA,eAClCtK,IAAA,WACEuK,OAAO,CAAEA,CAAA,GAAMpJ,cAAc,CAAC,CAAC,CAAE,CACjCuK,QAAQ,CAAExK,WAAW,GAAK,CAAE,CAC5BmJ,SAAS,CAAC,0BAA0B,CAAAC,QAAA,CACrC,OAED,CAAQ,CAAC,cACTtK,IAAA,WACEuK,OAAO,CAAEA,CAAA,GAAMpJ,cAAc,CAACD,WAAW,CAAG,CAAC,CAAE,CAC/CwK,QAAQ,CAAExK,WAAW,GAAK,CAAE,CAC5BmJ,SAAS,CAAC,0BAA0B,CAAAC,QAAA,CACrC,UAED,CAAQ,CAAC,cACTtK,IAAA,QAAKqK,SAAS,CAAC,kBAAkB,CAAAC,QAAA,CAC9BqB,KAAK,CAACC,IAAI,CAAC,CAAEnI,MAAM,CAAEa,IAAI,CAACkH,GAAG,CAAC,CAAC,CAAEtE,UAAU,CAAE,CAAC,CAAE,CAAC2E,CAAC,CAAErI,CAAC,GAAK,CACzD,KAAM,CAAAsI,UAAU,CAAGxH,IAAI,CAACyH,GAAG,CAAC,CAAC,CAAEzH,IAAI,CAACkH,GAAG,CAACtE,UAAU,CAAG,CAAC,CAAEhG,WAAW,CAAG,CAAC,CAAC,CAAC,CAAGsC,CAAC,CAC7E,mBACExD,IAAA,WAEEuK,OAAO,CAAEA,CAAA,GAAMpJ,cAAc,CAAC2K,UAAU,CAAE,CAC1CzB,SAAS,6BAAAiB,MAAA,CAA8BpK,WAAW,GAAK4K,UAAU,CAAG,QAAQ,CAAG,EAAE,CAAG,CAAAxB,QAAA,CAEnFwB,UAAU,EAJNA,UAKC,CAAC,CAEb,CAAC,CAAC,CACC,CAAC,cACN9L,IAAA,WACEuK,OAAO,CAAEA,CAAA,GAAMpJ,cAAc,CAACD,WAAW,CAAG,CAAC,CAAE,CAC/CwK,QAAQ,CAAExK,WAAW,GAAKgG,UAAW,CACrCmD,SAAS,CAAC,0BAA0B,CAAAC,QAAA,CACrC,MAED,CAAQ,CAAC,cACTtK,IAAA,WACEuK,OAAO,CAAEA,CAAA,GAAMpJ,cAAc,CAAC+F,UAAU,CAAE,CAC1CwE,QAAQ,CAAExK,WAAW,GAAKgG,UAAW,CACrCmD,SAAS,CAAC,0BAA0B,CAAAC,QAAA,CACrC,MAED,CAAQ,CAAC,EACN,CAAC,EACH,CACN,cAGDtK,IAAA,QAAKqK,SAAS,CAAC,oBAAoB,CAAAC,QAAA,cACjCpK,KAAA,YAAAoK,QAAA,eACEtK,IAAA,YAAAsK,QAAA,CAAS,oBAAkB,CAAS,CAAC,cACrCpK,KAAA,QAAKmK,SAAS,CAAC,gBAAgB,CAAAC,QAAA,eAC7BpK,KAAA,QAAKmK,SAAS,CAAC,eAAe,CAAAC,QAAA,eAC5BtK,IAAA,QAAAsK,QAAA,CAAK,QAAC,CAAK,CAAC,IAAC,cAAAtK,IAAA,SAAAsK,QAAA,CAAM,eAAa,CAAM,CAAC,EACpC,CAAC,cACNpK,KAAA,QAAKmK,SAAS,CAAC,eAAe,CAAAC,QAAA,eAC5BtK,IAAA,QAAAsK,QAAA,CAAK,QAAC,CAAK,CAAC,IAAC,cAAAtK,IAAA,SAAAsK,QAAA,CAAM,WAAS,CAAM,CAAC,EAChC,CAAC,cACNpK,KAAA,QAAKmK,SAAS,CAAC,eAAe,CAAAC,QAAA,eAC5BtK,IAAA,QAAAsK,QAAA,CAAK,MAAI,CAAK,CAAC,IAAC,cAAAtK,IAAA,SAAAsK,QAAA,CAAM,YAAU,CAAM,CAAC,EACpC,CAAC,cACNpK,KAAA,QAAKmK,SAAS,CAAC,eAAe,CAAAC,QAAA,eAC5BtK,IAAA,QAAAsK,QAAA,CAAK,KAAG,CAAK,CAAC,IAAC,cAAAtK,IAAA,SAAAsK,QAAA,CAAM,WAAS,CAAM,CAAC,EAClC,CAAC,cACNpK,KAAA,QAAKmK,SAAS,CAAC,eAAe,CAAAC,QAAA,eAC5BtK,IAAA,QAAAsK,QAAA,CAAK,QAAM,CAAK,CAAC,IAAC,cAAAtK,IAAA,SAAAsK,QAAA,CAAM,cAAY,CAAM,CAAC,EACxC,CAAC,cACNpK,KAAA,QAAKmK,SAAS,CAAC,eAAe,CAAAC,QAAA,eAC5BtK,IAAA,QAAAsK,QAAA,CAAK,QAAM,CAAK,CAAC,IAAC,cAAAtK,IAAA,SAAAsK,QAAA,CAAM,SAAO,CAAM,CAAC,EACnC,CAAC,cACNpK,KAAA,QAAKmK,SAAS,CAAC,eAAe,CAAAC,QAAA,eAC5BtK,IAAA,QAAAsK,QAAA,CAAK,QAAM,CAAK,CAAC,IAAC,cAAAtK,IAAA,SAAAsK,QAAA,CAAM,YAAU,CAAM,CAAC,EACtC,CAAC,cACNpK,KAAA,QAAKmK,SAAS,CAAC,eAAe,CAAAC,QAAA,eAC5BtK,IAAA,QAAAsK,QAAA,CAAK,KAAG,CAAK,CAAC,IAAC,cAAAtK,IAAA,SAAAsK,QAAA,CAAM,iBAAe,CAAM,CAAC,EACxC,CAAC,EACH,CAAC,EACC,CAAC,CACP,CAAC,EACH,CAAC,CAEV,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}