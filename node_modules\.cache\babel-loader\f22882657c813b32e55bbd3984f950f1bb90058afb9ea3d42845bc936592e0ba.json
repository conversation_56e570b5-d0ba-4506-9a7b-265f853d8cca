{"ast": null, "code": "import _objectSpread from\"C:/tmsft/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";import React,{useState,useEffect,useCallback,useMemo}from'react';import{transactionStorageService}from'../services/transactionStorageService';import{bankAccountService}from'../services/bankAccountService';import{TransactionCategorization}from'./TransactionCategorization';import'./Transactions.css';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const ITEMS_PER_PAGE_OPTIONS=[10,25,50,100];const DEFAULT_ITEMS_PER_PAGE=50;export const Transactions=_ref=>{let{onTransactionUpdate,refreshTrigger}=_ref;// Tab state\nconst[activeTab,setActiveTab]=useState('all');// State management\nconst[transactions,setTransactions]=useState([]);const[bankAccounts,setBankAccounts]=useState([]);const[duplicateGroups,setDuplicateGroups]=useState([]);const[loading,setLoading]=useState(true);const[error,setError]=useState(null);const[refreshing,setRefreshing]=useState(false);// Pagination state\nconst[currentPage,setCurrentPage]=useState(1);const[itemsPerPage,setItemsPerPage]=useState(DEFAULT_ITEMS_PER_PAGE);// Sorting state\nconst[sortField,setSortField]=useState('postDateTime');const[sortDirection,setSortDirection]=useState('desc');// Filtering state\nconst[filters,setFilters]=useState({accountId:'',dateFrom:'',dateTo:'',amountFrom:'',amountTo:'',description:'',type:'all'});// Selection state\nconst[selectedTransactions,setSelectedTransactions]=useState(new Set());const[showDuplicatesOnly,setShowDuplicatesOnly]=useState(false);// Calculate string similarity\n/* Temporarily commented out for debugging pagination\n  const calculateStringSimilarity = useCallback((str1: string, str2: string): number => {\n    const longer = str1.length > str2.length ? str1 : str2;\n    const shorter = str1.length > str2.length ? str2 : str1;\n    \n    if (longer.length === 0) return 1.0;\n    \n    // Simple similarity based on common characters\n    const s1 = str1.toLowerCase();\n    const s2 = str2.toLowerCase();\n    \n    let matches = 0;\n    for (let i = 0; i < shorter.length; i++) {\n      if (s1.includes(s2[i])) matches++;\n    }\n    \n    return matches / longer.length;\n  }, []);\n  */// Check if two transactions are potential duplicates\n/* Temporarily commented out for debugging pagination\n  const arePotentialDuplicates = useCallback((t1: StoredTransaction, t2: StoredTransaction): boolean => {\n    // Same account\n    if (t1.accountId !== t2.accountId) return false;\n\n    // Same date (within 1 day)\n    const date1 = new Date(t1.postDateTime);\n    const date2 = new Date(t2.postDateTime);\n    const daysDiff = Math.abs(date1.getTime() - date2.getTime()) / (1000 * 60 * 60 * 24);\n    if (daysDiff > 1) return false;\n\n    // Same amounts\n    const sameDebit = Math.abs((t1.debitAmount || 0) - (t2.debitAmount || 0)) < 0.01;\n    const sameCredit = Math.abs((t1.creditAmount || 0) - (t2.creditAmount || 0)) < 0.01;\n    if (!sameDebit || !sameCredit) return false;\n\n    // Similar description (at least 80% similarity)\n    const similarity = calculateStringSimilarity(t1.description, t2.description);\n    if (similarity < 0.8) return false;\n\n    return true;\n  }, [calculateStringSimilarity]);\n  */// Simple duplicate detection within a single set of transactions\nconst findDuplicatesInTransactions=useCallback(_transactions=>{// Temporarily disable complex duplicate detection to debug pagination\nreturn[];/* Original code commented out for debugging\n    const duplicateGroups: StoredTransaction[][] = [];\n    const processed = new Set<string>();\n\n    for (let i = 0; i < transactions.length; i++) {\n      if (processed.has(transactions[i].id)) continue;\n\n      const group: StoredTransaction[] = [transactions[i]];\n      processed.add(transactions[i].id);\n\n      for (let j = i + 1; j < transactions.length; j++) {\n        if (processed.has(transactions[j].id)) continue;\n\n        // Check if transactions are potential duplicates\n        if (arePotentialDuplicates(transactions[i], transactions[j])) {\n          group.push(transactions[j]);\n          processed.add(transactions[j].id);\n        }\n      }\n\n      // Only consider groups with 2 or more transactions as duplicates\n      if (group.length > 1) {\n        duplicateGroups.push(group);\n      }\n    }\n\n    return duplicateGroups;\n    */},[]);// Empty dependency array since function currently doesn't use any dependencies\n// Load data on component mount\nconst loadData=useCallback(async()=>{try{// Show refreshing indicator if not initial load\nif(transactions.length>0){setRefreshing(true);}else{setLoading(true);}setError(null);// Load bank accounts\nconst accounts=bankAccountService.getAllAccounts();setBankAccounts(accounts);// Load all transactions\nconst allTransactions=[];accounts.forEach(account=>{const accountTransactions=transactionStorageService.getTransactionsByAccount(account.id);allTransactions.push(...accountTransactions);});setTransactions(allTransactions);// Detect duplicates within the transaction set\nconst duplicates=findDuplicatesInTransactions(allTransactions);setDuplicateGroups(duplicates);if(onTransactionUpdate){onTransactionUpdate(allTransactions);}}catch(err){setError(err instanceof Error?err.message:'Failed to load transactions');}finally{setLoading(false);setRefreshing(false);}},[onTransactionUpdate,findDuplicatesInTransactions]);useEffect(()=>{loadData();},[loadData,refreshTrigger]);// Filtered and sorted transactions\nconst filteredAndSortedTransactions=useMemo(()=>{// Early return if transactions not loaded yet\nif(!transactions||transactions.length===0){return[];}console.log('FILTERING DEBUG: Starting with',transactions.length,'transactions');let filtered=[...transactions];// Apply filters\nif(filters.accountId){filtered=filtered.filter(t=>t.accountId===filters.accountId);console.log('FILTERING DEBUG: After accountId filter:',filtered.length);}if(filters.dateFrom){const fromDate=new Date(filters.dateFrom);filtered=filtered.filter(t=>new Date(t.postDateTime)>=fromDate);console.log('FILTERING DEBUG: After dateFrom filter:',filtered.length);}if(filters.dateTo){const toDate=new Date(filters.dateTo);filtered=filtered.filter(t=>new Date(t.postDateTime)<=toDate);console.log('FILTERING DEBUG: After dateTo filter:',filtered.length);}if(filters.description){const searchTerm=filters.description.toLowerCase();filtered=filtered.filter(t=>t.description.toLowerCase().includes(searchTerm)||t.reference&&t.reference.toLowerCase().includes(searchTerm));console.log('FILTERING DEBUG: After description filter:',filtered.length);}if(filters.amountFrom){const minAmount=parseFloat(filters.amountFrom);if(!isNaN(minAmount)){filtered=filtered.filter(t=>{const amount=Math.abs((t.debitAmount||0)+(t.creditAmount||0));return amount>=minAmount;});console.log('FILTERING DEBUG: After amountFrom filter:',filtered.length);}}if(filters.amountTo){const maxAmount=parseFloat(filters.amountTo);if(!isNaN(maxAmount)){filtered=filtered.filter(t=>{const amount=Math.abs((t.debitAmount||0)+(t.creditAmount||0));return amount<=maxAmount;});console.log('FILTERING DEBUG: After amountTo filter:',filtered.length);}}if(filters.type==='debits'){filtered=filtered.filter(t=>(t.debitAmount||0)>0);console.log('FILTERING DEBUG: After debits filter:',filtered.length);}else if(filters.type==='credits'){filtered=filtered.filter(t=>(t.creditAmount||0)>0);console.log('FILTERING DEBUG: After credits filter:',filtered.length);}// Show duplicates only\nif(showDuplicatesOnly&&duplicateGroups.length>0){const duplicateIds=new Set(duplicateGroups.flat().map(t=>t.id));filtered=filtered.filter(t=>duplicateIds.has(t.id));console.log('FILTERING DEBUG: After duplicates filter:',filtered.length);}// Sort transactions\nif(bankAccounts.length>0){filtered.sort((a,b)=>{let aValue;let bValue;switch(sortField){case'postDateTime':aValue=new Date(a.postDateTime).getTime();bValue=new Date(b.postDateTime).getTime();break;case'description':aValue=a.description.toLowerCase();bValue=b.description.toLowerCase();break;case'amount':aValue=Math.abs((a.debitAmount||0)+(a.creditAmount||0));bValue=Math.abs((b.debitAmount||0)+(b.creditAmount||0));break;case'balance':aValue=a.balance;bValue=b.balance;break;case'accountName':const accountA=bankAccounts.find(acc=>acc.id===a.accountId);const accountB=bankAccounts.find(acc=>acc.id===b.accountId);aValue=(accountA===null||accountA===void 0?void 0:accountA.name.toLowerCase())||'';bValue=(accountB===null||accountB===void 0?void 0:accountB.name.toLowerCase())||'';break;default:return 0;}if(sortDirection==='asc'){return aValue<bValue?-1:aValue>bValue?1:0;}else{return aValue>bValue?-1:aValue<bValue?1:0;}});}console.log('FILTERING DEBUG: Final filtered result:',filtered.length);return filtered;},[transactions,filters,sortField,sortDirection,showDuplicatesOnly,duplicateGroups,bankAccounts]);// Pagination calculations\nconst totalTransactions=filteredAndSortedTransactions.length;const totalPages=Math.ceil(totalTransactions/itemsPerPage);const startIndex=(currentPage-1)*itemsPerPage;const endIndex=Math.min(startIndex+itemsPerPage,totalTransactions);const currentTransactions=filteredAndSortedTransactions.slice(startIndex,endIndex);// Debug pagination issue\nconsole.log('PAGINATION DEBUG:',{currentPage,itemsPerPage,totalTransactions,totalPages,startIndex,endIndex,sliceLength:currentTransactions.length,expectedLength:Math.min(itemsPerPage,totalTransactions-startIndex),filteredAndSortedLength:filteredAndSortedTransactions.length,transactionsLength:transactions.length,actualSlice:currentTransactions.slice(0,3).map(t=>({id:t.id,desc:t.description.substring(0,20)}))});// Check DOM rendering\nReact.useEffect(()=>{const tableRows=document.querySelectorAll('.transactions-table tbody tr');console.log('DOM ROWS COUNT:',tableRows.length,'Expected:',currentTransactions.length);},[currentTransactions]);// Reset to first page when filters change\nuseEffect(()=>{setCurrentPage(1);},[filters,sortField,sortDirection,showDuplicatesOnly,itemsPerPage]);// Event handlers\nconst handleSort=field=>{if(sortField===field){setSortDirection(sortDirection==='asc'?'desc':'asc');}else{setSortField(field);setSortDirection('desc');}};const handleFilterChange=(key,value)=>{setFilters(prev=>_objectSpread(_objectSpread({},prev),{},{[key]:value}));};const handleSelectTransaction=transactionId=>{setSelectedTransactions(prev=>{const newSet=new Set(prev);if(newSet.has(transactionId)){newSet.delete(transactionId);}else{newSet.add(transactionId);}return newSet;});};const handleSelectAll=useCallback(()=>{if(selectedTransactions.size===currentTransactions.length){setSelectedTransactions(new Set());}else{setSelectedTransactions(new Set(currentTransactions.map(t=>t.id)));}},[selectedTransactions.size,currentTransactions]);const clearFilters=()=>{setFilters({accountId:'',dateFrom:'',dateTo:'',amountFrom:'',amountTo:'',description:'',type:'all'});setShowDuplicatesOnly(false);};// Keyboard navigation\nuseEffect(()=>{const handleKeyPress=e=>{if(e.target instanceof HTMLInputElement||e.target instanceof HTMLTextAreaElement){return;// Don't interfere with input fields\n}switch(e.key){case'ArrowLeft':if(currentPage>1){setCurrentPage(currentPage-1);}break;case'ArrowRight':if(currentPage<totalPages){setCurrentPage(currentPage+1);}break;case'Home':setCurrentPage(1);break;case'End':setCurrentPage(totalPages);break;case'f':if(e.ctrlKey||e.metaKey){var _document$getElementB;e.preventDefault();(_document$getElementB=document.getElementById('search-input'))===null||_document$getElementB===void 0?void 0:_document$getElementB.focus();}break;case'r':if(e.ctrlKey||e.metaKey){e.preventDefault();loadData();}break;case'a':if(e.ctrlKey||e.metaKey){e.preventDefault();handleSelectAll();}break;case'Escape':setSelectedTransactions(new Set());break;}};document.addEventListener('keydown',handleKeyPress);return()=>document.removeEventListener('keydown',handleKeyPress);},[currentPage,totalPages,loadData,handleSelectAll]);// Utility functions\nconst formatCurrency=amount=>{return new Intl.NumberFormat('en-US',{minimumFractionDigits:2,maximumFractionDigits:2}).format(amount);};const formatDate=dateString=>{const date=new Date(dateString);return date.toLocaleDateString('en-GB',{day:'2-digit',month:'2-digit',year:'numeric'});};const getAccountName=accountId=>{const account=bankAccounts.find(acc=>acc.id===accountId);return(account===null||account===void 0?void 0:account.name)||'Unknown Account';};const isDuplicate=transaction=>{return duplicateGroups.some(group=>group.some(t=>t.id===transaction.id));};if(loading){return/*#__PURE__*/_jsxs(\"div\",{className:\"transactions-loading\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"loading-spinner\"}),/*#__PURE__*/_jsx(\"p\",{children:\"Loading transactions...\"})]});}if(error){return/*#__PURE__*/_jsxs(\"div\",{className:\"transactions-error\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"error-icon\",children:\"\\u26A0\\uFE0F\"}),/*#__PURE__*/_jsx(\"h3\",{children:\"Error Loading Transactions\"}),/*#__PURE__*/_jsx(\"p\",{children:error}),/*#__PURE__*/_jsx(\"button\",{onClick:loadData,className:\"btn btn-primary\",children:\"Try Again\"})]});}const renderTabContent=()=>{switch(activeTab){case'categorization':return/*#__PURE__*/_jsx(TransactionCategorization,{refreshTrigger:refreshTrigger});case'all':default:return/*#__PURE__*/_jsxs(\"div\",{className:\"all-transactions-content\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"transactions-filters\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"filters-row\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"filter-group\",children:[/*#__PURE__*/_jsx(\"label\",{className:\"filter-label\",children:\"Account\"}),/*#__PURE__*/_jsxs(\"select\",{value:filters.accountId,onChange:e=>handleFilterChange('accountId',e.target.value),className:\"filter-select\",children:[/*#__PURE__*/_jsx(\"option\",{value:\"\",children:\"All Accounts\"}),bankAccounts.map(account=>/*#__PURE__*/_jsxs(\"option\",{value:account.id,children:[account.name,\" - \",account.accountNumber]},account.id))]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"filter-group\",children:[/*#__PURE__*/_jsx(\"label\",{className:\"filter-label\",children:\"Type\"}),/*#__PURE__*/_jsxs(\"select\",{value:filters.type,onChange:e=>handleFilterChange('type',e.target.value),className:\"filter-select\",children:[/*#__PURE__*/_jsx(\"option\",{value:\"all\",children:\"All Types\"}),/*#__PURE__*/_jsx(\"option\",{value:\"debits\",children:\"Debits Only\"}),/*#__PURE__*/_jsx(\"option\",{value:\"credits\",children:\"Credits Only\"})]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"filter-group\",children:/*#__PURE__*/_jsxs(\"label\",{className:\"filter-label\",children:[/*#__PURE__*/_jsx(\"input\",{type:\"checkbox\",checked:showDuplicatesOnly,onChange:e=>setShowDuplicatesOnly(e.target.checked),className:\"filter-checkbox\"}),\"Show Duplicates Only\"]})})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"filters-row\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"filter-group\",children:[/*#__PURE__*/_jsx(\"label\",{className:\"filter-label\",children:\"Search\"}),/*#__PURE__*/_jsx(\"input\",{id:\"search-input\",type:\"text\",value:filters.description,onChange:e=>handleFilterChange('description',e.target.value),placeholder:\"Search description or reference...\",className:\"filter-input\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"filter-group\",children:[/*#__PURE__*/_jsx(\"label\",{className:\"filter-label\",children:\"Date From\"}),/*#__PURE__*/_jsx(\"input\",{type:\"date\",value:filters.dateFrom,onChange:e=>handleFilterChange('dateFrom',e.target.value),className:\"filter-input\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"filter-group\",children:[/*#__PURE__*/_jsx(\"label\",{className:\"filter-label\",children:\"Date To\"}),/*#__PURE__*/_jsx(\"input\",{type:\"date\",value:filters.dateTo,onChange:e=>handleFilterChange('dateTo',e.target.value),className:\"filter-input\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"filter-group\",children:[/*#__PURE__*/_jsx(\"label\",{className:\"filter-label\",children:\"Amount Range\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"amount-range\",children:[/*#__PURE__*/_jsx(\"input\",{type:\"number\",value:filters.amountFrom,onChange:e=>handleFilterChange('amountFrom',e.target.value),placeholder:\"Min\",className:\"filter-input amount-input\",step:\"0.01\"}),/*#__PURE__*/_jsx(\"span\",{className:\"amount-separator\",children:\"-\"}),/*#__PURE__*/_jsx(\"input\",{type:\"number\",value:filters.amountTo,onChange:e=>handleFilterChange('amountTo',e.target.value),placeholder:\"Max\",className:\"filter-input amount-input\",step:\"0.01\"})]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"filter-group\",children:/*#__PURE__*/_jsx(\"button\",{onClick:clearFilters,className:\"btn btn-secondary btn-sm\",children:\"Clear Filters\"})})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"transactions-table-container\",children:[/*#__PURE__*/_jsxs(\"table\",{className:\"transactions-table\",children:[/*#__PURE__*/_jsx(\"thead\",{children:/*#__PURE__*/_jsxs(\"tr\",{children:[/*#__PURE__*/_jsx(\"th\",{className:\"checkbox-col\",children:/*#__PURE__*/_jsx(\"input\",{type:\"checkbox\",checked:selectedTransactions.size===currentTransactions.length&&currentTransactions.length>0,onChange:handleSelectAll,className:\"table-checkbox\"})}),/*#__PURE__*/_jsxs(\"th\",{className:\"sortable \".concat(sortField==='postDateTime'?'sorted':''),onClick:()=>handleSort('postDateTime'),children:[\"Date\",sortField==='postDateTime'&&/*#__PURE__*/_jsx(\"span\",{className:\"sort-arrow\",children:sortDirection==='asc'?'↑':'↓'})]}),/*#__PURE__*/_jsxs(\"th\",{className:\"sortable \".concat(sortField==='accountName'?'sorted':''),onClick:()=>handleSort('accountName'),children:[\"Account\",sortField==='accountName'&&/*#__PURE__*/_jsx(\"span\",{className:\"sort-arrow\",children:sortDirection==='asc'?'↑':'↓'})]}),/*#__PURE__*/_jsxs(\"th\",{className:\"sortable \".concat(sortField==='description'?'sorted':''),onClick:()=>handleSort('description'),children:[\"Description\",sortField==='description'&&/*#__PURE__*/_jsx(\"span\",{className:\"sort-arrow\",children:sortDirection==='asc'?'↑':'↓'})]}),/*#__PURE__*/_jsx(\"th\",{className:\"amount-col\",children:\"Debit\"}),/*#__PURE__*/_jsx(\"th\",{className:\"amount-col\",children:\"Credit\"}),/*#__PURE__*/_jsxs(\"th\",{className:\"amount-col sortable \".concat(sortField==='balance'?'sorted':''),onClick:()=>handleSort('balance'),children:[\"Balance\",sortField==='balance'&&/*#__PURE__*/_jsx(\"span\",{className:\"sort-arrow\",children:sortDirection==='asc'?'↑':'↓'})]}),/*#__PURE__*/_jsx(\"th\",{children:\"Reference\"})]})}),/*#__PURE__*/_jsx(\"tbody\",{children:currentTransactions.map((transaction,_index)=>/*#__PURE__*/_jsxs(\"tr\",{className:\"\\n                        \".concat(selectedTransactions.has(transaction.id)?'selected':'',\"\\n                        \").concat(isDuplicate(transaction)?'duplicate':'',\"\\n                      \"),title:transaction.description,children:[/*#__PURE__*/_jsx(\"td\",{className:\"checkbox-col\",children:/*#__PURE__*/_jsx(\"input\",{type:\"checkbox\",checked:selectedTransactions.has(transaction.id),onChange:()=>handleSelectTransaction(transaction.id),className:\"table-checkbox\"})}),/*#__PURE__*/_jsx(\"td\",{className:\"date-col\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"date-display\",children:[formatDate(transaction.postDateTime),transaction.time&&/*#__PURE__*/_jsx(\"span\",{className:\"time-display\",children:transaction.time})]})}),/*#__PURE__*/_jsx(\"td\",{className:\"account-col\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"account-info\",children:[getAccountName(transaction.accountId),isDuplicate(transaction)&&/*#__PURE__*/_jsx(\"span\",{className:\"duplicate-badge\",children:\"DUPLICATE\"})]})}),/*#__PURE__*/_jsx(\"td\",{className:\"description-col\",children:/*#__PURE__*/_jsx(\"div\",{className:\"description-content\",children:transaction.description})}),/*#__PURE__*/_jsx(\"td\",{className:\"amount-col debit\",children:transaction.debitAmount?formatCurrency(transaction.debitAmount):''}),/*#__PURE__*/_jsx(\"td\",{className:\"amount-col credit\",children:transaction.creditAmount?formatCurrency(transaction.creditAmount):''}),/*#__PURE__*/_jsx(\"td\",{className:\"amount-col balance\",children:formatCurrency(transaction.balance)}),/*#__PURE__*/_jsx(\"td\",{className:\"reference-col\",children:transaction.reference||''})]},transaction.id))})]}),currentTransactions.length===0&&/*#__PURE__*/_jsxs(\"div\",{className:\"no-transactions\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"no-transactions-icon\",children:\"\\uD83D\\uDCCA\"}),/*#__PURE__*/_jsx(\"h3\",{children:\"No Transactions Found\"}),/*#__PURE__*/_jsx(\"p\",{children:filteredAndSortedTransactions.length===0&&transactions.length===0?'No transactions have been imported yet.':'No transactions match your current filters.'}),filteredAndSortedTransactions.length===0&&transactions.length>0&&/*#__PURE__*/_jsx(\"button\",{onClick:clearFilters,className:\"btn btn-primary\",children:\"Clear Filters\"})]})]}),totalPages>1&&/*#__PURE__*/_jsxs(\"div\",{className:\"transactions-pagination\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"pagination-info\",children:[/*#__PURE__*/_jsxs(\"span\",{children:[\"Showing \",startIndex+1,\"-\",Math.min(endIndex,filteredAndSortedTransactions.length),\" of \",filteredAndSortedTransactions.length,\" transactions\"]}),/*#__PURE__*/_jsxs(\"div\",{className:\"items-per-page\",children:[/*#__PURE__*/_jsx(\"label\",{children:\"Items per page:\"}),/*#__PURE__*/_jsx(\"select\",{value:itemsPerPage,onChange:e=>setItemsPerPage(Number(e.target.value)),className:\"page-size-select\",children:ITEMS_PER_PAGE_OPTIONS.map(size=>/*#__PURE__*/_jsx(\"option\",{value:size,children:size},size))})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"pagination-controls\",children:[/*#__PURE__*/_jsx(\"button\",{onClick:()=>setCurrentPage(1),disabled:currentPage===1,className:\"btn btn-secondary btn-sm\",children:\"First\"}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>setCurrentPage(currentPage-1),disabled:currentPage===1,className:\"btn btn-secondary btn-sm\",children:\"Previous\"}),/*#__PURE__*/_jsx(\"div\",{className:\"pagination-pages\",children:Array.from({length:Math.min(5,totalPages)},(_,i)=>{const pageNumber=Math.max(1,Math.min(totalPages-4,currentPage-2))+i;return/*#__PURE__*/_jsx(\"button\",{onClick:()=>setCurrentPage(pageNumber),className:\"btn btn-secondary btn-sm \".concat(currentPage===pageNumber?'active':''),children:pageNumber},pageNumber);})}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>setCurrentPage(currentPage+1),disabled:currentPage===totalPages,className:\"btn btn-secondary btn-sm\",children:\"Next\"}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>setCurrentPage(totalPages),disabled:currentPage===totalPages,className:\"btn btn-secondary btn-sm\",children:\"Last\"})]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"keyboard-shortcuts\",children:/*#__PURE__*/_jsxs(\"details\",{children:[/*#__PURE__*/_jsx(\"summary\",{children:\"Keyboard Shortcuts\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"shortcuts-grid\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"shortcut-item\",children:[/*#__PURE__*/_jsx(\"kbd\",{children:\"\\u2190\"}),\" \",/*#__PURE__*/_jsx(\"span\",{children:\"Previous page\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"shortcut-item\",children:[/*#__PURE__*/_jsx(\"kbd\",{children:\"\\u2192\"}),\" \",/*#__PURE__*/_jsx(\"span\",{children:\"Next page\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"shortcut-item\",children:[/*#__PURE__*/_jsx(\"kbd\",{children:\"Home\"}),\" \",/*#__PURE__*/_jsx(\"span\",{children:\"First page\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"shortcut-item\",children:[/*#__PURE__*/_jsx(\"kbd\",{children:\"End\"}),\" \",/*#__PURE__*/_jsx(\"span\",{children:\"Last page\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"shortcut-item\",children:[/*#__PURE__*/_jsx(\"kbd\",{children:\"Ctrl+F\"}),\" \",/*#__PURE__*/_jsx(\"span\",{children:\"Focus search\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"shortcut-item\",children:[/*#__PURE__*/_jsx(\"kbd\",{children:\"Ctrl+R\"}),\" \",/*#__PURE__*/_jsx(\"span\",{children:\"Refresh\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"shortcut-item\",children:[/*#__PURE__*/_jsx(\"kbd\",{children:\"Ctrl+A\"}),\" \",/*#__PURE__*/_jsx(\"span\",{children:\"Select all\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"shortcut-item\",children:[/*#__PURE__*/_jsx(\"kbd\",{children:\"Esc\"}),\" \",/*#__PURE__*/_jsx(\"span\",{children:\"Clear selection\"})]})]})]})})]});}};return/*#__PURE__*/_jsxs(\"div\",{className:\"transactions\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"transactions-header\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"transactions-title-section\",children:[/*#__PURE__*/_jsx(\"h2\",{className:\"transactions-title\",children:\"Transactions\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"transactions-stats\",children:[/*#__PURE__*/_jsxs(\"span\",{className:\"stat-item\",children:[\"Total: \",/*#__PURE__*/_jsx(\"strong\",{children:transactions.length.toLocaleString()})]}),/*#__PURE__*/_jsxs(\"span\",{className:\"stat-item\",children:[\"Filtered: \",/*#__PURE__*/_jsx(\"strong\",{children:filteredAndSortedTransactions.length.toLocaleString()})]}),duplicateGroups.length>0&&/*#__PURE__*/_jsxs(\"span\",{className:\"stat-item duplicate-stat\",children:[\"Duplicates: \",/*#__PURE__*/_jsx(\"strong\",{children:duplicateGroups.flat().length})]})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"transactions-actions\",children:[/*#__PURE__*/_jsxs(\"button\",{onClick:loadData,className:\"btn btn-secondary btn-sm\",disabled:refreshing,children:[/*#__PURE__*/_jsxs(\"svg\",{width:\"16\",height:\"16\",viewBox:\"0 0 24 24\",fill:\"none\",stroke:\"currentColor\",strokeWidth:\"2\",children:[/*#__PURE__*/_jsx(\"polyline\",{points:\"23 4 23 10 17 10\"}),/*#__PURE__*/_jsx(\"polyline\",{points:\"1 20 1 14 7 14\"}),/*#__PURE__*/_jsx(\"path\",{d:\"M20.49 9A9 9 0 0 0 5.64 5.64L1 10m22 4l-4.64 4.36A9 9 0 0 1 3.51 15\"})]}),refreshing?'Refreshing...':'Refresh']}),refreshing&&/*#__PURE__*/_jsxs(\"span\",{className:\"refresh-indicator\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"refresh-spinner\"}),\"Data updated - refreshing...\"]}),selectedTransactions.size>0&&/*#__PURE__*/_jsxs(\"span\",{className:\"selection-count\",children:[selectedTransactions.size,\" selected\"]})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"transaction-tabs\",children:[/*#__PURE__*/_jsxs(\"button\",{className:\"tab-button \".concat(activeTab==='all'?'active':''),onClick:()=>setActiveTab('all'),children:[/*#__PURE__*/_jsxs(\"svg\",{width:\"20\",height:\"20\",viewBox:\"0 0 24 24\",fill:\"none\",stroke:\"currentColor\",strokeWidth:\"2\",children:[/*#__PURE__*/_jsx(\"path\",{d:\"M3 6h18\"}),/*#__PURE__*/_jsx(\"path\",{d:\"M3 12h18\"}),/*#__PURE__*/_jsx(\"path\",{d:\"M3 18h18\"}),/*#__PURE__*/_jsx(\"circle\",{cx:\"6\",cy:\"6\",r:\"1\"}),/*#__PURE__*/_jsx(\"circle\",{cx:\"6\",cy:\"12\",r:\"1\"}),/*#__PURE__*/_jsx(\"circle\",{cx:\"6\",cy:\"18\",r:\"1\"})]}),\"All Transactions\"]}),/*#__PURE__*/_jsxs(\"button\",{className:\"tab-button \".concat(activeTab==='categorization'?'active':''),onClick:()=>setActiveTab('categorization'),children:[/*#__PURE__*/_jsx(\"svg\",{width:\"20\",height:\"20\",viewBox:\"0 0 24 24\",fill:\"none\",stroke:\"currentColor\",strokeWidth:\"2\",children:/*#__PURE__*/_jsx(\"path\",{d:\"M22 12h-4l-3 9L9 3l-3 9H2\"})}),\"Categorization\"]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"tab-content\",children:renderTabContent()})]});};", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useCallback", "useMemo", "transactionStorageService", "bankAccountService", "TransactionCategorization", "jsx", "_jsx", "jsxs", "_jsxs", "ITEMS_PER_PAGE_OPTIONS", "DEFAULT_ITEMS_PER_PAGE", "Transactions", "_ref", "onTransactionUpdate", "refreshTrigger", "activeTab", "setActiveTab", "transactions", "setTransactions", "bankAccounts", "setBankAccounts", "duplicateGroups", "setDuplicateGroups", "loading", "setLoading", "error", "setError", "refreshing", "setRefreshing", "currentPage", "setCurrentPage", "itemsPerPage", "setItemsPerPage", "sortField", "setSortField", "sortDirection", "setSortDirection", "filters", "setFilters", "accountId", "dateFrom", "dateTo", "amountFrom", "amountTo", "description", "type", "selectedTransactions", "setSelectedTransactions", "Set", "showDuplicatesOnly", "setShowDuplicatesOnly", "findDuplicatesInTransactions", "_transactions", "loadData", "length", "accounts", "getAllAccounts", "allTransactions", "for<PERSON>ach", "account", "accountTransactions", "getTransactionsByAccount", "id", "push", "duplicates", "err", "Error", "message", "filteredAndSortedTransactions", "console", "log", "filtered", "filter", "t", "fromDate", "Date", "postDateTime", "toDate", "searchTerm", "toLowerCase", "includes", "reference", "minAmount", "parseFloat", "isNaN", "amount", "Math", "abs", "debitAmount", "creditAmount", "maxAmount", "duplicateIds", "flat", "map", "has", "sort", "a", "b", "aValue", "bValue", "getTime", "balance", "accountA", "find", "acc", "accountB", "name", "totalTransactions", "totalPages", "ceil", "startIndex", "endIndex", "min", "currentTransactions", "slice", "slice<PERSON><PERSON>th", "<PERSON><PERSON><PERSON><PERSON>", "filteredAndSortedLength", "transactionsLength", "actualSlice", "desc", "substring", "tableRows", "document", "querySelectorAll", "handleSort", "field", "handleFilterChange", "key", "value", "prev", "_objectSpread", "handleSelectTransaction", "transactionId", "newSet", "delete", "add", "handleSelectAll", "size", "clearFilters", "handleKeyPress", "e", "target", "HTMLInputElement", "HTMLTextAreaElement", "ctrl<PERSON>ey", "metaKey", "_document$getElementB", "preventDefault", "getElementById", "focus", "addEventListener", "removeEventListener", "formatCurrency", "Intl", "NumberFormat", "minimumFractionDigits", "maximumFractionDigits", "format", "formatDate", "dateString", "date", "toLocaleDateString", "day", "month", "year", "getAccountName", "isDuplicate", "transaction", "some", "group", "className", "children", "onClick", "renderTabContent", "onChange", "accountNumber", "checked", "placeholder", "step", "concat", "_index", "title", "time", "Number", "disabled", "Array", "from", "_", "i", "pageNumber", "max", "toLocaleString", "width", "height", "viewBox", "fill", "stroke", "strokeWidth", "points", "d", "cx", "cy", "r"], "sources": ["C:/tmsft/src/components/Transactions.tsx"], "sourcesContent": ["import React, { useState, useEffect, useCallback, useMemo } from 'react';\nimport { transactionStorageService, type StoredTransaction } from '../services/transactionStorageService';\nimport { bankAccountService } from '../services/bankAccountService';\nimport { BankAccount } from '../types';\nimport { TransactionCategorization } from './TransactionCategorization';\nimport './Transactions.css';\n\ninterface TransactionsProps {\n  onTransactionUpdate?: (transactions: StoredTransaction[]) => void;\n  refreshTrigger?: number;\n}\n\ntype TransactionTab = 'all' | 'categorization';\n\ntype SortField = 'postDateTime' | 'description' | 'amount' | 'balance' | 'accountName';\ntype SortDirection = 'asc' | 'desc';\ntype FilterType = 'all' | 'debits' | 'credits' | 'duplicates';\n\ninterface TransactionFilters {\n  accountId: string;\n  dateFrom: string;\n  dateTo: string;\n  amountFrom: string;\n  amountTo: string;\n  description: string;\n  type: FilterType;\n}\n\nconst ITEMS_PER_PAGE_OPTIONS = [10, 25, 50, 100];\nconst DEFAULT_ITEMS_PER_PAGE = 50;\n\nexport const Transactions: React.FC<TransactionsProps> = ({ onTransactionUpdate, refreshTrigger }) => {\n  // Tab state\n  const [activeTab, setActiveTab] = useState<TransactionTab>('all');\n  \n  // State management\n  const [transactions, setTransactions] = useState<StoredTransaction[]>([]);\n  const [bankAccounts, setBankAccounts] = useState<BankAccount[]>([]);\n  const [duplicateGroups, setDuplicateGroups] = useState<StoredTransaction[][]>([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n  const [refreshing, setRefreshing] = useState(false);\n  \n  // Pagination state\n  const [currentPage, setCurrentPage] = useState(1);\n  const [itemsPerPage, setItemsPerPage] = useState(DEFAULT_ITEMS_PER_PAGE);\n  \n  // Sorting state\n  const [sortField, setSortField] = useState<SortField>('postDateTime');\n  const [sortDirection, setSortDirection] = useState<SortDirection>('desc');\n  \n  // Filtering state\n  const [filters, setFilters] = useState<TransactionFilters>({\n    accountId: '',\n    dateFrom: '',\n    dateTo: '',\n    amountFrom: '',\n    amountTo: '',\n    description: '',\n    type: 'all'\n  });\n  \n  // Selection state\n  const [selectedTransactions, setSelectedTransactions] = useState<Set<string>>(new Set());\n  const [showDuplicatesOnly, setShowDuplicatesOnly] = useState(false);\n\n  // Calculate string similarity\n  /* Temporarily commented out for debugging pagination\n  const calculateStringSimilarity = useCallback((str1: string, str2: string): number => {\n    const longer = str1.length > str2.length ? str1 : str2;\n    const shorter = str1.length > str2.length ? str2 : str1;\n    \n    if (longer.length === 0) return 1.0;\n    \n    // Simple similarity based on common characters\n    const s1 = str1.toLowerCase();\n    const s2 = str2.toLowerCase();\n    \n    let matches = 0;\n    for (let i = 0; i < shorter.length; i++) {\n      if (s1.includes(s2[i])) matches++;\n    }\n    \n    return matches / longer.length;\n  }, []);\n  */\n\n  // Check if two transactions are potential duplicates\n  /* Temporarily commented out for debugging pagination\n  const arePotentialDuplicates = useCallback((t1: StoredTransaction, t2: StoredTransaction): boolean => {\n    // Same account\n    if (t1.accountId !== t2.accountId) return false;\n\n    // Same date (within 1 day)\n    const date1 = new Date(t1.postDateTime);\n    const date2 = new Date(t2.postDateTime);\n    const daysDiff = Math.abs(date1.getTime() - date2.getTime()) / (1000 * 60 * 60 * 24);\n    if (daysDiff > 1) return false;\n\n    // Same amounts\n    const sameDebit = Math.abs((t1.debitAmount || 0) - (t2.debitAmount || 0)) < 0.01;\n    const sameCredit = Math.abs((t1.creditAmount || 0) - (t2.creditAmount || 0)) < 0.01;\n    if (!sameDebit || !sameCredit) return false;\n\n    // Similar description (at least 80% similarity)\n    const similarity = calculateStringSimilarity(t1.description, t2.description);\n    if (similarity < 0.8) return false;\n\n    return true;\n  }, [calculateStringSimilarity]);\n  */\n\n  // Simple duplicate detection within a single set of transactions\n  const findDuplicatesInTransactions = useCallback((_transactions: StoredTransaction[]): StoredTransaction[][] => {\n    // Temporarily disable complex duplicate detection to debug pagination\n    return [];\n    \n    /* Original code commented out for debugging\n    const duplicateGroups: StoredTransaction[][] = [];\n    const processed = new Set<string>();\n\n    for (let i = 0; i < transactions.length; i++) {\n      if (processed.has(transactions[i].id)) continue;\n\n      const group: StoredTransaction[] = [transactions[i]];\n      processed.add(transactions[i].id);\n\n      for (let j = i + 1; j < transactions.length; j++) {\n        if (processed.has(transactions[j].id)) continue;\n\n        // Check if transactions are potential duplicates\n        if (arePotentialDuplicates(transactions[i], transactions[j])) {\n          group.push(transactions[j]);\n          processed.add(transactions[j].id);\n        }\n      }\n\n      // Only consider groups with 2 or more transactions as duplicates\n      if (group.length > 1) {\n        duplicateGroups.push(group);\n      }\n    }\n\n    return duplicateGroups;\n    */\n  }, []); // Empty dependency array since function currently doesn't use any dependencies\n\n  // Load data on component mount\n  const loadData = useCallback(async () => {\n    try {\n      // Show refreshing indicator if not initial load\n      if (transactions.length > 0) {\n        setRefreshing(true);\n      } else {\n        setLoading(true);\n      }\n      setError(null);\n      \n      // Load bank accounts\n      const accounts = bankAccountService.getAllAccounts();\n      setBankAccounts(accounts);\n      \n      // Load all transactions\n      const allTransactions: StoredTransaction[] = [];\n      accounts.forEach(account => {\n        const accountTransactions = transactionStorageService.getTransactionsByAccount(account.id);\n        allTransactions.push(...accountTransactions);\n      });\n      \n      setTransactions(allTransactions);\n      \n      // Detect duplicates within the transaction set\n      const duplicates = findDuplicatesInTransactions(allTransactions);\n      setDuplicateGroups(duplicates);\n      \n      if (onTransactionUpdate) {\n        onTransactionUpdate(allTransactions);\n      }\n    } catch (err) {\n      setError(err instanceof Error ? err.message : 'Failed to load transactions');\n    } finally {\n      setLoading(false);\n      setRefreshing(false);\n    }\n  }, [onTransactionUpdate, findDuplicatesInTransactions]);\n\n  useEffect(() => {\n    loadData();\n  }, [loadData, refreshTrigger]);\n\n  // Filtered and sorted transactions\n  const filteredAndSortedTransactions = useMemo(() => {\n    // Early return if transactions not loaded yet\n    if (!transactions || transactions.length === 0) {\n      return [];\n    }\n    \n    console.log('FILTERING DEBUG: Starting with', transactions.length, 'transactions');\n    let filtered = [...transactions];\n    \n    // Apply filters\n    if (filters.accountId) {\n      filtered = filtered.filter(t => t.accountId === filters.accountId);\n      console.log('FILTERING DEBUG: After accountId filter:', filtered.length);\n    }\n    \n    if (filters.dateFrom) {\n      const fromDate = new Date(filters.dateFrom);\n      filtered = filtered.filter(t => new Date(t.postDateTime) >= fromDate);\n      console.log('FILTERING DEBUG: After dateFrom filter:', filtered.length);\n    }\n    \n    if (filters.dateTo) {\n      const toDate = new Date(filters.dateTo);\n      filtered = filtered.filter(t => new Date(t.postDateTime) <= toDate);\n      console.log('FILTERING DEBUG: After dateTo filter:', filtered.length);\n    }\n    \n    if (filters.description) {\n      const searchTerm = filters.description.toLowerCase();\n      filtered = filtered.filter(t => \n        t.description.toLowerCase().includes(searchTerm) ||\n        (t.reference && t.reference.toLowerCase().includes(searchTerm))\n      );\n      console.log('FILTERING DEBUG: After description filter:', filtered.length);\n    }\n    \n    if (filters.amountFrom) {\n      const minAmount = parseFloat(filters.amountFrom);\n      if (!isNaN(minAmount)) {\n        filtered = filtered.filter(t => {\n          const amount = Math.abs((t.debitAmount || 0) + (t.creditAmount || 0));\n          return amount >= minAmount;\n        });\n        console.log('FILTERING DEBUG: After amountFrom filter:', filtered.length);\n      }\n    }\n    \n    if (filters.amountTo) {\n      const maxAmount = parseFloat(filters.amountTo);\n      if (!isNaN(maxAmount)) {\n        filtered = filtered.filter(t => {\n          const amount = Math.abs((t.debitAmount || 0) + (t.creditAmount || 0));\n          return amount <= maxAmount;\n        });\n        console.log('FILTERING DEBUG: After amountTo filter:', filtered.length);\n      }\n    }\n    \n    if (filters.type === 'debits') {\n      filtered = filtered.filter(t => (t.debitAmount || 0) > 0);\n      console.log('FILTERING DEBUG: After debits filter:', filtered.length);\n    } else if (filters.type === 'credits') {\n      filtered = filtered.filter(t => (t.creditAmount || 0) > 0);\n      console.log('FILTERING DEBUG: After credits filter:', filtered.length);\n    }\n    \n    // Show duplicates only\n    if (showDuplicatesOnly && duplicateGroups.length > 0) {\n      const duplicateIds = new Set(duplicateGroups.flat().map(t => t.id));\n      filtered = filtered.filter(t => duplicateIds.has(t.id));\n      console.log('FILTERING DEBUG: After duplicates filter:', filtered.length);\n    }\n    \n    // Sort transactions\n    if (bankAccounts.length > 0) {\n      filtered.sort((a, b) => {\n        let aValue: string | number;\n        let bValue: string | number;\n        \n        switch (sortField) {\n          case 'postDateTime':\n            aValue = new Date(a.postDateTime).getTime();\n            bValue = new Date(b.postDateTime).getTime();\n            break;\n          case 'description':\n            aValue = a.description.toLowerCase();\n            bValue = b.description.toLowerCase();\n            break;\n          case 'amount':\n            aValue = Math.abs((a.debitAmount || 0) + (a.creditAmount || 0));\n            bValue = Math.abs((b.debitAmount || 0) + (b.creditAmount || 0));\n            break;\n          case 'balance':\n            aValue = a.balance;\n            bValue = b.balance;\n            break;\n          case 'accountName':\n            const accountA = bankAccounts.find(acc => acc.id === a.accountId);\n            const accountB = bankAccounts.find(acc => acc.id === b.accountId);\n            aValue = accountA?.name.toLowerCase() || '';\n            bValue = accountB?.name.toLowerCase() || '';\n            break;\n          default:\n            return 0;\n        }\n        \n        if (sortDirection === 'asc') {\n          return aValue < bValue ? -1 : aValue > bValue ? 1 : 0;\n        } else {\n          return aValue > bValue ? -1 : aValue < bValue ? 1 : 0;\n        }\n      });\n    }\n    \n    console.log('FILTERING DEBUG: Final filtered result:', filtered.length);\n    return filtered;\n  }, [transactions, filters, sortField, sortDirection, showDuplicatesOnly, duplicateGroups, bankAccounts]);\n\n  // Pagination calculations\n  const totalTransactions = filteredAndSortedTransactions.length;\n  const totalPages = Math.ceil(totalTransactions / itemsPerPage);\n  const startIndex = (currentPage - 1) * itemsPerPage;\n  const endIndex = Math.min(startIndex + itemsPerPage, totalTransactions);\n  const currentTransactions = filteredAndSortedTransactions.slice(startIndex, endIndex);\n\n  // Debug pagination issue\n  console.log('PAGINATION DEBUG:', {\n    currentPage,\n    itemsPerPage,\n    totalTransactions,\n    totalPages,\n    startIndex,\n    endIndex,\n    sliceLength: currentTransactions.length,\n    expectedLength: Math.min(itemsPerPage, totalTransactions - startIndex),\n    filteredAndSortedLength: filteredAndSortedTransactions.length,\n    transactionsLength: transactions.length,\n    actualSlice: currentTransactions.slice(0, 3).map(t => ({ id: t.id, desc: t.description.substring(0, 20) }))\n  });\n\n  // Check DOM rendering\n  React.useEffect(() => {\n    const tableRows = document.querySelectorAll('.transactions-table tbody tr');\n    console.log('DOM ROWS COUNT:', tableRows.length, 'Expected:', currentTransactions.length);\n  }, [currentTransactions]);\n\n\n\n  // Reset to first page when filters change\n  useEffect(() => {\n    setCurrentPage(1);\n  }, [filters, sortField, sortDirection, showDuplicatesOnly, itemsPerPage]);\n\n  // Event handlers\n  const handleSort = (field: SortField) => {\n    if (sortField === field) {\n      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');\n    } else {\n      setSortField(field);\n      setSortDirection('desc');\n    }\n  };\n\n  const handleFilterChange = (key: keyof TransactionFilters, value: string) => {\n    setFilters(prev => ({ ...prev, [key]: value }));\n  };\n\n  const handleSelectTransaction = (transactionId: string) => {\n    setSelectedTransactions(prev => {\n      const newSet = new Set(prev);\n      if (newSet.has(transactionId)) {\n        newSet.delete(transactionId);\n      } else {\n        newSet.add(transactionId);\n      }\n      return newSet;\n    });\n  };\n\n  const handleSelectAll = useCallback(() => {\n    if (selectedTransactions.size === currentTransactions.length) {\n      setSelectedTransactions(new Set());\n    } else {\n      setSelectedTransactions(new Set(currentTransactions.map(t => t.id)));\n    }\n  }, [selectedTransactions.size, currentTransactions]);\n\n  const clearFilters = () => {\n    setFilters({\n      accountId: '',\n      dateFrom: '',\n      dateTo: '',\n      amountFrom: '',\n      amountTo: '',\n      description: '',\n      type: 'all'\n    });\n    setShowDuplicatesOnly(false);\n  };\n\n  // Keyboard navigation\n  useEffect(() => {\n    const handleKeyPress = (e: KeyboardEvent) => {\n      if (e.target instanceof HTMLInputElement || e.target instanceof HTMLTextAreaElement) {\n        return; // Don't interfere with input fields\n      }\n\n      switch (e.key) {\n        case 'ArrowLeft':\n          if (currentPage > 1) {\n            setCurrentPage(currentPage - 1);\n          }\n          break;\n        case 'ArrowRight':\n          if (currentPage < totalPages) {\n            setCurrentPage(currentPage + 1);\n          }\n          break;\n        case 'Home':\n          setCurrentPage(1);\n          break;\n        case 'End':\n          setCurrentPage(totalPages);\n          break;\n        case 'f':\n          if (e.ctrlKey || e.metaKey) {\n            e.preventDefault();\n            document.getElementById('search-input')?.focus();\n          }\n          break;\n        case 'r':\n          if (e.ctrlKey || e.metaKey) {\n            e.preventDefault();\n            loadData();\n          }\n          break;\n        case 'a':\n          if (e.ctrlKey || e.metaKey) {\n            e.preventDefault();\n            handleSelectAll();\n          }\n          break;\n        case 'Escape':\n          setSelectedTransactions(new Set());\n          break;\n      }\n    };\n\n    document.addEventListener('keydown', handleKeyPress);\n    return () => document.removeEventListener('keydown', handleKeyPress);\n  }, [currentPage, totalPages, loadData, handleSelectAll]);\n\n  // Utility functions\n  const formatCurrency = (amount: number): string => {\n    return new Intl.NumberFormat('en-US', {\n      minimumFractionDigits: 2,\n      maximumFractionDigits: 2\n    }).format(amount);\n  };\n\n  const formatDate = (dateString: string): string => {\n    const date = new Date(dateString);\n    return date.toLocaleDateString('en-GB', {\n      day: '2-digit',\n      month: '2-digit',\n      year: 'numeric'\n    });\n  };\n\n  const getAccountName = (accountId: string): string => {\n    const account = bankAccounts.find(acc => acc.id === accountId);\n    return account?.name || 'Unknown Account';\n  };\n\n  const isDuplicate = (transaction: StoredTransaction): boolean => {\n    return duplicateGroups.some(group => group.some(t => t.id === transaction.id));\n  };\n\n  if (loading) {\n    return (\n      <div className=\"transactions-loading\">\n        <div className=\"loading-spinner\"></div>\n        <p>Loading transactions...</p>\n      </div>\n    );\n  }\n\n  if (error) {\n    return (\n      <div className=\"transactions-error\">\n        <div className=\"error-icon\">⚠️</div>\n        <h3>Error Loading Transactions</h3>\n        <p>{error}</p>\n        <button onClick={loadData} className=\"btn btn-primary\">\n          Try Again\n        </button>\n      </div>\n    );\n  }\n\n  const renderTabContent = () => {\n    switch (activeTab) {\n      case 'categorization':\n        return <TransactionCategorization refreshTrigger={refreshTrigger} />;\n      case 'all':\n      default:\n        return (\n          <div className=\"all-transactions-content\">\n            {/* Filters Section */}\n            <div className=\"transactions-filters\">\n              <div className=\"filters-row\">\n                <div className=\"filter-group\">\n                  <label className=\"filter-label\">Account</label>\n                  <select\n                    value={filters.accountId}\n                    onChange={(e) => handleFilterChange('accountId', e.target.value)}\n                    className=\"filter-select\"\n                  >\n                    <option value=\"\">All Accounts</option>\n                    {bankAccounts.map(account => (\n                      <option key={account.id} value={account.id}>\n                        {account.name} - {account.accountNumber}\n                      </option>\n                    ))}\n                  </select>\n                </div>\n\n                <div className=\"filter-group\">\n                  <label className=\"filter-label\">Type</label>\n                  <select\n                    value={filters.type}\n                    onChange={(e) => handleFilterChange('type', e.target.value as FilterType)}\n                    className=\"filter-select\"\n                  >\n                    <option value=\"all\">All Types</option>\n                    <option value=\"debits\">Debits Only</option>\n                    <option value=\"credits\">Credits Only</option>\n                  </select>\n                </div>\n\n                <div className=\"filter-group\">\n                  <label className=\"filter-label\">\n                    <input\n                      type=\"checkbox\"\n                      checked={showDuplicatesOnly}\n                      onChange={(e) => setShowDuplicatesOnly(e.target.checked)}\n                      className=\"filter-checkbox\"\n                    />\n                    Show Duplicates Only\n                  </label>\n                </div>\n              </div>\n\n              <div className=\"filters-row\">\n                <div className=\"filter-group\">\n                  <label className=\"filter-label\">Search</label>\n                  <input\n                    id=\"search-input\"\n                    type=\"text\"\n                    value={filters.description}\n                    onChange={(e) => handleFilterChange('description', e.target.value)}\n                    placeholder=\"Search description or reference...\"\n                    className=\"filter-input\"\n                  />\n                </div>\n\n                <div className=\"filter-group\">\n                  <label className=\"filter-label\">Date From</label>\n                  <input\n                    type=\"date\"\n                    value={filters.dateFrom}\n                    onChange={(e) => handleFilterChange('dateFrom', e.target.value)}\n                    className=\"filter-input\"\n                  />\n                </div>\n\n                <div className=\"filter-group\">\n                  <label className=\"filter-label\">Date To</label>\n                  <input\n                    type=\"date\"\n                    value={filters.dateTo}\n                    onChange={(e) => handleFilterChange('dateTo', e.target.value)}\n                    className=\"filter-input\"\n                  />\n                </div>\n\n                <div className=\"filter-group\">\n                  <label className=\"filter-label\">Amount Range</label>\n                  <div className=\"amount-range\">\n                    <input\n                      type=\"number\"\n                      value={filters.amountFrom}\n                      onChange={(e) => handleFilterChange('amountFrom', e.target.value)}\n                      placeholder=\"Min\"\n                      className=\"filter-input amount-input\"\n                      step=\"0.01\"\n                    />\n                    <span className=\"amount-separator\">-</span>\n                    <input\n                      type=\"number\"\n                      value={filters.amountTo}\n                      onChange={(e) => handleFilterChange('amountTo', e.target.value)}\n                      placeholder=\"Max\"\n                      className=\"filter-input amount-input\"\n                      step=\"0.01\"\n                    />\n                  </div>\n                </div>\n\n                <div className=\"filter-group\">\n                  <button onClick={clearFilters} className=\"btn btn-secondary btn-sm\">\n                    Clear Filters\n                  </button>\n                </div>\n              </div>\n            </div>\n\n            {/* Transactions Table */}\n            <div className=\"transactions-table-container\">\n              <table className=\"transactions-table\">\n                <thead>\n                  <tr>\n                    <th className=\"checkbox-col\">\n                      <input\n                        type=\"checkbox\"\n                        checked={selectedTransactions.size === currentTransactions.length && currentTransactions.length > 0}\n                        onChange={handleSelectAll}\n                        className=\"table-checkbox\"\n                      />\n                    </th>\n                    <th \n                      className={`sortable ${sortField === 'postDateTime' ? 'sorted' : ''}`}\n                      onClick={() => handleSort('postDateTime')}\n                    >\n                      Date\n                      {sortField === 'postDateTime' && (\n                        <span className=\"sort-arrow\">\n                          {sortDirection === 'asc' ? '↑' : '↓'}\n                        </span>\n                      )}\n                    </th>\n                    <th \n                      className={`sortable ${sortField === 'accountName' ? 'sorted' : ''}`}\n                      onClick={() => handleSort('accountName')}\n                    >\n                      Account\n                      {sortField === 'accountName' && (\n                        <span className=\"sort-arrow\">\n                          {sortDirection === 'asc' ? '↑' : '↓'}\n                        </span>\n                      )}\n                    </th>\n                    <th \n                      className={`sortable ${sortField === 'description' ? 'sorted' : ''}`}\n                      onClick={() => handleSort('description')}\n                    >\n                      Description\n                      {sortField === 'description' && (\n                        <span className=\"sort-arrow\">\n                          {sortDirection === 'asc' ? '↑' : '↓'}\n                        </span>\n                      )}\n                    </th>\n                    <th className=\"amount-col\">Debit</th>\n                    <th className=\"amount-col\">Credit</th>\n                    <th \n                      className={`amount-col sortable ${sortField === 'balance' ? 'sorted' : ''}`}\n                      onClick={() => handleSort('balance')}\n                    >\n                      Balance\n                      {sortField === 'balance' && (\n                        <span className=\"sort-arrow\">\n                          {sortDirection === 'asc' ? '↑' : '↓'}\n                        </span>\n                      )}\n                    </th>\n                    <th>Reference</th>\n                  </tr>\n                </thead>\n                <tbody>\n                  {currentTransactions.map((transaction, _index) => (\n                    <tr \n                      key={transaction.id}\n                      className={`\n                        ${selectedTransactions.has(transaction.id) ? 'selected' : ''}\n                        ${isDuplicate(transaction) ? 'duplicate' : ''}\n                      `}\n                      title={transaction.description}\n                    >\n                      <td className=\"checkbox-col\">\n                        <input\n                          type=\"checkbox\"\n                          checked={selectedTransactions.has(transaction.id)}\n                          onChange={() => handleSelectTransaction(transaction.id)}\n                          className=\"table-checkbox\"\n                        />\n                      </td>\n                      <td className=\"date-col\">\n                        <div className=\"date-display\">\n                          {formatDate(transaction.postDateTime)}\n                          {transaction.time && (\n                            <span className=\"time-display\">{transaction.time}</span>\n                          )}\n                        </div>\n                      </td>\n                      <td className=\"account-col\">\n                        <div className=\"account-info\">\n                          {getAccountName(transaction.accountId)}\n                          {isDuplicate(transaction) && (\n                            <span className=\"duplicate-badge\">DUPLICATE</span>\n                          )}\n                        </div>\n                      </td>\n                      <td className=\"description-col\">\n                        <div className=\"description-content\">\n                          {transaction.description}\n                        </div>\n                      </td>\n                      <td className=\"amount-col debit\">\n                        {transaction.debitAmount ? formatCurrency(transaction.debitAmount) : ''}\n                      </td>\n                      <td className=\"amount-col credit\">\n                        {transaction.creditAmount ? formatCurrency(transaction.creditAmount) : ''}\n                      </td>\n                      <td className=\"amount-col balance\">\n                        {formatCurrency(transaction.balance)}\n                      </td>\n                      <td className=\"reference-col\">\n                        {transaction.reference || ''}\n                      </td>\n                    </tr>\n                  ))}\n                </tbody>\n              </table>\n\n              {currentTransactions.length === 0 && (\n                <div className=\"no-transactions\">\n                  <div className=\"no-transactions-icon\">📊</div>\n                  <h3>No Transactions Found</h3>\n                  <p>\n                    {filteredAndSortedTransactions.length === 0 && transactions.length === 0\n                      ? 'No transactions have been imported yet.'\n                      : 'No transactions match your current filters.'}\n                  </p>\n                  {filteredAndSortedTransactions.length === 0 && transactions.length > 0 && (\n                    <button onClick={clearFilters} className=\"btn btn-primary\">\n                      Clear Filters\n                    </button>\n                  )}\n                </div>\n              )}\n            </div>\n\n            {/* Pagination */}\n            {totalPages > 1 && (\n              <div className=\"transactions-pagination\">\n                <div className=\"pagination-info\">\n                  <span>\n                    Showing {startIndex + 1}-{Math.min(endIndex, filteredAndSortedTransactions.length)} of {filteredAndSortedTransactions.length} transactions\n                  </span>\n                  <div className=\"items-per-page\">\n                    <label>Items per page:</label>\n                    <select\n                      value={itemsPerPage}\n                      onChange={(e) => setItemsPerPage(Number(e.target.value))}\n                      className=\"page-size-select\"\n                    >\n                      {ITEMS_PER_PAGE_OPTIONS.map(size => (\n                        <option key={size} value={size}>{size}</option>\n                      ))}\n                    </select>\n                  </div>\n                </div>\n                <div className=\"pagination-controls\">\n                  <button\n                    onClick={() => setCurrentPage(1)}\n                    disabled={currentPage === 1}\n                    className=\"btn btn-secondary btn-sm\"\n                  >\n                    First\n                  </button>\n                  <button\n                    onClick={() => setCurrentPage(currentPage - 1)}\n                    disabled={currentPage === 1}\n                    className=\"btn btn-secondary btn-sm\"\n                  >\n                    Previous\n                  </button>\n                  <div className=\"pagination-pages\">\n                    {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {\n                      const pageNumber = Math.max(1, Math.min(totalPages - 4, currentPage - 2)) + i;\n                      return (\n                        <button\n                          key={pageNumber}\n                          onClick={() => setCurrentPage(pageNumber)}\n                          className={`btn btn-secondary btn-sm ${currentPage === pageNumber ? 'active' : ''}`}\n                        >\n                          {pageNumber}\n                        </button>\n                      );\n                    })}\n                  </div>\n                  <button\n                    onClick={() => setCurrentPage(currentPage + 1)}\n                    disabled={currentPage === totalPages}\n                    className=\"btn btn-secondary btn-sm\"\n                  >\n                    Next\n                  </button>\n                  <button\n                    onClick={() => setCurrentPage(totalPages)}\n                    disabled={currentPage === totalPages}\n                    className=\"btn btn-secondary btn-sm\"\n                  >\n                    Last\n                  </button>\n                </div>\n              </div>\n            )}\n\n            {/* Keyboard Shortcuts Help */}\n            <div className=\"keyboard-shortcuts\">\n              <details>\n                <summary>Keyboard Shortcuts</summary>\n                <div className=\"shortcuts-grid\">\n                  <div className=\"shortcut-item\">\n                    <kbd>←</kbd> <span>Previous page</span>\n                  </div>\n                  <div className=\"shortcut-item\">\n                    <kbd>→</kbd> <span>Next page</span>\n                  </div>\n                  <div className=\"shortcut-item\">\n                    <kbd>Home</kbd> <span>First page</span>\n                  </div>\n                  <div className=\"shortcut-item\">\n                    <kbd>End</kbd> <span>Last page</span>\n                  </div>\n                  <div className=\"shortcut-item\">\n                    <kbd>Ctrl+F</kbd> <span>Focus search</span>\n                  </div>\n                  <div className=\"shortcut-item\">\n                    <kbd>Ctrl+R</kbd> <span>Refresh</span>\n                  </div>\n                  <div className=\"shortcut-item\">\n                    <kbd>Ctrl+A</kbd> <span>Select all</span>\n                  </div>\n                  <div className=\"shortcut-item\">\n                    <kbd>Esc</kbd> <span>Clear selection</span>\n                  </div>\n                </div>\n              </details>\n            </div>\n          </div>\n        );\n    }\n  };\n\n  return (\n    <div className=\"transactions\">\n      <div className=\"transactions-header\">\n        <div className=\"transactions-title-section\">\n          <h2 className=\"transactions-title\">Transactions</h2>\n          <div className=\"transactions-stats\">\n            <span className=\"stat-item\">\n              Total: <strong>{transactions.length.toLocaleString()}</strong>\n            </span>\n            <span className=\"stat-item\">\n              Filtered: <strong>{filteredAndSortedTransactions.length.toLocaleString()}</strong>\n            </span>\n            {duplicateGroups.length > 0 && (\n              <span className=\"stat-item duplicate-stat\">\n                Duplicates: <strong>{duplicateGroups.flat().length}</strong>\n              </span>\n            )}\n          </div>\n        </div>\n        <div className=\"transactions-actions\">\n          <button onClick={loadData} className=\"btn btn-secondary btn-sm\" disabled={refreshing}>\n            <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\">\n              <polyline points=\"23 4 23 10 17 10\"></polyline>\n              <polyline points=\"1 20 1 14 7 14\"></polyline>\n              <path d=\"M20.49 9A9 9 0 0 0 5.64 5.64L1 10m22 4l-4.64 4.36A9 9 0 0 1 3.51 15\"></path>\n            </svg>\n            {refreshing ? 'Refreshing...' : 'Refresh'}\n          </button>\n          {refreshing && (\n            <span className=\"refresh-indicator\">\n              <div className=\"refresh-spinner\"></div>\n              Data updated - refreshing...\n            </span>\n          )}\n          {selectedTransactions.size > 0 && (\n            <span className=\"selection-count\">\n              {selectedTransactions.size} selected\n            </span>\n          )}\n        </div>\n      </div>\n\n      {/* Transaction Tabs */}\n      <div className=\"transaction-tabs\">\n        <button\n          className={`tab-button ${activeTab === 'all' ? 'active' : ''}`}\n          onClick={() => setActiveTab('all')}\n        >\n          <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\">\n            <path d=\"M3 6h18\" />\n            <path d=\"M3 12h18\" />\n            <path d=\"M3 18h18\" />\n            <circle cx=\"6\" cy=\"6\" r=\"1\" />\n            <circle cx=\"6\" cy=\"12\" r=\"1\" />\n            <circle cx=\"6\" cy=\"18\" r=\"1\" />\n          </svg>\n          All Transactions\n        </button>\n        <button\n          className={`tab-button ${activeTab === 'categorization' ? 'active' : ''}`}\n          onClick={() => setActiveTab('categorization')}\n        >\n          <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\">\n            <path d=\"M22 12h-4l-3 9L9 3l-3 9H2\" />\n          </svg>\n          Categorization\n        </button>\n      </div>\n\n      {/* Tab Content */}\n      <div className=\"tab-content\">\n        {renderTabContent()}\n      </div>\n    </div>\n  );\n}; "], "mappings": "6FAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,CAAEC,WAAW,CAAEC,OAAO,KAAQ,OAAO,CACxE,OAASC,yBAAyB,KAAgC,uCAAuC,CACzG,OAASC,kBAAkB,KAAQ,gCAAgC,CAEnE,OAASC,yBAAyB,KAAQ,6BAA6B,CACvE,MAAO,oBAAoB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAuB5B,KAAM,CAAAC,sBAAsB,CAAG,CAAC,EAAE,CAAE,EAAE,CAAE,EAAE,CAAE,GAAG,CAAC,CAChD,KAAM,CAAAC,sBAAsB,CAAG,EAAE,CAEjC,MAAO,MAAM,CAAAC,YAAyC,CAAGC,IAAA,EAA6C,IAA5C,CAAEC,mBAAmB,CAAEC,cAAe,CAAC,CAAAF,IAAA,CAC/F;AACA,KAAM,CAACG,SAAS,CAAEC,YAAY,CAAC,CAAGlB,QAAQ,CAAiB,KAAK,CAAC,CAEjE;AACA,KAAM,CAACmB,YAAY,CAAEC,eAAe,CAAC,CAAGpB,QAAQ,CAAsB,EAAE,CAAC,CACzE,KAAM,CAACqB,YAAY,CAAEC,eAAe,CAAC,CAAGtB,QAAQ,CAAgB,EAAE,CAAC,CACnE,KAAM,CAACuB,eAAe,CAAEC,kBAAkB,CAAC,CAAGxB,QAAQ,CAAwB,EAAE,CAAC,CACjF,KAAM,CAACyB,OAAO,CAAEC,UAAU,CAAC,CAAG1B,QAAQ,CAAC,IAAI,CAAC,CAC5C,KAAM,CAAC2B,KAAK,CAAEC,QAAQ,CAAC,CAAG5B,QAAQ,CAAgB,IAAI,CAAC,CACvD,KAAM,CAAC6B,UAAU,CAAEC,aAAa,CAAC,CAAG9B,QAAQ,CAAC,KAAK,CAAC,CAEnD;AACA,KAAM,CAAC+B,WAAW,CAAEC,cAAc,CAAC,CAAGhC,QAAQ,CAAC,CAAC,CAAC,CACjD,KAAM,CAACiC,YAAY,CAAEC,eAAe,CAAC,CAAGlC,QAAQ,CAACY,sBAAsB,CAAC,CAExE;AACA,KAAM,CAACuB,SAAS,CAAEC,YAAY,CAAC,CAAGpC,QAAQ,CAAY,cAAc,CAAC,CACrE,KAAM,CAACqC,aAAa,CAAEC,gBAAgB,CAAC,CAAGtC,QAAQ,CAAgB,MAAM,CAAC,CAEzE;AACA,KAAM,CAACuC,OAAO,CAAEC,UAAU,CAAC,CAAGxC,QAAQ,CAAqB,CACzDyC,SAAS,CAAE,EAAE,CACbC,QAAQ,CAAE,EAAE,CACZC,MAAM,CAAE,EAAE,CACVC,UAAU,CAAE,EAAE,CACdC,QAAQ,CAAE,EAAE,CACZC,WAAW,CAAE,EAAE,CACfC,IAAI,CAAE,KACR,CAAC,CAAC,CAEF;AACA,KAAM,CAACC,oBAAoB,CAAEC,uBAAuB,CAAC,CAAGjD,QAAQ,CAAc,GAAI,CAAAkD,GAAG,CAAC,CAAC,CAAC,CACxF,KAAM,CAACC,kBAAkB,CAAEC,qBAAqB,CAAC,CAAGpD,QAAQ,CAAC,KAAK,CAAC,CAEnE;AACA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAEE;AACA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAEE;AACA,KAAM,CAAAqD,4BAA4B,CAAGnD,WAAW,CAAEoD,aAAkC,EAA4B,CAC9G;AACA,MAAO,EAAE,CAET;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MACE,CAAC,CAAE,EAAE,CAAC,CAAE;AAER;AACA,KAAM,CAAAC,QAAQ,CAAGrD,WAAW,CAAC,SAAY,CACvC,GAAI,CACF;AACA,GAAIiB,YAAY,CAACqC,MAAM,CAAG,CAAC,CAAE,CAC3B1B,aAAa,CAAC,IAAI,CAAC,CACrB,CAAC,IAAM,CACLJ,UAAU,CAAC,IAAI,CAAC,CAClB,CACAE,QAAQ,CAAC,IAAI,CAAC,CAEd;AACA,KAAM,CAAA6B,QAAQ,CAAGpD,kBAAkB,CAACqD,cAAc,CAAC,CAAC,CACpDpC,eAAe,CAACmC,QAAQ,CAAC,CAEzB;AACA,KAAM,CAAAE,eAAoC,CAAG,EAAE,CAC/CF,QAAQ,CAACG,OAAO,CAACC,OAAO,EAAI,CAC1B,KAAM,CAAAC,mBAAmB,CAAG1D,yBAAyB,CAAC2D,wBAAwB,CAACF,OAAO,CAACG,EAAE,CAAC,CAC1FL,eAAe,CAACM,IAAI,CAAC,GAAGH,mBAAmB,CAAC,CAC9C,CAAC,CAAC,CAEF1C,eAAe,CAACuC,eAAe,CAAC,CAEhC;AACA,KAAM,CAAAO,UAAU,CAAGb,4BAA4B,CAACM,eAAe,CAAC,CAChEnC,kBAAkB,CAAC0C,UAAU,CAAC,CAE9B,GAAInD,mBAAmB,CAAE,CACvBA,mBAAmB,CAAC4C,eAAe,CAAC,CACtC,CACF,CAAE,MAAOQ,GAAG,CAAE,CACZvC,QAAQ,CAACuC,GAAG,WAAY,CAAAC,KAAK,CAAGD,GAAG,CAACE,OAAO,CAAG,6BAA6B,CAAC,CAC9E,CAAC,OAAS,CACR3C,UAAU,CAAC,KAAK,CAAC,CACjBI,aAAa,CAAC,KAAK,CAAC,CACtB,CACF,CAAC,CAAE,CAACf,mBAAmB,CAAEsC,4BAA4B,CAAC,CAAC,CAEvDpD,SAAS,CAAC,IAAM,CACdsD,QAAQ,CAAC,CAAC,CACZ,CAAC,CAAE,CAACA,QAAQ,CAAEvC,cAAc,CAAC,CAAC,CAE9B;AACA,KAAM,CAAAsD,6BAA6B,CAAGnE,OAAO,CAAC,IAAM,CAClD;AACA,GAAI,CAACgB,YAAY,EAAIA,YAAY,CAACqC,MAAM,GAAK,CAAC,CAAE,CAC9C,MAAO,EAAE,CACX,CAEAe,OAAO,CAACC,GAAG,CAAC,gCAAgC,CAAErD,YAAY,CAACqC,MAAM,CAAE,cAAc,CAAC,CAClF,GAAI,CAAAiB,QAAQ,CAAG,CAAC,GAAGtD,YAAY,CAAC,CAEhC;AACA,GAAIoB,OAAO,CAACE,SAAS,CAAE,CACrBgC,QAAQ,CAAGA,QAAQ,CAACC,MAAM,CAACC,CAAC,EAAIA,CAAC,CAAClC,SAAS,GAAKF,OAAO,CAACE,SAAS,CAAC,CAClE8B,OAAO,CAACC,GAAG,CAAC,0CAA0C,CAAEC,QAAQ,CAACjB,MAAM,CAAC,CAC1E,CAEA,GAAIjB,OAAO,CAACG,QAAQ,CAAE,CACpB,KAAM,CAAAkC,QAAQ,CAAG,GAAI,CAAAC,IAAI,CAACtC,OAAO,CAACG,QAAQ,CAAC,CAC3C+B,QAAQ,CAAGA,QAAQ,CAACC,MAAM,CAACC,CAAC,EAAI,GAAI,CAAAE,IAAI,CAACF,CAAC,CAACG,YAAY,CAAC,EAAIF,QAAQ,CAAC,CACrEL,OAAO,CAACC,GAAG,CAAC,yCAAyC,CAAEC,QAAQ,CAACjB,MAAM,CAAC,CACzE,CAEA,GAAIjB,OAAO,CAACI,MAAM,CAAE,CAClB,KAAM,CAAAoC,MAAM,CAAG,GAAI,CAAAF,IAAI,CAACtC,OAAO,CAACI,MAAM,CAAC,CACvC8B,QAAQ,CAAGA,QAAQ,CAACC,MAAM,CAACC,CAAC,EAAI,GAAI,CAAAE,IAAI,CAACF,CAAC,CAACG,YAAY,CAAC,EAAIC,MAAM,CAAC,CACnER,OAAO,CAACC,GAAG,CAAC,uCAAuC,CAAEC,QAAQ,CAACjB,MAAM,CAAC,CACvE,CAEA,GAAIjB,OAAO,CAACO,WAAW,CAAE,CACvB,KAAM,CAAAkC,UAAU,CAAGzC,OAAO,CAACO,WAAW,CAACmC,WAAW,CAAC,CAAC,CACpDR,QAAQ,CAAGA,QAAQ,CAACC,MAAM,CAACC,CAAC,EAC1BA,CAAC,CAAC7B,WAAW,CAACmC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACF,UAAU,CAAC,EAC/CL,CAAC,CAACQ,SAAS,EAAIR,CAAC,CAACQ,SAAS,CAACF,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACF,UAAU,CAC/D,CAAC,CACDT,OAAO,CAACC,GAAG,CAAC,4CAA4C,CAAEC,QAAQ,CAACjB,MAAM,CAAC,CAC5E,CAEA,GAAIjB,OAAO,CAACK,UAAU,CAAE,CACtB,KAAM,CAAAwC,SAAS,CAAGC,UAAU,CAAC9C,OAAO,CAACK,UAAU,CAAC,CAChD,GAAI,CAAC0C,KAAK,CAACF,SAAS,CAAC,CAAE,CACrBX,QAAQ,CAAGA,QAAQ,CAACC,MAAM,CAACC,CAAC,EAAI,CAC9B,KAAM,CAAAY,MAAM,CAAGC,IAAI,CAACC,GAAG,CAAC,CAACd,CAAC,CAACe,WAAW,EAAI,CAAC,GAAKf,CAAC,CAACgB,YAAY,EAAI,CAAC,CAAC,CAAC,CACrE,MAAO,CAAAJ,MAAM,EAAIH,SAAS,CAC5B,CAAC,CAAC,CACFb,OAAO,CAACC,GAAG,CAAC,2CAA2C,CAAEC,QAAQ,CAACjB,MAAM,CAAC,CAC3E,CACF,CAEA,GAAIjB,OAAO,CAACM,QAAQ,CAAE,CACpB,KAAM,CAAA+C,SAAS,CAAGP,UAAU,CAAC9C,OAAO,CAACM,QAAQ,CAAC,CAC9C,GAAI,CAACyC,KAAK,CAACM,SAAS,CAAC,CAAE,CACrBnB,QAAQ,CAAGA,QAAQ,CAACC,MAAM,CAACC,CAAC,EAAI,CAC9B,KAAM,CAAAY,MAAM,CAAGC,IAAI,CAACC,GAAG,CAAC,CAACd,CAAC,CAACe,WAAW,EAAI,CAAC,GAAKf,CAAC,CAACgB,YAAY,EAAI,CAAC,CAAC,CAAC,CACrE,MAAO,CAAAJ,MAAM,EAAIK,SAAS,CAC5B,CAAC,CAAC,CACFrB,OAAO,CAACC,GAAG,CAAC,yCAAyC,CAAEC,QAAQ,CAACjB,MAAM,CAAC,CACzE,CACF,CAEA,GAAIjB,OAAO,CAACQ,IAAI,GAAK,QAAQ,CAAE,CAC7B0B,QAAQ,CAAGA,QAAQ,CAACC,MAAM,CAACC,CAAC,EAAI,CAACA,CAAC,CAACe,WAAW,EAAI,CAAC,EAAI,CAAC,CAAC,CACzDnB,OAAO,CAACC,GAAG,CAAC,uCAAuC,CAAEC,QAAQ,CAACjB,MAAM,CAAC,CACvE,CAAC,IAAM,IAAIjB,OAAO,CAACQ,IAAI,GAAK,SAAS,CAAE,CACrC0B,QAAQ,CAAGA,QAAQ,CAACC,MAAM,CAACC,CAAC,EAAI,CAACA,CAAC,CAACgB,YAAY,EAAI,CAAC,EAAI,CAAC,CAAC,CAC1DpB,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAEC,QAAQ,CAACjB,MAAM,CAAC,CACxE,CAEA;AACA,GAAIL,kBAAkB,EAAI5B,eAAe,CAACiC,MAAM,CAAG,CAAC,CAAE,CACpD,KAAM,CAAAqC,YAAY,CAAG,GAAI,CAAA3C,GAAG,CAAC3B,eAAe,CAACuE,IAAI,CAAC,CAAC,CAACC,GAAG,CAACpB,CAAC,EAAIA,CAAC,CAACX,EAAE,CAAC,CAAC,CACnES,QAAQ,CAAGA,QAAQ,CAACC,MAAM,CAACC,CAAC,EAAIkB,YAAY,CAACG,GAAG,CAACrB,CAAC,CAACX,EAAE,CAAC,CAAC,CACvDO,OAAO,CAACC,GAAG,CAAC,2CAA2C,CAAEC,QAAQ,CAACjB,MAAM,CAAC,CAC3E,CAEA;AACA,GAAInC,YAAY,CAACmC,MAAM,CAAG,CAAC,CAAE,CAC3BiB,QAAQ,CAACwB,IAAI,CAAC,CAACC,CAAC,CAAEC,CAAC,GAAK,CACtB,GAAI,CAAAC,MAAuB,CAC3B,GAAI,CAAAC,MAAuB,CAE3B,OAAQlE,SAAS,EACf,IAAK,cAAc,CACjBiE,MAAM,CAAG,GAAI,CAAAvB,IAAI,CAACqB,CAAC,CAACpB,YAAY,CAAC,CAACwB,OAAO,CAAC,CAAC,CAC3CD,MAAM,CAAG,GAAI,CAAAxB,IAAI,CAACsB,CAAC,CAACrB,YAAY,CAAC,CAACwB,OAAO,CAAC,CAAC,CAC3C,MACF,IAAK,aAAa,CAChBF,MAAM,CAAGF,CAAC,CAACpD,WAAW,CAACmC,WAAW,CAAC,CAAC,CACpCoB,MAAM,CAAGF,CAAC,CAACrD,WAAW,CAACmC,WAAW,CAAC,CAAC,CACpC,MACF,IAAK,QAAQ,CACXmB,MAAM,CAAGZ,IAAI,CAACC,GAAG,CAAC,CAACS,CAAC,CAACR,WAAW,EAAI,CAAC,GAAKQ,CAAC,CAACP,YAAY,EAAI,CAAC,CAAC,CAAC,CAC/DU,MAAM,CAAGb,IAAI,CAACC,GAAG,CAAC,CAACU,CAAC,CAACT,WAAW,EAAI,CAAC,GAAKS,CAAC,CAACR,YAAY,EAAI,CAAC,CAAC,CAAC,CAC/D,MACF,IAAK,SAAS,CACZS,MAAM,CAAGF,CAAC,CAACK,OAAO,CAClBF,MAAM,CAAGF,CAAC,CAACI,OAAO,CAClB,MACF,IAAK,aAAa,CAChB,KAAM,CAAAC,QAAQ,CAAGnF,YAAY,CAACoF,IAAI,CAACC,GAAG,EAAIA,GAAG,CAAC1C,EAAE,GAAKkC,CAAC,CAACzD,SAAS,CAAC,CACjE,KAAM,CAAAkE,QAAQ,CAAGtF,YAAY,CAACoF,IAAI,CAACC,GAAG,EAAIA,GAAG,CAAC1C,EAAE,GAAKmC,CAAC,CAAC1D,SAAS,CAAC,CACjE2D,MAAM,CAAG,CAAAI,QAAQ,SAARA,QAAQ,iBAARA,QAAQ,CAAEI,IAAI,CAAC3B,WAAW,CAAC,CAAC,GAAI,EAAE,CAC3CoB,MAAM,CAAG,CAAAM,QAAQ,SAARA,QAAQ,iBAARA,QAAQ,CAAEC,IAAI,CAAC3B,WAAW,CAAC,CAAC,GAAI,EAAE,CAC3C,MACF,QACE,MAAO,EAAC,CACZ,CAEA,GAAI5C,aAAa,GAAK,KAAK,CAAE,CAC3B,MAAO,CAAA+D,MAAM,CAAGC,MAAM,CAAG,CAAC,CAAC,CAAGD,MAAM,CAAGC,MAAM,CAAG,CAAC,CAAG,CAAC,CACvD,CAAC,IAAM,CACL,MAAO,CAAAD,MAAM,CAAGC,MAAM,CAAG,CAAC,CAAC,CAAGD,MAAM,CAAGC,MAAM,CAAG,CAAC,CAAG,CAAC,CACvD,CACF,CAAC,CAAC,CACJ,CAEA9B,OAAO,CAACC,GAAG,CAAC,yCAAyC,CAAEC,QAAQ,CAACjB,MAAM,CAAC,CACvE,MAAO,CAAAiB,QAAQ,CACjB,CAAC,CAAE,CAACtD,YAAY,CAAEoB,OAAO,CAAEJ,SAAS,CAAEE,aAAa,CAAEc,kBAAkB,CAAE5B,eAAe,CAAEF,YAAY,CAAC,CAAC,CAExG;AACA,KAAM,CAAAwF,iBAAiB,CAAGvC,6BAA6B,CAACd,MAAM,CAC9D,KAAM,CAAAsD,UAAU,CAAGtB,IAAI,CAACuB,IAAI,CAACF,iBAAiB,CAAG5E,YAAY,CAAC,CAC9D,KAAM,CAAA+E,UAAU,CAAG,CAACjF,WAAW,CAAG,CAAC,EAAIE,YAAY,CACnD,KAAM,CAAAgF,QAAQ,CAAGzB,IAAI,CAAC0B,GAAG,CAACF,UAAU,CAAG/E,YAAY,CAAE4E,iBAAiB,CAAC,CACvE,KAAM,CAAAM,mBAAmB,CAAG7C,6BAA6B,CAAC8C,KAAK,CAACJ,UAAU,CAAEC,QAAQ,CAAC,CAErF;AACA1C,OAAO,CAACC,GAAG,CAAC,mBAAmB,CAAE,CAC/BzC,WAAW,CACXE,YAAY,CACZ4E,iBAAiB,CACjBC,UAAU,CACVE,UAAU,CACVC,QAAQ,CACRI,WAAW,CAAEF,mBAAmB,CAAC3D,MAAM,CACvC8D,cAAc,CAAE9B,IAAI,CAAC0B,GAAG,CAACjF,YAAY,CAAE4E,iBAAiB,CAAGG,UAAU,CAAC,CACtEO,uBAAuB,CAAEjD,6BAA6B,CAACd,MAAM,CAC7DgE,kBAAkB,CAAErG,YAAY,CAACqC,MAAM,CACvCiE,WAAW,CAAEN,mBAAmB,CAACC,KAAK,CAAC,CAAC,CAAE,CAAC,CAAC,CAACrB,GAAG,CAACpB,CAAC,GAAK,CAAEX,EAAE,CAAEW,CAAC,CAACX,EAAE,CAAE0D,IAAI,CAAE/C,CAAC,CAAC7B,WAAW,CAAC6E,SAAS,CAAC,CAAC,CAAE,EAAE,CAAE,CAAC,CAAC,CAC5G,CAAC,CAAC,CAEF;AACA5H,KAAK,CAACE,SAAS,CAAC,IAAM,CACpB,KAAM,CAAA2H,SAAS,CAAGC,QAAQ,CAACC,gBAAgB,CAAC,8BAA8B,CAAC,CAC3EvD,OAAO,CAACC,GAAG,CAAC,iBAAiB,CAAEoD,SAAS,CAACpE,MAAM,CAAE,WAAW,CAAE2D,mBAAmB,CAAC3D,MAAM,CAAC,CAC3F,CAAC,CAAE,CAAC2D,mBAAmB,CAAC,CAAC,CAIzB;AACAlH,SAAS,CAAC,IAAM,CACd+B,cAAc,CAAC,CAAC,CAAC,CACnB,CAAC,CAAE,CAACO,OAAO,CAAEJ,SAAS,CAAEE,aAAa,CAAEc,kBAAkB,CAAElB,YAAY,CAAC,CAAC,CAEzE;AACA,KAAM,CAAA8F,UAAU,CAAIC,KAAgB,EAAK,CACvC,GAAI7F,SAAS,GAAK6F,KAAK,CAAE,CACvB1F,gBAAgB,CAACD,aAAa,GAAK,KAAK,CAAG,MAAM,CAAG,KAAK,CAAC,CAC5D,CAAC,IAAM,CACLD,YAAY,CAAC4F,KAAK,CAAC,CACnB1F,gBAAgB,CAAC,MAAM,CAAC,CAC1B,CACF,CAAC,CAED,KAAM,CAAA2F,kBAAkB,CAAGA,CAACC,GAA6B,CAAEC,KAAa,GAAK,CAC3E3F,UAAU,CAAC4F,IAAI,EAAAC,aAAA,CAAAA,aAAA,IAAUD,IAAI,MAAE,CAACF,GAAG,EAAGC,KAAK,EAAG,CAAC,CACjD,CAAC,CAED,KAAM,CAAAG,uBAAuB,CAAIC,aAAqB,EAAK,CACzDtF,uBAAuB,CAACmF,IAAI,EAAI,CAC9B,KAAM,CAAAI,MAAM,CAAG,GAAI,CAAAtF,GAAG,CAACkF,IAAI,CAAC,CAC5B,GAAII,MAAM,CAACxC,GAAG,CAACuC,aAAa,CAAC,CAAE,CAC7BC,MAAM,CAACC,MAAM,CAACF,aAAa,CAAC,CAC9B,CAAC,IAAM,CACLC,MAAM,CAACE,GAAG,CAACH,aAAa,CAAC,CAC3B,CACA,MAAO,CAAAC,MAAM,CACf,CAAC,CAAC,CACJ,CAAC,CAED,KAAM,CAAAG,eAAe,CAAGzI,WAAW,CAAC,IAAM,CACxC,GAAI8C,oBAAoB,CAAC4F,IAAI,GAAKzB,mBAAmB,CAAC3D,MAAM,CAAE,CAC5DP,uBAAuB,CAAC,GAAI,CAAAC,GAAG,CAAC,CAAC,CAAC,CACpC,CAAC,IAAM,CACLD,uBAAuB,CAAC,GAAI,CAAAC,GAAG,CAACiE,mBAAmB,CAACpB,GAAG,CAACpB,CAAC,EAAIA,CAAC,CAACX,EAAE,CAAC,CAAC,CAAC,CACtE,CACF,CAAC,CAAE,CAAChB,oBAAoB,CAAC4F,IAAI,CAAEzB,mBAAmB,CAAC,CAAC,CAEpD,KAAM,CAAA0B,YAAY,CAAGA,CAAA,GAAM,CACzBrG,UAAU,CAAC,CACTC,SAAS,CAAE,EAAE,CACbC,QAAQ,CAAE,EAAE,CACZC,MAAM,CAAE,EAAE,CACVC,UAAU,CAAE,EAAE,CACdC,QAAQ,CAAE,EAAE,CACZC,WAAW,CAAE,EAAE,CACfC,IAAI,CAAE,KACR,CAAC,CAAC,CACFK,qBAAqB,CAAC,KAAK,CAAC,CAC9B,CAAC,CAED;AACAnD,SAAS,CAAC,IAAM,CACd,KAAM,CAAA6I,cAAc,CAAIC,CAAgB,EAAK,CAC3C,GAAIA,CAAC,CAACC,MAAM,WAAY,CAAAC,gBAAgB,EAAIF,CAAC,CAACC,MAAM,WAAY,CAAAE,mBAAmB,CAAE,CACnF,OAAQ;AACV,CAEA,OAAQH,CAAC,CAACb,GAAG,EACX,IAAK,WAAW,CACd,GAAInG,WAAW,CAAG,CAAC,CAAE,CACnBC,cAAc,CAACD,WAAW,CAAG,CAAC,CAAC,CACjC,CACA,MACF,IAAK,YAAY,CACf,GAAIA,WAAW,CAAG+E,UAAU,CAAE,CAC5B9E,cAAc,CAACD,WAAW,CAAG,CAAC,CAAC,CACjC,CACA,MACF,IAAK,MAAM,CACTC,cAAc,CAAC,CAAC,CAAC,CACjB,MACF,IAAK,KAAK,CACRA,cAAc,CAAC8E,UAAU,CAAC,CAC1B,MACF,IAAK,GAAG,CACN,GAAIiC,CAAC,CAACI,OAAO,EAAIJ,CAAC,CAACK,OAAO,CAAE,KAAAC,qBAAA,CAC1BN,CAAC,CAACO,cAAc,CAAC,CAAC,CAClB,CAAAD,qBAAA,CAAAxB,QAAQ,CAAC0B,cAAc,CAAC,cAAc,CAAC,UAAAF,qBAAA,iBAAvCA,qBAAA,CAAyCG,KAAK,CAAC,CAAC,CAClD,CACA,MACF,IAAK,GAAG,CACN,GAAIT,CAAC,CAACI,OAAO,EAAIJ,CAAC,CAACK,OAAO,CAAE,CAC1BL,CAAC,CAACO,cAAc,CAAC,CAAC,CAClB/F,QAAQ,CAAC,CAAC,CACZ,CACA,MACF,IAAK,GAAG,CACN,GAAIwF,CAAC,CAACI,OAAO,EAAIJ,CAAC,CAACK,OAAO,CAAE,CAC1BL,CAAC,CAACO,cAAc,CAAC,CAAC,CAClBX,eAAe,CAAC,CAAC,CACnB,CACA,MACF,IAAK,QAAQ,CACX1F,uBAAuB,CAAC,GAAI,CAAAC,GAAG,CAAC,CAAC,CAAC,CAClC,MACJ,CACF,CAAC,CAED2E,QAAQ,CAAC4B,gBAAgB,CAAC,SAAS,CAAEX,cAAc,CAAC,CACpD,MAAO,IAAMjB,QAAQ,CAAC6B,mBAAmB,CAAC,SAAS,CAAEZ,cAAc,CAAC,CACtE,CAAC,CAAE,CAAC/G,WAAW,CAAE+E,UAAU,CAAEvD,QAAQ,CAAEoF,eAAe,CAAC,CAAC,CAExD;AACA,KAAM,CAAAgB,cAAc,CAAIpE,MAAc,EAAa,CACjD,MAAO,IAAI,CAAAqE,IAAI,CAACC,YAAY,CAAC,OAAO,CAAE,CACpCC,qBAAqB,CAAE,CAAC,CACxBC,qBAAqB,CAAE,CACzB,CAAC,CAAC,CAACC,MAAM,CAACzE,MAAM,CAAC,CACnB,CAAC,CAED,KAAM,CAAA0E,UAAU,CAAIC,UAAkB,EAAa,CACjD,KAAM,CAAAC,IAAI,CAAG,GAAI,CAAAtF,IAAI,CAACqF,UAAU,CAAC,CACjC,MAAO,CAAAC,IAAI,CAACC,kBAAkB,CAAC,OAAO,CAAE,CACtCC,GAAG,CAAE,SAAS,CACdC,KAAK,CAAE,SAAS,CAChBC,IAAI,CAAE,SACR,CAAC,CAAC,CACJ,CAAC,CAED,KAAM,CAAAC,cAAc,CAAI/H,SAAiB,EAAa,CACpD,KAAM,CAAAoB,OAAO,CAAGxC,YAAY,CAACoF,IAAI,CAACC,GAAG,EAAIA,GAAG,CAAC1C,EAAE,GAAKvB,SAAS,CAAC,CAC9D,MAAO,CAAAoB,OAAO,SAAPA,OAAO,iBAAPA,OAAO,CAAE+C,IAAI,GAAI,iBAAiB,CAC3C,CAAC,CAED,KAAM,CAAA6D,WAAW,CAAIC,WAA8B,EAAc,CAC/D,MAAO,CAAAnJ,eAAe,CAACoJ,IAAI,CAACC,KAAK,EAAIA,KAAK,CAACD,IAAI,CAAChG,CAAC,EAAIA,CAAC,CAACX,EAAE,GAAK0G,WAAW,CAAC1G,EAAE,CAAC,CAAC,CAChF,CAAC,CAED,GAAIvC,OAAO,CAAE,CACX,mBACEf,KAAA,QAAKmK,SAAS,CAAC,sBAAsB,CAAAC,QAAA,eACnCtK,IAAA,QAAKqK,SAAS,CAAC,iBAAiB,CAAM,CAAC,cACvCrK,IAAA,MAAAsK,QAAA,CAAG,yBAAuB,CAAG,CAAC,EAC3B,CAAC,CAEV,CAEA,GAAInJ,KAAK,CAAE,CACT,mBACEjB,KAAA,QAAKmK,SAAS,CAAC,oBAAoB,CAAAC,QAAA,eACjCtK,IAAA,QAAKqK,SAAS,CAAC,YAAY,CAAAC,QAAA,CAAC,cAAE,CAAK,CAAC,cACpCtK,IAAA,OAAAsK,QAAA,CAAI,4BAA0B,CAAI,CAAC,cACnCtK,IAAA,MAAAsK,QAAA,CAAInJ,KAAK,CAAI,CAAC,cACdnB,IAAA,WAAQuK,OAAO,CAAExH,QAAS,CAACsH,SAAS,CAAC,iBAAiB,CAAAC,QAAA,CAAC,WAEvD,CAAQ,CAAC,EACN,CAAC,CAEV,CAEA,KAAM,CAAAE,gBAAgB,CAAGA,CAAA,GAAM,CAC7B,OAAQ/J,SAAS,EACf,IAAK,gBAAgB,CACnB,mBAAOT,IAAA,CAACF,yBAAyB,EAACU,cAAc,CAAEA,cAAe,CAAE,CAAC,CACtE,IAAK,KAAK,CACV,QACE,mBACEN,KAAA,QAAKmK,SAAS,CAAC,0BAA0B,CAAAC,QAAA,eAEvCpK,KAAA,QAAKmK,SAAS,CAAC,sBAAsB,CAAAC,QAAA,eACnCpK,KAAA,QAAKmK,SAAS,CAAC,aAAa,CAAAC,QAAA,eAC1BpK,KAAA,QAAKmK,SAAS,CAAC,cAAc,CAAAC,QAAA,eAC3BtK,IAAA,UAAOqK,SAAS,CAAC,cAAc,CAAAC,QAAA,CAAC,SAAO,CAAO,CAAC,cAC/CpK,KAAA,WACEyH,KAAK,CAAE5F,OAAO,CAACE,SAAU,CACzBwI,QAAQ,CAAGlC,CAAC,EAAKd,kBAAkB,CAAC,WAAW,CAAEc,CAAC,CAACC,MAAM,CAACb,KAAK,CAAE,CACjE0C,SAAS,CAAC,eAAe,CAAAC,QAAA,eAEzBtK,IAAA,WAAQ2H,KAAK,CAAC,EAAE,CAAA2C,QAAA,CAAC,cAAY,CAAQ,CAAC,CACrCzJ,YAAY,CAAC0E,GAAG,CAAClC,OAAO,eACvBnD,KAAA,WAAyByH,KAAK,CAAEtE,OAAO,CAACG,EAAG,CAAA8G,QAAA,EACxCjH,OAAO,CAAC+C,IAAI,CAAC,KAAG,CAAC/C,OAAO,CAACqH,aAAa,GAD5BrH,OAAO,CAACG,EAEb,CACT,CAAC,EACI,CAAC,EACN,CAAC,cAENtD,KAAA,QAAKmK,SAAS,CAAC,cAAc,CAAAC,QAAA,eAC3BtK,IAAA,UAAOqK,SAAS,CAAC,cAAc,CAAAC,QAAA,CAAC,MAAI,CAAO,CAAC,cAC5CpK,KAAA,WACEyH,KAAK,CAAE5F,OAAO,CAACQ,IAAK,CACpBkI,QAAQ,CAAGlC,CAAC,EAAKd,kBAAkB,CAAC,MAAM,CAAEc,CAAC,CAACC,MAAM,CAACb,KAAmB,CAAE,CAC1E0C,SAAS,CAAC,eAAe,CAAAC,QAAA,eAEzBtK,IAAA,WAAQ2H,KAAK,CAAC,KAAK,CAAA2C,QAAA,CAAC,WAAS,CAAQ,CAAC,cACtCtK,IAAA,WAAQ2H,KAAK,CAAC,QAAQ,CAAA2C,QAAA,CAAC,aAAW,CAAQ,CAAC,cAC3CtK,IAAA,WAAQ2H,KAAK,CAAC,SAAS,CAAA2C,QAAA,CAAC,cAAY,CAAQ,CAAC,EACvC,CAAC,EACN,CAAC,cAENtK,IAAA,QAAKqK,SAAS,CAAC,cAAc,CAAAC,QAAA,cAC3BpK,KAAA,UAAOmK,SAAS,CAAC,cAAc,CAAAC,QAAA,eAC7BtK,IAAA,UACEuC,IAAI,CAAC,UAAU,CACfoI,OAAO,CAAEhI,kBAAmB,CAC5B8H,QAAQ,CAAGlC,CAAC,EAAK3F,qBAAqB,CAAC2F,CAAC,CAACC,MAAM,CAACmC,OAAO,CAAE,CACzDN,SAAS,CAAC,iBAAiB,CAC5B,CAAC,uBAEJ,EAAO,CAAC,CACL,CAAC,EACH,CAAC,cAENnK,KAAA,QAAKmK,SAAS,CAAC,aAAa,CAAAC,QAAA,eAC1BpK,KAAA,QAAKmK,SAAS,CAAC,cAAc,CAAAC,QAAA,eAC3BtK,IAAA,UAAOqK,SAAS,CAAC,cAAc,CAAAC,QAAA,CAAC,QAAM,CAAO,CAAC,cAC9CtK,IAAA,UACEwD,EAAE,CAAC,cAAc,CACjBjB,IAAI,CAAC,MAAM,CACXoF,KAAK,CAAE5F,OAAO,CAACO,WAAY,CAC3BmI,QAAQ,CAAGlC,CAAC,EAAKd,kBAAkB,CAAC,aAAa,CAAEc,CAAC,CAACC,MAAM,CAACb,KAAK,CAAE,CACnEiD,WAAW,CAAC,oCAAoC,CAChDP,SAAS,CAAC,cAAc,CACzB,CAAC,EACC,CAAC,cAENnK,KAAA,QAAKmK,SAAS,CAAC,cAAc,CAAAC,QAAA,eAC3BtK,IAAA,UAAOqK,SAAS,CAAC,cAAc,CAAAC,QAAA,CAAC,WAAS,CAAO,CAAC,cACjDtK,IAAA,UACEuC,IAAI,CAAC,MAAM,CACXoF,KAAK,CAAE5F,OAAO,CAACG,QAAS,CACxBuI,QAAQ,CAAGlC,CAAC,EAAKd,kBAAkB,CAAC,UAAU,CAAEc,CAAC,CAACC,MAAM,CAACb,KAAK,CAAE,CAChE0C,SAAS,CAAC,cAAc,CACzB,CAAC,EACC,CAAC,cAENnK,KAAA,QAAKmK,SAAS,CAAC,cAAc,CAAAC,QAAA,eAC3BtK,IAAA,UAAOqK,SAAS,CAAC,cAAc,CAAAC,QAAA,CAAC,SAAO,CAAO,CAAC,cAC/CtK,IAAA,UACEuC,IAAI,CAAC,MAAM,CACXoF,KAAK,CAAE5F,OAAO,CAACI,MAAO,CACtBsI,QAAQ,CAAGlC,CAAC,EAAKd,kBAAkB,CAAC,QAAQ,CAAEc,CAAC,CAACC,MAAM,CAACb,KAAK,CAAE,CAC9D0C,SAAS,CAAC,cAAc,CACzB,CAAC,EACC,CAAC,cAENnK,KAAA,QAAKmK,SAAS,CAAC,cAAc,CAAAC,QAAA,eAC3BtK,IAAA,UAAOqK,SAAS,CAAC,cAAc,CAAAC,QAAA,CAAC,cAAY,CAAO,CAAC,cACpDpK,KAAA,QAAKmK,SAAS,CAAC,cAAc,CAAAC,QAAA,eAC3BtK,IAAA,UACEuC,IAAI,CAAC,QAAQ,CACboF,KAAK,CAAE5F,OAAO,CAACK,UAAW,CAC1BqI,QAAQ,CAAGlC,CAAC,EAAKd,kBAAkB,CAAC,YAAY,CAAEc,CAAC,CAACC,MAAM,CAACb,KAAK,CAAE,CAClEiD,WAAW,CAAC,KAAK,CACjBP,SAAS,CAAC,2BAA2B,CACrCQ,IAAI,CAAC,MAAM,CACZ,CAAC,cACF7K,IAAA,SAAMqK,SAAS,CAAC,kBAAkB,CAAAC,QAAA,CAAC,GAAC,CAAM,CAAC,cAC3CtK,IAAA,UACEuC,IAAI,CAAC,QAAQ,CACboF,KAAK,CAAE5F,OAAO,CAACM,QAAS,CACxBoI,QAAQ,CAAGlC,CAAC,EAAKd,kBAAkB,CAAC,UAAU,CAAEc,CAAC,CAACC,MAAM,CAACb,KAAK,CAAE,CAChEiD,WAAW,CAAC,KAAK,CACjBP,SAAS,CAAC,2BAA2B,CACrCQ,IAAI,CAAC,MAAM,CACZ,CAAC,EACC,CAAC,EACH,CAAC,cAEN7K,IAAA,QAAKqK,SAAS,CAAC,cAAc,CAAAC,QAAA,cAC3BtK,IAAA,WAAQuK,OAAO,CAAElC,YAAa,CAACgC,SAAS,CAAC,0BAA0B,CAAAC,QAAA,CAAC,eAEpE,CAAQ,CAAC,CACN,CAAC,EACH,CAAC,EACH,CAAC,cAGNpK,KAAA,QAAKmK,SAAS,CAAC,8BAA8B,CAAAC,QAAA,eAC3CpK,KAAA,UAAOmK,SAAS,CAAC,oBAAoB,CAAAC,QAAA,eACnCtK,IAAA,UAAAsK,QAAA,cACEpK,KAAA,OAAAoK,QAAA,eACEtK,IAAA,OAAIqK,SAAS,CAAC,cAAc,CAAAC,QAAA,cAC1BtK,IAAA,UACEuC,IAAI,CAAC,UAAU,CACfoI,OAAO,CAAEnI,oBAAoB,CAAC4F,IAAI,GAAKzB,mBAAmB,CAAC3D,MAAM,EAAI2D,mBAAmB,CAAC3D,MAAM,CAAG,CAAE,CACpGyH,QAAQ,CAAEtC,eAAgB,CAC1BkC,SAAS,CAAC,gBAAgB,CAC3B,CAAC,CACA,CAAC,cACLnK,KAAA,OACEmK,SAAS,aAAAS,MAAA,CAAcnJ,SAAS,GAAK,cAAc,CAAG,QAAQ,CAAG,EAAE,CAAG,CACtE4I,OAAO,CAAEA,CAAA,GAAMhD,UAAU,CAAC,cAAc,CAAE,CAAA+C,QAAA,EAC3C,MAEC,CAAC3I,SAAS,GAAK,cAAc,eAC3B3B,IAAA,SAAMqK,SAAS,CAAC,YAAY,CAAAC,QAAA,CACzBzI,aAAa,GAAK,KAAK,CAAG,GAAG,CAAG,GAAG,CAChC,CACP,EACC,CAAC,cACL3B,KAAA,OACEmK,SAAS,aAAAS,MAAA,CAAcnJ,SAAS,GAAK,aAAa,CAAG,QAAQ,CAAG,EAAE,CAAG,CACrE4I,OAAO,CAAEA,CAAA,GAAMhD,UAAU,CAAC,aAAa,CAAE,CAAA+C,QAAA,EAC1C,SAEC,CAAC3I,SAAS,GAAK,aAAa,eAC1B3B,IAAA,SAAMqK,SAAS,CAAC,YAAY,CAAAC,QAAA,CACzBzI,aAAa,GAAK,KAAK,CAAG,GAAG,CAAG,GAAG,CAChC,CACP,EACC,CAAC,cACL3B,KAAA,OACEmK,SAAS,aAAAS,MAAA,CAAcnJ,SAAS,GAAK,aAAa,CAAG,QAAQ,CAAG,EAAE,CAAG,CACrE4I,OAAO,CAAEA,CAAA,GAAMhD,UAAU,CAAC,aAAa,CAAE,CAAA+C,QAAA,EAC1C,aAEC,CAAC3I,SAAS,GAAK,aAAa,eAC1B3B,IAAA,SAAMqK,SAAS,CAAC,YAAY,CAAAC,QAAA,CACzBzI,aAAa,GAAK,KAAK,CAAG,GAAG,CAAG,GAAG,CAChC,CACP,EACC,CAAC,cACL7B,IAAA,OAAIqK,SAAS,CAAC,YAAY,CAAAC,QAAA,CAAC,OAAK,CAAI,CAAC,cACrCtK,IAAA,OAAIqK,SAAS,CAAC,YAAY,CAAAC,QAAA,CAAC,QAAM,CAAI,CAAC,cACtCpK,KAAA,OACEmK,SAAS,wBAAAS,MAAA,CAAyBnJ,SAAS,GAAK,SAAS,CAAG,QAAQ,CAAG,EAAE,CAAG,CAC5E4I,OAAO,CAAEA,CAAA,GAAMhD,UAAU,CAAC,SAAS,CAAE,CAAA+C,QAAA,EACtC,SAEC,CAAC3I,SAAS,GAAK,SAAS,eACtB3B,IAAA,SAAMqK,SAAS,CAAC,YAAY,CAAAC,QAAA,CACzBzI,aAAa,GAAK,KAAK,CAAG,GAAG,CAAG,GAAG,CAChC,CACP,EACC,CAAC,cACL7B,IAAA,OAAAsK,QAAA,CAAI,WAAS,CAAI,CAAC,EAChB,CAAC,CACA,CAAC,cACRtK,IAAA,UAAAsK,QAAA,CACG3D,mBAAmB,CAACpB,GAAG,CAAC,CAAC2E,WAAW,CAAEa,MAAM,gBAC3C7K,KAAA,OAEEmK,SAAS,8BAAAS,MAAA,CACLtI,oBAAoB,CAACgD,GAAG,CAAC0E,WAAW,CAAC1G,EAAE,CAAC,CAAG,UAAU,CAAG,EAAE,+BAAAsH,MAAA,CAC1Db,WAAW,CAACC,WAAW,CAAC,CAAG,WAAW,CAAG,EAAE,4BAC7C,CACFc,KAAK,CAAEd,WAAW,CAAC5H,WAAY,CAAAgI,QAAA,eAE/BtK,IAAA,OAAIqK,SAAS,CAAC,cAAc,CAAAC,QAAA,cAC1BtK,IAAA,UACEuC,IAAI,CAAC,UAAU,CACfoI,OAAO,CAAEnI,oBAAoB,CAACgD,GAAG,CAAC0E,WAAW,CAAC1G,EAAE,CAAE,CAClDiH,QAAQ,CAAEA,CAAA,GAAM3C,uBAAuB,CAACoC,WAAW,CAAC1G,EAAE,CAAE,CACxD6G,SAAS,CAAC,gBAAgB,CAC3B,CAAC,CACA,CAAC,cACLrK,IAAA,OAAIqK,SAAS,CAAC,UAAU,CAAAC,QAAA,cACtBpK,KAAA,QAAKmK,SAAS,CAAC,cAAc,CAAAC,QAAA,EAC1Bb,UAAU,CAACS,WAAW,CAAC5F,YAAY,CAAC,CACpC4F,WAAW,CAACe,IAAI,eACfjL,IAAA,SAAMqK,SAAS,CAAC,cAAc,CAAAC,QAAA,CAAEJ,WAAW,CAACe,IAAI,CAAO,CACxD,EACE,CAAC,CACJ,CAAC,cACLjL,IAAA,OAAIqK,SAAS,CAAC,aAAa,CAAAC,QAAA,cACzBpK,KAAA,QAAKmK,SAAS,CAAC,cAAc,CAAAC,QAAA,EAC1BN,cAAc,CAACE,WAAW,CAACjI,SAAS,CAAC,CACrCgI,WAAW,CAACC,WAAW,CAAC,eACvBlK,IAAA,SAAMqK,SAAS,CAAC,iBAAiB,CAAAC,QAAA,CAAC,WAAS,CAAM,CAClD,EACE,CAAC,CACJ,CAAC,cACLtK,IAAA,OAAIqK,SAAS,CAAC,iBAAiB,CAAAC,QAAA,cAC7BtK,IAAA,QAAKqK,SAAS,CAAC,qBAAqB,CAAAC,QAAA,CACjCJ,WAAW,CAAC5H,WAAW,CACrB,CAAC,CACJ,CAAC,cACLtC,IAAA,OAAIqK,SAAS,CAAC,kBAAkB,CAAAC,QAAA,CAC7BJ,WAAW,CAAChF,WAAW,CAAGiE,cAAc,CAACe,WAAW,CAAChF,WAAW,CAAC,CAAG,EAAE,CACrE,CAAC,cACLlF,IAAA,OAAIqK,SAAS,CAAC,mBAAmB,CAAAC,QAAA,CAC9BJ,WAAW,CAAC/E,YAAY,CAAGgE,cAAc,CAACe,WAAW,CAAC/E,YAAY,CAAC,CAAG,EAAE,CACvE,CAAC,cACLnF,IAAA,OAAIqK,SAAS,CAAC,oBAAoB,CAAAC,QAAA,CAC/BnB,cAAc,CAACe,WAAW,CAACnE,OAAO,CAAC,CAClC,CAAC,cACL/F,IAAA,OAAIqK,SAAS,CAAC,eAAe,CAAAC,QAAA,CAC1BJ,WAAW,CAACvF,SAAS,EAAI,EAAE,CAC1B,CAAC,GA/CAuF,WAAW,CAAC1G,EAgDf,CACL,CAAC,CACG,CAAC,EACH,CAAC,CAEPmD,mBAAmB,CAAC3D,MAAM,GAAK,CAAC,eAC/B9C,KAAA,QAAKmK,SAAS,CAAC,iBAAiB,CAAAC,QAAA,eAC9BtK,IAAA,QAAKqK,SAAS,CAAC,sBAAsB,CAAAC,QAAA,CAAC,cAAE,CAAK,CAAC,cAC9CtK,IAAA,OAAAsK,QAAA,CAAI,uBAAqB,CAAI,CAAC,cAC9BtK,IAAA,MAAAsK,QAAA,CACGxG,6BAA6B,CAACd,MAAM,GAAK,CAAC,EAAIrC,YAAY,CAACqC,MAAM,GAAK,CAAC,CACpE,yCAAyC,CACzC,6CAA6C,CAChD,CAAC,CACHc,6BAA6B,CAACd,MAAM,GAAK,CAAC,EAAIrC,YAAY,CAACqC,MAAM,CAAG,CAAC,eACpEhD,IAAA,WAAQuK,OAAO,CAAElC,YAAa,CAACgC,SAAS,CAAC,iBAAiB,CAAAC,QAAA,CAAC,eAE3D,CAAQ,CACT,EACE,CACN,EACE,CAAC,CAGLhE,UAAU,CAAG,CAAC,eACbpG,KAAA,QAAKmK,SAAS,CAAC,yBAAyB,CAAAC,QAAA,eACtCpK,KAAA,QAAKmK,SAAS,CAAC,iBAAiB,CAAAC,QAAA,eAC9BpK,KAAA,SAAAoK,QAAA,EAAM,UACI,CAAC9D,UAAU,CAAG,CAAC,CAAC,GAAC,CAACxB,IAAI,CAAC0B,GAAG,CAACD,QAAQ,CAAE3C,6BAA6B,CAACd,MAAM,CAAC,CAAC,MAAI,CAACc,6BAA6B,CAACd,MAAM,CAAC,eAC/H,EAAM,CAAC,cACP9C,KAAA,QAAKmK,SAAS,CAAC,gBAAgB,CAAAC,QAAA,eAC7BtK,IAAA,UAAAsK,QAAA,CAAO,iBAAe,CAAO,CAAC,cAC9BtK,IAAA,WACE2H,KAAK,CAAElG,YAAa,CACpBgJ,QAAQ,CAAGlC,CAAC,EAAK7G,eAAe,CAACwJ,MAAM,CAAC3C,CAAC,CAACC,MAAM,CAACb,KAAK,CAAC,CAAE,CACzD0C,SAAS,CAAC,kBAAkB,CAAAC,QAAA,CAE3BnK,sBAAsB,CAACoF,GAAG,CAAC6C,IAAI,eAC9BpI,IAAA,WAAmB2H,KAAK,CAAES,IAAK,CAAAkC,QAAA,CAAElC,IAAI,EAAxBA,IAAiC,CAC/C,CAAC,CACI,CAAC,EACN,CAAC,EACH,CAAC,cACNlI,KAAA,QAAKmK,SAAS,CAAC,qBAAqB,CAAAC,QAAA,eAClCtK,IAAA,WACEuK,OAAO,CAAEA,CAAA,GAAM/I,cAAc,CAAC,CAAC,CAAE,CACjC2J,QAAQ,CAAE5J,WAAW,GAAK,CAAE,CAC5B8I,SAAS,CAAC,0BAA0B,CAAAC,QAAA,CACrC,OAED,CAAQ,CAAC,cACTtK,IAAA,WACEuK,OAAO,CAAEA,CAAA,GAAM/I,cAAc,CAACD,WAAW,CAAG,CAAC,CAAE,CAC/C4J,QAAQ,CAAE5J,WAAW,GAAK,CAAE,CAC5B8I,SAAS,CAAC,0BAA0B,CAAAC,QAAA,CACrC,UAED,CAAQ,CAAC,cACTtK,IAAA,QAAKqK,SAAS,CAAC,kBAAkB,CAAAC,QAAA,CAC9Bc,KAAK,CAACC,IAAI,CAAC,CAAErI,MAAM,CAAEgC,IAAI,CAAC0B,GAAG,CAAC,CAAC,CAAEJ,UAAU,CAAE,CAAC,CAAE,CAACgF,CAAC,CAAEC,CAAC,GAAK,CACzD,KAAM,CAAAC,UAAU,CAAGxG,IAAI,CAACyG,GAAG,CAAC,CAAC,CAAEzG,IAAI,CAAC0B,GAAG,CAACJ,UAAU,CAAG,CAAC,CAAE/E,WAAW,CAAG,CAAC,CAAC,CAAC,CAAGgK,CAAC,CAC7E,mBACEvL,IAAA,WAEEuK,OAAO,CAAEA,CAAA,GAAM/I,cAAc,CAACgK,UAAU,CAAE,CAC1CnB,SAAS,6BAAAS,MAAA,CAA8BvJ,WAAW,GAAKiK,UAAU,CAAG,QAAQ,CAAG,EAAE,CAAG,CAAAlB,QAAA,CAEnFkB,UAAU,EAJNA,UAKC,CAAC,CAEb,CAAC,CAAC,CACC,CAAC,cACNxL,IAAA,WACEuK,OAAO,CAAEA,CAAA,GAAM/I,cAAc,CAACD,WAAW,CAAG,CAAC,CAAE,CAC/C4J,QAAQ,CAAE5J,WAAW,GAAK+E,UAAW,CACrC+D,SAAS,CAAC,0BAA0B,CAAAC,QAAA,CACrC,MAED,CAAQ,CAAC,cACTtK,IAAA,WACEuK,OAAO,CAAEA,CAAA,GAAM/I,cAAc,CAAC8E,UAAU,CAAE,CAC1C6E,QAAQ,CAAE5J,WAAW,GAAK+E,UAAW,CACrC+D,SAAS,CAAC,0BAA0B,CAAAC,QAAA,CACrC,MAED,CAAQ,CAAC,EACN,CAAC,EACH,CACN,cAGDtK,IAAA,QAAKqK,SAAS,CAAC,oBAAoB,CAAAC,QAAA,cACjCpK,KAAA,YAAAoK,QAAA,eACEtK,IAAA,YAAAsK,QAAA,CAAS,oBAAkB,CAAS,CAAC,cACrCpK,KAAA,QAAKmK,SAAS,CAAC,gBAAgB,CAAAC,QAAA,eAC7BpK,KAAA,QAAKmK,SAAS,CAAC,eAAe,CAAAC,QAAA,eAC5BtK,IAAA,QAAAsK,QAAA,CAAK,QAAC,CAAK,CAAC,IAAC,cAAAtK,IAAA,SAAAsK,QAAA,CAAM,eAAa,CAAM,CAAC,EACpC,CAAC,cACNpK,KAAA,QAAKmK,SAAS,CAAC,eAAe,CAAAC,QAAA,eAC5BtK,IAAA,QAAAsK,QAAA,CAAK,QAAC,CAAK,CAAC,IAAC,cAAAtK,IAAA,SAAAsK,QAAA,CAAM,WAAS,CAAM,CAAC,EAChC,CAAC,cACNpK,KAAA,QAAKmK,SAAS,CAAC,eAAe,CAAAC,QAAA,eAC5BtK,IAAA,QAAAsK,QAAA,CAAK,MAAI,CAAK,CAAC,IAAC,cAAAtK,IAAA,SAAAsK,QAAA,CAAM,YAAU,CAAM,CAAC,EACpC,CAAC,cACNpK,KAAA,QAAKmK,SAAS,CAAC,eAAe,CAAAC,QAAA,eAC5BtK,IAAA,QAAAsK,QAAA,CAAK,KAAG,CAAK,CAAC,IAAC,cAAAtK,IAAA,SAAAsK,QAAA,CAAM,WAAS,CAAM,CAAC,EAClC,CAAC,cACNpK,KAAA,QAAKmK,SAAS,CAAC,eAAe,CAAAC,QAAA,eAC5BtK,IAAA,QAAAsK,QAAA,CAAK,QAAM,CAAK,CAAC,IAAC,cAAAtK,IAAA,SAAAsK,QAAA,CAAM,cAAY,CAAM,CAAC,EACxC,CAAC,cACNpK,KAAA,QAAKmK,SAAS,CAAC,eAAe,CAAAC,QAAA,eAC5BtK,IAAA,QAAAsK,QAAA,CAAK,QAAM,CAAK,CAAC,IAAC,cAAAtK,IAAA,SAAAsK,QAAA,CAAM,SAAO,CAAM,CAAC,EACnC,CAAC,cACNpK,KAAA,QAAKmK,SAAS,CAAC,eAAe,CAAAC,QAAA,eAC5BtK,IAAA,QAAAsK,QAAA,CAAK,QAAM,CAAK,CAAC,IAAC,cAAAtK,IAAA,SAAAsK,QAAA,CAAM,YAAU,CAAM,CAAC,EACtC,CAAC,cACNpK,KAAA,QAAKmK,SAAS,CAAC,eAAe,CAAAC,QAAA,eAC5BtK,IAAA,QAAAsK,QAAA,CAAK,KAAG,CAAK,CAAC,IAAC,cAAAtK,IAAA,SAAAsK,QAAA,CAAM,iBAAe,CAAM,CAAC,EACxC,CAAC,EACH,CAAC,EACC,CAAC,CACP,CAAC,EACH,CAAC,CAEZ,CACF,CAAC,CAED,mBACEpK,KAAA,QAAKmK,SAAS,CAAC,cAAc,CAAAC,QAAA,eAC3BpK,KAAA,QAAKmK,SAAS,CAAC,qBAAqB,CAAAC,QAAA,eAClCpK,KAAA,QAAKmK,SAAS,CAAC,4BAA4B,CAAAC,QAAA,eACzCtK,IAAA,OAAIqK,SAAS,CAAC,oBAAoB,CAAAC,QAAA,CAAC,cAAY,CAAI,CAAC,cACpDpK,KAAA,QAAKmK,SAAS,CAAC,oBAAoB,CAAAC,QAAA,eACjCpK,KAAA,SAAMmK,SAAS,CAAC,WAAW,CAAAC,QAAA,EAAC,SACnB,cAAAtK,IAAA,WAAAsK,QAAA,CAAS3J,YAAY,CAACqC,MAAM,CAAC0I,cAAc,CAAC,CAAC,CAAS,CAAC,EAC1D,CAAC,cACPxL,KAAA,SAAMmK,SAAS,CAAC,WAAW,CAAAC,QAAA,EAAC,YAChB,cAAAtK,IAAA,WAAAsK,QAAA,CAASxG,6BAA6B,CAACd,MAAM,CAAC0I,cAAc,CAAC,CAAC,CAAS,CAAC,EAC9E,CAAC,CACN3K,eAAe,CAACiC,MAAM,CAAG,CAAC,eACzB9C,KAAA,SAAMmK,SAAS,CAAC,0BAA0B,CAAAC,QAAA,EAAC,cAC7B,cAAAtK,IAAA,WAAAsK,QAAA,CAASvJ,eAAe,CAACuE,IAAI,CAAC,CAAC,CAACtC,MAAM,CAAS,CAAC,EACxD,CACP,EACE,CAAC,EACH,CAAC,cACN9C,KAAA,QAAKmK,SAAS,CAAC,sBAAsB,CAAAC,QAAA,eACnCpK,KAAA,WAAQqK,OAAO,CAAExH,QAAS,CAACsH,SAAS,CAAC,0BAA0B,CAACc,QAAQ,CAAE9J,UAAW,CAAAiJ,QAAA,eACnFpK,KAAA,QAAKyL,KAAK,CAAC,IAAI,CAACC,MAAM,CAAC,IAAI,CAACC,OAAO,CAAC,WAAW,CAACC,IAAI,CAAC,MAAM,CAACC,MAAM,CAAC,cAAc,CAACC,WAAW,CAAC,GAAG,CAAA1B,QAAA,eAC/FtK,IAAA,aAAUiM,MAAM,CAAC,kBAAkB,CAAW,CAAC,cAC/CjM,IAAA,aAAUiM,MAAM,CAAC,gBAAgB,CAAW,CAAC,cAC7CjM,IAAA,SAAMkM,CAAC,CAAC,qEAAqE,CAAO,CAAC,EAClF,CAAC,CACL7K,UAAU,CAAG,eAAe,CAAG,SAAS,EACnC,CAAC,CACRA,UAAU,eACTnB,KAAA,SAAMmK,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eACjCtK,IAAA,QAAKqK,SAAS,CAAC,iBAAiB,CAAM,CAAC,+BAEzC,EAAM,CACP,CACA7H,oBAAoB,CAAC4F,IAAI,CAAG,CAAC,eAC5BlI,KAAA,SAAMmK,SAAS,CAAC,iBAAiB,CAAAC,QAAA,EAC9B9H,oBAAoB,CAAC4F,IAAI,CAAC,WAC7B,EAAM,CACP,EACE,CAAC,EACH,CAAC,cAGNlI,KAAA,QAAKmK,SAAS,CAAC,kBAAkB,CAAAC,QAAA,eAC/BpK,KAAA,WACEmK,SAAS,eAAAS,MAAA,CAAgBrK,SAAS,GAAK,KAAK,CAAG,QAAQ,CAAG,EAAE,CAAG,CAC/D8J,OAAO,CAAEA,CAAA,GAAM7J,YAAY,CAAC,KAAK,CAAE,CAAA4J,QAAA,eAEnCpK,KAAA,QAAKyL,KAAK,CAAC,IAAI,CAACC,MAAM,CAAC,IAAI,CAACC,OAAO,CAAC,WAAW,CAACC,IAAI,CAAC,MAAM,CAACC,MAAM,CAAC,cAAc,CAACC,WAAW,CAAC,GAAG,CAAA1B,QAAA,eAC/FtK,IAAA,SAAMkM,CAAC,CAAC,SAAS,CAAE,CAAC,cACpBlM,IAAA,SAAMkM,CAAC,CAAC,UAAU,CAAE,CAAC,cACrBlM,IAAA,SAAMkM,CAAC,CAAC,UAAU,CAAE,CAAC,cACrBlM,IAAA,WAAQmM,EAAE,CAAC,GAAG,CAACC,EAAE,CAAC,GAAG,CAACC,CAAC,CAAC,GAAG,CAAE,CAAC,cAC9BrM,IAAA,WAAQmM,EAAE,CAAC,GAAG,CAACC,EAAE,CAAC,IAAI,CAACC,CAAC,CAAC,GAAG,CAAE,CAAC,cAC/BrM,IAAA,WAAQmM,EAAE,CAAC,GAAG,CAACC,EAAE,CAAC,IAAI,CAACC,CAAC,CAAC,GAAG,CAAE,CAAC,EAC5B,CAAC,mBAER,EAAQ,CAAC,cACTnM,KAAA,WACEmK,SAAS,eAAAS,MAAA,CAAgBrK,SAAS,GAAK,gBAAgB,CAAG,QAAQ,CAAG,EAAE,CAAG,CAC1E8J,OAAO,CAAEA,CAAA,GAAM7J,YAAY,CAAC,gBAAgB,CAAE,CAAA4J,QAAA,eAE9CtK,IAAA,QAAK2L,KAAK,CAAC,IAAI,CAACC,MAAM,CAAC,IAAI,CAACC,OAAO,CAAC,WAAW,CAACC,IAAI,CAAC,MAAM,CAACC,MAAM,CAAC,cAAc,CAACC,WAAW,CAAC,GAAG,CAAA1B,QAAA,cAC/FtK,IAAA,SAAMkM,CAAC,CAAC,2BAA2B,CAAE,CAAC,CACnC,CAAC,iBAER,EAAQ,CAAC,EACN,CAAC,cAGNlM,IAAA,QAAKqK,SAAS,CAAC,aAAa,CAAAC,QAAA,CACzBE,gBAAgB,CAAC,CAAC,CAChB,CAAC,EACH,CAAC,CAEV,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}