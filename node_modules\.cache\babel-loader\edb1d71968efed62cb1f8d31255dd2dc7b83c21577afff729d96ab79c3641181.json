{"ast": null, "code": "/**\n * @license\n * Copyright 2019 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { getGlslDifferences } from './glsl_version';\nimport { useShapeUniforms } from './gpgpu_math';\nexport class Im2ColPackedProgram {\n  constructor(outputShape, convInfo) {\n    this.variableNames = ['A'];\n    this.packedInputs = true;\n    this.packedOutput = true;\n    this.customUniforms = [{\n      name: 'inputShape',\n      type: 'ivec4'\n    }, {\n      name: 'pad',\n      type: 'ivec2'\n    }, {\n      name: 'stride',\n      type: 'ivec2'\n    }, {\n      name: 'dilation',\n      type: 'ivec2'\n    }, {\n      name: 'inChannels',\n      type: 'int'\n    }, {\n      name: 'itemsPerBlockRow',\n      type: 'int'\n    }, {\n      name: 'outWidth',\n      type: 'int'\n    }];\n    this.outputShape = outputShape;\n    this.enableShapeUniforms = useShapeUniforms(this.outputShape.length);\n    const {\n      dataFormat\n    } = convInfo;\n    const glsl = getGlslDifferences();\n    const isChannelsLast = dataFormat === 'channelsLast';\n    const rowDim = isChannelsLast ? 1 : 2;\n    const colDim = isChannelsLast ? 2 : 3;\n    const boundsCheckingSnippet = this.enableShapeUniforms ? 'if(blockIndex < outShape[2] && pos < outShape[1]) {' : \"if(blockIndex < \".concat(outputShape[2], \" && pos < \").concat(outputShape[1], \") {\");\n    let unrolled = \"\";\n    for (let row = 0; row <= 1; row++) {\n      for (let col = 0; col <= 1; col++) {\n        unrolled += \"\\n          blockIndex = rc.z + \".concat(col, \";\\n          pos = rc.y + \").concat(row, \";\\n\\n          \").concat(boundsCheckingSnippet, \"\\n            offsetY = int(blockIndex / outWidth) * stride[0] - pad[0];\\n            d0 = offsetY + dilation[0] * (pos / itemsPerBlockRow);\\n\\n            if(d0 < inputShape[\").concat(rowDim, \"] && d0 >= 0) {\\n              // Use custom imod instead mod. On Intel GPU, mod may generate\\n              // unexpected value.\\n              // https://github.com/tensorflow/tfjs/issues/5447\\n              offsetX = imod(blockIndex, outWidth) * stride[1] - pad[1];\\n              d1 = offsetX + dilation[1] * (imod(pos, itemsPerBlockRow) /\\n                  inChannels);\\n\\n              if(d1 < inputShape[\").concat(colDim, \"] && d1 >= 0) {\\n\\n                ch = imod(pos, inChannels);\\n\\n                if (\").concat(isChannelsLast, \") {\\n                  innerDims = vec2(d1, ch);\\n                  result[\").concat(row * 2 + col, \"] = getChannel(\\n                    getA(rc.x, d0, int(innerDims.x),\\n                    int(innerDims.y)), innerDims);\\n                } else {\\n                  innerDims = vec2(d0, d1);\\n                  result[\").concat(row * 2 + col, \"] = getChannel(\\n                    getA(rc.x, ch, int(innerDims.x),\\n                    int(innerDims.y)), innerDims);\\n                }\\n              }\\n            }\\n          }\\n        \");\n      }\n    }\n    this.userCode = \"\\n      void main() {\\n        ivec3 rc = getOutputCoords();\\n\\n        vec4 result = vec4(0);\\n\\n        int blockIndex, pos, offsetY, d0, offsetX, d1, ch;\\n        vec2 innerDims;\\n\\n        \".concat(unrolled, \"\\n\\n        \").concat(glsl.output, \" = result;\\n      }\\n    \");\n  }\n}", "map": {"version": 3, "names": ["getGlslDifferences", "useShapeUniforms", "Im2ColPackedProgram", "constructor", "outputShape", "convInfo", "variableNames", "packedInputs", "packedOutput", "customUniforms", "name", "type", "enableShapeUniforms", "length", "dataFormat", "glsl", "isChannelsLast", "row<PERSON><PERSON>", "col<PERSON><PERSON>", "boundsCheckingSnippet", "concat", "unrolled", "row", "col", "userCode", "output"], "sources": ["C:\\tfjs-backend-webgl\\src\\im2col_packed_gpu.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2019 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {backend_util} from '@tensorflow/tfjs-core';\nimport {getGlslDifferences} from './glsl_version';\nimport {GPGPUProgram, useShapeUniforms} from './gpgpu_math';\n\nexport class Im2ColPackedProgram implements GPGPUProgram {\n  variableNames = ['A'];\n  packedInputs = true;\n  packedOutput = true;\n  outputShape: number[];\n  userCode: string;\n  enableShapeUniforms: boolean;\n  customUniforms = [\n    {name: 'inputShape', type: 'ivec4' as const },\n    {name: 'pad', type: 'ivec2' as const },\n    {name: 'stride', type: 'ivec2' as const },\n    {name: 'dilation', type: 'ivec2' as const },\n    {name: 'inChannels', type: 'int' as const },\n    {name: 'itemsPerBlockRow', type: 'int' as const },\n    {name: 'outWidth', type: 'int' as const },\n  ];\n\n  constructor(outputShape: number[], convInfo: backend_util.Conv2DInfo) {\n    this.outputShape = outputShape;\n    this.enableShapeUniforms = useShapeUniforms(this.outputShape.length);\n    const {dataFormat} = convInfo;\n    const glsl = getGlslDifferences();\n    const isChannelsLast = dataFormat === 'channelsLast';\n    const rowDim = isChannelsLast ? 1 : 2;\n    const colDim = isChannelsLast ? 2 : 3;\n\n    const boundsCheckingSnippet = this.enableShapeUniforms ?\n        'if(blockIndex < outShape[2] && pos < outShape[1]) {' :\n        `if(blockIndex < ${outputShape[2]} && pos < ${outputShape[1]}) {`;\n    let unrolled = ``;\n\n    for (let row = 0; row <= 1; row++) {\n      for (let col = 0; col <= 1; col++) {\n        unrolled += `\n          blockIndex = rc.z + ${col};\n          pos = rc.y + ${row};\n\n          ${boundsCheckingSnippet}\n            offsetY = int(blockIndex / outWidth) * stride[0] - pad[0];\n            d0 = offsetY + dilation[0] * (pos / itemsPerBlockRow);\n\n            if(d0 < inputShape[${rowDim}] && d0 >= 0) {\n              // Use custom imod instead mod. On Intel GPU, mod may generate\n              // unexpected value.\n              // https://github.com/tensorflow/tfjs/issues/5447\n              offsetX = imod(blockIndex, outWidth) * stride[1] - pad[1];\n              d1 = offsetX + dilation[1] * (imod(pos, itemsPerBlockRow) /\n                  inChannels);\n\n              if(d1 < inputShape[${colDim}] && d1 >= 0) {\n\n                ch = imod(pos, inChannels);\n\n                if (${isChannelsLast}) {\n                  innerDims = vec2(d1, ch);\n                  result[${row * 2 + col}] = getChannel(\n                    getA(rc.x, d0, int(innerDims.x),\n                    int(innerDims.y)), innerDims);\n                } else {\n                  innerDims = vec2(d0, d1);\n                  result[${row * 2 + col}] = getChannel(\n                    getA(rc.x, ch, int(innerDims.x),\n                    int(innerDims.y)), innerDims);\n                }\n              }\n            }\n          }\n        `;\n      }\n    }\n\n    this.userCode = `\n      void main() {\n        ivec3 rc = getOutputCoords();\n\n        vec4 result = vec4(0);\n\n        int blockIndex, pos, offsetY, d0, offsetX, d1, ch;\n        vec2 innerDims;\n\n        ${unrolled}\n\n        ${glsl.output} = result;\n      }\n    `;\n  }\n}\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAkBA,SAAQA,kBAAkB,QAAO,gBAAgB;AACjD,SAAsBC,gBAAgB,QAAO,cAAc;AAE3D,OAAM,MAAOC,mBAAmB;EAiB9BC,YAAYC,WAAqB,EAAEC,QAAiC;IAhBpE,KAAAC,aAAa,GAAG,CAAC,GAAG,CAAC;IACrB,KAAAC,YAAY,GAAG,IAAI;IACnB,KAAAC,YAAY,GAAG,IAAI;IAInB,KAAAC,cAAc,GAAG,CACf;MAACC,IAAI,EAAE,YAAY;MAAEC,IAAI,EAAE;IAAgB,CAAE,EAC7C;MAACD,IAAI,EAAE,KAAK;MAAEC,IAAI,EAAE;IAAgB,CAAE,EACtC;MAACD,IAAI,EAAE,QAAQ;MAAEC,IAAI,EAAE;IAAgB,CAAE,EACzC;MAACD,IAAI,EAAE,UAAU;MAAEC,IAAI,EAAE;IAAgB,CAAE,EAC3C;MAACD,IAAI,EAAE,YAAY;MAAEC,IAAI,EAAE;IAAc,CAAE,EAC3C;MAACD,IAAI,EAAE,kBAAkB;MAAEC,IAAI,EAAE;IAAc,CAAE,EACjD;MAACD,IAAI,EAAE,UAAU;MAAEC,IAAI,EAAE;IAAc,CAAE,CAC1C;IAGC,IAAI,CAACP,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACQ,mBAAmB,GAAGX,gBAAgB,CAAC,IAAI,CAACG,WAAW,CAACS,MAAM,CAAC;IACpE,MAAM;MAACC;IAAU,CAAC,GAAGT,QAAQ;IAC7B,MAAMU,IAAI,GAAGf,kBAAkB,EAAE;IACjC,MAAMgB,cAAc,GAAGF,UAAU,KAAK,cAAc;IACpD,MAAMG,MAAM,GAAGD,cAAc,GAAG,CAAC,GAAG,CAAC;IACrC,MAAME,MAAM,GAAGF,cAAc,GAAG,CAAC,GAAG,CAAC;IAErC,MAAMG,qBAAqB,GAAG,IAAI,CAACP,mBAAmB,GAClD,qDAAqD,sBAAAQ,MAAA,CAClChB,WAAW,CAAC,CAAC,CAAC,gBAAAgB,MAAA,CAAahB,WAAW,CAAC,CAAC,CAAC,QAAK;IACrE,IAAIiB,QAAQ,KAAK;IAEjB,KAAK,IAAIC,GAAG,GAAG,CAAC,EAAEA,GAAG,IAAI,CAAC,EAAEA,GAAG,EAAE,EAAE;MACjC,KAAK,IAAIC,GAAG,GAAG,CAAC,EAAEA,GAAG,IAAI,CAAC,EAAEA,GAAG,EAAE,EAAE;QACjCF,QAAQ,uCAAAD,MAAA,CACgBG,GAAG,gCAAAH,MAAA,CACVE,GAAG,qBAAAF,MAAA,CAEhBD,qBAAqB,qLAAAC,MAAA,CAIAH,MAAM,kaAAAG,MAAA,CAQJF,MAAM,4FAAAE,MAAA,CAInBJ,cAAc,iFAAAI,MAAA,CAETE,GAAG,GAAG,CAAC,GAAGC,GAAG,iOAAAH,MAAA,CAKbE,GAAG,GAAG,CAAC,GAAGC,GAAG,wMAO/B;;;IAIL,IAAI,CAACC,QAAQ,uMAAAJ,MAAA,CASPC,QAAQ,kBAAAD,MAAA,CAERL,IAAI,CAACU,MAAM,8BAEhB;EACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}