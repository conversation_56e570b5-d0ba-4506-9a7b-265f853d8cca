{"ast": null, "code": "'use strict';\n\nconst {\n  hasOwnProperty\n} = Object.prototype;\nconst stringify = configure();\n\n// @ts-expect-error\nstringify.configure = configure;\n// @ts-expect-error\nstringify.stringify = stringify;\n\n// @ts-expect-error\nstringify.default = stringify;\n\n// @ts-expect-error used for named export\nexports.stringify = stringify;\n// @ts-expect-error used for named export\nexports.configure = configure;\nmodule.exports = stringify;\n\n// eslint-disable-next-line no-control-regex\nconst strEscapeSequencesRegExp = /[\\u0000-\\u001f\\u0022\\u005c\\ud800-\\udfff]/;\n\n// Escape C0 control characters, double quotes, the backslash and every code\n// unit with a numeric value in the inclusive range 0xD800 to 0xDFFF.\nfunction strEscape(str) {\n  // Some magic numbers that worked out fine while benchmarking with v8 8.0\n  if (str.length < 5000 && !strEscapeSequencesRegExp.test(str)) {\n    return `\"${str}\"`;\n  }\n  return JSON.stringify(str);\n}\nfunction sort(array, comparator) {\n  // Insertion sort is very efficient for small input sizes, but it has a bad\n  // worst case complexity. Thus, use native array sort for bigger values.\n  if (array.length > 2e2 || comparator) {\n    return array.sort(comparator);\n  }\n  for (let i = 1; i < array.length; i++) {\n    const currentValue = array[i];\n    let position = i;\n    while (position !== 0 && array[position - 1] > currentValue) {\n      array[position] = array[position - 1];\n      position--;\n    }\n    array[position] = currentValue;\n  }\n  return array;\n}\nconst typedArrayPrototypeGetSymbolToStringTag = Object.getOwnPropertyDescriptor(Object.getPrototypeOf(Object.getPrototypeOf(new Int8Array())), Symbol.toStringTag).get;\nfunction isTypedArrayWithEntries(value) {\n  return typedArrayPrototypeGetSymbolToStringTag.call(value) !== undefined && value.length !== 0;\n}\nfunction stringifyTypedArray(array, separator, maximumBreadth) {\n  if (array.length < maximumBreadth) {\n    maximumBreadth = array.length;\n  }\n  const whitespace = separator === ',' ? '' : ' ';\n  let res = `\"0\":${whitespace}${array[0]}`;\n  for (let i = 1; i < maximumBreadth; i++) {\n    res += `${separator}\"${i}\":${whitespace}${array[i]}`;\n  }\n  return res;\n}\nfunction getCircularValueOption(options) {\n  if (hasOwnProperty.call(options, 'circularValue')) {\n    const circularValue = options.circularValue;\n    if (typeof circularValue === 'string') {\n      return `\"${circularValue}\"`;\n    }\n    if (circularValue == null) {\n      return circularValue;\n    }\n    if (circularValue === Error || circularValue === TypeError) {\n      return {\n        toString() {\n          throw new TypeError('Converting circular structure to JSON');\n        }\n      };\n    }\n    throw new TypeError('The \"circularValue\" argument must be of type string or the value null or undefined');\n  }\n  return '\"[Circular]\"';\n}\nfunction getDeterministicOption(options) {\n  let value;\n  if (hasOwnProperty.call(options, 'deterministic')) {\n    value = options.deterministic;\n    if (typeof value !== 'boolean' && typeof value !== 'function') {\n      throw new TypeError('The \"deterministic\" argument must be of type boolean or comparator function');\n    }\n  }\n  return value === undefined ? true : value;\n}\nfunction getBooleanOption(options, key) {\n  let value;\n  if (hasOwnProperty.call(options, key)) {\n    value = options[key];\n    if (typeof value !== 'boolean') {\n      throw new TypeError(`The \"${key}\" argument must be of type boolean`);\n    }\n  }\n  return value === undefined ? true : value;\n}\nfunction getPositiveIntegerOption(options, key) {\n  let value;\n  if (hasOwnProperty.call(options, key)) {\n    value = options[key];\n    if (typeof value !== 'number') {\n      throw new TypeError(`The \"${key}\" argument must be of type number`);\n    }\n    if (!Number.isInteger(value)) {\n      throw new TypeError(`The \"${key}\" argument must be an integer`);\n    }\n    if (value < 1) {\n      throw new RangeError(`The \"${key}\" argument must be >= 1`);\n    }\n  }\n  return value === undefined ? Infinity : value;\n}\nfunction getItemCount(number) {\n  if (number === 1) {\n    return '1 item';\n  }\n  return `${number} items`;\n}\nfunction getUniqueReplacerSet(replacerArray) {\n  const replacerSet = new Set();\n  for (const value of replacerArray) {\n    if (typeof value === 'string' || typeof value === 'number') {\n      replacerSet.add(String(value));\n    }\n  }\n  return replacerSet;\n}\nfunction getStrictOption(options) {\n  if (hasOwnProperty.call(options, 'strict')) {\n    const value = options.strict;\n    if (typeof value !== 'boolean') {\n      throw new TypeError('The \"strict\" argument must be of type boolean');\n    }\n    if (value) {\n      return value => {\n        let message = `Object can not safely be stringified. Received type ${typeof value}`;\n        if (typeof value !== 'function') message += ` (${value.toString()})`;\n        throw new Error(message);\n      };\n    }\n  }\n}\nfunction configure(options) {\n  options = {\n    ...options\n  };\n  const fail = getStrictOption(options);\n  if (fail) {\n    if (options.bigint === undefined) {\n      options.bigint = false;\n    }\n    if (!('circularValue' in options)) {\n      options.circularValue = Error;\n    }\n  }\n  const circularValue = getCircularValueOption(options);\n  const bigint = getBooleanOption(options, 'bigint');\n  const deterministic = getDeterministicOption(options);\n  const comparator = typeof deterministic === 'function' ? deterministic : undefined;\n  const maximumDepth = getPositiveIntegerOption(options, 'maximumDepth');\n  const maximumBreadth = getPositiveIntegerOption(options, 'maximumBreadth');\n  function stringifyFnReplacer(key, parent, stack, replacer, spacer, indentation) {\n    let value = parent[key];\n    if (typeof value === 'object' && value !== null && typeof value.toJSON === 'function') {\n      value = value.toJSON(key);\n    }\n    value = replacer.call(parent, key, value);\n    switch (typeof value) {\n      case 'string':\n        return strEscape(value);\n      case 'object':\n        {\n          if (value === null) {\n            return 'null';\n          }\n          if (stack.indexOf(value) !== -1) {\n            return circularValue;\n          }\n          let res = '';\n          let join = ',';\n          const originalIndentation = indentation;\n          if (Array.isArray(value)) {\n            if (value.length === 0) {\n              return '[]';\n            }\n            if (maximumDepth < stack.length + 1) {\n              return '\"[Array]\"';\n            }\n            stack.push(value);\n            if (spacer !== '') {\n              indentation += spacer;\n              res += `\\n${indentation}`;\n              join = `,\\n${indentation}`;\n            }\n            const maximumValuesToStringify = Math.min(value.length, maximumBreadth);\n            let i = 0;\n            for (; i < maximumValuesToStringify - 1; i++) {\n              const tmp = stringifyFnReplacer(String(i), value, stack, replacer, spacer, indentation);\n              res += tmp !== undefined ? tmp : 'null';\n              res += join;\n            }\n            const tmp = stringifyFnReplacer(String(i), value, stack, replacer, spacer, indentation);\n            res += tmp !== undefined ? tmp : 'null';\n            if (value.length - 1 > maximumBreadth) {\n              const removedKeys = value.length - maximumBreadth - 1;\n              res += `${join}\"... ${getItemCount(removedKeys)} not stringified\"`;\n            }\n            if (spacer !== '') {\n              res += `\\n${originalIndentation}`;\n            }\n            stack.pop();\n            return `[${res}]`;\n          }\n          let keys = Object.keys(value);\n          const keyLength = keys.length;\n          if (keyLength === 0) {\n            return '{}';\n          }\n          if (maximumDepth < stack.length + 1) {\n            return '\"[Object]\"';\n          }\n          let whitespace = '';\n          let separator = '';\n          if (spacer !== '') {\n            indentation += spacer;\n            join = `,\\n${indentation}`;\n            whitespace = ' ';\n          }\n          const maximumPropertiesToStringify = Math.min(keyLength, maximumBreadth);\n          if (deterministic && !isTypedArrayWithEntries(value)) {\n            keys = sort(keys, comparator);\n          }\n          stack.push(value);\n          for (let i = 0; i < maximumPropertiesToStringify; i++) {\n            const key = keys[i];\n            const tmp = stringifyFnReplacer(key, value, stack, replacer, spacer, indentation);\n            if (tmp !== undefined) {\n              res += `${separator}${strEscape(key)}:${whitespace}${tmp}`;\n              separator = join;\n            }\n          }\n          if (keyLength > maximumBreadth) {\n            const removedKeys = keyLength - maximumBreadth;\n            res += `${separator}\"...\":${whitespace}\"${getItemCount(removedKeys)} not stringified\"`;\n            separator = join;\n          }\n          if (spacer !== '' && separator.length > 1) {\n            res = `\\n${indentation}${res}\\n${originalIndentation}`;\n          }\n          stack.pop();\n          return `{${res}}`;\n        }\n      case 'number':\n        return isFinite(value) ? String(value) : fail ? fail(value) : 'null';\n      case 'boolean':\n        return value === true ? 'true' : 'false';\n      case 'undefined':\n        return undefined;\n      case 'bigint':\n        if (bigint) {\n          return String(value);\n        }\n      // fallthrough\n      default:\n        return fail ? fail(value) : undefined;\n    }\n  }\n  function stringifyArrayReplacer(key, value, stack, replacer, spacer, indentation) {\n    if (typeof value === 'object' && value !== null && typeof value.toJSON === 'function') {\n      value = value.toJSON(key);\n    }\n    switch (typeof value) {\n      case 'string':\n        return strEscape(value);\n      case 'object':\n        {\n          if (value === null) {\n            return 'null';\n          }\n          if (stack.indexOf(value) !== -1) {\n            return circularValue;\n          }\n          const originalIndentation = indentation;\n          let res = '';\n          let join = ',';\n          if (Array.isArray(value)) {\n            if (value.length === 0) {\n              return '[]';\n            }\n            if (maximumDepth < stack.length + 1) {\n              return '\"[Array]\"';\n            }\n            stack.push(value);\n            if (spacer !== '') {\n              indentation += spacer;\n              res += `\\n${indentation}`;\n              join = `,\\n${indentation}`;\n            }\n            const maximumValuesToStringify = Math.min(value.length, maximumBreadth);\n            let i = 0;\n            for (; i < maximumValuesToStringify - 1; i++) {\n              const tmp = stringifyArrayReplacer(String(i), value[i], stack, replacer, spacer, indentation);\n              res += tmp !== undefined ? tmp : 'null';\n              res += join;\n            }\n            const tmp = stringifyArrayReplacer(String(i), value[i], stack, replacer, spacer, indentation);\n            res += tmp !== undefined ? tmp : 'null';\n            if (value.length - 1 > maximumBreadth) {\n              const removedKeys = value.length - maximumBreadth - 1;\n              res += `${join}\"... ${getItemCount(removedKeys)} not stringified\"`;\n            }\n            if (spacer !== '') {\n              res += `\\n${originalIndentation}`;\n            }\n            stack.pop();\n            return `[${res}]`;\n          }\n          stack.push(value);\n          let whitespace = '';\n          if (spacer !== '') {\n            indentation += spacer;\n            join = `,\\n${indentation}`;\n            whitespace = ' ';\n          }\n          let separator = '';\n          for (const key of replacer) {\n            const tmp = stringifyArrayReplacer(key, value[key], stack, replacer, spacer, indentation);\n            if (tmp !== undefined) {\n              res += `${separator}${strEscape(key)}:${whitespace}${tmp}`;\n              separator = join;\n            }\n          }\n          if (spacer !== '' && separator.length > 1) {\n            res = `\\n${indentation}${res}\\n${originalIndentation}`;\n          }\n          stack.pop();\n          return `{${res}}`;\n        }\n      case 'number':\n        return isFinite(value) ? String(value) : fail ? fail(value) : 'null';\n      case 'boolean':\n        return value === true ? 'true' : 'false';\n      case 'undefined':\n        return undefined;\n      case 'bigint':\n        if (bigint) {\n          return String(value);\n        }\n      // fallthrough\n      default:\n        return fail ? fail(value) : undefined;\n    }\n  }\n  function stringifyIndent(key, value, stack, spacer, indentation) {\n    switch (typeof value) {\n      case 'string':\n        return strEscape(value);\n      case 'object':\n        {\n          if (value === null) {\n            return 'null';\n          }\n          if (typeof value.toJSON === 'function') {\n            value = value.toJSON(key);\n            // Prevent calling `toJSON` again.\n            if (typeof value !== 'object') {\n              return stringifyIndent(key, value, stack, spacer, indentation);\n            }\n            if (value === null) {\n              return 'null';\n            }\n          }\n          if (stack.indexOf(value) !== -1) {\n            return circularValue;\n          }\n          const originalIndentation = indentation;\n          if (Array.isArray(value)) {\n            if (value.length === 0) {\n              return '[]';\n            }\n            if (maximumDepth < stack.length + 1) {\n              return '\"[Array]\"';\n            }\n            stack.push(value);\n            indentation += spacer;\n            let res = `\\n${indentation}`;\n            const join = `,\\n${indentation}`;\n            const maximumValuesToStringify = Math.min(value.length, maximumBreadth);\n            let i = 0;\n            for (; i < maximumValuesToStringify - 1; i++) {\n              const tmp = stringifyIndent(String(i), value[i], stack, spacer, indentation);\n              res += tmp !== undefined ? tmp : 'null';\n              res += join;\n            }\n            const tmp = stringifyIndent(String(i), value[i], stack, spacer, indentation);\n            res += tmp !== undefined ? tmp : 'null';\n            if (value.length - 1 > maximumBreadth) {\n              const removedKeys = value.length - maximumBreadth - 1;\n              res += `${join}\"... ${getItemCount(removedKeys)} not stringified\"`;\n            }\n            res += `\\n${originalIndentation}`;\n            stack.pop();\n            return `[${res}]`;\n          }\n          let keys = Object.keys(value);\n          const keyLength = keys.length;\n          if (keyLength === 0) {\n            return '{}';\n          }\n          if (maximumDepth < stack.length + 1) {\n            return '\"[Object]\"';\n          }\n          indentation += spacer;\n          const join = `,\\n${indentation}`;\n          let res = '';\n          let separator = '';\n          let maximumPropertiesToStringify = Math.min(keyLength, maximumBreadth);\n          if (isTypedArrayWithEntries(value)) {\n            res += stringifyTypedArray(value, join, maximumBreadth);\n            keys = keys.slice(value.length);\n            maximumPropertiesToStringify -= value.length;\n            separator = join;\n          }\n          if (deterministic) {\n            keys = sort(keys, comparator);\n          }\n          stack.push(value);\n          for (let i = 0; i < maximumPropertiesToStringify; i++) {\n            const key = keys[i];\n            const tmp = stringifyIndent(key, value[key], stack, spacer, indentation);\n            if (tmp !== undefined) {\n              res += `${separator}${strEscape(key)}: ${tmp}`;\n              separator = join;\n            }\n          }\n          if (keyLength > maximumBreadth) {\n            const removedKeys = keyLength - maximumBreadth;\n            res += `${separator}\"...\": \"${getItemCount(removedKeys)} not stringified\"`;\n            separator = join;\n          }\n          if (separator !== '') {\n            res = `\\n${indentation}${res}\\n${originalIndentation}`;\n          }\n          stack.pop();\n          return `{${res}}`;\n        }\n      case 'number':\n        return isFinite(value) ? String(value) : fail ? fail(value) : 'null';\n      case 'boolean':\n        return value === true ? 'true' : 'false';\n      case 'undefined':\n        return undefined;\n      case 'bigint':\n        if (bigint) {\n          return String(value);\n        }\n      // fallthrough\n      default:\n        return fail ? fail(value) : undefined;\n    }\n  }\n  function stringifySimple(key, value, stack) {\n    switch (typeof value) {\n      case 'string':\n        return strEscape(value);\n      case 'object':\n        {\n          if (value === null) {\n            return 'null';\n          }\n          if (typeof value.toJSON === 'function') {\n            value = value.toJSON(key);\n            // Prevent calling `toJSON` again\n            if (typeof value !== 'object') {\n              return stringifySimple(key, value, stack);\n            }\n            if (value === null) {\n              return 'null';\n            }\n          }\n          if (stack.indexOf(value) !== -1) {\n            return circularValue;\n          }\n          let res = '';\n          const hasLength = value.length !== undefined;\n          if (hasLength && Array.isArray(value)) {\n            if (value.length === 0) {\n              return '[]';\n            }\n            if (maximumDepth < stack.length + 1) {\n              return '\"[Array]\"';\n            }\n            stack.push(value);\n            const maximumValuesToStringify = Math.min(value.length, maximumBreadth);\n            let i = 0;\n            for (; i < maximumValuesToStringify - 1; i++) {\n              const tmp = stringifySimple(String(i), value[i], stack);\n              res += tmp !== undefined ? tmp : 'null';\n              res += ',';\n            }\n            const tmp = stringifySimple(String(i), value[i], stack);\n            res += tmp !== undefined ? tmp : 'null';\n            if (value.length - 1 > maximumBreadth) {\n              const removedKeys = value.length - maximumBreadth - 1;\n              res += `,\"... ${getItemCount(removedKeys)} not stringified\"`;\n            }\n            stack.pop();\n            return `[${res}]`;\n          }\n          let keys = Object.keys(value);\n          const keyLength = keys.length;\n          if (keyLength === 0) {\n            return '{}';\n          }\n          if (maximumDepth < stack.length + 1) {\n            return '\"[Object]\"';\n          }\n          let separator = '';\n          let maximumPropertiesToStringify = Math.min(keyLength, maximumBreadth);\n          if (hasLength && isTypedArrayWithEntries(value)) {\n            res += stringifyTypedArray(value, ',', maximumBreadth);\n            keys = keys.slice(value.length);\n            maximumPropertiesToStringify -= value.length;\n            separator = ',';\n          }\n          if (deterministic) {\n            keys = sort(keys, comparator);\n          }\n          stack.push(value);\n          for (let i = 0; i < maximumPropertiesToStringify; i++) {\n            const key = keys[i];\n            const tmp = stringifySimple(key, value[key], stack);\n            if (tmp !== undefined) {\n              res += `${separator}${strEscape(key)}:${tmp}`;\n              separator = ',';\n            }\n          }\n          if (keyLength > maximumBreadth) {\n            const removedKeys = keyLength - maximumBreadth;\n            res += `${separator}\"...\":\"${getItemCount(removedKeys)} not stringified\"`;\n          }\n          stack.pop();\n          return `{${res}}`;\n        }\n      case 'number':\n        return isFinite(value) ? String(value) : fail ? fail(value) : 'null';\n      case 'boolean':\n        return value === true ? 'true' : 'false';\n      case 'undefined':\n        return undefined;\n      case 'bigint':\n        if (bigint) {\n          return String(value);\n        }\n      // fallthrough\n      default:\n        return fail ? fail(value) : undefined;\n    }\n  }\n  function stringify(value, replacer, space) {\n    if (arguments.length > 1) {\n      let spacer = '';\n      if (typeof space === 'number') {\n        spacer = ' '.repeat(Math.min(space, 10));\n      } else if (typeof space === 'string') {\n        spacer = space.slice(0, 10);\n      }\n      if (replacer != null) {\n        if (typeof replacer === 'function') {\n          return stringifyFnReplacer('', {\n            '': value\n          }, [], replacer, spacer, '');\n        }\n        if (Array.isArray(replacer)) {\n          return stringifyArrayReplacer('', value, [], getUniqueReplacerSet(replacer), spacer, '');\n        }\n      }\n      if (spacer.length !== 0) {\n        return stringifyIndent('', value, [], spacer, '');\n      }\n    }\n    return stringifySimple('', value, []);\n  }\n  return stringify;\n}", "map": {"version": 3, "names": ["hasOwnProperty", "Object", "prototype", "stringify", "configure", "default", "exports", "module", "strEscapeSequencesRegExp", "strEscape", "str", "length", "test", "JSON", "sort", "array", "comparator", "i", "currentValue", "position", "typedArrayPrototypeGetSymbolToStringTag", "getOwnPropertyDescriptor", "getPrototypeOf", "Int8Array", "Symbol", "toStringTag", "get", "isTypedArrayWithEntries", "value", "call", "undefined", "stringifyTypedArray", "separator", "maximumBreadth", "whitespace", "res", "getCircularValueOption", "options", "circularValue", "Error", "TypeError", "toString", "getDeterministicOption", "deterministic", "getBooleanOption", "key", "getPositiveIntegerOption", "Number", "isInteger", "RangeError", "Infinity", "getItemCount", "number", "getUniqueReplacerSet", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "replacerSet", "Set", "add", "String", "getStrictOption", "strict", "message", "fail", "bigint", "maximumDepth", "stringifyFnReplacer", "parent", "stack", "replacer", "spacer", "indentation", "toJSON", "indexOf", "join", "originalIndentation", "Array", "isArray", "push", "maximumValuesToStringify", "Math", "min", "tmp", "<PERSON><PERSON><PERSON><PERSON>", "pop", "keys", "<PERSON><PERSON><PERSON><PERSON>", "maximumPropertiesToStringify", "isFinite", "stringifyArrayReplacer", "stringifyIndent", "slice", "stringifySimple", "<PERSON><PERSON><PERSON><PERSON>", "space", "arguments", "repeat"], "sources": ["C:/tmsft/node_modules/safe-stable-stringify/index.js"], "sourcesContent": ["'use strict'\n\nconst { hasOwnProperty } = Object.prototype\n\nconst stringify = configure()\n\n// @ts-expect-error\nstringify.configure = configure\n// @ts-expect-error\nstringify.stringify = stringify\n\n// @ts-expect-error\nstringify.default = stringify\n\n// @ts-expect-error used for named export\nexports.stringify = stringify\n// @ts-expect-error used for named export\nexports.configure = configure\n\nmodule.exports = stringify\n\n// eslint-disable-next-line no-control-regex\nconst strEscapeSequencesRegExp = /[\\u0000-\\u001f\\u0022\\u005c\\ud800-\\udfff]/\n\n// Escape C0 control characters, double quotes, the backslash and every code\n// unit with a numeric value in the inclusive range 0xD800 to 0xDFFF.\nfunction strEscape (str) {\n  // Some magic numbers that worked out fine while benchmarking with v8 8.0\n  if (str.length < 5000 && !strEscapeSequencesRegExp.test(str)) {\n    return `\"${str}\"`\n  }\n  return JSON.stringify(str)\n}\n\nfunction sort (array, comparator) {\n  // Insertion sort is very efficient for small input sizes, but it has a bad\n  // worst case complexity. Thus, use native array sort for bigger values.\n  if (array.length > 2e2 || comparator) {\n    return array.sort(comparator)\n  }\n  for (let i = 1; i < array.length; i++) {\n    const currentValue = array[i]\n    let position = i\n    while (position !== 0 && array[position - 1] > currentValue) {\n      array[position] = array[position - 1]\n      position--\n    }\n    array[position] = currentValue\n  }\n  return array\n}\n\nconst typedArrayPrototypeGetSymbolToStringTag =\n  Object.getOwnPropertyDescriptor(\n    Object.getPrototypeOf(\n      Object.getPrototypeOf(\n        new Int8Array()\n      )\n    ),\n    Symbol.toStringTag\n  ).get\n\nfunction isTypedArrayWithEntries (value) {\n  return typedArrayPrototypeGetSymbolToStringTag.call(value) !== undefined && value.length !== 0\n}\n\nfunction stringifyTypedArray (array, separator, maximumBreadth) {\n  if (array.length < maximumBreadth) {\n    maximumBreadth = array.length\n  }\n  const whitespace = separator === ',' ? '' : ' '\n  let res = `\"0\":${whitespace}${array[0]}`\n  for (let i = 1; i < maximumBreadth; i++) {\n    res += `${separator}\"${i}\":${whitespace}${array[i]}`\n  }\n  return res\n}\n\nfunction getCircularValueOption (options) {\n  if (hasOwnProperty.call(options, 'circularValue')) {\n    const circularValue = options.circularValue\n    if (typeof circularValue === 'string') {\n      return `\"${circularValue}\"`\n    }\n    if (circularValue == null) {\n      return circularValue\n    }\n    if (circularValue === Error || circularValue === TypeError) {\n      return {\n        toString () {\n          throw new TypeError('Converting circular structure to JSON')\n        }\n      }\n    }\n    throw new TypeError('The \"circularValue\" argument must be of type string or the value null or undefined')\n  }\n  return '\"[Circular]\"'\n}\n\nfunction getDeterministicOption (options) {\n  let value\n  if (hasOwnProperty.call(options, 'deterministic')) {\n    value = options.deterministic\n    if (typeof value !== 'boolean' && typeof value !== 'function') {\n      throw new TypeError('The \"deterministic\" argument must be of type boolean or comparator function')\n    }\n  }\n  return value === undefined ? true : value\n}\n\nfunction getBooleanOption (options, key) {\n  let value\n  if (hasOwnProperty.call(options, key)) {\n    value = options[key]\n    if (typeof value !== 'boolean') {\n      throw new TypeError(`The \"${key}\" argument must be of type boolean`)\n    }\n  }\n  return value === undefined ? true : value\n}\n\nfunction getPositiveIntegerOption (options, key) {\n  let value\n  if (hasOwnProperty.call(options, key)) {\n    value = options[key]\n    if (typeof value !== 'number') {\n      throw new TypeError(`The \"${key}\" argument must be of type number`)\n    }\n    if (!Number.isInteger(value)) {\n      throw new TypeError(`The \"${key}\" argument must be an integer`)\n    }\n    if (value < 1) {\n      throw new RangeError(`The \"${key}\" argument must be >= 1`)\n    }\n  }\n  return value === undefined ? Infinity : value\n}\n\nfunction getItemCount (number) {\n  if (number === 1) {\n    return '1 item'\n  }\n  return `${number} items`\n}\n\nfunction getUniqueReplacerSet (replacerArray) {\n  const replacerSet = new Set()\n  for (const value of replacerArray) {\n    if (typeof value === 'string' || typeof value === 'number') {\n      replacerSet.add(String(value))\n    }\n  }\n  return replacerSet\n}\n\nfunction getStrictOption (options) {\n  if (hasOwnProperty.call(options, 'strict')) {\n    const value = options.strict\n    if (typeof value !== 'boolean') {\n      throw new TypeError('The \"strict\" argument must be of type boolean')\n    }\n    if (value) {\n      return (value) => {\n        let message = `Object can not safely be stringified. Received type ${typeof value}`\n        if (typeof value !== 'function') message += ` (${value.toString()})`\n        throw new Error(message)\n      }\n    }\n  }\n}\n\nfunction configure (options) {\n  options = { ...options }\n  const fail = getStrictOption(options)\n  if (fail) {\n    if (options.bigint === undefined) {\n      options.bigint = false\n    }\n    if (!('circularValue' in options)) {\n      options.circularValue = Error\n    }\n  }\n  const circularValue = getCircularValueOption(options)\n  const bigint = getBooleanOption(options, 'bigint')\n  const deterministic = getDeterministicOption(options)\n  const comparator = typeof deterministic === 'function' ? deterministic : undefined\n  const maximumDepth = getPositiveIntegerOption(options, 'maximumDepth')\n  const maximumBreadth = getPositiveIntegerOption(options, 'maximumBreadth')\n\n  function stringifyFnReplacer (key, parent, stack, replacer, spacer, indentation) {\n    let value = parent[key]\n\n    if (typeof value === 'object' && value !== null && typeof value.toJSON === 'function') {\n      value = value.toJSON(key)\n    }\n    value = replacer.call(parent, key, value)\n\n    switch (typeof value) {\n      case 'string':\n        return strEscape(value)\n      case 'object': {\n        if (value === null) {\n          return 'null'\n        }\n        if (stack.indexOf(value) !== -1) {\n          return circularValue\n        }\n\n        let res = ''\n        let join = ','\n        const originalIndentation = indentation\n\n        if (Array.isArray(value)) {\n          if (value.length === 0) {\n            return '[]'\n          }\n          if (maximumDepth < stack.length + 1) {\n            return '\"[Array]\"'\n          }\n          stack.push(value)\n          if (spacer !== '') {\n            indentation += spacer\n            res += `\\n${indentation}`\n            join = `,\\n${indentation}`\n          }\n          const maximumValuesToStringify = Math.min(value.length, maximumBreadth)\n          let i = 0\n          for (; i < maximumValuesToStringify - 1; i++) {\n            const tmp = stringifyFnReplacer(String(i), value, stack, replacer, spacer, indentation)\n            res += tmp !== undefined ? tmp : 'null'\n            res += join\n          }\n          const tmp = stringifyFnReplacer(String(i), value, stack, replacer, spacer, indentation)\n          res += tmp !== undefined ? tmp : 'null'\n          if (value.length - 1 > maximumBreadth) {\n            const removedKeys = value.length - maximumBreadth - 1\n            res += `${join}\"... ${getItemCount(removedKeys)} not stringified\"`\n          }\n          if (spacer !== '') {\n            res += `\\n${originalIndentation}`\n          }\n          stack.pop()\n          return `[${res}]`\n        }\n\n        let keys = Object.keys(value)\n        const keyLength = keys.length\n        if (keyLength === 0) {\n          return '{}'\n        }\n        if (maximumDepth < stack.length + 1) {\n          return '\"[Object]\"'\n        }\n        let whitespace = ''\n        let separator = ''\n        if (spacer !== '') {\n          indentation += spacer\n          join = `,\\n${indentation}`\n          whitespace = ' '\n        }\n        const maximumPropertiesToStringify = Math.min(keyLength, maximumBreadth)\n        if (deterministic && !isTypedArrayWithEntries(value)) {\n          keys = sort(keys, comparator)\n        }\n        stack.push(value)\n        for (let i = 0; i < maximumPropertiesToStringify; i++) {\n          const key = keys[i]\n          const tmp = stringifyFnReplacer(key, value, stack, replacer, spacer, indentation)\n          if (tmp !== undefined) {\n            res += `${separator}${strEscape(key)}:${whitespace}${tmp}`\n            separator = join\n          }\n        }\n        if (keyLength > maximumBreadth) {\n          const removedKeys = keyLength - maximumBreadth\n          res += `${separator}\"...\":${whitespace}\"${getItemCount(removedKeys)} not stringified\"`\n          separator = join\n        }\n        if (spacer !== '' && separator.length > 1) {\n          res = `\\n${indentation}${res}\\n${originalIndentation}`\n        }\n        stack.pop()\n        return `{${res}}`\n      }\n      case 'number':\n        return isFinite(value) ? String(value) : fail ? fail(value) : 'null'\n      case 'boolean':\n        return value === true ? 'true' : 'false'\n      case 'undefined':\n        return undefined\n      case 'bigint':\n        if (bigint) {\n          return String(value)\n        }\n        // fallthrough\n      default:\n        return fail ? fail(value) : undefined\n    }\n  }\n\n  function stringifyArrayReplacer (key, value, stack, replacer, spacer, indentation) {\n    if (typeof value === 'object' && value !== null && typeof value.toJSON === 'function') {\n      value = value.toJSON(key)\n    }\n\n    switch (typeof value) {\n      case 'string':\n        return strEscape(value)\n      case 'object': {\n        if (value === null) {\n          return 'null'\n        }\n        if (stack.indexOf(value) !== -1) {\n          return circularValue\n        }\n\n        const originalIndentation = indentation\n        let res = ''\n        let join = ','\n\n        if (Array.isArray(value)) {\n          if (value.length === 0) {\n            return '[]'\n          }\n          if (maximumDepth < stack.length + 1) {\n            return '\"[Array]\"'\n          }\n          stack.push(value)\n          if (spacer !== '') {\n            indentation += spacer\n            res += `\\n${indentation}`\n            join = `,\\n${indentation}`\n          }\n          const maximumValuesToStringify = Math.min(value.length, maximumBreadth)\n          let i = 0\n          for (; i < maximumValuesToStringify - 1; i++) {\n            const tmp = stringifyArrayReplacer(String(i), value[i], stack, replacer, spacer, indentation)\n            res += tmp !== undefined ? tmp : 'null'\n            res += join\n          }\n          const tmp = stringifyArrayReplacer(String(i), value[i], stack, replacer, spacer, indentation)\n          res += tmp !== undefined ? tmp : 'null'\n          if (value.length - 1 > maximumBreadth) {\n            const removedKeys = value.length - maximumBreadth - 1\n            res += `${join}\"... ${getItemCount(removedKeys)} not stringified\"`\n          }\n          if (spacer !== '') {\n            res += `\\n${originalIndentation}`\n          }\n          stack.pop()\n          return `[${res}]`\n        }\n        stack.push(value)\n        let whitespace = ''\n        if (spacer !== '') {\n          indentation += spacer\n          join = `,\\n${indentation}`\n          whitespace = ' '\n        }\n        let separator = ''\n        for (const key of replacer) {\n          const tmp = stringifyArrayReplacer(key, value[key], stack, replacer, spacer, indentation)\n          if (tmp !== undefined) {\n            res += `${separator}${strEscape(key)}:${whitespace}${tmp}`\n            separator = join\n          }\n        }\n        if (spacer !== '' && separator.length > 1) {\n          res = `\\n${indentation}${res}\\n${originalIndentation}`\n        }\n        stack.pop()\n        return `{${res}}`\n      }\n      case 'number':\n        return isFinite(value) ? String(value) : fail ? fail(value) : 'null'\n      case 'boolean':\n        return value === true ? 'true' : 'false'\n      case 'undefined':\n        return undefined\n      case 'bigint':\n        if (bigint) {\n          return String(value)\n        }\n        // fallthrough\n      default:\n        return fail ? fail(value) : undefined\n    }\n  }\n\n  function stringifyIndent (key, value, stack, spacer, indentation) {\n    switch (typeof value) {\n      case 'string':\n        return strEscape(value)\n      case 'object': {\n        if (value === null) {\n          return 'null'\n        }\n        if (typeof value.toJSON === 'function') {\n          value = value.toJSON(key)\n          // Prevent calling `toJSON` again.\n          if (typeof value !== 'object') {\n            return stringifyIndent(key, value, stack, spacer, indentation)\n          }\n          if (value === null) {\n            return 'null'\n          }\n        }\n        if (stack.indexOf(value) !== -1) {\n          return circularValue\n        }\n        const originalIndentation = indentation\n\n        if (Array.isArray(value)) {\n          if (value.length === 0) {\n            return '[]'\n          }\n          if (maximumDepth < stack.length + 1) {\n            return '\"[Array]\"'\n          }\n          stack.push(value)\n          indentation += spacer\n          let res = `\\n${indentation}`\n          const join = `,\\n${indentation}`\n          const maximumValuesToStringify = Math.min(value.length, maximumBreadth)\n          let i = 0\n          for (; i < maximumValuesToStringify - 1; i++) {\n            const tmp = stringifyIndent(String(i), value[i], stack, spacer, indentation)\n            res += tmp !== undefined ? tmp : 'null'\n            res += join\n          }\n          const tmp = stringifyIndent(String(i), value[i], stack, spacer, indentation)\n          res += tmp !== undefined ? tmp : 'null'\n          if (value.length - 1 > maximumBreadth) {\n            const removedKeys = value.length - maximumBreadth - 1\n            res += `${join}\"... ${getItemCount(removedKeys)} not stringified\"`\n          }\n          res += `\\n${originalIndentation}`\n          stack.pop()\n          return `[${res}]`\n        }\n\n        let keys = Object.keys(value)\n        const keyLength = keys.length\n        if (keyLength === 0) {\n          return '{}'\n        }\n        if (maximumDepth < stack.length + 1) {\n          return '\"[Object]\"'\n        }\n        indentation += spacer\n        const join = `,\\n${indentation}`\n        let res = ''\n        let separator = ''\n        let maximumPropertiesToStringify = Math.min(keyLength, maximumBreadth)\n        if (isTypedArrayWithEntries(value)) {\n          res += stringifyTypedArray(value, join, maximumBreadth)\n          keys = keys.slice(value.length)\n          maximumPropertiesToStringify -= value.length\n          separator = join\n        }\n        if (deterministic) {\n          keys = sort(keys, comparator)\n        }\n        stack.push(value)\n        for (let i = 0; i < maximumPropertiesToStringify; i++) {\n          const key = keys[i]\n          const tmp = stringifyIndent(key, value[key], stack, spacer, indentation)\n          if (tmp !== undefined) {\n            res += `${separator}${strEscape(key)}: ${tmp}`\n            separator = join\n          }\n        }\n        if (keyLength > maximumBreadth) {\n          const removedKeys = keyLength - maximumBreadth\n          res += `${separator}\"...\": \"${getItemCount(removedKeys)} not stringified\"`\n          separator = join\n        }\n        if (separator !== '') {\n          res = `\\n${indentation}${res}\\n${originalIndentation}`\n        }\n        stack.pop()\n        return `{${res}}`\n      }\n      case 'number':\n        return isFinite(value) ? String(value) : fail ? fail(value) : 'null'\n      case 'boolean':\n        return value === true ? 'true' : 'false'\n      case 'undefined':\n        return undefined\n      case 'bigint':\n        if (bigint) {\n          return String(value)\n        }\n        // fallthrough\n      default:\n        return fail ? fail(value) : undefined\n    }\n  }\n\n  function stringifySimple (key, value, stack) {\n    switch (typeof value) {\n      case 'string':\n        return strEscape(value)\n      case 'object': {\n        if (value === null) {\n          return 'null'\n        }\n        if (typeof value.toJSON === 'function') {\n          value = value.toJSON(key)\n          // Prevent calling `toJSON` again\n          if (typeof value !== 'object') {\n            return stringifySimple(key, value, stack)\n          }\n          if (value === null) {\n            return 'null'\n          }\n        }\n        if (stack.indexOf(value) !== -1) {\n          return circularValue\n        }\n\n        let res = ''\n\n        const hasLength = value.length !== undefined\n        if (hasLength && Array.isArray(value)) {\n          if (value.length === 0) {\n            return '[]'\n          }\n          if (maximumDepth < stack.length + 1) {\n            return '\"[Array]\"'\n          }\n          stack.push(value)\n          const maximumValuesToStringify = Math.min(value.length, maximumBreadth)\n          let i = 0\n          for (; i < maximumValuesToStringify - 1; i++) {\n            const tmp = stringifySimple(String(i), value[i], stack)\n            res += tmp !== undefined ? tmp : 'null'\n            res += ','\n          }\n          const tmp = stringifySimple(String(i), value[i], stack)\n          res += tmp !== undefined ? tmp : 'null'\n          if (value.length - 1 > maximumBreadth) {\n            const removedKeys = value.length - maximumBreadth - 1\n            res += `,\"... ${getItemCount(removedKeys)} not stringified\"`\n          }\n          stack.pop()\n          return `[${res}]`\n        }\n\n        let keys = Object.keys(value)\n        const keyLength = keys.length\n        if (keyLength === 0) {\n          return '{}'\n        }\n        if (maximumDepth < stack.length + 1) {\n          return '\"[Object]\"'\n        }\n        let separator = ''\n        let maximumPropertiesToStringify = Math.min(keyLength, maximumBreadth)\n        if (hasLength && isTypedArrayWithEntries(value)) {\n          res += stringifyTypedArray(value, ',', maximumBreadth)\n          keys = keys.slice(value.length)\n          maximumPropertiesToStringify -= value.length\n          separator = ','\n        }\n        if (deterministic) {\n          keys = sort(keys, comparator)\n        }\n        stack.push(value)\n        for (let i = 0; i < maximumPropertiesToStringify; i++) {\n          const key = keys[i]\n          const tmp = stringifySimple(key, value[key], stack)\n          if (tmp !== undefined) {\n            res += `${separator}${strEscape(key)}:${tmp}`\n            separator = ','\n          }\n        }\n        if (keyLength > maximumBreadth) {\n          const removedKeys = keyLength - maximumBreadth\n          res += `${separator}\"...\":\"${getItemCount(removedKeys)} not stringified\"`\n        }\n        stack.pop()\n        return `{${res}}`\n      }\n      case 'number':\n        return isFinite(value) ? String(value) : fail ? fail(value) : 'null'\n      case 'boolean':\n        return value === true ? 'true' : 'false'\n      case 'undefined':\n        return undefined\n      case 'bigint':\n        if (bigint) {\n          return String(value)\n        }\n        // fallthrough\n      default:\n        return fail ? fail(value) : undefined\n    }\n  }\n\n  function stringify (value, replacer, space) {\n    if (arguments.length > 1) {\n      let spacer = ''\n      if (typeof space === 'number') {\n        spacer = ' '.repeat(Math.min(space, 10))\n      } else if (typeof space === 'string') {\n        spacer = space.slice(0, 10)\n      }\n      if (replacer != null) {\n        if (typeof replacer === 'function') {\n          return stringifyFnReplacer('', { '': value }, [], replacer, spacer, '')\n        }\n        if (Array.isArray(replacer)) {\n          return stringifyArrayReplacer('', value, [], getUniqueReplacerSet(replacer), spacer, '')\n        }\n      }\n      if (spacer.length !== 0) {\n        return stringifyIndent('', value, [], spacer, '')\n      }\n    }\n    return stringifySimple('', value, [])\n  }\n\n  return stringify\n}\n"], "mappings": "AAAA,YAAY;;AAEZ,MAAM;EAAEA;AAAe,CAAC,GAAGC,MAAM,CAACC,SAAS;AAE3C,MAAMC,SAAS,GAAGC,SAAS,CAAC,CAAC;;AAE7B;AACAD,SAAS,CAACC,SAAS,GAAGA,SAAS;AAC/B;AACAD,SAAS,CAACA,SAAS,GAAGA,SAAS;;AAE/B;AACAA,SAAS,CAACE,OAAO,GAAGF,SAAS;;AAE7B;AACAG,OAAO,CAACH,SAAS,GAAGA,SAAS;AAC7B;AACAG,OAAO,CAACF,SAAS,GAAGA,SAAS;AAE7BG,MAAM,CAACD,OAAO,GAAGH,SAAS;;AAE1B;AACA,MAAMK,wBAAwB,GAAG,0CAA0C;;AAE3E;AACA;AACA,SAASC,SAASA,CAAEC,GAAG,EAAE;EACvB;EACA,IAAIA,GAAG,CAACC,MAAM,GAAG,IAAI,IAAI,CAACH,wBAAwB,CAACI,IAAI,CAACF,GAAG,CAAC,EAAE;IAC5D,OAAO,IAAIA,GAAG,GAAG;EACnB;EACA,OAAOG,IAAI,CAACV,SAAS,CAACO,GAAG,CAAC;AAC5B;AAEA,SAASI,IAAIA,CAAEC,KAAK,EAAEC,UAAU,EAAE;EAChC;EACA;EACA,IAAID,KAAK,CAACJ,MAAM,GAAG,GAAG,IAAIK,UAAU,EAAE;IACpC,OAAOD,KAAK,CAACD,IAAI,CAACE,UAAU,CAAC;EAC/B;EACA,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,KAAK,CAACJ,MAAM,EAAEM,CAAC,EAAE,EAAE;IACrC,MAAMC,YAAY,GAAGH,KAAK,CAACE,CAAC,CAAC;IAC7B,IAAIE,QAAQ,GAAGF,CAAC;IAChB,OAAOE,QAAQ,KAAK,CAAC,IAAIJ,KAAK,CAACI,QAAQ,GAAG,CAAC,CAAC,GAAGD,YAAY,EAAE;MAC3DH,KAAK,CAACI,QAAQ,CAAC,GAAGJ,KAAK,CAACI,QAAQ,GAAG,CAAC,CAAC;MACrCA,QAAQ,EAAE;IACZ;IACAJ,KAAK,CAACI,QAAQ,CAAC,GAAGD,YAAY;EAChC;EACA,OAAOH,KAAK;AACd;AAEA,MAAMK,uCAAuC,GAC3CnB,MAAM,CAACoB,wBAAwB,CAC7BpB,MAAM,CAACqB,cAAc,CACnBrB,MAAM,CAACqB,cAAc,CACnB,IAAIC,SAAS,CAAC,CAChB,CACF,CAAC,EACDC,MAAM,CAACC,WACT,CAAC,CAACC,GAAG;AAEP,SAASC,uBAAuBA,CAAEC,KAAK,EAAE;EACvC,OAAOR,uCAAuC,CAACS,IAAI,CAACD,KAAK,CAAC,KAAKE,SAAS,IAAIF,KAAK,CAACjB,MAAM,KAAK,CAAC;AAChG;AAEA,SAASoB,mBAAmBA,CAAEhB,KAAK,EAAEiB,SAAS,EAAEC,cAAc,EAAE;EAC9D,IAAIlB,KAAK,CAACJ,MAAM,GAAGsB,cAAc,EAAE;IACjCA,cAAc,GAAGlB,KAAK,CAACJ,MAAM;EAC/B;EACA,MAAMuB,UAAU,GAAGF,SAAS,KAAK,GAAG,GAAG,EAAE,GAAG,GAAG;EAC/C,IAAIG,GAAG,GAAG,OAAOD,UAAU,GAAGnB,KAAK,CAAC,CAAC,CAAC,EAAE;EACxC,KAAK,IAAIE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGgB,cAAc,EAAEhB,CAAC,EAAE,EAAE;IACvCkB,GAAG,IAAI,GAAGH,SAAS,IAAIf,CAAC,KAAKiB,UAAU,GAAGnB,KAAK,CAACE,CAAC,CAAC,EAAE;EACtD;EACA,OAAOkB,GAAG;AACZ;AAEA,SAASC,sBAAsBA,CAAEC,OAAO,EAAE;EACxC,IAAIrC,cAAc,CAAC6B,IAAI,CAACQ,OAAO,EAAE,eAAe,CAAC,EAAE;IACjD,MAAMC,aAAa,GAAGD,OAAO,CAACC,aAAa;IAC3C,IAAI,OAAOA,aAAa,KAAK,QAAQ,EAAE;MACrC,OAAO,IAAIA,aAAa,GAAG;IAC7B;IACA,IAAIA,aAAa,IAAI,IAAI,EAAE;MACzB,OAAOA,aAAa;IACtB;IACA,IAAIA,aAAa,KAAKC,KAAK,IAAID,aAAa,KAAKE,SAAS,EAAE;MAC1D,OAAO;QACLC,QAAQA,CAAA,EAAI;UACV,MAAM,IAAID,SAAS,CAAC,uCAAuC,CAAC;QAC9D;MACF,CAAC;IACH;IACA,MAAM,IAAIA,SAAS,CAAC,oFAAoF,CAAC;EAC3G;EACA,OAAO,cAAc;AACvB;AAEA,SAASE,sBAAsBA,CAAEL,OAAO,EAAE;EACxC,IAAIT,KAAK;EACT,IAAI5B,cAAc,CAAC6B,IAAI,CAACQ,OAAO,EAAE,eAAe,CAAC,EAAE;IACjDT,KAAK,GAAGS,OAAO,CAACM,aAAa;IAC7B,IAAI,OAAOf,KAAK,KAAK,SAAS,IAAI,OAAOA,KAAK,KAAK,UAAU,EAAE;MAC7D,MAAM,IAAIY,SAAS,CAAC,6EAA6E,CAAC;IACpG;EACF;EACA,OAAOZ,KAAK,KAAKE,SAAS,GAAG,IAAI,GAAGF,KAAK;AAC3C;AAEA,SAASgB,gBAAgBA,CAAEP,OAAO,EAAEQ,GAAG,EAAE;EACvC,IAAIjB,KAAK;EACT,IAAI5B,cAAc,CAAC6B,IAAI,CAACQ,OAAO,EAAEQ,GAAG,CAAC,EAAE;IACrCjB,KAAK,GAAGS,OAAO,CAACQ,GAAG,CAAC;IACpB,IAAI,OAAOjB,KAAK,KAAK,SAAS,EAAE;MAC9B,MAAM,IAAIY,SAAS,CAAC,QAAQK,GAAG,oCAAoC,CAAC;IACtE;EACF;EACA,OAAOjB,KAAK,KAAKE,SAAS,GAAG,IAAI,GAAGF,KAAK;AAC3C;AAEA,SAASkB,wBAAwBA,CAAET,OAAO,EAAEQ,GAAG,EAAE;EAC/C,IAAIjB,KAAK;EACT,IAAI5B,cAAc,CAAC6B,IAAI,CAACQ,OAAO,EAAEQ,GAAG,CAAC,EAAE;IACrCjB,KAAK,GAAGS,OAAO,CAACQ,GAAG,CAAC;IACpB,IAAI,OAAOjB,KAAK,KAAK,QAAQ,EAAE;MAC7B,MAAM,IAAIY,SAAS,CAAC,QAAQK,GAAG,mCAAmC,CAAC;IACrE;IACA,IAAI,CAACE,MAAM,CAACC,SAAS,CAACpB,KAAK,CAAC,EAAE;MAC5B,MAAM,IAAIY,SAAS,CAAC,QAAQK,GAAG,+BAA+B,CAAC;IACjE;IACA,IAAIjB,KAAK,GAAG,CAAC,EAAE;MACb,MAAM,IAAIqB,UAAU,CAAC,QAAQJ,GAAG,yBAAyB,CAAC;IAC5D;EACF;EACA,OAAOjB,KAAK,KAAKE,SAAS,GAAGoB,QAAQ,GAAGtB,KAAK;AAC/C;AAEA,SAASuB,YAAYA,CAAEC,MAAM,EAAE;EAC7B,IAAIA,MAAM,KAAK,CAAC,EAAE;IAChB,OAAO,QAAQ;EACjB;EACA,OAAO,GAAGA,MAAM,QAAQ;AAC1B;AAEA,SAASC,oBAAoBA,CAAEC,aAAa,EAAE;EAC5C,MAAMC,WAAW,GAAG,IAAIC,GAAG,CAAC,CAAC;EAC7B,KAAK,MAAM5B,KAAK,IAAI0B,aAAa,EAAE;IACjC,IAAI,OAAO1B,KAAK,KAAK,QAAQ,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;MAC1D2B,WAAW,CAACE,GAAG,CAACC,MAAM,CAAC9B,KAAK,CAAC,CAAC;IAChC;EACF;EACA,OAAO2B,WAAW;AACpB;AAEA,SAASI,eAAeA,CAAEtB,OAAO,EAAE;EACjC,IAAIrC,cAAc,CAAC6B,IAAI,CAACQ,OAAO,EAAE,QAAQ,CAAC,EAAE;IAC1C,MAAMT,KAAK,GAAGS,OAAO,CAACuB,MAAM;IAC5B,IAAI,OAAOhC,KAAK,KAAK,SAAS,EAAE;MAC9B,MAAM,IAAIY,SAAS,CAAC,+CAA+C,CAAC;IACtE;IACA,IAAIZ,KAAK,EAAE;MACT,OAAQA,KAAK,IAAK;QAChB,IAAIiC,OAAO,GAAG,uDAAuD,OAAOjC,KAAK,EAAE;QACnF,IAAI,OAAOA,KAAK,KAAK,UAAU,EAAEiC,OAAO,IAAI,KAAKjC,KAAK,CAACa,QAAQ,CAAC,CAAC,GAAG;QACpE,MAAM,IAAIF,KAAK,CAACsB,OAAO,CAAC;MAC1B,CAAC;IACH;EACF;AACF;AAEA,SAASzD,SAASA,CAAEiC,OAAO,EAAE;EAC3BA,OAAO,GAAG;IAAE,GAAGA;EAAQ,CAAC;EACxB,MAAMyB,IAAI,GAAGH,eAAe,CAACtB,OAAO,CAAC;EACrC,IAAIyB,IAAI,EAAE;IACR,IAAIzB,OAAO,CAAC0B,MAAM,KAAKjC,SAAS,EAAE;MAChCO,OAAO,CAAC0B,MAAM,GAAG,KAAK;IACxB;IACA,IAAI,EAAE,eAAe,IAAI1B,OAAO,CAAC,EAAE;MACjCA,OAAO,CAACC,aAAa,GAAGC,KAAK;IAC/B;EACF;EACA,MAAMD,aAAa,GAAGF,sBAAsB,CAACC,OAAO,CAAC;EACrD,MAAM0B,MAAM,GAAGnB,gBAAgB,CAACP,OAAO,EAAE,QAAQ,CAAC;EAClD,MAAMM,aAAa,GAAGD,sBAAsB,CAACL,OAAO,CAAC;EACrD,MAAMrB,UAAU,GAAG,OAAO2B,aAAa,KAAK,UAAU,GAAGA,aAAa,GAAGb,SAAS;EAClF,MAAMkC,YAAY,GAAGlB,wBAAwB,CAACT,OAAO,EAAE,cAAc,CAAC;EACtE,MAAMJ,cAAc,GAAGa,wBAAwB,CAACT,OAAO,EAAE,gBAAgB,CAAC;EAE1E,SAAS4B,mBAAmBA,CAAEpB,GAAG,EAAEqB,MAAM,EAAEC,KAAK,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,WAAW,EAAE;IAC/E,IAAI1C,KAAK,GAAGsC,MAAM,CAACrB,GAAG,CAAC;IAEvB,IAAI,OAAOjB,KAAK,KAAK,QAAQ,IAAIA,KAAK,KAAK,IAAI,IAAI,OAAOA,KAAK,CAAC2C,MAAM,KAAK,UAAU,EAAE;MACrF3C,KAAK,GAAGA,KAAK,CAAC2C,MAAM,CAAC1B,GAAG,CAAC;IAC3B;IACAjB,KAAK,GAAGwC,QAAQ,CAACvC,IAAI,CAACqC,MAAM,EAAErB,GAAG,EAAEjB,KAAK,CAAC;IAEzC,QAAQ,OAAOA,KAAK;MAClB,KAAK,QAAQ;QACX,OAAOnB,SAAS,CAACmB,KAAK,CAAC;MACzB,KAAK,QAAQ;QAAE;UACb,IAAIA,KAAK,KAAK,IAAI,EAAE;YAClB,OAAO,MAAM;UACf;UACA,IAAIuC,KAAK,CAACK,OAAO,CAAC5C,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE;YAC/B,OAAOU,aAAa;UACtB;UAEA,IAAIH,GAAG,GAAG,EAAE;UACZ,IAAIsC,IAAI,GAAG,GAAG;UACd,MAAMC,mBAAmB,GAAGJ,WAAW;UAEvC,IAAIK,KAAK,CAACC,OAAO,CAAChD,KAAK,CAAC,EAAE;YACxB,IAAIA,KAAK,CAACjB,MAAM,KAAK,CAAC,EAAE;cACtB,OAAO,IAAI;YACb;YACA,IAAIqD,YAAY,GAAGG,KAAK,CAACxD,MAAM,GAAG,CAAC,EAAE;cACnC,OAAO,WAAW;YACpB;YACAwD,KAAK,CAACU,IAAI,CAACjD,KAAK,CAAC;YACjB,IAAIyC,MAAM,KAAK,EAAE,EAAE;cACjBC,WAAW,IAAID,MAAM;cACrBlC,GAAG,IAAI,KAAKmC,WAAW,EAAE;cACzBG,IAAI,GAAG,MAAMH,WAAW,EAAE;YAC5B;YACA,MAAMQ,wBAAwB,GAAGC,IAAI,CAACC,GAAG,CAACpD,KAAK,CAACjB,MAAM,EAAEsB,cAAc,CAAC;YACvE,IAAIhB,CAAC,GAAG,CAAC;YACT,OAAOA,CAAC,GAAG6D,wBAAwB,GAAG,CAAC,EAAE7D,CAAC,EAAE,EAAE;cAC5C,MAAMgE,GAAG,GAAGhB,mBAAmB,CAACP,MAAM,CAACzC,CAAC,CAAC,EAAEW,KAAK,EAAEuC,KAAK,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,WAAW,CAAC;cACvFnC,GAAG,IAAI8C,GAAG,KAAKnD,SAAS,GAAGmD,GAAG,GAAG,MAAM;cACvC9C,GAAG,IAAIsC,IAAI;YACb;YACA,MAAMQ,GAAG,GAAGhB,mBAAmB,CAACP,MAAM,CAACzC,CAAC,CAAC,EAAEW,KAAK,EAAEuC,KAAK,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,WAAW,CAAC;YACvFnC,GAAG,IAAI8C,GAAG,KAAKnD,SAAS,GAAGmD,GAAG,GAAG,MAAM;YACvC,IAAIrD,KAAK,CAACjB,MAAM,GAAG,CAAC,GAAGsB,cAAc,EAAE;cACrC,MAAMiD,WAAW,GAAGtD,KAAK,CAACjB,MAAM,GAAGsB,cAAc,GAAG,CAAC;cACrDE,GAAG,IAAI,GAAGsC,IAAI,QAAQtB,YAAY,CAAC+B,WAAW,CAAC,mBAAmB;YACpE;YACA,IAAIb,MAAM,KAAK,EAAE,EAAE;cACjBlC,GAAG,IAAI,KAAKuC,mBAAmB,EAAE;YACnC;YACAP,KAAK,CAACgB,GAAG,CAAC,CAAC;YACX,OAAO,IAAIhD,GAAG,GAAG;UACnB;UAEA,IAAIiD,IAAI,GAAGnF,MAAM,CAACmF,IAAI,CAACxD,KAAK,CAAC;UAC7B,MAAMyD,SAAS,GAAGD,IAAI,CAACzE,MAAM;UAC7B,IAAI0E,SAAS,KAAK,CAAC,EAAE;YACnB,OAAO,IAAI;UACb;UACA,IAAIrB,YAAY,GAAGG,KAAK,CAACxD,MAAM,GAAG,CAAC,EAAE;YACnC,OAAO,YAAY;UACrB;UACA,IAAIuB,UAAU,GAAG,EAAE;UACnB,IAAIF,SAAS,GAAG,EAAE;UAClB,IAAIqC,MAAM,KAAK,EAAE,EAAE;YACjBC,WAAW,IAAID,MAAM;YACrBI,IAAI,GAAG,MAAMH,WAAW,EAAE;YAC1BpC,UAAU,GAAG,GAAG;UAClB;UACA,MAAMoD,4BAA4B,GAAGP,IAAI,CAACC,GAAG,CAACK,SAAS,EAAEpD,cAAc,CAAC;UACxE,IAAIU,aAAa,IAAI,CAAChB,uBAAuB,CAACC,KAAK,CAAC,EAAE;YACpDwD,IAAI,GAAGtE,IAAI,CAACsE,IAAI,EAAEpE,UAAU,CAAC;UAC/B;UACAmD,KAAK,CAACU,IAAI,CAACjD,KAAK,CAAC;UACjB,KAAK,IAAIX,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGqE,4BAA4B,EAAErE,CAAC,EAAE,EAAE;YACrD,MAAM4B,GAAG,GAAGuC,IAAI,CAACnE,CAAC,CAAC;YACnB,MAAMgE,GAAG,GAAGhB,mBAAmB,CAACpB,GAAG,EAAEjB,KAAK,EAAEuC,KAAK,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,WAAW,CAAC;YACjF,IAAIW,GAAG,KAAKnD,SAAS,EAAE;cACrBK,GAAG,IAAI,GAAGH,SAAS,GAAGvB,SAAS,CAACoC,GAAG,CAAC,IAAIX,UAAU,GAAG+C,GAAG,EAAE;cAC1DjD,SAAS,GAAGyC,IAAI;YAClB;UACF;UACA,IAAIY,SAAS,GAAGpD,cAAc,EAAE;YAC9B,MAAMiD,WAAW,GAAGG,SAAS,GAAGpD,cAAc;YAC9CE,GAAG,IAAI,GAAGH,SAAS,SAASE,UAAU,IAAIiB,YAAY,CAAC+B,WAAW,CAAC,mBAAmB;YACtFlD,SAAS,GAAGyC,IAAI;UAClB;UACA,IAAIJ,MAAM,KAAK,EAAE,IAAIrC,SAAS,CAACrB,MAAM,GAAG,CAAC,EAAE;YACzCwB,GAAG,GAAG,KAAKmC,WAAW,GAAGnC,GAAG,KAAKuC,mBAAmB,EAAE;UACxD;UACAP,KAAK,CAACgB,GAAG,CAAC,CAAC;UACX,OAAO,IAAIhD,GAAG,GAAG;QACnB;MACA,KAAK,QAAQ;QACX,OAAOoD,QAAQ,CAAC3D,KAAK,CAAC,GAAG8B,MAAM,CAAC9B,KAAK,CAAC,GAAGkC,IAAI,GAAGA,IAAI,CAAClC,KAAK,CAAC,GAAG,MAAM;MACtE,KAAK,SAAS;QACZ,OAAOA,KAAK,KAAK,IAAI,GAAG,MAAM,GAAG,OAAO;MAC1C,KAAK,WAAW;QACd,OAAOE,SAAS;MAClB,KAAK,QAAQ;QACX,IAAIiC,MAAM,EAAE;UACV,OAAOL,MAAM,CAAC9B,KAAK,CAAC;QACtB;MACA;MACF;QACE,OAAOkC,IAAI,GAAGA,IAAI,CAAClC,KAAK,CAAC,GAAGE,SAAS;IACzC;EACF;EAEA,SAAS0D,sBAAsBA,CAAE3C,GAAG,EAAEjB,KAAK,EAAEuC,KAAK,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,WAAW,EAAE;IACjF,IAAI,OAAO1C,KAAK,KAAK,QAAQ,IAAIA,KAAK,KAAK,IAAI,IAAI,OAAOA,KAAK,CAAC2C,MAAM,KAAK,UAAU,EAAE;MACrF3C,KAAK,GAAGA,KAAK,CAAC2C,MAAM,CAAC1B,GAAG,CAAC;IAC3B;IAEA,QAAQ,OAAOjB,KAAK;MAClB,KAAK,QAAQ;QACX,OAAOnB,SAAS,CAACmB,KAAK,CAAC;MACzB,KAAK,QAAQ;QAAE;UACb,IAAIA,KAAK,KAAK,IAAI,EAAE;YAClB,OAAO,MAAM;UACf;UACA,IAAIuC,KAAK,CAACK,OAAO,CAAC5C,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE;YAC/B,OAAOU,aAAa;UACtB;UAEA,MAAMoC,mBAAmB,GAAGJ,WAAW;UACvC,IAAInC,GAAG,GAAG,EAAE;UACZ,IAAIsC,IAAI,GAAG,GAAG;UAEd,IAAIE,KAAK,CAACC,OAAO,CAAChD,KAAK,CAAC,EAAE;YACxB,IAAIA,KAAK,CAACjB,MAAM,KAAK,CAAC,EAAE;cACtB,OAAO,IAAI;YACb;YACA,IAAIqD,YAAY,GAAGG,KAAK,CAACxD,MAAM,GAAG,CAAC,EAAE;cACnC,OAAO,WAAW;YACpB;YACAwD,KAAK,CAACU,IAAI,CAACjD,KAAK,CAAC;YACjB,IAAIyC,MAAM,KAAK,EAAE,EAAE;cACjBC,WAAW,IAAID,MAAM;cACrBlC,GAAG,IAAI,KAAKmC,WAAW,EAAE;cACzBG,IAAI,GAAG,MAAMH,WAAW,EAAE;YAC5B;YACA,MAAMQ,wBAAwB,GAAGC,IAAI,CAACC,GAAG,CAACpD,KAAK,CAACjB,MAAM,EAAEsB,cAAc,CAAC;YACvE,IAAIhB,CAAC,GAAG,CAAC;YACT,OAAOA,CAAC,GAAG6D,wBAAwB,GAAG,CAAC,EAAE7D,CAAC,EAAE,EAAE;cAC5C,MAAMgE,GAAG,GAAGO,sBAAsB,CAAC9B,MAAM,CAACzC,CAAC,CAAC,EAAEW,KAAK,CAACX,CAAC,CAAC,EAAEkD,KAAK,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,WAAW,CAAC;cAC7FnC,GAAG,IAAI8C,GAAG,KAAKnD,SAAS,GAAGmD,GAAG,GAAG,MAAM;cACvC9C,GAAG,IAAIsC,IAAI;YACb;YACA,MAAMQ,GAAG,GAAGO,sBAAsB,CAAC9B,MAAM,CAACzC,CAAC,CAAC,EAAEW,KAAK,CAACX,CAAC,CAAC,EAAEkD,KAAK,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,WAAW,CAAC;YAC7FnC,GAAG,IAAI8C,GAAG,KAAKnD,SAAS,GAAGmD,GAAG,GAAG,MAAM;YACvC,IAAIrD,KAAK,CAACjB,MAAM,GAAG,CAAC,GAAGsB,cAAc,EAAE;cACrC,MAAMiD,WAAW,GAAGtD,KAAK,CAACjB,MAAM,GAAGsB,cAAc,GAAG,CAAC;cACrDE,GAAG,IAAI,GAAGsC,IAAI,QAAQtB,YAAY,CAAC+B,WAAW,CAAC,mBAAmB;YACpE;YACA,IAAIb,MAAM,KAAK,EAAE,EAAE;cACjBlC,GAAG,IAAI,KAAKuC,mBAAmB,EAAE;YACnC;YACAP,KAAK,CAACgB,GAAG,CAAC,CAAC;YACX,OAAO,IAAIhD,GAAG,GAAG;UACnB;UACAgC,KAAK,CAACU,IAAI,CAACjD,KAAK,CAAC;UACjB,IAAIM,UAAU,GAAG,EAAE;UACnB,IAAImC,MAAM,KAAK,EAAE,EAAE;YACjBC,WAAW,IAAID,MAAM;YACrBI,IAAI,GAAG,MAAMH,WAAW,EAAE;YAC1BpC,UAAU,GAAG,GAAG;UAClB;UACA,IAAIF,SAAS,GAAG,EAAE;UAClB,KAAK,MAAMa,GAAG,IAAIuB,QAAQ,EAAE;YAC1B,MAAMa,GAAG,GAAGO,sBAAsB,CAAC3C,GAAG,EAAEjB,KAAK,CAACiB,GAAG,CAAC,EAAEsB,KAAK,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,WAAW,CAAC;YACzF,IAAIW,GAAG,KAAKnD,SAAS,EAAE;cACrBK,GAAG,IAAI,GAAGH,SAAS,GAAGvB,SAAS,CAACoC,GAAG,CAAC,IAAIX,UAAU,GAAG+C,GAAG,EAAE;cAC1DjD,SAAS,GAAGyC,IAAI;YAClB;UACF;UACA,IAAIJ,MAAM,KAAK,EAAE,IAAIrC,SAAS,CAACrB,MAAM,GAAG,CAAC,EAAE;YACzCwB,GAAG,GAAG,KAAKmC,WAAW,GAAGnC,GAAG,KAAKuC,mBAAmB,EAAE;UACxD;UACAP,KAAK,CAACgB,GAAG,CAAC,CAAC;UACX,OAAO,IAAIhD,GAAG,GAAG;QACnB;MACA,KAAK,QAAQ;QACX,OAAOoD,QAAQ,CAAC3D,KAAK,CAAC,GAAG8B,MAAM,CAAC9B,KAAK,CAAC,GAAGkC,IAAI,GAAGA,IAAI,CAAClC,KAAK,CAAC,GAAG,MAAM;MACtE,KAAK,SAAS;QACZ,OAAOA,KAAK,KAAK,IAAI,GAAG,MAAM,GAAG,OAAO;MAC1C,KAAK,WAAW;QACd,OAAOE,SAAS;MAClB,KAAK,QAAQ;QACX,IAAIiC,MAAM,EAAE;UACV,OAAOL,MAAM,CAAC9B,KAAK,CAAC;QACtB;MACA;MACF;QACE,OAAOkC,IAAI,GAAGA,IAAI,CAAClC,KAAK,CAAC,GAAGE,SAAS;IACzC;EACF;EAEA,SAAS2D,eAAeA,CAAE5C,GAAG,EAAEjB,KAAK,EAAEuC,KAAK,EAAEE,MAAM,EAAEC,WAAW,EAAE;IAChE,QAAQ,OAAO1C,KAAK;MAClB,KAAK,QAAQ;QACX,OAAOnB,SAAS,CAACmB,KAAK,CAAC;MACzB,KAAK,QAAQ;QAAE;UACb,IAAIA,KAAK,KAAK,IAAI,EAAE;YAClB,OAAO,MAAM;UACf;UACA,IAAI,OAAOA,KAAK,CAAC2C,MAAM,KAAK,UAAU,EAAE;YACtC3C,KAAK,GAAGA,KAAK,CAAC2C,MAAM,CAAC1B,GAAG,CAAC;YACzB;YACA,IAAI,OAAOjB,KAAK,KAAK,QAAQ,EAAE;cAC7B,OAAO6D,eAAe,CAAC5C,GAAG,EAAEjB,KAAK,EAAEuC,KAAK,EAAEE,MAAM,EAAEC,WAAW,CAAC;YAChE;YACA,IAAI1C,KAAK,KAAK,IAAI,EAAE;cAClB,OAAO,MAAM;YACf;UACF;UACA,IAAIuC,KAAK,CAACK,OAAO,CAAC5C,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE;YAC/B,OAAOU,aAAa;UACtB;UACA,MAAMoC,mBAAmB,GAAGJ,WAAW;UAEvC,IAAIK,KAAK,CAACC,OAAO,CAAChD,KAAK,CAAC,EAAE;YACxB,IAAIA,KAAK,CAACjB,MAAM,KAAK,CAAC,EAAE;cACtB,OAAO,IAAI;YACb;YACA,IAAIqD,YAAY,GAAGG,KAAK,CAACxD,MAAM,GAAG,CAAC,EAAE;cACnC,OAAO,WAAW;YACpB;YACAwD,KAAK,CAACU,IAAI,CAACjD,KAAK,CAAC;YACjB0C,WAAW,IAAID,MAAM;YACrB,IAAIlC,GAAG,GAAG,KAAKmC,WAAW,EAAE;YAC5B,MAAMG,IAAI,GAAG,MAAMH,WAAW,EAAE;YAChC,MAAMQ,wBAAwB,GAAGC,IAAI,CAACC,GAAG,CAACpD,KAAK,CAACjB,MAAM,EAAEsB,cAAc,CAAC;YACvE,IAAIhB,CAAC,GAAG,CAAC;YACT,OAAOA,CAAC,GAAG6D,wBAAwB,GAAG,CAAC,EAAE7D,CAAC,EAAE,EAAE;cAC5C,MAAMgE,GAAG,GAAGQ,eAAe,CAAC/B,MAAM,CAACzC,CAAC,CAAC,EAAEW,KAAK,CAACX,CAAC,CAAC,EAAEkD,KAAK,EAAEE,MAAM,EAAEC,WAAW,CAAC;cAC5EnC,GAAG,IAAI8C,GAAG,KAAKnD,SAAS,GAAGmD,GAAG,GAAG,MAAM;cACvC9C,GAAG,IAAIsC,IAAI;YACb;YACA,MAAMQ,GAAG,GAAGQ,eAAe,CAAC/B,MAAM,CAACzC,CAAC,CAAC,EAAEW,KAAK,CAACX,CAAC,CAAC,EAAEkD,KAAK,EAAEE,MAAM,EAAEC,WAAW,CAAC;YAC5EnC,GAAG,IAAI8C,GAAG,KAAKnD,SAAS,GAAGmD,GAAG,GAAG,MAAM;YACvC,IAAIrD,KAAK,CAACjB,MAAM,GAAG,CAAC,GAAGsB,cAAc,EAAE;cACrC,MAAMiD,WAAW,GAAGtD,KAAK,CAACjB,MAAM,GAAGsB,cAAc,GAAG,CAAC;cACrDE,GAAG,IAAI,GAAGsC,IAAI,QAAQtB,YAAY,CAAC+B,WAAW,CAAC,mBAAmB;YACpE;YACA/C,GAAG,IAAI,KAAKuC,mBAAmB,EAAE;YACjCP,KAAK,CAACgB,GAAG,CAAC,CAAC;YACX,OAAO,IAAIhD,GAAG,GAAG;UACnB;UAEA,IAAIiD,IAAI,GAAGnF,MAAM,CAACmF,IAAI,CAACxD,KAAK,CAAC;UAC7B,MAAMyD,SAAS,GAAGD,IAAI,CAACzE,MAAM;UAC7B,IAAI0E,SAAS,KAAK,CAAC,EAAE;YACnB,OAAO,IAAI;UACb;UACA,IAAIrB,YAAY,GAAGG,KAAK,CAACxD,MAAM,GAAG,CAAC,EAAE;YACnC,OAAO,YAAY;UACrB;UACA2D,WAAW,IAAID,MAAM;UACrB,MAAMI,IAAI,GAAG,MAAMH,WAAW,EAAE;UAChC,IAAInC,GAAG,GAAG,EAAE;UACZ,IAAIH,SAAS,GAAG,EAAE;UAClB,IAAIsD,4BAA4B,GAAGP,IAAI,CAACC,GAAG,CAACK,SAAS,EAAEpD,cAAc,CAAC;UACtE,IAAIN,uBAAuB,CAACC,KAAK,CAAC,EAAE;YAClCO,GAAG,IAAIJ,mBAAmB,CAACH,KAAK,EAAE6C,IAAI,EAAExC,cAAc,CAAC;YACvDmD,IAAI,GAAGA,IAAI,CAACM,KAAK,CAAC9D,KAAK,CAACjB,MAAM,CAAC;YAC/B2E,4BAA4B,IAAI1D,KAAK,CAACjB,MAAM;YAC5CqB,SAAS,GAAGyC,IAAI;UAClB;UACA,IAAI9B,aAAa,EAAE;YACjByC,IAAI,GAAGtE,IAAI,CAACsE,IAAI,EAAEpE,UAAU,CAAC;UAC/B;UACAmD,KAAK,CAACU,IAAI,CAACjD,KAAK,CAAC;UACjB,KAAK,IAAIX,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGqE,4BAA4B,EAAErE,CAAC,EAAE,EAAE;YACrD,MAAM4B,GAAG,GAAGuC,IAAI,CAACnE,CAAC,CAAC;YACnB,MAAMgE,GAAG,GAAGQ,eAAe,CAAC5C,GAAG,EAAEjB,KAAK,CAACiB,GAAG,CAAC,EAAEsB,KAAK,EAAEE,MAAM,EAAEC,WAAW,CAAC;YACxE,IAAIW,GAAG,KAAKnD,SAAS,EAAE;cACrBK,GAAG,IAAI,GAAGH,SAAS,GAAGvB,SAAS,CAACoC,GAAG,CAAC,KAAKoC,GAAG,EAAE;cAC9CjD,SAAS,GAAGyC,IAAI;YAClB;UACF;UACA,IAAIY,SAAS,GAAGpD,cAAc,EAAE;YAC9B,MAAMiD,WAAW,GAAGG,SAAS,GAAGpD,cAAc;YAC9CE,GAAG,IAAI,GAAGH,SAAS,WAAWmB,YAAY,CAAC+B,WAAW,CAAC,mBAAmB;YAC1ElD,SAAS,GAAGyC,IAAI;UAClB;UACA,IAAIzC,SAAS,KAAK,EAAE,EAAE;YACpBG,GAAG,GAAG,KAAKmC,WAAW,GAAGnC,GAAG,KAAKuC,mBAAmB,EAAE;UACxD;UACAP,KAAK,CAACgB,GAAG,CAAC,CAAC;UACX,OAAO,IAAIhD,GAAG,GAAG;QACnB;MACA,KAAK,QAAQ;QACX,OAAOoD,QAAQ,CAAC3D,KAAK,CAAC,GAAG8B,MAAM,CAAC9B,KAAK,CAAC,GAAGkC,IAAI,GAAGA,IAAI,CAAClC,KAAK,CAAC,GAAG,MAAM;MACtE,KAAK,SAAS;QACZ,OAAOA,KAAK,KAAK,IAAI,GAAG,MAAM,GAAG,OAAO;MAC1C,KAAK,WAAW;QACd,OAAOE,SAAS;MAClB,KAAK,QAAQ;QACX,IAAIiC,MAAM,EAAE;UACV,OAAOL,MAAM,CAAC9B,KAAK,CAAC;QACtB;MACA;MACF;QACE,OAAOkC,IAAI,GAAGA,IAAI,CAAClC,KAAK,CAAC,GAAGE,SAAS;IACzC;EACF;EAEA,SAAS6D,eAAeA,CAAE9C,GAAG,EAAEjB,KAAK,EAAEuC,KAAK,EAAE;IAC3C,QAAQ,OAAOvC,KAAK;MAClB,KAAK,QAAQ;QACX,OAAOnB,SAAS,CAACmB,KAAK,CAAC;MACzB,KAAK,QAAQ;QAAE;UACb,IAAIA,KAAK,KAAK,IAAI,EAAE;YAClB,OAAO,MAAM;UACf;UACA,IAAI,OAAOA,KAAK,CAAC2C,MAAM,KAAK,UAAU,EAAE;YACtC3C,KAAK,GAAGA,KAAK,CAAC2C,MAAM,CAAC1B,GAAG,CAAC;YACzB;YACA,IAAI,OAAOjB,KAAK,KAAK,QAAQ,EAAE;cAC7B,OAAO+D,eAAe,CAAC9C,GAAG,EAAEjB,KAAK,EAAEuC,KAAK,CAAC;YAC3C;YACA,IAAIvC,KAAK,KAAK,IAAI,EAAE;cAClB,OAAO,MAAM;YACf;UACF;UACA,IAAIuC,KAAK,CAACK,OAAO,CAAC5C,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE;YAC/B,OAAOU,aAAa;UACtB;UAEA,IAAIH,GAAG,GAAG,EAAE;UAEZ,MAAMyD,SAAS,GAAGhE,KAAK,CAACjB,MAAM,KAAKmB,SAAS;UAC5C,IAAI8D,SAAS,IAAIjB,KAAK,CAACC,OAAO,CAAChD,KAAK,CAAC,EAAE;YACrC,IAAIA,KAAK,CAACjB,MAAM,KAAK,CAAC,EAAE;cACtB,OAAO,IAAI;YACb;YACA,IAAIqD,YAAY,GAAGG,KAAK,CAACxD,MAAM,GAAG,CAAC,EAAE;cACnC,OAAO,WAAW;YACpB;YACAwD,KAAK,CAACU,IAAI,CAACjD,KAAK,CAAC;YACjB,MAAMkD,wBAAwB,GAAGC,IAAI,CAACC,GAAG,CAACpD,KAAK,CAACjB,MAAM,EAAEsB,cAAc,CAAC;YACvE,IAAIhB,CAAC,GAAG,CAAC;YACT,OAAOA,CAAC,GAAG6D,wBAAwB,GAAG,CAAC,EAAE7D,CAAC,EAAE,EAAE;cAC5C,MAAMgE,GAAG,GAAGU,eAAe,CAACjC,MAAM,CAACzC,CAAC,CAAC,EAAEW,KAAK,CAACX,CAAC,CAAC,EAAEkD,KAAK,CAAC;cACvDhC,GAAG,IAAI8C,GAAG,KAAKnD,SAAS,GAAGmD,GAAG,GAAG,MAAM;cACvC9C,GAAG,IAAI,GAAG;YACZ;YACA,MAAM8C,GAAG,GAAGU,eAAe,CAACjC,MAAM,CAACzC,CAAC,CAAC,EAAEW,KAAK,CAACX,CAAC,CAAC,EAAEkD,KAAK,CAAC;YACvDhC,GAAG,IAAI8C,GAAG,KAAKnD,SAAS,GAAGmD,GAAG,GAAG,MAAM;YACvC,IAAIrD,KAAK,CAACjB,MAAM,GAAG,CAAC,GAAGsB,cAAc,EAAE;cACrC,MAAMiD,WAAW,GAAGtD,KAAK,CAACjB,MAAM,GAAGsB,cAAc,GAAG,CAAC;cACrDE,GAAG,IAAI,SAASgB,YAAY,CAAC+B,WAAW,CAAC,mBAAmB;YAC9D;YACAf,KAAK,CAACgB,GAAG,CAAC,CAAC;YACX,OAAO,IAAIhD,GAAG,GAAG;UACnB;UAEA,IAAIiD,IAAI,GAAGnF,MAAM,CAACmF,IAAI,CAACxD,KAAK,CAAC;UAC7B,MAAMyD,SAAS,GAAGD,IAAI,CAACzE,MAAM;UAC7B,IAAI0E,SAAS,KAAK,CAAC,EAAE;YACnB,OAAO,IAAI;UACb;UACA,IAAIrB,YAAY,GAAGG,KAAK,CAACxD,MAAM,GAAG,CAAC,EAAE;YACnC,OAAO,YAAY;UACrB;UACA,IAAIqB,SAAS,GAAG,EAAE;UAClB,IAAIsD,4BAA4B,GAAGP,IAAI,CAACC,GAAG,CAACK,SAAS,EAAEpD,cAAc,CAAC;UACtE,IAAI2D,SAAS,IAAIjE,uBAAuB,CAACC,KAAK,CAAC,EAAE;YAC/CO,GAAG,IAAIJ,mBAAmB,CAACH,KAAK,EAAE,GAAG,EAAEK,cAAc,CAAC;YACtDmD,IAAI,GAAGA,IAAI,CAACM,KAAK,CAAC9D,KAAK,CAACjB,MAAM,CAAC;YAC/B2E,4BAA4B,IAAI1D,KAAK,CAACjB,MAAM;YAC5CqB,SAAS,GAAG,GAAG;UACjB;UACA,IAAIW,aAAa,EAAE;YACjByC,IAAI,GAAGtE,IAAI,CAACsE,IAAI,EAAEpE,UAAU,CAAC;UAC/B;UACAmD,KAAK,CAACU,IAAI,CAACjD,KAAK,CAAC;UACjB,KAAK,IAAIX,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGqE,4BAA4B,EAAErE,CAAC,EAAE,EAAE;YACrD,MAAM4B,GAAG,GAAGuC,IAAI,CAACnE,CAAC,CAAC;YACnB,MAAMgE,GAAG,GAAGU,eAAe,CAAC9C,GAAG,EAAEjB,KAAK,CAACiB,GAAG,CAAC,EAAEsB,KAAK,CAAC;YACnD,IAAIc,GAAG,KAAKnD,SAAS,EAAE;cACrBK,GAAG,IAAI,GAAGH,SAAS,GAAGvB,SAAS,CAACoC,GAAG,CAAC,IAAIoC,GAAG,EAAE;cAC7CjD,SAAS,GAAG,GAAG;YACjB;UACF;UACA,IAAIqD,SAAS,GAAGpD,cAAc,EAAE;YAC9B,MAAMiD,WAAW,GAAGG,SAAS,GAAGpD,cAAc;YAC9CE,GAAG,IAAI,GAAGH,SAAS,UAAUmB,YAAY,CAAC+B,WAAW,CAAC,mBAAmB;UAC3E;UACAf,KAAK,CAACgB,GAAG,CAAC,CAAC;UACX,OAAO,IAAIhD,GAAG,GAAG;QACnB;MACA,KAAK,QAAQ;QACX,OAAOoD,QAAQ,CAAC3D,KAAK,CAAC,GAAG8B,MAAM,CAAC9B,KAAK,CAAC,GAAGkC,IAAI,GAAGA,IAAI,CAAClC,KAAK,CAAC,GAAG,MAAM;MACtE,KAAK,SAAS;QACZ,OAAOA,KAAK,KAAK,IAAI,GAAG,MAAM,GAAG,OAAO;MAC1C,KAAK,WAAW;QACd,OAAOE,SAAS;MAClB,KAAK,QAAQ;QACX,IAAIiC,MAAM,EAAE;UACV,OAAOL,MAAM,CAAC9B,KAAK,CAAC;QACtB;MACA;MACF;QACE,OAAOkC,IAAI,GAAGA,IAAI,CAAClC,KAAK,CAAC,GAAGE,SAAS;IACzC;EACF;EAEA,SAAS3B,SAASA,CAAEyB,KAAK,EAAEwC,QAAQ,EAAEyB,KAAK,EAAE;IAC1C,IAAIC,SAAS,CAACnF,MAAM,GAAG,CAAC,EAAE;MACxB,IAAI0D,MAAM,GAAG,EAAE;MACf,IAAI,OAAOwB,KAAK,KAAK,QAAQ,EAAE;QAC7BxB,MAAM,GAAG,GAAG,CAAC0B,MAAM,CAAChB,IAAI,CAACC,GAAG,CAACa,KAAK,EAAE,EAAE,CAAC,CAAC;MAC1C,CAAC,MAAM,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;QACpCxB,MAAM,GAAGwB,KAAK,CAACH,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC;MAC7B;MACA,IAAItB,QAAQ,IAAI,IAAI,EAAE;QACpB,IAAI,OAAOA,QAAQ,KAAK,UAAU,EAAE;UAClC,OAAOH,mBAAmB,CAAC,EAAE,EAAE;YAAE,EAAE,EAAErC;UAAM,CAAC,EAAE,EAAE,EAAEwC,QAAQ,EAAEC,MAAM,EAAE,EAAE,CAAC;QACzE;QACA,IAAIM,KAAK,CAACC,OAAO,CAACR,QAAQ,CAAC,EAAE;UAC3B,OAAOoB,sBAAsB,CAAC,EAAE,EAAE5D,KAAK,EAAE,EAAE,EAAEyB,oBAAoB,CAACe,QAAQ,CAAC,EAAEC,MAAM,EAAE,EAAE,CAAC;QAC1F;MACF;MACA,IAAIA,MAAM,CAAC1D,MAAM,KAAK,CAAC,EAAE;QACvB,OAAO8E,eAAe,CAAC,EAAE,EAAE7D,KAAK,EAAE,EAAE,EAAEyC,MAAM,EAAE,EAAE,CAAC;MACnD;IACF;IACA,OAAOsB,eAAe,CAAC,EAAE,EAAE/D,KAAK,EAAE,EAAE,CAAC;EACvC;EAEA,OAAOzB,SAAS;AAClB", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}