{"ast": null, "code": "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { Greater } from '@tensorflow/tfjs-core';\nimport { binaryKernelFunc } from '../kernel_utils/kernel_funcs_utils';\nimport { greaterImplCPU } from '../kernel_utils/shared';\nconst GREATER = \"return float(a > b);\";\nconst GREATER_PACKED = \"\\n  return vec4(greaterThan(a, b));\\n\";\nexport const greater = binaryKernelFunc({\n  opSnippet: GREATER,\n  packedOpSnippet: GREATER_PACKED,\n  cpuKernelImpl: greaterImplCPU,\n  dtype: 'bool'\n});\nexport const greaterConfig = {\n  kernelName: Greater,\n  backendName: 'webgl',\n  kernelFunc: greater\n};", "map": {"version": 3, "names": ["Greater", "binaryKernelFunc", "greaterImplCPU", "GREATER", "GREATER_PACKED", "greater", "opSnippet", "packedOpSnippet", "cpuKernelImpl", "dtype", "greaterConfig", "kernelName", "backendName", "kernelFunc"], "sources": ["C:\\tfjs-backend-webgl\\src\\kernels\\Greater.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {Greater, KernelConfig, KernelFunc} from '@tensorflow/tfjs-core';\n\nimport {binaryKernelFunc} from '../kernel_utils/kernel_funcs_utils';\nimport {greaterImplCPU} from '../kernel_utils/shared';\n\nconst GREATER = `return float(a > b);`;\nconst GREATER_PACKED = `\n  return vec4(greaterThan(a, b));\n`;\n\nexport const greater = binaryKernelFunc({\n  opSnippet: GREATER,\n  packedOpSnippet: GREATER_PACKED,\n  cpuKernelImpl: greaterImplCPU,\n  dtype: 'bool'\n});\n\nexport const greaterConfig: KernelConfig = {\n  kernelName: Greater,\n  backendName: 'webgl',\n  kernelFunc: greater as unknown as KernelFunc\n};\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAiBA,SAAQA,OAAO,QAAiC,uBAAuB;AAEvE,SAAQC,gBAAgB,QAAO,oCAAoC;AACnE,SAAQC,cAAc,QAAO,wBAAwB;AAErD,MAAMC,OAAO,yBAAyB;AACtC,MAAMC,cAAc,0CAEnB;AAED,OAAO,MAAMC,OAAO,GAAGJ,gBAAgB,CAAC;EACtCK,SAAS,EAAEH,OAAO;EAClBI,eAAe,EAAEH,cAAc;EAC/BI,aAAa,EAAEN,cAAc;EAC7BO,KAAK,EAAE;CACR,CAAC;AAEF,OAAO,MAAMC,aAAa,GAAiB;EACzCC,UAAU,EAAEX,OAAO;EACnBY,WAAW,EAAE,OAAO;EACpBC,UAAU,EAAER;CACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}