{"ast": null, "code": "/**\n * @license\n * Copyright 2023 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nexport const json = [{\n  'tfOpName': 'PlaceholderWithDefault',\n  'category': 'graph',\n  'inputs': [{\n    'start': 0,\n    'name': 'default',\n    'type': 'tensor'\n  }],\n  'attrs': [{\n    'tfName': 'shape',\n    'name': 'shape',\n    'type': 'shape'\n  }, {\n    'tfName': 'dtype',\n    'name': 'dtype',\n    'type': 'dtype'\n  }]\n}, {\n  'tfOpName': 'Placeholder',\n  'category': 'graph',\n  'attrs': [{\n    'tfName': 'shape',\n    'name': 'shape',\n    'type': 'shape'\n  }, {\n    'tfName': 'dtype',\n    'name': 'dtype',\n    'type': 'dtype'\n  }]\n}, {\n  'tfOpName': 'Const',\n  'category': 'graph'\n}, {\n  'tfOpName': 'Identity',\n  'category': 'graph',\n  'inputs': [{\n    'start': 0,\n    'name': 'x',\n    'type': 'tensor'\n  }]\n}, {\n  'tfOpName': 'IdentityN',\n  'category': 'graph',\n  'inputs': [{\n    'start': 0,\n    'end': 0,\n    'name': 'x',\n    'type': 'tensors'\n  }]\n}, {\n  'tfOpName': 'Snapshot',\n  'category': 'graph',\n  'inputs': [{\n    'start': 0,\n    'name': 'x',\n    'type': 'tensor'\n  }]\n}, {\n  'tfOpName': 'Rank',\n  'category': 'graph',\n  'inputs': [{\n    'start': 0,\n    'name': 'x',\n    'type': 'tensor'\n  }]\n}, {\n  'tfOpName': 'Size',\n  'category': 'graph',\n  'inputs': [{\n    'start': 0,\n    'name': 'x',\n    'type': 'tensor'\n  }]\n}, {\n  'tfOpName': 'Shape',\n  'category': 'graph',\n  'inputs': [{\n    'start': 0,\n    'name': 'x',\n    'type': 'tensor'\n  }]\n}, {\n  'tfOpName': 'ShapeN',\n  'category': 'graph',\n  'inputs': [{\n    'start': 0,\n    'end': 0,\n    'name': 'x',\n    'type': 'tensors'\n  }]\n}, {\n  'tfOpName': 'Print',\n  'category': 'graph',\n  'inputs': [{\n    'start': 0,\n    'name': 'x',\n    'type': 'tensor'\n  }, {\n    'start': 1,\n    'name': 'data',\n    'type': 'tensors'\n  }],\n  'attrs': [{\n    'tfName': 'message',\n    'name': 'message',\n    'type': 'string'\n  }, {\n    'tfName': 'first_n',\n    'name': 'firstN',\n    'type': 'number',\n    'notSupported': true\n  }, {\n    'tfName': 'summarize',\n    'name': 'summarize',\n    'type': 'number',\n    'defaultValue': 3\n  }]\n}, {\n  'tfOpName': 'NoOp',\n  'category': 'graph',\n  'inputs': []\n}, {\n  'tfOpName': 'StopGradient',\n  'category': 'graph',\n  'inputs': [{\n    'start': 0,\n    'name': 'x',\n    'type': 'tensor'\n  }]\n}, {\n  'tfOpName': 'FakeQuantWithMinMaxVars',\n  'category': 'graph',\n  'inputs': [{\n    'start': 0,\n    'name': 'x',\n    'type': 'tensor'\n  }],\n  'attrs': [{\n    'tfName': 'min',\n    'name': 'min',\n    'type': 'number'\n  }, {\n    'tfName': 'max',\n    'name': 'max',\n    'type': 'number'\n  }]\n}];", "map": {"version": 3, "names": ["json"], "sources": ["C:\\tfjs-converter\\src\\operations\\op_list\\graph.ts"], "sourcesContent": ["\n/**\n * @license\n * Copyright 2023 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {OpMapper} from '../types';\n\nexport const json: OpMapper[] = [\n  {\n    'tfOpName': 'PlaceholderWithDefault',\n    'category': 'graph',\n    'inputs': [\n      {\n        'start': 0,\n        'name': 'default',\n        'type': 'tensor'\n      }\n    ],\n    'attrs': [\n      {\n        'tfName': 'shape',\n        'name': 'shape',\n        'type': 'shape'\n      },\n      {\n        'tfName': 'dtype',\n        'name': 'dtype',\n        'type': 'dtype'\n      }\n    ]\n  },\n  {\n    'tfOpName': 'Placeholder',\n    'category': 'graph',\n    'attrs': [\n      {\n        'tfName': 'shape',\n        'name': 'shape',\n        'type': 'shape'\n      },\n      {\n        'tfName': 'dtype',\n        'name': 'dtype',\n        'type': 'dtype'\n      }\n    ]\n  },\n  {\n    'tfOpName': 'Const',\n    'category': 'graph'\n  },\n  {\n    'tfOpName': 'Identity',\n    'category': 'graph',\n    'inputs': [\n      {\n        'start': 0,\n        'name': 'x',\n        'type': 'tensor'\n      }\n    ]\n  },\n  {\n    'tfOpName': 'IdentityN',\n    'category': 'graph',\n    'inputs': [\n      {\n        'start': 0,\n        'end': 0,\n        'name': 'x',\n        'type': 'tensors'\n      }\n    ]\n  },\n  {\n    'tfOpName': 'Snapshot',\n    'category': 'graph',\n    'inputs': [\n      {\n        'start': 0,\n        'name': 'x',\n        'type': 'tensor'\n      }\n    ]\n  },\n  {\n    'tfOpName': 'Rank',\n    'category': 'graph',\n    'inputs': [\n      {\n        'start': 0,\n        'name': 'x',\n        'type': 'tensor'\n      }\n    ]\n  },\n  {\n    'tfOpName': 'Size',\n    'category': 'graph',\n    'inputs': [\n      {\n        'start': 0,\n        'name': 'x',\n        'type': 'tensor'\n      }\n    ]\n  },\n  {\n    'tfOpName': 'Shape',\n    'category': 'graph',\n    'inputs': [\n      {\n        'start': 0,\n        'name': 'x',\n        'type': 'tensor'\n      }\n    ]\n  },\n  {\n    'tfOpName': 'ShapeN',\n    'category': 'graph',\n    'inputs': [\n      {\n        'start': 0,\n        'end': 0,\n        'name': 'x',\n        'type': 'tensors'\n      }\n    ]\n  },\n  {\n    'tfOpName': 'Print',\n    'category': 'graph',\n    'inputs': [\n      {\n        'start': 0,\n        'name': 'x',\n        'type': 'tensor'\n      },\n      {\n        'start': 1,\n        'name': 'data',\n        'type': 'tensors'\n      }\n    ],\n    'attrs': [\n      {\n        'tfName': 'message',\n        'name': 'message',\n        'type': 'string'\n      },\n      {\n        'tfName': 'first_n',\n        'name': 'firstN',\n        'type': 'number',\n        'notSupported': true\n      },\n      {\n        'tfName': 'summarize',\n        'name': 'summarize',\n        'type': 'number',\n        'defaultValue': 3\n      }\n    ]\n  },\n  {\n    'tfOpName': 'NoOp',\n    'category': 'graph',\n    'inputs': []\n  },\n  {\n    'tfOpName': 'StopGradient',\n    'category': 'graph',\n    'inputs': [\n      {\n        'start': 0,\n        'name': 'x',\n        'type': 'tensor'\n      }\n    ]\n  },\n  {\n    'tfOpName': 'FakeQuantWithMinMaxVars',\n    'category': 'graph',\n    'inputs': [\n      {\n        'start': 0,\n        'name': 'x',\n        'type': 'tensor'\n      }\n    ],\n    'attrs': [\n      {\n        'tfName': 'min',\n        'name': 'min',\n        'type': 'number'\n      },\n      {\n        'tfName': 'max',\n        'name': 'max',\n        'type': 'number'\n      }\n    ]\n  }\n];\n"], "mappings": "AACA;;;;;;;;;;;;;;;;AAmBA,OAAO,MAAMA,IAAI,GAAe,CAC9B;EACE,UAAU,EAAE,wBAAwB;EACpC,UAAU,EAAE,OAAO;EACnB,QAAQ,EAAE,CACR;IACE,OAAO,EAAE,CAAC;IACV,MAAM,EAAE,SAAS;IACjB,MAAM,EAAE;GACT,CACF;EACD,OAAO,EAAE,CACP;IACE,QAAQ,EAAE,OAAO;IACjB,MAAM,EAAE,OAAO;IACf,MAAM,EAAE;GACT,EACD;IACE,QAAQ,EAAE,OAAO;IACjB,MAAM,EAAE,OAAO;IACf,MAAM,EAAE;GACT;CAEJ,EACD;EACE,UAAU,EAAE,aAAa;EACzB,UAAU,EAAE,OAAO;EACnB,OAAO,EAAE,CACP;IACE,QAAQ,EAAE,OAAO;IACjB,MAAM,EAAE,OAAO;IACf,MAAM,EAAE;GACT,EACD;IACE,QAAQ,EAAE,OAAO;IACjB,MAAM,EAAE,OAAO;IACf,MAAM,EAAE;GACT;CAEJ,EACD;EACE,UAAU,EAAE,OAAO;EACnB,UAAU,EAAE;CACb,EACD;EACE,UAAU,EAAE,UAAU;EACtB,UAAU,EAAE,OAAO;EACnB,QAAQ,EAAE,CACR;IACE,OAAO,EAAE,CAAC;IACV,MAAM,EAAE,GAAG;IACX,MAAM,EAAE;GACT;CAEJ,EACD;EACE,UAAU,EAAE,WAAW;EACvB,UAAU,EAAE,OAAO;EACnB,QAAQ,EAAE,CACR;IACE,OAAO,EAAE,CAAC;IACV,KAAK,EAAE,CAAC;IACR,MAAM,EAAE,GAAG;IACX,MAAM,EAAE;GACT;CAEJ,EACD;EACE,UAAU,EAAE,UAAU;EACtB,UAAU,EAAE,OAAO;EACnB,QAAQ,EAAE,CACR;IACE,OAAO,EAAE,CAAC;IACV,MAAM,EAAE,GAAG;IACX,MAAM,EAAE;GACT;CAEJ,EACD;EACE,UAAU,EAAE,MAAM;EAClB,UAAU,EAAE,OAAO;EACnB,QAAQ,EAAE,CACR;IACE,OAAO,EAAE,CAAC;IACV,MAAM,EAAE,GAAG;IACX,MAAM,EAAE;GACT;CAEJ,EACD;EACE,UAAU,EAAE,MAAM;EAClB,UAAU,EAAE,OAAO;EACnB,QAAQ,EAAE,CACR;IACE,OAAO,EAAE,CAAC;IACV,MAAM,EAAE,GAAG;IACX,MAAM,EAAE;GACT;CAEJ,EACD;EACE,UAAU,EAAE,OAAO;EACnB,UAAU,EAAE,OAAO;EACnB,QAAQ,EAAE,CACR;IACE,OAAO,EAAE,CAAC;IACV,MAAM,EAAE,GAAG;IACX,MAAM,EAAE;GACT;CAEJ,EACD;EACE,UAAU,EAAE,QAAQ;EACpB,UAAU,EAAE,OAAO;EACnB,QAAQ,EAAE,CACR;IACE,OAAO,EAAE,CAAC;IACV,KAAK,EAAE,CAAC;IACR,MAAM,EAAE,GAAG;IACX,MAAM,EAAE;GACT;CAEJ,EACD;EACE,UAAU,EAAE,OAAO;EACnB,UAAU,EAAE,OAAO;EACnB,QAAQ,EAAE,CACR;IACE,OAAO,EAAE,CAAC;IACV,MAAM,EAAE,GAAG;IACX,MAAM,EAAE;GACT,EACD;IACE,OAAO,EAAE,CAAC;IACV,MAAM,EAAE,MAAM;IACd,MAAM,EAAE;GACT,CACF;EACD,OAAO,EAAE,CACP;IACE,QAAQ,EAAE,SAAS;IACnB,MAAM,EAAE,SAAS;IACjB,MAAM,EAAE;GACT,EACD;IACE,QAAQ,EAAE,SAAS;IACnB,MAAM,EAAE,QAAQ;IAChB,MAAM,EAAE,QAAQ;IAChB,cAAc,EAAE;GACjB,EACD;IACE,QAAQ,EAAE,WAAW;IACrB,MAAM,EAAE,WAAW;IACnB,MAAM,EAAE,QAAQ;IAChB,cAAc,EAAE;GACjB;CAEJ,EACD;EACE,UAAU,EAAE,MAAM;EAClB,UAAU,EAAE,OAAO;EACnB,QAAQ,EAAE;CACX,EACD;EACE,UAAU,EAAE,cAAc;EAC1B,UAAU,EAAE,OAAO;EACnB,QAAQ,EAAE,CACR;IACE,OAAO,EAAE,CAAC;IACV,MAAM,EAAE,GAAG;IACX,MAAM,EAAE;GACT;CAEJ,EACD;EACE,UAAU,EAAE,yBAAyB;EACrC,UAAU,EAAE,OAAO;EACnB,QAAQ,EAAE,CACR;IACE,OAAO,EAAE,CAAC;IACV,MAAM,EAAE,GAAG;IACX,MAAM,EAAE;GACT,CACF;EACD,OAAO,EAAE,CACP;IACE,QAAQ,EAAE,KAAK;IACf,MAAM,EAAE,KAAK;IACb,MAAM,EAAE;GACT,EACD;IACE,QAAQ,EAAE,KAAK;IACf,MAAM,EAAE,KAAK;IACb,MAAM,EAAE;GACT;CAEJ,CACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}