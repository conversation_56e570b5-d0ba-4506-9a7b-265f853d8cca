{"ast": null, "code": "/*\nCopyright (c) 2014, <PERSON>\n\nPer<PERSON> is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in\nall copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\nTHE SOFTWARE.\n*/\n\n'use strict';\n\nclass Bag {\n  constructor() {\n    this.dictionary = [];\n    this.nElement = 0;\n  }\n  add(element) {\n    this.dictionary.push(element);\n    this.nElement++;\n    return this;\n  }\n  isEmpty() {\n    return this.nElement > 0;\n  }\n  contains(item) {\n    return this.dictionary.indexOf(item) >= 0;\n  }\n\n  /**\n   * unpack the bag , and get all items\n  */\n  unpack() {\n    // return a copy is better than original\n    return this.dictionary.slice();\n  }\n}\nmodule.exports = Bag;", "map": {"version": 3, "names": ["Bag", "constructor", "dictionary", "nElement", "add", "element", "push", "isEmpty", "contains", "item", "indexOf", "unpack", "slice", "module", "exports"], "sources": ["C:/tmsft/node_modules/natural/lib/natural/util/bag.js"], "sourcesContent": ["/*\nCopyright (c) 2014, <PERSON>\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in\nall copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\nTHE SOFTWARE.\n*/\n\n'use strict'\n\nclass Bag {\n  constructor () {\n    this.dictionary = []\n    this.nElement = 0\n  }\n\n  add (element) {\n    this.dictionary.push(element)\n    this.nElement++\n    return this\n  }\n\n  isEmpty () {\n    return this.nElement > 0\n  }\n\n  contains (item) {\n    return this.dictionary.indexOf(item) >= 0\n  }\n\n  /**\n   * unpack the bag , and get all items\n  */\n  unpack () {\n    // return a copy is better than original\n    return this.dictionary.slice()\n  }\n}\n\nmodule.exports = Bag\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,YAAY;;AAEZ,MAAMA,GAAG,CAAC;EACRC,WAAWA,CAAA,EAAI;IACb,IAAI,CAACC,UAAU,GAAG,EAAE;IACpB,IAAI,CAACC,QAAQ,GAAG,CAAC;EACnB;EAEAC,GAAGA,CAAEC,OAAO,EAAE;IACZ,IAAI,CAACH,UAAU,CAACI,IAAI,CAACD,OAAO,CAAC;IAC7B,IAAI,CAACF,QAAQ,EAAE;IACf,OAAO,IAAI;EACb;EAEAI,OAAOA,CAAA,EAAI;IACT,OAAO,IAAI,CAACJ,QAAQ,GAAG,CAAC;EAC1B;EAEAK,QAAQA,CAAEC,IAAI,EAAE;IACd,OAAO,IAAI,CAACP,UAAU,CAACQ,OAAO,CAACD,IAAI,CAAC,IAAI,CAAC;EAC3C;;EAEA;AACF;AACA;EACEE,MAAMA,CAAA,EAAI;IACR;IACA,OAAO,IAAI,CAACT,UAAU,CAACU,KAAK,CAAC,CAAC;EAChC;AACF;AAEAC,MAAM,CAACC,OAAO,GAAGd,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}