{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.transformArguments = exports.FIRST_KEY_INDEX = void 0;\nconst generic_transformers_1 = require(\"./generic-transformers\");\nexports.FIRST_KEY_INDEX = 1;\nfunction transformArguments(destination, keys, options) {\n  const args = (0, generic_transformers_1.pushVerdictArgument)(['ZINTERSTORE', destination], keys);\n  if (options?.WEIGHTS) {\n    args.push('WEIGHTS', ...options.WEIGHTS.map(weight => weight.toString()));\n  }\n  if (options?.AGGREGATE) {\n    args.push('AGGREGATE', options.AGGREGATE);\n  }\n  return args;\n}\nexports.transformArguments = transformArguments;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "transformArguments", "FIRST_KEY_INDEX", "generic_transformers_1", "require", "destination", "keys", "options", "args", "pushVerdictArgument", "WEIGHTS", "push", "map", "weight", "toString", "AGGREGATE"], "sources": ["C:/tmsft/node_modules/@redis/client/dist/lib/commands/ZINTERSTORE.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.transformArguments = exports.FIRST_KEY_INDEX = void 0;\nconst generic_transformers_1 = require(\"./generic-transformers\");\nexports.FIRST_KEY_INDEX = 1;\nfunction transformArguments(destination, keys, options) {\n    const args = (0, generic_transformers_1.pushVerdictArgument)(['ZINTERSTORE', destination], keys);\n    if (options?.WEIGHTS) {\n        args.push('WEIGHTS', ...options.WEIGHTS.map(weight => weight.toString()));\n    }\n    if (options?.AGGREGATE) {\n        args.push('AGGREGATE', options.AGGREGATE);\n    }\n    return args;\n}\nexports.transformArguments = transformArguments;\n"], "mappings": "AAAA,YAAY;;AACZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEC,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7DD,OAAO,CAACE,kBAAkB,GAAGF,OAAO,CAACG,eAAe,GAAG,KAAK,CAAC;AAC7D,MAAMC,sBAAsB,GAAGC,OAAO,CAAC,wBAAwB,CAAC;AAChEL,OAAO,CAACG,eAAe,GAAG,CAAC;AAC3B,SAASD,kBAAkBA,CAACI,WAAW,EAAEC,IAAI,EAAEC,OAAO,EAAE;EACpD,MAAMC,IAAI,GAAG,CAAC,CAAC,EAAEL,sBAAsB,CAACM,mBAAmB,EAAE,CAAC,aAAa,EAAEJ,WAAW,CAAC,EAAEC,IAAI,CAAC;EAChG,IAAIC,OAAO,EAAEG,OAAO,EAAE;IAClBF,IAAI,CAACG,IAAI,CAAC,SAAS,EAAE,GAAGJ,OAAO,CAACG,OAAO,CAACE,GAAG,CAACC,MAAM,IAAIA,MAAM,CAACC,QAAQ,CAAC,CAAC,CAAC,CAAC;EAC7E;EACA,IAAIP,OAAO,EAAEQ,SAAS,EAAE;IACpBP,IAAI,CAACG,IAAI,CAAC,WAAW,EAAEJ,OAAO,CAACQ,SAAS,CAAC;EAC7C;EACA,OAAOP,IAAI;AACf;AACAT,OAAO,CAACE,kBAAkB,GAAGA,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}