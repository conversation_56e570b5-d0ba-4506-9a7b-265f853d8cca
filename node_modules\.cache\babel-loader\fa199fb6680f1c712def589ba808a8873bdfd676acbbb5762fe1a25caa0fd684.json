{"ast": null, "code": "/**\n * @license\n * Copyright 2023 Google LLC.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { getCoordsDataType } from './shader_compiler';\nexport class ScatterPackedProgram {\n  constructor(updateSize, sliceDim, indicesRank, updatesRank, strides, shape) {\n    let summingDupeIndex = arguments.length > 6 && arguments[6] !== undefined ? arguments[6] : true;\n    let defaultIsTensor = arguments.length > 7 && arguments[7] !== undefined ? arguments[7] : false;\n    this.variableNames = ['updates', 'indices', 'defaultValue'];\n    this.packedInputs = true;\n    this.packedOutput = true;\n    this.outputShape = shape;\n    const stridesType = getCoordsDataType(strides.length);\n    const dtype = getCoordsDataType(shape.length);\n    let indicesString = '';\n    if (indicesRank === 1) {\n      indicesString = 'i';\n    } else if (indicesRank === 2) {\n      indicesString = 'i, j';\n    }\n    const indicesSnippet = \"getIndices(\".concat(indicesString, \")\");\n    let updatesString = '';\n    if (updatesRank === 1) {\n      updatesString = 'i';\n    } else if (updatesRank === 2) {\n      updatesString = 'i, coords[1]';\n    }\n    const updatesSnippet = \"getUpdates(\".concat(updatesString, \")\");\n    let defaultValuesString = '';\n    if (defaultIsTensor) {\n      defaultValuesString = 'coords[0], coords[1]';\n    }\n    const defaultValueSnippet = \"getDefaultValue(\".concat(defaultValuesString, \")\");\n    const strideString = sliceDim > 1 ? 'strides[j]' : 'strides';\n    const strideString2 = sliceDim > 1 ? 'strides[j + 1]' : 'strides';\n    this.userCode = \"\\n        \".concat(stridesType, \" strides = \").concat(stridesType, \"(\").concat(strides, \");\\n\\n        void main() {\\n          \").concat(dtype, \" coords = getOutputCoords();\\n          vec4 sum = vec4(0.);\\n          vec4 found = vec4(0.);\\n          for (int i = 0; i < \").concat(updateSize, \"; i+=2) {\\n            ivec2 flattenedIndex = ivec2(0);\\n            for (int j = 0; j < \").concat(sliceDim, \"; j+=2) {\\n              ivec4 index = round(\").concat(indicesSnippet, \");\\n              flattenedIndex += index.xz * \").concat(strideString, \";\\n              if (j + 1 < \").concat(sliceDim, \") {\\n                flattenedIndex += index.yw * \").concat(strideString2, \";\\n              }\\n            }\\n            if (flattenedIndex[0] == coords[0] || flattenedIndex[1] == coords[0] ||\\n                flattenedIndex[0] == coords[0] + 1 || flattenedIndex[1] == coords[0] + 1) {\\n              vec4 updVals = \").concat(updatesSnippet, \";\\n              if (flattenedIndex[0] == coords[0]) {\\n                sum.xy += updVals.xy;\\n                found.xy = vec2(1.);\\n              } else if (flattenedIndex[0] == coords[0] + 1) {\\n                sum.zw += updVals.xy;\\n                found.zw = vec2(1.);\\n              }\\n              if (flattenedIndex[1] == coords[0]) {\\n                sum.xy += updVals.zw;\\n                found.xy = vec2(1.);\\n              } else if (flattenedIndex[1] == coords[0] + 1) {\\n                sum.zw += updVals.zw;\\n                found.zw = vec2(1.);\\n              }\\n            }\\n          }\\n          setOutput(mix(\").concat(defaultValueSnippet, \", sum, found));\\n        }\\n      \");\n  }\n}", "map": {"version": 3, "names": ["getCoordsDataType", "ScatterPackedProgram", "constructor", "updateSize", "sliceDim", "indicesRank", "updatesRank", "strides", "shape", "summingDupeIndex", "arguments", "length", "undefined", "defaultIsTensor", "variableNames", "packedInputs", "packedOutput", "outputShape", "stridesType", "dtype", "indicesString", "indicesSnippet", "concat", "updatesString", "updatesSnippet", "defaultValuesString", "defaultValueSnippet", "strideString", "strideString2", "userCode"], "sources": ["C:\\tfjs-backend-webgl\\src\\scatter_packed_gpu.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2023 Google LLC.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {GPGPUProgram} from './gpgpu_math';\nimport {getCoordsDataType} from './shader_compiler';\n\nexport class ScatterPackedProgram implements GPGPUProgram {\n  variableNames = ['updates', 'indices', 'defaultValue'];\n  outputShape: number[];\n  packedInputs = true;\n  packedOutput = true;\n  userCode: string;\n\n  constructor(\n      updateSize: number, sliceDim: number, indicesRank: number,\n      updatesRank: number, strides: number[], shape: number[],\n      summingDupeIndex = true, defaultIsTensor = false) {\n    this.outputShape = shape;\n    const stridesType = getCoordsDataType(strides.length);\n    const dtype = getCoordsDataType(shape.length);\n    let indicesString = '';\n    if (indicesRank === 1) {\n      indicesString = 'i';\n    } else if (indicesRank === 2) {\n      indicesString = 'i, j';\n    }\n    const indicesSnippet = `getIndices(${indicesString})`;\n\n    let updatesString = '';\n    if (updatesRank === 1) {\n      updatesString = 'i';\n    } else if (updatesRank === 2) {\n      updatesString = 'i, coords[1]';\n    }\n    const updatesSnippet = `getUpdates(${updatesString})`;\n\n    let defaultValuesString = '';\n    if (defaultIsTensor) {\n      defaultValuesString = 'coords[0], coords[1]';\n    }\n    const defaultValueSnippet = `getDefaultValue(${defaultValuesString})`;\n\n    const strideString = sliceDim > 1 ? 'strides[j]' : 'strides';\n    const strideString2 = sliceDim > 1 ? 'strides[j + 1]' : 'strides';\n\n    this.userCode = `\n        ${stridesType} strides = ${stridesType}(${strides});\n\n        void main() {\n          ${dtype} coords = getOutputCoords();\n          vec4 sum = vec4(0.);\n          vec4 found = vec4(0.);\n          for (int i = 0; i < ${updateSize}; i+=2) {\n            ivec2 flattenedIndex = ivec2(0);\n            for (int j = 0; j < ${sliceDim}; j+=2) {\n              ivec4 index = round(${indicesSnippet});\n              flattenedIndex += index.xz * ${strideString};\n              if (j + 1 < ${sliceDim}) {\n                flattenedIndex += index.yw * ${strideString2};\n              }\n            }\n            if (flattenedIndex[0] == coords[0] || flattenedIndex[1] == coords[0] ||\n                flattenedIndex[0] == coords[0] + 1 || flattenedIndex[1] == coords[0] + 1) {\n              vec4 updVals = ${updatesSnippet};\n              if (flattenedIndex[0] == coords[0]) {\n                sum.xy += updVals.xy;\n                found.xy = vec2(1.);\n              } else if (flattenedIndex[0] == coords[0] + 1) {\n                sum.zw += updVals.xy;\n                found.zw = vec2(1.);\n              }\n              if (flattenedIndex[1] == coords[0]) {\n                sum.xy += updVals.zw;\n                found.xy = vec2(1.);\n              } else if (flattenedIndex[1] == coords[0] + 1) {\n                sum.zw += updVals.zw;\n                found.zw = vec2(1.);\n              }\n            }\n          }\n          setOutput(mix(${defaultValueSnippet}, sum, found));\n        }\n      `;\n  }\n}\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAkBA,SAAQA,iBAAiB,QAAO,mBAAmB;AAEnD,OAAM,MAAOC,oBAAoB;EAO/BC,YACIC,UAAkB,EAAEC,QAAgB,EAAEC,WAAmB,EACzDC,WAAmB,EAAEC,OAAiB,EAAEC,KAAe,EACP;IAAA,IAAhDC,gBAAgB,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,IAAI;IAAA,IAAEG,eAAe,GAAAH,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,KAAK;IATpD,KAAAI,aAAa,GAAG,CAAC,SAAS,EAAE,SAAS,EAAE,cAAc,CAAC;IAEtD,KAAAC,YAAY,GAAG,IAAI;IACnB,KAAAC,YAAY,GAAG,IAAI;IAOjB,IAAI,CAACC,WAAW,GAAGT,KAAK;IACxB,MAAMU,WAAW,GAAGlB,iBAAiB,CAACO,OAAO,CAACI,MAAM,CAAC;IACrD,MAAMQ,KAAK,GAAGnB,iBAAiB,CAACQ,KAAK,CAACG,MAAM,CAAC;IAC7C,IAAIS,aAAa,GAAG,EAAE;IACtB,IAAIf,WAAW,KAAK,CAAC,EAAE;MACrBe,aAAa,GAAG,GAAG;KACpB,MAAM,IAAIf,WAAW,KAAK,CAAC,EAAE;MAC5Be,aAAa,GAAG,MAAM;;IAExB,MAAMC,cAAc,iBAAAC,MAAA,CAAiBF,aAAa,MAAG;IAErD,IAAIG,aAAa,GAAG,EAAE;IACtB,IAAIjB,WAAW,KAAK,CAAC,EAAE;MACrBiB,aAAa,GAAG,GAAG;KACpB,MAAM,IAAIjB,WAAW,KAAK,CAAC,EAAE;MAC5BiB,aAAa,GAAG,cAAc;;IAEhC,MAAMC,cAAc,iBAAAF,MAAA,CAAiBC,aAAa,MAAG;IAErD,IAAIE,mBAAmB,GAAG,EAAE;IAC5B,IAAIZ,eAAe,EAAE;MACnBY,mBAAmB,GAAG,sBAAsB;;IAE9C,MAAMC,mBAAmB,sBAAAJ,MAAA,CAAsBG,mBAAmB,MAAG;IAErE,MAAME,YAAY,GAAGvB,QAAQ,GAAG,CAAC,GAAG,YAAY,GAAG,SAAS;IAC5D,MAAMwB,aAAa,GAAGxB,QAAQ,GAAG,CAAC,GAAG,gBAAgB,GAAG,SAAS;IAEjE,IAAI,CAACyB,QAAQ,gBAAAP,MAAA,CACPJ,WAAW,iBAAAI,MAAA,CAAcJ,WAAW,OAAAI,MAAA,CAAIf,OAAO,6CAAAe,MAAA,CAG7CH,KAAK,oIAAAG,MAAA,CAGenB,UAAU,+FAAAmB,MAAA,CAERlB,QAAQ,mDAAAkB,MAAA,CACND,cAAc,qDAAAC,MAAA,CACLK,YAAY,mCAAAL,MAAA,CAC7BlB,QAAQ,wDAAAkB,MAAA,CACWM,aAAa,wPAAAN,MAAA,CAK7BE,cAAc,6nBAAAF,MAAA,CAiBnBI,mBAAmB,uCAEtC;EACL", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}