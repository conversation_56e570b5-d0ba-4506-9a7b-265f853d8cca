{"ast": null, "code": "/**\n * @license\n * Copyright 2018 Google LLC\n *\n * Use of this source code is governed by an MIT-style\n * license that can be found in the LICENSE file or at\n * https://opensource.org/licenses/MIT.\n * =============================================================================\n */\n/**\n * Layers that augment the functionality of a base layer.\n */\nimport * as tfc from '@tensorflow/tfjs-core';\nimport { serialization, tidy } from '@tensorflow/tfjs-core';\nimport * as K from '../backend/tfjs_backend';\nimport { nameScope } from '../common';\nimport { InputSpec, Layer, SymbolicTensor } from '../engine/topology';\nimport { NotImplementedError, ValueError } from '../errors';\nimport { VALID_BIDIRECTIONAL_MERGE_MODES } from '../keras_format/common';\nimport * as generic_utils from '../utils/generic_utils';\nimport { getExactlyOneShape, getExactlyOneTensor } from '../utils/types_utils';\nimport { rnn, standardizeArgs } from './recurrent';\nimport { deserialize } from './serialization';\n/**\n * Abstract wrapper base class.\n *\n * Wrappers take another layer and augment it in various ways.\n * Do not use this class as a layer, it is only an abstract base class.\n * Two usable wrappers are the `TimeDistributed` and `Bidirectional` wrappers.\n */\nexport class Wrapper extends Layer {\n  constructor(args) {\n    // Porting Note: In PyKeras, `self.layer` is set prior to the calling\n    //   `super()`. But we can't do that here due to TypeScript's restriction.\n    //   See: https://github.com/Microsoft/TypeScript/issues/8277\n    //   As a result, we have to add checks in `get trainable()` and\n    //   `set trainable()` below in order to prevent using `this.layer` when\n    //   its value is `undefined`. The super constructor does use the getter\n    //   and the setter of `this.layer`.\n    super(args);\n    this.layer = args.layer;\n  }\n  build(inputShape) {\n    this.built = true;\n  }\n  // TODO(cais): Implement activityRegularizer getter.\n  get trainable() {\n    // Porting Note: the check of `this.layer` here is necessary due to the\n    //   way the `constructor` of this class is written (see Porting Note\n    //   above).\n    if (this.layer != null) {\n      return this.layer.trainable;\n    } else {\n      return false;\n    }\n  }\n  set trainable(value) {\n    // Porting Note: the check of `this.layer` here is necessary due to the\n    //   way the `constructor` of this class is written (see Porting Note\n    //   above).\n    if (this.layer != null) {\n      this.layer.trainable = value;\n    }\n  }\n  get trainableWeights() {\n    return this.layer.trainableWeights;\n  }\n  // TODO(cais): Implement setter for trainableWeights.\n  get nonTrainableWeights() {\n    return this.layer.nonTrainableWeights;\n  }\n  // TODO(cais): Implement setter for nonTrainableWeights.\n  get updates() {\n    // tslint:disable-next-line:no-any\n    return this.layer._updates;\n  }\n  // TODO(cais): Implement getUpdatesFor().\n  get losses() {\n    return this.layer.losses;\n  }\n  // TODO(cais): Implement getLossesFor().\n  getWeights() {\n    return this.layer.getWeights();\n  }\n  setWeights(weights) {\n    this.layer.setWeights(weights);\n  }\n  getConfig() {\n    const config = {\n      'layer': {\n        'className': this.layer.getClassName(),\n        'config': this.layer.getConfig()\n      }\n    };\n    const baseConfig = super.getConfig();\n    Object.assign(config, baseConfig);\n    return config;\n  }\n  setFastWeightInitDuringBuild(value) {\n    super.setFastWeightInitDuringBuild(value);\n    if (this.layer != null) {\n      this.layer.setFastWeightInitDuringBuild(value);\n    }\n  }\n  /** @nocollapse */\n  static fromConfig(cls, config) {\n    let customObjects = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n    const layerConfig = config['layer'];\n    const layer = deserialize(layerConfig, customObjects);\n    delete config['layer'];\n    const newConfig = {\n      layer\n    };\n    Object.assign(newConfig, config);\n    return new cls(newConfig);\n  }\n}\nclass TimeDistributed extends Wrapper {\n  constructor(args) {\n    super(args);\n    this.supportsMasking = true;\n  }\n  build(inputShape) {\n    inputShape = getExactlyOneShape(inputShape);\n    if (inputShape.length < 3) {\n      throw new ValueError(\"TimeDistributed layer expects an input shape >= 3D, but received \" + \"input shape \".concat(JSON.stringify(inputShape)));\n    }\n    this.inputSpec = [{\n      shape: inputShape\n    }];\n    const childInputShape = [inputShape[0]].concat(inputShape.slice(2));\n    if (!this.layer.built) {\n      this.layer.build(childInputShape);\n      this.layer.built = true;\n    }\n    super.build(inputShape);\n  }\n  computeOutputShape(inputShape) {\n    inputShape = getExactlyOneShape(inputShape);\n    const childInputShape = [inputShape[0]].concat(inputShape.slice(2));\n    const childOutputShape = this.layer.computeOutputShape(childInputShape);\n    const timesteps = inputShape[1];\n    return [childOutputShape[0], timesteps].concat(childOutputShape.slice(1));\n  }\n  call(inputs, kwargs) {\n    return tidy(() => {\n      // TODO(cais): Add 'training' and 'useLearningPhase' to kwargs.\n      inputs = getExactlyOneTensor(inputs);\n      // Porting Note: In tfjs-layers, `inputs` are always concrete tensor\n      // values. Hence the inputs can't have an undetermined first (batch)\n      // dimension, which is why we always use the K.rnn approach here.\n      const step = (inputs, states) => {\n        // TODO(cais): Add useLearningPhase.\n        // NOTE(cais): `layer.call` may return a length-1 array of Tensor in\n        //   some cases (e.g., `layer` is a `Sequential` instance), which is\n        //   why `getExactlyOneTensor` is used below.\n        const output = getExactlyOneTensor(this.layer.call(inputs, kwargs));\n        return [output, []];\n      };\n      const rnnOutputs = rnn(step, inputs, [], false /* goBackwards */, null /* mask */, null /* constants */, false /* unroll */, true /* needPerStepOutputs */);\n      const y = rnnOutputs[1];\n      // TODO(cais): Add activity regularization.\n      // TODO(cais): Add useLearningPhase.\n      return y;\n    });\n  }\n}\n/** @nocollapse */\nTimeDistributed.className = 'TimeDistributed';\nexport { TimeDistributed };\nserialization.registerClass(TimeDistributed);\nexport function checkBidirectionalMergeMode(value) {\n  generic_utils.checkStringTypeUnionValue(VALID_BIDIRECTIONAL_MERGE_MODES, 'BidirectionalMergeMode', value);\n}\nconst DEFAULT_BIDIRECTIONAL_MERGE_MODE = 'concat';\nclass Bidirectional extends Wrapper {\n  constructor(args) {\n    super(args);\n    // Note: When creating `this.forwardLayer`, the original Layer object\n    //   (`config.layer`) ought to be cloned. This is why we call\n    //   `getConfig()` followed by `deserialize()`. Without this cloning,\n    //   the layer names saved during serialization will incorrectly contain\n    //   the 'forward_' prefix. In Python Keras, this is done using\n    //   `copy.copy` (shallow copy), which does not have a simple equivalent\n    //   in JavaScript. JavaScript's `Object.assign()` does not copy\n    //   methods.\n    const layerConfig = args.layer.getConfig();\n    const forwDict = {};\n    forwDict['className'] = args.layer.getClassName();\n    forwDict['config'] = layerConfig;\n    this.forwardLayer = deserialize(forwDict);\n    layerConfig['goBackwards'] = layerConfig['goBackwards'] === true ? false : true;\n    const backDict = {};\n    backDict['className'] = args.layer.getClassName();\n    backDict['config'] = layerConfig;\n    this.backwardLayer = deserialize(backDict);\n    this.forwardLayer.name = 'forward_' + this.forwardLayer.name;\n    this.backwardLayer.name = 'backward_' + this.backwardLayer.name;\n    this.mergeMode = args.mergeMode === undefined ? DEFAULT_BIDIRECTIONAL_MERGE_MODE : args.mergeMode;\n    checkBidirectionalMergeMode(this.mergeMode);\n    if (args.weights) {\n      throw new NotImplementedError('weights support is not implemented for Bidirectional layer yet.');\n    }\n    this._stateful = args.layer.stateful;\n    this.returnSequences = args.layer.returnSequences;\n    this.returnState = args.layer.returnState;\n    this.supportsMasking = true;\n    this._trainable = true;\n    this.inputSpec = args.layer.inputSpec;\n    this.numConstants = null;\n  }\n  get trainable() {\n    return this._trainable;\n  }\n  set trainable(value) {\n    // Porting Note: the check of `this.layer` here is necessary due to the\n    //   way the `constructor` of this class is written (see Porting Note\n    //   above).\n    this._trainable = value;\n    if (this.forwardLayer != null) {\n      this.forwardLayer.trainable = value;\n    }\n    if (this.backwardLayer != null) {\n      this.backwardLayer.trainable = value;\n    }\n  }\n  getWeights() {\n    return this.forwardLayer.getWeights().concat(this.backwardLayer.getWeights());\n  }\n  setWeights(weights) {\n    const numWeights = weights.length;\n    const numeightsOver2 = Math.floor(numWeights / 2);\n    this.forwardLayer.setWeights(weights.slice(0, numeightsOver2));\n    this.backwardLayer.setWeights(weights.slice(numeightsOver2));\n  }\n  computeOutputShape(inputShape) {\n    let layerShapes = this.forwardLayer.computeOutputShape(inputShape);\n    if (!(Array.isArray(layerShapes) && Array.isArray(layerShapes[0]))) {\n      layerShapes = [layerShapes];\n    }\n    layerShapes = layerShapes;\n    let outputShape;\n    let outputShapes;\n    let stateShape;\n    if (this.returnState) {\n      stateShape = layerShapes.slice(1);\n      outputShape = layerShapes[0];\n    } else {\n      outputShape = layerShapes[0];\n    }\n    outputShape = outputShape;\n    if (this.mergeMode === 'concat') {\n      outputShape[outputShape.length - 1] *= 2;\n      outputShapes = [outputShape];\n    } else if (this.mergeMode == null) {\n      outputShapes = [outputShape, outputShape.slice()];\n    } else {\n      outputShapes = [outputShape];\n    }\n    if (this.returnState) {\n      if (this.mergeMode == null) {\n        return outputShapes.concat(stateShape).concat(stateShape.slice());\n      }\n      return [outputShape].concat(stateShape).concat(stateShape.slice());\n    }\n    return generic_utils.singletonOrArray(outputShapes);\n  }\n  apply(inputs, kwargs) {\n    let initialState = kwargs == null ? null : kwargs['initialState'];\n    let constants = kwargs == null ? null : kwargs['constants'];\n    if (kwargs == null) {\n      kwargs = {};\n    }\n    const standardized = standardizeArgs(inputs, initialState, constants, this.numConstants);\n    inputs = standardized.inputs;\n    initialState = standardized.initialState;\n    constants = standardized.constants;\n    if (Array.isArray(inputs)) {\n      initialState = inputs.slice(1);\n      inputs = inputs[0];\n    }\n    if ((initialState == null || initialState.length === 0) && constants == null) {\n      return super.apply(inputs, kwargs);\n    }\n    const additionalInputs = [];\n    const additionalSpecs = [];\n    if (initialState != null) {\n      const numStates = initialState.length;\n      if (numStates % 2 > 0) {\n        throw new ValueError('When passing `initialState` to a Bidrectional RNN, ' + 'the state should be an Array containing the states of ' + 'the underlying RNNs.');\n      }\n      kwargs['initialState'] = initialState;\n      additionalInputs.push(...initialState);\n      const stateSpecs = initialState.map(state => new InputSpec({\n        shape: state.shape\n      }));\n      this.forwardLayer.stateSpec = stateSpecs.slice(0, numStates / 2);\n      this.backwardLayer.stateSpec = stateSpecs.slice(numStates / 2);\n      additionalSpecs.push(...stateSpecs);\n    }\n    if (constants != null) {\n      throw new NotImplementedError('Support for constants in Bidirectional layers is not ' + 'implemented yet.');\n    }\n    const isSymbolicTensor = additionalInputs[0] instanceof SymbolicTensor;\n    for (const tensor of additionalInputs) {\n      if (tensor instanceof SymbolicTensor !== isSymbolicTensor) {\n        throw new ValueError('The initial state of a Bidirectional layer cannot be ' + 'specified as a mix of symbolic and non-symbolic tensors');\n      }\n    }\n    if (isSymbolicTensor) {\n      // Compute the full input and specs, including the states.\n      const fullInput = [inputs].concat(additionalInputs);\n      const fullInputSpec = this.inputSpec.concat(additionalSpecs);\n      // Perform the call temporarily and replace inputSpec.\n      // Note: with initial states symbolic calls and non-symbolic calls to\n      // this method differ in how the initial states are passed. For\n      // symbolic calls, the initial states are passed in the first arg, as\n      // an Array of SymbolicTensors; for non-symbolic calls, they are\n      // passed in the second arg as a part of the kwargs. Hence the need to\n      // temporarily modify inputSpec here.\n      // TODO(cais): Make refactoring so that this hacky code below is no\n      // longer needed.\n      const originalInputSpec = this.inputSpec;\n      this.inputSpec = fullInputSpec;\n      const output = super.apply(fullInput, kwargs);\n      this.inputSpec = originalInputSpec;\n      return output;\n    } else {\n      return super.apply(inputs, kwargs);\n    }\n  }\n  call(inputs, kwargs) {\n    return tidy(() => {\n      const initialState = kwargs['initialState'];\n      let y;\n      let yRev;\n      if (initialState == null) {\n        y = this.forwardLayer.call(inputs, kwargs);\n        yRev = this.backwardLayer.call(inputs, kwargs);\n      } else {\n        const forwardState = initialState.slice(0, initialState.length / 2);\n        const backwardState = initialState.slice(initialState.length / 2);\n        y = this.forwardLayer.call(inputs, Object.assign(kwargs, {\n          initialState: forwardState\n        }));\n        yRev = this.backwardLayer.call(inputs, Object.assign(kwargs, {\n          initialState: backwardState\n        }));\n      }\n      let states;\n      if (this.returnState) {\n        if (Array.isArray(y)) {\n          states = y.slice(1).concat(yRev.slice(1));\n        } else {}\n        y = y[0];\n        yRev = yRev[0];\n      }\n      if (this.returnSequences) {\n        yRev = tfc.reverse(yRev, 1);\n      }\n      let output;\n      if (this.mergeMode === 'concat') {\n        output = K.concatenate([y, yRev]);\n      } else if (this.mergeMode === 'sum') {\n        output = tfc.add(y, yRev);\n      } else if (this.mergeMode === 'ave') {\n        output = tfc.mul(.5, tfc.add(y, yRev));\n      } else if (this.mergeMode === 'mul') {\n        output = tfc.mul(y, yRev);\n      } else if (this.mergeMode == null) {\n        output = [y, yRev];\n      }\n      // TODO(cais): Properly set learning phase.\n      if (this.returnState) {\n        if (this.mergeMode == null) {\n          return output.concat(states);\n        }\n        return [output].concat(states);\n      }\n      return output;\n    });\n  }\n  resetStates(states) {\n    this.forwardLayer.resetStates();\n    this.backwardLayer.resetStates();\n  }\n  build(inputShape) {\n    nameScope(this.forwardLayer.name, () => {\n      this.forwardLayer.build(inputShape);\n    });\n    nameScope(this.backwardLayer.name, () => {\n      this.backwardLayer.build(inputShape);\n    });\n    this.built = true;\n  }\n  computeMask(inputs, mask) {\n    if (Array.isArray(mask)) {\n      mask = mask[0];\n    }\n    let outputMask;\n    if (this.returnSequences) {\n      if (this.mergeMode == null) {\n        outputMask = [mask, mask];\n      } else {\n        outputMask = mask;\n      }\n    } else {\n      if (this.mergeMode == null) {\n        outputMask = [null, null];\n      } else {\n        outputMask = null;\n      }\n    }\n    if (this.returnState) {\n      const states = this.forwardLayer.states;\n      const stateMask = states.map(state => null);\n      if (Array.isArray(outputMask)) {\n        return outputMask.concat(stateMask).concat(stateMask);\n      } else {\n        return [outputMask].concat(stateMask).concat(stateMask);\n      }\n    } else {\n      return outputMask;\n    }\n  }\n  get trainableWeights() {\n    return this.forwardLayer.trainableWeights.concat(this.backwardLayer.trainableWeights);\n  }\n  get nonTrainableWeights() {\n    return this.forwardLayer.nonTrainableWeights.concat(this.backwardLayer.nonTrainableWeights);\n  }\n  // TODO(cais): Implement constraints().\n  setFastWeightInitDuringBuild(value) {\n    super.setFastWeightInitDuringBuild(value);\n    if (this.forwardLayer != null) {\n      this.forwardLayer.setFastWeightInitDuringBuild(value);\n    }\n    if (this.backwardLayer != null) {\n      this.backwardLayer.setFastWeightInitDuringBuild(value);\n    }\n  }\n  getConfig() {\n    const config = {\n      'mergeMode': this.mergeMode\n    };\n    // TODO(cais): Add logic for `numConstants` once the property is added.\n    const baseConfig = super.getConfig();\n    Object.assign(config, baseConfig);\n    return config;\n  }\n  /** @nocollapse */\n  static fromConfig(cls, config) {\n    const rnnLayer = deserialize(config['layer']);\n    delete config['layer'];\n    // TODO(cais): Add logic for `numConstants` once the property is added.\n    if (config['numConstants'] != null) {\n      throw new NotImplementedError(\"Deserialization of a Bidirectional layer with numConstants \" + \"present is not supported yet.\");\n    }\n    // tslint:disable-next-line:no-any\n    const newConfig = config;\n    newConfig['layer'] = rnnLayer;\n    return new cls(newConfig);\n  }\n}\n/** @nocollapse */\nBidirectional.className = 'Bidirectional';\nexport { Bidirectional };\nserialization.registerClass(Bidirectional);", "map": {"version": 3, "names": ["tfc", "serialization", "tidy", "K", "nameScope", "InputSpec", "Layer", "SymbolicTensor", "NotImplementedError", "ValueError", "VALID_BIDIRECTIONAL_MERGE_MODES", "generic_utils", "getExactlyOneShape", "getExactlyOneTensor", "rnn", "standardizeArgs", "deserialize", "Wrapper", "constructor", "args", "layer", "build", "inputShape", "built", "trainable", "value", "trainableWeights", "nonTrainableWeights", "updates", "_updates", "losses", "getWeights", "setWeights", "weights", "getConfig", "config", "getClassName", "baseConfig", "Object", "assign", "setFastWeightInitDuringBuild", "fromConfig", "cls", "customObjects", "arguments", "length", "undefined", "layerConfig", "newConfig", "TimeDistributed", "supportsMasking", "concat", "JSON", "stringify", "inputSpec", "shape", "childInputShape", "slice", "computeOutputShape", "childOutputShape", "timesteps", "call", "inputs", "kwargs", "step", "states", "output", "rnnOutputs", "y", "className", "registerClass", "checkBidirectionalMergeMode", "checkStringTypeUnionValue", "DEFAULT_BIDIRECTIONAL_MERGE_MODE", "Bidirectional", "forwDict", "<PERSON><PERSON><PERSON><PERSON>", "backDict", "<PERSON><PERSON><PERSON><PERSON>", "name", "mergeMode", "_stateful", "stateful", "returnSequences", "returnState", "_trainable", "numConstants", "numWeights", "numeightsOver2", "Math", "floor", "layerShapes", "Array", "isArray", "outputShape", "outputShapes", "stateShape", "singletonOrArray", "apply", "initialState", "constants", "standardized", "additionalInputs", "additionalSpecs", "numStates", "push", "stateSpecs", "map", "state", "stateSpec", "isSymbolicTensor", "tensor", "fullInput", "fullInputSpec", "originalInputSpec", "yRev", "forwardState", "backwardState", "reverse", "concatenate", "add", "mul", "resetStates", "computeMask", "mask", "outputMask", "stateMask", "rnn<PERSON><PERSON><PERSON>"], "sources": ["C:\\tfjs-layers\\src\\layers\\wrappers.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2018 Google LLC\n *\n * Use of this source code is governed by an MIT-style\n * license that can be found in the LICENSE file or at\n * https://opensource.org/licenses/MIT.\n * =============================================================================\n */\n\n/**\n * Layers that augment the functionality of a base layer.\n */\n\nimport * as tfc from '@tensorflow/tfjs-core';\nimport {serialization, Tensor, tidy} from '@tensorflow/tfjs-core';\nimport * as K from '../backend/tfjs_backend';\nimport {nameScope} from '../common';\nimport {InputSpec, Layer, LayerArgs, SymbolicTensor} from '../engine/topology';\nimport {NotImplementedError, ValueError} from '../errors';\nimport {BidirectionalMergeMode, Shape, VALID_BIDIRECTIONAL_MERGE_MODES} from '../keras_format/common';\nimport {Kwargs} from '../types';\nimport {RegularizerFn, RnnStepFunction} from '../types';\nimport * as generic_utils from '../utils/generic_utils';\nimport {getExactlyOneShape, getExactlyOneTensor} from '../utils/types_utils';\nimport {LayerVariable} from '../variables';\n\nimport {rnn, RNN, standardizeArgs} from './recurrent';\nimport {deserialize} from './serialization';\n\nexport declare interface WrapperLayerArgs extends LayerArgs {\n  /**\n   * The layer to be wrapped.\n   */\n  layer: Layer;\n}\n\n/**\n * Abstract wrapper base class.\n *\n * Wrappers take another layer and augment it in various ways.\n * Do not use this class as a layer, it is only an abstract base class.\n * Two usable wrappers are the `TimeDistributed` and `Bidirectional` wrappers.\n */\nexport abstract class Wrapper extends Layer {\n  readonly layer: Layer;\n\n  constructor(args: WrapperLayerArgs) {\n    // Porting Note: In PyKeras, `self.layer` is set prior to the calling\n    //   `super()`. But we can't do that here due to TypeScript's restriction.\n    //   See: https://github.com/Microsoft/TypeScript/issues/8277\n    //   As a result, we have to add checks in `get trainable()` and\n    //   `set trainable()` below in order to prevent using `this.layer` when\n    //   its value is `undefined`. The super constructor does use the getter\n    //   and the setter of `this.layer`.\n    super(args);\n    this.layer = args.layer;\n  }\n\n  override build(inputShape: Shape|Shape[]): void {\n    this.built = true;\n  }\n\n  // TODO(cais): Implement activityRegularizer getter.\n\n  override get trainable(): boolean {\n    // Porting Note: the check of `this.layer` here is necessary due to the\n    //   way the `constructor` of this class is written (see Porting Note\n    //   above).\n    if (this.layer != null) {\n      return this.layer.trainable;\n    } else {\n      return false;\n    }\n  }\n\n  override set trainable(value: boolean) {\n    // Porting Note: the check of `this.layer` here is necessary due to the\n    //   way the `constructor` of this class is written (see Porting Note\n    //   above).\n    if (this.layer != null) {\n      this.layer.trainable = value;\n    }\n  }\n\n  override get trainableWeights(): LayerVariable[] {\n    return this.layer.trainableWeights;\n  }\n  // TODO(cais): Implement setter for trainableWeights.\n\n  override get nonTrainableWeights(): LayerVariable[] {\n    return this.layer.nonTrainableWeights;\n  }\n  // TODO(cais): Implement setter for nonTrainableWeights.\n\n  override get updates(): Tensor[] {\n    // tslint:disable-next-line:no-any\n    return (this.layer as any)._updates;\n  }\n\n  // TODO(cais): Implement getUpdatesFor().\n\n  override get losses(): RegularizerFn[] {\n    return this.layer.losses;\n  }\n\n  // TODO(cais): Implement getLossesFor().\n\n  override getWeights(): Tensor[] {\n    return this.layer.getWeights();\n  }\n\n  override setWeights(weights: Tensor[]): void {\n    this.layer.setWeights(weights);\n  }\n\n  override getConfig(): serialization.ConfigDict {\n    const config: serialization.ConfigDict = {\n      'layer': {\n        'className': this.layer.getClassName(),\n        'config': this.layer.getConfig(),\n      }\n    };\n    const baseConfig = super.getConfig();\n    Object.assign(config, baseConfig);\n    return config;\n  }\n\n  override setFastWeightInitDuringBuild(value: boolean) {\n    super.setFastWeightInitDuringBuild(value);\n    if (this.layer != null) {\n      this.layer.setFastWeightInitDuringBuild(value);\n    }\n  }\n\n  /** @nocollapse */\n  static override fromConfig<T extends serialization.Serializable>(\n      cls: serialization.SerializableConstructor<T>,\n      config: serialization.ConfigDict,\n      customObjects = {} as serialization.ConfigDict): T {\n    const layerConfig = config['layer'] as serialization.ConfigDict;\n    const layer = deserialize(layerConfig, customObjects) as Layer;\n    delete config['layer'];\n    const newConfig = {layer};\n    Object.assign(newConfig, config);\n    return new cls(newConfig);\n  }\n}\n\nexport class TimeDistributed extends Wrapper {\n  /** @nocollapse */\n  static className = 'TimeDistributed';\n  constructor(args: WrapperLayerArgs) {\n    super(args);\n    this.supportsMasking = true;\n  }\n\n  override build(inputShape: Shape|Shape[]): void {\n    inputShape = getExactlyOneShape(inputShape);\n    if (inputShape.length < 3) {\n      throw new ValueError(\n          `TimeDistributed layer expects an input shape >= 3D, but received ` +\n          `input shape ${JSON.stringify(inputShape)}`);\n    }\n    this.inputSpec = [{shape: inputShape}];\n    const childInputShape = [inputShape[0]].concat(inputShape.slice(2));\n    if (!this.layer.built) {\n      this.layer.build(childInputShape);\n      this.layer.built = true;\n    }\n    super.build(inputShape);\n  }\n\n  override computeOutputShape(inputShape: Shape|Shape[]): Shape|Shape[] {\n    inputShape = getExactlyOneShape(inputShape);\n    const childInputShape = [inputShape[0]].concat(inputShape.slice(2));\n    const childOutputShape =\n        this.layer.computeOutputShape(childInputShape) as Shape;\n    const timesteps = inputShape[1];\n    return [childOutputShape[0], timesteps].concat(childOutputShape.slice(1));\n  }\n\n  override call(inputs: Tensor|Tensor[], kwargs: Kwargs): Tensor|Tensor[] {\n    return tidy(() => {\n      // TODO(cais): Add 'training' and 'useLearningPhase' to kwargs.\n      inputs = getExactlyOneTensor(inputs);\n      // Porting Note: In tfjs-layers, `inputs` are always concrete tensor\n      // values. Hence the inputs can't have an undetermined first (batch)\n      // dimension, which is why we always use the K.rnn approach here.\n      const step: RnnStepFunction = (inputs: Tensor, states: Tensor[]) => {\n        // TODO(cais): Add useLearningPhase.\n        // NOTE(cais): `layer.call` may return a length-1 array of Tensor in\n        //   some cases (e.g., `layer` is a `Sequential` instance), which is\n        //   why `getExactlyOneTensor` is used below.\n        const output = getExactlyOneTensor(this.layer.call(inputs, kwargs));\n        return [output, []];\n      };\n      const rnnOutputs =\n          rnn(step, inputs, [], false /* goBackwards */, null /* mask */,\n              null /* constants */, false /* unroll */,\n              true /* needPerStepOutputs */);\n      const y = rnnOutputs[1];\n      // TODO(cais): Add activity regularization.\n      // TODO(cais): Add useLearningPhase.\n      return y;\n    });\n  }\n\n  // TODO(cais): Implement detailed computeMask() logic.\n}\nserialization.registerClass(TimeDistributed);\n\nexport function checkBidirectionalMergeMode(value?: string): void {\n  generic_utils.checkStringTypeUnionValue(\n      VALID_BIDIRECTIONAL_MERGE_MODES, 'BidirectionalMergeMode', value);\n}\n\nexport declare interface BidirectionalLayerArgs extends WrapperLayerArgs {\n  /**\n   * The instance of an `RNN` layer to be wrapped.\n   */\n  layer: RNN;\n\n  /**\n   * Mode by which outputs of the forward and backward RNNs are\n   * combined. If `null` or `undefined`, the output will not be\n   * combined, they will be returned as an `Array`.\n   *\n   * If `undefined` (i.e., not provided), defaults to `'concat'`.\n   */\n  mergeMode?: BidirectionalMergeMode;\n}\n\nconst DEFAULT_BIDIRECTIONAL_MERGE_MODE: BidirectionalMergeMode = 'concat';\n\nexport class Bidirectional extends Wrapper {\n  /** @nocollapse */\n  static className = 'Bidirectional';\n  mergeMode: BidirectionalMergeMode;\n  private forwardLayer: RNN;\n  private backwardLayer: RNN;\n  private returnSequences: boolean;\n  private returnState: boolean;\n  private numConstants?: number;\n  private _trainable: boolean;\n\n  constructor(args: BidirectionalLayerArgs) {\n    super(args);\n\n    // Note: When creating `this.forwardLayer`, the original Layer object\n    //   (`config.layer`) ought to be cloned. This is why we call\n    //   `getConfig()` followed by `deserialize()`. Without this cloning,\n    //   the layer names saved during serialization will incorrectly contain\n    //   the 'forward_' prefix. In Python Keras, this is done using\n    //   `copy.copy` (shallow copy), which does not have a simple equivalent\n    //   in JavaScript. JavaScript's `Object.assign()` does not copy\n    //   methods.\n    const layerConfig = args.layer.getConfig();\n    const forwDict: serialization.ConfigDict = {};\n    forwDict['className'] = args.layer.getClassName();\n    forwDict['config'] = layerConfig;\n    this.forwardLayer = deserialize(forwDict) as RNN;\n    layerConfig['goBackwards'] =\n        layerConfig['goBackwards'] === true ? false : true;\n    const backDict: serialization.ConfigDict = {};\n    backDict['className'] = args.layer.getClassName();\n    backDict['config'] = layerConfig;\n    this.backwardLayer = deserialize(backDict) as RNN;\n    this.forwardLayer.name = 'forward_' + this.forwardLayer.name;\n    this.backwardLayer.name = 'backward_' + this.backwardLayer.name;\n\n    this.mergeMode = args.mergeMode === undefined ?\n        DEFAULT_BIDIRECTIONAL_MERGE_MODE :\n        args.mergeMode;\n    checkBidirectionalMergeMode(this.mergeMode);\n    if (args.weights) {\n      throw new NotImplementedError(\n          'weights support is not implemented for Bidirectional layer yet.');\n    }\n    this._stateful = args.layer.stateful;\n    this.returnSequences = args.layer.returnSequences;\n    this.returnState = args.layer.returnState;\n    this.supportsMasking = true;\n    this._trainable = true;\n    this.inputSpec = args.layer.inputSpec;\n    this.numConstants = null;\n  }\n\n  override get trainable(): boolean {\n    return this._trainable;\n  }\n\n  override set trainable(value: boolean) {\n    // Porting Note: the check of `this.layer` here is necessary due to the\n    //   way the `constructor` of this class is written (see Porting Note\n    //   above).\n    this._trainable = value;\n    if (this.forwardLayer != null) {\n      this.forwardLayer.trainable = value;\n    }\n    if (this.backwardLayer != null) {\n      this.backwardLayer.trainable = value;\n    }\n  }\n\n  override getWeights(): Tensor[] {\n    return this.forwardLayer.getWeights().concat(\n        this.backwardLayer.getWeights());\n  }\n\n  override setWeights(weights: Tensor[]): void {\n    const numWeights = weights.length;\n    const numeightsOver2 = Math.floor(numWeights / 2);\n    this.forwardLayer.setWeights(weights.slice(0, numeightsOver2));\n    this.backwardLayer.setWeights(weights.slice(numeightsOver2));\n  }\n\n  override computeOutputShape(inputShape: Shape|Shape[]): Shape|Shape[] {\n    let layerShapes: Shape|Shape[] =\n        this.forwardLayer.computeOutputShape(inputShape);\n    if (!(Array.isArray(layerShapes) && Array.isArray(layerShapes[0]))) {\n      layerShapes = [layerShapes as Shape];\n    }\n    layerShapes = layerShapes as Shape[];\n\n    let outputShape: Shape;\n    let outputShapes: Shape[];\n    let stateShape: Shape[];\n    if (this.returnState) {\n      stateShape = layerShapes.slice(1);\n      outputShape = layerShapes[0];\n    } else {\n      outputShape = layerShapes[0];\n    }\n    outputShape = outputShape;\n    if (this.mergeMode === 'concat') {\n      outputShape[outputShape.length - 1] *= 2;\n      outputShapes = [outputShape];\n    } else if (this.mergeMode == null) {\n      outputShapes = [outputShape, outputShape.slice()];\n    } else {\n      outputShapes = [outputShape];\n    }\n\n    if (this.returnState) {\n      if (this.mergeMode == null) {\n        return outputShapes.concat(stateShape).concat(stateShape.slice());\n      }\n      return [outputShape].concat(stateShape).concat(stateShape.slice());\n    }\n    return generic_utils.singletonOrArray(outputShapes);\n  }\n\n  override apply(\n      inputs: Tensor|Tensor[]|SymbolicTensor|SymbolicTensor[],\n      kwargs?: Kwargs): Tensor|Tensor[]|SymbolicTensor|SymbolicTensor[] {\n    let initialState: Tensor[]|SymbolicTensor[] =\n        kwargs == null ? null : kwargs['initialState'];\n    let constants: Tensor[]|SymbolicTensor[] =\n        kwargs == null ? null : kwargs['constants'];\n    if (kwargs == null) {\n      kwargs = {};\n    }\n    const standardized =\n        standardizeArgs(inputs, initialState, constants, this.numConstants);\n    inputs = standardized.inputs;\n    initialState = standardized.initialState;\n    constants = standardized.constants;\n\n    if (Array.isArray(inputs)) {\n      initialState = (inputs as Tensor[] | SymbolicTensor[]).slice(1);\n      inputs = (inputs as Tensor[] | SymbolicTensor[])[0];\n    }\n\n    if ((initialState == null || initialState.length === 0) &&\n        constants == null) {\n      return super.apply(inputs, kwargs);\n    }\n    const additionalInputs: Array<Tensor|SymbolicTensor> = [];\n    const additionalSpecs: InputSpec[] = [];\n    if (initialState != null) {\n      const numStates = initialState.length;\n      if (numStates % 2 > 0) {\n        throw new ValueError(\n            'When passing `initialState` to a Bidrectional RNN, ' +\n            'the state should be an Array containing the states of ' +\n            'the underlying RNNs.');\n      }\n      kwargs['initialState'] = initialState;\n      additionalInputs.push(...initialState);\n      const stateSpecs = (initialState as Array<Tensor|SymbolicTensor>)\n                             .map(state => new InputSpec({shape: state.shape}));\n      this.forwardLayer.stateSpec = stateSpecs.slice(0, numStates / 2);\n      this.backwardLayer.stateSpec = stateSpecs.slice(numStates / 2);\n      additionalSpecs.push(...stateSpecs);\n    }\n    if (constants != null) {\n      throw new NotImplementedError(\n          'Support for constants in Bidirectional layers is not ' +\n          'implemented yet.');\n    }\n\n    const isSymbolicTensor = additionalInputs[0] instanceof SymbolicTensor;\n    for (const tensor of additionalInputs) {\n      if (tensor instanceof SymbolicTensor !== isSymbolicTensor) {\n        throw new ValueError(\n            'The initial state of a Bidirectional layer cannot be ' +\n            'specified as a mix of symbolic and non-symbolic tensors');\n      }\n    }\n\n    if (isSymbolicTensor) {\n      // Compute the full input and specs, including the states.\n      const fullInput = [inputs].concat(additionalInputs);\n      const fullInputSpec = this.inputSpec.concat(additionalSpecs);\n      // Perform the call temporarily and replace inputSpec.\n      // Note: with initial states symbolic calls and non-symbolic calls to\n      // this method differ in how the initial states are passed. For\n      // symbolic calls, the initial states are passed in the first arg, as\n      // an Array of SymbolicTensors; for non-symbolic calls, they are\n      // passed in the second arg as a part of the kwargs. Hence the need to\n      // temporarily modify inputSpec here.\n      // TODO(cais): Make refactoring so that this hacky code below is no\n      // longer needed.\n      const originalInputSpec = this.inputSpec;\n      this.inputSpec = fullInputSpec;\n      const output =\n          super.apply(fullInput as Tensor[] | SymbolicTensor[], kwargs);\n      this.inputSpec = originalInputSpec;\n      return output;\n    } else {\n      return super.apply(inputs, kwargs);\n    }\n  }\n\n  override call(inputs: Tensor|Tensor[], kwargs: Kwargs): Tensor|Tensor[] {\n    return tidy(() => {\n      const initialState = kwargs['initialState'];\n\n      let y: Tensor|Tensor[];\n      let yRev: Tensor|Tensor[];\n      if (initialState == null) {\n        y = this.forwardLayer.call(inputs, kwargs);\n        yRev = this.backwardLayer.call(inputs, kwargs);\n      } else {\n        const forwardState = initialState.slice(0, initialState.length / 2);\n        const backwardState = initialState.slice(initialState.length / 2);\n        y = this.forwardLayer.call(\n            inputs, Object.assign(kwargs, {initialState: forwardState}));\n        yRev = this.backwardLayer.call(\n            inputs, Object.assign(kwargs, {initialState: backwardState}));\n      }\n\n      let states: Tensor[];\n      if (this.returnState) {\n        if (Array.isArray(y)) {\n          states = y.slice(1).concat((yRev as Tensor[]).slice(1));\n        } else {\n        }\n        y = (y as Tensor[])[0];\n        yRev = (yRev as Tensor[])[0];\n      }\n\n      if (this.returnSequences) {\n        yRev = tfc.reverse(yRev as Tensor, 1);\n      }\n\n      let output: Tensor|Tensor[];\n      if (this.mergeMode === 'concat') {\n        output = K.concatenate([y as Tensor, yRev as Tensor]);\n      } else if (this.mergeMode === 'sum') {\n        output = tfc.add(y as Tensor, yRev as Tensor);\n      } else if (this.mergeMode === 'ave') {\n        output = tfc.mul(.5, tfc.add(y as Tensor, yRev as Tensor));\n      } else if (this.mergeMode === 'mul') {\n        output = tfc.mul(y as Tensor, yRev as Tensor);\n      } else if (this.mergeMode == null) {\n        output = [y as Tensor, yRev as Tensor];\n      }\n\n      // TODO(cais): Properly set learning phase.\n      if (this.returnState) {\n        if (this.mergeMode == null) {\n          return (output as Tensor[]).concat(states);\n        }\n        return [output as Tensor].concat(states);\n      }\n      return output;\n    });\n  }\n\n  override resetStates(states?: Tensor|Tensor[]): void {\n    this.forwardLayer.resetStates();\n    this.backwardLayer.resetStates();\n  }\n\n  override build(inputShape: Shape|Shape[]): void {\n    nameScope(this.forwardLayer.name, () => {\n      this.forwardLayer.build(inputShape);\n    });\n    nameScope(this.backwardLayer.name, () => {\n      this.backwardLayer.build(inputShape);\n    });\n    this.built = true;\n  }\n\n  override computeMask(inputs: Tensor|Tensor[], mask?: Tensor|Tensor[]): Tensor\n      |Tensor[] {\n    if (Array.isArray(mask)) {\n      mask = mask[0];\n    }\n    let outputMask: Tensor|Tensor[];\n    if (this.returnSequences) {\n      if (this.mergeMode == null) {\n        outputMask = [mask, mask];\n      } else {\n        outputMask = mask;\n      }\n    } else {\n      if (this.mergeMode == null) {\n        outputMask = [null, null];\n      } else {\n        outputMask = null;\n      }\n    }\n    if (this.returnState) {\n      const states = this.forwardLayer.states;\n      const stateMask: Tensor[] = states.map(state => null);\n      if (Array.isArray(outputMask)) {\n        return outputMask.concat(stateMask).concat(stateMask);\n      } else {\n        return [outputMask].concat(stateMask).concat(stateMask);\n      }\n    } else {\n      return outputMask;\n    }\n  }\n\n  override get trainableWeights(): LayerVariable[] {\n    return this.forwardLayer.trainableWeights.concat(\n        this.backwardLayer.trainableWeights);\n  }\n\n  override get nonTrainableWeights(): LayerVariable[] {\n    return this.forwardLayer.nonTrainableWeights.concat(\n        this.backwardLayer.nonTrainableWeights);\n  }\n\n  // TODO(cais): Implement constraints().\n\n  override setFastWeightInitDuringBuild(value: boolean) {\n    super.setFastWeightInitDuringBuild(value);\n    if (this.forwardLayer != null) {\n      this.forwardLayer.setFastWeightInitDuringBuild(value);\n    }\n    if (this.backwardLayer != null) {\n      this.backwardLayer.setFastWeightInitDuringBuild(value);\n    }\n  }\n\n  override getConfig(): serialization.ConfigDict {\n    const config: serialization.ConfigDict = {\n      'mergeMode': this.mergeMode,\n    };\n    // TODO(cais): Add logic for `numConstants` once the property is added.\n    const baseConfig = super.getConfig();\n    Object.assign(config, baseConfig);\n    return config;\n  }\n\n  /** @nocollapse */\n  static override fromConfig<T extends serialization.Serializable>(\n      cls: serialization.SerializableConstructor<T>,\n      config: serialization.ConfigDict): T {\n    const rnnLayer =\n        deserialize(config['layer'] as serialization.ConfigDict) as RNN;\n    delete config['layer'];\n    // TODO(cais): Add logic for `numConstants` once the property is added.\n    if (config['numConstants'] != null) {\n      throw new NotImplementedError(\n          `Deserialization of a Bidirectional layer with numConstants ` +\n          `present is not supported yet.`);\n    }\n    // tslint:disable-next-line:no-any\n    const newConfig: {[key: string]: any} = config;\n    newConfig['layer'] = rnnLayer;\n    return new cls(newConfig);\n  }\n}\nserialization.registerClass(Bidirectional);\n"], "mappings": "AAAA;;;;;;;;;AAUA;;;AAIA,OAAO,KAAKA,GAAG,MAAM,uBAAuB;AAC5C,SAAQC,aAAa,EAAUC,IAAI,QAAO,uBAAuB;AACjE,OAAO,KAAKC,CAAC,MAAM,yBAAyB;AAC5C,SAAQC,SAAS,QAAO,WAAW;AACnC,SAAQC,SAAS,EAAEC,KAAK,EAAaC,cAAc,QAAO,oBAAoB;AAC9E,SAAQC,mBAAmB,EAAEC,UAAU,QAAO,WAAW;AACzD,SAAuCC,+BAA+B,QAAO,wBAAwB;AAGrG,OAAO,KAAKC,aAAa,MAAM,wBAAwB;AACvD,SAAQC,kBAAkB,EAAEC,mBAAmB,QAAO,sBAAsB;AAG5E,SAAQC,GAAG,EAAOC,eAAe,QAAO,aAAa;AACrD,SAAQC,WAAW,QAAO,iBAAiB;AAS3C;;;;;;;AAOA,OAAM,MAAgBC,OAAQ,SAAQX,KAAK;EAGzCY,YAAYC,IAAsB;IAChC;IACA;IACA;IACA;IACA;IACA;IACA;IACA,KAAK,CAACA,IAAI,CAAC;IACX,IAAI,CAACC,KAAK,GAAGD,IAAI,CAACC,KAAK;EACzB;EAESC,KAAKA,CAACC,UAAyB;IACtC,IAAI,CAACC,KAAK,GAAG,IAAI;EACnB;EAEA;EAEA,IAAaC,SAASA,CAAA;IACpB;IACA;IACA;IACA,IAAI,IAAI,CAACJ,KAAK,IAAI,IAAI,EAAE;MACtB,OAAO,IAAI,CAACA,KAAK,CAACI,SAAS;KAC5B,MAAM;MACL,OAAO,KAAK;;EAEhB;EAEA,IAAaA,SAASA,CAACC,KAAc;IACnC;IACA;IACA;IACA,IAAI,IAAI,CAACL,KAAK,IAAI,IAAI,EAAE;MACtB,IAAI,CAACA,KAAK,CAACI,SAAS,GAAGC,KAAK;;EAEhC;EAEA,IAAaC,gBAAgBA,CAAA;IAC3B,OAAO,IAAI,CAACN,KAAK,CAACM,gBAAgB;EACpC;EACA;EAEA,IAAaC,mBAAmBA,CAAA;IAC9B,OAAO,IAAI,CAACP,KAAK,CAACO,mBAAmB;EACvC;EACA;EAEA,IAAaC,OAAOA,CAAA;IAClB;IACA,OAAQ,IAAI,CAACR,KAAa,CAACS,QAAQ;EACrC;EAEA;EAEA,IAAaC,MAAMA,CAAA;IACjB,OAAO,IAAI,CAACV,KAAK,CAACU,MAAM;EAC1B;EAEA;EAESC,UAAUA,CAAA;IACjB,OAAO,IAAI,CAACX,KAAK,CAACW,UAAU,EAAE;EAChC;EAESC,UAAUA,CAACC,OAAiB;IACnC,IAAI,CAACb,KAAK,CAACY,UAAU,CAACC,OAAO,CAAC;EAChC;EAESC,SAASA,CAAA;IAChB,MAAMC,MAAM,GAA6B;MACvC,OAAO,EAAE;QACP,WAAW,EAAE,IAAI,CAACf,KAAK,CAACgB,YAAY,EAAE;QACtC,QAAQ,EAAE,IAAI,CAAChB,KAAK,CAACc,SAAS;;KAEjC;IACD,MAAMG,UAAU,GAAG,KAAK,CAACH,SAAS,EAAE;IACpCI,MAAM,CAACC,MAAM,CAACJ,MAAM,EAAEE,UAAU,CAAC;IACjC,OAAOF,MAAM;EACf;EAESK,4BAA4BA,CAACf,KAAc;IAClD,KAAK,CAACe,4BAA4B,CAACf,KAAK,CAAC;IACzC,IAAI,IAAI,CAACL,KAAK,IAAI,IAAI,EAAE;MACtB,IAAI,CAACA,KAAK,CAACoB,4BAA4B,CAACf,KAAK,CAAC;;EAElD;EAEA;EACA,OAAgBgB,UAAUA,CACtBC,GAA6C,EAC7CP,MAAgC,EACc;IAAA,IAA9CQ,aAAA,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAgB,EAA8B;IAChD,MAAMG,WAAW,GAAGZ,MAAM,CAAC,OAAO,CAA6B;IAC/D,MAAMf,KAAK,GAAGJ,WAAW,CAAC+B,WAAW,EAAEJ,aAAa,CAAU;IAC9D,OAAOR,MAAM,CAAC,OAAO,CAAC;IACtB,MAAMa,SAAS,GAAG;MAAC5B;IAAK,CAAC;IACzBkB,MAAM,CAACC,MAAM,CAACS,SAAS,EAAEb,MAAM,CAAC;IAChC,OAAO,IAAIO,GAAG,CAACM,SAAS,CAAC;EAC3B;;AAGF,MAAaC,eAAgB,SAAQhC,OAAO;EAG1CC,YAAYC,IAAsB;IAChC,KAAK,CAACA,IAAI,CAAC;IACX,IAAI,CAAC+B,eAAe,GAAG,IAAI;EAC7B;EAES7B,KAAKA,CAACC,UAAyB;IACtCA,UAAU,GAAGV,kBAAkB,CAACU,UAAU,CAAC;IAC3C,IAAIA,UAAU,CAACuB,MAAM,GAAG,CAAC,EAAE;MACzB,MAAM,IAAIpC,UAAU,CAChB,qFAAA0C,MAAA,CACeC,IAAI,CAACC,SAAS,CAAC/B,UAAU,CAAC,CAAE,CAAC;;IAElD,IAAI,CAACgC,SAAS,GAAG,CAAC;MAACC,KAAK,EAAEjC;IAAU,CAAC,CAAC;IACtC,MAAMkC,eAAe,GAAG,CAAClC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC6B,MAAM,CAAC7B,UAAU,CAACmC,KAAK,CAAC,CAAC,CAAC,CAAC;IACnE,IAAI,CAAC,IAAI,CAACrC,KAAK,CAACG,KAAK,EAAE;MACrB,IAAI,CAACH,KAAK,CAACC,KAAK,CAACmC,eAAe,CAAC;MACjC,IAAI,CAACpC,KAAK,CAACG,KAAK,GAAG,IAAI;;IAEzB,KAAK,CAACF,KAAK,CAACC,UAAU,CAAC;EACzB;EAESoC,kBAAkBA,CAACpC,UAAyB;IACnDA,UAAU,GAAGV,kBAAkB,CAACU,UAAU,CAAC;IAC3C,MAAMkC,eAAe,GAAG,CAAClC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC6B,MAAM,CAAC7B,UAAU,CAACmC,KAAK,CAAC,CAAC,CAAC,CAAC;IACnE,MAAME,gBAAgB,GAClB,IAAI,CAACvC,KAAK,CAACsC,kBAAkB,CAACF,eAAe,CAAU;IAC3D,MAAMI,SAAS,GAAGtC,UAAU,CAAC,CAAC,CAAC;IAC/B,OAAO,CAACqC,gBAAgB,CAAC,CAAC,CAAC,EAAEC,SAAS,CAAC,CAACT,MAAM,CAACQ,gBAAgB,CAACF,KAAK,CAAC,CAAC,CAAC,CAAC;EAC3E;EAESI,IAAIA,CAACC,MAAuB,EAAEC,MAAc;IACnD,OAAO7D,IAAI,CAAC,MAAK;MACf;MACA4D,MAAM,GAAGjD,mBAAmB,CAACiD,MAAM,CAAC;MACpC;MACA;MACA;MACA,MAAME,IAAI,GAAoBA,CAACF,MAAc,EAAEG,MAAgB,KAAI;QACjE;QACA;QACA;QACA;QACA,MAAMC,MAAM,GAAGrD,mBAAmB,CAAC,IAAI,CAACO,KAAK,CAACyC,IAAI,CAACC,MAAM,EAAEC,MAAM,CAAC,CAAC;QACnE,OAAO,CAACG,MAAM,EAAE,EAAE,CAAC;MACrB,CAAC;MACD,MAAMC,UAAU,GACZrD,GAAG,CAACkD,IAAI,EAAEF,MAAM,EAAE,EAAE,EAAE,KAAK,CAAC,mBAAmB,IAAI,CAAC,YAChD,IAAI,CAAC,iBAAiB,KAAK,CAAC,cAC5B,IAAI,CAAC,wBAAwB,CAAC;MACtC,MAAMM,CAAC,GAAGD,UAAU,CAAC,CAAC,CAAC;MACvB;MACA;MACA,OAAOC,CAAC;IACV,CAAC,CAAC;EACJ;;AAxDA;AACOnB,eAAA,CAAAoB,SAAS,GAAG,iBAAiB;SAFzBpB,eAAe;AA6D5BhD,aAAa,CAACqE,aAAa,CAACrB,eAAe,CAAC;AAE5C,OAAM,SAAUsB,2BAA2BA,CAAC9C,KAAc;EACxDd,aAAa,CAAC6D,yBAAyB,CACnC9D,+BAA+B,EAAE,wBAAwB,EAAEe,KAAK,CAAC;AACvE;AAkBA,MAAMgD,gCAAgC,GAA2B,QAAQ;AAEzE,MAAaC,aAAc,SAAQzD,OAAO;EAWxCC,YAAYC,IAA4B;IACtC,KAAK,CAACA,IAAI,CAAC;IAEX;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,MAAM4B,WAAW,GAAG5B,IAAI,CAACC,KAAK,CAACc,SAAS,EAAE;IAC1C,MAAMyC,QAAQ,GAA6B,EAAE;IAC7CA,QAAQ,CAAC,WAAW,CAAC,GAAGxD,IAAI,CAACC,KAAK,CAACgB,YAAY,EAAE;IACjDuC,QAAQ,CAAC,QAAQ,CAAC,GAAG5B,WAAW;IAChC,IAAI,CAAC6B,YAAY,GAAG5D,WAAW,CAAC2D,QAAQ,CAAQ;IAChD5B,WAAW,CAAC,aAAa,CAAC,GACtBA,WAAW,CAAC,aAAa,CAAC,KAAK,IAAI,GAAG,KAAK,GAAG,IAAI;IACtD,MAAM8B,QAAQ,GAA6B,EAAE;IAC7CA,QAAQ,CAAC,WAAW,CAAC,GAAG1D,IAAI,CAACC,KAAK,CAACgB,YAAY,EAAE;IACjDyC,QAAQ,CAAC,QAAQ,CAAC,GAAG9B,WAAW;IAChC,IAAI,CAAC+B,aAAa,GAAG9D,WAAW,CAAC6D,QAAQ,CAAQ;IACjD,IAAI,CAACD,YAAY,CAACG,IAAI,GAAG,UAAU,GAAG,IAAI,CAACH,YAAY,CAACG,IAAI;IAC5D,IAAI,CAACD,aAAa,CAACC,IAAI,GAAG,WAAW,GAAG,IAAI,CAACD,aAAa,CAACC,IAAI;IAE/D,IAAI,CAACC,SAAS,GAAG7D,IAAI,CAAC6D,SAAS,KAAKlC,SAAS,GACzC2B,gCAAgC,GAChCtD,IAAI,CAAC6D,SAAS;IAClBT,2BAA2B,CAAC,IAAI,CAACS,SAAS,CAAC;IAC3C,IAAI7D,IAAI,CAACc,OAAO,EAAE;MAChB,MAAM,IAAIzB,mBAAmB,CACzB,iEAAiE,CAAC;;IAExE,IAAI,CAACyE,SAAS,GAAG9D,IAAI,CAACC,KAAK,CAAC8D,QAAQ;IACpC,IAAI,CAACC,eAAe,GAAGhE,IAAI,CAACC,KAAK,CAAC+D,eAAe;IACjD,IAAI,CAACC,WAAW,GAAGjE,IAAI,CAACC,KAAK,CAACgE,WAAW;IACzC,IAAI,CAAClC,eAAe,GAAG,IAAI;IAC3B,IAAI,CAACmC,UAAU,GAAG,IAAI;IACtB,IAAI,CAAC/B,SAAS,GAAGnC,IAAI,CAACC,KAAK,CAACkC,SAAS;IACrC,IAAI,CAACgC,YAAY,GAAG,IAAI;EAC1B;EAEA,IAAa9D,SAASA,CAAA;IACpB,OAAO,IAAI,CAAC6D,UAAU;EACxB;EAEA,IAAa7D,SAASA,CAACC,KAAc;IACnC;IACA;IACA;IACA,IAAI,CAAC4D,UAAU,GAAG5D,KAAK;IACvB,IAAI,IAAI,CAACmD,YAAY,IAAI,IAAI,EAAE;MAC7B,IAAI,CAACA,YAAY,CAACpD,SAAS,GAAGC,KAAK;;IAErC,IAAI,IAAI,CAACqD,aAAa,IAAI,IAAI,EAAE;MAC9B,IAAI,CAACA,aAAa,CAACtD,SAAS,GAAGC,KAAK;;EAExC;EAESM,UAAUA,CAAA;IACjB,OAAO,IAAI,CAAC6C,YAAY,CAAC7C,UAAU,EAAE,CAACoB,MAAM,CACxC,IAAI,CAAC2B,aAAa,CAAC/C,UAAU,EAAE,CAAC;EACtC;EAESC,UAAUA,CAACC,OAAiB;IACnC,MAAMsD,UAAU,GAAGtD,OAAO,CAACY,MAAM;IACjC,MAAM2C,cAAc,GAAGC,IAAI,CAACC,KAAK,CAACH,UAAU,GAAG,CAAC,CAAC;IACjD,IAAI,CAACX,YAAY,CAAC5C,UAAU,CAACC,OAAO,CAACwB,KAAK,CAAC,CAAC,EAAE+B,cAAc,CAAC,CAAC;IAC9D,IAAI,CAACV,aAAa,CAAC9C,UAAU,CAACC,OAAO,CAACwB,KAAK,CAAC+B,cAAc,CAAC,CAAC;EAC9D;EAES9B,kBAAkBA,CAACpC,UAAyB;IACnD,IAAIqE,WAAW,GACX,IAAI,CAACf,YAAY,CAAClB,kBAAkB,CAACpC,UAAU,CAAC;IACpD,IAAI,EAAEsE,KAAK,CAACC,OAAO,CAACF,WAAW,CAAC,IAAIC,KAAK,CAACC,OAAO,CAACF,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MAClEA,WAAW,GAAG,CAACA,WAAoB,CAAC;;IAEtCA,WAAW,GAAGA,WAAsB;IAEpC,IAAIG,WAAkB;IACtB,IAAIC,YAAqB;IACzB,IAAIC,UAAmB;IACvB,IAAI,IAAI,CAACZ,WAAW,EAAE;MACpBY,UAAU,GAAGL,WAAW,CAAClC,KAAK,CAAC,CAAC,CAAC;MACjCqC,WAAW,GAAGH,WAAW,CAAC,CAAC,CAAC;KAC7B,MAAM;MACLG,WAAW,GAAGH,WAAW,CAAC,CAAC,CAAC;;IAE9BG,WAAW,GAAGA,WAAW;IACzB,IAAI,IAAI,CAACd,SAAS,KAAK,QAAQ,EAAE;MAC/Bc,WAAW,CAACA,WAAW,CAACjD,MAAM,GAAG,CAAC,CAAC,IAAI,CAAC;MACxCkD,YAAY,GAAG,CAACD,WAAW,CAAC;KAC7B,MAAM,IAAI,IAAI,CAACd,SAAS,IAAI,IAAI,EAAE;MACjCe,YAAY,GAAG,CAACD,WAAW,EAAEA,WAAW,CAACrC,KAAK,EAAE,CAAC;KAClD,MAAM;MACLsC,YAAY,GAAG,CAACD,WAAW,CAAC;;IAG9B,IAAI,IAAI,CAACV,WAAW,EAAE;MACpB,IAAI,IAAI,CAACJ,SAAS,IAAI,IAAI,EAAE;QAC1B,OAAOe,YAAY,CAAC5C,MAAM,CAAC6C,UAAU,CAAC,CAAC7C,MAAM,CAAC6C,UAAU,CAACvC,KAAK,EAAE,CAAC;;MAEnE,OAAO,CAACqC,WAAW,CAAC,CAAC3C,MAAM,CAAC6C,UAAU,CAAC,CAAC7C,MAAM,CAAC6C,UAAU,CAACvC,KAAK,EAAE,CAAC;;IAEpE,OAAO9C,aAAa,CAACsF,gBAAgB,CAACF,YAAY,CAAC;EACrD;EAESG,KAAKA,CACVpC,MAAuD,EACvDC,MAAe;IACjB,IAAIoC,YAAY,GACZpC,MAAM,IAAI,IAAI,GAAG,IAAI,GAAGA,MAAM,CAAC,cAAc,CAAC;IAClD,IAAIqC,SAAS,GACTrC,MAAM,IAAI,IAAI,GAAG,IAAI,GAAGA,MAAM,CAAC,WAAW,CAAC;IAC/C,IAAIA,MAAM,IAAI,IAAI,EAAE;MAClBA,MAAM,GAAG,EAAE;;IAEb,MAAMsC,YAAY,GACdtF,eAAe,CAAC+C,MAAM,EAAEqC,YAAY,EAAEC,SAAS,EAAE,IAAI,CAACd,YAAY,CAAC;IACvExB,MAAM,GAAGuC,YAAY,CAACvC,MAAM;IAC5BqC,YAAY,GAAGE,YAAY,CAACF,YAAY;IACxCC,SAAS,GAAGC,YAAY,CAACD,SAAS;IAElC,IAAIR,KAAK,CAACC,OAAO,CAAC/B,MAAM,CAAC,EAAE;MACzBqC,YAAY,GAAIrC,MAAsC,CAACL,KAAK,CAAC,CAAC,CAAC;MAC/DK,MAAM,GAAIA,MAAsC,CAAC,CAAC,CAAC;;IAGrD,IAAI,CAACqC,YAAY,IAAI,IAAI,IAAIA,YAAY,CAACtD,MAAM,KAAK,CAAC,KAClDuD,SAAS,IAAI,IAAI,EAAE;MACrB,OAAO,KAAK,CAACF,KAAK,CAACpC,MAAM,EAAEC,MAAM,CAAC;;IAEpC,MAAMuC,gBAAgB,GAAiC,EAAE;IACzD,MAAMC,eAAe,GAAgB,EAAE;IACvC,IAAIJ,YAAY,IAAI,IAAI,EAAE;MACxB,MAAMK,SAAS,GAAGL,YAAY,CAACtD,MAAM;MACrC,IAAI2D,SAAS,GAAG,CAAC,GAAG,CAAC,EAAE;QACrB,MAAM,IAAI/F,UAAU,CAChB,qDAAqD,GACrD,wDAAwD,GACxD,sBAAsB,CAAC;;MAE7BsD,MAAM,CAAC,cAAc,CAAC,GAAGoC,YAAY;MACrCG,gBAAgB,CAACG,IAAI,CAAC,GAAGN,YAAY,CAAC;MACtC,MAAMO,UAAU,GAAIP,YAA6C,CACzCQ,GAAG,CAACC,KAAK,IAAI,IAAIvG,SAAS,CAAC;QAACkD,KAAK,EAAEqD,KAAK,CAACrD;MAAK,CAAC,CAAC,CAAC;MACzE,IAAI,CAACqB,YAAY,CAACiC,SAAS,GAAGH,UAAU,CAACjD,KAAK,CAAC,CAAC,EAAE+C,SAAS,GAAG,CAAC,CAAC;MAChE,IAAI,CAAC1B,aAAa,CAAC+B,SAAS,GAAGH,UAAU,CAACjD,KAAK,CAAC+C,SAAS,GAAG,CAAC,CAAC;MAC9DD,eAAe,CAACE,IAAI,CAAC,GAAGC,UAAU,CAAC;;IAErC,IAAIN,SAAS,IAAI,IAAI,EAAE;MACrB,MAAM,IAAI5F,mBAAmB,CACzB,uDAAuD,GACvD,kBAAkB,CAAC;;IAGzB,MAAMsG,gBAAgB,GAAGR,gBAAgB,CAAC,CAAC,CAAC,YAAY/F,cAAc;IACtE,KAAK,MAAMwG,MAAM,IAAIT,gBAAgB,EAAE;MACrC,IAAIS,MAAM,YAAYxG,cAAc,KAAKuG,gBAAgB,EAAE;QACzD,MAAM,IAAIrG,UAAU,CAChB,uDAAuD,GACvD,yDAAyD,CAAC;;;IAIlE,IAAIqG,gBAAgB,EAAE;MACpB;MACA,MAAME,SAAS,GAAG,CAAClD,MAAM,CAAC,CAACX,MAAM,CAACmD,gBAAgB,CAAC;MACnD,MAAMW,aAAa,GAAG,IAAI,CAAC3D,SAAS,CAACH,MAAM,CAACoD,eAAe,CAAC;MAC5D;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA,MAAMW,iBAAiB,GAAG,IAAI,CAAC5D,SAAS;MACxC,IAAI,CAACA,SAAS,GAAG2D,aAAa;MAC9B,MAAM/C,MAAM,GACR,KAAK,CAACgC,KAAK,CAACc,SAAwC,EAAEjD,MAAM,CAAC;MACjE,IAAI,CAACT,SAAS,GAAG4D,iBAAiB;MAClC,OAAOhD,MAAM;KACd,MAAM;MACL,OAAO,KAAK,CAACgC,KAAK,CAACpC,MAAM,EAAEC,MAAM,CAAC;;EAEtC;EAESF,IAAIA,CAACC,MAAuB,EAAEC,MAAc;IACnD,OAAO7D,IAAI,CAAC,MAAK;MACf,MAAMiG,YAAY,GAAGpC,MAAM,CAAC,cAAc,CAAC;MAE3C,IAAIK,CAAkB;MACtB,IAAI+C,IAAqB;MACzB,IAAIhB,YAAY,IAAI,IAAI,EAAE;QACxB/B,CAAC,GAAG,IAAI,CAACQ,YAAY,CAACf,IAAI,CAACC,MAAM,EAAEC,MAAM,CAAC;QAC1CoD,IAAI,GAAG,IAAI,CAACrC,aAAa,CAACjB,IAAI,CAACC,MAAM,EAAEC,MAAM,CAAC;OAC/C,MAAM;QACL,MAAMqD,YAAY,GAAGjB,YAAY,CAAC1C,KAAK,CAAC,CAAC,EAAE0C,YAAY,CAACtD,MAAM,GAAG,CAAC,CAAC;QACnE,MAAMwE,aAAa,GAAGlB,YAAY,CAAC1C,KAAK,CAAC0C,YAAY,CAACtD,MAAM,GAAG,CAAC,CAAC;QACjEuB,CAAC,GAAG,IAAI,CAACQ,YAAY,CAACf,IAAI,CACtBC,MAAM,EAAExB,MAAM,CAACC,MAAM,CAACwB,MAAM,EAAE;UAACoC,YAAY,EAAEiB;QAAY,CAAC,CAAC,CAAC;QAChED,IAAI,GAAG,IAAI,CAACrC,aAAa,CAACjB,IAAI,CAC1BC,MAAM,EAAExB,MAAM,CAACC,MAAM,CAACwB,MAAM,EAAE;UAACoC,YAAY,EAAEkB;QAAa,CAAC,CAAC,CAAC;;MAGnE,IAAIpD,MAAgB;MACpB,IAAI,IAAI,CAACmB,WAAW,EAAE;QACpB,IAAIQ,KAAK,CAACC,OAAO,CAACzB,CAAC,CAAC,EAAE;UACpBH,MAAM,GAAGG,CAAC,CAACX,KAAK,CAAC,CAAC,CAAC,CAACN,MAAM,CAAEgE,IAAiB,CAAC1D,KAAK,CAAC,CAAC,CAAC,CAAC;SACxD,MAAM,C;QAEPW,CAAC,GAAIA,CAAc,CAAC,CAAC,CAAC;QACtB+C,IAAI,GAAIA,IAAiB,CAAC,CAAC,CAAC;;MAG9B,IAAI,IAAI,CAAChC,eAAe,EAAE;QACxBgC,IAAI,GAAGnH,GAAG,CAACsH,OAAO,CAACH,IAAc,EAAE,CAAC,CAAC;;MAGvC,IAAIjD,MAAuB;MAC3B,IAAI,IAAI,CAACc,SAAS,KAAK,QAAQ,EAAE;QAC/Bd,MAAM,GAAG/D,CAAC,CAACoH,WAAW,CAAC,CAACnD,CAAW,EAAE+C,IAAc,CAAC,CAAC;OACtD,MAAM,IAAI,IAAI,CAACnC,SAAS,KAAK,KAAK,EAAE;QACnCd,MAAM,GAAGlE,GAAG,CAACwH,GAAG,CAACpD,CAAW,EAAE+C,IAAc,CAAC;OAC9C,MAAM,IAAI,IAAI,CAACnC,SAAS,KAAK,KAAK,EAAE;QACnCd,MAAM,GAAGlE,GAAG,CAACyH,GAAG,CAAC,EAAE,EAAEzH,GAAG,CAACwH,GAAG,CAACpD,CAAW,EAAE+C,IAAc,CAAC,CAAC;OAC3D,MAAM,IAAI,IAAI,CAACnC,SAAS,KAAK,KAAK,EAAE;QACnCd,MAAM,GAAGlE,GAAG,CAACyH,GAAG,CAACrD,CAAW,EAAE+C,IAAc,CAAC;OAC9C,MAAM,IAAI,IAAI,CAACnC,SAAS,IAAI,IAAI,EAAE;QACjCd,MAAM,GAAG,CAACE,CAAW,EAAE+C,IAAc,CAAC;;MAGxC;MACA,IAAI,IAAI,CAAC/B,WAAW,EAAE;QACpB,IAAI,IAAI,CAACJ,SAAS,IAAI,IAAI,EAAE;UAC1B,OAAQd,MAAmB,CAACf,MAAM,CAACc,MAAM,CAAC;;QAE5C,OAAO,CAACC,MAAgB,CAAC,CAACf,MAAM,CAACc,MAAM,CAAC;;MAE1C,OAAOC,MAAM;IACf,CAAC,CAAC;EACJ;EAESwD,WAAWA,CAACzD,MAAwB;IAC3C,IAAI,CAACW,YAAY,CAAC8C,WAAW,EAAE;IAC/B,IAAI,CAAC5C,aAAa,CAAC4C,WAAW,EAAE;EAClC;EAESrG,KAAKA,CAACC,UAAyB;IACtClB,SAAS,CAAC,IAAI,CAACwE,YAAY,CAACG,IAAI,EAAE,MAAK;MACrC,IAAI,CAACH,YAAY,CAACvD,KAAK,CAACC,UAAU,CAAC;IACrC,CAAC,CAAC;IACFlB,SAAS,CAAC,IAAI,CAAC0E,aAAa,CAACC,IAAI,EAAE,MAAK;MACtC,IAAI,CAACD,aAAa,CAACzD,KAAK,CAACC,UAAU,CAAC;IACtC,CAAC,CAAC;IACF,IAAI,CAACC,KAAK,GAAG,IAAI;EACnB;EAESoG,WAAWA,CAAC7D,MAAuB,EAAE8D,IAAsB;IAElE,IAAIhC,KAAK,CAACC,OAAO,CAAC+B,IAAI,CAAC,EAAE;MACvBA,IAAI,GAAGA,IAAI,CAAC,CAAC,CAAC;;IAEhB,IAAIC,UAA2B;IAC/B,IAAI,IAAI,CAAC1C,eAAe,EAAE;MACxB,IAAI,IAAI,CAACH,SAAS,IAAI,IAAI,EAAE;QAC1B6C,UAAU,GAAG,CAACD,IAAI,EAAEA,IAAI,CAAC;OAC1B,MAAM;QACLC,UAAU,GAAGD,IAAI;;KAEpB,MAAM;MACL,IAAI,IAAI,CAAC5C,SAAS,IAAI,IAAI,EAAE;QAC1B6C,UAAU,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC;OAC1B,MAAM;QACLA,UAAU,GAAG,IAAI;;;IAGrB,IAAI,IAAI,CAACzC,WAAW,EAAE;MACpB,MAAMnB,MAAM,GAAG,IAAI,CAACW,YAAY,CAACX,MAAM;MACvC,MAAM6D,SAAS,GAAa7D,MAAM,CAAC0C,GAAG,CAACC,KAAK,IAAI,IAAI,CAAC;MACrD,IAAIhB,KAAK,CAACC,OAAO,CAACgC,UAAU,CAAC,EAAE;QAC7B,OAAOA,UAAU,CAAC1E,MAAM,CAAC2E,SAAS,CAAC,CAAC3E,MAAM,CAAC2E,SAAS,CAAC;OACtD,MAAM;QACL,OAAO,CAACD,UAAU,CAAC,CAAC1E,MAAM,CAAC2E,SAAS,CAAC,CAAC3E,MAAM,CAAC2E,SAAS,CAAC;;KAE1D,MAAM;MACL,OAAOD,UAAU;;EAErB;EAEA,IAAanG,gBAAgBA,CAAA;IAC3B,OAAO,IAAI,CAACkD,YAAY,CAAClD,gBAAgB,CAACyB,MAAM,CAC5C,IAAI,CAAC2B,aAAa,CAACpD,gBAAgB,CAAC;EAC1C;EAEA,IAAaC,mBAAmBA,CAAA;IAC9B,OAAO,IAAI,CAACiD,YAAY,CAACjD,mBAAmB,CAACwB,MAAM,CAC/C,IAAI,CAAC2B,aAAa,CAACnD,mBAAmB,CAAC;EAC7C;EAEA;EAESa,4BAA4BA,CAACf,KAAc;IAClD,KAAK,CAACe,4BAA4B,CAACf,KAAK,CAAC;IACzC,IAAI,IAAI,CAACmD,YAAY,IAAI,IAAI,EAAE;MAC7B,IAAI,CAACA,YAAY,CAACpC,4BAA4B,CAACf,KAAK,CAAC;;IAEvD,IAAI,IAAI,CAACqD,aAAa,IAAI,IAAI,EAAE;MAC9B,IAAI,CAACA,aAAa,CAACtC,4BAA4B,CAACf,KAAK,CAAC;;EAE1D;EAESS,SAASA,CAAA;IAChB,MAAMC,MAAM,GAA6B;MACvC,WAAW,EAAE,IAAI,CAAC6C;KACnB;IACD;IACA,MAAM3C,UAAU,GAAG,KAAK,CAACH,SAAS,EAAE;IACpCI,MAAM,CAACC,MAAM,CAACJ,MAAM,EAAEE,UAAU,CAAC;IACjC,OAAOF,MAAM;EACf;EAEA;EACA,OAAgBM,UAAUA,CACtBC,GAA6C,EAC7CP,MAAgC;IAClC,MAAM4F,QAAQ,GACV/G,WAAW,CAACmB,MAAM,CAAC,OAAO,CAA6B,CAAQ;IACnE,OAAOA,MAAM,CAAC,OAAO,CAAC;IACtB;IACA,IAAIA,MAAM,CAAC,cAAc,CAAC,IAAI,IAAI,EAAE;MAClC,MAAM,IAAI3B,mBAAmB,CACzB,+FAC+B,CAAC;;IAEtC;IACA,MAAMwC,SAAS,GAAyBb,MAAM;IAC9Ca,SAAS,CAAC,OAAO,CAAC,GAAG+E,QAAQ;IAC7B,OAAO,IAAIrF,GAAG,CAACM,SAAS,CAAC;EAC3B;;AA/VA;AACO0B,aAAA,CAAAL,SAAS,GAAG,eAAe;SAFvBK,aAAa;AAkW1BzE,aAAa,CAACqE,aAAa,CAACI,aAAa,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}