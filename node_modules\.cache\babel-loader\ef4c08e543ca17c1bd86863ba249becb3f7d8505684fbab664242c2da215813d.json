{"ast": null, "code": "\"use strict\";\n\nvar __classPrivateFieldGet = this && this.__classPrivateFieldGet || function (receiver, state, kind, f) {\n  if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a getter\");\n  if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot read private member from an object whose class did not declare it\");\n  return kind === \"m\" ? f : kind === \"a\" ? f.call(receiver) : f ? f.value : state.get(receiver);\n};\nvar __classPrivateFieldSet = this && this.__classPrivateFieldSet || function (receiver, state, value, kind, f) {\n  if (kind === \"m\") throw new TypeError(\"Private method is not writable\");\n  if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a setter\");\n  if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot write private member to an object whose class did not declare it\");\n  return kind === \"a\" ? f.call(receiver, value) : f ? f.value = value : state.set(receiver, value), value;\n};\nvar _RedisCluster_instances, _RedisCluster_options, _RedisCluster_slots, _RedisCluster_Multi, _RedisCluster_execute;\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nconst commands_1 = require(\"./commands\");\nconst cluster_slots_1 = require(\"./cluster-slots\");\nconst commander_1 = require(\"../commander\");\nconst events_1 = require(\"events\");\nconst multi_command_1 = require(\"./multi-command\");\nconst errors_1 = require(\"../errors\");\nclass RedisCluster extends events_1.EventEmitter {\n  static extractFirstKey(command, originalArgs, redisArgs) {\n    if (command.FIRST_KEY_INDEX === undefined) {\n      return undefined;\n    } else if (typeof command.FIRST_KEY_INDEX === 'number') {\n      return redisArgs[command.FIRST_KEY_INDEX];\n    }\n    return command.FIRST_KEY_INDEX(...originalArgs);\n  }\n  static create(options) {\n    return new ((0, commander_1.attachExtensions)({\n      BaseClass: RedisCluster,\n      modulesExecutor: RedisCluster.prototype.commandsExecutor,\n      modules: options?.modules,\n      functionsExecutor: RedisCluster.prototype.functionsExecutor,\n      functions: options?.functions,\n      scriptsExecutor: RedisCluster.prototype.scriptsExecutor,\n      scripts: options?.scripts\n    }))(options);\n  }\n  get slots() {\n    return __classPrivateFieldGet(this, _RedisCluster_slots, \"f\").slots;\n  }\n  get shards() {\n    return __classPrivateFieldGet(this, _RedisCluster_slots, \"f\").shards;\n  }\n  get masters() {\n    return __classPrivateFieldGet(this, _RedisCluster_slots, \"f\").masters;\n  }\n  get replicas() {\n    return __classPrivateFieldGet(this, _RedisCluster_slots, \"f\").replicas;\n  }\n  get nodeByAddress() {\n    return __classPrivateFieldGet(this, _RedisCluster_slots, \"f\").nodeByAddress;\n  }\n  get pubSubNode() {\n    return __classPrivateFieldGet(this, _RedisCluster_slots, \"f\").pubSubNode;\n  }\n  get isOpen() {\n    return __classPrivateFieldGet(this, _RedisCluster_slots, \"f\").isOpen;\n  }\n  constructor(options) {\n    super();\n    _RedisCluster_instances.add(this);\n    _RedisCluster_options.set(this, void 0);\n    _RedisCluster_slots.set(this, void 0);\n    _RedisCluster_Multi.set(this, void 0);\n    Object.defineProperty(this, \"multi\", {\n      enumerable: true,\n      configurable: true,\n      writable: true,\n      value: this.MULTI\n    });\n    Object.defineProperty(this, \"subscribe\", {\n      enumerable: true,\n      configurable: true,\n      writable: true,\n      value: this.SUBSCRIBE\n    });\n    Object.defineProperty(this, \"unsubscribe\", {\n      enumerable: true,\n      configurable: true,\n      writable: true,\n      value: this.UNSUBSCRIBE\n    });\n    Object.defineProperty(this, \"pSubscribe\", {\n      enumerable: true,\n      configurable: true,\n      writable: true,\n      value: this.PSUBSCRIBE\n    });\n    Object.defineProperty(this, \"pUnsubscribe\", {\n      enumerable: true,\n      configurable: true,\n      writable: true,\n      value: this.PUNSUBSCRIBE\n    });\n    Object.defineProperty(this, \"sSubscribe\", {\n      enumerable: true,\n      configurable: true,\n      writable: true,\n      value: this.SSUBSCRIBE\n    });\n    Object.defineProperty(this, \"sUnsubscribe\", {\n      enumerable: true,\n      configurable: true,\n      writable: true,\n      value: this.SUNSUBSCRIBE\n    });\n    __classPrivateFieldSet(this, _RedisCluster_options, options, \"f\");\n    __classPrivateFieldSet(this, _RedisCluster_slots, new cluster_slots_1.default(options, this.emit.bind(this)), \"f\");\n    __classPrivateFieldSet(this, _RedisCluster_Multi, multi_command_1.default.extend(options), \"f\");\n  }\n  duplicate(overrides) {\n    return new (Object.getPrototypeOf(this).constructor)({\n      ...__classPrivateFieldGet(this, _RedisCluster_options, \"f\"),\n      ...overrides\n    });\n  }\n  connect() {\n    return __classPrivateFieldGet(this, _RedisCluster_slots, \"f\").connect();\n  }\n  async commandsExecutor(command, args) {\n    const {\n      jsArgs,\n      args: redisArgs,\n      options\n    } = (0, commander_1.transformCommandArguments)(command, args);\n    return (0, commander_1.transformCommandReply)(command, await this.sendCommand(RedisCluster.extractFirstKey(command, jsArgs, redisArgs), command.IS_READ_ONLY, redisArgs, options), redisArgs.preserve);\n  }\n  async sendCommand(firstKey, isReadonly, args, options) {\n    return __classPrivateFieldGet(this, _RedisCluster_instances, \"m\", _RedisCluster_execute).call(this, firstKey, isReadonly, client => client.sendCommand(args, options));\n  }\n  async functionsExecutor(fn, args, name) {\n    const {\n      args: redisArgs,\n      options\n    } = (0, commander_1.transformCommandArguments)(fn, args);\n    return (0, commander_1.transformCommandReply)(fn, await this.executeFunction(name, fn, args, redisArgs, options), redisArgs.preserve);\n  }\n  async executeFunction(name, fn, originalArgs, redisArgs, options) {\n    return __classPrivateFieldGet(this, _RedisCluster_instances, \"m\", _RedisCluster_execute).call(this, RedisCluster.extractFirstKey(fn, originalArgs, redisArgs), fn.IS_READ_ONLY, client => client.executeFunction(name, fn, redisArgs, options));\n  }\n  async scriptsExecutor(script, args) {\n    const {\n      args: redisArgs,\n      options\n    } = (0, commander_1.transformCommandArguments)(script, args);\n    return (0, commander_1.transformCommandReply)(script, await this.executeScript(script, args, redisArgs, options), redisArgs.preserve);\n  }\n  async executeScript(script, originalArgs, redisArgs, options) {\n    return __classPrivateFieldGet(this, _RedisCluster_instances, \"m\", _RedisCluster_execute).call(this, RedisCluster.extractFirstKey(script, originalArgs, redisArgs), script.IS_READ_ONLY, client => client.executeScript(script, redisArgs, options));\n  }\n  MULTI(routing) {\n    return new (__classPrivateFieldGet(this, _RedisCluster_Multi, \"f\"))((commands, firstKey, chainId) => {\n      return __classPrivateFieldGet(this, _RedisCluster_instances, \"m\", _RedisCluster_execute).call(this, firstKey, false, client => client.multiExecutor(commands, undefined, chainId));\n    }, routing);\n  }\n  async SUBSCRIBE(channels, listener, bufferMode) {\n    return (await __classPrivateFieldGet(this, _RedisCluster_slots, \"f\").getPubSubClient()).SUBSCRIBE(channels, listener, bufferMode);\n  }\n  async UNSUBSCRIBE(channels, listener, bufferMode) {\n    return __classPrivateFieldGet(this, _RedisCluster_slots, \"f\").executeUnsubscribeCommand(client => client.UNSUBSCRIBE(channels, listener, bufferMode));\n  }\n  async PSUBSCRIBE(patterns, listener, bufferMode) {\n    return (await __classPrivateFieldGet(this, _RedisCluster_slots, \"f\").getPubSubClient()).PSUBSCRIBE(patterns, listener, bufferMode);\n  }\n  async PUNSUBSCRIBE(patterns, listener, bufferMode) {\n    return __classPrivateFieldGet(this, _RedisCluster_slots, \"f\").executeUnsubscribeCommand(client => client.PUNSUBSCRIBE(patterns, listener, bufferMode));\n  }\n  async SSUBSCRIBE(channels, listener, bufferMode) {\n    const maxCommandRedirections = __classPrivateFieldGet(this, _RedisCluster_options, \"f\").maxCommandRedirections ?? 16,\n      firstChannel = Array.isArray(channels) ? channels[0] : channels;\n    let client = await __classPrivateFieldGet(this, _RedisCluster_slots, \"f\").getShardedPubSubClient(firstChannel);\n    for (let i = 0;; i++) {\n      try {\n        return await client.SSUBSCRIBE(channels, listener, bufferMode);\n      } catch (err) {\n        if (++i > maxCommandRedirections || !(err instanceof errors_1.ErrorReply)) {\n          throw err;\n        }\n        if (err.message.startsWith('MOVED')) {\n          await __classPrivateFieldGet(this, _RedisCluster_slots, \"f\").rediscover(client);\n          client = await __classPrivateFieldGet(this, _RedisCluster_slots, \"f\").getShardedPubSubClient(firstChannel);\n          continue;\n        }\n        throw err;\n      }\n    }\n  }\n  SUNSUBSCRIBE(channels, listener, bufferMode) {\n    return __classPrivateFieldGet(this, _RedisCluster_slots, \"f\").executeShardedUnsubscribeCommand(Array.isArray(channels) ? channels[0] : channels, client => client.SUNSUBSCRIBE(channels, listener, bufferMode));\n  }\n  quit() {\n    return __classPrivateFieldGet(this, _RedisCluster_slots, \"f\").quit();\n  }\n  disconnect() {\n    return __classPrivateFieldGet(this, _RedisCluster_slots, \"f\").disconnect();\n  }\n  nodeClient(node) {\n    return __classPrivateFieldGet(this, _RedisCluster_slots, \"f\").nodeClient(node);\n  }\n  getRandomNode() {\n    return __classPrivateFieldGet(this, _RedisCluster_slots, \"f\").getRandomNode();\n  }\n  getSlotRandomNode(slot) {\n    return __classPrivateFieldGet(this, _RedisCluster_slots, \"f\").getSlotRandomNode(slot);\n  }\n  /**\n   * @deprecated use `.masters` instead\n   */\n  getMasters() {\n    return this.masters;\n  }\n  /**\n   * @deprecated use `.slots[<SLOT>]` instead\n   */\n  getSlotMaster(slot) {\n    return this.slots[slot].master;\n  }\n}\n_RedisCluster_options = new WeakMap(), _RedisCluster_slots = new WeakMap(), _RedisCluster_Multi = new WeakMap(), _RedisCluster_instances = new WeakSet(), _RedisCluster_execute = async function _RedisCluster_execute(firstKey, isReadonly, executor) {\n  const maxCommandRedirections = __classPrivateFieldGet(this, _RedisCluster_options, \"f\").maxCommandRedirections ?? 16;\n  let client = await __classPrivateFieldGet(this, _RedisCluster_slots, \"f\").getClient(firstKey, isReadonly);\n  for (let i = 0;; i++) {\n    try {\n      return await executor(client);\n    } catch (err) {\n      if (++i > maxCommandRedirections || !(err instanceof errors_1.ErrorReply)) {\n        throw err;\n      }\n      if (err.message.startsWith('ASK')) {\n        const address = err.message.substring(err.message.lastIndexOf(' ') + 1);\n        let redirectTo = await __classPrivateFieldGet(this, _RedisCluster_slots, \"f\").getMasterByAddress(address);\n        if (!redirectTo) {\n          await __classPrivateFieldGet(this, _RedisCluster_slots, \"f\").rediscover(client);\n          redirectTo = await __classPrivateFieldGet(this, _RedisCluster_slots, \"f\").getMasterByAddress(address);\n        }\n        if (!redirectTo) {\n          throw new Error(`Cannot find node ${address}`);\n        }\n        await redirectTo.asking();\n        client = redirectTo;\n        continue;\n      } else if (err.message.startsWith('MOVED')) {\n        await __classPrivateFieldGet(this, _RedisCluster_slots, \"f\").rediscover(client);\n        client = await __classPrivateFieldGet(this, _RedisCluster_slots, \"f\").getClient(firstKey, isReadonly);\n        continue;\n      }\n      throw err;\n    }\n  }\n};\nexports.default = RedisCluster;\n(0, commander_1.attachCommands)({\n  BaseClass: RedisCluster,\n  commands: commands_1.default,\n  executor: RedisCluster.prototype.commandsExecutor\n});", "map": {"version": 3, "names": ["__classPrivateFieldGet", "receiver", "state", "kind", "f", "TypeError", "has", "call", "value", "get", "__classPrivateFieldSet", "set", "_RedisCluster_instances", "_RedisCluster_options", "_RedisCluster_slots", "_RedisCluster_Multi", "_RedisCluster_execute", "Object", "defineProperty", "exports", "commands_1", "require", "cluster_slots_1", "commander_1", "events_1", "multi_command_1", "errors_1", "RedisCluster", "EventEmitter", "extractFirstKey", "command", "originalArgs", "redisArgs", "FIRST_KEY_INDEX", "undefined", "create", "options", "attachExtensions", "BaseClass", "modulesExecutor", "prototype", "commandsExecutor", "modules", "functionsExecutor", "functions", "scriptsExecutor", "scripts", "slots", "shards", "masters", "replicas", "nodeByAddress", "pubSubNode", "isOpen", "constructor", "add", "enumerable", "configurable", "writable", "MULTI", "SUBSCRIBE", "UNSUBSCRIBE", "PSUBSCRIBE", "PUNSUBSCRIBE", "SSUBSCRIBE", "SUNSUBSCRIBE", "default", "emit", "bind", "extend", "duplicate", "overrides", "getPrototypeOf", "connect", "args", "jsArgs", "transformCommandArguments", "transformCommandReply", "sendCommand", "IS_READ_ONLY", "preserve", "firstKey", "is<PERSON><PERSON><PERSON>ly", "client", "fn", "name", "executeFunction", "script", "executeScript", "routing", "commands", "chainId", "multiExecutor", "channels", "listener", "bufferMode", "getPubSubClient", "executeUnsubscribeCommand", "patterns", "maxCommandRedirections", "firstChannel", "Array", "isArray", "getShardedPubSubClient", "i", "err", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "message", "startsWith", "rediscover", "executeShardedUnsubscribeCommand", "quit", "disconnect", "nodeClient", "node", "getRandomNode", "getSlotRandomNode", "slot", "getMasters", "getSlotMaster", "master", "WeakMap", "WeakSet", "executor", "getClient", "address", "substring", "lastIndexOf", "redirectTo", "getMasterByAddress", "Error", "asking", "attachCommands"], "sources": ["C:/tmsft/node_modules/@redis/client/dist/lib/cluster/index.js"], "sourcesContent": ["\"use strict\";\nvar __classPrivateFieldGet = (this && this.__classPrivateFieldGet) || function (receiver, state, kind, f) {\n    if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a getter\");\n    if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot read private member from an object whose class did not declare it\");\n    return kind === \"m\" ? f : kind === \"a\" ? f.call(receiver) : f ? f.value : state.get(receiver);\n};\nvar __classPrivateFieldSet = (this && this.__classPrivateFieldSet) || function (receiver, state, value, kind, f) {\n    if (kind === \"m\") throw new TypeError(\"Private method is not writable\");\n    if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a setter\");\n    if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot write private member to an object whose class did not declare it\");\n    return (kind === \"a\" ? f.call(receiver, value) : f ? f.value = value : state.set(receiver, value)), value;\n};\nvar _RedisCluster_instances, _RedisCluster_options, _RedisCluster_slots, _RedisCluster_Multi, _RedisCluster_execute;\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst commands_1 = require(\"./commands\");\nconst cluster_slots_1 = require(\"./cluster-slots\");\nconst commander_1 = require(\"../commander\");\nconst events_1 = require(\"events\");\nconst multi_command_1 = require(\"./multi-command\");\nconst errors_1 = require(\"../errors\");\nclass RedisCluster extends events_1.EventEmitter {\n    static extractFirstKey(command, originalArgs, redisArgs) {\n        if (command.FIRST_KEY_INDEX === undefined) {\n            return undefined;\n        }\n        else if (typeof command.FIRST_KEY_INDEX === 'number') {\n            return redisArgs[command.FIRST_KEY_INDEX];\n        }\n        return command.FIRST_KEY_INDEX(...originalArgs);\n    }\n    static create(options) {\n        return new ((0, commander_1.attachExtensions)({\n            BaseClass: RedisCluster,\n            modulesExecutor: RedisCluster.prototype.commandsExecutor,\n            modules: options?.modules,\n            functionsExecutor: RedisCluster.prototype.functionsExecutor,\n            functions: options?.functions,\n            scriptsExecutor: RedisCluster.prototype.scriptsExecutor,\n            scripts: options?.scripts\n        }))(options);\n    }\n    get slots() {\n        return __classPrivateFieldGet(this, _RedisCluster_slots, \"f\").slots;\n    }\n    get shards() {\n        return __classPrivateFieldGet(this, _RedisCluster_slots, \"f\").shards;\n    }\n    get masters() {\n        return __classPrivateFieldGet(this, _RedisCluster_slots, \"f\").masters;\n    }\n    get replicas() {\n        return __classPrivateFieldGet(this, _RedisCluster_slots, \"f\").replicas;\n    }\n    get nodeByAddress() {\n        return __classPrivateFieldGet(this, _RedisCluster_slots, \"f\").nodeByAddress;\n    }\n    get pubSubNode() {\n        return __classPrivateFieldGet(this, _RedisCluster_slots, \"f\").pubSubNode;\n    }\n    get isOpen() {\n        return __classPrivateFieldGet(this, _RedisCluster_slots, \"f\").isOpen;\n    }\n    constructor(options) {\n        super();\n        _RedisCluster_instances.add(this);\n        _RedisCluster_options.set(this, void 0);\n        _RedisCluster_slots.set(this, void 0);\n        _RedisCluster_Multi.set(this, void 0);\n        Object.defineProperty(this, \"multi\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: this.MULTI\n        });\n        Object.defineProperty(this, \"subscribe\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: this.SUBSCRIBE\n        });\n        Object.defineProperty(this, \"unsubscribe\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: this.UNSUBSCRIBE\n        });\n        Object.defineProperty(this, \"pSubscribe\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: this.PSUBSCRIBE\n        });\n        Object.defineProperty(this, \"pUnsubscribe\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: this.PUNSUBSCRIBE\n        });\n        Object.defineProperty(this, \"sSubscribe\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: this.SSUBSCRIBE\n        });\n        Object.defineProperty(this, \"sUnsubscribe\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: this.SUNSUBSCRIBE\n        });\n        __classPrivateFieldSet(this, _RedisCluster_options, options, \"f\");\n        __classPrivateFieldSet(this, _RedisCluster_slots, new cluster_slots_1.default(options, this.emit.bind(this)), \"f\");\n        __classPrivateFieldSet(this, _RedisCluster_Multi, multi_command_1.default.extend(options), \"f\");\n    }\n    duplicate(overrides) {\n        return new (Object.getPrototypeOf(this).constructor)({\n            ...__classPrivateFieldGet(this, _RedisCluster_options, \"f\"),\n            ...overrides\n        });\n    }\n    connect() {\n        return __classPrivateFieldGet(this, _RedisCluster_slots, \"f\").connect();\n    }\n    async commandsExecutor(command, args) {\n        const { jsArgs, args: redisArgs, options } = (0, commander_1.transformCommandArguments)(command, args);\n        return (0, commander_1.transformCommandReply)(command, await this.sendCommand(RedisCluster.extractFirstKey(command, jsArgs, redisArgs), command.IS_READ_ONLY, redisArgs, options), redisArgs.preserve);\n    }\n    async sendCommand(firstKey, isReadonly, args, options) {\n        return __classPrivateFieldGet(this, _RedisCluster_instances, \"m\", _RedisCluster_execute).call(this, firstKey, isReadonly, client => client.sendCommand(args, options));\n    }\n    async functionsExecutor(fn, args, name) {\n        const { args: redisArgs, options } = (0, commander_1.transformCommandArguments)(fn, args);\n        return (0, commander_1.transformCommandReply)(fn, await this.executeFunction(name, fn, args, redisArgs, options), redisArgs.preserve);\n    }\n    async executeFunction(name, fn, originalArgs, redisArgs, options) {\n        return __classPrivateFieldGet(this, _RedisCluster_instances, \"m\", _RedisCluster_execute).call(this, RedisCluster.extractFirstKey(fn, originalArgs, redisArgs), fn.IS_READ_ONLY, client => client.executeFunction(name, fn, redisArgs, options));\n    }\n    async scriptsExecutor(script, args) {\n        const { args: redisArgs, options } = (0, commander_1.transformCommandArguments)(script, args);\n        return (0, commander_1.transformCommandReply)(script, await this.executeScript(script, args, redisArgs, options), redisArgs.preserve);\n    }\n    async executeScript(script, originalArgs, redisArgs, options) {\n        return __classPrivateFieldGet(this, _RedisCluster_instances, \"m\", _RedisCluster_execute).call(this, RedisCluster.extractFirstKey(script, originalArgs, redisArgs), script.IS_READ_ONLY, client => client.executeScript(script, redisArgs, options));\n    }\n    MULTI(routing) {\n        return new (__classPrivateFieldGet(this, _RedisCluster_Multi, \"f\"))((commands, firstKey, chainId) => {\n            return __classPrivateFieldGet(this, _RedisCluster_instances, \"m\", _RedisCluster_execute).call(this, firstKey, false, client => client.multiExecutor(commands, undefined, chainId));\n        }, routing);\n    }\n    async SUBSCRIBE(channels, listener, bufferMode) {\n        return (await __classPrivateFieldGet(this, _RedisCluster_slots, \"f\").getPubSubClient())\n            .SUBSCRIBE(channels, listener, bufferMode);\n    }\n    async UNSUBSCRIBE(channels, listener, bufferMode) {\n        return __classPrivateFieldGet(this, _RedisCluster_slots, \"f\").executeUnsubscribeCommand(client => client.UNSUBSCRIBE(channels, listener, bufferMode));\n    }\n    async PSUBSCRIBE(patterns, listener, bufferMode) {\n        return (await __classPrivateFieldGet(this, _RedisCluster_slots, \"f\").getPubSubClient())\n            .PSUBSCRIBE(patterns, listener, bufferMode);\n    }\n    async PUNSUBSCRIBE(patterns, listener, bufferMode) {\n        return __classPrivateFieldGet(this, _RedisCluster_slots, \"f\").executeUnsubscribeCommand(client => client.PUNSUBSCRIBE(patterns, listener, bufferMode));\n    }\n    async SSUBSCRIBE(channels, listener, bufferMode) {\n        const maxCommandRedirections = __classPrivateFieldGet(this, _RedisCluster_options, \"f\").maxCommandRedirections ?? 16, firstChannel = Array.isArray(channels) ? channels[0] : channels;\n        let client = await __classPrivateFieldGet(this, _RedisCluster_slots, \"f\").getShardedPubSubClient(firstChannel);\n        for (let i = 0;; i++) {\n            try {\n                return await client.SSUBSCRIBE(channels, listener, bufferMode);\n            }\n            catch (err) {\n                if (++i > maxCommandRedirections || !(err instanceof errors_1.ErrorReply)) {\n                    throw err;\n                }\n                if (err.message.startsWith('MOVED')) {\n                    await __classPrivateFieldGet(this, _RedisCluster_slots, \"f\").rediscover(client);\n                    client = await __classPrivateFieldGet(this, _RedisCluster_slots, \"f\").getShardedPubSubClient(firstChannel);\n                    continue;\n                }\n                throw err;\n            }\n        }\n    }\n    SUNSUBSCRIBE(channels, listener, bufferMode) {\n        return __classPrivateFieldGet(this, _RedisCluster_slots, \"f\").executeShardedUnsubscribeCommand(Array.isArray(channels) ? channels[0] : channels, client => client.SUNSUBSCRIBE(channels, listener, bufferMode));\n    }\n    quit() {\n        return __classPrivateFieldGet(this, _RedisCluster_slots, \"f\").quit();\n    }\n    disconnect() {\n        return __classPrivateFieldGet(this, _RedisCluster_slots, \"f\").disconnect();\n    }\n    nodeClient(node) {\n        return __classPrivateFieldGet(this, _RedisCluster_slots, \"f\").nodeClient(node);\n    }\n    getRandomNode() {\n        return __classPrivateFieldGet(this, _RedisCluster_slots, \"f\").getRandomNode();\n    }\n    getSlotRandomNode(slot) {\n        return __classPrivateFieldGet(this, _RedisCluster_slots, \"f\").getSlotRandomNode(slot);\n    }\n    /**\n     * @deprecated use `.masters` instead\n     */\n    getMasters() {\n        return this.masters;\n    }\n    /**\n     * @deprecated use `.slots[<SLOT>]` instead\n     */\n    getSlotMaster(slot) {\n        return this.slots[slot].master;\n    }\n}\n_RedisCluster_options = new WeakMap(), _RedisCluster_slots = new WeakMap(), _RedisCluster_Multi = new WeakMap(), _RedisCluster_instances = new WeakSet(), _RedisCluster_execute = async function _RedisCluster_execute(firstKey, isReadonly, executor) {\n    const maxCommandRedirections = __classPrivateFieldGet(this, _RedisCluster_options, \"f\").maxCommandRedirections ?? 16;\n    let client = await __classPrivateFieldGet(this, _RedisCluster_slots, \"f\").getClient(firstKey, isReadonly);\n    for (let i = 0;; i++) {\n        try {\n            return await executor(client);\n        }\n        catch (err) {\n            if (++i > maxCommandRedirections || !(err instanceof errors_1.ErrorReply)) {\n                throw err;\n            }\n            if (err.message.startsWith('ASK')) {\n                const address = err.message.substring(err.message.lastIndexOf(' ') + 1);\n                let redirectTo = await __classPrivateFieldGet(this, _RedisCluster_slots, \"f\").getMasterByAddress(address);\n                if (!redirectTo) {\n                    await __classPrivateFieldGet(this, _RedisCluster_slots, \"f\").rediscover(client);\n                    redirectTo = await __classPrivateFieldGet(this, _RedisCluster_slots, \"f\").getMasterByAddress(address);\n                }\n                if (!redirectTo) {\n                    throw new Error(`Cannot find node ${address}`);\n                }\n                await redirectTo.asking();\n                client = redirectTo;\n                continue;\n            }\n            else if (err.message.startsWith('MOVED')) {\n                await __classPrivateFieldGet(this, _RedisCluster_slots, \"f\").rediscover(client);\n                client = await __classPrivateFieldGet(this, _RedisCluster_slots, \"f\").getClient(firstKey, isReadonly);\n                continue;\n            }\n            throw err;\n        }\n    }\n};\nexports.default = RedisCluster;\n(0, commander_1.attachCommands)({\n    BaseClass: RedisCluster,\n    commands: commands_1.default,\n    executor: RedisCluster.prototype.commandsExecutor\n});\n"], "mappings": "AAAA,YAAY;;AACZ,IAAIA,sBAAsB,GAAI,IAAI,IAAI,IAAI,CAACA,sBAAsB,IAAK,UAAUC,QAAQ,EAAEC,KAAK,EAAEC,IAAI,EAAEC,CAAC,EAAE;EACtG,IAAID,IAAI,KAAK,GAAG,IAAI,CAACC,CAAC,EAAE,MAAM,IAAIC,SAAS,CAAC,+CAA+C,CAAC;EAC5F,IAAI,OAAOH,KAAK,KAAK,UAAU,GAAGD,QAAQ,KAAKC,KAAK,IAAI,CAACE,CAAC,GAAG,CAACF,KAAK,CAACI,GAAG,CAACL,QAAQ,CAAC,EAAE,MAAM,IAAII,SAAS,CAAC,0EAA0E,CAAC;EAClL,OAAOF,IAAI,KAAK,GAAG,GAAGC,CAAC,GAAGD,IAAI,KAAK,GAAG,GAAGC,CAAC,CAACG,IAAI,CAACN,QAAQ,CAAC,GAAGG,CAAC,GAAGA,CAAC,CAACI,KAAK,GAAGN,KAAK,CAACO,GAAG,CAACR,QAAQ,CAAC;AACjG,CAAC;AACD,IAAIS,sBAAsB,GAAI,IAAI,IAAI,IAAI,CAACA,sBAAsB,IAAK,UAAUT,QAAQ,EAAEC,KAAK,EAAEM,KAAK,EAAEL,IAAI,EAAEC,CAAC,EAAE;EAC7G,IAAID,IAAI,KAAK,GAAG,EAAE,MAAM,IAAIE,SAAS,CAAC,gCAAgC,CAAC;EACvE,IAAIF,IAAI,KAAK,GAAG,IAAI,CAACC,CAAC,EAAE,MAAM,IAAIC,SAAS,CAAC,+CAA+C,CAAC;EAC5F,IAAI,OAAOH,KAAK,KAAK,UAAU,GAAGD,QAAQ,KAAKC,KAAK,IAAI,CAACE,CAAC,GAAG,CAACF,KAAK,CAACI,GAAG,CAACL,QAAQ,CAAC,EAAE,MAAM,IAAII,SAAS,CAAC,yEAAyE,CAAC;EACjL,OAAQF,IAAI,KAAK,GAAG,GAAGC,CAAC,CAACG,IAAI,CAACN,QAAQ,EAAEO,KAAK,CAAC,GAAGJ,CAAC,GAAGA,CAAC,CAACI,KAAK,GAAGA,KAAK,GAAGN,KAAK,CAACS,GAAG,CAACV,QAAQ,EAAEO,KAAK,CAAC,EAAGA,KAAK;AAC7G,CAAC;AACD,IAAII,uBAAuB,EAAEC,qBAAqB,EAAEC,mBAAmB,EAAEC,mBAAmB,EAAEC,qBAAqB;AACnHC,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEX,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7D,MAAMY,UAAU,GAAGC,OAAO,CAAC,YAAY,CAAC;AACxC,MAAMC,eAAe,GAAGD,OAAO,CAAC,iBAAiB,CAAC;AAClD,MAAME,WAAW,GAAGF,OAAO,CAAC,cAAc,CAAC;AAC3C,MAAMG,QAAQ,GAAGH,OAAO,CAAC,QAAQ,CAAC;AAClC,MAAMI,eAAe,GAAGJ,OAAO,CAAC,iBAAiB,CAAC;AAClD,MAAMK,QAAQ,GAAGL,OAAO,CAAC,WAAW,CAAC;AACrC,MAAMM,YAAY,SAASH,QAAQ,CAACI,YAAY,CAAC;EAC7C,OAAOC,eAAeA,CAACC,OAAO,EAAEC,YAAY,EAAEC,SAAS,EAAE;IACrD,IAAIF,OAAO,CAACG,eAAe,KAAKC,SAAS,EAAE;MACvC,OAAOA,SAAS;IACpB,CAAC,MACI,IAAI,OAAOJ,OAAO,CAACG,eAAe,KAAK,QAAQ,EAAE;MAClD,OAAOD,SAAS,CAACF,OAAO,CAACG,eAAe,CAAC;IAC7C;IACA,OAAOH,OAAO,CAACG,eAAe,CAAC,GAAGF,YAAY,CAAC;EACnD;EACA,OAAOI,MAAMA,CAACC,OAAO,EAAE;IACnB,OAAO,KAAK,CAAC,CAAC,EAAEb,WAAW,CAACc,gBAAgB,EAAE;MAC1CC,SAAS,EAAEX,YAAY;MACvBY,eAAe,EAAEZ,YAAY,CAACa,SAAS,CAACC,gBAAgB;MACxDC,OAAO,EAAEN,OAAO,EAAEM,OAAO;MACzBC,iBAAiB,EAAEhB,YAAY,CAACa,SAAS,CAACG,iBAAiB;MAC3DC,SAAS,EAAER,OAAO,EAAEQ,SAAS;MAC7BC,eAAe,EAAElB,YAAY,CAACa,SAAS,CAACK,eAAe;MACvDC,OAAO,EAAEV,OAAO,EAAEU;IACtB,CAAC,CAAC,EAAEV,OAAO,CAAC;EAChB;EACA,IAAIW,KAAKA,CAAA,EAAG;IACR,OAAO/C,sBAAsB,CAAC,IAAI,EAAEc,mBAAmB,EAAE,GAAG,CAAC,CAACiC,KAAK;EACvE;EACA,IAAIC,MAAMA,CAAA,EAAG;IACT,OAAOhD,sBAAsB,CAAC,IAAI,EAAEc,mBAAmB,EAAE,GAAG,CAAC,CAACkC,MAAM;EACxE;EACA,IAAIC,OAAOA,CAAA,EAAG;IACV,OAAOjD,sBAAsB,CAAC,IAAI,EAAEc,mBAAmB,EAAE,GAAG,CAAC,CAACmC,OAAO;EACzE;EACA,IAAIC,QAAQA,CAAA,EAAG;IACX,OAAOlD,sBAAsB,CAAC,IAAI,EAAEc,mBAAmB,EAAE,GAAG,CAAC,CAACoC,QAAQ;EAC1E;EACA,IAAIC,aAAaA,CAAA,EAAG;IAChB,OAAOnD,sBAAsB,CAAC,IAAI,EAAEc,mBAAmB,EAAE,GAAG,CAAC,CAACqC,aAAa;EAC/E;EACA,IAAIC,UAAUA,CAAA,EAAG;IACb,OAAOpD,sBAAsB,CAAC,IAAI,EAAEc,mBAAmB,EAAE,GAAG,CAAC,CAACsC,UAAU;EAC5E;EACA,IAAIC,MAAMA,CAAA,EAAG;IACT,OAAOrD,sBAAsB,CAAC,IAAI,EAAEc,mBAAmB,EAAE,GAAG,CAAC,CAACuC,MAAM;EACxE;EACAC,WAAWA,CAAClB,OAAO,EAAE;IACjB,KAAK,CAAC,CAAC;IACPxB,uBAAuB,CAAC2C,GAAG,CAAC,IAAI,CAAC;IACjC1C,qBAAqB,CAACF,GAAG,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;IACvCG,mBAAmB,CAACH,GAAG,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;IACrCI,mBAAmB,CAACJ,GAAG,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;IACrCM,MAAM,CAACC,cAAc,CAAC,IAAI,EAAE,OAAO,EAAE;MACjCsC,UAAU,EAAE,IAAI;MAChBC,YAAY,EAAE,IAAI;MAClBC,QAAQ,EAAE,IAAI;MACdlD,KAAK,EAAE,IAAI,CAACmD;IAChB,CAAC,CAAC;IACF1C,MAAM,CAACC,cAAc,CAAC,IAAI,EAAE,WAAW,EAAE;MACrCsC,UAAU,EAAE,IAAI;MAChBC,YAAY,EAAE,IAAI;MAClBC,QAAQ,EAAE,IAAI;MACdlD,KAAK,EAAE,IAAI,CAACoD;IAChB,CAAC,CAAC;IACF3C,MAAM,CAACC,cAAc,CAAC,IAAI,EAAE,aAAa,EAAE;MACvCsC,UAAU,EAAE,IAAI;MAChBC,YAAY,EAAE,IAAI;MAClBC,QAAQ,EAAE,IAAI;MACdlD,KAAK,EAAE,IAAI,CAACqD;IAChB,CAAC,CAAC;IACF5C,MAAM,CAACC,cAAc,CAAC,IAAI,EAAE,YAAY,EAAE;MACtCsC,UAAU,EAAE,IAAI;MAChBC,YAAY,EAAE,IAAI;MAClBC,QAAQ,EAAE,IAAI;MACdlD,KAAK,EAAE,IAAI,CAACsD;IAChB,CAAC,CAAC;IACF7C,MAAM,CAACC,cAAc,CAAC,IAAI,EAAE,cAAc,EAAE;MACxCsC,UAAU,EAAE,IAAI;MAChBC,YAAY,EAAE,IAAI;MAClBC,QAAQ,EAAE,IAAI;MACdlD,KAAK,EAAE,IAAI,CAACuD;IAChB,CAAC,CAAC;IACF9C,MAAM,CAACC,cAAc,CAAC,IAAI,EAAE,YAAY,EAAE;MACtCsC,UAAU,EAAE,IAAI;MAChBC,YAAY,EAAE,IAAI;MAClBC,QAAQ,EAAE,IAAI;MACdlD,KAAK,EAAE,IAAI,CAACwD;IAChB,CAAC,CAAC;IACF/C,MAAM,CAACC,cAAc,CAAC,IAAI,EAAE,cAAc,EAAE;MACxCsC,UAAU,EAAE,IAAI;MAChBC,YAAY,EAAE,IAAI;MAClBC,QAAQ,EAAE,IAAI;MACdlD,KAAK,EAAE,IAAI,CAACyD;IAChB,CAAC,CAAC;IACFvD,sBAAsB,CAAC,IAAI,EAAEG,qBAAqB,EAAEuB,OAAO,EAAE,GAAG,CAAC;IACjE1B,sBAAsB,CAAC,IAAI,EAAEI,mBAAmB,EAAE,IAAIQ,eAAe,CAAC4C,OAAO,CAAC9B,OAAO,EAAE,IAAI,CAAC+B,IAAI,CAACC,IAAI,CAAC,IAAI,CAAC,CAAC,EAAE,GAAG,CAAC;IAClH1D,sBAAsB,CAAC,IAAI,EAAEK,mBAAmB,EAAEU,eAAe,CAACyC,OAAO,CAACG,MAAM,CAACjC,OAAO,CAAC,EAAE,GAAG,CAAC;EACnG;EACAkC,SAASA,CAACC,SAAS,EAAE;IACjB,OAAO,KAAKtD,MAAM,CAACuD,cAAc,CAAC,IAAI,CAAC,CAAClB,WAAW,EAAE;MACjD,GAAGtD,sBAAsB,CAAC,IAAI,EAAEa,qBAAqB,EAAE,GAAG,CAAC;MAC3D,GAAG0D;IACP,CAAC,CAAC;EACN;EACAE,OAAOA,CAAA,EAAG;IACN,OAAOzE,sBAAsB,CAAC,IAAI,EAAEc,mBAAmB,EAAE,GAAG,CAAC,CAAC2D,OAAO,CAAC,CAAC;EAC3E;EACA,MAAMhC,gBAAgBA,CAACX,OAAO,EAAE4C,IAAI,EAAE;IAClC,MAAM;MAAEC,MAAM;MAAED,IAAI,EAAE1C,SAAS;MAAEI;IAAQ,CAAC,GAAG,CAAC,CAAC,EAAEb,WAAW,CAACqD,yBAAyB,EAAE9C,OAAO,EAAE4C,IAAI,CAAC;IACtG,OAAO,CAAC,CAAC,EAAEnD,WAAW,CAACsD,qBAAqB,EAAE/C,OAAO,EAAE,MAAM,IAAI,CAACgD,WAAW,CAACnD,YAAY,CAACE,eAAe,CAACC,OAAO,EAAE6C,MAAM,EAAE3C,SAAS,CAAC,EAAEF,OAAO,CAACiD,YAAY,EAAE/C,SAAS,EAAEI,OAAO,CAAC,EAAEJ,SAAS,CAACgD,QAAQ,CAAC;EAC1M;EACA,MAAMF,WAAWA,CAACG,QAAQ,EAAEC,UAAU,EAAER,IAAI,EAAEtC,OAAO,EAAE;IACnD,OAAOpC,sBAAsB,CAAC,IAAI,EAAEY,uBAAuB,EAAE,GAAG,EAAEI,qBAAqB,CAAC,CAACT,IAAI,CAAC,IAAI,EAAE0E,QAAQ,EAAEC,UAAU,EAAEC,MAAM,IAAIA,MAAM,CAACL,WAAW,CAACJ,IAAI,EAAEtC,OAAO,CAAC,CAAC;EAC1K;EACA,MAAMO,iBAAiBA,CAACyC,EAAE,EAAEV,IAAI,EAAEW,IAAI,EAAE;IACpC,MAAM;MAAEX,IAAI,EAAE1C,SAAS;MAAEI;IAAQ,CAAC,GAAG,CAAC,CAAC,EAAEb,WAAW,CAACqD,yBAAyB,EAAEQ,EAAE,EAAEV,IAAI,CAAC;IACzF,OAAO,CAAC,CAAC,EAAEnD,WAAW,CAACsD,qBAAqB,EAAEO,EAAE,EAAE,MAAM,IAAI,CAACE,eAAe,CAACD,IAAI,EAAED,EAAE,EAAEV,IAAI,EAAE1C,SAAS,EAAEI,OAAO,CAAC,EAAEJ,SAAS,CAACgD,QAAQ,CAAC;EACzI;EACA,MAAMM,eAAeA,CAACD,IAAI,EAAED,EAAE,EAAErD,YAAY,EAAEC,SAAS,EAAEI,OAAO,EAAE;IAC9D,OAAOpC,sBAAsB,CAAC,IAAI,EAAEY,uBAAuB,EAAE,GAAG,EAAEI,qBAAqB,CAAC,CAACT,IAAI,CAAC,IAAI,EAAEoB,YAAY,CAACE,eAAe,CAACuD,EAAE,EAAErD,YAAY,EAAEC,SAAS,CAAC,EAAEoD,EAAE,CAACL,YAAY,EAAEI,MAAM,IAAIA,MAAM,CAACG,eAAe,CAACD,IAAI,EAAED,EAAE,EAAEpD,SAAS,EAAEI,OAAO,CAAC,CAAC;EACnP;EACA,MAAMS,eAAeA,CAAC0C,MAAM,EAAEb,IAAI,EAAE;IAChC,MAAM;MAAEA,IAAI,EAAE1C,SAAS;MAAEI;IAAQ,CAAC,GAAG,CAAC,CAAC,EAAEb,WAAW,CAACqD,yBAAyB,EAAEW,MAAM,EAAEb,IAAI,CAAC;IAC7F,OAAO,CAAC,CAAC,EAAEnD,WAAW,CAACsD,qBAAqB,EAAEU,MAAM,EAAE,MAAM,IAAI,CAACC,aAAa,CAACD,MAAM,EAAEb,IAAI,EAAE1C,SAAS,EAAEI,OAAO,CAAC,EAAEJ,SAAS,CAACgD,QAAQ,CAAC;EACzI;EACA,MAAMQ,aAAaA,CAACD,MAAM,EAAExD,YAAY,EAAEC,SAAS,EAAEI,OAAO,EAAE;IAC1D,OAAOpC,sBAAsB,CAAC,IAAI,EAAEY,uBAAuB,EAAE,GAAG,EAAEI,qBAAqB,CAAC,CAACT,IAAI,CAAC,IAAI,EAAEoB,YAAY,CAACE,eAAe,CAAC0D,MAAM,EAAExD,YAAY,EAAEC,SAAS,CAAC,EAAEuD,MAAM,CAACR,YAAY,EAAEI,MAAM,IAAIA,MAAM,CAACK,aAAa,CAACD,MAAM,EAAEvD,SAAS,EAAEI,OAAO,CAAC,CAAC;EACvP;EACAuB,KAAKA,CAAC8B,OAAO,EAAE;IACX,OAAO,KAAKzF,sBAAsB,CAAC,IAAI,EAAEe,mBAAmB,EAAE,GAAG,CAAC,EAAE,CAAC2E,QAAQ,EAAET,QAAQ,EAAEU,OAAO,KAAK;MACjG,OAAO3F,sBAAsB,CAAC,IAAI,EAAEY,uBAAuB,EAAE,GAAG,EAAEI,qBAAqB,CAAC,CAACT,IAAI,CAAC,IAAI,EAAE0E,QAAQ,EAAE,KAAK,EAAEE,MAAM,IAAIA,MAAM,CAACS,aAAa,CAACF,QAAQ,EAAExD,SAAS,EAAEyD,OAAO,CAAC,CAAC;IACtL,CAAC,EAAEF,OAAO,CAAC;EACf;EACA,MAAM7B,SAASA,CAACiC,QAAQ,EAAEC,QAAQ,EAAEC,UAAU,EAAE;IAC5C,OAAO,CAAC,MAAM/F,sBAAsB,CAAC,IAAI,EAAEc,mBAAmB,EAAE,GAAG,CAAC,CAACkF,eAAe,CAAC,CAAC,EACjFpC,SAAS,CAACiC,QAAQ,EAAEC,QAAQ,EAAEC,UAAU,CAAC;EAClD;EACA,MAAMlC,WAAWA,CAACgC,QAAQ,EAAEC,QAAQ,EAAEC,UAAU,EAAE;IAC9C,OAAO/F,sBAAsB,CAAC,IAAI,EAAEc,mBAAmB,EAAE,GAAG,CAAC,CAACmF,yBAAyB,CAACd,MAAM,IAAIA,MAAM,CAACtB,WAAW,CAACgC,QAAQ,EAAEC,QAAQ,EAAEC,UAAU,CAAC,CAAC;EACzJ;EACA,MAAMjC,UAAUA,CAACoC,QAAQ,EAAEJ,QAAQ,EAAEC,UAAU,EAAE;IAC7C,OAAO,CAAC,MAAM/F,sBAAsB,CAAC,IAAI,EAAEc,mBAAmB,EAAE,GAAG,CAAC,CAACkF,eAAe,CAAC,CAAC,EACjFlC,UAAU,CAACoC,QAAQ,EAAEJ,QAAQ,EAAEC,UAAU,CAAC;EACnD;EACA,MAAMhC,YAAYA,CAACmC,QAAQ,EAAEJ,QAAQ,EAAEC,UAAU,EAAE;IAC/C,OAAO/F,sBAAsB,CAAC,IAAI,EAAEc,mBAAmB,EAAE,GAAG,CAAC,CAACmF,yBAAyB,CAACd,MAAM,IAAIA,MAAM,CAACpB,YAAY,CAACmC,QAAQ,EAAEJ,QAAQ,EAAEC,UAAU,CAAC,CAAC;EAC1J;EACA,MAAM/B,UAAUA,CAAC6B,QAAQ,EAAEC,QAAQ,EAAEC,UAAU,EAAE;IAC7C,MAAMI,sBAAsB,GAAGnG,sBAAsB,CAAC,IAAI,EAAEa,qBAAqB,EAAE,GAAG,CAAC,CAACsF,sBAAsB,IAAI,EAAE;MAAEC,YAAY,GAAGC,KAAK,CAACC,OAAO,CAACT,QAAQ,CAAC,GAAGA,QAAQ,CAAC,CAAC,CAAC,GAAGA,QAAQ;IACrL,IAAIV,MAAM,GAAG,MAAMnF,sBAAsB,CAAC,IAAI,EAAEc,mBAAmB,EAAE,GAAG,CAAC,CAACyF,sBAAsB,CAACH,YAAY,CAAC;IAC9G,KAAK,IAAII,CAAC,GAAG,CAAC,GAAGA,CAAC,EAAE,EAAE;MAClB,IAAI;QACA,OAAO,MAAMrB,MAAM,CAACnB,UAAU,CAAC6B,QAAQ,EAAEC,QAAQ,EAAEC,UAAU,CAAC;MAClE,CAAC,CACD,OAAOU,GAAG,EAAE;QACR,IAAI,EAAED,CAAC,GAAGL,sBAAsB,IAAI,EAAEM,GAAG,YAAY/E,QAAQ,CAACgF,UAAU,CAAC,EAAE;UACvE,MAAMD,GAAG;QACb;QACA,IAAIA,GAAG,CAACE,OAAO,CAACC,UAAU,CAAC,OAAO,CAAC,EAAE;UACjC,MAAM5G,sBAAsB,CAAC,IAAI,EAAEc,mBAAmB,EAAE,GAAG,CAAC,CAAC+F,UAAU,CAAC1B,MAAM,CAAC;UAC/EA,MAAM,GAAG,MAAMnF,sBAAsB,CAAC,IAAI,EAAEc,mBAAmB,EAAE,GAAG,CAAC,CAACyF,sBAAsB,CAACH,YAAY,CAAC;UAC1G;QACJ;QACA,MAAMK,GAAG;MACb;IACJ;EACJ;EACAxC,YAAYA,CAAC4B,QAAQ,EAAEC,QAAQ,EAAEC,UAAU,EAAE;IACzC,OAAO/F,sBAAsB,CAAC,IAAI,EAAEc,mBAAmB,EAAE,GAAG,CAAC,CAACgG,gCAAgC,CAACT,KAAK,CAACC,OAAO,CAACT,QAAQ,CAAC,GAAGA,QAAQ,CAAC,CAAC,CAAC,GAAGA,QAAQ,EAAEV,MAAM,IAAIA,MAAM,CAAClB,YAAY,CAAC4B,QAAQ,EAAEC,QAAQ,EAAEC,UAAU,CAAC,CAAC;EACnN;EACAgB,IAAIA,CAAA,EAAG;IACH,OAAO/G,sBAAsB,CAAC,IAAI,EAAEc,mBAAmB,EAAE,GAAG,CAAC,CAACiG,IAAI,CAAC,CAAC;EACxE;EACAC,UAAUA,CAAA,EAAG;IACT,OAAOhH,sBAAsB,CAAC,IAAI,EAAEc,mBAAmB,EAAE,GAAG,CAAC,CAACkG,UAAU,CAAC,CAAC;EAC9E;EACAC,UAAUA,CAACC,IAAI,EAAE;IACb,OAAOlH,sBAAsB,CAAC,IAAI,EAAEc,mBAAmB,EAAE,GAAG,CAAC,CAACmG,UAAU,CAACC,IAAI,CAAC;EAClF;EACAC,aAAaA,CAAA,EAAG;IACZ,OAAOnH,sBAAsB,CAAC,IAAI,EAAEc,mBAAmB,EAAE,GAAG,CAAC,CAACqG,aAAa,CAAC,CAAC;EACjF;EACAC,iBAAiBA,CAACC,IAAI,EAAE;IACpB,OAAOrH,sBAAsB,CAAC,IAAI,EAAEc,mBAAmB,EAAE,GAAG,CAAC,CAACsG,iBAAiB,CAACC,IAAI,CAAC;EACzF;EACA;AACJ;AACA;EACIC,UAAUA,CAAA,EAAG;IACT,OAAO,IAAI,CAACrE,OAAO;EACvB;EACA;AACJ;AACA;EACIsE,aAAaA,CAACF,IAAI,EAAE;IAChB,OAAO,IAAI,CAACtE,KAAK,CAACsE,IAAI,CAAC,CAACG,MAAM;EAClC;AACJ;AACA3G,qBAAqB,GAAG,IAAI4G,OAAO,CAAC,CAAC,EAAE3G,mBAAmB,GAAG,IAAI2G,OAAO,CAAC,CAAC,EAAE1G,mBAAmB,GAAG,IAAI0G,OAAO,CAAC,CAAC,EAAE7G,uBAAuB,GAAG,IAAI8G,OAAO,CAAC,CAAC,EAAE1G,qBAAqB,GAAG,eAAeA,qBAAqBA,CAACiE,QAAQ,EAAEC,UAAU,EAAEyC,QAAQ,EAAE;EACnP,MAAMxB,sBAAsB,GAAGnG,sBAAsB,CAAC,IAAI,EAAEa,qBAAqB,EAAE,GAAG,CAAC,CAACsF,sBAAsB,IAAI,EAAE;EACpH,IAAIhB,MAAM,GAAG,MAAMnF,sBAAsB,CAAC,IAAI,EAAEc,mBAAmB,EAAE,GAAG,CAAC,CAAC8G,SAAS,CAAC3C,QAAQ,EAAEC,UAAU,CAAC;EACzG,KAAK,IAAIsB,CAAC,GAAG,CAAC,GAAGA,CAAC,EAAE,EAAE;IAClB,IAAI;MACA,OAAO,MAAMmB,QAAQ,CAACxC,MAAM,CAAC;IACjC,CAAC,CACD,OAAOsB,GAAG,EAAE;MACR,IAAI,EAAED,CAAC,GAAGL,sBAAsB,IAAI,EAAEM,GAAG,YAAY/E,QAAQ,CAACgF,UAAU,CAAC,EAAE;QACvE,MAAMD,GAAG;MACb;MACA,IAAIA,GAAG,CAACE,OAAO,CAACC,UAAU,CAAC,KAAK,CAAC,EAAE;QAC/B,MAAMiB,OAAO,GAAGpB,GAAG,CAACE,OAAO,CAACmB,SAAS,CAACrB,GAAG,CAACE,OAAO,CAACoB,WAAW,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QACvE,IAAIC,UAAU,GAAG,MAAMhI,sBAAsB,CAAC,IAAI,EAAEc,mBAAmB,EAAE,GAAG,CAAC,CAACmH,kBAAkB,CAACJ,OAAO,CAAC;QACzG,IAAI,CAACG,UAAU,EAAE;UACb,MAAMhI,sBAAsB,CAAC,IAAI,EAAEc,mBAAmB,EAAE,GAAG,CAAC,CAAC+F,UAAU,CAAC1B,MAAM,CAAC;UAC/E6C,UAAU,GAAG,MAAMhI,sBAAsB,CAAC,IAAI,EAAEc,mBAAmB,EAAE,GAAG,CAAC,CAACmH,kBAAkB,CAACJ,OAAO,CAAC;QACzG;QACA,IAAI,CAACG,UAAU,EAAE;UACb,MAAM,IAAIE,KAAK,CAAC,oBAAoBL,OAAO,EAAE,CAAC;QAClD;QACA,MAAMG,UAAU,CAACG,MAAM,CAAC,CAAC;QACzBhD,MAAM,GAAG6C,UAAU;QACnB;MACJ,CAAC,MACI,IAAIvB,GAAG,CAACE,OAAO,CAACC,UAAU,CAAC,OAAO,CAAC,EAAE;QACtC,MAAM5G,sBAAsB,CAAC,IAAI,EAAEc,mBAAmB,EAAE,GAAG,CAAC,CAAC+F,UAAU,CAAC1B,MAAM,CAAC;QAC/EA,MAAM,GAAG,MAAMnF,sBAAsB,CAAC,IAAI,EAAEc,mBAAmB,EAAE,GAAG,CAAC,CAAC8G,SAAS,CAAC3C,QAAQ,EAAEC,UAAU,CAAC;QACrG;MACJ;MACA,MAAMuB,GAAG;IACb;EACJ;AACJ,CAAC;AACDtF,OAAO,CAAC+C,OAAO,GAAGvC,YAAY;AAC9B,CAAC,CAAC,EAAEJ,WAAW,CAAC6G,cAAc,EAAE;EAC5B9F,SAAS,EAAEX,YAAY;EACvB+D,QAAQ,EAAEtE,UAAU,CAAC8C,OAAO;EAC5ByD,QAAQ,EAAEhG,YAAY,CAACa,SAAS,CAACC;AACrC,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}