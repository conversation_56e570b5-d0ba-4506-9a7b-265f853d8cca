{"ast": null, "code": "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n/**\n * This file exports ops used by the converters executors. By default it\n * re-exports all ops. In a custom build this is aliased to a file that will\n * only exports ops for a given model.json.\n */\nexport * from './ops';", "map": {"version": 3, "names": [], "sources": ["C:\\tfjs-core\\src\\ops\\ops_for_converter.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\n/**\n * This file exports ops used by the converters executors. By default it\n * re-exports all ops. In a custom build this is aliased to a file that will\n * only exports ops for a given model.json.\n */\nexport * from './ops';\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAiBA;;;;;AAKA,cAAc,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}