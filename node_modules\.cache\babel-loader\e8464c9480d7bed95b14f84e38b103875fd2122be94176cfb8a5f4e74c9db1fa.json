{"ast": null, "code": "/**\n * @license\n * Copyright 2021 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { Transform } from '@tensorflow/tfjs-core';\nimport { TransformProgram } from '../transform_gpu';\nexport function transform(args) {\n  const {\n    inputs,\n    backend,\n    attrs\n  } = args;\n  const {\n    image,\n    transforms\n  } = inputs;\n  const {\n    interpolation,\n    fillMode,\n    fillValue,\n    outputShape\n  } = attrs;\n  const [batch, imageHeight, imageWidth, numChannels] = image.shape;\n  const [outHeight, outWidth] = outputShape != null ? outputShape : [imageHeight, imageWidth];\n  const outShape = [batch, outHeight, outWidth, numChannels];\n  const program = new TransformProgram(imageHeight, imageWidth, interpolation, fillMode, fillValue, outShape);\n  return backend.runWebGLProgram(program, [image, transforms], 'float32');\n}\nexport const transformConfig = {\n  kernelName: Transform,\n  backendName: 'webgl',\n  kernelFunc: transform\n};", "map": {"version": 3, "names": ["Transform", "TransformProgram", "transform", "args", "inputs", "backend", "attrs", "image", "transforms", "interpolation", "fillMode", "fillValue", "outputShape", "batch", "imageHeight", "imageWidth", "numChannels", "shape", "outHeight", "outWidth", "outShape", "program", "runWebGLProgram", "transformConfig", "kernelName", "backendName", "kernelFunc"], "sources": ["C:\\tfjs-backend-webgl\\src\\kernels\\Transform.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2021 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {KernelConfig, KernelFunc, TensorInfo, Transform, TransformAttrs, TransformInputs} from '@tensorflow/tfjs-core';\n\nimport {MathBackendWebGL} from '../backend_webgl';\nimport {TransformProgram} from '../transform_gpu';\n\nexport function transform(args: {\n  inputs: TransformInputs,\n  backend: MathBackendWebGL,\n  attrs: TransformAttrs\n}): TensorInfo {\n  const {inputs, backend, attrs} = args;\n  const {image, transforms} = inputs;\n  const {interpolation, fillMode, fillValue, outputShape} = attrs;\n\n  const [batch, imageHeight, imageWidth, numChannels] = image.shape;\n  const [outHeight, outWidth] =\n      outputShape != null ? outputShape : [imageHeight, imageWidth];\n  const outShape =\n      [batch, outHeight, outWidth,\n       numChannels] as [number, number, number, number];\n\n  const program = new TransformProgram(\n      imageHeight, imageWidth, interpolation, fillMode, fillValue, outShape);\n  return backend.runWebGLProgram(program, [image, transforms], 'float32');\n}\n\nexport const transformConfig: KernelConfig = {\n  kernelName: Transform,\n  backendName: 'webgl',\n  kernelFunc: transform as unknown as KernelFunc\n};\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAiBA,SAA8CA,SAAS,QAAwC,uBAAuB;AAGtH,SAAQC,gBAAgB,QAAO,kBAAkB;AAEjD,OAAM,SAAUC,SAASA,CAACC,IAIzB;EACC,MAAM;IAACC,MAAM;IAAEC,OAAO;IAAEC;EAAK,CAAC,GAAGH,IAAI;EACrC,MAAM;IAACI,KAAK;IAAEC;EAAU,CAAC,GAAGJ,MAAM;EAClC,MAAM;IAACK,aAAa;IAAEC,QAAQ;IAAEC,SAAS;IAAEC;EAAW,CAAC,GAAGN,KAAK;EAE/D,MAAM,CAACO,KAAK,EAAEC,WAAW,EAAEC,UAAU,EAAEC,WAAW,CAAC,GAAGT,KAAK,CAACU,KAAK;EACjE,MAAM,CAACC,SAAS,EAAEC,QAAQ,CAAC,GACvBP,WAAW,IAAI,IAAI,GAAGA,WAAW,GAAG,CAACE,WAAW,EAAEC,UAAU,CAAC;EACjE,MAAMK,QAAQ,GACV,CAACP,KAAK,EAAEK,SAAS,EAAEC,QAAQ,EAC1BH,WAAW,CAAqC;EAErD,MAAMK,OAAO,GAAG,IAAIpB,gBAAgB,CAChCa,WAAW,EAAEC,UAAU,EAAEN,aAAa,EAAEC,QAAQ,EAAEC,SAAS,EAAES,QAAQ,CAAC;EAC1E,OAAOf,OAAO,CAACiB,eAAe,CAACD,OAAO,EAAE,CAACd,KAAK,EAAEC,UAAU,CAAC,EAAE,SAAS,CAAC;AACzE;AAEA,OAAO,MAAMe,eAAe,GAAiB;EAC3CC,UAAU,EAAExB,SAAS;EACrByB,WAAW,EAAE,OAAO;EACpBC,UAAU,EAAExB;CACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}