{"ast": null, "code": "/**\n * @license\n * Copyright 2018 Google LLC\n *\n * Use of this source code is governed by an MIT-style\n * license that can be found in the LICENSE file or at\n * https://opensource.org/licenses/MIT.\n * =============================================================================\n */\nimport { eye, linalg, mul, ones, randomUniform, scalar, serialization, tidy, truncatedNormal, util, zeros } from '@tensorflow/tfjs-core';\nimport * as K from './backend/tfjs_backend';\nimport { checkDataFormat } from './common';\nimport { NotImplementedError, ValueError } from './errors';\nimport { VALID_DISTRIBUTION_VALUES, VALID_FAN_MODE_VALUES } from './keras_format/initializer_config';\nimport { checkStringTypeUnionValue, deserializeKerasObject, serializeKerasObject } from './utils/generic_utils';\nimport { arrayProd } from './utils/math_utils';\nexport function checkFanMode(value) {\n  checkStringTypeUnionValue(VALID_FAN_MODE_VALUES, 'FanMode', value);\n}\nexport function checkDistribution(value) {\n  checkStringTypeUnionValue(VALID_DISTRIBUTION_VALUES, 'Distribution', value);\n}\n/**\n * Initializer base class.\n *\n * @doc {\n *   heading: 'Initializers', subheading: 'Classes', namespace: 'initializers'}\n */\nexport class Initializer extends serialization.Serializable {\n  fromConfigUsesCustomObjects() {\n    return false;\n  }\n  getConfig() {\n    return {};\n  }\n}\nclass Zeros extends Initializer {\n  apply(shape, dtype) {\n    return zeros(shape, dtype);\n  }\n}\n/** @nocollapse */\nZeros.className = 'Zeros';\nexport { Zeros };\nserialization.registerClass(Zeros);\nclass Ones extends Initializer {\n  apply(shape, dtype) {\n    return ones(shape, dtype);\n  }\n}\n/** @nocollapse */\nOnes.className = 'Ones';\nexport { Ones };\nserialization.registerClass(Ones);\nclass Constant extends Initializer {\n  constructor(args) {\n    super();\n    if (typeof args !== 'object') {\n      throw new ValueError(`Expected argument of type ConstantConfig but got ${args}`);\n    }\n    if (args.value === undefined) {\n      throw new ValueError(`config must have value set but got ${args}`);\n    }\n    this.value = args.value;\n  }\n  apply(shape, dtype) {\n    return tidy(() => mul(scalar(this.value), ones(shape, dtype)));\n  }\n  getConfig() {\n    return {\n      value: this.value\n    };\n  }\n}\n/** @nocollapse */\nConstant.className = 'Constant';\nexport { Constant };\nserialization.registerClass(Constant);\nclass RandomUniform extends Initializer {\n  constructor(args) {\n    super();\n    this.DEFAULT_MINVAL = -0.05;\n    this.DEFAULT_MAXVAL = 0.05;\n    this.minval = args.minval || this.DEFAULT_MINVAL;\n    this.maxval = args.maxval || this.DEFAULT_MAXVAL;\n    this.seed = args.seed;\n  }\n  apply(shape, dtype) {\n    return randomUniform(shape, this.minval, this.maxval, dtype, this.seed);\n  }\n  getConfig() {\n    return {\n      minval: this.minval,\n      maxval: this.maxval,\n      seed: this.seed\n    };\n  }\n}\n/** @nocollapse */\nRandomUniform.className = 'RandomUniform';\nexport { RandomUniform };\nserialization.registerClass(RandomUniform);\nclass RandomNormal extends Initializer {\n  constructor(args) {\n    super();\n    this.DEFAULT_MEAN = 0.;\n    this.DEFAULT_STDDEV = 0.05;\n    this.mean = args.mean || this.DEFAULT_MEAN;\n    this.stddev = args.stddev || this.DEFAULT_STDDEV;\n    this.seed = args.seed;\n  }\n  apply(shape, dtype) {\n    dtype = dtype || 'float32';\n    if (dtype !== 'float32' && dtype !== 'int32') {\n      throw new NotImplementedError(`randomNormal does not support dType ${dtype}.`);\n    }\n    return K.randomNormal(shape, this.mean, this.stddev, dtype, this.seed);\n  }\n  getConfig() {\n    return {\n      mean: this.mean,\n      stddev: this.stddev,\n      seed: this.seed\n    };\n  }\n}\n/** @nocollapse */\nRandomNormal.className = 'RandomNormal';\nexport { RandomNormal };\nserialization.registerClass(RandomNormal);\nclass TruncatedNormal extends Initializer {\n  constructor(args) {\n    super();\n    this.DEFAULT_MEAN = 0.;\n    this.DEFAULT_STDDEV = 0.05;\n    this.mean = args.mean || this.DEFAULT_MEAN;\n    this.stddev = args.stddev || this.DEFAULT_STDDEV;\n    this.seed = args.seed;\n  }\n  apply(shape, dtype) {\n    dtype = dtype || 'float32';\n    if (dtype !== 'float32' && dtype !== 'int32') {\n      throw new NotImplementedError(`truncatedNormal does not support dType ${dtype}.`);\n    }\n    return truncatedNormal(shape, this.mean, this.stddev, dtype, this.seed);\n  }\n  getConfig() {\n    return {\n      mean: this.mean,\n      stddev: this.stddev,\n      seed: this.seed\n    };\n  }\n}\n/** @nocollapse */\nTruncatedNormal.className = 'TruncatedNormal';\nexport { TruncatedNormal };\nserialization.registerClass(TruncatedNormal);\nclass Identity extends Initializer {\n  constructor(args) {\n    super();\n    this.gain = args.gain != null ? args.gain : 1.0;\n  }\n  apply(shape, dtype) {\n    return tidy(() => {\n      if (shape.length !== 2 || shape[0] !== shape[1]) {\n        throw new ValueError('Identity matrix initializer can only be used for' + ' 2D square matrices.');\n      } else {\n        return mul(this.gain, eye(shape[0]));\n      }\n    });\n  }\n  getConfig() {\n    return {\n      gain: this.gain\n    };\n  }\n}\n/** @nocollapse */\nIdentity.className = 'Identity';\nexport { Identity };\nserialization.registerClass(Identity);\n/**\n * Computes the number of input and output units for a weight shape.\n * @param shape Shape of weight.\n * @param dataFormat data format to use for convolution kernels.\n *   Note that all kernels in Keras are standardized on the\n *   CHANNEL_LAST ordering (even when inputs are set to CHANNEL_FIRST).\n * @return An length-2 array: fanIn, fanOut.\n */\nfunction computeFans(shape, dataFormat = 'channelsLast') {\n  let fanIn;\n  let fanOut;\n  checkDataFormat(dataFormat);\n  if (shape.length === 2) {\n    fanIn = shape[0];\n    fanOut = shape[1];\n  } else if ([3, 4, 5].indexOf(shape.length) !== -1) {\n    if (dataFormat === 'channelsFirst') {\n      const receptiveFieldSize = arrayProd(shape, 2);\n      fanIn = shape[1] * receptiveFieldSize;\n      fanOut = shape[0] * receptiveFieldSize;\n    } else if (dataFormat === 'channelsLast') {\n      const receptiveFieldSize = arrayProd(shape, 0, shape.length - 2);\n      fanIn = shape[shape.length - 2] * receptiveFieldSize;\n      fanOut = shape[shape.length - 1] * receptiveFieldSize;\n    }\n  } else {\n    const shapeProd = arrayProd(shape);\n    fanIn = Math.sqrt(shapeProd);\n    fanOut = Math.sqrt(shapeProd);\n  }\n  return [fanIn, fanOut];\n}\nclass VarianceScaling extends Initializer {\n  /**\n   * Constructor of VarianceScaling.\n   * @throws ValueError for invalid value in scale.\n   */\n  constructor(args) {\n    super();\n    if (args.scale < 0.0) {\n      throw new ValueError(`scale must be a positive float. Got: ${args.scale}`);\n    }\n    this.scale = args.scale == null ? 1.0 : args.scale;\n    this.mode = args.mode == null ? 'fanIn' : args.mode;\n    checkFanMode(this.mode);\n    this.distribution = args.distribution == null ? 'normal' : args.distribution;\n    checkDistribution(this.distribution);\n    this.seed = args.seed;\n  }\n  apply(shape, dtype) {\n    const fans = computeFans(shape);\n    const fanIn = fans[0];\n    const fanOut = fans[1];\n    let scale = this.scale;\n    if (this.mode === 'fanIn') {\n      scale /= Math.max(1, fanIn);\n    } else if (this.mode === 'fanOut') {\n      scale /= Math.max(1, fanOut);\n    } else {\n      scale /= Math.max(1, (fanIn + fanOut) / 2);\n    }\n    if (this.distribution === 'normal') {\n      const stddev = Math.sqrt(scale);\n      dtype = dtype || 'float32';\n      if (dtype !== 'float32' && dtype !== 'int32') {\n        throw new NotImplementedError(`${this.getClassName()} does not support dType ${dtype}.`);\n      }\n      return truncatedNormal(shape, 0, stddev, dtype, this.seed);\n    } else {\n      const limit = Math.sqrt(3 * scale);\n      return randomUniform(shape, -limit, limit, dtype, this.seed);\n    }\n  }\n  getConfig() {\n    return {\n      scale: this.scale,\n      mode: this.mode,\n      distribution: this.distribution,\n      seed: this.seed\n    };\n  }\n}\n/** @nocollapse */\nVarianceScaling.className = 'VarianceScaling';\nexport { VarianceScaling };\nserialization.registerClass(VarianceScaling);\nclass GlorotUniform extends VarianceScaling {\n  /**\n   * Constructor of GlorotUniform\n   * @param scale\n   * @param mode\n   * @param distribution\n   * @param seed\n   */\n  constructor(args) {\n    super({\n      scale: 1.0,\n      mode: 'fanAvg',\n      distribution: 'uniform',\n      seed: args == null ? null : args.seed\n    });\n  }\n  getClassName() {\n    // In Python Keras, GlorotUniform is not a class, but a helper method\n    // that creates a VarianceScaling object. Use 'VarianceScaling' as\n    // class name to be compatible with that.\n    return VarianceScaling.className;\n  }\n}\n/** @nocollapse */\nGlorotUniform.className = 'GlorotUniform';\nexport { GlorotUniform };\nserialization.registerClass(GlorotUniform);\nclass GlorotNormal extends VarianceScaling {\n  /**\n   * Constructor of GlorotNormal.\n   * @param scale\n   * @param mode\n   * @param distribution\n   * @param seed\n   */\n  constructor(args) {\n    super({\n      scale: 1.0,\n      mode: 'fanAvg',\n      distribution: 'normal',\n      seed: args == null ? null : args.seed\n    });\n  }\n  getClassName() {\n    // In Python Keras, GlorotNormal is not a class, but a helper method\n    // that creates a VarianceScaling object. Use 'VarianceScaling' as\n    // class name to be compatible with that.\n    return VarianceScaling.className;\n  }\n}\n/** @nocollapse */\nGlorotNormal.className = 'GlorotNormal';\nexport { GlorotNormal };\nserialization.registerClass(GlorotNormal);\nclass HeNormal extends VarianceScaling {\n  constructor(args) {\n    super({\n      scale: 2.0,\n      mode: 'fanIn',\n      distribution: 'normal',\n      seed: args == null ? null : args.seed\n    });\n  }\n  getClassName() {\n    // In Python Keras, HeNormal is not a class, but a helper method\n    // that creates a VarianceScaling object. Use 'VarianceScaling' as\n    // class name to be compatible with that.\n    return VarianceScaling.className;\n  }\n}\n/** @nocollapse */\nHeNormal.className = 'HeNormal';\nexport { HeNormal };\nserialization.registerClass(HeNormal);\nclass HeUniform extends VarianceScaling {\n  constructor(args) {\n    super({\n      scale: 2.0,\n      mode: 'fanIn',\n      distribution: 'uniform',\n      seed: args == null ? null : args.seed\n    });\n  }\n  getClassName() {\n    // In Python Keras, HeUniform is not a class, but a helper method\n    // that creates a VarianceScaling object. Use 'VarianceScaling' as\n    // class name to be compatible with that.\n    return VarianceScaling.className;\n  }\n}\n/** @nocollapse */\nHeUniform.className = 'HeUniform';\nexport { HeUniform };\nserialization.registerClass(HeUniform);\nclass LeCunNormal extends VarianceScaling {\n  constructor(args) {\n    super({\n      scale: 1.0,\n      mode: 'fanIn',\n      distribution: 'normal',\n      seed: args == null ? null : args.seed\n    });\n  }\n  getClassName() {\n    // In Python Keras, LeCunNormal is not a class, but a helper method\n    // that creates a VarianceScaling object. Use 'VarianceScaling' as\n    // class name to be compatible with that.\n    return VarianceScaling.className;\n  }\n}\n/** @nocollapse */\nLeCunNormal.className = 'LeCunNormal';\nexport { LeCunNormal };\nserialization.registerClass(LeCunNormal);\nclass LeCunUniform extends VarianceScaling {\n  constructor(args) {\n    super({\n      scale: 1.0,\n      mode: 'fanIn',\n      distribution: 'uniform',\n      seed: args == null ? null : args.seed\n    });\n  }\n  getClassName() {\n    // In Python Keras, LeCunUniform is not a class, but a helper method\n    // that creates a VarianceScaling object. Use 'VarianceScaling' as\n    // class name to be compatible with that.\n    return VarianceScaling.className;\n  }\n}\n/** @nocollapse */\nLeCunUniform.className = 'LeCunUniform';\nexport { LeCunUniform };\nserialization.registerClass(LeCunUniform);\nclass Orthogonal extends Initializer {\n  constructor(args) {\n    super();\n    this.DEFAULT_GAIN = 1;\n    this.ELEMENTS_WARN_SLOW = 2000;\n    this.gain = args.gain == null ? this.DEFAULT_GAIN : args.gain;\n    this.seed = args.seed;\n  }\n  apply(shape, dtype) {\n    return tidy(() => {\n      if (shape.length < 2) {\n        throw new NotImplementedError('Shape must be at least 2D.');\n      }\n      if (dtype !== 'int32' && dtype !== 'float32' && dtype !== undefined) {\n        throw new TypeError(`Unsupported data type ${dtype}.`);\n      }\n      dtype = dtype;\n      // flatten the input shape with the last dimension remaining its\n      // original shape so it works for conv2d\n      const numRows = util.sizeFromShape(shape.slice(0, -1));\n      const numCols = shape[shape.length - 1];\n      const numElements = numRows * numCols;\n      if (numElements > this.ELEMENTS_WARN_SLOW) {\n        console.warn(`Orthogonal initializer is being called on a matrix with more ` + `than ${this.ELEMENTS_WARN_SLOW} (${numElements}) elements: ` + `Slowness may result.`);\n      }\n      const flatShape = [Math.max(numCols, numRows), Math.min(numCols, numRows)];\n      // Generate a random matrix\n      const randNormalMat = K.randomNormal(flatShape, 0, 1, dtype, this.seed);\n      // Compute QR factorization\n      const qr = linalg.qr(randNormalMat, false);\n      let qMat = qr[0];\n      const rMat = qr[1];\n      // Make Q uniform\n      const diag = rMat.flatten().stridedSlice([0], [Math.min(numCols, numRows) * Math.min(numCols, numRows)], [Math.min(numCols, numRows) + 1]);\n      qMat = mul(qMat, diag.sign());\n      if (numRows < numCols) {\n        qMat = qMat.transpose();\n      }\n      return mul(scalar(this.gain), qMat.reshape(shape));\n    });\n  }\n  getConfig() {\n    return {\n      gain: this.gain,\n      seed: this.seed\n    };\n  }\n}\n/** @nocollapse */\nOrthogonal.className = 'Orthogonal';\nexport { Orthogonal };\nserialization.registerClass(Orthogonal);\n// Maps the JavaScript-like identifier keys to the corresponding registry\n// symbols.\nexport const INITIALIZER_IDENTIFIER_REGISTRY_SYMBOL_MAP = {\n  'constant': 'Constant',\n  'glorotNormal': 'GlorotNormal',\n  'glorotUniform': 'GlorotUniform',\n  'heNormal': 'HeNormal',\n  'heUniform': 'HeUniform',\n  'identity': 'Identity',\n  'leCunNormal': 'LeCunNormal',\n  'leCunUniform': 'LeCunUniform',\n  'ones': 'Ones',\n  'orthogonal': 'Orthogonal',\n  'randomNormal': 'RandomNormal',\n  'randomUniform': 'RandomUniform',\n  'truncatedNormal': 'TruncatedNormal',\n  'varianceScaling': 'VarianceScaling',\n  'zeros': 'Zeros'\n};\nfunction deserializeInitializer(config, customObjects = {}) {\n  return deserializeKerasObject(config, serialization.SerializationMap.getMap().classNameMap, customObjects, 'initializer');\n}\nexport function serializeInitializer(initializer) {\n  return serializeKerasObject(initializer);\n}\nexport function getInitializer(identifier) {\n  if (typeof identifier === 'string') {\n    const className = identifier in INITIALIZER_IDENTIFIER_REGISTRY_SYMBOL_MAP ? INITIALIZER_IDENTIFIER_REGISTRY_SYMBOL_MAP[identifier] : identifier;\n    /* We have four 'helper' classes for common initializers that\n    all get serialized as 'VarianceScaling' and shouldn't go through\n    the deserializeInitializer pathway. */\n    if (className === 'GlorotNormal') {\n      return new GlorotNormal();\n    } else if (className === 'GlorotUniform') {\n      return new GlorotUniform();\n    } else if (className === 'HeNormal') {\n      return new HeNormal();\n    } else if (className === 'HeUniform') {\n      return new HeUniform();\n    } else if (className === 'LeCunNormal') {\n      return new LeCunNormal();\n    } else if (className === 'LeCunUniform') {\n      return new LeCunUniform();\n    } else {\n      const config = {};\n      config['className'] = className;\n      config['config'] = {};\n      return deserializeInitializer(config);\n    }\n  } else if (identifier instanceof Initializer) {\n    return identifier;\n  } else {\n    return deserializeInitializer(identifier);\n  }\n}", "map": {"version": 3, "names": ["eye", "l<PERSON>g", "mul", "ones", "randomUniform", "scalar", "serialization", "tidy", "truncatedNormal", "util", "zeros", "K", "checkDataFormat", "NotImplementedError", "ValueError", "VALID_DISTRIBUTION_VALUES", "VALID_FAN_MODE_VALUES", "checkStringTypeUnionValue", "deserializeKerasObject", "serializeKerasObject", "arrayProd", "checkFanMode", "value", "checkDistribution", "Initializer", "Serializable", "fromConfigUsesCustomObjects", "getConfig", "Zeros", "apply", "shape", "dtype", "className", "registerClass", "Ones", "Constant", "constructor", "args", "undefined", "RandomUniform", "DEFAULT_MINVAL", "DEFAULT_MAXVAL", "min<PERSON>", "maxval", "seed", "RandomNormal", "DEFAULT_MEAN", "DEFAULT_STDDEV", "mean", "stddev", "randomNormal", "TruncatedNormal", "Identity", "gain", "length", "computeFans", "dataFormat", "fanIn", "fanOut", "indexOf", "receptiveFieldSize", "shapeProd", "Math", "sqrt", "VarianceScaling", "scale", "mode", "distribution", "fans", "max", "getClassName", "limit", "GlorotUniform", "GlorotNormal", "<PERSON><PERSON><PERSON><PERSON>", "HeUniform", "LeCunNormal", "LeCunUniform", "Orthogonal", "DEFAULT_GAIN", "ELEMENTS_WARN_SLOW", "TypeError", "numRows", "sizeFromShape", "slice", "numCols", "numElements", "console", "warn", "flatShape", "min", "randNormalMat", "qr", "qMat", "rMat", "diag", "flatten", "stridedSlice", "sign", "transpose", "reshape", "INITIALIZER_IDENTIFIER_REGISTRY_SYMBOL_MAP", "deserializeInitializer", "config", "customObjects", "SerializationMap", "getMap", "classNameMap", "serializeInitializer", "initializer", "getInitializer", "identifier"], "sources": ["C:\\tfjs-layers\\src\\initializers.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2018 Google LLC\n *\n * Use of this source code is governed by an MIT-style\n * license that can be found in the LICENSE file or at\n * https://opensource.org/licenses/MIT.\n * =============================================================================\n */\n\nimport {DataType, eye, linalg, mul, ones, randomUniform, scalar, serialization, Tensor, tidy, truncatedNormal, util, zeros} from '@tensorflow/tfjs-core';\n\nimport * as K from './backend/tfjs_backend';\nimport {checkDataFormat} from './common';\nimport {NotImplementedError, ValueError} from './errors';\nimport {DataFormat, Shape} from './keras_format/common';\nimport {Distribution, FanMode, VALID_DISTRIBUTION_VALUES, VALID_FAN_MODE_VALUES} from './keras_format/initializer_config';\nimport {checkStringTypeUnionValue, deserializeKerasObject, serializeKerasObject} from './utils/generic_utils';\nimport {arrayProd} from './utils/math_utils';\n\nexport function checkFanMode(value?: string): void {\n  checkStringTypeUnionValue(VALID_FAN_MODE_VALUES, 'FanMode', value);\n}\n\nexport function checkDistribution(value?: string): void {\n  checkStringTypeUnionValue(VALID_DISTRIBUTION_VALUES, 'Distribution', value);\n}\n\n/**\n * Initializer base class.\n *\n * @doc {\n *   heading: 'Initializers', subheading: 'Classes', namespace: 'initializers'}\n */\nexport abstract class Initializer extends serialization.Serializable {\n  public fromConfigUsesCustomObjects(): boolean {\n    return false;\n  }\n  /**\n   * Generate an initial value.\n   * @param shape\n   * @param dtype\n   * @return The init value.\n   */\n  abstract apply(shape: Shape, dtype?: DataType): Tensor;\n\n  getConfig(): serialization.ConfigDict {\n    return {};\n  }\n}\n\nexport class Zeros extends Initializer {\n  /** @nocollapse */\n  static className = 'Zeros';\n\n  apply(shape: Shape, dtype?: DataType): Tensor {\n    return zeros(shape, dtype);\n  }\n}\nserialization.registerClass(Zeros);\n\nexport class Ones extends Initializer {\n  /** @nocollapse */\n  static className = 'Ones';\n\n  apply(shape: Shape, dtype?: DataType): Tensor {\n    return ones(shape, dtype);\n  }\n}\nserialization.registerClass(Ones);\n\nexport interface ConstantArgs {\n  /** The value for each element in the variable. */\n  value: number;\n}\n\nexport class Constant extends Initializer {\n  /** @nocollapse */\n  static className = 'Constant';\n  private value: number;\n  constructor(args: ConstantArgs) {\n    super();\n    if (typeof args !== 'object') {\n      throw new ValueError(\n          `Expected argument of type ConstantConfig but got ${args}`);\n    }\n    if (args.value === undefined) {\n      throw new ValueError(`config must have value set but got ${args}`);\n    }\n    this.value = args.value;\n  }\n\n  apply(shape: Shape, dtype?: DataType): Tensor {\n    return tidy(() => mul(scalar(this.value), ones(shape, dtype)));\n  }\n\n  override getConfig(): serialization.ConfigDict {\n    return {\n      value: this.value,\n    };\n  }\n}\nserialization.registerClass(Constant);\n\nexport interface RandomUniformArgs {\n  /** Lower bound of the range of random values to generate. */\n  minval?: number;\n  /** Upper bound of the range of random values to generate. */\n  maxval?: number;\n  /** Used to seed the random generator. */\n  seed?: number;\n}\n\nexport class RandomUniform extends Initializer {\n  /** @nocollapse */\n  static className = 'RandomUniform';\n  readonly DEFAULT_MINVAL = -0.05;\n  readonly DEFAULT_MAXVAL = 0.05;\n  private minval: number;\n  private maxval: number;\n  private seed: number;\n\n  constructor(args: RandomUniformArgs) {\n    super();\n    this.minval = args.minval || this.DEFAULT_MINVAL;\n    this.maxval = args.maxval || this.DEFAULT_MAXVAL;\n    this.seed = args.seed;\n  }\n\n  apply(shape: Shape, dtype?: DataType): Tensor {\n    return randomUniform(shape, this.minval, this.maxval, dtype, this.seed);\n  }\n\n  override getConfig(): serialization.ConfigDict {\n    return {minval: this.minval, maxval: this.maxval, seed: this.seed};\n  }\n}\nserialization.registerClass(RandomUniform);\n\nexport interface RandomNormalArgs {\n  /** Mean of the random values to generate. */\n  mean?: number;\n  /** Standard deviation of the random values to generate. */\n  stddev?: number;\n  /** Used to seed the random generator. */\n  seed?: number;\n}\n\nexport class RandomNormal extends Initializer {\n  /** @nocollapse */\n  static className = 'RandomNormal';\n  readonly DEFAULT_MEAN = 0.;\n  readonly DEFAULT_STDDEV = 0.05;\n  private mean: number;\n  private stddev: number;\n  private seed: number;\n\n  constructor(args: RandomNormalArgs) {\n    super();\n    this.mean = args.mean || this.DEFAULT_MEAN;\n    this.stddev = args.stddev || this.DEFAULT_STDDEV;\n    this.seed = args.seed;\n  }\n\n  apply(shape: Shape, dtype?: DataType): Tensor {\n    dtype = dtype || 'float32';\n    if (dtype !== 'float32' && dtype !== 'int32') {\n      throw new NotImplementedError(\n          `randomNormal does not support dType ${dtype}.`);\n    }\n\n    return K.randomNormal(shape, this.mean, this.stddev, dtype, this.seed);\n  }\n\n  override getConfig(): serialization.ConfigDict {\n    return {mean: this.mean, stddev: this.stddev, seed: this.seed};\n  }\n}\nserialization.registerClass(RandomNormal);\n\nexport interface TruncatedNormalArgs {\n  /** Mean of the random values to generate. */\n  mean?: number;\n  /** Standard deviation of the random values to generate. */\n  stddev?: number;\n  /** Used to seed the random generator. */\n  seed?: number;\n}\n\nexport class TruncatedNormal extends Initializer {\n  /** @nocollapse */\n  static className = 'TruncatedNormal';\n\n  readonly DEFAULT_MEAN = 0.;\n  readonly DEFAULT_STDDEV = 0.05;\n  private mean: number;\n  private stddev: number;\n  private seed: number;\n\n  constructor(args: TruncatedNormalArgs) {\n    super();\n    this.mean = args.mean || this.DEFAULT_MEAN;\n    this.stddev = args.stddev || this.DEFAULT_STDDEV;\n    this.seed = args.seed;\n  }\n\n  apply(shape: Shape, dtype?: DataType): Tensor {\n    dtype = dtype || 'float32';\n    if (dtype !== 'float32' && dtype !== 'int32') {\n      throw new NotImplementedError(\n          `truncatedNormal does not support dType ${dtype}.`);\n    }\n    return truncatedNormal(shape, this.mean, this.stddev, dtype, this.seed);\n  }\n\n  override getConfig(): serialization.ConfigDict {\n    return {mean: this.mean, stddev: this.stddev, seed: this.seed};\n  }\n}\nserialization.registerClass(TruncatedNormal);\n\nexport interface IdentityArgs {\n  /**\n   * Multiplicative factor to apply to the identity matrix.\n   */\n  gain?: number;\n}\n\nexport class Identity extends Initializer {\n  /** @nocollapse */\n  static className = 'Identity';\n  private gain: number;\n  constructor(args: IdentityArgs) {\n    super();\n    this.gain = args.gain != null ? args.gain : 1.0;\n  }\n\n  apply(shape: Shape, dtype?: DataType): Tensor {\n    return tidy(() => {\n      if (shape.length !== 2 || shape[0] !== shape[1]) {\n        throw new ValueError(\n            'Identity matrix initializer can only be used for' +\n            ' 2D square matrices.');\n      } else {\n        return mul(this.gain, eye(shape[0]));\n      }\n    });\n  }\n\n  override getConfig(): serialization.ConfigDict {\n    return {gain: this.gain};\n  }\n}\nserialization.registerClass(Identity);\n\n/**\n * Computes the number of input and output units for a weight shape.\n * @param shape Shape of weight.\n * @param dataFormat data format to use for convolution kernels.\n *   Note that all kernels in Keras are standardized on the\n *   CHANNEL_LAST ordering (even when inputs are set to CHANNEL_FIRST).\n * @return An length-2 array: fanIn, fanOut.\n */\nfunction computeFans(\n    shape: Shape, dataFormat: DataFormat = 'channelsLast'): number[] {\n  let fanIn: number;\n  let fanOut: number;\n  checkDataFormat(dataFormat);\n  if (shape.length === 2) {\n    fanIn = shape[0];\n    fanOut = shape[1];\n  } else if ([3, 4, 5].indexOf(shape.length) !== -1) {\n    if (dataFormat === 'channelsFirst') {\n      const receptiveFieldSize = arrayProd(shape, 2);\n      fanIn = shape[1] * receptiveFieldSize;\n      fanOut = shape[0] * receptiveFieldSize;\n    } else if (dataFormat === 'channelsLast') {\n      const receptiveFieldSize = arrayProd(shape, 0, shape.length - 2);\n      fanIn = shape[shape.length - 2] * receptiveFieldSize;\n      fanOut = shape[shape.length - 1] * receptiveFieldSize;\n    }\n  } else {\n    const shapeProd = arrayProd(shape);\n    fanIn = Math.sqrt(shapeProd);\n    fanOut = Math.sqrt(shapeProd);\n  }\n\n  return [fanIn, fanOut];\n}\n\nexport interface VarianceScalingArgs {\n  /** Scaling factor (positive float). */\n  scale?: number;\n\n  /** Fanning mode for inputs and outputs. */\n  mode?: FanMode;\n\n  /** Probabilistic distribution of the values. */\n  distribution?: Distribution;\n\n  /** Random number generator seed. */\n  seed?: number;\n}\n\nexport class VarianceScaling extends Initializer {\n  /** @nocollapse */\n  static className = 'VarianceScaling';\n  private scale: number;\n  private mode: FanMode;\n  private distribution: Distribution;\n  private seed: number;\n\n  /**\n   * Constructor of VarianceScaling.\n   * @throws ValueError for invalid value in scale.\n   */\n  constructor(args: VarianceScalingArgs) {\n    super();\n    if (args.scale < 0.0) {\n      throw new ValueError(\n          `scale must be a positive float. Got: ${args.scale}`);\n    }\n    this.scale = args.scale == null ? 1.0 : args.scale;\n    this.mode = args.mode == null ? 'fanIn' : args.mode;\n    checkFanMode(this.mode);\n    this.distribution =\n        args.distribution == null ? 'normal' : args.distribution;\n    checkDistribution(this.distribution);\n    this.seed = args.seed;\n  }\n\n  apply(shape: Shape, dtype?: DataType): Tensor {\n    const fans = computeFans(shape);\n    const fanIn = fans[0];\n    const fanOut = fans[1];\n    let scale = this.scale;\n    if (this.mode === 'fanIn') {\n      scale /= Math.max(1, fanIn);\n    } else if (this.mode === 'fanOut') {\n      scale /= Math.max(1, fanOut);\n    } else {\n      scale /= Math.max(1, (fanIn + fanOut) / 2);\n    }\n\n    if (this.distribution === 'normal') {\n      const stddev = Math.sqrt(scale);\n      dtype = dtype || 'float32';\n      if (dtype !== 'float32' && dtype !== 'int32') {\n        throw new NotImplementedError(\n            `${this.getClassName()} does not support dType ${dtype}.`);\n      }\n      return truncatedNormal(shape, 0, stddev, dtype, this.seed);\n    } else {\n      const limit = Math.sqrt(3 * scale);\n      return randomUniform(shape, -limit, limit, dtype, this.seed);\n    }\n  }\n\n  override getConfig(): serialization.ConfigDict {\n    return {\n      scale: this.scale,\n      mode: this.mode,\n      distribution: this.distribution,\n      seed: this.seed\n    };\n  }\n}\nserialization.registerClass(VarianceScaling);\n\nexport interface SeedOnlyInitializerArgs {\n  /** Random number generator seed. */\n  seed?: number;\n}\n\nexport class GlorotUniform extends VarianceScaling {\n  /** @nocollapse */\n  static override className = 'GlorotUniform';\n\n  /**\n   * Constructor of GlorotUniform\n   * @param scale\n   * @param mode\n   * @param distribution\n   * @param seed\n   */\n  constructor(args?: SeedOnlyInitializerArgs) {\n    super({\n      scale: 1.0,\n      mode: 'fanAvg',\n      distribution: 'uniform',\n      seed: args == null ? null : args.seed\n    });\n  }\n\n  override getClassName(): string {\n    // In Python Keras, GlorotUniform is not a class, but a helper method\n    // that creates a VarianceScaling object. Use 'VarianceScaling' as\n    // class name to be compatible with that.\n    return VarianceScaling.className;\n  }\n}\nserialization.registerClass(GlorotUniform);\n\nexport class GlorotNormal extends VarianceScaling {\n  /** @nocollapse */\n  static override className = 'GlorotNormal';\n\n  /**\n   * Constructor of GlorotNormal.\n   * @param scale\n   * @param mode\n   * @param distribution\n   * @param seed\n   */\n  constructor(args?: SeedOnlyInitializerArgs) {\n    super({\n      scale: 1.0,\n      mode: 'fanAvg',\n      distribution: 'normal',\n      seed: args == null ? null : args.seed\n    });\n  }\n\n  override getClassName(): string {\n    // In Python Keras, GlorotNormal is not a class, but a helper method\n    // that creates a VarianceScaling object. Use 'VarianceScaling' as\n    // class name to be compatible with that.\n    return VarianceScaling.className;\n  }\n}\nserialization.registerClass(GlorotNormal);\n\nexport class HeNormal extends VarianceScaling {\n  /** @nocollapse */\n  static override className = 'HeNormal';\n\n  constructor(args?: SeedOnlyInitializerArgs) {\n    super({\n      scale: 2.0,\n      mode: 'fanIn',\n      distribution: 'normal',\n      seed: args == null ? null : args.seed\n    });\n  }\n\n  override getClassName(): string {\n    // In Python Keras, HeNormal is not a class, but a helper method\n    // that creates a VarianceScaling object. Use 'VarianceScaling' as\n    // class name to be compatible with that.\n    return VarianceScaling.className;\n  }\n}\nserialization.registerClass(HeNormal);\n\nexport class HeUniform extends VarianceScaling {\n  /** @nocollapse */\n  static override className = 'HeUniform';\n\n  constructor(args?: SeedOnlyInitializerArgs) {\n    super({\n      scale: 2.0,\n      mode: 'fanIn',\n      distribution: 'uniform',\n      seed: args == null ? null : args.seed\n    });\n  }\n\n  override getClassName(): string {\n    // In Python Keras, HeUniform is not a class, but a helper method\n    // that creates a VarianceScaling object. Use 'VarianceScaling' as\n    // class name to be compatible with that.\n    return VarianceScaling.className;\n  }\n}\nserialization.registerClass(HeUniform);\n\nexport class LeCunNormal extends VarianceScaling {\n  /** @nocollapse */\n  static override className = 'LeCunNormal';\n\n  constructor(args?: SeedOnlyInitializerArgs) {\n    super({\n      scale: 1.0,\n      mode: 'fanIn',\n      distribution: 'normal',\n      seed: args == null ? null : args.seed\n    });\n  }\n\n  override getClassName(): string {\n    // In Python Keras, LeCunNormal is not a class, but a helper method\n    // that creates a VarianceScaling object. Use 'VarianceScaling' as\n    // class name to be compatible with that.\n    return VarianceScaling.className;\n  }\n}\nserialization.registerClass(LeCunNormal);\n\nexport class LeCunUniform extends VarianceScaling {\n  /** @nocollapse */\n  static override className = 'LeCunUniform';\n\n  constructor(args?: SeedOnlyInitializerArgs) {\n    super({\n      scale: 1.0,\n      mode: 'fanIn',\n      distribution: 'uniform',\n      seed: args == null ? null : args.seed\n    });\n  }\n\n  override getClassName(): string {\n    // In Python Keras, LeCunUniform is not a class, but a helper method\n    // that creates a VarianceScaling object. Use 'VarianceScaling' as\n    // class name to be compatible with that.\n    return VarianceScaling.className;\n  }\n}\nserialization.registerClass(LeCunUniform);\n\nexport interface OrthogonalArgs extends SeedOnlyInitializerArgs {\n  /**\n   * Multiplicative factor to apply to the orthogonal matrix. Defaults to 1.\n   */\n  gain?: number;\n}\n\nexport class Orthogonal extends Initializer {\n  /** @nocollapse */\n  static className = 'Orthogonal';\n  readonly DEFAULT_GAIN = 1;\n  readonly ELEMENTS_WARN_SLOW = 2000;\n  protected readonly gain: number;\n  protected readonly seed: number;\n\n  constructor(args?: OrthogonalArgs) {\n    super();\n    this.gain = args.gain == null ? this.DEFAULT_GAIN : args.gain;\n    this.seed = args.seed;\n  }\n\n  apply(shape: Shape, dtype?: DataType): Tensor {\n    return tidy(() => {\n      if (shape.length < 2) {\n        throw new NotImplementedError('Shape must be at least 2D.');\n      }\n      if (dtype !== 'int32' && dtype !== 'float32' && dtype !== undefined) {\n        throw new TypeError(`Unsupported data type ${dtype}.`);\n      }\n      dtype = dtype as 'int32' | 'float32' | undefined;\n\n      // flatten the input shape with the last dimension remaining its\n      // original shape so it works for conv2d\n      const numRows = util.sizeFromShape(shape.slice(0, -1));\n      const numCols = shape[shape.length - 1];\n      const numElements = numRows * numCols;\n      if (numElements > this.ELEMENTS_WARN_SLOW) {\n        console.warn(\n            `Orthogonal initializer is being called on a matrix with more ` +\n            `than ${this.ELEMENTS_WARN_SLOW} (${numElements}) elements: ` +\n            `Slowness may result.`);\n      }\n      const flatShape =\n          [Math.max(numCols, numRows), Math.min(numCols, numRows)];\n\n      // Generate a random matrix\n      const randNormalMat = K.randomNormal(flatShape, 0, 1, dtype, this.seed);\n\n      // Compute QR factorization\n      const qr = linalg.qr(randNormalMat, false);\n      let qMat = qr[0];\n      const rMat = qr[1];\n\n      // Make Q uniform\n      const diag = rMat.flatten().stridedSlice(\n          [0], [Math.min(numCols, numRows) * Math.min(numCols, numRows)],\n          [Math.min(numCols, numRows) + 1]);\n      qMat = mul(qMat, diag.sign());\n      if (numRows < numCols) {\n        qMat = qMat.transpose();\n      }\n\n      return mul(scalar(this.gain), qMat.reshape(shape));\n    });\n  }\n\n  override getConfig(): serialization.ConfigDict {\n    return {\n      gain: this.gain,\n      seed: this.seed,\n    };\n  }\n}\nserialization.registerClass(Orthogonal);\n\n/** @docinline */\nexport type InitializerIdentifier =\n    'constant'|'glorotNormal'|'glorotUniform'|'heNormal'|'heUniform'|'identity'|\n    'leCunNormal'|'leCunUniform'|'ones'|'orthogonal'|'randomNormal'|\n    'randomUniform'|'truncatedNormal'|'varianceScaling'|'zeros'|string;\n\n// Maps the JavaScript-like identifier keys to the corresponding registry\n// symbols.\nexport const INITIALIZER_IDENTIFIER_REGISTRY_SYMBOL_MAP:\n    {[identifier in InitializerIdentifier]: string} = {\n      'constant': 'Constant',\n      'glorotNormal': 'GlorotNormal',\n      'glorotUniform': 'GlorotUniform',\n      'heNormal': 'HeNormal',\n      'heUniform': 'HeUniform',\n      'identity': 'Identity',\n      'leCunNormal': 'LeCunNormal',\n      'leCunUniform': 'LeCunUniform',\n      'ones': 'Ones',\n      'orthogonal': 'Orthogonal',\n      'randomNormal': 'RandomNormal',\n      'randomUniform': 'RandomUniform',\n      'truncatedNormal': 'TruncatedNormal',\n      'varianceScaling': 'VarianceScaling',\n      'zeros': 'Zeros'\n    };\n\nfunction deserializeInitializer(\n    config: serialization.ConfigDict,\n    customObjects: serialization.ConfigDict = {}): Initializer {\n  return deserializeKerasObject(\n      config, serialization.SerializationMap.getMap().classNameMap,\n      customObjects, 'initializer');\n}\n\nexport function serializeInitializer(initializer: Initializer):\n    serialization.ConfigDictValue {\n  return serializeKerasObject(initializer);\n}\n\nexport function getInitializer(identifier: InitializerIdentifier|Initializer|\n                               serialization.ConfigDict): Initializer {\n  if (typeof identifier === 'string') {\n    const className = identifier in INITIALIZER_IDENTIFIER_REGISTRY_SYMBOL_MAP ?\n        INITIALIZER_IDENTIFIER_REGISTRY_SYMBOL_MAP[identifier] :\n        identifier;\n    /* We have four 'helper' classes for common initializers that\n    all get serialized as 'VarianceScaling' and shouldn't go through\n    the deserializeInitializer pathway. */\n    if (className === 'GlorotNormal') {\n      return new GlorotNormal();\n    } else if (className === 'GlorotUniform') {\n      return new GlorotUniform();\n    } else if (className === 'HeNormal') {\n      return new HeNormal();\n    } else if (className === 'HeUniform') {\n      return new HeUniform();\n    } else if (className === 'LeCunNormal') {\n      return new LeCunNormal();\n    } else if (className === 'LeCunUniform') {\n      return new LeCunUniform();\n    } else {\n      const config: serialization.ConfigDict = {};\n      config['className'] = className;\n      config['config'] = {};\n      return deserializeInitializer(config);\n    }\n  } else if (identifier instanceof Initializer) {\n    return identifier;\n  } else {\n    return deserializeInitializer(identifier);\n  }\n}\n"], "mappings": "AAAA;;;;;;;;;AAUA,SAAkBA,GAAG,EAAEC,MAAM,EAAEC,GAAG,EAAEC,IAAI,EAAEC,aAAa,EAAEC,MAAM,EAAEC,aAAa,EAAUC,IAAI,EAAEC,eAAe,EAAEC,IAAI,EAAEC,KAAK,QAAO,uBAAuB;AAExJ,OAAO,KAAKC,CAAC,MAAM,wBAAwB;AAC3C,SAAQC,eAAe,QAAO,UAAU;AACxC,SAAQC,mBAAmB,EAAEC,UAAU,QAAO,UAAU;AAExD,SAA+BC,yBAAyB,EAAEC,qBAAqB,QAAO,mCAAmC;AACzH,SAAQC,yBAAyB,EAAEC,sBAAsB,EAAEC,oBAAoB,QAAO,uBAAuB;AAC7G,SAAQC,SAAS,QAAO,oBAAoB;AAE5C,OAAM,SAAUC,YAAYA,CAACC,KAAc;EACzCL,yBAAyB,CAACD,qBAAqB,EAAE,SAAS,EAAEM,KAAK,CAAC;AACpE;AAEA,OAAM,SAAUC,iBAAiBA,CAACD,KAAc;EAC9CL,yBAAyB,CAACF,yBAAyB,EAAE,cAAc,EAAEO,KAAK,CAAC;AAC7E;AAEA;;;;;;AAMA,OAAM,MAAgBE,WAAY,SAAQlB,aAAa,CAACmB,YAAY;EAC3DC,2BAA2BA,CAAA;IAChC,OAAO,KAAK;EACd;EASAC,SAASA,CAAA;IACP,OAAO,EAAE;EACX;;AAGF,MAAaC,KAAM,SAAQJ,WAAW;EAIpCK,KAAKA,CAACC,KAAY,EAAEC,KAAgB;IAClC,OAAOrB,KAAK,CAACoB,KAAK,EAAEC,KAAK,CAAC;EAC5B;;AALA;AACOH,KAAA,CAAAI,SAAS,GAAG,OAAO;SAFfJ,KAAK;AAQlBtB,aAAa,CAAC2B,aAAa,CAACL,KAAK,CAAC;AAElC,MAAaM,IAAK,SAAQV,WAAW;EAInCK,KAAKA,CAACC,KAAY,EAAEC,KAAgB;IAClC,OAAO5B,IAAI,CAAC2B,KAAK,EAAEC,KAAK,CAAC;EAC3B;;AALA;AACOG,IAAA,CAAAF,SAAS,GAAG,MAAM;SAFdE,IAAI;AAQjB5B,aAAa,CAAC2B,aAAa,CAACC,IAAI,CAAC;AAOjC,MAAaC,QAAS,SAAQX,WAAW;EAIvCY,YAAYC,IAAkB;IAC5B,KAAK,EAAE;IACP,IAAI,OAAOA,IAAI,KAAK,QAAQ,EAAE;MAC5B,MAAM,IAAIvB,UAAU,CAChB,oDAAoDuB,IAAI,EAAE,CAAC;;IAEjE,IAAIA,IAAI,CAACf,KAAK,KAAKgB,SAAS,EAAE;MAC5B,MAAM,IAAIxB,UAAU,CAAC,sCAAsCuB,IAAI,EAAE,CAAC;;IAEpE,IAAI,CAACf,KAAK,GAAGe,IAAI,CAACf,KAAK;EACzB;EAEAO,KAAKA,CAACC,KAAY,EAAEC,KAAgB;IAClC,OAAOxB,IAAI,CAAC,MAAML,GAAG,CAACG,MAAM,CAAC,IAAI,CAACiB,KAAK,CAAC,EAAEnB,IAAI,CAAC2B,KAAK,EAAEC,KAAK,CAAC,CAAC,CAAC;EAChE;EAESJ,SAASA,CAAA;IAChB,OAAO;MACLL,KAAK,EAAE,IAAI,CAACA;KACb;EACH;;AAvBA;AACOa,QAAA,CAAAH,SAAS,GAAG,UAAU;SAFlBG,QAAQ;AA0BrB7B,aAAa,CAAC2B,aAAa,CAACE,QAAQ,CAAC;AAWrC,MAAaI,aAAc,SAAQf,WAAW;EAS5CY,YAAYC,IAAuB;IACjC,KAAK,EAAE;IAPA,KAAAG,cAAc,GAAG,CAAC,IAAI;IACtB,KAAAC,cAAc,GAAG,IAAI;IAO5B,IAAI,CAACC,MAAM,GAAGL,IAAI,CAACK,MAAM,IAAI,IAAI,CAACF,cAAc;IAChD,IAAI,CAACG,MAAM,GAAGN,IAAI,CAACM,MAAM,IAAI,IAAI,CAACF,cAAc;IAChD,IAAI,CAACG,IAAI,GAAGP,IAAI,CAACO,IAAI;EACvB;EAEAf,KAAKA,CAACC,KAAY,EAAEC,KAAgB;IAClC,OAAO3B,aAAa,CAAC0B,KAAK,EAAE,IAAI,CAACY,MAAM,EAAE,IAAI,CAACC,MAAM,EAAEZ,KAAK,EAAE,IAAI,CAACa,IAAI,CAAC;EACzE;EAESjB,SAASA,CAAA;IAChB,OAAO;MAACe,MAAM,EAAE,IAAI,CAACA,MAAM;MAAEC,MAAM,EAAE,IAAI,CAACA,MAAM;MAAEC,IAAI,EAAE,IAAI,CAACA;IAAI,CAAC;EACpE;;AArBA;AACOL,aAAA,CAAAP,SAAS,GAAG,eAAe;SAFvBO,aAAa;AAwB1BjC,aAAa,CAAC2B,aAAa,CAACM,aAAa,CAAC;AAW1C,MAAaM,YAAa,SAAQrB,WAAW;EAS3CY,YAAYC,IAAsB;IAChC,KAAK,EAAE;IAPA,KAAAS,YAAY,GAAG,EAAE;IACjB,KAAAC,cAAc,GAAG,IAAI;IAO5B,IAAI,CAACC,IAAI,GAAGX,IAAI,CAACW,IAAI,IAAI,IAAI,CAACF,YAAY;IAC1C,IAAI,CAACG,MAAM,GAAGZ,IAAI,CAACY,MAAM,IAAI,IAAI,CAACF,cAAc;IAChD,IAAI,CAACH,IAAI,GAAGP,IAAI,CAACO,IAAI;EACvB;EAEAf,KAAKA,CAACC,KAAY,EAAEC,KAAgB;IAClCA,KAAK,GAAGA,KAAK,IAAI,SAAS;IAC1B,IAAIA,KAAK,KAAK,SAAS,IAAIA,KAAK,KAAK,OAAO,EAAE;MAC5C,MAAM,IAAIlB,mBAAmB,CACzB,uCAAuCkB,KAAK,GAAG,CAAC;;IAGtD,OAAOpB,CAAC,CAACuC,YAAY,CAACpB,KAAK,EAAE,IAAI,CAACkB,IAAI,EAAE,IAAI,CAACC,MAAM,EAAElB,KAAK,EAAE,IAAI,CAACa,IAAI,CAAC;EACxE;EAESjB,SAASA,CAAA;IAChB,OAAO;MAACqB,IAAI,EAAE,IAAI,CAACA,IAAI;MAAEC,MAAM,EAAE,IAAI,CAACA,MAAM;MAAEL,IAAI,EAAE,IAAI,CAACA;IAAI,CAAC;EAChE;;AA3BA;AACOC,YAAA,CAAAb,SAAS,GAAG,cAAc;SAFtBa,YAAY;AA8BzBvC,aAAa,CAAC2B,aAAa,CAACY,YAAY,CAAC;AAWzC,MAAaM,eAAgB,SAAQ3B,WAAW;EAU9CY,YAAYC,IAAyB;IACnC,KAAK,EAAE;IAPA,KAAAS,YAAY,GAAG,EAAE;IACjB,KAAAC,cAAc,GAAG,IAAI;IAO5B,IAAI,CAACC,IAAI,GAAGX,IAAI,CAACW,IAAI,IAAI,IAAI,CAACF,YAAY;IAC1C,IAAI,CAACG,MAAM,GAAGZ,IAAI,CAACY,MAAM,IAAI,IAAI,CAACF,cAAc;IAChD,IAAI,CAACH,IAAI,GAAGP,IAAI,CAACO,IAAI;EACvB;EAEAf,KAAKA,CAACC,KAAY,EAAEC,KAAgB;IAClCA,KAAK,GAAGA,KAAK,IAAI,SAAS;IAC1B,IAAIA,KAAK,KAAK,SAAS,IAAIA,KAAK,KAAK,OAAO,EAAE;MAC5C,MAAM,IAAIlB,mBAAmB,CACzB,0CAA0CkB,KAAK,GAAG,CAAC;;IAEzD,OAAOvB,eAAe,CAACsB,KAAK,EAAE,IAAI,CAACkB,IAAI,EAAE,IAAI,CAACC,MAAM,EAAElB,KAAK,EAAE,IAAI,CAACa,IAAI,CAAC;EACzE;EAESjB,SAASA,CAAA;IAChB,OAAO;MAACqB,IAAI,EAAE,IAAI,CAACA,IAAI;MAAEC,MAAM,EAAE,IAAI,CAACA,MAAM;MAAEL,IAAI,EAAE,IAAI,CAACA;IAAI,CAAC;EAChE;;AA3BA;AACOO,eAAA,CAAAnB,SAAS,GAAG,iBAAiB;SAFzBmB,eAAe;AA8B5B7C,aAAa,CAAC2B,aAAa,CAACkB,eAAe,CAAC;AAS5C,MAAaC,QAAS,SAAQ5B,WAAW;EAIvCY,YAAYC,IAAkB;IAC5B,KAAK,EAAE;IACP,IAAI,CAACgB,IAAI,GAAGhB,IAAI,CAACgB,IAAI,IAAI,IAAI,GAAGhB,IAAI,CAACgB,IAAI,GAAG,GAAG;EACjD;EAEAxB,KAAKA,CAACC,KAAY,EAAEC,KAAgB;IAClC,OAAOxB,IAAI,CAAC,MAAK;MACf,IAAIuB,KAAK,CAACwB,MAAM,KAAK,CAAC,IAAIxB,KAAK,CAAC,CAAC,CAAC,KAAKA,KAAK,CAAC,CAAC,CAAC,EAAE;QAC/C,MAAM,IAAIhB,UAAU,CAChB,kDAAkD,GAClD,sBAAsB,CAAC;OAC5B,MAAM;QACL,OAAOZ,GAAG,CAAC,IAAI,CAACmD,IAAI,EAAErD,GAAG,CAAC8B,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;;IAExC,CAAC,CAAC;EACJ;EAESH,SAASA,CAAA;IAChB,OAAO;MAAC0B,IAAI,EAAE,IAAI,CAACA;IAAI,CAAC;EAC1B;;AAtBA;AACOD,QAAA,CAAApB,SAAS,GAAG,UAAU;SAFlBoB,QAAQ;AAyBrB9C,aAAa,CAAC2B,aAAa,CAACmB,QAAQ,CAAC;AAErC;;;;;;;;AAQA,SAASG,WAAWA,CAChBzB,KAAY,EAAE0B,UAAA,GAAyB,cAAc;EACvD,IAAIC,KAAa;EACjB,IAAIC,MAAc;EAClB9C,eAAe,CAAC4C,UAAU,CAAC;EAC3B,IAAI1B,KAAK,CAACwB,MAAM,KAAK,CAAC,EAAE;IACtBG,KAAK,GAAG3B,KAAK,CAAC,CAAC,CAAC;IAChB4B,MAAM,GAAG5B,KAAK,CAAC,CAAC,CAAC;GAClB,MAAM,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC6B,OAAO,CAAC7B,KAAK,CAACwB,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE;IACjD,IAAIE,UAAU,KAAK,eAAe,EAAE;MAClC,MAAMI,kBAAkB,GAAGxC,SAAS,CAACU,KAAK,EAAE,CAAC,CAAC;MAC9C2B,KAAK,GAAG3B,KAAK,CAAC,CAAC,CAAC,GAAG8B,kBAAkB;MACrCF,MAAM,GAAG5B,KAAK,CAAC,CAAC,CAAC,GAAG8B,kBAAkB;KACvC,MAAM,IAAIJ,UAAU,KAAK,cAAc,EAAE;MACxC,MAAMI,kBAAkB,GAAGxC,SAAS,CAACU,KAAK,EAAE,CAAC,EAAEA,KAAK,CAACwB,MAAM,GAAG,CAAC,CAAC;MAChEG,KAAK,GAAG3B,KAAK,CAACA,KAAK,CAACwB,MAAM,GAAG,CAAC,CAAC,GAAGM,kBAAkB;MACpDF,MAAM,GAAG5B,KAAK,CAACA,KAAK,CAACwB,MAAM,GAAG,CAAC,CAAC,GAAGM,kBAAkB;;GAExD,MAAM;IACL,MAAMC,SAAS,GAAGzC,SAAS,CAACU,KAAK,CAAC;IAClC2B,KAAK,GAAGK,IAAI,CAACC,IAAI,CAACF,SAAS,CAAC;IAC5BH,MAAM,GAAGI,IAAI,CAACC,IAAI,CAACF,SAAS,CAAC;;EAG/B,OAAO,CAACJ,KAAK,EAAEC,MAAM,CAAC;AACxB;AAgBA,MAAaM,eAAgB,SAAQxC,WAAW;EAQ9C;;;;EAIAY,YAAYC,IAAyB;IACnC,KAAK,EAAE;IACP,IAAIA,IAAI,CAAC4B,KAAK,GAAG,GAAG,EAAE;MACpB,MAAM,IAAInD,UAAU,CAChB,wCAAwCuB,IAAI,CAAC4B,KAAK,EAAE,CAAC;;IAE3D,IAAI,CAACA,KAAK,GAAG5B,IAAI,CAAC4B,KAAK,IAAI,IAAI,GAAG,GAAG,GAAG5B,IAAI,CAAC4B,KAAK;IAClD,IAAI,CAACC,IAAI,GAAG7B,IAAI,CAAC6B,IAAI,IAAI,IAAI,GAAG,OAAO,GAAG7B,IAAI,CAAC6B,IAAI;IACnD7C,YAAY,CAAC,IAAI,CAAC6C,IAAI,CAAC;IACvB,IAAI,CAACC,YAAY,GACb9B,IAAI,CAAC8B,YAAY,IAAI,IAAI,GAAG,QAAQ,GAAG9B,IAAI,CAAC8B,YAAY;IAC5D5C,iBAAiB,CAAC,IAAI,CAAC4C,YAAY,CAAC;IACpC,IAAI,CAACvB,IAAI,GAAGP,IAAI,CAACO,IAAI;EACvB;EAEAf,KAAKA,CAACC,KAAY,EAAEC,KAAgB;IAClC,MAAMqC,IAAI,GAAGb,WAAW,CAACzB,KAAK,CAAC;IAC/B,MAAM2B,KAAK,GAAGW,IAAI,CAAC,CAAC,CAAC;IACrB,MAAMV,MAAM,GAAGU,IAAI,CAAC,CAAC,CAAC;IACtB,IAAIH,KAAK,GAAG,IAAI,CAACA,KAAK;IACtB,IAAI,IAAI,CAACC,IAAI,KAAK,OAAO,EAAE;MACzBD,KAAK,IAAIH,IAAI,CAACO,GAAG,CAAC,CAAC,EAAEZ,KAAK,CAAC;KAC5B,MAAM,IAAI,IAAI,CAACS,IAAI,KAAK,QAAQ,EAAE;MACjCD,KAAK,IAAIH,IAAI,CAACO,GAAG,CAAC,CAAC,EAAEX,MAAM,CAAC;KAC7B,MAAM;MACLO,KAAK,IAAIH,IAAI,CAACO,GAAG,CAAC,CAAC,EAAE,CAACZ,KAAK,GAAGC,MAAM,IAAI,CAAC,CAAC;;IAG5C,IAAI,IAAI,CAACS,YAAY,KAAK,QAAQ,EAAE;MAClC,MAAMlB,MAAM,GAAGa,IAAI,CAACC,IAAI,CAACE,KAAK,CAAC;MAC/BlC,KAAK,GAAGA,KAAK,IAAI,SAAS;MAC1B,IAAIA,KAAK,KAAK,SAAS,IAAIA,KAAK,KAAK,OAAO,EAAE;QAC5C,MAAM,IAAIlB,mBAAmB,CACzB,GAAG,IAAI,CAACyD,YAAY,EAAE,2BAA2BvC,KAAK,GAAG,CAAC;;MAEhE,OAAOvB,eAAe,CAACsB,KAAK,EAAE,CAAC,EAAEmB,MAAM,EAAElB,KAAK,EAAE,IAAI,CAACa,IAAI,CAAC;KAC3D,MAAM;MACL,MAAM2B,KAAK,GAAGT,IAAI,CAACC,IAAI,CAAC,CAAC,GAAGE,KAAK,CAAC;MAClC,OAAO7D,aAAa,CAAC0B,KAAK,EAAE,CAACyC,KAAK,EAAEA,KAAK,EAAExC,KAAK,EAAE,IAAI,CAACa,IAAI,CAAC;;EAEhE;EAESjB,SAASA,CAAA;IAChB,OAAO;MACLsC,KAAK,EAAE,IAAI,CAACA,KAAK;MACjBC,IAAI,EAAE,IAAI,CAACA,IAAI;MACfC,YAAY,EAAE,IAAI,CAACA,YAAY;MAC/BvB,IAAI,EAAE,IAAI,CAACA;KACZ;EACH;;AA5DA;AACOoB,eAAA,CAAAhC,SAAS,GAAG,iBAAiB;SAFzBgC,eAAe;AA+D5B1D,aAAa,CAAC2B,aAAa,CAAC+B,eAAe,CAAC;AAO5C,MAAaQ,aAAc,SAAQR,eAAe;EAIhD;;;;;;;EAOA5B,YAAYC,IAA8B;IACxC,KAAK,CAAC;MACJ4B,KAAK,EAAE,GAAG;MACVC,IAAI,EAAE,QAAQ;MACdC,YAAY,EAAE,SAAS;MACvBvB,IAAI,EAAEP,IAAI,IAAI,IAAI,GAAG,IAAI,GAAGA,IAAI,CAACO;KAClC,CAAC;EACJ;EAES0B,YAAYA,CAAA;IACnB;IACA;IACA;IACA,OAAON,eAAe,CAAChC,SAAS;EAClC;;AAxBA;AACgBwC,aAAA,CAAAxC,SAAS,GAAG,eAAe;SAFhCwC,aAAa;AA2B1BlE,aAAa,CAAC2B,aAAa,CAACuC,aAAa,CAAC;AAE1C,MAAaC,YAAa,SAAQT,eAAe;EAI/C;;;;;;;EAOA5B,YAAYC,IAA8B;IACxC,KAAK,CAAC;MACJ4B,KAAK,EAAE,GAAG;MACVC,IAAI,EAAE,QAAQ;MACdC,YAAY,EAAE,QAAQ;MACtBvB,IAAI,EAAEP,IAAI,IAAI,IAAI,GAAG,IAAI,GAAGA,IAAI,CAACO;KAClC,CAAC;EACJ;EAES0B,YAAYA,CAAA;IACnB;IACA;IACA;IACA,OAAON,eAAe,CAAChC,SAAS;EAClC;;AAxBA;AACgByC,YAAA,CAAAzC,SAAS,GAAG,cAAc;SAF/ByC,YAAY;AA2BzBnE,aAAa,CAAC2B,aAAa,CAACwC,YAAY,CAAC;AAEzC,MAAaC,QAAS,SAAQV,eAAe;EAI3C5B,YAAYC,IAA8B;IACxC,KAAK,CAAC;MACJ4B,KAAK,EAAE,GAAG;MACVC,IAAI,EAAE,OAAO;MACbC,YAAY,EAAE,QAAQ;MACtBvB,IAAI,EAAEP,IAAI,IAAI,IAAI,GAAG,IAAI,GAAGA,IAAI,CAACO;KAClC,CAAC;EACJ;EAES0B,YAAYA,CAAA;IACnB;IACA;IACA;IACA,OAAON,eAAe,CAAChC,SAAS;EAClC;;AAjBA;AACgB0C,QAAA,CAAA1C,SAAS,GAAG,UAAU;SAF3B0C,QAAQ;AAoBrBpE,aAAa,CAAC2B,aAAa,CAACyC,QAAQ,CAAC;AAErC,MAAaC,SAAU,SAAQX,eAAe;EAI5C5B,YAAYC,IAA8B;IACxC,KAAK,CAAC;MACJ4B,KAAK,EAAE,GAAG;MACVC,IAAI,EAAE,OAAO;MACbC,YAAY,EAAE,SAAS;MACvBvB,IAAI,EAAEP,IAAI,IAAI,IAAI,GAAG,IAAI,GAAGA,IAAI,CAACO;KAClC,CAAC;EACJ;EAES0B,YAAYA,CAAA;IACnB;IACA;IACA;IACA,OAAON,eAAe,CAAChC,SAAS;EAClC;;AAjBA;AACgB2C,SAAA,CAAA3C,SAAS,GAAG,WAAW;SAF5B2C,SAAS;AAoBtBrE,aAAa,CAAC2B,aAAa,CAAC0C,SAAS,CAAC;AAEtC,MAAaC,WAAY,SAAQZ,eAAe;EAI9C5B,YAAYC,IAA8B;IACxC,KAAK,CAAC;MACJ4B,KAAK,EAAE,GAAG;MACVC,IAAI,EAAE,OAAO;MACbC,YAAY,EAAE,QAAQ;MACtBvB,IAAI,EAAEP,IAAI,IAAI,IAAI,GAAG,IAAI,GAAGA,IAAI,CAACO;KAClC,CAAC;EACJ;EAES0B,YAAYA,CAAA;IACnB;IACA;IACA;IACA,OAAON,eAAe,CAAChC,SAAS;EAClC;;AAjBA;AACgB4C,WAAA,CAAA5C,SAAS,GAAG,aAAa;SAF9B4C,WAAW;AAoBxBtE,aAAa,CAAC2B,aAAa,CAAC2C,WAAW,CAAC;AAExC,MAAaC,YAAa,SAAQb,eAAe;EAI/C5B,YAAYC,IAA8B;IACxC,KAAK,CAAC;MACJ4B,KAAK,EAAE,GAAG;MACVC,IAAI,EAAE,OAAO;MACbC,YAAY,EAAE,SAAS;MACvBvB,IAAI,EAAEP,IAAI,IAAI,IAAI,GAAG,IAAI,GAAGA,IAAI,CAACO;KAClC,CAAC;EACJ;EAES0B,YAAYA,CAAA;IACnB;IACA;IACA;IACA,OAAON,eAAe,CAAChC,SAAS;EAClC;;AAjBA;AACgB6C,YAAA,CAAA7C,SAAS,GAAG,cAAc;SAF/B6C,YAAY;AAoBzBvE,aAAa,CAAC2B,aAAa,CAAC4C,YAAY,CAAC;AASzC,MAAaC,UAAW,SAAQtD,WAAW;EAQzCY,YAAYC,IAAqB;IAC/B,KAAK,EAAE;IANA,KAAA0C,YAAY,GAAG,CAAC;IAChB,KAAAC,kBAAkB,GAAG,IAAI;IAMhC,IAAI,CAAC3B,IAAI,GAAGhB,IAAI,CAACgB,IAAI,IAAI,IAAI,GAAG,IAAI,CAAC0B,YAAY,GAAG1C,IAAI,CAACgB,IAAI;IAC7D,IAAI,CAACT,IAAI,GAAGP,IAAI,CAACO,IAAI;EACvB;EAEAf,KAAKA,CAACC,KAAY,EAAEC,KAAgB;IAClC,OAAOxB,IAAI,CAAC,MAAK;MACf,IAAIuB,KAAK,CAACwB,MAAM,GAAG,CAAC,EAAE;QACpB,MAAM,IAAIzC,mBAAmB,CAAC,4BAA4B,CAAC;;MAE7D,IAAIkB,KAAK,KAAK,OAAO,IAAIA,KAAK,KAAK,SAAS,IAAIA,KAAK,KAAKO,SAAS,EAAE;QACnE,MAAM,IAAI2C,SAAS,CAAC,yBAAyBlD,KAAK,GAAG,CAAC;;MAExDA,KAAK,GAAGA,KAAwC;MAEhD;MACA;MACA,MAAMmD,OAAO,GAAGzE,IAAI,CAAC0E,aAAa,CAACrD,KAAK,CAACsD,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACtD,MAAMC,OAAO,GAAGvD,KAAK,CAACA,KAAK,CAACwB,MAAM,GAAG,CAAC,CAAC;MACvC,MAAMgC,WAAW,GAAGJ,OAAO,GAAGG,OAAO;MACrC,IAAIC,WAAW,GAAG,IAAI,CAACN,kBAAkB,EAAE;QACzCO,OAAO,CAACC,IAAI,CACR,+DAA+D,GAC/D,QAAQ,IAAI,CAACR,kBAAkB,KAAKM,WAAW,cAAc,GAC7D,sBAAsB,CAAC;;MAE7B,MAAMG,SAAS,GACX,CAAC3B,IAAI,CAACO,GAAG,CAACgB,OAAO,EAAEH,OAAO,CAAC,EAAEpB,IAAI,CAAC4B,GAAG,CAACL,OAAO,EAAEH,OAAO,CAAC,CAAC;MAE5D;MACA,MAAMS,aAAa,GAAGhF,CAAC,CAACuC,YAAY,CAACuC,SAAS,EAAE,CAAC,EAAE,CAAC,EAAE1D,KAAK,EAAE,IAAI,CAACa,IAAI,CAAC;MAEvE;MACA,MAAMgD,EAAE,GAAG3F,MAAM,CAAC2F,EAAE,CAACD,aAAa,EAAE,KAAK,CAAC;MAC1C,IAAIE,IAAI,GAAGD,EAAE,CAAC,CAAC,CAAC;MAChB,MAAME,IAAI,GAAGF,EAAE,CAAC,CAAC,CAAC;MAElB;MACA,MAAMG,IAAI,GAAGD,IAAI,CAACE,OAAO,EAAE,CAACC,YAAY,CACpC,CAAC,CAAC,CAAC,EAAE,CAACnC,IAAI,CAAC4B,GAAG,CAACL,OAAO,EAAEH,OAAO,CAAC,GAAGpB,IAAI,CAAC4B,GAAG,CAACL,OAAO,EAAEH,OAAO,CAAC,CAAC,EAC9D,CAACpB,IAAI,CAAC4B,GAAG,CAACL,OAAO,EAAEH,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC;MACrCW,IAAI,GAAG3F,GAAG,CAAC2F,IAAI,EAAEE,IAAI,CAACG,IAAI,EAAE,CAAC;MAC7B,IAAIhB,OAAO,GAAGG,OAAO,EAAE;QACrBQ,IAAI,GAAGA,IAAI,CAACM,SAAS,EAAE;;MAGzB,OAAOjG,GAAG,CAACG,MAAM,CAAC,IAAI,CAACgD,IAAI,CAAC,EAAEwC,IAAI,CAACO,OAAO,CAACtE,KAAK,CAAC,CAAC;IACpD,CAAC,CAAC;EACJ;EAESH,SAASA,CAAA;IAChB,OAAO;MACL0B,IAAI,EAAE,IAAI,CAACA,IAAI;MACfT,IAAI,EAAE,IAAI,CAACA;KACZ;EACH;;AA/DA;AACOkC,UAAA,CAAA9C,SAAS,GAAG,YAAY;SAFpB8C,UAAU;AAkEvBxE,aAAa,CAAC2B,aAAa,CAAC6C,UAAU,CAAC;AAQvC;AACA;AACA,OAAO,MAAMuB,0CAA0C,GACD;EAChD,UAAU,EAAE,UAAU;EACtB,cAAc,EAAE,cAAc;EAC9B,eAAe,EAAE,eAAe;EAChC,UAAU,EAAE,UAAU;EACtB,WAAW,EAAE,WAAW;EACxB,UAAU,EAAE,UAAU;EACtB,aAAa,EAAE,aAAa;EAC5B,cAAc,EAAE,cAAc;EAC9B,MAAM,EAAE,MAAM;EACd,YAAY,EAAE,YAAY;EAC1B,cAAc,EAAE,cAAc;EAC9B,eAAe,EAAE,eAAe;EAChC,iBAAiB,EAAE,iBAAiB;EACpC,iBAAiB,EAAE,iBAAiB;EACpC,OAAO,EAAE;CACV;AAEL,SAASC,sBAAsBA,CAC3BC,MAAgC,EAChCC,aAAA,GAA0C,EAAE;EAC9C,OAAOtF,sBAAsB,CACzBqF,MAAM,EAAEjG,aAAa,CAACmG,gBAAgB,CAACC,MAAM,EAAE,CAACC,YAAY,EAC5DH,aAAa,EAAE,aAAa,CAAC;AACnC;AAEA,OAAM,SAAUI,oBAAoBA,CAACC,WAAwB;EAE3D,OAAO1F,oBAAoB,CAAC0F,WAAW,CAAC;AAC1C;AAEA,OAAM,SAAUC,cAAcA,CAACC,UACwB;EACrD,IAAI,OAAOA,UAAU,KAAK,QAAQ,EAAE;IAClC,MAAM/E,SAAS,GAAG+E,UAAU,IAAIV,0CAA0C,GACtEA,0CAA0C,CAACU,UAAU,CAAC,GACtDA,UAAU;IACd;;;IAGA,IAAI/E,SAAS,KAAK,cAAc,EAAE;MAChC,OAAO,IAAIyC,YAAY,EAAE;KAC1B,MAAM,IAAIzC,SAAS,KAAK,eAAe,EAAE;MACxC,OAAO,IAAIwC,aAAa,EAAE;KAC3B,MAAM,IAAIxC,SAAS,KAAK,UAAU,EAAE;MACnC,OAAO,IAAI0C,QAAQ,EAAE;KACtB,MAAM,IAAI1C,SAAS,KAAK,WAAW,EAAE;MACpC,OAAO,IAAI2C,SAAS,EAAE;KACvB,MAAM,IAAI3C,SAAS,KAAK,aAAa,EAAE;MACtC,OAAO,IAAI4C,WAAW,EAAE;KACzB,MAAM,IAAI5C,SAAS,KAAK,cAAc,EAAE;MACvC,OAAO,IAAI6C,YAAY,EAAE;KAC1B,MAAM;MACL,MAAM0B,MAAM,GAA6B,EAAE;MAC3CA,MAAM,CAAC,WAAW,CAAC,GAAGvE,SAAS;MAC/BuE,MAAM,CAAC,QAAQ,CAAC,GAAG,EAAE;MACrB,OAAOD,sBAAsB,CAACC,MAAM,CAAC;;GAExC,MAAM,IAAIQ,UAAU,YAAYvF,WAAW,EAAE;IAC5C,OAAOuF,UAAU;GAClB,MAAM;IACL,OAAOT,sBAAsB,CAACS,UAAU,CAAC;;AAE7C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}