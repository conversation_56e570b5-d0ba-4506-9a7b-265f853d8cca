{"ast": null, "code": "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { Maximum } from '@tensorflow/tfjs-core';\nimport { CHECK_NAN_SNIPPET } from '../binaryop_gpu';\nimport { CHECK_NAN_SNIPPET_PACKED } from '../binaryop_packed_gpu';\nimport { binaryKernelFunc } from '../kernel_utils/kernel_funcs_utils';\nimport { maximumImplCPU } from '../kernel_utils/shared';\nconst MAXIMUM = CHECK_NAN_SNIPPET + \"\\n  return max(a, b);\\n\";\nconst MAXIMUM_PACKED = \"\\n  vec4 result = vec4(max(a, b));\\n  bvec4 isNaNA = isnan(a);\\n  bvec4 isNaNB = isnan(b);\\n  bvec4 isNaN = bvec4(isNaNA.x || isNaNB.x, isNaNA.y || isNaNB.y, isNaNA.z || isNaNB.z, isNaNA.w || isNaNB.w);\\n  \" + CHECK_NAN_SNIPPET_PACKED + \"\\n  return result;\\n\";\nexport const maximum = binaryKernelFunc({\n  opSnippet: MAXIMUM,\n  packedOpSnippet: MAXIMUM_PACKED,\n  cpuKernelImpl: maximumImplCPU\n});\nexport const maximumConfig = {\n  kernelName: Maximum,\n  backendName: 'webgl',\n  kernelFunc: maximum\n};", "map": {"version": 3, "names": ["Maximum", "CHECK_NAN_SNIPPET", "CHECK_NAN_SNIPPET_PACKED", "binaryKernelFunc", "maximumImplCPU", "MAXIMUM", "MAXIMUM_PACKED", "maximum", "opSnippet", "packedOpSnippet", "cpuKernelImpl", "maximumConfig", "kernelName", "backendName", "kernelFunc"], "sources": ["C:\\tfjs-backend-webgl\\src\\kernels\\Maximum.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {KernelConfig, KernelFunc, Maximum} from '@tensorflow/tfjs-core';\n\nimport {CHECK_NAN_SNIPPET} from '../binaryop_gpu';\nimport {CHECK_NAN_SNIPPET_PACKED} from '../binaryop_packed_gpu';\nimport {binaryKernelFunc} from '../kernel_utils/kernel_funcs_utils';\nimport {maximumImplCPU} from '../kernel_utils/shared';\n\nconst MAXIMUM = CHECK_NAN_SNIPPET + `\n  return max(a, b);\n`;\n\nconst MAXIMUM_PACKED = `\n  vec4 result = vec4(max(a, b));\n  bvec4 isNaNA = isnan(a);\n  bvec4 isNaNB = isnan(b);\n  bvec4 isNaN = bvec4(isNaNA.x || isNaNB.x, isNaNA.y || isNaNB.y, isNaNA.z || isNaNB.z, isNaNA.w || isNaNB.w);\n  ` +\n    CHECK_NAN_SNIPPET_PACKED + `\n  return result;\n`;\n\nexport const maximum = binaryKernelFunc({\n  opSnippet: MAXIMUM,\n  packedOpSnippet: MAXIMUM_PACKED,\n  cpuKernelImpl: maximumImplCPU\n});\n\nexport const maximumConfig: KernelConfig = {\n  kernelName: Maximum,\n  backendName: 'webgl',\n  kernelFunc: maximum as unknown as KernelFunc\n};\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAiBA,SAAkCA,OAAO,QAAO,uBAAuB;AAEvE,SAAQC,iBAAiB,QAAO,iBAAiB;AACjD,SAAQC,wBAAwB,QAAO,wBAAwB;AAC/D,SAAQC,gBAAgB,QAAO,oCAAoC;AACnE,SAAQC,cAAc,QAAO,wBAAwB;AAErD,MAAMC,OAAO,GAAGJ,iBAAiB,4BAEhC;AAED,MAAMK,cAAc,GAAG,mNAMnBJ,wBAAwB,yBAE3B;AAED,OAAO,MAAMK,OAAO,GAAGJ,gBAAgB,CAAC;EACtCK,SAAS,EAAEH,OAAO;EAClBI,eAAe,EAAEH,cAAc;EAC/BI,aAAa,EAAEN;CAChB,CAAC;AAEF,OAAO,MAAMO,aAAa,GAAiB;EACzCC,UAAU,EAAEZ,OAAO;EACnBa,WAAW,EAAE,OAAO;EACpBC,UAAU,EAAEP;CACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}