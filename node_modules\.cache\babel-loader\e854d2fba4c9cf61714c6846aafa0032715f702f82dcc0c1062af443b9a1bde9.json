{"ast": null, "code": "/**\n * @license\n * Copyright 2021 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { ENGINE } from '../../engine';\nimport { Transform } from '../../kernel_names';\nimport { convertToTensor } from '../../tensor_util_env';\nimport * as util from '../../util';\nimport { op } from '../operation';\n/**\n * Applies the given transform(s) to the image(s).\n *\n * @param image 4d tensor of shape `[batch, imageHeight, imageWidth, depth]`.\n * @param transforms Projective transform matrix/matrices. A tensor1d of length\n *     8 or tensor of size N x 8. If one row of transforms is [a0, a1, a2, b0,\n *     b1, b2, c0, c1], then it maps the output point (x, y) to a transformed\n *     input point (x', y') = ((a0 x + a1 y + a2) / k, (b0 x + b1 y + b2) / k),\n *     where k = c0 x + c1 y + 1. The transforms are inverted compared to the\n *     transform mapping input points to output points.\n * @param interpolation Interpolation mode.\n *     Supported values: 'nearest', 'bilinear'. Default to 'nearest'.\n * @param fillMode Points outside the boundaries of the input are filled\n *     according to the given mode, one of 'constant', 'reflect', 'wrap',\n *     'nearest'. Default to 'constant'.\n *     'reflect': (d c b a | a b c d | d c b a ) The input is extended by\n *     reflecting about the edge of the last pixel.\n *     'constant': (k k k k | a b c d | k k k k) The input is extended by\n *     filling all values beyond the edge with the same constant value k.\n *     'wrap': (a b c d | a b c d | a b c d) The input is extended by\n *     wrapping around to the opposite edge.\n *     'nearest': (a a a a | a b c d | d d d d) The input is extended by\n *     the nearest pixel.\n * @param fillValue A float represents the value to be filled outside the\n *     boundaries when fillMode is 'constant'.\n * @param Output dimension after the transform, [height, width]. If undefined,\n *     output is the same size as input image.\n *\n * @doc {heading: 'Operations', subheading: 'Images', namespace: 'image'}\n */\nfunction transform_(image, transforms) {\n  let interpolation = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 'nearest';\n  let fillMode = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : 'constant';\n  let fillValue = arguments.length > 4 && arguments[4] !== undefined ? arguments[4] : 0;\n  let outputShape = arguments.length > 5 ? arguments[5] : undefined;\n  const $image = convertToTensor(image, 'image', 'transform', 'float32');\n  const $transforms = convertToTensor(transforms, 'transforms', 'transform', 'float32');\n  util.assert($image.rank === 4, () => 'Error in transform: image must be rank 4,' + \"but got rank \".concat($image.rank, \".\"));\n  util.assert($transforms.rank === 2 && ($transforms.shape[0] === $image.shape[0] || $transforms.shape[0] === 1) && $transforms.shape[1] === 8, () => \"Error in transform: Input transform should be batch x 8 or 1 x 8\");\n  util.assert(outputShape == null || outputShape.length === 2, () => 'Error in transform: outputShape must be [height, width] or null, ' + \"but got \".concat(outputShape, \".\"));\n  const inputs = {\n    image: $image,\n    transforms: $transforms\n  };\n  const attrs = {\n    interpolation,\n    fillMode,\n    fillValue,\n    outputShape\n  };\n  return ENGINE.runKernel(Transform, inputs, attrs);\n}\nexport const transform = /* @__PURE__ */op({\n  transform_\n});", "map": {"version": 3, "names": ["ENGINE", "Transform", "convertToTensor", "util", "op", "transform_", "image", "transforms", "interpolation", "arguments", "length", "undefined", "fillMode", "fillValue", "outputShape", "$image", "$transforms", "assert", "rank", "concat", "shape", "inputs", "attrs", "runKernel", "transform"], "sources": ["C:\\tfjs-core\\src\\ops\\image\\transform.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2021 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport {ENGINE} from '../../engine';\nimport {Transform, TransformAttrs, TransformInputs} from '../../kernel_names';\nimport {NamedAttrMap} from '../../kernel_registry';\nimport {Tensor2D, Tensor4D} from '../../tensor';\nimport {NamedTensorMap} from '../../tensor_types';\nimport {convertToTensor} from '../../tensor_util_env';\nimport {TensorLike} from '../../types';\nimport * as util from '../../util';\n\nimport {op} from '../operation';\n\n/**\n * Applies the given transform(s) to the image(s).\n *\n * @param image 4d tensor of shape `[batch, imageHeight, imageWidth, depth]`.\n * @param transforms Projective transform matrix/matrices. A tensor1d of length\n *     8 or tensor of size N x 8. If one row of transforms is [a0, a1, a2, b0,\n *     b1, b2, c0, c1], then it maps the output point (x, y) to a transformed\n *     input point (x', y') = ((a0 x + a1 y + a2) / k, (b0 x + b1 y + b2) / k),\n *     where k = c0 x + c1 y + 1. The transforms are inverted compared to the\n *     transform mapping input points to output points.\n * @param interpolation Interpolation mode.\n *     Supported values: 'nearest', 'bilinear'. Default to 'nearest'.\n * @param fillMode Points outside the boundaries of the input are filled\n *     according to the given mode, one of 'constant', 'reflect', 'wrap',\n *     'nearest'. Default to 'constant'.\n *     'reflect': (d c b a | a b c d | d c b a ) The input is extended by\n *     reflecting about the edge of the last pixel.\n *     'constant': (k k k k | a b c d | k k k k) The input is extended by\n *     filling all values beyond the edge with the same constant value k.\n *     'wrap': (a b c d | a b c d | a b c d) The input is extended by\n *     wrapping around to the opposite edge.\n *     'nearest': (a a a a | a b c d | d d d d) The input is extended by\n *     the nearest pixel.\n * @param fillValue A float represents the value to be filled outside the\n *     boundaries when fillMode is 'constant'.\n * @param Output dimension after the transform, [height, width]. If undefined,\n *     output is the same size as input image.\n *\n * @doc {heading: 'Operations', subheading: 'Images', namespace: 'image'}\n */\nfunction transform_(\n    image: Tensor4D|TensorLike, transforms: Tensor2D|TensorLike,\n    interpolation: 'nearest'|'bilinear' = 'nearest',\n    fillMode: 'constant'|'reflect'|'wrap'|'nearest' = 'constant', fillValue = 0,\n    outputShape?: [number, number]): Tensor4D {\n  const $image = convertToTensor(image, 'image', 'transform', 'float32');\n  const $transforms =\n      convertToTensor(transforms, 'transforms', 'transform', 'float32');\n\n  util.assert(\n      $image.rank === 4,\n      () => 'Error in transform: image must be rank 4,' +\n          `but got rank ${$image.rank}.`);\n\n  util.assert(\n      $transforms.rank === 2 &&\n          ($transforms.shape[0] === $image.shape[0] ||\n           $transforms.shape[0] === 1) &&\n          $transforms.shape[1] === 8,\n      () => `Error in transform: Input transform should be batch x 8 or 1 x 8`);\n\n  util.assert(\n      outputShape == null || outputShape.length === 2,\n      () =>\n          'Error in transform: outputShape must be [height, width] or null, ' +\n          `but got ${outputShape}.`);\n\n  const inputs: TransformInputs = {image: $image, transforms: $transforms};\n  const attrs:\n      TransformAttrs = {interpolation, fillMode, fillValue, outputShape};\n\n  return ENGINE.runKernel(\n      Transform, inputs as unknown as NamedTensorMap,\n      attrs as unknown as NamedAttrMap);\n}\n\nexport const transform = /* @__PURE__ */ op({transform_});\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAgBA,SAAQA,MAAM,QAAO,cAAc;AACnC,SAAQC,SAAS,QAAwC,oBAAoB;AAI7E,SAAQC,eAAe,QAAO,uBAAuB;AAErD,OAAO,KAAKC,IAAI,MAAM,YAAY;AAElC,SAAQC,EAAE,QAAO,cAAc;AAE/B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8BA,SAASC,UAAUA,CACfC,KAA0B,EAAEC,UAA+B,EAG7B;EAAA,IAF9BC,aAAA,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAsC,SAAS;EAAA,IAC/CG,QAAA,GAAAH,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAkD,UAAU;EAAA,IAAEI,SAAS,GAAAJ,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC;EAAA,IAC3EK,WAA8B,GAAAL,SAAA,CAAAC,MAAA,OAAAD,SAAA,MAAAE,SAAA;EAChC,MAAMI,MAAM,GAAGb,eAAe,CAACI,KAAK,EAAE,OAAO,EAAE,WAAW,EAAE,SAAS,CAAC;EACtE,MAAMU,WAAW,GACbd,eAAe,CAACK,UAAU,EAAE,YAAY,EAAE,WAAW,EAAE,SAAS,CAAC;EAErEJ,IAAI,CAACc,MAAM,CACPF,MAAM,CAACG,IAAI,KAAK,CAAC,EACjB,MAAM,2CAA2C,mBAAAC,MAAA,CAC7BJ,MAAM,CAACG,IAAI,MAAG,CAAC;EAEvCf,IAAI,CAACc,MAAM,CACPD,WAAW,CAACE,IAAI,KAAK,CAAC,KACjBF,WAAW,CAACI,KAAK,CAAC,CAAC,CAAC,KAAKL,MAAM,CAACK,KAAK,CAAC,CAAC,CAAC,IACxCJ,WAAW,CAACI,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,IAC5BJ,WAAW,CAACI,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,EAC9B,wEAAwE,CAAC;EAE7EjB,IAAI,CAACc,MAAM,CACPH,WAAW,IAAI,IAAI,IAAIA,WAAW,CAACJ,MAAM,KAAK,CAAC,EAC/C,MACI,mEAAmE,cAAAS,MAAA,CACxDL,WAAW,MAAG,CAAC;EAElC,MAAMO,MAAM,GAAoB;IAACf,KAAK,EAAES,MAAM;IAAER,UAAU,EAAES;EAAW,CAAC;EACxE,MAAMM,KAAK,GACU;IAACd,aAAa;IAAEI,QAAQ;IAAEC,SAAS;IAAEC;EAAW,CAAC;EAEtE,OAAOd,MAAM,CAACuB,SAAS,CACnBtB,SAAS,EAAEoB,MAAmC,EAC9CC,KAAgC,CAAC;AACvC;AAEA,OAAO,MAAME,SAAS,GAAG,eAAgBpB,EAAE,CAAC;EAACC;AAAU,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}