{"ast": null, "code": "/*\n Copyright (c) 2014, <PERSON><PERSON><PERSON>\n\n Permission is hereby granted, free of charge, to any person obtaining a copy\n of this software and associated documentation files (the \"Software\"), to deal\n in the Software without restriction, including without limitation the rights\n to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n copies of the Software, and to permit persons to whom the Software is\n furnished to do so, subject to the following conditions:\n\n The above copyright notice and this permission notice shall be included in\n all copies or substantial portions of the Software.\n\n THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\n THE SOFTWARE.\n */\n\n'use strict';\n\n/**\n * <PERSON>move commonly used diacritic marks from a string as these\n * are not used in a consistent manner. Leave only ä, ö, ü.\n */\nconst removeDiacritics = function (text) {\n  text = text.replace('à', 'a');\n  text = text.replace('À', 'A');\n  text = text.replace('á', 'a');\n  text = text.replace('Á', 'A');\n  text = text.replace('â', 'a');\n  text = text.replace('Â', 'A');\n  text = text.replace('ç', 'c');\n  text = text.replace('Ç', 'C');\n  text = text.replace('è', 'e');\n  text = text.replace('È', 'E');\n  text = text.replace('é', 'e');\n  text = text.replace('É', 'E');\n  text = text.replace('ê', 'e');\n  text = text.replace('Ê', 'E');\n  text = text.replace('î', 'i');\n  text = text.replace('Î', 'I');\n  text = text.replace('ñ', 'n');\n  text = text.replace('Ñ', 'N');\n  text = text.replace('ó', 'o');\n  text = text.replace('Ó', 'O');\n  text = text.replace('ô', 'o');\n  text = text.replace('Ô', 'O');\n  text = text.replace('û', 'u');\n  text = text.replace('Û', 'U');\n  text = text.replace('š', 's');\n  text = text.replace('Š', 'S');\n  return text;\n};\n\n// export the relevant stuff.\nexports.removeDiacritics = removeDiacritics;", "map": {"version": 3, "names": ["removeDiacritics", "text", "replace", "exports"], "sources": ["C:/tmsft/node_modules/natural/lib/natural/normalizers/normalizer_no.js"], "sourcesContent": ["/*\n Copyright (c) 2014, <PERSON><PERSON><PERSON>\n\n Permission is hereby granted, free of charge, to any person obtaining a copy\n of this software and associated documentation files (the \"Software\"), to deal\n in the Software without restriction, including without limitation the rights\n to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n copies of the Software, and to permit persons to whom the Software is\n furnished to do so, subject to the following conditions:\n\n The above copyright notice and this permission notice shall be included in\n all copies or substantial portions of the Software.\n\n THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\n THE SOFTWARE.\n */\n\n'use strict'\n\n/**\n * <PERSON>move commonly used diacritic marks from a string as these\n * are not used in a consistent manner. Leave only ä, ö, ü.\n */\nconst removeDiacritics = function (text) {\n  text = text.replace('à', 'a')\n  text = text.replace('À', 'A')\n  text = text.replace('á', 'a')\n  text = text.replace('Á', 'A')\n  text = text.replace('â', 'a')\n  text = text.replace('Â', 'A')\n  text = text.replace('ç', 'c')\n  text = text.replace('Ç', 'C')\n  text = text.replace('è', 'e')\n  text = text.replace('È', 'E')\n  text = text.replace('é', 'e')\n  text = text.replace('É', 'E')\n  text = text.replace('ê', 'e')\n  text = text.replace('Ê', 'E')\n  text = text.replace('î', 'i')\n  text = text.replace('Î', 'I')\n  text = text.replace('ñ', 'n')\n  text = text.replace('Ñ', 'N')\n  text = text.replace('ó', 'o')\n  text = text.replace('Ó', 'O')\n  text = text.replace('ô', 'o')\n  text = text.replace('Ô', 'O')\n  text = text.replace('û', 'u')\n  text = text.replace('Û', 'U')\n  text = text.replace('š', 's')\n  text = text.replace('Š', 'S')\n\n  return text\n}\n\n// export the relevant stuff.\nexports.removeDiacritics = removeDiacritics\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,YAAY;;AAEZ;AACA;AACA;AACA;AACA,MAAMA,gBAAgB,GAAG,SAAAA,CAAUC,IAAI,EAAE;EACvCA,IAAI,GAAGA,IAAI,CAACC,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC;EAC7BD,IAAI,GAAGA,IAAI,CAACC,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC;EAC7BD,IAAI,GAAGA,IAAI,CAACC,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC;EAC7BD,IAAI,GAAGA,IAAI,CAACC,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC;EAC7BD,IAAI,GAAGA,IAAI,CAACC,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC;EAC7BD,IAAI,GAAGA,IAAI,CAACC,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC;EAC7BD,IAAI,GAAGA,IAAI,CAACC,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC;EAC7BD,IAAI,GAAGA,IAAI,CAACC,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC;EAC7BD,IAAI,GAAGA,IAAI,CAACC,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC;EAC7BD,IAAI,GAAGA,IAAI,CAACC,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC;EAC7BD,IAAI,GAAGA,IAAI,CAACC,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC;EAC7BD,IAAI,GAAGA,IAAI,CAACC,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC;EAC7BD,IAAI,GAAGA,IAAI,CAACC,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC;EAC7BD,IAAI,GAAGA,IAAI,CAACC,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC;EAC7BD,IAAI,GAAGA,IAAI,CAACC,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC;EAC7BD,IAAI,GAAGA,IAAI,CAACC,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC;EAC7BD,IAAI,GAAGA,IAAI,CAACC,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC;EAC7BD,IAAI,GAAGA,IAAI,CAACC,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC;EAC7BD,IAAI,GAAGA,IAAI,CAACC,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC;EAC7BD,IAAI,GAAGA,IAAI,CAACC,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC;EAC7BD,IAAI,GAAGA,IAAI,CAACC,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC;EAC7BD,IAAI,GAAGA,IAAI,CAACC,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC;EAC7BD,IAAI,GAAGA,IAAI,CAACC,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC;EAC7BD,IAAI,GAAGA,IAAI,CAACC,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC;EAC7BD,IAAI,GAAGA,IAAI,CAACC,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC;EAC7BD,IAAI,GAAGA,IAAI,CAACC,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC;EAE7B,OAAOD,IAAI;AACb,CAAC;;AAED;AACAE,OAAO,CAACH,gBAAgB,GAAGA,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}