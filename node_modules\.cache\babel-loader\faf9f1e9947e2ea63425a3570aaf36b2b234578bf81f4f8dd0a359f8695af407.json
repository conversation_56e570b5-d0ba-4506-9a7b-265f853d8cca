{"ast": null, "code": "/**\n * @license\n * Copyright 2019 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { env } from './environment';\nimport { getGlobal } from './global_util';\nimport * as log from './log';\nconst kernelRegistry = getGlobal('kernelRegistry', () => new Map());\nconst gradRegistry = getGlobal('gradRegistry', () => new Map());\n/**\n * Returns the kernel function (code) associated with the provided names.\n *\n * @param kernelName The official name of the kernel.\n * @param backendName The official name of the backend.\n */\nexport function getKernel(kernelName, backendName) {\n  const key = makeKey(kernelName, backendName);\n  return kernelRegistry.get(key);\n}\n/**\n * Returns the registered gradient info associated with the provided kernel.\n * @param kernelName The official TF kernel name.\n */\nexport function getGradient(kernelName) {\n  return gradRegistry.get(kernelName);\n}\nexport function getKernelsForBackend(backendName) {\n  const it = kernelRegistry.entries();\n  const result = [];\n  while (true) {\n    const {\n      done,\n      value\n    } = it.next();\n    if (done) {\n      break;\n    }\n    const [key, config] = value;\n    const [backend] = key.split('_');\n    if (backend === backendName) {\n      result.push(config);\n    }\n  }\n  return result;\n}\n/**\n * Registers the function (forward pass) for the kernel in a global registry.\n *\n * @param config A config object with the following properties:\n * - `kernelName` The official name of the kernel.\n * - `backendName` The official name of the backend.\n * - `kernelFunc` The function to run during the forward pass of the kernel.\n * - `setupFunc` Optional. Gets called once, after the backend initializes.\n * - `disposeFunc` Optional. Gets called once, right before the backend is\n * disposed.\n */\nexport function registerKernel(config) {\n  const {\n    kernelName,\n    backendName\n  } = config;\n  const key = makeKey(kernelName, backendName);\n  if (kernelRegistry.has(key)) {\n    log.warn(`The kernel '${kernelName}' for backend ` + `'${backendName}' is already registered`);\n  }\n  kernelRegistry.set(key, config);\n}\n/**\n * Registers a gradient function for a given kernel in the global registry,\n * to be used during the back-propagation of that kernel.\n *\n * @param config An object with the following properties:\n * - `kernelName` The name of the kernel that the gradient function is for.\n * - `gradFunc` The function to run during back-propagation.\n */\nexport function registerGradient(config) {\n  const {\n    kernelName\n  } = config;\n  if (gradRegistry.has(kernelName)) {\n    // TODO (yassogba) after 3.0 assess whether we need to keep this gated\n    // to debug mode.\n    if (env().getBool('DEBUG')) {\n      log.warn(`Overriding the gradient for '${kernelName}'`);\n    }\n  }\n  gradRegistry.set(kernelName, config);\n}\n/**\n * Removes the kernel function from the registry.\n *\n * @param kernelName The official name of the kernel.\n * @param backendName The official name of the backend.\n *\n */\nexport function unregisterKernel(kernelName, backendName) {\n  const key = makeKey(kernelName, backendName);\n  if (!kernelRegistry.has(key)) {\n    throw new Error(`The kernel '${kernelName}' for backend ` + `'${backendName}' is not registered`);\n  }\n  kernelRegistry.delete(key);\n}\n/** Removes the registered gradient from the global registry. */\nexport function unregisterGradient(kernelName) {\n  if (!gradRegistry.has(kernelName)) {\n    throw new Error(`The gradient '${kernelName}' for backend is not registered`);\n  }\n  gradRegistry.delete(kernelName);\n}\n/**\n * Finds kernels that have already been registered to a backend and re-registers\n * them for a new backend. Useful for registering custom backends.\n * @param registeredBackendName Already registered backend.\n * @param newBackendName New backend.\n */\nexport function copyRegisteredKernels(registeredBackendName, newBackendName) {\n  const kernels = getKernelsForBackend(registeredBackendName);\n  kernels.forEach(kernelConfig => {\n    const newKernelConfig = Object.assign({}, kernelConfig, {\n      backendName: newBackendName\n    });\n    registerKernel(newKernelConfig);\n  });\n}\nfunction makeKey(kernelName, backendName) {\n  return `${backendName}_${kernelName}`;\n}", "map": {"version": 3, "names": ["env", "getGlobal", "log", "kernelRegistry", "Map", "gradRegistry", "getKernel", "kernelName", "backendName", "key", "<PERSON><PERSON><PERSON>", "get", "getGradient", "getKernelsForBackend", "it", "entries", "result", "done", "value", "next", "config", "backend", "split", "push", "registerKernel", "has", "warn", "set", "registerGradient", "getBool", "unregisterKernel", "Error", "delete", "unregisterGradient", "copyRegisteredKernels", "registeredBackendName", "newBackendName", "kernels", "for<PERSON>ach", "kernelConfig", "newKernelConfig", "Object", "assign"], "sources": ["C:\\tfjs-core\\src\\kernel_registry.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2019 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport {env} from './environment';\nimport {getGlobal} from './global_util';\nimport * as log from './log';\nimport {NamedGradientMap} from './tape';\nimport {Tensor} from './tensor';\nimport {TensorInfo} from './tensor_info';\nimport {RecursiveArray} from './types';\n\nconst kernelRegistry =\n  getGlobal('kernelRegistry', () => new Map<`${string}_${string}`,\n    KernelConfig>());\nconst gradRegistry =\n  getGlobal('gradRegistry', () => new Map<string, GradConfig>());\n\ntype AttributeValue =\n  number | number[] | boolean | boolean[] | string | string[] | NamedAttrMap;\n\n/** These are extra non-tensor/primitive params passed to kernel functions. */\nexport type Attribute = AttributeValue | RecursiveArray<AttributeValue>;\n\n/** Specifies the code to run when executing a kernel. */\nexport type KernelFunc = (params: {\n  inputs: NamedTensorInfoMap,\n  backend: {},\n  attrs?: NamedAttrMap,\n}) => TensorInfo | TensorInfo[];\n\n/** The function to run when computing a gradient during backprop. */\nexport type GradFunc =\n  (dy: Tensor | Tensor[], saved: Tensor[], attrs: NamedAttrMap) =>\n    NamedGradientMap;\n\n/** Function that gets called after the backend initializes. */\nexport type KernelSetupFunc = (backend: {}) => void;\n/** Function that gets called right before the backend is disposed. */\nexport type KernelDisposeFunc = KernelSetupFunc;\n\n/** Config object for registering a kernel in the global registry. */\nexport interface KernelConfig {\n  kernelName: string;\n  backendName: string;\n  kernelFunc: KernelFunc;\n  setupFunc?: KernelSetupFunc;\n  disposeFunc?: KernelDisposeFunc;\n}\n\n/** Config object for registering a gradient in the global registry. */\nexport interface GradConfig {\n  kernelName: string;\n  inputsToSave?: string[];\n  // When saveAllInputs is true, all inputs will be saved. Only use this flag\n  // if inputs is an array of Tensors.\n  saveAllInputs?: boolean;\n  outputsToSave?: boolean[];\n  gradFunc: GradFunc;\n}\n\nexport interface NamedTensorInfoMap {\n  [name: string]: TensorInfo|undefined;\n}\n\nexport interface NamedAttrMap {\n  [name: string]: Attribute;\n}\n\n/**\n * Returns the kernel function (code) associated with the provided names.\n *\n * @param kernelName The official name of the kernel.\n * @param backendName The official name of the backend.\n */\nexport function getKernel(\n    kernelName: string, backendName: string): KernelConfig {\n  const key = makeKey(kernelName, backendName);\n  return kernelRegistry.get(key);\n}\n\n/**\n * Returns the registered gradient info associated with the provided kernel.\n * @param kernelName The official TF kernel name.\n */\nexport function getGradient(kernelName: string): GradConfig {\n  return gradRegistry.get(kernelName);\n}\n\nexport function getKernelsForBackend(backendName: string): KernelConfig[] {\n  const it = kernelRegistry.entries();\n  const result: KernelConfig[] = [];\n\n  while (true) {\n    const {done, value} = it.next();\n    if (done) {\n      break;\n    }\n    const [key, config] = value;\n    const [backend, ] = key.split('_');\n    if (backend === backendName) {\n      result.push(config);\n    }\n  }\n  return result;\n}\n\n/**\n * Registers the function (forward pass) for the kernel in a global registry.\n *\n * @param config A config object with the following properties:\n * - `kernelName` The official name of the kernel.\n * - `backendName` The official name of the backend.\n * - `kernelFunc` The function to run during the forward pass of the kernel.\n * - `setupFunc` Optional. Gets called once, after the backend initializes.\n * - `disposeFunc` Optional. Gets called once, right before the backend is\n * disposed.\n */\nexport function registerKernel(config: KernelConfig) {\n  const {kernelName, backendName} = config;\n  const key = makeKey(kernelName, backendName);\n  if (kernelRegistry.has(key)) {\n    log.warn(\n        `The kernel '${kernelName}' for backend ` +\n        `'${backendName}' is already registered`);\n  }\n  kernelRegistry.set(key, config);\n}\n\n/**\n * Registers a gradient function for a given kernel in the global registry,\n * to be used during the back-propagation of that kernel.\n *\n * @param config An object with the following properties:\n * - `kernelName` The name of the kernel that the gradient function is for.\n * - `gradFunc` The function to run during back-propagation.\n */\nexport function registerGradient(config: GradConfig) {\n  const {kernelName} = config;\n\n  if (gradRegistry.has(kernelName)) {\n    // TODO (yassogba) after 3.0 assess whether we need to keep this gated\n    // to debug mode.\n    if (env().getBool('DEBUG')) {\n      log.warn(`Overriding the gradient for '${kernelName}'`);\n    }\n  }\n  gradRegistry.set(kernelName, config);\n}\n\n/**\n * Removes the kernel function from the registry.\n *\n * @param kernelName The official name of the kernel.\n * @param backendName The official name of the backend.\n *\n */\nexport function unregisterKernel(\n    kernelName: string, backendName: string): void {\n  const key = makeKey(kernelName, backendName);\n  if (!kernelRegistry.has(key)) {\n    throw new Error(\n        `The kernel '${kernelName}' for backend ` +\n        `'${backendName}' is not registered`);\n  }\n  kernelRegistry.delete(key);\n}\n\n/** Removes the registered gradient from the global registry. */\nexport function unregisterGradient(kernelName: string): void {\n  if (!gradRegistry.has(kernelName)) {\n    throw new Error(\n        `The gradient '${kernelName}' for backend is not registered`);\n  }\n  gradRegistry.delete(kernelName);\n}\n\n/**\n * Finds kernels that have already been registered to a backend and re-registers\n * them for a new backend. Useful for registering custom backends.\n * @param registeredBackendName Already registered backend.\n * @param newBackendName New backend.\n */\nexport function copyRegisteredKernels(\n    registeredBackendName: string, newBackendName: string): void {\n  const kernels = getKernelsForBackend(registeredBackendName);\n  kernels.forEach(kernelConfig => {\n    const newKernelConfig =\n        Object.assign({}, kernelConfig, {backendName: newBackendName});\n    registerKernel(newKernelConfig);\n  });\n}\n\nfunction makeKey(kernelName: string,\n                 backendName: string): `${string}_${string}` {\n  return `${backendName}_${kernelName}`;\n}\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAgBA,SAAQA,GAAG,QAAO,eAAe;AACjC,SAAQC,SAAS,QAAO,eAAe;AACvC,OAAO,KAAKC,GAAG,MAAM,OAAO;AAM5B,MAAMC,cAAc,GAClBF,SAAS,CAAC,gBAAgB,EAAE,MAAM,IAAIG,GAAG,EACxB,CAAC;AACpB,MAAMC,YAAY,GAChBJ,SAAS,CAAC,cAAc,EAAE,MAAM,IAAIG,GAAG,EAAsB,CAAC;AAqDhE;;;;;;AAMA,OAAM,SAAUE,SAASA,CACrBC,UAAkB,EAAEC,WAAmB;EACzC,MAAMC,GAAG,GAAGC,OAAO,CAACH,UAAU,EAAEC,WAAW,CAAC;EAC5C,OAAOL,cAAc,CAACQ,GAAG,CAACF,GAAG,CAAC;AAChC;AAEA;;;;AAIA,OAAM,SAAUG,WAAWA,CAACL,UAAkB;EAC5C,OAAOF,YAAY,CAACM,GAAG,CAACJ,UAAU,CAAC;AACrC;AAEA,OAAM,SAAUM,oBAAoBA,CAACL,WAAmB;EACtD,MAAMM,EAAE,GAAGX,cAAc,CAACY,OAAO,EAAE;EACnC,MAAMC,MAAM,GAAmB,EAAE;EAEjC,OAAO,IAAI,EAAE;IACX,MAAM;MAACC,IAAI;MAAEC;IAAK,CAAC,GAAGJ,EAAE,CAACK,IAAI,EAAE;IAC/B,IAAIF,IAAI,EAAE;MACR;;IAEF,MAAM,CAACR,GAAG,EAAEW,MAAM,CAAC,GAAGF,KAAK;IAC3B,MAAM,CAACG,OAAO,CAAG,GAAGZ,GAAG,CAACa,KAAK,CAAC,GAAG,CAAC;IAClC,IAAID,OAAO,KAAKb,WAAW,EAAE;MAC3BQ,MAAM,CAACO,IAAI,CAACH,MAAM,CAAC;;;EAGvB,OAAOJ,MAAM;AACf;AAEA;;;;;;;;;;;AAWA,OAAM,SAAUQ,cAAcA,CAACJ,MAAoB;EACjD,MAAM;IAACb,UAAU;IAAEC;EAAW,CAAC,GAAGY,MAAM;EACxC,MAAMX,GAAG,GAAGC,OAAO,CAACH,UAAU,EAAEC,WAAW,CAAC;EAC5C,IAAIL,cAAc,CAACsB,GAAG,CAAChB,GAAG,CAAC,EAAE;IAC3BP,GAAG,CAACwB,IAAI,CACJ,eAAenB,UAAU,gBAAgB,GACzC,IAAIC,WAAW,yBAAyB,CAAC;;EAE/CL,cAAc,CAACwB,GAAG,CAAClB,GAAG,EAAEW,MAAM,CAAC;AACjC;AAEA;;;;;;;;AAQA,OAAM,SAAUQ,gBAAgBA,CAACR,MAAkB;EACjD,MAAM;IAACb;EAAU,CAAC,GAAGa,MAAM;EAE3B,IAAIf,YAAY,CAACoB,GAAG,CAAClB,UAAU,CAAC,EAAE;IAChC;IACA;IACA,IAAIP,GAAG,EAAE,CAAC6B,OAAO,CAAC,OAAO,CAAC,EAAE;MAC1B3B,GAAG,CAACwB,IAAI,CAAC,gCAAgCnB,UAAU,GAAG,CAAC;;;EAG3DF,YAAY,CAACsB,GAAG,CAACpB,UAAU,EAAEa,MAAM,CAAC;AACtC;AAEA;;;;;;;AAOA,OAAM,SAAUU,gBAAgBA,CAC5BvB,UAAkB,EAAEC,WAAmB;EACzC,MAAMC,GAAG,GAAGC,OAAO,CAACH,UAAU,EAAEC,WAAW,CAAC;EAC5C,IAAI,CAACL,cAAc,CAACsB,GAAG,CAAChB,GAAG,CAAC,EAAE;IAC5B,MAAM,IAAIsB,KAAK,CACX,eAAexB,UAAU,gBAAgB,GACzC,IAAIC,WAAW,qBAAqB,CAAC;;EAE3CL,cAAc,CAAC6B,MAAM,CAACvB,GAAG,CAAC;AAC5B;AAEA;AACA,OAAM,SAAUwB,kBAAkBA,CAAC1B,UAAkB;EACnD,IAAI,CAACF,YAAY,CAACoB,GAAG,CAAClB,UAAU,CAAC,EAAE;IACjC,MAAM,IAAIwB,KAAK,CACX,iBAAiBxB,UAAU,iCAAiC,CAAC;;EAEnEF,YAAY,CAAC2B,MAAM,CAACzB,UAAU,CAAC;AACjC;AAEA;;;;;;AAMA,OAAM,SAAU2B,qBAAqBA,CACjCC,qBAA6B,EAAEC,cAAsB;EACvD,MAAMC,OAAO,GAAGxB,oBAAoB,CAACsB,qBAAqB,CAAC;EAC3DE,OAAO,CAACC,OAAO,CAACC,YAAY,IAAG;IAC7B,MAAMC,eAAe,GACjBC,MAAM,CAACC,MAAM,CAAC,EAAE,EAAEH,YAAY,EAAE;MAAC/B,WAAW,EAAE4B;IAAc,CAAC,CAAC;IAClEZ,cAAc,CAACgB,eAAe,CAAC;EACjC,CAAC,CAAC;AACJ;AAEA,SAAS9B,OAAOA,CAACH,UAAkB,EAClBC,WAAmB;EAClC,OAAO,GAAGA,WAAW,IAAID,UAAU,EAAE;AACvC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}