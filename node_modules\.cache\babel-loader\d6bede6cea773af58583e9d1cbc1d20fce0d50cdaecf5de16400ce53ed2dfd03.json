{"ast": null, "code": "/*\nCopyright (c) 2011, <PERSON>\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in\nall copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\nTHE SOFTWARE.\n*/\n\nconst SingularPluralInflector = require('./singular_plural_inflector');\nconst FormSet = require('./form_set');\nclass VerbInflector extends SingularPluralInflector {\n  constructor() {\n    super();\n    this.ambiguous = ['will'];\n\n    // this.attach = attach\n\n    this.customPluralForms = [];\n    this.customSingularForms = [];\n    this.singularForms = new FormSet();\n    this.pluralForms = new FormSet();\n    this.addIrregular('am', 'are');\n    this.addIrregular('is', 'are');\n    this.addIrregular('was', 'were');\n    this.addIrregular('has', 'have');\n    this.singularForms.regularForms.push([/ed$/i, 'ed']);\n    this.singularForms.regularForms.push([/ss$/i, 'sses']);\n    this.singularForms.regularForms.push([/x$/i, 'xes']);\n    this.singularForms.regularForms.push([/(h|z|o)$/i, '$1es']);\n    this.singularForms.regularForms.push([/$zz/i, 'zzes']);\n    this.singularForms.regularForms.push([/([^a|e|i|o|u])y$/i, '$1ies']);\n    this.singularForms.regularForms.push([/$/i, 's']);\n    this.pluralForms.regularForms.push([/sses$/i, 'ss']);\n    this.pluralForms.regularForms.push([/xes$/i, 'x']);\n    this.pluralForms.regularForms.push([/([cs])hes$/i, '$1h']);\n    this.pluralForms.regularForms.push([/zzes$/i, 'zz']);\n    this.pluralForms.regularForms.push([/([^h|z|o|i])es$/i, '$1e']);\n    this.pluralForms.regularForms.push([/ies$/i, 'y']); // flies->fly\n    this.pluralForms.regularForms.push([/e?s$/i, '']);\n  }\n}\nmodule.exports = VerbInflector;", "map": {"version": 3, "names": ["SingularPluralInflector", "require", "FormSet", "VerbInflector", "constructor", "ambiguous", "customPluralForms", "customSingularForms", "singularForms", "pluralForms", "addIrregular", "regularForms", "push", "module", "exports"], "sources": ["C:/tmsft/node_modules/natural/lib/natural/inflectors/present_verb_inflector.js"], "sourcesContent": ["/*\nCopyright (c) 2011, <PERSON>\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in\nall copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\nTHE SOFTWARE.\n*/\n\nconst SingularPluralInflector = require('./singular_plural_inflector')\nconst FormSet = require('./form_set')\n\nclass VerbInflector extends SingularPluralInflector {\n  constructor () {\n    super()\n    this.ambiguous = [\n      'will'\n    ]\n\n    // this.attach = attach\n\n    this.customPluralForms = []\n    this.customSingularForms = []\n    this.singularForms = new FormSet()\n    this.pluralForms = new FormSet()\n\n    this.addIrregular('am', 'are')\n    this.addIrregular('is', 'are')\n    this.addIrregular('was', 'were')\n    this.addIrregular('has', 'have')\n\n    this.singularForms.regularForms.push([/ed$/i, 'ed'])\n    this.singularForms.regularForms.push([/ss$/i, 'sses'])\n    this.singularForms.regularForms.push([/x$/i, 'xes'])\n    this.singularForms.regularForms.push([/(h|z|o)$/i, '$1es'])\n    this.singularForms.regularForms.push([/$zz/i, 'zzes'])\n    this.singularForms.regularForms.push([/([^a|e|i|o|u])y$/i, '$1ies'])\n    this.singularForms.regularForms.push([/$/i, 's'])\n\n    this.pluralForms.regularForms.push([/sses$/i, 'ss'])\n    this.pluralForms.regularForms.push([/xes$/i, 'x'])\n    this.pluralForms.regularForms.push([/([cs])hes$/i, '$1h'])\n    this.pluralForms.regularForms.push([/zzes$/i, 'zz'])\n    this.pluralForms.regularForms.push([/([^h|z|o|i])es$/i, '$1e'])\n    this.pluralForms.regularForms.push([/ies$/i, 'y'])// flies->fly\n    this.pluralForms.regularForms.push([/e?s$/i, ''])\n  }\n}\n\nmodule.exports = VerbInflector\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,MAAMA,uBAAuB,GAAGC,OAAO,CAAC,6BAA6B,CAAC;AACtE,MAAMC,OAAO,GAAGD,OAAO,CAAC,YAAY,CAAC;AAErC,MAAME,aAAa,SAASH,uBAAuB,CAAC;EAClDI,WAAWA,CAAA,EAAI;IACb,KAAK,CAAC,CAAC;IACP,IAAI,CAACC,SAAS,GAAG,CACf,MAAM,CACP;;IAED;;IAEA,IAAI,CAACC,iBAAiB,GAAG,EAAE;IAC3B,IAAI,CAACC,mBAAmB,GAAG,EAAE;IAC7B,IAAI,CAACC,aAAa,GAAG,IAAIN,OAAO,CAAC,CAAC;IAClC,IAAI,CAACO,WAAW,GAAG,IAAIP,OAAO,CAAC,CAAC;IAEhC,IAAI,CAACQ,YAAY,CAAC,IAAI,EAAE,KAAK,CAAC;IAC9B,IAAI,CAACA,YAAY,CAAC,IAAI,EAAE,KAAK,CAAC;IAC9B,IAAI,CAACA,YAAY,CAAC,KAAK,EAAE,MAAM,CAAC;IAChC,IAAI,CAACA,YAAY,CAAC,KAAK,EAAE,MAAM,CAAC;IAEhC,IAAI,CAACF,aAAa,CAACG,YAAY,CAACC,IAAI,CAAC,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;IACpD,IAAI,CAACJ,aAAa,CAACG,YAAY,CAACC,IAAI,CAAC,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;IACtD,IAAI,CAACJ,aAAa,CAACG,YAAY,CAACC,IAAI,CAAC,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;IACpD,IAAI,CAACJ,aAAa,CAACG,YAAY,CAACC,IAAI,CAAC,CAAC,WAAW,EAAE,MAAM,CAAC,CAAC;IAC3D,IAAI,CAACJ,aAAa,CAACG,YAAY,CAACC,IAAI,CAAC,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;IACtD,IAAI,CAACJ,aAAa,CAACG,YAAY,CAACC,IAAI,CAAC,CAAC,mBAAmB,EAAE,OAAO,CAAC,CAAC;IACpE,IAAI,CAACJ,aAAa,CAACG,YAAY,CAACC,IAAI,CAAC,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;IAEjD,IAAI,CAACH,WAAW,CAACE,YAAY,CAACC,IAAI,CAAC,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;IACpD,IAAI,CAACH,WAAW,CAACE,YAAY,CAACC,IAAI,CAAC,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC;IAClD,IAAI,CAACH,WAAW,CAACE,YAAY,CAACC,IAAI,CAAC,CAAC,aAAa,EAAE,KAAK,CAAC,CAAC;IAC1D,IAAI,CAACH,WAAW,CAACE,YAAY,CAACC,IAAI,CAAC,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;IACpD,IAAI,CAACH,WAAW,CAACE,YAAY,CAACC,IAAI,CAAC,CAAC,kBAAkB,EAAE,KAAK,CAAC,CAAC;IAC/D,IAAI,CAACH,WAAW,CAACE,YAAY,CAACC,IAAI,CAAC,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC;IAClD,IAAI,CAACH,WAAW,CAACE,YAAY,CAACC,IAAI,CAAC,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC;EACnD;AACF;AAEAC,MAAM,CAACC,OAAO,GAAGX,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}