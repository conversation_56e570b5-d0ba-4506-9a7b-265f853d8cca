{"ast": null, "code": "import _objectSpread from\"C:/tmsft/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";import React,{useState,useCallback}from'react';import{FileUpload}from'./FileUpload';import{csvProcessingService}from'../services/csvProcessingService';import{bankAccountService}from'../services/bankAccountService';import{transactionStorageService}from'../services/transactionStorageService';import{fileStorageService}from'../services/fileStorageService';import{BalanceValidationDialog}from'./BalanceValidationDialog';import'./BankStatementImport.css';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";export const BankStatementImport=_ref=>{let{onImportComplete}=_ref;const[step,setStep]=useState('upload');const[files,setFiles]=useState([]);const[selectedBankAccount,setSelectedBankAccount]=useState(null);const[importSummaries,setImportSummaries]=useState([]);const[isProcessing,setIsProcessing]=useState(false);const[error,setError]=useState(null);const[bankAccounts]=useState(bankAccountService.getAllAccounts());// Balance validation state\nconst[balanceValidation,setBalanceValidation]=useState(null);const[showBalanceDialog,setShowBalanceDialog]=useState(false);const handleFilesSelected=useCallback(async selectedFiles=>{setFiles(selectedFiles);setIsProcessing(true);setError(null);try{const summaries=[];for(const file of selectedFiles){const summary=await csvProcessingService.processFile(file);summaries.push(summary);}setImportSummaries(summaries);setStep('selectBank');}catch(err){setError(err instanceof Error?err.message:'Failed to process files');}finally{setIsProcessing(false);}},[]);const handleBankAccountSelect=useCallback(accountId=>{const account=bankAccounts.find(acc=>acc.id===accountId);if(account){setSelectedBankAccount(account);// Perform balance validation\nconst allTransactions=importSummaries.flatMap(summary=>summary.transactions);const validation=transactionStorageService.validateBalance(accountId,allTransactions,account.currentBalance);setBalanceValidation(validation);if(!validation.isValid){setShowBalanceDialog(true);}else{setStep('review');}}},[bankAccounts,importSummaries]);const handleDownloadTemplate=useCallback(()=>{csvProcessingService.downloadTemplate();},[]);const handleTransactionEdit=useCallback((summaryIndex,transactionIndex,field,value)=>{setImportSummaries(prev=>{const updated=[...prev];updated[summaryIndex].transactions[transactionIndex]=_objectSpread(_objectSpread({},updated[summaryIndex].transactions[transactionIndex]),{},{[field]:value});return updated;});},[]);// Handle balance validation dialog\nconst handleBalanceValidationConfirm=useCallback(useImportBalance=>{if(!selectedBankAccount||!balanceValidation)return;setShowBalanceDialog(false);if(useImportBalance){// Update account balance to match import balance\nbankAccountService.updateBalance(selectedBankAccount.id,balanceValidation.actualBalance);// Update local state\nsetSelectedBankAccount(prev=>prev?_objectSpread(_objectSpread({},prev),{},{currentBalance:balanceValidation.actualBalance}):null);}setStep('review');},[selectedBankAccount,balanceValidation]);const handleBalanceValidationCancel=useCallback(()=>{setShowBalanceDialog(false);setSelectedBankAccount(null);setBalanceValidation(null);setStep('selectBank');},[]);const handleConfirmImport=useCallback(()=>{if(!selectedBankAccount)return;const allTransactions=importSummaries.flatMap(summary=>summary.transactions);// Store transactions in the system\ntransactionStorageService.storeTransactions(selectedBankAccount.id,allTransactions);// Track uploaded files\nfiles.forEach((file,index)=>{const summary=importSummaries[index];if(summary){try{fileStorageService.addUploadedFile({fileName:file.name,accountId:selectedBankAccount.id,accountName:selectedBankAccount.name,transactionCount:summary.totalTransactions,fileSize:file.size,checksum:\"\".concat(file.name,\"_\").concat(file.size,\"_\").concat(Date.now())});}catch(error){console.error('Error tracking uploaded file:',error);}}});// Update account balance to the most recent transaction balance (Post date + Time based)\nconst sortedTransactions=[...allTransactions].sort((a,b)=>{const dateTimeA=new Date(\"\".concat(a.postDate||a.date,\"T\").concat(a.time||'00:00'));const dateTimeB=new Date(\"\".concat(b.postDate||b.date,\"T\").concat(b.time||'00:00'));return dateTimeB.getTime()-dateTimeA.getTime();});if(sortedTransactions.length>0){const latestBalance=sortedTransactions[0].balance;bankAccountService.updateBalance(selectedBankAccount.id,latestBalance);}if(onImportComplete){onImportComplete(allTransactions,selectedBankAccount);}// Reset state\nsetStep('upload');setFiles([]);setSelectedBankAccount(null);setImportSummaries([]);setBalanceValidation(null);setShowBalanceDialog(false);},[selectedBankAccount,importSummaries,onImportComplete,files]);const handleCancel=useCallback(()=>{setStep('upload');setFiles([]);setSelectedBankAccount(null);setImportSummaries([]);setError(null);setBalanceValidation(null);setShowBalanceDialog(false);},[]);const formatCurrency=amount=>{return new Intl.NumberFormat('en-US',{style:'currency',currency:'USD'}).format(amount);};const totalTransactions=importSummaries.reduce((sum,summary)=>sum+summary.totalTransactions,0);const totalDebitAmount=importSummaries.reduce((sum,summary)=>sum+summary.totalDebitAmount,0);const totalCreditAmount=importSummaries.reduce((sum,summary)=>sum+summary.totalCreditAmount,0);const totalValidationErrors=importSummaries.reduce((sum,summary)=>sum+summary.validationErrors.length,0);return/*#__PURE__*/_jsxs(\"div\",{className:\"bank-statement-import\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"import-header\",children:[/*#__PURE__*/_jsx(\"h2\",{className:\"import-title\",children:\"Bank Statement Import\"}),/*#__PURE__*/_jsx(\"p\",{className:\"import-description\",children:\"Import CSV bank statements to process transactions automatically\"})]}),step==='upload'&&/*#__PURE__*/_jsxs(\"div\",{className:\"import-step\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"step-header\",children:[/*#__PURE__*/_jsx(\"h3\",{children:\"Step 1: Upload CSV Files\"}),/*#__PURE__*/_jsx(\"button\",{type:\"button\",onClick:handleDownloadTemplate,className:\"btn btn-secondary btn-sm\",children:\"Download CSV Template\"})]}),/*#__PURE__*/_jsx(FileUpload,{onFilesSelected:handleFilesSelected,disabled:isProcessing}),isProcessing&&/*#__PURE__*/_jsxs(\"div\",{className:\"processing-indicator\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"spinner\"}),/*#__PURE__*/_jsx(\"p\",{children:\"Processing files...\"})]}),error&&/*#__PURE__*/_jsx(\"div\",{className:\"error-message\",children:/*#__PURE__*/_jsx(\"p\",{children:error})})]}),step==='selectBank'&&/*#__PURE__*/_jsxs(\"div\",{className:\"import-step\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"step-header\",children:[/*#__PURE__*/_jsx(\"h3\",{children:\"Step 2: Select Bank Account\"}),/*#__PURE__*/_jsx(\"p\",{children:\"Choose which bank account these transactions belong to\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"bank-selection\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"form-group\",children:[/*#__PURE__*/_jsx(\"label\",{htmlFor:\"bank-account-select\",className:\"form-label\",children:\"Bank Account\"}),/*#__PURE__*/_jsxs(\"select\",{id:\"bank-account-select\",className:\"form-select\",onChange:e=>handleBankAccountSelect(e.target.value),defaultValue:\"\",children:[/*#__PURE__*/_jsx(\"option\",{value:\"\",children:\"Select a bank account...\"}),bankAccounts.map(account=>/*#__PURE__*/_jsxs(\"option\",{value:account.id,children:[account.name,\" - \",account.bankName,\" (\",account.accountNumber,\")\"]},account.id))]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"import-summary-preview\",children:[/*#__PURE__*/_jsx(\"h4\",{children:\"Import Summary\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"summary-stats\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"stat-item\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"stat-label\",children:\"Files:\"}),/*#__PURE__*/_jsx(\"span\",{className:\"stat-value\",children:files.length})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"stat-item\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"stat-label\",children:\"Total Transactions:\"}),/*#__PURE__*/_jsx(\"span\",{className:\"stat-value\",children:totalTransactions})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"stat-item\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"stat-label\",children:\"Total Debits:\"}),/*#__PURE__*/_jsx(\"span\",{className:\"stat-value text-error\",children:formatCurrency(totalDebitAmount)})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"stat-item\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"stat-label\",children:\"Total Credits:\"}),/*#__PURE__*/_jsx(\"span\",{className:\"stat-value text-success\",children:formatCurrency(totalCreditAmount)})]}),totalValidationErrors>0&&/*#__PURE__*/_jsxs(\"div\",{className:\"stat-item\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"stat-label\",children:\"Validation Errors:\"}),/*#__PURE__*/_jsx(\"span\",{className:\"stat-value text-warning\",children:totalValidationErrors})]})]})]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"step-actions\",children:/*#__PURE__*/_jsx(\"button\",{type:\"button\",onClick:handleCancel,className:\"btn btn-secondary\",children:\"Cancel\"})})]}),step==='review'&&selectedBankAccount&&/*#__PURE__*/_jsxs(\"div\",{className:\"import-step\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"step-header\",children:[/*#__PURE__*/_jsx(\"h3\",{children:\"Step 3: Review & Edit Transactions\"}),/*#__PURE__*/_jsx(\"p\",{children:\"Review the imported transactions and make any necessary adjustments\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"account-info\",children:[/*#__PURE__*/_jsx(\"h4\",{children:\"Selected Account\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"account-details\",children:[/*#__PURE__*/_jsx(\"p\",{children:/*#__PURE__*/_jsx(\"strong\",{children:selectedBankAccount.name})}),/*#__PURE__*/_jsxs(\"p\",{children:[selectedBankAccount.bankName,\" - \",selectedBankAccount.accountNumber]}),/*#__PURE__*/_jsxs(\"p\",{children:[\"Current Balance: \",formatCurrency(selectedBankAccount.currentBalance)]}),importSummaries.length>0&&balanceValidation&&/*#__PURE__*/_jsxs(\"div\",{className:\"balance-comparison\",children:[/*#__PURE__*/_jsxs(\"p\",{children:[\"Import Closing Balance: \",formatCurrency(balanceValidation.actualBalance)]}),!balanceValidation.isValid&&/*#__PURE__*/_jsxs(\"div\",{className:\"balance-warning\",children:[\"\\u26A0\\uFE0F Balance Validated: Expected \",formatCurrency(balanceValidation.expectedBalance),\", Got \",formatCurrency(balanceValidation.actualBalance),\"(Difference: \",formatCurrency(balanceValidation.difference),\")\"]}),balanceValidation.isValid&&/*#__PURE__*/_jsx(\"div\",{className:\"balance-success\",children:\"\\u2705 Balance validation passed - transactions are consistent with account history\"})]})]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"import-summaries\",children:importSummaries.map((summary,summaryIndex)=>/*#__PURE__*/_jsxs(\"div\",{className:\"import-summary-card\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"summary-header\",children:[/*#__PURE__*/_jsx(\"h4\",{children:summary.fileName}),/*#__PURE__*/_jsxs(\"div\",{className:\"summary-stats\",children:[/*#__PURE__*/_jsxs(\"span\",{children:[summary.totalTransactions,\" transactions\"]}),/*#__PURE__*/_jsxs(\"span\",{children:[\"Period: \",summary.dateRange.from,\" to \",summary.dateRange.to]})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"balance-summary\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"balance-row\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"balance-label\",children:\"Opening Balance:\"}),/*#__PURE__*/_jsx(\"span\",{className:\"balance-value\",children:formatCurrency(summary.openingBalance)})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"balance-row\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"balance-label\",children:\"Daily Movement:\"}),/*#__PURE__*/_jsxs(\"span\",{className:\"balance-value \".concat(summary.dailyMovement>=0?'text-success':'text-error'),children:[summary.dailyMovement>=0?'+':'',formatCurrency(summary.dailyMovement)]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"balance-row\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"balance-label\",children:\"Closing Balance:\"}),/*#__PURE__*/_jsx(\"span\",{className:\"balance-value\",children:formatCurrency(summary.closingBalance)})]})]}),summary.validationErrors.length>0&&/*#__PURE__*/_jsxs(\"div\",{className:\"validation-errors\",children:[/*#__PURE__*/_jsx(\"h5\",{children:\"Validation Errors\"}),/*#__PURE__*/_jsx(\"ul\",{children:summary.validationErrors.map((error,index)=>/*#__PURE__*/_jsxs(\"li\",{className:\"validation-error\",children:[\"Row \",error.row,\", \",error.field,\": \",error.message]},index))})]}),/*#__PURE__*/_jsx(\"div\",{className:\"transactions-table-container\",children:/*#__PURE__*/_jsxs(\"table\",{className:\"transactions-table\",children:[/*#__PURE__*/_jsx(\"thead\",{children:/*#__PURE__*/_jsxs(\"tr\",{children:[/*#__PURE__*/_jsx(\"th\",{children:\"Date\"}),/*#__PURE__*/_jsx(\"th\",{children:\"Description\"}),/*#__PURE__*/_jsx(\"th\",{children:\"Debit\"}),/*#__PURE__*/_jsx(\"th\",{children:\"Credit\"}),/*#__PURE__*/_jsx(\"th\",{children:\"Balance\"}),/*#__PURE__*/_jsx(\"th\",{children:\"Reference\"})]})}),/*#__PURE__*/_jsx(\"tbody\",{children:summary.transactions.map((transaction,transactionIndex)=>/*#__PURE__*/_jsxs(\"tr\",{children:[/*#__PURE__*/_jsx(\"td\",{children:/*#__PURE__*/_jsx(\"input\",{type:\"date\",value:transaction.date,onChange:e=>handleTransactionEdit(summaryIndex,transactionIndex,'date',e.target.value),className:\"form-input\"})}),/*#__PURE__*/_jsx(\"td\",{children:/*#__PURE__*/_jsx(\"input\",{type:\"text\",value:transaction.description,onChange:e=>handleTransactionEdit(summaryIndex,transactionIndex,'description',e.target.value),className:\"form-input\"})}),/*#__PURE__*/_jsx(\"td\",{children:/*#__PURE__*/_jsx(\"input\",{type:\"number\",step:\"0.01\",value:transaction.debitAmount?transaction.debitAmount.toFixed(2):'',onChange:e=>handleTransactionEdit(summaryIndex,transactionIndex,'debitAmount',parseFloat(e.target.value)||0),className:\"form-input\",placeholder:\"0.00\"})}),/*#__PURE__*/_jsx(\"td\",{children:/*#__PURE__*/_jsx(\"input\",{type:\"number\",step:\"0.01\",value:transaction.creditAmount?transaction.creditAmount.toFixed(2):'',onChange:e=>handleTransactionEdit(summaryIndex,transactionIndex,'creditAmount',parseFloat(e.target.value)||0),className:\"form-input\",placeholder:\"0.00\"})}),/*#__PURE__*/_jsx(\"td\",{children:/*#__PURE__*/_jsx(\"input\",{type:\"number\",step:\"0.01\",value:transaction.balance.toFixed(2),onChange:e=>handleTransactionEdit(summaryIndex,transactionIndex,'balance',parseFloat(e.target.value)||0),className:\"form-input\"})}),/*#__PURE__*/_jsx(\"td\",{children:/*#__PURE__*/_jsx(\"input\",{type:\"text\",value:transaction.reference||'',onChange:e=>handleTransactionEdit(summaryIndex,transactionIndex,'reference',e.target.value),className:\"form-input\"})})]},transaction.id))})]})})]},summaryIndex))}),/*#__PURE__*/_jsxs(\"div\",{className:\"step-actions\",children:[/*#__PURE__*/_jsx(\"button\",{type:\"button\",onClick:handleCancel,className:\"btn btn-secondary\",children:\"Cancel\"}),/*#__PURE__*/_jsx(\"button\",{type:\"button\",onClick:()=>setStep('confirm'),className:\"btn btn-primary\",disabled:totalValidationErrors>0,children:\"Proceed to Confirmation\"})]})]}),step==='confirm'&&selectedBankAccount&&/*#__PURE__*/_jsxs(\"div\",{className:\"import-step\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"step-header\",children:[/*#__PURE__*/_jsx(\"h3\",{children:\"Step 4: Confirm Import\"}),/*#__PURE__*/_jsx(\"p\",{children:\"Please review the final summary before proceeding with the import\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"confirmation-summary\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"summary-section\",children:[/*#__PURE__*/_jsx(\"h4\",{children:\"Account Information\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"account-details\",children:[/*#__PURE__*/_jsx(\"p\",{children:/*#__PURE__*/_jsx(\"strong\",{children:selectedBankAccount.name})}),/*#__PURE__*/_jsxs(\"p\",{children:[selectedBankAccount.bankName,\" - \",selectedBankAccount.accountNumber]}),/*#__PURE__*/_jsxs(\"p\",{children:[\"Current Balance: \",formatCurrency(selectedBankAccount.currentBalance)]})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"summary-section\",children:[/*#__PURE__*/_jsx(\"h4\",{children:\"Import Summary\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"summary-stats\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"stat-item\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"stat-label\",children:\"Files Processed:\"}),/*#__PURE__*/_jsx(\"span\",{className:\"stat-value\",children:files.length})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"stat-item\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"stat-label\",children:\"Total Transactions:\"}),/*#__PURE__*/_jsx(\"span\",{className:\"stat-value\",children:totalTransactions})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"stat-item\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"stat-label\",children:\"Total Debit Amount:\"}),/*#__PURE__*/_jsx(\"span\",{className:\"stat-value text-error\",children:formatCurrency(totalDebitAmount)})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"stat-item\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"stat-label\",children:\"Total Credit Amount:\"}),/*#__PURE__*/_jsx(\"span\",{className:\"stat-value text-success\",children:formatCurrency(totalCreditAmount)})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"stat-item\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"stat-label\",children:\"Net Change:\"}),/*#__PURE__*/_jsx(\"span\",{className:\"stat-value \".concat(totalCreditAmount-totalDebitAmount>=0?'text-success':'text-error'),children:formatCurrency(totalCreditAmount-totalDebitAmount)})]})]})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"confirmation-question\",children:[/*#__PURE__*/_jsx(\"h4\",{children:\"Do you want to proceed with this import?\"}),/*#__PURE__*/_jsx(\"p\",{children:\"This action will add all transactions to the selected bank account.\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"step-actions\",children:[/*#__PURE__*/_jsx(\"button\",{type:\"button\",onClick:handleCancel,className:\"btn btn-danger\",children:\"No, Cancel\"}),/*#__PURE__*/_jsx(\"button\",{type:\"button\",onClick:handleConfirmImport,className:\"btn btn-success btn-lg\",children:\"Yes, Proceed\"})]})]}),balanceValidation&&/*#__PURE__*/_jsx(BalanceValidationDialog,{isOpen:showBalanceDialog,validationResult:balanceValidation,onConfirm:handleBalanceValidationConfirm,onCancel:handleBalanceValidationCancel})]});};", "map": {"version": 3, "names": ["React", "useState", "useCallback", "FileUpload", "csvProcessingService", "bankAccountService", "transactionStorageService", "fileStorageService", "BalanceValidationDialog", "jsx", "_jsx", "jsxs", "_jsxs", "BankStatementImport", "_ref", "onImportComplete", "step", "setStep", "files", "setFiles", "selected<PERSON><PERSON>kAccount", "setSelectedBankAccount", "importSummaries", "setImportSummaries", "isProcessing", "setIsProcessing", "error", "setError", "bankAccounts", "getAllAccounts", "balanceValidation", "setBalanceValidation", "showBalanceDialog", "setShowBalanceDialog", "handleFilesSelected", "selectedFiles", "summaries", "file", "summary", "processFile", "push", "err", "Error", "message", "handleBankAccountSelect", "accountId", "account", "find", "acc", "id", "allTransactions", "flatMap", "transactions", "validation", "validateBalance", "currentBalance", "<PERSON><PERSON><PERSON><PERSON>", "handleDownloadTemplate", "downloadTemplate", "handleTransactionEdit", "summaryIndex", "transactionIndex", "field", "value", "prev", "updated", "_objectSpread", "handleBalanceValidationConfirm", "useImportBalance", "updateBalance", "actualBalance", "handleBalanceValidationCancel", "handleConfirmImport", "storeTransactions", "for<PERSON>ach", "index", "addUploadedFile", "fileName", "name", "accountName", "transactionCount", "totalTransactions", "fileSize", "size", "checksum", "concat", "Date", "now", "console", "sortedTransactions", "sort", "a", "b", "dateTimeA", "postDate", "date", "time", "dateTimeB", "getTime", "length", "latestBalance", "balance", "handleCancel", "formatCurrency", "amount", "Intl", "NumberFormat", "style", "currency", "format", "reduce", "sum", "totalDebitAmount", "totalCreditAmount", "totalValidationErrors", "validationErrors", "className", "children", "type", "onClick", "onFilesSelected", "disabled", "htmlFor", "onChange", "e", "target", "defaultValue", "map", "bankName", "accountNumber", "expectedBalance", "difference", "date<PERSON><PERSON><PERSON>", "from", "to", "openingBalance", "dailyMovement", "closingBalance", "row", "transaction", "description", "debitAmount", "toFixed", "parseFloat", "placeholder", "creditAmount", "reference", "isOpen", "validationResult", "onConfirm", "onCancel"], "sources": ["C:/tmsft/src/components/BankStatementImport.tsx"], "sourcesContent": ["import React, { useState, useCallback } from 'react';\nimport { FileUpload } from './FileUpload';\nimport { ImportSummary, BankAccount, Transaction } from '../types';\nimport { csvProcessingService } from '../services/csvProcessingService';\nimport { bankAccountService } from '../services/bankAccountService';\nimport { transactionStorageService, BalanceValidationResult } from '../services/transactionStorageService';\nimport { fileStorageService } from '../services/fileStorageService';\nimport { BalanceValidationDialog } from './BalanceValidationDialog';\nimport './BankStatementImport.css';\n\ninterface BankStatementImportProps {\n  onImportComplete?: (transactions: Transaction[], bankAccount: BankAccount) => void;\n}\n\nexport const BankStatementImport: React.FC<BankStatementImportProps> = ({\n  onImportComplete\n}) => {\n  const [step, setStep] = useState<'upload' | 'selectBank' | 'review' | 'confirm'>('upload');\n  const [files, setFiles] = useState<File[]>([]);\n  const [selectedBankAccount, setSelectedBankAccount] = useState<BankAccount | null>(null);\n  const [importSummaries, setImportSummaries] = useState<ImportSummary[]>([]);\n  const [isProcessing, setIsProcessing] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n  const [bankAccounts] = useState<BankAccount[]>(bankAccountService.getAllAccounts());\n  \n  // Balance validation state\n  const [balanceValidation, setBalanceValidation] = useState<BalanceValidationResult | null>(null);\n  const [showBalanceDialog, setShowBalanceDialog] = useState(false);\n\n  const handleFilesSelected = useCallback(async (selectedFiles: File[]) => {\n    setFiles(selectedFiles);\n    setIsProcessing(true);\n    setError(null);\n\n    try {\n      const summaries: ImportSummary[] = [];\n      \n      for (const file of selectedFiles) {\n        const summary = await csvProcessingService.processFile(file);\n        summaries.push(summary);\n      }\n      \n      setImportSummaries(summaries);\n      setStep('selectBank');\n    } catch (err) {\n      setError(err instanceof Error ? err.message : 'Failed to process files');\n    } finally {\n      setIsProcessing(false);\n    }\n  }, []);\n\n  const handleBankAccountSelect = useCallback((accountId: string) => {\n    const account = bankAccounts.find(acc => acc.id === accountId);\n    if (account) {\n      setSelectedBankAccount(account);\n      \n      // Perform balance validation\n      const allTransactions = importSummaries.flatMap(summary => summary.transactions);\n      const validation = transactionStorageService.validateBalance(\n        accountId, \n        allTransactions, \n        account.currentBalance\n      );\n      \n      setBalanceValidation(validation);\n      \n      if (!validation.isValid) {\n        setShowBalanceDialog(true);\n      } else {\n        setStep('review');\n      }\n    }\n  }, [bankAccounts, importSummaries]);\n\n  const handleDownloadTemplate = useCallback(() => {\n    csvProcessingService.downloadTemplate();\n  }, []);\n\n  const handleTransactionEdit = useCallback((\n    summaryIndex: number, \n    transactionIndex: number, \n    field: keyof Transaction, \n    value: string | number\n  ) => {\n    setImportSummaries(prev => {\n      const updated = [...prev];\n      updated[summaryIndex].transactions[transactionIndex] = {\n        ...updated[summaryIndex].transactions[transactionIndex],\n        [field]: value\n      };\n      return updated;\n    });\n  }, []);\n\n  // Handle balance validation dialog\n  const handleBalanceValidationConfirm = useCallback((useImportBalance: boolean) => {\n    if (!selectedBankAccount || !balanceValidation) return;\n    \n    setShowBalanceDialog(false);\n    \n    if (useImportBalance) {\n      // Update account balance to match import balance\n      bankAccountService.updateBalance(selectedBankAccount.id, balanceValidation.actualBalance);\n      // Update local state\n      setSelectedBankAccount(prev => prev ? { ...prev, currentBalance: balanceValidation.actualBalance } : null);\n    }\n    \n    setStep('review');\n  }, [selectedBankAccount, balanceValidation]);\n\n  const handleBalanceValidationCancel = useCallback(() => {\n    setShowBalanceDialog(false);\n    setSelectedBankAccount(null);\n    setBalanceValidation(null);\n    setStep('selectBank');\n  }, []);\n\n  const handleConfirmImport = useCallback(() => {\n    if (!selectedBankAccount) return;\n\n    const allTransactions = importSummaries.flatMap(summary => summary.transactions);\n    \n    // Store transactions in the system\n    transactionStorageService.storeTransactions(selectedBankAccount.id, allTransactions);\n    \n    // Track uploaded files\n    files.forEach((file, index) => {\n      const summary = importSummaries[index];\n      if (summary) {\n        try {\n          fileStorageService.addUploadedFile({\n            fileName: file.name,\n            accountId: selectedBankAccount.id,\n            accountName: selectedBankAccount.name,\n            transactionCount: summary.totalTransactions,\n            fileSize: file.size,\n            checksum: `${file.name}_${file.size}_${Date.now()}`\n          });\n        } catch (error) {\n          console.error('Error tracking uploaded file:', error);\n        }\n      }\n    });\n    \n    // Update account balance to the most recent transaction balance (Post date + Time based)\n    const sortedTransactions = [...allTransactions].sort((a, b) => {\n      const dateTimeA = new Date(`${a.postDate || a.date}T${a.time || '00:00'}`);\n      const dateTimeB = new Date(`${b.postDate || b.date}T${b.time || '00:00'}`);\n      return dateTimeB.getTime() - dateTimeA.getTime();\n    });\n    \n    if (sortedTransactions.length > 0) {\n      const latestBalance = sortedTransactions[0].balance;\n      bankAccountService.updateBalance(selectedBankAccount.id, latestBalance);\n    }\n    \n    if (onImportComplete) {\n      onImportComplete(allTransactions, selectedBankAccount);\n    }\n    \n    // Reset state\n    setStep('upload');\n    setFiles([]);\n    setSelectedBankAccount(null);\n    setImportSummaries([]);\n    setBalanceValidation(null);\n    setShowBalanceDialog(false);\n  }, [selectedBankAccount, importSummaries, onImportComplete, files]);\n\n  const handleCancel = useCallback(() => {\n    setStep('upload');\n    setFiles([]);\n    setSelectedBankAccount(null);\n    setImportSummaries([]);\n    setError(null);\n    setBalanceValidation(null);\n    setShowBalanceDialog(false);\n  }, []);\n\n  const formatCurrency = (amount: number): string => {\n    return new Intl.NumberFormat('en-US', {\n      style: 'currency',\n      currency: 'USD'\n    }).format(amount);\n  };\n\n  const totalTransactions = importSummaries.reduce((sum, summary) => sum + summary.totalTransactions, 0);\n  const totalDebitAmount = importSummaries.reduce((sum, summary) => sum + summary.totalDebitAmount, 0);\n  const totalCreditAmount = importSummaries.reduce((sum, summary) => sum + summary.totalCreditAmount, 0);\n  const totalValidationErrors = importSummaries.reduce((sum, summary) => sum + summary.validationErrors.length, 0);\n\n  return (\n    <div className=\"bank-statement-import\">\n      <div className=\"import-header\">\n        <h2 className=\"import-title\">Bank Statement Import</h2>\n        <p className=\"import-description\">\n          Import CSV bank statements to process transactions automatically\n        </p>\n      </div>\n\n      {/* Step 1: File Upload */}\n      {step === 'upload' && (\n        <div className=\"import-step\">\n          <div className=\"step-header\">\n            <h3>Step 1: Upload CSV Files</h3>\n            <button\n              type=\"button\"\n              onClick={handleDownloadTemplate}\n              className=\"btn btn-secondary btn-sm\"\n            >\n              Download CSV Template\n            </button>\n          </div>\n          \n          <FileUpload\n            onFilesSelected={handleFilesSelected}\n            disabled={isProcessing}\n          />\n          \n          {isProcessing && (\n            <div className=\"processing-indicator\">\n              <div className=\"spinner\"></div>\n              <p>Processing files...</p>\n            </div>\n          )}\n          \n          {error && (\n            <div className=\"error-message\">\n              <p>{error}</p>\n            </div>\n          )}\n        </div>\n      )}\n\n      {/* Step 2: Select Bank Account */}\n      {step === 'selectBank' && (\n        <div className=\"import-step\">\n          <div className=\"step-header\">\n            <h3>Step 2: Select Bank Account</h3>\n            <p>Choose which bank account these transactions belong to</p>\n          </div>\n          \n          <div className=\"bank-selection\">\n            <div className=\"form-group\">\n              <label htmlFor=\"bank-account-select\" className=\"form-label\">\n                Bank Account\n              </label>\n              <select\n                id=\"bank-account-select\"\n                className=\"form-select\"\n                onChange={(e) => handleBankAccountSelect(e.target.value)}\n                defaultValue=\"\"\n              >\n                <option value=\"\">Select a bank account...</option>\n                {bankAccounts.map(account => (\n                  <option key={account.id} value={account.id}>\n                    {account.name} - {account.bankName} ({account.accountNumber})\n                  </option>\n                ))}\n              </select>\n            </div>\n            \n            <div className=\"import-summary-preview\">\n              <h4>Import Summary</h4>\n              <div className=\"summary-stats\">\n                <div className=\"stat-item\">\n                  <span className=\"stat-label\">Files:</span>\n                  <span className=\"stat-value\">{files.length}</span>\n                </div>\n                <div className=\"stat-item\">\n                  <span className=\"stat-label\">Total Transactions:</span>\n                  <span className=\"stat-value\">{totalTransactions}</span>\n                </div>\n                <div className=\"stat-item\">\n                  <span className=\"stat-label\">Total Debits:</span>\n                  <span className=\"stat-value text-error\">{formatCurrency(totalDebitAmount)}</span>\n                </div>\n                <div className=\"stat-item\">\n                  <span className=\"stat-label\">Total Credits:</span>\n                  <span className=\"stat-value text-success\">{formatCurrency(totalCreditAmount)}</span>\n                </div>\n                {totalValidationErrors > 0 && (\n                  <div className=\"stat-item\">\n                    <span className=\"stat-label\">Validation Errors:</span>\n                    <span className=\"stat-value text-warning\">{totalValidationErrors}</span>\n                  </div>\n                )}\n              </div>\n            </div>\n          </div>\n          \n          <div className=\"step-actions\">\n            <button\n              type=\"button\"\n              onClick={handleCancel}\n              className=\"btn btn-secondary\"\n            >\n              Cancel\n            </button>\n          </div>\n        </div>\n      )}\n\n      {/* Step 3: Review and Edit */}\n      {step === 'review' && selectedBankAccount && (\n        <div className=\"import-step\">\n          <div className=\"step-header\">\n            <h3>Step 3: Review & Edit Transactions</h3>\n            <p>Review the imported transactions and make any necessary adjustments</p>\n          </div>\n          \n          <div className=\"account-info\">\n            <h4>Selected Account</h4>\n            <div className=\"account-details\">\n              <p><strong>{selectedBankAccount.name}</strong></p>\n              <p>{selectedBankAccount.bankName} - {selectedBankAccount.accountNumber}</p>\n              <p>Current Balance: {formatCurrency(selectedBankAccount.currentBalance)}</p>\n              {importSummaries.length > 0 && balanceValidation && (\n                <div className=\"balance-comparison\">\n                  <p>Import Closing Balance: {formatCurrency(balanceValidation.actualBalance)}</p>\n                  {!balanceValidation.isValid && (\n                    <div className=\"balance-warning\">\n                      ⚠️ Balance Validated: Expected {formatCurrency(balanceValidation.expectedBalance)}, \n                      Got {formatCurrency(balanceValidation.actualBalance)} \n                      (Difference: {formatCurrency(balanceValidation.difference)})\n                    </div>\n                  )}\n                  {balanceValidation.isValid && (\n                    <div className=\"balance-success\">\n                      ✅ Balance validation passed - transactions are consistent with account history\n                    </div>\n                  )}\n                </div>\n              )}\n            </div>\n          </div>\n\n          <div className=\"import-summaries\">\n            {importSummaries.map((summary, summaryIndex) => (\n              <div key={summaryIndex} className=\"import-summary-card\">\n                <div className=\"summary-header\">\n                  <h4>{summary.fileName}</h4>\n                  <div className=\"summary-stats\">\n                    <span>{summary.totalTransactions} transactions</span>\n                    <span>Period: {summary.dateRange.from} to {summary.dateRange.to}</span>\n                  </div>\n                </div>\n                \n                <div className=\"balance-summary\">\n                  <div className=\"balance-row\">\n                    <span className=\"balance-label\">Opening Balance:</span>\n                    <span className=\"balance-value\">{formatCurrency(summary.openingBalance)}</span>\n                  </div>\n                  <div className=\"balance-row\">\n                    <span className=\"balance-label\">Daily Movement:</span>\n                    <span className={`balance-value ${summary.dailyMovement >= 0 ? 'text-success' : 'text-error'}`}>\n                      {summary.dailyMovement >= 0 ? '+' : ''}{formatCurrency(summary.dailyMovement)}\n                    </span>\n                  </div>\n                  <div className=\"balance-row\">\n                    <span className=\"balance-label\">Closing Balance:</span>\n                    <span className=\"balance-value\">{formatCurrency(summary.closingBalance)}</span>\n                  </div>\n                </div>\n                \n                {summary.validationErrors.length > 0 && (\n                  <div className=\"validation-errors\">\n                    <h5>Validation Errors</h5>\n                    <ul>\n                      {summary.validationErrors.map((error, index) => (\n                        <li key={index} className=\"validation-error\">\n                          Row {error.row}, {error.field}: {error.message}\n                        </li>\n                      ))}\n                    </ul>\n                  </div>\n                )}\n                \n                <div className=\"transactions-table-container\">\n                  <table className=\"transactions-table\">\n                    <thead>\n                      <tr>\n                        <th>Date</th>\n                        <th>Description</th>\n                        <th>Debit</th>\n                        <th>Credit</th>\n                        <th>Balance</th>\n                        <th>Reference</th>\n                      </tr>\n                    </thead>\n                    <tbody>\n                      {summary.transactions.map((transaction, transactionIndex) => (\n                        <tr key={transaction.id}>\n                          <td>\n                            <input\n                              type=\"date\"\n                              value={transaction.date}\n                              onChange={(e) => handleTransactionEdit(summaryIndex, transactionIndex, 'date', e.target.value)}\n                              className=\"form-input\"\n                            />\n                          </td>\n                          <td>\n                            <input\n                              type=\"text\"\n                              value={transaction.description}\n                              onChange={(e) => handleTransactionEdit(summaryIndex, transactionIndex, 'description', e.target.value)}\n                              className=\"form-input\"\n                            />\n                          </td>\n                                                     <td>\n                             <input\n                               type=\"number\"\n                               step=\"0.01\"\n                               value={transaction.debitAmount ? transaction.debitAmount.toFixed(2) : ''}\n                               onChange={(e) => handleTransactionEdit(summaryIndex, transactionIndex, 'debitAmount', parseFloat(e.target.value) || 0)}\n                               className=\"form-input\"\n                               placeholder=\"0.00\"\n                             />\n                           </td>\n                           <td>\n                             <input\n                               type=\"number\"\n                               step=\"0.01\"\n                               value={transaction.creditAmount ? transaction.creditAmount.toFixed(2) : ''}\n                               onChange={(e) => handleTransactionEdit(summaryIndex, transactionIndex, 'creditAmount', parseFloat(e.target.value) || 0)}\n                               className=\"form-input\"\n                               placeholder=\"0.00\"\n                             />\n                           </td>\n                           <td>\n                             <input\n                               type=\"number\"\n                               step=\"0.01\"\n                               value={transaction.balance.toFixed(2)}\n                               onChange={(e) => handleTransactionEdit(summaryIndex, transactionIndex, 'balance', parseFloat(e.target.value) || 0)}\n                               className=\"form-input\"\n                             />\n                           </td>\n                          <td>\n                            <input\n                              type=\"text\"\n                              value={transaction.reference || ''}\n                              onChange={(e) => handleTransactionEdit(summaryIndex, transactionIndex, 'reference', e.target.value)}\n                              className=\"form-input\"\n                            />\n                          </td>\n                        </tr>\n                      ))}\n                    </tbody>\n                  </table>\n                </div>\n              </div>\n            ))}\n          </div>\n          \n          <div className=\"step-actions\">\n            <button\n              type=\"button\"\n              onClick={handleCancel}\n              className=\"btn btn-secondary\"\n            >\n              Cancel\n            </button>\n            <button\n              type=\"button\"\n              onClick={() => setStep('confirm')}\n              className=\"btn btn-primary\"\n              disabled={totalValidationErrors > 0}\n            >\n              Proceed to Confirmation\n            </button>\n          </div>\n        </div>\n      )}\n\n      {/* Step 4: Confirmation */}\n      {step === 'confirm' && selectedBankAccount && (\n        <div className=\"import-step\">\n          <div className=\"step-header\">\n            <h3>Step 4: Confirm Import</h3>\n            <p>Please review the final summary before proceeding with the import</p>\n          </div>\n          \n          <div className=\"confirmation-summary\">\n            <div className=\"summary-section\">\n              <h4>Account Information</h4>\n              <div className=\"account-details\">\n                <p><strong>{selectedBankAccount.name}</strong></p>\n                <p>{selectedBankAccount.bankName} - {selectedBankAccount.accountNumber}</p>\n                <p>Current Balance: {formatCurrency(selectedBankAccount.currentBalance)}</p>\n              </div>\n            </div>\n            \n            <div className=\"summary-section\">\n              <h4>Import Summary</h4>\n              <div className=\"summary-stats\">\n                <div className=\"stat-item\">\n                  <span className=\"stat-label\">Files Processed:</span>\n                  <span className=\"stat-value\">{files.length}</span>\n                </div>\n                <div className=\"stat-item\">\n                  <span className=\"stat-label\">Total Transactions:</span>\n                  <span className=\"stat-value\">{totalTransactions}</span>\n                </div>\n                <div className=\"stat-item\">\n                  <span className=\"stat-label\">Total Debit Amount:</span>\n                  <span className=\"stat-value text-error\">{formatCurrency(totalDebitAmount)}</span>\n                </div>\n                <div className=\"stat-item\">\n                  <span className=\"stat-label\">Total Credit Amount:</span>\n                  <span className=\"stat-value text-success\">{formatCurrency(totalCreditAmount)}</span>\n                </div>\n                <div className=\"stat-item\">\n                  <span className=\"stat-label\">Net Change:</span>\n                  <span className={`stat-value ${totalCreditAmount - totalDebitAmount >= 0 ? 'text-success' : 'text-error'}`}>\n                    {formatCurrency(totalCreditAmount - totalDebitAmount)}\n                  </span>\n                </div>\n              </div>\n            </div>\n          </div>\n          \n          <div className=\"confirmation-question\">\n            <h4>Do you want to proceed with this import?</h4>\n            <p>This action will add all transactions to the selected bank account.</p>\n          </div>\n          \n          <div className=\"step-actions\">\n            <button\n              type=\"button\"\n              onClick={handleCancel}\n              className=\"btn btn-danger\"\n            >\n              No, Cancel\n            </button>\n            <button\n              type=\"button\"\n              onClick={handleConfirmImport}\n              className=\"btn btn-success btn-lg\"\n            >\n              Yes, Proceed\n            </button>\n          </div>\n        </div>\n      )}\n\n      {/* Balance Validation Dialog */}\n      {balanceValidation && (\n        <BalanceValidationDialog\n          isOpen={showBalanceDialog}\n          validationResult={balanceValidation}\n          onConfirm={handleBalanceValidationConfirm}\n          onCancel={handleBalanceValidationCancel}\n        />\n      )}\n    </div>\n  );\n}; "], "mappings": "6FAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,WAAW,KAAQ,OAAO,CACpD,OAASC,UAAU,KAAQ,cAAc,CAEzC,OAASC,oBAAoB,KAAQ,kCAAkC,CACvE,OAASC,kBAAkB,KAAQ,gCAAgC,CACnE,OAASC,yBAAyB,KAAiC,uCAAuC,CAC1G,OAASC,kBAAkB,KAAQ,gCAAgC,CACnE,OAASC,uBAAuB,KAAQ,2BAA2B,CACnE,MAAO,2BAA2B,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAMnC,MAAO,MAAM,CAAAC,mBAAuD,CAAGC,IAAA,EAEjE,IAFkE,CACtEC,gBACF,CAAC,CAAAD,IAAA,CACC,KAAM,CAACE,IAAI,CAAEC,OAAO,CAAC,CAAGhB,QAAQ,CAAiD,QAAQ,CAAC,CAC1F,KAAM,CAACiB,KAAK,CAAEC,QAAQ,CAAC,CAAGlB,QAAQ,CAAS,EAAE,CAAC,CAC9C,KAAM,CAACmB,mBAAmB,CAAEC,sBAAsB,CAAC,CAAGpB,QAAQ,CAAqB,IAAI,CAAC,CACxF,KAAM,CAACqB,eAAe,CAAEC,kBAAkB,CAAC,CAAGtB,QAAQ,CAAkB,EAAE,CAAC,CAC3E,KAAM,CAACuB,YAAY,CAAEC,eAAe,CAAC,CAAGxB,QAAQ,CAAC,KAAK,CAAC,CACvD,KAAM,CAACyB,KAAK,CAAEC,QAAQ,CAAC,CAAG1B,QAAQ,CAAgB,IAAI,CAAC,CACvD,KAAM,CAAC2B,YAAY,CAAC,CAAG3B,QAAQ,CAAgBI,kBAAkB,CAACwB,cAAc,CAAC,CAAC,CAAC,CAEnF;AACA,KAAM,CAACC,iBAAiB,CAAEC,oBAAoB,CAAC,CAAG9B,QAAQ,CAAiC,IAAI,CAAC,CAChG,KAAM,CAAC+B,iBAAiB,CAAEC,oBAAoB,CAAC,CAAGhC,QAAQ,CAAC,KAAK,CAAC,CAEjE,KAAM,CAAAiC,mBAAmB,CAAGhC,WAAW,CAAC,KAAO,CAAAiC,aAAqB,EAAK,CACvEhB,QAAQ,CAACgB,aAAa,CAAC,CACvBV,eAAe,CAAC,IAAI,CAAC,CACrBE,QAAQ,CAAC,IAAI,CAAC,CAEd,GAAI,CACF,KAAM,CAAAS,SAA0B,CAAG,EAAE,CAErC,IAAK,KAAM,CAAAC,IAAI,GAAI,CAAAF,aAAa,CAAE,CAChC,KAAM,CAAAG,OAAO,CAAG,KAAM,CAAAlC,oBAAoB,CAACmC,WAAW,CAACF,IAAI,CAAC,CAC5DD,SAAS,CAACI,IAAI,CAACF,OAAO,CAAC,CACzB,CAEAf,kBAAkB,CAACa,SAAS,CAAC,CAC7BnB,OAAO,CAAC,YAAY,CAAC,CACvB,CAAE,MAAOwB,GAAG,CAAE,CACZd,QAAQ,CAACc,GAAG,WAAY,CAAAC,KAAK,CAAGD,GAAG,CAACE,OAAO,CAAG,yBAAyB,CAAC,CAC1E,CAAC,OAAS,CACRlB,eAAe,CAAC,KAAK,CAAC,CACxB,CACF,CAAC,CAAE,EAAE,CAAC,CAEN,KAAM,CAAAmB,uBAAuB,CAAG1C,WAAW,CAAE2C,SAAiB,EAAK,CACjE,KAAM,CAAAC,OAAO,CAAGlB,YAAY,CAACmB,IAAI,CAACC,GAAG,EAAIA,GAAG,CAACC,EAAE,GAAKJ,SAAS,CAAC,CAC9D,GAAIC,OAAO,CAAE,CACXzB,sBAAsB,CAACyB,OAAO,CAAC,CAE/B;AACA,KAAM,CAAAI,eAAe,CAAG5B,eAAe,CAAC6B,OAAO,CAACb,OAAO,EAAIA,OAAO,CAACc,YAAY,CAAC,CAChF,KAAM,CAAAC,UAAU,CAAG/C,yBAAyB,CAACgD,eAAe,CAC1DT,SAAS,CACTK,eAAe,CACfJ,OAAO,CAACS,cACV,CAAC,CAEDxB,oBAAoB,CAACsB,UAAU,CAAC,CAEhC,GAAI,CAACA,UAAU,CAACG,OAAO,CAAE,CACvBvB,oBAAoB,CAAC,IAAI,CAAC,CAC5B,CAAC,IAAM,CACLhB,OAAO,CAAC,QAAQ,CAAC,CACnB,CACF,CACF,CAAC,CAAE,CAACW,YAAY,CAAEN,eAAe,CAAC,CAAC,CAEnC,KAAM,CAAAmC,sBAAsB,CAAGvD,WAAW,CAAC,IAAM,CAC/CE,oBAAoB,CAACsD,gBAAgB,CAAC,CAAC,CACzC,CAAC,CAAE,EAAE,CAAC,CAEN,KAAM,CAAAC,qBAAqB,CAAGzD,WAAW,CAAC,CACxC0D,YAAoB,CACpBC,gBAAwB,CACxBC,KAAwB,CACxBC,KAAsB,GACnB,CACHxC,kBAAkB,CAACyC,IAAI,EAAI,CACzB,KAAM,CAAAC,OAAO,CAAG,CAAC,GAAGD,IAAI,CAAC,CACzBC,OAAO,CAACL,YAAY,CAAC,CAACR,YAAY,CAACS,gBAAgB,CAAC,CAAAK,aAAA,CAAAA,aAAA,IAC/CD,OAAO,CAACL,YAAY,CAAC,CAACR,YAAY,CAACS,gBAAgB,CAAC,MACvD,CAACC,KAAK,EAAGC,KAAK,EACf,CACD,MAAO,CAAAE,OAAO,CAChB,CAAC,CAAC,CACJ,CAAC,CAAE,EAAE,CAAC,CAEN;AACA,KAAM,CAAAE,8BAA8B,CAAGjE,WAAW,CAAEkE,gBAAyB,EAAK,CAChF,GAAI,CAAChD,mBAAmB,EAAI,CAACU,iBAAiB,CAAE,OAEhDG,oBAAoB,CAAC,KAAK,CAAC,CAE3B,GAAImC,gBAAgB,CAAE,CACpB;AACA/D,kBAAkB,CAACgE,aAAa,CAACjD,mBAAmB,CAAC6B,EAAE,CAAEnB,iBAAiB,CAACwC,aAAa,CAAC,CACzF;AACAjD,sBAAsB,CAAC2C,IAAI,EAAIA,IAAI,CAAAE,aAAA,CAAAA,aAAA,IAAQF,IAAI,MAAET,cAAc,CAAEzB,iBAAiB,CAACwC,aAAa,GAAK,IAAI,CAAC,CAC5G,CAEArD,OAAO,CAAC,QAAQ,CAAC,CACnB,CAAC,CAAE,CAACG,mBAAmB,CAAEU,iBAAiB,CAAC,CAAC,CAE5C,KAAM,CAAAyC,6BAA6B,CAAGrE,WAAW,CAAC,IAAM,CACtD+B,oBAAoB,CAAC,KAAK,CAAC,CAC3BZ,sBAAsB,CAAC,IAAI,CAAC,CAC5BU,oBAAoB,CAAC,IAAI,CAAC,CAC1Bd,OAAO,CAAC,YAAY,CAAC,CACvB,CAAC,CAAE,EAAE,CAAC,CAEN,KAAM,CAAAuD,mBAAmB,CAAGtE,WAAW,CAAC,IAAM,CAC5C,GAAI,CAACkB,mBAAmB,CAAE,OAE1B,KAAM,CAAA8B,eAAe,CAAG5B,eAAe,CAAC6B,OAAO,CAACb,OAAO,EAAIA,OAAO,CAACc,YAAY,CAAC,CAEhF;AACA9C,yBAAyB,CAACmE,iBAAiB,CAACrD,mBAAmB,CAAC6B,EAAE,CAAEC,eAAe,CAAC,CAEpF;AACAhC,KAAK,CAACwD,OAAO,CAAC,CAACrC,IAAI,CAAEsC,KAAK,GAAK,CAC7B,KAAM,CAAArC,OAAO,CAAGhB,eAAe,CAACqD,KAAK,CAAC,CACtC,GAAIrC,OAAO,CAAE,CACX,GAAI,CACF/B,kBAAkB,CAACqE,eAAe,CAAC,CACjCC,QAAQ,CAAExC,IAAI,CAACyC,IAAI,CACnBjC,SAAS,CAAEzB,mBAAmB,CAAC6B,EAAE,CACjC8B,WAAW,CAAE3D,mBAAmB,CAAC0D,IAAI,CACrCE,gBAAgB,CAAE1C,OAAO,CAAC2C,iBAAiB,CAC3CC,QAAQ,CAAE7C,IAAI,CAAC8C,IAAI,CACnBC,QAAQ,IAAAC,MAAA,CAAKhD,IAAI,CAACyC,IAAI,MAAAO,MAAA,CAAIhD,IAAI,CAAC8C,IAAI,MAAAE,MAAA,CAAIC,IAAI,CAACC,GAAG,CAAC,CAAC,CACnD,CAAC,CAAC,CACJ,CAAE,MAAO7D,KAAK,CAAE,CACd8D,OAAO,CAAC9D,KAAK,CAAC,+BAA+B,CAAEA,KAAK,CAAC,CACvD,CACF,CACF,CAAC,CAAC,CAEF;AACA,KAAM,CAAA+D,kBAAkB,CAAG,CAAC,GAAGvC,eAAe,CAAC,CAACwC,IAAI,CAAC,CAACC,CAAC,CAAEC,CAAC,GAAK,CAC7D,KAAM,CAAAC,SAAS,CAAG,GAAI,CAAAP,IAAI,IAAAD,MAAA,CAAIM,CAAC,CAACG,QAAQ,EAAIH,CAAC,CAACI,IAAI,MAAAV,MAAA,CAAIM,CAAC,CAACK,IAAI,EAAI,OAAO,CAAE,CAAC,CAC1E,KAAM,CAAAC,SAAS,CAAG,GAAI,CAAAX,IAAI,IAAAD,MAAA,CAAIO,CAAC,CAACE,QAAQ,EAAIF,CAAC,CAACG,IAAI,MAAAV,MAAA,CAAIO,CAAC,CAACI,IAAI,EAAI,OAAO,CAAE,CAAC,CAC1E,MAAO,CAAAC,SAAS,CAACC,OAAO,CAAC,CAAC,CAAGL,SAAS,CAACK,OAAO,CAAC,CAAC,CAClD,CAAC,CAAC,CAEF,GAAIT,kBAAkB,CAACU,MAAM,CAAG,CAAC,CAAE,CACjC,KAAM,CAAAC,aAAa,CAAGX,kBAAkB,CAAC,CAAC,CAAC,CAACY,OAAO,CACnDhG,kBAAkB,CAACgE,aAAa,CAACjD,mBAAmB,CAAC6B,EAAE,CAAEmD,aAAa,CAAC,CACzE,CAEA,GAAIrF,gBAAgB,CAAE,CACpBA,gBAAgB,CAACmC,eAAe,CAAE9B,mBAAmB,CAAC,CACxD,CAEA;AACAH,OAAO,CAAC,QAAQ,CAAC,CACjBE,QAAQ,CAAC,EAAE,CAAC,CACZE,sBAAsB,CAAC,IAAI,CAAC,CAC5BE,kBAAkB,CAAC,EAAE,CAAC,CACtBQ,oBAAoB,CAAC,IAAI,CAAC,CAC1BE,oBAAoB,CAAC,KAAK,CAAC,CAC7B,CAAC,CAAE,CAACb,mBAAmB,CAAEE,eAAe,CAAEP,gBAAgB,CAAEG,KAAK,CAAC,CAAC,CAEnE,KAAM,CAAAoF,YAAY,CAAGpG,WAAW,CAAC,IAAM,CACrCe,OAAO,CAAC,QAAQ,CAAC,CACjBE,QAAQ,CAAC,EAAE,CAAC,CACZE,sBAAsB,CAAC,IAAI,CAAC,CAC5BE,kBAAkB,CAAC,EAAE,CAAC,CACtBI,QAAQ,CAAC,IAAI,CAAC,CACdI,oBAAoB,CAAC,IAAI,CAAC,CAC1BE,oBAAoB,CAAC,KAAK,CAAC,CAC7B,CAAC,CAAE,EAAE,CAAC,CAEN,KAAM,CAAAsE,cAAc,CAAIC,MAAc,EAAa,CACjD,MAAO,IAAI,CAAAC,IAAI,CAACC,YAAY,CAAC,OAAO,CAAE,CACpCC,KAAK,CAAE,UAAU,CACjBC,QAAQ,CAAE,KACZ,CAAC,CAAC,CAACC,MAAM,CAACL,MAAM,CAAC,CACnB,CAAC,CAED,KAAM,CAAAvB,iBAAiB,CAAG3D,eAAe,CAACwF,MAAM,CAAC,CAACC,GAAG,CAAEzE,OAAO,GAAKyE,GAAG,CAAGzE,OAAO,CAAC2C,iBAAiB,CAAE,CAAC,CAAC,CACtG,KAAM,CAAA+B,gBAAgB,CAAG1F,eAAe,CAACwF,MAAM,CAAC,CAACC,GAAG,CAAEzE,OAAO,GAAKyE,GAAG,CAAGzE,OAAO,CAAC0E,gBAAgB,CAAE,CAAC,CAAC,CACpG,KAAM,CAAAC,iBAAiB,CAAG3F,eAAe,CAACwF,MAAM,CAAC,CAACC,GAAG,CAAEzE,OAAO,GAAKyE,GAAG,CAAGzE,OAAO,CAAC2E,iBAAiB,CAAE,CAAC,CAAC,CACtG,KAAM,CAAAC,qBAAqB,CAAG5F,eAAe,CAACwF,MAAM,CAAC,CAACC,GAAG,CAAEzE,OAAO,GAAKyE,GAAG,CAAGzE,OAAO,CAAC6E,gBAAgB,CAAChB,MAAM,CAAE,CAAC,CAAC,CAEhH,mBACEvF,KAAA,QAAKwG,SAAS,CAAC,uBAAuB,CAAAC,QAAA,eACpCzG,KAAA,QAAKwG,SAAS,CAAC,eAAe,CAAAC,QAAA,eAC5B3G,IAAA,OAAI0G,SAAS,CAAC,cAAc,CAAAC,QAAA,CAAC,uBAAqB,CAAI,CAAC,cACvD3G,IAAA,MAAG0G,SAAS,CAAC,oBAAoB,CAAAC,QAAA,CAAC,kEAElC,CAAG,CAAC,EACD,CAAC,CAGLrG,IAAI,GAAK,QAAQ,eAChBJ,KAAA,QAAKwG,SAAS,CAAC,aAAa,CAAAC,QAAA,eAC1BzG,KAAA,QAAKwG,SAAS,CAAC,aAAa,CAAAC,QAAA,eAC1B3G,IAAA,OAAA2G,QAAA,CAAI,0BAAwB,CAAI,CAAC,cACjC3G,IAAA,WACE4G,IAAI,CAAC,QAAQ,CACbC,OAAO,CAAE9D,sBAAuB,CAChC2D,SAAS,CAAC,0BAA0B,CAAAC,QAAA,CACrC,uBAED,CAAQ,CAAC,EACN,CAAC,cAEN3G,IAAA,CAACP,UAAU,EACTqH,eAAe,CAAEtF,mBAAoB,CACrCuF,QAAQ,CAAEjG,YAAa,CACxB,CAAC,CAEDA,YAAY,eACXZ,KAAA,QAAKwG,SAAS,CAAC,sBAAsB,CAAAC,QAAA,eACnC3G,IAAA,QAAK0G,SAAS,CAAC,SAAS,CAAM,CAAC,cAC/B1G,IAAA,MAAA2G,QAAA,CAAG,qBAAmB,CAAG,CAAC,EACvB,CACN,CAEA3F,KAAK,eACJhB,IAAA,QAAK0G,SAAS,CAAC,eAAe,CAAAC,QAAA,cAC5B3G,IAAA,MAAA2G,QAAA,CAAI3F,KAAK,CAAI,CAAC,CACX,CACN,EACE,CACN,CAGAV,IAAI,GAAK,YAAY,eACpBJ,KAAA,QAAKwG,SAAS,CAAC,aAAa,CAAAC,QAAA,eAC1BzG,KAAA,QAAKwG,SAAS,CAAC,aAAa,CAAAC,QAAA,eAC1B3G,IAAA,OAAA2G,QAAA,CAAI,6BAA2B,CAAI,CAAC,cACpC3G,IAAA,MAAA2G,QAAA,CAAG,wDAAsD,CAAG,CAAC,EAC1D,CAAC,cAENzG,KAAA,QAAKwG,SAAS,CAAC,gBAAgB,CAAAC,QAAA,eAC7BzG,KAAA,QAAKwG,SAAS,CAAC,YAAY,CAAAC,QAAA,eACzB3G,IAAA,UAAOgH,OAAO,CAAC,qBAAqB,CAACN,SAAS,CAAC,YAAY,CAAAC,QAAA,CAAC,cAE5D,CAAO,CAAC,cACRzG,KAAA,WACEqC,EAAE,CAAC,qBAAqB,CACxBmE,SAAS,CAAC,aAAa,CACvBO,QAAQ,CAAGC,CAAC,EAAKhF,uBAAuB,CAACgF,CAAC,CAACC,MAAM,CAAC9D,KAAK,CAAE,CACzD+D,YAAY,CAAC,EAAE,CAAAT,QAAA,eAEf3G,IAAA,WAAQqD,KAAK,CAAC,EAAE,CAAAsD,QAAA,CAAC,0BAAwB,CAAQ,CAAC,CACjDzF,YAAY,CAACmG,GAAG,CAACjF,OAAO,eACvBlC,KAAA,WAAyBmD,KAAK,CAAEjB,OAAO,CAACG,EAAG,CAAAoE,QAAA,EACxCvE,OAAO,CAACgC,IAAI,CAAC,KAAG,CAAChC,OAAO,CAACkF,QAAQ,CAAC,IAAE,CAAClF,OAAO,CAACmF,aAAa,CAAC,GAC9D,GAFanF,OAAO,CAACG,EAEb,CACT,CAAC,EACI,CAAC,EACN,CAAC,cAENrC,KAAA,QAAKwG,SAAS,CAAC,wBAAwB,CAAAC,QAAA,eACrC3G,IAAA,OAAA2G,QAAA,CAAI,gBAAc,CAAI,CAAC,cACvBzG,KAAA,QAAKwG,SAAS,CAAC,eAAe,CAAAC,QAAA,eAC5BzG,KAAA,QAAKwG,SAAS,CAAC,WAAW,CAAAC,QAAA,eACxB3G,IAAA,SAAM0G,SAAS,CAAC,YAAY,CAAAC,QAAA,CAAC,QAAM,CAAM,CAAC,cAC1C3G,IAAA,SAAM0G,SAAS,CAAC,YAAY,CAAAC,QAAA,CAAEnG,KAAK,CAACiF,MAAM,CAAO,CAAC,EAC/C,CAAC,cACNvF,KAAA,QAAKwG,SAAS,CAAC,WAAW,CAAAC,QAAA,eACxB3G,IAAA,SAAM0G,SAAS,CAAC,YAAY,CAAAC,QAAA,CAAC,qBAAmB,CAAM,CAAC,cACvD3G,IAAA,SAAM0G,SAAS,CAAC,YAAY,CAAAC,QAAA,CAAEpC,iBAAiB,CAAO,CAAC,EACpD,CAAC,cACNrE,KAAA,QAAKwG,SAAS,CAAC,WAAW,CAAAC,QAAA,eACxB3G,IAAA,SAAM0G,SAAS,CAAC,YAAY,CAAAC,QAAA,CAAC,eAAa,CAAM,CAAC,cACjD3G,IAAA,SAAM0G,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAEd,cAAc,CAACS,gBAAgB,CAAC,CAAO,CAAC,EAC9E,CAAC,cACNpG,KAAA,QAAKwG,SAAS,CAAC,WAAW,CAAAC,QAAA,eACxB3G,IAAA,SAAM0G,SAAS,CAAC,YAAY,CAAAC,QAAA,CAAC,gBAAc,CAAM,CAAC,cAClD3G,IAAA,SAAM0G,SAAS,CAAC,yBAAyB,CAAAC,QAAA,CAAEd,cAAc,CAACU,iBAAiB,CAAC,CAAO,CAAC,EACjF,CAAC,CACLC,qBAAqB,CAAG,CAAC,eACxBtG,KAAA,QAAKwG,SAAS,CAAC,WAAW,CAAAC,QAAA,eACxB3G,IAAA,SAAM0G,SAAS,CAAC,YAAY,CAAAC,QAAA,CAAC,oBAAkB,CAAM,CAAC,cACtD3G,IAAA,SAAM0G,SAAS,CAAC,yBAAyB,CAAAC,QAAA,CAAEH,qBAAqB,CAAO,CAAC,EACrE,CACN,EACE,CAAC,EACH,CAAC,EACH,CAAC,cAENxG,IAAA,QAAK0G,SAAS,CAAC,cAAc,CAAAC,QAAA,cAC3B3G,IAAA,WACE4G,IAAI,CAAC,QAAQ,CACbC,OAAO,CAAEjB,YAAa,CACtBc,SAAS,CAAC,mBAAmB,CAAAC,QAAA,CAC9B,QAED,CAAQ,CAAC,CACN,CAAC,EACH,CACN,CAGArG,IAAI,GAAK,QAAQ,EAAII,mBAAmB,eACvCR,KAAA,QAAKwG,SAAS,CAAC,aAAa,CAAAC,QAAA,eAC1BzG,KAAA,QAAKwG,SAAS,CAAC,aAAa,CAAAC,QAAA,eAC1B3G,IAAA,OAAA2G,QAAA,CAAI,oCAAkC,CAAI,CAAC,cAC3C3G,IAAA,MAAA2G,QAAA,CAAG,qEAAmE,CAAG,CAAC,EACvE,CAAC,cAENzG,KAAA,QAAKwG,SAAS,CAAC,cAAc,CAAAC,QAAA,eAC3B3G,IAAA,OAAA2G,QAAA,CAAI,kBAAgB,CAAI,CAAC,cACzBzG,KAAA,QAAKwG,SAAS,CAAC,iBAAiB,CAAAC,QAAA,eAC9B3G,IAAA,MAAA2G,QAAA,cAAG3G,IAAA,WAAA2G,QAAA,CAASjG,mBAAmB,CAAC0D,IAAI,CAAS,CAAC,CAAG,CAAC,cAClDlE,KAAA,MAAAyG,QAAA,EAAIjG,mBAAmB,CAAC4G,QAAQ,CAAC,KAAG,CAAC5G,mBAAmB,CAAC6G,aAAa,EAAI,CAAC,cAC3ErH,KAAA,MAAAyG,QAAA,EAAG,mBAAiB,CAACd,cAAc,CAACnF,mBAAmB,CAACmC,cAAc,CAAC,EAAI,CAAC,CAC3EjC,eAAe,CAAC6E,MAAM,CAAG,CAAC,EAAIrE,iBAAiB,eAC9ClB,KAAA,QAAKwG,SAAS,CAAC,oBAAoB,CAAAC,QAAA,eACjCzG,KAAA,MAAAyG,QAAA,EAAG,0BAAwB,CAACd,cAAc,CAACzE,iBAAiB,CAACwC,aAAa,CAAC,EAAI,CAAC,CAC/E,CAACxC,iBAAiB,CAAC0B,OAAO,eACzB5C,KAAA,QAAKwG,SAAS,CAAC,iBAAiB,CAAAC,QAAA,EAAC,2CACA,CAACd,cAAc,CAACzE,iBAAiB,CAACoG,eAAe,CAAC,CAAC,QAC9E,CAAC3B,cAAc,CAACzE,iBAAiB,CAACwC,aAAa,CAAC,CAAC,eACxC,CAACiC,cAAc,CAACzE,iBAAiB,CAACqG,UAAU,CAAC,CAAC,GAC7D,EAAK,CACN,CACArG,iBAAiB,CAAC0B,OAAO,eACxB9C,IAAA,QAAK0G,SAAS,CAAC,iBAAiB,CAAAC,QAAA,CAAC,qFAEjC,CAAK,CACN,EACE,CACN,EACE,CAAC,EACH,CAAC,cAEN3G,IAAA,QAAK0G,SAAS,CAAC,kBAAkB,CAAAC,QAAA,CAC9B/F,eAAe,CAACyG,GAAG,CAAC,CAACzF,OAAO,CAAEsB,YAAY,gBACzChD,KAAA,QAAwBwG,SAAS,CAAC,qBAAqB,CAAAC,QAAA,eACrDzG,KAAA,QAAKwG,SAAS,CAAC,gBAAgB,CAAAC,QAAA,eAC7B3G,IAAA,OAAA2G,QAAA,CAAK/E,OAAO,CAACuC,QAAQ,CAAK,CAAC,cAC3BjE,KAAA,QAAKwG,SAAS,CAAC,eAAe,CAAAC,QAAA,eAC5BzG,KAAA,SAAAyG,QAAA,EAAO/E,OAAO,CAAC2C,iBAAiB,CAAC,eAAa,EAAM,CAAC,cACrDrE,KAAA,SAAAyG,QAAA,EAAM,UAAQ,CAAC/E,OAAO,CAAC8F,SAAS,CAACC,IAAI,CAAC,MAAI,CAAC/F,OAAO,CAAC8F,SAAS,CAACE,EAAE,EAAO,CAAC,EACpE,CAAC,EACH,CAAC,cAEN1H,KAAA,QAAKwG,SAAS,CAAC,iBAAiB,CAAAC,QAAA,eAC9BzG,KAAA,QAAKwG,SAAS,CAAC,aAAa,CAAAC,QAAA,eAC1B3G,IAAA,SAAM0G,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAC,kBAAgB,CAAM,CAAC,cACvD3G,IAAA,SAAM0G,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAEd,cAAc,CAACjE,OAAO,CAACiG,cAAc,CAAC,CAAO,CAAC,EAC5E,CAAC,cACN3H,KAAA,QAAKwG,SAAS,CAAC,aAAa,CAAAC,QAAA,eAC1B3G,IAAA,SAAM0G,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAC,iBAAe,CAAM,CAAC,cACtDzG,KAAA,SAAMwG,SAAS,kBAAA/B,MAAA,CAAmB/C,OAAO,CAACkG,aAAa,EAAI,CAAC,CAAG,cAAc,CAAG,YAAY,CAAG,CAAAnB,QAAA,EAC5F/E,OAAO,CAACkG,aAAa,EAAI,CAAC,CAAG,GAAG,CAAG,EAAE,CAAEjC,cAAc,CAACjE,OAAO,CAACkG,aAAa,CAAC,EACzE,CAAC,EACJ,CAAC,cACN5H,KAAA,QAAKwG,SAAS,CAAC,aAAa,CAAAC,QAAA,eAC1B3G,IAAA,SAAM0G,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAC,kBAAgB,CAAM,CAAC,cACvD3G,IAAA,SAAM0G,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAEd,cAAc,CAACjE,OAAO,CAACmG,cAAc,CAAC,CAAO,CAAC,EAC5E,CAAC,EACH,CAAC,CAELnG,OAAO,CAAC6E,gBAAgB,CAAChB,MAAM,CAAG,CAAC,eAClCvF,KAAA,QAAKwG,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAChC3G,IAAA,OAAA2G,QAAA,CAAI,mBAAiB,CAAI,CAAC,cAC1B3G,IAAA,OAAA2G,QAAA,CACG/E,OAAO,CAAC6E,gBAAgB,CAACY,GAAG,CAAC,CAACrG,KAAK,CAAEiD,KAAK,gBACzC/D,KAAA,OAAgBwG,SAAS,CAAC,kBAAkB,CAAAC,QAAA,EAAC,MACvC,CAAC3F,KAAK,CAACgH,GAAG,CAAC,IAAE,CAAChH,KAAK,CAACoC,KAAK,CAAC,IAAE,CAACpC,KAAK,CAACiB,OAAO,GADvCgC,KAEL,CACL,CAAC,CACA,CAAC,EACF,CACN,cAEDjE,IAAA,QAAK0G,SAAS,CAAC,8BAA8B,CAAAC,QAAA,cAC3CzG,KAAA,UAAOwG,SAAS,CAAC,oBAAoB,CAAAC,QAAA,eACnC3G,IAAA,UAAA2G,QAAA,cACEzG,KAAA,OAAAyG,QAAA,eACE3G,IAAA,OAAA2G,QAAA,CAAI,MAAI,CAAI,CAAC,cACb3G,IAAA,OAAA2G,QAAA,CAAI,aAAW,CAAI,CAAC,cACpB3G,IAAA,OAAA2G,QAAA,CAAI,OAAK,CAAI,CAAC,cACd3G,IAAA,OAAA2G,QAAA,CAAI,QAAM,CAAI,CAAC,cACf3G,IAAA,OAAA2G,QAAA,CAAI,SAAO,CAAI,CAAC,cAChB3G,IAAA,OAAA2G,QAAA,CAAI,WAAS,CAAI,CAAC,EAChB,CAAC,CACA,CAAC,cACR3G,IAAA,UAAA2G,QAAA,CACG/E,OAAO,CAACc,YAAY,CAAC2E,GAAG,CAAC,CAACY,WAAW,CAAE9E,gBAAgB,gBACtDjD,KAAA,OAAAyG,QAAA,eACE3G,IAAA,OAAA2G,QAAA,cACE3G,IAAA,UACE4G,IAAI,CAAC,MAAM,CACXvD,KAAK,CAAE4E,WAAW,CAAC5C,IAAK,CACxB4B,QAAQ,CAAGC,CAAC,EAAKjE,qBAAqB,CAACC,YAAY,CAAEC,gBAAgB,CAAE,MAAM,CAAE+D,CAAC,CAACC,MAAM,CAAC9D,KAAK,CAAE,CAC/FqD,SAAS,CAAC,YAAY,CACvB,CAAC,CACA,CAAC,cACL1G,IAAA,OAAA2G,QAAA,cACE3G,IAAA,UACE4G,IAAI,CAAC,MAAM,CACXvD,KAAK,CAAE4E,WAAW,CAACC,WAAY,CAC/BjB,QAAQ,CAAGC,CAAC,EAAKjE,qBAAqB,CAACC,YAAY,CAAEC,gBAAgB,CAAE,aAAa,CAAE+D,CAAC,CAACC,MAAM,CAAC9D,KAAK,CAAE,CACtGqD,SAAS,CAAC,YAAY,CACvB,CAAC,CACA,CAAC,cACsB1G,IAAA,OAAA2G,QAAA,cACxB3G,IAAA,UACE4G,IAAI,CAAC,QAAQ,CACbtG,IAAI,CAAC,MAAM,CACX+C,KAAK,CAAE4E,WAAW,CAACE,WAAW,CAAGF,WAAW,CAACE,WAAW,CAACC,OAAO,CAAC,CAAC,CAAC,CAAG,EAAG,CACzEnB,QAAQ,CAAGC,CAAC,EAAKjE,qBAAqB,CAACC,YAAY,CAAEC,gBAAgB,CAAE,aAAa,CAAEkF,UAAU,CAACnB,CAAC,CAACC,MAAM,CAAC9D,KAAK,CAAC,EAAI,CAAC,CAAE,CACvHqD,SAAS,CAAC,YAAY,CACtB4B,WAAW,CAAC,MAAM,CACnB,CAAC,CACA,CAAC,cACLtI,IAAA,OAAA2G,QAAA,cACE3G,IAAA,UACE4G,IAAI,CAAC,QAAQ,CACbtG,IAAI,CAAC,MAAM,CACX+C,KAAK,CAAE4E,WAAW,CAACM,YAAY,CAAGN,WAAW,CAACM,YAAY,CAACH,OAAO,CAAC,CAAC,CAAC,CAAG,EAAG,CAC3EnB,QAAQ,CAAGC,CAAC,EAAKjE,qBAAqB,CAACC,YAAY,CAAEC,gBAAgB,CAAE,cAAc,CAAEkF,UAAU,CAACnB,CAAC,CAACC,MAAM,CAAC9D,KAAK,CAAC,EAAI,CAAC,CAAE,CACxHqD,SAAS,CAAC,YAAY,CACtB4B,WAAW,CAAC,MAAM,CACnB,CAAC,CACA,CAAC,cACLtI,IAAA,OAAA2G,QAAA,cACE3G,IAAA,UACE4G,IAAI,CAAC,QAAQ,CACbtG,IAAI,CAAC,MAAM,CACX+C,KAAK,CAAE4E,WAAW,CAACtC,OAAO,CAACyC,OAAO,CAAC,CAAC,CAAE,CACtCnB,QAAQ,CAAGC,CAAC,EAAKjE,qBAAqB,CAACC,YAAY,CAAEC,gBAAgB,CAAE,SAAS,CAAEkF,UAAU,CAACnB,CAAC,CAACC,MAAM,CAAC9D,KAAK,CAAC,EAAI,CAAC,CAAE,CACnHqD,SAAS,CAAC,YAAY,CACvB,CAAC,CACA,CAAC,cACN1G,IAAA,OAAA2G,QAAA,cACE3G,IAAA,UACE4G,IAAI,CAAC,MAAM,CACXvD,KAAK,CAAE4E,WAAW,CAACO,SAAS,EAAI,EAAG,CACnCvB,QAAQ,CAAGC,CAAC,EAAKjE,qBAAqB,CAACC,YAAY,CAAEC,gBAAgB,CAAE,WAAW,CAAE+D,CAAC,CAACC,MAAM,CAAC9D,KAAK,CAAE,CACpGqD,SAAS,CAAC,YAAY,CACvB,CAAC,CACA,CAAC,GArDEuB,WAAW,CAAC1F,EAsDjB,CACL,CAAC,CACG,CAAC,EACH,CAAC,CACL,CAAC,GA/GEW,YAgHL,CACN,CAAC,CACC,CAAC,cAENhD,KAAA,QAAKwG,SAAS,CAAC,cAAc,CAAAC,QAAA,eAC3B3G,IAAA,WACE4G,IAAI,CAAC,QAAQ,CACbC,OAAO,CAAEjB,YAAa,CACtBc,SAAS,CAAC,mBAAmB,CAAAC,QAAA,CAC9B,QAED,CAAQ,CAAC,cACT3G,IAAA,WACE4G,IAAI,CAAC,QAAQ,CACbC,OAAO,CAAEA,CAAA,GAAMtG,OAAO,CAAC,SAAS,CAAE,CAClCmG,SAAS,CAAC,iBAAiB,CAC3BK,QAAQ,CAAEP,qBAAqB,CAAG,CAAE,CAAAG,QAAA,CACrC,yBAED,CAAQ,CAAC,EACN,CAAC,EACH,CACN,CAGArG,IAAI,GAAK,SAAS,EAAII,mBAAmB,eACxCR,KAAA,QAAKwG,SAAS,CAAC,aAAa,CAAAC,QAAA,eAC1BzG,KAAA,QAAKwG,SAAS,CAAC,aAAa,CAAAC,QAAA,eAC1B3G,IAAA,OAAA2G,QAAA,CAAI,wBAAsB,CAAI,CAAC,cAC/B3G,IAAA,MAAA2G,QAAA,CAAG,mEAAiE,CAAG,CAAC,EACrE,CAAC,cAENzG,KAAA,QAAKwG,SAAS,CAAC,sBAAsB,CAAAC,QAAA,eACnCzG,KAAA,QAAKwG,SAAS,CAAC,iBAAiB,CAAAC,QAAA,eAC9B3G,IAAA,OAAA2G,QAAA,CAAI,qBAAmB,CAAI,CAAC,cAC5BzG,KAAA,QAAKwG,SAAS,CAAC,iBAAiB,CAAAC,QAAA,eAC9B3G,IAAA,MAAA2G,QAAA,cAAG3G,IAAA,WAAA2G,QAAA,CAASjG,mBAAmB,CAAC0D,IAAI,CAAS,CAAC,CAAG,CAAC,cAClDlE,KAAA,MAAAyG,QAAA,EAAIjG,mBAAmB,CAAC4G,QAAQ,CAAC,KAAG,CAAC5G,mBAAmB,CAAC6G,aAAa,EAAI,CAAC,cAC3ErH,KAAA,MAAAyG,QAAA,EAAG,mBAAiB,CAACd,cAAc,CAACnF,mBAAmB,CAACmC,cAAc,CAAC,EAAI,CAAC,EACzE,CAAC,EACH,CAAC,cAEN3C,KAAA,QAAKwG,SAAS,CAAC,iBAAiB,CAAAC,QAAA,eAC9B3G,IAAA,OAAA2G,QAAA,CAAI,gBAAc,CAAI,CAAC,cACvBzG,KAAA,QAAKwG,SAAS,CAAC,eAAe,CAAAC,QAAA,eAC5BzG,KAAA,QAAKwG,SAAS,CAAC,WAAW,CAAAC,QAAA,eACxB3G,IAAA,SAAM0G,SAAS,CAAC,YAAY,CAAAC,QAAA,CAAC,kBAAgB,CAAM,CAAC,cACpD3G,IAAA,SAAM0G,SAAS,CAAC,YAAY,CAAAC,QAAA,CAAEnG,KAAK,CAACiF,MAAM,CAAO,CAAC,EAC/C,CAAC,cACNvF,KAAA,QAAKwG,SAAS,CAAC,WAAW,CAAAC,QAAA,eACxB3G,IAAA,SAAM0G,SAAS,CAAC,YAAY,CAAAC,QAAA,CAAC,qBAAmB,CAAM,CAAC,cACvD3G,IAAA,SAAM0G,SAAS,CAAC,YAAY,CAAAC,QAAA,CAAEpC,iBAAiB,CAAO,CAAC,EACpD,CAAC,cACNrE,KAAA,QAAKwG,SAAS,CAAC,WAAW,CAAAC,QAAA,eACxB3G,IAAA,SAAM0G,SAAS,CAAC,YAAY,CAAAC,QAAA,CAAC,qBAAmB,CAAM,CAAC,cACvD3G,IAAA,SAAM0G,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAEd,cAAc,CAACS,gBAAgB,CAAC,CAAO,CAAC,EAC9E,CAAC,cACNpG,KAAA,QAAKwG,SAAS,CAAC,WAAW,CAAAC,QAAA,eACxB3G,IAAA,SAAM0G,SAAS,CAAC,YAAY,CAAAC,QAAA,CAAC,sBAAoB,CAAM,CAAC,cACxD3G,IAAA,SAAM0G,SAAS,CAAC,yBAAyB,CAAAC,QAAA,CAAEd,cAAc,CAACU,iBAAiB,CAAC,CAAO,CAAC,EACjF,CAAC,cACNrG,KAAA,QAAKwG,SAAS,CAAC,WAAW,CAAAC,QAAA,eACxB3G,IAAA,SAAM0G,SAAS,CAAC,YAAY,CAAAC,QAAA,CAAC,aAAW,CAAM,CAAC,cAC/C3G,IAAA,SAAM0G,SAAS,eAAA/B,MAAA,CAAgB4B,iBAAiB,CAAGD,gBAAgB,EAAI,CAAC,CAAG,cAAc,CAAG,YAAY,CAAG,CAAAK,QAAA,CACxGd,cAAc,CAACU,iBAAiB,CAAGD,gBAAgB,CAAC,CACjD,CAAC,EACJ,CAAC,EACH,CAAC,EACH,CAAC,EACH,CAAC,cAENpG,KAAA,QAAKwG,SAAS,CAAC,uBAAuB,CAAAC,QAAA,eACpC3G,IAAA,OAAA2G,QAAA,CAAI,0CAAwC,CAAI,CAAC,cACjD3G,IAAA,MAAA2G,QAAA,CAAG,qEAAmE,CAAG,CAAC,EACvE,CAAC,cAENzG,KAAA,QAAKwG,SAAS,CAAC,cAAc,CAAAC,QAAA,eAC3B3G,IAAA,WACE4G,IAAI,CAAC,QAAQ,CACbC,OAAO,CAAEjB,YAAa,CACtBc,SAAS,CAAC,gBAAgB,CAAAC,QAAA,CAC3B,YAED,CAAQ,CAAC,cACT3G,IAAA,WACE4G,IAAI,CAAC,QAAQ,CACbC,OAAO,CAAE/C,mBAAoB,CAC7B4C,SAAS,CAAC,wBAAwB,CAAAC,QAAA,CACnC,cAED,CAAQ,CAAC,EACN,CAAC,EACH,CACN,CAGAvF,iBAAiB,eAChBpB,IAAA,CAACF,uBAAuB,EACtB2I,MAAM,CAAEnH,iBAAkB,CAC1BoH,gBAAgB,CAAEtH,iBAAkB,CACpCuH,SAAS,CAAElF,8BAA+B,CAC1CmF,QAAQ,CAAE/E,6BAA8B,CACzC,CACF,EACE,CAAC,CAEV,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}