{"ast": null, "code": "// Is a given value equal to null?\nexport default function isNull(obj) {\n  return obj === null;\n}", "map": {"version": 3, "names": ["isNull", "obj"], "sources": ["C:/tmsft/node_modules/underscore/modules/isNull.js"], "sourcesContent": ["// Is a given value equal to null?\nexport default function isNull(obj) {\n  return obj === null;\n}\n"], "mappings": "AAAA;AACA,eAAe,SAASA,MAAMA,CAACC,GAAG,EAAE;EAClC,OAAOA,GAAG,KAAK,IAAI;AACrB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}