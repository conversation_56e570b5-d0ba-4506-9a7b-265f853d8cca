{"ast": null, "code": "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { ENGINE } from '../engine';\nimport { LeakyRelu } from '../kernel_names';\nimport { convertToTensor } from '../tensor_util_env';\nimport { op } from './operation';\n/**\n * Computes leaky rectified linear element-wise.\n *\n * See\n * [http://web.stanford.edu/~awni/papers/relu_hybrid_icml2013_final.pdf](\n *     http://web.stanford.edu/~awni/papers/relu_hybrid_icml2013_final.pdf)\n *\n * ```js\n * const x = tf.tensor1d([-1, 2, -3, 4]);\n *\n * x.leakyRelu(0.1).print();  // or tf.leakyRelu(x, 0.1)\n * ```\n * @param x The input tensor.\n * @param alpha The scaling factor for negative values, defaults to 0.2.\n *\n * @doc {heading: 'Operations', subheading: 'Basic math'}\n */\nfunction leakyRelu_(x) {\n  let alpha = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 0.2;\n  const $x = convertToTensor(x, 'x', 'leakyRelu');\n  const inputs = {\n    x: $x\n  };\n  const attrs = {\n    alpha\n  };\n  return ENGINE.runKernel(LeakyRelu, inputs, attrs);\n}\nexport const leakyRelu = /* @__PURE__ */op({\n  leakyRelu_\n});", "map": {"version": 3, "names": ["ENGINE", "LeakyRelu", "convertToTensor", "op", "leakyRelu_", "x", "alpha", "arguments", "length", "undefined", "$x", "inputs", "attrs", "runKernel", "leakyRelu"], "sources": ["C:\\tfjs-core\\src\\ops\\leaky_relu.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {ENGINE} from '../engine';\nimport {LeakyRelu, LeakyReluAttrs, LeakyReluInputs} from '../kernel_names';\nimport {NamedAttrMap} from '../kernel_registry';\nimport {Tensor} from '../tensor';\nimport {NamedTensorMap} from '../tensor_types';\nimport {convertToTensor} from '../tensor_util_env';\nimport {TensorLike} from '../types';\n\nimport {op} from './operation';\n\n/**\n * Computes leaky rectified linear element-wise.\n *\n * See\n * [http://web.stanford.edu/~awni/papers/relu_hybrid_icml2013_final.pdf](\n *     http://web.stanford.edu/~awni/papers/relu_hybrid_icml2013_final.pdf)\n *\n * ```js\n * const x = tf.tensor1d([-1, 2, -3, 4]);\n *\n * x.leakyRelu(0.1).print();  // or tf.leakyRelu(x, 0.1)\n * ```\n * @param x The input tensor.\n * @param alpha The scaling factor for negative values, defaults to 0.2.\n *\n * @doc {heading: 'Operations', subheading: 'Basic math'}\n */\nfunction leakyRelu_<T extends Tensor>(x: T|TensorLike, alpha = 0.2): T {\n  const $x = convertToTensor(x, 'x', 'leakyRelu');\n\n  const inputs: LeakyReluInputs = {x: $x};\n  const attrs: LeakyReluAttrs = {alpha};\n\n  return ENGINE.runKernel(\n      LeakyRelu, inputs as unknown as NamedTensorMap,\n      attrs as unknown as NamedAttrMap);\n}\n\nexport const leakyRelu = /* @__PURE__ */ op({leakyRelu_});\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAiBA,SAAQA,MAAM,QAAO,WAAW;AAChC,SAAQC,SAAS,QAAwC,iBAAiB;AAI1E,SAAQC,eAAe,QAAO,oBAAoB;AAGlD,SAAQC,EAAE,QAAO,aAAa;AAE9B;;;;;;;;;;;;;;;;;AAiBA,SAASC,UAAUA,CAAmBC,CAAe,EAAa;EAAA,IAAXC,KAAK,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,GAAG;EAChE,MAAMG,EAAE,GAAGR,eAAe,CAACG,CAAC,EAAE,GAAG,EAAE,WAAW,CAAC;EAE/C,MAAMM,MAAM,GAAoB;IAACN,CAAC,EAAEK;EAAE,CAAC;EACvC,MAAME,KAAK,GAAmB;IAACN;EAAK,CAAC;EAErC,OAAON,MAAM,CAACa,SAAS,CACnBZ,SAAS,EAAEU,MAAmC,EAC9CC,KAAgC,CAAC;AACvC;AAEA,OAAO,MAAME,SAAS,GAAG,eAAgBX,EAAE,CAAC;EAACC;AAAU,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}