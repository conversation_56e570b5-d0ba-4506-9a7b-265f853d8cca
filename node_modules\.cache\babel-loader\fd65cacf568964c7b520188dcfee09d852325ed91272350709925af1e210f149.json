{"ast": null, "code": "/**\n * @license\n * Copyright 2022 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { searchSorted } from './search_sorted';\n/**\n * Searches for where a value would go in a sorted sequence.\n *\n * This is not a method for checking containment (like javascript in).\n *\n * The typical use case for this operation is \"binning\", \"bucketing\", or\n * \"discretizing\". The values are assigned to bucket-indices based on the edges\n * listed in 'sortedSequence'. This operation returns the bucket-index for each\n * value.\n *\n * The index returned corresponds to the first edge greater than or equal to the\n * value.\n *\n * The axis is not settable for this operation. It always operates on the\n * innermost dimension (axis=-1). The operation will accept any number of outer\n * dimensions.\n *\n * Note: This operation assumes that 'lowerBound' is sorted along the\n * innermost axis, maybe using 'sort(..., axis=-1)'. If the sequence is not\n * sorted no error is raised and the content of the returned tensor is not well\n * defined.\n *\n * ```js\n * const edges = tf.tensor1d([-1, 3.3, 9.1, 10.0]);\n * let values = tf.tensor1d([0.0, 4.1, 12.0]);\n * const result1 = tf.lowerBound(edges, values);\n * result1.print(); // [1, 2, 4]\n *\n * const seq = tf.tensor1d([0, 3, 9, 10, 10]);\n * values = tf.tensor1d([0, 4, 10]);\n * const result2 = tf.lowerBound(seq, values);\n * result2.print(); // [0, 2, 3]\n *\n * const sortedSequence = tf.tensor2d([[0., 3., 8., 9., 10.],\n *                                     [1., 2., 3., 4., 5.]]);\n * values = tf.tensor2d([[9.8, 2.1, 4.3],\n *                       [0.1, 6.6, 4.5, ]]);\n * const result3 = tf.lowerBound(sortedSequence, values);\n * result3.print(); // [[4, 1, 2], [0, 5, 4]]\n * ```\n * @param sortedSequence: N-D. Sorted sequence.\n * @param values: N-D. Search values.\n * @return An N-D int32 tensor the size of values containing the result of\n *     applying lower bound to each value. The result is not a global index to\n *     the entire Tensor, but the index in the last dimension.\n * @doc {heading: 'Operations', subheading: 'Evaluation'}\n */\nexport function lowerBound(sortedSequence, values) {\n  return searchSorted(sortedSequence, values, 'left');\n}", "map": {"version": 3, "names": ["searchSorted", "lowerBound", "sortedSequence", "values"], "sources": ["C:\\tfjs-core\\src\\ops\\lower_bound.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2022 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {Tensor} from '../tensor';\nimport {TensorLike} from '../types';\nimport {searchSorted} from './search_sorted';\n\n/**\n * Searches for where a value would go in a sorted sequence.\n *\n * This is not a method for checking containment (like javascript in).\n *\n * The typical use case for this operation is \"binning\", \"bucketing\", or\n * \"discretizing\". The values are assigned to bucket-indices based on the edges\n * listed in 'sortedSequence'. This operation returns the bucket-index for each\n * value.\n *\n * The index returned corresponds to the first edge greater than or equal to the\n * value.\n *\n * The axis is not settable for this operation. It always operates on the\n * innermost dimension (axis=-1). The operation will accept any number of outer\n * dimensions.\n *\n * Note: This operation assumes that 'lowerBound' is sorted along the\n * innermost axis, maybe using 'sort(..., axis=-1)'. If the sequence is not\n * sorted no error is raised and the content of the returned tensor is not well\n * defined.\n *\n * ```js\n * const edges = tf.tensor1d([-1, 3.3, 9.1, 10.0]);\n * let values = tf.tensor1d([0.0, 4.1, 12.0]);\n * const result1 = tf.lowerBound(edges, values);\n * result1.print(); // [1, 2, 4]\n *\n * const seq = tf.tensor1d([0, 3, 9, 10, 10]);\n * values = tf.tensor1d([0, 4, 10]);\n * const result2 = tf.lowerBound(seq, values);\n * result2.print(); // [0, 2, 3]\n *\n * const sortedSequence = tf.tensor2d([[0., 3., 8., 9., 10.],\n *                                     [1., 2., 3., 4., 5.]]);\n * values = tf.tensor2d([[9.8, 2.1, 4.3],\n *                       [0.1, 6.6, 4.5, ]]);\n * const result3 = tf.lowerBound(sortedSequence, values);\n * result3.print(); // [[4, 1, 2], [0, 5, 4]]\n * ```\n * @param sortedSequence: N-D. Sorted sequence.\n * @param values: N-D. Search values.\n * @return An N-D int32 tensor the size of values containing the result of\n *     applying lower bound to each value. The result is not a global index to\n *     the entire Tensor, but the index in the last dimension.\n * @doc {heading: 'Operations', subheading: 'Evaluation'}\n */\nexport function lowerBound(\n    sortedSequence: Tensor|TensorLike, values: Tensor|TensorLike): Tensor {\n  return searchSorted(sortedSequence, values, 'left');\n}\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAmBA,SAAQA,YAAY,QAAO,iBAAiB;AAE5C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA+CA,OAAM,SAAUC,UAAUA,CACtBC,cAAiC,EAAEC,MAAyB;EAC9D,OAAOH,YAAY,CAACE,cAAc,EAAEC,MAAM,EAAE,MAAM,CAAC;AACrD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}