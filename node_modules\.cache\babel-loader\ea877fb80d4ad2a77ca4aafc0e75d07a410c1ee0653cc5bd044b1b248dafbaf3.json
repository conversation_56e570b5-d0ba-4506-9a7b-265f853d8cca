{"ast": null, "code": "/**\n * @license\n * Copyright 2020 Google Inc. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { TensorBuffer } from '../tensor';\nimport { convertToTensor } from '../tensor_util_env';\nimport * as util from '../util';\n/**\n * Computes the difference between two lists of numbers.\n *\n * Given a Tensor `x` and a Tensor `y`, this operation returns a Tensor `out`\n * that represents all values that are in `x` but not in `y`. The returned\n * Tensor `out` is sorted in the same order that the numbers appear in `x`\n * (duplicates are preserved). This operation also returns a Tensor indices that\n * represents the position of each out element in `x`. In other words:\n *\n * `out[i] = x[idx[i]] for i in [0, 1, ..., out.length - 1]`\n *\n * ```js\n * const x = [1, 2, 3, 4, 5, 6];\n * const y = [1, 3, 5];\n *\n * const [out, indices] = await tf.setdiff1dAsync(x, y);\n * out.print(); // [2, 4, 6]\n * indices.print(); // [1, 3, 5]\n * ```\n *\n * @param x 1-D Tensor. Values to keep.\n * @param y 1-D Tensor. Must have the same type as x. Values to exclude in the\n *     output.\n * @returns Promise of Tensor tuple [out, indices].\n *  out: Tensor with the same type as x.\n *  indices: A Tensor of type int32.\n *\n * @doc {heading: 'Tensors', subheading: 'Transformations'}\n */\nasync function setdiff1dAsync_(x, y) {\n  const $x = convertToTensor(x, 'x', 'setdiff1d');\n  const $y = convertToTensor(y, 'y', 'setdiff1d');\n  util.assert($x.dtype === $y.dtype, () => \"x and y should have the same dtype, but got x (\".concat($x.dtype, \") and y (\").concat($y.dtype, \").\"));\n  util.assert($x.rank === 1, () => \"x should be 1D tensor, but got x (\".concat($x.shape, \").\"));\n  util.assert($y.rank === 1, () => \"y should be 1D tensor, but got y (\".concat($y.shape, \").\"));\n  const xVals = await $x.data();\n  const yVals = await $y.data();\n  const ySet = new Set(yVals);\n  let outputSize = 0;\n  for (let i = 0; i < xVals.length; i++) {\n    if (!ySet.has(xVals[i])) {\n      outputSize++;\n    }\n  }\n  const buffer = new TensorBuffer([outputSize], $x.dtype);\n  const indices = new TensorBuffer([outputSize], 'int32');\n  for (let i = 0, p = 0; i < xVals.length; i++) {\n    if (!ySet.has(xVals[i])) {\n      buffer.values[p] = xVals[i];\n      indices.values[p] = i;\n      p++;\n    }\n  }\n  return [buffer.toTensor(), indices.toTensor()];\n}\nexport const setdiff1dAsync = setdiff1dAsync_;", "map": {"version": 3, "names": ["Tensor<PERSON><PERSON><PERSON>", "convertToTensor", "util", "setdiff1dAsync_", "x", "y", "$x", "$y", "assert", "dtype", "concat", "rank", "shape", "xVals", "data", "yVals", "ySet", "Set", "outputSize", "i", "length", "has", "buffer", "indices", "p", "values", "toTensor", "setdiff1dAsync"], "sources": ["C:\\tfjs-core\\src\\ops\\setdiff1d_async.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google Inc. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport {Tensor, TensorBuffer} from '../tensor';\nimport {convertToTensor} from '../tensor_util_env';\nimport {TensorLike} from '../types';\nimport * as util from '../util';\n\n/**\n * Computes the difference between two lists of numbers.\n *\n * Given a Tensor `x` and a Tensor `y`, this operation returns a Tensor `out`\n * that represents all values that are in `x` but not in `y`. The returned\n * Tensor `out` is sorted in the same order that the numbers appear in `x`\n * (duplicates are preserved). This operation also returns a Tensor indices that\n * represents the position of each out element in `x`. In other words:\n *\n * `out[i] = x[idx[i]] for i in [0, 1, ..., out.length - 1]`\n *\n * ```js\n * const x = [1, 2, 3, 4, 5, 6];\n * const y = [1, 3, 5];\n *\n * const [out, indices] = await tf.setdiff1dAsync(x, y);\n * out.print(); // [2, 4, 6]\n * indices.print(); // [1, 3, 5]\n * ```\n *\n * @param x 1-D Tensor. Values to keep.\n * @param y 1-D Tensor. Must have the same type as x. Values to exclude in the\n *     output.\n * @returns Promise of Tensor tuple [out, indices].\n *  out: Tensor with the same type as x.\n *  indices: A Tensor of type int32.\n *\n * @doc {heading: 'Tensors', subheading: 'Transformations'}\n */\nasync function setdiff1dAsync_(\n    x: Tensor|TensorLike, y: Tensor|TensorLike): Promise<[Tensor, Tensor]> {\n  const $x = convertToTensor(x, 'x', 'setdiff1d');\n  const $y = convertToTensor(y, 'y', 'setdiff1d');\n\n  util.assert(\n      $x.dtype === $y.dtype,\n      () => `x and y should have the same dtype, but got x (${\n          $x.dtype}) and y (${$y.dtype}).`);\n\n  util.assert(\n      $x.rank === 1, () => `x should be 1D tensor, but got x (${$x.shape}).`);\n\n  util.assert(\n      $y.rank === 1, () => `y should be 1D tensor, but got y (${$y.shape}).`);\n\n  const xVals = await $x.data();\n  const yVals = await $y.data();\n  const ySet = new Set(yVals);\n\n  let outputSize = 0;\n  for (let i = 0; i < xVals.length; i++) {\n    if (!ySet.has(xVals[i])) {\n      outputSize++;\n    }\n  }\n\n  const buffer = new TensorBuffer([outputSize], $x.dtype);\n  const indices = new TensorBuffer([outputSize], 'int32');\n  for (let i = 0, p = 0; i < xVals.length; i++) {\n    if (!ySet.has(xVals[i])) {\n      buffer.values[p] = xVals[i];\n      indices.values[p] = i;\n      p++;\n    }\n  }\n  return [buffer.toTensor(), indices.toTensor()];\n}\nexport const setdiff1dAsync = setdiff1dAsync_;\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAgBA,SAAgBA,YAAY,QAAO,WAAW;AAC9C,SAAQC,eAAe,QAAO,oBAAoB;AAElD,OAAO,KAAKC,IAAI,MAAM,SAAS;AAE/B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6BA,eAAeC,eAAeA,CAC1BC,CAAoB,EAAEC,CAAoB;EAC5C,MAAMC,EAAE,GAAGL,eAAe,CAACG,CAAC,EAAE,GAAG,EAAE,WAAW,CAAC;EAC/C,MAAMG,EAAE,GAAGN,eAAe,CAACI,CAAC,EAAE,GAAG,EAAE,WAAW,CAAC;EAE/CH,IAAI,CAACM,MAAM,CACPF,EAAE,CAACG,KAAK,KAAKF,EAAE,CAACE,KAAK,EACrB,wDAAAC,MAAA,CACIJ,EAAE,CAACG,KAAK,eAAAC,MAAA,CAAYH,EAAE,CAACE,KAAK,OAAI,CAAC;EAEzCP,IAAI,CAACM,MAAM,CACPF,EAAE,CAACK,IAAI,KAAK,CAAC,EAAE,2CAAAD,MAAA,CAA2CJ,EAAE,CAACM,KAAK,OAAI,CAAC;EAE3EV,IAAI,CAACM,MAAM,CACPD,EAAE,CAACI,IAAI,KAAK,CAAC,EAAE,2CAAAD,MAAA,CAA2CH,EAAE,CAACK,KAAK,OAAI,CAAC;EAE3E,MAAMC,KAAK,GAAG,MAAMP,EAAE,CAACQ,IAAI,EAAE;EAC7B,MAAMC,KAAK,GAAG,MAAMR,EAAE,CAACO,IAAI,EAAE;EAC7B,MAAME,IAAI,GAAG,IAAIC,GAAG,CAACF,KAAK,CAAC;EAE3B,IAAIG,UAAU,GAAG,CAAC;EAClB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGN,KAAK,CAACO,MAAM,EAAED,CAAC,EAAE,EAAE;IACrC,IAAI,CAACH,IAAI,CAACK,GAAG,CAACR,KAAK,CAACM,CAAC,CAAC,CAAC,EAAE;MACvBD,UAAU,EAAE;;;EAIhB,MAAMI,MAAM,GAAG,IAAItB,YAAY,CAAC,CAACkB,UAAU,CAAC,EAAEZ,EAAE,CAACG,KAAK,CAAC;EACvD,MAAMc,OAAO,GAAG,IAAIvB,YAAY,CAAC,CAACkB,UAAU,CAAC,EAAE,OAAO,CAAC;EACvD,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEK,CAAC,GAAG,CAAC,EAAEL,CAAC,GAAGN,KAAK,CAACO,MAAM,EAAED,CAAC,EAAE,EAAE;IAC5C,IAAI,CAACH,IAAI,CAACK,GAAG,CAACR,KAAK,CAACM,CAAC,CAAC,CAAC,EAAE;MACvBG,MAAM,CAACG,MAAM,CAACD,CAAC,CAAC,GAAGX,KAAK,CAACM,CAAC,CAAC;MAC3BI,OAAO,CAACE,MAAM,CAACD,CAAC,CAAC,GAAGL,CAAC;MACrBK,CAAC,EAAE;;;EAGP,OAAO,CAACF,MAAM,CAACI,QAAQ,EAAE,EAAEH,OAAO,CAACG,QAAQ,EAAE,CAAC;AAChD;AACA,OAAO,MAAMC,cAAc,GAAGxB,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}