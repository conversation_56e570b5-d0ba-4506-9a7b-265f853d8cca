{"ast": null, "code": "/**\n * @license\n * Copyright 2023 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nexport const json = [{\n  'tfOpName': 'AvgPool',\n  'category': 'convolution',\n  'inputs': [{\n    'start': 0,\n    'name': 'x',\n    'type': 'tensor'\n  }],\n  'attrs': [{\n    'tfName': 'strides',\n    'name': 'strides',\n    'type': 'number[]'\n  }, {\n    'tfName': 'padding',\n    'name': 'pad',\n    'type': 'string'\n  }, {\n    'tfName': 'data_format',\n    'name': 'dataFormat',\n    'type': 'string',\n    'notSupported': true\n  }, {\n    'tfName': 'ksize',\n    'name': 'kernelSize',\n    'type': 'number[]'\n  }, {\n    'tfName': 'T',\n    'name': 'dtype',\n    'type': 'dtype',\n    'notSupported': true\n  }]\n}, {\n  'tfOpName': 'MaxPool',\n  'category': 'convolution',\n  'inputs': [{\n    'start': 0,\n    'name': 'x',\n    'type': 'tensor'\n  }],\n  'attrs': [{\n    'tfName': 'strides',\n    'name': 'strides',\n    'type': 'number[]'\n  }, {\n    'tfName': 'padding',\n    'name': 'pad',\n    'type': 'string'\n  }, {\n    'tfName': 'data_format',\n    'name': 'dataFormat',\n    'type': 'string',\n    'notSupported': true\n  }, {\n    'tfName': 'ksize',\n    'name': 'kernelSize',\n    'type': 'number[]'\n  }, {\n    'tfName': 'explicit_paddings',\n    'name': 'explicitPaddings',\n    'type': 'number[]',\n    'defaultValue': [],\n    'notSupported': true\n  }, {\n    'tfName': 'T',\n    'name': 'dtype',\n    'type': 'dtype',\n    'notSupported': true\n  }]\n}, {\n  'tfOpName': 'MaxPoolWithArgmax',\n  'category': 'convolution',\n  'inputs': [{\n    'start': 0,\n    'name': 'x',\n    'type': 'tensor'\n  }],\n  'attrs': [{\n    'tfName': 'strides',\n    'name': 'strides',\n    'type': 'number[]'\n  }, {\n    'tfName': 'padding',\n    'name': 'pad',\n    'type': 'string'\n  }, {\n    'tfName': 'ksize',\n    'name': 'kernelSize',\n    'type': 'number[]'\n  }, {\n    'tfName': 'include_batch_in_index',\n    'name': 'includeBatchInIndex',\n    'type': 'bool'\n  }, {\n    'tfName': 'T',\n    'name': 'dtype',\n    'type': 'dtype',\n    'notSupported': true\n  }]\n}, {\n  'tfOpName': 'AvgPool3D',\n  'category': 'convolution',\n  'inputs': [{\n    'start': 0,\n    'name': 'x',\n    'type': 'tensor'\n  }],\n  'attrs': [{\n    'tfName': 'strides',\n    'name': 'strides',\n    'type': 'number[]'\n  }, {\n    'tfName': 'padding',\n    'name': 'pad',\n    'type': 'string'\n  }, {\n    'tfName': 'data_format',\n    'name': 'dataFormat',\n    'type': 'string',\n    'notSupported': true\n  }, {\n    'tfName': 'ksize',\n    'name': 'kernelSize',\n    'type': 'number[]'\n  }, {\n    'tfName': 'T',\n    'name': 'dtype',\n    'type': 'dtype',\n    'notSupported': true\n  }]\n}, {\n  'tfOpName': 'MaxPool3D',\n  'category': 'convolution',\n  'inputs': [{\n    'start': 0,\n    'name': 'x',\n    'type': 'tensor'\n  }],\n  'attrs': [{\n    'tfName': 'strides',\n    'name': 'strides',\n    'type': 'number[]'\n  }, {\n    'tfName': 'padding',\n    'name': 'pad',\n    'type': 'string'\n  }, {\n    'tfName': 'data_format',\n    'name': 'dataFormat',\n    'type': 'string',\n    'notSupported': true\n  }, {\n    'tfName': 'ksize',\n    'name': 'kernelSize',\n    'type': 'number[]'\n  }, {\n    'tfName': 'T',\n    'name': 'dtype',\n    'type': 'dtype',\n    'notSupported': true\n  }]\n}, {\n  'tfOpName': 'Conv1D',\n  'category': 'convolution',\n  'inputs': [{\n    'start': 0,\n    'name': 'x',\n    'type': 'tensor'\n  }, {\n    'start': 1,\n    'name': 'filter',\n    'type': 'tensor'\n  }],\n  'attrs': [{\n    'tfName': 'stride',\n    'name': 'stride',\n    'type': 'number'\n  }, {\n    'tfName': 'padding',\n    'name': 'pad',\n    'type': 'string'\n  }, {\n    'tfName': 'data_format',\n    'name': 'dataFormat',\n    'type': 'string',\n    'defaultValue': 'NWC'\n  }, {\n    'tfName': 'T',\n    'name': 'dtype',\n    'type': 'dtype',\n    'notSupported': true\n  }, {\n    'tfName': 'dilation',\n    'name': 'dilation',\n    'type': 'number',\n    'defaultValue': 1\n  }]\n}, {\n  'tfOpName': 'Conv2D',\n  'category': 'convolution',\n  'inputs': [{\n    'start': 0,\n    'name': 'x',\n    'type': 'tensor'\n  }, {\n    'start': 1,\n    'name': 'filter',\n    'type': 'tensor'\n  }],\n  'attrs': [{\n    'tfName': 'T',\n    'name': 'dtype',\n    'type': 'dtype',\n    'notSupported': true\n  }, {\n    'tfName': 'strides',\n    'name': 'strides',\n    'type': 'number[]'\n  }, {\n    'tfName': 'padding',\n    'name': 'pad',\n    'type': 'string'\n  }, {\n    'tfName': 'useCudnnOnGpu',\n    'name': 'useCudnnOnGpu',\n    'type': 'bool'\n  }, {\n    'tfName': 'data_format',\n    'name': 'dataFormat',\n    'type': 'string',\n    'defaultValue': 'NHWC'\n  }, {\n    'tfName': 'explicit_paddings',\n    'name': 'explicitPaddings',\n    'type': 'number[]',\n    'defaultValue': []\n  }, {\n    'tfName': 'dilations',\n    'name': 'dilations',\n    'type': 'number[]'\n  }]\n}, {\n  'tfOpName': '_FusedConv2D',\n  'category': 'convolution',\n  'inputs': [{\n    'start': 0,\n    'name': 'x',\n    'type': 'tensor'\n  }, {\n    'start': 1,\n    'name': 'filter',\n    'type': 'tensor'\n  }, {\n    'start': 2,\n    'end': 0,\n    'name': 'args',\n    'type': 'tensors'\n  }],\n  'attrs': [{\n    'tfName': 'num_args',\n    'name': 'numArgs',\n    'type': 'number'\n  }, {\n    'tfName': 'T',\n    'name': 'dtype',\n    'type': 'dtype',\n    'notSupported': true\n  }, {\n    'tfName': 'strides',\n    'name': 'strides',\n    'type': 'number[]'\n  }, {\n    'tfName': 'padding',\n    'name': 'pad',\n    'type': 'string'\n  }, {\n    'tfName': 'explicit_paddings',\n    'name': 'explicitPaddings',\n    'type': 'number[]',\n    'defaultValue': []\n  }, {\n    'tfName': 'use_cudnn_on_gpu',\n    'name': 'useCudnnOnGpu',\n    'type': 'bool',\n    'defaultValue': true\n  }, {\n    'tfName': 'data_format',\n    'name': 'dataFormat',\n    'type': 'string',\n    'defaultValue': 'NHWC'\n  }, {\n    'tfName': 'dilations',\n    'name': 'dilations',\n    'type': 'number[]',\n    'defaultValue': [1, 1, 1, 1]\n  }, {\n    'tfName': 'fused_ops',\n    'name': 'fusedOps',\n    'type': 'string[]',\n    'defaultValue': []\n  }, {\n    'tfName': 'epsilon',\n    'name': 'epsilon',\n    'type': 'number',\n    'defaultValue': 0.0001\n  }, {\n    'tfName': 'leakyrelu_alpha',\n    'name': 'leakyreluAlpha',\n    'type': 'number',\n    'defaultValue': 0.2\n  }]\n}, {\n  'tfOpName': 'Conv2DBackpropInput',\n  'category': 'convolution',\n  'inputs': [{\n    'start': 2,\n    'name': 'x',\n    'type': 'tensor'\n  }, {\n    'start': 1,\n    'name': 'filter',\n    'type': 'tensor'\n  }, {\n    'start': 0,\n    'name': 'outputShape',\n    'type': 'number[]'\n  }],\n  'attrs': [{\n    'tfName': 'strides',\n    'name': 'strides',\n    'type': 'number[]'\n  }, {\n    'tfName': 'padding',\n    'name': 'pad',\n    'type': 'string'\n  }, {\n    'tfName': 'data_format',\n    'name': 'dataFormat',\n    'type': 'string',\n    'notSupported': true\n  }, {\n    'tfName': 'explicit_paddings',\n    'name': 'explicitPaddings',\n    'type': 'number[]',\n    'defaultValue': []\n  }, {\n    'tfName': 'dilations',\n    'name': 'dilations',\n    'type': 'number[]',\n    'notSupported': true\n  }]\n}, {\n  'tfOpName': 'DepthwiseConv2d',\n  'category': 'convolution',\n  'inputs': [{\n    'start': 0,\n    'name': 'input',\n    'type': 'tensor'\n  }, {\n    'start': 1,\n    'name': 'filter',\n    'type': 'tensor'\n  }],\n  'attrs': [{\n    'tfName': 'strides',\n    'name': 'strides',\n    'type': 'number[]'\n  }, {\n    'tfName': 'padding',\n    'name': 'pad',\n    'type': 'string'\n  }, {\n    'tfName': 'data_format',\n    'name': 'dataFormat',\n    'type': 'string',\n    'defaultValue': 'NHWC'\n  }, {\n    'tfName': 'explicit_paddings',\n    'name': 'explicitPaddings',\n    'type': 'number[]',\n    'defaultValue': []\n  }, {\n    'tfName': 'dilations',\n    'name': 'dilations',\n    'type': 'number[]'\n  }]\n}, {\n  'tfOpName': 'DepthwiseConv2dNative',\n  'category': 'convolution',\n  'inputs': [{\n    'start': 0,\n    'name': 'input',\n    'type': 'tensor'\n  }, {\n    'start': 1,\n    'name': 'filter',\n    'type': 'tensor'\n  }],\n  'attrs': [{\n    'tfName': 'strides',\n    'name': 'strides',\n    'type': 'number[]'\n  }, {\n    'tfName': 'padding',\n    'name': 'pad',\n    'type': 'string'\n  }, {\n    'tfName': 'data_format',\n    'name': 'dataFormat',\n    'type': 'string',\n    'defaultValue': 'NHWC'\n  }, {\n    'tfName': 'explicit_paddings',\n    'name': 'explicitPaddings',\n    'type': 'number[]',\n    'defaultValue': []\n  }, {\n    'tfName': 'dilations',\n    'name': 'dilations',\n    'type': 'number[]'\n  }]\n}, {\n  'tfOpName': 'FusedDepthwiseConv2dNative',\n  'category': 'convolution',\n  'inputs': [{\n    'start': 0,\n    'name': 'x',\n    'type': 'tensor'\n  }, {\n    'start': 1,\n    'name': 'filter',\n    'type': 'tensor'\n  }, {\n    'start': 2,\n    'end': 0,\n    'name': 'args',\n    'type': 'tensors'\n  }],\n  'attrs': [{\n    'tfName': 'num_args',\n    'name': 'numArgs',\n    'type': 'number'\n  }, {\n    'tfName': 'T',\n    'name': 'dtype',\n    'type': 'dtype',\n    'notSupported': true\n  }, {\n    'tfName': 'strides',\n    'name': 'strides',\n    'type': 'number[]'\n  }, {\n    'tfName': 'padding',\n    'name': 'pad',\n    'type': 'string'\n  }, {\n    'tfName': 'data_format',\n    'name': 'dataFormat',\n    'type': 'string',\n    'defaultValue': 'NHWC'\n  }, {\n    'tfName': 'dilations',\n    'name': 'dilations',\n    'type': 'number[]',\n    'defaultValue': [1, 1, 1, 1]\n  }, {\n    'tfName': 'fused_ops',\n    'name': 'fusedOps',\n    'type': 'string[]',\n    'defaultValue': []\n  }, {\n    'tfName': 'explicit_paddings',\n    'name': 'explicitPaddings',\n    'type': 'number[]',\n    'defaultValue': []\n  }]\n}, {\n  'tfOpName': 'Conv3D',\n  'category': 'convolution',\n  'inputs': [{\n    'start': 0,\n    'name': 'x',\n    'type': 'tensor'\n  }, {\n    'start': 1,\n    'name': 'filter',\n    'type': 'tensor'\n  }],\n  'attrs': [{\n    'tfName': 'strides',\n    'name': 'strides',\n    'type': 'number[]'\n  }, {\n    'tfName': 'padding',\n    'name': 'pad',\n    'type': 'string'\n  }, {\n    'tfName': 'data_format',\n    'name': 'dataFormat',\n    'type': 'string',\n    'defaultValue': 'NHWC'\n  }, {\n    'tfName': 'dilations',\n    'name': 'dilations',\n    'type': 'number[]'\n  }]\n}, {\n  'tfOpName': 'Dilation2D',\n  'category': 'convolution',\n  'inputs': [{\n    'start': 0,\n    'name': 'x',\n    'type': 'tensor'\n  }, {\n    'start': 1,\n    'name': 'filter',\n    'type': 'tensor'\n  }],\n  'attrs': [{\n    'tfName': 'strides',\n    'name': 'strides',\n    'type': 'number[]'\n  }, {\n    'tfName': 'rates',\n    'name': 'dilations',\n    'type': 'number[]'\n  }, {\n    'tfName': 'padding',\n    'name': 'pad',\n    'type': 'string'\n  }]\n}];", "map": {"version": 3, "names": ["json"], "sources": ["C:\\tfjs-converter\\src\\operations\\op_list\\convolution.ts"], "sourcesContent": ["\n/**\n * @license\n * Copyright 2023 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {OpMapper} from '../types';\n\nexport const json: OpMapper[] = [\n  {\n    'tfOpName': 'AvgPool',\n    'category': 'convolution',\n    'inputs': [\n      {\n        'start': 0,\n        'name': 'x',\n        'type': 'tensor'\n      }\n    ],\n    'attrs': [\n      {\n        'tfName': 'strides',\n        'name': 'strides',\n        'type': 'number[]'\n      },\n      {\n        'tfName': 'padding',\n        'name': 'pad',\n        'type': 'string'\n      },\n      {\n        'tfName': 'data_format',\n        'name': 'dataFormat',\n        'type': 'string',\n        'notSupported': true\n      },\n      {\n        'tfName': 'ksize',\n        'name': 'kernelSize',\n        'type': 'number[]'\n      },\n      {\n        'tfName': 'T',\n        'name': 'dtype',\n        'type': 'dtype',\n        'notSupported': true\n      }\n    ]\n  },\n  {\n    'tfOpName': 'MaxPool',\n    'category': 'convolution',\n    'inputs': [\n      {\n        'start': 0,\n        'name': 'x',\n        'type': 'tensor'\n      }\n    ],\n    'attrs': [\n      {\n        'tfName': 'strides',\n        'name': 'strides',\n        'type': 'number[]'\n      },\n      {\n        'tfName': 'padding',\n        'name': 'pad',\n        'type': 'string'\n      },\n      {\n        'tfName': 'data_format',\n        'name': 'dataFormat',\n        'type': 'string',\n        'notSupported': true\n      },\n      {\n        'tfName': 'ksize',\n        'name': 'kernelSize',\n        'type': 'number[]'\n      },\n      {\n        'tfName': 'explicit_paddings',\n        'name': 'explicitPaddings',\n        'type': 'number[]',\n        'defaultValue': [],\n        'notSupported': true\n      },\n      {\n        'tfName': 'T',\n        'name': 'dtype',\n        'type': 'dtype',\n        'notSupported': true\n      }\n    ]\n  },\n  {\n    'tfOpName': 'MaxPoolWithArgmax',\n    'category': 'convolution',\n    'inputs': [\n      {\n        'start': 0,\n        'name': 'x',\n        'type': 'tensor'\n      }\n    ],\n    'attrs': [\n      {\n        'tfName': 'strides',\n        'name': 'strides',\n        'type': 'number[]'\n      },\n      {\n        'tfName': 'padding',\n        'name': 'pad',\n        'type': 'string'\n      },\n      {\n        'tfName': 'ksize',\n        'name': 'kernelSize',\n        'type': 'number[]'\n      },\n      {\n        'tfName': 'include_batch_in_index',\n        'name': 'includeBatchInIndex',\n        'type': 'bool'\n      },\n      {\n        'tfName': 'T',\n        'name': 'dtype',\n        'type': 'dtype',\n        'notSupported': true\n      }\n    ]\n  },\n  {\n    'tfOpName': 'AvgPool3D',\n    'category': 'convolution',\n    'inputs': [\n      {\n        'start': 0,\n        'name': 'x',\n        'type': 'tensor'\n      }\n    ],\n    'attrs': [\n      {\n        'tfName': 'strides',\n        'name': 'strides',\n        'type': 'number[]'\n      },\n      {\n        'tfName': 'padding',\n        'name': 'pad',\n        'type': 'string'\n      },\n      {\n        'tfName': 'data_format',\n        'name': 'dataFormat',\n        'type': 'string',\n        'notSupported': true\n      },\n      {\n        'tfName': 'ksize',\n        'name': 'kernelSize',\n        'type': 'number[]'\n      },\n      {\n        'tfName': 'T',\n        'name': 'dtype',\n        'type': 'dtype',\n        'notSupported': true\n      }\n    ]\n  },\n  {\n    'tfOpName': 'MaxPool3D',\n    'category': 'convolution',\n    'inputs': [\n      {\n        'start': 0,\n        'name': 'x',\n        'type': 'tensor'\n      }\n    ],\n    'attrs': [\n      {\n        'tfName': 'strides',\n        'name': 'strides',\n        'type': 'number[]'\n      },\n      {\n        'tfName': 'padding',\n        'name': 'pad',\n        'type': 'string'\n      },\n      {\n        'tfName': 'data_format',\n        'name': 'dataFormat',\n        'type': 'string',\n        'notSupported': true\n      },\n      {\n        'tfName': 'ksize',\n        'name': 'kernelSize',\n        'type': 'number[]'\n      },\n      {\n        'tfName': 'T',\n        'name': 'dtype',\n        'type': 'dtype',\n        'notSupported': true\n      }\n    ]\n  },\n  {\n    'tfOpName': 'Conv1D',\n    'category': 'convolution',\n    'inputs': [\n      {\n        'start': 0,\n        'name': 'x',\n        'type': 'tensor'\n      },\n      {\n        'start': 1,\n        'name': 'filter',\n        'type': 'tensor'\n      }\n    ],\n    'attrs': [\n      {\n        'tfName': 'stride',\n        'name': 'stride',\n        'type': 'number'\n      },\n      {\n        'tfName': 'padding',\n        'name': 'pad',\n        'type': 'string'\n      },\n      {\n        'tfName': 'data_format',\n        'name': 'dataFormat',\n        'type': 'string',\n        'defaultValue': 'NWC'\n      },\n      {\n        'tfName': 'T',\n        'name': 'dtype',\n        'type': 'dtype',\n        'notSupported': true\n      },\n      {\n        'tfName': 'dilation',\n        'name': 'dilation',\n        'type': 'number',\n        'defaultValue': 1\n      }\n    ]\n  },\n  {\n    'tfOpName': 'Conv2D',\n    'category': 'convolution',\n    'inputs': [\n      {\n        'start': 0,\n        'name': 'x',\n        'type': 'tensor'\n      },\n      {\n        'start': 1,\n        'name': 'filter',\n        'type': 'tensor'\n      }\n    ],\n    'attrs': [\n      {\n        'tfName': 'T',\n        'name': 'dtype',\n        'type': 'dtype',\n        'notSupported': true\n      },\n      {\n        'tfName': 'strides',\n        'name': 'strides',\n        'type': 'number[]'\n      },\n      {\n        'tfName': 'padding',\n        'name': 'pad',\n        'type': 'string'\n      },\n      {\n        'tfName': 'useCudnnOnGpu',\n        'name': 'useCudnnOnGpu',\n        'type': 'bool'\n      },\n      {\n        'tfName': 'data_format',\n        'name': 'dataFormat',\n        'type': 'string',\n        'defaultValue': 'NHWC'\n      },\n      {\n        'tfName': 'explicit_paddings',\n        'name': 'explicitPaddings',\n        'type': 'number[]',\n        'defaultValue': []\n      },\n      {\n        'tfName': 'dilations',\n        'name': 'dilations',\n        'type': 'number[]'\n      }\n    ]\n  },\n  {\n    'tfOpName': '_FusedConv2D',\n    'category': 'convolution',\n    'inputs': [\n      {\n        'start': 0,\n        'name': 'x',\n        'type': 'tensor'\n      },\n      {\n        'start': 1,\n        'name': 'filter',\n        'type': 'tensor'\n      },\n      {\n        'start': 2,\n        'end': 0,\n        'name': 'args',\n        'type': 'tensors'\n      }\n    ],\n    'attrs': [\n      {\n        'tfName': 'num_args',\n        'name': 'numArgs',\n        'type': 'number'\n      },\n      {\n        'tfName': 'T',\n        'name': 'dtype',\n        'type': 'dtype',\n        'notSupported': true\n      },\n      {\n        'tfName': 'strides',\n        'name': 'strides',\n        'type': 'number[]'\n      },\n      {\n        'tfName': 'padding',\n        'name': 'pad',\n        'type': 'string'\n      },\n      {\n        'tfName': 'explicit_paddings',\n        'name': 'explicitPaddings',\n        'type': 'number[]',\n        'defaultValue': []\n      },\n      {\n        'tfName': 'use_cudnn_on_gpu',\n        'name': 'useCudnnOnGpu',\n        'type': 'bool',\n        'defaultValue': true\n      },\n      {\n        'tfName': 'data_format',\n        'name': 'dataFormat',\n        'type': 'string',\n        'defaultValue': 'NHWC'\n      },\n      {\n        'tfName': 'dilations',\n        'name': 'dilations',\n        'type': 'number[]',\n        'defaultValue': [\n          1,\n          1,\n          1,\n          1\n        ]\n      },\n      {\n        'tfName': 'fused_ops',\n        'name': 'fusedOps',\n        'type': 'string[]',\n        'defaultValue': []\n      },\n      {\n        'tfName': 'epsilon',\n        'name': 'epsilon',\n        'type': 'number',\n        'defaultValue': 0.0001\n      },\n      {\n        'tfName': 'leakyrelu_alpha',\n        'name': 'leakyreluAlpha',\n        'type': 'number',\n        'defaultValue': 0.2\n      }\n    ]\n  },\n  {\n    'tfOpName': 'Conv2DBackpropInput',\n    'category': 'convolution',\n    'inputs': [\n      {\n        'start': 2,\n        'name': 'x',\n        'type': 'tensor'\n      },\n      {\n        'start': 1,\n        'name': 'filter',\n        'type': 'tensor'\n      },\n      {\n        'start': 0,\n        'name': 'outputShape',\n        'type': 'number[]'\n      }\n    ],\n    'attrs': [\n      {\n        'tfName': 'strides',\n        'name': 'strides',\n        'type': 'number[]'\n      },\n      {\n        'tfName': 'padding',\n        'name': 'pad',\n        'type': 'string'\n      },\n      {\n        'tfName': 'data_format',\n        'name': 'dataFormat',\n        'type': 'string',\n        'notSupported': true\n      },\n      {\n        'tfName': 'explicit_paddings',\n        'name': 'explicitPaddings',\n        'type': 'number[]',\n        'defaultValue': []\n      },\n      {\n        'tfName': 'dilations',\n        'name': 'dilations',\n        'type': 'number[]',\n        'notSupported': true\n      }\n    ]\n  },\n  {\n    'tfOpName': 'DepthwiseConv2d',\n    'category': 'convolution',\n    'inputs': [\n      {\n        'start': 0,\n        'name': 'input',\n        'type': 'tensor'\n      },\n      {\n        'start': 1,\n        'name': 'filter',\n        'type': 'tensor'\n      }\n    ],\n    'attrs': [\n      {\n        'tfName': 'strides',\n        'name': 'strides',\n        'type': 'number[]'\n      },\n      {\n        'tfName': 'padding',\n        'name': 'pad',\n        'type': 'string'\n      },\n      {\n        'tfName': 'data_format',\n        'name': 'dataFormat',\n        'type': 'string',\n        'defaultValue': 'NHWC'\n      },\n      {\n        'tfName': 'explicit_paddings',\n        'name': 'explicitPaddings',\n        'type': 'number[]',\n        'defaultValue': []\n      },\n      {\n        'tfName': 'dilations',\n        'name': 'dilations',\n        'type': 'number[]'\n      }\n    ]\n  },\n  {\n    'tfOpName': 'DepthwiseConv2dNative',\n    'category': 'convolution',\n    'inputs': [\n      {\n        'start': 0,\n        'name': 'input',\n        'type': 'tensor'\n      },\n      {\n        'start': 1,\n        'name': 'filter',\n        'type': 'tensor'\n      }\n    ],\n    'attrs': [\n      {\n        'tfName': 'strides',\n        'name': 'strides',\n        'type': 'number[]'\n      },\n      {\n        'tfName': 'padding',\n        'name': 'pad',\n        'type': 'string'\n      },\n      {\n        'tfName': 'data_format',\n        'name': 'dataFormat',\n        'type': 'string',\n        'defaultValue': 'NHWC'\n      },\n      {\n        'tfName': 'explicit_paddings',\n        'name': 'explicitPaddings',\n        'type': 'number[]',\n        'defaultValue': []\n      },\n      {\n        'tfName': 'dilations',\n        'name': 'dilations',\n        'type': 'number[]'\n      }\n    ]\n  },\n  {\n    'tfOpName': 'FusedDepthwiseConv2dNative',\n    'category': 'convolution',\n    'inputs': [\n      {\n        'start': 0,\n        'name': 'x',\n        'type': 'tensor'\n      },\n      {\n        'start': 1,\n        'name': 'filter',\n        'type': 'tensor'\n      },\n      {\n        'start': 2,\n        'end': 0,\n        'name': 'args',\n        'type': 'tensors'\n      }\n    ],\n    'attrs': [\n      {\n        'tfName': 'num_args',\n        'name': 'numArgs',\n        'type': 'number'\n      },\n      {\n        'tfName': 'T',\n        'name': 'dtype',\n        'type': 'dtype',\n        'notSupported': true\n      },\n      {\n        'tfName': 'strides',\n        'name': 'strides',\n        'type': 'number[]'\n      },\n      {\n        'tfName': 'padding',\n        'name': 'pad',\n        'type': 'string'\n      },\n      {\n        'tfName': 'data_format',\n        'name': 'dataFormat',\n        'type': 'string',\n        'defaultValue': 'NHWC'\n      },\n      {\n        'tfName': 'dilations',\n        'name': 'dilations',\n        'type': 'number[]',\n        'defaultValue': [\n          1,\n          1,\n          1,\n          1\n        ]\n      },\n      {\n        'tfName': 'fused_ops',\n        'name': 'fusedOps',\n        'type': 'string[]',\n        'defaultValue': []\n      },\n      {\n        'tfName': 'explicit_paddings',\n        'name': 'explicitPaddings',\n        'type': 'number[]',\n        'defaultValue': []\n      }\n    ]\n  },\n  {\n    'tfOpName': 'Conv3D',\n    'category': 'convolution',\n    'inputs': [\n      {\n        'start': 0,\n        'name': 'x',\n        'type': 'tensor'\n      },\n      {\n        'start': 1,\n        'name': 'filter',\n        'type': 'tensor'\n      }\n    ],\n    'attrs': [\n      {\n        'tfName': 'strides',\n        'name': 'strides',\n        'type': 'number[]'\n      },\n      {\n        'tfName': 'padding',\n        'name': 'pad',\n        'type': 'string'\n      },\n      {\n        'tfName': 'data_format',\n        'name': 'dataFormat',\n        'type': 'string',\n        'defaultValue': 'NHWC'\n      },\n      {\n        'tfName': 'dilations',\n        'name': 'dilations',\n        'type': 'number[]'\n      }\n    ]\n  },\n  {\n    'tfOpName': 'Dilation2D',\n    'category': 'convolution',\n    'inputs': [\n      {\n        'start': 0,\n        'name': 'x',\n        'type': 'tensor'\n      },\n      {\n        'start': 1,\n        'name': 'filter',\n        'type': 'tensor'\n      }\n    ],\n    'attrs': [\n      {\n        'tfName': 'strides',\n        'name': 'strides',\n        'type': 'number[]'\n      },\n      {\n        'tfName': 'rates',\n        'name': 'dilations',\n        'type': 'number[]'\n      },\n      {\n        'tfName': 'padding',\n        'name': 'pad',\n        'type': 'string'\n      }\n    ]\n  }\n]\n;\n"], "mappings": "AACA;;;;;;;;;;;;;;;;AAmBA,OAAO,MAAMA,IAAI,GAAe,CAC9B;EACE,UAAU,EAAE,SAAS;EACrB,UAAU,EAAE,aAAa;EACzB,QAAQ,EAAE,CACR;IACE,OAAO,EAAE,CAAC;IACV,MAAM,EAAE,GAAG;IACX,MAAM,EAAE;GACT,CACF;EACD,OAAO,EAAE,CACP;IACE,QAAQ,EAAE,SAAS;IACnB,MAAM,EAAE,SAAS;IACjB,MAAM,EAAE;GACT,EACD;IACE,QAAQ,EAAE,SAAS;IACnB,MAAM,EAAE,KAAK;IACb,MAAM,EAAE;GACT,EACD;IACE,QAAQ,EAAE,aAAa;IACvB,MAAM,EAAE,YAAY;IACpB,MAAM,EAAE,QAAQ;IAChB,cAAc,EAAE;GACjB,EACD;IACE,QAAQ,EAAE,OAAO;IACjB,MAAM,EAAE,YAAY;IACpB,MAAM,EAAE;GACT,EACD;IACE,QAAQ,EAAE,GAAG;IACb,MAAM,EAAE,OAAO;IACf,MAAM,EAAE,OAAO;IACf,cAAc,EAAE;GACjB;CAEJ,EACD;EACE,UAAU,EAAE,SAAS;EACrB,UAAU,EAAE,aAAa;EACzB,QAAQ,EAAE,CACR;IACE,OAAO,EAAE,CAAC;IACV,MAAM,EAAE,GAAG;IACX,MAAM,EAAE;GACT,CACF;EACD,OAAO,EAAE,CACP;IACE,QAAQ,EAAE,SAAS;IACnB,MAAM,EAAE,SAAS;IACjB,MAAM,EAAE;GACT,EACD;IACE,QAAQ,EAAE,SAAS;IACnB,MAAM,EAAE,KAAK;IACb,MAAM,EAAE;GACT,EACD;IACE,QAAQ,EAAE,aAAa;IACvB,MAAM,EAAE,YAAY;IACpB,MAAM,EAAE,QAAQ;IAChB,cAAc,EAAE;GACjB,EACD;IACE,QAAQ,EAAE,OAAO;IACjB,MAAM,EAAE,YAAY;IACpB,MAAM,EAAE;GACT,EACD;IACE,QAAQ,EAAE,mBAAmB;IAC7B,MAAM,EAAE,kBAAkB;IAC1B,MAAM,EAAE,UAAU;IAClB,cAAc,EAAE,EAAE;IAClB,cAAc,EAAE;GACjB,EACD;IACE,QAAQ,EAAE,GAAG;IACb,MAAM,EAAE,OAAO;IACf,MAAM,EAAE,OAAO;IACf,cAAc,EAAE;GACjB;CAEJ,EACD;EACE,UAAU,EAAE,mBAAmB;EAC/B,UAAU,EAAE,aAAa;EACzB,QAAQ,EAAE,CACR;IACE,OAAO,EAAE,CAAC;IACV,MAAM,EAAE,GAAG;IACX,MAAM,EAAE;GACT,CACF;EACD,OAAO,EAAE,CACP;IACE,QAAQ,EAAE,SAAS;IACnB,MAAM,EAAE,SAAS;IACjB,MAAM,EAAE;GACT,EACD;IACE,QAAQ,EAAE,SAAS;IACnB,MAAM,EAAE,KAAK;IACb,MAAM,EAAE;GACT,EACD;IACE,QAAQ,EAAE,OAAO;IACjB,MAAM,EAAE,YAAY;IACpB,MAAM,EAAE;GACT,EACD;IACE,QAAQ,EAAE,wBAAwB;IAClC,MAAM,EAAE,qBAAqB;IAC7B,MAAM,EAAE;GACT,EACD;IACE,QAAQ,EAAE,GAAG;IACb,MAAM,EAAE,OAAO;IACf,MAAM,EAAE,OAAO;IACf,cAAc,EAAE;GACjB;CAEJ,EACD;EACE,UAAU,EAAE,WAAW;EACvB,UAAU,EAAE,aAAa;EACzB,QAAQ,EAAE,CACR;IACE,OAAO,EAAE,CAAC;IACV,MAAM,EAAE,GAAG;IACX,MAAM,EAAE;GACT,CACF;EACD,OAAO,EAAE,CACP;IACE,QAAQ,EAAE,SAAS;IACnB,MAAM,EAAE,SAAS;IACjB,MAAM,EAAE;GACT,EACD;IACE,QAAQ,EAAE,SAAS;IACnB,MAAM,EAAE,KAAK;IACb,MAAM,EAAE;GACT,EACD;IACE,QAAQ,EAAE,aAAa;IACvB,MAAM,EAAE,YAAY;IACpB,MAAM,EAAE,QAAQ;IAChB,cAAc,EAAE;GACjB,EACD;IACE,QAAQ,EAAE,OAAO;IACjB,MAAM,EAAE,YAAY;IACpB,MAAM,EAAE;GACT,EACD;IACE,QAAQ,EAAE,GAAG;IACb,MAAM,EAAE,OAAO;IACf,MAAM,EAAE,OAAO;IACf,cAAc,EAAE;GACjB;CAEJ,EACD;EACE,UAAU,EAAE,WAAW;EACvB,UAAU,EAAE,aAAa;EACzB,QAAQ,EAAE,CACR;IACE,OAAO,EAAE,CAAC;IACV,MAAM,EAAE,GAAG;IACX,MAAM,EAAE;GACT,CACF;EACD,OAAO,EAAE,CACP;IACE,QAAQ,EAAE,SAAS;IACnB,MAAM,EAAE,SAAS;IACjB,MAAM,EAAE;GACT,EACD;IACE,QAAQ,EAAE,SAAS;IACnB,MAAM,EAAE,KAAK;IACb,MAAM,EAAE;GACT,EACD;IACE,QAAQ,EAAE,aAAa;IACvB,MAAM,EAAE,YAAY;IACpB,MAAM,EAAE,QAAQ;IAChB,cAAc,EAAE;GACjB,EACD;IACE,QAAQ,EAAE,OAAO;IACjB,MAAM,EAAE,YAAY;IACpB,MAAM,EAAE;GACT,EACD;IACE,QAAQ,EAAE,GAAG;IACb,MAAM,EAAE,OAAO;IACf,MAAM,EAAE,OAAO;IACf,cAAc,EAAE;GACjB;CAEJ,EACD;EACE,UAAU,EAAE,QAAQ;EACpB,UAAU,EAAE,aAAa;EACzB,QAAQ,EAAE,CACR;IACE,OAAO,EAAE,CAAC;IACV,MAAM,EAAE,GAAG;IACX,MAAM,EAAE;GACT,EACD;IACE,OAAO,EAAE,CAAC;IACV,MAAM,EAAE,QAAQ;IAChB,MAAM,EAAE;GACT,CACF;EACD,OAAO,EAAE,CACP;IACE,QAAQ,EAAE,QAAQ;IAClB,MAAM,EAAE,QAAQ;IAChB,MAAM,EAAE;GACT,EACD;IACE,QAAQ,EAAE,SAAS;IACnB,MAAM,EAAE,KAAK;IACb,MAAM,EAAE;GACT,EACD;IACE,QAAQ,EAAE,aAAa;IACvB,MAAM,EAAE,YAAY;IACpB,MAAM,EAAE,QAAQ;IAChB,cAAc,EAAE;GACjB,EACD;IACE,QAAQ,EAAE,GAAG;IACb,MAAM,EAAE,OAAO;IACf,MAAM,EAAE,OAAO;IACf,cAAc,EAAE;GACjB,EACD;IACE,QAAQ,EAAE,UAAU;IACpB,MAAM,EAAE,UAAU;IAClB,MAAM,EAAE,QAAQ;IAChB,cAAc,EAAE;GACjB;CAEJ,EACD;EACE,UAAU,EAAE,QAAQ;EACpB,UAAU,EAAE,aAAa;EACzB,QAAQ,EAAE,CACR;IACE,OAAO,EAAE,CAAC;IACV,MAAM,EAAE,GAAG;IACX,MAAM,EAAE;GACT,EACD;IACE,OAAO,EAAE,CAAC;IACV,MAAM,EAAE,QAAQ;IAChB,MAAM,EAAE;GACT,CACF;EACD,OAAO,EAAE,CACP;IACE,QAAQ,EAAE,GAAG;IACb,MAAM,EAAE,OAAO;IACf,MAAM,EAAE,OAAO;IACf,cAAc,EAAE;GACjB,EACD;IACE,QAAQ,EAAE,SAAS;IACnB,MAAM,EAAE,SAAS;IACjB,MAAM,EAAE;GACT,EACD;IACE,QAAQ,EAAE,SAAS;IACnB,MAAM,EAAE,KAAK;IACb,MAAM,EAAE;GACT,EACD;IACE,QAAQ,EAAE,eAAe;IACzB,MAAM,EAAE,eAAe;IACvB,MAAM,EAAE;GACT,EACD;IACE,QAAQ,EAAE,aAAa;IACvB,MAAM,EAAE,YAAY;IACpB,MAAM,EAAE,QAAQ;IAChB,cAAc,EAAE;GACjB,EACD;IACE,QAAQ,EAAE,mBAAmB;IAC7B,MAAM,EAAE,kBAAkB;IAC1B,MAAM,EAAE,UAAU;IAClB,cAAc,EAAE;GACjB,EACD;IACE,QAAQ,EAAE,WAAW;IACrB,MAAM,EAAE,WAAW;IACnB,MAAM,EAAE;GACT;CAEJ,EACD;EACE,UAAU,EAAE,cAAc;EAC1B,UAAU,EAAE,aAAa;EACzB,QAAQ,EAAE,CACR;IACE,OAAO,EAAE,CAAC;IACV,MAAM,EAAE,GAAG;IACX,MAAM,EAAE;GACT,EACD;IACE,OAAO,EAAE,CAAC;IACV,MAAM,EAAE,QAAQ;IAChB,MAAM,EAAE;GACT,EACD;IACE,OAAO,EAAE,CAAC;IACV,KAAK,EAAE,CAAC;IACR,MAAM,EAAE,MAAM;IACd,MAAM,EAAE;GACT,CACF;EACD,OAAO,EAAE,CACP;IACE,QAAQ,EAAE,UAAU;IACpB,MAAM,EAAE,SAAS;IACjB,MAAM,EAAE;GACT,EACD;IACE,QAAQ,EAAE,GAAG;IACb,MAAM,EAAE,OAAO;IACf,MAAM,EAAE,OAAO;IACf,cAAc,EAAE;GACjB,EACD;IACE,QAAQ,EAAE,SAAS;IACnB,MAAM,EAAE,SAAS;IACjB,MAAM,EAAE;GACT,EACD;IACE,QAAQ,EAAE,SAAS;IACnB,MAAM,EAAE,KAAK;IACb,MAAM,EAAE;GACT,EACD;IACE,QAAQ,EAAE,mBAAmB;IAC7B,MAAM,EAAE,kBAAkB;IAC1B,MAAM,EAAE,UAAU;IAClB,cAAc,EAAE;GACjB,EACD;IACE,QAAQ,EAAE,kBAAkB;IAC5B,MAAM,EAAE,eAAe;IACvB,MAAM,EAAE,MAAM;IACd,cAAc,EAAE;GACjB,EACD;IACE,QAAQ,EAAE,aAAa;IACvB,MAAM,EAAE,YAAY;IACpB,MAAM,EAAE,QAAQ;IAChB,cAAc,EAAE;GACjB,EACD;IACE,QAAQ,EAAE,WAAW;IACrB,MAAM,EAAE,WAAW;IACnB,MAAM,EAAE,UAAU;IAClB,cAAc,EAAE,CACd,CAAC,EACD,CAAC,EACD,CAAC,EACD,CAAC;GAEJ,EACD;IACE,QAAQ,EAAE,WAAW;IACrB,MAAM,EAAE,UAAU;IAClB,MAAM,EAAE,UAAU;IAClB,cAAc,EAAE;GACjB,EACD;IACE,QAAQ,EAAE,SAAS;IACnB,MAAM,EAAE,SAAS;IACjB,MAAM,EAAE,QAAQ;IAChB,cAAc,EAAE;GACjB,EACD;IACE,QAAQ,EAAE,iBAAiB;IAC3B,MAAM,EAAE,gBAAgB;IACxB,MAAM,EAAE,QAAQ;IAChB,cAAc,EAAE;GACjB;CAEJ,EACD;EACE,UAAU,EAAE,qBAAqB;EACjC,UAAU,EAAE,aAAa;EACzB,QAAQ,EAAE,CACR;IACE,OAAO,EAAE,CAAC;IACV,MAAM,EAAE,GAAG;IACX,MAAM,EAAE;GACT,EACD;IACE,OAAO,EAAE,CAAC;IACV,MAAM,EAAE,QAAQ;IAChB,MAAM,EAAE;GACT,EACD;IACE,OAAO,EAAE,CAAC;IACV,MAAM,EAAE,aAAa;IACrB,MAAM,EAAE;GACT,CACF;EACD,OAAO,EAAE,CACP;IACE,QAAQ,EAAE,SAAS;IACnB,MAAM,EAAE,SAAS;IACjB,MAAM,EAAE;GACT,EACD;IACE,QAAQ,EAAE,SAAS;IACnB,MAAM,EAAE,KAAK;IACb,MAAM,EAAE;GACT,EACD;IACE,QAAQ,EAAE,aAAa;IACvB,MAAM,EAAE,YAAY;IACpB,MAAM,EAAE,QAAQ;IAChB,cAAc,EAAE;GACjB,EACD;IACE,QAAQ,EAAE,mBAAmB;IAC7B,MAAM,EAAE,kBAAkB;IAC1B,MAAM,EAAE,UAAU;IAClB,cAAc,EAAE;GACjB,EACD;IACE,QAAQ,EAAE,WAAW;IACrB,MAAM,EAAE,WAAW;IACnB,MAAM,EAAE,UAAU;IAClB,cAAc,EAAE;GACjB;CAEJ,EACD;EACE,UAAU,EAAE,iBAAiB;EAC7B,UAAU,EAAE,aAAa;EACzB,QAAQ,EAAE,CACR;IACE,OAAO,EAAE,CAAC;IACV,MAAM,EAAE,OAAO;IACf,MAAM,EAAE;GACT,EACD;IACE,OAAO,EAAE,CAAC;IACV,MAAM,EAAE,QAAQ;IAChB,MAAM,EAAE;GACT,CACF;EACD,OAAO,EAAE,CACP;IACE,QAAQ,EAAE,SAAS;IACnB,MAAM,EAAE,SAAS;IACjB,MAAM,EAAE;GACT,EACD;IACE,QAAQ,EAAE,SAAS;IACnB,MAAM,EAAE,KAAK;IACb,MAAM,EAAE;GACT,EACD;IACE,QAAQ,EAAE,aAAa;IACvB,MAAM,EAAE,YAAY;IACpB,MAAM,EAAE,QAAQ;IAChB,cAAc,EAAE;GACjB,EACD;IACE,QAAQ,EAAE,mBAAmB;IAC7B,MAAM,EAAE,kBAAkB;IAC1B,MAAM,EAAE,UAAU;IAClB,cAAc,EAAE;GACjB,EACD;IACE,QAAQ,EAAE,WAAW;IACrB,MAAM,EAAE,WAAW;IACnB,MAAM,EAAE;GACT;CAEJ,EACD;EACE,UAAU,EAAE,uBAAuB;EACnC,UAAU,EAAE,aAAa;EACzB,QAAQ,EAAE,CACR;IACE,OAAO,EAAE,CAAC;IACV,MAAM,EAAE,OAAO;IACf,MAAM,EAAE;GACT,EACD;IACE,OAAO,EAAE,CAAC;IACV,MAAM,EAAE,QAAQ;IAChB,MAAM,EAAE;GACT,CACF;EACD,OAAO,EAAE,CACP;IACE,QAAQ,EAAE,SAAS;IACnB,MAAM,EAAE,SAAS;IACjB,MAAM,EAAE;GACT,EACD;IACE,QAAQ,EAAE,SAAS;IACnB,MAAM,EAAE,KAAK;IACb,MAAM,EAAE;GACT,EACD;IACE,QAAQ,EAAE,aAAa;IACvB,MAAM,EAAE,YAAY;IACpB,MAAM,EAAE,QAAQ;IAChB,cAAc,EAAE;GACjB,EACD;IACE,QAAQ,EAAE,mBAAmB;IAC7B,MAAM,EAAE,kBAAkB;IAC1B,MAAM,EAAE,UAAU;IAClB,cAAc,EAAE;GACjB,EACD;IACE,QAAQ,EAAE,WAAW;IACrB,MAAM,EAAE,WAAW;IACnB,MAAM,EAAE;GACT;CAEJ,EACD;EACE,UAAU,EAAE,4BAA4B;EACxC,UAAU,EAAE,aAAa;EACzB,QAAQ,EAAE,CACR;IACE,OAAO,EAAE,CAAC;IACV,MAAM,EAAE,GAAG;IACX,MAAM,EAAE;GACT,EACD;IACE,OAAO,EAAE,CAAC;IACV,MAAM,EAAE,QAAQ;IAChB,MAAM,EAAE;GACT,EACD;IACE,OAAO,EAAE,CAAC;IACV,KAAK,EAAE,CAAC;IACR,MAAM,EAAE,MAAM;IACd,MAAM,EAAE;GACT,CACF;EACD,OAAO,EAAE,CACP;IACE,QAAQ,EAAE,UAAU;IACpB,MAAM,EAAE,SAAS;IACjB,MAAM,EAAE;GACT,EACD;IACE,QAAQ,EAAE,GAAG;IACb,MAAM,EAAE,OAAO;IACf,MAAM,EAAE,OAAO;IACf,cAAc,EAAE;GACjB,EACD;IACE,QAAQ,EAAE,SAAS;IACnB,MAAM,EAAE,SAAS;IACjB,MAAM,EAAE;GACT,EACD;IACE,QAAQ,EAAE,SAAS;IACnB,MAAM,EAAE,KAAK;IACb,MAAM,EAAE;GACT,EACD;IACE,QAAQ,EAAE,aAAa;IACvB,MAAM,EAAE,YAAY;IACpB,MAAM,EAAE,QAAQ;IAChB,cAAc,EAAE;GACjB,EACD;IACE,QAAQ,EAAE,WAAW;IACrB,MAAM,EAAE,WAAW;IACnB,MAAM,EAAE,UAAU;IAClB,cAAc,EAAE,CACd,CAAC,EACD,CAAC,EACD,CAAC,EACD,CAAC;GAEJ,EACD;IACE,QAAQ,EAAE,WAAW;IACrB,MAAM,EAAE,UAAU;IAClB,MAAM,EAAE,UAAU;IAClB,cAAc,EAAE;GACjB,EACD;IACE,QAAQ,EAAE,mBAAmB;IAC7B,MAAM,EAAE,kBAAkB;IAC1B,MAAM,EAAE,UAAU;IAClB,cAAc,EAAE;GACjB;CAEJ,EACD;EACE,UAAU,EAAE,QAAQ;EACpB,UAAU,EAAE,aAAa;EACzB,QAAQ,EAAE,CACR;IACE,OAAO,EAAE,CAAC;IACV,MAAM,EAAE,GAAG;IACX,MAAM,EAAE;GACT,EACD;IACE,OAAO,EAAE,CAAC;IACV,MAAM,EAAE,QAAQ;IAChB,MAAM,EAAE;GACT,CACF;EACD,OAAO,EAAE,CACP;IACE,QAAQ,EAAE,SAAS;IACnB,MAAM,EAAE,SAAS;IACjB,MAAM,EAAE;GACT,EACD;IACE,QAAQ,EAAE,SAAS;IACnB,MAAM,EAAE,KAAK;IACb,MAAM,EAAE;GACT,EACD;IACE,QAAQ,EAAE,aAAa;IACvB,MAAM,EAAE,YAAY;IACpB,MAAM,EAAE,QAAQ;IAChB,cAAc,EAAE;GACjB,EACD;IACE,QAAQ,EAAE,WAAW;IACrB,MAAM,EAAE,WAAW;IACnB,MAAM,EAAE;GACT;CAEJ,EACD;EACE,UAAU,EAAE,YAAY;EACxB,UAAU,EAAE,aAAa;EACzB,QAAQ,EAAE,CACR;IACE,OAAO,EAAE,CAAC;IACV,MAAM,EAAE,GAAG;IACX,MAAM,EAAE;GACT,EACD;IACE,OAAO,EAAE,CAAC;IACV,MAAM,EAAE,QAAQ;IAChB,MAAM,EAAE;GACT,CACF;EACD,OAAO,EAAE,CACP;IACE,QAAQ,EAAE,SAAS;IACnB,MAAM,EAAE,SAAS;IACjB,MAAM,EAAE;GACT,EACD;IACE,QAAQ,EAAE,OAAO;IACjB,MAAM,EAAE,WAAW;IACnB,MAAM,EAAE;GACT,EACD;IACE,QAAQ,EAAE,SAAS;IACnB,MAAM,EAAE,KAAK;IACb,MAAM,EAAE;GACT;CAEJ,CACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}