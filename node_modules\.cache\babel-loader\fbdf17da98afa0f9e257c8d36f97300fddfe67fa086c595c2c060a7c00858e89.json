{"ast": null, "code": "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { backend_util, MaxPool3D } from '@tensorflow/tfjs-core';\nimport { Pool3DProgram } from '../pool_gpu';\nexport function maxPool3d(args) {\n  const {\n    inputs,\n    backend,\n    attrs\n  } = args;\n  const {\n    x\n  } = inputs;\n  const {\n    filterSize,\n    strides,\n    pad,\n    dataFormat,\n    dimRoundingMode\n  } = attrs;\n  const dilations = [1, 1, 1];\n  const convInfo = backend_util.computePool3DInfo(x.shape, filterSize, strides, dilations, pad, dimRoundingMode, dataFormat);\n  const maxPoolProgram = new Pool3DProgram(convInfo, 'max', false);\n  return backend.runWebGLProgram(maxPoolProgram, [x], x.dtype);\n}\nexport const maxPool3DConfig = {\n  kernelName: MaxPool3D,\n  backendName: 'webgl',\n  kernelFunc: maxPool3d\n};", "map": {"version": 3, "names": ["backend_util", "MaxPool3D", "Pool3DProgram", "maxPool3d", "args", "inputs", "backend", "attrs", "x", "filterSize", "strides", "pad", "dataFormat", "dimRoundingMode", "dilations", "convInfo", "computePool3DInfo", "shape", "maxPoolProgram", "runWebGLProgram", "dtype", "maxPool3DConfig", "kernelName", "backendName", "kernelFunc"], "sources": ["C:\\tfjs-backend-webgl\\src\\kernels\\MaxPool3D.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport {backend_util, KernelConfig, KernelFunc, MaxPool3D, MaxPool3DAttrs, MaxPool3DInputs, TensorInfo} from '@tensorflow/tfjs-core';\n\nimport {MathBackendWebGL} from '../backend_webgl';\nimport {Pool3DProgram} from '../pool_gpu';\n\nexport function maxPool3d(args: {\n  inputs: MaxPool3DInputs,\n  backend: MathBackendWebGL,\n  attrs: MaxPool3DAttrs\n}): TensorInfo {\n  const {inputs, backend, attrs} = args;\n  const {x} = inputs;\n  const {filterSize, strides, pad, dataFormat, dimRoundingMode} = attrs;\n  const dilations: [number, number, number] = [1, 1, 1];\n\n  const convInfo = backend_util.computePool3DInfo(\n      x.shape as [number, number, number, number, number], filterSize, strides,\n      dilations, pad, dimRoundingMode, dataFormat);\n  const maxPoolProgram = new Pool3DProgram(convInfo, 'max', false);\n  return backend.runWebGLProgram(maxPoolProgram, [x], x.dtype);\n}\n\nexport const maxPool3DConfig: KernelConfig = {\n  kernelName: MaxPool3D,\n  backendName: 'webgl',\n  kernelFunc: maxPool3d as unknown as KernelFunc\n};\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAgBA,SAAQA,YAAY,EAA4BC,SAAS,QAAoD,uBAAuB;AAGpI,SAAQC,aAAa,QAAO,aAAa;AAEzC,OAAM,SAAUC,SAASA,CAACC,IAIzB;EACC,MAAM;IAACC,MAAM;IAAEC,OAAO;IAAEC;EAAK,CAAC,GAAGH,IAAI;EACrC,MAAM;IAACI;EAAC,CAAC,GAAGH,MAAM;EAClB,MAAM;IAACI,UAAU;IAAEC,OAAO;IAAEC,GAAG;IAAEC,UAAU;IAAEC;EAAe,CAAC,GAAGN,KAAK;EACrE,MAAMO,SAAS,GAA6B,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;EAErD,MAAMC,QAAQ,GAAGf,YAAY,CAACgB,iBAAiB,CAC3CR,CAAC,CAACS,KAAiD,EAAER,UAAU,EAAEC,OAAO,EACxEI,SAAS,EAAEH,GAAG,EAAEE,eAAe,EAAED,UAAU,CAAC;EAChD,MAAMM,cAAc,GAAG,IAAIhB,aAAa,CAACa,QAAQ,EAAE,KAAK,EAAE,KAAK,CAAC;EAChE,OAAOT,OAAO,CAACa,eAAe,CAACD,cAAc,EAAE,CAACV,CAAC,CAAC,EAAEA,CAAC,CAACY,KAAK,CAAC;AAC9D;AAEA,OAAO,MAAMC,eAAe,GAAiB;EAC3CC,UAAU,EAAErB,SAAS;EACrBsB,WAAW,EAAE,OAAO;EACpBC,UAAU,EAAErB;CACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}