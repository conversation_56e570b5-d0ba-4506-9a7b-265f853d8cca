{"ast": null, "code": "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { buffer } from '@tensorflow/tfjs-core';\nexport function pool(xValues, xShape, dtype, strides, convInfo, poolType) {\n  const strideHeight = convInfo.strideHeight;\n  const strideWidth = convInfo.strideWidth;\n  const dilationHeight = convInfo.dilationHeight;\n  const dilationWidth = convInfo.dilationWidth;\n  const effectiveFilterHeight = convInfo.effectiveFilterHeight;\n  const effectiveFilterWidth = convInfo.effectiveFilterWidth;\n  const padTop = convInfo.padInfo.top;\n  const padLeft = convInfo.padInfo.left;\n  const initialValue = poolType === 'max' ? Number.NEGATIVE_INFINITY : Number.POSITIVE_INFINITY;\n  const output = buffer(convInfo.outShape, dtype);\n  const outputVals = output.values;\n  const outputBatchStrides = convInfo.outShape[1] * convInfo.outShape[2] * convInfo.outShape[3];\n  const outputRowStrides = convInfo.outShape[2] * convInfo.outShape[3];\n  const outputColStrides = convInfo.outShape[3];\n  for (let b = 0; b < convInfo.batchSize; ++b) {\n    const outputBatchOffset = b * outputBatchStrides;\n    const inputBatchOffset = b * strides[0];\n    for (let d = 0; d < convInfo.inChannels; ++d) {\n      for (let yR = 0; yR < convInfo.outHeight; ++yR) {\n        const xRCorner = yR * strideHeight - padTop;\n        const xRMin = Math.max(0, xRCorner);\n        const xRMax = Math.min(convInfo.inHeight, effectiveFilterHeight + xRCorner);\n        const outputRowOffset = outputBatchOffset + yR * outputRowStrides;\n        for (let yC = 0; yC < convInfo.outWidth; ++yC) {\n          const xCCorner = yC * strideWidth - padLeft;\n          const xCMin = Math.max(0, xCCorner);\n          const xCMax = Math.min(convInfo.inWidth, effectiveFilterWidth + xCCorner);\n          let minMaxValue = initialValue;\n          let avgValue = 0;\n          let count = 0;\n          for (let xR = xRMin; xR < xRMax; xR += dilationHeight) {\n            const xROffset = inputBatchOffset + xR * strides[1];\n            for (let xC = xCMin; xC < xCMax; xC += dilationWidth) {\n              const xCOffset = xROffset + xC * strides[2];\n              const pixel = xValues[xCOffset + d];\n              if (poolType === 'max' && pixel > minMaxValue) {\n                minMaxValue = pixel;\n              } else if (poolType === 'avg') {\n                avgValue += pixel;\n                count++;\n              }\n            }\n            if (isNaN(minMaxValue)) {\n              break;\n            }\n          }\n          const outputOffset = outputRowOffset + yC * outputColStrides + d;\n          outputVals[outputOffset] = poolType === 'avg' ? avgValue / count : minMaxValue;\n        }\n      }\n    }\n  }\n  return output;\n}\nexport function maxPoolPositions(xValues, xShape, dtype, convInfo, flattenPositions = false, includeBatchInIndex = false) {\n  const maxPositions = buffer(convInfo.outShape, 'int32');\n  const strideHeight = convInfo.strideHeight;\n  const strideWidth = convInfo.strideWidth;\n  const dilationHeight = convInfo.dilationHeight;\n  const dilationWidth = convInfo.dilationWidth;\n  const effectiveFilterHeight = convInfo.effectiveFilterHeight;\n  const effectiveFilterWidth = convInfo.effectiveFilterWidth;\n  const padTop = convInfo.padInfo.top;\n  const padLeft = convInfo.padInfo.left;\n  const xBuf = buffer(xShape, dtype, xValues);\n  for (let b = 0; b < convInfo.batchSize; ++b) {\n    for (let d = 0; d < convInfo.inChannels; ++d) {\n      for (let yR = 0; yR < convInfo.outHeight; ++yR) {\n        const xRCorner = yR * strideHeight - padTop;\n        let xRMin = xRCorner;\n        while (xRMin < 0) {\n          xRMin += dilationHeight;\n        }\n        // const xRMin = Math.max(0, xRCorner);\n        const xRMax = Math.min(convInfo.inHeight, effectiveFilterHeight + xRCorner);\n        for (let yC = 0; yC < convInfo.outWidth; ++yC) {\n          const xCCorner = yC * strideWidth - padLeft;\n          let xCMin = xCCorner;\n          while (xCMin < 0) {\n            xCMin += dilationWidth;\n          }\n          const xCMax = Math.min(convInfo.inWidth, effectiveFilterWidth + xCCorner);\n          let maxValue = Number.NEGATIVE_INFINITY;\n          let maxPosition = -1;\n          for (let xR = xRMin; xR < xRMax; xR += dilationHeight) {\n            const wR = xR - xRCorner;\n            for (let xC = xCMin; xC < xCMax; xC += dilationWidth) {\n              const wC = xC - xCCorner;\n              // For some reason, disable-next-line is not working\n              // TODO(mattsoulanille): Remove this when switching to TS5.\n              /* tslint:disable: no-unnecessary-type-assertion */\n              const pixel = xBuf.get(b, xR, xC, d);\n              if (pixel > maxValue) {\n                maxValue = pixel;\n                if (flattenPositions) {\n                  maxPosition = includeBatchInIndex ? ((b * convInfo.inHeight + xR) * convInfo.inWidth + xC) * convInfo.inChannels + d : (xR * convInfo.inWidth + xC) * convInfo.inChannels + d;\n                } else {\n                  maxPosition = wR * effectiveFilterWidth + wC;\n                }\n              }\n            }\n          }\n          maxPositions.set(maxPosition, b, yR, yC, d);\n        }\n      }\n    }\n  }\n  return maxPositions;\n}\nexport function pool3d(xValues, xShape, dtype, strides, convInfo, poolType) {\n  const strideDepth = convInfo.strideDepth;\n  const strideHeight = convInfo.strideHeight;\n  const strideWidth = convInfo.strideWidth;\n  const dilationDepth = convInfo.dilationDepth;\n  const dilationHeight = convInfo.dilationHeight;\n  const dilationWidth = convInfo.dilationWidth;\n  const effectiveFilterDepth = convInfo.effectiveFilterDepth;\n  const effectiveFilterHeight = convInfo.effectiveFilterHeight;\n  const effectiveFilterWidth = convInfo.effectiveFilterWidth;\n  const padFront = convInfo.padInfo.front;\n  const padTop = convInfo.padInfo.top;\n  const padLeft = convInfo.padInfo.left;\n  const initialValue = poolType === 'max' ? Number.NEGATIVE_INFINITY : Number.POSITIVE_INFINITY;\n  const output = buffer(convInfo.outShape, dtype);\n  const outputVals = output.values;\n  const outputBatchStrides = convInfo.outShape[1] * convInfo.outShape[2] * convInfo.outShape[3] * convInfo.outShape[4];\n  const outputDepthStrides = convInfo.outShape[2] * convInfo.outShape[3] * convInfo.outShape[4];\n  const outputRowStrides = convInfo.outShape[3] * convInfo.outShape[4];\n  const outputColStrides = convInfo.outShape[4];\n  for (let batch = 0; batch < convInfo.batchSize; ++batch) {\n    const outputBatchOffset = batch * outputBatchStrides;\n    const inputBatchOffset = batch * strides[0];\n    for (let channel = 0; channel < convInfo.inChannels; ++channel) {\n      for (let yDepth = 0; yDepth < convInfo.outDepth; ++yDepth) {\n        const xDepthCorner = yDepth * strideDepth - padFront;\n        let xDepthMin = xDepthCorner;\n        while (xDepthMin < 0) {\n          xDepthMin += dilationDepth;\n        }\n        const xDepthMax = Math.min(convInfo.inDepth, effectiveFilterDepth + xDepthCorner);\n        const outputDepthOffset = outputBatchOffset + yDepth * outputDepthStrides;\n        for (let yRow = 0; yRow < convInfo.outHeight; ++yRow) {\n          const xRowCorner = yRow * strideHeight - padTop;\n          let xRowMin = xRowCorner;\n          while (xRowMin < 0) {\n            xRowMin += dilationHeight;\n          }\n          const xRowMax = Math.min(convInfo.inHeight, effectiveFilterHeight + xRowCorner);\n          const outputRowOffset = outputDepthOffset + yRow * outputRowStrides;\n          for (let yCol = 0; yCol < convInfo.outWidth; ++yCol) {\n            const xColCorner = yCol * strideWidth - padLeft;\n            let xColMin = xColCorner;\n            while (xColMin < 0) {\n              xColMin += dilationWidth;\n            }\n            const xColMax = Math.min(convInfo.inWidth, effectiveFilterWidth + xColCorner);\n            // Shader code begins\n            const outputColOffset = outputRowOffset + yCol * outputColStrides;\n            let minMaxValue = initialValue;\n            let avgValue = 0;\n            let count = 0;\n            for (let xDepth = xDepthMin; xDepth < xDepthMax; xDepth += dilationDepth) {\n              const xDepthOffset = inputBatchOffset + xDepth * strides[1];\n              for (let xRow = xRowMin; xRow < xRowMax; xRow += dilationHeight) {\n                const xRowOffset = xDepthOffset + xRow * strides[2];\n                for (let xCol = xColMin; xCol < xColMax; xCol += dilationWidth) {\n                  const xColOffset = xRowOffset + xCol * strides[3];\n                  const pixel = xValues[xColOffset + channel];\n                  if (poolType === 'max' && pixel > minMaxValue) {\n                    minMaxValue = pixel;\n                  } else if (poolType === 'avg') {\n                    avgValue += pixel;\n                    count++;\n                  }\n                  if (isNaN(minMaxValue)) {\n                    break;\n                  }\n                }\n                if (isNaN(minMaxValue)) {\n                  break;\n                }\n              }\n              if (isNaN(minMaxValue)) {\n                break;\n              }\n            }\n            const outputOffset = outputColOffset + channel;\n            outputVals[outputOffset] = poolType === 'avg' ? avgValue / Math.max(count, 1) : minMaxValue;\n          }\n        }\n      }\n    }\n  }\n  return output;\n}\nexport function maxPool3dPositions(xBuf, convInfo) {\n  const maxPositions = buffer(convInfo.outShape, 'int32');\n  const strideDepth = convInfo.strideDepth;\n  const strideHeight = convInfo.strideHeight;\n  const strideWidth = convInfo.strideWidth;\n  const dilationDepth = convInfo.dilationDepth;\n  const dilationHeight = convInfo.dilationHeight;\n  const dilationWidth = convInfo.dilationWidth;\n  const effectiveFilterDepth = convInfo.effectiveFilterDepth;\n  const effectiveFilterHeight = convInfo.effectiveFilterHeight;\n  const effectiveFilterWidth = convInfo.effectiveFilterWidth;\n  const padFront = convInfo.padInfo.front;\n  const padTop = convInfo.padInfo.top;\n  const padLeft = convInfo.padInfo.left;\n  for (let batch = 0; batch < convInfo.batchSize; ++batch) {\n    for (let channel = 0; channel < convInfo.inChannels; ++channel) {\n      for (let yDepth = 0; yDepth < convInfo.outDepth; ++yDepth) {\n        const xDepthCorner = yDepth * strideDepth - padFront;\n        let xDepthMin = xDepthCorner;\n        while (xDepthMin < 0) {\n          xDepthMin += dilationDepth;\n        }\n        const xDepthMax = Math.min(convInfo.inDepth, effectiveFilterDepth + xDepthCorner);\n        for (let yRow = 0; yRow < convInfo.outHeight; ++yRow) {\n          const xRowCorner = yRow * strideHeight - padTop;\n          let xRowMin = xRowCorner;\n          while (xRowMin < 0) {\n            xRowMin += dilationHeight;\n          }\n          const xRowMax = Math.min(convInfo.inHeight, effectiveFilterHeight + xRowCorner);\n          for (let yCol = 0; yCol < convInfo.outWidth; ++yCol) {\n            const xColCorner = yCol * strideWidth - padLeft;\n            let xColMin = xColCorner;\n            while (xColMin < 0) {\n              xColMin += dilationWidth;\n            }\n            const xColMax = Math.min(convInfo.inWidth, effectiveFilterWidth + xColCorner);\n            // Shader code begins\n            let maxValue = Number.NEGATIVE_INFINITY;\n            let maxPosition = -1;\n            for (let xDepth = xDepthMin; xDepth < xDepthMax; xDepth += dilationDepth) {\n              const wDepth = xDepth - xDepthCorner;\n              for (let xRow = xRowMin; xRow < xRowMax; xRow += dilationHeight) {\n                const wRow = xRow - xRowCorner;\n                for (let xCol = xColMin; xCol < xColMax; xCol += dilationWidth) {\n                  const wCol = xCol - xColCorner;\n                  const pixel = xBuf.get(batch, xDepth, xRow, xCol, channel);\n                  if (pixel >= maxValue) {\n                    maxValue = pixel;\n                    maxPosition = wDepth * effectiveFilterHeight * effectiveFilterWidth + wRow * effectiveFilterHeight + wCol;\n                  }\n                }\n              }\n            }\n            maxPositions.set(maxPosition, batch, yDepth, yRow, yCol, channel);\n          }\n        }\n      }\n    }\n  }\n  return maxPositions;\n}", "map": {"version": 3, "names": ["buffer", "pool", "xValues", "xShape", "dtype", "strides", "convInfo", "poolType", "strideHeight", "<PERSON><PERSON><PERSON><PERSON>", "dilationHeight", "dilationWidth", "effectiveFilterHeight", "effectiveFilterWidth", "padTop", "padInfo", "top", "padLeft", "left", "initialValue", "Number", "NEGATIVE_INFINITY", "POSITIVE_INFINITY", "output", "outShape", "outputVals", "values", "outputBatchStrides", "outputRowStrides", "outputColStrides", "b", "batchSize", "outputBatchOffset", "inputBatchOffset", "d", "inChannels", "yR", "outHeight", "<PERSON><PERSON><PERSON><PERSON>", "xRMin", "Math", "max", "xRMax", "min", "inHeight", "outputRowOffset", "yC", "outWidth", "x<PERSON><PERSON><PERSON>", "xCMin", "xCMax", "inWidth", "minMaxValue", "avgValue", "count", "xR", "xROffset", "xC", "xCOffset", "pixel", "isNaN", "outputOffset", "maxPoolPositions", "flattenPositions", "includeBatchInIndex", "maxPositions", "xBuf", "maxValue", "maxPosition", "wR", "wC", "get", "set", "pool3d", "<PERSON><PERSON><PERSON>h", "dilationDepth", "effectiveFilterDepth", "padFront", "front", "outputDepthStrides", "batch", "channel", "y<PERSON><PERSON>h", "outDepth", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "xDepthMin", "xDepthMax", "inDepth", "outputDepthOffset", "yRow", "xRowCorner", "xRowMin", "xRowMax", "yCol", "xColCorner", "xColMin", "xColMax", "outputColOffset", "xDepth", "xDepthOffset", "xRow", "xRowOffset", "xCol", "xColOffset", "maxPool3dPositions", "wDepth", "wRow", "wCol"], "sources": ["C:\\tfjs-backend-cpu\\src\\utils\\pool_utils.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {backend_util, buffer, DataType, Rank, TensorBuffer, TypedArray} from '@tensorflow/tfjs-core';\n\nexport function pool(\n    xValues: TypedArray, xShape: number[], dtype: DataType, strides: number[],\n    convInfo: backend_util.Conv2DInfo,\n    poolType: 'max'|'avg'): TensorBuffer<Rank, DataType> {\n  const strideHeight = convInfo.strideHeight;\n  const strideWidth = convInfo.strideWidth;\n  const dilationHeight = convInfo.dilationHeight;\n  const dilationWidth = convInfo.dilationWidth;\n  const effectiveFilterHeight = convInfo.effectiveFilterHeight;\n  const effectiveFilterWidth = convInfo.effectiveFilterWidth;\n  const padTop = convInfo.padInfo.top;\n  const padLeft = convInfo.padInfo.left;\n\n  const initialValue =\n      (poolType === 'max' ? Number.NEGATIVE_INFINITY :\n                            Number.POSITIVE_INFINITY);\n\n  const output = buffer(convInfo.outShape, dtype);\n  const outputVals = output.values;\n\n  const outputBatchStrides =\n      convInfo.outShape[1] * convInfo.outShape[2] * convInfo.outShape[3];\n  const outputRowStrides = convInfo.outShape[2] * convInfo.outShape[3];\n  const outputColStrides = convInfo.outShape[3];\n\n  for (let b = 0; b < convInfo.batchSize; ++b) {\n    const outputBatchOffset = b * outputBatchStrides;\n    const inputBatchOffset = b * strides[0];\n    for (let d = 0; d < convInfo.inChannels; ++d) {\n      for (let yR = 0; yR < convInfo.outHeight; ++yR) {\n        const xRCorner = yR * strideHeight - padTop;\n        const xRMin = Math.max(0, xRCorner);\n        const xRMax =\n            Math.min(convInfo.inHeight, effectiveFilterHeight + xRCorner);\n        const outputRowOffset = outputBatchOffset + yR * outputRowStrides;\n        for (let yC = 0; yC < convInfo.outWidth; ++yC) {\n          const xCCorner = yC * strideWidth - padLeft;\n          const xCMin = Math.max(0, xCCorner);\n          const xCMax =\n              Math.min(convInfo.inWidth, effectiveFilterWidth + xCCorner);\n          let minMaxValue = initialValue;\n          let avgValue = 0;\n          let count = 0;\n          for (let xR = xRMin; xR < xRMax; xR += dilationHeight) {\n            const xROffset = inputBatchOffset + xR * strides[1];\n            for (let xC = xCMin; xC < xCMax; xC += dilationWidth) {\n              const xCOffset = xROffset + xC * strides[2];\n              const pixel = xValues[xCOffset + d];\n              if ((poolType === 'max' && pixel > minMaxValue)) {\n                minMaxValue = pixel;\n              } else if (poolType === 'avg') {\n                avgValue += pixel;\n                count++;\n              }\n            }\n            if (isNaN(minMaxValue)) {\n              break;\n            }\n          }\n          const outputOffset = outputRowOffset + yC * outputColStrides + d;\n          outputVals[outputOffset] =\n              poolType === 'avg' ? avgValue / count : minMaxValue;\n        }\n      }\n    }\n  }\n  return output;\n}\n\nexport function maxPoolPositions(\n    xValues: TypedArray, xShape: number[], dtype: DataType,\n    convInfo: backend_util.Conv2DInfo, flattenPositions = false,\n    includeBatchInIndex = false): TensorBuffer<Rank, 'int32'> {\n  const maxPositions = buffer(convInfo.outShape, 'int32');\n  const strideHeight = convInfo.strideHeight;\n  const strideWidth = convInfo.strideWidth;\n  const dilationHeight = convInfo.dilationHeight;\n  const dilationWidth = convInfo.dilationWidth;\n  const effectiveFilterHeight = convInfo.effectiveFilterHeight;\n  const effectiveFilterWidth = convInfo.effectiveFilterWidth;\n  const padTop = convInfo.padInfo.top;\n  const padLeft = convInfo.padInfo.left;\n\n  const xBuf = buffer(xShape, dtype, xValues);\n  for (let b = 0; b < convInfo.batchSize; ++b) {\n    for (let d = 0; d < convInfo.inChannels; ++d) {\n      for (let yR = 0; yR < convInfo.outHeight; ++yR) {\n        const xRCorner = yR * strideHeight - padTop;\n        let xRMin = xRCorner;\n        while (xRMin < 0) {\n          xRMin += dilationHeight;\n        }\n        // const xRMin = Math.max(0, xRCorner);\n        const xRMax =\n            Math.min(convInfo.inHeight, effectiveFilterHeight + xRCorner);\n        for (let yC = 0; yC < convInfo.outWidth; ++yC) {\n          const xCCorner = yC * strideWidth - padLeft;\n          let xCMin = xCCorner;\n          while (xCMin < 0) {\n            xCMin += dilationWidth;\n          }\n          const xCMax =\n              Math.min(convInfo.inWidth, effectiveFilterWidth + xCCorner);\n          let maxValue = Number.NEGATIVE_INFINITY;\n          let maxPosition = -1;\n\n          for (let xR = xRMin; xR < xRMax; xR += dilationHeight) {\n            const wR = xR - xRCorner;\n            for (let xC = xCMin; xC < xCMax; xC += dilationWidth) {\n              const wC = xC - xCCorner;\n              // For some reason, disable-next-line is not working\n              // TODO(mattsoulanille): Remove this when switching to TS5.\n              /* tslint:disable: no-unnecessary-type-assertion */\n              const pixel = xBuf.get(b, xR, xC, d) as number;\n              if (pixel > maxValue) {\n                maxValue = pixel as number;\n                if (flattenPositions) {\n                  maxPosition = includeBatchInIndex ?\n                      ((b * convInfo.inHeight + xR) * convInfo.inWidth + xC) *\n                              convInfo.inChannels +\n                          d :\n                      (xR * convInfo.inWidth + xC) * convInfo.inChannels + d;\n                } else {\n                  maxPosition = wR * effectiveFilterWidth + wC;\n                }\n              }\n            }\n          }\n          maxPositions.set(maxPosition, b, yR, yC, d);\n        }\n      }\n    }\n  }\n  return maxPositions;\n}\n\nexport function pool3d(\n    xValues: TypedArray, xShape: number[], dtype: DataType, strides: number[],\n    convInfo: backend_util.Conv3DInfo,\n    poolType: 'max'|'avg'): TensorBuffer<Rank, DataType> {\n  const strideDepth = convInfo.strideDepth;\n  const strideHeight = convInfo.strideHeight;\n  const strideWidth = convInfo.strideWidth;\n  const dilationDepth = convInfo.dilationDepth;\n  const dilationHeight = convInfo.dilationHeight;\n  const dilationWidth = convInfo.dilationWidth;\n  const effectiveFilterDepth = convInfo.effectiveFilterDepth;\n  const effectiveFilterHeight = convInfo.effectiveFilterHeight;\n  const effectiveFilterWidth = convInfo.effectiveFilterWidth;\n  const padFront = convInfo.padInfo.front;\n  const padTop = convInfo.padInfo.top;\n  const padLeft = convInfo.padInfo.left;\n\n  const initialValue =\n      (poolType === 'max' ? Number.NEGATIVE_INFINITY :\n                            Number.POSITIVE_INFINITY);\n\n  const output = buffer(convInfo.outShape, dtype);\n  const outputVals = output.values;\n\n  const outputBatchStrides = convInfo.outShape[1] * convInfo.outShape[2] *\n      convInfo.outShape[3] * convInfo.outShape[4];\n  const outputDepthStrides =\n      convInfo.outShape[2] * convInfo.outShape[3] * convInfo.outShape[4];\n  const outputRowStrides = convInfo.outShape[3] * convInfo.outShape[4];\n  const outputColStrides = convInfo.outShape[4];\n\n  for (let batch = 0; batch < convInfo.batchSize; ++batch) {\n    const outputBatchOffset = batch * outputBatchStrides;\n    const inputBatchOffset = batch * strides[0];\n    for (let channel = 0; channel < convInfo.inChannels; ++channel) {\n      for (let yDepth = 0; yDepth < convInfo.outDepth; ++yDepth) {\n        const xDepthCorner = yDepth * strideDepth - padFront;\n        let xDepthMin = xDepthCorner;\n        while (xDepthMin < 0) {\n          xDepthMin += dilationDepth;\n        }\n        const xDepthMax =\n            Math.min(convInfo.inDepth, effectiveFilterDepth + xDepthCorner);\n        const outputDepthOffset =\n            outputBatchOffset + yDepth * outputDepthStrides;\n        for (let yRow = 0; yRow < convInfo.outHeight; ++yRow) {\n          const xRowCorner = yRow * strideHeight - padTop;\n          let xRowMin = xRowCorner;\n          while (xRowMin < 0) {\n            xRowMin += dilationHeight;\n          }\n          const xRowMax =\n              Math.min(convInfo.inHeight, effectiveFilterHeight + xRowCorner);\n          const outputRowOffset = outputDepthOffset + yRow * outputRowStrides;\n          for (let yCol = 0; yCol < convInfo.outWidth; ++yCol) {\n            const xColCorner = yCol * strideWidth - padLeft;\n            let xColMin = xColCorner;\n            while (xColMin < 0) {\n              xColMin += dilationWidth;\n            }\n            const xColMax =\n                Math.min(convInfo.inWidth, effectiveFilterWidth + xColCorner);\n            // Shader code begins\n            const outputColOffset = outputRowOffset + yCol * outputColStrides;\n            let minMaxValue = initialValue;\n            let avgValue = 0;\n            let count = 0;\n            for (let xDepth = xDepthMin; xDepth < xDepthMax;\n                 xDepth += dilationDepth) {\n              const xDepthOffset = inputBatchOffset + xDepth * strides[1];\n              for (let xRow = xRowMin; xRow < xRowMax; xRow += dilationHeight) {\n                const xRowOffset = xDepthOffset + xRow * strides[2];\n                for (let xCol = xColMin; xCol < xColMax;\n                     xCol += dilationWidth) {\n                  const xColOffset = xRowOffset + xCol * strides[3];\n                  const pixel = xValues[xColOffset + channel];\n                  if ((poolType === 'max' && pixel > minMaxValue)) {\n                    minMaxValue = pixel;\n                  } else if (poolType === 'avg') {\n                    avgValue += pixel;\n                    count++;\n                  }\n                  if (isNaN(minMaxValue)) {\n                    break;\n                  }\n                }\n                if (isNaN(minMaxValue)) {\n                  break;\n                }\n              }\n              if (isNaN(minMaxValue)) {\n                break;\n              }\n            }\n            const outputOffset = outputColOffset + channel;\n            outputVals[outputOffset] = poolType === 'avg' ?\n                avgValue / Math.max(count, 1) :\n                minMaxValue;\n          }\n        }\n      }\n    }\n  }\n\n  return output;\n}\n\nexport function maxPool3dPositions(\n    xBuf: TensorBuffer<Rank, DataType>,\n    convInfo: backend_util.Conv3DInfo): TensorBuffer<Rank, DataType> {\n  const maxPositions = buffer(convInfo.outShape, 'int32');\n  const strideDepth = convInfo.strideDepth;\n  const strideHeight = convInfo.strideHeight;\n  const strideWidth = convInfo.strideWidth;\n  const dilationDepth = convInfo.dilationDepth;\n  const dilationHeight = convInfo.dilationHeight;\n  const dilationWidth = convInfo.dilationWidth;\n  const effectiveFilterDepth = convInfo.effectiveFilterDepth;\n  const effectiveFilterHeight = convInfo.effectiveFilterHeight;\n  const effectiveFilterWidth = convInfo.effectiveFilterWidth;\n  const padFront = convInfo.padInfo.front;\n  const padTop = convInfo.padInfo.top;\n  const padLeft = convInfo.padInfo.left;\n\n  for (let batch = 0; batch < convInfo.batchSize; ++batch) {\n    for (let channel = 0; channel < convInfo.inChannels; ++channel) {\n      for (let yDepth = 0; yDepth < convInfo.outDepth; ++yDepth) {\n        const xDepthCorner = yDepth * strideDepth - padFront;\n        let xDepthMin = xDepthCorner;\n        while (xDepthMin < 0) {\n          xDepthMin += dilationDepth;\n        }\n        const xDepthMax =\n            Math.min(convInfo.inDepth, effectiveFilterDepth + xDepthCorner);\n        for (let yRow = 0; yRow < convInfo.outHeight; ++yRow) {\n          const xRowCorner = yRow * strideHeight - padTop;\n          let xRowMin = xRowCorner;\n          while (xRowMin < 0) {\n            xRowMin += dilationHeight;\n          }\n          const xRowMax =\n              Math.min(convInfo.inHeight, effectiveFilterHeight + xRowCorner);\n          for (let yCol = 0; yCol < convInfo.outWidth; ++yCol) {\n            const xColCorner = yCol * strideWidth - padLeft;\n            let xColMin = xColCorner;\n            while (xColMin < 0) {\n              xColMin += dilationWidth;\n            }\n            const xColMax =\n                Math.min(convInfo.inWidth, effectiveFilterWidth + xColCorner);\n\n            // Shader code begins\n            let maxValue = Number.NEGATIVE_INFINITY;\n            let maxPosition = -1;\n\n            for (let xDepth = xDepthMin; xDepth < xDepthMax;\n                 xDepth += dilationDepth) {\n              const wDepth = xDepth - xDepthCorner;\n              for (let xRow = xRowMin; xRow < xRowMax; xRow += dilationHeight) {\n                const wRow = xRow - xRowCorner;\n                for (let xCol = xColMin; xCol < xColMax;\n                     xCol += dilationWidth) {\n                  const wCol = xCol - xColCorner;\n                  const pixel = xBuf.get(batch, xDepth, xRow, xCol,\n                                         channel) as number;\n                  if (pixel >= maxValue) {\n                    maxValue = pixel as number;\n                    maxPosition =\n                        wDepth * effectiveFilterHeight * effectiveFilterWidth +\n                        wRow * effectiveFilterHeight + wCol;\n                  }\n                }\n              }\n            }\n\n            maxPositions.set(maxPosition, batch, yDepth, yRow, yCol, channel);\n          }\n        }\n      }\n    }\n  }\n\n  return maxPositions;\n}\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAiBA,SAAsBA,MAAM,QAAiD,uBAAuB;AAEpG,OAAM,SAAUC,IAAIA,CAChBC,OAAmB,EAAEC,MAAgB,EAAEC,KAAe,EAAEC,OAAiB,EACzEC,QAAiC,EACjCC,QAAqB;EACvB,MAAMC,YAAY,GAAGF,QAAQ,CAACE,YAAY;EAC1C,MAAMC,WAAW,GAAGH,QAAQ,CAACG,WAAW;EACxC,MAAMC,cAAc,GAAGJ,QAAQ,CAACI,cAAc;EAC9C,MAAMC,aAAa,GAAGL,QAAQ,CAACK,aAAa;EAC5C,MAAMC,qBAAqB,GAAGN,QAAQ,CAACM,qBAAqB;EAC5D,MAAMC,oBAAoB,GAAGP,QAAQ,CAACO,oBAAoB;EAC1D,MAAMC,MAAM,GAAGR,QAAQ,CAACS,OAAO,CAACC,GAAG;EACnC,MAAMC,OAAO,GAAGX,QAAQ,CAACS,OAAO,CAACG,IAAI;EAErC,MAAMC,YAAY,GACbZ,QAAQ,KAAK,KAAK,GAAGa,MAAM,CAACC,iBAAiB,GACxBD,MAAM,CAACE,iBAAkB;EAEnD,MAAMC,MAAM,GAAGvB,MAAM,CAACM,QAAQ,CAACkB,QAAQ,EAAEpB,KAAK,CAAC;EAC/C,MAAMqB,UAAU,GAAGF,MAAM,CAACG,MAAM;EAEhC,MAAMC,kBAAkB,GACpBrB,QAAQ,CAACkB,QAAQ,CAAC,CAAC,CAAC,GAAGlB,QAAQ,CAACkB,QAAQ,CAAC,CAAC,CAAC,GAAGlB,QAAQ,CAACkB,QAAQ,CAAC,CAAC,CAAC;EACtE,MAAMI,gBAAgB,GAAGtB,QAAQ,CAACkB,QAAQ,CAAC,CAAC,CAAC,GAAGlB,QAAQ,CAACkB,QAAQ,CAAC,CAAC,CAAC;EACpE,MAAMK,gBAAgB,GAAGvB,QAAQ,CAACkB,QAAQ,CAAC,CAAC,CAAC;EAE7C,KAAK,IAAIM,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGxB,QAAQ,CAACyB,SAAS,EAAE,EAAED,CAAC,EAAE;IAC3C,MAAME,iBAAiB,GAAGF,CAAC,GAAGH,kBAAkB;IAChD,MAAMM,gBAAgB,GAAGH,CAAC,GAAGzB,OAAO,CAAC,CAAC,CAAC;IACvC,KAAK,IAAI6B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG5B,QAAQ,CAAC6B,UAAU,EAAE,EAAED,CAAC,EAAE;MAC5C,KAAK,IAAIE,EAAE,GAAG,CAAC,EAAEA,EAAE,GAAG9B,QAAQ,CAAC+B,SAAS,EAAE,EAAED,EAAE,EAAE;QAC9C,MAAME,QAAQ,GAAGF,EAAE,GAAG5B,YAAY,GAAGM,MAAM;QAC3C,MAAMyB,KAAK,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEH,QAAQ,CAAC;QACnC,MAAMI,KAAK,GACPF,IAAI,CAACG,GAAG,CAACrC,QAAQ,CAACsC,QAAQ,EAAEhC,qBAAqB,GAAG0B,QAAQ,CAAC;QACjE,MAAMO,eAAe,GAAGb,iBAAiB,GAAGI,EAAE,GAAGR,gBAAgB;QACjE,KAAK,IAAIkB,EAAE,GAAG,CAAC,EAAEA,EAAE,GAAGxC,QAAQ,CAACyC,QAAQ,EAAE,EAAED,EAAE,EAAE;UAC7C,MAAME,QAAQ,GAAGF,EAAE,GAAGrC,WAAW,GAAGQ,OAAO;UAC3C,MAAMgC,KAAK,GAAGT,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEO,QAAQ,CAAC;UACnC,MAAME,KAAK,GACPV,IAAI,CAACG,GAAG,CAACrC,QAAQ,CAAC6C,OAAO,EAAEtC,oBAAoB,GAAGmC,QAAQ,CAAC;UAC/D,IAAII,WAAW,GAAGjC,YAAY;UAC9B,IAAIkC,QAAQ,GAAG,CAAC;UAChB,IAAIC,KAAK,GAAG,CAAC;UACb,KAAK,IAAIC,EAAE,GAAGhB,KAAK,EAAEgB,EAAE,GAAGb,KAAK,EAAEa,EAAE,IAAI7C,cAAc,EAAE;YACrD,MAAM8C,QAAQ,GAAGvB,gBAAgB,GAAGsB,EAAE,GAAGlD,OAAO,CAAC,CAAC,CAAC;YACnD,KAAK,IAAIoD,EAAE,GAAGR,KAAK,EAAEQ,EAAE,GAAGP,KAAK,EAAEO,EAAE,IAAI9C,aAAa,EAAE;cACpD,MAAM+C,QAAQ,GAAGF,QAAQ,GAAGC,EAAE,GAAGpD,OAAO,CAAC,CAAC,CAAC;cAC3C,MAAMsD,KAAK,GAAGzD,OAAO,CAACwD,QAAQ,GAAGxB,CAAC,CAAC;cACnC,IAAK3B,QAAQ,KAAK,KAAK,IAAIoD,KAAK,GAAGP,WAAW,EAAG;gBAC/CA,WAAW,GAAGO,KAAK;eACpB,MAAM,IAAIpD,QAAQ,KAAK,KAAK,EAAE;gBAC7B8C,QAAQ,IAAIM,KAAK;gBACjBL,KAAK,EAAE;;;YAGX,IAAIM,KAAK,CAACR,WAAW,CAAC,EAAE;cACtB;;;UAGJ,MAAMS,YAAY,GAAGhB,eAAe,GAAGC,EAAE,GAAGjB,gBAAgB,GAAGK,CAAC;UAChET,UAAU,CAACoC,YAAY,CAAC,GACpBtD,QAAQ,KAAK,KAAK,GAAG8C,QAAQ,GAAGC,KAAK,GAAGF,WAAW;;;;;EAK/D,OAAO7B,MAAM;AACf;AAEA,OAAM,SAAUuC,gBAAgBA,CAC5B5D,OAAmB,EAAEC,MAAgB,EAAEC,KAAe,EACtDE,QAAiC,EAAEyD,gBAAgB,GAAG,KAAK,EAC3DC,mBAAmB,GAAG,KAAK;EAC7B,MAAMC,YAAY,GAAGjE,MAAM,CAACM,QAAQ,CAACkB,QAAQ,EAAE,OAAO,CAAC;EACvD,MAAMhB,YAAY,GAAGF,QAAQ,CAACE,YAAY;EAC1C,MAAMC,WAAW,GAAGH,QAAQ,CAACG,WAAW;EACxC,MAAMC,cAAc,GAAGJ,QAAQ,CAACI,cAAc;EAC9C,MAAMC,aAAa,GAAGL,QAAQ,CAACK,aAAa;EAC5C,MAAMC,qBAAqB,GAAGN,QAAQ,CAACM,qBAAqB;EAC5D,MAAMC,oBAAoB,GAAGP,QAAQ,CAACO,oBAAoB;EAC1D,MAAMC,MAAM,GAAGR,QAAQ,CAACS,OAAO,CAACC,GAAG;EACnC,MAAMC,OAAO,GAAGX,QAAQ,CAACS,OAAO,CAACG,IAAI;EAErC,MAAMgD,IAAI,GAAGlE,MAAM,CAACG,MAAM,EAAEC,KAAK,EAAEF,OAAO,CAAC;EAC3C,KAAK,IAAI4B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGxB,QAAQ,CAACyB,SAAS,EAAE,EAAED,CAAC,EAAE;IAC3C,KAAK,IAAII,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG5B,QAAQ,CAAC6B,UAAU,EAAE,EAAED,CAAC,EAAE;MAC5C,KAAK,IAAIE,EAAE,GAAG,CAAC,EAAEA,EAAE,GAAG9B,QAAQ,CAAC+B,SAAS,EAAE,EAAED,EAAE,EAAE;QAC9C,MAAME,QAAQ,GAAGF,EAAE,GAAG5B,YAAY,GAAGM,MAAM;QAC3C,IAAIyB,KAAK,GAAGD,QAAQ;QACpB,OAAOC,KAAK,GAAG,CAAC,EAAE;UAChBA,KAAK,IAAI7B,cAAc;;QAEzB;QACA,MAAMgC,KAAK,GACPF,IAAI,CAACG,GAAG,CAACrC,QAAQ,CAACsC,QAAQ,EAAEhC,qBAAqB,GAAG0B,QAAQ,CAAC;QACjE,KAAK,IAAIQ,EAAE,GAAG,CAAC,EAAEA,EAAE,GAAGxC,QAAQ,CAACyC,QAAQ,EAAE,EAAED,EAAE,EAAE;UAC7C,MAAME,QAAQ,GAAGF,EAAE,GAAGrC,WAAW,GAAGQ,OAAO;UAC3C,IAAIgC,KAAK,GAAGD,QAAQ;UACpB,OAAOC,KAAK,GAAG,CAAC,EAAE;YAChBA,KAAK,IAAItC,aAAa;;UAExB,MAAMuC,KAAK,GACPV,IAAI,CAACG,GAAG,CAACrC,QAAQ,CAAC6C,OAAO,EAAEtC,oBAAoB,GAAGmC,QAAQ,CAAC;UAC/D,IAAImB,QAAQ,GAAG/C,MAAM,CAACC,iBAAiB;UACvC,IAAI+C,WAAW,GAAG,CAAC,CAAC;UAEpB,KAAK,IAAIb,EAAE,GAAGhB,KAAK,EAAEgB,EAAE,GAAGb,KAAK,EAAEa,EAAE,IAAI7C,cAAc,EAAE;YACrD,MAAM2D,EAAE,GAAGd,EAAE,GAAGjB,QAAQ;YACxB,KAAK,IAAImB,EAAE,GAAGR,KAAK,EAAEQ,EAAE,GAAGP,KAAK,EAAEO,EAAE,IAAI9C,aAAa,EAAE;cACpD,MAAM2D,EAAE,GAAGb,EAAE,GAAGT,QAAQ;cACxB;cACA;cACA;cACA,MAAMW,KAAK,GAAGO,IAAI,CAACK,GAAG,CAACzC,CAAC,EAAEyB,EAAE,EAAEE,EAAE,EAAEvB,CAAC,CAAW;cAC9C,IAAIyB,KAAK,GAAGQ,QAAQ,EAAE;gBACpBA,QAAQ,GAAGR,KAAe;gBAC1B,IAAII,gBAAgB,EAAE;kBACpBK,WAAW,GAAGJ,mBAAmB,GAC7B,CAAC,CAAClC,CAAC,GAAGxB,QAAQ,CAACsC,QAAQ,GAAGW,EAAE,IAAIjD,QAAQ,CAAC6C,OAAO,GAAGM,EAAE,IAC7CnD,QAAQ,CAAC6B,UAAU,GACvBD,CAAC,GACL,CAACqB,EAAE,GAAGjD,QAAQ,CAAC6C,OAAO,GAAGM,EAAE,IAAInD,QAAQ,CAAC6B,UAAU,GAAGD,CAAC;iBAC3D,MAAM;kBACLkC,WAAW,GAAGC,EAAE,GAAGxD,oBAAoB,GAAGyD,EAAE;;;;;UAKpDL,YAAY,CAACO,GAAG,CAACJ,WAAW,EAAEtC,CAAC,EAAEM,EAAE,EAAEU,EAAE,EAAEZ,CAAC,CAAC;;;;;EAKnD,OAAO+B,YAAY;AACrB;AAEA,OAAM,SAAUQ,MAAMA,CAClBvE,OAAmB,EAAEC,MAAgB,EAAEC,KAAe,EAAEC,OAAiB,EACzEC,QAAiC,EACjCC,QAAqB;EACvB,MAAMmE,WAAW,GAAGpE,QAAQ,CAACoE,WAAW;EACxC,MAAMlE,YAAY,GAAGF,QAAQ,CAACE,YAAY;EAC1C,MAAMC,WAAW,GAAGH,QAAQ,CAACG,WAAW;EACxC,MAAMkE,aAAa,GAAGrE,QAAQ,CAACqE,aAAa;EAC5C,MAAMjE,cAAc,GAAGJ,QAAQ,CAACI,cAAc;EAC9C,MAAMC,aAAa,GAAGL,QAAQ,CAACK,aAAa;EAC5C,MAAMiE,oBAAoB,GAAGtE,QAAQ,CAACsE,oBAAoB;EAC1D,MAAMhE,qBAAqB,GAAGN,QAAQ,CAACM,qBAAqB;EAC5D,MAAMC,oBAAoB,GAAGP,QAAQ,CAACO,oBAAoB;EAC1D,MAAMgE,QAAQ,GAAGvE,QAAQ,CAACS,OAAO,CAAC+D,KAAK;EACvC,MAAMhE,MAAM,GAAGR,QAAQ,CAACS,OAAO,CAACC,GAAG;EACnC,MAAMC,OAAO,GAAGX,QAAQ,CAACS,OAAO,CAACG,IAAI;EAErC,MAAMC,YAAY,GACbZ,QAAQ,KAAK,KAAK,GAAGa,MAAM,CAACC,iBAAiB,GACxBD,MAAM,CAACE,iBAAkB;EAEnD,MAAMC,MAAM,GAAGvB,MAAM,CAACM,QAAQ,CAACkB,QAAQ,EAAEpB,KAAK,CAAC;EAC/C,MAAMqB,UAAU,GAAGF,MAAM,CAACG,MAAM;EAEhC,MAAMC,kBAAkB,GAAGrB,QAAQ,CAACkB,QAAQ,CAAC,CAAC,CAAC,GAAGlB,QAAQ,CAACkB,QAAQ,CAAC,CAAC,CAAC,GAClElB,QAAQ,CAACkB,QAAQ,CAAC,CAAC,CAAC,GAAGlB,QAAQ,CAACkB,QAAQ,CAAC,CAAC,CAAC;EAC/C,MAAMuD,kBAAkB,GACpBzE,QAAQ,CAACkB,QAAQ,CAAC,CAAC,CAAC,GAAGlB,QAAQ,CAACkB,QAAQ,CAAC,CAAC,CAAC,GAAGlB,QAAQ,CAACkB,QAAQ,CAAC,CAAC,CAAC;EACtE,MAAMI,gBAAgB,GAAGtB,QAAQ,CAACkB,QAAQ,CAAC,CAAC,CAAC,GAAGlB,QAAQ,CAACkB,QAAQ,CAAC,CAAC,CAAC;EACpE,MAAMK,gBAAgB,GAAGvB,QAAQ,CAACkB,QAAQ,CAAC,CAAC,CAAC;EAE7C,KAAK,IAAIwD,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAG1E,QAAQ,CAACyB,SAAS,EAAE,EAAEiD,KAAK,EAAE;IACvD,MAAMhD,iBAAiB,GAAGgD,KAAK,GAAGrD,kBAAkB;IACpD,MAAMM,gBAAgB,GAAG+C,KAAK,GAAG3E,OAAO,CAAC,CAAC,CAAC;IAC3C,KAAK,IAAI4E,OAAO,GAAG,CAAC,EAAEA,OAAO,GAAG3E,QAAQ,CAAC6B,UAAU,EAAE,EAAE8C,OAAO,EAAE;MAC9D,KAAK,IAAIC,MAAM,GAAG,CAAC,EAAEA,MAAM,GAAG5E,QAAQ,CAAC6E,QAAQ,EAAE,EAAED,MAAM,EAAE;QACzD,MAAME,YAAY,GAAGF,MAAM,GAAGR,WAAW,GAAGG,QAAQ;QACpD,IAAIQ,SAAS,GAAGD,YAAY;QAC5B,OAAOC,SAAS,GAAG,CAAC,EAAE;UACpBA,SAAS,IAAIV,aAAa;;QAE5B,MAAMW,SAAS,GACX9C,IAAI,CAACG,GAAG,CAACrC,QAAQ,CAACiF,OAAO,EAAEX,oBAAoB,GAAGQ,YAAY,CAAC;QACnE,MAAMI,iBAAiB,GACnBxD,iBAAiB,GAAGkD,MAAM,GAAGH,kBAAkB;QACnD,KAAK,IAAIU,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAGnF,QAAQ,CAAC+B,SAAS,EAAE,EAAEoD,IAAI,EAAE;UACpD,MAAMC,UAAU,GAAGD,IAAI,GAAGjF,YAAY,GAAGM,MAAM;UAC/C,IAAI6E,OAAO,GAAGD,UAAU;UACxB,OAAOC,OAAO,GAAG,CAAC,EAAE;YAClBA,OAAO,IAAIjF,cAAc;;UAE3B,MAAMkF,OAAO,GACTpD,IAAI,CAACG,GAAG,CAACrC,QAAQ,CAACsC,QAAQ,EAAEhC,qBAAqB,GAAG8E,UAAU,CAAC;UACnE,MAAM7C,eAAe,GAAG2C,iBAAiB,GAAGC,IAAI,GAAG7D,gBAAgB;UACnE,KAAK,IAAIiE,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAGvF,QAAQ,CAACyC,QAAQ,EAAE,EAAE8C,IAAI,EAAE;YACnD,MAAMC,UAAU,GAAGD,IAAI,GAAGpF,WAAW,GAAGQ,OAAO;YAC/C,IAAI8E,OAAO,GAAGD,UAAU;YACxB,OAAOC,OAAO,GAAG,CAAC,EAAE;cAClBA,OAAO,IAAIpF,aAAa;;YAE1B,MAAMqF,OAAO,GACTxD,IAAI,CAACG,GAAG,CAACrC,QAAQ,CAAC6C,OAAO,EAAEtC,oBAAoB,GAAGiF,UAAU,CAAC;YACjE;YACA,MAAMG,eAAe,GAAGpD,eAAe,GAAGgD,IAAI,GAAGhE,gBAAgB;YACjE,IAAIuB,WAAW,GAAGjC,YAAY;YAC9B,IAAIkC,QAAQ,GAAG,CAAC;YAChB,IAAIC,KAAK,GAAG,CAAC;YACb,KAAK,IAAI4C,MAAM,GAAGb,SAAS,EAAEa,MAAM,GAAGZ,SAAS,EAC1CY,MAAM,IAAIvB,aAAa,EAAE;cAC5B,MAAMwB,YAAY,GAAGlE,gBAAgB,GAAGiE,MAAM,GAAG7F,OAAO,CAAC,CAAC,CAAC;cAC3D,KAAK,IAAI+F,IAAI,GAAGT,OAAO,EAAES,IAAI,GAAGR,OAAO,EAAEQ,IAAI,IAAI1F,cAAc,EAAE;gBAC/D,MAAM2F,UAAU,GAAGF,YAAY,GAAGC,IAAI,GAAG/F,OAAO,CAAC,CAAC,CAAC;gBACnD,KAAK,IAAIiG,IAAI,GAAGP,OAAO,EAAEO,IAAI,GAAGN,OAAO,EAClCM,IAAI,IAAI3F,aAAa,EAAE;kBAC1B,MAAM4F,UAAU,GAAGF,UAAU,GAAGC,IAAI,GAAGjG,OAAO,CAAC,CAAC,CAAC;kBACjD,MAAMsD,KAAK,GAAGzD,OAAO,CAACqG,UAAU,GAAGtB,OAAO,CAAC;kBAC3C,IAAK1E,QAAQ,KAAK,KAAK,IAAIoD,KAAK,GAAGP,WAAW,EAAG;oBAC/CA,WAAW,GAAGO,KAAK;mBACpB,MAAM,IAAIpD,QAAQ,KAAK,KAAK,EAAE;oBAC7B8C,QAAQ,IAAIM,KAAK;oBACjBL,KAAK,EAAE;;kBAET,IAAIM,KAAK,CAACR,WAAW,CAAC,EAAE;oBACtB;;;gBAGJ,IAAIQ,KAAK,CAACR,WAAW,CAAC,EAAE;kBACtB;;;cAGJ,IAAIQ,KAAK,CAACR,WAAW,CAAC,EAAE;gBACtB;;;YAGJ,MAAMS,YAAY,GAAGoC,eAAe,GAAGhB,OAAO;YAC9CxD,UAAU,CAACoC,YAAY,CAAC,GAAGtD,QAAQ,KAAK,KAAK,GACzC8C,QAAQ,GAAGb,IAAI,CAACC,GAAG,CAACa,KAAK,EAAE,CAAC,CAAC,GAC7BF,WAAW;;;;;;EAOzB,OAAO7B,MAAM;AACf;AAEA,OAAM,SAAUiF,kBAAkBA,CAC9BtC,IAAkC,EAClC5D,QAAiC;EACnC,MAAM2D,YAAY,GAAGjE,MAAM,CAACM,QAAQ,CAACkB,QAAQ,EAAE,OAAO,CAAC;EACvD,MAAMkD,WAAW,GAAGpE,QAAQ,CAACoE,WAAW;EACxC,MAAMlE,YAAY,GAAGF,QAAQ,CAACE,YAAY;EAC1C,MAAMC,WAAW,GAAGH,QAAQ,CAACG,WAAW;EACxC,MAAMkE,aAAa,GAAGrE,QAAQ,CAACqE,aAAa;EAC5C,MAAMjE,cAAc,GAAGJ,QAAQ,CAACI,cAAc;EAC9C,MAAMC,aAAa,GAAGL,QAAQ,CAACK,aAAa;EAC5C,MAAMiE,oBAAoB,GAAGtE,QAAQ,CAACsE,oBAAoB;EAC1D,MAAMhE,qBAAqB,GAAGN,QAAQ,CAACM,qBAAqB;EAC5D,MAAMC,oBAAoB,GAAGP,QAAQ,CAACO,oBAAoB;EAC1D,MAAMgE,QAAQ,GAAGvE,QAAQ,CAACS,OAAO,CAAC+D,KAAK;EACvC,MAAMhE,MAAM,GAAGR,QAAQ,CAACS,OAAO,CAACC,GAAG;EACnC,MAAMC,OAAO,GAAGX,QAAQ,CAACS,OAAO,CAACG,IAAI;EAErC,KAAK,IAAI8D,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAG1E,QAAQ,CAACyB,SAAS,EAAE,EAAEiD,KAAK,EAAE;IACvD,KAAK,IAAIC,OAAO,GAAG,CAAC,EAAEA,OAAO,GAAG3E,QAAQ,CAAC6B,UAAU,EAAE,EAAE8C,OAAO,EAAE;MAC9D,KAAK,IAAIC,MAAM,GAAG,CAAC,EAAEA,MAAM,GAAG5E,QAAQ,CAAC6E,QAAQ,EAAE,EAAED,MAAM,EAAE;QACzD,MAAME,YAAY,GAAGF,MAAM,GAAGR,WAAW,GAAGG,QAAQ;QACpD,IAAIQ,SAAS,GAAGD,YAAY;QAC5B,OAAOC,SAAS,GAAG,CAAC,EAAE;UACpBA,SAAS,IAAIV,aAAa;;QAE5B,MAAMW,SAAS,GACX9C,IAAI,CAACG,GAAG,CAACrC,QAAQ,CAACiF,OAAO,EAAEX,oBAAoB,GAAGQ,YAAY,CAAC;QACnE,KAAK,IAAIK,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAGnF,QAAQ,CAAC+B,SAAS,EAAE,EAAEoD,IAAI,EAAE;UACpD,MAAMC,UAAU,GAAGD,IAAI,GAAGjF,YAAY,GAAGM,MAAM;UAC/C,IAAI6E,OAAO,GAAGD,UAAU;UACxB,OAAOC,OAAO,GAAG,CAAC,EAAE;YAClBA,OAAO,IAAIjF,cAAc;;UAE3B,MAAMkF,OAAO,GACTpD,IAAI,CAACG,GAAG,CAACrC,QAAQ,CAACsC,QAAQ,EAAEhC,qBAAqB,GAAG8E,UAAU,CAAC;UACnE,KAAK,IAAIG,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAGvF,QAAQ,CAACyC,QAAQ,EAAE,EAAE8C,IAAI,EAAE;YACnD,MAAMC,UAAU,GAAGD,IAAI,GAAGpF,WAAW,GAAGQ,OAAO;YAC/C,IAAI8E,OAAO,GAAGD,UAAU;YACxB,OAAOC,OAAO,GAAG,CAAC,EAAE;cAClBA,OAAO,IAAIpF,aAAa;;YAE1B,MAAMqF,OAAO,GACTxD,IAAI,CAACG,GAAG,CAACrC,QAAQ,CAAC6C,OAAO,EAAEtC,oBAAoB,GAAGiF,UAAU,CAAC;YAEjE;YACA,IAAI3B,QAAQ,GAAG/C,MAAM,CAACC,iBAAiB;YACvC,IAAI+C,WAAW,GAAG,CAAC,CAAC;YAEpB,KAAK,IAAI8B,MAAM,GAAGb,SAAS,EAAEa,MAAM,GAAGZ,SAAS,EAC1CY,MAAM,IAAIvB,aAAa,EAAE;cAC5B,MAAM8B,MAAM,GAAGP,MAAM,GAAGd,YAAY;cACpC,KAAK,IAAIgB,IAAI,GAAGT,OAAO,EAAES,IAAI,GAAGR,OAAO,EAAEQ,IAAI,IAAI1F,cAAc,EAAE;gBAC/D,MAAMgG,IAAI,GAAGN,IAAI,GAAGV,UAAU;gBAC9B,KAAK,IAAIY,IAAI,GAAGP,OAAO,EAAEO,IAAI,GAAGN,OAAO,EAClCM,IAAI,IAAI3F,aAAa,EAAE;kBAC1B,MAAMgG,IAAI,GAAGL,IAAI,GAAGR,UAAU;kBAC9B,MAAMnC,KAAK,GAAGO,IAAI,CAACK,GAAG,CAACS,KAAK,EAAEkB,MAAM,EAAEE,IAAI,EAAEE,IAAI,EACzBrB,OAAO,CAAW;kBACzC,IAAItB,KAAK,IAAIQ,QAAQ,EAAE;oBACrBA,QAAQ,GAAGR,KAAe;oBAC1BS,WAAW,GACPqC,MAAM,GAAG7F,qBAAqB,GAAGC,oBAAoB,GACrD6F,IAAI,GAAG9F,qBAAqB,GAAG+F,IAAI;;;;;YAM/C1C,YAAY,CAACO,GAAG,CAACJ,WAAW,EAAEY,KAAK,EAAEE,MAAM,EAAEO,IAAI,EAAEI,IAAI,EAAEZ,OAAO,CAAC;;;;;;EAO3E,OAAOhB,YAAY;AACrB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}