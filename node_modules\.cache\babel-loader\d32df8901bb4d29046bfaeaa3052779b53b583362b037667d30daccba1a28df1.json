{"ast": null, "code": "/*\nCopyright (c) 2015, <PERSON><PERSON>\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in\nall copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\nTHE SOFTWARE.\n*/\n\n'use strict';\n\nmodule.exports = function () {\n  const Stemmer = require('./stemmer_pt');\n  const Token = require('./token');\n  const PorterStemmer = new Stemmer();\n\n  /**\n   * Marks a region after the first non-vowel following a vowel, or the\n   * null region at the end of the word if there is no such non-vowel.\n   *\n   * @param {Object} token Token to stem.\n   * @param {Number} start Start index (defaults to 0).\n   * @param {Number}       Region start index.\n   */\n  const markRegionN = function (start) {\n    let index = start || 0;\n    const length = this.string.length;\n    let region = length;\n    while (index < length - 1 && region === length) {\n      if (this.hasVowelAtIndex(index) && !this.hasVowelAtIndex(index + 1)) {\n        region = index + 2;\n      }\n      index++;\n    }\n    return region;\n  };\n\n  /**\n   * Mark RV.\n   *\n   * @param  {Object} token Token to stem.\n   * @return {Number}       Region start index.\n   */\n  const markRegionV = function () {\n    let rv = this.string.length;\n    if (rv > 3) {\n      if (!this.hasVowelAtIndex(1)) {\n        rv = this.nextVowelIndex(2) + 1;\n      } else if (this.hasVowelAtIndex(0) && this.hasVowelAtIndex(1)) {\n        rv = this.nextConsonantIndex(2) + 1;\n      } else {\n        rv = 3;\n      }\n    }\n    return rv;\n  };\n\n  /**\n   * Prelude.\n   *\n   * Nasalised vowel forms should be treated as a vowel followed by a consonant.\n   *\n   * @param  {String} token Word to stem.\n   * @return {String}       Stemmed token.\n   */\n  function prelude(token) {\n    return token.replaceAll('ã', 'a~').replaceAll('õ', 'o~');\n  }\n\n  /**\n   * Step 1: Standard suffix removal.\n   *\n   * This step should always be performed.\n   *\n   * @param  {Token} token Word to stem.\n   * @return {Token}       Stemmed token.\n   */\n  function standardSuffix(token) {\n    token.replaceSuffixInRegion(['amentos', 'imentos', 'aço~es', 'adoras', 'adores', 'amento', 'imento', 'aça~o', 'adora', 'ância', 'antes', 'ismos', 'istas', 'ador', 'ante', 'ável', 'ezas', 'icas', 'icos', 'ismo', 'ista', 'ível', 'osas', 'osos', 'eza', 'ica', 'ico', 'osa', 'oso'], '', 'r2');\n    token.replaceSuffixInRegion(['logias', 'logia'], 'log', 'r2');\n\n    // token.replaceSuffixInRegion(['uço~es', 'uça~o'], 'u', 'r1');\n\n    token.replaceSuffixInRegion(['ências', 'ência'], 'ente', 'r2');\n    token.replaceSuffixInRegion(['ativamente', 'icamente', 'ivamente', 'osamente', 'adamente'], '', 'r2');\n    token.replaceSuffixInRegion('amente', '', 'r1');\n    token.replaceSuffixInRegion(['antemente', 'avelmente', 'ivelmente', 'mente'], '', 'r2');\n    token.replaceSuffixInRegion(['abilidades', 'abilidade', 'icidades', 'icidade', 'ividades', 'ividade', 'idades', 'idade'], '', 'r2');\n    token.replaceSuffixInRegion(['ativas', 'ativos', 'ativa', 'ativo', 'ivas', 'ivos', 'iva', 'ivo'], '', 'r2');\n    if (token.hasSuffix('eiras') || token.hasSuffix('eira')) {\n      token.replaceSuffixInRegion(['iras', 'ira'], 'ir', 'rv');\n    }\n    return token;\n  }\n\n  /**\n   * Step 2: Verb suffix removal.\n   *\n   * Perform this step if no ending was removed in step 1.\n   *\n   * @param  {Token} token   Token to stem.\n   * @return {Token}         Stemmed token.\n   */\n  function verbSuffix(token) {\n    token.replaceSuffixInRegion(['aríamos', 'ássemos', 'eríamos', 'êssemos', 'iríamos', 'íssemos', 'áramos', 'aremos', 'aríeis', 'ásseis', 'ávamos', 'éramos', 'eremos', 'eríeis', 'ésseis', 'íramos', 'iremos', 'iríeis', 'ísseis', 'ara~o', 'ardes', 'areis', 'áreis', 'ariam', 'arias', 'armos', 'assem', 'asses', 'astes', 'áveis', 'era~o', 'erdes', 'ereis', 'éreis', 'eriam', 'erias', 'ermos', 'essem', 'esses', 'estes', 'íamos', 'ira~o', 'irdes', 'ireis', 'íreis', 'iriam', 'irias', 'irmos', 'issem', 'isses', 'istes', 'adas', 'ados', 'amos', 'ámos', 'ando', 'aram', 'aras', 'arás', 'arei', 'arem', 'ares', 'aria', 'asse', 'aste', 'avam', 'avas', 'emos', 'endo', 'eram', 'eras', 'erás', 'erei', 'erem', 'eres', 'eria', 'esse', 'este', 'idas', 'idos', 'íeis', 'imos', 'indo', 'iram', 'iras', 'irás', 'irei', 'irem', 'ires', 'iria', 'isse', 'iste', 'ada', 'ado', 'ais', 'ara', 'ará', 'ava', 'eis', 'era', 'erá', 'iam', 'ias', 'ida', 'ido', 'ira', 'irá', 'am', 'ar', 'as', 'ei', 'em', 'er', 'es', 'eu', 'ia', 'ir', 'is', 'iu', 'ou'], '', 'rv');\n    return token;\n  }\n\n  /**\n   * Step 3: Delete suffix i.\n   *\n   * Perform this step if the word was changed, in RV and preceded by c.\n   *\n   * @param  {Token} token   Token to stem.\n   * @return {Token}         Stemmed token.\n   */\n  function iPrecededByCSuffix(token) {\n    if (token.hasSuffix('ci')) {\n      token.replaceSuffixInRegion('i', '', 'rv');\n    }\n    return token;\n  }\n\n  /**\n   * Step 4: Residual suffix.\n   *\n   * Perform this step if steps 1 and 2 did not alter the word.\n   *\n   * @param  {Token} token Token to stem.\n   * @return {Token}       Stemmed token.\n   */\n  function residualSuffix(token) {\n    token.replaceSuffixInRegion(['os', 'a', 'i', 'o', 'á', 'í', 'ó'], '', 'rv');\n    return token;\n  }\n\n  /**\n   * Step 5: Residual form.\n   *\n   * This step should always be performed.\n   *\n   * @param  {Token} token Token to stem.\n   * @return {Token}       Stemmed token.\n   */\n  function residualForm(token) {\n    const tokenString = token.string;\n    if (token.hasSuffix('gue') || token.hasSuffix('gué') || token.hasSuffix('guê')) {\n      token.replaceSuffixInRegion(['ue', 'ué', 'uê'], '', 'rv');\n    }\n    if (token.hasSuffix('cie') || token.hasSuffix('cié') || token.hasSuffix('ciê')) {\n      token.replaceSuffixInRegion(['ie', 'ié', 'iê'], '', 'rv');\n    }\n    if (tokenString === token.string) {\n      token.replaceSuffixInRegion(['e', 'é', 'ê'], '', 'rv');\n    }\n    token.replaceSuffixInRegion('ç', 'c', 'all');\n    return token;\n  }\n\n  /**\n   * Postlude.\n   *\n   * Turns a~, o~ back into ã, õ.\n   *\n   * @param  {String} token Word to stem.\n   * @return {String}       Stemmed token.\n   */\n  function postlude(token) {\n    return token.replaceAll('a~', 'ã').replaceAll('o~', 'õ');\n  }\n\n  /**\n   * Stems a word using a Porter stemmer algorithm.\n   *\n   * @param  {String} word Word to stem.\n   * @return {String}      Stemmed token.\n   */\n  PorterStemmer.stem = function (word) {\n    let token = new Token(word.toLowerCase());\n    token = prelude(token);\n    token.usingVowels('aeiouáéíóúâêôàãõ').markRegion('all', 0).markRegion('r1', null, markRegionN).markRegion('r2', token.regions.r1, markRegionN).markRegion('rv', null, markRegionV);\n    const original = token.string;\n\n    // Always do step 1.\n    token = standardSuffix(token);\n\n    // Do step 2 if no ending was removed by step 1.\n    if (token.string === original) {\n      token = verbSuffix(token);\n    }\n\n    // If the last step to be obeyed — either step 1 or 2 — altered the word,\n    // do step 3. Alternatively, if neither steps 1 nor 2 altered the word, do\n    // step 4.\n    token = token.string !== original ? iPrecededByCSuffix(token) : residualSuffix(token);\n\n    // Always do step 5.\n    token = residualForm(token);\n    token = postlude(token);\n    return token.string;\n  };\n  return PorterStemmer;\n}();", "map": {"version": 3, "names": ["module", "exports", "<PERSON><PERSON><PERSON>", "require", "Token", "PorterStemmer", "markRegionN", "start", "index", "length", "string", "region", "hasVowelAtIndex", "markRegionV", "rv", "nextVowelIndex", "nextConsonantIndex", "prelude", "token", "replaceAll", "standardSuffix", "replaceSuffixInRegion", "hasSuffix", "verbSuffix", "iPrecededByCSuffix", "residualSuffix", "residualForm", "tokenString", "postlude", "stem", "word", "toLowerCase", "usingVowels", "markRegion", "regions", "r1", "original"], "sources": ["C:/tmsft/node_modules/natural/lib/natural/stemmers/porter_stemmer_pt.js"], "sourcesContent": ["/*\nCopyright (c) 2015, <PERSON><PERSON>\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in\nall copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\nTHE SOFTWARE.\n*/\n\n'use strict'\n\nmodule.exports = (function () {\n  const Stemmer = require('./stemmer_pt')\n  const Token = require('./token')\n  const PorterStemmer = new Stemmer()\n\n  /**\n   * Marks a region after the first non-vowel following a vowel, or the\n   * null region at the end of the word if there is no such non-vowel.\n   *\n   * @param {Object} token Token to stem.\n   * @param {Number} start Start index (defaults to 0).\n   * @param {Number}       Region start index.\n   */\n  const markRegionN = function (start) {\n    let index = start || 0\n    const length = this.string.length\n    let region = length\n\n    while (index < length - 1 && region === length) {\n      if (this.hasVowelAtIndex(index) && !this.hasVowelAtIndex(index + 1)) {\n        region = index + 2\n      }\n      index++\n    }\n\n    return region\n  }\n\n  /**\n   * Mark RV.\n   *\n   * @param  {Object} token Token to stem.\n   * @return {Number}       Region start index.\n   */\n  const markRegionV = function () {\n    let rv = this.string.length\n\n    if (rv > 3) {\n      if (!this.hasVowelAtIndex(1)) {\n        rv = this.nextVowelIndex(2) + 1\n      } else if (this.hasVowelAtIndex(0) && this.hasVowelAtIndex(1)) {\n        rv = this.nextConsonantIndex(2) + 1\n      } else {\n        rv = 3\n      }\n    }\n\n    return rv\n  }\n\n  /**\n   * Prelude.\n   *\n   * Nasalised vowel forms should be treated as a vowel followed by a consonant.\n   *\n   * @param  {String} token Word to stem.\n   * @return {String}       Stemmed token.\n   */\n  function prelude (token) {\n    return token\n      .replaceAll('ã', 'a~')\n      .replaceAll('õ', 'o~')\n  }\n\n  /**\n   * Step 1: Standard suffix removal.\n   *\n   * This step should always be performed.\n   *\n   * @param  {Token} token Word to stem.\n   * @return {Token}       Stemmed token.\n   */\n  function standardSuffix (token) {\n    token.replaceSuffixInRegion([\n      'amentos', 'imentos', 'aço~es', 'adoras', 'adores', 'amento', 'imento',\n\n      'aça~o', 'adora', 'ância', 'antes', 'ismos', 'istas',\n\n      'ador', 'ante', 'ável', 'ezas', 'icas', 'icos', 'ismo', 'ista', 'ível',\n      'osas', 'osos',\n\n      'eza', 'ica', 'ico', 'osa', 'oso'\n\n    ], '', 'r2')\n\n    token.replaceSuffixInRegion(['logias', 'logia'], 'log', 'r2')\n\n    // token.replaceSuffixInRegion(['uço~es', 'uça~o'], 'u', 'r1');\n\n    token.replaceSuffixInRegion(['ências', 'ência'], 'ente', 'r2')\n\n    token.replaceSuffixInRegion([\n      'ativamente', 'icamente', 'ivamente', 'osamente', 'adamente'\n    ], '', 'r2')\n\n    token.replaceSuffixInRegion('amente', '', 'r1')\n\n    token.replaceSuffixInRegion([\n      'antemente', 'avelmente', 'ivelmente', 'mente'\n    ], '', 'r2')\n\n    token.replaceSuffixInRegion([\n      'abilidades', 'abilidade',\n      'icidades', 'icidade',\n      'ividades', 'ividade',\n      'idades', 'idade'\n    ], '', 'r2')\n\n    token.replaceSuffixInRegion([\n      'ativas', 'ativos', 'ativa', 'ativo',\n      'ivas', 'ivos', 'iva', 'ivo'\n    ], '', 'r2')\n\n    if (token.hasSuffix('eiras') || token.hasSuffix('eira')) {\n      token.replaceSuffixInRegion(['iras', 'ira'], 'ir', 'rv')\n    }\n\n    return token\n  }\n\n  /**\n   * Step 2: Verb suffix removal.\n   *\n   * Perform this step if no ending was removed in step 1.\n   *\n   * @param  {Token} token   Token to stem.\n   * @return {Token}         Stemmed token.\n   */\n  function verbSuffix (token) {\n    token.replaceSuffixInRegion([\n      'aríamos', 'ássemos', 'eríamos', 'êssemos', 'iríamos', 'íssemos',\n\n      'áramos', 'aremos', 'aríeis', 'ásseis', 'ávamos', 'éramos', 'eremos',\n      'eríeis', 'ésseis', 'íramos', 'iremos', 'iríeis', 'ísseis',\n\n      'ara~o', 'ardes', 'areis', 'áreis', 'ariam', 'arias', 'armos', 'assem',\n      'asses', 'astes', 'áveis', 'era~o', 'erdes', 'ereis', 'éreis', 'eriam',\n      'erias', 'ermos', 'essem', 'esses', 'estes', 'íamos', 'ira~o', 'irdes',\n      'ireis', 'íreis', 'iriam', 'irias', 'irmos', 'issem', 'isses', 'istes',\n\n      'adas', 'ados', 'amos', 'ámos', 'ando', 'aram', 'aras', 'arás', 'arei',\n      'arem', 'ares', 'aria', 'asse', 'aste', 'avam', 'avas', 'emos', 'endo',\n      'eram', 'eras', 'erás', 'erei', 'erem', 'eres', 'eria', 'esse', 'este',\n      'idas', 'idos', 'íeis', 'imos', 'indo', 'iram', 'iras', 'irás', 'irei',\n      'irem', 'ires', 'iria', 'isse', 'iste',\n\n      'ada', 'ado', 'ais', 'ara', 'ará', 'ava', 'eis', 'era', 'erá', 'iam',\n      'ias', 'ida', 'ido', 'ira', 'irá',\n\n      'am', 'ar', 'as', 'ei', 'em', 'er', 'es', 'eu', 'ia', 'ir', 'is', 'iu', 'ou'\n\n    ], '', 'rv')\n\n    return token\n  }\n\n  /**\n   * Step 3: Delete suffix i.\n   *\n   * Perform this step if the word was changed, in RV and preceded by c.\n   *\n   * @param  {Token} token   Token to stem.\n   * @return {Token}         Stemmed token.\n   */\n  function iPrecededByCSuffix (token) {\n    if (token.hasSuffix('ci')) {\n      token.replaceSuffixInRegion('i', '', 'rv')\n    }\n\n    return token\n  }\n\n  /**\n   * Step 4: Residual suffix.\n   *\n   * Perform this step if steps 1 and 2 did not alter the word.\n   *\n   * @param  {Token} token Token to stem.\n   * @return {Token}       Stemmed token.\n   */\n  function residualSuffix (token) {\n    token.replaceSuffixInRegion(['os', 'a', 'i', 'o', 'á', 'í', 'ó'], '', 'rv')\n\n    return token\n  }\n\n  /**\n   * Step 5: Residual form.\n   *\n   * This step should always be performed.\n   *\n   * @param  {Token} token Token to stem.\n   * @return {Token}       Stemmed token.\n   */\n  function residualForm (token) {\n    const tokenString = token.string\n\n    if (token.hasSuffix('gue') || token.hasSuffix('gué') || token.hasSuffix('guê')) {\n      token.replaceSuffixInRegion(['ue', 'ué', 'uê'], '', 'rv')\n    }\n\n    if (token.hasSuffix('cie') || token.hasSuffix('cié') || token.hasSuffix('ciê')) {\n      token.replaceSuffixInRegion(['ie', 'ié', 'iê'], '', 'rv')\n    }\n\n    if (tokenString === token.string) {\n      token.replaceSuffixInRegion(['e', 'é', 'ê'], '', 'rv')\n    }\n\n    token.replaceSuffixInRegion('ç', 'c', 'all')\n\n    return token\n  }\n\n  /**\n   * Postlude.\n   *\n   * Turns a~, o~ back into ã, õ.\n   *\n   * @param  {String} token Word to stem.\n   * @return {String}       Stemmed token.\n   */\n  function postlude (token) {\n    return token\n      .replaceAll('a~', 'ã')\n      .replaceAll('o~', 'õ')\n  }\n\n  /**\n   * Stems a word using a Porter stemmer algorithm.\n   *\n   * @param  {String} word Word to stem.\n   * @return {String}      Stemmed token.\n   */\n  PorterStemmer.stem = function (word) {\n    let token = new Token(word.toLowerCase())\n\n    token = prelude(token)\n\n    token.usingVowels('aeiouáéíóúâêôàãõ')\n      .markRegion('all', 0)\n      .markRegion('r1', null, markRegionN)\n      .markRegion('r2', token.regions.r1, markRegionN)\n      .markRegion('rv', null, markRegionV)\n\n    const original = token.string\n\n    // Always do step 1.\n    token = standardSuffix(token)\n\n    // Do step 2 if no ending was removed by step 1.\n    if (token.string === original) {\n      token = verbSuffix(token)\n    }\n\n    // If the last step to be obeyed — either step 1 or 2 — altered the word,\n    // do step 3. Alternatively, if neither steps 1 nor 2 altered the word, do\n    // step 4.\n    token = token.string !== original ? iPrecededByCSuffix(token) : residualSuffix(token)\n\n    // Always do step 5.\n    token = residualForm(token)\n\n    token = postlude(token)\n\n    return token.string\n  }\n\n  return PorterStemmer\n})()\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,YAAY;;AAEZA,MAAM,CAACC,OAAO,GAAI,YAAY;EAC5B,MAAMC,OAAO,GAAGC,OAAO,CAAC,cAAc,CAAC;EACvC,MAAMC,KAAK,GAAGD,OAAO,CAAC,SAAS,CAAC;EAChC,MAAME,aAAa,GAAG,IAAIH,OAAO,CAAC,CAAC;;EAEnC;AACF;AACA;AACA;AACA;AACA;AACA;AACA;EACE,MAAMI,WAAW,GAAG,SAAAA,CAAUC,KAAK,EAAE;IACnC,IAAIC,KAAK,GAAGD,KAAK,IAAI,CAAC;IACtB,MAAME,MAAM,GAAG,IAAI,CAACC,MAAM,CAACD,MAAM;IACjC,IAAIE,MAAM,GAAGF,MAAM;IAEnB,OAAOD,KAAK,GAAGC,MAAM,GAAG,CAAC,IAAIE,MAAM,KAAKF,MAAM,EAAE;MAC9C,IAAI,IAAI,CAACG,eAAe,CAACJ,KAAK,CAAC,IAAI,CAAC,IAAI,CAACI,eAAe,CAACJ,KAAK,GAAG,CAAC,CAAC,EAAE;QACnEG,MAAM,GAAGH,KAAK,GAAG,CAAC;MACpB;MACAA,KAAK,EAAE;IACT;IAEA,OAAOG,MAAM;EACf,CAAC;;EAED;AACF;AACA;AACA;AACA;AACA;EACE,MAAME,WAAW,GAAG,SAAAA,CAAA,EAAY;IAC9B,IAAIC,EAAE,GAAG,IAAI,CAACJ,MAAM,CAACD,MAAM;IAE3B,IAAIK,EAAE,GAAG,CAAC,EAAE;MACV,IAAI,CAAC,IAAI,CAACF,eAAe,CAAC,CAAC,CAAC,EAAE;QAC5BE,EAAE,GAAG,IAAI,CAACC,cAAc,CAAC,CAAC,CAAC,GAAG,CAAC;MACjC,CAAC,MAAM,IAAI,IAAI,CAACH,eAAe,CAAC,CAAC,CAAC,IAAI,IAAI,CAACA,eAAe,CAAC,CAAC,CAAC,EAAE;QAC7DE,EAAE,GAAG,IAAI,CAACE,kBAAkB,CAAC,CAAC,CAAC,GAAG,CAAC;MACrC,CAAC,MAAM;QACLF,EAAE,GAAG,CAAC;MACR;IACF;IAEA,OAAOA,EAAE;EACX,CAAC;;EAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASG,OAAOA,CAAEC,KAAK,EAAE;IACvB,OAAOA,KAAK,CACTC,UAAU,CAAC,GAAG,EAAE,IAAI,CAAC,CACrBA,UAAU,CAAC,GAAG,EAAE,IAAI,CAAC;EAC1B;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASC,cAAcA,CAAEF,KAAK,EAAE;IAC9BA,KAAK,CAACG,qBAAqB,CAAC,CAC1B,SAAS,EAAE,SAAS,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAEtE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAEpD,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EACtE,MAAM,EAAE,MAAM,EAEd,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAElC,EAAE,EAAE,EAAE,IAAI,CAAC;IAEZH,KAAK,CAACG,qBAAqB,CAAC,CAAC,QAAQ,EAAE,OAAO,CAAC,EAAE,KAAK,EAAE,IAAI,CAAC;;IAE7D;;IAEAH,KAAK,CAACG,qBAAqB,CAAC,CAAC,QAAQ,EAAE,OAAO,CAAC,EAAE,MAAM,EAAE,IAAI,CAAC;IAE9DH,KAAK,CAACG,qBAAqB,CAAC,CAC1B,YAAY,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,CAC7D,EAAE,EAAE,EAAE,IAAI,CAAC;IAEZH,KAAK,CAACG,qBAAqB,CAAC,QAAQ,EAAE,EAAE,EAAE,IAAI,CAAC;IAE/CH,KAAK,CAACG,qBAAqB,CAAC,CAC1B,WAAW,EAAE,WAAW,EAAE,WAAW,EAAE,OAAO,CAC/C,EAAE,EAAE,EAAE,IAAI,CAAC;IAEZH,KAAK,CAACG,qBAAqB,CAAC,CAC1B,YAAY,EAAE,WAAW,EACzB,UAAU,EAAE,SAAS,EACrB,UAAU,EAAE,SAAS,EACrB,QAAQ,EAAE,OAAO,CAClB,EAAE,EAAE,EAAE,IAAI,CAAC;IAEZH,KAAK,CAACG,qBAAqB,CAAC,CAC1B,QAAQ,EAAE,QAAQ,EAAE,OAAO,EAAE,OAAO,EACpC,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,CAC7B,EAAE,EAAE,EAAE,IAAI,CAAC;IAEZ,IAAIH,KAAK,CAACI,SAAS,CAAC,OAAO,CAAC,IAAIJ,KAAK,CAACI,SAAS,CAAC,MAAM,CAAC,EAAE;MACvDJ,KAAK,CAACG,qBAAqB,CAAC,CAAC,MAAM,EAAE,KAAK,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC;IAC1D;IAEA,OAAOH,KAAK;EACd;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASK,UAAUA,CAAEL,KAAK,EAAE;IAC1BA,KAAK,CAACG,qBAAqB,CAAC,CAC1B,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAEhE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EACpE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAE1D,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EACtE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EACtE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EACtE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAEtE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EACtE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EACtE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EACtE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EACtE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAEtC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EACpE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAEjC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAE7E,EAAE,EAAE,EAAE,IAAI,CAAC;IAEZ,OAAOH,KAAK;EACd;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASM,kBAAkBA,CAAEN,KAAK,EAAE;IAClC,IAAIA,KAAK,CAACI,SAAS,CAAC,IAAI,CAAC,EAAE;MACzBJ,KAAK,CAACG,qBAAqB,CAAC,GAAG,EAAE,EAAE,EAAE,IAAI,CAAC;IAC5C;IAEA,OAAOH,KAAK;EACd;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASO,cAAcA,CAAEP,KAAK,EAAE;IAC9BA,KAAK,CAACG,qBAAqB,CAAC,CAAC,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE,EAAE,EAAE,IAAI,CAAC;IAE3E,OAAOH,KAAK;EACd;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASQ,YAAYA,CAAER,KAAK,EAAE;IAC5B,MAAMS,WAAW,GAAGT,KAAK,CAACR,MAAM;IAEhC,IAAIQ,KAAK,CAACI,SAAS,CAAC,KAAK,CAAC,IAAIJ,KAAK,CAACI,SAAS,CAAC,KAAK,CAAC,IAAIJ,KAAK,CAACI,SAAS,CAAC,KAAK,CAAC,EAAE;MAC9EJ,KAAK,CAACG,qBAAqB,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,EAAE,EAAE,EAAE,IAAI,CAAC;IAC3D;IAEA,IAAIH,KAAK,CAACI,SAAS,CAAC,KAAK,CAAC,IAAIJ,KAAK,CAACI,SAAS,CAAC,KAAK,CAAC,IAAIJ,KAAK,CAACI,SAAS,CAAC,KAAK,CAAC,EAAE;MAC9EJ,KAAK,CAACG,qBAAqB,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,EAAE,EAAE,EAAE,IAAI,CAAC;IAC3D;IAEA,IAAIM,WAAW,KAAKT,KAAK,CAACR,MAAM,EAAE;MAChCQ,KAAK,CAACG,qBAAqB,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE,EAAE,EAAE,IAAI,CAAC;IACxD;IAEAH,KAAK,CAACG,qBAAqB,CAAC,GAAG,EAAE,GAAG,EAAE,KAAK,CAAC;IAE5C,OAAOH,KAAK;EACd;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASU,QAAQA,CAAEV,KAAK,EAAE;IACxB,OAAOA,KAAK,CACTC,UAAU,CAAC,IAAI,EAAE,GAAG,CAAC,CACrBA,UAAU,CAAC,IAAI,EAAE,GAAG,CAAC;EAC1B;;EAEA;AACF;AACA;AACA;AACA;AACA;EACEd,aAAa,CAACwB,IAAI,GAAG,UAAUC,IAAI,EAAE;IACnC,IAAIZ,KAAK,GAAG,IAAId,KAAK,CAAC0B,IAAI,CAACC,WAAW,CAAC,CAAC,CAAC;IAEzCb,KAAK,GAAGD,OAAO,CAACC,KAAK,CAAC;IAEtBA,KAAK,CAACc,WAAW,CAAC,kBAAkB,CAAC,CAClCC,UAAU,CAAC,KAAK,EAAE,CAAC,CAAC,CACpBA,UAAU,CAAC,IAAI,EAAE,IAAI,EAAE3B,WAAW,CAAC,CACnC2B,UAAU,CAAC,IAAI,EAAEf,KAAK,CAACgB,OAAO,CAACC,EAAE,EAAE7B,WAAW,CAAC,CAC/C2B,UAAU,CAAC,IAAI,EAAE,IAAI,EAAEpB,WAAW,CAAC;IAEtC,MAAMuB,QAAQ,GAAGlB,KAAK,CAACR,MAAM;;IAE7B;IACAQ,KAAK,GAAGE,cAAc,CAACF,KAAK,CAAC;;IAE7B;IACA,IAAIA,KAAK,CAACR,MAAM,KAAK0B,QAAQ,EAAE;MAC7BlB,KAAK,GAAGK,UAAU,CAACL,KAAK,CAAC;IAC3B;;IAEA;IACA;IACA;IACAA,KAAK,GAAGA,KAAK,CAACR,MAAM,KAAK0B,QAAQ,GAAGZ,kBAAkB,CAACN,KAAK,CAAC,GAAGO,cAAc,CAACP,KAAK,CAAC;;IAErF;IACAA,KAAK,GAAGQ,YAAY,CAACR,KAAK,CAAC;IAE3BA,KAAK,GAAGU,QAAQ,CAACV,KAAK,CAAC;IAEvB,OAAOA,KAAK,CAACR,MAAM;EACrB,CAAC;EAED,OAAOL,aAAa;AACtB,CAAC,CAAE,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}