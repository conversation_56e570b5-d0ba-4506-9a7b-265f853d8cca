{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.CloudflareSocket = void 0;\nconst events_1 = require(\"events\");\n/**\n * Wrapper around the Cloudflare built-in socket that can be used by the `Connection`.\n */\nclass CloudflareSocket extends events_1.EventEmitter {\n  constructor(ssl) {\n    super();\n    this.ssl = ssl;\n    this.writable = false;\n    this.destroyed = false;\n    this._upgrading = false;\n    this._upgraded = false;\n    this._cfSocket = null;\n    this._cfWriter = null;\n    this._cfReader = null;\n  }\n  setNoDelay() {\n    return this;\n  }\n  setKeepAlive() {\n    return this;\n  }\n  ref() {\n    return this;\n  }\n  unref() {\n    return this;\n  }\n  async connect(port, host, connectListener) {\n    try {\n      log('connecting');\n      if (connectListener) this.once('connect', connectListener);\n      const options = this.ssl ? {\n        secureTransport: 'starttls'\n      } : {};\n      const mod = await import('cloudflare:sockets');\n      const connect = mod.connect;\n      this._cfSocket = connect(`${host}:${port}`, options);\n      this._cfWriter = this._cfSocket.writable.getWriter();\n      this._addClosedHandler();\n      this._cfReader = this._cfSocket.readable.getReader();\n      if (this.ssl) {\n        this._listenOnce().catch(e => this.emit('error', e));\n      } else {\n        this._listen().catch(e => this.emit('error', e));\n      }\n      await this._cfWriter.ready;\n      log('socket ready');\n      this.writable = true;\n      this.emit('connect');\n      return this;\n    } catch (e) {\n      this.emit('error', e);\n    }\n  }\n  async _listen() {\n    // eslint-disable-next-line no-constant-condition\n    while (true) {\n      log('awaiting receive from CF socket');\n      const {\n        done,\n        value\n      } = await this._cfReader.read();\n      log('CF socket received:', done, value);\n      if (done) {\n        log('done');\n        break;\n      }\n      this.emit('data', Buffer.from(value));\n    }\n  }\n  async _listenOnce() {\n    log('awaiting first receive from CF socket');\n    const {\n      done,\n      value\n    } = await this._cfReader.read();\n    log('First CF socket received:', done, value);\n    this.emit('data', Buffer.from(value));\n  }\n  write(data, encoding = 'utf8', callback = () => {}) {\n    if (data.length === 0) return callback();\n    if (typeof data === 'string') data = Buffer.from(data, encoding);\n    log('sending data direct:', data);\n    this._cfWriter.write(data).then(() => {\n      log('data sent');\n      callback();\n    }, err => {\n      log('send error', err);\n      callback(err);\n    });\n    return true;\n  }\n  end(data = Buffer.alloc(0), encoding = 'utf8', callback = () => {}) {\n    log('ending CF socket');\n    this.write(data, encoding, err => {\n      this._cfSocket.close();\n      if (callback) callback(err);\n    });\n    return this;\n  }\n  destroy(reason) {\n    log('destroying CF socket', reason);\n    this.destroyed = true;\n    return this.end();\n  }\n  startTls(options) {\n    if (this._upgraded) {\n      // Don't try to upgrade again.\n      this.emit('error', 'Cannot call `startTls()` more than once on a socket');\n      return;\n    }\n    this._cfWriter.releaseLock();\n    this._cfReader.releaseLock();\n    this._upgrading = true;\n    this._cfSocket = this._cfSocket.startTls(options);\n    this._cfWriter = this._cfSocket.writable.getWriter();\n    this._cfReader = this._cfSocket.readable.getReader();\n    this._addClosedHandler();\n    this._listen().catch(e => this.emit('error', e));\n  }\n  _addClosedHandler() {\n    this._cfSocket.closed.then(() => {\n      if (!this._upgrading) {\n        log('CF socket closed');\n        this._cfSocket = null;\n        this.emit('close');\n      } else {\n        this._upgrading = false;\n        this._upgraded = true;\n      }\n    }).catch(e => this.emit('error', e));\n  }\n}\nexports.CloudflareSocket = CloudflareSocket;\nconst debug = false;\nfunction dump(data) {\n  if (data instanceof Uint8Array || data instanceof ArrayBuffer) {\n    const hex = Buffer.from(data).toString('hex');\n    const str = new TextDecoder().decode(data);\n    return `\\n>>> STR: \"${str.replace(/\\n/g, '\\\\n')}\"\\n>>> HEX: ${hex}\\n`;\n  } else {\n    return data;\n  }\n}\nfunction log(...args) {\n  debug && console.log(...args.map(dump));\n}", "map": {"version": 3, "names": ["events_1", "require", "CloudflareSocket", "EventEmitter", "constructor", "ssl", "writable", "destroyed", "_upgrading", "_upgraded", "_cfSocket", "_cfWriter", "_cfReader", "set<PERSON><PERSON><PERSON>elay", "setKeepAlive", "ref", "unref", "connect", "port", "host", "connectListener", "log", "once", "options", "secureTransport", "mod", "getWriter", "_addClosedHandler", "readable", "<PERSON><PERSON><PERSON><PERSON>", "_listenOnce", "catch", "e", "emit", "_listen", "ready", "done", "value", "read", "<PERSON><PERSON><PERSON>", "from", "write", "data", "encoding", "callback", "length", "then", "err", "end", "alloc", "close", "destroy", "reason", "startTls", "releaseLock", "closed", "exports", "debug", "dump", "Uint8Array", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hex", "toString", "str", "TextDecoder", "decode", "replace", "args", "console", "map"], "sources": ["C:\\tmsft\\node_modules\\pg-cloudflare\\src\\index.ts"], "sourcesContent": ["import { SocketOptions, Socket, TlsOptions } from 'cloudflare:sockets'\nimport { EventEmitter } from 'events'\n\n/**\n * Wrapper around the Cloudflare built-in socket that can be used by the `Connection`.\n */\nexport class CloudflareSocket extends EventEmitter {\n  writable = false\n  destroyed = false\n\n  private _upgrading = false\n  private _upgraded = false\n  private _cfSocket: Socket | null = null\n  private _cfWriter: WritableStreamDefaultWriter | null = null\n  private _cfReader: ReadableStreamDefaultReader | null = null\n\n  constructor(readonly ssl: boolean) {\n    super()\n  }\n\n  setNoDelay() {\n    return this\n  }\n  setKeepAlive() {\n    return this\n  }\n  ref() {\n    return this\n  }\n  unref() {\n    return this\n  }\n\n  async connect(port: number, host: string, connectListener?: (...args: unknown[]) => void) {\n    try {\n      log('connecting')\n      if (connectListener) this.once('connect', connectListener)\n\n      const options: SocketOptions = this.ssl ? { secureTransport: 'starttls' } : {}\n      const mod = await import('cloudflare:sockets')\n      const connect = mod.connect\n      this._cfSocket = connect(`${host}:${port}`, options)\n      this._cfWriter = this._cfSocket.writable.getWriter()\n      this._addClosedHandler()\n\n      this._cfReader = this._cfSocket.readable.getReader()\n      if (this.ssl) {\n        this._listenOnce().catch((e) => this.emit('error', e))\n      } else {\n        this._listen().catch((e) => this.emit('error', e))\n      }\n\n      await this._cfWriter!.ready\n      log('socket ready')\n      this.writable = true\n      this.emit('connect')\n\n      return this\n    } catch (e) {\n      this.emit('error', e)\n    }\n  }\n\n  async _listen() {\n    // eslint-disable-next-line no-constant-condition\n    while (true) {\n      log('awaiting receive from CF socket')\n      const { done, value } = await this._cfReader!.read()\n      log('CF socket received:', done, value)\n      if (done) {\n        log('done')\n        break\n      }\n      this.emit('data', Buffer.from(value))\n    }\n  }\n\n  async _listenOnce() {\n    log('awaiting first receive from CF socket')\n    const { done, value } = await this._cfReader!.read()\n    log('First CF socket received:', done, value)\n    this.emit('data', Buffer.from(value))\n  }\n\n  write(\n    data: Uint8Array | string,\n    encoding: BufferEncoding = 'utf8',\n    callback: (...args: unknown[]) => void = () => {}\n  ) {\n    if (data.length === 0) return callback()\n    if (typeof data === 'string') data = Buffer.from(data, encoding)\n\n    log('sending data direct:', data)\n    this._cfWriter!.write(data).then(\n      () => {\n        log('data sent')\n        callback()\n      },\n      (err) => {\n        log('send error', err)\n        callback(err)\n      }\n    )\n    return true\n  }\n\n  end(data = Buffer.alloc(0), encoding: BufferEncoding = 'utf8', callback: (...args: unknown[]) => void = () => {}) {\n    log('ending CF socket')\n    this.write(data, encoding, (err) => {\n      this._cfSocket!.close()\n      if (callback) callback(err)\n    })\n    return this\n  }\n\n  destroy(reason: string) {\n    log('destroying CF socket', reason)\n    this.destroyed = true\n    return this.end()\n  }\n\n  startTls(options: TlsOptions) {\n    if (this._upgraded) {\n      // Don't try to upgrade again.\n      this.emit('error', 'Cannot call `startTls()` more than once on a socket')\n      return\n    }\n    this._cfWriter!.releaseLock()\n    this._cfReader!.releaseLock()\n    this._upgrading = true\n    this._cfSocket = this._cfSocket!.startTls(options)\n    this._cfWriter = this._cfSocket.writable.getWriter()\n    this._cfReader = this._cfSocket.readable.getReader()\n    this._addClosedHandler()\n    this._listen().catch((e) => this.emit('error', e))\n  }\n\n  _addClosedHandler() {\n    this._cfSocket!.closed.then(() => {\n      if (!this._upgrading) {\n        log('CF socket closed')\n        this._cfSocket = null\n        this.emit('close')\n      } else {\n        this._upgrading = false\n        this._upgraded = true\n      }\n    }).catch((e) => this.emit('error', e))\n  }\n}\n\nconst debug = false\n\nfunction dump(data: unknown) {\n  if (data instanceof Uint8Array || data instanceof ArrayBuffer) {\n    const hex = Buffer.from(data).toString('hex')\n    const str = new TextDecoder().decode(data)\n    return `\\n>>> STR: \"${str.replace(/\\n/g, '\\\\n')}\"\\n>>> HEX: ${hex}\\n`\n  } else {\n    return data\n  }\n}\n\nfunction log(...args: unknown[]) {\n  debug && console.log(...args.map(dump))\n}\n"], "mappings": ";;;;;;AACA,MAAAA,QAAA,GAAAC,OAAA;AAEA;;;AAGA,MAAaC,gBAAiB,SAAQF,QAAA,CAAAG,YAAY;EAUhDC,YAAqBC,GAAY;IAC/B,KAAK,EAAE;IADY,KAAAA,GAAG,GAAHA,GAAG;IATxB,KAAAC,QAAQ,GAAG,KAAK;IAChB,KAAAC,SAAS,GAAG,KAAK;IAET,KAAAC,UAAU,GAAG,KAAK;IAClB,KAAAC,SAAS,GAAG,KAAK;IACjB,KAAAC,SAAS,GAAkB,IAAI;IAC/B,KAAAC,SAAS,GAAuC,IAAI;IACpD,KAAAC,SAAS,GAAuC,IAAI;EAI5D;EAEAC,UAAUA,CAAA;IACR,OAAO,IAAI;EACb;EACAC,YAAYA,CAAA;IACV,OAAO,IAAI;EACb;EACAC,GAAGA,CAAA;IACD,OAAO,IAAI;EACb;EACAC,KAAKA,CAAA;IACH,OAAO,IAAI;EACb;EAEA,MAAMC,OAAOA,CAACC,IAAY,EAAEC,IAAY,EAAEC,eAA8C;IACtF,IAAI;MACFC,GAAG,CAAC,YAAY,CAAC;MACjB,IAAID,eAAe,EAAE,IAAI,CAACE,IAAI,CAAC,SAAS,EAAEF,eAAe,CAAC;MAE1D,MAAMG,OAAO,GAAkB,IAAI,CAAClB,GAAG,GAAG;QAAEmB,eAAe,EAAE;MAAU,CAAE,GAAG,EAAE;MAC9E,MAAMC,GAAG,GAAG,MAAM,MAAM,CAAC,oBAAoB,CAAC;MAC9C,MAAMR,OAAO,GAAGQ,GAAG,CAACR,OAAO;MAC3B,IAAI,CAACP,SAAS,GAAGO,OAAO,CAAC,GAAGE,IAAI,IAAID,IAAI,EAAE,EAAEK,OAAO,CAAC;MACpD,IAAI,CAACZ,SAAS,GAAG,IAAI,CAACD,SAAS,CAACJ,QAAQ,CAACoB,SAAS,EAAE;MACpD,IAAI,CAACC,iBAAiB,EAAE;MAExB,IAAI,CAACf,SAAS,GAAG,IAAI,CAACF,SAAS,CAACkB,QAAQ,CAACC,SAAS,EAAE;MACpD,IAAI,IAAI,CAACxB,GAAG,EAAE;QACZ,IAAI,CAACyB,WAAW,EAAE,CAACC,KAAK,CAAEC,CAAC,IAAK,IAAI,CAACC,IAAI,CAAC,OAAO,EAAED,CAAC,CAAC,CAAC;OACvD,MAAM;QACL,IAAI,CAACE,OAAO,EAAE,CAACH,KAAK,CAAEC,CAAC,IAAK,IAAI,CAACC,IAAI,CAAC,OAAO,EAAED,CAAC,CAAC,CAAC;;MAGpD,MAAM,IAAI,CAACrB,SAAU,CAACwB,KAAK;MAC3Bd,GAAG,CAAC,cAAc,CAAC;MACnB,IAAI,CAACf,QAAQ,GAAG,IAAI;MACpB,IAAI,CAAC2B,IAAI,CAAC,SAAS,CAAC;MAEpB,OAAO,IAAI;KACZ,CAAC,OAAOD,CAAC,EAAE;MACV,IAAI,CAACC,IAAI,CAAC,OAAO,EAAED,CAAC,CAAC;;EAEzB;EAEA,MAAME,OAAOA,CAAA;IACX;IACA,OAAO,IAAI,EAAE;MACXb,GAAG,CAAC,iCAAiC,CAAC;MACtC,MAAM;QAAEe,IAAI;QAAEC;MAAK,CAAE,GAAG,MAAM,IAAI,CAACzB,SAAU,CAAC0B,IAAI,EAAE;MACpDjB,GAAG,CAAC,qBAAqB,EAAEe,IAAI,EAAEC,KAAK,CAAC;MACvC,IAAID,IAAI,EAAE;QACRf,GAAG,CAAC,MAAM,CAAC;QACX;;MAEF,IAAI,CAACY,IAAI,CAAC,MAAM,EAAEM,MAAM,CAACC,IAAI,CAACH,KAAK,CAAC,CAAC;;EAEzC;EAEA,MAAMP,WAAWA,CAAA;IACfT,GAAG,CAAC,uCAAuC,CAAC;IAC5C,MAAM;MAAEe,IAAI;MAAEC;IAAK,CAAE,GAAG,MAAM,IAAI,CAACzB,SAAU,CAAC0B,IAAI,EAAE;IACpDjB,GAAG,CAAC,2BAA2B,EAAEe,IAAI,EAAEC,KAAK,CAAC;IAC7C,IAAI,CAACJ,IAAI,CAAC,MAAM,EAAEM,MAAM,CAACC,IAAI,CAACH,KAAK,CAAC,CAAC;EACvC;EAEAI,KAAKA,CACHC,IAAyB,EACzBC,QAAA,GAA2B,MAAM,EACjCC,QAAA,GAAyCA,CAAA,KAAK,CAAE,CAAC;IAEjD,IAAIF,IAAI,CAACG,MAAM,KAAK,CAAC,EAAE,OAAOD,QAAQ,EAAE;IACxC,IAAI,OAAOF,IAAI,KAAK,QAAQ,EAAEA,IAAI,GAAGH,MAAM,CAACC,IAAI,CAACE,IAAI,EAAEC,QAAQ,CAAC;IAEhEtB,GAAG,CAAC,sBAAsB,EAAEqB,IAAI,CAAC;IACjC,IAAI,CAAC/B,SAAU,CAAC8B,KAAK,CAACC,IAAI,CAAC,CAACI,IAAI,CAC9B,MAAK;MACHzB,GAAG,CAAC,WAAW,CAAC;MAChBuB,QAAQ,EAAE;IACZ,CAAC,EACAG,GAAG,IAAI;MACN1B,GAAG,CAAC,YAAY,EAAE0B,GAAG,CAAC;MACtBH,QAAQ,CAACG,GAAG,CAAC;IACf,CAAC,CACF;IACD,OAAO,IAAI;EACb;EAEAC,GAAGA,CAACN,IAAI,GAAGH,MAAM,CAACU,KAAK,CAAC,CAAC,CAAC,EAAEN,QAAA,GAA2B,MAAM,EAAEC,QAAA,GAAyCA,CAAA,KAAK,CAAE,CAAC;IAC9GvB,GAAG,CAAC,kBAAkB,CAAC;IACvB,IAAI,CAACoB,KAAK,CAACC,IAAI,EAAEC,QAAQ,EAAGI,GAAG,IAAI;MACjC,IAAI,CAACrC,SAAU,CAACwC,KAAK,EAAE;MACvB,IAAIN,QAAQ,EAAEA,QAAQ,CAACG,GAAG,CAAC;IAC7B,CAAC,CAAC;IACF,OAAO,IAAI;EACb;EAEAI,OAAOA,CAACC,MAAc;IACpB/B,GAAG,CAAC,sBAAsB,EAAE+B,MAAM,CAAC;IACnC,IAAI,CAAC7C,SAAS,GAAG,IAAI;IACrB,OAAO,IAAI,CAACyC,GAAG,EAAE;EACnB;EAEAK,QAAQA,CAAC9B,OAAmB;IAC1B,IAAI,IAAI,CAACd,SAAS,EAAE;MAClB;MACA,IAAI,CAACwB,IAAI,CAAC,OAAO,EAAE,qDAAqD,CAAC;MACzE;;IAEF,IAAI,CAACtB,SAAU,CAAC2C,WAAW,EAAE;IAC7B,IAAI,CAAC1C,SAAU,CAAC0C,WAAW,EAAE;IAC7B,IAAI,CAAC9C,UAAU,GAAG,IAAI;IACtB,IAAI,CAACE,SAAS,GAAG,IAAI,CAACA,SAAU,CAAC2C,QAAQ,CAAC9B,OAAO,CAAC;IAClD,IAAI,CAACZ,SAAS,GAAG,IAAI,CAACD,SAAS,CAACJ,QAAQ,CAACoB,SAAS,EAAE;IACpD,IAAI,CAACd,SAAS,GAAG,IAAI,CAACF,SAAS,CAACkB,QAAQ,CAACC,SAAS,EAAE;IACpD,IAAI,CAACF,iBAAiB,EAAE;IACxB,IAAI,CAACO,OAAO,EAAE,CAACH,KAAK,CAAEC,CAAC,IAAK,IAAI,CAACC,IAAI,CAAC,OAAO,EAAED,CAAC,CAAC,CAAC;EACpD;EAEAL,iBAAiBA,CAAA;IACf,IAAI,CAACjB,SAAU,CAAC6C,MAAM,CAACT,IAAI,CAAC,MAAK;MAC/B,IAAI,CAAC,IAAI,CAACtC,UAAU,EAAE;QACpBa,GAAG,CAAC,kBAAkB,CAAC;QACvB,IAAI,CAACX,SAAS,GAAG,IAAI;QACrB,IAAI,CAACuB,IAAI,CAAC,OAAO,CAAC;OACnB,MAAM;QACL,IAAI,CAACzB,UAAU,GAAG,KAAK;QACvB,IAAI,CAACC,SAAS,GAAG,IAAI;;IAEzB,CAAC,CAAC,CAACsB,KAAK,CAAEC,CAAC,IAAK,IAAI,CAACC,IAAI,CAAC,OAAO,EAAED,CAAC,CAAC,CAAC;EACxC;;AA9IFwB,OAAA,CAAAtD,gBAAA,GAAAA,gBAAA;AAiJA,MAAMuD,KAAK,GAAG,KAAK;AAEnB,SAASC,IAAIA,CAAChB,IAAa;EACzB,IAAIA,IAAI,YAAYiB,UAAU,IAAIjB,IAAI,YAAYkB,WAAW,EAAE;IAC7D,MAAMC,GAAG,GAAGtB,MAAM,CAACC,IAAI,CAACE,IAAI,CAAC,CAACoB,QAAQ,CAAC,KAAK,CAAC;IAC7C,MAAMC,GAAG,GAAG,IAAIC,WAAW,EAAE,CAACC,MAAM,CAACvB,IAAI,CAAC;IAC1C,OAAO,eAAeqB,GAAG,CAACG,OAAO,CAAC,KAAK,EAAE,KAAK,CAAC,eAAeL,GAAG,IAAI;GACtE,MAAM;IACL,OAAOnB,IAAI;;AAEf;AAEA,SAASrB,GAAGA,CAAC,GAAG8C,IAAe;EAC7BV,KAAK,IAAIW,OAAO,CAAC/C,GAAG,CAAC,GAAG8C,IAAI,CAACE,GAAG,CAACX,IAAI,CAAC,CAAC;AACzC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}