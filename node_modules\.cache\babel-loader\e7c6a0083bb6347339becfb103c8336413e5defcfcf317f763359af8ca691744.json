{"ast": null, "code": "/**\n * @license\n * Copyright 2018 Google LLC\n *\n * Use of this source code is governed by an MIT-style\n * license that can be found in the LICENSE file or at\n * https://opensource.org/licenses/MIT.\n * =============================================================================\n */\n/**\n * TensorFlow.js Layers: Convolutional Layers\n */\nimport * as tfc from '@tensorflow/tfjs-core';\nimport { serialization, tidy } from '@tensorflow/tfjs-core';\nimport { getActivation, serializeActivation } from '../activations';\nimport { imageDataFormat } from '../backend/common';\nimport * as K from '../backend/tfjs_backend';\nimport { checkDataFormat, checkInterpolationFormat, checkPaddingMode } from '../common';\nimport { getConstraint, serializeConstraint } from '../constraints';\nimport { InputSpec, Layer } from '../engine/topology';\nimport { NotImplementedError, ValueError } from '../errors';\nimport { getInitializer, serializeInitializer } from '../initializers';\nimport { getRegularizer, serializeRegularizer } from '../regularizers';\nimport { convOutputLength, deconvLength, normalizeArray } from '../utils/conv_utils';\nimport * as generic_utils from '../utils/generic_utils';\nimport { getExactlyOneShape, getExactlyOneTensor } from '../utils/types_utils';\n/**\n * Transpose and cast the input before the conv2d.\n * @param x Input image tensor.\n * @param dataFormat\n */\nexport function preprocessConv2DInput(x, dataFormat) {\n  // TODO(cais): Cast type to float32 if not.\n  return tidy(() => {\n    checkDataFormat(dataFormat);\n    if (dataFormat === 'channelsFirst') {\n      return tfc.transpose(x, [0, 2, 3, 1]); // NCHW -> NHWC.\n    } else {\n      return x;\n    }\n  });\n}\n/**\n * Transpose and cast the input before the conv3d.\n * @param x Input image tensor.\n * @param dataFormat\n */\nexport function preprocessConv3DInput(x, dataFormat) {\n  return tidy(() => {\n    checkDataFormat(dataFormat);\n    if (dataFormat === 'channelsFirst') {\n      return tfc.transpose(x, [0, 2, 3, 4, 1]); // NCDHW -> NDHWC.\n    } else {\n      return x;\n    }\n  });\n}\n/**\n * 1D-convolution with bias added.\n *\n * Porting Note: This function does not exist in the Python Keras backend.\n *   It is exactly the same as `conv2d`, except the added `bias`.\n *\n * @param x Input tensor, rank-3, of shape `[batchSize, width, inChannels]`.\n * @param kernel Kernel, rank-3, of shape `[filterWidth, inDepth, outDepth]`.\n * @param bias Bias, rank-3, of shape `[outDepth]`.\n * @param strides\n * @param padding Padding mode.\n * @param dataFormat Data format.\n * @param dilationRate\n * @returns The result of the 1D convolution.\n * @throws ValueError, if `x`, `kernel` or `bias` is not of the correct rank.\n */\nexport function conv1dWithBias(x, kernel, bias) {\n  let strides = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : 1;\n  let padding = arguments.length > 4 && arguments[4] !== undefined ? arguments[4] : 'valid';\n  let dataFormat = arguments.length > 5 ? arguments[5] : undefined;\n  let dilationRate = arguments.length > 6 && arguments[6] !== undefined ? arguments[6] : 1;\n  return tidy(() => {\n    if (dataFormat == null) {\n      dataFormat = imageDataFormat();\n    }\n    checkDataFormat(dataFormat);\n    // Check the ranks of x, kernel and bias.\n    if (x.shape.length !== 3) {\n      throw new ValueError(\"The input of a conv1dWithBias operation should be 3, but is \" + \"\".concat(x.shape.length, \" instead.\"));\n    }\n    if (kernel.shape.length !== 3) {\n      throw new ValueError(\"The kernel for a conv1dWithBias operation should be 3, but is \" + \"\".concat(kernel.shape.length, \" instead\"));\n    }\n    if (bias != null && bias.shape.length !== 1) {\n      throw new ValueError(\"The bias for a conv1dWithBias operation should be 1, but is \" + \"\".concat(bias.shape.length, \" instead\"));\n    }\n    // TODO(cais): Support CAUSAL padding mode.\n    if (dataFormat === 'channelsFirst') {\n      x = tfc.transpose(x, [0, 2, 1]); // NCW -> NWC.\n    }\n    if (padding === 'causal') {\n      throw new NotImplementedError('The support for CAUSAL padding mode in conv1dWithBias is not ' + 'implemented yet.');\n    }\n    let y = tfc.conv1d(x, kernel, strides, padding === 'same' ? 'same' : 'valid', 'NWC', dilationRate);\n    if (bias != null) {\n      y = K.biasAdd(y, bias);\n    }\n    return y;\n  });\n}\n/**\n * 1D-convolution.\n *\n * @param x Input tensor, rank-3, of shape `[batchSize, width, inChannels]`.\n * @param kernel Kernel, rank-3, of shape `[filterWidth, inDepth, outDepth]`.s\n * @param strides\n * @param padding Padding mode.\n * @param dataFormat Data format.\n * @param dilationRate\n * @returns The result of the 1D convolution.\n * @throws ValueError, if `x`, `kernel` or `bias` is not of the correct rank.\n */\nexport function conv1d(x, kernel) {\n  let strides = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 1;\n  let padding = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : 'valid';\n  let dataFormat = arguments.length > 4 ? arguments[4] : undefined;\n  let dilationRate = arguments.length > 5 && arguments[5] !== undefined ? arguments[5] : 1;\n  return tidy(() => {\n    checkDataFormat(dataFormat);\n    return conv1dWithBias(x, kernel, null, strides, padding, dataFormat, dilationRate);\n  });\n}\n/**\n * 2D Convolution\n * @param x\n * @param kernel kernel of the convolution.\n * @param strides strides array.\n * @param padding padding mode. Default to 'valid'.\n * @param dataFormat data format. Defaults to 'channelsLast'.\n * @param dilationRate dilation rate array.\n * @returns Result of the 2D pooling.\n */\nexport function conv2d(x, kernel) {\n  let strides = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : [1, 1];\n  let padding = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : 'valid';\n  let dataFormat = arguments.length > 4 ? arguments[4] : undefined;\n  let dilationRate = arguments.length > 5 ? arguments[5] : undefined;\n  return tidy(() => {\n    checkDataFormat(dataFormat);\n    return conv2dWithBiasActivation(x, kernel, null, strides, padding, dataFormat, dilationRate);\n  });\n}\n/**\n * 2D Convolution with an added bias and optional activation.\n * Note: This function does not exist in the Python Keras Backend. This function\n * is exactly the same as `conv2d`, except the added `bias`.\n */\nexport function conv2dWithBiasActivation(x, kernel, bias) {\n  let strides = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : [1, 1];\n  let padding = arguments.length > 4 && arguments[4] !== undefined ? arguments[4] : 'valid';\n  let dataFormat = arguments.length > 5 ? arguments[5] : undefined;\n  let dilationRate = arguments.length > 6 ? arguments[6] : undefined;\n  let activation = arguments.length > 7 && arguments[7] !== undefined ? arguments[7] : null;\n  return tidy(() => {\n    if (dataFormat == null) {\n      dataFormat = imageDataFormat();\n    }\n    checkDataFormat(dataFormat);\n    if (x.rank !== 3 && x.rank !== 4) {\n      throw new ValueError(\"conv2dWithBiasActivation expects input to be of rank 3 or 4, \" + \"but received \".concat(x.rank, \".\"));\n    }\n    if (kernel.rank !== 3 && kernel.rank !== 4) {\n      throw new ValueError(\"conv2dWithBiasActivation expects kernel to be of rank 3 or 4, \" + \"but received \".concat(x.rank, \".\"));\n    }\n    let y = preprocessConv2DInput(x, dataFormat);\n    if (padding === 'causal') {\n      throw new NotImplementedError('The support for CAUSAL padding mode in conv1dWithBias is not ' + 'implemented yet.');\n    }\n    y = tfc.fused.conv2d({\n      x: y,\n      filter: kernel,\n      strides: strides,\n      pad: padding === 'same' ? 'same' : 'valid',\n      dilations: dilationRate,\n      dataFormat: 'NHWC',\n      bias,\n      activation\n    });\n    if (dataFormat === 'channelsFirst') {\n      y = tfc.transpose(y, [0, 3, 1, 2]);\n    }\n    return y;\n  });\n}\n/**\n * 3D Convolution.\n * @param x\n * @param kernel kernel of the convolution.\n * @param strides strides array.\n * @param padding padding mode. Default to 'valid'.\n * @param dataFormat data format. Defaults to 'channelsLast'.\n * @param dilationRate dilation rate array.\n * @returns Result of the 3D convolution.\n */\nexport function conv3d(x, kernel) {\n  let strides = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : [1, 1, 1];\n  let padding = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : 'valid';\n  let dataFormat = arguments.length > 4 ? arguments[4] : undefined;\n  let dilationRate = arguments.length > 5 ? arguments[5] : undefined;\n  return tidy(() => {\n    checkDataFormat(dataFormat);\n    return conv3dWithBias(x, kernel, null, strides, padding, dataFormat, dilationRate);\n  });\n}\n/**\n * 3D Convolution with an added bias.\n * Note: This function does not exist in the Python Keras Backend. This function\n * is exactly the same as `conv3d`, except the added `bias`.\n */\nexport function conv3dWithBias(x, kernel, bias) {\n  let strides = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : [1, 1, 1];\n  let padding = arguments.length > 4 && arguments[4] !== undefined ? arguments[4] : 'valid';\n  let dataFormat = arguments.length > 5 ? arguments[5] : undefined;\n  let dilationRate = arguments.length > 6 ? arguments[6] : undefined;\n  return tidy(() => {\n    if (dataFormat == null) {\n      dataFormat = imageDataFormat();\n    }\n    checkDataFormat(dataFormat);\n    if (x.rank !== 4 && x.rank !== 5) {\n      throw new ValueError(\"conv3dWithBias expects input to be of rank 4 or 5, but received \" + \"\".concat(x.rank, \".\"));\n    }\n    if (kernel.rank !== 4 && kernel.rank !== 5) {\n      throw new ValueError(\"conv3dWithBias expects kernel to be of rank 4 or 5, but received \" + \"\".concat(x.rank, \".\"));\n    }\n    let y = preprocessConv3DInput(x, dataFormat);\n    if (padding === 'causal') {\n      throw new NotImplementedError('The support for CAUSAL padding mode in conv3dWithBias is not ' + 'implemented yet.');\n    }\n    y = tfc.conv3d(y, kernel, strides, padding === 'same' ? 'same' : 'valid', 'NDHWC', dilationRate);\n    if (bias != null) {\n      y = K.biasAdd(y, bias);\n    }\n    if (dataFormat === 'channelsFirst') {\n      y = tfc.transpose(y, [0, 4, 1, 2, 3]);\n    }\n    return y;\n  });\n}\n/**\n * Abstract convolution layer.\n */\nexport class BaseConv extends Layer {\n  constructor(rank, args) {\n    super(args);\n    this.bias = null;\n    this.DEFAULT_KERNEL_INITIALIZER = 'glorotNormal';\n    this.DEFAULT_BIAS_INITIALIZER = 'zeros';\n    BaseConv.verifyArgs(args);\n    this.rank = rank;\n    generic_utils.assertPositiveInteger(this.rank, 'rank');\n    if (this.rank !== 1 && this.rank !== 2 && this.rank !== 3) {\n      throw new NotImplementedError(\"Convolution layer for rank other than 1, 2, or 3 (\".concat(this.rank, \") is \") + \"not implemented yet.\");\n    }\n    this.kernelSize = normalizeArray(args.kernelSize, rank, 'kernelSize');\n    this.strides = normalizeArray(args.strides == null ? 1 : args.strides, rank, 'strides');\n    this.padding = args.padding == null ? 'valid' : args.padding;\n    checkPaddingMode(this.padding);\n    this.dataFormat = args.dataFormat == null ? 'channelsLast' : args.dataFormat;\n    checkDataFormat(this.dataFormat);\n    this.activation = getActivation(args.activation);\n    this.useBias = args.useBias == null ? true : args.useBias;\n    this.biasInitializer = getInitializer(args.biasInitializer || this.DEFAULT_BIAS_INITIALIZER);\n    this.biasConstraint = getConstraint(args.biasConstraint);\n    this.biasRegularizer = getRegularizer(args.biasRegularizer);\n    this.activityRegularizer = getRegularizer(args.activityRegularizer);\n    this.dilationRate = normalizeArray(args.dilationRate == null ? 1 : args.dilationRate, rank, 'dilationRate');\n    if (this.rank === 1 && Array.isArray(this.dilationRate) && this.dilationRate.length !== 1) {\n      throw new ValueError(\"dilationRate must be a number or an array of a single number \" + \"for 1D convolution, but received \" + \"\".concat(JSON.stringify(this.dilationRate)));\n    } else if (this.rank === 2) {\n      if (typeof this.dilationRate === 'number') {\n        this.dilationRate = [this.dilationRate, this.dilationRate];\n      } else if (this.dilationRate.length !== 2) {\n        throw new ValueError(\"dilationRate must be a number or array of two numbers for 2D \" + \"convolution, but received \".concat(JSON.stringify(this.dilationRate)));\n      }\n    } else if (this.rank === 3) {\n      if (typeof this.dilationRate === 'number') {\n        this.dilationRate = [this.dilationRate, this.dilationRate, this.dilationRate];\n      } else if (this.dilationRate.length !== 3) {\n        throw new ValueError(\"dilationRate must be a number or array of three numbers for 3D \" + \"convolution, but received \".concat(JSON.stringify(this.dilationRate)));\n      }\n    }\n  }\n  static verifyArgs(args) {\n    // Check config.kernelSize type and shape.\n    generic_utils.assert('kernelSize' in args, \"required key 'kernelSize' not in config\");\n    if (typeof args.kernelSize !== 'number' && !generic_utils.checkArrayTypeAndLength(args.kernelSize, 'number', 1, 3)) {\n      throw new ValueError(\"BaseConv expects config.kernelSize to be number or number[] with \" + \"length 1, 2, or 3, but received \".concat(JSON.stringify(args.kernelSize), \".\"));\n    }\n  }\n  getConfig() {\n    const config = {\n      kernelSize: this.kernelSize,\n      strides: this.strides,\n      padding: this.padding,\n      dataFormat: this.dataFormat,\n      dilationRate: this.dilationRate,\n      activation: serializeActivation(this.activation),\n      useBias: this.useBias,\n      biasInitializer: serializeInitializer(this.biasInitializer),\n      biasRegularizer: serializeRegularizer(this.biasRegularizer),\n      activityRegularizer: serializeRegularizer(this.activityRegularizer),\n      biasConstraint: serializeConstraint(this.biasConstraint)\n    };\n    const baseConfig = super.getConfig();\n    Object.assign(config, baseConfig);\n    return config;\n  }\n}\n/**\n * Abstract nD convolution layer.  Ancestor of convolution layers which reduce\n * across channels, i.e., Conv1D and Conv2D, but not DepthwiseConv2D.\n */\nexport class Conv extends BaseConv {\n  constructor(rank, args) {\n    super(rank, args);\n    this.kernel = null;\n    Conv.verifyArgs(args);\n    this.filters = args.filters;\n    generic_utils.assertPositiveInteger(this.filters, 'filters');\n    this.kernelInitializer = getInitializer(args.kernelInitializer || this.DEFAULT_KERNEL_INITIALIZER);\n    this.kernelConstraint = getConstraint(args.kernelConstraint);\n    this.kernelRegularizer = getRegularizer(args.kernelRegularizer);\n  }\n  build(inputShape) {\n    inputShape = getExactlyOneShape(inputShape);\n    const channelAxis = this.dataFormat === 'channelsFirst' ? 1 : inputShape.length - 1;\n    if (inputShape[channelAxis] == null) {\n      throw new ValueError(\"The channel dimension of the input should be defined. \" + \"Found \".concat(inputShape[channelAxis]));\n    }\n    const inputDim = inputShape[channelAxis];\n    const kernelShape = this.kernelSize.concat([inputDim, this.filters]);\n    this.kernel = this.addWeight('kernel', kernelShape, null, this.kernelInitializer, this.kernelRegularizer, true, this.kernelConstraint);\n    if (this.useBias) {\n      this.bias = this.addWeight('bias', [this.filters], null, this.biasInitializer, this.biasRegularizer, true, this.biasConstraint);\n    }\n    this.inputSpec = [{\n      ndim: this.rank + 2,\n      axes: {\n        [channelAxis]: inputDim\n      }\n    }];\n    this.built = true;\n  }\n  call(inputs, kwargs) {\n    return tidy(() => {\n      inputs = getExactlyOneTensor(inputs);\n      let outputs;\n      const biasValue = this.bias == null ? null : this.bias.read();\n      const fusedActivationName = generic_utils.mapActivationToFusedKernel(this.activation.getClassName());\n      if (fusedActivationName != null && this.rank === 2) {\n        outputs = conv2dWithBiasActivation(inputs, this.kernel.read(), biasValue, this.strides, this.padding, this.dataFormat, this.dilationRate, fusedActivationName);\n      } else {\n        if (this.rank === 1) {\n          outputs = conv1dWithBias(inputs, this.kernel.read(), biasValue, this.strides[0], this.padding, this.dataFormat, this.dilationRate[0]);\n        } else if (this.rank === 2) {\n          // TODO(cais): Move up to constructor.\n          outputs = conv2dWithBiasActivation(inputs, this.kernel.read(), biasValue, this.strides, this.padding, this.dataFormat, this.dilationRate);\n        } else if (this.rank === 3) {\n          outputs = conv3dWithBias(inputs, this.kernel.read(), biasValue, this.strides, this.padding, this.dataFormat, this.dilationRate);\n        } else {\n          throw new NotImplementedError('convolutions greater than 3D are not implemented yet.');\n        }\n        if (this.activation != null) {\n          outputs = this.activation.apply(outputs);\n        }\n      }\n      return outputs;\n    });\n  }\n  computeOutputShape(inputShape) {\n    inputShape = getExactlyOneShape(inputShape);\n    const newSpace = [];\n    const space = this.dataFormat === 'channelsLast' ? inputShape.slice(1, inputShape.length - 1) : inputShape.slice(2);\n    for (let i = 0; i < space.length; ++i) {\n      const newDim = convOutputLength(space[i], this.kernelSize[i], this.padding, this.strides[i], typeof this.dilationRate === 'number' ? this.dilationRate : this.dilationRate[i]);\n      newSpace.push(newDim);\n    }\n    let outputShape = [inputShape[0]];\n    if (this.dataFormat === 'channelsLast') {\n      outputShape = outputShape.concat(newSpace);\n      outputShape.push(this.filters);\n    } else {\n      outputShape.push(this.filters);\n      outputShape = outputShape.concat(newSpace);\n    }\n    return outputShape;\n  }\n  getConfig() {\n    const config = {\n      filters: this.filters,\n      kernelInitializer: serializeInitializer(this.kernelInitializer),\n      kernelRegularizer: serializeRegularizer(this.kernelRegularizer),\n      kernelConstraint: serializeConstraint(this.kernelConstraint)\n    };\n    const baseConfig = super.getConfig();\n    Object.assign(config, baseConfig);\n    return config;\n  }\n  static verifyArgs(args) {\n    // Check config.filters type, shape, and value.\n    if (!('filters' in args) || typeof args.filters !== 'number' || args.filters < 1) {\n      throw new ValueError(\"Convolution layer expected config.filters to be a 'number' > 0 \" + \"but got \".concat(JSON.stringify(args.filters)));\n    }\n  }\n}\nclass Conv2D extends Conv {\n  constructor(args) {\n    super(2, args);\n    Conv2D.verifyArgs(args);\n  }\n  getConfig() {\n    const config = super.getConfig();\n    delete config['rank'];\n    return config;\n  }\n  static verifyArgs(args) {\n    // config.kernelSize must be a number or array of numbers.\n    if (typeof args.kernelSize !== 'number' && !generic_utils.checkArrayTypeAndLength(args.kernelSize, 'number', 1, 2)) {\n      throw new ValueError(\"Conv2D expects config.kernelSize to be number or number[] with \" + \"length 1 or 2, but received \".concat(JSON.stringify(args.kernelSize), \".\"));\n    }\n  }\n}\n/** @nocollapse */\nConv2D.className = 'Conv2D';\nexport { Conv2D };\nserialization.registerClass(Conv2D);\nclass Conv3D extends Conv {\n  constructor(args) {\n    super(3, args);\n    Conv3D.verifyArgs(args);\n  }\n  getConfig() {\n    const config = super.getConfig();\n    delete config['rank'];\n    return config;\n  }\n  static verifyArgs(args) {\n    // config.kernelSize must be a number or array of numbers.\n    if (typeof args.kernelSize !== 'number') {\n      if (!(Array.isArray(args.kernelSize) && (args.kernelSize.length === 1 || args.kernelSize.length === 3))) {\n        throw new ValueError(\"Conv3D expects config.kernelSize to be number or\" + \" [number, number, number], but received \".concat(JSON.stringify(args.kernelSize), \".\"));\n      }\n    }\n  }\n}\n/** @nocollapse */\nConv3D.className = 'Conv3D';\nexport { Conv3D };\nserialization.registerClass(Conv3D);\nclass Conv2DTranspose extends Conv2D {\n  constructor(args) {\n    super(args);\n    this.inputSpec = [new InputSpec({\n      ndim: 4\n    })];\n    if (this.padding !== 'same' && this.padding !== 'valid') {\n      throw new ValueError(\"Conv2DTranspose currently supports only padding modes 'same' \" + \"and 'valid', but received padding mode \".concat(this.padding));\n    }\n  }\n  build(inputShape) {\n    inputShape = getExactlyOneShape(inputShape);\n    if (inputShape.length !== 4) {\n      throw new ValueError('Input should have rank 4; Received input shape: ' + JSON.stringify(inputShape));\n    }\n    const channelAxis = this.dataFormat === 'channelsFirst' ? 1 : inputShape.length - 1;\n    if (inputShape[channelAxis] == null) {\n      throw new ValueError('The channel dimension of the inputs should be defined. ' + 'Found `None`.');\n    }\n    const inputDim = inputShape[channelAxis];\n    const kernelShape = this.kernelSize.concat([this.filters, inputDim]);\n    this.kernel = this.addWeight('kernel', kernelShape, 'float32', this.kernelInitializer, this.kernelRegularizer, true, this.kernelConstraint);\n    if (this.useBias) {\n      this.bias = this.addWeight('bias', [this.filters], 'float32', this.biasInitializer, this.biasRegularizer, true, this.biasConstraint);\n    }\n    // Set input spec.\n    this.inputSpec = [new InputSpec({\n      ndim: 4,\n      axes: {\n        [channelAxis]: inputDim\n      }\n    })];\n    this.built = true;\n  }\n  call(inputs, kwargs) {\n    return tfc.tidy(() => {\n      let input = getExactlyOneTensor(inputs);\n      if (input.shape.length !== 4) {\n        throw new ValueError(\"Conv2DTranspose.call() expects input tensor to be rank-4, but \" + \"received a tensor of rank-\".concat(input.shape.length));\n      }\n      const inputShape = input.shape;\n      const batchSize = inputShape[0];\n      let hAxis;\n      let wAxis;\n      if (this.dataFormat === 'channelsFirst') {\n        hAxis = 2;\n        wAxis = 3;\n      } else {\n        hAxis = 1;\n        wAxis = 2;\n      }\n      const height = inputShape[hAxis];\n      const width = inputShape[wAxis];\n      const kernelH = this.kernelSize[0];\n      const kernelW = this.kernelSize[1];\n      const strideH = this.strides[0];\n      const strideW = this.strides[1];\n      // Infer the dynamic output shape.\n      const outHeight = deconvLength(height, strideH, kernelH, this.padding);\n      const outWidth = deconvLength(width, strideW, kernelW, this.padding);\n      // Porting Note: We don't branch based on `this.dataFormat` here,\n      // because\n      //   the tjfs-core function `conv2dTranspose` called below always\n      //   assumes channelsLast.\n      const outputShape = [batchSize, outHeight, outWidth, this.filters];\n      if (this.dataFormat !== 'channelsLast') {\n        input = tfc.transpose(input, [0, 2, 3, 1]);\n      }\n      let outputs = tfc.conv2dTranspose(input, this.kernel.read(), outputShape, this.strides, this.padding);\n      if (this.dataFormat !== 'channelsLast') {\n        outputs = tfc.transpose(outputs, [0, 3, 1, 2]);\n      }\n      if (this.bias != null) {\n        outputs = K.biasAdd(outputs, this.bias.read(), this.dataFormat);\n      }\n      if (this.activation != null) {\n        outputs = this.activation.apply(outputs);\n      }\n      return outputs;\n    });\n  }\n  computeOutputShape(inputShape) {\n    inputShape = getExactlyOneShape(inputShape);\n    const outputShape = inputShape.slice();\n    let channelAxis;\n    let heightAxis;\n    let widthAxis;\n    if (this.dataFormat === 'channelsFirst') {\n      channelAxis = 1;\n      heightAxis = 2;\n      widthAxis = 3;\n    } else {\n      channelAxis = 3;\n      heightAxis = 1;\n      widthAxis = 2;\n    }\n    const kernelH = this.kernelSize[0];\n    const kernelW = this.kernelSize[1];\n    const strideH = this.strides[0];\n    const strideW = this.strides[1];\n    outputShape[channelAxis] = this.filters;\n    outputShape[heightAxis] = deconvLength(outputShape[heightAxis], strideH, kernelH, this.padding);\n    outputShape[widthAxis] = deconvLength(outputShape[widthAxis], strideW, kernelW, this.padding);\n    return outputShape;\n  }\n  getConfig() {\n    const config = super.getConfig();\n    delete config['dilationRate'];\n    return config;\n  }\n}\n/** @nocollapse */\nConv2DTranspose.className = 'Conv2DTranspose';\nexport { Conv2DTranspose };\nserialization.registerClass(Conv2DTranspose);\nclass Conv3DTranspose extends Conv3D {\n  constructor(args) {\n    super(args);\n    this.inputSpec = [new InputSpec({\n      ndim: 5\n    })];\n    if (this.padding !== 'same' && this.padding !== 'valid') {\n      throw new ValueError(\"Conv3DTranspose currently supports only padding modes 'same' \" + \"and 'valid', but received padding mode \".concat(this.padding));\n    }\n  }\n  build(inputShape) {\n    inputShape = getExactlyOneShape(inputShape);\n    if (inputShape.length !== 5) {\n      throw new ValueError('Input should have rank 5; Received input shape: ' + JSON.stringify(inputShape));\n    }\n    const channelAxis = this.dataFormat === 'channelsFirst' ? 1 : inputShape.length - 1;\n    if (inputShape[channelAxis] == null) {\n      throw new ValueError('The channel dimension of the inputs should be defined. ' + 'Found `None`.');\n    }\n    const inputDim = inputShape[channelAxis];\n    const kernelShape = this.kernelSize.concat([this.filters, inputDim]);\n    this.kernel = this.addWeight('kernel', kernelShape, 'float32', this.kernelInitializer, this.kernelRegularizer, true, this.kernelConstraint);\n    if (this.useBias) {\n      this.bias = this.addWeight('bias', [this.filters], 'float32', this.biasInitializer, this.biasRegularizer, true, this.biasConstraint);\n    }\n    // Set input spec.\n    this.inputSpec = [new InputSpec({\n      ndim: 5,\n      axes: {\n        [channelAxis]: inputDim\n      }\n    })];\n    this.built = true;\n  }\n  call(inputs, kwargs) {\n    return tfc.tidy(() => {\n      let input = getExactlyOneTensor(inputs);\n      if (input.shape.length !== 5) {\n        throw new ValueError(\"Conv3DTranspose.call() expects input tensor to be rank-4, but \" + \"received a tensor of rank-\".concat(input.shape.length));\n      }\n      const inputShape = input.shape;\n      const batchSize = inputShape[0];\n      let hAxis;\n      let wAxis;\n      let dAxis;\n      if (this.dataFormat === 'channelsFirst') {\n        dAxis = 2;\n        hAxis = 3;\n        wAxis = 4;\n      } else {\n        dAxis = 1;\n        hAxis = 2;\n        wAxis = 3;\n      }\n      const depth = inputShape[dAxis];\n      const height = inputShape[hAxis];\n      const width = inputShape[wAxis];\n      const kernelD = this.kernelSize[0];\n      const kernelH = this.kernelSize[1];\n      const kernelW = this.kernelSize[2];\n      const strideD = this.strides[0];\n      const strideH = this.strides[1];\n      const strideW = this.strides[2];\n      // Infer the dynamic output shape.\n      const outDepth = deconvLength(depth, strideD, kernelD, this.padding);\n      const outHeight = deconvLength(height, strideH, kernelH, this.padding);\n      const outWidth = deconvLength(width, strideW, kernelW, this.padding);\n      // Same as `conv2dTranspose`. We always assumes channelsLast.\n      const outputShape = [batchSize, outDepth, outHeight, outWidth, this.filters];\n      if (this.dataFormat !== 'channelsLast') {\n        input = tfc.transpose(input, [0, 2, 3, 4, 1]);\n      }\n      let outputs = tfc.conv3dTranspose(input, this.kernel.read(), outputShape, this.strides, this.padding);\n      if (this.dataFormat !== 'channelsLast') {\n        outputs = tfc.transpose(outputs, [0, 4, 1, 2, 3]);\n      }\n      if (this.bias !== null) {\n        outputs = K.biasAdd(outputs, this.bias.read(), this.dataFormat);\n      }\n      if (this.activation !== null) {\n        outputs = this.activation.apply(outputs);\n      }\n      return outputs;\n    });\n  }\n  computeOutputShape(inputShape) {\n    inputShape = getExactlyOneShape(inputShape);\n    const outputShape = inputShape.slice();\n    let channelAxis;\n    let depthAxis;\n    let heightAxis;\n    let widthAxis;\n    if (this.dataFormat === 'channelsFirst') {\n      channelAxis = 1;\n      depthAxis = 2;\n      heightAxis = 3;\n      widthAxis = 4;\n    } else {\n      channelAxis = 4;\n      depthAxis = 1;\n      heightAxis = 2;\n      widthAxis = 3;\n    }\n    const kernelD = this.kernelSize[0];\n    const kernelH = this.kernelSize[1];\n    const kernelW = this.kernelSize[2];\n    const strideD = this.strides[0];\n    const strideH = this.strides[1];\n    const strideW = this.strides[2];\n    outputShape[channelAxis] = this.filters;\n    outputShape[depthAxis] = deconvLength(outputShape[depthAxis], strideD, kernelD, this.padding);\n    outputShape[heightAxis] = deconvLength(outputShape[heightAxis], strideH, kernelH, this.padding);\n    outputShape[widthAxis] = deconvLength(outputShape[widthAxis], strideW, kernelW, this.padding);\n    return outputShape;\n  }\n  getConfig() {\n    const config = super.getConfig();\n    delete config['dilationRate'];\n    return config;\n  }\n}\n/** @nocollapse */\nConv3DTranspose.className = 'Conv3DTranspose';\nexport { Conv3DTranspose };\nserialization.registerClass(Conv3DTranspose);\nclass SeparableConv extends Conv {\n  constructor(rank, config) {\n    super(rank, config);\n    this.DEFAULT_DEPTHWISE_INITIALIZER = 'glorotUniform';\n    this.DEFAULT_POINTWISE_INITIALIZER = 'glorotUniform';\n    this.depthwiseKernel = null;\n    this.pointwiseKernel = null;\n    if (config.filters == null) {\n      throw new ValueError('The `filters` configuration field is required by SeparableConv, ' + 'but is unspecified.');\n    }\n    if (config.kernelInitializer != null || config.kernelRegularizer != null || config.kernelConstraint != null) {\n      throw new ValueError('Fields kernelInitializer, kernelRegularizer and kernelConstraint ' + 'are invalid for SeparableConv2D. Use depthwiseInitializer, ' + 'depthwiseRegularizer, depthwiseConstraint, pointwiseInitializer, ' + 'pointwiseRegularizer and pointwiseConstraint instead.');\n    }\n    if (config.padding != null && config.padding !== 'same' && config.padding !== 'valid') {\n      throw new ValueError(\"SeparableConv\".concat(this.rank, \"D supports only padding modes: \") + \"'same' and 'valid', but received \".concat(JSON.stringify(config.padding)));\n    }\n    this.depthMultiplier = config.depthMultiplier == null ? 1 : config.depthMultiplier;\n    this.depthwiseInitializer = getInitializer(config.depthwiseInitializer || this.DEFAULT_DEPTHWISE_INITIALIZER);\n    this.depthwiseRegularizer = getRegularizer(config.depthwiseRegularizer);\n    this.depthwiseConstraint = getConstraint(config.depthwiseConstraint);\n    this.pointwiseInitializer = getInitializer(config.depthwiseInitializer || this.DEFAULT_POINTWISE_INITIALIZER);\n    this.pointwiseRegularizer = getRegularizer(config.pointwiseRegularizer);\n    this.pointwiseConstraint = getConstraint(config.pointwiseConstraint);\n  }\n  build(inputShape) {\n    inputShape = getExactlyOneShape(inputShape);\n    if (inputShape.length < this.rank + 2) {\n      throw new ValueError(\"Inputs to SeparableConv\".concat(this.rank, \"D should have rank \") + \"\".concat(this.rank + 2, \", but received input shape: \") + \"\".concat(JSON.stringify(inputShape)));\n    }\n    const channelAxis = this.dataFormat === 'channelsFirst' ? 1 : inputShape.length - 1;\n    if (inputShape[channelAxis] == null || inputShape[channelAxis] < 0) {\n      throw new ValueError(\"The channel dimension of the inputs should be defined, \" + \"but found \".concat(JSON.stringify(inputShape[channelAxis])));\n    }\n    const inputDim = inputShape[channelAxis];\n    const depthwiseKernelShape = this.kernelSize.concat([inputDim, this.depthMultiplier]);\n    const pointwiseKernelShape = [];\n    for (let i = 0; i < this.rank; ++i) {\n      pointwiseKernelShape.push(1);\n    }\n    pointwiseKernelShape.push(inputDim * this.depthMultiplier, this.filters);\n    const trainable = true;\n    this.depthwiseKernel = this.addWeight('depthwise_kernel', depthwiseKernelShape, 'float32', this.depthwiseInitializer, this.depthwiseRegularizer, trainable, this.depthwiseConstraint);\n    this.pointwiseKernel = this.addWeight('pointwise_kernel', pointwiseKernelShape, 'float32', this.pointwiseInitializer, this.pointwiseRegularizer, trainable, this.pointwiseConstraint);\n    if (this.useBias) {\n      this.bias = this.addWeight('bias', [this.filters], 'float32', this.biasInitializer, this.biasRegularizer, trainable, this.biasConstraint);\n    } else {\n      this.bias = null;\n    }\n    this.inputSpec = [new InputSpec({\n      ndim: this.rank + 2,\n      axes: {\n        [channelAxis]: inputDim\n      }\n    })];\n    this.built = true;\n  }\n  call(inputs, kwargs) {\n    return tidy(() => {\n      inputs = getExactlyOneTensor(inputs);\n      let output;\n      if (this.rank === 1) {\n        throw new NotImplementedError('1D separable convolution is not implemented yet.');\n      } else if (this.rank === 2) {\n        if (this.dataFormat === 'channelsFirst') {\n          inputs = tfc.transpose(inputs, [0, 2, 3, 1]); // NCHW -> NHWC.\n        }\n        output = tfc.separableConv2d(inputs, this.depthwiseKernel.read(), this.pointwiseKernel.read(), this.strides, this.padding, this.dilationRate, 'NHWC');\n      }\n      if (this.useBias) {\n        output = K.biasAdd(output, this.bias.read(), this.dataFormat);\n      }\n      if (this.activation != null) {\n        output = this.activation.apply(output);\n      }\n      if (this.dataFormat === 'channelsFirst') {\n        output = tfc.transpose(output, [0, 3, 1, 2]); // NHWC -> NCHW.\n      }\n      return output;\n    });\n  }\n  getConfig() {\n    const config = super.getConfig();\n    delete config['rank'];\n    delete config['kernelInitializer'];\n    delete config['kernelRegularizer'];\n    delete config['kernelConstraint'];\n    config['depthwiseInitializer'] = serializeInitializer(this.depthwiseInitializer);\n    config['pointwiseInitializer'] = serializeInitializer(this.pointwiseInitializer);\n    config['depthwiseRegularizer'] = serializeRegularizer(this.depthwiseRegularizer);\n    config['pointwiseRegularizer'] = serializeRegularizer(this.pointwiseRegularizer);\n    config['depthwiseConstraint'] = serializeConstraint(this.depthwiseConstraint);\n    config['pointwiseConstraint'] = serializeConstraint(this.pointwiseConstraint);\n    return config;\n  }\n}\n/** @nocollapse */\nSeparableConv.className = 'SeparableConv';\nexport { SeparableConv };\nclass SeparableConv2D extends SeparableConv {\n  constructor(args) {\n    super(2, args);\n  }\n}\n/** @nocollapse */\nSeparableConv2D.className = 'SeparableConv2D';\nexport { SeparableConv2D };\nserialization.registerClass(SeparableConv2D);\nclass Conv1D extends Conv {\n  constructor(args) {\n    super(1, args);\n    Conv1D.verifyArgs(args);\n    this.inputSpec = [{\n      ndim: 3\n    }];\n  }\n  getConfig() {\n    const config = super.getConfig();\n    delete config['rank'];\n    delete config['dataFormat'];\n    return config;\n  }\n  static verifyArgs(args) {\n    // config.kernelSize must be a number or array of numbers.\n    if (typeof args.kernelSize !== 'number' && !generic_utils.checkArrayTypeAndLength(args.kernelSize, 'number', 1, 1)) {\n      throw new ValueError(\"Conv1D expects config.kernelSize to be number or number[] with \" + \"length 1, but received \".concat(JSON.stringify(args.kernelSize), \".\"));\n    }\n  }\n}\n/** @nocollapse */\nConv1D.className = 'Conv1D';\nexport { Conv1D };\nserialization.registerClass(Conv1D);\nclass Cropping2D extends Layer {\n  constructor(args) {\n    super(args);\n    if (typeof args.cropping === 'number') {\n      this.cropping = [[args.cropping, args.cropping], [args.cropping, args.cropping]];\n    } else if (typeof args.cropping[0] === 'number') {\n      this.cropping = [[args.cropping[0], args.cropping[0]], [args.cropping[1], args.cropping[1]]];\n    } else {\n      this.cropping = args.cropping;\n    }\n    this.dataFormat = args.dataFormat === undefined ? 'channelsLast' : args.dataFormat;\n    this.inputSpec = [{\n      ndim: 4\n    }];\n  }\n  computeOutputShape(inputShape) {\n    if (this.dataFormat === 'channelsFirst') {\n      return [inputShape[0], inputShape[1], inputShape[2] - this.cropping[0][0] - this.cropping[0][1], inputShape[3] - this.cropping[1][0] - this.cropping[1][1]];\n    } else {\n      return [inputShape[0], inputShape[1] - this.cropping[0][0] - this.cropping[0][1], inputShape[2] - this.cropping[1][0] - this.cropping[1][1], inputShape[3]];\n    }\n  }\n  call(inputs, kwargs) {\n    return tidy(() => {\n      inputs = getExactlyOneTensor(inputs);\n      if (this.dataFormat === 'channelsLast') {\n        const hSliced = K.sliceAlongAxis(inputs, this.cropping[0][0], inputs.shape[1] - this.cropping[0][0] - this.cropping[0][1], 2);\n        return K.sliceAlongAxis(hSliced, this.cropping[1][0], inputs.shape[2] - this.cropping[1][1] - this.cropping[1][0], 3);\n      } else {\n        const hSliced = K.sliceAlongAxis(inputs, this.cropping[0][0], inputs.shape[2] - this.cropping[0][0] - this.cropping[0][1], 3);\n        return K.sliceAlongAxis(hSliced, this.cropping[1][0], inputs.shape[3] - this.cropping[1][1] - this.cropping[1][0], 4);\n      }\n    });\n  }\n  getConfig() {\n    const config = {\n      cropping: this.cropping,\n      dataFormat: this.dataFormat\n    };\n    const baseConfig = super.getConfig();\n    Object.assign(config, baseConfig);\n    return config;\n  }\n}\n/** @nocollapse */\nCropping2D.className = 'Cropping2D';\nexport { Cropping2D };\nserialization.registerClass(Cropping2D);\nclass UpSampling2D extends Layer {\n  constructor(args) {\n    super(args);\n    this.DEFAULT_SIZE = [2, 2];\n    this.inputSpec = [{\n      ndim: 4\n    }];\n    this.size = args.size == null ? this.DEFAULT_SIZE : args.size;\n    this.dataFormat = args.dataFormat == null ? 'channelsLast' : args.dataFormat;\n    checkDataFormat(this.dataFormat);\n    this.interpolation = args.interpolation == null ? 'nearest' : args.interpolation;\n    checkInterpolationFormat(this.interpolation);\n  }\n  computeOutputShape(inputShape) {\n    if (this.dataFormat === 'channelsFirst') {\n      const height = inputShape[2] == null ? null : this.size[0] * inputShape[2];\n      const width = inputShape[3] == null ? null : this.size[1] * inputShape[3];\n      return [inputShape[0], inputShape[1], height, width];\n    } else {\n      const height = inputShape[1] == null ? null : this.size[0] * inputShape[1];\n      const width = inputShape[2] == null ? null : this.size[1] * inputShape[2];\n      return [inputShape[0], height, width, inputShape[3]];\n    }\n  }\n  call(inputs, kwargs) {\n    return tfc.tidy(() => {\n      let input = getExactlyOneTensor(inputs);\n      const inputShape = input.shape;\n      if (this.dataFormat === 'channelsFirst') {\n        input = tfc.transpose(input, [0, 2, 3, 1]);\n        const height = this.size[0] * inputShape[2];\n        const width = this.size[1] * inputShape[3];\n        const resized = this.interpolation === 'nearest' ? tfc.image.resizeNearestNeighbor(input, [height, width]) : tfc.image.resizeBilinear(input, [height, width]);\n        return tfc.transpose(resized, [0, 3, 1, 2]);\n      } else {\n        const height = this.size[0] * inputShape[1];\n        const width = this.size[1] * inputShape[2];\n        return this.interpolation === 'nearest' ? tfc.image.resizeNearestNeighbor(input, [height, width]) : tfc.image.resizeBilinear(input, [height, width]);\n      }\n    });\n  }\n  getConfig() {\n    const config = {\n      size: this.size,\n      dataFormat: this.dataFormat,\n      interpolation: this.interpolation\n    };\n    const baseConfig = super.getConfig();\n    Object.assign(config, baseConfig);\n    return config;\n  }\n}\n/** @nocollapse */\nUpSampling2D.className = 'UpSampling2D';\nexport { UpSampling2D };\nserialization.registerClass(UpSampling2D);", "map": {"version": 3, "names": ["tfc", "serialization", "tidy", "getActivation", "serializeActivation", "imageDataFormat", "K", "checkDataFormat", "checkInterpolationFormat", "checkPaddingMode", "getConstraint", "serializeConstraint", "InputSpec", "Layer", "NotImplementedError", "ValueError", "getInitializer", "serializeInitializer", "getRegularizer", "serializeRegularizer", "convOutputLength", "deconvLength", "normalizeArray", "generic_utils", "getExactlyOneShape", "getExactlyOneTensor", "preprocessConv2DInput", "x", "dataFormat", "transpose", "preprocessConv3DInput", "conv1dWithBias", "kernel", "bias", "strides", "arguments", "length", "undefined", "padding", "dilationRate", "shape", "concat", "y", "conv1d", "biasAdd", "conv2d", "conv2dWithBiasActivation", "activation", "rank", "fused", "filter", "pad", "dilations", "conv3d", "conv3dWithBias", "BaseConv", "constructor", "args", "DEFAULT_KERNEL_INITIALIZER", "DEFAULT_BIAS_INITIALIZER", "verify<PERSON>rgs", "assertPositiveInteger", "kernelSize", "useBias", "biasInitializer", "biasConstraint", "biasRegularizer", "activityRegularizer", "Array", "isArray", "JSON", "stringify", "assert", "checkArrayTypeAndLength", "getConfig", "config", "baseConfig", "Object", "assign", "Conv", "filters", "kernelInitializer", "kernelConstraint", "kernelRegularizer", "build", "inputShape", "channelAxis", "inputDim", "kernelShape", "addWeight", "inputSpec", "ndim", "axes", "built", "call", "inputs", "kwargs", "outputs", "biasValue", "read", "fusedActivationName", "mapActivationToFusedKernel", "getClassName", "apply", "computeOutputShape", "newSpace", "space", "slice", "i", "new<PERSON><PERSON>", "push", "outputShape", "Conv2D", "className", "registerClass", "Conv3D", "Conv2DTranspose", "input", "batchSize", "hAxis", "wAxis", "height", "width", "kernelH", "kernelW", "strideH", "strideW", "outHeight", "outWidth", "conv2dTranspose", "heightAxis", "widthAxis", "Conv3DTranspose", "dAxis", "depth", "kernelD", "strideD", "outDepth", "conv3dTranspose", "depthAxis", "SeparableConv", "DEFAULT_DEPTHWISE_INITIALIZER", "DEFAULT_POINTWISE_INITIALIZER", "depthwiseKernel", "pointwise<PERSON><PERSON><PERSON>", "depthMultiplier", "depthwiseInitializer", "depthwiseRegularizer", "depthwiseConstraint", "pointwiseInitializer", "pointwiseRegularizer", "pointwiseConstraint", "depthwiseKernelShape", "pointwiseKernelShape", "trainable", "output", "separableConv2d", "SeparableConv2D", "Conv1D", "Cropping2D", "cropping", "hSliced", "sliceAlongAxis", "UpSampling2D", "DEFAULT_SIZE", "size", "interpolation", "resized", "image", "resizeNearestNeighbor", "resizeBilinear"], "sources": ["C:\\tfjs-layers\\src\\layers\\convolutional.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2018 Google LLC\n *\n * Use of this source code is governed by an MIT-style\n * license that can be found in the LICENSE file or at\n * https://opensource.org/licenses/MIT.\n * =============================================================================\n */\n\n/**\n * TensorFlow.js Layers: Convolutional Layers\n */\n\nimport * as tfc from '@tensorflow/tfjs-core';\nimport {fused, serialization, Tensor, Tensor1D, Tensor2D, Tensor3D, Tensor4D, Tensor5D, tidy} from '@tensorflow/tfjs-core';\n\nimport {Activation, getActivation, serializeActivation} from '../activations';\nimport {imageDataFormat} from '../backend/common';\nimport * as K from '../backend/tfjs_backend';\nimport {checkDataFormat, checkInterpolationFormat, checkPaddingMode} from '../common';\nimport {Constraint, ConstraintIdentifier, getConstraint, serializeConstraint} from '../constraints';\nimport {InputSpec, Layer, LayerArgs} from '../engine/topology';\nimport {NotImplementedError, ValueError} from '../errors';\nimport {getInitializer, Initializer, InitializerIdentifier, serializeInitializer} from '../initializers';\nimport {ActivationIdentifier} from '../keras_format/activation_config';\nimport {DataFormat, InterpolationFormat, PaddingMode, Shape} from '../keras_format/common';\nimport {getRegularizer, Regularizer, RegularizerIdentifier, serializeRegularizer} from '../regularizers';\nimport {Kwargs} from '../types';\nimport {convOutputLength, deconvLength, normalizeArray} from '../utils/conv_utils';\nimport * as generic_utils from '../utils/generic_utils';\nimport {getExactlyOneShape, getExactlyOneTensor} from '../utils/types_utils';\nimport {LayerVariable} from '../variables';\n\n/**\n * Transpose and cast the input before the conv2d.\n * @param x Input image tensor.\n * @param dataFormat\n */\nexport function preprocessConv2DInput(\n    x: Tensor, dataFormat: DataFormat): Tensor {\n  // TODO(cais): Cast type to float32 if not.\n  return tidy(() => {\n    checkDataFormat(dataFormat);\n    if (dataFormat === 'channelsFirst') {\n      return tfc.transpose(x, [0, 2, 3, 1]);  // NCHW -> NHWC.\n    } else {\n      return x;\n    }\n  });\n}\n\n/**\n * Transpose and cast the input before the conv3d.\n * @param x Input image tensor.\n * @param dataFormat\n */\nexport function preprocessConv3DInput(\n    x: Tensor, dataFormat: DataFormat): Tensor {\n  return tidy(() => {\n    checkDataFormat(dataFormat);\n    if (dataFormat === 'channelsFirst') {\n      return tfc.transpose(x, [0, 2, 3, 4, 1]);  // NCDHW -> NDHWC.\n    } else {\n      return x;\n    }\n  });\n}\n\n/**\n * 1D-convolution with bias added.\n *\n * Porting Note: This function does not exist in the Python Keras backend.\n *   It is exactly the same as `conv2d`, except the added `bias`.\n *\n * @param x Input tensor, rank-3, of shape `[batchSize, width, inChannels]`.\n * @param kernel Kernel, rank-3, of shape `[filterWidth, inDepth, outDepth]`.\n * @param bias Bias, rank-3, of shape `[outDepth]`.\n * @param strides\n * @param padding Padding mode.\n * @param dataFormat Data format.\n * @param dilationRate\n * @returns The result of the 1D convolution.\n * @throws ValueError, if `x`, `kernel` or `bias` is not of the correct rank.\n */\nexport function conv1dWithBias(\n    x: Tensor, kernel: Tensor, bias: Tensor, strides = 1, padding = 'valid',\n    dataFormat?: DataFormat, dilationRate = 1): Tensor {\n  return tidy(() => {\n    if (dataFormat == null) {\n      dataFormat = imageDataFormat();\n    }\n    checkDataFormat(dataFormat);\n    // Check the ranks of x, kernel and bias.\n    if (x.shape.length !== 3) {\n      throw new ValueError(\n          `The input of a conv1dWithBias operation should be 3, but is ` +\n          `${x.shape.length} instead.`);\n    }\n    if (kernel.shape.length !== 3) {\n      throw new ValueError(\n          `The kernel for a conv1dWithBias operation should be 3, but is ` +\n          `${kernel.shape.length} instead`);\n    }\n    if (bias != null && bias.shape.length !== 1) {\n      throw new ValueError(\n          `The bias for a conv1dWithBias operation should be 1, but is ` +\n          `${bias.shape.length} instead`);\n    }\n    // TODO(cais): Support CAUSAL padding mode.\n    if (dataFormat === 'channelsFirst') {\n      x = tfc.transpose(x, [0, 2, 1]);  // NCW -> NWC.\n    }\n    if (padding === 'causal') {\n      throw new NotImplementedError(\n          'The support for CAUSAL padding mode in conv1dWithBias is not ' +\n          'implemented yet.');\n    }\n    let y: Tensor = tfc.conv1d(\n        x as Tensor2D | Tensor3D, kernel as Tensor3D, strides,\n        padding === 'same' ? 'same' : 'valid', 'NWC', dilationRate);\n    if (bias != null) {\n      y = K.biasAdd(y, bias);\n    }\n    return y;\n  });\n}\n\n/**\n * 1D-convolution.\n *\n * @param x Input tensor, rank-3, of shape `[batchSize, width, inChannels]`.\n * @param kernel Kernel, rank-3, of shape `[filterWidth, inDepth, outDepth]`.s\n * @param strides\n * @param padding Padding mode.\n * @param dataFormat Data format.\n * @param dilationRate\n * @returns The result of the 1D convolution.\n * @throws ValueError, if `x`, `kernel` or `bias` is not of the correct rank.\n */\nexport function conv1d(\n    x: Tensor, kernel: Tensor, strides = 1, padding = 'valid',\n    dataFormat?: DataFormat, dilationRate = 1): Tensor {\n  return tidy(() => {\n    checkDataFormat(dataFormat);\n    return conv1dWithBias(\n        x, kernel, null, strides, padding, dataFormat, dilationRate);\n  });\n}\n\n/**\n * 2D Convolution\n * @param x\n * @param kernel kernel of the convolution.\n * @param strides strides array.\n * @param padding padding mode. Default to 'valid'.\n * @param dataFormat data format. Defaults to 'channelsLast'.\n * @param dilationRate dilation rate array.\n * @returns Result of the 2D pooling.\n */\nexport function conv2d(\n    x: Tensor, kernel: Tensor, strides = [1, 1], padding = 'valid',\n    dataFormat?: DataFormat, dilationRate?: [number, number]): Tensor {\n  return tidy(() => {\n    checkDataFormat(dataFormat);\n    return conv2dWithBiasActivation(\n        x, kernel, null, strides, padding, dataFormat, dilationRate);\n  });\n}\n\n/**\n * 2D Convolution with an added bias and optional activation.\n * Note: This function does not exist in the Python Keras Backend. This function\n * is exactly the same as `conv2d`, except the added `bias`.\n */\nexport function conv2dWithBiasActivation(\n    x: Tensor, kernel: Tensor, bias: Tensor, strides = [1, 1],\n    padding = 'valid', dataFormat?: DataFormat, dilationRate?: [number, number],\n    activation: fused.Activation = null): Tensor {\n  return tidy(() => {\n    if (dataFormat == null) {\n      dataFormat = imageDataFormat();\n    }\n    checkDataFormat(dataFormat);\n    if (x.rank !== 3 && x.rank !== 4) {\n      throw new ValueError(\n          `conv2dWithBiasActivation expects input to be of rank 3 or 4, ` +\n          `but received ${x.rank}.`);\n    }\n    if (kernel.rank !== 3 && kernel.rank !== 4) {\n      throw new ValueError(\n          `conv2dWithBiasActivation expects kernel to be of rank 3 or 4, ` +\n          `but received ${x.rank}.`);\n    }\n    let y = preprocessConv2DInput(x, dataFormat);\n    if (padding === 'causal') {\n      throw new NotImplementedError(\n          'The support for CAUSAL padding mode in conv1dWithBias is not ' +\n          'implemented yet.');\n    }\n    y = tfc.fused.conv2d({\n      x: y as Tensor3D | Tensor4D,\n      filter: kernel as Tensor4D,\n      strides: strides as [number, number],\n      pad: padding === 'same' ? 'same' : 'valid',\n      dilations: dilationRate,\n      dataFormat: 'NHWC',\n      bias,\n      activation\n    });\n    if (dataFormat === 'channelsFirst') {\n      y = tfc.transpose(y, [0, 3, 1, 2]);\n    }\n    return y;\n  });\n}\n\n/**\n * 3D Convolution.\n * @param x\n * @param kernel kernel of the convolution.\n * @param strides strides array.\n * @param padding padding mode. Default to 'valid'.\n * @param dataFormat data format. Defaults to 'channelsLast'.\n * @param dilationRate dilation rate array.\n * @returns Result of the 3D convolution.\n */\nexport function conv3d(\n    x: Tensor, kernel: Tensor, strides = [1, 1, 1], padding = 'valid',\n    dataFormat?: DataFormat, dilationRate?: [number, number, number]): Tensor {\n  return tidy(() => {\n    checkDataFormat(dataFormat);\n    return conv3dWithBias(\n        x, kernel, null, strides, padding, dataFormat, dilationRate);\n  });\n}\n\n/**\n * 3D Convolution with an added bias.\n * Note: This function does not exist in the Python Keras Backend. This function\n * is exactly the same as `conv3d`, except the added `bias`.\n */\nexport function conv3dWithBias(\n    x: Tensor, kernel: Tensor, bias: Tensor, strides = [1, 1, 1],\n    padding = 'valid', dataFormat?: DataFormat,\n    dilationRate?: [number, number, number]): Tensor {\n  return tidy(() => {\n    if (dataFormat == null) {\n      dataFormat = imageDataFormat();\n    }\n    checkDataFormat(dataFormat);\n    if (x.rank !== 4 && x.rank !== 5) {\n      throw new ValueError(\n          `conv3dWithBias expects input to be of rank 4 or 5, but received ` +\n          `${x.rank}.`);\n    }\n    if (kernel.rank !== 4 && kernel.rank !== 5) {\n      throw new ValueError(\n          `conv3dWithBias expects kernel to be of rank 4 or 5, but received ` +\n          `${x.rank}.`);\n    }\n    let y = preprocessConv3DInput(x, dataFormat);\n    if (padding === 'causal') {\n      throw new NotImplementedError(\n          'The support for CAUSAL padding mode in conv3dWithBias is not ' +\n          'implemented yet.');\n    }\n    y = tfc.conv3d(\n        y as Tensor4D | tfc.Tensor<tfc.Rank.R5>,\n        kernel as tfc.Tensor<tfc.Rank.R5>, strides as [number, number, number],\n        padding === 'same' ? 'same' : 'valid', 'NDHWC', dilationRate);\n    if (bias != null) {\n      y = K.biasAdd(y, bias as Tensor1D);\n    }\n    if (dataFormat === 'channelsFirst') {\n      y = tfc.transpose(y, [0, 4, 1, 2, 3]);\n    }\n    return y;\n  });\n}\n\n/**\n * Base LayerConfig for depthwise and non-depthwise convolutional layers.\n */\nexport declare interface BaseConvLayerArgs extends LayerArgs {\n  /**\n   * The dimensions of the convolution window. If kernelSize is a number, the\n   * convolutional window will be square.\n   */\n  kernelSize: number|number[];\n\n  /**\n   * The strides of the convolution in each dimension. If strides is a number,\n   * strides in both dimensions are equal.\n   *\n   * Specifying any stride value != 1 is incompatible with specifying any\n   * `dilationRate` value != 1.\n   */\n  strides?: number|number[];\n\n  /**\n   * Padding mode.\n   */\n  padding?: PaddingMode;\n\n  /**\n   * Format of the data, which determines the ordering of the dimensions in\n   * the inputs.\n   *\n   * `channels_last` corresponds to inputs with shape\n   *   `(batch, ..., channels)`\n   *\n   *  `channels_first` corresponds to inputs with shape `(batch, channels,\n   * ...)`.\n   *\n   * Defaults to `channels_last`.\n   */\n  dataFormat?: DataFormat;\n\n  /**\n   * The dilation rate to use for the dilated convolution in each dimension.\n   * Should be an integer or array of two or three integers.\n   *\n   * Currently, specifying any `dilationRate` value != 1 is incompatible with\n   * specifying any `strides` value != 1.\n   */\n  dilationRate?: number|[number]|[number, number]|[number, number, number];\n\n  /**\n   * Activation function of the layer.\n   *\n   * If you don't specify the activation, none is applied.\n   */\n  activation?: ActivationIdentifier;\n\n  /**\n   * Whether the layer uses a bias vector. Defaults to `true`.\n   */\n  useBias?: boolean;\n\n  /**\n   * Initializer for the convolutional kernel weights matrix.\n   */\n  kernelInitializer?: InitializerIdentifier|Initializer;\n\n  /**\n   * Initializer for the bias vector.\n   */\n  biasInitializer?: InitializerIdentifier|Initializer;\n\n  /**\n   * Constraint for the convolutional kernel weights.\n   */\n  kernelConstraint?: ConstraintIdentifier|Constraint;\n\n  /**\n   * Constraint for the bias vector.\n   */\n  biasConstraint?: ConstraintIdentifier|Constraint;\n\n  /**\n   * Regularizer function applied to the kernel weights matrix.\n   */\n  kernelRegularizer?: RegularizerIdentifier|Regularizer;\n\n  /**\n   * Regularizer function applied to the bias vector.\n   */\n  biasRegularizer?: RegularizerIdentifier|Regularizer;\n\n  /**\n   * Regularizer function applied to the activation.\n   */\n  activityRegularizer?: RegularizerIdentifier|Regularizer;\n}\n\n/**\n * LayerConfig for non-depthwise convolutional layers.\n * Applies to non-depthwise convolution of all ranks (e.g, Conv1D, Conv2D,\n * Conv3D).\n */\nexport declare interface ConvLayerArgs extends BaseConvLayerArgs {\n  /**\n   * The dimensionality of the output space (i.e. the number of filters in the\n   * convolution).\n   */\n  filters: number;\n}\n\n/**\n * Abstract convolution layer.\n */\nexport abstract class BaseConv extends Layer {\n  protected readonly rank: number;\n  protected readonly kernelSize: number[];\n  protected readonly strides: number[];\n  protected readonly padding: PaddingMode;\n  protected readonly dataFormat: DataFormat;\n  protected readonly activation: Activation;\n  protected readonly useBias: boolean;\n  protected readonly dilationRate: number[];\n\n  // Bias-related members are here because all convolution subclasses use the\n  // same configuration parmeters to control bias.  Kernel-related members\n  // are in subclass `Conv` because some subclasses use different parameters to\n  // control kernel properties, for instance, `DepthwiseConv2D` uses\n  // `depthwiseInitializer` instead of `kernelInitializer`.\n  protected readonly biasInitializer?: Initializer;\n  protected readonly biasConstraint?: Constraint;\n  protected readonly biasRegularizer?: Regularizer;\n\n  protected bias: LayerVariable = null;\n\n  readonly DEFAULT_KERNEL_INITIALIZER: InitializerIdentifier = 'glorotNormal';\n  readonly DEFAULT_BIAS_INITIALIZER: InitializerIdentifier = 'zeros';\n\n  constructor(rank: number, args: BaseConvLayerArgs) {\n    super(args as LayerArgs);\n    BaseConv.verifyArgs(args);\n    this.rank = rank;\n    generic_utils.assertPositiveInteger(this.rank, 'rank');\n    if (this.rank !== 1 && this.rank !== 2 && this.rank !== 3) {\n      throw new NotImplementedError(\n          `Convolution layer for rank other than 1, 2, or 3 (${\n              this.rank}) is ` +\n          `not implemented yet.`);\n    }\n    this.kernelSize = normalizeArray(args.kernelSize, rank, 'kernelSize');\n    this.strides = normalizeArray(\n        args.strides == null ? 1 : args.strides, rank, 'strides');\n    this.padding = args.padding == null ? 'valid' : args.padding;\n    checkPaddingMode(this.padding);\n    this.dataFormat =\n        args.dataFormat == null ? 'channelsLast' : args.dataFormat;\n    checkDataFormat(this.dataFormat);\n    this.activation = getActivation(args.activation);\n    this.useBias = args.useBias == null ? true : args.useBias;\n    this.biasInitializer =\n        getInitializer(args.biasInitializer || this.DEFAULT_BIAS_INITIALIZER);\n    this.biasConstraint = getConstraint(args.biasConstraint);\n    this.biasRegularizer = getRegularizer(args.biasRegularizer);\n    this.activityRegularizer = getRegularizer(args.activityRegularizer);\n    this.dilationRate = normalizeArray(\n        args.dilationRate == null ? 1 : args.dilationRate, rank,\n        'dilationRate');\n    if (this.rank === 1 &&\n        (Array.isArray(this.dilationRate) && this.dilationRate.length !== 1)) {\n      throw new ValueError(\n          `dilationRate must be a number or an array of a single number ` +\n          `for 1D convolution, but received ` +\n          `${JSON.stringify(this.dilationRate)}`);\n    } else if (this.rank === 2) {\n      if (typeof this.dilationRate === 'number') {\n        this.dilationRate = [this.dilationRate, this.dilationRate];\n      } else if (this.dilationRate.length !== 2) {\n        throw new ValueError(\n            `dilationRate must be a number or array of two numbers for 2D ` +\n            `convolution, but received ${JSON.stringify(this.dilationRate)}`);\n      }\n    } else if (this.rank === 3) {\n      if (typeof this.dilationRate === 'number') {\n        this.dilationRate =\n            [this.dilationRate, this.dilationRate, this.dilationRate];\n      } else if (this.dilationRate.length !== 3) {\n        throw new ValueError(\n            `dilationRate must be a number or array of three numbers for 3D ` +\n            `convolution, but received ${JSON.stringify(this.dilationRate)}`);\n      }\n    }\n  }\n\n  protected static verifyArgs(args: BaseConvLayerArgs) {\n    // Check config.kernelSize type and shape.\n    generic_utils.assert(\n        'kernelSize' in args, `required key 'kernelSize' not in config`);\n    if (typeof args.kernelSize !== 'number' &&\n        !generic_utils.checkArrayTypeAndLength(\n            args.kernelSize, 'number', 1, 3)) {\n      throw new ValueError(\n          `BaseConv expects config.kernelSize to be number or number[] with ` +\n          `length 1, 2, or 3, but received ${\n              JSON.stringify(args.kernelSize)}.`);\n    }\n  }\n\n  override getConfig(): serialization.ConfigDict {\n    const config: serialization.ConfigDict = {\n      kernelSize: this.kernelSize,\n      strides: this.strides,\n      padding: this.padding,\n      dataFormat: this.dataFormat,\n      dilationRate: this.dilationRate,\n      activation: serializeActivation(this.activation),\n      useBias: this.useBias,\n      biasInitializer: serializeInitializer(this.biasInitializer),\n      biasRegularizer: serializeRegularizer(this.biasRegularizer),\n      activityRegularizer: serializeRegularizer(this.activityRegularizer),\n      biasConstraint: serializeConstraint(this.biasConstraint)\n    };\n    const baseConfig = super.getConfig();\n    Object.assign(config, baseConfig);\n    return config;\n  }\n}\n\n/**\n * Abstract nD convolution layer.  Ancestor of convolution layers which reduce\n * across channels, i.e., Conv1D and Conv2D, but not DepthwiseConv2D.\n */\nexport abstract class Conv extends BaseConv {\n  protected readonly filters: number;\n\n  protected kernel: LayerVariable = null;\n\n  // Bias-related properties are stored in the superclass `BaseConv` because all\n  // convolution subclasses use the same configuration parameters to control\n  // bias. Kernel-related properties are defined here rather than in the\n  // superclass because some convolution subclasses use different names and\n  // configuration parameters for their internal kernel state.\n  protected readonly kernelInitializer?: Initializer;\n  protected readonly kernelConstraint?: Constraint;\n  protected readonly kernelRegularizer?: Regularizer;\n\n  constructor(rank: number, args: ConvLayerArgs) {\n    super(rank, args as BaseConvLayerArgs);\n    Conv.verifyArgs(args);\n    this.filters = args.filters;\n    generic_utils.assertPositiveInteger(this.filters, 'filters');\n    this.kernelInitializer = getInitializer(\n        args.kernelInitializer || this.DEFAULT_KERNEL_INITIALIZER);\n    this.kernelConstraint = getConstraint(args.kernelConstraint);\n    this.kernelRegularizer = getRegularizer(args.kernelRegularizer);\n  }\n\n  override build(inputShape: Shape|Shape[]): void {\n    inputShape = getExactlyOneShape(inputShape);\n    const channelAxis =\n        this.dataFormat === 'channelsFirst' ? 1 : inputShape.length - 1;\n    if (inputShape[channelAxis] == null) {\n      throw new ValueError(\n          `The channel dimension of the input should be defined. ` +\n          `Found ${inputShape[channelAxis]}`);\n    }\n    const inputDim = inputShape[channelAxis];\n\n    const kernelShape = this.kernelSize.concat([inputDim, this.filters]);\n\n    this.kernel = this.addWeight(\n        'kernel', kernelShape, null, this.kernelInitializer,\n        this.kernelRegularizer, true, this.kernelConstraint);\n    if (this.useBias) {\n      this.bias = this.addWeight(\n          'bias', [this.filters], null, this.biasInitializer,\n          this.biasRegularizer, true, this.biasConstraint);\n    }\n\n    this.inputSpec = [{ndim: this.rank + 2, axes: {[channelAxis]: inputDim}}];\n    this.built = true;\n  }\n\n  override call(inputs: Tensor|Tensor[], kwargs: Kwargs): Tensor|Tensor[] {\n    return tidy(() => {\n      inputs = getExactlyOneTensor(inputs);\n      let outputs: Tensor;\n      const biasValue = this.bias == null ? null : this.bias.read();\n      const fusedActivationName = generic_utils.mapActivationToFusedKernel(\n          this.activation.getClassName());\n\n      if (fusedActivationName != null && this.rank === 2) {\n        outputs = conv2dWithBiasActivation(\n            inputs, this.kernel.read(), biasValue, this.strides, this.padding,\n            this.dataFormat, this.dilationRate as [number, number],\n            fusedActivationName);\n      } else {\n        if (this.rank === 1) {\n          outputs = conv1dWithBias(\n              inputs, this.kernel.read(), biasValue, this.strides[0],\n              this.padding, this.dataFormat, this.dilationRate[0]);\n        } else if (this.rank === 2) {\n          // TODO(cais): Move up to constructor.\n          outputs = conv2dWithBiasActivation(\n              inputs, this.kernel.read(), biasValue, this.strides, this.padding,\n              this.dataFormat, this.dilationRate as [number, number]);\n        } else if (this.rank === 3) {\n          outputs = conv3dWithBias(\n              inputs, this.kernel.read(), biasValue, this.strides, this.padding,\n              this.dataFormat, this.dilationRate as [number, number, number]);\n        } else {\n          throw new NotImplementedError(\n              'convolutions greater than 3D are not implemented yet.');\n        }\n\n        if (this.activation != null) {\n          outputs = this.activation.apply(outputs);\n        }\n      }\n\n      return outputs;\n    });\n  }\n\n  override computeOutputShape(inputShape: Shape|Shape[]): Shape|Shape[] {\n    inputShape = getExactlyOneShape(inputShape);\n    const newSpace: number[] = [];\n    const space = (this.dataFormat === 'channelsLast') ?\n        inputShape.slice(1, inputShape.length - 1) :\n        inputShape.slice(2);\n    for (let i = 0; i < space.length; ++i) {\n      const newDim = convOutputLength(\n          space[i], this.kernelSize[i], this.padding, this.strides[i],\n          typeof this.dilationRate === 'number' ? this.dilationRate :\n                                                  this.dilationRate[i]);\n      newSpace.push(newDim);\n    }\n\n    let outputShape = [inputShape[0]];\n    if (this.dataFormat === 'channelsLast') {\n      outputShape = outputShape.concat(newSpace);\n      outputShape.push(this.filters);\n    } else {\n      outputShape.push(this.filters);\n      outputShape = outputShape.concat(newSpace);\n    }\n    return outputShape;\n  }\n\n  override getConfig(): serialization.ConfigDict {\n    const config = {\n      filters: this.filters,\n      kernelInitializer: serializeInitializer(this.kernelInitializer),\n      kernelRegularizer: serializeRegularizer(this.kernelRegularizer),\n      kernelConstraint: serializeConstraint(this.kernelConstraint)\n    };\n    const baseConfig = super.getConfig();\n    Object.assign(config, baseConfig);\n    return config;\n  }\n\n  protected static override verifyArgs(args: ConvLayerArgs) {\n    // Check config.filters type, shape, and value.\n    if (!('filters' in args) || typeof args.filters !== 'number' ||\n        args.filters < 1) {\n      throw new ValueError(\n          `Convolution layer expected config.filters to be a 'number' > 0 ` +\n          `but got ${JSON.stringify(args.filters)}`);\n    }\n  }\n}\n\nexport class Conv2D extends Conv {\n  /** @nocollapse */\n  static className = 'Conv2D';\n  constructor(args: ConvLayerArgs) {\n    super(2, args);\n    Conv2D.verifyArgs(args);\n  }\n\n  override getConfig(): serialization.ConfigDict {\n    const config = super.getConfig();\n    delete config['rank'];\n    return config;\n  }\n\n  protected static override verifyArgs(args: ConvLayerArgs) {\n    // config.kernelSize must be a number or array of numbers.\n    if ((typeof args.kernelSize !== 'number') &&\n        !generic_utils.checkArrayTypeAndLength(\n            args.kernelSize, 'number', 1, 2)) {\n      throw new ValueError(\n          `Conv2D expects config.kernelSize to be number or number[] with ` +\n          `length 1 or 2, but received ${JSON.stringify(args.kernelSize)}.`);\n    }\n  }\n}\nserialization.registerClass(Conv2D);\n\nexport class Conv3D extends Conv {\n  /** @nocollapse */\n  static className = 'Conv3D';\n  constructor(args: ConvLayerArgs) {\n    super(3, args);\n    Conv3D.verifyArgs(args);\n  }\n\n  override getConfig(): serialization.ConfigDict {\n    const config = super.getConfig();\n    delete config['rank'];\n    return config;\n  }\n\n  protected static override verifyArgs(args: ConvLayerArgs) {\n    // config.kernelSize must be a number or array of numbers.\n    if (typeof args.kernelSize !== 'number') {\n      if (!(Array.isArray(args.kernelSize) &&\n            (args.kernelSize.length === 1 || args.kernelSize.length === 3))) {\n        throw new ValueError(\n            `Conv3D expects config.kernelSize to be number or` +\n            ` [number, number, number], but received ${\n                JSON.stringify(args.kernelSize)}.`);\n      }\n    }\n  }\n}\nserialization.registerClass(Conv3D);\n\nexport class Conv2DTranspose extends Conv2D {\n  /** @nocollapse */\n  static override className = 'Conv2DTranspose';\n\n  constructor(args: ConvLayerArgs) {\n    super(args);\n    this.inputSpec = [new InputSpec({ndim: 4})];\n\n    if (this.padding !== 'same' && this.padding !== 'valid') {\n      throw new ValueError(\n          `Conv2DTranspose currently supports only padding modes 'same' ` +\n          `and 'valid', but received padding mode ${this.padding}`);\n    }\n  }\n\n  override build(inputShape: Shape|Shape[]): void {\n    inputShape = getExactlyOneShape(inputShape);\n\n    if (inputShape.length !== 4) {\n      throw new ValueError(\n          'Input should have rank 4; Received input shape: ' +\n          JSON.stringify(inputShape));\n    }\n\n    const channelAxis =\n        this.dataFormat === 'channelsFirst' ? 1 : inputShape.length - 1;\n    if (inputShape[channelAxis] == null) {\n      throw new ValueError(\n          'The channel dimension of the inputs should be defined. ' +\n          'Found `None`.');\n    }\n    const inputDim = inputShape[channelAxis];\n    const kernelShape = this.kernelSize.concat([this.filters, inputDim]);\n\n    this.kernel = this.addWeight(\n        'kernel', kernelShape, 'float32', this.kernelInitializer,\n        this.kernelRegularizer, true, this.kernelConstraint);\n    if (this.useBias) {\n      this.bias = this.addWeight(\n          'bias', [this.filters], 'float32', this.biasInitializer,\n          this.biasRegularizer, true, this.biasConstraint);\n    }\n\n    // Set input spec.\n    this.inputSpec =\n        [new InputSpec({ndim: 4, axes: {[channelAxis]: inputDim}})];\n    this.built = true;\n  }\n\n  override call(inputs: Tensor|Tensor[], kwargs: Kwargs): Tensor|Tensor[] {\n    return tfc.tidy(() => {\n      let input = getExactlyOneTensor(inputs);\n      if (input.shape.length !== 4) {\n        throw new ValueError(\n            `Conv2DTranspose.call() expects input tensor to be rank-4, but ` +\n            `received a tensor of rank-${input.shape.length}`);\n      }\n\n      const inputShape = input.shape;\n      const batchSize = inputShape[0];\n\n      let hAxis: number;\n      let wAxis: number;\n      if (this.dataFormat === 'channelsFirst') {\n        hAxis = 2;\n        wAxis = 3;\n      } else {\n        hAxis = 1;\n        wAxis = 2;\n      }\n\n      const height = inputShape[hAxis];\n      const width = inputShape[wAxis];\n      const kernelH = this.kernelSize[0];\n      const kernelW = this.kernelSize[1];\n      const strideH = this.strides[0];\n      const strideW = this.strides[1];\n\n      // Infer the dynamic output shape.\n      const outHeight = deconvLength(height, strideH, kernelH, this.padding);\n      const outWidth = deconvLength(width, strideW, kernelW, this.padding);\n\n      // Porting Note: We don't branch based on `this.dataFormat` here,\n      // because\n      //   the tjfs-core function `conv2dTranspose` called below always\n      //   assumes channelsLast.\n      const outputShape: [number, number, number, number] =\n          [batchSize, outHeight, outWidth, this.filters];\n\n      if (this.dataFormat !== 'channelsLast') {\n        input = tfc.transpose(input, [0, 2, 3, 1]);\n      }\n      let outputs = tfc.conv2dTranspose(\n          input as Tensor4D, this.kernel.read() as Tensor4D, outputShape,\n          this.strides as [number, number], this.padding as 'same' | 'valid');\n      if (this.dataFormat !== 'channelsLast') {\n        outputs = tfc.transpose(outputs, [0, 3, 1, 2]);\n      }\n\n      if (this.bias != null) {\n        outputs =\n            K.biasAdd(outputs, this.bias.read(), this.dataFormat) as Tensor4D;\n      }\n      if (this.activation != null) {\n        outputs = this.activation.apply(outputs) as Tensor4D;\n      }\n      return outputs;\n    });\n  }\n\n  override computeOutputShape(inputShape: Shape|Shape[]): Shape|Shape[] {\n    inputShape = getExactlyOneShape(inputShape);\n    const outputShape = inputShape.slice();\n\n    let channelAxis: number;\n    let heightAxis: number;\n    let widthAxis: number;\n    if (this.dataFormat === 'channelsFirst') {\n      channelAxis = 1;\n      heightAxis = 2;\n      widthAxis = 3;\n    } else {\n      channelAxis = 3;\n      heightAxis = 1;\n      widthAxis = 2;\n    }\n\n    const kernelH = this.kernelSize[0];\n    const kernelW = this.kernelSize[1];\n    const strideH = this.strides[0];\n    const strideW = this.strides[1];\n\n    outputShape[channelAxis] = this.filters;\n    outputShape[heightAxis] =\n        deconvLength(outputShape[heightAxis], strideH, kernelH, this.padding);\n    outputShape[widthAxis] =\n        deconvLength(outputShape[widthAxis], strideW, kernelW, this.padding);\n    return outputShape;\n  }\n\n  override getConfig(): serialization.ConfigDict {\n    const config = super.getConfig();\n    delete config['dilationRate'];\n    return config;\n  }\n}\nserialization.registerClass(Conv2DTranspose);\n\nexport class Conv3DTranspose extends Conv3D {\n  /** @nocollapse */\n  static override className = 'Conv3DTranspose';\n\n  constructor(args: ConvLayerArgs) {\n    super(args);\n    this.inputSpec = [new InputSpec({ndim: 5})];\n\n    if (this.padding !== 'same' && this.padding !== 'valid') {\n      throw new ValueError(\n          `Conv3DTranspose currently supports only padding modes 'same' ` +\n          `and 'valid', but received padding mode ${this.padding}`);\n    }\n  }\n\n  override build(inputShape: Shape|Shape[]): void {\n    inputShape = getExactlyOneShape(inputShape);\n\n    if (inputShape.length !== 5) {\n      throw new ValueError(\n          'Input should have rank 5; Received input shape: ' +\n          JSON.stringify(inputShape));\n    }\n\n    const channelAxis =\n        this.dataFormat === 'channelsFirst' ? 1 : inputShape.length - 1;\n    if (inputShape[channelAxis] == null) {\n      throw new ValueError(\n          'The channel dimension of the inputs should be defined. ' +\n          'Found `None`.');\n    }\n    const inputDim = inputShape[channelAxis];\n    const kernelShape = this.kernelSize.concat([this.filters, inputDim]);\n\n    this.kernel = this.addWeight(\n        'kernel', kernelShape, 'float32', this.kernelInitializer,\n        this.kernelRegularizer, true, this.kernelConstraint);\n    if (this.useBias) {\n      this.bias = this.addWeight(\n          'bias', [this.filters], 'float32', this.biasInitializer,\n          this.biasRegularizer, true, this.biasConstraint);\n    }\n\n    // Set input spec.\n    this.inputSpec =\n        [new InputSpec({ndim: 5, axes: {[channelAxis]: inputDim}})];\n    this.built = true;\n  }\n\n  override call(inputs: Tensor|Tensor[], kwargs: Kwargs): Tensor|Tensor[] {\n    return tfc.tidy<tfc.Tensor5D>(() => {\n      let input = getExactlyOneTensor(inputs);\n      if (input.shape.length !== 5) {\n        throw new ValueError(\n            `Conv3DTranspose.call() expects input tensor to be rank-4, but ` +\n            `received a tensor of rank-${input.shape.length}`);\n      }\n\n      const inputShape = input.shape;\n      const batchSize = inputShape[0];\n\n      let hAxis: number;\n      let wAxis: number;\n      let dAxis: number;\n\n      if (this.dataFormat === 'channelsFirst') {\n        dAxis = 2;\n        hAxis = 3;\n        wAxis = 4;\n      } else {\n        dAxis = 1;\n        hAxis = 2;\n        wAxis = 3;\n      }\n\n      const depth = inputShape[dAxis];\n      const height = inputShape[hAxis];\n      const width = inputShape[wAxis];\n      const kernelD = this.kernelSize[0];\n      const kernelH = this.kernelSize[1];\n      const kernelW = this.kernelSize[2];\n      const strideD = this.strides[0];\n      const strideH = this.strides[1];\n      const strideW = this.strides[2];\n\n      // Infer the dynamic output shape.\n      const outDepth = deconvLength(depth, strideD, kernelD, this.padding);\n      const outHeight = deconvLength(height, strideH, kernelH, this.padding);\n      const outWidth = deconvLength(width, strideW, kernelW, this.padding);\n\n      // Same as `conv2dTranspose`. We always assumes channelsLast.\n      const outputShape: [number, number, number, number, number] =\n          [batchSize, outDepth, outHeight, outWidth, this.filters];\n      if (this.dataFormat !== 'channelsLast') {\n        input = tfc.transpose(input, [0, 2, 3, 4, 1]);\n      }\n      let outputs = tfc.conv3dTranspose(\n          input as Tensor5D, this.kernel.read() as Tensor5D, outputShape,\n          this.strides as [number, number, number],\n          this.padding as 'same' | 'valid');\n      if (this.dataFormat !== 'channelsLast') {\n        outputs = tfc.transpose(outputs, [0, 4, 1, 2, 3]);\n      }\n\n      if (this.bias !== null) {\n        outputs =\n            K.biasAdd(outputs, this.bias.read(), this.dataFormat) as Tensor5D;\n      }\n      if (this.activation !== null) {\n        outputs = this.activation.apply(outputs) as Tensor5D;\n      }\n      return outputs;\n    });\n  }\n\n  override computeOutputShape(inputShape: Shape|Shape[]): Shape|Shape[] {\n    inputShape = getExactlyOneShape(inputShape);\n    const outputShape = inputShape.slice();\n\n    let channelAxis: number;\n    let depthAxis: number;\n    let heightAxis: number;\n    let widthAxis: number;\n    if (this.dataFormat === 'channelsFirst') {\n      channelAxis = 1;\n      depthAxis = 2;\n      heightAxis = 3;\n      widthAxis = 4;\n    } else {\n      channelAxis = 4;\n      depthAxis = 1;\n      heightAxis = 2;\n      widthAxis = 3;\n    }\n\n    const kernelD = this.kernelSize[0];\n    const kernelH = this.kernelSize[1];\n    const kernelW = this.kernelSize[2];\n    const strideD = this.strides[0];\n    const strideH = this.strides[1];\n    const strideW = this.strides[2];\n\n    outputShape[channelAxis] = this.filters;\n    outputShape[depthAxis] =\n        deconvLength(outputShape[depthAxis], strideD, kernelD, this.padding);\n    outputShape[heightAxis] =\n        deconvLength(outputShape[heightAxis], strideH, kernelH, this.padding);\n    outputShape[widthAxis] =\n        deconvLength(outputShape[widthAxis], strideW, kernelW, this.padding);\n    return outputShape;\n  }\n\n  override getConfig(): serialization.ConfigDict {\n    const config = super.getConfig();\n    delete config['dilationRate'];\n    return config;\n  }\n}\nserialization.registerClass(Conv3DTranspose);\n\nexport declare interface SeparableConvLayerArgs extends ConvLayerArgs {\n  /**\n   * The number of depthwise convolution output channels for each input\n   * channel.\n   * The total number of depthwise convolution output channels will be equal\n   * to `filtersIn * depthMultiplier`. Default: 1.\n   */\n  depthMultiplier?: number;\n\n  /**\n   * Initializer for the depthwise kernel matrix.\n   */\n  depthwiseInitializer?: InitializerIdentifier|Initializer;\n\n  /**\n   * Initializer for the pointwise kernel matrix.\n   */\n  pointwiseInitializer?: InitializerIdentifier|Initializer;\n\n  /**\n   * Regularizer function applied to the depthwise kernel matrix.\n   */\n  depthwiseRegularizer?: RegularizerIdentifier|Regularizer;\n\n  /**\n   * Regularizer function applied to the pointwise kernel matrix.\n   */\n  pointwiseRegularizer?: RegularizerIdentifier|Regularizer;\n\n  /**\n   * Constraint function applied to the depthwise kernel matrix.\n   */\n  depthwiseConstraint?: ConstraintIdentifier|Constraint;\n\n  /**\n   * Constraint function applied to the pointwise kernel matrix.\n   */\n  pointwiseConstraint?: ConstraintIdentifier|Constraint;\n}\n\nexport class SeparableConv extends Conv {\n  /** @nocollapse */\n  static className = 'SeparableConv';\n\n  readonly depthMultiplier: number;\n\n  protected readonly depthwiseInitializer?: Initializer;\n  protected readonly depthwiseRegularizer?: Regularizer;\n  protected readonly depthwiseConstraint?: Constraint;\n  protected readonly pointwiseInitializer?: Initializer;\n  protected readonly pointwiseRegularizer?: Regularizer;\n  protected readonly pointwiseConstraint?: Constraint;\n\n  readonly DEFAULT_DEPTHWISE_INITIALIZER: InitializerIdentifier =\n      'glorotUniform';\n  readonly DEFAULT_POINTWISE_INITIALIZER: InitializerIdentifier =\n      'glorotUniform';\n\n  protected depthwiseKernel: LayerVariable = null;\n  protected pointwiseKernel: LayerVariable = null;\n\n  constructor(rank: number, config?: SeparableConvLayerArgs) {\n    super(rank, config);\n\n    if (config.filters == null) {\n      throw new ValueError(\n          'The `filters` configuration field is required by SeparableConv, ' +\n          'but is unspecified.');\n    }\n    if (config.kernelInitializer != null || config.kernelRegularizer != null ||\n        config.kernelConstraint != null) {\n      throw new ValueError(\n          'Fields kernelInitializer, kernelRegularizer and kernelConstraint ' +\n          'are invalid for SeparableConv2D. Use depthwiseInitializer, ' +\n          'depthwiseRegularizer, depthwiseConstraint, pointwiseInitializer, ' +\n          'pointwiseRegularizer and pointwiseConstraint instead.');\n    }\n    if (config.padding != null && config.padding !== 'same' &&\n        config.padding !== 'valid') {\n      throw new ValueError(\n          `SeparableConv${this.rank}D supports only padding modes: ` +\n          `'same' and 'valid', but received ${JSON.stringify(config.padding)}`);\n    }\n\n    this.depthMultiplier =\n        config.depthMultiplier == null ? 1 : config.depthMultiplier;\n    this.depthwiseInitializer = getInitializer(\n        config.depthwiseInitializer || this.DEFAULT_DEPTHWISE_INITIALIZER);\n    this.depthwiseRegularizer = getRegularizer(config.depthwiseRegularizer);\n    this.depthwiseConstraint = getConstraint(config.depthwiseConstraint);\n    this.pointwiseInitializer = getInitializer(\n        config.depthwiseInitializer || this.DEFAULT_POINTWISE_INITIALIZER);\n    this.pointwiseRegularizer = getRegularizer(config.pointwiseRegularizer);\n    this.pointwiseConstraint = getConstraint(config.pointwiseConstraint);\n  }\n\n  override build(inputShape: Shape|Shape[]): void {\n    inputShape = getExactlyOneShape(inputShape);\n    if (inputShape.length < this.rank + 2) {\n      throw new ValueError(\n          `Inputs to SeparableConv${this.rank}D should have rank ` +\n          `${this.rank + 2}, but received input shape: ` +\n          `${JSON.stringify(inputShape)}`);\n    }\n    const channelAxis =\n        this.dataFormat === 'channelsFirst' ? 1 : inputShape.length - 1;\n    if (inputShape[channelAxis] == null || inputShape[channelAxis] < 0) {\n      throw new ValueError(\n          `The channel dimension of the inputs should be defined, ` +\n          `but found ${JSON.stringify(inputShape[channelAxis])}`);\n    }\n\n    const inputDim = inputShape[channelAxis];\n    const depthwiseKernelShape =\n        this.kernelSize.concat([inputDim, this.depthMultiplier]);\n    const pointwiseKernelShape = [];\n    for (let i = 0; i < this.rank; ++i) {\n      pointwiseKernelShape.push(1);\n    }\n    pointwiseKernelShape.push(inputDim * this.depthMultiplier, this.filters);\n\n    const trainable = true;\n    this.depthwiseKernel = this.addWeight(\n        'depthwise_kernel', depthwiseKernelShape, 'float32',\n        this.depthwiseInitializer, this.depthwiseRegularizer, trainable,\n        this.depthwiseConstraint);\n    this.pointwiseKernel = this.addWeight(\n        'pointwise_kernel', pointwiseKernelShape, 'float32',\n        this.pointwiseInitializer, this.pointwiseRegularizer, trainable,\n        this.pointwiseConstraint);\n    if (this.useBias) {\n      this.bias = this.addWeight(\n          'bias', [this.filters], 'float32', this.biasInitializer,\n          this.biasRegularizer, trainable, this.biasConstraint);\n    } else {\n      this.bias = null;\n    }\n\n    this.inputSpec =\n        [new InputSpec({ndim: this.rank + 2, axes: {[channelAxis]: inputDim}})];\n    this.built = true;\n  }\n\n  override call(inputs: Tensor|Tensor[], kwargs: Kwargs): Tensor|Tensor[] {\n    return tidy(() => {\n      inputs = getExactlyOneTensor(inputs);\n\n      let output: Tensor;\n      if (this.rank === 1) {\n        throw new NotImplementedError(\n            '1D separable convolution is not implemented yet.');\n      } else if (this.rank === 2) {\n        if (this.dataFormat === 'channelsFirst') {\n          inputs = tfc.transpose(inputs, [0, 2, 3, 1]);  // NCHW -> NHWC.\n        }\n\n        output = tfc.separableConv2d(\n            inputs as Tensor4D, this.depthwiseKernel.read() as Tensor4D,\n            this.pointwiseKernel.read() as Tensor4D,\n            this.strides as [number, number], this.padding as 'same' | 'valid',\n            this.dilationRate as [number, number], 'NHWC');\n      }\n\n      if (this.useBias) {\n        output = K.biasAdd(output, this.bias.read(), this.dataFormat);\n      }\n      if (this.activation != null) {\n        output = this.activation.apply(output);\n      }\n\n      if (this.dataFormat === 'channelsFirst') {\n        output = tfc.transpose(output, [0, 3, 1, 2]);  // NHWC -> NCHW.\n      }\n      return output;\n    });\n  }\n\n  override getConfig(): serialization.ConfigDict {\n    const config = super.getConfig();\n    delete config['rank'];\n    delete config['kernelInitializer'];\n    delete config['kernelRegularizer'];\n    delete config['kernelConstraint'];\n    config['depthwiseInitializer'] =\n        serializeInitializer(this.depthwiseInitializer);\n    config['pointwiseInitializer'] =\n        serializeInitializer(this.pointwiseInitializer);\n    config['depthwiseRegularizer'] =\n        serializeRegularizer(this.depthwiseRegularizer);\n    config['pointwiseRegularizer'] =\n        serializeRegularizer(this.pointwiseRegularizer);\n    config['depthwiseConstraint'] =\n        serializeConstraint(this.depthwiseConstraint);\n    config['pointwiseConstraint'] =\n        serializeConstraint(this.pointwiseConstraint);\n    return config;\n  }\n}\n\nexport class SeparableConv2D extends SeparableConv {\n  /** @nocollapse */\n  static override className = 'SeparableConv2D';\n  constructor(args?: SeparableConvLayerArgs) {\n    super(2, args);\n  }\n}\nserialization.registerClass(SeparableConv2D);\n\nexport class Conv1D extends Conv {\n  /** @nocollapse */\n  static className = 'Conv1D';\n  constructor(args: ConvLayerArgs) {\n    super(1, args);\n    Conv1D.verifyArgs(args);\n    this.inputSpec = [{ndim: 3}];\n  }\n\n  override getConfig(): serialization.ConfigDict {\n    const config = super.getConfig();\n    delete config['rank'];\n    delete config['dataFormat'];\n    return config;\n  }\n\n  protected static override verifyArgs(args: ConvLayerArgs) {\n    // config.kernelSize must be a number or array of numbers.\n    if (typeof args.kernelSize !== 'number' &&\n        !generic_utils.checkArrayTypeAndLength(\n            args.kernelSize, 'number', 1, 1)) {\n      throw new ValueError(\n          `Conv1D expects config.kernelSize to be number or number[] with ` +\n          `length 1, but received ${JSON.stringify(args.kernelSize)}.`);\n    }\n  }\n}\nserialization.registerClass(Conv1D);\n\nexport declare interface Cropping2DLayerArgs extends LayerArgs {\n  /**\n   * Dimension of the cropping along the width and the height.\n   * - If integer: the same symmetric cropping\n   *  is applied to width and height.\n   * - If list of 2 integers:\n   *   interpreted as two different\n   *   symmetric cropping values for height and width:\n   *   `[symmetric_height_crop, symmetric_width_crop]`.\n   * - If a list of 2 lists of 2 integers:\n   *   interpreted as\n   *   `[[top_crop, bottom_crop], [left_crop, right_crop]]`\n   */\n  cropping: number|[number, number]|[[number, number], [number, number]];\n\n  /**\n   * Format of the data, which determines the ordering of the dimensions in\n   * the inputs.\n   *\n   * `channels_last` corresponds to inputs with shape\n   *   `(batch, ..., channels)`\n   *\n   * `channels_first` corresponds to inputs with shape\n   *   `(batch, channels, ...)`\n   *\n   * Defaults to `channels_last`.\n   */\n  dataFormat?: DataFormat;\n}\n\nexport class Cropping2D extends Layer {\n  /** @nocollapse */\n  static className = 'Cropping2D';\n  protected readonly cropping: [[number, number], [number, number]];\n  protected readonly dataFormat: DataFormat;\n\n  constructor(args: Cropping2DLayerArgs) {\n    super(args);\n    if (typeof args.cropping === 'number') {\n      this.cropping =\n          [[args.cropping, args.cropping], [args.cropping, args.cropping]];\n    } else if (typeof args.cropping[0] === 'number') {\n      this.cropping = [\n        [args.cropping[0], args.cropping[0]],\n        [args.cropping[1] as number, args.cropping[1] as number]\n      ];\n    } else {\n      this.cropping = args.cropping as [[number, number], [number, number]];\n    }\n    this.dataFormat =\n        args.dataFormat === undefined ? 'channelsLast' : args.dataFormat;\n    this.inputSpec = [{ndim: 4}];\n  }\n\n  override computeOutputShape(inputShape: Shape): Shape {\n    if (this.dataFormat === 'channelsFirst') {\n      return [\n        inputShape[0], inputShape[1],\n        inputShape[2] - this.cropping[0][0] - this.cropping[0][1],\n        inputShape[3] - this.cropping[1][0] - this.cropping[1][1]\n      ];\n    } else {\n      return [\n        inputShape[0],\n        inputShape[1] - this.cropping[0][0] - this.cropping[0][1],\n        inputShape[2] - this.cropping[1][0] - this.cropping[1][1], inputShape[3]\n      ];\n    }\n  }\n\n  override call(inputs: Tensor|Tensor[], kwargs: Kwargs): Tensor|Tensor[] {\n    return tidy(() => {\n      inputs = getExactlyOneTensor(inputs);\n\n      if (this.dataFormat === 'channelsLast') {\n        const hSliced = K.sliceAlongAxis(\n            inputs, this.cropping[0][0],\n            inputs.shape[1] - this.cropping[0][0] - this.cropping[0][1], 2);\n        return K.sliceAlongAxis(\n            hSliced, this.cropping[1][0],\n            inputs.shape[2] - this.cropping[1][1] - this.cropping[1][0], 3);\n      } else {\n        const hSliced = K.sliceAlongAxis(\n            inputs, this.cropping[0][0],\n            inputs.shape[2] - this.cropping[0][0] - this.cropping[0][1], 3);\n        return K.sliceAlongAxis(\n            hSliced, this.cropping[1][0],\n            inputs.shape[3] - this.cropping[1][1] - this.cropping[1][0], 4);\n      }\n    });\n  }\n\n  override getConfig(): serialization.ConfigDict {\n    const config = {cropping: this.cropping, dataFormat: this.dataFormat};\n    const baseConfig = super.getConfig();\n    Object.assign(config, baseConfig);\n    return config;\n  }\n}\nserialization.registerClass(Cropping2D);\n\nexport declare interface UpSampling2DLayerArgs extends LayerArgs {\n  /**\n   * The upsampling factors for rows and columns.\n   *\n   * Defaults to `[2, 2]`.\n   */\n  size?: number[];\n  /**\n   * Format of the data, which determines the ordering of the dimensions in\n   * the inputs.\n   *\n   * `\"channelsLast\"` corresponds to inputs with shape\n   *   `[batch, ..., channels]`\n   *\n   *  `\"channelsFirst\"` corresponds to inputs with shape `[batch, channels,\n   * ...]`.\n   *\n   * Defaults to `\"channelsLast\"`.\n   */\n  dataFormat?: DataFormat;\n  /**\n   * The interpolation mechanism, one of `\"nearest\"` or `\"bilinear\"`, default\n   * to `\"nearest\"`.\n   */\n  interpolation?: InterpolationFormat;\n}\n\nexport class UpSampling2D extends Layer {\n  /** @nocollapse */\n  static className = 'UpSampling2D';\n  protected readonly DEFAULT_SIZE = [2, 2];\n  protected readonly size: number[];\n  protected readonly dataFormat: DataFormat;\n  protected readonly interpolation: InterpolationFormat;\n\n  constructor(args: UpSampling2DLayerArgs) {\n    super(args);\n    this.inputSpec = [{ndim: 4}];\n    this.size = args.size == null ? this.DEFAULT_SIZE : args.size;\n    this.dataFormat =\n        args.dataFormat == null ? 'channelsLast' : args.dataFormat;\n    checkDataFormat(this.dataFormat);\n    this.interpolation =\n        args.interpolation == null ? 'nearest' : args.interpolation;\n    checkInterpolationFormat(this.interpolation);\n  }\n\n  override computeOutputShape(inputShape: Shape): Shape {\n    if (this.dataFormat === 'channelsFirst') {\n      const height =\n          inputShape[2] == null ? null : this.size[0] * inputShape[2];\n      const width = inputShape[3] == null ? null : this.size[1] * inputShape[3];\n      return [inputShape[0], inputShape[1], height, width];\n    } else {\n      const height =\n          inputShape[1] == null ? null : this.size[0] * inputShape[1];\n      const width = inputShape[2] == null ? null : this.size[1] * inputShape[2];\n      return [inputShape[0], height, width, inputShape[3]];\n    }\n  }\n\n  override call(inputs: Tensor|Tensor[], kwargs: Kwargs): Tensor|Tensor[] {\n    return tfc.tidy(() => {\n      let input = getExactlyOneTensor(inputs) as Tensor4D;\n      const inputShape = input.shape;\n\n      if (this.dataFormat === 'channelsFirst') {\n        input = tfc.transpose(input, [0, 2, 3, 1]);\n        const height = this.size[0] * inputShape[2];\n        const width = this.size[1] * inputShape[3];\n\n        const resized = this.interpolation === 'nearest' ?\n            tfc.image.resizeNearestNeighbor(input, [height, width]) :\n            tfc.image.resizeBilinear(input, [height, width]);\n        return tfc.transpose(resized, [0, 3, 1, 2]);\n      } else {\n        const height = this.size[0] * inputShape[1];\n        const width = this.size[1] * inputShape[2];\n        return this.interpolation === 'nearest' ?\n            tfc.image.resizeNearestNeighbor(input, [height, width]) :\n            tfc.image.resizeBilinear(input, [height, width]);\n      }\n    });\n  }\n\n  override getConfig(): serialization.ConfigDict {\n    const config = {\n        size: this.size,\n        dataFormat: this.dataFormat,\n        interpolation: this.interpolation\n    };\n    const baseConfig = super.getConfig();\n    Object.assign(config, baseConfig);\n    return config;\n  }\n}\nserialization.registerClass(UpSampling2D);\n"], "mappings": "AAAA;;;;;;;;;AAUA;;;AAIA,OAAO,KAAKA,GAAG,MAAM,uBAAuB;AAC5C,SAAeC,aAAa,EAA4DC,IAAI,QAAO,uBAAuB;AAE1H,SAAoBC,aAAa,EAAEC,mBAAmB,QAAO,gBAAgB;AAC7E,SAAQC,eAAe,QAAO,mBAAmB;AACjD,OAAO,KAAKC,CAAC,MAAM,yBAAyB;AAC5C,SAAQC,eAAe,EAAEC,wBAAwB,EAAEC,gBAAgB,QAAO,WAAW;AACrF,SAA0CC,aAAa,EAAEC,mBAAmB,QAAO,gBAAgB;AACnG,SAAQC,SAAS,EAAEC,KAAK,QAAkB,oBAAoB;AAC9D,SAAQC,mBAAmB,EAAEC,UAAU,QAAO,WAAW;AACzD,SAAQC,cAAc,EAAsCC,oBAAoB,QAAO,iBAAiB;AAGxG,SAAQC,cAAc,EAAsCC,oBAAoB,QAAO,iBAAiB;AAExG,SAAQC,gBAAgB,EAAEC,YAAY,EAAEC,cAAc,QAAO,qBAAqB;AAClF,OAAO,KAAKC,aAAa,MAAM,wBAAwB;AACvD,SAAQC,kBAAkB,EAAEC,mBAAmB,QAAO,sBAAsB;AAG5E;;;;;AAKA,OAAM,SAAUC,qBAAqBA,CACjCC,CAAS,EAAEC,UAAsB;EACnC;EACA,OAAO1B,IAAI,CAAC,MAAK;IACfK,eAAe,CAACqB,UAAU,CAAC;IAC3B,IAAIA,UAAU,KAAK,eAAe,EAAE;MAClC,OAAO5B,GAAG,CAAC6B,SAAS,CAACF,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAE;KACzC,MAAM;MACL,OAAOA,CAAC;;EAEZ,CAAC,CAAC;AACJ;AAEA;;;;;AAKA,OAAM,SAAUG,qBAAqBA,CACjCH,CAAS,EAAEC,UAAsB;EACnC,OAAO1B,IAAI,CAAC,MAAK;IACfK,eAAe,CAACqB,UAAU,CAAC;IAC3B,IAAIA,UAAU,KAAK,eAAe,EAAE;MAClC,OAAO5B,GAAG,CAAC6B,SAAS,CAACF,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAE;KAC5C,MAAM;MACL,OAAOA,CAAC;;EAEZ,CAAC,CAAC;AACJ;AAEA;;;;;;;;;;;;;;;;AAgBA,OAAM,SAAUI,cAAcA,CAC1BJ,CAAS,EAAEK,MAAc,EAAEC,IAAY,EACE;EAAA,IADAC,OAAO,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC;EAAA,IAAEG,OAAO,GAAAH,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,OAAO;EAAA,IACvEP,UAAuB,GAAAO,SAAA,CAAAC,MAAA,OAAAD,SAAA,MAAAE,SAAA;EAAA,IAAEE,YAAY,GAAAJ,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC;EAC3C,OAAOjC,IAAI,CAAC,MAAK;IACf,IAAI0B,UAAU,IAAI,IAAI,EAAE;MACtBA,UAAU,GAAGvB,eAAe,EAAE;;IAEhCE,eAAe,CAACqB,UAAU,CAAC;IAC3B;IACA,IAAID,CAAC,CAACa,KAAK,CAACJ,MAAM,KAAK,CAAC,EAAE;MACxB,MAAM,IAAIrB,UAAU,CAChB,oEAAA0B,MAAA,CACGd,CAAC,CAACa,KAAK,CAACJ,MAAM,cAAW,CAAC;;IAEnC,IAAIJ,MAAM,CAACQ,KAAK,CAACJ,MAAM,KAAK,CAAC,EAAE;MAC7B,MAAM,IAAIrB,UAAU,CAChB,sEAAA0B,MAAA,CACGT,MAAM,CAACQ,KAAK,CAACJ,MAAM,aAAU,CAAC;;IAEvC,IAAIH,IAAI,IAAI,IAAI,IAAIA,IAAI,CAACO,KAAK,CAACJ,MAAM,KAAK,CAAC,EAAE;MAC3C,MAAM,IAAIrB,UAAU,CAChB,oEAAA0B,MAAA,CACGR,IAAI,CAACO,KAAK,CAACJ,MAAM,aAAU,CAAC;;IAErC;IACA,IAAIR,UAAU,KAAK,eAAe,EAAE;MAClCD,CAAC,GAAG3B,GAAG,CAAC6B,SAAS,CAACF,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAE;;IAEpC,IAAIW,OAAO,KAAK,QAAQ,EAAE;MACxB,MAAM,IAAIxB,mBAAmB,CACzB,+DAA+D,GAC/D,kBAAkB,CAAC;;IAEzB,IAAI4B,CAAC,GAAW1C,GAAG,CAAC2C,MAAM,CACtBhB,CAAwB,EAAEK,MAAkB,EAAEE,OAAO,EACrDI,OAAO,KAAK,MAAM,GAAG,MAAM,GAAG,OAAO,EAAE,KAAK,EAAEC,YAAY,CAAC;IAC/D,IAAIN,IAAI,IAAI,IAAI,EAAE;MAChBS,CAAC,GAAGpC,CAAC,CAACsC,OAAO,CAACF,CAAC,EAAET,IAAI,CAAC;;IAExB,OAAOS,CAAC;EACV,CAAC,CAAC;AACJ;AAEA;;;;;;;;;;;;AAYA,OAAM,SAAUC,MAAMA,CAClBhB,CAAS,EAAEK,MAAc,EACgB;EAAA,IADdE,OAAO,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC;EAAA,IAAEG,OAAO,GAAAH,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,OAAO;EAAA,IACzDP,UAAuB,GAAAO,SAAA,CAAAC,MAAA,OAAAD,SAAA,MAAAE,SAAA;EAAA,IAAEE,YAAY,GAAAJ,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC;EAC3C,OAAOjC,IAAI,CAAC,MAAK;IACfK,eAAe,CAACqB,UAAU,CAAC;IAC3B,OAAOG,cAAc,CACjBJ,CAAC,EAAEK,MAAM,EAAE,IAAI,EAAEE,OAAO,EAAEI,OAAO,EAAEV,UAAU,EAAEW,YAAY,CAAC;EAClE,CAAC,CAAC;AACJ;AAEA;;;;;;;;;;AAUA,OAAM,SAAUM,MAAMA,CAClBlB,CAAS,EAAEK,MAAc,EAC+B;EAAA,IAD7BE,OAAO,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC,EAAE,CAAC,CAAC;EAAA,IAAEG,OAAO,GAAAH,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,OAAO;EAAA,IAC9DP,UAAuB,GAAAO,SAAA,CAAAC,MAAA,OAAAD,SAAA,MAAAE,SAAA;EAAA,IAAEE,YAA+B,GAAAJ,SAAA,CAAAC,MAAA,OAAAD,SAAA,MAAAE,SAAA;EAC1D,OAAOnC,IAAI,CAAC,MAAK;IACfK,eAAe,CAACqB,UAAU,CAAC;IAC3B,OAAOkB,wBAAwB,CAC3BnB,CAAC,EAAEK,MAAM,EAAE,IAAI,EAAEE,OAAO,EAAEI,OAAO,EAAEV,UAAU,EAAEW,YAAY,CAAC;EAClE,CAAC,CAAC;AACJ;AAEA;;;;;AAKA,OAAM,SAAUO,wBAAwBA,CACpCnB,CAAS,EAAEK,MAAc,EAAEC,IAAY,EAEJ;EAAA,IAFMC,OAAO,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC,EAAE,CAAC,CAAC;EAAA,IACzDG,OAAO,GAAAH,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,OAAO;EAAA,IAAEP,UAAuB,GAAAO,SAAA,CAAAC,MAAA,OAAAD,SAAA,MAAAE,SAAA;EAAA,IAAEE,YAA+B,GAAAJ,SAAA,CAAAC,MAAA,OAAAD,SAAA,MAAAE,SAAA;EAAA,IAC3EU,UAAA,GAAAZ,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAA+B,IAAI;EACrC,OAAOjC,IAAI,CAAC,MAAK;IACf,IAAI0B,UAAU,IAAI,IAAI,EAAE;MACtBA,UAAU,GAAGvB,eAAe,EAAE;;IAEhCE,eAAe,CAACqB,UAAU,CAAC;IAC3B,IAAID,CAAC,CAACqB,IAAI,KAAK,CAAC,IAAIrB,CAAC,CAACqB,IAAI,KAAK,CAAC,EAAE;MAChC,MAAM,IAAIjC,UAAU,CAChB,kFAAA0B,MAAA,CACgBd,CAAC,CAACqB,IAAI,MAAG,CAAC;;IAEhC,IAAIhB,MAAM,CAACgB,IAAI,KAAK,CAAC,IAAIhB,MAAM,CAACgB,IAAI,KAAK,CAAC,EAAE;MAC1C,MAAM,IAAIjC,UAAU,CAChB,mFAAA0B,MAAA,CACgBd,CAAC,CAACqB,IAAI,MAAG,CAAC;;IAEhC,IAAIN,CAAC,GAAGhB,qBAAqB,CAACC,CAAC,EAAEC,UAAU,CAAC;IAC5C,IAAIU,OAAO,KAAK,QAAQ,EAAE;MACxB,MAAM,IAAIxB,mBAAmB,CACzB,+DAA+D,GAC/D,kBAAkB,CAAC;;IAEzB4B,CAAC,GAAG1C,GAAG,CAACiD,KAAK,CAACJ,MAAM,CAAC;MACnBlB,CAAC,EAAEe,CAAwB;MAC3BQ,MAAM,EAAElB,MAAkB;MAC1BE,OAAO,EAAEA,OAA2B;MACpCiB,GAAG,EAAEb,OAAO,KAAK,MAAM,GAAG,MAAM,GAAG,OAAO;MAC1Cc,SAAS,EAAEb,YAAY;MACvBX,UAAU,EAAE,MAAM;MAClBK,IAAI;MACJc;KACD,CAAC;IACF,IAAInB,UAAU,KAAK,eAAe,EAAE;MAClCc,CAAC,GAAG1C,GAAG,CAAC6B,SAAS,CAACa,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;;IAEpC,OAAOA,CAAC;EACV,CAAC,CAAC;AACJ;AAEA;;;;;;;;;;AAUA,OAAM,SAAUW,MAAMA,CAClB1B,CAAS,EAAEK,MAAc,EACuC;EAAA,IADrCE,OAAO,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;EAAA,IAAEG,OAAO,GAAAH,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,OAAO;EAAA,IACjEP,UAAuB,GAAAO,SAAA,CAAAC,MAAA,OAAAD,SAAA,MAAAE,SAAA;EAAA,IAAEE,YAAuC,GAAAJ,SAAA,CAAAC,MAAA,OAAAD,SAAA,MAAAE,SAAA;EAClE,OAAOnC,IAAI,CAAC,MAAK;IACfK,eAAe,CAACqB,UAAU,CAAC;IAC3B,OAAO0B,cAAc,CACjB3B,CAAC,EAAEK,MAAM,EAAE,IAAI,EAAEE,OAAO,EAAEI,OAAO,EAAEV,UAAU,EAAEW,YAAY,CAAC;EAClE,CAAC,CAAC;AACJ;AAEA;;;;;AAKA,OAAM,SAAUe,cAAcA,CAC1B3B,CAAS,EAAEK,MAAc,EAAEC,IAAY,EAEA;EAAA,IAFEC,OAAO,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;EAAA,IAC5DG,OAAO,GAAAH,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,OAAO;EAAA,IAAEP,UAAuB,GAAAO,SAAA,CAAAC,MAAA,OAAAD,SAAA,MAAAE,SAAA;EAAA,IAC1CE,YAAuC,GAAAJ,SAAA,CAAAC,MAAA,OAAAD,SAAA,MAAAE,SAAA;EACzC,OAAOnC,IAAI,CAAC,MAAK;IACf,IAAI0B,UAAU,IAAI,IAAI,EAAE;MACtBA,UAAU,GAAGvB,eAAe,EAAE;;IAEhCE,eAAe,CAACqB,UAAU,CAAC;IAC3B,IAAID,CAAC,CAACqB,IAAI,KAAK,CAAC,IAAIrB,CAAC,CAACqB,IAAI,KAAK,CAAC,EAAE;MAChC,MAAM,IAAIjC,UAAU,CAChB,wEAAA0B,MAAA,CACGd,CAAC,CAACqB,IAAI,MAAG,CAAC;;IAEnB,IAAIhB,MAAM,CAACgB,IAAI,KAAK,CAAC,IAAIhB,MAAM,CAACgB,IAAI,KAAK,CAAC,EAAE;MAC1C,MAAM,IAAIjC,UAAU,CAChB,yEAAA0B,MAAA,CACGd,CAAC,CAACqB,IAAI,MAAG,CAAC;;IAEnB,IAAIN,CAAC,GAAGZ,qBAAqB,CAACH,CAAC,EAAEC,UAAU,CAAC;IAC5C,IAAIU,OAAO,KAAK,QAAQ,EAAE;MACxB,MAAM,IAAIxB,mBAAmB,CACzB,+DAA+D,GAC/D,kBAAkB,CAAC;;IAEzB4B,CAAC,GAAG1C,GAAG,CAACqD,MAAM,CACVX,CAAuC,EACvCV,MAAiC,EAAEE,OAAmC,EACtEI,OAAO,KAAK,MAAM,GAAG,MAAM,GAAG,OAAO,EAAE,OAAO,EAAEC,YAAY,CAAC;IACjE,IAAIN,IAAI,IAAI,IAAI,EAAE;MAChBS,CAAC,GAAGpC,CAAC,CAACsC,OAAO,CAACF,CAAC,EAAET,IAAgB,CAAC;;IAEpC,IAAIL,UAAU,KAAK,eAAe,EAAE;MAClCc,CAAC,GAAG1C,GAAG,CAAC6B,SAAS,CAACa,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;;IAEvC,OAAOA,CAAC;EACV,CAAC,CAAC;AACJ;AA8GA;;;AAGA,OAAM,MAAgBa,QAAS,SAAQ1C,KAAK;EAwB1C2C,YAAYR,IAAY,EAAES,IAAuB;IAC/C,KAAK,CAACA,IAAiB,CAAC;IANhB,KAAAxB,IAAI,GAAkB,IAAI;IAE3B,KAAAyB,0BAA0B,GAA0B,cAAc;IAClE,KAAAC,wBAAwB,GAA0B,OAAO;IAIhEJ,QAAQ,CAACK,UAAU,CAACH,IAAI,CAAC;IACzB,IAAI,CAACT,IAAI,GAAGA,IAAI;IAChBzB,aAAa,CAACsC,qBAAqB,CAAC,IAAI,CAACb,IAAI,EAAE,MAAM,CAAC;IACtD,IAAI,IAAI,CAACA,IAAI,KAAK,CAAC,IAAI,IAAI,CAACA,IAAI,KAAK,CAAC,IAAI,IAAI,CAACA,IAAI,KAAK,CAAC,EAAE;MACzD,MAAM,IAAIlC,mBAAmB,CACzB,qDAAA2B,MAAA,CACI,IAAI,CAACO,IAAI,mCACS,CAAC;;IAE7B,IAAI,CAACc,UAAU,GAAGxC,cAAc,CAACmC,IAAI,CAACK,UAAU,EAAEd,IAAI,EAAE,YAAY,CAAC;IACrE,IAAI,CAACd,OAAO,GAAGZ,cAAc,CACzBmC,IAAI,CAACvB,OAAO,IAAI,IAAI,GAAG,CAAC,GAAGuB,IAAI,CAACvB,OAAO,EAAEc,IAAI,EAAE,SAAS,CAAC;IAC7D,IAAI,CAACV,OAAO,GAAGmB,IAAI,CAACnB,OAAO,IAAI,IAAI,GAAG,OAAO,GAAGmB,IAAI,CAACnB,OAAO;IAC5D7B,gBAAgB,CAAC,IAAI,CAAC6B,OAAO,CAAC;IAC9B,IAAI,CAACV,UAAU,GACX6B,IAAI,CAAC7B,UAAU,IAAI,IAAI,GAAG,cAAc,GAAG6B,IAAI,CAAC7B,UAAU;IAC9DrB,eAAe,CAAC,IAAI,CAACqB,UAAU,CAAC;IAChC,IAAI,CAACmB,UAAU,GAAG5C,aAAa,CAACsD,IAAI,CAACV,UAAU,CAAC;IAChD,IAAI,CAACgB,OAAO,GAAGN,IAAI,CAACM,OAAO,IAAI,IAAI,GAAG,IAAI,GAAGN,IAAI,CAACM,OAAO;IACzD,IAAI,CAACC,eAAe,GAChBhD,cAAc,CAACyC,IAAI,CAACO,eAAe,IAAI,IAAI,CAACL,wBAAwB,CAAC;IACzE,IAAI,CAACM,cAAc,GAAGvD,aAAa,CAAC+C,IAAI,CAACQ,cAAc,CAAC;IACxD,IAAI,CAACC,eAAe,GAAGhD,cAAc,CAACuC,IAAI,CAACS,eAAe,CAAC;IAC3D,IAAI,CAACC,mBAAmB,GAAGjD,cAAc,CAACuC,IAAI,CAACU,mBAAmB,CAAC;IACnE,IAAI,CAAC5B,YAAY,GAAGjB,cAAc,CAC9BmC,IAAI,CAAClB,YAAY,IAAI,IAAI,GAAG,CAAC,GAAGkB,IAAI,CAAClB,YAAY,EAAES,IAAI,EACvD,cAAc,CAAC;IACnB,IAAI,IAAI,CAACA,IAAI,KAAK,CAAC,IACdoB,KAAK,CAACC,OAAO,CAAC,IAAI,CAAC9B,YAAY,CAAC,IAAI,IAAI,CAACA,YAAY,CAACH,MAAM,KAAK,CAAE,EAAE;MACxE,MAAM,IAAIrB,UAAU,CAChB,qGACmC,MAAA0B,MAAA,CAChC6B,IAAI,CAACC,SAAS,CAAC,IAAI,CAAChC,YAAY,CAAC,CAAE,CAAC;KAC5C,MAAM,IAAI,IAAI,CAACS,IAAI,KAAK,CAAC,EAAE;MAC1B,IAAI,OAAO,IAAI,CAACT,YAAY,KAAK,QAAQ,EAAE;QACzC,IAAI,CAACA,YAAY,GAAG,CAAC,IAAI,CAACA,YAAY,EAAE,IAAI,CAACA,YAAY,CAAC;OAC3D,MAAM,IAAI,IAAI,CAACA,YAAY,CAACH,MAAM,KAAK,CAAC,EAAE;QACzC,MAAM,IAAIrB,UAAU,CAChB,+FAAA0B,MAAA,CAC6B6B,IAAI,CAACC,SAAS,CAAC,IAAI,CAAChC,YAAY,CAAC,CAAE,CAAC;;KAExE,MAAM,IAAI,IAAI,CAACS,IAAI,KAAK,CAAC,EAAE;MAC1B,IAAI,OAAO,IAAI,CAACT,YAAY,KAAK,QAAQ,EAAE;QACzC,IAAI,CAACA,YAAY,GACb,CAAC,IAAI,CAACA,YAAY,EAAE,IAAI,CAACA,YAAY,EAAE,IAAI,CAACA,YAAY,CAAC;OAC9D,MAAM,IAAI,IAAI,CAACA,YAAY,CAACH,MAAM,KAAK,CAAC,EAAE;QACzC,MAAM,IAAIrB,UAAU,CAChB,iGAAA0B,MAAA,CAC6B6B,IAAI,CAACC,SAAS,CAAC,IAAI,CAAChC,YAAY,CAAC,CAAE,CAAC;;;EAG3E;EAEU,OAAOqB,UAAUA,CAACH,IAAuB;IACjD;IACAlC,aAAa,CAACiD,MAAM,CAChB,YAAY,IAAIf,IAAI,2CAA2C,CAAC;IACpE,IAAI,OAAOA,IAAI,CAACK,UAAU,KAAK,QAAQ,IACnC,CAACvC,aAAa,CAACkD,uBAAuB,CAClChB,IAAI,CAACK,UAAU,EAAE,QAAQ,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE;MACxC,MAAM,IAAI/C,UAAU,CAChB,yGAAA0B,MAAA,CAEI6B,IAAI,CAACC,SAAS,CAACd,IAAI,CAACK,UAAU,CAAC,MAAG,CAAC;;EAE/C;EAESY,SAASA,CAAA;IAChB,MAAMC,MAAM,GAA6B;MACvCb,UAAU,EAAE,IAAI,CAACA,UAAU;MAC3B5B,OAAO,EAAE,IAAI,CAACA,OAAO;MACrBI,OAAO,EAAE,IAAI,CAACA,OAAO;MACrBV,UAAU,EAAE,IAAI,CAACA,UAAU;MAC3BW,YAAY,EAAE,IAAI,CAACA,YAAY;MAC/BQ,UAAU,EAAE3C,mBAAmB,CAAC,IAAI,CAAC2C,UAAU,CAAC;MAChDgB,OAAO,EAAE,IAAI,CAACA,OAAO;MACrBC,eAAe,EAAE/C,oBAAoB,CAAC,IAAI,CAAC+C,eAAe,CAAC;MAC3DE,eAAe,EAAE/C,oBAAoB,CAAC,IAAI,CAAC+C,eAAe,CAAC;MAC3DC,mBAAmB,EAAEhD,oBAAoB,CAAC,IAAI,CAACgD,mBAAmB,CAAC;MACnEF,cAAc,EAAEtD,mBAAmB,CAAC,IAAI,CAACsD,cAAc;KACxD;IACD,MAAMW,UAAU,GAAG,KAAK,CAACF,SAAS,EAAE;IACpCG,MAAM,CAACC,MAAM,CAACH,MAAM,EAAEC,UAAU,CAAC;IACjC,OAAOD,MAAM;EACf;;AAGF;;;;AAIA,OAAM,MAAgBI,IAAK,SAAQxB,QAAQ;EAczCC,YAAYR,IAAY,EAAES,IAAmB;IAC3C,KAAK,CAACT,IAAI,EAAES,IAAyB,CAAC;IAZ9B,KAAAzB,MAAM,GAAkB,IAAI;IAapC+C,IAAI,CAACnB,UAAU,CAACH,IAAI,CAAC;IACrB,IAAI,CAACuB,OAAO,GAAGvB,IAAI,CAACuB,OAAO;IAC3BzD,aAAa,CAACsC,qBAAqB,CAAC,IAAI,CAACmB,OAAO,EAAE,SAAS,CAAC;IAC5D,IAAI,CAACC,iBAAiB,GAAGjE,cAAc,CACnCyC,IAAI,CAACwB,iBAAiB,IAAI,IAAI,CAACvB,0BAA0B,CAAC;IAC9D,IAAI,CAACwB,gBAAgB,GAAGxE,aAAa,CAAC+C,IAAI,CAACyB,gBAAgB,CAAC;IAC5D,IAAI,CAACC,iBAAiB,GAAGjE,cAAc,CAACuC,IAAI,CAAC0B,iBAAiB,CAAC;EACjE;EAESC,KAAKA,CAACC,UAAyB;IACtCA,UAAU,GAAG7D,kBAAkB,CAAC6D,UAAU,CAAC;IAC3C,MAAMC,WAAW,GACb,IAAI,CAAC1D,UAAU,KAAK,eAAe,GAAG,CAAC,GAAGyD,UAAU,CAACjD,MAAM,GAAG,CAAC;IACnE,IAAIiD,UAAU,CAACC,WAAW,CAAC,IAAI,IAAI,EAAE;MACnC,MAAM,IAAIvE,UAAU,CAChB,oEAAA0B,MAAA,CACS4C,UAAU,CAACC,WAAW,CAAC,CAAE,CAAC;;IAEzC,MAAMC,QAAQ,GAAGF,UAAU,CAACC,WAAW,CAAC;IAExC,MAAME,WAAW,GAAG,IAAI,CAAC1B,UAAU,CAACrB,MAAM,CAAC,CAAC8C,QAAQ,EAAE,IAAI,CAACP,OAAO,CAAC,CAAC;IAEpE,IAAI,CAAChD,MAAM,GAAG,IAAI,CAACyD,SAAS,CACxB,QAAQ,EAAED,WAAW,EAAE,IAAI,EAAE,IAAI,CAACP,iBAAiB,EACnD,IAAI,CAACE,iBAAiB,EAAE,IAAI,EAAE,IAAI,CAACD,gBAAgB,CAAC;IACxD,IAAI,IAAI,CAACnB,OAAO,EAAE;MAChB,IAAI,CAAC9B,IAAI,GAAG,IAAI,CAACwD,SAAS,CACtB,MAAM,EAAE,CAAC,IAAI,CAACT,OAAO,CAAC,EAAE,IAAI,EAAE,IAAI,CAAChB,eAAe,EAClD,IAAI,CAACE,eAAe,EAAE,IAAI,EAAE,IAAI,CAACD,cAAc,CAAC;;IAGtD,IAAI,CAACyB,SAAS,GAAG,CAAC;MAACC,IAAI,EAAE,IAAI,CAAC3C,IAAI,GAAG,CAAC;MAAE4C,IAAI,EAAE;QAAC,CAACN,WAAW,GAAGC;MAAQ;IAAC,CAAC,CAAC;IACzE,IAAI,CAACM,KAAK,GAAG,IAAI;EACnB;EAESC,IAAIA,CAACC,MAAuB,EAAEC,MAAc;IACnD,OAAO9F,IAAI,CAAC,MAAK;MACf6F,MAAM,GAAGtE,mBAAmB,CAACsE,MAAM,CAAC;MACpC,IAAIE,OAAe;MACnB,MAAMC,SAAS,GAAG,IAAI,CAACjE,IAAI,IAAI,IAAI,GAAG,IAAI,GAAG,IAAI,CAACA,IAAI,CAACkE,IAAI,EAAE;MAC7D,MAAMC,mBAAmB,GAAG7E,aAAa,CAAC8E,0BAA0B,CAChE,IAAI,CAACtD,UAAU,CAACuD,YAAY,EAAE,CAAC;MAEnC,IAAIF,mBAAmB,IAAI,IAAI,IAAI,IAAI,CAACpD,IAAI,KAAK,CAAC,EAAE;QAClDiD,OAAO,GAAGnD,wBAAwB,CAC9BiD,MAAM,EAAE,IAAI,CAAC/D,MAAM,CAACmE,IAAI,EAAE,EAAED,SAAS,EAAE,IAAI,CAAChE,OAAO,EAAE,IAAI,CAACI,OAAO,EACjE,IAAI,CAACV,UAAU,EAAE,IAAI,CAACW,YAAgC,EACtD6D,mBAAmB,CAAC;OACzB,MAAM;QACL,IAAI,IAAI,CAACpD,IAAI,KAAK,CAAC,EAAE;UACnBiD,OAAO,GAAGlE,cAAc,CACpBgE,MAAM,EAAE,IAAI,CAAC/D,MAAM,CAACmE,IAAI,EAAE,EAAED,SAAS,EAAE,IAAI,CAAChE,OAAO,CAAC,CAAC,CAAC,EACtD,IAAI,CAACI,OAAO,EAAE,IAAI,CAACV,UAAU,EAAE,IAAI,CAACW,YAAY,CAAC,CAAC,CAAC,CAAC;SACzD,MAAM,IAAI,IAAI,CAACS,IAAI,KAAK,CAAC,EAAE;UAC1B;UACAiD,OAAO,GAAGnD,wBAAwB,CAC9BiD,MAAM,EAAE,IAAI,CAAC/D,MAAM,CAACmE,IAAI,EAAE,EAAED,SAAS,EAAE,IAAI,CAAChE,OAAO,EAAE,IAAI,CAACI,OAAO,EACjE,IAAI,CAACV,UAAU,EAAE,IAAI,CAACW,YAAgC,CAAC;SAC5D,MAAM,IAAI,IAAI,CAACS,IAAI,KAAK,CAAC,EAAE;UAC1BiD,OAAO,GAAG3C,cAAc,CACpByC,MAAM,EAAE,IAAI,CAAC/D,MAAM,CAACmE,IAAI,EAAE,EAAED,SAAS,EAAE,IAAI,CAAChE,OAAO,EAAE,IAAI,CAACI,OAAO,EACjE,IAAI,CAACV,UAAU,EAAE,IAAI,CAACW,YAAwC,CAAC;SACpE,MAAM;UACL,MAAM,IAAIzB,mBAAmB,CACzB,uDAAuD,CAAC;;QAG9D,IAAI,IAAI,CAACiC,UAAU,IAAI,IAAI,EAAE;UAC3BkD,OAAO,GAAG,IAAI,CAAClD,UAAU,CAACwD,KAAK,CAACN,OAAO,CAAC;;;MAI5C,OAAOA,OAAO;IAChB,CAAC,CAAC;EACJ;EAESO,kBAAkBA,CAACnB,UAAyB;IACnDA,UAAU,GAAG7D,kBAAkB,CAAC6D,UAAU,CAAC;IAC3C,MAAMoB,QAAQ,GAAa,EAAE;IAC7B,MAAMC,KAAK,GAAI,IAAI,CAAC9E,UAAU,KAAK,cAAc,GAC7CyD,UAAU,CAACsB,KAAK,CAAC,CAAC,EAAEtB,UAAU,CAACjD,MAAM,GAAG,CAAC,CAAC,GAC1CiD,UAAU,CAACsB,KAAK,CAAC,CAAC,CAAC;IACvB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,KAAK,CAACtE,MAAM,EAAE,EAAEwE,CAAC,EAAE;MACrC,MAAMC,MAAM,GAAGzF,gBAAgB,CAC3BsF,KAAK,CAACE,CAAC,CAAC,EAAE,IAAI,CAAC9C,UAAU,CAAC8C,CAAC,CAAC,EAAE,IAAI,CAACtE,OAAO,EAAE,IAAI,CAACJ,OAAO,CAAC0E,CAAC,CAAC,EAC3D,OAAO,IAAI,CAACrE,YAAY,KAAK,QAAQ,GAAG,IAAI,CAACA,YAAY,GACjB,IAAI,CAACA,YAAY,CAACqE,CAAC,CAAC,CAAC;MACjEH,QAAQ,CAACK,IAAI,CAACD,MAAM,CAAC;;IAGvB,IAAIE,WAAW,GAAG,CAAC1B,UAAU,CAAC,CAAC,CAAC,CAAC;IACjC,IAAI,IAAI,CAACzD,UAAU,KAAK,cAAc,EAAE;MACtCmF,WAAW,GAAGA,WAAW,CAACtE,MAAM,CAACgE,QAAQ,CAAC;MAC1CM,WAAW,CAACD,IAAI,CAAC,IAAI,CAAC9B,OAAO,CAAC;KAC/B,MAAM;MACL+B,WAAW,CAACD,IAAI,CAAC,IAAI,CAAC9B,OAAO,CAAC;MAC9B+B,WAAW,GAAGA,WAAW,CAACtE,MAAM,CAACgE,QAAQ,CAAC;;IAE5C,OAAOM,WAAW;EACpB;EAESrC,SAASA,CAAA;IAChB,MAAMC,MAAM,GAAG;MACbK,OAAO,EAAE,IAAI,CAACA,OAAO;MACrBC,iBAAiB,EAAEhE,oBAAoB,CAAC,IAAI,CAACgE,iBAAiB,CAAC;MAC/DE,iBAAiB,EAAEhE,oBAAoB,CAAC,IAAI,CAACgE,iBAAiB,CAAC;MAC/DD,gBAAgB,EAAEvE,mBAAmB,CAAC,IAAI,CAACuE,gBAAgB;KAC5D;IACD,MAAMN,UAAU,GAAG,KAAK,CAACF,SAAS,EAAE;IACpCG,MAAM,CAACC,MAAM,CAACH,MAAM,EAAEC,UAAU,CAAC;IACjC,OAAOD,MAAM;EACf;EAEU,OAAgBf,UAAUA,CAACH,IAAmB;IACtD;IACA,IAAI,EAAE,SAAS,IAAIA,IAAI,CAAC,IAAI,OAAOA,IAAI,CAACuB,OAAO,KAAK,QAAQ,IACxDvB,IAAI,CAACuB,OAAO,GAAG,CAAC,EAAE;MACpB,MAAM,IAAIjE,UAAU,CAChB,+EAAA0B,MAAA,CACW6B,IAAI,CAACC,SAAS,CAACd,IAAI,CAACuB,OAAO,CAAC,CAAE,CAAC;;EAElD;;AAGF,MAAagC,MAAO,SAAQjC,IAAI;EAG9BvB,YAAYC,IAAmB;IAC7B,KAAK,CAAC,CAAC,EAAEA,IAAI,CAAC;IACduD,MAAM,CAACpD,UAAU,CAACH,IAAI,CAAC;EACzB;EAESiB,SAASA,CAAA;IAChB,MAAMC,MAAM,GAAG,KAAK,CAACD,SAAS,EAAE;IAChC,OAAOC,MAAM,CAAC,MAAM,CAAC;IACrB,OAAOA,MAAM;EACf;EAEU,OAAgBf,UAAUA,CAACH,IAAmB;IACtD;IACA,IAAK,OAAOA,IAAI,CAACK,UAAU,KAAK,QAAQ,IACpC,CAACvC,aAAa,CAACkD,uBAAuB,CAClChB,IAAI,CAACK,UAAU,EAAE,QAAQ,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE;MACxC,MAAM,IAAI/C,UAAU,CAChB,mGAAA0B,MAAA,CAC+B6B,IAAI,CAACC,SAAS,CAACd,IAAI,CAACK,UAAU,CAAC,MAAG,CAAC;;EAE1E;;AAtBA;AACOkD,MAAA,CAAAC,SAAS,GAAG,QAAQ;SAFhBD,MAAM;AAyBnB/G,aAAa,CAACiH,aAAa,CAACF,MAAM,CAAC;AAEnC,MAAaG,MAAO,SAAQpC,IAAI;EAG9BvB,YAAYC,IAAmB;IAC7B,KAAK,CAAC,CAAC,EAAEA,IAAI,CAAC;IACd0D,MAAM,CAACvD,UAAU,CAACH,IAAI,CAAC;EACzB;EAESiB,SAASA,CAAA;IAChB,MAAMC,MAAM,GAAG,KAAK,CAACD,SAAS,EAAE;IAChC,OAAOC,MAAM,CAAC,MAAM,CAAC;IACrB,OAAOA,MAAM;EACf;EAEU,OAAgBf,UAAUA,CAACH,IAAmB;IACtD;IACA,IAAI,OAAOA,IAAI,CAACK,UAAU,KAAK,QAAQ,EAAE;MACvC,IAAI,EAAEM,KAAK,CAACC,OAAO,CAACZ,IAAI,CAACK,UAAU,CAAC,KAC7BL,IAAI,CAACK,UAAU,CAAC1B,MAAM,KAAK,CAAC,IAAIqB,IAAI,CAACK,UAAU,CAAC1B,MAAM,KAAK,CAAC,CAAC,CAAC,EAAE;QACrE,MAAM,IAAIrB,UAAU,CAChB,gGAAA0B,MAAA,CAEI6B,IAAI,CAACC,SAAS,CAACd,IAAI,CAACK,UAAU,CAAC,MAAG,CAAC;;;EAGjD;;AAxBA;AACOqD,MAAA,CAAAF,SAAS,GAAG,QAAQ;SAFhBE,MAAM;AA2BnBlH,aAAa,CAACiH,aAAa,CAACC,MAAM,CAAC;AAEnC,MAAaC,eAAgB,SAAQJ,MAAM;EAIzCxD,YAAYC,IAAmB;IAC7B,KAAK,CAACA,IAAI,CAAC;IACX,IAAI,CAACiC,SAAS,GAAG,CAAC,IAAI9E,SAAS,CAAC;MAAC+E,IAAI,EAAE;IAAC,CAAC,CAAC,CAAC;IAE3C,IAAI,IAAI,CAACrD,OAAO,KAAK,MAAM,IAAI,IAAI,CAACA,OAAO,KAAK,OAAO,EAAE;MACvD,MAAM,IAAIvB,UAAU,CAChB,4GAAA0B,MAAA,CAC0C,IAAI,CAACH,OAAO,CAAE,CAAC;;EAEjE;EAES8C,KAAKA,CAACC,UAAyB;IACtCA,UAAU,GAAG7D,kBAAkB,CAAC6D,UAAU,CAAC;IAE3C,IAAIA,UAAU,CAACjD,MAAM,KAAK,CAAC,EAAE;MAC3B,MAAM,IAAIrB,UAAU,CAChB,kDAAkD,GAClDuD,IAAI,CAACC,SAAS,CAACc,UAAU,CAAC,CAAC;;IAGjC,MAAMC,WAAW,GACb,IAAI,CAAC1D,UAAU,KAAK,eAAe,GAAG,CAAC,GAAGyD,UAAU,CAACjD,MAAM,GAAG,CAAC;IACnE,IAAIiD,UAAU,CAACC,WAAW,CAAC,IAAI,IAAI,EAAE;MACnC,MAAM,IAAIvE,UAAU,CAChB,yDAAyD,GACzD,eAAe,CAAC;;IAEtB,MAAMwE,QAAQ,GAAGF,UAAU,CAACC,WAAW,CAAC;IACxC,MAAME,WAAW,GAAG,IAAI,CAAC1B,UAAU,CAACrB,MAAM,CAAC,CAAC,IAAI,CAACuC,OAAO,EAAEO,QAAQ,CAAC,CAAC;IAEpE,IAAI,CAACvD,MAAM,GAAG,IAAI,CAACyD,SAAS,CACxB,QAAQ,EAAED,WAAW,EAAE,SAAS,EAAE,IAAI,CAACP,iBAAiB,EACxD,IAAI,CAACE,iBAAiB,EAAE,IAAI,EAAE,IAAI,CAACD,gBAAgB,CAAC;IACxD,IAAI,IAAI,CAACnB,OAAO,EAAE;MAChB,IAAI,CAAC9B,IAAI,GAAG,IAAI,CAACwD,SAAS,CACtB,MAAM,EAAE,CAAC,IAAI,CAACT,OAAO,CAAC,EAAE,SAAS,EAAE,IAAI,CAAChB,eAAe,EACvD,IAAI,CAACE,eAAe,EAAE,IAAI,EAAE,IAAI,CAACD,cAAc,CAAC;;IAGtD;IACA,IAAI,CAACyB,SAAS,GACV,CAAC,IAAI9E,SAAS,CAAC;MAAC+E,IAAI,EAAE,CAAC;MAAEC,IAAI,EAAE;QAAC,CAACN,WAAW,GAAGC;MAAQ;IAAC,CAAC,CAAC,CAAC;IAC/D,IAAI,CAACM,KAAK,GAAG,IAAI;EACnB;EAESC,IAAIA,CAACC,MAAuB,EAAEC,MAAc;IACnD,OAAOhG,GAAG,CAACE,IAAI,CAAC,MAAK;MACnB,IAAImH,KAAK,GAAG5F,mBAAmB,CAACsE,MAAM,CAAC;MACvC,IAAIsB,KAAK,CAAC7E,KAAK,CAACJ,MAAM,KAAK,CAAC,EAAE;QAC5B,MAAM,IAAIrB,UAAU,CAChB,gGAAA0B,MAAA,CAC6B4E,KAAK,CAAC7E,KAAK,CAACJ,MAAM,CAAE,CAAC;;MAGxD,MAAMiD,UAAU,GAAGgC,KAAK,CAAC7E,KAAK;MAC9B,MAAM8E,SAAS,GAAGjC,UAAU,CAAC,CAAC,CAAC;MAE/B,IAAIkC,KAAa;MACjB,IAAIC,KAAa;MACjB,IAAI,IAAI,CAAC5F,UAAU,KAAK,eAAe,EAAE;QACvC2F,KAAK,GAAG,CAAC;QACTC,KAAK,GAAG,CAAC;OACV,MAAM;QACLD,KAAK,GAAG,CAAC;QACTC,KAAK,GAAG,CAAC;;MAGX,MAAMC,MAAM,GAAGpC,UAAU,CAACkC,KAAK,CAAC;MAChC,MAAMG,KAAK,GAAGrC,UAAU,CAACmC,KAAK,CAAC;MAC/B,MAAMG,OAAO,GAAG,IAAI,CAAC7D,UAAU,CAAC,CAAC,CAAC;MAClC,MAAM8D,OAAO,GAAG,IAAI,CAAC9D,UAAU,CAAC,CAAC,CAAC;MAClC,MAAM+D,OAAO,GAAG,IAAI,CAAC3F,OAAO,CAAC,CAAC,CAAC;MAC/B,MAAM4F,OAAO,GAAG,IAAI,CAAC5F,OAAO,CAAC,CAAC,CAAC;MAE/B;MACA,MAAM6F,SAAS,GAAG1G,YAAY,CAACoG,MAAM,EAAEI,OAAO,EAAEF,OAAO,EAAE,IAAI,CAACrF,OAAO,CAAC;MACtE,MAAM0F,QAAQ,GAAG3G,YAAY,CAACqG,KAAK,EAAEI,OAAO,EAAEF,OAAO,EAAE,IAAI,CAACtF,OAAO,CAAC;MAEpE;MACA;MACA;MACA;MACA,MAAMyE,WAAW,GACb,CAACO,SAAS,EAAES,SAAS,EAAEC,QAAQ,EAAE,IAAI,CAAChD,OAAO,CAAC;MAElD,IAAI,IAAI,CAACpD,UAAU,KAAK,cAAc,EAAE;QACtCyF,KAAK,GAAGrH,GAAG,CAAC6B,SAAS,CAACwF,KAAK,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;;MAE5C,IAAIpB,OAAO,GAAGjG,GAAG,CAACiI,eAAe,CAC7BZ,KAAiB,EAAE,IAAI,CAACrF,MAAM,CAACmE,IAAI,EAAc,EAAEY,WAAW,EAC9D,IAAI,CAAC7E,OAA2B,EAAE,IAAI,CAACI,OAA2B,CAAC;MACvE,IAAI,IAAI,CAACV,UAAU,KAAK,cAAc,EAAE;QACtCqE,OAAO,GAAGjG,GAAG,CAAC6B,SAAS,CAACoE,OAAO,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;;MAGhD,IAAI,IAAI,CAAChE,IAAI,IAAI,IAAI,EAAE;QACrBgE,OAAO,GACH3F,CAAC,CAACsC,OAAO,CAACqD,OAAO,EAAE,IAAI,CAAChE,IAAI,CAACkE,IAAI,EAAE,EAAE,IAAI,CAACvE,UAAU,CAAa;;MAEvE,IAAI,IAAI,CAACmB,UAAU,IAAI,IAAI,EAAE;QAC3BkD,OAAO,GAAG,IAAI,CAAClD,UAAU,CAACwD,KAAK,CAACN,OAAO,CAAa;;MAEtD,OAAOA,OAAO;IAChB,CAAC,CAAC;EACJ;EAESO,kBAAkBA,CAACnB,UAAyB;IACnDA,UAAU,GAAG7D,kBAAkB,CAAC6D,UAAU,CAAC;IAC3C,MAAM0B,WAAW,GAAG1B,UAAU,CAACsB,KAAK,EAAE;IAEtC,IAAIrB,WAAmB;IACvB,IAAI4C,UAAkB;IACtB,IAAIC,SAAiB;IACrB,IAAI,IAAI,CAACvG,UAAU,KAAK,eAAe,EAAE;MACvC0D,WAAW,GAAG,CAAC;MACf4C,UAAU,GAAG,CAAC;MACdC,SAAS,GAAG,CAAC;KACd,MAAM;MACL7C,WAAW,GAAG,CAAC;MACf4C,UAAU,GAAG,CAAC;MACdC,SAAS,GAAG,CAAC;;IAGf,MAAMR,OAAO,GAAG,IAAI,CAAC7D,UAAU,CAAC,CAAC,CAAC;IAClC,MAAM8D,OAAO,GAAG,IAAI,CAAC9D,UAAU,CAAC,CAAC,CAAC;IAClC,MAAM+D,OAAO,GAAG,IAAI,CAAC3F,OAAO,CAAC,CAAC,CAAC;IAC/B,MAAM4F,OAAO,GAAG,IAAI,CAAC5F,OAAO,CAAC,CAAC,CAAC;IAE/B6E,WAAW,CAACzB,WAAW,CAAC,GAAG,IAAI,CAACN,OAAO;IACvC+B,WAAW,CAACmB,UAAU,CAAC,GACnB7G,YAAY,CAAC0F,WAAW,CAACmB,UAAU,CAAC,EAAEL,OAAO,EAAEF,OAAO,EAAE,IAAI,CAACrF,OAAO,CAAC;IACzEyE,WAAW,CAACoB,SAAS,CAAC,GAClB9G,YAAY,CAAC0F,WAAW,CAACoB,SAAS,CAAC,EAAEL,OAAO,EAAEF,OAAO,EAAE,IAAI,CAACtF,OAAO,CAAC;IACxE,OAAOyE,WAAW;EACpB;EAESrC,SAASA,CAAA;IAChB,MAAMC,MAAM,GAAG,KAAK,CAACD,SAAS,EAAE;IAChC,OAAOC,MAAM,CAAC,cAAc,CAAC;IAC7B,OAAOA,MAAM;EACf;;AA/IA;AACgByC,eAAA,CAAAH,SAAS,GAAG,iBAAiB;SAFlCG,eAAe;AAkJ5BnH,aAAa,CAACiH,aAAa,CAACE,eAAe,CAAC;AAE5C,MAAagB,eAAgB,SAAQjB,MAAM;EAIzC3D,YAAYC,IAAmB;IAC7B,KAAK,CAACA,IAAI,CAAC;IACX,IAAI,CAACiC,SAAS,GAAG,CAAC,IAAI9E,SAAS,CAAC;MAAC+E,IAAI,EAAE;IAAC,CAAC,CAAC,CAAC;IAE3C,IAAI,IAAI,CAACrD,OAAO,KAAK,MAAM,IAAI,IAAI,CAACA,OAAO,KAAK,OAAO,EAAE;MACvD,MAAM,IAAIvB,UAAU,CAChB,4GAAA0B,MAAA,CAC0C,IAAI,CAACH,OAAO,CAAE,CAAC;;EAEjE;EAES8C,KAAKA,CAACC,UAAyB;IACtCA,UAAU,GAAG7D,kBAAkB,CAAC6D,UAAU,CAAC;IAE3C,IAAIA,UAAU,CAACjD,MAAM,KAAK,CAAC,EAAE;MAC3B,MAAM,IAAIrB,UAAU,CAChB,kDAAkD,GAClDuD,IAAI,CAACC,SAAS,CAACc,UAAU,CAAC,CAAC;;IAGjC,MAAMC,WAAW,GACb,IAAI,CAAC1D,UAAU,KAAK,eAAe,GAAG,CAAC,GAAGyD,UAAU,CAACjD,MAAM,GAAG,CAAC;IACnE,IAAIiD,UAAU,CAACC,WAAW,CAAC,IAAI,IAAI,EAAE;MACnC,MAAM,IAAIvE,UAAU,CAChB,yDAAyD,GACzD,eAAe,CAAC;;IAEtB,MAAMwE,QAAQ,GAAGF,UAAU,CAACC,WAAW,CAAC;IACxC,MAAME,WAAW,GAAG,IAAI,CAAC1B,UAAU,CAACrB,MAAM,CAAC,CAAC,IAAI,CAACuC,OAAO,EAAEO,QAAQ,CAAC,CAAC;IAEpE,IAAI,CAACvD,MAAM,GAAG,IAAI,CAACyD,SAAS,CACxB,QAAQ,EAAED,WAAW,EAAE,SAAS,EAAE,IAAI,CAACP,iBAAiB,EACxD,IAAI,CAACE,iBAAiB,EAAE,IAAI,EAAE,IAAI,CAACD,gBAAgB,CAAC;IACxD,IAAI,IAAI,CAACnB,OAAO,EAAE;MAChB,IAAI,CAAC9B,IAAI,GAAG,IAAI,CAACwD,SAAS,CACtB,MAAM,EAAE,CAAC,IAAI,CAACT,OAAO,CAAC,EAAE,SAAS,EAAE,IAAI,CAAChB,eAAe,EACvD,IAAI,CAACE,eAAe,EAAE,IAAI,EAAE,IAAI,CAACD,cAAc,CAAC;;IAGtD;IACA,IAAI,CAACyB,SAAS,GACV,CAAC,IAAI9E,SAAS,CAAC;MAAC+E,IAAI,EAAE,CAAC;MAAEC,IAAI,EAAE;QAAC,CAACN,WAAW,GAAGC;MAAQ;IAAC,CAAC,CAAC,CAAC;IAC/D,IAAI,CAACM,KAAK,GAAG,IAAI;EACnB;EAESC,IAAIA,CAACC,MAAuB,EAAEC,MAAc;IACnD,OAAOhG,GAAG,CAACE,IAAI,CAAe,MAAK;MACjC,IAAImH,KAAK,GAAG5F,mBAAmB,CAACsE,MAAM,CAAC;MACvC,IAAIsB,KAAK,CAAC7E,KAAK,CAACJ,MAAM,KAAK,CAAC,EAAE;QAC5B,MAAM,IAAIrB,UAAU,CAChB,gGAAA0B,MAAA,CAC6B4E,KAAK,CAAC7E,KAAK,CAACJ,MAAM,CAAE,CAAC;;MAGxD,MAAMiD,UAAU,GAAGgC,KAAK,CAAC7E,KAAK;MAC9B,MAAM8E,SAAS,GAAGjC,UAAU,CAAC,CAAC,CAAC;MAE/B,IAAIkC,KAAa;MACjB,IAAIC,KAAa;MACjB,IAAIa,KAAa;MAEjB,IAAI,IAAI,CAACzG,UAAU,KAAK,eAAe,EAAE;QACvCyG,KAAK,GAAG,CAAC;QACTd,KAAK,GAAG,CAAC;QACTC,KAAK,GAAG,CAAC;OACV,MAAM;QACLa,KAAK,GAAG,CAAC;QACTd,KAAK,GAAG,CAAC;QACTC,KAAK,GAAG,CAAC;;MAGX,MAAMc,KAAK,GAAGjD,UAAU,CAACgD,KAAK,CAAC;MAC/B,MAAMZ,MAAM,GAAGpC,UAAU,CAACkC,KAAK,CAAC;MAChC,MAAMG,KAAK,GAAGrC,UAAU,CAACmC,KAAK,CAAC;MAC/B,MAAMe,OAAO,GAAG,IAAI,CAACzE,UAAU,CAAC,CAAC,CAAC;MAClC,MAAM6D,OAAO,GAAG,IAAI,CAAC7D,UAAU,CAAC,CAAC,CAAC;MAClC,MAAM8D,OAAO,GAAG,IAAI,CAAC9D,UAAU,CAAC,CAAC,CAAC;MAClC,MAAM0E,OAAO,GAAG,IAAI,CAACtG,OAAO,CAAC,CAAC,CAAC;MAC/B,MAAM2F,OAAO,GAAG,IAAI,CAAC3F,OAAO,CAAC,CAAC,CAAC;MAC/B,MAAM4F,OAAO,GAAG,IAAI,CAAC5F,OAAO,CAAC,CAAC,CAAC;MAE/B;MACA,MAAMuG,QAAQ,GAAGpH,YAAY,CAACiH,KAAK,EAAEE,OAAO,EAAED,OAAO,EAAE,IAAI,CAACjG,OAAO,CAAC;MACpE,MAAMyF,SAAS,GAAG1G,YAAY,CAACoG,MAAM,EAAEI,OAAO,EAAEF,OAAO,EAAE,IAAI,CAACrF,OAAO,CAAC;MACtE,MAAM0F,QAAQ,GAAG3G,YAAY,CAACqG,KAAK,EAAEI,OAAO,EAAEF,OAAO,EAAE,IAAI,CAACtF,OAAO,CAAC;MAEpE;MACA,MAAMyE,WAAW,GACb,CAACO,SAAS,EAAEmB,QAAQ,EAAEV,SAAS,EAAEC,QAAQ,EAAE,IAAI,CAAChD,OAAO,CAAC;MAC5D,IAAI,IAAI,CAACpD,UAAU,KAAK,cAAc,EAAE;QACtCyF,KAAK,GAAGrH,GAAG,CAAC6B,SAAS,CAACwF,KAAK,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;;MAE/C,IAAIpB,OAAO,GAAGjG,GAAG,CAAC0I,eAAe,CAC7BrB,KAAiB,EAAE,IAAI,CAACrF,MAAM,CAACmE,IAAI,EAAc,EAAEY,WAAW,EAC9D,IAAI,CAAC7E,OAAmC,EACxC,IAAI,CAACI,OAA2B,CAAC;MACrC,IAAI,IAAI,CAACV,UAAU,KAAK,cAAc,EAAE;QACtCqE,OAAO,GAAGjG,GAAG,CAAC6B,SAAS,CAACoE,OAAO,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;;MAGnD,IAAI,IAAI,CAAChE,IAAI,KAAK,IAAI,EAAE;QACtBgE,OAAO,GACH3F,CAAC,CAACsC,OAAO,CAACqD,OAAO,EAAE,IAAI,CAAChE,IAAI,CAACkE,IAAI,EAAE,EAAE,IAAI,CAACvE,UAAU,CAAa;;MAEvE,IAAI,IAAI,CAACmB,UAAU,KAAK,IAAI,EAAE;QAC5BkD,OAAO,GAAG,IAAI,CAAClD,UAAU,CAACwD,KAAK,CAACN,OAAO,CAAa;;MAEtD,OAAOA,OAAO;IAChB,CAAC,CAAC;EACJ;EAESO,kBAAkBA,CAACnB,UAAyB;IACnDA,UAAU,GAAG7D,kBAAkB,CAAC6D,UAAU,CAAC;IAC3C,MAAM0B,WAAW,GAAG1B,UAAU,CAACsB,KAAK,EAAE;IAEtC,IAAIrB,WAAmB;IACvB,IAAIqD,SAAiB;IACrB,IAAIT,UAAkB;IACtB,IAAIC,SAAiB;IACrB,IAAI,IAAI,CAACvG,UAAU,KAAK,eAAe,EAAE;MACvC0D,WAAW,GAAG,CAAC;MACfqD,SAAS,GAAG,CAAC;MACbT,UAAU,GAAG,CAAC;MACdC,SAAS,GAAG,CAAC;KACd,MAAM;MACL7C,WAAW,GAAG,CAAC;MACfqD,SAAS,GAAG,CAAC;MACbT,UAAU,GAAG,CAAC;MACdC,SAAS,GAAG,CAAC;;IAGf,MAAMI,OAAO,GAAG,IAAI,CAACzE,UAAU,CAAC,CAAC,CAAC;IAClC,MAAM6D,OAAO,GAAG,IAAI,CAAC7D,UAAU,CAAC,CAAC,CAAC;IAClC,MAAM8D,OAAO,GAAG,IAAI,CAAC9D,UAAU,CAAC,CAAC,CAAC;IAClC,MAAM0E,OAAO,GAAG,IAAI,CAACtG,OAAO,CAAC,CAAC,CAAC;IAC/B,MAAM2F,OAAO,GAAG,IAAI,CAAC3F,OAAO,CAAC,CAAC,CAAC;IAC/B,MAAM4F,OAAO,GAAG,IAAI,CAAC5F,OAAO,CAAC,CAAC,CAAC;IAE/B6E,WAAW,CAACzB,WAAW,CAAC,GAAG,IAAI,CAACN,OAAO;IACvC+B,WAAW,CAAC4B,SAAS,CAAC,GAClBtH,YAAY,CAAC0F,WAAW,CAAC4B,SAAS,CAAC,EAAEH,OAAO,EAAED,OAAO,EAAE,IAAI,CAACjG,OAAO,CAAC;IACxEyE,WAAW,CAACmB,UAAU,CAAC,GACnB7G,YAAY,CAAC0F,WAAW,CAACmB,UAAU,CAAC,EAAEL,OAAO,EAAEF,OAAO,EAAE,IAAI,CAACrF,OAAO,CAAC;IACzEyE,WAAW,CAACoB,SAAS,CAAC,GAClB9G,YAAY,CAAC0F,WAAW,CAACoB,SAAS,CAAC,EAAEL,OAAO,EAAEF,OAAO,EAAE,IAAI,CAACtF,OAAO,CAAC;IACxE,OAAOyE,WAAW;EACpB;EAESrC,SAASA,CAAA;IAChB,MAAMC,MAAM,GAAG,KAAK,CAACD,SAAS,EAAE;IAChC,OAAOC,MAAM,CAAC,cAAc,CAAC;IAC7B,OAAOA,MAAM;EACf;;AA3JA;AACgByD,eAAA,CAAAnB,SAAS,GAAG,iBAAiB;SAFlCmB,eAAe;AA8J5BnI,aAAa,CAACiH,aAAa,CAACkB,eAAe,CAAC;AA0C5C,MAAaQ,aAAc,SAAQ7D,IAAI;EAqBrCvB,YAAYR,IAAY,EAAE2B,MAA+B;IACvD,KAAK,CAAC3B,IAAI,EAAE2B,MAAM,CAAC;IATZ,KAAAkE,6BAA6B,GAClC,eAAe;IACV,KAAAC,6BAA6B,GAClC,eAAe;IAET,KAAAC,eAAe,GAAkB,IAAI;IACrC,KAAAC,eAAe,GAAkB,IAAI;IAK7C,IAAIrE,MAAM,CAACK,OAAO,IAAI,IAAI,EAAE;MAC1B,MAAM,IAAIjE,UAAU,CAChB,kEAAkE,GAClE,qBAAqB,CAAC;;IAE5B,IAAI4D,MAAM,CAACM,iBAAiB,IAAI,IAAI,IAAIN,MAAM,CAACQ,iBAAiB,IAAI,IAAI,IACpER,MAAM,CAACO,gBAAgB,IAAI,IAAI,EAAE;MACnC,MAAM,IAAInE,UAAU,CAChB,mEAAmE,GACnE,6DAA6D,GAC7D,mEAAmE,GACnE,uDAAuD,CAAC;;IAE9D,IAAI4D,MAAM,CAACrC,OAAO,IAAI,IAAI,IAAIqC,MAAM,CAACrC,OAAO,KAAK,MAAM,IACnDqC,MAAM,CAACrC,OAAO,KAAK,OAAO,EAAE;MAC9B,MAAM,IAAIvB,UAAU,CAChB,gBAAA0B,MAAA,CAAgB,IAAI,CAACO,IAAI,2EAAAP,MAAA,CACW6B,IAAI,CAACC,SAAS,CAACI,MAAM,CAACrC,OAAO,CAAC,CAAE,CAAC;;IAG3E,IAAI,CAAC2G,eAAe,GAChBtE,MAAM,CAACsE,eAAe,IAAI,IAAI,GAAG,CAAC,GAAGtE,MAAM,CAACsE,eAAe;IAC/D,IAAI,CAACC,oBAAoB,GAAGlI,cAAc,CACtC2D,MAAM,CAACuE,oBAAoB,IAAI,IAAI,CAACL,6BAA6B,CAAC;IACtE,IAAI,CAACM,oBAAoB,GAAGjI,cAAc,CAACyD,MAAM,CAACwE,oBAAoB,CAAC;IACvE,IAAI,CAACC,mBAAmB,GAAG1I,aAAa,CAACiE,MAAM,CAACyE,mBAAmB,CAAC;IACpE,IAAI,CAACC,oBAAoB,GAAGrI,cAAc,CACtC2D,MAAM,CAACuE,oBAAoB,IAAI,IAAI,CAACJ,6BAA6B,CAAC;IACtE,IAAI,CAACQ,oBAAoB,GAAGpI,cAAc,CAACyD,MAAM,CAAC2E,oBAAoB,CAAC;IACvE,IAAI,CAACC,mBAAmB,GAAG7I,aAAa,CAACiE,MAAM,CAAC4E,mBAAmB,CAAC;EACtE;EAESnE,KAAKA,CAACC,UAAyB;IACtCA,UAAU,GAAG7D,kBAAkB,CAAC6D,UAAU,CAAC;IAC3C,IAAIA,UAAU,CAACjD,MAAM,GAAG,IAAI,CAACY,IAAI,GAAG,CAAC,EAAE;MACrC,MAAM,IAAIjC,UAAU,CAChB,0BAAA0B,MAAA,CAA0B,IAAI,CAACO,IAAI,8BAAAP,MAAA,CAChC,IAAI,CAACO,IAAI,GAAG,CAAC,iCAA8B,MAAAP,MAAA,CAC3C6B,IAAI,CAACC,SAAS,CAACc,UAAU,CAAC,CAAE,CAAC;;IAEtC,MAAMC,WAAW,GACb,IAAI,CAAC1D,UAAU,KAAK,eAAe,GAAG,CAAC,GAAGyD,UAAU,CAACjD,MAAM,GAAG,CAAC;IACnE,IAAIiD,UAAU,CAACC,WAAW,CAAC,IAAI,IAAI,IAAID,UAAU,CAACC,WAAW,CAAC,GAAG,CAAC,EAAE;MAClE,MAAM,IAAIvE,UAAU,CAChB,yEAAA0B,MAAA,CACa6B,IAAI,CAACC,SAAS,CAACc,UAAU,CAACC,WAAW,CAAC,CAAC,CAAE,CAAC;;IAG7D,MAAMC,QAAQ,GAAGF,UAAU,CAACC,WAAW,CAAC;IACxC,MAAMkE,oBAAoB,GACtB,IAAI,CAAC1F,UAAU,CAACrB,MAAM,CAAC,CAAC8C,QAAQ,EAAE,IAAI,CAAC0D,eAAe,CAAC,CAAC;IAC5D,MAAMQ,oBAAoB,GAAG,EAAE;IAC/B,KAAK,IAAI7C,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAAC5D,IAAI,EAAE,EAAE4D,CAAC,EAAE;MAClC6C,oBAAoB,CAAC3C,IAAI,CAAC,CAAC,CAAC;;IAE9B2C,oBAAoB,CAAC3C,IAAI,CAACvB,QAAQ,GAAG,IAAI,CAAC0D,eAAe,EAAE,IAAI,CAACjE,OAAO,CAAC;IAExE,MAAM0E,SAAS,GAAG,IAAI;IACtB,IAAI,CAACX,eAAe,GAAG,IAAI,CAACtD,SAAS,CACjC,kBAAkB,EAAE+D,oBAAoB,EAAE,SAAS,EACnD,IAAI,CAACN,oBAAoB,EAAE,IAAI,CAACC,oBAAoB,EAAEO,SAAS,EAC/D,IAAI,CAACN,mBAAmB,CAAC;IAC7B,IAAI,CAACJ,eAAe,GAAG,IAAI,CAACvD,SAAS,CACjC,kBAAkB,EAAEgE,oBAAoB,EAAE,SAAS,EACnD,IAAI,CAACJ,oBAAoB,EAAE,IAAI,CAACC,oBAAoB,EAAEI,SAAS,EAC/D,IAAI,CAACH,mBAAmB,CAAC;IAC7B,IAAI,IAAI,CAACxF,OAAO,EAAE;MAChB,IAAI,CAAC9B,IAAI,GAAG,IAAI,CAACwD,SAAS,CACtB,MAAM,EAAE,CAAC,IAAI,CAACT,OAAO,CAAC,EAAE,SAAS,EAAE,IAAI,CAAChB,eAAe,EACvD,IAAI,CAACE,eAAe,EAAEwF,SAAS,EAAE,IAAI,CAACzF,cAAc,CAAC;KAC1D,MAAM;MACL,IAAI,CAAChC,IAAI,GAAG,IAAI;;IAGlB,IAAI,CAACyD,SAAS,GACV,CAAC,IAAI9E,SAAS,CAAC;MAAC+E,IAAI,EAAE,IAAI,CAAC3C,IAAI,GAAG,CAAC;MAAE4C,IAAI,EAAE;QAAC,CAACN,WAAW,GAAGC;MAAQ;IAAC,CAAC,CAAC,CAAC;IAC3E,IAAI,CAACM,KAAK,GAAG,IAAI;EACnB;EAESC,IAAIA,CAACC,MAAuB,EAAEC,MAAc;IACnD,OAAO9F,IAAI,CAAC,MAAK;MACf6F,MAAM,GAAGtE,mBAAmB,CAACsE,MAAM,CAAC;MAEpC,IAAI4D,MAAc;MAClB,IAAI,IAAI,CAAC3G,IAAI,KAAK,CAAC,EAAE;QACnB,MAAM,IAAIlC,mBAAmB,CACzB,kDAAkD,CAAC;OACxD,MAAM,IAAI,IAAI,CAACkC,IAAI,KAAK,CAAC,EAAE;QAC1B,IAAI,IAAI,CAACpB,UAAU,KAAK,eAAe,EAAE;UACvCmE,MAAM,GAAG/F,GAAG,CAAC6B,SAAS,CAACkE,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAE;;QAGjD4D,MAAM,GAAG3J,GAAG,CAAC4J,eAAe,CACxB7D,MAAkB,EAAE,IAAI,CAACgD,eAAe,CAAC5C,IAAI,EAAc,EAC3D,IAAI,CAAC6C,eAAe,CAAC7C,IAAI,EAAc,EACvC,IAAI,CAACjE,OAA2B,EAAE,IAAI,CAACI,OAA2B,EAClE,IAAI,CAACC,YAAgC,EAAE,MAAM,CAAC;;MAGpD,IAAI,IAAI,CAACwB,OAAO,EAAE;QAChB4F,MAAM,GAAGrJ,CAAC,CAACsC,OAAO,CAAC+G,MAAM,EAAE,IAAI,CAAC1H,IAAI,CAACkE,IAAI,EAAE,EAAE,IAAI,CAACvE,UAAU,CAAC;;MAE/D,IAAI,IAAI,CAACmB,UAAU,IAAI,IAAI,EAAE;QAC3B4G,MAAM,GAAG,IAAI,CAAC5G,UAAU,CAACwD,KAAK,CAACoD,MAAM,CAAC;;MAGxC,IAAI,IAAI,CAAC/H,UAAU,KAAK,eAAe,EAAE;QACvC+H,MAAM,GAAG3J,GAAG,CAAC6B,SAAS,CAAC8H,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAE;;MAEjD,OAAOA,MAAM;IACf,CAAC,CAAC;EACJ;EAESjF,SAASA,CAAA;IAChB,MAAMC,MAAM,GAAG,KAAK,CAACD,SAAS,EAAE;IAChC,OAAOC,MAAM,CAAC,MAAM,CAAC;IACrB,OAAOA,MAAM,CAAC,mBAAmB,CAAC;IAClC,OAAOA,MAAM,CAAC,mBAAmB,CAAC;IAClC,OAAOA,MAAM,CAAC,kBAAkB,CAAC;IACjCA,MAAM,CAAC,sBAAsB,CAAC,GAC1B1D,oBAAoB,CAAC,IAAI,CAACiI,oBAAoB,CAAC;IACnDvE,MAAM,CAAC,sBAAsB,CAAC,GAC1B1D,oBAAoB,CAAC,IAAI,CAACoI,oBAAoB,CAAC;IACnD1E,MAAM,CAAC,sBAAsB,CAAC,GAC1BxD,oBAAoB,CAAC,IAAI,CAACgI,oBAAoB,CAAC;IACnDxE,MAAM,CAAC,sBAAsB,CAAC,GAC1BxD,oBAAoB,CAAC,IAAI,CAACmI,oBAAoB,CAAC;IACnD3E,MAAM,CAAC,qBAAqB,CAAC,GACzBhE,mBAAmB,CAAC,IAAI,CAACyI,mBAAmB,CAAC;IACjDzE,MAAM,CAAC,qBAAqB,CAAC,GACzBhE,mBAAmB,CAAC,IAAI,CAAC4I,mBAAmB,CAAC;IACjD,OAAO5E,MAAM;EACf;;AA3JA;AACOiE,aAAA,CAAA3B,SAAS,GAAG,eAAe;SAFvB2B,aAAa;AA+J1B,MAAaiB,eAAgB,SAAQjB,aAAa;EAGhDpF,YAAYC,IAA6B;IACvC,KAAK,CAAC,CAAC,EAAEA,IAAI,CAAC;EAChB;;AAJA;AACgBoG,eAAA,CAAA5C,SAAS,GAAG,iBAAiB;SAFlC4C,eAAe;AAO5B5J,aAAa,CAACiH,aAAa,CAAC2C,eAAe,CAAC;AAE5C,MAAaC,MAAO,SAAQ/E,IAAI;EAG9BvB,YAAYC,IAAmB;IAC7B,KAAK,CAAC,CAAC,EAAEA,IAAI,CAAC;IACdqG,MAAM,CAAClG,UAAU,CAACH,IAAI,CAAC;IACvB,IAAI,CAACiC,SAAS,GAAG,CAAC;MAACC,IAAI,EAAE;IAAC,CAAC,CAAC;EAC9B;EAESjB,SAASA,CAAA;IAChB,MAAMC,MAAM,GAAG,KAAK,CAACD,SAAS,EAAE;IAChC,OAAOC,MAAM,CAAC,MAAM,CAAC;IACrB,OAAOA,MAAM,CAAC,YAAY,CAAC;IAC3B,OAAOA,MAAM;EACf;EAEU,OAAgBf,UAAUA,CAACH,IAAmB;IACtD;IACA,IAAI,OAAOA,IAAI,CAACK,UAAU,KAAK,QAAQ,IACnC,CAACvC,aAAa,CAACkD,uBAAuB,CAClChB,IAAI,CAACK,UAAU,EAAE,QAAQ,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE;MACxC,MAAM,IAAI/C,UAAU,CAChB,8FAAA0B,MAAA,CAC0B6B,IAAI,CAACC,SAAS,CAACd,IAAI,CAACK,UAAU,CAAC,MAAG,CAAC;;EAErE;;AAxBA;AACOgG,MAAA,CAAA7C,SAAS,GAAG,QAAQ;SAFhB6C,MAAM;AA2BnB7J,aAAa,CAACiH,aAAa,CAAC4C,MAAM,CAAC;AAgCnC,MAAaC,UAAW,SAAQlJ,KAAK;EAMnC2C,YAAYC,IAAyB;IACnC,KAAK,CAACA,IAAI,CAAC;IACX,IAAI,OAAOA,IAAI,CAACuG,QAAQ,KAAK,QAAQ,EAAE;MACrC,IAAI,CAACA,QAAQ,GACT,CAAC,CAACvG,IAAI,CAACuG,QAAQ,EAAEvG,IAAI,CAACuG,QAAQ,CAAC,EAAE,CAACvG,IAAI,CAACuG,QAAQ,EAAEvG,IAAI,CAACuG,QAAQ,CAAC,CAAC;KACrE,MAAM,IAAI,OAAOvG,IAAI,CAACuG,QAAQ,CAAC,CAAC,CAAC,KAAK,QAAQ,EAAE;MAC/C,IAAI,CAACA,QAAQ,GAAG,CACd,CAACvG,IAAI,CAACuG,QAAQ,CAAC,CAAC,CAAC,EAAEvG,IAAI,CAACuG,QAAQ,CAAC,CAAC,CAAC,CAAC,EACpC,CAACvG,IAAI,CAACuG,QAAQ,CAAC,CAAC,CAAW,EAAEvG,IAAI,CAACuG,QAAQ,CAAC,CAAC,CAAW,CAAC,CACzD;KACF,MAAM;MACL,IAAI,CAACA,QAAQ,GAAGvG,IAAI,CAACuG,QAAgD;;IAEvE,IAAI,CAACpI,UAAU,GACX6B,IAAI,CAAC7B,UAAU,KAAKS,SAAS,GAAG,cAAc,GAAGoB,IAAI,CAAC7B,UAAU;IACpE,IAAI,CAAC8D,SAAS,GAAG,CAAC;MAACC,IAAI,EAAE;IAAC,CAAC,CAAC;EAC9B;EAESa,kBAAkBA,CAACnB,UAAiB;IAC3C,IAAI,IAAI,CAACzD,UAAU,KAAK,eAAe,EAAE;MACvC,OAAO,CACLyD,UAAU,CAAC,CAAC,CAAC,EAAEA,UAAU,CAAC,CAAC,CAAC,EAC5BA,UAAU,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC2E,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,CAACA,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EACzD3E,UAAU,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC2E,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,CAACA,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAC1D;KACF,MAAM;MACL,OAAO,CACL3E,UAAU,CAAC,CAAC,CAAC,EACbA,UAAU,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC2E,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,CAACA,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EACzD3E,UAAU,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC2E,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,CAACA,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE3E,UAAU,CAAC,CAAC,CAAC,CACzE;;EAEL;EAESS,IAAIA,CAACC,MAAuB,EAAEC,MAAc;IACnD,OAAO9F,IAAI,CAAC,MAAK;MACf6F,MAAM,GAAGtE,mBAAmB,CAACsE,MAAM,CAAC;MAEpC,IAAI,IAAI,CAACnE,UAAU,KAAK,cAAc,EAAE;QACtC,MAAMqI,OAAO,GAAG3J,CAAC,CAAC4J,cAAc,CAC5BnE,MAAM,EAAE,IAAI,CAACiE,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAC3BjE,MAAM,CAACvD,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI,CAACwH,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,CAACA,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;QACnE,OAAO1J,CAAC,CAAC4J,cAAc,CACnBD,OAAO,EAAE,IAAI,CAACD,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAC5BjE,MAAM,CAACvD,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI,CAACwH,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,CAACA,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;OACpE,MAAM;QACL,MAAMC,OAAO,GAAG3J,CAAC,CAAC4J,cAAc,CAC5BnE,MAAM,EAAE,IAAI,CAACiE,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAC3BjE,MAAM,CAACvD,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI,CAACwH,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,CAACA,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;QACnE,OAAO1J,CAAC,CAAC4J,cAAc,CACnBD,OAAO,EAAE,IAAI,CAACD,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAC5BjE,MAAM,CAACvD,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI,CAACwH,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,CAACA,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;;IAEvE,CAAC,CAAC;EACJ;EAEStF,SAASA,CAAA;IAChB,MAAMC,MAAM,GAAG;MAACqF,QAAQ,EAAE,IAAI,CAACA,QAAQ;MAAEpI,UAAU,EAAE,IAAI,CAACA;IAAU,CAAC;IACrE,MAAMgD,UAAU,GAAG,KAAK,CAACF,SAAS,EAAE;IACpCG,MAAM,CAACC,MAAM,CAACH,MAAM,EAAEC,UAAU,CAAC;IACjC,OAAOD,MAAM;EACf;;AAlEA;AACOoF,UAAA,CAAA9C,SAAS,GAAG,YAAY;SAFpB8C,UAAU;AAqEvB9J,aAAa,CAACiH,aAAa,CAAC6C,UAAU,CAAC;AA6BvC,MAAaI,YAAa,SAAQtJ,KAAK;EAQrC2C,YAAYC,IAA2B;IACrC,KAAK,CAACA,IAAI,CAAC;IANM,KAAA2G,YAAY,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;IAOtC,IAAI,CAAC1E,SAAS,GAAG,CAAC;MAACC,IAAI,EAAE;IAAC,CAAC,CAAC;IAC5B,IAAI,CAAC0E,IAAI,GAAG5G,IAAI,CAAC4G,IAAI,IAAI,IAAI,GAAG,IAAI,CAACD,YAAY,GAAG3G,IAAI,CAAC4G,IAAI;IAC7D,IAAI,CAACzI,UAAU,GACX6B,IAAI,CAAC7B,UAAU,IAAI,IAAI,GAAG,cAAc,GAAG6B,IAAI,CAAC7B,UAAU;IAC9DrB,eAAe,CAAC,IAAI,CAACqB,UAAU,CAAC;IAChC,IAAI,CAAC0I,aAAa,GACd7G,IAAI,CAAC6G,aAAa,IAAI,IAAI,GAAG,SAAS,GAAG7G,IAAI,CAAC6G,aAAa;IAC/D9J,wBAAwB,CAAC,IAAI,CAAC8J,aAAa,CAAC;EAC9C;EAES9D,kBAAkBA,CAACnB,UAAiB;IAC3C,IAAI,IAAI,CAACzD,UAAU,KAAK,eAAe,EAAE;MACvC,MAAM6F,MAAM,GACRpC,UAAU,CAAC,CAAC,CAAC,IAAI,IAAI,GAAG,IAAI,GAAG,IAAI,CAACgF,IAAI,CAAC,CAAC,CAAC,GAAGhF,UAAU,CAAC,CAAC,CAAC;MAC/D,MAAMqC,KAAK,GAAGrC,UAAU,CAAC,CAAC,CAAC,IAAI,IAAI,GAAG,IAAI,GAAG,IAAI,CAACgF,IAAI,CAAC,CAAC,CAAC,GAAGhF,UAAU,CAAC,CAAC,CAAC;MACzE,OAAO,CAACA,UAAU,CAAC,CAAC,CAAC,EAAEA,UAAU,CAAC,CAAC,CAAC,EAAEoC,MAAM,EAAEC,KAAK,CAAC;KACrD,MAAM;MACL,MAAMD,MAAM,GACRpC,UAAU,CAAC,CAAC,CAAC,IAAI,IAAI,GAAG,IAAI,GAAG,IAAI,CAACgF,IAAI,CAAC,CAAC,CAAC,GAAGhF,UAAU,CAAC,CAAC,CAAC;MAC/D,MAAMqC,KAAK,GAAGrC,UAAU,CAAC,CAAC,CAAC,IAAI,IAAI,GAAG,IAAI,GAAG,IAAI,CAACgF,IAAI,CAAC,CAAC,CAAC,GAAGhF,UAAU,CAAC,CAAC,CAAC;MACzE,OAAO,CAACA,UAAU,CAAC,CAAC,CAAC,EAAEoC,MAAM,EAAEC,KAAK,EAAErC,UAAU,CAAC,CAAC,CAAC,CAAC;;EAExD;EAESS,IAAIA,CAACC,MAAuB,EAAEC,MAAc;IACnD,OAAOhG,GAAG,CAACE,IAAI,CAAC,MAAK;MACnB,IAAImH,KAAK,GAAG5F,mBAAmB,CAACsE,MAAM,CAAa;MACnD,MAAMV,UAAU,GAAGgC,KAAK,CAAC7E,KAAK;MAE9B,IAAI,IAAI,CAACZ,UAAU,KAAK,eAAe,EAAE;QACvCyF,KAAK,GAAGrH,GAAG,CAAC6B,SAAS,CAACwF,KAAK,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QAC1C,MAAMI,MAAM,GAAG,IAAI,CAAC4C,IAAI,CAAC,CAAC,CAAC,GAAGhF,UAAU,CAAC,CAAC,CAAC;QAC3C,MAAMqC,KAAK,GAAG,IAAI,CAAC2C,IAAI,CAAC,CAAC,CAAC,GAAGhF,UAAU,CAAC,CAAC,CAAC;QAE1C,MAAMkF,OAAO,GAAG,IAAI,CAACD,aAAa,KAAK,SAAS,GAC5CtK,GAAG,CAACwK,KAAK,CAACC,qBAAqB,CAACpD,KAAK,EAAE,CAACI,MAAM,EAAEC,KAAK,CAAC,CAAC,GACvD1H,GAAG,CAACwK,KAAK,CAACE,cAAc,CAACrD,KAAK,EAAE,CAACI,MAAM,EAAEC,KAAK,CAAC,CAAC;QACpD,OAAO1H,GAAG,CAAC6B,SAAS,CAAC0I,OAAO,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;OAC5C,MAAM;QACL,MAAM9C,MAAM,GAAG,IAAI,CAAC4C,IAAI,CAAC,CAAC,CAAC,GAAGhF,UAAU,CAAC,CAAC,CAAC;QAC3C,MAAMqC,KAAK,GAAG,IAAI,CAAC2C,IAAI,CAAC,CAAC,CAAC,GAAGhF,UAAU,CAAC,CAAC,CAAC;QAC1C,OAAO,IAAI,CAACiF,aAAa,KAAK,SAAS,GACnCtK,GAAG,CAACwK,KAAK,CAACC,qBAAqB,CAACpD,KAAK,EAAE,CAACI,MAAM,EAAEC,KAAK,CAAC,CAAC,GACvD1H,GAAG,CAACwK,KAAK,CAACE,cAAc,CAACrD,KAAK,EAAE,CAACI,MAAM,EAAEC,KAAK,CAAC,CAAC;;IAExD,CAAC,CAAC;EACJ;EAEShD,SAASA,CAAA;IAChB,MAAMC,MAAM,GAAG;MACX0F,IAAI,EAAE,IAAI,CAACA,IAAI;MACfzI,UAAU,EAAE,IAAI,CAACA,UAAU;MAC3B0I,aAAa,EAAE,IAAI,CAACA;KACvB;IACD,MAAM1F,UAAU,GAAG,KAAK,CAACF,SAAS,EAAE;IACpCG,MAAM,CAACC,MAAM,CAACH,MAAM,EAAEC,UAAU,CAAC;IACjC,OAAOD,MAAM;EACf;;AAlEA;AACOwF,YAAA,CAAAlD,SAAS,GAAG,cAAc;SAFtBkD,YAAY;AAqEzBlK,aAAa,CAACiH,aAAa,CAACiD,YAAY,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}