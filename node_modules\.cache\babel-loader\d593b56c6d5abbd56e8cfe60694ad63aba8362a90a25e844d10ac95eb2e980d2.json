{"ast": null, "code": "import { convertToTensor } from '../tensor_util_env';\nimport { conv3DBackpropInput } from './conv3d_backprop_input';\nimport { op } from './operation';\n/**\n * Computes the transposed 3D convolution of a volume, also known as a\n * deconvolution.\n *\n * @param x The input image, of rank 5 or rank 4, of shape\n *   `[batch, depth, height, width, inDepth]`. If rank 4, batch of 1 is assumed.\n * @param filter The filter, rank 4, of shape\n *     `[depth, filterHeight, filterWidth, outDepth, inDepth]`.\n *     `inDepth` must match `inDepth` in `x`.\n * @param outputShape Output shape, of rank 5 or rank 4:\n *     `[batch, depth, height, width, outDepth]`. If rank 3, batch of 1 is\n *    assumed.\n * @param strides The strides of the original convolution:\n *     `[strideDepth, strideHeight, strideWidth]`.\n * @param pad  The type of padding algorithm used in the non-transpose version\n *    of the op.\n *\n * @doc {heading: 'Operations', subheading: 'Convolution'}\n */\nfunction conv3dTranspose_(x, filter, outputShape, strides, pad) {\n  const $x = convertToTensor(x, 'x', 'conv3dTranspose');\n  const $filter = convertToTensor(filter, 'filter', 'conv3dTranspose');\n  return conv3DBackpropInput(outputShape, $x, $filter, strides, pad);\n}\nexport const conv3dTranspose = /* @__PURE__ */op({\n  conv3dTranspose_\n});", "map": {"version": 3, "names": ["convertToTensor", "conv3DBackpropInput", "op", "conv3dTranspose_", "x", "filter", "outputShape", "strides", "pad", "$x", "$filter", "conv3dTranspose"], "sources": ["C:\\tfjs-core\\src\\ops\\conv3d_transpose.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport {Tensor4D, Tensor5D} from '../tensor';\nimport {convertToTensor} from '../tensor_util_env';\nimport {TensorLike} from '../types';\n\nimport {conv3DBackpropInput} from './conv3d_backprop_input';\nimport {op} from './operation';\n\n/**\n * Computes the transposed 3D convolution of a volume, also known as a\n * deconvolution.\n *\n * @param x The input image, of rank 5 or rank 4, of shape\n *   `[batch, depth, height, width, inDepth]`. If rank 4, batch of 1 is assumed.\n * @param filter The filter, rank 4, of shape\n *     `[depth, filterHeight, filterWidth, outDepth, inDepth]`.\n *     `inDepth` must match `inDepth` in `x`.\n * @param outputShape Output shape, of rank 5 or rank 4:\n *     `[batch, depth, height, width, outDepth]`. If rank 3, batch of 1 is\n *    assumed.\n * @param strides The strides of the original convolution:\n *     `[strideDepth, strideHeight, strideWidth]`.\n * @param pad  The type of padding algorithm used in the non-transpose version\n *    of the op.\n *\n * @doc {heading: 'Operations', subheading: 'Convolution'}\n */\nfunction conv3dTranspose_<T extends Tensor4D|Tensor5D>(\n    x: T|TensorLike, filter: Tensor5D|TensorLike,\n    outputShape:\n        [number, number, number, number,\n         number]|[number, number, number, number],\n    strides: [number, number, number]|number, pad: 'valid'|'same'): T {\n  const $x = convertToTensor(x, 'x', 'conv3dTranspose');\n  const $filter = convertToTensor(filter, 'filter', 'conv3dTranspose');\n\n  return conv3DBackpropInput(outputShape, $x, $filter, strides, pad);\n}\n\nexport const conv3dTranspose = /* @__PURE__ */ op({conv3dTranspose_});\n"], "mappings": "AAiBA,SAAQA,eAAe,QAAO,oBAAoB;AAGlD,SAAQC,mBAAmB,QAAO,yBAAyB;AAC3D,SAAQC,EAAE,QAAO,aAAa;AAE9B;;;;;;;;;;;;;;;;;;;AAmBA,SAASC,gBAAgBA,CACrBC,CAAe,EAAEC,MAA2B,EAC5CC,WAE6C,EAC7CC,OAAwC,EAAEC,GAAmB;EAC/D,MAAMC,EAAE,GAAGT,eAAe,CAACI,CAAC,EAAE,GAAG,EAAE,iBAAiB,CAAC;EACrD,MAAMM,OAAO,GAAGV,eAAe,CAACK,MAAM,EAAE,QAAQ,EAAE,iBAAiB,CAAC;EAEpE,OAAOJ,mBAAmB,CAACK,WAAW,EAAEG,EAAE,EAAEC,OAAO,EAAEH,OAAO,EAAEC,GAAG,CAAC;AACpE;AAEA,OAAO,MAAMG,eAAe,GAAG,eAAgBT,EAAE,CAAC;EAACC;AAAgB,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}