{"ast": null, "code": "/*\r\nCopyright (c) 2012, <PERSON>, <PERSON>\r\n\r\nPermission is hereby granted, free of charge, to any person obtaining a copy\r\nof this software and associated documentation files (the \"Software\"), to deal\r\nin the Software without restriction, including without limitation the rights\r\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\r\ncopies of the Software, and to permit persons to whom the Software is\r\nfurnished to do so, subject to the following conditions:\r\n\r\nThe above copyright notice and this permission notice shall be included in\r\nall copies or substantial portions of the Software.\r\n\r\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\r\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\r\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\r\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\r\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\r\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\r\nTHE SOFTWARE.\r\n*/\n\n'use strict';\n\nconst Stemmer = require('./stemmer_it');\nconst PorterStemmer = new Stemmer();\nmodule.exports = PorterStemmer;\nfunction isVowel(letter) {\n  return letter === 'a' || letter === 'e' || letter === 'i' || letter === 'o' || letter === 'u' || letter === 'à' || letter === 'è' || letter === 'ì' || letter === 'ò' || letter === 'ù';\n}\nfunction getNextVowelPos(token, start) {\n  start = start + 1;\n  const length = token.length;\n  for (let i = start; i < length; i++) {\n    if (isVowel(token[i])) {\n      return i;\n    }\n  }\n  return length;\n}\nfunction getNextConsonantPos(token, start) {\n  const length = token.length;\n  for (let i = start; i < length; i++) {\n    if (!isVowel(token[i])) return i;\n  }\n  return length;\n}\nfunction endsin(token, suffix) {\n  if (token.length < suffix.length) return false;\n  return token.slice(-suffix.length) === suffix;\n}\nfunction endsinArr(token, suffixes) {\n  for (let i = 0; i < suffixes.length; i++) {\n    if (endsin(token, suffixes[i])) return suffixes[i];\n  }\n  return '';\n}\nfunction replaceAcute(token) {\n  let str = token.replace(/á/gi, 'à');\n  str = str.replace(/é/gi, 'è');\n  str = str.replace(/í/gi, 'ì');\n  str = str.replace(/ó/gi, 'ò');\n  str = str.replace(/ú/gi, 'ù');\n  return str;\n}\nfunction vowelMarking(token) {\n  function replacer(match, p1, p2, p3) {\n    return p1 + p2.toUpperCase() + p3;\n  }\n  const str = token.replace(/([aeiou])(i|u)([aeiou])/g, replacer);\n  return str;\n}\n\n// perform full stemming algorithm on a single word\nPorterStemmer.stem = function (token) {\n  token = token.toLowerCase();\n  token = replaceAcute(token);\n  token = token.replace(/qu/g, 'qU');\n  token = vowelMarking(token);\n  if (token.length < 3) {\n    return token;\n  }\n  let r1 = token.length;\n  let r2 = token.length;\n  let rv = token.length;\n  const len = token.length;\n  // R1 is the region after the first non-vowel following a vowel,\n  for (let i = 0; i < token.length - 1 && r1 === len; i++) {\n    if (isVowel(token[i]) && !isVowel(token[i + 1])) {\n      r1 = i + 2;\n    }\n  }\n  // Or is the null region at the end of the word if there is no such non-vowel.\n\n  // R2 is the region after the first non-vowel following a vowel in R1\n  for (let i = r1; i < token.length - 1 && r2 === len; i++) {\n    if (isVowel(token[i]) && !isVowel(token[i + 1])) {\n      r2 = i + 2;\n    }\n  }\n\n  // Or is the null region at the end of the word if there is no such non-vowel.\n\n  // If the second letter is a consonant, RV is the region after the next following vowel,\n\n  // RV as follow\n\n  if (len > 3) {\n    if (!isVowel(token[1])) {\n      // If the second letter is a consonant, RV is the region after the next following vowel\n      rv = getNextVowelPos(token, 1) + 1;\n    } else if (isVowel(token[0]) && isVowel(token[1])) {\n      // or if the first two letters are vowels, RV is the region after the next consonant\n      rv = getNextConsonantPos(token, 2) + 1;\n    } else {\n      // otherwise (consonant-vowel case) RV is the region after the third letter. But RV is the end of the word if these positions cannot be found.\n      rv = 3;\n    }\n  }\n  let r1txt = token.substring(r1);\n  let r2txt = token.substring(r2);\n  let rvtxt = token.substring(rv);\n  const tokenOrig = token;\n\n  // Step 0: Attached pronoun\n\n  const pronounSuf = ['glieli', 'glielo', 'gliene', 'gliela', 'gliele', 'sene', 'tene', 'cela', 'cele', 'celi', 'celo', 'cene', 'vela', 'vele', 'veli', 'velo', 'vene', 'mela', 'mele', 'meli', 'melo', 'mene', 'tela', 'tele', 'teli', 'telo', 'gli', 'ci', 'la', 'le', 'li', 'lo', 'mi', 'ne', 'si', 'ti', 'vi'];\n  const pronounSufPre1 = ['ando', 'endo'];\n  const pronounSufPre2 = ['ar', 'er', 'ir'];\n  let suf = endsinArr(token, pronounSuf);\n  if (suf !== '') {\n    const preSuff1 = endsinArr(rvtxt.slice(0, -suf.length), pronounSufPre1);\n    const preSuff2 = endsinArr(rvtxt.slice(0, -suf.length), pronounSufPre2);\n    if (preSuff1 !== '') {\n      token = token.slice(0, -suf.length);\n    }\n    if (preSuff2 !== '') {\n      token = token.slice(0, -suf.length) + 'e';\n    }\n  }\n  if (token !== tokenOrig) {\n    r1txt = token.substring(r1);\n    r2txt = token.substring(r2);\n    rvtxt = token.substring(rv);\n  }\n  const tokenAfter0 = token;\n\n  // Step 1:  Standard suffix removal\n\n  if ((suf = endsinArr(r2txt, ['ativamente', 'abilamente', 'ivamente', 'osamente', 'icamente'])) !== '') {\n    token = token.slice(0, -suf.length); // delete\n  } else if ((suf = endsinArr(r2txt, ['icazione', 'icazioni', 'icatore', 'icatori', 'azione', 'azioni', 'atore', 'atori'])) !== '') {\n    token = token.slice(0, -suf.length); // delete\n  } else if ((suf = endsinArr(r2txt, ['logia', 'logie'])) !== '') {\n    token = token.slice(0, -suf.length) + 'log'; // replace with log\n  } else if ((suf = endsinArr(r2txt, ['uzione', 'uzioni', 'usione', 'usioni'])) !== '') {\n    token = token.slice(0, -suf.length) + 'u'; // replace with u\n  } else if ((suf = endsinArr(r2txt, ['enza', 'enze'])) !== '') {\n    token = token.slice(0, -suf.length) + 'ente'; // replace with ente\n  } else if ((suf = endsinArr(rvtxt, ['amento', 'amenti', 'imento', 'imenti'])) !== '') {\n    token = token.slice(0, -suf.length); // delete\n  } else if ((suf = endsinArr(r1txt, ['amente'])) !== '') {\n    token = token.slice(0, -suf.length); // delete\n  } else if ((suf = endsinArr(r2txt, ['atrice', 'atrici', 'abile', 'abili', 'ibile', 'ibili', 'mente', 'ante', 'anti', 'anza', 'anze', 'iche', 'ichi', 'ismo', 'ismi', 'ista', 'iste', 'isti', 'istà', 'istè', 'istì', 'ico', 'ici', 'ica', 'ice', 'oso', 'osi', 'osa', 'ose'])) !== '') {\n    token = token.slice(0, -suf.length); // delete\n  } else if ((suf = endsinArr(r2txt, ['abilità', 'icità', 'ività', 'ità'])) !== '') {\n    token = token.slice(0, -suf.length); // delete\n  } else if ((suf = endsinArr(r2txt, ['icativa', 'icativo', 'icativi', 'icative', 'ativa', 'ativo', 'ativi', 'ative', 'iva', 'ivo', 'ivi', 'ive'])) !== '') {\n    token = token.slice(0, -suf.length);\n  }\n  if (token !== tokenAfter0) {\n    r1txt = token.substring(r1);\n    r2txt = token.substring(r2);\n    rvtxt = token.substring(rv);\n  }\n  const tokenAfter1 = token;\n\n  // Step 2:  Verb suffixes\n\n  if (tokenAfter0 === tokenAfter1) {\n    if ((suf = endsinArr(rvtxt, ['erebbero', 'irebbero', 'assero', 'assimo', 'eranno', 'erebbe', 'eremmo', 'ereste', 'eresti', 'essero', 'iranno', 'irebbe', 'iremmo', 'ireste', 'iresti', 'iscano', 'iscono', 'issero', 'arono', 'avamo', 'avano', 'avate', 'eremo', 'erete', 'erono', 'evamo', 'evano', 'evate', 'iremo', 'irete', 'irono', 'ivamo', 'ivano', 'ivate', 'ammo', 'ando', 'asse', 'assi', 'emmo', 'enda', 'ende', 'endi', 'endo', 'erai', 'Yamo', 'iamo', 'immo', 'irai', 'irei', 'isca', 'isce', 'isci', 'isco', 'erei', 'uti', 'uto', 'ita', 'ite', 'iti', 'ito', 'iva', 'ivi', 'ivo', 'ono', 'uta', 'ute', 'ano', 'are', 'ata', 'ate', 'ati', 'ato', 'ava', 'avi', 'avo', 'erà', 'ere', 'erò', 'ete', 'eva', 'evi', 'evo', 'irà', 'ire', 'irò', 'ar', 'ir'])) !== '') {\n      token = token.slice(0, -suf.length);\n    }\n  }\n  r1txt = token.substring(r1);\n  r2txt = token.substring(r2);\n  rvtxt = token.substring(rv);\n\n  // Always do step 3.\n\n  if ((suf = endsinArr(rvtxt, ['ia', 'ie', 'ii', 'io', 'ià', 'iè', 'iì', 'iò', 'a', 'e', 'i', 'o', 'à', 'è', 'ì', 'ò'])) !== '') {\n    token = token.slice(0, -suf.length);\n  }\n  r1txt = token.substring(r1);\n  r2txt = token.substring(r2);\n  rvtxt = token.substring(rv);\n  if ((suf = endsinArr(rvtxt, ['ch'])) !== '') {\n    token = token.slice(0, -suf.length) + 'c'; // replace with c\n  } else if ((suf = endsinArr(rvtxt, ['gh'])) !== '') {\n    token = token.slice(0, -suf.length) + 'g'; // replace with g\n  }\n  r1txt = token.substring(r1);\n  r2txt = token.substring(r2);\n  rvtxt = token.substring(rv);\n  return token.toLowerCase();\n};", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON>", "require", "PorterStemmer", "module", "exports", "isVowel", "letter", "getNextVowelPos", "token", "start", "length", "i", "getNextConsonantPos", "endsin", "suffix", "slice", "endsinArr", "suffixes", "replaceAcute", "str", "replace", "vowelMarking", "replacer", "match", "p1", "p2", "p3", "toUpperCase", "stem", "toLowerCase", "r1", "r2", "rv", "len", "r1txt", "substring", "r2txt", "rvtxt", "tokenOrig", "pronounSuf", "pronounSufPre1", "pronounSufPre2", "suf", "preSuff1", "preSuff2", "tokenAfter0", "tokenAfter1"], "sources": ["C:/tmsft/node_modules/natural/lib/natural/stemmers/porter_stemmer_it.js"], "sourcesContent": ["/*\r\nCopyright (c) 2012, <PERSON>, <PERSON>\r\n\r\nPermission is hereby granted, free of charge, to any person obtaining a copy\r\nof this software and associated documentation files (the \"Software\"), to deal\r\nin the Software without restriction, including without limitation the rights\r\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\r\ncopies of the Software, and to permit persons to whom the Software is\r\nfurnished to do so, subject to the following conditions:\r\n\r\nThe above copyright notice and this permission notice shall be included in\r\nall copies or substantial portions of the Software.\r\n\r\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\r\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\r\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\r\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\r\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\r\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\r\nTHE SOFTWARE.\r\n*/\r\n\r\n'use strict'\r\n\r\nconst Stemmer = require('./stemmer_it')\r\n\r\nconst PorterStemmer = new Stemmer()\r\nmodule.exports = PorterStemmer\r\n\r\nfunction isVowel (letter) {\r\n  return (letter === 'a' || letter === 'e' || letter === 'i' || letter === 'o' || letter === 'u' || letter === 'à' ||\r\n    letter === 'è' || letter === 'ì' || letter === 'ò' || letter === 'ù')\r\n}\r\n\r\nfunction getNextVowelPos (token, start) {\r\n  start = start + 1\r\n  const length = token.length\r\n  for (let i = start; i < length; i++) {\r\n    if (isVowel(token[i])) {\r\n      return i\r\n    }\r\n  }\r\n  return length\r\n}\r\n\r\nfunction getNextConsonantPos (token, start) {\r\n  const length = token.length\r\n  for (let i = start; i < length; i++) { if (!isVowel(token[i])) return i }\r\n  return length\r\n}\r\n\r\nfunction endsin (token, suffix) {\r\n  if (token.length < suffix.length) return false\r\n  return (token.slice(-suffix.length) === suffix)\r\n}\r\n\r\nfunction endsinArr (token, suffixes) {\r\n  for (let i = 0; i < suffixes.length; i++) {\r\n    if (endsin(token, suffixes[i])) return suffixes[i]\r\n  }\r\n  return ''\r\n}\r\n\r\nfunction replaceAcute (token) {\r\n  let str = token.replace(/á/gi, 'à')\r\n  str = str.replace(/é/gi, 'è')\r\n  str = str.replace(/í/gi, 'ì')\r\n  str = str.replace(/ó/gi, 'ò')\r\n  str = str.replace(/ú/gi, 'ù')\r\n  return str\r\n}\r\n\r\nfunction vowelMarking (token) {\r\n  function replacer (match, p1, p2, p3) {\r\n    return p1 + p2.toUpperCase() + p3\r\n  }\r\n  const str = token.replace(/([aeiou])(i|u)([aeiou])/g, replacer)\r\n  return str\r\n}\r\n\r\n// perform full stemming algorithm on a single word\r\nPorterStemmer.stem = function (token) {\n  token = token.toLowerCase()\r\n  token = replaceAcute(token)\r\n  token = token.replace(/qu/g, 'qU')\r\n  token = vowelMarking(token)\r\n\r\n  if (token.length < 3) {\r\n    return token\r\n  }\r\n\r\n  let r1 = token.length\r\n  let r2 = token.length\r\n  let rv = token.length\r\n  const len = token.length\r\n  // R1 is the region after the first non-vowel following a vowel,\r\n  for (let i = 0; i < token.length - 1 && r1 === len; i++) {\r\n    if (isVowel(token[i]) && !isVowel(token[i + 1])) {\r\n      r1 = i + 2\r\n    }\r\n  }\r\n  // Or is the null region at the end of the word if there is no such non-vowel.\r\n\r\n  // R2 is the region after the first non-vowel following a vowel in R1\r\n  for (let i = r1; i < token.length - 1 && r2 === len; i++) {\r\n    if (isVowel(token[i]) && !isVowel(token[i + 1])) {\r\n      r2 = i + 2\r\n    }\r\n  }\r\n\r\n  // Or is the null region at the end of the word if there is no such non-vowel.\r\n\r\n  // If the second letter is a consonant, RV is the region after the next following vowel,\r\n\r\n  // RV as follow\r\n\r\n  if (len > 3) {\r\n    if (!isVowel(token[1])) {\r\n      // If the second letter is a consonant, RV is the region after the next following vowel\r\n      rv = getNextVowelPos(token, 1) + 1\r\n    } else if (isVowel(token[0]) && isVowel(token[1])) {\r\n      // or if the first two letters are vowels, RV is the region after the next consonant\r\n      rv = getNextConsonantPos(token, 2) + 1\r\n    } else {\r\n      // otherwise (consonant-vowel case) RV is the region after the third letter. But RV is the end of the word if these positions cannot be found.\r\n      rv = 3\r\n    }\r\n  }\r\n\r\n  let r1txt = token.substring(r1)\r\n  let r2txt = token.substring(r2)\r\n  let rvtxt = token.substring(rv)\r\n\r\n  const tokenOrig = token\r\n\r\n  // Step 0: Attached pronoun\r\n\r\n  const pronounSuf = ['glieli', 'glielo', 'gliene', 'gliela', 'gliele', 'sene', 'tene', 'cela', 'cele', 'celi', 'celo', 'cene', 'vela', 'vele', 'veli', 'velo', 'vene', 'mela', 'mele', 'meli', 'melo', 'mene', 'tela', 'tele', 'teli', 'telo', 'gli', 'ci', 'la', 'le', 'li', 'lo', 'mi', 'ne', 'si', 'ti', 'vi']\r\n  const pronounSufPre1 = ['ando', 'endo']\r\n  const pronounSufPre2 = ['ar', 'er', 'ir']\r\n  let suf = endsinArr(token, pronounSuf)\r\n\r\n  if (suf !== '') {\r\n    const preSuff1 = endsinArr(rvtxt.slice(0, -suf.length), pronounSufPre1)\r\n    const preSuff2 = endsinArr(rvtxt.slice(0, -suf.length), pronounSufPre2)\r\n\r\n    if (preSuff1 !== '') {\r\n      token = token.slice(0, -suf.length)\r\n    }\r\n    if (preSuff2 !== '') {\r\n      token = token.slice(0, -suf.length) + 'e'\r\n    }\r\n  }\r\n\r\n  if (token !== tokenOrig) {\r\n    r1txt = token.substring(r1)\r\n    r2txt = token.substring(r2)\r\n    rvtxt = token.substring(rv)\r\n  }\r\n\r\n  const tokenAfter0 = token\r\n\r\n  // Step 1:  Standard suffix removal\r\n\r\n  if ((suf = endsinArr(r2txt, ['ativamente', 'abilamente', 'ivamente', 'osamente', 'icamente'])) !== '') {\r\n    token = token.slice(0, -suf.length) // delete\r\n  } else if ((suf = endsinArr(r2txt, ['icazione', 'icazioni', 'icatore', 'icatori', 'azione', 'azioni', 'atore', 'atori'])) !== '') {\r\n    token = token.slice(0, -suf.length) // delete\r\n  } else if ((suf = endsinArr(r2txt, ['logia', 'logie'])) !== '') {\r\n    token = token.slice(0, -suf.length) + 'log' // replace with log\r\n  } else if ((suf = endsinArr(r2txt, ['uzione', 'uzioni', 'usione', 'usioni'])) !== '') {\r\n    token = token.slice(0, -suf.length) + 'u' // replace with u\r\n  } else if ((suf = endsinArr(r2txt, ['enza', 'enze'])) !== '') {\r\n    token = token.slice(0, -suf.length) + 'ente' // replace with ente\r\n  } else if ((suf = endsinArr(rvtxt, ['amento', 'amenti', 'imento', 'imenti'])) !== '') {\r\n    token = token.slice(0, -suf.length) // delete\r\n  } else if ((suf = endsinArr(r1txt, ['amente'])) !== '') {\r\n    token = token.slice(0, -suf.length) // delete\r\n  } else if ((suf = endsinArr(r2txt, ['atrice', 'atrici', 'abile', 'abili', 'ibile', 'ibili', 'mente', 'ante', 'anti', 'anza', 'anze', 'iche', 'ichi', 'ismo', 'ismi', 'ista', 'iste', 'isti', 'istà', 'istè', 'istì', 'ico', 'ici', 'ica', 'ice', 'oso', 'osi', 'osa', 'ose'])) !== '') {\r\n    token = token.slice(0, -suf.length) // delete\r\n  } else if ((suf = endsinArr(r2txt, ['abilità', 'icità', 'ività', 'ità'])) !== '') {\r\n    token = token.slice(0, -suf.length) // delete\r\n  } else if ((suf = endsinArr(r2txt, ['icativa', 'icativo', 'icativi', 'icative', 'ativa', 'ativo', 'ativi', 'ative', 'iva', 'ivo', 'ivi', 'ive'])) !== '') {\r\n    token = token.slice(0, -suf.length)\r\n  }\r\n\r\n  if (token !== tokenAfter0) {\r\n    r1txt = token.substring(r1)\r\n    r2txt = token.substring(r2)\r\n    rvtxt = token.substring(rv)\r\n  }\r\n\r\n  const tokenAfter1 = token\r\n\r\n  // Step 2:  Verb suffixes\r\n\r\n  if (tokenAfter0 === tokenAfter1) {\r\n    if ((suf = endsinArr(rvtxt, ['erebbero', 'irebbero', 'assero', 'assimo', 'eranno', 'erebbe', 'eremmo', 'ereste', 'eresti', 'essero', 'iranno', 'irebbe', 'iremmo', 'ireste', 'iresti', 'iscano', 'iscono', 'issero', 'arono', 'avamo', 'avano', 'avate', 'eremo', 'erete', 'erono', 'evamo', 'evano', 'evate', 'iremo', 'irete', 'irono', 'ivamo', 'ivano', 'ivate', 'ammo', 'ando', 'asse', 'assi', 'emmo', 'enda', 'ende', 'endi', 'endo', 'erai', 'Yamo', 'iamo', 'immo', 'irai', 'irei', 'isca', 'isce', 'isci', 'isco', 'erei', 'uti', 'uto', 'ita', 'ite', 'iti', 'ito', 'iva', 'ivi', 'ivo', 'ono', 'uta', 'ute', 'ano', 'are', 'ata', 'ate', 'ati', 'ato', 'ava', 'avi', 'avo', 'erà', 'ere', 'erò', 'ete', 'eva', 'evi', 'evo', 'irà', 'ire', 'irò', 'ar', 'ir'])) !== '') {\r\n      token = token.slice(0, -suf.length)\r\n    }\r\n  }\r\n\r\n  r1txt = token.substring(r1)\r\n  r2txt = token.substring(r2)\r\n  rvtxt = token.substring(rv)\r\n\r\n  // Always do step 3.\r\n\r\n  if ((suf = endsinArr(rvtxt, ['ia', 'ie', 'ii', 'io', 'ià', 'iè', 'iì', 'iò', 'a', 'e', 'i', 'o', 'à', 'è', 'ì', 'ò'])) !== '') {\r\n    token = token.slice(0, -suf.length)\r\n  }\r\n\r\n  r1txt = token.substring(r1)\r\n  r2txt = token.substring(r2)\r\n  rvtxt = token.substring(rv)\r\n\r\n  if ((suf = endsinArr(rvtxt, ['ch'])) !== '') {\r\n    token = token.slice(0, -suf.length) + 'c' // replace with c\r\n  } else if ((suf = endsinArr(rvtxt, ['gh'])) !== '') {\r\n    token = token.slice(0, -suf.length) + 'g' // replace with g\r\n  }\r\n\r\n  r1txt = token.substring(r1)\r\n  r2txt = token.substring(r2)\r\n  rvtxt = token.substring(rv)\r\n\r\n  return token.toLowerCase()\n}\r\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,YAAY;;AAEZ,MAAMA,OAAO,GAAGC,OAAO,CAAC,cAAc,CAAC;AAEvC,MAAMC,aAAa,GAAG,IAAIF,OAAO,CAAC,CAAC;AACnCG,MAAM,CAACC,OAAO,GAAGF,aAAa;AAE9B,SAASG,OAAOA,CAAEC,MAAM,EAAE;EACxB,OAAQA,MAAM,KAAK,GAAG,IAAIA,MAAM,KAAK,GAAG,IAAIA,MAAM,KAAK,GAAG,IAAIA,MAAM,KAAK,GAAG,IAAIA,MAAM,KAAK,GAAG,IAAIA,MAAM,KAAK,GAAG,IAC9GA,MAAM,KAAK,GAAG,IAAIA,MAAM,KAAK,GAAG,IAAIA,MAAM,KAAK,GAAG,IAAIA,MAAM,KAAK,GAAG;AACxE;AAEA,SAASC,eAAeA,CAAEC,KAAK,EAAEC,KAAK,EAAE;EACtCA,KAAK,GAAGA,KAAK,GAAG,CAAC;EACjB,MAAMC,MAAM,GAAGF,KAAK,CAACE,MAAM;EAC3B,KAAK,IAAIC,CAAC,GAAGF,KAAK,EAAEE,CAAC,GAAGD,MAAM,EAAEC,CAAC,EAAE,EAAE;IACnC,IAAIN,OAAO,CAACG,KAAK,CAACG,CAAC,CAAC,CAAC,EAAE;MACrB,OAAOA,CAAC;IACV;EACF;EACA,OAAOD,MAAM;AACf;AAEA,SAASE,mBAAmBA,CAAEJ,KAAK,EAAEC,KAAK,EAAE;EAC1C,MAAMC,MAAM,GAAGF,KAAK,CAACE,MAAM;EAC3B,KAAK,IAAIC,CAAC,GAAGF,KAAK,EAAEE,CAAC,GAAGD,MAAM,EAAEC,CAAC,EAAE,EAAE;IAAE,IAAI,CAACN,OAAO,CAACG,KAAK,CAACG,CAAC,CAAC,CAAC,EAAE,OAAOA,CAAC;EAAC;EACxE,OAAOD,MAAM;AACf;AAEA,SAASG,MAAMA,CAAEL,KAAK,EAAEM,MAAM,EAAE;EAC9B,IAAIN,KAAK,CAACE,MAAM,GAAGI,MAAM,CAACJ,MAAM,EAAE,OAAO,KAAK;EAC9C,OAAQF,KAAK,CAACO,KAAK,CAAC,CAACD,MAAM,CAACJ,MAAM,CAAC,KAAKI,MAAM;AAChD;AAEA,SAASE,SAASA,CAAER,KAAK,EAAES,QAAQ,EAAE;EACnC,KAAK,IAAIN,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGM,QAAQ,CAACP,MAAM,EAAEC,CAAC,EAAE,EAAE;IACxC,IAAIE,MAAM,CAACL,KAAK,EAAES,QAAQ,CAACN,CAAC,CAAC,CAAC,EAAE,OAAOM,QAAQ,CAACN,CAAC,CAAC;EACpD;EACA,OAAO,EAAE;AACX;AAEA,SAASO,YAAYA,CAAEV,KAAK,EAAE;EAC5B,IAAIW,GAAG,GAAGX,KAAK,CAACY,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC;EACnCD,GAAG,GAAGA,GAAG,CAACC,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC;EAC7BD,GAAG,GAAGA,GAAG,CAACC,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC;EAC7BD,GAAG,GAAGA,GAAG,CAACC,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC;EAC7BD,GAAG,GAAGA,GAAG,CAACC,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC;EAC7B,OAAOD,GAAG;AACZ;AAEA,SAASE,YAAYA,CAAEb,KAAK,EAAE;EAC5B,SAASc,QAAQA,CAAEC,KAAK,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAE;IACpC,OAAOF,EAAE,GAAGC,EAAE,CAACE,WAAW,CAAC,CAAC,GAAGD,EAAE;EACnC;EACA,MAAMP,GAAG,GAAGX,KAAK,CAACY,OAAO,CAAC,0BAA0B,EAAEE,QAAQ,CAAC;EAC/D,OAAOH,GAAG;AACZ;;AAEA;AACAjB,aAAa,CAAC0B,IAAI,GAAG,UAAUpB,KAAK,EAAE;EACpCA,KAAK,GAAGA,KAAK,CAACqB,WAAW,CAAC,CAAC;EAC3BrB,KAAK,GAAGU,YAAY,CAACV,KAAK,CAAC;EAC3BA,KAAK,GAAGA,KAAK,CAACY,OAAO,CAAC,KAAK,EAAE,IAAI,CAAC;EAClCZ,KAAK,GAAGa,YAAY,CAACb,KAAK,CAAC;EAE3B,IAAIA,KAAK,CAACE,MAAM,GAAG,CAAC,EAAE;IACpB,OAAOF,KAAK;EACd;EAEA,IAAIsB,EAAE,GAAGtB,KAAK,CAACE,MAAM;EACrB,IAAIqB,EAAE,GAAGvB,KAAK,CAACE,MAAM;EACrB,IAAIsB,EAAE,GAAGxB,KAAK,CAACE,MAAM;EACrB,MAAMuB,GAAG,GAAGzB,KAAK,CAACE,MAAM;EACxB;EACA,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,KAAK,CAACE,MAAM,GAAG,CAAC,IAAIoB,EAAE,KAAKG,GAAG,EAAEtB,CAAC,EAAE,EAAE;IACvD,IAAIN,OAAO,CAACG,KAAK,CAACG,CAAC,CAAC,CAAC,IAAI,CAACN,OAAO,CAACG,KAAK,CAACG,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE;MAC/CmB,EAAE,GAAGnB,CAAC,GAAG,CAAC;IACZ;EACF;EACA;;EAEA;EACA,KAAK,IAAIA,CAAC,GAAGmB,EAAE,EAAEnB,CAAC,GAAGH,KAAK,CAACE,MAAM,GAAG,CAAC,IAAIqB,EAAE,KAAKE,GAAG,EAAEtB,CAAC,EAAE,EAAE;IACxD,IAAIN,OAAO,CAACG,KAAK,CAACG,CAAC,CAAC,CAAC,IAAI,CAACN,OAAO,CAACG,KAAK,CAACG,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE;MAC/CoB,EAAE,GAAGpB,CAAC,GAAG,CAAC;IACZ;EACF;;EAEA;;EAEA;;EAEA;;EAEA,IAAIsB,GAAG,GAAG,CAAC,EAAE;IACX,IAAI,CAAC5B,OAAO,CAACG,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE;MACtB;MACAwB,EAAE,GAAGzB,eAAe,CAACC,KAAK,EAAE,CAAC,CAAC,GAAG,CAAC;IACpC,CAAC,MAAM,IAAIH,OAAO,CAACG,KAAK,CAAC,CAAC,CAAC,CAAC,IAAIH,OAAO,CAACG,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE;MACjD;MACAwB,EAAE,GAAGpB,mBAAmB,CAACJ,KAAK,EAAE,CAAC,CAAC,GAAG,CAAC;IACxC,CAAC,MAAM;MACL;MACAwB,EAAE,GAAG,CAAC;IACR;EACF;EAEA,IAAIE,KAAK,GAAG1B,KAAK,CAAC2B,SAAS,CAACL,EAAE,CAAC;EAC/B,IAAIM,KAAK,GAAG5B,KAAK,CAAC2B,SAAS,CAACJ,EAAE,CAAC;EAC/B,IAAIM,KAAK,GAAG7B,KAAK,CAAC2B,SAAS,CAACH,EAAE,CAAC;EAE/B,MAAMM,SAAS,GAAG9B,KAAK;;EAEvB;;EAEA,MAAM+B,UAAU,GAAG,CAAC,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EAChT,MAAMC,cAAc,GAAG,CAAC,MAAM,EAAE,MAAM,CAAC;EACvC,MAAMC,cAAc,GAAG,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EACzC,IAAIC,GAAG,GAAG1B,SAAS,CAACR,KAAK,EAAE+B,UAAU,CAAC;EAEtC,IAAIG,GAAG,KAAK,EAAE,EAAE;IACd,MAAMC,QAAQ,GAAG3B,SAAS,CAACqB,KAAK,CAACtB,KAAK,CAAC,CAAC,EAAE,CAAC2B,GAAG,CAAChC,MAAM,CAAC,EAAE8B,cAAc,CAAC;IACvE,MAAMI,QAAQ,GAAG5B,SAAS,CAACqB,KAAK,CAACtB,KAAK,CAAC,CAAC,EAAE,CAAC2B,GAAG,CAAChC,MAAM,CAAC,EAAE+B,cAAc,CAAC;IAEvE,IAAIE,QAAQ,KAAK,EAAE,EAAE;MACnBnC,KAAK,GAAGA,KAAK,CAACO,KAAK,CAAC,CAAC,EAAE,CAAC2B,GAAG,CAAChC,MAAM,CAAC;IACrC;IACA,IAAIkC,QAAQ,KAAK,EAAE,EAAE;MACnBpC,KAAK,GAAGA,KAAK,CAACO,KAAK,CAAC,CAAC,EAAE,CAAC2B,GAAG,CAAChC,MAAM,CAAC,GAAG,GAAG;IAC3C;EACF;EAEA,IAAIF,KAAK,KAAK8B,SAAS,EAAE;IACvBJ,KAAK,GAAG1B,KAAK,CAAC2B,SAAS,CAACL,EAAE,CAAC;IAC3BM,KAAK,GAAG5B,KAAK,CAAC2B,SAAS,CAACJ,EAAE,CAAC;IAC3BM,KAAK,GAAG7B,KAAK,CAAC2B,SAAS,CAACH,EAAE,CAAC;EAC7B;EAEA,MAAMa,WAAW,GAAGrC,KAAK;;EAEzB;;EAEA,IAAI,CAACkC,GAAG,GAAG1B,SAAS,CAACoB,KAAK,EAAE,CAAC,YAAY,EAAE,YAAY,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,CAAC,CAAC,MAAM,EAAE,EAAE;IACrG5B,KAAK,GAAGA,KAAK,CAACO,KAAK,CAAC,CAAC,EAAE,CAAC2B,GAAG,CAAChC,MAAM,CAAC,EAAC;EACtC,CAAC,MAAM,IAAI,CAACgC,GAAG,GAAG1B,SAAS,CAACoB,KAAK,EAAE,CAAC,UAAU,EAAE,UAAU,EAAE,SAAS,EAAE,SAAS,EAAE,QAAQ,EAAE,QAAQ,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC,MAAM,EAAE,EAAE;IAChI5B,KAAK,GAAGA,KAAK,CAACO,KAAK,CAAC,CAAC,EAAE,CAAC2B,GAAG,CAAChC,MAAM,CAAC,EAAC;EACtC,CAAC,MAAM,IAAI,CAACgC,GAAG,GAAG1B,SAAS,CAACoB,KAAK,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC,MAAM,EAAE,EAAE;IAC9D5B,KAAK,GAAGA,KAAK,CAACO,KAAK,CAAC,CAAC,EAAE,CAAC2B,GAAG,CAAChC,MAAM,CAAC,GAAG,KAAK,EAAC;EAC9C,CAAC,MAAM,IAAI,CAACgC,GAAG,GAAG1B,SAAS,CAACoB,KAAK,EAAE,CAAC,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAAC,MAAM,EAAE,EAAE;IACpF5B,KAAK,GAAGA,KAAK,CAACO,KAAK,CAAC,CAAC,EAAE,CAAC2B,GAAG,CAAChC,MAAM,CAAC,GAAG,GAAG,EAAC;EAC5C,CAAC,MAAM,IAAI,CAACgC,GAAG,GAAG1B,SAAS,CAACoB,KAAK,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC,MAAM,EAAE,EAAE;IAC5D5B,KAAK,GAAGA,KAAK,CAACO,KAAK,CAAC,CAAC,EAAE,CAAC2B,GAAG,CAAChC,MAAM,CAAC,GAAG,MAAM,EAAC;EAC/C,CAAC,MAAM,IAAI,CAACgC,GAAG,GAAG1B,SAAS,CAACqB,KAAK,EAAE,CAAC,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAAC,MAAM,EAAE,EAAE;IACpF7B,KAAK,GAAGA,KAAK,CAACO,KAAK,CAAC,CAAC,EAAE,CAAC2B,GAAG,CAAChC,MAAM,CAAC,EAAC;EACtC,CAAC,MAAM,IAAI,CAACgC,GAAG,GAAG1B,SAAS,CAACkB,KAAK,EAAE,CAAC,QAAQ,CAAC,CAAC,MAAM,EAAE,EAAE;IACtD1B,KAAK,GAAGA,KAAK,CAACO,KAAK,CAAC,CAAC,EAAE,CAAC2B,GAAG,CAAChC,MAAM,CAAC,EAAC;EACtC,CAAC,MAAM,IAAI,CAACgC,GAAG,GAAG1B,SAAS,CAACoB,KAAK,EAAE,CAAC,QAAQ,EAAE,QAAQ,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC,MAAM,EAAE,EAAE;IACrR5B,KAAK,GAAGA,KAAK,CAACO,KAAK,CAAC,CAAC,EAAE,CAAC2B,GAAG,CAAChC,MAAM,CAAC,EAAC;EACtC,CAAC,MAAM,IAAI,CAACgC,GAAG,GAAG1B,SAAS,CAACoB,KAAK,EAAE,CAAC,SAAS,EAAE,OAAO,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC,MAAM,EAAE,EAAE;IAChF5B,KAAK,GAAGA,KAAK,CAACO,KAAK,CAAC,CAAC,EAAE,CAAC2B,GAAG,CAAChC,MAAM,CAAC,EAAC;EACtC,CAAC,MAAM,IAAI,CAACgC,GAAG,GAAG1B,SAAS,CAACoB,KAAK,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC,MAAM,EAAE,EAAE;IACxJ5B,KAAK,GAAGA,KAAK,CAACO,KAAK,CAAC,CAAC,EAAE,CAAC2B,GAAG,CAAChC,MAAM,CAAC;EACrC;EAEA,IAAIF,KAAK,KAAKqC,WAAW,EAAE;IACzBX,KAAK,GAAG1B,KAAK,CAAC2B,SAAS,CAACL,EAAE,CAAC;IAC3BM,KAAK,GAAG5B,KAAK,CAAC2B,SAAS,CAACJ,EAAE,CAAC;IAC3BM,KAAK,GAAG7B,KAAK,CAAC2B,SAAS,CAACH,EAAE,CAAC;EAC7B;EAEA,MAAMc,WAAW,GAAGtC,KAAK;;EAEzB;;EAEA,IAAIqC,WAAW,KAAKC,WAAW,EAAE;IAC/B,IAAI,CAACJ,GAAG,GAAG1B,SAAS,CAACqB,KAAK,EAAE,CAAC,UAAU,EAAE,UAAU,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC,MAAM,EAAE,EAAE;MAClvB7B,KAAK,GAAGA,KAAK,CAACO,KAAK,CAAC,CAAC,EAAE,CAAC2B,GAAG,CAAChC,MAAM,CAAC;IACrC;EACF;EAEAwB,KAAK,GAAG1B,KAAK,CAAC2B,SAAS,CAACL,EAAE,CAAC;EAC3BM,KAAK,GAAG5B,KAAK,CAAC2B,SAAS,CAACJ,EAAE,CAAC;EAC3BM,KAAK,GAAG7B,KAAK,CAAC2B,SAAS,CAACH,EAAE,CAAC;;EAE3B;;EAEA,IAAI,CAACU,GAAG,GAAG1B,SAAS,CAACqB,KAAK,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE;IAC7H7B,KAAK,GAAGA,KAAK,CAACO,KAAK,CAAC,CAAC,EAAE,CAAC2B,GAAG,CAAChC,MAAM,CAAC;EACrC;EAEAwB,KAAK,GAAG1B,KAAK,CAAC2B,SAAS,CAACL,EAAE,CAAC;EAC3BM,KAAK,GAAG5B,KAAK,CAAC2B,SAAS,CAACJ,EAAE,CAAC;EAC3BM,KAAK,GAAG7B,KAAK,CAAC2B,SAAS,CAACH,EAAE,CAAC;EAE3B,IAAI,CAACU,GAAG,GAAG1B,SAAS,CAACqB,KAAK,EAAE,CAAC,IAAI,CAAC,CAAC,MAAM,EAAE,EAAE;IAC3C7B,KAAK,GAAGA,KAAK,CAACO,KAAK,CAAC,CAAC,EAAE,CAAC2B,GAAG,CAAChC,MAAM,CAAC,GAAG,GAAG,EAAC;EAC5C,CAAC,MAAM,IAAI,CAACgC,GAAG,GAAG1B,SAAS,CAACqB,KAAK,EAAE,CAAC,IAAI,CAAC,CAAC,MAAM,EAAE,EAAE;IAClD7B,KAAK,GAAGA,KAAK,CAACO,KAAK,CAAC,CAAC,EAAE,CAAC2B,GAAG,CAAChC,MAAM,CAAC,GAAG,GAAG,EAAC;EAC5C;EAEAwB,KAAK,GAAG1B,KAAK,CAAC2B,SAAS,CAACL,EAAE,CAAC;EAC3BM,KAAK,GAAG5B,KAAK,CAAC2B,SAAS,CAACJ,EAAE,CAAC;EAC3BM,KAAK,GAAG7B,KAAK,CAAC2B,SAAS,CAACH,EAAE,CAAC;EAE3B,OAAOxB,KAAK,CAACqB,WAAW,CAAC,CAAC;AAC5B,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}