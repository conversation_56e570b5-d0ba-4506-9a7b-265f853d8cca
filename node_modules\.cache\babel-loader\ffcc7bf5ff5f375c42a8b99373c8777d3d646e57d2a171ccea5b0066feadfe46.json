{"ast": null, "code": "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { Reciprocal } from '@tensorflow/tfjs-core';\nimport { unaryKernelFunc } from '../kernel_utils/kernel_funcs_utils';\nconst RECIPROCAL = \"return 1.0 / x;\";\nexport const reciprocal = unaryKernelFunc({\n  opSnippet: RECIPROCAL\n});\nexport const reciprocalConfig = {\n  kernelName: Reciprocal,\n  backendName: 'webgl',\n  kernelFunc: reciprocal\n};", "map": {"version": 3, "names": ["Reciprocal", "unaryKernelFunc", "RECIPROCAL", "reciprocal", "opSnippet", "reciprocalConfig", "kernelName", "backendName", "kernelFunc"], "sources": ["C:\\tfjs-backend-webgl\\src\\kernels\\Reciprocal.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {KernelConfig, Reciprocal} from '@tensorflow/tfjs-core';\nimport {unaryKernelFunc} from '../kernel_utils/kernel_funcs_utils';\n\nconst RECIPROCAL = `return 1.0 / x;`;\n\nexport const reciprocal = unaryKernelFunc({opSnippet: RECIPROCAL});\n\nexport const reciprocalConfig: KernelConfig = {\n  kernelName: Reciprocal,\n  backendName: 'webgl',\n  kernelFunc: reciprocal,\n};\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAiBA,SAAsBA,UAAU,QAAO,uBAAuB;AAC9D,SAAQC,eAAe,QAAO,oCAAoC;AAElE,MAAMC,UAAU,oBAAoB;AAEpC,OAAO,MAAMC,UAAU,GAAGF,eAAe,CAAC;EAACG,SAAS,EAAEF;AAAU,CAAC,CAAC;AAElE,OAAO,MAAMG,gBAAgB,GAAiB;EAC5CC,UAAU,EAAEN,UAAU;EACtBO,WAAW,EAAE,OAAO;EACpBC,UAAU,EAAEL;CACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}