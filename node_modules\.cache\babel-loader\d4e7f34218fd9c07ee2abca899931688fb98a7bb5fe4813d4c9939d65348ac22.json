{"ast": null, "code": "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { ENGINE } from '../engine';\nimport { AvgPool } from '../kernel_names';\nimport { convertToTensor } from '../tensor_util_env';\nimport * as util from '../util';\nimport { cast } from './cast';\nimport * as conv_util from './conv_util';\nimport { op } from './operation';\nimport { reshape } from './reshape';\n/**\n * Computes the 2D average pooling of an image.\n *\n * @param x The input tensor, of rank 4 or rank 3 of shape\n *     `[batch, height, width, inChannels]`. If rank 3, batch of 1 is assumed.\n * @param filterSize The filter size: `[filterHeight, filterWidth]`. If\n *     `filterSize` is a single number, then `filterHeight == filterWidth`.\n * @param strides The strides of the pooling: `[strideHeight, strideWidth]`. If\n *     `strides` is a single number, then `strideHeight == strideWidth`.\n * @param pad The type of padding algorithm:\n *    - `same` and stride 1: output will be of same size as input,\n *       regardless of filter size.\n *    - `valid`: output will be smaller than input if filter is larger\n *       than 1x1.\n *    - For more info, see this guide:\n *     [https://www.tensorflow.org/api_docs/python/tf/nn/convolution](\n *         https://www.tensorflow.org/api_docs/python/tf/nn/convolution)\n * @param dimRoundingMode A string from: 'ceil', 'round', 'floor'. If none is\n *     provided, it will default to truncate.\n *\n * @doc {heading: 'Operations', subheading: 'Convolution'}\n */\nfunction avgPool_(x, filterSize, strides, pad, dimRoundingMode) {\n  const $x = convertToTensor(x, 'x', 'avgPool', 'float32');\n  const dilations = 1;\n  util.assert(conv_util.eitherStridesOrDilationsAreOne(strides, dilations), () => 'Error in avgPool: Either strides or dilations must be 1. ' + \"Got strides \".concat(strides, \" and dilations '\").concat(dilations, \"'\"));\n  let x4D = $x;\n  let reshapedTo4D = false;\n  if ($x.rank === 3) {\n    reshapedTo4D = true;\n    x4D = reshape($x, [1, $x.shape[0], $x.shape[1], $x.shape[2]]);\n  }\n  util.assert(x4D.rank === 4, () => \"Error in avgPool: x must be rank 4 but got rank \".concat(x4D.rank, \".\"));\n  conv_util.checkPadOnDimRoundingMode('avgPool', pad, dimRoundingMode);\n  const inputs = {\n    x: x4D\n  };\n  const attrs = {\n    filterSize,\n    strides,\n    pad,\n    dimRoundingMode\n  };\n  // tslint:disable-next-line: no-unnecessary-type-assertion\n  let res = ENGINE.runKernel(AvgPool, inputs, attrs);\n  res = cast(res, $x.dtype);\n  if (reshapedTo4D) {\n    return reshape(res, [res.shape[1], res.shape[2], res.shape[3]]);\n  }\n  return res;\n}\nexport const avgPool = /* @__PURE__ */op({\n  avgPool_\n});", "map": {"version": 3, "names": ["ENGINE", "AvgPool", "convertToTensor", "util", "cast", "conv_util", "op", "reshape", "avgPool_", "x", "filterSize", "strides", "pad", "dimRoundingMode", "$x", "dilations", "assert", "eitherStridesOrDilationsAreOne", "concat", "x4D", "reshapedTo4D", "rank", "shape", "checkPadOnDimRoundingMode", "inputs", "attrs", "res", "runKernel", "dtype", "avgPool"], "sources": ["C:\\tfjs-core\\src\\ops\\avg_pool.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {ENGINE} from '../engine';\nimport {AvgPool, AvgPoolAttrs, AvgPoolInputs} from '../kernel_names';\nimport {NamedAttrMap} from '../kernel_registry';\nimport {Tensor3D, Tensor4D} from '../tensor';\nimport {NamedTensorMap} from '../tensor_types';\nimport {convertToTensor} from '../tensor_util_env';\nimport {TensorLike} from '../types';\nimport * as util from '../util';\n\nimport {cast} from './cast';\nimport * as conv_util from './conv_util';\nimport {op} from './operation';\nimport {reshape} from './reshape';\n\n/**\n * Computes the 2D average pooling of an image.\n *\n * @param x The input tensor, of rank 4 or rank 3 of shape\n *     `[batch, height, width, inChannels]`. If rank 3, batch of 1 is assumed.\n * @param filterSize The filter size: `[filterHeight, filterWidth]`. If\n *     `filterSize` is a single number, then `filterHeight == filterWidth`.\n * @param strides The strides of the pooling: `[strideHeight, strideWidth]`. If\n *     `strides` is a single number, then `strideHeight == strideWidth`.\n * @param pad The type of padding algorithm:\n *    - `same` and stride 1: output will be of same size as input,\n *       regardless of filter size.\n *    - `valid`: output will be smaller than input if filter is larger\n *       than 1x1.\n *    - For more info, see this guide:\n *     [https://www.tensorflow.org/api_docs/python/tf/nn/convolution](\n *         https://www.tensorflow.org/api_docs/python/tf/nn/convolution)\n * @param dimRoundingMode A string from: 'ceil', 'round', 'floor'. If none is\n *     provided, it will default to truncate.\n *\n * @doc {heading: 'Operations', subheading: 'Convolution'}\n */\nfunction avgPool_<T extends Tensor3D|Tensor4D>(\n    x: T|TensorLike, filterSize: [number, number]|number,\n    strides: [number, number]|number,\n    pad: 'valid'|'same'|number|conv_util.ExplicitPadding,\n    dimRoundingMode?: 'floor'|'round'|'ceil'): T {\n  const $x = convertToTensor(x, 'x', 'avgPool', 'float32');\n  const dilations = 1;\n\n  util.assert(\n      conv_util.eitherStridesOrDilationsAreOne(strides, dilations),\n      () => 'Error in avgPool: Either strides or dilations must be 1. ' +\n          `Got strides ${strides} and dilations '${dilations}'`);\n\n  let x4D = $x as Tensor4D;\n  let reshapedTo4D = false;\n  if ($x.rank === 3) {\n    reshapedTo4D = true;\n    x4D = reshape($x, [1, $x.shape[0], $x.shape[1], $x.shape[2]]);\n  }\n\n  util.assert(\n      x4D.rank === 4,\n      () => `Error in avgPool: x must be rank 4 but got rank ${x4D.rank}.`);\n  conv_util.checkPadOnDimRoundingMode('avgPool', pad, dimRoundingMode);\n  const inputs: AvgPoolInputs = {x: x4D};\n  const attrs: AvgPoolAttrs = {filterSize, strides, pad, dimRoundingMode};\n\n  // tslint:disable-next-line: no-unnecessary-type-assertion\n  let res = ENGINE.runKernel(\n                AvgPool, inputs as unknown as NamedTensorMap,\n                attrs as unknown as NamedAttrMap) as T;\n\n  res = cast(res, $x.dtype);\n\n  if (reshapedTo4D) {\n    return reshape(res, [res.shape[1], res.shape[2], res.shape[3]]) as T;\n  }\n\n  return res;\n}\n\nexport const avgPool = /* @__PURE__ */ op({avgPool_});\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAiBA,SAAQA,MAAM,QAAO,WAAW;AAChC,SAAQC,OAAO,QAAoC,iBAAiB;AAIpE,SAAQC,eAAe,QAAO,oBAAoB;AAElD,OAAO,KAAKC,IAAI,MAAM,SAAS;AAE/B,SAAQC,IAAI,QAAO,QAAQ;AAC3B,OAAO,KAAKC,SAAS,MAAM,aAAa;AACxC,SAAQC,EAAE,QAAO,aAAa;AAC9B,SAAQC,OAAO,QAAO,WAAW;AAEjC;;;;;;;;;;;;;;;;;;;;;;AAsBA,SAASC,QAAQA,CACbC,CAAe,EAAEC,UAAmC,EACpDC,OAAgC,EAChCC,GAAoD,EACpDC,eAAwC;EAC1C,MAAMC,EAAE,GAAGZ,eAAe,CAACO,CAAC,EAAE,GAAG,EAAE,SAAS,EAAE,SAAS,CAAC;EACxD,MAAMM,SAAS,GAAG,CAAC;EAEnBZ,IAAI,CAACa,MAAM,CACPX,SAAS,CAACY,8BAA8B,CAACN,OAAO,EAAEI,SAAS,CAAC,EAC5D,MAAM,2DAA2D,kBAAAG,MAAA,CAC9CP,OAAO,sBAAAO,MAAA,CAAmBH,SAAS,MAAG,CAAC;EAE9D,IAAII,GAAG,GAAGL,EAAc;EACxB,IAAIM,YAAY,GAAG,KAAK;EACxB,IAAIN,EAAE,CAACO,IAAI,KAAK,CAAC,EAAE;IACjBD,YAAY,GAAG,IAAI;IACnBD,GAAG,GAAGZ,OAAO,CAACO,EAAE,EAAE,CAAC,CAAC,EAAEA,EAAE,CAACQ,KAAK,CAAC,CAAC,CAAC,EAAER,EAAE,CAACQ,KAAK,CAAC,CAAC,CAAC,EAAER,EAAE,CAACQ,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;;EAG/DnB,IAAI,CAACa,MAAM,CACPG,GAAG,CAACE,IAAI,KAAK,CAAC,EACd,yDAAAH,MAAA,CAAyDC,GAAG,CAACE,IAAI,MAAG,CAAC;EACzEhB,SAAS,CAACkB,yBAAyB,CAAC,SAAS,EAAEX,GAAG,EAAEC,eAAe,CAAC;EACpE,MAAMW,MAAM,GAAkB;IAACf,CAAC,EAAEU;EAAG,CAAC;EACtC,MAAMM,KAAK,GAAiB;IAACf,UAAU;IAAEC,OAAO;IAAEC,GAAG;IAAEC;EAAe,CAAC;EAEvE;EACA,IAAIa,GAAG,GAAG1B,MAAM,CAAC2B,SAAS,CACZ1B,OAAO,EAAEuB,MAAmC,EAC5CC,KAAgC,CAAM;EAEpDC,GAAG,GAAGtB,IAAI,CAACsB,GAAG,EAAEZ,EAAE,CAACc,KAAK,CAAC;EAEzB,IAAIR,YAAY,EAAE;IAChB,OAAOb,OAAO,CAACmB,GAAG,EAAE,CAACA,GAAG,CAACJ,KAAK,CAAC,CAAC,CAAC,EAAEI,GAAG,CAACJ,KAAK,CAAC,CAAC,CAAC,EAAEI,GAAG,CAACJ,KAAK,CAAC,CAAC,CAAC,CAAC,CAAM;;EAGtE,OAAOI,GAAG;AACZ;AAEA,OAAO,MAAMG,OAAO,GAAG,eAAgBvB,EAAE,CAAC;EAACE;AAAQ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}