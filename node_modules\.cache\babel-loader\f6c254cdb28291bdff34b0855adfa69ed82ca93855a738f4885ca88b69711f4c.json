{"ast": null, "code": "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { reverse } from '../../ops/reverse';\nimport { getGlobalTensorClass } from '../../tensor';\ngetGlobalTensorClass().prototype.reverse = function (axis) {\n  this.throwIfDisposed();\n  return reverse(this, axis);\n};", "map": {"version": 3, "names": ["reverse", "getGlobalTensorClass", "prototype", "axis", "throwIfDisposed"], "sources": ["C:\\tfjs-core\\src\\public\\chained_ops\\reverse.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport {reverse} from '../../ops/reverse';\nimport {getGlobalTensorClass, Tensor} from '../../tensor';\nimport {Rank} from '../../types';\n\ndeclare module '../../tensor' {\n  interface Tensor<R extends Rank = Rank> {\n    reverse<T extends Tensor>(this: T, axis?: number|number[]): T;\n  }\n}\n\ngetGlobalTensorClass().prototype.reverse = function<T extends Tensor>(\n    this: T, axis?: number|number[]): T {\n  this.throwIfDisposed();\n  return reverse(this, axis);\n};\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAgBA,SAAQA,OAAO,QAAO,mBAAmB;AACzC,SAAQC,oBAAoB,QAAe,cAAc;AASzDA,oBAAoB,EAAE,CAACC,SAAS,CAACF,OAAO,GAAG,UAC9BG,IAAsB;EACjC,IAAI,CAACC,eAAe,EAAE;EACtB,OAAOJ,OAAO,CAAC,IAAI,EAAEG,IAAI,CAAC;AAC5B,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}