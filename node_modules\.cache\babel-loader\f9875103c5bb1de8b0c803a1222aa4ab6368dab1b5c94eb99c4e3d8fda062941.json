{"ast": null, "code": "/**\n * @license\n * Copyright 2018 Google LLC\n *\n * Use of this source code is governed by an MIT-style\n * license that can be found in the LICENSE file or at\n * https://opensource.org/licenses/MIT.\n * =============================================================================\n */\n/**\n * Explicit error types.\n *\n * See the following link for more information about why the code includes\n * calls to setPrototypeOf:\n *\n * https://github.com/Microsoft/TypeScript-wiki/blob/master/Breaking-Changes.md#extending-built-ins-like-error-array-and-map-may-no-longer-work\n */\n// tslint:enable\n/**\n * Equivalent of Python's AttributeError.\n */\nexport class AttributeError extends Error {\n  constructor(message) {\n    super(message);\n    // Set the prototype explicitly.\n    Object.setPrototypeOf(this, AttributeError.prototype);\n  }\n}\n/**\n * Equivalent of Python's RuntimeError.\n */\nexport class RuntimeError extends Error {\n  constructor(message) {\n    super(message);\n    // Set the prototype explicitly.\n    Object.setPrototypeOf(this, RuntimeError.prototype);\n  }\n}\n/**\n * Equivalent of Python's ValueError.\n */\nexport class ValueError extends Error {\n  constructor(message) {\n    super(message);\n    // Set the prototype explicitly.\n    Object.setPrototypeOf(this, ValueError.prototype);\n  }\n}\n/**\n * Equivalent of Python's NotImplementedError.\n */\nexport class NotImplementedError extends Error {\n  constructor(message) {\n    super(message);\n    // Set the prototype explicitly.\n    Object.setPrototypeOf(this, NotImplementedError.prototype);\n  }\n}\n/**\n * Equivalent of Python's AssertionError.\n */\nexport class AssertionError extends Error {\n  constructor(message) {\n    super(message);\n    // Set the prototype explicitly.\n    Object.setPrototypeOf(this, AssertionError.prototype);\n  }\n}\n/**\n * Equivalent of Python's IndexError.\n */\nexport class IndexError extends Error {\n  constructor(message) {\n    super(message);\n    // Set the prototype explicitly.\n    Object.setPrototypeOf(this, IndexError.prototype);\n  }\n}", "map": {"version": 3, "names": ["AttributeError", "Error", "constructor", "message", "Object", "setPrototypeOf", "prototype", "RuntimeError", "ValueError", "NotImplementedError", "AssertionError", "IndexError"], "sources": ["C:\\tfjs-layers\\src\\errors.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2018 Google LLC\n *\n * Use of this source code is governed by an MIT-style\n * license that can be found in the LICENSE file or at\n * https://opensource.org/licenses/MIT.\n * =============================================================================\n */\n\n/**\n * Explicit error types.\n *\n * See the following link for more information about why the code includes\n * calls to setPrototypeOf:\n *\n * https://github.com/Microsoft/TypeScript-wiki/blob/master/Breaking-Changes.md#extending-built-ins-like-error-array-and-map-may-no-longer-work\n */\n// tslint:enable\n\n/**\n * Equivalent of Python's AttributeError.\n */\nexport class AttributeError extends Error {\n  constructor(message?: string) {\n    super(message);\n    // Set the prototype explicitly.\n    Object.setPrototypeOf(this, AttributeError.prototype);\n  }\n}\n\n/**\n * Equivalent of Python's RuntimeError.\n */\nexport class RuntimeError extends Error {\n  constructor(message?: string) {\n    super(message);\n    // Set the prototype explicitly.\n    Object.setPrototypeOf(this, RuntimeError.prototype);\n  }\n}\n\n/**\n * Equivalent of Python's ValueError.\n */\nexport class ValueError extends Error {\n  constructor(message?: string) {\n    super(message);\n    // Set the prototype explicitly.\n    Object.setPrototypeOf(this, ValueError.prototype);\n  }\n}\n\n/**\n * Equivalent of Python's NotImplementedError.\n */\nexport class NotImplementedError extends Error {\n  constructor(message?: string) {\n    super(message);\n    // Set the prototype explicitly.\n    Object.setPrototypeOf(this, NotImplementedError.prototype);\n  }\n}\n\n/**\n * Equivalent of Python's AssertionError.\n */\nexport class AssertionError extends Error {\n  constructor(message?: string) {\n    super(message);\n    // Set the prototype explicitly.\n    Object.setPrototypeOf(this, AssertionError.prototype);\n  }\n}\n\n/**\n * Equivalent of Python's IndexError.\n */\nexport class IndexError extends Error {\n  constructor(message?: string) {\n    super(message);\n    // Set the prototype explicitly.\n    Object.setPrototypeOf(this, IndexError.prototype);\n  }\n}\n"], "mappings": "AAAA;;;;;;;;;AAUA;;;;;;;;AAQA;AAEA;;;AAGA,OAAM,MAAOA,cAAe,SAAQC,KAAK;EACvCC,YAAYC,OAAgB;IAC1B,KAAK,CAACA,OAAO,CAAC;IACd;IACAC,MAAM,CAACC,cAAc,CAAC,IAAI,EAAEL,cAAc,CAACM,SAAS,CAAC;EACvD;;AAGF;;;AAGA,OAAM,MAAOC,YAAa,SAAQN,KAAK;EACrCC,YAAYC,OAAgB;IAC1B,KAAK,CAACA,OAAO,CAAC;IACd;IACAC,MAAM,CAACC,cAAc,CAAC,IAAI,EAAEE,YAAY,CAACD,SAAS,CAAC;EACrD;;AAGF;;;AAGA,OAAM,MAAOE,UAAW,SAAQP,KAAK;EACnCC,YAAYC,OAAgB;IAC1B,KAAK,CAACA,OAAO,CAAC;IACd;IACAC,MAAM,CAACC,cAAc,CAAC,IAAI,EAAEG,UAAU,CAACF,SAAS,CAAC;EACnD;;AAGF;;;AAGA,OAAM,MAAOG,mBAAoB,SAAQR,KAAK;EAC5CC,YAAYC,OAAgB;IAC1B,KAAK,CAACA,OAAO,CAAC;IACd;IACAC,MAAM,CAACC,cAAc,CAAC,IAAI,EAAEI,mBAAmB,CAACH,SAAS,CAAC;EAC5D;;AAGF;;;AAGA,OAAM,MAAOI,cAAe,SAAQT,KAAK;EACvCC,YAAYC,OAAgB;IAC1B,KAAK,CAACA,OAAO,CAAC;IACd;IACAC,MAAM,CAACC,cAAc,CAAC,IAAI,EAAEK,cAAc,CAACJ,SAAS,CAAC;EACvD;;AAGF;;;AAGA,OAAM,MAAOK,UAAW,SAAQV,KAAK;EACnCC,YAAYC,OAAgB;IAC1B,KAAK,CAACA,OAAO,CAAC;IACd;IACAC,MAAM,CAACC,cAAc,CAAC,IAAI,EAAEM,UAAU,CAACL,SAAS,CAAC;EACnD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}