{"ast": null, "code": "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { relu6 } from '../../ops/relu6';\nimport { getGlobalTensorClass } from '../../tensor';\ngetGlobalTensorClass().prototype.relu6 = function () {\n  this.throwIfDisposed();\n  return relu6(this);\n};", "map": {"version": 3, "names": ["relu6", "getGlobalTensorClass", "prototype", "throwIfDisposed"], "sources": ["C:\\tfjs-core\\src\\public\\chained_ops\\relu6.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport {relu6} from '../../ops/relu6';\nimport {getGlobalTensorClass, Tensor} from '../../tensor';\nimport {Rank} from '../../types';\n\ndeclare module '../../tensor' {\n  interface Tensor<R extends Rank = Rank> {\n    relu6<T extends Tensor>(): T;\n  }\n}\n\ngetGlobalTensorClass().prototype.relu6 = function<T extends Tensor>(this: T):\n    T {\n  this.throwIfDisposed();\n  return relu6(this);\n};\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAgBA,SAAQA,KAAK,QAAO,iBAAiB;AACrC,SAAQC,oBAAoB,QAAe,cAAc;AASzDA,oBAAoB,EAAE,CAACC,SAAS,CAACF,KAAK,GAAG;EAEvC,IAAI,CAACG,eAAe,EAAE;EACtB,OAAOH,KAAK,CAAC,IAAI,CAAC;AACpB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}