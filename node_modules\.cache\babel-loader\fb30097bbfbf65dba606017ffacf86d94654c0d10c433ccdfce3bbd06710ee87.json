{"ast": null, "code": "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the License);\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an AS IS BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { Exp } from '@tensorflow/tfjs-core';\nimport { createSimpleUnaryImpl } from '../utils/unary_impl';\nimport { unaryKernelFuncFromImpl } from '../utils/unary_utils';\nexport const expImpl = createSimpleUnaryImpl(xi => Math.exp(xi));\nexport const exp = unaryKernelFuncFromImpl(Exp, expImpl, 'float32');\nexport const expConfig = {\n  kernelName: Exp,\n  backendName: 'cpu',\n  kernelFunc: exp\n};", "map": {"version": 3, "names": ["Exp", "createSimpleUnaryImpl", "unaryKernelFuncFromImpl", "expImpl", "xi", "Math", "exp", "expConfig", "kernelName", "backendName", "kernelFunc"], "sources": ["C:\\tfjs-backend-cpu\\src\\kernels\\Exp.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the License);\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an AS IS BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {Exp, KernelConfig} from '@tensorflow/tfjs-core';\n\nimport {createSimpleUnaryImpl} from '../utils/unary_impl';\nimport {unaryKernelFuncFromImpl} from '../utils/unary_utils';\n\nexport const expImpl = createSimpleUnaryImpl((xi) => Math.exp(xi));\nexport const exp = unaryKernelFuncFromImpl(Exp, expImpl, 'float32');\n\nexport const expConfig: KernelConfig = {\n  kernelName: Exp,\n  backendName: 'cpu',\n  kernelFunc: exp,\n};\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAiBA,SAAQA,GAAG,QAAqB,uBAAuB;AAEvD,SAAQC,qBAAqB,QAAO,qBAAqB;AACzD,SAAQC,uBAAuB,QAAO,sBAAsB;AAE5D,OAAO,MAAMC,OAAO,GAAGF,qBAAqB,CAAEG,EAAE,IAAKC,IAAI,CAACC,GAAG,CAACF,EAAE,CAAC,CAAC;AAClE,OAAO,MAAME,GAAG,GAAGJ,uBAAuB,CAACF,GAAG,EAAEG,OAAO,EAAE,SAAS,CAAC;AAEnE,OAAO,MAAMI,SAAS,GAAiB;EACrCC,UAAU,EAAER,GAAG;EACfS,WAAW,EAAE,KAAK;EAClBC,UAAU,EAAEJ;CACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}