{"ast": null, "code": "/**\n * @license\n * Copyright 2018 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n *\n * =============================================================================\n */\nimport * as tf from '@tensorflow/tfjs-core';\nimport * as seedrandom from 'seedrandom';\nimport { iteratorFromConcatenated, iteratorFromFunction, iteratorFromItems, iteratorFromZipped, ZipMismatchMode } from './iterators/lazy_iterator';\nimport { canTensorify, deepMapAndAwaitAll, isIterable } from './util/deep_map';\n// TODO(soergel): consider vectorized operations within the pipeline.\n/**\n * Represents a potentially large list of independent data elements (typically\n * 'samples' or 'examples').\n *\n * A 'data example' may be a primitive, an array, a map from string keys to\n * values, or any nested structure of these.\n *\n * A `Dataset` represents an ordered collection of elements, together with a\n * chain of transformations to be performed on those elements. Each\n * transformation is a method of `Dataset` that returns another `Dataset`, so\n * these may be chained, e.g.\n * `const processedDataset = rawDataset.filter(...).map(...).batch(...)`.\n *\n * Data loading and transformation is done in a lazy, streaming fashion.  The\n * dataset may be iterated over multiple times; each iteration starts the data\n * loading anew and recapitulates the transformations.\n *\n * A `Dataset` is typically processed as a stream of unbatched examples -- i.e.,\n * its transformations are applied one example at a time. Batching produces a\n * new `Dataset` where each element is a batch. Batching should usually come\n * last in a pipeline, because data transformations are easier to express on a\n * per-example basis than on a per-batch basis.\n *\n * The following code examples are calling `await dataset.forEachAsync(...)` to\n * iterate once over the entire dataset in order to print out the data.\n *\n * @doc {heading: 'Data', subheading: 'Classes', namespace: 'data'}\n */\nclass Dataset {\n  constructor() {\n    this.size = null;\n  }\n  // TODO(soergel): Make Datasets report whether repeated iterator() calls\n  // produce the same result (e.g., reading from a file) or different results\n  // (e.g., from the webcam).  Currently we don't make this distinction but it\n  // could be important for the user to know.\n  // abstract isDeterministic(): boolean;\n  /**\n   * Groups elements into batches.\n   *\n   * It is assumed that each of the incoming dataset elements has the same\n   * structure -- i.e. the same set of keys at each location in an object\n   * hierarchy.  For each key, the resulting `Dataset` provides a batched\n   * element collecting all of the incoming values for that key.\n   *\n   *  * Incoming primitives are grouped into a 1-D Tensor.\n   *  * Incoming Tensors are grouped into a new Tensor where the 0th axis is\n   *    the batch dimension.\n   *  * Incoming arrays are converted to Tensor and then batched.\n   *  * A nested array is interpreted as an n-D Tensor, so the batched result\n   *    has n+1 dimensions.\n   *  * An array that cannot be converted to Tensor produces an error.\n   *\n   * If an array should not be batched as a unit, it should first be converted\n   * to an object with integer keys.\n   *\n   * Here are a few examples:\n   *\n   * Batch a dataset of numbers:\n   * ```js\n   * const a = tf.data.array([1, 2, 3, 4, 5, 6, 7, 8]).batch(4);\n   * await a.forEachAsync(e => e.print());\n   * ```\n   *\n   * Batch a dataset of arrays:\n   * ```js\n   * const b = tf.data.array([[1], [2], [3], [4], [5], [6], [7], [8]]).batch(4);\n   * await b.forEachAsync(e => e.print());\n   * ```\n   *\n   * Batch a dataset of objects:\n   * ```js\n   * const c = tf.data.array([{a: 1, b: 11}, {a: 2, b: 12}, {a: 3, b: 13},\n   *   {a: 4, b: 14}, {a: 5, b: 15}, {a: 6, b: 16}, {a: 7, b: 17},\n   *   {a: 8, b: 18}]).batch(4);\n   * await c.forEachAsync(e => {\n   *   console.log('{');\n   *   for(var key in e) {\n   *     console.log(key+':');\n   *     e[key].print();\n   *   }\n   *   console.log('}');\n   * })\n   * ```\n   *\n   * @param batchSize The number of elements desired per batch.\n   * @param smallLastBatch Whether to emit the final batch when it has fewer\n   *   than batchSize elements. Default true.\n   * @returns A `Dataset`, from which a stream of batches can be obtained.\n   *\n   * @doc {heading: 'Data', subheading: 'Classes'}\n   */\n  batch(batchSize) {\n    let smallLastBatch = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : true;\n    const base = this;\n    tf.util.assert(batchSize > 0, () => \"batchSize needs to be positive, but it is\\n      \".concat(batchSize));\n    let size;\n    if (this.size === Infinity || this.size == null) {\n      // If the size of this dataset is infinity or null, the new size keeps the\n      // same.\n      size = this.size;\n    } else if (smallLastBatch) {\n      // If the size of this dataset is known and include small last batch, the\n      // new size is full batch count plus last batch.\n      size = Math.ceil(this.size / batchSize);\n    } else {\n      // If the size of this dataset is known and not include small last batch,\n      // the new size is full batch count.\n      size = Math.floor(this.size / batchSize);\n    }\n    return datasetFromIteratorFn(async () => {\n      return (await base.iterator()).columnMajorBatch(batchSize, smallLastBatch, deepBatchConcat);\n    }, size);\n  }\n  /**\n   * Concatenates this `Dataset` with another.\n   *\n   * ```js\n   * const a = tf.data.array([1, 2, 3]);\n   * const b = tf.data.array([4, 5, 6]);\n   * const c = a.concatenate(b);\n   * await c.forEachAsync(e => console.log(e));\n   * ```\n   *\n   * @param dataset A `Dataset` to be concatenated onto this one.\n   * @returns A `Dataset`.\n   *\n   * @doc {heading: 'Data', subheading: 'Classes'}\n   */\n  concatenate(dataset) {\n    const base = this;\n    let size;\n    if (this.size === Infinity || dataset.size === Infinity) {\n      // If the size of any of these two dataset is infinity, new size is\n      // infinity.\n      size = Infinity;\n    } else if (this.size != null && dataset.size != null) {\n      // If the size of both datasets are known and not infinity, new size is\n      // sum the size of these two datasets.\n      size = this.size + dataset.size;\n    } else {\n      // If neither of these two datasets has infinite size and any of these two\n      // datasets' size is null, the new size is null.\n      size = null;\n    }\n    return datasetFromIteratorFn(async () => (await base.iterator()).concatenate(await dataset.iterator()), size);\n  }\n  /**\n   * Filters this dataset according to `predicate`.\n   *\n   * ```js\n   * const a = tf.data.array([1, 2, 3, 4, 5, 6, 7, 8, 9, 10])\n   *   .filter(x => x%2 === 0);\n   * await a.forEachAsync(e => console.log(e));\n   * ```\n   *\n   * @param predicate A function mapping a dataset element to a boolean or a\n   * `Promise` for one.\n   *\n   * @returns A `Dataset` of elements for which the predicate was true.\n   *\n   * @doc {heading: 'Data', subheading: 'Classes'}\n   */\n  filter(predicate) {\n    const base = this;\n    let size;\n    if (this.size === Infinity) {\n      // If the size of this dataset is infinity, new size is infinity\n      size = Infinity;\n    } else {\n      // If this dataset has limited elements, new size is null because it might\n      // exhausted randomly.\n      size = null;\n    }\n    return datasetFromIteratorFn(async () => {\n      return (await base.iterator()).filter(x => tf.tidy(() => predicate(x)));\n    }, size);\n  }\n  /**\n   * Apply a function to every element of the dataset.\n   *\n   * After the function is applied to a dataset element, any Tensors contained\n   * within that element are disposed.\n   *\n   * ```js\n   * const a = tf.data.array([1, 2, 3]);\n   * await a.forEachAsync(e => console.log(e));\n   * ```\n   *\n   * @param f A function to apply to each dataset element.\n   * @returns A `Promise` that resolves after all elements have been processed.\n   *\n   * @doc {heading: 'Data', subheading: 'Classes'}\n   */\n  async forEachAsync(f) {\n    return (await this.iterator()).forEachAsync(f);\n  }\n  /**\n   * Maps this dataset through a 1-to-1 transform.\n   *\n   * ```js\n   * const a = tf.data.array([1, 2, 3]).map(x => x*x);\n   * await a.forEachAsync(e => console.log(e));\n   * ```\n   *\n   * @param transform A function mapping a dataset element to a transformed\n   *   dataset element.\n   *\n   * @returns A `Dataset` of transformed elements.\n   *\n   * @doc {heading: 'Data', subheading: 'Classes'}\n   */\n  map(transform) {\n    const base = this;\n    return datasetFromIteratorFn(async () => {\n      return (await base.iterator()).map(x => tf.tidy(() => transform(x)));\n    }, this.size);\n  }\n  /**\n   * Maps this dataset through an async 1-to-1 transform.\n   *\n   * ```js\n   * const a =\n   *  tf.data.array([1, 2, 3]).mapAsync(x => new Promise(function(resolve){\n   *    setTimeout(() => {\n   *      resolve(x * x);\n   *    }, Math.random()*1000 + 500);\n   *  }));\n   * console.log(await a.toArray());\n   * ```\n   *\n   * @param transform A function mapping a dataset element to a `Promise` for a\n   *   transformed dataset element.  This transform is responsible for disposing\n   *   any intermediate `Tensor`s, i.e. by wrapping its computation in\n   *   `tf.tidy()`; that cannot be automated here (as it is in the synchronous\n   *   `map()` case).\n   *\n   * @returns A `Dataset` of transformed elements.\n   *\n   * @doc {heading: 'Data', subheading: 'Classes'}\n   */\n  mapAsync(transform) {\n    const base = this;\n    return datasetFromIteratorFn(async () => {\n      return (await base.iterator()).mapAsync(transform);\n    }, this.size);\n  }\n  /**\n   *  Creates a `Dataset` that prefetches elements from this dataset.\n   *\n   * @param bufferSize: An integer specifying the number of elements to be\n   *   prefetched.\n   * @returns A `Dataset`.\n   *\n   * @doc {heading: 'Data', subheading: 'Classes'}\n   */\n  prefetch(bufferSize) {\n    if (bufferSize == null) {\n      throw new RangeError('`Dataset.prefetch()` requires bufferSize to be specified.');\n    }\n    const base = this;\n    return datasetFromIteratorFn(async () => (await base.iterator()).prefetch(bufferSize), this.size);\n  }\n  /**\n   * Repeats this dataset `count` times.\n   *\n   * NOTE: If this dataset is a function of global state (e.g. a random number\n   * generator), then different repetitions may produce different elements.\n   *\n   * ```js\n   * const a = tf.data.array([1, 2, 3]).repeat(3);\n   * await a.forEachAsync(e => console.log(e));\n   * ```\n   *\n   * @param count: (Optional) An integer, representing the number of times\n   *   the dataset should be repeated. The default behavior (if `count` is\n   *   `undefined` or negative) is for the dataset be repeated indefinitely.\n   * @returns A `Dataset`.\n   *\n   * @doc {heading: 'Data', subheading: 'Classes'}\n   */\n  repeat(count) {\n    const base = this;\n    let size;\n    if (this.size != null && count > 0) {\n      // If this dataset has size and count is positive, new size is current\n      // size multiply count. This also covers the case that current size is\n      // infinity.\n      size = this.size * count;\n    } else if (count === 0) {\n      // If count is 0, new size is 0.\n      size = 0;\n    } else if (this.size != null && (count === undefined || count < 0)) {\n      // If this dataset has size and count is undefined or negative, the\n      // dataset will be repeated indefinitely and new size is infinity.\n      size = Infinity;\n    } else {\n      // If the size of this dataset is null, the new dataset's size is null.\n      size = null;\n    }\n    return datasetFromIteratorFn(async () => {\n      const iteratorIterator = iteratorFromFunction(async () => ({\n        value: await base.iterator(),\n        done: false\n      }));\n      return iteratorFromConcatenated(iteratorIterator.take(count));\n    }, size);\n  }\n  /**\n   * Creates a `Dataset` that skips `count` initial elements from this dataset.\n   *\n   * ```js\n   * const a = tf.data.array([1, 2, 3, 4, 5, 6]).skip(3);\n   * await a.forEachAsync(e => console.log(e));\n   * ```\n   *\n   * @param count: The number of elements of this dataset that should be skipped\n   *   to form the new dataset.  If `count` is greater than the size of this\n   *   dataset, the new dataset will contain no elements.  If `count`\n   *   is `undefined` or negative, skips the entire dataset.\n   *\n   * @returns A `Dataset`.\n   *\n   * @doc {heading: 'Data', subheading: 'Classes'}\n   */\n  skip(count) {\n    const base = this;\n    let size;\n    if (this.size != null && count >= 0 && this.size >= count) {\n      // If the size of this dataset is greater than count, the new dataset's\n      // size is current size minus skipped size.This also covers the case that\n      // current size is infinity.\n      size = this.size - count;\n    } else if (this.size != null && (this.size < count || count === undefined || count < 0)) {\n      // If the size of this dataset is smaller than count, or count is\n      // undefined or negative, skips the entire dataset and the new size is 0.\n      size = 0;\n    } else {\n      // If the size of this dataset is null, the new dataset's size is null.\n      size = null;\n    }\n    return datasetFromIteratorFn(async () => (await base.iterator()).skip(count), size);\n  }\n  /**\n   * Pseudorandomly shuffles the elements of this dataset. This is done in a\n   * streaming manner, by sampling from a given number of prefetched elements.\n   *\n   * ```js\n   * const a = tf.data.array([1, 2, 3, 4, 5, 6]).shuffle(3);\n   * await a.forEachAsync(e => console.log(e));\n   * ```\n   *\n   * @param bufferSize: An integer specifying the number of elements from this\n   *   dataset from which the new dataset will sample.\n   * @param seed: (Optional) An integer specifying the random seed that will\n   *   be used to create the distribution.\n   * @param reshuffleEachIteration: (Optional) A boolean, which if true\n   *   indicates that the dataset should be pseudorandomly reshuffled each time\n   *   it is iterated over. If false, elements will be returned in the same\n   *   shuffled order on each iteration. (Defaults to `true`.)\n   * @returns A `Dataset`.\n   *\n   * @doc {heading: 'Data', subheading: 'Classes'}\n   */\n  shuffle(bufferSize, seed) {\n    let reshuffleEachIteration = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : true;\n    if (bufferSize == null || bufferSize < 0) {\n      if (this.size == null) {\n        throw new RangeError('`Dataset.shuffle()` requires bufferSize to be specified.');\n      } else {\n        throw new RangeError('`Dataset.shuffle()` requires bufferSize to be specified.  ' + 'If your data fits in main memory (for regular JS objects), ' + 'and/or GPU memory (for `tf.Tensor`s), consider setting ' + \"bufferSize to the dataset size (\".concat(this.size, \" elements)\"));\n      }\n    }\n    const base = this;\n    const random = seedrandom.alea(seed || tf.util.now().toString());\n    return datasetFromIteratorFn(async () => {\n      let seed2 = random.int32();\n      if (reshuffleEachIteration) {\n        seed2 += random.int32();\n      }\n      return (await base.iterator()).shuffle(bufferSize, seed2.toString());\n    }, this.size);\n  }\n  /**\n   * Creates a `Dataset` with at most `count` initial elements from this\n   * dataset.\n   *\n   * ```js\n   * const a = tf.data.array([1, 2, 3, 4, 5, 6]).take(3);\n   * await a.forEachAsync(e => console.log(e));\n   * ```\n   *\n   * @param count: The number of elements of this dataset that should be taken\n   *   to form the new dataset.  If `count` is `undefined` or negative, or if\n   *   `count` is greater than the size of this dataset, the new dataset will\n   *   contain all elements of this dataset.\n   * @returns A `Dataset`.\n   *\n   * @doc {heading: 'Data', subheading: 'Classes'}\n   */\n  take(count) {\n    const base = this;\n    let size;\n    if (this.size != null && this.size > count) {\n      // If the size of this dataset is greater than count, the new dataset's\n      // size is count.\n      size = count;\n    } else if (this.size != null && this.size <= count) {\n      // If the size of this dataset is equal or smaller than count, the new\n      // dataset's size is the size of this dataset.\n      size = this.size;\n    } else {\n      // If the size of this dataset is null, the new dataset's size is null.\n      size = null;\n    }\n    return datasetFromIteratorFn(async () => (await base.iterator()).take(count), size);\n  }\n  /**\n   * Collect all elements of this dataset into an array.\n   *\n   * Obviously this will succeed only for small datasets that fit in memory.\n   * Useful for testing and generally should be avoided if possible.\n   *\n   * ```js\n   * const a = tf.data.array([1, 2, 3, 4, 5, 6]);\n   * console.log(await a.toArray());\n   * ```\n   *\n   * @returns A Promise for an array of elements, which will resolve\n   *   when a new stream has been obtained and fully consumed.\n   *\n   * @doc {heading: 'Data', subheading: 'Classes'}\n   */\n  async toArray() {\n    if (this.size === Infinity) {\n      throw new Error('Can not convert infinite data stream to array.');\n    }\n    return (await this.iterator()).toArray();\n  }\n  /**\n   * Collect all elements of this dataset into an array with prefetching 100\n   * elements. This is useful for testing, because the prefetch changes the\n   * order in which the Promises are resolved along the processing pipeline.\n   * This may help expose bugs where results are dependent on the order of\n   * Promise resolution rather than on the logical order of the stream (i.e.,\n   * due to hidden mutable state).\n   *\n   * @returns A Promise for an array of elements, which will resolve\n   *   when a new stream has been obtained and fully consumed.\n   */\n  async toArrayForTest() {\n    if (this.size === Infinity) {\n      throw new Error('Can not convert infinite data stream to array.');\n    }\n    return (await this.iterator()).toArrayForTest();\n  }\n}\n// TODO(soergel): deep sharded shuffle, where supported\nDataset.MAX_BUFFER_SIZE = 10000;\nexport { Dataset };\n/**\n * Create a `Dataset` defined by a provided iterator() function.\n *\n * ```js\n * let i = -1;\n * const func = () =>\n *    ++i < 5 ? {value: i, done: false} : {value: null, done: true};\n * const iter = tf.data.iteratorFromFunction(func);\n * const ds = tf.data.datasetFromIteratorFn(iter);\n * await ds.forEachAsync(e => console.log(e));\n * ```\n */\nexport function datasetFromIteratorFn(iteratorFn) {\n  let size = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : null;\n  return new class extends Dataset {\n    constructor() {\n      super(...arguments);\n      this.size = size;\n    }\n    /*\n     * Provide a new stream of elements.  Note this will also start new streams\n     * from any underlying `Dataset`s.\n     */\n    async iterator() {\n      return iteratorFn();\n    }\n  }();\n}\n/**\n * Create a `Dataset` from an array of elements.\n *\n * Create a Dataset from an array of objects:\n * ```js\n * const a = tf.data.array([{'item': 1}, {'item': 2}, {'item': 3}]);\n * await a.forEachAsync(e => console.log(e));\n * ```\n *\n * Create a Dataset from an array of numbers:\n * ```js\n * const a = tf.data.array([4, 5, 6]);\n * await a.forEachAsync(e => console.log(e));\n * ```\n * @param items An array of elements that will be parsed as items in a dataset.\n *\n * @doc {heading: 'Data', subheading: 'Creation', namespace: 'data'}\n */\nexport function array(items) {\n  return datasetFromIteratorFn(async () => iteratorFromItems(items), items.length);\n}\n/**\n * Create a `Dataset` by zipping together an array, dict, or nested\n * structure of `Dataset`s (and perhaps additional constants).\n * The underlying datasets must provide elements in a consistent order such that\n * they correspond.\n *\n * The number of elements in the resulting dataset is the same as the size of\n * the smallest dataset in datasets.\n *\n * The nested structure of the `datasets` argument determines the\n * structure of elements in the resulting iterator.\n *\n * Note this means that, given an array of two datasets that produce dict\n * elements, the result is a dataset that produces elements that are arrays\n * of two dicts:\n *\n * Zip an array of datasets:\n * ```js\n * console.log('Zip two datasets of objects:');\n * const ds1 = tf.data.array([{a: 1}, {a: 2}, {a: 3}]);\n * const ds2 = tf.data.array([{b: 4}, {b: 5}, {b: 6}]);\n * const ds3 = tf.data.zip([ds1, ds2]);\n * await ds3.forEachAsync(e => console.log(JSON.stringify(e)));\n *\n * // If the goal is to merge the dicts in order to produce elements like\n * // {a: ..., b: ...}, this requires a second step such as:\n * console.log('Merge the objects:');\n * const ds4 = ds3.map(x => {return {a: x[0].a, b: x[1].b}});\n * await ds4.forEachAsync(e => console.log(e));\n * ```\n *\n * Zip a dict of datasets:\n * ```js\n * const a = tf.data.array([{a: 1}, {a: 2}, {a: 3}]);\n * const b = tf.data.array([{b: 4}, {b: 5}, {b: 6}]);\n * const c = tf.data.zip({c: a, d: b});\n * await c.forEachAsync(e => console.log(JSON.stringify(e)));\n * ```\n *\n * @doc {heading: 'Data', subheading: 'Operations', namespace: 'data'}\n */\nexport function zip(datasets) {\n  // manually type-check the argument for JS users\n  if (!isIterable(datasets)) {\n    throw new Error('The argument to zip() must be an object or array.');\n  }\n  let size;\n  if (Array.isArray(datasets)) {\n    for (let i = 0; i < datasets.length; i++) {\n      size = size == null ? datasets[i].size : Math.min(size, datasets[i].size);\n    }\n  } else if (datasets instanceof Object) {\n    for (const ds in datasets) {\n      size = size == null ? datasets[ds].size : Math.min(size, datasets[ds].size);\n    }\n  }\n  return datasetFromIteratorFn(async () => {\n    const streams = await deepMapAndAwaitAll(datasets, d => {\n      if (d instanceof Dataset) {\n        return {\n          value: d.iterator(),\n          recurse: false\n        };\n      } else if (isIterable(d)) {\n        return {\n          value: null,\n          recurse: true\n        };\n      } else {\n        throw new Error('Leaves of the structure passed to zip() must be Datasets, ' + 'not primitives.');\n      }\n    });\n    return iteratorFromZipped(streams, ZipMismatchMode.SHORTEST);\n  }, size);\n}\n/**\n * A zip function for use with deepZip, passed via the columnMajorBatch call.\n *\n * Accepts an array of identically-structured nested elements and either batches\n * them (if they are primitives, numeric arrays, or Tensors) or requests\n * recursion (if not).\n */\n// tslint:disable-next-line:no-any\nfunction deepBatchConcat(rows) {\n  if (rows === null) {\n    return null;\n  }\n  // use the first item to decide whether to recurse or batch here.\n  const exampleRow = rows[0];\n  if (canTensorify(exampleRow)) {\n    // rows is an array of primitives, Tensors, or arrays.  Batch them.\n    const value = batchConcat(rows);\n    return {\n      value,\n      recurse: false\n    };\n  }\n  // the example row is an object, so recurse into it.\n  return {\n    value: null,\n    recurse: true\n  };\n}\n/**\n * Assembles a list of same-shaped numbers, number arrays, or Tensors\n * into a single new Tensor where axis 0 is the batch dimension.\n */\nfunction batchConcat(arrays) {\n  if (arrays.length === 0) {\n    // We can't return an empty Tensor because we don't know the element shape.\n    throw new Error('Can\\'t make a batch of zero elements.');\n  }\n  if (arrays[0] instanceof tf.Tensor) {\n    // Input is an array of Tensors\n    return tf.stack(arrays);\n  } else {\n    // Input is a possibly-nested array of numbers.\n    return tf.tensor(arrays);\n  }\n}", "map": {"version": 3, "names": ["tf", "seedrandom", "iteratorFromConcatenated", "iteratorFromFunction", "iteratorFromItems", "iteratorFromZipped", "ZipMismatchMode", "canTensorify", "deepMapAndAwaitAll", "isIterable", "Dataset", "constructor", "size", "batch", "batchSize", "smallLastBatch", "arguments", "length", "undefined", "base", "util", "assert", "concat", "Infinity", "Math", "ceil", "floor", "datasetFromIteratorFn", "iterator", "columnMajorBatch", "deepBatchConcat", "concatenate", "dataset", "filter", "predicate", "x", "tidy", "forEachAsync", "f", "map", "transform", "mapAsync", "prefetch", "bufferSize", "RangeError", "repeat", "count", "iteratorIterator", "value", "done", "take", "skip", "shuffle", "seed", "reshuffleEachIteration", "random", "alea", "now", "toString", "seed2", "int32", "toArray", "Error", "toArrayForTest", "MAX_BUFFER_SIZE", "iteratorFn", "array", "items", "zip", "datasets", "Array", "isArray", "i", "min", "Object", "ds", "streams", "d", "recurse", "SHORTEST", "rows", "exampleRow", "batchConcat", "arrays", "Tensor", "stack", "tensor"], "sources": ["C:\\tfjs-data\\src\\dataset.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2018 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n *\n * =============================================================================\n */\n\nimport * as tf from '@tensorflow/tfjs-core';\nimport {TensorContainer, TensorLike} from '@tensorflow/tfjs-core';\nimport * as seedrandom from 'seedrandom';\n\nimport {iteratorFromConcatenated, iteratorFromFunction, iteratorFromItems, iteratorFromZipped, LazyIterator, ZipMismatchMode} from './iterators/lazy_iterator';\nimport {Container} from './types';\nimport {canTensorify, deepMapAndAwaitAll, DeepMapResult, isIterable} from './util/deep_map';\n\n/**\n * A nested structure of Datasets, used as the input to zip().\n */\nexport type DatasetContainer = Container<Dataset<TensorContainer>>;\n\n// TODO(soergel): consider vectorized operations within the pipeline.\n\n/**\n * Represents a potentially large list of independent data elements (typically\n * 'samples' or 'examples').\n *\n * A 'data example' may be a primitive, an array, a map from string keys to\n * values, or any nested structure of these.\n *\n * A `Dataset` represents an ordered collection of elements, together with a\n * chain of transformations to be performed on those elements. Each\n * transformation is a method of `Dataset` that returns another `Dataset`, so\n * these may be chained, e.g.\n * `const processedDataset = rawDataset.filter(...).map(...).batch(...)`.\n *\n * Data loading and transformation is done in a lazy, streaming fashion.  The\n * dataset may be iterated over multiple times; each iteration starts the data\n * loading anew and recapitulates the transformations.\n *\n * A `Dataset` is typically processed as a stream of unbatched examples -- i.e.,\n * its transformations are applied one example at a time. Batching produces a\n * new `Dataset` where each element is a batch. Batching should usually come\n * last in a pipeline, because data transformations are easier to express on a\n * per-example basis than on a per-batch basis.\n *\n * The following code examples are calling `await dataset.forEachAsync(...)` to\n * iterate once over the entire dataset in order to print out the data.\n *\n * @doc {heading: 'Data', subheading: 'Classes', namespace: 'data'}\n */\nexport abstract class Dataset<T extends tf.TensorContainer> {\n  /*\n   * Provide a new stream of elements.  Note this will also start new streams\n   * from any underlying `Dataset`s.\n   *\n   * CAUTION: Any Tensors contained within the elements returned from\n   * this stream *must* be manually disposed to avoid a GPU memory leak.\n   * The tf.tidy() approach cannot be used in an asynchronous context.\n   */\n  abstract iterator(): Promise<LazyIterator<T>>;\n\n  readonly size: number = null;\n\n  // TODO(soergel): Make Datasets report whether repeated iterator() calls\n  // produce the same result (e.g., reading from a file) or different results\n  // (e.g., from the webcam).  Currently we don't make this distinction but it\n  // could be important for the user to know.\n  // abstract isDeterministic(): boolean;\n\n  /**\n   * Groups elements into batches.\n   *\n   * It is assumed that each of the incoming dataset elements has the same\n   * structure -- i.e. the same set of keys at each location in an object\n   * hierarchy.  For each key, the resulting `Dataset` provides a batched\n   * element collecting all of the incoming values for that key.\n   *\n   *  * Incoming primitives are grouped into a 1-D Tensor.\n   *  * Incoming Tensors are grouped into a new Tensor where the 0th axis is\n   *    the batch dimension.\n   *  * Incoming arrays are converted to Tensor and then batched.\n   *  * A nested array is interpreted as an n-D Tensor, so the batched result\n   *    has n+1 dimensions.\n   *  * An array that cannot be converted to Tensor produces an error.\n   *\n   * If an array should not be batched as a unit, it should first be converted\n   * to an object with integer keys.\n   *\n   * Here are a few examples:\n   *\n   * Batch a dataset of numbers:\n   * ```js\n   * const a = tf.data.array([1, 2, 3, 4, 5, 6, 7, 8]).batch(4);\n   * await a.forEachAsync(e => e.print());\n   * ```\n   *\n   * Batch a dataset of arrays:\n   * ```js\n   * const b = tf.data.array([[1], [2], [3], [4], [5], [6], [7], [8]]).batch(4);\n   * await b.forEachAsync(e => e.print());\n   * ```\n   *\n   * Batch a dataset of objects:\n   * ```js\n   * const c = tf.data.array([{a: 1, b: 11}, {a: 2, b: 12}, {a: 3, b: 13},\n   *   {a: 4, b: 14}, {a: 5, b: 15}, {a: 6, b: 16}, {a: 7, b: 17},\n   *   {a: 8, b: 18}]).batch(4);\n   * await c.forEachAsync(e => {\n   *   console.log('{');\n   *   for(var key in e) {\n   *     console.log(key+':');\n   *     e[key].print();\n   *   }\n   *   console.log('}');\n   * })\n   * ```\n   *\n   * @param batchSize The number of elements desired per batch.\n   * @param smallLastBatch Whether to emit the final batch when it has fewer\n   *   than batchSize elements. Default true.\n   * @returns A `Dataset`, from which a stream of batches can be obtained.\n   *\n   * @doc {heading: 'Data', subheading: 'Classes'}\n   */\n  batch(batchSize: number, smallLastBatch = true): Dataset<tf.TensorContainer> {\n    const base = this;\n    tf.util.assert(\n        batchSize > 0, () => `batchSize needs to be positive, but it is\n      ${batchSize}`);\n    let size;\n    if (this.size === Infinity || this.size == null) {\n      // If the size of this dataset is infinity or null, the new size keeps the\n      // same.\n      size = this.size;\n    } else if (smallLastBatch) {\n      // If the size of this dataset is known and include small last batch, the\n      // new size is full batch count plus last batch.\n      size = Math.ceil(this.size / batchSize);\n    } else {\n      // If the size of this dataset is known and not include small last batch,\n      // the new size is full batch count.\n      size = Math.floor(this.size / batchSize);\n    }\n    return datasetFromIteratorFn(async () => {\n      return (await base.iterator())\n          .columnMajorBatch(batchSize, smallLastBatch, deepBatchConcat);\n    }, size);\n  }\n\n  /**\n   * Concatenates this `Dataset` with another.\n   *\n   * ```js\n   * const a = tf.data.array([1, 2, 3]);\n   * const b = tf.data.array([4, 5, 6]);\n   * const c = a.concatenate(b);\n   * await c.forEachAsync(e => console.log(e));\n   * ```\n   *\n   * @param dataset A `Dataset` to be concatenated onto this one.\n   * @returns A `Dataset`.\n   *\n   * @doc {heading: 'Data', subheading: 'Classes'}\n   */\n  concatenate(dataset: Dataset<T>): Dataset<T> {\n    const base = this;\n    let size;\n    if (this.size === Infinity || dataset.size === Infinity) {\n      // If the size of any of these two dataset is infinity, new size is\n      // infinity.\n      size = Infinity;\n    } else if (this.size != null && dataset.size != null) {\n      // If the size of both datasets are known and not infinity, new size is\n      // sum the size of these two datasets.\n      size = this.size + dataset.size;\n    } else {\n      // If neither of these two datasets has infinite size and any of these two\n      // datasets' size is null, the new size is null.\n      size = null;\n    }\n    return datasetFromIteratorFn(\n        async () =>\n            (await base.iterator()).concatenate(await dataset.iterator()),\n        size);\n  }\n\n  /**\n   * Filters this dataset according to `predicate`.\n   *\n   * ```js\n   * const a = tf.data.array([1, 2, 3, 4, 5, 6, 7, 8, 9, 10])\n   *   .filter(x => x%2 === 0);\n   * await a.forEachAsync(e => console.log(e));\n   * ```\n   *\n   * @param predicate A function mapping a dataset element to a boolean or a\n   * `Promise` for one.\n   *\n   * @returns A `Dataset` of elements for which the predicate was true.\n   *\n   * @doc {heading: 'Data', subheading: 'Classes'}\n   */\n  filter(predicate: (value: T) => boolean): Dataset<T> {\n    const base = this;\n    let size;\n    if (this.size === Infinity) {\n      // If the size of this dataset is infinity, new size is infinity\n      size = Infinity;\n    } else {\n      // If this dataset has limited elements, new size is null because it might\n      // exhausted randomly.\n      size = null;\n    }\n    return datasetFromIteratorFn(async () => {\n      return (await base.iterator()).filter(x => tf.tidy(() => predicate(x)));\n    }, size);\n  }\n\n  /**\n   * Apply a function to every element of the dataset.\n   *\n   * After the function is applied to a dataset element, any Tensors contained\n   * within that element are disposed.\n   *\n   * ```js\n   * const a = tf.data.array([1, 2, 3]);\n   * await a.forEachAsync(e => console.log(e));\n   * ```\n   *\n   * @param f A function to apply to each dataset element.\n   * @returns A `Promise` that resolves after all elements have been processed.\n   *\n   * @doc {heading: 'Data', subheading: 'Classes'}\n   */\n  async forEachAsync(f: (input: T) => void): Promise<void> {\n    return (await this.iterator()).forEachAsync(f);\n  }\n\n  /**\n   * Maps this dataset through a 1-to-1 transform.\n   *\n   * ```js\n   * const a = tf.data.array([1, 2, 3]).map(x => x*x);\n   * await a.forEachAsync(e => console.log(e));\n   * ```\n   *\n   * @param transform A function mapping a dataset element to a transformed\n   *   dataset element.\n   *\n   * @returns A `Dataset` of transformed elements.\n   *\n   * @doc {heading: 'Data', subheading: 'Classes'}\n   */\n  map<O extends tf.TensorContainer>(transform: (value: T) => O): Dataset<O> {\n    const base = this;\n    return datasetFromIteratorFn(async () => {\n      return (await base.iterator()).map(x => tf.tidy(() => transform(x)));\n    }, this.size);\n  }\n\n  /**\n   * Maps this dataset through an async 1-to-1 transform.\n   *\n   * ```js\n   * const a =\n   *  tf.data.array([1, 2, 3]).mapAsync(x => new Promise(function(resolve){\n   *    setTimeout(() => {\n   *      resolve(x * x);\n   *    }, Math.random()*1000 + 500);\n   *  }));\n   * console.log(await a.toArray());\n   * ```\n   *\n   * @param transform A function mapping a dataset element to a `Promise` for a\n   *   transformed dataset element.  This transform is responsible for disposing\n   *   any intermediate `Tensor`s, i.e. by wrapping its computation in\n   *   `tf.tidy()`; that cannot be automated here (as it is in the synchronous\n   *   `map()` case).\n   *\n   * @returns A `Dataset` of transformed elements.\n   *\n   * @doc {heading: 'Data', subheading: 'Classes'}\n   */\n  mapAsync<O extends tf.TensorContainer>(transform: (value: T) => Promise<O>):\n      Dataset<O> {\n    const base = this;\n    return datasetFromIteratorFn(async () => {\n      return (await base.iterator()).mapAsync(transform);\n    }, this.size);\n  }\n\n  /**\n   *  Creates a `Dataset` that prefetches elements from this dataset.\n   *\n   * @param bufferSize: An integer specifying the number of elements to be\n   *   prefetched.\n   * @returns A `Dataset`.\n   *\n   * @doc {heading: 'Data', subheading: 'Classes'}\n   */\n  prefetch(bufferSize: number): Dataset<T> {\n    if (bufferSize == null) {\n      throw new RangeError(\n          '`Dataset.prefetch()` requires bufferSize to be specified.');\n    }\n\n    const base = this;\n    return datasetFromIteratorFn(\n        async () => (await base.iterator()).prefetch(bufferSize), this.size);\n  }\n\n  /**\n   * Repeats this dataset `count` times.\n   *\n   * NOTE: If this dataset is a function of global state (e.g. a random number\n   * generator), then different repetitions may produce different elements.\n   *\n   * ```js\n   * const a = tf.data.array([1, 2, 3]).repeat(3);\n   * await a.forEachAsync(e => console.log(e));\n   * ```\n   *\n   * @param count: (Optional) An integer, representing the number of times\n   *   the dataset should be repeated. The default behavior (if `count` is\n   *   `undefined` or negative) is for the dataset be repeated indefinitely.\n   * @returns A `Dataset`.\n   *\n   * @doc {heading: 'Data', subheading: 'Classes'}\n   */\n  repeat(count?: number): Dataset<T> {\n    const base = this;\n    let size;\n    if (this.size != null && count > 0) {\n      // If this dataset has size and count is positive, new size is current\n      // size multiply count. This also covers the case that current size is\n      // infinity.\n      size = this.size * count;\n    } else if (count === 0) {\n      // If count is 0, new size is 0.\n      size = 0;\n    } else if (this.size != null && (count === undefined || count < 0)) {\n      // If this dataset has size and count is undefined or negative, the\n      // dataset will be repeated indefinitely and new size is infinity.\n      size = Infinity;\n    } else {\n      // If the size of this dataset is null, the new dataset's size is null.\n      size = null;\n    }\n    return datasetFromIteratorFn(async () => {\n      const iteratorIterator = iteratorFromFunction(\n          async () => ({value: await base.iterator(), done: false}));\n      return iteratorFromConcatenated(iteratorIterator.take(count));\n    }, size);\n  }\n\n  /**\n   * Creates a `Dataset` that skips `count` initial elements from this dataset.\n   *\n   * ```js\n   * const a = tf.data.array([1, 2, 3, 4, 5, 6]).skip(3);\n   * await a.forEachAsync(e => console.log(e));\n   * ```\n   *\n   * @param count: The number of elements of this dataset that should be skipped\n   *   to form the new dataset.  If `count` is greater than the size of this\n   *   dataset, the new dataset will contain no elements.  If `count`\n   *   is `undefined` or negative, skips the entire dataset.\n   *\n   * @returns A `Dataset`.\n   *\n   * @doc {heading: 'Data', subheading: 'Classes'}\n   */\n  skip(count: number): Dataset<T> {\n    const base = this;\n    let size;\n    if (this.size != null && count >= 0 && this.size >= count) {\n      // If the size of this dataset is greater than count, the new dataset's\n      // size is current size minus skipped size.This also covers the case that\n      // current size is infinity.\n      size = this.size - count;\n    } else if (\n        this.size != null &&\n        (this.size < count || count === undefined || count < 0)) {\n      // If the size of this dataset is smaller than count, or count is\n      // undefined or negative, skips the entire dataset and the new size is 0.\n      size = 0;\n    } else {\n      // If the size of this dataset is null, the new dataset's size is null.\n      size = null;\n    }\n    return datasetFromIteratorFn(\n        async () => (await base.iterator()).skip(count), size);\n  }\n\n  // TODO(soergel): deep sharded shuffle, where supported\n\n  static readonly MAX_BUFFER_SIZE = 10000;\n\n  /**\n   * Pseudorandomly shuffles the elements of this dataset. This is done in a\n   * streaming manner, by sampling from a given number of prefetched elements.\n   *\n   * ```js\n   * const a = tf.data.array([1, 2, 3, 4, 5, 6]).shuffle(3);\n   * await a.forEachAsync(e => console.log(e));\n   * ```\n   *\n   * @param bufferSize: An integer specifying the number of elements from this\n   *   dataset from which the new dataset will sample.\n   * @param seed: (Optional) An integer specifying the random seed that will\n   *   be used to create the distribution.\n   * @param reshuffleEachIteration: (Optional) A boolean, which if true\n   *   indicates that the dataset should be pseudorandomly reshuffled each time\n   *   it is iterated over. If false, elements will be returned in the same\n   *   shuffled order on each iteration. (Defaults to `true`.)\n   * @returns A `Dataset`.\n   *\n   * @doc {heading: 'Data', subheading: 'Classes'}\n   */\n  shuffle(bufferSize: number, seed?: string, reshuffleEachIteration = true):\n      Dataset<T> {\n    if (bufferSize == null || bufferSize < 0) {\n      if (this.size == null) {\n        throw new RangeError(\n            '`Dataset.shuffle()` requires bufferSize to be specified.');\n      } else {\n        throw new RangeError(\n            '`Dataset.shuffle()` requires bufferSize to be specified.  ' +\n            'If your data fits in main memory (for regular JS objects), ' +\n            'and/or GPU memory (for `tf.Tensor`s), consider setting ' +\n            `bufferSize to the dataset size (${this.size} elements)`);\n      }\n    }\n    const base = this;\n    const random = seedrandom.alea(seed || tf.util.now().toString());\n    return datasetFromIteratorFn(async () => {\n      let seed2 = random.int32();\n      if (reshuffleEachIteration) {\n        seed2 += random.int32();\n      }\n      return (await base.iterator()).shuffle(bufferSize, seed2.toString());\n    }, this.size);\n  }\n\n  /**\n   * Creates a `Dataset` with at most `count` initial elements from this\n   * dataset.\n   *\n   * ```js\n   * const a = tf.data.array([1, 2, 3, 4, 5, 6]).take(3);\n   * await a.forEachAsync(e => console.log(e));\n   * ```\n   *\n   * @param count: The number of elements of this dataset that should be taken\n   *   to form the new dataset.  If `count` is `undefined` or negative, or if\n   *   `count` is greater than the size of this dataset, the new dataset will\n   *   contain all elements of this dataset.\n   * @returns A `Dataset`.\n   *\n   * @doc {heading: 'Data', subheading: 'Classes'}\n   */\n  take(count: number): Dataset<T> {\n    const base = this;\n    let size;\n    if (this.size != null && this.size > count) {\n      // If the size of this dataset is greater than count, the new dataset's\n      // size is count.\n      size = count;\n    } else if (this.size != null && this.size <= count) {\n      // If the size of this dataset is equal or smaller than count, the new\n      // dataset's size is the size of this dataset.\n      size = this.size;\n    } else {\n      // If the size of this dataset is null, the new dataset's size is null.\n      size = null;\n    }\n    return datasetFromIteratorFn(\n        async () => (await base.iterator()).take(count), size);\n  }\n\n  /**\n   * Collect all elements of this dataset into an array.\n   *\n   * Obviously this will succeed only for small datasets that fit in memory.\n   * Useful for testing and generally should be avoided if possible.\n   *\n   * ```js\n   * const a = tf.data.array([1, 2, 3, 4, 5, 6]);\n   * console.log(await a.toArray());\n   * ```\n   *\n   * @returns A Promise for an array of elements, which will resolve\n   *   when a new stream has been obtained and fully consumed.\n   *\n   * @doc {heading: 'Data', subheading: 'Classes'}\n   */\n  async toArray() {\n    if (this.size === Infinity) {\n      throw new Error('Can not convert infinite data stream to array.');\n    }\n    return (await this.iterator()).toArray();\n  }\n\n  /**\n   * Collect all elements of this dataset into an array with prefetching 100\n   * elements. This is useful for testing, because the prefetch changes the\n   * order in which the Promises are resolved along the processing pipeline.\n   * This may help expose bugs where results are dependent on the order of\n   * Promise resolution rather than on the logical order of the stream (i.e.,\n   * due to hidden mutable state).\n   *\n   * @returns A Promise for an array of elements, which will resolve\n   *   when a new stream has been obtained and fully consumed.\n   */\n  async toArrayForTest() {\n    if (this.size === Infinity) {\n      throw new Error('Can not convert infinite data stream to array.');\n    }\n    return (await this.iterator()).toArrayForTest();\n  }\n}\n\n/**\n * Create a `Dataset` defined by a provided iterator() function.\n *\n * ```js\n * let i = -1;\n * const func = () =>\n *    ++i < 5 ? {value: i, done: false} : {value: null, done: true};\n * const iter = tf.data.iteratorFromFunction(func);\n * const ds = tf.data.datasetFromIteratorFn(iter);\n * await ds.forEachAsync(e => console.log(e));\n * ```\n */\nexport function datasetFromIteratorFn<T extends tf.TensorContainer>(\n    iteratorFn: () => Promise<LazyIterator<T>>,\n    size: number = null): Dataset<T> {\n  return new class extends Dataset<T> {\n    override size = size;\n\n    /*\n     * Provide a new stream of elements.  Note this will also start new streams\n     * from any underlying `Dataset`s.\n     */\n    async iterator(): Promise<LazyIterator<T>> {\n      return iteratorFn();\n    }\n  }\n  ();\n}\n\n/**\n * Create a `Dataset` from an array of elements.\n *\n * Create a Dataset from an array of objects:\n * ```js\n * const a = tf.data.array([{'item': 1}, {'item': 2}, {'item': 3}]);\n * await a.forEachAsync(e => console.log(e));\n * ```\n *\n * Create a Dataset from an array of numbers:\n * ```js\n * const a = tf.data.array([4, 5, 6]);\n * await a.forEachAsync(e => console.log(e));\n * ```\n * @param items An array of elements that will be parsed as items in a dataset.\n *\n * @doc {heading: 'Data', subheading: 'Creation', namespace: 'data'}\n */\nexport function array<T extends tf.TensorContainer>(items: T[]): Dataset<T> {\n  return datasetFromIteratorFn(\n      async () => iteratorFromItems(items), items.length);\n}\n\n/**\n * Create a `Dataset` by zipping together an array, dict, or nested\n * structure of `Dataset`s (and perhaps additional constants).\n * The underlying datasets must provide elements in a consistent order such that\n * they correspond.\n *\n * The number of elements in the resulting dataset is the same as the size of\n * the smallest dataset in datasets.\n *\n * The nested structure of the `datasets` argument determines the\n * structure of elements in the resulting iterator.\n *\n * Note this means that, given an array of two datasets that produce dict\n * elements, the result is a dataset that produces elements that are arrays\n * of two dicts:\n *\n * Zip an array of datasets:\n * ```js\n * console.log('Zip two datasets of objects:');\n * const ds1 = tf.data.array([{a: 1}, {a: 2}, {a: 3}]);\n * const ds2 = tf.data.array([{b: 4}, {b: 5}, {b: 6}]);\n * const ds3 = tf.data.zip([ds1, ds2]);\n * await ds3.forEachAsync(e => console.log(JSON.stringify(e)));\n *\n * // If the goal is to merge the dicts in order to produce elements like\n * // {a: ..., b: ...}, this requires a second step such as:\n * console.log('Merge the objects:');\n * const ds4 = ds3.map(x => {return {a: x[0].a, b: x[1].b}});\n * await ds4.forEachAsync(e => console.log(e));\n * ```\n *\n * Zip a dict of datasets:\n * ```js\n * const a = tf.data.array([{a: 1}, {a: 2}, {a: 3}]);\n * const b = tf.data.array([{b: 4}, {b: 5}, {b: 6}]);\n * const c = tf.data.zip({c: a, d: b});\n * await c.forEachAsync(e => console.log(JSON.stringify(e)));\n * ```\n *\n * @doc {heading: 'Data', subheading: 'Operations', namespace: 'data'}\n */\nexport function zip<O extends tf.TensorContainer>(datasets: DatasetContainer):\n    Dataset<O> {\n  // manually type-check the argument for JS users\n  if (!isIterable(datasets)) {\n    throw new Error('The argument to zip() must be an object or array.');\n  }\n  let size;\n  if (Array.isArray(datasets)) {\n    for (let i = 0; i < datasets.length; i++) {\n      size = size == null ? (datasets[i] as Dataset<O>).size :\n                            Math.min(size, (datasets[i] as Dataset<O>).size);\n    }\n  } else if (datasets instanceof Object) {\n    for (const ds in datasets) {\n      size = size == null ? (datasets[ds] as Dataset<O>).size :\n                            Math.min(size, (datasets[ds] as Dataset<O>).size);\n    }\n  }\n  return datasetFromIteratorFn<O>(async () => {\n    const streams = await deepMapAndAwaitAll(datasets, d => {\n      if (d instanceof Dataset) {\n        return {value: d.iterator(), recurse: false};\n      } else if (isIterable(d)) {\n        return {value: null, recurse: true};\n      } else {\n        throw new Error(\n            'Leaves of the structure passed to zip() must be Datasets, ' +\n            'not primitives.');\n      }\n    });\n    return iteratorFromZipped<O>(streams, ZipMismatchMode.SHORTEST);\n  }, size);\n}\n\n/**\n * A zip function for use with deepZip, passed via the columnMajorBatch call.\n *\n * Accepts an array of identically-structured nested elements and either batches\n * them (if they are primitives, numeric arrays, or Tensors) or requests\n * recursion (if not).\n */\n// tslint:disable-next-line:no-any\nfunction deepBatchConcat(rows: any[]): DeepMapResult {\n  if (rows === null) {\n    return null;\n  }\n\n  // use the first item to decide whether to recurse or batch here.\n  const exampleRow = rows[0];\n\n  if (canTensorify(exampleRow)) {\n    // rows is an array of primitives, Tensors, or arrays.  Batch them.\n    const value = batchConcat(rows);\n    return {value, recurse: false};\n  }\n\n  // the example row is an object, so recurse into it.\n  return {value: null, recurse: true};\n}\n\n/**\n * Assembles a list of same-shaped numbers, number arrays, or Tensors\n * into a single new Tensor where axis 0 is the batch dimension.\n */\nfunction batchConcat<T extends(TensorLike | tf.Tensor)>(arrays: T[]):\n    tf.Tensor {\n  if (arrays.length === 0) {\n    // We can't return an empty Tensor because we don't know the element shape.\n    throw new Error('Can\\'t make a batch of zero elements.');\n  }\n\n  if (arrays[0] instanceof tf.Tensor) {\n    // Input is an array of Tensors\n    return tf.stack(arrays as tf.Tensor[]);\n  } else {\n    // Input is a possibly-nested array of numbers.\n    return tf.tensor(arrays as TensorLike);\n  }\n}\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;;AAkBA,OAAO,KAAKA,EAAE,MAAM,uBAAuB;AAE3C,OAAO,KAAKC,UAAU,MAAM,YAAY;AAExC,SAAQC,wBAAwB,EAAEC,oBAAoB,EAAEC,iBAAiB,EAAEC,kBAAkB,EAAgBC,eAAe,QAAO,2BAA2B;AAE9J,SAAQC,YAAY,EAAEC,kBAAkB,EAAiBC,UAAU,QAAO,iBAAiB;AAO3F;AAEA;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4BA,MAAsBC,OAAO;EAA7BC,YAAA;IAWW,KAAAC,IAAI,GAAW,IAAI;EA2c9B;EAzcE;EACA;EACA;EACA;EACA;EAEA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EAuDAC,KAAKA,CAACC,SAAiB,EAAuB;IAAA,IAArBC,cAAc,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,IAAI;IAC5C,MAAMG,IAAI,GAAG,IAAI;IACjBnB,EAAE,CAACoB,IAAI,CAACC,MAAM,CACVP,SAAS,GAAG,CAAC,EAAE,0DAAAQ,MAAA,CACfR,SAAS,CAAE,CAAC;IAChB,IAAIF,IAAI;IACR,IAAI,IAAI,CAACA,IAAI,KAAKW,QAAQ,IAAI,IAAI,CAACX,IAAI,IAAI,IAAI,EAAE;MAC/C;MACA;MACAA,IAAI,GAAG,IAAI,CAACA,IAAI;KACjB,MAAM,IAAIG,cAAc,EAAE;MACzB;MACA;MACAH,IAAI,GAAGY,IAAI,CAACC,IAAI,CAAC,IAAI,CAACb,IAAI,GAAGE,SAAS,CAAC;KACxC,MAAM;MACL;MACA;MACAF,IAAI,GAAGY,IAAI,CAACE,KAAK,CAAC,IAAI,CAACd,IAAI,GAAGE,SAAS,CAAC;;IAE1C,OAAOa,qBAAqB,CAAC,YAAW;MACtC,OAAO,CAAC,MAAMR,IAAI,CAACS,QAAQ,EAAE,EACxBC,gBAAgB,CAACf,SAAS,EAAEC,cAAc,EAAEe,eAAe,CAAC;IACnE,CAAC,EAAElB,IAAI,CAAC;EACV;EAEA;;;;;;;;;;;;;;;EAeAmB,WAAWA,CAACC,OAAmB;IAC7B,MAAMb,IAAI,GAAG,IAAI;IACjB,IAAIP,IAAI;IACR,IAAI,IAAI,CAACA,IAAI,KAAKW,QAAQ,IAAIS,OAAO,CAACpB,IAAI,KAAKW,QAAQ,EAAE;MACvD;MACA;MACAX,IAAI,GAAGW,QAAQ;KAChB,MAAM,IAAI,IAAI,CAACX,IAAI,IAAI,IAAI,IAAIoB,OAAO,CAACpB,IAAI,IAAI,IAAI,EAAE;MACpD;MACA;MACAA,IAAI,GAAG,IAAI,CAACA,IAAI,GAAGoB,OAAO,CAACpB,IAAI;KAChC,MAAM;MACL;MACA;MACAA,IAAI,GAAG,IAAI;;IAEb,OAAOe,qBAAqB,CACxB,YACI,CAAC,MAAMR,IAAI,CAACS,QAAQ,EAAE,EAAEG,WAAW,CAAC,MAAMC,OAAO,CAACJ,QAAQ,EAAE,CAAC,EACjEhB,IAAI,CAAC;EACX;EAEA;;;;;;;;;;;;;;;;EAgBAqB,MAAMA,CAACC,SAAgC;IACrC,MAAMf,IAAI,GAAG,IAAI;IACjB,IAAIP,IAAI;IACR,IAAI,IAAI,CAACA,IAAI,KAAKW,QAAQ,EAAE;MAC1B;MACAX,IAAI,GAAGW,QAAQ;KAChB,MAAM;MACL;MACA;MACAX,IAAI,GAAG,IAAI;;IAEb,OAAOe,qBAAqB,CAAC,YAAW;MACtC,OAAO,CAAC,MAAMR,IAAI,CAACS,QAAQ,EAAE,EAAEK,MAAM,CAACE,CAAC,IAAInC,EAAE,CAACoC,IAAI,CAAC,MAAMF,SAAS,CAACC,CAAC,CAAC,CAAC,CAAC;IACzE,CAAC,EAAEvB,IAAI,CAAC;EACV;EAEA;;;;;;;;;;;;;;;;EAgBA,MAAMyB,YAAYA,CAACC,CAAqB;IACtC,OAAO,CAAC,MAAM,IAAI,CAACV,QAAQ,EAAE,EAAES,YAAY,CAACC,CAAC,CAAC;EAChD;EAEA;;;;;;;;;;;;;;;EAeAC,GAAGA,CAA+BC,SAA0B;IAC1D,MAAMrB,IAAI,GAAG,IAAI;IACjB,OAAOQ,qBAAqB,CAAC,YAAW;MACtC,OAAO,CAAC,MAAMR,IAAI,CAACS,QAAQ,EAAE,EAAEW,GAAG,CAACJ,CAAC,IAAInC,EAAE,CAACoC,IAAI,CAAC,MAAMI,SAAS,CAACL,CAAC,CAAC,CAAC,CAAC;IACtE,CAAC,EAAE,IAAI,CAACvB,IAAI,CAAC;EACf;EAEA;;;;;;;;;;;;;;;;;;;;;;;EAuBA6B,QAAQA,CAA+BD,SAAmC;IAExE,MAAMrB,IAAI,GAAG,IAAI;IACjB,OAAOQ,qBAAqB,CAAC,YAAW;MACtC,OAAO,CAAC,MAAMR,IAAI,CAACS,QAAQ,EAAE,EAAEa,QAAQ,CAACD,SAAS,CAAC;IACpD,CAAC,EAAE,IAAI,CAAC5B,IAAI,CAAC;EACf;EAEA;;;;;;;;;EASA8B,QAAQA,CAACC,UAAkB;IACzB,IAAIA,UAAU,IAAI,IAAI,EAAE;MACtB,MAAM,IAAIC,UAAU,CAChB,2DAA2D,CAAC;;IAGlE,MAAMzB,IAAI,GAAG,IAAI;IACjB,OAAOQ,qBAAqB,CACxB,YAAY,CAAC,MAAMR,IAAI,CAACS,QAAQ,EAAE,EAAEc,QAAQ,CAACC,UAAU,CAAC,EAAE,IAAI,CAAC/B,IAAI,CAAC;EAC1E;EAEA;;;;;;;;;;;;;;;;;;EAkBAiC,MAAMA,CAACC,KAAc;IACnB,MAAM3B,IAAI,GAAG,IAAI;IACjB,IAAIP,IAAI;IACR,IAAI,IAAI,CAACA,IAAI,IAAI,IAAI,IAAIkC,KAAK,GAAG,CAAC,EAAE;MAClC;MACA;MACA;MACAlC,IAAI,GAAG,IAAI,CAACA,IAAI,GAAGkC,KAAK;KACzB,MAAM,IAAIA,KAAK,KAAK,CAAC,EAAE;MACtB;MACAlC,IAAI,GAAG,CAAC;KACT,MAAM,IAAI,IAAI,CAACA,IAAI,IAAI,IAAI,KAAKkC,KAAK,KAAK5B,SAAS,IAAI4B,KAAK,GAAG,CAAC,CAAC,EAAE;MAClE;MACA;MACAlC,IAAI,GAAGW,QAAQ;KAChB,MAAM;MACL;MACAX,IAAI,GAAG,IAAI;;IAEb,OAAOe,qBAAqB,CAAC,YAAW;MACtC,MAAMoB,gBAAgB,GAAG5C,oBAAoB,CACzC,aAAa;QAAC6C,KAAK,EAAE,MAAM7B,IAAI,CAACS,QAAQ,EAAE;QAAEqB,IAAI,EAAE;MAAK,CAAC,CAAC,CAAC;MAC9D,OAAO/C,wBAAwB,CAAC6C,gBAAgB,CAACG,IAAI,CAACJ,KAAK,CAAC,CAAC;IAC/D,CAAC,EAAElC,IAAI,CAAC;EACV;EAEA;;;;;;;;;;;;;;;;;EAiBAuC,IAAIA,CAACL,KAAa;IAChB,MAAM3B,IAAI,GAAG,IAAI;IACjB,IAAIP,IAAI;IACR,IAAI,IAAI,CAACA,IAAI,IAAI,IAAI,IAAIkC,KAAK,IAAI,CAAC,IAAI,IAAI,CAAClC,IAAI,IAAIkC,KAAK,EAAE;MACzD;MACA;MACA;MACAlC,IAAI,GAAG,IAAI,CAACA,IAAI,GAAGkC,KAAK;KACzB,MAAM,IACH,IAAI,CAAClC,IAAI,IAAI,IAAI,KAChB,IAAI,CAACA,IAAI,GAAGkC,KAAK,IAAIA,KAAK,KAAK5B,SAAS,IAAI4B,KAAK,GAAG,CAAC,CAAC,EAAE;MAC3D;MACA;MACAlC,IAAI,GAAG,CAAC;KACT,MAAM;MACL;MACAA,IAAI,GAAG,IAAI;;IAEb,OAAOe,qBAAqB,CACxB,YAAY,CAAC,MAAMR,IAAI,CAACS,QAAQ,EAAE,EAAEuB,IAAI,CAACL,KAAK,CAAC,EAAElC,IAAI,CAAC;EAC5D;EAMA;;;;;;;;;;;;;;;;;;;;;EAqBAwC,OAAOA,CAACT,UAAkB,EAAEU,IAAa,EAA+B;IAAA,IAA7BC,sBAAsB,GAAAtC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,IAAI;IAEtE,IAAI2B,UAAU,IAAI,IAAI,IAAIA,UAAU,GAAG,CAAC,EAAE;MACxC,IAAI,IAAI,CAAC/B,IAAI,IAAI,IAAI,EAAE;QACrB,MAAM,IAAIgC,UAAU,CAChB,0DAA0D,CAAC;OAChE,MAAM;QACL,MAAM,IAAIA,UAAU,CAChB,4DAA4D,GAC5D,6DAA6D,GAC7D,yDAAyD,sCAAAtB,MAAA,CACtB,IAAI,CAACV,IAAI,eAAY,CAAC;;;IAGjE,MAAMO,IAAI,GAAG,IAAI;IACjB,MAAMoC,MAAM,GAAGtD,UAAU,CAACuD,IAAI,CAACH,IAAI,IAAIrD,EAAE,CAACoB,IAAI,CAACqC,GAAG,EAAE,CAACC,QAAQ,EAAE,CAAC;IAChE,OAAO/B,qBAAqB,CAAC,YAAW;MACtC,IAAIgC,KAAK,GAAGJ,MAAM,CAACK,KAAK,EAAE;MAC1B,IAAIN,sBAAsB,EAAE;QAC1BK,KAAK,IAAIJ,MAAM,CAACK,KAAK,EAAE;;MAEzB,OAAO,CAAC,MAAMzC,IAAI,CAACS,QAAQ,EAAE,EAAEwB,OAAO,CAACT,UAAU,EAAEgB,KAAK,CAACD,QAAQ,EAAE,CAAC;IACtE,CAAC,EAAE,IAAI,CAAC9C,IAAI,CAAC;EACf;EAEA;;;;;;;;;;;;;;;;;EAiBAsC,IAAIA,CAACJ,KAAa;IAChB,MAAM3B,IAAI,GAAG,IAAI;IACjB,IAAIP,IAAI;IACR,IAAI,IAAI,CAACA,IAAI,IAAI,IAAI,IAAI,IAAI,CAACA,IAAI,GAAGkC,KAAK,EAAE;MAC1C;MACA;MACAlC,IAAI,GAAGkC,KAAK;KACb,MAAM,IAAI,IAAI,CAAClC,IAAI,IAAI,IAAI,IAAI,IAAI,CAACA,IAAI,IAAIkC,KAAK,EAAE;MAClD;MACA;MACAlC,IAAI,GAAG,IAAI,CAACA,IAAI;KACjB,MAAM;MACL;MACAA,IAAI,GAAG,IAAI;;IAEb,OAAOe,qBAAqB,CACxB,YAAY,CAAC,MAAMR,IAAI,CAACS,QAAQ,EAAE,EAAEsB,IAAI,CAACJ,KAAK,CAAC,EAAElC,IAAI,CAAC;EAC5D;EAEA;;;;;;;;;;;;;;;;EAgBA,MAAMiD,OAAOA,CAAA;IACX,IAAI,IAAI,CAACjD,IAAI,KAAKW,QAAQ,EAAE;MAC1B,MAAM,IAAIuC,KAAK,CAAC,gDAAgD,CAAC;;IAEnE,OAAO,CAAC,MAAM,IAAI,CAAClC,QAAQ,EAAE,EAAEiC,OAAO,EAAE;EAC1C;EAEA;;;;;;;;;;;EAWA,MAAME,cAAcA,CAAA;IAClB,IAAI,IAAI,CAACnD,IAAI,KAAKW,QAAQ,EAAE;MAC1B,MAAM,IAAIuC,KAAK,CAAC,gDAAgD,CAAC;;IAEnE,OAAO,CAAC,MAAM,IAAI,CAAClC,QAAQ,EAAE,EAAEmC,cAAc,EAAE;EACjD;;AA7HA;AAEgBrD,OAAA,CAAAsD,eAAe,GAAG,KAAK;SA1VnBtD,OAAO;AAwd7B;;;;;;;;;;;;AAYA,OAAM,SAAUiB,qBAAqBA,CACjCsC,UAA0C,EACvB;EAAA,IAAnBrD,IAAA,GAAAI,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAe,IAAI;EACrB,OAAO,IAAI,cAAcN,OAAU;IAAxBC,YAAA;;MACA,KAAAC,IAAI,GAAGA,IAAI;IAStB;IAPE;;;;IAIA,MAAMgB,QAAQA,CAAA;MACZ,OAAOqC,UAAU,EAAE;IACrB;GACD,EACC;AACJ;AAEA;;;;;;;;;;;;;;;;;;AAkBA,OAAM,SAAUC,KAAKA,CAA+BC,KAAU;EAC5D,OAAOxC,qBAAqB,CACxB,YAAYvB,iBAAiB,CAAC+D,KAAK,CAAC,EAAEA,KAAK,CAAClD,MAAM,CAAC;AACzD;AAEA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyCA,OAAM,SAAUmD,GAAGA,CAA+BC,QAA0B;EAE1E;EACA,IAAI,CAAC5D,UAAU,CAAC4D,QAAQ,CAAC,EAAE;IACzB,MAAM,IAAIP,KAAK,CAAC,mDAAmD,CAAC;;EAEtE,IAAIlD,IAAI;EACR,IAAI0D,KAAK,CAACC,OAAO,CAACF,QAAQ,CAAC,EAAE;IAC3B,KAAK,IAAIG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,QAAQ,CAACpD,MAAM,EAAEuD,CAAC,EAAE,EAAE;MACxC5D,IAAI,GAAGA,IAAI,IAAI,IAAI,GAAIyD,QAAQ,CAACG,CAAC,CAAgB,CAAC5D,IAAI,GAChCY,IAAI,CAACiD,GAAG,CAAC7D,IAAI,EAAGyD,QAAQ,CAACG,CAAC,CAAgB,CAAC5D,IAAI,CAAC;;GAEzE,MAAM,IAAIyD,QAAQ,YAAYK,MAAM,EAAE;IACrC,KAAK,MAAMC,EAAE,IAAIN,QAAQ,EAAE;MACzBzD,IAAI,GAAGA,IAAI,IAAI,IAAI,GAAIyD,QAAQ,CAACM,EAAE,CAAgB,CAAC/D,IAAI,GACjCY,IAAI,CAACiD,GAAG,CAAC7D,IAAI,EAAGyD,QAAQ,CAACM,EAAE,CAAgB,CAAC/D,IAAI,CAAC;;;EAG3E,OAAOe,qBAAqB,CAAI,YAAW;IACzC,MAAMiD,OAAO,GAAG,MAAMpE,kBAAkB,CAAC6D,QAAQ,EAAEQ,CAAC,IAAG;MACrD,IAAIA,CAAC,YAAYnE,OAAO,EAAE;QACxB,OAAO;UAACsC,KAAK,EAAE6B,CAAC,CAACjD,QAAQ,EAAE;UAAEkD,OAAO,EAAE;QAAK,CAAC;OAC7C,MAAM,IAAIrE,UAAU,CAACoE,CAAC,CAAC,EAAE;QACxB,OAAO;UAAC7B,KAAK,EAAE,IAAI;UAAE8B,OAAO,EAAE;QAAI,CAAC;OACpC,MAAM;QACL,MAAM,IAAIhB,KAAK,CACX,4DAA4D,GAC5D,iBAAiB,CAAC;;IAE1B,CAAC,CAAC;IACF,OAAOzD,kBAAkB,CAAIuE,OAAO,EAAEtE,eAAe,CAACyE,QAAQ,CAAC;EACjE,CAAC,EAAEnE,IAAI,CAAC;AACV;AAEA;;;;;;;AAOA;AACA,SAASkB,eAAeA,CAACkD,IAAW;EAClC,IAAIA,IAAI,KAAK,IAAI,EAAE;IACjB,OAAO,IAAI;;EAGb;EACA,MAAMC,UAAU,GAAGD,IAAI,CAAC,CAAC,CAAC;EAE1B,IAAIzE,YAAY,CAAC0E,UAAU,CAAC,EAAE;IAC5B;IACA,MAAMjC,KAAK,GAAGkC,WAAW,CAACF,IAAI,CAAC;IAC/B,OAAO;MAAChC,KAAK;MAAE8B,OAAO,EAAE;IAAK,CAAC;;EAGhC;EACA,OAAO;IAAC9B,KAAK,EAAE,IAAI;IAAE8B,OAAO,EAAE;EAAI,CAAC;AACrC;AAEA;;;;AAIA,SAASI,WAAWA,CAAoCC,MAAW;EAEjE,IAAIA,MAAM,CAAClE,MAAM,KAAK,CAAC,EAAE;IACvB;IACA,MAAM,IAAI6C,KAAK,CAAC,uCAAuC,CAAC;;EAG1D,IAAIqB,MAAM,CAAC,CAAC,CAAC,YAAYnF,EAAE,CAACoF,MAAM,EAAE;IAClC;IACA,OAAOpF,EAAE,CAACqF,KAAK,CAACF,MAAqB,CAAC;GACvC,MAAM;IACL;IACA,OAAOnF,EAAE,CAACsF,MAAM,CAACH,MAAoB,CAAC;;AAE1C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}