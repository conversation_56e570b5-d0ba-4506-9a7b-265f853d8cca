{"ast": null, "code": "import _ from './underscore.js';\n\n// Start chaining a wrapped Underscore object.\nexport default function chain(obj) {\n  var instance = _(obj);\n  instance._chain = true;\n  return instance;\n}", "map": {"version": 3, "names": ["_", "chain", "obj", "instance", "_chain"], "sources": ["C:/tmsft/node_modules/underscore/modules/chain.js"], "sourcesContent": ["import _ from './underscore.js';\n\n// Start chaining a wrapped Underscore object.\nexport default function chain(obj) {\n  var instance = _(obj);\n  instance._chain = true;\n  return instance;\n}\n"], "mappings": "AAAA,OAAOA,CAAC,MAAM,iBAAiB;;AAE/B;AACA,eAAe,SAASC,KAAKA,CAACC,GAAG,EAAE;EACjC,IAAIC,QAAQ,GAAGH,CAAC,CAACE,GAAG,CAAC;EACrBC,QAAQ,CAACC,MAAM,GAAG,IAAI;EACtB,OAAOD,QAAQ;AACjB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}