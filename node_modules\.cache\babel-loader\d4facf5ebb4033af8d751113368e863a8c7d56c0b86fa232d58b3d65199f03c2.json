{"ast": null, "code": "/**\n * @license\n * Copyright 2018 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { dispose, io, Tensor, util } from '@tensorflow/tfjs-core';\nimport { OperationMapper } from '../operations/operation_mapper';\nimport { GraphExecutor } from './graph_executor';\nimport { ResourceManager } from './resource_manager';\n// tslint:disable-next-line: no-imports-from-dist\nimport { decodeWeightsStream } from '@tensorflow/tfjs-core/dist/io/io_utils';\nexport const TFHUB_SEARCH_PARAM = '?tfjs-format=file';\nexport const DEFAULT_MODEL_NAME = 'model.json';\n/**\n * A `tf.GraphModel` is a directed, acyclic graph built from a\n * SavedModel GraphDef and allows inference execution.\n *\n * A `tf.GraphModel` can only be created by loading from a model converted from\n * a [TensorFlow SavedModel](https://www.tensorflow.org/guide/saved_model) using\n * the command line converter tool and loaded via `tf.loadGraphModel`.\n *\n * @doc {heading: 'Models', subheading: 'Classes'}\n */\nexport class GraphModel {\n  // Returns the version information for the tensorflow model GraphDef.\n  get modelVersion() {\n    return this.version;\n  }\n  get inputNodes() {\n    return this.executor.inputNodes;\n  }\n  get outputNodes() {\n    return this.executor.outputNodes;\n  }\n  get inputs() {\n    return this.executor.inputs;\n  }\n  get outputs() {\n    return this.executor.outputs;\n  }\n  get weights() {\n    return this.executor.weightMap;\n  }\n  get metadata() {\n    return this.artifacts.userDefinedMetadata;\n  }\n  get modelSignature() {\n    return this.signature;\n  }\n  get modelStructuredOutputKeys() {\n    return this.structuredOutputKeys;\n  }\n  /**\n   * @param modelUrl url for the model, or an `io.IOHandler`.\n   * @param weightManifestUrl url for the weight file generated by\n   * scripts/convert.py script.\n   * @param requestOption options for Request, which allows to send credentials\n   * and custom headers.\n   * @param onProgress Optional, progress callback function, fired periodically\n   * before the load is completed.\n   */\n  constructor(modelUrl) {\n    let loadOptions = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    let tfio = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : io;\n    this.modelUrl = modelUrl;\n    this.loadOptions = loadOptions;\n    this.version = 'n/a';\n    this.io = tfio;\n    if (loadOptions == null) {\n      this.loadOptions = {};\n    }\n    this.resourceManager = new ResourceManager();\n  }\n  findIOHandler() {\n    const path = this.modelUrl;\n    if (path.load != null) {\n      // Path is an IO Handler.\n      this.handler = path;\n    } else if (this.loadOptions.requestInit != null) {\n      this.handler = this.io.browserHTTPRequest(path, this.loadOptions);\n    } else {\n      const handlers = this.io.getLoadHandlers(path, this.loadOptions);\n      if (handlers.length === 0) {\n        // For backward compatibility: if no load handler can be found,\n        // assume it is a relative http path.\n        handlers.push(this.io.browserHTTPRequest(path, this.loadOptions));\n      } else if (handlers.length > 1) {\n        throw new Error(\"Found more than one (\".concat(handlers.length, \") load handlers for \") + \"URL '\".concat([path], \"'\"));\n      }\n      this.handler = handlers[0];\n    }\n  }\n  /**\n   * Loads the model and weight files, construct the in memory weight map and\n   * compile the inference graph.\n   */\n  load() {\n    this.findIOHandler();\n    if (this.handler.load == null) {\n      throw new Error('Cannot proceed with model loading because the IOHandler provided ' + 'does not have the `load` method implemented.');\n    }\n    const loadResult = this.handler.load();\n    if (util.isPromise(loadResult)) {\n      return loadResult.then(artifacts => {\n        if (artifacts.getWeightStream == null) {\n          return this.loadSync(artifacts);\n        }\n        return this.loadStreaming(artifacts);\n      });\n    }\n    return this.loadSync(loadResult);\n  }\n  /**\n   * Synchronously construct the in memory weight map and\n   * compile the inference graph.\n   *\n   * @doc {heading: 'Models', subheading: 'Classes', ignoreCI: true}\n   */\n  loadSync(artifacts) {\n    const weightMap = this.io.decodeWeights(artifacts.weightData, artifacts.weightSpecs);\n    return this.loadWithWeightMap(artifacts, weightMap);\n  }\n  async loadStreaming(artifacts) {\n    if (artifacts.getWeightStream == null) {\n      throw new Error('Model artifacts missing streamWeights function');\n    }\n    const weightMap = await decodeWeightsStream(artifacts.getWeightStream(), artifacts.weightSpecs);\n    return this.loadWithWeightMap(artifacts, weightMap);\n  }\n  loadWithWeightMap(artifacts, weightMap) {\n    this.artifacts = artifacts;\n    const graph = this.artifacts.modelTopology;\n    let signature = this.artifacts.signature;\n    if (this.artifacts.userDefinedMetadata != null) {\n      const metadata = this.artifacts.userDefinedMetadata;\n      if (metadata.signature != null) {\n        signature = metadata.signature;\n      }\n      if (metadata.structuredOutputKeys != null) {\n        this.structuredOutputKeys = metadata.structuredOutputKeys;\n      }\n    }\n    this.signature = signature;\n    this.version = \"\".concat(graph.versions.producer, \".\").concat(graph.versions.minConsumer);\n    this.executor = new GraphExecutor(OperationMapper.Instance.transformGraph(graph, this.signature));\n    this.executor.weightMap = this.convertTensorMapToTensorsMap(weightMap);\n    // Attach a model-level resourceManager to each executor to share resources,\n    // such as `HashTable`.\n    this.executor.resourceManager = this.resourceManager;\n    if (artifacts.modelInitializer != null && artifacts.modelInitializer.node != null) {\n      const initializer = OperationMapper.Instance.transformGraph(artifacts.modelInitializer);\n      this.initializer = new GraphExecutor(initializer);\n      this.initializer.weightMap = this.executor.weightMap;\n      // Attach a model-level resourceManager to the initializer, the\n      // hashTables created from when executing the initializer will be stored\n      // in the resourceManager.\n      this.initializer.resourceManager = this.resourceManager;\n      this.initializerSignature = artifacts.initializerSignature;\n    }\n    return true;\n  }\n  /**\n   * Save the configuration and/or weights of the GraphModel.\n   *\n   * An `IOHandler` is an object that has a `save` method of the proper\n   * signature defined. The `save` method manages the storing or\n   * transmission of serialized data (\"artifacts\") that represent the\n   * model's topology and weights onto or via a specific medium, such as\n   * file downloads, local storage, IndexedDB in the web browser and HTTP\n   * requests to a server. TensorFlow.js provides `IOHandler`\n   * implementations for a number of frequently used saving mediums, such as\n   * `tf.io.browserDownloads` and `tf.io.browserLocalStorage`. See `tf.io`\n   * for more details.\n   *\n   * This method also allows you to refer to certain types of `IOHandler`s\n   * as URL-like string shortcuts, such as 'localstorage://' and\n   * 'indexeddb://'.\n   *\n   * Example 1: Save `model`'s topology and weights to browser [local\n   * storage](https://developer.mozilla.org/en-US/docs/Web/API/Window/localStorage);\n   * then load it back.\n   *\n   * ```js\n   * const modelUrl =\n   *    'https://storage.googleapis.com/tfjs-models/savedmodel/mobilenet_v2_1.0_224/model.json';\n   * const model = await tf.loadGraphModel(modelUrl);\n   * const zeros = tf.zeros([1, 224, 224, 3]);\n   * model.predict(zeros).print();\n   *\n   * const saveResults = await model.save('localstorage://my-model-1');\n   *\n   * const loadedModel = await tf.loadGraphModel('localstorage://my-model-1');\n   * console.log('Prediction from loaded model:');\n   * model.predict(zeros).print();\n   * ```\n   *\n   * @param handlerOrURL An instance of `IOHandler` or a URL-like,\n   * scheme-based string shortcut for `IOHandler`.\n   * @param config Options for saving the model.\n   * @returns A `Promise` of `SaveResult`, which summarizes the result of\n   * the saving, such as byte sizes of the saved artifacts for the model's\n   *   topology and weight values.\n   *\n   * @doc {heading: 'Models', subheading: 'Classes', ignoreCI: true}\n   */\n  async save(handlerOrURL, config) {\n    if (typeof handlerOrURL === 'string') {\n      const handlers = this.io.getSaveHandlers(handlerOrURL);\n      if (handlers.length === 0) {\n        throw new Error(\"Cannot find any save handlers for URL '\".concat(handlerOrURL, \"'\"));\n      } else if (handlers.length > 1) {\n        throw new Error(\"Found more than one (\".concat(handlers.length, \") save handlers for \") + \"URL '\".concat(handlerOrURL, \"'\"));\n      }\n      handlerOrURL = handlers[0];\n    }\n    if (handlerOrURL.save == null) {\n      throw new Error('GraphModel.save() cannot proceed because the IOHandler ' + 'provided does not have the `save` attribute defined.');\n    }\n    return handlerOrURL.save(this.artifacts);\n  }\n  addStructuredOutputNames(outputTensors) {\n    if (this.structuredOutputKeys) {\n      const outputTensorsArray = outputTensors instanceof Tensor ? [outputTensors] : outputTensors;\n      const outputTensorMap = {};\n      outputTensorsArray.forEach((outputTensor, i) => outputTensorMap[this.structuredOutputKeys[i]] = outputTensor);\n      return outputTensorMap;\n    }\n    return outputTensors;\n  }\n  /**\n   * Execute the inference for the input tensors.\n   *\n   * @param input The input tensors, when there is single input for the model,\n   * inputs param should be a `tf.Tensor`. For models with multiple inputs,\n   * inputs params should be in either `tf.Tensor`[] if the input order is\n   * fixed, or otherwise NamedTensorMap format.\n   *\n   * For model with multiple inputs, we recommend you use NamedTensorMap as the\n   * input type, if you use `tf.Tensor`[], the order of the array needs to\n   * follow the\n   * order of inputNodes array. @see {@link GraphModel.inputNodes}\n   *\n   * You can also feed any intermediate nodes using the NamedTensorMap as the\n   * input type. For example, given the graph\n   *    InputNode => Intermediate => OutputNode,\n   * you can execute the subgraph Intermediate => OutputNode by calling\n   *    model.execute('IntermediateNode' : tf.tensor(...));\n   *\n   * This is useful for models that uses tf.dynamic_rnn, where the intermediate\n   * state needs to be fed manually.\n   *\n   * For batch inference execution, the tensors for each input need to be\n   * concatenated together. For example with mobilenet, the required input shape\n   * is [1, 244, 244, 3], which represents the [batch, height, width, channel].\n   * If we are provide a batched data of 100 images, the input tensor should be\n   * in the shape of [100, 244, 244, 3].\n   *\n   * @param config Prediction configuration for specifying the batch size.\n   * Currently the batch size option is ignored for graph model.\n   *\n   * @returns Inference result tensors. If the model is converted and it\n   * originally had structured_outputs in tensorflow, then a NamedTensorMap\n   * will be returned matching the structured_outputs. If no structured_outputs\n   * are present, the output will be single `tf.Tensor` if the model has single\n   * output node, otherwise Tensor[].\n   *\n   * @doc {heading: 'Models', subheading: 'Classes'}\n   */\n  predict(inputs, config) {\n    const outputTensors = this.execute(inputs, this.outputNodes);\n    return this.addStructuredOutputNames(outputTensors);\n  }\n  /**\n   * Execute the inference for the input tensors in async fashion, use this\n   * method when your model contains control flow ops.\n   *\n   * @param input The input tensors, when there is single input for the model,\n   * inputs param should be a `tf.Tensor`. For models with mutliple inputs,\n   * inputs params should be in either `tf.Tensor`[] if the input order is\n   * fixed, or otherwise NamedTensorMap format.\n   *\n   * For model with multiple inputs, we recommend you use NamedTensorMap as the\n   * input type, if you use `tf.Tensor`[], the order of the array needs to\n   * follow the\n   * order of inputNodes array. @see {@link GraphModel.inputNodes}\n   *\n   * You can also feed any intermediate nodes using the NamedTensorMap as the\n   * input type. For example, given the graph\n   *    InputNode => Intermediate => OutputNode,\n   * you can execute the subgraph Intermediate => OutputNode by calling\n   *    model.execute('IntermediateNode' : tf.tensor(...));\n   *\n   * This is useful for models that uses tf.dynamic_rnn, where the intermediate\n   * state needs to be fed manually.\n   *\n   * For batch inference execution, the tensors for each input need to be\n   * concatenated together. For example with mobilenet, the required input shape\n   * is [1, 244, 244, 3], which represents the [batch, height, width, channel].\n   * If we are provide a batched data of 100 images, the input tensor should be\n   * in the shape of [100, 244, 244, 3].\n   *\n   * @param config Prediction configuration for specifying the batch size.\n   * Currently the batch size option is ignored for graph model.\n   *\n   * @returns A Promise of inference result tensors. If the model is converted\n   * and it originally had structured_outputs in tensorflow, then a\n   * NamedTensorMap will be returned matching the structured_outputs. If no\n   * structured_outputs are present, the output will be single `tf.Tensor` if\n   * the model has single output node, otherwise Tensor[].\n   *\n   * @doc {heading: 'Models', subheading: 'Classes'}\n   */\n  async predictAsync(inputs, config) {\n    const outputTensors = await this.executeAsync(inputs, this.outputNodes);\n    return this.addStructuredOutputNames(outputTensors);\n  }\n  normalizeInputs(inputs) {\n    var _a;\n    if (!(inputs instanceof Tensor) && !Array.isArray(inputs)) {\n      // The input is already a NamedTensorMap.\n      const signatureInputs = (_a = this.signature) === null || _a === void 0 ? void 0 : _a.inputs;\n      if (signatureInputs != null) {\n        for (const input in signatureInputs) {\n          const tensor = signatureInputs[input];\n          if (tensor.resourceId != null) {\n            inputs[input] = this.resourceIdToCapturedInput[tensor.resourceId];\n          }\n        }\n      }\n      return inputs;\n    }\n    inputs = Array.isArray(inputs) ? inputs : [inputs];\n    const numCapturedInputs = Object.keys(this.resourceIdToCapturedInput).length;\n    if (inputs.length + numCapturedInputs !== this.inputNodes.length) {\n      throw new Error(\"Input tensor count mismatch, the graph model has \".concat(this.inputNodes.length - numCapturedInputs, \" non-resource placeholders, while there are \").concat(inputs.length, \" input tensors provided.\"));\n    }\n    let inputIndex = 0;\n    return this.inputNodes.reduce((map, inputName) => {\n      var _a, _b, _c;\n      const resourceId = (_c = (_b = (_a = this.signature) === null || _a === void 0 ? void 0 : _a.inputs) === null || _b === void 0 ? void 0 : _b[inputName]) === null || _c === void 0 ? void 0 : _c.resourceId;\n      if (resourceId != null) {\n        map[inputName] = this.resourceIdToCapturedInput[resourceId];\n      } else {\n        map[inputName] = inputs[inputIndex++];\n      }\n      return map;\n    }, {});\n  }\n  normalizeOutputs(outputs) {\n    outputs = outputs || this.outputNodes;\n    return !Array.isArray(outputs) ? [outputs] : outputs;\n  }\n  executeInitializerGraph() {\n    if (this.initializer == null) {\n      return [];\n    }\n    if (this.initializerSignature == null) {\n      return this.initializer.execute({}, []);\n    } else {\n      return this.initializer.execute({}, Object.keys(this.initializerSignature.outputs));\n    }\n  }\n  async executeInitializerGraphAsync() {\n    if (this.initializer == null) {\n      return [];\n    }\n    if (this.initializerSignature == null) {\n      return this.initializer.executeAsync({}, []);\n    } else {\n      return this.initializer.executeAsync({}, Object.keys(this.initializerSignature.outputs));\n    }\n  }\n  setResourceIdToCapturedInput(outputs) {\n    this.resourceIdToCapturedInput = {};\n    if (this.initializerSignature) {\n      const signatureOutputs = this.initializerSignature.outputs;\n      const outputNames = Object.keys(signatureOutputs);\n      for (let i = 0; i < outputNames.length; i++) {\n        const outputName = outputNames[i];\n        const tensorInfo = signatureOutputs[outputName];\n        this.resourceIdToCapturedInput[tensorInfo.resourceId] = outputs[i];\n      }\n    }\n  }\n  /**\n   * Executes inference for the model for given input tensors.\n   * @param inputs tensor, tensor array or tensor map of the inputs for the\n   * model, keyed by the input node names.\n   * @param outputs output node name from the TensorFlow model, if no\n   * outputs are specified, the default outputs of the model would be used.\n   * You can inspect intermediate nodes of the model by adding them to the\n   * outputs array.\n   *\n   * @returns A single tensor if provided with a single output or no outputs\n   * are provided and there is only one default output, otherwise return a\n   * tensor array. The order of the tensor array is the same as the outputs\n   * if provided, otherwise the order of outputNodes attribute of the model.\n   *\n   * @doc {heading: 'Models', subheading: 'Classes'}\n   */\n  execute(inputs, outputs) {\n    if (this.resourceIdToCapturedInput == null) {\n      this.setResourceIdToCapturedInput(this.executeInitializerGraph());\n    }\n    inputs = this.normalizeInputs(inputs);\n    outputs = this.normalizeOutputs(outputs);\n    const result = this.executor.execute(inputs, outputs);\n    return result.length > 1 ? result : result[0];\n  }\n  /**\n   * Executes inference for the model for given input tensors in async\n   * fashion, use this method when your model contains control flow ops.\n   * @param inputs tensor, tensor array or tensor map of the inputs for the\n   * model, keyed by the input node names.\n   * @param outputs output node name from the TensorFlow model, if no outputs\n   * are specified, the default outputs of the model would be used. You can\n   * inspect intermediate nodes of the model by adding them to the outputs\n   * array.\n   *\n   * @returns A Promise of single tensor if provided with a single output or\n   * no outputs are provided and there is only one default output, otherwise\n   * return a tensor map.\n   *\n   * @doc {heading: 'Models', subheading: 'Classes'}\n   */\n  async executeAsync(inputs, outputs) {\n    if (this.resourceIdToCapturedInput == null) {\n      this.setResourceIdToCapturedInput(await this.executeInitializerGraphAsync());\n    }\n    inputs = this.normalizeInputs(inputs);\n    outputs = this.normalizeOutputs(outputs);\n    const result = await this.executor.executeAsync(inputs, outputs);\n    return result.length > 1 ? result : result[0];\n  }\n  /**\n   * Get intermediate tensors for model debugging mode (flag\n   * KEEP_INTERMEDIATE_TENSORS is true).\n   *\n   * @doc {heading: 'Models', subheading: 'Classes'}\n   */\n  getIntermediateTensors() {\n    return this.executor.getIntermediateTensors();\n  }\n  /**\n   * Dispose intermediate tensors for model debugging mode (flag\n   * KEEP_INTERMEDIATE_TENSORS is true).\n   *\n   * @doc {heading: 'Models', subheading: 'Classes'}\n   */\n  disposeIntermediateTensors() {\n    this.executor.disposeIntermediateTensors();\n  }\n  convertTensorMapToTensorsMap(map) {\n    return Object.keys(map).reduce((newMap, key) => {\n      newMap[key] = [map[key]];\n      return newMap;\n    }, {});\n  }\n  /**\n   * Releases the memory used by the weight tensors and resourceManager.\n   *\n   * @doc {heading: 'Models', subheading: 'Classes'}\n   */\n  dispose() {\n    this.executor.dispose();\n    if (this.initializer) {\n      this.initializer.dispose();\n      if (this.resourceIdToCapturedInput) {\n        dispose(this.resourceIdToCapturedInput);\n      }\n    }\n    this.resourceManager.dispose();\n  }\n}\n/**\n * Load a graph model given a URL to the model definition.\n *\n * Example of loading MobileNetV2 from a URL and making a prediction with a\n * zeros input:\n *\n * ```js\n * const modelUrl =\n *    'https://storage.googleapis.com/tfjs-models/savedmodel/mobilenet_v2_1.0_224/model.json';\n * const model = await tf.loadGraphModel(modelUrl);\n * const zeros = tf.zeros([1, 224, 224, 3]);\n * model.predict(zeros).print();\n * ```\n *\n * Example of loading MobileNetV2 from a TF Hub URL and making a prediction\n * with a zeros input:\n *\n * ```js\n * const modelUrl =\n *    'https://tfhub.dev/google/imagenet/mobilenet_v2_140_224/classification/2';\n * const model = await tf.loadGraphModel(modelUrl, {fromTFHub: true});\n * const zeros = tf.zeros([1, 224, 224, 3]);\n * model.predict(zeros).print();\n * ```\n * @param modelUrl The url or an `io.IOHandler` that loads the model.\n * @param options Options for the HTTP request, which allows to send\n *     credentials\n *    and custom headers.\n *\n * @doc {heading: 'Models', subheading: 'Loading'}\n */\nexport async function loadGraphModel(modelUrl) {\n  let options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n  let tfio = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : io;\n  if (modelUrl == null) {\n    throw new Error('modelUrl in loadGraphModel() cannot be null. Please provide a url ' + 'or an IOHandler that loads the model');\n  }\n  if (options == null) {\n    options = {};\n  }\n  if (options.fromTFHub && typeof modelUrl === 'string') {\n    modelUrl = getTFHubUrl(modelUrl);\n  }\n  const model = new GraphModel(modelUrl, options, tfio);\n  await model.load();\n  return model;\n}\n/**\n * Load a graph model given a synchronous IO handler with a 'load' method.\n *\n * @param modelSource The `io.IOHandlerSync` that loads the model, or the\n *     `io.ModelArtifacts` that encode the model, or a tuple of\n *     `[io.ModelJSON, ArrayBuffer]` of which the first element encodes the\n *      model and the second contains the weights.\n *\n * @doc {heading: 'Models', subheading: 'Loading'}\n */\nexport function loadGraphModelSync(modelSource) {\n  if (modelSource == null) {\n    throw new Error('modelUrl in loadGraphModelSync() cannot be null. Please provide ' + 'model artifacts or an IOHandler that loads the model');\n  }\n  let ioHandler;\n  if (modelSource instanceof Array) {\n    const [modelJSON, weights] = modelSource;\n    if (!modelJSON) {\n      throw new Error('modelJSON must be the first element of the array');\n    }\n    if (!weights || !(weights instanceof ArrayBuffer)) {\n      throw new Error('An ArrayBuffer of weights must be the second element of' + ' the array');\n    }\n    if (!('modelTopology' in modelJSON)) {\n      throw new Error('Model JSON is missing \\'modelTopology\\'');\n    }\n    if (!('weightsManifest' in modelJSON)) {\n      throw new Error('Model JSON is missing \\'weightsManifest\\'');\n    }\n    const weightSpecs = io.getWeightSpecs(modelJSON.weightsManifest);\n    const modelArtifacts = io.getModelArtifactsForJSONSync(modelJSON, weightSpecs, weights);\n    ioHandler = io.fromMemorySync(modelArtifacts);\n  } else if ('load' in modelSource) {\n    // Then modelSource is already an IOHandlerSync.\n    ioHandler = modelSource;\n  } else if ('modelTopology' in modelSource && 'weightSpecs' in modelSource && 'weightData' in modelSource) {\n    // modelSource is of type ModelArtifacts.\n    ioHandler = io.fromMemorySync(modelSource);\n  } else {\n    throw new Error('Unknown model format');\n  }\n  const model = new GraphModel(ioHandler);\n  model.load();\n  return model;\n}\nfunction getTFHubUrl(modelUrl) {\n  if (!modelUrl.endsWith('/')) {\n    modelUrl = modelUrl + '/';\n  }\n  return \"\".concat(modelUrl).concat(DEFAULT_MODEL_NAME).concat(TFHUB_SEARCH_PARAM);\n}", "map": {"version": 3, "names": ["dispose", "io", "Tensor", "util", "OperationMapper", "GraphExecutor", "ResourceManager", "decodeWeightsStream", "TFHUB_SEARCH_PARAM", "DEFAULT_MODEL_NAME", "GraphModel", "modelVersion", "version", "inputNodes", "executor", "outputNodes", "inputs", "outputs", "weights", "weightMap", "metadata", "artifacts", "userDefinedMetadata", "modelSignature", "signature", "modelStructuredOutputKeys", "structuredOutputKeys", "constructor", "modelUrl", "loadOptions", "arguments", "length", "undefined", "tfio", "resourceManager", "find<PERSON><PERSON><PERSON>ler", "path", "load", "handler", "requestInit", "browserHTTPRequest", "handlers", "getLoadHandlers", "push", "Error", "concat", "loadResult", "isPromise", "then", "getWeightStream", "loadSync", "loadStreaming", "decodeWeights", "weightData", "weightSpecs", "loadWithWeightMap", "graph", "modelTopology", "versions", "producer", "minConsumer", "Instance", "transformGraph", "convertTensorMapToTensorsMap", "modelInitializer", "node", "initializer", "initializerSignature", "save", "handlerOrURL", "config", "getSaveHandlers", "addStructuredOutputNames", "outputTensors", "outputTensorsArray", "outputTensorMap", "for<PERSON>ach", "outputTensor", "i", "predict", "execute", "predictAsync", "executeAsync", "normalizeInputs", "Array", "isArray", "signatureInputs", "_a", "input", "tensor", "resourceId", "resourceIdToCapturedInput", "numCapturedInputs", "Object", "keys", "inputIndex", "reduce", "map", "inputName", "_c", "_b", "normalizeOutputs", "executeInitializerGraph", "executeInitializerGraphAsync", "setResourceIdToCapturedInput", "signatureOutputs", "outputNames", "outputName", "tensorInfo", "result", "getIntermediateTensors", "disposeIntermediateTensors", "newMap", "key", "loadGraphModel", "options", "fromTFHub", "getTFHubUrl", "model", "loadGraphModelSync", "modelSource", "ioHandler", "modelJSON", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "getWeightSpecs", "weightsManifest", "modelArtifacts", "getModelArtifactsForJSONSync", "fromMemorySync", "endsWith"], "sources": ["C:\\tfjs-converter\\src\\executor\\graph_model.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2018 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {dispose, InferenceModel, io, ModelPredictConfig, NamedTensorMap, Tensor, util} from '@tensorflow/tfjs-core';\n\nimport * as tensorflow from '../data/compiled_api';\nimport {NamedTensorsMap, TensorInfo} from '../data/types';\nimport {OperationMapper} from '../operations/operation_mapper';\n\nimport {GraphExecutor} from './graph_executor';\nimport {ResourceManager} from './resource_manager';\n// tslint:disable-next-line: no-imports-from-dist\nimport {decodeWeightsStream} from '@tensorflow/tfjs-core/dist/io/io_utils';\n\nexport const TFHUB_SEARCH_PARAM = '?tfjs-format=file';\nexport const DEFAULT_MODEL_NAME = 'model.json';\ntype Url = string|io.IOHandler|io.IOHandlerSync;\ntype UrlIOHandler<T extends Url> = T extends string ? io.IOHandler : T;\n\n/**\n * A `tf.GraphModel` is a directed, acyclic graph built from a\n * SavedModel GraphDef and allows inference execution.\n *\n * A `tf.GraphModel` can only be created by loading from a model converted from\n * a [TensorFlow SavedModel](https://www.tensorflow.org/guide/saved_model) using\n * the command line converter tool and loaded via `tf.loadGraphModel`.\n *\n * @doc {heading: 'Models', subheading: 'Classes'}\n */\nexport class GraphModel<ModelURL extends Url = string | io.IOHandler> implements\n    InferenceModel {\n  private executor: GraphExecutor;\n  private version = 'n/a';\n  private handler: UrlIOHandler<ModelURL>;\n  private artifacts: io.ModelArtifacts;\n  private initializer: GraphExecutor;\n  private resourceIdToCapturedInput: {[key: number]: Tensor};\n  private resourceManager: ResourceManager;\n  private signature: tensorflow.ISignatureDef;\n  private initializerSignature: tensorflow.ISignatureDef;\n  private structuredOutputKeys: string[];\n  private readonly io: typeof io;\n\n  // Returns the version information for the tensorflow model GraphDef.\n  get modelVersion(): string {\n    return this.version;\n  }\n\n  get inputNodes(): string[] {\n    return this.executor.inputNodes;\n  }\n\n  get outputNodes(): string[] {\n    return this.executor.outputNodes;\n  }\n\n  get inputs(): TensorInfo[] {\n    return this.executor.inputs;\n  }\n\n  get outputs(): TensorInfo[] {\n    return this.executor.outputs;\n  }\n\n  get weights(): NamedTensorsMap {\n    return this.executor.weightMap;\n  }\n\n  get metadata(): {} {\n    return this.artifacts.userDefinedMetadata;\n  }\n\n  get modelSignature(): {} {\n    return this.signature;\n  }\n\n  get modelStructuredOutputKeys(): {} {\n    return this.structuredOutputKeys;\n  }\n\n  /**\n   * @param modelUrl url for the model, or an `io.IOHandler`.\n   * @param weightManifestUrl url for the weight file generated by\n   * scripts/convert.py script.\n   * @param requestOption options for Request, which allows to send credentials\n   * and custom headers.\n   * @param onProgress Optional, progress callback function, fired periodically\n   * before the load is completed.\n   */\n  constructor(\n      private modelUrl: ModelURL, private loadOptions: io.LoadOptions = {},\n      tfio = io) {\n    this.io = tfio;\n    if (loadOptions == null) {\n      this.loadOptions = {};\n    }\n    this.resourceManager = new ResourceManager();\n  }\n\n  private findIOHandler() {\n    type IOHandler = UrlIOHandler<ModelURL>;\n    const path = this.modelUrl;\n    if ((path as io.IOHandler).load != null) {\n      // Path is an IO Handler.\n      this.handler = path as IOHandler;\n    } else if (this.loadOptions.requestInit != null) {\n      this.handler = this.io.browserHTTPRequest(\n                         path as string, this.loadOptions) as IOHandler;\n    } else {\n      const handlers =\n          this.io.getLoadHandlers(path as string, this.loadOptions);\n      if (handlers.length === 0) {\n        // For backward compatibility: if no load handler can be found,\n        // assume it is a relative http path.\n        handlers.push(\n            this.io.browserHTTPRequest(path as string, this.loadOptions));\n      } else if (handlers.length > 1) {\n        throw new Error(\n            `Found more than one (${handlers.length}) load handlers for ` +\n            `URL '${[path]}'`);\n      }\n      this.handler = handlers[0] as IOHandler;\n    }\n  }\n\n  /**\n   * Loads the model and weight files, construct the in memory weight map and\n   * compile the inference graph.\n   */\n  load(): UrlIOHandler<ModelURL> extends io.IOHandlerSync? boolean:\n                                             Promise<boolean> {\n    type IOHandler = UrlIOHandler<ModelURL>;\n    this.findIOHandler();\n    if (this.handler.load == null) {\n      throw new Error(\n          'Cannot proceed with model loading because the IOHandler provided ' +\n          'does not have the `load` method implemented.');\n    }\n\n    type Result =\n        IOHandler extends io.IOHandlerSync ? boolean : Promise<boolean>;\n\n    const loadResult = this.handler.load() as ReturnType<IOHandler['load']>;\n    if (util.isPromise(loadResult)) {\n      return loadResult.then(artifacts => {\n        if (artifacts.getWeightStream == null) {\n          return this.loadSync(artifacts);\n        }\n        return this.loadStreaming(artifacts);\n      }) as Result;\n    }\n\n    return this.loadSync(loadResult) as Result;\n  }\n\n  /**\n   * Synchronously construct the in memory weight map and\n   * compile the inference graph.\n   *\n   * @doc {heading: 'Models', subheading: 'Classes', ignoreCI: true}\n   */\n  loadSync(artifacts: io.ModelArtifacts) {\n    const weightMap = this.io.decodeWeights(\n        artifacts.weightData, artifacts.weightSpecs);\n\n    return this.loadWithWeightMap(artifacts, weightMap);\n  }\n\n  private async loadStreaming(artifacts: io.ModelArtifacts): Promise<boolean> {\n    if (artifacts.getWeightStream == null) {\n      throw new Error('Model artifacts missing streamWeights function');\n    }\n\n    const weightMap = await decodeWeightsStream(\n      artifacts.getWeightStream(), artifacts.weightSpecs);\n\n    return this.loadWithWeightMap(artifacts, weightMap);\n  }\n\n  private loadWithWeightMap(artifacts: io.ModelArtifacts,\n                            weightMap: NamedTensorMap) {\n    this.artifacts = artifacts;\n    const graph = this.artifacts.modelTopology as tensorflow.IGraphDef;\n\n    let signature = this.artifacts.signature;\n    if (this.artifacts.userDefinedMetadata != null) {\n      const metadata = this.artifacts.userDefinedMetadata;\n      if (metadata.signature != null) {\n        signature = metadata.signature;\n      }\n\n      if (metadata.structuredOutputKeys != null) {\n        this.structuredOutputKeys = metadata.structuredOutputKeys as string[];\n      }\n    }\n    this.signature = signature;\n\n    this.version = `${graph.versions.producer}.${graph.versions.minConsumer}`;\n    this.executor = new GraphExecutor(\n        OperationMapper.Instance.transformGraph(graph, this.signature));\n    this.executor.weightMap = this.convertTensorMapToTensorsMap(weightMap);\n    // Attach a model-level resourceManager to each executor to share resources,\n    // such as `HashTable`.\n    this.executor.resourceManager = this.resourceManager;\n\n    if (artifacts.modelInitializer != null &&\n        (artifacts.modelInitializer as tensorflow.IGraphDef).node != null) {\n      const initializer =\n          OperationMapper.Instance.transformGraph(artifacts.modelInitializer);\n      this.initializer = new GraphExecutor(initializer);\n      this.initializer.weightMap = this.executor.weightMap;\n      // Attach a model-level resourceManager to the initializer, the\n      // hashTables created from when executing the initializer will be stored\n      // in the resourceManager.\n      this.initializer.resourceManager = this.resourceManager;\n      this.initializerSignature = artifacts.initializerSignature;\n    }\n\n    return true;\n  }\n\n  /**\n   * Save the configuration and/or weights of the GraphModel.\n   *\n   * An `IOHandler` is an object that has a `save` method of the proper\n   * signature defined. The `save` method manages the storing or\n   * transmission of serialized data (\"artifacts\") that represent the\n   * model's topology and weights onto or via a specific medium, such as\n   * file downloads, local storage, IndexedDB in the web browser and HTTP\n   * requests to a server. TensorFlow.js provides `IOHandler`\n   * implementations for a number of frequently used saving mediums, such as\n   * `tf.io.browserDownloads` and `tf.io.browserLocalStorage`. See `tf.io`\n   * for more details.\n   *\n   * This method also allows you to refer to certain types of `IOHandler`s\n   * as URL-like string shortcuts, such as 'localstorage://' and\n   * 'indexeddb://'.\n   *\n   * Example 1: Save `model`'s topology and weights to browser [local\n   * storage](https://developer.mozilla.org/en-US/docs/Web/API/Window/localStorage);\n   * then load it back.\n   *\n   * ```js\n   * const modelUrl =\n   *    'https://storage.googleapis.com/tfjs-models/savedmodel/mobilenet_v2_1.0_224/model.json';\n   * const model = await tf.loadGraphModel(modelUrl);\n   * const zeros = tf.zeros([1, 224, 224, 3]);\n   * model.predict(zeros).print();\n   *\n   * const saveResults = await model.save('localstorage://my-model-1');\n   *\n   * const loadedModel = await tf.loadGraphModel('localstorage://my-model-1');\n   * console.log('Prediction from loaded model:');\n   * model.predict(zeros).print();\n   * ```\n   *\n   * @param handlerOrURL An instance of `IOHandler` or a URL-like,\n   * scheme-based string shortcut for `IOHandler`.\n   * @param config Options for saving the model.\n   * @returns A `Promise` of `SaveResult`, which summarizes the result of\n   * the saving, such as byte sizes of the saved artifacts for the model's\n   *   topology and weight values.\n   *\n   * @doc {heading: 'Models', subheading: 'Classes', ignoreCI: true}\n   */\n  async save(handlerOrURL: io.IOHandler|string, config?: io.SaveConfig):\n      Promise<io.SaveResult> {\n    if (typeof handlerOrURL === 'string') {\n      const handlers = this.io.getSaveHandlers(handlerOrURL);\n      if (handlers.length === 0) {\n        throw new Error(\n            `Cannot find any save handlers for URL '${handlerOrURL}'`);\n      } else if (handlers.length > 1) {\n        throw new Error(\n            `Found more than one (${handlers.length}) save handlers for ` +\n            `URL '${handlerOrURL}'`);\n      }\n      handlerOrURL = handlers[0];\n    }\n    if (handlerOrURL.save == null) {\n      throw new Error(\n          'GraphModel.save() cannot proceed because the IOHandler ' +\n          'provided does not have the `save` attribute defined.');\n    }\n\n    return handlerOrURL.save(this.artifacts);\n  }\n\n  private addStructuredOutputNames(outputTensors: Tensor|Tensor[]) {\n    if (this.structuredOutputKeys) {\n      const outputTensorsArray =\n          outputTensors instanceof Tensor ? [outputTensors] : outputTensors;\n      const outputTensorMap: NamedTensorMap = {};\n\n      outputTensorsArray.forEach(\n          (outputTensor, i) => outputTensorMap[this.structuredOutputKeys[i]] =\n              outputTensor);\n\n      return outputTensorMap;\n    }\n    return outputTensors;\n  }\n\n  /**\n   * Execute the inference for the input tensors.\n   *\n   * @param input The input tensors, when there is single input for the model,\n   * inputs param should be a `tf.Tensor`. For models with multiple inputs,\n   * inputs params should be in either `tf.Tensor`[] if the input order is\n   * fixed, or otherwise NamedTensorMap format.\n   *\n   * For model with multiple inputs, we recommend you use NamedTensorMap as the\n   * input type, if you use `tf.Tensor`[], the order of the array needs to\n   * follow the\n   * order of inputNodes array. @see {@link GraphModel.inputNodes}\n   *\n   * You can also feed any intermediate nodes using the NamedTensorMap as the\n   * input type. For example, given the graph\n   *    InputNode => Intermediate => OutputNode,\n   * you can execute the subgraph Intermediate => OutputNode by calling\n   *    model.execute('IntermediateNode' : tf.tensor(...));\n   *\n   * This is useful for models that uses tf.dynamic_rnn, where the intermediate\n   * state needs to be fed manually.\n   *\n   * For batch inference execution, the tensors for each input need to be\n   * concatenated together. For example with mobilenet, the required input shape\n   * is [1, 244, 244, 3], which represents the [batch, height, width, channel].\n   * If we are provide a batched data of 100 images, the input tensor should be\n   * in the shape of [100, 244, 244, 3].\n   *\n   * @param config Prediction configuration for specifying the batch size.\n   * Currently the batch size option is ignored for graph model.\n   *\n   * @returns Inference result tensors. If the model is converted and it\n   * originally had structured_outputs in tensorflow, then a NamedTensorMap\n   * will be returned matching the structured_outputs. If no structured_outputs\n   * are present, the output will be single `tf.Tensor` if the model has single\n   * output node, otherwise Tensor[].\n   *\n   * @doc {heading: 'Models', subheading: 'Classes'}\n   */\n  predict(inputs: Tensor|Tensor[]|NamedTensorMap, config?: ModelPredictConfig):\n      Tensor|Tensor[]|NamedTensorMap {\n    const outputTensors = this.execute(inputs, this.outputNodes);\n    return this.addStructuredOutputNames(outputTensors);\n  }\n\n  /**\n   * Execute the inference for the input tensors in async fashion, use this\n   * method when your model contains control flow ops.\n   *\n   * @param input The input tensors, when there is single input for the model,\n   * inputs param should be a `tf.Tensor`. For models with mutliple inputs,\n   * inputs params should be in either `tf.Tensor`[] if the input order is\n   * fixed, or otherwise NamedTensorMap format.\n   *\n   * For model with multiple inputs, we recommend you use NamedTensorMap as the\n   * input type, if you use `tf.Tensor`[], the order of the array needs to\n   * follow the\n   * order of inputNodes array. @see {@link GraphModel.inputNodes}\n   *\n   * You can also feed any intermediate nodes using the NamedTensorMap as the\n   * input type. For example, given the graph\n   *    InputNode => Intermediate => OutputNode,\n   * you can execute the subgraph Intermediate => OutputNode by calling\n   *    model.execute('IntermediateNode' : tf.tensor(...));\n   *\n   * This is useful for models that uses tf.dynamic_rnn, where the intermediate\n   * state needs to be fed manually.\n   *\n   * For batch inference execution, the tensors for each input need to be\n   * concatenated together. For example with mobilenet, the required input shape\n   * is [1, 244, 244, 3], which represents the [batch, height, width, channel].\n   * If we are provide a batched data of 100 images, the input tensor should be\n   * in the shape of [100, 244, 244, 3].\n   *\n   * @param config Prediction configuration for specifying the batch size.\n   * Currently the batch size option is ignored for graph model.\n   *\n   * @returns A Promise of inference result tensors. If the model is converted\n   * and it originally had structured_outputs in tensorflow, then a\n   * NamedTensorMap will be returned matching the structured_outputs. If no\n   * structured_outputs are present, the output will be single `tf.Tensor` if\n   * the model has single output node, otherwise Tensor[].\n   *\n   * @doc {heading: 'Models', subheading: 'Classes'}\n   */\n  async predictAsync(\n      inputs: Tensor|Tensor[]|NamedTensorMap,\n      config?: ModelPredictConfig): Promise<Tensor|Tensor[]|NamedTensorMap> {\n    const outputTensors = await this.executeAsync(inputs, this.outputNodes);\n    return this.addStructuredOutputNames(outputTensors);\n  }\n\n  private normalizeInputs(inputs: Tensor|Tensor[]|\n                          NamedTensorMap): NamedTensorMap {\n    if (!(inputs instanceof Tensor) && !Array.isArray(inputs)) {\n      // The input is already a NamedTensorMap.\n      const signatureInputs = this.signature?.inputs;\n      if (signatureInputs != null) {\n        for (const input in signatureInputs) {\n          const tensor = signatureInputs[input];\n          if (tensor.resourceId != null) {\n            inputs[input] = this.resourceIdToCapturedInput[tensor.resourceId];\n          }\n        }\n      }\n      return inputs;\n    }\n    inputs = Array.isArray(inputs) ? inputs : [inputs];\n\n    const numCapturedInputs =\n        Object.keys(this.resourceIdToCapturedInput).length;\n    if (inputs.length + numCapturedInputs !== this.inputNodes.length) {\n      throw new Error(`Input tensor count mismatch, the graph model has ${\n          this.inputNodes.length -\n          numCapturedInputs} non-resource placeholders, while there are ${\n          inputs.length} input tensors provided.`);\n    }\n\n    let inputIndex = 0;\n    return this.inputNodes.reduce((map, inputName) => {\n      const resourceId = this.signature?.inputs?.[inputName]?.resourceId;\n      if (resourceId != null) {\n        map[inputName] = this.resourceIdToCapturedInput[resourceId];\n      } else {\n        map[inputName] = (inputs as Tensor[])[inputIndex++];\n      }\n      return map;\n    }, {} as NamedTensorMap);\n  }\n\n  private normalizeOutputs(outputs: string|string[]): string[] {\n    outputs = outputs || this.outputNodes;\n    return !Array.isArray(outputs) ? [outputs] : outputs;\n  }\n\n  private executeInitializerGraph() {\n    if (this.initializer == null) {\n      return [];\n    }\n    if (this.initializerSignature == null) {\n      return this.initializer.execute({}, []);\n    } else {\n      return this.initializer.execute(\n          {}, Object.keys(this.initializerSignature.outputs));\n    }\n  }\n\n  private async executeInitializerGraphAsync() {\n    if (this.initializer == null) {\n      return [];\n    }\n    if (this.initializerSignature == null) {\n      return this.initializer.executeAsync({}, []);\n    } else {\n      return this.initializer.executeAsync(\n          {}, Object.keys(this.initializerSignature.outputs));\n    }\n  }\n\n  private setResourceIdToCapturedInput(outputs: Tensor[]) {\n    this.resourceIdToCapturedInput = {};\n\n    if (this.initializerSignature) {\n      const signatureOutputs = this.initializerSignature.outputs;\n      const outputNames = Object.keys(signatureOutputs);\n      for (let i = 0; i < outputNames.length; i++) {\n        const outputName = outputNames[i];\n        const tensorInfo = signatureOutputs[outputName];\n        this.resourceIdToCapturedInput[tensorInfo.resourceId] = outputs[i];\n      }\n    }\n  }\n\n  /**\n   * Executes inference for the model for given input tensors.\n   * @param inputs tensor, tensor array or tensor map of the inputs for the\n   * model, keyed by the input node names.\n   * @param outputs output node name from the TensorFlow model, if no\n   * outputs are specified, the default outputs of the model would be used.\n   * You can inspect intermediate nodes of the model by adding them to the\n   * outputs array.\n   *\n   * @returns A single tensor if provided with a single output or no outputs\n   * are provided and there is only one default output, otherwise return a\n   * tensor array. The order of the tensor array is the same as the outputs\n   * if provided, otherwise the order of outputNodes attribute of the model.\n   *\n   * @doc {heading: 'Models', subheading: 'Classes'}\n   */\n  execute(inputs: Tensor|Tensor[]|NamedTensorMap, outputs?: string|string[]):\n      Tensor|Tensor[] {\n    if (this.resourceIdToCapturedInput == null) {\n      this.setResourceIdToCapturedInput(this.executeInitializerGraph());\n    }\n    inputs = this.normalizeInputs(inputs);\n    outputs = this.normalizeOutputs(outputs);\n    const result = this.executor.execute(inputs, outputs);\n    return result.length > 1 ? result : result[0];\n  }\n\n  /**\n   * Executes inference for the model for given input tensors in async\n   * fashion, use this method when your model contains control flow ops.\n   * @param inputs tensor, tensor array or tensor map of the inputs for the\n   * model, keyed by the input node names.\n   * @param outputs output node name from the TensorFlow model, if no outputs\n   * are specified, the default outputs of the model would be used. You can\n   * inspect intermediate nodes of the model by adding them to the outputs\n   * array.\n   *\n   * @returns A Promise of single tensor if provided with a single output or\n   * no outputs are provided and there is only one default output, otherwise\n   * return a tensor map.\n   *\n   * @doc {heading: 'Models', subheading: 'Classes'}\n   */\n  async executeAsync(\n      inputs: Tensor|Tensor[]|NamedTensorMap,\n      outputs?: string|string[]): Promise<Tensor|Tensor[]> {\n    if (this.resourceIdToCapturedInput == null) {\n      this.setResourceIdToCapturedInput(\n          await this.executeInitializerGraphAsync());\n    }\n    inputs = this.normalizeInputs(inputs);\n    outputs = this.normalizeOutputs(outputs);\n    const result = await this.executor.executeAsync(inputs, outputs);\n    return result.length > 1 ? result : result[0];\n  }\n\n  /**\n   * Get intermediate tensors for model debugging mode (flag\n   * KEEP_INTERMEDIATE_TENSORS is true).\n   *\n   * @doc {heading: 'Models', subheading: 'Classes'}\n   */\n  getIntermediateTensors(): NamedTensorsMap {\n    return this.executor.getIntermediateTensors();\n  }\n\n  /**\n   * Dispose intermediate tensors for model debugging mode (flag\n   * KEEP_INTERMEDIATE_TENSORS is true).\n   *\n   * @doc {heading: 'Models', subheading: 'Classes'}\n   */\n  disposeIntermediateTensors() {\n    this.executor.disposeIntermediateTensors();\n  }\n\n  private convertTensorMapToTensorsMap(map: NamedTensorMap): NamedTensorsMap {\n    return Object.keys(map).reduce((newMap: NamedTensorsMap, key) => {\n      newMap[key] = [map[key]];\n      return newMap;\n    }, {});\n  }\n\n  /**\n   * Releases the memory used by the weight tensors and resourceManager.\n   *\n   * @doc {heading: 'Models', subheading: 'Classes'}\n   */\n  dispose() {\n    this.executor.dispose();\n\n    if (this.initializer) {\n      this.initializer.dispose();\n      if (this.resourceIdToCapturedInput) {\n        dispose(this.resourceIdToCapturedInput);\n      }\n    }\n\n    this.resourceManager.dispose();\n  }\n}\n\n/**\n * Load a graph model given a URL to the model definition.\n *\n * Example of loading MobileNetV2 from a URL and making a prediction with a\n * zeros input:\n *\n * ```js\n * const modelUrl =\n *    'https://storage.googleapis.com/tfjs-models/savedmodel/mobilenet_v2_1.0_224/model.json';\n * const model = await tf.loadGraphModel(modelUrl);\n * const zeros = tf.zeros([1, 224, 224, 3]);\n * model.predict(zeros).print();\n * ```\n *\n * Example of loading MobileNetV2 from a TF Hub URL and making a prediction\n * with a zeros input:\n *\n * ```js\n * const modelUrl =\n *    'https://tfhub.dev/google/imagenet/mobilenet_v2_140_224/classification/2';\n * const model = await tf.loadGraphModel(modelUrl, {fromTFHub: true});\n * const zeros = tf.zeros([1, 224, 224, 3]);\n * model.predict(zeros).print();\n * ```\n * @param modelUrl The url or an `io.IOHandler` that loads the model.\n * @param options Options for the HTTP request, which allows to send\n *     credentials\n *    and custom headers.\n *\n * @doc {heading: 'Models', subheading: 'Loading'}\n */\nexport async function loadGraphModel(\n    modelUrl: string|io.IOHandler, options: io.LoadOptions = {},\n    tfio = io): Promise<GraphModel> {\n  if (modelUrl == null) {\n    throw new Error(\n        'modelUrl in loadGraphModel() cannot be null. Please provide a url ' +\n        'or an IOHandler that loads the model');\n  }\n  if (options == null) {\n    options = {};\n  }\n\n  if (options.fromTFHub && typeof modelUrl === 'string') {\n    modelUrl = getTFHubUrl(modelUrl);\n  }\n  const model = new GraphModel(modelUrl, options, tfio);\n  await model.load();\n  return model;\n}\n\n/**\n * Load a graph model given a synchronous IO handler with a 'load' method.\n *\n * @param modelSource The `io.IOHandlerSync` that loads the model, or the\n *     `io.ModelArtifacts` that encode the model, or a tuple of\n *     `[io.ModelJSON, ArrayBuffer]` of which the first element encodes the\n *      model and the second contains the weights.\n *\n * @doc {heading: 'Models', subheading: 'Loading'}\n */\nexport function loadGraphModelSync(\n    modelSource: io.IOHandlerSync|\n    io.ModelArtifacts|[io.ModelJSON, /* Weights */ ArrayBuffer]):\n    GraphModel<io.IOHandlerSync> {\n  if (modelSource == null) {\n    throw new Error(\n        'modelUrl in loadGraphModelSync() cannot be null. Please provide ' +\n        'model artifacts or an IOHandler that loads the model');\n  }\n\n  let ioHandler: io.IOHandlerSync;\n  if (modelSource instanceof Array) {\n    const [modelJSON, weights] = modelSource;\n    if (!modelJSON) {\n      throw new Error('modelJSON must be the first element of the array');\n    }\n    if (!weights || !(weights instanceof ArrayBuffer)) {\n      throw new Error(\n          'An ArrayBuffer of weights must be the second element of' +\n          ' the array');\n    }\n    if (!('modelTopology' in modelJSON)) {\n      throw new Error('Model JSON is missing \\'modelTopology\\'');\n    }\n    if (!('weightsManifest' in modelJSON)) {\n      throw new Error('Model JSON is missing \\'weightsManifest\\'');\n    }\n\n    const weightSpecs = io.getWeightSpecs(modelJSON.weightsManifest);\n    const modelArtifacts =\n        io.getModelArtifactsForJSONSync(modelJSON, weightSpecs, weights);\n    ioHandler = io.fromMemorySync(modelArtifacts);\n  } else if ('load' in modelSource) {\n    // Then modelSource is already an IOHandlerSync.\n    ioHandler = modelSource;\n  } else if (\n      'modelTopology' in modelSource && 'weightSpecs' in modelSource &&\n      'weightData' in modelSource) {\n    // modelSource is of type ModelArtifacts.\n    ioHandler = io.fromMemorySync(modelSource);\n  } else {\n    throw new Error('Unknown model format');\n  }\n\n  const model = new GraphModel(ioHandler);\n  model.load();\n  return model;\n}\n\nfunction getTFHubUrl(modelUrl: string): string {\n  if (!modelUrl.endsWith('/')) {\n    modelUrl = (modelUrl) + '/';\n  }\n  return `${modelUrl}${DEFAULT_MODEL_NAME}${TFHUB_SEARCH_PARAM}`;\n}\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAiBA,SAAQA,OAAO,EAAkBC,EAAE,EAAsCC,MAAM,EAAEC,IAAI,QAAO,uBAAuB;AAInH,SAAQC,eAAe,QAAO,gCAAgC;AAE9D,SAAQC,aAAa,QAAO,kBAAkB;AAC9C,SAAQC,eAAe,QAAO,oBAAoB;AAClD;AACA,SAAQC,mBAAmB,QAAO,wCAAwC;AAE1E,OAAO,MAAMC,kBAAkB,GAAG,mBAAmB;AACrD,OAAO,MAAMC,kBAAkB,GAAG,YAAY;AAI9C;;;;;;;;;;AAUA,OAAM,MAAOC,UAAU;EAcrB;EACA,IAAIC,YAAYA,CAAA;IACd,OAAO,IAAI,CAACC,OAAO;EACrB;EAEA,IAAIC,UAAUA,CAAA;IACZ,OAAO,IAAI,CAACC,QAAQ,CAACD,UAAU;EACjC;EAEA,IAAIE,WAAWA,CAAA;IACb,OAAO,IAAI,CAACD,QAAQ,CAACC,WAAW;EAClC;EAEA,IAAIC,MAAMA,CAAA;IACR,OAAO,IAAI,CAACF,QAAQ,CAACE,MAAM;EAC7B;EAEA,IAAIC,OAAOA,CAAA;IACT,OAAO,IAAI,CAACH,QAAQ,CAACG,OAAO;EAC9B;EAEA,IAAIC,OAAOA,CAAA;IACT,OAAO,IAAI,CAACJ,QAAQ,CAACK,SAAS;EAChC;EAEA,IAAIC,QAAQA,CAAA;IACV,OAAO,IAAI,CAACC,SAAS,CAACC,mBAAmB;EAC3C;EAEA,IAAIC,cAAcA,CAAA;IAChB,OAAO,IAAI,CAACC,SAAS;EACvB;EAEA,IAAIC,yBAAyBA,CAAA;IAC3B,OAAO,IAAI,CAACC,oBAAoB;EAClC;EAEA;;;;;;;;;EASAC,YACYC,QAAkB,EACjB;IAAA,IAD2BC,WAAA,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAA8B,EAAE;IAAA,IACpEG,IAAI,GAAAH,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG7B,EAAE;IADD,KAAA2B,QAAQ,GAARA,QAAQ;IAAoB,KAAAC,WAAW,GAAXA,WAAW;IA1D3C,KAAAjB,OAAO,GAAG,KAAK;IA4DrB,IAAI,CAACX,EAAE,GAAGgC,IAAI;IACd,IAAIJ,WAAW,IAAI,IAAI,EAAE;MACvB,IAAI,CAACA,WAAW,GAAG,EAAE;;IAEvB,IAAI,CAACK,eAAe,GAAG,IAAI5B,eAAe,EAAE;EAC9C;EAEQ6B,aAAaA,CAAA;IAEnB,MAAMC,IAAI,GAAG,IAAI,CAACR,QAAQ;IAC1B,IAAKQ,IAAqB,CAACC,IAAI,IAAI,IAAI,EAAE;MACvC;MACA,IAAI,CAACC,OAAO,GAAGF,IAAiB;KACjC,MAAM,IAAI,IAAI,CAACP,WAAW,CAACU,WAAW,IAAI,IAAI,EAAE;MAC/C,IAAI,CAACD,OAAO,GAAG,IAAI,CAACrC,EAAE,CAACuC,kBAAkB,CACtBJ,IAAc,EAAE,IAAI,CAACP,WAAW,CAAc;KAClE,MAAM;MACL,MAAMY,QAAQ,GACV,IAAI,CAACxC,EAAE,CAACyC,eAAe,CAACN,IAAc,EAAE,IAAI,CAACP,WAAW,CAAC;MAC7D,IAAIY,QAAQ,CAACV,MAAM,KAAK,CAAC,EAAE;QACzB;QACA;QACAU,QAAQ,CAACE,IAAI,CACT,IAAI,CAAC1C,EAAE,CAACuC,kBAAkB,CAACJ,IAAc,EAAE,IAAI,CAACP,WAAW,CAAC,CAAC;OAClE,MAAM,IAAIY,QAAQ,CAACV,MAAM,GAAG,CAAC,EAAE;QAC9B,MAAM,IAAIa,KAAK,CACX,wBAAAC,MAAA,CAAwBJ,QAAQ,CAACV,MAAM,oCAAAc,MAAA,CAC/B,CAACT,IAAI,CAAC,MAAG,CAAC;;MAExB,IAAI,CAACE,OAAO,GAAGG,QAAQ,CAAC,CAAC,CAAc;;EAE3C;EAEA;;;;EAIAJ,IAAIA,CAAA;IAGF,IAAI,CAACF,aAAa,EAAE;IACpB,IAAI,IAAI,CAACG,OAAO,CAACD,IAAI,IAAI,IAAI,EAAE;MAC7B,MAAM,IAAIO,KAAK,CACX,mEAAmE,GACnE,8CAA8C,CAAC;;IAMrD,MAAME,UAAU,GAAG,IAAI,CAACR,OAAO,CAACD,IAAI,EAAmC;IACvE,IAAIlC,IAAI,CAAC4C,SAAS,CAACD,UAAU,CAAC,EAAE;MAC9B,OAAOA,UAAU,CAACE,IAAI,CAAC3B,SAAS,IAAG;QACjC,IAAIA,SAAS,CAAC4B,eAAe,IAAI,IAAI,EAAE;UACrC,OAAO,IAAI,CAACC,QAAQ,CAAC7B,SAAS,CAAC;;QAEjC,OAAO,IAAI,CAAC8B,aAAa,CAAC9B,SAAS,CAAC;MACtC,CAAC,CAAW;;IAGd,OAAO,IAAI,CAAC6B,QAAQ,CAACJ,UAAU,CAAW;EAC5C;EAEA;;;;;;EAMAI,QAAQA,CAAC7B,SAA4B;IACnC,MAAMF,SAAS,GAAG,IAAI,CAAClB,EAAE,CAACmD,aAAa,CACnC/B,SAAS,CAACgC,UAAU,EAAEhC,SAAS,CAACiC,WAAW,CAAC;IAEhD,OAAO,IAAI,CAACC,iBAAiB,CAAClC,SAAS,EAAEF,SAAS,CAAC;EACrD;EAEQ,MAAMgC,aAAaA,CAAC9B,SAA4B;IACtD,IAAIA,SAAS,CAAC4B,eAAe,IAAI,IAAI,EAAE;MACrC,MAAM,IAAIL,KAAK,CAAC,gDAAgD,CAAC;;IAGnE,MAAMzB,SAAS,GAAG,MAAMZ,mBAAmB,CACzCc,SAAS,CAAC4B,eAAe,EAAE,EAAE5B,SAAS,CAACiC,WAAW,CAAC;IAErD,OAAO,IAAI,CAACC,iBAAiB,CAAClC,SAAS,EAAEF,SAAS,CAAC;EACrD;EAEQoC,iBAAiBA,CAAClC,SAA4B,EAC5BF,SAAyB;IACjD,IAAI,CAACE,SAAS,GAAGA,SAAS;IAC1B,MAAMmC,KAAK,GAAG,IAAI,CAACnC,SAAS,CAACoC,aAAqC;IAElE,IAAIjC,SAAS,GAAG,IAAI,CAACH,SAAS,CAACG,SAAS;IACxC,IAAI,IAAI,CAACH,SAAS,CAACC,mBAAmB,IAAI,IAAI,EAAE;MAC9C,MAAMF,QAAQ,GAAG,IAAI,CAACC,SAAS,CAACC,mBAAmB;MACnD,IAAIF,QAAQ,CAACI,SAAS,IAAI,IAAI,EAAE;QAC9BA,SAAS,GAAGJ,QAAQ,CAACI,SAAS;;MAGhC,IAAIJ,QAAQ,CAACM,oBAAoB,IAAI,IAAI,EAAE;QACzC,IAAI,CAACA,oBAAoB,GAAGN,QAAQ,CAACM,oBAAgC;;;IAGzE,IAAI,CAACF,SAAS,GAAGA,SAAS;IAE1B,IAAI,CAACZ,OAAO,MAAAiC,MAAA,CAAMW,KAAK,CAACE,QAAQ,CAACC,QAAQ,OAAAd,MAAA,CAAIW,KAAK,CAACE,QAAQ,CAACE,WAAW,CAAE;IACzE,IAAI,CAAC9C,QAAQ,GAAG,IAAIT,aAAa,CAC7BD,eAAe,CAACyD,QAAQ,CAACC,cAAc,CAACN,KAAK,EAAE,IAAI,CAAChC,SAAS,CAAC,CAAC;IACnE,IAAI,CAACV,QAAQ,CAACK,SAAS,GAAG,IAAI,CAAC4C,4BAA4B,CAAC5C,SAAS,CAAC;IACtE;IACA;IACA,IAAI,CAACL,QAAQ,CAACoB,eAAe,GAAG,IAAI,CAACA,eAAe;IAEpD,IAAIb,SAAS,CAAC2C,gBAAgB,IAAI,IAAI,IACjC3C,SAAS,CAAC2C,gBAAyC,CAACC,IAAI,IAAI,IAAI,EAAE;MACrE,MAAMC,WAAW,GACb9D,eAAe,CAACyD,QAAQ,CAACC,cAAc,CAACzC,SAAS,CAAC2C,gBAAgB,CAAC;MACvE,IAAI,CAACE,WAAW,GAAG,IAAI7D,aAAa,CAAC6D,WAAW,CAAC;MACjD,IAAI,CAACA,WAAW,CAAC/C,SAAS,GAAG,IAAI,CAACL,QAAQ,CAACK,SAAS;MACpD;MACA;MACA;MACA,IAAI,CAAC+C,WAAW,CAAChC,eAAe,GAAG,IAAI,CAACA,eAAe;MACvD,IAAI,CAACiC,oBAAoB,GAAG9C,SAAS,CAAC8C,oBAAoB;;IAG5D,OAAO,IAAI;EACb;EAEA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EA4CA,MAAMC,IAAIA,CAACC,YAAiC,EAAEC,MAAsB;IAElE,IAAI,OAAOD,YAAY,KAAK,QAAQ,EAAE;MACpC,MAAM5B,QAAQ,GAAG,IAAI,CAACxC,EAAE,CAACsE,eAAe,CAACF,YAAY,CAAC;MACtD,IAAI5B,QAAQ,CAACV,MAAM,KAAK,CAAC,EAAE;QACzB,MAAM,IAAIa,KAAK,2CAAAC,MAAA,CAC+BwB,YAAY,MAAG,CAAC;OAC/D,MAAM,IAAI5B,QAAQ,CAACV,MAAM,GAAG,CAAC,EAAE;QAC9B,MAAM,IAAIa,KAAK,CACX,wBAAAC,MAAA,CAAwBJ,QAAQ,CAACV,MAAM,oCAAAc,MAAA,CAC/BwB,YAAY,MAAG,CAAC;;MAE9BA,YAAY,GAAG5B,QAAQ,CAAC,CAAC,CAAC;;IAE5B,IAAI4B,YAAY,CAACD,IAAI,IAAI,IAAI,EAAE;MAC7B,MAAM,IAAIxB,KAAK,CACX,yDAAyD,GACzD,sDAAsD,CAAC;;IAG7D,OAAOyB,YAAY,CAACD,IAAI,CAAC,IAAI,CAAC/C,SAAS,CAAC;EAC1C;EAEQmD,wBAAwBA,CAACC,aAA8B;IAC7D,IAAI,IAAI,CAAC/C,oBAAoB,EAAE;MAC7B,MAAMgD,kBAAkB,GACpBD,aAAa,YAAYvE,MAAM,GAAG,CAACuE,aAAa,CAAC,GAAGA,aAAa;MACrE,MAAME,eAAe,GAAmB,EAAE;MAE1CD,kBAAkB,CAACE,OAAO,CACtB,CAACC,YAAY,EAAEC,CAAC,KAAKH,eAAe,CAAC,IAAI,CAACjD,oBAAoB,CAACoD,CAAC,CAAC,CAAC,GAC9DD,YAAY,CAAC;MAErB,OAAOF,eAAe;;IAExB,OAAOF,aAAa;EACtB;EAEA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EAuCAM,OAAOA,CAAC/D,MAAsC,EAAEsD,MAA2B;IAEzE,MAAMG,aAAa,GAAG,IAAI,CAACO,OAAO,CAAChE,MAAM,EAAE,IAAI,CAACD,WAAW,CAAC;IAC5D,OAAO,IAAI,CAACyD,wBAAwB,CAACC,aAAa,CAAC;EACrD;EAEA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EAwCA,MAAMQ,YAAYA,CACdjE,MAAsC,EACtCsD,MAA2B;IAC7B,MAAMG,aAAa,GAAG,MAAM,IAAI,CAACS,YAAY,CAAClE,MAAM,EAAE,IAAI,CAACD,WAAW,CAAC;IACvE,OAAO,IAAI,CAACyD,wBAAwB,CAACC,aAAa,CAAC;EACrD;EAEQU,eAAeA,CAACnE,MACc;;IACpC,IAAI,EAAEA,MAAM,YAAYd,MAAM,CAAC,IAAI,CAACkF,KAAK,CAACC,OAAO,CAACrE,MAAM,CAAC,EAAE;MACzD;MACA,MAAMsE,eAAe,GAAG,CAAAC,EAAA,OAAI,CAAC/D,SAAS,cAAA+D,EAAA,uBAAAA,EAAA,CAAEvE,MAAM;MAC9C,IAAIsE,eAAe,IAAI,IAAI,EAAE;QAC3B,KAAK,MAAME,KAAK,IAAIF,eAAe,EAAE;UACnC,MAAMG,MAAM,GAAGH,eAAe,CAACE,KAAK,CAAC;UACrC,IAAIC,MAAM,CAACC,UAAU,IAAI,IAAI,EAAE;YAC7B1E,MAAM,CAACwE,KAAK,CAAC,GAAG,IAAI,CAACG,yBAAyB,CAACF,MAAM,CAACC,UAAU,CAAC;;;;MAIvE,OAAO1E,MAAM;;IAEfA,MAAM,GAAGoE,KAAK,CAACC,OAAO,CAACrE,MAAM,CAAC,GAAGA,MAAM,GAAG,CAACA,MAAM,CAAC;IAElD,MAAM4E,iBAAiB,GACnBC,MAAM,CAACC,IAAI,CAAC,IAAI,CAACH,yBAAyB,CAAC,CAAC5D,MAAM;IACtD,IAAIf,MAAM,CAACe,MAAM,GAAG6D,iBAAiB,KAAK,IAAI,CAAC/E,UAAU,CAACkB,MAAM,EAAE;MAChE,MAAM,IAAIa,KAAK,qDAAAC,MAAA,CACX,IAAI,CAAChC,UAAU,CAACkB,MAAM,GACtB6D,iBAAiB,kDAAA/C,MAAA,CACjB7B,MAAM,CAACe,MAAM,6BAA0B,CAAC;;IAG9C,IAAIgE,UAAU,GAAG,CAAC;IAClB,OAAO,IAAI,CAAClF,UAAU,CAACmF,MAAM,CAAC,CAACC,GAAG,EAAEC,SAAS,KAAI;;MAC/C,MAAMR,UAAU,GAAG,CAAAS,EAAA,IAAAC,EAAA,IAAAb,EAAA,OAAI,CAAC/D,SAAS,cAAA+D,EAAA,uBAAAA,EAAA,CAAEvE,MAAM,cAAAoF,EAAA,uBAAAA,EAAA,CAAGF,SAAS,CAAC,cAAAC,EAAA,uBAAAA,EAAA,CAAET,UAAU;MAClE,IAAIA,UAAU,IAAI,IAAI,EAAE;QACtBO,GAAG,CAACC,SAAS,CAAC,GAAG,IAAI,CAACP,yBAAyB,CAACD,UAAU,CAAC;OAC5D,MAAM;QACLO,GAAG,CAACC,SAAS,CAAC,GAAIlF,MAAmB,CAAC+E,UAAU,EAAE,CAAC;;MAErD,OAAOE,GAAG;IACZ,CAAC,EAAE,EAAoB,CAAC;EAC1B;EAEQI,gBAAgBA,CAACpF,OAAwB;IAC/CA,OAAO,GAAGA,OAAO,IAAI,IAAI,CAACF,WAAW;IACrC,OAAO,CAACqE,KAAK,CAACC,OAAO,CAACpE,OAAO,CAAC,GAAG,CAACA,OAAO,CAAC,GAAGA,OAAO;EACtD;EAEQqF,uBAAuBA,CAAA;IAC7B,IAAI,IAAI,CAACpC,WAAW,IAAI,IAAI,EAAE;MAC5B,OAAO,EAAE;;IAEX,IAAI,IAAI,CAACC,oBAAoB,IAAI,IAAI,EAAE;MACrC,OAAO,IAAI,CAACD,WAAW,CAACc,OAAO,CAAC,EAAE,EAAE,EAAE,CAAC;KACxC,MAAM;MACL,OAAO,IAAI,CAACd,WAAW,CAACc,OAAO,CAC3B,EAAE,EAAEa,MAAM,CAACC,IAAI,CAAC,IAAI,CAAC3B,oBAAoB,CAAClD,OAAO,CAAC,CAAC;;EAE3D;EAEQ,MAAMsF,4BAA4BA,CAAA;IACxC,IAAI,IAAI,CAACrC,WAAW,IAAI,IAAI,EAAE;MAC5B,OAAO,EAAE;;IAEX,IAAI,IAAI,CAACC,oBAAoB,IAAI,IAAI,EAAE;MACrC,OAAO,IAAI,CAACD,WAAW,CAACgB,YAAY,CAAC,EAAE,EAAE,EAAE,CAAC;KAC7C,MAAM;MACL,OAAO,IAAI,CAAChB,WAAW,CAACgB,YAAY,CAChC,EAAE,EAAEW,MAAM,CAACC,IAAI,CAAC,IAAI,CAAC3B,oBAAoB,CAAClD,OAAO,CAAC,CAAC;;EAE3D;EAEQuF,4BAA4BA,CAACvF,OAAiB;IACpD,IAAI,CAAC0E,yBAAyB,GAAG,EAAE;IAEnC,IAAI,IAAI,CAACxB,oBAAoB,EAAE;MAC7B,MAAMsC,gBAAgB,GAAG,IAAI,CAACtC,oBAAoB,CAAClD,OAAO;MAC1D,MAAMyF,WAAW,GAAGb,MAAM,CAACC,IAAI,CAACW,gBAAgB,CAAC;MACjD,KAAK,IAAI3B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG4B,WAAW,CAAC3E,MAAM,EAAE+C,CAAC,EAAE,EAAE;QAC3C,MAAM6B,UAAU,GAAGD,WAAW,CAAC5B,CAAC,CAAC;QACjC,MAAM8B,UAAU,GAAGH,gBAAgB,CAACE,UAAU,CAAC;QAC/C,IAAI,CAAChB,yBAAyB,CAACiB,UAAU,CAAClB,UAAU,CAAC,GAAGzE,OAAO,CAAC6D,CAAC,CAAC;;;EAGxE;EAEA;;;;;;;;;;;;;;;;EAgBAE,OAAOA,CAAChE,MAAsC,EAAEC,OAAyB;IAEvE,IAAI,IAAI,CAAC0E,yBAAyB,IAAI,IAAI,EAAE;MAC1C,IAAI,CAACa,4BAA4B,CAAC,IAAI,CAACF,uBAAuB,EAAE,CAAC;;IAEnEtF,MAAM,GAAG,IAAI,CAACmE,eAAe,CAACnE,MAAM,CAAC;IACrCC,OAAO,GAAG,IAAI,CAACoF,gBAAgB,CAACpF,OAAO,CAAC;IACxC,MAAM4F,MAAM,GAAG,IAAI,CAAC/F,QAAQ,CAACkE,OAAO,CAAChE,MAAM,EAAEC,OAAO,CAAC;IACrD,OAAO4F,MAAM,CAAC9E,MAAM,GAAG,CAAC,GAAG8E,MAAM,GAAGA,MAAM,CAAC,CAAC,CAAC;EAC/C;EAEA;;;;;;;;;;;;;;;;EAgBA,MAAM3B,YAAYA,CACdlE,MAAsC,EACtCC,OAAyB;IAC3B,IAAI,IAAI,CAAC0E,yBAAyB,IAAI,IAAI,EAAE;MAC1C,IAAI,CAACa,4BAA4B,CAC7B,MAAM,IAAI,CAACD,4BAA4B,EAAE,CAAC;;IAEhDvF,MAAM,GAAG,IAAI,CAACmE,eAAe,CAACnE,MAAM,CAAC;IACrCC,OAAO,GAAG,IAAI,CAACoF,gBAAgB,CAACpF,OAAO,CAAC;IACxC,MAAM4F,MAAM,GAAG,MAAM,IAAI,CAAC/F,QAAQ,CAACoE,YAAY,CAAClE,MAAM,EAAEC,OAAO,CAAC;IAChE,OAAO4F,MAAM,CAAC9E,MAAM,GAAG,CAAC,GAAG8E,MAAM,GAAGA,MAAM,CAAC,CAAC,CAAC;EAC/C;EAEA;;;;;;EAMAC,sBAAsBA,CAAA;IACpB,OAAO,IAAI,CAAChG,QAAQ,CAACgG,sBAAsB,EAAE;EAC/C;EAEA;;;;;;EAMAC,0BAA0BA,CAAA;IACxB,IAAI,CAACjG,QAAQ,CAACiG,0BAA0B,EAAE;EAC5C;EAEQhD,4BAA4BA,CAACkC,GAAmB;IACtD,OAAOJ,MAAM,CAACC,IAAI,CAACG,GAAG,CAAC,CAACD,MAAM,CAAC,CAACgB,MAAuB,EAAEC,GAAG,KAAI;MAC9DD,MAAM,CAACC,GAAG,CAAC,GAAG,CAAChB,GAAG,CAACgB,GAAG,CAAC,CAAC;MACxB,OAAOD,MAAM;IACf,CAAC,EAAE,EAAE,CAAC;EACR;EAEA;;;;;EAKAhH,OAAOA,CAAA;IACL,IAAI,CAACc,QAAQ,CAACd,OAAO,EAAE;IAEvB,IAAI,IAAI,CAACkE,WAAW,EAAE;MACpB,IAAI,CAACA,WAAW,CAAClE,OAAO,EAAE;MAC1B,IAAI,IAAI,CAAC2F,yBAAyB,EAAE;QAClC3F,OAAO,CAAC,IAAI,CAAC2F,yBAAyB,CAAC;;;IAI3C,IAAI,CAACzD,eAAe,CAAClC,OAAO,EAAE;EAChC;;AAGF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA+BA,OAAO,eAAekH,cAAcA,CAChCtF,QAA6B,EACpB;EAAA,IADsBuF,OAAA,GAAArF,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAA0B,EAAE;EAAA,IAC3DG,IAAI,GAAAH,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG7B,EAAE;EACX,IAAI2B,QAAQ,IAAI,IAAI,EAAE;IACpB,MAAM,IAAIgB,KAAK,CACX,oEAAoE,GACpE,sCAAsC,CAAC;;EAE7C,IAAIuE,OAAO,IAAI,IAAI,EAAE;IACnBA,OAAO,GAAG,EAAE;;EAGd,IAAIA,OAAO,CAACC,SAAS,IAAI,OAAOxF,QAAQ,KAAK,QAAQ,EAAE;IACrDA,QAAQ,GAAGyF,WAAW,CAACzF,QAAQ,CAAC;;EAElC,MAAM0F,KAAK,GAAG,IAAI5G,UAAU,CAACkB,QAAQ,EAAEuF,OAAO,EAAElF,IAAI,CAAC;EACrD,MAAMqF,KAAK,CAACjF,IAAI,EAAE;EAClB,OAAOiF,KAAK;AACd;AAEA;;;;;;;;;;AAUA,OAAM,SAAUC,kBAAkBA,CAC9BC,WAC2D;EAE7D,IAAIA,WAAW,IAAI,IAAI,EAAE;IACvB,MAAM,IAAI5E,KAAK,CACX,kEAAkE,GAClE,sDAAsD,CAAC;;EAG7D,IAAI6E,SAA2B;EAC/B,IAAID,WAAW,YAAYpC,KAAK,EAAE;IAChC,MAAM,CAACsC,SAAS,EAAExG,OAAO,CAAC,GAAGsG,WAAW;IACxC,IAAI,CAACE,SAAS,EAAE;MACd,MAAM,IAAI9E,KAAK,CAAC,kDAAkD,CAAC;;IAErE,IAAI,CAAC1B,OAAO,IAAI,EAAEA,OAAO,YAAYyG,WAAW,CAAC,EAAE;MACjD,MAAM,IAAI/E,KAAK,CACX,yDAAyD,GACzD,YAAY,CAAC;;IAEnB,IAAI,EAAE,eAAe,IAAI8E,SAAS,CAAC,EAAE;MACnC,MAAM,IAAI9E,KAAK,CAAC,yCAAyC,CAAC;;IAE5D,IAAI,EAAE,iBAAiB,IAAI8E,SAAS,CAAC,EAAE;MACrC,MAAM,IAAI9E,KAAK,CAAC,2CAA2C,CAAC;;IAG9D,MAAMU,WAAW,GAAGrD,EAAE,CAAC2H,cAAc,CAACF,SAAS,CAACG,eAAe,CAAC;IAChE,MAAMC,cAAc,GAChB7H,EAAE,CAAC8H,4BAA4B,CAACL,SAAS,EAAEpE,WAAW,EAAEpC,OAAO,CAAC;IACpEuG,SAAS,GAAGxH,EAAE,CAAC+H,cAAc,CAACF,cAAc,CAAC;GAC9C,MAAM,IAAI,MAAM,IAAIN,WAAW,EAAE;IAChC;IACAC,SAAS,GAAGD,WAAW;GACxB,MAAM,IACH,eAAe,IAAIA,WAAW,IAAI,aAAa,IAAIA,WAAW,IAC9D,YAAY,IAAIA,WAAW,EAAE;IAC/B;IACAC,SAAS,GAAGxH,EAAE,CAAC+H,cAAc,CAACR,WAAW,CAAC;GAC3C,MAAM;IACL,MAAM,IAAI5E,KAAK,CAAC,sBAAsB,CAAC;;EAGzC,MAAM0E,KAAK,GAAG,IAAI5G,UAAU,CAAC+G,SAAS,CAAC;EACvCH,KAAK,CAACjF,IAAI,EAAE;EACZ,OAAOiF,KAAK;AACd;AAEA,SAASD,WAAWA,CAACzF,QAAgB;EACnC,IAAI,CAACA,QAAQ,CAACqG,QAAQ,CAAC,GAAG,CAAC,EAAE;IAC3BrG,QAAQ,GAAIA,QAAQ,GAAI,GAAG;;EAE7B,UAAAiB,MAAA,CAAUjB,QAAQ,EAAAiB,MAAA,CAAGpC,kBAAkB,EAAAoC,MAAA,CAAGrC,kBAAkB;AAC9D", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}