{"ast": null, "code": "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { binaryInsert } from './non_max_suppression_util';\nexport function nonMaxSuppressionV3Impl(boxes, scores, maxOutputSize, iouThreshold, scoreThreshold) {\n  return nonMaxSuppressionImpl_(boxes, scores, maxOutputSize, iouThreshold, scoreThreshold, 0 /* softNmsSigma */);\n}\nexport function nonMaxSuppressionV4Impl(boxes, scores, maxOutputSize, iouThreshold, scoreThreshold, padToMaxOutputSize) {\n  return nonMaxSuppressionImpl_(boxes, scores, maxOutputSize, iouThreshold, scoreThreshold, 0 /* softNmsSigma */, false /* returnScoresTensor */, padToMaxOutputSize /* padToMaxOutputSize */, true\n  /* returnValidOutputs */);\n}\nexport function nonMaxSuppressionV5Impl(boxes, scores, maxOutputSize, iouThreshold, scoreThreshold, softNmsSigma) {\n  return nonMaxSuppressionImpl_(boxes, scores, maxOutputSize, iouThreshold, scoreThreshold, softNmsSigma, true /* returnScoresTensor */);\n}\nfunction nonMaxSuppressionImpl_(boxes, scores, maxOutputSize, iouThreshold, scoreThreshold, softNmsSigma) {\n  let returnScoresTensor = arguments.length > 6 && arguments[6] !== undefined ? arguments[6] : false;\n  let padToMaxOutputSize = arguments.length > 7 && arguments[7] !== undefined ? arguments[7] : false;\n  let returnValidOutputs = arguments.length > 8 && arguments[8] !== undefined ? arguments[8] : false;\n  // The list is sorted in ascending order, so that we can always pop the\n  // candidate with the largest score in O(1) time.\n  const candidates = [];\n  for (let i = 0; i < scores.length; i++) {\n    if (scores[i] > scoreThreshold) {\n      candidates.push({\n        score: scores[i],\n        boxIndex: i,\n        suppressBeginIndex: 0\n      });\n    }\n  }\n  candidates.sort(ascendingComparator);\n  // If softNmsSigma is 0, the outcome of this algorithm is exactly same as\n  // before.\n  const scale = softNmsSigma > 0 ? -0.5 / softNmsSigma : 0.0;\n  const selectedIndices = [];\n  const selectedScores = [];\n  while (selectedIndices.length < maxOutputSize && candidates.length > 0) {\n    const candidate = candidates.pop();\n    const {\n      score: originalScore,\n      boxIndex,\n      suppressBeginIndex\n    } = candidate;\n    if (originalScore < scoreThreshold) {\n      break;\n    }\n    // Overlapping boxes are likely to have similar scores, therefore we\n    // iterate through the previously selected boxes backwards in order to\n    // see if candidate's score should be suppressed. We use\n    // suppressBeginIndex to track and ensure a candidate can be suppressed\n    // by a selected box no more than once. Also, if the overlap exceeds\n    // iouThreshold, we simply ignore the candidate.\n    let ignoreCandidate = false;\n    for (let j = selectedIndices.length - 1; j >= suppressBeginIndex; --j) {\n      const iou = intersectionOverUnion(boxes, boxIndex, selectedIndices[j]);\n      if (iou >= iouThreshold) {\n        ignoreCandidate = true;\n        break;\n      }\n      candidate.score = candidate.score * suppressWeight(iouThreshold, scale, iou);\n      if (candidate.score <= scoreThreshold) {\n        break;\n      }\n    }\n    // At this point, if `candidate.score` has not dropped below\n    // `scoreThreshold`, then we know that we went through all of the\n    // previous selections and can safely update `suppressBeginIndex` to the\n    // end of the selected array. Then we can re-insert the candidate with\n    // the updated score and suppressBeginIndex back in the candidate list.\n    // If on the other hand, `candidate.score` has dropped below the score\n    // threshold, we will not add it back to the candidates list.\n    candidate.suppressBeginIndex = selectedIndices.length;\n    if (!ignoreCandidate) {\n      // Candidate has passed all the tests, and is not suppressed, so\n      // select the candidate.\n      if (candidate.score === originalScore) {\n        selectedIndices.push(boxIndex);\n        selectedScores.push(candidate.score);\n      } else if (candidate.score > scoreThreshold) {\n        // Candidate's score is suppressed but is still high enough to be\n        // considered, so add back to the candidates list.\n        binaryInsert(candidates, candidate, ascendingComparator);\n      }\n    }\n  }\n  // NonMaxSuppressionV4 feature: padding output to maxOutputSize.\n  const validOutputs = selectedIndices.length;\n  const elemsToPad = maxOutputSize - validOutputs;\n  if (padToMaxOutputSize && elemsToPad > 0) {\n    selectedIndices.push(...new Array(elemsToPad).fill(0));\n    selectedScores.push(...new Array(elemsToPad).fill(0.0));\n  }\n  const result = {\n    selectedIndices\n  };\n  if (returnScoresTensor) {\n    result['selectedScores'] = selectedScores;\n  }\n  if (returnValidOutputs) {\n    result['validOutputs'] = validOutputs;\n  }\n  return result;\n}\nfunction intersectionOverUnion(boxes, i, j) {\n  const iCoord = boxes.subarray(i * 4, i * 4 + 4);\n  const jCoord = boxes.subarray(j * 4, j * 4 + 4);\n  const yminI = Math.min(iCoord[0], iCoord[2]);\n  const xminI = Math.min(iCoord[1], iCoord[3]);\n  const ymaxI = Math.max(iCoord[0], iCoord[2]);\n  const xmaxI = Math.max(iCoord[1], iCoord[3]);\n  const yminJ = Math.min(jCoord[0], jCoord[2]);\n  const xminJ = Math.min(jCoord[1], jCoord[3]);\n  const ymaxJ = Math.max(jCoord[0], jCoord[2]);\n  const xmaxJ = Math.max(jCoord[1], jCoord[3]);\n  const areaI = (ymaxI - yminI) * (xmaxI - xminI);\n  const areaJ = (ymaxJ - yminJ) * (xmaxJ - xminJ);\n  if (areaI <= 0 || areaJ <= 0) {\n    return 0.0;\n  }\n  const intersectionYmin = Math.max(yminI, yminJ);\n  const intersectionXmin = Math.max(xminI, xminJ);\n  const intersectionYmax = Math.min(ymaxI, ymaxJ);\n  const intersectionXmax = Math.min(xmaxI, xmaxJ);\n  const intersectionArea = Math.max(intersectionYmax - intersectionYmin, 0.0) * Math.max(intersectionXmax - intersectionXmin, 0.0);\n  return intersectionArea / (areaI + areaJ - intersectionArea);\n}\n// A Gaussian penalty function, this method always returns values in [0, 1].\n// The weight is a function of similarity, the more overlap two boxes are, the\n// smaller the weight is,meaning highly overlapping boxes will be significantly\n// penalized. On the other hand, a non-overlapping box will not be penalized.\nfunction suppressWeight(iouThreshold, scale, iou) {\n  const weight = Math.exp(scale * iou * iou);\n  return iou <= iouThreshold ? weight : 0.0;\n}\nfunction ascendingComparator(c1, c2) {\n  // For objects with same scores, we make the object with the larger index go\n  // first. In an array that pops from the end, this means that the object with\n  // the smaller index will be popped first. This ensures the same output as\n  // the TensorFlow python version.\n  return c1.score - c2.score || c1.score === c2.score && c2.boxIndex - c1.boxIndex;\n}", "map": {"version": 3, "names": ["binaryInsert", "nonMaxSuppressionV3Impl", "boxes", "scores", "maxOutputSize", "iouThreshold", "scoreThreshold", "nonMaxSuppressionImpl_", "nonMaxSuppressionV4Impl", "padToMaxOutputSize", "nonMaxSuppressionV5Impl", "softNmsSigma", "returnScoresTensor", "arguments", "length", "undefined", "returnValidOutputs", "candidates", "i", "push", "score", "boxIndex", "suppressBeginIndex", "sort", "ascendingComparator", "scale", "selectedIndices", "selectedScores", "candidate", "pop", "originalScore", "ignoreCandidate", "j", "iou", "intersectionOverUnion", "suppressWeight", "validOutputs", "elemsToPad", "Array", "fill", "result", "iCoord", "subarray", "jCoord", "yminI", "Math", "min", "xminI", "ymaxI", "max", "xmaxI", "yminJ", "xminJ", "ymaxJ", "xmaxJ", "areaI", "areaJ", "intersectionYmin", "intersectionXmin", "intersectionYmax", "intersectionXmax", "intersectionArea", "weight", "exp", "c1", "c2"], "sources": ["C:\\tfjs-core\\src\\backends\\non_max_suppression_impl.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {TypedArray} from '../types';\nimport {binaryInsert} from './non_max_suppression_util';\n\n/**\n * Implementation of the NonMaxSuppression kernel shared between webgl and cpu.\n */\ninterface Candidate {\n  score: number;\n  boxIndex: number;\n  suppressBeginIndex: number;\n}\n\ninterface NonMaxSuppressionResult {\n  selectedIndices: number[];\n  selectedScores?: number[];\n  validOutputs?: number;\n}\n\nexport function nonMaxSuppressionV3Impl(\n    boxes: TypedArray, scores: TypedArray, maxOutputSize: number,\n    iouThreshold: number, scoreThreshold: number): NonMaxSuppressionResult {\n  return nonMaxSuppressionImpl_(\n      boxes, scores, maxOutputSize, iouThreshold, scoreThreshold,\n      0 /* softNmsSigma */);\n}\n\nexport function nonMaxSuppressionV4Impl(\n    boxes: TypedArray, scores: TypedArray, maxOutputSize: number,\n    iouThreshold: number, scoreThreshold: number,\n    padToMaxOutputSize: boolean): NonMaxSuppressionResult {\n  return nonMaxSuppressionImpl_(\n      boxes, scores, maxOutputSize, iouThreshold, scoreThreshold,\n      0 /* softNmsSigma */, false /* returnScoresTensor */,\n      padToMaxOutputSize /* padToMaxOutputSize */, true\n      /* returnValidOutputs */);\n}\n\nexport function nonMaxSuppressionV5Impl(\n    boxes: TypedArray, scores: TypedArray, maxOutputSize: number,\n    iouThreshold: number, scoreThreshold: number,\n    softNmsSigma: number): NonMaxSuppressionResult {\n  return nonMaxSuppressionImpl_(\n      boxes, scores, maxOutputSize, iouThreshold, scoreThreshold, softNmsSigma,\n      true /* returnScoresTensor */);\n}\n\nfunction nonMaxSuppressionImpl_(\n    boxes: TypedArray, scores: TypedArray, maxOutputSize: number,\n    iouThreshold: number, scoreThreshold: number, softNmsSigma: number,\n    returnScoresTensor = false, padToMaxOutputSize = false,\n    returnValidOutputs = false): NonMaxSuppressionResult {\n  // The list is sorted in ascending order, so that we can always pop the\n  // candidate with the largest score in O(1) time.\n  const candidates = [];\n\n  for (let i = 0; i < scores.length; i++) {\n    if (scores[i] > scoreThreshold) {\n      candidates.push({score: scores[i], boxIndex: i, suppressBeginIndex: 0});\n    }\n  }\n\n  candidates.sort(ascendingComparator);\n\n  // If softNmsSigma is 0, the outcome of this algorithm is exactly same as\n  // before.\n  const scale = softNmsSigma > 0 ? (-0.5 / softNmsSigma) : 0.0;\n\n  const selectedIndices: number[] = [];\n  const selectedScores: number[] = [];\n\n  while (selectedIndices.length < maxOutputSize && candidates.length > 0) {\n    const candidate = candidates.pop();\n    const {score: originalScore, boxIndex, suppressBeginIndex} = candidate;\n\n    if (originalScore < scoreThreshold) {\n      break;\n    }\n\n    // Overlapping boxes are likely to have similar scores, therefore we\n    // iterate through the previously selected boxes backwards in order to\n    // see if candidate's score should be suppressed. We use\n    // suppressBeginIndex to track and ensure a candidate can be suppressed\n    // by a selected box no more than once. Also, if the overlap exceeds\n    // iouThreshold, we simply ignore the candidate.\n    let ignoreCandidate = false;\n    for (let j = selectedIndices.length - 1; j >= suppressBeginIndex; --j) {\n      const iou = intersectionOverUnion(boxes, boxIndex, selectedIndices[j]);\n\n      if (iou >= iouThreshold) {\n        ignoreCandidate = true;\n        break;\n      }\n\n      candidate.score =\n          candidate.score * suppressWeight(iouThreshold, scale, iou);\n\n      if (candidate.score <= scoreThreshold) {\n        break;\n      }\n    }\n\n    // At this point, if `candidate.score` has not dropped below\n    // `scoreThreshold`, then we know that we went through all of the\n    // previous selections and can safely update `suppressBeginIndex` to the\n    // end of the selected array. Then we can re-insert the candidate with\n    // the updated score and suppressBeginIndex back in the candidate list.\n    // If on the other hand, `candidate.score` has dropped below the score\n    // threshold, we will not add it back to the candidates list.\n    candidate.suppressBeginIndex = selectedIndices.length;\n\n    if (!ignoreCandidate) {\n      // Candidate has passed all the tests, and is not suppressed, so\n      // select the candidate.\n      if (candidate.score === originalScore) {\n        selectedIndices.push(boxIndex);\n        selectedScores.push(candidate.score);\n      } else if (candidate.score > scoreThreshold) {\n        // Candidate's score is suppressed but is still high enough to be\n        // considered, so add back to the candidates list.\n        binaryInsert(candidates, candidate, ascendingComparator);\n      }\n    }\n  }\n\n  // NonMaxSuppressionV4 feature: padding output to maxOutputSize.\n  const validOutputs = selectedIndices.length;\n  const elemsToPad = maxOutputSize - validOutputs;\n\n  if (padToMaxOutputSize && elemsToPad > 0) {\n    selectedIndices.push(...new Array(elemsToPad).fill(0));\n    selectedScores.push(...new Array(elemsToPad).fill(0.0));\n  }\n\n  const result: NonMaxSuppressionResult = {selectedIndices};\n\n  if (returnScoresTensor) {\n    result['selectedScores'] = selectedScores;\n  }\n\n  if (returnValidOutputs) {\n    result['validOutputs'] = validOutputs;\n  }\n\n  return result;\n}\n\nfunction intersectionOverUnion(boxes: TypedArray, i: number, j: number) {\n  const iCoord = boxes.subarray(i * 4, i * 4 + 4);\n  const jCoord = boxes.subarray(j * 4, j * 4 + 4);\n  const yminI = Math.min(iCoord[0], iCoord[2]);\n  const xminI = Math.min(iCoord[1], iCoord[3]);\n  const ymaxI = Math.max(iCoord[0], iCoord[2]);\n  const xmaxI = Math.max(iCoord[1], iCoord[3]);\n  const yminJ = Math.min(jCoord[0], jCoord[2]);\n  const xminJ = Math.min(jCoord[1], jCoord[3]);\n  const ymaxJ = Math.max(jCoord[0], jCoord[2]);\n  const xmaxJ = Math.max(jCoord[1], jCoord[3]);\n  const areaI = (ymaxI - yminI) * (xmaxI - xminI);\n  const areaJ = (ymaxJ - yminJ) * (xmaxJ - xminJ);\n  if (areaI <= 0 || areaJ <= 0) {\n    return 0.0;\n  }\n  const intersectionYmin = Math.max(yminI, yminJ);\n  const intersectionXmin = Math.max(xminI, xminJ);\n  const intersectionYmax = Math.min(ymaxI, ymaxJ);\n  const intersectionXmax = Math.min(xmaxI, xmaxJ);\n  const intersectionArea = Math.max(intersectionYmax - intersectionYmin, 0.0) *\n      Math.max(intersectionXmax - intersectionXmin, 0.0);\n  return intersectionArea / (areaI + areaJ - intersectionArea);\n}\n\n// A Gaussian penalty function, this method always returns values in [0, 1].\n// The weight is a function of similarity, the more overlap two boxes are, the\n// smaller the weight is,meaning highly overlapping boxes will be significantly\n// penalized. On the other hand, a non-overlapping box will not be penalized.\nfunction suppressWeight(iouThreshold: number, scale: number, iou: number) {\n  const weight = Math.exp(scale * iou * iou);\n  return iou <= iouThreshold ? weight : 0.0;\n}\n\nfunction ascendingComparator(c1: Candidate, c2: Candidate) {\n  // For objects with same scores, we make the object with the larger index go\n  // first. In an array that pops from the end, this means that the object with\n  // the smaller index will be popped first. This ensures the same output as\n  // the TensorFlow python version.\n  return (c1.score - c2.score) ||\n      ((c1.score === c2.score) && (c2.boxIndex - c1.boxIndex));\n}\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAkBA,SAAQA,YAAY,QAAO,4BAA4B;AAiBvD,OAAM,SAAUC,uBAAuBA,CACnCC,KAAiB,EAAEC,MAAkB,EAAEC,aAAqB,EAC5DC,YAAoB,EAAEC,cAAsB;EAC9C,OAAOC,sBAAsB,CACzBL,KAAK,EAAEC,MAAM,EAAEC,aAAa,EAAEC,YAAY,EAAEC,cAAc,EAC1D,CAAC,CAAC,kBAAkB,CAAC;AAC3B;AAEA,OAAM,SAAUE,uBAAuBA,CACnCN,KAAiB,EAAEC,MAAkB,EAAEC,aAAqB,EAC5DC,YAAoB,EAAEC,cAAsB,EAC5CG,kBAA2B;EAC7B,OAAOF,sBAAsB,CACzBL,KAAK,EAAEC,MAAM,EAAEC,aAAa,EAAEC,YAAY,EAAEC,cAAc,EAC1D,CAAC,CAAC,oBAAoB,KAAK,CAAC,0BAC5BG,kBAAkB,CAAC,0BAA0B;EAC7C,wBAAwB,CAAC;AAC/B;AAEA,OAAM,SAAUC,uBAAuBA,CACnCR,KAAiB,EAAEC,MAAkB,EAAEC,aAAqB,EAC5DC,YAAoB,EAAEC,cAAsB,EAC5CK,YAAoB;EACtB,OAAOJ,sBAAsB,CACzBL,KAAK,EAAEC,MAAM,EAAEC,aAAa,EAAEC,YAAY,EAAEC,cAAc,EAAEK,YAAY,EACxE,IAAI,CAAC,wBAAwB,CAAC;AACpC;AAEA,SAASJ,sBAAsBA,CAC3BL,KAAiB,EAAEC,MAAkB,EAAEC,aAAqB,EAC5DC,YAAoB,EAAEC,cAAsB,EAAEK,YAAoB,EAExC;EAAA,IAD1BC,kBAAkB,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,KAAK;EAAA,IAAEJ,kBAAkB,GAAAI,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,KAAK;EAAA,IACtDG,kBAAkB,GAAAH,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,KAAK;EAC5B;EACA;EACA,MAAMI,UAAU,GAAG,EAAE;EAErB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGf,MAAM,CAACW,MAAM,EAAEI,CAAC,EAAE,EAAE;IACtC,IAAIf,MAAM,CAACe,CAAC,CAAC,GAAGZ,cAAc,EAAE;MAC9BW,UAAU,CAACE,IAAI,CAAC;QAACC,KAAK,EAAEjB,MAAM,CAACe,CAAC,CAAC;QAAEG,QAAQ,EAAEH,CAAC;QAAEI,kBAAkB,EAAE;MAAC,CAAC,CAAC;;;EAI3EL,UAAU,CAACM,IAAI,CAACC,mBAAmB,CAAC;EAEpC;EACA;EACA,MAAMC,KAAK,GAAGd,YAAY,GAAG,CAAC,GAAI,CAAC,GAAG,GAAGA,YAAY,GAAI,GAAG;EAE5D,MAAMe,eAAe,GAAa,EAAE;EACpC,MAAMC,cAAc,GAAa,EAAE;EAEnC,OAAOD,eAAe,CAACZ,MAAM,GAAGV,aAAa,IAAIa,UAAU,CAACH,MAAM,GAAG,CAAC,EAAE;IACtE,MAAMc,SAAS,GAAGX,UAAU,CAACY,GAAG,EAAE;IAClC,MAAM;MAACT,KAAK,EAAEU,aAAa;MAAET,QAAQ;MAAEC;IAAkB,CAAC,GAAGM,SAAS;IAEtE,IAAIE,aAAa,GAAGxB,cAAc,EAAE;MAClC;;IAGF;IACA;IACA;IACA;IACA;IACA;IACA,IAAIyB,eAAe,GAAG,KAAK;IAC3B,KAAK,IAAIC,CAAC,GAAGN,eAAe,CAACZ,MAAM,GAAG,CAAC,EAAEkB,CAAC,IAAIV,kBAAkB,EAAE,EAAEU,CAAC,EAAE;MACrE,MAAMC,GAAG,GAAGC,qBAAqB,CAAChC,KAAK,EAAEmB,QAAQ,EAAEK,eAAe,CAACM,CAAC,CAAC,CAAC;MAEtE,IAAIC,GAAG,IAAI5B,YAAY,EAAE;QACvB0B,eAAe,GAAG,IAAI;QACtB;;MAGFH,SAAS,CAACR,KAAK,GACXQ,SAAS,CAACR,KAAK,GAAGe,cAAc,CAAC9B,YAAY,EAAEoB,KAAK,EAAEQ,GAAG,CAAC;MAE9D,IAAIL,SAAS,CAACR,KAAK,IAAId,cAAc,EAAE;QACrC;;;IAIJ;IACA;IACA;IACA;IACA;IACA;IACA;IACAsB,SAAS,CAACN,kBAAkB,GAAGI,eAAe,CAACZ,MAAM;IAErD,IAAI,CAACiB,eAAe,EAAE;MACpB;MACA;MACA,IAAIH,SAAS,CAACR,KAAK,KAAKU,aAAa,EAAE;QACrCJ,eAAe,CAACP,IAAI,CAACE,QAAQ,CAAC;QAC9BM,cAAc,CAACR,IAAI,CAACS,SAAS,CAACR,KAAK,CAAC;OACrC,MAAM,IAAIQ,SAAS,CAACR,KAAK,GAAGd,cAAc,EAAE;QAC3C;QACA;QACAN,YAAY,CAACiB,UAAU,EAAEW,SAAS,EAAEJ,mBAAmB,CAAC;;;;EAK9D;EACA,MAAMY,YAAY,GAAGV,eAAe,CAACZ,MAAM;EAC3C,MAAMuB,UAAU,GAAGjC,aAAa,GAAGgC,YAAY;EAE/C,IAAI3B,kBAAkB,IAAI4B,UAAU,GAAG,CAAC,EAAE;IACxCX,eAAe,CAACP,IAAI,CAAC,GAAG,IAAImB,KAAK,CAACD,UAAU,CAAC,CAACE,IAAI,CAAC,CAAC,CAAC,CAAC;IACtDZ,cAAc,CAACR,IAAI,CAAC,GAAG,IAAImB,KAAK,CAACD,UAAU,CAAC,CAACE,IAAI,CAAC,GAAG,CAAC,CAAC;;EAGzD,MAAMC,MAAM,GAA4B;IAACd;EAAe,CAAC;EAEzD,IAAId,kBAAkB,EAAE;IACtB4B,MAAM,CAAC,gBAAgB,CAAC,GAAGb,cAAc;;EAG3C,IAAIX,kBAAkB,EAAE;IACtBwB,MAAM,CAAC,cAAc,CAAC,GAAGJ,YAAY;;EAGvC,OAAOI,MAAM;AACf;AAEA,SAASN,qBAAqBA,CAAChC,KAAiB,EAAEgB,CAAS,EAAEc,CAAS;EACpE,MAAMS,MAAM,GAAGvC,KAAK,CAACwC,QAAQ,CAACxB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;EAC/C,MAAMyB,MAAM,GAAGzC,KAAK,CAACwC,QAAQ,CAACV,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;EAC/C,MAAMY,KAAK,GAAGC,IAAI,CAACC,GAAG,CAACL,MAAM,CAAC,CAAC,CAAC,EAAEA,MAAM,CAAC,CAAC,CAAC,CAAC;EAC5C,MAAMM,KAAK,GAAGF,IAAI,CAACC,GAAG,CAACL,MAAM,CAAC,CAAC,CAAC,EAAEA,MAAM,CAAC,CAAC,CAAC,CAAC;EAC5C,MAAMO,KAAK,GAAGH,IAAI,CAACI,GAAG,CAACR,MAAM,CAAC,CAAC,CAAC,EAAEA,MAAM,CAAC,CAAC,CAAC,CAAC;EAC5C,MAAMS,KAAK,GAAGL,IAAI,CAACI,GAAG,CAACR,MAAM,CAAC,CAAC,CAAC,EAAEA,MAAM,CAAC,CAAC,CAAC,CAAC;EAC5C,MAAMU,KAAK,GAAGN,IAAI,CAACC,GAAG,CAACH,MAAM,CAAC,CAAC,CAAC,EAAEA,MAAM,CAAC,CAAC,CAAC,CAAC;EAC5C,MAAMS,KAAK,GAAGP,IAAI,CAACC,GAAG,CAACH,MAAM,CAAC,CAAC,CAAC,EAAEA,MAAM,CAAC,CAAC,CAAC,CAAC;EAC5C,MAAMU,KAAK,GAAGR,IAAI,CAACI,GAAG,CAACN,MAAM,CAAC,CAAC,CAAC,EAAEA,MAAM,CAAC,CAAC,CAAC,CAAC;EAC5C,MAAMW,KAAK,GAAGT,IAAI,CAACI,GAAG,CAACN,MAAM,CAAC,CAAC,CAAC,EAAEA,MAAM,CAAC,CAAC,CAAC,CAAC;EAC5C,MAAMY,KAAK,GAAG,CAACP,KAAK,GAAGJ,KAAK,KAAKM,KAAK,GAAGH,KAAK,CAAC;EAC/C,MAAMS,KAAK,GAAG,CAACH,KAAK,GAAGF,KAAK,KAAKG,KAAK,GAAGF,KAAK,CAAC;EAC/C,IAAIG,KAAK,IAAI,CAAC,IAAIC,KAAK,IAAI,CAAC,EAAE;IAC5B,OAAO,GAAG;;EAEZ,MAAMC,gBAAgB,GAAGZ,IAAI,CAACI,GAAG,CAACL,KAAK,EAAEO,KAAK,CAAC;EAC/C,MAAMO,gBAAgB,GAAGb,IAAI,CAACI,GAAG,CAACF,KAAK,EAAEK,KAAK,CAAC;EAC/C,MAAMO,gBAAgB,GAAGd,IAAI,CAACC,GAAG,CAACE,KAAK,EAAEK,KAAK,CAAC;EAC/C,MAAMO,gBAAgB,GAAGf,IAAI,CAACC,GAAG,CAACI,KAAK,EAAEI,KAAK,CAAC;EAC/C,MAAMO,gBAAgB,GAAGhB,IAAI,CAACI,GAAG,CAACU,gBAAgB,GAAGF,gBAAgB,EAAE,GAAG,CAAC,GACvEZ,IAAI,CAACI,GAAG,CAACW,gBAAgB,GAAGF,gBAAgB,EAAE,GAAG,CAAC;EACtD,OAAOG,gBAAgB,IAAIN,KAAK,GAAGC,KAAK,GAAGK,gBAAgB,CAAC;AAC9D;AAEA;AACA;AACA;AACA;AACA,SAAS1B,cAAcA,CAAC9B,YAAoB,EAAEoB,KAAa,EAAEQ,GAAW;EACtE,MAAM6B,MAAM,GAAGjB,IAAI,CAACkB,GAAG,CAACtC,KAAK,GAAGQ,GAAG,GAAGA,GAAG,CAAC;EAC1C,OAAOA,GAAG,IAAI5B,YAAY,GAAGyD,MAAM,GAAG,GAAG;AAC3C;AAEA,SAAStC,mBAAmBA,CAACwC,EAAa,EAAEC,EAAa;EACvD;EACA;EACA;EACA;EACA,OAAQD,EAAE,CAAC5C,KAAK,GAAG6C,EAAE,CAAC7C,KAAK,IACrB4C,EAAE,CAAC5C,KAAK,KAAK6C,EAAE,CAAC7C,KAAK,IAAM6C,EAAE,CAAC5C,QAAQ,GAAG2C,EAAE,CAAC3C,QAAU;AAC9D", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}