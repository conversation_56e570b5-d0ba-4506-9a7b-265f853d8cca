{"ast": null, "code": "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { kernel_impls, NonMaxSuppressionV3 } from '@tensorflow/tfjs-core';\nconst nonMaxSuppressionV3Impl = kernel_impls.nonMaxSuppressionV3Impl;\nimport { assertNotComplex } from '../cpu_util';\nexport function nonMaxSuppressionV3(args) {\n  const {\n    inputs,\n    backend,\n    attrs\n  } = args;\n  const {\n    boxes,\n    scores\n  } = inputs;\n  const {\n    maxOutputSize,\n    iouThreshold,\n    scoreThreshold\n  } = attrs;\n  assertNotComplex(boxes, 'NonMaxSuppression');\n  const boxesVals = backend.data.get(boxes.dataId).values;\n  const scoresVals = backend.data.get(scores.dataId).values;\n  const {\n    selectedIndices\n  } = nonMaxSuppressionV3Impl(boxesVals, scoresVals, maxOutputSize, iouThreshold, scoreThreshold);\n  return backend.makeTensorInfo([selectedIndices.length], 'int32', new Int32Array(selectedIndices));\n}\nexport const nonMaxSuppressionV3Config = {\n  kernelName: NonMaxSuppressionV3,\n  backendName: 'cpu',\n  kernelFunc: nonMaxSuppressionV3\n};", "map": {"version": 3, "names": ["kernel_impls", "NonMaxSuppressionV3", "nonMaxSuppressionV3Impl", "assertNotComplex", "nonMaxSuppressionV3", "args", "inputs", "backend", "attrs", "boxes", "scores", "maxOutputSize", "iouThreshold", "scoreThreshold", "boxesVals", "data", "get", "dataId", "values", "scoresVals", "selectedIndices", "makeTensorInfo", "length", "Int32Array", "nonMaxSuppressionV3Config", "kernelName", "backendName", "kernelFunc"], "sources": ["C:\\tfjs-backend-cpu\\src\\kernels\\NonMaxSuppressionV3.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {kernel_impls, KernelConfig, KernelFunc, NonMaxSuppressionV3, NonMaxSuppressionV3Attrs, NonMaxSuppressionV3Inputs, TensorInfo, TypedArray} from '@tensorflow/tfjs-core';\n\nconst nonMaxSuppressionV3Impl = kernel_impls.nonMaxSuppressionV3Impl;\n\nimport {MathBackendCPU} from '../backend_cpu';\nimport {assertNotComplex} from '../cpu_util';\n\nexport function nonMaxSuppressionV3(args: {\n  inputs: NonMaxSuppressionV3Inputs,\n  backend: MathBackendCPU,\n  attrs: NonMaxSuppressionV3Attrs\n}): TensorInfo {\n  const {inputs, backend, attrs} = args;\n  const {boxes, scores} = inputs;\n  const {maxOutputSize, iouThreshold, scoreThreshold} = attrs;\n\n  assertNotComplex(boxes, 'NonMaxSuppression');\n\n  const boxesVals = backend.data.get(boxes.dataId).values as TypedArray;\n  const scoresVals = backend.data.get(scores.dataId).values as TypedArray;\n\n  const {selectedIndices} = nonMaxSuppressionV3Impl(\n      boxesVals, scoresVals, maxOutputSize, iouThreshold, scoreThreshold);\n\n  return backend.makeTensorInfo(\n      [selectedIndices.length], 'int32', new Int32Array(selectedIndices));\n}\n\nexport const nonMaxSuppressionV3Config: KernelConfig = {\n  kernelName: NonMaxSuppressionV3,\n  backendName: 'cpu',\n  kernelFunc: nonMaxSuppressionV3 as unknown as KernelFunc\n};\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAiBA,SAAQA,YAAY,EAA4BC,mBAAmB,QAAoF,uBAAuB;AAE9K,MAAMC,uBAAuB,GAAGF,YAAY,CAACE,uBAAuB;AAGpE,SAAQC,gBAAgB,QAAO,aAAa;AAE5C,OAAM,SAAUC,mBAAmBA,CAACC,IAInC;EACC,MAAM;IAACC,MAAM;IAAEC,OAAO;IAAEC;EAAK,CAAC,GAAGH,IAAI;EACrC,MAAM;IAACI,KAAK;IAAEC;EAAM,CAAC,GAAGJ,MAAM;EAC9B,MAAM;IAACK,aAAa;IAAEC,YAAY;IAAEC;EAAc,CAAC,GAAGL,KAAK;EAE3DL,gBAAgB,CAACM,KAAK,EAAE,mBAAmB,CAAC;EAE5C,MAAMK,SAAS,GAAGP,OAAO,CAACQ,IAAI,CAACC,GAAG,CAACP,KAAK,CAACQ,MAAM,CAAC,CAACC,MAAoB;EACrE,MAAMC,UAAU,GAAGZ,OAAO,CAACQ,IAAI,CAACC,GAAG,CAACN,MAAM,CAACO,MAAM,CAAC,CAACC,MAAoB;EAEvE,MAAM;IAACE;EAAe,CAAC,GAAGlB,uBAAuB,CAC7CY,SAAS,EAAEK,UAAU,EAAER,aAAa,EAAEC,YAAY,EAAEC,cAAc,CAAC;EAEvE,OAAON,OAAO,CAACc,cAAc,CACzB,CAACD,eAAe,CAACE,MAAM,CAAC,EAAE,OAAO,EAAE,IAAIC,UAAU,CAACH,eAAe,CAAC,CAAC;AACzE;AAEA,OAAO,MAAMI,yBAAyB,GAAiB;EACrDC,UAAU,EAAExB,mBAAmB;EAC/ByB,WAAW,EAAE,KAAK;EAClBC,UAAU,EAAEvB;CACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}