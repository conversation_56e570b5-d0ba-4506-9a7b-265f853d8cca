{"ast": null, "code": "\"use strict\";\n\nfunction noop() {}\n\n/**\n * Reflects a promise but does not expose any\n * underlying value or rejection from that promise.\n * @param  {Promise} promise [description]\n * @return {Promise}         [description]\n */\nexports.reflector = function (promise) {\n  return promise.then(noop, noop);\n};", "map": {"version": 3, "names": ["noop", "exports", "reflector", "promise", "then"], "sources": ["C:/tmsft/node_modules/generic-pool/lib/utils.js"], "sourcesContent": ["\"use strict\";\n\nfunction noop() {}\n\n/**\n * Reflects a promise but does not expose any\n * underlying value or rejection from that promise.\n * @param  {Promise} promise [description]\n * @return {Promise}         [description]\n */\nexports.reflector = function(promise) {\n  return promise.then(noop, noop);\n};\n"], "mappings": "AAAA,YAAY;;AAEZ,SAASA,IAAIA,CAAA,EAAG,CAAC;;AAEjB;AACA;AACA;AACA;AACA;AACA;AACAC,OAAO,CAACC,SAAS,GAAG,UAASC,OAAO,EAAE;EACpC,OAAOA,OAAO,CAACC,IAAI,CAACJ,IAAI,EAAEA,IAAI,CAAC;AACjC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}