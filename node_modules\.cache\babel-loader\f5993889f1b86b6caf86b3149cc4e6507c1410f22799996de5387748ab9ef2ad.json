{"ast": null, "code": "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { Acosh } from '@tensorflow/tfjs-core';\nimport { unaryKernelFunc } from '../kernel_utils/kernel_funcs_utils';\nimport { CHECK_NAN_SNIPPET } from '../unaryop_gpu';\nconst ACOSH = CHECK_NAN_SNIPPET + `\n  if (x < 1.0) return NAN;\nreturn log(x + sqrt(x * x - 1.0));`;\nexport const acosh = unaryKernelFunc({\n  opSnippet: ACOSH\n});\nexport const acoshConfig = {\n  kernelName: Acosh,\n  backendName: 'webgl',\n  kernelFunc: acosh\n};", "map": {"version": 3, "names": ["Acosh", "unaryKernelFunc", "CHECK_NAN_SNIPPET", "ACOSH", "acosh", "opSnippet", "acoshConfig", "kernelName", "backendName", "kernelFunc"], "sources": ["C:\\tfjs-backend-webgl\\src\\kernels\\Acosh.ts"], "sourcesContent": ["\n/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {Acosh, KernelConfig} from '@tensorflow/tfjs-core';\n\nimport {unaryKernelFunc} from '../kernel_utils/kernel_funcs_utils';\nimport {CHECK_NAN_SNIPPET} from '../unaryop_gpu';\n\nconst ACOSH = CHECK_NAN_SNIPPET + `\n  if (x < 1.0) return NAN;\nreturn log(x + sqrt(x * x - 1.0));`;\n\nexport const acosh = unaryKernelFunc({opSnippet: ACOSH});\n\nexport const acoshConfig: KernelConfig = {\n  kernelName: Acosh,\n  backendName: 'webgl',\n  kernelFunc: acosh,\n};\n"], "mappings": "AACA;;;;;;;;;;;;;;;;AAiBA,SAAQA,KAAK,QAAqB,uBAAuB;AAEzD,SAAQC,eAAe,QAAO,oCAAoC;AAClE,SAAQC,iBAAiB,QAAO,gBAAgB;AAEhD,MAAMC,KAAK,GAAGD,iBAAiB,GAAG;;mCAEC;AAEnC,OAAO,MAAME,KAAK,GAAGH,eAAe,CAAC;EAACI,SAAS,EAAEF;AAAK,CAAC,CAAC;AAExD,OAAO,MAAMG,WAAW,GAAiB;EACvCC,UAAU,EAAEP,KAAK;EACjBQ,WAAW,EAAE,OAAO;EACpBC,UAAU,EAAEL;CACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}