{"ast": null, "code": "/**\n * @license\n * Copyright 2018 Google LLC\n *\n * Use of this source code is governed by an MIT-style\n * license that can be found in the LICENSE file or at\n * https://opensource.org/licenses/MIT.\n * =============================================================================\n */\n/* Original Source: losses.py */\nimport * as tfc from '@tensorflow/tfjs-core';\nimport { tidy, util } from '@tensorflow/tfjs-core';\nimport { epsilon } from './backend/common';\nimport * as K from './backend/tfjs_backend';\nimport { ValueError } from './errors';\n/**\n * Normalizes a tensor wrt the L2 norm alongside the specified axis.\n * @param x\n * @param axis Axis along which to perform normalization.\n */\nexport function l2Normalize(x, axis) {\n  return tidy(() => {\n    if (x.dtype !== 'float32') {\n      x = tfc.cast(x, 'float32');\n    }\n    const squareSum = tfc.sum(K.square(x), axis, true);\n    const epsilonTensor = tfc.fill(squareSum.shape, epsilon());\n    const norm = tfc.sqrt(tfc.maximum(squareSum, epsilonTensor));\n    return tfc.div(x, norm);\n  });\n}\nexport function meanSquaredError(yTrue, yPred) {\n  return tidy(() => tfc.mean(K.square(tfc.sub(yPred, yTrue)), -1));\n}\nexport function meanAbsoluteError(yTrue, yPred) {\n  return tidy(() => tfc.mean(tfc.abs(tfc.sub(yPred, yTrue)), -1));\n}\nexport function meanAbsolutePercentageError(yTrue, yPred) {\n  return tidy(() => {\n    const diff = tfc.sub(yTrue, yPred);\n    const clippedTrue = tfc.clipByValue(tfc.abs(yTrue), epsilon(), Number.MAX_VALUE);\n    const absResult = tfc.abs(tfc.div(diff, clippedTrue));\n    return tfc.mul(100, tfc.mean(absResult, -1));\n  });\n}\nexport function meanSquaredLogarithmicError(yTrue, yPred) {\n  return tidy(() => {\n    const clippedPred = tfc.clipByValue(yPred, epsilon(), Number.MAX_VALUE);\n    const firstLog = tfc.log(tfc.add(1, clippedPred));\n    const clippedTrue = tfc.clipByValue(yTrue, epsilon(), Number.MAX_VALUE);\n    const secondLog = tfc.log(tfc.add(1, clippedTrue));\n    return tfc.mean(K.square(tfc.sub(firstLog, secondLog)), -1);\n  });\n}\nexport function squaredHinge(yTrue, yPred) {\n  return tidy(() => {\n    const maxResult = tfc.maximum(0, tfc.sub(1, tfc.mul(yTrue, yPred)));\n    return tfc.mean(K.square(maxResult), -1);\n  });\n}\nexport function hinge(yTrue, yPred) {\n  return tidy(() => {\n    const maxResult = tfc.maximum(0, tfc.sub(1, tfc.mul(yTrue, yPred)));\n    return tfc.mean(maxResult, -1);\n  });\n}\nexport function categoricalHinge(yTrue, yPred) {\n  return tidy(() => {\n    const pos = tfc.sum(tfc.mul(yTrue, yPred), -1);\n    const neg = tfc.max(tfc.mul(tfc.sub(1, yTrue), yPred), -1);\n    return tfc.maximum(0, tfc.add(1, tfc.sub(neg, pos)));\n  });\n}\n/**\n * Logarithm of the hyperbolic cosine of the prediction error.\n *\n * `log(cosh(x))` is approximately equal to `(x ** 2) / 2` for small `x` and\n * to `abs(x) - log(2)` for large `x`. This means that 'logcosh' works mostly\n * like the mean squared error, but will not be so strongly affected by the\n * occasional wildly incorrect prediction.\n */\nexport function logcosh(yTrue, yPred) {\n  return tidy(() => {\n    const log2 = Math.log(2);\n    const predictionDiff = tfc.sub(yPred, yTrue);\n    const logcoshResult = tfc.sub(tfc.add(predictionDiff, tfc.softplus(tfc.mul(-2, predictionDiff))), log2);\n    return tfc.mean(logcoshResult, -1);\n  });\n}\nexport function categoricalCrossentropy(target, output, fromLogits = false) {\n  return tidy(() => {\n    if (fromLogits) {\n      output = tfc.softmax(output);\n    } else {\n      // scale preds so that the class probabilities of each sample sum to 1.\n      const outputSum = tfc.sum(output, output.shape.length - 1, true);\n      output = tfc.div(output, outputSum);\n    }\n    output = tfc.clipByValue(output, epsilon(), 1 - epsilon());\n    return tfc.neg(tfc.sum(tfc.mul(tfc.cast(target, 'float32'), tfc.log(output)), output.shape.length - 1));\n  });\n}\n/**\n * Categorical crossentropy with integer targets.\n *\n * @param target An integer tensor.\n * @param output A tensor resulting from a softmax (unless `fromLogits` is\n *  `true`, in which case `output` is expected to be the logits).\n * @param fromLogits Boolean, whether `output` is the result of a softmax, or is\n *   a tensor of logits.\n */\nexport function sparseCategoricalCrossentropy(target, output, fromLogits = false) {\n  return tidy(() => {\n    const flatTarget = tfc.cast(tfc.floor(K.flatten(target)), 'int32');\n    output = tfc.clipByValue(output, epsilon(), 1 - epsilon());\n    const outputShape = output.shape;\n    const oneHotTarget = tfc.reshape(tfc.oneHot(flatTarget, outputShape[outputShape.length - 1]), outputShape);\n    return categoricalCrossentropy(oneHotTarget, output, fromLogits);\n  });\n}\n/**\n * From TensorFlow's implementation in nn_impl.py:\n *\n * For brevity, let `x = logits`, `z = labels`.  The logistic loss is\n *      z * -log(sigmoid(x)) + (1 - z) * -log(1 - sigmoid(x))\n *    = z * -log(1 / (1 + exp(-x))) + (1 - z) * -log(exp(-x) / (1 + exp(-x)))\n *    = z * log(1 + exp(-x)) + (1 - z) * (-log(exp(-x)) + log(1 + exp(-x)))\n *    = z * log(1 + exp(-x)) + (1 - z) * (x + log(1 + exp(-x))\n *    = (1 - z) * x + log(1 + exp(-x))\n *    = x - x * z + log(1 + exp(-x))\n * For x < 0, to avoid overflow in exp(-x), we reformulate the above\n *      x - x * z + log(1 + exp(-x))\n *    = log(exp(x)) - x * z + log(1 + exp(-x))\n *    = - x * z + log(1 + exp(x))\n * Hence, to ensure stability and avoid overflow, the implementation uses this\n * equivalent formulation\n *    max(x, 0) - x * z + log(1 + exp(-abs(x)))\n *\n * @param labels The labels.\n * @param logits The logits.\n */\nexport function sigmoidCrossEntropyWithLogits(labels, logits) {\n  if (!util.arraysEqual(labels.shape, logits.shape)) {\n    throw new ValueError(`logits and labels must have the same shape, but got shapes ` + `${JSON.stringify(labels.shape)} and ${JSON.stringify(logits.shape)}`);\n  }\n  return tidy(() => {\n    // The logistic loss formula from above is\n    //   x - x * z + log(1 + exp(-x))\n    // For x < 0, a more numerically stable formula is\n    //   -x * z + log(1 + exp(x))\n    // Note that these two expressions can be combined into the following:\n    //   max(x, 0) - x * z + log(1 + exp(-abs(x)))\n    const reluLogits = tfc.relu(logits);\n    const negAbsLogits = tfc.neg(tfc.abs(logits));\n    return tfc.add(tfc.sub(reluLogits, tfc.mul(logits, labels)), tfc.log1p(tfc.exp(negAbsLogits)));\n  });\n}\nexport function binaryCrossentropy(yTrue, yPred) {\n  return tidy(() => {\n    let y;\n    y = tfc.clipByValue(yPred, epsilon(), 1 - epsilon());\n    y = tfc.log(tfc.div(y, tfc.sub(1, y)));\n    return tfc.mean(sigmoidCrossEntropyWithLogits(yTrue, y), -1);\n  });\n}\nexport function kullbackLeiblerDivergence(yTrue, yPred) {\n  return tidy(() => {\n    const clippedTrue = tfc.clipByValue(yTrue, epsilon(), 1);\n    const clippedPred = tfc.clipByValue(yPred, epsilon(), 1);\n    return tfc.sum(tfc.mul(yTrue, tfc.log(tfc.div(clippedTrue, clippedPred))), -1);\n  });\n}\nexport function poisson(yTrue, yPred) {\n  return tidy(() => {\n    const logPred = tfc.log(tfc.add(epsilon(), yPred));\n    return tfc.mean(tfc.sub(yPred, tfc.mul(yTrue, logPred)), -1);\n  });\n}\nexport function cosineProximity(yTrue, yPred) {\n  return tidy(() => {\n    const trueNormalized = l2Normalize(yTrue, -1);\n    const predNormalized = l2Normalize(yPred, -1);\n    const trueXPred = tfc.mul(trueNormalized, predNormalized);\n    return tfc.neg(tfc.sum(trueXPred, -1));\n  });\n}\nexport const mse = meanSquaredError;\nexport const MSE = meanSquaredError;\nexport const mae = meanAbsoluteError;\nexport const MAE = meanAbsoluteError;\nexport const mape = meanAbsolutePercentageError;\nexport const MAPE = meanAbsolutePercentageError;\nexport const msle = meanSquaredLogarithmicError;\nexport const MSLE = meanSquaredLogarithmicError;\nexport const kld = kullbackLeiblerDivergence;\nexport const KLD = kullbackLeiblerDivergence;\nexport const cosine = cosineProximity;\n// TODO(michaelterry): Add deserialize() function.\nexport const lossesMap = {\n  meanSquaredError,\n  meanAbsoluteError,\n  meanAbsolutePercentageError,\n  meanSquaredLogarithmicError,\n  squaredHinge,\n  hinge,\n  categoricalHinge,\n  logcosh,\n  categoricalCrossentropy,\n  sparseCategoricalCrossentropy,\n  binaryCrossentropy,\n  kullbackLeiblerDivergence,\n  poisson,\n  cosineProximity\n};\n// Porting note: This diverges from the PyKeras implementation and may need to\n// change based on (de)serialization requirements.\nexport function get(identifierOrFn) {\n  if (typeof identifierOrFn === 'string') {\n    if (identifierOrFn in lossesMap) {\n      return lossesMap[identifierOrFn];\n    }\n    let errMsg = `Unknown loss ${identifierOrFn}`;\n    if (identifierOrFn.toLowerCase().includes('softmaxcrossentropy')) {\n      errMsg = `Unknown loss ${identifierOrFn}. ` + 'Use \"categoricalCrossentropy\" as the string name for ' + 'tf.losses.softmaxCrossEntropy';\n    }\n    throw new ValueError(errMsg);\n  } else {\n    return identifierOrFn;\n  }\n}", "map": {"version": 3, "names": ["tfc", "tidy", "util", "epsilon", "K", "ValueError", "l2Normalize", "x", "axis", "dtype", "cast", "squareSum", "sum", "square", "epsilonTensor", "fill", "shape", "norm", "sqrt", "maximum", "div", "meanSquaredError", "yTrue", "yPred", "mean", "sub", "meanAbsoluteError", "abs", "meanAbsolutePercentageError", "diff", "clipped<PERSON>rue", "clipByValue", "Number", "MAX_VALUE", "absResult", "mul", "meanSquaredLogarithmicError", "<PERSON><PERSON><PERSON>", "firstLog", "log", "add", "secondLog", "squaredHinge", "maxResult", "hinge", "categoricalHinge", "pos", "neg", "max", "logcosh", "log2", "Math", "predictionDiff", "logcoshResult", "softplus", "categoricalCrossentropy", "target", "output", "fromLogits", "softmax", "outputSum", "length", "sparseCategoricalCrossentropy", "flatTarget", "floor", "flatten", "outputShape", "oneHotTarget", "reshape", "oneHot", "sigmoidCrossEntropyWithLogits", "labels", "logits", "arraysEqual", "JSON", "stringify", "reluLogits", "relu", "negAbsLogits", "log1p", "exp", "binaryCrossentropy", "y", "kullbackLeiblerDivergence", "poisson", "logPred", "cosineProximity", "trueNormalized", "predNormalized", "trueXPred", "mse", "MSE", "mae", "MAE", "mape", "MAPE", "msle", "MSLE", "kld", "KLD", "cosine", "lossesMap", "get", "identifierOrFn", "errMsg", "toLowerCase", "includes"], "sources": ["C:\\tfjs-layers\\src\\losses.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2018 Google LLC\n *\n * Use of this source code is governed by an MIT-style\n * license that can be found in the LICENSE file or at\n * https://opensource.org/licenses/MIT.\n * =============================================================================\n */\n\n/* Original Source: losses.py */\nimport * as tfc from '@tensorflow/tfjs-core';\nimport {Tensor, Tensor1D, tidy, util} from '@tensorflow/tfjs-core';\n\nimport {epsilon} from './backend/common';\nimport * as K from './backend/tfjs_backend';\nimport {ValueError} from './errors';\nimport {LossOrMetricFn} from './types';\n\n/**\n * Normalizes a tensor wrt the L2 norm alongside the specified axis.\n * @param x\n * @param axis Axis along which to perform normalization.\n */\nexport function l2Normalize(x: Tensor, axis?: number): Tensor {\n  return tidy(() => {\n    if (x.dtype !== 'float32') {\n      x = tfc.cast(x, 'float32');\n    }\n    const squareSum = tfc.sum(K.square(x), axis, true);\n    const epsilonTensor = tfc.fill(squareSum.shape, epsilon());\n    const norm = tfc.sqrt(tfc.maximum(squareSum, epsilonTensor));\n    return tfc.div(x, norm);\n  });\n}\n\nexport function meanSquaredError(yTrue: Tensor, yPred: Tensor): Tensor {\n  return tidy(() => tfc.mean(K.square(tfc.sub(yPred, yTrue)), -1));\n}\n\nexport function meanAbsoluteError(yTrue: Tensor, yPred: Tensor): Tensor {\n  return tidy(() => tfc.mean(tfc.abs(tfc.sub(yPred, yTrue)), -1));\n}\n\nexport function meanAbsolutePercentageError(\n    yTrue: Tensor, yPred: Tensor): Tensor {\n  return tidy(() => {\n    const diff = tfc.sub(yTrue, yPred);\n    const clippedTrue =\n        tfc.clipByValue(tfc.abs(yTrue), epsilon(), Number.MAX_VALUE);\n    const absResult = tfc.abs(tfc.div(diff, clippedTrue));\n    return tfc.mul(100, tfc.mean(absResult, -1));\n  });\n}\n\nexport function meanSquaredLogarithmicError(\n    yTrue: Tensor, yPred: Tensor): Tensor {\n  return tidy(() => {\n    const clippedPred = tfc.clipByValue(yPred, epsilon(), Number.MAX_VALUE);\n    const firstLog = tfc.log(tfc.add(1, clippedPred));\n\n    const clippedTrue = tfc.clipByValue(yTrue, epsilon(), Number.MAX_VALUE);\n    const secondLog = tfc.log(tfc.add(1, clippedTrue));\n\n    return tfc.mean(K.square(tfc.sub(firstLog, secondLog)), -1);\n  });\n}\n\nexport function squaredHinge(yTrue: Tensor, yPred: Tensor): Tensor {\n  return tidy(() => {\n    const maxResult = tfc.maximum(0, tfc.sub(1, tfc.mul(yTrue, yPred)));\n    return tfc.mean(K.square(maxResult), -1);\n  });\n}\n\nexport function hinge(yTrue: Tensor, yPred: Tensor): Tensor {\n  return tidy(() => {\n    const maxResult = tfc.maximum(0, tfc.sub(1, tfc.mul(yTrue, yPred)));\n    return tfc.mean(maxResult, -1);\n  });\n}\n\nexport function categoricalHinge(yTrue: Tensor, yPred: Tensor): Tensor {\n  return tidy(() => {\n    const pos = tfc.sum(tfc.mul(yTrue, yPred), -1);\n    const neg = tfc.max(tfc.mul(tfc.sub(1, yTrue), yPred), -1);\n    return tfc.maximum(0, tfc.add(1, tfc.sub(neg, pos)));\n  });\n}\n\n/**\n * Logarithm of the hyperbolic cosine of the prediction error.\n *\n * `log(cosh(x))` is approximately equal to `(x ** 2) / 2` for small `x` and\n * to `abs(x) - log(2)` for large `x`. This means that 'logcosh' works mostly\n * like the mean squared error, but will not be so strongly affected by the\n * occasional wildly incorrect prediction.\n */\nexport function logcosh(yTrue: Tensor, yPred: Tensor): Tensor {\n  return tidy(() => {\n    const log2 = Math.log(2);\n    const predictionDiff = tfc.sub(yPred, yTrue);\n    const logcoshResult = tfc.sub(\n        tfc.add(predictionDiff, tfc.softplus(tfc.mul(-2, predictionDiff))),\n        log2);\n    return tfc.mean(logcoshResult, -1);\n  });\n}\n\nexport function categoricalCrossentropy(\n    target: Tensor, output: Tensor, fromLogits = false): Tensor {\n  return tidy(() => {\n    if (fromLogits) {\n      output = tfc.softmax(output);\n    } else {\n      // scale preds so that the class probabilities of each sample sum to 1.\n      const outputSum = tfc.sum(output, output.shape.length - 1, true);\n      output = tfc.div(output, outputSum);\n    }\n    output = tfc.clipByValue(output, epsilon(), 1 - epsilon());\n    return tfc.neg(tfc.sum(\n        tfc.mul(tfc.cast(target, 'float32'), tfc.log(output)),\n        output.shape.length - 1));\n  });\n}\n\n/**\n * Categorical crossentropy with integer targets.\n *\n * @param target An integer tensor.\n * @param output A tensor resulting from a softmax (unless `fromLogits` is\n *  `true`, in which case `output` is expected to be the logits).\n * @param fromLogits Boolean, whether `output` is the result of a softmax, or is\n *   a tensor of logits.\n */\nexport function sparseCategoricalCrossentropy(\n    target: Tensor, output: Tensor, fromLogits = false): Tensor {\n  return tidy(() => {\n    const flatTarget =\n        tfc.cast(tfc.floor(K.flatten(target)), 'int32') as Tensor1D;\n    output = tfc.clipByValue(output, epsilon(), 1 - epsilon());\n    const outputShape = output.shape;\n    const oneHotTarget = tfc.reshape(\n        tfc.oneHot(flatTarget, outputShape[outputShape.length - 1]),\n        outputShape);\n    return categoricalCrossentropy(oneHotTarget, output, fromLogits);\n  });\n}\n\n/**\n * From TensorFlow's implementation in nn_impl.py:\n *\n * For brevity, let `x = logits`, `z = labels`.  The logistic loss is\n *      z * -log(sigmoid(x)) + (1 - z) * -log(1 - sigmoid(x))\n *    = z * -log(1 / (1 + exp(-x))) + (1 - z) * -log(exp(-x) / (1 + exp(-x)))\n *    = z * log(1 + exp(-x)) + (1 - z) * (-log(exp(-x)) + log(1 + exp(-x)))\n *    = z * log(1 + exp(-x)) + (1 - z) * (x + log(1 + exp(-x))\n *    = (1 - z) * x + log(1 + exp(-x))\n *    = x - x * z + log(1 + exp(-x))\n * For x < 0, to avoid overflow in exp(-x), we reformulate the above\n *      x - x * z + log(1 + exp(-x))\n *    = log(exp(x)) - x * z + log(1 + exp(-x))\n *    = - x * z + log(1 + exp(x))\n * Hence, to ensure stability and avoid overflow, the implementation uses this\n * equivalent formulation\n *    max(x, 0) - x * z + log(1 + exp(-abs(x)))\n *\n * @param labels The labels.\n * @param logits The logits.\n */\nexport function sigmoidCrossEntropyWithLogits(\n    labels: Tensor, logits: Tensor): Tensor {\n  if (!util.arraysEqual(labels.shape, logits.shape)) {\n    throw new ValueError(\n        `logits and labels must have the same shape, but got shapes ` +\n        `${JSON.stringify(labels.shape)} and ${JSON.stringify(logits.shape)}`);\n  }\n  return tidy(() => {\n    // The logistic loss formula from above is\n    //   x - x * z + log(1 + exp(-x))\n    // For x < 0, a more numerically stable formula is\n    //   -x * z + log(1 + exp(x))\n    // Note that these two expressions can be combined into the following:\n    //   max(x, 0) - x * z + log(1 + exp(-abs(x)))\n    const reluLogits = tfc.relu(logits);\n    const negAbsLogits = tfc.neg(tfc.abs(logits));\n    return tfc.add(\n        tfc.sub(reluLogits, tfc.mul(logits, labels)),\n        tfc.log1p(tfc.exp(negAbsLogits)));\n  });\n}\n\nexport function binaryCrossentropy(yTrue: Tensor, yPred: Tensor): Tensor {\n  return tidy(() => {\n    let y: Tensor;\n    y = tfc.clipByValue(yPred, epsilon(), 1 - epsilon());\n    y = tfc.log(tfc.div(y, tfc.sub(1, y)));\n    return tfc.mean(sigmoidCrossEntropyWithLogits(yTrue, y), -1);\n  });\n}\n\nexport function kullbackLeiblerDivergence(\n    yTrue: Tensor, yPred: Tensor): Tensor {\n  return tidy(() => {\n    const clippedTrue = tfc.clipByValue(yTrue, epsilon(), 1);\n    const clippedPred = tfc.clipByValue(yPred, epsilon(), 1);\n    return tfc.sum(\n        tfc.mul(yTrue, tfc.log(tfc.div(clippedTrue, clippedPred))), -1);\n  });\n}\n\nexport function poisson(yTrue: Tensor, yPred: Tensor): Tensor {\n  return tidy(() => {\n    const logPred = tfc.log(tfc.add(epsilon(), yPred));\n    return tfc.mean(tfc.sub(yPred, tfc.mul(yTrue, logPred)), -1);\n  });\n}\n\nexport function cosineProximity(yTrue: Tensor, yPred: Tensor): Tensor {\n  return tidy(() => {\n    const trueNormalized = l2Normalize(yTrue, -1);\n    const predNormalized = l2Normalize(yPred, -1);\n    const trueXPred = tfc.mul(trueNormalized, predNormalized);\n    return tfc.neg(tfc.sum(trueXPred, -1));\n  });\n}\n\nexport const mse = meanSquaredError;\nexport const MSE = meanSquaredError;\nexport const mae = meanAbsoluteError;\nexport const MAE = meanAbsoluteError;\nexport const mape = meanAbsolutePercentageError;\nexport const MAPE = meanAbsolutePercentageError;\nexport const msle = meanSquaredLogarithmicError;\nexport const MSLE = meanSquaredLogarithmicError;\nexport const kld = kullbackLeiblerDivergence;\nexport const KLD = kullbackLeiblerDivergence;\nexport const cosine = cosineProximity;\n\n// TODO(michaelterry): Add deserialize() function.\n\nexport const lossesMap: {[functionName: string]: LossOrMetricFn} = {\n  meanSquaredError,\n  meanAbsoluteError,\n  meanAbsolutePercentageError,\n  meanSquaredLogarithmicError,\n  squaredHinge,\n  hinge,\n  categoricalHinge,\n  logcosh,\n  categoricalCrossentropy,\n  sparseCategoricalCrossentropy,\n  binaryCrossentropy,\n  kullbackLeiblerDivergence,\n  poisson,\n  cosineProximity\n};\n\n// Porting note: This diverges from the PyKeras implementation and may need to\n// change based on (de)serialization requirements.\nexport function get(identifierOrFn: string|LossOrMetricFn): LossOrMetricFn {\n  if (typeof identifierOrFn === 'string') {\n    if (identifierOrFn in lossesMap) {\n      return lossesMap[identifierOrFn];\n    }\n    let errMsg = `Unknown loss ${identifierOrFn}`;\n    if (identifierOrFn.toLowerCase().includes('softmaxcrossentropy')) {\n      errMsg = `Unknown loss ${identifierOrFn}. ` +\n          'Use \"categoricalCrossentropy\" as the string name for ' +\n          'tf.losses.softmaxCrossEntropy';\n    }\n    throw new ValueError(errMsg);\n  } else {\n    return identifierOrFn;\n  }\n}\n"], "mappings": "AAAA;;;;;;;;;AAUA;AACA,OAAO,KAAKA,GAAG,MAAM,uBAAuB;AAC5C,SAA0BC,IAAI,EAAEC,IAAI,QAAO,uBAAuB;AAElE,SAAQC,OAAO,QAAO,kBAAkB;AACxC,OAAO,KAAKC,CAAC,MAAM,wBAAwB;AAC3C,SAAQC,UAAU,QAAO,UAAU;AAGnC;;;;;AAKA,OAAM,SAAUC,WAAWA,CAACC,CAAS,EAAEC,IAAa;EAClD,OAAOP,IAAI,CAAC,MAAK;IACf,IAAIM,CAAC,CAACE,KAAK,KAAK,SAAS,EAAE;MACzBF,CAAC,GAAGP,GAAG,CAACU,IAAI,CAACH,CAAC,EAAE,SAAS,CAAC;;IAE5B,MAAMI,SAAS,GAAGX,GAAG,CAACY,GAAG,CAACR,CAAC,CAACS,MAAM,CAACN,CAAC,CAAC,EAAEC,IAAI,EAAE,IAAI,CAAC;IAClD,MAAMM,aAAa,GAAGd,GAAG,CAACe,IAAI,CAACJ,SAAS,CAACK,KAAK,EAAEb,OAAO,EAAE,CAAC;IAC1D,MAAMc,IAAI,GAAGjB,GAAG,CAACkB,IAAI,CAAClB,GAAG,CAACmB,OAAO,CAACR,SAAS,EAAEG,aAAa,CAAC,CAAC;IAC5D,OAAOd,GAAG,CAACoB,GAAG,CAACb,CAAC,EAAEU,IAAI,CAAC;EACzB,CAAC,CAAC;AACJ;AAEA,OAAM,SAAUI,gBAAgBA,CAACC,KAAa,EAAEC,KAAa;EAC3D,OAAOtB,IAAI,CAAC,MAAMD,GAAG,CAACwB,IAAI,CAACpB,CAAC,CAACS,MAAM,CAACb,GAAG,CAACyB,GAAG,CAACF,KAAK,EAAED,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AAClE;AAEA,OAAM,SAAUI,iBAAiBA,CAACJ,KAAa,EAAEC,KAAa;EAC5D,OAAOtB,IAAI,CAAC,MAAMD,GAAG,CAACwB,IAAI,CAACxB,GAAG,CAAC2B,GAAG,CAAC3B,GAAG,CAACyB,GAAG,CAACF,KAAK,EAAED,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AACjE;AAEA,OAAM,SAAUM,2BAA2BA,CACvCN,KAAa,EAAEC,KAAa;EAC9B,OAAOtB,IAAI,CAAC,MAAK;IACf,MAAM4B,IAAI,GAAG7B,GAAG,CAACyB,GAAG,CAACH,KAAK,EAAEC,KAAK,CAAC;IAClC,MAAMO,WAAW,GACb9B,GAAG,CAAC+B,WAAW,CAAC/B,GAAG,CAAC2B,GAAG,CAACL,KAAK,CAAC,EAAEnB,OAAO,EAAE,EAAE6B,MAAM,CAACC,SAAS,CAAC;IAChE,MAAMC,SAAS,GAAGlC,GAAG,CAAC2B,GAAG,CAAC3B,GAAG,CAACoB,GAAG,CAACS,IAAI,EAAEC,WAAW,CAAC,CAAC;IACrD,OAAO9B,GAAG,CAACmC,GAAG,CAAC,GAAG,EAAEnC,GAAG,CAACwB,IAAI,CAACU,SAAS,EAAE,CAAC,CAAC,CAAC,CAAC;EAC9C,CAAC,CAAC;AACJ;AAEA,OAAM,SAAUE,2BAA2BA,CACvCd,KAAa,EAAEC,KAAa;EAC9B,OAAOtB,IAAI,CAAC,MAAK;IACf,MAAMoC,WAAW,GAAGrC,GAAG,CAAC+B,WAAW,CAACR,KAAK,EAAEpB,OAAO,EAAE,EAAE6B,MAAM,CAACC,SAAS,CAAC;IACvE,MAAMK,QAAQ,GAAGtC,GAAG,CAACuC,GAAG,CAACvC,GAAG,CAACwC,GAAG,CAAC,CAAC,EAAEH,WAAW,CAAC,CAAC;IAEjD,MAAMP,WAAW,GAAG9B,GAAG,CAAC+B,WAAW,CAACT,KAAK,EAAEnB,OAAO,EAAE,EAAE6B,MAAM,CAACC,SAAS,CAAC;IACvE,MAAMQ,SAAS,GAAGzC,GAAG,CAACuC,GAAG,CAACvC,GAAG,CAACwC,GAAG,CAAC,CAAC,EAAEV,WAAW,CAAC,CAAC;IAElD,OAAO9B,GAAG,CAACwB,IAAI,CAACpB,CAAC,CAACS,MAAM,CAACb,GAAG,CAACyB,GAAG,CAACa,QAAQ,EAAEG,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EAC7D,CAAC,CAAC;AACJ;AAEA,OAAM,SAAUC,YAAYA,CAACpB,KAAa,EAAEC,KAAa;EACvD,OAAOtB,IAAI,CAAC,MAAK;IACf,MAAM0C,SAAS,GAAG3C,GAAG,CAACmB,OAAO,CAAC,CAAC,EAAEnB,GAAG,CAACyB,GAAG,CAAC,CAAC,EAAEzB,GAAG,CAACmC,GAAG,CAACb,KAAK,EAAEC,KAAK,CAAC,CAAC,CAAC;IACnE,OAAOvB,GAAG,CAACwB,IAAI,CAACpB,CAAC,CAACS,MAAM,CAAC8B,SAAS,CAAC,EAAE,CAAC,CAAC,CAAC;EAC1C,CAAC,CAAC;AACJ;AAEA,OAAM,SAAUC,KAAKA,CAACtB,KAAa,EAAEC,KAAa;EAChD,OAAOtB,IAAI,CAAC,MAAK;IACf,MAAM0C,SAAS,GAAG3C,GAAG,CAACmB,OAAO,CAAC,CAAC,EAAEnB,GAAG,CAACyB,GAAG,CAAC,CAAC,EAAEzB,GAAG,CAACmC,GAAG,CAACb,KAAK,EAAEC,KAAK,CAAC,CAAC,CAAC;IACnE,OAAOvB,GAAG,CAACwB,IAAI,CAACmB,SAAS,EAAE,CAAC,CAAC,CAAC;EAChC,CAAC,CAAC;AACJ;AAEA,OAAM,SAAUE,gBAAgBA,CAACvB,KAAa,EAAEC,KAAa;EAC3D,OAAOtB,IAAI,CAAC,MAAK;IACf,MAAM6C,GAAG,GAAG9C,GAAG,CAACY,GAAG,CAACZ,GAAG,CAACmC,GAAG,CAACb,KAAK,EAAEC,KAAK,CAAC,EAAE,CAAC,CAAC,CAAC;IAC9C,MAAMwB,GAAG,GAAG/C,GAAG,CAACgD,GAAG,CAAChD,GAAG,CAACmC,GAAG,CAACnC,GAAG,CAACyB,GAAG,CAAC,CAAC,EAAEH,KAAK,CAAC,EAAEC,KAAK,CAAC,EAAE,CAAC,CAAC,CAAC;IAC1D,OAAOvB,GAAG,CAACmB,OAAO,CAAC,CAAC,EAAEnB,GAAG,CAACwC,GAAG,CAAC,CAAC,EAAExC,GAAG,CAACyB,GAAG,CAACsB,GAAG,EAAED,GAAG,CAAC,CAAC,CAAC;EACtD,CAAC,CAAC;AACJ;AAEA;;;;;;;;AAQA,OAAM,SAAUG,OAAOA,CAAC3B,KAAa,EAAEC,KAAa;EAClD,OAAOtB,IAAI,CAAC,MAAK;IACf,MAAMiD,IAAI,GAAGC,IAAI,CAACZ,GAAG,CAAC,CAAC,CAAC;IACxB,MAAMa,cAAc,GAAGpD,GAAG,CAACyB,GAAG,CAACF,KAAK,EAAED,KAAK,CAAC;IAC5C,MAAM+B,aAAa,GAAGrD,GAAG,CAACyB,GAAG,CACzBzB,GAAG,CAACwC,GAAG,CAACY,cAAc,EAAEpD,GAAG,CAACsD,QAAQ,CAACtD,GAAG,CAACmC,GAAG,CAAC,CAAC,CAAC,EAAEiB,cAAc,CAAC,CAAC,CAAC,EAClEF,IAAI,CAAC;IACT,OAAOlD,GAAG,CAACwB,IAAI,CAAC6B,aAAa,EAAE,CAAC,CAAC,CAAC;EACpC,CAAC,CAAC;AACJ;AAEA,OAAM,SAAUE,uBAAuBA,CACnCC,MAAc,EAAEC,MAAc,EAAEC,UAAU,GAAG,KAAK;EACpD,OAAOzD,IAAI,CAAC,MAAK;IACf,IAAIyD,UAAU,EAAE;MACdD,MAAM,GAAGzD,GAAG,CAAC2D,OAAO,CAACF,MAAM,CAAC;KAC7B,MAAM;MACL;MACA,MAAMG,SAAS,GAAG5D,GAAG,CAACY,GAAG,CAAC6C,MAAM,EAAEA,MAAM,CAACzC,KAAK,CAAC6C,MAAM,GAAG,CAAC,EAAE,IAAI,CAAC;MAChEJ,MAAM,GAAGzD,GAAG,CAACoB,GAAG,CAACqC,MAAM,EAAEG,SAAS,CAAC;;IAErCH,MAAM,GAAGzD,GAAG,CAAC+B,WAAW,CAAC0B,MAAM,EAAEtD,OAAO,EAAE,EAAE,CAAC,GAAGA,OAAO,EAAE,CAAC;IAC1D,OAAOH,GAAG,CAAC+C,GAAG,CAAC/C,GAAG,CAACY,GAAG,CAClBZ,GAAG,CAACmC,GAAG,CAACnC,GAAG,CAACU,IAAI,CAAC8C,MAAM,EAAE,SAAS,CAAC,EAAExD,GAAG,CAACuC,GAAG,CAACkB,MAAM,CAAC,CAAC,EACrDA,MAAM,CAACzC,KAAK,CAAC6C,MAAM,GAAG,CAAC,CAAC,CAAC;EAC/B,CAAC,CAAC;AACJ;AAEA;;;;;;;;;AASA,OAAM,SAAUC,6BAA6BA,CACzCN,MAAc,EAAEC,MAAc,EAAEC,UAAU,GAAG,KAAK;EACpD,OAAOzD,IAAI,CAAC,MAAK;IACf,MAAM8D,UAAU,GACZ/D,GAAG,CAACU,IAAI,CAACV,GAAG,CAACgE,KAAK,CAAC5D,CAAC,CAAC6D,OAAO,CAACT,MAAM,CAAC,CAAC,EAAE,OAAO,CAAa;IAC/DC,MAAM,GAAGzD,GAAG,CAAC+B,WAAW,CAAC0B,MAAM,EAAEtD,OAAO,EAAE,EAAE,CAAC,GAAGA,OAAO,EAAE,CAAC;IAC1D,MAAM+D,WAAW,GAAGT,MAAM,CAACzC,KAAK;IAChC,MAAMmD,YAAY,GAAGnE,GAAG,CAACoE,OAAO,CAC5BpE,GAAG,CAACqE,MAAM,CAACN,UAAU,EAAEG,WAAW,CAACA,WAAW,CAACL,MAAM,GAAG,CAAC,CAAC,CAAC,EAC3DK,WAAW,CAAC;IAChB,OAAOX,uBAAuB,CAACY,YAAY,EAAEV,MAAM,EAAEC,UAAU,CAAC;EAClE,CAAC,CAAC;AACJ;AAEA;;;;;;;;;;;;;;;;;;;;;AAqBA,OAAM,SAAUY,6BAA6BA,CACzCC,MAAc,EAAEC,MAAc;EAChC,IAAI,CAACtE,IAAI,CAACuE,WAAW,CAACF,MAAM,CAACvD,KAAK,EAAEwD,MAAM,CAACxD,KAAK,CAAC,EAAE;IACjD,MAAM,IAAIX,UAAU,CAChB,6DAA6D,GAC7D,GAAGqE,IAAI,CAACC,SAAS,CAACJ,MAAM,CAACvD,KAAK,CAAC,QAAQ0D,IAAI,CAACC,SAAS,CAACH,MAAM,CAACxD,KAAK,CAAC,EAAE,CAAC;;EAE5E,OAAOf,IAAI,CAAC,MAAK;IACf;IACA;IACA;IACA;IACA;IACA;IACA,MAAM2E,UAAU,GAAG5E,GAAG,CAAC6E,IAAI,CAACL,MAAM,CAAC;IACnC,MAAMM,YAAY,GAAG9E,GAAG,CAAC+C,GAAG,CAAC/C,GAAG,CAAC2B,GAAG,CAAC6C,MAAM,CAAC,CAAC;IAC7C,OAAOxE,GAAG,CAACwC,GAAG,CACVxC,GAAG,CAACyB,GAAG,CAACmD,UAAU,EAAE5E,GAAG,CAACmC,GAAG,CAACqC,MAAM,EAAED,MAAM,CAAC,CAAC,EAC5CvE,GAAG,CAAC+E,KAAK,CAAC/E,GAAG,CAACgF,GAAG,CAACF,YAAY,CAAC,CAAC,CAAC;EACvC,CAAC,CAAC;AACJ;AAEA,OAAM,SAAUG,kBAAkBA,CAAC3D,KAAa,EAAEC,KAAa;EAC7D,OAAOtB,IAAI,CAAC,MAAK;IACf,IAAIiF,CAAS;IACbA,CAAC,GAAGlF,GAAG,CAAC+B,WAAW,CAACR,KAAK,EAAEpB,OAAO,EAAE,EAAE,CAAC,GAAGA,OAAO,EAAE,CAAC;IACpD+E,CAAC,GAAGlF,GAAG,CAACuC,GAAG,CAACvC,GAAG,CAACoB,GAAG,CAAC8D,CAAC,EAAElF,GAAG,CAACyB,GAAG,CAAC,CAAC,EAAEyD,CAAC,CAAC,CAAC,CAAC;IACtC,OAAOlF,GAAG,CAACwB,IAAI,CAAC8C,6BAA6B,CAAChD,KAAK,EAAE4D,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EAC9D,CAAC,CAAC;AACJ;AAEA,OAAM,SAAUC,yBAAyBA,CACrC7D,KAAa,EAAEC,KAAa;EAC9B,OAAOtB,IAAI,CAAC,MAAK;IACf,MAAM6B,WAAW,GAAG9B,GAAG,CAAC+B,WAAW,CAACT,KAAK,EAAEnB,OAAO,EAAE,EAAE,CAAC,CAAC;IACxD,MAAMkC,WAAW,GAAGrC,GAAG,CAAC+B,WAAW,CAACR,KAAK,EAAEpB,OAAO,EAAE,EAAE,CAAC,CAAC;IACxD,OAAOH,GAAG,CAACY,GAAG,CACVZ,GAAG,CAACmC,GAAG,CAACb,KAAK,EAAEtB,GAAG,CAACuC,GAAG,CAACvC,GAAG,CAACoB,GAAG,CAACU,WAAW,EAAEO,WAAW,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EACrE,CAAC,CAAC;AACJ;AAEA,OAAM,SAAU+C,OAAOA,CAAC9D,KAAa,EAAEC,KAAa;EAClD,OAAOtB,IAAI,CAAC,MAAK;IACf,MAAMoF,OAAO,GAAGrF,GAAG,CAACuC,GAAG,CAACvC,GAAG,CAACwC,GAAG,CAACrC,OAAO,EAAE,EAAEoB,KAAK,CAAC,CAAC;IAClD,OAAOvB,GAAG,CAACwB,IAAI,CAACxB,GAAG,CAACyB,GAAG,CAACF,KAAK,EAAEvB,GAAG,CAACmC,GAAG,CAACb,KAAK,EAAE+D,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EAC9D,CAAC,CAAC;AACJ;AAEA,OAAM,SAAUC,eAAeA,CAAChE,KAAa,EAAEC,KAAa;EAC1D,OAAOtB,IAAI,CAAC,MAAK;IACf,MAAMsF,cAAc,GAAGjF,WAAW,CAACgB,KAAK,EAAE,CAAC,CAAC,CAAC;IAC7C,MAAMkE,cAAc,GAAGlF,WAAW,CAACiB,KAAK,EAAE,CAAC,CAAC,CAAC;IAC7C,MAAMkE,SAAS,GAAGzF,GAAG,CAACmC,GAAG,CAACoD,cAAc,EAAEC,cAAc,CAAC;IACzD,OAAOxF,GAAG,CAAC+C,GAAG,CAAC/C,GAAG,CAACY,GAAG,CAAC6E,SAAS,EAAE,CAAC,CAAC,CAAC,CAAC;EACxC,CAAC,CAAC;AACJ;AAEA,OAAO,MAAMC,GAAG,GAAGrE,gBAAgB;AACnC,OAAO,MAAMsE,GAAG,GAAGtE,gBAAgB;AACnC,OAAO,MAAMuE,GAAG,GAAGlE,iBAAiB;AACpC,OAAO,MAAMmE,GAAG,GAAGnE,iBAAiB;AACpC,OAAO,MAAMoE,IAAI,GAAGlE,2BAA2B;AAC/C,OAAO,MAAMmE,IAAI,GAAGnE,2BAA2B;AAC/C,OAAO,MAAMoE,IAAI,GAAG5D,2BAA2B;AAC/C,OAAO,MAAM6D,IAAI,GAAG7D,2BAA2B;AAC/C,OAAO,MAAM8D,GAAG,GAAGf,yBAAyB;AAC5C,OAAO,MAAMgB,GAAG,GAAGhB,yBAAyB;AAC5C,OAAO,MAAMiB,MAAM,GAAGd,eAAe;AAErC;AAEA,OAAO,MAAMe,SAAS,GAA6C;EACjEhF,gBAAgB;EAChBK,iBAAiB;EACjBE,2BAA2B;EAC3BQ,2BAA2B;EAC3BM,YAAY;EACZE,KAAK;EACLC,gBAAgB;EAChBI,OAAO;EACPM,uBAAuB;EACvBO,6BAA6B;EAC7BmB,kBAAkB;EAClBE,yBAAyB;EACzBC,OAAO;EACPE;CACD;AAED;AACA;AACA,OAAM,SAAUgB,GAAGA,CAACC,cAAqC;EACvD,IAAI,OAAOA,cAAc,KAAK,QAAQ,EAAE;IACtC,IAAIA,cAAc,IAAIF,SAAS,EAAE;MAC/B,OAAOA,SAAS,CAACE,cAAc,CAAC;;IAElC,IAAIC,MAAM,GAAG,gBAAgBD,cAAc,EAAE;IAC7C,IAAIA,cAAc,CAACE,WAAW,EAAE,CAACC,QAAQ,CAAC,qBAAqB,CAAC,EAAE;MAChEF,MAAM,GAAG,gBAAgBD,cAAc,IAAI,GACvC,uDAAuD,GACvD,+BAA+B;;IAErC,MAAM,IAAIlG,UAAU,CAACmG,MAAM,CAAC;GAC7B,MAAM;IACL,OAAOD,cAAc;;AAEzB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}