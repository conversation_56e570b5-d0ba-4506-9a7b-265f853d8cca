{"ast": null, "code": "/**\n * @license\n * Copyright 2018 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n// tslint:disable-next-line: no-imports-from-dist\nimport * as tfOps from '@tensorflow/tfjs-core/dist/ops/ops_for_converter';\nimport { getPadding, getParamValue } from './utils';\nfunction fusedConvAndDepthWiseParams(node, tensorMap, context) {\n  const [extraOp, activationFunc] = getParamValue('fusedOps', node, tensorMap, context);\n  const isBiasAdd = extraOp === 'biasadd';\n  const noBiasAdd = !isBiasAdd;\n  const isPrelu = activationFunc === 'prelu';\n  const isBatchNorm = extraOp === 'fusedbatchnorm';\n  const numArgs = getParamValue('numArgs', node, tensorMap, context);\n  if (isBiasAdd) {\n    if (isPrelu && numArgs !== 2) {\n      throw new Error('FusedConv2d and DepthwiseConv2d with BiasAdd and Prelu ' + 'must have two extra arguments: bias and alpha.');\n    }\n    if (!isPrelu && isBiasAdd && numArgs !== 1) {\n      throw new Error('FusedConv2d and DepthwiseConv2d with BiasAdd must have ' + 'one extra argument: bias.');\n    }\n  }\n  if (isBatchNorm) {\n    throw new Error('FusedConv2d and DepthwiseConv2d with FusedBatchNorm is not supported');\n  }\n  const stride = getParamValue('strides', node, tensorMap, context);\n  const pad = getPadding(node, tensorMap, context);\n  const dataFormat = getParamValue('dataFormat', node, tensorMap, context).toUpperCase();\n  const dilations = getParamValue('dilations', node, tensorMap, context);\n  let [biasArg, preluArg] = getParamValue('args', node, tensorMap, context);\n  if (noBiasAdd) {\n    preluArg = biasArg;\n    biasArg = undefined;\n  }\n  const leakyreluAlpha = getParamValue('leakyreluAlpha', node, tensorMap, context);\n  return {\n    stride,\n    pad,\n    dataFormat,\n    dilations,\n    biasArg,\n    preluArg,\n    activationFunc,\n    leakyreluAlpha\n  };\n}\nexport const executeOp = (node, tensorMap, context, ops = tfOps) => {\n  switch (node.op) {\n    case 'Conv1D':\n      {\n        const stride = getParamValue('stride', node, tensorMap, context);\n        const pad = getParamValue('pad', node, tensorMap, context);\n        const dataFormat = getParamValue('dataFormat', node, tensorMap, context).toUpperCase();\n        const dilation = getParamValue('dilation', node, tensorMap, context);\n        return [ops.conv1d(getParamValue('x', node, tensorMap, context), getParamValue('filter', node, tensorMap, context), stride, pad, dataFormat, dilation)];\n      }\n    case 'Conv2D':\n      {\n        const stride = getParamValue('strides', node, tensorMap, context);\n        const pad = getPadding(node, tensorMap, context);\n        const dataFormat = getParamValue('dataFormat', node, tensorMap, context).toUpperCase();\n        const dilations = getParamValue('dilations', node, tensorMap, context);\n        return [ops.conv2d(getParamValue('x', node, tensorMap, context), getParamValue('filter', node, tensorMap, context), [stride[1], stride[2]], pad, dataFormat, [dilations[1], dilations[2]])];\n      }\n    case '_FusedConv2D':\n      {\n        const {\n          stride,\n          pad,\n          dataFormat,\n          dilations,\n          biasArg,\n          preluArg,\n          activationFunc,\n          leakyreluAlpha\n        } = fusedConvAndDepthWiseParams(node, tensorMap, context);\n        return [ops.fused.conv2d({\n          x: getParamValue('x', node, tensorMap, context),\n          filter: getParamValue('filter', node, tensorMap, context),\n          strides: [stride[1], stride[2]],\n          pad: pad,\n          dataFormat: dataFormat,\n          dilations: [dilations[1], dilations[2]],\n          bias: biasArg,\n          activation: activationFunc,\n          preluActivationWeights: preluArg,\n          leakyreluAlpha\n        })];\n      }\n    case 'FusedDepthwiseConv2dNative':\n      {\n        const {\n          stride,\n          pad,\n          dataFormat,\n          dilations,\n          biasArg,\n          preluArg,\n          activationFunc,\n          leakyreluAlpha\n        } = fusedConvAndDepthWiseParams(node, tensorMap, context);\n        return [ops.fused.depthwiseConv2d({\n          x: getParamValue('x', node, tensorMap, context),\n          filter: getParamValue('filter', node, tensorMap, context),\n          strides: [stride[1], stride[2]],\n          pad: pad,\n          dataFormat: dataFormat,\n          dilations: [dilations[1], dilations[2]],\n          bias: biasArg,\n          activation: activationFunc,\n          preluActivationWeights: preluArg,\n          leakyreluAlpha\n        })];\n      }\n    case 'Conv2DBackpropInput':\n    case 'Conv2dTranspose':\n      {\n        const shape = getParamValue('outputShape', node, tensorMap, context);\n        const stride = getParamValue('strides', node, tensorMap, context);\n        const pad = getPadding(node, tensorMap, context);\n        return [ops.conv2dTranspose(getParamValue('x', node, tensorMap, context), getParamValue('filter', node, tensorMap, context), shape, [stride[1], stride[2]], pad)];\n      }\n    case 'DepthwiseConv2dNative':\n    case 'DepthwiseConv2d':\n      {\n        const stride = getParamValue('strides', node, tensorMap, context);\n        const pad = getPadding(node, tensorMap, context);\n        const dilations = getParamValue('dilations', node, tensorMap, context);\n        const dataFormat = getParamValue('dataFormat', node, tensorMap, context).toUpperCase();\n        return [ops.depthwiseConv2d(getParamValue('input', node, tensorMap, context), getParamValue('filter', node, tensorMap, context), [stride[1], stride[2]], pad, dataFormat, [dilations[1], dilations[2]])];\n      }\n    case 'Conv3D':\n      {\n        const stride = getParamValue('strides', node, tensorMap, context);\n        const pad = getParamValue('pad', node, tensorMap, context);\n        const dataFormat = getParamValue('dataFormat', node, tensorMap, context).toUpperCase();\n        const dilations = getParamValue('dilations', node, tensorMap, context);\n        return [ops.conv3d(getParamValue('x', node, tensorMap, context), getParamValue('filter', node, tensorMap, context), [stride[1], stride[2], stride[3]], pad, dataFormat, [dilations[1], dilations[2], dilations[3]])];\n      }\n    case 'AvgPool':\n      {\n        const stride = getParamValue('strides', node, tensorMap, context);\n        const pad = getParamValue('pad', node, tensorMap, context);\n        const kernelSize = getParamValue('kernelSize', node, tensorMap, context);\n        return [ops.avgPool(getParamValue('x', node, tensorMap, context), [kernelSize[1], kernelSize[2]], [stride[1], stride[2]], pad)];\n      }\n    case 'MaxPool':\n      {\n        const stride = getParamValue('strides', node, tensorMap, context);\n        const pad = getParamValue('pad', node, tensorMap, context);\n        const kernelSize = getParamValue('kernelSize', node, tensorMap, context);\n        return [ops.maxPool(getParamValue('x', node, tensorMap, context), [kernelSize[1], kernelSize[2]], [stride[1], stride[2]], pad)];\n      }\n    case 'MaxPoolWithArgmax':\n      {\n        const stride = getParamValue('strides', node, tensorMap, context);\n        const pad = getParamValue('pad', node, tensorMap, context);\n        const kernelSize = getParamValue('kernelSize', node, tensorMap, context);\n        const includeBatchInIndex = getParamValue('includeBatchInIndex', node, tensorMap, context);\n        const {\n          result,\n          indexes\n        } = ops.maxPoolWithArgmax(getParamValue('x', node, tensorMap, context), [kernelSize[1], kernelSize[2]], [stride[1], stride[2]], pad, includeBatchInIndex);\n        return [result, indexes];\n      }\n    case 'AvgPool3D':\n      {\n        const stride = getParamValue('strides', node, tensorMap, context);\n        const pad = getParamValue('pad', node, tensorMap, context);\n        const kernelSize = getParamValue('kernelSize', node, tensorMap, context);\n        return [ops.avgPool3d(getParamValue('x', node, tensorMap, context), [kernelSize[1], kernelSize[2], kernelSize[3]], [stride[1], stride[2], stride[3]], pad)];\n      }\n    case 'MaxPool3D':\n      {\n        const stride = getParamValue('strides', node, tensorMap, context);\n        const pad = getParamValue('pad', node, tensorMap, context);\n        const kernelSize = getParamValue('kernelSize', node, tensorMap, context);\n        return [ops.maxPool3d(getParamValue('x', node, tensorMap, context), [kernelSize[1], kernelSize[2], kernelSize[3]], [stride[1], stride[2], stride[3]], pad)];\n      }\n    case 'Dilation2D':\n      {\n        const strides = getParamValue('strides', node, tensorMap, context);\n        const pad = getParamValue('pad', node, tensorMap, context);\n        const dilations = getParamValue('dilations', node, tensorMap, context);\n        // strides: [1, stride_height, stride_width, 1].\n        const strideHeight = strides[1];\n        const strideWidth = strides[2];\n        // dilations: [1, dilation_height, dilation_width, 1].\n        const dilationHeight = dilations[1];\n        const dilationWidth = dilations[2];\n        return [ops.dilation2d(getParamValue('x', node, tensorMap, context), getParamValue('filter', node, tensorMap, context), [strideHeight, strideWidth], pad, [dilationHeight, dilationWidth], 'NHWC' /* dataFormat */)];\n      }\n    default:\n      throw TypeError(`Node type ${node.op} is not implemented`);\n  }\n};\nexport const CATEGORY = 'convolution';", "map": {"version": 3, "names": ["tfOps", "getPadding", "getParamValue", "fusedConvAndDepthWiseParams", "node", "tensorMap", "context", "extraOp", "activationFunc", "isBiasAdd", "noBiasAdd", "isPrelu", "isBatchNorm", "numArgs", "Error", "stride", "pad", "dataFormat", "toUpperCase", "dilations", "biasArg", "preluArg", "undefined", "leakyreluAlpha", "executeOp", "ops", "op", "dilation", "conv1d", "conv2d", "fused", "x", "filter", "strides", "bias", "activation", "preluActivationWeights", "depthwiseConv2d", "shape", "conv2dTranspose", "conv3d", "kernelSize", "avgPool", "maxPool", "includeBatchInIndex", "result", "indexes", "maxPoolWithArgmax", "avgPool3d", "maxPool3d", "strideHeight", "<PERSON><PERSON><PERSON><PERSON>", "dilationHeight", "dilationWidth", "dilation2d", "TypeError", "CATEGORY"], "sources": ["C:\\tfjs-converter\\src\\operations\\executors\\convolution_executor.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2018 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {Rank, Tensor, Tensor3D, Tensor4D, Tensor5D} from '@tensorflow/tfjs-core';\n// tslint:disable-next-line: no-imports-from-dist\nimport * as tfOps from '@tensorflow/tfjs-core/dist/ops/ops_for_converter';\n\nimport {NamedTensorsMap} from '../../data/types';\nimport {ExecutionContext} from '../../executor/execution_context';\nimport {InternalOpExecutor, Node} from '../types';\n\nimport {getPadding, getParamValue} from './utils';\n\nfunction fusedConvAndDepthWiseParams(\n    node: Node, tensorMap: NamedTensorsMap, context: ExecutionContext) {\n  const [extraOp, activationFunc] =\n      (getParamValue('fusedOps', node, tensorMap, context) as string[]);\n\n  const isBiasAdd = extraOp === 'biasadd';\n  const noBiasAdd = !isBiasAdd;\n  const isPrelu = activationFunc === 'prelu';\n  const isBatchNorm = extraOp === 'fusedbatchnorm';\n\n  const numArgs =\n      (getParamValue('numArgs', node, tensorMap, context) as number);\n  if (isBiasAdd) {\n    if (isPrelu && numArgs !== 2) {\n      throw new Error(\n          'FusedConv2d and DepthwiseConv2d with BiasAdd and Prelu ' +\n          'must have two extra arguments: bias and alpha.');\n    }\n    if (!isPrelu && isBiasAdd && numArgs !== 1) {\n      throw new Error(\n          'FusedConv2d and DepthwiseConv2d with BiasAdd must have ' +\n          'one extra argument: bias.');\n    }\n  }\n  if (isBatchNorm) {\n    throw new Error(\n        'FusedConv2d and DepthwiseConv2d with FusedBatchNorm is not supported');\n  }\n  const stride = getParamValue('strides', node, tensorMap, context) as number[];\n  const pad = getPadding(node, tensorMap, context);\n  const dataFormat =\n      (getParamValue('dataFormat', node, tensorMap, context) as string)\n          .toUpperCase();\n  const dilations =\n      getParamValue('dilations', node, tensorMap, context) as number[];\n  let [biasArg, preluArg] =\n      getParamValue('args', node, tensorMap, context) as Tensor[];\n  if (noBiasAdd) {\n    preluArg = biasArg;\n    biasArg = undefined;\n  }\n  const leakyreluAlpha =\n      getParamValue('leakyreluAlpha', node, tensorMap, context) as number;\n\n  return {\n    stride,\n    pad,\n    dataFormat,\n    dilations,\n    biasArg,\n    preluArg,\n    activationFunc,\n    leakyreluAlpha\n  };\n}\n\nexport const executeOp: InternalOpExecutor =\n    (node: Node, tensorMap: NamedTensorsMap,\n     context: ExecutionContext, ops = tfOps): Tensor[] => {\n      switch (node.op) {\n        case 'Conv1D': {\n          const stride =\n              getParamValue('stride', node, tensorMap, context) as number;\n          const pad = getParamValue('pad', node, tensorMap, context);\n          const dataFormat =\n              (getParamValue('dataFormat', node, tensorMap, context) as string)\n                  .toUpperCase();\n          const dilation =\n              getParamValue('dilation', node, tensorMap, context) as number;\n          return [ops.conv1d(\n              getParamValue('x', node, tensorMap, context) as Tensor3D,\n              getParamValue('filter', node, tensorMap, context) as Tensor3D,\n              stride, pad as 'valid' | 'same', dataFormat as 'NWC' | 'NCW',\n              dilation)];\n        }\n        case 'Conv2D': {\n          const stride =\n              getParamValue('strides', node, tensorMap, context) as number[];\n          const pad = getPadding(node, tensorMap, context);\n          const dataFormat =\n              (getParamValue('dataFormat', node, tensorMap, context) as string)\n                  .toUpperCase();\n          const dilations =\n              getParamValue('dilations', node, tensorMap, context) as number[];\n          return [ops.conv2d(\n              getParamValue('x', node, tensorMap, context) as Tensor3D |\n                  Tensor4D,\n              getParamValue('filter', node, tensorMap, context) as Tensor4D,\n              [stride[1], stride[2]], pad as 'valid' | 'same',\n              dataFormat as 'NHWC' | 'NCHW', [dilations[1], dilations[2]])];\n        }\n        case '_FusedConv2D': {\n          const {\n            stride,\n            pad,\n            dataFormat,\n            dilations,\n            biasArg,\n            preluArg,\n            activationFunc,\n            leakyreluAlpha\n          } = fusedConvAndDepthWiseParams(node, tensorMap, context);\n\n          return [ops.fused.conv2d({\n            x: getParamValue('x', node, tensorMap, context) as Tensor3D |\n                Tensor4D,\n            filter: getParamValue('filter', node, tensorMap, context) as\n                Tensor4D,\n            strides: [stride[1], stride[2]],\n            pad: pad as 'valid' | 'same',\n            dataFormat: dataFormat as 'NHWC' | 'NCHW',\n            dilations: [dilations[1], dilations[2]],\n            bias: biasArg,\n            activation: activationFunc as tfOps.fused.Activation,\n            preluActivationWeights: preluArg,\n            leakyreluAlpha\n          })];\n        }\n\n        case 'FusedDepthwiseConv2dNative': {\n          const {\n            stride,\n            pad,\n            dataFormat,\n            dilations,\n            biasArg,\n            preluArg,\n            activationFunc,\n            leakyreluAlpha,\n          } = fusedConvAndDepthWiseParams(node, tensorMap, context);\n\n          return [ops.fused.depthwiseConv2d({\n            x: getParamValue('x', node, tensorMap, context) as Tensor3D |\n                Tensor4D,\n            filter: getParamValue('filter', node, tensorMap, context) as\n                Tensor4D,\n            strides: [stride[1], stride[2]],\n            pad: pad as 'valid' | 'same',\n            dataFormat: dataFormat as 'NHWC' | 'NCHW',\n            dilations: [dilations[1], dilations[2]],\n            bias: biasArg,\n            activation: activationFunc as tfOps.fused.Activation,\n            preluActivationWeights: preluArg,\n            leakyreluAlpha\n          })];\n        }\n        case 'Conv2DBackpropInput':\n        case 'Conv2dTranspose': {\n          const shape = getParamValue(\n                            'outputShape', node, tensorMap,\n                            context) as [number, number, number] |\n              [number, number, number, number];\n          const stride =\n              getParamValue('strides', node, tensorMap, context) as number[];\n          const pad = getPadding(node, tensorMap, context);\n          return [ops.conv2dTranspose(\n              getParamValue('x', node, tensorMap, context) as Tensor3D |\n                  Tensor4D,\n              getParamValue('filter', node, tensorMap, context) as Tensor4D,\n              shape, [stride[1], stride[2]], pad as 'valid' | 'same')];\n        }\n        case 'DepthwiseConv2dNative':\n        case 'DepthwiseConv2d': {\n          const stride =\n              getParamValue('strides', node, tensorMap, context) as number[];\n          const pad = getPadding(node, tensorMap, context);\n          const dilations =\n              getParamValue('dilations', node, tensorMap, context) as number[];\n          const dataFormat =\n              (getParamValue('dataFormat', node, tensorMap, context) as string)\n                  .toUpperCase();\n\n          return [ops.depthwiseConv2d(\n              getParamValue('input', node, tensorMap, context) as Tensor3D |\n                  Tensor4D,\n              getParamValue('filter', node, tensorMap, context) as Tensor4D,\n              [stride[1], stride[2]], pad as 'valid' | 'same',\n              dataFormat as 'NHWC' | 'NCHW', [dilations[1], dilations[2]])];\n        }\n        case 'Conv3D': {\n          const stride =\n              getParamValue('strides', node, tensorMap, context) as number[];\n          const pad = getParamValue('pad', node, tensorMap, context);\n          const dataFormat =\n              (getParamValue('dataFormat', node, tensorMap, context) as string)\n                  .toUpperCase();\n          const dilations =\n              getParamValue('dilations', node, tensorMap, context) as number[];\n          return [ops.conv3d(\n              getParamValue('x', node, tensorMap, context) as Tensor4D |\n                  Tensor<Rank.R5>,\n              getParamValue('filter', node, tensorMap, context) as\n                  Tensor<Rank.R5>,\n              [stride[1], stride[2], stride[3]], pad as 'valid' | 'same',\n              dataFormat as 'NDHWC' | 'NCDHW',\n              [dilations[1], dilations[2], dilations[3]])];\n        }\n        case 'AvgPool': {\n          const stride =\n              getParamValue('strides', node, tensorMap, context) as number[];\n          const pad = getParamValue('pad', node, tensorMap, context);\n          const kernelSize =\n              getParamValue('kernelSize', node, tensorMap, context) as number[];\n\n          return [ops.avgPool(\n              getParamValue('x', node, tensorMap, context) as Tensor3D |\n                  Tensor4D,\n              [kernelSize[1], kernelSize[2]], [stride[1], stride[2]],\n              pad as 'valid' | 'same')];\n        }\n        case 'MaxPool': {\n          const stride =\n              getParamValue('strides', node, tensorMap, context) as number[];\n          const pad = getParamValue('pad', node, tensorMap, context);\n          const kernelSize =\n              getParamValue('kernelSize', node, tensorMap, context) as number[];\n\n          return [ops.maxPool(\n              getParamValue('x', node, tensorMap, context) as Tensor3D |\n                  Tensor4D,\n              [kernelSize[1], kernelSize[2]], [stride[1], stride[2]],\n              pad as 'valid' | 'same')];\n        }\n        case 'MaxPoolWithArgmax': {\n          const stride =\n              getParamValue('strides', node, tensorMap, context) as number[];\n          const pad = getParamValue('pad', node, tensorMap, context);\n          const kernelSize =\n              getParamValue('kernelSize', node, tensorMap, context) as number[];\n          const includeBatchInIndex =\n              getParamValue('includeBatchInIndex', node, tensorMap, context) as\n              boolean;\n          const {result, indexes} = ops.maxPoolWithArgmax(\n              getParamValue('x', node, tensorMap, context) as Tensor4D,\n              [kernelSize[1], kernelSize[2]], [stride[1], stride[2]],\n              pad as 'valid' | 'same', includeBatchInIndex);\n          return [result, indexes];\n        }\n        case 'AvgPool3D': {\n          const stride =\n              getParamValue('strides', node, tensorMap, context) as number[];\n          const pad = getParamValue('pad', node, tensorMap, context);\n          const kernelSize =\n              getParamValue('kernelSize', node, tensorMap, context) as number[];\n\n          return [ops.avgPool3d(\n              getParamValue('x', node, tensorMap, context) as Tensor5D,\n              [kernelSize[1], kernelSize[2], kernelSize[3]],\n              [stride[1], stride[2], stride[3]], pad as 'valid' | 'same')];\n        }\n\n        case 'MaxPool3D': {\n          const stride =\n              getParamValue('strides', node, tensorMap, context) as number[];\n          const pad = getParamValue('pad', node, tensorMap, context);\n          const kernelSize =\n              getParamValue('kernelSize', node, tensorMap, context) as number[];\n\n          return [ops.maxPool3d(\n              getParamValue('x', node, tensorMap, context) as Tensor5D,\n              [kernelSize[1], kernelSize[2], kernelSize[3]],\n              [stride[1], stride[2], stride[3]], pad as 'valid' | 'same')];\n        }\n\n        case 'Dilation2D': {\n          const strides =\n              getParamValue('strides', node, tensorMap, context) as number[];\n          const pad = getParamValue('pad', node, tensorMap, context);\n          const dilations =\n              getParamValue('dilations', node, tensorMap, context) as number[];\n\n          // strides: [1, stride_height, stride_width, 1].\n          const strideHeight = strides[1];\n          const strideWidth = strides[2];\n\n          // dilations: [1, dilation_height, dilation_width, 1].\n          const dilationHeight = dilations[1];\n          const dilationWidth = dilations[2];\n\n          return [ops.dilation2d(\n              getParamValue('x', node, tensorMap, context) as Tensor3D |\n                  Tensor4D,\n              getParamValue('filter', node, tensorMap, context) as Tensor3D,\n              [strideHeight, strideWidth], pad as 'valid' | 'same',\n              [dilationHeight, dilationWidth], 'NHWC' /* dataFormat */)];\n        }\n\n        default:\n          throw TypeError(`Node type ${node.op} is not implemented`);\n      }\n    };\n\nexport const CATEGORY = 'convolution';\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAkBA;AACA,OAAO,KAAKA,KAAK,MAAM,kDAAkD;AAMzE,SAAQC,UAAU,EAAEC,aAAa,QAAO,SAAS;AAEjD,SAASC,2BAA2BA,CAChCC,IAAU,EAAEC,SAA0B,EAAEC,OAAyB;EACnE,MAAM,CAACC,OAAO,EAAEC,cAAc,CAAC,GAC1BN,aAAa,CAAC,UAAU,EAAEE,IAAI,EAAEC,SAAS,EAAEC,OAAO,CAAc;EAErE,MAAMG,SAAS,GAAGF,OAAO,KAAK,SAAS;EACvC,MAAMG,SAAS,GAAG,CAACD,SAAS;EAC5B,MAAME,OAAO,GAAGH,cAAc,KAAK,OAAO;EAC1C,MAAMI,WAAW,GAAGL,OAAO,KAAK,gBAAgB;EAEhD,MAAMM,OAAO,GACRX,aAAa,CAAC,SAAS,EAAEE,IAAI,EAAEC,SAAS,EAAEC,OAAO,CAAY;EAClE,IAAIG,SAAS,EAAE;IACb,IAAIE,OAAO,IAAIE,OAAO,KAAK,CAAC,EAAE;MAC5B,MAAM,IAAIC,KAAK,CACX,yDAAyD,GACzD,gDAAgD,CAAC;;IAEvD,IAAI,CAACH,OAAO,IAAIF,SAAS,IAAII,OAAO,KAAK,CAAC,EAAE;MAC1C,MAAM,IAAIC,KAAK,CACX,yDAAyD,GACzD,2BAA2B,CAAC;;;EAGpC,IAAIF,WAAW,EAAE;IACf,MAAM,IAAIE,KAAK,CACX,sEAAsE,CAAC;;EAE7E,MAAMC,MAAM,GAAGb,aAAa,CAAC,SAAS,EAAEE,IAAI,EAAEC,SAAS,EAAEC,OAAO,CAAa;EAC7E,MAAMU,GAAG,GAAGf,UAAU,CAACG,IAAI,EAAEC,SAAS,EAAEC,OAAO,CAAC;EAChD,MAAMW,UAAU,GACXf,aAAa,CAAC,YAAY,EAAEE,IAAI,EAAEC,SAAS,EAAEC,OAAO,CAAY,CAC5DY,WAAW,EAAE;EACtB,MAAMC,SAAS,GACXjB,aAAa,CAAC,WAAW,EAAEE,IAAI,EAAEC,SAAS,EAAEC,OAAO,CAAa;EACpE,IAAI,CAACc,OAAO,EAAEC,QAAQ,CAAC,GACnBnB,aAAa,CAAC,MAAM,EAAEE,IAAI,EAAEC,SAAS,EAAEC,OAAO,CAAa;EAC/D,IAAII,SAAS,EAAE;IACbW,QAAQ,GAAGD,OAAO;IAClBA,OAAO,GAAGE,SAAS;;EAErB,MAAMC,cAAc,GAChBrB,aAAa,CAAC,gBAAgB,EAAEE,IAAI,EAAEC,SAAS,EAAEC,OAAO,CAAW;EAEvE,OAAO;IACLS,MAAM;IACNC,GAAG;IACHC,UAAU;IACVE,SAAS;IACTC,OAAO;IACPC,QAAQ;IACRb,cAAc;IACde;GACD;AACH;AAEA,OAAO,MAAMC,SAAS,GAClBA,CAACpB,IAAU,EAAEC,SAA0B,EACtCC,OAAyB,EAAEmB,GAAG,GAAGzB,KAAK,KAAc;EACnD,QAAQI,IAAI,CAACsB,EAAE;IACb,KAAK,QAAQ;MAAE;QACb,MAAMX,MAAM,GACRb,aAAa,CAAC,QAAQ,EAAEE,IAAI,EAAEC,SAAS,EAAEC,OAAO,CAAW;QAC/D,MAAMU,GAAG,GAAGd,aAAa,CAAC,KAAK,EAAEE,IAAI,EAAEC,SAAS,EAAEC,OAAO,CAAC;QAC1D,MAAMW,UAAU,GACXf,aAAa,CAAC,YAAY,EAAEE,IAAI,EAAEC,SAAS,EAAEC,OAAO,CAAY,CAC5DY,WAAW,EAAE;QACtB,MAAMS,QAAQ,GACVzB,aAAa,CAAC,UAAU,EAAEE,IAAI,EAAEC,SAAS,EAAEC,OAAO,CAAW;QACjE,OAAO,CAACmB,GAAG,CAACG,MAAM,CACd1B,aAAa,CAAC,GAAG,EAAEE,IAAI,EAAEC,SAAS,EAAEC,OAAO,CAAa,EACxDJ,aAAa,CAAC,QAAQ,EAAEE,IAAI,EAAEC,SAAS,EAAEC,OAAO,CAAa,EAC7DS,MAAM,EAAEC,GAAuB,EAAEC,UAA2B,EAC5DU,QAAQ,CAAC,CAAC;;IAEhB,KAAK,QAAQ;MAAE;QACb,MAAMZ,MAAM,GACRb,aAAa,CAAC,SAAS,EAAEE,IAAI,EAAEC,SAAS,EAAEC,OAAO,CAAa;QAClE,MAAMU,GAAG,GAAGf,UAAU,CAACG,IAAI,EAAEC,SAAS,EAAEC,OAAO,CAAC;QAChD,MAAMW,UAAU,GACXf,aAAa,CAAC,YAAY,EAAEE,IAAI,EAAEC,SAAS,EAAEC,OAAO,CAAY,CAC5DY,WAAW,EAAE;QACtB,MAAMC,SAAS,GACXjB,aAAa,CAAC,WAAW,EAAEE,IAAI,EAAEC,SAAS,EAAEC,OAAO,CAAa;QACpE,OAAO,CAACmB,GAAG,CAACI,MAAM,CACd3B,aAAa,CAAC,GAAG,EAAEE,IAAI,EAAEC,SAAS,EAAEC,OAAO,CAC/B,EACZJ,aAAa,CAAC,QAAQ,EAAEE,IAAI,EAAEC,SAAS,EAAEC,OAAO,CAAa,EAC7D,CAACS,MAAM,CAAC,CAAC,CAAC,EAAEA,MAAM,CAAC,CAAC,CAAC,CAAC,EAAEC,GAAuB,EAC/CC,UAA6B,EAAE,CAACE,SAAS,CAAC,CAAC,CAAC,EAAEA,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;IAEnE,KAAK,cAAc;MAAE;QACnB,MAAM;UACJJ,MAAM;UACNC,GAAG;UACHC,UAAU;UACVE,SAAS;UACTC,OAAO;UACPC,QAAQ;UACRb,cAAc;UACde;QAAc,CACf,GAAGpB,2BAA2B,CAACC,IAAI,EAAEC,SAAS,EAAEC,OAAO,CAAC;QAEzD,OAAO,CAACmB,GAAG,CAACK,KAAK,CAACD,MAAM,CAAC;UACvBE,CAAC,EAAE7B,aAAa,CAAC,GAAG,EAAEE,IAAI,EAAEC,SAAS,EAAEC,OAAO,CAClC;UACZ0B,MAAM,EAAE9B,aAAa,CAAC,QAAQ,EAAEE,IAAI,EAAEC,SAAS,EAAEC,OAAO,CAC5C;UACZ2B,OAAO,EAAE,CAAClB,MAAM,CAAC,CAAC,CAAC,EAAEA,MAAM,CAAC,CAAC,CAAC,CAAC;UAC/BC,GAAG,EAAEA,GAAuB;UAC5BC,UAAU,EAAEA,UAA6B;UACzCE,SAAS,EAAE,CAACA,SAAS,CAAC,CAAC,CAAC,EAAEA,SAAS,CAAC,CAAC,CAAC,CAAC;UACvCe,IAAI,EAAEd,OAAO;UACbe,UAAU,EAAE3B,cAAwC;UACpD4B,sBAAsB,EAAEf,QAAQ;UAChCE;SACD,CAAC,CAAC;;IAGL,KAAK,4BAA4B;MAAE;QACjC,MAAM;UACJR,MAAM;UACNC,GAAG;UACHC,UAAU;UACVE,SAAS;UACTC,OAAO;UACPC,QAAQ;UACRb,cAAc;UACde;QAAc,CACf,GAAGpB,2BAA2B,CAACC,IAAI,EAAEC,SAAS,EAAEC,OAAO,CAAC;QAEzD,OAAO,CAACmB,GAAG,CAACK,KAAK,CAACO,eAAe,CAAC;UAChCN,CAAC,EAAE7B,aAAa,CAAC,GAAG,EAAEE,IAAI,EAAEC,SAAS,EAAEC,OAAO,CAClC;UACZ0B,MAAM,EAAE9B,aAAa,CAAC,QAAQ,EAAEE,IAAI,EAAEC,SAAS,EAAEC,OAAO,CAC5C;UACZ2B,OAAO,EAAE,CAAClB,MAAM,CAAC,CAAC,CAAC,EAAEA,MAAM,CAAC,CAAC,CAAC,CAAC;UAC/BC,GAAG,EAAEA,GAAuB;UAC5BC,UAAU,EAAEA,UAA6B;UACzCE,SAAS,EAAE,CAACA,SAAS,CAAC,CAAC,CAAC,EAAEA,SAAS,CAAC,CAAC,CAAC,CAAC;UACvCe,IAAI,EAAEd,OAAO;UACbe,UAAU,EAAE3B,cAAwC;UACpD4B,sBAAsB,EAAEf,QAAQ;UAChCE;SACD,CAAC,CAAC;;IAEL,KAAK,qBAAqB;IAC1B,KAAK,iBAAiB;MAAE;QACtB,MAAMe,KAAK,GAAGpC,aAAa,CACT,aAAa,EAAEE,IAAI,EAAEC,SAAS,EAC9BC,OAAO,CACW;QACpC,MAAMS,MAAM,GACRb,aAAa,CAAC,SAAS,EAAEE,IAAI,EAAEC,SAAS,EAAEC,OAAO,CAAa;QAClE,MAAMU,GAAG,GAAGf,UAAU,CAACG,IAAI,EAAEC,SAAS,EAAEC,OAAO,CAAC;QAChD,OAAO,CAACmB,GAAG,CAACc,eAAe,CACvBrC,aAAa,CAAC,GAAG,EAAEE,IAAI,EAAEC,SAAS,EAAEC,OAAO,CAC/B,EACZJ,aAAa,CAAC,QAAQ,EAAEE,IAAI,EAAEC,SAAS,EAAEC,OAAO,CAAa,EAC7DgC,KAAK,EAAE,CAACvB,MAAM,CAAC,CAAC,CAAC,EAAEA,MAAM,CAAC,CAAC,CAAC,CAAC,EAAEC,GAAuB,CAAC,CAAC;;IAE9D,KAAK,uBAAuB;IAC5B,KAAK,iBAAiB;MAAE;QACtB,MAAMD,MAAM,GACRb,aAAa,CAAC,SAAS,EAAEE,IAAI,EAAEC,SAAS,EAAEC,OAAO,CAAa;QAClE,MAAMU,GAAG,GAAGf,UAAU,CAACG,IAAI,EAAEC,SAAS,EAAEC,OAAO,CAAC;QAChD,MAAMa,SAAS,GACXjB,aAAa,CAAC,WAAW,EAAEE,IAAI,EAAEC,SAAS,EAAEC,OAAO,CAAa;QACpE,MAAMW,UAAU,GACXf,aAAa,CAAC,YAAY,EAAEE,IAAI,EAAEC,SAAS,EAAEC,OAAO,CAAY,CAC5DY,WAAW,EAAE;QAEtB,OAAO,CAACO,GAAG,CAACY,eAAe,CACvBnC,aAAa,CAAC,OAAO,EAAEE,IAAI,EAAEC,SAAS,EAAEC,OAAO,CACnC,EACZJ,aAAa,CAAC,QAAQ,EAAEE,IAAI,EAAEC,SAAS,EAAEC,OAAO,CAAa,EAC7D,CAACS,MAAM,CAAC,CAAC,CAAC,EAAEA,MAAM,CAAC,CAAC,CAAC,CAAC,EAAEC,GAAuB,EAC/CC,UAA6B,EAAE,CAACE,SAAS,CAAC,CAAC,CAAC,EAAEA,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;IAEnE,KAAK,QAAQ;MAAE;QACb,MAAMJ,MAAM,GACRb,aAAa,CAAC,SAAS,EAAEE,IAAI,EAAEC,SAAS,EAAEC,OAAO,CAAa;QAClE,MAAMU,GAAG,GAAGd,aAAa,CAAC,KAAK,EAAEE,IAAI,EAAEC,SAAS,EAAEC,OAAO,CAAC;QAC1D,MAAMW,UAAU,GACXf,aAAa,CAAC,YAAY,EAAEE,IAAI,EAAEC,SAAS,EAAEC,OAAO,CAAY,CAC5DY,WAAW,EAAE;QACtB,MAAMC,SAAS,GACXjB,aAAa,CAAC,WAAW,EAAEE,IAAI,EAAEC,SAAS,EAAEC,OAAO,CAAa;QACpE,OAAO,CAACmB,GAAG,CAACe,MAAM,CACdtC,aAAa,CAAC,GAAG,EAAEE,IAAI,EAAEC,SAAS,EAAEC,OAAO,CACxB,EACnBJ,aAAa,CAAC,QAAQ,EAAEE,IAAI,EAAEC,SAAS,EAAEC,OAAO,CAC7B,EACnB,CAACS,MAAM,CAAC,CAAC,CAAC,EAAEA,MAAM,CAAC,CAAC,CAAC,EAAEA,MAAM,CAAC,CAAC,CAAC,CAAC,EAAEC,GAAuB,EAC1DC,UAA+B,EAC/B,CAACE,SAAS,CAAC,CAAC,CAAC,EAAEA,SAAS,CAAC,CAAC,CAAC,EAAEA,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;IAElD,KAAK,SAAS;MAAE;QACd,MAAMJ,MAAM,GACRb,aAAa,CAAC,SAAS,EAAEE,IAAI,EAAEC,SAAS,EAAEC,OAAO,CAAa;QAClE,MAAMU,GAAG,GAAGd,aAAa,CAAC,KAAK,EAAEE,IAAI,EAAEC,SAAS,EAAEC,OAAO,CAAC;QAC1D,MAAMmC,UAAU,GACZvC,aAAa,CAAC,YAAY,EAAEE,IAAI,EAAEC,SAAS,EAAEC,OAAO,CAAa;QAErE,OAAO,CAACmB,GAAG,CAACiB,OAAO,CACfxC,aAAa,CAAC,GAAG,EAAEE,IAAI,EAAEC,SAAS,EAAEC,OAAO,CAC/B,EACZ,CAACmC,UAAU,CAAC,CAAC,CAAC,EAAEA,UAAU,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC1B,MAAM,CAAC,CAAC,CAAC,EAAEA,MAAM,CAAC,CAAC,CAAC,CAAC,EACtDC,GAAuB,CAAC,CAAC;;IAE/B,KAAK,SAAS;MAAE;QACd,MAAMD,MAAM,GACRb,aAAa,CAAC,SAAS,EAAEE,IAAI,EAAEC,SAAS,EAAEC,OAAO,CAAa;QAClE,MAAMU,GAAG,GAAGd,aAAa,CAAC,KAAK,EAAEE,IAAI,EAAEC,SAAS,EAAEC,OAAO,CAAC;QAC1D,MAAMmC,UAAU,GACZvC,aAAa,CAAC,YAAY,EAAEE,IAAI,EAAEC,SAAS,EAAEC,OAAO,CAAa;QAErE,OAAO,CAACmB,GAAG,CAACkB,OAAO,CACfzC,aAAa,CAAC,GAAG,EAAEE,IAAI,EAAEC,SAAS,EAAEC,OAAO,CAC/B,EACZ,CAACmC,UAAU,CAAC,CAAC,CAAC,EAAEA,UAAU,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC1B,MAAM,CAAC,CAAC,CAAC,EAAEA,MAAM,CAAC,CAAC,CAAC,CAAC,EACtDC,GAAuB,CAAC,CAAC;;IAE/B,KAAK,mBAAmB;MAAE;QACxB,MAAMD,MAAM,GACRb,aAAa,CAAC,SAAS,EAAEE,IAAI,EAAEC,SAAS,EAAEC,OAAO,CAAa;QAClE,MAAMU,GAAG,GAAGd,aAAa,CAAC,KAAK,EAAEE,IAAI,EAAEC,SAAS,EAAEC,OAAO,CAAC;QAC1D,MAAMmC,UAAU,GACZvC,aAAa,CAAC,YAAY,EAAEE,IAAI,EAAEC,SAAS,EAAEC,OAAO,CAAa;QACrE,MAAMsC,mBAAmB,GACrB1C,aAAa,CAAC,qBAAqB,EAAEE,IAAI,EAAEC,SAAS,EAAEC,OAAO,CACtD;QACX,MAAM;UAACuC,MAAM;UAAEC;QAAO,CAAC,GAAGrB,GAAG,CAACsB,iBAAiB,CAC3C7C,aAAa,CAAC,GAAG,EAAEE,IAAI,EAAEC,SAAS,EAAEC,OAAO,CAAa,EACxD,CAACmC,UAAU,CAAC,CAAC,CAAC,EAAEA,UAAU,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC1B,MAAM,CAAC,CAAC,CAAC,EAAEA,MAAM,CAAC,CAAC,CAAC,CAAC,EACtDC,GAAuB,EAAE4B,mBAAmB,CAAC;QACjD,OAAO,CAACC,MAAM,EAAEC,OAAO,CAAC;;IAE1B,KAAK,WAAW;MAAE;QAChB,MAAM/B,MAAM,GACRb,aAAa,CAAC,SAAS,EAAEE,IAAI,EAAEC,SAAS,EAAEC,OAAO,CAAa;QAClE,MAAMU,GAAG,GAAGd,aAAa,CAAC,KAAK,EAAEE,IAAI,EAAEC,SAAS,EAAEC,OAAO,CAAC;QAC1D,MAAMmC,UAAU,GACZvC,aAAa,CAAC,YAAY,EAAEE,IAAI,EAAEC,SAAS,EAAEC,OAAO,CAAa;QAErE,OAAO,CAACmB,GAAG,CAACuB,SAAS,CACjB9C,aAAa,CAAC,GAAG,EAAEE,IAAI,EAAEC,SAAS,EAAEC,OAAO,CAAa,EACxD,CAACmC,UAAU,CAAC,CAAC,CAAC,EAAEA,UAAU,CAAC,CAAC,CAAC,EAAEA,UAAU,CAAC,CAAC,CAAC,CAAC,EAC7C,CAAC1B,MAAM,CAAC,CAAC,CAAC,EAAEA,MAAM,CAAC,CAAC,CAAC,EAAEA,MAAM,CAAC,CAAC,CAAC,CAAC,EAAEC,GAAuB,CAAC,CAAC;;IAGlE,KAAK,WAAW;MAAE;QAChB,MAAMD,MAAM,GACRb,aAAa,CAAC,SAAS,EAAEE,IAAI,EAAEC,SAAS,EAAEC,OAAO,CAAa;QAClE,MAAMU,GAAG,GAAGd,aAAa,CAAC,KAAK,EAAEE,IAAI,EAAEC,SAAS,EAAEC,OAAO,CAAC;QAC1D,MAAMmC,UAAU,GACZvC,aAAa,CAAC,YAAY,EAAEE,IAAI,EAAEC,SAAS,EAAEC,OAAO,CAAa;QAErE,OAAO,CAACmB,GAAG,CAACwB,SAAS,CACjB/C,aAAa,CAAC,GAAG,EAAEE,IAAI,EAAEC,SAAS,EAAEC,OAAO,CAAa,EACxD,CAACmC,UAAU,CAAC,CAAC,CAAC,EAAEA,UAAU,CAAC,CAAC,CAAC,EAAEA,UAAU,CAAC,CAAC,CAAC,CAAC,EAC7C,CAAC1B,MAAM,CAAC,CAAC,CAAC,EAAEA,MAAM,CAAC,CAAC,CAAC,EAAEA,MAAM,CAAC,CAAC,CAAC,CAAC,EAAEC,GAAuB,CAAC,CAAC;;IAGlE,KAAK,YAAY;MAAE;QACjB,MAAMiB,OAAO,GACT/B,aAAa,CAAC,SAAS,EAAEE,IAAI,EAAEC,SAAS,EAAEC,OAAO,CAAa;QAClE,MAAMU,GAAG,GAAGd,aAAa,CAAC,KAAK,EAAEE,IAAI,EAAEC,SAAS,EAAEC,OAAO,CAAC;QAC1D,MAAMa,SAAS,GACXjB,aAAa,CAAC,WAAW,EAAEE,IAAI,EAAEC,SAAS,EAAEC,OAAO,CAAa;QAEpE;QACA,MAAM4C,YAAY,GAAGjB,OAAO,CAAC,CAAC,CAAC;QAC/B,MAAMkB,WAAW,GAAGlB,OAAO,CAAC,CAAC,CAAC;QAE9B;QACA,MAAMmB,cAAc,GAAGjC,SAAS,CAAC,CAAC,CAAC;QACnC,MAAMkC,aAAa,GAAGlC,SAAS,CAAC,CAAC,CAAC;QAElC,OAAO,CAACM,GAAG,CAAC6B,UAAU,CAClBpD,aAAa,CAAC,GAAG,EAAEE,IAAI,EAAEC,SAAS,EAAEC,OAAO,CAC/B,EACZJ,aAAa,CAAC,QAAQ,EAAEE,IAAI,EAAEC,SAAS,EAAEC,OAAO,CAAa,EAC7D,CAAC4C,YAAY,EAAEC,WAAW,CAAC,EAAEnC,GAAuB,EACpD,CAACoC,cAAc,EAAEC,aAAa,CAAC,EAAE,MAAM,CAAC,gBAAgB,CAAC,CAAC;;IAGhE;MACE,MAAME,SAAS,CAAC,aAAanD,IAAI,CAACsB,EAAE,qBAAqB,CAAC;;AAEhE,CAAC;AAEL,OAAO,MAAM8B,QAAQ,GAAG,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}