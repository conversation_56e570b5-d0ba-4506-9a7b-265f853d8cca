{"ast": null, "code": "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { OneHot } from '../kernel_names';\nimport { zeros } from '../ops/zeros';\nexport const oneHotGradConfig = {\n  kernelName: OneHot,\n  inputsToSave: ['indices'],\n  gradFunc: (dy, saved) => {\n    const indices = saved[0];\n    return {\n      indices: () => zeros(indices.shape, 'float32')\n    };\n  }\n};", "map": {"version": 3, "names": ["OneHot", "zeros", "oneHotGradConfig", "kernelName", "inputsToSave", "grad<PERSON>unc", "dy", "saved", "indices", "shape"], "sources": ["C:\\tfjs-core\\src\\gradients\\OneHot_grad.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {OneHot} from '../kernel_names';\nimport {GradConfig} from '../kernel_registry';\nimport {zeros} from '../ops/zeros';\nimport {Tensor} from '../tensor';\n\nexport const oneHotGradConfig: GradConfig = {\n  kernelName: OneHot,\n  inputsToSave: ['indices'],\n  gradFunc: (dy: Tensor, saved: Tensor[]) => {\n    const indices = saved[0];\n    return {indices: () => zeros(indices.shape, 'float32')};\n  }\n};\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAiBA,SAAQA,MAAM,QAAO,iBAAiB;AAEtC,SAAQC,KAAK,QAAO,cAAc;AAGlC,OAAO,MAAMC,gBAAgB,GAAe;EAC1CC,UAAU,EAAEH,MAAM;EAClBI,YAAY,EAAE,CAAC,SAAS,CAAC;EACzBC,QAAQ,EAAEA,CAACC,EAAU,EAAEC,KAAe,KAAI;IACxC,MAAMC,OAAO,GAAGD,KAAK,CAAC,CAAC,CAAC;IACxB,OAAO;MAACC,OAAO,EAAEA,CAAA,KAAMP,KAAK,CAACO,OAAO,CAACC,KAAK,EAAE,SAAS;IAAC,CAAC;EACzD;CACD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}