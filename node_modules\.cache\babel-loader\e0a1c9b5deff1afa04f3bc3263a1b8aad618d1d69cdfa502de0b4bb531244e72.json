{"ast": null, "code": "import { assert } from '../util';\n/**\n * Prepare the split size array. When the input is a number, the axis is evenly\n * divided among the split size. When the input contains the negative value, the\n * rest of the axis is allocated toward that.\n */\nexport function prepareSplitSize(x, numOrSizeSplits, axis = 0) {\n  let splitSizes = [];\n  if (typeof numOrSizeSplits === 'number') {\n    assert(x.shape[axis] % numOrSizeSplits === 0, () => 'Number of splits must evenly divide the axis.');\n    splitSizes = new Array(numOrSizeSplits).fill(x.shape[axis] / numOrSizeSplits);\n  } else {\n    const numOfNegs = numOrSizeSplits.reduce((count, value) => {\n      if (value === -1) {\n        count += 1;\n      }\n      return count;\n    }, 0);\n    assert(numOfNegs <= 1, () => 'There should be only one negative value in split array.');\n    const negIndex = numOrSizeSplits.indexOf(-1);\n    // Allow the number of split array to be -1, which indicates the rest\n    // of dimension is allocated to that split.\n    if (negIndex !== -1) {\n      const total = numOrSizeSplits.reduce((a, b) => b > 0 ? a + b : a);\n      numOrSizeSplits[negIndex] = x.shape[axis] - total;\n    }\n    assert(x.shape[axis] === numOrSizeSplits.reduce((a, b) => a + b), () => 'The sum of sizes must match the size of the axis dimension.');\n    splitSizes = numOrSizeSplits;\n  }\n  return splitSizes;\n}", "map": {"version": 3, "names": ["assert", "prepareSplitSize", "x", "numOrSizeSplits", "axis", "splitSizes", "shape", "Array", "fill", "numOfNegs", "reduce", "count", "value", "negIndex", "indexOf", "total", "a", "b"], "sources": ["C:\\tfjs-core\\src\\ops\\split_util.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { TensorInfo } from '../tensor_info';\nimport {Tensor} from '../tensor';\nimport {assert} from '../util';\n\n/**\n * Prepare the split size array. When the input is a number, the axis is evenly\n * divided among the split size. When the input contains the negative value, the\n * rest of the axis is allocated toward that.\n */\nexport function prepareSplitSize(\n    x: Tensor|TensorInfo, numOrSizeSplits: number[]|number,\n    axis = 0): number[] {\n  let splitSizes = [];\n  if (typeof (numOrSizeSplits) === 'number') {\n    assert(\n        x.shape[axis] % numOrSizeSplits === 0,\n        () => 'Number of splits must evenly divide the axis.');\n    splitSizes =\n        new Array(numOrSizeSplits).fill(x.shape[axis] / numOrSizeSplits);\n  } else {\n    const numOfNegs = numOrSizeSplits.reduce((count, value) => {\n      if (value === -1) {\n        count += 1;\n      }\n      return count;\n    }, 0);\n    assert(\n        numOfNegs <= 1,\n        () => 'There should be only one negative value in split array.');\n    const negIndex = numOrSizeSplits.indexOf(-1);\n    // Allow the number of split array to be -1, which indicates the rest\n    // of dimension is allocated to that split.\n    if (negIndex !== -1) {\n      const total = numOrSizeSplits.reduce((a, b) => b > 0 ? a + b : a);\n      numOrSizeSplits[negIndex] = x.shape[axis] - total;\n    }\n    assert(\n        x.shape[axis] === numOrSizeSplits.reduce((a, b) => a + b),\n        () => 'The sum of sizes must match the size of the axis dimension.');\n    splitSizes = numOrSizeSplits;\n  }\n\n  return splitSizes;\n}\n"], "mappings": "AAkBA,SAAQA,MAAM,QAAO,SAAS;AAE9B;;;;;AAKA,OAAM,SAAUC,gBAAgBA,CAC5BC,CAAoB,EAAEC,eAAgC,EACtDC,IAAI,GAAG,CAAC;EACV,IAAIC,UAAU,GAAG,EAAE;EACnB,IAAI,OAAQF,eAAgB,KAAK,QAAQ,EAAE;IACzCH,MAAM,CACFE,CAAC,CAACI,KAAK,CAACF,IAAI,CAAC,GAAGD,eAAe,KAAK,CAAC,EACrC,MAAM,+CAA+C,CAAC;IAC1DE,UAAU,GACN,IAAIE,KAAK,CAACJ,eAAe,CAAC,CAACK,IAAI,CAACN,CAAC,CAACI,KAAK,CAACF,IAAI,CAAC,GAAGD,eAAe,CAAC;GACrE,MAAM;IACL,MAAMM,SAAS,GAAGN,eAAe,CAACO,MAAM,CAAC,CAACC,KAAK,EAAEC,KAAK,KAAI;MACxD,IAAIA,KAAK,KAAK,CAAC,CAAC,EAAE;QAChBD,KAAK,IAAI,CAAC;;MAEZ,OAAOA,KAAK;IACd,CAAC,EAAE,CAAC,CAAC;IACLX,MAAM,CACFS,SAAS,IAAI,CAAC,EACd,MAAM,yDAAyD,CAAC;IACpE,MAAMI,QAAQ,GAAGV,eAAe,CAACW,OAAO,CAAC,CAAC,CAAC,CAAC;IAC5C;IACA;IACA,IAAID,QAAQ,KAAK,CAAC,CAAC,EAAE;MACnB,MAAME,KAAK,GAAGZ,eAAe,CAACO,MAAM,CAAC,CAACM,CAAC,EAAEC,CAAC,KAAKA,CAAC,GAAG,CAAC,GAAGD,CAAC,GAAGC,CAAC,GAAGD,CAAC,CAAC;MACjEb,eAAe,CAACU,QAAQ,CAAC,GAAGX,CAAC,CAACI,KAAK,CAACF,IAAI,CAAC,GAAGW,KAAK;;IAEnDf,MAAM,CACFE,CAAC,CAACI,KAAK,CAACF,IAAI,CAAC,KAAKD,eAAe,CAACO,MAAM,CAAC,CAACM,CAAC,EAAEC,CAAC,KAAKD,CAAC,GAAGC,CAAC,CAAC,EACzD,MAAM,6DAA6D,CAAC;IACxEZ,UAAU,GAAGF,eAAe;;EAG9B,OAAOE,UAAU;AACnB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}