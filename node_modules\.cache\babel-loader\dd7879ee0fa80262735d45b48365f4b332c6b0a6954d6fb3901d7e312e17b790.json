{"ast": null, "code": "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { convertToTensor } from '../../tensor_util_env';\nimport { assertShapesMatch } from '../../util';\nimport { add } from '../add';\nimport { log } from '../log';\nimport { Reduction } from '../loss_ops_utils';\nimport { mul } from '../mul';\nimport { neg } from '../neg';\nimport { op } from '../operation';\nimport { scalar } from '../scalar';\nimport { sub } from '../sub';\nimport { computeWeightedLoss } from './compute_weighted_loss';\n/**\n * Computes the log loss between two tensors.\n *\n * @param labels The ground truth output tensor, same dimensions as\n *    'predictions'.\n * @param predictions The predicted outputs.\n * @param weights Tensor whose rank is either 0, or the same rank as\n *    `labels`, and must be broadcastable to `labels` (i.e., all dimensions\n *    must be either `1`, or the same as the corresponding `losses`\n *    dimension).\n * @param epsilon A small increment to avoid taking log of zero\n * @param reduction Type of reduction to apply to loss. Should be of type\n *    `Reduction`\n *\n * @doc {heading: 'Training', subheading: 'Losses', namespace: 'losses'}\n */\nfunction logLoss_(labels, predictions, weights) {\n  let epsilon = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : 1e-7;\n  let reduction = arguments.length > 4 && arguments[4] !== undefined ? arguments[4] : Reduction.SUM_BY_NONZERO_WEIGHTS;\n  const $labels = convertToTensor(labels, 'labels', 'logLoss');\n  const $predictions = convertToTensor(predictions, 'predictions', 'logLoss');\n  let $weights = null;\n  if (weights != null) {\n    $weights = convertToTensor(weights, 'weights', 'logLoss');\n  }\n  assertShapesMatch($labels.shape, $predictions.shape, 'Error in logLoss: ');\n  const one = scalar(1);\n  const epsilonScalar = scalar(epsilon);\n  const l1 = neg(mul($labels, log(add($predictions, epsilonScalar))));\n  const l2 = mul(sub(one, $labels), log(add(sub(one, $predictions), epsilonScalar)));\n  const losses = sub(l1, l2);\n  return computeWeightedLoss(losses, $weights, reduction);\n}\nexport const logLoss = /* @__PURE__ */op({\n  logLoss_\n});", "map": {"version": 3, "names": ["convertToTensor", "assertShapesMatch", "add", "log", "Reduction", "mul", "neg", "op", "scalar", "sub", "computeWeightedLoss", "logLoss_", "labels", "predictions", "weights", "epsilon", "arguments", "length", "undefined", "reduction", "SUM_BY_NONZERO_WEIGHTS", "$labels", "$predictions", "$weights", "shape", "one", "epsilonScalar", "l1", "l2", "losses", "logLoss"], "sources": ["C:\\tfjs-core\\src\\ops\\losses\\log_loss.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {Tensor} from '../../tensor';\nimport {convertToTensor} from '../../tensor_util_env';\nimport {TensorLike} from '../../types';\nimport {assertShapesMatch} from '../../util';\nimport {add} from '../add';\nimport {log} from '../log';\nimport {Reduction} from '../loss_ops_utils';\nimport {mul} from '../mul';\nimport {neg} from '../neg';\nimport {op} from '../operation';\nimport {scalar} from '../scalar';\nimport {sub} from '../sub';\n\nimport {computeWeightedLoss} from './compute_weighted_loss';\n\n/**\n * Computes the log loss between two tensors.\n *\n * @param labels The ground truth output tensor, same dimensions as\n *    'predictions'.\n * @param predictions The predicted outputs.\n * @param weights Tensor whose rank is either 0, or the same rank as\n *    `labels`, and must be broadcastable to `labels` (i.e., all dimensions\n *    must be either `1`, or the same as the corresponding `losses`\n *    dimension).\n * @param epsilon A small increment to avoid taking log of zero\n * @param reduction Type of reduction to apply to loss. Should be of type\n *    `Reduction`\n *\n * @doc {heading: 'Training', subheading: 'Losses', namespace: 'losses'}\n */\nfunction logLoss_<T extends Tensor, O extends Tensor>(\n    labels: T|TensorLike, predictions: T|TensorLike,\n    weights?: Tensor|TensorLike, epsilon = 1e-7,\n    reduction = Reduction.SUM_BY_NONZERO_WEIGHTS): O {\n  const $labels = convertToTensor(labels, 'labels', 'logLoss');\n  const $predictions = convertToTensor(predictions, 'predictions', 'logLoss');\n  let $weights: Tensor = null;\n  if (weights != null) {\n    $weights = convertToTensor(weights, 'weights', 'logLoss');\n  }\n  assertShapesMatch($labels.shape, $predictions.shape, 'Error in logLoss: ');\n\n  const one = scalar(1);\n  const epsilonScalar = scalar(epsilon);\n\n  const l1 = neg(mul($labels, log(add($predictions, epsilonScalar))));\n  const l2 =\n      mul(sub(one, $labels), log(add(sub(one, $predictions), epsilonScalar)));\n  const losses = sub(l1, l2);\n  return computeWeightedLoss(losses, $weights, reduction);\n}\nexport const logLoss = /* @__PURE__ */ op({logLoss_});\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAkBA,SAAQA,eAAe,QAAO,uBAAuB;AAErD,SAAQC,iBAAiB,QAAO,YAAY;AAC5C,SAAQC,GAAG,QAAO,QAAQ;AAC1B,SAAQC,GAAG,QAAO,QAAQ;AAC1B,SAAQC,SAAS,QAAO,mBAAmB;AAC3C,SAAQC,GAAG,QAAO,QAAQ;AAC1B,SAAQC,GAAG,QAAO,QAAQ;AAC1B,SAAQC,EAAE,QAAO,cAAc;AAC/B,SAAQC,MAAM,QAAO,WAAW;AAChC,SAAQC,GAAG,QAAO,QAAQ;AAE1B,SAAQC,mBAAmB,QAAO,yBAAyB;AAE3D;;;;;;;;;;;;;;;;AAgBA,SAASC,QAAQA,CACbC,MAAoB,EAAEC,WAAyB,EAC/CC,OAA2B,EACiB;EAAA,IADfC,OAAO,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,IAAI;EAAA,IAC3CG,SAAS,GAAAH,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAGZ,SAAS,CAACgB,sBAAsB;EAC9C,MAAMC,OAAO,GAAGrB,eAAe,CAACY,MAAM,EAAE,QAAQ,EAAE,SAAS,CAAC;EAC5D,MAAMU,YAAY,GAAGtB,eAAe,CAACa,WAAW,EAAE,aAAa,EAAE,SAAS,CAAC;EAC3E,IAAIU,QAAQ,GAAW,IAAI;EAC3B,IAAIT,OAAO,IAAI,IAAI,EAAE;IACnBS,QAAQ,GAAGvB,eAAe,CAACc,OAAO,EAAE,SAAS,EAAE,SAAS,CAAC;;EAE3Db,iBAAiB,CAACoB,OAAO,CAACG,KAAK,EAAEF,YAAY,CAACE,KAAK,EAAE,oBAAoB,CAAC;EAE1E,MAAMC,GAAG,GAAGjB,MAAM,CAAC,CAAC,CAAC;EACrB,MAAMkB,aAAa,GAAGlB,MAAM,CAACO,OAAO,CAAC;EAErC,MAAMY,EAAE,GAAGrB,GAAG,CAACD,GAAG,CAACgB,OAAO,EAAElB,GAAG,CAACD,GAAG,CAACoB,YAAY,EAAEI,aAAa,CAAC,CAAC,CAAC,CAAC;EACnE,MAAME,EAAE,GACJvB,GAAG,CAACI,GAAG,CAACgB,GAAG,EAAEJ,OAAO,CAAC,EAAElB,GAAG,CAACD,GAAG,CAACO,GAAG,CAACgB,GAAG,EAAEH,YAAY,CAAC,EAAEI,aAAa,CAAC,CAAC,CAAC;EAC3E,MAAMG,MAAM,GAAGpB,GAAG,CAACkB,EAAE,EAAEC,EAAE,CAAC;EAC1B,OAAOlB,mBAAmB,CAACmB,MAAM,EAAEN,QAAQ,EAAEJ,SAAS,CAAC;AACzD;AACA,OAAO,MAAMW,OAAO,GAAG,eAAgBvB,EAAE,CAAC;EAACI;AAAQ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}