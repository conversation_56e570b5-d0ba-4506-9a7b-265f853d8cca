{"ast": null, "code": "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { backend_util, GatherNd, util } from '@tensorflow/tfjs-core';\nimport { gatherNdImpl } from './GatherNd_Impl';\nexport function gatherNd(args) {\n  const {\n    inputs,\n    backend\n  } = args;\n  const {\n    params,\n    indices\n  } = inputs;\n  const paramsSize = util.sizeFromShape(params.shape);\n  const indicesShape = indices.shape;\n  const sliceRank = indicesShape[indicesShape.length - 1];\n  const [resultShape, numSlices, sliceSize, strides] = backend_util.prepareAndValidate(params, indices);\n  if (numSlices === 0) {\n    return backend.makeTensorInfo(resultShape, params.dtype, []);\n  }\n  const indicesData = backend.data.get(indices.dataId).values;\n  const paramsBuf = backend.bufferSync(params);\n  const outBuf = gatherNdImpl(indicesData, paramsBuf, params.dtype, numSlices, sliceRank, sliceSize, strides, params.shape, paramsSize);\n  return backend.makeTensorInfo(resultShape, params.dtype, outBuf.values);\n}\nexport const gatherNdConfig = {\n  kernelName: GatherNd,\n  backendName: 'cpu',\n  kernelFunc: gatherNd\n};", "map": {"version": 3, "names": ["backend_util", "GatherNd", "util", "gatherNdImpl", "gatherNd", "args", "inputs", "backend", "params", "indices", "paramsSize", "sizeFromShape", "shape", "indicesShape", "sliceRank", "length", "resultShape", "numSlices", "sliceSize", "strides", "prepareAndValidate", "makeTensorInfo", "dtype", "indicesData", "data", "get", "dataId", "values", "paramsBuf", "bufferSync", "outBuf", "gatherNdConfig", "kernelName", "backendName", "kernelFunc"], "sources": ["C:\\tfjs-backend-cpu\\src\\kernels\\GatherNd.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {backend_util, GatherNd, GatherNdInputs, KernelConfig, KernelFunc, Rank, TensorInfo, TypedArray, util} from '@tensorflow/tfjs-core';\n\nimport {MathBackendCPU} from '../backend_cpu';\n\nimport {gatherNdImpl} from './GatherNd_Impl';\n\nexport function gatherNd(\n    args: {inputs: GatherNdInputs, backend: MathBackendCPU}): TensorInfo {\n  const {inputs, backend} = args;\n  const {params, indices} = inputs;\n\n  const paramsSize = util.sizeFromShape(params.shape);\n\n  const indicesShape = indices.shape;\n  const sliceRank = indicesShape[indicesShape.length - 1];\n\n  const [resultShape, numSlices, sliceSize, strides] =\n      backend_util.prepareAndValidate(params, indices);\n  if (numSlices === 0) {\n    return backend.makeTensorInfo(resultShape, params.dtype, []);\n  }\n\n  const indicesData = backend.data.get(indices.dataId).values as TypedArray;\n  const paramsBuf = backend.bufferSync<Rank, 'float32'>(params);\n  const outBuf = gatherNdImpl(\n      indicesData, paramsBuf, params.dtype, numSlices, sliceRank, sliceSize,\n      strides, params.shape, paramsSize);\n\n  return backend.makeTensorInfo(resultShape, params.dtype, outBuf.values);\n}\n\nexport const gatherNdConfig: KernelConfig = {\n  kernelName: GatherNd,\n  backendName: 'cpu',\n  kernelFunc: gatherNd as unknown as KernelFunc\n};\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAiBA,SAAQA,YAAY,EAAEC,QAAQ,EAA0EC,IAAI,QAAO,uBAAuB;AAI1I,SAAQC,YAAY,QAAO,iBAAiB;AAE5C,OAAM,SAAUC,QAAQA,CACpBC,IAAuD;EACzD,MAAM;IAACC,MAAM;IAAEC;EAAO,CAAC,GAAGF,IAAI;EAC9B,MAAM;IAACG,MAAM;IAAEC;EAAO,CAAC,GAAGH,MAAM;EAEhC,MAAMI,UAAU,GAAGR,IAAI,CAACS,aAAa,CAACH,MAAM,CAACI,KAAK,CAAC;EAEnD,MAAMC,YAAY,GAAGJ,OAAO,CAACG,KAAK;EAClC,MAAME,SAAS,GAAGD,YAAY,CAACA,YAAY,CAACE,MAAM,GAAG,CAAC,CAAC;EAEvD,MAAM,CAACC,WAAW,EAAEC,SAAS,EAAEC,SAAS,EAAEC,OAAO,CAAC,GAC9CnB,YAAY,CAACoB,kBAAkB,CAACZ,MAAM,EAAEC,OAAO,CAAC;EACpD,IAAIQ,SAAS,KAAK,CAAC,EAAE;IACnB,OAAOV,OAAO,CAACc,cAAc,CAACL,WAAW,EAAER,MAAM,CAACc,KAAK,EAAE,EAAE,CAAC;;EAG9D,MAAMC,WAAW,GAAGhB,OAAO,CAACiB,IAAI,CAACC,GAAG,CAAChB,OAAO,CAACiB,MAAM,CAAC,CAACC,MAAoB;EACzE,MAAMC,SAAS,GAAGrB,OAAO,CAACsB,UAAU,CAAkBrB,MAAM,CAAC;EAC7D,MAAMsB,MAAM,GAAG3B,YAAY,CACvBoB,WAAW,EAAEK,SAAS,EAAEpB,MAAM,CAACc,KAAK,EAAEL,SAAS,EAAEH,SAAS,EAAEI,SAAS,EACrEC,OAAO,EAAEX,MAAM,CAACI,KAAK,EAAEF,UAAU,CAAC;EAEtC,OAAOH,OAAO,CAACc,cAAc,CAACL,WAAW,EAAER,MAAM,CAACc,KAAK,EAAEQ,MAAM,CAACH,MAAM,CAAC;AACzE;AAEA,OAAO,MAAMI,cAAc,GAAiB;EAC1CC,UAAU,EAAE/B,QAAQ;EACpBgC,WAAW,EAAE,KAAK;EAClBC,UAAU,EAAE9B;CACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}