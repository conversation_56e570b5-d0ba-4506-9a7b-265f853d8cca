{"ast": null, "code": "/**\n * @license\n * Copyright 2023 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nexport const json = [{\n  'tfOpName': 'LowerBound',\n  'category': 'evaluation',\n  'inputs': [{\n    'start': 0,\n    'name': 'sortedSequence',\n    'type': 'tensor'\n  }, {\n    'start': 1,\n    'name': 'values',\n    'type': 'tensor'\n  }]\n}, {\n  'tfOpName': 'TopKV2',\n  'category': 'evaluation',\n  'inputs': [{\n    'start': 0,\n    'name': 'x',\n    'type': 'tensor'\n  }, {\n    'start': 1,\n    'name': 'k',\n    'type': 'number'\n  }],\n  'attrs': [{\n    'tfName': 'sorted',\n    'name': 'sorted',\n    'type': 'bool'\n  }]\n}, {\n  'tfOpName': 'UpperBound',\n  'category': 'evaluation',\n  'inputs': [{\n    'start': 0,\n    'name': 'sortedSequence',\n    'type': 'tensor'\n  }, {\n    'start': 1,\n    'name': 'values',\n    'type': 'tensor'\n  }]\n}, {\n  'tfOpName': 'Unique',\n  'category': 'evaluation',\n  'inputs': [{\n    'start': 0,\n    'name': 'x',\n    'type': 'tensor'\n  }]\n}, {\n  'tfOpName': 'UniqueV2',\n  'category': 'evaluation',\n  'inputs': [{\n    'start': 0,\n    'name': 'x',\n    'type': 'tensor'\n  }, {\n    'start': 1,\n    'name': 'axis',\n    'type': 'number'\n  }]\n}];", "map": {"version": 3, "names": ["json"], "sources": ["C:\\tfjs-converter\\src\\operations\\op_list\\evaluation.ts"], "sourcesContent": ["\n/**\n * @license\n * Copyright 2023 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {OpMapper} from '../types';\n\nexport const json: OpMapper[] = [\n  {\n    'tfOpName': 'LowerBound',\n    'category': 'evaluation',\n    'inputs': [\n      {\n        'start': 0,\n        'name': 'sortedSequence',\n        'type': 'tensor'\n      },\n      {\n        'start': 1,\n        'name': 'values',\n        'type': 'tensor'\n      }\n    ]\n  },\n  {\n    'tfOpName': 'TopKV2',\n    'category': 'evaluation',\n    'inputs': [\n      {\n        'start': 0,\n        'name': 'x',\n        'type': 'tensor'\n      },\n      {\n        'start': 1,\n        'name': 'k',\n        'type': 'number'\n      }\n    ],\n    'attrs': [\n      {\n        'tfName': 'sorted',\n        'name': 'sorted',\n        'type': 'bool'\n      }\n    ]\n  },\n  {\n    'tfOpName': 'UpperBound',\n    'category': 'evaluation',\n    'inputs': [\n      {\n        'start': 0,\n        'name': 'sortedSequence',\n        'type': 'tensor'\n      },\n      {\n        'start': 1,\n        'name': 'values',\n        'type': 'tensor'\n      }\n    ]\n  },\n  {\n    'tfOpName': 'Unique',\n    'category': 'evaluation',\n    'inputs': [\n      {\n        'start': 0,\n        'name': 'x',\n        'type': 'tensor'\n      }\n    ]\n  },\n  {\n    'tfOpName': 'UniqueV2',\n    'category': 'evaluation',\n    'inputs': [\n      {\n        'start': 0,\n        'name': 'x',\n        'type': 'tensor'\n      },\n      {\n        'start': 1,\n        'name': 'axis',\n        'type': 'number'\n      }\n    ]\n  }\n]\n;\n"], "mappings": "AACA;;;;;;;;;;;;;;;;AAmBA,OAAO,MAAMA,IAAI,GAAe,CAC9B;EACE,UAAU,EAAE,YAAY;EACxB,UAAU,EAAE,YAAY;EACxB,QAAQ,EAAE,CACR;IACE,OAAO,EAAE,CAAC;IACV,MAAM,EAAE,gBAAgB;IACxB,MAAM,EAAE;GACT,EACD;IACE,OAAO,EAAE,CAAC;IACV,MAAM,EAAE,QAAQ;IAChB,MAAM,EAAE;GACT;CAEJ,EACD;EACE,UAAU,EAAE,QAAQ;EACpB,UAAU,EAAE,YAAY;EACxB,QAAQ,EAAE,CACR;IACE,OAAO,EAAE,CAAC;IACV,MAAM,EAAE,GAAG;IACX,MAAM,EAAE;GACT,EACD;IACE,OAAO,EAAE,CAAC;IACV,MAAM,EAAE,GAAG;IACX,MAAM,EAAE;GACT,CACF;EACD,OAAO,EAAE,CACP;IACE,QAAQ,EAAE,QAAQ;IAClB,MAAM,EAAE,QAAQ;IAChB,MAAM,EAAE;GACT;CAEJ,EACD;EACE,UAAU,EAAE,YAAY;EACxB,UAAU,EAAE,YAAY;EACxB,QAAQ,EAAE,CACR;IACE,OAAO,EAAE,CAAC;IACV,MAAM,EAAE,gBAAgB;IACxB,MAAM,EAAE;GACT,EACD;IACE,OAAO,EAAE,CAAC;IACV,MAAM,EAAE,QAAQ;IAChB,MAAM,EAAE;GACT;CAEJ,EACD;EACE,UAAU,EAAE,QAAQ;EACpB,UAAU,EAAE,YAAY;EACxB,QAAQ,EAAE,CACR;IACE,OAAO,EAAE,CAAC;IACV,MAAM,EAAE,GAAG;IACX,MAAM,EAAE;GACT;CAEJ,EACD;EACE,UAAU,EAAE,UAAU;EACtB,UAAU,EAAE,YAAY;EACxB,QAAQ,EAAE,CACR;IACE,OAAO,EAAE,CAAC;IACV,MAAM,EAAE,GAAG;IACX,MAAM,EAAE;GACT,EACD;IACE,OAAO,EAAE,CAAC;IACV,MAAM,EAAE,MAAM;IACd,MAAM,EAAE;GACT;CAEJ,CACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}