{"ast": null, "code": "import restArguments from './restArguments.js';\nimport unzip from './unzip.js';\n\n// Zip together multiple lists into a single array -- elements that share\n// an index go together.\nexport default restArguments(unzip);", "map": {"version": 3, "names": ["restArguments", "unzip"], "sources": ["C:/tmsft/node_modules/underscore/modules/zip.js"], "sourcesContent": ["import restArguments from './restArguments.js';\nimport unzip from './unzip.js';\n\n// Zip together multiple lists into a single array -- elements that share\n// an index go together.\nexport default restArguments(unzip);\n"], "mappings": "AAAA,OAAOA,aAAa,MAAM,oBAAoB;AAC9C,OAAOC,KAAK,MAAM,YAAY;;AAE9B;AACA;AACA,eAAeD,aAAa,CAACC,KAAK,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}