{"ast": null, "code": "/**\n * @license\n * Copyright 2018 Google LLC\n *\n * Use of this source code is governed by an MIT-style\n * license that can be found in the LICENSE file or at\n * https://opensource.org/licenses/MIT.\n * =============================================================================\n */\nimport { argMax, clone, dispose, mul, reshape, tensor1d, tidy } from '@tensorflow/tfjs-core';\nfunction standardizeSampleOrClassWeights(xWeight, outputNames, weightType) {\n  const numOutputs = outputNames.length;\n  if (xWeight == null || Array.isArray(xWeight) && xWeight.length === 0) {\n    return outputNames.map(name => null);\n  }\n  if (numOutputs === 1) {\n    if (Array.isArray(xWeight) && xWeight.length === 1) {\n      return xWeight;\n    } else if (typeof xWeight === 'object' && outputNames[0] in xWeight) {\n      return [xWeight[outputNames[0]]];\n    } else {\n      return [xWeight];\n    }\n  }\n  if (Array.isArray(xWeight)) {\n    if (xWeight.length !== numOutputs) {\n      throw new Error(`Provided ${weightType} is an array of ${xWeight.length} ` + `element(s), but the model has ${numOutputs} outputs. ` + `Make sure a set of weights is provided for each model output.`);\n    }\n    return xWeight;\n  } else if (typeof xWeight === 'object' && Object.keys(xWeight).length > 0 && typeof xWeight[Object.keys(xWeight)[0]] === 'object') {\n    const output = [];\n    outputNames.forEach(outputName => {\n      if (outputName in xWeight) {\n        output.push(xWeight[outputName]);\n      } else {\n        output.push(null);\n      }\n    });\n    return output;\n  } else {\n    throw new Error(`The model has multiple (${numOutputs}) outputs, ` + `so ${weightType} must be either an array with ` + `${numOutputs} elements or an object with ${outputNames} keys. ` + `Provided ${weightType} not understood: ${JSON.stringify(xWeight)}`);\n  }\n}\n/**\n * Standardize class weighting objects.\n *\n * This function takes a single class-weighting object, an array of them,\n * or a map from output name to class-weighting object. It compares it to the\n * output name(s) of the model, base on which it outputs an array of\n * class-weighting objects of which the length matches the number of outputs.\n *\n * @param classWeight Input class-weighting object(s).\n * @param outputNames All output name(s) of the model.\n * @return An array of class-weighting objects. The length of the array matches\n *   the model's number of outputs.\n */\nexport function standardizeClassWeights(classWeight, outputNames) {\n  return standardizeSampleOrClassWeights(classWeight, outputNames, 'classWeight');\n}\nexport function standardizeSampleWeights(classWeight, outputNames) {\n  return standardizeSampleOrClassWeights(classWeight, outputNames, 'sampleWeight');\n}\n/**\n * Standardize by-sample and/or by-class weights for training.\n *\n * Note that this function operates on one model output at a time. For a model\n * with multiple outputs, you must call this function multiple times.\n *\n * @param y The target tensor that the by-sample and/or by-class weight is for.\n *     The values of y are assumed to encode the classes, either directly\n *     as an integer index, or as one-hot encoding.\n * @param sampleWeight By-sample weights.\n * @param classWeight By-class weights: an object mapping class indices\n *     (integers) to a weight (float) to apply to the model's loss for the\n *     samples from this class during training. This can be useful to tell the\n *     model to \"pay more attention\" to samples from an under-represented class.\n * @param sampleWeightMode The mode for the sample weights.\n * @return A Promise of weight tensor, of which the size of the first dimension\n *     matches that of `y`.\n */\nexport async function standardizeWeights(y, sampleWeight, classWeight, sampleWeightMode) {\n  if (sampleWeight != null || sampleWeightMode != null) {\n    // TODO(cais): Once 'temporal' mode is implemented, document it in the doc\n    // string.\n    throw new Error('Support sampleWeight is not implemented yet');\n  }\n  if (classWeight != null) {\n    // Apply class weights per sample.\n    const yClasses = tidy(() => {\n      if (y.shape.length === 1) {\n        // Assume class indices.\n        return clone(y);\n      } else if (y.shape.length === 2) {\n        if (y.shape[1] > 1) {\n          // Assume one-hot encoding of classes.\n          const axis = 1;\n          return argMax(y, axis);\n        } else if (y.shape[1] === 1) {\n          // Class index.\n          return reshape(y, [y.shape[0]]);\n        } else {\n          throw new Error(`Encountered unexpected last-dimension size (${y.shape[1]}) ` + `during handling of class weights. The size is expected to be ` + `>= 1.`);\n        }\n      } else {\n        throw new Error(`Unexpected rank of target (y) tensor (${y.rank}) during ` + `handling of class weights. The rank is expected to be 1 or 2.`);\n      }\n    });\n    const yClassIndices = Array.from(await yClasses.data());\n    dispose(yClasses);\n    const classSampleWeight = [];\n    yClassIndices.forEach(classIndex => {\n      if (classWeight[classIndex] == null) {\n        throw new Error(`classWeight must contain all classes in the training data. ` + `The class ${classIndex} exists in the data but not in ` + `classWeight`);\n      } else {\n        classSampleWeight.push(classWeight[classIndex]);\n      }\n    });\n    return tensor1d(classSampleWeight, 'float32');\n  } else {\n    return null;\n  }\n}\n/**\n * Apply per-sample weights on the loss values from a number of samples.\n *\n * @param losses Loss tensor of shape `[batchSize]`.\n * @param sampleWeights Per-sample weight tensor of shape `[batchSize]`.\n * @returns Tensor of the same shape as`losses`.\n */\nexport function computeWeightedLoss(losses, sampleWeights) {\n  return mul(losses, sampleWeights);\n}", "map": {"version": 3, "names": ["argMax", "clone", "dispose", "mul", "reshape", "tensor1d", "tidy", "standardizeSampleOrClassWeights", "xWeight", "outputNames", "weightType", "numOutputs", "length", "Array", "isArray", "map", "name", "Error", "Object", "keys", "output", "for<PERSON>ach", "outputName", "push", "JSON", "stringify", "standardizeClassWeights", "classWeight", "standardizeSampleWeights", "standardizeWeights", "y", "sampleWeight", "sampleWeightMode", "yClasses", "shape", "axis", "rank", "yClassIndices", "from", "data", "classSampleWeight", "classIndex", "computeWeightedLoss", "losses", "sampleWeights"], "sources": ["C:\\tfjs-layers\\src\\engine\\training_utils.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2018 Google LLC\n *\n * Use of this source code is governed by an MIT-style\n * license that can be found in the LICENSE file or at\n * https://opensource.org/licenses/MIT.\n * =============================================================================\n */\n\nimport {argMax, clone, dispose, mul, reshape, Tensor, Tensor1D, tensor1d, tidy} from '@tensorflow/tfjs-core';\n\n/**\n * For multi-class classification problems, this object is designed to store a\n * mapping from class index to the \"weight\" of the class, where higher weighted\n * classes have larger impact on loss, accuracy, and other metrics.\n *\n * This is useful for cases in which you want the model to \"pay more attention\"\n * to examples from an under-represented class, e.g., in unbalanced datasets.\n */\nexport type ClassWeight = {\n  [classIndex: number]: number\n};\n\n/**\n * Class weighting for a model with multiple outputs.\n *\n * This object maps each output name to a class-weighting object.\n */\nexport type ClassWeightMap = {\n  [outputName: string]: ClassWeight\n};\n\nfunction standardizeSampleOrClassWeights(\n    xWeight: ClassWeight|ClassWeight[]|ClassWeightMap, outputNames: string[],\n    weightType: 'sampleWeight'|'classWeight'): ClassWeight[] {\n  const numOutputs = outputNames.length;\n  if (xWeight == null || (Array.isArray(xWeight) && xWeight.length === 0)) {\n    return outputNames.map(name => null);\n  }\n  if (numOutputs === 1) {\n    if (Array.isArray(xWeight) && xWeight.length === 1) {\n      return xWeight;\n    } else if (typeof xWeight === 'object' && outputNames[0] in xWeight) {\n      return [(xWeight as ClassWeightMap)[outputNames[0]]];\n    } else {\n      return [xWeight as ClassWeight];\n    }\n  }\n  if (Array.isArray(xWeight)) {\n    if (xWeight.length !== numOutputs) {\n      throw new Error(\n          `Provided ${weightType} is an array of ${xWeight.length} ` +\n          `element(s), but the model has ${numOutputs} outputs. ` +\n          `Make sure a set of weights is provided for each model output.`);\n    }\n    return xWeight;\n  } else if (\n      typeof xWeight === 'object' && Object.keys(xWeight).length > 0 &&\n      typeof (xWeight as ClassWeightMap)[Object.keys(xWeight)[0]] ===\n          'object') {\n    const output: ClassWeight[] = [];\n    outputNames.forEach(outputName => {\n      if (outputName in xWeight) {\n        output.push((xWeight as ClassWeightMap)[outputName]);\n      } else {\n        output.push(null);\n      }\n    });\n    return output;\n  } else {\n    throw new Error(\n        `The model has multiple (${numOutputs}) outputs, ` +\n        `so ${weightType} must be either an array with ` +\n        `${numOutputs} elements or an object with ${outputNames} keys. ` +\n        `Provided ${weightType} not understood: ${JSON.stringify(xWeight)}`);\n  }\n}\n\n/**\n * Standardize class weighting objects.\n *\n * This function takes a single class-weighting object, an array of them,\n * or a map from output name to class-weighting object. It compares it to the\n * output name(s) of the model, base on which it outputs an array of\n * class-weighting objects of which the length matches the number of outputs.\n *\n * @param classWeight Input class-weighting object(s).\n * @param outputNames All output name(s) of the model.\n * @return An array of class-weighting objects. The length of the array matches\n *   the model's number of outputs.\n */\nexport function standardizeClassWeights(\n    classWeight: ClassWeight|ClassWeight[]|ClassWeightMap,\n    outputNames: string[]): ClassWeight[] {\n  return standardizeSampleOrClassWeights(\n      classWeight, outputNames, 'classWeight');\n}\n\nexport function standardizeSampleWeights(\n    classWeight: ClassWeight|ClassWeight[]|ClassWeightMap,\n    outputNames: string[]): ClassWeight[] {\n  return standardizeSampleOrClassWeights(\n      classWeight, outputNames, 'sampleWeight');\n}\n\n/**\n * Standardize by-sample and/or by-class weights for training.\n *\n * Note that this function operates on one model output at a time. For a model\n * with multiple outputs, you must call this function multiple times.\n *\n * @param y The target tensor that the by-sample and/or by-class weight is for.\n *     The values of y are assumed to encode the classes, either directly\n *     as an integer index, or as one-hot encoding.\n * @param sampleWeight By-sample weights.\n * @param classWeight By-class weights: an object mapping class indices\n *     (integers) to a weight (float) to apply to the model's loss for the\n *     samples from this class during training. This can be useful to tell the\n *     model to \"pay more attention\" to samples from an under-represented class.\n * @param sampleWeightMode The mode for the sample weights.\n * @return A Promise of weight tensor, of which the size of the first dimension\n *     matches that of `y`.\n */\nexport async function standardizeWeights(\n    y: Tensor, sampleWeight?: Tensor, classWeight?: ClassWeight,\n    sampleWeightMode?: 'temporal'): Promise<Tensor> {\n  if (sampleWeight != null || sampleWeightMode != null) {\n    // TODO(cais): Once 'temporal' mode is implemented, document it in the doc\n    // string.\n    throw new Error('Support sampleWeight is not implemented yet');\n  }\n\n  if (classWeight != null) {\n    // Apply class weights per sample.\n    const yClasses: Tensor1D = tidy(() => {\n      if (y.shape.length === 1) {\n        // Assume class indices.\n        return clone(y) as Tensor1D;\n      } else if (y.shape.length === 2) {\n        if (y.shape[1] > 1) {\n          // Assume one-hot encoding of classes.\n          const axis = 1;\n          return argMax(y, axis);\n        } else if (y.shape[1] === 1) {\n          // Class index.\n          return reshape(y, [y.shape[0]]);\n        } else {\n          throw new Error(\n              `Encountered unexpected last-dimension size (${y.shape[1]}) ` +\n              `during handling of class weights. The size is expected to be ` +\n              `>= 1.`);\n        }\n      } else {\n        throw new Error(\n            `Unexpected rank of target (y) tensor (${y.rank}) during ` +\n            `handling of class weights. The rank is expected to be 1 or 2.`);\n      }\n    });\n\n    const yClassIndices = Array.from(await yClasses.data());\n    dispose(yClasses);\n    const classSampleWeight: number[] = [];\n    yClassIndices.forEach(classIndex => {\n      if (classWeight[classIndex] == null) {\n        throw new Error(\n            `classWeight must contain all classes in the training data. ` +\n            `The class ${classIndex} exists in the data but not in ` +\n            `classWeight`);\n      } else {\n        classSampleWeight.push(classWeight[classIndex]);\n      }\n    });\n\n    return tensor1d(classSampleWeight, 'float32');\n  } else {\n    return null;\n  }\n}\n\n/**\n * Apply per-sample weights on the loss values from a number of samples.\n *\n * @param losses Loss tensor of shape `[batchSize]`.\n * @param sampleWeights Per-sample weight tensor of shape `[batchSize]`.\n * @returns Tensor of the same shape as`losses`.\n */\nexport function computeWeightedLoss(losses: Tensor, sampleWeights: Tensor) {\n  return mul(losses, sampleWeights);\n}\n"], "mappings": "AAAA;;;;;;;;;AAUA,SAAQA,MAAM,EAAEC,KAAK,EAAEC,OAAO,EAAEC,GAAG,EAAEC,OAAO,EAAoBC,QAAQ,EAAEC,IAAI,QAAO,uBAAuB;AAuB5G,SAASC,+BAA+BA,CACpCC,OAAiD,EAAEC,WAAqB,EACxEC,UAAwC;EAC1C,MAAMC,UAAU,GAAGF,WAAW,CAACG,MAAM;EACrC,IAAIJ,OAAO,IAAI,IAAI,IAAKK,KAAK,CAACC,OAAO,CAACN,OAAO,CAAC,IAAIA,OAAO,CAACI,MAAM,KAAK,CAAE,EAAE;IACvE,OAAOH,WAAW,CAACM,GAAG,CAACC,IAAI,IAAI,IAAI,CAAC;;EAEtC,IAAIL,UAAU,KAAK,CAAC,EAAE;IACpB,IAAIE,KAAK,CAACC,OAAO,CAACN,OAAO,CAAC,IAAIA,OAAO,CAACI,MAAM,KAAK,CAAC,EAAE;MAClD,OAAOJ,OAAO;KACf,MAAM,IAAI,OAAOA,OAAO,KAAK,QAAQ,IAAIC,WAAW,CAAC,CAAC,CAAC,IAAID,OAAO,EAAE;MACnE,OAAO,CAAEA,OAA0B,CAACC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC;KACrD,MAAM;MACL,OAAO,CAACD,OAAsB,CAAC;;;EAGnC,IAAIK,KAAK,CAACC,OAAO,CAACN,OAAO,CAAC,EAAE;IAC1B,IAAIA,OAAO,CAACI,MAAM,KAAKD,UAAU,EAAE;MACjC,MAAM,IAAIM,KAAK,CACX,YAAYP,UAAU,mBAAmBF,OAAO,CAACI,MAAM,GAAG,GAC1D,iCAAiCD,UAAU,YAAY,GACvD,+DAA+D,CAAC;;IAEtE,OAAOH,OAAO;GACf,MAAM,IACH,OAAOA,OAAO,KAAK,QAAQ,IAAIU,MAAM,CAACC,IAAI,CAACX,OAAO,CAAC,CAACI,MAAM,GAAG,CAAC,IAC9D,OAAQJ,OAA0B,CAACU,MAAM,CAACC,IAAI,CAACX,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,KACvD,QAAQ,EAAE;IAChB,MAAMY,MAAM,GAAkB,EAAE;IAChCX,WAAW,CAACY,OAAO,CAACC,UAAU,IAAG;MAC/B,IAAIA,UAAU,IAAId,OAAO,EAAE;QACzBY,MAAM,CAACG,IAAI,CAAEf,OAA0B,CAACc,UAAU,CAAC,CAAC;OACrD,MAAM;QACLF,MAAM,CAACG,IAAI,CAAC,IAAI,CAAC;;IAErB,CAAC,CAAC;IACF,OAAOH,MAAM;GACd,MAAM;IACL,MAAM,IAAIH,KAAK,CACX,2BAA2BN,UAAU,aAAa,GAClD,MAAMD,UAAU,gCAAgC,GAChD,GAAGC,UAAU,+BAA+BF,WAAW,SAAS,GAChE,YAAYC,UAAU,oBAAoBc,IAAI,CAACC,SAAS,CAACjB,OAAO,CAAC,EAAE,CAAC;;AAE5E;AAEA;;;;;;;;;;;;;AAaA,OAAM,SAAUkB,uBAAuBA,CACnCC,WAAqD,EACrDlB,WAAqB;EACvB,OAAOF,+BAA+B,CAClCoB,WAAW,EAAElB,WAAW,EAAE,aAAa,CAAC;AAC9C;AAEA,OAAM,SAAUmB,wBAAwBA,CACpCD,WAAqD,EACrDlB,WAAqB;EACvB,OAAOF,+BAA+B,CAClCoB,WAAW,EAAElB,WAAW,EAAE,cAAc,CAAC;AAC/C;AAEA;;;;;;;;;;;;;;;;;;AAkBA,OAAO,eAAeoB,kBAAkBA,CACpCC,CAAS,EAAEC,YAAqB,EAAEJ,WAAyB,EAC3DK,gBAA6B;EAC/B,IAAID,YAAY,IAAI,IAAI,IAAIC,gBAAgB,IAAI,IAAI,EAAE;IACpD;IACA;IACA,MAAM,IAAIf,KAAK,CAAC,6CAA6C,CAAC;;EAGhE,IAAIU,WAAW,IAAI,IAAI,EAAE;IACvB;IACA,MAAMM,QAAQ,GAAa3B,IAAI,CAAC,MAAK;MACnC,IAAIwB,CAAC,CAACI,KAAK,CAACtB,MAAM,KAAK,CAAC,EAAE;QACxB;QACA,OAAOX,KAAK,CAAC6B,CAAC,CAAa;OAC5B,MAAM,IAAIA,CAAC,CAACI,KAAK,CAACtB,MAAM,KAAK,CAAC,EAAE;QAC/B,IAAIkB,CAAC,CAACI,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE;UAClB;UACA,MAAMC,IAAI,GAAG,CAAC;UACd,OAAOnC,MAAM,CAAC8B,CAAC,EAAEK,IAAI,CAAC;SACvB,MAAM,IAAIL,CAAC,CAACI,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE;UAC3B;UACA,OAAO9B,OAAO,CAAC0B,CAAC,EAAE,CAACA,CAAC,CAACI,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;SAChC,MAAM;UACL,MAAM,IAAIjB,KAAK,CACX,+CAA+Ca,CAAC,CAACI,KAAK,CAAC,CAAC,CAAC,IAAI,GAC7D,+DAA+D,GAC/D,OAAO,CAAC;;OAEf,MAAM;QACL,MAAM,IAAIjB,KAAK,CACX,yCAAyCa,CAAC,CAACM,IAAI,WAAW,GAC1D,+DAA+D,CAAC;;IAExE,CAAC,CAAC;IAEF,MAAMC,aAAa,GAAGxB,KAAK,CAACyB,IAAI,CAAC,MAAML,QAAQ,CAACM,IAAI,EAAE,CAAC;IACvDrC,OAAO,CAAC+B,QAAQ,CAAC;IACjB,MAAMO,iBAAiB,GAAa,EAAE;IACtCH,aAAa,CAAChB,OAAO,CAACoB,UAAU,IAAG;MACjC,IAAId,WAAW,CAACc,UAAU,CAAC,IAAI,IAAI,EAAE;QACnC,MAAM,IAAIxB,KAAK,CACX,6DAA6D,GAC7D,aAAawB,UAAU,iCAAiC,GACxD,aAAa,CAAC;OACnB,MAAM;QACLD,iBAAiB,CAACjB,IAAI,CAACI,WAAW,CAACc,UAAU,CAAC,CAAC;;IAEnD,CAAC,CAAC;IAEF,OAAOpC,QAAQ,CAACmC,iBAAiB,EAAE,SAAS,CAAC;GAC9C,MAAM;IACL,OAAO,IAAI;;AAEf;AAEA;;;;;;;AAOA,OAAM,SAAUE,mBAAmBA,CAACC,MAAc,EAAEC,aAAqB;EACvE,OAAOzC,GAAG,CAACwC,MAAM,EAAEC,aAAa,CAAC;AACnC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}