{"ast": null, "code": "/*\nCopyright (c) 2017, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON> (Reimplemented from https://github.com/sastrawi/sastrawi)\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in\nall copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\nTHE SOFTWARE.\n*/\n\nconst BaseStemmer = require('./base_stemmer_id');\nconst stemmer = new BaseStemmer();\n\n// Dictionary\nconst dictionary = loadDictionary();\n\n// Rules\nconst SuffixRules = require('./suffix_rules');\nconst PrefixRules = require('./prefix_rules');\nconst suffixRules = SuffixRules.rules;\nconst prefixRules = PrefixRules.rules;\n\n// Removals\nlet removals = null;\n\n// Words\nlet originalWord = null;\nlet currentWord = null;\nmodule.exports = stemmer;\n\n// perform full stemming algorithm on a single word\nstemmer.stem = function (token) {\n  // Cache stemmer not yet implemented\n  // Set to lowercase\n  token = token.toLowerCase();\n\n  // Initiate everything\n  removals = [];\n  if (isPlural(token)) {\n    return stemPluralWord(token);\n  } else {\n    return stemSingularWord(token);\n  }\n};\n\n// Stem for plural word\nfunction stemPluralWord(pluralWord) {\n  let matches = pluralWord.match(/^(.*)-(.*)$/);\n  if (!matches) {\n    return pluralWord;\n  }\n  const words = [matches[1], matches[2]];\n\n  // malaikat-malaikat-nya -> malaikat malaikat-nya\n  const suffix = words[1];\n  const suffixes = ['ku', 'mu', 'nya', 'lah', 'kah', 'tah', 'pun'];\n  matches = words[0].match(/^(.*)-(.*)$/);\n  if (suffixes.indexOf(suffix) !== -1 && matches) {\n    words[0] = matches[1];\n    words[1] = matches[2] + '-' + suffix;\n  }\n\n  // berbalas-balasan -> balas\n  const rootWord1 = stemSingularWord(words[0]);\n  let rootWord2 = stemSingularWord(words[1]);\n\n  // meniru-nirukan -> tiru\n  if (!find(words[1]) && rootWord2 === words[1]) {\n    rootWord2 = stemSingularWord('me' + words[1]);\n  }\n  if (rootWord1 === rootWord2) {\n    return rootWord1;\n  } else {\n    return pluralWord;\n  }\n}\n\n// Stem for singular word\nfunction stemSingularWord(word) {\n  originalWord = word; // Save the original word for reverting later\n  currentWord = word;\n\n  // Step 1\n  if (currentWord.length > 3) {\n    // Step 2-5\n    stemmingProcess();\n  }\n\n  // Step 6\n  if (find(currentWord)) {\n    return currentWord;\n  } else {\n    return originalWord;\n  }\n}\n\n// Return true if word is in plural form ex: gelas-gelas, else false\nfunction isPlural(token) {\n  const matches = token.match(/^(.*)-(ku|mu|nya|lah|kah|tah|pun)$/);\n  if (matches) {\n    return matches[1].search('-') !== -1;\n  }\n  return token.search('-') !== -1;\n}\n\n// Find certain word in dictionary\nfunction find(word) {\n  return dictionary.has(word);\n}\nfunction loadDictionary() {\n  const fin = require('./data/kata-dasar.json');\n  return new Set(fin.filter(Boolean));\n}\n\n// Stemming from step 2-5\nfunction stemmingProcess() {\n  if (find(currentWord)) {\n    return;\n  }\n\n  // Confix Stripping\n  // Try to remove prefixes first before suffixes if the specification is met\n  if (precedenceAdjustmentSpecification(originalWord)) {\n    // Step 4, 5\n    removePrefixes();\n    if (find(currentWord)) {\n      return;\n    }\n\n    // Step 2, 3\n    removeSuffixes();\n    if (find(currentWord)) {\n      return;\n    } else {\n      // if the trial is failed, restore the original word\n      // and continue to normal rule precedence (suffix first, prefix afterwards)\n      currentWord = originalWord;\n      removals = [];\n    }\n  }\n\n  // Step 2, 3\n  removeSuffixes();\n  if (find(currentWord)) {\n    return;\n  }\n\n  // Step 4, 5\n  removePrefixes();\n  if (find(currentWord)) {\n    return;\n  }\n\n  // ECS Loop Restore Prefixes\n  loopRestorePrefixes();\n}\n\n// Remove Suffixes\nfunction removeSuffixes() {\n  for (const i in suffixRules) {\n    const resultObj = suffixRules[i](currentWord);\n\n    // Add result to variable\n    if (resultObj.removal !== undefined) {\n      removals.push(resultObj.removal);\n    }\n    currentWord = resultObj.currentWord;\n    if (find(currentWord)) {\n      return currentWord;\n    }\n  }\n}\n\n// Remove Prefixes\nfunction removePrefixes() {\n  for (let i = 0; i < 3; i++) {\n    checkPrefixRules();\n    if (find(currentWord)) {\n      return currentWord;\n    }\n  }\n}\nfunction checkPrefixRules() {\n  const removalCount = removals.length;\n  let j = 0;\n  for (j = 0; j < prefixRules.length; j++) {\n    const resultObj = prefixRules[j](currentWord);\n\n    // Add result to variable\n    if (resultObj.removal !== undefined) {\n      removals.push(resultObj.removal);\n    }\n    currentWord = resultObj.currentWord;\n    if (find(currentWord)) {\n      return currentWord;\n    }\n    if (removals.length > removalCount) {\n      return;\n    }\n  }\n}\n\n// Loop Restore Prefixes\nfunction loopRestorePrefixes() {\n  restorePrefix();\n  const reversedRemovals = removals.reverse();\n  const tempCurrentWord = currentWord;\n  for (const i in reversedRemovals) {\n    const currentRemoval = reversedRemovals[i];\n    if (!isSuffixRemovals(currentRemoval)) {\n      continue;\n    }\n    if (currentRemoval.getRemovedPart() === 'kan') {\n      currentWord = currentRemoval.getResult() + 'k';\n\n      // Step 4, 5\n      removePrefixes();\n      if (find(currentWord)) {\n        return;\n      }\n      currentWord = currentRemoval.getResult() + 'kan';\n    } else {\n      currentWord = currentRemoval.getOriginalWord();\n    }\n\n    // Step 4, 5\n    removePrefixes();\n    if (find(currentWord)) {\n      return;\n    }\n    currentWord = tempCurrentWord;\n  }\n}\nfunction isSuffixRemovals(removal) {\n  const type = removal.getAffixType();\n  if (type === 'DS' || type === 'PP' || type === 'P') {\n    return true;\n  }\n  return false;\n}\nfunction restorePrefix() {\n  for (let i = 0; i < removals.length; i++) {\n    currentWord = removals[i].getOriginalWord();\n    // break\n  }\n  for (let i = 0; i < removals.length; i++) {\n    if (removals[i].getAffixType() === 'DP') {\n      removals.splice(i, 1);\n      i--;\n    }\n  }\n}\n\n// Check if word require precedence adjustment or not\n// Adjustment means remove prefix then suffix instead of remove suffix then prefix\nfunction precedenceAdjustmentSpecification(word) {\n  const regexRules = [/^be(.*)lah$/, /^be(.*)an$/, /^me(.*)i$/, /^di(.*)i$/, /^pe(.*)i$/, /^ter(.*)i$/];\n  for (const i in regexRules) {\n    if (word.match(regexRules[i])) {\n      return true;\n    }\n  }\n  return false;\n}\n\n// exports for tests\nstemmer.isPlural = isPlural;\nstemmer.dictionary = Array.from(dictionary);\nstemmer.a = suffixRules[0];", "map": {"version": 3, "names": ["BaseStemmer", "require", "stemmer", "dictionary", "loadDictionary", "SuffixRules", "PrefixRules", "suffixRules", "rules", "prefixRules", "removals", "originalWord", "currentWord", "module", "exports", "stem", "token", "toLowerCase", "isPlural", "stemPluralWord", "stemSingularWord", "pluralWord", "matches", "match", "words", "suffix", "suffixes", "indexOf", "rootWord1", "rootWord2", "find", "word", "length", "stemmingProcess", "search", "has", "fin", "Set", "filter", "Boolean", "precedenceAdjustmentSpecification", "removePrefixes", "removeSuffixes", "loopRestorePrefixes", "i", "resultObj", "removal", "undefined", "push", "checkPrefixRules", "removalCount", "j", "restorePrefix", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "reverse", "tempCurrentWord", "currentRemoval", "isSuffixRemovals", "getRemovedPart", "getResult", "getOriginalWord", "type", "getAffixType", "splice", "regexRules", "Array", "from", "a"], "sources": ["C:/tmsft/node_modules/natural/lib/natural/stemmers/indonesian/stemmer_id.js"], "sourcesContent": ["/*\nCopyright (c) 2017, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON> (Reimplemented from https://github.com/sastrawi/sastrawi)\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in\nall copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\nTHE SOFTWARE.\n*/\n\nconst BaseStemmer = require('./base_stemmer_id')\nconst stemmer = new BaseStemmer()\n\n// Dictionary\nconst dictionary = loadDictionary()\n\n// Rules\nconst SuffixRules = require('./suffix_rules')\nconst PrefixRules = require('./prefix_rules')\n\nconst suffixRules = SuffixRules.rules\nconst prefixRules = PrefixRules.rules\n\n// Removals\nlet removals = null\n\n// Words\nlet originalWord = null\nlet currentWord = null\n\nmodule.exports = stemmer\n\n// perform full stemming algorithm on a single word\nstemmer.stem = function (token) {\n  // Cache stemmer not yet implemented\n  // Set to lowercase\n  token = token.toLowerCase()\n\n  // Initiate everything\n  removals = []\n\n  if (isPlural(token)) {\n    return stemPluralWord(token)\n  } else {\n    return stemSingularWord(token)\n  }\n}\n\n// Stem for plural word\nfunction stemPluralWord (pluralWord) {\n  let matches = pluralWord.match(/^(.*)-(.*)$/)\n  if (!matches) {\n    return pluralWord\n  }\n  const words = [matches[1], matches[2]]\n\n  // malaikat-malaikat-nya -> malaikat malaikat-nya\n  const suffix = words[1]\n  const suffixes = ['ku', 'mu', 'nya', 'lah', 'kah', 'tah', 'pun']\n  matches = words[0].match(/^(.*)-(.*)$/)\n  if (suffixes.indexOf(suffix) !== -1 && matches) {\n    words[0] = matches[1]\n    words[1] = matches[2] + '-' + suffix\n  }\n\n  // berbalas-balasan -> balas\n  const rootWord1 = stemSingularWord(words[0])\n  let rootWord2 = stemSingularWord(words[1])\n\n  // meniru-nirukan -> tiru\n  if (!find(words[1]) && rootWord2 === words[1]) {\n    rootWord2 = stemSingularWord('me' + words[1])\n  }\n  if (rootWord1 === rootWord2) {\n    return rootWord1\n  } else {\n    return pluralWord\n  }\n}\n\n// Stem for singular word\nfunction stemSingularWord (word) {\n  originalWord = word // Save the original word for reverting later\n  currentWord = word\n\n  // Step 1\n  if (currentWord.length > 3) {\n    // Step 2-5\n    stemmingProcess()\n  }\n\n  // Step 6\n  if (find(currentWord)) {\n    return currentWord\n  } else {\n    return originalWord\n  }\n}\n\n// Return true if word is in plural form ex: gelas-gelas, else false\nfunction isPlural (token) {\n  const matches = token.match(/^(.*)-(ku|mu|nya|lah|kah|tah|pun)$/)\n  if (matches) {\n    return matches[1].search('-') !== -1\n  }\n  return token.search('-') !== -1\n}\n\n// Find certain word in dictionary\nfunction find (word) {\n  return dictionary.has(word)\n}\n\nfunction loadDictionary () {\n  const fin = require('./data/kata-dasar.json')\n  return new Set(fin.filter(Boolean))\n}\n\n// Stemming from step 2-5\nfunction stemmingProcess () {\n  if (find(currentWord)) { return }\n\n  // Confix Stripping\n  // Try to remove prefixes first before suffixes if the specification is met\n  if (precedenceAdjustmentSpecification(originalWord)) {\n    // Step 4, 5\n    removePrefixes()\n    if (find(currentWord)) { return }\n\n    // Step 2, 3\n    removeSuffixes()\n    if (find(currentWord)) {\n      return\n    } else {\n      // if the trial is failed, restore the original word\n      // and continue to normal rule precedence (suffix first, prefix afterwards)\n      currentWord = originalWord\n      removals = []\n    }\n  }\n\n  // Step 2, 3\n  removeSuffixes()\n  if (find(currentWord)) { return }\n\n  // Step 4, 5\n  removePrefixes()\n  if (find(currentWord)) { return }\n\n  // ECS Loop Restore Prefixes\n  loopRestorePrefixes()\n}\n\n// Remove Suffixes\nfunction removeSuffixes () {\n  for (const i in suffixRules) {\n    const resultObj = suffixRules[i](currentWord)\n\n    // Add result to variable\n    if (resultObj.removal !== undefined) {\n      removals.push(resultObj.removal)\n    }\n    currentWord = resultObj.currentWord\n\n    if (find(currentWord)) { return currentWord }\n  }\n}\n\n// Remove Prefixes\nfunction removePrefixes () {\n  for (let i = 0; i < 3; i++) {\n    checkPrefixRules()\n    if (find(currentWord)) {\n      return currentWord\n    }\n  }\n}\n\nfunction checkPrefixRules () {\n  const removalCount = removals.length\n  let j = 0\n  for (j = 0; j < prefixRules.length; j++) {\n    const resultObj = prefixRules[j](currentWord)\n\n    // Add result to variable\n    if (resultObj.removal !== undefined) {\n      removals.push(resultObj.removal)\n    }\n    currentWord = resultObj.currentWord\n\n    if (find(currentWord)) {\n      return currentWord\n    }\n    if (removals.length > removalCount) {\n      return\n    }\n  }\n}\n\n// Loop Restore Prefixes\nfunction loopRestorePrefixes () {\n  restorePrefix()\n\n  const reversedRemovals = removals.reverse()\n  const tempCurrentWord = currentWord\n\n  for (const i in reversedRemovals) {\n    const currentRemoval = reversedRemovals[i]\n\n    if (!isSuffixRemovals(currentRemoval)) {\n      continue\n    }\n\n    if (currentRemoval.getRemovedPart() === 'kan') {\n      currentWord = currentRemoval.getResult() + 'k'\n\n      // Step 4, 5\n      removePrefixes()\n      if (find(currentWord)) {\n        return\n      }\n      currentWord = currentRemoval.getResult() + 'kan'\n    } else {\n      currentWord = currentRemoval.getOriginalWord()\n    }\n\n    // Step 4, 5\n    removePrefixes()\n    if (find(currentWord)) {\n      return\n    }\n\n    currentWord = tempCurrentWord\n  }\n}\n\nfunction isSuffixRemovals (removal) {\n  const type = removal.getAffixType()\n  if (type === 'DS' || type === 'PP' || type === 'P') {\n    return true\n  }\n  return false\n}\n\nfunction restorePrefix () {\n  for (let i = 0; i < removals.length; i++) {\n    currentWord = removals[i].getOriginalWord()\n    // break\n  }\n\n  for (let i = 0; i < removals.length; i++) {\n    if (removals[i].getAffixType() === 'DP') {\n      removals.splice(i, 1)\n      i--\n    }\n  }\n}\n\n// Check if word require precedence adjustment or not\n// Adjustment means remove prefix then suffix instead of remove suffix then prefix\nfunction precedenceAdjustmentSpecification (word) {\n  const regexRules = [\n    /^be(.*)lah$/,\n    /^be(.*)an$/,\n    /^me(.*)i$/,\n    /^di(.*)i$/,\n    /^pe(.*)i$/,\n    /^ter(.*)i$/\n  ]\n\n  for (const i in regexRules) {\n    if (word.match(regexRules[i])) {\n      return true\n    }\n  }\n  return false\n}\n\n// exports for tests\nstemmer.isPlural = isPlural\nstemmer.dictionary = Array.from(dictionary)\nstemmer.a = suffixRules[0]\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,MAAMA,WAAW,GAAGC,OAAO,CAAC,mBAAmB,CAAC;AAChD,MAAMC,OAAO,GAAG,IAAIF,WAAW,CAAC,CAAC;;AAEjC;AACA,MAAMG,UAAU,GAAGC,cAAc,CAAC,CAAC;;AAEnC;AACA,MAAMC,WAAW,GAAGJ,OAAO,CAAC,gBAAgB,CAAC;AAC7C,MAAMK,WAAW,GAAGL,OAAO,CAAC,gBAAgB,CAAC;AAE7C,MAAMM,WAAW,GAAGF,WAAW,CAACG,KAAK;AACrC,MAAMC,WAAW,GAAGH,WAAW,CAACE,KAAK;;AAErC;AACA,IAAIE,QAAQ,GAAG,IAAI;;AAEnB;AACA,IAAIC,YAAY,GAAG,IAAI;AACvB,IAAIC,WAAW,GAAG,IAAI;AAEtBC,MAAM,CAACC,OAAO,GAAGZ,OAAO;;AAExB;AACAA,OAAO,CAACa,IAAI,GAAG,UAAUC,KAAK,EAAE;EAC9B;EACA;EACAA,KAAK,GAAGA,KAAK,CAACC,WAAW,CAAC,CAAC;;EAE3B;EACAP,QAAQ,GAAG,EAAE;EAEb,IAAIQ,QAAQ,CAACF,KAAK,CAAC,EAAE;IACnB,OAAOG,cAAc,CAACH,KAAK,CAAC;EAC9B,CAAC,MAAM;IACL,OAAOI,gBAAgB,CAACJ,KAAK,CAAC;EAChC;AACF,CAAC;;AAED;AACA,SAASG,cAAcA,CAAEE,UAAU,EAAE;EACnC,IAAIC,OAAO,GAAGD,UAAU,CAACE,KAAK,CAAC,aAAa,CAAC;EAC7C,IAAI,CAACD,OAAO,EAAE;IACZ,OAAOD,UAAU;EACnB;EACA,MAAMG,KAAK,GAAG,CAACF,OAAO,CAAC,CAAC,CAAC,EAAEA,OAAO,CAAC,CAAC,CAAC,CAAC;;EAEtC;EACA,MAAMG,MAAM,GAAGD,KAAK,CAAC,CAAC,CAAC;EACvB,MAAME,QAAQ,GAAG,CAAC,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;EAChEJ,OAAO,GAAGE,KAAK,CAAC,CAAC,CAAC,CAACD,KAAK,CAAC,aAAa,CAAC;EACvC,IAAIG,QAAQ,CAACC,OAAO,CAACF,MAAM,CAAC,KAAK,CAAC,CAAC,IAAIH,OAAO,EAAE;IAC9CE,KAAK,CAAC,CAAC,CAAC,GAAGF,OAAO,CAAC,CAAC,CAAC;IACrBE,KAAK,CAAC,CAAC,CAAC,GAAGF,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG,GAAGG,MAAM;EACtC;;EAEA;EACA,MAAMG,SAAS,GAAGR,gBAAgB,CAACI,KAAK,CAAC,CAAC,CAAC,CAAC;EAC5C,IAAIK,SAAS,GAAGT,gBAAgB,CAACI,KAAK,CAAC,CAAC,CAAC,CAAC;;EAE1C;EACA,IAAI,CAACM,IAAI,CAACN,KAAK,CAAC,CAAC,CAAC,CAAC,IAAIK,SAAS,KAAKL,KAAK,CAAC,CAAC,CAAC,EAAE;IAC7CK,SAAS,GAAGT,gBAAgB,CAAC,IAAI,GAAGI,KAAK,CAAC,CAAC,CAAC,CAAC;EAC/C;EACA,IAAII,SAAS,KAAKC,SAAS,EAAE;IAC3B,OAAOD,SAAS;EAClB,CAAC,MAAM;IACL,OAAOP,UAAU;EACnB;AACF;;AAEA;AACA,SAASD,gBAAgBA,CAAEW,IAAI,EAAE;EAC/BpB,YAAY,GAAGoB,IAAI,EAAC;EACpBnB,WAAW,GAAGmB,IAAI;;EAElB;EACA,IAAInB,WAAW,CAACoB,MAAM,GAAG,CAAC,EAAE;IAC1B;IACAC,eAAe,CAAC,CAAC;EACnB;;EAEA;EACA,IAAIH,IAAI,CAAClB,WAAW,CAAC,EAAE;IACrB,OAAOA,WAAW;EACpB,CAAC,MAAM;IACL,OAAOD,YAAY;EACrB;AACF;;AAEA;AACA,SAASO,QAAQA,CAAEF,KAAK,EAAE;EACxB,MAAMM,OAAO,GAAGN,KAAK,CAACO,KAAK,CAAC,oCAAoC,CAAC;EACjE,IAAID,OAAO,EAAE;IACX,OAAOA,OAAO,CAAC,CAAC,CAAC,CAACY,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;EACtC;EACA,OAAOlB,KAAK,CAACkB,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;AACjC;;AAEA;AACA,SAASJ,IAAIA,CAAEC,IAAI,EAAE;EACnB,OAAO5B,UAAU,CAACgC,GAAG,CAACJ,IAAI,CAAC;AAC7B;AAEA,SAAS3B,cAAcA,CAAA,EAAI;EACzB,MAAMgC,GAAG,GAAGnC,OAAO,CAAC,wBAAwB,CAAC;EAC7C,OAAO,IAAIoC,GAAG,CAACD,GAAG,CAACE,MAAM,CAACC,OAAO,CAAC,CAAC;AACrC;;AAEA;AACA,SAASN,eAAeA,CAAA,EAAI;EAC1B,IAAIH,IAAI,CAAClB,WAAW,CAAC,EAAE;IAAE;EAAO;;EAEhC;EACA;EACA,IAAI4B,iCAAiC,CAAC7B,YAAY,CAAC,EAAE;IACnD;IACA8B,cAAc,CAAC,CAAC;IAChB,IAAIX,IAAI,CAAClB,WAAW,CAAC,EAAE;MAAE;IAAO;;IAEhC;IACA8B,cAAc,CAAC,CAAC;IAChB,IAAIZ,IAAI,CAAClB,WAAW,CAAC,EAAE;MACrB;IACF,CAAC,MAAM;MACL;MACA;MACAA,WAAW,GAAGD,YAAY;MAC1BD,QAAQ,GAAG,EAAE;IACf;EACF;;EAEA;EACAgC,cAAc,CAAC,CAAC;EAChB,IAAIZ,IAAI,CAAClB,WAAW,CAAC,EAAE;IAAE;EAAO;;EAEhC;EACA6B,cAAc,CAAC,CAAC;EAChB,IAAIX,IAAI,CAAClB,WAAW,CAAC,EAAE;IAAE;EAAO;;EAEhC;EACA+B,mBAAmB,CAAC,CAAC;AACvB;;AAEA;AACA,SAASD,cAAcA,CAAA,EAAI;EACzB,KAAK,MAAME,CAAC,IAAIrC,WAAW,EAAE;IAC3B,MAAMsC,SAAS,GAAGtC,WAAW,CAACqC,CAAC,CAAC,CAAChC,WAAW,CAAC;;IAE7C;IACA,IAAIiC,SAAS,CAACC,OAAO,KAAKC,SAAS,EAAE;MACnCrC,QAAQ,CAACsC,IAAI,CAACH,SAAS,CAACC,OAAO,CAAC;IAClC;IACAlC,WAAW,GAAGiC,SAAS,CAACjC,WAAW;IAEnC,IAAIkB,IAAI,CAAClB,WAAW,CAAC,EAAE;MAAE,OAAOA,WAAW;IAAC;EAC9C;AACF;;AAEA;AACA,SAAS6B,cAAcA,CAAA,EAAI;EACzB,KAAK,IAAIG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAE,EAAE;IAC1BK,gBAAgB,CAAC,CAAC;IAClB,IAAInB,IAAI,CAAClB,WAAW,CAAC,EAAE;MACrB,OAAOA,WAAW;IACpB;EACF;AACF;AAEA,SAASqC,gBAAgBA,CAAA,EAAI;EAC3B,MAAMC,YAAY,GAAGxC,QAAQ,CAACsB,MAAM;EACpC,IAAImB,CAAC,GAAG,CAAC;EACT,KAAKA,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG1C,WAAW,CAACuB,MAAM,EAAEmB,CAAC,EAAE,EAAE;IACvC,MAAMN,SAAS,GAAGpC,WAAW,CAAC0C,CAAC,CAAC,CAACvC,WAAW,CAAC;;IAE7C;IACA,IAAIiC,SAAS,CAACC,OAAO,KAAKC,SAAS,EAAE;MACnCrC,QAAQ,CAACsC,IAAI,CAACH,SAAS,CAACC,OAAO,CAAC;IAClC;IACAlC,WAAW,GAAGiC,SAAS,CAACjC,WAAW;IAEnC,IAAIkB,IAAI,CAAClB,WAAW,CAAC,EAAE;MACrB,OAAOA,WAAW;IACpB;IACA,IAAIF,QAAQ,CAACsB,MAAM,GAAGkB,YAAY,EAAE;MAClC;IACF;EACF;AACF;;AAEA;AACA,SAASP,mBAAmBA,CAAA,EAAI;EAC9BS,aAAa,CAAC,CAAC;EAEf,MAAMC,gBAAgB,GAAG3C,QAAQ,CAAC4C,OAAO,CAAC,CAAC;EAC3C,MAAMC,eAAe,GAAG3C,WAAW;EAEnC,KAAK,MAAMgC,CAAC,IAAIS,gBAAgB,EAAE;IAChC,MAAMG,cAAc,GAAGH,gBAAgB,CAACT,CAAC,CAAC;IAE1C,IAAI,CAACa,gBAAgB,CAACD,cAAc,CAAC,EAAE;MACrC;IACF;IAEA,IAAIA,cAAc,CAACE,cAAc,CAAC,CAAC,KAAK,KAAK,EAAE;MAC7C9C,WAAW,GAAG4C,cAAc,CAACG,SAAS,CAAC,CAAC,GAAG,GAAG;;MAE9C;MACAlB,cAAc,CAAC,CAAC;MAChB,IAAIX,IAAI,CAAClB,WAAW,CAAC,EAAE;QACrB;MACF;MACAA,WAAW,GAAG4C,cAAc,CAACG,SAAS,CAAC,CAAC,GAAG,KAAK;IAClD,CAAC,MAAM;MACL/C,WAAW,GAAG4C,cAAc,CAACI,eAAe,CAAC,CAAC;IAChD;;IAEA;IACAnB,cAAc,CAAC,CAAC;IAChB,IAAIX,IAAI,CAAClB,WAAW,CAAC,EAAE;MACrB;IACF;IAEAA,WAAW,GAAG2C,eAAe;EAC/B;AACF;AAEA,SAASE,gBAAgBA,CAAEX,OAAO,EAAE;EAClC,MAAMe,IAAI,GAAGf,OAAO,CAACgB,YAAY,CAAC,CAAC;EACnC,IAAID,IAAI,KAAK,IAAI,IAAIA,IAAI,KAAK,IAAI,IAAIA,IAAI,KAAK,GAAG,EAAE;IAClD,OAAO,IAAI;EACb;EACA,OAAO,KAAK;AACd;AAEA,SAAST,aAAaA,CAAA,EAAI;EACxB,KAAK,IAAIR,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGlC,QAAQ,CAACsB,MAAM,EAAEY,CAAC,EAAE,EAAE;IACxChC,WAAW,GAAGF,QAAQ,CAACkC,CAAC,CAAC,CAACgB,eAAe,CAAC,CAAC;IAC3C;EACF;EAEA,KAAK,IAAIhB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGlC,QAAQ,CAACsB,MAAM,EAAEY,CAAC,EAAE,EAAE;IACxC,IAAIlC,QAAQ,CAACkC,CAAC,CAAC,CAACkB,YAAY,CAAC,CAAC,KAAK,IAAI,EAAE;MACvCpD,QAAQ,CAACqD,MAAM,CAACnB,CAAC,EAAE,CAAC,CAAC;MACrBA,CAAC,EAAE;IACL;EACF;AACF;;AAEA;AACA;AACA,SAASJ,iCAAiCA,CAAET,IAAI,EAAE;EAChD,MAAMiC,UAAU,GAAG,CACjB,aAAa,EACb,YAAY,EACZ,WAAW,EACX,WAAW,EACX,WAAW,EACX,YAAY,CACb;EAED,KAAK,MAAMpB,CAAC,IAAIoB,UAAU,EAAE;IAC1B,IAAIjC,IAAI,CAACR,KAAK,CAACyC,UAAU,CAACpB,CAAC,CAAC,CAAC,EAAE;MAC7B,OAAO,IAAI;IACb;EACF;EACA,OAAO,KAAK;AACd;;AAEA;AACA1C,OAAO,CAACgB,QAAQ,GAAGA,QAAQ;AAC3BhB,OAAO,CAACC,UAAU,GAAG8D,KAAK,CAACC,IAAI,CAAC/D,UAAU,CAAC;AAC3CD,OAAO,CAACiE,CAAC,GAAG5D,WAAW,CAAC,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}