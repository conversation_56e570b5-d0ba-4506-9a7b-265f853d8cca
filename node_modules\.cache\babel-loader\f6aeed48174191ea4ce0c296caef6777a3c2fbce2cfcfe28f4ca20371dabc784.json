{"ast": null, "code": "/**\n * @license\n * Copyright 2018 Google LLC\n *\n * Use of this source code is governed by an MIT-style\n * license that can be found in the LICENSE file or at\n * https://opensource.org/licenses/MIT.\n * =============================================================================\n */\n/**\n *  Advanced activation layers.\n */\nimport { add, cast, clipByValue, elu, exp, greater, leakyRelu, logSumExp, mul, ones, prelu, relu, scalar, serialization, sub, tidy } from '@tensorflow/tfjs-core';\nimport { Softmax as softmaxActivation } from '../activations';\nimport { getConstraint, serializeConstraint } from '../constraints';\nimport { InputSpec, Layer } from '../engine/topology';\nimport { NotImplementedError, ValueError } from '../errors';\nimport { getInitializer, serializeInitializer } from '../initializers';\nimport { getRegularizer, serializeRegularizer } from '../regularizers';\nimport { getExactlyOneShape, getExactlyOneTensor } from '../utils/types_utils';\nclass ReLU extends Layer {\n  constructor(args) {\n    super(args == null ? {} : args);\n    this.supportsMasking = true;\n    if (args != null) {\n      this.maxValue = args.maxValue;\n    }\n  }\n  call(inputs, kwargs) {\n    inputs = getExactlyOneTensor(inputs);\n    let output = relu(inputs);\n    if (this.maxValue != null) {\n      output = clipByValue(output, 0, this.maxValue);\n    }\n    return output;\n  }\n  computeOutputShape(inputShape) {\n    return inputShape;\n  }\n  getConfig() {\n    const config = {\n      maxValue: this.maxValue\n    };\n    const baseConfig = super.getConfig();\n    Object.assign(config, baseConfig);\n    return config;\n  }\n}\n/** @nocollapse */\nReLU.className = 'ReLU';\nexport { ReLU };\nserialization.registerClass(ReLU);\nclass LeakyReLU extends Layer {\n  constructor(args) {\n    super(args == null ? {} : args);\n    this.DEFAULT_ALPHA = 0.3;\n    if (args == null) {\n      args = {};\n    }\n    this.alpha = args.alpha == null ? this.DEFAULT_ALPHA : args.alpha;\n  }\n  call(inputs, kwargs) {\n    const x = getExactlyOneTensor(inputs);\n    return leakyRelu(x, this.alpha);\n  }\n  computeOutputShape(inputShape) {\n    return inputShape;\n  }\n  getConfig() {\n    const config = {\n      alpha: this.alpha\n    };\n    const baseConfig = super.getConfig();\n    Object.assign(config, baseConfig);\n    return config;\n  }\n}\n/** @nocollapse */\nLeakyReLU.className = 'LeakyReLU';\nexport { LeakyReLU };\nserialization.registerClass(LeakyReLU);\nclass PReLU extends Layer {\n  constructor(args) {\n    super(args == null ? {} : args);\n    this.DEFAULT_ALPHA_INITIALIZER = 'zeros';\n    if (args == null) {\n      args = {};\n    }\n    this.supportsMasking = true;\n    this.alphaInitializer = getInitializer(args.alphaInitializer || this.DEFAULT_ALPHA_INITIALIZER);\n    this.alphaRegularizer = getRegularizer(args.alphaRegularizer);\n    this.alphaConstraint = getConstraint(args.alphaConstraint);\n    if (args.sharedAxes == null) {\n      this.sharedAxes = null;\n    } else if (Array.isArray(args.sharedAxes)) {\n      this.sharedAxes = args.sharedAxes;\n    } else if (typeof args.sharedAxes === 'number') {\n      this.sharedAxes = [args.sharedAxes];\n    } else {\n      throw new ValueError(\"Expected sharedAxes to be a number or an array of numbers, \" + \"but got \".concat(args.sharedAxes));\n    }\n  }\n  build(inputShape) {\n    inputShape = getExactlyOneShape(inputShape);\n    const paramShape = inputShape.slice(1);\n    if (this.sharedAxes != null) {\n      for (const i of this.sharedAxes) {\n        paramShape[i - 1] = 1;\n      }\n    }\n    this.alpha = this.addWeight('alpha', paramShape, 'float32', this.alphaInitializer, this.alphaRegularizer, true, this.alphaConstraint);\n    // Set input spec.\n    const axes = {};\n    if (this.sharedAxes != null) {\n      for (let i = 1; i < inputShape.length; ++i) {\n        axes[i] = inputShape[i];\n      }\n    }\n    this.inputSpec = [new InputSpec({\n      ndim: inputShape.length,\n      axes\n    })];\n    this.built = true;\n  }\n  call(inputs, kwargs) {\n    inputs = getExactlyOneTensor(inputs);\n    return prelu(inputs, this.alpha.read());\n  }\n  getConfig() {\n    const config = {\n      alphaInitializer: serializeInitializer(this.alphaInitializer),\n      alphaRegularizer: serializeRegularizer(this.alphaRegularizer),\n      alphaConstraint: serializeConstraint(this.alphaConstraint),\n      sharedAxes: this.sharedAxes\n    };\n    const baseConfig = super.getConfig();\n    Object.assign(config, baseConfig);\n    return config;\n  }\n}\n/** @nocollapse */\nPReLU.className = 'PReLU';\nexport { PReLU };\nserialization.registerClass(PReLU);\nclass ELU extends Layer {\n  constructor(args) {\n    super(args == null ? {} : args);\n    this.DEFAULT_ALPHA = 1.0;\n    if (args == null) {\n      args = {};\n    }\n    if (args.alpha != null && args.alpha !== this.DEFAULT_ALPHA) {\n      throw new NotImplementedError(\"Non-default alpha value (\".concat(args.alpha, \") is not supported by the \") + \"ELU layer yet.\");\n    }\n    this.alpha = args.alpha == null ? this.DEFAULT_ALPHA : args.alpha;\n  }\n  call(inputs, kwargs) {\n    const x = getExactlyOneTensor(inputs);\n    return elu(x);\n  }\n  computeOutputShape(inputShape) {\n    return inputShape;\n  }\n  getConfig() {\n    const config = {\n      alpha: this.alpha\n    };\n    const baseConfig = super.getConfig();\n    Object.assign(config, baseConfig);\n    return config;\n  }\n}\n/** @nocollapse */\nELU.className = 'ELU';\nexport { ELU };\nserialization.registerClass(ELU);\nclass ThresholdedReLU extends Layer {\n  constructor(args) {\n    super(args == null ? {} : args);\n    this.DEFAULT_THETA = 1.0;\n    if (args == null) {\n      args = {};\n    }\n    this.theta = args.theta == null ? this.DEFAULT_THETA : args.theta;\n  }\n  call(inputs, kwargs) {\n    const x = getExactlyOneTensor(inputs);\n    return mul(x, cast(greater(x, this.theta), 'float32'));\n  }\n  computeOutputShape(inputShape) {\n    return inputShape;\n  }\n  getConfig() {\n    const config = {\n      theta: this.theta\n    };\n    const baseConfig = super.getConfig();\n    Object.assign(config, baseConfig);\n    return config;\n  }\n}\n/** @nocollapse */\nThresholdedReLU.className = 'ThresholdedReLU';\nexport { ThresholdedReLU };\nserialization.registerClass(ThresholdedReLU);\nclass Softmax extends Layer {\n  constructor(args) {\n    super(args == null ? {} : args);\n    this.DEFAULT_AXIS = 1.0;\n    if (args == null) {\n      args = {};\n    }\n    this.softmax = new softmaxActivation().apply;\n    this.axis = args.axis == null ? this.DEFAULT_AXIS : args.axis;\n  }\n  call(inputs, kwargs) {\n    // TODO(pforderique): Add tests for when `this.axis` is a number[].\n    return tidy(() => {\n      let x = getExactlyOneTensor(inputs);\n      const mask = kwargs['mask'];\n      if (mask != null) {\n        // Since mask is 1.0 for positions we want to keep and 0.0 for masked\n        // positions, this operation will create a tensor which is 0.0 for\n        // positions we want to attend and -1e.9 for masked positions.\n        const adder = mul(sub(ones(x.shape), cast(mask, x.dtype)), scalar(-1e9));\n        // Since we are adding it to the raw scores before the softmax, this\n        // is effectively the same as removing these entirely.\n        x = add(x, adder);\n      }\n      if (this.axis instanceof Array) {\n        if (this.axis.length > 1) {\n          return exp(sub(x, logSumExp(x, this.axis, true)));\n        } else {\n          return this.softmax(x, this.axis[0]);\n        }\n      }\n      return this.softmax(x, this.axis);\n    });\n  }\n  computeOutputShape(inputShape) {\n    return inputShape;\n  }\n  getConfig() {\n    const config = {\n      axis: this.axis\n    };\n    const baseConfig = super.getConfig();\n    Object.assign(config, baseConfig);\n    return config;\n  }\n}\n/** @nocollapse */\nSoftmax.className = 'Softmax';\nexport { Softmax };\nserialization.registerClass(Softmax);", "map": {"version": 3, "names": ["add", "cast", "clipByValue", "elu", "exp", "greater", "leakyRelu", "logSumExp", "mul", "ones", "prelu", "relu", "scalar", "serialization", "sub", "tidy", "Softmax", "softmaxActivation", "getConstraint", "serializeConstraint", "InputSpec", "Layer", "NotImplementedError", "ValueError", "getInitializer", "serializeInitializer", "getRegularizer", "serializeRegularizer", "getExactlyOneShape", "getExactlyOneTensor", "ReLU", "constructor", "args", "supportsMasking", "maxValue", "call", "inputs", "kwargs", "output", "computeOutputShape", "inputShape", "getConfig", "config", "baseConfig", "Object", "assign", "className", "registerClass", "LeakyReLU", "DEFAULT_ALPHA", "alpha", "x", "PReLU", "DEFAULT_ALPHA_INITIALIZER", "alphaInitializer", "alphaRegularizer", "alphaConstraint", "sharedAxes", "Array", "isArray", "concat", "build", "param<PERSON><PERSON><PERSON>", "slice", "i", "addWeight", "axes", "length", "inputSpec", "ndim", "built", "read", "ELU", "ThresholdedReLU", "DEFAULT_THETA", "theta", "DEFAULT_AXIS", "softmax", "apply", "axis", "mask", "adder", "shape", "dtype"], "sources": ["C:\\tfjs-layers\\src\\layers\\advanced_activations.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2018 Google LLC\n *\n * Use of this source code is governed by an MIT-style\n * license that can be found in the LICENSE file or at\n * https://opensource.org/licenses/MIT.\n * =============================================================================\n */\n\n/**\n *  Advanced activation layers.\n */\n\nimport {add, cast, clipByValue, elu, exp, greater, leakyRelu, logSumExp, mul, ones, prelu, relu, scalar, serialization, sub, Tensor, tidy} from '@tensorflow/tfjs-core';\n\nimport {Softmax as softmaxActivation} from '../activations';\nimport {Constraint, getConstraint, serializeConstraint} from '../constraints';\nimport {InputSpec, Layer, LayerArgs} from '../engine/topology';\nimport {NotImplementedError, ValueError} from '../errors';\nimport {getInitializer, Initializer, InitializerIdentifier, serializeInitializer} from '../initializers';\nimport {Shape} from '../keras_format/common';\nimport {getRegularizer, Regularizer, serializeRegularizer} from '../regularizers';\nimport {Kwargs} from '../types';\nimport {getExactlyOneShape, getExactlyOneTensor} from '../utils/types_utils';\nimport {LayerVariable} from '../variables';\n\nexport declare interface ReLULayerArgs extends LayerArgs {\n  /**\n   * Float, the maximum output value.\n   */\n  maxValue?: number;\n}\n\nexport class ReLU extends Layer {\n  /** @nocollapse */\n  static className = 'ReLU';\n  maxValue: number;\n\n  constructor(args?: ReLULayerArgs) {\n    super(args == null ? {} : args);\n    this.supportsMasking = true;\n    if (args != null) {\n      this.maxValue = args.maxValue;\n    }\n  }\n\n  override call(inputs: Tensor|Tensor[], kwargs: Kwargs): Tensor|Tensor[] {\n    inputs = getExactlyOneTensor(inputs);\n    let output = relu(inputs);\n    if (this.maxValue != null) {\n      output = clipByValue(output, 0, this.maxValue);\n    }\n    return output;\n  }\n\n  override computeOutputShape(inputShape: Shape|Shape[]): Shape|Shape[] {\n    return inputShape;\n  }\n\n  override getConfig(): serialization.ConfigDict {\n    const config: serialization.ConfigDict = {maxValue: this.maxValue};\n    const baseConfig = super.getConfig();\n    Object.assign(config, baseConfig);\n    return config;\n  }\n}\nserialization.registerClass(ReLU);\n\nexport declare interface LeakyReLULayerArgs extends LayerArgs {\n  /**\n   * Float `>= 0`. Negative slope coefficient. Defaults to `0.3`.\n   */\n  alpha?: number;\n}\n\nexport class LeakyReLU extends Layer {\n  /** @nocollapse */\n  static className = 'LeakyReLU';\n  readonly alpha: number;\n\n  readonly DEFAULT_ALPHA = 0.3;\n\n  constructor(args?: LeakyReLULayerArgs) {\n    super(args == null ? {} : args);\n    if (args == null) {\n      args = {};\n    }\n    this.alpha = args.alpha == null ? this.DEFAULT_ALPHA : args.alpha;\n  }\n\n  override call(inputs: Tensor|Tensor[], kwargs: Kwargs): Tensor|Tensor[] {\n    const x = getExactlyOneTensor(inputs);\n    return leakyRelu(x, this.alpha);\n  }\n\n  override computeOutputShape(inputShape: Shape|Shape[]): Shape|Shape[] {\n    return inputShape;\n  }\n\n  override getConfig(): serialization.ConfigDict {\n    const config: serialization.ConfigDict = {alpha: this.alpha};\n    const baseConfig = super.getConfig();\n    Object.assign(config, baseConfig);\n    return config;\n  }\n}\nserialization.registerClass(LeakyReLU);\n\nexport declare interface PReLULayerArgs extends LayerArgs {\n  /**\n   * Initializer for the learnable alpha.\n   */\n  alphaInitializer?: Initializer|InitializerIdentifier;\n\n  /**\n   * Regularizer for the learnable alpha.\n   */\n  alphaRegularizer?: Regularizer;\n\n  /**\n   * Constraint for the learnable alpha.\n   */\n  alphaConstraint?: Constraint;\n\n  /**\n   * The axes along which to share learnable parameters for the activation\n   * function. For example, if the incoming feature maps are from a 2D\n   * convolution with output shape `[numExamples, height, width, channels]`,\n   * and you wish to share parameters across space (height and width) so that\n   * each filter channels has only one set of parameters, set\n   * `shared_axes: [1, 2]`.\n   */\n  sharedAxes?: number|number[];\n}\n\nexport class PReLU extends Layer {\n  /** @nocollapse */\n  static className = 'PReLU';\n  private readonly alphaInitializer: Initializer;\n  private readonly alphaRegularizer: Regularizer;\n  private readonly alphaConstraint: Constraint;\n  private readonly sharedAxes: number[];\n  private alpha: LayerVariable;\n\n  readonly DEFAULT_ALPHA_INITIALIZER: InitializerIdentifier = 'zeros';\n\n  constructor(args?: PReLULayerArgs) {\n    super(args == null ? {} : args);\n    if (args == null) {\n      args = {};\n    }\n\n    this.supportsMasking = true;\n    this.alphaInitializer =\n        getInitializer(args.alphaInitializer || this.DEFAULT_ALPHA_INITIALIZER);\n    this.alphaRegularizer = getRegularizer(args.alphaRegularizer);\n    this.alphaConstraint = getConstraint(args.alphaConstraint);\n    if (args.sharedAxes == null) {\n      this.sharedAxes = null;\n    } else if (Array.isArray(args.sharedAxes)) {\n      this.sharedAxes = args.sharedAxes;\n    } else if (typeof args.sharedAxes === 'number') {\n      this.sharedAxes = [args.sharedAxes];\n    } else {\n      throw new ValueError(\n          `Expected sharedAxes to be a number or an array of numbers, ` +\n          `but got ${args.sharedAxes}`);\n    }\n  }\n\n  override build(inputShape: Shape|Shape[]) {\n    inputShape = getExactlyOneShape(inputShape);\n    const paramShape: Shape = inputShape.slice(1);\n    if (this.sharedAxes != null) {\n      for (const i of this.sharedAxes) {\n        paramShape[i - 1] = 1;\n      }\n    }\n    this.alpha = this.addWeight(\n        'alpha', paramShape, 'float32', this.alphaInitializer,\n        this.alphaRegularizer, true, this.alphaConstraint);\n    // Set input spec.\n    const axes: {[axis: number]: number} = {};\n    if (this.sharedAxes != null) {\n      for (let i = 1; i < inputShape.length; ++i) {\n        axes[i] = inputShape[i];\n      }\n    }\n    this.inputSpec = [new InputSpec({\n      ndim: inputShape.length,\n      axes,\n    })];\n    this.built = true;\n  }\n\n  override call(inputs: Tensor|Tensor[], kwargs: Kwargs): Tensor|Tensor[] {\n    inputs = getExactlyOneTensor(inputs);\n    return prelu(inputs, this.alpha.read());\n  }\n\n  override getConfig(): serialization.ConfigDict {\n    const config: serialization.ConfigDict = {\n      alphaInitializer: serializeInitializer(this.alphaInitializer),\n      alphaRegularizer: serializeRegularizer(this.alphaRegularizer),\n      alphaConstraint: serializeConstraint(this.alphaConstraint),\n      sharedAxes: this.sharedAxes\n    };\n    const baseConfig = super.getConfig();\n    Object.assign(config, baseConfig);\n    return config;\n  }\n}\nserialization.registerClass(PReLU);\n\nexport declare interface ELULayerArgs extends LayerArgs {\n  /**\n   * Float `>= 0`. Negative slope coefficient. Defaults to `1.0`.\n   */\n  alpha?: number;\n}\n\nexport class ELU extends Layer {\n  /** @nocollapse */\n  static className = 'ELU';\n  readonly alpha: number;\n\n  readonly DEFAULT_ALPHA = 1.0;\n\n  constructor(args?: ELULayerArgs) {\n    super(args == null ? {} : args);\n    if (args == null) {\n      args = {};\n    }\n\n    if (args.alpha != null && args.alpha !== this.DEFAULT_ALPHA) {\n      throw new NotImplementedError(\n          `Non-default alpha value (${args.alpha}) is not supported by the ` +\n          `ELU layer yet.`);\n    }\n\n    this.alpha = args.alpha == null ? this.DEFAULT_ALPHA : args.alpha;\n  }\n\n  override call(inputs: Tensor|Tensor[], kwargs: Kwargs): Tensor|Tensor[] {\n    const x = getExactlyOneTensor(inputs);\n    return elu(x);\n  }\n\n  override computeOutputShape(inputShape: Shape|Shape[]): Shape|Shape[] {\n    return inputShape;\n  }\n\n  override getConfig(): serialization.ConfigDict {\n    const config: serialization.ConfigDict = {alpha: this.alpha};\n    const baseConfig = super.getConfig();\n    Object.assign(config, baseConfig);\n    return config;\n  }\n}\nserialization.registerClass(ELU);\n\nexport declare interface ThresholdedReLULayerArgs extends LayerArgs {\n  /**\n   * Float >= 0. Threshold location of activation.\n   */\n  theta?: number;\n}\n\nexport class ThresholdedReLU extends Layer {\n  /** @nocollapse */\n  static className = 'ThresholdedReLU';\n  readonly theta: number;\n\n  readonly DEFAULT_THETA = 1.0;\n\n  constructor(args?: ThresholdedReLULayerArgs) {\n    super(args == null ? {} : args);\n    if (args == null) {\n      args = {};\n    }\n\n    this.theta = args.theta == null ? this.DEFAULT_THETA : args.theta;\n  }\n\n  override call(inputs: Tensor|Tensor[], kwargs: Kwargs): Tensor|Tensor[] {\n    const x = getExactlyOneTensor(inputs);\n    return mul(x, cast(greater(x, this.theta), 'float32'));\n  }\n\n  override computeOutputShape(inputShape: Shape|Shape[]): Shape|Shape[] {\n    return inputShape;\n  }\n\n  override getConfig(): serialization.ConfigDict {\n    const config: serialization.ConfigDict = {theta: this.theta};\n    const baseConfig = super.getConfig();\n    Object.assign(config, baseConfig);\n    return config;\n  }\n}\nserialization.registerClass(ThresholdedReLU);\n\nexport declare interface SoftmaxLayerArgs extends LayerArgs {\n  /**\n   * Integer, axis along which the softmax normalization is applied.\n   * Defaults to `-1` (i.e., the last axis).\n   */\n  axis?: number|number[];\n}\n\nexport class Softmax extends Layer {\n  /** @nocollapse */\n  static className = 'Softmax';\n  readonly axis: number|number[];\n  readonly softmax: (t: Tensor, a?: number) => Tensor;\n  readonly DEFAULT_AXIS = 1.0;\n\n  constructor(args?: SoftmaxLayerArgs) {\n    super(args == null ? {} : args);\n    if (args == null) {\n      args = {};\n    }\n    this.softmax = new softmaxActivation().apply;\n    this.axis = args.axis == null ? this.DEFAULT_AXIS : args.axis;\n  }\n\n  override call(inputs: Tensor|Tensor[], kwargs: Kwargs): Tensor|Tensor[] {\n    // TODO(pforderique): Add tests for when `this.axis` is a number[].\n    return tidy(() => {\n      let x = getExactlyOneTensor(inputs);\n      const mask = kwargs['mask'] as Tensor;\n      if (mask != null) {\n        // Since mask is 1.0 for positions we want to keep and 0.0 for masked\n        // positions, this operation will create a tensor which is 0.0 for\n        // positions we want to attend and -1e.9 for masked positions.\n        const adder =\n          mul(sub(ones(x.shape), cast(mask, x.dtype)), scalar(-1e9));\n\n        // Since we are adding it to the raw scores before the softmax, this\n        // is effectively the same as removing these entirely.\n        x = add(x, adder);\n      }\n      if (this.axis instanceof Array) {\n        if (this.axis.length > 1) {\n          return exp(sub(x, logSumExp(x, this.axis, true)));\n        } else {\n          return this.softmax(x, this.axis[0]);\n        }\n      }\n      return this.softmax(x, this.axis);\n    });\n  }\n\n  override computeOutputShape(inputShape: Shape|Shape[]): Shape|Shape[] {\n    return inputShape;\n  }\n\n  override getConfig(): serialization.ConfigDict {\n    const config: serialization.ConfigDict = {axis: this.axis};\n    const baseConfig = super.getConfig();\n    Object.assign(config, baseConfig);\n    return config;\n  }\n}\nserialization.registerClass(Softmax);\n"], "mappings": "AAAA;;;;;;;;;AAUA;;;AAIA,SAAQA,GAAG,EAAEC,IAAI,EAAEC,WAAW,EAAEC,GAAG,EAAEC,GAAG,EAAEC,OAAO,EAAEC,SAAS,EAAEC,SAAS,EAAEC,GAAG,EAAEC,IAAI,EAAEC,KAAK,EAAEC,IAAI,EAAEC,MAAM,EAAEC,aAAa,EAAEC,GAAG,EAAUC,IAAI,QAAO,uBAAuB;AAEvK,SAAQC,OAAO,IAAIC,iBAAiB,QAAO,gBAAgB;AAC3D,SAAoBC,aAAa,EAAEC,mBAAmB,QAAO,gBAAgB;AAC7E,SAAQC,SAAS,EAAEC,KAAK,QAAkB,oBAAoB;AAC9D,SAAQC,mBAAmB,EAAEC,UAAU,QAAO,WAAW;AACzD,SAAQC,cAAc,EAAsCC,oBAAoB,QAAO,iBAAiB;AAExG,SAAQC,cAAc,EAAeC,oBAAoB,QAAO,iBAAiB;AAEjF,SAAQC,kBAAkB,EAAEC,mBAAmB,QAAO,sBAAsB;AAU5E,MAAaC,IAAK,SAAQT,KAAK;EAK7BU,YAAYC,IAAoB;IAC9B,KAAK,CAACA,IAAI,IAAI,IAAI,GAAG,EAAE,GAAGA,IAAI,CAAC;IAC/B,IAAI,CAACC,eAAe,GAAG,IAAI;IAC3B,IAAID,IAAI,IAAI,IAAI,EAAE;MAChB,IAAI,CAACE,QAAQ,GAAGF,IAAI,CAACE,QAAQ;;EAEjC;EAESC,IAAIA,CAACC,MAAuB,EAAEC,MAAc;IACnDD,MAAM,GAAGP,mBAAmB,CAACO,MAAM,CAAC;IACpC,IAAIE,MAAM,GAAG3B,IAAI,CAACyB,MAAM,CAAC;IACzB,IAAI,IAAI,CAACF,QAAQ,IAAI,IAAI,EAAE;MACzBI,MAAM,GAAGpC,WAAW,CAACoC,MAAM,EAAE,CAAC,EAAE,IAAI,CAACJ,QAAQ,CAAC;;IAEhD,OAAOI,MAAM;EACf;EAESC,kBAAkBA,CAACC,UAAyB;IACnD,OAAOA,UAAU;EACnB;EAESC,SAASA,CAAA;IAChB,MAAMC,MAAM,GAA6B;MAACR,QAAQ,EAAE,IAAI,CAACA;IAAQ,CAAC;IAClE,MAAMS,UAAU,GAAG,KAAK,CAACF,SAAS,EAAE;IACpCG,MAAM,CAACC,MAAM,CAACH,MAAM,EAAEC,UAAU,CAAC;IACjC,OAAOD,MAAM;EACf;;AA9BA;AACOZ,IAAA,CAAAgB,SAAS,GAAG,MAAM;SAFdhB,IAAI;AAiCjBjB,aAAa,CAACkC,aAAa,CAACjB,IAAI,CAAC;AASjC,MAAakB,SAAU,SAAQ3B,KAAK;EAOlCU,YAAYC,IAAyB;IACnC,KAAK,CAACA,IAAI,IAAI,IAAI,GAAG,EAAE,GAAGA,IAAI,CAAC;IAHxB,KAAAiB,aAAa,GAAG,GAAG;IAI1B,IAAIjB,IAAI,IAAI,IAAI,EAAE;MAChBA,IAAI,GAAG,EAAE;;IAEX,IAAI,CAACkB,KAAK,GAAGlB,IAAI,CAACkB,KAAK,IAAI,IAAI,GAAG,IAAI,CAACD,aAAa,GAAGjB,IAAI,CAACkB,KAAK;EACnE;EAESf,IAAIA,CAACC,MAAuB,EAAEC,MAAc;IACnD,MAAMc,CAAC,GAAGtB,mBAAmB,CAACO,MAAM,CAAC;IACrC,OAAO9B,SAAS,CAAC6C,CAAC,EAAE,IAAI,CAACD,KAAK,CAAC;EACjC;EAESX,kBAAkBA,CAACC,UAAyB;IACnD,OAAOA,UAAU;EACnB;EAESC,SAASA,CAAA;IAChB,MAAMC,MAAM,GAA6B;MAACQ,KAAK,EAAE,IAAI,CAACA;IAAK,CAAC;IAC5D,MAAMP,UAAU,GAAG,KAAK,CAACF,SAAS,EAAE;IACpCG,MAAM,CAACC,MAAM,CAACH,MAAM,EAAEC,UAAU,CAAC;IACjC,OAAOD,MAAM;EACf;;AA5BA;AACOM,SAAA,CAAAF,SAAS,GAAG,WAAW;SAFnBE,SAAS;AA+BtBnC,aAAa,CAACkC,aAAa,CAACC,SAAS,CAAC;AA6BtC,MAAaI,KAAM,SAAQ/B,KAAK;EAW9BU,YAAYC,IAAqB;IAC/B,KAAK,CAACA,IAAI,IAAI,IAAI,GAAG,EAAE,GAAGA,IAAI,CAAC;IAHxB,KAAAqB,yBAAyB,GAA0B,OAAO;IAIjE,IAAIrB,IAAI,IAAI,IAAI,EAAE;MAChBA,IAAI,GAAG,EAAE;;IAGX,IAAI,CAACC,eAAe,GAAG,IAAI;IAC3B,IAAI,CAACqB,gBAAgB,GACjB9B,cAAc,CAACQ,IAAI,CAACsB,gBAAgB,IAAI,IAAI,CAACD,yBAAyB,CAAC;IAC3E,IAAI,CAACE,gBAAgB,GAAG7B,cAAc,CAACM,IAAI,CAACuB,gBAAgB,CAAC;IAC7D,IAAI,CAACC,eAAe,GAAGtC,aAAa,CAACc,IAAI,CAACwB,eAAe,CAAC;IAC1D,IAAIxB,IAAI,CAACyB,UAAU,IAAI,IAAI,EAAE;MAC3B,IAAI,CAACA,UAAU,GAAG,IAAI;KACvB,MAAM,IAAIC,KAAK,CAACC,OAAO,CAAC3B,IAAI,CAACyB,UAAU,CAAC,EAAE;MACzC,IAAI,CAACA,UAAU,GAAGzB,IAAI,CAACyB,UAAU;KAClC,MAAM,IAAI,OAAOzB,IAAI,CAACyB,UAAU,KAAK,QAAQ,EAAE;MAC9C,IAAI,CAACA,UAAU,GAAG,CAACzB,IAAI,CAACyB,UAAU,CAAC;KACpC,MAAM;MACL,MAAM,IAAIlC,UAAU,CAChB,2EAAAqC,MAAA,CACW5B,IAAI,CAACyB,UAAU,CAAE,CAAC;;EAErC;EAESI,KAAKA,CAACrB,UAAyB;IACtCA,UAAU,GAAGZ,kBAAkB,CAACY,UAAU,CAAC;IAC3C,MAAMsB,UAAU,GAAUtB,UAAU,CAACuB,KAAK,CAAC,CAAC,CAAC;IAC7C,IAAI,IAAI,CAACN,UAAU,IAAI,IAAI,EAAE;MAC3B,KAAK,MAAMO,CAAC,IAAI,IAAI,CAACP,UAAU,EAAE;QAC/BK,UAAU,CAACE,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC;;;IAGzB,IAAI,CAACd,KAAK,GAAG,IAAI,CAACe,SAAS,CACvB,OAAO,EAAEH,UAAU,EAAE,SAAS,EAAE,IAAI,CAACR,gBAAgB,EACrD,IAAI,CAACC,gBAAgB,EAAE,IAAI,EAAE,IAAI,CAACC,eAAe,CAAC;IACtD;IACA,MAAMU,IAAI,GAA6B,EAAE;IACzC,IAAI,IAAI,CAACT,UAAU,IAAI,IAAI,EAAE;MAC3B,KAAK,IAAIO,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGxB,UAAU,CAAC2B,MAAM,EAAE,EAAEH,CAAC,EAAE;QAC1CE,IAAI,CAACF,CAAC,CAAC,GAAGxB,UAAU,CAACwB,CAAC,CAAC;;;IAG3B,IAAI,CAACI,SAAS,GAAG,CAAC,IAAIhD,SAAS,CAAC;MAC9BiD,IAAI,EAAE7B,UAAU,CAAC2B,MAAM;MACvBD;KACD,CAAC,CAAC;IACH,IAAI,CAACI,KAAK,GAAG,IAAI;EACnB;EAESnC,IAAIA,CAACC,MAAuB,EAAEC,MAAc;IACnDD,MAAM,GAAGP,mBAAmB,CAACO,MAAM,CAAC;IACpC,OAAO1B,KAAK,CAAC0B,MAAM,EAAE,IAAI,CAACc,KAAK,CAACqB,IAAI,EAAE,CAAC;EACzC;EAES9B,SAASA,CAAA;IAChB,MAAMC,MAAM,GAA6B;MACvCY,gBAAgB,EAAE7B,oBAAoB,CAAC,IAAI,CAAC6B,gBAAgB,CAAC;MAC7DC,gBAAgB,EAAE5B,oBAAoB,CAAC,IAAI,CAAC4B,gBAAgB,CAAC;MAC7DC,eAAe,EAAErC,mBAAmB,CAAC,IAAI,CAACqC,eAAe,CAAC;MAC1DC,UAAU,EAAE,IAAI,CAACA;KAClB;IACD,MAAMd,UAAU,GAAG,KAAK,CAACF,SAAS,EAAE;IACpCG,MAAM,CAACC,MAAM,CAACH,MAAM,EAAEC,UAAU,CAAC;IACjC,OAAOD,MAAM;EACf;;AA1EA;AACOU,KAAA,CAAAN,SAAS,GAAG,OAAO;SAFfM,KAAK;AA6ElBvC,aAAa,CAACkC,aAAa,CAACK,KAAK,CAAC;AASlC,MAAaoB,GAAI,SAAQnD,KAAK;EAO5BU,YAAYC,IAAmB;IAC7B,KAAK,CAACA,IAAI,IAAI,IAAI,GAAG,EAAE,GAAGA,IAAI,CAAC;IAHxB,KAAAiB,aAAa,GAAG,GAAG;IAI1B,IAAIjB,IAAI,IAAI,IAAI,EAAE;MAChBA,IAAI,GAAG,EAAE;;IAGX,IAAIA,IAAI,CAACkB,KAAK,IAAI,IAAI,IAAIlB,IAAI,CAACkB,KAAK,KAAK,IAAI,CAACD,aAAa,EAAE;MAC3D,MAAM,IAAI3B,mBAAmB,CACzB,4BAAAsC,MAAA,CAA4B5B,IAAI,CAACkB,KAAK,kDACtB,CAAC;;IAGvB,IAAI,CAACA,KAAK,GAAGlB,IAAI,CAACkB,KAAK,IAAI,IAAI,GAAG,IAAI,CAACD,aAAa,GAAGjB,IAAI,CAACkB,KAAK;EACnE;EAESf,IAAIA,CAACC,MAAuB,EAAEC,MAAc;IACnD,MAAMc,CAAC,GAAGtB,mBAAmB,CAACO,MAAM,CAAC;IACrC,OAAOjC,GAAG,CAACgD,CAAC,CAAC;EACf;EAESZ,kBAAkBA,CAACC,UAAyB;IACnD,OAAOA,UAAU;EACnB;EAESC,SAASA,CAAA;IAChB,MAAMC,MAAM,GAA6B;MAACQ,KAAK,EAAE,IAAI,CAACA;IAAK,CAAC;IAC5D,MAAMP,UAAU,GAAG,KAAK,CAACF,SAAS,EAAE;IACpCG,MAAM,CAACC,MAAM,CAACH,MAAM,EAAEC,UAAU,CAAC;IACjC,OAAOD,MAAM;EACf;;AAnCA;AACO8B,GAAA,CAAA1B,SAAS,GAAG,KAAK;SAFb0B,GAAG;AAsChB3D,aAAa,CAACkC,aAAa,CAACyB,GAAG,CAAC;AAShC,MAAaC,eAAgB,SAAQpD,KAAK;EAOxCU,YAAYC,IAA+B;IACzC,KAAK,CAACA,IAAI,IAAI,IAAI,GAAG,EAAE,GAAGA,IAAI,CAAC;IAHxB,KAAA0C,aAAa,GAAG,GAAG;IAI1B,IAAI1C,IAAI,IAAI,IAAI,EAAE;MAChBA,IAAI,GAAG,EAAE;;IAGX,IAAI,CAAC2C,KAAK,GAAG3C,IAAI,CAAC2C,KAAK,IAAI,IAAI,GAAG,IAAI,CAACD,aAAa,GAAG1C,IAAI,CAAC2C,KAAK;EACnE;EAESxC,IAAIA,CAACC,MAAuB,EAAEC,MAAc;IACnD,MAAMc,CAAC,GAAGtB,mBAAmB,CAACO,MAAM,CAAC;IACrC,OAAO5B,GAAG,CAAC2C,CAAC,EAAElD,IAAI,CAACI,OAAO,CAAC8C,CAAC,EAAE,IAAI,CAACwB,KAAK,CAAC,EAAE,SAAS,CAAC,CAAC;EACxD;EAESpC,kBAAkBA,CAACC,UAAyB;IACnD,OAAOA,UAAU;EACnB;EAESC,SAASA,CAAA;IAChB,MAAMC,MAAM,GAA6B;MAACiC,KAAK,EAAE,IAAI,CAACA;IAAK,CAAC;IAC5D,MAAMhC,UAAU,GAAG,KAAK,CAACF,SAAS,EAAE;IACpCG,MAAM,CAACC,MAAM,CAACH,MAAM,EAAEC,UAAU,CAAC;IACjC,OAAOD,MAAM;EACf;;AA7BA;AACO+B,eAAA,CAAA3B,SAAS,GAAG,iBAAiB;SAFzB2B,eAAe;AAgC5B5D,aAAa,CAACkC,aAAa,CAAC0B,eAAe,CAAC;AAU5C,MAAazD,OAAQ,SAAQK,KAAK;EAOhCU,YAAYC,IAAuB;IACjC,KAAK,CAACA,IAAI,IAAI,IAAI,GAAG,EAAE,GAAGA,IAAI,CAAC;IAHxB,KAAA4C,YAAY,GAAG,GAAG;IAIzB,IAAI5C,IAAI,IAAI,IAAI,EAAE;MAChBA,IAAI,GAAG,EAAE;;IAEX,IAAI,CAAC6C,OAAO,GAAG,IAAI5D,iBAAiB,EAAE,CAAC6D,KAAK;IAC5C,IAAI,CAACC,IAAI,GAAG/C,IAAI,CAAC+C,IAAI,IAAI,IAAI,GAAG,IAAI,CAACH,YAAY,GAAG5C,IAAI,CAAC+C,IAAI;EAC/D;EAES5C,IAAIA,CAACC,MAAuB,EAAEC,MAAc;IACnD;IACA,OAAOtB,IAAI,CAAC,MAAK;MACf,IAAIoC,CAAC,GAAGtB,mBAAmB,CAACO,MAAM,CAAC;MACnC,MAAM4C,IAAI,GAAG3C,MAAM,CAAC,MAAM,CAAW;MACrC,IAAI2C,IAAI,IAAI,IAAI,EAAE;QAChB;QACA;QACA;QACA,MAAMC,KAAK,GACTzE,GAAG,CAACM,GAAG,CAACL,IAAI,CAAC0C,CAAC,CAAC+B,KAAK,CAAC,EAAEjF,IAAI,CAAC+E,IAAI,EAAE7B,CAAC,CAACgC,KAAK,CAAC,CAAC,EAAEvE,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC;QAE5D;QACA;QACAuC,CAAC,GAAGnD,GAAG,CAACmD,CAAC,EAAE8B,KAAK,CAAC;;MAEnB,IAAI,IAAI,CAACF,IAAI,YAAYrB,KAAK,EAAE;QAC9B,IAAI,IAAI,CAACqB,IAAI,CAACZ,MAAM,GAAG,CAAC,EAAE;UACxB,OAAO/D,GAAG,CAACU,GAAG,CAACqC,CAAC,EAAE5C,SAAS,CAAC4C,CAAC,EAAE,IAAI,CAAC4B,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC;SAClD,MAAM;UACL,OAAO,IAAI,CAACF,OAAO,CAAC1B,CAAC,EAAE,IAAI,CAAC4B,IAAI,CAAC,CAAC,CAAC,CAAC;;;MAGxC,OAAO,IAAI,CAACF,OAAO,CAAC1B,CAAC,EAAE,IAAI,CAAC4B,IAAI,CAAC;IACnC,CAAC,CAAC;EACJ;EAESxC,kBAAkBA,CAACC,UAAyB;IACnD,OAAOA,UAAU;EACnB;EAESC,SAASA,CAAA;IAChB,MAAMC,MAAM,GAA6B;MAACqC,IAAI,EAAE,IAAI,CAACA;IAAI,CAAC;IAC1D,MAAMpC,UAAU,GAAG,KAAK,CAACF,SAAS,EAAE;IACpCG,MAAM,CAACC,MAAM,CAACH,MAAM,EAAEC,UAAU,CAAC;IACjC,OAAOD,MAAM;EACf;;AAnDA;AACO1B,OAAA,CAAA8B,SAAS,GAAG,SAAS;SAFjB9B,OAAO;AAsDpBH,aAAa,CAACkC,aAAa,CAAC/B,OAAO,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}