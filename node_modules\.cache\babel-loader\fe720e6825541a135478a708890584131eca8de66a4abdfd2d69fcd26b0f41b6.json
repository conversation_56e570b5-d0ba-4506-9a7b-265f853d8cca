{"ast": null, "code": "import find from './find.js';\nimport matcher from './matcher.js';\n\n// Convenience version of a common use case of `_.find`: getting the first\n// object containing specific `key:value` pairs.\nexport default function findWhere(obj, attrs) {\n  return find(obj, matcher(attrs));\n}", "map": {"version": 3, "names": ["find", "matcher", "findWhere", "obj", "attrs"], "sources": ["C:/tmsft/node_modules/underscore/modules/findWhere.js"], "sourcesContent": ["import find from './find.js';\nimport matcher from './matcher.js';\n\n// Convenience version of a common use case of `_.find`: getting the first\n// object containing specific `key:value` pairs.\nexport default function findWhere(obj, attrs) {\n  return find(obj, matcher(attrs));\n}\n"], "mappings": "AAAA,OAAOA,IAAI,MAAM,WAAW;AAC5B,OAAOC,OAAO,MAAM,cAAc;;AAElC;AACA;AACA,eAAe,SAASC,SAASA,CAACC,GAAG,EAAEC,KAAK,EAAE;EAC5C,OAAOJ,IAAI,CAACG,GAAG,EAAEF,OAAO,CAACG,KAAK,CAAC,CAAC;AAClC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}