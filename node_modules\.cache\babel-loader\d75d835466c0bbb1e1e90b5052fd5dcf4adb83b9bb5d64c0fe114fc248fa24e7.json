{"ast": null, "code": "import isFunction from './isFunction.js';\n\n// Return a sorted list of the function names available on the object.\nexport default function functions(obj) {\n  var names = [];\n  for (var key in obj) {\n    if (isFunction(obj[key])) names.push(key);\n  }\n  return names.sort();\n}", "map": {"version": 3, "names": ["isFunction", "functions", "obj", "names", "key", "push", "sort"], "sources": ["C:/tmsft/node_modules/underscore/modules/functions.js"], "sourcesContent": ["import isFunction from './isFunction.js';\n\n// Return a sorted list of the function names available on the object.\nexport default function functions(obj) {\n  var names = [];\n  for (var key in obj) {\n    if (isFunction(obj[key])) names.push(key);\n  }\n  return names.sort();\n}\n"], "mappings": "AAAA,OAAOA,UAAU,MAAM,iBAAiB;;AAExC;AACA,eAAe,SAASC,SAASA,CAACC,GAAG,EAAE;EACrC,IAAIC,KAAK,GAAG,EAAE;EACd,KAAK,IAAIC,GAAG,IAAIF,GAAG,EAAE;IACnB,IAAIF,UAAU,CAACE,GAAG,CAACE,GAAG,CAAC,CAAC,EAAED,KAAK,CAACE,IAAI,CAACD,GAAG,CAAC;EAC3C;EACA,OAAOD,KAAK,CAACG,IAAI,CAAC,CAAC;AACrB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}