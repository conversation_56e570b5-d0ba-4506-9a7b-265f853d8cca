{"ast": null, "code": "/**\n * @license\n * Copyright 2022 CodeSmith LLC\n *\n * Use of this source code is governed by an MIT-style\n * license that can be found in the LICENSE file or at\n * https://opensource.org/licenses/MIT.\n * =============================================================================\n */\nimport { Layer } from '../../engine/topology';\nimport { serialization, mul, add, tidy } from '@tensorflow/tfjs-core';\nimport { getExactlyOneTensor } from '../../utils/types_utils';\nimport * as K from '../../backend/tfjs_backend';\n/**\n * Preprocessing Rescaling Layer\n *\n * This rescales images by a scaling and offset factor\n */\nclass Rescaling extends Layer {\n  constructor(args) {\n    super(args);\n    this.scale = args.scale;\n    if (args.offset) {\n      this.offset = args.offset;\n    } else {\n      this.offset = 0;\n    }\n  }\n  getConfig() {\n    const config = {\n      'scale': this.scale,\n      'offset': this.offset\n    };\n    const baseConfig = super.getConfig();\n    Object.assign(config, baseConfig);\n    return config;\n  }\n  call(inputs, kwargs) {\n    return tidy(() => {\n      inputs = getExactlyOneTensor(inputs);\n      if (inputs.dtype !== 'float32') {\n        inputs = K.cast(inputs, 'float32');\n      }\n      return add(mul(inputs, this.scale), this.offset);\n    });\n  }\n}\n/** @nocollapse */\nRescaling.className = 'Rescaling';\nexport { Rescaling };\nserialization.registerClass(Rescaling);", "map": {"version": 3, "names": ["Layer", "serialization", "mul", "add", "tidy", "getExactlyOneTensor", "K", "Rescaling", "constructor", "args", "scale", "offset", "getConfig", "config", "baseConfig", "Object", "assign", "call", "inputs", "kwargs", "dtype", "cast", "className", "registerClass"], "sources": ["C:\\tfjs-layers\\src\\layers\\preprocessing\\image_preprocessing.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2022 CodeSmith LLC\n *\n * Use of this source code is governed by an MIT-style\n * license that can be found in the LICENSE file or at\n * https://opensource.org/licenses/MIT.\n * =============================================================================\n */\n\nimport {LayerArgs, Layer} from '../../engine/topology';\nimport { serialization, Tensor, mul, add, tidy } from '@tensorflow/tfjs-core';\nimport { getExactlyOneTensor } from '../../utils/types_utils';\nimport * as K from '../../backend/tfjs_backend';\nimport { Kwargs } from '../../types';\n\nexport declare interface RescalingArgs extends LayerArgs {\n  scale: number;\n  offset?: number;\n}\n\n/**\n * Preprocessing Rescaling Layer\n *\n * This rescales images by a scaling and offset factor\n */\nexport class Rescaling extends Layer {\n  /** @nocollapse */\n  static className = 'Rescaling';\n  private readonly scale: number;\n  private readonly offset: number;\n  constructor(args: RescalingArgs) {\n    super(args);\n\n    this.scale = args.scale;\n\n    if(args.offset) {\n    this.offset = args.offset;\n    } else {\n      this.offset = 0;\n    }\n  }\n\n  override getConfig(): serialization.ConfigDict {\n    const config: serialization.ConfigDict = {\n      'scale': this.scale,\n      'offset': this.offset\n    };\n\n    const baseConfig = super.getConfig();\n    Object.assign(config, baseConfig);\n    return config;\n  }\n\n  override call(inputs: Tensor|Tensor[], kwargs: Kwargs): Tensor[]|Tensor {\n    return tidy(() => {\n      inputs = getExactlyOneTensor(inputs);\n      if(inputs.dtype !== 'float32') {\n          inputs = K.cast(inputs, 'float32');\n      }\n      return add(mul(inputs, this.scale), this.offset);\n    });\n  }\n}\n\nserialization.registerClass(Rescaling);\n"], "mappings": "AAAA;;;;;;;;;AAUA,SAAmBA,KAAK,QAAO,uBAAuB;AACtD,SAASC,aAAa,EAAUC,GAAG,EAAEC,GAAG,EAAEC,IAAI,QAAQ,uBAAuB;AAC7E,SAASC,mBAAmB,QAAQ,yBAAyB;AAC7D,OAAO,KAAKC,CAAC,MAAM,4BAA4B;AAQ/C;;;;;AAKA,MAAaC,SAAU,SAAQP,KAAK;EAKlCQ,YAAYC,IAAmB;IAC7B,KAAK,CAACA,IAAI,CAAC;IAEX,IAAI,CAACC,KAAK,GAAGD,IAAI,CAACC,KAAK;IAEvB,IAAGD,IAAI,CAACE,MAAM,EAAE;MAChB,IAAI,CAACA,MAAM,GAAGF,IAAI,CAACE,MAAM;KACxB,MAAM;MACL,IAAI,CAACA,MAAM,GAAG,CAAC;;EAEnB;EAESC,SAASA,CAAA;IAChB,MAAMC,MAAM,GAA6B;MACvC,OAAO,EAAE,IAAI,CAACH,KAAK;MACnB,QAAQ,EAAE,IAAI,CAACC;KAChB;IAED,MAAMG,UAAU,GAAG,KAAK,CAACF,SAAS,EAAE;IACpCG,MAAM,CAACC,MAAM,CAACH,MAAM,EAAEC,UAAU,CAAC;IACjC,OAAOD,MAAM;EACf;EAESI,IAAIA,CAACC,MAAuB,EAAEC,MAAc;IACnD,OAAOf,IAAI,CAAC,MAAK;MACfc,MAAM,GAAGb,mBAAmB,CAACa,MAAM,CAAC;MACpC,IAAGA,MAAM,CAACE,KAAK,KAAK,SAAS,EAAE;QAC3BF,MAAM,GAAGZ,CAAC,CAACe,IAAI,CAACH,MAAM,EAAE,SAAS,CAAC;;MAEtC,OAAOf,GAAG,CAACD,GAAG,CAACgB,MAAM,EAAE,IAAI,CAACR,KAAK,CAAC,EAAE,IAAI,CAACC,MAAM,CAAC;IAClD,CAAC,CAAC;EACJ;;AAnCA;AACOJ,SAAA,CAAAe,SAAS,GAAG,WAAW;SAFnBf,SAAS;AAuCtBN,aAAa,CAACsB,aAAa,CAAChB,SAAS,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}