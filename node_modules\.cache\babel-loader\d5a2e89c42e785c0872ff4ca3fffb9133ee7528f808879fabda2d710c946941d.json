{"ast": null, "code": "import _objectSpread from\"C:/tmsft/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";import React,{useState,useCallback}from'react';import{FileUpload}from'./FileUpload';import{csvProcessingService}from'../services/csvProcessingService';import{bankAccountService}from'../services/bankAccountService';import'./BankStatementImport.css';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";export const BankStatementImport=_ref=>{let{onImportComplete}=_ref;const[step,setStep]=useState('upload');const[files,setFiles]=useState([]);const[selectedBankAccount,setSelectedBankAccount]=useState(null);const[importSummaries,setImportSummaries]=useState([]);const[isProcessing,setIsProcessing]=useState(false);const[error,setError]=useState(null);const[bankAccounts]=useState(bankAccountService.getAllAccounts());const handleFilesSelected=useCallback(async selectedFiles=>{setFiles(selectedFiles);setIsProcessing(true);setError(null);try{const summaries=[];for(const file of selectedFiles){const summary=await csvProcessingService.processFile(file);summaries.push(summary);}setImportSummaries(summaries);setStep('selectBank');}catch(err){setError(err instanceof Error?err.message:'Failed to process files');}finally{setIsProcessing(false);}},[]);const handleBankAccountSelect=useCallback(accountId=>{const account=bankAccounts.find(acc=>acc.id===accountId);if(account){setSelectedBankAccount(account);setStep('review');}},[bankAccounts]);const handleDownloadTemplate=useCallback(()=>{csvProcessingService.downloadTemplate();},[]);const handleTransactionEdit=useCallback((summaryIndex,transactionIndex,field,value)=>{setImportSummaries(prev=>{const updated=[...prev];updated[summaryIndex].transactions[transactionIndex]=_objectSpread(_objectSpread({},updated[summaryIndex].transactions[transactionIndex]),{},{[field]:value});return updated;});},[]);const handleConfirmImport=useCallback(()=>{if(!selectedBankAccount)return;const allTransactions=importSummaries.flatMap(summary=>summary.transactions);if(onImportComplete){onImportComplete(allTransactions,selectedBankAccount);}// Reset state\nsetStep('upload');setFiles([]);setSelectedBankAccount(null);setImportSummaries([]);},[selectedBankAccount,importSummaries,onImportComplete]);const handleCancel=useCallback(()=>{setStep('upload');setFiles([]);setSelectedBankAccount(null);setImportSummaries([]);setError(null);},[]);const formatCurrency=amount=>{return new Intl.NumberFormat('en-US',{style:'currency',currency:'USD'}).format(amount);};const totalTransactions=importSummaries.reduce((sum,summary)=>sum+summary.totalTransactions,0);const totalDebitAmount=importSummaries.reduce((sum,summary)=>sum+summary.totalDebitAmount,0);const totalCreditAmount=importSummaries.reduce((sum,summary)=>sum+summary.totalCreditAmount,0);const totalValidationErrors=importSummaries.reduce((sum,summary)=>sum+summary.validationErrors.length,0);return/*#__PURE__*/_jsxs(\"div\",{className:\"bank-statement-import\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"import-header\",children:[/*#__PURE__*/_jsx(\"h2\",{className:\"import-title\",children:\"Bank Statement Import\"}),/*#__PURE__*/_jsx(\"p\",{className:\"import-description\",children:\"Import CSV bank statements to process transactions automatically\"})]}),step==='upload'&&/*#__PURE__*/_jsxs(\"div\",{className:\"import-step\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"step-header\",children:[/*#__PURE__*/_jsx(\"h3\",{children:\"Step 1: Upload CSV Files\"}),/*#__PURE__*/_jsx(\"button\",{type:\"button\",onClick:handleDownloadTemplate,className:\"btn btn-secondary btn-sm\",children:\"Download CSV Template\"})]}),/*#__PURE__*/_jsx(FileUpload,{onFilesSelected:handleFilesSelected,disabled:isProcessing}),isProcessing&&/*#__PURE__*/_jsxs(\"div\",{className:\"processing-indicator\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"spinner\"}),/*#__PURE__*/_jsx(\"p\",{children:\"Processing files...\"})]}),error&&/*#__PURE__*/_jsx(\"div\",{className:\"error-message\",children:/*#__PURE__*/_jsx(\"p\",{children:error})})]}),step==='selectBank'&&/*#__PURE__*/_jsxs(\"div\",{className:\"import-step\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"step-header\",children:[/*#__PURE__*/_jsx(\"h3\",{children:\"Step 2: Select Bank Account\"}),/*#__PURE__*/_jsx(\"p\",{children:\"Choose which bank account these transactions belong to\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"bank-selection\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"form-group\",children:[/*#__PURE__*/_jsx(\"label\",{htmlFor:\"bank-account-select\",className:\"form-label\",children:\"Bank Account\"}),/*#__PURE__*/_jsxs(\"select\",{id:\"bank-account-select\",className:\"form-select\",onChange:e=>handleBankAccountSelect(e.target.value),defaultValue:\"\",children:[/*#__PURE__*/_jsx(\"option\",{value:\"\",children:\"Select a bank account...\"}),bankAccounts.map(account=>/*#__PURE__*/_jsxs(\"option\",{value:account.id,children:[account.name,\" - \",account.bankName,\" (\",account.accountNumber,\")\"]},account.id))]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"import-summary-preview\",children:[/*#__PURE__*/_jsx(\"h4\",{children:\"Import Summary\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"summary-stats\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"stat-item\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"stat-label\",children:\"Files:\"}),/*#__PURE__*/_jsx(\"span\",{className:\"stat-value\",children:files.length})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"stat-item\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"stat-label\",children:\"Total Transactions:\"}),/*#__PURE__*/_jsx(\"span\",{className:\"stat-value\",children:totalTransactions})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"stat-item\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"stat-label\",children:\"Total Debits:\"}),/*#__PURE__*/_jsx(\"span\",{className:\"stat-value text-error\",children:formatCurrency(totalDebitAmount)})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"stat-item\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"stat-label\",children:\"Total Credits:\"}),/*#__PURE__*/_jsx(\"span\",{className:\"stat-value text-success\",children:formatCurrency(totalCreditAmount)})]}),totalValidationErrors>0&&/*#__PURE__*/_jsxs(\"div\",{className:\"stat-item\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"stat-label\",children:\"Validation Errors:\"}),/*#__PURE__*/_jsx(\"span\",{className:\"stat-value text-warning\",children:totalValidationErrors})]})]})]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"step-actions\",children:/*#__PURE__*/_jsx(\"button\",{type:\"button\",onClick:handleCancel,className:\"btn btn-secondary\",children:\"Cancel\"})})]}),step==='review'&&selectedBankAccount&&/*#__PURE__*/_jsxs(\"div\",{className:\"import-step\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"step-header\",children:[/*#__PURE__*/_jsx(\"h3\",{children:\"Step 3: Review & Edit Transactions\"}),/*#__PURE__*/_jsx(\"p\",{children:\"Review the imported transactions and make any necessary adjustments\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"account-info\",children:[/*#__PURE__*/_jsx(\"h4\",{children:\"Selected Account\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"account-details\",children:[/*#__PURE__*/_jsx(\"p\",{children:/*#__PURE__*/_jsx(\"strong\",{children:selectedBankAccount.name})}),/*#__PURE__*/_jsxs(\"p\",{children:[selectedBankAccount.bankName,\" - \",selectedBankAccount.accountNumber]}),/*#__PURE__*/_jsxs(\"p\",{children:[\"Current Balance: \",formatCurrency(selectedBankAccount.currentBalance)]}),importSummaries.length>0&&/*#__PURE__*/_jsxs(\"div\",{className:\"balance-comparison\",children:[/*#__PURE__*/_jsxs(\"p\",{children:[\"Import Closing Balance: \",formatCurrency(importSummaries[importSummaries.length-1].closingBalance)]}),Math.abs(selectedBankAccount.currentBalance-importSummaries[importSummaries.length-1].closingBalance)>0.01&&/*#__PURE__*/_jsx(\"div\",{className:\"balance-warning\",children:\"\\u26A0\\uFE0F Notice: Current account balance differs from import closing balance. Please verify this is expected.\"})]})]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"import-summaries\",children:importSummaries.map((summary,summaryIndex)=>/*#__PURE__*/_jsxs(\"div\",{className:\"import-summary-card\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"summary-header\",children:[/*#__PURE__*/_jsx(\"h4\",{children:summary.fileName}),/*#__PURE__*/_jsxs(\"div\",{className:\"summary-stats\",children:[/*#__PURE__*/_jsxs(\"span\",{children:[summary.totalTransactions,\" transactions\"]}),/*#__PURE__*/_jsxs(\"span\",{children:[\"Period: \",summary.dateRange.from,\" to \",summary.dateRange.to]})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"balance-summary\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"balance-row\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"balance-label\",children:\"Opening Balance:\"}),/*#__PURE__*/_jsx(\"span\",{className:\"balance-value\",children:formatCurrency(summary.openingBalance)})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"balance-row\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"balance-label\",children:\"Daily Movement:\"}),/*#__PURE__*/_jsxs(\"span\",{className:\"balance-value \".concat(summary.dailyMovement>=0?'text-success':'text-error'),children:[summary.dailyMovement>=0?'+':'',formatCurrency(summary.dailyMovement)]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"balance-row\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"balance-label\",children:\"Closing Balance:\"}),/*#__PURE__*/_jsx(\"span\",{className:\"balance-value\",children:formatCurrency(summary.closingBalance)})]})]}),summary.validationErrors.length>0&&/*#__PURE__*/_jsxs(\"div\",{className:\"validation-errors\",children:[/*#__PURE__*/_jsx(\"h5\",{children:\"Validation Errors\"}),/*#__PURE__*/_jsx(\"ul\",{children:summary.validationErrors.map((error,index)=>/*#__PURE__*/_jsxs(\"li\",{className:\"validation-error\",children:[\"Row \",error.row,\", \",error.field,\": \",error.message]},index))})]}),/*#__PURE__*/_jsx(\"div\",{className:\"transactions-table-container\",children:/*#__PURE__*/_jsxs(\"table\",{className:\"transactions-table\",children:[/*#__PURE__*/_jsx(\"thead\",{children:/*#__PURE__*/_jsxs(\"tr\",{children:[/*#__PURE__*/_jsx(\"th\",{children:\"Date\"}),/*#__PURE__*/_jsx(\"th\",{children:\"Description\"}),/*#__PURE__*/_jsx(\"th\",{children:\"Debit\"}),/*#__PURE__*/_jsx(\"th\",{children:\"Credit\"}),/*#__PURE__*/_jsx(\"th\",{children:\"Balance\"}),/*#__PURE__*/_jsx(\"th\",{children:\"Reference\"})]})}),/*#__PURE__*/_jsx(\"tbody\",{children:summary.transactions.map((transaction,transactionIndex)=>/*#__PURE__*/_jsxs(\"tr\",{children:[/*#__PURE__*/_jsx(\"td\",{children:/*#__PURE__*/_jsx(\"input\",{type:\"date\",value:transaction.date,onChange:e=>handleTransactionEdit(summaryIndex,transactionIndex,'date',e.target.value),className:\"form-input\"})}),/*#__PURE__*/_jsx(\"td\",{children:/*#__PURE__*/_jsx(\"input\",{type:\"text\",value:transaction.description,onChange:e=>handleTransactionEdit(summaryIndex,transactionIndex,'description',e.target.value),className:\"form-input\"})}),/*#__PURE__*/_jsx(\"td\",{children:/*#__PURE__*/_jsx(\"input\",{type:\"number\",step:\"0.01\",value:transaction.debitAmount?transaction.debitAmount.toFixed(2):'',onChange:e=>handleTransactionEdit(summaryIndex,transactionIndex,'debitAmount',parseFloat(e.target.value)||0),className:\"form-input\",placeholder:\"0.00\"})}),/*#__PURE__*/_jsx(\"td\",{children:/*#__PURE__*/_jsx(\"input\",{type:\"number\",step:\"0.01\",value:transaction.creditAmount?transaction.creditAmount.toFixed(2):'',onChange:e=>handleTransactionEdit(summaryIndex,transactionIndex,'creditAmount',parseFloat(e.target.value)||0),className:\"form-input\",placeholder:\"0.00\"})}),/*#__PURE__*/_jsx(\"td\",{children:/*#__PURE__*/_jsx(\"input\",{type:\"number\",step:\"0.01\",value:transaction.balance.toFixed(2),onChange:e=>handleTransactionEdit(summaryIndex,transactionIndex,'balance',parseFloat(e.target.value)||0),className:\"form-input\"})}),/*#__PURE__*/_jsx(\"td\",{children:/*#__PURE__*/_jsx(\"input\",{type:\"text\",value:transaction.reference||'',onChange:e=>handleTransactionEdit(summaryIndex,transactionIndex,'reference',e.target.value),className:\"form-input\"})})]},transaction.id))})]})})]},summaryIndex))}),/*#__PURE__*/_jsxs(\"div\",{className:\"step-actions\",children:[/*#__PURE__*/_jsx(\"button\",{type:\"button\",onClick:handleCancel,className:\"btn btn-secondary\",children:\"Cancel\"}),/*#__PURE__*/_jsx(\"button\",{type:\"button\",onClick:()=>setStep('confirm'),className:\"btn btn-primary\",disabled:totalValidationErrors>0,children:\"Proceed to Confirmation\"})]})]}),step==='confirm'&&selectedBankAccount&&/*#__PURE__*/_jsxs(\"div\",{className:\"import-step\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"step-header\",children:[/*#__PURE__*/_jsx(\"h3\",{children:\"Step 4: Confirm Import\"}),/*#__PURE__*/_jsx(\"p\",{children:\"Please review the final summary before proceeding with the import\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"confirmation-summary\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"summary-section\",children:[/*#__PURE__*/_jsx(\"h4\",{children:\"Account Information\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"account-details\",children:[/*#__PURE__*/_jsx(\"p\",{children:/*#__PURE__*/_jsx(\"strong\",{children:selectedBankAccount.name})}),/*#__PURE__*/_jsxs(\"p\",{children:[selectedBankAccount.bankName,\" - \",selectedBankAccount.accountNumber]}),/*#__PURE__*/_jsxs(\"p\",{children:[\"Current Balance: \",formatCurrency(selectedBankAccount.currentBalance)]})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"summary-section\",children:[/*#__PURE__*/_jsx(\"h4\",{children:\"Import Summary\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"summary-stats\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"stat-item\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"stat-label\",children:\"Files Processed:\"}),/*#__PURE__*/_jsx(\"span\",{className:\"stat-value\",children:files.length})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"stat-item\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"stat-label\",children:\"Total Transactions:\"}),/*#__PURE__*/_jsx(\"span\",{className:\"stat-value\",children:totalTransactions})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"stat-item\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"stat-label\",children:\"Total Debit Amount:\"}),/*#__PURE__*/_jsx(\"span\",{className:\"stat-value text-error\",children:formatCurrency(totalDebitAmount)})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"stat-item\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"stat-label\",children:\"Total Credit Amount:\"}),/*#__PURE__*/_jsx(\"span\",{className:\"stat-value text-success\",children:formatCurrency(totalCreditAmount)})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"stat-item\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"stat-label\",children:\"Net Change:\"}),/*#__PURE__*/_jsx(\"span\",{className:\"stat-value \".concat(totalCreditAmount-totalDebitAmount>=0?'text-success':'text-error'),children:formatCurrency(totalCreditAmount-totalDebitAmount)})]})]})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"confirmation-question\",children:[/*#__PURE__*/_jsx(\"h4\",{children:\"Do you want to proceed with this import?\"}),/*#__PURE__*/_jsx(\"p\",{children:\"This action will add all transactions to the selected bank account.\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"step-actions\",children:[/*#__PURE__*/_jsx(\"button\",{type:\"button\",onClick:handleCancel,className:\"btn btn-danger\",children:\"No, Cancel\"}),/*#__PURE__*/_jsx(\"button\",{type:\"button\",onClick:handleConfirmImport,className:\"btn btn-success btn-lg\",children:\"Yes, Proceed\"})]})]})]});};", "map": {"version": 3, "names": ["React", "useState", "useCallback", "FileUpload", "csvProcessingService", "bankAccountService", "jsx", "_jsx", "jsxs", "_jsxs", "BankStatementImport", "_ref", "onImportComplete", "step", "setStep", "files", "setFiles", "selected<PERSON><PERSON>kAccount", "setSelectedBankAccount", "importSummaries", "setImportSummaries", "isProcessing", "setIsProcessing", "error", "setError", "bankAccounts", "getAllAccounts", "handleFilesSelected", "selectedFiles", "summaries", "file", "summary", "processFile", "push", "err", "Error", "message", "handleBankAccountSelect", "accountId", "account", "find", "acc", "id", "handleDownloadTemplate", "downloadTemplate", "handleTransactionEdit", "summaryIndex", "transactionIndex", "field", "value", "prev", "updated", "transactions", "_objectSpread", "handleConfirmImport", "allTransactions", "flatMap", "handleCancel", "formatCurrency", "amount", "Intl", "NumberFormat", "style", "currency", "format", "totalTransactions", "reduce", "sum", "totalDebitAmount", "totalCreditAmount", "totalValidationErrors", "validationErrors", "length", "className", "children", "type", "onClick", "onFilesSelected", "disabled", "htmlFor", "onChange", "e", "target", "defaultValue", "map", "name", "bankName", "accountNumber", "currentBalance", "closingBalance", "Math", "abs", "fileName", "date<PERSON><PERSON><PERSON>", "from", "to", "openingBalance", "concat", "dailyMovement", "index", "row", "transaction", "date", "description", "debitAmount", "toFixed", "parseFloat", "placeholder", "creditAmount", "balance", "reference"], "sources": ["C:/tmsft/src/components/BankStatementImport.tsx"], "sourcesContent": ["import React, { useState, useCallback } from 'react';\r\nimport { FileUpload } from './FileUpload';\r\nimport { ImportSummary, BankAccount, Transaction } from '../types';\r\nimport { csvProcessingService } from '../services/csvProcessingService';\r\nimport { bankAccountService } from '../services/bankAccountService';\r\nimport './BankStatementImport.css';\r\n\r\ninterface BankStatementImportProps {\r\n  onImportComplete?: (transactions: Transaction[], bankAccount: BankAccount) => void;\r\n}\r\n\r\nexport const BankStatementImport: React.FC<BankStatementImportProps> = ({\r\n  onImportComplete\r\n}) => {\r\n  const [step, setStep] = useState<'upload' | 'selectBank' | 'review' | 'confirm'>('upload');\r\n  const [files, setFiles] = useState<File[]>([]);\r\n  const [selectedBankAccount, setSelectedBankAccount] = useState<BankAccount | null>(null);\r\n  const [importSummaries, setImportSummaries] = useState<ImportSummary[]>([]);\r\n  const [isProcessing, setIsProcessing] = useState(false);\r\n  const [error, setError] = useState<string | null>(null);\r\n  const [bankAccounts] = useState<BankAccount[]>(bankAccountService.getAllAccounts());\r\n\r\n  const handleFilesSelected = useCallback(async (selectedFiles: File[]) => {\r\n    setFiles(selectedFiles);\r\n    setIsProcessing(true);\r\n    setError(null);\r\n\r\n    try {\r\n      const summaries: ImportSummary[] = [];\r\n      \r\n      for (const file of selectedFiles) {\r\n        const summary = await csvProcessingService.processFile(file);\r\n        summaries.push(summary);\r\n      }\r\n      \r\n      setImportSummaries(summaries);\r\n      setStep('selectBank');\r\n    } catch (err) {\r\n      setError(err instanceof Error ? err.message : 'Failed to process files');\r\n    } finally {\r\n      setIsProcessing(false);\r\n    }\r\n  }, []);\r\n\r\n  const handleBankAccountSelect = useCallback((accountId: string) => {\r\n    const account = bankAccounts.find(acc => acc.id === accountId);\r\n    if (account) {\r\n      setSelectedBankAccount(account);\r\n      setStep('review');\r\n    }\r\n  }, [bankAccounts]);\r\n\r\n  const handleDownloadTemplate = useCallback(() => {\r\n    csvProcessingService.downloadTemplate();\r\n  }, []);\r\n\r\n  const handleTransactionEdit = useCallback((\r\n    summaryIndex: number, \r\n    transactionIndex: number, \r\n    field: keyof Transaction, \r\n    value: string | number\r\n  ) => {\r\n    setImportSummaries(prev => {\r\n      const updated = [...prev];\r\n      updated[summaryIndex].transactions[transactionIndex] = {\r\n        ...updated[summaryIndex].transactions[transactionIndex],\r\n        [field]: value\r\n      };\r\n      return updated;\r\n    });\r\n  }, []);\r\n\r\n  const handleConfirmImport = useCallback(() => {\r\n    if (!selectedBankAccount) return;\r\n\r\n    const allTransactions = importSummaries.flatMap(summary => summary.transactions);\r\n    \r\n    if (onImportComplete) {\r\n      onImportComplete(allTransactions, selectedBankAccount);\r\n    }\r\n    \r\n    // Reset state\r\n    setStep('upload');\r\n    setFiles([]);\r\n    setSelectedBankAccount(null);\r\n    setImportSummaries([]);\r\n  }, [selectedBankAccount, importSummaries, onImportComplete]);\r\n\r\n  const handleCancel = useCallback(() => {\r\n    setStep('upload');\r\n    setFiles([]);\r\n    setSelectedBankAccount(null);\r\n    setImportSummaries([]);\r\n    setError(null);\r\n  }, []);\r\n\r\n  const formatCurrency = (amount: number): string => {\r\n    return new Intl.NumberFormat('en-US', {\r\n      style: 'currency',\r\n      currency: 'USD'\r\n    }).format(amount);\r\n  };\r\n\r\n  const totalTransactions = importSummaries.reduce((sum, summary) => sum + summary.totalTransactions, 0);\r\n  const totalDebitAmount = importSummaries.reduce((sum, summary) => sum + summary.totalDebitAmount, 0);\r\n  const totalCreditAmount = importSummaries.reduce((sum, summary) => sum + summary.totalCreditAmount, 0);\r\n  const totalValidationErrors = importSummaries.reduce((sum, summary) => sum + summary.validationErrors.length, 0);\r\n\r\n  return (\r\n    <div className=\"bank-statement-import\">\r\n      <div className=\"import-header\">\r\n        <h2 className=\"import-title\">Bank Statement Import</h2>\r\n        <p className=\"import-description\">\r\n          Import CSV bank statements to process transactions automatically\r\n        </p>\r\n      </div>\r\n\r\n      {/* Step 1: File Upload */}\r\n      {step === 'upload' && (\r\n        <div className=\"import-step\">\r\n          <div className=\"step-header\">\r\n            <h3>Step 1: Upload CSV Files</h3>\r\n            <button\r\n              type=\"button\"\r\n              onClick={handleDownloadTemplate}\r\n              className=\"btn btn-secondary btn-sm\"\r\n            >\r\n              Download CSV Template\r\n            </button>\r\n          </div>\r\n          \r\n          <FileUpload\r\n            onFilesSelected={handleFilesSelected}\r\n            disabled={isProcessing}\r\n          />\r\n          \r\n          {isProcessing && (\r\n            <div className=\"processing-indicator\">\r\n              <div className=\"spinner\"></div>\r\n              <p>Processing files...</p>\r\n            </div>\r\n          )}\r\n          \r\n          {error && (\r\n            <div className=\"error-message\">\r\n              <p>{error}</p>\r\n            </div>\r\n          )}\r\n        </div>\r\n      )}\r\n\r\n      {/* Step 2: Select Bank Account */}\r\n      {step === 'selectBank' && (\r\n        <div className=\"import-step\">\r\n          <div className=\"step-header\">\r\n            <h3>Step 2: Select Bank Account</h3>\r\n            <p>Choose which bank account these transactions belong to</p>\r\n          </div>\r\n          \r\n          <div className=\"bank-selection\">\r\n            <div className=\"form-group\">\r\n              <label htmlFor=\"bank-account-select\" className=\"form-label\">\r\n                Bank Account\r\n              </label>\r\n              <select\r\n                id=\"bank-account-select\"\r\n                className=\"form-select\"\r\n                onChange={(e) => handleBankAccountSelect(e.target.value)}\r\n                defaultValue=\"\"\r\n              >\r\n                <option value=\"\">Select a bank account...</option>\r\n                {bankAccounts.map(account => (\r\n                  <option key={account.id} value={account.id}>\r\n                    {account.name} - {account.bankName} ({account.accountNumber})\r\n                  </option>\r\n                ))}\r\n              </select>\r\n            </div>\r\n            \r\n            <div className=\"import-summary-preview\">\r\n              <h4>Import Summary</h4>\r\n              <div className=\"summary-stats\">\r\n                <div className=\"stat-item\">\r\n                  <span className=\"stat-label\">Files:</span>\r\n                  <span className=\"stat-value\">{files.length}</span>\r\n                </div>\r\n                <div className=\"stat-item\">\r\n                  <span className=\"stat-label\">Total Transactions:</span>\r\n                  <span className=\"stat-value\">{totalTransactions}</span>\r\n                </div>\r\n                <div className=\"stat-item\">\r\n                  <span className=\"stat-label\">Total Debits:</span>\r\n                  <span className=\"stat-value text-error\">{formatCurrency(totalDebitAmount)}</span>\r\n                </div>\r\n                <div className=\"stat-item\">\r\n                  <span className=\"stat-label\">Total Credits:</span>\r\n                  <span className=\"stat-value text-success\">{formatCurrency(totalCreditAmount)}</span>\r\n                </div>\r\n                {totalValidationErrors > 0 && (\r\n                  <div className=\"stat-item\">\r\n                    <span className=\"stat-label\">Validation Errors:</span>\r\n                    <span className=\"stat-value text-warning\">{totalValidationErrors}</span>\r\n                  </div>\r\n                )}\r\n              </div>\r\n            </div>\r\n          </div>\r\n          \r\n          <div className=\"step-actions\">\r\n            <button\r\n              type=\"button\"\r\n              onClick={handleCancel}\r\n              className=\"btn btn-secondary\"\r\n            >\r\n              Cancel\r\n            </button>\r\n          </div>\r\n        </div>\r\n      )}\r\n\r\n      {/* Step 3: Review and Edit */}\r\n      {step === 'review' && selectedBankAccount && (\r\n        <div className=\"import-step\">\r\n          <div className=\"step-header\">\r\n            <h3>Step 3: Review & Edit Transactions</h3>\r\n            <p>Review the imported transactions and make any necessary adjustments</p>\r\n          </div>\r\n          \r\n          <div className=\"account-info\">\r\n            <h4>Selected Account</h4>\r\n            <div className=\"account-details\">\r\n              <p><strong>{selectedBankAccount.name}</strong></p>\r\n              <p>{selectedBankAccount.bankName} - {selectedBankAccount.accountNumber}</p>\r\n              <p>Current Balance: {formatCurrency(selectedBankAccount.currentBalance)}</p>\r\n              {importSummaries.length > 0 && (\r\n                <div className=\"balance-comparison\">\r\n                  <p>Import Closing Balance: {formatCurrency(importSummaries[importSummaries.length - 1].closingBalance)}</p>\r\n                  {Math.abs(selectedBankAccount.currentBalance - importSummaries[importSummaries.length - 1].closingBalance) > 0.01 && (\r\n                    <div className=\"balance-warning\">\r\n                      ⚠️ Notice: Current account balance differs from import closing balance. Please verify this is expected.\r\n                    </div>\r\n                  )}\r\n                </div>\r\n              )}\r\n            </div>\r\n          </div>\r\n\r\n          <div className=\"import-summaries\">\r\n            {importSummaries.map((summary, summaryIndex) => (\r\n              <div key={summaryIndex} className=\"import-summary-card\">\r\n                <div className=\"summary-header\">\r\n                  <h4>{summary.fileName}</h4>\r\n                  <div className=\"summary-stats\">\r\n                    <span>{summary.totalTransactions} transactions</span>\r\n                    <span>Period: {summary.dateRange.from} to {summary.dateRange.to}</span>\r\n                  </div>\r\n                </div>\r\n                \r\n                <div className=\"balance-summary\">\r\n                  <div className=\"balance-row\">\r\n                    <span className=\"balance-label\">Opening Balance:</span>\r\n                    <span className=\"balance-value\">{formatCurrency(summary.openingBalance)}</span>\r\n                  </div>\r\n                  <div className=\"balance-row\">\r\n                    <span className=\"balance-label\">Daily Movement:</span>\r\n                    <span className={`balance-value ${summary.dailyMovement >= 0 ? 'text-success' : 'text-error'}`}>\r\n                      {summary.dailyMovement >= 0 ? '+' : ''}{formatCurrency(summary.dailyMovement)}\r\n                    </span>\r\n                  </div>\r\n                  <div className=\"balance-row\">\r\n                    <span className=\"balance-label\">Closing Balance:</span>\r\n                    <span className=\"balance-value\">{formatCurrency(summary.closingBalance)}</span>\r\n                  </div>\r\n                </div>\r\n                \r\n                {summary.validationErrors.length > 0 && (\r\n                  <div className=\"validation-errors\">\r\n                    <h5>Validation Errors</h5>\r\n                    <ul>\r\n                      {summary.validationErrors.map((error, index) => (\r\n                        <li key={index} className=\"validation-error\">\r\n                          Row {error.row}, {error.field}: {error.message}\r\n                        </li>\r\n                      ))}\r\n                    </ul>\r\n                  </div>\r\n                )}\r\n                \r\n                <div className=\"transactions-table-container\">\r\n                  <table className=\"transactions-table\">\r\n                    <thead>\r\n                      <tr>\r\n                        <th>Date</th>\r\n                        <th>Description</th>\r\n                        <th>Debit</th>\r\n                        <th>Credit</th>\r\n                        <th>Balance</th>\r\n                        <th>Reference</th>\r\n                      </tr>\r\n                    </thead>\r\n                    <tbody>\r\n                      {summary.transactions.map((transaction, transactionIndex) => (\r\n                        <tr key={transaction.id}>\r\n                          <td>\r\n                            <input\r\n                              type=\"date\"\r\n                              value={transaction.date}\r\n                              onChange={(e) => handleTransactionEdit(summaryIndex, transactionIndex, 'date', e.target.value)}\r\n                              className=\"form-input\"\r\n                            />\r\n                          </td>\r\n                          <td>\r\n                            <input\r\n                              type=\"text\"\r\n                              value={transaction.description}\r\n                              onChange={(e) => handleTransactionEdit(summaryIndex, transactionIndex, 'description', e.target.value)}\r\n                              className=\"form-input\"\r\n                            />\r\n                          </td>\r\n                                                     <td>\r\n                             <input\r\n                               type=\"number\"\r\n                               step=\"0.01\"\r\n                               value={transaction.debitAmount ? transaction.debitAmount.toFixed(2) : ''}\r\n                               onChange={(e) => handleTransactionEdit(summaryIndex, transactionIndex, 'debitAmount', parseFloat(e.target.value) || 0)}\r\n                               className=\"form-input\"\r\n                               placeholder=\"0.00\"\r\n                             />\r\n                           </td>\r\n                           <td>\r\n                             <input\r\n                               type=\"number\"\r\n                               step=\"0.01\"\r\n                               value={transaction.creditAmount ? transaction.creditAmount.toFixed(2) : ''}\r\n                               onChange={(e) => handleTransactionEdit(summaryIndex, transactionIndex, 'creditAmount', parseFloat(e.target.value) || 0)}\r\n                               className=\"form-input\"\r\n                               placeholder=\"0.00\"\r\n                             />\r\n                           </td>\r\n                           <td>\r\n                             <input\r\n                               type=\"number\"\r\n                               step=\"0.01\"\r\n                               value={transaction.balance.toFixed(2)}\r\n                               onChange={(e) => handleTransactionEdit(summaryIndex, transactionIndex, 'balance', parseFloat(e.target.value) || 0)}\r\n                               className=\"form-input\"\r\n                             />\r\n                           </td>\r\n                          <td>\r\n                            <input\r\n                              type=\"text\"\r\n                              value={transaction.reference || ''}\r\n                              onChange={(e) => handleTransactionEdit(summaryIndex, transactionIndex, 'reference', e.target.value)}\r\n                              className=\"form-input\"\r\n                            />\r\n                          </td>\r\n                        </tr>\r\n                      ))}\r\n                    </tbody>\r\n                  </table>\r\n                </div>\r\n              </div>\r\n            ))}\r\n          </div>\r\n          \r\n          <div className=\"step-actions\">\r\n            <button\r\n              type=\"button\"\r\n              onClick={handleCancel}\r\n              className=\"btn btn-secondary\"\r\n            >\r\n              Cancel\r\n            </button>\r\n            <button\r\n              type=\"button\"\r\n              onClick={() => setStep('confirm')}\r\n              className=\"btn btn-primary\"\r\n              disabled={totalValidationErrors > 0}\r\n            >\r\n              Proceed to Confirmation\r\n            </button>\r\n          </div>\r\n        </div>\r\n      )}\r\n\r\n      {/* Step 4: Confirmation */}\r\n      {step === 'confirm' && selectedBankAccount && (\r\n        <div className=\"import-step\">\r\n          <div className=\"step-header\">\r\n            <h3>Step 4: Confirm Import</h3>\r\n            <p>Please review the final summary before proceeding with the import</p>\r\n          </div>\r\n          \r\n          <div className=\"confirmation-summary\">\r\n            <div className=\"summary-section\">\r\n              <h4>Account Information</h4>\r\n              <div className=\"account-details\">\r\n                <p><strong>{selectedBankAccount.name}</strong></p>\r\n                <p>{selectedBankAccount.bankName} - {selectedBankAccount.accountNumber}</p>\r\n                <p>Current Balance: {formatCurrency(selectedBankAccount.currentBalance)}</p>\r\n              </div>\r\n            </div>\r\n            \r\n            <div className=\"summary-section\">\r\n              <h4>Import Summary</h4>\r\n              <div className=\"summary-stats\">\r\n                <div className=\"stat-item\">\r\n                  <span className=\"stat-label\">Files Processed:</span>\r\n                  <span className=\"stat-value\">{files.length}</span>\r\n                </div>\r\n                <div className=\"stat-item\">\r\n                  <span className=\"stat-label\">Total Transactions:</span>\r\n                  <span className=\"stat-value\">{totalTransactions}</span>\r\n                </div>\r\n                <div className=\"stat-item\">\r\n                  <span className=\"stat-label\">Total Debit Amount:</span>\r\n                  <span className=\"stat-value text-error\">{formatCurrency(totalDebitAmount)}</span>\r\n                </div>\r\n                <div className=\"stat-item\">\r\n                  <span className=\"stat-label\">Total Credit Amount:</span>\r\n                  <span className=\"stat-value text-success\">{formatCurrency(totalCreditAmount)}</span>\r\n                </div>\r\n                <div className=\"stat-item\">\r\n                  <span className=\"stat-label\">Net Change:</span>\r\n                  <span className={`stat-value ${totalCreditAmount - totalDebitAmount >= 0 ? 'text-success' : 'text-error'}`}>\r\n                    {formatCurrency(totalCreditAmount - totalDebitAmount)}\r\n                  </span>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n          \r\n          <div className=\"confirmation-question\">\r\n            <h4>Do you want to proceed with this import?</h4>\r\n            <p>This action will add all transactions to the selected bank account.</p>\r\n          </div>\r\n          \r\n          <div className=\"step-actions\">\r\n            <button\r\n              type=\"button\"\r\n              onClick={handleCancel}\r\n              className=\"btn btn-danger\"\r\n            >\r\n              No, Cancel\r\n            </button>\r\n            <button\r\n              type=\"button\"\r\n              onClick={handleConfirmImport}\r\n              className=\"btn btn-success btn-lg\"\r\n            >\r\n              Yes, Proceed\r\n            </button>\r\n          </div>\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n}; "], "mappings": "6FAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,WAAW,KAAQ,OAAO,CACpD,OAASC,UAAU,KAAQ,cAAc,CAEzC,OAASC,oBAAoB,KAAQ,kCAAkC,CACvE,OAASC,kBAAkB,KAAQ,gCAAgC,CACnE,MAAO,2BAA2B,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAMnC,MAAO,MAAM,CAAAC,mBAAuD,CAAGC,IAAA,EAEjE,IAFkE,CACtEC,gBACF,CAAC,CAAAD,IAAA,CACC,KAAM,CAACE,IAAI,CAAEC,OAAO,CAAC,CAAGb,QAAQ,CAAiD,QAAQ,CAAC,CAC1F,KAAM,CAACc,KAAK,CAAEC,QAAQ,CAAC,CAAGf,QAAQ,CAAS,EAAE,CAAC,CAC9C,KAAM,CAACgB,mBAAmB,CAAEC,sBAAsB,CAAC,CAAGjB,QAAQ,CAAqB,IAAI,CAAC,CACxF,KAAM,CAACkB,eAAe,CAAEC,kBAAkB,CAAC,CAAGnB,QAAQ,CAAkB,EAAE,CAAC,CAC3E,KAAM,CAACoB,YAAY,CAAEC,eAAe,CAAC,CAAGrB,QAAQ,CAAC,KAAK,CAAC,CACvD,KAAM,CAACsB,KAAK,CAAEC,QAAQ,CAAC,CAAGvB,QAAQ,CAAgB,IAAI,CAAC,CACvD,KAAM,CAACwB,YAAY,CAAC,CAAGxB,QAAQ,CAAgBI,kBAAkB,CAACqB,cAAc,CAAC,CAAC,CAAC,CAEnF,KAAM,CAAAC,mBAAmB,CAAGzB,WAAW,CAAC,KAAO,CAAA0B,aAAqB,EAAK,CACvEZ,QAAQ,CAACY,aAAa,CAAC,CACvBN,eAAe,CAAC,IAAI,CAAC,CACrBE,QAAQ,CAAC,IAAI,CAAC,CAEd,GAAI,CACF,KAAM,CAAAK,SAA0B,CAAG,EAAE,CAErC,IAAK,KAAM,CAAAC,IAAI,GAAI,CAAAF,aAAa,CAAE,CAChC,KAAM,CAAAG,OAAO,CAAG,KAAM,CAAA3B,oBAAoB,CAAC4B,WAAW,CAACF,IAAI,CAAC,CAC5DD,SAAS,CAACI,IAAI,CAACF,OAAO,CAAC,CACzB,CAEAX,kBAAkB,CAACS,SAAS,CAAC,CAC7Bf,OAAO,CAAC,YAAY,CAAC,CACvB,CAAE,MAAOoB,GAAG,CAAE,CACZV,QAAQ,CAACU,GAAG,WAAY,CAAAC,KAAK,CAAGD,GAAG,CAACE,OAAO,CAAG,yBAAyB,CAAC,CAC1E,CAAC,OAAS,CACRd,eAAe,CAAC,KAAK,CAAC,CACxB,CACF,CAAC,CAAE,EAAE,CAAC,CAEN,KAAM,CAAAe,uBAAuB,CAAGnC,WAAW,CAAEoC,SAAiB,EAAK,CACjE,KAAM,CAAAC,OAAO,CAAGd,YAAY,CAACe,IAAI,CAACC,GAAG,EAAIA,GAAG,CAACC,EAAE,GAAKJ,SAAS,CAAC,CAC9D,GAAIC,OAAO,CAAE,CACXrB,sBAAsB,CAACqB,OAAO,CAAC,CAC/BzB,OAAO,CAAC,QAAQ,CAAC,CACnB,CACF,CAAC,CAAE,CAACW,YAAY,CAAC,CAAC,CAElB,KAAM,CAAAkB,sBAAsB,CAAGzC,WAAW,CAAC,IAAM,CAC/CE,oBAAoB,CAACwC,gBAAgB,CAAC,CAAC,CACzC,CAAC,CAAE,EAAE,CAAC,CAEN,KAAM,CAAAC,qBAAqB,CAAG3C,WAAW,CAAC,CACxC4C,YAAoB,CACpBC,gBAAwB,CACxBC,KAAwB,CACxBC,KAAsB,GACnB,CACH7B,kBAAkB,CAAC8B,IAAI,EAAI,CACzB,KAAM,CAAAC,OAAO,CAAG,CAAC,GAAGD,IAAI,CAAC,CACzBC,OAAO,CAACL,YAAY,CAAC,CAACM,YAAY,CAACL,gBAAgB,CAAC,CAAAM,aAAA,CAAAA,aAAA,IAC/CF,OAAO,CAACL,YAAY,CAAC,CAACM,YAAY,CAACL,gBAAgB,CAAC,MACvD,CAACC,KAAK,EAAGC,KAAK,EACf,CACD,MAAO,CAAAE,OAAO,CAChB,CAAC,CAAC,CACJ,CAAC,CAAE,EAAE,CAAC,CAEN,KAAM,CAAAG,mBAAmB,CAAGpD,WAAW,CAAC,IAAM,CAC5C,GAAI,CAACe,mBAAmB,CAAE,OAE1B,KAAM,CAAAsC,eAAe,CAAGpC,eAAe,CAACqC,OAAO,CAACzB,OAAO,EAAIA,OAAO,CAACqB,YAAY,CAAC,CAEhF,GAAIxC,gBAAgB,CAAE,CACpBA,gBAAgB,CAAC2C,eAAe,CAAEtC,mBAAmB,CAAC,CACxD,CAEA;AACAH,OAAO,CAAC,QAAQ,CAAC,CACjBE,QAAQ,CAAC,EAAE,CAAC,CACZE,sBAAsB,CAAC,IAAI,CAAC,CAC5BE,kBAAkB,CAAC,EAAE,CAAC,CACxB,CAAC,CAAE,CAACH,mBAAmB,CAAEE,eAAe,CAAEP,gBAAgB,CAAC,CAAC,CAE5D,KAAM,CAAA6C,YAAY,CAAGvD,WAAW,CAAC,IAAM,CACrCY,OAAO,CAAC,QAAQ,CAAC,CACjBE,QAAQ,CAAC,EAAE,CAAC,CACZE,sBAAsB,CAAC,IAAI,CAAC,CAC5BE,kBAAkB,CAAC,EAAE,CAAC,CACtBI,QAAQ,CAAC,IAAI,CAAC,CAChB,CAAC,CAAE,EAAE,CAAC,CAEN,KAAM,CAAAkC,cAAc,CAAIC,MAAc,EAAa,CACjD,MAAO,IAAI,CAAAC,IAAI,CAACC,YAAY,CAAC,OAAO,CAAE,CACpCC,KAAK,CAAE,UAAU,CACjBC,QAAQ,CAAE,KACZ,CAAC,CAAC,CAACC,MAAM,CAACL,MAAM,CAAC,CACnB,CAAC,CAED,KAAM,CAAAM,iBAAiB,CAAG9C,eAAe,CAAC+C,MAAM,CAAC,CAACC,GAAG,CAAEpC,OAAO,GAAKoC,GAAG,CAAGpC,OAAO,CAACkC,iBAAiB,CAAE,CAAC,CAAC,CACtG,KAAM,CAAAG,gBAAgB,CAAGjD,eAAe,CAAC+C,MAAM,CAAC,CAACC,GAAG,CAAEpC,OAAO,GAAKoC,GAAG,CAAGpC,OAAO,CAACqC,gBAAgB,CAAE,CAAC,CAAC,CACpG,KAAM,CAAAC,iBAAiB,CAAGlD,eAAe,CAAC+C,MAAM,CAAC,CAACC,GAAG,CAAEpC,OAAO,GAAKoC,GAAG,CAAGpC,OAAO,CAACsC,iBAAiB,CAAE,CAAC,CAAC,CACtG,KAAM,CAAAC,qBAAqB,CAAGnD,eAAe,CAAC+C,MAAM,CAAC,CAACC,GAAG,CAAEpC,OAAO,GAAKoC,GAAG,CAAGpC,OAAO,CAACwC,gBAAgB,CAACC,MAAM,CAAE,CAAC,CAAC,CAEhH,mBACE/D,KAAA,QAAKgE,SAAS,CAAC,uBAAuB,CAAAC,QAAA,eACpCjE,KAAA,QAAKgE,SAAS,CAAC,eAAe,CAAAC,QAAA,eAC5BnE,IAAA,OAAIkE,SAAS,CAAC,cAAc,CAAAC,QAAA,CAAC,uBAAqB,CAAI,CAAC,cACvDnE,IAAA,MAAGkE,SAAS,CAAC,oBAAoB,CAAAC,QAAA,CAAC,kEAElC,CAAG,CAAC,EACD,CAAC,CAGL7D,IAAI,GAAK,QAAQ,eAChBJ,KAAA,QAAKgE,SAAS,CAAC,aAAa,CAAAC,QAAA,eAC1BjE,KAAA,QAAKgE,SAAS,CAAC,aAAa,CAAAC,QAAA,eAC1BnE,IAAA,OAAAmE,QAAA,CAAI,0BAAwB,CAAI,CAAC,cACjCnE,IAAA,WACEoE,IAAI,CAAC,QAAQ,CACbC,OAAO,CAAEjC,sBAAuB,CAChC8B,SAAS,CAAC,0BAA0B,CAAAC,QAAA,CACrC,uBAED,CAAQ,CAAC,EACN,CAAC,cAENnE,IAAA,CAACJ,UAAU,EACT0E,eAAe,CAAElD,mBAAoB,CACrCmD,QAAQ,CAAEzD,YAAa,CACxB,CAAC,CAEDA,YAAY,eACXZ,KAAA,QAAKgE,SAAS,CAAC,sBAAsB,CAAAC,QAAA,eACnCnE,IAAA,QAAKkE,SAAS,CAAC,SAAS,CAAM,CAAC,cAC/BlE,IAAA,MAAAmE,QAAA,CAAG,qBAAmB,CAAG,CAAC,EACvB,CACN,CAEAnD,KAAK,eACJhB,IAAA,QAAKkE,SAAS,CAAC,eAAe,CAAAC,QAAA,cAC5BnE,IAAA,MAAAmE,QAAA,CAAInD,KAAK,CAAI,CAAC,CACX,CACN,EACE,CACN,CAGAV,IAAI,GAAK,YAAY,eACpBJ,KAAA,QAAKgE,SAAS,CAAC,aAAa,CAAAC,QAAA,eAC1BjE,KAAA,QAAKgE,SAAS,CAAC,aAAa,CAAAC,QAAA,eAC1BnE,IAAA,OAAAmE,QAAA,CAAI,6BAA2B,CAAI,CAAC,cACpCnE,IAAA,MAAAmE,QAAA,CAAG,wDAAsD,CAAG,CAAC,EAC1D,CAAC,cAENjE,KAAA,QAAKgE,SAAS,CAAC,gBAAgB,CAAAC,QAAA,eAC7BjE,KAAA,QAAKgE,SAAS,CAAC,YAAY,CAAAC,QAAA,eACzBnE,IAAA,UAAOwE,OAAO,CAAC,qBAAqB,CAACN,SAAS,CAAC,YAAY,CAAAC,QAAA,CAAC,cAE5D,CAAO,CAAC,cACRjE,KAAA,WACEiC,EAAE,CAAC,qBAAqB,CACxB+B,SAAS,CAAC,aAAa,CACvBO,QAAQ,CAAGC,CAAC,EAAK5C,uBAAuB,CAAC4C,CAAC,CAACC,MAAM,CAACjC,KAAK,CAAE,CACzDkC,YAAY,CAAC,EAAE,CAAAT,QAAA,eAEfnE,IAAA,WAAQ0C,KAAK,CAAC,EAAE,CAAAyB,QAAA,CAAC,0BAAwB,CAAQ,CAAC,CACjDjD,YAAY,CAAC2D,GAAG,CAAC7C,OAAO,eACvB9B,KAAA,WAAyBwC,KAAK,CAAEV,OAAO,CAACG,EAAG,CAAAgC,QAAA,EACxCnC,OAAO,CAAC8C,IAAI,CAAC,KAAG,CAAC9C,OAAO,CAAC+C,QAAQ,CAAC,IAAE,CAAC/C,OAAO,CAACgD,aAAa,CAAC,GAC9D,GAFahD,OAAO,CAACG,EAEb,CACT,CAAC,EACI,CAAC,EACN,CAAC,cAENjC,KAAA,QAAKgE,SAAS,CAAC,wBAAwB,CAAAC,QAAA,eACrCnE,IAAA,OAAAmE,QAAA,CAAI,gBAAc,CAAI,CAAC,cACvBjE,KAAA,QAAKgE,SAAS,CAAC,eAAe,CAAAC,QAAA,eAC5BjE,KAAA,QAAKgE,SAAS,CAAC,WAAW,CAAAC,QAAA,eACxBnE,IAAA,SAAMkE,SAAS,CAAC,YAAY,CAAAC,QAAA,CAAC,QAAM,CAAM,CAAC,cAC1CnE,IAAA,SAAMkE,SAAS,CAAC,YAAY,CAAAC,QAAA,CAAE3D,KAAK,CAACyD,MAAM,CAAO,CAAC,EAC/C,CAAC,cACN/D,KAAA,QAAKgE,SAAS,CAAC,WAAW,CAAAC,QAAA,eACxBnE,IAAA,SAAMkE,SAAS,CAAC,YAAY,CAAAC,QAAA,CAAC,qBAAmB,CAAM,CAAC,cACvDnE,IAAA,SAAMkE,SAAS,CAAC,YAAY,CAAAC,QAAA,CAAET,iBAAiB,CAAO,CAAC,EACpD,CAAC,cACNxD,KAAA,QAAKgE,SAAS,CAAC,WAAW,CAAAC,QAAA,eACxBnE,IAAA,SAAMkE,SAAS,CAAC,YAAY,CAAAC,QAAA,CAAC,eAAa,CAAM,CAAC,cACjDnE,IAAA,SAAMkE,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAEhB,cAAc,CAACU,gBAAgB,CAAC,CAAO,CAAC,EAC9E,CAAC,cACN3D,KAAA,QAAKgE,SAAS,CAAC,WAAW,CAAAC,QAAA,eACxBnE,IAAA,SAAMkE,SAAS,CAAC,YAAY,CAAAC,QAAA,CAAC,gBAAc,CAAM,CAAC,cAClDnE,IAAA,SAAMkE,SAAS,CAAC,yBAAyB,CAAAC,QAAA,CAAEhB,cAAc,CAACW,iBAAiB,CAAC,CAAO,CAAC,EACjF,CAAC,CACLC,qBAAqB,CAAG,CAAC,eACxB7D,KAAA,QAAKgE,SAAS,CAAC,WAAW,CAAAC,QAAA,eACxBnE,IAAA,SAAMkE,SAAS,CAAC,YAAY,CAAAC,QAAA,CAAC,oBAAkB,CAAM,CAAC,cACtDnE,IAAA,SAAMkE,SAAS,CAAC,yBAAyB,CAAAC,QAAA,CAAEJ,qBAAqB,CAAO,CAAC,EACrE,CACN,EACE,CAAC,EACH,CAAC,EACH,CAAC,cAEN/D,IAAA,QAAKkE,SAAS,CAAC,cAAc,CAAAC,QAAA,cAC3BnE,IAAA,WACEoE,IAAI,CAAC,QAAQ,CACbC,OAAO,CAAEnB,YAAa,CACtBgB,SAAS,CAAC,mBAAmB,CAAAC,QAAA,CAC9B,QAED,CAAQ,CAAC,CACN,CAAC,EACH,CACN,CAGA7D,IAAI,GAAK,QAAQ,EAAII,mBAAmB,eACvCR,KAAA,QAAKgE,SAAS,CAAC,aAAa,CAAAC,QAAA,eAC1BjE,KAAA,QAAKgE,SAAS,CAAC,aAAa,CAAAC,QAAA,eAC1BnE,IAAA,OAAAmE,QAAA,CAAI,oCAAkC,CAAI,CAAC,cAC3CnE,IAAA,MAAAmE,QAAA,CAAG,qEAAmE,CAAG,CAAC,EACvE,CAAC,cAENjE,KAAA,QAAKgE,SAAS,CAAC,cAAc,CAAAC,QAAA,eAC3BnE,IAAA,OAAAmE,QAAA,CAAI,kBAAgB,CAAI,CAAC,cACzBjE,KAAA,QAAKgE,SAAS,CAAC,iBAAiB,CAAAC,QAAA,eAC9BnE,IAAA,MAAAmE,QAAA,cAAGnE,IAAA,WAAAmE,QAAA,CAASzD,mBAAmB,CAACoE,IAAI,CAAS,CAAC,CAAG,CAAC,cAClD5E,KAAA,MAAAiE,QAAA,EAAIzD,mBAAmB,CAACqE,QAAQ,CAAC,KAAG,CAACrE,mBAAmB,CAACsE,aAAa,EAAI,CAAC,cAC3E9E,KAAA,MAAAiE,QAAA,EAAG,mBAAiB,CAAChB,cAAc,CAACzC,mBAAmB,CAACuE,cAAc,CAAC,EAAI,CAAC,CAC3ErE,eAAe,CAACqD,MAAM,CAAG,CAAC,eACzB/D,KAAA,QAAKgE,SAAS,CAAC,oBAAoB,CAAAC,QAAA,eACjCjE,KAAA,MAAAiE,QAAA,EAAG,0BAAwB,CAAChB,cAAc,CAACvC,eAAe,CAACA,eAAe,CAACqD,MAAM,CAAG,CAAC,CAAC,CAACiB,cAAc,CAAC,EAAI,CAAC,CAC1GC,IAAI,CAACC,GAAG,CAAC1E,mBAAmB,CAACuE,cAAc,CAAGrE,eAAe,CAACA,eAAe,CAACqD,MAAM,CAAG,CAAC,CAAC,CAACiB,cAAc,CAAC,CAAG,IAAI,eAC/GlF,IAAA,QAAKkE,SAAS,CAAC,iBAAiB,CAAAC,QAAA,CAAC,mHAEjC,CAAK,CACN,EACE,CACN,EACE,CAAC,EACH,CAAC,cAENnE,IAAA,QAAKkE,SAAS,CAAC,kBAAkB,CAAAC,QAAA,CAC9BvD,eAAe,CAACiE,GAAG,CAAC,CAACrD,OAAO,CAAEe,YAAY,gBACzCrC,KAAA,QAAwBgE,SAAS,CAAC,qBAAqB,CAAAC,QAAA,eACrDjE,KAAA,QAAKgE,SAAS,CAAC,gBAAgB,CAAAC,QAAA,eAC7BnE,IAAA,OAAAmE,QAAA,CAAK3C,OAAO,CAAC6D,QAAQ,CAAK,CAAC,cAC3BnF,KAAA,QAAKgE,SAAS,CAAC,eAAe,CAAAC,QAAA,eAC5BjE,KAAA,SAAAiE,QAAA,EAAO3C,OAAO,CAACkC,iBAAiB,CAAC,eAAa,EAAM,CAAC,cACrDxD,KAAA,SAAAiE,QAAA,EAAM,UAAQ,CAAC3C,OAAO,CAAC8D,SAAS,CAACC,IAAI,CAAC,MAAI,CAAC/D,OAAO,CAAC8D,SAAS,CAACE,EAAE,EAAO,CAAC,EACpE,CAAC,EACH,CAAC,cAENtF,KAAA,QAAKgE,SAAS,CAAC,iBAAiB,CAAAC,QAAA,eAC9BjE,KAAA,QAAKgE,SAAS,CAAC,aAAa,CAAAC,QAAA,eAC1BnE,IAAA,SAAMkE,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAC,kBAAgB,CAAM,CAAC,cACvDnE,IAAA,SAAMkE,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAEhB,cAAc,CAAC3B,OAAO,CAACiE,cAAc,CAAC,CAAO,CAAC,EAC5E,CAAC,cACNvF,KAAA,QAAKgE,SAAS,CAAC,aAAa,CAAAC,QAAA,eAC1BnE,IAAA,SAAMkE,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAC,iBAAe,CAAM,CAAC,cACtDjE,KAAA,SAAMgE,SAAS,kBAAAwB,MAAA,CAAmBlE,OAAO,CAACmE,aAAa,EAAI,CAAC,CAAG,cAAc,CAAG,YAAY,CAAG,CAAAxB,QAAA,EAC5F3C,OAAO,CAACmE,aAAa,EAAI,CAAC,CAAG,GAAG,CAAG,EAAE,CAAExC,cAAc,CAAC3B,OAAO,CAACmE,aAAa,CAAC,EACzE,CAAC,EACJ,CAAC,cACNzF,KAAA,QAAKgE,SAAS,CAAC,aAAa,CAAAC,QAAA,eAC1BnE,IAAA,SAAMkE,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAC,kBAAgB,CAAM,CAAC,cACvDnE,IAAA,SAAMkE,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAEhB,cAAc,CAAC3B,OAAO,CAAC0D,cAAc,CAAC,CAAO,CAAC,EAC5E,CAAC,EACH,CAAC,CAEL1D,OAAO,CAACwC,gBAAgB,CAACC,MAAM,CAAG,CAAC,eAClC/D,KAAA,QAAKgE,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAChCnE,IAAA,OAAAmE,QAAA,CAAI,mBAAiB,CAAI,CAAC,cAC1BnE,IAAA,OAAAmE,QAAA,CACG3C,OAAO,CAACwC,gBAAgB,CAACa,GAAG,CAAC,CAAC7D,KAAK,CAAE4E,KAAK,gBACzC1F,KAAA,OAAgBgE,SAAS,CAAC,kBAAkB,CAAAC,QAAA,EAAC,MACvC,CAACnD,KAAK,CAAC6E,GAAG,CAAC,IAAE,CAAC7E,KAAK,CAACyB,KAAK,CAAC,IAAE,CAACzB,KAAK,CAACa,OAAO,GADvC+D,KAEL,CACL,CAAC,CACA,CAAC,EACF,CACN,cAED5F,IAAA,QAAKkE,SAAS,CAAC,8BAA8B,CAAAC,QAAA,cAC3CjE,KAAA,UAAOgE,SAAS,CAAC,oBAAoB,CAAAC,QAAA,eACnCnE,IAAA,UAAAmE,QAAA,cACEjE,KAAA,OAAAiE,QAAA,eACEnE,IAAA,OAAAmE,QAAA,CAAI,MAAI,CAAI,CAAC,cACbnE,IAAA,OAAAmE,QAAA,CAAI,aAAW,CAAI,CAAC,cACpBnE,IAAA,OAAAmE,QAAA,CAAI,OAAK,CAAI,CAAC,cACdnE,IAAA,OAAAmE,QAAA,CAAI,QAAM,CAAI,CAAC,cACfnE,IAAA,OAAAmE,QAAA,CAAI,SAAO,CAAI,CAAC,cAChBnE,IAAA,OAAAmE,QAAA,CAAI,WAAS,CAAI,CAAC,EAChB,CAAC,CACA,CAAC,cACRnE,IAAA,UAAAmE,QAAA,CACG3C,OAAO,CAACqB,YAAY,CAACgC,GAAG,CAAC,CAACiB,WAAW,CAAEtD,gBAAgB,gBACtDtC,KAAA,OAAAiE,QAAA,eACEnE,IAAA,OAAAmE,QAAA,cACEnE,IAAA,UACEoE,IAAI,CAAC,MAAM,CACX1B,KAAK,CAAEoD,WAAW,CAACC,IAAK,CACxBtB,QAAQ,CAAGC,CAAC,EAAKpC,qBAAqB,CAACC,YAAY,CAAEC,gBAAgB,CAAE,MAAM,CAAEkC,CAAC,CAACC,MAAM,CAACjC,KAAK,CAAE,CAC/FwB,SAAS,CAAC,YAAY,CACvB,CAAC,CACA,CAAC,cACLlE,IAAA,OAAAmE,QAAA,cACEnE,IAAA,UACEoE,IAAI,CAAC,MAAM,CACX1B,KAAK,CAAEoD,WAAW,CAACE,WAAY,CAC/BvB,QAAQ,CAAGC,CAAC,EAAKpC,qBAAqB,CAACC,YAAY,CAAEC,gBAAgB,CAAE,aAAa,CAAEkC,CAAC,CAACC,MAAM,CAACjC,KAAK,CAAE,CACtGwB,SAAS,CAAC,YAAY,CACvB,CAAC,CACA,CAAC,cACsBlE,IAAA,OAAAmE,QAAA,cACxBnE,IAAA,UACEoE,IAAI,CAAC,QAAQ,CACb9D,IAAI,CAAC,MAAM,CACXoC,KAAK,CAAEoD,WAAW,CAACG,WAAW,CAAGH,WAAW,CAACG,WAAW,CAACC,OAAO,CAAC,CAAC,CAAC,CAAG,EAAG,CACzEzB,QAAQ,CAAGC,CAAC,EAAKpC,qBAAqB,CAACC,YAAY,CAAEC,gBAAgB,CAAE,aAAa,CAAE2D,UAAU,CAACzB,CAAC,CAACC,MAAM,CAACjC,KAAK,CAAC,EAAI,CAAC,CAAE,CACvHwB,SAAS,CAAC,YAAY,CACtBkC,WAAW,CAAC,MAAM,CACnB,CAAC,CACA,CAAC,cACLpG,IAAA,OAAAmE,QAAA,cACEnE,IAAA,UACEoE,IAAI,CAAC,QAAQ,CACb9D,IAAI,CAAC,MAAM,CACXoC,KAAK,CAAEoD,WAAW,CAACO,YAAY,CAAGP,WAAW,CAACO,YAAY,CAACH,OAAO,CAAC,CAAC,CAAC,CAAG,EAAG,CAC3EzB,QAAQ,CAAGC,CAAC,EAAKpC,qBAAqB,CAACC,YAAY,CAAEC,gBAAgB,CAAE,cAAc,CAAE2D,UAAU,CAACzB,CAAC,CAACC,MAAM,CAACjC,KAAK,CAAC,EAAI,CAAC,CAAE,CACxHwB,SAAS,CAAC,YAAY,CACtBkC,WAAW,CAAC,MAAM,CACnB,CAAC,CACA,CAAC,cACLpG,IAAA,OAAAmE,QAAA,cACEnE,IAAA,UACEoE,IAAI,CAAC,QAAQ,CACb9D,IAAI,CAAC,MAAM,CACXoC,KAAK,CAAEoD,WAAW,CAACQ,OAAO,CAACJ,OAAO,CAAC,CAAC,CAAE,CACtCzB,QAAQ,CAAGC,CAAC,EAAKpC,qBAAqB,CAACC,YAAY,CAAEC,gBAAgB,CAAE,SAAS,CAAE2D,UAAU,CAACzB,CAAC,CAACC,MAAM,CAACjC,KAAK,CAAC,EAAI,CAAC,CAAE,CACnHwB,SAAS,CAAC,YAAY,CACvB,CAAC,CACA,CAAC,cACNlE,IAAA,OAAAmE,QAAA,cACEnE,IAAA,UACEoE,IAAI,CAAC,MAAM,CACX1B,KAAK,CAAEoD,WAAW,CAACS,SAAS,EAAI,EAAG,CACnC9B,QAAQ,CAAGC,CAAC,EAAKpC,qBAAqB,CAACC,YAAY,CAAEC,gBAAgB,CAAE,WAAW,CAAEkC,CAAC,CAACC,MAAM,CAACjC,KAAK,CAAE,CACpGwB,SAAS,CAAC,YAAY,CACvB,CAAC,CACA,CAAC,GArDE4B,WAAW,CAAC3D,EAsDjB,CACL,CAAC,CACG,CAAC,EACH,CAAC,CACL,CAAC,GA/GEI,YAgHL,CACN,CAAC,CACC,CAAC,cAENrC,KAAA,QAAKgE,SAAS,CAAC,cAAc,CAAAC,QAAA,eAC3BnE,IAAA,WACEoE,IAAI,CAAC,QAAQ,CACbC,OAAO,CAAEnB,YAAa,CACtBgB,SAAS,CAAC,mBAAmB,CAAAC,QAAA,CAC9B,QAED,CAAQ,CAAC,cACTnE,IAAA,WACEoE,IAAI,CAAC,QAAQ,CACbC,OAAO,CAAEA,CAAA,GAAM9D,OAAO,CAAC,SAAS,CAAE,CAClC2D,SAAS,CAAC,iBAAiB,CAC3BK,QAAQ,CAAER,qBAAqB,CAAG,CAAE,CAAAI,QAAA,CACrC,yBAED,CAAQ,CAAC,EACN,CAAC,EACH,CACN,CAGA7D,IAAI,GAAK,SAAS,EAAII,mBAAmB,eACxCR,KAAA,QAAKgE,SAAS,CAAC,aAAa,CAAAC,QAAA,eAC1BjE,KAAA,QAAKgE,SAAS,CAAC,aAAa,CAAAC,QAAA,eAC1BnE,IAAA,OAAAmE,QAAA,CAAI,wBAAsB,CAAI,CAAC,cAC/BnE,IAAA,MAAAmE,QAAA,CAAG,mEAAiE,CAAG,CAAC,EACrE,CAAC,cAENjE,KAAA,QAAKgE,SAAS,CAAC,sBAAsB,CAAAC,QAAA,eACnCjE,KAAA,QAAKgE,SAAS,CAAC,iBAAiB,CAAAC,QAAA,eAC9BnE,IAAA,OAAAmE,QAAA,CAAI,qBAAmB,CAAI,CAAC,cAC5BjE,KAAA,QAAKgE,SAAS,CAAC,iBAAiB,CAAAC,QAAA,eAC9BnE,IAAA,MAAAmE,QAAA,cAAGnE,IAAA,WAAAmE,QAAA,CAASzD,mBAAmB,CAACoE,IAAI,CAAS,CAAC,CAAG,CAAC,cAClD5E,KAAA,MAAAiE,QAAA,EAAIzD,mBAAmB,CAACqE,QAAQ,CAAC,KAAG,CAACrE,mBAAmB,CAACsE,aAAa,EAAI,CAAC,cAC3E9E,KAAA,MAAAiE,QAAA,EAAG,mBAAiB,CAAChB,cAAc,CAACzC,mBAAmB,CAACuE,cAAc,CAAC,EAAI,CAAC,EACzE,CAAC,EACH,CAAC,cAEN/E,KAAA,QAAKgE,SAAS,CAAC,iBAAiB,CAAAC,QAAA,eAC9BnE,IAAA,OAAAmE,QAAA,CAAI,gBAAc,CAAI,CAAC,cACvBjE,KAAA,QAAKgE,SAAS,CAAC,eAAe,CAAAC,QAAA,eAC5BjE,KAAA,QAAKgE,SAAS,CAAC,WAAW,CAAAC,QAAA,eACxBnE,IAAA,SAAMkE,SAAS,CAAC,YAAY,CAAAC,QAAA,CAAC,kBAAgB,CAAM,CAAC,cACpDnE,IAAA,SAAMkE,SAAS,CAAC,YAAY,CAAAC,QAAA,CAAE3D,KAAK,CAACyD,MAAM,CAAO,CAAC,EAC/C,CAAC,cACN/D,KAAA,QAAKgE,SAAS,CAAC,WAAW,CAAAC,QAAA,eACxBnE,IAAA,SAAMkE,SAAS,CAAC,YAAY,CAAAC,QAAA,CAAC,qBAAmB,CAAM,CAAC,cACvDnE,IAAA,SAAMkE,SAAS,CAAC,YAAY,CAAAC,QAAA,CAAET,iBAAiB,CAAO,CAAC,EACpD,CAAC,cACNxD,KAAA,QAAKgE,SAAS,CAAC,WAAW,CAAAC,QAAA,eACxBnE,IAAA,SAAMkE,SAAS,CAAC,YAAY,CAAAC,QAAA,CAAC,qBAAmB,CAAM,CAAC,cACvDnE,IAAA,SAAMkE,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAEhB,cAAc,CAACU,gBAAgB,CAAC,CAAO,CAAC,EAC9E,CAAC,cACN3D,KAAA,QAAKgE,SAAS,CAAC,WAAW,CAAAC,QAAA,eACxBnE,IAAA,SAAMkE,SAAS,CAAC,YAAY,CAAAC,QAAA,CAAC,sBAAoB,CAAM,CAAC,cACxDnE,IAAA,SAAMkE,SAAS,CAAC,yBAAyB,CAAAC,QAAA,CAAEhB,cAAc,CAACW,iBAAiB,CAAC,CAAO,CAAC,EACjF,CAAC,cACN5D,KAAA,QAAKgE,SAAS,CAAC,WAAW,CAAAC,QAAA,eACxBnE,IAAA,SAAMkE,SAAS,CAAC,YAAY,CAAAC,QAAA,CAAC,aAAW,CAAM,CAAC,cAC/CnE,IAAA,SAAMkE,SAAS,eAAAwB,MAAA,CAAgB5B,iBAAiB,CAAGD,gBAAgB,EAAI,CAAC,CAAG,cAAc,CAAG,YAAY,CAAG,CAAAM,QAAA,CACxGhB,cAAc,CAACW,iBAAiB,CAAGD,gBAAgB,CAAC,CACjD,CAAC,EACJ,CAAC,EACH,CAAC,EACH,CAAC,EACH,CAAC,cAEN3D,KAAA,QAAKgE,SAAS,CAAC,uBAAuB,CAAAC,QAAA,eACpCnE,IAAA,OAAAmE,QAAA,CAAI,0CAAwC,CAAI,CAAC,cACjDnE,IAAA,MAAAmE,QAAA,CAAG,qEAAmE,CAAG,CAAC,EACvE,CAAC,cAENjE,KAAA,QAAKgE,SAAS,CAAC,cAAc,CAAAC,QAAA,eAC3BnE,IAAA,WACEoE,IAAI,CAAC,QAAQ,CACbC,OAAO,CAAEnB,YAAa,CACtBgB,SAAS,CAAC,gBAAgB,CAAAC,QAAA,CAC3B,YAED,CAAQ,CAAC,cACTnE,IAAA,WACEoE,IAAI,CAAC,QAAQ,CACbC,OAAO,CAAEtB,mBAAoB,CAC7BmB,SAAS,CAAC,wBAAwB,CAAAC,QAAA,CACnC,cAED,CAAQ,CAAC,EACN,CAAC,EACH,CACN,EACE,CAAC,CAEV,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}