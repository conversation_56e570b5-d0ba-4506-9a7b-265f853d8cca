{"ast": null, "code": "/**\n * @license\n * Copyright 2018 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport '../flags';\nimport { env } from '../environment';\nimport { getModelArtifactsInfoForJSON } from './io_utils';\nimport { IORouterRegistry } from './router_registry';\nimport { CompositeArrayBuffer } from './composite_array_buffer';\nconst DATABASE_NAME = 'tensorflowjs';\nconst DATABASE_VERSION = 1;\n// Model data and ModelArtifactsInfo (metadata) are stored in two separate\n// stores for efficient access of the list of stored models and their metadata.\n// 1. The object store for model data: topology, weights and weight manifests.\nconst MODEL_STORE_NAME = 'models_store';\n// 2. The object store for ModelArtifactsInfo, including meta-information such\n//    as the type of topology (JSON vs binary), byte size of the topology, byte\n//    size of the weights, etc.\nconst INFO_STORE_NAME = 'model_info_store';\n/**\n * Delete the entire database for tensorflow.js, including the models store.\n */\nexport async function deleteDatabase() {\n  const idbFactory = getIndexedDBFactory();\n  return new Promise((resolve, reject) => {\n    const deleteRequest = idbFactory.deleteDatabase(DATABASE_NAME);\n    deleteRequest.onsuccess = () => resolve();\n    deleteRequest.onerror = error => reject(error);\n  });\n}\nfunction getIndexedDBFactory() {\n  if (!env().getBool('IS_BROWSER')) {\n    // TODO(cais): Add more info about what IOHandler subtypes are available.\n    //   Maybe point to a doc page on the web and/or automatically determine\n    //   the available IOHandlers and print them in the error message.\n    throw new Error('Failed to obtain IndexedDB factory because the current environment' + 'is not a web browser.');\n  }\n  // tslint:disable-next-line:no-any\n  const theWindow = typeof window === 'undefined' ? self : window;\n  const factory = theWindow.indexedDB || theWindow.mozIndexedDB || theWindow.webkitIndexedDB || theWindow.msIndexedDB || theWindow.shimIndexedDB;\n  if (factory == null) {\n    throw new Error('The current browser does not appear to support IndexedDB.');\n  }\n  return factory;\n}\nfunction setUpDatabase(openRequest) {\n  const db = openRequest.result;\n  db.createObjectStore(MODEL_STORE_NAME, {\n    keyPath: 'modelPath'\n  });\n  db.createObjectStore(INFO_STORE_NAME, {\n    keyPath: 'modelPath'\n  });\n}\n/**\n * IOHandler subclass: Browser IndexedDB.\n *\n * See the doc string of `browserIndexedDB` for more details.\n */\nclass BrowserIndexedDB {\n  constructor(modelPath) {\n    this.indexedDB = getIndexedDBFactory();\n    if (modelPath == null || !modelPath) {\n      throw new Error('For IndexedDB, modelPath must not be null, undefined or empty.');\n    }\n    this.modelPath = modelPath;\n  }\n  async save(modelArtifacts) {\n    // TODO(cais): Support saving GraphDef models.\n    if (modelArtifacts.modelTopology instanceof ArrayBuffer) {\n      throw new Error('BrowserLocalStorage.save() does not support saving model topology ' + 'in binary formats yet.');\n    }\n    return this.databaseAction(this.modelPath, modelArtifacts);\n  }\n  async load() {\n    return this.databaseAction(this.modelPath);\n  }\n  /**\n   * Perform database action to put model artifacts into or read model artifacts\n   * from IndexedDB object store.\n   *\n   * Whether the action is put or get depends on whether `modelArtifacts` is\n   * specified. If it is specified, the action will be put; otherwise the action\n   * will be get.\n   *\n   * @param modelPath A unique string path for the model.\n   * @param modelArtifacts If specified, it will be the model artifacts to be\n   *   stored in IndexedDB.\n   * @returns A `Promise` of `SaveResult`, if the action is put, or a `Promise`\n   *   of `ModelArtifacts`, if the action is get.\n   */\n  databaseAction(modelPath, modelArtifacts) {\n    return new Promise((resolve, reject) => {\n      const openRequest = this.indexedDB.open(DATABASE_NAME, DATABASE_VERSION);\n      openRequest.onupgradeneeded = () => setUpDatabase(openRequest);\n      openRequest.onsuccess = () => {\n        const db = openRequest.result;\n        if (modelArtifacts == null) {\n          // Read model out from object store.\n          const modelTx = db.transaction(MODEL_STORE_NAME, 'readonly');\n          const modelStore = modelTx.objectStore(MODEL_STORE_NAME);\n          const getRequest = modelStore.get(this.modelPath);\n          getRequest.onsuccess = () => {\n            if (getRequest.result == null) {\n              db.close();\n              return reject(new Error(`Cannot find model with path '${this.modelPath}' ` + `in IndexedDB.`));\n            } else {\n              resolve(getRequest.result.modelArtifacts);\n            }\n          };\n          getRequest.onerror = error => {\n            db.close();\n            return reject(getRequest.error);\n          };\n          modelTx.oncomplete = () => db.close();\n        } else {\n          // Put model into object store.\n          // Concatenate all the model weights into a single ArrayBuffer. Large\n          // models (~1GB) have problems saving if they are not concatenated.\n          // TODO(mattSoulanille): Save large models to multiple indexeddb\n          // records.\n          modelArtifacts.weightData = CompositeArrayBuffer.join(modelArtifacts.weightData);\n          const modelArtifactsInfo = getModelArtifactsInfoForJSON(modelArtifacts);\n          // First, put ModelArtifactsInfo into info store.\n          const infoTx = db.transaction(INFO_STORE_NAME, 'readwrite');\n          let infoStore = infoTx.objectStore(INFO_STORE_NAME);\n          let putInfoRequest;\n          try {\n            putInfoRequest = infoStore.put({\n              modelPath: this.modelPath,\n              modelArtifactsInfo\n            });\n          } catch (error) {\n            return reject(error);\n          }\n          let modelTx;\n          putInfoRequest.onsuccess = () => {\n            // Second, put model data into model store.\n            modelTx = db.transaction(MODEL_STORE_NAME, 'readwrite');\n            const modelStore = modelTx.objectStore(MODEL_STORE_NAME);\n            let putModelRequest;\n            try {\n              putModelRequest = modelStore.put({\n                modelPath: this.modelPath,\n                modelArtifacts,\n                modelArtifactsInfo\n              });\n            } catch (error) {\n              // Sometimes, the serialized value is too large to store.\n              return reject(error);\n            }\n            putModelRequest.onsuccess = () => resolve({\n              modelArtifactsInfo\n            });\n            putModelRequest.onerror = error => {\n              // If the put-model request fails, roll back the info entry as\n              // well.\n              infoStore = infoTx.objectStore(INFO_STORE_NAME);\n              const deleteInfoRequest = infoStore.delete(this.modelPath);\n              deleteInfoRequest.onsuccess = () => {\n                db.close();\n                return reject(putModelRequest.error);\n              };\n              deleteInfoRequest.onerror = error => {\n                db.close();\n                return reject(putModelRequest.error);\n              };\n            };\n          };\n          putInfoRequest.onerror = error => {\n            db.close();\n            return reject(putInfoRequest.error);\n          };\n          infoTx.oncomplete = () => {\n            if (modelTx == null) {\n              db.close();\n            } else {\n              modelTx.oncomplete = () => db.close();\n            }\n          };\n        }\n      };\n      openRequest.onerror = error => reject(openRequest.error);\n    });\n  }\n}\nBrowserIndexedDB.URL_SCHEME = 'indexeddb://';\nexport { BrowserIndexedDB };\nexport const indexedDBRouter = url => {\n  if (!env().getBool('IS_BROWSER')) {\n    return null;\n  } else {\n    if (!Array.isArray(url) && url.startsWith(BrowserIndexedDB.URL_SCHEME)) {\n      return browserIndexedDB(url.slice(BrowserIndexedDB.URL_SCHEME.length));\n    } else {\n      return null;\n    }\n  }\n};\nIORouterRegistry.registerSaveRouter(indexedDBRouter);\nIORouterRegistry.registerLoadRouter(indexedDBRouter);\n/**\n * Creates a browser IndexedDB IOHandler for saving and loading models.\n *\n * ```js\n * const model = tf.sequential();\n * model.add(\n *     tf.layers.dense({units: 1, inputShape: [100], activation: 'sigmoid'}));\n *\n * const saveResult = await model.save('indexeddb://MyModel'));\n * console.log(saveResult);\n * ```\n *\n * @param modelPath A unique identifier for the model to be saved. Must be a\n *   non-empty string.\n * @returns An instance of `BrowserIndexedDB` (subclass of `IOHandler`),\n *   which can be used with, e.g., `tf.Model.save`.\n */\nexport function browserIndexedDB(modelPath) {\n  return new BrowserIndexedDB(modelPath);\n}\nfunction maybeStripScheme(key) {\n  return key.startsWith(BrowserIndexedDB.URL_SCHEME) ? key.slice(BrowserIndexedDB.URL_SCHEME.length) : key;\n}\nexport class BrowserIndexedDBManager {\n  constructor() {\n    this.indexedDB = getIndexedDBFactory();\n  }\n  async listModels() {\n    return new Promise((resolve, reject) => {\n      const openRequest = this.indexedDB.open(DATABASE_NAME, DATABASE_VERSION);\n      openRequest.onupgradeneeded = () => setUpDatabase(openRequest);\n      openRequest.onsuccess = () => {\n        const db = openRequest.result;\n        const tx = db.transaction(INFO_STORE_NAME, 'readonly');\n        const store = tx.objectStore(INFO_STORE_NAME);\n        // tslint:disable:max-line-length\n        // Need to cast `store` as `any` here because TypeScript's DOM\n        // library does not have the `getAll()` method even though the\n        // method is supported in the latest version of most mainstream\n        // browsers:\n        // https://developer.mozilla.org/en-US/docs/Web/API/IDBObjectStore/getAll\n        // tslint:enable:max-line-length\n        // tslint:disable-next-line:no-any\n        const getAllInfoRequest = store.getAll();\n        getAllInfoRequest.onsuccess = () => {\n          const out = {};\n          for (const item of getAllInfoRequest.result) {\n            out[item.modelPath] = item.modelArtifactsInfo;\n          }\n          resolve(out);\n        };\n        getAllInfoRequest.onerror = error => {\n          db.close();\n          return reject(getAllInfoRequest.error);\n        };\n        tx.oncomplete = () => db.close();\n      };\n      openRequest.onerror = error => reject(openRequest.error);\n    });\n  }\n  async removeModel(path) {\n    path = maybeStripScheme(path);\n    return new Promise((resolve, reject) => {\n      const openRequest = this.indexedDB.open(DATABASE_NAME, DATABASE_VERSION);\n      openRequest.onupgradeneeded = () => setUpDatabase(openRequest);\n      openRequest.onsuccess = () => {\n        const db = openRequest.result;\n        const infoTx = db.transaction(INFO_STORE_NAME, 'readwrite');\n        const infoStore = infoTx.objectStore(INFO_STORE_NAME);\n        const getInfoRequest = infoStore.get(path);\n        let modelTx;\n        getInfoRequest.onsuccess = () => {\n          if (getInfoRequest.result == null) {\n            db.close();\n            return reject(new Error(`Cannot find model with path '${path}' ` + `in IndexedDB.`));\n          } else {\n            // First, delete the entry in the info store.\n            const deleteInfoRequest = infoStore.delete(path);\n            const deleteModelData = () => {\n              // Second, delete the entry in the model store.\n              modelTx = db.transaction(MODEL_STORE_NAME, 'readwrite');\n              const modelStore = modelTx.objectStore(MODEL_STORE_NAME);\n              const deleteModelRequest = modelStore.delete(path);\n              deleteModelRequest.onsuccess = () => resolve(getInfoRequest.result.modelArtifactsInfo);\n              deleteModelRequest.onerror = error => reject(getInfoRequest.error);\n            };\n            // Proceed with deleting model data regardless of whether deletion\n            // of info data succeeds or not.\n            deleteInfoRequest.onsuccess = deleteModelData;\n            deleteInfoRequest.onerror = error => {\n              deleteModelData();\n              db.close();\n              return reject(getInfoRequest.error);\n            };\n          }\n        };\n        getInfoRequest.onerror = error => {\n          db.close();\n          return reject(getInfoRequest.error);\n        };\n        infoTx.oncomplete = () => {\n          if (modelTx == null) {\n            db.close();\n          } else {\n            modelTx.oncomplete = () => db.close();\n          }\n        };\n      };\n      openRequest.onerror = error => reject(openRequest.error);\n    });\n  }\n}", "map": {"version": 3, "names": ["env", "getModelArtifactsInfoForJSON", "IORouterRegistry", "CompositeArrayBuffer", "DATABASE_NAME", "DATABASE_VERSION", "MODEL_STORE_NAME", "INFO_STORE_NAME", "deleteDatabase", "idbFactory", "getIndexedDBFactory", "Promise", "resolve", "reject", "deleteRequest", "onsuccess", "onerror", "error", "getBool", "Error", "theWindow", "window", "self", "factory", "indexedDB", "mozIndexedDB", "webkitIndexedDB", "msIndexedDB", "shimIndexedDB", "setUpDatabase", "openRequest", "db", "result", "createObjectStore", "keyP<PERSON>", "BrowserIndexedDB", "constructor", "modelPath", "save", "modelArtifacts", "modelTopology", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "databaseAction", "load", "open", "onupgradeneeded", "modelTx", "transaction", "modelStore", "objectStore", "getRequest", "get", "close", "oncomplete", "weightData", "join", "modelArtifactsInfo", "infoTx", "infoStore", "putInfoRequest", "put", "putModelRequest", "deleteInfoRequest", "delete", "URL_SCHEME", "indexedDBRouter", "url", "Array", "isArray", "startsWith", "browserIndexedDB", "slice", "length", "registerSaveRouter", "registerLoadRouter", "maybeStripScheme", "key", "BrowserIndexedDBManager", "listModels", "tx", "store", "getAllInfoRequest", "getAll", "out", "item", "removeModel", "path", "getInfoRequest", "deleteModelData", "deleteModelRequest"], "sources": ["C:\\tfjs-core\\src\\io\\indexed_db.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2018 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport '../flags';\n\nimport {env} from '../environment';\n\nimport {getModelArtifactsInfoForJSON} from './io_utils';\nimport {<PERSON><PERSON><PERSON><PERSON>, IORouterRegistry} from './router_registry';\nimport {<PERSON><PERSON><PERSON><PERSON>, ModelArtifacts, ModelArtifactsInfo, ModelStoreManager, SaveResult} from './types';\nimport {CompositeArrayBuffer} from './composite_array_buffer';\n\nconst DATABASE_NAME = 'tensorflowjs';\nconst DATABASE_VERSION = 1;\n\n// Model data and ModelArtifactsInfo (metadata) are stored in two separate\n// stores for efficient access of the list of stored models and their metadata.\n// 1. The object store for model data: topology, weights and weight manifests.\nconst MODEL_STORE_NAME = 'models_store';\n// 2. The object store for ModelArtifactsInfo, including meta-information such\n//    as the type of topology (JSON vs binary), byte size of the topology, byte\n//    size of the weights, etc.\nconst INFO_STORE_NAME = 'model_info_store';\n\n/**\n * Delete the entire database for tensorflow.js, including the models store.\n */\nexport async function deleteDatabase(): Promise<void> {\n  const idbFactory = getIndexedDBFactory();\n\n  return new Promise<void>((resolve, reject) => {\n    const deleteRequest = idbFactory.deleteDatabase(DATABASE_NAME);\n    deleteRequest.onsuccess = () => resolve();\n    deleteRequest.onerror = error => reject(error);\n  });\n}\n\nfunction getIndexedDBFactory(): IDBFactory {\n  if (!env().getBool('IS_BROWSER')) {\n    // TODO(cais): Add more info about what IOHandler subtypes are available.\n    //   Maybe point to a doc page on the web and/or automatically determine\n    //   the available IOHandlers and print them in the error message.\n    throw new Error(\n        'Failed to obtain IndexedDB factory because the current environment' +\n        'is not a web browser.');\n  }\n  // tslint:disable-next-line:no-any\n  const theWindow: any = typeof window === 'undefined' ? self : window;\n  const factory = theWindow.indexedDB || theWindow.mozIndexedDB ||\n      theWindow.webkitIndexedDB || theWindow.msIndexedDB ||\n      theWindow.shimIndexedDB;\n  if (factory == null) {\n    throw new Error(\n        'The current browser does not appear to support IndexedDB.');\n  }\n  return factory;\n}\n\nfunction setUpDatabase(openRequest: IDBRequest) {\n  const db = openRequest.result as IDBDatabase;\n  db.createObjectStore(MODEL_STORE_NAME, {keyPath: 'modelPath'});\n  db.createObjectStore(INFO_STORE_NAME, {keyPath: 'modelPath'});\n}\n\n/**\n * IOHandler subclass: Browser IndexedDB.\n *\n * See the doc string of `browserIndexedDB` for more details.\n */\nexport class BrowserIndexedDB implements IOHandler {\n  protected readonly indexedDB: IDBFactory;\n  protected readonly modelPath: string;\n\n  static readonly URL_SCHEME = 'indexeddb://';\n\n  constructor(modelPath: string) {\n    this.indexedDB = getIndexedDBFactory();\n\n    if (modelPath == null || !modelPath) {\n      throw new Error(\n          'For IndexedDB, modelPath must not be null, undefined or empty.');\n    }\n    this.modelPath = modelPath;\n  }\n\n  async save(modelArtifacts: ModelArtifacts): Promise<SaveResult> {\n    // TODO(cais): Support saving GraphDef models.\n    if (modelArtifacts.modelTopology instanceof ArrayBuffer) {\n      throw new Error(\n          'BrowserLocalStorage.save() does not support saving model topology ' +\n          'in binary formats yet.');\n    }\n\n    return this.databaseAction(this.modelPath, modelArtifacts) as\n        Promise<SaveResult>;\n  }\n\n  async load(): Promise<ModelArtifacts> {\n    return this.databaseAction(this.modelPath) as Promise<ModelArtifacts>;\n  }\n\n  /**\n   * Perform database action to put model artifacts into or read model artifacts\n   * from IndexedDB object store.\n   *\n   * Whether the action is put or get depends on whether `modelArtifacts` is\n   * specified. If it is specified, the action will be put; otherwise the action\n   * will be get.\n   *\n   * @param modelPath A unique string path for the model.\n   * @param modelArtifacts If specified, it will be the model artifacts to be\n   *   stored in IndexedDB.\n   * @returns A `Promise` of `SaveResult`, if the action is put, or a `Promise`\n   *   of `ModelArtifacts`, if the action is get.\n   */\n  private databaseAction(modelPath: string, modelArtifacts?: ModelArtifacts):\n      Promise<ModelArtifacts|SaveResult> {\n    return new Promise<ModelArtifacts|SaveResult>((resolve, reject) => {\n      const openRequest = this.indexedDB.open(DATABASE_NAME, DATABASE_VERSION);\n      openRequest.onupgradeneeded = () => setUpDatabase(openRequest);\n\n      openRequest.onsuccess = () => {\n        const db = openRequest.result;\n\n        if (modelArtifacts == null) {\n          // Read model out from object store.\n          const modelTx = db.transaction(MODEL_STORE_NAME, 'readonly');\n          const modelStore = modelTx.objectStore(MODEL_STORE_NAME);\n          const getRequest = modelStore.get(this.modelPath);\n          getRequest.onsuccess = () => {\n            if (getRequest.result == null) {\n              db.close();\n              return reject(new Error(\n                  `Cannot find model with path '${this.modelPath}' ` +\n                  `in IndexedDB.`));\n            } else {\n              resolve(getRequest.result.modelArtifacts);\n            }\n          };\n          getRequest.onerror = error => {\n            db.close();\n            return reject(getRequest.error);\n          };\n          modelTx.oncomplete = () => db.close();\n        } else {\n          // Put model into object store.\n\n          // Concatenate all the model weights into a single ArrayBuffer. Large\n          // models (~1GB) have problems saving if they are not concatenated.\n          // TODO(mattSoulanille): Save large models to multiple indexeddb\n          // records.\n          modelArtifacts.weightData = CompositeArrayBuffer.join(\n              modelArtifacts.weightData);\n          const modelArtifactsInfo: ModelArtifactsInfo =\n              getModelArtifactsInfoForJSON(modelArtifacts);\n          // First, put ModelArtifactsInfo into info store.\n          const infoTx = db.transaction(INFO_STORE_NAME, 'readwrite');\n          let infoStore = infoTx.objectStore(INFO_STORE_NAME);\n          let putInfoRequest: IDBRequest<IDBValidKey>;\n          try {\n            putInfoRequest =\n              infoStore.put({modelPath: this.modelPath, modelArtifactsInfo});\n          } catch (error) {\n            return reject(error);\n          }\n          let modelTx: IDBTransaction;\n          putInfoRequest.onsuccess = () => {\n            // Second, put model data into model store.\n            modelTx = db.transaction(MODEL_STORE_NAME, 'readwrite');\n            const modelStore = modelTx.objectStore(MODEL_STORE_NAME);\n            let putModelRequest: IDBRequest<IDBValidKey>;\n            try {\n              putModelRequest = modelStore.put({\n                modelPath: this.modelPath,\n                modelArtifacts,\n                modelArtifactsInfo\n              });\n            } catch (error) {\n              // Sometimes, the serialized value is too large to store.\n              return reject(error);\n            }\n            putModelRequest.onsuccess = () => resolve({modelArtifactsInfo});\n            putModelRequest.onerror = error => {\n              // If the put-model request fails, roll back the info entry as\n              // well.\n              infoStore = infoTx.objectStore(INFO_STORE_NAME);\n              const deleteInfoRequest = infoStore.delete(this.modelPath);\n              deleteInfoRequest.onsuccess = () => {\n                db.close();\n                return reject(putModelRequest.error);\n              };\n              deleteInfoRequest.onerror = error => {\n                db.close();\n                return reject(putModelRequest.error);\n              };\n            };\n          };\n          putInfoRequest.onerror = error => {\n            db.close();\n            return reject(putInfoRequest.error);\n          };\n          infoTx.oncomplete = () => {\n            if (modelTx == null) {\n              db.close();\n            } else {\n              modelTx.oncomplete = () => db.close();\n            }\n          };\n        }\n      };\n      openRequest.onerror = error => reject(openRequest.error);\n    });\n  }\n}\n\nexport const indexedDBRouter: IORouter = (url: string|string[]) => {\n  if (!env().getBool('IS_BROWSER')) {\n    return null;\n  } else {\n    if (!Array.isArray(url) && url.startsWith(BrowserIndexedDB.URL_SCHEME)) {\n      return browserIndexedDB(url.slice(BrowserIndexedDB.URL_SCHEME.length));\n    } else {\n      return null;\n    }\n  }\n};\nIORouterRegistry.registerSaveRouter(indexedDBRouter);\nIORouterRegistry.registerLoadRouter(indexedDBRouter);\n\n/**\n * Creates a browser IndexedDB IOHandler for saving and loading models.\n *\n * ```js\n * const model = tf.sequential();\n * model.add(\n *     tf.layers.dense({units: 1, inputShape: [100], activation: 'sigmoid'}));\n *\n * const saveResult = await model.save('indexeddb://MyModel'));\n * console.log(saveResult);\n * ```\n *\n * @param modelPath A unique identifier for the model to be saved. Must be a\n *   non-empty string.\n * @returns An instance of `BrowserIndexedDB` (subclass of `IOHandler`),\n *   which can be used with, e.g., `tf.Model.save`.\n */\nexport function browserIndexedDB(modelPath: string): IOHandler {\n  return new BrowserIndexedDB(modelPath);\n}\n\nfunction maybeStripScheme(key: string) {\n  return key.startsWith(BrowserIndexedDB.URL_SCHEME) ?\n      key.slice(BrowserIndexedDB.URL_SCHEME.length) :\n      key;\n}\n\nexport class BrowserIndexedDBManager implements ModelStoreManager {\n  private indexedDB: IDBFactory;\n\n  constructor() {\n    this.indexedDB = getIndexedDBFactory();\n  }\n\n  async listModels(): Promise<{[path: string]: ModelArtifactsInfo}> {\n    return new Promise<{[path: string]: ModelArtifactsInfo}>(\n        (resolve, reject) => {\n          const openRequest =\n              this.indexedDB.open(DATABASE_NAME, DATABASE_VERSION);\n          openRequest.onupgradeneeded = () => setUpDatabase(openRequest);\n\n          openRequest.onsuccess = () => {\n            const db = openRequest.result;\n            const tx = db.transaction(INFO_STORE_NAME, 'readonly');\n            const store = tx.objectStore(INFO_STORE_NAME);\n            // tslint:disable:max-line-length\n            // Need to cast `store` as `any` here because TypeScript's DOM\n            // library does not have the `getAll()` method even though the\n            // method is supported in the latest version of most mainstream\n            // browsers:\n            // https://developer.mozilla.org/en-US/docs/Web/API/IDBObjectStore/getAll\n            // tslint:enable:max-line-length\n            // tslint:disable-next-line:no-any\n            const getAllInfoRequest = (store as any).getAll() as IDBRequest;\n            getAllInfoRequest.onsuccess = () => {\n              const out: {[path: string]: ModelArtifactsInfo} = {};\n              for (const item of getAllInfoRequest.result) {\n                out[item.modelPath] = item.modelArtifactsInfo;\n              }\n              resolve(out);\n            };\n            getAllInfoRequest.onerror = error => {\n              db.close();\n              return reject(getAllInfoRequest.error);\n            };\n            tx.oncomplete = () => db.close();\n          };\n          openRequest.onerror = error => reject(openRequest.error);\n        });\n  }\n\n  async removeModel(path: string): Promise<ModelArtifactsInfo> {\n    path = maybeStripScheme(path);\n    return new Promise<ModelArtifactsInfo>((resolve, reject) => {\n      const openRequest = this.indexedDB.open(DATABASE_NAME, DATABASE_VERSION);\n      openRequest.onupgradeneeded = () => setUpDatabase(openRequest);\n\n      openRequest.onsuccess = () => {\n        const db = openRequest.result;\n        const infoTx = db.transaction(INFO_STORE_NAME, 'readwrite');\n        const infoStore = infoTx.objectStore(INFO_STORE_NAME);\n\n        const getInfoRequest = infoStore.get(path);\n        let modelTx: IDBTransaction;\n        getInfoRequest.onsuccess = () => {\n          if (getInfoRequest.result == null) {\n            db.close();\n            return reject(new Error(\n                `Cannot find model with path '${path}' ` +\n                `in IndexedDB.`));\n          } else {\n            // First, delete the entry in the info store.\n            const deleteInfoRequest = infoStore.delete(path);\n            const deleteModelData = () => {\n              // Second, delete the entry in the model store.\n              modelTx = db.transaction(MODEL_STORE_NAME, 'readwrite');\n              const modelStore = modelTx.objectStore(MODEL_STORE_NAME);\n              const deleteModelRequest = modelStore.delete(path);\n              deleteModelRequest.onsuccess = () =>\n                  resolve(getInfoRequest.result.modelArtifactsInfo);\n              deleteModelRequest.onerror = error =>\n                  reject(getInfoRequest.error);\n            };\n            // Proceed with deleting model data regardless of whether deletion\n            // of info data succeeds or not.\n            deleteInfoRequest.onsuccess = deleteModelData;\n            deleteInfoRequest.onerror = error => {\n              deleteModelData();\n              db.close();\n              return reject(getInfoRequest.error);\n            };\n          }\n        };\n        getInfoRequest.onerror = error => {\n          db.close();\n          return reject(getInfoRequest.error);\n        };\n\n        infoTx.oncomplete = () => {\n          if (modelTx == null) {\n            db.close();\n          } else {\n            modelTx.oncomplete = () => db.close();\n          }\n        };\n      };\n      openRequest.onerror = error => reject(openRequest.error);\n    });\n  }\n}\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAiBA,OAAO,UAAU;AAEjB,SAAQA,GAAG,QAAO,gBAAgB;AAElC,SAAQC,4BAA4B,QAAO,YAAY;AACvD,SAAkBC,gBAAgB,QAAO,mBAAmB;AAE5D,SAAQC,oBAAoB,QAAO,0BAA0B;AAE7D,MAAMC,aAAa,GAAG,cAAc;AACpC,MAAMC,gBAAgB,GAAG,CAAC;AAE1B;AACA;AACA;AACA,MAAMC,gBAAgB,GAAG,cAAc;AACvC;AACA;AACA;AACA,MAAMC,eAAe,GAAG,kBAAkB;AAE1C;;;AAGA,OAAO,eAAeC,cAAcA,CAAA;EAClC,MAAMC,UAAU,GAAGC,mBAAmB,EAAE;EAExC,OAAO,IAAIC,OAAO,CAAO,CAACC,OAAO,EAAEC,MAAM,KAAI;IAC3C,MAAMC,aAAa,GAAGL,UAAU,CAACD,cAAc,CAACJ,aAAa,CAAC;IAC9DU,aAAa,CAACC,SAAS,GAAG,MAAMH,OAAO,EAAE;IACzCE,aAAa,CAACE,OAAO,GAAGC,KAAK,IAAIJ,MAAM,CAACI,KAAK,CAAC;EAChD,CAAC,CAAC;AACJ;AAEA,SAASP,mBAAmBA,CAAA;EAC1B,IAAI,CAACV,GAAG,EAAE,CAACkB,OAAO,CAAC,YAAY,CAAC,EAAE;IAChC;IACA;IACA;IACA,MAAM,IAAIC,KAAK,CACX,oEAAoE,GACpE,uBAAuB,CAAC;;EAE9B;EACA,MAAMC,SAAS,GAAQ,OAAOC,MAAM,KAAK,WAAW,GAAGC,IAAI,GAAGD,MAAM;EACpE,MAAME,OAAO,GAAGH,SAAS,CAACI,SAAS,IAAIJ,SAAS,CAACK,YAAY,IACzDL,SAAS,CAACM,eAAe,IAAIN,SAAS,CAACO,WAAW,IAClDP,SAAS,CAACQ,aAAa;EAC3B,IAAIL,OAAO,IAAI,IAAI,EAAE;IACnB,MAAM,IAAIJ,KAAK,CACX,2DAA2D,CAAC;;EAElE,OAAOI,OAAO;AAChB;AAEA,SAASM,aAAaA,CAACC,WAAuB;EAC5C,MAAMC,EAAE,GAAGD,WAAW,CAACE,MAAqB;EAC5CD,EAAE,CAACE,iBAAiB,CAAC3B,gBAAgB,EAAE;IAAC4B,OAAO,EAAE;EAAW,CAAC,CAAC;EAC9DH,EAAE,CAACE,iBAAiB,CAAC1B,eAAe,EAAE;IAAC2B,OAAO,EAAE;EAAW,CAAC,CAAC;AAC/D;AAEA;;;;;AAKA,MAAaC,gBAAgB;EAM3BC,YAAYC,SAAiB;IAC3B,IAAI,CAACb,SAAS,GAAGd,mBAAmB,EAAE;IAEtC,IAAI2B,SAAS,IAAI,IAAI,IAAI,CAACA,SAAS,EAAE;MACnC,MAAM,IAAIlB,KAAK,CACX,gEAAgE,CAAC;;IAEvE,IAAI,CAACkB,SAAS,GAAGA,SAAS;EAC5B;EAEA,MAAMC,IAAIA,CAACC,cAA8B;IACvC;IACA,IAAIA,cAAc,CAACC,aAAa,YAAYC,WAAW,EAAE;MACvD,MAAM,IAAItB,KAAK,CACX,oEAAoE,GACpE,wBAAwB,CAAC;;IAG/B,OAAO,IAAI,CAACuB,cAAc,CAAC,IAAI,CAACL,SAAS,EAAEE,cAAc,CAClC;EACzB;EAEA,MAAMI,IAAIA,CAAA;IACR,OAAO,IAAI,CAACD,cAAc,CAAC,IAAI,CAACL,SAAS,CAA4B;EACvE;EAEA;;;;;;;;;;;;;;EAcQK,cAAcA,CAACL,SAAiB,EAAEE,cAA+B;IAEvE,OAAO,IAAI5B,OAAO,CAA4B,CAACC,OAAO,EAAEC,MAAM,KAAI;MAChE,MAAMiB,WAAW,GAAG,IAAI,CAACN,SAAS,CAACoB,IAAI,CAACxC,aAAa,EAAEC,gBAAgB,CAAC;MACxEyB,WAAW,CAACe,eAAe,GAAG,MAAMhB,aAAa,CAACC,WAAW,CAAC;MAE9DA,WAAW,CAACf,SAAS,GAAG,MAAK;QAC3B,MAAMgB,EAAE,GAAGD,WAAW,CAACE,MAAM;QAE7B,IAAIO,cAAc,IAAI,IAAI,EAAE;UAC1B;UACA,MAAMO,OAAO,GAAGf,EAAE,CAACgB,WAAW,CAACzC,gBAAgB,EAAE,UAAU,CAAC;UAC5D,MAAM0C,UAAU,GAAGF,OAAO,CAACG,WAAW,CAAC3C,gBAAgB,CAAC;UACxD,MAAM4C,UAAU,GAAGF,UAAU,CAACG,GAAG,CAAC,IAAI,CAACd,SAAS,CAAC;UACjDa,UAAU,CAACnC,SAAS,GAAG,MAAK;YAC1B,IAAImC,UAAU,CAAClB,MAAM,IAAI,IAAI,EAAE;cAC7BD,EAAE,CAACqB,KAAK,EAAE;cACV,OAAOvC,MAAM,CAAC,IAAIM,KAAK,CACnB,gCAAgC,IAAI,CAACkB,SAAS,IAAI,GAClD,eAAe,CAAC,CAAC;aACtB,MAAM;cACLzB,OAAO,CAACsC,UAAU,CAAClB,MAAM,CAACO,cAAc,CAAC;;UAE7C,CAAC;UACDW,UAAU,CAAClC,OAAO,GAAGC,KAAK,IAAG;YAC3Bc,EAAE,CAACqB,KAAK,EAAE;YACV,OAAOvC,MAAM,CAACqC,UAAU,CAACjC,KAAK,CAAC;UACjC,CAAC;UACD6B,OAAO,CAACO,UAAU,GAAG,MAAMtB,EAAE,CAACqB,KAAK,EAAE;SACtC,MAAM;UACL;UAEA;UACA;UACA;UACA;UACAb,cAAc,CAACe,UAAU,GAAGnD,oBAAoB,CAACoD,IAAI,CACjDhB,cAAc,CAACe,UAAU,CAAC;UAC9B,MAAME,kBAAkB,GACpBvD,4BAA4B,CAACsC,cAAc,CAAC;UAChD;UACA,MAAMkB,MAAM,GAAG1B,EAAE,CAACgB,WAAW,CAACxC,eAAe,EAAE,WAAW,CAAC;UAC3D,IAAImD,SAAS,GAAGD,MAAM,CAACR,WAAW,CAAC1C,eAAe,CAAC;UACnD,IAAIoD,cAAuC;UAC3C,IAAI;YACFA,cAAc,GACZD,SAAS,CAACE,GAAG,CAAC;cAACvB,SAAS,EAAE,IAAI,CAACA,SAAS;cAAEmB;YAAkB,CAAC,CAAC;WACjE,CAAC,OAAOvC,KAAK,EAAE;YACd,OAAOJ,MAAM,CAACI,KAAK,CAAC;;UAEtB,IAAI6B,OAAuB;UAC3Ba,cAAc,CAAC5C,SAAS,GAAG,MAAK;YAC9B;YACA+B,OAAO,GAAGf,EAAE,CAACgB,WAAW,CAACzC,gBAAgB,EAAE,WAAW,CAAC;YACvD,MAAM0C,UAAU,GAAGF,OAAO,CAACG,WAAW,CAAC3C,gBAAgB,CAAC;YACxD,IAAIuD,eAAwC;YAC5C,IAAI;cACFA,eAAe,GAAGb,UAAU,CAACY,GAAG,CAAC;gBAC/BvB,SAAS,EAAE,IAAI,CAACA,SAAS;gBACzBE,cAAc;gBACdiB;eACD,CAAC;aACH,CAAC,OAAOvC,KAAK,EAAE;cACd;cACA,OAAOJ,MAAM,CAACI,KAAK,CAAC;;YAEtB4C,eAAe,CAAC9C,SAAS,GAAG,MAAMH,OAAO,CAAC;cAAC4C;YAAkB,CAAC,CAAC;YAC/DK,eAAe,CAAC7C,OAAO,GAAGC,KAAK,IAAG;cAChC;cACA;cACAyC,SAAS,GAAGD,MAAM,CAACR,WAAW,CAAC1C,eAAe,CAAC;cAC/C,MAAMuD,iBAAiB,GAAGJ,SAAS,CAACK,MAAM,CAAC,IAAI,CAAC1B,SAAS,CAAC;cAC1DyB,iBAAiB,CAAC/C,SAAS,GAAG,MAAK;gBACjCgB,EAAE,CAACqB,KAAK,EAAE;gBACV,OAAOvC,MAAM,CAACgD,eAAe,CAAC5C,KAAK,CAAC;cACtC,CAAC;cACD6C,iBAAiB,CAAC9C,OAAO,GAAGC,KAAK,IAAG;gBAClCc,EAAE,CAACqB,KAAK,EAAE;gBACV,OAAOvC,MAAM,CAACgD,eAAe,CAAC5C,KAAK,CAAC;cACtC,CAAC;YACH,CAAC;UACH,CAAC;UACD0C,cAAc,CAAC3C,OAAO,GAAGC,KAAK,IAAG;YAC/Bc,EAAE,CAACqB,KAAK,EAAE;YACV,OAAOvC,MAAM,CAAC8C,cAAc,CAAC1C,KAAK,CAAC;UACrC,CAAC;UACDwC,MAAM,CAACJ,UAAU,GAAG,MAAK;YACvB,IAAIP,OAAO,IAAI,IAAI,EAAE;cACnBf,EAAE,CAACqB,KAAK,EAAE;aACX,MAAM;cACLN,OAAO,CAACO,UAAU,GAAG,MAAMtB,EAAE,CAACqB,KAAK,EAAE;;UAEzC,CAAC;;MAEL,CAAC;MACDtB,WAAW,CAACd,OAAO,GAAGC,KAAK,IAAIJ,MAAM,CAACiB,WAAW,CAACb,KAAK,CAAC;IAC1D,CAAC,CAAC;EACJ;;AA3IgBkB,gBAAA,CAAA6B,UAAU,GAAG,cAAc;SAJhC7B,gBAAgB;AAkJ7B,OAAO,MAAM8B,eAAe,GAAcC,GAAoB,IAAI;EAChE,IAAI,CAAClE,GAAG,EAAE,CAACkB,OAAO,CAAC,YAAY,CAAC,EAAE;IAChC,OAAO,IAAI;GACZ,MAAM;IACL,IAAI,CAACiD,KAAK,CAACC,OAAO,CAACF,GAAG,CAAC,IAAIA,GAAG,CAACG,UAAU,CAAClC,gBAAgB,CAAC6B,UAAU,CAAC,EAAE;MACtE,OAAOM,gBAAgB,CAACJ,GAAG,CAACK,KAAK,CAACpC,gBAAgB,CAAC6B,UAAU,CAACQ,MAAM,CAAC,CAAC;KACvE,MAAM;MACL,OAAO,IAAI;;;AAGjB,CAAC;AACDtE,gBAAgB,CAACuE,kBAAkB,CAACR,eAAe,CAAC;AACpD/D,gBAAgB,CAACwE,kBAAkB,CAACT,eAAe,CAAC;AAEpD;;;;;;;;;;;;;;;;;AAiBA,OAAM,SAAUK,gBAAgBA,CAACjC,SAAiB;EAChD,OAAO,IAAIF,gBAAgB,CAACE,SAAS,CAAC;AACxC;AAEA,SAASsC,gBAAgBA,CAACC,GAAW;EACnC,OAAOA,GAAG,CAACP,UAAU,CAAClC,gBAAgB,CAAC6B,UAAU,CAAC,GAC9CY,GAAG,CAACL,KAAK,CAACpC,gBAAgB,CAAC6B,UAAU,CAACQ,MAAM,CAAC,GAC7CI,GAAG;AACT;AAEA,OAAM,MAAOC,uBAAuB;EAGlCzC,YAAA;IACE,IAAI,CAACZ,SAAS,GAAGd,mBAAmB,EAAE;EACxC;EAEA,MAAMoE,UAAUA,CAAA;IACd,OAAO,IAAInE,OAAO,CACd,CAACC,OAAO,EAAEC,MAAM,KAAI;MAClB,MAAMiB,WAAW,GACb,IAAI,CAACN,SAAS,CAACoB,IAAI,CAACxC,aAAa,EAAEC,gBAAgB,CAAC;MACxDyB,WAAW,CAACe,eAAe,GAAG,MAAMhB,aAAa,CAACC,WAAW,CAAC;MAE9DA,WAAW,CAACf,SAAS,GAAG,MAAK;QAC3B,MAAMgB,EAAE,GAAGD,WAAW,CAACE,MAAM;QAC7B,MAAM+C,EAAE,GAAGhD,EAAE,CAACgB,WAAW,CAACxC,eAAe,EAAE,UAAU,CAAC;QACtD,MAAMyE,KAAK,GAAGD,EAAE,CAAC9B,WAAW,CAAC1C,eAAe,CAAC;QAC7C;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA,MAAM0E,iBAAiB,GAAID,KAAa,CAACE,MAAM,EAAgB;QAC/DD,iBAAiB,CAAClE,SAAS,GAAG,MAAK;UACjC,MAAMoE,GAAG,GAAyC,EAAE;UACpD,KAAK,MAAMC,IAAI,IAAIH,iBAAiB,CAACjD,MAAM,EAAE;YAC3CmD,GAAG,CAACC,IAAI,CAAC/C,SAAS,CAAC,GAAG+C,IAAI,CAAC5B,kBAAkB;;UAE/C5C,OAAO,CAACuE,GAAG,CAAC;QACd,CAAC;QACDF,iBAAiB,CAACjE,OAAO,GAAGC,KAAK,IAAG;UAClCc,EAAE,CAACqB,KAAK,EAAE;UACV,OAAOvC,MAAM,CAACoE,iBAAiB,CAAChE,KAAK,CAAC;QACxC,CAAC;QACD8D,EAAE,CAAC1B,UAAU,GAAG,MAAMtB,EAAE,CAACqB,KAAK,EAAE;MAClC,CAAC;MACDtB,WAAW,CAACd,OAAO,GAAGC,KAAK,IAAIJ,MAAM,CAACiB,WAAW,CAACb,KAAK,CAAC;IAC1D,CAAC,CAAC;EACR;EAEA,MAAMoE,WAAWA,CAACC,IAAY;IAC5BA,IAAI,GAAGX,gBAAgB,CAACW,IAAI,CAAC;IAC7B,OAAO,IAAI3E,OAAO,CAAqB,CAACC,OAAO,EAAEC,MAAM,KAAI;MACzD,MAAMiB,WAAW,GAAG,IAAI,CAACN,SAAS,CAACoB,IAAI,CAACxC,aAAa,EAAEC,gBAAgB,CAAC;MACxEyB,WAAW,CAACe,eAAe,GAAG,MAAMhB,aAAa,CAACC,WAAW,CAAC;MAE9DA,WAAW,CAACf,SAAS,GAAG,MAAK;QAC3B,MAAMgB,EAAE,GAAGD,WAAW,CAACE,MAAM;QAC7B,MAAMyB,MAAM,GAAG1B,EAAE,CAACgB,WAAW,CAACxC,eAAe,EAAE,WAAW,CAAC;QAC3D,MAAMmD,SAAS,GAAGD,MAAM,CAACR,WAAW,CAAC1C,eAAe,CAAC;QAErD,MAAMgF,cAAc,GAAG7B,SAAS,CAACP,GAAG,CAACmC,IAAI,CAAC;QAC1C,IAAIxC,OAAuB;QAC3ByC,cAAc,CAACxE,SAAS,GAAG,MAAK;UAC9B,IAAIwE,cAAc,CAACvD,MAAM,IAAI,IAAI,EAAE;YACjCD,EAAE,CAACqB,KAAK,EAAE;YACV,OAAOvC,MAAM,CAAC,IAAIM,KAAK,CACnB,gCAAgCmE,IAAI,IAAI,GACxC,eAAe,CAAC,CAAC;WACtB,MAAM;YACL;YACA,MAAMxB,iBAAiB,GAAGJ,SAAS,CAACK,MAAM,CAACuB,IAAI,CAAC;YAChD,MAAME,eAAe,GAAGA,CAAA,KAAK;cAC3B;cACA1C,OAAO,GAAGf,EAAE,CAACgB,WAAW,CAACzC,gBAAgB,EAAE,WAAW,CAAC;cACvD,MAAM0C,UAAU,GAAGF,OAAO,CAACG,WAAW,CAAC3C,gBAAgB,CAAC;cACxD,MAAMmF,kBAAkB,GAAGzC,UAAU,CAACe,MAAM,CAACuB,IAAI,CAAC;cAClDG,kBAAkB,CAAC1E,SAAS,GAAG,MAC3BH,OAAO,CAAC2E,cAAc,CAACvD,MAAM,CAACwB,kBAAkB,CAAC;cACrDiC,kBAAkB,CAACzE,OAAO,GAAGC,KAAK,IAC9BJ,MAAM,CAAC0E,cAAc,CAACtE,KAAK,CAAC;YAClC,CAAC;YACD;YACA;YACA6C,iBAAiB,CAAC/C,SAAS,GAAGyE,eAAe;YAC7C1B,iBAAiB,CAAC9C,OAAO,GAAGC,KAAK,IAAG;cAClCuE,eAAe,EAAE;cACjBzD,EAAE,CAACqB,KAAK,EAAE;cACV,OAAOvC,MAAM,CAAC0E,cAAc,CAACtE,KAAK,CAAC;YACrC,CAAC;;QAEL,CAAC;QACDsE,cAAc,CAACvE,OAAO,GAAGC,KAAK,IAAG;UAC/Bc,EAAE,CAACqB,KAAK,EAAE;UACV,OAAOvC,MAAM,CAAC0E,cAAc,CAACtE,KAAK,CAAC;QACrC,CAAC;QAEDwC,MAAM,CAACJ,UAAU,GAAG,MAAK;UACvB,IAAIP,OAAO,IAAI,IAAI,EAAE;YACnBf,EAAE,CAACqB,KAAK,EAAE;WACX,MAAM;YACLN,OAAO,CAACO,UAAU,GAAG,MAAMtB,EAAE,CAACqB,KAAK,EAAE;;QAEzC,CAAC;MACH,CAAC;MACDtB,WAAW,CAACd,OAAO,GAAGC,KAAK,IAAIJ,MAAM,CAACiB,WAAW,CAACb,KAAK,CAAC;IAC1D,CAAC,CAAC;EACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}