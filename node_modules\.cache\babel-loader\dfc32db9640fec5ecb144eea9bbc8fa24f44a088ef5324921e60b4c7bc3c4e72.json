{"ast": null, "code": "/**\n * @license\n * Copyright 2021 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { sizeFromShape } from '../../util';\n/**\n * Generates sparse reshape multiple negative 1 output dimension error message.\n *\n * @param dim1 The first dimension with a negative 1 value.\n * @param dim2 The second dimension with a negative 1 value.\n */\nexport function getSparseReshapeMultipleNegativeOneOutputDimErrorMessage(dim1, dim2) {\n  return `only one output dimension may be -1, not both ${dim1} and ${dim2}`;\n}\n/**\n * Generates sparse reshape negative output dimension error message.\n *\n * @param dim The dimension with a negative value.\n * @param value The negative value.\n */\nexport function getSparseReshapeNegativeOutputDimErrorMessage(dim, value) {\n  return `size ${dim} must be non-negative, not ${value}`;\n}\n/**\n * Generates sparse reshape empty tensor zero output dimension error message.\n *\n */\nexport function getSparseReshapeEmptyTensorZeroOutputDimErrorMessage() {\n  return 'reshape cannot infer the missing input size for an empty tensor ' + 'unless all specified input sizes are non-zero';\n}\n/**\n * Generates sparse reshape input output multiple mismatch error message.\n *\n * @param inputShape the input shape.\n * @param outputShape the requested output shape.\n */\nexport function getSparseReshapeInputOutputMultipleErrorMessage(inputShape, outputShape) {\n  const inputSize = sizeFromShape(inputShape);\n  const outputSize = sizeFromShape(outputShape);\n  return `Input to reshape is a SparseTensor with ${inputSize}\n  dense values, but the requested shape requires a multiple of ${outputSize}. inputShape=${inputShape} outputShape= ${outputShape}`;\n}\n/**\n * Generates sparse reshape input output inequality error message.\n *\n * @param inputShape the input shape.\n * @param outputShape the requested output shape.\n */\nexport function getSparseReshapeInputOutputMismatchErrorMessage(inputShape, outputShape) {\n  const inputSize = sizeFromShape(inputShape);\n  const outputSize = sizeFromShape(outputShape);\n  return `Input to reshape is a tensor with ${inputSize} dense values, but the requested shape has ${outputSize}. inputShape=${inputShape} outputShape=${outputShape}`;\n}", "map": {"version": 3, "names": ["sizeFromShape", "getSparseReshapeMultipleNegativeOneOutputDimErrorMessage", "dim1", "dim2", "getSparseReshapeNegativeOutputDimErrorMessage", "dim", "value", "getSparseReshapeEmptyTensorZeroOutputDimErrorMessage", "getSparseReshapeInputOutputMultipleErrorMessage", "inputShape", "outputShape", "inputSize", "outputSize", "getSparseReshapeInputOutputMismatchErrorMessage"], "sources": ["C:\\tfjs-core\\src\\ops\\sparse\\sparse_reshape_util.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2021 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport {sizeFromShape} from '../../util';\n\n/**\n * Generates sparse reshape multiple negative 1 output dimension error message.\n *\n * @param dim1 The first dimension with a negative 1 value.\n * @param dim2 The second dimension with a negative 1 value.\n */\nexport function getSparseReshapeMultipleNegativeOneOutputDimErrorMessage(\n    dim1: number, dim2: number) {\n  return `only one output dimension may be -1, not both ${dim1} and ${dim2}`;\n}\n\n/**\n * Generates sparse reshape negative output dimension error message.\n *\n * @param dim The dimension with a negative value.\n * @param value The negative value.\n */\nexport function getSparseReshapeNegativeOutputDimErrorMessage(\n    dim: number, value: number) {\n  return `size ${dim} must be non-negative, not ${value}`;\n}\n\n/**\n * Generates sparse reshape empty tensor zero output dimension error message.\n *\n */\nexport function getSparseReshapeEmptyTensorZeroOutputDimErrorMessage() {\n  return 'reshape cannot infer the missing input size for an empty tensor ' +\n      'unless all specified input sizes are non-zero';\n}\n\n/**\n * Generates sparse reshape input output multiple mismatch error message.\n *\n * @param inputShape the input shape.\n * @param outputShape the requested output shape.\n */\nexport function getSparseReshapeInputOutputMultipleErrorMessage(\n    inputShape: number[], outputShape: number[]) {\n  const inputSize = sizeFromShape(inputShape);\n  const outputSize = sizeFromShape(outputShape);\n  return `Input to reshape is a SparseTensor with ${inputSize}\n  dense values, but the requested shape requires a multiple of ${\n      outputSize}. inputShape=${inputShape} outputShape= ${outputShape}`;\n}\n\n/**\n * Generates sparse reshape input output inequality error message.\n *\n * @param inputShape the input shape.\n * @param outputShape the requested output shape.\n */\nexport function getSparseReshapeInputOutputMismatchErrorMessage(\n    inputShape: number[], outputShape: number[]) {\n  const inputSize = sizeFromShape(inputShape);\n  const outputSize = sizeFromShape(outputShape);\n  return `Input to reshape is a tensor with ${\n      inputSize} dense values, but the requested shape has ${\n      outputSize}. inputShape=${inputShape} outputShape=${outputShape}`;\n}\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAgBA,SAAQA,aAAa,QAAO,YAAY;AAExC;;;;;;AAMA,OAAM,SAAUC,wDAAwDA,CACpEC,IAAY,EAAEC,IAAY;EAC5B,OAAO,iDAAiDD,IAAI,QAAQC,IAAI,EAAE;AAC5E;AAEA;;;;;;AAMA,OAAM,SAAUC,6CAA6CA,CACzDC,GAAW,EAAEC,KAAa;EAC5B,OAAO,QAAQD,GAAG,8BAA8BC,KAAK,EAAE;AACzD;AAEA;;;;AAIA,OAAM,SAAUC,oDAAoDA,CAAA;EAClE,OAAO,kEAAkE,GACrE,+CAA+C;AACrD;AAEA;;;;;;AAMA,OAAM,SAAUC,+CAA+CA,CAC3DC,UAAoB,EAAEC,WAAqB;EAC7C,MAAMC,SAAS,GAAGX,aAAa,CAACS,UAAU,CAAC;EAC3C,MAAMG,UAAU,GAAGZ,aAAa,CAACU,WAAW,CAAC;EAC7C,OAAO,2CAA2CC,SAAS;iEAEvDC,UAAU,gBAAgBH,UAAU,iBAAiBC,WAAW,EAAE;AACxE;AAEA;;;;;;AAMA,OAAM,SAAUG,+CAA+CA,CAC3DJ,UAAoB,EAAEC,WAAqB;EAC7C,MAAMC,SAAS,GAAGX,aAAa,CAACS,UAAU,CAAC;EAC3C,MAAMG,UAAU,GAAGZ,aAAa,CAACU,WAAW,CAAC;EAC7C,OAAO,qCACHC,SAAS,8CACTC,UAAU,gBAAgBH,UAAU,gBAAgBC,WAAW,EAAE;AACvE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}