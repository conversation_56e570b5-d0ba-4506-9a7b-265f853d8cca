{"ast": null, "code": "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { gather } from '../../ops/gather';\nimport { getGlobalTensorClass } from '../../tensor';\ngetGlobalTensorClass().prototype.gather = function (indices, axis, batchDims) {\n  this.throwIfDisposed();\n  return gather(this, indices, axis, batchDims);\n};", "map": {"version": 3, "names": ["gather", "getGlobalTensorClass", "prototype", "indices", "axis", "batchDims", "throwIfDisposed"], "sources": ["C:\\tfjs-core\\src\\public\\chained_ops\\gather.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {gather} from '../../ops/gather';\nimport {getGlobalTensorClass, Tensor} from '../../tensor';\nimport {Rank, TensorLike} from '../../types';\n\ndeclare module '../../tensor' {\n  interface Tensor<R extends Rank = Rank> {\n    gather<T extends Tensor>(\n        this: T, indices: Tensor|TensorLike, axis?: number,\n        batchDims?: number): T;\n  }\n}\n\ngetGlobalTensorClass().prototype.gather = function<T extends Tensor>(\n    this: T, indices: Tensor|TensorLike, axis?: number, batchDims?: number): T {\n  this.throwIfDisposed();\n  return gather(this, indices, axis, batchDims);\n};\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAiBA,SAAQA,MAAM,QAAO,kBAAkB;AACvC,SAAQC,oBAAoB,QAAe,cAAc;AAWzDA,oBAAoB,EAAE,CAACC,SAAS,CAACF,MAAM,GAAG,UAC7BG,OAA0B,EAAEC,IAAa,EAAEC,SAAkB;EACxE,IAAI,CAACC,eAAe,EAAE;EACtB,OAAON,MAAM,CAAC,IAAI,EAAEG,OAAO,EAAEC,IAAI,EAAEC,SAAS,CAAC;AAC/C,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}