{"ast": null, "code": "/**\n * @license\n * Copyright 2021 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { backend_util, util } from '@tensorflow/tfjs-core';\nexport function sparseFillEmptyRowsImpl(indices, indicesShape, indicesDType, values, valuesDType, denseShape, defaultValue) {\n  const indicesCount = indicesShape[0];\n  const denseRows = denseShape[0];\n  const emptyRowIndicator = new Array(denseRows);\n  const reverseIndexMap = new Array(indicesCount);\n  const rank = indicesShape[1];\n  if (denseRows === 0) {\n    if (indicesCount !== 0) {\n      throw new Error(backend_util.getSparseFillEmptyRowsIndicesDenseShapeMismatch(indicesCount));\n    }\n    const outputIndices = util.getArrayFromDType(indicesDType, 0);\n    const outputValues = util.getArrayFromDType(valuesDType, 0);\n    return [outputIndices, [0, rank], outputValues, emptyRowIndicator, reverseIndexMap];\n  }\n  let rowsAreOrdered = true;\n  let lastIndicesRow = 0;\n  const csrOffset = new Array(denseRows).fill(0);\n  for (let i = 0; i < indicesCount; ++i) {\n    // indices is a 2d tensor with shape of [N, rank]\n    const row = indices[i * rank];\n    if (row < 0) {\n      throw new Error(backend_util.getSparseFillEmptyRowsNegativeIndexErrorMessage(i, row));\n    }\n    if (row >= denseRows) {\n      throw new Error(backend_util.getSparseFillEmptyRowsOutOfRangeIndexErrorMessage(i, row, denseRows));\n    }\n    ++csrOffset[row];\n    rowsAreOrdered = rowsAreOrdered && row >= lastIndicesRow;\n    lastIndicesRow = row;\n  }\n  let allRowsFull = true;\n  for (let row = 0; row < denseRows; ++row) {\n    // csrOffset here describes the number of elements in this dense row\n    const rowEmpty = csrOffset[row] === 0;\n    emptyRowIndicator[row] = rowEmpty;\n    allRowsFull = allRowsFull && !rowEmpty;\n    // In filled version, each row has at least one element.\n    csrOffset[row] = Math.max(csrOffset[row], 1);\n    // Update csrOffset to represent the number of elements up to and\n    // including denseRows + 1:\n    //  csrOffset[0] == #{elements of row 0}\n    //  csrOffset[1] == #{elements of row 1} + #{elements of row 0}\n    //  ..\n    //  csrOffset[i] == starting index for elements in row i + 1.\n    if (row > 0) {\n      csrOffset[row] += csrOffset[row - 1];\n    }\n  }\n  if (allRowsFull && rowsAreOrdered) {\n    const outputIndices = indices;\n    const outputValues = values;\n    for (let i = 0; i < indicesCount; ++i) {\n      reverseIndexMap[i] = i;\n    }\n    return [outputIndices, [indicesCount, rank], outputValues, emptyRowIndicator, reverseIndexMap];\n  } else {\n    const fullIndicesCount = csrOffset[denseRows - 1];\n    const outputIndices = util.getArrayFromDType(indicesDType, fullIndicesCount * rank);\n    const outputValues = util.getArrayFromDType(valuesDType, fullIndicesCount);\n    const filledCount = new Array(denseRows).fill(0);\n    // Fill in values for rows that are not missing\n    for (let i = 0; i < indicesCount; ++i) {\n      // indices is a 2d tensor with shape of [N, rank]\n      const row = indices[i * rank];\n      const offset = filledCount[row];\n      const outputI = (row === 0 ? 0 : csrOffset[row - 1]) + offset;\n      filledCount[row]++; // Increment the filled count for this row.\n      for (let j = 0; j < rank; ++j) {\n        // indices and outputIndices are 2d tensors with shape of [N, rank]\n        outputIndices[outputI * rank + j] = indices[i * rank + j];\n      }\n      outputValues[outputI] = values[i];\n      // We'll need this reverse index map to backprop correctly.\n      reverseIndexMap[i] = outputI;\n    }\n    // Fill in values for rows that are missing\n    for (let row = 0; row < denseRows; ++row) {\n      const rowCount = filledCount[row];\n      if (rowCount === 0) {\n        // We haven't filled this row\n        const startingIndex = row === 0 ? 0 : csrOffset[row - 1];\n        // Remaining index values were set to zero already.\n        // Just need to set the row index in the right location.\n        // outputIndices is a 2d tensor with shape of [N, rank]\n        outputIndices[startingIndex * rank + 0] = row;\n        for (let col = 1; col < rank; ++col) {\n          outputIndices[startingIndex * rank + col] = 0;\n        }\n        outputValues[startingIndex] = defaultValue;\n      }\n    }\n    return [outputIndices, [fullIndicesCount, rank], outputValues, emptyRowIndicator, reverseIndexMap];\n  }\n}", "map": {"version": 3, "names": ["backend_util", "util", "sparseFillEmptyRowsImpl", "indices", "indicesShape", "indicesDType", "values", "valuesDType", "denseShape", "defaultValue", "indicesCount", "denseRows", "emptyRowIndicator", "Array", "reverseIndexMap", "rank", "Error", "getSparseFillEmptyRowsIndicesDenseShapeMismatch", "outputIndices", "getArrayFromDType", "outputValues", "rowsAreOrdered", "lastIndicesRow", "csrOffset", "fill", "i", "row", "getSparseFillEmptyRowsNegativeIndexErrorMessage", "getSparseFillEmptyRowsOutOfRangeIndexErrorMessage", "allRowsFull", "rowEmpty", "Math", "max", "fullIndicesCount", "filledCount", "offset", "outputI", "j", "rowCount", "startingIndex", "col"], "sources": ["C:\\tfjs-backend-cpu\\src\\kernels\\SparseFillEmptyRows_impl.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2021 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {backend_util, DataType, TypedArray, util} from '@tensorflow/tfjs-core';\n\nexport function sparseFillEmptyRowsImpl(\n    indices: TypedArray, indicesShape: number[], indicesDType: DataType,\n    values: TypedArray, valuesDType: DataType, denseShape: TypedArray,\n    defaultValue: number):\n    [TypedArray, number[], TypedArray, boolean[], number[]] {\n  const indicesCount = indicesShape[0];\n  const denseRows = denseShape[0];\n\n  const emptyRowIndicator: boolean[] = new Array(denseRows);\n  const reverseIndexMap: number[] = new Array(indicesCount);\n\n  const rank = indicesShape[1];\n\n  if (denseRows === 0) {\n    if (indicesCount !== 0) {\n      throw new Error(\n          backend_util.getSparseFillEmptyRowsIndicesDenseShapeMismatch(\n              indicesCount));\n    }\n    const outputIndices = util.getArrayFromDType(indicesDType, 0) as TypedArray;\n    const outputValues = util.getArrayFromDType(valuesDType, 0) as TypedArray;\n    return [\n      outputIndices, [0, rank], outputValues, emptyRowIndicator, reverseIndexMap\n    ];\n  }\n\n  let rowsAreOrdered = true;\n  let lastIndicesRow = 0;\n  const csrOffset: number[] = new Array(denseRows).fill(0);\n\n  for (let i = 0; i < indicesCount; ++i) {\n    // indices is a 2d tensor with shape of [N, rank]\n    const row = indices[i * rank];\n    if (row < 0) {\n      throw new Error(\n          backend_util.getSparseFillEmptyRowsNegativeIndexErrorMessage(i, row));\n    }\n    if (row >= denseRows) {\n      throw new Error(\n          backend_util.getSparseFillEmptyRowsOutOfRangeIndexErrorMessage(\n              i, row, denseRows));\n    }\n    ++csrOffset[row];\n    rowsAreOrdered = rowsAreOrdered && (row >= lastIndicesRow);\n    lastIndicesRow = row;\n  }\n\n  let allRowsFull = true;\n  for (let row = 0; row < denseRows; ++row) {\n    // csrOffset here describes the number of elements in this dense row\n    const rowEmpty = (csrOffset[row] === 0);\n    emptyRowIndicator[row] = rowEmpty;\n    allRowsFull = allRowsFull && !rowEmpty;\n    // In filled version, each row has at least one element.\n    csrOffset[row] = Math.max(csrOffset[row], 1);\n    // Update csrOffset to represent the number of elements up to and\n    // including denseRows + 1:\n    //  csrOffset[0] == #{elements of row 0}\n    //  csrOffset[1] == #{elements of row 1} + #{elements of row 0}\n    //  ..\n    //  csrOffset[i] == starting index for elements in row i + 1.\n    if (row > 0) {\n      csrOffset[row] += csrOffset[row - 1];\n    }\n  }\n\n  if (allRowsFull && rowsAreOrdered) {\n    const outputIndices: TypedArray = indices;\n    const outputValues: TypedArray = values;\n    for (let i = 0; i < indicesCount; ++i) {\n      reverseIndexMap[i] = i;\n    }\n    return [\n      outputIndices, [indicesCount, rank], outputValues, emptyRowIndicator,\n      reverseIndexMap\n    ];\n  } else {\n    const fullIndicesCount = csrOffset[denseRows - 1];\n    const outputIndices =\n        util.getArrayFromDType(indicesDType, fullIndicesCount * rank) as\n        TypedArray;\n    const outputValues =\n        util.getArrayFromDType(valuesDType, fullIndicesCount) as TypedArray;\n    const filledCount: number[] = new Array(denseRows).fill(0);\n\n    // Fill in values for rows that are not missing\n    for (let i = 0; i < indicesCount; ++i) {\n      // indices is a 2d tensor with shape of [N, rank]\n      const row = indices[i * rank];\n      const offset = filledCount[row];\n      const outputI = ((row === 0) ? 0 : csrOffset[row - 1]) + offset;\n      filledCount[row]++;  // Increment the filled count for this row.\n      for (let j = 0; j < rank; ++j) {\n        // indices and outputIndices are 2d tensors with shape of [N, rank]\n        outputIndices[outputI * rank + j] = indices[i * rank + j];\n      }\n      outputValues[outputI] = values[i];\n      // We'll need this reverse index map to backprop correctly.\n      reverseIndexMap[i] = outputI;\n    }\n\n    // Fill in values for rows that are missing\n    for (let row = 0; row < denseRows; ++row) {\n      const rowCount = filledCount[row];\n      if (rowCount === 0) {  // We haven't filled this row\n        const startingIndex = (row === 0) ? 0 : csrOffset[row - 1];\n        // Remaining index values were set to zero already.\n        // Just need to set the row index in the right location.\n        // outputIndices is a 2d tensor with shape of [N, rank]\n        outputIndices[startingIndex * rank + 0] = row;\n        for (let col = 1; col < rank; ++col) {\n          outputIndices[startingIndex * rank + col] = 0;\n        }\n        outputValues[startingIndex] = defaultValue;\n      }\n    }\n    return [\n      outputIndices, [fullIndicesCount, rank], outputValues, emptyRowIndicator,\n      reverseIndexMap\n    ];\n  }\n}\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAiBA,SAAQA,YAAY,EAAwBC,IAAI,QAAO,uBAAuB;AAE9E,OAAM,SAAUC,uBAAuBA,CACnCC,OAAmB,EAAEC,YAAsB,EAAEC,YAAsB,EACnEC,MAAkB,EAAEC,WAAqB,EAAEC,UAAsB,EACjEC,YAAoB;EAEtB,MAAMC,YAAY,GAAGN,YAAY,CAAC,CAAC,CAAC;EACpC,MAAMO,SAAS,GAAGH,UAAU,CAAC,CAAC,CAAC;EAE/B,MAAMI,iBAAiB,GAAc,IAAIC,KAAK,CAACF,SAAS,CAAC;EACzD,MAAMG,eAAe,GAAa,IAAID,KAAK,CAACH,YAAY,CAAC;EAEzD,MAAMK,IAAI,GAAGX,YAAY,CAAC,CAAC,CAAC;EAE5B,IAAIO,SAAS,KAAK,CAAC,EAAE;IACnB,IAAID,YAAY,KAAK,CAAC,EAAE;MACtB,MAAM,IAAIM,KAAK,CACXhB,YAAY,CAACiB,+CAA+C,CACxDP,YAAY,CAAC,CAAC;;IAExB,MAAMQ,aAAa,GAAGjB,IAAI,CAACkB,iBAAiB,CAACd,YAAY,EAAE,CAAC,CAAe;IAC3E,MAAMe,YAAY,GAAGnB,IAAI,CAACkB,iBAAiB,CAACZ,WAAW,EAAE,CAAC,CAAe;IACzE,OAAO,CACLW,aAAa,EAAE,CAAC,CAAC,EAAEH,IAAI,CAAC,EAAEK,YAAY,EAAER,iBAAiB,EAAEE,eAAe,CAC3E;;EAGH,IAAIO,cAAc,GAAG,IAAI;EACzB,IAAIC,cAAc,GAAG,CAAC;EACtB,MAAMC,SAAS,GAAa,IAAIV,KAAK,CAACF,SAAS,CAAC,CAACa,IAAI,CAAC,CAAC,CAAC;EAExD,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGf,YAAY,EAAE,EAAEe,CAAC,EAAE;IACrC;IACA,MAAMC,GAAG,GAAGvB,OAAO,CAACsB,CAAC,GAAGV,IAAI,CAAC;IAC7B,IAAIW,GAAG,GAAG,CAAC,EAAE;MACX,MAAM,IAAIV,KAAK,CACXhB,YAAY,CAAC2B,+CAA+C,CAACF,CAAC,EAAEC,GAAG,CAAC,CAAC;;IAE3E,IAAIA,GAAG,IAAIf,SAAS,EAAE;MACpB,MAAM,IAAIK,KAAK,CACXhB,YAAY,CAAC4B,iDAAiD,CAC1DH,CAAC,EAAEC,GAAG,EAAEf,SAAS,CAAC,CAAC;;IAE7B,EAAEY,SAAS,CAACG,GAAG,CAAC;IAChBL,cAAc,GAAGA,cAAc,IAAKK,GAAG,IAAIJ,cAAe;IAC1DA,cAAc,GAAGI,GAAG;;EAGtB,IAAIG,WAAW,GAAG,IAAI;EACtB,KAAK,IAAIH,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAGf,SAAS,EAAE,EAAEe,GAAG,EAAE;IACxC;IACA,MAAMI,QAAQ,GAAIP,SAAS,CAACG,GAAG,CAAC,KAAK,CAAE;IACvCd,iBAAiB,CAACc,GAAG,CAAC,GAAGI,QAAQ;IACjCD,WAAW,GAAGA,WAAW,IAAI,CAACC,QAAQ;IACtC;IACAP,SAAS,CAACG,GAAG,CAAC,GAAGK,IAAI,CAACC,GAAG,CAACT,SAAS,CAACG,GAAG,CAAC,EAAE,CAAC,CAAC;IAC5C;IACA;IACA;IACA;IACA;IACA;IACA,IAAIA,GAAG,GAAG,CAAC,EAAE;MACXH,SAAS,CAACG,GAAG,CAAC,IAAIH,SAAS,CAACG,GAAG,GAAG,CAAC,CAAC;;;EAIxC,IAAIG,WAAW,IAAIR,cAAc,EAAE;IACjC,MAAMH,aAAa,GAAef,OAAO;IACzC,MAAMiB,YAAY,GAAed,MAAM;IACvC,KAAK,IAAImB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGf,YAAY,EAAE,EAAEe,CAAC,EAAE;MACrCX,eAAe,CAACW,CAAC,CAAC,GAAGA,CAAC;;IAExB,OAAO,CACLP,aAAa,EAAE,CAACR,YAAY,EAAEK,IAAI,CAAC,EAAEK,YAAY,EAAER,iBAAiB,EACpEE,eAAe,CAChB;GACF,MAAM;IACL,MAAMmB,gBAAgB,GAAGV,SAAS,CAACZ,SAAS,GAAG,CAAC,CAAC;IACjD,MAAMO,aAAa,GACfjB,IAAI,CAACkB,iBAAiB,CAACd,YAAY,EAAE4B,gBAAgB,GAAGlB,IAAI,CAClD;IACd,MAAMK,YAAY,GACdnB,IAAI,CAACkB,iBAAiB,CAACZ,WAAW,EAAE0B,gBAAgB,CAAe;IACvE,MAAMC,WAAW,GAAa,IAAIrB,KAAK,CAACF,SAAS,CAAC,CAACa,IAAI,CAAC,CAAC,CAAC;IAE1D;IACA,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGf,YAAY,EAAE,EAAEe,CAAC,EAAE;MACrC;MACA,MAAMC,GAAG,GAAGvB,OAAO,CAACsB,CAAC,GAAGV,IAAI,CAAC;MAC7B,MAAMoB,MAAM,GAAGD,WAAW,CAACR,GAAG,CAAC;MAC/B,MAAMU,OAAO,GAAG,CAAEV,GAAG,KAAK,CAAC,GAAI,CAAC,GAAGH,SAAS,CAACG,GAAG,GAAG,CAAC,CAAC,IAAIS,MAAM;MAC/DD,WAAW,CAACR,GAAG,CAAC,EAAE,CAAC,CAAE;MACrB,KAAK,IAAIW,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGtB,IAAI,EAAE,EAAEsB,CAAC,EAAE;QAC7B;QACAnB,aAAa,CAACkB,OAAO,GAAGrB,IAAI,GAAGsB,CAAC,CAAC,GAAGlC,OAAO,CAACsB,CAAC,GAAGV,IAAI,GAAGsB,CAAC,CAAC;;MAE3DjB,YAAY,CAACgB,OAAO,CAAC,GAAG9B,MAAM,CAACmB,CAAC,CAAC;MACjC;MACAX,eAAe,CAACW,CAAC,CAAC,GAAGW,OAAO;;IAG9B;IACA,KAAK,IAAIV,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAGf,SAAS,EAAE,EAAEe,GAAG,EAAE;MACxC,MAAMY,QAAQ,GAAGJ,WAAW,CAACR,GAAG,CAAC;MACjC,IAAIY,QAAQ,KAAK,CAAC,EAAE;QAAG;QACrB,MAAMC,aAAa,GAAIb,GAAG,KAAK,CAAC,GAAI,CAAC,GAAGH,SAAS,CAACG,GAAG,GAAG,CAAC,CAAC;QAC1D;QACA;QACA;QACAR,aAAa,CAACqB,aAAa,GAAGxB,IAAI,GAAG,CAAC,CAAC,GAAGW,GAAG;QAC7C,KAAK,IAAIc,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAGzB,IAAI,EAAE,EAAEyB,GAAG,EAAE;UACnCtB,aAAa,CAACqB,aAAa,GAAGxB,IAAI,GAAGyB,GAAG,CAAC,GAAG,CAAC;;QAE/CpB,YAAY,CAACmB,aAAa,CAAC,GAAG9B,YAAY;;;IAG9C,OAAO,CACLS,aAAa,EAAE,CAACe,gBAAgB,EAAElB,IAAI,CAAC,EAAEK,YAAY,EAAER,iBAAiB,EACxEE,eAAe,CAChB;;AAEL", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}