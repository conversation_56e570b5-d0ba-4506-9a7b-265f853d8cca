{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.transformReply = exports.transformArguments = exports.IS_READ_ONLY = exports.FIRST_KEY_INDEX = void 0;\nexports.FIRST_KEY_INDEX = 1;\nexports.IS_READ_ONLY = true;\nfunction transformArguments(key) {\n  return ['CF.INFO', key];\n}\nexports.transformArguments = transformArguments;\nfunction transformReply(reply) {\n  return {\n    size: reply[1],\n    numberOfBuckets: reply[3],\n    numberOfFilters: reply[5],\n    numberOfInsertedItems: reply[7],\n    numberOfDeletedItems: reply[9],\n    bucketSize: reply[11],\n    expansionRate: reply[13],\n    maxIteration: reply[15]\n  };\n}\nexports.transformReply = transformReply;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "transformReply", "transformArguments", "IS_READ_ONLY", "FIRST_KEY_INDEX", "key", "reply", "size", "numberOfBuckets", "numberOfFilters", "numberOfInsertedItems", "numberOfDeletedItems", "bucketSize", "expansionRate", "maxIteration"], "sources": ["C:/tmsft/node_modules/@redis/bloom/dist/commands/cuckoo/INFO.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.transformReply = exports.transformArguments = exports.IS_READ_ONLY = exports.FIRST_KEY_INDEX = void 0;\nexports.FIRST_KEY_INDEX = 1;\nexports.IS_READ_ONLY = true;\nfunction transformArguments(key) {\n    return ['CF.INFO', key];\n}\nexports.transformArguments = transformArguments;\nfunction transformReply(reply) {\n    return {\n        size: reply[1],\n        numberOfBuckets: reply[3],\n        numberOfFilters: reply[5],\n        numberOfInsertedItems: reply[7],\n        numberOfDeletedItems: reply[9],\n        bucketSize: reply[11],\n        expansionRate: reply[13],\n        maxIteration: reply[15]\n    };\n}\nexports.transformReply = transformReply;\n"], "mappings": "AAAA,YAAY;;AACZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEC,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7DD,OAAO,CAACE,cAAc,GAAGF,OAAO,CAACG,kBAAkB,GAAGH,OAAO,CAACI,YAAY,GAAGJ,OAAO,CAACK,eAAe,GAAG,KAAK,CAAC;AAC7GL,OAAO,CAACK,eAAe,GAAG,CAAC;AAC3BL,OAAO,CAACI,YAAY,GAAG,IAAI;AAC3B,SAASD,kBAAkBA,CAACG,GAAG,EAAE;EAC7B,OAAO,CAAC,SAAS,EAAEA,GAAG,CAAC;AAC3B;AACAN,OAAO,CAACG,kBAAkB,GAAGA,kBAAkB;AAC/C,SAASD,cAAcA,CAACK,KAAK,EAAE;EAC3B,OAAO;IACHC,IAAI,EAAED,KAAK,CAAC,CAAC,CAAC;IACdE,eAAe,EAAEF,KAAK,CAAC,CAAC,CAAC;IACzBG,eAAe,EAAEH,KAAK,CAAC,CAAC,CAAC;IACzBI,qBAAqB,EAAEJ,KAAK,CAAC,CAAC,CAAC;IAC/BK,oBAAoB,EAAEL,KAAK,CAAC,CAAC,CAAC;IAC9BM,UAAU,EAAEN,KAAK,CAAC,EAAE,CAAC;IACrBO,aAAa,EAAEP,KAAK,CAAC,EAAE,CAAC;IACxBQ,YAAY,EAAER,KAAK,CAAC,EAAE;EAC1B,CAAC;AACL;AACAP,OAAO,CAACE,cAAc,GAAGA,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}