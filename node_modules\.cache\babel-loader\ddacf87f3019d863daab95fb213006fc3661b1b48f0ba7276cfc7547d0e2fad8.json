{"ast": null, "code": "/**\n * @license\n * Copyright 2019 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { Square } from '@tensorflow/tfjs-core';\nimport { unaryKernelFunc } from '../kernel_utils/kernel_funcs_utils';\nconst SQUARE = \"return x * x;\";\nexport const square = unaryKernelFunc({\n  opSnippet: SQUARE\n});\nexport const squareConfig = {\n  kernelName: Square,\n  backendName: 'webgl',\n  kernelFunc: square\n};", "map": {"version": 3, "names": ["Square", "unaryKernelFunc", "SQUARE", "square", "opSnippet", "squareConfig", "kernelName", "backendName", "kernelFunc"], "sources": ["C:\\tfjs-backend-webgl\\src\\kernels\\Square.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2019 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {KernelConfig, Square} from '@tensorflow/tfjs-core';\n\nimport {unaryKernelFunc} from '../kernel_utils/kernel_funcs_utils';\n\nconst SQUARE = `return x * x;`;\n\nexport const square = unaryKernelFunc({opSnippet: SQUARE});\n\nexport const squareConfig: KernelConfig = {\n  kernelName: Square,\n  backendName: 'webgl',\n  kernelFunc: square,\n};\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAiBA,SAAsBA,MAAM,QAAO,uBAAuB;AAE1D,SAAQC,eAAe,QAAO,oCAAoC;AAElE,MAAMC,MAAM,kBAAkB;AAE9B,OAAO,MAAMC,MAAM,GAAGF,eAAe,CAAC;EAACG,SAAS,EAAEF;AAAM,CAAC,CAAC;AAE1D,OAAO,MAAMG,YAAY,GAAiB;EACxCC,UAAU,EAAEN,MAAM;EAClBO,WAAW,EAAE,OAAO;EACpBC,UAAU,EAAEL;CACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}