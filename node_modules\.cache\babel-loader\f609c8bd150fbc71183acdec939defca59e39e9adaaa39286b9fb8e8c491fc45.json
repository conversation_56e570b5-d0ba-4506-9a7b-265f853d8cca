{"ast": null, "code": "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { ComplexAbs } from '../kernel_names';\nimport { absGradConfig } from './Abs_grad';\nexport const complexAbsGradConfig = {\n  kernelName: ComplexAbs,\n  inputsToSave: ['x'],\n  gradFunc: absGradConfig.gradFunc\n};", "map": {"version": 3, "names": ["ComplexAbs", "absGradConfig", "complexAbsGradConfig", "kernelName", "inputsToSave", "grad<PERSON>unc"], "sources": ["C:\\tfjs-core\\src\\gradients\\ComplexAbs_grad.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {ComplexAbs} from '../kernel_names';\nimport {GradConfig} from '../kernel_registry';\nimport {absGradConfig} from './Abs_grad';\n\nexport const complexAbsGradConfig: GradConfig = {\n  kernelName: ComplexAbs,\n  inputsToSave: ['x'],\n  gradFunc: absGradConfig.gradFunc,\n};\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAiBA,SAAQA,UAAU,QAAO,iBAAiB;AAE1C,SAAQC,aAAa,QAAO,YAAY;AAExC,OAAO,MAAMC,oBAAoB,GAAe;EAC9CC,UAAU,EAAEH,UAAU;EACtBI,YAAY,EAAE,CAAC,GAAG,CAAC;EACnBC,QAAQ,EAAEJ,aAAa,CAACI;CACzB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}