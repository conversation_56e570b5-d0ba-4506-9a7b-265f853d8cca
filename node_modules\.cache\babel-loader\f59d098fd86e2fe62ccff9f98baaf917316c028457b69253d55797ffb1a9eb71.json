{"ast": null, "code": "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { ENGINE } from '../engine';\nimport { DepthwiseConv2dNative } from '../kernel_names';\nimport { convertToTensor } from '../tensor_util_env';\nimport * as util from '../util';\nimport * as conv_util from './conv_util';\nimport { op } from './operation';\nimport { reshape } from './reshape';\n/**\n * Depthwise 2D convolution.\n *\n * Given a 4D `input` array and a `filter` array of shape\n * `[filterHeight, filterWidth, inChannels, channelMultiplier]` containing\n * `inChannels` convolutional filters of depth 1, this op applies a\n * different filter to each input channel (expanding from 1 channel to\n * `channelMultiplier` channels for each), then concatenates the results\n * together. The output has `inChannels * channelMultiplier` channels.\n *\n * See\n * [https://www.tensorflow.org/api_docs/python/tf/nn/depthwise_conv2d](\n *     https://www.tensorflow.org/api_docs/python/tf/nn/depthwise_conv2d)\n * for more details.\n *\n * @param x The input tensor, of rank 4 or rank 3, of shape\n *     `[batch, height, width, inChannels]`. If rank 3, batch of 1 is\n * assumed.\n * @param filter The filter tensor, rank 4, of shape\n *     `[filterHeight, filterWidth, inChannels, channelMultiplier]`.\n * @param strides The strides of the convolution: `[strideHeight,\n * strideWidth]`. If strides is a single number, then `strideHeight ==\n * strideWidth`.\n * @param pad The type of padding algorithm.\n *   - `same` and stride 1: output will be of same size as input,\n *       regardless of filter size.\n *   - `valid`: output will be smaller than input if filter is larger\n *       than 1x1.\n *   - For more info, see this guide:\n *     [https://www.tensorflow.org/api_docs/python/tf/nn/convolution](\n *          https://www.tensorflow.org/api_docs/python/tf/nn/convolution)\n * @param dilations The dilation rates: `[dilationHeight, dilationWidth]`\n *     in which we sample input values across the height and width dimensions\n *     in atrous convolution. Defaults to `[1, 1]`. If `rate` is a single\n *     number, then `dilationHeight == dilationWidth`. If it is greater than\n *     1, then all values of `strides` must be 1.\n * @param dataFormat: An optional string from: \"NHWC\", \"NCHW\". Defaults to\n *     \"NHWC\". Specify the data format of the input and output data. With the\n *     default format \"NHWC\", the data is stored in the order of: [batch,\n *     height, width, channels]. Only \"NHWC\" is currently supported.\n * @param dimRoundingMode A string from: 'ceil', 'round', 'floor'. If none is\n *     provided, it will default to truncate.\n *\n * @doc {heading: 'Operations', subheading: 'Convolution'}\n */\nfunction depthwiseConv2d_(x, filter, strides, pad, dataFormat = 'NHWC', dilations = [1, 1], dimRoundingMode) {\n  const $x = convertToTensor(x, 'x', 'depthwiseConv2d', 'float32');\n  const $filter = convertToTensor(filter, 'filter', 'depthwiseConv2d', 'float32');\n  let x4D = $x;\n  let reshapedTo4D = false;\n  if ($x.rank === 3) {\n    reshapedTo4D = true;\n    x4D = reshape($x, [1, $x.shape[0], $x.shape[1], $x.shape[2]]);\n  }\n  util.assert(x4D.rank === 4, () => `Error in depthwiseConv2d: input must be rank 4, but got ` + `rank ${x4D.rank}.`);\n  util.assert($filter.rank === 4, () => `Error in depthwiseConv2d: filter must be rank 4, but got rank ` + `${$filter.rank}.`);\n  const inChannels = dataFormat === 'NHWC' ? x4D.shape[3] : x4D.shape[1];\n  util.assert(inChannels === $filter.shape[2], () => `Error in depthwiseConv2d: number of input channels ` + `(${inChannels}) must match the inChannels dimension in ` + `filter ${$filter.shape[2]}.`);\n  conv_util.checkPadOnDimRoundingMode('depthwiseConv2d', pad, dimRoundingMode);\n  const inputs = {\n    x: x4D,\n    filter: $filter\n  };\n  const attrs = {\n    strides,\n    pad,\n    dataFormat,\n    dilations,\n    dimRoundingMode\n  };\n  // tslint:disable-next-line: no-unnecessary-type-assertion\n  const res = ENGINE.runKernel(DepthwiseConv2dNative, inputs, attrs);\n  if (reshapedTo4D) {\n    return reshape(res, [res.shape[1], res.shape[2], res.shape[3]]);\n  }\n  return res;\n}\nexport const depthwiseConv2d = /* @__PURE__ */op({\n  depthwiseConv2d_\n});", "map": {"version": 3, "names": ["ENGINE", "DepthwiseConv2dNative", "convertToTensor", "util", "conv_util", "op", "reshape", "depthwiseConv2d_", "x", "filter", "strides", "pad", "dataFormat", "dilations", "dimRoundingMode", "$x", "$filter", "x4D", "reshapedTo4D", "rank", "shape", "assert", "inChannels", "checkPadOnDimRoundingMode", "inputs", "attrs", "res", "runKernel", "depthwiseConv2d"], "sources": ["C:\\tfjs-core\\src\\ops\\depthwise_conv2d.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport {ENGINE} from '../engine';\nimport {DepthwiseConv2dNative, DepthwiseConv2dNativeAttrs, DepthwiseConv2dNativeInputs} from '../kernel_names';\nimport {NamedAttrMap} from '../kernel_registry';\nimport {Tensor3D, Tensor4D} from '../tensor';\nimport {NamedTensorMap} from '../tensor_types';\nimport {convertToTensor} from '../tensor_util_env';\nimport {TensorLike} from '../types';\nimport * as util from '../util';\n\nimport * as conv_util from './conv_util';\nimport {op} from './operation';\nimport {reshape} from './reshape';\n\n/**\n * Depthwise 2D convolution.\n *\n * Given a 4D `input` array and a `filter` array of shape\n * `[filterHeight, filterWidth, inChannels, channelMultiplier]` containing\n * `inChannels` convolutional filters of depth 1, this op applies a\n * different filter to each input channel (expanding from 1 channel to\n * `channelMultiplier` channels for each), then concatenates the results\n * together. The output has `inChannels * channelMultiplier` channels.\n *\n * See\n * [https://www.tensorflow.org/api_docs/python/tf/nn/depthwise_conv2d](\n *     https://www.tensorflow.org/api_docs/python/tf/nn/depthwise_conv2d)\n * for more details.\n *\n * @param x The input tensor, of rank 4 or rank 3, of shape\n *     `[batch, height, width, inChannels]`. If rank 3, batch of 1 is\n * assumed.\n * @param filter The filter tensor, rank 4, of shape\n *     `[filterHeight, filterWidth, inChannels, channelMultiplier]`.\n * @param strides The strides of the convolution: `[strideHeight,\n * strideWidth]`. If strides is a single number, then `strideHeight ==\n * strideWidth`.\n * @param pad The type of padding algorithm.\n *   - `same` and stride 1: output will be of same size as input,\n *       regardless of filter size.\n *   - `valid`: output will be smaller than input if filter is larger\n *       than 1x1.\n *   - For more info, see this guide:\n *     [https://www.tensorflow.org/api_docs/python/tf/nn/convolution](\n *          https://www.tensorflow.org/api_docs/python/tf/nn/convolution)\n * @param dilations The dilation rates: `[dilationHeight, dilationWidth]`\n *     in which we sample input values across the height and width dimensions\n *     in atrous convolution. Defaults to `[1, 1]`. If `rate` is a single\n *     number, then `dilationHeight == dilationWidth`. If it is greater than\n *     1, then all values of `strides` must be 1.\n * @param dataFormat: An optional string from: \"NHWC\", \"NCHW\". Defaults to\n *     \"NHWC\". Specify the data format of the input and output data. With the\n *     default format \"NHWC\", the data is stored in the order of: [batch,\n *     height, width, channels]. Only \"NHWC\" is currently supported.\n * @param dimRoundingMode A string from: 'ceil', 'round', 'floor'. If none is\n *     provided, it will default to truncate.\n *\n * @doc {heading: 'Operations', subheading: 'Convolution'}\n */\nfunction depthwiseConv2d_<T extends Tensor3D|Tensor4D>(\n    x: T|TensorLike, filter: Tensor4D|TensorLike,\n    strides: [number, number]|number,\n    pad: 'valid'|'same'|number|conv_util.ExplicitPadding,\n    dataFormat: 'NHWC'|'NCHW' = 'NHWC',\n    dilations: [number, number]|number = [1, 1],\n    dimRoundingMode?: 'floor'|'round'|'ceil'): T {\n  const $x = convertToTensor(x, 'x', 'depthwiseConv2d', 'float32');\n  const $filter =\n      convertToTensor(filter, 'filter', 'depthwiseConv2d', 'float32');\n\n  let x4D = $x as Tensor4D;\n  let reshapedTo4D = false;\n  if ($x.rank === 3) {\n    reshapedTo4D = true;\n    x4D = reshape($x, [1, $x.shape[0], $x.shape[1], $x.shape[2]]);\n  }\n  util.assert(\n      x4D.rank === 4,\n      () => `Error in depthwiseConv2d: input must be rank 4, but got ` +\n          `rank ${x4D.rank}.`);\n  util.assert(\n      $filter.rank === 4,\n      () => `Error in depthwiseConv2d: filter must be rank 4, but got rank ` +\n          `${$filter.rank}.`);\n  const inChannels = dataFormat === 'NHWC' ? x4D.shape[3] : x4D.shape[1];\n  util.assert(\n      inChannels === $filter.shape[2],\n      () => `Error in depthwiseConv2d: number of input channels ` +\n          `(${inChannels}) must match the inChannels dimension in ` +\n          `filter ${$filter.shape[2]}.`);\n  conv_util.checkPadOnDimRoundingMode('depthwiseConv2d', pad, dimRoundingMode);\n  const inputs: DepthwiseConv2dNativeInputs = {x: x4D, filter: $filter};\n  const attrs: DepthwiseConv2dNativeAttrs =\n      {strides, pad, dataFormat, dilations, dimRoundingMode};\n\n  // tslint:disable-next-line: no-unnecessary-type-assertion\n  const res = ENGINE.runKernel(\n                  DepthwiseConv2dNative, inputs as unknown as NamedTensorMap,\n                  attrs as unknown as NamedAttrMap) as T;\n\n  if (reshapedTo4D) {\n    return reshape(res, [res.shape[1], res.shape[2], res.shape[3]]) as T;\n  }\n  return res;\n}\n\nexport const depthwiseConv2d = /* @__PURE__ */ op({depthwiseConv2d_});\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAgBA,SAAQA,MAAM,QAAO,WAAW;AAChC,SAAQC,qBAAqB,QAAgE,iBAAiB;AAI9G,SAAQC,eAAe,QAAO,oBAAoB;AAElD,OAAO,KAAKC,IAAI,MAAM,SAAS;AAE/B,OAAO,KAAKC,SAAS,MAAM,aAAa;AACxC,SAAQC,EAAE,QAAO,aAAa;AAC9B,SAAQC,OAAO,QAAO,WAAW;AAEjC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6CA,SAASC,gBAAgBA,CACrBC,CAAe,EAAEC,MAA2B,EAC5CC,OAAgC,EAChCC,GAAoD,EACpDC,UAAA,GAA4B,MAAM,EAClCC,SAAA,GAAqC,CAAC,CAAC,EAAE,CAAC,CAAC,EAC3CC,eAAwC;EAC1C,MAAMC,EAAE,GAAGb,eAAe,CAACM,CAAC,EAAE,GAAG,EAAE,iBAAiB,EAAE,SAAS,CAAC;EAChE,MAAMQ,OAAO,GACTd,eAAe,CAACO,MAAM,EAAE,QAAQ,EAAE,iBAAiB,EAAE,SAAS,CAAC;EAEnE,IAAIQ,GAAG,GAAGF,EAAc;EACxB,IAAIG,YAAY,GAAG,KAAK;EACxB,IAAIH,EAAE,CAACI,IAAI,KAAK,CAAC,EAAE;IACjBD,YAAY,GAAG,IAAI;IACnBD,GAAG,GAAGX,OAAO,CAACS,EAAE,EAAE,CAAC,CAAC,EAAEA,EAAE,CAACK,KAAK,CAAC,CAAC,CAAC,EAAEL,EAAE,CAACK,KAAK,CAAC,CAAC,CAAC,EAAEL,EAAE,CAACK,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;;EAE/DjB,IAAI,CAACkB,MAAM,CACPJ,GAAG,CAACE,IAAI,KAAK,CAAC,EACd,MAAM,0DAA0D,GAC5D,QAAQF,GAAG,CAACE,IAAI,GAAG,CAAC;EAC5BhB,IAAI,CAACkB,MAAM,CACPL,OAAO,CAACG,IAAI,KAAK,CAAC,EAClB,MAAM,gEAAgE,GAClE,GAAGH,OAAO,CAACG,IAAI,GAAG,CAAC;EAC3B,MAAMG,UAAU,GAAGV,UAAU,KAAK,MAAM,GAAGK,GAAG,CAACG,KAAK,CAAC,CAAC,CAAC,GAAGH,GAAG,CAACG,KAAK,CAAC,CAAC,CAAC;EACtEjB,IAAI,CAACkB,MAAM,CACPC,UAAU,KAAKN,OAAO,CAACI,KAAK,CAAC,CAAC,CAAC,EAC/B,MAAM,qDAAqD,GACvD,IAAIE,UAAU,2CAA2C,GACzD,UAAUN,OAAO,CAACI,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC;EACtChB,SAAS,CAACmB,yBAAyB,CAAC,iBAAiB,EAAEZ,GAAG,EAAEG,eAAe,CAAC;EAC5E,MAAMU,MAAM,GAAgC;IAAChB,CAAC,EAAES,GAAG;IAAER,MAAM,EAAEO;EAAO,CAAC;EACrE,MAAMS,KAAK,GACP;IAACf,OAAO;IAAEC,GAAG;IAAEC,UAAU;IAAEC,SAAS;IAAEC;EAAe,CAAC;EAE1D;EACA,MAAMY,GAAG,GAAG1B,MAAM,CAAC2B,SAAS,CACZ1B,qBAAqB,EAAEuB,MAAmC,EAC1DC,KAAgC,CAAM;EAEtD,IAAIP,YAAY,EAAE;IAChB,OAAOZ,OAAO,CAACoB,GAAG,EAAE,CAACA,GAAG,CAACN,KAAK,CAAC,CAAC,CAAC,EAAEM,GAAG,CAACN,KAAK,CAAC,CAAC,CAAC,EAAEM,GAAG,CAACN,KAAK,CAAC,CAAC,CAAC,CAAC,CAAM;;EAEtE,OAAOM,GAAG;AACZ;AAEA,OAAO,MAAME,eAAe,GAAG,eAAgBvB,EAAE,CAAC;EAACE;AAAgB,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}