{"ast": null, "code": "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { matMul } from '../../ops/mat_mul';\nimport { getGlobalTensorClass } from '../../tensor';\ngetGlobalTensorClass().prototype.matMul = function (b, transposeA, transposeB) {\n  this.throwIfDisposed();\n  return matMul(this, b, transposeA, transposeB);\n};", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON>", "getGlobalTensorClass", "prototype", "b", "transposeA", "transposeB", "throwIfDisposed"], "sources": ["C:\\tfjs-core\\src\\public\\chained_ops\\mat_mul.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport {matMul} from '../../ops/mat_mul';\nimport {getGlobalTensorClass, Tensor} from '../../tensor';\nimport {Rank, TensorLike} from '../../types';\n\ndeclare module '../../tensor' {\n  interface Tensor<R extends Rank = Rank> {\n    matMul<T extends Tensor>(\n        b: Tensor|TensorLike, transposeA?: boolean,\n        transposeB?: boolean): Tensor;\n  }\n}\n\ngetGlobalTensorClass().prototype.matMul = function<T extends Tensor>(\n    this: T, b: Tensor|TensorLike, transposeA?: boolean,\n    transposeB?: boolean): Tensor {\n  this.throwIfDisposed();\n  return matMul(this, b, transposeA, transposeB);\n};\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAgBA,SAAQA,MAAM,QAAO,mBAAmB;AACxC,SAAQC,oBAAoB,QAAe,cAAc;AAWzDA,oBAAoB,EAAE,CAACC,SAAS,CAACF,MAAM,GAAG,UAC7BG,CAAoB,EAAEC,UAAoB,EACnDC,UAAoB;EACtB,IAAI,CAACC,eAAe,EAAE;EACtB,OAAON,MAAM,CAAC,IAAI,EAAEG,CAAC,EAAEC,UAAU,EAAEC,UAAU,CAAC;AAChD,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}