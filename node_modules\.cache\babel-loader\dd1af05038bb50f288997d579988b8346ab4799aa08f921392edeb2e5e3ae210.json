{"ast": null, "code": "// CSV Template Configuration\nexport const CSV_TEMPLATE = {\n  headers: ['Date', 'Description', 'Debit', 'Credit', 'Balance', 'Reference'],\n  validationRules: [{\n    field: 'Date',\n    rule: 'required',\n    message: 'Date is required'\n  }, {\n    field: 'Date',\n    rule: 'date',\n    message: 'Date must be in valid format (YYYY-MM-DD or MM/DD/YYYY)'\n  }, {\n    field: 'Description',\n    rule: 'required',\n    message: 'Description is required'\n  }, {\n    field: 'Debit',\n    rule: 'number',\n    message: 'Debit must be a valid number'\n  }, {\n    field: 'Credit',\n    rule: 'number',\n    message: 'Credit must be a valid number'\n  }, {\n    field: 'Balance',\n    rule: 'required',\n    message: 'Balance is required'\n  }, {\n    field: 'Balance',\n    rule: 'number',\n    message: 'Balance must be a valid number'\n  }, {\n    field: 'Balance',\n    rule: 'balance',\n    message: 'Balance calculation does not match'\n  }],\n  sampleData: [{\n    'Date': '2024-01-15',\n    'Description': 'Initial Deposit',\n    'Debit': '',\n    'Credit': '10000.00',\n    'Balance': '10000.00',\n    'Reference': 'DEP001'\n  }, {\n    'Date': '2024-01-16',\n    'Description': 'Office Supplies',\n    'Debit': '250.00',\n    'Credit': '',\n    'Balance': '9750.00',\n    'Reference': 'CHK001'\n  }, {\n    'Date': '2024-01-17',\n    'Description': 'Client Payment',\n    'Debit': '',\n    'Credit': '5000.00',\n    'Balance': '14750.00',\n    'Reference': 'TRF001'\n  }]\n};\nclass CSVProcessingService {\n  // Generate CSV template for download\n  generateTemplate() {\n    const headers = CSV_TEMPLATE.headers.join(',');\n    const rows = CSV_TEMPLATE.sampleData.map(row => CSV_TEMPLATE.headers.map(header => `\"${row[header] || ''}\"`).join(','));\n    return [headers, ...rows].join('\\n');\n  }\n\n  // Download CSV template\n  downloadTemplate() {\n    const csvContent = this.generateTemplate();\n    const blob = new Blob([csvContent], {\n      type: 'text/csv;charset=utf-8;'\n    });\n    const link = document.createElement('a');\n    if (link.download !== undefined) {\n      const url = URL.createObjectURL(blob);\n      link.setAttribute('href', url);\n      link.setAttribute('download', 'bank_statement_template.csv');\n      link.style.visibility = 'hidden';\n      document.body.appendChild(link);\n      link.click();\n      document.body.removeChild(link);\n    }\n  }\n\n  // Parse CSV content\n  async parseCSV(file) {\n    return new Promise((resolve, reject) => {\n      const reader = new FileReader();\n      reader.onload = event => {\n        try {\n          var _event$target;\n          const csv = (_event$target = event.target) === null || _event$target === void 0 ? void 0 : _event$target.result;\n          const rows = this.parseCSVContent(csv);\n          resolve(rows);\n        } catch (error) {\n          reject(error);\n        }\n      };\n      reader.onerror = () => reject(new Error('Failed to read file'));\n      reader.readAsText(file);\n    });\n  }\n  parseCSVContent(csv) {\n    const lines = csv.split('\\n').filter(line => line.trim());\n    if (lines.length < 2) throw new Error('CSV must contain at least a header and one data row');\n    const headers = this.parseCSVLine(lines[0]).map(h => h.trim());\n    const expectedHeaders = ['date', 'description', 'debit', 'credit', 'balance'];\n\n    // Validate headers (case-insensitive)\n    const normalizedHeaders = headers.map(h => h.toLowerCase());\n    const missingHeaders = expectedHeaders.filter(h => !normalizedHeaders.includes(h));\n    if (missingHeaders.length > 0) {\n      throw new Error(`Missing required headers: ${missingHeaders.join(', ')}`);\n    }\n\n    // Create header mapping\n    const headerMap = {};\n    expectedHeaders.forEach(expectedHeader => {\n      const index = normalizedHeaders.findIndex(h => h === expectedHeader);\n      headerMap[expectedHeader] = index;\n    });\n\n    // Parse data rows\n    const dataRows = [];\n    for (let i = 1; i < lines.length; i++) {\n      var _values$headerMap$dat, _values$headerMap$des, _values$headerMap$deb, _values$headerMap$cre, _values$headerMap$bal, _values$headerMap$ref;\n      const values = this.parseCSVLine(lines[i]);\n      if (values.length === 0 || values.every(v => !v.trim())) continue; // Skip empty rows\n\n      const row = {\n        date: ((_values$headerMap$dat = values[headerMap.date]) === null || _values$headerMap$dat === void 0 ? void 0 : _values$headerMap$dat.trim()) || '',\n        description: ((_values$headerMap$des = values[headerMap.description]) === null || _values$headerMap$des === void 0 ? void 0 : _values$headerMap$des.trim()) || '',\n        debit: ((_values$headerMap$deb = values[headerMap.debit]) === null || _values$headerMap$deb === void 0 ? void 0 : _values$headerMap$deb.trim()) || '',\n        credit: ((_values$headerMap$cre = values[headerMap.credit]) === null || _values$headerMap$cre === void 0 ? void 0 : _values$headerMap$cre.trim()) || '',\n        balance: ((_values$headerMap$bal = values[headerMap.balance]) === null || _values$headerMap$bal === void 0 ? void 0 : _values$headerMap$bal.trim()) || '',\n        reference: ((_values$headerMap$ref = values[headerMap.reference]) === null || _values$headerMap$ref === void 0 ? void 0 : _values$headerMap$ref.trim()) || ''\n      };\n      dataRows.push(row);\n    }\n    return dataRows;\n  }\n  parseCSVLine(line) {\n    const result = [];\n    let current = '';\n    let inQuotes = false;\n    for (let i = 0; i < line.length; i++) {\n      const char = line[i];\n      if (char === '\"') {\n        if (inQuotes && line[i + 1] === '\"') {\n          current += '\"';\n          i++; // Skip next quote\n        } else {\n          inQuotes = !inQuotes;\n        }\n      } else if (char === ',' && !inQuotes) {\n        result.push(current);\n        current = '';\n      } else {\n        current += char;\n      }\n    }\n    result.push(current);\n    return result;\n  }\n\n  // Validate CSV data\n  validateCSVData(rows) {\n    const errors = [];\n    rows.forEach((row, index) => {\n      const rowNumber = index + 2; // +2 because index is 0-based and we skip header\n\n      // Validate each field\n      CSV_TEMPLATE.validationRules.forEach(rule => {\n        const fieldValue = this.getFieldValue(row, rule.field);\n        const error = this.validateField(fieldValue, rule, rowNumber);\n        if (error) errors.push(error);\n      });\n\n      // Special validation for balance calculation\n      const debitValue = this.parseAmount(row.debit);\n      const creditValue = this.parseAmount(row.credit);\n      const balanceValue = this.parseAmount(row.balance);\n      if (index > 0) {\n        const previousBalance = this.parseAmount(rows[index - 1].balance);\n        const expectedBalance = previousBalance - debitValue + creditValue;\n        if (Math.abs(balanceValue - expectedBalance) > 0.01) {\n          errors.push({\n            row: rowNumber,\n            field: 'Balance',\n            message: `Balance calculation error. Expected: ${expectedBalance.toFixed(2)}, Got: ${balanceValue.toFixed(2)}`,\n            value: row.balance\n          });\n        }\n      }\n    });\n    return errors;\n  }\n  getFieldValue(row, field) {\n    switch (field.toLowerCase()) {\n      case 'date':\n        return row.date;\n      case 'description':\n        return row.description;\n      case 'debit':\n        return row.debit;\n      case 'credit':\n        return row.credit;\n      case 'balance':\n        return row.balance;\n      case 'reference':\n        return row.reference || '';\n      default:\n        return '';\n    }\n  }\n  validateField(value, rule, rowNumber) {\n    switch (rule.rule) {\n      case 'required':\n        if (!value || value.trim() === '') {\n          return {\n            row: rowNumber,\n            field: rule.field,\n            message: rule.message,\n            value\n          };\n        }\n        break;\n      case 'number':\n        if (value.trim() && isNaN(this.parseAmount(value))) {\n          return {\n            row: rowNumber,\n            field: rule.field,\n            message: rule.message,\n            value\n          };\n        }\n        break;\n      case 'date':\n        if (value.trim() && !this.isValidDate(value)) {\n          return {\n            row: rowNumber,\n            field: rule.field,\n            message: rule.message,\n            value\n          };\n        }\n        break;\n      case 'positive':\n        if (value.trim() && this.parseAmount(value) < 0) {\n          return {\n            row: rowNumber,\n            field: rule.field,\n            message: rule.message,\n            value\n          };\n        }\n        break;\n    }\n    return null;\n  }\n  parseAmount(value) {\n    if (!value || value.trim() === '') return 0;\n\n    // Remove currency symbols and commas, handle negative values\n    const cleaned = value.replace(/[$,\\s]/g, '');\n    const amount = parseFloat(cleaned);\n    return isNaN(amount) ? 0 : Math.abs(amount); // Always return positive for calculations\n  }\n  isValidDate(dateString) {\n    const date = new Date(dateString);\n    return date instanceof Date && !isNaN(date.getTime());\n  }\n\n  // Convert CSV rows to transactions\n  convertToTransactions(rows) {\n    return rows.map((row, index) => ({\n      id: `temp_${Date.now()}_${index}`,\n      date: this.formatDate(row.date),\n      description: row.description,\n      debitAmount: this.parseAmount(row.debit),\n      creditAmount: this.parseAmount(row.credit),\n      balance: this.parseAmount(row.balance),\n      reference: row.reference\n    }));\n  }\n  formatDate(dateString) {\n    const date = new Date(dateString);\n    return date.toISOString().split('T')[0];\n  }\n\n  // Process file and generate import summary\n  async processFile(file) {\n    try {\n      const rows = await this.parseCSV(file);\n      const validationErrors = this.validateCSVData(rows);\n      const transactions = this.convertToTransactions(rows);\n      const totalDebitAmount = transactions.reduce((sum, t) => sum + t.debitAmount, 0);\n      const totalCreditAmount = transactions.reduce((sum, t) => sum + t.creditAmount, 0);\n      const closingBalance = transactions.length > 0 ? transactions[transactions.length - 1].balance : 0;\n      return {\n        fileName: file.name,\n        totalTransactions: transactions.length,\n        totalDebitAmount,\n        totalCreditAmount,\n        closingBalance,\n        validationErrors,\n        transactions\n      };\n    } catch (error) {\n      throw new Error(`Failed to process ${file.name}: ${error instanceof Error ? error.message : 'Unknown error'}`);\n    }\n  }\n}\nexport const csvProcessingService = new CSVProcessingService();", "map": {"version": 3, "names": ["CSV_TEMPLATE", "headers", "validationRules", "field", "rule", "message", "sampleData", "CSVProcessingService", "generateTemplate", "join", "rows", "map", "row", "header", "downloadTemplate", "csv<PERSON><PERSON>nt", "blob", "Blob", "type", "link", "document", "createElement", "download", "undefined", "url", "URL", "createObjectURL", "setAttribute", "style", "visibility", "body", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "parseCSV", "file", "Promise", "resolve", "reject", "reader", "FileReader", "onload", "event", "_event$target", "csv", "target", "result", "parseCS<PERSON><PERSON>nt", "error", "onerror", "Error", "readAsText", "lines", "split", "filter", "line", "trim", "length", "parseCSVLine", "h", "expectedHeaders", "normalizedHeaders", "toLowerCase", "missingHeaders", "includes", "headerMap", "for<PERSON>ach", "expected<PERSON>eader", "index", "findIndex", "dataRows", "i", "_values$headerMap$dat", "_values$headerMap$des", "_values$headerMap$deb", "_values$headerMap$cre", "_values$headerMap$bal", "_values$headerMap$ref", "values", "every", "v", "date", "description", "debit", "credit", "balance", "reference", "push", "current", "inQuotes", "char", "validateCSVData", "errors", "rowNumber", "fieldValue", "getFieldValue", "validateField", "debitValue", "parseAmount", "creditValue", "balanceValue", "previousBalance", "expectedBalance", "Math", "abs", "toFixed", "value", "isNaN", "isValidDate", "cleaned", "replace", "amount", "parseFloat", "dateString", "Date", "getTime", "convertToTransactions", "id", "now", "formatDate", "debitAmount", "creditAmount", "toISOString", "processFile", "validationErrors", "transactions", "totalDebitAmount", "reduce", "sum", "t", "totalCreditAmount", "closingBalance", "fileName", "name", "totalTransactions", "csvProcessingService"], "sources": ["C:/tmsft/src/services/csvProcessingService.ts"], "sourcesContent": ["import { CSVRow, Transaction, ImportSummary, ValidationError, ValidationRule, CSVTemplate } from '../types';\r\n\r\n// CSV Template Configuration\r\nexport const CSV_TEMPLATE: CSVTemplate = {\r\n  headers: ['Date', 'Description', 'Debit', 'Credit', 'Balance', 'Reference'],\r\n  validationRules: [\r\n    { field: 'Date', rule: 'required', message: 'Date is required' },\r\n    { field: 'Date', rule: 'date', message: 'Date must be in valid format (YYYY-MM-DD or MM/DD/YYYY)' },\r\n    { field: 'Description', rule: 'required', message: 'Description is required' },\r\n    { field: 'Debit', rule: 'number', message: 'Debit must be a valid number' },\r\n    { field: 'Credit', rule: 'number', message: 'Credit must be a valid number' },\r\n    { field: 'Balance', rule: 'required', message: 'Balance is required' },\r\n    { field: 'Balance', rule: 'number', message: 'Balance must be a valid number' },\r\n    { field: 'Balance', rule: 'balance', message: 'Balance calculation does not match' }\r\n  ],\r\n  sampleData: [\r\n    {\r\n      'Date': '2024-01-15',\r\n      'Description': 'Initial Deposit',\r\n      'Debit': '',\r\n      'Credit': '10000.00',\r\n      'Balance': '10000.00',\r\n      'Reference': 'DEP001'\r\n    },\r\n    {\r\n      'Date': '2024-01-16',\r\n      'Description': 'Office Supplies',\r\n      'Debit': '250.00',\r\n      'Credit': '',\r\n      'Balance': '9750.00',\r\n      'Reference': 'CHK001'\r\n    },\r\n    {\r\n      'Date': '2024-01-17',\r\n      'Description': 'Client Payment',\r\n      'Debit': '',\r\n      'Credit': '5000.00',\r\n      'Balance': '14750.00',\r\n      'Reference': 'TRF001'\r\n    }\r\n  ]\r\n};\r\n\r\nclass CSVProcessingService {\r\n  \r\n  // Generate CSV template for download\r\n  generateTemplate(): string {\r\n    const headers = CSV_TEMPLATE.headers.join(',');\r\n    const rows = CSV_TEMPLATE.sampleData.map(row => \r\n      CSV_TEMPLATE.headers.map(header => `\"${row[header] || ''}\"`).join(',')\r\n    );\r\n    \r\n    return [headers, ...rows].join('\\n');\r\n  }\r\n\r\n  // Download CSV template\r\n  downloadTemplate(): void {\r\n    const csvContent = this.generateTemplate();\r\n    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });\r\n    const link = document.createElement('a');\r\n    \r\n    if (link.download !== undefined) {\r\n      const url = URL.createObjectURL(blob);\r\n      link.setAttribute('href', url);\r\n      link.setAttribute('download', 'bank_statement_template.csv');\r\n      link.style.visibility = 'hidden';\r\n      document.body.appendChild(link);\r\n      link.click();\r\n      document.body.removeChild(link);\r\n    }\r\n  }\r\n\r\n  // Parse CSV content\r\n  async parseCSV(file: File): Promise<CSVRow[]> {\r\n    return new Promise((resolve, reject) => {\r\n      const reader = new FileReader();\r\n      \r\n      reader.onload = (event) => {\r\n        try {\r\n          const csv = event.target?.result as string;\r\n          const rows = this.parseCSVContent(csv);\r\n          resolve(rows);\r\n        } catch (error) {\r\n          reject(error);\r\n        }\r\n      };\r\n      \r\n      reader.onerror = () => reject(new Error('Failed to read file'));\r\n      reader.readAsText(file);\r\n    });\r\n  }\r\n\r\n  private parseCSVContent(csv: string): CSVRow[] {\r\n    const lines = csv.split('\\n').filter(line => line.trim());\r\n    if (lines.length < 2) throw new Error('CSV must contain at least a header and one data row');\r\n    \r\n    const headers = this.parseCSVLine(lines[0]).map(h => h.trim());\r\n    const expectedHeaders = ['date', 'description', 'debit', 'credit', 'balance'];\r\n    \r\n    // Validate headers (case-insensitive)\r\n    const normalizedHeaders = headers.map(h => h.toLowerCase());\r\n    const missingHeaders = expectedHeaders.filter(h => !normalizedHeaders.includes(h));\r\n    \r\n    if (missingHeaders.length > 0) {\r\n      throw new Error(`Missing required headers: ${missingHeaders.join(', ')}`);\r\n    }\r\n\r\n    // Create header mapping\r\n    const headerMap: Record<string, number> = {};\r\n    expectedHeaders.forEach(expectedHeader => {\r\n      const index = normalizedHeaders.findIndex(h => h === expectedHeader);\r\n      headerMap[expectedHeader] = index;\r\n    });\r\n\r\n    // Parse data rows\r\n    const dataRows: CSVRow[] = [];\r\n    for (let i = 1; i < lines.length; i++) {\r\n      const values = this.parseCSVLine(lines[i]);\r\n      if (values.length === 0 || values.every(v => !v.trim())) continue; // Skip empty rows\r\n      \r\n      const row: CSVRow = {\r\n        date: values[headerMap.date]?.trim() || '',\r\n        description: values[headerMap.description]?.trim() || '',\r\n        debit: values[headerMap.debit]?.trim() || '',\r\n        credit: values[headerMap.credit]?.trim() || '',\r\n        balance: values[headerMap.balance]?.trim() || '',\r\n        reference: values[headerMap.reference]?.trim() || ''\r\n      };\r\n      \r\n      dataRows.push(row);\r\n    }\r\n    \r\n    return dataRows;\r\n  }\r\n\r\n  private parseCSVLine(line: string): string[] {\r\n    const result: string[] = [];\r\n    let current = '';\r\n    let inQuotes = false;\r\n    \r\n    for (let i = 0; i < line.length; i++) {\r\n      const char = line[i];\r\n      \r\n      if (char === '\"') {\r\n        if (inQuotes && line[i + 1] === '\"') {\r\n          current += '\"';\r\n          i++; // Skip next quote\r\n        } else {\r\n          inQuotes = !inQuotes;\r\n        }\r\n      } else if (char === ',' && !inQuotes) {\r\n        result.push(current);\r\n        current = '';\r\n      } else {\r\n        current += char;\r\n      }\r\n    }\r\n    \r\n    result.push(current);\r\n    return result;\r\n  }\r\n\r\n  // Validate CSV data\r\n  validateCSVData(rows: CSVRow[]): ValidationError[] {\r\n    const errors: ValidationError[] = [];\r\n    \r\n    rows.forEach((row, index) => {\r\n      const rowNumber = index + 2; // +2 because index is 0-based and we skip header\r\n      \r\n      // Validate each field\r\n      CSV_TEMPLATE.validationRules.forEach(rule => {\r\n        const fieldValue = this.getFieldValue(row, rule.field);\r\n        const error = this.validateField(fieldValue, rule, rowNumber);\r\n        if (error) errors.push(error);\r\n      });\r\n      \r\n      // Special validation for balance calculation\r\n      const debitValue = this.parseAmount(row.debit);\r\n      const creditValue = this.parseAmount(row.credit);\r\n      const balanceValue = this.parseAmount(row.balance);\r\n      \r\n      if (index > 0) {\r\n        const previousBalance = this.parseAmount(rows[index - 1].balance);\r\n        const expectedBalance = previousBalance - debitValue + creditValue;\r\n        \r\n        if (Math.abs(balanceValue - expectedBalance) > 0.01) {\r\n          errors.push({\r\n            row: rowNumber,\r\n            field: 'Balance',\r\n            message: `Balance calculation error. Expected: ${expectedBalance.toFixed(2)}, Got: ${balanceValue.toFixed(2)}`,\r\n            value: row.balance\r\n          });\r\n        }\r\n      }\r\n    });\r\n    \r\n    return errors;\r\n  }\r\n\r\n  private getFieldValue(row: CSVRow, field: string): string {\r\n    switch (field.toLowerCase()) {\r\n      case 'date': return row.date;\r\n      case 'description': return row.description;\r\n      case 'debit': return row.debit;\r\n      case 'credit': return row.credit;\r\n      case 'balance': return row.balance;\r\n      case 'reference': return row.reference || '';\r\n      default: return '';\r\n    }\r\n  }\r\n\r\n  private validateField(value: string, rule: ValidationRule, rowNumber: number): ValidationError | null {\r\n    switch (rule.rule) {\r\n      case 'required':\r\n        if (!value || value.trim() === '') {\r\n          return { row: rowNumber, field: rule.field, message: rule.message, value };\r\n        }\r\n        break;\r\n        \r\n      case 'number':\r\n        if (value.trim() && isNaN(this.parseAmount(value))) {\r\n          return { row: rowNumber, field: rule.field, message: rule.message, value };\r\n        }\r\n        break;\r\n        \r\n      case 'date':\r\n        if (value.trim() && !this.isValidDate(value)) {\r\n          return { row: rowNumber, field: rule.field, message: rule.message, value };\r\n        }\r\n        break;\r\n        \r\n      case 'positive':\r\n        if (value.trim() && this.parseAmount(value) < 0) {\r\n          return { row: rowNumber, field: rule.field, message: rule.message, value };\r\n        }\r\n        break;\r\n    }\r\n    \r\n    return null;\r\n  }\r\n\r\n  private parseAmount(value: string): number {\r\n    if (!value || value.trim() === '') return 0;\r\n    \r\n    // Remove currency symbols and commas, handle negative values\r\n    const cleaned = value.replace(/[$,\\s]/g, '');\r\n    const amount = parseFloat(cleaned);\r\n    \r\n    return isNaN(amount) ? 0 : Math.abs(amount); // Always return positive for calculations\r\n  }\r\n\r\n  private isValidDate(dateString: string): boolean {\r\n    const date = new Date(dateString);\r\n    return date instanceof Date && !isNaN(date.getTime());\r\n  }\r\n\r\n  // Convert CSV rows to transactions\r\n  convertToTransactions(rows: CSVRow[]): Transaction[] {\r\n    return rows.map((row, index) => ({\r\n      id: `temp_${Date.now()}_${index}`,\r\n      date: this.formatDate(row.date),\r\n      description: row.description,\r\n      debitAmount: this.parseAmount(row.debit),\r\n      creditAmount: this.parseAmount(row.credit),\r\n      balance: this.parseAmount(row.balance),\r\n      reference: row.reference\r\n    }));\r\n  }\r\n\r\n  private formatDate(dateString: string): string {\r\n    const date = new Date(dateString);\r\n    return date.toISOString().split('T')[0];\r\n  }\r\n\r\n  // Process file and generate import summary\r\n  async processFile(file: File): Promise<ImportSummary> {\r\n    try {\r\n      const rows = await this.parseCSV(file);\r\n      const validationErrors = this.validateCSVData(rows);\r\n      const transactions = this.convertToTransactions(rows);\r\n      \r\n      const totalDebitAmount = transactions.reduce((sum, t) => sum + t.debitAmount, 0);\r\n      const totalCreditAmount = transactions.reduce((sum, t) => sum + t.creditAmount, 0);\r\n      const closingBalance = transactions.length > 0 ? transactions[transactions.length - 1].balance : 0;\r\n      \r\n      return {\r\n        fileName: file.name,\r\n        totalTransactions: transactions.length,\r\n        totalDebitAmount,\r\n        totalCreditAmount,\r\n        closingBalance,\r\n        validationErrors,\r\n        transactions\r\n      };\r\n    } catch (error) {\r\n      throw new Error(`Failed to process ${file.name}: ${error instanceof Error ? error.message : 'Unknown error'}`);\r\n    }\r\n  }\r\n}\r\n\r\nexport const csvProcessingService = new CSVProcessingService(); "], "mappings": "AAEA;AACA,OAAO,MAAMA,YAAyB,GAAG;EACvCC,OAAO,EAAE,CAAC,MAAM,EAAE,aAAa,EAAE,OAAO,EAAE,QAAQ,EAAE,SAAS,EAAE,WAAW,CAAC;EAC3EC,eAAe,EAAE,CACf;IAAEC,KAAK,EAAE,MAAM;IAAEC,IAAI,EAAE,UAAU;IAAEC,OAAO,EAAE;EAAmB,CAAC,EAChE;IAAEF,KAAK,EAAE,MAAM;IAAEC,IAAI,EAAE,MAAM;IAAEC,OAAO,EAAE;EAA0D,CAAC,EACnG;IAAEF,KAAK,EAAE,aAAa;IAAEC,IAAI,EAAE,UAAU;IAAEC,OAAO,EAAE;EAA0B,CAAC,EAC9E;IAAEF,KAAK,EAAE,OAAO;IAAEC,IAAI,EAAE,QAAQ;IAAEC,OAAO,EAAE;EAA+B,CAAC,EAC3E;IAAEF,KAAK,EAAE,QAAQ;IAAEC,IAAI,EAAE,QAAQ;IAAEC,OAAO,EAAE;EAAgC,CAAC,EAC7E;IAAEF,KAAK,EAAE,SAAS;IAAEC,IAAI,EAAE,UAAU;IAAEC,OAAO,EAAE;EAAsB,CAAC,EACtE;IAAEF,KAAK,EAAE,SAAS;IAAEC,IAAI,EAAE,QAAQ;IAAEC,OAAO,EAAE;EAAiC,CAAC,EAC/E;IAAEF,KAAK,EAAE,SAAS;IAAEC,IAAI,EAAE,SAAS;IAAEC,OAAO,EAAE;EAAqC,CAAC,CACrF;EACDC,UAAU,EAAE,CACV;IACE,MAAM,EAAE,YAAY;IACpB,aAAa,EAAE,iBAAiB;IAChC,OAAO,EAAE,EAAE;IACX,QAAQ,EAAE,UAAU;IACpB,SAAS,EAAE,UAAU;IACrB,WAAW,EAAE;EACf,CAAC,EACD;IACE,MAAM,EAAE,YAAY;IACpB,aAAa,EAAE,iBAAiB;IAChC,OAAO,EAAE,QAAQ;IACjB,QAAQ,EAAE,EAAE;IACZ,SAAS,EAAE,SAAS;IACpB,WAAW,EAAE;EACf,CAAC,EACD;IACE,MAAM,EAAE,YAAY;IACpB,aAAa,EAAE,gBAAgB;IAC/B,OAAO,EAAE,EAAE;IACX,QAAQ,EAAE,SAAS;IACnB,SAAS,EAAE,UAAU;IACrB,WAAW,EAAE;EACf,CAAC;AAEL,CAAC;AAED,MAAMC,oBAAoB,CAAC;EAEzB;EACAC,gBAAgBA,CAAA,EAAW;IACzB,MAAMP,OAAO,GAAGD,YAAY,CAACC,OAAO,CAACQ,IAAI,CAAC,GAAG,CAAC;IAC9C,MAAMC,IAAI,GAAGV,YAAY,CAACM,UAAU,CAACK,GAAG,CAACC,GAAG,IAC1CZ,YAAY,CAACC,OAAO,CAACU,GAAG,CAACE,MAAM,IAAI,IAAID,GAAG,CAACC,MAAM,CAAC,IAAI,EAAE,GAAG,CAAC,CAACJ,IAAI,CAAC,GAAG,CACvE,CAAC;IAED,OAAO,CAACR,OAAO,EAAE,GAAGS,IAAI,CAAC,CAACD,IAAI,CAAC,IAAI,CAAC;EACtC;;EAEA;EACAK,gBAAgBA,CAAA,EAAS;IACvB,MAAMC,UAAU,GAAG,IAAI,CAACP,gBAAgB,CAAC,CAAC;IAC1C,MAAMQ,IAAI,GAAG,IAAIC,IAAI,CAAC,CAACF,UAAU,CAAC,EAAE;MAAEG,IAAI,EAAE;IAA0B,CAAC,CAAC;IACxE,MAAMC,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;IAExC,IAAIF,IAAI,CAACG,QAAQ,KAAKC,SAAS,EAAE;MAC/B,MAAMC,GAAG,GAAGC,GAAG,CAACC,eAAe,CAACV,IAAI,CAAC;MACrCG,IAAI,CAACQ,YAAY,CAAC,MAAM,EAAEH,GAAG,CAAC;MAC9BL,IAAI,CAACQ,YAAY,CAAC,UAAU,EAAE,6BAA6B,CAAC;MAC5DR,IAAI,CAACS,KAAK,CAACC,UAAU,GAAG,QAAQ;MAChCT,QAAQ,CAACU,IAAI,CAACC,WAAW,CAACZ,IAAI,CAAC;MAC/BA,IAAI,CAACa,KAAK,CAAC,CAAC;MACZZ,QAAQ,CAACU,IAAI,CAACG,WAAW,CAACd,IAAI,CAAC;IACjC;EACF;;EAEA;EACA,MAAMe,QAAQA,CAACC,IAAU,EAAqB;IAC5C,OAAO,IAAIC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;MACtC,MAAMC,MAAM,GAAG,IAAIC,UAAU,CAAC,CAAC;MAE/BD,MAAM,CAACE,MAAM,GAAIC,KAAK,IAAK;QACzB,IAAI;UAAA,IAAAC,aAAA;UACF,MAAMC,GAAG,IAAAD,aAAA,GAAGD,KAAK,CAACG,MAAM,cAAAF,aAAA,uBAAZA,aAAA,CAAcG,MAAgB;UAC1C,MAAMpC,IAAI,GAAG,IAAI,CAACqC,eAAe,CAACH,GAAG,CAAC;UACtCP,OAAO,CAAC3B,IAAI,CAAC;QACf,CAAC,CAAC,OAAOsC,KAAK,EAAE;UACdV,MAAM,CAACU,KAAK,CAAC;QACf;MACF,CAAC;MAEDT,MAAM,CAACU,OAAO,GAAG,MAAMX,MAAM,CAAC,IAAIY,KAAK,CAAC,qBAAqB,CAAC,CAAC;MAC/DX,MAAM,CAACY,UAAU,CAAChB,IAAI,CAAC;IACzB,CAAC,CAAC;EACJ;EAEQY,eAAeA,CAACH,GAAW,EAAY;IAC7C,MAAMQ,KAAK,GAAGR,GAAG,CAACS,KAAK,CAAC,IAAI,CAAC,CAACC,MAAM,CAACC,IAAI,IAAIA,IAAI,CAACC,IAAI,CAAC,CAAC,CAAC;IACzD,IAAIJ,KAAK,CAACK,MAAM,GAAG,CAAC,EAAE,MAAM,IAAIP,KAAK,CAAC,qDAAqD,CAAC;IAE5F,MAAMjD,OAAO,GAAG,IAAI,CAACyD,YAAY,CAACN,KAAK,CAAC,CAAC,CAAC,CAAC,CAACzC,GAAG,CAACgD,CAAC,IAAIA,CAAC,CAACH,IAAI,CAAC,CAAC,CAAC;IAC9D,MAAMI,eAAe,GAAG,CAAC,MAAM,EAAE,aAAa,EAAE,OAAO,EAAE,QAAQ,EAAE,SAAS,CAAC;;IAE7E;IACA,MAAMC,iBAAiB,GAAG5D,OAAO,CAACU,GAAG,CAACgD,CAAC,IAAIA,CAAC,CAACG,WAAW,CAAC,CAAC,CAAC;IAC3D,MAAMC,cAAc,GAAGH,eAAe,CAACN,MAAM,CAACK,CAAC,IAAI,CAACE,iBAAiB,CAACG,QAAQ,CAACL,CAAC,CAAC,CAAC;IAElF,IAAII,cAAc,CAACN,MAAM,GAAG,CAAC,EAAE;MAC7B,MAAM,IAAIP,KAAK,CAAC,6BAA6Ba,cAAc,CAACtD,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;IAC3E;;IAEA;IACA,MAAMwD,SAAiC,GAAG,CAAC,CAAC;IAC5CL,eAAe,CAACM,OAAO,CAACC,cAAc,IAAI;MACxC,MAAMC,KAAK,GAAGP,iBAAiB,CAACQ,SAAS,CAACV,CAAC,IAAIA,CAAC,KAAKQ,cAAc,CAAC;MACpEF,SAAS,CAACE,cAAc,CAAC,GAAGC,KAAK;IACnC,CAAC,CAAC;;IAEF;IACA,MAAME,QAAkB,GAAG,EAAE;IAC7B,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGnB,KAAK,CAACK,MAAM,EAAEc,CAAC,EAAE,EAAE;MAAA,IAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA;MACrC,MAAMC,MAAM,GAAG,IAAI,CAACpB,YAAY,CAACN,KAAK,CAACmB,CAAC,CAAC,CAAC;MAC1C,IAAIO,MAAM,CAACrB,MAAM,KAAK,CAAC,IAAIqB,MAAM,CAACC,KAAK,CAACC,CAAC,IAAI,CAACA,CAAC,CAACxB,IAAI,CAAC,CAAC,CAAC,EAAE,SAAS,CAAC;;MAEnE,MAAM5C,GAAW,GAAG;QAClBqE,IAAI,EAAE,EAAAT,qBAAA,GAAAM,MAAM,CAACb,SAAS,CAACgB,IAAI,CAAC,cAAAT,qBAAA,uBAAtBA,qBAAA,CAAwBhB,IAAI,CAAC,CAAC,KAAI,EAAE;QAC1C0B,WAAW,EAAE,EAAAT,qBAAA,GAAAK,MAAM,CAACb,SAAS,CAACiB,WAAW,CAAC,cAAAT,qBAAA,uBAA7BA,qBAAA,CAA+BjB,IAAI,CAAC,CAAC,KAAI,EAAE;QACxD2B,KAAK,EAAE,EAAAT,qBAAA,GAAAI,MAAM,CAACb,SAAS,CAACkB,KAAK,CAAC,cAAAT,qBAAA,uBAAvBA,qBAAA,CAAyBlB,IAAI,CAAC,CAAC,KAAI,EAAE;QAC5C4B,MAAM,EAAE,EAAAT,qBAAA,GAAAG,MAAM,CAACb,SAAS,CAACmB,MAAM,CAAC,cAAAT,qBAAA,uBAAxBA,qBAAA,CAA0BnB,IAAI,CAAC,CAAC,KAAI,EAAE;QAC9C6B,OAAO,EAAE,EAAAT,qBAAA,GAAAE,MAAM,CAACb,SAAS,CAACoB,OAAO,CAAC,cAAAT,qBAAA,uBAAzBA,qBAAA,CAA2BpB,IAAI,CAAC,CAAC,KAAI,EAAE;QAChD8B,SAAS,EAAE,EAAAT,qBAAA,GAAAC,MAAM,CAACb,SAAS,CAACqB,SAAS,CAAC,cAAAT,qBAAA,uBAA3BA,qBAAA,CAA6BrB,IAAI,CAAC,CAAC,KAAI;MACpD,CAAC;MAEDc,QAAQ,CAACiB,IAAI,CAAC3E,GAAG,CAAC;IACpB;IAEA,OAAO0D,QAAQ;EACjB;EAEQZ,YAAYA,CAACH,IAAY,EAAY;IAC3C,MAAMT,MAAgB,GAAG,EAAE;IAC3B,IAAI0C,OAAO,GAAG,EAAE;IAChB,IAAIC,QAAQ,GAAG,KAAK;IAEpB,KAAK,IAAIlB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGhB,IAAI,CAACE,MAAM,EAAEc,CAAC,EAAE,EAAE;MACpC,MAAMmB,IAAI,GAAGnC,IAAI,CAACgB,CAAC,CAAC;MAEpB,IAAImB,IAAI,KAAK,GAAG,EAAE;QAChB,IAAID,QAAQ,IAAIlC,IAAI,CAACgB,CAAC,GAAG,CAAC,CAAC,KAAK,GAAG,EAAE;UACnCiB,OAAO,IAAI,GAAG;UACdjB,CAAC,EAAE,CAAC,CAAC;QACP,CAAC,MAAM;UACLkB,QAAQ,GAAG,CAACA,QAAQ;QACtB;MACF,CAAC,MAAM,IAAIC,IAAI,KAAK,GAAG,IAAI,CAACD,QAAQ,EAAE;QACpC3C,MAAM,CAACyC,IAAI,CAACC,OAAO,CAAC;QACpBA,OAAO,GAAG,EAAE;MACd,CAAC,MAAM;QACLA,OAAO,IAAIE,IAAI;MACjB;IACF;IAEA5C,MAAM,CAACyC,IAAI,CAACC,OAAO,CAAC;IACpB,OAAO1C,MAAM;EACf;;EAEA;EACA6C,eAAeA,CAACjF,IAAc,EAAqB;IACjD,MAAMkF,MAAyB,GAAG,EAAE;IAEpClF,IAAI,CAACwD,OAAO,CAAC,CAACtD,GAAG,EAAEwD,KAAK,KAAK;MAC3B,MAAMyB,SAAS,GAAGzB,KAAK,GAAG,CAAC,CAAC,CAAC;;MAE7B;MACApE,YAAY,CAACE,eAAe,CAACgE,OAAO,CAAC9D,IAAI,IAAI;QAC3C,MAAM0F,UAAU,GAAG,IAAI,CAACC,aAAa,CAACnF,GAAG,EAAER,IAAI,CAACD,KAAK,CAAC;QACtD,MAAM6C,KAAK,GAAG,IAAI,CAACgD,aAAa,CAACF,UAAU,EAAE1F,IAAI,EAAEyF,SAAS,CAAC;QAC7D,IAAI7C,KAAK,EAAE4C,MAAM,CAACL,IAAI,CAACvC,KAAK,CAAC;MAC/B,CAAC,CAAC;;MAEF;MACA,MAAMiD,UAAU,GAAG,IAAI,CAACC,WAAW,CAACtF,GAAG,CAACuE,KAAK,CAAC;MAC9C,MAAMgB,WAAW,GAAG,IAAI,CAACD,WAAW,CAACtF,GAAG,CAACwE,MAAM,CAAC;MAChD,MAAMgB,YAAY,GAAG,IAAI,CAACF,WAAW,CAACtF,GAAG,CAACyE,OAAO,CAAC;MAElD,IAAIjB,KAAK,GAAG,CAAC,EAAE;QACb,MAAMiC,eAAe,GAAG,IAAI,CAACH,WAAW,CAACxF,IAAI,CAAC0D,KAAK,GAAG,CAAC,CAAC,CAACiB,OAAO,CAAC;QACjE,MAAMiB,eAAe,GAAGD,eAAe,GAAGJ,UAAU,GAAGE,WAAW;QAElE,IAAII,IAAI,CAACC,GAAG,CAACJ,YAAY,GAAGE,eAAe,CAAC,GAAG,IAAI,EAAE;UACnDV,MAAM,CAACL,IAAI,CAAC;YACV3E,GAAG,EAAEiF,SAAS;YACd1F,KAAK,EAAE,SAAS;YAChBE,OAAO,EAAE,wCAAwCiG,eAAe,CAACG,OAAO,CAAC,CAAC,CAAC,UAAUL,YAAY,CAACK,OAAO,CAAC,CAAC,CAAC,EAAE;YAC9GC,KAAK,EAAE9F,GAAG,CAACyE;UACb,CAAC,CAAC;QACJ;MACF;IACF,CAAC,CAAC;IAEF,OAAOO,MAAM;EACf;EAEQG,aAAaA,CAACnF,GAAW,EAAET,KAAa,EAAU;IACxD,QAAQA,KAAK,CAAC2D,WAAW,CAAC,CAAC;MACzB,KAAK,MAAM;QAAE,OAAOlD,GAAG,CAACqE,IAAI;MAC5B,KAAK,aAAa;QAAE,OAAOrE,GAAG,CAACsE,WAAW;MAC1C,KAAK,OAAO;QAAE,OAAOtE,GAAG,CAACuE,KAAK;MAC9B,KAAK,QAAQ;QAAE,OAAOvE,GAAG,CAACwE,MAAM;MAChC,KAAK,SAAS;QAAE,OAAOxE,GAAG,CAACyE,OAAO;MAClC,KAAK,WAAW;QAAE,OAAOzE,GAAG,CAAC0E,SAAS,IAAI,EAAE;MAC5C;QAAS,OAAO,EAAE;IACpB;EACF;EAEQU,aAAaA,CAACU,KAAa,EAAEtG,IAAoB,EAAEyF,SAAiB,EAA0B;IACpG,QAAQzF,IAAI,CAACA,IAAI;MACf,KAAK,UAAU;QACb,IAAI,CAACsG,KAAK,IAAIA,KAAK,CAAClD,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;UACjC,OAAO;YAAE5C,GAAG,EAAEiF,SAAS;YAAE1F,KAAK,EAAEC,IAAI,CAACD,KAAK;YAAEE,OAAO,EAAED,IAAI,CAACC,OAAO;YAAEqG;UAAM,CAAC;QAC5E;QACA;MAEF,KAAK,QAAQ;QACX,IAAIA,KAAK,CAAClD,IAAI,CAAC,CAAC,IAAImD,KAAK,CAAC,IAAI,CAACT,WAAW,CAACQ,KAAK,CAAC,CAAC,EAAE;UAClD,OAAO;YAAE9F,GAAG,EAAEiF,SAAS;YAAE1F,KAAK,EAAEC,IAAI,CAACD,KAAK;YAAEE,OAAO,EAAED,IAAI,CAACC,OAAO;YAAEqG;UAAM,CAAC;QAC5E;QACA;MAEF,KAAK,MAAM;QACT,IAAIA,KAAK,CAAClD,IAAI,CAAC,CAAC,IAAI,CAAC,IAAI,CAACoD,WAAW,CAACF,KAAK,CAAC,EAAE;UAC5C,OAAO;YAAE9F,GAAG,EAAEiF,SAAS;YAAE1F,KAAK,EAAEC,IAAI,CAACD,KAAK;YAAEE,OAAO,EAAED,IAAI,CAACC,OAAO;YAAEqG;UAAM,CAAC;QAC5E;QACA;MAEF,KAAK,UAAU;QACb,IAAIA,KAAK,CAAClD,IAAI,CAAC,CAAC,IAAI,IAAI,CAAC0C,WAAW,CAACQ,KAAK,CAAC,GAAG,CAAC,EAAE;UAC/C,OAAO;YAAE9F,GAAG,EAAEiF,SAAS;YAAE1F,KAAK,EAAEC,IAAI,CAACD,KAAK;YAAEE,OAAO,EAAED,IAAI,CAACC,OAAO;YAAEqG;UAAM,CAAC;QAC5E;QACA;IACJ;IAEA,OAAO,IAAI;EACb;EAEQR,WAAWA,CAACQ,KAAa,EAAU;IACzC,IAAI,CAACA,KAAK,IAAIA,KAAK,CAAClD,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE,OAAO,CAAC;;IAE3C;IACA,MAAMqD,OAAO,GAAGH,KAAK,CAACI,OAAO,CAAC,SAAS,EAAE,EAAE,CAAC;IAC5C,MAAMC,MAAM,GAAGC,UAAU,CAACH,OAAO,CAAC;IAElC,OAAOF,KAAK,CAACI,MAAM,CAAC,GAAG,CAAC,GAAGR,IAAI,CAACC,GAAG,CAACO,MAAM,CAAC,CAAC,CAAC;EAC/C;EAEQH,WAAWA,CAACK,UAAkB,EAAW;IAC/C,MAAMhC,IAAI,GAAG,IAAIiC,IAAI,CAACD,UAAU,CAAC;IACjC,OAAOhC,IAAI,YAAYiC,IAAI,IAAI,CAACP,KAAK,CAAC1B,IAAI,CAACkC,OAAO,CAAC,CAAC,CAAC;EACvD;;EAEA;EACAC,qBAAqBA,CAAC1G,IAAc,EAAiB;IACnD,OAAOA,IAAI,CAACC,GAAG,CAAC,CAACC,GAAG,EAAEwD,KAAK,MAAM;MAC/BiD,EAAE,EAAE,QAAQH,IAAI,CAACI,GAAG,CAAC,CAAC,IAAIlD,KAAK,EAAE;MACjCa,IAAI,EAAE,IAAI,CAACsC,UAAU,CAAC3G,GAAG,CAACqE,IAAI,CAAC;MAC/BC,WAAW,EAAEtE,GAAG,CAACsE,WAAW;MAC5BsC,WAAW,EAAE,IAAI,CAACtB,WAAW,CAACtF,GAAG,CAACuE,KAAK,CAAC;MACxCsC,YAAY,EAAE,IAAI,CAACvB,WAAW,CAACtF,GAAG,CAACwE,MAAM,CAAC;MAC1CC,OAAO,EAAE,IAAI,CAACa,WAAW,CAACtF,GAAG,CAACyE,OAAO,CAAC;MACtCC,SAAS,EAAE1E,GAAG,CAAC0E;IACjB,CAAC,CAAC,CAAC;EACL;EAEQiC,UAAUA,CAACN,UAAkB,EAAU;IAC7C,MAAMhC,IAAI,GAAG,IAAIiC,IAAI,CAACD,UAAU,CAAC;IACjC,OAAOhC,IAAI,CAACyC,WAAW,CAAC,CAAC,CAACrE,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;EACzC;;EAEA;EACA,MAAMsE,WAAWA,CAACxF,IAAU,EAA0B;IACpD,IAAI;MACF,MAAMzB,IAAI,GAAG,MAAM,IAAI,CAACwB,QAAQ,CAACC,IAAI,CAAC;MACtC,MAAMyF,gBAAgB,GAAG,IAAI,CAACjC,eAAe,CAACjF,IAAI,CAAC;MACnD,MAAMmH,YAAY,GAAG,IAAI,CAACT,qBAAqB,CAAC1G,IAAI,CAAC;MAErD,MAAMoH,gBAAgB,GAAGD,YAAY,CAACE,MAAM,CAAC,CAACC,GAAG,EAAEC,CAAC,KAAKD,GAAG,GAAGC,CAAC,CAACT,WAAW,EAAE,CAAC,CAAC;MAChF,MAAMU,iBAAiB,GAAGL,YAAY,CAACE,MAAM,CAAC,CAACC,GAAG,EAAEC,CAAC,KAAKD,GAAG,GAAGC,CAAC,CAACR,YAAY,EAAE,CAAC,CAAC;MAClF,MAAMU,cAAc,GAAGN,YAAY,CAACpE,MAAM,GAAG,CAAC,GAAGoE,YAAY,CAACA,YAAY,CAACpE,MAAM,GAAG,CAAC,CAAC,CAAC4B,OAAO,GAAG,CAAC;MAElG,OAAO;QACL+C,QAAQ,EAAEjG,IAAI,CAACkG,IAAI;QACnBC,iBAAiB,EAAET,YAAY,CAACpE,MAAM;QACtCqE,gBAAgB;QAChBI,iBAAiB;QACjBC,cAAc;QACdP,gBAAgB;QAChBC;MACF,CAAC;IACH,CAAC,CAAC,OAAO7E,KAAK,EAAE;MACd,MAAM,IAAIE,KAAK,CAAC,qBAAqBf,IAAI,CAACkG,IAAI,KAAKrF,KAAK,YAAYE,KAAK,GAAGF,KAAK,CAAC3C,OAAO,GAAG,eAAe,EAAE,CAAC;IAChH;EACF;AACF;AAEA,OAAO,MAAMkI,oBAAoB,GAAG,IAAIhI,oBAAoB,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}