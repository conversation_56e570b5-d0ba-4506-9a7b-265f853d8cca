{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.transformArguments = exports.IS_READ_ONLY = void 0;\nexports.IS_READ_ONLY = true;\nfunction transformArguments(username, command) {\n  return ['ACL', 'DRYRUN', username, ...command];\n}\nexports.transformArguments = transformArguments;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "transformArguments", "IS_READ_ONLY", "username", "command"], "sources": ["C:/tmsft/node_modules/@redis/client/dist/lib/commands/ACL_DRYRUN.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.transformArguments = exports.IS_READ_ONLY = void 0;\nexports.IS_READ_ONLY = true;\nfunction transformArguments(username, command) {\n    return [\n        'ACL',\n        'DRYRUN',\n        username,\n        ...command\n    ];\n}\nexports.transformArguments = transformArguments;\n"], "mappings": "AAAA,YAAY;;AACZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEC,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7DD,OAAO,CAACE,kBAAkB,GAAGF,OAAO,CAACG,YAAY,GAAG,KAAK,CAAC;AAC1DH,OAAO,CAACG,YAAY,GAAG,IAAI;AAC3B,SAASD,kBAAkBA,CAACE,QAAQ,EAAEC,OAAO,EAAE;EAC3C,OAAO,CACH,KAAK,EACL,QAAQ,EACRD,QAAQ,EACR,GAAGC,OAAO,CACb;AACL;AACAL,OAAO,CAACE,kBAAkB,GAAGA,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}