{"ast": null, "code": "/**\n * @license\n * Copyright 2017 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { env, util } from '@tensorflow/tfjs-core';\nimport { getWebGLContext } from './canvas_util';\nimport { getTextureConfig } from './tex_util';\nexport function callAndCheck(gl, func) {\n  const returnValue = func();\n  if (env().getBool('DEBUG')) {\n    checkWebGLError(gl);\n  }\n  return returnValue;\n}\nfunction checkWebGLError(gl) {\n  const error = gl.getError();\n  if (error !== gl.NO_ERROR) {\n    throw new Error('WebGL Error: ' + getWebGLErrorMessage(gl, error));\n  }\n}\n// https://en.wikipedia.org/wiki/Half-precision_floating-point_format\nconst MIN_FLOAT16 = 5.96e-8;\nconst MAX_FLOAT16 = 65504;\nexport function canBeRepresented(num) {\n  if (env().getBool('WEBGL_RENDER_FLOAT32_ENABLED') || num === 0 || MIN_FLOAT16 < Math.abs(num) && Math.abs(num) < MAX_FLOAT16) {\n    return true;\n  }\n  return false;\n}\nexport function getWebGLErrorMessage(gl, status) {\n  switch (status) {\n    case gl.NO_ERROR:\n      return 'NO_ERROR';\n    case gl.INVALID_ENUM:\n      return 'INVALID_ENUM';\n    case gl.INVALID_VALUE:\n      return 'INVALID_VALUE';\n    case gl.INVALID_OPERATION:\n      return 'INVALID_OPERATION';\n    case gl.INVALID_FRAMEBUFFER_OPERATION:\n      return 'INVALID_FRAMEBUFFER_OPERATION';\n    case gl.OUT_OF_MEMORY:\n      return 'OUT_OF_MEMORY';\n    case gl.CONTEXT_LOST_WEBGL:\n      return 'CONTEXT_LOST_WEBGL';\n    default:\n      return `Unknown error code ${status}`;\n  }\n}\nexport function getExtensionOrThrow(gl, extensionName) {\n  return throwIfNull(gl, () => gl.getExtension(extensionName), 'Extension \"' + extensionName + '\" not supported on this browser.');\n}\nexport function createVertexShader(gl, vertexShaderSource) {\n  const vertexShader = throwIfNull(gl, () => gl.createShader(gl.VERTEX_SHADER), 'Unable to create vertex WebGLShader.');\n  callAndCheck(gl, () => gl.shaderSource(vertexShader, vertexShaderSource));\n  callAndCheck(gl, () => gl.compileShader(vertexShader));\n  if (gl.getShaderParameter(vertexShader, gl.COMPILE_STATUS) === false) {\n    console.log(gl.getShaderInfoLog(vertexShader));\n    throw new Error('Failed to compile vertex shader.');\n  }\n  return vertexShader;\n}\nexport function createFragmentShader(gl, fragmentShaderSource) {\n  const fragmentShader = throwIfNull(gl, () => gl.createShader(gl.FRAGMENT_SHADER), 'Unable to create fragment WebGLShader.');\n  callAndCheck(gl, () => gl.shaderSource(fragmentShader, fragmentShaderSource));\n  callAndCheck(gl, () => gl.compileShader(fragmentShader));\n  if (env().get('ENGINE_COMPILE_ONLY')) {\n    return fragmentShader;\n  }\n  if (gl.getShaderParameter(fragmentShader, gl.COMPILE_STATUS) === false) {\n    logShaderSourceAndInfoLog(fragmentShaderSource, gl.getShaderInfoLog(fragmentShader));\n    throw new Error('Failed to compile fragment shader.');\n  }\n  return fragmentShader;\n}\nconst lineNumberRegex = /ERROR: [0-9]+:([0-9]+):/g;\nexport function logShaderSourceAndInfoLog(shaderSource, shaderInfoLog) {\n  const lineNumberRegexResult = lineNumberRegex.exec(shaderInfoLog);\n  if (lineNumberRegexResult == null) {\n    console.log(`Couldn't parse line number in error: ${shaderInfoLog}`);\n    console.log(shaderSource);\n    return;\n  }\n  const lineNumber = +lineNumberRegexResult[1];\n  const shaderLines = shaderSource.split('\\n');\n  const pad = shaderLines.length.toString().length + 2;\n  const linesWithLineNumbers = shaderLines.map((line, lineNumber) => util.rightPad((lineNumber + 1).toString(), pad) + line);\n  let maxLineLength = 0;\n  for (let i = 0; i < linesWithLineNumbers.length; i++) {\n    maxLineLength = Math.max(linesWithLineNumbers[i].length, maxLineLength);\n  }\n  const beforeErrorLines = linesWithLineNumbers.slice(0, lineNumber - 1);\n  const errorLine = linesWithLineNumbers.slice(lineNumber - 1, lineNumber);\n  const afterErrorLines = linesWithLineNumbers.slice(lineNumber);\n  console.log(beforeErrorLines.join('\\n'));\n  console.log(shaderInfoLog.split('\\n')[0]);\n  console.log(`%c ${util.rightPad(errorLine[0], maxLineLength)}`, 'border:1px solid red; background-color:#e3d2d2; color:#a61717');\n  console.log(afterErrorLines.join('\\n'));\n}\nexport function createProgram(gl) {\n  return throwIfNull(gl, () => gl.createProgram(), 'Unable to create WebGLProgram.');\n}\nexport function linkProgram(gl, program) {\n  callAndCheck(gl, () => gl.linkProgram(program));\n  if (env().get('ENGINE_COMPILE_ONLY')) {\n    return;\n  }\n  if (gl.getProgramParameter(program, gl.LINK_STATUS) === false) {\n    console.log(gl.getProgramInfoLog(program));\n    throw new Error('Failed to link vertex and fragment shaders.');\n  }\n}\n/// validateProgram is effectively \"If we `useProgram(program); drawArrays();`,\n/// give feedback in log about perf/correctness warnings or errors that would\n/// occur.\"\n/// So make sure we set up all vertex/texture/sampler/uniform data before\n/// calling validateProgram!\nexport function validateProgram(gl, program) {\n  callAndCheck(gl, () => gl.validateProgram(program));\n  if (gl.getProgramParameter(program, gl.VALIDATE_STATUS) === false) {\n    console.log(gl.getProgramInfoLog(program));\n    throw new Error('Shader program validation failed.');\n  }\n}\nexport function createStaticVertexBuffer(gl, data) {\n  const buffer = throwIfNull(gl, () => gl.createBuffer(), 'Unable to create WebGLBuffer');\n  callAndCheck(gl, () => gl.bindBuffer(gl.ARRAY_BUFFER, buffer));\n  callAndCheck(gl, () => gl.bufferData(gl.ARRAY_BUFFER, data, gl.STATIC_DRAW));\n  return buffer;\n}\nexport function createStaticIndexBuffer(gl, data) {\n  const buffer = throwIfNull(gl, () => gl.createBuffer(), 'Unable to create WebGLBuffer');\n  callAndCheck(gl, () => gl.bindBuffer(gl.ELEMENT_ARRAY_BUFFER, buffer));\n  callAndCheck(gl, () => gl.bufferData(gl.ELEMENT_ARRAY_BUFFER, data, gl.STATIC_DRAW));\n  return buffer;\n}\nexport function getNumChannels() {\n  if (env().getNumber('WEBGL_VERSION') === 2) {\n    return 1;\n  }\n  return 4;\n}\nexport function createTexture(gl) {\n  return throwIfNull(gl, () => gl.createTexture(), 'Unable to create WebGLTexture.');\n}\nexport function validateTextureSize(width, height) {\n  const maxTextureSize = env().getNumber('WEBGL_MAX_TEXTURE_SIZE');\n  if (width <= 0 || height <= 0) {\n    const requested = `[${width}x${height}]`;\n    throw new Error('Requested texture size ' + requested + ' is invalid.');\n  }\n  if (width > maxTextureSize || height > maxTextureSize) {\n    const requested = `[${width}x${height}]`;\n    const max = `[${maxTextureSize}x${maxTextureSize}]`;\n    throw new Error('Requested texture size ' + requested + ' greater than WebGL maximum on this browser / GPU ' + max + '.');\n  }\n}\nexport function createFramebuffer(gl) {\n  return throwIfNull(gl, () => gl.createFramebuffer(), 'Unable to create WebGLFramebuffer.');\n}\nexport function bindVertexBufferToProgramAttribute(gl, program, attribute, buffer, arrayEntriesPerItem, itemStrideInBytes, itemOffsetInBytes) {\n  const loc = gl.getAttribLocation(program, attribute);\n  if (loc === -1) {\n    // The GPU compiler decided to strip out this attribute because it's unused,\n    // thus no need to bind.\n    return false;\n  }\n  callAndCheck(gl, () => gl.bindBuffer(gl.ARRAY_BUFFER, buffer));\n  callAndCheck(gl, () => gl.vertexAttribPointer(loc, arrayEntriesPerItem, gl.FLOAT, false, itemStrideInBytes, itemOffsetInBytes));\n  callAndCheck(gl, () => gl.enableVertexAttribArray(loc));\n  return true;\n}\nexport function bindTextureUnit(gl, texture, textureUnit) {\n  validateTextureUnit(gl, textureUnit);\n  callAndCheck(gl, () => gl.activeTexture(gl.TEXTURE0 + textureUnit));\n  callAndCheck(gl, () => gl.bindTexture(gl.TEXTURE_2D, texture));\n}\nexport function unbindTextureUnit(gl, textureUnit) {\n  validateTextureUnit(gl, textureUnit);\n  callAndCheck(gl, () => gl.activeTexture(gl.TEXTURE0 + textureUnit));\n  callAndCheck(gl, () => gl.bindTexture(gl.TEXTURE_2D, null));\n}\nexport function getProgramUniformLocationOrThrow(gl, program, uniformName) {\n  return throwIfNull(gl, () => gl.getUniformLocation(program, uniformName), 'uniform \"' + uniformName + '\" not present in program.');\n}\nexport function getProgramUniformLocation(gl, program, uniformName) {\n  return gl.getUniformLocation(program, uniformName);\n}\nexport function bindTextureToProgramUniformSampler(gl, texture, uniformSamplerLocation, textureUnit) {\n  callAndCheck(gl, () => bindTextureUnit(gl, texture, textureUnit));\n  callAndCheck(gl, () => gl.uniform1i(uniformSamplerLocation, textureUnit));\n}\nexport function bindCanvasToFramebuffer(gl) {\n  callAndCheck(gl, () => gl.bindFramebuffer(gl.FRAMEBUFFER, null));\n  callAndCheck(gl, () => gl.viewport(0, 0, gl.canvas.width, gl.canvas.height));\n  callAndCheck(gl, () => gl.scissor(0, 0, gl.canvas.width, gl.canvas.height));\n}\nexport function bindColorTextureToFramebuffer(gl, texture, framebuffer) {\n  callAndCheck(gl, () => gl.bindFramebuffer(gl.FRAMEBUFFER, framebuffer));\n  callAndCheck(gl, () => gl.framebufferTexture2D(gl.FRAMEBUFFER, gl.COLOR_ATTACHMENT0, gl.TEXTURE_2D, texture, 0));\n}\nexport function unbindColorTextureFromFramebuffer(gl, framebuffer) {\n  callAndCheck(gl, () => gl.bindFramebuffer(gl.FRAMEBUFFER, framebuffer));\n  callAndCheck(gl, () => gl.framebufferTexture2D(gl.FRAMEBUFFER, gl.COLOR_ATTACHMENT0, gl.TEXTURE_2D, null, 0));\n}\nexport function validateFramebuffer(gl) {\n  const status = gl.checkFramebufferStatus(gl.FRAMEBUFFER);\n  if (status !== gl.FRAMEBUFFER_COMPLETE) {\n    throw new Error('Error binding framebuffer: ' + getFramebufferErrorMessage(gl, status));\n  }\n}\nexport function getFramebufferErrorMessage(gl, status) {\n  switch (status) {\n    case gl.FRAMEBUFFER_INCOMPLETE_ATTACHMENT:\n      return 'FRAMEBUFFER_INCOMPLETE_ATTACHMENT';\n    case gl.FRAMEBUFFER_INCOMPLETE_MISSING_ATTACHMENT:\n      return 'FRAMEBUFFER_INCOMPLETE_MISSING_ATTACHMENT';\n    case gl.FRAMEBUFFER_INCOMPLETE_DIMENSIONS:\n      return 'FRAMEBUFFER_INCOMPLETE_DIMENSIONS';\n    case gl.FRAMEBUFFER_UNSUPPORTED:\n      return 'FRAMEBUFFER_UNSUPPORTED';\n    default:\n      return `unknown error ${status}`;\n  }\n}\nfunction throwIfNull(gl, returnTOrNull, failureMessage) {\n  const tOrNull = callAndCheck(gl, () => returnTOrNull());\n  if (tOrNull == null) {\n    throw new Error(failureMessage);\n  }\n  return tOrNull;\n}\nfunction validateTextureUnit(gl, textureUnit) {\n  const maxTextureUnit = gl.MAX_COMBINED_TEXTURE_IMAGE_UNITS - 1;\n  const glTextureUnit = textureUnit + gl.TEXTURE0;\n  if (glTextureUnit < gl.TEXTURE0 || glTextureUnit > maxTextureUnit) {\n    const textureUnitRange = `[gl.TEXTURE0, gl.TEXTURE${maxTextureUnit}]`;\n    throw new Error(`textureUnit must be in ${textureUnitRange}.`);\n  }\n}\nexport function getBatchDim(shape, dimsToSkip = 2) {\n  return util.sizeFromShape(shape.slice(0, shape.length - dimsToSkip));\n}\nexport function getRowsCols(shape) {\n  if (shape.length === 0) {\n    throw Error('Cannot get rows and columns of an empty shape array.');\n  }\n  return [shape.length > 1 ? shape[shape.length - 2] : 1, shape[shape.length - 1]];\n}\nexport function getShapeAs3D(shape) {\n  let shapeAs3D = [1, 1, 1];\n  const isScalar = shape.length === 0 || shape.length === 1 && shape[0] === 1;\n  if (!isScalar) {\n    shapeAs3D = [getBatchDim(shape), ...getRowsCols(shape)];\n  }\n  return shapeAs3D;\n}\nexport function getTextureShapeFromLogicalShape(logShape, isPacked = false) {\n  let maxTexSize = env().getNumber('WEBGL_MAX_TEXTURE_SIZE');\n  let maxSizeForNarrowTex = env().getNumber('WEBGL_MAX_SIZE_FOR_NARROW_TEXTURE');\n  if (maxSizeForNarrowTex === Infinity && env().getBool('WEBGL_AUTO_SQUARIFY_NARROW_TEXTURE_SHAPE')) {\n    maxSizeForNarrowTex = maxTexSize / 2;\n  }\n  if (isPacked) {\n    maxTexSize = maxTexSize * 2;\n    maxSizeForNarrowTex = maxSizeForNarrowTex * 2;\n    // This logic ensures we accurately count the number of packed texels needed\n    // to accommodate the tensor. We can only pack values in the same texel if\n    // they are from adjacent pairs of rows/cols within the same batch. So if a\n    // tensor has 3 rows, we pretend it has 4 rows in order to account for the\n    // fact that the texels containing the third row are half empty.\n    logShape = logShape.map((d, i) => i >= logShape.length - 2 ? util.nearestLargerEven(logShape[i]) : logShape[i]);\n    // Packed texture height is at least 2 (the channel height of a single\n    // texel).\n    if (logShape.length === 1) {\n      logShape = [2, logShape[0]];\n    }\n  }\n  // If logical shape is 2, we don't squeeze, since we want to match physical.\n  if (logShape.length !== 2) {\n    const squeezeResult = util.squeezeShape(logShape);\n    logShape = squeezeResult.newShape;\n  }\n  let size = util.sizeFromShape(logShape);\n  let textureShape = null;\n  if (logShape.length <= 1 && size <= maxTexSize) {\n    textureShape = [1, size];\n  } else if (logShape.length === 2 && logShape[0] <= maxTexSize && logShape[1] <= maxTexSize) {\n    textureShape = logShape;\n  } else if (logShape.length === 3 && logShape[0] * logShape[1] <= maxTexSize && logShape[2] <= maxTexSize) {\n    textureShape = [logShape[0] * logShape[1], logShape[2]];\n  } else if (logShape.length === 3 && logShape[0] <= maxTexSize && logShape[1] * logShape[2] <= maxTexSize) {\n    textureShape = [logShape[0], logShape[1] * logShape[2]];\n  } else if (logShape.length === 4 && logShape[0] * logShape[1] * logShape[2] <= maxTexSize && logShape[3] <= maxTexSize) {\n    textureShape = [logShape[0] * logShape[1] * logShape[2], logShape[3]];\n  } else if (logShape.length === 4 && logShape[0] <= maxTexSize && logShape[1] * logShape[2] * logShape[3] <= maxTexSize) {\n    textureShape = [logShape[0], logShape[1] * logShape[2] * logShape[3]];\n  }\n  // true if one edge length is 1 (1 or 2, if packed), while another edge\n  // length exceeds maxSizeForNarrowTex.\n  const isLongNarrowTex = textureShape != null && Math.max(...textureShape) > maxSizeForNarrowTex && Math.min(...textureShape) <= (isPacked ? 2 : 1) && Math.min(...textureShape) > 0;\n  if (textureShape == null || isLongNarrowTex) {\n    if (isPacked) {\n      // For packed textures size equals the number of channels required to\n      // accommodate the texture data. However in order to squarify such that\n      // inner dimensions stay even, we rewrite size to equal the number of\n      // texels. Then in the return statement we rehydrate the squarified\n      // dimensions to channel units.\n      const batchDim = getBatchDim(logShape);\n      let rows = 2,\n        cols = 2;\n      if (logShape.length) {\n        [rows, cols] = getRowsCols(logShape);\n      }\n      size = batchDim * (rows / 2) * (cols / 2);\n      textureShape = util.sizeToSquarishShape(size).map(d => d * 2);\n    } else {\n      textureShape = util.sizeToSquarishShape(size);\n    }\n  }\n  return textureShape;\n}\nfunction isEven(n) {\n  return n % 2 === 0;\n}\n/**\n * This determines whether reshaping a packed texture requires rearranging\n * the data within the texture, assuming 2x2 packing.\n */\nexport function isReshapeFree(shape1, shape2) {\n  shape1 = shape1.slice(-2);\n  shape2 = shape2.slice(-2);\n  if (util.arraysEqual(shape1, shape2)) {\n    return true;\n  }\n  if (!shape1.length || !shape2.length) {\n    // One of the shapes is a scalar.\n    return true;\n  }\n  if (shape1[0] === 0 || shape1[1] === 0 || shape2[0] === 0 || shape2[1] === 0) {\n    return true;\n  }\n  if (shape1.length !== shape2.length) {\n    // One of the shapes is a vector.\n    const shape1Cols = shape1[shape1.length - 1];\n    const shape2Cols = shape2[shape2.length - 1];\n    if (shape1Cols === shape2Cols) {\n      return true;\n    }\n    if (isEven(shape1Cols) && isEven(shape2Cols) && (shape1[0] === 1 || shape2[0] === 1)) {\n      return true;\n    }\n  }\n  return shape1[1] === shape2[1] && isEven(shape1[0]) && isEven(shape2[0]);\n}\n// We cache webgl params because the environment gets reset between\n// unit tests and we don't want to constantly query the WebGLContext for\n// MAX_TEXTURE_SIZE.\nlet MAX_TEXTURE_SIZE;\nlet MAX_TEXTURES_IN_SHADER;\nexport function getWebGLMaxTextureSize(webGLVersion) {\n  if (MAX_TEXTURE_SIZE == null) {\n    const gl = getWebGLContext(webGLVersion);\n    MAX_TEXTURE_SIZE = gl.getParameter(gl.MAX_TEXTURE_SIZE);\n  }\n  return MAX_TEXTURE_SIZE;\n}\nexport function resetMaxTextureSize() {\n  MAX_TEXTURE_SIZE = null;\n}\nexport function resetMaxTexturesInShader() {\n  MAX_TEXTURES_IN_SHADER = null;\n}\nexport function getMaxTexturesInShader(webGLVersion) {\n  if (MAX_TEXTURES_IN_SHADER == null) {\n    const gl = getWebGLContext(webGLVersion);\n    MAX_TEXTURES_IN_SHADER = gl.getParameter(gl.MAX_TEXTURE_IMAGE_UNITS);\n  }\n  // We cap at 16 to avoid spurious runtime \"memory exhausted\" error.\n  return Math.min(16, MAX_TEXTURES_IN_SHADER);\n}\nexport function getWebGLDisjointQueryTimerVersion(webGLVersion) {\n  if (webGLVersion === 0) {\n    return 0;\n  }\n  let queryTimerVersion;\n  const gl = getWebGLContext(webGLVersion);\n  if (hasExtension(gl, 'EXT_disjoint_timer_query_webgl2') && webGLVersion === 2) {\n    queryTimerVersion = 2;\n  } else if (hasExtension(gl, 'EXT_disjoint_timer_query')) {\n    queryTimerVersion = 1;\n  } else {\n    queryTimerVersion = 0;\n  }\n  return queryTimerVersion;\n}\nexport function hasExtension(gl, extensionName) {\n  const ext = gl.getExtension(extensionName);\n  return ext != null;\n}\nexport function isWebGLVersionEnabled(webGLVersion) {\n  try {\n    const gl = getWebGLContext(webGLVersion);\n    if (gl != null) {\n      return true;\n    }\n  } catch (e) {\n    console.log('Error when getting WebGL context: ', e);\n    return false;\n  }\n  return false;\n}\nexport function isCapableOfRenderingToFloatTexture(webGLVersion) {\n  if (webGLVersion === 0) {\n    return false;\n  }\n  const gl = getWebGLContext(webGLVersion);\n  if (webGLVersion === 1) {\n    if (!hasExtension(gl, 'OES_texture_float')) {\n      return false;\n    }\n  } else {\n    if (!hasExtension(gl, 'EXT_color_buffer_float')) {\n      return false;\n    }\n  }\n  const isFrameBufferComplete = createFloatTextureAndBindToFramebuffer(gl);\n  return isFrameBufferComplete;\n}\n/**\n * Check if we can download values from a float/half-float texture.\n *\n * Note that for performance reasons we use binding a texture to a framebuffer\n * as a proxy for ability to download float values later using readPixels. The\n * texture params of this texture will not match those in readPixels exactly\n * but if we are unable to bind some kind of float texture to the frameBuffer\n * then we definitely will not be able to read float values from it.\n */\nexport function isDownloadFloatTextureEnabled(webGLVersion) {\n  if (webGLVersion === 0) {\n    return false;\n  }\n  const gl = getWebGLContext(webGLVersion);\n  if (webGLVersion === 1) {\n    if (!hasExtension(gl, 'OES_texture_float')) {\n      return false;\n    }\n    if (!hasExtension(gl, 'WEBGL_color_buffer_float')) {\n      return false;\n    }\n  } else {\n    if (hasExtension(gl, 'EXT_color_buffer_float')) {\n      return createFloatTextureAndBindToFramebuffer(gl);\n    }\n    const COLOR_BUFFER_HALF_FLOAT = 'EXT_color_buffer_half_float';\n    if (hasExtension(gl, COLOR_BUFFER_HALF_FLOAT)) {\n      const textureHalfFloatExtension = gl.getExtension(COLOR_BUFFER_HALF_FLOAT);\n      return createHalfFloatTextureAndBindToFramebuffer(gl, textureHalfFloatExtension);\n    }\n    return false;\n  }\n  const isFrameBufferComplete = createFloatTextureAndBindToFramebuffer(gl);\n  return isFrameBufferComplete;\n}\nfunction createFloatTextureAndBindToFramebuffer(gl) {\n  const texConfig = getTextureConfig(gl);\n  const texture = gl.createTexture();\n  gl.bindTexture(gl.TEXTURE_2D, texture);\n  const width = 1;\n  const height = 1;\n  gl.texImage2D(gl.TEXTURE_2D, 0, texConfig.internalFormatFloat, width, height, 0, texConfig.textureFormatFloat, texConfig.textureTypeFloat, null);\n  const frameBuffer = gl.createFramebuffer();\n  gl.bindFramebuffer(gl.FRAMEBUFFER, frameBuffer);\n  gl.framebufferTexture2D(gl.FRAMEBUFFER, gl.COLOR_ATTACHMENT0, gl.TEXTURE_2D, texture, 0);\n  const isFrameBufferComplete = gl.checkFramebufferStatus(gl.FRAMEBUFFER) === gl.FRAMEBUFFER_COMPLETE;\n  gl.bindTexture(gl.TEXTURE_2D, null);\n  gl.bindFramebuffer(gl.FRAMEBUFFER, null);\n  gl.deleteTexture(texture);\n  gl.deleteFramebuffer(frameBuffer);\n  return isFrameBufferComplete;\n}\nfunction createHalfFloatTextureAndBindToFramebuffer(\n// tslint:disable-next-line:no-any\ngl, textureHalfFloatExtension) {\n  const texConfig = getTextureConfig(gl, textureHalfFloatExtension);\n  const texture = gl.createTexture();\n  gl.bindTexture(gl.TEXTURE_2D, texture);\n  const width = 1;\n  const height = 1;\n  gl.texImage2D(gl.TEXTURE_2D, 0, texConfig.internalFormatHalfFloat, width, height, 0, texConfig.textureFormatFloat, texConfig.textureTypeHalfFloat, null);\n  const frameBuffer = gl.createFramebuffer();\n  gl.bindFramebuffer(gl.FRAMEBUFFER, frameBuffer);\n  gl.framebufferTexture2D(gl.FRAMEBUFFER, gl.COLOR_ATTACHMENT0, gl.TEXTURE_2D, texture, 0);\n  const isFrameBufferComplete = gl.checkFramebufferStatus(gl.FRAMEBUFFER) === gl.FRAMEBUFFER_COMPLETE;\n  gl.bindTexture(gl.TEXTURE_2D, null);\n  gl.bindFramebuffer(gl.FRAMEBUFFER, null);\n  gl.deleteTexture(texture);\n  gl.deleteFramebuffer(frameBuffer);\n  return isFrameBufferComplete;\n}\nexport function isWebGLFenceEnabled(webGLVersion) {\n  if (webGLVersion !== 2) {\n    return false;\n  }\n  const gl = getWebGLContext(webGLVersion);\n  // tslint:disable-next-line:no-any\n  const isEnabled = gl.fenceSync != null;\n  return isEnabled;\n}\nexport function assertNotComplex(tensor, opName) {\n  if (!Array.isArray(tensor)) {\n    tensor = [tensor];\n  }\n  tensor.forEach(t => {\n    if (t != null) {\n      util.assert(t.dtype !== 'complex64', () => `${opName} does not support complex64 tensors ` + 'in the WebGL backend.');\n    }\n  });\n}", "map": {"version": 3, "names": ["env", "util", "getWebGLContext", "getTextureConfig", "callAndCheck", "gl", "func", "returnValue", "getBool", "checkWebGLError", "error", "getError", "NO_ERROR", "Error", "getWebGLErrorMessage", "MIN_FLOAT16", "MAX_FLOAT16", "canBeRepresented", "num", "Math", "abs", "status", "INVALID_ENUM", "INVALID_VALUE", "INVALID_OPERATION", "INVALID_FRAMEBUFFER_OPERATION", "OUT_OF_MEMORY", "CONTEXT_LOST_WEBGL", "getExtensionOrThrow", "extensionName", "throwIfNull", "getExtension", "createVertexShader", "vertexShaderSource", "vertexShader", "createShader", "VERTEX_SHADER", "shaderSource", "compileShader", "getShaderParameter", "COMPILE_STATUS", "console", "log", "getShaderInfoLog", "createFragmentShader", "fragmentShaderSource", "fragmentShader", "FRAGMENT_SHADER", "get", "logShaderSourceAndInfoLog", "lineNumberRegex", "shaderInfoLog", "lineNumberRegexResult", "exec", "lineNumber", "shaderLines", "split", "pad", "length", "toString", "linesWithLineNumbers", "map", "line", "rightPad", "max<PERSON><PERSON><PERSON><PERSON><PERSON>", "i", "max", "beforeErrorLines", "slice", "errorLine", "afterErrorLines", "join", "createProgram", "linkProgram", "program", "getProgramParameter", "LINK_STATUS", "getProgramInfoLog", "validateProgram", "VALIDATE_STATUS", "createStaticVertexBuffer", "data", "buffer", "createBuffer", "<PERSON><PERSON><PERSON><PERSON>", "ARRAY_BUFFER", "bufferData", "STATIC_DRAW", "createStaticIndexBuffer", "ELEMENT_ARRAY_BUFFER", "getNumChannels", "getNumber", "createTexture", "validateTextureSize", "width", "height", "maxTextureSize", "requested", "createFramebuffer", "bindVertexBufferToProgramAttribute", "attribute", "arrayEntriesPerItem", "itemStrideInBytes", "itemOffsetInBytes", "loc", "getAttribLocation", "vertexAttribPointer", "FLOAT", "enableVertexAttribArray", "bindTextureUnit", "texture", "textureUnit", "validateTextureUnit", "activeTexture", "TEXTURE0", "bindTexture", "TEXTURE_2D", "unbindTextureUnit", "getProgramUniformLocationOrThrow", "uniformName", "getUniformLocation", "getProgramUniformLocation", "bindTextureToProgramUniformSampler", "uniformSamplerLocation", "uniform1i", "bindCanvasToFramebuffer", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "FRAMEBUFFER", "viewport", "canvas", "scissor", "bindColorTextureToFramebuffer", "framebuffer", "framebufferTexture2D", "COLOR_ATTACHMENT0", "unbindColorTextureFromFramebuffer", "validate<PERSON><PERSON><PERSON><PERSON><PERSON>", "checkFramebufferStatus", "FRAMEBUFFER_COMPLETE", "getFramebufferErrorMessage", "FRAMEBUFFER_INCOMPLETE_ATTACHMENT", "FRAMEBUFFER_INCOMPLETE_MISSING_ATTACHMENT", "FRAMEBUFFER_INCOMPLETE_DIMENSIONS", "FRAMEBUFFER_UNSUPPORTED", "returnTOrNull", "failureMessage", "tOrNull", "maxTextureUnit", "MAX_COMBINED_TEXTURE_IMAGE_UNITS", "glTextureUnit", "textureUnitRange", "getBatchDim", "shape", "dimsToSkip", "sizeFromShape", "getRowsCols", "getShapeAs3D", "shapeAs3D", "isScalar", "getTextureShapeFromLogicalShape", "logShape", "isPacked", "maxTexSize", "maxSizeForNarrowTex", "Infinity", "d", "nearestLargerEven", "squeezeResult", "squeezeShape", "newShape", "size", "textureShape", "isLongNarrowTex", "min", "batchDim", "rows", "cols", "sizeToSquarishShape", "isEven", "n", "isReshapeFree", "shape1", "shape2", "arraysEqual", "shape1Cols", "shape2Cols", "MAX_TEXTURE_SIZE", "MAX_TEXTURES_IN_SHADER", "getWebGLMaxTextureSize", "webGLVersion", "getParameter", "resetMaxTextureSize", "resetMaxTexturesInShader", "getMaxTexturesInShader", "MAX_TEXTURE_IMAGE_UNITS", "getWebGLDisjointQueryTimerVersion", "queryTimerVersion", "hasExtension", "ext", "isWebGLVersionEnabled", "e", "isCapableOfRenderingToFloatTexture", "isFrameBufferComplete", "createFloatTextureAndBindToFramebuffer", "isDownloadFloatTextureEnabled", "COLOR_BUFFER_HALF_FLOAT", "textureHalfFloatExtension", "createHalfFloatTextureAndBindToFramebuffer", "texConfig", "texImage2D", "internalFormatFloat", "textureFormatFloat", "textureTypeFloat", "frameBuffer", "deleteTexture", "deleteFramebuffer", "internalFormatHalfFloat", "textureTypeHalfFloat", "isWebGLFenceEnabled", "isEnabled", "fenceSync", "assertNotComplex", "tensor", "opName", "Array", "isArray", "for<PERSON>ach", "t", "assert", "dtype"], "sources": ["C:\\tfjs-backend-webgl\\src\\webgl_util.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2017 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {env, TensorInfo, util} from '@tensorflow/tfjs-core';\n\nimport {getWebGLContext} from './canvas_util';\nimport {getTextureConfig} from './tex_util';\n\nexport function callAndCheck<T>(gl: WebGLRenderingContext, func: () => T): T {\n  const returnValue = func();\n  if (env().getBool('DEBUG')) {\n    checkWebGLError(gl);\n  }\n  return returnValue;\n}\n\nfunction checkWebGLError(gl: WebGLRenderingContext) {\n  const error = gl.getError();\n  if (error !== gl.NO_ERROR) {\n    throw new Error('WebGL Error: ' + getWebGLErrorMessage(gl, error));\n  }\n}\n\n// https://en.wikipedia.org/wiki/Half-precision_floating-point_format\nconst MIN_FLOAT16 = 5.96e-8;\nconst MAX_FLOAT16 = 65504;\n\nexport function canBeRepresented(num: number): boolean {\n  if (env().getBool('WEBGL_RENDER_FLOAT32_ENABLED') || num === 0 ||\n      (MIN_FLOAT16 < Math.abs(num) && Math.abs(num) < MAX_FLOAT16)) {\n    return true;\n  }\n  return false;\n}\n\nexport function getWebGLErrorMessage(\n    gl: WebGLRenderingContext, status: number): string {\n  switch (status) {\n    case gl.NO_ERROR:\n      return 'NO_ERROR';\n    case gl.INVALID_ENUM:\n      return 'INVALID_ENUM';\n    case gl.INVALID_VALUE:\n      return 'INVALID_VALUE';\n    case gl.INVALID_OPERATION:\n      return 'INVALID_OPERATION';\n    case gl.INVALID_FRAMEBUFFER_OPERATION:\n      return 'INVALID_FRAMEBUFFER_OPERATION';\n    case gl.OUT_OF_MEMORY:\n      return 'OUT_OF_MEMORY';\n    case gl.CONTEXT_LOST_WEBGL:\n      return 'CONTEXT_LOST_WEBGL';\n    default:\n      return `Unknown error code ${status}`;\n  }\n}\n\nexport function getExtensionOrThrow(\n    gl: WebGLRenderingContext, extensionName: string): {} {\n  return throwIfNull<{}>(\n      gl, () => gl.getExtension(extensionName),\n      'Extension \"' + extensionName + '\" not supported on this browser.');\n}\n\nexport function createVertexShader(\n    gl: WebGLRenderingContext, vertexShaderSource: string): WebGLShader {\n  const vertexShader: WebGLShader = throwIfNull<WebGLShader>(\n      gl, () => gl.createShader(gl.VERTEX_SHADER),\n      'Unable to create vertex WebGLShader.');\n  callAndCheck(gl, () => gl.shaderSource(vertexShader, vertexShaderSource));\n  callAndCheck(gl, () => gl.compileShader(vertexShader));\n  if (gl.getShaderParameter(vertexShader, gl.COMPILE_STATUS) === false) {\n    console.log(gl.getShaderInfoLog(vertexShader));\n    throw new Error('Failed to compile vertex shader.');\n  }\n  return vertexShader;\n}\n\nexport function createFragmentShader(\n    gl: WebGLRenderingContext, fragmentShaderSource: string): WebGLShader {\n  const fragmentShader: WebGLShader = throwIfNull<WebGLShader>(\n      gl, () => gl.createShader(gl.FRAGMENT_SHADER),\n      'Unable to create fragment WebGLShader.');\n  callAndCheck(gl, () => gl.shaderSource(fragmentShader, fragmentShaderSource));\n  callAndCheck(gl, () => gl.compileShader(fragmentShader));\n  if (env().get('ENGINE_COMPILE_ONLY')) {\n    return fragmentShader;\n  }\n  if (gl.getShaderParameter(fragmentShader, gl.COMPILE_STATUS) === false) {\n    logShaderSourceAndInfoLog(\n        fragmentShaderSource, gl.getShaderInfoLog(fragmentShader));\n    throw new Error('Failed to compile fragment shader.');\n  }\n  return fragmentShader;\n}\n\nconst lineNumberRegex = /ERROR: [0-9]+:([0-9]+):/g;\nexport function logShaderSourceAndInfoLog(\n    shaderSource: string, shaderInfoLog: string) {\n  const lineNumberRegexResult = lineNumberRegex.exec(shaderInfoLog);\n  if (lineNumberRegexResult == null) {\n    console.log(`Couldn't parse line number in error: ${shaderInfoLog}`);\n    console.log(shaderSource);\n    return;\n  }\n\n  const lineNumber = +lineNumberRegexResult[1];\n\n  const shaderLines = shaderSource.split('\\n');\n  const pad = shaderLines.length.toString().length + 2;\n  const linesWithLineNumbers = shaderLines.map(\n      (line, lineNumber) =>\n          util.rightPad((lineNumber + 1).toString(), pad) + line);\n  let maxLineLength = 0;\n  for (let i = 0; i < linesWithLineNumbers.length; i++) {\n    maxLineLength = Math.max(linesWithLineNumbers[i].length, maxLineLength);\n  }\n\n  const beforeErrorLines = linesWithLineNumbers.slice(0, lineNumber - 1);\n  const errorLine = linesWithLineNumbers.slice(lineNumber - 1, lineNumber);\n  const afterErrorLines = linesWithLineNumbers.slice(lineNumber);\n\n  console.log(beforeErrorLines.join('\\n'));\n  console.log(shaderInfoLog.split('\\n')[0]);\n  console.log(\n      `%c ${util.rightPad(errorLine[0], maxLineLength)}`,\n      'border:1px solid red; background-color:#e3d2d2; color:#a61717');\n  console.log(afterErrorLines.join('\\n'));\n}\n\nexport function createProgram(gl: WebGLRenderingContext): WebGLProgram {\n  return throwIfNull<WebGLProgram>(\n      gl, () => gl.createProgram(), 'Unable to create WebGLProgram.');\n}\n\nexport function linkProgram(gl: WebGLRenderingContext, program: WebGLProgram) {\n  callAndCheck(gl, () => gl.linkProgram(program));\n  if (env().get('ENGINE_COMPILE_ONLY')) {\n    return;\n  }\n  if (gl.getProgramParameter(program, gl.LINK_STATUS) === false) {\n    console.log(gl.getProgramInfoLog(program));\n    throw new Error('Failed to link vertex and fragment shaders.');\n  }\n}\n\n/// validateProgram is effectively \"If we `useProgram(program); drawArrays();`,\n/// give feedback in log about perf/correctness warnings or errors that would\n/// occur.\"\n/// So make sure we set up all vertex/texture/sampler/uniform data before\n/// calling validateProgram!\nexport function validateProgram(\n    gl: WebGLRenderingContext, program: WebGLProgram) {\n  callAndCheck(gl, () => gl.validateProgram(program));\n  if (gl.getProgramParameter(program, gl.VALIDATE_STATUS) === false) {\n    console.log(gl.getProgramInfoLog(program));\n    throw new Error('Shader program validation failed.');\n  }\n}\n\nexport function createStaticVertexBuffer(\n    gl: WebGLRenderingContext, data: Float32Array): WebGLBuffer {\n  const buffer: WebGLBuffer = throwIfNull<WebGLBuffer>(\n      gl, () => gl.createBuffer(), 'Unable to create WebGLBuffer');\n  callAndCheck(gl, () => gl.bindBuffer(gl.ARRAY_BUFFER, buffer));\n  callAndCheck(gl, () => gl.bufferData(gl.ARRAY_BUFFER, data, gl.STATIC_DRAW));\n  return buffer;\n}\n\nexport function createStaticIndexBuffer(\n    gl: WebGLRenderingContext, data: Uint16Array): WebGLBuffer {\n  const buffer: WebGLBuffer = throwIfNull<WebGLBuffer>(\n      gl, () => gl.createBuffer(), 'Unable to create WebGLBuffer');\n  callAndCheck(gl, () => gl.bindBuffer(gl.ELEMENT_ARRAY_BUFFER, buffer));\n  callAndCheck(\n      gl, () => gl.bufferData(gl.ELEMENT_ARRAY_BUFFER, data, gl.STATIC_DRAW));\n  return buffer;\n}\n\nexport function getNumChannels(): number {\n  if (env().getNumber('WEBGL_VERSION') === 2) {\n    return 1;\n  }\n  return 4;\n}\n\nexport function createTexture(gl: WebGLRenderingContext): WebGLTexture {\n  return throwIfNull<WebGLTexture>(\n      gl, () => gl.createTexture(), 'Unable to create WebGLTexture.');\n}\n\nexport function validateTextureSize(width: number, height: number) {\n  const maxTextureSize = env().getNumber('WEBGL_MAX_TEXTURE_SIZE');\n  if ((width <= 0) || (height <= 0)) {\n    const requested = `[${width}x${height}]`;\n    throw new Error('Requested texture size ' + requested + ' is invalid.');\n  }\n  if ((width > maxTextureSize) || (height > maxTextureSize)) {\n    const requested = `[${width}x${height}]`;\n    const max = `[${maxTextureSize}x${maxTextureSize}]`;\n    throw new Error(\n        'Requested texture size ' + requested +\n        ' greater than WebGL maximum on this browser / GPU ' + max + '.');\n  }\n}\n\nexport function createFramebuffer(gl: WebGLRenderingContext): WebGLFramebuffer {\n  return throwIfNull<WebGLFramebuffer>(\n      gl, () => gl.createFramebuffer(), 'Unable to create WebGLFramebuffer.');\n}\n\nexport function bindVertexBufferToProgramAttribute(\n    gl: WebGLRenderingContext, program: WebGLProgram, attribute: string,\n    buffer: WebGLBuffer, arrayEntriesPerItem: number, itemStrideInBytes: number,\n    itemOffsetInBytes: number): boolean {\n  const loc = gl.getAttribLocation(program, attribute);\n  if (loc === -1) {\n    // The GPU compiler decided to strip out this attribute because it's unused,\n    // thus no need to bind.\n    return false;\n  }\n  callAndCheck(gl, () => gl.bindBuffer(gl.ARRAY_BUFFER, buffer));\n  callAndCheck(\n      gl,\n      () => gl.vertexAttribPointer(\n          loc, arrayEntriesPerItem, gl.FLOAT, false, itemStrideInBytes,\n          itemOffsetInBytes));\n  callAndCheck(gl, () => gl.enableVertexAttribArray(loc));\n  return true;\n}\n\nexport function bindTextureUnit(\n    gl: WebGLRenderingContext, texture: WebGLTexture, textureUnit: number) {\n  validateTextureUnit(gl, textureUnit);\n  callAndCheck(gl, () => gl.activeTexture(gl.TEXTURE0 + textureUnit));\n  callAndCheck(gl, () => gl.bindTexture(gl.TEXTURE_2D, texture));\n}\n\nexport function unbindTextureUnit(\n    gl: WebGLRenderingContext, textureUnit: number) {\n  validateTextureUnit(gl, textureUnit);\n  callAndCheck(gl, () => gl.activeTexture(gl.TEXTURE0 + textureUnit));\n  callAndCheck(gl, () => gl.bindTexture(gl.TEXTURE_2D, null));\n}\n\nexport function getProgramUniformLocationOrThrow(\n    gl: WebGLRenderingContext, program: WebGLProgram,\n    uniformName: string): WebGLUniformLocation {\n  return throwIfNull<WebGLUniformLocation>(\n      gl, () => gl.getUniformLocation(program, uniformName),\n      'uniform \"' + uniformName + '\" not present in program.');\n}\n\nexport function getProgramUniformLocation(\n    gl: WebGLRenderingContext, program: WebGLProgram,\n    uniformName: string): WebGLUniformLocation {\n  return gl.getUniformLocation(program, uniformName);\n}\n\nexport function bindTextureToProgramUniformSampler(\n    gl: WebGLRenderingContext, texture: WebGLTexture,\n    uniformSamplerLocation: WebGLUniformLocation, textureUnit: number) {\n  callAndCheck(gl, () => bindTextureUnit(gl, texture, textureUnit));\n  callAndCheck(gl, () => gl.uniform1i(uniformSamplerLocation, textureUnit));\n}\n\nexport function bindCanvasToFramebuffer(gl: WebGLRenderingContext) {\n  callAndCheck(gl, () => gl.bindFramebuffer(gl.FRAMEBUFFER, null));\n  callAndCheck(gl, () => gl.viewport(0, 0, gl.canvas.width, gl.canvas.height));\n  callAndCheck(gl, () => gl.scissor(0, 0, gl.canvas.width, gl.canvas.height));\n}\n\nexport function bindColorTextureToFramebuffer(\n    gl: WebGLRenderingContext, texture: WebGLTexture,\n    framebuffer: WebGLFramebuffer) {\n  callAndCheck(gl, () => gl.bindFramebuffer(gl.FRAMEBUFFER, framebuffer));\n  callAndCheck(\n      gl,\n      () => gl.framebufferTexture2D(\n          gl.FRAMEBUFFER, gl.COLOR_ATTACHMENT0, gl.TEXTURE_2D, texture, 0));\n}\n\nexport function unbindColorTextureFromFramebuffer(\n    gl: WebGLRenderingContext, framebuffer: WebGLFramebuffer) {\n  callAndCheck(gl, () => gl.bindFramebuffer(gl.FRAMEBUFFER, framebuffer));\n  callAndCheck(\n      gl,\n      () => gl.framebufferTexture2D(\n          gl.FRAMEBUFFER, gl.COLOR_ATTACHMENT0, gl.TEXTURE_2D, null, 0));\n}\n\nexport function validateFramebuffer(gl: WebGLRenderingContext) {\n  const status = gl.checkFramebufferStatus(gl.FRAMEBUFFER);\n  if (status !== gl.FRAMEBUFFER_COMPLETE) {\n    throw new Error(\n        'Error binding framebuffer: ' + getFramebufferErrorMessage(gl, status));\n  }\n}\n\nexport function getFramebufferErrorMessage(\n    gl: WebGLRenderingContext, status: number): string {\n  switch (status) {\n    case gl.FRAMEBUFFER_INCOMPLETE_ATTACHMENT:\n      return 'FRAMEBUFFER_INCOMPLETE_ATTACHMENT';\n    case gl.FRAMEBUFFER_INCOMPLETE_MISSING_ATTACHMENT:\n      return 'FRAMEBUFFER_INCOMPLETE_MISSING_ATTACHMENT';\n    case gl.FRAMEBUFFER_INCOMPLETE_DIMENSIONS:\n      return 'FRAMEBUFFER_INCOMPLETE_DIMENSIONS';\n    case gl.FRAMEBUFFER_UNSUPPORTED:\n      return 'FRAMEBUFFER_UNSUPPORTED';\n    default:\n      return `unknown error ${status}`;\n  }\n}\n\nfunction throwIfNull<T>(\n    gl: WebGLRenderingContext, returnTOrNull: () => T | null,\n    failureMessage: string): T {\n  const tOrNull: T|null = callAndCheck(gl, () => returnTOrNull());\n  if (tOrNull == null) {\n    throw new Error(failureMessage);\n  }\n  return tOrNull;\n}\n\nfunction validateTextureUnit(gl: WebGLRenderingContext, textureUnit: number) {\n  const maxTextureUnit = gl.MAX_COMBINED_TEXTURE_IMAGE_UNITS - 1;\n  const glTextureUnit = textureUnit + gl.TEXTURE0;\n  if (glTextureUnit < gl.TEXTURE0 || glTextureUnit > maxTextureUnit) {\n    const textureUnitRange = `[gl.TEXTURE0, gl.TEXTURE${maxTextureUnit}]`;\n    throw new Error(`textureUnit must be in ${textureUnitRange}.`);\n  }\n}\n\nexport function getBatchDim(shape: number[], dimsToSkip = 2): number {\n  return util.sizeFromShape(shape.slice(0, shape.length - dimsToSkip));\n}\n\nexport function getRowsCols(shape: number[]): [number, number] {\n  if (shape.length === 0) {\n    throw Error('Cannot get rows and columns of an empty shape array.');\n  }\n\n  return [\n    shape.length > 1 ? shape[shape.length - 2] : 1, shape[shape.length - 1]\n  ];\n}\n\nexport function getShapeAs3D(shape: number[]): [number, number, number] {\n  let shapeAs3D: [number, number, number] = [1, 1, 1];\n  const isScalar = shape.length === 0 || (shape.length === 1 && shape[0] === 1);\n  if (!isScalar) {\n    shapeAs3D =\n        [getBatchDim(shape), ...getRowsCols(shape)] as [number, number, number];\n  }\n  return shapeAs3D;\n}\n\nexport function getTextureShapeFromLogicalShape(\n    logShape: number[], isPacked = false): [number, number] {\n  let maxTexSize = env().getNumber('WEBGL_MAX_TEXTURE_SIZE');\n  let maxSizeForNarrowTex =\n      env().getNumber('WEBGL_MAX_SIZE_FOR_NARROW_TEXTURE');\n  if (maxSizeForNarrowTex === Infinity &&\n      env().getBool('WEBGL_AUTO_SQUARIFY_NARROW_TEXTURE_SHAPE')) {\n    maxSizeForNarrowTex = maxTexSize / 2;\n  }\n\n  if (isPacked) {\n    maxTexSize = maxTexSize * 2;\n    maxSizeForNarrowTex = maxSizeForNarrowTex * 2;\n\n    // This logic ensures we accurately count the number of packed texels needed\n    // to accommodate the tensor. We can only pack values in the same texel if\n    // they are from adjacent pairs of rows/cols within the same batch. So if a\n    // tensor has 3 rows, we pretend it has 4 rows in order to account for the\n    // fact that the texels containing the third row are half empty.\n    logShape = logShape.map(\n        (d, i) => i >= logShape.length - 2 ?\n            util.nearestLargerEven(logShape[i]) :\n            logShape[i]);\n\n    // Packed texture height is at least 2 (the channel height of a single\n    // texel).\n    if (logShape.length === 1) {\n      logShape = [2, logShape[0]];\n    }\n  }\n\n  // If logical shape is 2, we don't squeeze, since we want to match physical.\n  if (logShape.length !== 2) {\n    const squeezeResult = util.squeezeShape(logShape);\n    logShape = squeezeResult.newShape;\n  }\n\n  let size = util.sizeFromShape(logShape);\n  let textureShape: [number, number] = null;\n  if (logShape.length <= 1 && size <= maxTexSize) {\n    textureShape = [1, size];\n  } else if (\n      logShape.length === 2 && logShape[0] <= maxTexSize &&\n      logShape[1] <= maxTexSize) {\n    textureShape = logShape as [number, number];\n  } else if (\n      logShape.length === 3 && logShape[0] * logShape[1] <= maxTexSize &&\n      logShape[2] <= maxTexSize) {\n    textureShape = [logShape[0] * logShape[1], logShape[2]];\n  } else if (\n      logShape.length === 3 && logShape[0] <= maxTexSize &&\n      logShape[1] * logShape[2] <= maxTexSize) {\n    textureShape = [logShape[0], logShape[1] * logShape[2]];\n  } else if (\n      logShape.length === 4 &&\n      logShape[0] * logShape[1] * logShape[2] <= maxTexSize &&\n      logShape[3] <= maxTexSize) {\n    textureShape = [logShape[0] * logShape[1] * logShape[2], logShape[3]];\n  } else if (\n      logShape.length === 4 && logShape[0] <= maxTexSize &&\n      logShape[1] * logShape[2] * logShape[3] <= maxTexSize) {\n    textureShape = [logShape[0], logShape[1] * logShape[2] * logShape[3]];\n  }\n\n  // true if one edge length is 1 (1 or 2, if packed), while another edge\n  // length exceeds maxSizeForNarrowTex.\n  const isLongNarrowTex = textureShape != null &&\n      Math.max(...textureShape) > maxSizeForNarrowTex &&\n      Math.min(...textureShape) <= (isPacked ? 2 : 1) &&\n      Math.min(...textureShape) > 0;\n\n  if (textureShape == null || isLongNarrowTex) {\n    if (isPacked) {\n      // For packed textures size equals the number of channels required to\n      // accommodate the texture data. However in order to squarify such that\n      // inner dimensions stay even, we rewrite size to equal the number of\n      // texels. Then in the return statement we rehydrate the squarified\n      // dimensions to channel units.\n\n      const batchDim = getBatchDim(logShape);\n      let rows = 2, cols = 2;\n      if (logShape.length) {\n        [rows, cols] = getRowsCols(logShape);\n      }\n      size = batchDim * (rows / 2) * (cols / 2);\n      textureShape =\n          util.sizeToSquarishShape(size).map(d => d * 2) as [number, number];\n    } else {\n      textureShape = util.sizeToSquarishShape(size);\n    }\n  }\n\n  return textureShape;\n}\n\nfunction isEven(n: number): boolean {\n  return n % 2 === 0;\n}\n\n/**\n * This determines whether reshaping a packed texture requires rearranging\n * the data within the texture, assuming 2x2 packing.\n */\nexport function isReshapeFree(shape1: number[], shape2: number[]): boolean {\n  shape1 = shape1.slice(-2);\n  shape2 = shape2.slice(-2);\n\n  if (util.arraysEqual(shape1, shape2)) {\n    return true;\n  }\n\n  if (!shape1.length || !shape2.length) {  // One of the shapes is a scalar.\n    return true;\n  }\n\n  if (shape1[0] === 0 || shape1[1] === 0 || shape2[0] === 0 ||\n      shape2[1] === 0) {\n    return true;\n  }\n\n  if (shape1.length !== shape2.length) {  // One of the shapes is a vector.\n    const shape1Cols = shape1[shape1.length - 1];\n    const shape2Cols = shape2[shape2.length - 1];\n    if (shape1Cols === shape2Cols) {\n      return true;\n    }\n\n    if (isEven(shape1Cols) && isEven(shape2Cols) &&\n        (shape1[0] === 1 || shape2[0] === 1)) {\n      return true;\n    }\n  }\n  return shape1[1] === shape2[1] && isEven(shape1[0]) && isEven(shape2[0]);\n}\n\n// We cache webgl params because the environment gets reset between\n// unit tests and we don't want to constantly query the WebGLContext for\n// MAX_TEXTURE_SIZE.\nlet MAX_TEXTURE_SIZE: number;\nlet MAX_TEXTURES_IN_SHADER: number;\n\nexport function getWebGLMaxTextureSize(webGLVersion: number): number {\n  if (MAX_TEXTURE_SIZE == null) {\n    const gl = getWebGLContext(webGLVersion);\n    MAX_TEXTURE_SIZE = gl.getParameter(gl.MAX_TEXTURE_SIZE);\n  }\n  return MAX_TEXTURE_SIZE;\n}\n\nexport function resetMaxTextureSize() {\n  MAX_TEXTURE_SIZE = null;\n}\nexport function resetMaxTexturesInShader() {\n  MAX_TEXTURES_IN_SHADER = null;\n}\n\nexport function getMaxTexturesInShader(webGLVersion: number): number {\n  if (MAX_TEXTURES_IN_SHADER == null) {\n    const gl = getWebGLContext(webGLVersion);\n    MAX_TEXTURES_IN_SHADER = gl.getParameter(gl.MAX_TEXTURE_IMAGE_UNITS);\n  }\n  // We cap at 16 to avoid spurious runtime \"memory exhausted\" error.\n  return Math.min(16, MAX_TEXTURES_IN_SHADER);\n}\n\nexport function getWebGLDisjointQueryTimerVersion(webGLVersion: number):\n    number {\n  if (webGLVersion === 0) {\n    return 0;\n  }\n\n  let queryTimerVersion: number;\n  const gl = getWebGLContext(webGLVersion);\n\n  if (hasExtension(gl, 'EXT_disjoint_timer_query_webgl2') &&\n      webGLVersion === 2) {\n    queryTimerVersion = 2;\n  } else if (hasExtension(gl, 'EXT_disjoint_timer_query')) {\n    queryTimerVersion = 1;\n  } else {\n    queryTimerVersion = 0;\n  }\n  return queryTimerVersion;\n}\n\nexport function hasExtension(gl: WebGLRenderingContext, extensionName: string) {\n  const ext = gl.getExtension(extensionName);\n  return ext != null;\n}\n\nexport function isWebGLVersionEnabled(webGLVersion: 1|2) {\n  try {\n    const gl = getWebGLContext(webGLVersion);\n    if (gl != null) {\n      return true;\n    }\n  } catch (e) {\n    console.log('Error when getting WebGL context: ', e);\n    return false;\n  }\n  return false;\n}\n\nexport function isCapableOfRenderingToFloatTexture(webGLVersion: number):\n    boolean {\n  if (webGLVersion === 0) {\n    return false;\n  }\n\n  const gl = getWebGLContext(webGLVersion);\n\n  if (webGLVersion === 1) {\n    if (!hasExtension(gl, 'OES_texture_float')) {\n      return false;\n    }\n  } else {\n    if (!hasExtension(gl, 'EXT_color_buffer_float')) {\n      return false;\n    }\n  }\n\n  const isFrameBufferComplete = createFloatTextureAndBindToFramebuffer(gl);\n  return isFrameBufferComplete;\n}\n\n/**\n * Check if we can download values from a float/half-float texture.\n *\n * Note that for performance reasons we use binding a texture to a framebuffer\n * as a proxy for ability to download float values later using readPixels. The\n * texture params of this texture will not match those in readPixels exactly\n * but if we are unable to bind some kind of float texture to the frameBuffer\n * then we definitely will not be able to read float values from it.\n */\nexport function isDownloadFloatTextureEnabled(webGLVersion: number): boolean {\n  if (webGLVersion === 0) {\n    return false;\n  }\n\n  const gl = getWebGLContext(webGLVersion);\n\n  if (webGLVersion === 1) {\n    if (!hasExtension(gl, 'OES_texture_float')) {\n      return false;\n    }\n    if (!hasExtension(gl, 'WEBGL_color_buffer_float')) {\n      return false;\n    }\n  } else {\n    if (hasExtension(gl, 'EXT_color_buffer_float')) {\n      return createFloatTextureAndBindToFramebuffer(gl);\n    }\n\n    const COLOR_BUFFER_HALF_FLOAT = 'EXT_color_buffer_half_float';\n    if (hasExtension(gl, COLOR_BUFFER_HALF_FLOAT)) {\n      const textureHalfFloatExtension =\n          gl.getExtension(COLOR_BUFFER_HALF_FLOAT);\n      return createHalfFloatTextureAndBindToFramebuffer(\n          gl, textureHalfFloatExtension);\n    }\n\n    return false;\n  }\n\n  const isFrameBufferComplete = createFloatTextureAndBindToFramebuffer(gl);\n  return isFrameBufferComplete;\n}\n\nfunction createFloatTextureAndBindToFramebuffer(gl: WebGLRenderingContext):\n    boolean {\n  const texConfig = getTextureConfig(gl);\n\n  const texture = gl.createTexture();\n  gl.bindTexture(gl.TEXTURE_2D, texture);\n\n  const width = 1;\n  const height = 1;\n  gl.texImage2D(\n      gl.TEXTURE_2D, 0, texConfig.internalFormatFloat, width, height, 0,\n      texConfig.textureFormatFloat, texConfig.textureTypeFloat, null);\n\n  const frameBuffer = gl.createFramebuffer();\n  gl.bindFramebuffer(gl.FRAMEBUFFER, frameBuffer);\n  gl.framebufferTexture2D(\n      gl.FRAMEBUFFER, gl.COLOR_ATTACHMENT0, gl.TEXTURE_2D, texture, 0);\n\n  const isFrameBufferComplete =\n      gl.checkFramebufferStatus(gl.FRAMEBUFFER) === gl.FRAMEBUFFER_COMPLETE;\n\n  gl.bindTexture(gl.TEXTURE_2D, null);\n  gl.bindFramebuffer(gl.FRAMEBUFFER, null);\n  gl.deleteTexture(texture);\n  gl.deleteFramebuffer(frameBuffer);\n\n  return isFrameBufferComplete;\n}\n\nfunction createHalfFloatTextureAndBindToFramebuffer(\n    // tslint:disable-next-line:no-any\n    gl: WebGLRenderingContext, textureHalfFloatExtension: any): boolean {\n  const texConfig = getTextureConfig(gl, textureHalfFloatExtension);\n  const texture = gl.createTexture();\n  gl.bindTexture(gl.TEXTURE_2D, texture);\n\n  const width = 1;\n  const height = 1;\n  gl.texImage2D(\n      gl.TEXTURE_2D, 0, texConfig.internalFormatHalfFloat, width, height, 0,\n      texConfig.textureFormatFloat, texConfig.textureTypeHalfFloat, null);\n\n  const frameBuffer = gl.createFramebuffer();\n  gl.bindFramebuffer(gl.FRAMEBUFFER, frameBuffer);\n  gl.framebufferTexture2D(\n      gl.FRAMEBUFFER, gl.COLOR_ATTACHMENT0, gl.TEXTURE_2D, texture, 0);\n\n  const isFrameBufferComplete =\n      gl.checkFramebufferStatus(gl.FRAMEBUFFER) === gl.FRAMEBUFFER_COMPLETE;\n\n  gl.bindTexture(gl.TEXTURE_2D, null);\n  gl.bindFramebuffer(gl.FRAMEBUFFER, null);\n  gl.deleteTexture(texture);\n  gl.deleteFramebuffer(frameBuffer);\n\n  return isFrameBufferComplete;\n}\n\nexport function isWebGLFenceEnabled(webGLVersion: number) {\n  if (webGLVersion !== 2) {\n    return false;\n  }\n  const gl = getWebGLContext(webGLVersion);\n\n  // tslint:disable-next-line:no-any\n  const isEnabled = (gl as any).fenceSync != null;\n  return isEnabled;\n}\n\nexport function assertNotComplex(\n    tensor: TensorInfo|TensorInfo[], opName: string): void {\n  if (!Array.isArray(tensor)) {\n    tensor = [tensor];\n  }\n  tensor.forEach(t => {\n    if (t != null) {\n      util.assert(\n          t.dtype !== 'complex64',\n          () => `${opName} does not support complex64 tensors ` +\n              'in the WebGL backend.');\n    }\n  });\n}\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAiBA,SAAQA,GAAG,EAAcC,IAAI,QAAO,uBAAuB;AAE3D,SAAQC,eAAe,QAAO,eAAe;AAC7C,SAAQC,gBAAgB,QAAO,YAAY;AAE3C,OAAM,SAAUC,YAAYA,CAAIC,EAAyB,EAAEC,IAAa;EACtE,MAAMC,WAAW,GAAGD,IAAI,EAAE;EAC1B,IAAIN,GAAG,EAAE,CAACQ,OAAO,CAAC,OAAO,CAAC,EAAE;IAC1BC,eAAe,CAACJ,EAAE,CAAC;;EAErB,OAAOE,WAAW;AACpB;AAEA,SAASE,eAAeA,CAACJ,EAAyB;EAChD,MAAMK,KAAK,GAAGL,EAAE,CAACM,QAAQ,EAAE;EAC3B,IAAID,KAAK,KAAKL,EAAE,CAACO,QAAQ,EAAE;IACzB,MAAM,IAAIC,KAAK,CAAC,eAAe,GAAGC,oBAAoB,CAACT,EAAE,EAAEK,KAAK,CAAC,CAAC;;AAEtE;AAEA;AACA,MAAMK,WAAW,GAAG,OAAO;AAC3B,MAAMC,WAAW,GAAG,KAAK;AAEzB,OAAM,SAAUC,gBAAgBA,CAACC,GAAW;EAC1C,IAAIlB,GAAG,EAAE,CAACQ,OAAO,CAAC,8BAA8B,CAAC,IAAIU,GAAG,KAAK,CAAC,IACzDH,WAAW,GAAGI,IAAI,CAACC,GAAG,CAACF,GAAG,CAAC,IAAIC,IAAI,CAACC,GAAG,CAACF,GAAG,CAAC,GAAGF,WAAY,EAAE;IAChE,OAAO,IAAI;;EAEb,OAAO,KAAK;AACd;AAEA,OAAM,SAAUF,oBAAoBA,CAChCT,EAAyB,EAAEgB,MAAc;EAC3C,QAAQA,MAAM;IACZ,KAAKhB,EAAE,CAACO,QAAQ;MACd,OAAO,UAAU;IACnB,KAAKP,EAAE,CAACiB,YAAY;MAClB,OAAO,cAAc;IACvB,KAAKjB,EAAE,CAACkB,aAAa;MACnB,OAAO,eAAe;IACxB,KAAKlB,EAAE,CAACmB,iBAAiB;MACvB,OAAO,mBAAmB;IAC5B,KAAKnB,EAAE,CAACoB,6BAA6B;MACnC,OAAO,+BAA+B;IACxC,KAAKpB,EAAE,CAACqB,aAAa;MACnB,OAAO,eAAe;IACxB,KAAKrB,EAAE,CAACsB,kBAAkB;MACxB,OAAO,oBAAoB;IAC7B;MACE,OAAO,sBAAsBN,MAAM,EAAE;;AAE3C;AAEA,OAAM,SAAUO,mBAAmBA,CAC/BvB,EAAyB,EAAEwB,aAAqB;EAClD,OAAOC,WAAW,CACdzB,EAAE,EAAE,MAAMA,EAAE,CAAC0B,YAAY,CAACF,aAAa,CAAC,EACxC,aAAa,GAAGA,aAAa,GAAG,kCAAkC,CAAC;AACzE;AAEA,OAAM,SAAUG,kBAAkBA,CAC9B3B,EAAyB,EAAE4B,kBAA0B;EACvD,MAAMC,YAAY,GAAgBJ,WAAW,CACzCzB,EAAE,EAAE,MAAMA,EAAE,CAAC8B,YAAY,CAAC9B,EAAE,CAAC+B,aAAa,CAAC,EAC3C,sCAAsC,CAAC;EAC3ChC,YAAY,CAACC,EAAE,EAAE,MAAMA,EAAE,CAACgC,YAAY,CAACH,YAAY,EAAED,kBAAkB,CAAC,CAAC;EACzE7B,YAAY,CAACC,EAAE,EAAE,MAAMA,EAAE,CAACiC,aAAa,CAACJ,YAAY,CAAC,CAAC;EACtD,IAAI7B,EAAE,CAACkC,kBAAkB,CAACL,YAAY,EAAE7B,EAAE,CAACmC,cAAc,CAAC,KAAK,KAAK,EAAE;IACpEC,OAAO,CAACC,GAAG,CAACrC,EAAE,CAACsC,gBAAgB,CAACT,YAAY,CAAC,CAAC;IAC9C,MAAM,IAAIrB,KAAK,CAAC,kCAAkC,CAAC;;EAErD,OAAOqB,YAAY;AACrB;AAEA,OAAM,SAAUU,oBAAoBA,CAChCvC,EAAyB,EAAEwC,oBAA4B;EACzD,MAAMC,cAAc,GAAgBhB,WAAW,CAC3CzB,EAAE,EAAE,MAAMA,EAAE,CAAC8B,YAAY,CAAC9B,EAAE,CAAC0C,eAAe,CAAC,EAC7C,wCAAwC,CAAC;EAC7C3C,YAAY,CAACC,EAAE,EAAE,MAAMA,EAAE,CAACgC,YAAY,CAACS,cAAc,EAAED,oBAAoB,CAAC,CAAC;EAC7EzC,YAAY,CAACC,EAAE,EAAE,MAAMA,EAAE,CAACiC,aAAa,CAACQ,cAAc,CAAC,CAAC;EACxD,IAAI9C,GAAG,EAAE,CAACgD,GAAG,CAAC,qBAAqB,CAAC,EAAE;IACpC,OAAOF,cAAc;;EAEvB,IAAIzC,EAAE,CAACkC,kBAAkB,CAACO,cAAc,EAAEzC,EAAE,CAACmC,cAAc,CAAC,KAAK,KAAK,EAAE;IACtES,yBAAyB,CACrBJ,oBAAoB,EAAExC,EAAE,CAACsC,gBAAgB,CAACG,cAAc,CAAC,CAAC;IAC9D,MAAM,IAAIjC,KAAK,CAAC,oCAAoC,CAAC;;EAEvD,OAAOiC,cAAc;AACvB;AAEA,MAAMI,eAAe,GAAG,0BAA0B;AAClD,OAAM,SAAUD,yBAAyBA,CACrCZ,YAAoB,EAAEc,aAAqB;EAC7C,MAAMC,qBAAqB,GAAGF,eAAe,CAACG,IAAI,CAACF,aAAa,CAAC;EACjE,IAAIC,qBAAqB,IAAI,IAAI,EAAE;IACjCX,OAAO,CAACC,GAAG,CAAC,wCAAwCS,aAAa,EAAE,CAAC;IACpEV,OAAO,CAACC,GAAG,CAACL,YAAY,CAAC;IACzB;;EAGF,MAAMiB,UAAU,GAAG,CAACF,qBAAqB,CAAC,CAAC,CAAC;EAE5C,MAAMG,WAAW,GAAGlB,YAAY,CAACmB,KAAK,CAAC,IAAI,CAAC;EAC5C,MAAMC,GAAG,GAAGF,WAAW,CAACG,MAAM,CAACC,QAAQ,EAAE,CAACD,MAAM,GAAG,CAAC;EACpD,MAAME,oBAAoB,GAAGL,WAAW,CAACM,GAAG,CACxC,CAACC,IAAI,EAAER,UAAU,KACbrD,IAAI,CAAC8D,QAAQ,CAAC,CAACT,UAAU,GAAG,CAAC,EAAEK,QAAQ,EAAE,EAAEF,GAAG,CAAC,GAAGK,IAAI,CAAC;EAC/D,IAAIE,aAAa,GAAG,CAAC;EACrB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGL,oBAAoB,CAACF,MAAM,EAAEO,CAAC,EAAE,EAAE;IACpDD,aAAa,GAAG7C,IAAI,CAAC+C,GAAG,CAACN,oBAAoB,CAACK,CAAC,CAAC,CAACP,MAAM,EAAEM,aAAa,CAAC;;EAGzE,MAAMG,gBAAgB,GAAGP,oBAAoB,CAACQ,KAAK,CAAC,CAAC,EAAEd,UAAU,GAAG,CAAC,CAAC;EACtE,MAAMe,SAAS,GAAGT,oBAAoB,CAACQ,KAAK,CAACd,UAAU,GAAG,CAAC,EAAEA,UAAU,CAAC;EACxE,MAAMgB,eAAe,GAAGV,oBAAoB,CAACQ,KAAK,CAACd,UAAU,CAAC;EAE9Db,OAAO,CAACC,GAAG,CAACyB,gBAAgB,CAACI,IAAI,CAAC,IAAI,CAAC,CAAC;EACxC9B,OAAO,CAACC,GAAG,CAACS,aAAa,CAACK,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;EACzCf,OAAO,CAACC,GAAG,CACP,MAAMzC,IAAI,CAAC8D,QAAQ,CAACM,SAAS,CAAC,CAAC,CAAC,EAAEL,aAAa,CAAC,EAAE,EAClD,+DAA+D,CAAC;EACpEvB,OAAO,CAACC,GAAG,CAAC4B,eAAe,CAACC,IAAI,CAAC,IAAI,CAAC,CAAC;AACzC;AAEA,OAAM,SAAUC,aAAaA,CAACnE,EAAyB;EACrD,OAAOyB,WAAW,CACdzB,EAAE,EAAE,MAAMA,EAAE,CAACmE,aAAa,EAAE,EAAE,gCAAgC,CAAC;AACrE;AAEA,OAAM,SAAUC,WAAWA,CAACpE,EAAyB,EAAEqE,OAAqB;EAC1EtE,YAAY,CAACC,EAAE,EAAE,MAAMA,EAAE,CAACoE,WAAW,CAACC,OAAO,CAAC,CAAC;EAC/C,IAAI1E,GAAG,EAAE,CAACgD,GAAG,CAAC,qBAAqB,CAAC,EAAE;IACpC;;EAEF,IAAI3C,EAAE,CAACsE,mBAAmB,CAACD,OAAO,EAAErE,EAAE,CAACuE,WAAW,CAAC,KAAK,KAAK,EAAE;IAC7DnC,OAAO,CAACC,GAAG,CAACrC,EAAE,CAACwE,iBAAiB,CAACH,OAAO,CAAC,CAAC;IAC1C,MAAM,IAAI7D,KAAK,CAAC,6CAA6C,CAAC;;AAElE;AAEA;AACA;AACA;AACA;AACA;AACA,OAAM,SAAUiE,eAAeA,CAC3BzE,EAAyB,EAAEqE,OAAqB;EAClDtE,YAAY,CAACC,EAAE,EAAE,MAAMA,EAAE,CAACyE,eAAe,CAACJ,OAAO,CAAC,CAAC;EACnD,IAAIrE,EAAE,CAACsE,mBAAmB,CAACD,OAAO,EAAErE,EAAE,CAAC0E,eAAe,CAAC,KAAK,KAAK,EAAE;IACjEtC,OAAO,CAACC,GAAG,CAACrC,EAAE,CAACwE,iBAAiB,CAACH,OAAO,CAAC,CAAC;IAC1C,MAAM,IAAI7D,KAAK,CAAC,mCAAmC,CAAC;;AAExD;AAEA,OAAM,SAAUmE,wBAAwBA,CACpC3E,EAAyB,EAAE4E,IAAkB;EAC/C,MAAMC,MAAM,GAAgBpD,WAAW,CACnCzB,EAAE,EAAE,MAAMA,EAAE,CAAC8E,YAAY,EAAE,EAAE,8BAA8B,CAAC;EAChE/E,YAAY,CAACC,EAAE,EAAE,MAAMA,EAAE,CAAC+E,UAAU,CAAC/E,EAAE,CAACgF,YAAY,EAAEH,MAAM,CAAC,CAAC;EAC9D9E,YAAY,CAACC,EAAE,EAAE,MAAMA,EAAE,CAACiF,UAAU,CAACjF,EAAE,CAACgF,YAAY,EAAEJ,IAAI,EAAE5E,EAAE,CAACkF,WAAW,CAAC,CAAC;EAC5E,OAAOL,MAAM;AACf;AAEA,OAAM,SAAUM,uBAAuBA,CACnCnF,EAAyB,EAAE4E,IAAiB;EAC9C,MAAMC,MAAM,GAAgBpD,WAAW,CACnCzB,EAAE,EAAE,MAAMA,EAAE,CAAC8E,YAAY,EAAE,EAAE,8BAA8B,CAAC;EAChE/E,YAAY,CAACC,EAAE,EAAE,MAAMA,EAAE,CAAC+E,UAAU,CAAC/E,EAAE,CAACoF,oBAAoB,EAAEP,MAAM,CAAC,CAAC;EACtE9E,YAAY,CACRC,EAAE,EAAE,MAAMA,EAAE,CAACiF,UAAU,CAACjF,EAAE,CAACoF,oBAAoB,EAAER,IAAI,EAAE5E,EAAE,CAACkF,WAAW,CAAC,CAAC;EAC3E,OAAOL,MAAM;AACf;AAEA,OAAM,SAAUQ,cAAcA,CAAA;EAC5B,IAAI1F,GAAG,EAAE,CAAC2F,SAAS,CAAC,eAAe,CAAC,KAAK,CAAC,EAAE;IAC1C,OAAO,CAAC;;EAEV,OAAO,CAAC;AACV;AAEA,OAAM,SAAUC,aAAaA,CAACvF,EAAyB;EACrD,OAAOyB,WAAW,CACdzB,EAAE,EAAE,MAAMA,EAAE,CAACuF,aAAa,EAAE,EAAE,gCAAgC,CAAC;AACrE;AAEA,OAAM,SAAUC,mBAAmBA,CAACC,KAAa,EAAEC,MAAc;EAC/D,MAAMC,cAAc,GAAGhG,GAAG,EAAE,CAAC2F,SAAS,CAAC,wBAAwB,CAAC;EAChE,IAAKG,KAAK,IAAI,CAAC,IAAMC,MAAM,IAAI,CAAE,EAAE;IACjC,MAAME,SAAS,GAAG,IAAIH,KAAK,IAAIC,MAAM,GAAG;IACxC,MAAM,IAAIlF,KAAK,CAAC,yBAAyB,GAAGoF,SAAS,GAAG,cAAc,CAAC;;EAEzE,IAAKH,KAAK,GAAGE,cAAc,IAAMD,MAAM,GAAGC,cAAe,EAAE;IACzD,MAAMC,SAAS,GAAG,IAAIH,KAAK,IAAIC,MAAM,GAAG;IACxC,MAAM7B,GAAG,GAAG,IAAI8B,cAAc,IAAIA,cAAc,GAAG;IACnD,MAAM,IAAInF,KAAK,CACX,yBAAyB,GAAGoF,SAAS,GACrC,oDAAoD,GAAG/B,GAAG,GAAG,GAAG,CAAC;;AAEzE;AAEA,OAAM,SAAUgC,iBAAiBA,CAAC7F,EAAyB;EACzD,OAAOyB,WAAW,CACdzB,EAAE,EAAE,MAAMA,EAAE,CAAC6F,iBAAiB,EAAE,EAAE,oCAAoC,CAAC;AAC7E;AAEA,OAAM,SAAUC,kCAAkCA,CAC9C9F,EAAyB,EAAEqE,OAAqB,EAAE0B,SAAiB,EACnElB,MAAmB,EAAEmB,mBAA2B,EAAEC,iBAAyB,EAC3EC,iBAAyB;EAC3B,MAAMC,GAAG,GAAGnG,EAAE,CAACoG,iBAAiB,CAAC/B,OAAO,EAAE0B,SAAS,CAAC;EACpD,IAAII,GAAG,KAAK,CAAC,CAAC,EAAE;IACd;IACA;IACA,OAAO,KAAK;;EAEdpG,YAAY,CAACC,EAAE,EAAE,MAAMA,EAAE,CAAC+E,UAAU,CAAC/E,EAAE,CAACgF,YAAY,EAAEH,MAAM,CAAC,CAAC;EAC9D9E,YAAY,CACRC,EAAE,EACF,MAAMA,EAAE,CAACqG,mBAAmB,CACxBF,GAAG,EAAEH,mBAAmB,EAAEhG,EAAE,CAACsG,KAAK,EAAE,KAAK,EAAEL,iBAAiB,EAC5DC,iBAAiB,CAAC,CAAC;EAC3BnG,YAAY,CAACC,EAAE,EAAE,MAAMA,EAAE,CAACuG,uBAAuB,CAACJ,GAAG,CAAC,CAAC;EACvD,OAAO,IAAI;AACb;AAEA,OAAM,SAAUK,eAAeA,CAC3BxG,EAAyB,EAAEyG,OAAqB,EAAEC,WAAmB;EACvEC,mBAAmB,CAAC3G,EAAE,EAAE0G,WAAW,CAAC;EACpC3G,YAAY,CAACC,EAAE,EAAE,MAAMA,EAAE,CAAC4G,aAAa,CAAC5G,EAAE,CAAC6G,QAAQ,GAAGH,WAAW,CAAC,CAAC;EACnE3G,YAAY,CAACC,EAAE,EAAE,MAAMA,EAAE,CAAC8G,WAAW,CAAC9G,EAAE,CAAC+G,UAAU,EAAEN,OAAO,CAAC,CAAC;AAChE;AAEA,OAAM,SAAUO,iBAAiBA,CAC7BhH,EAAyB,EAAE0G,WAAmB;EAChDC,mBAAmB,CAAC3G,EAAE,EAAE0G,WAAW,CAAC;EACpC3G,YAAY,CAACC,EAAE,EAAE,MAAMA,EAAE,CAAC4G,aAAa,CAAC5G,EAAE,CAAC6G,QAAQ,GAAGH,WAAW,CAAC,CAAC;EACnE3G,YAAY,CAACC,EAAE,EAAE,MAAMA,EAAE,CAAC8G,WAAW,CAAC9G,EAAE,CAAC+G,UAAU,EAAE,IAAI,CAAC,CAAC;AAC7D;AAEA,OAAM,SAAUE,gCAAgCA,CAC5CjH,EAAyB,EAAEqE,OAAqB,EAChD6C,WAAmB;EACrB,OAAOzF,WAAW,CACdzB,EAAE,EAAE,MAAMA,EAAE,CAACmH,kBAAkB,CAAC9C,OAAO,EAAE6C,WAAW,CAAC,EACrD,WAAW,GAAGA,WAAW,GAAG,2BAA2B,CAAC;AAC9D;AAEA,OAAM,SAAUE,yBAAyBA,CACrCpH,EAAyB,EAAEqE,OAAqB,EAChD6C,WAAmB;EACrB,OAAOlH,EAAE,CAACmH,kBAAkB,CAAC9C,OAAO,EAAE6C,WAAW,CAAC;AACpD;AAEA,OAAM,SAAUG,kCAAkCA,CAC9CrH,EAAyB,EAAEyG,OAAqB,EAChDa,sBAA4C,EAAEZ,WAAmB;EACnE3G,YAAY,CAACC,EAAE,EAAE,MAAMwG,eAAe,CAACxG,EAAE,EAAEyG,OAAO,EAAEC,WAAW,CAAC,CAAC;EACjE3G,YAAY,CAACC,EAAE,EAAE,MAAMA,EAAE,CAACuH,SAAS,CAACD,sBAAsB,EAAEZ,WAAW,CAAC,CAAC;AAC3E;AAEA,OAAM,SAAUc,uBAAuBA,CAACxH,EAAyB;EAC/DD,YAAY,CAACC,EAAE,EAAE,MAAMA,EAAE,CAACyH,eAAe,CAACzH,EAAE,CAAC0H,WAAW,EAAE,IAAI,CAAC,CAAC;EAChE3H,YAAY,CAACC,EAAE,EAAE,MAAMA,EAAE,CAAC2H,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE3H,EAAE,CAAC4H,MAAM,CAACnC,KAAK,EAAEzF,EAAE,CAAC4H,MAAM,CAAClC,MAAM,CAAC,CAAC;EAC5E3F,YAAY,CAACC,EAAE,EAAE,MAAMA,EAAE,CAAC6H,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE7H,EAAE,CAAC4H,MAAM,CAACnC,KAAK,EAAEzF,EAAE,CAAC4H,MAAM,CAAClC,MAAM,CAAC,CAAC;AAC7E;AAEA,OAAM,SAAUoC,6BAA6BA,CACzC9H,EAAyB,EAAEyG,OAAqB,EAChDsB,WAA6B;EAC/BhI,YAAY,CAACC,EAAE,EAAE,MAAMA,EAAE,CAACyH,eAAe,CAACzH,EAAE,CAAC0H,WAAW,EAAEK,WAAW,CAAC,CAAC;EACvEhI,YAAY,CACRC,EAAE,EACF,MAAMA,EAAE,CAACgI,oBAAoB,CACzBhI,EAAE,CAAC0H,WAAW,EAAE1H,EAAE,CAACiI,iBAAiB,EAAEjI,EAAE,CAAC+G,UAAU,EAAEN,OAAO,EAAE,CAAC,CAAC,CAAC;AAC3E;AAEA,OAAM,SAAUyB,iCAAiCA,CAC7ClI,EAAyB,EAAE+H,WAA6B;EAC1DhI,YAAY,CAACC,EAAE,EAAE,MAAMA,EAAE,CAACyH,eAAe,CAACzH,EAAE,CAAC0H,WAAW,EAAEK,WAAW,CAAC,CAAC;EACvEhI,YAAY,CACRC,EAAE,EACF,MAAMA,EAAE,CAACgI,oBAAoB,CACzBhI,EAAE,CAAC0H,WAAW,EAAE1H,EAAE,CAACiI,iBAAiB,EAAEjI,EAAE,CAAC+G,UAAU,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;AACxE;AAEA,OAAM,SAAUoB,mBAAmBA,CAACnI,EAAyB;EAC3D,MAAMgB,MAAM,GAAGhB,EAAE,CAACoI,sBAAsB,CAACpI,EAAE,CAAC0H,WAAW,CAAC;EACxD,IAAI1G,MAAM,KAAKhB,EAAE,CAACqI,oBAAoB,EAAE;IACtC,MAAM,IAAI7H,KAAK,CACX,6BAA6B,GAAG8H,0BAA0B,CAACtI,EAAE,EAAEgB,MAAM,CAAC,CAAC;;AAE/E;AAEA,OAAM,SAAUsH,0BAA0BA,CACtCtI,EAAyB,EAAEgB,MAAc;EAC3C,QAAQA,MAAM;IACZ,KAAKhB,EAAE,CAACuI,iCAAiC;MACvC,OAAO,mCAAmC;IAC5C,KAAKvI,EAAE,CAACwI,yCAAyC;MAC/C,OAAO,2CAA2C;IACpD,KAAKxI,EAAE,CAACyI,iCAAiC;MACvC,OAAO,mCAAmC;IAC5C,KAAKzI,EAAE,CAAC0I,uBAAuB;MAC7B,OAAO,yBAAyB;IAClC;MACE,OAAO,iBAAiB1H,MAAM,EAAE;;AAEtC;AAEA,SAASS,WAAWA,CAChBzB,EAAyB,EAAE2I,aAA6B,EACxDC,cAAsB;EACxB,MAAMC,OAAO,GAAW9I,YAAY,CAACC,EAAE,EAAE,MAAM2I,aAAa,EAAE,CAAC;EAC/D,IAAIE,OAAO,IAAI,IAAI,EAAE;IACnB,MAAM,IAAIrI,KAAK,CAACoI,cAAc,CAAC;;EAEjC,OAAOC,OAAO;AAChB;AAEA,SAASlC,mBAAmBA,CAAC3G,EAAyB,EAAE0G,WAAmB;EACzE,MAAMoC,cAAc,GAAG9I,EAAE,CAAC+I,gCAAgC,GAAG,CAAC;EAC9D,MAAMC,aAAa,GAAGtC,WAAW,GAAG1G,EAAE,CAAC6G,QAAQ;EAC/C,IAAImC,aAAa,GAAGhJ,EAAE,CAAC6G,QAAQ,IAAImC,aAAa,GAAGF,cAAc,EAAE;IACjE,MAAMG,gBAAgB,GAAG,2BAA2BH,cAAc,GAAG;IACrE,MAAM,IAAItI,KAAK,CAAC,0BAA0ByI,gBAAgB,GAAG,CAAC;;AAElE;AAEA,OAAM,SAAUC,WAAWA,CAACC,KAAe,EAAEC,UAAU,GAAG,CAAC;EACzD,OAAOxJ,IAAI,CAACyJ,aAAa,CAACF,KAAK,CAACpF,KAAK,CAAC,CAAC,EAAEoF,KAAK,CAAC9F,MAAM,GAAG+F,UAAU,CAAC,CAAC;AACtE;AAEA,OAAM,SAAUE,WAAWA,CAACH,KAAe;EACzC,IAAIA,KAAK,CAAC9F,MAAM,KAAK,CAAC,EAAE;IACtB,MAAM7C,KAAK,CAAC,sDAAsD,CAAC;;EAGrE,OAAO,CACL2I,KAAK,CAAC9F,MAAM,GAAG,CAAC,GAAG8F,KAAK,CAACA,KAAK,CAAC9F,MAAM,GAAG,CAAC,CAAC,GAAG,CAAC,EAAE8F,KAAK,CAACA,KAAK,CAAC9F,MAAM,GAAG,CAAC,CAAC,CACxE;AACH;AAEA,OAAM,SAAUkG,YAAYA,CAACJ,KAAe;EAC1C,IAAIK,SAAS,GAA6B,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;EACnD,MAAMC,QAAQ,GAAGN,KAAK,CAAC9F,MAAM,KAAK,CAAC,IAAK8F,KAAK,CAAC9F,MAAM,KAAK,CAAC,IAAI8F,KAAK,CAAC,CAAC,CAAC,KAAK,CAAE;EAC7E,IAAI,CAACM,QAAQ,EAAE;IACbD,SAAS,GACL,CAACN,WAAW,CAACC,KAAK,CAAC,EAAE,GAAGG,WAAW,CAACH,KAAK,CAAC,CAA6B;;EAE7E,OAAOK,SAAS;AAClB;AAEA,OAAM,SAAUE,+BAA+BA,CAC3CC,QAAkB,EAAEC,QAAQ,GAAG,KAAK;EACtC,IAAIC,UAAU,GAAGlK,GAAG,EAAE,CAAC2F,SAAS,CAAC,wBAAwB,CAAC;EAC1D,IAAIwE,mBAAmB,GACnBnK,GAAG,EAAE,CAAC2F,SAAS,CAAC,mCAAmC,CAAC;EACxD,IAAIwE,mBAAmB,KAAKC,QAAQ,IAChCpK,GAAG,EAAE,CAACQ,OAAO,CAAC,0CAA0C,CAAC,EAAE;IAC7D2J,mBAAmB,GAAGD,UAAU,GAAG,CAAC;;EAGtC,IAAID,QAAQ,EAAE;IACZC,UAAU,GAAGA,UAAU,GAAG,CAAC;IAC3BC,mBAAmB,GAAGA,mBAAmB,GAAG,CAAC;IAE7C;IACA;IACA;IACA;IACA;IACAH,QAAQ,GAAGA,QAAQ,CAACnG,GAAG,CACnB,CAACwG,CAAC,EAAEpG,CAAC,KAAKA,CAAC,IAAI+F,QAAQ,CAACtG,MAAM,GAAG,CAAC,GAC9BzD,IAAI,CAACqK,iBAAiB,CAACN,QAAQ,CAAC/F,CAAC,CAAC,CAAC,GACnC+F,QAAQ,CAAC/F,CAAC,CAAC,CAAC;IAEpB;IACA;IACA,IAAI+F,QAAQ,CAACtG,MAAM,KAAK,CAAC,EAAE;MACzBsG,QAAQ,GAAG,CAAC,CAAC,EAAEA,QAAQ,CAAC,CAAC,CAAC,CAAC;;;EAI/B;EACA,IAAIA,QAAQ,CAACtG,MAAM,KAAK,CAAC,EAAE;IACzB,MAAM6G,aAAa,GAAGtK,IAAI,CAACuK,YAAY,CAACR,QAAQ,CAAC;IACjDA,QAAQ,GAAGO,aAAa,CAACE,QAAQ;;EAGnC,IAAIC,IAAI,GAAGzK,IAAI,CAACyJ,aAAa,CAACM,QAAQ,CAAC;EACvC,IAAIW,YAAY,GAAqB,IAAI;EACzC,IAAIX,QAAQ,CAACtG,MAAM,IAAI,CAAC,IAAIgH,IAAI,IAAIR,UAAU,EAAE;IAC9CS,YAAY,GAAG,CAAC,CAAC,EAAED,IAAI,CAAC;GACzB,MAAM,IACHV,QAAQ,CAACtG,MAAM,KAAK,CAAC,IAAIsG,QAAQ,CAAC,CAAC,CAAC,IAAIE,UAAU,IAClDF,QAAQ,CAAC,CAAC,CAAC,IAAIE,UAAU,EAAE;IAC7BS,YAAY,GAAGX,QAA4B;GAC5C,MAAM,IACHA,QAAQ,CAACtG,MAAM,KAAK,CAAC,IAAIsG,QAAQ,CAAC,CAAC,CAAC,GAAGA,QAAQ,CAAC,CAAC,CAAC,IAAIE,UAAU,IAChEF,QAAQ,CAAC,CAAC,CAAC,IAAIE,UAAU,EAAE;IAC7BS,YAAY,GAAG,CAACX,QAAQ,CAAC,CAAC,CAAC,GAAGA,QAAQ,CAAC,CAAC,CAAC,EAAEA,QAAQ,CAAC,CAAC,CAAC,CAAC;GACxD,MAAM,IACHA,QAAQ,CAACtG,MAAM,KAAK,CAAC,IAAIsG,QAAQ,CAAC,CAAC,CAAC,IAAIE,UAAU,IAClDF,QAAQ,CAAC,CAAC,CAAC,GAAGA,QAAQ,CAAC,CAAC,CAAC,IAAIE,UAAU,EAAE;IAC3CS,YAAY,GAAG,CAACX,QAAQ,CAAC,CAAC,CAAC,EAAEA,QAAQ,CAAC,CAAC,CAAC,GAAGA,QAAQ,CAAC,CAAC,CAAC,CAAC;GACxD,MAAM,IACHA,QAAQ,CAACtG,MAAM,KAAK,CAAC,IACrBsG,QAAQ,CAAC,CAAC,CAAC,GAAGA,QAAQ,CAAC,CAAC,CAAC,GAAGA,QAAQ,CAAC,CAAC,CAAC,IAAIE,UAAU,IACrDF,QAAQ,CAAC,CAAC,CAAC,IAAIE,UAAU,EAAE;IAC7BS,YAAY,GAAG,CAACX,QAAQ,CAAC,CAAC,CAAC,GAAGA,QAAQ,CAAC,CAAC,CAAC,GAAGA,QAAQ,CAAC,CAAC,CAAC,EAAEA,QAAQ,CAAC,CAAC,CAAC,CAAC;GACtE,MAAM,IACHA,QAAQ,CAACtG,MAAM,KAAK,CAAC,IAAIsG,QAAQ,CAAC,CAAC,CAAC,IAAIE,UAAU,IAClDF,QAAQ,CAAC,CAAC,CAAC,GAAGA,QAAQ,CAAC,CAAC,CAAC,GAAGA,QAAQ,CAAC,CAAC,CAAC,IAAIE,UAAU,EAAE;IACzDS,YAAY,GAAG,CAACX,QAAQ,CAAC,CAAC,CAAC,EAAEA,QAAQ,CAAC,CAAC,CAAC,GAAGA,QAAQ,CAAC,CAAC,CAAC,GAAGA,QAAQ,CAAC,CAAC,CAAC,CAAC;;EAGvE;EACA;EACA,MAAMY,eAAe,GAAGD,YAAY,IAAI,IAAI,IACxCxJ,IAAI,CAAC+C,GAAG,CAAC,GAAGyG,YAAY,CAAC,GAAGR,mBAAmB,IAC/ChJ,IAAI,CAAC0J,GAAG,CAAC,GAAGF,YAAY,CAAC,KAAKV,QAAQ,GAAG,CAAC,GAAG,CAAC,CAAC,IAC/C9I,IAAI,CAAC0J,GAAG,CAAC,GAAGF,YAAY,CAAC,GAAG,CAAC;EAEjC,IAAIA,YAAY,IAAI,IAAI,IAAIC,eAAe,EAAE;IAC3C,IAAIX,QAAQ,EAAE;MACZ;MACA;MACA;MACA;MACA;MAEA,MAAMa,QAAQ,GAAGvB,WAAW,CAACS,QAAQ,CAAC;MACtC,IAAIe,IAAI,GAAG,CAAC;QAAEC,IAAI,GAAG,CAAC;MACtB,IAAIhB,QAAQ,CAACtG,MAAM,EAAE;QACnB,CAACqH,IAAI,EAAEC,IAAI,CAAC,GAAGrB,WAAW,CAACK,QAAQ,CAAC;;MAEtCU,IAAI,GAAGI,QAAQ,IAAIC,IAAI,GAAG,CAAC,CAAC,IAAIC,IAAI,GAAG,CAAC,CAAC;MACzCL,YAAY,GACR1K,IAAI,CAACgL,mBAAmB,CAACP,IAAI,CAAC,CAAC7G,GAAG,CAACwG,CAAC,IAAIA,CAAC,GAAG,CAAC,CAAqB;KACvE,MAAM;MACLM,YAAY,GAAG1K,IAAI,CAACgL,mBAAmB,CAACP,IAAI,CAAC;;;EAIjD,OAAOC,YAAY;AACrB;AAEA,SAASO,MAAMA,CAACC,CAAS;EACvB,OAAOA,CAAC,GAAG,CAAC,KAAK,CAAC;AACpB;AAEA;;;;AAIA,OAAM,SAAUC,aAAaA,CAACC,MAAgB,EAAEC,MAAgB;EAC9DD,MAAM,GAAGA,MAAM,CAACjH,KAAK,CAAC,CAAC,CAAC,CAAC;EACzBkH,MAAM,GAAGA,MAAM,CAAClH,KAAK,CAAC,CAAC,CAAC,CAAC;EAEzB,IAAInE,IAAI,CAACsL,WAAW,CAACF,MAAM,EAAEC,MAAM,CAAC,EAAE;IACpC,OAAO,IAAI;;EAGb,IAAI,CAACD,MAAM,CAAC3H,MAAM,IAAI,CAAC4H,MAAM,CAAC5H,MAAM,EAAE;IAAG;IACvC,OAAO,IAAI;;EAGb,IAAI2H,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,IAAIA,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,IAAIC,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,IACrDA,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE;IACnB,OAAO,IAAI;;EAGb,IAAID,MAAM,CAAC3H,MAAM,KAAK4H,MAAM,CAAC5H,MAAM,EAAE;IAAG;IACtC,MAAM8H,UAAU,GAAGH,MAAM,CAACA,MAAM,CAAC3H,MAAM,GAAG,CAAC,CAAC;IAC5C,MAAM+H,UAAU,GAAGH,MAAM,CAACA,MAAM,CAAC5H,MAAM,GAAG,CAAC,CAAC;IAC5C,IAAI8H,UAAU,KAAKC,UAAU,EAAE;MAC7B,OAAO,IAAI;;IAGb,IAAIP,MAAM,CAACM,UAAU,CAAC,IAAIN,MAAM,CAACO,UAAU,CAAC,KACvCJ,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,IAAIC,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE;MACxC,OAAO,IAAI;;;EAGf,OAAOD,MAAM,CAAC,CAAC,CAAC,KAAKC,MAAM,CAAC,CAAC,CAAC,IAAIJ,MAAM,CAACG,MAAM,CAAC,CAAC,CAAC,CAAC,IAAIH,MAAM,CAACI,MAAM,CAAC,CAAC,CAAC,CAAC;AAC1E;AAEA;AACA;AACA;AACA,IAAII,gBAAwB;AAC5B,IAAIC,sBAA8B;AAElC,OAAM,SAAUC,sBAAsBA,CAACC,YAAoB;EACzD,IAAIH,gBAAgB,IAAI,IAAI,EAAE;IAC5B,MAAMrL,EAAE,GAAGH,eAAe,CAAC2L,YAAY,CAAC;IACxCH,gBAAgB,GAAGrL,EAAE,CAACyL,YAAY,CAACzL,EAAE,CAACqL,gBAAgB,CAAC;;EAEzD,OAAOA,gBAAgB;AACzB;AAEA,OAAM,SAAUK,mBAAmBA,CAAA;EACjCL,gBAAgB,GAAG,IAAI;AACzB;AACA,OAAM,SAAUM,wBAAwBA,CAAA;EACtCL,sBAAsB,GAAG,IAAI;AAC/B;AAEA,OAAM,SAAUM,sBAAsBA,CAACJ,YAAoB;EACzD,IAAIF,sBAAsB,IAAI,IAAI,EAAE;IAClC,MAAMtL,EAAE,GAAGH,eAAe,CAAC2L,YAAY,CAAC;IACxCF,sBAAsB,GAAGtL,EAAE,CAACyL,YAAY,CAACzL,EAAE,CAAC6L,uBAAuB,CAAC;;EAEtE;EACA,OAAO/K,IAAI,CAAC0J,GAAG,CAAC,EAAE,EAAEc,sBAAsB,CAAC;AAC7C;AAEA,OAAM,SAAUQ,iCAAiCA,CAACN,YAAoB;EAEpE,IAAIA,YAAY,KAAK,CAAC,EAAE;IACtB,OAAO,CAAC;;EAGV,IAAIO,iBAAyB;EAC7B,MAAM/L,EAAE,GAAGH,eAAe,CAAC2L,YAAY,CAAC;EAExC,IAAIQ,YAAY,CAAChM,EAAE,EAAE,iCAAiC,CAAC,IACnDwL,YAAY,KAAK,CAAC,EAAE;IACtBO,iBAAiB,GAAG,CAAC;GACtB,MAAM,IAAIC,YAAY,CAAChM,EAAE,EAAE,0BAA0B,CAAC,EAAE;IACvD+L,iBAAiB,GAAG,CAAC;GACtB,MAAM;IACLA,iBAAiB,GAAG,CAAC;;EAEvB,OAAOA,iBAAiB;AAC1B;AAEA,OAAM,SAAUC,YAAYA,CAAChM,EAAyB,EAAEwB,aAAqB;EAC3E,MAAMyK,GAAG,GAAGjM,EAAE,CAAC0B,YAAY,CAACF,aAAa,CAAC;EAC1C,OAAOyK,GAAG,IAAI,IAAI;AACpB;AAEA,OAAM,SAAUC,qBAAqBA,CAACV,YAAiB;EACrD,IAAI;IACF,MAAMxL,EAAE,GAAGH,eAAe,CAAC2L,YAAY,CAAC;IACxC,IAAIxL,EAAE,IAAI,IAAI,EAAE;MACd,OAAO,IAAI;;GAEd,CAAC,OAAOmM,CAAC,EAAE;IACV/J,OAAO,CAACC,GAAG,CAAC,oCAAoC,EAAE8J,CAAC,CAAC;IACpD,OAAO,KAAK;;EAEd,OAAO,KAAK;AACd;AAEA,OAAM,SAAUC,kCAAkCA,CAACZ,YAAoB;EAErE,IAAIA,YAAY,KAAK,CAAC,EAAE;IACtB,OAAO,KAAK;;EAGd,MAAMxL,EAAE,GAAGH,eAAe,CAAC2L,YAAY,CAAC;EAExC,IAAIA,YAAY,KAAK,CAAC,EAAE;IACtB,IAAI,CAACQ,YAAY,CAAChM,EAAE,EAAE,mBAAmB,CAAC,EAAE;MAC1C,OAAO,KAAK;;GAEf,MAAM;IACL,IAAI,CAACgM,YAAY,CAAChM,EAAE,EAAE,wBAAwB,CAAC,EAAE;MAC/C,OAAO,KAAK;;;EAIhB,MAAMqM,qBAAqB,GAAGC,sCAAsC,CAACtM,EAAE,CAAC;EACxE,OAAOqM,qBAAqB;AAC9B;AAEA;;;;;;;;;AASA,OAAM,SAAUE,6BAA6BA,CAACf,YAAoB;EAChE,IAAIA,YAAY,KAAK,CAAC,EAAE;IACtB,OAAO,KAAK;;EAGd,MAAMxL,EAAE,GAAGH,eAAe,CAAC2L,YAAY,CAAC;EAExC,IAAIA,YAAY,KAAK,CAAC,EAAE;IACtB,IAAI,CAACQ,YAAY,CAAChM,EAAE,EAAE,mBAAmB,CAAC,EAAE;MAC1C,OAAO,KAAK;;IAEd,IAAI,CAACgM,YAAY,CAAChM,EAAE,EAAE,0BAA0B,CAAC,EAAE;MACjD,OAAO,KAAK;;GAEf,MAAM;IACL,IAAIgM,YAAY,CAAChM,EAAE,EAAE,wBAAwB,CAAC,EAAE;MAC9C,OAAOsM,sCAAsC,CAACtM,EAAE,CAAC;;IAGnD,MAAMwM,uBAAuB,GAAG,6BAA6B;IAC7D,IAAIR,YAAY,CAAChM,EAAE,EAAEwM,uBAAuB,CAAC,EAAE;MAC7C,MAAMC,yBAAyB,GAC3BzM,EAAE,CAAC0B,YAAY,CAAC8K,uBAAuB,CAAC;MAC5C,OAAOE,0CAA0C,CAC7C1M,EAAE,EAAEyM,yBAAyB,CAAC;;IAGpC,OAAO,KAAK;;EAGd,MAAMJ,qBAAqB,GAAGC,sCAAsC,CAACtM,EAAE,CAAC;EACxE,OAAOqM,qBAAqB;AAC9B;AAEA,SAASC,sCAAsCA,CAACtM,EAAyB;EAEvE,MAAM2M,SAAS,GAAG7M,gBAAgB,CAACE,EAAE,CAAC;EAEtC,MAAMyG,OAAO,GAAGzG,EAAE,CAACuF,aAAa,EAAE;EAClCvF,EAAE,CAAC8G,WAAW,CAAC9G,EAAE,CAAC+G,UAAU,EAAEN,OAAO,CAAC;EAEtC,MAAMhB,KAAK,GAAG,CAAC;EACf,MAAMC,MAAM,GAAG,CAAC;EAChB1F,EAAE,CAAC4M,UAAU,CACT5M,EAAE,CAAC+G,UAAU,EAAE,CAAC,EAAE4F,SAAS,CAACE,mBAAmB,EAAEpH,KAAK,EAAEC,MAAM,EAAE,CAAC,EACjEiH,SAAS,CAACG,kBAAkB,EAAEH,SAAS,CAACI,gBAAgB,EAAE,IAAI,CAAC;EAEnE,MAAMC,WAAW,GAAGhN,EAAE,CAAC6F,iBAAiB,EAAE;EAC1C7F,EAAE,CAACyH,eAAe,CAACzH,EAAE,CAAC0H,WAAW,EAAEsF,WAAW,CAAC;EAC/ChN,EAAE,CAACgI,oBAAoB,CACnBhI,EAAE,CAAC0H,WAAW,EAAE1H,EAAE,CAACiI,iBAAiB,EAAEjI,EAAE,CAAC+G,UAAU,EAAEN,OAAO,EAAE,CAAC,CAAC;EAEpE,MAAM4F,qBAAqB,GACvBrM,EAAE,CAACoI,sBAAsB,CAACpI,EAAE,CAAC0H,WAAW,CAAC,KAAK1H,EAAE,CAACqI,oBAAoB;EAEzErI,EAAE,CAAC8G,WAAW,CAAC9G,EAAE,CAAC+G,UAAU,EAAE,IAAI,CAAC;EACnC/G,EAAE,CAACyH,eAAe,CAACzH,EAAE,CAAC0H,WAAW,EAAE,IAAI,CAAC;EACxC1H,EAAE,CAACiN,aAAa,CAACxG,OAAO,CAAC;EACzBzG,EAAE,CAACkN,iBAAiB,CAACF,WAAW,CAAC;EAEjC,OAAOX,qBAAqB;AAC9B;AAEA,SAASK,0CAA0CA;AAC/C;AACA1M,EAAyB,EAAEyM,yBAA8B;EAC3D,MAAME,SAAS,GAAG7M,gBAAgB,CAACE,EAAE,EAAEyM,yBAAyB,CAAC;EACjE,MAAMhG,OAAO,GAAGzG,EAAE,CAACuF,aAAa,EAAE;EAClCvF,EAAE,CAAC8G,WAAW,CAAC9G,EAAE,CAAC+G,UAAU,EAAEN,OAAO,CAAC;EAEtC,MAAMhB,KAAK,GAAG,CAAC;EACf,MAAMC,MAAM,GAAG,CAAC;EAChB1F,EAAE,CAAC4M,UAAU,CACT5M,EAAE,CAAC+G,UAAU,EAAE,CAAC,EAAE4F,SAAS,CAACQ,uBAAuB,EAAE1H,KAAK,EAAEC,MAAM,EAAE,CAAC,EACrEiH,SAAS,CAACG,kBAAkB,EAAEH,SAAS,CAACS,oBAAoB,EAAE,IAAI,CAAC;EAEvE,MAAMJ,WAAW,GAAGhN,EAAE,CAAC6F,iBAAiB,EAAE;EAC1C7F,EAAE,CAACyH,eAAe,CAACzH,EAAE,CAAC0H,WAAW,EAAEsF,WAAW,CAAC;EAC/ChN,EAAE,CAACgI,oBAAoB,CACnBhI,EAAE,CAAC0H,WAAW,EAAE1H,EAAE,CAACiI,iBAAiB,EAAEjI,EAAE,CAAC+G,UAAU,EAAEN,OAAO,EAAE,CAAC,CAAC;EAEpE,MAAM4F,qBAAqB,GACvBrM,EAAE,CAACoI,sBAAsB,CAACpI,EAAE,CAAC0H,WAAW,CAAC,KAAK1H,EAAE,CAACqI,oBAAoB;EAEzErI,EAAE,CAAC8G,WAAW,CAAC9G,EAAE,CAAC+G,UAAU,EAAE,IAAI,CAAC;EACnC/G,EAAE,CAACyH,eAAe,CAACzH,EAAE,CAAC0H,WAAW,EAAE,IAAI,CAAC;EACxC1H,EAAE,CAACiN,aAAa,CAACxG,OAAO,CAAC;EACzBzG,EAAE,CAACkN,iBAAiB,CAACF,WAAW,CAAC;EAEjC,OAAOX,qBAAqB;AAC9B;AAEA,OAAM,SAAUgB,mBAAmBA,CAAC7B,YAAoB;EACtD,IAAIA,YAAY,KAAK,CAAC,EAAE;IACtB,OAAO,KAAK;;EAEd,MAAMxL,EAAE,GAAGH,eAAe,CAAC2L,YAAY,CAAC;EAExC;EACA,MAAM8B,SAAS,GAAItN,EAAU,CAACuN,SAAS,IAAI,IAAI;EAC/C,OAAOD,SAAS;AAClB;AAEA,OAAM,SAAUE,gBAAgBA,CAC5BC,MAA+B,EAAEC,MAAc;EACjD,IAAI,CAACC,KAAK,CAACC,OAAO,CAACH,MAAM,CAAC,EAAE;IAC1BA,MAAM,GAAG,CAACA,MAAM,CAAC;;EAEnBA,MAAM,CAACI,OAAO,CAACC,CAAC,IAAG;IACjB,IAAIA,CAAC,IAAI,IAAI,EAAE;MACblO,IAAI,CAACmO,MAAM,CACPD,CAAC,CAACE,KAAK,KAAK,WAAW,EACvB,MAAM,GAAGN,MAAM,sCAAsC,GACjD,uBAAuB,CAAC;;EAEpC,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}