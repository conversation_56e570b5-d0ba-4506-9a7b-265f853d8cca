{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.transformArguments = exports.ClientKillFilters = void 0;\nvar ClientKillFilters;\n(function (ClientKillFilters) {\n  ClientKillFilters[\"ADDRESS\"] = \"ADDR\";\n  ClientKillFilters[\"LOCAL_ADDRESS\"] = \"LADDR\";\n  ClientKillFilters[\"ID\"] = \"ID\";\n  ClientKillFilters[\"TYPE\"] = \"TYPE\";\n  ClientKillFilters[\"USER\"] = \"USER\";\n  ClientKillFilters[\"SKIP_ME\"] = \"SKIPME\";\n  ClientKillFilters[\"MAXAGE\"] = \"MAXAGE\";\n})(ClientKillFilters || (exports.ClientKillFilters = ClientKillFilters = {}));\nfunction transformArguments(filters) {\n  const args = ['CLIENT', 'KILL'];\n  if (Array.isArray(filters)) {\n    for (const filter of filters) {\n      pushFilter(args, filter);\n    }\n  } else {\n    pushFilter(args, filters);\n  }\n  return args;\n}\nexports.transformArguments = transformArguments;\nfunction pushFilter(args, filter) {\n  if (filter === ClientKillFilters.SKIP_ME) {\n    args.push('SKIPME');\n    return;\n  }\n  args.push(filter.filter);\n  switch (filter.filter) {\n    case ClientKillFilters.ADDRESS:\n      args.push(filter.address);\n      break;\n    case ClientKillFilters.LOCAL_ADDRESS:\n      args.push(filter.localAddress);\n      break;\n    case ClientKillFilters.ID:\n      args.push(typeof filter.id === 'number' ? filter.id.toString() : filter.id);\n      break;\n    case ClientKillFilters.TYPE:\n      args.push(filter.type);\n      break;\n    case ClientKillFilters.USER:\n      args.push(filter.username);\n      break;\n    case ClientKillFilters.SKIP_ME:\n      args.push(filter.skipMe ? 'yes' : 'no');\n      break;\n    case ClientKillFilters.MAXAGE:\n      args.push(filter.maxAge.toString());\n      break;\n  }\n}", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "transformArguments", "ClientKillFilters", "filters", "args", "Array", "isArray", "filter", "pushFilter", "SKIP_ME", "push", "ADDRESS", "address", "LOCAL_ADDRESS", "localAddress", "ID", "id", "toString", "TYPE", "type", "USER", "username", "skip<PERSON>e", "MAXAGE", "maxAge"], "sources": ["C:/tmsft/node_modules/@redis/client/dist/lib/commands/CLIENT_KILL.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.transformArguments = exports.ClientKillFilters = void 0;\nvar ClientKillFilters;\n(function (ClientKillFilters) {\n    ClientKillFilters[\"ADDRESS\"] = \"ADDR\";\n    ClientKillFilters[\"LOCAL_ADDRESS\"] = \"LADDR\";\n    ClientKillFilters[\"ID\"] = \"ID\";\n    ClientKillFilters[\"TYPE\"] = \"TYPE\";\n    ClientKillFilters[\"USER\"] = \"USER\";\n    ClientKillFilters[\"SKIP_ME\"] = \"SKIPME\";\n    ClientKillFilters[\"MAXAGE\"] = \"MAXAGE\";\n})(ClientKillFilters || (exports.ClientKillFilters = ClientKillFilters = {}));\nfunction transformArguments(filters) {\n    const args = ['CLIENT', 'KILL'];\n    if (Array.isArray(filters)) {\n        for (const filter of filters) {\n            pushFilter(args, filter);\n        }\n    }\n    else {\n        pushFilter(args, filters);\n    }\n    return args;\n}\nexports.transformArguments = transformArguments;\nfunction pushFilter(args, filter) {\n    if (filter === ClientKillFilters.SKIP_ME) {\n        args.push('SKIPME');\n        return;\n    }\n    args.push(filter.filter);\n    switch (filter.filter) {\n        case ClientKillFilters.ADDRESS:\n            args.push(filter.address);\n            break;\n        case ClientKillFilters.LOCAL_ADDRESS:\n            args.push(filter.localAddress);\n            break;\n        case ClientKillFilters.ID:\n            args.push(typeof filter.id === 'number' ?\n                filter.id.toString() :\n                filter.id);\n            break;\n        case ClientKillFilters.TYPE:\n            args.push(filter.type);\n            break;\n        case ClientKillFilters.USER:\n            args.push(filter.username);\n            break;\n        case ClientKillFilters.SKIP_ME:\n            args.push(filter.skipMe ? 'yes' : 'no');\n            break;\n        case ClientKillFilters.MAXAGE:\n            args.push(filter.maxAge.toString());\n            break;\n    }\n}\n"], "mappings": "AAAA,YAAY;;AACZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEC,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7DD,OAAO,CAACE,kBAAkB,GAAGF,OAAO,CAACG,iBAAiB,GAAG,KAAK,CAAC;AAC/D,IAAIA,iBAAiB;AACrB,CAAC,UAAUA,iBAAiB,EAAE;EAC1BA,iBAAiB,CAAC,SAAS,CAAC,GAAG,MAAM;EACrCA,iBAAiB,CAAC,eAAe,CAAC,GAAG,OAAO;EAC5CA,iBAAiB,CAAC,IAAI,CAAC,GAAG,IAAI;EAC9BA,iBAAiB,CAAC,MAAM,CAAC,GAAG,MAAM;EAClCA,iBAAiB,CAAC,MAAM,CAAC,GAAG,MAAM;EAClCA,iBAAiB,CAAC,SAAS,CAAC,GAAG,QAAQ;EACvCA,iBAAiB,CAAC,QAAQ,CAAC,GAAG,QAAQ;AAC1C,CAAC,EAAEA,iBAAiB,KAAKH,OAAO,CAACG,iBAAiB,GAAGA,iBAAiB,GAAG,CAAC,CAAC,CAAC,CAAC;AAC7E,SAASD,kBAAkBA,CAACE,OAAO,EAAE;EACjC,MAAMC,IAAI,GAAG,CAAC,QAAQ,EAAE,MAAM,CAAC;EAC/B,IAAIC,KAAK,CAACC,OAAO,CAACH,OAAO,CAAC,EAAE;IACxB,KAAK,MAAMI,MAAM,IAAIJ,OAAO,EAAE;MAC1BK,UAAU,CAACJ,IAAI,EAAEG,MAAM,CAAC;IAC5B;EACJ,CAAC,MACI;IACDC,UAAU,CAACJ,IAAI,EAAED,OAAO,CAAC;EAC7B;EACA,OAAOC,IAAI;AACf;AACAL,OAAO,CAACE,kBAAkB,GAAGA,kBAAkB;AAC/C,SAASO,UAAUA,CAACJ,IAAI,EAAEG,MAAM,EAAE;EAC9B,IAAIA,MAAM,KAAKL,iBAAiB,CAACO,OAAO,EAAE;IACtCL,IAAI,CAACM,IAAI,CAAC,QAAQ,CAAC;IACnB;EACJ;EACAN,IAAI,CAACM,IAAI,CAACH,MAAM,CAACA,MAAM,CAAC;EACxB,QAAQA,MAAM,CAACA,MAAM;IACjB,KAAKL,iBAAiB,CAACS,OAAO;MAC1BP,IAAI,CAACM,IAAI,CAACH,MAAM,CAACK,OAAO,CAAC;MACzB;IACJ,KAAKV,iBAAiB,CAACW,aAAa;MAChCT,IAAI,CAACM,IAAI,CAACH,MAAM,CAACO,YAAY,CAAC;MAC9B;IACJ,KAAKZ,iBAAiB,CAACa,EAAE;MACrBX,IAAI,CAACM,IAAI,CAAC,OAAOH,MAAM,CAACS,EAAE,KAAK,QAAQ,GACnCT,MAAM,CAACS,EAAE,CAACC,QAAQ,CAAC,CAAC,GACpBV,MAAM,CAACS,EAAE,CAAC;MACd;IACJ,KAAKd,iBAAiB,CAACgB,IAAI;MACvBd,IAAI,CAACM,IAAI,CAACH,MAAM,CAACY,IAAI,CAAC;MACtB;IACJ,KAAKjB,iBAAiB,CAACkB,IAAI;MACvBhB,IAAI,CAACM,IAAI,CAACH,MAAM,CAACc,QAAQ,CAAC;MAC1B;IACJ,KAAKnB,iBAAiB,CAACO,OAAO;MAC1BL,IAAI,CAACM,IAAI,CAACH,MAAM,CAACe,MAAM,GAAG,KAAK,GAAG,IAAI,CAAC;MACvC;IACJ,KAAKpB,iBAAiB,CAACqB,MAAM;MACzBnB,IAAI,CAACM,IAAI,CAACH,MAAM,CAACiB,MAAM,CAACP,QAAQ,CAAC,CAAC,CAAC;MACnC;EACR;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}