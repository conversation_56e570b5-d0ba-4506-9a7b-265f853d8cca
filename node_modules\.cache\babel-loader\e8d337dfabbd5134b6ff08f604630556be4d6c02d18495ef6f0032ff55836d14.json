{"ast": null, "code": "/*\nCopyright (c) 2014, <PERSON><PERSON><PERSON> is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in\nall copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\nTHE SOFTWARE.\n*/\n\n'use strict';\n\nconst stopwords = require('../util/stopwords_fr');\nconst Tokenizer = require('../tokenizers/aggressive_tokenizer_fr');\nmodule.exports = function () {\n  const stemmer = this;\n  stemmer.stem = function (token) {\n    return token;\n  };\n  stemmer.tokenizeAndStem = function (text, keepStops) {\n    const stemmedTokens = [];\n    new Tokenizer().tokenize(text).forEach(function (token) {\n      let resultToken = token.toLowerCase();\n      if (keepStops || stopwords.words.indexOf(resultToken) === -1) {\n        // var resultToken = token.toLowerCase();\n        if (resultToken.match(/[a-zâàëéêèïîôûùç0-9]/gi)) {\n          resultToken = stemmer.stem(resultToken);\n        }\n        stemmedTokens.push(resultToken);\n      }\n    });\n    return stemmedTokens;\n  };\n\n  /*\n  stemmer.attach = function () {\n    String.prototype.stem = function () {\n      return stemmer.stem(this)\n    }\n     String.prototype.tokenizeAndStem = function (keepStops) {\n      return stemmer.tokenizeAndStem(this, keepStops)\n    }\n  }\n  */\n};", "map": {"version": 3, "names": ["stopwords", "require", "Tokenizer", "module", "exports", "stemmer", "stem", "token", "tokenizeAndStem", "text", "keepStops", "stemmedTokens", "tokenize", "for<PERSON>ach", "resultToken", "toLowerCase", "words", "indexOf", "match", "push"], "sources": ["C:/tmsft/node_modules/natural/lib/natural/stemmers/stemmer_fr.js"], "sourcesContent": ["/*\nCopyright (c) 2014, <PERSON><PERSON><PERSON> is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in\nall copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\nTHE SOFTWARE.\n*/\n\n'use strict'\n\nconst stopwords = require('../util/stopwords_fr')\nconst Tokenizer = require('../tokenizers/aggressive_tokenizer_fr')\n\nmodule.exports = function () {\n  const stemmer = this\n\n  stemmer.stem = function (token) {\n    return token\n  }\n\n  stemmer.tokenizeAndStem = function (text, keepStops) {\n    const stemmedTokens = []\n\n    new Tokenizer().tokenize(text).forEach(function (token) {\n      let resultToken = token.toLowerCase()\n      if (keepStops || stopwords.words.indexOf(resultToken) === -1) {\n        // var resultToken = token.toLowerCase();\n        if (resultToken.match(/[a-zâàëéêèïîôûùç0-9]/gi)) {\n          resultToken = stemmer.stem(resultToken)\n        }\n        stemmedTokens.push(resultToken)\n      }\n    })\n\n    return stemmedTokens\n  }\n\n  /*\n  stemmer.attach = function () {\n    String.prototype.stem = function () {\n      return stemmer.stem(this)\n    }\n\n    String.prototype.tokenizeAndStem = function (keepStops) {\n      return stemmer.tokenizeAndStem(this, keepStops)\n    }\n  }\n  */\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,YAAY;;AAEZ,MAAMA,SAAS,GAAGC,OAAO,CAAC,sBAAsB,CAAC;AACjD,MAAMC,SAAS,GAAGD,OAAO,CAAC,uCAAuC,CAAC;AAElEE,MAAM,CAACC,OAAO,GAAG,YAAY;EAC3B,MAAMC,OAAO,GAAG,IAAI;EAEpBA,OAAO,CAACC,IAAI,GAAG,UAAUC,KAAK,EAAE;IAC9B,OAAOA,KAAK;EACd,CAAC;EAEDF,OAAO,CAACG,eAAe,GAAG,UAAUC,IAAI,EAAEC,SAAS,EAAE;IACnD,MAAMC,aAAa,GAAG,EAAE;IAExB,IAAIT,SAAS,CAAC,CAAC,CAACU,QAAQ,CAACH,IAAI,CAAC,CAACI,OAAO,CAAC,UAAUN,KAAK,EAAE;MACtD,IAAIO,WAAW,GAAGP,KAAK,CAACQ,WAAW,CAAC,CAAC;MACrC,IAAIL,SAAS,IAAIV,SAAS,CAACgB,KAAK,CAACC,OAAO,CAACH,WAAW,CAAC,KAAK,CAAC,CAAC,EAAE;QAC5D;QACA,IAAIA,WAAW,CAACI,KAAK,CAAC,wBAAwB,CAAC,EAAE;UAC/CJ,WAAW,GAAGT,OAAO,CAACC,IAAI,CAACQ,WAAW,CAAC;QACzC;QACAH,aAAa,CAACQ,IAAI,CAACL,WAAW,CAAC;MACjC;IACF,CAAC,CAAC;IAEF,OAAOH,aAAa;EACtB,CAAC;;EAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}