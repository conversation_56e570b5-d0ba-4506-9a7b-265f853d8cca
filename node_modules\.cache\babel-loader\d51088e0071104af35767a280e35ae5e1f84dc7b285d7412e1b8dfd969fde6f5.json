{"ast": null, "code": "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { env, Prelu } from '@tensorflow/tfjs-core';\nimport { BinaryOpProgram } from '../binaryop_gpu';\nimport { BinaryOpPackedProgram } from '../binaryop_packed_gpu';\nexport const PRELU = \"return (a < 0.) ? b * a : a;\";\nexport const PRELU_PACKED = \"\\n  vec4 aLessThanZero = vec4(lessThan(a, vec4(0.)));\\n  return (aLessThanZero * (b * a)) + ((vec4(1.0) - aLessThanZero) * a);\\n\";\nexport function prelu(args) {\n  const {\n    inputs,\n    backend\n  } = args;\n  const {\n    x,\n    alpha\n  } = inputs;\n  const program = env().getBool('WEBGL_PACK_BINARY_OPERATIONS') ? new BinaryOpPackedProgram(PRELU_PACKED, x.shape, alpha.shape) : new BinaryOpProgram(PRELU, x.shape, alpha.shape);\n  return backend.runWebGLProgram(program, [x, alpha], 'float32');\n}\nexport const preluConfig = {\n  kernelName: Prelu,\n  backendName: 'webgl',\n  kernelFunc: prelu\n};", "map": {"version": 3, "names": ["env", "<PERSON><PERSON>", "BinaryOpProgram", "BinaryOpPackedProgram", "PRELU", "PRELU_PACKED", "prelu", "args", "inputs", "backend", "x", "alpha", "program", "getBool", "shape", "runWebGLProgram", "preluConfig", "kernelName", "backendName", "kernelFunc"], "sources": ["C:\\tfjs-backend-webgl\\src\\kernels\\Prelu.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {env, KernelConfig, KernelFunc, Prelu, PreluInputs, TensorInfo} from '@tensorflow/tfjs-core';\n\nimport {MathBackendWebGL} from '../backend_webgl';\nimport {BinaryOpProgram} from '../binaryop_gpu';\nimport {BinaryOpPackedProgram} from '../binaryop_packed_gpu';\n\nexport const PRELU = `return (a < 0.) ? b * a : a;`;\nexport const PRELU_PACKED = `\n  vec4 aLessThanZero = vec4(lessThan(a, vec4(0.)));\n  return (aLessThanZero * (b * a)) + ((vec4(1.0) - aLessThanZero) * a);\n`;\n\nexport function prelu(args: {inputs: PreluInputs, backend: MathBackendWebGL}):\n    TensorInfo {\n  const {inputs, backend} = args;\n  const {x, alpha} = inputs;\n\n  const program = env().getBool('WEBGL_PACK_BINARY_OPERATIONS') ?\n      new BinaryOpPackedProgram(PRELU_PACKED, x.shape, alpha.shape) :\n      new BinaryOpProgram(PRELU, x.shape, alpha.shape);\n  return backend.runWebGLProgram(program, [x, alpha], 'float32');\n}\n\nexport const preluConfig: KernelConfig = {\n  kernelName: Prelu,\n  backendName: 'webgl',\n  kernelFunc: prelu as unknown as KernelFunc\n};\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAiBA,SAAQA,GAAG,EAA4BC,KAAK,QAAgC,uBAAuB;AAGnG,SAAQC,eAAe,QAAO,iBAAiB;AAC/C,SAAQC,qBAAqB,QAAO,wBAAwB;AAE5D,OAAO,MAAMC,KAAK,iCAAiC;AACnD,OAAO,MAAMC,YAAY,qIAGxB;AAED,OAAM,SAAUC,KAAKA,CAACC,IAAsD;EAE1E,MAAM;IAACC,MAAM;IAAEC;EAAO,CAAC,GAAGF,IAAI;EAC9B,MAAM;IAACG,CAAC;IAAEC;EAAK,CAAC,GAAGH,MAAM;EAEzB,MAAMI,OAAO,GAAGZ,GAAG,EAAE,CAACa,OAAO,CAAC,8BAA8B,CAAC,GACzD,IAAIV,qBAAqB,CAACE,YAAY,EAAEK,CAAC,CAACI,KAAK,EAAEH,KAAK,CAACG,KAAK,CAAC,GAC7D,IAAIZ,eAAe,CAACE,KAAK,EAAEM,CAAC,CAACI,KAAK,EAAEH,KAAK,CAACG,KAAK,CAAC;EACpD,OAAOL,OAAO,CAACM,eAAe,CAACH,OAAO,EAAE,CAACF,CAAC,EAAEC,KAAK,CAAC,EAAE,SAAS,CAAC;AAChE;AAEA,OAAO,MAAMK,WAAW,GAAiB;EACvCC,UAAU,EAAEhB,KAAK;EACjBiB,WAAW,EAAE,OAAO;EACpBC,UAAU,EAAEb;CACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}