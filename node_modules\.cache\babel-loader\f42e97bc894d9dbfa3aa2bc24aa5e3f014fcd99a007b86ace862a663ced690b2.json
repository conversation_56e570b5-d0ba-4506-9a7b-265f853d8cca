{"ast": null, "code": "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n// Note that the identifier globalNameSpace is scoped to this module, but will\n// always resolve to the same global object regardless of how the module is\n// resolved.\n// tslint:disable-next-line:no-any\nlet globalNameSpace;\n// tslint:disable-next-line:no-any\nexport function getGlobalNamespace() {\n  if (globalNameSpace == null) {\n    // tslint:disable-next-line:no-any\n    let ns;\n    if (typeof window !== 'undefined') {\n      ns = window;\n    } else if (typeof global !== 'undefined') {\n      ns = global;\n    } else if (typeof process !== 'undefined') {\n      ns = process;\n    } else if (typeof self !== 'undefined') {\n      ns = self;\n    } else {\n      throw new Error('Could not find a global object');\n    }\n    globalNameSpace = ns;\n  }\n  return globalNameSpace;\n}\n// tslint:disable-next-line:no-any\nfunction getGlobalMap() {\n  const ns = getGlobalNamespace();\n  if (ns._tfGlobals == null) {\n    ns._tfGlobals = new Map();\n  }\n  return ns._tfGlobals;\n}\n/**\n * Returns a globally accessible 'singleton' object.\n *\n * @param key the name of the object\n * @param init a function to initialize to initialize this object\n *             the first time it is fetched.\n */\nexport function getGlobal(key, init) {\n  const globalMap = getGlobalMap();\n  if (globalMap.has(key)) {\n    return globalMap.get(key);\n  } else {\n    const singleton = init();\n    globalMap.set(key, singleton);\n    return globalMap.get(key);\n  }\n}", "map": {"version": 3, "names": ["globalNameSpace", "getGlobalNamespace", "ns", "window", "global", "process", "self", "Error", "getGlobalMap", "_tfGlobals", "Map", "getGlobal", "key", "init", "globalMap", "has", "get", "singleton", "set"], "sources": ["C:\\tfjs-core\\src\\global_util.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\n// Note that the identifier globalNameSpace is scoped to this module, but will\n// always resolve to the same global object regardless of how the module is\n// resolved.\n// tslint:disable-next-line:no-any\nlet globalNameSpace: {_tfGlobals: Map<string, any>};\n// tslint:disable-next-line:no-any\nexport function getGlobalNamespace(): {_tfGlobals: Map<string, any>} {\n  if (globalNameSpace == null) {\n    // tslint:disable-next-line:no-any\n    let ns: any;\n    if (typeof (window) !== 'undefined') {\n      ns = window;\n    } else if (typeof (global) !== 'undefined') {\n      ns = global;\n    } else if (typeof (process) !== 'undefined') {\n      ns = process;\n    } else if (typeof (self) !== 'undefined') {\n      ns = self;\n    } else {\n      throw new Error('Could not find a global object');\n    }\n    globalNameSpace = ns;\n  }\n  return globalNameSpace;\n}\n\n// tslint:disable-next-line:no-any\nfunction getGlobalMap(): Map<string, any> {\n  const ns = getGlobalNamespace();\n  if (ns._tfGlobals == null) {\n    ns._tfGlobals = new Map();\n  }\n  return ns._tfGlobals;\n}\n\n/**\n * Returns a globally accessible 'singleton' object.\n *\n * @param key the name of the object\n * @param init a function to initialize to initialize this object\n *             the first time it is fetched.\n */\nexport function getGlobal<T>(key: string, init: () => T): T {\n  const globalMap = getGlobalMap();\n  if (globalMap.has(key)) {\n    return globalMap.get(key);\n  } else {\n    const singleton = init();\n    globalMap.set(key, singleton);\n    return globalMap.get(key);\n  }\n}\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAiBA;AACA;AACA;AACA;AACA,IAAIA,eAA+C;AACnD;AACA,OAAM,SAAUC,kBAAkBA,CAAA;EAChC,IAAID,eAAe,IAAI,IAAI,EAAE;IAC3B;IACA,IAAIE,EAAO;IACX,IAAI,OAAQC,MAAO,KAAK,WAAW,EAAE;MACnCD,EAAE,GAAGC,MAAM;KACZ,MAAM,IAAI,OAAQC,MAAO,KAAK,WAAW,EAAE;MAC1CF,EAAE,GAAGE,MAAM;KACZ,MAAM,IAAI,OAAQC,OAAQ,KAAK,WAAW,EAAE;MAC3CH,EAAE,GAAGG,OAAO;KACb,MAAM,IAAI,OAAQC,IAAK,KAAK,WAAW,EAAE;MACxCJ,EAAE,GAAGI,IAAI;KACV,MAAM;MACL,MAAM,IAAIC,KAAK,CAAC,gCAAgC,CAAC;;IAEnDP,eAAe,GAAGE,EAAE;;EAEtB,OAAOF,eAAe;AACxB;AAEA;AACA,SAASQ,YAAYA,CAAA;EACnB,MAAMN,EAAE,GAAGD,kBAAkB,EAAE;EAC/B,IAAIC,EAAE,CAACO,UAAU,IAAI,IAAI,EAAE;IACzBP,EAAE,CAACO,UAAU,GAAG,IAAIC,GAAG,EAAE;;EAE3B,OAAOR,EAAE,CAACO,UAAU;AACtB;AAEA;;;;;;;AAOA,OAAM,SAAUE,SAASA,CAAIC,GAAW,EAAEC,IAAa;EACrD,MAAMC,SAAS,GAAGN,YAAY,EAAE;EAChC,IAAIM,SAAS,CAACC,GAAG,CAACH,GAAG,CAAC,EAAE;IACtB,OAAOE,SAAS,CAACE,GAAG,CAACJ,GAAG,CAAC;GAC1B,MAAM;IACL,MAAMK,SAAS,GAAGJ,IAAI,EAAE;IACxBC,SAAS,CAACI,GAAG,CAACN,GAAG,EAAEK,SAAS,CAAC;IAC7B,OAAOH,SAAS,CAACE,GAAG,CAACJ,GAAG,CAAC;;AAE7B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}