{"ast": null, "code": "/**\n * @license\n * Copyright 2018 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { ENGINE } from '../engine';\nimport { ClipByValue } from '../kernel_names';\nimport { convertToTensor } from '../tensor_util_env';\nimport * as util from '../util';\nimport { fill } from './fill';\nimport { op } from './operation';\n/**\n * Clips values element-wise. `max(min(x, clipValueMax), clipValueMin)`\n *\n * ```js\n * const x = tf.tensor1d([-1, 2, -3, 4]);\n *\n * x.clipByValue(-2, 3).print();  // or tf.clipByValue(x, -2, 3)\n * ```\n * @param x The input tensor.\n * @param clipValueMin Lower bound of range to be clipped to.\n * @param clipValueMax Upper bound of range to be clipped to.\n *\n * @doc {heading: 'Operations', subheading: 'Basic math'}\n */\nfunction clipByValue_(x, clipValueMin, clipValueMax) {\n  const $x = convertToTensor(x, 'x', 'clipByValue');\n  util.assert(clipValueMin <= clipValueMax, () => `Error in clip: min (${clipValueMin}) must be ` + `less than or equal to max (${clipValueMax}).`);\n  if (clipValueMin === clipValueMax) {\n    return fill($x.shape, clipValueMin, $x.dtype);\n  }\n  const inputs = {\n    x: $x\n  };\n  const attrs = {\n    clipValueMin,\n    clipValueMax\n  };\n  return ENGINE.runKernel(ClipByValue, inputs, attrs);\n}\nexport const clipByValue = /* @__PURE__ */op({\n  clipByValue_\n});", "map": {"version": 3, "names": ["ENGINE", "ClipByValue", "convertToTensor", "util", "fill", "op", "clipByValue_", "x", "clipValueMin", "clipValueMax", "$x", "assert", "shape", "dtype", "inputs", "attrs", "runKernel", "clipByValue"], "sources": ["C:\\tfjs-core\\src\\ops\\clip_by_value.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2018 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport {ENGINE} from '../engine';\nimport {ClipByValue, ClipByValueAttrs, ClipByValueInputs} from '../kernel_names';\nimport {NamedAttrMap} from '../kernel_registry';\nimport {Tensor} from '../tensor';\nimport {NamedTensorMap} from '../tensor_types';\nimport {convertToTensor} from '../tensor_util_env';\nimport {TensorLike} from '../types';\nimport * as util from '../util';\nimport {fill} from './fill';\n\nimport {op} from './operation';\n\n/**\n * Clips values element-wise. `max(min(x, clipValueMax), clipValueMin)`\n *\n * ```js\n * const x = tf.tensor1d([-1, 2, -3, 4]);\n *\n * x.clipByValue(-2, 3).print();  // or tf.clipByValue(x, -2, 3)\n * ```\n * @param x The input tensor.\n * @param clipValueMin Lower bound of range to be clipped to.\n * @param clipValueMax Upper bound of range to be clipped to.\n *\n * @doc {heading: 'Operations', subheading: 'Basic math'}\n */\nfunction clipByValue_<T extends Tensor>(\n    x: T|TensorLike, clipValueMin: number, clipValueMax: number): T {\n  const $x = convertToTensor(x, 'x', 'clipByValue');\n  util.assert(\n      (clipValueMin <= clipValueMax),\n      () => `Error in clip: min (${clipValueMin}) must be ` +\n          `less than or equal to max (${clipValueMax}).`);\n\n  if (clipValueMin === clipValueMax) {\n    return fill($x.shape, clipValueMin, $x.dtype) as T;\n  }\n\n  const inputs: ClipByValueInputs = {x: $x};\n  const attrs: ClipByValueAttrs = {clipValueMin, clipValueMax};\n\n  return ENGINE.runKernel(\n      ClipByValue, inputs as unknown as NamedTensorMap,\n      attrs as unknown as NamedAttrMap);\n}\n\nexport const clipByValue = /* @__PURE__ */ op({clipByValue_});\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAgBA,SAAQA,MAAM,QAAO,WAAW;AAChC,SAAQC,WAAW,QAA4C,iBAAiB;AAIhF,SAAQC,eAAe,QAAO,oBAAoB;AAElD,OAAO,KAAKC,IAAI,MAAM,SAAS;AAC/B,SAAQC,IAAI,QAAO,QAAQ;AAE3B,SAAQC,EAAE,QAAO,aAAa;AAE9B;;;;;;;;;;;;;;AAcA,SAASC,YAAYA,CACjBC,CAAe,EAAEC,YAAoB,EAAEC,YAAoB;EAC7D,MAAMC,EAAE,GAAGR,eAAe,CAACK,CAAC,EAAE,GAAG,EAAE,aAAa,CAAC;EACjDJ,IAAI,CAACQ,MAAM,CACNH,YAAY,IAAIC,YAAY,EAC7B,MAAM,uBAAuBD,YAAY,YAAY,GACjD,8BAA8BC,YAAY,IAAI,CAAC;EAEvD,IAAID,YAAY,KAAKC,YAAY,EAAE;IACjC,OAAOL,IAAI,CAACM,EAAE,CAACE,KAAK,EAAEJ,YAAY,EAAEE,EAAE,CAACG,KAAK,CAAM;;EAGpD,MAAMC,MAAM,GAAsB;IAACP,CAAC,EAAEG;EAAE,CAAC;EACzC,MAAMK,KAAK,GAAqB;IAACP,YAAY;IAAEC;EAAY,CAAC;EAE5D,OAAOT,MAAM,CAACgB,SAAS,CACnBf,WAAW,EAAEa,MAAmC,EAChDC,KAAgC,CAAC;AACvC;AAEA,OAAO,MAAME,WAAW,GAAG,eAAgBZ,EAAE,CAAC;EAACC;AAAY,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}