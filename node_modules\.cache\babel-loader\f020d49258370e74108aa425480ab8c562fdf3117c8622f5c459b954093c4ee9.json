{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.transformArguments = void 0;\nfunction transformArguments(index) {\n  return ['FT.SYNDUMP', index];\n}\nexports.transformArguments = transformArguments;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "transformArguments", "index"], "sources": ["C:/tmsft/node_modules/@redis/search/dist/commands/SYNDUMP.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.transformArguments = void 0;\nfunction transformArguments(index) {\n    return ['FT.SYNDUMP', index];\n}\nexports.transformArguments = transformArguments;\n"], "mappings": "AAAA,YAAY;;AACZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEC,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7DD,OAAO,CAACE,kBAAkB,GAAG,KAAK,CAAC;AACnC,SAASA,kBAAkBA,CAACC,KAAK,EAAE;EAC/B,OAAO,CAAC,YAAY,EAAEA,KAAK,CAAC;AAChC;AACAH,OAAO,CAACE,kBAAkB,GAAGA,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}