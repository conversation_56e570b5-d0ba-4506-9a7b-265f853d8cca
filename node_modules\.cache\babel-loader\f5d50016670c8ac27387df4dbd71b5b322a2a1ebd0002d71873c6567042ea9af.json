{"ast": null, "code": "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { backend_util, util } from '@tensorflow/tfjs-core';\nimport { assertNotComplex } from '../cpu_util';\nimport { cast } from '../kernels/Cast';\nimport { complex } from '../kernels/Complex';\n/**\n * Template that creates a `KernelFunc` for binary ops.\n * @param name Kernel name.\n * @param binaryKernelImpl A `SimpleBinaryKernelImpl` for the kernel.\n * @param binaryKernelComplexImpl Optional. If exists, represents a\n *     `ComplexBinaryKernelImpl` for the kernel, will be used when input dtype\n *     is `complex64`.\n * @param dtype Optional. If set, the result has this dtype. Otherwise, the\n *     result has the same dtype as the first input. This is mainly used in\n *     comparison kernels, such as Equal, Less, Greater, etc.\n */\nexport function binaryKernelFunc(name, simpleImpl, complexImpl, dtype) {\n  if (complexImpl == null) {\n    return ({\n      inputs,\n      backend\n    }) => {\n      const {\n        a,\n        b\n      } = inputs;\n      const cpuBackend = backend;\n      assertNotComplex([a, b], name);\n      const aVals = cpuBackend.data.get(a.dataId).values;\n      const bVals = cpuBackend.data.get(b.dataId).values;\n      const decodedAVals = a.dtype === 'string' ?\n      // tslint:disable-next-line: no-any\n      backend_util.fromUint8ToStringArray(aVals) : aVals;\n      const decodedBVals = a.dtype === 'string' ?\n      // tslint:disable-next-line: no-any\n      backend_util.fromUint8ToStringArray(bVals) : bVals;\n      const $dtype = dtype || a.dtype;\n      const [resultData, resultShape] = simpleImpl(a.shape, b.shape, decodedAVals, decodedBVals, $dtype);\n      return cpuBackend.makeTensorInfo(resultShape, $dtype, resultData);\n    };\n  }\n  return ({\n    inputs,\n    backend\n  }) => {\n    const {\n      a,\n      b\n    } = inputs;\n    const cpuBackend = backend;\n    if (a.dtype === 'complex64' || b.dtype === 'complex64') {\n      const $aComplex = cast({\n        inputs: {\n          x: a\n        },\n        backend: cpuBackend,\n        attrs: {\n          dtype: 'complex64'\n        }\n      });\n      const $aComplexVals = cpuBackend.data.get($aComplex.dataId);\n      const aReal = $aComplexVals.complexTensorInfos.real;\n      const aImag = $aComplexVals.complexTensorInfos.imag;\n      const aRealVals = cpuBackend.data.get(aReal.dataId).values;\n      const aImagVals = cpuBackend.data.get(aImag.dataId).values;\n      const $bComplex = cast({\n        inputs: {\n          x: b\n        },\n        backend: cpuBackend,\n        attrs: {\n          dtype: 'complex64'\n        }\n      });\n      const $bComplexVals = cpuBackend.data.get($bComplex.dataId);\n      const bReal = $bComplexVals.complexTensorInfos.real;\n      const bImag = $bComplexVals.complexTensorInfos.imag;\n      const bRealVals = cpuBackend.data.get(bReal.dataId).values;\n      const bImagVals = cpuBackend.data.get(bImag.dataId).values;\n      const [resultRealData, resultImagData, resultShape] = complexImpl(a.shape, b.shape, aRealVals, aImagVals, bRealVals, bImagVals);\n      const resultReal = cpuBackend.makeTensorInfo(resultShape, 'float32', resultRealData);\n      const resultImag = cpuBackend.makeTensorInfo(resultShape, 'float32', resultImagData);\n      const result = complex({\n        inputs: {\n          real: resultReal,\n          imag: resultImag\n        },\n        backend: cpuBackend\n      });\n      cpuBackend.disposeIntermediateTensorInfo($aComplex);\n      cpuBackend.disposeIntermediateTensorInfo($bComplex);\n      cpuBackend.disposeIntermediateTensorInfo(resultReal);\n      cpuBackend.disposeIntermediateTensorInfo(resultImag);\n      return result;\n    } else {\n      const aVals = cpuBackend.data.get(a.dataId).values;\n      const bVals = cpuBackend.data.get(b.dataId).values;\n      const $dtype = dtype || a.dtype;\n      const [resultData, resultShape] = simpleImpl(a.shape, b.shape, aVals, bVals, $dtype);\n      return cpuBackend.makeTensorInfo(resultShape, $dtype, resultData);\n    }\n  };\n}\n/**\n * Template that creates the complex type implementation for binary ops.\n * Supports broadcast.\n */\nexport function createComplexBinaryKernelImpl(op) {\n  return (aShape, bShape, aRealVals, aImagVals, bRealVals, bImagVals) => {\n    const resultShape = backend_util.assertAndGetBroadcastShape(aShape, bShape);\n    const resultSize = util.sizeFromShape(resultShape);\n    const resultRank = resultShape.length;\n    const resultStrides = util.computeStrides(resultShape);\n    const resultRealVals = util.getTypedArrayFromDType('float32', resultSize);\n    const resultImagVals = util.getTypedArrayFromDType('float32', resultSize);\n    const aBroadcastDims = backend_util.getBroadcastDims(aShape, resultShape);\n    const bBroadcastDims = backend_util.getBroadcastDims(bShape, resultShape);\n    const aVals = backend_util.mergeRealAndImagArrays(aRealVals, aImagVals);\n    const bVals = backend_util.mergeRealAndImagArrays(bRealVals, bImagVals);\n    const aRank = aShape.length;\n    const aStrides = util.computeStrides(aShape);\n    const bRank = bShape.length;\n    const bStrides = util.computeStrides(bShape);\n    if (aBroadcastDims.length + bBroadcastDims.length === 0) {\n      for (let i = 0; i < resultRealVals.length; i++) {\n        const aIdx = i % aVals.length;\n        const bIdx = i % bVals.length;\n        const result = op(aVals[aIdx * 2], aVals[aIdx * 2 + 1], bVals[bIdx * 2], bVals[bIdx * 2 + 1]);\n        resultRealVals[i] = result.real;\n        resultImagVals[i] = result.imag;\n      }\n    } else {\n      for (let i = 0; i < resultRealVals.length; i++) {\n        const loc = util.indexToLoc(i, resultRank, resultStrides);\n        const aLoc = loc.slice(-aRank);\n        aBroadcastDims.forEach(d => aLoc[d] = 0);\n        const aIndex = util.locToIndex(aLoc, aRank, aStrides);\n        const bLoc = loc.slice(-bRank);\n        bBroadcastDims.forEach(d => bLoc[d] = 0);\n        const bIndex = util.locToIndex(bLoc, bRank, bStrides);\n        const opResult = op(aVals[aIndex * 2], aVals[aIndex * 2 + 1], bVals[bIndex * 2], bVals[bIndex * 2 + 1]);\n        resultRealVals[i] = opResult.real;\n        resultImagVals[i] = opResult.imag;\n      }\n    }\n    return [resultRealVals, resultImagVals, resultShape];\n  };\n}", "map": {"version": 3, "names": ["backend_util", "util", "assertNotComplex", "cast", "complex", "binaryKernelFunc", "name", "simpleImpl", "complexImpl", "dtype", "inputs", "backend", "a", "b", "cpuBackend", "aVals", "data", "get", "dataId", "values", "bVals", "decodedAVals", "fromUint8ToStringArray", "decodedBVals", "$dtype", "resultData", "resultShape", "shape", "makeTensorInfo", "$aComplex", "x", "attrs", "$aComplexVals", "aReal", "complexTensorInfos", "real", "aImag", "imag", "aRealVals", "aImagVals", "$bComplex", "$bComplexVals", "bReal", "bImag", "bRealVals", "bImagVals", "resultRealData", "resultImagData", "resultReal", "resultImag", "result", "disposeIntermediateTensorInfo", "createComplexBinaryKernelImpl", "op", "aShape", "bShape", "assertAndGetBroadcastShape", "resultSize", "sizeFromShape", "resultRank", "length", "<PERSON><PERSON><PERSON><PERSON>", "computeStrides", "resultRealVals", "getTypedArrayFromDType", "resultImagVals", "aBroadcastDims", "getBroadcastDims", "bBroadcastDims", "mergeRealAndImagArrays", "aRank", "a<PERSON><PERSON><PERSON>", "bRank", "b<PERSON><PERSON><PERSON>", "i", "aIdx", "bIdx", "loc", "indexToLoc", "aLoc", "slice", "for<PERSON>ach", "d", "aIndex", "locToIndex", "bLoc", "bIndex", "opResult"], "sources": ["C:\\tfjs-backend-cpu\\src\\utils\\binary_utils.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {backend_util, BinaryInputs, DataType, KernelFunc, TypedArray, util} from '@tensorflow/tfjs-core';\n\nimport {MathBackendCPU} from '../backend_cpu';\nimport {assertNotComplex} from '../cpu_util';\nimport {cast} from '../kernels/Cast';\nimport {complex} from '../kernels/Complex';\n\nimport {ComplexBinaryKernelImpl, ComplexBinaryOperation, SimpleBinaryKernelImpl} from './binary_types';\n\n/**\n * Template that creates a `KernelFunc` for binary ops.\n * @param name Kernel name.\n * @param binaryKernelImpl A `SimpleBinaryKernelImpl` for the kernel.\n * @param binaryKernelComplexImpl Optional. If exists, represents a\n *     `ComplexBinaryKernelImpl` for the kernel, will be used when input dtype\n *     is `complex64`.\n * @param dtype Optional. If set, the result has this dtype. Otherwise, the\n *     result has the same dtype as the first input. This is mainly used in\n *     comparison kernels, such as Equal, Less, Greater, etc.\n */\nexport function binaryKernelFunc(\n    name: string, simpleImpl: SimpleBinaryKernelImpl,\n    complexImpl?: ComplexBinaryKernelImpl, dtype?: DataType): KernelFunc {\n  if (complexImpl == null) {\n    return ({inputs, backend}) => {\n      const {a, b} = inputs as BinaryInputs;\n      const cpuBackend = backend as MathBackendCPU;\n\n      assertNotComplex([a, b], name);\n\n      const aVals = cpuBackend.data.get(a.dataId).values as TypedArray;\n      const bVals = cpuBackend.data.get(b.dataId).values as TypedArray;\n\n      const decodedAVals = a.dtype === 'string' ?\n          // tslint:disable-next-line: no-any\n          backend_util.fromUint8ToStringArray(aVals as any as Uint8Array[]) :\n          aVals;\n      const decodedBVals = a.dtype === 'string' ?\n          // tslint:disable-next-line: no-any\n          backend_util.fromUint8ToStringArray(bVals as any as Uint8Array[]) :\n          bVals;\n      const $dtype = dtype || a.dtype;\n\n      const [resultData, resultShape] =\n          simpleImpl(a.shape, b.shape, decodedAVals, decodedBVals, $dtype);\n\n      return cpuBackend.makeTensorInfo(resultShape, $dtype, resultData);\n    };\n  }\n\n  return ({inputs, backend}) => {\n    const {a, b} = inputs as BinaryInputs;\n    const cpuBackend = backend as MathBackendCPU;\n\n    if (a.dtype === 'complex64' || b.dtype === 'complex64') {\n      const $aComplex = cast(\n          {inputs: {x: a}, backend: cpuBackend, attrs: {dtype: 'complex64'}});\n\n      const $aComplexVals = cpuBackend.data.get($aComplex.dataId);\n\n      const aReal = $aComplexVals.complexTensorInfos.real;\n      const aImag = $aComplexVals.complexTensorInfos.imag;\n\n      const aRealVals =\n          cpuBackend.data.get(aReal.dataId).values as Float32Array;\n      const aImagVals =\n          cpuBackend.data.get(aImag.dataId).values as Float32Array;\n\n      const $bComplex = cast(\n          {inputs: {x: b}, backend: cpuBackend, attrs: {dtype: 'complex64'}});\n\n      const $bComplexVals = cpuBackend.data.get($bComplex.dataId);\n\n      const bReal = $bComplexVals.complexTensorInfos.real;\n      const bImag = $bComplexVals.complexTensorInfos.imag;\n\n      const bRealVals =\n          cpuBackend.data.get(bReal.dataId).values as Float32Array;\n      const bImagVals =\n          cpuBackend.data.get(bImag.dataId).values as Float32Array;\n\n      const [resultRealData, resultImagData, resultShape] = complexImpl(\n          a.shape, b.shape, aRealVals, aImagVals, bRealVals, bImagVals);\n\n      const resultReal =\n          cpuBackend.makeTensorInfo(resultShape, 'float32', resultRealData);\n\n      const resultImag =\n          cpuBackend.makeTensorInfo(resultShape, 'float32', resultImagData);\n\n      const result = complex(\n          {inputs: {real: resultReal, imag: resultImag}, backend: cpuBackend});\n\n      cpuBackend.disposeIntermediateTensorInfo($aComplex);\n      cpuBackend.disposeIntermediateTensorInfo($bComplex);\n      cpuBackend.disposeIntermediateTensorInfo(resultReal);\n      cpuBackend.disposeIntermediateTensorInfo(resultImag);\n\n      return result;\n    } else {\n      const aVals = cpuBackend.data.get(a.dataId).values as TypedArray;\n      const bVals = cpuBackend.data.get(b.dataId).values as TypedArray;\n\n      const $dtype = dtype || a.dtype;\n\n      const [resultData, resultShape] =\n          simpleImpl(a.shape, b.shape, aVals, bVals, $dtype);\n\n      return cpuBackend.makeTensorInfo(resultShape, $dtype, resultData);\n    }\n  };\n}\n\n/**\n * Template that creates the complex type implementation for binary ops.\n * Supports broadcast.\n */\nexport function createComplexBinaryKernelImpl(op: ComplexBinaryOperation):\n    ComplexBinaryKernelImpl {\n  return (aShape: number[], bShape: number[], aRealVals: Float32Array,\n          aImagVals: Float32Array, bRealVals: Float32Array,\n          bImagVals: Float32Array): [TypedArray, TypedArray, number[]] => {\n    const resultShape = backend_util.assertAndGetBroadcastShape(aShape, bShape);\n    const resultSize = util.sizeFromShape(resultShape);\n    const resultRank = resultShape.length;\n    const resultStrides = util.computeStrides(resultShape);\n\n    const resultRealVals = util.getTypedArrayFromDType('float32', resultSize);\n    const resultImagVals = util.getTypedArrayFromDType('float32', resultSize);\n\n    const aBroadcastDims = backend_util.getBroadcastDims(aShape, resultShape);\n    const bBroadcastDims = backend_util.getBroadcastDims(bShape, resultShape);\n\n    const aVals = backend_util.mergeRealAndImagArrays(aRealVals, aImagVals);\n    const bVals = backend_util.mergeRealAndImagArrays(bRealVals, bImagVals);\n\n    const aRank = aShape.length;\n    const aStrides = util.computeStrides(aShape);\n\n    const bRank = bShape.length;\n    const bStrides = util.computeStrides(bShape);\n\n    if (aBroadcastDims.length + bBroadcastDims.length === 0) {\n      for (let i = 0; i < resultRealVals.length; i++) {\n        const aIdx = i % aVals.length;\n        const bIdx = i % bVals.length;\n\n        const result =\n            op(aVals[aIdx * 2], aVals[aIdx * 2 + 1], bVals[bIdx * 2],\n               bVals[bIdx * 2 + 1]);\n\n        resultRealVals[i] = result.real;\n        resultImagVals[i] = result.imag;\n      }\n    } else {\n      for (let i = 0; i < resultRealVals.length; i++) {\n        const loc = util.indexToLoc(i, resultRank, resultStrides);\n\n        const aLoc = loc.slice(-aRank);\n        aBroadcastDims.forEach(d => aLoc[d] = 0);\n        const aIndex = util.locToIndex(aLoc, aRank, aStrides);\n\n        const bLoc = loc.slice(-bRank);\n        bBroadcastDims.forEach(d => bLoc[d] = 0);\n        const bIndex = util.locToIndex(bLoc, bRank, bStrides);\n\n        const opResult =\n            op(aVals[aIndex * 2], aVals[aIndex * 2 + 1], bVals[bIndex * 2],\n               bVals[bIndex * 2 + 1]);\n\n        resultRealVals[i] = opResult.real;\n        resultImagVals[i] = opResult.imag;\n      }\n    }\n    return [resultRealVals, resultImagVals, resultShape];\n  };\n}\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAiBA,SAAQA,YAAY,EAAkDC,IAAI,QAAO,uBAAuB;AAGxG,SAAQC,gBAAgB,QAAO,aAAa;AAC5C,SAAQC,IAAI,QAAO,iBAAiB;AACpC,SAAQC,OAAO,QAAO,oBAAoB;AAI1C;;;;;;;;;;;AAWA,OAAM,SAAUC,gBAAgBA,CAC5BC,IAAY,EAAEC,UAAkC,EAChDC,WAAqC,EAAEC,KAAgB;EACzD,IAAID,WAAW,IAAI,IAAI,EAAE;IACvB,OAAO,CAAC;MAACE,MAAM;MAAEC;IAAO,CAAC,KAAI;MAC3B,MAAM;QAACC,CAAC;QAAEC;MAAC,CAAC,GAAGH,MAAsB;MACrC,MAAMI,UAAU,GAAGH,OAAyB;MAE5CT,gBAAgB,CAAC,CAACU,CAAC,EAAEC,CAAC,CAAC,EAAEP,IAAI,CAAC;MAE9B,MAAMS,KAAK,GAAGD,UAAU,CAACE,IAAI,CAACC,GAAG,CAACL,CAAC,CAACM,MAAM,CAAC,CAACC,MAAoB;MAChE,MAAMC,KAAK,GAAGN,UAAU,CAACE,IAAI,CAACC,GAAG,CAACJ,CAAC,CAACK,MAAM,CAAC,CAACC,MAAoB;MAEhE,MAAME,YAAY,GAAGT,CAAC,CAACH,KAAK,KAAK,QAAQ;MACrC;MACAT,YAAY,CAACsB,sBAAsB,CAACP,KAA4B,CAAC,GACjEA,KAAK;MACT,MAAMQ,YAAY,GAAGX,CAAC,CAACH,KAAK,KAAK,QAAQ;MACrC;MACAT,YAAY,CAACsB,sBAAsB,CAACF,KAA4B,CAAC,GACjEA,KAAK;MACT,MAAMI,MAAM,GAAGf,KAAK,IAAIG,CAAC,CAACH,KAAK;MAE/B,MAAM,CAACgB,UAAU,EAAEC,WAAW,CAAC,GAC3BnB,UAAU,CAACK,CAAC,CAACe,KAAK,EAAEd,CAAC,CAACc,KAAK,EAAEN,YAAY,EAAEE,YAAY,EAAEC,MAAM,CAAC;MAEpE,OAAOV,UAAU,CAACc,cAAc,CAACF,WAAW,EAAEF,MAAM,EAAEC,UAAU,CAAC;IACnE,CAAC;;EAGH,OAAO,CAAC;IAACf,MAAM;IAAEC;EAAO,CAAC,KAAI;IAC3B,MAAM;MAACC,CAAC;MAAEC;IAAC,CAAC,GAAGH,MAAsB;IACrC,MAAMI,UAAU,GAAGH,OAAyB;IAE5C,IAAIC,CAAC,CAACH,KAAK,KAAK,WAAW,IAAII,CAAC,CAACJ,KAAK,KAAK,WAAW,EAAE;MACtD,MAAMoB,SAAS,GAAG1B,IAAI,CAClB;QAACO,MAAM,EAAE;UAACoB,CAAC,EAAElB;QAAC,CAAC;QAAED,OAAO,EAAEG,UAAU;QAAEiB,KAAK,EAAE;UAACtB,KAAK,EAAE;QAAW;MAAC,CAAC,CAAC;MAEvE,MAAMuB,aAAa,GAAGlB,UAAU,CAACE,IAAI,CAACC,GAAG,CAACY,SAAS,CAACX,MAAM,CAAC;MAE3D,MAAMe,KAAK,GAAGD,aAAa,CAACE,kBAAkB,CAACC,IAAI;MACnD,MAAMC,KAAK,GAAGJ,aAAa,CAACE,kBAAkB,CAACG,IAAI;MAEnD,MAAMC,SAAS,GACXxB,UAAU,CAACE,IAAI,CAACC,GAAG,CAACgB,KAAK,CAACf,MAAM,CAAC,CAACC,MAAsB;MAC5D,MAAMoB,SAAS,GACXzB,UAAU,CAACE,IAAI,CAACC,GAAG,CAACmB,KAAK,CAAClB,MAAM,CAAC,CAACC,MAAsB;MAE5D,MAAMqB,SAAS,GAAGrC,IAAI,CAClB;QAACO,MAAM,EAAE;UAACoB,CAAC,EAAEjB;QAAC,CAAC;QAAEF,OAAO,EAAEG,UAAU;QAAEiB,KAAK,EAAE;UAACtB,KAAK,EAAE;QAAW;MAAC,CAAC,CAAC;MAEvE,MAAMgC,aAAa,GAAG3B,UAAU,CAACE,IAAI,CAACC,GAAG,CAACuB,SAAS,CAACtB,MAAM,CAAC;MAE3D,MAAMwB,KAAK,GAAGD,aAAa,CAACP,kBAAkB,CAACC,IAAI;MACnD,MAAMQ,KAAK,GAAGF,aAAa,CAACP,kBAAkB,CAACG,IAAI;MAEnD,MAAMO,SAAS,GACX9B,UAAU,CAACE,IAAI,CAACC,GAAG,CAACyB,KAAK,CAACxB,MAAM,CAAC,CAACC,MAAsB;MAC5D,MAAM0B,SAAS,GACX/B,UAAU,CAACE,IAAI,CAACC,GAAG,CAAC0B,KAAK,CAACzB,MAAM,CAAC,CAACC,MAAsB;MAE5D,MAAM,CAAC2B,cAAc,EAAEC,cAAc,EAAErB,WAAW,CAAC,GAAGlB,WAAW,CAC7DI,CAAC,CAACe,KAAK,EAAEd,CAAC,CAACc,KAAK,EAAEW,SAAS,EAAEC,SAAS,EAAEK,SAAS,EAAEC,SAAS,CAAC;MAEjE,MAAMG,UAAU,GACZlC,UAAU,CAACc,cAAc,CAACF,WAAW,EAAE,SAAS,EAAEoB,cAAc,CAAC;MAErE,MAAMG,UAAU,GACZnC,UAAU,CAACc,cAAc,CAACF,WAAW,EAAE,SAAS,EAAEqB,cAAc,CAAC;MAErE,MAAMG,MAAM,GAAG9C,OAAO,CAClB;QAACM,MAAM,EAAE;UAACyB,IAAI,EAAEa,UAAU;UAAEX,IAAI,EAAEY;QAAU,CAAC;QAAEtC,OAAO,EAAEG;MAAU,CAAC,CAAC;MAExEA,UAAU,CAACqC,6BAA6B,CAACtB,SAAS,CAAC;MACnDf,UAAU,CAACqC,6BAA6B,CAACX,SAAS,CAAC;MACnD1B,UAAU,CAACqC,6BAA6B,CAACH,UAAU,CAAC;MACpDlC,UAAU,CAACqC,6BAA6B,CAACF,UAAU,CAAC;MAEpD,OAAOC,MAAM;KACd,MAAM;MACL,MAAMnC,KAAK,GAAGD,UAAU,CAACE,IAAI,CAACC,GAAG,CAACL,CAAC,CAACM,MAAM,CAAC,CAACC,MAAoB;MAChE,MAAMC,KAAK,GAAGN,UAAU,CAACE,IAAI,CAACC,GAAG,CAACJ,CAAC,CAACK,MAAM,CAAC,CAACC,MAAoB;MAEhE,MAAMK,MAAM,GAAGf,KAAK,IAAIG,CAAC,CAACH,KAAK;MAE/B,MAAM,CAACgB,UAAU,EAAEC,WAAW,CAAC,GAC3BnB,UAAU,CAACK,CAAC,CAACe,KAAK,EAAEd,CAAC,CAACc,KAAK,EAAEZ,KAAK,EAAEK,KAAK,EAAEI,MAAM,CAAC;MAEtD,OAAOV,UAAU,CAACc,cAAc,CAACF,WAAW,EAAEF,MAAM,EAAEC,UAAU,CAAC;;EAErE,CAAC;AACH;AAEA;;;;AAIA,OAAM,SAAU2B,6BAA6BA,CAACC,EAA0B;EAEtE,OAAO,CAACC,MAAgB,EAAEC,MAAgB,EAAEjB,SAAuB,EAC3DC,SAAuB,EAAEK,SAAuB,EAChDC,SAAuB,KAAwC;IACrE,MAAMnB,WAAW,GAAG1B,YAAY,CAACwD,0BAA0B,CAACF,MAAM,EAAEC,MAAM,CAAC;IAC3E,MAAME,UAAU,GAAGxD,IAAI,CAACyD,aAAa,CAAChC,WAAW,CAAC;IAClD,MAAMiC,UAAU,GAAGjC,WAAW,CAACkC,MAAM;IACrC,MAAMC,aAAa,GAAG5D,IAAI,CAAC6D,cAAc,CAACpC,WAAW,CAAC;IAEtD,MAAMqC,cAAc,GAAG9D,IAAI,CAAC+D,sBAAsB,CAAC,SAAS,EAAEP,UAAU,CAAC;IACzE,MAAMQ,cAAc,GAAGhE,IAAI,CAAC+D,sBAAsB,CAAC,SAAS,EAAEP,UAAU,CAAC;IAEzE,MAAMS,cAAc,GAAGlE,YAAY,CAACmE,gBAAgB,CAACb,MAAM,EAAE5B,WAAW,CAAC;IACzE,MAAM0C,cAAc,GAAGpE,YAAY,CAACmE,gBAAgB,CAACZ,MAAM,EAAE7B,WAAW,CAAC;IAEzE,MAAMX,KAAK,GAAGf,YAAY,CAACqE,sBAAsB,CAAC/B,SAAS,EAAEC,SAAS,CAAC;IACvE,MAAMnB,KAAK,GAAGpB,YAAY,CAACqE,sBAAsB,CAACzB,SAAS,EAAEC,SAAS,CAAC;IAEvE,MAAMyB,KAAK,GAAGhB,MAAM,CAACM,MAAM;IAC3B,MAAMW,QAAQ,GAAGtE,IAAI,CAAC6D,cAAc,CAACR,MAAM,CAAC;IAE5C,MAAMkB,KAAK,GAAGjB,MAAM,CAACK,MAAM;IAC3B,MAAMa,QAAQ,GAAGxE,IAAI,CAAC6D,cAAc,CAACP,MAAM,CAAC;IAE5C,IAAIW,cAAc,CAACN,MAAM,GAAGQ,cAAc,CAACR,MAAM,KAAK,CAAC,EAAE;MACvD,KAAK,IAAIc,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGX,cAAc,CAACH,MAAM,EAAEc,CAAC,EAAE,EAAE;QAC9C,MAAMC,IAAI,GAAGD,CAAC,GAAG3D,KAAK,CAAC6C,MAAM;QAC7B,MAAMgB,IAAI,GAAGF,CAAC,GAAGtD,KAAK,CAACwC,MAAM;QAE7B,MAAMV,MAAM,GACRG,EAAE,CAACtC,KAAK,CAAC4D,IAAI,GAAG,CAAC,CAAC,EAAE5D,KAAK,CAAC4D,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC,EAAEvD,KAAK,CAACwD,IAAI,GAAG,CAAC,CAAC,EACrDxD,KAAK,CAACwD,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;QAE3Bb,cAAc,CAACW,CAAC,CAAC,GAAGxB,MAAM,CAACf,IAAI;QAC/B8B,cAAc,CAACS,CAAC,CAAC,GAAGxB,MAAM,CAACb,IAAI;;KAElC,MAAM;MACL,KAAK,IAAIqC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGX,cAAc,CAACH,MAAM,EAAEc,CAAC,EAAE,EAAE;QAC9C,MAAMG,GAAG,GAAG5E,IAAI,CAAC6E,UAAU,CAACJ,CAAC,EAAEf,UAAU,EAAEE,aAAa,CAAC;QAEzD,MAAMkB,IAAI,GAAGF,GAAG,CAACG,KAAK,CAAC,CAACV,KAAK,CAAC;QAC9BJ,cAAc,CAACe,OAAO,CAACC,CAAC,IAAIH,IAAI,CAACG,CAAC,CAAC,GAAG,CAAC,CAAC;QACxC,MAAMC,MAAM,GAAGlF,IAAI,CAACmF,UAAU,CAACL,IAAI,EAAET,KAAK,EAAEC,QAAQ,CAAC;QAErD,MAAMc,IAAI,GAAGR,GAAG,CAACG,KAAK,CAAC,CAACR,KAAK,CAAC;QAC9BJ,cAAc,CAACa,OAAO,CAACC,CAAC,IAAIG,IAAI,CAACH,CAAC,CAAC,GAAG,CAAC,CAAC;QACxC,MAAMI,MAAM,GAAGrF,IAAI,CAACmF,UAAU,CAACC,IAAI,EAAEb,KAAK,EAAEC,QAAQ,CAAC;QAErD,MAAMc,QAAQ,GACVlC,EAAE,CAACtC,KAAK,CAACoE,MAAM,GAAG,CAAC,CAAC,EAAEpE,KAAK,CAACoE,MAAM,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE/D,KAAK,CAACkE,MAAM,GAAG,CAAC,CAAC,EAC3DlE,KAAK,CAACkE,MAAM,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;QAE7BvB,cAAc,CAACW,CAAC,CAAC,GAAGa,QAAQ,CAACpD,IAAI;QACjC8B,cAAc,CAACS,CAAC,CAAC,GAAGa,QAAQ,CAAClD,IAAI;;;IAGrC,OAAO,CAAC0B,cAAc,EAAEE,cAAc,EAAEvC,WAAW,CAAC;EACtD,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}