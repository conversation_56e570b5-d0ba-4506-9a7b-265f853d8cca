{"ast": null, "code": "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nexport const EPSILON_FLOAT32 = 1e-7;\nexport const EPSILON_FLOAT16 = 1e-4;\n/** Convenient class for storing tensor-related data. */\nexport class DataStorage {\n  constructor(backend, dataMover) {\n    this.backend = backend;\n    this.dataMover = dataMover;\n    this.data = new WeakMap();\n    this.dataIdsCount = 0;\n  }\n  get(dataId) {\n    if (!this.data.has(dataId)) {\n      this.dataMover.moveData(this.backend, dataId);\n    }\n    return this.data.get(dataId);\n  }\n  set(dataId, value) {\n    this.dataIdsCount++;\n    this.data.set(dataId, value);\n  }\n  has(dataId) {\n    return this.data.has(dataId);\n  }\n  delete(dataId) {\n    this.dataIdsCount--;\n    return this.data.delete(dataId);\n  }\n  numDataIds() {\n    return this.dataIdsCount;\n  }\n}\n/**\n * The interface that defines the kernels that should be implemented when\n * adding a new backend. New backends don't need to implement every one of the\n * methods, this can be done gradually (throw an error for unimplemented\n * methods).\n */\nexport class KernelBackend {\n  refCount(dataId) {\n    return notYetImplemented('refCount');\n  }\n  incRef(dataId) {\n    return notYetImplemented('incRef');\n  }\n  timerAvailable() {\n    return true;\n  }\n  time(f) {\n    return notYetImplemented('time');\n  }\n  read(dataId) {\n    return notYetImplemented('read');\n  }\n  readSync(dataId) {\n    return notYetImplemented('readSync');\n  }\n  readToGPU(dataId, options) {\n    return notYetImplemented('readToGPU');\n  }\n  numDataIds() {\n    return notYetImplemented('numDataIds');\n  }\n  disposeData(dataId, force) {\n    return notYetImplemented('disposeData');\n  }\n  write(values, shape, dtype) {\n    return notYetImplemented('write');\n  }\n  move(dataId, values, shape, dtype, refCount) {\n    return notYetImplemented('move');\n  }\n  createTensorFromGPUData(values, shape, dtype) {\n    return notYetImplemented('createTensorFromGPUData');\n  }\n  memory() {\n    return notYetImplemented('memory');\n  }\n  /** Returns the highest precision for floats in bits (e.g. 16 or 32) */\n  floatPrecision() {\n    return notYetImplemented('floatPrecision');\n  }\n  /** Returns the smallest representable number.  */\n  epsilon() {\n    return this.floatPrecision() === 32 ? EPSILON_FLOAT32 : EPSILON_FLOAT16;\n  }\n  dispose() {\n    return notYetImplemented('dispose');\n  }\n}\nfunction notYetImplemented(kernelName) {\n  throw new Error(\"'\".concat(kernelName, \"' not yet implemented or not found in the registry. \") + \"This kernel may not be supported by the tfjs backend you have chosen\");\n}", "map": {"version": 3, "names": ["EPSILON_FLOAT32", "EPSILON_FLOAT16", "DataStorage", "constructor", "backend", "dataMover", "data", "WeakMap", "dataIdsCount", "get", "dataId", "has", "moveData", "set", "value", "delete", "numDataIds", "KernelBackend", "refCount", "notYetImplemented", "incRef", "timerAvailable", "time", "f", "read", "readSync", "readToGPU", "options", "disposeData", "force", "write", "values", "shape", "dtype", "move", "createTensorFromGPUData", "memory", "floatPrecision", "epsilon", "dispose", "kernelName", "Error", "concat"], "sources": ["C:\\tfjs-core\\src\\backends\\backend.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {Backend, DataToGPUOptions, GPUData, Tensor} from '../tensor';\nimport {DataId} from '../tensor_info';\nimport {BackendValues, DataType, WebGLData, WebGPUData} from '../types';\n\nexport const EPSILON_FLOAT32 = 1e-7;\nexport const EPSILON_FLOAT16 = 1e-4;\n\n// Required information for all backends.\nexport interface BackendTimingInfo {\n  kernelMs: number|{error: string};\n  getExtraProfileInfo?(): string;  // a field for additional timing information\n                                   // e.g. packing / unpacking for WebGL backend\n}\n\nexport interface TensorStorage {\n  read(dataId: DataId): Promise<BackendValues>;\n  readSync(dataId: DataId): BackendValues;\n  disposeData(dataId: DataId, force?: boolean): boolean;\n  write(values: BackendValues, shape: number[], dtype: DataType): DataId;\n  move(\n      dataId: DataId, values: BackendValues, shape: number[], dtype: DataType,\n      refCount: number): void;\n  memory(): {unreliable: boolean;};  // Backend-specific information.\n  /** Returns number of data ids currently in the storage. */\n  numDataIds(): number;\n  refCount(dataId: DataId): number;\n}\n\n/** Convenient class for storing tensor-related data. */\nexport class DataStorage<T> {\n  private data = new WeakMap<DataId, T>();\n  private dataIdsCount = 0;\n\n  constructor(private backend: KernelBackend, private dataMover: DataMover) {}\n\n  get(dataId: DataId) {\n    if (!this.data.has(dataId)) {\n      this.dataMover.moveData(this.backend, dataId);\n    }\n    return this.data.get(dataId);\n  }\n\n  set(dataId: DataId, value: T): void {\n    this.dataIdsCount++;\n    this.data.set(dataId, value);\n  }\n\n  has(dataId: DataId): boolean {\n    return this.data.has(dataId);\n  }\n\n  delete(dataId: DataId): boolean {\n    this.dataIdsCount--;\n    return this.data.delete(dataId);\n  }\n\n  numDataIds(): number {\n    return this.dataIdsCount;\n  }\n}\n\nexport interface DataMover {\n  /**\n   * To be called by backends whenever they see a dataId that they don't own.\n   * Upon calling this method, the mover will fetch the tensor from another\n   * backend and register it with the current active backend.\n   */\n  moveData(backend: KernelBackend, dataId: DataId): void;\n}\n\nexport interface BackendTimer {\n  // check if backend timer is available\n  timerAvailable(): boolean;\n  time(f: () => void): Promise<BackendTimingInfo>;\n}\n\n/**\n * The interface that defines the kernels that should be implemented when\n * adding a new backend. New backends don't need to implement every one of the\n * methods, this can be done gradually (throw an error for unimplemented\n * methods).\n */\nexport class KernelBackend implements TensorStorage, Backend, BackendTimer {\n  refCount(dataId: DataId): number {\n    return notYetImplemented('refCount');\n  }\n  incRef(dataId: DataId): void {\n    return notYetImplemented('incRef');\n  }\n  timerAvailable(): boolean {\n    return true;\n  }\n  time(f: () => void): Promise<BackendTimingInfo> {\n    return notYetImplemented('time');\n  }\n  read(dataId: object): Promise<BackendValues> {\n    return notYetImplemented('read');\n  }\n  readSync(dataId: object): BackendValues {\n    return notYetImplemented('readSync');\n  }\n  readToGPU(dataId: object, options?: DataToGPUOptions): GPUData {\n    return notYetImplemented('readToGPU');\n  }\n  numDataIds(): number {\n    return notYetImplemented('numDataIds');\n  }\n  disposeData(dataId: object, force?: boolean): boolean {\n    return notYetImplemented('disposeData');\n  }\n  write(values: BackendValues, shape: number[], dtype: DataType): DataId {\n    return notYetImplemented('write');\n  }\n  move(\n      dataId: DataId, values: BackendValues, shape: number[], dtype: DataType,\n      refCount: number): void {\n    return notYetImplemented('move');\n  }\n\n  createTensorFromGPUData(\n      values: WebGLData|WebGPUData, shape: number[], dtype: DataType): Tensor {\n    return notYetImplemented('createTensorFromGPUData');\n  }\n\n  memory(): {unreliable: boolean; reasons?: string[]} {\n    return notYetImplemented('memory');\n  }\n  /** Returns the highest precision for floats in bits (e.g. 16 or 32) */\n  floatPrecision(): 16|32 {\n    return notYetImplemented('floatPrecision');\n  }\n  /** Returns the smallest representable number.  */\n  epsilon(): number {\n    return this.floatPrecision() === 32 ? EPSILON_FLOAT32 : EPSILON_FLOAT16;\n  }\n  dispose(): void {\n    return notYetImplemented('dispose');\n  }\n}\n\nfunction notYetImplemented(kernelName: string): never {\n  throw new Error(\n      `'${kernelName}' not yet implemented or not found in the registry. ` +\n      `This kernel may not be supported by the tfjs backend you have chosen`);\n}\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAqBA,OAAO,MAAMA,eAAe,GAAG,IAAI;AACnC,OAAO,MAAMC,eAAe,GAAG,IAAI;AAuBnC;AACA,OAAM,MAAOC,WAAW;EAItBC,YAAoBC,OAAsB,EAAUC,SAAoB;IAApD,KAAAD,OAAO,GAAPA,OAAO;IAAyB,KAAAC,SAAS,GAATA,SAAS;IAHrD,KAAAC,IAAI,GAAG,IAAIC,OAAO,EAAa;IAC/B,KAAAC,YAAY,GAAG,CAAC;EAEmD;EAE3EC,GAAGA,CAACC,MAAc;IAChB,IAAI,CAAC,IAAI,CAACJ,IAAI,CAACK,GAAG,CAACD,MAAM,CAAC,EAAE;MAC1B,IAAI,CAACL,SAAS,CAACO,QAAQ,CAAC,IAAI,CAACR,OAAO,EAAEM,MAAM,CAAC;;IAE/C,OAAO,IAAI,CAACJ,IAAI,CAACG,GAAG,CAACC,MAAM,CAAC;EAC9B;EAEAG,GAAGA,CAACH,MAAc,EAAEI,KAAQ;IAC1B,IAAI,CAACN,YAAY,EAAE;IACnB,IAAI,CAACF,IAAI,CAACO,GAAG,CAACH,MAAM,EAAEI,KAAK,CAAC;EAC9B;EAEAH,GAAGA,CAACD,MAAc;IAChB,OAAO,IAAI,CAACJ,IAAI,CAACK,GAAG,CAACD,MAAM,CAAC;EAC9B;EAEAK,MAAMA,CAACL,MAAc;IACnB,IAAI,CAACF,YAAY,EAAE;IACnB,OAAO,IAAI,CAACF,IAAI,CAACS,MAAM,CAACL,MAAM,CAAC;EACjC;EAEAM,UAAUA,CAAA;IACR,OAAO,IAAI,CAACR,YAAY;EAC1B;;AAkBF;;;;;;AAMA,OAAM,MAAOS,aAAa;EACxBC,QAAQA,CAACR,MAAc;IACrB,OAAOS,iBAAiB,CAAC,UAAU,CAAC;EACtC;EACAC,MAAMA,CAACV,MAAc;IACnB,OAAOS,iBAAiB,CAAC,QAAQ,CAAC;EACpC;EACAE,cAAcA,CAAA;IACZ,OAAO,IAAI;EACb;EACAC,IAAIA,CAACC,CAAa;IAChB,OAAOJ,iBAAiB,CAAC,MAAM,CAAC;EAClC;EACAK,IAAIA,CAACd,MAAc;IACjB,OAAOS,iBAAiB,CAAC,MAAM,CAAC;EAClC;EACAM,QAAQA,CAACf,MAAc;IACrB,OAAOS,iBAAiB,CAAC,UAAU,CAAC;EACtC;EACAO,SAASA,CAAChB,MAAc,EAAEiB,OAA0B;IAClD,OAAOR,iBAAiB,CAAC,WAAW,CAAC;EACvC;EACAH,UAAUA,CAAA;IACR,OAAOG,iBAAiB,CAAC,YAAY,CAAC;EACxC;EACAS,WAAWA,CAAClB,MAAc,EAAEmB,KAAe;IACzC,OAAOV,iBAAiB,CAAC,aAAa,CAAC;EACzC;EACAW,KAAKA,CAACC,MAAqB,EAAEC,KAAe,EAAEC,KAAe;IAC3D,OAAOd,iBAAiB,CAAC,OAAO,CAAC;EACnC;EACAe,IAAIA,CACAxB,MAAc,EAAEqB,MAAqB,EAAEC,KAAe,EAAEC,KAAe,EACvEf,QAAgB;IAClB,OAAOC,iBAAiB,CAAC,MAAM,CAAC;EAClC;EAEAgB,uBAAuBA,CACnBJ,MAA4B,EAAEC,KAAe,EAAEC,KAAe;IAChE,OAAOd,iBAAiB,CAAC,yBAAyB,CAAC;EACrD;EAEAiB,MAAMA,CAAA;IACJ,OAAOjB,iBAAiB,CAAC,QAAQ,CAAC;EACpC;EACA;EACAkB,cAAcA,CAAA;IACZ,OAAOlB,iBAAiB,CAAC,gBAAgB,CAAC;EAC5C;EACA;EACAmB,OAAOA,CAAA;IACL,OAAO,IAAI,CAACD,cAAc,EAAE,KAAK,EAAE,GAAGrC,eAAe,GAAGC,eAAe;EACzE;EACAsC,OAAOA,CAAA;IACL,OAAOpB,iBAAiB,CAAC,SAAS,CAAC;EACrC;;AAGF,SAASA,iBAAiBA,CAACqB,UAAkB;EAC3C,MAAM,IAAIC,KAAK,CACX,IAAAC,MAAA,CAAIF,UAAU,kIACwD,CAAC;AAC7E", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}