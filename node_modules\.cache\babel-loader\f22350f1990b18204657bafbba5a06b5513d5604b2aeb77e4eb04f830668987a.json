{"ast": null, "code": "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n// TODO update import path once op is modularized.\nimport { irfft } from '../../ops/ops';\nimport { getGlobalTensorClass } from '../../tensor';\ngetGlobalTensorClass().prototype.irfft = function () {\n  this.throwIfDisposed();\n  return irfft(this);\n};", "map": {"version": 3, "names": ["irfft", "getGlobalTensorClass", "prototype", "throwIfDisposed"], "sources": ["C:\\tfjs-core\\src\\public\\chained_ops\\irfft.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\n// TODO update import path once op is modularized.\nimport {irfft} from '../../ops/ops';\nimport {getGlobalTensorClass, Tensor} from '../../tensor';\nimport {Rank} from '../../types';\n\ndeclare module '../../tensor' {\n  interface Tensor<R extends Rank = Rank> {\n    irfft<T extends Tensor>(this: Tensor): Tensor;\n  }\n}\n\ngetGlobalTensorClass().prototype.irfft = function<T extends Tensor>(\n    this: Tensor): T {\n  this.throwIfDisposed();\n  return irfft(this) as T;\n};\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAiBA;AACA,SAAQA,KAAK,QAAO,eAAe;AACnC,SAAQC,oBAAoB,QAAe,cAAc;AASzDA,oBAAoB,EAAE,CAACC,SAAS,CAACF,KAAK,GAAG;EAEvC,IAAI,CAACG,eAAe,EAAE;EACtB,OAAOH,KAAK,CAAC,IAAI,CAAM;AACzB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}