{"ast": null, "code": "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { backend_util, Conv3D } from '@tensorflow/tfjs-core';\nimport { Conv3DProgram } from '../conv_gpu';\nexport function conv3D(args) {\n  const {\n    inputs,\n    backend,\n    attrs\n  } = args;\n  const {\n    x,\n    filter\n  } = inputs;\n  const {\n    strides,\n    pad,\n    dilations\n  } = attrs;\n  const convInfo = backend_util.computeConv3DInfo(x.shape, filter.shape, strides, dilations, pad);\n  const program = new Conv3DProgram(convInfo);\n  return backend.runWebGLProgram(program, [x, filter], 'float32');\n}\nexport const conv3DConfig = {\n  kernelName: Conv3D,\n  backendName: 'webgl',\n  kernelFunc: conv3D\n};", "map": {"version": 3, "names": ["backend_util", "Conv3D", "Conv3DProgram", "conv3D", "args", "inputs", "backend", "attrs", "x", "filter", "strides", "pad", "dilations", "convInfo", "computeConv3DInfo", "shape", "program", "runWebGLProgram", "conv3DConfig", "kernelName", "backendName", "kernelFunc"], "sources": ["C:\\tfjs-backend-webgl\\src\\kernels\\Conv3D.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {backend_util, Conv3D, Conv3DAttrs, Conv3DInputs, KernelConfig, KernelFunc} from '@tensorflow/tfjs-core';\n\nimport {MathBackendWebGL} from '../backend_webgl';\nimport {Conv3DProgram} from '../conv_gpu';\n\nexport function conv3D(\n    args:\n        {inputs: Conv3DInputs, attrs: Conv3DAttrs, backend: MathBackendWebGL}) {\n  const {inputs, backend, attrs} = args;\n  const {x, filter} = inputs;\n  const {strides, pad, dilations} = attrs;\n\n  const convInfo = backend_util.computeConv3DInfo(\n      x.shape as [number, number, number, number, number],\n      filter.shape as [number, number, number, number, number], strides,\n      dilations, pad);\n\n  const program = new Conv3DProgram(convInfo);\n  return backend.runWebGLProgram(program, [x, filter], 'float32');\n}\n\nexport const conv3DConfig: KernelConfig = {\n  kernelName: Conv3D,\n  backendName: 'webgl',\n  kernelFunc: conv3D as unknown as KernelFunc,\n};\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAiBA,SAAQA,YAAY,EAAEC,MAAM,QAA4D,uBAAuB;AAG/G,SAAQC,aAAa,QAAO,aAAa;AAEzC,OAAM,SAAUC,MAAMA,CAClBC,IACyE;EAC3E,MAAM;IAACC,MAAM;IAAEC,OAAO;IAAEC;EAAK,CAAC,GAAGH,IAAI;EACrC,MAAM;IAACI,CAAC;IAAEC;EAAM,CAAC,GAAGJ,MAAM;EAC1B,MAAM;IAACK,OAAO;IAAEC,GAAG;IAAEC;EAAS,CAAC,GAAGL,KAAK;EAEvC,MAAMM,QAAQ,GAAGb,YAAY,CAACc,iBAAiB,CAC3CN,CAAC,CAACO,KAAiD,EACnDN,MAAM,CAACM,KAAiD,EAAEL,OAAO,EACjEE,SAAS,EAAED,GAAG,CAAC;EAEnB,MAAMK,OAAO,GAAG,IAAId,aAAa,CAACW,QAAQ,CAAC;EAC3C,OAAOP,OAAO,CAACW,eAAe,CAACD,OAAO,EAAE,CAACR,CAAC,EAAEC,MAAM,CAAC,EAAE,SAAS,CAAC;AACjE;AAEA,OAAO,MAAMS,YAAY,GAAiB;EACxCC,UAAU,EAAElB,MAAM;EAClBmB,WAAW,EAAE,OAAO;EACpBC,UAAU,EAAElB;CACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}