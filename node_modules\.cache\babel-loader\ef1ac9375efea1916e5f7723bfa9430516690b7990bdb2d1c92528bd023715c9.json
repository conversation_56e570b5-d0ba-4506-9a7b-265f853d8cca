{"ast": null, "code": "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { convertToTensor } from '../../tensor_util_env';\nimport { assertShapesMatch } from '../../util';\nimport { abs } from '../abs';\nimport { add } from '../add';\nimport { exp } from '../exp';\nimport { log1p } from '../log1p';\nimport { Reduction } from '../loss_ops_utils';\nimport { mul } from '../mul';\nimport { neg } from '../neg';\nimport { op } from '../operation';\nimport { relu } from '../relu';\nimport { scalar } from '../scalar';\nimport { sub } from '../sub';\nimport { computeWeightedLoss } from './compute_weighted_loss';\nfunction sigmoidCrossEntropyWithLogits_(labels, logits) {\n  const $labels = convertToTensor(labels, 'labels', 'sigmoidCrossEntropyWithLogits');\n  const $logits = convertToTensor(logits, 'logits', 'sigmoidCrossEntropyWithLogits');\n  assertShapesMatch($labels.shape, $logits.shape, 'Error in sigmoidCrossEntropyWithLogits: ');\n  /**\n   * Implementation Details:\n   *\n   * For brevity, let `x = logits`, `z = labels`.  The logistic loss is\n   *     z * -log(sigmoid(x)) + (1 - z) * -log(1 - sigmoid(x))\n   *   = z * -log(1 / (1 + exp(-x))) + (1 - z) * -log(exp(-x) / (1 + exp(-x)))\n   *   = z * log(1 + exp(-x)) + (1 - z) * (-log(exp(-x)) + log(1 + exp(-x)))\n   *   = z * log(1 + exp(-x)) + (1 - z) * (x + log(1 + exp(-x))\n   *   = (1 - z) * x + log(1 + exp(-x))\n   *   = x - x * z + log(1 + exp(-x))\n   *\n   *   For x < 0, to avoid overflow in exp(-x), we reformulate the above\n   *     x - x * z + log(1 + exp(-x))\n   *   = log(exp(x)) - x * z + log(1 + exp(-x))\n   *   = - x * z + log(1 + exp(x))\n   *\n   * Hence, to ensure stability and avoid overflow, the implementation uses\n   * this equivalent formulation:\n   *     max(x, 0) - x * z + log(1 + exp(-abs(x)))\n   */\n  const maxOutput = relu($logits);\n  const outputXTarget = mul($logits, $labels);\n  const sigmoidOutput = log1p(exp(neg(abs($logits))));\n  return add(sub(maxOutput, outputXTarget), sigmoidOutput);\n}\n/**\n * Computes the sigmoid cross entropy loss between two tensors.\n *\n * If labelSmoothing is nonzero, smooth the labels towards 1/2:\n *\n *   newMulticlassLabels = multiclassLabels * (1 - labelSmoothing)\n *                         + 0.5 * labelSmoothing\n *\n * @param multiClassLabels The ground truth output tensor of shape\n * [batch_size, num_classes], same dimensions as 'predictions'.\n * @param logits The predicted outputs.\n * @param weights Tensor whose rank is either 0, or the same rank as\n *    `labels`, and must be broadcastable to `labels` (i.e., all dimensions\n *    must be either `1`, or the same as the corresponding `losses`\n *    dimension).\n * @param labelSmoothing If greater than 0, then smooth the labels.\n * @param reduction Type of reduction to apply to loss. Should be of type\n *    `Reduction`\n *\n * @doc { heading: 'Training', subheading: 'Losses', namespace: 'losses' }\n */\nfunction sigmoidCrossEntropy_(multiClassLabels, logits, weights, labelSmoothing = 0, reduction = Reduction.SUM_BY_NONZERO_WEIGHTS) {\n  let $multiClassLabels = convertToTensor(multiClassLabels, 'multiClassLabels', 'sigmoidCrossEntropy');\n  const $logits = convertToTensor(logits, 'logits', 'sigmoidCrossEntropy');\n  let $weights = null;\n  if (weights != null) {\n    $weights = convertToTensor(weights, 'weights', 'sigmoidCrossEntropy');\n  }\n  assertShapesMatch($multiClassLabels.shape, $logits.shape, 'Error in sigmoidCrossEntropy: ');\n  if (labelSmoothing > 0) {\n    const labelSmoothingScalar = scalar(labelSmoothing);\n    const one = scalar(1);\n    const half = scalar(0.5);\n    $multiClassLabels = add(mul($multiClassLabels, sub(one, labelSmoothingScalar)), mul(half, labelSmoothingScalar));\n  }\n  const losses = sigmoidCrossEntropyWithLogits_($multiClassLabels, $logits);\n  return computeWeightedLoss(losses, $weights, reduction);\n}\nexport const sigmoidCrossEntropy = /* @__PURE__ */op({\n  sigmoidCrossEntropy_\n});", "map": {"version": 3, "names": ["convertToTensor", "assertShapesMatch", "abs", "add", "exp", "log1p", "Reduction", "mul", "neg", "op", "relu", "scalar", "sub", "computeWeightedLoss", "sigmoidCrossEntropyWithLogits_", "labels", "logits", "$labels", "$logits", "shape", "maxOutput", "outputXTarget", "sigmoidOutput", "sigmoidCrossEntropy_", "multiClassLabels", "weights", "labelSmoothing", "reduction", "SUM_BY_NONZERO_WEIGHTS", "$multiClassLabels", "$weights", "labelSmoothingScalar", "one", "half", "losses", "sigmoidCrossEntropy"], "sources": ["C:\\tfjs-core\\src\\ops\\losses\\sigmoid_cross_entropy.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {Tensor} from '../../tensor';\nimport {convertToTensor} from '../../tensor_util_env';\nimport {TensorLike} from '../../types';\nimport {assertShapesMatch} from '../../util';\nimport {abs} from '../abs';\nimport {add} from '../add';\nimport {exp} from '../exp';\nimport {log1p} from '../log1p';\nimport {Reduction} from '../loss_ops_utils';\nimport {mul} from '../mul';\nimport {neg} from '../neg';\nimport {op} from '../operation';\nimport {relu} from '../relu';\nimport {scalar} from '../scalar';\nimport {sub} from '../sub';\n\nimport {computeWeightedLoss} from './compute_weighted_loss';\n\nfunction sigmoidCrossEntropyWithLogits_<T extends Tensor, O extends Tensor>(\n    labels: T|TensorLike, logits: T|TensorLike): O {\n  const $labels =\n      convertToTensor(labels, 'labels', 'sigmoidCrossEntropyWithLogits');\n  const $logits =\n      convertToTensor(logits, 'logits', 'sigmoidCrossEntropyWithLogits');\n  assertShapesMatch(\n      $labels.shape, $logits.shape, 'Error in sigmoidCrossEntropyWithLogits: ');\n\n  /**\n   * Implementation Details:\n   *\n   * For brevity, let `x = logits`, `z = labels`.  The logistic loss is\n   *     z * -log(sigmoid(x)) + (1 - z) * -log(1 - sigmoid(x))\n   *   = z * -log(1 / (1 + exp(-x))) + (1 - z) * -log(exp(-x) / (1 + exp(-x)))\n   *   = z * log(1 + exp(-x)) + (1 - z) * (-log(exp(-x)) + log(1 + exp(-x)))\n   *   = z * log(1 + exp(-x)) + (1 - z) * (x + log(1 + exp(-x))\n   *   = (1 - z) * x + log(1 + exp(-x))\n   *   = x - x * z + log(1 + exp(-x))\n   *\n   *   For x < 0, to avoid overflow in exp(-x), we reformulate the above\n   *     x - x * z + log(1 + exp(-x))\n   *   = log(exp(x)) - x * z + log(1 + exp(-x))\n   *   = - x * z + log(1 + exp(x))\n   *\n   * Hence, to ensure stability and avoid overflow, the implementation uses\n   * this equivalent formulation:\n   *     max(x, 0) - x * z + log(1 + exp(-abs(x)))\n   */\n  const maxOutput = relu($logits);\n  const outputXTarget = mul($logits, $labels);\n  const sigmoidOutput = log1p(exp(neg(abs($logits))));\n\n  return add(sub(maxOutput, outputXTarget), sigmoidOutput);\n}\n\n/**\n * Computes the sigmoid cross entropy loss between two tensors.\n *\n * If labelSmoothing is nonzero, smooth the labels towards 1/2:\n *\n *   newMulticlassLabels = multiclassLabels * (1 - labelSmoothing)\n *                         + 0.5 * labelSmoothing\n *\n * @param multiClassLabels The ground truth output tensor of shape\n * [batch_size, num_classes], same dimensions as 'predictions'.\n * @param logits The predicted outputs.\n * @param weights Tensor whose rank is either 0, or the same rank as\n *    `labels`, and must be broadcastable to `labels` (i.e., all dimensions\n *    must be either `1`, or the same as the corresponding `losses`\n *    dimension).\n * @param labelSmoothing If greater than 0, then smooth the labels.\n * @param reduction Type of reduction to apply to loss. Should be of type\n *    `Reduction`\n *\n * @doc { heading: 'Training', subheading: 'Losses', namespace: 'losses' }\n */\nfunction sigmoidCrossEntropy_<T extends Tensor, O extends Tensor>(\n    multiClassLabels: T|TensorLike, logits: T|TensorLike,\n    weights?: Tensor|TensorLike, labelSmoothing = 0,\n    reduction = Reduction.SUM_BY_NONZERO_WEIGHTS): O {\n  let $multiClassLabels = convertToTensor(\n      multiClassLabels, 'multiClassLabels', 'sigmoidCrossEntropy');\n  const $logits = convertToTensor(logits, 'logits', 'sigmoidCrossEntropy');\n  let $weights: Tensor = null;\n  if (weights != null) {\n    $weights = convertToTensor(weights, 'weights', 'sigmoidCrossEntropy');\n  }\n  assertShapesMatch(\n      $multiClassLabels.shape, $logits.shape, 'Error in sigmoidCrossEntropy: ');\n\n  if (labelSmoothing > 0) {\n    const labelSmoothingScalar = scalar(labelSmoothing);\n    const one = scalar(1);\n    const half = scalar(0.5);\n\n    $multiClassLabels =\n        add(mul($multiClassLabels, sub(one, labelSmoothingScalar)),\n            mul(half, labelSmoothingScalar));\n  }\n  const losses = sigmoidCrossEntropyWithLogits_($multiClassLabels, $logits);\n\n  return computeWeightedLoss(losses, $weights, reduction);\n}\n\nexport const sigmoidCrossEntropy = /* @__PURE__ */ op({sigmoidCrossEntropy_});\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAkBA,SAAQA,eAAe,QAAO,uBAAuB;AAErD,SAAQC,iBAAiB,QAAO,YAAY;AAC5C,SAAQC,GAAG,QAAO,QAAQ;AAC1B,SAAQC,GAAG,QAAO,QAAQ;AAC1B,SAAQC,GAAG,QAAO,QAAQ;AAC1B,SAAQC,KAAK,QAAO,UAAU;AAC9B,SAAQC,SAAS,QAAO,mBAAmB;AAC3C,SAAQC,GAAG,QAAO,QAAQ;AAC1B,SAAQC,GAAG,QAAO,QAAQ;AAC1B,SAAQC,EAAE,QAAO,cAAc;AAC/B,SAAQC,IAAI,QAAO,SAAS;AAC5B,SAAQC,MAAM,QAAO,WAAW;AAChC,SAAQC,GAAG,QAAO,QAAQ;AAE1B,SAAQC,mBAAmB,QAAO,yBAAyB;AAE3D,SAASC,8BAA8BA,CACnCC,MAAoB,EAAEC,MAAoB;EAC5C,MAAMC,OAAO,GACTjB,eAAe,CAACe,MAAM,EAAE,QAAQ,EAAE,+BAA+B,CAAC;EACtE,MAAMG,OAAO,GACTlB,eAAe,CAACgB,MAAM,EAAE,QAAQ,EAAE,+BAA+B,CAAC;EACtEf,iBAAiB,CACbgB,OAAO,CAACE,KAAK,EAAED,OAAO,CAACC,KAAK,EAAE,0CAA0C,CAAC;EAE7E;;;;;;;;;;;;;;;;;;;;EAoBA,MAAMC,SAAS,GAAGV,IAAI,CAACQ,OAAO,CAAC;EAC/B,MAAMG,aAAa,GAAGd,GAAG,CAACW,OAAO,EAAED,OAAO,CAAC;EAC3C,MAAMK,aAAa,GAAGjB,KAAK,CAACD,GAAG,CAACI,GAAG,CAACN,GAAG,CAACgB,OAAO,CAAC,CAAC,CAAC,CAAC;EAEnD,OAAOf,GAAG,CAACS,GAAG,CAACQ,SAAS,EAAEC,aAAa,CAAC,EAAEC,aAAa,CAAC;AAC1D;AAEA;;;;;;;;;;;;;;;;;;;;;AAqBA,SAASC,oBAAoBA,CACzBC,gBAA8B,EAAER,MAAoB,EACpDS,OAA2B,EAAEC,cAAc,GAAG,CAAC,EAC/CC,SAAS,GAAGrB,SAAS,CAACsB,sBAAsB;EAC9C,IAAIC,iBAAiB,GAAG7B,eAAe,CACnCwB,gBAAgB,EAAE,kBAAkB,EAAE,qBAAqB,CAAC;EAChE,MAAMN,OAAO,GAAGlB,eAAe,CAACgB,MAAM,EAAE,QAAQ,EAAE,qBAAqB,CAAC;EACxE,IAAIc,QAAQ,GAAW,IAAI;EAC3B,IAAIL,OAAO,IAAI,IAAI,EAAE;IACnBK,QAAQ,GAAG9B,eAAe,CAACyB,OAAO,EAAE,SAAS,EAAE,qBAAqB,CAAC;;EAEvExB,iBAAiB,CACb4B,iBAAiB,CAACV,KAAK,EAAED,OAAO,CAACC,KAAK,EAAE,gCAAgC,CAAC;EAE7E,IAAIO,cAAc,GAAG,CAAC,EAAE;IACtB,MAAMK,oBAAoB,GAAGpB,MAAM,CAACe,cAAc,CAAC;IACnD,MAAMM,GAAG,GAAGrB,MAAM,CAAC,CAAC,CAAC;IACrB,MAAMsB,IAAI,GAAGtB,MAAM,CAAC,GAAG,CAAC;IAExBkB,iBAAiB,GACb1B,GAAG,CAACI,GAAG,CAACsB,iBAAiB,EAAEjB,GAAG,CAACoB,GAAG,EAAED,oBAAoB,CAAC,CAAC,EACtDxB,GAAG,CAAC0B,IAAI,EAAEF,oBAAoB,CAAC,CAAC;;EAE1C,MAAMG,MAAM,GAAGpB,8BAA8B,CAACe,iBAAiB,EAAEX,OAAO,CAAC;EAEzE,OAAOL,mBAAmB,CAACqB,MAAM,EAAEJ,QAAQ,EAAEH,SAAS,CAAC;AACzD;AAEA,OAAO,MAAMQ,mBAAmB,GAAG,eAAgB1B,EAAE,CAAC;EAACc;AAAoB,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}