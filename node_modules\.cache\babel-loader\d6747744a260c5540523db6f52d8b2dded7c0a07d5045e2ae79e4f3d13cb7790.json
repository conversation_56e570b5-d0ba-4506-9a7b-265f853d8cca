{"ast": null, "code": "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { Relu6 } from '../kernel_names';\nimport { cast } from '../ops/cast';\nimport { lessEqual } from '../ops/less_equal';\nimport { mul } from '../ops/mul';\nimport { step } from '../ops/step';\nexport const relu6GradConfig = {\n  kernelName: Relu6,\n  inputsToSave: ['x'],\n  gradFunc: (dy, saved) => {\n    const [x] = saved;\n    const mask = mul(lessEqual(x, 6), step(x));\n    return {\n      x: () => mul(dy, cast(mask, 'float32'))\n    };\n  }\n};", "map": {"version": 3, "names": ["Relu6", "cast", "lessEqual", "mul", "step", "relu6GradConfig", "kernelName", "inputsToSave", "grad<PERSON>unc", "dy", "saved", "x", "mask"], "sources": ["C:\\tfjs-core\\src\\gradients\\Relu6_grad.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport {Relu6} from '../kernel_names';\nimport {GradConfig} from '../kernel_registry';\nimport {cast} from '../ops/cast';\nimport {lessEqual} from '../ops/less_equal';\nimport {mul} from '../ops/mul';\nimport {step} from '../ops/step';\nimport {Tensor} from '../tensor';\n\nexport const relu6GradConfig: GradConfig = {\n  kernelName: Relu6,\n  inputsToSave: ['x'],\n  gradFunc: (dy: Tensor, saved: Tensor[]) => {\n    const [x] = saved;\n    const mask = mul(lessEqual(x, 6), step(x));\n\n    return {x: () => mul(dy, cast(mask, 'float32'))};\n  }\n};\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAgBA,SAAQA,KAAK,QAAO,iBAAiB;AAErC,SAAQC,IAAI,QAAO,aAAa;AAChC,SAAQC,SAAS,QAAO,mBAAmB;AAC3C,SAAQC,GAAG,QAAO,YAAY;AAC9B,SAAQC,IAAI,QAAO,aAAa;AAGhC,OAAO,MAAMC,eAAe,GAAe;EACzCC,UAAU,EAAEN,KAAK;EACjBO,YAAY,EAAE,CAAC,GAAG,CAAC;EACnBC,QAAQ,EAAEA,CAACC,EAAU,EAAEC,KAAe,KAAI;IACxC,MAAM,CAACC,CAAC,CAAC,GAAGD,KAAK;IACjB,MAAME,IAAI,GAAGT,GAAG,CAACD,SAAS,CAACS,CAAC,EAAE,CAAC,CAAC,EAAEP,IAAI,CAACO,CAAC,CAAC,CAAC;IAE1C,OAAO;MAACA,CAAC,EAAEA,CAAA,KAAMR,GAAG,CAACM,EAAE,EAAER,IAAI,CAACW,IAAI,EAAE,SAAS,CAAC;IAAC,CAAC;EAClD;CACD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}