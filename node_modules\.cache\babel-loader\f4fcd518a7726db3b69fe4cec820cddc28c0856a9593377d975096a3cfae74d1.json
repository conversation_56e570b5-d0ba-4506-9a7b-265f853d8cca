{"ast": null, "code": "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { util } from '@tensorflow/tfjs-core';\nimport { complex } from '../kernels/Complex';\n/**\n * Generates a tensorInfo with all zeros value.\n * @param backend cpu backend.\n * @param shape Shape for the zeros tensor.\n * @param dtype Optional. If set, the result has this dtype.\n */\nexport function zeros(backend, shape) {\n  let dtype = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 'float32';\n  if (dtype === 'complex64') {\n    const real = zeros(backend, shape, 'float32');\n    const imag = zeros(backend, shape, 'float32');\n    return complex({\n      inputs: {\n        real,\n        imag\n      },\n      backend\n    });\n  }\n  const values = util.makeZerosTypedArray(util.sizeFromShape(shape), dtype);\n  return backend.makeTensorInfo(shape, dtype, values);\n}", "map": {"version": 3, "names": ["util", "complex", "zeros", "backend", "shape", "dtype", "arguments", "length", "undefined", "real", "imag", "inputs", "values", "makeZerosTypedArray", "sizeFromShape", "makeTensorInfo"], "sources": ["C:\\tfjs-backend-cpu\\src\\utils\\zeros_impl.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {DataType, TensorInfo, util} from '@tensorflow/tfjs-core';\nimport {MathBackendCPU} from '../backend_cpu';\nimport {complex} from '../kernels/Complex';\n\n/**\n * Generates a tensorInfo with all zeros value.\n * @param backend cpu backend.\n * @param shape Shape for the zeros tensor.\n * @param dtype Optional. If set, the result has this dtype.\n */\nexport function zeros(\n    backend: MathBackendCPU, shape: number[],\n    dtype: DataType = 'float32'): TensorInfo {\n  if (dtype === 'complex64') {\n    const real = zeros(backend, shape, 'float32');\n    const imag = zeros(backend, shape, 'float32');\n\n    return complex({inputs: {real, imag}, backend});\n  }\n\n  const values = util.makeZerosTypedArray(util.sizeFromShape(shape), dtype);\n\n  return backend.makeTensorInfo(shape, dtype, values);\n}\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAiBA,SAA8BA,IAAI,QAAO,uBAAuB;AAEhE,SAAQC,OAAO,QAAO,oBAAoB;AAE1C;;;;;;AAMA,OAAM,SAAUC,KAAKA,CACjBC,OAAuB,EAAEC,KAAe,EACb;EAAA,IAA3BC,KAAA,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAkB,SAAS;EAC7B,IAAID,KAAK,KAAK,WAAW,EAAE;IACzB,MAAMI,IAAI,GAAGP,KAAK,CAACC,OAAO,EAAEC,KAAK,EAAE,SAAS,CAAC;IAC7C,MAAMM,IAAI,GAAGR,KAAK,CAACC,OAAO,EAAEC,KAAK,EAAE,SAAS,CAAC;IAE7C,OAAOH,OAAO,CAAC;MAACU,MAAM,EAAE;QAACF,IAAI;QAAEC;MAAI,CAAC;MAAEP;IAAO,CAAC,CAAC;;EAGjD,MAAMS,MAAM,GAAGZ,IAAI,CAACa,mBAAmB,CAACb,IAAI,CAACc,aAAa,CAACV,KAAK,CAAC,EAAEC,KAAK,CAAC;EAEzE,OAAOF,OAAO,CAACY,cAAc,CAACX,KAAK,EAAEC,KAAK,EAAEO,MAAM,CAAC;AACrD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}