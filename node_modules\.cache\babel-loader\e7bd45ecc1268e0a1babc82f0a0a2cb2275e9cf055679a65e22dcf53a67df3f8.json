{"ast": null, "code": "/**\n * @license\n * Copyright 2018 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { util } from '@tensorflow/tfjs-core';\n/**\n * Produces GLSL code that derives logical coordinates from a flat\n * index. The code performs integer division with each stride and decrements\n * the index until the index equals the final dimension coordinate.\n */\nexport function getLogicalCoordinatesFromFlatIndex(coords, shape) {\n  let index = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 'index';\n  const strides = util.computeStrides(shape);\n  return strides.map((stride, i) => {\n    const line1 = \"int \".concat(coords[i], \" = \").concat(index, \" / \").concat(stride);\n    const line2 = i === strides.length - 1 ? \"int \".concat(coords[i + 1], \" = \").concat(index, \" - \").concat(coords[i], \" * \").concat(stride) : \"index -= \".concat(coords[i], \" * \").concat(stride);\n    return \"\".concat(line1, \"; \").concat(line2, \";\");\n  }).join('');\n}\nexport function getOutputLogicalCoordinatesFromFlatIndexByUniform(coords, shape) {\n  let index = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 'index';\n  const strides = util.computeStrides(shape);\n  return strides.map((_, i) => {\n    const line1 = \"int \".concat(coords[i], \" = \").concat(index, \" / outShapeStrides[\").concat(i, \"]\");\n    const line2 = i === strides.length - 1 ? \"int \".concat(coords[i + 1], \" = \").concat(index, \" - \").concat(coords[i], \" * outShapeStrides[\").concat(i, \"]\") : \"index -= \".concat(coords[i], \" * outShapeStrides[\").concat(i, \"]\");\n    return \"\".concat(line1, \"; \").concat(line2, \";\");\n  }).join('');\n}\n// Produces GLSL code that computes strides.\nfunction symbolicallyComputeStrides(indicesArr, variableName) {\n  const numCoords = indicesArr.length;\n  const shape = indicesArr.map(d => \"\".concat(variableName, \"[\").concat(d, \"]\"));\n  const strides = new Array(numCoords - 1);\n  strides[numCoords - 2] = shape[numCoords - 1];\n  for (let i = numCoords - 3; i >= 0; --i) {\n    strides[i] = \"(\".concat(strides[i + 1], \" * \").concat(shape[i + 1], \")\");\n  }\n  return strides;\n}\nexport function getLogicalCoordinatesFromFlatIndexByUniform(coords, variableName) {\n  let index = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 'index';\n  const indicesArray = coords.map((_, i) => i);\n  const strides = symbolicallyComputeStrides(indicesArray, variableName);\n  return strides.map((_, i) => {\n    const line1 = \"int \".concat(coords[i], \" = \").concat(index, \" / \").concat(strides[i]);\n    const line2 = i === strides.length - 1 ? \"int \".concat(coords[i + 1], \" = \").concat(index, \" - \").concat(coords[i], \" * \").concat(strides[i]) : \"index -= \".concat(coords[i], \" * \").concat(strides[i]);\n    return \"\".concat(line1, \"; \").concat(line2, \";\");\n  }).join('');\n}\nfunction buildVec(x) {\n  if (x.length === 1) {\n    return \"\".concat(x[0]);\n  }\n  return \"vec\".concat(x.length, \"(\").concat(x.join(','), \")\");\n}\n/**\n * Produces GLSL code that computes the dot product of the input x and y\n * vectors. Handles splitting inputs into increments of vec4s when necessary.\n */\nexport function dotify(x, y) {\n  if (x.length !== y.length) {\n    throw new Error(\"Vectors to be dotted must be of the same length -\" + \"got \".concat(x.length, \" and \").concat(y.length));\n  }\n  const slices = [];\n  const nearestVec4 = Math.floor(x.length / 4);\n  const nearestVec4Remainder = x.length % 4;\n  for (let i = 0; i < nearestVec4; i++) {\n    const xSlice = x.slice(i * 4, i * 4 + 4);\n    const ySlice = y.slice(i * 4, i * 4 + 4);\n    slices.push(\"\".concat(buildVec(xSlice), \", \").concat(buildVec(ySlice)));\n  }\n  if (nearestVec4Remainder !== 0) {\n    let xSlice = x.slice(nearestVec4 * 4);\n    let ySlice = y.slice(nearestVec4 * 4);\n    if (xSlice.length === 1) {\n      xSlice = xSlice.map(d => \"float(\".concat(d, \")\"));\n      ySlice = ySlice.map(d => \"float(\".concat(d, \")\"));\n    }\n    slices.push(\"\".concat(buildVec(xSlice), \", \").concat(buildVec(ySlice)));\n  }\n  return slices.map((d, i) => \"dot(\".concat(d, \")\")).join('+');\n}\n/**\n * Produces GLSL that computes the flat index from 3D coordinates.\n */\nexport function getFlatIndexFrom3D(shape) {\n  const strides = util.computeStrides(shape).map(d => d.toString());\n  return \"\\n  int getFlatIndex(ivec3 coords) {\\n    return coords.x * \".concat(strides[0], \" + coords.y * \").concat(strides[1], \" + coords.z;\\n  }\\n\");\n}\nexport function getFlatIndexFrom3DOutput() {\n  return \"\\n  int getFlatIndex(ivec3 coords) {\\n    return coords.x * outShapeStrides[0] + coords.y * outShapeStrides[1] + coords.z;\\n  }\\n\";\n}\nexport const ENCODE_FLOAT_SNIPPET = \"\\n  const float FLOAT_MAX = 1.70141184e38;\\n  const float FLOAT_MIN = 1.17549435e-38;\\n\\n  lowp vec4 encode_float(highp float v) {\\n    if (isnan(v)) {\\n      return vec4(255, 255, 255, 255);\\n    }\\n\\n    highp float av = abs(v);\\n\\n    if(av < FLOAT_MIN) {\\n      return vec4(0.0, 0.0, 0.0, 0.0);\\n    } else if(v > FLOAT_MAX) {\\n      return vec4(0.0, 0.0, 128.0, 127.0) / 255.0;\\n    } else if(v < -FLOAT_MAX) {\\n      return vec4(0.0, 0.0,  128.0, 255.0) / 255.0;\\n    }\\n\\n    highp vec4 c = vec4(0,0,0,0);\\n\\n    highp float e = floor(log2(av));\\n    highp float m = exp2(fract(log2(av))) - 1.0;\\n\\n    c[2] = floor(128.0 * m);\\n    m -= c[2] / 128.0;\\n    c[1] = floor(32768.0 * m);\\n    m -= c[1] / 32768.0;\\n    c[0] = floor(8388608.0 * m);\\n\\n    highp float ebias = e + 127.0;\\n    c[3] = floor(ebias / 2.0);\\n    ebias -= c[3] * 2.0;\\n    c[2] += floor(ebias) * 128.0;\\n\\n    c[3] += 128.0 * step(0.0, -v);\\n\\n    return c / 255.0;\\n  }\\n\";", "map": {"version": 3, "names": ["util", "getLogicalCoordinatesFromFlatIndex", "coords", "shape", "index", "arguments", "length", "undefined", "strides", "computeStrides", "map", "stride", "i", "line1", "concat", "line2", "join", "getOutputLogicalCoordinatesFromFlatIndexByUniform", "_", "symbolicallyComputeStrides", "indicesArr", "variableName", "numCoords", "d", "Array", "getLogicalCoordinatesFromFlatIndexByUniform", "indicesArray", "buildVec", "x", "dotify", "y", "Error", "slices", "nearestVec4", "Math", "floor", "nearestVec4Remainder", "xSlice", "slice", "ySlice", "push", "getFlatIndexFrom3D", "toString", "getFlatIndexFrom3DOutput", "ENCODE_FLOAT_SNIPPET"], "sources": ["C:\\tfjs-backend-webgl\\src\\shader_compiler_util.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2018 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {util} from '@tensorflow/tfjs-core';\n\n/**\n * Produces GLSL code that derives logical coordinates from a flat\n * index. The code performs integer division with each stride and decrements\n * the index until the index equals the final dimension coordinate.\n */\nexport function getLogicalCoordinatesFromFlatIndex(\n    coords: string[], shape: number[], index = 'index'): string {\n  const strides = util.computeStrides(shape);\n  return strides\n      .map((stride, i) => {\n        const line1 = `int ${coords[i]} = ${index} / ${stride}`;\n        const line2 = i === strides.length - 1 ?\n            `int ${coords[i + 1]} = ${index} - ${coords[i]} * ${stride}` :\n            `index -= ${coords[i]} * ${stride}`;\n        return `${line1}; ${line2};`;\n      })\n      .join('');\n}\n\nexport function getOutputLogicalCoordinatesFromFlatIndexByUniform(\n    coords: string[], shape: number[], index = 'index'): string {\n  const strides = util.computeStrides(shape);\n  return strides\n      .map((_, i) => {\n        const line1 = `int ${coords[i]} = ${index} / outShapeStrides[${i}]`;\n        const line2 = i === strides.length - 1 ?\n            `int ${coords[i + 1]} = ${index} - ${coords[i]} * outShapeStrides[${\n                i}]` :\n            `index -= ${coords[i]} * outShapeStrides[${i}]`;\n        return `${line1}; ${line2};`;\n      })\n      .join('');\n}\n\n// Produces GLSL code that computes strides.\nfunction symbolicallyComputeStrides(\n    indicesArr: number[], variableName: string): string[] {\n  const numCoords = indicesArr.length;\n  const shape = indicesArr.map(d => `${variableName}[${d}]`);\n  const strides = new Array(numCoords - 1);\n  strides[numCoords - 2] = shape[numCoords - 1];\n  for (let i = numCoords - 3; i >= 0; --i) {\n    strides[i] = `(${strides[i + 1]} * ${shape[i + 1]})`;\n  }\n\n  return strides;\n}\n\nexport function getLogicalCoordinatesFromFlatIndexByUniform(\n    coords: string[], variableName: string, index = 'index'): string {\n  const indicesArray = coords.map((_, i) => i);\n  const strides = symbolicallyComputeStrides(indicesArray, variableName);\n  return strides\n      .map((_, i) => {\n        const line1 = `int ${coords[i]} = ${index} / ${strides[i]}`;\n        const line2 = i === strides.length - 1 ?\n            `int ${coords[i + 1]} = ${index} - ${coords[i]} * ${strides[i]}` :\n            `index -= ${coords[i]} * ${strides[i]}`;\n        return `${line1}; ${line2};`;\n      })\n      .join('');\n}\n\nfunction buildVec(x: string[]): string {\n  if (x.length === 1) {\n    return `${x[0]}`;\n  }\n  return `vec${x.length}(${x.join(',')})`;\n}\n\n/**\n * Produces GLSL code that computes the dot product of the input x and y\n * vectors. Handles splitting inputs into increments of vec4s when necessary.\n */\nexport function dotify(x: string[], y: string[]): string {\n  if (x.length !== y.length) {\n    throw new Error(\n        `Vectors to be dotted must be of the same length -` +\n        `got ${x.length} and ${y.length}`);\n  }\n\n  const slices: string[] = [];\n  const nearestVec4 = Math.floor(x.length / 4);\n  const nearestVec4Remainder = x.length % 4;\n\n  for (let i = 0; i < nearestVec4; i++) {\n    const xSlice = x.slice(i * 4, i * 4 + 4);\n    const ySlice = y.slice(i * 4, i * 4 + 4);\n    slices.push(`${buildVec(xSlice)}, ${buildVec(ySlice)}`);\n  }\n\n  if (nearestVec4Remainder !== 0) {\n    let xSlice = x.slice(nearestVec4 * 4);\n    let ySlice = y.slice(nearestVec4 * 4);\n    if (xSlice.length === 1) {\n      xSlice = xSlice.map(d => `float(${d})`);\n      ySlice = ySlice.map(d => `float(${d})`);\n    }\n    slices.push(`${buildVec(xSlice)}, ${buildVec(ySlice)}`);\n  }\n\n  return slices.map((d, i) => `dot(${d})`).join('+');\n}\n\n/**\n * Produces GLSL that computes the flat index from 3D coordinates.\n */\nexport function getFlatIndexFrom3D(shape: [number, number, number]): string {\n  const strides = util.computeStrides(shape).map(d => d.toString());\n\n  return `\n  int getFlatIndex(ivec3 coords) {\n    return coords.x * ${strides[0]} + coords.y * ${strides[1]} + coords.z;\n  }\n`;\n}\n\nexport function getFlatIndexFrom3DOutput(): string {\n  return `\n  int getFlatIndex(ivec3 coords) {\n    return coords.x * outShapeStrides[0] + coords.y * outShapeStrides[1] + coords.z;\n  }\n`;\n}\n\nexport const ENCODE_FLOAT_SNIPPET = `\n  const float FLOAT_MAX = 1.70141184e38;\n  const float FLOAT_MIN = 1.17549435e-38;\n\n  lowp vec4 encode_float(highp float v) {\n    if (isnan(v)) {\n      return vec4(255, 255, 255, 255);\n    }\n\n    highp float av = abs(v);\n\n    if(av < FLOAT_MIN) {\n      return vec4(0.0, 0.0, 0.0, 0.0);\n    } else if(v > FLOAT_MAX) {\n      return vec4(0.0, 0.0, 128.0, 127.0) / 255.0;\n    } else if(v < -FLOAT_MAX) {\n      return vec4(0.0, 0.0,  128.0, 255.0) / 255.0;\n    }\n\n    highp vec4 c = vec4(0,0,0,0);\n\n    highp float e = floor(log2(av));\n    highp float m = exp2(fract(log2(av))) - 1.0;\n\n    c[2] = floor(128.0 * m);\n    m -= c[2] / 128.0;\n    c[1] = floor(32768.0 * m);\n    m -= c[1] / 32768.0;\n    c[0] = floor(8388608.0 * m);\n\n    highp float ebias = e + 127.0;\n    c[3] = floor(ebias / 2.0);\n    ebias -= c[3] * 2.0;\n    c[2] += floor(ebias) * 128.0;\n\n    c[3] += 128.0 * step(0.0, -v);\n\n    return c / 255.0;\n  }\n`;\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAiBA,SAAQA,IAAI,QAAO,uBAAuB;AAE1C;;;;;AAKA,OAAM,SAAUC,kCAAkCA,CAC9CC,MAAgB,EAAEC,KAAe,EAAiB;EAAA,IAAfC,KAAK,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,OAAO;EACpD,MAAMG,OAAO,GAAGR,IAAI,CAACS,cAAc,CAACN,KAAK,CAAC;EAC1C,OAAOK,OAAO,CACTE,GAAG,CAAC,CAACC,MAAM,EAAEC,CAAC,KAAI;IACjB,MAAMC,KAAK,UAAAC,MAAA,CAAUZ,MAAM,CAACU,CAAC,CAAC,SAAAE,MAAA,CAAMV,KAAK,SAAAU,MAAA,CAAMH,MAAM,CAAE;IACvD,MAAMI,KAAK,GAAGH,CAAC,KAAKJ,OAAO,CAACF,MAAM,GAAG,CAAC,UAAAQ,MAAA,CAC3BZ,MAAM,CAACU,CAAC,GAAG,CAAC,CAAC,SAAAE,MAAA,CAAMV,KAAK,SAAAU,MAAA,CAAMZ,MAAM,CAACU,CAAC,CAAC,SAAAE,MAAA,CAAMH,MAAM,gBAAAG,MAAA,CAC9CZ,MAAM,CAACU,CAAC,CAAC,SAAAE,MAAA,CAAMH,MAAM,CAAE;IACvC,UAAAG,MAAA,CAAUD,KAAK,QAAAC,MAAA,CAAKC,KAAK;EAC3B,CAAC,CAAC,CACDC,IAAI,CAAC,EAAE,CAAC;AACf;AAEA,OAAM,SAAUC,iDAAiDA,CAC7Df,MAAgB,EAAEC,KAAe,EAAiB;EAAA,IAAfC,KAAK,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,OAAO;EACpD,MAAMG,OAAO,GAAGR,IAAI,CAACS,cAAc,CAACN,KAAK,CAAC;EAC1C,OAAOK,OAAO,CACTE,GAAG,CAAC,CAACQ,CAAC,EAAEN,CAAC,KAAI;IACZ,MAAMC,KAAK,UAAAC,MAAA,CAAUZ,MAAM,CAACU,CAAC,CAAC,SAAAE,MAAA,CAAMV,KAAK,yBAAAU,MAAA,CAAsBF,CAAC,MAAG;IACnE,MAAMG,KAAK,GAAGH,CAAC,KAAKJ,OAAO,CAACF,MAAM,GAAG,CAAC,UAAAQ,MAAA,CAC3BZ,MAAM,CAACU,CAAC,GAAG,CAAC,CAAC,SAAAE,MAAA,CAAMV,KAAK,SAAAU,MAAA,CAAMZ,MAAM,CAACU,CAAC,CAAC,yBAAAE,MAAA,CAC1CF,CAAC,qBAAAE,MAAA,CACOZ,MAAM,CAACU,CAAC,CAAC,yBAAAE,MAAA,CAAsBF,CAAC,MAAG;IACnD,UAAAE,MAAA,CAAUD,KAAK,QAAAC,MAAA,CAAKC,KAAK;EAC3B,CAAC,CAAC,CACDC,IAAI,CAAC,EAAE,CAAC;AACf;AAEA;AACA,SAASG,0BAA0BA,CAC/BC,UAAoB,EAAEC,YAAoB;EAC5C,MAAMC,SAAS,GAAGF,UAAU,CAACd,MAAM;EACnC,MAAMH,KAAK,GAAGiB,UAAU,CAACV,GAAG,CAACa,CAAC,OAAAT,MAAA,CAAOO,YAAY,OAAAP,MAAA,CAAIS,CAAC,MAAG,CAAC;EAC1D,MAAMf,OAAO,GAAG,IAAIgB,KAAK,CAACF,SAAS,GAAG,CAAC,CAAC;EACxCd,OAAO,CAACc,SAAS,GAAG,CAAC,CAAC,GAAGnB,KAAK,CAACmB,SAAS,GAAG,CAAC,CAAC;EAC7C,KAAK,IAAIV,CAAC,GAAGU,SAAS,GAAG,CAAC,EAAEV,CAAC,IAAI,CAAC,EAAE,EAAEA,CAAC,EAAE;IACvCJ,OAAO,CAACI,CAAC,CAAC,OAAAE,MAAA,CAAON,OAAO,CAACI,CAAC,GAAG,CAAC,CAAC,SAAAE,MAAA,CAAMX,KAAK,CAACS,CAAC,GAAG,CAAC,CAAC,MAAG;;EAGtD,OAAOJ,OAAO;AAChB;AAEA,OAAM,SAAUiB,2CAA2CA,CACvDvB,MAAgB,EAAEmB,YAAoB,EAAiB;EAAA,IAAfjB,KAAK,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,OAAO;EACzD,MAAMqB,YAAY,GAAGxB,MAAM,CAACQ,GAAG,CAAC,CAACQ,CAAC,EAAEN,CAAC,KAAKA,CAAC,CAAC;EAC5C,MAAMJ,OAAO,GAAGW,0BAA0B,CAACO,YAAY,EAAEL,YAAY,CAAC;EACtE,OAAOb,OAAO,CACTE,GAAG,CAAC,CAACQ,CAAC,EAAEN,CAAC,KAAI;IACZ,MAAMC,KAAK,UAAAC,MAAA,CAAUZ,MAAM,CAACU,CAAC,CAAC,SAAAE,MAAA,CAAMV,KAAK,SAAAU,MAAA,CAAMN,OAAO,CAACI,CAAC,CAAC,CAAE;IAC3D,MAAMG,KAAK,GAAGH,CAAC,KAAKJ,OAAO,CAACF,MAAM,GAAG,CAAC,UAAAQ,MAAA,CAC3BZ,MAAM,CAACU,CAAC,GAAG,CAAC,CAAC,SAAAE,MAAA,CAAMV,KAAK,SAAAU,MAAA,CAAMZ,MAAM,CAACU,CAAC,CAAC,SAAAE,MAAA,CAAMN,OAAO,CAACI,CAAC,CAAC,gBAAAE,MAAA,CAClDZ,MAAM,CAACU,CAAC,CAAC,SAAAE,MAAA,CAAMN,OAAO,CAACI,CAAC,CAAC,CAAE;IAC3C,UAAAE,MAAA,CAAUD,KAAK,QAAAC,MAAA,CAAKC,KAAK;EAC3B,CAAC,CAAC,CACDC,IAAI,CAAC,EAAE,CAAC;AACf;AAEA,SAASW,QAAQA,CAACC,CAAW;EAC3B,IAAIA,CAAC,CAACtB,MAAM,KAAK,CAAC,EAAE;IAClB,UAAAQ,MAAA,CAAUc,CAAC,CAAC,CAAC,CAAC;;EAEhB,aAAAd,MAAA,CAAac,CAAC,CAACtB,MAAM,OAAAQ,MAAA,CAAIc,CAAC,CAACZ,IAAI,CAAC,GAAG,CAAC;AACtC;AAEA;;;;AAIA,OAAM,SAAUa,MAAMA,CAACD,CAAW,EAAEE,CAAW;EAC7C,IAAIF,CAAC,CAACtB,MAAM,KAAKwB,CAAC,CAACxB,MAAM,EAAE;IACzB,MAAM,IAAIyB,KAAK,CACX,6DAAAjB,MAAA,CACOc,CAAC,CAACtB,MAAM,WAAAQ,MAAA,CAAQgB,CAAC,CAACxB,MAAM,CAAE,CAAC;;EAGxC,MAAM0B,MAAM,GAAa,EAAE;EAC3B,MAAMC,WAAW,GAAGC,IAAI,CAACC,KAAK,CAACP,CAAC,CAACtB,MAAM,GAAG,CAAC,CAAC;EAC5C,MAAM8B,oBAAoB,GAAGR,CAAC,CAACtB,MAAM,GAAG,CAAC;EAEzC,KAAK,IAAIM,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGqB,WAAW,EAAErB,CAAC,EAAE,EAAE;IACpC,MAAMyB,MAAM,GAAGT,CAAC,CAACU,KAAK,CAAC1B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;IACxC,MAAM2B,MAAM,GAAGT,CAAC,CAACQ,KAAK,CAAC1B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;IACxCoB,MAAM,CAACQ,IAAI,IAAA1B,MAAA,CAAIa,QAAQ,CAACU,MAAM,CAAC,QAAAvB,MAAA,CAAKa,QAAQ,CAACY,MAAM,CAAC,CAAE,CAAC;;EAGzD,IAAIH,oBAAoB,KAAK,CAAC,EAAE;IAC9B,IAAIC,MAAM,GAAGT,CAAC,CAACU,KAAK,CAACL,WAAW,GAAG,CAAC,CAAC;IACrC,IAAIM,MAAM,GAAGT,CAAC,CAACQ,KAAK,CAACL,WAAW,GAAG,CAAC,CAAC;IACrC,IAAII,MAAM,CAAC/B,MAAM,KAAK,CAAC,EAAE;MACvB+B,MAAM,GAAGA,MAAM,CAAC3B,GAAG,CAACa,CAAC,aAAAT,MAAA,CAAaS,CAAC,MAAG,CAAC;MACvCgB,MAAM,GAAGA,MAAM,CAAC7B,GAAG,CAACa,CAAC,aAAAT,MAAA,CAAaS,CAAC,MAAG,CAAC;;IAEzCS,MAAM,CAACQ,IAAI,IAAA1B,MAAA,CAAIa,QAAQ,CAACU,MAAM,CAAC,QAAAvB,MAAA,CAAKa,QAAQ,CAACY,MAAM,CAAC,CAAE,CAAC;;EAGzD,OAAOP,MAAM,CAACtB,GAAG,CAAC,CAACa,CAAC,EAAEX,CAAC,YAAAE,MAAA,CAAYS,CAAC,MAAG,CAAC,CAACP,IAAI,CAAC,GAAG,CAAC;AACpD;AAEA;;;AAGA,OAAM,SAAUyB,kBAAkBA,CAACtC,KAA+B;EAChE,MAAMK,OAAO,GAAGR,IAAI,CAACS,cAAc,CAACN,KAAK,CAAC,CAACO,GAAG,CAACa,CAAC,IAAIA,CAAC,CAACmB,QAAQ,EAAE,CAAC;EAEjE,sEAAA5B,MAAA,CAEsBN,OAAO,CAAC,CAAC,CAAC,oBAAAM,MAAA,CAAiBN,OAAO,CAAC,CAAC,CAAC;AAG7D;AAEA,OAAM,SAAUmC,wBAAwBA,CAAA;EACtC;AAKF;AAEA,OAAO,MAAMC,oBAAoB,47BAuChC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}