{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.transformReply = exports.transformArguments = exports.IS_READ_ONLY = exports.FIRST_KEY_INDEX = void 0;\nconst _1 = require(\".\");\nexports.FIRST_KEY_INDEX = 1;\nexports.IS_READ_ONLY = true;\nfunction transformArguments(key, options) {\n  return (0, _1.pushLatestArgument)(['TS.GET', key], options?.LATEST);\n}\nexports.transformArguments = transformArguments;\nfunction transformReply(reply) {\n  if (reply.length === 0) return null;\n  return (0, _1.transformSampleReply)(reply);\n}\nexports.transformReply = transformReply;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "transformReply", "transformArguments", "IS_READ_ONLY", "FIRST_KEY_INDEX", "_1", "require", "key", "options", "pushLatestArgument", "LATEST", "reply", "length", "transformSampleReply"], "sources": ["C:/tmsft/node_modules/@redis/time-series/dist/commands/GET.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.transformReply = exports.transformArguments = exports.IS_READ_ONLY = exports.FIRST_KEY_INDEX = void 0;\nconst _1 = require(\".\");\nexports.FIRST_KEY_INDEX = 1;\nexports.IS_READ_ONLY = true;\nfunction transformArguments(key, options) {\n    return (0, _1.pushLatestArgument)(['TS.GET', key], options?.LATEST);\n}\nexports.transformArguments = transformArguments;\nfunction transformReply(reply) {\n    if (reply.length === 0)\n        return null;\n    return (0, _1.transformSampleReply)(reply);\n}\nexports.transformReply = transformReply;\n"], "mappings": "AAAA,YAAY;;AACZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEC,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7DD,OAAO,CAACE,cAAc,GAAGF,OAAO,CAACG,kBAAkB,GAAGH,OAAO,CAACI,YAAY,GAAGJ,OAAO,CAACK,eAAe,GAAG,KAAK,CAAC;AAC7G,MAAMC,EAAE,GAAGC,OAAO,CAAC,GAAG,CAAC;AACvBP,OAAO,CAACK,eAAe,GAAG,CAAC;AAC3BL,OAAO,CAACI,YAAY,GAAG,IAAI;AAC3B,SAASD,kBAAkBA,CAACK,GAAG,EAAEC,OAAO,EAAE;EACtC,OAAO,CAAC,CAAC,EAAEH,EAAE,CAACI,kBAAkB,EAAE,CAAC,QAAQ,EAAEF,GAAG,CAAC,EAAEC,OAAO,EAAEE,MAAM,CAAC;AACvE;AACAX,OAAO,CAACG,kBAAkB,GAAGA,kBAAkB;AAC/C,SAASD,cAAcA,CAACU,KAAK,EAAE;EAC3B,IAAIA,KAAK,CAACC,MAAM,KAAK,CAAC,EAClB,OAAO,IAAI;EACf,OAAO,CAAC,CAAC,EAAEP,EAAE,CAACQ,oBAAoB,EAAEF,KAAK,CAAC;AAC9C;AACAZ,OAAO,CAACE,cAAc,GAAGA,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}