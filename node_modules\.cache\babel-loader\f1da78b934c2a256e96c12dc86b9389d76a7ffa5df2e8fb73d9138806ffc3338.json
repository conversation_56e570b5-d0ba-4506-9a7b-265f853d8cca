{"ast": null, "code": "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { Reciprocal } from '../kernel_names';\nimport { div } from '../ops/div';\nimport { neg } from '../ops/neg';\nimport { square } from '../ops/square';\nexport const reciprocalGradConfig = {\n  kernelName: Reciprocal,\n  inputsToSave: ['x'],\n  gradFunc: (dy, saved) => {\n    const [x] = saved;\n    return {\n      x: () => div(dy, neg(square(x)))\n    };\n  }\n};", "map": {"version": 3, "names": ["Reciprocal", "div", "neg", "square", "reciprocalGradConfig", "kernelName", "inputsToSave", "grad<PERSON>unc", "dy", "saved", "x"], "sources": ["C:\\tfjs-core\\src\\gradients\\Reciprocal_grad.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {Reciprocal} from '../kernel_names';\nimport {GradConfig} from '../kernel_registry';\nimport {div} from '../ops/div';\nimport {neg} from '../ops/neg';\nimport {square} from '../ops/square';\nimport {Tensor} from '../tensor';\n\nexport const reciprocalGradConfig: GradConfig = {\n  kernelName: Reciprocal,\n  inputsToSave: ['x'],\n  gradFunc: (dy: Tensor, saved: Tensor[]) => {\n    const [x] = saved;\n    return {x: () => div(dy, neg(square(x)))};\n  }\n};\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAiBA,SAAQA,UAAU,QAAO,iBAAiB;AAE1C,SAAQC,GAAG,QAAO,YAAY;AAC9B,SAAQC,GAAG,QAAO,YAAY;AAC9B,SAAQC,MAAM,QAAO,eAAe;AAGpC,OAAO,MAAMC,oBAAoB,GAAe;EAC9CC,UAAU,EAAEL,UAAU;EACtBM,YAAY,EAAE,CAAC,GAAG,CAAC;EACnBC,QAAQ,EAAEA,CAACC,EAAU,EAAEC,KAAe,KAAI;IACxC,MAAM,CAACC,CAAC,CAAC,GAAGD,KAAK;IACjB,OAAO;MAACC,CAAC,EAAEA,CAAA,KAAMT,GAAG,CAACO,EAAE,EAAEN,GAAG,CAACC,MAAM,CAACO,CAAC,CAAC,CAAC;IAAC,CAAC;EAC3C;CACD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}