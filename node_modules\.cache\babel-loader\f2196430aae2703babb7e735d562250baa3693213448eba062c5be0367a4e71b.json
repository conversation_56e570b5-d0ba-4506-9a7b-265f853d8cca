{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.transformArguments = exports.IS_READ_ONLY = void 0;\nexports.IS_READ_ONLY = true;\nfunction transformArguments(pattern) {\n  const args = ['PUBSUB', 'CHANNELS'];\n  if (pattern) {\n    args.push(pattern);\n  }\n  return args;\n}\nexports.transformArguments = transformArguments;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "transformArguments", "IS_READ_ONLY", "pattern", "args", "push"], "sources": ["C:/tmsft/node_modules/@redis/client/dist/lib/commands/PUBSUB_CHANNELS.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.transformArguments = exports.IS_READ_ONLY = void 0;\nexports.IS_READ_ONLY = true;\nfunction transformArguments(pattern) {\n    const args = ['PUBSUB', 'CHANNELS'];\n    if (pattern) {\n        args.push(pattern);\n    }\n    return args;\n}\nexports.transformArguments = transformArguments;\n"], "mappings": "AAAA,YAAY;;AACZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEC,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7DD,OAAO,CAACE,kBAAkB,GAAGF,OAAO,CAACG,YAAY,GAAG,KAAK,CAAC;AAC1DH,OAAO,CAACG,YAAY,GAAG,IAAI;AAC3B,SAASD,kBAAkBA,CAACE,OAAO,EAAE;EACjC,MAAMC,IAAI,GAAG,CAAC,QAAQ,EAAE,UAAU,CAAC;EACnC,IAAID,OAAO,EAAE;IACTC,IAAI,CAACC,IAAI,CAACF,OAAO,CAAC;EACtB;EACA,OAAOC,IAAI;AACf;AACAL,OAAO,CAACE,kBAAkB,GAAGA,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}