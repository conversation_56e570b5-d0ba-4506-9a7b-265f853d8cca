{"ast": null, "code": "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { LinSpace } from '@tensorflow/tfjs-core';\nimport { linSpaceImpl } from './LinSpace_impl';\nexport function linSpace(args) {\n  const {\n    backend,\n    attrs\n  } = args;\n  const {\n    start,\n    stop,\n    num\n  } = attrs;\n  const outVals = linSpaceImpl(start, stop, num);\n  return backend.makeTensorInfo([outVals.length], 'float32', outVals);\n}\nexport const linSpaceConfig = {\n  kernelName: LinSpace,\n  backendName: 'cpu',\n  kernelFunc: linSpace\n};", "map": {"version": 3, "names": ["LinSpace", "linSpaceImpl", "linSpace", "args", "backend", "attrs", "start", "stop", "num", "outVals", "makeTensorInfo", "length", "linSpaceConfig", "kernelName", "backendName", "kernelFunc"], "sources": ["C:\\tfjs-backend-cpu\\src\\kernels\\LinSpace.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {KernelConfig, KernelFunc, LinSpace, LinSpaceAttrs, TensorInfo} from '@tensorflow/tfjs-core';\n\nimport {MathBackendCPU} from '../backend_cpu';\nimport {linSpaceImpl} from './LinSpace_impl';\n\nexport function linSpace(args: {backend: MathBackendCPU, attrs: LinSpaceAttrs}):\n    TensorInfo {\n  const {backend, attrs} = args;\n  const {start, stop, num} = attrs;\n\n  const outVals = linSpaceImpl(start, stop, num);\n\n  return backend.makeTensorInfo([outVals.length], 'float32', outVals);\n}\n\nexport const linSpaceConfig: KernelConfig = {\n  kernelName: LinSpace,\n  backendName: 'cpu',\n  kernelFunc: linSpace as unknown as KernelFunc\n};\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAiBA,SAAkCA,QAAQ,QAAkC,uBAAuB;AAGnG,SAAQC,YAAY,QAAO,iBAAiB;AAE5C,OAAM,SAAUC,QAAQA,CAACC,IAAqD;EAE5E,MAAM;IAACC,OAAO;IAAEC;EAAK,CAAC,GAAGF,IAAI;EAC7B,MAAM;IAACG,KAAK;IAAEC,IAAI;IAAEC;EAAG,CAAC,GAAGH,KAAK;EAEhC,MAAMI,OAAO,GAAGR,YAAY,CAACK,KAAK,EAAEC,IAAI,EAAEC,GAAG,CAAC;EAE9C,OAAOJ,OAAO,CAACM,cAAc,CAAC,CAACD,OAAO,CAACE,MAAM,CAAC,EAAE,SAAS,EAAEF,OAAO,CAAC;AACrE;AAEA,OAAO,MAAMG,cAAc,GAAiB;EAC1CC,UAAU,EAAEb,QAAQ;EACpBc,WAAW,EAAE,KAAK;EAClBC,UAAU,EAAEb;CACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}