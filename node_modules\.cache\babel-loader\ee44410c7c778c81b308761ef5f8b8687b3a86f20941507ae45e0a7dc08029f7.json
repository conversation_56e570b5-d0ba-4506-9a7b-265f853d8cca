{"ast": null, "code": "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { assertNonNegativeIntegerDimensions } from '../util_base';\nimport { buffer } from './buffer';\nimport { op } from './operation';\nimport { MPRandGauss } from './rand_util';\n/**\n * Creates a `tf.Tensor` with values sampled from a truncated normal\n * distribution.\n *\n * ```js\n * tf.truncatedNormal([2, 2]).print();\n * ```\n *\n * The generated values follow a normal distribution with specified mean and\n * standard deviation, except that values whose magnitude is more than 2\n * standard deviations from the mean are dropped and re-picked.\n *\n * @param shape An array of integers defining the output tensor shape.\n * @param mean The mean of the normal distribution.\n * @param stdDev The standard deviation of the normal distribution.\n * @param dtype The data type of the output tensor.\n * @param seed The seed for the random number generator.\n *\n * @doc {heading: 'Tensors', subheading: 'Creation'}\n */\nfunction truncatedNormal_(shape) {\n  let mean = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 0;\n  let stdDev = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 1;\n  let dtype = arguments.length > 3 ? arguments[3] : undefined;\n  let seed = arguments.length > 4 ? arguments[4] : undefined;\n  assertNonNegativeIntegerDimensions(shape);\n  if (dtype != null && dtype === 'bool') {\n    throw new Error(\"Unsupported data type $ { dtype }\");\n  }\n  const randGauss = new MPRandGauss(mean, stdDev, dtype, true /* truncated */, seed);\n  const res = buffer(shape, dtype);\n  for (let i = 0; i < res.values.length; i++) {\n    res.values[i] = randGauss.nextValue();\n  }\n  return res.toTensor();\n}\nexport const truncatedNormal = /* @__PURE__ */op({\n  truncatedNormal_\n});", "map": {"version": 3, "names": ["assertNonNegativeIntegerDimensions", "buffer", "op", "MP<PERSON>and<PERSON><PERSON><PERSON>", "truncatedNormal_", "shape", "mean", "arguments", "length", "undefined", "stdDev", "dtype", "seed", "Error", "rand<PERSON><PERSON><PERSON>", "res", "i", "values", "nextValue", "toTensor", "truncatedNormal"], "sources": ["C:\\tfjs-core\\src\\ops\\truncated_normal.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {Tensor} from '../tensor';\nimport {DataType, Rank, ShapeMap} from '../types';\nimport {assertNonNegativeIntegerDimensions} from '../util_base';\n\nimport {buffer} from './buffer';\nimport {op} from './operation';\nimport {MPRandGauss} from './rand_util';\n\n/**\n * Creates a `tf.Tensor` with values sampled from a truncated normal\n * distribution.\n *\n * ```js\n * tf.truncatedNormal([2, 2]).print();\n * ```\n *\n * The generated values follow a normal distribution with specified mean and\n * standard deviation, except that values whose magnitude is more than 2\n * standard deviations from the mean are dropped and re-picked.\n *\n * @param shape An array of integers defining the output tensor shape.\n * @param mean The mean of the normal distribution.\n * @param stdDev The standard deviation of the normal distribution.\n * @param dtype The data type of the output tensor.\n * @param seed The seed for the random number generator.\n *\n * @doc {heading: 'Tensors', subheading: 'Creation'}\n */\nfunction truncatedNormal_<R extends Rank>(\n    shape: ShapeMap[R], mean = 0, stdDev = 1, dtype?: 'float32'|'int32',\n    seed?: number): Tensor<R> {\n  assertNonNegativeIntegerDimensions(shape);\n  if (dtype != null && (dtype as DataType) === 'bool') {\n    throw new Error(`Unsupported data type $ { dtype }`);\n  }\n  const randGauss =\n      new MPRandGauss(mean, stdDev, dtype, true /* truncated */, seed);\n  const res = buffer(shape, dtype);\n  for (let i = 0; i < res.values.length; i++) {\n    res.values[i] = randGauss.nextValue();\n  }\n  return res.toTensor();\n}\n\nexport const truncatedNormal = /* @__PURE__ */ op({truncatedNormal_});\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAmBA,SAAQA,kCAAkC,QAAO,cAAc;AAE/D,SAAQC,MAAM,QAAO,UAAU;AAC/B,SAAQC,EAAE,QAAO,aAAa;AAC9B,SAAQC,WAAW,QAAO,aAAa;AAEvC;;;;;;;;;;;;;;;;;;;;AAoBA,SAASC,gBAAgBA,CACrBC,KAAkB,EACL;EAAA,IADOC,IAAI,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC;EAAA,IAAEG,MAAM,GAAAH,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC;EAAA,IAAEI,KAAyB,GAAAJ,SAAA,CAAAC,MAAA,OAAAD,SAAA,MAAAE,SAAA;EAAA,IACnEG,IAAa,GAAAL,SAAA,CAAAC,MAAA,OAAAD,SAAA,MAAAE,SAAA;EACfT,kCAAkC,CAACK,KAAK,CAAC;EACzC,IAAIM,KAAK,IAAI,IAAI,IAAKA,KAAkB,KAAK,MAAM,EAAE;IACnD,MAAM,IAAIE,KAAK,oCAAoC,CAAC;;EAEtD,MAAMC,SAAS,GACX,IAAIX,WAAW,CAACG,IAAI,EAAEI,MAAM,EAAEC,KAAK,EAAE,IAAI,CAAC,iBAAiBC,IAAI,CAAC;EACpE,MAAMG,GAAG,GAAGd,MAAM,CAACI,KAAK,EAAEM,KAAK,CAAC;EAChC,KAAK,IAAIK,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,GAAG,CAACE,MAAM,CAACT,MAAM,EAAEQ,CAAC,EAAE,EAAE;IAC1CD,GAAG,CAACE,MAAM,CAACD,CAAC,CAAC,GAAGF,SAAS,CAACI,SAAS,EAAE;;EAEvC,OAAOH,GAAG,CAACI,QAAQ,EAAE;AACvB;AAEA,OAAO,MAAMC,eAAe,GAAG,eAAgBlB,EAAE,CAAC;EAACE;AAAgB,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}