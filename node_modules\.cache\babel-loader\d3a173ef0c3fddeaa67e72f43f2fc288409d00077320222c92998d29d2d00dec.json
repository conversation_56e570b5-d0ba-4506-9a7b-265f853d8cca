{"ast": null, "code": "/**\n * @license\n * Copyright 2018 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n// tslint:disable-next-line: no-imports-from-dist\nimport * as tfOps from '@tensorflow/tfjs-core/dist/ops/ops_for_converter';\nimport { getParamValue } from './utils';\nexport const executeOp = function (node, tensorMap, context) {\n  let ops = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : tfOps;\n  switch (node.op) {\n    case 'BiasAdd':\n    case 'AddV2':\n    case 'Add':\n      {\n        return [ops.add(getParamValue('a', node, tensorMap, context), getParamValue('b', node, tensorMap, context))];\n      }\n    case 'AddN':\n      {\n        return [ops.addN(getParamValue('tensors', node, tensorMap, context))];\n      }\n    case 'FloorMod':\n    case 'Mod':\n      return [ops.mod(getParamValue('a', node, tensorMap, context), getParamValue('b', node, tensorMap, context))];\n    case 'Mul':\n      return [ops.mul(getParamValue('a', node, tensorMap, context), getParamValue('b', node, tensorMap, context))];\n    case 'RealDiv':\n    case 'Div':\n      {\n        return [ops.div(getParamValue('a', node, tensorMap, context), getParamValue('b', node, tensorMap, context))];\n      }\n    case 'DivNoNan':\n      {\n        return [ops.divNoNan(getParamValue('a', node, tensorMap, context), getParamValue('b', node, tensorMap, context))];\n      }\n    case 'FloorDiv':\n      {\n        return [ops.floorDiv(getParamValue('a', node, tensorMap, context), getParamValue('b', node, tensorMap, context))];\n      }\n    case 'Sub':\n      {\n        return [ops.sub(getParamValue('a', node, tensorMap, context), getParamValue('b', node, tensorMap, context))];\n      }\n    case 'Minimum':\n      {\n        return [ops.minimum(getParamValue('a', node, tensorMap, context), getParamValue('b', node, tensorMap, context))];\n      }\n    case 'Maximum':\n      {\n        return [ops.maximum(getParamValue('a', node, tensorMap, context), getParamValue('b', node, tensorMap, context))];\n      }\n    case 'Pow':\n      {\n        return [ops.pow(getParamValue('a', node, tensorMap, context), getParamValue('b', node, tensorMap, context))];\n      }\n    case 'SquaredDifference':\n      {\n        return [ops.squaredDifference(getParamValue('a', node, tensorMap, context), getParamValue('b', node, tensorMap, context))];\n      }\n    default:\n      throw TypeError(\"Node type \".concat(node.op, \" is not implemented\"));\n  }\n};\nexport const CATEGORY = 'arithmetic';", "map": {"version": 3, "names": ["tfOps", "getParamValue", "executeOp", "node", "tensorMap", "context", "ops", "arguments", "length", "undefined", "op", "add", "addN", "mod", "mul", "div", "divNoNan", "floorDiv", "sub", "minimum", "maximum", "pow", "squaredDifference", "TypeError", "concat", "CATEGORY"], "sources": ["C:\\tfjs-converter\\src\\operations\\executors\\arithmetic_executor.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2018 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {Tensor} from '@tensorflow/tfjs-core';\n// tslint:disable-next-line: no-imports-from-dist\nimport * as tfOps from '@tensorflow/tfjs-core/dist/ops/ops_for_converter';\n\nimport {NamedTensorsMap} from '../../data/types';\nimport {ExecutionContext} from '../../executor/execution_context';\nimport {InternalOpExecutor, Node} from '../types';\n\nimport {getParamValue} from './utils';\n\nexport const executeOp: InternalOpExecutor =\n    (node: Node, tensorMap: NamedTensorsMap,\n     context: ExecutionContext, ops = tfOps): Tensor[] => {\n      switch (node.op) {\n        case 'BiasAdd':\n        case 'AddV2':\n        case 'Add': {\n          return [ops.add(\n              (getParamValue('a', node, tensorMap, context) as Tensor),\n              getParamValue('b', node, tensorMap, context) as Tensor)];\n        }\n        case 'AddN': {\n          return [ops.addN((\n              getParamValue('tensors', node, tensorMap, context) as Tensor[]))];\n        }\n        case 'FloorMod':\n        case 'Mod':\n          return [ops.mod(\n              getParamValue('a', node, tensorMap, context) as Tensor,\n              getParamValue('b', node, tensorMap, context) as Tensor)];\n        case 'Mul':\n          return [ops.mul(\n              getParamValue('a', node, tensorMap, context) as Tensor,\n              getParamValue('b', node, tensorMap, context) as Tensor)];\n        case 'RealDiv':\n        case 'Div': {\n          return [ops.div(\n              getParamValue('a', node, tensorMap, context) as Tensor,\n              getParamValue('b', node, tensorMap, context) as Tensor)];\n        }\n        case 'DivNoNan': {\n          return [ops.divNoNan(\n              getParamValue('a', node, tensorMap, context) as Tensor,\n              getParamValue('b', node, tensorMap, context) as Tensor)];\n        }\n        case 'FloorDiv': {\n          return [ops.floorDiv(\n              getParamValue('a', node, tensorMap, context) as Tensor,\n              getParamValue('b', node, tensorMap, context) as Tensor)];\n        }\n        case 'Sub': {\n          return [ops.sub(\n              getParamValue('a', node, tensorMap, context) as Tensor,\n              getParamValue('b', node, tensorMap, context) as Tensor)];\n        }\n        case 'Minimum': {\n          return [ops.minimum(\n              getParamValue('a', node, tensorMap, context) as Tensor,\n              getParamValue('b', node, tensorMap, context) as Tensor)];\n        }\n        case 'Maximum': {\n          return [ops.maximum(\n              getParamValue('a', node, tensorMap, context) as Tensor,\n              getParamValue('b', node, tensorMap, context) as Tensor)];\n        }\n        case 'Pow': {\n          return [ops.pow(\n              getParamValue('a', node, tensorMap, context) as Tensor,\n              getParamValue('b', node, tensorMap, context) as Tensor)];\n        }\n        case 'SquaredDifference': {\n          return [ops.squaredDifference(\n              getParamValue('a', node, tensorMap, context) as Tensor,\n              getParamValue('b', node, tensorMap, context) as Tensor)];\n        }\n        default:\n          throw TypeError(`Node type ${node.op} is not implemented`);\n      }\n    };\n\nexport const CATEGORY = 'arithmetic';\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAkBA;AACA,OAAO,KAAKA,KAAK,MAAM,kDAAkD;AAMzE,SAAQC,aAAa,QAAO,SAAS;AAErC,OAAO,MAAMC,SAAS,GAClB,SAAAA,CAACC,IAAU,EAAEC,SAA0B,EACtCC,OAAyB,EAA2B;EAAA,IAAzBC,GAAG,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAGP,KAAK;EACrC,QAAQG,IAAI,CAACO,EAAE;IACb,KAAK,SAAS;IACd,KAAK,OAAO;IACZ,KAAK,KAAK;MAAE;QACV,OAAO,CAACJ,GAAG,CAACK,GAAG,CACVV,aAAa,CAAC,GAAG,EAAEE,IAAI,EAAEC,SAAS,EAAEC,OAAO,CAAY,EACxDJ,aAAa,CAAC,GAAG,EAAEE,IAAI,EAAEC,SAAS,EAAEC,OAAO,CAAW,CAAC,CAAC;;IAE9D,KAAK,MAAM;MAAE;QACX,OAAO,CAACC,GAAG,CAACM,IAAI,CACZX,aAAa,CAAC,SAAS,EAAEE,IAAI,EAAEC,SAAS,EAAEC,OAAO,CAAc,CAAC,CAAC;;IAEvE,KAAK,UAAU;IACf,KAAK,KAAK;MACR,OAAO,CAACC,GAAG,CAACO,GAAG,CACXZ,aAAa,CAAC,GAAG,EAAEE,IAAI,EAAEC,SAAS,EAAEC,OAAO,CAAW,EACtDJ,aAAa,CAAC,GAAG,EAAEE,IAAI,EAAEC,SAAS,EAAEC,OAAO,CAAW,CAAC,CAAC;IAC9D,KAAK,KAAK;MACR,OAAO,CAACC,GAAG,CAACQ,GAAG,CACXb,aAAa,CAAC,GAAG,EAAEE,IAAI,EAAEC,SAAS,EAAEC,OAAO,CAAW,EACtDJ,aAAa,CAAC,GAAG,EAAEE,IAAI,EAAEC,SAAS,EAAEC,OAAO,CAAW,CAAC,CAAC;IAC9D,KAAK,SAAS;IACd,KAAK,KAAK;MAAE;QACV,OAAO,CAACC,GAAG,CAACS,GAAG,CACXd,aAAa,CAAC,GAAG,EAAEE,IAAI,EAAEC,SAAS,EAAEC,OAAO,CAAW,EACtDJ,aAAa,CAAC,GAAG,EAAEE,IAAI,EAAEC,SAAS,EAAEC,OAAO,CAAW,CAAC,CAAC;;IAE9D,KAAK,UAAU;MAAE;QACf,OAAO,CAACC,GAAG,CAACU,QAAQ,CAChBf,aAAa,CAAC,GAAG,EAAEE,IAAI,EAAEC,SAAS,EAAEC,OAAO,CAAW,EACtDJ,aAAa,CAAC,GAAG,EAAEE,IAAI,EAAEC,SAAS,EAAEC,OAAO,CAAW,CAAC,CAAC;;IAE9D,KAAK,UAAU;MAAE;QACf,OAAO,CAACC,GAAG,CAACW,QAAQ,CAChBhB,aAAa,CAAC,GAAG,EAAEE,IAAI,EAAEC,SAAS,EAAEC,OAAO,CAAW,EACtDJ,aAAa,CAAC,GAAG,EAAEE,IAAI,EAAEC,SAAS,EAAEC,OAAO,CAAW,CAAC,CAAC;;IAE9D,KAAK,KAAK;MAAE;QACV,OAAO,CAACC,GAAG,CAACY,GAAG,CACXjB,aAAa,CAAC,GAAG,EAAEE,IAAI,EAAEC,SAAS,EAAEC,OAAO,CAAW,EACtDJ,aAAa,CAAC,GAAG,EAAEE,IAAI,EAAEC,SAAS,EAAEC,OAAO,CAAW,CAAC,CAAC;;IAE9D,KAAK,SAAS;MAAE;QACd,OAAO,CAACC,GAAG,CAACa,OAAO,CACflB,aAAa,CAAC,GAAG,EAAEE,IAAI,EAAEC,SAAS,EAAEC,OAAO,CAAW,EACtDJ,aAAa,CAAC,GAAG,EAAEE,IAAI,EAAEC,SAAS,EAAEC,OAAO,CAAW,CAAC,CAAC;;IAE9D,KAAK,SAAS;MAAE;QACd,OAAO,CAACC,GAAG,CAACc,OAAO,CACfnB,aAAa,CAAC,GAAG,EAAEE,IAAI,EAAEC,SAAS,EAAEC,OAAO,CAAW,EACtDJ,aAAa,CAAC,GAAG,EAAEE,IAAI,EAAEC,SAAS,EAAEC,OAAO,CAAW,CAAC,CAAC;;IAE9D,KAAK,KAAK;MAAE;QACV,OAAO,CAACC,GAAG,CAACe,GAAG,CACXpB,aAAa,CAAC,GAAG,EAAEE,IAAI,EAAEC,SAAS,EAAEC,OAAO,CAAW,EACtDJ,aAAa,CAAC,GAAG,EAAEE,IAAI,EAAEC,SAAS,EAAEC,OAAO,CAAW,CAAC,CAAC;;IAE9D,KAAK,mBAAmB;MAAE;QACxB,OAAO,CAACC,GAAG,CAACgB,iBAAiB,CACzBrB,aAAa,CAAC,GAAG,EAAEE,IAAI,EAAEC,SAAS,EAAEC,OAAO,CAAW,EACtDJ,aAAa,CAAC,GAAG,EAAEE,IAAI,EAAEC,SAAS,EAAEC,OAAO,CAAW,CAAC,CAAC;;IAE9D;MACE,MAAMkB,SAAS,cAAAC,MAAA,CAAcrB,IAAI,CAACO,EAAE,wBAAqB,CAAC;;AAEhE,CAAC;AAEL,OAAO,MAAMe,QAAQ,GAAG,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}