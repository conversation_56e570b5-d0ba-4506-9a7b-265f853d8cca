{"ast": null, "code": "import { getCoordsDataType } from './shader_compiler';\nexport class GatherNDProgram {\n  constructor(sliceDim, strides, shape, paramsShape) {\n    this.sliceDim = sliceDim;\n    this.strides = strides;\n    this.paramsShape = paramsShape;\n    this.variableNames = ['x', 'indices'];\n    this.outputShape = shape;\n    const dtype = getCoordsDataType(shape.length);\n    let mainLoop = `\n    int index;`;\n    for (let j = 0; j < this.sliceDim; j++) {\n      mainLoop += `\n          index = round(getIndices(coords[0], ${j}));\n          out_of_bounds = out_of_bounds || index < 0;\n          out_of_bounds = out_of_bounds || index >= ${this.paramsShape[j]};\n          flattenIndex += index * ${this.strides[j]};`;\n    }\n    this.userCode = `\n         void main() {\n          ${dtype} coords = getOutputCoords();\n          int flattenIndex = 0;\n          bool out_of_bounds = false;\n\n          ${mainLoop}\n\n          setOutput(out_of_bounds ? 0.0 : getX(flattenIndex, coords[1]));\n        }\n      `;\n  }\n}", "map": {"version": 3, "names": ["getCoordsDataType", "GatherNDProgram", "constructor", "sliceDim", "strides", "shape", "params<PERSON>hape", "variableNames", "outputShape", "dtype", "length", "mainLoop", "j", "userCode"], "sources": ["C:\\tfjs-backend-webgl\\src\\gather_nd_gpu.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2018 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport {GPGPUProgram} from './gpgpu_math';\nimport {getCoordsDataType} from './shader_compiler';\n\nexport class GatherNDProgram implements GPGPUProgram {\n  variableNames = ['x', 'indices'];\n  outputShape: number[];\n  userCode: string;\n  constructor(\n      private sliceDim: number, private strides: number[], shape: number[],\n      private paramsShape: number[]) {\n    this.outputShape = shape;\n    const dtype = getCoordsDataType(shape.length);\n\n    let mainLoop = `\n    int index;`;\n    for (let j = 0; j < this.sliceDim; j++) {\n      mainLoop += `\n          index = round(getIndices(coords[0], ${j}));\n          out_of_bounds = out_of_bounds || index < 0;\n          out_of_bounds = out_of_bounds || index >= ${this.paramsShape[j]};\n          flattenIndex += index * ${this.strides[j]};`;\n    }\n\n    this.userCode = `\n         void main() {\n          ${dtype} coords = getOutputCoords();\n          int flattenIndex = 0;\n          bool out_of_bounds = false;\n\n          ${mainLoop}\n\n          setOutput(out_of_bounds ? 0.0 : getX(flattenIndex, coords[1]));\n        }\n      `;\n  }\n}\n"], "mappings": "AAiBA,SAAQA,iBAAiB,QAAO,mBAAmB;AAEnD,OAAM,MAAOC,eAAe;EAI1BC,YACYC,QAAgB,EAAUC,OAAiB,EAAEC,KAAe,EAC5DC,WAAqB;IADrB,KAAAH,QAAQ,GAARA,QAAQ;IAAkB,KAAAC,OAAO,GAAPA,OAAO;IACjC,KAAAE,WAAW,GAAXA,WAAW;IALvB,KAAAC,aAAa,GAAG,CAAC,GAAG,EAAE,SAAS,CAAC;IAM9B,IAAI,CAACC,WAAW,GAAGH,KAAK;IACxB,MAAMI,KAAK,GAAGT,iBAAiB,CAACK,KAAK,CAACK,MAAM,CAAC;IAE7C,IAAIC,QAAQ,GAAG;eACJ;IACX,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACT,QAAQ,EAAES,CAAC,EAAE,EAAE;MACtCD,QAAQ,IAAI;gDAC8BC,CAAC;;sDAEK,IAAI,CAACN,WAAW,CAACM,CAAC,CAAC;oCACrC,IAAI,CAACR,OAAO,CAACQ,CAAC,CAAC,GAAG;;IAGlD,IAAI,CAACC,QAAQ,GAAG;;YAERJ,KAAK;;;;YAILE,QAAQ;;;;OAIb;EACL", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}