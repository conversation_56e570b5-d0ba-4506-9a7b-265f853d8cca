{"ast": null, "code": "var _jsxFileName = \"C:\\\\tmsft\\\\src\\\\components\\\\TransactionCategorization.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useCallback, useMemo } from 'react';\nimport { transactionStorageService } from '../services/transactionStorageService';\nimport { categorizationService } from '../services/categorizationService';\nimport { mlCategorizationService } from '../services/mlCategorizationService';\nimport './TransactionCategorization.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport const TransactionCategorization = ({\n  refreshTrigger\n}) => {\n  _s();\n  // State management\n  const [transactions, setTransactions] = useState([]);\n  const [categories, setCategories] = useState([]);\n  const [categorizations, setCategorizations] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [processingML, setProcessingML] = useState(false);\n  const [mlProgress, setMLProgress] = useState({\n    current: 0,\n    total: 0\n  });\n  const [selectedTransactions, setSelectedTransactions] = useState(new Set());\n\n  // View and filter state\n  const [viewMode, setViewMode] = useState('uncategorized');\n  const [sortField, setSortField] = useState('date');\n  const [sortDirection, setSortDirection] = useState('desc');\n  const [categoryFilter] = useState({\n    categoryId: '',\n    method: 'all',\n    confidenceMin: 0,\n    confidenceMax: 1\n  });\n\n  // ML Configuration state\n  const [mlConfig] = useState({\n    confidenceThreshold: 0.7,\n    batchSize: 10,\n    autoApplyHighConfidence: true\n  });\n\n  // Modal state\n  const [showMLConfigModal, setShowMLConfigModal] = useState(false);\n  const [showCategoryModal, setShowCategoryModal] = useState(false);\n\n  // Load data\n  const loadData = useCallback(async () => {\n    try {\n      setLoading(true);\n\n      // Load all data\n      const [allTransactions, allCategories, allCategorizations] = await Promise.all([Promise.resolve(transactionStorageService.getAllTransactions()), Promise.resolve(categorizationService.getAllCategories()), Promise.resolve(categorizationService.getAllCategorizations())]);\n      setTransactions(allTransactions);\n      setCategories(allCategories);\n      setCategorizations(allCategorizations);\n    } catch (error) {\n      console.error('Failed to load categorization data:', error);\n    } finally {\n      setLoading(false);\n    }\n  }, []);\n  useEffect(() => {\n    loadData();\n  }, [loadData, refreshTrigger]);\n\n  // Get categorization data for a transaction\n  const getTransactionCategorization = useCallback(transactionId => {\n    return categorizations.find(cat => cat.transactionId === transactionId);\n  }, [categorizations]);\n\n  // Get category by ID\n  const getCategoryById = useCallback(categoryId => {\n    return categories.find(cat => cat.id === categoryId);\n  }, [categories]);\n\n  // Filter and sort transactions based on current settings\n  const filteredTransactions = useMemo(() => {\n    let filtered = [...transactions];\n\n    // Apply view mode filter\n    switch (viewMode) {\n      case 'uncategorized':\n        filtered = filtered.filter(t => !getTransactionCategorization(t.id));\n        break;\n      case 'ml-pending':\n        filtered = filtered.filter(t => {\n          const cat = getTransactionCategorization(t.id);\n          return cat && cat.method === 'ml' && (cat.confidence || 0) < mlConfig.confidenceThreshold;\n        });\n        break;\n      case 'low-confidence':\n        filtered = filtered.filter(t => {\n          const cat = getTransactionCategorization(t.id);\n          return cat && cat.method === 'ml' && (cat.confidence || 0) < 0.8;\n        });\n        break;\n      case 'all':\n      default:\n        // No additional filtering\n        break;\n    }\n\n    // Apply category filter\n    if (categoryFilter.categoryId) {\n      filtered = filtered.filter(t => {\n        const cat = getTransactionCategorization(t.id);\n        return cat && cat.categoryId === categoryFilter.categoryId;\n      });\n    }\n\n    // Apply method filter\n    if (categoryFilter.method !== 'all') {\n      filtered = filtered.filter(t => {\n        const cat = getTransactionCategorization(t.id);\n        return cat && cat.method === categoryFilter.method;\n      });\n    }\n\n    // Apply confidence filter\n    filtered = filtered.filter(t => {\n      const cat = getTransactionCategorization(t.id);\n      if (!cat || cat.method !== 'ml') return true; // Include non-ML transactions\n      const confidence = cat.confidence || 0;\n      return confidence >= categoryFilter.confidenceMin && confidence <= categoryFilter.confidenceMax;\n    });\n\n    // Sort transactions\n    filtered.sort((a, b) => {\n      var _getTransactionCatego, _getTransactionCatego2;\n      let aValue;\n      let bValue;\n      switch (sortField) {\n        case 'date':\n          aValue = new Date(a.postDateTime).getTime();\n          bValue = new Date(b.postDateTime).getTime();\n          break;\n        case 'amount':\n          aValue = Math.abs(a.debitAmount || a.creditAmount || 0);\n          bValue = Math.abs(b.debitAmount || b.creditAmount || 0);\n          break;\n        case 'confidence':\n          aValue = ((_getTransactionCatego = getTransactionCategorization(a.id)) === null || _getTransactionCatego === void 0 ? void 0 : _getTransactionCatego.confidence) || 0;\n          bValue = ((_getTransactionCatego2 = getTransactionCategorization(b.id)) === null || _getTransactionCatego2 === void 0 ? void 0 : _getTransactionCatego2.confidence) || 0;\n          break;\n        case 'description':\n        default:\n          aValue = a.description.toLowerCase();\n          bValue = b.description.toLowerCase();\n          break;\n      }\n      if (aValue < bValue) return sortDirection === 'asc' ? -1 : 1;\n      if (aValue > bValue) return sortDirection === 'asc' ? 1 : -1;\n      return 0;\n    });\n    return filtered;\n  }, [transactions, categorizations, viewMode, categoryFilter, sortField, sortDirection, mlConfig.confidenceThreshold, getTransactionCategorization]);\n\n  // Handle manual categorization\n  const handleManualCategorization = useCallback(async (transactionId, categoryId) => {\n    try {\n      categorizationService.categorizeTransaction(transactionId, categoryId, 'manual');\n\n      // Refresh categorizations\n      const updatedCategorizations = categorizationService.getAllCategorizations();\n      setCategorizations(updatedCategorizations);\n\n      // Clear selection\n      setSelectedTransactions(prev => {\n        const newSet = new Set(prev);\n        newSet.delete(transactionId);\n        return newSet;\n      });\n    } catch (error) {\n      console.error('Failed to categorize transaction:', error);\n    }\n  }, []);\n\n  // Handle batch ML categorization\n  const handleMLCategorization = useCallback(async transactionIds => {\n    try {\n      setProcessingML(true);\n      const transactionsToProcess = transactionIds ? transactions.filter(t => transactionIds.includes(t.id)) : filteredTransactions.filter(t => !getTransactionCategorization(t.id));\n      setMLProgress({\n        current: 0,\n        total: transactionsToProcess.length\n      });\n\n      // Convert StoredTransaction to Transaction format for ML service\n      const mlTransactions = transactionsToProcess.map(t => ({\n        id: t.id,\n        date: t.postDateTime,\n        description: t.description,\n        debitAmount: t.debitAmount || 0,\n        creditAmount: t.creditAmount || 0,\n        balance: t.balance,\n        reference: t.reference\n      }));\n      const results = await mlCategorizationService.categorizeTransactionsBatch(mlTransactions);\n\n      // Update progress and categorizations\n      let processed = 0;\n      for (const result of results) {\n        processed++;\n        setMLProgress({\n          current: processed,\n          total: transactionsToProcess.length\n        });\n        if (result.result && result.result.confidence >= mlConfig.confidenceThreshold && mlConfig.autoApplyHighConfidence) {\n          // High confidence results are automatically applied\n          continue; // Already applied by ML service\n        }\n      }\n\n      // Refresh categorizations\n      const updatedCategorizations = categorizationService.getAllCategorizations();\n      setCategorizations(updatedCategorizations);\n    } catch (error) {\n      console.error('ML categorization failed:', error);\n    } finally {\n      setProcessingML(false);\n      setMLProgress({\n        current: 0,\n        total: 0\n      });\n    }\n  }, [transactions, filteredTransactions, mlConfig, getTransactionCategorization]);\n\n  // Format currency\n  const formatCurrency = useCallback(amount => {\n    return new Intl.NumberFormat('en-US', {\n      style: 'currency',\n      currency: 'USD',\n      minimumFractionDigits: 2,\n      maximumFractionDigits: 2\n    }).format(amount);\n  }, []);\n\n  // Format confidence as percentage\n  const formatConfidence = useCallback(confidence => {\n    if (confidence === undefined) return 'N/A';\n    return `${Math.round(confidence * 100)}%`;\n  }, []);\n\n  // Get confidence color\n  const getConfidenceColor = useCallback(confidence => {\n    if (confidence === undefined) return '#6B7280';\n    if (confidence >= 0.9) return '#10B981';\n    if (confidence >= 0.7) return '#F59E0B';\n    return '#EF4444';\n  }, []);\n\n  // Statistics\n  const stats = useMemo(() => {\n    const total = transactions.length;\n    const categorized = categorizations.length;\n    const uncategorized = total - categorized;\n    const mlCategorized = categorizations.filter(c => c.method === 'ml').length;\n    const manualCategorized = categorizations.filter(c => c.method === 'manual').length;\n    const averageMLConfidence = categorizations.filter(c => c.method === 'ml' && c.confidence !== undefined).reduce((sum, c, _, arr) => sum + (c.confidence || 0) / arr.length, 0);\n    return {\n      total,\n      categorized,\n      uncategorized,\n      mlCategorized,\n      manualCategorized,\n      averageMLConfidence,\n      categorizationRate: total > 0 ? categorized / total * 100 : 0\n    };\n  }, [transactions, categorizations]);\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"categorization-loading\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"loading-spinner\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 292,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"Loading transaction categorization data...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 293,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 291,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"transaction-categorization\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"categorization-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"categorization-title\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          children: \"Transaction Categorization\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 302,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Categorize transactions manually or use AI-powered categorization with Qwen 3\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 303,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 301,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"categorization-stats\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-card\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat-value\",\n            children: stats.total\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 308,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat-label\",\n            children: \"Total Transactions\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 309,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 307,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-card\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat-value\",\n            children: stats.categorized\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 312,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat-label\",\n            children: \"Categorized\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 313,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 311,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-card\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat-value\",\n            children: stats.uncategorized\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 316,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat-label\",\n            children: \"Uncategorized\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 317,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 315,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-card\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat-value\",\n            children: [stats.categorizationRate.toFixed(1), \"%\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 320,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat-label\",\n            children: \"Completion Rate\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 321,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 319,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-card\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat-value\",\n            children: formatConfidence(stats.averageMLConfidence)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 324,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat-label\",\n            children: \"Avg ML Confidence\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 325,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 323,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 306,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 300,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"categorization-controls\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"view-modes\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          className: `view-mode-btn ${viewMode === 'uncategorized' ? 'active' : ''}`,\n          onClick: () => setViewMode('uncategorized'),\n          children: [\"Uncategorized (\", stats.uncategorized, \")\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 332,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: `view-mode-btn ${viewMode === 'all' ? 'active' : ''}`,\n          onClick: () => setViewMode('all'),\n          children: \"All Transactions\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 338,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: `view-mode-btn ${viewMode === 'ml-pending' ? 'active' : ''}`,\n          onClick: () => setViewMode('ml-pending'),\n          children: \"ML Pending Review\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 344,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: `view-mode-btn ${viewMode === 'low-confidence' ? 'active' : ''}`,\n          onClick: () => setViewMode('low-confidence'),\n          children: \"Low Confidence\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 350,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 331,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"action-buttons\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"btn btn-primary\",\n          onClick: () => handleMLCategorization(),\n          disabled: processingML,\n          children: processingML ? `Processing ${mlProgress.current}/${mlProgress.total}...` : 'Run ML Categorization'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 359,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"btn btn-secondary\",\n          onClick: () => setShowMLConfigModal(true),\n          children: \"ML Settings\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 366,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"btn btn-secondary\",\n          onClick: () => setShowCategoryModal(true),\n          children: \"Manage Categories\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 372,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 358,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 330,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"transactions-table-container\",\n      children: /*#__PURE__*/_jsxDEV(\"table\", {\n        className: \"transactions-table\",\n        children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n          children: /*#__PURE__*/_jsxDEV(\"tr\", {\n            children: [/*#__PURE__*/_jsxDEV(\"th\", {\n              children: /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"checkbox\",\n                onChange: e => {\n                  if (e.target.checked) {\n                    setSelectedTransactions(new Set(filteredTransactions.map(t => t.id)));\n                  } else {\n                    setSelectedTransactions(new Set());\n                  }\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 386,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 385,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              className: `sortable ${sortField === 'date' ? sortDirection : ''}`,\n              onClick: () => {\n                if (sortField === 'date') {\n                  setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');\n                } else {\n                  setSortField('date');\n                  setSortDirection('desc');\n                }\n              },\n              children: \"Date\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 397,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              className: `sortable ${sortField === 'description' ? sortDirection : ''}`,\n              onClick: () => {\n                if (sortField === 'description') {\n                  setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');\n                } else {\n                  setSortField('description');\n                  setSortDirection('asc');\n                }\n              },\n              children: \"Description\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 410,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              className: `sortable ${sortField === 'amount' ? sortDirection : ''}`,\n              onClick: () => {\n                if (sortField === 'amount') {\n                  setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');\n                } else {\n                  setSortField('amount');\n                  setSortDirection('desc');\n                }\n              },\n              children: \"Amount\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 423,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              children: \"Category\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 436,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              className: `sortable ${sortField === 'confidence' ? sortDirection : ''}`,\n              onClick: () => {\n                if (sortField === 'confidence') {\n                  setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');\n                } else {\n                  setSortField('confidence');\n                  setSortDirection('desc');\n                }\n              },\n              children: \"Confidence\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 437,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              children: \"Actions\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 450,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 384,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 383,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n          children: filteredTransactions.map(transaction => {\n            const categorization = getTransactionCategorization(transaction.id);\n            const category = categorization ? getCategoryById(categorization.categoryId) : undefined;\n            const amount = transaction.debitAmount ? -transaction.debitAmount : transaction.creditAmount || 0;\n            return /*#__PURE__*/_jsxDEV(\"tr\", {\n              className: selectedTransactions.has(transaction.id) ? 'selected' : '',\n              children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                children: /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"checkbox\",\n                  checked: selectedTransactions.has(transaction.id),\n                  onChange: e => {\n                    const newSet = new Set(selectedTransactions);\n                    if (e.target.checked) {\n                      newSet.add(transaction.id);\n                    } else {\n                      newSet.delete(transaction.id);\n                    }\n                    setSelectedTransactions(newSet);\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 462,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 461,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: new Date(transaction.postDateTime).toLocaleDateString()\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 476,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"description-cell\",\n                title: transaction.description,\n                children: transaction.description\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 477,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: `amount-cell ${amount < 0 ? 'debit' : 'credit'}`,\n                children: formatCurrency(Math.abs(amount))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 480,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: category ? /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"category-tag\",\n                  style: {\n                    backgroundColor: category.color\n                  },\n                  children: [category.name, (categorization === null || categorization === void 0 ? void 0 : categorization.method) && /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"category-method\",\n                    children: categorization.method.toUpperCase()\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 488,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 485,\n                  columnNumber: 23\n                }, this) : /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"uncategorized\",\n                  children: \"Uncategorized\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 492,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 483,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: (categorization === null || categorization === void 0 ? void 0 : categorization.confidence) !== undefined ? /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"confidence-score\",\n                  style: {\n                    color: getConfidenceColor(categorization.confidence)\n                  },\n                  children: formatConfidence(categorization.confidence)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 497,\n                  columnNumber: 23\n                }, this) : /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"no-confidence\",\n                  children: \"-\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 504,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 495,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"action-buttons\",\n                  children: /*#__PURE__*/_jsxDEV(\"select\", {\n                    value: (categorization === null || categorization === void 0 ? void 0 : categorization.categoryId) || '',\n                    onChange: e => {\n                      if (e.target.value) {\n                        handleManualCategorization(transaction.id, e.target.value);\n                      }\n                    },\n                    className: \"category-select\",\n                    children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"\",\n                      children: \"Select Category...\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 518,\n                      columnNumber: 25\n                    }, this), categories.map(cat => /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: cat.id,\n                      children: cat.name\n                    }, cat.id, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 520,\n                      columnNumber: 27\n                    }, this))]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 509,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 508,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 507,\n                columnNumber: 19\n              }, this)]\n            }, transaction.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 460,\n              columnNumber: 17\n            }, this);\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 453,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 382,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 381,\n      columnNumber: 7\n    }, this), selectedTransactions.size > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"batch-actions\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"batch-info\",\n        children: [selectedTransactions.size, \" transaction(s) selected\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 534,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"btn btn-primary\",\n        onClick: () => handleMLCategorization(Array.from(selectedTransactions)),\n        disabled: processingML,\n        children: \"Categorize Selected with ML\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 537,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"btn btn-secondary\",\n        onClick: () => setSelectedTransactions(new Set()),\n        children: \"Clear Selection\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 544,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 533,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 299,\n    columnNumber: 5\n  }, this);\n};\n_s(TransactionCategorization, \"YDJjZQpWtIVried0w5VIhNjceSs=\");\n_c = TransactionCategorization;\nvar _c;\n$RefreshReg$(_c, \"TransactionCategorization\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useCallback", "useMemo", "transactionStorageService", "categorizationService", "mlCategorizationService", "jsxDEV", "_jsxDEV", "TransactionCategorization", "refreshTrigger", "_s", "transactions", "setTransactions", "categories", "setCategories", "categorizations", "setCategorizations", "loading", "setLoading", "processingML", "setProcessingML", "mlProgress", "setMLProgress", "current", "total", "selectedTransactions", "setSelectedTransactions", "Set", "viewMode", "setViewMode", "sortField", "setSortField", "sortDirection", "setSortDirection", "categoryFilter", "categoryId", "method", "confidenceMin", "confidenceMax", "mlConfig", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "batchSize", "autoApplyHighConfidence", "showMLConfigModal", "setShowMLConfigModal", "showCategoryModal", "setShowCategoryModal", "loadData", "allTransactions", "allCategories", "allCategorizations", "Promise", "all", "resolve", "getAllTransactions", "getAllCategories", "getAllCategorizations", "error", "console", "getTransactionCategorization", "transactionId", "find", "cat", "getCategoryById", "id", "filteredTransactions", "filtered", "filter", "t", "confidence", "sort", "a", "b", "_getTransactionCatego", "_getTransactionCatego2", "aValue", "bValue", "Date", "postDateTime", "getTime", "Math", "abs", "debitAmount", "creditAmount", "description", "toLowerCase", "handleManualCategorization", "categorizeTransaction", "updatedCategorizations", "prev", "newSet", "delete", "handleMLCategorization", "transactionIds", "transactionsToProcess", "includes", "length", "mlTransactions", "map", "date", "balance", "reference", "results", "categorizeTransactionsBatch", "processed", "result", "formatCurrency", "amount", "Intl", "NumberFormat", "style", "currency", "minimumFractionDigits", "maximumFractionDigits", "format", "formatConfidence", "undefined", "round", "getConfidenceColor", "stats", "categorized", "uncategorized", "mlCategorized", "c", "manualCategorized", "averageMLConfidence", "reduce", "sum", "_", "arr", "categorizationRate", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "toFixed", "onClick", "disabled", "type", "onChange", "e", "target", "checked", "transaction", "categorization", "category", "has", "add", "toLocaleDateString", "title", "backgroundColor", "color", "name", "toUpperCase", "value", "size", "Array", "from", "_c", "$RefreshReg$"], "sources": ["C:/tmsft/src/components/TransactionCategorization.tsx"], "sourcesContent": ["import React, { useState, useEffect, useCallback, useMemo } from 'react';\r\nimport { Transaction, TransactionCategory, TransactionCategorization as TransactionCategorizationData } from '../types';\r\nimport { transactionStorageService, type StoredTransaction } from '../services/transactionStorageService';\r\nimport { categorizationService } from '../services/categorizationService';\r\nimport { mlCategorizationService } from '../services/mlCategorizationService';\r\nimport './TransactionCategorization.css';\r\n\r\ninterface TransactionCategorizationProps {\r\n  refreshTrigger?: number;\r\n}\r\n\r\ntype ViewMode = 'uncategorized' | 'all' | 'ml-pending' | 'low-confidence';\r\ntype SortField = 'date' | 'amount' | 'confidence' | 'description';\r\ntype SortDirection = 'asc' | 'desc';\r\n\r\ninterface CategoryFilter {\r\n  categoryId: string;\r\n  method: 'all' | 'manual' | 'ml' | 'rule';\r\n  confidenceMin: number;\r\n  confidenceMax: number;\r\n}\r\n\r\nexport const TransactionCategorization: React.FC<TransactionCategorizationProps> = ({ refreshTrigger }) => {\r\n  // State management\r\n  const [transactions, setTransactions] = useState<StoredTransaction[]>([]);\r\n  const [categories, setCategories] = useState<TransactionCategory[]>([]);\r\n  const [categorizations, setCategorizations] = useState<TransactionCategorizationData[]>([]);\r\n  const [loading, setLoading] = useState(true);\r\n  const [processingML, setProcessingML] = useState(false);\r\n  const [mlProgress, setMLProgress] = useState({ current: 0, total: 0 });\r\n  const [selectedTransactions, setSelectedTransactions] = useState<Set<string>>(new Set());\r\n  \r\n  // View and filter state\r\n  const [viewMode, setViewMode] = useState<ViewMode>('uncategorized');\r\n  const [sortField, setSortField] = useState<SortField>('date');\r\n  const [sortDirection, setSortDirection] = useState<SortDirection>('desc');\r\n  const [categoryFilter] = useState<CategoryFilter>({\r\n    categoryId: '',\r\n    method: 'all',\r\n    confidenceMin: 0,\r\n    confidenceMax: 1\r\n  });\r\n\r\n  // ML Configuration state\r\n  const [mlConfig] = useState({\r\n    confidenceThreshold: 0.7,\r\n    batchSize: 10,\r\n    autoApplyHighConfidence: true\r\n  });\r\n\r\n  // Modal state\r\n  const [showMLConfigModal, setShowMLConfigModal] = useState(false);\r\n  const [showCategoryModal, setShowCategoryModal] = useState(false);\r\n\r\n  // Load data\r\n  const loadData = useCallback(async () => {\r\n    try {\r\n      setLoading(true);\r\n      \r\n      // Load all data\r\n      const [allTransactions, allCategories, allCategorizations] = await Promise.all([\r\n        Promise.resolve(transactionStorageService.getAllTransactions()),\r\n        Promise.resolve(categorizationService.getAllCategories()),\r\n        Promise.resolve(categorizationService.getAllCategorizations())\r\n      ]);\r\n\r\n      setTransactions(allTransactions);\r\n      setCategories(allCategories);\r\n      setCategorizations(allCategorizations);\r\n    } catch (error) {\r\n      console.error('Failed to load categorization data:', error);\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  }, []);\r\n\r\n  useEffect(() => {\r\n    loadData();\r\n  }, [loadData, refreshTrigger]);\r\n\r\n  // Get categorization data for a transaction\r\n  const getTransactionCategorization = useCallback((transactionId: string): TransactionCategorizationData | undefined => {\r\n    return categorizations.find(cat => cat.transactionId === transactionId);\r\n  }, [categorizations]);\r\n\r\n  // Get category by ID\r\n  const getCategoryById = useCallback((categoryId: string): TransactionCategory | undefined => {\r\n    return categories.find(cat => cat.id === categoryId);\r\n  }, [categories]);\r\n\r\n  // Filter and sort transactions based on current settings\r\n  const filteredTransactions = useMemo(() => {\r\n    let filtered = [...transactions];\r\n\r\n    // Apply view mode filter\r\n    switch (viewMode) {\r\n      case 'uncategorized':\r\n        filtered = filtered.filter(t => !getTransactionCategorization(t.id));\r\n        break;\r\n      case 'ml-pending':\r\n        filtered = filtered.filter(t => {\r\n          const cat = getTransactionCategorization(t.id);\r\n          return cat && cat.method === 'ml' && (cat.confidence || 0) < mlConfig.confidenceThreshold;\r\n        });\r\n        break;\r\n      case 'low-confidence':\r\n        filtered = filtered.filter(t => {\r\n          const cat = getTransactionCategorization(t.id);\r\n          return cat && cat.method === 'ml' && (cat.confidence || 0) < 0.8;\r\n        });\r\n        break;\r\n      case 'all':\r\n      default:\r\n        // No additional filtering\r\n        break;\r\n    }\r\n\r\n    // Apply category filter\r\n    if (categoryFilter.categoryId) {\r\n      filtered = filtered.filter(t => {\r\n        const cat = getTransactionCategorization(t.id);\r\n        return cat && cat.categoryId === categoryFilter.categoryId;\r\n      });\r\n    }\r\n\r\n    // Apply method filter\r\n    if (categoryFilter.method !== 'all') {\r\n      filtered = filtered.filter(t => {\r\n        const cat = getTransactionCategorization(t.id);\r\n        return cat && cat.method === categoryFilter.method;\r\n      });\r\n    }\r\n\r\n    // Apply confidence filter\r\n    filtered = filtered.filter(t => {\r\n      const cat = getTransactionCategorization(t.id);\r\n      if (!cat || cat.method !== 'ml') return true; // Include non-ML transactions\r\n      const confidence = cat.confidence || 0;\r\n      return confidence >= categoryFilter.confidenceMin && confidence <= categoryFilter.confidenceMax;\r\n    });\r\n\r\n    // Sort transactions\r\n    filtered.sort((a, b) => {\r\n      let aValue: string | number;\r\n      let bValue: string | number;\r\n\r\n      switch (sortField) {\r\n        case 'date':\r\n          aValue = new Date(a.postDateTime).getTime();\r\n          bValue = new Date(b.postDateTime).getTime();\r\n          break;\r\n        case 'amount':\r\n          aValue = Math.abs(a.debitAmount || a.creditAmount || 0);\r\n          bValue = Math.abs(b.debitAmount || b.creditAmount || 0);\r\n          break;\r\n        case 'confidence':\r\n          aValue = getTransactionCategorization(a.id)?.confidence || 0;\r\n          bValue = getTransactionCategorization(b.id)?.confidence || 0;\r\n          break;\r\n        case 'description':\r\n        default:\r\n          aValue = a.description.toLowerCase();\r\n          bValue = b.description.toLowerCase();\r\n          break;\r\n      }\r\n\r\n      if (aValue < bValue) return sortDirection === 'asc' ? -1 : 1;\r\n      if (aValue > bValue) return sortDirection === 'asc' ? 1 : -1;\r\n      return 0;\r\n    });\r\n\r\n    return filtered;\r\n  }, [transactions, categorizations, viewMode, categoryFilter, sortField, sortDirection, mlConfig.confidenceThreshold, getTransactionCategorization]);\r\n\r\n  // Handle manual categorization\r\n  const handleManualCategorization = useCallback(async (transactionId: string, categoryId: string) => {\r\n    try {\r\n      categorizationService.categorizeTransaction(transactionId, categoryId, 'manual');\r\n      \r\n      // Refresh categorizations\r\n      const updatedCategorizations = categorizationService.getAllCategorizations();\r\n      setCategorizations(updatedCategorizations);\r\n      \r\n      // Clear selection\r\n      setSelectedTransactions(prev => {\r\n        const newSet = new Set(prev);\r\n        newSet.delete(transactionId);\r\n        return newSet;\r\n      });\r\n    } catch (error) {\r\n      console.error('Failed to categorize transaction:', error);\r\n    }\r\n  }, []);\r\n\r\n  // Handle batch ML categorization\r\n  const handleMLCategorization = useCallback(async (transactionIds?: string[]) => {\r\n    try {\r\n      setProcessingML(true);\r\n      \r\n      const transactionsToProcess = transactionIds \r\n        ? transactions.filter(t => transactionIds.includes(t.id))\r\n        : filteredTransactions.filter(t => !getTransactionCategorization(t.id));\r\n\r\n      setMLProgress({ current: 0, total: transactionsToProcess.length });\r\n\r\n      // Convert StoredTransaction to Transaction format for ML service\r\n      const mlTransactions: Transaction[] = transactionsToProcess.map(t => ({\r\n        id: t.id,\r\n        date: t.postDateTime,\r\n        description: t.description,\r\n        debitAmount: t.debitAmount || 0,\r\n        creditAmount: t.creditAmount || 0,\r\n        balance: t.balance,\r\n        reference: t.reference\r\n      }));\r\n\r\n      const results = await mlCategorizationService.categorizeTransactionsBatch(mlTransactions);\r\n      \r\n      // Update progress and categorizations\r\n      let processed = 0;\r\n      for (const result of results) {\r\n        processed++;\r\n        setMLProgress({ current: processed, total: transactionsToProcess.length });\r\n        \r\n        if (result.result && result.result.confidence >= mlConfig.confidenceThreshold && mlConfig.autoApplyHighConfidence) {\r\n          // High confidence results are automatically applied\r\n          continue; // Already applied by ML service\r\n        }\r\n      }\r\n\r\n      // Refresh categorizations\r\n      const updatedCategorizations = categorizationService.getAllCategorizations();\r\n      setCategorizations(updatedCategorizations);\r\n      \r\n    } catch (error) {\r\n      console.error('ML categorization failed:', error);\r\n    } finally {\r\n      setProcessingML(false);\r\n      setMLProgress({ current: 0, total: 0 });\r\n    }\r\n  }, [transactions, filteredTransactions, mlConfig, getTransactionCategorization]);\r\n\r\n  // Format currency\r\n  const formatCurrency = useCallback((amount: number): string => {\r\n    return new Intl.NumberFormat('en-US', {\r\n      style: 'currency',\r\n      currency: 'USD',\r\n      minimumFractionDigits: 2,\r\n      maximumFractionDigits: 2\r\n    }).format(amount);\r\n  }, []);\r\n\r\n  // Format confidence as percentage\r\n  const formatConfidence = useCallback((confidence?: number): string => {\r\n    if (confidence === undefined) return 'N/A';\r\n    return `${Math.round(confidence * 100)}%`;\r\n  }, []);\r\n\r\n  // Get confidence color\r\n  const getConfidenceColor = useCallback((confidence?: number): string => {\r\n    if (confidence === undefined) return '#6B7280';\r\n    if (confidence >= 0.9) return '#10B981';\r\n    if (confidence >= 0.7) return '#F59E0B';\r\n    return '#EF4444';\r\n  }, []);\r\n\r\n  // Statistics\r\n  const stats = useMemo(() => {\r\n    const total = transactions.length;\r\n    const categorized = categorizations.length;\r\n    const uncategorized = total - categorized;\r\n    const mlCategorized = categorizations.filter(c => c.method === 'ml').length;\r\n    const manualCategorized = categorizations.filter(c => c.method === 'manual').length;\r\n    const averageMLConfidence = categorizations\r\n      .filter(c => c.method === 'ml' && c.confidence !== undefined)\r\n      .reduce((sum, c, _, arr) => sum + (c.confidence || 0) / arr.length, 0);\r\n\r\n    return {\r\n      total,\r\n      categorized,\r\n      uncategorized,\r\n      mlCategorized,\r\n      manualCategorized,\r\n      averageMLConfidence,\r\n      categorizationRate: total > 0 ? (categorized / total) * 100 : 0\r\n    };\r\n  }, [transactions, categorizations]);\r\n\r\n  if (loading) {\r\n    return (\r\n      <div className=\"categorization-loading\">\r\n        <div className=\"loading-spinner\"></div>\r\n        <p>Loading transaction categorization data...</p>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className=\"transaction-categorization\">\r\n      <div className=\"categorization-header\">\r\n        <div className=\"categorization-title\">\r\n          <h2>Transaction Categorization</h2>\r\n          <p>Categorize transactions manually or use AI-powered categorization with Qwen 3</p>\r\n        </div>\r\n        \r\n        <div className=\"categorization-stats\">\r\n          <div className=\"stat-card\">\r\n            <div className=\"stat-value\">{stats.total}</div>\r\n            <div className=\"stat-label\">Total Transactions</div>\r\n          </div>\r\n          <div className=\"stat-card\">\r\n            <div className=\"stat-value\">{stats.categorized}</div>\r\n            <div className=\"stat-label\">Categorized</div>\r\n          </div>\r\n          <div className=\"stat-card\">\r\n            <div className=\"stat-value\">{stats.uncategorized}</div>\r\n            <div className=\"stat-label\">Uncategorized</div>\r\n          </div>\r\n          <div className=\"stat-card\">\r\n            <div className=\"stat-value\">{stats.categorizationRate.toFixed(1)}%</div>\r\n            <div className=\"stat-label\">Completion Rate</div>\r\n          </div>\r\n          <div className=\"stat-card\">\r\n            <div className=\"stat-value\">{formatConfidence(stats.averageMLConfidence)}</div>\r\n            <div className=\"stat-label\">Avg ML Confidence</div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <div className=\"categorization-controls\">\r\n        <div className=\"view-modes\">\r\n          <button \r\n            className={`view-mode-btn ${viewMode === 'uncategorized' ? 'active' : ''}`}\r\n            onClick={() => setViewMode('uncategorized')}\r\n          >\r\n            Uncategorized ({stats.uncategorized})\r\n          </button>\r\n          <button \r\n            className={`view-mode-btn ${viewMode === 'all' ? 'active' : ''}`}\r\n            onClick={() => setViewMode('all')}\r\n          >\r\n            All Transactions\r\n          </button>\r\n          <button \r\n            className={`view-mode-btn ${viewMode === 'ml-pending' ? 'active' : ''}`}\r\n            onClick={() => setViewMode('ml-pending')}\r\n          >\r\n            ML Pending Review\r\n          </button>\r\n          <button \r\n            className={`view-mode-btn ${viewMode === 'low-confidence' ? 'active' : ''}`}\r\n            onClick={() => setViewMode('low-confidence')}\r\n          >\r\n            Low Confidence\r\n          </button>\r\n        </div>\r\n\r\n        <div className=\"action-buttons\">\r\n          <button \r\n            className=\"btn btn-primary\"\r\n            onClick={() => handleMLCategorization()}\r\n            disabled={processingML}\r\n          >\r\n            {processingML ? `Processing ${mlProgress.current}/${mlProgress.total}...` : 'Run ML Categorization'}\r\n          </button>\r\n          <button \r\n            className=\"btn btn-secondary\"\r\n            onClick={() => setShowMLConfigModal(true)}\r\n          >\r\n            ML Settings\r\n          </button>\r\n          <button \r\n            className=\"btn btn-secondary\"\r\n            onClick={() => setShowCategoryModal(true)}\r\n          >\r\n            Manage Categories\r\n          </button>\r\n        </div>\r\n      </div>\r\n\r\n      <div className=\"transactions-table-container\">\r\n        <table className=\"transactions-table\">\r\n          <thead>\r\n            <tr>\r\n              <th>\r\n                <input \r\n                  type=\"checkbox\" \r\n                  onChange={(e) => {\r\n                    if (e.target.checked) {\r\n                      setSelectedTransactions(new Set(filteredTransactions.map(t => t.id)));\r\n                    } else {\r\n                      setSelectedTransactions(new Set());\r\n                    }\r\n                  }}\r\n                />\r\n              </th>\r\n              <th \r\n                className={`sortable ${sortField === 'date' ? sortDirection : ''}`}\r\n                onClick={() => {\r\n                  if (sortField === 'date') {\r\n                    setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');\r\n                  } else {\r\n                    setSortField('date');\r\n                    setSortDirection('desc');\r\n                  }\r\n                }}\r\n              >\r\n                Date\r\n              </th>\r\n              <th \r\n                className={`sortable ${sortField === 'description' ? sortDirection : ''}`}\r\n                onClick={() => {\r\n                  if (sortField === 'description') {\r\n                    setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');\r\n                  } else {\r\n                    setSortField('description');\r\n                    setSortDirection('asc');\r\n                  }\r\n                }}\r\n              >\r\n                Description\r\n              </th>\r\n              <th \r\n                className={`sortable ${sortField === 'amount' ? sortDirection : ''}`}\r\n                onClick={() => {\r\n                  if (sortField === 'amount') {\r\n                    setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');\r\n                  } else {\r\n                    setSortField('amount');\r\n                    setSortDirection('desc');\r\n                  }\r\n                }}\r\n              >\r\n                Amount\r\n              </th>\r\n              <th>Category</th>\r\n              <th \r\n                className={`sortable ${sortField === 'confidence' ? sortDirection : ''}`}\r\n                onClick={() => {\r\n                  if (sortField === 'confidence') {\r\n                    setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');\r\n                  } else {\r\n                    setSortField('confidence');\r\n                    setSortDirection('desc');\r\n                  }\r\n                }}\r\n              >\r\n                Confidence\r\n              </th>\r\n              <th>Actions</th>\r\n            </tr>\r\n          </thead>\r\n          <tbody>\r\n            {filteredTransactions.map(transaction => {\r\n              const categorization = getTransactionCategorization(transaction.id);\r\n              const category = categorization ? getCategoryById(categorization.categoryId) : undefined;\r\n              const amount = transaction.debitAmount ? -transaction.debitAmount : (transaction.creditAmount || 0);\r\n              \r\n              return (\r\n                <tr key={transaction.id} className={selectedTransactions.has(transaction.id) ? 'selected' : ''}>\r\n                  <td>\r\n                    <input \r\n                      type=\"checkbox\" \r\n                      checked={selectedTransactions.has(transaction.id)}\r\n                      onChange={(e) => {\r\n                        const newSet = new Set(selectedTransactions);\r\n                        if (e.target.checked) {\r\n                          newSet.add(transaction.id);\r\n                        } else {\r\n                          newSet.delete(transaction.id);\r\n                        }\r\n                        setSelectedTransactions(newSet);\r\n                      }}\r\n                    />\r\n                  </td>\r\n                  <td>{new Date(transaction.postDateTime).toLocaleDateString()}</td>\r\n                  <td className=\"description-cell\" title={transaction.description}>\r\n                    {transaction.description}\r\n                  </td>\r\n                  <td className={`amount-cell ${amount < 0 ? 'debit' : 'credit'}`}>\r\n                    {formatCurrency(Math.abs(amount))}\r\n                  </td>\r\n                  <td>\r\n                    {category ? (\r\n                      <div className=\"category-tag\" style={{ backgroundColor: category.color }}>\r\n                        {category.name}\r\n                        {categorization?.method && (\r\n                          <span className=\"category-method\">{categorization.method.toUpperCase()}</span>\r\n                        )}\r\n                      </div>\r\n                    ) : (\r\n                      <span className=\"uncategorized\">Uncategorized</span>\r\n                    )}\r\n                  </td>\r\n                  <td>\r\n                    {categorization?.confidence !== undefined ? (\r\n                      <span \r\n                        className=\"confidence-score\"\r\n                        style={{ color: getConfidenceColor(categorization.confidence) }}\r\n                      >\r\n                        {formatConfidence(categorization.confidence)}\r\n                      </span>\r\n                    ) : (\r\n                      <span className=\"no-confidence\">-</span>\r\n                    )}\r\n                  </td>\r\n                  <td>\r\n                    <div className=\"action-buttons\">\r\n                      <select \r\n                        value={categorization?.categoryId || ''}\r\n                        onChange={(e) => {\r\n                          if (e.target.value) {\r\n                            handleManualCategorization(transaction.id, e.target.value);\r\n                          }\r\n                        }}\r\n                        className=\"category-select\"\r\n                      >\r\n                        <option value=\"\">Select Category...</option>\r\n                        {categories.map(cat => (\r\n                          <option key={cat.id} value={cat.id}>{cat.name}</option>\r\n                        ))}\r\n                      </select>\r\n                    </div>\r\n                  </td>\r\n                </tr>\r\n              );\r\n            })}\r\n          </tbody>\r\n        </table>\r\n      </div>\r\n\r\n      {selectedTransactions.size > 0 && (\r\n        <div className=\"batch-actions\">\r\n          <div className=\"batch-info\">\r\n            {selectedTransactions.size} transaction(s) selected\r\n          </div>\r\n          <button \r\n            className=\"btn btn-primary\"\r\n            onClick={() => handleMLCategorization(Array.from(selectedTransactions))}\r\n            disabled={processingML}\r\n          >\r\n            Categorize Selected with ML\r\n          </button>\r\n          <button \r\n            className=\"btn btn-secondary\"\r\n            onClick={() => setSelectedTransactions(new Set())}\r\n          >\r\n            Clear Selection\r\n          </button>\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n}; "], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,EAAEC,OAAO,QAAQ,OAAO;AAExE,SAASC,yBAAyB,QAAgC,uCAAuC;AACzG,SAASC,qBAAqB,QAAQ,mCAAmC;AACzE,SAASC,uBAAuB,QAAQ,qCAAqC;AAC7E,OAAO,iCAAiC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAiBzC,OAAO,MAAMC,yBAAmE,GAAGA,CAAC;EAAEC;AAAe,CAAC,KAAK;EAAAC,EAAA;EACzG;EACA,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGb,QAAQ,CAAsB,EAAE,CAAC;EACzE,MAAM,CAACc,UAAU,EAAEC,aAAa,CAAC,GAAGf,QAAQ,CAAwB,EAAE,CAAC;EACvE,MAAM,CAACgB,eAAe,EAAEC,kBAAkB,CAAC,GAAGjB,QAAQ,CAAkC,EAAE,CAAC;EAC3F,MAAM,CAACkB,OAAO,EAAEC,UAAU,CAAC,GAAGnB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACoB,YAAY,EAAEC,eAAe,CAAC,GAAGrB,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACsB,UAAU,EAAEC,aAAa,CAAC,GAAGvB,QAAQ,CAAC;IAAEwB,OAAO,EAAE,CAAC;IAAEC,KAAK,EAAE;EAAE,CAAC,CAAC;EACtE,MAAM,CAACC,oBAAoB,EAAEC,uBAAuB,CAAC,GAAG3B,QAAQ,CAAc,IAAI4B,GAAG,CAAC,CAAC,CAAC;;EAExF;EACA,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAG9B,QAAQ,CAAW,eAAe,CAAC;EACnE,MAAM,CAAC+B,SAAS,EAAEC,YAAY,CAAC,GAAGhC,QAAQ,CAAY,MAAM,CAAC;EAC7D,MAAM,CAACiC,aAAa,EAAEC,gBAAgB,CAAC,GAAGlC,QAAQ,CAAgB,MAAM,CAAC;EACzE,MAAM,CAACmC,cAAc,CAAC,GAAGnC,QAAQ,CAAiB;IAChDoC,UAAU,EAAE,EAAE;IACdC,MAAM,EAAE,KAAK;IACbC,aAAa,EAAE,CAAC;IAChBC,aAAa,EAAE;EACjB,CAAC,CAAC;;EAEF;EACA,MAAM,CAACC,QAAQ,CAAC,GAAGxC,QAAQ,CAAC;IAC1ByC,mBAAmB,EAAE,GAAG;IACxBC,SAAS,EAAE,EAAE;IACbC,uBAAuB,EAAE;EAC3B,CAAC,CAAC;;EAEF;EACA,MAAM,CAACC,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG7C,QAAQ,CAAC,KAAK,CAAC;EACjE,MAAM,CAAC8C,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG/C,QAAQ,CAAC,KAAK,CAAC;;EAEjE;EACA,MAAMgD,QAAQ,GAAG9C,WAAW,CAAC,YAAY;IACvC,IAAI;MACFiB,UAAU,CAAC,IAAI,CAAC;;MAEhB;MACA,MAAM,CAAC8B,eAAe,EAAEC,aAAa,EAAEC,kBAAkB,CAAC,GAAG,MAAMC,OAAO,CAACC,GAAG,CAAC,CAC7ED,OAAO,CAACE,OAAO,CAAClD,yBAAyB,CAACmD,kBAAkB,CAAC,CAAC,CAAC,EAC/DH,OAAO,CAACE,OAAO,CAACjD,qBAAqB,CAACmD,gBAAgB,CAAC,CAAC,CAAC,EACzDJ,OAAO,CAACE,OAAO,CAACjD,qBAAqB,CAACoD,qBAAqB,CAAC,CAAC,CAAC,CAC/D,CAAC;MAEF5C,eAAe,CAACoC,eAAe,CAAC;MAChClC,aAAa,CAACmC,aAAa,CAAC;MAC5BjC,kBAAkB,CAACkC,kBAAkB,CAAC;IACxC,CAAC,CAAC,OAAOO,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,qCAAqC,EAAEA,KAAK,CAAC;IAC7D,CAAC,SAAS;MACRvC,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC,EAAE,EAAE,CAAC;EAENlB,SAAS,CAAC,MAAM;IACd+C,QAAQ,CAAC,CAAC;EACZ,CAAC,EAAE,CAACA,QAAQ,EAAEtC,cAAc,CAAC,CAAC;;EAE9B;EACA,MAAMkD,4BAA4B,GAAG1D,WAAW,CAAE2D,aAAqB,IAAgD;IACrH,OAAO7C,eAAe,CAAC8C,IAAI,CAACC,GAAG,IAAIA,GAAG,CAACF,aAAa,KAAKA,aAAa,CAAC;EACzE,CAAC,EAAE,CAAC7C,eAAe,CAAC,CAAC;;EAErB;EACA,MAAMgD,eAAe,GAAG9D,WAAW,CAAEkC,UAAkB,IAAsC;IAC3F,OAAOtB,UAAU,CAACgD,IAAI,CAACC,GAAG,IAAIA,GAAG,CAACE,EAAE,KAAK7B,UAAU,CAAC;EACtD,CAAC,EAAE,CAACtB,UAAU,CAAC,CAAC;;EAEhB;EACA,MAAMoD,oBAAoB,GAAG/D,OAAO,CAAC,MAAM;IACzC,IAAIgE,QAAQ,GAAG,CAAC,GAAGvD,YAAY,CAAC;;IAEhC;IACA,QAAQiB,QAAQ;MACd,KAAK,eAAe;QAClBsC,QAAQ,GAAGA,QAAQ,CAACC,MAAM,CAACC,CAAC,IAAI,CAACT,4BAA4B,CAACS,CAAC,CAACJ,EAAE,CAAC,CAAC;QACpE;MACF,KAAK,YAAY;QACfE,QAAQ,GAAGA,QAAQ,CAACC,MAAM,CAACC,CAAC,IAAI;UAC9B,MAAMN,GAAG,GAAGH,4BAA4B,CAACS,CAAC,CAACJ,EAAE,CAAC;UAC9C,OAAOF,GAAG,IAAIA,GAAG,CAAC1B,MAAM,KAAK,IAAI,IAAI,CAAC0B,GAAG,CAACO,UAAU,IAAI,CAAC,IAAI9B,QAAQ,CAACC,mBAAmB;QAC3F,CAAC,CAAC;QACF;MACF,KAAK,gBAAgB;QACnB0B,QAAQ,GAAGA,QAAQ,CAACC,MAAM,CAACC,CAAC,IAAI;UAC9B,MAAMN,GAAG,GAAGH,4BAA4B,CAACS,CAAC,CAACJ,EAAE,CAAC;UAC9C,OAAOF,GAAG,IAAIA,GAAG,CAAC1B,MAAM,KAAK,IAAI,IAAI,CAAC0B,GAAG,CAACO,UAAU,IAAI,CAAC,IAAI,GAAG;QAClE,CAAC,CAAC;QACF;MACF,KAAK,KAAK;MACV;QACE;QACA;IACJ;;IAEA;IACA,IAAInC,cAAc,CAACC,UAAU,EAAE;MAC7B+B,QAAQ,GAAGA,QAAQ,CAACC,MAAM,CAACC,CAAC,IAAI;QAC9B,MAAMN,GAAG,GAAGH,4BAA4B,CAACS,CAAC,CAACJ,EAAE,CAAC;QAC9C,OAAOF,GAAG,IAAIA,GAAG,CAAC3B,UAAU,KAAKD,cAAc,CAACC,UAAU;MAC5D,CAAC,CAAC;IACJ;;IAEA;IACA,IAAID,cAAc,CAACE,MAAM,KAAK,KAAK,EAAE;MACnC8B,QAAQ,GAAGA,QAAQ,CAACC,MAAM,CAACC,CAAC,IAAI;QAC9B,MAAMN,GAAG,GAAGH,4BAA4B,CAACS,CAAC,CAACJ,EAAE,CAAC;QAC9C,OAAOF,GAAG,IAAIA,GAAG,CAAC1B,MAAM,KAAKF,cAAc,CAACE,MAAM;MACpD,CAAC,CAAC;IACJ;;IAEA;IACA8B,QAAQ,GAAGA,QAAQ,CAACC,MAAM,CAACC,CAAC,IAAI;MAC9B,MAAMN,GAAG,GAAGH,4BAA4B,CAACS,CAAC,CAACJ,EAAE,CAAC;MAC9C,IAAI,CAACF,GAAG,IAAIA,GAAG,CAAC1B,MAAM,KAAK,IAAI,EAAE,OAAO,IAAI,CAAC,CAAC;MAC9C,MAAMiC,UAAU,GAAGP,GAAG,CAACO,UAAU,IAAI,CAAC;MACtC,OAAOA,UAAU,IAAInC,cAAc,CAACG,aAAa,IAAIgC,UAAU,IAAInC,cAAc,CAACI,aAAa;IACjG,CAAC,CAAC;;IAEF;IACA4B,QAAQ,CAACI,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;MAAA,IAAAC,qBAAA,EAAAC,sBAAA;MACtB,IAAIC,MAAuB;MAC3B,IAAIC,MAAuB;MAE3B,QAAQ9C,SAAS;QACf,KAAK,MAAM;UACT6C,MAAM,GAAG,IAAIE,IAAI,CAACN,CAAC,CAACO,YAAY,CAAC,CAACC,OAAO,CAAC,CAAC;UAC3CH,MAAM,GAAG,IAAIC,IAAI,CAACL,CAAC,CAACM,YAAY,CAAC,CAACC,OAAO,CAAC,CAAC;UAC3C;QACF,KAAK,QAAQ;UACXJ,MAAM,GAAGK,IAAI,CAACC,GAAG,CAACV,CAAC,CAACW,WAAW,IAAIX,CAAC,CAACY,YAAY,IAAI,CAAC,CAAC;UACvDP,MAAM,GAAGI,IAAI,CAACC,GAAG,CAACT,CAAC,CAACU,WAAW,IAAIV,CAAC,CAACW,YAAY,IAAI,CAAC,CAAC;UACvD;QACF,KAAK,YAAY;UACfR,MAAM,GAAG,EAAAF,qBAAA,GAAAd,4BAA4B,CAACY,CAAC,CAACP,EAAE,CAAC,cAAAS,qBAAA,uBAAlCA,qBAAA,CAAoCJ,UAAU,KAAI,CAAC;UAC5DO,MAAM,GAAG,EAAAF,sBAAA,GAAAf,4BAA4B,CAACa,CAAC,CAACR,EAAE,CAAC,cAAAU,sBAAA,uBAAlCA,sBAAA,CAAoCL,UAAU,KAAI,CAAC;UAC5D;QACF,KAAK,aAAa;QAClB;UACEM,MAAM,GAAGJ,CAAC,CAACa,WAAW,CAACC,WAAW,CAAC,CAAC;UACpCT,MAAM,GAAGJ,CAAC,CAACY,WAAW,CAACC,WAAW,CAAC,CAAC;UACpC;MACJ;MAEA,IAAIV,MAAM,GAAGC,MAAM,EAAE,OAAO5C,aAAa,KAAK,KAAK,GAAG,CAAC,CAAC,GAAG,CAAC;MAC5D,IAAI2C,MAAM,GAAGC,MAAM,EAAE,OAAO5C,aAAa,KAAK,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC;MAC5D,OAAO,CAAC;IACV,CAAC,CAAC;IAEF,OAAOkC,QAAQ;EACjB,CAAC,EAAE,CAACvD,YAAY,EAAEI,eAAe,EAAEa,QAAQ,EAAEM,cAAc,EAAEJ,SAAS,EAAEE,aAAa,EAAEO,QAAQ,CAACC,mBAAmB,EAAEmB,4BAA4B,CAAC,CAAC;;EAEnJ;EACA,MAAM2B,0BAA0B,GAAGrF,WAAW,CAAC,OAAO2D,aAAqB,EAAEzB,UAAkB,KAAK;IAClG,IAAI;MACF/B,qBAAqB,CAACmF,qBAAqB,CAAC3B,aAAa,EAAEzB,UAAU,EAAE,QAAQ,CAAC;;MAEhF;MACA,MAAMqD,sBAAsB,GAAGpF,qBAAqB,CAACoD,qBAAqB,CAAC,CAAC;MAC5ExC,kBAAkB,CAACwE,sBAAsB,CAAC;;MAE1C;MACA9D,uBAAuB,CAAC+D,IAAI,IAAI;QAC9B,MAAMC,MAAM,GAAG,IAAI/D,GAAG,CAAC8D,IAAI,CAAC;QAC5BC,MAAM,CAACC,MAAM,CAAC/B,aAAa,CAAC;QAC5B,OAAO8B,MAAM;MACf,CAAC,CAAC;IACJ,CAAC,CAAC,OAAOjC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;IAC3D;EACF,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMmC,sBAAsB,GAAG3F,WAAW,CAAC,MAAO4F,cAAyB,IAAK;IAC9E,IAAI;MACFzE,eAAe,CAAC,IAAI,CAAC;MAErB,MAAM0E,qBAAqB,GAAGD,cAAc,GACxClF,YAAY,CAACwD,MAAM,CAACC,CAAC,IAAIyB,cAAc,CAACE,QAAQ,CAAC3B,CAAC,CAACJ,EAAE,CAAC,CAAC,GACvDC,oBAAoB,CAACE,MAAM,CAACC,CAAC,IAAI,CAACT,4BAA4B,CAACS,CAAC,CAACJ,EAAE,CAAC,CAAC;MAEzE1C,aAAa,CAAC;QAAEC,OAAO,EAAE,CAAC;QAAEC,KAAK,EAAEsE,qBAAqB,CAACE;MAAO,CAAC,CAAC;;MAElE;MACA,MAAMC,cAA6B,GAAGH,qBAAqB,CAACI,GAAG,CAAC9B,CAAC,KAAK;QACpEJ,EAAE,EAAEI,CAAC,CAACJ,EAAE;QACRmC,IAAI,EAAE/B,CAAC,CAACU,YAAY;QACpBM,WAAW,EAAEhB,CAAC,CAACgB,WAAW;QAC1BF,WAAW,EAAEd,CAAC,CAACc,WAAW,IAAI,CAAC;QAC/BC,YAAY,EAAEf,CAAC,CAACe,YAAY,IAAI,CAAC;QACjCiB,OAAO,EAAEhC,CAAC,CAACgC,OAAO;QAClBC,SAAS,EAAEjC,CAAC,CAACiC;MACf,CAAC,CAAC,CAAC;MAEH,MAAMC,OAAO,GAAG,MAAMjG,uBAAuB,CAACkG,2BAA2B,CAACN,cAAc,CAAC;;MAEzF;MACA,IAAIO,SAAS,GAAG,CAAC;MACjB,KAAK,MAAMC,MAAM,IAAIH,OAAO,EAAE;QAC5BE,SAAS,EAAE;QACXlF,aAAa,CAAC;UAAEC,OAAO,EAAEiF,SAAS;UAAEhF,KAAK,EAAEsE,qBAAqB,CAACE;QAAO,CAAC,CAAC;QAE1E,IAAIS,MAAM,CAACA,MAAM,IAAIA,MAAM,CAACA,MAAM,CAACpC,UAAU,IAAI9B,QAAQ,CAACC,mBAAmB,IAAID,QAAQ,CAACG,uBAAuB,EAAE;UACjH;UACA,SAAS,CAAC;QACZ;MACF;;MAEA;MACA,MAAM8C,sBAAsB,GAAGpF,qBAAqB,CAACoD,qBAAqB,CAAC,CAAC;MAC5ExC,kBAAkB,CAACwE,sBAAsB,CAAC;IAE5C,CAAC,CAAC,OAAO/B,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;IACnD,CAAC,SAAS;MACRrC,eAAe,CAAC,KAAK,CAAC;MACtBE,aAAa,CAAC;QAAEC,OAAO,EAAE,CAAC;QAAEC,KAAK,EAAE;MAAE,CAAC,CAAC;IACzC;EACF,CAAC,EAAE,CAACb,YAAY,EAAEsD,oBAAoB,EAAE1B,QAAQ,EAAEoB,4BAA4B,CAAC,CAAC;;EAEhF;EACA,MAAM+C,cAAc,GAAGzG,WAAW,CAAE0G,MAAc,IAAa;IAC7D,OAAO,IAAIC,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;MACpCC,KAAK,EAAE,UAAU;MACjBC,QAAQ,EAAE,KAAK;MACfC,qBAAqB,EAAE,CAAC;MACxBC,qBAAqB,EAAE;IACzB,CAAC,CAAC,CAACC,MAAM,CAACP,MAAM,CAAC;EACnB,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMQ,gBAAgB,GAAGlH,WAAW,CAAEoE,UAAmB,IAAa;IACpE,IAAIA,UAAU,KAAK+C,SAAS,EAAE,OAAO,KAAK;IAC1C,OAAO,GAAGpC,IAAI,CAACqC,KAAK,CAAChD,UAAU,GAAG,GAAG,CAAC,GAAG;EAC3C,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMiD,kBAAkB,GAAGrH,WAAW,CAAEoE,UAAmB,IAAa;IACtE,IAAIA,UAAU,KAAK+C,SAAS,EAAE,OAAO,SAAS;IAC9C,IAAI/C,UAAU,IAAI,GAAG,EAAE,OAAO,SAAS;IACvC,IAAIA,UAAU,IAAI,GAAG,EAAE,OAAO,SAAS;IACvC,OAAO,SAAS;EAClB,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMkD,KAAK,GAAGrH,OAAO,CAAC,MAAM;IAC1B,MAAMsB,KAAK,GAAGb,YAAY,CAACqF,MAAM;IACjC,MAAMwB,WAAW,GAAGzG,eAAe,CAACiF,MAAM;IAC1C,MAAMyB,aAAa,GAAGjG,KAAK,GAAGgG,WAAW;IACzC,MAAME,aAAa,GAAG3G,eAAe,CAACoD,MAAM,CAACwD,CAAC,IAAIA,CAAC,CAACvF,MAAM,KAAK,IAAI,CAAC,CAAC4D,MAAM;IAC3E,MAAM4B,iBAAiB,GAAG7G,eAAe,CAACoD,MAAM,CAACwD,CAAC,IAAIA,CAAC,CAACvF,MAAM,KAAK,QAAQ,CAAC,CAAC4D,MAAM;IACnF,MAAM6B,mBAAmB,GAAG9G,eAAe,CACxCoD,MAAM,CAACwD,CAAC,IAAIA,CAAC,CAACvF,MAAM,KAAK,IAAI,IAAIuF,CAAC,CAACtD,UAAU,KAAK+C,SAAS,CAAC,CAC5DU,MAAM,CAAC,CAACC,GAAG,EAAEJ,CAAC,EAAEK,CAAC,EAAEC,GAAG,KAAKF,GAAG,GAAG,CAACJ,CAAC,CAACtD,UAAU,IAAI,CAAC,IAAI4D,GAAG,CAACjC,MAAM,EAAE,CAAC,CAAC;IAExE,OAAO;MACLxE,KAAK;MACLgG,WAAW;MACXC,aAAa;MACbC,aAAa;MACbE,iBAAiB;MACjBC,mBAAmB;MACnBK,kBAAkB,EAAE1G,KAAK,GAAG,CAAC,GAAIgG,WAAW,GAAGhG,KAAK,GAAI,GAAG,GAAG;IAChE,CAAC;EACH,CAAC,EAAE,CAACb,YAAY,EAAEI,eAAe,CAAC,CAAC;EAEnC,IAAIE,OAAO,EAAE;IACX,oBACEV,OAAA;MAAK4H,SAAS,EAAC,wBAAwB;MAAAC,QAAA,gBACrC7H,OAAA;QAAK4H,SAAS,EAAC;MAAiB;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACvCjI,OAAA;QAAA6H,QAAA,EAAG;MAA0C;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC9C,CAAC;EAEV;EAEA,oBACEjI,OAAA;IAAK4H,SAAS,EAAC,4BAA4B;IAAAC,QAAA,gBACzC7H,OAAA;MAAK4H,SAAS,EAAC,uBAAuB;MAAAC,QAAA,gBACpC7H,OAAA;QAAK4H,SAAS,EAAC,sBAAsB;QAAAC,QAAA,gBACnC7H,OAAA;UAAA6H,QAAA,EAAI;QAA0B;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACnCjI,OAAA;UAAA6H,QAAA,EAAG;QAA6E;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjF,CAAC,eAENjI,OAAA;QAAK4H,SAAS,EAAC,sBAAsB;QAAAC,QAAA,gBACnC7H,OAAA;UAAK4H,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxB7H,OAAA;YAAK4H,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAEb,KAAK,CAAC/F;UAAK;YAAA6G,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC/CjI,OAAA;YAAK4H,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAC;UAAkB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjD,CAAC,eACNjI,OAAA;UAAK4H,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxB7H,OAAA;YAAK4H,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAEb,KAAK,CAACC;UAAW;YAAAa,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACrDjI,OAAA;YAAK4H,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAC;UAAW;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1C,CAAC,eACNjI,OAAA;UAAK4H,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxB7H,OAAA;YAAK4H,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAEb,KAAK,CAACE;UAAa;YAAAY,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACvDjI,OAAA;YAAK4H,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAC;UAAa;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5C,CAAC,eACNjI,OAAA;UAAK4H,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxB7H,OAAA;YAAK4H,SAAS,EAAC,YAAY;YAAAC,QAAA,GAAEb,KAAK,CAACW,kBAAkB,CAACO,OAAO,CAAC,CAAC,CAAC,EAAC,GAAC;UAAA;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACxEjI,OAAA;YAAK4H,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAC;UAAe;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9C,CAAC,eACNjI,OAAA;UAAK4H,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxB7H,OAAA;YAAK4H,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAEjB,gBAAgB,CAACI,KAAK,CAACM,mBAAmB;UAAC;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC/EjI,OAAA;YAAK4H,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAC;UAAiB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENjI,OAAA;MAAK4H,SAAS,EAAC,yBAAyB;MAAAC,QAAA,gBACtC7H,OAAA;QAAK4H,SAAS,EAAC,YAAY;QAAAC,QAAA,gBACzB7H,OAAA;UACE4H,SAAS,EAAE,iBAAiBvG,QAAQ,KAAK,eAAe,GAAG,QAAQ,GAAG,EAAE,EAAG;UAC3E8G,OAAO,EAAEA,CAAA,KAAM7G,WAAW,CAAC,eAAe,CAAE;UAAAuG,QAAA,GAC7C,iBACgB,EAACb,KAAK,CAACE,aAAa,EAAC,GACtC;QAAA;UAAAY,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTjI,OAAA;UACE4H,SAAS,EAAE,iBAAiBvG,QAAQ,KAAK,KAAK,GAAG,QAAQ,GAAG,EAAE,EAAG;UACjE8G,OAAO,EAAEA,CAAA,KAAM7G,WAAW,CAAC,KAAK,CAAE;UAAAuG,QAAA,EACnC;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTjI,OAAA;UACE4H,SAAS,EAAE,iBAAiBvG,QAAQ,KAAK,YAAY,GAAG,QAAQ,GAAG,EAAE,EAAG;UACxE8G,OAAO,EAAEA,CAAA,KAAM7G,WAAW,CAAC,YAAY,CAAE;UAAAuG,QAAA,EAC1C;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTjI,OAAA;UACE4H,SAAS,EAAE,iBAAiBvG,QAAQ,KAAK,gBAAgB,GAAG,QAAQ,GAAG,EAAE,EAAG;UAC5E8G,OAAO,EAAEA,CAAA,KAAM7G,WAAW,CAAC,gBAAgB,CAAE;UAAAuG,QAAA,EAC9C;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAENjI,OAAA;QAAK4H,SAAS,EAAC,gBAAgB;QAAAC,QAAA,gBAC7B7H,OAAA;UACE4H,SAAS,EAAC,iBAAiB;UAC3BO,OAAO,EAAEA,CAAA,KAAM9C,sBAAsB,CAAC,CAAE;UACxC+C,QAAQ,EAAExH,YAAa;UAAAiH,QAAA,EAEtBjH,YAAY,GAAG,cAAcE,UAAU,CAACE,OAAO,IAAIF,UAAU,CAACG,KAAK,KAAK,GAAG;QAAuB;UAAA6G,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7F,CAAC,eACTjI,OAAA;UACE4H,SAAS,EAAC,mBAAmB;UAC7BO,OAAO,EAAEA,CAAA,KAAM9F,oBAAoB,CAAC,IAAI,CAAE;UAAAwF,QAAA,EAC3C;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTjI,OAAA;UACE4H,SAAS,EAAC,mBAAmB;UAC7BO,OAAO,EAAEA,CAAA,KAAM5F,oBAAoB,CAAC,IAAI,CAAE;UAAAsF,QAAA,EAC3C;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENjI,OAAA;MAAK4H,SAAS,EAAC,8BAA8B;MAAAC,QAAA,eAC3C7H,OAAA;QAAO4H,SAAS,EAAC,oBAAoB;QAAAC,QAAA,gBACnC7H,OAAA;UAAA6H,QAAA,eACE7H,OAAA;YAAA6H,QAAA,gBACE7H,OAAA;cAAA6H,QAAA,eACE7H,OAAA;gBACEqI,IAAI,EAAC,UAAU;gBACfC,QAAQ,EAAGC,CAAC,IAAK;kBACf,IAAIA,CAAC,CAACC,MAAM,CAACC,OAAO,EAAE;oBACpBtH,uBAAuB,CAAC,IAAIC,GAAG,CAACsC,oBAAoB,CAACiC,GAAG,CAAC9B,CAAC,IAAIA,CAAC,CAACJ,EAAE,CAAC,CAAC,CAAC;kBACvE,CAAC,MAAM;oBACLtC,uBAAuB,CAAC,IAAIC,GAAG,CAAC,CAAC,CAAC;kBACpC;gBACF;cAAE;gBAAA0G,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC,eACLjI,OAAA;cACE4H,SAAS,EAAE,YAAYrG,SAAS,KAAK,MAAM,GAAGE,aAAa,GAAG,EAAE,EAAG;cACnE0G,OAAO,EAAEA,CAAA,KAAM;gBACb,IAAI5G,SAAS,KAAK,MAAM,EAAE;kBACxBG,gBAAgB,CAACD,aAAa,KAAK,KAAK,GAAG,MAAM,GAAG,KAAK,CAAC;gBAC5D,CAAC,MAAM;kBACLD,YAAY,CAAC,MAAM,CAAC;kBACpBE,gBAAgB,CAAC,MAAM,CAAC;gBAC1B;cACF,CAAE;cAAAmG,QAAA,EACH;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACLjI,OAAA;cACE4H,SAAS,EAAE,YAAYrG,SAAS,KAAK,aAAa,GAAGE,aAAa,GAAG,EAAE,EAAG;cAC1E0G,OAAO,EAAEA,CAAA,KAAM;gBACb,IAAI5G,SAAS,KAAK,aAAa,EAAE;kBAC/BG,gBAAgB,CAACD,aAAa,KAAK,KAAK,GAAG,MAAM,GAAG,KAAK,CAAC;gBAC5D,CAAC,MAAM;kBACLD,YAAY,CAAC,aAAa,CAAC;kBAC3BE,gBAAgB,CAAC,KAAK,CAAC;gBACzB;cACF,CAAE;cAAAmG,QAAA,EACH;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACLjI,OAAA;cACE4H,SAAS,EAAE,YAAYrG,SAAS,KAAK,QAAQ,GAAGE,aAAa,GAAG,EAAE,EAAG;cACrE0G,OAAO,EAAEA,CAAA,KAAM;gBACb,IAAI5G,SAAS,KAAK,QAAQ,EAAE;kBAC1BG,gBAAgB,CAACD,aAAa,KAAK,KAAK,GAAG,MAAM,GAAG,KAAK,CAAC;gBAC5D,CAAC,MAAM;kBACLD,YAAY,CAAC,QAAQ,CAAC;kBACtBE,gBAAgB,CAAC,MAAM,CAAC;gBAC1B;cACF,CAAE;cAAAmG,QAAA,EACH;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACLjI,OAAA;cAAA6H,QAAA,EAAI;YAAQ;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACjBjI,OAAA;cACE4H,SAAS,EAAE,YAAYrG,SAAS,KAAK,YAAY,GAAGE,aAAa,GAAG,EAAE,EAAG;cACzE0G,OAAO,EAAEA,CAAA,KAAM;gBACb,IAAI5G,SAAS,KAAK,YAAY,EAAE;kBAC9BG,gBAAgB,CAACD,aAAa,KAAK,KAAK,GAAG,MAAM,GAAG,KAAK,CAAC;gBAC5D,CAAC,MAAM;kBACLD,YAAY,CAAC,YAAY,CAAC;kBAC1BE,gBAAgB,CAAC,MAAM,CAAC;gBAC1B;cACF,CAAE;cAAAmG,QAAA,EACH;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACLjI,OAAA;cAAA6H,QAAA,EAAI;YAAO;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACd;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eACRjI,OAAA;UAAA6H,QAAA,EACGnE,oBAAoB,CAACiC,GAAG,CAAC+C,WAAW,IAAI;YACvC,MAAMC,cAAc,GAAGvF,4BAA4B,CAACsF,WAAW,CAACjF,EAAE,CAAC;YACnE,MAAMmF,QAAQ,GAAGD,cAAc,GAAGnF,eAAe,CAACmF,cAAc,CAAC/G,UAAU,CAAC,GAAGiF,SAAS;YACxF,MAAMT,MAAM,GAAGsC,WAAW,CAAC/D,WAAW,GAAG,CAAC+D,WAAW,CAAC/D,WAAW,GAAI+D,WAAW,CAAC9D,YAAY,IAAI,CAAE;YAEnG,oBACE5E,OAAA;cAAyB4H,SAAS,EAAE1G,oBAAoB,CAAC2H,GAAG,CAACH,WAAW,CAACjF,EAAE,CAAC,GAAG,UAAU,GAAG,EAAG;cAAAoE,QAAA,gBAC7F7H,OAAA;gBAAA6H,QAAA,eACE7H,OAAA;kBACEqI,IAAI,EAAC,UAAU;kBACfI,OAAO,EAAEvH,oBAAoB,CAAC2H,GAAG,CAACH,WAAW,CAACjF,EAAE,CAAE;kBAClD6E,QAAQ,EAAGC,CAAC,IAAK;oBACf,MAAMpD,MAAM,GAAG,IAAI/D,GAAG,CAACF,oBAAoB,CAAC;oBAC5C,IAAIqH,CAAC,CAACC,MAAM,CAACC,OAAO,EAAE;sBACpBtD,MAAM,CAAC2D,GAAG,CAACJ,WAAW,CAACjF,EAAE,CAAC;oBAC5B,CAAC,MAAM;sBACL0B,MAAM,CAACC,MAAM,CAACsD,WAAW,CAACjF,EAAE,CAAC;oBAC/B;oBACAtC,uBAAuB,CAACgE,MAAM,CAAC;kBACjC;gBAAE;kBAAA2C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA,CAAC,eACLjI,OAAA;gBAAA6H,QAAA,EAAK,IAAIvD,IAAI,CAACoE,WAAW,CAACnE,YAAY,CAAC,CAACwE,kBAAkB,CAAC;cAAC;gBAAAjB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAClEjI,OAAA;gBAAI4H,SAAS,EAAC,kBAAkB;gBAACoB,KAAK,EAAEN,WAAW,CAAC7D,WAAY;gBAAAgD,QAAA,EAC7Da,WAAW,CAAC7D;cAAW;gBAAAiD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtB,CAAC,eACLjI,OAAA;gBAAI4H,SAAS,EAAE,eAAexB,MAAM,GAAG,CAAC,GAAG,OAAO,GAAG,QAAQ,EAAG;gBAAAyB,QAAA,EAC7D1B,cAAc,CAAC1B,IAAI,CAACC,GAAG,CAAC0B,MAAM,CAAC;cAAC;gBAAA0B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/B,CAAC,eACLjI,OAAA;gBAAA6H,QAAA,EACGe,QAAQ,gBACP5I,OAAA;kBAAK4H,SAAS,EAAC,cAAc;kBAACrB,KAAK,EAAE;oBAAE0C,eAAe,EAAEL,QAAQ,CAACM;kBAAM,CAAE;kBAAArB,QAAA,GACtEe,QAAQ,CAACO,IAAI,EACb,CAAAR,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAE9G,MAAM,kBACrB7B,OAAA;oBAAM4H,SAAS,EAAC,iBAAiB;oBAAAC,QAAA,EAAEc,cAAc,CAAC9G,MAAM,CAACuH,WAAW,CAAC;kBAAC;oBAAAtB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAC9E;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC,gBAENjI,OAAA;kBAAM4H,SAAS,EAAC,eAAe;kBAAAC,QAAA,EAAC;gBAAa;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM;cACpD;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACLjI,OAAA;gBAAA6H,QAAA,EACG,CAAAc,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAE7E,UAAU,MAAK+C,SAAS,gBACvC7G,OAAA;kBACE4H,SAAS,EAAC,kBAAkB;kBAC5BrB,KAAK,EAAE;oBAAE2C,KAAK,EAAEnC,kBAAkB,CAAC4B,cAAc,CAAC7E,UAAU;kBAAE,CAAE;kBAAA+D,QAAA,EAE/DjB,gBAAgB,CAAC+B,cAAc,CAAC7E,UAAU;gBAAC;kBAAAgE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxC,CAAC,gBAEPjI,OAAA;kBAAM4H,SAAS,EAAC,eAAe;kBAAAC,QAAA,EAAC;gBAAC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM;cACxC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACLjI,OAAA;gBAAA6H,QAAA,eACE7H,OAAA;kBAAK4H,SAAS,EAAC,gBAAgB;kBAAAC,QAAA,eAC7B7H,OAAA;oBACEqJ,KAAK,EAAE,CAAAV,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAE/G,UAAU,KAAI,EAAG;oBACxC0G,QAAQ,EAAGC,CAAC,IAAK;sBACf,IAAIA,CAAC,CAACC,MAAM,CAACa,KAAK,EAAE;wBAClBtE,0BAA0B,CAAC2D,WAAW,CAACjF,EAAE,EAAE8E,CAAC,CAACC,MAAM,CAACa,KAAK,CAAC;sBAC5D;oBACF,CAAE;oBACFzB,SAAS,EAAC,iBAAiB;oBAAAC,QAAA,gBAE3B7H,OAAA;sBAAQqJ,KAAK,EAAC,EAAE;sBAAAxB,QAAA,EAAC;oBAAkB;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,EAC3C3H,UAAU,CAACqF,GAAG,CAACpC,GAAG,iBACjBvD,OAAA;sBAAqBqJ,KAAK,EAAE9F,GAAG,CAACE,EAAG;sBAAAoE,QAAA,EAAEtE,GAAG,CAAC4F;oBAAI,GAAhC5F,GAAG,CAACE,EAAE;sBAAAqE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAmC,CACvD,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACI;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC;YAAA,GAhEES,WAAW,CAACjF,EAAE;cAAAqE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAiEnB,CAAC;UAET,CAAC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,EAEL/G,oBAAoB,CAACoI,IAAI,GAAG,CAAC,iBAC5BtJ,OAAA;MAAK4H,SAAS,EAAC,eAAe;MAAAC,QAAA,gBAC5B7H,OAAA;QAAK4H,SAAS,EAAC,YAAY;QAAAC,QAAA,GACxB3G,oBAAoB,CAACoI,IAAI,EAAC,0BAC7B;MAAA;QAAAxB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eACNjI,OAAA;QACE4H,SAAS,EAAC,iBAAiB;QAC3BO,OAAO,EAAEA,CAAA,KAAM9C,sBAAsB,CAACkE,KAAK,CAACC,IAAI,CAACtI,oBAAoB,CAAC,CAAE;QACxEkH,QAAQ,EAAExH,YAAa;QAAAiH,QAAA,EACxB;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACTjI,OAAA;QACE4H,SAAS,EAAC,mBAAmB;QAC7BO,OAAO,EAAEA,CAAA,KAAMhH,uBAAuB,CAAC,IAAIC,GAAG,CAAC,CAAC,CAAE;QAAAyG,QAAA,EACnD;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAAC9H,EAAA,CAnhBWF,yBAAmE;AAAAwJ,EAAA,GAAnExJ,yBAAmE;AAAA,IAAAwJ,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}