{"ast": null, "code": "/**\n * @license\n * Copyright 2018 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n *\n * =============================================================================\n */\nimport { util } from '@tensorflow/tfjs-core';\nimport { FileChunkIterator } from './file_chunk_iterator';\n/**\n * Provide a stream of chunks from a URL.\n *\n * Note this class first downloads the entire file into memory before providing\n * the first element from the stream.  This is because the Fetch API does not\n * yet reliably provide a reader stream for the response body.\n */\nexport async function urlChunkIterator(url, options = {}, fetchFunc) {\n  let urlString;\n  let requestInit;\n  if (typeof url === 'string') {\n    urlString = url;\n  } else {\n    urlString = url.url;\n    requestInit = getRequestInitFromRequest(url);\n  }\n  const response = await (fetchFunc || util.fetch)(urlString, requestInit);\n  if (response.ok) {\n    const uint8Array = new Uint8Array(await response.arrayBuffer());\n    return new FileChunkIterator(uint8Array, options);\n  } else {\n    throw new Error(response.statusText);\n  }\n}\n// Generate RequestInit from Request to match tf.util.fetch signature.\nconst getRequestInitFromRequest = request => {\n  const init = {\n    method: request.method,\n    headers: request.headers,\n    body: request.body,\n    mode: request.mode,\n    credentials: request.credentials,\n    cache: request.cache,\n    redirect: request.redirect,\n    referrer: request.referrer,\n    integrity: request.integrity\n  };\n  return init;\n};", "map": {"version": 3, "names": ["util", "FileChunkIterator", "urlChunkIterator", "url", "options", "fetchFunc", "urlString", "requestInit", "getRequestInitFromRequest", "response", "fetch", "ok", "uint8Array", "Uint8Array", "arrayBuffer", "Error", "statusText", "request", "init", "method", "headers", "body", "mode", "credentials", "cache", "redirect", "referrer", "integrity"], "sources": ["C:\\tfjs-data\\src\\iterators\\url_chunk_iterator.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2018 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n *\n * =============================================================================\n */\n\nimport {util} from '@tensorflow/tfjs-core';\nimport {FileChunkIterator, FileChunkIteratorOptions} from './file_chunk_iterator';\n\n/**\n * Provide a stream of chunks from a URL.\n *\n * Note this class first downloads the entire file into memory before providing\n * the first element from the stream.  This is because the Fetch API does not\n * yet reliably provide a reader stream for the response body.\n */\nexport async function urlChunkIterator(\n    url: RequestInfo, options: FileChunkIteratorOptions = {},\n    fetchFunc?: Function) {\n  let urlString;\n  let requestInit;\n  if ((typeof url) === 'string') {\n    urlString = url as string;\n  } else {\n    urlString = (url as Request).url;\n    requestInit = getRequestInitFromRequest(url as Request);\n  }\n  const response = await (fetchFunc || util.fetch)(urlString, requestInit);\n  if (response.ok) {\n    const uint8Array = new Uint8Array(await response.arrayBuffer());\n    return new FileChunkIterator(uint8Array, options);\n  } else {\n    throw new Error(response.statusText);\n  }\n}\n\n// Generate RequestInit from Request to match tf.util.fetch signature.\nconst getRequestInitFromRequest = (request: Request) => {\n  const init = {\n    method: request.method,\n    headers: request.headers,\n    body: request.body,\n    mode: request.mode,\n    credentials: request.credentials,\n    cache: request.cache,\n    redirect: request.redirect,\n    referrer: request.referrer,\n    integrity: request.integrity,\n  };\n  return init;\n};\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;;AAkBA,SAAQA,IAAI,QAAO,uBAAuB;AAC1C,SAAQC,iBAAiB,QAAiC,uBAAuB;AAEjF;;;;;;;AAOA,OAAO,eAAeC,gBAAgBA,CAClCC,GAAgB,EAAEC,OAAA,GAAoC,EAAE,EACxDC,SAAoB;EACtB,IAAIC,SAAS;EACb,IAAIC,WAAW;EACf,IAAK,OAAOJ,GAAG,KAAM,QAAQ,EAAE;IAC7BG,SAAS,GAAGH,GAAa;GAC1B,MAAM;IACLG,SAAS,GAAIH,GAAe,CAACA,GAAG;IAChCI,WAAW,GAAGC,yBAAyB,CAACL,GAAc,CAAC;;EAEzD,MAAMM,QAAQ,GAAG,MAAM,CAACJ,SAAS,IAAIL,IAAI,CAACU,KAAK,EAAEJ,SAAS,EAAEC,WAAW,CAAC;EACxE,IAAIE,QAAQ,CAACE,EAAE,EAAE;IACf,MAAMC,UAAU,GAAG,IAAIC,UAAU,CAAC,MAAMJ,QAAQ,CAACK,WAAW,EAAE,CAAC;IAC/D,OAAO,IAAIb,iBAAiB,CAACW,UAAU,EAAER,OAAO,CAAC;GAClD,MAAM;IACL,MAAM,IAAIW,KAAK,CAACN,QAAQ,CAACO,UAAU,CAAC;;AAExC;AAEA;AACA,MAAMR,yBAAyB,GAAIS,OAAgB,IAAI;EACrD,MAAMC,IAAI,GAAG;IACXC,MAAM,EAAEF,OAAO,CAACE,MAAM;IACtBC,OAAO,EAAEH,OAAO,CAACG,OAAO;IACxBC,IAAI,EAAEJ,OAAO,CAACI,IAAI;IAClBC,IAAI,EAAEL,OAAO,CAACK,IAAI;IAClBC,WAAW,EAAEN,OAAO,CAACM,WAAW;IAChCC,KAAK,EAAEP,OAAO,CAACO,KAAK;IACpBC,QAAQ,EAAER,OAAO,CAACQ,QAAQ;IAC1BC,QAAQ,EAAET,OAAO,CAACS,QAAQ;IAC1BC,SAAS,EAAEV,OAAO,CAACU;GACpB;EACD,OAAOT,IAAI;AACb,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}