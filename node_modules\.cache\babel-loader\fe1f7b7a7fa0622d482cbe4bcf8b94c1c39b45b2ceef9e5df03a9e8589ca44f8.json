{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.transformReply = exports.transformArguments = exports.FIRST_KEY_INDEX = void 0;\nconst generic_transformers_1 = require(\"@redis/client/dist/lib/commands/generic-transformers\");\nexports.FIRST_KEY_INDEX = 1;\nfunction transformArguments(key, items, options) {\n  const args = ['BF.INSERT', key];\n  if (options?.CAPACITY) {\n    args.push('CAPACITY', options.CAPACITY.toString());\n  }\n  if (options?.ERROR) {\n    args.push('ERROR', options.ERROR.toString());\n  }\n  if (options?.EXPANSION) {\n    args.push('EXPANSION', options.EXPANSION.toString());\n  }\n  if (options?.NOCREATE) {\n    args.push('NOCREATE');\n  }\n  if (options?.NONSCALING) {\n    args.push('NONSCALING');\n  }\n  args.push('ITEMS');\n  return (0, generic_transformers_1.pushVerdictArguments)(args, items);\n}\nexports.transformArguments = transformArguments;\nvar generic_transformers_2 = require(\"@redis/client/dist/lib/commands/generic-transformers\");\nObject.defineProperty(exports, \"transformReply\", {\n  enumerable: true,\n  get: function () {\n    return generic_transformers_2.transformBooleanArrayReply;\n  }\n});", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "transformReply", "transformArguments", "FIRST_KEY_INDEX", "generic_transformers_1", "require", "key", "items", "options", "args", "CAPACITY", "push", "toString", "ERROR", "EXPANSION", "NOCREATE", "NONSCALING", "pushVerdictArguments", "generic_transformers_2", "enumerable", "get", "transformBooleanArrayReply"], "sources": ["C:/tmsft/node_modules/@redis/bloom/dist/commands/bloom/INSERT.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.transformReply = exports.transformArguments = exports.FIRST_KEY_INDEX = void 0;\nconst generic_transformers_1 = require(\"@redis/client/dist/lib/commands/generic-transformers\");\nexports.FIRST_KEY_INDEX = 1;\nfunction transformArguments(key, items, options) {\n    const args = ['BF.INSERT', key];\n    if (options?.CAPACITY) {\n        args.push('CAPACITY', options.CAPACITY.toString());\n    }\n    if (options?.ERROR) {\n        args.push('ERROR', options.ERROR.toString());\n    }\n    if (options?.EXPANSION) {\n        args.push('EXPANSION', options.EXPANSION.toString());\n    }\n    if (options?.NOCREATE) {\n        args.push('NOCREATE');\n    }\n    if (options?.NONSCALING) {\n        args.push('NONSCALING');\n    }\n    args.push('ITEMS');\n    return (0, generic_transformers_1.pushVerdictArguments)(args, items);\n}\nexports.transformArguments = transformArguments;\nvar generic_transformers_2 = require(\"@redis/client/dist/lib/commands/generic-transformers\");\nObject.defineProperty(exports, \"transformReply\", { enumerable: true, get: function () { return generic_transformers_2.transformBooleanArrayReply; } });\n"], "mappings": "AAAA,YAAY;;AACZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEC,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7DD,OAAO,CAACE,cAAc,GAAGF,OAAO,CAACG,kBAAkB,GAAGH,OAAO,CAACI,eAAe,GAAG,KAAK,CAAC;AACtF,MAAMC,sBAAsB,GAAGC,OAAO,CAAC,sDAAsD,CAAC;AAC9FN,OAAO,CAACI,eAAe,GAAG,CAAC;AAC3B,SAASD,kBAAkBA,CAACI,GAAG,EAAEC,KAAK,EAAEC,OAAO,EAAE;EAC7C,MAAMC,IAAI,GAAG,CAAC,WAAW,EAAEH,GAAG,CAAC;EAC/B,IAAIE,OAAO,EAAEE,QAAQ,EAAE;IACnBD,IAAI,CAACE,IAAI,CAAC,UAAU,EAAEH,OAAO,CAACE,QAAQ,CAACE,QAAQ,CAAC,CAAC,CAAC;EACtD;EACA,IAAIJ,OAAO,EAAEK,KAAK,EAAE;IAChBJ,IAAI,CAACE,IAAI,CAAC,OAAO,EAAEH,OAAO,CAACK,KAAK,CAACD,QAAQ,CAAC,CAAC,CAAC;EAChD;EACA,IAAIJ,OAAO,EAAEM,SAAS,EAAE;IACpBL,IAAI,CAACE,IAAI,CAAC,WAAW,EAAEH,OAAO,CAACM,SAAS,CAACF,QAAQ,CAAC,CAAC,CAAC;EACxD;EACA,IAAIJ,OAAO,EAAEO,QAAQ,EAAE;IACnBN,IAAI,CAACE,IAAI,CAAC,UAAU,CAAC;EACzB;EACA,IAAIH,OAAO,EAAEQ,UAAU,EAAE;IACrBP,IAAI,CAACE,IAAI,CAAC,YAAY,CAAC;EAC3B;EACAF,IAAI,CAACE,IAAI,CAAC,OAAO,CAAC;EAClB,OAAO,CAAC,CAAC,EAAEP,sBAAsB,CAACa,oBAAoB,EAAER,IAAI,EAAEF,KAAK,CAAC;AACxE;AACAR,OAAO,CAACG,kBAAkB,GAAGA,kBAAkB;AAC/C,IAAIgB,sBAAsB,GAAGb,OAAO,CAAC,sDAAsD,CAAC;AAC5FR,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,gBAAgB,EAAE;EAAEoB,UAAU,EAAE,IAAI;EAAEC,GAAG,EAAE,SAAAA,CAAA,EAAY;IAAE,OAAOF,sBAAsB,CAACG,0BAA0B;EAAE;AAAE,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}