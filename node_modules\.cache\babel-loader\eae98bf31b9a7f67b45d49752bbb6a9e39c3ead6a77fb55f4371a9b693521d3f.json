{"ast": null, "code": "/**\n * @license\n * Copyright 2021 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { StringNGrams } from '@tensorflow/tfjs-core';\nimport { stringNGramsImplCPU } from '../kernel_utils/shared';\nexport function stringNGrams(args) {\n  const {\n    inputs,\n    backend,\n    attrs\n  } = args;\n  const {\n    separator,\n    nGramWidths,\n    leftPad,\n    rightPad,\n    padWidth,\n    preserveShortSequences\n  } = attrs;\n  const {\n    data,\n    dataSplits\n  } = inputs;\n  const $data = backend.readSync(data.dataId);\n  const $dataSplits = backend.readSync(dataSplits.dataId);\n  const [nGrams, nGramsSplits] = stringNGramsImplCPU($data, $dataSplits, separator, nGramWidths, leftPad, rightPad, padWidth, preserveShortSequences);\n  return [backend.makeTensorInfo([nGrams.length], 'string', nGrams), backend.makeTensorInfo(dataSplits.shape, 'int32', nGramsSplits)];\n}\nexport const stringNGramsConfig = {\n  kernelName: StringNGrams,\n  backendName: 'webgl',\n  kernelFunc: stringNGrams\n};", "map": {"version": 3, "names": ["StringNGrams", "stringNGramsImplCPU", "stringNGrams", "args", "inputs", "backend", "attrs", "separator", "nGramWidths", "leftPad", "rightPad", "<PERSON><PERSON><PERSON><PERSON>", "preserveShortSequences", "data", "dataSplits", "$data", "readSync", "dataId", "$dataSplits", "nGrams", "nGramsSplits", "makeTensorInfo", "length", "shape", "stringNGramsConfig", "kernelName", "backendName", "kernelFunc"], "sources": ["C:\\tfjs-backend-webgl\\src\\kernels\\StringNGrams.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2021 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {KernelConfig, KernelFunc, StringNGrams, StringNGramsAttrs, StringNGramsInputs, TensorInfo} from '@tensorflow/tfjs-core';\n\nimport {MathBackendWebGL} from '../backend_webgl';\nimport {stringNGramsImplCPU} from '../kernel_utils/shared';\n\nexport function stringNGrams(args: {\n  inputs: StringNGramsInputs,\n  backend: MathBackendWebGL,\n  attrs: StringNGramsAttrs\n}): [TensorInfo, TensorInfo] {\n  const {inputs, backend, attrs} = args;\n  const {\n    separator,\n    nGramWidths,\n    leftPad,\n    rightPad,\n    padWidth,\n    preserveShortSequences\n  } = attrs;\n  const {data, dataSplits} = inputs;\n  const $data = backend.readSync(data.dataId) as Uint8Array[];\n  const $dataSplits = backend.readSync(dataSplits.dataId) as Int32Array;\n\n  const [nGrams, nGramsSplits] = stringNGramsImplCPU(\n      $data, $dataSplits, separator, nGramWidths, leftPad, rightPad, padWidth,\n      preserveShortSequences);\n  return [\n    backend.makeTensorInfo([nGrams.length], 'string', nGrams),\n    backend.makeTensorInfo(dataSplits.shape, 'int32', nGramsSplits),\n  ];\n}\n\nexport const stringNGramsConfig: KernelConfig = {\n  kernelName: StringNGrams,\n  backendName: 'webgl',\n  kernelFunc: stringNGrams as unknown as KernelFunc,\n};\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAiBA,SAAkCA,YAAY,QAA0D,uBAAuB;AAG/H,SAAQC,mBAAmB,QAAO,wBAAwB;AAE1D,OAAM,SAAUC,YAAYA,CAACC,IAI5B;EACC,MAAM;IAACC,MAAM;IAAEC,OAAO;IAAEC;EAAK,CAAC,GAAGH,IAAI;EACrC,MAAM;IACJI,SAAS;IACTC,WAAW;IACXC,OAAO;IACPC,QAAQ;IACRC,QAAQ;IACRC;EAAsB,CACvB,GAAGN,KAAK;EACT,MAAM;IAACO,IAAI;IAAEC;EAAU,CAAC,GAAGV,MAAM;EACjC,MAAMW,KAAK,GAAGV,OAAO,CAACW,QAAQ,CAACH,IAAI,CAACI,MAAM,CAAiB;EAC3D,MAAMC,WAAW,GAAGb,OAAO,CAACW,QAAQ,CAACF,UAAU,CAACG,MAAM,CAAe;EAErE,MAAM,CAACE,MAAM,EAAEC,YAAY,CAAC,GAAGnB,mBAAmB,CAC9Cc,KAAK,EAAEG,WAAW,EAAEX,SAAS,EAAEC,WAAW,EAAEC,OAAO,EAAEC,QAAQ,EAAEC,QAAQ,EACvEC,sBAAsB,CAAC;EAC3B,OAAO,CACLP,OAAO,CAACgB,cAAc,CAAC,CAACF,MAAM,CAACG,MAAM,CAAC,EAAE,QAAQ,EAAEH,MAAM,CAAC,EACzDd,OAAO,CAACgB,cAAc,CAACP,UAAU,CAACS,KAAK,EAAE,OAAO,EAAEH,YAAY,CAAC,CAChE;AACH;AAEA,OAAO,MAAMI,kBAAkB,GAAiB;EAC9CC,UAAU,EAAEzB,YAAY;EACxB0B,WAAW,EAAE,OAAO;EACpBC,UAAU,EAAEzB;CACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}