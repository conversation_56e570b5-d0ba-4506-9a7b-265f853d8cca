{"ast": null, "code": "/**\n * @license\n * Copyright 2017 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { backend_util } from '@tensorflow/tfjs-core';\nexport class BatchNormProgram {\n  constructor(xShape, meanShape, varianceShape, offsetShape, scaleShape, varianceEpsilon) {\n    this.outputShape = [];\n    this.variableNames = ['x', 'mean', 'variance'];\n    backend_util.assertAndGetBroadcastShape(xShape, meanShape);\n    backend_util.assertAndGetBroadcastShape(xShape, varianceShape);\n    let offsetSnippet = '0.0';\n    if (offsetShape != null) {\n      backend_util.assertAndGetBroadcastShape(xShape, offsetShape);\n      this.variableNames.push('offset');\n      offsetSnippet = 'getOffsetAtOutCoords()';\n    }\n    let scaleSnippet = '1.0';\n    if (scaleShape != null) {\n      backend_util.assertAndGetBroadcastShape(xShape, scaleShape);\n      this.variableNames.push('scale');\n      scaleSnippet = 'getScaleAtOutCoords()';\n    }\n    this.outputShape = xShape;\n    this.userCode = \"\\n      void main() {\\n        float x = getXAtOutCoords();\\n        float mean = getMeanAtOutCoords();\\n        float variance = getVarianceAtOutCoords();\\n        float offset = \".concat(offsetSnippet, \";\\n        float scale = \").concat(scaleSnippet, \";\\n        float inv = scale * inversesqrt(variance + float(\").concat(varianceEpsilon, \"));\\n        setOutput(dot(vec3(x, -mean, offset), vec3(inv, inv, 1)));\\n      }\\n    \");\n  }\n}", "map": {"version": 3, "names": ["backend_util", "BatchNormProgram", "constructor", "xShape", "meanShape", "varianceShape", "offsetShape", "scaleShape", "varianceEpsilon", "outputShape", "variableNames", "assertAndGetBroadcastShape", "offsetSnippet", "push", "scaleSnippet", "userCode", "concat"], "sources": ["C:\\tfjs-backend-webgl\\src\\batchnorm_gpu.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2017 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {backend_util} from '@tensorflow/tfjs-core';\nimport {GPGPUProgram} from './gpgpu_math';\n\nexport class BatchNormProgram implements GPGPUProgram {\n  variableNames: string[];\n  outputShape: number[] = [];\n  userCode: string;\n\n  constructor(\n      xShape: number[], meanShape: number[], varianceShape: number[],\n      offsetShape: number[]|null, scaleShape: number[]|null,\n      varianceEpsilon: number) {\n    this.variableNames = ['x', 'mean', 'variance'];\n    backend_util.assertAndGetBroadcastShape(xShape, meanShape);\n    backend_util.assertAndGetBroadcastShape(xShape, varianceShape);\n\n    let offsetSnippet = '0.0';\n    if (offsetShape != null) {\n      backend_util.assertAndGetBroadcastShape(xShape, offsetShape);\n      this.variableNames.push('offset');\n      offsetSnippet = 'getOffsetAtOutCoords()';\n    }\n\n    let scaleSnippet = '1.0';\n    if (scaleShape != null) {\n      backend_util.assertAndGetBroadcastShape(xShape, scaleShape);\n      this.variableNames.push('scale');\n      scaleSnippet = 'getScaleAtOutCoords()';\n    }\n\n    this.outputShape = xShape;\n    this.userCode = `\n      void main() {\n        float x = getXAtOutCoords();\n        float mean = getMeanAtOutCoords();\n        float variance = getVarianceAtOutCoords();\n        float offset = ${offsetSnippet};\n        float scale = ${scaleSnippet};\n        float inv = scale * inversesqrt(variance + float(${varianceEpsilon}));\n        setOutput(dot(vec3(x, -mean, offset), vec3(inv, inv, 1)));\n      }\n    `;\n  }\n}\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAiBA,SAAQA,YAAY,QAAO,uBAAuB;AAGlD,OAAM,MAAOC,gBAAgB;EAK3BC,YACIC,MAAgB,EAAEC,SAAmB,EAAEC,aAAuB,EAC9DC,WAA0B,EAAEC,UAAyB,EACrDC,eAAuB;IAN3B,KAAAC,WAAW,GAAa,EAAE;IAOxB,IAAI,CAACC,aAAa,GAAG,CAAC,GAAG,EAAE,MAAM,EAAE,UAAU,CAAC;IAC9CV,YAAY,CAACW,0BAA0B,CAACR,MAAM,EAAEC,SAAS,CAAC;IAC1DJ,YAAY,CAACW,0BAA0B,CAACR,MAAM,EAAEE,aAAa,CAAC;IAE9D,IAAIO,aAAa,GAAG,KAAK;IACzB,IAAIN,WAAW,IAAI,IAAI,EAAE;MACvBN,YAAY,CAACW,0BAA0B,CAACR,MAAM,EAAEG,WAAW,CAAC;MAC5D,IAAI,CAACI,aAAa,CAACG,IAAI,CAAC,QAAQ,CAAC;MACjCD,aAAa,GAAG,wBAAwB;;IAG1C,IAAIE,YAAY,GAAG,KAAK;IACxB,IAAIP,UAAU,IAAI,IAAI,EAAE;MACtBP,YAAY,CAACW,0BAA0B,CAACR,MAAM,EAAEI,UAAU,CAAC;MAC3D,IAAI,CAACG,aAAa,CAACG,IAAI,CAAC,OAAO,CAAC;MAChCC,YAAY,GAAG,uBAAuB;;IAGxC,IAAI,CAACL,WAAW,GAAGN,MAAM;IACzB,IAAI,CAACY,QAAQ,0LAAAC,MAAA,CAKQJ,aAAa,+BAAAI,MAAA,CACdF,YAAY,kEAAAE,MAAA,CACuBR,eAAe,2FAGrE;EACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}