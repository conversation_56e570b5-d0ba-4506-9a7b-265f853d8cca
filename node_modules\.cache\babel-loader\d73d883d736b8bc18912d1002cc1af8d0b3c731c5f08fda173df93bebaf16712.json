{"ast": null, "code": "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { getCoordsDataType } from './shader_compiler';\nexport class MirrorPadProgram {\n  constructor(xShape, paddings, mode) {\n    this.variableNames = ['x'];\n    this.outputShape = paddings.map((p, i) => p[0] /* beforePad */ + xShape[i] + p[1] /* afterPad */);\n    const rank = xShape.length;\n    const dtype = getCoordsDataType(rank);\n    const start = paddings.map(p => p[0]).join(',');\n    const end = paddings.map((p, i) => p[0] + xShape[i]).join(',');\n    const unpackedCoords = ['coords[0]', 'coords[1]', 'coords[2]', 'coords[3]'].slice(0, rank);\n    const offset = mode === 'reflect' ? 0 : 1;\n    if (rank === 1) {\n      this.userCode = `\n        int start = ${start};\n        int end = ${end};\n\n        void main() {\n          int outC = getOutputCoords();\n          if (outC < start) {\n            outC = start * 2 - outC - ${offset};\n          } else if(outC >= end) {\n            outC = (end - 1) * 2 - outC + ${offset};\n          }\n          setOutput(getX(outC - start));\n        }\n      `;\n      return;\n    }\n    this.userCode = `\n      ${dtype} start = ${dtype}(${start});\n      ${dtype} end = ${dtype}(${end});\n\n      void main() {\n        ${dtype} outC = getOutputCoords();\n        for (int i = 0; i < ${rank}; i++) {\n          if (outC[i] < start[i]) {\n            outC[i] = start[i] * 2 - outC[i] - ${offset};\n          } else if(outC[i] >= end[i]) {\n            outC[i] = (end[i] - 1) * 2 - outC[i] + ${offset};\n          }\n        }\n        ${dtype} coords = outC - start;\n        setOutput(getX(${unpackedCoords}));\n      }\n    `;\n  }\n}", "map": {"version": 3, "names": ["getCoordsDataType", "MirrorPadProgram", "constructor", "xShape", "paddings", "mode", "variableNames", "outputShape", "map", "p", "i", "rank", "length", "dtype", "start", "join", "end", "unpackedCoords", "slice", "offset", "userCode"], "sources": ["C:\\tfjs-backend-webgl\\src\\mirror_pad_gpu.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {GPGPUProgram} from './gpgpu_math';\nimport {getCoordsDataType} from './shader_compiler';\n\nexport class MirrorPadProgram implements GPGPUProgram {\n  variableNames = ['x'];\n  outputShape: number[];\n  userCode: string;\n\n  constructor(\n      xShape: number[], paddings: Array<[number, number]>,\n      mode: 'reflect'|'symmetric') {\n    this.outputShape = paddings.map(\n        (p, i) => p[0] /* beforePad */ + xShape[i] + p[1] /* afterPad */);\n    const rank = xShape.length;\n    const dtype = getCoordsDataType(rank);\n\n    const start = paddings.map(p => p[0]).join(',');\n    const end = paddings.map((p, i) => p[0] + xShape[i]).join(',');\n    const unpackedCoords =\n        ['coords[0]', 'coords[1]', 'coords[2]', 'coords[3]'].slice(0, rank);\n    const offset = mode === 'reflect' ? 0 : 1;\n\n    if (rank === 1) {\n      this.userCode = `\n        int start = ${start};\n        int end = ${end};\n\n        void main() {\n          int outC = getOutputCoords();\n          if (outC < start) {\n            outC = start * 2 - outC - ${offset};\n          } else if(outC >= end) {\n            outC = (end - 1) * 2 - outC + ${offset};\n          }\n          setOutput(getX(outC - start));\n        }\n      `;\n      return;\n    }\n    this.userCode = `\n      ${dtype} start = ${dtype}(${start});\n      ${dtype} end = ${dtype}(${end});\n\n      void main() {\n        ${dtype} outC = getOutputCoords();\n        for (int i = 0; i < ${rank}; i++) {\n          if (outC[i] < start[i]) {\n            outC[i] = start[i] * 2 - outC[i] - ${offset};\n          } else if(outC[i] >= end[i]) {\n            outC[i] = (end[i] - 1) * 2 - outC[i] + ${offset};\n          }\n        }\n        ${dtype} coords = outC - start;\n        setOutput(getX(${unpackedCoords}));\n      }\n    `;\n  }\n}\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAkBA,SAAQA,iBAAiB,QAAO,mBAAmB;AAEnD,OAAM,MAAOC,gBAAgB;EAK3BC,YACIC,MAAgB,EAAEC,QAAiC,EACnDC,IAA2B;IAN/B,KAAAC,aAAa,GAAG,CAAC,GAAG,CAAC;IAOnB,IAAI,CAACC,WAAW,GAAGH,QAAQ,CAACI,GAAG,CAC3B,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAAC,CAAC,CAAC,CAAC,kBAAkBN,MAAM,CAACO,CAAC,CAAC,GAAGD,CAAC,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC;IACrE,MAAME,IAAI,GAAGR,MAAM,CAACS,MAAM;IAC1B,MAAMC,KAAK,GAAGb,iBAAiB,CAACW,IAAI,CAAC;IAErC,MAAMG,KAAK,GAAGV,QAAQ,CAACI,GAAG,CAACC,CAAC,IAAIA,CAAC,CAAC,CAAC,CAAC,CAAC,CAACM,IAAI,CAAC,GAAG,CAAC;IAC/C,MAAMC,GAAG,GAAGZ,QAAQ,CAACI,GAAG,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAAC,CAAC,CAAC,GAAGN,MAAM,CAACO,CAAC,CAAC,CAAC,CAACK,IAAI,CAAC,GAAG,CAAC;IAC9D,MAAME,cAAc,GAChB,CAAC,WAAW,EAAE,WAAW,EAAE,WAAW,EAAE,WAAW,CAAC,CAACC,KAAK,CAAC,CAAC,EAAEP,IAAI,CAAC;IACvE,MAAMQ,MAAM,GAAGd,IAAI,KAAK,SAAS,GAAG,CAAC,GAAG,CAAC;IAEzC,IAAIM,IAAI,KAAK,CAAC,EAAE;MACd,IAAI,CAACS,QAAQ,GAAG;sBACAN,KAAK;oBACPE,GAAG;;;;;wCAKiBG,MAAM;;4CAEFA,MAAM;;;;OAI3C;MACD;;IAEF,IAAI,CAACC,QAAQ,GAAG;QACZP,KAAK,YAAYA,KAAK,IAAIC,KAAK;QAC/BD,KAAK,UAAUA,KAAK,IAAIG,GAAG;;;UAGzBH,KAAK;8BACeF,IAAI;;iDAEeQ,MAAM;;qDAEFA,MAAM;;;UAGjDN,KAAK;yBACUI,cAAc;;KAElC;EACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}