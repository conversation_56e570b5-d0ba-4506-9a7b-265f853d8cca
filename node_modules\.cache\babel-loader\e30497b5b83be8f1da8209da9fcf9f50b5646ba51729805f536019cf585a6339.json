{"ast": null, "code": "/**\n * @license\n * Copyright 2019 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { getChannels } from './packing_util';\nimport { getCoordsDataType } from './shader_compiler';\nexport class SlicePackedProgram {\n  constructor(destSize) {\n    this.variableNames = ['source'];\n    this.packedInputs = true;\n    this.packedOutput = true;\n    this.outputShape = destSize;\n    this.rank = destSize.length;\n    this.customUniforms = [{\n      name: 'start',\n      arrayIndex: this.rank,\n      type: 'int'\n    }];\n    const dtype = getCoordsDataType(this.rank);\n    const coords = getChannels('coords', this.rank);\n    const sourceLoc = getChannels('sourceLoc', this.rank);\n    const innerDims = this.rank === 1 ? 'sourceLoc' : `vec2(${sourceLoc.slice(-2).join()})`;\n    const getChannel = `getChannel(getSource(${sourceLoc.join()}), ${innerDims})`;\n    const upperRow = `\n      result.x = ${getChannel};\n      if (++${coords[this.rank - 1]} < ${destSize[this.rank - 1]}) {\n        ++${sourceLoc[this.rank - 1]};\n        result.y = ${getChannel};\n        --${sourceLoc[this.rank - 1]};\n      }\n    `;\n    const lowerRow = this.rank === 1 ? '' : `\n      --${coords[this.rank - 1]};\n      if (++${coords[this.rank - 2]} < ${destSize[this.rank - 2]}) {\n        ++${sourceLoc[this.rank - 2]};\n        result.z = ${getChannel};\n        if (++${coords[this.rank - 1]} < ${destSize[this.rank - 1]}) {\n          ++${sourceLoc[this.rank - 1]};\n          result.w = ${getChannel};\n        }\n      }\n    `;\n    const sourceLocSetup = this.rank <= 4 ? `sourceLoc = coords +\n            ${dtype}(${destSize.map((_, i) => `start[${i}]`).join()});` : destSize.map((_, i) => `${sourceLoc[i]} = ${coords[i]} + start[${i}];`).join('\\n');\n    this.userCode = `\n      void main() {\n        ${dtype} coords = getOutputCoords();\n        ${dtype} sourceLoc;\n        ${sourceLocSetup}\n        vec4 result = vec4(0.);\n        ${upperRow}\n        ${lowerRow}\n        setOutput(result);\n      }\n    `;\n  }\n}", "map": {"version": 3, "names": ["getChannels", "getCoordsDataType", "SlicePackedProgram", "constructor", "destSize", "variableNames", "packedInputs", "packedOutput", "outputShape", "rank", "length", "customUniforms", "name", "arrayIndex", "type", "dtype", "coords", "sourceLoc", "innerDims", "slice", "join", "getChannel", "upperRow", "lowerRow", "sourceLocSetup", "map", "_", "i", "userCode"], "sources": ["C:\\tfjs-backend-webgl\\src\\slice_packed_gpu.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2019 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {GPGPUProgram} from './gpgpu_math';\nimport {getChannels} from './packing_util';\nimport {getCoordsDataType, UniformType} from './shader_compiler';\n\nexport class SlicePackedProgram implements GPGPUProgram {\n  variableNames = ['source'];\n  packedInputs = true;\n  packedOutput = true;\n  outputShape: number[];\n  userCode: string;\n  rank: number;\n  customUniforms: Array<{name: string; arrayIndex: number; type: UniformType;}>;\n\n  constructor(destSize: number[]) {\n    this.outputShape = destSize;\n    this.rank = destSize.length;\n    this.customUniforms = [{name: 'start', arrayIndex: this.rank, type: 'int'}];\n    const dtype = getCoordsDataType(this.rank);\n    const coords = getChannels('coords', this.rank);\n    const sourceLoc = getChannels('sourceLoc', this.rank);\n\n    const innerDims =\n        this.rank === 1 ? 'sourceLoc' : `vec2(${sourceLoc.slice(-2).join()})`;\n    const getChannel =\n        `getChannel(getSource(${sourceLoc.join()}), ${innerDims})`;\n    const upperRow = `\n      result.x = ${getChannel};\n      if (++${coords[this.rank - 1]} < ${destSize[this.rank - 1]}) {\n        ++${sourceLoc[this.rank - 1]};\n        result.y = ${getChannel};\n        --${sourceLoc[this.rank - 1]};\n      }\n    `;\n    const lowerRow = this.rank === 1 ? '' : `\n      --${coords[this.rank - 1]};\n      if (++${coords[this.rank - 2]} < ${destSize[this.rank - 2]}) {\n        ++${sourceLoc[this.rank - 2]};\n        result.z = ${getChannel};\n        if (++${coords[this.rank - 1]} < ${destSize[this.rank - 1]}) {\n          ++${sourceLoc[this.rank - 1]};\n          result.w = ${getChannel};\n        }\n      }\n    `;\n\n    const sourceLocSetup = this.rank <= 4 ?\n        `sourceLoc = coords +\n            ${dtype}(${destSize.map((_, i) => `start[${i}]`).join()});` :\n        destSize.map((_, i) => `${sourceLoc[i]} = ${coords[i]} + start[${i}];`)\n            .join('\\n');\n    this.userCode = `\n      void main() {\n        ${dtype} coords = getOutputCoords();\n        ${dtype} sourceLoc;\n        ${sourceLocSetup}\n        vec4 result = vec4(0.);\n        ${upperRow}\n        ${lowerRow}\n        setOutput(result);\n      }\n    `;\n  }\n}\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAkBA,SAAQA,WAAW,QAAO,gBAAgB;AAC1C,SAAQC,iBAAiB,QAAoB,mBAAmB;AAEhE,OAAM,MAAOC,kBAAkB;EAS7BC,YAAYC,QAAkB;IAR9B,KAAAC,aAAa,GAAG,CAAC,QAAQ,CAAC;IAC1B,KAAAC,YAAY,GAAG,IAAI;IACnB,KAAAC,YAAY,GAAG,IAAI;IAOjB,IAAI,CAACC,WAAW,GAAGJ,QAAQ;IAC3B,IAAI,CAACK,IAAI,GAAGL,QAAQ,CAACM,MAAM;IAC3B,IAAI,CAACC,cAAc,GAAG,CAAC;MAACC,IAAI,EAAE,OAAO;MAAEC,UAAU,EAAE,IAAI,CAACJ,IAAI;MAAEK,IAAI,EAAE;IAAK,CAAC,CAAC;IAC3E,MAAMC,KAAK,GAAGd,iBAAiB,CAAC,IAAI,CAACQ,IAAI,CAAC;IAC1C,MAAMO,MAAM,GAAGhB,WAAW,CAAC,QAAQ,EAAE,IAAI,CAACS,IAAI,CAAC;IAC/C,MAAMQ,SAAS,GAAGjB,WAAW,CAAC,WAAW,EAAE,IAAI,CAACS,IAAI,CAAC;IAErD,MAAMS,SAAS,GACX,IAAI,CAACT,IAAI,KAAK,CAAC,GAAG,WAAW,GAAG,QAAQQ,SAAS,CAACE,KAAK,CAAC,CAAC,CAAC,CAAC,CAACC,IAAI,EAAE,GAAG;IACzE,MAAMC,UAAU,GACZ,wBAAwBJ,SAAS,CAACG,IAAI,EAAE,MAAMF,SAAS,GAAG;IAC9D,MAAMI,QAAQ,GAAG;mBACFD,UAAU;cACfL,MAAM,CAAC,IAAI,CAACP,IAAI,GAAG,CAAC,CAAC,MAAML,QAAQ,CAAC,IAAI,CAACK,IAAI,GAAG,CAAC,CAAC;YACpDQ,SAAS,CAAC,IAAI,CAACR,IAAI,GAAG,CAAC,CAAC;qBACfY,UAAU;YACnBJ,SAAS,CAAC,IAAI,CAACR,IAAI,GAAG,CAAC,CAAC;;KAE/B;IACD,MAAMc,QAAQ,GAAG,IAAI,CAACd,IAAI,KAAK,CAAC,GAAG,EAAE,GAAG;UAClCO,MAAM,CAAC,IAAI,CAACP,IAAI,GAAG,CAAC,CAAC;cACjBO,MAAM,CAAC,IAAI,CAACP,IAAI,GAAG,CAAC,CAAC,MAAML,QAAQ,CAAC,IAAI,CAACK,IAAI,GAAG,CAAC,CAAC;YACpDQ,SAAS,CAAC,IAAI,CAACR,IAAI,GAAG,CAAC,CAAC;qBACfY,UAAU;gBACfL,MAAM,CAAC,IAAI,CAACP,IAAI,GAAG,CAAC,CAAC,MAAML,QAAQ,CAAC,IAAI,CAACK,IAAI,GAAG,CAAC,CAAC;cACpDQ,SAAS,CAAC,IAAI,CAACR,IAAI,GAAG,CAAC,CAAC;uBACfY,UAAU;;;KAG5B;IAED,MAAMG,cAAc,GAAG,IAAI,CAACf,IAAI,IAAI,CAAC,GACjC;cACMM,KAAK,IAAIX,QAAQ,CAACqB,GAAG,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK,SAASA,CAAC,GAAG,CAAC,CAACP,IAAI,EAAE,IAAI,GAC/DhB,QAAQ,CAACqB,GAAG,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK,GAAGV,SAAS,CAACU,CAAC,CAAC,MAAMX,MAAM,CAACW,CAAC,CAAC,YAAYA,CAAC,IAAI,CAAC,CAClEP,IAAI,CAAC,IAAI,CAAC;IACnB,IAAI,CAACQ,QAAQ,GAAG;;UAEVb,KAAK;UACLA,KAAK;UACLS,cAAc;;UAEdF,QAAQ;UACRC,QAAQ;;;KAGb;EACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}