{"ast": null, "code": "/**\n * @license\n * Copyright 2018 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n// tslint:disable-next-line: no-imports-from-dist\nimport * as tfOps from '@tensorflow/tfjs-core/dist/ops/ops_for_converter';\nimport { cloneTensor, getParamValue, getTensor } from './utils';\nexport const executeOp = (node, tensorMap, context, ops = tfOps) => {\n  switch (node.op) {\n    case 'Const':\n      {\n        return tensorMap[node.name];\n      }\n    case 'PlaceholderWithDefault':\n      const def = getParamValue('default', node, tensorMap, context);\n      return [getTensor(node.name, tensorMap, context) || def];\n    case 'Placeholder':\n      return [getTensor(node.name, tensorMap, context)];\n    case 'Identity':\n    case 'StopGradient':\n    case 'FakeQuantWithMinMaxVars':\n      {\n        // This op is currently ignored.\n        const data = getParamValue('x', node, tensorMap, context);\n        return [cloneTensor(data)];\n      }\n    case 'IdentityN':\n      return getParamValue('x', node, tensorMap, context).map(t => cloneTensor(t));\n    case 'Snapshot':\n      const snapshot = getParamValue('x', node, tensorMap, context);\n      return [cloneTensor(snapshot)];\n    case 'Shape':\n      return [ops.tensor1d(getParamValue('x', node, tensorMap, context).shape, 'int32')];\n    case 'ShapeN':\n      return getParamValue('x', node, tensorMap, context).map(t => ops.tensor1d(t.shape));\n    case 'Size':\n      return [ops.scalar(getParamValue('x', node, tensorMap, context).size, 'int32')];\n    case 'Rank':\n      return [ops.scalar(getParamValue('x', node, tensorMap, context).rank, 'int32')];\n    case 'NoOp':\n      return [ops.scalar(1)];\n    case 'Print':\n      const input = getParamValue('x', node, tensorMap, context);\n      const data = getParamValue('data', node, tensorMap, context);\n      const message = getParamValue('message', node, tensorMap, context);\n      const summarize = getParamValue('summarize', node, tensorMap, context);\n      console.warn('The graph has a tf.print() operation,' + 'usually used for debugging, which slows down performance.');\n      console.log(message);\n      for (let i = 0; i < data.length; i++) {\n        console.log(Array.prototype.slice.call(data[i].dataSync()).slice(0, summarize));\n      }\n      return [input];\n    default:\n      throw TypeError(`Node type ${node.op} is not implemented`);\n  }\n};\nexport const CATEGORY = 'graph';", "map": {"version": 3, "names": ["tfOps", "cloneTensor", "getParamValue", "getTensor", "executeOp", "node", "tensorMap", "context", "ops", "op", "name", "def", "data", "map", "t", "snapshot", "tensor1d", "shape", "scalar", "size", "rank", "input", "message", "summarize", "console", "warn", "log", "i", "length", "Array", "prototype", "slice", "call", "dataSync", "TypeError", "CATEGORY"], "sources": ["C:\\tfjs-converter\\src\\operations\\executors\\graph_executor.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2018 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {Tensor} from '@tensorflow/tfjs-core';\n// tslint:disable-next-line: no-imports-from-dist\nimport * as tfOps from '@tensorflow/tfjs-core/dist/ops/ops_for_converter';\n\nimport {NamedTensorsMap} from '../../data/types';\nimport {ExecutionContext} from '../../executor/execution_context';\nimport {InternalOpExecutor, Node} from '../types';\n\nimport {cloneTensor, getParamValue, getTensor} from './utils';\n\nexport const executeOp: InternalOpExecutor =\n    (node: Node, tensorMap: NamedTensorsMap,\n     context: ExecutionContext, ops = tfOps): Tensor[] => {\n      switch (node.op) {\n        case 'Const': {\n          return tensorMap[node.name];\n        }\n        case 'PlaceholderWithDefault':\n          const def =\n              getParamValue('default', node, tensorMap, context) as Tensor;\n          return [getTensor(node.name, tensorMap, context) || def];\n        case 'Placeholder':\n          return [getTensor(node.name, tensorMap, context)];\n        case 'Identity':\n        case 'StopGradient':\n        case 'FakeQuantWithMinMaxVars': {  // This op is currently ignored.\n          const data = getParamValue('x', node, tensorMap, context) as Tensor;\n          return [cloneTensor(data)];\n        }\n        case 'IdentityN':\n          return (getParamValue('x', node, tensorMap, context) as Tensor[])\n              .map((t: Tensor) => cloneTensor(t));\n        case 'Snapshot':\n          const snapshot =\n              (getParamValue('x', node, tensorMap, context) as Tensor);\n          return [cloneTensor(snapshot)];\n        case 'Shape':\n          return [ops.tensor1d(\n              (getParamValue('x', node, tensorMap, context) as Tensor).shape,\n              'int32')];\n        case 'ShapeN':\n          return (getParamValue('x', node, tensorMap, context) as Tensor[])\n              .map((t: Tensor) => ops.tensor1d(t.shape));\n        case 'Size':\n          return [ops.scalar(\n              (getParamValue('x', node, tensorMap, context) as Tensor).size,\n              'int32')];\n        case 'Rank':\n          return [ops.scalar(\n              (getParamValue('x', node, tensorMap, context) as Tensor).rank,\n              'int32')];\n        case 'NoOp':\n          return [ops.scalar(1)];\n        case 'Print':\n          const input = getParamValue('x', node, tensorMap, context) as Tensor;\n          const data =\n              getParamValue('data', node, tensorMap, context) as Tensor[];\n          const message =\n              getParamValue('message', node, tensorMap, context) as string;\n          const summarize =\n              getParamValue('summarize', node, tensorMap, context) as number;\n          console.warn(\n              'The graph has a tf.print() operation,' +\n              'usually used for debugging, which slows down performance.');\n          console.log(message);\n          for (let i = 0; i < data.length; i++) {\n            console.log(Array.prototype.slice.call(data[i].dataSync())\n                            .slice(0, summarize));\n          }\n          return [input];\n\n        default:\n          throw TypeError(`Node type ${node.op} is not implemented`);\n      }\n    };\n\nexport const CATEGORY = 'graph';\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAkBA;AACA,OAAO,KAAKA,KAAK,MAAM,kDAAkD;AAMzE,SAAQC,WAAW,EAAEC,aAAa,EAAEC,SAAS,QAAO,SAAS;AAE7D,OAAO,MAAMC,SAAS,GAClBA,CAACC,IAAU,EAAEC,SAA0B,EACtCC,OAAyB,EAAEC,GAAG,GAAGR,KAAK,KAAc;EACnD,QAAQK,IAAI,CAACI,EAAE;IACb,KAAK,OAAO;MAAE;QACZ,OAAOH,SAAS,CAACD,IAAI,CAACK,IAAI,CAAC;;IAE7B,KAAK,wBAAwB;MAC3B,MAAMC,GAAG,GACLT,aAAa,CAAC,SAAS,EAAEG,IAAI,EAAEC,SAAS,EAAEC,OAAO,CAAW;MAChE,OAAO,CAACJ,SAAS,CAACE,IAAI,CAACK,IAAI,EAAEJ,SAAS,EAAEC,OAAO,CAAC,IAAII,GAAG,CAAC;IAC1D,KAAK,aAAa;MAChB,OAAO,CAACR,SAAS,CAACE,IAAI,CAACK,IAAI,EAAEJ,SAAS,EAAEC,OAAO,CAAC,CAAC;IACnD,KAAK,UAAU;IACf,KAAK,cAAc;IACnB,KAAK,yBAAyB;MAAE;QAAG;QACjC,MAAMK,IAAI,GAAGV,aAAa,CAAC,GAAG,EAAEG,IAAI,EAAEC,SAAS,EAAEC,OAAO,CAAW;QACnE,OAAO,CAACN,WAAW,CAACW,IAAI,CAAC,CAAC;;IAE5B,KAAK,WAAW;MACd,OAAQV,aAAa,CAAC,GAAG,EAAEG,IAAI,EAAEC,SAAS,EAAEC,OAAO,CAAc,CAC5DM,GAAG,CAAEC,CAAS,IAAKb,WAAW,CAACa,CAAC,CAAC,CAAC;IACzC,KAAK,UAAU;MACb,MAAMC,QAAQ,GACTb,aAAa,CAAC,GAAG,EAAEG,IAAI,EAAEC,SAAS,EAAEC,OAAO,CAAY;MAC5D,OAAO,CAACN,WAAW,CAACc,QAAQ,CAAC,CAAC;IAChC,KAAK,OAAO;MACV,OAAO,CAACP,GAAG,CAACQ,QAAQ,CACfd,aAAa,CAAC,GAAG,EAAEG,IAAI,EAAEC,SAAS,EAAEC,OAAO,CAAY,CAACU,KAAK,EAC9D,OAAO,CAAC,CAAC;IACf,KAAK,QAAQ;MACX,OAAQf,aAAa,CAAC,GAAG,EAAEG,IAAI,EAAEC,SAAS,EAAEC,OAAO,CAAc,CAC5DM,GAAG,CAAEC,CAAS,IAAKN,GAAG,CAACQ,QAAQ,CAACF,CAAC,CAACG,KAAK,CAAC,CAAC;IAChD,KAAK,MAAM;MACT,OAAO,CAACT,GAAG,CAACU,MAAM,CACbhB,aAAa,CAAC,GAAG,EAAEG,IAAI,EAAEC,SAAS,EAAEC,OAAO,CAAY,CAACY,IAAI,EAC7D,OAAO,CAAC,CAAC;IACf,KAAK,MAAM;MACT,OAAO,CAACX,GAAG,CAACU,MAAM,CACbhB,aAAa,CAAC,GAAG,EAAEG,IAAI,EAAEC,SAAS,EAAEC,OAAO,CAAY,CAACa,IAAI,EAC7D,OAAO,CAAC,CAAC;IACf,KAAK,MAAM;MACT,OAAO,CAACZ,GAAG,CAACU,MAAM,CAAC,CAAC,CAAC,CAAC;IACxB,KAAK,OAAO;MACV,MAAMG,KAAK,GAAGnB,aAAa,CAAC,GAAG,EAAEG,IAAI,EAAEC,SAAS,EAAEC,OAAO,CAAW;MACpE,MAAMK,IAAI,GACNV,aAAa,CAAC,MAAM,EAAEG,IAAI,EAAEC,SAAS,EAAEC,OAAO,CAAa;MAC/D,MAAMe,OAAO,GACTpB,aAAa,CAAC,SAAS,EAAEG,IAAI,EAAEC,SAAS,EAAEC,OAAO,CAAW;MAChE,MAAMgB,SAAS,GACXrB,aAAa,CAAC,WAAW,EAAEG,IAAI,EAAEC,SAAS,EAAEC,OAAO,CAAW;MAClEiB,OAAO,CAACC,IAAI,CACR,uCAAuC,GACvC,2DAA2D,CAAC;MAChED,OAAO,CAACE,GAAG,CAACJ,OAAO,CAAC;MACpB,KAAK,IAAIK,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGf,IAAI,CAACgB,MAAM,EAAED,CAAC,EAAE,EAAE;QACpCH,OAAO,CAACE,GAAG,CAACG,KAAK,CAACC,SAAS,CAACC,KAAK,CAACC,IAAI,CAACpB,IAAI,CAACe,CAAC,CAAC,CAACM,QAAQ,EAAE,CAAC,CACzCF,KAAK,CAAC,CAAC,EAAER,SAAS,CAAC,CAAC;;MAEvC,OAAO,CAACF,KAAK,CAAC;IAEhB;MACE,MAAMa,SAAS,CAAC,aAAa7B,IAAI,CAACI,EAAE,qBAAqB,CAAC;;AAEhE,CAAC;AAEL,OAAO,MAAM0B,QAAQ,GAAG,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}