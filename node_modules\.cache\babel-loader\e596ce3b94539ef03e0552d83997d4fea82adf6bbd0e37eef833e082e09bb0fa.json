{"ast": null, "code": "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { LogSoftmax } from '../kernel_names';\nimport { exp } from '../ops/exp';\nimport { mul } from '../ops/mul';\nimport { sub } from '../ops/sub';\nimport { sum } from '../ops/sum';\nexport const logSoftmaxGradConfig = {\n  kernelName: LogSoftmax,\n  inputsToSave: [],\n  outputsToSave: [true],\n  gradFunc: (dy, saved, attrs) => {\n    const [value] = saved;\n    const {\n      axis\n    } = attrs;\n    return {\n      logits: () => {\n        const keepDims = true;\n        const softmax = exp(value);\n        return sub(dy, mul(sum(dy, axis, keepDims), softmax));\n      }\n    };\n  }\n};", "map": {"version": 3, "names": ["LogSoftmax", "exp", "mul", "sub", "sum", "logSoftmaxGradConfig", "kernelName", "inputsToSave", "outputsToSave", "grad<PERSON>unc", "dy", "saved", "attrs", "value", "axis", "logits", "keepDims", "softmax"], "sources": ["C:\\tfjs-core\\src\\gradients\\LogSoftmax_grad.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {LogSoftmax, LogSoftmaxAttrs} from '../kernel_names';\nimport {GradConfig, NamedAttrMap} from '../kernel_registry';\nimport {exp} from '../ops/exp';\nimport {mul} from '../ops/mul';\nimport {sub} from '../ops/sub';\nimport {sum} from '../ops/sum';\nimport {Tensor} from '../tensor';\n\nexport const logSoftmaxGradConfig: GradConfig = {\n  kernelName: LogSoftmax,\n  inputsToSave: [],\n  outputsToSave: [true],\n  gradFunc: (dy: Tensor, saved: Tensor[], attrs: NamedAttrMap) => {\n    const [value] = saved;\n    const {axis} = attrs as unknown as LogSoftmaxAttrs;\n    return {\n      logits: () => {\n        const keepDims = true;\n        const softmax = exp(value);\n        return sub(dy, mul(sum(dy, axis, keepDims), softmax));\n      }\n    };\n  }\n};\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAiBA,SAAQA,UAAU,QAAwB,iBAAiB;AAE3D,SAAQC,GAAG,QAAO,YAAY;AAC9B,SAAQC,GAAG,QAAO,YAAY;AAC9B,SAAQC,GAAG,QAAO,YAAY;AAC9B,SAAQC,GAAG,QAAO,YAAY;AAG9B,OAAO,MAAMC,oBAAoB,GAAe;EAC9CC,UAAU,EAAEN,UAAU;EACtBO,YAAY,EAAE,EAAE;EAChBC,aAAa,EAAE,CAAC,IAAI,CAAC;EACrBC,QAAQ,EAAEA,CAACC,EAAU,EAAEC,KAAe,EAAEC,KAAmB,KAAI;IAC7D,MAAM,CAACC,KAAK,CAAC,GAAGF,KAAK;IACrB,MAAM;MAACG;IAAI,CAAC,GAAGF,KAAmC;IAClD,OAAO;MACLG,MAAM,EAAEA,CAAA,KAAK;QACX,MAAMC,QAAQ,GAAG,IAAI;QACrB,MAAMC,OAAO,GAAGhB,GAAG,CAACY,KAAK,CAAC;QAC1B,OAAOV,GAAG,CAACO,EAAE,EAAER,GAAG,CAACE,GAAG,CAACM,EAAE,EAAEI,IAAI,EAAEE,QAAQ,CAAC,EAAEC,OAAO,CAAC,CAAC;MACvD;KACD;EACH;CACD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}