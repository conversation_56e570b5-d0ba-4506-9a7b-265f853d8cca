{"ast": null, "code": "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the License);\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an AS IS BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { ComplexAbs, util } from '@tensorflow/tfjs-core';\nexport const complexAbs = args => {\n  const {\n    x\n  } = args.inputs;\n  const cpuBackend = args.backend;\n  const resultValues = new Float32Array(util.sizeFromShape(x.shape));\n  const complexVals = cpuBackend.data.get(x.dataId);\n  const real = complexVals.complexTensorInfos.real;\n  const imag = complexVals.complexTensorInfos.imag;\n  const realVals = cpuBackend.data.get(real.dataId).values;\n  const imagVals = cpuBackend.data.get(imag.dataId).values;\n  for (let i = 0; i < realVals.length; i++) {\n    const real = realVals[i];\n    const imag = imagVals[i];\n    resultValues[i] = Math.hypot(real, imag);\n  }\n  return cpuBackend.makeOutput(resultValues, x.shape, 'float32');\n};\nexport const complexAbsConfig = {\n  kernelName: ComplexAbs,\n  backendName: 'cpu',\n  kernelFunc: complexAbs\n};", "map": {"version": 3, "names": ["ComplexAbs", "util", "complexAbs", "args", "x", "inputs", "cpuBackend", "backend", "resultValues", "Float32Array", "sizeFromShape", "shape", "complexVals", "data", "get", "dataId", "real", "complexTensorInfos", "imag", "realVals", "values", "imagVals", "i", "length", "Math", "hypot", "makeOutput", "complexAbsConfig", "kernelName", "backendName", "kernelFunc"], "sources": ["C:\\tfjs-backend-cpu\\src\\kernels\\ComplexAbs.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the License);\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an AS IS BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {ComplexAbs, ComplexAbsInputs, KernelConfig, KernelFunc, util} from '@tensorflow/tfjs-core';\n\nimport {MathBackendCPU} from '../backend_cpu';\n\nexport const complexAbs =\n    (args: {inputs: ComplexAbsInputs, backend: MathBackendCPU}) => {\n      const {x} = args.inputs;\n      const cpuBackend = args.backend;\n      const resultValues = new Float32Array(util.sizeFromShape(x.shape));\n      const complexVals = cpuBackend.data.get(x.dataId);\n      const real = complexVals.complexTensorInfos.real;\n      const imag = complexVals.complexTensorInfos.imag;\n      const realVals = cpuBackend.data.get(real.dataId).values as Float32Array;\n      const imagVals = cpuBackend.data.get(imag.dataId).values as Float32Array;\n      for (let i = 0; i < realVals.length; i++) {\n        const real = realVals[i];\n        const imag = imagVals[i];\n        resultValues[i] = Math.hypot(real, imag);\n      }\n\n      return cpuBackend.makeOutput(resultValues, x.shape, 'float32');\n    };\n\nexport const complexAbsConfig: KernelConfig = {\n  kernelName: ComplexAbs,\n  backendName: 'cpu',\n  kernelFunc: complexAbs as unknown as KernelFunc,\n};\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAiBA,SAAQA,UAAU,EAA8CC,IAAI,QAAO,uBAAuB;AAIlG,OAAO,MAAMC,UAAU,GAClBC,IAAyD,IAAI;EAC5D,MAAM;IAACC;EAAC,CAAC,GAAGD,IAAI,CAACE,MAAM;EACvB,MAAMC,UAAU,GAAGH,IAAI,CAACI,OAAO;EAC/B,MAAMC,YAAY,GAAG,IAAIC,YAAY,CAACR,IAAI,CAACS,aAAa,CAACN,CAAC,CAACO,KAAK,CAAC,CAAC;EAClE,MAAMC,WAAW,GAAGN,UAAU,CAACO,IAAI,CAACC,GAAG,CAACV,CAAC,CAACW,MAAM,CAAC;EACjD,MAAMC,IAAI,GAAGJ,WAAW,CAACK,kBAAkB,CAACD,IAAI;EAChD,MAAME,IAAI,GAAGN,WAAW,CAACK,kBAAkB,CAACC,IAAI;EAChD,MAAMC,QAAQ,GAAGb,UAAU,CAACO,IAAI,CAACC,GAAG,CAACE,IAAI,CAACD,MAAM,CAAC,CAACK,MAAsB;EACxE,MAAMC,QAAQ,GAAGf,UAAU,CAACO,IAAI,CAACC,GAAG,CAACI,IAAI,CAACH,MAAM,CAAC,CAACK,MAAsB;EACxE,KAAK,IAAIE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,QAAQ,CAACI,MAAM,EAAED,CAAC,EAAE,EAAE;IACxC,MAAMN,IAAI,GAAGG,QAAQ,CAACG,CAAC,CAAC;IACxB,MAAMJ,IAAI,GAAGG,QAAQ,CAACC,CAAC,CAAC;IACxBd,YAAY,CAACc,CAAC,CAAC,GAAGE,IAAI,CAACC,KAAK,CAACT,IAAI,EAAEE,IAAI,CAAC;;EAG1C,OAAOZ,UAAU,CAACoB,UAAU,CAAClB,YAAY,EAAEJ,CAAC,CAACO,KAAK,EAAE,SAAS,CAAC;AAChE,CAAC;AAEL,OAAO,MAAMgB,gBAAgB,GAAiB;EAC5CC,UAAU,EAAE5B,UAAU;EACtB6B,WAAW,EAAE,KAAK;EAClBC,UAAU,EAAE5B;CACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}