{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.transformReply = exports.transformArguments = exports.FIRST_KEY_INDEX = exports.IS_READ_ONLY = void 0;\nexports.IS_READ_ONLY = true;\nexports.FIRST_KEY_INDEX = 1;\nfunction transformArguments(key) {\n  return ['GRAPH.SLOWLOG', key];\n}\nexports.transformArguments = transformArguments;\nfunction transformReply(logs) {\n  return logs.map(([timestamp, command, query, took]) => ({\n    timestamp: new Date(Number(timestamp) * 1000),\n    command,\n    query,\n    took: Number(took)\n  }));\n}\nexports.transformReply = transformReply;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "transformReply", "transformArguments", "FIRST_KEY_INDEX", "IS_READ_ONLY", "key", "logs", "map", "timestamp", "command", "query", "took", "Date", "Number"], "sources": ["C:/tmsft/node_modules/@redis/graph/dist/commands/SLOWLOG.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.transformReply = exports.transformArguments = exports.FIRST_KEY_INDEX = exports.IS_READ_ONLY = void 0;\nexports.IS_READ_ONLY = true;\nexports.FIRST_KEY_INDEX = 1;\nfunction transformArguments(key) {\n    return ['GRAPH.SLOWLOG', key];\n}\nexports.transformArguments = transformArguments;\nfunction transformReply(logs) {\n    return logs.map(([timestamp, command, query, took]) => ({\n        timestamp: new Date(Number(timestamp) * 1000),\n        command,\n        query,\n        took: Number(took)\n    }));\n}\nexports.transformReply = transformReply;\n"], "mappings": "AAAA,YAAY;;AACZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEC,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7DD,OAAO,CAACE,cAAc,GAAGF,OAAO,CAACG,kBAAkB,GAAGH,OAAO,CAACI,eAAe,GAAGJ,OAAO,CAACK,YAAY,GAAG,KAAK,CAAC;AAC7GL,OAAO,CAACK,YAAY,GAAG,IAAI;AAC3BL,OAAO,CAACI,eAAe,GAAG,CAAC;AAC3B,SAASD,kBAAkBA,CAACG,GAAG,EAAE;EAC7B,OAAO,CAAC,eAAe,EAAEA,GAAG,CAAC;AACjC;AACAN,OAAO,CAACG,kBAAkB,GAAGA,kBAAkB;AAC/C,SAASD,cAAcA,CAACK,IAAI,EAAE;EAC1B,OAAOA,IAAI,CAACC,GAAG,CAAC,CAAC,CAACC,SAAS,EAAEC,OAAO,EAAEC,KAAK,EAAEC,IAAI,CAAC,MAAM;IACpDH,SAAS,EAAE,IAAII,IAAI,CAACC,MAAM,CAACL,SAAS,CAAC,GAAG,IAAI,CAAC;IAC7CC,OAAO;IACPC,KAAK;IACLC,IAAI,EAAEE,MAAM,CAACF,IAAI;EACrB,CAAC,CAAC,CAAC;AACP;AACAZ,OAAO,CAACE,cAAc,GAAGA,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}