{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.transformArguments = void 0;\nfunction transformArguments(slot) {\n  return ['CLUSTER', 'COUNTKEYSINSLOT', slot.toString()];\n}\nexports.transformArguments = transformArguments;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "transformArguments", "slot", "toString"], "sources": ["C:/tmsft/node_modules/@redis/client/dist/lib/commands/CLUSTER_COUNTKEYSINSLOT.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.transformArguments = void 0;\nfunction transformArguments(slot) {\n    return ['CLUSTER', 'COUNTKEYSINSLOT', slot.toString()];\n}\nexports.transformArguments = transformArguments;\n"], "mappings": "AAAA,YAAY;;AACZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEC,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7DD,OAAO,CAACE,kBAAkB,GAAG,KAAK,CAAC;AACnC,SAASA,kBAAkBA,CAACC,IAAI,EAAE;EAC9B,OAAO,CAAC,SAAS,EAAE,iBAAiB,EAAEA,IAAI,CAACC,QAAQ,CAAC,CAAC,CAAC;AAC1D;AACAJ,OAAO,CAACE,kBAAkB,GAAGA,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}