{"ast": null, "code": "/**\n * @license\n * Copyright 2022 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { backend_util } from '@tensorflow/tfjs-core';\nimport { CumProgram } from '../cum_gpu';\nimport { identity } from './Identity';\nimport { transpose } from './Transpose';\nexport function cumImpl(op, x, backend, axis, exclusive, reverse) {\n  const xRank = x.shape.length;\n  const permutation = backend_util.getAxesPermutation([axis], xRank);\n  let permutedX = x;\n  if (permutation != null) {\n    permutedX = transpose({\n      inputs: {\n        x\n      },\n      backend,\n      attrs: {\n        perm: permutation\n      }\n    });\n  }\n  const permutedAxis = backend_util.getInnerMostAxes(1, xRank)[0];\n  if (permutedAxis !== xRank - 1) {\n    throw new Error(\"WebGL cumprod shader expects an inner-most axis=\".concat(x.shape.length - 1, \" \") + \"but got axis=\".concat(axis));\n  }\n  const size = permutedX.shape[permutedAxis];\n  let result = identity({\n    inputs: {\n      x: permutedX\n    },\n    backend\n  });\n  // Use cum parallel algorithm, inspired by:\n  // https://developer.nvidia.com/gpugems/gpugems3/part-vi-gpu-computing/chapter-39-parallel-prefix-sum-scan-cuda\n  // Note: although the algorithm is called sum, it works for any associtative\n  // operator with an identity.\n  for (let i = 0; i <= Math.ceil(Math.log2(size)) - 1; i++) {\n    const program = new CumProgram(op, permutedX.shape, false, reverse);\n    const customValues = [[i]];\n    const prevResult = result;\n    result = backend.runWebGLProgram(program, [result], result.dtype, customValues);\n    backend.disposeIntermediateTensorInfo(prevResult);\n  }\n  // For exclusive cum, shift the end result in the direction of product or sum\n  // and add 1 for product or 0 for sum to the front index.\n  if (exclusive) {\n    const program = new CumProgram(op, permutedX.shape, exclusive, reverse);\n    const prevResult = result;\n    result = backend.runWebGLProgram(program, [result], result.dtype);\n    backend.disposeIntermediateTensorInfo(prevResult);\n  }\n  if (permutation != null) {\n    const reversePermutation = backend_util.getUndoAxesPermutation(permutation);\n    const reverseTransposedResult = transpose({\n      inputs: {\n        x: result\n      },\n      backend,\n      attrs: {\n        perm: reversePermutation\n      }\n    });\n    backend.disposeIntermediateTensorInfo(result);\n    backend.disposeIntermediateTensorInfo(permutedX);\n    return reverseTransposedResult;\n  }\n  return result;\n}", "map": {"version": 3, "names": ["backend_util", "CumProgram", "identity", "transpose", "cumImpl", "op", "x", "backend", "axis", "exclusive", "reverse", "xRank", "shape", "length", "permutation", "getAxesPermutation", "permutedX", "inputs", "attrs", "perm", "permutedAxis", "getInnerMostAxes", "Error", "concat", "size", "result", "i", "Math", "ceil", "log2", "program", "customValues", "prevResult", "runWebGLProgram", "dtype", "disposeIntermediateTensorInfo", "reversePermutation", "getUndoAxesPermutation", "reverseTransposedResult"], "sources": ["C:\\tfjs-backend-webgl\\src\\kernels\\Cum_impl.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2022 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {backend_util, TensorInfo} from '@tensorflow/tfjs-core';\n\nimport {MathBackendWebGL} from '../backend_webgl';\nimport {CumOpType, CumProgram} from '../cum_gpu';\n\nimport {identity} from './Identity';\nimport {transpose} from './Transpose';\n\nexport function cumImpl(\n    op: CumOpType, x: TensorInfo, backend: MathBackendWebGL, axis: number,\n    exclusive: boolean, reverse: boolean): TensorInfo {\n  const xRank = x.shape.length;\n  const permutation = backend_util.getAxesPermutation([axis], xRank);\n  let permutedX = x;\n  if (permutation != null) {\n    permutedX = transpose({inputs: {x}, backend, attrs: {perm: permutation}});\n  }\n  const permutedAxis = backend_util.getInnerMostAxes(1, xRank)[0];\n\n  if (permutedAxis !== xRank - 1) {\n    throw new Error(\n        `WebGL cumprod shader expects an inner-most axis=${\n            x.shape.length - 1} ` +\n        `but got axis=${axis}`);\n  }\n  const size = permutedX.shape[permutedAxis];\n  let result = identity({inputs: {x: permutedX}, backend});\n  // Use cum parallel algorithm, inspired by:\n  // https://developer.nvidia.com/gpugems/gpugems3/part-vi-gpu-computing/chapter-39-parallel-prefix-sum-scan-cuda\n  // Note: although the algorithm is called sum, it works for any associtative\n  // operator with an identity.\n\n  for (let i = 0; i <= Math.ceil(Math.log2(size)) - 1; i++) {\n    const program = new CumProgram(op, permutedX.shape, false, reverse);\n    const customValues = [[i]];\n    const prevResult = result;\n    result =\n        backend.runWebGLProgram(program, [result], result.dtype, customValues);\n    backend.disposeIntermediateTensorInfo(prevResult);\n  }\n  // For exclusive cum, shift the end result in the direction of product or sum\n  // and add 1 for product or 0 for sum to the front index.\n  if (exclusive) {\n    const program = new CumProgram(op, permutedX.shape, exclusive, reverse);\n    const prevResult = result;\n    result = backend.runWebGLProgram(program, [result], result.dtype);\n    backend.disposeIntermediateTensorInfo(prevResult);\n  }\n\n  if (permutation != null) {\n    const reversePermutation = backend_util.getUndoAxesPermutation(permutation);\n    const reverseTransposedResult = transpose(\n        {inputs: {x: result}, backend, attrs: {perm: reversePermutation}});\n\n    backend.disposeIntermediateTensorInfo(result);\n    backend.disposeIntermediateTensorInfo(permutedX);\n\n    return reverseTransposedResult;\n  }\n\n  return result;\n}\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAiBA,SAAQA,YAAY,QAAmB,uBAAuB;AAG9D,SAAmBC,UAAU,QAAO,YAAY;AAEhD,SAAQC,QAAQ,QAAO,YAAY;AACnC,SAAQC,SAAS,QAAO,aAAa;AAErC,OAAM,SAAUC,OAAOA,CACnBC,EAAa,EAAEC,CAAa,EAAEC,OAAyB,EAAEC,IAAY,EACrEC,SAAkB,EAAEC,OAAgB;EACtC,MAAMC,KAAK,GAAGL,CAAC,CAACM,KAAK,CAACC,MAAM;EAC5B,MAAMC,WAAW,GAAGd,YAAY,CAACe,kBAAkB,CAAC,CAACP,IAAI,CAAC,EAAEG,KAAK,CAAC;EAClE,IAAIK,SAAS,GAAGV,CAAC;EACjB,IAAIQ,WAAW,IAAI,IAAI,EAAE;IACvBE,SAAS,GAAGb,SAAS,CAAC;MAACc,MAAM,EAAE;QAACX;MAAC,CAAC;MAAEC,OAAO;MAAEW,KAAK,EAAE;QAACC,IAAI,EAAEL;MAAW;IAAC,CAAC,CAAC;;EAE3E,MAAMM,YAAY,GAAGpB,YAAY,CAACqB,gBAAgB,CAAC,CAAC,EAAEV,KAAK,CAAC,CAAC,CAAC,CAAC;EAE/D,IAAIS,YAAY,KAAKT,KAAK,GAAG,CAAC,EAAE;IAC9B,MAAM,IAAIW,KAAK,CACX,mDAAAC,MAAA,CACIjB,CAAC,CAACM,KAAK,CAACC,MAAM,GAAG,CAAC,yBAAAU,MAAA,CACNf,IAAI,CAAE,CAAC;;EAE7B,MAAMgB,IAAI,GAAGR,SAAS,CAACJ,KAAK,CAACQ,YAAY,CAAC;EAC1C,IAAIK,MAAM,GAAGvB,QAAQ,CAAC;IAACe,MAAM,EAAE;MAACX,CAAC,EAAEU;IAAS,CAAC;IAAET;EAAO,CAAC,CAAC;EACxD;EACA;EACA;EACA;EAEA,KAAK,IAAImB,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAIC,IAAI,CAACC,IAAI,CAACD,IAAI,CAACE,IAAI,CAACL,IAAI,CAAC,CAAC,GAAG,CAAC,EAAEE,CAAC,EAAE,EAAE;IACxD,MAAMI,OAAO,GAAG,IAAI7B,UAAU,CAACI,EAAE,EAAEW,SAAS,CAACJ,KAAK,EAAE,KAAK,EAAEF,OAAO,CAAC;IACnE,MAAMqB,YAAY,GAAG,CAAC,CAACL,CAAC,CAAC,CAAC;IAC1B,MAAMM,UAAU,GAAGP,MAAM;IACzBA,MAAM,GACFlB,OAAO,CAAC0B,eAAe,CAACH,OAAO,EAAE,CAACL,MAAM,CAAC,EAAEA,MAAM,CAACS,KAAK,EAAEH,YAAY,CAAC;IAC1ExB,OAAO,CAAC4B,6BAA6B,CAACH,UAAU,CAAC;;EAEnD;EACA;EACA,IAAIvB,SAAS,EAAE;IACb,MAAMqB,OAAO,GAAG,IAAI7B,UAAU,CAACI,EAAE,EAAEW,SAAS,CAACJ,KAAK,EAAEH,SAAS,EAAEC,OAAO,CAAC;IACvE,MAAMsB,UAAU,GAAGP,MAAM;IACzBA,MAAM,GAAGlB,OAAO,CAAC0B,eAAe,CAACH,OAAO,EAAE,CAACL,MAAM,CAAC,EAAEA,MAAM,CAACS,KAAK,CAAC;IACjE3B,OAAO,CAAC4B,6BAA6B,CAACH,UAAU,CAAC;;EAGnD,IAAIlB,WAAW,IAAI,IAAI,EAAE;IACvB,MAAMsB,kBAAkB,GAAGpC,YAAY,CAACqC,sBAAsB,CAACvB,WAAW,CAAC;IAC3E,MAAMwB,uBAAuB,GAAGnC,SAAS,CACrC;MAACc,MAAM,EAAE;QAACX,CAAC,EAAEmB;MAAM,CAAC;MAAElB,OAAO;MAAEW,KAAK,EAAE;QAACC,IAAI,EAAEiB;MAAkB;IAAC,CAAC,CAAC;IAEtE7B,OAAO,CAAC4B,6BAA6B,CAACV,MAAM,CAAC;IAC7ClB,OAAO,CAAC4B,6BAA6B,CAACnB,SAAS,CAAC;IAEhD,OAAOsB,uBAAuB;;EAGhC,OAAOb,MAAM;AACf", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}