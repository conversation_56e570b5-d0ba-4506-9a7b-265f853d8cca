{"ast": null, "code": "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { backend_util, kernel_impls, NonMaxSuppressionV3 } from '@tensorflow/tfjs-core';\nconst nonMaxSuppressionV3Impl = kernel_impls.nonMaxSuppressionV3Impl;\nexport function nonMaxSuppressionV3(args) {\n  backend_util.warn('tf.nonMaxSuppression() in webgl locks the UI thread. ' + 'Call tf.nonMaxSuppressionAsync() instead');\n  const {\n    inputs,\n    backend,\n    attrs\n  } = args;\n  const {\n    boxes,\n    scores\n  } = inputs;\n  const {\n    maxOutputSize,\n    iouThreshold,\n    scoreThreshold\n  } = attrs;\n  const boxesVals = backend.readSync(boxes.dataId);\n  const scoresVals = backend.readSync(scores.dataId);\n  const {\n    selectedIndices\n  } = nonMaxSuppressionV3Impl(boxesVals, scoresVals, maxOutputSize, iouThreshold, scoreThreshold);\n  return backend.makeTensorInfo([selectedIndices.length], 'int32', new Int32Array(selectedIndices));\n}\nexport const nonMaxSuppressionV3Config = {\n  kernelName: NonMaxSuppressionV3,\n  backendName: 'webgl',\n  kernelFunc: nonMaxSuppressionV3\n};", "map": {"version": 3, "names": ["backend_util", "kernel_impls", "NonMaxSuppressionV3", "nonMaxSuppressionV3Impl", "nonMaxSuppressionV3", "args", "warn", "inputs", "backend", "attrs", "boxes", "scores", "maxOutputSize", "iouThreshold", "scoreThreshold", "boxesVals", "readSync", "dataId", "scoresVals", "selectedIndices", "makeTensorInfo", "length", "Int32Array", "nonMaxSuppressionV3Config", "kernelName", "backendName", "kernelFunc"], "sources": ["C:\\tfjs-backend-webgl\\src\\kernels\\NonMaxSuppressionV3.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {backend_util, kernel_impls, KernelConfig, KernelFunc, NonMaxSuppressionV3, NonMaxSuppressionV3Attrs, NonMaxSuppressionV3Inputs, TypedArray} from '@tensorflow/tfjs-core';\n\nconst nonMaxSuppressionV3Impl = kernel_impls.nonMaxSuppressionV3Impl;\nimport {MathBackendWebGL} from '../backend_webgl';\n\nexport function nonMaxSuppressionV3(args: {\n  inputs: NonMaxSuppressionV3Inputs,\n  backend: MathBackendWebGL,\n  attrs: NonMaxSuppressionV3Attrs\n}) {\n  backend_util.warn(\n      'tf.nonMaxSuppression() in webgl locks the UI thread. ' +\n      'Call tf.nonMaxSuppressionAsync() instead');\n\n  const {inputs, backend, attrs} = args;\n  const {boxes, scores} = inputs;\n  const {maxOutputSize, iouThreshold, scoreThreshold} = attrs;\n\n  const boxesVals = backend.readSync(boxes.dataId) as TypedArray;\n  const scoresVals = backend.readSync(scores.dataId) as TypedArray;\n\n  const {selectedIndices} = nonMaxSuppressionV3Impl(\n      boxesVals, scoresVals, maxOutputSize, iouThreshold, scoreThreshold);\n\n  return backend.makeTensorInfo(\n      [selectedIndices.length], 'int32', new Int32Array(selectedIndices));\n}\n\nexport const nonMaxSuppressionV3Config: KernelConfig = {\n  kernelName: NonMaxSuppressionV3,\n  backendName: 'webgl',\n  kernelFunc: nonMaxSuppressionV3 as unknown as KernelFunc\n};\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAiBA,SAAQA,YAAY,EAAEC,YAAY,EAA4BC,mBAAmB,QAAwE,uBAAuB;AAEhL,MAAMC,uBAAuB,GAAGF,YAAY,CAACE,uBAAuB;AAGpE,OAAM,SAAUC,mBAAmBA,CAACC,IAInC;EACCL,YAAY,CAACM,IAAI,CACb,uDAAuD,GACvD,0CAA0C,CAAC;EAE/C,MAAM;IAACC,MAAM;IAAEC,OAAO;IAAEC;EAAK,CAAC,GAAGJ,IAAI;EACrC,MAAM;IAACK,KAAK;IAAEC;EAAM,CAAC,GAAGJ,MAAM;EAC9B,MAAM;IAACK,aAAa;IAAEC,YAAY;IAAEC;EAAc,CAAC,GAAGL,KAAK;EAE3D,MAAMM,SAAS,GAAGP,OAAO,CAACQ,QAAQ,CAACN,KAAK,CAACO,MAAM,CAAe;EAC9D,MAAMC,UAAU,GAAGV,OAAO,CAACQ,QAAQ,CAACL,MAAM,CAACM,MAAM,CAAe;EAEhE,MAAM;IAACE;EAAe,CAAC,GAAGhB,uBAAuB,CAC7CY,SAAS,EAAEG,UAAU,EAAEN,aAAa,EAAEC,YAAY,EAAEC,cAAc,CAAC;EAEvE,OAAON,OAAO,CAACY,cAAc,CACzB,CAACD,eAAe,CAACE,MAAM,CAAC,EAAE,OAAO,EAAE,IAAIC,UAAU,CAACH,eAAe,CAAC,CAAC;AACzE;AAEA,OAAO,MAAMI,yBAAyB,GAAiB;EACrDC,UAAU,EAAEtB,mBAAmB;EAC/BuB,WAAW,EAAE,OAAO;EACpBC,UAAU,EAAEtB;CACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}