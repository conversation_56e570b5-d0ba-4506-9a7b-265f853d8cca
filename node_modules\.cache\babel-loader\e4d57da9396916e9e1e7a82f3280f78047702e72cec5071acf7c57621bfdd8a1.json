{"ast": null, "code": "var _jsxFileName = \"C:\\\\tmsft\\\\src\\\\components\\\\DataHub.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { BankStatementImport } from './BankStatementImport';\nimport { BankAccountManager } from './BankAccountManager';\nimport { Transactions } from './Transactions';\nimport './DataHub.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport const DataHub = ({\n  onTransactionImport\n}) => {\n  _s();\n  const [activeTab, setActiveTab] = useState('bankStatement');\n  const handleImportComplete = (transactions, bankAccount) => {\n    console.log(`Imported ${transactions.length} transactions for ${bankAccount.name}`);\n    if (onTransactionImport) {\n      onTransactionImport(transactions, bankAccount);\n    }\n    // Here you would typically save to your local database or state management\n  };\n  const tabs = [{\n    id: 'bankStatement',\n    label: 'Bank Statements',\n    icon: /*#__PURE__*/_jsxDEV(\"svg\", {\n      width: \"20\",\n      height: \"20\",\n      viewBox: \"0 0 24 24\",\n      fill: \"none\",\n      stroke: \"currentColor\",\n      strokeWidth: \"2\",\n      children: [/*#__PURE__*/_jsxDEV(\"rect\", {\n        x: \"2\",\n        y: \"3\",\n        width: \"20\",\n        height: \"14\",\n        rx: \"2\",\n        ry: \"2\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 29,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"line\", {\n        x1: \"8\",\n        y1: \"21\",\n        x2: \"16\",\n        y2: \"21\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 30,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"line\", {\n        x1: \"12\",\n        y1: \"17\",\n        x2: \"12\",\n        y2: \"21\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 31,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 28,\n      columnNumber: 9\n    }, this),\n    description: 'Import and process bank statement CSV files'\n  }, {\n    id: 'accounts',\n    label: 'Bank Accounts',\n    icon: /*#__PURE__*/_jsxDEV(\"svg\", {\n      width: \"20\",\n      height: \"20\",\n      viewBox: \"0 0 24 24\",\n      fill: \"none\",\n      stroke: \"currentColor\",\n      strokeWidth: \"2\",\n      children: [/*#__PURE__*/_jsxDEV(\"rect\", {\n        x: \"2\",\n        y: \"3\",\n        width: \"20\",\n        height: \"14\",\n        rx: \"2\",\n        ry: \"2\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 41,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"line\", {\n        x1: \"2\",\n        y1: \"12\",\n        x2: \"22\",\n        y2: \"12\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 42,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 40,\n      columnNumber: 9\n    }, this),\n    description: 'Manage and configure your bank accounts'\n  }, {\n    id: 'transactions',\n    label: 'Transactions',\n    icon: /*#__PURE__*/_jsxDEV(\"svg\", {\n      width: \"20\",\n      height: \"20\",\n      viewBox: \"0 0 24 24\",\n      fill: \"none\",\n      stroke: \"currentColor\",\n      strokeWidth: \"2\",\n      children: [/*#__PURE__*/_jsxDEV(\"path\", {\n        d: \"M3 6h18\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 52,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n        d: \"M3 12h18\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 53,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n        d: \"M3 18h18\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 54,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"circle\", {\n        cx: \"6\",\n        cy: \"6\",\n        r: \"1\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 55,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"circle\", {\n        cx: \"6\",\n        cy: \"12\",\n        r: \"1\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 56,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"circle\", {\n        cx: \"6\",\n        cy: \"18\",\n        r: \"1\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 57,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 51,\n      columnNumber: 9\n    }, this),\n    description: 'View and manage all imported transactions'\n  }, {\n    id: 'payroll',\n    label: 'Payroll Data',\n    icon: /*#__PURE__*/_jsxDEV(\"svg\", {\n      width: \"20\",\n      height: \"20\",\n      viewBox: \"0 0 24 24\",\n      fill: \"none\",\n      stroke: \"currentColor\",\n      strokeWidth: \"2\",\n      children: [/*#__PURE__*/_jsxDEV(\"path\", {\n        d: \"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 67,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"circle\", {\n        cx: \"9\",\n        cy: \"7\",\n        r: \"4\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 68,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n        d: \"M22 21v-2a4 4 0 0 0-3-3.87\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 69,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n        d: \"M16 3.13a4 4 0 0 1 0 7.75\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 70,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 66,\n      columnNumber: 9\n    }, this),\n    description: 'Process payroll and employee compensation data'\n  }, {\n    id: 'investments',\n    label: 'Investment Data',\n    icon: /*#__PURE__*/_jsxDEV(\"svg\", {\n      width: \"20\",\n      height: \"20\",\n      viewBox: \"0 0 24 24\",\n      fill: \"none\",\n      stroke: \"currentColor\",\n      strokeWidth: \"2\",\n      children: /*#__PURE__*/_jsxDEV(\"polyline\", {\n        points: \"22,12 18,12 15,21 9,3 6,12 2,12\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 80,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 79,\n      columnNumber: 9\n    }, this),\n    description: 'Import investment portfolio and market data'\n  }, {\n    id: 'reports',\n    label: 'Financial Reports',\n    icon: /*#__PURE__*/_jsxDEV(\"svg\", {\n      width: \"20\",\n      height: \"20\",\n      viewBox: \"0 0 24 24\",\n      fill: \"none\",\n      stroke: \"currentColor\",\n      strokeWidth: \"2\",\n      children: [/*#__PURE__*/_jsxDEV(\"path\", {\n        d: \"M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 90,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"polyline\", {\n        points: \"14,2 14,8 20,8\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 91,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"line\", {\n        x1: \"16\",\n        y1: \"13\",\n        x2: \"8\",\n        y2: \"13\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 92,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"line\", {\n        x1: \"16\",\n        y1: \"17\",\n        x2: \"8\",\n        y2: \"17\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 93,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"polyline\", {\n        points: \"10,9 9,9 8,9\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 94,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 89,\n      columnNumber: 9\n    }, this),\n    description: 'Generate and export financial reports'\n  }];\n  const renderTabContent = () => {\n    switch (activeTab) {\n      case 'bankStatement':\n        return /*#__PURE__*/_jsxDEV(BankStatementImport, {\n          onImportComplete: handleImportComplete\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 104,\n          columnNumber: 16\n        }, this);\n      case 'accounts':\n        return /*#__PURE__*/_jsxDEV(BankAccountManager, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 106,\n          columnNumber: 16\n        }, this);\n      case 'transactions':\n        return /*#__PURE__*/_jsxDEV(Transactions, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 108,\n          columnNumber: 16\n        }, this);\n      case 'payroll':\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"tab-placeholder\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"placeholder-icon\",\n            children: /*#__PURE__*/_jsxDEV(\"svg\", {\n              width: \"64\",\n              height: \"64\",\n              viewBox: \"0 0 24 24\",\n              fill: \"none\",\n              stroke: \"currentColor\",\n              strokeWidth: \"1\",\n              children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                d: \"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 114,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"circle\", {\n                cx: \"9\",\n                cy: \"7\",\n                r: \"4\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 115,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                d: \"M22 21v-2a4 4 0 0 0-3-3.87\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 116,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                d: \"M16 3.13a4 4 0 0 1 0 7.75\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 117,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 113,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 112,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"Payroll Data Import\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 120,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"This section will allow you to import and process payroll data, employee compensation records, and related HR financial information.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 121,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"placeholder-features\",\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              children: \"Coming Soon:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 123,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n              children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"Employee salary and wage data import\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 125,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"Benefits and deductions processing\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 126,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"Tax withholding calculations\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 127,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"Payroll period reconciliation\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 128,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 124,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 122,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 111,\n          columnNumber: 11\n        }, this);\n      case 'investments':\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"tab-placeholder\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"placeholder-icon\",\n            children: /*#__PURE__*/_jsxDEV(\"svg\", {\n              width: \"64\",\n              height: \"64\",\n              viewBox: \"0 0 24 24\",\n              fill: \"none\",\n              stroke: \"currentColor\",\n              strokeWidth: \"1\",\n              children: /*#__PURE__*/_jsxDEV(\"polyline\", {\n                points: \"22,12 18,12 15,21 9,3 6,12 2,12\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 138,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 137,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 136,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"Investment Data Import\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 141,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"Manage your investment portfolio data, market valuations, and performance tracking.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 142,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"placeholder-features\",\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              children: \"Coming Soon:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 144,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n              children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"Portfolio holdings import\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 146,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"Market price updates\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 147,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"Performance analytics\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 148,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"Asset allocation tracking\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 149,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 145,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 143,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 135,\n          columnNumber: 11\n        }, this);\n      case 'reports':\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"tab-placeholder\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"placeholder-icon\",\n            children: /*#__PURE__*/_jsxDEV(\"svg\", {\n              width: \"64\",\n              height: \"64\",\n              viewBox: \"0 0 24 24\",\n              fill: \"none\",\n              stroke: \"currentColor\",\n              strokeWidth: \"1\",\n              children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                d: \"M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 159,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"polyline\", {\n                points: \"14,2 14,8 20,8\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 160,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"line\", {\n                x1: \"16\",\n                y1: \"13\",\n                x2: \"8\",\n                y2: \"13\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 161,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"line\", {\n                x1: \"16\",\n                y1: \"17\",\n                x2: \"8\",\n                y2: \"17\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 162,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"polyline\", {\n                points: \"10,9 9,9 8,9\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 163,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 158,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 157,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"Financial Reports\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 166,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"Generate comprehensive financial reports and export data for analysis and compliance.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 167,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"placeholder-features\",\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              children: \"Coming Soon:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 169,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n              children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"Balance sheet generation\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 171,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"Income statement reports\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 172,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"Cash flow analysis\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 173,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"Custom report builder\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 174,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 170,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 168,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 156,\n          columnNumber: 11\n        }, this);\n      default:\n        return null;\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"datahub\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"datahub-header\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"datahub-title\",\n          children: \"DataHub\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 188,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"datahub-description\",\n          children: \"Central hub for importing, processing, and managing all your financial data\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 189,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 187,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 186,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"datahub-content\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"datahub-tabs\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"tab-list\",\n            children: tabs.map(tab => /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setActiveTab(tab.id),\n              className: `tab-button ${activeTab === tab.id ? 'active' : ''}`,\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"tab-icon\",\n                children: tab.icon\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 205,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"tab-content\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"tab-label\",\n                  children: tab.label\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 207,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"tab-description\",\n                  children: tab.description\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 208,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 206,\n                columnNumber: 19\n              }, this)]\n            }, tab.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 200,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 198,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 197,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"tab-panel\",\n          children: renderTabContent()\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 215,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 196,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 195,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 185,\n    columnNumber: 5\n  }, this);\n};\n_s(DataHub, \"3JDgah+6/cLZS8//WDfj/RDd3DA=\");\n_c = DataHub;\nvar _c;\n$RefreshReg$(_c, \"DataHub\");", "map": {"version": 3, "names": ["React", "useState", "BankStatementImport", "BankAccountManager", "Transactions", "jsxDEV", "_jsxDEV", "DataHub", "onTransactionImport", "_s", "activeTab", "setActiveTab", "handleImportComplete", "transactions", "bankAccount", "console", "log", "length", "name", "tabs", "id", "label", "icon", "width", "height", "viewBox", "fill", "stroke", "strokeWidth", "children", "x", "y", "rx", "ry", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "x1", "y1", "x2", "y2", "description", "d", "cx", "cy", "r", "points", "renderTabContent", "onImportComplete", "className", "map", "tab", "onClick", "_c", "$RefreshReg$"], "sources": ["C:/tmsft/src/components/DataHub.tsx"], "sourcesContent": ["import React, { useState } from 'react';\r\nimport { BankStatementImport } from './BankStatementImport';\r\nimport { BankAccountManager } from './BankAccountManager';\r\nimport { Transactions } from './Transactions';\r\nimport { Transaction, BankAccount } from '../types';\r\nimport './DataHub.css';\r\n\r\ninterface DataHubProps {\r\n  onTransactionImport?: (transactions: Transaction[], bankAccount: BankAccount) => void;\r\n}\r\n\r\nexport const DataHub: React.FC<DataHubProps> = ({ onTransactionImport }) => {\r\n  const [activeTab, setActiveTab] = useState<'bankStatement' | 'accounts' | 'transactions' | 'payroll' | 'investments' | 'reports'>('bankStatement');\r\n\r\n  const handleImportComplete = (transactions: Transaction[], bankAccount: BankAccount) => {\r\n    console.log(`Imported ${transactions.length} transactions for ${bankAccount.name}`);\r\n    if (onTransactionImport) {\r\n      onTransactionImport(transactions, bankAccount);\r\n    }\r\n    // Here you would typically save to your local database or state management\r\n  };\r\n\r\n  const tabs = [\r\n    {\r\n      id: 'bankStatement' as const,\r\n      label: 'Bank Statements',\r\n      icon: (\r\n        <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\">\r\n          <rect x=\"2\" y=\"3\" width=\"20\" height=\"14\" rx=\"2\" ry=\"2\" />\r\n          <line x1=\"8\" y1=\"21\" x2=\"16\" y2=\"21\" />\r\n          <line x1=\"12\" y1=\"17\" x2=\"12\" y2=\"21\" />\r\n        </svg>\r\n      ),\r\n      description: 'Import and process bank statement CSV files'\r\n    },\r\n    {\r\n      id: 'accounts' as const,\r\n      label: 'Bank Accounts',\r\n      icon: (\r\n        <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\">\r\n          <rect x=\"2\" y=\"3\" width=\"20\" height=\"14\" rx=\"2\" ry=\"2\" />\r\n          <line x1=\"2\" y1=\"12\" x2=\"22\" y2=\"12\" />\r\n        </svg>\r\n      ),\r\n      description: 'Manage and configure your bank accounts'\r\n    },\r\n    {\r\n      id: 'transactions' as const,\r\n      label: 'Transactions',\r\n      icon: (\r\n        <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\">\r\n          <path d=\"M3 6h18\" />\r\n          <path d=\"M3 12h18\" />\r\n          <path d=\"M3 18h18\" />\r\n          <circle cx=\"6\" cy=\"6\" r=\"1\" />\r\n          <circle cx=\"6\" cy=\"12\" r=\"1\" />\r\n          <circle cx=\"6\" cy=\"18\" r=\"1\" />\r\n        </svg>\r\n      ),\r\n      description: 'View and manage all imported transactions'\r\n    },\r\n    {\r\n      id: 'payroll' as const,\r\n      label: 'Payroll Data',\r\n      icon: (\r\n        <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\">\r\n          <path d=\"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2\" />\r\n          <circle cx=\"9\" cy=\"7\" r=\"4\" />\r\n          <path d=\"M22 21v-2a4 4 0 0 0-3-3.87\" />\r\n          <path d=\"M16 3.13a4 4 0 0 1 0 7.75\" />\r\n        </svg>\r\n      ),\r\n      description: 'Process payroll and employee compensation data'\r\n    },\r\n    {\r\n      id: 'investments' as const,\r\n      label: 'Investment Data',\r\n      icon: (\r\n        <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\">\r\n          <polyline points=\"22,12 18,12 15,21 9,3 6,12 2,12\" />\r\n        </svg>\r\n      ),\r\n      description: 'Import investment portfolio and market data'\r\n    },\r\n    {\r\n      id: 'reports' as const,\r\n      label: 'Financial Reports',\r\n      icon: (\r\n        <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\">\r\n          <path d=\"M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z\" />\r\n          <polyline points=\"14,2 14,8 20,8\" />\r\n          <line x1=\"16\" y1=\"13\" x2=\"8\" y2=\"13\" />\r\n          <line x1=\"16\" y1=\"17\" x2=\"8\" y2=\"17\" />\r\n          <polyline points=\"10,9 9,9 8,9\" />\r\n        </svg>\r\n      ),\r\n      description: 'Generate and export financial reports'\r\n    }\r\n  ];\r\n\r\n  const renderTabContent = () => {\r\n    switch (activeTab) {\r\n      case 'bankStatement':\r\n        return <BankStatementImport onImportComplete={handleImportComplete} />;\r\n      case 'accounts':\r\n        return <BankAccountManager />;\r\n      case 'transactions':\r\n        return <Transactions />;\r\n      case 'payroll':\r\n        return (\r\n          <div className=\"tab-placeholder\">\r\n            <div className=\"placeholder-icon\">\r\n              <svg width=\"64\" height=\"64\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"1\">\r\n                <path d=\"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2\" />\r\n                <circle cx=\"9\" cy=\"7\" r=\"4\" />\r\n                <path d=\"M22 21v-2a4 4 0 0 0-3-3.87\" />\r\n                <path d=\"M16 3.13a4 4 0 0 1 0 7.75\" />\r\n              </svg>\r\n            </div>\r\n            <h3>Payroll Data Import</h3>\r\n            <p>This section will allow you to import and process payroll data, employee compensation records, and related HR financial information.</p>\r\n            <div className=\"placeholder-features\">\r\n              <h4>Coming Soon:</h4>\r\n              <ul>\r\n                <li>Employee salary and wage data import</li>\r\n                <li>Benefits and deductions processing</li>\r\n                <li>Tax withholding calculations</li>\r\n                <li>Payroll period reconciliation</li>\r\n              </ul>\r\n            </div>\r\n          </div>\r\n        );\r\n      case 'investments':\r\n        return (\r\n          <div className=\"tab-placeholder\">\r\n            <div className=\"placeholder-icon\">\r\n              <svg width=\"64\" height=\"64\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"1\">\r\n                <polyline points=\"22,12 18,12 15,21 9,3 6,12 2,12\" />\r\n              </svg>\r\n            </div>\r\n            <h3>Investment Data Import</h3>\r\n            <p>Manage your investment portfolio data, market valuations, and performance tracking.</p>\r\n            <div className=\"placeholder-features\">\r\n              <h4>Coming Soon:</h4>\r\n              <ul>\r\n                <li>Portfolio holdings import</li>\r\n                <li>Market price updates</li>\r\n                <li>Performance analytics</li>\r\n                <li>Asset allocation tracking</li>\r\n              </ul>\r\n            </div>\r\n          </div>\r\n        );\r\n      case 'reports':\r\n        return (\r\n          <div className=\"tab-placeholder\">\r\n            <div className=\"placeholder-icon\">\r\n              <svg width=\"64\" height=\"64\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"1\">\r\n                <path d=\"M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z\" />\r\n                <polyline points=\"14,2 14,8 20,8\" />\r\n                <line x1=\"16\" y1=\"13\" x2=\"8\" y2=\"13\" />\r\n                <line x1=\"16\" y1=\"17\" x2=\"8\" y2=\"17\" />\r\n                <polyline points=\"10,9 9,9 8,9\" />\r\n              </svg>\r\n            </div>\r\n            <h3>Financial Reports</h3>\r\n            <p>Generate comprehensive financial reports and export data for analysis and compliance.</p>\r\n            <div className=\"placeholder-features\">\r\n              <h4>Coming Soon:</h4>\r\n              <ul>\r\n                <li>Balance sheet generation</li>\r\n                <li>Income statement reports</li>\r\n                <li>Cash flow analysis</li>\r\n                <li>Custom report builder</li>\r\n              </ul>\r\n            </div>\r\n          </div>\r\n        );\r\n      default:\r\n        return null;\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className=\"datahub\">\r\n      <div className=\"datahub-header\">\r\n        <div className=\"container\">\r\n          <h1 className=\"datahub-title\">DataHub</h1>\r\n          <p className=\"datahub-description\">\r\n            Central hub for importing, processing, and managing all your financial data\r\n          </p>\r\n        </div>\r\n      </div>\r\n\r\n      <div className=\"datahub-content\">\r\n        <div className=\"container\">\r\n          <div className=\"datahub-tabs\">\r\n            <div className=\"tab-list\">\r\n              {tabs.map((tab) => (\r\n                <button\r\n                  key={tab.id}\r\n                  onClick={() => setActiveTab(tab.id)}\r\n                  className={`tab-button ${activeTab === tab.id ? 'active' : ''}`}\r\n                >\r\n                  <div className=\"tab-icon\">{tab.icon}</div>\r\n                  <div className=\"tab-content\">\r\n                    <div className=\"tab-label\">{tab.label}</div>\r\n                    <div className=\"tab-description\">{tab.description}</div>\r\n                  </div>\r\n                </button>\r\n              ))}\r\n            </div>\r\n          </div>\r\n\r\n          <div className=\"tab-panel\">\r\n            {renderTabContent()}\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n}; "], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,mBAAmB,QAAQ,uBAAuB;AAC3D,SAASC,kBAAkB,QAAQ,sBAAsB;AACzD,SAASC,YAAY,QAAQ,gBAAgB;AAE7C,OAAO,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAMvB,OAAO,MAAMC,OAA+B,GAAGA,CAAC;EAAEC;AAAoB,CAAC,KAAK;EAAAC,EAAA;EAC1E,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGV,QAAQ,CAAwF,eAAe,CAAC;EAElJ,MAAMW,oBAAoB,GAAGA,CAACC,YAA2B,EAAEC,WAAwB,KAAK;IACtFC,OAAO,CAACC,GAAG,CAAC,YAAYH,YAAY,CAACI,MAAM,qBAAqBH,WAAW,CAACI,IAAI,EAAE,CAAC;IACnF,IAAIV,mBAAmB,EAAE;MACvBA,mBAAmB,CAACK,YAAY,EAAEC,WAAW,CAAC;IAChD;IACA;EACF,CAAC;EAED,MAAMK,IAAI,GAAG,CACX;IACEC,EAAE,EAAE,eAAwB;IAC5BC,KAAK,EAAE,iBAAiB;IACxBC,IAAI,eACFhB,OAAA;MAAKiB,KAAK,EAAC,IAAI;MAACC,MAAM,EAAC,IAAI;MAACC,OAAO,EAAC,WAAW;MAACC,IAAI,EAAC,MAAM;MAACC,MAAM,EAAC,cAAc;MAACC,WAAW,EAAC,GAAG;MAAAC,QAAA,gBAC/FvB,OAAA;QAAMwB,CAAC,EAAC,GAAG;QAACC,CAAC,EAAC,GAAG;QAACR,KAAK,EAAC,IAAI;QAACC,MAAM,EAAC,IAAI;QAACQ,EAAE,EAAC,GAAG;QAACC,EAAE,EAAC;MAAG;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACzD/B,OAAA;QAAMgC,EAAE,EAAC,GAAG;QAACC,EAAE,EAAC,IAAI;QAACC,EAAE,EAAC,IAAI;QAACC,EAAE,EAAC;MAAI;QAAAP,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACvC/B,OAAA;QAAMgC,EAAE,EAAC,IAAI;QAACC,EAAE,EAAC,IAAI;QAACC,EAAE,EAAC,IAAI;QAACC,EAAE,EAAC;MAAI;QAAAP,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACrC,CACN;IACDK,WAAW,EAAE;EACf,CAAC,EACD;IACEtB,EAAE,EAAE,UAAmB;IACvBC,KAAK,EAAE,eAAe;IACtBC,IAAI,eACFhB,OAAA;MAAKiB,KAAK,EAAC,IAAI;MAACC,MAAM,EAAC,IAAI;MAACC,OAAO,EAAC,WAAW;MAACC,IAAI,EAAC,MAAM;MAACC,MAAM,EAAC,cAAc;MAACC,WAAW,EAAC,GAAG;MAAAC,QAAA,gBAC/FvB,OAAA;QAAMwB,CAAC,EAAC,GAAG;QAACC,CAAC,EAAC,GAAG;QAACR,KAAK,EAAC,IAAI;QAACC,MAAM,EAAC,IAAI;QAACQ,EAAE,EAAC,GAAG;QAACC,EAAE,EAAC;MAAG;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACzD/B,OAAA;QAAMgC,EAAE,EAAC,GAAG;QAACC,EAAE,EAAC,IAAI;QAACC,EAAE,EAAC,IAAI;QAACC,EAAE,EAAC;MAAI;QAAAP,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACpC,CACN;IACDK,WAAW,EAAE;EACf,CAAC,EACD;IACEtB,EAAE,EAAE,cAAuB;IAC3BC,KAAK,EAAE,cAAc;IACrBC,IAAI,eACFhB,OAAA;MAAKiB,KAAK,EAAC,IAAI;MAACC,MAAM,EAAC,IAAI;MAACC,OAAO,EAAC,WAAW;MAACC,IAAI,EAAC,MAAM;MAACC,MAAM,EAAC,cAAc;MAACC,WAAW,EAAC,GAAG;MAAAC,QAAA,gBAC/FvB,OAAA;QAAMqC,CAAC,EAAC;MAAS;QAAAT,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACpB/B,OAAA;QAAMqC,CAAC,EAAC;MAAU;QAAAT,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACrB/B,OAAA;QAAMqC,CAAC,EAAC;MAAU;QAAAT,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACrB/B,OAAA;QAAQsC,EAAE,EAAC,GAAG;QAACC,EAAE,EAAC,GAAG;QAACC,CAAC,EAAC;MAAG;QAAAZ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC9B/B,OAAA;QAAQsC,EAAE,EAAC,GAAG;QAACC,EAAE,EAAC,IAAI;QAACC,CAAC,EAAC;MAAG;QAAAZ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC/B/B,OAAA;QAAQsC,EAAE,EAAC,GAAG;QAACC,EAAE,EAAC,IAAI;QAACC,CAAC,EAAC;MAAG;QAAAZ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC5B,CACN;IACDK,WAAW,EAAE;EACf,CAAC,EACD;IACEtB,EAAE,EAAE,SAAkB;IACtBC,KAAK,EAAE,cAAc;IACrBC,IAAI,eACFhB,OAAA;MAAKiB,KAAK,EAAC,IAAI;MAACC,MAAM,EAAC,IAAI;MAACC,OAAO,EAAC,WAAW;MAACC,IAAI,EAAC,MAAM;MAACC,MAAM,EAAC,cAAc;MAACC,WAAW,EAAC,GAAG;MAAAC,QAAA,gBAC/FvB,OAAA;QAAMqC,CAAC,EAAC;MAA2C;QAAAT,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACtD/B,OAAA;QAAQsC,EAAE,EAAC,GAAG;QAACC,EAAE,EAAC,GAAG;QAACC,CAAC,EAAC;MAAG;QAAAZ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC9B/B,OAAA;QAAMqC,CAAC,EAAC;MAA4B;QAAAT,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACvC/B,OAAA;QAAMqC,CAAC,EAAC;MAA2B;QAAAT,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACnC,CACN;IACDK,WAAW,EAAE;EACf,CAAC,EACD;IACEtB,EAAE,EAAE,aAAsB;IAC1BC,KAAK,EAAE,iBAAiB;IACxBC,IAAI,eACFhB,OAAA;MAAKiB,KAAK,EAAC,IAAI;MAACC,MAAM,EAAC,IAAI;MAACC,OAAO,EAAC,WAAW;MAACC,IAAI,EAAC,MAAM;MAACC,MAAM,EAAC,cAAc;MAACC,WAAW,EAAC,GAAG;MAAAC,QAAA,eAC/FvB,OAAA;QAAUyC,MAAM,EAAC;MAAiC;QAAAb,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAClD,CACN;IACDK,WAAW,EAAE;EACf,CAAC,EACD;IACEtB,EAAE,EAAE,SAAkB;IACtBC,KAAK,EAAE,mBAAmB;IAC1BC,IAAI,eACFhB,OAAA;MAAKiB,KAAK,EAAC,IAAI;MAACC,MAAM,EAAC,IAAI;MAACC,OAAO,EAAC,WAAW;MAACC,IAAI,EAAC,MAAM;MAACC,MAAM,EAAC,cAAc;MAACC,WAAW,EAAC,GAAG;MAAAC,QAAA,gBAC/FvB,OAAA;QAAMqC,CAAC,EAAC;MAA4D;QAAAT,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACvE/B,OAAA;QAAUyC,MAAM,EAAC;MAAgB;QAAAb,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACpC/B,OAAA;QAAMgC,EAAE,EAAC,IAAI;QAACC,EAAE,EAAC,IAAI;QAACC,EAAE,EAAC,GAAG;QAACC,EAAE,EAAC;MAAI;QAAAP,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACvC/B,OAAA;QAAMgC,EAAE,EAAC,IAAI;QAACC,EAAE,EAAC,IAAI;QAACC,EAAE,EAAC,GAAG;QAACC,EAAE,EAAC;MAAI;QAAAP,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACvC/B,OAAA;QAAUyC,MAAM,EAAC;MAAc;QAAAb,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC/B,CACN;IACDK,WAAW,EAAE;EACf,CAAC,CACF;EAED,MAAMM,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,QAAQtC,SAAS;MACf,KAAK,eAAe;QAClB,oBAAOJ,OAAA,CAACJ,mBAAmB;UAAC+C,gBAAgB,EAAErC;QAAqB;UAAAsB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACxE,KAAK,UAAU;QACb,oBAAO/B,OAAA,CAACH,kBAAkB;UAAA+B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAC/B,KAAK,cAAc;QACjB,oBAAO/B,OAAA,CAACF,YAAY;UAAA8B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACzB,KAAK,SAAS;QACZ,oBACE/B,OAAA;UAAK4C,SAAS,EAAC,iBAAiB;UAAArB,QAAA,gBAC9BvB,OAAA;YAAK4C,SAAS,EAAC,kBAAkB;YAAArB,QAAA,eAC/BvB,OAAA;cAAKiB,KAAK,EAAC,IAAI;cAACC,MAAM,EAAC,IAAI;cAACC,OAAO,EAAC,WAAW;cAACC,IAAI,EAAC,MAAM;cAACC,MAAM,EAAC,cAAc;cAACC,WAAW,EAAC,GAAG;cAAAC,QAAA,gBAC/FvB,OAAA;gBAAMqC,CAAC,EAAC;cAA2C;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACtD/B,OAAA;gBAAQsC,EAAE,EAAC,GAAG;gBAACC,EAAE,EAAC,GAAG;gBAACC,CAAC,EAAC;cAAG;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC9B/B,OAAA;gBAAMqC,CAAC,EAAC;cAA4B;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACvC/B,OAAA;gBAAMqC,CAAC,EAAC;cAA2B;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACN/B,OAAA;YAAAuB,QAAA,EAAI;UAAmB;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC5B/B,OAAA;YAAAuB,QAAA,EAAG;UAAoI;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAC3I/B,OAAA;YAAK4C,SAAS,EAAC,sBAAsB;YAAArB,QAAA,gBACnCvB,OAAA;cAAAuB,QAAA,EAAI;YAAY;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACrB/B,OAAA;cAAAuB,QAAA,gBACEvB,OAAA;gBAAAuB,QAAA,EAAI;cAAoC;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC7C/B,OAAA;gBAAAuB,QAAA,EAAI;cAAkC;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC3C/B,OAAA;gBAAAuB,QAAA,EAAI;cAA4B;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACrC/B,OAAA;gBAAAuB,QAAA,EAAI;cAA6B;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAEV,KAAK,aAAa;QAChB,oBACE/B,OAAA;UAAK4C,SAAS,EAAC,iBAAiB;UAAArB,QAAA,gBAC9BvB,OAAA;YAAK4C,SAAS,EAAC,kBAAkB;YAAArB,QAAA,eAC/BvB,OAAA;cAAKiB,KAAK,EAAC,IAAI;cAACC,MAAM,EAAC,IAAI;cAACC,OAAO,EAAC,WAAW;cAACC,IAAI,EAAC,MAAM;cAACC,MAAM,EAAC,cAAc;cAACC,WAAW,EAAC,GAAG;cAAAC,QAAA,eAC/FvB,OAAA;gBAAUyC,MAAM,EAAC;cAAiC;gBAAAb,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACN/B,OAAA;YAAAuB,QAAA,EAAI;UAAsB;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC/B/B,OAAA;YAAAuB,QAAA,EAAG;UAAmF;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAC1F/B,OAAA;YAAK4C,SAAS,EAAC,sBAAsB;YAAArB,QAAA,gBACnCvB,OAAA;cAAAuB,QAAA,EAAI;YAAY;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACrB/B,OAAA;cAAAuB,QAAA,gBACEvB,OAAA;gBAAAuB,QAAA,EAAI;cAAyB;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAClC/B,OAAA;gBAAAuB,QAAA,EAAI;cAAoB;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC7B/B,OAAA;gBAAAuB,QAAA,EAAI;cAAqB;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC9B/B,OAAA;gBAAAuB,QAAA,EAAI;cAAyB;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAEV,KAAK,SAAS;QACZ,oBACE/B,OAAA;UAAK4C,SAAS,EAAC,iBAAiB;UAAArB,QAAA,gBAC9BvB,OAAA;YAAK4C,SAAS,EAAC,kBAAkB;YAAArB,QAAA,eAC/BvB,OAAA;cAAKiB,KAAK,EAAC,IAAI;cAACC,MAAM,EAAC,IAAI;cAACC,OAAO,EAAC,WAAW;cAACC,IAAI,EAAC,MAAM;cAACC,MAAM,EAAC,cAAc;cAACC,WAAW,EAAC,GAAG;cAAAC,QAAA,gBAC/FvB,OAAA;gBAAMqC,CAAC,EAAC;cAA4D;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACvE/B,OAAA;gBAAUyC,MAAM,EAAC;cAAgB;gBAAAb,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACpC/B,OAAA;gBAAMgC,EAAE,EAAC,IAAI;gBAACC,EAAE,EAAC,IAAI;gBAACC,EAAE,EAAC,GAAG;gBAACC,EAAE,EAAC;cAAI;gBAAAP,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACvC/B,OAAA;gBAAMgC,EAAE,EAAC,IAAI;gBAACC,EAAE,EAAC,IAAI;gBAACC,EAAE,EAAC,GAAG;gBAACC,EAAE,EAAC;cAAI;gBAAAP,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACvC/B,OAAA;gBAAUyC,MAAM,EAAC;cAAc;gBAAAb,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/B;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACN/B,OAAA;YAAAuB,QAAA,EAAI;UAAiB;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC1B/B,OAAA;YAAAuB,QAAA,EAAG;UAAqF;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAC5F/B,OAAA;YAAK4C,SAAS,EAAC,sBAAsB;YAAArB,QAAA,gBACnCvB,OAAA;cAAAuB,QAAA,EAAI;YAAY;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACrB/B,OAAA;cAAAuB,QAAA,gBACEvB,OAAA;gBAAAuB,QAAA,EAAI;cAAwB;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACjC/B,OAAA;gBAAAuB,QAAA,EAAI;cAAwB;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACjC/B,OAAA;gBAAAuB,QAAA,EAAI;cAAkB;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC3B/B,OAAA;gBAAAuB,QAAA,EAAI;cAAqB;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAEV;QACE,OAAO,IAAI;IACf;EACF,CAAC;EAED,oBACE/B,OAAA;IAAK4C,SAAS,EAAC,SAAS;IAAArB,QAAA,gBACtBvB,OAAA;MAAK4C,SAAS,EAAC,gBAAgB;MAAArB,QAAA,eAC7BvB,OAAA;QAAK4C,SAAS,EAAC,WAAW;QAAArB,QAAA,gBACxBvB,OAAA;UAAI4C,SAAS,EAAC,eAAe;UAAArB,QAAA,EAAC;QAAO;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC1C/B,OAAA;UAAG4C,SAAS,EAAC,qBAAqB;UAAArB,QAAA,EAAC;QAEnC;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAEN/B,OAAA;MAAK4C,SAAS,EAAC,iBAAiB;MAAArB,QAAA,eAC9BvB,OAAA;QAAK4C,SAAS,EAAC,WAAW;QAAArB,QAAA,gBACxBvB,OAAA;UAAK4C,SAAS,EAAC,cAAc;UAAArB,QAAA,eAC3BvB,OAAA;YAAK4C,SAAS,EAAC,UAAU;YAAArB,QAAA,EACtBV,IAAI,CAACgC,GAAG,CAAEC,GAAG,iBACZ9C,OAAA;cAEE+C,OAAO,EAAEA,CAAA,KAAM1C,YAAY,CAACyC,GAAG,CAAChC,EAAE,CAAE;cACpC8B,SAAS,EAAE,cAAcxC,SAAS,KAAK0C,GAAG,CAAChC,EAAE,GAAG,QAAQ,GAAG,EAAE,EAAG;cAAAS,QAAA,gBAEhEvB,OAAA;gBAAK4C,SAAS,EAAC,UAAU;gBAAArB,QAAA,EAAEuB,GAAG,CAAC9B;cAAI;gBAAAY,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC1C/B,OAAA;gBAAK4C,SAAS,EAAC,aAAa;gBAAArB,QAAA,gBAC1BvB,OAAA;kBAAK4C,SAAS,EAAC,WAAW;kBAAArB,QAAA,EAAEuB,GAAG,CAAC/B;gBAAK;kBAAAa,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC5C/B,OAAA;kBAAK4C,SAAS,EAAC,iBAAiB;kBAAArB,QAAA,EAAEuB,GAAG,CAACV;gBAAW;kBAAAR,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrD,CAAC;YAAA,GARDe,GAAG,CAAChC,EAAE;cAAAc,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OASL,CACT;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEN/B,OAAA;UAAK4C,SAAS,EAAC,WAAW;UAAArB,QAAA,EACvBmB,gBAAgB,CAAC;QAAC;UAAAd,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC5B,EAAA,CAlNWF,OAA+B;AAAA+C,EAAA,GAA/B/C,OAA+B;AAAA,IAAA+C,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}