{"ast": null, "code": "/**\n * @license\n * Copyright 2023 Google LLC.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { Draw } from '@tensorflow/tfjs-core';\nexport function draw(args) {\n  const {\n    inputs,\n    backend,\n    attrs\n  } = args;\n  const {\n    image\n  } = inputs;\n  const {\n    canvas,\n    options\n  } = attrs;\n  const {\n    contextOptions,\n    imageOptions\n  } = options || {};\n  const alpha = (imageOptions === null || imageOptions === void 0 ? void 0 : imageOptions.alpha) || 1;\n  const contextType = (contextOptions === null || contextOptions === void 0 ? void 0 : contextOptions.contextType) || '2d';\n  if (contextType !== '2d') {\n    throw new Error(\"Context type \".concat(contextOptions.contextType, \" is not supported by the CPU backend.\"));\n  }\n  const ctx = canvas.getContext(contextType, (contextOptions === null || contextOptions === void 0 ? void 0 : contextOptions.contextAttributes) || {});\n  if (ctx == null) {\n    throw new Error(\"Could not get the context with \".concat(contextType, \" type.\"));\n  }\n  const [height, width] = image.shape.slice(0, 2);\n  const depth = image.shape.length === 2 ? 1 : image.shape[2];\n  const data = backend.data.get(image.dataId).values;\n  const multiplier = image.dtype === 'float32' ? 255 : 1;\n  const bytes = new Uint8ClampedArray(width * height * 4);\n  for (let i = 0; i < height * width; ++i) {\n    const rgba = [0, 0, 0, 255 * alpha];\n    for (let d = 0; d < depth; d++) {\n      const value = data[i * depth + d];\n      if (image.dtype === 'float32') {\n        if (value < 0 || value > 1) {\n          throw new Error(\"Tensor values for a float32 Tensor must be in the \" + \"range [0 - 1] but encountered \".concat(value, \".\"));\n        }\n      } else if (image.dtype === 'int32') {\n        if (value < 0 || value > 255) {\n          throw new Error(\"Tensor values for a int32 Tensor must be in the \" + \"range [0 - 255] but encountered \".concat(value, \".\"));\n        }\n      }\n      if (depth === 1) {\n        rgba[0] = value * multiplier;\n        rgba[1] = value * multiplier;\n        rgba[2] = value * multiplier;\n      } else {\n        rgba[d] = value * multiplier;\n      }\n    }\n    const j = i * 4;\n    bytes[j + 0] = Math.round(rgba[0]);\n    bytes[j + 1] = Math.round(rgba[1]);\n    bytes[j + 2] = Math.round(rgba[2]);\n    bytes[j + 3] = Math.round(rgba[3]);\n  }\n  canvas.width = width;\n  canvas.height = height;\n  const imageData = new ImageData(bytes, width, height);\n  ctx.putImageData(imageData, 0, 0);\n  return image;\n}\nexport const drawConfig = {\n  kernelName: Draw,\n  backendName: 'cpu',\n  kernelFunc: draw\n};", "map": {"version": 3, "names": ["Draw", "draw", "args", "inputs", "backend", "attrs", "image", "canvas", "options", "contextOptions", "imageOptions", "alpha", "contextType", "Error", "concat", "ctx", "getContext", "contextAttributes", "height", "width", "shape", "slice", "depth", "length", "data", "get", "dataId", "values", "multiplier", "dtype", "bytes", "Uint8ClampedArray", "i", "rgba", "d", "value", "j", "Math", "round", "imageData", "ImageData", "putImageData", "drawConfig", "kernelName", "backendName", "kernelFunc"], "sources": ["C:\\tfjs-backend-cpu\\src\\kernels\\Draw.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2023 Google LLC.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {Draw, DrawAttrs, DrawInputs, KernelConfig, KernelFunc, TypedArray} from '@tensorflow/tfjs-core';\nimport {TensorInfo} from '@tensorflow/tfjs-core';\n\nimport {MathBackendCPU} from '../backend_cpu';\n\nexport function draw(\n    args: {inputs: DrawInputs, backend: MathBackendCPU, attrs: DrawAttrs}):\n    TensorInfo {\n  const {inputs, backend, attrs} = args;\n  const {image} = inputs;\n  const {canvas, options} = attrs;\n  const {contextOptions, imageOptions} = options || {};\n  const alpha = imageOptions ?.alpha || 1;\n\n  const contextType = contextOptions ?.contextType || '2d';\n  if (contextType !== '2d') {\n    throw new Error(`Context type ${\n        contextOptions.contextType} is not supported by the CPU backend.`);\n  }\n  const ctx = canvas.getContext(contextType,\n    contextOptions?.contextAttributes || {}) as CanvasRenderingContext2D ;\n  if (ctx == null) {\n    throw new Error(`Could not get the context with ${contextType} type.`);\n  }\n\n  const [height, width] = image.shape.slice(0, 2);\n  const depth = image.shape.length === 2 ? 1 : image.shape[2];\n  const data = backend.data.get(image.dataId).values as TypedArray;\n  const multiplier = image.dtype === 'float32' ? 255 : 1;\n  const bytes = new Uint8ClampedArray(width * height * 4);\n\n  for (let i = 0; i < height * width; ++i) {\n    const rgba = [0, 0, 0, 255 * alpha];\n\n    for (let d = 0; d < depth; d++) {\n      const value = data[i * depth + d];\n\n      if (image.dtype === 'float32') {\n        if (value < 0 || value > 1) {\n          throw new Error(\n              `Tensor values for a float32 Tensor must be in the ` +\n              `range [0 - 1] but encountered ${value}.`);\n        }\n      } else if (image.dtype === 'int32') {\n        if (value < 0 || value > 255) {\n          throw new Error(\n              `Tensor values for a int32 Tensor must be in the ` +\n              `range [0 - 255] but encountered ${value}.`);\n        }\n      }\n\n      if (depth === 1) {\n        rgba[0] = value * multiplier;\n        rgba[1] = value * multiplier;\n        rgba[2] = value * multiplier;\n      } else {\n        rgba[d] = value * multiplier;\n      }\n    }\n\n    const j = i * 4;\n    bytes[j + 0] = Math.round(rgba[0]);\n    bytes[j + 1] = Math.round(rgba[1]);\n    bytes[j + 2] = Math.round(rgba[2]);\n    bytes[j + 3] = Math.round(rgba[3]);\n  }\n\n  canvas.width = width;\n  canvas.height = height;\n  const imageData = new ImageData(bytes, width, height);\n  ctx.putImageData(imageData, 0, 0);\n  return image;\n}\n\nexport const drawConfig: KernelConfig = {\n  kernelName: Draw,\n  backendName: 'cpu',\n  kernelFunc: draw as unknown as KernelFunc\n};\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAiBA,SAAQA,IAAI,QAAoE,uBAAuB;AAKvG,OAAM,SAAUC,IAAIA,CAChBC,IAAqE;EAEvE,MAAM;IAACC,MAAM;IAAEC,OAAO;IAAEC;EAAK,CAAC,GAAGH,IAAI;EACrC,MAAM;IAACI;EAAK,CAAC,GAAGH,MAAM;EACtB,MAAM;IAACI,MAAM;IAAEC;EAAO,CAAC,GAAGH,KAAK;EAC/B,MAAM;IAACI,cAAc;IAAEC;EAAY,CAAC,GAAGF,OAAO,IAAI,EAAE;EACpD,MAAMG,KAAK,GAAG,CAAAD,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAGC,KAAK,KAAI,CAAC;EAEvC,MAAMC,WAAW,GAAG,CAAAH,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAGG,WAAW,KAAI,IAAI;EACxD,IAAIA,WAAW,KAAK,IAAI,EAAE;IACxB,MAAM,IAAIC,KAAK,iBAAAC,MAAA,CACXL,cAAc,CAACG,WAAW,0CAAuC,CAAC;;EAExE,MAAMG,GAAG,GAAGR,MAAM,CAACS,UAAU,CAACJ,WAAW,EACvC,CAAAH,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAEQ,iBAAiB,KAAI,EAAE,CAA6B;EACtE,IAAIF,GAAG,IAAI,IAAI,EAAE;IACf,MAAM,IAAIF,KAAK,mCAAAC,MAAA,CAAmCF,WAAW,WAAQ,CAAC;;EAGxE,MAAM,CAACM,MAAM,EAAEC,KAAK,CAAC,GAAGb,KAAK,CAACc,KAAK,CAACC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;EAC/C,MAAMC,KAAK,GAAGhB,KAAK,CAACc,KAAK,CAACG,MAAM,KAAK,CAAC,GAAG,CAAC,GAAGjB,KAAK,CAACc,KAAK,CAAC,CAAC,CAAC;EAC3D,MAAMI,IAAI,GAAGpB,OAAO,CAACoB,IAAI,CAACC,GAAG,CAACnB,KAAK,CAACoB,MAAM,CAAC,CAACC,MAAoB;EAChE,MAAMC,UAAU,GAAGtB,KAAK,CAACuB,KAAK,KAAK,SAAS,GAAG,GAAG,GAAG,CAAC;EACtD,MAAMC,KAAK,GAAG,IAAIC,iBAAiB,CAACZ,KAAK,GAAGD,MAAM,GAAG,CAAC,CAAC;EAEvD,KAAK,IAAIc,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGd,MAAM,GAAGC,KAAK,EAAE,EAAEa,CAAC,EAAE;IACvC,MAAMC,IAAI,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,GAAGtB,KAAK,CAAC;IAEnC,KAAK,IAAIuB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGZ,KAAK,EAAEY,CAAC,EAAE,EAAE;MAC9B,MAAMC,KAAK,GAAGX,IAAI,CAACQ,CAAC,GAAGV,KAAK,GAAGY,CAAC,CAAC;MAEjC,IAAI5B,KAAK,CAACuB,KAAK,KAAK,SAAS,EAAE;QAC7B,IAAIM,KAAK,GAAG,CAAC,IAAIA,KAAK,GAAG,CAAC,EAAE;UAC1B,MAAM,IAAItB,KAAK,CACX,wFAAAC,MAAA,CACiCqB,KAAK,MAAG,CAAC;;OAEjD,MAAM,IAAI7B,KAAK,CAACuB,KAAK,KAAK,OAAO,EAAE;QAClC,IAAIM,KAAK,GAAG,CAAC,IAAIA,KAAK,GAAG,GAAG,EAAE;UAC5B,MAAM,IAAItB,KAAK,CACX,wFAAAC,MAAA,CACmCqB,KAAK,MAAG,CAAC;;;MAIpD,IAAIb,KAAK,KAAK,CAAC,EAAE;QACfW,IAAI,CAAC,CAAC,CAAC,GAAGE,KAAK,GAAGP,UAAU;QAC5BK,IAAI,CAAC,CAAC,CAAC,GAAGE,KAAK,GAAGP,UAAU;QAC5BK,IAAI,CAAC,CAAC,CAAC,GAAGE,KAAK,GAAGP,UAAU;OAC7B,MAAM;QACLK,IAAI,CAACC,CAAC,CAAC,GAAGC,KAAK,GAAGP,UAAU;;;IAIhC,MAAMQ,CAAC,GAAGJ,CAAC,GAAG,CAAC;IACfF,KAAK,CAACM,CAAC,GAAG,CAAC,CAAC,GAAGC,IAAI,CAACC,KAAK,CAACL,IAAI,CAAC,CAAC,CAAC,CAAC;IAClCH,KAAK,CAACM,CAAC,GAAG,CAAC,CAAC,GAAGC,IAAI,CAACC,KAAK,CAACL,IAAI,CAAC,CAAC,CAAC,CAAC;IAClCH,KAAK,CAACM,CAAC,GAAG,CAAC,CAAC,GAAGC,IAAI,CAACC,KAAK,CAACL,IAAI,CAAC,CAAC,CAAC,CAAC;IAClCH,KAAK,CAACM,CAAC,GAAG,CAAC,CAAC,GAAGC,IAAI,CAACC,KAAK,CAACL,IAAI,CAAC,CAAC,CAAC,CAAC;;EAGpC1B,MAAM,CAACY,KAAK,GAAGA,KAAK;EACpBZ,MAAM,CAACW,MAAM,GAAGA,MAAM;EACtB,MAAMqB,SAAS,GAAG,IAAIC,SAAS,CAACV,KAAK,EAAEX,KAAK,EAAED,MAAM,CAAC;EACrDH,GAAG,CAAC0B,YAAY,CAACF,SAAS,EAAE,CAAC,EAAE,CAAC,CAAC;EACjC,OAAOjC,KAAK;AACd;AAEA,OAAO,MAAMoC,UAAU,GAAiB;EACtCC,UAAU,EAAE3C,IAAI;EAChB4C,WAAW,EAAE,KAAK;EAClBC,UAAU,EAAE5C;CACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}