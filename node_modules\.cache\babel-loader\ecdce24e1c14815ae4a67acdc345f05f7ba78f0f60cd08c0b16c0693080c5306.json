{"ast": null, "code": "/**\n * @license\n * Copyright 2018 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nexport class FFTProgram {\n  constructor(component, inputShape, inverse) {\n    this.variableNames = ['real', 'imag'];\n    const innerDim = inputShape[1];\n    this.outputShape = inputShape;\n    const exponentMultiplierSnippet = inverse ? \"2.0 * \".concat(Math.PI) : \"-2.0 * \".concat(Math.PI);\n    const resultDenominator = inverse ? \"\".concat(innerDim, \".0\") : '1.0';\n    let opString;\n    if (component === 'real') {\n      opString = 'return real * expR - imag * expI;';\n    } else if (component === 'imag') {\n      opString = 'return real * expI + imag * expR;';\n    } else {\n      throw new Error(\"FFT component must be either \\\"real\\\" or \\\"imag\\\", got \".concat(component, \".\"));\n    }\n    this.userCode = \"\\n      const float exponentMultiplier = \".concat(exponentMultiplierSnippet, \";\\n\\n      float unaryOpComplex(float real, float expR, float imag, float expI) {\\n        \").concat(opString, \"\\n      }\\n\\n      float mulMatDFT(int batch, int index) {\\n        float indexRatio = float(index) / float(\").concat(innerDim, \");\\n        float exponentMultiplierTimesIndexRatio =\\n            exponentMultiplier * indexRatio;\\n\\n        float result = 0.0;\\n\\n        for (int i = 0; i < \").concat(innerDim, \"; i++) {\\n          // x = (-2|2 * PI / N) * index * i;\\n          float x = exponentMultiplierTimesIndexRatio * float(i);\\n          float expR = cos(x);\\n          float expI = sin(x);\\n          float real = getReal(batch, i);\\n          float imag = getImag(batch, i);\\n\\n          result +=\\n              unaryOpComplex(real, expR, imag, expI) / \").concat(resultDenominator, \";\\n        }\\n\\n        return result;\\n      }\\n\\n      void main() {\\n        ivec2 coords = getOutputCoords();\\n        setOutput(mulMatDFT(coords[0], coords[1]));\\n      }\\n    \");\n  }\n}", "map": {"version": 3, "names": ["FFTProgram", "constructor", "component", "inputShape", "inverse", "variableNames", "innerDim", "outputShape", "exponentMultiplierSnippet", "concat", "Math", "PI", "resultDenominator", "opString", "Error", "userCode"], "sources": ["C:\\tfjs-backend-webgl\\src\\fft_gpu.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2018 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {GPGPUProgram} from './gpgpu_math';\n\nexport class FFTProgram implements GPGPUProgram {\n  variableNames = ['real', 'imag'];\n  outputShape: number[];\n  userCode: string;\n\n  constructor(\n      component: 'real'|'imag', inputShape: [number, number],\n      inverse: boolean) {\n    const innerDim = inputShape[1];\n    this.outputShape = inputShape;\n\n    const exponentMultiplierSnippet =\n        inverse ? `2.0 * ${Math.PI}` : `-2.0 * ${Math.PI}`;\n    const resultDenominator = inverse ? `${innerDim}.0` : '1.0';\n\n    let opString: string;\n    if (component === 'real') {\n      opString = 'return real * expR - imag * expI;';\n    } else if (component === 'imag') {\n      opString = 'return real * expI + imag * expR;';\n    } else {\n      throw new Error(\n          `FFT component must be either \"real\" or \"imag\", got ${component}.`);\n    }\n\n    this.userCode = `\n      const float exponentMultiplier = ${exponentMultiplierSnippet};\n\n      float unaryOpComplex(float real, float expR, float imag, float expI) {\n        ${opString}\n      }\n\n      float mulMatDFT(int batch, int index) {\n        float indexRatio = float(index) / float(${innerDim});\n        float exponentMultiplierTimesIndexRatio =\n            exponentMultiplier * indexRatio;\n\n        float result = 0.0;\n\n        for (int i = 0; i < ${innerDim}; i++) {\n          // x = (-2|2 * PI / N) * index * i;\n          float x = exponentMultiplierTimesIndexRatio * float(i);\n          float expR = cos(x);\n          float expI = sin(x);\n          float real = getReal(batch, i);\n          float imag = getImag(batch, i);\n\n          result +=\n              unaryOpComplex(real, expR, imag, expI) / ${resultDenominator};\n        }\n\n        return result;\n      }\n\n      void main() {\n        ivec2 coords = getOutputCoords();\n        setOutput(mulMatDFT(coords[0], coords[1]));\n      }\n    `;\n  }\n}\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAmBA,OAAM,MAAOA,UAAU;EAKrBC,YACIC,SAAwB,EAAEC,UAA4B,EACtDC,OAAgB;IANpB,KAAAC,aAAa,GAAG,CAAC,MAAM,EAAE,MAAM,CAAC;IAO9B,MAAMC,QAAQ,GAAGH,UAAU,CAAC,CAAC,CAAC;IAC9B,IAAI,CAACI,WAAW,GAAGJ,UAAU;IAE7B,MAAMK,yBAAyB,GAC3BJ,OAAO,YAAAK,MAAA,CAAYC,IAAI,CAACC,EAAE,cAAAF,MAAA,CAAeC,IAAI,CAACC,EAAE,CAAE;IACtD,MAAMC,iBAAiB,GAAGR,OAAO,MAAAK,MAAA,CAAMH,QAAQ,UAAO,KAAK;IAE3D,IAAIO,QAAgB;IACpB,IAAIX,SAAS,KAAK,MAAM,EAAE;MACxBW,QAAQ,GAAG,mCAAmC;KAC/C,MAAM,IAAIX,SAAS,KAAK,MAAM,EAAE;MAC/BW,QAAQ,GAAG,mCAAmC;KAC/C,MAAM;MACL,MAAM,IAAIC,KAAK,2DAAAL,MAAA,CAC2CP,SAAS,MAAG,CAAC;;IAGzE,IAAI,CAACa,QAAQ,+CAAAN,MAAA,CACwBD,yBAAyB,iGAAAC,MAAA,CAGxDI,QAAQ,kHAAAJ,MAAA,CAIgCH,QAAQ,wKAAAG,MAAA,CAM5BH,QAAQ,sWAAAG,MAAA,CASmBG,iBAAiB,0LAUrE;EACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}