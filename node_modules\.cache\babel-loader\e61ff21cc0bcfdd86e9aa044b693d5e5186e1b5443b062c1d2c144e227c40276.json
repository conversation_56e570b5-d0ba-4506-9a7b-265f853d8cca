{"ast": null, "code": "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { env, Reverse, util } from '@tensorflow/tfjs-core';\nimport { ReverseProgram } from '../reverse_gpu';\nimport { ReversePackedProgram } from '../reverse_packed_gpu';\nimport { identity } from './Identity';\nexport function reverse(args) {\n  const {\n    inputs,\n    backend,\n    attrs\n  } = args;\n  const {\n    x\n  } = inputs;\n  const {\n    dims\n  } = attrs;\n  const xRank = x.shape.length;\n  const $dims = util.parseAxisParam(dims, x.shape);\n  if (xRank === 0) {\n    return identity({\n      inputs: {\n        x\n      },\n      backend\n    });\n  }\n  const program = env().getBool('WEBGL_PACK_ARRAY_OPERATIONS') ? new ReversePackedProgram(x.shape, $dims) : new ReverseProgram(x.shape, $dims);\n  return backend.runWebGLProgram(program, [x], x.dtype);\n}\nexport const reverseConfig = {\n  kernelName: Reverse,\n  backendName: 'webgl',\n  kernelFunc: reverse\n};", "map": {"version": 3, "names": ["env", "Reverse", "util", "ReverseProgram", "ReversePackedProgram", "identity", "reverse", "args", "inputs", "backend", "attrs", "x", "dims", "xRank", "shape", "length", "$dims", "parseAxisParam", "program", "getBool", "runWebGLProgram", "dtype", "reverseConfig", "kernelName", "backendName", "kernelFunc"], "sources": ["C:\\tfjs-backend-webgl\\src\\kernels\\Reverse.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {env, KernelConfig, KernelFunc, Reverse, ReverseAttrs, ReverseInputs, TensorInfo, util} from '@tensorflow/tfjs-core';\n\nimport {MathBackendWebGL} from '../backend_webgl';\nimport {ReverseProgram} from '../reverse_gpu';\nimport {ReversePackedProgram} from '../reverse_packed_gpu';\n\nimport {identity} from './Identity';\n\nexport function reverse(args: {\n  inputs: ReverseInputs,\n  backend: MathBackendWebGL,\n  attrs: ReverseAttrs\n}): TensorInfo {\n  const {inputs, backend, attrs} = args;\n  const {x} = inputs;\n  const {dims} = attrs;\n\n  const xRank = x.shape.length;\n\n  const $dims = util.parseAxisParam(dims, x.shape);\n  if (xRank === 0) {\n    return identity({inputs: {x}, backend});\n  }\n\n  const program = env().getBool('WEBGL_PACK_ARRAY_OPERATIONS') ?\n      new ReversePackedProgram(x.shape, $dims) :\n      new ReverseProgram(x.shape, $dims);\n\n  return backend.runWebGLProgram(program, [x], x.dtype);\n}\n\nexport const reverseConfig: KernelConfig = {\n  kernelName: Reverse,\n  backendName: 'webgl',\n  kernelFunc: reverse as unknown as KernelFunc\n};\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAiBA,SAAQA,GAAG,EAA4BC,OAAO,EAA2CC,IAAI,QAAO,uBAAuB;AAG3H,SAAQC,cAAc,QAAO,gBAAgB;AAC7C,SAAQC,oBAAoB,QAAO,uBAAuB;AAE1D,SAAQC,QAAQ,QAAO,YAAY;AAEnC,OAAM,SAAUC,OAAOA,CAACC,IAIvB;EACC,MAAM;IAACC,MAAM;IAAEC,OAAO;IAAEC;EAAK,CAAC,GAAGH,IAAI;EACrC,MAAM;IAACI;EAAC,CAAC,GAAGH,MAAM;EAClB,MAAM;IAACI;EAAI,CAAC,GAAGF,KAAK;EAEpB,MAAMG,KAAK,GAAGF,CAAC,CAACG,KAAK,CAACC,MAAM;EAE5B,MAAMC,KAAK,GAAGd,IAAI,CAACe,cAAc,CAACL,IAAI,EAAED,CAAC,CAACG,KAAK,CAAC;EAChD,IAAID,KAAK,KAAK,CAAC,EAAE;IACf,OAAOR,QAAQ,CAAC;MAACG,MAAM,EAAE;QAACG;MAAC,CAAC;MAAEF;IAAO,CAAC,CAAC;;EAGzC,MAAMS,OAAO,GAAGlB,GAAG,EAAE,CAACmB,OAAO,CAAC,6BAA6B,CAAC,GACxD,IAAIf,oBAAoB,CAACO,CAAC,CAACG,KAAK,EAAEE,KAAK,CAAC,GACxC,IAAIb,cAAc,CAACQ,CAAC,CAACG,KAAK,EAAEE,KAAK,CAAC;EAEtC,OAAOP,OAAO,CAACW,eAAe,CAACF,OAAO,EAAE,CAACP,CAAC,CAAC,EAAEA,CAAC,CAACU,KAAK,CAAC;AACvD;AAEA,OAAO,MAAMC,aAAa,GAAiB;EACzCC,UAAU,EAAEtB,OAAO;EACnBuB,WAAW,EAAE,OAAO;EACpBC,UAAU,EAAEnB;CACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}