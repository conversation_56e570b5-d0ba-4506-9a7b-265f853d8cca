{"ast": null, "code": "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { ENGINE } from '../engine';\nimport { Reshape } from '../kernel_names';\nimport { convertToTensor } from '../tensor_util_env';\nimport { op } from './operation';\n/**\n * Reshapes a `tf.Tensor` to a given shape.\n *\n * Given an input tensor, returns a new tensor with the same values as the\n * input tensor with shape `shape`.\n *\n * If one component of shape is the special value -1, the size of that\n * dimension is computed so that the total size remains constant. In\n * particular, a shape of [-1] flattens into 1-D. At most one component of\n * shape can be -1.\n *\n * If shape is 1-D or higher, then the operation returns a tensor with shape\n * shape filled with the values of tensor. In this case, the number of\n * elements implied by shape must be the same as the number of elements in\n * tensor.\n *\n * ```js\n * const x = tf.tensor1d([1, 2, 3, 4]);\n * x.reshape([2, 2]).print();\n * ```\n *\n * @param x The input tensor to be reshaped.\n * @param shape An array of integers defining the output tensor shape.\n *\n * @doc {heading: 'Tensors', subheading: 'Transformations'}\n */\nfunction reshape_(x, shape) {\n  const $x = convertToTensor(x, 'x', 'reshape', 'string_or_numeric');\n  const inputs = {\n    x: $x\n  };\n  const attrs = {\n    shape\n  };\n  return ENGINE.runKernel(Reshape, inputs, attrs);\n}\nexport const reshape = /* @__PURE__ */op({\n  reshape_\n});", "map": {"version": 3, "names": ["ENGINE", "Reshape", "convertToTensor", "op", "reshape_", "x", "shape", "$x", "inputs", "attrs", "runKernel", "reshape"], "sources": ["C:\\tfjs-core\\src\\ops\\reshape.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {ENGINE} from '../engine';\nimport {Reshape, ReshapeAttrs, ReshapeInputs} from '../kernel_names';\nimport {NamedAttrMap} from '../kernel_registry';\nimport {Tensor} from '../tensor';\nimport {NamedTensorMap} from '../tensor_types';\nimport {convertToTensor} from '../tensor_util_env';\nimport {Rank, ShapeMap, TensorLike} from '../types';\n\nimport {op} from './operation';\n\n/**\n * Reshapes a `tf.Tensor` to a given shape.\n *\n * Given an input tensor, returns a new tensor with the same values as the\n * input tensor with shape `shape`.\n *\n * If one component of shape is the special value -1, the size of that\n * dimension is computed so that the total size remains constant. In\n * particular, a shape of [-1] flattens into 1-D. At most one component of\n * shape can be -1.\n *\n * If shape is 1-D or higher, then the operation returns a tensor with shape\n * shape filled with the values of tensor. In this case, the number of\n * elements implied by shape must be the same as the number of elements in\n * tensor.\n *\n * ```js\n * const x = tf.tensor1d([1, 2, 3, 4]);\n * x.reshape([2, 2]).print();\n * ```\n *\n * @param x The input tensor to be reshaped.\n * @param shape An array of integers defining the output tensor shape.\n *\n * @doc {heading: 'Tensors', subheading: 'Transformations'}\n */\nfunction reshape_<R extends Rank>(\n    x: Tensor|TensorLike, shape: ShapeMap[R]): Tensor<R> {\n  const $x = convertToTensor(x, 'x', 'reshape', 'string_or_numeric');\n\n  const inputs: ReshapeInputs = {x: $x};\n  const attrs: ReshapeAttrs = {shape};\n  return ENGINE.runKernel(\n      Reshape, inputs as unknown as NamedTensorMap,\n      attrs as unknown as NamedAttrMap);\n}\nexport const reshape = /* @__PURE__ */ op({reshape_});\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAiBA,SAAQA,MAAM,QAAO,WAAW;AAChC,SAAQC,OAAO,QAAoC,iBAAiB;AAIpE,SAAQC,eAAe,QAAO,oBAAoB;AAGlD,SAAQC,EAAE,QAAO,aAAa;AAE9B;;;;;;;;;;;;;;;;;;;;;;;;;;AA0BA,SAASC,QAAQA,CACbC,CAAoB,EAAEC,KAAkB;EAC1C,MAAMC,EAAE,GAAGL,eAAe,CAACG,CAAC,EAAE,GAAG,EAAE,SAAS,EAAE,mBAAmB,CAAC;EAElE,MAAMG,MAAM,GAAkB;IAACH,CAAC,EAAEE;EAAE,CAAC;EACrC,MAAME,KAAK,GAAiB;IAACH;EAAK,CAAC;EACnC,OAAON,MAAM,CAACU,SAAS,CACnBT,OAAO,EAAEO,MAAmC,EAC5CC,KAAgC,CAAC;AACvC;AACA,OAAO,MAAME,OAAO,GAAG,eAAgBR,EAAE,CAAC;EAACC;AAAQ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}