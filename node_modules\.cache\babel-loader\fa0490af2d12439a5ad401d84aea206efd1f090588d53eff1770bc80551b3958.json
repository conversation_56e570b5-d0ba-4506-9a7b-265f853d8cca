{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.transformReply = exports.transformArguments = exports.IS_READ_ONLY = void 0;\nconst SEARCH_1 = require(\"./SEARCH\");\nconst _1 = require(\".\");\nexports.IS_READ_ONLY = true;\nfunction transformArguments(index, query, options) {\n  let args = ['FT.PROFILE', index, 'SEARCH'];\n  if (options?.LIMITED) {\n    args.push('LIMITED');\n  }\n  args.push('QUERY', query);\n  return (0, _1.pushSearchOptions)(args, options);\n}\nexports.transformArguments = transformArguments;\nfunction transformReply(reply, withoutDocuments) {\n  return {\n    results: (0, SEARCH_1.transformReply)(reply[0], withoutDocuments),\n    profile: (0, _1.transformProfile)(reply[1])\n  };\n}\nexports.transformReply = transformReply;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "transformReply", "transformArguments", "IS_READ_ONLY", "SEARCH_1", "require", "_1", "index", "query", "options", "args", "LIMITED", "push", "pushSearchOptions", "reply", "withoutDocuments", "results", "profile", "transformProfile"], "sources": ["C:/tmsft/node_modules/@redis/search/dist/commands/PROFILE_SEARCH.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.transformReply = exports.transformArguments = exports.IS_READ_ONLY = void 0;\nconst SEARCH_1 = require(\"./SEARCH\");\nconst _1 = require(\".\");\nexports.IS_READ_ONLY = true;\nfunction transformArguments(index, query, options) {\n    let args = ['FT.PROFILE', index, 'SEARCH'];\n    if (options?.LIMITED) {\n        args.push('LIMITED');\n    }\n    args.push('QUERY', query);\n    return (0, _1.pushSearchOptions)(args, options);\n}\nexports.transformArguments = transformArguments;\nfunction transformReply(reply, withoutDocuments) {\n    return {\n        results: (0, SEARCH_1.transformReply)(reply[0], withoutDocuments),\n        profile: (0, _1.transformProfile)(reply[1])\n    };\n}\nexports.transformReply = transformReply;\n"], "mappings": "AAAA,YAAY;;AACZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEC,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7DD,OAAO,CAACE,cAAc,GAAGF,OAAO,CAACG,kBAAkB,GAAGH,OAAO,CAACI,YAAY,GAAG,KAAK,CAAC;AACnF,MAAMC,QAAQ,GAAGC,OAAO,CAAC,UAAU,CAAC;AACpC,MAAMC,EAAE,GAAGD,OAAO,CAAC,GAAG,CAAC;AACvBN,OAAO,CAACI,YAAY,GAAG,IAAI;AAC3B,SAASD,kBAAkBA,CAACK,KAAK,EAAEC,KAAK,EAAEC,OAAO,EAAE;EAC/C,IAAIC,IAAI,GAAG,CAAC,YAAY,EAAEH,KAAK,EAAE,QAAQ,CAAC;EAC1C,IAAIE,OAAO,EAAEE,OAAO,EAAE;IAClBD,IAAI,CAACE,IAAI,CAAC,SAAS,CAAC;EACxB;EACAF,IAAI,CAACE,IAAI,CAAC,OAAO,EAAEJ,KAAK,CAAC;EACzB,OAAO,CAAC,CAAC,EAAEF,EAAE,CAACO,iBAAiB,EAAEH,IAAI,EAAED,OAAO,CAAC;AACnD;AACAV,OAAO,CAACG,kBAAkB,GAAGA,kBAAkB;AAC/C,SAASD,cAAcA,CAACa,KAAK,EAAEC,gBAAgB,EAAE;EAC7C,OAAO;IACHC,OAAO,EAAE,CAAC,CAAC,EAAEZ,QAAQ,CAACH,cAAc,EAAEa,KAAK,CAAC,CAAC,CAAC,EAAEC,gBAAgB,CAAC;IACjEE,OAAO,EAAE,CAAC,CAAC,EAAEX,EAAE,CAACY,gBAAgB,EAAEJ,KAAK,CAAC,CAAC,CAAC;EAC9C,CAAC;AACL;AACAf,OAAO,CAACE,cAAc,GAAGA,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}