{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.transformArguments = exports.FIRST_KEY_INDEX = void 0;\nexports.FIRST_KEY_INDEX = 1;\nfunction transformArguments(key, path) {\n  const args = ['JSON.TYPE', key];\n  if (path) {\n    args.push(path);\n  }\n  return args;\n}\nexports.transformArguments = transformArguments;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "transformArguments", "FIRST_KEY_INDEX", "key", "path", "args", "push"], "sources": ["C:/tmsft/node_modules/@redis/json/dist/commands/TYPE.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.transformArguments = exports.FIRST_KEY_INDEX = void 0;\nexports.FIRST_KEY_INDEX = 1;\nfunction transformArguments(key, path) {\n    const args = ['JSON.TYPE', key];\n    if (path) {\n        args.push(path);\n    }\n    return args;\n}\nexports.transformArguments = transformArguments;\n"], "mappings": "AAAA,YAAY;;AACZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEC,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7DD,OAAO,CAACE,kBAAkB,GAAGF,OAAO,CAACG,eAAe,GAAG,KAAK,CAAC;AAC7DH,OAAO,CAACG,eAAe,GAAG,CAAC;AAC3B,SAASD,kBAAkBA,CAACE,GAAG,EAAEC,IAAI,EAAE;EACnC,MAAMC,IAAI,GAAG,CAAC,WAAW,EAAEF,GAAG,CAAC;EAC/B,IAAIC,IAAI,EAAE;IACNC,IAAI,CAACC,IAAI,CAACF,IAAI,CAAC;EACnB;EACA,OAAOC,IAAI;AACf;AACAN,OAAO,CAACE,kBAAkB,GAAGA,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}