{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.transformArguments = exports.FIRST_KEY_INDEX = void 0;\nconst _1 = require(\".\");\nexports.FIRST_KEY_INDEX = 1;\nfunction transformArguments(toAdd) {\n  const args = ['TS.MADD'];\n  for (const {\n    key,\n    timestamp,\n    value\n  } of toAdd) {\n    args.push(key, (0, _1.transformTimestampArgument)(timestamp), value.toString());\n  }\n  return args;\n}\nexports.transformArguments = transformArguments;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "transformArguments", "FIRST_KEY_INDEX", "_1", "require", "toAdd", "args", "key", "timestamp", "push", "transformTimestampArgument", "toString"], "sources": ["C:/tmsft/node_modules/@redis/time-series/dist/commands/MADD.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.transformArguments = exports.FIRST_KEY_INDEX = void 0;\nconst _1 = require(\".\");\nexports.FIRST_KEY_INDEX = 1;\nfunction transformArguments(toAdd) {\n    const args = ['TS.MADD'];\n    for (const { key, timestamp, value } of toAdd) {\n        args.push(key, (0, _1.transformTimestampArgument)(timestamp), value.toString());\n    }\n    return args;\n}\nexports.transformArguments = transformArguments;\n"], "mappings": "AAAA,YAAY;;AACZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEC,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7DD,OAAO,CAACE,kBAAkB,GAAGF,OAAO,CAACG,eAAe,GAAG,KAAK,CAAC;AAC7D,MAAMC,EAAE,GAAGC,OAAO,CAAC,GAAG,CAAC;AACvBL,OAAO,CAACG,eAAe,GAAG,CAAC;AAC3B,SAASD,kBAAkBA,CAACI,KAAK,EAAE;EAC/B,MAAMC,IAAI,GAAG,CAAC,SAAS,CAAC;EACxB,KAAK,MAAM;IAAEC,GAAG;IAAEC,SAAS;IAAER;EAAM,CAAC,IAAIK,KAAK,EAAE;IAC3CC,IAAI,CAACG,IAAI,CAACF,GAAG,EAAE,CAAC,CAAC,EAAEJ,EAAE,CAACO,0BAA0B,EAAEF,SAAS,CAAC,EAAER,KAAK,CAACW,QAAQ,CAAC,CAAC,CAAC;EACnF;EACA,OAAOL,IAAI;AACf;AACAP,OAAO,CAACE,kBAAkB,GAAGA,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}