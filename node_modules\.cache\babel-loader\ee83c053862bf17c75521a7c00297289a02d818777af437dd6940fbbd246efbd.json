{"ast": null, "code": "/**\n * @license\n * Copyright 2018 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n *\n * =============================================================================\n */\nimport { env } from '@tensorflow/tfjs-core';\nimport { DataSource } from '../datasource';\nimport { FileChunkIterator } from '../iterators/file_chunk_iterator';\nimport { isLocalPath } from '../util/source_util';\n/**\n * Represents a file, blob, or Uint8Array readable as a stream of binary data\n * chunks.\n */\nexport class FileDataSource extends DataSource {\n  /**\n   * Create a `FileDataSource`.\n   *\n   * @param input Local file path, or `File`/`Blob`/`Uint8Array` object to\n   *     read. Local file only works in node environment.\n   * @param options Options passed to the underlying `FileChunkIterator`s,\n   *   such as {chunksize: 1024}.\n   */\n  constructor(input, options = {}) {\n    super();\n    this.input = input;\n    this.options = options;\n  }\n  async iterator() {\n    if (isLocalPath(this.input) && env().get('IS_NODE')) {\n      // tslint:disable-next-line:no-require-imports\n      const fs = require('fs');\n      this.input = fs.readFileSync(this.input.slice(7));\n    }\n    // TODO(kangyizhang): Add LocalFileChunkIterator to split local streaming\n    // with file in browser.\n    return new FileChunkIterator(this.input, this.options);\n  }\n}", "map": {"version": 3, "names": ["env", "DataSource", "FileChunkIterator", "isLocalPath", "FileDataSource", "constructor", "input", "options", "iterator", "get", "fs", "require", "readFileSync", "slice"], "sources": ["C:\\tfjs-data\\src\\sources\\file_data_source.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2018 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n *\n * =============================================================================\n */\n\nimport {env} from '@tensorflow/tfjs-core';\nimport {DataSource} from '../datasource';\nimport {ByteChunkIterator} from '../iterators/byte_chunk_iterator';\nimport {FileChunkIterator, FileChunkIteratorOptions} from '../iterators/file_chunk_iterator';\nimport {FileElement} from '../types';\nimport {isLocalPath} from '../util/source_util';\n\n/**\n * Represents a file, blob, or Uint8Array readable as a stream of binary data\n * chunks.\n */\nexport class FileDataSource extends DataSource {\n  /**\n   * Create a `FileDataSource`.\n   *\n   * @param input Local file path, or `File`/`Blob`/`Uint8Array` object to\n   *     read. Local file only works in node environment.\n   * @param options Options passed to the underlying `FileChunkIterator`s,\n   *   such as {chunksize: 1024}.\n   */\n  constructor(\n      protected input: FileElement|string,\n      protected readonly options: FileChunkIteratorOptions = {}) {\n    super();\n  }\n\n  async iterator(): Promise<ByteChunkIterator> {\n    if (isLocalPath(this.input) && env().get('IS_NODE')) {\n      // tslint:disable-next-line:no-require-imports\n      const fs = require('fs');\n      this.input = fs.readFileSync((this.input as string).slice(7));\n    }\n    // TODO(kangyizhang): Add LocalFileChunkIterator to split local streaming\n    // with file in browser.\n    return new FileChunkIterator(this.input as FileElement, this.options);\n  }\n}\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;;AAkBA,SAAQA,GAAG,QAAO,uBAAuB;AACzC,SAAQC,UAAU,QAAO,eAAe;AAExC,SAAQC,iBAAiB,QAAiC,kCAAkC;AAE5F,SAAQC,WAAW,QAAO,qBAAqB;AAE/C;;;;AAIA,OAAM,MAAOC,cAAe,SAAQH,UAAU;EAC5C;;;;;;;;EAQAI,YACcC,KAAyB,EAChBC,OAAA,GAAoC,EAAE;IAC3D,KAAK,EAAE;IAFK,KAAAD,KAAK,GAALA,KAAK;IACI,KAAAC,OAAO,GAAPA,OAAO;EAE9B;EAEA,MAAMC,QAAQA,CAAA;IACZ,IAAIL,WAAW,CAAC,IAAI,CAACG,KAAK,CAAC,IAAIN,GAAG,EAAE,CAACS,GAAG,CAAC,SAAS,CAAC,EAAE;MACnD;MACA,MAAMC,EAAE,GAAGC,OAAO,CAAC,IAAI,CAAC;MACxB,IAAI,CAACL,KAAK,GAAGI,EAAE,CAACE,YAAY,CAAE,IAAI,CAACN,KAAgB,CAACO,KAAK,CAAC,CAAC,CAAC,CAAC;;IAE/D;IACA;IACA,OAAO,IAAIX,iBAAiB,CAAC,IAAI,CAACI,KAAoB,EAAE,IAAI,CAACC,OAAO,CAAC;EACvE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}