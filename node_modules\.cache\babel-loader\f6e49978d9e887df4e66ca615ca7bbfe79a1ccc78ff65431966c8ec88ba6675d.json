{"ast": null, "code": "/**\n * @license\n * Copyright 2018 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n// tslint:disable-next-line: no-imports-from-dist\nimport * as tfOps from '@tensorflow/tfjs-core/dist/ops/ops_for_converter';\nimport { getParamValue } from './utils';\nexport const executeOp = (node, tensorMap, context, ops = tfOps) => {\n  switch (node.op) {\n    case 'LowerBound':\n      {\n        const sortedSequence = getParamValue('sortedSequence', node, tensorMap, context);\n        const values = getParamValue('values', node, tensorMap, context);\n        return [ops.lowerBound(sortedSequence, values)];\n      }\n    case 'TopKV2':\n      {\n        const x = getParamValue('x', node, tensorMap, context);\n        const k = getParamValue('k', node, tensorMap, context);\n        const sorted = getParamValue('sorted', node, tensorMap, context);\n        const result = ops.topk(x, k, sorted);\n        return [result.values, result.indices];\n      }\n    case 'UpperBound':\n      {\n        const sortedSequence = getParamValue('sortedSequence', node, tensorMap, context);\n        const values = getParamValue('values', node, tensorMap, context);\n        return [ops.upperBound(sortedSequence, values)];\n      }\n    case 'Unique':\n      {\n        const x = getParamValue('x', node, tensorMap, context);\n        const result = ops.unique(x);\n        return [result.values, result.indices];\n      }\n    case 'UniqueV2':\n      {\n        const x = getParamValue('x', node, tensorMap, context);\n        const axis = getParamValue('axis', node, tensorMap, context);\n        const result = ops.unique(x, axis);\n        return [result.values, result.indices];\n      }\n    default:\n      throw TypeError(`Node type ${node.op} is not implemented`);\n  }\n};\nexport const CATEGORY = 'evaluation';", "map": {"version": 3, "names": ["tfOps", "getParamValue", "executeOp", "node", "tensorMap", "context", "ops", "op", "sortedSequence", "values", "lowerBound", "x", "k", "sorted", "result", "topk", "indices", "upperBound", "unique", "axis", "TypeError", "CATEGORY"], "sources": ["C:\\tfjs-converter\\src\\operations\\executors\\evaluation_executor.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2018 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {Tensor} from '@tensorflow/tfjs-core';\n// tslint:disable-next-line: no-imports-from-dist\nimport * as tfOps from '@tensorflow/tfjs-core/dist/ops/ops_for_converter';\n\nimport {NamedTensorsMap} from '../../data/types';\nimport {ExecutionContext} from '../../executor/execution_context';\nimport {InternalOpExecutor, Node} from '../types';\n\nimport {getParamValue} from './utils';\n\nexport const executeOp: InternalOpExecutor =\n    (node: Node, tensorMap: NamedTensorsMap, context: ExecutionContext,\n     ops = tfOps):\n        Tensor[] => {\n          switch (node.op) {\n            case 'LowerBound': {\n              const sortedSequence =\n                  getParamValue('sortedSequence', node, tensorMap, context) as\n                  Tensor;\n              const values =\n                  getParamValue('values', node, tensorMap, context) as Tensor;\n              return [ops.lowerBound(sortedSequence, values)];\n            }\n            case 'TopKV2': {\n              const x = getParamValue('x', node, tensorMap, context) as Tensor;\n              const k = getParamValue('k', node, tensorMap, context) as number;\n              const sorted =\n                  getParamValue('sorted', node, tensorMap, context) as boolean;\n              const result = ops.topk(x, k, sorted);\n              return [result.values, result.indices];\n            }\n            case 'UpperBound': {\n              const sortedSequence =\n                  getParamValue('sortedSequence', node, tensorMap, context) as\n                  Tensor;\n              const values =\n                  getParamValue('values', node, tensorMap, context) as Tensor;\n              return [ops.upperBound(sortedSequence, values)];\n            }\n            case 'Unique': {\n              const x = getParamValue('x', node, tensorMap, context) as Tensor;\n              const result = ops.unique(x);\n              return [result.values, result.indices];\n            }\n            case 'UniqueV2': {\n              const x = getParamValue('x', node, tensorMap, context) as Tensor;\n              const axis =\n                  getParamValue('axis', node, tensorMap, context) as number;\n              const result = ops.unique(x, axis);\n              return [result.values, result.indices];\n            }\n            default:\n              throw TypeError(`Node type ${node.op} is not implemented`);\n          }\n        };\n\nexport const CATEGORY = 'evaluation';\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAkBA;AACA,OAAO,KAAKA,KAAK,MAAM,kDAAkD;AAMzE,SAAQC,aAAa,QAAO,SAAS;AAErC,OAAO,MAAMC,SAAS,GAClBA,CAACC,IAAU,EAAEC,SAA0B,EAAEC,OAAyB,EACjEC,GAAG,GAAGN,KAAK,KACG;EACT,QAAQG,IAAI,CAACI,EAAE;IACb,KAAK,YAAY;MAAE;QACjB,MAAMC,cAAc,GAChBP,aAAa,CAAC,gBAAgB,EAAEE,IAAI,EAAEC,SAAS,EAAEC,OAAO,CAClD;QACV,MAAMI,MAAM,GACRR,aAAa,CAAC,QAAQ,EAAEE,IAAI,EAAEC,SAAS,EAAEC,OAAO,CAAW;QAC/D,OAAO,CAACC,GAAG,CAACI,UAAU,CAACF,cAAc,EAAEC,MAAM,CAAC,CAAC;;IAEjD,KAAK,QAAQ;MAAE;QACb,MAAME,CAAC,GAAGV,aAAa,CAAC,GAAG,EAAEE,IAAI,EAAEC,SAAS,EAAEC,OAAO,CAAW;QAChE,MAAMO,CAAC,GAAGX,aAAa,CAAC,GAAG,EAAEE,IAAI,EAAEC,SAAS,EAAEC,OAAO,CAAW;QAChE,MAAMQ,MAAM,GACRZ,aAAa,CAAC,QAAQ,EAAEE,IAAI,EAAEC,SAAS,EAAEC,OAAO,CAAY;QAChE,MAAMS,MAAM,GAAGR,GAAG,CAACS,IAAI,CAACJ,CAAC,EAAEC,CAAC,EAAEC,MAAM,CAAC;QACrC,OAAO,CAACC,MAAM,CAACL,MAAM,EAAEK,MAAM,CAACE,OAAO,CAAC;;IAExC,KAAK,YAAY;MAAE;QACjB,MAAMR,cAAc,GAChBP,aAAa,CAAC,gBAAgB,EAAEE,IAAI,EAAEC,SAAS,EAAEC,OAAO,CAClD;QACV,MAAMI,MAAM,GACRR,aAAa,CAAC,QAAQ,EAAEE,IAAI,EAAEC,SAAS,EAAEC,OAAO,CAAW;QAC/D,OAAO,CAACC,GAAG,CAACW,UAAU,CAACT,cAAc,EAAEC,MAAM,CAAC,CAAC;;IAEjD,KAAK,QAAQ;MAAE;QACb,MAAME,CAAC,GAAGV,aAAa,CAAC,GAAG,EAAEE,IAAI,EAAEC,SAAS,EAAEC,OAAO,CAAW;QAChE,MAAMS,MAAM,GAAGR,GAAG,CAACY,MAAM,CAACP,CAAC,CAAC;QAC5B,OAAO,CAACG,MAAM,CAACL,MAAM,EAAEK,MAAM,CAACE,OAAO,CAAC;;IAExC,KAAK,UAAU;MAAE;QACf,MAAML,CAAC,GAAGV,aAAa,CAAC,GAAG,EAAEE,IAAI,EAAEC,SAAS,EAAEC,OAAO,CAAW;QAChE,MAAMc,IAAI,GACNlB,aAAa,CAAC,MAAM,EAAEE,IAAI,EAAEC,SAAS,EAAEC,OAAO,CAAW;QAC7D,MAAMS,MAAM,GAAGR,GAAG,CAACY,MAAM,CAACP,CAAC,EAAEQ,IAAI,CAAC;QAClC,OAAO,CAACL,MAAM,CAACL,MAAM,EAAEK,MAAM,CAACE,OAAO,CAAC;;IAExC;MACE,MAAMI,SAAS,CAAC,aAAajB,IAAI,CAACI,EAAE,qBAAqB,CAAC;;AAEhE,CAAC;AAET,OAAO,MAAMc,QAAQ,GAAG,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}