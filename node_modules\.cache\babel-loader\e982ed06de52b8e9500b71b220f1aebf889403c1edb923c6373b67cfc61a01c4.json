{"ast": null, "code": "// Copyright (c) 2011, <PERSON>, <PERSON>\n// Plane class - depends on Vector. Some methods require Matrix and Line.\nvar Vector = require('./vector');\nvar Matrix = require('./matrix');\nvar Line = require('./line');\nvar Sylvester = require('./sylvester');\nfunction Plane() {}\nPlane.prototype = {\n  // Returns true iff the plane occupies the same space as the argument\n  eql: function (plane) {\n    return this.contains(plane.anchor) && this.isParallelTo(plane);\n  },\n  // Returns a copy of the plane\n  dup: function () {\n    return Plane.create(this.anchor, this.normal);\n  },\n  // Returns the result of translating the plane by the given vector\n  translate: function (vector) {\n    var V = vector.elements || vector;\n    return Plane.create([this.anchor.elements[0] + V[0], this.anchor.elements[1] + V[1], this.anchor.elements[2] + (V[2] || 0)], this.normal);\n  },\n  // Returns true iff the plane is parallel to the argument. Will return true\n  // if the planes are equal, or if you give a line and it lies in the plane.\n  isParallelTo: function (obj) {\n    var theta;\n    if (obj.normal) {\n      // obj is a plane\n      theta = this.normal.angleFrom(obj.normal);\n      return Math.abs(theta) <= Sylvester.precision || Math.abs(Math.PI - theta) <= Sylvester.precision;\n    } else if (obj.direction) {\n      // obj is a line\n      return this.normal.isPerpendicularTo(obj.direction);\n    }\n    return null;\n  },\n  // Returns true iff the receiver is perpendicular to the argument\n  isPerpendicularTo: function (plane) {\n    var theta = this.normal.angleFrom(plane.normal);\n    return Math.abs(Math.PI / 2 - theta) <= Sylvester.precision;\n  },\n  // Returns the plane's distance from the given object (point, line or plane)\n  distanceFrom: function (obj) {\n    if (this.intersects(obj) || this.contains(obj)) {\n      return 0;\n    }\n    if (obj.anchor) {\n      // obj is a plane or line\n      var A = this.anchor.elements,\n        B = obj.anchor.elements,\n        N = this.normal.elements;\n      return Math.abs((A[0] - B[0]) * N[0] + (A[1] - B[1]) * N[1] + (A[2] - B[2]) * N[2]);\n    } else {\n      // obj is a point\n      var P = obj.elements || obj;\n      var A = this.anchor.elements,\n        N = this.normal.elements;\n      return Math.abs((A[0] - P[0]) * N[0] + (A[1] - P[1]) * N[1] + (A[2] - (P[2] || 0)) * N[2]);\n    }\n  },\n  // Returns true iff the plane contains the given point or line\n  contains: function (obj) {\n    if (obj.normal) {\n      return null;\n    }\n    if (obj.direction) {\n      return this.contains(obj.anchor) && this.contains(obj.anchor.add(obj.direction));\n    } else {\n      var P = obj.elements || obj;\n      var A = this.anchor.elements,\n        N = this.normal.elements;\n      var diff = Math.abs(N[0] * (A[0] - P[0]) + N[1] * (A[1] - P[1]) + N[2] * (A[2] - (P[2] || 0)));\n      return diff <= Sylvester.precision;\n    }\n  },\n  // Returns true iff the plane has a unique point/line of intersection with the argument\n  intersects: function (obj) {\n    if (typeof obj.direction == 'undefined' && typeof obj.normal == 'undefined') {\n      return null;\n    }\n    return !this.isParallelTo(obj);\n  },\n  // Returns the unique intersection with the argument, if one exists. The result\n  // will be a vector if a line is supplied, and a line if a plane is supplied.\n  intersectionWith: function (obj) {\n    if (!this.intersects(obj)) {\n      return null;\n    }\n    if (obj.direction) {\n      // obj is a line\n      var A = obj.anchor.elements,\n        D = obj.direction.elements,\n        P = this.anchor.elements,\n        N = this.normal.elements;\n      var multiplier = (N[0] * (P[0] - A[0]) + N[1] * (P[1] - A[1]) + N[2] * (P[2] - A[2])) / (N[0] * D[0] + N[1] * D[1] + N[2] * D[2]);\n      return Vector.create([A[0] + D[0] * multiplier, A[1] + D[1] * multiplier, A[2] + D[2] * multiplier]);\n    } else if (obj.normal) {\n      // obj is a plane\n      var direction = this.normal.cross(obj.normal).toUnitVector();\n      // To find an anchor point, we find one co-ordinate that has a value\n      // of zero somewhere on the intersection, and remember which one we picked\n      var N = this.normal.elements,\n        A = this.anchor.elements,\n        O = obj.normal.elements,\n        B = obj.anchor.elements;\n      var solver = Matrix.Zero(2, 2),\n        i = 0;\n      while (solver.isSingular()) {\n        i++;\n        solver = Matrix.create([[N[i % 3], N[(i + 1) % 3]], [O[i % 3], O[(i + 1) % 3]]]);\n      }\n      // Then we solve the simultaneous equations in the remaining dimensions\n      var inverse = solver.inverse().elements;\n      var x = N[0] * A[0] + N[1] * A[1] + N[2] * A[2];\n      var y = O[0] * B[0] + O[1] * B[1] + O[2] * B[2];\n      var intersection = [inverse[0][0] * x + inverse[0][1] * y, inverse[1][0] * x + inverse[1][1] * y];\n      var anchor = [];\n      for (var j = 1; j <= 3; j++) {\n        // This formula picks the right element from intersection by\n        // cycling depending on which element we set to zero above\n        anchor.push(i == j ? 0 : intersection[(j + (5 - i) % 3) % 3]);\n      }\n      return Line.create(anchor, direction);\n    }\n  },\n  // Returns the point in the plane closest to the given point\n  pointClosestTo: function (point) {\n    var P = point.elements || point;\n    var A = this.anchor.elements,\n      N = this.normal.elements;\n    var dot = (A[0] - P[0]) * N[0] + (A[1] - P[1]) * N[1] + (A[2] - (P[2] || 0)) * N[2];\n    return Vector.create([P[0] + N[0] * dot, P[1] + N[1] * dot, (P[2] || 0) + N[2] * dot]);\n  },\n  // Returns a copy of the plane, rotated by t radians about the given line\n  // See notes on Line#rotate.\n  rotate: function (t, line) {\n    var R = t.determinant ? t.elements : Matrix.Rotation(t, line.direction).elements;\n    var C = line.pointClosestTo(this.anchor).elements;\n    var A = this.anchor.elements,\n      N = this.normal.elements;\n    var C1 = C[0],\n      C2 = C[1],\n      C3 = C[2],\n      A1 = A[0],\n      A2 = A[1],\n      A3 = A[2];\n    var x = A1 - C1,\n      y = A2 - C2,\n      z = A3 - C3;\n    return Plane.create([C1 + R[0][0] * x + R[0][1] * y + R[0][2] * z, C2 + R[1][0] * x + R[1][1] * y + R[1][2] * z, C3 + R[2][0] * x + R[2][1] * y + R[2][2] * z], [R[0][0] * N[0] + R[0][1] * N[1] + R[0][2] * N[2], R[1][0] * N[0] + R[1][1] * N[1] + R[1][2] * N[2], R[2][0] * N[0] + R[2][1] * N[1] + R[2][2] * N[2]]);\n  },\n  // Returns the reflection of the plane in the given point, line or plane.\n  reflectionIn: function (obj) {\n    if (obj.normal) {\n      // obj is a plane\n      var A = this.anchor.elements,\n        N = this.normal.elements;\n      var A1 = A[0],\n        A2 = A[1],\n        A3 = A[2],\n        N1 = N[0],\n        N2 = N[1],\n        N3 = N[2];\n      var newA = this.anchor.reflectionIn(obj).elements;\n      // Add the plane's normal to its anchor, then mirror that in the other plane\n      var AN1 = A1 + N1,\n        AN2 = A2 + N2,\n        AN3 = A3 + N3;\n      var Q = obj.pointClosestTo([AN1, AN2, AN3]).elements;\n      var newN = [Q[0] + (Q[0] - AN1) - newA[0], Q[1] + (Q[1] - AN2) - newA[1], Q[2] + (Q[2] - AN3) - newA[2]];\n      return Plane.create(newA, newN);\n    } else if (obj.direction) {\n      // obj is a line\n      return this.rotate(Math.PI, obj);\n    } else {\n      // obj is a point\n      var P = obj.elements || obj;\n      return Plane.create(this.anchor.reflectionIn([P[0], P[1], P[2] || 0]), this.normal);\n    }\n  },\n  // Sets the anchor point and normal to the plane. If three arguments are specified,\n  // the normal is calculated by assuming the three points should lie in the same plane.\n  // If only two are sepcified, the second is taken to be the normal. Normal vector is\n  // normalised before storage.\n  setVectors: function (anchor, v1, v2) {\n    anchor = Vector.create(anchor);\n    anchor = anchor.to3D();\n    if (anchor === null) {\n      return null;\n    }\n    v1 = Vector.create(v1);\n    v1 = v1.to3D();\n    if (v1 === null) {\n      return null;\n    }\n    if (typeof v2 == 'undefined') {\n      v2 = null;\n    } else {\n      v2 = Vector.create(v2);\n      v2 = v2.to3D();\n      if (v2 === null) {\n        return null;\n      }\n    }\n    var A1 = anchor.elements[0],\n      A2 = anchor.elements[1],\n      A3 = anchor.elements[2];\n    var v11 = v1.elements[0],\n      v12 = v1.elements[1],\n      v13 = v1.elements[2];\n    var normal, mod;\n    if (v2 !== null) {\n      var v21 = v2.elements[0],\n        v22 = v2.elements[1],\n        v23 = v2.elements[2];\n      normal = Vector.create([(v12 - A2) * (v23 - A3) - (v13 - A3) * (v22 - A2), (v13 - A3) * (v21 - A1) - (v11 - A1) * (v23 - A3), (v11 - A1) * (v22 - A2) - (v12 - A2) * (v21 - A1)]);\n      mod = normal.modulus();\n      if (mod === 0) {\n        return null;\n      }\n      normal = Vector.create([normal.elements[0] / mod, normal.elements[1] / mod, normal.elements[2] / mod]);\n    } else {\n      mod = Math.sqrt(v11 * v11 + v12 * v12 + v13 * v13);\n      if (mod === 0) {\n        return null;\n      }\n      normal = Vector.create([v1.elements[0] / mod, v1.elements[1] / mod, v1.elements[2] / mod]);\n    }\n    this.anchor = anchor;\n    this.normal = normal;\n    return this;\n  }\n};\n\n// Constructor function\nPlane.create = function (anchor, v1, v2) {\n  var P = new Plane();\n  return P.setVectors(anchor, v1, v2);\n};\n\n// X-Y-Z planes\nPlane.XY = Plane.create(Vector.Zero(3), Vector.k);\nPlane.YZ = Plane.create(Vector.Zero(3), Vector.i);\nPlane.ZX = Plane.create(Vector.Zero(3), Vector.j);\nPlane.YX = Plane.XY;\nPlane.ZY = Plane.YZ;\nPlane.XZ = Plane.ZX;\n\n// Returns the plane containing the given points (can be arrays as\n// well as vectors). If the points are not coplanar, returns null.\nPlane.fromPoints = function (points) {\n  var np = points.length,\n    list = [],\n    i,\n    P,\n    n,\n    N,\n    A,\n    B,\n    C,\n    D,\n    theta,\n    prevN,\n    totalN = Vector.Zero(3);\n  for (i = 0; i < np; i++) {\n    P = Vector.create(points[i]).to3D();\n    if (P === null) {\n      return null;\n    }\n    list.push(P);\n    n = list.length;\n    if (n > 2) {\n      // Compute plane normal for the latest three points\n      A = list[n - 1].elements;\n      B = list[n - 2].elements;\n      C = list[n - 3].elements;\n      N = Vector.create([(A[1] - B[1]) * (C[2] - B[2]) - (A[2] - B[2]) * (C[1] - B[1]), (A[2] - B[2]) * (C[0] - B[0]) - (A[0] - B[0]) * (C[2] - B[2]), (A[0] - B[0]) * (C[1] - B[1]) - (A[1] - B[1]) * (C[0] - B[0])]).toUnitVector();\n      if (n > 3) {\n        // If the latest normal is not (anti)parallel to the previous one, we've strayed off the plane.\n        // This might be a slightly long-winded way of doing things, but we need the sum of all the normals\n        // to find which way the plane normal should point so that the points form an anticlockwise list.\n        theta = N.angleFrom(prevN);\n        if (theta !== null) {\n          if (!(Math.abs(theta) <= Sylvester.precision || Math.abs(theta - Math.PI) <= Sylvester.precision)) {\n            return null;\n          }\n        }\n      }\n      totalN = totalN.add(N);\n      prevN = N;\n    }\n  }\n  // We need to add in the normals at the start and end points, which the above misses out\n  A = list[1].elements;\n  B = list[0].elements;\n  C = list[n - 1].elements;\n  D = list[n - 2].elements;\n  totalN = totalN.add(Vector.create([(A[1] - B[1]) * (C[2] - B[2]) - (A[2] - B[2]) * (C[1] - B[1]), (A[2] - B[2]) * (C[0] - B[0]) - (A[0] - B[0]) * (C[2] - B[2]), (A[0] - B[0]) * (C[1] - B[1]) - (A[1] - B[1]) * (C[0] - B[0])]).toUnitVector()).add(Vector.create([(B[1] - C[1]) * (D[2] - C[2]) - (B[2] - C[2]) * (D[1] - C[1]), (B[2] - C[2]) * (D[0] - C[0]) - (B[0] - C[0]) * (D[2] - C[2]), (B[0] - C[0]) * (D[1] - C[1]) - (B[1] - C[1]) * (D[0] - C[0])]).toUnitVector());\n  return Plane.create(list[0], totalN);\n};\nmodule.exports = Plane;", "map": {"version": 3, "names": ["Vector", "require", "Matrix", "Line", "<PERSON>", "Plane", "prototype", "eql", "plane", "contains", "anchor", "isParallelTo", "dup", "create", "normal", "translate", "vector", "V", "elements", "obj", "theta", "angleFrom", "Math", "abs", "precision", "PI", "direction", "isPerpendicularTo", "distanceFrom", "intersects", "A", "B", "N", "P", "add", "diff", "intersectionWith", "D", "multiplier", "cross", "toUnitVector", "O", "solver", "Zero", "i", "isSingular", "inverse", "x", "y", "intersection", "j", "push", "pointClosestTo", "point", "dot", "rotate", "t", "line", "R", "determinant", "Rotation", "C", "C1", "C2", "C3", "A1", "A2", "A3", "z", "reflectionIn", "N1", "N2", "N3", "newA", "AN1", "AN2", "AN3", "Q", "newN", "setVectors", "v1", "v2", "to3D", "v11", "v12", "v13", "mod", "v21", "v22", "v23", "modulus", "sqrt", "XY", "k", "YZ", "ZX", "YX", "ZY", "XZ", "fromPoints", "points", "np", "length", "list", "n", "prevN", "totalN", "module", "exports"], "sources": ["C:/tmsft/node_modules/sylvester/lib/node-sylvester/plane.js"], "sourcesContent": ["// Copyright (c) 2011, <PERSON>, <PERSON>\n// Plane class - depends on Vector. Some methods require Matrix and Line.\nvar Vector = require('./vector');\nvar Matrix = require('./matrix');\nvar Line = require('./line');\n\nvar Sylvester = require('./sylvester');\n\nfunction Plane() {}\nPlane.prototype = {\n\n  // Returns true iff the plane occupies the same space as the argument\n  eql: function(plane) {\n    return (this.contains(plane.anchor) && this.isParallelTo(plane));\n  },\n\n  // Returns a copy of the plane\n  dup: function() {\n    return Plane.create(this.anchor, this.normal);\n  },\n\n  // Returns the result of translating the plane by the given vector\n  translate: function(vector) {\n    var V = vector.elements || vector;\n    return Plane.create([\n      this.anchor.elements[0] + V[0],\n      this.anchor.elements[1] + V[1],\n      this.anchor.elements[2] + (V[2] || 0)\n    ], this.normal);\n  },\n\n  // Returns true iff the plane is parallel to the argument. Will return true\n  // if the planes are equal, or if you give a line and it lies in the plane.\n  isParallelTo: function(obj) {\n    var theta;\n    if (obj.normal) {\n      // obj is a plane\n      theta = this.normal.angleFrom(obj.normal);\n      return (Math.abs(theta) <= Sylvester.precision || Math.abs(Math.PI - theta) <= Sylvester.precision);\n    } else if (obj.direction) {\n      // obj is a line\n      return this.normal.isPerpendicularTo(obj.direction);\n    }\n    return null;\n  },\n\n  // Returns true iff the receiver is perpendicular to the argument\n  isPerpendicularTo: function(plane) {\n    var theta = this.normal.angleFrom(plane.normal);\n    return (Math.abs(Math.PI/2 - theta) <= Sylvester.precision);\n  },\n\n  // Returns the plane's distance from the given object (point, line or plane)\n  distanceFrom: function(obj) {\n    if (this.intersects(obj) || this.contains(obj)) { return 0; }\n    if (obj.anchor) {\n      // obj is a plane or line\n      var A = this.anchor.elements, B = obj.anchor.elements, N = this.normal.elements;\n      return Math.abs((A[0] - B[0]) * N[0] + (A[1] - B[1]) * N[1] + (A[2] - B[2]) * N[2]);\n    } else {\n      // obj is a point\n      var P = obj.elements || obj;\n      var A = this.anchor.elements, N = this.normal.elements;\n      return Math.abs((A[0] - P[0]) * N[0] + (A[1] - P[1]) * N[1] + (A[2] - (P[2] || 0)) * N[2]);\n    }\n  },\n\n  // Returns true iff the plane contains the given point or line\n  contains: function(obj) {\n    if (obj.normal) { return null; }\n    if (obj.direction) {\n      return (this.contains(obj.anchor) && this.contains(obj.anchor.add(obj.direction)));\n    } else {\n      var P = obj.elements || obj;\n      var A = this.anchor.elements, N = this.normal.elements;\n      var diff = Math.abs(N[0]*(A[0] - P[0]) + N[1]*(A[1] - P[1]) + N[2]*(A[2] - (P[2] || 0)));\n      return (diff <= Sylvester.precision);\n    }\n  },\n\n  // Returns true iff the plane has a unique point/line of intersection with the argument\n  intersects: function(obj) {\n    if (typeof(obj.direction) == 'undefined' && typeof(obj.normal) == 'undefined') { return null; }\n    return !this.isParallelTo(obj);\n  },\n\n  // Returns the unique intersection with the argument, if one exists. The result\n  // will be a vector if a line is supplied, and a line if a plane is supplied.\n  intersectionWith: function(obj) {\n    if (!this.intersects(obj)) { return null; }\n    if (obj.direction) {\n      // obj is a line\n      var A = obj.anchor.elements, D = obj.direction.elements,\n          P = this.anchor.elements, N = this.normal.elements;\n      var multiplier = (N[0]*(P[0]-A[0]) + N[1]*(P[1]-A[1]) + N[2]*(P[2]-A[2])) / (N[0]*D[0] + N[1]*D[1] + N[2]*D[2]);\n      return Vector.create([A[0] + D[0]*multiplier, A[1] + D[1]*multiplier, A[2] + D[2]*multiplier]);\n    } else if (obj.normal) {\n      // obj is a plane\n      var direction = this.normal.cross(obj.normal).toUnitVector();\n      // To find an anchor point, we find one co-ordinate that has a value\n      // of zero somewhere on the intersection, and remember which one we picked\n      var N = this.normal.elements, A = this.anchor.elements,\n          O = obj.normal.elements, B = obj.anchor.elements;\n      var solver = Matrix.Zero(2,2), i = 0;\n      while (solver.isSingular()) {\n        i++;\n        solver = Matrix.create([\n          [ N[i%3], N[(i+1)%3] ],\n          [ O[i%3], O[(i+1)%3]  ]\n        ]);\n      }\n      // Then we solve the simultaneous equations in the remaining dimensions\n      var inverse = solver.inverse().elements;\n      var x = N[0]*A[0] + N[1]*A[1] + N[2]*A[2];\n      var y = O[0]*B[0] + O[1]*B[1] + O[2]*B[2];\n      var intersection = [\n        inverse[0][0] * x + inverse[0][1] * y,\n        inverse[1][0] * x + inverse[1][1] * y\n      ];\n      var anchor = [];\n      for (var j = 1; j <= 3; j++) {\n        // This formula picks the right element from intersection by\n        // cycling depending on which element we set to zero above\n        anchor.push((i == j) ? 0 : intersection[(j + (5 - i)%3)%3]);\n      }\n      return Line.create(anchor, direction);\n    }\n  },\n\n  // Returns the point in the plane closest to the given point\n  pointClosestTo: function(point) {\n    var P = point.elements || point;\n    var A = this.anchor.elements, N = this.normal.elements;\n    var dot = (A[0] - P[0]) * N[0] + (A[1] - P[1]) * N[1] + (A[2] - (P[2] || 0)) * N[2];\n    return Vector.create([P[0] + N[0] * dot, P[1] + N[1] * dot, (P[2] || 0) + N[2] * dot]);\n  },\n\n  // Returns a copy of the plane, rotated by t radians about the given line\n  // See notes on Line#rotate.\n  rotate: function(t, line) {\n    var R = t.determinant ? t.elements : Matrix.Rotation(t, line.direction).elements;\n    var C = line.pointClosestTo(this.anchor).elements;\n    var A = this.anchor.elements, N = this.normal.elements;\n    var C1 = C[0], C2 = C[1], C3 = C[2], A1 = A[0], A2 = A[1], A3 = A[2];\n    var x = A1 - C1, y = A2 - C2, z = A3 - C3;\n    return Plane.create([\n      C1 + R[0][0] * x + R[0][1] * y + R[0][2] * z,\n      C2 + R[1][0] * x + R[1][1] * y + R[1][2] * z,\n      C3 + R[2][0] * x + R[2][1] * y + R[2][2] * z\n    ], [\n      R[0][0] * N[0] + R[0][1] * N[1] + R[0][2] * N[2],\n      R[1][0] * N[0] + R[1][1] * N[1] + R[1][2] * N[2],\n      R[2][0] * N[0] + R[2][1] * N[1] + R[2][2] * N[2]\n    ]);\n  },\n\n  // Returns the reflection of the plane in the given point, line or plane.\n  reflectionIn: function(obj) {\n    if (obj.normal) {\n      // obj is a plane\n      var A = this.anchor.elements, N = this.normal.elements;\n      var A1 = A[0], A2 = A[1], A3 = A[2], N1 = N[0], N2 = N[1], N3 = N[2];\n      var newA = this.anchor.reflectionIn(obj).elements;\n      // Add the plane's normal to its anchor, then mirror that in the other plane\n      var AN1 = A1 + N1, AN2 = A2 + N2, AN3 = A3 + N3;\n      var Q = obj.pointClosestTo([AN1, AN2, AN3]).elements;\n      var newN = [Q[0] + (Q[0] - AN1) - newA[0], Q[1] + (Q[1] - AN2) - newA[1], Q[2] + (Q[2] - AN3) - newA[2]];\n      return Plane.create(newA, newN);\n    } else if (obj.direction) {\n      // obj is a line\n      return this.rotate(Math.PI, obj);\n    } else {\n      // obj is a point\n      var P = obj.elements || obj;\n      return Plane.create(this.anchor.reflectionIn([P[0], P[1], (P[2] || 0)]), this.normal);\n    }\n  },\n\n  // Sets the anchor point and normal to the plane. If three arguments are specified,\n  // the normal is calculated by assuming the three points should lie in the same plane.\n  // If only two are sepcified, the second is taken to be the normal. Normal vector is\n  // normalised before storage.\n  setVectors: function(anchor, v1, v2) {\n    anchor = Vector.create(anchor);\n    anchor = anchor.to3D(); if (anchor === null) { return null; }\n    v1 = Vector.create(v1);\n    v1 = v1.to3D(); if (v1 === null) { return null; }\n    if (typeof(v2) == 'undefined') {\n      v2 = null;\n    } else {\n      v2 = Vector.create(v2);\n      v2 = v2.to3D(); if (v2 === null) { return null; }\n    }\n    var A1 = anchor.elements[0], A2 = anchor.elements[1], A3 = anchor.elements[2];\n    var v11 = v1.elements[0], v12 = v1.elements[1], v13 = v1.elements[2];\n    var normal, mod;\n    if (v2 !== null) {\n      var v21 = v2.elements[0], v22 = v2.elements[1], v23 = v2.elements[2];\n      normal = Vector.create([\n        (v12 - A2) * (v23 - A3) - (v13 - A3) * (v22 - A2),\n        (v13 - A3) * (v21 - A1) - (v11 - A1) * (v23 - A3),\n        (v11 - A1) * (v22 - A2) - (v12 - A2) * (v21 - A1)\n      ]);\n      mod = normal.modulus();\n      if (mod === 0) { return null; }\n      normal = Vector.create([normal.elements[0] / mod, normal.elements[1] / mod, normal.elements[2] / mod]);\n    } else {\n      mod = Math.sqrt(v11*v11 + v12*v12 + v13*v13);\n      if (mod === 0) { return null; }\n      normal = Vector.create([v1.elements[0] / mod, v1.elements[1] / mod, v1.elements[2] / mod]);\n    }\n    this.anchor = anchor;\n    this.normal = normal;\n    return this;\n  }\n};\n\n// Constructor function\nPlane.create = function(anchor, v1, v2) {\n  var P = new Plane();\n  return P.setVectors(anchor, v1, v2);\n};\n\n// X-Y-Z planes\nPlane.XY = Plane.create(Vector.Zero(3), Vector.k);\nPlane.YZ = Plane.create(Vector.Zero(3), Vector.i);\nPlane.ZX = Plane.create(Vector.Zero(3), Vector.j);\nPlane.YX = Plane.XY; Plane.ZY = Plane.YZ; Plane.XZ = Plane.ZX;\n\n// Returns the plane containing the given points (can be arrays as\n// well as vectors). If the points are not coplanar, returns null.\nPlane.fromPoints = function(points) {\n  var np = points.length, list = [], i, P, n, N, A, B, C, D, theta, prevN, totalN = Vector.Zero(3);\n  for (i = 0; i < np; i++) {\n    P = Vector.create(points[i]).to3D();\n    if (P === null) { return null; }\n    list.push(P);\n    n = list.length;\n    if (n > 2) {\n      // Compute plane normal for the latest three points\n      A = list[n-1].elements; B = list[n-2].elements; C = list[n-3].elements;\n      N = Vector.create([\n        (A[1] - B[1]) * (C[2] - B[2]) - (A[2] - B[2]) * (C[1] - B[1]),\n        (A[2] - B[2]) * (C[0] - B[0]) - (A[0] - B[0]) * (C[2] - B[2]),\n        (A[0] - B[0]) * (C[1] - B[1]) - (A[1] - B[1]) * (C[0] - B[0])\n      ]).toUnitVector();\n      if (n > 3) {\n        // If the latest normal is not (anti)parallel to the previous one, we've strayed off the plane.\n        // This might be a slightly long-winded way of doing things, but we need the sum of all the normals\n        // to find which way the plane normal should point so that the points form an anticlockwise list.\n        theta = N.angleFrom(prevN);\n        if (theta !== null) {\n          if (!(Math.abs(theta) <= Sylvester.precision || Math.abs(theta - Math.PI) <= Sylvester.precision)) { return null; }\n        }\n      }\n      totalN = totalN.add(N);\n      prevN = N;\n    }\n  }\n  // We need to add in the normals at the start and end points, which the above misses out\n  A = list[1].elements; B = list[0].elements; C = list[n-1].elements; D = list[n-2].elements;\n  totalN = totalN.add(Vector.create([\n    (A[1] - B[1]) * (C[2] - B[2]) - (A[2] - B[2]) * (C[1] - B[1]),\n    (A[2] - B[2]) * (C[0] - B[0]) - (A[0] - B[0]) * (C[2] - B[2]),\n    (A[0] - B[0]) * (C[1] - B[1]) - (A[1] - B[1]) * (C[0] - B[0])\n  ]).toUnitVector()).add(Vector.create([\n    (B[1] - C[1]) * (D[2] - C[2]) - (B[2] - C[2]) * (D[1] - C[1]),\n    (B[2] - C[2]) * (D[0] - C[0]) - (B[0] - C[0]) * (D[2] - C[2]),\n    (B[0] - C[0]) * (D[1] - C[1]) - (B[1] - C[1]) * (D[0] - C[0])\n  ]).toUnitVector());\n  return Plane.create(list[0], totalN);\n};\n\nmodule.exports = Plane;\n"], "mappings": "AAAA;AACA;AACA,IAAIA,MAAM,GAAGC,OAAO,CAAC,UAAU,CAAC;AAChC,IAAIC,MAAM,GAAGD,OAAO,CAAC,UAAU,CAAC;AAChC,IAAIE,IAAI,GAAGF,OAAO,CAAC,QAAQ,CAAC;AAE5B,IAAIG,SAAS,GAAGH,OAAO,CAAC,aAAa,CAAC;AAEtC,SAASI,KAAKA,CAAA,EAAG,CAAC;AAClBA,KAAK,CAACC,SAAS,GAAG;EAEhB;EACAC,GAAG,EAAE,SAAAA,CAASC,KAAK,EAAE;IACnB,OAAQ,IAAI,CAACC,QAAQ,CAACD,KAAK,CAACE,MAAM,CAAC,IAAI,IAAI,CAACC,YAAY,CAACH,KAAK,CAAC;EACjE,CAAC;EAED;EACAI,GAAG,EAAE,SAAAA,CAAA,EAAW;IACd,OAAOP,KAAK,CAACQ,MAAM,CAAC,IAAI,CAACH,MAAM,EAAE,IAAI,CAACI,MAAM,CAAC;EAC/C,CAAC;EAED;EACAC,SAAS,EAAE,SAAAA,CAASC,MAAM,EAAE;IAC1B,IAAIC,CAAC,GAAGD,MAAM,CAACE,QAAQ,IAAIF,MAAM;IACjC,OAAOX,KAAK,CAACQ,MAAM,CAAC,CAClB,IAAI,CAACH,MAAM,CAACQ,QAAQ,CAAC,CAAC,CAAC,GAAGD,CAAC,CAAC,CAAC,CAAC,EAC9B,IAAI,CAACP,MAAM,CAACQ,QAAQ,CAAC,CAAC,CAAC,GAAGD,CAAC,CAAC,CAAC,CAAC,EAC9B,IAAI,CAACP,MAAM,CAACQ,QAAQ,CAAC,CAAC,CAAC,IAAID,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CACtC,EAAE,IAAI,CAACH,MAAM,CAAC;EACjB,CAAC;EAED;EACA;EACAH,YAAY,EAAE,SAAAA,CAASQ,GAAG,EAAE;IAC1B,IAAIC,KAAK;IACT,IAAID,GAAG,CAACL,MAAM,EAAE;MACd;MACAM,KAAK,GAAG,IAAI,CAACN,MAAM,CAACO,SAAS,CAACF,GAAG,CAACL,MAAM,CAAC;MACzC,OAAQQ,IAAI,CAACC,GAAG,CAACH,KAAK,CAAC,IAAIhB,SAAS,CAACoB,SAAS,IAAIF,IAAI,CAACC,GAAG,CAACD,IAAI,CAACG,EAAE,GAAGL,KAAK,CAAC,IAAIhB,SAAS,CAACoB,SAAS;IACpG,CAAC,MAAM,IAAIL,GAAG,CAACO,SAAS,EAAE;MACxB;MACA,OAAO,IAAI,CAACZ,MAAM,CAACa,iBAAiB,CAACR,GAAG,CAACO,SAAS,CAAC;IACrD;IACA,OAAO,IAAI;EACb,CAAC;EAED;EACAC,iBAAiB,EAAE,SAAAA,CAASnB,KAAK,EAAE;IACjC,IAAIY,KAAK,GAAG,IAAI,CAACN,MAAM,CAACO,SAAS,CAACb,KAAK,CAACM,MAAM,CAAC;IAC/C,OAAQQ,IAAI,CAACC,GAAG,CAACD,IAAI,CAACG,EAAE,GAAC,CAAC,GAAGL,KAAK,CAAC,IAAIhB,SAAS,CAACoB,SAAS;EAC5D,CAAC;EAED;EACAI,YAAY,EAAE,SAAAA,CAAST,GAAG,EAAE;IAC1B,IAAI,IAAI,CAACU,UAAU,CAACV,GAAG,CAAC,IAAI,IAAI,CAACV,QAAQ,CAACU,GAAG,CAAC,EAAE;MAAE,OAAO,CAAC;IAAE;IAC5D,IAAIA,GAAG,CAACT,MAAM,EAAE;MACd;MACA,IAAIoB,CAAC,GAAG,IAAI,CAACpB,MAAM,CAACQ,QAAQ;QAAEa,CAAC,GAAGZ,GAAG,CAACT,MAAM,CAACQ,QAAQ;QAAEc,CAAC,GAAG,IAAI,CAAClB,MAAM,CAACI,QAAQ;MAC/E,OAAOI,IAAI,CAACC,GAAG,CAAC,CAACO,CAAC,CAAC,CAAC,CAAC,GAAGC,CAAC,CAAC,CAAC,CAAC,IAAIC,CAAC,CAAC,CAAC,CAAC,GAAG,CAACF,CAAC,CAAC,CAAC,CAAC,GAAGC,CAAC,CAAC,CAAC,CAAC,IAAIC,CAAC,CAAC,CAAC,CAAC,GAAG,CAACF,CAAC,CAAC,CAAC,CAAC,GAAGC,CAAC,CAAC,CAAC,CAAC,IAAIC,CAAC,CAAC,CAAC,CAAC,CAAC;IACrF,CAAC,MAAM;MACL;MACA,IAAIC,CAAC,GAAGd,GAAG,CAACD,QAAQ,IAAIC,GAAG;MAC3B,IAAIW,CAAC,GAAG,IAAI,CAACpB,MAAM,CAACQ,QAAQ;QAAEc,CAAC,GAAG,IAAI,CAAClB,MAAM,CAACI,QAAQ;MACtD,OAAOI,IAAI,CAACC,GAAG,CAAC,CAACO,CAAC,CAAC,CAAC,CAAC,GAAGG,CAAC,CAAC,CAAC,CAAC,IAAID,CAAC,CAAC,CAAC,CAAC,GAAG,CAACF,CAAC,CAAC,CAAC,CAAC,GAAGG,CAAC,CAAC,CAAC,CAAC,IAAID,CAAC,CAAC,CAAC,CAAC,GAAG,CAACF,CAAC,CAAC,CAAC,CAAC,IAAIG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,IAAID,CAAC,CAAC,CAAC,CAAC,CAAC;IAC5F;EACF,CAAC;EAED;EACAvB,QAAQ,EAAE,SAAAA,CAASU,GAAG,EAAE;IACtB,IAAIA,GAAG,CAACL,MAAM,EAAE;MAAE,OAAO,IAAI;IAAE;IAC/B,IAAIK,GAAG,CAACO,SAAS,EAAE;MACjB,OAAQ,IAAI,CAACjB,QAAQ,CAACU,GAAG,CAACT,MAAM,CAAC,IAAI,IAAI,CAACD,QAAQ,CAACU,GAAG,CAACT,MAAM,CAACwB,GAAG,CAACf,GAAG,CAACO,SAAS,CAAC,CAAC;IACnF,CAAC,MAAM;MACL,IAAIO,CAAC,GAAGd,GAAG,CAACD,QAAQ,IAAIC,GAAG;MAC3B,IAAIW,CAAC,GAAG,IAAI,CAACpB,MAAM,CAACQ,QAAQ;QAAEc,CAAC,GAAG,IAAI,CAAClB,MAAM,CAACI,QAAQ;MACtD,IAAIiB,IAAI,GAAGb,IAAI,CAACC,GAAG,CAACS,CAAC,CAAC,CAAC,CAAC,IAAEF,CAAC,CAAC,CAAC,CAAC,GAAGG,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGD,CAAC,CAAC,CAAC,CAAC,IAAEF,CAAC,CAAC,CAAC,CAAC,GAAGG,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGD,CAAC,CAAC,CAAC,CAAC,IAAEF,CAAC,CAAC,CAAC,CAAC,IAAIG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;MACxF,OAAQE,IAAI,IAAI/B,SAAS,CAACoB,SAAS;IACrC;EACF,CAAC;EAED;EACAK,UAAU,EAAE,SAAAA,CAASV,GAAG,EAAE;IACxB,IAAI,OAAOA,GAAG,CAACO,SAAU,IAAI,WAAW,IAAI,OAAOP,GAAG,CAACL,MAAO,IAAI,WAAW,EAAE;MAAE,OAAO,IAAI;IAAE;IAC9F,OAAO,CAAC,IAAI,CAACH,YAAY,CAACQ,GAAG,CAAC;EAChC,CAAC;EAED;EACA;EACAiB,gBAAgB,EAAE,SAAAA,CAASjB,GAAG,EAAE;IAC9B,IAAI,CAAC,IAAI,CAACU,UAAU,CAACV,GAAG,CAAC,EAAE;MAAE,OAAO,IAAI;IAAE;IAC1C,IAAIA,GAAG,CAACO,SAAS,EAAE;MACjB;MACA,IAAII,CAAC,GAAGX,GAAG,CAACT,MAAM,CAACQ,QAAQ;QAAEmB,CAAC,GAAGlB,GAAG,CAACO,SAAS,CAACR,QAAQ;QACnDe,CAAC,GAAG,IAAI,CAACvB,MAAM,CAACQ,QAAQ;QAAEc,CAAC,GAAG,IAAI,CAAClB,MAAM,CAACI,QAAQ;MACtD,IAAIoB,UAAU,GAAG,CAACN,CAAC,CAAC,CAAC,CAAC,IAAEC,CAAC,CAAC,CAAC,CAAC,GAACH,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGE,CAAC,CAAC,CAAC,CAAC,IAAEC,CAAC,CAAC,CAAC,CAAC,GAACH,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGE,CAAC,CAAC,CAAC,CAAC,IAAEC,CAAC,CAAC,CAAC,CAAC,GAACH,CAAC,CAAC,CAAC,CAAC,CAAC,KAAKE,CAAC,CAAC,CAAC,CAAC,GAACK,CAAC,CAAC,CAAC,CAAC,GAAGL,CAAC,CAAC,CAAC,CAAC,GAACK,CAAC,CAAC,CAAC,CAAC,GAAGL,CAAC,CAAC,CAAC,CAAC,GAACK,CAAC,CAAC,CAAC,CAAC,CAAC;MAC/G,OAAOrC,MAAM,CAACa,MAAM,CAAC,CAACiB,CAAC,CAAC,CAAC,CAAC,GAAGO,CAAC,CAAC,CAAC,CAAC,GAACC,UAAU,EAAER,CAAC,CAAC,CAAC,CAAC,GAAGO,CAAC,CAAC,CAAC,CAAC,GAACC,UAAU,EAAER,CAAC,CAAC,CAAC,CAAC,GAAGO,CAAC,CAAC,CAAC,CAAC,GAACC,UAAU,CAAC,CAAC;IAChG,CAAC,MAAM,IAAInB,GAAG,CAACL,MAAM,EAAE;MACrB;MACA,IAAIY,SAAS,GAAG,IAAI,CAACZ,MAAM,CAACyB,KAAK,CAACpB,GAAG,CAACL,MAAM,CAAC,CAAC0B,YAAY,CAAC,CAAC;MAC5D;MACA;MACA,IAAIR,CAAC,GAAG,IAAI,CAAClB,MAAM,CAACI,QAAQ;QAAEY,CAAC,GAAG,IAAI,CAACpB,MAAM,CAACQ,QAAQ;QAClDuB,CAAC,GAAGtB,GAAG,CAACL,MAAM,CAACI,QAAQ;QAAEa,CAAC,GAAGZ,GAAG,CAACT,MAAM,CAACQ,QAAQ;MACpD,IAAIwB,MAAM,GAAGxC,MAAM,CAACyC,IAAI,CAAC,CAAC,EAAC,CAAC,CAAC;QAAEC,CAAC,GAAG,CAAC;MACpC,OAAOF,MAAM,CAACG,UAAU,CAAC,CAAC,EAAE;QAC1BD,CAAC,EAAE;QACHF,MAAM,GAAGxC,MAAM,CAACW,MAAM,CAAC,CACrB,CAAEmB,CAAC,CAACY,CAAC,GAAC,CAAC,CAAC,EAAEZ,CAAC,CAAC,CAACY,CAAC,GAAC,CAAC,IAAE,CAAC,CAAC,CAAE,EACtB,CAAEH,CAAC,CAACG,CAAC,GAAC,CAAC,CAAC,EAAEH,CAAC,CAAC,CAACG,CAAC,GAAC,CAAC,IAAE,CAAC,CAAC,CAAG,CACxB,CAAC;MACJ;MACA;MACA,IAAIE,OAAO,GAAGJ,MAAM,CAACI,OAAO,CAAC,CAAC,CAAC5B,QAAQ;MACvC,IAAI6B,CAAC,GAAGf,CAAC,CAAC,CAAC,CAAC,GAACF,CAAC,CAAC,CAAC,CAAC,GAAGE,CAAC,CAAC,CAAC,CAAC,GAACF,CAAC,CAAC,CAAC,CAAC,GAAGE,CAAC,CAAC,CAAC,CAAC,GAACF,CAAC,CAAC,CAAC,CAAC;MACzC,IAAIkB,CAAC,GAAGP,CAAC,CAAC,CAAC,CAAC,GAACV,CAAC,CAAC,CAAC,CAAC,GAAGU,CAAC,CAAC,CAAC,CAAC,GAACV,CAAC,CAAC,CAAC,CAAC,GAAGU,CAAC,CAAC,CAAC,CAAC,GAACV,CAAC,CAAC,CAAC,CAAC;MACzC,IAAIkB,YAAY,GAAG,CACjBH,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGC,CAAC,GAAGD,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGE,CAAC,EACrCF,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGC,CAAC,GAAGD,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGE,CAAC,CACtC;MACD,IAAItC,MAAM,GAAG,EAAE;MACf,KAAK,IAAIwC,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;QAC3B;QACA;QACAxC,MAAM,CAACyC,IAAI,CAAEP,CAAC,IAAIM,CAAC,GAAI,CAAC,GAAGD,YAAY,CAAC,CAACC,CAAC,GAAG,CAAC,CAAC,GAAGN,CAAC,IAAE,CAAC,IAAE,CAAC,CAAC,CAAC;MAC7D;MACA,OAAOzC,IAAI,CAACU,MAAM,CAACH,MAAM,EAAEgB,SAAS,CAAC;IACvC;EACF,CAAC;EAED;EACA0B,cAAc,EAAE,SAAAA,CAASC,KAAK,EAAE;IAC9B,IAAIpB,CAAC,GAAGoB,KAAK,CAACnC,QAAQ,IAAImC,KAAK;IAC/B,IAAIvB,CAAC,GAAG,IAAI,CAACpB,MAAM,CAACQ,QAAQ;MAAEc,CAAC,GAAG,IAAI,CAAClB,MAAM,CAACI,QAAQ;IACtD,IAAIoC,GAAG,GAAG,CAACxB,CAAC,CAAC,CAAC,CAAC,GAAGG,CAAC,CAAC,CAAC,CAAC,IAAID,CAAC,CAAC,CAAC,CAAC,GAAG,CAACF,CAAC,CAAC,CAAC,CAAC,GAAGG,CAAC,CAAC,CAAC,CAAC,IAAID,CAAC,CAAC,CAAC,CAAC,GAAG,CAACF,CAAC,CAAC,CAAC,CAAC,IAAIG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,IAAID,CAAC,CAAC,CAAC,CAAC;IACnF,OAAOhC,MAAM,CAACa,MAAM,CAAC,CAACoB,CAAC,CAAC,CAAC,CAAC,GAAGD,CAAC,CAAC,CAAC,CAAC,GAAGsB,GAAG,EAAErB,CAAC,CAAC,CAAC,CAAC,GAAGD,CAAC,CAAC,CAAC,CAAC,GAAGsB,GAAG,EAAE,CAACrB,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAID,CAAC,CAAC,CAAC,CAAC,GAAGsB,GAAG,CAAC,CAAC;EACxF,CAAC;EAED;EACA;EACAC,MAAM,EAAE,SAAAA,CAASC,CAAC,EAAEC,IAAI,EAAE;IACxB,IAAIC,CAAC,GAAGF,CAAC,CAACG,WAAW,GAAGH,CAAC,CAACtC,QAAQ,GAAGhB,MAAM,CAAC0D,QAAQ,CAACJ,CAAC,EAAEC,IAAI,CAAC/B,SAAS,CAAC,CAACR,QAAQ;IAChF,IAAI2C,CAAC,GAAGJ,IAAI,CAACL,cAAc,CAAC,IAAI,CAAC1C,MAAM,CAAC,CAACQ,QAAQ;IACjD,IAAIY,CAAC,GAAG,IAAI,CAACpB,MAAM,CAACQ,QAAQ;MAAEc,CAAC,GAAG,IAAI,CAAClB,MAAM,CAACI,QAAQ;IACtD,IAAI4C,EAAE,GAAGD,CAAC,CAAC,CAAC,CAAC;MAAEE,EAAE,GAAGF,CAAC,CAAC,CAAC,CAAC;MAAEG,EAAE,GAAGH,CAAC,CAAC,CAAC,CAAC;MAAEI,EAAE,GAAGnC,CAAC,CAAC,CAAC,CAAC;MAAEoC,EAAE,GAAGpC,CAAC,CAAC,CAAC,CAAC;MAAEqC,EAAE,GAAGrC,CAAC,CAAC,CAAC,CAAC;IACpE,IAAIiB,CAAC,GAAGkB,EAAE,GAAGH,EAAE;MAAEd,CAAC,GAAGkB,EAAE,GAAGH,EAAE;MAAEK,CAAC,GAAGD,EAAE,GAAGH,EAAE;IACzC,OAAO3D,KAAK,CAACQ,MAAM,CAAC,CAClBiD,EAAE,GAAGJ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGX,CAAC,GAAGW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGV,CAAC,GAAGU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGU,CAAC,EAC5CL,EAAE,GAAGL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGX,CAAC,GAAGW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGV,CAAC,GAAGU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGU,CAAC,EAC5CJ,EAAE,GAAGN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGX,CAAC,GAAGW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGV,CAAC,GAAGU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGU,CAAC,CAC7C,EAAE,CACDV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG1B,CAAC,CAAC,CAAC,CAAC,GAAG0B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG1B,CAAC,CAAC,CAAC,CAAC,GAAG0B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG1B,CAAC,CAAC,CAAC,CAAC,EAChD0B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG1B,CAAC,CAAC,CAAC,CAAC,GAAG0B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG1B,CAAC,CAAC,CAAC,CAAC,GAAG0B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG1B,CAAC,CAAC,CAAC,CAAC,EAChD0B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG1B,CAAC,CAAC,CAAC,CAAC,GAAG0B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG1B,CAAC,CAAC,CAAC,CAAC,GAAG0B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG1B,CAAC,CAAC,CAAC,CAAC,CACjD,CAAC;EACJ,CAAC;EAED;EACAqC,YAAY,EAAE,SAAAA,CAASlD,GAAG,EAAE;IAC1B,IAAIA,GAAG,CAACL,MAAM,EAAE;MACd;MACA,IAAIgB,CAAC,GAAG,IAAI,CAACpB,MAAM,CAACQ,QAAQ;QAAEc,CAAC,GAAG,IAAI,CAAClB,MAAM,CAACI,QAAQ;MACtD,IAAI+C,EAAE,GAAGnC,CAAC,CAAC,CAAC,CAAC;QAAEoC,EAAE,GAAGpC,CAAC,CAAC,CAAC,CAAC;QAAEqC,EAAE,GAAGrC,CAAC,CAAC,CAAC,CAAC;QAAEwC,EAAE,GAAGtC,CAAC,CAAC,CAAC,CAAC;QAAEuC,EAAE,GAAGvC,CAAC,CAAC,CAAC,CAAC;QAAEwC,EAAE,GAAGxC,CAAC,CAAC,CAAC,CAAC;MACpE,IAAIyC,IAAI,GAAG,IAAI,CAAC/D,MAAM,CAAC2D,YAAY,CAAClD,GAAG,CAAC,CAACD,QAAQ;MACjD;MACA,IAAIwD,GAAG,GAAGT,EAAE,GAAGK,EAAE;QAAEK,GAAG,GAAGT,EAAE,GAAGK,EAAE;QAAEK,GAAG,GAAGT,EAAE,GAAGK,EAAE;MAC/C,IAAIK,CAAC,GAAG1D,GAAG,CAACiC,cAAc,CAAC,CAACsB,GAAG,EAAEC,GAAG,EAAEC,GAAG,CAAC,CAAC,CAAC1D,QAAQ;MACpD,IAAI4D,IAAI,GAAG,CAACD,CAAC,CAAC,CAAC,CAAC,IAAIA,CAAC,CAAC,CAAC,CAAC,GAAGH,GAAG,CAAC,GAAGD,IAAI,CAAC,CAAC,CAAC,EAAEI,CAAC,CAAC,CAAC,CAAC,IAAIA,CAAC,CAAC,CAAC,CAAC,GAAGF,GAAG,CAAC,GAAGF,IAAI,CAAC,CAAC,CAAC,EAAEI,CAAC,CAAC,CAAC,CAAC,IAAIA,CAAC,CAAC,CAAC,CAAC,GAAGD,GAAG,CAAC,GAAGH,IAAI,CAAC,CAAC,CAAC,CAAC;MACxG,OAAOpE,KAAK,CAACQ,MAAM,CAAC4D,IAAI,EAAEK,IAAI,CAAC;IACjC,CAAC,MAAM,IAAI3D,GAAG,CAACO,SAAS,EAAE;MACxB;MACA,OAAO,IAAI,CAAC6B,MAAM,CAACjC,IAAI,CAACG,EAAE,EAAEN,GAAG,CAAC;IAClC,CAAC,MAAM;MACL;MACA,IAAIc,CAAC,GAAGd,GAAG,CAACD,QAAQ,IAAIC,GAAG;MAC3B,OAAOd,KAAK,CAACQ,MAAM,CAAC,IAAI,CAACH,MAAM,CAAC2D,YAAY,CAAC,CAACpC,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,EAAGA,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAE,CAAC,EAAE,IAAI,CAACnB,MAAM,CAAC;IACvF;EACF,CAAC;EAED;EACA;EACA;EACA;EACAiE,UAAU,EAAE,SAAAA,CAASrE,MAAM,EAAEsE,EAAE,EAAEC,EAAE,EAAE;IACnCvE,MAAM,GAAGV,MAAM,CAACa,MAAM,CAACH,MAAM,CAAC;IAC9BA,MAAM,GAAGA,MAAM,CAACwE,IAAI,CAAC,CAAC;IAAE,IAAIxE,MAAM,KAAK,IAAI,EAAE;MAAE,OAAO,IAAI;IAAE;IAC5DsE,EAAE,GAAGhF,MAAM,CAACa,MAAM,CAACmE,EAAE,CAAC;IACtBA,EAAE,GAAGA,EAAE,CAACE,IAAI,CAAC,CAAC;IAAE,IAAIF,EAAE,KAAK,IAAI,EAAE;MAAE,OAAO,IAAI;IAAE;IAChD,IAAI,OAAOC,EAAG,IAAI,WAAW,EAAE;MAC7BA,EAAE,GAAG,IAAI;IACX,CAAC,MAAM;MACLA,EAAE,GAAGjF,MAAM,CAACa,MAAM,CAACoE,EAAE,CAAC;MACtBA,EAAE,GAAGA,EAAE,CAACC,IAAI,CAAC,CAAC;MAAE,IAAID,EAAE,KAAK,IAAI,EAAE;QAAE,OAAO,IAAI;MAAE;IAClD;IACA,IAAIhB,EAAE,GAAGvD,MAAM,CAACQ,QAAQ,CAAC,CAAC,CAAC;MAAEgD,EAAE,GAAGxD,MAAM,CAACQ,QAAQ,CAAC,CAAC,CAAC;MAAEiD,EAAE,GAAGzD,MAAM,CAACQ,QAAQ,CAAC,CAAC,CAAC;IAC7E,IAAIiE,GAAG,GAAGH,EAAE,CAAC9D,QAAQ,CAAC,CAAC,CAAC;MAAEkE,GAAG,GAAGJ,EAAE,CAAC9D,QAAQ,CAAC,CAAC,CAAC;MAAEmE,GAAG,GAAGL,EAAE,CAAC9D,QAAQ,CAAC,CAAC,CAAC;IACpE,IAAIJ,MAAM,EAAEwE,GAAG;IACf,IAAIL,EAAE,KAAK,IAAI,EAAE;MACf,IAAIM,GAAG,GAAGN,EAAE,CAAC/D,QAAQ,CAAC,CAAC,CAAC;QAAEsE,GAAG,GAAGP,EAAE,CAAC/D,QAAQ,CAAC,CAAC,CAAC;QAAEuE,GAAG,GAAGR,EAAE,CAAC/D,QAAQ,CAAC,CAAC,CAAC;MACpEJ,MAAM,GAAGd,MAAM,CAACa,MAAM,CAAC,CACrB,CAACuE,GAAG,GAAGlB,EAAE,KAAKuB,GAAG,GAAGtB,EAAE,CAAC,GAAG,CAACkB,GAAG,GAAGlB,EAAE,KAAKqB,GAAG,GAAGtB,EAAE,CAAC,EACjD,CAACmB,GAAG,GAAGlB,EAAE,KAAKoB,GAAG,GAAGtB,EAAE,CAAC,GAAG,CAACkB,GAAG,GAAGlB,EAAE,KAAKwB,GAAG,GAAGtB,EAAE,CAAC,EACjD,CAACgB,GAAG,GAAGlB,EAAE,KAAKuB,GAAG,GAAGtB,EAAE,CAAC,GAAG,CAACkB,GAAG,GAAGlB,EAAE,KAAKqB,GAAG,GAAGtB,EAAE,CAAC,CAClD,CAAC;MACFqB,GAAG,GAAGxE,MAAM,CAAC4E,OAAO,CAAC,CAAC;MACtB,IAAIJ,GAAG,KAAK,CAAC,EAAE;QAAE,OAAO,IAAI;MAAE;MAC9BxE,MAAM,GAAGd,MAAM,CAACa,MAAM,CAAC,CAACC,MAAM,CAACI,QAAQ,CAAC,CAAC,CAAC,GAAGoE,GAAG,EAAExE,MAAM,CAACI,QAAQ,CAAC,CAAC,CAAC,GAAGoE,GAAG,EAAExE,MAAM,CAACI,QAAQ,CAAC,CAAC,CAAC,GAAGoE,GAAG,CAAC,CAAC;IACxG,CAAC,MAAM;MACLA,GAAG,GAAGhE,IAAI,CAACqE,IAAI,CAACR,GAAG,GAACA,GAAG,GAAGC,GAAG,GAACA,GAAG,GAAGC,GAAG,GAACA,GAAG,CAAC;MAC5C,IAAIC,GAAG,KAAK,CAAC,EAAE;QAAE,OAAO,IAAI;MAAE;MAC9BxE,MAAM,GAAGd,MAAM,CAACa,MAAM,CAAC,CAACmE,EAAE,CAAC9D,QAAQ,CAAC,CAAC,CAAC,GAAGoE,GAAG,EAAEN,EAAE,CAAC9D,QAAQ,CAAC,CAAC,CAAC,GAAGoE,GAAG,EAAEN,EAAE,CAAC9D,QAAQ,CAAC,CAAC,CAAC,GAAGoE,GAAG,CAAC,CAAC;IAC5F;IACA,IAAI,CAAC5E,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACI,MAAM,GAAGA,MAAM;IACpB,OAAO,IAAI;EACb;AACF,CAAC;;AAED;AACAT,KAAK,CAACQ,MAAM,GAAG,UAASH,MAAM,EAAEsE,EAAE,EAAEC,EAAE,EAAE;EACtC,IAAIhD,CAAC,GAAG,IAAI5B,KAAK,CAAC,CAAC;EACnB,OAAO4B,CAAC,CAAC8C,UAAU,CAACrE,MAAM,EAAEsE,EAAE,EAAEC,EAAE,CAAC;AACrC,CAAC;;AAED;AACA5E,KAAK,CAACuF,EAAE,GAAGvF,KAAK,CAACQ,MAAM,CAACb,MAAM,CAAC2C,IAAI,CAAC,CAAC,CAAC,EAAE3C,MAAM,CAAC6F,CAAC,CAAC;AACjDxF,KAAK,CAACyF,EAAE,GAAGzF,KAAK,CAACQ,MAAM,CAACb,MAAM,CAAC2C,IAAI,CAAC,CAAC,CAAC,EAAE3C,MAAM,CAAC4C,CAAC,CAAC;AACjDvC,KAAK,CAAC0F,EAAE,GAAG1F,KAAK,CAACQ,MAAM,CAACb,MAAM,CAAC2C,IAAI,CAAC,CAAC,CAAC,EAAE3C,MAAM,CAACkD,CAAC,CAAC;AACjD7C,KAAK,CAAC2F,EAAE,GAAG3F,KAAK,CAACuF,EAAE;AAAEvF,KAAK,CAAC4F,EAAE,GAAG5F,KAAK,CAACyF,EAAE;AAAEzF,KAAK,CAAC6F,EAAE,GAAG7F,KAAK,CAAC0F,EAAE;;AAE7D;AACA;AACA1F,KAAK,CAAC8F,UAAU,GAAG,UAASC,MAAM,EAAE;EAClC,IAAIC,EAAE,GAAGD,MAAM,CAACE,MAAM;IAAEC,IAAI,GAAG,EAAE;IAAE3D,CAAC;IAAEX,CAAC;IAAEuE,CAAC;IAAExE,CAAC;IAAEF,CAAC;IAAEC,CAAC;IAAE8B,CAAC;IAAExB,CAAC;IAAEjB,KAAK;IAAEqF,KAAK;IAAEC,MAAM,GAAG1G,MAAM,CAAC2C,IAAI,CAAC,CAAC,CAAC;EAChG,KAAKC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGyD,EAAE,EAAEzD,CAAC,EAAE,EAAE;IACvBX,CAAC,GAAGjC,MAAM,CAACa,MAAM,CAACuF,MAAM,CAACxD,CAAC,CAAC,CAAC,CAACsC,IAAI,CAAC,CAAC;IACnC,IAAIjD,CAAC,KAAK,IAAI,EAAE;MAAE,OAAO,IAAI;IAAE;IAC/BsE,IAAI,CAACpD,IAAI,CAAClB,CAAC,CAAC;IACZuE,CAAC,GAAGD,IAAI,CAACD,MAAM;IACf,IAAIE,CAAC,GAAG,CAAC,EAAE;MACT;MACA1E,CAAC,GAAGyE,IAAI,CAACC,CAAC,GAAC,CAAC,CAAC,CAACtF,QAAQ;MAAEa,CAAC,GAAGwE,IAAI,CAACC,CAAC,GAAC,CAAC,CAAC,CAACtF,QAAQ;MAAE2C,CAAC,GAAG0C,IAAI,CAACC,CAAC,GAAC,CAAC,CAAC,CAACtF,QAAQ;MACtEc,CAAC,GAAGhC,MAAM,CAACa,MAAM,CAAC,CAChB,CAACiB,CAAC,CAAC,CAAC,CAAC,GAAGC,CAAC,CAAC,CAAC,CAAC,KAAK8B,CAAC,CAAC,CAAC,CAAC,GAAG9B,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAACD,CAAC,CAAC,CAAC,CAAC,GAAGC,CAAC,CAAC,CAAC,CAAC,KAAK8B,CAAC,CAAC,CAAC,CAAC,GAAG9B,CAAC,CAAC,CAAC,CAAC,CAAC,EAC7D,CAACD,CAAC,CAAC,CAAC,CAAC,GAAGC,CAAC,CAAC,CAAC,CAAC,KAAK8B,CAAC,CAAC,CAAC,CAAC,GAAG9B,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAACD,CAAC,CAAC,CAAC,CAAC,GAAGC,CAAC,CAAC,CAAC,CAAC,KAAK8B,CAAC,CAAC,CAAC,CAAC,GAAG9B,CAAC,CAAC,CAAC,CAAC,CAAC,EAC7D,CAACD,CAAC,CAAC,CAAC,CAAC,GAAGC,CAAC,CAAC,CAAC,CAAC,KAAK8B,CAAC,CAAC,CAAC,CAAC,GAAG9B,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAACD,CAAC,CAAC,CAAC,CAAC,GAAGC,CAAC,CAAC,CAAC,CAAC,KAAK8B,CAAC,CAAC,CAAC,CAAC,GAAG9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAC9D,CAAC,CAACS,YAAY,CAAC,CAAC;MACjB,IAAIgE,CAAC,GAAG,CAAC,EAAE;QACT;QACA;QACA;QACApF,KAAK,GAAGY,CAAC,CAACX,SAAS,CAACoF,KAAK,CAAC;QAC1B,IAAIrF,KAAK,KAAK,IAAI,EAAE;UAClB,IAAI,EAAEE,IAAI,CAACC,GAAG,CAACH,KAAK,CAAC,IAAIhB,SAAS,CAACoB,SAAS,IAAIF,IAAI,CAACC,GAAG,CAACH,KAAK,GAAGE,IAAI,CAACG,EAAE,CAAC,IAAIrB,SAAS,CAACoB,SAAS,CAAC,EAAE;YAAE,OAAO,IAAI;UAAE;QACpH;MACF;MACAkF,MAAM,GAAGA,MAAM,CAACxE,GAAG,CAACF,CAAC,CAAC;MACtByE,KAAK,GAAGzE,CAAC;IACX;EACF;EACA;EACAF,CAAC,GAAGyE,IAAI,CAAC,CAAC,CAAC,CAACrF,QAAQ;EAAEa,CAAC,GAAGwE,IAAI,CAAC,CAAC,CAAC,CAACrF,QAAQ;EAAE2C,CAAC,GAAG0C,IAAI,CAACC,CAAC,GAAC,CAAC,CAAC,CAACtF,QAAQ;EAAEmB,CAAC,GAAGkE,IAAI,CAACC,CAAC,GAAC,CAAC,CAAC,CAACtF,QAAQ;EAC1FwF,MAAM,GAAGA,MAAM,CAACxE,GAAG,CAAClC,MAAM,CAACa,MAAM,CAAC,CAChC,CAACiB,CAAC,CAAC,CAAC,CAAC,GAAGC,CAAC,CAAC,CAAC,CAAC,KAAK8B,CAAC,CAAC,CAAC,CAAC,GAAG9B,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAACD,CAAC,CAAC,CAAC,CAAC,GAAGC,CAAC,CAAC,CAAC,CAAC,KAAK8B,CAAC,CAAC,CAAC,CAAC,GAAG9B,CAAC,CAAC,CAAC,CAAC,CAAC,EAC7D,CAACD,CAAC,CAAC,CAAC,CAAC,GAAGC,CAAC,CAAC,CAAC,CAAC,KAAK8B,CAAC,CAAC,CAAC,CAAC,GAAG9B,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAACD,CAAC,CAAC,CAAC,CAAC,GAAGC,CAAC,CAAC,CAAC,CAAC,KAAK8B,CAAC,CAAC,CAAC,CAAC,GAAG9B,CAAC,CAAC,CAAC,CAAC,CAAC,EAC7D,CAACD,CAAC,CAAC,CAAC,CAAC,GAAGC,CAAC,CAAC,CAAC,CAAC,KAAK8B,CAAC,CAAC,CAAC,CAAC,GAAG9B,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAACD,CAAC,CAAC,CAAC,CAAC,GAAGC,CAAC,CAAC,CAAC,CAAC,KAAK8B,CAAC,CAAC,CAAC,CAAC,GAAG9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAC9D,CAAC,CAACS,YAAY,CAAC,CAAC,CAAC,CAACN,GAAG,CAAClC,MAAM,CAACa,MAAM,CAAC,CACnC,CAACkB,CAAC,CAAC,CAAC,CAAC,GAAG8B,CAAC,CAAC,CAAC,CAAC,KAAKxB,CAAC,CAAC,CAAC,CAAC,GAAGwB,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC9B,CAAC,CAAC,CAAC,CAAC,GAAG8B,CAAC,CAAC,CAAC,CAAC,KAAKxB,CAAC,CAAC,CAAC,CAAC,GAAGwB,CAAC,CAAC,CAAC,CAAC,CAAC,EAC7D,CAAC9B,CAAC,CAAC,CAAC,CAAC,GAAG8B,CAAC,CAAC,CAAC,CAAC,KAAKxB,CAAC,CAAC,CAAC,CAAC,GAAGwB,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC9B,CAAC,CAAC,CAAC,CAAC,GAAG8B,CAAC,CAAC,CAAC,CAAC,KAAKxB,CAAC,CAAC,CAAC,CAAC,GAAGwB,CAAC,CAAC,CAAC,CAAC,CAAC,EAC7D,CAAC9B,CAAC,CAAC,CAAC,CAAC,GAAG8B,CAAC,CAAC,CAAC,CAAC,KAAKxB,CAAC,CAAC,CAAC,CAAC,GAAGwB,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC9B,CAAC,CAAC,CAAC,CAAC,GAAG8B,CAAC,CAAC,CAAC,CAAC,KAAKxB,CAAC,CAAC,CAAC,CAAC,GAAGwB,CAAC,CAAC,CAAC,CAAC,CAAC,CAC9D,CAAC,CAACrB,YAAY,CAAC,CAAC,CAAC;EAClB,OAAOnC,KAAK,CAACQ,MAAM,CAAC0F,IAAI,CAAC,CAAC,CAAC,EAAEG,MAAM,CAAC;AACtC,CAAC;AAEDC,MAAM,CAACC,OAAO,GAAGvG,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}