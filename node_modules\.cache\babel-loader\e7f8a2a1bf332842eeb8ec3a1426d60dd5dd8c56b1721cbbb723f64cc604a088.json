{"ast": null, "code": "/**\n * @license\n * Copyright 2018 Google LLC\n *\n * Use of this source code is governed by an MIT-style\n * license that can be found in the LICENSE file or at\n * https://opensource.org/licenses/MIT.\n * =============================================================================\n */\n// Layer activation functions\nimport * as tfc from '@tensorflow/tfjs-core';\nimport { serialization, tidy } from '@tensorflow/tfjs-core';\nimport * as K from './backend/tfjs_backend';\nimport { deserializeKerasObject } from './utils/generic_utils';\n/**\n * Base class for Activations.\n *\n * Special note: due to cross-language compatibility reasons, the\n * static readonly className field in this family of classes must be set to\n * the initialLowerCamelCase name of the activation.\n */\nexport class Activation extends serialization.Serializable {\n  getConfig() {\n    return {};\n  }\n}\n/**\n * Exponential linear unit (ELU).\n * Reference: https://arxiv.org/abs/1511.07289\n */\nclass Elu extends Activation {\n  /**\n   * Calculate the activation function.\n   *\n   * @param x: Input.\n   * @param alpha: Scaling factor the negative section.\n   * @return Output of the ELU activation.\n   */\n  apply(x) {\n    let alpha = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 1;\n    return K.elu(x, alpha);\n  }\n}\n/** @nocollapse */\nElu.className = 'elu';\nexport { Elu };\nserialization.registerClass(Elu);\n/**\n * Scaled Exponential Linear Unit. (Klambauer et al., 2017).\n * Reference: Self-Normalizing Neural Networks, https://arxiv.org/abs/1706.02515\n * Notes:\n *   - To be used together with the initialization \"lecunNormal\".\n *   - To be used together with the dropout variant \"AlphaDropout\".\n */\nclass Selu extends Activation {\n  apply(x) {\n    return tfc.selu(x);\n  }\n}\n/** @nocollapse */\nSelu.className = 'selu';\nexport { Selu };\nserialization.registerClass(Selu);\n/**\n *  Rectified linear unit\n */\nclass Relu extends Activation {\n  apply(x) {\n    return tfc.relu(x);\n  }\n}\n/** @nocollapse */\nRelu.className = 'relu';\nexport { Relu };\nserialization.registerClass(Relu);\n/**\n * Rectified linear unit activation maxing out at 6.0.\n */\nclass Relu6 extends Activation {\n  apply(x) {\n    return tidy(() => tfc.minimum(6.0, tfc.relu(x)));\n  }\n}\n/** @nocollapse */\nRelu6.className = 'relu6';\nexport { Relu6 };\nserialization.registerClass(Relu6);\n//* Linear activation (no-op) */\nclass Linear extends Activation {\n  apply(x) {\n    return x;\n  }\n}\n/** @nocollapse */\nLinear.className = 'linear';\nexport { Linear };\nserialization.registerClass(Linear);\n/**\n * Sigmoid activation function.\n */\nclass Sigmoid extends Activation {\n  apply(x) {\n    return tfc.sigmoid(x);\n  }\n}\n/** @nocollapse */\nSigmoid.className = 'sigmoid';\nexport { Sigmoid };\nserialization.registerClass(Sigmoid);\n/**\n * Segment-wise linear approximation of sigmoid.\n */\nclass HardSigmoid extends Activation {\n  apply(x) {\n    return K.hardSigmoid(x);\n  }\n}\n/** @nocollapse */\nHardSigmoid.className = 'hardSigmoid';\nexport { HardSigmoid };\nserialization.registerClass(HardSigmoid);\n/**\n * Softplus activation function.\n */\nclass Softplus extends Activation {\n  apply(x) {\n    return tfc.softplus(x);\n  }\n}\n/** @nocollapse */\nSoftplus.className = 'softplus';\nexport { Softplus };\nserialization.registerClass(Softplus);\n/**\n * Softsign activation function.\n */\nclass Softsign extends Activation {\n  apply(x) {\n    return K.softsign(x);\n  }\n}\n/** @nocollapse */\nSoftsign.className = 'softsign';\nexport { Softsign };\nserialization.registerClass(Softsign);\n/**\n * Hyperbolic tangent function.\n */\nclass Tanh extends Activation {\n  apply(x) {\n    return tfc.tanh(x);\n  }\n}\n/** @nocollapse */\nTanh.className = 'tanh';\nexport { Tanh };\nserialization.registerClass(Tanh);\n/**\n * Softmax activation function\n */\nclass Softmax extends Activation {\n  /**\n   * Calculate the activation function.\n   *\n   * @param x Tensor.\n   * @param axis Integer, axis along which the softmax normalization is applied.\n   * Invalid if < 2, as softmax across 1 (the batch dimension) is assumed to be\n   * an error.\n   *\n   * @returns a Tensor of the same shape as x\n   *\n   * @throws ValueError: In case `dim(x) < 2`.\n   */\n  apply(x) {\n    let axis = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : -1;\n    return tfc.softmax(x, axis);\n  }\n}\n/** @nocollapse */\nSoftmax.className = 'softmax';\nexport { Softmax };\nserialization.registerClass(Softmax);\n/**\n * Log softmax activation function\n */\nclass LogSoftmax extends Activation {\n  /**\n   * Calculate the activation function of log softmax:\n   * log( exp(x_i) / sum(exp(x)) )\n   *\n   * @param x Tensor.\n   * @param axis Integer, axis along which the softmax normalization is applied.\n   * Invalid if < 2, as softmax across 1 (the batch dimension) is assumed to be\n   * an error.\n   *\n   * @returns a Tensor of the same shape as x\n   *\n   * @throws ValueError: In case `dim(x) < 2`.\n   */\n  apply(x) {\n    let axis = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : -1;\n    return tfc.logSoftmax(x, axis);\n  }\n}\n/** @nocollapse */\nLogSoftmax.className = 'logSoftmax';\nexport { LogSoftmax };\nserialization.registerClass(LogSoftmax);\n/**\n * Gelu activation function\n */\nclass Gelu extends Activation {\n  /**\n   * Calculate the activation function.\n   *\n   * @param x Tensor.\n   * @returns a Tensor of the same shape as x\n   */\n  apply(x) {\n    return tidy(() => {\n      return tfc.tidy(() => {\n        const sqrtTwo = Math.sqrt(2);\n        // Compute Φ(x) using the erf function\n        const cdf = tfc.mul(0.5, tfc.add(1, tfc.erf(tfc.div(x, sqrtTwo))));\n        // Compute GELU(x) = x * Φ(x)\n        return tfc.mul(x, cdf);\n      });\n    });\n  }\n}\n/** @nocollapse */\nGelu.className = 'gelu';\nexport { Gelu };\nserialization.registerClass(Gelu);\n/**\n * GeluNew activation function\n */\nclass GeluNew extends Activation {\n  /**\n   * Calculate the activation function.\n   *\n   * @param x Tensor.\n   * @returns a Tensor of the same shape as x\n   */\n  apply(x) {\n    return tidy(() => {\n      return tfc.mul(0.5, tfc.mul(x, tfc.add(1, tfc.tanh(tfc.mul(tfc.sqrt(tfc.div(2, Math.PI)), tfc.add(x, tfc.mul(0.044715, tfc.pow(x, 3))))))));\n    });\n  }\n}\n/** @nocollapse */\nGeluNew.className = 'gelu_new';\nexport { GeluNew };\nserialization.registerClass(GeluNew);\n/**\n * Mish activation function\n */\nclass Mish extends Activation {\n  /**\n   * Calculate the activation function.\n   *\n   * @param x Tensor.\n   * @returns a Tensor of the same shape as x\n   */\n  apply(x) {\n    return tidy(() => tfc.mul(x, tfc.tanh(tfc.softplus(x))));\n  }\n}\n/** @nocollapse */\nMish.className = 'mish';\nexport { Mish };\nserialization.registerClass(Mish);\n/**\n * Swish activation function\n */\nclass Swish extends Activation {\n  /**\n   * Calculate the activation function.\n   *\n   * @param x Tensor.\n   * @param alpha Scaling factor for the sigmoid function.\n   * @returns a Tensor of the same shape as x\n   */\n  apply(x) {\n    let alpha = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 1;\n    return tidy(() => tfc.mul(tfc.sigmoid(tfc.mul(x, alpha)), x));\n  }\n}\n/** @nocollapse */\nSwish.className = 'swish';\nexport { Swish };\nserialization.registerClass(Swish);\nexport function serializeActivation(activation) {\n  return activation.getClassName();\n}\nexport function deserializeActivation(config) {\n  let customObjects = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n  return deserializeKerasObject(config, serialization.SerializationMap.getMap().classNameMap, customObjects, 'activation');\n}\nexport function getActivation(identifier) {\n  if (identifier == null) {\n    const config = {};\n    config['className'] = 'linear';\n    config['config'] = {};\n    return deserializeActivation(config);\n  }\n  if (typeof identifier === 'string') {\n    const config = {};\n    config['className'] = identifier;\n    config['config'] = {};\n    return deserializeActivation(config);\n  } else if (identifier instanceof Activation) {\n    return identifier;\n  } else {\n    return deserializeActivation(identifier);\n  }\n}", "map": {"version": 3, "names": ["tfc", "serialization", "tidy", "K", "deserializeKerasObject", "Activation", "Serializable", "getConfig", "<PERSON><PERSON>", "apply", "x", "alpha", "arguments", "length", "undefined", "elu", "className", "registerClass", "<PERSON><PERSON>", "selu", "<PERSON><PERSON>", "relu", "Relu6", "minimum", "Linear", "<PERSON><PERSON><PERSON><PERSON>", "sigmoid", "HardSigmoid", "hardSigmoid", "Softplus", "softplus", "Softsign", "softsign", "<PERSON><PERSON>", "tanh", "Softmax", "axis", "softmax", "LogSoftmax", "logSoftmax", "G<PERSON>u", "sqrtTwo", "Math", "sqrt", "cdf", "mul", "add", "erf", "div", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PI", "pow", "<PERSON>sh", "Swish", "serializeActivation", "activation", "getClassName", "deserializeActivation", "config", "customObjects", "SerializationMap", "getMap", "classNameMap", "getActivation", "identifier"], "sources": ["C:\\tfjs-layers\\src\\activations.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2018 Google LLC\n *\n * Use of this source code is governed by an MIT-style\n * license that can be found in the LICENSE file or at\n * https://opensource.org/licenses/MIT.\n * =============================================================================\n */\n\n// Layer activation functions\nimport * as tfc from '@tensorflow/tfjs-core';\nimport {serialization, Tensor, tidy} from '@tensorflow/tfjs-core';\nimport * as K from './backend/tfjs_backend';\nimport {ActivationIdentifier} from './keras_format/activation_config';\nimport {deserializeKerasObject} from './utils/generic_utils';\n\n/**\n * Base class for Activations.\n *\n * Special note: due to cross-language compatibility reasons, the\n * static readonly className field in this family of classes must be set to\n * the initialLowerCamelCase name of the activation.\n */\nexport abstract class Activation extends serialization.Serializable {\n  abstract apply(tensor: Tensor, axis?: number): Tensor;\n  getConfig(): serialization.ConfigDict {\n    return {};\n  }\n}\n\n/**\n * Exponential linear unit (ELU).\n * Reference: https://arxiv.org/abs/1511.07289\n */\nexport class Elu extends Activation {\n  /** @nocollapse */\n  static readonly className = 'elu';\n  /**\n   * Calculate the activation function.\n   *\n   * @param x: Input.\n   * @param alpha: Scaling factor the negative section.\n   * @return Output of the ELU activation.\n   */\n  apply(x: Tensor, alpha = 1): Tensor {\n    return K.elu(x, alpha);\n  }\n}\nserialization.registerClass(Elu);\n\n/**\n * Scaled Exponential Linear Unit. (Klambauer et al., 2017).\n * Reference: Self-Normalizing Neural Networks, https://arxiv.org/abs/1706.02515\n * Notes:\n *   - To be used together with the initialization \"lecunNormal\".\n *   - To be used together with the dropout variant \"AlphaDropout\".\n */\nexport class Selu extends Activation {\n  /** @nocollapse */\n  static readonly className = 'selu';\n  apply(x: Tensor): Tensor {\n    return tfc.selu(x);\n  }\n}\nserialization.registerClass(Selu);\n\n/**\n *  Rectified linear unit\n */\nexport class Relu extends Activation {\n  /** @nocollapse */\n  static readonly className = 'relu';\n  apply(x: Tensor): Tensor {\n    return tfc.relu(x);\n  }\n}\nserialization.registerClass(Relu);\n\n/**\n * Rectified linear unit activation maxing out at 6.0.\n */\nexport class Relu6 extends Activation {\n  /** @nocollapse */\n  static readonly className = 'relu6';\n  apply(x: Tensor): Tensor {\n    return tidy(() => tfc.minimum(6.0, tfc.relu(x)));\n  }\n}\nserialization.registerClass(Relu6);\n\n//* Linear activation (no-op) */\nexport class Linear extends Activation {\n  /** @nocollapse */\n  static readonly className = 'linear';\n  apply(x: Tensor): Tensor {\n    return x;\n  }\n}\nserialization.registerClass(Linear);\n\n/**\n * Sigmoid activation function.\n */\nexport class Sigmoid extends Activation {\n  /** @nocollapse */\n  static readonly className = 'sigmoid';\n  apply(x: Tensor): Tensor {\n    return tfc.sigmoid(x);\n  }\n}\nserialization.registerClass(Sigmoid);\n\n/**\n * Segment-wise linear approximation of sigmoid.\n */\nexport class HardSigmoid extends Activation {\n  /** @nocollapse */\n  static readonly className = 'hardSigmoid';\n  apply(x: Tensor): Tensor {\n    return K.hardSigmoid(x);\n  }\n}\nserialization.registerClass(HardSigmoid);\n\n/**\n * Softplus activation function.\n */\nexport class Softplus extends Activation {\n  /** @nocollapse */\n  static readonly className = 'softplus';\n  apply(x: Tensor): Tensor {\n    return tfc.softplus(x);\n  }\n}\nserialization.registerClass(Softplus);\n\n/**\n * Softsign activation function.\n */\nexport class Softsign extends Activation {\n  /** @nocollapse */\n  static readonly className = 'softsign';\n  apply(x: Tensor): Tensor {\n    return K.softsign(x);\n  }\n}\nserialization.registerClass(Softsign);\n\n/**\n * Hyperbolic tangent function.\n */\nexport class Tanh extends Activation {\n  /** @nocollapse */\n  static readonly className = 'tanh';\n  apply(x: Tensor): Tensor {\n    return tfc.tanh(x);\n  }\n}\nserialization.registerClass(Tanh);\n\n/**\n * Softmax activation function\n */\nexport class Softmax extends Activation {\n  /** @nocollapse */\n  static readonly className = 'softmax';\n  /**\n   * Calculate the activation function.\n   *\n   * @param x Tensor.\n   * @param axis Integer, axis along which the softmax normalization is applied.\n   * Invalid if < 2, as softmax across 1 (the batch dimension) is assumed to be\n   * an error.\n   *\n   * @returns a Tensor of the same shape as x\n   *\n   * @throws ValueError: In case `dim(x) < 2`.\n   */\n  apply(x: Tensor, axis: number = (-1)): Tensor {\n    return tfc.softmax(x, axis);\n  }\n}\nserialization.registerClass(Softmax);\n\n/**\n * Log softmax activation function\n */\nexport class LogSoftmax extends Activation {\n  /** @nocollapse */\n  static readonly className = 'logSoftmax';\n  /**\n   * Calculate the activation function of log softmax:\n   * log( exp(x_i) / sum(exp(x)) )\n   *\n   * @param x Tensor.\n   * @param axis Integer, axis along which the softmax normalization is applied.\n   * Invalid if < 2, as softmax across 1 (the batch dimension) is assumed to be\n   * an error.\n   *\n   * @returns a Tensor of the same shape as x\n   *\n   * @throws ValueError: In case `dim(x) < 2`.\n   */\n  apply(x: Tensor, axis: number = (-1)): Tensor {\n    return tfc.logSoftmax(x, axis);\n  }\n}\nserialization.registerClass(LogSoftmax);\n\n/**\n * Gelu activation function\n */\nexport class Gelu extends Activation {\n  /** @nocollapse */\n  static readonly className = 'gelu';\n  /**\n   * Calculate the activation function.\n   *\n   * @param x Tensor.\n   * @returns a Tensor of the same shape as x\n   */\n  apply(x: Tensor): Tensor {\n    return tidy(() => {\n      return tfc.tidy(() => {\n        const sqrtTwo = Math.sqrt(2);\n        // Compute Φ(x) using the erf function\n        const cdf = tfc.mul(0.5, tfc.add(1, tfc.erf(tfc.div(x, sqrtTwo))));\n        // Compute GELU(x) = x * Φ(x)\n        return tfc.mul(x, cdf);\n      });\n    });\n  }\n}\nserialization.registerClass(Gelu);\n\n/**\n * GeluNew activation function\n */\nexport class GeluNew extends Activation {\n  /** @nocollapse */\n  static readonly className = 'gelu_new';\n  /**\n   * Calculate the activation function.\n   *\n   * @param x Tensor.\n   * @returns a Tensor of the same shape as x\n   */\n  apply(x: Tensor): Tensor {\n    return tidy(() => {\n      return tfc.mul(\n        0.5,\n        tfc.mul(\n          x,\n          tfc.add(\n              1,\n              tfc.tanh(\n                tfc.mul(\n                  tfc.sqrt(tfc.div(2, Math.PI)),\n                  tfc.add(x, tfc.mul(0.044715, tfc.pow(x, 3)))\n                  )\n              )\n          )\n        )\n      );\n    });\n  }\n}\nserialization.registerClass(GeluNew);\n\n/**\n * Mish activation function\n */\nexport class Mish extends Activation {\n  /** @nocollapse */\n  static readonly className = 'mish';\n  /**\n   * Calculate the activation function.\n   *\n   * @param x Tensor.\n   * @returns a Tensor of the same shape as x\n   */\n  apply(x: Tensor): Tensor {\n    return tidy(() => tfc.mul(x, tfc.tanh(tfc.softplus(x))));\n  }\n}\nserialization.registerClass(Mish);\n\n/**\n * Swish activation function\n */\nexport class Swish extends Activation {\n  /** @nocollapse */\n  static readonly className = 'swish';\n  /**\n   * Calculate the activation function.\n   *\n   * @param x Tensor.\n   * @param alpha Scaling factor for the sigmoid function.\n   * @returns a Tensor of the same shape as x\n   */\n  apply(x: Tensor, alpha = 1): Tensor {\n    return tidy(() => tfc.mul(tfc.sigmoid(tfc.mul(x, alpha)), x));\n  }\n}\nserialization.registerClass(Swish);\n\nexport function serializeActivation(activation: Activation): string {\n  return activation.getClassName();\n}\n\nexport function deserializeActivation(\n    config: serialization.ConfigDict,\n    customObjects: serialization.ConfigDict = {}): Activation {\n  return deserializeKerasObject(\n      config, serialization.SerializationMap.getMap().classNameMap,\n      customObjects, 'activation');\n}\n\nexport function getActivation(identifier: ActivationIdentifier|\n                              serialization.ConfigDict|Activation): Activation {\n  if (identifier == null) {\n    const config: serialization.ConfigDict = {};\n    config['className'] = 'linear';\n    config['config'] = {};\n    return deserializeActivation(config);\n  }\n  if (typeof identifier === 'string') {\n    const config: serialization.ConfigDict = {};\n    config['className'] = identifier;\n    config['config'] = {};\n    return deserializeActivation(config);\n  } else if (identifier instanceof Activation) {\n    return identifier;\n  } else {\n    return deserializeActivation(identifier);\n  }\n}\n"], "mappings": "AAAA;;;;;;;;;AAUA;AACA,OAAO,KAAKA,GAAG,MAAM,uBAAuB;AAC5C,SAAQC,aAAa,EAAUC,IAAI,QAAO,uBAAuB;AACjE,OAAO,KAAKC,CAAC,MAAM,wBAAwB;AAE3C,SAAQC,sBAAsB,QAAO,uBAAuB;AAE5D;;;;;;;AAOA,OAAM,MAAgBC,UAAW,SAAQJ,aAAa,CAACK,YAAY;EAEjEC,SAASA,CAAA;IACP,OAAO,EAAE;EACX;;AAGF;;;;AAIA,MAAaC,GAAI,SAAQH,UAAU;EAGjC;;;;;;;EAOAI,KAAKA,CAACC,CAAS,EAAW;IAAA,IAATC,KAAK,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC;IACxB,OAAOT,CAAC,CAACY,GAAG,CAACL,CAAC,EAAEC,KAAK,CAAC;EACxB;;AAXA;AACgBH,GAAA,CAAAQ,SAAS,GAAG,KAAK;SAFtBR,GAAG;AAchBP,aAAa,CAACgB,aAAa,CAACT,GAAG,CAAC;AAEhC;;;;;;;AAOA,MAAaU,IAAK,SAAQb,UAAU;EAGlCI,KAAKA,CAACC,CAAS;IACb,OAAOV,GAAG,CAACmB,IAAI,CAACT,CAAC,CAAC;EACpB;;AAJA;AACgBQ,IAAA,CAAAF,SAAS,GAAG,MAAM;SAFvBE,IAAI;AAOjBjB,aAAa,CAACgB,aAAa,CAACC,IAAI,CAAC;AAEjC;;;AAGA,MAAaE,IAAK,SAAQf,UAAU;EAGlCI,KAAKA,CAACC,CAAS;IACb,OAAOV,GAAG,CAACqB,IAAI,CAACX,CAAC,CAAC;EACpB;;AAJA;AACgBU,IAAA,CAAAJ,SAAS,GAAG,MAAM;SAFvBI,IAAI;AAOjBnB,aAAa,CAACgB,aAAa,CAACG,IAAI,CAAC;AAEjC;;;AAGA,MAAaE,KAAM,SAAQjB,UAAU;EAGnCI,KAAKA,CAACC,CAAS;IACb,OAAOR,IAAI,CAAC,MAAMF,GAAG,CAACuB,OAAO,CAAC,GAAG,EAAEvB,GAAG,CAACqB,IAAI,CAACX,CAAC,CAAC,CAAC,CAAC;EAClD;;AAJA;AACgBY,KAAA,CAAAN,SAAS,GAAG,OAAO;SAFxBM,KAAK;AAOlBrB,aAAa,CAACgB,aAAa,CAACK,KAAK,CAAC;AAElC;AACA,MAAaE,MAAO,SAAQnB,UAAU;EAGpCI,KAAKA,CAACC,CAAS;IACb,OAAOA,CAAC;EACV;;AAJA;AACgBc,MAAA,CAAAR,SAAS,GAAG,QAAQ;SAFzBQ,MAAM;AAOnBvB,aAAa,CAACgB,aAAa,CAACO,MAAM,CAAC;AAEnC;;;AAGA,MAAaC,OAAQ,SAAQpB,UAAU;EAGrCI,KAAKA,CAACC,CAAS;IACb,OAAOV,GAAG,CAAC0B,OAAO,CAAChB,CAAC,CAAC;EACvB;;AAJA;AACgBe,OAAA,CAAAT,SAAS,GAAG,SAAS;SAF1BS,OAAO;AAOpBxB,aAAa,CAACgB,aAAa,CAACQ,OAAO,CAAC;AAEpC;;;AAGA,MAAaE,WAAY,SAAQtB,UAAU;EAGzCI,KAAKA,CAACC,CAAS;IACb,OAAOP,CAAC,CAACyB,WAAW,CAAClB,CAAC,CAAC;EACzB;;AAJA;AACgBiB,WAAA,CAAAX,SAAS,GAAG,aAAa;SAF9BW,WAAW;AAOxB1B,aAAa,CAACgB,aAAa,CAACU,WAAW,CAAC;AAExC;;;AAGA,MAAaE,QAAS,SAAQxB,UAAU;EAGtCI,KAAKA,CAACC,CAAS;IACb,OAAOV,GAAG,CAAC8B,QAAQ,CAACpB,CAAC,CAAC;EACxB;;AAJA;AACgBmB,QAAA,CAAAb,SAAS,GAAG,UAAU;SAF3Ba,QAAQ;AAOrB5B,aAAa,CAACgB,aAAa,CAACY,QAAQ,CAAC;AAErC;;;AAGA,MAAaE,QAAS,SAAQ1B,UAAU;EAGtCI,KAAKA,CAACC,CAAS;IACb,OAAOP,CAAC,CAAC6B,QAAQ,CAACtB,CAAC,CAAC;EACtB;;AAJA;AACgBqB,QAAA,CAAAf,SAAS,GAAG,UAAU;SAF3Be,QAAQ;AAOrB9B,aAAa,CAACgB,aAAa,CAACc,QAAQ,CAAC;AAErC;;;AAGA,MAAaE,IAAK,SAAQ5B,UAAU;EAGlCI,KAAKA,CAACC,CAAS;IACb,OAAOV,GAAG,CAACkC,IAAI,CAACxB,CAAC,CAAC;EACpB;;AAJA;AACgBuB,IAAA,CAAAjB,SAAS,GAAG,MAAM;SAFvBiB,IAAI;AAOjBhC,aAAa,CAACgB,aAAa,CAACgB,IAAI,CAAC;AAEjC;;;AAGA,MAAaE,OAAQ,SAAQ9B,UAAU;EAGrC;;;;;;;;;;;;EAYAI,KAAKA,CAACC,CAAS,EAAqB;IAAA,IAAnB0B,IAAA,GAAAxB,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAgB,CAAC,CAAC;IACjC,OAAOZ,GAAG,CAACqC,OAAO,CAAC3B,CAAC,EAAE0B,IAAI,CAAC;EAC7B;;AAhBA;AACgBD,OAAA,CAAAnB,SAAS,GAAG,SAAS;SAF1BmB,OAAO;AAmBpBlC,aAAa,CAACgB,aAAa,CAACkB,OAAO,CAAC;AAEpC;;;AAGA,MAAaG,UAAW,SAAQjC,UAAU;EAGxC;;;;;;;;;;;;;EAaAI,KAAKA,CAACC,CAAS,EAAqB;IAAA,IAAnB0B,IAAA,GAAAxB,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAgB,CAAC,CAAC;IACjC,OAAOZ,GAAG,CAACuC,UAAU,CAAC7B,CAAC,EAAE0B,IAAI,CAAC;EAChC;;AAjBA;AACgBE,UAAA,CAAAtB,SAAS,GAAG,YAAY;SAF7BsB,UAAU;AAoBvBrC,aAAa,CAACgB,aAAa,CAACqB,UAAU,CAAC;AAEvC;;;AAGA,MAAaE,IAAK,SAAQnC,UAAU;EAGlC;;;;;;EAMAI,KAAKA,CAACC,CAAS;IACb,OAAOR,IAAI,CAAC,MAAK;MACf,OAAOF,GAAG,CAACE,IAAI,CAAC,MAAK;QACnB,MAAMuC,OAAO,GAAGC,IAAI,CAACC,IAAI,CAAC,CAAC,CAAC;QAC5B;QACA,MAAMC,GAAG,GAAG5C,GAAG,CAAC6C,GAAG,CAAC,GAAG,EAAE7C,GAAG,CAAC8C,GAAG,CAAC,CAAC,EAAE9C,GAAG,CAAC+C,GAAG,CAAC/C,GAAG,CAACgD,GAAG,CAACtC,CAAC,EAAE+B,OAAO,CAAC,CAAC,CAAC,CAAC;QAClE;QACA,OAAOzC,GAAG,CAAC6C,GAAG,CAACnC,CAAC,EAAEkC,GAAG,CAAC;MACxB,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ;;AAlBA;AACgBJ,IAAA,CAAAxB,SAAS,GAAG,MAAM;SAFvBwB,IAAI;AAqBjBvC,aAAa,CAACgB,aAAa,CAACuB,IAAI,CAAC;AAEjC;;;AAGA,MAAaS,OAAQ,SAAQ5C,UAAU;EAGrC;;;;;;EAMAI,KAAKA,CAACC,CAAS;IACb,OAAOR,IAAI,CAAC,MAAK;MACf,OAAOF,GAAG,CAAC6C,GAAG,CACZ,GAAG,EACH7C,GAAG,CAAC6C,GAAG,CACLnC,CAAC,EACDV,GAAG,CAAC8C,GAAG,CACH,CAAC,EACD9C,GAAG,CAACkC,IAAI,CACNlC,GAAG,CAAC6C,GAAG,CACL7C,GAAG,CAAC2C,IAAI,CAAC3C,GAAG,CAACgD,GAAG,CAAC,CAAC,EAAEN,IAAI,CAACQ,EAAE,CAAC,CAAC,EAC7BlD,GAAG,CAAC8C,GAAG,CAACpC,CAAC,EAAEV,GAAG,CAAC6C,GAAG,CAAC,QAAQ,EAAE7C,GAAG,CAACmD,GAAG,CAACzC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAC3C,CACJ,CACJ,CACF,CACF;IACH,CAAC,CAAC;EACJ;;AA1BA;AACgBuC,OAAA,CAAAjC,SAAS,GAAG,UAAU;SAF3BiC,OAAO;AA6BpBhD,aAAa,CAACgB,aAAa,CAACgC,OAAO,CAAC;AAEpC;;;AAGA,MAAaG,IAAK,SAAQ/C,UAAU;EAGlC;;;;;;EAMAI,KAAKA,CAACC,CAAS;IACb,OAAOR,IAAI,CAAC,MAAMF,GAAG,CAAC6C,GAAG,CAACnC,CAAC,EAAEV,GAAG,CAACkC,IAAI,CAAClC,GAAG,CAAC8B,QAAQ,CAACpB,CAAC,CAAC,CAAC,CAAC,CAAC;EAC1D;;AAVA;AACgB0C,IAAA,CAAApC,SAAS,GAAG,MAAM;SAFvBoC,IAAI;AAajBnD,aAAa,CAACgB,aAAa,CAACmC,IAAI,CAAC;AAEjC;;;AAGA,MAAaC,KAAM,SAAQhD,UAAU;EAGnC;;;;;;;EAOAI,KAAKA,CAACC,CAAS,EAAW;IAAA,IAATC,KAAK,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC;IACxB,OAAOV,IAAI,CAAC,MAAMF,GAAG,CAAC6C,GAAG,CAAC7C,GAAG,CAAC0B,OAAO,CAAC1B,GAAG,CAAC6C,GAAG,CAACnC,CAAC,EAAEC,KAAK,CAAC,CAAC,EAAED,CAAC,CAAC,CAAC;EAC/D;;AAXA;AACgB2C,KAAA,CAAArC,SAAS,GAAG,OAAO;SAFxBqC,KAAK;AAclBpD,aAAa,CAACgB,aAAa,CAACoC,KAAK,CAAC;AAElC,OAAM,SAAUC,mBAAmBA,CAACC,UAAsB;EACxD,OAAOA,UAAU,CAACC,YAAY,EAAE;AAClC;AAEA,OAAM,SAAUC,qBAAqBA,CACjCC,MAAgC,EACY;EAAA,IAA5CC,aAAA,GAAA/C,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAA0C,EAAE;EAC9C,OAAOR,sBAAsB,CACzBsD,MAAM,EAAEzD,aAAa,CAAC2D,gBAAgB,CAACC,MAAM,EAAE,CAACC,YAAY,EAC5DH,aAAa,EAAE,YAAY,CAAC;AAClC;AAEA,OAAM,SAAUI,aAAaA,CAACC,UACmC;EAC/D,IAAIA,UAAU,IAAI,IAAI,EAAE;IACtB,MAAMN,MAAM,GAA6B,EAAE;IAC3CA,MAAM,CAAC,WAAW,CAAC,GAAG,QAAQ;IAC9BA,MAAM,CAAC,QAAQ,CAAC,GAAG,EAAE;IACrB,OAAOD,qBAAqB,CAACC,MAAM,CAAC;;EAEtC,IAAI,OAAOM,UAAU,KAAK,QAAQ,EAAE;IAClC,MAAMN,MAAM,GAA6B,EAAE;IAC3CA,MAAM,CAAC,WAAW,CAAC,GAAGM,UAAU;IAChCN,MAAM,CAAC,QAAQ,CAAC,GAAG,EAAE;IACrB,OAAOD,qBAAqB,CAACC,MAAM,CAAC;GACrC,MAAM,IAAIM,UAAU,YAAY3D,UAAU,EAAE;IAC3C,OAAO2D,UAAU;GAClB,MAAM;IACL,OAAOP,qBAAqB,CAACO,UAAU,CAAC;;AAE5C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}