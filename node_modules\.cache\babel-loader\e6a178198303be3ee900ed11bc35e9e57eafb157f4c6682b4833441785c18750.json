{"ast": null, "code": "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { UnsortedSegmentSum, util } from '@tensorflow/tfjs-core';\nimport { assertNotComplex } from '../cpu_util';\nimport { cast } from './Cast';\nimport { equal } from './Equal';\nimport { expandDims } from './ExpandDims';\nimport { multiply } from './Multiply';\nimport { pack } from './Pack';\nimport { sum } from './Sum';\nexport function unsortedSegmentSum(args) {\n  const {\n    inputs,\n    backend,\n    attrs\n  } = args;\n  const {\n    x,\n    segmentIds\n  } = inputs;\n  const {\n    numSegments\n  } = attrs;\n  assertNotComplex(x, 'unsortedSegmentSum');\n  const xRank = x.shape.length;\n  const segmentIdsRank = segmentIds.shape.length;\n  const res = [];\n  const intermediates = [];\n  // Reshape the segment id's so that they can be broadcast with\n  // x. The new shape should be [segmentIds.shape, 1, ..., 1]\n  const numIters = xRank - segmentIdsRank;\n  let $segmentIds = segmentIds;\n  for (let i = 0; i < numIters; ++i) {\n    const expanded = expandDims({\n      inputs: {\n        input: $segmentIds\n      },\n      backend,\n      attrs: {\n        dim: i + 1\n      }\n    });\n    $segmentIds = expanded;\n    intermediates.push(expanded);\n  }\n  for (let i = 0; i < numSegments; ++i) {\n    const scalarValue = util.createScalarValue(i, 'int32');\n    const segmentId = backend.makeTensorInfo([], 'int32', scalarValue);\n    const mask = equal({\n      inputs: {\n        a: segmentId,\n        b: $segmentIds\n      },\n      backend\n    });\n    const maskCasted = cast({\n      inputs: {\n        x: mask\n      },\n      backend,\n      attrs: {\n        dtype: 'float32'\n      }\n    });\n    const mul = multiply({\n      inputs: {\n        a: maskCasted,\n        b: x\n      },\n      backend\n    });\n    const sumTensorInfo = sum({\n      inputs: {\n        x: mul\n      },\n      backend,\n      attrs: {\n        axis: 0,\n        keepDims: false\n      }\n    });\n    res.push(sumTensorInfo);\n    intermediates.push(segmentId);\n    intermediates.push(mask);\n    intermediates.push(maskCasted);\n    intermediates.push(mul);\n    intermediates.push(sumTensorInfo);\n  }\n  const result = pack({\n    inputs: res,\n    backend,\n    attrs: {\n      axis: 0\n    }\n  });\n  intermediates.forEach(t => backend.disposeIntermediateTensorInfo(t));\n  return result;\n}\nexport const unsortedSegmentSumConfig = {\n  kernelName: UnsortedSegmentSum,\n  backendName: 'cpu',\n  kernelFunc: unsortedSegmentSum\n};", "map": {"version": 3, "names": ["UnsortedSegmentSum", "util", "assertNotComplex", "cast", "equal", "expandDims", "multiply", "pack", "sum", "unsortedSegmentSum", "args", "inputs", "backend", "attrs", "x", "segmentIds", "numSegments", "xRank", "shape", "length", "segmentIdsRank", "res", "intermediates", "numIters", "$segmentIds", "i", "expanded", "input", "dim", "push", "scalarValue", "createScalarValue", "segmentId", "makeTensorInfo", "mask", "a", "b", "maskCasted", "dtype", "mul", "sumTensorInfo", "axis", "keepDims", "result", "for<PERSON>ach", "t", "disposeIntermediateTensorInfo", "unsortedSegmentSumConfig", "kernelName", "backendName", "kernelFunc"], "sources": ["C:\\tfjs-backend-cpu\\src\\kernels\\UnsortedSegmentSum.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {KernelConfig, KernelFunc, TensorInfo, UnsortedSegmentSum, UnsortedSegmentSumAttrs, UnsortedSegmentSumInputs, util} from '@tensorflow/tfjs-core';\n\nimport {MathBackendCPU} from '../backend_cpu';\nimport {assertNotComplex} from '../cpu_util';\nimport {cast} from './Cast';\nimport {equal} from './Equal';\nimport {expandDims} from './ExpandDims';\nimport {multiply} from './Multiply';\nimport {pack} from './Pack';\nimport {sum} from './Sum';\n\nexport function unsortedSegmentSum(args: {\n  inputs: UnsortedSegmentSumInputs,\n  backend: MathBackendCPU,\n  attrs: UnsortedSegmentSumAttrs\n}): TensorInfo {\n  const {inputs, backend, attrs} = args;\n  const {x, segmentIds} = inputs;\n  const {numSegments} = attrs;\n\n  assertNotComplex(x, 'unsortedSegmentSum');\n\n  const xRank = x.shape.length;\n  const segmentIdsRank = segmentIds.shape.length;\n  const res = [];\n  const intermediates: TensorInfo[] = [];\n\n  // Reshape the segment id's so that they can be broadcast with\n  // x. The new shape should be [segmentIds.shape, 1, ..., 1]\n  const numIters = xRank - segmentIdsRank;\n  let $segmentIds = segmentIds;\n\n  for (let i = 0; i < numIters; ++i) {\n    const expanded = expandDims(\n        {inputs: {input: $segmentIds}, backend, attrs: {dim: i + 1}});\n    $segmentIds = expanded;\n    intermediates.push(expanded);\n  }\n\n  for (let i = 0; i < numSegments; ++i) {\n    const scalarValue = util.createScalarValue(\n      i as unknown as 'int32', 'int32');\n    const segmentId = backend.makeTensorInfo([], 'int32', scalarValue);\n    const mask =\n        equal({inputs: {a: segmentId, b: $segmentIds}, backend}) as TensorInfo;\n    const maskCasted =\n        cast({inputs: {x: mask}, backend, attrs: {dtype: 'float32'}});\n    const mul =\n        multiply({inputs: {a: maskCasted, b: x}, backend}) as TensorInfo;\n    const sumTensorInfo =\n        sum({inputs: {x: mul}, backend, attrs: {axis: 0, keepDims: false}});\n    res.push(sumTensorInfo);\n    intermediates.push(segmentId);\n    intermediates.push(mask);\n    intermediates.push(maskCasted);\n    intermediates.push(mul);\n    intermediates.push(sumTensorInfo);\n  }\n\n  const result = pack({inputs: res, backend, attrs: {axis: 0}});\n\n  intermediates.forEach(t => backend.disposeIntermediateTensorInfo(t));\n\n  return result;\n}\n\nexport const unsortedSegmentSumConfig: KernelConfig = {\n  kernelName: UnsortedSegmentSum,\n  backendName: 'cpu',\n  kernelFunc: unsortedSegmentSum as unknown as KernelFunc\n};\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAiBA,SAA8CA,kBAAkB,EAAqDC,IAAI,QAAO,uBAAuB;AAGvJ,SAAQC,gBAAgB,QAAO,aAAa;AAC5C,SAAQC,IAAI,QAAO,QAAQ;AAC3B,SAAQC,KAAK,QAAO,SAAS;AAC7B,SAAQC,UAAU,QAAO,cAAc;AACvC,SAAQC,QAAQ,QAAO,YAAY;AACnC,SAAQC,IAAI,QAAO,QAAQ;AAC3B,SAAQC,GAAG,QAAO,OAAO;AAEzB,OAAM,SAAUC,kBAAkBA,CAACC,IAIlC;EACC,MAAM;IAACC,MAAM;IAAEC,OAAO;IAAEC;EAAK,CAAC,GAAGH,IAAI;EACrC,MAAM;IAACI,CAAC;IAAEC;EAAU,CAAC,GAAGJ,MAAM;EAC9B,MAAM;IAACK;EAAW,CAAC,GAAGH,KAAK;EAE3BX,gBAAgB,CAACY,CAAC,EAAE,oBAAoB,CAAC;EAEzC,MAAMG,KAAK,GAAGH,CAAC,CAACI,KAAK,CAACC,MAAM;EAC5B,MAAMC,cAAc,GAAGL,UAAU,CAACG,KAAK,CAACC,MAAM;EAC9C,MAAME,GAAG,GAAG,EAAE;EACd,MAAMC,aAAa,GAAiB,EAAE;EAEtC;EACA;EACA,MAAMC,QAAQ,GAAGN,KAAK,GAAGG,cAAc;EACvC,IAAII,WAAW,GAAGT,UAAU;EAE5B,KAAK,IAAIU,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,QAAQ,EAAE,EAAEE,CAAC,EAAE;IACjC,MAAMC,QAAQ,GAAGrB,UAAU,CACvB;MAACM,MAAM,EAAE;QAACgB,KAAK,EAAEH;MAAW,CAAC;MAAEZ,OAAO;MAAEC,KAAK,EAAE;QAACe,GAAG,EAAEH,CAAC,GAAG;MAAC;IAAC,CAAC,CAAC;IACjED,WAAW,GAAGE,QAAQ;IACtBJ,aAAa,CAACO,IAAI,CAACH,QAAQ,CAAC;;EAG9B,KAAK,IAAID,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGT,WAAW,EAAE,EAAES,CAAC,EAAE;IACpC,MAAMK,WAAW,GAAG7B,IAAI,CAAC8B,iBAAiB,CACxCN,CAAuB,EAAE,OAAO,CAAC;IACnC,MAAMO,SAAS,GAAGpB,OAAO,CAACqB,cAAc,CAAC,EAAE,EAAE,OAAO,EAAEH,WAAW,CAAC;IAClE,MAAMI,IAAI,GACN9B,KAAK,CAAC;MAACO,MAAM,EAAE;QAACwB,CAAC,EAAEH,SAAS;QAAEI,CAAC,EAAEZ;MAAW,CAAC;MAAEZ;IAAO,CAAC,CAAe;IAC1E,MAAMyB,UAAU,GACZlC,IAAI,CAAC;MAACQ,MAAM,EAAE;QAACG,CAAC,EAAEoB;MAAI,CAAC;MAAEtB,OAAO;MAAEC,KAAK,EAAE;QAACyB,KAAK,EAAE;MAAS;IAAC,CAAC,CAAC;IACjE,MAAMC,GAAG,GACLjC,QAAQ,CAAC;MAACK,MAAM,EAAE;QAACwB,CAAC,EAAEE,UAAU;QAAED,CAAC,EAAEtB;MAAC,CAAC;MAAEF;IAAO,CAAC,CAAe;IACpE,MAAM4B,aAAa,GACfhC,GAAG,CAAC;MAACG,MAAM,EAAE;QAACG,CAAC,EAAEyB;MAAG,CAAC;MAAE3B,OAAO;MAAEC,KAAK,EAAE;QAAC4B,IAAI,EAAE,CAAC;QAAEC,QAAQ,EAAE;MAAK;IAAC,CAAC,CAAC;IACvErB,GAAG,CAACQ,IAAI,CAACW,aAAa,CAAC;IACvBlB,aAAa,CAACO,IAAI,CAACG,SAAS,CAAC;IAC7BV,aAAa,CAACO,IAAI,CAACK,IAAI,CAAC;IACxBZ,aAAa,CAACO,IAAI,CAACQ,UAAU,CAAC;IAC9Bf,aAAa,CAACO,IAAI,CAACU,GAAG,CAAC;IACvBjB,aAAa,CAACO,IAAI,CAACW,aAAa,CAAC;;EAGnC,MAAMG,MAAM,GAAGpC,IAAI,CAAC;IAACI,MAAM,EAAEU,GAAG;IAAET,OAAO;IAAEC,KAAK,EAAE;MAAC4B,IAAI,EAAE;IAAC;EAAC,CAAC,CAAC;EAE7DnB,aAAa,CAACsB,OAAO,CAACC,CAAC,IAAIjC,OAAO,CAACkC,6BAA6B,CAACD,CAAC,CAAC,CAAC;EAEpE,OAAOF,MAAM;AACf;AAEA,OAAO,MAAMI,wBAAwB,GAAiB;EACpDC,UAAU,EAAEhD,kBAAkB;EAC9BiD,WAAW,EAAE,KAAK;EAClBC,UAAU,EAAEzC;CACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}