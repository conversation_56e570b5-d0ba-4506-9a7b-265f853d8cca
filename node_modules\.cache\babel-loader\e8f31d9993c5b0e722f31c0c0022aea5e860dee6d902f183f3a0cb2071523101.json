{"ast": null, "code": "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { ENGINE } from '../engine';\nimport { SquaredDifference } from '../kernel_names';\nimport { makeTypesMatch } from '../tensor_util';\nimport { convertToTensor } from '../tensor_util_env';\nimport { assertAndGetBroadcastShape } from './broadcast_util';\nimport { op } from './operation';\n/**\n * Returns (a - b) * (a - b) element-wise.\n * Supports broadcasting.\n *\n * ```js\n * const a = tf.tensor1d([1, 4, 3, 16]);\n * const b = tf.tensor1d([1, 2, 9, 4]);\n *\n * a.squaredDifference(b).print();  // or tf.squaredDifference(a, b)\n * ```\n *\n * ```js\n * // Broadcast squared difference  a with b.\n * const a = tf.tensor1d([2, 4, 6, 8]);\n * const b = tf.scalar(5);\n *\n * a.squaredDifference(b).print();  // or tf.squaredDifference(a, b)\n * ```\n *\n * @param a The first tensor.\n * @param b The second tensor. Must have the same type as `a`.\n *\n * @doc {heading: 'Operations', subheading: 'Arithmetic'}\n */\nfunction squaredDifference_(a, b) {\n  let $a = convertToTensor(a, 'a', 'squaredDifference');\n  let $b = convertToTensor(b, 'b', 'squaredDifference');\n  [$a, $b] = makeTypesMatch($a, $b);\n  assertAndGetBroadcastShape($a.shape, $b.shape);\n  const inputs = {\n    a: $a,\n    b: $b\n  };\n  const attrs = {};\n  return ENGINE.runKernel(SquaredDifference, inputs, attrs);\n}\nexport const squaredDifference = /* @__PURE__ */op({\n  squaredDifference_\n});", "map": {"version": 3, "names": ["ENGINE", "SquaredDifference", "makeTypesMatch", "convertToTensor", "assertAndGetBroadcastShape", "op", "squaredDifference_", "a", "b", "$a", "$b", "shape", "inputs", "attrs", "runKernel", "squaredDifference"], "sources": ["C:\\tfjs-core\\src\\ops\\squared_difference.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {ENGINE} from '../engine';\nimport {SquaredDifference, SquaredDifferenceInputs} from '../kernel_names';\nimport {Tensor} from '../tensor';\nimport {NamedTensorMap} from '../tensor_types';\nimport {makeTypesMatch} from '../tensor_util';\nimport {convertToTensor} from '../tensor_util_env';\nimport {TensorLike} from '../types';\n\nimport {assertAndGetBroadcastShape} from './broadcast_util';\nimport {op} from './operation';\n\n/**\n * Returns (a - b) * (a - b) element-wise.\n * Supports broadcasting.\n *\n * ```js\n * const a = tf.tensor1d([1, 4, 3, 16]);\n * const b = tf.tensor1d([1, 2, 9, 4]);\n *\n * a.squaredDifference(b).print();  // or tf.squaredDifference(a, b)\n * ```\n *\n * ```js\n * // Broadcast squared difference  a with b.\n * const a = tf.tensor1d([2, 4, 6, 8]);\n * const b = tf.scalar(5);\n *\n * a.squaredDifference(b).print();  // or tf.squaredDifference(a, b)\n * ```\n *\n * @param a The first tensor.\n * @param b The second tensor. Must have the same type as `a`.\n *\n * @doc {heading: 'Operations', subheading: 'Arithmetic'}\n */\nfunction squaredDifference_<T extends Tensor>(\n    a: Tensor|TensorLike, b: Tensor|TensorLike): T {\n  let $a = convertToTensor(a, 'a', 'squaredDifference');\n  let $b = convertToTensor(b, 'b', 'squaredDifference');\n  [$a, $b] = makeTypesMatch($a, $b);\n\n  assertAndGetBroadcastShape($a.shape, $b.shape);\n\n  const inputs: SquaredDifferenceInputs = {a: $a, b: $b};\n  const attrs = {};\n\n  return ENGINE.runKernel(\n      SquaredDifference, inputs as unknown as NamedTensorMap, attrs);\n}\n\nexport const squaredDifference = /* @__PURE__ */ op({squaredDifference_});\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAiBA,SAAQA,MAAM,QAAO,WAAW;AAChC,SAAQC,iBAAiB,QAAgC,iBAAiB;AAG1E,SAAQC,cAAc,QAAO,gBAAgB;AAC7C,SAAQC,eAAe,QAAO,oBAAoB;AAGlD,SAAQC,0BAA0B,QAAO,kBAAkB;AAC3D,SAAQC,EAAE,QAAO,aAAa;AAE9B;;;;;;;;;;;;;;;;;;;;;;;;AAwBA,SAASC,kBAAkBA,CACvBC,CAAoB,EAAEC,CAAoB;EAC5C,IAAIC,EAAE,GAAGN,eAAe,CAACI,CAAC,EAAE,GAAG,EAAE,mBAAmB,CAAC;EACrD,IAAIG,EAAE,GAAGP,eAAe,CAACK,CAAC,EAAE,GAAG,EAAE,mBAAmB,CAAC;EACrD,CAACC,EAAE,EAAEC,EAAE,CAAC,GAAGR,cAAc,CAACO,EAAE,EAAEC,EAAE,CAAC;EAEjCN,0BAA0B,CAACK,EAAE,CAACE,KAAK,EAAED,EAAE,CAACC,KAAK,CAAC;EAE9C,MAAMC,MAAM,GAA4B;IAACL,CAAC,EAAEE,EAAE;IAAED,CAAC,EAAEE;EAAE,CAAC;EACtD,MAAMG,KAAK,GAAG,EAAE;EAEhB,OAAOb,MAAM,CAACc,SAAS,CACnBb,iBAAiB,EAAEW,MAAmC,EAAEC,KAAK,CAAC;AACpE;AAEA,OAAO,MAAME,iBAAiB,GAAG,eAAgBV,EAAE,CAAC;EAACC;AAAkB,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}