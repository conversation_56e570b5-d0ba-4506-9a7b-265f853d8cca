{"ast": null, "code": "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { Multiply } from '../kernel_names';\nimport { assertAndGetBroadcastShape, getReductionAxes } from '../ops/broadcast_util';\nimport { cast } from '../ops/cast';\nimport { mul } from '../ops/mul';\nimport { reshape } from '../ops/reshape';\nimport { sum } from '../ops/sum';\nexport const multiplyGradConfig = {\n  kernelName: Multiply,\n  inputsToSave: ['a', 'b'],\n  gradFunc: (dy, saved) => {\n    const [a, b] = saved;\n    const outShape = assertAndGetBroadcastShape(a.shape, b.shape);\n    const derA = () => {\n      const res = mul(dy, cast(b, 'float32'));\n      const reduceAxes = getReductionAxes(a.shape, outShape);\n      if (reduceAxes.length > 0) {\n        return reshape(sum(res, reduceAxes), a.shape);\n      }\n      return res;\n    };\n    const derB = () => {\n      const res = mul(dy, cast(a, 'float32'));\n      const reduceAxes = getReductionAxes(b.shape, outShape);\n      if (reduceAxes.length > 0) {\n        return reshape(sum(res, reduceAxes), b.shape);\n      }\n      return res;\n    };\n    return {\n      a: derA,\n      b: derB\n    };\n  }\n};", "map": {"version": 3, "names": ["Multiply", "assertAndGetBroadcastShape", "getReductionAxes", "cast", "mul", "reshape", "sum", "multiplyGradConfig", "kernelName", "inputsToSave", "grad<PERSON>unc", "dy", "saved", "a", "b", "outShape", "shape", "derA", "res", "reduceAxes", "length", "derB"], "sources": ["C:\\tfjs-core\\src\\gradients\\Multiply_grad.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {Multiply} from '../kernel_names';\nimport {GradConfig} from '../kernel_registry';\nimport {assertAndGetBroadcastShape, getReductionAxes} from '../ops/broadcast_util';\nimport {cast} from '../ops/cast';\nimport {mul} from '../ops/mul';\nimport {reshape} from '../ops/reshape';\nimport {sum} from '../ops/sum';\nimport {Tensor} from '../tensor';\n\nexport const multiplyGradConfig: GradConfig = {\n  kernelName: Multiply,\n  inputsToSave: ['a', 'b'],\n  gradFunc: (dy: Tensor, saved: Tensor[]) => {\n    const [a, b] = saved;\n    const outShape = assertAndGetBroadcastShape(a.shape, b.shape);\n\n    const derA = () => {\n      const res = mul(dy, cast(b, 'float32'));\n      const reduceAxes = getReductionAxes(a.shape, outShape);\n      if (reduceAxes.length > 0) {\n        return reshape(sum(res, reduceAxes), a.shape);\n      }\n      return res;\n    };\n    const derB = () => {\n      const res = mul(dy, cast(a, 'float32'));\n      const reduceAxes = getReductionAxes(b.shape, outShape);\n      if (reduceAxes.length > 0) {\n        return reshape(sum(res, reduceAxes), b.shape);\n      }\n      return res;\n    };\n    return {a: derA, b: derB};\n  }\n};\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAiBA,SAAQA,QAAQ,QAAO,iBAAiB;AAExC,SAAQC,0BAA0B,EAAEC,gBAAgB,QAAO,uBAAuB;AAClF,SAAQC,IAAI,QAAO,aAAa;AAChC,SAAQC,GAAG,QAAO,YAAY;AAC9B,SAAQC,OAAO,QAAO,gBAAgB;AACtC,SAAQC,GAAG,QAAO,YAAY;AAG9B,OAAO,MAAMC,kBAAkB,GAAe;EAC5CC,UAAU,EAAER,QAAQ;EACpBS,YAAY,EAAE,CAAC,GAAG,EAAE,GAAG,CAAC;EACxBC,QAAQ,EAAEA,CAACC,EAAU,EAAEC,KAAe,KAAI;IACxC,MAAM,CAACC,CAAC,EAAEC,CAAC,CAAC,GAAGF,KAAK;IACpB,MAAMG,QAAQ,GAAGd,0BAA0B,CAACY,CAAC,CAACG,KAAK,EAAEF,CAAC,CAACE,KAAK,CAAC;IAE7D,MAAMC,IAAI,GAAGA,CAAA,KAAK;MAChB,MAAMC,GAAG,GAAGd,GAAG,CAACO,EAAE,EAAER,IAAI,CAACW,CAAC,EAAE,SAAS,CAAC,CAAC;MACvC,MAAMK,UAAU,GAAGjB,gBAAgB,CAACW,CAAC,CAACG,KAAK,EAAED,QAAQ,CAAC;MACtD,IAAII,UAAU,CAACC,MAAM,GAAG,CAAC,EAAE;QACzB,OAAOf,OAAO,CAACC,GAAG,CAACY,GAAG,EAAEC,UAAU,CAAC,EAAEN,CAAC,CAACG,KAAK,CAAC;;MAE/C,OAAOE,GAAG;IACZ,CAAC;IACD,MAAMG,IAAI,GAAGA,CAAA,KAAK;MAChB,MAAMH,GAAG,GAAGd,GAAG,CAACO,EAAE,EAAER,IAAI,CAACU,CAAC,EAAE,SAAS,CAAC,CAAC;MACvC,MAAMM,UAAU,GAAGjB,gBAAgB,CAACY,CAAC,CAACE,KAAK,EAAED,QAAQ,CAAC;MACtD,IAAII,UAAU,CAACC,MAAM,GAAG,CAAC,EAAE;QACzB,OAAOf,OAAO,CAACC,GAAG,CAACY,GAAG,EAAEC,UAAU,CAAC,EAAEL,CAAC,CAACE,KAAK,CAAC;;MAE/C,OAAOE,GAAG;IACZ,CAAC;IACD,OAAO;MAACL,CAAC,EAAEI,IAAI;MAAEH,CAAC,EAAEO;IAAI,CAAC;EAC3B;CACD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}