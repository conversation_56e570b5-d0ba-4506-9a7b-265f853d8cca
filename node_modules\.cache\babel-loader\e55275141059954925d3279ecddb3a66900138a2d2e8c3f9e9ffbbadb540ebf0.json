{"ast": null, "code": "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { backend_util, SparseToDense, util } from '@tensorflow/tfjs-core';\nimport { scatterImpl } from './Scatter_impl';\nexport function sparseToDense(args) {\n  const {\n    inputs,\n    backend,\n    attrs\n  } = args;\n  const {\n    sparseIndices,\n    sparseValues,\n    defaultValue\n  } = inputs;\n  const {\n    outputShape\n  } = attrs;\n  const {\n    sliceRank,\n    numUpdates,\n    sliceSize,\n    strides,\n    outputSize\n  } = backend_util.calculateShapes(sparseValues, sparseIndices, outputShape);\n  const sumDupeIndices = false;\n  const indicesBuf = backend.bufferSync(sparseIndices);\n  let outBuf;\n  switch (sparseValues.dtype) {\n    case 'bool':\n      {\n        const updatesBuf = backend.bufferSync(sparseValues);\n        const $defaultValue = Boolean(backend.data.get(defaultValue.dataId).values[0]);\n        outBuf = scatterImpl(indicesBuf, updatesBuf, outputShape, outputSize, sliceSize, numUpdates, sliceRank, strides, $defaultValue, sumDupeIndices);\n        break;\n      }\n    case 'float32':\n      {\n        const updatesBuf = backend.bufferSync(sparseValues);\n        const $defaultValue = backend.data.get(defaultValue.dataId).values[0];\n        outBuf = scatterImpl(indicesBuf, updatesBuf, outputShape, outputSize, sliceSize, numUpdates, sliceRank, strides, $defaultValue, sumDupeIndices);\n        break;\n      }\n    case 'int32':\n      {\n        const updatesBuf = backend.bufferSync(sparseValues);\n        const $defaultValue = backend.data.get(defaultValue.dataId).values[0];\n        outBuf = scatterImpl(indicesBuf, updatesBuf, outputShape, outputSize, sliceSize, numUpdates, sliceRank, strides, $defaultValue, sumDupeIndices);\n        break;\n      }\n    case 'string':\n      {\n        const updatesBuf = backend.bufferSync(sparseValues);\n        const $defaultValue = util.decodeString(backend.data.get(defaultValue.dataId).values[0]);\n        outBuf = scatterImpl(indicesBuf, updatesBuf, outputShape, outputSize, sliceSize, numUpdates, sliceRank, strides, $defaultValue, sumDupeIndices);\n        break;\n      }\n    default:\n      throw new Error(`Unsupported type ${sparseValues.dtype}`);\n  }\n  return backend.makeTensorInfo(outputShape, outBuf.dtype, outBuf.values);\n}\nexport const sparseToDenseConfig = {\n  kernelName: SparseToDense,\n  backendName: 'cpu',\n  kernelFunc: sparseToDense\n};", "map": {"version": 3, "names": ["backend_util", "SparseToDense", "util", "scatterImpl", "sparseToDense", "args", "inputs", "backend", "attrs", "sparseIndices", "sparseValues", "defaultValue", "outputShape", "sliceRank", "numUpdates", "sliceSize", "strides", "outputSize", "calculateShapes", "sumDupeIndices", "indicesBuf", "bufferSync", "outBuf", "dtype", "updatesBuf", "$defaultValue", "Boolean", "data", "get", "dataId", "values", "decodeString", "Error", "makeTensorInfo", "sparseToDenseConfig", "kernelName", "backendName", "kernelFunc"], "sources": ["C:\\tfjs-backend-cpu\\src\\kernels\\SparseToDense.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {backend_util, KernelConfig, KernelFunc, Rank, SparseToDense, SparseToDenseAttrs, SparseToDenseInputs, TensorInfo, util} from '@tensorflow/tfjs-core';\n\nimport {MathBackendCPU} from '../backend_cpu';\nimport {scatterImpl} from './Scatter_impl';\n\nexport function sparseToDense(args: {\n  inputs: SparseToDenseInputs,\n  backend: MathBackendCPU,\n  attrs: SparseToDenseAttrs\n}): TensorInfo {\n  const {inputs, backend, attrs} = args;\n  const {sparseIndices, sparseValues, defaultValue} = inputs;\n  const {outputShape} = attrs;\n\n  const {sliceRank, numUpdates, sliceSize, strides, outputSize} =\n      backend_util.calculateShapes(sparseValues, sparseIndices, outputShape);\n  const sumDupeIndices = false;\n\n  const indicesBuf = backend.bufferSync<Rank, 'int32'>(sparseIndices);\n\n  let outBuf;\n  switch (sparseValues.dtype) {\n    case 'bool': {\n      const updatesBuf = backend.bufferSync<Rank, 'bool'>(sparseValues);\n      const $defaultValue =\n          Boolean(backend.data.get(defaultValue.dataId).values[0]);\n      outBuf = scatterImpl(\n          indicesBuf, updatesBuf, outputShape, outputSize, sliceSize,\n          numUpdates, sliceRank, strides, $defaultValue, sumDupeIndices);\n      break;\n    }\n    case 'float32': {\n      const updatesBuf = backend.bufferSync<Rank, 'float32'>(sparseValues);\n      const $defaultValue =\n          backend.data.get(defaultValue.dataId).values[0] as number;\n      outBuf = scatterImpl(\n          indicesBuf, updatesBuf, outputShape, outputSize, sliceSize,\n          numUpdates, sliceRank, strides, $defaultValue, sumDupeIndices);\n      break;\n    }\n    case 'int32': {\n      const updatesBuf = backend.bufferSync<Rank, 'int32'>(sparseValues);\n      const $defaultValue =\n          backend.data.get(defaultValue.dataId).values[0] as number;\n      outBuf = scatterImpl(\n          indicesBuf, updatesBuf, outputShape, outputSize, sliceSize,\n          numUpdates, sliceRank, strides, $defaultValue, sumDupeIndices);\n      break;\n    }\n    case 'string': {\n      const updatesBuf = backend.bufferSync<Rank, 'string'>(sparseValues);\n      const $defaultValue = util.decodeString(\n          backend.data.get(defaultValue.dataId).values[0] as Uint8Array);\n      outBuf = scatterImpl(\n          indicesBuf, updatesBuf, outputShape, outputSize, sliceSize,\n          numUpdates, sliceRank, strides, $defaultValue, sumDupeIndices);\n      break;\n    }\n    default:\n      throw new Error(`Unsupported type ${sparseValues.dtype}`);\n  }\n  return backend.makeTensorInfo(outputShape, outBuf.dtype, outBuf.values);\n}\n\nexport const sparseToDenseConfig: KernelConfig = {\n  kernelName: SparseToDense,\n  backendName: 'cpu',\n  kernelFunc: sparseToDense as unknown as KernelFunc\n};\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAiBA,SAAQA,YAAY,EAAkCC,aAAa,EAAuDC,IAAI,QAAO,uBAAuB;AAG5J,SAAQC,WAAW,QAAO,gBAAgB;AAE1C,OAAM,SAAUC,aAAaA,CAACC,IAI7B;EACC,MAAM;IAACC,MAAM;IAAEC,OAAO;IAAEC;EAAK,CAAC,GAAGH,IAAI;EACrC,MAAM;IAACI,aAAa;IAAEC,YAAY;IAAEC;EAAY,CAAC,GAAGL,MAAM;EAC1D,MAAM;IAACM;EAAW,CAAC,GAAGJ,KAAK;EAE3B,MAAM;IAACK,SAAS;IAAEC,UAAU;IAAEC,SAAS;IAAEC,OAAO;IAAEC;EAAU,CAAC,GACzDjB,YAAY,CAACkB,eAAe,CAACR,YAAY,EAAED,aAAa,EAAEG,WAAW,CAAC;EAC1E,MAAMO,cAAc,GAAG,KAAK;EAE5B,MAAMC,UAAU,GAAGb,OAAO,CAACc,UAAU,CAAgBZ,aAAa,CAAC;EAEnE,IAAIa,MAAM;EACV,QAAQZ,YAAY,CAACa,KAAK;IACxB,KAAK,MAAM;MAAE;QACX,MAAMC,UAAU,GAAGjB,OAAO,CAACc,UAAU,CAAeX,YAAY,CAAC;QACjE,MAAMe,aAAa,GACfC,OAAO,CAACnB,OAAO,CAACoB,IAAI,CAACC,GAAG,CAACjB,YAAY,CAACkB,MAAM,CAAC,CAACC,MAAM,CAAC,CAAC,CAAC,CAAC;QAC5DR,MAAM,GAAGnB,WAAW,CAChBiB,UAAU,EAAEI,UAAU,EAAEZ,WAAW,EAAEK,UAAU,EAAEF,SAAS,EAC1DD,UAAU,EAAED,SAAS,EAAEG,OAAO,EAAES,aAAa,EAAEN,cAAc,CAAC;QAClE;;IAEF,KAAK,SAAS;MAAE;QACd,MAAMK,UAAU,GAAGjB,OAAO,CAACc,UAAU,CAAkBX,YAAY,CAAC;QACpE,MAAMe,aAAa,GACflB,OAAO,CAACoB,IAAI,CAACC,GAAG,CAACjB,YAAY,CAACkB,MAAM,CAAC,CAACC,MAAM,CAAC,CAAC,CAAW;QAC7DR,MAAM,GAAGnB,WAAW,CAChBiB,UAAU,EAAEI,UAAU,EAAEZ,WAAW,EAAEK,UAAU,EAAEF,SAAS,EAC1DD,UAAU,EAAED,SAAS,EAAEG,OAAO,EAAES,aAAa,EAAEN,cAAc,CAAC;QAClE;;IAEF,KAAK,OAAO;MAAE;QACZ,MAAMK,UAAU,GAAGjB,OAAO,CAACc,UAAU,CAAgBX,YAAY,CAAC;QAClE,MAAMe,aAAa,GACflB,OAAO,CAACoB,IAAI,CAACC,GAAG,CAACjB,YAAY,CAACkB,MAAM,CAAC,CAACC,MAAM,CAAC,CAAC,CAAW;QAC7DR,MAAM,GAAGnB,WAAW,CAChBiB,UAAU,EAAEI,UAAU,EAAEZ,WAAW,EAAEK,UAAU,EAAEF,SAAS,EAC1DD,UAAU,EAAED,SAAS,EAAEG,OAAO,EAAES,aAAa,EAAEN,cAAc,CAAC;QAClE;;IAEF,KAAK,QAAQ;MAAE;QACb,MAAMK,UAAU,GAAGjB,OAAO,CAACc,UAAU,CAAiBX,YAAY,CAAC;QACnE,MAAMe,aAAa,GAAGvB,IAAI,CAAC6B,YAAY,CACnCxB,OAAO,CAACoB,IAAI,CAACC,GAAG,CAACjB,YAAY,CAACkB,MAAM,CAAC,CAACC,MAAM,CAAC,CAAC,CAAe,CAAC;QAClER,MAAM,GAAGnB,WAAW,CAChBiB,UAAU,EAAEI,UAAU,EAAEZ,WAAW,EAAEK,UAAU,EAAEF,SAAS,EAC1DD,UAAU,EAAED,SAAS,EAAEG,OAAO,EAAES,aAAa,EAAEN,cAAc,CAAC;QAClE;;IAEF;MACE,MAAM,IAAIa,KAAK,CAAC,oBAAoBtB,YAAY,CAACa,KAAK,EAAE,CAAC;;EAE7D,OAAOhB,OAAO,CAAC0B,cAAc,CAACrB,WAAW,EAAEU,MAAM,CAACC,KAAK,EAAED,MAAM,CAACQ,MAAM,CAAC;AACzE;AAEA,OAAO,MAAMI,mBAAmB,GAAiB;EAC/CC,UAAU,EAAElC,aAAa;EACzBmC,WAAW,EAAE,KAAK;EAClBC,UAAU,EAAEjC;CACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}