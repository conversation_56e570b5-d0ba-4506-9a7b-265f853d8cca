{"ast": null, "code": "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the License);\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an AS IS BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { Unique } from '@tensorflow/tfjs-core';\nimport { uniqueImplCPU } from '../kernel_utils/shared';\nimport { assertNotComplex } from '../webgl_util';\nexport function unique(args) {\n  const {\n    inputs,\n    attrs,\n    backend\n  } = args;\n  const {\n    axis\n  } = attrs;\n  const {\n    x\n  } = inputs;\n  assertNotComplex(x, 'unique');\n  // For now, always forward calculation to the CPU backend.\n  console.warn('WARNING: ', 'UI might be locked temporarily as data is being downloaded');\n  const values = backend.readSync(x.dataId);\n  const {\n    outputValues,\n    outputShape,\n    indices\n  } = uniqueImplCPU(values, axis, x.shape, x.dtype);\n  return [backend.makeTensorInfo(outputShape, x.dtype, outputValues), backend.makeTensorInfo([indices.length], 'int32', indices)];\n}\nexport const uniqueConfig = {\n  kernelName: Unique,\n  backendName: 'webgl',\n  kernelFunc: unique\n};", "map": {"version": 3, "names": ["Unique", "uniqueImplCPU", "assertNotComplex", "unique", "args", "inputs", "attrs", "backend", "axis", "x", "console", "warn", "values", "readSync", "dataId", "outputValues", "outputShape", "indices", "shape", "dtype", "makeTensorInfo", "length", "uniqueConfig", "kernelName", "backendName", "kernelFunc"], "sources": ["C:\\tfjs-backend-webgl\\src\\kernels\\Unique.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the License);\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an AS IS BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {KernelConfig, KernelFunc, TensorInfo, Unique, UniqueAttrs, UniqueInputs} from '@tensorflow/tfjs-core';\n\nimport {MathBackendWebGL} from '../backend_webgl';\nimport {uniqueImplCPU} from '../kernel_utils/shared';\nimport {assertNotComplex} from '../webgl_util';\n\nexport function unique(\n    args:\n        {inputs: UniqueInputs, attrs: UniqueAttrs, backend: MathBackendWebGL}):\n    TensorInfo[] {\n  const {inputs, attrs, backend} = args;\n  const {axis} = attrs;\n  const {x} = inputs;\n  assertNotComplex(x, 'unique');\n\n  // For now, always forward calculation to the CPU backend.\n  console.warn(\n      'WARNING: ',\n      'UI might be locked temporarily as data is being downloaded');\n  const values = backend.readSync(x.dataId);\n  const {outputValues, outputShape, indices} =\n      uniqueImplCPU(values, axis, x.shape, x.dtype);\n  return [\n    backend.makeTensorInfo(outputShape, x.dtype, outputValues),\n    backend.makeTensorInfo([indices.length], 'int32', indices),\n  ];\n}\n\nexport const uniqueConfig: KernelConfig = {\n  kernelName: Unique,\n  backendName: 'webgl',\n  kernelFunc: unique as unknown as KernelFunc,\n};\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAiBA,SAA8CA,MAAM,QAAkC,uBAAuB;AAG7G,SAAQC,aAAa,QAAO,wBAAwB;AACpD,SAAQC,gBAAgB,QAAO,eAAe;AAE9C,OAAM,SAAUC,MAAMA,CAClBC,IACyE;EAE3E,MAAM;IAACC,MAAM;IAAEC,KAAK;IAAEC;EAAO,CAAC,GAAGH,IAAI;EACrC,MAAM;IAACI;EAAI,CAAC,GAAGF,KAAK;EACpB,MAAM;IAACG;EAAC,CAAC,GAAGJ,MAAM;EAClBH,gBAAgB,CAACO,CAAC,EAAE,QAAQ,CAAC;EAE7B;EACAC,OAAO,CAACC,IAAI,CACR,WAAW,EACX,4DAA4D,CAAC;EACjE,MAAMC,MAAM,GAAGL,OAAO,CAACM,QAAQ,CAACJ,CAAC,CAACK,MAAM,CAAC;EACzC,MAAM;IAACC,YAAY;IAAEC,WAAW;IAAEC;EAAO,CAAC,GACtChB,aAAa,CAACW,MAAM,EAAEJ,IAAI,EAAEC,CAAC,CAACS,KAAK,EAAET,CAAC,CAACU,KAAK,CAAC;EACjD,OAAO,CACLZ,OAAO,CAACa,cAAc,CAACJ,WAAW,EAAEP,CAAC,CAACU,KAAK,EAAEJ,YAAY,CAAC,EAC1DR,OAAO,CAACa,cAAc,CAAC,CAACH,OAAO,CAACI,MAAM,CAAC,EAAE,OAAO,EAAEJ,OAAO,CAAC,CAC3D;AACH;AAEA,OAAO,MAAMK,YAAY,GAAiB;EACxCC,UAAU,EAAEvB,MAAM;EAClBwB,WAAW,EAAE,OAAO;EACpBC,UAAU,EAAEtB;CACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}