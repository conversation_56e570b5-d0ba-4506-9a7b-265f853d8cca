{"ast": null, "code": "/**\n * @license\n * Copyright 2018 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { env, keep, tidy, util } from '@tensorflow/tfjs-core';\nimport { getNodeNameAndIndex, getParamValue, getTensor, getTensorsForCurrentContext, parseNodeName } from '../operations/executors/utils';\nimport { executeOp } from '../operations/operation_executor';\nimport { ExecutionContext } from './execution_context';\nimport { getExecutionSubgraph, getNodeLiveUntilMap, getNodesInTopologicalOrder, isControlFlow } from './model_analysis';\nexport class GraphExecutor {\n  get weightIds() {\n    return this.parent ? this.parent.weightIds : this._weightIds;\n  }\n  get functionExecutorMap() {\n    return this.parent ? this.parent.functionExecutorMap : this._functionExecutorMap;\n  }\n  get weightMap() {\n    return this.parent ? this.parent.weightMap : this._weightMap;\n  }\n  set weightMap(weightMap) {\n    const weightIds = Object.keys(weightMap).map(key => weightMap[key].map(tensor => tensor.id));\n    this._weightIds = [].concat(...weightIds);\n    this._weightMap = weightMap;\n  }\n  /**\n   * Set `ResourceManager` shared by executors of a model.\n   * @param resourceManager: `ResourceManager` of the `GraphModel`.\n   */\n  set resourceManager(resourceManager) {\n    this._resourceManager = resourceManager;\n  }\n  get inputs() {\n    return this._inputs.map(node => {\n      return {\n        name: node.name,\n        shape: node.attrParams['shape'] ? node.attrParams['shape'].value : undefined,\n        dtype: node.attrParams['dtype'] ? node.attrParams['dtype'].value : undefined\n      };\n    });\n  }\n  get outputs() {\n    return this._outputs.map(node => {\n      return {\n        name: node.name,\n        shape: node.attrParams['shape'] ? node.attrParams['shape'].value : undefined,\n        dtype: node.attrParams['dtype'] ? node.attrParams['dtype'].value : undefined\n      };\n    });\n  }\n  get inputNodes() {\n    return this._inputs.map(node => node.signatureKey || node.name);\n  }\n  get outputNodes() {\n    return this._outputs.map(node => {\n      const name = node.signatureKey || node.name;\n      return node.defaultOutput ? `${name}:${node.defaultOutput}` : name;\n    });\n  }\n  get functions() {\n    return Object.keys(this._functions).reduce((map, key) => {\n      map[key] = this._functions[key].signature;\n      return map;\n    }, {});\n  }\n  /**\n   *\n   * @param graph Graph the model or function graph to be executed.\n   * @param parent When building function exector you need to set the parent\n   * executor. Since the weights and function executor maps are set at parant\n   * level, that function executor can access the function maps and weight maps\n   * through the parent.\n   */\n  constructor(graph, parent) {\n    this.graph = graph;\n    this.parent = parent;\n    this.compiledMap = new Map();\n    this.parseNodeNameCache = new Map();\n    this._weightMap = {};\n    this.SEPARATOR = ',';\n    this._functions = {};\n    this._functionExecutorMap = {};\n    this.keepIntermediateTensors = false;\n    this._outputs = graph.outputs;\n    this._inputs = graph.inputs;\n    this._initNodes = graph.initNodes;\n    this._signature = graph.signature;\n    this._functions = graph.functions;\n    // create sub-graph executors\n    if (graph.functions != null) {\n      Object.keys(graph.functions).forEach(name => {\n        this._functionExecutorMap[name] = new GraphExecutor(graph.functions[name], this);\n      });\n    }\n  }\n  getCompilationKey(inputs, outputs) {\n    const sortedInputs = inputs.map(node => node.name).sort();\n    const sortedOutputs = outputs.map(node => node.name).sort();\n    return sortedInputs.join(this.SEPARATOR) + '--' + sortedOutputs.join(this.SEPARATOR);\n  }\n  /**\n   * Compiles the inference graph and returns the minimal set of nodes that are\n   * required for execution, in the correct execution order.\n   * @returns {Object} compilation The compile result.\n   * @returns {Node[]} compilation.orderedNodes Nodes in the correct execution\n   *     order.\n   * @returns {Map<string, Node[]>} compilation.nodeLiveUntilMap A map from node\n   *     to disposable nodes after its execution. That is, for a node `x`,\n   *     `nodeLiveUntilMap[x]` indicates all nodes whose intermediate\n   *     tensors should be disposed after `x` is executed.\n   */\n  compile(inputs, outputs) {\n    const executionInfo = getExecutionSubgraph(inputs, outputs, this.weightMap, this._initNodes);\n    const {\n      missingInputs,\n      dynamicNode,\n      syncInputs\n    } = executionInfo;\n    if (dynamicNode != null) {\n      throw new Error(`This execution contains the node '${dynamicNode.name}', which has ` + `the dynamic op '${dynamicNode.op}'. Please use ` + `model.executeAsync() instead. Alternatively, to avoid the ` + `dynamic ops, specify the inputs [${syncInputs}]`);\n    }\n    if (missingInputs.length > 0) {\n      const outNames = outputs.map(n => n.name);\n      const inNames = Object.keys(inputs);\n      throw new Error(`Cannot compute the outputs [${outNames}] from the provided inputs ` + `[${inNames}]. Missing the following inputs: [${missingInputs}]`);\n    }\n    const orderedNodes = getNodesInTopologicalOrder(this.graph, executionInfo);\n    const nodeLiveUntilMap = getNodeLiveUntilMap(orderedNodes);\n    return {\n      orderedNodes,\n      nodeLiveUntilMap\n    };\n  }\n  cloneAndKeepTensor(tensor) {\n    if (tensor == null) {\n      return null;\n    }\n    const clone = tensor.clone();\n    // Keep the clone because`model.execute()` may be called within\n    // a `tidy()`, but the user may inspect these tensors after the\n    // tidy.\n    keep(clone);\n    return clone;\n  }\n  cloneTensorList(tensors) {\n    if (!tensors) {\n      return null;\n    }\n    const clonedTensor = tensors.map(tensor => {\n      return this.cloneAndKeepTensor(tensor);\n    });\n    return clonedTensor;\n  }\n  cloneTensorMap(tensorsMap) {\n    return Object.fromEntries(Object.entries(tensorsMap).map(([name, tensorsList]) => {\n      return [name, this.cloneTensorList(tensorsList)];\n    }));\n  }\n  /**\n   * Executes the inference for given input tensors.\n   * @param inputs Tensor map for the model inputs, keyed by the input node\n   * names.\n   * @param outputs Optional. output node name from the Tensorflow model, if\n   * no outputs are specified, the default outputs of the model would be used.\n   * You can inspect intermediate nodes of the model by adding them to the\n   * outputs array.\n   */\n  execute(inputs, outputs) {\n    // Dispose any tensors from a prior run to avoid leaking them.\n    this.disposeIntermediateTensors();\n    inputs = this.mapInputs(inputs);\n    const names = Object.keys(inputs).sort();\n    this.checkInputs(inputs);\n    this.checkInputShapeAndType(inputs);\n    outputs = this.mapOutputs(outputs);\n    this.checkOutputs(outputs);\n    const inputNodes = names.map(name => this.graph.nodes[parseNodeName(name)[0]]);\n    const outputNodeNames = outputs.map(name => parseNodeName(name)[0]);\n    const outputNodeNameSet = new Set(outputNodeNames);\n    let outputNodes = outputNodeNames.map(name => this.graph.nodes[name]);\n    // If no outputs are specified, then use the default outputs of the model.\n    if (outputNodes.length === 0) {\n      outputNodes = this._outputs;\n    }\n    const compilationKey = this.getCompilationKey(inputNodes, outputNodes);\n    // Do nothing if the compiled graph cache contains the input.\n    let compilation = this.compiledMap.get(compilationKey);\n    if (compilation == null) {\n      compilation = this.compile(inputs, outputNodes);\n      this.compiledMap.set(compilationKey, compilation);\n    }\n    // Keep tensors if KEEP_INTERMEDIATE_TENSORS is on.\n    try {\n      this.keepIntermediateTensors = env().getBool('KEEP_INTERMEDIATE_TENSORS');\n    } catch (e) {\n      this.keepIntermediateTensors = false;\n      console.warn(e.message);\n    }\n    const tensorArrayMap = {};\n    const tensorListMap = {};\n    return tidy(() => {\n      const context = new ExecutionContext(this.weightMap, tensorArrayMap, tensorListMap, this.functionExecutorMap, this.parseNodeNameCache);\n      const tensorsMap = Object.assign({}, this.weightMap);\n      if (this.keepIntermediateTensors) {\n        this.clonedTensorsMap = this.cloneTensorMap(this.weightMap);\n      }\n      Object.keys(inputs).forEach(name => {\n        const [nodeName, index] = parseNodeName(name, context);\n        const tensors = [];\n        tensors[index] = inputs[name];\n        tensorsMap[nodeName] = tensors;\n        if (this.keepIntermediateTensors) {\n          this.clonedTensorsMap[nodeName] = this.cloneTensorList(tensors);\n        }\n      });\n      const tensorsToKeep = this.getFrozenTensorIds(tensorsMap);\n      const {\n        orderedNodes,\n        nodeLiveUntilMap\n      } = compilation;\n      for (const node of orderedNodes) {\n        if (tensorsMap[node.name]) {\n          continue;\n        }\n        const tensors = executeOp(node, tensorsMap, context, this._resourceManager);\n        if (util.isPromise(tensors)) {\n          throw new Error(`The execution of the op '${node.op}' returned a promise. ` + `Please use model.executeAsync() instead.`);\n        }\n        tensorsMap[node.name] = tensors;\n        if (this.keepIntermediateTensors) {\n          this.clonedTensorsMap[node.name] = this.cloneTensorList(tensors);\n        }\n        this.checkTensorForDisposalWithNodeLiveUntilInfo(node, tensorsMap, context, tensorsToKeep, outputNodeNameSet, nodeLiveUntilMap.get(node.name));\n      }\n      // dispose the context for the root executor\n      if (this.parent == null) {\n        context.dispose(tensorsToKeep);\n      }\n      return outputs.map(name => getTensor(name, tensorsMap, context));\n    });\n  }\n  getFrozenTensorIds(tensorMap) {\n    const ids = [].concat.apply([], Object.keys(tensorMap).map(key => tensorMap[key]).map(tensors => tensors.map(tensor => tensor.id)));\n    return new Set(ids);\n  }\n  checkTensorForDisposal(nodeName, node, tensorMap, context, tensorsToKeep, outputNodeNameSet, intermediateTensorConsumerCount) {\n    // Skip output nodes and any control flow nodes, since its dependency is\n    // tricky to track correctly.\n    if (isControlFlow(node) || outputNodeNameSet.has(nodeName)) {\n      return;\n    }\n    for (const tensor of tensorMap[nodeName]) {\n      if (tensor == null) {\n        continue;\n      }\n      intermediateTensorConsumerCount[tensor.id] = (intermediateTensorConsumerCount[tensor.id] || 0) + node.children.length;\n    }\n    for (const input of node.inputs) {\n      // Skip any control flow nodes, since its dependency is tricky to track\n      // correctly.\n      if (isControlFlow(input)) {\n        continue;\n      }\n      const tensors = getTensorsForCurrentContext(input.name, tensorMap, context);\n      if (tensors == null) {\n        continue;\n      }\n      for (const tensor of tensors) {\n        if (!tensor || tensor.kept || tensorsToKeep.has(tensor.id)) {\n          continue;\n        }\n        // Only intermediate nodes' tensors have counts set, not marked as\n        // kept, and not in `tensorsToKeep`.\n        // Input and weight nodes' tensors should exist in `tensorsToKeep`.\n        // Output and control flow nodes' tensors should never have count set.\n        const count = intermediateTensorConsumerCount[tensor.id];\n        if (count === 1) {\n          tensor.dispose();\n          delete intermediateTensorConsumerCount[tensor.id];\n        } else if (count != null) {\n          intermediateTensorConsumerCount[tensor.id]--;\n        }\n      }\n    }\n  }\n  checkTensorForDisposalWithNodeLiveUntilInfo(node, tensorMap, context, tensorsToKeep, outputNodeNameSet, liveUntilNodes) {\n    function isNonDisposableNode(node) {\n      // Skip output nodes and any control flow nodes, since its dependency is\n      // tricky to track correctly.\n      return isControlFlow(node) || outputNodeNameSet.has(node.name);\n    }\n    if (isControlFlow(node) || liveUntilNodes == null) {\n      return;\n    }\n    for (const nodeToDispose of liveUntilNodes) {\n      if (isNonDisposableNode(nodeToDispose)) {\n        continue;\n      }\n      const tensors = getTensorsForCurrentContext(nodeToDispose.name, tensorMap, context);\n      for (const tensor of tensors) {\n        if (!tensor || tensor.kept || tensorsToKeep.has(tensor.id)) {\n          continue;\n        }\n        tensor.dispose();\n      }\n    }\n  }\n  /**\n   * Executes the inference for given input tensors in Async fashion.\n   * @param inputs Tensor map for the model inputs, keyed by the input node\n   * names.\n   * @param outputs output node name from the Tensorflow model, if no outputs\n   * are specified, the default outputs of the model would be used. You can\n   * inspect intermediate nodes of the model by adding them to the outputs\n   * array.\n   */\n  async executeAsync(inputs, outputs) {\n    return this._executeAsync(inputs, outputs);\n  }\n  disposeIntermediateTensors() {\n    if (!this.clonedTensorsMap) {\n      return;\n    }\n    Object.values(this.clonedTensorsMap).forEach(tensorsList => {\n      for (const tensor of tensorsList) {\n        if (tensor && !tensor.isDisposed) {\n          tensor.dispose();\n        }\n      }\n    });\n    this.clonedTensorsMap = null;\n  }\n  getIntermediateTensors() {\n    return this.clonedTensorsMap;\n  }\n  /**\n   * Executes the inference for given input tensors in Async fashion.\n   * @param inputs Tensor map for the model inputs, keyed by the input node\n   * names.\n   * @param outputs Optional. output node name from the Tensorflow model,\n   * if no outputs are specified, the default outputs of the model would be\n   * used. You can inspect intermediate nodes of the model by adding them to\n   * the outputs array.\n   * @param isFunctionExecution Optional. Flag for executing a function.\n   * @param tensorArrayMap Optional, global TensorArray map by id. Used for\n   * function execution.\n   * @param tensorArrayMap Optional global TensorList map by id. Used for\n   * function execution.\n   */\n  async _executeAsync(inputs, outputs, isFunctionExecution = false, tensorArrayMap = {}, tensorListMap = {}) {\n    // Dispose any tensors from a prior run to avoid leaking them.\n    this.disposeIntermediateTensors();\n    if (!isFunctionExecution) {\n      inputs = this.mapInputs(inputs);\n      this.checkInputs(inputs);\n      this.checkInputShapeAndType(inputs);\n      outputs = this.mapOutputs(outputs);\n      this.checkOutputs(outputs);\n    }\n    // Keep tensors if KEEP_INTERMEDIATE_TENSORS is on.\n    try {\n      this.keepIntermediateTensors = env().getBool('KEEP_INTERMEDIATE_TENSORS');\n    } catch (e) {\n      this.keepIntermediateTensors = false;\n      console.warn(e.message);\n    }\n    const context = new ExecutionContext(this.weightMap, tensorArrayMap, tensorListMap, this.functionExecutorMap, this.parseNodeNameCache);\n    if (this.keepIntermediateTensors) {\n      this.clonedTensorsMap = this.cloneTensorMap(this.weightMap);\n    }\n    // Graph with control flow op requires runtime evaluation of the execution\n    // order, while without control flow the execution order is pre-determined\n    // in the compile method.\n    const tensorsMap = await this.executeWithControlFlow(inputs, context, outputs, isFunctionExecution);\n    const results = outputs.map(name => getTensor(name, tensorsMap, context));\n    // dispose all the intermediate tensors\n    const outputIds = results.map(t => t.id);\n    const inputIds = Object.keys(inputs).map(name => inputs[name].id);\n    const keepIds = new Set([...outputIds, ...inputIds, ...this.weightIds]);\n    Object.values(tensorsMap).forEach(tensorsList => {\n      tensorsList.forEach(tensor => {\n        if (tensor && !tensor.isDisposed && !keepIds.has(tensor.id)) {\n          tensor.dispose();\n        }\n      });\n    });\n    // dispose the context for the root executor\n    if (this.parent == null) {\n      context.dispose(keepIds);\n    }\n    return results;\n  }\n  async executeFunctionAsync(inputs, tensorArrayMap, tensorListMap) {\n    const mappedInputs = inputs.reduce((map, tensor, index) => {\n      map[this.inputs[index].name] = tensor;\n      return map;\n    }, {});\n    return this._executeAsync(mappedInputs, this.outputNodes, true, tensorArrayMap, tensorListMap);\n  }\n  /**\n   * When there are control flow nodes in the graph, the graph execution use\n   * ExecutionContext to keep track of the frames and loop iterators.\n   * @param inputs placeholder tensors for the graph.\n   * @param context the execution context object for current execution.\n   * @param outputNames Optional. output node name from the Tensorflow model,\n   * if no outputs are specified, the default outputs of the model would be\n   * used. You can inspect intermediate nodes of the model by adding them to\n   * the outputs array.\n   * @param isFunctionExecution Flag for executing a function.\n   */\n  async executeWithControlFlow(inputs, context, outputNames, isFunctionExecution) {\n    const names = Object.keys(inputs);\n    const inputNodes = names.map(name => this.graph.nodes[parseNodeName(name)[0]]);\n    const outputNodeNames = outputNames.map(name => parseNodeName(name)[0]);\n    const outputNodeNameSet = new Set(outputNodeNames);\n    let outputNodes = outputNodeNames.map(name => this.graph.nodes[name]);\n    // If no outputs are specified, then use the default outputs of the model.\n    if (outputNodes.length === 0) {\n      outputNodes = this._outputs;\n    }\n    const {\n      usedNodes,\n      missingInputs,\n      dynamicNode,\n      syncInputs\n    } = getExecutionSubgraph(inputs, outputNodes, this.weightMap, this._initNodes);\n    // First nodes to execute include inputNodes, weights, and initNodes.\n    const stack = [...inputNodes, ...this.graph.weights, ...(this._initNodes || [])].map(node => {\n      return {\n        node,\n        contexts: context.currentContext\n      };\n    });\n    const tensorsMap = Object.assign({}, this.weightMap);\n    Object.keys(inputs).forEach(name => {\n      const [nodeName, index] = parseNodeName(name);\n      const tensors = [];\n      tensors[index] = inputs[name];\n      tensorsMap[nodeName] = tensors;\n    });\n    const intermediateTensorConsumerCount = {};\n    const tensorsToKeep = this.getFrozenTensorIds(tensorsMap);\n    const added = {};\n    while (stack.length > 0) {\n      const promises = this.processStack(inputNodes, stack, context, tensorsMap, added, tensorsToKeep, outputNodeNameSet, intermediateTensorConsumerCount, usedNodes);\n      await Promise.all(promises);\n    }\n    if (dynamicNode == null && !isFunctionExecution) {\n      console.warn(`This model execution did not contain any nodes with control flow ` + `or dynamic output shapes. You can use model.execute() instead.`);\n    }\n    const missingOutputs = outputNodes.filter(node => !isControlFlow(node) && !getTensor(node.name, tensorsMap, context)).map(node => node.name);\n    if (missingOutputs.length > 0) {\n      let alternativeMsg = '';\n      if (dynamicNode != null) {\n        alternativeMsg = `Alternatively, to avoid the dynamic ops, use model.execute() ` + `and specify the inputs [${syncInputs}]`;\n      }\n      throw new Error(`Cannot compute the outputs [${missingOutputs}] from the provided ` + `inputs [${names}]. Consider providing the following inputs: ` + `[${missingInputs}]. ${alternativeMsg}`);\n    }\n    return tensorsMap;\n  }\n  processStack(inputNodes, stack, context, tensorMap, added, tensorsToKeep, outputNodeNameSet, intermediateTensorConsumerCount, usedNodes) {\n    const promises = [];\n    while (stack.length > 0) {\n      const item = stack.pop();\n      context.currentContext = item.contexts;\n      let nodeName = '';\n      // The tensor of the Enter op with isConstant set should be set\n      // in the parent scope, so it will be available as constant for the\n      // whole loop.\n      if (item.node.op === 'Enter' && getParamValue('isConstant', item.node, tensorMap, context)) {\n        [nodeName] = getNodeNameAndIndex(item.node.name, context);\n      }\n      // only process nodes that are not in the tensorMap yet, this include\n      // inputNodes and internal initNodes.\n      if (tensorMap[item.node.name] == null) {\n        const tensors = executeOp(item.node, tensorMap, context, this._resourceManager);\n        if (!nodeName) {\n          [nodeName] = getNodeNameAndIndex(item.node.name, context);\n        }\n        const currentContext = context.currentContext;\n        if (util.isPromise(tensors)) {\n          promises.push(tensors.then(t => {\n            tensorMap[nodeName] = t;\n            if (this.keepIntermediateTensors) {\n              this.clonedTensorsMap[nodeName] = this.cloneTensorList(t);\n            }\n            context.currentContext = currentContext;\n            this.checkTensorForDisposal(nodeName, item.node, tensorMap, context, tensorsToKeep, outputNodeNameSet, intermediateTensorConsumerCount);\n            this.processChildNodes(item.node, stack, context, tensorMap, added, usedNodes);\n            return t;\n          }));\n        } else {\n          tensorMap[nodeName] = tensors;\n          if (this.keepIntermediateTensors) {\n            this.clonedTensorsMap[nodeName] = this.cloneTensorList(tensors);\n          }\n          this.checkTensorForDisposal(nodeName, item.node, tensorMap, context, tensorsToKeep, outputNodeNameSet, intermediateTensorConsumerCount);\n          this.processChildNodes(item.node, stack, context, tensorMap, added, usedNodes);\n        }\n      } else {\n        this.processChildNodes(item.node, stack, context, tensorMap, added, usedNodes);\n      }\n    }\n    return promises;\n  }\n  processChildNodes(node, stack, context, tensorMap, added, usedNodes) {\n    node.children.forEach(childNode => {\n      const [nodeName] = getNodeNameAndIndex(childNode.name, context);\n      if (added[nodeName] || !usedNodes.has(childNode.name)) {\n        return;\n      }\n      // Merge op can be pushed if any of its inputs has value.\n      if (childNode.op === 'Merge') {\n        if (childNode.inputNames.some(name => {\n          return !!getTensor(name, tensorMap, context);\n        })) {\n          added[nodeName] = true;\n          stack.push({\n            contexts: context.currentContext,\n            node: childNode\n          });\n        }\n      } else\n        // Otherwise all inputs must to have value.\n        if (childNode.inputNames.every(name => {\n          return !!getTensor(name, tensorMap, context);\n        })) {\n          added[nodeName] = true;\n          stack.push({\n            contexts: context.currentContext,\n            node: childNode\n          });\n        }\n    });\n  }\n  /**\n   * Releases the memory used by the weight tensors.\n   */\n  dispose() {\n    Object.keys(this.weightMap).forEach(key => this.weightMap[key].forEach(tensor => tensor.dispose()));\n  }\n  checkInputShapeAndType(inputs) {\n    Object.keys(inputs).forEach(name => {\n      const input = inputs[name];\n      const [nodeName] = parseNodeName(name);\n      const node = this.graph.nodes[nodeName];\n      if (node.attrParams['shape'] && node.attrParams['shape'].value) {\n        const shape = node.attrParams['shape'].value;\n        const match = shape.length === input.shape.length && input.shape.every((dim, index) => shape[index] === -1 || shape[index] === dim);\n        util.assert(match, () => `The shape of dict['${node.name}'] provided in ` + `model.execute(dict) must be [${shape}], but was ` + `[${input.shape}]`);\n      }\n      if (node.attrParams['dtype'] && node.attrParams['dtype'].value) {\n        util.assert(input.dtype === node.attrParams['dtype'].value, () => `The dtype of dict['${node.name}'] provided in ` + `model.execute(dict) must be ` + `${node.attrParams['dtype'].value}, but was ${input.dtype}`);\n      }\n    });\n  }\n  mapInputs(inputs) {\n    var _a, _b;\n    const result = {};\n    for (const inputName in inputs) {\n      const tensor = (_b = (_a = this._signature) === null || _a === void 0 ? void 0 : _a.inputs) === null || _b === void 0 ? void 0 : _b[inputName];\n      if (tensor != null) {\n        result[tensor.name] = inputs[inputName];\n      } else {\n        result[inputName] = inputs[inputName];\n      }\n    }\n    return result;\n  }\n  checkInputs(inputs) {\n    const notInGraph = Object.keys(inputs).filter(name => {\n      const [nodeName] = parseNodeName(name);\n      return this.graph.nodes[nodeName] == null;\n    });\n    if (notInGraph.length > 0) {\n      throw new Error(`The dict provided in model.execute(dict) has ` + `keys: [${notInGraph}] that are not part of graph`);\n    }\n  }\n  mapOutputs(outputs) {\n    return outputs.map(name => {\n      var _a, _b;\n      const tensor = (_b = (_a = this._signature) === null || _a === void 0 ? void 0 : _a.outputs) === null || _b === void 0 ? void 0 : _b[name];\n      if (tensor != null) {\n        return tensor.name;\n      }\n      return name;\n    }, {});\n  }\n  checkOutputs(outputs) {\n    outputs.forEach(name => {\n      const [normalizedName] = parseNodeName(name);\n      if (!this.graph.nodes[normalizedName]) {\n        throw new Error(`The output '${name}' is not found in the graph`);\n      }\n    });\n  }\n}", "map": {"version": 3, "names": ["env", "keep", "tidy", "util", "getNodeNameAndIndex", "getParamValue", "getTensor", "getTensorsForCurrentContext", "parseNodeName", "executeOp", "ExecutionContext", "getExecutionSubgraph", "getNodeLiveUntilMap", "getNodesInTopologicalOrder", "isControlFlow", "GraphExecutor", "weightIds", "parent", "_weightIds", "functionExecutorMap", "_functionExecutorMap", "weightMap", "_weightMap", "Object", "keys", "map", "key", "tensor", "id", "concat", "resourceManager", "_resourceManager", "inputs", "_inputs", "node", "name", "shape", "attrParams", "value", "undefined", "dtype", "outputs", "_outputs", "inputNodes", "signature<PERSON>ey", "outputNodes", "defaultOutput", "functions", "_functions", "reduce", "signature", "constructor", "graph", "compiledMap", "Map", "parseNodeNameCache", "SEPARATOR", "keepIntermediateTensors", "_initNodes", "initNodes", "_signature", "for<PERSON>ach", "getCompilationKey", "sortedInputs", "sort", "sortedOutputs", "join", "compile", "executionInfo", "missingInputs", "dynamicNode", "syncInputs", "Error", "op", "length", "outNames", "n", "inNames", "orderedNodes", "nodeLiveUntilMap", "cloneAndKeepTensor", "clone", "cloneTensorList", "tensors", "clonedTensor", "cloneTensorMap", "tensorsMap", "fromEntries", "entries", "tensorsList", "execute", "disposeIntermediateTensors", "mapInputs", "names", "checkInputs", "checkInputShapeAndType", "mapOutputs", "checkOutputs", "nodes", "outputNodeNames", "outputNodeNameSet", "Set", "compilationKey", "compilation", "get", "set", "getBool", "e", "console", "warn", "message", "tensorArrayMap", "tensorListMap", "context", "assign", "clonedTensorsMap", "nodeName", "index", "tensorsToKeep", "getFrozenTensorIds", "isPromise", "checkTensorForDisposalWithNodeLiveUntilInfo", "dispose", "tensorMap", "ids", "apply", "checkTensorForDisposal", "intermediateTensorConsumerCount", "has", "children", "input", "kept", "count", "liveUntilNodes", "isNonDisposableNode", "nodeToDispose", "executeAsync", "_executeAsync", "values", "isDisposed", "getIntermediateTensors", "isFunctionExecution", "executeWithControlFlow", "results", "outputIds", "t", "inputIds", "keepIds", "executeFunctionAsync", "mappedInputs", "outputNames", "usedNodes", "stack", "weights", "contexts", "currentContext", "added", "promises", "processStack", "Promise", "all", "missingOutputs", "filter", "alternativeMsg", "item", "pop", "push", "then", "processChildNodes", "childNode", "inputNames", "some", "every", "match", "dim", "assert", "result", "inputName", "_b", "_a", "notInGraph", "normalizedName"], "sources": ["C:\\tfjs-converter\\src\\executor\\graph_executor.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2018 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {DataType, env, keep, NamedTensorMap, Tensor, tidy, util} from '@tensorflow/tfjs-core';\n\nimport {ISignatureDef} from '../data/compiled_api';\nimport {NamedTensorsMap, TensorArrayMap, TensorInfo, TensorListMap} from '../data/types';\nimport {getNodeNameAndIndex, getParamValue, getTensor, getTensorsForCurrentContext, parseNodeName} from '../operations/executors/utils';\nimport {executeOp} from '../operations/operation_executor';\nimport {Graph, Node} from '../operations/types';\n\nimport {ExecutionContext, ExecutionContextInfo} from './execution_context';\nimport {getExecutionSubgraph, getNodeLiveUntilMap, getNodesInTopologicalOrder, isControlFlow} from './model_analysis';\nimport {ResourceManager} from './resource_manager';\nimport {FunctionExecutor} from './types';\n\ninterface NodeWithContexts {\n  contexts: ExecutionContextInfo[];\n  node: Node;\n}\n\nexport class GraphExecutor implements FunctionExecutor {\n  private compiledMap = new Map<string, ReturnType<typeof this.compile>>();\n  private parseNodeNameCache = new Map<string, [string, number, string?]>();\n  private _weightMap: NamedTensorsMap = {};\n  private _weightIds: number[];\n  private _signature: ISignatureDef;\n  private _inputs: Node[];\n  private _outputs: Node[];\n  private _initNodes: Node[];  // Internal init nodes to start initialization.\n  private SEPARATOR = ',';\n  private _functions: {[key: string]: Graph} = {};\n  private _functionExecutorMap: {[key: string]: FunctionExecutor} = {};\n  private _resourceManager: ResourceManager;\n  private clonedTensorsMap: NamedTensorsMap;\n  private keepIntermediateTensors = false;\n\n  get weightIds(): number[] {\n    return this.parent ? this.parent.weightIds : this._weightIds;\n  }\n\n  get functionExecutorMap(): {[key: string]: FunctionExecutor} {\n    return this.parent ? this.parent.functionExecutorMap :\n                         this._functionExecutorMap;\n  }\n\n  get weightMap(): NamedTensorsMap {\n    return this.parent ? this.parent.weightMap : this._weightMap;\n  }\n\n  set weightMap(weightMap: NamedTensorsMap) {\n    const weightIds = Object.keys(weightMap).map(\n        key => weightMap[key].map(tensor => tensor.id));\n    this._weightIds = [].concat(...weightIds);\n    this._weightMap = weightMap;\n  }\n\n  /**\n   * Set `ResourceManager` shared by executors of a model.\n   * @param resourceManager: `ResourceManager` of the `GraphModel`.\n   */\n  set resourceManager(resourceManager: ResourceManager) {\n    this._resourceManager = resourceManager;\n  }\n\n  get inputs(): TensorInfo[] {\n    return this._inputs.map(node => {\n      return {\n        name: node.name,\n        shape: node.attrParams['shape'] ?\n            node.attrParams['shape'].value as number[] :\n            undefined,\n        dtype: node.attrParams['dtype'] ?\n            node.attrParams['dtype'].value as DataType :\n            undefined\n      };\n    });\n  }\n\n  get outputs(): TensorInfo[] {\n    return this._outputs.map(node => {\n      return {\n        name: node.name,\n        shape: node.attrParams['shape'] ?\n            node.attrParams['shape'].value as number[] :\n            undefined,\n        dtype: node.attrParams['dtype'] ?\n            node.attrParams['dtype'].value as DataType :\n            undefined\n      };\n    });\n  }\n\n  get inputNodes(): string[] {\n    return this._inputs.map(node => node.signatureKey || node.name);\n  }\n\n  get outputNodes(): string[] {\n    return this._outputs.map((node) => {\n      const name = node.signatureKey || node.name;\n      return node.defaultOutput ? (`${name}:${node.defaultOutput}`) : name;\n    });\n  }\n\n  get functions(): {[key: string]: ISignatureDef} {\n    return Object.keys(this._functions).reduce((map, key) => {\n      map[key] = this._functions[key].signature;\n      return map;\n    }, {} as {[key: string]: ISignatureDef});\n  }\n\n  /**\n   *\n   * @param graph Graph the model or function graph to be executed.\n   * @param parent When building function exector you need to set the parent\n   * executor. Since the weights and function executor maps are set at parant\n   * level, that function executor can access the function maps and weight maps\n   * through the parent.\n   */\n  constructor(private graph: Graph, private parent?: GraphExecutor) {\n    this._outputs = graph.outputs;\n    this._inputs = graph.inputs;\n    this._initNodes = graph.initNodes;\n    this._signature = graph.signature;\n    this._functions = graph.functions;\n    // create sub-graph executors\n    if (graph.functions != null) {\n      Object.keys(graph.functions).forEach(name => {\n        this._functionExecutorMap[name] =\n            new GraphExecutor(graph.functions[name], this);\n      });\n    }\n  }\n\n  private getCompilationKey(inputs: Node[], outputs: Node[]): string {\n    const sortedInputs = inputs.map(node => node.name).sort();\n    const sortedOutputs = outputs.map(node => node.name).sort();\n    return sortedInputs.join(this.SEPARATOR) + '--' +\n        sortedOutputs.join(this.SEPARATOR);\n  }\n\n  /**\n   * Compiles the inference graph and returns the minimal set of nodes that are\n   * required for execution, in the correct execution order.\n   * @returns {Object} compilation The compile result.\n   * @returns {Node[]} compilation.orderedNodes Nodes in the correct execution\n   *     order.\n   * @returns {Map<string, Node[]>} compilation.nodeLiveUntilMap A map from node\n   *     to disposable nodes after its execution. That is, for a node `x`,\n   *     `nodeLiveUntilMap[x]` indicates all nodes whose intermediate\n   *     tensors should be disposed after `x` is executed.\n   */\n  private compile(inputs: NamedTensorMap, outputs: Node[]):\n      {orderedNodes: Node[], nodeLiveUntilMap: Map<string, Node[]>} {\n    const executionInfo =\n        getExecutionSubgraph(inputs, outputs, this.weightMap, this._initNodes);\n    const {missingInputs, dynamicNode, syncInputs} = executionInfo;\n    if (dynamicNode != null) {\n      throw new Error(\n          `This execution contains the node '${dynamicNode.name}', which has ` +\n          `the dynamic op '${dynamicNode.op}'. Please use ` +\n          `model.executeAsync() instead. Alternatively, to avoid the ` +\n          `dynamic ops, specify the inputs [${syncInputs}]`);\n    }\n\n    if (missingInputs.length > 0) {\n      const outNames = outputs.map(n => n.name);\n      const inNames = Object.keys(inputs);\n      throw new Error(\n          `Cannot compute the outputs [${outNames}] from the provided inputs ` +\n          `[${inNames}]. Missing the following inputs: [${missingInputs}]`);\n    }\n\n    const orderedNodes = getNodesInTopologicalOrder(this.graph, executionInfo);\n    const nodeLiveUntilMap = getNodeLiveUntilMap(orderedNodes);\n    return {orderedNodes, nodeLiveUntilMap};\n  }\n\n  private cloneAndKeepTensor(tensor: Tensor) {\n    if (tensor == null) {\n      return null;\n    }\n    const clone = tensor.clone();\n    // Keep the clone because`model.execute()` may be called within\n    // a `tidy()`, but the user may inspect these tensors after the\n    // tidy.\n    keep(clone);\n    return clone;\n  }\n\n  private cloneTensorList(tensors: Tensor[]) {\n    if (!tensors) {\n      return null;\n    }\n    const clonedTensor = tensors.map(tensor => {\n      return this.cloneAndKeepTensor(tensor);\n    });\n    return clonedTensor;\n  }\n\n  private cloneTensorMap(tensorsMap: NamedTensorsMap): NamedTensorsMap {\n    return Object.fromEntries(\n        Object.entries(tensorsMap).map(([name, tensorsList]) => {\n          return [name, this.cloneTensorList(tensorsList)];\n        }));\n  }\n\n  /**\n   * Executes the inference for given input tensors.\n   * @param inputs Tensor map for the model inputs, keyed by the input node\n   * names.\n   * @param outputs Optional. output node name from the Tensorflow model, if\n   * no outputs are specified, the default outputs of the model would be used.\n   * You can inspect intermediate nodes of the model by adding them to the\n   * outputs array.\n   */\n  execute(inputs: NamedTensorMap, outputs?: string[]): Tensor[] {\n    // Dispose any tensors from a prior run to avoid leaking them.\n    this.disposeIntermediateTensors();\n    inputs = this.mapInputs(inputs);\n    const names = Object.keys(inputs).sort();\n    this.checkInputs(inputs);\n    this.checkInputShapeAndType(inputs);\n    outputs = this.mapOutputs(outputs);\n    this.checkOutputs(outputs);\n    const inputNodes =\n        names.map(name => this.graph.nodes[parseNodeName(name)[0]]);\n    const outputNodeNames = outputs.map(name => parseNodeName(name)[0]);\n    const outputNodeNameSet = new Set(outputNodeNames);\n    let outputNodes = outputNodeNames.map(name => this.graph.nodes[name]);\n    // If no outputs are specified, then use the default outputs of the model.\n    if (outputNodes.length === 0) {\n      outputNodes = this._outputs;\n    }\n\n    const compilationKey = this.getCompilationKey(inputNodes, outputNodes);\n\n    // Do nothing if the compiled graph cache contains the input.\n    let compilation = this.compiledMap.get(compilationKey);\n    if (compilation == null) {\n      compilation = this.compile(inputs, outputNodes);\n      this.compiledMap.set(compilationKey, compilation);\n    }\n\n    // Keep tensors if KEEP_INTERMEDIATE_TENSORS is on.\n    try {\n      this.keepIntermediateTensors = env().getBool('KEEP_INTERMEDIATE_TENSORS');\n    } catch (e) {\n      this.keepIntermediateTensors = false;\n      console.warn(e.message);\n    }\n    const tensorArrayMap: TensorArrayMap = {};\n    const tensorListMap: TensorListMap = {};\n\n    return tidy(() => {\n      const context = new ExecutionContext(\n          this.weightMap, tensorArrayMap, tensorListMap,\n          this.functionExecutorMap, this.parseNodeNameCache);\n      const tensorsMap: NamedTensorsMap = {...this.weightMap};\n      if (this.keepIntermediateTensors) {\n        this.clonedTensorsMap = this.cloneTensorMap(this.weightMap);\n      }\n\n      Object.keys(inputs).forEach(name => {\n        const [nodeName, index] = parseNodeName(name, context);\n        const tensors: Tensor[] = [];\n        tensors[index] = inputs[name];\n        tensorsMap[nodeName] = tensors;\n        if (this.keepIntermediateTensors) {\n          this.clonedTensorsMap[nodeName] = this.cloneTensorList(tensors);\n        }\n      });\n\n      const tensorsToKeep = this.getFrozenTensorIds(tensorsMap);\n      const {orderedNodes, nodeLiveUntilMap} = compilation;\n      for (const node of orderedNodes) {\n        if (tensorsMap[node.name]) {\n          continue;\n        }\n        const tensors =\n            executeOp(node, tensorsMap, context, this._resourceManager) as\n            Tensor[];\n        if (util.isPromise(tensors)) {\n          throw new Error(\n              `The execution of the op '${node.op}' returned a promise. ` +\n              `Please use model.executeAsync() instead.`);\n        }\n        tensorsMap[node.name] = tensors;\n        if (this.keepIntermediateTensors) {\n          this.clonedTensorsMap[node.name] = this.cloneTensorList(tensors);\n        }\n        this.checkTensorForDisposalWithNodeLiveUntilInfo(\n            node, tensorsMap, context, tensorsToKeep, outputNodeNameSet,\n            nodeLiveUntilMap.get(node.name));\n      }\n\n      // dispose the context for the root executor\n      if (this.parent == null) {\n        context.dispose(tensorsToKeep);\n      }\n\n      return outputs.map(name => getTensor(name, tensorsMap, context));\n    });\n  }\n\n  private getFrozenTensorIds(tensorMap: NamedTensorsMap): Set<number> {\n    const ids = [].concat.apply(\n        [],\n        Object.keys(tensorMap)\n            .map(key => tensorMap[key])\n            .map(tensors => tensors.map(tensor => tensor.id)));\n    return new Set(ids);\n  }\n\n  private checkTensorForDisposal(\n      nodeName: string, node: Node, tensorMap: NamedTensorsMap,\n      context: ExecutionContext, tensorsToKeep: Set<number>,\n      outputNodeNameSet: Set<string>,\n      intermediateTensorConsumerCount: {[key: string]: number}) {\n    // Skip output nodes and any control flow nodes, since its dependency is\n    // tricky to track correctly.\n    if (isControlFlow(node) || outputNodeNameSet.has(nodeName)) {\n      return;\n    }\n\n    for (const tensor of tensorMap[nodeName]) {\n      if (tensor == null) {\n        continue;\n      }\n      intermediateTensorConsumerCount[tensor.id] =\n          (intermediateTensorConsumerCount[tensor.id] || 0) +\n          node.children.length;\n    }\n\n    for (const input of node.inputs) {\n      // Skip any control flow nodes, since its dependency is tricky to track\n      // correctly.\n      if (isControlFlow(input)) {\n        continue;\n      }\n\n      const tensors =\n          getTensorsForCurrentContext(input.name, tensorMap, context);\n      if (tensors == null) {\n        continue;\n      }\n\n      for (const tensor of tensors) {\n        if (!tensor || tensor.kept || tensorsToKeep.has(tensor.id)) {\n          continue;\n        }\n\n        // Only intermediate nodes' tensors have counts set, not marked as\n        // kept, and not in `tensorsToKeep`.\n        // Input and weight nodes' tensors should exist in `tensorsToKeep`.\n        // Output and control flow nodes' tensors should never have count set.\n        const count = intermediateTensorConsumerCount[tensor.id];\n        if (count === 1) {\n          tensor.dispose();\n          delete intermediateTensorConsumerCount[tensor.id];\n        } else if (count != null) {\n          intermediateTensorConsumerCount[tensor.id]--;\n        }\n      }\n    }\n  }\n\n  private checkTensorForDisposalWithNodeLiveUntilInfo(\n      node: Node, tensorMap: NamedTensorsMap, context: ExecutionContext,\n      tensorsToKeep: Set<number>, outputNodeNameSet: Set<string>,\n      liveUntilNodes?: Node[]) {\n    function isNonDisposableNode(node: Node) {\n      // Skip output nodes and any control flow nodes, since its dependency is\n      // tricky to track correctly.\n      return isControlFlow(node) || outputNodeNameSet.has(node.name);\n    }\n\n    if (isControlFlow(node) || liveUntilNodes == null) {\n      return;\n    }\n\n    for (const nodeToDispose of liveUntilNodes) {\n      if (isNonDisposableNode(nodeToDispose)) {\n        continue;\n      }\n      const tensors = getTensorsForCurrentContext(\n          nodeToDispose.name, tensorMap, context);\n      for (const tensor of tensors) {\n        if (!tensor || tensor.kept || tensorsToKeep.has(tensor.id)) {\n          continue;\n        }\n        tensor.dispose();\n      }\n    }\n  }\n\n  /**\n   * Executes the inference for given input tensors in Async fashion.\n   * @param inputs Tensor map for the model inputs, keyed by the input node\n   * names.\n   * @param outputs output node name from the Tensorflow model, if no outputs\n   * are specified, the default outputs of the model would be used. You can\n   * inspect intermediate nodes of the model by adding them to the outputs\n   * array.\n   */\n  async executeAsync(inputs: NamedTensorMap, outputs?: string[]):\n      Promise<Tensor[]> {\n    return this._executeAsync(inputs, outputs);\n  }\n\n  disposeIntermediateTensors() {\n    if (!this.clonedTensorsMap) {\n      return;\n    }\n    Object.values(this.clonedTensorsMap).forEach(tensorsList => {\n      for (const tensor of tensorsList) {\n        if (tensor && !tensor.isDisposed) {\n          tensor.dispose();\n        }\n      }\n    });\n\n    this.clonedTensorsMap = null;\n  }\n\n  getIntermediateTensors(): NamedTensorsMap {\n    return this.clonedTensorsMap;\n  }\n\n  /**\n   * Executes the inference for given input tensors in Async fashion.\n   * @param inputs Tensor map for the model inputs, keyed by the input node\n   * names.\n   * @param outputs Optional. output node name from the Tensorflow model,\n   * if no outputs are specified, the default outputs of the model would be\n   * used. You can inspect intermediate nodes of the model by adding them to\n   * the outputs array.\n   * @param isFunctionExecution Optional. Flag for executing a function.\n   * @param tensorArrayMap Optional, global TensorArray map by id. Used for\n   * function execution.\n   * @param tensorArrayMap Optional global TensorList map by id. Used for\n   * function execution.\n   */\n  private async _executeAsync(\n      inputs: NamedTensorMap, outputs?: string[], isFunctionExecution = false,\n      tensorArrayMap: TensorArrayMap = {},\n      tensorListMap: TensorListMap = {}): Promise<Tensor[]> {\n    // Dispose any tensors from a prior run to avoid leaking them.\n    this.disposeIntermediateTensors();\n    if (!isFunctionExecution) {\n      inputs = this.mapInputs(inputs);\n      this.checkInputs(inputs);\n      this.checkInputShapeAndType(inputs);\n      outputs = this.mapOutputs(outputs);\n      this.checkOutputs(outputs);\n    }\n\n    // Keep tensors if KEEP_INTERMEDIATE_TENSORS is on.\n    try {\n      this.keepIntermediateTensors = env().getBool('KEEP_INTERMEDIATE_TENSORS');\n    } catch (e) {\n      this.keepIntermediateTensors = false;\n      console.warn(e.message);\n    }\n\n    const context = new ExecutionContext(\n        this.weightMap, tensorArrayMap, tensorListMap, this.functionExecutorMap,\n        this.parseNodeNameCache);\n\n    if (this.keepIntermediateTensors) {\n      this.clonedTensorsMap = this.cloneTensorMap(this.weightMap);\n    }\n\n    // Graph with control flow op requires runtime evaluation of the execution\n    // order, while without control flow the execution order is pre-determined\n    // in the compile method.\n    const tensorsMap = await this.executeWithControlFlow(\n        inputs, context, outputs, isFunctionExecution);\n    const results = outputs.map(name => getTensor(name, tensorsMap, context));\n\n    // dispose all the intermediate tensors\n    const outputIds = results.map(t => t.id);\n    const inputIds = Object.keys(inputs).map(name => inputs[name].id);\n    const keepIds =\n        new Set<number>([...outputIds, ...inputIds, ...this.weightIds]);\n\n    Object.values(tensorsMap).forEach(tensorsList => {\n      tensorsList.forEach(tensor => {\n        if (tensor && !tensor.isDisposed && !keepIds.has(tensor.id)) {\n          tensor.dispose();\n        }\n      });\n    });\n\n    // dispose the context for the root executor\n    if (this.parent == null) {\n      context.dispose(keepIds);\n    }\n\n    return results;\n  }\n\n  async executeFunctionAsync(\n      inputs: Tensor[], tensorArrayMap: TensorArrayMap,\n      tensorListMap: TensorListMap): Promise<Tensor[]> {\n    const mappedInputs = inputs.reduce((map, tensor, index) => {\n      map[this.inputs[index].name] = tensor;\n      return map;\n    }, {} as NamedTensorMap);\n\n    return this._executeAsync(\n        mappedInputs, this.outputNodes, true, tensorArrayMap, tensorListMap);\n  }\n\n  /**\n   * When there are control flow nodes in the graph, the graph execution use\n   * ExecutionContext to keep track of the frames and loop iterators.\n   * @param inputs placeholder tensors for the graph.\n   * @param context the execution context object for current execution.\n   * @param outputNames Optional. output node name from the Tensorflow model,\n   * if no outputs are specified, the default outputs of the model would be\n   * used. You can inspect intermediate nodes of the model by adding them to\n   * the outputs array.\n   * @param isFunctionExecution Flag for executing a function.\n   */\n  private async executeWithControlFlow(\n      inputs: NamedTensorMap, context: ExecutionContext, outputNames?: string[],\n      isFunctionExecution?: boolean): Promise<NamedTensorsMap> {\n    const names = Object.keys(inputs);\n    const inputNodes =\n        names.map(name => this.graph.nodes[parseNodeName(name)[0]]);\n    const outputNodeNames = outputNames.map(name => parseNodeName(name)[0]);\n    const outputNodeNameSet = new Set(outputNodeNames);\n    let outputNodes = outputNodeNames.map(name => this.graph.nodes[name]);\n\n    // If no outputs are specified, then use the default outputs of the model.\n    if (outputNodes.length === 0) {\n      outputNodes = this._outputs;\n    }\n\n    const {usedNodes, missingInputs, dynamicNode, syncInputs} =\n        getExecutionSubgraph(\n            inputs, outputNodes, this.weightMap, this._initNodes);\n\n    // First nodes to execute include inputNodes, weights, and initNodes.\n    const stack: NodeWithContexts[] = [\n      ...inputNodes, ...this.graph.weights, ...(this._initNodes || [])\n    ].map(node => {\n      return {node, contexts: context.currentContext};\n    });\n    const tensorsMap: NamedTensorsMap = {...this.weightMap};\n    Object.keys(inputs).forEach(name => {\n      const [nodeName, index] = parseNodeName(name);\n      const tensors: Tensor[] = [];\n      tensors[index] = inputs[name];\n      tensorsMap[nodeName] = tensors;\n    });\n    const intermediateTensorConsumerCount: {[key: number]: number} = {};\n    const tensorsToKeep = this.getFrozenTensorIds(tensorsMap);\n    const added: {[key: string]: boolean} = {};\n    while (stack.length > 0) {\n      const promises = this.processStack(\n          inputNodes, stack, context, tensorsMap, added, tensorsToKeep,\n          outputNodeNameSet, intermediateTensorConsumerCount, usedNodes);\n      await Promise.all(promises);\n    }\n    if (dynamicNode == null && !isFunctionExecution) {\n      console.warn(\n          `This model execution did not contain any nodes with control flow ` +\n          `or dynamic output shapes. You can use model.execute() instead.`);\n    }\n    const missingOutputs =\n        outputNodes\n            .filter(\n                node => !isControlFlow(node) &&\n                    !getTensor(node.name, tensorsMap, context))\n            .map(node => node.name);\n    if (missingOutputs.length > 0) {\n      let alternativeMsg = '';\n      if (dynamicNode != null) {\n        alternativeMsg =\n            `Alternatively, to avoid the dynamic ops, use model.execute() ` +\n            `and specify the inputs [${syncInputs}]`;\n      }\n      throw new Error(\n          `Cannot compute the outputs [${missingOutputs}] from the provided ` +\n          `inputs [${names}]. Consider providing the following inputs: ` +\n          `[${missingInputs}]. ${alternativeMsg}`);\n    }\n    return tensorsMap;\n  }\n\n  private processStack(\n      inputNodes: Node[], stack: NodeWithContexts[], context: ExecutionContext,\n      tensorMap: NamedTensorsMap, added: {[key: string]: boolean},\n      tensorsToKeep: Set<number>, outputNodeNameSet: Set<string>,\n      intermediateTensorConsumerCount: {[key: number]: number},\n      usedNodes: Set<string>) {\n    const promises: Array<Promise<Tensor[]>> = [];\n    while (stack.length > 0) {\n      const item = stack.pop();\n      context.currentContext = item.contexts;\n      let nodeName = '';\n      // The tensor of the Enter op with isConstant set should be set\n      // in the parent scope, so it will be available as constant for the\n      // whole loop.\n      if (item.node.op === 'Enter' &&\n          getParamValue('isConstant', item.node, tensorMap, context)) {\n        [nodeName] = getNodeNameAndIndex(item.node.name, context);\n      }\n\n      // only process nodes that are not in the tensorMap yet, this include\n      // inputNodes and internal initNodes.\n      if (tensorMap[item.node.name] == null) {\n        const tensors =\n            executeOp(item.node, tensorMap, context, this._resourceManager);\n        if (!nodeName) {\n          [nodeName] = getNodeNameAndIndex(item.node.name, context);\n        }\n        const currentContext = context.currentContext;\n        if (util.isPromise(tensors)) {\n          promises.push(tensors.then(t => {\n            tensorMap[nodeName] = t;\n            if (this.keepIntermediateTensors) {\n              this.clonedTensorsMap[nodeName] = this.cloneTensorList(t);\n            }\n            context.currentContext = currentContext;\n            this.checkTensorForDisposal(\n                nodeName, item.node, tensorMap, context, tensorsToKeep,\n                outputNodeNameSet, intermediateTensorConsumerCount);\n            this.processChildNodes(\n                item.node, stack, context, tensorMap, added, usedNodes);\n            return t;\n          }));\n        } else {\n          tensorMap[nodeName] = tensors;\n          if (this.keepIntermediateTensors) {\n            this.clonedTensorsMap[nodeName] = this.cloneTensorList(tensors);\n          }\n          this.checkTensorForDisposal(\n              nodeName, item.node, tensorMap, context, tensorsToKeep,\n              outputNodeNameSet, intermediateTensorConsumerCount);\n          this.processChildNodes(\n              item.node, stack, context, tensorMap, added, usedNodes);\n        }\n      } else {\n        this.processChildNodes(\n            item.node, stack, context, tensorMap, added, usedNodes);\n      }\n    }\n    return promises;\n  }\n\n  private processChildNodes(\n      node: Node, stack: NodeWithContexts[], context: ExecutionContext,\n      tensorMap: NamedTensorsMap, added: {[key: string]: boolean},\n      usedNodes: Set<string>) {\n    node.children.forEach((childNode) => {\n      const [nodeName, ] = getNodeNameAndIndex(childNode.name, context);\n      if (added[nodeName] || !usedNodes.has(childNode.name)) {\n        return;\n      }\n      // Merge op can be pushed if any of its inputs has value.\n      if (childNode.op === 'Merge') {\n        if (childNode.inputNames.some(name => {\n              return !!getTensor(name, tensorMap, context);\n            })) {\n          added[nodeName] = true;\n          stack.push({contexts: context.currentContext, node: childNode});\n        }\n      } else  // Otherwise all inputs must to have value.\n          if (childNode.inputNames.every(name => {\n                return !!getTensor(name, tensorMap, context);\n              })) {\n        added[nodeName] = true;\n        stack.push({contexts: context.currentContext, node: childNode});\n      }\n    });\n  }\n\n  /**\n   * Releases the memory used by the weight tensors.\n   */\n  dispose() {\n    Object.keys(this.weightMap)\n        .forEach(\n            key => this.weightMap[key].forEach(tensor => tensor.dispose()));\n  }\n\n  private checkInputShapeAndType(inputs: NamedTensorMap) {\n    Object.keys(inputs).forEach(name => {\n      const input = inputs[name];\n      const [nodeName, ] = parseNodeName(name);\n      const node = this.graph.nodes[nodeName];\n      if (node.attrParams['shape'] && node.attrParams['shape'].value) {\n        const shape = node.attrParams['shape'].value as number[];\n        const match = shape.length === input.shape.length &&\n            input.shape.every(\n                (dim, index) => shape[index] === -1 || shape[index] === dim);\n        util.assert(\n            match,\n            () => `The shape of dict['${node.name}'] provided in ` +\n                `model.execute(dict) must be [${shape}], but was ` +\n                `[${input.shape}]`);\n      }\n      if (node.attrParams['dtype'] && node.attrParams['dtype'].value) {\n        util.assert(\n            input.dtype === node.attrParams['dtype'].value as string,\n            () => `The dtype of dict['${node.name}'] provided in ` +\n                `model.execute(dict) must be ` +\n                `${node.attrParams['dtype'].value}, but was ${input.dtype}`);\n      }\n    });\n  }\n\n  private mapInputs(inputs: NamedTensorMap) {\n    const result: NamedTensorMap = {};\n    for (const inputName in inputs) {\n      const tensor = this._signature ?.inputs ?.[inputName];\n      if (tensor != null) {\n        result[tensor.name] = inputs[inputName];\n      } else {\n        result[inputName] = inputs[inputName];\n      }\n    }\n    return result;\n  }\n\n  private checkInputs(inputs: NamedTensorMap) {\n    const notInGraph = Object.keys(inputs).filter(name => {\n      const [nodeName] = parseNodeName(name);\n      return this.graph.nodes[nodeName] == null;\n    });\n    if (notInGraph.length > 0) {\n      throw new Error(\n          `The dict provided in model.execute(dict) has ` +\n          `keys: [${notInGraph}] that are not part of graph`);\n    }\n  }\n\n  private mapOutputs(outputs: string[]) {\n    return outputs.map(name => {\n      const tensor = this._signature ?.outputs ?.[name];\n      if (tensor != null) {\n        return tensor.name;\n      }\n      return name;\n    }, {});\n  }\n\n  private checkOutputs(outputs: string[]): void {\n    outputs.forEach(name => {\n      const [normalizedName] = parseNodeName(name);\n      if (!this.graph.nodes[normalizedName]) {\n        throw new Error(`The output '${name}' is not found in the graph`);\n      }\n    });\n  }\n}\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAiBA,SAAkBA,GAAG,EAAEC,IAAI,EAA0BC,IAAI,EAAEC,IAAI,QAAO,uBAAuB;AAI7F,SAAQC,mBAAmB,EAAEC,aAAa,EAAEC,SAAS,EAAEC,2BAA2B,EAAEC,aAAa,QAAO,+BAA+B;AACvI,SAAQC,SAAS,QAAO,kCAAkC;AAG1D,SAAQC,gBAAgB,QAA6B,qBAAqB;AAC1E,SAAQC,oBAAoB,EAAEC,mBAAmB,EAAEC,0BAA0B,EAAEC,aAAa,QAAO,kBAAkB;AASrH,OAAM,MAAOC,aAAa;EAgBxB,IAAIC,SAASA,CAAA;IACX,OAAO,IAAI,CAACC,MAAM,GAAG,IAAI,CAACA,MAAM,CAACD,SAAS,GAAG,IAAI,CAACE,UAAU;EAC9D;EAEA,IAAIC,mBAAmBA,CAAA;IACrB,OAAO,IAAI,CAACF,MAAM,GAAG,IAAI,CAACA,MAAM,CAACE,mBAAmB,GAC/B,IAAI,CAACC,oBAAoB;EAChD;EAEA,IAAIC,SAASA,CAAA;IACX,OAAO,IAAI,CAACJ,MAAM,GAAG,IAAI,CAACA,MAAM,CAACI,SAAS,GAAG,IAAI,CAACC,UAAU;EAC9D;EAEA,IAAID,SAASA,CAACA,SAA0B;IACtC,MAAML,SAAS,GAAGO,MAAM,CAACC,IAAI,CAACH,SAAS,CAAC,CAACI,GAAG,CACxCC,GAAG,IAAIL,SAAS,CAACK,GAAG,CAAC,CAACD,GAAG,CAACE,MAAM,IAAIA,MAAM,CAACC,EAAE,CAAC,CAAC;IACnD,IAAI,CAACV,UAAU,GAAG,EAAE,CAACW,MAAM,CAAC,GAAGb,SAAS,CAAC;IACzC,IAAI,CAACM,UAAU,GAAGD,SAAS;EAC7B;EAEA;;;;EAIA,IAAIS,eAAeA,CAACA,eAAgC;IAClD,IAAI,CAACC,gBAAgB,GAAGD,eAAe;EACzC;EAEA,IAAIE,MAAMA,CAAA;IACR,OAAO,IAAI,CAACC,OAAO,CAACR,GAAG,CAACS,IAAI,IAAG;MAC7B,OAAO;QACLC,IAAI,EAAED,IAAI,CAACC,IAAI;QACfC,KAAK,EAAEF,IAAI,CAACG,UAAU,CAAC,OAAO,CAAC,GAC3BH,IAAI,CAACG,UAAU,CAAC,OAAO,CAAC,CAACC,KAAiB,GAC1CC,SAAS;QACbC,KAAK,EAAEN,IAAI,CAACG,UAAU,CAAC,OAAO,CAAC,GAC3BH,IAAI,CAACG,UAAU,CAAC,OAAO,CAAC,CAACC,KAAiB,GAC1CC;OACL;IACH,CAAC,CAAC;EACJ;EAEA,IAAIE,OAAOA,CAAA;IACT,OAAO,IAAI,CAACC,QAAQ,CAACjB,GAAG,CAACS,IAAI,IAAG;MAC9B,OAAO;QACLC,IAAI,EAAED,IAAI,CAACC,IAAI;QACfC,KAAK,EAAEF,IAAI,CAACG,UAAU,CAAC,OAAO,CAAC,GAC3BH,IAAI,CAACG,UAAU,CAAC,OAAO,CAAC,CAACC,KAAiB,GAC1CC,SAAS;QACbC,KAAK,EAAEN,IAAI,CAACG,UAAU,CAAC,OAAO,CAAC,GAC3BH,IAAI,CAACG,UAAU,CAAC,OAAO,CAAC,CAACC,KAAiB,GAC1CC;OACL;IACH,CAAC,CAAC;EACJ;EAEA,IAAII,UAAUA,CAAA;IACZ,OAAO,IAAI,CAACV,OAAO,CAACR,GAAG,CAACS,IAAI,IAAIA,IAAI,CAACU,YAAY,IAAIV,IAAI,CAACC,IAAI,CAAC;EACjE;EAEA,IAAIU,WAAWA,CAAA;IACb,OAAO,IAAI,CAACH,QAAQ,CAACjB,GAAG,CAAES,IAAI,IAAI;MAChC,MAAMC,IAAI,GAAGD,IAAI,CAACU,YAAY,IAAIV,IAAI,CAACC,IAAI;MAC3C,OAAOD,IAAI,CAACY,aAAa,GAAI,GAAGX,IAAI,IAAID,IAAI,CAACY,aAAa,EAAE,GAAIX,IAAI;IACtE,CAAC,CAAC;EACJ;EAEA,IAAIY,SAASA,CAAA;IACX,OAAOxB,MAAM,CAACC,IAAI,CAAC,IAAI,CAACwB,UAAU,CAAC,CAACC,MAAM,CAAC,CAACxB,GAAG,EAAEC,GAAG,KAAI;MACtDD,GAAG,CAACC,GAAG,CAAC,GAAG,IAAI,CAACsB,UAAU,CAACtB,GAAG,CAAC,CAACwB,SAAS;MACzC,OAAOzB,GAAG;IACZ,CAAC,EAAE,EAAoC,CAAC;EAC1C;EAEA;;;;;;;;EAQA0B,YAAoBC,KAAY,EAAUnC,MAAsB;IAA5C,KAAAmC,KAAK,GAALA,KAAK;IAAiB,KAAAnC,MAAM,GAANA,MAAM;IAjGxC,KAAAoC,WAAW,GAAG,IAAIC,GAAG,EAA2C;IAChE,KAAAC,kBAAkB,GAAG,IAAID,GAAG,EAAqC;IACjE,KAAAhC,UAAU,GAAoB,EAAE;IAMhC,KAAAkC,SAAS,GAAG,GAAG;IACf,KAAAR,UAAU,GAA2B,EAAE;IACvC,KAAA5B,oBAAoB,GAAsC,EAAE;IAG5D,KAAAqC,uBAAuB,GAAG,KAAK;IAqFrC,IAAI,CAACf,QAAQ,GAAGU,KAAK,CAACX,OAAO;IAC7B,IAAI,CAACR,OAAO,GAAGmB,KAAK,CAACpB,MAAM;IAC3B,IAAI,CAAC0B,UAAU,GAAGN,KAAK,CAACO,SAAS;IACjC,IAAI,CAACC,UAAU,GAAGR,KAAK,CAACF,SAAS;IACjC,IAAI,CAACF,UAAU,GAAGI,KAAK,CAACL,SAAS;IACjC;IACA,IAAIK,KAAK,CAACL,SAAS,IAAI,IAAI,EAAE;MAC3BxB,MAAM,CAACC,IAAI,CAAC4B,KAAK,CAACL,SAAS,CAAC,CAACc,OAAO,CAAC1B,IAAI,IAAG;QAC1C,IAAI,CAACf,oBAAoB,CAACe,IAAI,CAAC,GAC3B,IAAIpB,aAAa,CAACqC,KAAK,CAACL,SAAS,CAACZ,IAAI,CAAC,EAAE,IAAI,CAAC;MACpD,CAAC,CAAC;;EAEN;EAEQ2B,iBAAiBA,CAAC9B,MAAc,EAAES,OAAe;IACvD,MAAMsB,YAAY,GAAG/B,MAAM,CAACP,GAAG,CAACS,IAAI,IAAIA,IAAI,CAACC,IAAI,CAAC,CAAC6B,IAAI,EAAE;IACzD,MAAMC,aAAa,GAAGxB,OAAO,CAAChB,GAAG,CAACS,IAAI,IAAIA,IAAI,CAACC,IAAI,CAAC,CAAC6B,IAAI,EAAE;IAC3D,OAAOD,YAAY,CAACG,IAAI,CAAC,IAAI,CAACV,SAAS,CAAC,GAAG,IAAI,GAC3CS,aAAa,CAACC,IAAI,CAAC,IAAI,CAACV,SAAS,CAAC;EACxC;EAEA;;;;;;;;;;;EAWQW,OAAOA,CAACnC,MAAsB,EAAES,OAAe;IAErD,MAAM2B,aAAa,GACfzD,oBAAoB,CAACqB,MAAM,EAAES,OAAO,EAAE,IAAI,CAACpB,SAAS,EAAE,IAAI,CAACqC,UAAU,CAAC;IAC1E,MAAM;MAACW,aAAa;MAAEC,WAAW;MAAEC;IAAU,CAAC,GAAGH,aAAa;IAC9D,IAAIE,WAAW,IAAI,IAAI,EAAE;MACvB,MAAM,IAAIE,KAAK,CACX,qCAAqCF,WAAW,CAACnC,IAAI,eAAe,GACpE,mBAAmBmC,WAAW,CAACG,EAAE,gBAAgB,GACjD,4DAA4D,GAC5D,oCAAoCF,UAAU,GAAG,CAAC;;IAGxD,IAAIF,aAAa,CAACK,MAAM,GAAG,CAAC,EAAE;MAC5B,MAAMC,QAAQ,GAAGlC,OAAO,CAAChB,GAAG,CAACmD,CAAC,IAAIA,CAAC,CAACzC,IAAI,CAAC;MACzC,MAAM0C,OAAO,GAAGtD,MAAM,CAACC,IAAI,CAACQ,MAAM,CAAC;MACnC,MAAM,IAAIwC,KAAK,CACX,+BAA+BG,QAAQ,6BAA6B,GACpE,IAAIE,OAAO,qCAAqCR,aAAa,GAAG,CAAC;;IAGvE,MAAMS,YAAY,GAAGjE,0BAA0B,CAAC,IAAI,CAACuC,KAAK,EAAEgB,aAAa,CAAC;IAC1E,MAAMW,gBAAgB,GAAGnE,mBAAmB,CAACkE,YAAY,CAAC;IAC1D,OAAO;MAACA,YAAY;MAAEC;IAAgB,CAAC;EACzC;EAEQC,kBAAkBA,CAACrD,MAAc;IACvC,IAAIA,MAAM,IAAI,IAAI,EAAE;MAClB,OAAO,IAAI;;IAEb,MAAMsD,KAAK,GAAGtD,MAAM,CAACsD,KAAK,EAAE;IAC5B;IACA;IACA;IACAhF,IAAI,CAACgF,KAAK,CAAC;IACX,OAAOA,KAAK;EACd;EAEQC,eAAeA,CAACC,OAAiB;IACvC,IAAI,CAACA,OAAO,EAAE;MACZ,OAAO,IAAI;;IAEb,MAAMC,YAAY,GAAGD,OAAO,CAAC1D,GAAG,CAACE,MAAM,IAAG;MACxC,OAAO,IAAI,CAACqD,kBAAkB,CAACrD,MAAM,CAAC;IACxC,CAAC,CAAC;IACF,OAAOyD,YAAY;EACrB;EAEQC,cAAcA,CAACC,UAA2B;IAChD,OAAO/D,MAAM,CAACgE,WAAW,CACrBhE,MAAM,CAACiE,OAAO,CAACF,UAAU,CAAC,CAAC7D,GAAG,CAAC,CAAC,CAACU,IAAI,EAAEsD,WAAW,CAAC,KAAI;MACrD,OAAO,CAACtD,IAAI,EAAE,IAAI,CAAC+C,eAAe,CAACO,WAAW,CAAC,CAAC;IAClD,CAAC,CAAC,CAAC;EACT;EAEA;;;;;;;;;EASAC,OAAOA,CAAC1D,MAAsB,EAAES,OAAkB;IAChD;IACA,IAAI,CAACkD,0BAA0B,EAAE;IACjC3D,MAAM,GAAG,IAAI,CAAC4D,SAAS,CAAC5D,MAAM,CAAC;IAC/B,MAAM6D,KAAK,GAAGtE,MAAM,CAACC,IAAI,CAACQ,MAAM,CAAC,CAACgC,IAAI,EAAE;IACxC,IAAI,CAAC8B,WAAW,CAAC9D,MAAM,CAAC;IACxB,IAAI,CAAC+D,sBAAsB,CAAC/D,MAAM,CAAC;IACnCS,OAAO,GAAG,IAAI,CAACuD,UAAU,CAACvD,OAAO,CAAC;IAClC,IAAI,CAACwD,YAAY,CAACxD,OAAO,CAAC;IAC1B,MAAME,UAAU,GACZkD,KAAK,CAACpE,GAAG,CAACU,IAAI,IAAI,IAAI,CAACiB,KAAK,CAAC8C,KAAK,CAAC1F,aAAa,CAAC2B,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC/D,MAAMgE,eAAe,GAAG1D,OAAO,CAAChB,GAAG,CAACU,IAAI,IAAI3B,aAAa,CAAC2B,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;IACnE,MAAMiE,iBAAiB,GAAG,IAAIC,GAAG,CAACF,eAAe,CAAC;IAClD,IAAItD,WAAW,GAAGsD,eAAe,CAAC1E,GAAG,CAACU,IAAI,IAAI,IAAI,CAACiB,KAAK,CAAC8C,KAAK,CAAC/D,IAAI,CAAC,CAAC;IACrE;IACA,IAAIU,WAAW,CAAC6B,MAAM,KAAK,CAAC,EAAE;MAC5B7B,WAAW,GAAG,IAAI,CAACH,QAAQ;;IAG7B,MAAM4D,cAAc,GAAG,IAAI,CAACxC,iBAAiB,CAACnB,UAAU,EAAEE,WAAW,CAAC;IAEtE;IACA,IAAI0D,WAAW,GAAG,IAAI,CAAClD,WAAW,CAACmD,GAAG,CAACF,cAAc,CAAC;IACtD,IAAIC,WAAW,IAAI,IAAI,EAAE;MACvBA,WAAW,GAAG,IAAI,CAACpC,OAAO,CAACnC,MAAM,EAAEa,WAAW,CAAC;MAC/C,IAAI,CAACQ,WAAW,CAACoD,GAAG,CAACH,cAAc,EAAEC,WAAW,CAAC;;IAGnD;IACA,IAAI;MACF,IAAI,CAAC9C,uBAAuB,GAAGzD,GAAG,EAAE,CAAC0G,OAAO,CAAC,2BAA2B,CAAC;KAC1E,CAAC,OAAOC,CAAC,EAAE;MACV,IAAI,CAAClD,uBAAuB,GAAG,KAAK;MACpCmD,OAAO,CAACC,IAAI,CAACF,CAAC,CAACG,OAAO,CAAC;;IAEzB,MAAMC,cAAc,GAAmB,EAAE;IACzC,MAAMC,aAAa,GAAkB,EAAE;IAEvC,OAAO9G,IAAI,CAAC,MAAK;MACf,MAAM+G,OAAO,GAAG,IAAIvG,gBAAgB,CAChC,IAAI,CAACW,SAAS,EAAE0F,cAAc,EAAEC,aAAa,EAC7C,IAAI,CAAC7F,mBAAmB,EAAE,IAAI,CAACoC,kBAAkB,CAAC;MACtD,MAAM+B,UAAU,GAAA/D,MAAA,CAAA2F,MAAA,KAAwB,IAAI,CAAC7F,SAAS,CAAC;MACvD,IAAI,IAAI,CAACoC,uBAAuB,EAAE;QAChC,IAAI,CAAC0D,gBAAgB,GAAG,IAAI,CAAC9B,cAAc,CAAC,IAAI,CAAChE,SAAS,CAAC;;MAG7DE,MAAM,CAACC,IAAI,CAACQ,MAAM,CAAC,CAAC6B,OAAO,CAAC1B,IAAI,IAAG;QACjC,MAAM,CAACiF,QAAQ,EAAEC,KAAK,CAAC,GAAG7G,aAAa,CAAC2B,IAAI,EAAE8E,OAAO,CAAC;QACtD,MAAM9B,OAAO,GAAa,EAAE;QAC5BA,OAAO,CAACkC,KAAK,CAAC,GAAGrF,MAAM,CAACG,IAAI,CAAC;QAC7BmD,UAAU,CAAC8B,QAAQ,CAAC,GAAGjC,OAAO;QAC9B,IAAI,IAAI,CAAC1B,uBAAuB,EAAE;UAChC,IAAI,CAAC0D,gBAAgB,CAACC,QAAQ,CAAC,GAAG,IAAI,CAAClC,eAAe,CAACC,OAAO,CAAC;;MAEnE,CAAC,CAAC;MAEF,MAAMmC,aAAa,GAAG,IAAI,CAACC,kBAAkB,CAACjC,UAAU,CAAC;MACzD,MAAM;QAACR,YAAY;QAAEC;MAAgB,CAAC,GAAGwB,WAAW;MACpD,KAAK,MAAMrE,IAAI,IAAI4C,YAAY,EAAE;QAC/B,IAAIQ,UAAU,CAACpD,IAAI,CAACC,IAAI,CAAC,EAAE;UACzB;;QAEF,MAAMgD,OAAO,GACT1E,SAAS,CAACyB,IAAI,EAAEoD,UAAU,EAAE2B,OAAO,EAAE,IAAI,CAAClF,gBAAgB,CAClD;QACZ,IAAI5B,IAAI,CAACqH,SAAS,CAACrC,OAAO,CAAC,EAAE;UAC3B,MAAM,IAAIX,KAAK,CACX,4BAA4BtC,IAAI,CAACuC,EAAE,wBAAwB,GAC3D,0CAA0C,CAAC;;QAEjDa,UAAU,CAACpD,IAAI,CAACC,IAAI,CAAC,GAAGgD,OAAO;QAC/B,IAAI,IAAI,CAAC1B,uBAAuB,EAAE;UAChC,IAAI,CAAC0D,gBAAgB,CAACjF,IAAI,CAACC,IAAI,CAAC,GAAG,IAAI,CAAC+C,eAAe,CAACC,OAAO,CAAC;;QAElE,IAAI,CAACsC,2CAA2C,CAC5CvF,IAAI,EAAEoD,UAAU,EAAE2B,OAAO,EAAEK,aAAa,EAAElB,iBAAiB,EAC3DrB,gBAAgB,CAACyB,GAAG,CAACtE,IAAI,CAACC,IAAI,CAAC,CAAC;;MAGtC;MACA,IAAI,IAAI,CAAClB,MAAM,IAAI,IAAI,EAAE;QACvBgG,OAAO,CAACS,OAAO,CAACJ,aAAa,CAAC;;MAGhC,OAAO7E,OAAO,CAAChB,GAAG,CAACU,IAAI,IAAI7B,SAAS,CAAC6B,IAAI,EAAEmD,UAAU,EAAE2B,OAAO,CAAC,CAAC;IAClE,CAAC,CAAC;EACJ;EAEQM,kBAAkBA,CAACI,SAA0B;IACnD,MAAMC,GAAG,GAAG,EAAE,CAAC/F,MAAM,CAACgG,KAAK,CACvB,EAAE,EACFtG,MAAM,CAACC,IAAI,CAACmG,SAAS,CAAC,CACjBlG,GAAG,CAACC,GAAG,IAAIiG,SAAS,CAACjG,GAAG,CAAC,CAAC,CAC1BD,GAAG,CAAC0D,OAAO,IAAIA,OAAO,CAAC1D,GAAG,CAACE,MAAM,IAAIA,MAAM,CAACC,EAAE,CAAC,CAAC,CAAC;IAC1D,OAAO,IAAIyE,GAAG,CAACuB,GAAG,CAAC;EACrB;EAEQE,sBAAsBA,CAC1BV,QAAgB,EAAElF,IAAU,EAAEyF,SAA0B,EACxDV,OAAyB,EAAEK,aAA0B,EACrDlB,iBAA8B,EAC9B2B,+BAAwD;IAC1D;IACA;IACA,IAAIjH,aAAa,CAACoB,IAAI,CAAC,IAAIkE,iBAAiB,CAAC4B,GAAG,CAACZ,QAAQ,CAAC,EAAE;MAC1D;;IAGF,KAAK,MAAMzF,MAAM,IAAIgG,SAAS,CAACP,QAAQ,CAAC,EAAE;MACxC,IAAIzF,MAAM,IAAI,IAAI,EAAE;QAClB;;MAEFoG,+BAA+B,CAACpG,MAAM,CAACC,EAAE,CAAC,GACtC,CAACmG,+BAA+B,CAACpG,MAAM,CAACC,EAAE,CAAC,IAAI,CAAC,IAChDM,IAAI,CAAC+F,QAAQ,CAACvD,MAAM;;IAG1B,KAAK,MAAMwD,KAAK,IAAIhG,IAAI,CAACF,MAAM,EAAE;MAC/B;MACA;MACA,IAAIlB,aAAa,CAACoH,KAAK,CAAC,EAAE;QACxB;;MAGF,MAAM/C,OAAO,GACT5E,2BAA2B,CAAC2H,KAAK,CAAC/F,IAAI,EAAEwF,SAAS,EAAEV,OAAO,CAAC;MAC/D,IAAI9B,OAAO,IAAI,IAAI,EAAE;QACnB;;MAGF,KAAK,MAAMxD,MAAM,IAAIwD,OAAO,EAAE;QAC5B,IAAI,CAACxD,MAAM,IAAIA,MAAM,CAACwG,IAAI,IAAIb,aAAa,CAACU,GAAG,CAACrG,MAAM,CAACC,EAAE,CAAC,EAAE;UAC1D;;QAGF;QACA;QACA;QACA;QACA,MAAMwG,KAAK,GAAGL,+BAA+B,CAACpG,MAAM,CAACC,EAAE,CAAC;QACxD,IAAIwG,KAAK,KAAK,CAAC,EAAE;UACfzG,MAAM,CAAC+F,OAAO,EAAE;UAChB,OAAOK,+BAA+B,CAACpG,MAAM,CAACC,EAAE,CAAC;SAClD,MAAM,IAAIwG,KAAK,IAAI,IAAI,EAAE;UACxBL,+BAA+B,CAACpG,MAAM,CAACC,EAAE,CAAC,EAAE;;;;EAIpD;EAEQ6F,2CAA2CA,CAC/CvF,IAAU,EAAEyF,SAA0B,EAAEV,OAAyB,EACjEK,aAA0B,EAAElB,iBAA8B,EAC1DiC,cAAuB;IACzB,SAASC,mBAAmBA,CAACpG,IAAU;MACrC;MACA;MACA,OAAOpB,aAAa,CAACoB,IAAI,CAAC,IAAIkE,iBAAiB,CAAC4B,GAAG,CAAC9F,IAAI,CAACC,IAAI,CAAC;IAChE;IAEA,IAAIrB,aAAa,CAACoB,IAAI,CAAC,IAAImG,cAAc,IAAI,IAAI,EAAE;MACjD;;IAGF,KAAK,MAAME,aAAa,IAAIF,cAAc,EAAE;MAC1C,IAAIC,mBAAmB,CAACC,aAAa,CAAC,EAAE;QACtC;;MAEF,MAAMpD,OAAO,GAAG5E,2BAA2B,CACvCgI,aAAa,CAACpG,IAAI,EAAEwF,SAAS,EAAEV,OAAO,CAAC;MAC3C,KAAK,MAAMtF,MAAM,IAAIwD,OAAO,EAAE;QAC5B,IAAI,CAACxD,MAAM,IAAIA,MAAM,CAACwG,IAAI,IAAIb,aAAa,CAACU,GAAG,CAACrG,MAAM,CAACC,EAAE,CAAC,EAAE;UAC1D;;QAEFD,MAAM,CAAC+F,OAAO,EAAE;;;EAGtB;EAEA;;;;;;;;;EASA,MAAMc,YAAYA,CAACxG,MAAsB,EAAES,OAAkB;IAE3D,OAAO,IAAI,CAACgG,aAAa,CAACzG,MAAM,EAAES,OAAO,CAAC;EAC5C;EAEAkD,0BAA0BA,CAAA;IACxB,IAAI,CAAC,IAAI,CAACwB,gBAAgB,EAAE;MAC1B;;IAEF5F,MAAM,CAACmH,MAAM,CAAC,IAAI,CAACvB,gBAAgB,CAAC,CAACtD,OAAO,CAAC4B,WAAW,IAAG;MACzD,KAAK,MAAM9D,MAAM,IAAI8D,WAAW,EAAE;QAChC,IAAI9D,MAAM,IAAI,CAACA,MAAM,CAACgH,UAAU,EAAE;UAChChH,MAAM,CAAC+F,OAAO,EAAE;;;IAGtB,CAAC,CAAC;IAEF,IAAI,CAACP,gBAAgB,GAAG,IAAI;EAC9B;EAEAyB,sBAAsBA,CAAA;IACpB,OAAO,IAAI,CAACzB,gBAAgB;EAC9B;EAEA;;;;;;;;;;;;;;EAcQ,MAAMsB,aAAaA,CACvBzG,MAAsB,EAAES,OAAkB,EAAEoG,mBAAmB,GAAG,KAAK,EACvE9B,cAAA,GAAiC,EAAE,EACnCC,aAAA,GAA+B,EAAE;IACnC;IACA,IAAI,CAACrB,0BAA0B,EAAE;IACjC,IAAI,CAACkD,mBAAmB,EAAE;MACxB7G,MAAM,GAAG,IAAI,CAAC4D,SAAS,CAAC5D,MAAM,CAAC;MAC/B,IAAI,CAAC8D,WAAW,CAAC9D,MAAM,CAAC;MACxB,IAAI,CAAC+D,sBAAsB,CAAC/D,MAAM,CAAC;MACnCS,OAAO,GAAG,IAAI,CAACuD,UAAU,CAACvD,OAAO,CAAC;MAClC,IAAI,CAACwD,YAAY,CAACxD,OAAO,CAAC;;IAG5B;IACA,IAAI;MACF,IAAI,CAACgB,uBAAuB,GAAGzD,GAAG,EAAE,CAAC0G,OAAO,CAAC,2BAA2B,CAAC;KAC1E,CAAC,OAAOC,CAAC,EAAE;MACV,IAAI,CAAClD,uBAAuB,GAAG,KAAK;MACpCmD,OAAO,CAACC,IAAI,CAACF,CAAC,CAACG,OAAO,CAAC;;IAGzB,MAAMG,OAAO,GAAG,IAAIvG,gBAAgB,CAChC,IAAI,CAACW,SAAS,EAAE0F,cAAc,EAAEC,aAAa,EAAE,IAAI,CAAC7F,mBAAmB,EACvE,IAAI,CAACoC,kBAAkB,CAAC;IAE5B,IAAI,IAAI,CAACE,uBAAuB,EAAE;MAChC,IAAI,CAAC0D,gBAAgB,GAAG,IAAI,CAAC9B,cAAc,CAAC,IAAI,CAAChE,SAAS,CAAC;;IAG7D;IACA;IACA;IACA,MAAMiE,UAAU,GAAG,MAAM,IAAI,CAACwD,sBAAsB,CAChD9G,MAAM,EAAEiF,OAAO,EAAExE,OAAO,EAAEoG,mBAAmB,CAAC;IAClD,MAAME,OAAO,GAAGtG,OAAO,CAAChB,GAAG,CAACU,IAAI,IAAI7B,SAAS,CAAC6B,IAAI,EAAEmD,UAAU,EAAE2B,OAAO,CAAC,CAAC;IAEzE;IACA,MAAM+B,SAAS,GAAGD,OAAO,CAACtH,GAAG,CAACwH,CAAC,IAAIA,CAAC,CAACrH,EAAE,CAAC;IACxC,MAAMsH,QAAQ,GAAG3H,MAAM,CAACC,IAAI,CAACQ,MAAM,CAAC,CAACP,GAAG,CAACU,IAAI,IAAIH,MAAM,CAACG,IAAI,CAAC,CAACP,EAAE,CAAC;IACjE,MAAMuH,OAAO,GACT,IAAI9C,GAAG,CAAS,CAAC,GAAG2C,SAAS,EAAE,GAAGE,QAAQ,EAAE,GAAG,IAAI,CAAClI,SAAS,CAAC,CAAC;IAEnEO,MAAM,CAACmH,MAAM,CAACpD,UAAU,CAAC,CAACzB,OAAO,CAAC4B,WAAW,IAAG;MAC9CA,WAAW,CAAC5B,OAAO,CAAClC,MAAM,IAAG;QAC3B,IAAIA,MAAM,IAAI,CAACA,MAAM,CAACgH,UAAU,IAAI,CAACQ,OAAO,CAACnB,GAAG,CAACrG,MAAM,CAACC,EAAE,CAAC,EAAE;UAC3DD,MAAM,CAAC+F,OAAO,EAAE;;MAEpB,CAAC,CAAC;IACJ,CAAC,CAAC;IAEF;IACA,IAAI,IAAI,CAACzG,MAAM,IAAI,IAAI,EAAE;MACvBgG,OAAO,CAACS,OAAO,CAACyB,OAAO,CAAC;;IAG1B,OAAOJ,OAAO;EAChB;EAEA,MAAMK,oBAAoBA,CACtBpH,MAAgB,EAAE+E,cAA8B,EAChDC,aAA4B;IAC9B,MAAMqC,YAAY,GAAGrH,MAAM,CAACiB,MAAM,CAAC,CAACxB,GAAG,EAAEE,MAAM,EAAE0F,KAAK,KAAI;MACxD5F,GAAG,CAAC,IAAI,CAACO,MAAM,CAACqF,KAAK,CAAC,CAAClF,IAAI,CAAC,GAAGR,MAAM;MACrC,OAAOF,GAAG;IACZ,CAAC,EAAE,EAAoB,CAAC;IAExB,OAAO,IAAI,CAACgH,aAAa,CACrBY,YAAY,EAAE,IAAI,CAACxG,WAAW,EAAE,IAAI,EAAEkE,cAAc,EAAEC,aAAa,CAAC;EAC1E;EAEA;;;;;;;;;;;EAWQ,MAAM8B,sBAAsBA,CAChC9G,MAAsB,EAAEiF,OAAyB,EAAEqC,WAAsB,EACzET,mBAA6B;IAC/B,MAAMhD,KAAK,GAAGtE,MAAM,CAACC,IAAI,CAACQ,MAAM,CAAC;IACjC,MAAMW,UAAU,GACZkD,KAAK,CAACpE,GAAG,CAACU,IAAI,IAAI,IAAI,CAACiB,KAAK,CAAC8C,KAAK,CAAC1F,aAAa,CAAC2B,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC/D,MAAMgE,eAAe,GAAGmD,WAAW,CAAC7H,GAAG,CAACU,IAAI,IAAI3B,aAAa,CAAC2B,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;IACvE,MAAMiE,iBAAiB,GAAG,IAAIC,GAAG,CAACF,eAAe,CAAC;IAClD,IAAItD,WAAW,GAAGsD,eAAe,CAAC1E,GAAG,CAACU,IAAI,IAAI,IAAI,CAACiB,KAAK,CAAC8C,KAAK,CAAC/D,IAAI,CAAC,CAAC;IAErE;IACA,IAAIU,WAAW,CAAC6B,MAAM,KAAK,CAAC,EAAE;MAC5B7B,WAAW,GAAG,IAAI,CAACH,QAAQ;;IAG7B,MAAM;MAAC6G,SAAS;MAAElF,aAAa;MAAEC,WAAW;MAAEC;IAAU,CAAC,GACrD5D,oBAAoB,CAChBqB,MAAM,EAAEa,WAAW,EAAE,IAAI,CAACxB,SAAS,EAAE,IAAI,CAACqC,UAAU,CAAC;IAE7D;IACA,MAAM8F,KAAK,GAAuB,CAChC,GAAG7G,UAAU,EAAE,GAAG,IAAI,CAACS,KAAK,CAACqG,OAAO,EAAE,IAAI,IAAI,CAAC/F,UAAU,IAAI,EAAE,CAAC,CACjE,CAACjC,GAAG,CAACS,IAAI,IAAG;MACX,OAAO;QAACA,IAAI;QAAEwH,QAAQ,EAAEzC,OAAO,CAAC0C;MAAc,CAAC;IACjD,CAAC,CAAC;IACF,MAAMrE,UAAU,GAAA/D,MAAA,CAAA2F,MAAA,KAAwB,IAAI,CAAC7F,SAAS,CAAC;IACvDE,MAAM,CAACC,IAAI,CAACQ,MAAM,CAAC,CAAC6B,OAAO,CAAC1B,IAAI,IAAG;MACjC,MAAM,CAACiF,QAAQ,EAAEC,KAAK,CAAC,GAAG7G,aAAa,CAAC2B,IAAI,CAAC;MAC7C,MAAMgD,OAAO,GAAa,EAAE;MAC5BA,OAAO,CAACkC,KAAK,CAAC,GAAGrF,MAAM,CAACG,IAAI,CAAC;MAC7BmD,UAAU,CAAC8B,QAAQ,CAAC,GAAGjC,OAAO;IAChC,CAAC,CAAC;IACF,MAAM4C,+BAA+B,GAA4B,EAAE;IACnE,MAAMT,aAAa,GAAG,IAAI,CAACC,kBAAkB,CAACjC,UAAU,CAAC;IACzD,MAAMsE,KAAK,GAA6B,EAAE;IAC1C,OAAOJ,KAAK,CAAC9E,MAAM,GAAG,CAAC,EAAE;MACvB,MAAMmF,QAAQ,GAAG,IAAI,CAACC,YAAY,CAC9BnH,UAAU,EAAE6G,KAAK,EAAEvC,OAAO,EAAE3B,UAAU,EAAEsE,KAAK,EAAEtC,aAAa,EAC5DlB,iBAAiB,EAAE2B,+BAA+B,EAAEwB,SAAS,CAAC;MAClE,MAAMQ,OAAO,CAACC,GAAG,CAACH,QAAQ,CAAC;;IAE7B,IAAIvF,WAAW,IAAI,IAAI,IAAI,CAACuE,mBAAmB,EAAE;MAC/CjC,OAAO,CAACC,IAAI,CACR,mEAAmE,GACnE,gEAAgE,CAAC;;IAEvE,MAAMoD,cAAc,GAChBpH,WAAW,CACNqH,MAAM,CACHhI,IAAI,IAAI,CAACpB,aAAa,CAACoB,IAAI,CAAC,IACxB,CAAC5B,SAAS,CAAC4B,IAAI,CAACC,IAAI,EAAEmD,UAAU,EAAE2B,OAAO,CAAC,CAAC,CAClDxF,GAAG,CAACS,IAAI,IAAIA,IAAI,CAACC,IAAI,CAAC;IAC/B,IAAI8H,cAAc,CAACvF,MAAM,GAAG,CAAC,EAAE;MAC7B,IAAIyF,cAAc,GAAG,EAAE;MACvB,IAAI7F,WAAW,IAAI,IAAI,EAAE;QACvB6F,cAAc,GACV,+DAA+D,GAC/D,2BAA2B5F,UAAU,GAAG;;MAE9C,MAAM,IAAIC,KAAK,CACX,+BAA+ByF,cAAc,sBAAsB,GACnE,WAAWpE,KAAK,8CAA8C,GAC9D,IAAIxB,aAAa,MAAM8F,cAAc,EAAE,CAAC;;IAE9C,OAAO7E,UAAU;EACnB;EAEQwE,YAAYA,CAChBnH,UAAkB,EAAE6G,KAAyB,EAAEvC,OAAyB,EACxEU,SAA0B,EAAEiC,KAA+B,EAC3DtC,aAA0B,EAAElB,iBAA8B,EAC1D2B,+BAAwD,EACxDwB,SAAsB;IACxB,MAAMM,QAAQ,GAA6B,EAAE;IAC7C,OAAOL,KAAK,CAAC9E,MAAM,GAAG,CAAC,EAAE;MACvB,MAAM0F,IAAI,GAAGZ,KAAK,CAACa,GAAG,EAAE;MACxBpD,OAAO,CAAC0C,cAAc,GAAGS,IAAI,CAACV,QAAQ;MACtC,IAAItC,QAAQ,GAAG,EAAE;MACjB;MACA;MACA;MACA,IAAIgD,IAAI,CAAClI,IAAI,CAACuC,EAAE,KAAK,OAAO,IACxBpE,aAAa,CAAC,YAAY,EAAE+J,IAAI,CAAClI,IAAI,EAAEyF,SAAS,EAAEV,OAAO,CAAC,EAAE;QAC9D,CAACG,QAAQ,CAAC,GAAGhH,mBAAmB,CAACgK,IAAI,CAAClI,IAAI,CAACC,IAAI,EAAE8E,OAAO,CAAC;;MAG3D;MACA;MACA,IAAIU,SAAS,CAACyC,IAAI,CAAClI,IAAI,CAACC,IAAI,CAAC,IAAI,IAAI,EAAE;QACrC,MAAMgD,OAAO,GACT1E,SAAS,CAAC2J,IAAI,CAAClI,IAAI,EAAEyF,SAAS,EAAEV,OAAO,EAAE,IAAI,CAAClF,gBAAgB,CAAC;QACnE,IAAI,CAACqF,QAAQ,EAAE;UACb,CAACA,QAAQ,CAAC,GAAGhH,mBAAmB,CAACgK,IAAI,CAAClI,IAAI,CAACC,IAAI,EAAE8E,OAAO,CAAC;;QAE3D,MAAM0C,cAAc,GAAG1C,OAAO,CAAC0C,cAAc;QAC7C,IAAIxJ,IAAI,CAACqH,SAAS,CAACrC,OAAO,CAAC,EAAE;UAC3B0E,QAAQ,CAACS,IAAI,CAACnF,OAAO,CAACoF,IAAI,CAACtB,CAAC,IAAG;YAC7BtB,SAAS,CAACP,QAAQ,CAAC,GAAG6B,CAAC;YACvB,IAAI,IAAI,CAACxF,uBAAuB,EAAE;cAChC,IAAI,CAAC0D,gBAAgB,CAACC,QAAQ,CAAC,GAAG,IAAI,CAAClC,eAAe,CAAC+D,CAAC,CAAC;;YAE3DhC,OAAO,CAAC0C,cAAc,GAAGA,cAAc;YACvC,IAAI,CAAC7B,sBAAsB,CACvBV,QAAQ,EAAEgD,IAAI,CAAClI,IAAI,EAAEyF,SAAS,EAAEV,OAAO,EAAEK,aAAa,EACtDlB,iBAAiB,EAAE2B,+BAA+B,CAAC;YACvD,IAAI,CAACyC,iBAAiB,CAClBJ,IAAI,CAAClI,IAAI,EAAEsH,KAAK,EAAEvC,OAAO,EAAEU,SAAS,EAAEiC,KAAK,EAAEL,SAAS,CAAC;YAC3D,OAAON,CAAC;UACV,CAAC,CAAC,CAAC;SACJ,MAAM;UACLtB,SAAS,CAACP,QAAQ,CAAC,GAAGjC,OAAO;UAC7B,IAAI,IAAI,CAAC1B,uBAAuB,EAAE;YAChC,IAAI,CAAC0D,gBAAgB,CAACC,QAAQ,CAAC,GAAG,IAAI,CAAClC,eAAe,CAACC,OAAO,CAAC;;UAEjE,IAAI,CAAC2C,sBAAsB,CACvBV,QAAQ,EAAEgD,IAAI,CAAClI,IAAI,EAAEyF,SAAS,EAAEV,OAAO,EAAEK,aAAa,EACtDlB,iBAAiB,EAAE2B,+BAA+B,CAAC;UACvD,IAAI,CAACyC,iBAAiB,CAClBJ,IAAI,CAAClI,IAAI,EAAEsH,KAAK,EAAEvC,OAAO,EAAEU,SAAS,EAAEiC,KAAK,EAAEL,SAAS,CAAC;;OAE9D,MAAM;QACL,IAAI,CAACiB,iBAAiB,CAClBJ,IAAI,CAAClI,IAAI,EAAEsH,KAAK,EAAEvC,OAAO,EAAEU,SAAS,EAAEiC,KAAK,EAAEL,SAAS,CAAC;;;IAG/D,OAAOM,QAAQ;EACjB;EAEQW,iBAAiBA,CACrBtI,IAAU,EAAEsH,KAAyB,EAAEvC,OAAyB,EAChEU,SAA0B,EAAEiC,KAA+B,EAC3DL,SAAsB;IACxBrH,IAAI,CAAC+F,QAAQ,CAACpE,OAAO,CAAE4G,SAAS,IAAI;MAClC,MAAM,CAACrD,QAAQ,CAAG,GAAGhH,mBAAmB,CAACqK,SAAS,CAACtI,IAAI,EAAE8E,OAAO,CAAC;MACjE,IAAI2C,KAAK,CAACxC,QAAQ,CAAC,IAAI,CAACmC,SAAS,CAACvB,GAAG,CAACyC,SAAS,CAACtI,IAAI,CAAC,EAAE;QACrD;;MAEF;MACA,IAAIsI,SAAS,CAAChG,EAAE,KAAK,OAAO,EAAE;QAC5B,IAAIgG,SAAS,CAACC,UAAU,CAACC,IAAI,CAACxI,IAAI,IAAG;UAC/B,OAAO,CAAC,CAAC7B,SAAS,CAAC6B,IAAI,EAAEwF,SAAS,EAAEV,OAAO,CAAC;QAC9C,CAAC,CAAC,EAAE;UACN2C,KAAK,CAACxC,QAAQ,CAAC,GAAG,IAAI;UACtBoC,KAAK,CAACc,IAAI,CAAC;YAACZ,QAAQ,EAAEzC,OAAO,CAAC0C,cAAc;YAAEzH,IAAI,EAAEuI;UAAS,CAAC,CAAC;;OAElE;QAAO;QACJ,IAAIA,SAAS,CAACC,UAAU,CAACE,KAAK,CAACzI,IAAI,IAAG;UAChC,OAAO,CAAC,CAAC7B,SAAS,CAAC6B,IAAI,EAAEwF,SAAS,EAAEV,OAAO,CAAC;QAC9C,CAAC,CAAC,EAAE;UACV2C,KAAK,CAACxC,QAAQ,CAAC,GAAG,IAAI;UACtBoC,KAAK,CAACc,IAAI,CAAC;YAACZ,QAAQ,EAAEzC,OAAO,CAAC0C,cAAc;YAAEzH,IAAI,EAAEuI;UAAS,CAAC,CAAC;;IAEnE,CAAC,CAAC;EACJ;EAEA;;;EAGA/C,OAAOA,CAAA;IACLnG,MAAM,CAACC,IAAI,CAAC,IAAI,CAACH,SAAS,CAAC,CACtBwC,OAAO,CACJnC,GAAG,IAAI,IAAI,CAACL,SAAS,CAACK,GAAG,CAAC,CAACmC,OAAO,CAAClC,MAAM,IAAIA,MAAM,CAAC+F,OAAO,EAAE,CAAC,CAAC;EACzE;EAEQ3B,sBAAsBA,CAAC/D,MAAsB;IACnDT,MAAM,CAACC,IAAI,CAACQ,MAAM,CAAC,CAAC6B,OAAO,CAAC1B,IAAI,IAAG;MACjC,MAAM+F,KAAK,GAAGlG,MAAM,CAACG,IAAI,CAAC;MAC1B,MAAM,CAACiF,QAAQ,CAAG,GAAG5G,aAAa,CAAC2B,IAAI,CAAC;MACxC,MAAMD,IAAI,GAAG,IAAI,CAACkB,KAAK,CAAC8C,KAAK,CAACkB,QAAQ,CAAC;MACvC,IAAIlF,IAAI,CAACG,UAAU,CAAC,OAAO,CAAC,IAAIH,IAAI,CAACG,UAAU,CAAC,OAAO,CAAC,CAACC,KAAK,EAAE;QAC9D,MAAMF,KAAK,GAAGF,IAAI,CAACG,UAAU,CAAC,OAAO,CAAC,CAACC,KAAiB;QACxD,MAAMuI,KAAK,GAAGzI,KAAK,CAACsC,MAAM,KAAKwD,KAAK,CAAC9F,KAAK,CAACsC,MAAM,IAC7CwD,KAAK,CAAC9F,KAAK,CAACwI,KAAK,CACb,CAACE,GAAG,EAAEzD,KAAK,KAAKjF,KAAK,CAACiF,KAAK,CAAC,KAAK,CAAC,CAAC,IAAIjF,KAAK,CAACiF,KAAK,CAAC,KAAKyD,GAAG,CAAC;QACpE3K,IAAI,CAAC4K,MAAM,CACPF,KAAK,EACL,MAAM,sBAAsB3I,IAAI,CAACC,IAAI,iBAAiB,GAClD,gCAAgCC,KAAK,aAAa,GAClD,IAAI8F,KAAK,CAAC9F,KAAK,GAAG,CAAC;;MAE7B,IAAIF,IAAI,CAACG,UAAU,CAAC,OAAO,CAAC,IAAIH,IAAI,CAACG,UAAU,CAAC,OAAO,CAAC,CAACC,KAAK,EAAE;QAC9DnC,IAAI,CAAC4K,MAAM,CACP7C,KAAK,CAAC1F,KAAK,KAAKN,IAAI,CAACG,UAAU,CAAC,OAAO,CAAC,CAACC,KAAe,EACxD,MAAM,sBAAsBJ,IAAI,CAACC,IAAI,iBAAiB,GAClD,8BAA8B,GAC9B,GAAGD,IAAI,CAACG,UAAU,CAAC,OAAO,CAAC,CAACC,KAAK,aAAa4F,KAAK,CAAC1F,KAAK,EAAE,CAAC;;IAExE,CAAC,CAAC;EACJ;EAEQoD,SAASA,CAAC5D,MAAsB;;IACtC,MAAMgJ,MAAM,GAAmB,EAAE;IACjC,KAAK,MAAMC,SAAS,IAAIjJ,MAAM,EAAE;MAC9B,MAAML,MAAM,GAAG,CAAAuJ,EAAA,IAAAC,EAAA,OAAI,CAACvH,UAAU,cAAAuH,EAAA,uBAAAA,EAAA,CAAGnJ,MAAM,cAAAkJ,EAAA,uBAAAA,EAAA,CAAID,SAAS,CAAC;MACrD,IAAItJ,MAAM,IAAI,IAAI,EAAE;QAClBqJ,MAAM,CAACrJ,MAAM,CAACQ,IAAI,CAAC,GAAGH,MAAM,CAACiJ,SAAS,CAAC;OACxC,MAAM;QACLD,MAAM,CAACC,SAAS,CAAC,GAAGjJ,MAAM,CAACiJ,SAAS,CAAC;;;IAGzC,OAAOD,MAAM;EACf;EAEQlF,WAAWA,CAAC9D,MAAsB;IACxC,MAAMoJ,UAAU,GAAG7J,MAAM,CAACC,IAAI,CAACQ,MAAM,CAAC,CAACkI,MAAM,CAAC/H,IAAI,IAAG;MACnD,MAAM,CAACiF,QAAQ,CAAC,GAAG5G,aAAa,CAAC2B,IAAI,CAAC;MACtC,OAAO,IAAI,CAACiB,KAAK,CAAC8C,KAAK,CAACkB,QAAQ,CAAC,IAAI,IAAI;IAC3C,CAAC,CAAC;IACF,IAAIgE,UAAU,CAAC1G,MAAM,GAAG,CAAC,EAAE;MACzB,MAAM,IAAIF,KAAK,CACX,+CAA+C,GAC/C,UAAU4G,UAAU,8BAA8B,CAAC;;EAE3D;EAEQpF,UAAUA,CAACvD,OAAiB;IAClC,OAAOA,OAAO,CAAChB,GAAG,CAACU,IAAI,IAAG;;MACxB,MAAMR,MAAM,GAAG,CAAAuJ,EAAA,IAAAC,EAAA,OAAI,CAACvH,UAAU,cAAAuH,EAAA,uBAAAA,EAAA,CAAG1I,OAAO,cAAAyI,EAAA,uBAAAA,EAAA,CAAI/I,IAAI,CAAC;MACjD,IAAIR,MAAM,IAAI,IAAI,EAAE;QAClB,OAAOA,MAAM,CAACQ,IAAI;;MAEpB,OAAOA,IAAI;IACb,CAAC,EAAE,EAAE,CAAC;EACR;EAEQ8D,YAAYA,CAACxD,OAAiB;IACpCA,OAAO,CAACoB,OAAO,CAAC1B,IAAI,IAAG;MACrB,MAAM,CAACkJ,cAAc,CAAC,GAAG7K,aAAa,CAAC2B,IAAI,CAAC;MAC5C,IAAI,CAAC,IAAI,CAACiB,KAAK,CAAC8C,KAAK,CAACmF,cAAc,CAAC,EAAE;QACrC,MAAM,IAAI7G,KAAK,CAAC,eAAerC,IAAI,6BAA6B,CAAC;;IAErE,CAAC,CAAC;EACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}