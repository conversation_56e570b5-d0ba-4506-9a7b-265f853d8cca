{"ast": null, "code": "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { conv2d } from '../../ops/conv2d';\nimport { getGlobalTensorClass } from '../../tensor';\ngetGlobalTensorClass().prototype.conv2d = function (filter, strides, pad, dataFormat, dilations, dimRoundingMode) {\n  this.throwIfDisposed();\n  return conv2d(this, filter, strides, pad, dataFormat, dilations, dimRoundingMode);\n};", "map": {"version": 3, "names": ["conv2d", "getGlobalTensorClass", "prototype", "filter", "strides", "pad", "dataFormat", "dilations", "dimRoundingMode", "throwIfDisposed"], "sources": ["C:\\tfjs-core\\src\\public\\chained_ops\\conv2d.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport {conv2d} from '../../ops/conv2d';\nimport {getGlobalTensorClass, Tensor3D, Tensor4D} from '../../tensor';\nimport {Rank, TensorLike4D} from '../../types';\n\ndeclare module '../../tensor' {\n  interface Tensor<R extends Rank = Rank> {\n    conv2d<T extends Tensor3D|Tensor4D>(\n        filter: Tensor4D|TensorLike4D, strides: [number, number]|number,\n        pad: 'valid'|'same'|number, dataFormat?: 'NHWC'|'NCHW',\n        dilations?: [number, number]|number,\n        dimRoundingMode?: 'floor'|'round'|'ceil'): T;\n  }\n}\n\ngetGlobalTensorClass().prototype.conv2d = function<T extends Tensor3D|Tensor4D>(\n    filter: Tensor4D|TensorLike4D, strides: [number, number]|number,\n    pad: 'valid'|'same'|number, dataFormat?: 'NHWC'|'NCHW',\n    dilations?: [number, number]|number,\n    dimRoundingMode?: 'floor'|'round'|'ceil'): T {\n  this.throwIfDisposed();\n  return conv2d(\n             this, filter, strides, pad, dataFormat, dilations,\n             dimRoundingMode) as T;\n};\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAgBA,SAAQA,MAAM,QAAO,kBAAkB;AACvC,SAAQC,oBAAoB,QAA2B,cAAc;AAarEA,oBAAoB,EAAE,CAACC,SAAS,CAACF,MAAM,GAAG,UACtCG,MAA6B,EAAEC,OAAgC,EAC/DC,GAA0B,EAAEC,UAA0B,EACtDC,SAAmC,EACnCC,eAAwC;EAC1C,IAAI,CAACC,eAAe,EAAE;EACtB,OAAOT,MAAM,CACF,IAAI,EAAEG,MAAM,EAAEC,OAAO,EAAEC,GAAG,EAAEC,UAAU,EAAEC,SAAS,EACjDC,eAAe,CAAM;AAClC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}