{"ast": null, "code": "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n// TODO update import path once op is modularized.\nimport { cast } from '../../ops/ops';\nimport { getGlobalTensorClass } from '../../tensor';\n/**\n * Casts the array to type `float32`\n *\n * @doc {heading: 'Tensors', subheading: 'Classes'}\n */\ngetGlobalTensorClass().prototype.toFloat = function () {\n  this.throwIfDisposed();\n  return cast(this, 'float32');\n};", "map": {"version": 3, "names": ["cast", "getGlobalTensorClass", "prototype", "toFloat", "throwIfDisposed"], "sources": ["C:\\tfjs-core\\src\\public\\chained_ops\\to_float.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\n// TODO update import path once op is modularized.\nimport {cast} from '../../ops/ops';\nimport {getGlobalTensorClass, Tensor} from '../../tensor';\nimport {Rank} from '../../types';\n\ndeclare module '../../tensor' {\n  interface Tensor<R extends Rank = Rank> {\n    toFloat<T extends Tensor>(this: T): T;\n  }\n}\n\n/**\n * Casts the array to type `float32`\n *\n * @doc {heading: 'Tensors', subheading: 'Classes'}\n */\ngetGlobalTensorClass().prototype.toFloat = function<T extends Tensor>(this: T):\n    T {\n  this.throwIfDisposed();\n  return cast<T>(this, 'float32');\n};\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAiBA;AACA,SAAQA,IAAI,QAAO,eAAe;AAClC,SAAQC,oBAAoB,QAAe,cAAc;AASzD;;;;;AAKAA,oBAAoB,EAAE,CAACC,SAAS,CAACC,OAAO,GAAG;EAEzC,IAAI,CAACC,eAAe,EAAE;EACtB,OAAOJ,IAAI,CAAI,IAAI,EAAE,SAAS,CAAC;AACjC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}