{"ast": null, "code": "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { ENGINE } from '../engine';\nimport { Max } from '../kernel_names';\nimport { convertToTensor } from '../tensor_util_env';\nimport { op } from './operation';\n/**\n * Computes the maximum of elements across dimensions of a `tf.Tensor`.\n *\n * Reduces the input along the dimensions given in `axes`. Unless `keepDims`\n * is true, the rank of the `tf.Tensor` is reduced by 1 for each entry in\n * `axes`. If `keepDims` is true, the reduced dimensions are retained with\n * length 1. If `axes` has no entries, all dimensions are reduced, and a\n * `tf.Tensor` with a single element is returned.\n *\n * ```js\n * const x = tf.tensor1d([1, 2, 3]);\n *\n * x.max().print();  // or tf.max(x)\n * ```\n *\n * ```js\n * const x = tf.tensor2d([1, 2, 3, 4], [2, 2]);\n *\n * const axis = 1;\n * x.max(axis).print();  // or tf.max(x, axis)\n * ```\n *\n * @param x The input tensor.\n * @param axis The dimension(s) to reduce. By default it reduces\n *     all dimensions.\n * @param keepDims If true, retains reduced dimensions with size 1.\n *\n * @doc {heading: 'Operations', subheading: 'Reduction'}\n */\nfunction max_(x) {\n  let axis = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : null;\n  let keepDims = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : false;\n  const $x = convertToTensor(x, 'x', 'max');\n  const inputs = {\n    x: $x\n  };\n  const attrs = {\n    reductionIndices: axis,\n    keepDims\n  };\n  return ENGINE.runKernel(Max, inputs, attrs);\n}\nexport const max = /* @__PURE__ */op({\n  max_\n});", "map": {"version": 3, "names": ["ENGINE", "Max", "convertToTensor", "op", "max_", "x", "axis", "arguments", "length", "undefined", "keepDims", "$x", "inputs", "attrs", "reductionIndices", "runKernel", "max"], "sources": ["C:\\tfjs-core\\src\\ops\\max.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {ENGINE} from '../engine';\nimport {Max, MaxAttrs, MaxInputs} from '../kernel_names';\nimport {NamedAttrMap} from '../kernel_registry';\nimport {Tensor} from '../tensor';\nimport {NamedTensorMap} from '../tensor_types';\nimport {convertToTensor} from '../tensor_util_env';\nimport {TensorLike} from '../types';\n\nimport {op} from './operation';\n\n/**\n * Computes the maximum of elements across dimensions of a `tf.Tensor`.\n *\n * Reduces the input along the dimensions given in `axes`. Unless `keepDims`\n * is true, the rank of the `tf.Tensor` is reduced by 1 for each entry in\n * `axes`. If `keepDims` is true, the reduced dimensions are retained with\n * length 1. If `axes` has no entries, all dimensions are reduced, and a\n * `tf.Tensor` with a single element is returned.\n *\n * ```js\n * const x = tf.tensor1d([1, 2, 3]);\n *\n * x.max().print();  // or tf.max(x)\n * ```\n *\n * ```js\n * const x = tf.tensor2d([1, 2, 3, 4], [2, 2]);\n *\n * const axis = 1;\n * x.max(axis).print();  // or tf.max(x, axis)\n * ```\n *\n * @param x The input tensor.\n * @param axis The dimension(s) to reduce. By default it reduces\n *     all dimensions.\n * @param keepDims If true, retains reduced dimensions with size 1.\n *\n * @doc {heading: 'Operations', subheading: 'Reduction'}\n */\nfunction max_<T extends Tensor>(\n    x: Tensor|TensorLike, axis: number|number[] = null, keepDims = false): T {\n  const $x = convertToTensor(x, 'x', 'max');\n\n  const inputs: MaxInputs = {x: $x};\n  const attrs: MaxAttrs = {reductionIndices: axis, keepDims};\n\n  return ENGINE.runKernel(\n      Max, inputs as unknown as NamedTensorMap,\n      attrs as unknown as NamedAttrMap);\n}\n\nexport const max = /* @__PURE__ */ op({max_});\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAiBA,SAAQA,MAAM,QAAO,WAAW;AAChC,SAAQC,GAAG,QAA4B,iBAAiB;AAIxD,SAAQC,eAAe,QAAO,oBAAoB;AAGlD,SAAQC,EAAE,QAAO,aAAa;AAE9B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6BA,SAASC,IAAIA,CACTC,CAAoB,EAAgD;EAAA,IAA9CC,IAAA,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAwB,IAAI;EAAA,IAAEG,QAAQ,GAAAH,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,KAAK;EACtE,MAAMI,EAAE,GAAGT,eAAe,CAACG,CAAC,EAAE,GAAG,EAAE,KAAK,CAAC;EAEzC,MAAMO,MAAM,GAAc;IAACP,CAAC,EAAEM;EAAE,CAAC;EACjC,MAAME,KAAK,GAAa;IAACC,gBAAgB,EAAER,IAAI;IAAEI;EAAQ,CAAC;EAE1D,OAAOV,MAAM,CAACe,SAAS,CACnBd,GAAG,EAAEW,MAAmC,EACxCC,KAAgC,CAAC;AACvC;AAEA,OAAO,MAAMG,GAAG,GAAG,eAAgBb,EAAE,CAAC;EAACC;AAAI,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}