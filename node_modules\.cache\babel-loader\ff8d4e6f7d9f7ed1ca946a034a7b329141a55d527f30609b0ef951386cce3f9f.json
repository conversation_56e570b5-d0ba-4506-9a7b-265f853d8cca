{"ast": null, "code": "/*\nCopyright (c) 2011, <PERSON>\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in\nall copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\nTHE SOFTWARE.\n*/\n\n/***\n * RegExp definitions for tokenizing text in a specific language based\n * on its alphabet. Each language is keyed by the two-letter code per\n * ISO 639-1, and defines a RegExp that excludes alphabetic characters.\n */\n\n'use strict';\n\nconst matchers = {\n  fi: /[^A-Za-zÅåÄäÖö]/\n};\nmodule.exports = matchers;", "map": {"version": 3, "names": ["matchers", "fi", "module", "exports"], "sources": ["C:/tmsft/node_modules/natural/lib/natural/tokenizers/orthography_matchers.js"], "sourcesContent": ["/*\nCopyright (c) 2011, <PERSON>\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in\nall copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\nTHE SOFTWARE.\n*/\n\n/***\n * RegExp definitions for tokenizing text in a specific language based\n * on its alphabet. Each language is keyed by the two-letter code per\n * ISO 639-1, and defines a RegExp that excludes alphabetic characters.\n */\n\n'use strict'\n\nconst matchers = {\n  fi: /[^A-Za-zÅåÄäÖö]/\n}\n\nmodule.exports = matchers\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA,YAAY;;AAEZ,MAAMA,QAAQ,GAAG;EACfC,EAAE,EAAE;AACN,CAAC;AAEDC,MAAM,CAACC,OAAO,GAAGH,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}