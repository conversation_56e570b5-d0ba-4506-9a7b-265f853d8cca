{"ast": null, "code": "/*\nCopyright (c) 2011, <PERSON>\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in\nall copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\nTHE SOFTWARE.\n*/\n\n'use strict';\n\nconst Classifier = require('./classifier');\nconst ApparatusLogisticRegressionClassifier = require('apparatus').LogisticRegressionClassifier;\nclass LogisticRegressionClassifier extends Classifier {\n  constructor(stemmer) {\n    const abc = new ApparatusLogisticRegressionClassifier();\n    super(abc, stemmer);\n  }\n  static restore(classifier, stemmer) {\n    classifier = Classifier.restore(classifier, stemmer);\n    // Using ___proto__ is deprecated\n    // classifier.__proto__ = LogisticRegressionClassifier.prototype\n    Object.setPrototypeOf(classifier, LogisticRegressionClassifier.prototype);\n    classifier.classifier = ApparatusLogisticRegressionClassifier.restore(classifier.classifier);\n    return classifier;\n  }\n  static load(filename, stemmer, callback) {\n    Classifier.load(filename, stemmer, (err, classifier) => {\n      if (err) {\n        callback(err);\n      } else {\n        callback(err, LogisticRegressionClassifier.restore(classifier, stemmer));\n      }\n    });\n  }\n  static async loadFrom(key, stemmer, storageBackend) {\n    const classifier = await Classifier.loadFrom(key, storageBackend);\n    return LogisticRegressionClassifier.restore(classifier, stemmer);\n  }\n  train() {\n    // we need to reset the training state because logistic regression\n    // needs its matricies to have their widths synced, etc.\n    this.lastAdded = 0;\n    this.classifier = new ApparatusLogisticRegressionClassifier();\n    super.train();\n  }\n}\nmodule.exports = LogisticRegressionClassifier;", "map": {"version": 3, "names": ["Classifier", "require", "ApparatusLogisticRegressionClassifier", "LogisticRegressionClassifier", "constructor", "stemmer", "abc", "restore", "classifier", "Object", "setPrototypeOf", "prototype", "load", "filename", "callback", "err", "loadFrom", "key", "storageBackend", "train", "lastAdded", "module", "exports"], "sources": ["C:/tmsft/node_modules/natural/lib/natural/classifiers/logistic_regression_classifier.js"], "sourcesContent": ["/*\nCopyright (c) 2011, <PERSON>\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in\nall copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\nTHE SOFTWARE.\n*/\n\n'use strict'\n\nconst Classifier = require('./classifier')\nconst ApparatusLogisticRegressionClassifier = require('apparatus').LogisticRegressionClassifier\n\nclass LogisticRegressionClassifier extends Classifier {\n  constructor (stemmer) {\n    const abc = new ApparatusLogisticRegressionClassifier()\n    super(abc, stemmer)\n  }\n\n  static restore (classifier, stemmer) {\n    classifier = Classifier.restore(classifier, stemmer)\n    // Using ___proto__ is deprecated\n    // classifier.__proto__ = LogisticRegressionClassifier.prototype\n    Object.setPrototypeOf(classifier, LogisticRegressionClassifier.prototype)\n    classifier.classifier = ApparatusLogisticRegressionClassifier.restore(classifier.classifier)\n\n    return classifier\n  }\n\n  static load (filename, stemmer, callback) {\n    Classifier.load(filename, stemmer, (err, classifier) => {\n      if (err) {\n        callback(err)\n      } else {\n        callback(err, LogisticRegressionClassifier.restore(classifier, stemmer))\n      }\n    })\n  }\n\n  static async loadFrom (key, stemmer, storageBackend) {\n    const classifier = await Classifier.loadFrom(key, storageBackend)\n    return LogisticRegressionClassifier.restore(classifier, stemmer)\n  }\n\n  train () {\n    // we need to reset the training state because logistic regression\n    // needs its matricies to have their widths synced, etc.\n    this.lastAdded = 0\n    this.classifier = new ApparatusLogisticRegressionClassifier()\n    super.train()\n  }\n}\n\nmodule.exports = LogisticRegressionClassifier\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,YAAY;;AAEZ,MAAMA,UAAU,GAAGC,OAAO,CAAC,cAAc,CAAC;AAC1C,MAAMC,qCAAqC,GAAGD,OAAO,CAAC,WAAW,CAAC,CAACE,4BAA4B;AAE/F,MAAMA,4BAA4B,SAASH,UAAU,CAAC;EACpDI,WAAWA,CAAEC,OAAO,EAAE;IACpB,MAAMC,GAAG,GAAG,IAAIJ,qCAAqC,CAAC,CAAC;IACvD,KAAK,CAACI,GAAG,EAAED,OAAO,CAAC;EACrB;EAEA,OAAOE,OAAOA,CAAEC,UAAU,EAAEH,OAAO,EAAE;IACnCG,UAAU,GAAGR,UAAU,CAACO,OAAO,CAACC,UAAU,EAAEH,OAAO,CAAC;IACpD;IACA;IACAI,MAAM,CAACC,cAAc,CAACF,UAAU,EAAEL,4BAA4B,CAACQ,SAAS,CAAC;IACzEH,UAAU,CAACA,UAAU,GAAGN,qCAAqC,CAACK,OAAO,CAACC,UAAU,CAACA,UAAU,CAAC;IAE5F,OAAOA,UAAU;EACnB;EAEA,OAAOI,IAAIA,CAAEC,QAAQ,EAAER,OAAO,EAAES,QAAQ,EAAE;IACxCd,UAAU,CAACY,IAAI,CAACC,QAAQ,EAAER,OAAO,EAAE,CAACU,GAAG,EAAEP,UAAU,KAAK;MACtD,IAAIO,GAAG,EAAE;QACPD,QAAQ,CAACC,GAAG,CAAC;MACf,CAAC,MAAM;QACLD,QAAQ,CAACC,GAAG,EAAEZ,4BAA4B,CAACI,OAAO,CAACC,UAAU,EAAEH,OAAO,CAAC,CAAC;MAC1E;IACF,CAAC,CAAC;EACJ;EAEA,aAAaW,QAAQA,CAAEC,GAAG,EAAEZ,OAAO,EAAEa,cAAc,EAAE;IACnD,MAAMV,UAAU,GAAG,MAAMR,UAAU,CAACgB,QAAQ,CAACC,GAAG,EAAEC,cAAc,CAAC;IACjE,OAAOf,4BAA4B,CAACI,OAAO,CAACC,UAAU,EAAEH,OAAO,CAAC;EAClE;EAEAc,KAAKA,CAAA,EAAI;IACP;IACA;IACA,IAAI,CAACC,SAAS,GAAG,CAAC;IAClB,IAAI,CAACZ,UAAU,GAAG,IAAIN,qCAAqC,CAAC,CAAC;IAC7D,KAAK,CAACiB,KAAK,CAAC,CAAC;EACf;AACF;AAEAE,MAAM,CAACC,OAAO,GAAGnB,4BAA4B", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}