{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.transformArguments = exports.FIRST_KEY_INDEX = void 0;\nexports.FIRST_KEY_INDEX = 1;\nfunction transformArguments(key, width, depth) {\n  return ['CMS.INITBYDIM', key, width.toString(), depth.toString()];\n}\nexports.transformArguments = transformArguments;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "transformArguments", "FIRST_KEY_INDEX", "key", "width", "depth", "toString"], "sources": ["C:/tmsft/node_modules/@redis/bloom/dist/commands/count-min-sketch/INITBYDIM.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.transformArguments = exports.FIRST_KEY_INDEX = void 0;\nexports.FIRST_KEY_INDEX = 1;\nfunction transformArguments(key, width, depth) {\n    return ['CMS.INITBYDIM', key, width.toString(), depth.toString()];\n}\nexports.transformArguments = transformArguments;\n"], "mappings": "AAAA,YAAY;;AACZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEC,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7DD,OAAO,CAACE,kBAAkB,GAAGF,OAAO,CAACG,eAAe,GAAG,KAAK,CAAC;AAC7DH,OAAO,CAACG,eAAe,GAAG,CAAC;AAC3B,SAASD,kBAAkBA,CAACE,GAAG,EAAEC,KAAK,EAAEC,KAAK,EAAE;EAC3C,OAAO,CAAC,eAAe,EAAEF,GAAG,EAAEC,KAAK,CAACE,QAAQ,CAAC,CAAC,EAAED,KAAK,CAACC,QAAQ,CAAC,CAAC,CAAC;AACrE;AACAP,OAAO,CAACE,kBAAkB,GAAGA,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}