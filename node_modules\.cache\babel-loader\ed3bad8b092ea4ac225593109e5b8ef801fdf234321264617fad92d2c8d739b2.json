{"ast": null, "code": "/**\n * @license\n * Copyright 2018 Google LLC\n *\n * Use of this source code is governed by an MIT-style\n * license that can be found in the LICENSE file or at\n * https://opensource.org/licenses/MIT.\n * =============================================================================\n */\nimport { ValueError } from '../errors';\nimport { pyListRepeat } from './generic_utils';\nimport { isInteger, max } from './math_utils';\n/**\n * Transforms a single number of array of numbers into an array of numbers.\n * @param value\n * @param n: The size of the tuple to be returned.\n * @param name: Name of the parameter, used for generating error messages.\n * @returns An array of numbers.\n */\nexport function normalizeArray(value, n, name) {\n  if (typeof value === 'number') {\n    return pyListRepeat(value, n);\n  } else {\n    if (value.length !== n) {\n      throw new ValueError(\"The \".concat(name, \" argument must be an integer or tuple of \").concat(n, \" integers.\") + \" Received: \".concat(value.length, \" elements.\"));\n    }\n    for (let i = 0; i < n; ++i) {\n      const singleValue = value[i];\n      if (!isInteger(singleValue)) {\n        throw new ValueError(\"The \".concat(name, \" argument must be an integer or tuple of \").concat(n) + \" integers. Received: \".concat(JSON.stringify(value), \" including a\") + \" non-integer number \".concat(singleValue));\n      }\n    }\n    return value;\n  }\n}\n/**\n * Determines output length of a convolution given input length.\n * @param inputLength\n * @param filterSize\n * @param padding\n * @param stride\n * @param dilation: dilation rate.\n */\nexport function convOutputLength(inputLength, filterSize, padding, stride) {\n  let dilation = arguments.length > 4 && arguments[4] !== undefined ? arguments[4] : 1;\n  if (inputLength == null) {\n    return inputLength;\n  }\n  const dilatedFilterSize = filterSize + (filterSize - 1) * (dilation - 1);\n  let outputLength;\n  if (padding === 'same') {\n    outputLength = inputLength;\n  } else {\n    // VALID\n    outputLength = inputLength - dilatedFilterSize + 1;\n  }\n  return Math.floor((outputLength + stride - 1) / stride);\n}\nexport function deconvLength(dimSize, strideSize, kernelSize, padding) {\n  if (dimSize == null) {\n    return null;\n  }\n  if (padding === 'valid') {\n    dimSize = dimSize * strideSize + max([kernelSize - strideSize, 0]);\n  } else if (padding === 'same') {\n    dimSize = dimSize * strideSize;\n  } else {\n    throw new ValueError(\"Unsupport padding mode: \".concat(padding, \".\"));\n  }\n  return dimSize;\n}", "map": {"version": 3, "names": ["ValueError", "pyListRepeat", "isInteger", "max", "normalizeArray", "value", "n", "name", "length", "concat", "i", "singleValue", "JSON", "stringify", "convOutputLength", "inputLength", "filterSize", "padding", "stride", "dilation", "arguments", "undefined", "dilatedFilterSize", "outputLength", "Math", "floor", "deconvLength", "dimSize", "strideSize", "kernelSize"], "sources": ["C:\\tfjs-layers\\src\\utils\\conv_utils.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2018 Google LLC\n *\n * Use of this source code is governed by an MIT-style\n * license that can be found in the LICENSE file or at\n * https://opensource.org/licenses/MIT.\n * =============================================================================\n */\n\nimport {ValueError} from '../errors';\nimport {PaddingMode} from '../keras_format/common';\n\nimport {pyListRepeat} from './generic_utils';\nimport {isInteger, max} from './math_utils';\n\n/**\n * Transforms a single number of array of numbers into an array of numbers.\n * @param value\n * @param n: The size of the tuple to be returned.\n * @param name: Name of the parameter, used for generating error messages.\n * @returns An array of numbers.\n */\nexport function normalizeArray(\n    value: number|number[], n: number, name: string): number[] {\n  if (typeof value === 'number') {\n    return pyListRepeat(value, n);\n  } else {\n    if (value.length !== n) {\n      throw new ValueError(\n          `The ${name} argument must be an integer or tuple of ${n} integers.` +\n          ` Received: ${value.length} elements.`);\n    }\n    for (let i = 0; i < n; ++i) {\n      const singleValue = value[i];\n      if (!isInteger(singleValue)) {\n        throw new ValueError(\n            `The ${name} argument must be an integer or tuple of ${n}` +\n            ` integers. Received: ${JSON.stringify(value)} including a` +\n            ` non-integer number ${singleValue}`);\n      }\n    }\n    return value;\n  }\n}\n\n/**\n * Determines output length of a convolution given input length.\n * @param inputLength\n * @param filterSize\n * @param padding\n * @param stride\n * @param dilation: dilation rate.\n */\nexport function convOutputLength(\n    inputLength: number, filterSize: number, padding: PaddingMode,\n    stride: number, dilation = 1): number {\n  if (inputLength == null) {\n    return inputLength;\n  }\n  const dilatedFilterSize = filterSize + (filterSize - 1) * (dilation - 1);\n  let outputLength: number;\n  if (padding === 'same') {\n    outputLength = inputLength;\n  } else {  // VALID\n    outputLength = inputLength - dilatedFilterSize + 1;\n  }\n  return Math.floor((outputLength + stride - 1) / stride);\n}\n\nexport function deconvLength(\n    dimSize: number, strideSize: number, kernelSize: number,\n    padding: PaddingMode): number {\n  if (dimSize == null) {\n    return null;\n  }\n\n  if (padding === 'valid') {\n    dimSize = dimSize * strideSize + max([kernelSize - strideSize, 0]);\n  } else if (padding === 'same') {\n    dimSize = dimSize * strideSize;\n  } else {\n    throw new ValueError(`Unsupport padding mode: ${padding}.`);\n  }\n  return dimSize;\n}\n"], "mappings": "AAAA;;;;;;;;;AAUA,SAAQA,UAAU,QAAO,WAAW;AAGpC,SAAQC,YAAY,QAAO,iBAAiB;AAC5C,SAAQC,SAAS,EAAEC,GAAG,QAAO,cAAc;AAE3C;;;;;;;AAOA,OAAM,SAAUC,cAAcA,CAC1BC,KAAsB,EAAEC,CAAS,EAAEC,IAAY;EACjD,IAAI,OAAOF,KAAK,KAAK,QAAQ,EAAE;IAC7B,OAAOJ,YAAY,CAACI,KAAK,EAAEC,CAAC,CAAC;GAC9B,MAAM;IACL,IAAID,KAAK,CAACG,MAAM,KAAKF,CAAC,EAAE;MACtB,MAAM,IAAIN,UAAU,CAChB,OAAAS,MAAA,CAAOF,IAAI,+CAAAE,MAAA,CAA4CH,CAAC,gCAAAG,MAAA,CAC1CJ,KAAK,CAACG,MAAM,eAAY,CAAC;;IAE7C,KAAK,IAAIE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,CAAC,EAAE,EAAEI,CAAC,EAAE;MAC1B,MAAMC,WAAW,GAAGN,KAAK,CAACK,CAAC,CAAC;MAC5B,IAAI,CAACR,SAAS,CAACS,WAAW,CAAC,EAAE;QAC3B,MAAM,IAAIX,UAAU,CAChB,OAAAS,MAAA,CAAOF,IAAI,+CAAAE,MAAA,CAA4CH,CAAC,4BAAAG,MAAA,CAChCG,IAAI,CAACC,SAAS,CAACR,KAAK,CAAC,iBAAc,0BAAAI,MAAA,CACpCE,WAAW,CAAE,CAAC;;;IAG7C,OAAON,KAAK;;AAEhB;AAEA;;;;;;;;AAQA,OAAM,SAAUS,gBAAgBA,CAC5BC,WAAmB,EAAEC,UAAkB,EAAEC,OAAoB,EAC7DC,MAAc,EAAc;EAAA,IAAZC,QAAQ,GAAAC,SAAA,CAAAZ,MAAA,QAAAY,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAG,CAAC;EAC9B,IAAIL,WAAW,IAAI,IAAI,EAAE;IACvB,OAAOA,WAAW;;EAEpB,MAAMO,iBAAiB,GAAGN,UAAU,GAAG,CAACA,UAAU,GAAG,CAAC,KAAKG,QAAQ,GAAG,CAAC,CAAC;EACxE,IAAII,YAAoB;EACxB,IAAIN,OAAO,KAAK,MAAM,EAAE;IACtBM,YAAY,GAAGR,WAAW;GAC3B,MAAM;IAAG;IACRQ,YAAY,GAAGR,WAAW,GAAGO,iBAAiB,GAAG,CAAC;;EAEpD,OAAOE,IAAI,CAACC,KAAK,CAAC,CAACF,YAAY,GAAGL,MAAM,GAAG,CAAC,IAAIA,MAAM,CAAC;AACzD;AAEA,OAAM,SAAUQ,YAAYA,CACxBC,OAAe,EAAEC,UAAkB,EAAEC,UAAkB,EACvDZ,OAAoB;EACtB,IAAIU,OAAO,IAAI,IAAI,EAAE;IACnB,OAAO,IAAI;;EAGb,IAAIV,OAAO,KAAK,OAAO,EAAE;IACvBU,OAAO,GAAGA,OAAO,GAAGC,UAAU,GAAGzB,GAAG,CAAC,CAAC0B,UAAU,GAAGD,UAAU,EAAE,CAAC,CAAC,CAAC;GACnE,MAAM,IAAIX,OAAO,KAAK,MAAM,EAAE;IAC7BU,OAAO,GAAGA,OAAO,GAAGC,UAAU;GAC/B,MAAM;IACL,MAAM,IAAI5B,UAAU,4BAAAS,MAAA,CAA4BQ,OAAO,MAAG,CAAC;;EAE7D,OAAOU,OAAO;AAChB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}