{"ast": null, "code": "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { backend_util, DepthwiseConv2dNativeBackpropFilter, TensorBuffer } from '@tensorflow/tfjs-core';\nimport { assertNotComplex } from '../cpu_util';\nexport function depthwiseConv2dNativeBackpropFilter(args) {\n  const {\n    inputs,\n    backend,\n    attrs\n  } = args;\n  const {\n    x,\n    dy\n  } = inputs;\n  const {\n    strides,\n    dilations,\n    pad,\n    dimRoundingMode,\n    filterShape\n  } = attrs;\n  assertNotComplex([x, dy], 'depthwiseConv2dNativeBackpropFilter');\n  const convInfo = backend_util.computeConv2DInfo(x.shape, filterShape, strides, dilations, pad, dimRoundingMode, true /* depthwise */);\n  const {\n    strideHeight,\n    strideWidth,\n    filterHeight,\n    filterWidth\n  } = convInfo;\n  const dW = new TensorBuffer(convInfo.filterShape, 'float32');\n  const leftPad = convInfo.padInfo.left;\n  const topPad = convInfo.padInfo.top;\n  const chMul = convInfo.outChannels / convInfo.inChannels;\n  const xVals = backend.data.get(x.dataId).values;\n  const xBuf = new TensorBuffer(x.shape, x.dtype, xVals);\n  const dyVals = backend.data.get(dy.dataId).values;\n  const dyBuf = new TensorBuffer(dy.shape, dy.dtype, dyVals);\n  for (let wR = 0; wR < filterHeight; ++wR) {\n    const yRMin = Math.max(0, Math.ceil((topPad - wR) / strideHeight));\n    const yRMax = Math.min(convInfo.outHeight, (convInfo.inHeight + topPad - wR) / strideHeight);\n    for (let wC = 0; wC < filterWidth; ++wC) {\n      const yCMin = Math.max(0, Math.ceil((leftPad - wC) / strideWidth));\n      const yCMax = Math.min(convInfo.outWidth, (convInfo.inWidth + leftPad - wC) / strideWidth);\n      for (let d2 = 0; d2 < convInfo.outChannels; ++d2) {\n        const d1 = Math.trunc(d2 / chMul);\n        const dm = d2 % chMul;\n        let dotProd = 0;\n        for (let b = 0; b < convInfo.batchSize; ++b) {\n          for (let yR = yRMin; yR < yRMax; ++yR) {\n            const xR = wR + yR * strideHeight - topPad;\n            for (let yC = yCMin; yC < yCMax; ++yC) {\n              const xC = wC + yC * strideWidth - leftPad;\n              dotProd += xBuf.get(b, xR, xC, d1) * dyBuf.get(b, yR, yC, d2);\n            }\n          }\n        }\n        dW.set(dotProd, wR, wC, d1, dm);\n      }\n    }\n  }\n  return backend.makeTensorInfo(dW.shape, dW.dtype, dW.values);\n}\nexport const depthwiseConv2dNativeBackpropFilterConfig = {\n  kernelName: DepthwiseConv2dNativeBackpropFilter,\n  backendName: 'cpu',\n  kernelFunc: depthwiseConv2dNativeBackpropFilter\n};", "map": {"version": 3, "names": ["backend_util", "DepthwiseConv2dNativeBackpropFilter", "Tensor<PERSON><PERSON><PERSON>", "assertNotComplex", "depthwiseConv2dNativeBackpropFilter", "args", "inputs", "backend", "attrs", "x", "dy", "strides", "dilations", "pad", "dimRoundingMode", "filterShape", "convInfo", "computeConv2DInfo", "shape", "strideHeight", "<PERSON><PERSON><PERSON><PERSON>", "filterHeight", "filterWidth", "dW", "leftPad", "padInfo", "left", "topPad", "top", "chMul", "outChannels", "inChannels", "xVals", "data", "get", "dataId", "values", "xBuf", "dtype", "dyVals", "dyBuf", "wR", "yRMin", "Math", "max", "ceil", "yRMax", "min", "outHeight", "inHeight", "wC", "yCMin", "yCMax", "outWidth", "inWidth", "d2", "d1", "trunc", "dm", "dotProd", "b", "batchSize", "yR", "xR", "yC", "xC", "set", "makeTensorInfo", "depthwiseConv2dNativeBackpropFilterConfig", "kernelName", "backendName", "kernelFunc"], "sources": ["C:\\tfjs-backend-cpu\\src\\kernels\\DepthwiseConv2dNativeBackpropFilter.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {backend_util, DepthwiseConv2dNativeBackpropFilter, DepthwiseConv2dNativeBackpropFilterAttrs, DepthwiseConv2dNativeBackpropFilterInputs, KernelConfig, KernelFunc, TensorBuffer, TensorInfo, TypedArray} from '@tensorflow/tfjs-core';\n\nimport {MathBackendCPU} from '../backend_cpu';\nimport {assertNotComplex} from '../cpu_util';\n\nexport function depthwiseConv2dNativeBackpropFilter(args: {\n  inputs: DepthwiseConv2dNativeBackpropFilterInputs,\n  backend: MathBackendCPU,\n  attrs: DepthwiseConv2dNativeBackpropFilterAttrs\n}): TensorInfo {\n  const {inputs, backend, attrs} = args;\n  const {x, dy} = inputs;\n  const {strides, dilations, pad, dimRoundingMode, filterShape} = attrs;\n\n  assertNotComplex([x, dy], 'depthwiseConv2dNativeBackpropFilter');\n\n  const convInfo = backend_util.computeConv2DInfo(\n      x.shape as [number, number, number, number], filterShape, strides,\n      dilations, pad, dimRoundingMode, true /* depthwise */);\n\n  const {strideHeight, strideWidth, filterHeight, filterWidth} = convInfo;\n\n  const dW = new TensorBuffer(convInfo.filterShape, 'float32');\n\n  const leftPad = convInfo.padInfo.left;\n  const topPad = convInfo.padInfo.top;\n  const chMul = convInfo.outChannels / convInfo.inChannels;\n\n  const xVals = backend.data.get(x.dataId).values as TypedArray;\n  const xBuf = new TensorBuffer(x.shape, x.dtype, xVals);\n  const dyVals = backend.data.get(dy.dataId).values as TypedArray;\n  const dyBuf = new TensorBuffer(dy.shape, dy.dtype, dyVals);\n  for (let wR = 0; wR < filterHeight; ++wR) {\n    const yRMin = Math.max(0, Math.ceil((topPad - wR) / strideHeight));\n    const yRMax = Math.min(\n        convInfo.outHeight, (convInfo.inHeight + topPad - wR) / strideHeight);\n\n    for (let wC = 0; wC < filterWidth; ++wC) {\n      const yCMin = Math.max(0, Math.ceil((leftPad - wC) / strideWidth));\n      const yCMax = Math.min(\n          convInfo.outWidth, (convInfo.inWidth + leftPad - wC) / strideWidth);\n\n      for (let d2 = 0; d2 < convInfo.outChannels; ++d2) {\n        const d1 = Math.trunc(d2 / chMul);\n        const dm = d2 % chMul;\n\n        let dotProd = 0;\n        for (let b = 0; b < convInfo.batchSize; ++b) {\n          for (let yR = yRMin; yR < yRMax; ++yR) {\n            const xR = wR + yR * strideHeight - topPad;\n            for (let yC = yCMin; yC < yCMax; ++yC) {\n              const xC = wC + yC * strideWidth - leftPad;\n              dotProd += (xBuf.get(b, xR, xC, d1) as number) *\n                  (dyBuf.get(b, yR, yC, d2) as number);\n            }\n          }\n        }\n        dW.set(dotProd, wR, wC, d1, dm);\n      }\n    }\n  }\n\n  return backend.makeTensorInfo(dW.shape, dW.dtype, dW.values);\n}\n\nexport const depthwiseConv2dNativeBackpropFilterConfig: KernelConfig = {\n  kernelName: DepthwiseConv2dNativeBackpropFilter,\n  backendName: 'cpu',\n  kernelFunc: depthwiseConv2dNativeBackpropFilter as unknown as KernelFunc\n};\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAiBA,SAAQA,YAAY,EAAEC,mCAAmC,EAAiHC,YAAY,QAA+B,uBAAuB;AAG5O,SAAQC,gBAAgB,QAAO,aAAa;AAE5C,OAAM,SAAUC,mCAAmCA,CAACC,IAInD;EACC,MAAM;IAACC,MAAM;IAAEC,OAAO;IAAEC;EAAK,CAAC,GAAGH,IAAI;EACrC,MAAM;IAACI,CAAC;IAAEC;EAAE,CAAC,GAAGJ,MAAM;EACtB,MAAM;IAACK,OAAO;IAAEC,SAAS;IAAEC,GAAG;IAAEC,eAAe;IAAEC;EAAW,CAAC,GAAGP,KAAK;EAErEL,gBAAgB,CAAC,CAACM,CAAC,EAAEC,EAAE,CAAC,EAAE,qCAAqC,CAAC;EAEhE,MAAMM,QAAQ,GAAGhB,YAAY,CAACiB,iBAAiB,CAC3CR,CAAC,CAACS,KAAyC,EAAEH,WAAW,EAAEJ,OAAO,EACjEC,SAAS,EAAEC,GAAG,EAAEC,eAAe,EAAE,IAAI,CAAC,eAAe,CAAC;EAE1D,MAAM;IAACK,YAAY;IAAEC,WAAW;IAAEC,YAAY;IAAEC;EAAW,CAAC,GAAGN,QAAQ;EAEvE,MAAMO,EAAE,GAAG,IAAIrB,YAAY,CAACc,QAAQ,CAACD,WAAW,EAAE,SAAS,CAAC;EAE5D,MAAMS,OAAO,GAAGR,QAAQ,CAACS,OAAO,CAACC,IAAI;EACrC,MAAMC,MAAM,GAAGX,QAAQ,CAACS,OAAO,CAACG,GAAG;EACnC,MAAMC,KAAK,GAAGb,QAAQ,CAACc,WAAW,GAAGd,QAAQ,CAACe,UAAU;EAExD,MAAMC,KAAK,GAAGzB,OAAO,CAAC0B,IAAI,CAACC,GAAG,CAACzB,CAAC,CAAC0B,MAAM,CAAC,CAACC,MAAoB;EAC7D,MAAMC,IAAI,GAAG,IAAInC,YAAY,CAACO,CAAC,CAACS,KAAK,EAAET,CAAC,CAAC6B,KAAK,EAAEN,KAAK,CAAC;EACtD,MAAMO,MAAM,GAAGhC,OAAO,CAAC0B,IAAI,CAACC,GAAG,CAACxB,EAAE,CAACyB,MAAM,CAAC,CAACC,MAAoB;EAC/D,MAAMI,KAAK,GAAG,IAAItC,YAAY,CAACQ,EAAE,CAACQ,KAAK,EAAER,EAAE,CAAC4B,KAAK,EAAEC,MAAM,CAAC;EAC1D,KAAK,IAAIE,EAAE,GAAG,CAAC,EAAEA,EAAE,GAAGpB,YAAY,EAAE,EAAEoB,EAAE,EAAE;IACxC,MAAMC,KAAK,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAED,IAAI,CAACE,IAAI,CAAC,CAAClB,MAAM,GAAGc,EAAE,IAAItB,YAAY,CAAC,CAAC;IAClE,MAAM2B,KAAK,GAAGH,IAAI,CAACI,GAAG,CAClB/B,QAAQ,CAACgC,SAAS,EAAE,CAAChC,QAAQ,CAACiC,QAAQ,GAAGtB,MAAM,GAAGc,EAAE,IAAItB,YAAY,CAAC;IAEzE,KAAK,IAAI+B,EAAE,GAAG,CAAC,EAAEA,EAAE,GAAG5B,WAAW,EAAE,EAAE4B,EAAE,EAAE;MACvC,MAAMC,KAAK,GAAGR,IAAI,CAACC,GAAG,CAAC,CAAC,EAAED,IAAI,CAACE,IAAI,CAAC,CAACrB,OAAO,GAAG0B,EAAE,IAAI9B,WAAW,CAAC,CAAC;MAClE,MAAMgC,KAAK,GAAGT,IAAI,CAACI,GAAG,CAClB/B,QAAQ,CAACqC,QAAQ,EAAE,CAACrC,QAAQ,CAACsC,OAAO,GAAG9B,OAAO,GAAG0B,EAAE,IAAI9B,WAAW,CAAC;MAEvE,KAAK,IAAImC,EAAE,GAAG,CAAC,EAAEA,EAAE,GAAGvC,QAAQ,CAACc,WAAW,EAAE,EAAEyB,EAAE,EAAE;QAChD,MAAMC,EAAE,GAAGb,IAAI,CAACc,KAAK,CAACF,EAAE,GAAG1B,KAAK,CAAC;QACjC,MAAM6B,EAAE,GAAGH,EAAE,GAAG1B,KAAK;QAErB,IAAI8B,OAAO,GAAG,CAAC;QACf,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG5C,QAAQ,CAAC6C,SAAS,EAAE,EAAED,CAAC,EAAE;UAC3C,KAAK,IAAIE,EAAE,GAAGpB,KAAK,EAAEoB,EAAE,GAAGhB,KAAK,EAAE,EAAEgB,EAAE,EAAE;YACrC,MAAMC,EAAE,GAAGtB,EAAE,GAAGqB,EAAE,GAAG3C,YAAY,GAAGQ,MAAM;YAC1C,KAAK,IAAIqC,EAAE,GAAGb,KAAK,EAAEa,EAAE,GAAGZ,KAAK,EAAE,EAAEY,EAAE,EAAE;cACrC,MAAMC,EAAE,GAAGf,EAAE,GAAGc,EAAE,GAAG5C,WAAW,GAAGI,OAAO;cAC1CmC,OAAO,IAAKtB,IAAI,CAACH,GAAG,CAAC0B,CAAC,EAAEG,EAAE,EAAEE,EAAE,EAAET,EAAE,CAAY,GACzChB,KAAK,CAACN,GAAG,CAAC0B,CAAC,EAAEE,EAAE,EAAEE,EAAE,EAAET,EAAE,CAAY;;;;QAI9ChC,EAAE,CAAC2C,GAAG,CAACP,OAAO,EAAElB,EAAE,EAAES,EAAE,EAAEM,EAAE,EAAEE,EAAE,CAAC;;;;EAKrC,OAAOnD,OAAO,CAAC4D,cAAc,CAAC5C,EAAE,CAACL,KAAK,EAAEK,EAAE,CAACe,KAAK,EAAEf,EAAE,CAACa,MAAM,CAAC;AAC9D;AAEA,OAAO,MAAMgC,yCAAyC,GAAiB;EACrEC,UAAU,EAAEpE,mCAAmC;EAC/CqE,WAAW,EAAE,KAAK;EAClBC,UAAU,EAAEnE;CACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}