{"ast": null, "code": "/**\n * @license\n * Copyright 2019 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { op } from '../operation';\nimport { cosineWindow } from '../signal_ops_util';\n/**\n * Generate a Hann window.\n *\n * See: https://en.wikipedia.org/wiki/Window_function#Hann_and_Hamming_windows\n *\n * ```js\n * tf.signal.hannWindow(10).print();\n * ```\n * @param The length of window\n *\n * @doc {heading: 'Operations', subheading: 'Signal', namespace: 'signal'}\n */\nfunction hannWindow_(windowLength) {\n  return cosineWindow(windowLength, 0.5, 0.5);\n}\nexport const hannWindow = /* @__PURE__ */op({\n  hannWindow_\n});", "map": {"version": 3, "names": ["op", "cosineWindow", "hann<PERSON><PERSON>ow_", "windowLength", "hann<PERSON><PERSON><PERSON>"], "sources": ["C:\\tfjs-core\\src\\ops\\signal\\hann_window.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2019 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {Tensor1D} from '../../tensor';\nimport {op} from '../operation';\nimport {cosineWindow} from '../signal_ops_util';\n\n/**\n * Generate a Hann window.\n *\n * See: https://en.wikipedia.org/wiki/Window_function#Hann_and_Hamming_windows\n *\n * ```js\n * tf.signal.hannWindow(10).print();\n * ```\n * @param The length of window\n *\n * @doc {heading: 'Operations', subheading: 'Signal', namespace: 'signal'}\n */\nfunction hannWindow_(windowLength: number): Tensor1D {\n  return cosineWindow(windowLength, 0.5, 0.5);\n}\n\nexport const hannWindow = /* @__PURE__ */ op({hannWindow_});\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAkBA,SAAQA,EAAE,QAAO,cAAc;AAC/B,SAAQC,YAAY,QAAO,oBAAoB;AAE/C;;;;;;;;;;;;AAYA,SAASC,WAAWA,CAACC,YAAoB;EACvC,OAAOF,YAAY,CAACE,YAAY,EAAE,GAAG,EAAE,GAAG,CAAC;AAC7C;AAEA,OAAO,MAAMC,UAAU,GAAG,eAAgBJ,EAAE,CAAC;EAACE;AAAW,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}