{"ast": null, "code": "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { dilation2d } from '../../ops/dilation2d';\nimport { getGlobalTensorClass } from '../../tensor';\ngetGlobalTensorClass().prototype.dilation2d = function (filter, strides, pad, dilations, dataFormat) {\n  this.throwIfDisposed();\n  return dilation2d(this, filter, strides, pad, dilations, dataFormat);\n};", "map": {"version": 3, "names": ["dilation2d", "getGlobalTensorClass", "prototype", "filter", "strides", "pad", "dilations", "dataFormat", "throwIfDisposed"], "sources": ["C:\\tfjs-core\\src\\public\\chained_ops\\dilation2d.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport {dilation2d} from '../../ops/dilation2d';\nimport {getGlobalTensorClass, Tensor3D, Tensor4D} from '../../tensor';\nimport {Rank, TensorLike3D} from '../../types';\n\ndeclare module '../../tensor' {\n  interface Tensor<R extends Rank = Rank> {\n    dilation2d<T extends Tensor3D|Tensor4D>(\n        filter: Tensor3D|TensorLike3D, strides: [number, number]|number,\n        pad: 'valid'|'same', dilations?: [number, number]|number,\n        dataFormat?: 'NHWC'): T;\n  }\n}\n\ngetGlobalTensorClass().prototype.dilation2d =\n    function<T extends Tensor3D|Tensor4D>(\n        filter: Tensor3D|TensorLike3D, strides: [number, number]|number,\n        pad: 'valid'|'same', dilations?: [number, number]|number,\n        dataFormat?: 'NHWC'): T {\n  this.throwIfDisposed();\n  return dilation2d(this, filter, strides, pad, dilations, dataFormat) as T;\n};\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAgBA,SAAQA,UAAU,QAAO,sBAAsB;AAC/C,SAAQC,oBAAoB,QAA2B,cAAc;AAYrEA,oBAAoB,EAAE,CAACC,SAAS,CAACF,UAAU,GACvC,UACIG,MAA6B,EAAEC,OAAgC,EAC/DC,GAAmB,EAAEC,SAAmC,EACxDC,UAAmB;EACzB,IAAI,CAACC,eAAe,EAAE;EACtB,OAAOR,UAAU,CAAC,IAAI,EAAEG,MAAM,EAAEC,OAAO,EAAEC,GAAG,EAAEC,SAAS,EAAEC,UAAU,CAAM;AAC3E,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}