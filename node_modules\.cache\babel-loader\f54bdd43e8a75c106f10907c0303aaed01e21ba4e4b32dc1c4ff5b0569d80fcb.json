{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.transformReply = exports.transformArguments = exports.IS_READ_ONLY = exports.FIRST_KEY_INDEX = void 0;\nconst generic_transformers_1 = require(\"./generic-transformers\");\nexports.FIRST_KEY_INDEX = 1;\nexports.IS_READ_ONLY = true;\nfunction transformArguments(key, cursor, options) {\n  return (0, generic_transformers_1.pushScanArguments)(['SSCAN', key], cursor, options);\n}\nexports.transformArguments = transformArguments;\nfunction transformReply([cursor, members]) {\n  return {\n    cursor: Number(cursor),\n    members\n  };\n}\nexports.transformReply = transformReply;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "transformReply", "transformArguments", "IS_READ_ONLY", "FIRST_KEY_INDEX", "generic_transformers_1", "require", "key", "cursor", "options", "pushScanArguments", "members", "Number"], "sources": ["C:/tmsft/node_modules/@redis/client/dist/lib/commands/SSCAN.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.transformReply = exports.transformArguments = exports.IS_READ_ONLY = exports.FIRST_KEY_INDEX = void 0;\nconst generic_transformers_1 = require(\"./generic-transformers\");\nexports.FIRST_KEY_INDEX = 1;\nexports.IS_READ_ONLY = true;\nfunction transformArguments(key, cursor, options) {\n    return (0, generic_transformers_1.pushScanArguments)([\n        'SSCAN',\n        key,\n    ], cursor, options);\n}\nexports.transformArguments = transformArguments;\nfunction transformReply([cursor, members]) {\n    return {\n        cursor: Number(cursor),\n        members\n    };\n}\nexports.transformReply = transformReply;\n"], "mappings": "AAAA,YAAY;;AACZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEC,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7DD,OAAO,CAACE,cAAc,GAAGF,OAAO,CAACG,kBAAkB,GAAGH,OAAO,CAACI,YAAY,GAAGJ,OAAO,CAACK,eAAe,GAAG,KAAK,CAAC;AAC7G,MAAMC,sBAAsB,GAAGC,OAAO,CAAC,wBAAwB,CAAC;AAChEP,OAAO,CAACK,eAAe,GAAG,CAAC;AAC3BL,OAAO,CAACI,YAAY,GAAG,IAAI;AAC3B,SAASD,kBAAkBA,CAACK,GAAG,EAAEC,MAAM,EAAEC,OAAO,EAAE;EAC9C,OAAO,CAAC,CAAC,EAAEJ,sBAAsB,CAACK,iBAAiB,EAAE,CACjD,OAAO,EACPH,GAAG,CACN,EAAEC,MAAM,EAAEC,OAAO,CAAC;AACvB;AACAV,OAAO,CAACG,kBAAkB,GAAGA,kBAAkB;AAC/C,SAASD,cAAcA,CAAC,CAACO,MAAM,EAAEG,OAAO,CAAC,EAAE;EACvC,OAAO;IACHH,MAAM,EAAEI,MAAM,CAACJ,MAAM,CAAC;IACtBG;EACJ,CAAC;AACL;AACAZ,OAAO,CAACE,cAAc,GAAGA,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}