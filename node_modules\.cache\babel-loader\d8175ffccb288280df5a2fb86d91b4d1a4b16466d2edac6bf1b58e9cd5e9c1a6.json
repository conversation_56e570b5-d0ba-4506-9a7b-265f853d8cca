{"ast": null, "code": "/**\n * @license\n * Copyright 2023 Google LLC.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { BitwiseAnd, env } from '@tensorflow/tfjs-core';\nimport { BinaryOpProgram } from '../binaryop_gpu';\nimport { BinaryOpPackedProgram } from '../binaryop_packed_gpu';\nimport { bitwiseAndImplCPU as cpuBitwiseAnd } from '../kernel_utils/shared';\nexport const BITWISEAND = \"\\n  int r = int(a.r) & int(b.r);\\n  int g = int(a.g) & int(b.g);\\n  int rb = int(a.b) & int(b.b);\\n  int ra = int(a.a) & int(b.a);\\n  return vec4(r, g, rb, ra);\\n\";\nexport const BITWISEAND_UNPACKED = \"\\n  return float(int(a.r) & int(b.r));\\n\";\nexport function bitwiseAnd(args) {\n  const {\n    inputs,\n    backend\n  } = args;\n  const {\n    a,\n    b\n  } = inputs;\n  const shouldUsePackedProgram = env().getBool('WEBGL_PACK_BINARY_OPERATIONS');\n  const versionNumber = env().getNumber('WEBGL_VERSION');\n  // The type of a and b are ensured to be `int32` in core, therefore no need to\n  // consider other type situations.\n  if (backend.shouldExecuteOnCPU([a, b]) || versionNumber === 1) {\n    const aVals = backend.texData.get(a.dataId).values;\n    const bVals = backend.texData.get(b.dataId).values;\n    const [outValues, outShape] = cpuBitwiseAnd(a.shape, b.shape, aVals, bVals, a.dtype);\n    const out = backend.makeTensorInfo(outShape, a.dtype);\n    const outData = backend.texData.get(out.dataId);\n    outData.values = outValues;\n    return out;\n  }\n  let program;\n  if (shouldUsePackedProgram) {\n    program = new BinaryOpPackedProgram(BITWISEAND, a.shape, b.shape, false);\n  } else {\n    program = new BinaryOpProgram(BITWISEAND_UNPACKED, a.shape, b.shape);\n  }\n  return backend.runWebGLProgram(program, [a, b], a.dtype);\n}\nexport const bitwiseAndConfig = {\n  kernelName: BitwiseAnd,\n  backendName: 'webgl',\n  kernelFunc: bitwiseAnd\n};", "map": {"version": 3, "names": ["BitwiseAnd", "env", "BinaryOpProgram", "BinaryOpPackedProgram", "bitwiseAndImplCPU", "cpuBitwiseAnd", "BITWISEAND", "BITWISEAND_UNPACKED", "bitwiseAnd", "args", "inputs", "backend", "a", "b", "shouldUsePackedProgram", "getBool", "versionNumber", "getNumber", "shouldExecuteOnCPU", "aVals", "texData", "get", "dataId", "values", "bVals", "outValues", "outShape", "shape", "dtype", "out", "makeTensorInfo", "outData", "program", "runWebGLProgram", "bitwiseAndConfig", "kernelName", "backendName", "kernelFunc"], "sources": ["C:\\tfjs-backend-webgl\\src\\kernels\\BitwiseAnd.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2023 Google LLC.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {BitwiseAnd, BitwiseAndInputs, env, KernelConfig, KernelFunc, TensorInfo, TypedArray} from '@tensorflow/tfjs-core';\nimport {MathBackendWebGL} from '../backend_webgl';\nimport {BinaryOpProgram} from '../binaryop_gpu';\nimport {BinaryOpPackedProgram} from '../binaryop_packed_gpu';\nimport {bitwiseAndImplCPU as cpuBitwiseAnd} from '../kernel_utils/shared';\n\nexport const BITWISEAND = `\n  int r = int(a.r) & int(b.r);\n  int g = int(a.g) & int(b.g);\n  int rb = int(a.b) & int(b.b);\n  int ra = int(a.a) & int(b.a);\n  return vec4(r, g, rb, ra);\n`;\n\nexport const BITWISEAND_UNPACKED = `\n  return float(int(a.r) & int(b.r));\n`;\n\nexport function bitwiseAnd(args: {\n  inputs: BitwiseAndInputs,\n  backend: MathBackendWebGL,\n}): TensorInfo {\n  const {inputs, backend} = args;\n  const {a, b} = inputs;\n  const shouldUsePackedProgram = env().getBool('WEBGL_PACK_BINARY_OPERATIONS');\n  const versionNumber = env().getNumber('WEBGL_VERSION');\n\n  // The type of a and b are ensured to be `int32` in core, therefore no need to\n  // consider other type situations.\n  if ((backend.shouldExecuteOnCPU([a, b])) || versionNumber === 1) {\n    const aVals = backend.texData.get(a.dataId).values as TypedArray;\n    const bVals = backend.texData.get(b.dataId).values as TypedArray;\n    const [outValues, outShape] =\n        cpuBitwiseAnd(a.shape, b.shape, aVals, bVals, a.dtype);\n\n    const out = backend.makeTensorInfo(outShape, a.dtype);\n    const outData = backend.texData.get(out.dataId);\n    outData.values = outValues;\n    return out;\n  }\n\n  let program: BinaryOpProgram|BinaryOpPackedProgram;\n  if (shouldUsePackedProgram) {\n    program = new BinaryOpPackedProgram(BITWISEAND, a.shape, b.shape, false);\n  } else {\n    program = new BinaryOpProgram(BITWISEAND_UNPACKED, a.shape, b.shape);\n  }\n\n  return backend.runWebGLProgram(program, [a, b], a.dtype);\n}\n\nexport const bitwiseAndConfig: KernelConfig = {\n  kernelName: BitwiseAnd,\n  backendName: 'webgl',\n  kernelFunc: bitwiseAnd as unknown as KernelFunc\n};\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAiBA,SAAQA,UAAU,EAAoBC,GAAG,QAAyD,uBAAuB;AAEzH,SAAQC,eAAe,QAAO,iBAAiB;AAC/C,SAAQC,qBAAqB,QAAO,wBAAwB;AAC5D,SAAQC,iBAAiB,IAAIC,aAAa,QAAO,wBAAwB;AAEzE,OAAO,MAAMC,UAAU,uKAMtB;AAED,OAAO,MAAMC,mBAAmB,6CAE/B;AAED,OAAM,SAAUC,UAAUA,CAACC,IAG1B;EACC,MAAM;IAACC,MAAM;IAAEC;EAAO,CAAC,GAAGF,IAAI;EAC9B,MAAM;IAACG,CAAC;IAAEC;EAAC,CAAC,GAAGH,MAAM;EACrB,MAAMI,sBAAsB,GAAGb,GAAG,EAAE,CAACc,OAAO,CAAC,8BAA8B,CAAC;EAC5E,MAAMC,aAAa,GAAGf,GAAG,EAAE,CAACgB,SAAS,CAAC,eAAe,CAAC;EAEtD;EACA;EACA,IAAKN,OAAO,CAACO,kBAAkB,CAAC,CAACN,CAAC,EAAEC,CAAC,CAAC,CAAC,IAAKG,aAAa,KAAK,CAAC,EAAE;IAC/D,MAAMG,KAAK,GAAGR,OAAO,CAACS,OAAO,CAACC,GAAG,CAACT,CAAC,CAACU,MAAM,CAAC,CAACC,MAAoB;IAChE,MAAMC,KAAK,GAAGb,OAAO,CAACS,OAAO,CAACC,GAAG,CAACR,CAAC,CAACS,MAAM,CAAC,CAACC,MAAoB;IAChE,MAAM,CAACE,SAAS,EAAEC,QAAQ,CAAC,GACvBrB,aAAa,CAACO,CAAC,CAACe,KAAK,EAAEd,CAAC,CAACc,KAAK,EAAER,KAAK,EAAEK,KAAK,EAAEZ,CAAC,CAACgB,KAAK,CAAC;IAE1D,MAAMC,GAAG,GAAGlB,OAAO,CAACmB,cAAc,CAACJ,QAAQ,EAAEd,CAAC,CAACgB,KAAK,CAAC;IACrD,MAAMG,OAAO,GAAGpB,OAAO,CAACS,OAAO,CAACC,GAAG,CAACQ,GAAG,CAACP,MAAM,CAAC;IAC/CS,OAAO,CAACR,MAAM,GAAGE,SAAS;IAC1B,OAAOI,GAAG;;EAGZ,IAAIG,OAA8C;EAClD,IAAIlB,sBAAsB,EAAE;IAC1BkB,OAAO,GAAG,IAAI7B,qBAAqB,CAACG,UAAU,EAAEM,CAAC,CAACe,KAAK,EAAEd,CAAC,CAACc,KAAK,EAAE,KAAK,CAAC;GACzE,MAAM;IACLK,OAAO,GAAG,IAAI9B,eAAe,CAACK,mBAAmB,EAAEK,CAAC,CAACe,KAAK,EAAEd,CAAC,CAACc,KAAK,CAAC;;EAGtE,OAAOhB,OAAO,CAACsB,eAAe,CAACD,OAAO,EAAE,CAACpB,CAAC,EAAEC,CAAC,CAAC,EAAED,CAAC,CAACgB,KAAK,CAAC;AAC1D;AAEA,OAAO,MAAMM,gBAAgB,GAAiB;EAC5CC,UAAU,EAAEnC,UAAU;EACtBoC,WAAW,EAAE,OAAO;EACpBC,UAAU,EAAE7B;CACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}