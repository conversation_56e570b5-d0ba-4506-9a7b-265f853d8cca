{"ast": null, "code": "/**\n * @license\n * Copyright 2017 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nexport class Conv2DDerFilterProgram {\n  constructor(convInfo) {\n    this.variableNames = ['x', 'dy'];\n    this.outputShape = convInfo.filterShape;\n    const strideHeight = convInfo.strideHeight;\n    const strideWidth = convInfo.strideWidth;\n    const padTop = convInfo.padInfo.top;\n    const padLeft = convInfo.padInfo.left;\n    const isChannelsLast = convInfo.dataFormat === 'channelsLast';\n    this.userCode = \"\\n      void main() {\\n        ivec4 coords = getOutputCoords();\\n        int wR = coords.x;\\n        int wC = coords.y;\\n        int d1 = coords.z;\\n        int d2 = coords.w;\\n\\n        // Convolve x(?, ?, d1) with dy(:, :, d2) to get dw(wR, wC, d1, d2).\\n        // ? = to be determined. : = across all values in that axis.\\n        float dotProd = 0.0;\\n\\n        for (int b = 0; b < \".concat(convInfo.batchSize, \"; b++) {\\n          for (int yR = 0; yR < \").concat(convInfo.outHeight, \"; yR++) {\\n            int xR = wR + yR * \").concat(strideHeight, \" - \").concat(padTop, \";\\n\\n            if (xR < 0 || xR >= \").concat(convInfo.inHeight, \") {\\n              continue;\\n            }\\n\\n            for (int yC = 0; yC < \").concat(convInfo.outWidth, \"; yC++) {\\n              int xC = wC + yC * \").concat(strideWidth, \" - \").concat(padLeft, \";\\n\\n              if (xC < 0 || xC >= \").concat(convInfo.inWidth, \") {\\n                continue;\\n              }\\n\\n              \").concat(isChannelsLast ? \"float dyValue = getDy(b, yR, yC, d2);\\n              float xValue = getX(b, xR, xC, d1);\\n              dotProd += (xValue * dyValue);\" : \"float dyValue = getDy(b, d2, yR, yC);\\n              float xValue = getX(b, d1, xR, xC);\\n              dotProd += (xValue * dyValue);\", \"\\n            }\\n          }\\n        }\\n        setOutput(dotProd);\\n      }\\n    \");\n  }\n}\nexport class Conv2DDerInputProgram {\n  constructor(convInfo) {\n    this.variableNames = ['dy', 'W'];\n    this.outputShape = convInfo.inShape;\n    const filterHeight = convInfo.filterHeight;\n    const filterWidth = convInfo.filterWidth;\n    const strideHeight = convInfo.strideHeight;\n    const strideWidth = convInfo.strideWidth;\n    const isChannelsLast = convInfo.dataFormat === 'channelsLast';\n    const padTop = filterHeight - 1 - convInfo.padInfo.top;\n    const padLeft = filterWidth - 1 - convInfo.padInfo.left;\n    const rowDim = isChannelsLast ? 1 : 2;\n    const colDim = isChannelsLast ? 2 : 3;\n    const channelDim = isChannelsLast ? 3 : 1;\n    this.userCode = \"\\n      const ivec2 pads = ivec2(\".concat(padTop, \", \").concat(padLeft, \");\\n\\n      void main() {\\n        ivec4 coords = getOutputCoords();\\n        int batch = coords[0];\\n        int d1 = coords[\").concat(channelDim, \"];\\n\\n        ivec2 dyCorner = ivec2(coords[\").concat(rowDim, \"], coords[\").concat(colDim, \"]) - pads;\\n        int dyRCorner = dyCorner.x;\\n        int dyCCorner = dyCorner.y;\\n\\n        // Convolve dy(?, ?, d2) with w(:, :, d1, d2) to compute dx(xR, xC, d1).\\n        // ? = to be determined. : = across all values in that axis.\\n        float dotProd = 0.0;\\n        for (int wR = 0; wR < \").concat(filterHeight, \"; wR++) {\\n          float dyR = float(dyRCorner + wR) / \").concat(strideHeight, \".0;\\n\\n          if (dyR < 0.0 || dyR >= \").concat(convInfo.outHeight, \".0 || fract(dyR) > 0.0) {\\n            continue;\\n          }\\n          int idyR = int(dyR);\\n\\n          int wRPerm = \").concat(filterHeight, \" - 1 - wR;\\n\\n          for (int wC = 0; wC < \").concat(filterWidth, \"; wC++) {\\n            float dyC = float(dyCCorner + wC) / \").concat(strideWidth, \".0;\\n\\n            if (dyC < 0.0 || dyC >= \").concat(convInfo.outWidth, \".0 ||\\n                fract(dyC) > 0.0) {\\n              continue;\\n            }\\n            int idyC = int(dyC);\\n\\n            int wCPerm = \").concat(filterWidth, \" - 1 - wC;\\n\\n            for (int d2 = 0; d2 < \").concat(convInfo.outChannels, \"; d2++) {\\n\\n              if (\").concat(isChannelsLast, \") {\\n                float xValue = getDy(batch, idyR, idyC, d2);\\n                float wValue = getW(wRPerm, wCPerm, d1, d2);\\n                dotProd += xValue * wValue;\\n              } else {\\n                float xValue = getDy(batch, d2, idyR, idyC);\\n                float wValue = getW(wRPerm, wCPerm, d1, d2);\\n                dotProd += xValue * wValue;\\n              }\\n\\n            }\\n          }\\n        }\\n        setOutput(dotProd);\\n      }\\n    \");\n  }\n}\nexport class Conv3DDerFilterProgram {\n  constructor(convInfo) {\n    this.variableNames = ['x', 'dy'];\n    this.outputShape = convInfo.filterShape;\n    const strideDepth = convInfo.strideDepth;\n    const strideHeight = convInfo.strideHeight;\n    const strideWidth = convInfo.strideWidth;\n    const padFront = convInfo.padInfo.front;\n    const padTop = convInfo.padInfo.top;\n    const padLeft = convInfo.padInfo.left;\n    this.userCode = \"\\n      void main() {\\n        ivec5 coords = getOutputCoords();\\n        int wF = coords.x;\\n        int wR = coords.y;\\n        int wC = coords.z;\\n        int d1 = coords.w;\\n        int d2 = coords.u;\\n\\n        float dotProd = 0.0;\\n\\n        for (int b = 0; b < \".concat(convInfo.batchSize, \"; b++) {\\n          for (int yF = 0; yF < \").concat(convInfo.outDepth, \"; yF++) {\\n            int xF = wF + yF * \").concat(strideDepth, \" - \").concat(padFront, \";\\n\\n            if (xF < 0 || xF >= \").concat(convInfo.inDepth, \") {\\n              continue;\\n            }\\n\\n            for (int yR = 0; yR < \").concat(convInfo.outHeight, \"; yR++) {\\n              int xR = wR + yR * \").concat(strideHeight, \" - \").concat(padTop, \";\\n\\n              if (xR < 0 || xR >= \").concat(convInfo.inHeight, \") {\\n                continue;\\n              }\\n\\n              for (int yC = 0; yC < \").concat(convInfo.outWidth, \"; yC++) {\\n                int xC = wC + yC * \").concat(strideWidth, \" - \").concat(padLeft, \";\\n\\n                if (xC < 0 || xC >= \").concat(convInfo.inWidth, \") {\\n                  continue;\\n                }\\n\\n                float dyValue = getDy(b, yF, yR, yC, d2);\\n                float xValue = getX(b, xF, xR, xC, d1);\\n                dotProd += (xValue * dyValue);\\n              }\\n            }\\n          }\\n        }\\n        setOutput(dotProd);\\n      }\\n    \");\n  }\n}\nexport class Conv3DDerInputProgram {\n  constructor(convInfo) {\n    this.variableNames = ['dy', 'W'];\n    this.outputShape = convInfo.inShape;\n    const filterDepth = convInfo.filterDepth;\n    const filterHeight = convInfo.filterHeight;\n    const filterWidth = convInfo.filterWidth;\n    const strideDepth = convInfo.strideDepth;\n    const strideHeight = convInfo.strideHeight;\n    const strideWidth = convInfo.strideWidth;\n    const padFront = filterDepth - 1 - convInfo.padInfo.front;\n    const padTop = filterHeight - 1 - convInfo.padInfo.top;\n    const padLeft = filterWidth - 1 - convInfo.padInfo.left;\n    this.userCode = \"\\n      const ivec3 pads = ivec3(\".concat(padFront, \", \").concat(padTop, \", \").concat(padLeft, \");\\n\\n      void main() {\\n        ivec5 coords = getOutputCoords();\\n        int batch = coords.x;\\n        int d1 = coords.u;\\n\\n\\n        ivec3 dyCorner = ivec3(coords.y, coords.z, coords.w) - pads;\\n        int dyFCorner = dyCorner.x;\\n        int dyRCorner = dyCorner.y;\\n        int dyCCorner = dyCorner.z;\\n\\n        float dotProd = 0.0;\\n        for (int wF = 0; wF < \").concat(filterDepth, \"; wF++) {\\n          float dyF = float(dyFCorner + wF) / \").concat(strideDepth, \".0;\\n\\n          if (dyF < 0.0 || dyF >= \").concat(convInfo.outDepth, \".0 || fract(dyF) > 0.0) {\\n            continue;\\n          }\\n          int idyF = int(dyF);\\n\\n          int wFPerm = \").concat(filterDepth, \" - 1 - wF;\\n\\n          for (int wR = 0; wR < \").concat(filterHeight, \"; wR++) {\\n            float dyR = float(dyRCorner + wR) / \").concat(strideHeight, \".0;\\n\\n            if (dyR < 0.0 || dyR >= \").concat(convInfo.outHeight, \".0 ||\\n              fract(dyR) > 0.0) {\\n              continue;\\n            }\\n            int idyR = int(dyR);\\n\\n            int wRPerm = \").concat(filterHeight, \" - 1 - wR;\\n\\n            for (int wC = 0; wC < \").concat(filterWidth, \"; wC++) {\\n              float dyC = float(dyCCorner + wC) / \").concat(strideWidth, \".0;\\n\\n              if (dyC < 0.0 || dyC >= \").concat(convInfo.outWidth, \".0 ||\\n                  fract(dyC) > 0.0) {\\n                continue;\\n              }\\n              int idyC = int(dyC);\\n\\n              int wCPerm = \").concat(filterWidth, \" - 1 - wC;\\n\\n              for (int d2 = 0; d2 < \").concat(convInfo.outChannels, \"; d2++) {\\n                float xValue = getDy(batch, idyF, idyR, idyC, d2);\\n                float wValue = getW(wFPerm, wRPerm, wCPerm, d1, d2);\\n                dotProd += xValue * wValue;\\n              }\\n            }\\n          }\\n        }\\n        setOutput(dotProd);\\n      }\\n    \");\n  }\n}", "map": {"version": 3, "names": ["Conv2DDerFilterProgram", "constructor", "convInfo", "variableNames", "outputShape", "filterShape", "strideHeight", "<PERSON><PERSON><PERSON><PERSON>", "padTop", "padInfo", "top", "padLeft", "left", "isChannelsLast", "dataFormat", "userCode", "concat", "batchSize", "outHeight", "inHeight", "outWidth", "inWidth", "Conv2DDerInputProgram", "inShape", "filterHeight", "filterWidth", "row<PERSON><PERSON>", "col<PERSON><PERSON>", "channelDim", "outChannels", "Conv3DDerFilterProgram", "<PERSON><PERSON><PERSON>h", "padFront", "front", "outDepth", "inDepth", "Conv3DDerInputProgram", "<PERSON><PERSON><PERSON><PERSON>"], "sources": ["C:\\tfjs-backend-webgl\\src\\conv_backprop_gpu.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2017 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {backend_util} from '@tensorflow/tfjs-core';\nimport {GPGPUProgram} from './gpgpu_math';\n\nexport class Conv2DDerFilterProgram implements GPGPUProgram {\n  variableNames = ['x', 'dy'];\n  outputShape: number[];\n  userCode: string;\n\n  constructor(convInfo: backend_util.Conv2DInfo) {\n    this.outputShape = convInfo.filterShape;\n\n    const strideHeight = convInfo.strideHeight;\n    const strideWidth = convInfo.strideWidth;\n    const padTop = convInfo.padInfo.top;\n    const padLeft = convInfo.padInfo.left;\n    const isChannelsLast = convInfo.dataFormat === 'channelsLast';\n\n    this.userCode = `\n      void main() {\n        ivec4 coords = getOutputCoords();\n        int wR = coords.x;\n        int wC = coords.y;\n        int d1 = coords.z;\n        int d2 = coords.w;\n\n        // Convolve x(?, ?, d1) with dy(:, :, d2) to get dw(wR, wC, d1, d2).\n        // ? = to be determined. : = across all values in that axis.\n        float dotProd = 0.0;\n\n        for (int b = 0; b < ${convInfo.batchSize}; b++) {\n          for (int yR = 0; yR < ${convInfo.outHeight}; yR++) {\n            int xR = wR + yR * ${strideHeight} - ${padTop};\n\n            if (xR < 0 || xR >= ${convInfo.inHeight}) {\n              continue;\n            }\n\n            for (int yC = 0; yC < ${convInfo.outWidth}; yC++) {\n              int xC = wC + yC * ${strideWidth} - ${padLeft};\n\n              if (xC < 0 || xC >= ${convInfo.inWidth}) {\n                continue;\n              }\n\n              ${isChannelsLast?\n             `float dyValue = getDy(b, yR, yC, d2);\n              float xValue = getX(b, xR, xC, d1);\n              dotProd += (xValue * dyValue);` :\n             `float dyValue = getDy(b, d2, yR, yC);\n              float xValue = getX(b, d1, xR, xC);\n              dotProd += (xValue * dyValue);`}\n            }\n          }\n        }\n        setOutput(dotProd);\n      }\n    `;\n  }\n}\n\nexport class Conv2DDerInputProgram implements GPGPUProgram {\n  variableNames = ['dy', 'W'];\n  outputShape: number[];\n  userCode: string;\n\n  constructor(convInfo: backend_util.Conv2DInfo) {\n    this.outputShape = convInfo.inShape;\n\n    const filterHeight = convInfo.filterHeight;\n    const filterWidth = convInfo.filterWidth;\n    const strideHeight = convInfo.strideHeight;\n    const strideWidth = convInfo.strideWidth;\n    const isChannelsLast = convInfo.dataFormat === 'channelsLast';\n\n    const padTop = filterHeight - 1 - convInfo.padInfo.top;\n    const padLeft = filterWidth - 1 - convInfo.padInfo.left;\n\n    const rowDim = isChannelsLast ? 1 : 2;\n    const colDim = isChannelsLast ? 2 : 3;\n    const channelDim = isChannelsLast ? 3 : 1;\n\n    this.userCode = `\n      const ivec2 pads = ivec2(${padTop}, ${padLeft});\n\n      void main() {\n        ivec4 coords = getOutputCoords();\n        int batch = coords[0];\n        int d1 = coords[${channelDim}];\n\n        ivec2 dyCorner = ivec2(coords[${rowDim}], coords[${colDim}]) - pads;\n        int dyRCorner = dyCorner.x;\n        int dyCCorner = dyCorner.y;\n\n        // Convolve dy(?, ?, d2) with w(:, :, d1, d2) to compute dx(xR, xC, d1).\n        // ? = to be determined. : = across all values in that axis.\n        float dotProd = 0.0;\n        for (int wR = 0; wR < ${filterHeight}; wR++) {\n          float dyR = float(dyRCorner + wR) / ${strideHeight}.0;\n\n          if (dyR < 0.0 || dyR >= ${convInfo.outHeight}.0 || fract(dyR) > 0.0) {\n            continue;\n          }\n          int idyR = int(dyR);\n\n          int wRPerm = ${filterHeight} - 1 - wR;\n\n          for (int wC = 0; wC < ${filterWidth}; wC++) {\n            float dyC = float(dyCCorner + wC) / ${strideWidth}.0;\n\n            if (dyC < 0.0 || dyC >= ${convInfo.outWidth}.0 ||\n                fract(dyC) > 0.0) {\n              continue;\n            }\n            int idyC = int(dyC);\n\n            int wCPerm = ${filterWidth} - 1 - wC;\n\n            for (int d2 = 0; d2 < ${convInfo.outChannels}; d2++) {\n\n              if (${isChannelsLast}) {\n                float xValue = getDy(batch, idyR, idyC, d2);\n                float wValue = getW(wRPerm, wCPerm, d1, d2);\n                dotProd += xValue * wValue;\n              } else {\n                float xValue = getDy(batch, d2, idyR, idyC);\n                float wValue = getW(wRPerm, wCPerm, d1, d2);\n                dotProd += xValue * wValue;\n              }\n\n            }\n          }\n        }\n        setOutput(dotProd);\n      }\n    `;\n  }\n}\n\nexport class Conv3DDerFilterProgram implements GPGPUProgram {\n  variableNames = ['x', 'dy'];\n  outputShape: number[];\n  userCode: string;\n\n  constructor(convInfo: backend_util.Conv3DInfo) {\n    this.outputShape = convInfo.filterShape;\n\n    const strideDepth = convInfo.strideDepth;\n    const strideHeight = convInfo.strideHeight;\n    const strideWidth = convInfo.strideWidth;\n    const padFront = convInfo.padInfo.front;\n    const padTop = convInfo.padInfo.top;\n    const padLeft = convInfo.padInfo.left;\n\n    this.userCode = `\n      void main() {\n        ivec5 coords = getOutputCoords();\n        int wF = coords.x;\n        int wR = coords.y;\n        int wC = coords.z;\n        int d1 = coords.w;\n        int d2 = coords.u;\n\n        float dotProd = 0.0;\n\n        for (int b = 0; b < ${convInfo.batchSize}; b++) {\n          for (int yF = 0; yF < ${convInfo.outDepth}; yF++) {\n            int xF = wF + yF * ${strideDepth} - ${padFront};\n\n            if (xF < 0 || xF >= ${convInfo.inDepth}) {\n              continue;\n            }\n\n            for (int yR = 0; yR < ${convInfo.outHeight}; yR++) {\n              int xR = wR + yR * ${strideHeight} - ${padTop};\n\n              if (xR < 0 || xR >= ${convInfo.inHeight}) {\n                continue;\n              }\n\n              for (int yC = 0; yC < ${convInfo.outWidth}; yC++) {\n                int xC = wC + yC * ${strideWidth} - ${padLeft};\n\n                if (xC < 0 || xC >= ${convInfo.inWidth}) {\n                  continue;\n                }\n\n                float dyValue = getDy(b, yF, yR, yC, d2);\n                float xValue = getX(b, xF, xR, xC, d1);\n                dotProd += (xValue * dyValue);\n              }\n            }\n          }\n        }\n        setOutput(dotProd);\n      }\n    `;\n  }\n}\n\nexport class Conv3DDerInputProgram implements GPGPUProgram {\n  variableNames = ['dy', 'W'];\n  outputShape: number[];\n  userCode: string;\n\n  constructor(convInfo: backend_util.Conv3DInfo) {\n    this.outputShape = convInfo.inShape;\n\n    const filterDepth = convInfo.filterDepth;\n    const filterHeight = convInfo.filterHeight;\n    const filterWidth = convInfo.filterWidth;\n    const strideDepth = convInfo.strideDepth;\n    const strideHeight = convInfo.strideHeight;\n    const strideWidth = convInfo.strideWidth;\n\n    const padFront = filterDepth - 1 - convInfo.padInfo.front;\n    const padTop = filterHeight - 1 - convInfo.padInfo.top;\n    const padLeft = filterWidth - 1 - convInfo.padInfo.left;\n\n    this.userCode = `\n      const ivec3 pads = ivec3(${padFront}, ${padTop}, ${padLeft});\n\n      void main() {\n        ivec5 coords = getOutputCoords();\n        int batch = coords.x;\n        int d1 = coords.u;\n\n\n        ivec3 dyCorner = ivec3(coords.y, coords.z, coords.w) - pads;\n        int dyFCorner = dyCorner.x;\n        int dyRCorner = dyCorner.y;\n        int dyCCorner = dyCorner.z;\n\n        float dotProd = 0.0;\n        for (int wF = 0; wF < ${filterDepth}; wF++) {\n          float dyF = float(dyFCorner + wF) / ${strideDepth}.0;\n\n          if (dyF < 0.0 || dyF >= ${convInfo.outDepth}.0 || fract(dyF) > 0.0) {\n            continue;\n          }\n          int idyF = int(dyF);\n\n          int wFPerm = ${filterDepth} - 1 - wF;\n\n          for (int wR = 0; wR < ${filterHeight}; wR++) {\n            float dyR = float(dyRCorner + wR) / ${strideHeight}.0;\n\n            if (dyR < 0.0 || dyR >= ${convInfo.outHeight}.0 ||\n              fract(dyR) > 0.0) {\n              continue;\n            }\n            int idyR = int(dyR);\n\n            int wRPerm = ${filterHeight} - 1 - wR;\n\n            for (int wC = 0; wC < ${filterWidth}; wC++) {\n              float dyC = float(dyCCorner + wC) / ${strideWidth}.0;\n\n              if (dyC < 0.0 || dyC >= ${convInfo.outWidth}.0 ||\n                  fract(dyC) > 0.0) {\n                continue;\n              }\n              int idyC = int(dyC);\n\n              int wCPerm = ${filterWidth} - 1 - wC;\n\n              for (int d2 = 0; d2 < ${convInfo.outChannels}; d2++) {\n                float xValue = getDy(batch, idyF, idyR, idyC, d2);\n                float wValue = getW(wFPerm, wRPerm, wCPerm, d1, d2);\n                dotProd += xValue * wValue;\n              }\n            }\n          }\n        }\n        setOutput(dotProd);\n      }\n    `;\n  }\n}\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAoBA,OAAM,MAAOA,sBAAsB;EAKjCC,YAAYC,QAAiC;IAJ7C,KAAAC,aAAa,GAAG,CAAC,GAAG,EAAE,IAAI,CAAC;IAKzB,IAAI,CAACC,WAAW,GAAGF,QAAQ,CAACG,WAAW;IAEvC,MAAMC,YAAY,GAAGJ,QAAQ,CAACI,YAAY;IAC1C,MAAMC,WAAW,GAAGL,QAAQ,CAACK,WAAW;IACxC,MAAMC,MAAM,GAAGN,QAAQ,CAACO,OAAO,CAACC,GAAG;IACnC,MAAMC,OAAO,GAAGT,QAAQ,CAACO,OAAO,CAACG,IAAI;IACrC,MAAMC,cAAc,GAAGX,QAAQ,CAACY,UAAU,KAAK,cAAc;IAE7D,IAAI,CAACC,QAAQ,0YAAAC,MAAA,CAYad,QAAQ,CAACe,SAAS,gDAAAD,MAAA,CACdd,QAAQ,CAACgB,SAAS,gDAAAF,MAAA,CACnBV,YAAY,SAAAU,MAAA,CAAMR,MAAM,2CAAAQ,MAAA,CAEvBd,QAAQ,CAACiB,QAAQ,uFAAAH,MAAA,CAIfd,QAAQ,CAACkB,QAAQ,kDAAAJ,MAAA,CAClBT,WAAW,SAAAS,MAAA,CAAML,OAAO,6CAAAK,MAAA,CAEvBd,QAAQ,CAACmB,OAAO,uEAAAL,MAAA,CAIpCH,cAAc,sRAMe,wFAMxC;EACH;;AAGF,OAAM,MAAOS,qBAAqB;EAKhCrB,YAAYC,QAAiC;IAJ7C,KAAAC,aAAa,GAAG,CAAC,IAAI,EAAE,GAAG,CAAC;IAKzB,IAAI,CAACC,WAAW,GAAGF,QAAQ,CAACqB,OAAO;IAEnC,MAAMC,YAAY,GAAGtB,QAAQ,CAACsB,YAAY;IAC1C,MAAMC,WAAW,GAAGvB,QAAQ,CAACuB,WAAW;IACxC,MAAMnB,YAAY,GAAGJ,QAAQ,CAACI,YAAY;IAC1C,MAAMC,WAAW,GAAGL,QAAQ,CAACK,WAAW;IACxC,MAAMM,cAAc,GAAGX,QAAQ,CAACY,UAAU,KAAK,cAAc;IAE7D,MAAMN,MAAM,GAAGgB,YAAY,GAAG,CAAC,GAAGtB,QAAQ,CAACO,OAAO,CAACC,GAAG;IACtD,MAAMC,OAAO,GAAGc,WAAW,GAAG,CAAC,GAAGvB,QAAQ,CAACO,OAAO,CAACG,IAAI;IAEvD,MAAMc,MAAM,GAAGb,cAAc,GAAG,CAAC,GAAG,CAAC;IACrC,MAAMc,MAAM,GAAGd,cAAc,GAAG,CAAC,GAAG,CAAC;IACrC,MAAMe,UAAU,GAAGf,cAAc,GAAG,CAAC,GAAG,CAAC;IAEzC,IAAI,CAACE,QAAQ,uCAAAC,MAAA,CACgBR,MAAM,QAAAQ,MAAA,CAAKL,OAAO,oIAAAK,MAAA,CAKzBY,UAAU,kDAAAZ,MAAA,CAEIU,MAAM,gBAAAV,MAAA,CAAaW,MAAM,kTAAAX,MAAA,CAOjCQ,YAAY,+DAAAR,MAAA,CACIV,YAAY,+CAAAU,MAAA,CAExBd,QAAQ,CAACgB,SAAS,8HAAAF,MAAA,CAK7BQ,YAAY,oDAAAR,MAAA,CAEHS,WAAW,iEAAAT,MAAA,CACKT,WAAW,iDAAAS,MAAA,CAEvBd,QAAQ,CAACkB,QAAQ,uJAAAJ,MAAA,CAM5BS,WAAW,sDAAAT,MAAA,CAEFd,QAAQ,CAAC2B,WAAW,qCAAAb,MAAA,CAEpCH,cAAc,wdAe7B;EACH;;AAGF,OAAM,MAAOiB,sBAAsB;EAKjC7B,YAAYC,QAAiC;IAJ7C,KAAAC,aAAa,GAAG,CAAC,GAAG,EAAE,IAAI,CAAC;IAKzB,IAAI,CAACC,WAAW,GAAGF,QAAQ,CAACG,WAAW;IAEvC,MAAM0B,WAAW,GAAG7B,QAAQ,CAAC6B,WAAW;IACxC,MAAMzB,YAAY,GAAGJ,QAAQ,CAACI,YAAY;IAC1C,MAAMC,WAAW,GAAGL,QAAQ,CAACK,WAAW;IACxC,MAAMyB,QAAQ,GAAG9B,QAAQ,CAACO,OAAO,CAACwB,KAAK;IACvC,MAAMzB,MAAM,GAAGN,QAAQ,CAACO,OAAO,CAACC,GAAG;IACnC,MAAMC,OAAO,GAAGT,QAAQ,CAACO,OAAO,CAACG,IAAI;IAErC,IAAI,CAACG,QAAQ,kRAAAC,MAAA,CAWad,QAAQ,CAACe,SAAS,gDAAAD,MAAA,CACdd,QAAQ,CAACgC,QAAQ,gDAAAlB,MAAA,CAClBe,WAAW,SAAAf,MAAA,CAAMgB,QAAQ,2CAAAhB,MAAA,CAExBd,QAAQ,CAACiC,OAAO,uFAAAnB,MAAA,CAIdd,QAAQ,CAACgB,SAAS,kDAAAF,MAAA,CACnBV,YAAY,SAAAU,MAAA,CAAMR,MAAM,6CAAAQ,MAAA,CAEvBd,QAAQ,CAACiB,QAAQ,6FAAAH,MAAA,CAIfd,QAAQ,CAACkB,QAAQ,oDAAAJ,MAAA,CAClBT,WAAW,SAAAS,MAAA,CAAML,OAAO,+CAAAK,MAAA,CAEvBd,QAAQ,CAACmB,OAAO,kUAajD;EACH;;AAGF,OAAM,MAAOe,qBAAqB;EAKhCnC,YAAYC,QAAiC;IAJ7C,KAAAC,aAAa,GAAG,CAAC,IAAI,EAAE,GAAG,CAAC;IAKzB,IAAI,CAACC,WAAW,GAAGF,QAAQ,CAACqB,OAAO;IAEnC,MAAMc,WAAW,GAAGnC,QAAQ,CAACmC,WAAW;IACxC,MAAMb,YAAY,GAAGtB,QAAQ,CAACsB,YAAY;IAC1C,MAAMC,WAAW,GAAGvB,QAAQ,CAACuB,WAAW;IACxC,MAAMM,WAAW,GAAG7B,QAAQ,CAAC6B,WAAW;IACxC,MAAMzB,YAAY,GAAGJ,QAAQ,CAACI,YAAY;IAC1C,MAAMC,WAAW,GAAGL,QAAQ,CAACK,WAAW;IAExC,MAAMyB,QAAQ,GAAGK,WAAW,GAAG,CAAC,GAAGnC,QAAQ,CAACO,OAAO,CAACwB,KAAK;IACzD,MAAMzB,MAAM,GAAGgB,YAAY,GAAG,CAAC,GAAGtB,QAAQ,CAACO,OAAO,CAACC,GAAG;IACtD,MAAMC,OAAO,GAAGc,WAAW,GAAG,CAAC,GAAGvB,QAAQ,CAACO,OAAO,CAACG,IAAI;IAEvD,IAAI,CAACG,QAAQ,uCAAAC,MAAA,CACgBgB,QAAQ,QAAAhB,MAAA,CAAKR,MAAM,QAAAQ,MAAA,CAAKL,OAAO,8XAAAK,MAAA,CAchCqB,WAAW,+DAAArB,MAAA,CACKe,WAAW,+CAAAf,MAAA,CAEvBd,QAAQ,CAACgC,QAAQ,8HAAAlB,MAAA,CAK5BqB,WAAW,oDAAArB,MAAA,CAEFQ,YAAY,iEAAAR,MAAA,CACIV,YAAY,iDAAAU,MAAA,CAExBd,QAAQ,CAACgB,SAAS,qJAAAF,MAAA,CAM7BQ,YAAY,sDAAAR,MAAA,CAEHS,WAAW,mEAAAT,MAAA,CACKT,WAAW,mDAAAS,MAAA,CAEvBd,QAAQ,CAACkB,QAAQ,iKAAAJ,MAAA,CAM5BS,WAAW,wDAAAT,MAAA,CAEFd,QAAQ,CAAC2B,WAAW,ySAUrD;EACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}