{"ast": null, "code": "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { NotEqual } from '@tensorflow/tfjs-core';\nimport { createSimpleBinaryKernelImpl } from '../utils/binary_impl';\nimport { binaryKernelFunc } from '../utils/binary_utils';\nexport const notEqualImpl = createSimpleBinaryKernelImpl((a, b) => a !== b ? 1 : 0);\nexport const notEqual = binaryKernelFunc(NotEqual, notEqualImpl, null /* complexOp */, 'bool');\nexport const notEqualConfig = {\n  kernelName: NotEqual,\n  backendName: 'cpu',\n  kernelFunc: notEqual\n};", "map": {"version": 3, "names": ["NotEqual", "createSimpleBinaryKernelImpl", "binaryKernelFunc", "notEqualImpl", "a", "b", "notEqual", "notEqualConfig", "kernelName", "backendName", "kernelFunc"], "sources": ["C:\\tfjs-backend-cpu\\src\\kernels\\NotEqual.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {KernelConfig, NotEqual} from '@tensorflow/tfjs-core';\n\nimport {createSimpleBinaryKernelImpl} from '../utils/binary_impl';\nimport {binaryKernelFunc} from '../utils/binary_utils';\n\nexport const notEqualImpl =\n    createSimpleBinaryKernelImpl(((a, b) => (a !== b) ? 1 : 0));\nexport const notEqual =\n    binaryKernelFunc(NotEqual, notEqualImpl, null /* complexOp */, 'bool');\n\nexport const notEqualConfig: KernelConfig = {\n  kernelName: NotEqual,\n  backendName: 'cpu',\n  kernelFunc: notEqual\n};\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAiBA,SAAsBA,QAAQ,QAAO,uBAAuB;AAE5D,SAAQC,4BAA4B,QAAO,sBAAsB;AACjE,SAAQC,gBAAgB,QAAO,uBAAuB;AAEtD,OAAO,MAAMC,YAAY,GACrBF,4BAA4B,CAAE,CAACG,CAAC,EAAEC,CAAC,KAAMD,CAAC,KAAKC,CAAC,GAAI,CAAC,GAAG,CAAE,CAAC;AAC/D,OAAO,MAAMC,QAAQ,GACjBJ,gBAAgB,CAACF,QAAQ,EAAEG,YAAY,EAAE,IAAI,CAAC,iBAAiB,MAAM,CAAC;AAE1E,OAAO,MAAMI,cAAc,GAAiB;EAC1CC,UAAU,EAAER,QAAQ;EACpBS,WAAW,EAAE,KAAK;EAClBC,UAAU,EAAEJ;CACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}