{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.transformReply = exports.transformArguments = exports.IS_READ_ONLY = exports.FIRST_KEY_INDEX = void 0;\nconst generic_transformers_1 = require(\"./generic-transformers\");\nexports.FIRST_KEY_INDEX = 2;\nexports.IS_READ_ONLY = true;\nfunction transformArguments(key) {\n  return ['XINFO', 'STREAM', key];\n}\nexports.transformArguments = transformArguments;\nfunction transformReply(rawReply) {\n  const parsedReply = {};\n  for (let i = 0; i < rawReply.length; i += 2) {\n    switch (rawReply[i]) {\n      case 'length':\n        parsedReply.length = rawReply[i + 1];\n        break;\n      case 'radix-tree-keys':\n        parsedReply.radixTreeKeys = rawReply[i + 1];\n        break;\n      case 'radix-tree-nodes':\n        parsedReply.radixTreeNodes = rawReply[i + 1];\n        break;\n      case 'groups':\n        parsedReply.groups = rawReply[i + 1];\n        break;\n      case 'last-generated-id':\n        parsedReply.lastGeneratedId = rawReply[i + 1];\n        break;\n      case 'first-entry':\n        parsedReply.firstEntry = rawReply[i + 1] ? {\n          id: rawReply[i + 1][0],\n          message: (0, generic_transformers_1.transformTuplesReply)(rawReply[i + 1][1])\n        } : null;\n        break;\n      case 'last-entry':\n        parsedReply.lastEntry = rawReply[i + 1] ? {\n          id: rawReply[i + 1][0],\n          message: (0, generic_transformers_1.transformTuplesReply)(rawReply[i + 1][1])\n        } : null;\n        break;\n    }\n  }\n  return parsedReply;\n}\nexports.transformReply = transformReply;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "transformReply", "transformArguments", "IS_READ_ONLY", "FIRST_KEY_INDEX", "generic_transformers_1", "require", "key", "rawReply", "parsedReply", "i", "length", "radixTreeKeys", "radixTreeNodes", "groups", "lastGeneratedId", "firstEntry", "id", "message", "transformTuplesReply", "lastEntry"], "sources": ["C:/tmsft/node_modules/@redis/client/dist/lib/commands/XINFO_STREAM.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.transformReply = exports.transformArguments = exports.IS_READ_ONLY = exports.FIRST_KEY_INDEX = void 0;\nconst generic_transformers_1 = require(\"./generic-transformers\");\nexports.FIRST_KEY_INDEX = 2;\nexports.IS_READ_ONLY = true;\nfunction transformArguments(key) {\n    return ['XINFO', 'STREAM', key];\n}\nexports.transformArguments = transformArguments;\nfunction transformReply(rawReply) {\n    const parsedReply = {};\n    for (let i = 0; i < rawReply.length; i += 2) {\n        switch (rawReply[i]) {\n            case 'length':\n                parsedReply.length = rawReply[i + 1];\n                break;\n            case 'radix-tree-keys':\n                parsedReply.radixTreeKeys = rawReply[i + 1];\n                break;\n            case 'radix-tree-nodes':\n                parsedReply.radixTreeNodes = rawReply[i + 1];\n                break;\n            case 'groups':\n                parsedReply.groups = rawReply[i + 1];\n                break;\n            case 'last-generated-id':\n                parsedReply.lastGeneratedId = rawReply[i + 1];\n                break;\n            case 'first-entry':\n                parsedReply.firstEntry = rawReply[i + 1] ? {\n                    id: rawReply[i + 1][0],\n                    message: (0, generic_transformers_1.transformTuplesReply)(rawReply[i + 1][1])\n                } : null;\n                break;\n            case 'last-entry':\n                parsedReply.lastEntry = rawReply[i + 1] ? {\n                    id: rawReply[i + 1][0],\n                    message: (0, generic_transformers_1.transformTuplesReply)(rawReply[i + 1][1])\n                } : null;\n                break;\n        }\n    }\n    return parsedReply;\n}\nexports.transformReply = transformReply;\n"], "mappings": "AAAA,YAAY;;AACZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEC,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7DD,OAAO,CAACE,cAAc,GAAGF,OAAO,CAACG,kBAAkB,GAAGH,OAAO,CAACI,YAAY,GAAGJ,OAAO,CAACK,eAAe,GAAG,KAAK,CAAC;AAC7G,MAAMC,sBAAsB,GAAGC,OAAO,CAAC,wBAAwB,CAAC;AAChEP,OAAO,CAACK,eAAe,GAAG,CAAC;AAC3BL,OAAO,CAACI,YAAY,GAAG,IAAI;AAC3B,SAASD,kBAAkBA,CAACK,GAAG,EAAE;EAC7B,OAAO,CAAC,OAAO,EAAE,QAAQ,EAAEA,GAAG,CAAC;AACnC;AACAR,OAAO,CAACG,kBAAkB,GAAGA,kBAAkB;AAC/C,SAASD,cAAcA,CAACO,QAAQ,EAAE;EAC9B,MAAMC,WAAW,GAAG,CAAC,CAAC;EACtB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,QAAQ,CAACG,MAAM,EAAED,CAAC,IAAI,CAAC,EAAE;IACzC,QAAQF,QAAQ,CAACE,CAAC,CAAC;MACf,KAAK,QAAQ;QACTD,WAAW,CAACE,MAAM,GAAGH,QAAQ,CAACE,CAAC,GAAG,CAAC,CAAC;QACpC;MACJ,KAAK,iBAAiB;QAClBD,WAAW,CAACG,aAAa,GAAGJ,QAAQ,CAACE,CAAC,GAAG,CAAC,CAAC;QAC3C;MACJ,KAAK,kBAAkB;QACnBD,WAAW,CAACI,cAAc,GAAGL,QAAQ,CAACE,CAAC,GAAG,CAAC,CAAC;QAC5C;MACJ,KAAK,QAAQ;QACTD,WAAW,CAACK,MAAM,GAAGN,QAAQ,CAACE,CAAC,GAAG,CAAC,CAAC;QACpC;MACJ,KAAK,mBAAmB;QACpBD,WAAW,CAACM,eAAe,GAAGP,QAAQ,CAACE,CAAC,GAAG,CAAC,CAAC;QAC7C;MACJ,KAAK,aAAa;QACdD,WAAW,CAACO,UAAU,GAAGR,QAAQ,CAACE,CAAC,GAAG,CAAC,CAAC,GAAG;UACvCO,EAAE,EAAET,QAAQ,CAACE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;UACtBQ,OAAO,EAAE,CAAC,CAAC,EAAEb,sBAAsB,CAACc,oBAAoB,EAAEX,QAAQ,CAACE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QAChF,CAAC,GAAG,IAAI;QACR;MACJ,KAAK,YAAY;QACbD,WAAW,CAACW,SAAS,GAAGZ,QAAQ,CAACE,CAAC,GAAG,CAAC,CAAC,GAAG;UACtCO,EAAE,EAAET,QAAQ,CAACE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;UACtBQ,OAAO,EAAE,CAAC,CAAC,EAAEb,sBAAsB,CAACc,oBAAoB,EAAEX,QAAQ,CAACE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QAChF,CAAC,GAAG,IAAI;QACR;IACR;EACJ;EACA,OAAOD,WAAW;AACtB;AACAV,OAAO,CAACE,cAAc,GAAGA,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}