{"ast": null, "code": "/**\n * @license\n * Copyright 2018 Google LLC\n *\n * Use of this source code is governed by an MIT-style\n * license that can be found in the LICENSE file or at\n * https://opensource.org/licenses/MIT.\n * =============================================================================\n */\nimport { dispose } from '@tensorflow/tfjs-core';\n/**\n * Turn any Scalar values in a Logs object into actual number values.\n *\n * @param logs The `Logs` object to be resolved in place.\n */\nexport async function resolveScalarsInLogs(logs) {\n  if (logs == null) {\n    return;\n  }\n  const promises = [];\n  const keys = [];\n  const scalarsToDispose = [];\n  for (const key in logs) {\n    const value = logs[key];\n    if (typeof value !== 'number') {\n      const valueScalar = value;\n      promises.push(valueScalar.data());\n      keys.push(key);\n      scalarsToDispose.push(valueScalar);\n    }\n  }\n  if (promises.length > 0) {\n    const values = await Promise.all(promises);\n    for (let i = 0; i < values.length; ++i) {\n      logs[keys[i]] = values[i][0];\n    }\n    // Dispose the original scalar tensors.\n    dispose(scalarsToDispose);\n  }\n}\n/**\n * Dispose all Tensors in an UnresolvedLogs object.\n *\n * @param logs An `UnresolvedLogs` object potentially containing `tf.Tensor`s in\n *   places where the values can be `tf.Tensor` or `number`.\n */\nexport function disposeTensorsInLogs(logs) {\n  if (logs == null) {\n    return;\n  }\n  for (const key in logs) {\n    const value = logs[key];\n    if (typeof value !== 'number') {\n      value.dispose();\n    }\n  }\n}", "map": {"version": 3, "names": ["dispose", "resolveScalarsInLogs", "logs", "promises", "keys", "scalarsToDispose", "key", "value", "valueScalar", "push", "data", "length", "values", "Promise", "all", "i", "disposeTensorsInLogs"], "sources": ["C:\\tfjs-layers\\src\\logs.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2018 Google LLC\n *\n * Use of this source code is governed by an MIT-style\n * license that can be found in the LICENSE file or at\n * https://opensource.org/licenses/MIT.\n * =============================================================================\n */\n\nimport {dispose, Scalar} from '@tensorflow/tfjs-core';\n\n/**\n * Logs in which values can be either numbers or Tensors (Scalars).\n *\n * Used internally.\n */\nexport type UnresolvedLogs = {\n  [key: string]: number|Scalar;\n};\n\n/**\n * Turn any Scalar values in a Logs object into actual number values.\n *\n * @param logs The `Logs` object to be resolved in place.\n */\nexport async function resolveScalarsInLogs(logs: UnresolvedLogs) {\n  if (logs == null) {\n    return;\n  }\n  const promises: Array<Promise<Float32Array|Int32Array|Uint8Array>> = [];\n  const keys: string[] = [];\n  const scalarsToDispose: Scalar[] = [];\n  for (const key in logs) {\n    const value = logs[key];\n    if (typeof value !== 'number') {\n      const valueScalar = value;\n      promises.push(valueScalar.data());\n      keys.push(key);\n      scalarsToDispose.push(valueScalar);\n    }\n  }\n  if (promises.length > 0) {\n    const values = await Promise.all(promises);\n    for (let i = 0; i < values.length; ++i) {\n      logs[keys[i]] = values[i][0];\n    }\n    // Dispose the original scalar tensors.\n    dispose(scalarsToDispose);\n  }\n}\n\n/**\n * Dispose all Tensors in an UnresolvedLogs object.\n *\n * @param logs An `UnresolvedLogs` object potentially containing `tf.Tensor`s in\n *   places where the values can be `tf.Tensor` or `number`.\n */\nexport function disposeTensorsInLogs(logs: UnresolvedLogs) {\n  if (logs == null) {\n    return;\n  }\n  for (const key in logs) {\n    const value = logs[key];\n    if (typeof value !== 'number') {\n      value.dispose();\n    }\n  }\n}\n\n/**\n * Logs in which values can only be numbers.\n *\n * Used when calling client-provided custom callbacks.\n */\nexport type Logs = {\n  [key: string]: number;\n};\n"], "mappings": "AAAA;;;;;;;;;AAUA,SAAQA,OAAO,QAAe,uBAAuB;AAWrD;;;;;AAKA,OAAO,eAAeC,oBAAoBA,CAACC,IAAoB;EAC7D,IAAIA,IAAI,IAAI,IAAI,EAAE;IAChB;;EAEF,MAAMC,QAAQ,GAAuD,EAAE;EACvE,MAAMC,IAAI,GAAa,EAAE;EACzB,MAAMC,gBAAgB,GAAa,EAAE;EACrC,KAAK,MAAMC,GAAG,IAAIJ,IAAI,EAAE;IACtB,MAAMK,KAAK,GAAGL,IAAI,CAACI,GAAG,CAAC;IACvB,IAAI,OAAOC,KAAK,KAAK,QAAQ,EAAE;MAC7B,MAAMC,WAAW,GAAGD,KAAK;MACzBJ,QAAQ,CAACM,IAAI,CAACD,WAAW,CAACE,IAAI,EAAE,CAAC;MACjCN,IAAI,CAACK,IAAI,CAACH,GAAG,CAAC;MACdD,gBAAgB,CAACI,IAAI,CAACD,WAAW,CAAC;;;EAGtC,IAAIL,QAAQ,CAACQ,MAAM,GAAG,CAAC,EAAE;IACvB,MAAMC,MAAM,GAAG,MAAMC,OAAO,CAACC,GAAG,CAACX,QAAQ,CAAC;IAC1C,KAAK,IAAIY,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,MAAM,CAACD,MAAM,EAAE,EAAEI,CAAC,EAAE;MACtCb,IAAI,CAACE,IAAI,CAACW,CAAC,CAAC,CAAC,GAAGH,MAAM,CAACG,CAAC,CAAC,CAAC,CAAC,CAAC;;IAE9B;IACAf,OAAO,CAACK,gBAAgB,CAAC;;AAE7B;AAEA;;;;;;AAMA,OAAM,SAAUW,oBAAoBA,CAACd,IAAoB;EACvD,IAAIA,IAAI,IAAI,IAAI,EAAE;IAChB;;EAEF,KAAK,MAAMI,GAAG,IAAIJ,IAAI,EAAE;IACtB,MAAMK,KAAK,GAAGL,IAAI,CAACI,GAAG,CAAC;IACvB,IAAI,OAAOC,KAAK,KAAK,QAAQ,EAAE;MAC7BA,KAAK,CAACP,OAAO,EAAE;;;AAGrB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}