{"ast": null, "code": "/**\n * @license\n * Copyright 2019 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nexport class ResizeBilinearPackedProgram {\n  constructor(inputShape, newHeight, newWidth, alignCorners, halfPixelCenters) {\n    this.variableNames = ['A'];\n    this.packedInputs = true;\n    this.packedOutput = true;\n    this.outputShape = [];\n    const [batch, oldHeight, oldWidth, depth] = inputShape;\n    this.outputShape = [batch, newHeight, newWidth, depth];\n    const effectiveInSize = [alignCorners && newHeight > 1 ? oldHeight - 1 : oldHeight, alignCorners && newWidth > 1 ? oldWidth - 1 : oldWidth];\n    const effectiveOutSize = [alignCorners && newHeight > 1 ? newHeight - 1 : newHeight, alignCorners && newWidth > 1 ? newWidth - 1 : newWidth];\n    let sourceFracIndexRC;\n    if (halfPixelCenters) {\n      sourceFracIndexRC = \"(vec3(yRC) + vec3(0.5)) * \" + \"effectiveInputOverOutputRatioRC - vec3(0.5)\";\n    } else {\n      sourceFracIndexRC = \"vec3(yRC) * effectiveInputOverOutputRatioRC\";\n    }\n    this.userCode = \"\\n      const vec3 effectiveInputOverOutputRatioRC = vec3(\\n          \".concat(effectiveInSize[0] / effectiveOutSize[0], \",\\n          \").concat(effectiveInSize[1] / effectiveOutSize[1], \",\\n          \").concat(effectiveInSize[1] / effectiveOutSize[1], \");\\n      const vec3 inputShapeRC = vec3(\").concat(oldHeight, \".0, \").concat(oldWidth, \".0,\\n                                     \").concat(oldWidth, \".0);\\n\\n      float getAValue(int b, int r, int c, int d) {\\n        return getChannel(getA(b, r, c, d), vec2(c, d));\\n      }\\n\\n      void main() {\\n        ivec4 coords = getOutputCoords();\\n        int b = coords[0];\\n        int d = coords[3];\\n        // Calculate values for next column in yRC.z.\\n        ivec3 yRC = coords.yzz + ivec3(0, 0, 1);\\n\\n        // Fractional source index.\\n        vec3 sourceFracIndexRC = \").concat(sourceFracIndexRC, \";\\n\\n        // Compute the four integer indices.\\n        ivec3 sourceFloorRC = ivec3(max(sourceFracIndexRC, vec3(0.0)));\\n        ivec3 sourceCeilRC = ivec3(\\n          min(inputShapeRC - 1.0, ceil(sourceFracIndexRC)));\\n\\n        // Should we calculate next column and row elements in 2x2 packed cell.\\n        bool hasNextCol = d < \").concat(depth - 1, \";\\n        bool hasNextRow = coords.z < \").concat(newWidth - 1, \";\\n\\n        // In parallel, construct four corners for all four components in\\n        // packed 2x2 cell.\\n        vec4 topLeft = vec4(\\n          getAValue(b, sourceFloorRC.x, sourceFloorRC.y, d),\\n          hasNextCol ? getAValue(b, sourceFloorRC.x, sourceFloorRC.y, d + 1)\\n                     : 0.0,\\n          hasNextRow ? getAValue(b, sourceFloorRC.x, sourceFloorRC.z, d)\\n                     : 0.0,\\n          (hasNextRow && hasNextCol) ?\\n            getAValue(b, sourceFloorRC.x, sourceFloorRC.z, d + 1) : 0.0);\\n\\n        vec4 bottomLeft = vec4(\\n          getAValue(b, sourceCeilRC.x, sourceFloorRC.y, d),\\n          hasNextCol ? getAValue(b, sourceCeilRC.x, sourceFloorRC.y, d + 1)\\n                     : 0.0,\\n          hasNextRow ? getAValue(b, sourceCeilRC.x, sourceFloorRC.z, d)\\n                     : 0.0,\\n          (hasNextRow && hasNextCol) ?\\n            getAValue(b, sourceCeilRC.x, sourceFloorRC.z, d + 1) : 0.0);\\n\\n        vec4 topRight = vec4(\\n          getAValue(b, sourceFloorRC.x, sourceCeilRC.y, d),\\n          hasNextCol ? getAValue(b, sourceFloorRC.x, sourceCeilRC.y, d + 1)\\n                     : 0.0,\\n          hasNextRow ? getAValue(b, sourceFloorRC.x, sourceCeilRC.z, d)\\n                     : 0.0,\\n          (hasNextRow && hasNextCol) ?\\n            getAValue(b, sourceFloorRC.x, sourceCeilRC.z, d + 1) : 0.0);\\n\\n        vec4 bottomRight = vec4(\\n          getAValue(b, sourceCeilRC.x, sourceCeilRC.y, d),\\n          hasNextCol ? getAValue(b, sourceCeilRC.x, sourceCeilRC.y, d + 1)\\n                     : 0.0,\\n          hasNextRow ? getAValue(b, sourceCeilRC.x, sourceCeilRC.z, d)\\n                     : 0.0,\\n          (hasNextRow && hasNextCol) ?\\n            getAValue(b, sourceCeilRC.x, sourceCeilRC.z, d + 1) : 0.0);\\n\\n        vec3 fracRC = sourceFracIndexRC - vec3(sourceFloorRC);\\n\\n        vec4 top = mix(topLeft, topRight, fracRC.yyzz);\\n        vec4 bottom = mix(bottomLeft, bottomRight, fracRC.yyzz);\\n        vec4 newValue = mix(top, bottom, fracRC.x);\\n\\n        setOutput(newValue);\\n      }\\n    \");\n  }\n}", "map": {"version": 3, "names": ["ResizeBilinearPackedProgram", "constructor", "inputShape", "newHeight", "newWidth", "alignCorners", "halfPixelCenters", "variableNames", "packedInputs", "packedOutput", "outputShape", "batch", "oldHeight", "oldWidth", "depth", "effectiveInSize", "effectiveOutSize", "sourceFracIndexRC", "userCode", "concat"], "sources": ["C:\\tfjs-backend-webgl\\src\\resize_bilinear_packed_gpu.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2019 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {GPGPUProgram} from './gpgpu_math';\n\nexport class ResizeBilinearPackedProgram implements GPGPUProgram {\n  variableNames = ['A'];\n  packedInputs = true;\n  packedOutput = true;\n  outputShape: number[] = [];\n  userCode: string;\n\n  constructor(\n      inputShape: [number, number, number, number], newHeight: number,\n      newWidth: number, alignCorners: boolean, halfPixelCenters: boolean) {\n    const [batch, oldHeight, oldWidth, depth] = inputShape;\n    this.outputShape = [batch, newHeight, newWidth, depth];\n\n    const effectiveInSize: [number, number] = [\n      (alignCorners && newHeight > 1) ? oldHeight - 1 : oldHeight,\n      (alignCorners && newWidth > 1) ? oldWidth - 1 : oldWidth\n    ];\n\n    const effectiveOutSize: [number, number] = [\n      (alignCorners && newHeight > 1) ? newHeight - 1 : newHeight,\n      (alignCorners && newWidth > 1) ? newWidth - 1 : newWidth\n    ];\n\n    let sourceFracIndexRC: string;\n    if (halfPixelCenters) {\n      sourceFracIndexRC = `(vec3(yRC) + vec3(0.5)) * ` +\n          `effectiveInputOverOutputRatioRC - vec3(0.5)`;\n    } else {\n      sourceFracIndexRC = `vec3(yRC) * effectiveInputOverOutputRatioRC`;\n    }\n\n    this.userCode = `\n      const vec3 effectiveInputOverOutputRatioRC = vec3(\n          ${effectiveInSize[0] / effectiveOutSize[0]},\n          ${effectiveInSize[1] / effectiveOutSize[1]},\n          ${effectiveInSize[1] / effectiveOutSize[1]});\n      const vec3 inputShapeRC = vec3(${oldHeight}.0, ${oldWidth}.0,\n                                     ${oldWidth}.0);\n\n      float getAValue(int b, int r, int c, int d) {\n        return getChannel(getA(b, r, c, d), vec2(c, d));\n      }\n\n      void main() {\n        ivec4 coords = getOutputCoords();\n        int b = coords[0];\n        int d = coords[3];\n        // Calculate values for next column in yRC.z.\n        ivec3 yRC = coords.yzz + ivec3(0, 0, 1);\n\n        // Fractional source index.\n        vec3 sourceFracIndexRC = ${sourceFracIndexRC};\n\n        // Compute the four integer indices.\n        ivec3 sourceFloorRC = ivec3(max(sourceFracIndexRC, vec3(0.0)));\n        ivec3 sourceCeilRC = ivec3(\n          min(inputShapeRC - 1.0, ceil(sourceFracIndexRC)));\n\n        // Should we calculate next column and row elements in 2x2 packed cell.\n        bool hasNextCol = d < ${depth - 1};\n        bool hasNextRow = coords.z < ${newWidth - 1};\n\n        // In parallel, construct four corners for all four components in\n        // packed 2x2 cell.\n        vec4 topLeft = vec4(\n          getAValue(b, sourceFloorRC.x, sourceFloorRC.y, d),\n          hasNextCol ? getAValue(b, sourceFloorRC.x, sourceFloorRC.y, d + 1)\n                     : 0.0,\n          hasNextRow ? getAValue(b, sourceFloorRC.x, sourceFloorRC.z, d)\n                     : 0.0,\n          (hasNextRow && hasNextCol) ?\n            getAValue(b, sourceFloorRC.x, sourceFloorRC.z, d + 1) : 0.0);\n\n        vec4 bottomLeft = vec4(\n          getAValue(b, sourceCeilRC.x, sourceFloorRC.y, d),\n          hasNextCol ? getAValue(b, sourceCeilRC.x, sourceFloorRC.y, d + 1)\n                     : 0.0,\n          hasNextRow ? getAValue(b, sourceCeilRC.x, sourceFloorRC.z, d)\n                     : 0.0,\n          (hasNextRow && hasNextCol) ?\n            getAValue(b, sourceCeilRC.x, sourceFloorRC.z, d + 1) : 0.0);\n\n        vec4 topRight = vec4(\n          getAValue(b, sourceFloorRC.x, sourceCeilRC.y, d),\n          hasNextCol ? getAValue(b, sourceFloorRC.x, sourceCeilRC.y, d + 1)\n                     : 0.0,\n          hasNextRow ? getAValue(b, sourceFloorRC.x, sourceCeilRC.z, d)\n                     : 0.0,\n          (hasNextRow && hasNextCol) ?\n            getAValue(b, sourceFloorRC.x, sourceCeilRC.z, d + 1) : 0.0);\n\n        vec4 bottomRight = vec4(\n          getAValue(b, sourceCeilRC.x, sourceCeilRC.y, d),\n          hasNextCol ? getAValue(b, sourceCeilRC.x, sourceCeilRC.y, d + 1)\n                     : 0.0,\n          hasNextRow ? getAValue(b, sourceCeilRC.x, sourceCeilRC.z, d)\n                     : 0.0,\n          (hasNextRow && hasNextCol) ?\n            getAValue(b, sourceCeilRC.x, sourceCeilRC.z, d + 1) : 0.0);\n\n        vec3 fracRC = sourceFracIndexRC - vec3(sourceFloorRC);\n\n        vec4 top = mix(topLeft, topRight, fracRC.yyzz);\n        vec4 bottom = mix(bottomLeft, bottomRight, fracRC.yyzz);\n        vec4 newValue = mix(top, bottom, fracRC.x);\n\n        setOutput(newValue);\n      }\n    `;\n  }\n}\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAmBA,OAAM,MAAOA,2BAA2B;EAOtCC,YACIC,UAA4C,EAAEC,SAAiB,EAC/DC,QAAgB,EAAEC,YAAqB,EAAEC,gBAAyB;IARtE,KAAAC,aAAa,GAAG,CAAC,GAAG,CAAC;IACrB,KAAAC,YAAY,GAAG,IAAI;IACnB,KAAAC,YAAY,GAAG,IAAI;IACnB,KAAAC,WAAW,GAAa,EAAE;IAMxB,MAAM,CAACC,KAAK,EAAEC,SAAS,EAAEC,QAAQ,EAAEC,KAAK,CAAC,GAAGZ,UAAU;IACtD,IAAI,CAACQ,WAAW,GAAG,CAACC,KAAK,EAAER,SAAS,EAAEC,QAAQ,EAAEU,KAAK,CAAC;IAEtD,MAAMC,eAAe,GAAqB,CACvCV,YAAY,IAAIF,SAAS,GAAG,CAAC,GAAIS,SAAS,GAAG,CAAC,GAAGA,SAAS,EAC1DP,YAAY,IAAID,QAAQ,GAAG,CAAC,GAAIS,QAAQ,GAAG,CAAC,GAAGA,QAAQ,CACzD;IAED,MAAMG,gBAAgB,GAAqB,CACxCX,YAAY,IAAIF,SAAS,GAAG,CAAC,GAAIA,SAAS,GAAG,CAAC,GAAGA,SAAS,EAC1DE,YAAY,IAAID,QAAQ,GAAG,CAAC,GAAIA,QAAQ,GAAG,CAAC,GAAGA,QAAQ,CACzD;IAED,IAAIa,iBAAyB;IAC7B,IAAIX,gBAAgB,EAAE;MACpBW,iBAAiB,GAAG,4EAC6B;KAClD,MAAM;MACLA,iBAAiB,gDAAgD;;IAGnE,IAAI,CAACC,QAAQ,4EAAAC,MAAA,CAELJ,eAAe,CAAC,CAAC,CAAC,GAAGC,gBAAgB,CAAC,CAAC,CAAC,mBAAAG,MAAA,CACxCJ,eAAe,CAAC,CAAC,CAAC,GAAGC,gBAAgB,CAAC,CAAC,CAAC,mBAAAG,MAAA,CACxCJ,eAAe,CAAC,CAAC,CAAC,GAAGC,gBAAgB,CAAC,CAAC,CAAC,+CAAAG,MAAA,CACbP,SAAS,UAAAO,MAAA,CAAON,QAAQ,gDAAAM,MAAA,CACxBN,QAAQ,ibAAAM,MAAA,CAcZF,iBAAiB,sVAAAE,MAAA,CAQpBL,KAAK,GAAG,CAAC,8CAAAK,MAAA,CACFf,QAAQ,GAAG,CAAC,qhEAgD9C;EACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}