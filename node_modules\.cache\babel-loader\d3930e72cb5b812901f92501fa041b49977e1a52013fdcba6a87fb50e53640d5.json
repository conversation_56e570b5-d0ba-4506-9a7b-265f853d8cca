{"ast": null, "code": "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport * as util from '../util';\n/**\n *\n * @param inputShape Input tensor shape is of the following dimensions:\n *     `[batch, height, width, inChannels]`.\n * @param filterShape The filter shape is of the following dimensions:\n *     `[filterHeight, filterWidth, depth]`.\n * @param strides The strides of the sliding window for each dimension of the\n *     input tensor: `[strideHeight, strideWidth]`.\n *     If `strides` is a single number,\n *     then `strideHeight == strideWidth`.\n * @param pad The type of padding algorithm.\n *    - `same` and stride 1: output will be of same size as input,\n *       regardless of filter size.\n *    - `valid`: output will be smaller than input if filter is larger\n *       than 1*1x1.\n *    - For more info, see this guide:\n *     [https://www.tensorflow.org/api_docs/python/tf/nn/convolution](\n *          https://www.tensorflow.org/api_docs/python/tf/nn/convolution)\n * @param dataFormat The data format of the input and output data.\n *     Defaults to 'NHWC'.\n * @param dilations The dilation rates: `[dilationHeight, dilationWidth]`.\n *     Defaults to `[1, 1]`. If `dilations` is a single number, then\n *     `dilationHeight == dilationWidth`.\n */\nexport function computeDilation2DInfo(inputShape, filterShape, strides, pad) {\n  let dataFormat = arguments.length > 4 && arguments[4] !== undefined ? arguments[4] : 'NHWC';\n  let dilations = arguments.length > 5 ? arguments[5] : undefined;\n  // `computerConv2DInfo` require filterShape to be in the dimension of:\n  // `[filterHeight, filterWidth, depth, outDepth]`, dilation2d doesn't have\n  // outDepth, it should have the same depth as the input.\n  // Input shape: [batch, height, width, inChannels]\n  const inputChannels = inputShape[3];\n  const $filterShape = [...filterShape, inputChannels];\n  const $dataFormat = convertConv2DDataFormat(dataFormat);\n  return computeConv2DInfo(inputShape, $filterShape, strides, dilations, pad, null /* roundingMode */, null /* depthWise */, $dataFormat);\n}\nexport function computePool2DInfo(inShape, filterSize, strides, dilations, pad, roundingMode) {\n  let dataFormat = arguments.length > 6 && arguments[6] !== undefined ? arguments[6] : 'channelsLast';\n  const [filterHeight, filterWidth] = parseTupleParam(filterSize);\n  let filterShape;\n  if (dataFormat === 'channelsLast') {\n    filterShape = [filterHeight, filterWidth, inShape[3], inShape[3]];\n  } else if (dataFormat === 'channelsFirst') {\n    filterShape = [filterHeight, filterWidth, inShape[1], inShape[1]];\n  } else {\n    throw new Error(\"Unknown dataFormat \".concat(dataFormat));\n  }\n  return computeConv2DInfo(inShape, filterShape, strides, dilations, pad, roundingMode, false, dataFormat);\n}\n/**\n * Computes the information for a forward pass of a pooling3D operation.\n */\nexport function computePool3DInfo(inShape, filterSize, strides, dilations, pad, roundingMode) {\n  let dataFormat = arguments.length > 6 && arguments[6] !== undefined ? arguments[6] : 'NDHWC';\n  const [filterDepth, filterHeight, filterWidth] = parse3TupleParam(filterSize);\n  let filterShape;\n  let $dataFormat;\n  if (dataFormat === 'NDHWC') {\n    $dataFormat = 'channelsLast';\n    filterShape = [filterDepth, filterHeight, filterWidth, inShape[4], inShape[4]];\n  } else if (dataFormat === 'NCDHW') {\n    $dataFormat = 'channelsFirst';\n    filterShape = [filterDepth, filterHeight, filterWidth, inShape[1], inShape[1]];\n  } else {\n    throw new Error(\"Unknown dataFormat \".concat(dataFormat));\n  }\n  return computeConv3DInfo(inShape, filterShape, strides, dilations, pad, false, $dataFormat, roundingMode);\n}\n/**\n * Computes the information for a forward pass of a convolution/pooling\n * operation.\n */\nexport function computeConv2DInfo(inShape, filterShape, strides, dilations, pad, roundingMode) {\n  let depthwise = arguments.length > 6 && arguments[6] !== undefined ? arguments[6] : false;\n  let dataFormat = arguments.length > 7 && arguments[7] !== undefined ? arguments[7] : 'channelsLast';\n  let [batchSize, inHeight, inWidth, inChannels] = [-1, -1, -1, -1];\n  if (dataFormat === 'channelsLast') {\n    [batchSize, inHeight, inWidth, inChannels] = inShape;\n  } else if (dataFormat === 'channelsFirst') {\n    [batchSize, inChannels, inHeight, inWidth] = inShape;\n  } else {\n    throw new Error(\"Unknown dataFormat \".concat(dataFormat));\n  }\n  const [filterHeight, filterWidth,, filterChannels] = filterShape;\n  const [strideHeight, strideWidth] = parseTupleParam(strides);\n  const [dilationHeight, dilationWidth] = parseTupleParam(dilations);\n  const effectiveFilterHeight = getEffectiveFilterSize(filterHeight, dilationHeight);\n  const effectiveFilterWidth = getEffectiveFilterSize(filterWidth, dilationWidth);\n  const {\n    padInfo,\n    outHeight,\n    outWidth\n  } = getPadAndOutInfo(pad, inHeight, inWidth, strideHeight, strideWidth, effectiveFilterHeight, effectiveFilterWidth, roundingMode, dataFormat);\n  const outChannels = depthwise ? filterChannels * inChannels : filterChannels;\n  let outShape;\n  if (dataFormat === 'channelsFirst') {\n    outShape = [batchSize, outChannels, outHeight, outWidth];\n  } else if (dataFormat === 'channelsLast') {\n    outShape = [batchSize, outHeight, outWidth, outChannels];\n  }\n  return {\n    batchSize,\n    dataFormat,\n    inHeight,\n    inWidth,\n    inChannels,\n    outHeight,\n    outWidth,\n    outChannels,\n    padInfo,\n    strideHeight,\n    strideWidth,\n    filterHeight,\n    filterWidth,\n    effectiveFilterHeight,\n    effectiveFilterWidth,\n    dilationHeight,\n    dilationWidth,\n    inShape,\n    outShape,\n    filterShape\n  };\n}\n/**\n * Computes the information for a forward pass of a 3D convolution/pooling\n * operation.\n */\nexport function computeConv3DInfo(inShape, filterShape, strides, dilations, pad) {\n  let depthwise = arguments.length > 5 && arguments[5] !== undefined ? arguments[5] : false;\n  let dataFormat = arguments.length > 6 && arguments[6] !== undefined ? arguments[6] : 'channelsLast';\n  let roundingMode = arguments.length > 7 ? arguments[7] : undefined;\n  let [batchSize, inDepth, inHeight, inWidth, inChannels] = [-1, -1, -1, -1, -1];\n  if (dataFormat === 'channelsLast') {\n    [batchSize, inDepth, inHeight, inWidth, inChannels] = inShape;\n  } else if (dataFormat === 'channelsFirst') {\n    [batchSize, inChannels, inDepth, inHeight, inWidth] = inShape;\n  } else {\n    throw new Error(\"Unknown dataFormat \".concat(dataFormat));\n  }\n  const [filterDepth, filterHeight, filterWidth,, filterChannels] = filterShape;\n  const [strideDepth, strideHeight, strideWidth] = parse3TupleParam(strides);\n  const [dilationDepth, dilationHeight, dilationWidth] = parse3TupleParam(dilations);\n  const effectiveFilterDepth = getEffectiveFilterSize(filterDepth, dilationDepth);\n  const effectiveFilterHeight = getEffectiveFilterSize(filterHeight, dilationHeight);\n  const effectiveFilterWidth = getEffectiveFilterSize(filterWidth, dilationWidth);\n  const {\n    padInfo,\n    outDepth,\n    outHeight,\n    outWidth\n  } = get3DPadAndOutInfo(pad, inDepth, inHeight, inWidth, strideDepth, strideHeight, strideWidth, effectiveFilterDepth, effectiveFilterHeight, effectiveFilterWidth, roundingMode);\n  const outChannels = depthwise ? filterChannels * inChannels : filterChannels;\n  let outShape;\n  if (dataFormat === 'channelsFirst') {\n    outShape = [batchSize, outChannels, outDepth, outHeight, outWidth];\n  } else if (dataFormat === 'channelsLast') {\n    outShape = [batchSize, outDepth, outHeight, outWidth, outChannels];\n  }\n  return {\n    batchSize,\n    dataFormat,\n    inDepth,\n    inHeight,\n    inWidth,\n    inChannels,\n    outDepth,\n    outHeight,\n    outWidth,\n    outChannels,\n    padInfo,\n    strideDepth,\n    strideHeight,\n    strideWidth,\n    filterDepth,\n    filterHeight,\n    filterWidth,\n    effectiveFilterDepth,\n    effectiveFilterHeight,\n    effectiveFilterWidth,\n    dilationDepth,\n    dilationHeight,\n    dilationWidth,\n    inShape,\n    outShape,\n    filterShape\n  };\n}\nfunction computeOutputShape2D(inShape, fieldSize, stride, zeroPad, roundingMode) {\n  if (zeroPad == null) {\n    zeroPad = computeDefaultPad(inShape, fieldSize, stride);\n  }\n  const inputRows = inShape[0];\n  const inputCols = inShape[1];\n  const outputRows = round((inputRows - fieldSize + 2 * zeroPad) / stride + 1, roundingMode);\n  const outputCols = round((inputCols - fieldSize + 2 * zeroPad) / stride + 1, roundingMode);\n  return [outputRows, outputCols];\n}\nfunction computeOutputShape4D(inShape, filterShape, outChannels, strides, zeroPad, roundingMode) {\n  if (zeroPad == null) {\n    zeroPad = computeDefaultPad(inShape, filterShape[0], strides[0]);\n  }\n  const outShape = [0, 0, 0, outChannels];\n  for (let index = 0; index < 3; index++) {\n    if (inShape[index] + 2 * zeroPad >= filterShape[index]) {\n      outShape[index] = round((inShape[index] - filterShape[index] + 2 * zeroPad) / strides[index] + 1, roundingMode);\n    }\n  }\n  return outShape;\n}\nexport function computeDefaultPad(inputShape, fieldSize, stride) {\n  let dilation = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : 1;\n  const effectiveFieldSize = getEffectiveFilterSize(fieldSize, dilation);\n  return Math.floor((inputShape[0] * (stride - 1) - stride + effectiveFieldSize) / 2);\n}\nfunction parseTupleParam(param) {\n  if (typeof param === 'number') {\n    return [param, param, param];\n  }\n  if (param.length === 2) {\n    return [param[0], param[1], 1];\n  }\n  return param;\n}\nfunction parse3TupleParam(param) {\n  return typeof param === 'number' ? [param, param, param] : param;\n}\n/* See https://www.tensorflow.org/api_docs/python/tf/nn/atrous_conv2d\n * Atrous convolution is equivalent to standard convolution with upsampled\n * filters with effective_filter_height =\n * filter_height + (filter_height - 1) * (dilation - 1)\n * and effective_filter_width =\n * filter_width + (filter_width - 1) * (dilation - 1),\n * produced by inserting dilation - 1 zeros along consecutive elements across\n * the filters' spatial dimensions.\n * When there is a dilation, this converts a filter dimension to the\n * effective filter dimension, so it can be used in a standard convolution.\n */\nfunction getEffectiveFilterSize(filterSize, dilation) {\n  if (dilation <= 1) {\n    return filterSize;\n  }\n  return filterSize + (filterSize - 1) * (dilation - 1);\n}\nfunction getPadAndOutInfo(pad, inHeight, inWidth, strideHeight, strideWidth, filterHeight, filterWidth, roundingMode, dataFormat) {\n  let padInfo;\n  let outHeight;\n  let outWidth;\n  if (typeof pad === 'number') {\n    const padType = pad === 0 ? 'VALID' : 'NUMBER';\n    padInfo = {\n      top: pad,\n      bottom: pad,\n      left: pad,\n      right: pad,\n      type: padType\n    };\n    const outShape = computeOutputShape2D([inHeight, inWidth], filterHeight, strideHeight, pad, roundingMode);\n    outHeight = outShape[0];\n    outWidth = outShape[1];\n  } else if (pad === 'same') {\n    outHeight = Math.ceil(inHeight / strideHeight);\n    outWidth = Math.ceil(inWidth / strideWidth);\n    const padAlongHeight = Math.max(0, (outHeight - 1) * strideHeight + filterHeight - inHeight);\n    const padAlongWidth = Math.max(0, (outWidth - 1) * strideWidth + filterWidth - inWidth);\n    const top = Math.floor(padAlongHeight / 2);\n    const bottom = padAlongHeight - top;\n    const left = Math.floor(padAlongWidth / 2);\n    const right = padAlongWidth - left;\n    padInfo = {\n      top,\n      bottom,\n      left,\n      right,\n      type: 'SAME'\n    };\n  } else if (pad === 'valid') {\n    padInfo = {\n      top: 0,\n      bottom: 0,\n      left: 0,\n      right: 0,\n      type: 'VALID'\n    };\n    outHeight = Math.ceil((inHeight - filterHeight + 1) / strideHeight);\n    outWidth = Math.ceil((inWidth - filterWidth + 1) / strideWidth);\n  } else if (typeof pad === 'object') {\n    const top = dataFormat === 'channelsLast' ? pad[1][0] : pad[2][0];\n    const bottom = dataFormat === 'channelsLast' ? pad[1][1] : pad[2][1];\n    const left = dataFormat === 'channelsLast' ? pad[2][0] : pad[3][0];\n    const right = dataFormat === 'channelsLast' ? pad[2][1] : pad[3][1];\n    const padType = top === 0 && bottom === 0 && left === 0 && right === 0 ? 'VALID' : 'EXPLICIT';\n    padInfo = {\n      top,\n      bottom,\n      left,\n      right,\n      type: padType\n    };\n    outHeight = round((inHeight - filterHeight + top + bottom) / strideHeight + 1, roundingMode);\n    outWidth = round((inWidth - filterWidth + left + right) / strideWidth + 1, roundingMode);\n  } else {\n    throw Error(\"Unknown padding parameter: \".concat(pad));\n  }\n  return {\n    padInfo,\n    outHeight,\n    outWidth\n  };\n}\nfunction get3DPadAndOutInfo(pad, inDepth, inHeight, inWidth, strideDepth, strideHeight, strideWidth, filterDepth, filterHeight, filterWidth, roundingMode) {\n  let padInfo;\n  let outDepth;\n  let outHeight;\n  let outWidth;\n  if (pad === 'valid') {\n    pad = 0;\n  }\n  if (typeof pad === 'number') {\n    const padType = pad === 0 ? 'VALID' : 'NUMBER';\n    padInfo = {\n      top: pad,\n      bottom: pad,\n      left: pad,\n      right: pad,\n      front: pad,\n      back: pad,\n      type: padType\n    };\n    const outShape = computeOutputShape4D([inDepth, inHeight, inWidth, 1], [filterDepth, filterHeight, filterWidth], 1, [strideDepth, strideHeight, strideWidth], pad, roundingMode);\n    outDepth = outShape[0];\n    outHeight = outShape[1];\n    outWidth = outShape[2];\n  } else if (pad === 'same') {\n    outDepth = Math.ceil(inDepth / strideDepth);\n    outHeight = Math.ceil(inHeight / strideHeight);\n    outWidth = Math.ceil(inWidth / strideWidth);\n    const padAlongDepth = (outDepth - 1) * strideDepth + filterDepth - inDepth;\n    const padAlongHeight = (outHeight - 1) * strideHeight + filterHeight - inHeight;\n    const padAlongWidth = (outWidth - 1) * strideWidth + filterWidth - inWidth;\n    const front = Math.floor(padAlongDepth / 2);\n    const back = padAlongDepth - front;\n    const top = Math.floor(padAlongHeight / 2);\n    const bottom = padAlongHeight - top;\n    const left = Math.floor(padAlongWidth / 2);\n    const right = padAlongWidth - left;\n    padInfo = {\n      top,\n      bottom,\n      left,\n      right,\n      front,\n      back,\n      type: 'SAME'\n    };\n  } else {\n    throw Error(\"Unknown padding parameter: \".concat(pad));\n  }\n  return {\n    padInfo,\n    outDepth,\n    outHeight,\n    outWidth\n  };\n}\n/**\n * Rounds a value depending on the rounding mode\n * @param value\n * @param roundingMode A string from: 'ceil', 'round', 'floor'. If none is\n *     provided, it will default to truncate.\n */\nfunction round(value, roundingMode) {\n  if (!roundingMode) {\n    return Math.trunc(value);\n  }\n  switch (roundingMode) {\n    case 'round':\n      // used for Caffe Conv\n      return Math.round(value);\n    case 'ceil':\n      // used for Caffe Pool\n      return Math.ceil(value);\n    case 'floor':\n      return Math.floor(value);\n    default:\n      throw new Error(\"Unknown roundingMode \".concat(roundingMode));\n  }\n}\nexport function tupleValuesAreOne(param) {\n  const [dimA, dimB, dimC] = parseTupleParam(param);\n  return dimA === 1 && dimB === 1 && dimC === 1;\n}\nexport function eitherStridesOrDilationsAreOne(strides, dilations) {\n  return tupleValuesAreOne(strides) || tupleValuesAreOne(dilations);\n}\nexport function stridesOrDilationsArePositive(values) {\n  return parseTupleParam(values).every(value => value > 0);\n}\n/**\n * Convert Conv2D dataFormat from 'NHWC'|'NCHW' to\n *    'channelsLast'|'channelsFirst'\n * @param dataFormat in 'NHWC'|'NCHW' mode\n * @return dataFormat in 'channelsLast'|'channelsFirst' mode\n * @throws unknown dataFormat\n */\nexport function convertConv2DDataFormat(dataFormat) {\n  if (dataFormat === 'NHWC') {\n    return 'channelsLast';\n  } else if (dataFormat === 'NCHW') {\n    return 'channelsFirst';\n  } else {\n    throw new Error(\"Unknown dataFormat \".concat(dataFormat));\n  }\n}\n/**\n * Check validity of pad when using dimRoundingMode.\n * @param opDesc A string of op description\n * @param pad The type of padding algorithm.\n *   - `same` and stride 1: output will be of same size as input,\n *       regardless of filter size.\n *   - `valid` output will be smaller than input if filter is larger\n *       than 1x1.\n *   - For more info, see this guide:\n *     [https://www.tensorflow.org/api_docs/python/tf/nn/convolution](\n *          https://www.tensorflow.org/api_docs/python/tf/nn/convolution)\n * @param dimRoundingMode A string from: 'ceil', 'round', 'floor'. If none is\n *     provided, it will default to truncate.\n * @throws unknown padding parameter\n */\nexport function checkPadOnDimRoundingMode(opDesc, pad, dimRoundingMode) {\n  if (dimRoundingMode != null) {\n    if (typeof pad === 'string') {\n      throw Error(\"Error in \".concat(opDesc, \": pad must be an integer when using \") + \"dimRoundingMode \".concat(dimRoundingMode, \" but got pad \").concat(pad, \".\"));\n    } else if (typeof pad === 'number') {\n      util.assert(util.isInt(pad), () => \"Error in \".concat(opDesc, \": pad must be an integer when using \") + \"dimRoundingMode \".concat(dimRoundingMode, \" but got pad \").concat(pad, \".\"));\n    } else if (typeof pad === 'object') {\n      pad.forEach(p => {\n        p.forEach(v => {\n          util.assert(util.isInt(v), () => \"Error in \".concat(opDesc, \": pad must be an integer when using \") + \"dimRoundingMode \".concat(dimRoundingMode, \" but got pad \").concat(v, \".\"));\n        });\n      });\n    } else {\n      throw Error(\"Error in \".concat(opDesc, \": Unknown padding parameter: \").concat(pad));\n    }\n  }\n}", "map": {"version": 3, "names": ["util", "computeDilation2DInfo", "inputShape", "filterShape", "strides", "pad", "dataFormat", "arguments", "length", "undefined", "dilations", "inputChannels", "$filterShape", "$dataFormat", "convertConv2DDataFormat", "computeConv2DInfo", "computePool2DInfo", "inShape", "filterSize", "roundingMode", "filterHeight", "filterWidth", "parseTupleParam", "Error", "concat", "computePool3DInfo", "<PERSON><PERSON><PERSON><PERSON>", "parse3TupleParam", "computeConv3DInfo", "depthwise", "batchSize", "inHeight", "inWidth", "inChannels", "filterChannels", "strideHeight", "<PERSON><PERSON><PERSON><PERSON>", "dilationHeight", "dilationWidth", "effectiveFilterHeight", "getEffectiveFilterSize", "effectiveFilterWidth", "padInfo", "outHeight", "outWidth", "getPadAndOutInfo", "outChannels", "outShape", "inDepth", "<PERSON><PERSON><PERSON>h", "dilationDepth", "effectiveFilterDepth", "outDepth", "get3DPadAndOutInfo", "computeOutputShape2D", "fieldSize", "stride", "zeroPad", "computeDefaultPad", "inputRows", "inputCols", "outputRows", "round", "outputCols", "computeOutputShape4D", "index", "dilation", "effectiveFieldSize", "Math", "floor", "param", "padType", "top", "bottom", "left", "right", "type", "ceil", "padAlongHeight", "max", "pad<PERSON><PERSON><PERSON><PERSON>", "front", "back", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value", "trunc", "tupleValuesAreOne", "dimA", "dimB", "dimC", "eitherStridesOrDilationsAreOne", "stridesOrDilationsArePositive", "values", "every", "checkPadOnDimRoundingMode", "opDesc", "dimRoundingMode", "assert", "isInt", "for<PERSON>ach", "p", "v"], "sources": ["C:\\tfjs-core\\src\\ops\\conv_util.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport * as util from '../util';\n\ntype PadType = 'SAME'|'VALID'|'NUMBER'|'EXPLICIT';\n\n// For NHWC should be in the following form:\n//  [[0, 0], [pad_top,pad_bottom], [pad_left, pad_right], [0, 0]]\n// For NCHW should be in the following form:\n//  [[0, 0], [0, 0], [pad_top,pad_bottom], [pad_left, pad_right]]\n// Reference: https://www.tensorflow.org/api_docs/python/tf/nn/conv2d\nexport type ExplicitPadding =\n    [[number, number], [number, number], [number, number], [number, number]];\n\nexport type PadInfo = {\n  top: number,\n  left: number,\n  right: number,\n  bottom: number,\n  type: PadType\n};\n\nexport type PadInfo3D = {\n  top: number,\n  left: number,\n  right: number,\n  bottom: number,\n  front: number,\n  back: number,\n  type: PadType\n};\n\n/**\n * Information about the forward pass of a convolution/pooling operation.\n * It includes input and output shape, strides, filter size and padding\n * information.\n */\nexport type Conv2DInfo = {\n  batchSize: number,\n  inHeight: number,\n  inWidth: number,\n  inChannels: number,\n  outHeight: number,\n  outWidth: number,\n  outChannels: number,\n  dataFormat: 'channelsFirst'|'channelsLast',\n  strideHeight: number,\n  strideWidth: number,\n  dilationHeight: number,\n  dilationWidth: number,\n  filterHeight: number,\n  filterWidth: number,\n  effectiveFilterHeight: number,\n  effectiveFilterWidth: number,\n  padInfo: PadInfo,\n  inShape: [number, number, number, number],\n  outShape: [number, number, number, number],\n  filterShape: [number, number, number, number]\n};\n\n/**\n *\n * @param inputShape Input tensor shape is of the following dimensions:\n *     `[batch, height, width, inChannels]`.\n * @param filterShape The filter shape is of the following dimensions:\n *     `[filterHeight, filterWidth, depth]`.\n * @param strides The strides of the sliding window for each dimension of the\n *     input tensor: `[strideHeight, strideWidth]`.\n *     If `strides` is a single number,\n *     then `strideHeight == strideWidth`.\n * @param pad The type of padding algorithm.\n *    - `same` and stride 1: output will be of same size as input,\n *       regardless of filter size.\n *    - `valid`: output will be smaller than input if filter is larger\n *       than 1*1x1.\n *    - For more info, see this guide:\n *     [https://www.tensorflow.org/api_docs/python/tf/nn/convolution](\n *          https://www.tensorflow.org/api_docs/python/tf/nn/convolution)\n * @param dataFormat The data format of the input and output data.\n *     Defaults to 'NHWC'.\n * @param dilations The dilation rates: `[dilationHeight, dilationWidth]`.\n *     Defaults to `[1, 1]`. If `dilations` is a single number, then\n *     `dilationHeight == dilationWidth`.\n */\nexport function computeDilation2DInfo(\n    inputShape: [number, number, number, number],\n    filterShape: [number, number, number], strides: number|[number, number],\n    pad: 'same'|'valid'|number, dataFormat: 'NHWC' = 'NHWC',\n    dilations: number|[number, number]) {\n  // `computerConv2DInfo` require filterShape to be in the dimension of:\n  // `[filterHeight, filterWidth, depth, outDepth]`, dilation2d doesn't have\n  // outDepth, it should have the same depth as the input.\n  // Input shape: [batch, height, width, inChannels]\n  const inputChannels = inputShape[3];\n  const $filterShape =\n      [...filterShape, inputChannels] as [number, number, number, number];\n  const $dataFormat = convertConv2DDataFormat(dataFormat);\n\n  return computeConv2DInfo(\n      inputShape, $filterShape, strides, dilations, pad,\n      null /* roundingMode */, null /* depthWise */, $dataFormat);\n}\n\nexport function computePool2DInfo(\n    inShape: [number, number, number, number],\n    filterSize: [number, number]|number, strides: number|[number, number],\n    dilations: number|[number, number],\n    pad: 'same'|'valid'|number|ExplicitPadding,\n    roundingMode?: 'floor'|'round'|'ceil',\n    dataFormat: 'channelsFirst'|'channelsLast' = 'channelsLast'): Conv2DInfo {\n  const [filterHeight, filterWidth] = parseTupleParam(filterSize);\n\n  let filterShape: [number, number, number, number];\n  if (dataFormat === 'channelsLast') {\n    filterShape = [filterHeight, filterWidth, inShape[3], inShape[3]];\n  } else if (dataFormat === 'channelsFirst') {\n    filterShape = [filterHeight, filterWidth, inShape[1], inShape[1]];\n  } else {\n    throw new Error(`Unknown dataFormat ${dataFormat}`);\n  }\n\n  return computeConv2DInfo(\n      inShape, filterShape, strides, dilations, pad, roundingMode, false,\n      dataFormat);\n}\n\n/**\n * Computes the information for a forward pass of a pooling3D operation.\n */\nexport function computePool3DInfo(\n    inShape: [number, number, number, number, number],\n    filterSize: number|[number, number, number],\n    strides: number|[number, number, number],\n    dilations: number|[number, number, number], pad: 'same'|'valid'|number,\n    roundingMode?: 'floor'|'round'|'ceil',\n    dataFormat: 'NDHWC'|'NCDHW' = 'NDHWC'): Conv3DInfo {\n  const [filterDepth, filterHeight, filterWidth] = parse3TupleParam(filterSize);\n\n  let filterShape: [number, number, number, number, number];\n  let $dataFormat: 'channelsFirst'|'channelsLast';\n  if (dataFormat === 'NDHWC') {\n    $dataFormat = 'channelsLast';\n    filterShape =\n        [filterDepth, filterHeight, filterWidth, inShape[4], inShape[4]];\n  } else if (dataFormat === 'NCDHW') {\n    $dataFormat = 'channelsFirst';\n    filterShape =\n        [filterDepth, filterHeight, filterWidth, inShape[1], inShape[1]];\n  } else {\n    throw new Error(`Unknown dataFormat ${dataFormat}`);\n  }\n\n  return computeConv3DInfo(\n      inShape, filterShape, strides, dilations, pad, false, $dataFormat,\n      roundingMode);\n}\n\n/**\n * Computes the information for a forward pass of a convolution/pooling\n * operation.\n */\nexport function computeConv2DInfo(\n    inShape: [number, number, number, number],\n    filterShape: [number, number, number, number],\n    strides: number|[number, number], dilations: number|[number, number],\n    pad: 'same'|'valid'|number|ExplicitPadding,\n    roundingMode?: 'floor'|'round'|'ceil', depthwise = false,\n    dataFormat: 'channelsFirst'|'channelsLast' = 'channelsLast'): Conv2DInfo {\n  let [batchSize, inHeight, inWidth, inChannels] = [-1, -1, -1, -1];\n  if (dataFormat === 'channelsLast') {\n    [batchSize, inHeight, inWidth, inChannels] = inShape;\n  } else if (dataFormat === 'channelsFirst') {\n    [batchSize, inChannels, inHeight, inWidth] = inShape;\n  } else {\n    throw new Error(`Unknown dataFormat ${dataFormat}`);\n  }\n\n  const [filterHeight, filterWidth, , filterChannels] = filterShape;\n  const [strideHeight, strideWidth] = parseTupleParam(strides);\n  const [dilationHeight, dilationWidth] = parseTupleParam(dilations);\n\n  const effectiveFilterHeight =\n      getEffectiveFilterSize(filterHeight, dilationHeight);\n  const effectiveFilterWidth =\n      getEffectiveFilterSize(filterWidth, dilationWidth);\n  const {padInfo, outHeight, outWidth} = getPadAndOutInfo(\n      pad, inHeight, inWidth, strideHeight, strideWidth, effectiveFilterHeight,\n      effectiveFilterWidth, roundingMode, dataFormat);\n\n  const outChannels = depthwise ? filterChannels * inChannels : filterChannels;\n\n  let outShape: [number, number, number, number];\n  if (dataFormat === 'channelsFirst') {\n    outShape = [batchSize, outChannels, outHeight, outWidth];\n  } else if (dataFormat === 'channelsLast') {\n    outShape = [batchSize, outHeight, outWidth, outChannels];\n  }\n\n  return {\n    batchSize,\n    dataFormat,\n    inHeight,\n    inWidth,\n    inChannels,\n    outHeight,\n    outWidth,\n    outChannels,\n    padInfo,\n    strideHeight,\n    strideWidth,\n    filterHeight,\n    filterWidth,\n    effectiveFilterHeight,\n    effectiveFilterWidth,\n    dilationHeight,\n    dilationWidth,\n    inShape,\n    outShape,\n    filterShape\n  };\n}\n\n/**\n * Information about the forward pass of a 3D convolution/pooling operation.\n * It includes input and output shape, strides, filter size and padding\n * information.\n */\nexport type Conv3DInfo = {\n  batchSize: number,\n  inDepth: number,\n  inHeight: number,\n  inWidth: number,\n  inChannels: number,\n  outDepth: number,\n  outHeight: number,\n  outWidth: number,\n  outChannels: number,\n  dataFormat: 'channelsFirst'|'channelsLast',\n  strideDepth: number,\n  strideHeight: number,\n  strideWidth: number,\n  dilationDepth: number,\n  dilationHeight: number,\n  dilationWidth: number,\n  filterDepth: number,\n  filterHeight: number,\n  filterWidth: number,\n  effectiveFilterDepth: number,\n  effectiveFilterHeight: number,\n  effectiveFilterWidth: number,\n  padInfo: PadInfo3D,\n  inShape: [number, number, number, number, number],\n  outShape: [number, number, number, number, number],\n  filterShape: [number, number, number, number, number]\n};\n\n/**\n * Computes the information for a forward pass of a 3D convolution/pooling\n * operation.\n */\nexport function computeConv3DInfo(\n    inShape: [number, number, number, number, number],\n    filterShape: [number, number, number, number, number],\n    strides: number|[number, number, number],\n    dilations: number|[number, number, number], pad: 'same'|'valid'|number,\n    depthwise = false,\n    dataFormat: 'channelsFirst'|'channelsLast' = 'channelsLast',\n    roundingMode?: 'floor'|'round'|'ceil'): Conv3DInfo {\n  let [batchSize, inDepth, inHeight, inWidth, inChannels] =\n      [-1, -1, -1, -1, -1];\n  if (dataFormat === 'channelsLast') {\n    [batchSize, inDepth, inHeight, inWidth, inChannels] = inShape;\n  } else if (dataFormat === 'channelsFirst') {\n    [batchSize, inChannels, inDepth, inHeight, inWidth] = inShape;\n  } else {\n    throw new Error(`Unknown dataFormat ${dataFormat}`);\n  }\n\n  const [filterDepth, filterHeight, filterWidth, , filterChannels] =\n      filterShape;\n  const [strideDepth, strideHeight, strideWidth] = parse3TupleParam(strides);\n  const [dilationDepth, dilationHeight, dilationWidth] =\n      parse3TupleParam(dilations);\n\n  const effectiveFilterDepth =\n      getEffectiveFilterSize(filterDepth, dilationDepth);\n  const effectiveFilterHeight =\n      getEffectiveFilterSize(filterHeight, dilationHeight);\n  const effectiveFilterWidth =\n      getEffectiveFilterSize(filterWidth, dilationWidth);\n  const {padInfo, outDepth, outHeight, outWidth} = get3DPadAndOutInfo(\n      pad, inDepth, inHeight, inWidth, strideDepth, strideHeight, strideWidth,\n      effectiveFilterDepth, effectiveFilterHeight, effectiveFilterWidth,\n      roundingMode);\n\n  const outChannels = depthwise ? filterChannels * inChannels : filterChannels;\n\n  let outShape: [number, number, number, number, number];\n  if (dataFormat === 'channelsFirst') {\n    outShape = [batchSize, outChannels, outDepth, outHeight, outWidth];\n  } else if (dataFormat === 'channelsLast') {\n    outShape = [batchSize, outDepth, outHeight, outWidth, outChannels];\n  }\n\n  return {\n    batchSize,\n    dataFormat,\n    inDepth,\n    inHeight,\n    inWidth,\n    inChannels,\n    outDepth,\n    outHeight,\n    outWidth,\n    outChannels,\n    padInfo,\n    strideDepth,\n    strideHeight,\n    strideWidth,\n    filterDepth,\n    filterHeight,\n    filterWidth,\n    effectiveFilterDepth,\n    effectiveFilterHeight,\n    effectiveFilterWidth,\n    dilationDepth,\n    dilationHeight,\n    dilationWidth,\n    inShape,\n    outShape,\n    filterShape\n  };\n}\n\nfunction computeOutputShape2D(\n    inShape: [number, number], fieldSize: number, stride: number,\n    zeroPad?: number, roundingMode?: 'floor'|'round'|'ceil'): [number, number] {\n  if (zeroPad == null) {\n    zeroPad = computeDefaultPad(inShape, fieldSize, stride);\n  }\n  const inputRows = inShape[0];\n  const inputCols = inShape[1];\n\n  const outputRows =\n      round((inputRows - fieldSize + 2 * zeroPad) / stride + 1, roundingMode);\n  const outputCols =\n      round((inputCols - fieldSize + 2 * zeroPad) / stride + 1, roundingMode);\n\n  return [outputRows, outputCols];\n}\n\nfunction computeOutputShape4D(\n    inShape: [number, number, number, number],\n    filterShape: [number, number, number], outChannels: number,\n    strides: [number, number, number], zeroPad?: number,\n    roundingMode?: 'floor'|'round'|'ceil'): [number, number, number, number] {\n  if (zeroPad == null) {\n    zeroPad = computeDefaultPad(inShape, filterShape[0], strides[0]);\n  }\n  const outShape: [number, number, number, number] = [0, 0, 0, outChannels];\n  for (let index = 0; index < 3; index++) {\n    if (inShape[index] + 2 * zeroPad >= filterShape[index]) {\n      outShape[index] = round(\n          (inShape[index] - filterShape[index] + 2 * zeroPad) / strides[index] +\n              1,\n          roundingMode);\n    }\n  }\n  return outShape;\n}\n\nexport function computeDefaultPad(\n    inputShape: [number, number]|[number, number, number, number],\n    fieldSize: number, stride: number, dilation = 1): number {\n  const effectiveFieldSize = getEffectiveFilterSize(fieldSize, dilation);\n  return Math.floor(\n      (inputShape[0] * (stride - 1) - stride + effectiveFieldSize) / 2);\n}\n\nfunction parseTupleParam(param: number|number[]): [number, number, number] {\n  if (typeof param === 'number') {\n    return [param, param, param];\n  }\n  if (param.length === 2) {\n    return [param[0], param[1], 1];\n  }\n  return param as [number, number, number];\n}\n\nfunction parse3TupleParam(param: number|[number, number, number]):\n    [number, number, number] {\n  return typeof param === 'number' ? [param, param, param] : param;\n}\n\n/* See https://www.tensorflow.org/api_docs/python/tf/nn/atrous_conv2d\n * Atrous convolution is equivalent to standard convolution with upsampled\n * filters with effective_filter_height =\n * filter_height + (filter_height - 1) * (dilation - 1)\n * and effective_filter_width =\n * filter_width + (filter_width - 1) * (dilation - 1),\n * produced by inserting dilation - 1 zeros along consecutive elements across\n * the filters' spatial dimensions.\n * When there is a dilation, this converts a filter dimension to the\n * effective filter dimension, so it can be used in a standard convolution.\n */\nfunction getEffectiveFilterSize(filterSize: number, dilation: number) {\n  if (dilation <= 1) {\n    return filterSize;\n  }\n\n  return filterSize + (filterSize - 1) * (dilation - 1);\n}\n\nfunction getPadAndOutInfo(\n    pad: 'same'|'valid'|number|ExplicitPadding, inHeight: number,\n    inWidth: number, strideHeight: number, strideWidth: number,\n    filterHeight: number, filterWidth: number,\n    roundingMode: 'floor'|'round'|'ceil',\n    dataFormat: 'channelsFirst'|\n    'channelsLast'): {padInfo: PadInfo, outHeight: number, outWidth: number} {\n  let padInfo: PadInfo;\n  let outHeight: number;\n  let outWidth: number;\n\n  if (typeof pad === 'number') {\n    const padType = (pad === 0) ? 'VALID' : 'NUMBER';\n    padInfo = {top: pad, bottom: pad, left: pad, right: pad, type: padType};\n    const outShape = computeOutputShape2D(\n        [inHeight, inWidth], filterHeight, strideHeight, pad, roundingMode);\n    outHeight = outShape[0];\n    outWidth = outShape[1];\n  } else if (pad === 'same') {\n    outHeight = Math.ceil(inHeight / strideHeight);\n    outWidth = Math.ceil(inWidth / strideWidth);\n    const padAlongHeight =\n        Math.max(0, (outHeight - 1) * strideHeight + filterHeight - inHeight);\n    const padAlongWidth =\n        Math.max(0, (outWidth - 1) * strideWidth + filterWidth - inWidth);\n    const top = Math.floor(padAlongHeight / 2);\n    const bottom = padAlongHeight - top;\n    const left = Math.floor(padAlongWidth / 2);\n    const right = padAlongWidth - left;\n    padInfo = {top, bottom, left, right, type: 'SAME'};\n  } else if (pad === 'valid') {\n    padInfo = {top: 0, bottom: 0, left: 0, right: 0, type: 'VALID'};\n    outHeight = Math.ceil((inHeight - filterHeight + 1) / strideHeight);\n    outWidth = Math.ceil((inWidth - filterWidth + 1) / strideWidth);\n  } else if (typeof pad === 'object') {\n    const top = dataFormat === 'channelsLast' ? pad[1][0] : pad[2][0];\n    const bottom = dataFormat === 'channelsLast' ? pad[1][1] : pad[2][1];\n    const left = dataFormat === 'channelsLast' ? pad[2][0] : pad[3][0];\n    const right = dataFormat === 'channelsLast' ? pad[2][1] : pad[3][1];\n    const padType = (top === 0 && bottom === 0 && left === 0 && right === 0) ?\n        'VALID' :\n        'EXPLICIT';\n    padInfo = {top, bottom, left, right, type: padType};\n    outHeight = round(\n        (inHeight - filterHeight + top + bottom) / strideHeight + 1,\n        roundingMode);\n    outWidth = round(\n        (inWidth - filterWidth + left + right) / strideWidth + 1, roundingMode);\n  } else {\n    throw Error(`Unknown padding parameter: ${pad}`);\n  }\n  return {padInfo, outHeight, outWidth};\n}\n\nfunction get3DPadAndOutInfo(\n    pad: 'same'|'valid'|number, inDepth: number, inHeight: number,\n    inWidth: number, strideDepth: number, strideHeight: number,\n    strideWidth: number, filterDepth: number, filterHeight: number,\n    filterWidth: number, roundingMode?: 'floor'|'round'|'ceil'): {\n  padInfo: PadInfo3D,\n  outDepth: number,\n  outHeight: number,\n  outWidth: number\n} {\n  let padInfo: PadInfo3D;\n  let outDepth: number;\n  let outHeight: number;\n  let outWidth: number;\n\n  if (pad === 'valid') {\n    pad = 0;\n  }\n\n  if (typeof pad === 'number') {\n    const padType = (pad === 0) ? 'VALID' : 'NUMBER';\n    padInfo = {\n      top: pad,\n      bottom: pad,\n      left: pad,\n      right: pad,\n      front: pad,\n      back: pad,\n      type: padType\n    };\n    const outShape = computeOutputShape4D(\n        [inDepth, inHeight, inWidth, 1],\n        [filterDepth, filterHeight, filterWidth], 1,\n        [strideDepth, strideHeight, strideWidth], pad, roundingMode);\n    outDepth = outShape[0];\n    outHeight = outShape[1];\n    outWidth = outShape[2];\n  } else if (pad === 'same') {\n    outDepth = Math.ceil(inDepth / strideDepth);\n    outHeight = Math.ceil(inHeight / strideHeight);\n    outWidth = Math.ceil(inWidth / strideWidth);\n    const padAlongDepth = (outDepth - 1) * strideDepth + filterDepth - inDepth;\n    const padAlongHeight =\n        (outHeight - 1) * strideHeight + filterHeight - inHeight;\n    const padAlongWidth = (outWidth - 1) * strideWidth + filterWidth - inWidth;\n    const front = Math.floor(padAlongDepth / 2);\n    const back = padAlongDepth - front;\n    const top = Math.floor(padAlongHeight / 2);\n    const bottom = padAlongHeight - top;\n    const left = Math.floor(padAlongWidth / 2);\n    const right = padAlongWidth - left;\n\n    padInfo = {top, bottom, left, right, front, back, type: 'SAME'};\n  } else {\n    throw Error(`Unknown padding parameter: ${pad}`);\n  }\n  return {padInfo, outDepth, outHeight, outWidth};\n}\n\n/**\n * Rounds a value depending on the rounding mode\n * @param value\n * @param roundingMode A string from: 'ceil', 'round', 'floor'. If none is\n *     provided, it will default to truncate.\n */\nfunction round(value: number, roundingMode?: 'floor'|'round'|'ceil') {\n  if (!roundingMode) {\n    return Math.trunc(value);\n  }\n  switch (roundingMode) {\n    case 'round':\n      // used for Caffe Conv\n      return Math.round(value);\n    case 'ceil':\n      // used for Caffe Pool\n      return Math.ceil(value);\n    case 'floor':\n      return Math.floor(value);\n    default:\n      throw new Error(`Unknown roundingMode ${roundingMode}`);\n  }\n}\n\nexport function tupleValuesAreOne(param: number|number[]): boolean {\n  const [dimA, dimB, dimC] = parseTupleParam(param);\n  return dimA === 1 && dimB === 1 && dimC === 1;\n}\n\nexport function eitherStridesOrDilationsAreOne(\n    strides: number|number[], dilations: number|number[]): boolean {\n  return tupleValuesAreOne(strides) || tupleValuesAreOne(dilations);\n}\n\nexport function stridesOrDilationsArePositive(values: number|\n                                              number[]): boolean {\n  return parseTupleParam(values).every(value => value > 0);\n}\n\n/**\n * Convert Conv2D dataFormat from 'NHWC'|'NCHW' to\n *    'channelsLast'|'channelsFirst'\n * @param dataFormat in 'NHWC'|'NCHW' mode\n * @return dataFormat in 'channelsLast'|'channelsFirst' mode\n * @throws unknown dataFormat\n */\nexport function convertConv2DDataFormat(dataFormat: 'NHWC'|'NCHW'):\n    'channelsLast'|'channelsFirst' {\n  if (dataFormat === 'NHWC') {\n    return 'channelsLast';\n  } else if (dataFormat === 'NCHW') {\n    return 'channelsFirst';\n  } else {\n    throw new Error(`Unknown dataFormat ${dataFormat}`);\n  }\n}\n\n/**\n * Check validity of pad when using dimRoundingMode.\n * @param opDesc A string of op description\n * @param pad The type of padding algorithm.\n *   - `same` and stride 1: output will be of same size as input,\n *       regardless of filter size.\n *   - `valid` output will be smaller than input if filter is larger\n *       than 1x1.\n *   - For more info, see this guide:\n *     [https://www.tensorflow.org/api_docs/python/tf/nn/convolution](\n *          https://www.tensorflow.org/api_docs/python/tf/nn/convolution)\n * @param dimRoundingMode A string from: 'ceil', 'round', 'floor'. If none is\n *     provided, it will default to truncate.\n * @throws unknown padding parameter\n */\nexport function checkPadOnDimRoundingMode(\n    opDesc: string, pad: 'valid'|'same'|number|ExplicitPadding,\n    dimRoundingMode?: 'floor'|'round'|'ceil') {\n  if (dimRoundingMode != null) {\n    if (typeof pad === 'string') {\n      throw Error(\n          `Error in ${opDesc}: pad must be an integer when using ` +\n          `dimRoundingMode ${dimRoundingMode} but got pad ${pad}.`);\n    } else if (typeof pad === 'number') {\n      util.assert(\n          util.isInt(pad),\n          () => `Error in ${opDesc}: pad must be an integer when using ` +\n              `dimRoundingMode ${dimRoundingMode} but got pad ${pad}.`);\n    } else if (typeof pad === 'object') {\n      (pad as ExplicitPadding).forEach(p => {\n        p.forEach(v => {\n          util.assert(\n              util.isInt(v),\n              () => `Error in ${opDesc}: pad must be an integer when using ` +\n                  `dimRoundingMode ${dimRoundingMode} but got pad ${v}.`);\n        });\n      });\n    } else {\n      throw Error(`Error in ${opDesc}: Unknown padding parameter: ${pad}`);\n    }\n  }\n}\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAiBA,OAAO,KAAKA,IAAI,MAAM,SAAS;AA0D/B;;;;;;;;;;;;;;;;;;;;;;;;AAwBA,OAAM,SAAUC,qBAAqBA,CACjCC,UAA4C,EAC5CC,WAAqC,EAAEC,OAAgC,EACvEC,GAA0B,EACQ;EAAA,IADNC,UAAA,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAqB,MAAM;EAAA,IACvDG,SAAkC,GAAAH,SAAA,CAAAC,MAAA,OAAAD,SAAA,MAAAE,SAAA;EACpC;EACA;EACA;EACA;EACA,MAAME,aAAa,GAAGT,UAAU,CAAC,CAAC,CAAC;EACnC,MAAMU,YAAY,GACd,CAAC,GAAGT,WAAW,EAAEQ,aAAa,CAAqC;EACvE,MAAME,WAAW,GAAGC,uBAAuB,CAACR,UAAU,CAAC;EAEvD,OAAOS,iBAAiB,CACpBb,UAAU,EAAEU,YAAY,EAAER,OAAO,EAAEM,SAAS,EAAEL,GAAG,EACjD,IAAI,CAAC,oBAAoB,IAAI,CAAC,iBAAiBQ,WAAW,CAAC;AACjE;AAEA,OAAM,SAAUG,iBAAiBA,CAC7BC,OAAyC,EACzCC,UAAmC,EAAEd,OAAgC,EACrEM,SAAkC,EAClCL,GAA0C,EAC1Cc,YAAqC,EACsB;EAAA,IAA3Db,UAAA,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAA6C,cAAc;EAC7D,MAAM,CAACa,YAAY,EAAEC,WAAW,CAAC,GAAGC,eAAe,CAACJ,UAAU,CAAC;EAE/D,IAAIf,WAA6C;EACjD,IAAIG,UAAU,KAAK,cAAc,EAAE;IACjCH,WAAW,GAAG,CAACiB,YAAY,EAAEC,WAAW,EAAEJ,OAAO,CAAC,CAAC,CAAC,EAAEA,OAAO,CAAC,CAAC,CAAC,CAAC;GAClE,MAAM,IAAIX,UAAU,KAAK,eAAe,EAAE;IACzCH,WAAW,GAAG,CAACiB,YAAY,EAAEC,WAAW,EAAEJ,OAAO,CAAC,CAAC,CAAC,EAAEA,OAAO,CAAC,CAAC,CAAC,CAAC;GAClE,MAAM;IACL,MAAM,IAAIM,KAAK,uBAAAC,MAAA,CAAuBlB,UAAU,CAAE,CAAC;;EAGrD,OAAOS,iBAAiB,CACpBE,OAAO,EAAEd,WAAW,EAAEC,OAAO,EAAEM,SAAS,EAAEL,GAAG,EAAEc,YAAY,EAAE,KAAK,EAClEb,UAAU,CAAC;AACjB;AAEA;;;AAGA,OAAM,SAAUmB,iBAAiBA,CAC7BR,OAAiD,EACjDC,UAA2C,EAC3Cd,OAAwC,EACxCM,SAA0C,EAAEL,GAA0B,EACtEc,YAAqC,EACA;EAAA,IAArCb,UAAA,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAA8B,OAAO;EACvC,MAAM,CAACmB,WAAW,EAAEN,YAAY,EAAEC,WAAW,CAAC,GAAGM,gBAAgB,CAACT,UAAU,CAAC;EAE7E,IAAIf,WAAqD;EACzD,IAAIU,WAA2C;EAC/C,IAAIP,UAAU,KAAK,OAAO,EAAE;IAC1BO,WAAW,GAAG,cAAc;IAC5BV,WAAW,GACP,CAACuB,WAAW,EAAEN,YAAY,EAAEC,WAAW,EAAEJ,OAAO,CAAC,CAAC,CAAC,EAAEA,OAAO,CAAC,CAAC,CAAC,CAAC;GACrE,MAAM,IAAIX,UAAU,KAAK,OAAO,EAAE;IACjCO,WAAW,GAAG,eAAe;IAC7BV,WAAW,GACP,CAACuB,WAAW,EAAEN,YAAY,EAAEC,WAAW,EAAEJ,OAAO,CAAC,CAAC,CAAC,EAAEA,OAAO,CAAC,CAAC,CAAC,CAAC;GACrE,MAAM;IACL,MAAM,IAAIM,KAAK,uBAAAC,MAAA,CAAuBlB,UAAU,CAAE,CAAC;;EAGrD,OAAOsB,iBAAiB,CACpBX,OAAO,EAAEd,WAAW,EAAEC,OAAO,EAAEM,SAAS,EAAEL,GAAG,EAAE,KAAK,EAAEQ,WAAW,EACjEM,YAAY,CAAC;AACnB;AAEA;;;;AAIA,OAAM,SAAUJ,iBAAiBA,CAC7BE,OAAyC,EACzCd,WAA6C,EAC7CC,OAAgC,EAAEM,SAAkC,EACpEL,GAA0C,EAC1Cc,YAAqC,EACsB;EAAA,IADpBU,SAAS,GAAAtB,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,KAAK;EAAA,IACxDD,UAAA,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAA6C,cAAc;EAC7D,IAAI,CAACuB,SAAS,EAAEC,QAAQ,EAAEC,OAAO,EAAEC,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EACjE,IAAI3B,UAAU,KAAK,cAAc,EAAE;IACjC,CAACwB,SAAS,EAAEC,QAAQ,EAAEC,OAAO,EAAEC,UAAU,CAAC,GAAGhB,OAAO;GACrD,MAAM,IAAIX,UAAU,KAAK,eAAe,EAAE;IACzC,CAACwB,SAAS,EAAEG,UAAU,EAAEF,QAAQ,EAAEC,OAAO,CAAC,GAAGf,OAAO;GACrD,MAAM;IACL,MAAM,IAAIM,KAAK,uBAAAC,MAAA,CAAuBlB,UAAU,CAAE,CAAC;;EAGrD,MAAM,CAACc,YAAY,EAAEC,WAAW,GAAIa,cAAc,CAAC,GAAG/B,WAAW;EACjE,MAAM,CAACgC,YAAY,EAAEC,WAAW,CAAC,GAAGd,eAAe,CAAClB,OAAO,CAAC;EAC5D,MAAM,CAACiC,cAAc,EAAEC,aAAa,CAAC,GAAGhB,eAAe,CAACZ,SAAS,CAAC;EAElE,MAAM6B,qBAAqB,GACvBC,sBAAsB,CAACpB,YAAY,EAAEiB,cAAc,CAAC;EACxD,MAAMI,oBAAoB,GACtBD,sBAAsB,CAACnB,WAAW,EAAEiB,aAAa,CAAC;EACtD,MAAM;IAACI,OAAO;IAAEC,SAAS;IAAEC;EAAQ,CAAC,GAAGC,gBAAgB,CACnDxC,GAAG,EAAE0B,QAAQ,EAAEC,OAAO,EAAEG,YAAY,EAAEC,WAAW,EAAEG,qBAAqB,EACxEE,oBAAoB,EAAEtB,YAAY,EAAEb,UAAU,CAAC;EAEnD,MAAMwC,WAAW,GAAGjB,SAAS,GAAGK,cAAc,GAAGD,UAAU,GAAGC,cAAc;EAE5E,IAAIa,QAA0C;EAC9C,IAAIzC,UAAU,KAAK,eAAe,EAAE;IAClCyC,QAAQ,GAAG,CAACjB,SAAS,EAAEgB,WAAW,EAAEH,SAAS,EAAEC,QAAQ,CAAC;GACzD,MAAM,IAAItC,UAAU,KAAK,cAAc,EAAE;IACxCyC,QAAQ,GAAG,CAACjB,SAAS,EAAEa,SAAS,EAAEC,QAAQ,EAAEE,WAAW,CAAC;;EAG1D,OAAO;IACLhB,SAAS;IACTxB,UAAU;IACVyB,QAAQ;IACRC,OAAO;IACPC,UAAU;IACVU,SAAS;IACTC,QAAQ;IACRE,WAAW;IACXJ,OAAO;IACPP,YAAY;IACZC,WAAW;IACXhB,YAAY;IACZC,WAAW;IACXkB,qBAAqB;IACrBE,oBAAoB;IACpBJ,cAAc;IACdC,aAAa;IACbrB,OAAO;IACP8B,QAAQ;IACR5C;GACD;AACH;AAoCA;;;;AAIA,OAAM,SAAUyB,iBAAiBA,CAC7BX,OAAiD,EACjDd,WAAqD,EACrDC,OAAwC,EACxCM,SAA0C,EAAEL,GAA0B,EAGjC;EAAA,IAFrCwB,SAAS,GAAAtB,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,KAAK;EAAA,IACjBD,UAAA,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAA6C,cAAc;EAAA,IAC3DY,YAAqC,GAAAZ,SAAA,CAAAC,MAAA,OAAAD,SAAA,MAAAE,SAAA;EACvC,IAAI,CAACqB,SAAS,EAAEkB,OAAO,EAAEjB,QAAQ,EAAEC,OAAO,EAAEC,UAAU,CAAC,GACnD,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EACxB,IAAI3B,UAAU,KAAK,cAAc,EAAE;IACjC,CAACwB,SAAS,EAAEkB,OAAO,EAAEjB,QAAQ,EAAEC,OAAO,EAAEC,UAAU,CAAC,GAAGhB,OAAO;GAC9D,MAAM,IAAIX,UAAU,KAAK,eAAe,EAAE;IACzC,CAACwB,SAAS,EAAEG,UAAU,EAAEe,OAAO,EAAEjB,QAAQ,EAAEC,OAAO,CAAC,GAAGf,OAAO;GAC9D,MAAM;IACL,MAAM,IAAIM,KAAK,uBAAAC,MAAA,CAAuBlB,UAAU,CAAE,CAAC;;EAGrD,MAAM,CAACoB,WAAW,EAAEN,YAAY,EAAEC,WAAW,GAAIa,cAAc,CAAC,GAC5D/B,WAAW;EACf,MAAM,CAAC8C,WAAW,EAAEd,YAAY,EAAEC,WAAW,CAAC,GAAGT,gBAAgB,CAACvB,OAAO,CAAC;EAC1E,MAAM,CAAC8C,aAAa,EAAEb,cAAc,EAAEC,aAAa,CAAC,GAChDX,gBAAgB,CAACjB,SAAS,CAAC;EAE/B,MAAMyC,oBAAoB,GACtBX,sBAAsB,CAACd,WAAW,EAAEwB,aAAa,CAAC;EACtD,MAAMX,qBAAqB,GACvBC,sBAAsB,CAACpB,YAAY,EAAEiB,cAAc,CAAC;EACxD,MAAMI,oBAAoB,GACtBD,sBAAsB,CAACnB,WAAW,EAAEiB,aAAa,CAAC;EACtD,MAAM;IAACI,OAAO;IAAEU,QAAQ;IAAET,SAAS;IAAEC;EAAQ,CAAC,GAAGS,kBAAkB,CAC/DhD,GAAG,EAAE2C,OAAO,EAAEjB,QAAQ,EAAEC,OAAO,EAAEiB,WAAW,EAAEd,YAAY,EAAEC,WAAW,EACvEe,oBAAoB,EAAEZ,qBAAqB,EAAEE,oBAAoB,EACjEtB,YAAY,CAAC;EAEjB,MAAM2B,WAAW,GAAGjB,SAAS,GAAGK,cAAc,GAAGD,UAAU,GAAGC,cAAc;EAE5E,IAAIa,QAAkD;EACtD,IAAIzC,UAAU,KAAK,eAAe,EAAE;IAClCyC,QAAQ,GAAG,CAACjB,SAAS,EAAEgB,WAAW,EAAEM,QAAQ,EAAET,SAAS,EAAEC,QAAQ,CAAC;GACnE,MAAM,IAAItC,UAAU,KAAK,cAAc,EAAE;IACxCyC,QAAQ,GAAG,CAACjB,SAAS,EAAEsB,QAAQ,EAAET,SAAS,EAAEC,QAAQ,EAAEE,WAAW,CAAC;;EAGpE,OAAO;IACLhB,SAAS;IACTxB,UAAU;IACV0C,OAAO;IACPjB,QAAQ;IACRC,OAAO;IACPC,UAAU;IACVmB,QAAQ;IACRT,SAAS;IACTC,QAAQ;IACRE,WAAW;IACXJ,OAAO;IACPO,WAAW;IACXd,YAAY;IACZC,WAAW;IACXV,WAAW;IACXN,YAAY;IACZC,WAAW;IACX8B,oBAAoB;IACpBZ,qBAAqB;IACrBE,oBAAoB;IACpBS,aAAa;IACbb,cAAc;IACdC,aAAa;IACbrB,OAAO;IACP8B,QAAQ;IACR5C;GACD;AACH;AAEA,SAASmD,oBAAoBA,CACzBrC,OAAyB,EAAEsC,SAAiB,EAAEC,MAAc,EAC5DC,OAAgB,EAAEtC,YAAqC;EACzD,IAAIsC,OAAO,IAAI,IAAI,EAAE;IACnBA,OAAO,GAAGC,iBAAiB,CAACzC,OAAO,EAAEsC,SAAS,EAAEC,MAAM,CAAC;;EAEzD,MAAMG,SAAS,GAAG1C,OAAO,CAAC,CAAC,CAAC;EAC5B,MAAM2C,SAAS,GAAG3C,OAAO,CAAC,CAAC,CAAC;EAE5B,MAAM4C,UAAU,GACZC,KAAK,CAAC,CAACH,SAAS,GAAGJ,SAAS,GAAG,CAAC,GAAGE,OAAO,IAAID,MAAM,GAAG,CAAC,EAAErC,YAAY,CAAC;EAC3E,MAAM4C,UAAU,GACZD,KAAK,CAAC,CAACF,SAAS,GAAGL,SAAS,GAAG,CAAC,GAAGE,OAAO,IAAID,MAAM,GAAG,CAAC,EAAErC,YAAY,CAAC;EAE3E,OAAO,CAAC0C,UAAU,EAAEE,UAAU,CAAC;AACjC;AAEA,SAASC,oBAAoBA,CACzB/C,OAAyC,EACzCd,WAAqC,EAAE2C,WAAmB,EAC1D1C,OAAiC,EAAEqD,OAAgB,EACnDtC,YAAqC;EACvC,IAAIsC,OAAO,IAAI,IAAI,EAAE;IACnBA,OAAO,GAAGC,iBAAiB,CAACzC,OAAO,EAAEd,WAAW,CAAC,CAAC,CAAC,EAAEC,OAAO,CAAC,CAAC,CAAC,CAAC;;EAElE,MAAM2C,QAAQ,GAAqC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAED,WAAW,CAAC;EACzE,KAAK,IAAImB,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAG,CAAC,EAAEA,KAAK,EAAE,EAAE;IACtC,IAAIhD,OAAO,CAACgD,KAAK,CAAC,GAAG,CAAC,GAAGR,OAAO,IAAItD,WAAW,CAAC8D,KAAK,CAAC,EAAE;MACtDlB,QAAQ,CAACkB,KAAK,CAAC,GAAGH,KAAK,CACnB,CAAC7C,OAAO,CAACgD,KAAK,CAAC,GAAG9D,WAAW,CAAC8D,KAAK,CAAC,GAAG,CAAC,GAAGR,OAAO,IAAIrD,OAAO,CAAC6D,KAAK,CAAC,GAChE,CAAC,EACL9C,YAAY,CAAC;;;EAGrB,OAAO4B,QAAQ;AACjB;AAEA,OAAM,SAAUW,iBAAiBA,CAC7BxD,UAA6D,EAC7DqD,SAAiB,EAAEC,MAAc,EAAc;EAAA,IAAZU,QAAQ,GAAA3D,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC;EACjD,MAAM4D,kBAAkB,GAAG3B,sBAAsB,CAACe,SAAS,EAAEW,QAAQ,CAAC;EACtE,OAAOE,IAAI,CAACC,KAAK,CACb,CAACnE,UAAU,CAAC,CAAC,CAAC,IAAIsD,MAAM,GAAG,CAAC,CAAC,GAAGA,MAAM,GAAGW,kBAAkB,IAAI,CAAC,CAAC;AACvE;AAEA,SAAS7C,eAAeA,CAACgD,KAAsB;EAC7C,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;IAC7B,OAAO,CAACA,KAAK,EAAEA,KAAK,EAAEA,KAAK,CAAC;;EAE9B,IAAIA,KAAK,CAAC9D,MAAM,KAAK,CAAC,EAAE;IACtB,OAAO,CAAC8D,KAAK,CAAC,CAAC,CAAC,EAAEA,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;;EAEhC,OAAOA,KAAiC;AAC1C;AAEA,SAAS3C,gBAAgBA,CAAC2C,KAAsC;EAE9D,OAAO,OAAOA,KAAK,KAAK,QAAQ,GAAG,CAACA,KAAK,EAAEA,KAAK,EAAEA,KAAK,CAAC,GAAGA,KAAK;AAClE;AAEA;;;;;;;;;;;AAWA,SAAS9B,sBAAsBA,CAACtB,UAAkB,EAAEgD,QAAgB;EAClE,IAAIA,QAAQ,IAAI,CAAC,EAAE;IACjB,OAAOhD,UAAU;;EAGnB,OAAOA,UAAU,GAAG,CAACA,UAAU,GAAG,CAAC,KAAKgD,QAAQ,GAAG,CAAC,CAAC;AACvD;AAEA,SAASrB,gBAAgBA,CACrBxC,GAA0C,EAAE0B,QAAgB,EAC5DC,OAAe,EAAEG,YAAoB,EAAEC,WAAmB,EAC1DhB,YAAoB,EAAEC,WAAmB,EACzCF,YAAoC,EACpCb,UACc;EAChB,IAAIoC,OAAgB;EACpB,IAAIC,SAAiB;EACrB,IAAIC,QAAgB;EAEpB,IAAI,OAAOvC,GAAG,KAAK,QAAQ,EAAE;IAC3B,MAAMkE,OAAO,GAAIlE,GAAG,KAAK,CAAC,GAAI,OAAO,GAAG,QAAQ;IAChDqC,OAAO,GAAG;MAAC8B,GAAG,EAAEnE,GAAG;MAAEoE,MAAM,EAAEpE,GAAG;MAAEqE,IAAI,EAAErE,GAAG;MAAEsE,KAAK,EAAEtE,GAAG;MAAEuE,IAAI,EAAEL;IAAO,CAAC;IACvE,MAAMxB,QAAQ,GAAGO,oBAAoB,CACjC,CAACvB,QAAQ,EAAEC,OAAO,CAAC,EAAEZ,YAAY,EAAEe,YAAY,EAAE9B,GAAG,EAAEc,YAAY,CAAC;IACvEwB,SAAS,GAAGI,QAAQ,CAAC,CAAC,CAAC;IACvBH,QAAQ,GAAGG,QAAQ,CAAC,CAAC,CAAC;GACvB,MAAM,IAAI1C,GAAG,KAAK,MAAM,EAAE;IACzBsC,SAAS,GAAGyB,IAAI,CAACS,IAAI,CAAC9C,QAAQ,GAAGI,YAAY,CAAC;IAC9CS,QAAQ,GAAGwB,IAAI,CAACS,IAAI,CAAC7C,OAAO,GAAGI,WAAW,CAAC;IAC3C,MAAM0C,cAAc,GAChBV,IAAI,CAACW,GAAG,CAAC,CAAC,EAAE,CAACpC,SAAS,GAAG,CAAC,IAAIR,YAAY,GAAGf,YAAY,GAAGW,QAAQ,CAAC;IACzE,MAAMiD,aAAa,GACfZ,IAAI,CAACW,GAAG,CAAC,CAAC,EAAE,CAACnC,QAAQ,GAAG,CAAC,IAAIR,WAAW,GAAGf,WAAW,GAAGW,OAAO,CAAC;IACrE,MAAMwC,GAAG,GAAGJ,IAAI,CAACC,KAAK,CAACS,cAAc,GAAG,CAAC,CAAC;IAC1C,MAAML,MAAM,GAAGK,cAAc,GAAGN,GAAG;IACnC,MAAME,IAAI,GAAGN,IAAI,CAACC,KAAK,CAACW,aAAa,GAAG,CAAC,CAAC;IAC1C,MAAML,KAAK,GAAGK,aAAa,GAAGN,IAAI;IAClChC,OAAO,GAAG;MAAC8B,GAAG;MAAEC,MAAM;MAAEC,IAAI;MAAEC,KAAK;MAAEC,IAAI,EAAE;IAAM,CAAC;GACnD,MAAM,IAAIvE,GAAG,KAAK,OAAO,EAAE;IAC1BqC,OAAO,GAAG;MAAC8B,GAAG,EAAE,CAAC;MAAEC,MAAM,EAAE,CAAC;MAAEC,IAAI,EAAE,CAAC;MAAEC,KAAK,EAAE,CAAC;MAAEC,IAAI,EAAE;IAAO,CAAC;IAC/DjC,SAAS,GAAGyB,IAAI,CAACS,IAAI,CAAC,CAAC9C,QAAQ,GAAGX,YAAY,GAAG,CAAC,IAAIe,YAAY,CAAC;IACnES,QAAQ,GAAGwB,IAAI,CAACS,IAAI,CAAC,CAAC7C,OAAO,GAAGX,WAAW,GAAG,CAAC,IAAIe,WAAW,CAAC;GAChE,MAAM,IAAI,OAAO/B,GAAG,KAAK,QAAQ,EAAE;IAClC,MAAMmE,GAAG,GAAGlE,UAAU,KAAK,cAAc,GAAGD,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGA,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACjE,MAAMoE,MAAM,GAAGnE,UAAU,KAAK,cAAc,GAAGD,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGA,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACpE,MAAMqE,IAAI,GAAGpE,UAAU,KAAK,cAAc,GAAGD,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGA,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAClE,MAAMsE,KAAK,GAAGrE,UAAU,KAAK,cAAc,GAAGD,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGA,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACnE,MAAMkE,OAAO,GAAIC,GAAG,KAAK,CAAC,IAAIC,MAAM,KAAK,CAAC,IAAIC,IAAI,KAAK,CAAC,IAAIC,KAAK,KAAK,CAAC,GACnE,OAAO,GACP,UAAU;IACdjC,OAAO,GAAG;MAAC8B,GAAG;MAAEC,MAAM;MAAEC,IAAI;MAAEC,KAAK;MAAEC,IAAI,EAAEL;IAAO,CAAC;IACnD5B,SAAS,GAAGmB,KAAK,CACb,CAAC/B,QAAQ,GAAGX,YAAY,GAAGoD,GAAG,GAAGC,MAAM,IAAItC,YAAY,GAAG,CAAC,EAC3DhB,YAAY,CAAC;IACjByB,QAAQ,GAAGkB,KAAK,CACZ,CAAC9B,OAAO,GAAGX,WAAW,GAAGqD,IAAI,GAAGC,KAAK,IAAIvC,WAAW,GAAG,CAAC,EAAEjB,YAAY,CAAC;GAC5E,MAAM;IACL,MAAMI,KAAK,+BAAAC,MAAA,CAA+BnB,GAAG,CAAE,CAAC;;EAElD,OAAO;IAACqC,OAAO;IAAEC,SAAS;IAAEC;EAAQ,CAAC;AACvC;AAEA,SAASS,kBAAkBA,CACvBhD,GAA0B,EAAE2C,OAAe,EAAEjB,QAAgB,EAC7DC,OAAe,EAAEiB,WAAmB,EAAEd,YAAoB,EAC1DC,WAAmB,EAAEV,WAAmB,EAAEN,YAAoB,EAC9DC,WAAmB,EAAEF,YAAqC;EAM5D,IAAIuB,OAAkB;EACtB,IAAIU,QAAgB;EACpB,IAAIT,SAAiB;EACrB,IAAIC,QAAgB;EAEpB,IAAIvC,GAAG,KAAK,OAAO,EAAE;IACnBA,GAAG,GAAG,CAAC;;EAGT,IAAI,OAAOA,GAAG,KAAK,QAAQ,EAAE;IAC3B,MAAMkE,OAAO,GAAIlE,GAAG,KAAK,CAAC,GAAI,OAAO,GAAG,QAAQ;IAChDqC,OAAO,GAAG;MACR8B,GAAG,EAAEnE,GAAG;MACRoE,MAAM,EAAEpE,GAAG;MACXqE,IAAI,EAAErE,GAAG;MACTsE,KAAK,EAAEtE,GAAG;MACV4E,KAAK,EAAE5E,GAAG;MACV6E,IAAI,EAAE7E,GAAG;MACTuE,IAAI,EAAEL;KACP;IACD,MAAMxB,QAAQ,GAAGiB,oBAAoB,CACjC,CAAChB,OAAO,EAAEjB,QAAQ,EAAEC,OAAO,EAAE,CAAC,CAAC,EAC/B,CAACN,WAAW,EAAEN,YAAY,EAAEC,WAAW,CAAC,EAAE,CAAC,EAC3C,CAAC4B,WAAW,EAAEd,YAAY,EAAEC,WAAW,CAAC,EAAE/B,GAAG,EAAEc,YAAY,CAAC;IAChEiC,QAAQ,GAAGL,QAAQ,CAAC,CAAC,CAAC;IACtBJ,SAAS,GAAGI,QAAQ,CAAC,CAAC,CAAC;IACvBH,QAAQ,GAAGG,QAAQ,CAAC,CAAC,CAAC;GACvB,MAAM,IAAI1C,GAAG,KAAK,MAAM,EAAE;IACzB+C,QAAQ,GAAGgB,IAAI,CAACS,IAAI,CAAC7B,OAAO,GAAGC,WAAW,CAAC;IAC3CN,SAAS,GAAGyB,IAAI,CAACS,IAAI,CAAC9C,QAAQ,GAAGI,YAAY,CAAC;IAC9CS,QAAQ,GAAGwB,IAAI,CAACS,IAAI,CAAC7C,OAAO,GAAGI,WAAW,CAAC;IAC3C,MAAM+C,aAAa,GAAG,CAAC/B,QAAQ,GAAG,CAAC,IAAIH,WAAW,GAAGvB,WAAW,GAAGsB,OAAO;IAC1E,MAAM8B,cAAc,GAChB,CAACnC,SAAS,GAAG,CAAC,IAAIR,YAAY,GAAGf,YAAY,GAAGW,QAAQ;IAC5D,MAAMiD,aAAa,GAAG,CAACpC,QAAQ,GAAG,CAAC,IAAIR,WAAW,GAAGf,WAAW,GAAGW,OAAO;IAC1E,MAAMiD,KAAK,GAAGb,IAAI,CAACC,KAAK,CAACc,aAAa,GAAG,CAAC,CAAC;IAC3C,MAAMD,IAAI,GAAGC,aAAa,GAAGF,KAAK;IAClC,MAAMT,GAAG,GAAGJ,IAAI,CAACC,KAAK,CAACS,cAAc,GAAG,CAAC,CAAC;IAC1C,MAAML,MAAM,GAAGK,cAAc,GAAGN,GAAG;IACnC,MAAME,IAAI,GAAGN,IAAI,CAACC,KAAK,CAACW,aAAa,GAAG,CAAC,CAAC;IAC1C,MAAML,KAAK,GAAGK,aAAa,GAAGN,IAAI;IAElChC,OAAO,GAAG;MAAC8B,GAAG;MAAEC,MAAM;MAAEC,IAAI;MAAEC,KAAK;MAAEM,KAAK;MAAEC,IAAI;MAAEN,IAAI,EAAE;IAAM,CAAC;GAChE,MAAM;IACL,MAAMrD,KAAK,+BAAAC,MAAA,CAA+BnB,GAAG,CAAE,CAAC;;EAElD,OAAO;IAACqC,OAAO;IAAEU,QAAQ;IAAET,SAAS;IAAEC;EAAQ,CAAC;AACjD;AAEA;;;;;;AAMA,SAASkB,KAAKA,CAACsB,KAAa,EAAEjE,YAAqC;EACjE,IAAI,CAACA,YAAY,EAAE;IACjB,OAAOiD,IAAI,CAACiB,KAAK,CAACD,KAAK,CAAC;;EAE1B,QAAQjE,YAAY;IAClB,KAAK,OAAO;MACV;MACA,OAAOiD,IAAI,CAACN,KAAK,CAACsB,KAAK,CAAC;IAC1B,KAAK,MAAM;MACT;MACA,OAAOhB,IAAI,CAACS,IAAI,CAACO,KAAK,CAAC;IACzB,KAAK,OAAO;MACV,OAAOhB,IAAI,CAACC,KAAK,CAACe,KAAK,CAAC;IAC1B;MACE,MAAM,IAAI7D,KAAK,yBAAAC,MAAA,CAAyBL,YAAY,CAAE,CAAC;;AAE7D;AAEA,OAAM,SAAUmE,iBAAiBA,CAAChB,KAAsB;EACtD,MAAM,CAACiB,IAAI,EAAEC,IAAI,EAAEC,IAAI,CAAC,GAAGnE,eAAe,CAACgD,KAAK,CAAC;EACjD,OAAOiB,IAAI,KAAK,CAAC,IAAIC,IAAI,KAAK,CAAC,IAAIC,IAAI,KAAK,CAAC;AAC/C;AAEA,OAAM,SAAUC,8BAA8BA,CAC1CtF,OAAwB,EAAEM,SAA0B;EACtD,OAAO4E,iBAAiB,CAAClF,OAAO,CAAC,IAAIkF,iBAAiB,CAAC5E,SAAS,CAAC;AACnE;AAEA,OAAM,SAAUiF,6BAA6BA,CAACC,MACQ;EACpD,OAAOtE,eAAe,CAACsE,MAAM,CAAC,CAACC,KAAK,CAACT,KAAK,IAAIA,KAAK,GAAG,CAAC,CAAC;AAC1D;AAEA;;;;;;;AAOA,OAAM,SAAUtE,uBAAuBA,CAACR,UAAyB;EAE/D,IAAIA,UAAU,KAAK,MAAM,EAAE;IACzB,OAAO,cAAc;GACtB,MAAM,IAAIA,UAAU,KAAK,MAAM,EAAE;IAChC,OAAO,eAAe;GACvB,MAAM;IACL,MAAM,IAAIiB,KAAK,uBAAAC,MAAA,CAAuBlB,UAAU,CAAE,CAAC;;AAEvD;AAEA;;;;;;;;;;;;;;;AAeA,OAAM,SAAUwF,yBAAyBA,CACrCC,MAAc,EAAE1F,GAA0C,EAC1D2F,eAAwC;EAC1C,IAAIA,eAAe,IAAI,IAAI,EAAE;IAC3B,IAAI,OAAO3F,GAAG,KAAK,QAAQ,EAAE;MAC3B,MAAMkB,KAAK,CACP,YAAAC,MAAA,CAAYuE,MAAM,+DAAAvE,MAAA,CACCwE,eAAe,mBAAAxE,MAAA,CAAgBnB,GAAG,MAAG,CAAC;KAC9D,MAAM,IAAI,OAAOA,GAAG,KAAK,QAAQ,EAAE;MAClCL,IAAI,CAACiG,MAAM,CACPjG,IAAI,CAACkG,KAAK,CAAC7F,GAAG,CAAC,EACf,MAAM,YAAAmB,MAAA,CAAYuE,MAAM,+DAAAvE,MAAA,CACDwE,eAAe,mBAAAxE,MAAA,CAAgBnB,GAAG,MAAG,CAAC;KAClE,MAAM,IAAI,OAAOA,GAAG,KAAK,QAAQ,EAAE;MACjCA,GAAuB,CAAC8F,OAAO,CAACC,CAAC,IAAG;QACnCA,CAAC,CAACD,OAAO,CAACE,CAAC,IAAG;UACZrG,IAAI,CAACiG,MAAM,CACPjG,IAAI,CAACkG,KAAK,CAACG,CAAC,CAAC,EACb,MAAM,YAAA7E,MAAA,CAAYuE,MAAM,+DAAAvE,MAAA,CACDwE,eAAe,mBAAAxE,MAAA,CAAgB6E,CAAC,MAAG,CAAC;QACjE,CAAC,CAAC;MACJ,CAAC,CAAC;KACH,MAAM;MACL,MAAM9E,KAAK,aAAAC,MAAA,CAAauE,MAAM,mCAAAvE,MAAA,CAAgCnB,GAAG,CAAE,CAAC;;;AAG1E", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}