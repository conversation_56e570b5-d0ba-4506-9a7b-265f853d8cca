{"ast": null, "code": "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { util } from '@tensorflow/tfjs-core';\nexport function rangeImpl(start, stop, step, dtype) {\n  const sameStartStop = start === stop;\n  const increasingRangeNegativeStep = start < stop && step < 0;\n  const decreasingRangePositiveStep = stop < start && step > 1;\n  if (sameStartStop || increasingRangeNegativeStep || decreasingRangePositiveStep) {\n    return util.makeZerosTypedArray(0, dtype);\n  }\n  const numElements = Math.abs(Math.ceil((stop - start) / step));\n  const values = util.makeZerosTypedArray(numElements, dtype);\n  if (stop < start && step === 1) {\n    // Auto adjust the step's sign if it hasn't been set\n    // (or was set to 1)\n    step = -1;\n  }\n  values[0] = start;\n  for (let i = 1; i < values.length; i++) {\n    values[i] = values[i - 1] + step;\n  }\n  return values;\n}", "map": {"version": 3, "names": ["util", "rangeImpl", "start", "stop", "step", "dtype", "sameStartStop", "increasingRangeNegativeStep", "decreasingRangePositiveStep", "makeZerosTypedArray", "numElements", "Math", "abs", "ceil", "values", "i", "length"], "sources": ["C:\\tfjs-backend-cpu\\src\\kernels\\Range_impl.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {DataTypeMap, util} from '@tensorflow/tfjs-core';\n\nexport function rangeImpl(\n    start: number, stop: number, step: number,\n    dtype: 'float32'|'int32'): DataTypeMap['float32' | 'int32'] {\n  const sameStartStop = start === stop;\n  const increasingRangeNegativeStep = start < stop && step < 0;\n  const decreasingRangePositiveStep = stop < start && step > 1;\n\n  if (sameStartStop || increasingRangeNegativeStep ||\n      decreasingRangePositiveStep) {\n    return util.makeZerosTypedArray(0, dtype);\n  }\n\n  const numElements = Math.abs(Math.ceil((stop - start) / step));\n  const values = util.makeZerosTypedArray(numElements, dtype);\n\n  if (stop < start && step === 1) {\n    // Auto adjust the step's sign if it hasn't been set\n    // (or was set to 1)\n    step = -1;\n  }\n\n  values[0] = start;\n  for (let i = 1; i < values.length; i++) {\n    values[i] = values[i - 1] + step;\n  }\n  return values;\n}\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAiBA,SAAqBA,IAAI,QAAO,uBAAuB;AAEvD,OAAM,SAAUC,SAASA,CACrBC,KAAa,EAAEC,IAAY,EAAEC,IAAY,EACzCC,KAAwB;EAC1B,MAAMC,aAAa,GAAGJ,KAAK,KAAKC,IAAI;EACpC,MAAMI,2BAA2B,GAAGL,KAAK,GAAGC,IAAI,IAAIC,IAAI,GAAG,CAAC;EAC5D,MAAMI,2BAA2B,GAAGL,IAAI,GAAGD,KAAK,IAAIE,IAAI,GAAG,CAAC;EAE5D,IAAIE,aAAa,IAAIC,2BAA2B,IAC5CC,2BAA2B,EAAE;IAC/B,OAAOR,IAAI,CAACS,mBAAmB,CAAC,CAAC,EAAEJ,KAAK,CAAC;;EAG3C,MAAMK,WAAW,GAAGC,IAAI,CAACC,GAAG,CAACD,IAAI,CAACE,IAAI,CAAC,CAACV,IAAI,GAAGD,KAAK,IAAIE,IAAI,CAAC,CAAC;EAC9D,MAAMU,MAAM,GAAGd,IAAI,CAACS,mBAAmB,CAACC,WAAW,EAAEL,KAAK,CAAC;EAE3D,IAAIF,IAAI,GAAGD,KAAK,IAAIE,IAAI,KAAK,CAAC,EAAE;IAC9B;IACA;IACAA,IAAI,GAAG,CAAC,CAAC;;EAGXU,MAAM,CAAC,CAAC,CAAC,GAAGZ,KAAK;EACjB,KAAK,IAAIa,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,MAAM,CAACE,MAAM,EAAED,CAAC,EAAE,EAAE;IACtCD,MAAM,CAACC,CAAC,CAAC,GAAGD,MAAM,CAACC,CAAC,GAAG,CAAC,CAAC,GAAGX,IAAI;;EAElC,OAAOU,MAAM;AACf", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}