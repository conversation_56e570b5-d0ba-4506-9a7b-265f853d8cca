{"ast": null, "code": "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { Acos } from '@tensorflow/tfjs-core';\nimport { unaryKernelFunc } from '../kernel_utils/kernel_funcs_utils';\nimport { CHECK_NAN_SNIPPET } from '../unaryop_gpu';\nconst ACOS = CHECK_NAN_SNIPPET + \"\\n  if (abs(x) > 1.) {\\n    return NAN;\\n  }\\n  return acos(x);\\n\";\nexport const acos = unaryKernelFunc({\n  opSnippet: ACOS\n});\nexport const acosConfig = {\n  kernelName: Acos,\n  backendName: 'webgl',\n  kernelFunc: acos\n};", "map": {"version": 3, "names": ["Acos", "unaryKernelFunc", "CHECK_NAN_SNIPPET", "ACOS", "acos", "opSnippet", "acosConfig", "kernelName", "backendName", "kernelFunc"], "sources": ["C:\\tfjs-backend-webgl\\src\\kernels\\Acos.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {Acos, KernelConfig} from '@tensorflow/tfjs-core';\n\nimport {unaryKernelFunc} from '../kernel_utils/kernel_funcs_utils';\nimport {CHECK_NAN_SNIPPET} from '../unaryop_gpu';\n\nconst ACOS = CHECK_NAN_SNIPPET + `\n  if (abs(x) > 1.) {\n    return NAN;\n  }\n  return acos(x);\n`;\n\nexport const acos = unaryKernelFunc({opSnippet: ACOS});\n\nexport const acosConfig: KernelConfig = {\n  kernelName: Acos,\n  backendName: 'webgl',\n  kernelFunc: acos,\n};\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAiBA,SAAQA,IAAI,QAAqB,uBAAuB;AAExD,SAAQC,eAAe,QAAO,oCAAoC;AAClE,SAAQC,iBAAiB,QAAO,gBAAgB;AAEhD,MAAMC,IAAI,GAAGD,iBAAiB,sEAK7B;AAED,OAAO,MAAME,IAAI,GAAGH,eAAe,CAAC;EAACI,SAAS,EAAEF;AAAI,CAAC,CAAC;AAEtD,OAAO,MAAMG,UAAU,GAAiB;EACtCC,UAAU,EAAEP,IAAI;EAChBQ,WAAW,EAAE,OAAO;EACpBC,UAAU,EAAEL;CACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}