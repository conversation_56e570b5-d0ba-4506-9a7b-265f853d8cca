{"ast": null, "code": "// CSV Template Configuration\nexport const CSV_TEMPLATE={headers:['Bank reference','Narrative','Customer reference','TRN type','Value date','Credit amount','Debit amount','Time','Post date','Balance'],validationRules:[{field:'Bank reference',rule:'required',message:'Bank reference is required'},{field:'Narrative',rule:'required',message:'Narrative is required'},{field:'TRN type',rule:'required',message:'Transaction type is required'},{field:'Value date',rule:'required',message:'Value date is required'},{field:'Value date',rule:'date',message:'Value date must be in valid format (DD/MM/YYYY)'},{field:'Post date',rule:'required',message:'Post date is required'},{field:'Post date',rule:'date',message:'Post date must be in valid format (DD/MM/YYYY)'},{field:'Credit amount',rule:'number',message:'Credit amount must be a valid number'},{field:'Debit amount',rule:'number',message:'Debit amount must be a valid number'},{field:'Balance',rule:'required',message:'Balance is required'},{field:'Balance',rule:'number',message:'Balance must be a valid number'}],sampleData:[{'Bank reference':'829471Z01W0W','Narrative':'HELLMANN SAUDI ARABIA TFR+','Customer reference':'INV241221276270','TRN type':'Transfer','Value date':'31/12/2024','Credit amount':'5,444.10','Debit amount':'','Time':'19:05','Post date':'31/12/2024','Balance':'20,784,406.54'},{'Bank reference':'LP CPM4057MZ','Narrative':'TECHNICAL LINKS SERVICES COMPANY','Customer reference':'609720','TRN type':'Transfer','Value date':'30/12/2024','Credit amount':'','Debit amount':'15,000,000.00','Time':'12:45','Post date':'30/12/2024','Balance':'15,010,179.45'},{'Bank reference':'SDR5-00029','Narrative':'SEVING THOMAS THDMDIYIL SADAD','Customer reference':'NONREF','TRN type':'Cheque','Value date':'01/01/2025','Credit amount':'31,050.00','Debit amount':'','Time':'11:27','Post date':'30/12/2024','Balance':'33,922,117.24'}]};class CSVProcessingService{// Generate CSV template for download\ngenerateTemplate(){const headers=CSV_TEMPLATE.headers.join(',');const rows=CSV_TEMPLATE.sampleData.map(row=>CSV_TEMPLATE.headers.map(header=>\"\\\"\".concat(row[header]||'',\"\\\"\")).join(','));return[headers,...rows].join('\\n');}// Download CSV template\ndownloadTemplate(){const csvContent=this.generateTemplate();const blob=new Blob([csvContent],{type:'text/csv;charset=utf-8;'});const link=document.createElement('a');if(link.download!==undefined){const url=URL.createObjectURL(blob);link.setAttribute('href',url);link.setAttribute('download','bank_statement_template.csv');link.style.visibility='hidden';document.body.appendChild(link);link.click();document.body.removeChild(link);}}// Parse CSV content\nasync parseCSV(file){return new Promise((resolve,reject)=>{const reader=new FileReader();reader.onload=event=>{try{var _event$target;const csv=(_event$target=event.target)===null||_event$target===void 0?void 0:_event$target.result;const rows=this.parseCSVContent(csv);resolve(rows);}catch(error){reject(error);}};reader.onerror=()=>reject(new Error('Failed to read file'));reader.readAsText(file);});}parseCSVContent(csv){const lines=csv.split('\\n').filter(line=>line.trim());if(lines.length<2)throw new Error('CSV must contain at least a header and one data row');const headers=this.parseCSVLine(lines[0]).map(h=>h.trim());const expectedHeaders=['bank reference','narrative','customer reference','trn type','value date','credit amount','debit amount','time','post date','balance'];// Validate headers (case-insensitive)\nconst normalizedHeaders=headers.map(h=>h.toLowerCase());const missingHeaders=expectedHeaders.filter(h=>!normalizedHeaders.includes(h));if(missingHeaders.length>0){throw new Error(\"Missing required headers: \".concat(missingHeaders.join(', ')));}// Create header mapping\nconst headerMap={};expectedHeaders.forEach(expectedHeader=>{const index=normalizedHeaders.findIndex(h=>h===expectedHeader);headerMap[expectedHeader.replace(' ','')]=index;});// Parse data rows\nconst dataRows=[];for(let i=1;i<lines.length;i++){var _values$headerMap$ban,_values$headerMap$nar,_values$headerMap$cus,_values$headerMap$trn,_values$headerMap$val,_values$headerMap$cre,_values$headerMap$deb,_values$headerMap$tim,_values$headerMap$pos,_values$headerMap$bal;const values=this.parseCSVLine(lines[i]);if(values.length===0||values.every(v=>!v.trim()))continue;// Skip empty rows\nconst row={bankReference:((_values$headerMap$ban=values[headerMap.bankreference])===null||_values$headerMap$ban===void 0?void 0:_values$headerMap$ban.trim())||'',narrative:((_values$headerMap$nar=values[headerMap.narrative])===null||_values$headerMap$nar===void 0?void 0:_values$headerMap$nar.trim())||'',customerReference:((_values$headerMap$cus=values[headerMap.customerreference])===null||_values$headerMap$cus===void 0?void 0:_values$headerMap$cus.trim())||'',trnType:((_values$headerMap$trn=values[headerMap.trntype])===null||_values$headerMap$trn===void 0?void 0:_values$headerMap$trn.trim())||'',valueDate:((_values$headerMap$val=values[headerMap.valuedate])===null||_values$headerMap$val===void 0?void 0:_values$headerMap$val.trim())||'',creditAmount:((_values$headerMap$cre=values[headerMap.creditamount])===null||_values$headerMap$cre===void 0?void 0:_values$headerMap$cre.trim())||'',debitAmount:((_values$headerMap$deb=values[headerMap.debitamount])===null||_values$headerMap$deb===void 0?void 0:_values$headerMap$deb.trim())||'',time:((_values$headerMap$tim=values[headerMap.time])===null||_values$headerMap$tim===void 0?void 0:_values$headerMap$tim.trim())||'',postDate:((_values$headerMap$pos=values[headerMap.postdate])===null||_values$headerMap$pos===void 0?void 0:_values$headerMap$pos.trim())||'',balance:((_values$headerMap$bal=values[headerMap.balance])===null||_values$headerMap$bal===void 0?void 0:_values$headerMap$bal.trim())||''};dataRows.push(row);}return dataRows;}parseCSVLine(line){const result=[];let current='';let inQuotes=false;for(let i=0;i<line.length;i++){const char=line[i];if(char==='\"'){if(inQuotes&&line[i+1]==='\"'){current+='\"';i++;// Skip next quote\n}else{inQuotes=!inQuotes;}}else if(char===','&&!inQuotes){result.push(current);current='';}else{current+=char;}}result.push(current);return result;}// Validate CSV data\nvalidateCSVData(rows){const errors=[];rows.forEach((row,index)=>{const rowNumber=index+2;// +2 because index is 0-based and we skip header\n// Validate each field\nCSV_TEMPLATE.validationRules.forEach(rule=>{const fieldValue=this.getFieldValue(row,rule.field);const error=this.validateField(fieldValue,rule,rowNumber);if(error)errors.push(error);});// For bank statements, we trust the balance field as it represents the actual account balance\n// Balance validation is disabled for bank statement imports as the data comes directly from the bank\n// and may not be in strict chronological order or may include pending transactions\n});return errors;}getFieldValue(row,field){switch(field.toLowerCase()){case'bank reference':return row.bankReference;case'narrative':return row.narrative;case'customer reference':return row.customerReference;case'trn type':return row.trnType;case'value date':return row.valueDate;case'credit amount':return row.creditAmount;case'debit amount':return row.debitAmount;case'time':return row.time;case'post date':return row.postDate;case'balance':return row.balance;default:return'';}}validateField(value,rule,rowNumber){switch(rule.rule){case'required':if(!value||value.trim()===''){return{row:rowNumber,field:rule.field,message:rule.message,value};}break;case'number':if(value.trim()&&isNaN(this.parseAmount(value))){return{row:rowNumber,field:rule.field,message:rule.message,value};}break;case'date':if(value.trim()&&!this.isValidDate(value)){return{row:rowNumber,field:rule.field,message:rule.message,value};}break;case'positive':if(value.trim()&&this.parseAmount(value)<0){return{row:rowNumber,field:rule.field,message:rule.message,value};}break;}return null;}parseAmount(value){if(!value||value.trim()==='')return 0;// Remove currency symbols, commas, and quotes, handle negative values\nconst cleaned=value.replace(/[$,\\s\"]/g,'');// Handle negative values (with minus sign or parentheses)\nconst isNegative=cleaned.startsWith('-')||cleaned.startsWith('(')&&cleaned.endsWith(')');const numericValue=cleaned.replace(/[-()]/g,'');const amount=parseFloat(numericValue);if(isNaN(amount))return 0;return isNegative?-amount:amount;}isValidDate(dateString){// Handle DD/MM/YYYY format (Saudi bank format)\nif(dateString.includes('/')){const parts=dateString.split('/');if(parts.length===3){const day=parseInt(parts[0]);const month=parseInt(parts[1]);const year=parseInt(parts[2]);// Basic range validation\nif(day>=1&&day<=31&&month>=1&&month<=12&&year>=1900&&year<=2100){// Create date object to validate the actual date (handles leap years, etc.)\nconst date=new Date(year,month-1,day);return date.getFullYear()===year&&date.getMonth()===month-1&&date.getDate()===day;}}}// Fallback to original logic\nconst date=new Date(dateString);return date instanceof Date&&!isNaN(date.getTime());}// Convert CSV rows to transactions\nconvertToTransactions(rows){return rows.map((row,index)=>({id:\"temp_\".concat(Date.now(),\"_\").concat(index),date:this.formatDate(row.postDate),description:row.narrative,debitAmount:this.parseAmount(row.debitAmount),creditAmount:this.parseAmount(row.creditAmount),balance:this.parseAmount(row.balance),reference:row.customerReference,postDate:row.postDate,time:row.time,valueDate:row.valueDate}));}formatDate(dateString){// Handle DD/MM/YYYY format from bank statements (Saudi bank format)\nif(dateString.includes('/')){const parts=dateString.split('/');if(parts.length===3){const day=parts[0].padStart(2,'0');const month=parts[1].padStart(2,'0');const year=parts[2];// Validate the date parts\nconst dayNum=parseInt(day);const monthNum=parseInt(month);const yearNum=parseInt(year);if(dayNum>=1&&dayNum<=31&&monthNum>=1&&monthNum<=12&&yearNum>=1900){// Convert DD/MM/YYYY to YYYY-MM-DD format\nreturn\"\".concat(year,\"-\").concat(month,\"-\").concat(day);}}}// Fallback to original logic\nconst date=new Date(dateString);return date.toISOString().split('T')[0];}// Helper method to create a sortable datetime from Post date and Time\ncreateSortableDateTime(postDate,time){const formattedDate=this.formatDate(postDate);// Handle time format (HH:MM)\nlet timeString='00:00';if(time&&time.trim()){timeString=time.trim();// Ensure time is in HH:MM format\nif(timeString.length===4&&!timeString.includes(':')){// Convert HHMM to HH:MM\ntimeString=timeString.substring(0,2)+':'+timeString.substring(2);}}// Combine date and time\nconst dateTimeString=\"\".concat(formattedDate,\"T\").concat(timeString,\":00\");return new Date(dateTimeString);}// Process file and generate import summary\nasync processFile(file){try{const rows=await this.parseCSV(file);const validationErrors=this.validateCSVData(rows);const transactions=this.convertToTransactions(rows);// Sort transactions by Post date and time (newest first, matching bank statement order)\nconst sortedTransactions=transactions.sort((a,b)=>{const dateA=this.createSortableDateTime(a.postDate||a.date,a.time||'00:00');const dateB=this.createSortableDateTime(b.postDate||b.date,b.time||'00:00');return dateB.getTime()-dateA.getTime();// Newest first (most recent transaction first)\n});const totalDebitAmount=transactions.reduce((sum,t)=>sum+t.debitAmount,0);const totalCreditAmount=transactions.reduce((sum,t)=>sum+t.creditAmount,0);// The closing balance is from the FIRST transaction (most recent) since bank statements are in reverse chronological order\nconst closingBalance=transactions.length>0?transactions[0].balance:0;// Calculate opening balance from the LAST transaction (oldest)\nconst openingBalance=transactions.length>0?transactions[transactions.length-1].balance:0;// Calculate daily movement (net change)\nconst dailyMovement=closingBalance-openingBalance;return{fileName:file.name,totalTransactions:transactions.length,totalDebitAmount,totalCreditAmount,closingBalance,openingBalance,dailyMovement,validationErrors,transactions:sortedTransactions,dateRange:{from:transactions.length>0?sortedTransactions[sortedTransactions.length-1].date:'',to:transactions.length>0?sortedTransactions[0].date:''}};}catch(error){throw new Error(\"Failed to process \".concat(file.name,\": \").concat(error instanceof Error?error.message:'Unknown error'));}}}export const csvProcessingService=new CSVProcessingService();", "map": {"version": 3, "names": ["CSV_TEMPLATE", "headers", "validationRules", "field", "rule", "message", "sampleData", "CSVProcessingService", "generateTemplate", "join", "rows", "map", "row", "header", "concat", "downloadTemplate", "csv<PERSON><PERSON>nt", "blob", "Blob", "type", "link", "document", "createElement", "download", "undefined", "url", "URL", "createObjectURL", "setAttribute", "style", "visibility", "body", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "parseCSV", "file", "Promise", "resolve", "reject", "reader", "FileReader", "onload", "event", "_event$target", "csv", "target", "result", "parseCS<PERSON><PERSON>nt", "error", "onerror", "Error", "readAsText", "lines", "split", "filter", "line", "trim", "length", "parseCSVLine", "h", "expectedHeaders", "normalizedHeaders", "toLowerCase", "missingHeaders", "includes", "headerMap", "for<PERSON>ach", "expected<PERSON>eader", "index", "findIndex", "replace", "dataRows", "i", "_values$headerMap$ban", "_values$headerMap$nar", "_values$headerMap$cus", "_values$headerMap$trn", "_values$headerMap$val", "_values$headerMap$cre", "_values$headerMap$deb", "_values$headerMap$tim", "_values$headerMap$pos", "_values$headerMap$bal", "values", "every", "v", "bankReference", "bankreference", "narrative", "customerReference", "customerreference", "trnType", "trntype", "valueDate", "valuedate", "creditAmount", "creditamount", "debitAmount", "debitamount", "time", "postDate", "postdate", "balance", "push", "current", "inQuotes", "char", "validateCSVData", "errors", "rowNumber", "fieldValue", "getFieldValue", "validateField", "value", "isNaN", "parseAmount", "isValidDate", "cleaned", "isNegative", "startsWith", "endsWith", "numericValue", "amount", "parseFloat", "dateString", "parts", "day", "parseInt", "month", "year", "date", "Date", "getFullYear", "getMonth", "getDate", "getTime", "convertToTransactions", "id", "now", "formatDate", "description", "reference", "padStart", "day<PERSON>um", "monthNum", "yearNum", "toISOString", "createSortableDateTime", "formattedDate", "timeString", "substring", "dateTimeString", "processFile", "validationErrors", "transactions", "sortedTransactions", "sort", "a", "b", "dateA", "dateB", "totalDebitAmount", "reduce", "sum", "t", "totalCreditAmount", "closingBalance", "openingBalance", "dailyMovement", "fileName", "name", "totalTransactions", "date<PERSON><PERSON><PERSON>", "from", "to", "csvProcessingService"], "sources": ["C:/tmsft/src/services/csvProcessingService.ts"], "sourcesContent": ["import { CSVRow, Transaction, ImportSummary, ValidationError, ValidationRule, CSVTemplate } from '../types';\r\n\r\n// CSV Template Configuration\r\nexport const CSV_TEMPLATE: CSVTemplate = {\r\n  headers: ['Bank reference', 'Narrative', 'Customer reference', 'TRN type', 'Value date', 'Credit amount', 'Debit amount', 'Time', 'Post date', 'Balance'],\r\n  validationRules: [\r\n    { field: 'Bank reference', rule: 'required', message: 'Bank reference is required' },\r\n    { field: 'Narrative', rule: 'required', message: 'Narrative is required' },\r\n    { field: 'TRN type', rule: 'required', message: 'Transaction type is required' },\r\n    { field: 'Value date', rule: 'required', message: 'Value date is required' },\r\n    { field: 'Value date', rule: 'date', message: 'Value date must be in valid format (DD/MM/YYYY)' },\r\n    { field: 'Post date', rule: 'required', message: 'Post date is required' },\r\n    { field: 'Post date', rule: 'date', message: 'Post date must be in valid format (DD/MM/YYYY)' },\r\n    { field: 'Credit amount', rule: 'number', message: 'Credit amount must be a valid number' },\r\n    { field: 'Debit amount', rule: 'number', message: 'Debit amount must be a valid number' },\r\n    { field: 'Balance', rule: 'required', message: 'Balance is required' },\r\n    { field: 'Balance', rule: 'number', message: 'Balance must be a valid number' },\r\n\r\n  ],\r\n  sampleData: [\r\n    {\r\n      'Bank reference': '829471Z01W0W',\r\n      'Narrative': 'HELLMANN SAUDI ARABIA TFR+',\r\n      'Customer reference': 'INV241221276270',\r\n      'TRN type': 'Transfer',\r\n      'Value date': '31/12/2024',\r\n      'Credit amount': '5,444.10',\r\n      'Debit amount': '',\r\n      'Time': '19:05',\r\n      'Post date': '31/12/2024',\r\n      'Balance': '20,784,406.54'\r\n    },\r\n    {\r\n      'Bank reference': 'LP CPM4057MZ',\r\n      'Narrative': 'TECHNICAL LINKS SERVICES COMPANY',\r\n      'Customer reference': '609720',\r\n      'TRN type': 'Transfer',\r\n      'Value date': '30/12/2024',\r\n      'Credit amount': '',\r\n      'Debit amount': '15,000,000.00',\r\n      'Time': '12:45',\r\n      'Post date': '30/12/2024',\r\n      'Balance': '15,010,179.45'\r\n    },\r\n    {\r\n      'Bank reference': 'SDR5-00029',\r\n      'Narrative': 'SEVING THOMAS THDMDIYIL SADAD',\r\n      'Customer reference': 'NONREF',\r\n      'TRN type': 'Cheque',\r\n      'Value date': '01/01/2025',\r\n      'Credit amount': '31,050.00',\r\n      'Debit amount': '',\r\n      'Time': '11:27',\r\n      'Post date': '30/12/2024',\r\n      'Balance': '33,922,117.24'\r\n    }\r\n  ]\r\n};\r\n\r\nclass CSVProcessingService {\r\n  \r\n  // Generate CSV template for download\r\n  generateTemplate(): string {\r\n    const headers = CSV_TEMPLATE.headers.join(',');\r\n    const rows = CSV_TEMPLATE.sampleData.map(row => \r\n      CSV_TEMPLATE.headers.map(header => `\"${row[header] || ''}\"`).join(',')\r\n    );\r\n    \r\n    return [headers, ...rows].join('\\n');\r\n  }\r\n\r\n  // Download CSV template\r\n  downloadTemplate(): void {\r\n    const csvContent = this.generateTemplate();\r\n    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });\r\n    const link = document.createElement('a');\r\n    \r\n    if (link.download !== undefined) {\r\n      const url = URL.createObjectURL(blob);\r\n      link.setAttribute('href', url);\r\n      link.setAttribute('download', 'bank_statement_template.csv');\r\n      link.style.visibility = 'hidden';\r\n      document.body.appendChild(link);\r\n      link.click();\r\n      document.body.removeChild(link);\r\n    }\r\n  }\r\n\r\n  // Parse CSV content\r\n  async parseCSV(file: File): Promise<CSVRow[]> {\r\n    return new Promise((resolve, reject) => {\r\n      const reader = new FileReader();\r\n      \r\n      reader.onload = (event) => {\r\n        try {\r\n          const csv = event.target?.result as string;\r\n          const rows = this.parseCSVContent(csv);\r\n          resolve(rows);\r\n        } catch (error) {\r\n          reject(error);\r\n        }\r\n      };\r\n      \r\n      reader.onerror = () => reject(new Error('Failed to read file'));\r\n      reader.readAsText(file);\r\n    });\r\n  }\r\n\r\n  private parseCSVContent(csv: string): CSVRow[] {\r\n    const lines = csv.split('\\n').filter(line => line.trim());\r\n    if (lines.length < 2) throw new Error('CSV must contain at least a header and one data row');\r\n    \r\n    const headers = this.parseCSVLine(lines[0]).map(h => h.trim());\r\n    const expectedHeaders = [\r\n      'bank reference',\r\n      'narrative', \r\n      'customer reference',\r\n      'trn type',\r\n      'value date',\r\n      'credit amount',\r\n      'debit amount',\r\n      'time',\r\n      'post date',\r\n      'balance'\r\n    ];\r\n    \r\n    // Validate headers (case-insensitive)\r\n    const normalizedHeaders = headers.map(h => h.toLowerCase());\r\n    const missingHeaders = expectedHeaders.filter(h => !normalizedHeaders.includes(h));\r\n    \r\n    if (missingHeaders.length > 0) {\r\n      throw new Error(`Missing required headers: ${missingHeaders.join(', ')}`);\r\n    }\r\n\r\n    // Create header mapping\r\n    const headerMap: Record<string, number> = {};\r\n    expectedHeaders.forEach(expectedHeader => {\r\n      const index = normalizedHeaders.findIndex(h => h === expectedHeader);\r\n      headerMap[expectedHeader.replace(' ', '')] = index;\r\n    });\r\n\r\n    // Parse data rows\r\n    const dataRows: CSVRow[] = [];\r\n    for (let i = 1; i < lines.length; i++) {\r\n      const values = this.parseCSVLine(lines[i]);\r\n      if (values.length === 0 || values.every(v => !v.trim())) continue; // Skip empty rows\r\n      \r\n      const row: CSVRow = {\r\n        bankReference: values[headerMap.bankreference]?.trim() || '',\r\n        narrative: values[headerMap.narrative]?.trim() || '',\r\n        customerReference: values[headerMap.customerreference]?.trim() || '',\r\n        trnType: values[headerMap.trntype]?.trim() || '',\r\n        valueDate: values[headerMap.valuedate]?.trim() || '',\r\n        creditAmount: values[headerMap.creditamount]?.trim() || '',\r\n        debitAmount: values[headerMap.debitamount]?.trim() || '',\r\n        time: values[headerMap.time]?.trim() || '',\r\n        postDate: values[headerMap.postdate]?.trim() || '',\r\n        balance: values[headerMap.balance]?.trim() || ''\r\n      };\r\n      \r\n      dataRows.push(row);\r\n    }\r\n    \r\n    return dataRows;\r\n  }\r\n\r\n  private parseCSVLine(line: string): string[] {\r\n    const result: string[] = [];\r\n    let current = '';\r\n    let inQuotes = false;\r\n    \r\n    for (let i = 0; i < line.length; i++) {\r\n      const char = line[i];\r\n      \r\n      if (char === '\"') {\r\n        if (inQuotes && line[i + 1] === '\"') {\r\n          current += '\"';\r\n          i++; // Skip next quote\r\n        } else {\r\n          inQuotes = !inQuotes;\r\n        }\r\n      } else if (char === ',' && !inQuotes) {\r\n        result.push(current);\r\n        current = '';\r\n      } else {\r\n        current += char;\r\n      }\r\n    }\r\n    \r\n    result.push(current);\r\n    return result;\r\n  }\r\n\r\n  // Validate CSV data\r\n  validateCSVData(rows: CSVRow[]): ValidationError[] {\r\n    const errors: ValidationError[] = [];\r\n    \r\n    rows.forEach((row, index) => {\r\n      const rowNumber = index + 2; // +2 because index is 0-based and we skip header\r\n      \r\n      // Validate each field\r\n      CSV_TEMPLATE.validationRules.forEach(rule => {\r\n        const fieldValue = this.getFieldValue(row, rule.field);\r\n        const error = this.validateField(fieldValue, rule, rowNumber);\r\n        if (error) errors.push(error);\r\n      });\r\n      \r\n      // For bank statements, we trust the balance field as it represents the actual account balance\r\n      // Balance validation is disabled for bank statement imports as the data comes directly from the bank\r\n      // and may not be in strict chronological order or may include pending transactions\r\n    });\r\n    \r\n    return errors;\r\n  }\r\n\r\n  private getFieldValue(row: CSVRow, field: string): string {\r\n    switch (field.toLowerCase()) {\r\n      case 'bank reference': return row.bankReference;\r\n      case 'narrative': return row.narrative;\r\n      case 'customer reference': return row.customerReference;\r\n      case 'trn type': return row.trnType;\r\n      case 'value date': return row.valueDate;\r\n      case 'credit amount': return row.creditAmount;\r\n      case 'debit amount': return row.debitAmount;\r\n      case 'time': return row.time;\r\n      case 'post date': return row.postDate;\r\n      case 'balance': return row.balance;\r\n      default: return '';\r\n    }\r\n  }\r\n\r\n  private validateField(value: string, rule: ValidationRule, rowNumber: number): ValidationError | null {\r\n    switch (rule.rule) {\r\n      case 'required':\r\n        if (!value || value.trim() === '') {\r\n          return { row: rowNumber, field: rule.field, message: rule.message, value };\r\n        }\r\n        break;\r\n        \r\n      case 'number':\r\n        if (value.trim() && isNaN(this.parseAmount(value))) {\r\n          return { row: rowNumber, field: rule.field, message: rule.message, value };\r\n        }\r\n        break;\r\n        \r\n      case 'date':\r\n        if (value.trim() && !this.isValidDate(value)) {\r\n          return { row: rowNumber, field: rule.field, message: rule.message, value };\r\n        }\r\n        break;\r\n        \r\n      case 'positive':\r\n        if (value.trim() && this.parseAmount(value) < 0) {\r\n          return { row: rowNumber, field: rule.field, message: rule.message, value };\r\n        }\r\n        break;\r\n    }\r\n    \r\n    return null;\r\n  }\r\n\r\n  private parseAmount(value: string): number {\r\n    if (!value || value.trim() === '') return 0;\r\n    \r\n    // Remove currency symbols, commas, and quotes, handle negative values\r\n    const cleaned = value.replace(/[$,\\s\"]/g, '');\r\n    \r\n    // Handle negative values (with minus sign or parentheses)\r\n    const isNegative = cleaned.startsWith('-') || (cleaned.startsWith('(') && cleaned.endsWith(')'));\r\n    const numericValue = cleaned.replace(/[-()]/g, '');\r\n    \r\n    const amount = parseFloat(numericValue);\r\n    \r\n    if (isNaN(amount)) return 0;\r\n    \r\n    return isNegative ? -amount : amount;\r\n  }\r\n\r\n  private isValidDate(dateString: string): boolean {\r\n    // Handle DD/MM/YYYY format (Saudi bank format)\r\n    if (dateString.includes('/')) {\r\n      const parts = dateString.split('/');\r\n      if (parts.length === 3) {\r\n        const day = parseInt(parts[0]);\r\n        const month = parseInt(parts[1]);\r\n        const year = parseInt(parts[2]);\r\n        \r\n        // Basic range validation\r\n        if (day >= 1 && day <= 31 && month >= 1 && month <= 12 && year >= 1900 && year <= 2100) {\r\n          // Create date object to validate the actual date (handles leap years, etc.)\r\n          const date = new Date(year, month - 1, day);\r\n          return date.getFullYear() === year && \r\n                 date.getMonth() === month - 1 && \r\n                 date.getDate() === day;\r\n        }\r\n      }\r\n    }\r\n    \r\n    // Fallback to original logic\r\n    const date = new Date(dateString);\r\n    return date instanceof Date && !isNaN(date.getTime());\r\n  }\r\n\r\n  // Convert CSV rows to transactions\r\n  convertToTransactions(rows: CSVRow[]): Transaction[] {\r\n    return rows.map((row, index) => ({\r\n      id: `temp_${Date.now()}_${index}`,\r\n      date: this.formatDate(row.postDate),\r\n      description: row.narrative,\r\n      debitAmount: this.parseAmount(row.debitAmount),\r\n      creditAmount: this.parseAmount(row.creditAmount),\r\n      balance: this.parseAmount(row.balance),\r\n      reference: row.customerReference,\r\n      postDate: row.postDate,\r\n      time: row.time,\r\n      valueDate: row.valueDate\r\n    }));\r\n  }\r\n\r\n  private formatDate(dateString: string): string {\r\n    // Handle DD/MM/YYYY format from bank statements (Saudi bank format)\r\n    if (dateString.includes('/')) {\r\n      const parts = dateString.split('/');\r\n      if (parts.length === 3) {\r\n        const day = parts[0].padStart(2, '0');\r\n        const month = parts[1].padStart(2, '0');\r\n        const year = parts[2];\r\n        \r\n        // Validate the date parts\r\n        const dayNum = parseInt(day);\r\n        const monthNum = parseInt(month);\r\n        const yearNum = parseInt(year);\r\n        \r\n        if (dayNum >= 1 && dayNum <= 31 && monthNum >= 1 && monthNum <= 12 && yearNum >= 1900) {\r\n          // Convert DD/MM/YYYY to YYYY-MM-DD format\r\n          return `${year}-${month}-${day}`;\r\n        }\r\n      }\r\n    }\r\n    \r\n    // Fallback to original logic\r\n    const date = new Date(dateString);\r\n    return date.toISOString().split('T')[0];\r\n  }\r\n\r\n  // Helper method to create a sortable datetime from Post date and Time\r\n  private createSortableDateTime(postDate: string, time: string): Date {\r\n    const formattedDate = this.formatDate(postDate);\r\n    \r\n    // Handle time format (HH:MM)\r\n    let timeString = '00:00';\r\n    if (time && time.trim()) {\r\n      timeString = time.trim();\r\n      // Ensure time is in HH:MM format\r\n      if (timeString.length === 4 && !timeString.includes(':')) {\r\n        // Convert HHMM to HH:MM\r\n        timeString = timeString.substring(0, 2) + ':' + timeString.substring(2);\r\n      }\r\n    }\r\n    \r\n    // Combine date and time\r\n    const dateTimeString = `${formattedDate}T${timeString}:00`;\r\n    return new Date(dateTimeString);\r\n  }\r\n\r\n  // Process file and generate import summary\r\n  async processFile(file: File): Promise<ImportSummary> {\r\n    try {\r\n      const rows = await this.parseCSV(file);\r\n      const validationErrors = this.validateCSVData(rows);\r\n      const transactions = this.convertToTransactions(rows);\r\n      \r\n      // Sort transactions by Post date and time (newest first, matching bank statement order)\r\n      const sortedTransactions = transactions.sort((a, b) => {\r\n        const dateA = this.createSortableDateTime(a.postDate || a.date, a.time || '00:00');\r\n        const dateB = this.createSortableDateTime(b.postDate || b.date, b.time || '00:00');\r\n        return dateB.getTime() - dateA.getTime(); // Newest first (most recent transaction first)\r\n      });\r\n      \r\n      const totalDebitAmount = transactions.reduce((sum, t) => sum + t.debitAmount, 0);\r\n      const totalCreditAmount = transactions.reduce((sum, t) => sum + t.creditAmount, 0);\r\n      \r\n      // The closing balance is from the FIRST transaction (most recent) since bank statements are in reverse chronological order\r\n      const closingBalance = transactions.length > 0 ? transactions[0].balance : 0;\r\n      \r\n      // Calculate opening balance from the LAST transaction (oldest)\r\n      const openingBalance = transactions.length > 0 ? transactions[transactions.length - 1].balance : 0;\r\n      \r\n      // Calculate daily movement (net change)\r\n      const dailyMovement = closingBalance - openingBalance;\r\n      \r\n      return {\r\n        fileName: file.name,\r\n        totalTransactions: transactions.length,\r\n        totalDebitAmount,\r\n        totalCreditAmount,\r\n        closingBalance,\r\n        openingBalance,\r\n        dailyMovement,\r\n        validationErrors,\r\n        transactions: sortedTransactions,\r\n        dateRange: {\r\n          from: transactions.length > 0 ? sortedTransactions[sortedTransactions.length - 1].date : '',\r\n          to: transactions.length > 0 ? sortedTransactions[0].date : ''\r\n        }\r\n      };\r\n    } catch (error) {\r\n      throw new Error(`Failed to process ${file.name}: ${error instanceof Error ? error.message : 'Unknown error'}`);\r\n    }\r\n  }\r\n}\r\n\r\nexport const csvProcessingService = new CSVProcessingService(); "], "mappings": "AAEA;AACA,MAAO,MAAM,CAAAA,YAAyB,CAAG,CACvCC,OAAO,CAAE,CAAC,gBAAgB,CAAE,WAAW,CAAE,oBAAoB,CAAE,UAAU,CAAE,YAAY,CAAE,eAAe,CAAE,cAAc,CAAE,MAAM,CAAE,WAAW,CAAE,SAAS,CAAC,CACzJC,eAAe,CAAE,CACf,CAAEC,KAAK,CAAE,gBAAgB,CAAEC,IAAI,CAAE,UAAU,CAAEC,OAAO,CAAE,4BAA6B,CAAC,CACpF,CAAEF,KAAK,CAAE,WAAW,CAAEC,IAAI,CAAE,UAAU,CAAEC,OAAO,CAAE,uBAAwB,CAAC,CAC1E,CAAEF,KAAK,CAAE,UAAU,CAAEC,IAAI,CAAE,UAAU,CAAEC,OAAO,CAAE,8BAA+B,CAAC,CAChF,CAAEF,KAAK,CAAE,YAAY,CAAEC,IAAI,CAAE,UAAU,CAAEC,OAAO,CAAE,wBAAyB,CAAC,CAC5E,CAAEF,KAAK,CAAE,YAAY,CAAEC,IAAI,CAAE,MAAM,CAAEC,OAAO,CAAE,iDAAkD,CAAC,CACjG,CAAEF,KAAK,CAAE,WAAW,CAAEC,IAAI,CAAE,UAAU,CAAEC,OAAO,CAAE,uBAAwB,CAAC,CAC1E,CAAEF,KAAK,CAAE,WAAW,CAAEC,IAAI,CAAE,MAAM,CAAEC,OAAO,CAAE,gDAAiD,CAAC,CAC/F,CAAEF,KAAK,CAAE,eAAe,CAAEC,IAAI,CAAE,QAAQ,CAAEC,OAAO,CAAE,sCAAuC,CAAC,CAC3F,CAAEF,KAAK,CAAE,cAAc,CAAEC,IAAI,CAAE,QAAQ,CAAEC,OAAO,CAAE,qCAAsC,CAAC,CACzF,CAAEF,KAAK,CAAE,SAAS,CAAEC,IAAI,CAAE,UAAU,CAAEC,OAAO,CAAE,qBAAsB,CAAC,CACtE,CAAEF,KAAK,CAAE,SAAS,CAAEC,IAAI,CAAE,QAAQ,CAAEC,OAAO,CAAE,gCAAiC,CAAC,CAEhF,CACDC,UAAU,CAAE,CACV,CACE,gBAAgB,CAAE,cAAc,CAChC,WAAW,CAAE,4BAA4B,CACzC,oBAAoB,CAAE,iBAAiB,CACvC,UAAU,CAAE,UAAU,CACtB,YAAY,CAAE,YAAY,CAC1B,eAAe,CAAE,UAAU,CAC3B,cAAc,CAAE,EAAE,CAClB,MAAM,CAAE,OAAO,CACf,WAAW,CAAE,YAAY,CACzB,SAAS,CAAE,eACb,CAAC,CACD,CACE,gBAAgB,CAAE,cAAc,CAChC,WAAW,CAAE,kCAAkC,CAC/C,oBAAoB,CAAE,QAAQ,CAC9B,UAAU,CAAE,UAAU,CACtB,YAAY,CAAE,YAAY,CAC1B,eAAe,CAAE,EAAE,CACnB,cAAc,CAAE,eAAe,CAC/B,MAAM,CAAE,OAAO,CACf,WAAW,CAAE,YAAY,CACzB,SAAS,CAAE,eACb,CAAC,CACD,CACE,gBAAgB,CAAE,YAAY,CAC9B,WAAW,CAAE,+BAA+B,CAC5C,oBAAoB,CAAE,QAAQ,CAC9B,UAAU,CAAE,QAAQ,CACpB,YAAY,CAAE,YAAY,CAC1B,eAAe,CAAE,WAAW,CAC5B,cAAc,CAAE,EAAE,CAClB,MAAM,CAAE,OAAO,CACf,WAAW,CAAE,YAAY,CACzB,SAAS,CAAE,eACb,CAAC,CAEL,CAAC,CAED,KAAM,CAAAC,oBAAqB,CAEzB;AACAC,gBAAgBA,CAAA,CAAW,CACzB,KAAM,CAAAP,OAAO,CAAGD,YAAY,CAACC,OAAO,CAACQ,IAAI,CAAC,GAAG,CAAC,CAC9C,KAAM,CAAAC,IAAI,CAAGV,YAAY,CAACM,UAAU,CAACK,GAAG,CAACC,GAAG,EAC1CZ,YAAY,CAACC,OAAO,CAACU,GAAG,CAACE,MAAM,OAAAC,MAAA,CAAQF,GAAG,CAACC,MAAM,CAAC,EAAI,EAAE,MAAG,CAAC,CAACJ,IAAI,CAAC,GAAG,CACvE,CAAC,CAED,MAAO,CAACR,OAAO,CAAE,GAAGS,IAAI,CAAC,CAACD,IAAI,CAAC,IAAI,CAAC,CACtC,CAEA;AACAM,gBAAgBA,CAAA,CAAS,CACvB,KAAM,CAAAC,UAAU,CAAG,IAAI,CAACR,gBAAgB,CAAC,CAAC,CAC1C,KAAM,CAAAS,IAAI,CAAG,GAAI,CAAAC,IAAI,CAAC,CAACF,UAAU,CAAC,CAAE,CAAEG,IAAI,CAAE,yBAA0B,CAAC,CAAC,CACxE,KAAM,CAAAC,IAAI,CAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC,CAExC,GAAIF,IAAI,CAACG,QAAQ,GAAKC,SAAS,CAAE,CAC/B,KAAM,CAAAC,GAAG,CAAGC,GAAG,CAACC,eAAe,CAACV,IAAI,CAAC,CACrCG,IAAI,CAACQ,YAAY,CAAC,MAAM,CAAEH,GAAG,CAAC,CAC9BL,IAAI,CAACQ,YAAY,CAAC,UAAU,CAAE,6BAA6B,CAAC,CAC5DR,IAAI,CAACS,KAAK,CAACC,UAAU,CAAG,QAAQ,CAChCT,QAAQ,CAACU,IAAI,CAACC,WAAW,CAACZ,IAAI,CAAC,CAC/BA,IAAI,CAACa,KAAK,CAAC,CAAC,CACZZ,QAAQ,CAACU,IAAI,CAACG,WAAW,CAACd,IAAI,CAAC,CACjC,CACF,CAEA;AACA,KAAM,CAAAe,QAAQA,CAACC,IAAU,CAAqB,CAC5C,MAAO,IAAI,CAAAC,OAAO,CAAC,CAACC,OAAO,CAAEC,MAAM,GAAK,CACtC,KAAM,CAAAC,MAAM,CAAG,GAAI,CAAAC,UAAU,CAAC,CAAC,CAE/BD,MAAM,CAACE,MAAM,CAAIC,KAAK,EAAK,CACzB,GAAI,KAAAC,aAAA,CACF,KAAM,CAAAC,GAAG,EAAAD,aAAA,CAAGD,KAAK,CAACG,MAAM,UAAAF,aAAA,iBAAZA,aAAA,CAAcG,MAAgB,CAC1C,KAAM,CAAArC,IAAI,CAAG,IAAI,CAACsC,eAAe,CAACH,GAAG,CAAC,CACtCP,OAAO,CAAC5B,IAAI,CAAC,CACf,CAAE,MAAOuC,KAAK,CAAE,CACdV,MAAM,CAACU,KAAK,CAAC,CACf,CACF,CAAC,CAEDT,MAAM,CAACU,OAAO,CAAG,IAAMX,MAAM,CAAC,GAAI,CAAAY,KAAK,CAAC,qBAAqB,CAAC,CAAC,CAC/DX,MAAM,CAACY,UAAU,CAAChB,IAAI,CAAC,CACzB,CAAC,CAAC,CACJ,CAEQY,eAAeA,CAACH,GAAW,CAAY,CAC7C,KAAM,CAAAQ,KAAK,CAAGR,GAAG,CAACS,KAAK,CAAC,IAAI,CAAC,CAACC,MAAM,CAACC,IAAI,EAAIA,IAAI,CAACC,IAAI,CAAC,CAAC,CAAC,CACzD,GAAIJ,KAAK,CAACK,MAAM,CAAG,CAAC,CAAE,KAAM,IAAI,CAAAP,KAAK,CAAC,qDAAqD,CAAC,CAE5F,KAAM,CAAAlD,OAAO,CAAG,IAAI,CAAC0D,YAAY,CAACN,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC1C,GAAG,CAACiD,CAAC,EAAIA,CAAC,CAACH,IAAI,CAAC,CAAC,CAAC,CAC9D,KAAM,CAAAI,eAAe,CAAG,CACtB,gBAAgB,CAChB,WAAW,CACX,oBAAoB,CACpB,UAAU,CACV,YAAY,CACZ,eAAe,CACf,cAAc,CACd,MAAM,CACN,WAAW,CACX,SAAS,CACV,CAED;AACA,KAAM,CAAAC,iBAAiB,CAAG7D,OAAO,CAACU,GAAG,CAACiD,CAAC,EAAIA,CAAC,CAACG,WAAW,CAAC,CAAC,CAAC,CAC3D,KAAM,CAAAC,cAAc,CAAGH,eAAe,CAACN,MAAM,CAACK,CAAC,EAAI,CAACE,iBAAiB,CAACG,QAAQ,CAACL,CAAC,CAAC,CAAC,CAElF,GAAII,cAAc,CAACN,MAAM,CAAG,CAAC,CAAE,CAC7B,KAAM,IAAI,CAAAP,KAAK,8BAAArC,MAAA,CAA8BkD,cAAc,CAACvD,IAAI,CAAC,IAAI,CAAC,CAAE,CAAC,CAC3E,CAEA;AACA,KAAM,CAAAyD,SAAiC,CAAG,CAAC,CAAC,CAC5CL,eAAe,CAACM,OAAO,CAACC,cAAc,EAAI,CACxC,KAAM,CAAAC,KAAK,CAAGP,iBAAiB,CAACQ,SAAS,CAACV,CAAC,EAAIA,CAAC,GAAKQ,cAAc,CAAC,CACpEF,SAAS,CAACE,cAAc,CAACG,OAAO,CAAC,GAAG,CAAE,EAAE,CAAC,CAAC,CAAGF,KAAK,CACpD,CAAC,CAAC,CAEF;AACA,KAAM,CAAAG,QAAkB,CAAG,EAAE,CAC7B,IAAK,GAAI,CAAAC,CAAC,CAAG,CAAC,CAAEA,CAAC,CAAGpB,KAAK,CAACK,MAAM,CAAEe,CAAC,EAAE,CAAE,KAAAC,qBAAA,CAAAC,qBAAA,CAAAC,qBAAA,CAAAC,qBAAA,CAAAC,qBAAA,CAAAC,qBAAA,CAAAC,qBAAA,CAAAC,qBAAA,CAAAC,qBAAA,CAAAC,qBAAA,CACrC,KAAM,CAAAC,MAAM,CAAG,IAAI,CAACzB,YAAY,CAACN,KAAK,CAACoB,CAAC,CAAC,CAAC,CAC1C,GAAIW,MAAM,CAAC1B,MAAM,GAAK,CAAC,EAAI0B,MAAM,CAACC,KAAK,CAACC,CAAC,EAAI,CAACA,CAAC,CAAC7B,IAAI,CAAC,CAAC,CAAC,CAAE,SAAU;AAEnE,KAAM,CAAA7C,GAAW,CAAG,CAClB2E,aAAa,CAAE,EAAAb,qBAAA,CAAAU,MAAM,CAAClB,SAAS,CAACsB,aAAa,CAAC,UAAAd,qBAAA,iBAA/BA,qBAAA,CAAiCjB,IAAI,CAAC,CAAC,GAAI,EAAE,CAC5DgC,SAAS,CAAE,EAAAd,qBAAA,CAAAS,MAAM,CAAClB,SAAS,CAACuB,SAAS,CAAC,UAAAd,qBAAA,iBAA3BA,qBAAA,CAA6BlB,IAAI,CAAC,CAAC,GAAI,EAAE,CACpDiC,iBAAiB,CAAE,EAAAd,qBAAA,CAAAQ,MAAM,CAAClB,SAAS,CAACyB,iBAAiB,CAAC,UAAAf,qBAAA,iBAAnCA,qBAAA,CAAqCnB,IAAI,CAAC,CAAC,GAAI,EAAE,CACpEmC,OAAO,CAAE,EAAAf,qBAAA,CAAAO,MAAM,CAAClB,SAAS,CAAC2B,OAAO,CAAC,UAAAhB,qBAAA,iBAAzBA,qBAAA,CAA2BpB,IAAI,CAAC,CAAC,GAAI,EAAE,CAChDqC,SAAS,CAAE,EAAAhB,qBAAA,CAAAM,MAAM,CAAClB,SAAS,CAAC6B,SAAS,CAAC,UAAAjB,qBAAA,iBAA3BA,qBAAA,CAA6BrB,IAAI,CAAC,CAAC,GAAI,EAAE,CACpDuC,YAAY,CAAE,EAAAjB,qBAAA,CAAAK,MAAM,CAAClB,SAAS,CAAC+B,YAAY,CAAC,UAAAlB,qBAAA,iBAA9BA,qBAAA,CAAgCtB,IAAI,CAAC,CAAC,GAAI,EAAE,CAC1DyC,WAAW,CAAE,EAAAlB,qBAAA,CAAAI,MAAM,CAAClB,SAAS,CAACiC,WAAW,CAAC,UAAAnB,qBAAA,iBAA7BA,qBAAA,CAA+BvB,IAAI,CAAC,CAAC,GAAI,EAAE,CACxD2C,IAAI,CAAE,EAAAnB,qBAAA,CAAAG,MAAM,CAAClB,SAAS,CAACkC,IAAI,CAAC,UAAAnB,qBAAA,iBAAtBA,qBAAA,CAAwBxB,IAAI,CAAC,CAAC,GAAI,EAAE,CAC1C4C,QAAQ,CAAE,EAAAnB,qBAAA,CAAAE,MAAM,CAAClB,SAAS,CAACoC,QAAQ,CAAC,UAAApB,qBAAA,iBAA1BA,qBAAA,CAA4BzB,IAAI,CAAC,CAAC,GAAI,EAAE,CAClD8C,OAAO,CAAE,EAAApB,qBAAA,CAAAC,MAAM,CAAClB,SAAS,CAACqC,OAAO,CAAC,UAAApB,qBAAA,iBAAzBA,qBAAA,CAA2B1B,IAAI,CAAC,CAAC,GAAI,EAChD,CAAC,CAEDe,QAAQ,CAACgC,IAAI,CAAC5F,GAAG,CAAC,CACpB,CAEA,MAAO,CAAA4D,QAAQ,CACjB,CAEQb,YAAYA,CAACH,IAAY,CAAY,CAC3C,KAAM,CAAAT,MAAgB,CAAG,EAAE,CAC3B,GAAI,CAAA0D,OAAO,CAAG,EAAE,CAChB,GAAI,CAAAC,QAAQ,CAAG,KAAK,CAEpB,IAAK,GAAI,CAAAjC,CAAC,CAAG,CAAC,CAAEA,CAAC,CAAGjB,IAAI,CAACE,MAAM,CAAEe,CAAC,EAAE,CAAE,CACpC,KAAM,CAAAkC,IAAI,CAAGnD,IAAI,CAACiB,CAAC,CAAC,CAEpB,GAAIkC,IAAI,GAAK,GAAG,CAAE,CAChB,GAAID,QAAQ,EAAIlD,IAAI,CAACiB,CAAC,CAAG,CAAC,CAAC,GAAK,GAAG,CAAE,CACnCgC,OAAO,EAAI,GAAG,CACdhC,CAAC,EAAE,CAAE;AACP,CAAC,IAAM,CACLiC,QAAQ,CAAG,CAACA,QAAQ,CACtB,CACF,CAAC,IAAM,IAAIC,IAAI,GAAK,GAAG,EAAI,CAACD,QAAQ,CAAE,CACpC3D,MAAM,CAACyD,IAAI,CAACC,OAAO,CAAC,CACpBA,OAAO,CAAG,EAAE,CACd,CAAC,IAAM,CACLA,OAAO,EAAIE,IAAI,CACjB,CACF,CAEA5D,MAAM,CAACyD,IAAI,CAACC,OAAO,CAAC,CACpB,MAAO,CAAA1D,MAAM,CACf,CAEA;AACA6D,eAAeA,CAAClG,IAAc,CAAqB,CACjD,KAAM,CAAAmG,MAAyB,CAAG,EAAE,CAEpCnG,IAAI,CAACyD,OAAO,CAAC,CAACvD,GAAG,CAAEyD,KAAK,GAAK,CAC3B,KAAM,CAAAyC,SAAS,CAAGzC,KAAK,CAAG,CAAC,CAAE;AAE7B;AACArE,YAAY,CAACE,eAAe,CAACiE,OAAO,CAAC/D,IAAI,EAAI,CAC3C,KAAM,CAAA2G,UAAU,CAAG,IAAI,CAACC,aAAa,CAACpG,GAAG,CAAER,IAAI,CAACD,KAAK,CAAC,CACtD,KAAM,CAAA8C,KAAK,CAAG,IAAI,CAACgE,aAAa,CAACF,UAAU,CAAE3G,IAAI,CAAE0G,SAAS,CAAC,CAC7D,GAAI7D,KAAK,CAAE4D,MAAM,CAACL,IAAI,CAACvD,KAAK,CAAC,CAC/B,CAAC,CAAC,CAEF;AACA;AACA;AACF,CAAC,CAAC,CAEF,MAAO,CAAA4D,MAAM,CACf,CAEQG,aAAaA,CAACpG,GAAW,CAAET,KAAa,CAAU,CACxD,OAAQA,KAAK,CAAC4D,WAAW,CAAC,CAAC,EACzB,IAAK,gBAAgB,CAAE,MAAO,CAAAnD,GAAG,CAAC2E,aAAa,CAC/C,IAAK,WAAW,CAAE,MAAO,CAAA3E,GAAG,CAAC6E,SAAS,CACtC,IAAK,oBAAoB,CAAE,MAAO,CAAA7E,GAAG,CAAC8E,iBAAiB,CACvD,IAAK,UAAU,CAAE,MAAO,CAAA9E,GAAG,CAACgF,OAAO,CACnC,IAAK,YAAY,CAAE,MAAO,CAAAhF,GAAG,CAACkF,SAAS,CACvC,IAAK,eAAe,CAAE,MAAO,CAAAlF,GAAG,CAACoF,YAAY,CAC7C,IAAK,cAAc,CAAE,MAAO,CAAApF,GAAG,CAACsF,WAAW,CAC3C,IAAK,MAAM,CAAE,MAAO,CAAAtF,GAAG,CAACwF,IAAI,CAC5B,IAAK,WAAW,CAAE,MAAO,CAAAxF,GAAG,CAACyF,QAAQ,CACrC,IAAK,SAAS,CAAE,MAAO,CAAAzF,GAAG,CAAC2F,OAAO,CAClC,QAAS,MAAO,EAAE,CACpB,CACF,CAEQU,aAAaA,CAACC,KAAa,CAAE9G,IAAoB,CAAE0G,SAAiB,CAA0B,CACpG,OAAQ1G,IAAI,CAACA,IAAI,EACf,IAAK,UAAU,CACb,GAAI,CAAC8G,KAAK,EAAIA,KAAK,CAACzD,IAAI,CAAC,CAAC,GAAK,EAAE,CAAE,CACjC,MAAO,CAAE7C,GAAG,CAAEkG,SAAS,CAAE3G,KAAK,CAAEC,IAAI,CAACD,KAAK,CAAEE,OAAO,CAAED,IAAI,CAACC,OAAO,CAAE6G,KAAM,CAAC,CAC5E,CACA,MAEF,IAAK,QAAQ,CACX,GAAIA,KAAK,CAACzD,IAAI,CAAC,CAAC,EAAI0D,KAAK,CAAC,IAAI,CAACC,WAAW,CAACF,KAAK,CAAC,CAAC,CAAE,CAClD,MAAO,CAAEtG,GAAG,CAAEkG,SAAS,CAAE3G,KAAK,CAAEC,IAAI,CAACD,KAAK,CAAEE,OAAO,CAAED,IAAI,CAACC,OAAO,CAAE6G,KAAM,CAAC,CAC5E,CACA,MAEF,IAAK,MAAM,CACT,GAAIA,KAAK,CAACzD,IAAI,CAAC,CAAC,EAAI,CAAC,IAAI,CAAC4D,WAAW,CAACH,KAAK,CAAC,CAAE,CAC5C,MAAO,CAAEtG,GAAG,CAAEkG,SAAS,CAAE3G,KAAK,CAAEC,IAAI,CAACD,KAAK,CAAEE,OAAO,CAAED,IAAI,CAACC,OAAO,CAAE6G,KAAM,CAAC,CAC5E,CACA,MAEF,IAAK,UAAU,CACb,GAAIA,KAAK,CAACzD,IAAI,CAAC,CAAC,EAAI,IAAI,CAAC2D,WAAW,CAACF,KAAK,CAAC,CAAG,CAAC,CAAE,CAC/C,MAAO,CAAEtG,GAAG,CAAEkG,SAAS,CAAE3G,KAAK,CAAEC,IAAI,CAACD,KAAK,CAAEE,OAAO,CAAED,IAAI,CAACC,OAAO,CAAE6G,KAAM,CAAC,CAC5E,CACA,MACJ,CAEA,MAAO,KAAI,CACb,CAEQE,WAAWA,CAACF,KAAa,CAAU,CACzC,GAAI,CAACA,KAAK,EAAIA,KAAK,CAACzD,IAAI,CAAC,CAAC,GAAK,EAAE,CAAE,MAAO,EAAC,CAE3C;AACA,KAAM,CAAA6D,OAAO,CAAGJ,KAAK,CAAC3C,OAAO,CAAC,UAAU,CAAE,EAAE,CAAC,CAE7C;AACA,KAAM,CAAAgD,UAAU,CAAGD,OAAO,CAACE,UAAU,CAAC,GAAG,CAAC,EAAKF,OAAO,CAACE,UAAU,CAAC,GAAG,CAAC,EAAIF,OAAO,CAACG,QAAQ,CAAC,GAAG,CAAE,CAChG,KAAM,CAAAC,YAAY,CAAGJ,OAAO,CAAC/C,OAAO,CAAC,QAAQ,CAAE,EAAE,CAAC,CAElD,KAAM,CAAAoD,MAAM,CAAGC,UAAU,CAACF,YAAY,CAAC,CAEvC,GAAIP,KAAK,CAACQ,MAAM,CAAC,CAAE,MAAO,EAAC,CAE3B,MAAO,CAAAJ,UAAU,CAAG,CAACI,MAAM,CAAGA,MAAM,CACtC,CAEQN,WAAWA,CAACQ,UAAkB,CAAW,CAC/C;AACA,GAAIA,UAAU,CAAC5D,QAAQ,CAAC,GAAG,CAAC,CAAE,CAC5B,KAAM,CAAA6D,KAAK,CAAGD,UAAU,CAACvE,KAAK,CAAC,GAAG,CAAC,CACnC,GAAIwE,KAAK,CAACpE,MAAM,GAAK,CAAC,CAAE,CACtB,KAAM,CAAAqE,GAAG,CAAGC,QAAQ,CAACF,KAAK,CAAC,CAAC,CAAC,CAAC,CAC9B,KAAM,CAAAG,KAAK,CAAGD,QAAQ,CAACF,KAAK,CAAC,CAAC,CAAC,CAAC,CAChC,KAAM,CAAAI,IAAI,CAAGF,QAAQ,CAACF,KAAK,CAAC,CAAC,CAAC,CAAC,CAE/B;AACA,GAAIC,GAAG,EAAI,CAAC,EAAIA,GAAG,EAAI,EAAE,EAAIE,KAAK,EAAI,CAAC,EAAIA,KAAK,EAAI,EAAE,EAAIC,IAAI,EAAI,IAAI,EAAIA,IAAI,EAAI,IAAI,CAAE,CACtF;AACA,KAAM,CAAAC,IAAI,CAAG,GAAI,CAAAC,IAAI,CAACF,IAAI,CAAED,KAAK,CAAG,CAAC,CAAEF,GAAG,CAAC,CAC3C,MAAO,CAAAI,IAAI,CAACE,WAAW,CAAC,CAAC,GAAKH,IAAI,EAC3BC,IAAI,CAACG,QAAQ,CAAC,CAAC,GAAKL,KAAK,CAAG,CAAC,EAC7BE,IAAI,CAACI,OAAO,CAAC,CAAC,GAAKR,GAAG,CAC/B,CACF,CACF,CAEA;AACA,KAAM,CAAAI,IAAI,CAAG,GAAI,CAAAC,IAAI,CAACP,UAAU,CAAC,CACjC,MAAO,CAAAM,IAAI,WAAY,CAAAC,IAAI,EAAI,CAACjB,KAAK,CAACgB,IAAI,CAACK,OAAO,CAAC,CAAC,CAAC,CACvD,CAEA;AACAC,qBAAqBA,CAAC/H,IAAc,CAAiB,CACnD,MAAO,CAAAA,IAAI,CAACC,GAAG,CAAC,CAACC,GAAG,CAAEyD,KAAK,IAAM,CAC/BqE,EAAE,SAAA5H,MAAA,CAAUsH,IAAI,CAACO,GAAG,CAAC,CAAC,MAAA7H,MAAA,CAAIuD,KAAK,CAAE,CACjC8D,IAAI,CAAE,IAAI,CAACS,UAAU,CAAChI,GAAG,CAACyF,QAAQ,CAAC,CACnCwC,WAAW,CAAEjI,GAAG,CAAC6E,SAAS,CAC1BS,WAAW,CAAE,IAAI,CAACkB,WAAW,CAACxG,GAAG,CAACsF,WAAW,CAAC,CAC9CF,YAAY,CAAE,IAAI,CAACoB,WAAW,CAACxG,GAAG,CAACoF,YAAY,CAAC,CAChDO,OAAO,CAAE,IAAI,CAACa,WAAW,CAACxG,GAAG,CAAC2F,OAAO,CAAC,CACtCuC,SAAS,CAAElI,GAAG,CAAC8E,iBAAiB,CAChCW,QAAQ,CAAEzF,GAAG,CAACyF,QAAQ,CACtBD,IAAI,CAAExF,GAAG,CAACwF,IAAI,CACdN,SAAS,CAAElF,GAAG,CAACkF,SACjB,CAAC,CAAC,CAAC,CACL,CAEQ8C,UAAUA,CAACf,UAAkB,CAAU,CAC7C;AACA,GAAIA,UAAU,CAAC5D,QAAQ,CAAC,GAAG,CAAC,CAAE,CAC5B,KAAM,CAAA6D,KAAK,CAAGD,UAAU,CAACvE,KAAK,CAAC,GAAG,CAAC,CACnC,GAAIwE,KAAK,CAACpE,MAAM,GAAK,CAAC,CAAE,CACtB,KAAM,CAAAqE,GAAG,CAAGD,KAAK,CAAC,CAAC,CAAC,CAACiB,QAAQ,CAAC,CAAC,CAAE,GAAG,CAAC,CACrC,KAAM,CAAAd,KAAK,CAAGH,KAAK,CAAC,CAAC,CAAC,CAACiB,QAAQ,CAAC,CAAC,CAAE,GAAG,CAAC,CACvC,KAAM,CAAAb,IAAI,CAAGJ,KAAK,CAAC,CAAC,CAAC,CAErB;AACA,KAAM,CAAAkB,MAAM,CAAGhB,QAAQ,CAACD,GAAG,CAAC,CAC5B,KAAM,CAAAkB,QAAQ,CAAGjB,QAAQ,CAACC,KAAK,CAAC,CAChC,KAAM,CAAAiB,OAAO,CAAGlB,QAAQ,CAACE,IAAI,CAAC,CAE9B,GAAIc,MAAM,EAAI,CAAC,EAAIA,MAAM,EAAI,EAAE,EAAIC,QAAQ,EAAI,CAAC,EAAIA,QAAQ,EAAI,EAAE,EAAIC,OAAO,EAAI,IAAI,CAAE,CACrF;AACA,SAAApI,MAAA,CAAUoH,IAAI,MAAApH,MAAA,CAAImH,KAAK,MAAAnH,MAAA,CAAIiH,GAAG,EAChC,CACF,CACF,CAEA;AACA,KAAM,CAAAI,IAAI,CAAG,GAAI,CAAAC,IAAI,CAACP,UAAU,CAAC,CACjC,MAAO,CAAAM,IAAI,CAACgB,WAAW,CAAC,CAAC,CAAC7F,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CACzC,CAEA;AACQ8F,sBAAsBA,CAAC/C,QAAgB,CAAED,IAAY,CAAQ,CACnE,KAAM,CAAAiD,aAAa,CAAG,IAAI,CAACT,UAAU,CAACvC,QAAQ,CAAC,CAE/C;AACA,GAAI,CAAAiD,UAAU,CAAG,OAAO,CACxB,GAAIlD,IAAI,EAAIA,IAAI,CAAC3C,IAAI,CAAC,CAAC,CAAE,CACvB6F,UAAU,CAAGlD,IAAI,CAAC3C,IAAI,CAAC,CAAC,CACxB;AACA,GAAI6F,UAAU,CAAC5F,MAAM,GAAK,CAAC,EAAI,CAAC4F,UAAU,CAACrF,QAAQ,CAAC,GAAG,CAAC,CAAE,CACxD;AACAqF,UAAU,CAAGA,UAAU,CAACC,SAAS,CAAC,CAAC,CAAE,CAAC,CAAC,CAAG,GAAG,CAAGD,UAAU,CAACC,SAAS,CAAC,CAAC,CAAC,CACzE,CACF,CAEA;AACA,KAAM,CAAAC,cAAc,IAAA1I,MAAA,CAAMuI,aAAa,MAAAvI,MAAA,CAAIwI,UAAU,OAAK,CAC1D,MAAO,IAAI,CAAAlB,IAAI,CAACoB,cAAc,CAAC,CACjC,CAEA;AACA,KAAM,CAAAC,WAAWA,CAACrH,IAAU,CAA0B,CACpD,GAAI,CACF,KAAM,CAAA1B,IAAI,CAAG,KAAM,KAAI,CAACyB,QAAQ,CAACC,IAAI,CAAC,CACtC,KAAM,CAAAsH,gBAAgB,CAAG,IAAI,CAAC9C,eAAe,CAAClG,IAAI,CAAC,CACnD,KAAM,CAAAiJ,YAAY,CAAG,IAAI,CAAClB,qBAAqB,CAAC/H,IAAI,CAAC,CAErD;AACA,KAAM,CAAAkJ,kBAAkB,CAAGD,YAAY,CAACE,IAAI,CAAC,CAACC,CAAC,CAAEC,CAAC,GAAK,CACrD,KAAM,CAAAC,KAAK,CAAG,IAAI,CAACZ,sBAAsB,CAACU,CAAC,CAACzD,QAAQ,EAAIyD,CAAC,CAAC3B,IAAI,CAAE2B,CAAC,CAAC1D,IAAI,EAAI,OAAO,CAAC,CAClF,KAAM,CAAA6D,KAAK,CAAG,IAAI,CAACb,sBAAsB,CAACW,CAAC,CAAC1D,QAAQ,EAAI0D,CAAC,CAAC5B,IAAI,CAAE4B,CAAC,CAAC3D,IAAI,EAAI,OAAO,CAAC,CAClF,MAAO,CAAA6D,KAAK,CAACzB,OAAO,CAAC,CAAC,CAAGwB,KAAK,CAACxB,OAAO,CAAC,CAAC,CAAE;AAC5C,CAAC,CAAC,CAEF,KAAM,CAAA0B,gBAAgB,CAAGP,YAAY,CAACQ,MAAM,CAAC,CAACC,GAAG,CAAEC,CAAC,GAAKD,GAAG,CAAGC,CAAC,CAACnE,WAAW,CAAE,CAAC,CAAC,CAChF,KAAM,CAAAoE,iBAAiB,CAAGX,YAAY,CAACQ,MAAM,CAAC,CAACC,GAAG,CAAEC,CAAC,GAAKD,GAAG,CAAGC,CAAC,CAACrE,YAAY,CAAE,CAAC,CAAC,CAElF;AACA,KAAM,CAAAuE,cAAc,CAAGZ,YAAY,CAACjG,MAAM,CAAG,CAAC,CAAGiG,YAAY,CAAC,CAAC,CAAC,CAACpD,OAAO,CAAG,CAAC,CAE5E;AACA,KAAM,CAAAiE,cAAc,CAAGb,YAAY,CAACjG,MAAM,CAAG,CAAC,CAAGiG,YAAY,CAACA,YAAY,CAACjG,MAAM,CAAG,CAAC,CAAC,CAAC6C,OAAO,CAAG,CAAC,CAElG;AACA,KAAM,CAAAkE,aAAa,CAAGF,cAAc,CAAGC,cAAc,CAErD,MAAO,CACLE,QAAQ,CAAEtI,IAAI,CAACuI,IAAI,CACnBC,iBAAiB,CAAEjB,YAAY,CAACjG,MAAM,CACtCwG,gBAAgB,CAChBI,iBAAiB,CACjBC,cAAc,CACdC,cAAc,CACdC,aAAa,CACbf,gBAAgB,CAChBC,YAAY,CAAEC,kBAAkB,CAChCiB,SAAS,CAAE,CACTC,IAAI,CAAEnB,YAAY,CAACjG,MAAM,CAAG,CAAC,CAAGkG,kBAAkB,CAACA,kBAAkB,CAAClG,MAAM,CAAG,CAAC,CAAC,CAACyE,IAAI,CAAG,EAAE,CAC3F4C,EAAE,CAAEpB,YAAY,CAACjG,MAAM,CAAG,CAAC,CAAGkG,kBAAkB,CAAC,CAAC,CAAC,CAACzB,IAAI,CAAG,EAC7D,CACF,CAAC,CACH,CAAE,MAAOlF,KAAK,CAAE,CACd,KAAM,IAAI,CAAAE,KAAK,sBAAArC,MAAA,CAAsBsB,IAAI,CAACuI,IAAI,OAAA7J,MAAA,CAAKmC,KAAK,WAAY,CAAAE,KAAK,CAAGF,KAAK,CAAC5C,OAAO,CAAG,eAAe,CAAE,CAAC,CAChH,CACF,CACF,CAEA,MAAO,MAAM,CAAA2K,oBAAoB,CAAG,GAAI,CAAAzK,oBAAoB,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}