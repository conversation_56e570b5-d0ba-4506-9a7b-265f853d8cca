{"ast": null, "code": "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { SquaredDifference } from '../kernel_names';\nimport { mul } from '../ops/mul';\nimport { scalar } from '../ops/scalar';\nimport { sub } from '../ops/sub';\nexport const squaredDifferenceGradConfig = {\n  kernelName: SquaredDifference,\n  inputsToSave: ['a', 'b'],\n  gradFunc: (dy, saved) => {\n    const [a, b] = saved;\n    const two = scalar(2);\n    const derA = () => mul(dy, mul(two, sub(a, b)));\n    const derB = () => mul(dy, mul(two, sub(b, a)));\n    return {\n      a: derA,\n      b: derB\n    };\n  }\n};", "map": {"version": 3, "names": ["SquaredDifference", "mul", "scalar", "sub", "squaredDifferenceGradConfig", "kernelName", "inputsToSave", "grad<PERSON>unc", "dy", "saved", "a", "b", "two", "derA", "derB"], "sources": ["C:\\tfjs-core\\src\\gradients\\SquaredDifference_grad.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {SquaredDifference} from '../kernel_names';\nimport {GradConfig} from '../kernel_registry';\nimport {mul} from '../ops/mul';\nimport {scalar} from '../ops/scalar';\nimport {sub} from '../ops/sub';\nimport {Tensor} from '../tensor';\n\nexport const squaredDifferenceGradConfig: GradConfig = {\n  kernelName: SquaredDifference,\n  inputsToSave: ['a', 'b'],\n  gradFunc: (dy: Tensor, saved: Tensor[]) => {\n    const [a, b] = saved;\n    const two = scalar(2);\n    const derA = () => mul(dy, mul(two, sub(a, b)));\n    const derB = () => mul(dy, mul(two, sub(b, a)));\n    return {a: derA, b: derB};\n  }\n};\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAiBA,SAAQA,iBAAiB,QAAO,iBAAiB;AAEjD,SAAQC,GAAG,QAAO,YAAY;AAC9B,SAAQC,MAAM,QAAO,eAAe;AACpC,SAAQC,GAAG,QAAO,YAAY;AAG9B,OAAO,MAAMC,2BAA2B,GAAe;EACrDC,UAAU,EAAEL,iBAAiB;EAC7BM,YAAY,EAAE,CAAC,GAAG,EAAE,GAAG,CAAC;EACxBC,QAAQ,EAAEA,CAACC,EAAU,EAAEC,KAAe,KAAI;IACxC,MAAM,CAACC,CAAC,EAAEC,CAAC,CAAC,GAAGF,KAAK;IACpB,MAAMG,GAAG,GAAGV,MAAM,CAAC,CAAC,CAAC;IACrB,MAAMW,IAAI,GAAGA,CAAA,KAAMZ,GAAG,CAACO,EAAE,EAAEP,GAAG,CAACW,GAAG,EAAET,GAAG,CAACO,CAAC,EAAEC,CAAC,CAAC,CAAC,CAAC;IAC/C,MAAMG,IAAI,GAAGA,CAAA,KAAMb,GAAG,CAACO,EAAE,EAAEP,GAAG,CAACW,GAAG,EAAET,GAAG,CAACQ,CAAC,EAAED,CAAC,CAAC,CAAC,CAAC;IAC/C,OAAO;MAACA,CAAC,EAAEG,IAAI;MAAEF,CAAC,EAAEG;IAAI,CAAC;EAC3B;CACD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}