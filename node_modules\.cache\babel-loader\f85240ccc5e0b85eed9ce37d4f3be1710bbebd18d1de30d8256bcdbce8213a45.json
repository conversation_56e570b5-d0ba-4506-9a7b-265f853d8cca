{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.transformArguments = exports.FIRST_KEY_INDEX = void 0;\nconst _1 = require(\".\");\nexports.FIRST_KEY_INDEX = 1;\nfunction transformArguments(key, timestamp, value, options) {\n  const args = ['TS.ADD', key, (0, _1.transformTimestampArgument)(timestamp), value.toString()];\n  (0, _1.pushRetentionArgument)(args, options?.RETENTION);\n  (0, _1.pushEncodingArgument)(args, options?.ENCODING);\n  (0, _1.pushChunkSizeArgument)(args, options?.CHUNK_SIZE);\n  if (options?.ON_DUPLICATE) {\n    args.push('ON_DUPLICATE', options.ON_DUPLICATE);\n  }\n  (0, _1.pushLabelsArgument)(args, options?.LABELS);\n  (0, _1.pushIgnoreArgument)(args, options?.IGNORE);\n  return args;\n}\nexports.transformArguments = transformArguments;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "transformArguments", "FIRST_KEY_INDEX", "_1", "require", "key", "timestamp", "options", "args", "transformTimestampArgument", "toString", "pushRetentionArgument", "RETENTION", "pushEncodingArgument", "ENCODING", "pushChunkSizeArgument", "CHUNK_SIZE", "ON_DUPLICATE", "push", "pushLabelsArgument", "LABELS", "pushIgnoreArgument", "IGNORE"], "sources": ["C:/tmsft/node_modules/@redis/time-series/dist/commands/ADD.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.transformArguments = exports.FIRST_KEY_INDEX = void 0;\nconst _1 = require(\".\");\nexports.FIRST_KEY_INDEX = 1;\nfunction transformArguments(key, timestamp, value, options) {\n    const args = [\n        'TS.ADD',\n        key,\n        (0, _1.transformTimestampArgument)(timestamp),\n        value.toString()\n    ];\n    (0, _1.pushRetentionArgument)(args, options?.RETENTION);\n    (0, _1.pushEncodingArgument)(args, options?.ENCODING);\n    (0, _1.pushChunkSizeArgument)(args, options?.CHUNK_SIZE);\n    if (options?.ON_DUPLICATE) {\n        args.push('ON_DUPLICATE', options.ON_DUPLICATE);\n    }\n    (0, _1.pushLabelsArgument)(args, options?.LABELS);\n    (0, _1.pushIgnoreArgument)(args, options?.IGNORE);\n    return args;\n}\nexports.transformArguments = transformArguments;\n"], "mappings": "AAAA,YAAY;;AACZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEC,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7DD,OAAO,CAACE,kBAAkB,GAAGF,OAAO,CAACG,eAAe,GAAG,KAAK,CAAC;AAC7D,MAAMC,EAAE,GAAGC,OAAO,CAAC,GAAG,CAAC;AACvBL,OAAO,CAACG,eAAe,GAAG,CAAC;AAC3B,SAASD,kBAAkBA,CAACI,GAAG,EAAEC,SAAS,EAAEN,KAAK,EAAEO,OAAO,EAAE;EACxD,MAAMC,IAAI,GAAG,CACT,QAAQ,EACRH,GAAG,EACH,CAAC,CAAC,EAAEF,EAAE,CAACM,0BAA0B,EAAEH,SAAS,CAAC,EAC7CN,KAAK,CAACU,QAAQ,CAAC,CAAC,CACnB;EACD,CAAC,CAAC,EAAEP,EAAE,CAACQ,qBAAqB,EAAEH,IAAI,EAAED,OAAO,EAAEK,SAAS,CAAC;EACvD,CAAC,CAAC,EAAET,EAAE,CAACU,oBAAoB,EAAEL,IAAI,EAAED,OAAO,EAAEO,QAAQ,CAAC;EACrD,CAAC,CAAC,EAAEX,EAAE,CAACY,qBAAqB,EAAEP,IAAI,EAAED,OAAO,EAAES,UAAU,CAAC;EACxD,IAAIT,OAAO,EAAEU,YAAY,EAAE;IACvBT,IAAI,CAACU,IAAI,CAAC,cAAc,EAAEX,OAAO,CAACU,YAAY,CAAC;EACnD;EACA,CAAC,CAAC,EAAEd,EAAE,CAACgB,kBAAkB,EAAEX,IAAI,EAAED,OAAO,EAAEa,MAAM,CAAC;EACjD,CAAC,CAAC,EAAEjB,EAAE,CAACkB,kBAAkB,EAAEb,IAAI,EAAED,OAAO,EAAEe,MAAM,CAAC;EACjD,OAAOd,IAAI;AACf;AACAT,OAAO,CAACE,kBAAkB,GAAGA,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}