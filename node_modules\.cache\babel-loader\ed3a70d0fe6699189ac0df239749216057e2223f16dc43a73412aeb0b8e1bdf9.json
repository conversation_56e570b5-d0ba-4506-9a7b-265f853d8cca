{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.transformReply = exports.transformArguments = void 0;\nfunction transformArguments() {\n  return ['TIME'];\n}\nexports.transformArguments = transformArguments;\nfunction transformReply(reply) {\n  const seconds = Number(reply[0]),\n    microseconds = Number(reply[1]),\n    d = new Date(seconds * 1000 + microseconds / 1000);\n  d.microseconds = microseconds;\n  return d;\n}\nexports.transformReply = transformReply;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "transformReply", "transformArguments", "reply", "seconds", "Number", "microseconds", "d", "Date"], "sources": ["C:/tmsft/node_modules/@redis/client/dist/lib/commands/TIME.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.transformReply = exports.transformArguments = void 0;\nfunction transformArguments() {\n    return ['TIME'];\n}\nexports.transformArguments = transformArguments;\nfunction transformReply(reply) {\n    const seconds = Number(reply[0]), microseconds = Number(reply[1]), d = new Date(seconds * 1000 + microseconds / 1000);\n    d.microseconds = microseconds;\n    return d;\n}\nexports.transformReply = transformReply;\n"], "mappings": "AAAA,YAAY;;AACZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEC,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7DD,OAAO,CAACE,cAAc,GAAGF,OAAO,CAACG,kBAAkB,GAAG,KAAK,CAAC;AAC5D,SAASA,kBAAkBA,CAAA,EAAG;EAC1B,OAAO,CAAC,MAAM,CAAC;AACnB;AACAH,OAAO,CAACG,kBAAkB,GAAGA,kBAAkB;AAC/C,SAASD,cAAcA,CAACE,KAAK,EAAE;EAC3B,MAAMC,OAAO,GAAGC,MAAM,CAACF,KAAK,CAAC,CAAC,CAAC,CAAC;IAAEG,YAAY,GAAGD,MAAM,CAACF,KAAK,CAAC,CAAC,CAAC,CAAC;IAAEI,CAAC,GAAG,IAAIC,IAAI,CAACJ,OAAO,GAAG,IAAI,GAAGE,YAAY,GAAG,IAAI,CAAC;EACrHC,CAAC,CAACD,YAAY,GAAGA,YAAY;EAC7B,OAAOC,CAAC;AACZ;AACAR,OAAO,CAACE,cAAc,GAAGA,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}