{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.transformArguments = void 0;\nfunction transformArguments() {\n  return ['CLUSTER', 'SAVECONFIG'];\n}\nexports.transformArguments = transformArguments;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "transformArguments"], "sources": ["C:/tmsft/node_modules/@redis/client/dist/lib/commands/CLUSTER_SAVECONFIG.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.transformArguments = void 0;\nfunction transformArguments() {\n    return ['CLUSTER', 'SAVECONFIG'];\n}\nexports.transformArguments = transformArguments;\n"], "mappings": "AAAA,YAAY;;AACZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEC,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7DD,OAAO,CAACE,kBAAkB,GAAG,KAAK,CAAC;AACnC,SAASA,kBAAkBA,CAAA,EAAG;EAC1B,OAAO,CAAC,SAAS,EAAE,YAAY,CAAC;AACpC;AACAF,OAAO,CAACE,kBAAkB,GAAGA,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}