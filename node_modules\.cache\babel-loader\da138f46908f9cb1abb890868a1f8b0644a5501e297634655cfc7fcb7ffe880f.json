{"ast": null, "code": "/*\nCopyright (c) 2011, <PERSON><PERSON>\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in\nall copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\nTHE SOFTWARE.\n*/\n\n'use strict';\n\n// a list of commonly used words that have little meaning and can be excluded\n// from analysis.\nconst words = ['a', 'à', 'ao', 'aos', 'aquela', 'aquelas', 'aquele', 'aqueles', 'aquilo', 'as', 'às', 'até', 'com', 'como', 'da', 'das', 'de', 'dela', 'delas', 'dele', 'deles', 'depois', 'do', 'dos', 'e', 'ela', 'elas', 'ele', 'eles', 'em', 'entre', 'essa', 'essas', 'esse', 'esses', 'esta', 'estas', 'este', 'estes', 'eu', 'isso', 'isto', 'já', 'lhe', 'lhes', 'mais', 'mas', 'me', 'mesmo', 'meu', 'meus', 'minha', 'minhas', 'muito', 'muitos', 'na', 'não', 'nas', 'nem', 'no', 'nos', 'nós', 'nossa', 'nossas', 'nosso', 'nossos', 'num', 'nuns', 'numa', 'numas', 'o', 'os', 'ou', 'para', 'pela', 'pelas', 'pelo', 'pelos', 'por', 'quais', 'qual', 'quando', 'que', 'quem', 'se', 'sem', 'seu', 'seus', 'só', 'sua', 'suas', 'também', 'te', 'teu', 'teus', 'tu', 'tua', 'tuas', 'um', 'uma', 'umas', 'você', 'vocês', 'vos', 'vosso', 'vossos', '1', '2', '3', '4', '5', '6', '7', '8', '9', '0', '_'];\n\n// tell the world about the noise words.\nexports.words = words;", "map": {"version": 3, "names": ["words", "exports"], "sources": ["C:/tmsft/node_modules/natural/lib/natural/util/stopwords_pt.js"], "sourcesContent": ["/*\nCopyright (c) 2011, <PERSON><PERSON>\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in\nall copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\nTHE SOFTWARE.\n*/\n\n'use strict'\n\n// a list of commonly used words that have little meaning and can be excluded\n// from analysis.\nconst words = [\n  'a',\n  'à',\n  'ao',\n  'aos',\n  'aquela',\n  'aquelas',\n  'aquele',\n  'aqueles',\n  'aquilo',\n  'as',\n  'às',\n  'até',\n  'com',\n  'como',\n  'da',\n  'das',\n  'de',\n  'dela',\n  'delas',\n  'dele',\n  'deles',\n  'depois',\n  'do',\n  'dos',\n  'e',\n  'ela',\n  'elas',\n  'ele',\n  'eles',\n  'em',\n  'entre',\n  'essa',\n  'essas',\n  'esse',\n  'esses',\n  'esta',\n  'estas',\n  'este',\n  'estes',\n  'eu',\n  'isso',\n  'isto',\n  'já',\n  'lhe',\n  'lhes',\n  'mais',\n  'mas',\n  'me',\n  'mesmo',\n  'meu',\n  'meus',\n  'minha',\n  'minhas',\n  'muito',\n  'muitos',\n  'na',\n  'não',\n  'nas',\n  'nem',\n  'no',\n  'nos',\n  'nós',\n  'nossa',\n  'nossas',\n  'nosso',\n  'nossos',\n  'num',\n  'nuns',\n  'numa',\n  'numas',\n  'o',\n  'os',\n  'ou',\n  'para',\n  'pela',\n  'pelas',\n  'pelo',\n  'pelos',\n  'por',\n  'quais',\n  'qual',\n  'quando',\n  'que',\n  'quem',\n  'se',\n  'sem',\n  'seu',\n  'seus',\n  'só',\n  'sua',\n  'suas',\n  'também',\n  'te',\n  'teu',\n  'teus',\n  'tu',\n  'tua',\n  'tuas',\n  'um',\n  'uma',\n  'umas',\n  'você',\n  'vocês',\n  'vos',\n  'vosso',\n  'vossos',\n  '1', '2', '3', '4', '5', '6', '7', '8', '9', '0', '_'\n]\n\n// tell the world about the noise words.\nexports.words = words\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,YAAY;;AAEZ;AACA;AACA,MAAMA,KAAK,GAAG,CACZ,GAAG,EACH,GAAG,EACH,IAAI,EACJ,KAAK,EACL,QAAQ,EACR,SAAS,EACT,QAAQ,EACR,SAAS,EACT,QAAQ,EACR,IAAI,EACJ,IAAI,EACJ,KAAK,EACL,KAAK,EACL,MAAM,EACN,IAAI,EACJ,KAAK,EACL,IAAI,EACJ,MAAM,EACN,OAAO,EACP,MAAM,EACN,OAAO,EACP,QAAQ,EACR,IAAI,EACJ,KAAK,EACL,GAAG,EACH,KAAK,EACL,MAAM,EACN,KAAK,EACL,MAAM,EACN,IAAI,EACJ,OAAO,EACP,MAAM,EACN,OAAO,EACP,MAAM,EACN,OAAO,EACP,MAAM,EACN,OAAO,EACP,MAAM,EACN,OAAO,EACP,IAAI,EACJ,MAAM,EACN,MAAM,EACN,IAAI,EACJ,KAAK,EACL,MAAM,EACN,MAAM,EACN,KAAK,EACL,IAAI,EACJ,OAAO,EACP,KAAK,EACL,MAAM,EACN,OAAO,EACP,QAAQ,EACR,OAAO,EACP,QAAQ,EACR,IAAI,EACJ,KAAK,EACL,KAAK,EACL,KAAK,EACL,IAAI,EACJ,KAAK,EACL,KAAK,EACL,OAAO,EACP,QAAQ,EACR,OAAO,EACP,QAAQ,EACR,KAAK,EACL,MAAM,EACN,MAAM,EACN,OAAO,EACP,GAAG,EACH,IAAI,EACJ,IAAI,EACJ,MAAM,EACN,MAAM,EACN,OAAO,EACP,MAAM,EACN,OAAO,EACP,KAAK,EACL,OAAO,EACP,MAAM,EACN,QAAQ,EACR,KAAK,EACL,MAAM,EACN,IAAI,EACJ,KAAK,EACL,KAAK,EACL,MAAM,EACN,IAAI,EACJ,KAAK,EACL,MAAM,EACN,QAAQ,EACR,IAAI,EACJ,KAAK,EACL,MAAM,EACN,IAAI,EACJ,KAAK,EACL,MAAM,EACN,IAAI,EACJ,KAAK,EACL,MAAM,EACN,MAAM,EACN,OAAO,EACP,KAAK,EACL,OAAO,EACP,QAAQ,EACR,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CACtD;;AAED;AACAC,OAAO,CAACD,KAAK,GAAGA,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}