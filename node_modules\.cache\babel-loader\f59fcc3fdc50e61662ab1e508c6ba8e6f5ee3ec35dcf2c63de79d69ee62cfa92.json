{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.transformReply = exports.transformArguments = void 0;\nfunction transformArguments(nodeId) {\n  return ['CLUSTER', 'REPLICAS', nodeId];\n}\nexports.transformArguments = transformArguments;\nvar CLUSTER_NODES_1 = require(\"./CLUSTER_NODES\");\nObject.defineProperty(exports, \"transformReply\", {\n  enumerable: true,\n  get: function () {\n    return CLUSTER_NODES_1.transformReply;\n  }\n});", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "transformReply", "transformArguments", "nodeId", "CLUSTER_NODES_1", "require", "enumerable", "get"], "sources": ["C:/tmsft/node_modules/@redis/client/dist/lib/commands/CLUSTER_REPLICAS.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.transformReply = exports.transformArguments = void 0;\nfunction transformArguments(nodeId) {\n    return ['CLUSTER', 'REPLICAS', nodeId];\n}\nexports.transformArguments = transformArguments;\nvar CLUSTER_NODES_1 = require(\"./CLUSTER_NODES\");\nObject.defineProperty(exports, \"transformReply\", { enumerable: true, get: function () { return CLUSTER_NODES_1.transformReply; } });\n"], "mappings": "AAAA,YAAY;;AACZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEC,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7DD,OAAO,CAACE,cAAc,GAAGF,OAAO,CAACG,kBAAkB,GAAG,KAAK,CAAC;AAC5D,SAASA,kBAAkBA,CAACC,MAAM,EAAE;EAChC,OAAO,CAAC,SAAS,EAAE,UAAU,EAAEA,MAAM,CAAC;AAC1C;AACAJ,OAAO,CAACG,kBAAkB,GAAGA,kBAAkB;AAC/C,IAAIE,eAAe,GAAGC,OAAO,CAAC,iBAAiB,CAAC;AAChDR,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,gBAAgB,EAAE;EAAEO,UAAU,EAAE,IAAI;EAAEC,GAAG,EAAE,SAAAA,CAAA,EAAY;IAAE,OAAOH,eAAe,CAACH,cAAc;EAAE;AAAE,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}