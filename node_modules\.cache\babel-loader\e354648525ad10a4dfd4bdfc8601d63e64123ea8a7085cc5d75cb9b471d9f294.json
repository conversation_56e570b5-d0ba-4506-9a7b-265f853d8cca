{"ast": null, "code": "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n// TODO update import path once op is modularized.\nimport { slice } from '../../ops/ops';\nimport { getGlobalTensorClass } from '../../tensor';\ngetGlobalTensorClass().prototype.slice = function (begin, size) {\n  this.throwIfDisposed();\n  return slice(this, begin, size);\n};", "map": {"version": 3, "names": ["slice", "getGlobalTensorClass", "prototype", "begin", "size", "throwIfDisposed"], "sources": ["C:\\tfjs-core\\src\\public\\chained_ops\\slice.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\n// TODO update import path once op is modularized.\nimport {slice} from '../../ops/ops';\nimport {getGlobalTensorClass, Tensor} from '../../tensor';\nimport {Rank} from '../../types';\n\ndeclare module '../../tensor' {\n  interface Tensor<R extends Rank = Rank> {\n    slice<T extends Tensor>(\n        this: T, begin: number|number[], size?: number|number[]): T;\n  }\n}\n\ngetGlobalTensorClass().prototype.slice = function<T extends Tensor>(\n    this: T, begin: number|number[], size?: number|number[]): T {\n  this.throwIfDisposed();\n  return slice(this, begin, size);\n};\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAiBA;AACA,SAAQA,KAAK,QAAO,eAAe;AACnC,SAAQC,oBAAoB,QAAe,cAAc;AAUzDA,oBAAoB,EAAE,CAACC,SAAS,CAACF,KAAK,GAAG,UAC5BG,KAAsB,EAAEC,IAAsB;EACzD,IAAI,CAACC,eAAe,EAAE;EACtB,OAAOL,KAAK,CAAC,IAAI,EAAEG,KAAK,EAAEC,IAAI,CAAC;AACjC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}