{"ast": null, "code": "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { backend_util, kernel_impls, NonMaxSuppressionV5 } from '@tensorflow/tfjs-core';\nconst nonMaxSuppressionV5Impl = kernel_impls.nonMaxSuppressionV5Impl;\nexport function nonMaxSuppressionV5(args) {\n  backend_util.warn('tf.nonMaxSuppression() in webgl locks the UI thread. ' + 'Call tf.nonMaxSuppressionAsync() instead');\n  const {\n    inputs,\n    backend,\n    attrs\n  } = args;\n  const {\n    boxes,\n    scores\n  } = inputs;\n  const {\n    maxOutputSize,\n    iouThreshold,\n    scoreThreshold,\n    softNmsSigma\n  } = attrs;\n  const boxesVals = backend.readSync(boxes.dataId);\n  const scoresVals = backend.readSync(scores.dataId);\n  const maxOutputSizeVal = maxOutputSize;\n  const iouThresholdVal = iouThreshold;\n  const scoreThresholdVal = scoreThreshold;\n  const softNmsSigmaVal = softNmsSigma;\n  const {\n    selectedIndices,\n    selectedScores\n  } = nonMaxSuppressionV5Impl(boxesVals, scoresVals, maxOutputSizeVal, iouThresholdVal, scoreThresholdVal, softNmsSigmaVal);\n  return [backend.makeTensorInfo([selectedIndices.length], 'int32', new Int32Array(selectedIndices)), backend.makeTensorInfo([selectedScores.length], 'float32', new Float32Array(selectedScores))];\n}\nexport const nonMaxSuppressionV5Config = {\n  kernelName: NonMaxSuppressionV5,\n  backendName: 'webgl',\n  kernelFunc: nonMaxSuppressionV5\n};", "map": {"version": 3, "names": ["backend_util", "kernel_impls", "NonMaxSuppressionV5", "nonMaxSuppressionV5Impl", "nonMaxSuppressionV5", "args", "warn", "inputs", "backend", "attrs", "boxes", "scores", "maxOutputSize", "iouThreshold", "scoreThreshold", "softNmsSigma", "boxesVals", "readSync", "dataId", "scoresVals", "maxOutputSizeVal", "iouThresholdVal", "scoreThresholdVal", "softNmsSigmaVal", "selectedIndices", "selectedScores", "makeTensorInfo", "length", "Int32Array", "Float32Array", "nonMaxSuppressionV5Config", "kernelName", "backendName", "kernelFunc"], "sources": ["C:\\tfjs-backend-webgl\\src\\kernels\\NonMaxSuppressionV5.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {backend_util, kernel_impls, KernelConfig, KernelFunc, NonMaxSuppressionV5, NonMaxSuppressionV5Attrs, NonMaxSuppressionV5Inputs, TensorInfo, TypedArray} from '@tensorflow/tfjs-core';\n\nconst nonMaxSuppressionV5Impl = kernel_impls.nonMaxSuppressionV5Impl;\nimport {MathBackendWebGL} from '../backend_webgl';\n\nexport function nonMaxSuppressionV5(args: {\n  inputs: NonMaxSuppressionV5Inputs,\n  backend: MathBackendWebGL,\n  attrs: NonMaxSuppressionV5Attrs\n}): [TensorInfo, TensorInfo] {\n  backend_util.warn(\n      'tf.nonMaxSuppression() in webgl locks the UI thread. ' +\n      'Call tf.nonMaxSuppressionAsync() instead');\n\n  const {inputs, backend, attrs} = args;\n  const {boxes, scores} = inputs;\n  const {maxOutputSize, iouThreshold, scoreThreshold, softNmsSigma} = attrs;\n\n  const boxesVals = backend.readSync(boxes.dataId) as TypedArray;\n  const scoresVals = backend.readSync(scores.dataId) as TypedArray;\n\n  const maxOutputSizeVal = maxOutputSize;\n  const iouThresholdVal = iouThreshold;\n  const scoreThresholdVal = scoreThreshold;\n  const softNmsSigmaVal = softNmsSigma;\n\n  const {selectedIndices, selectedScores} = nonMaxSuppressionV5Impl(\n      boxesVals, scoresVals, maxOutputSizeVal, iouThresholdVal,\n      scoreThresholdVal, softNmsSigmaVal);\n\n  return [\n    backend.makeTensorInfo(\n        [selectedIndices.length], 'int32', new Int32Array(selectedIndices)),\n    backend.makeTensorInfo(\n        [selectedScores.length], 'float32', new Float32Array(selectedScores))\n  ];\n}\n\nexport const nonMaxSuppressionV5Config: KernelConfig = {\n  kernelName: NonMaxSuppressionV5,\n  backendName: 'webgl',\n  kernelFunc: nonMaxSuppressionV5 as unknown as KernelFunc\n};\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAiBA,SAAQA,YAAY,EAAEC,YAAY,EAA4BC,mBAAmB,QAAoF,uBAAuB;AAE5L,MAAMC,uBAAuB,GAAGF,YAAY,CAACE,uBAAuB;AAGpE,OAAM,SAAUC,mBAAmBA,CAACC,IAInC;EACCL,YAAY,CAACM,IAAI,CACb,uDAAuD,GACvD,0CAA0C,CAAC;EAE/C,MAAM;IAACC,MAAM;IAAEC,OAAO;IAAEC;EAAK,CAAC,GAAGJ,IAAI;EACrC,MAAM;IAACK,KAAK;IAAEC;EAAM,CAAC,GAAGJ,MAAM;EAC9B,MAAM;IAACK,aAAa;IAAEC,YAAY;IAAEC,cAAc;IAAEC;EAAY,CAAC,GAAGN,KAAK;EAEzE,MAAMO,SAAS,GAAGR,OAAO,CAACS,QAAQ,CAACP,KAAK,CAACQ,MAAM,CAAe;EAC9D,MAAMC,UAAU,GAAGX,OAAO,CAACS,QAAQ,CAACN,MAAM,CAACO,MAAM,CAAe;EAEhE,MAAME,gBAAgB,GAAGR,aAAa;EACtC,MAAMS,eAAe,GAAGR,YAAY;EACpC,MAAMS,iBAAiB,GAAGR,cAAc;EACxC,MAAMS,eAAe,GAAGR,YAAY;EAEpC,MAAM;IAACS,eAAe;IAAEC;EAAc,CAAC,GAAGtB,uBAAuB,CAC7Da,SAAS,EAAEG,UAAU,EAAEC,gBAAgB,EAAEC,eAAe,EACxDC,iBAAiB,EAAEC,eAAe,CAAC;EAEvC,OAAO,CACLf,OAAO,CAACkB,cAAc,CAClB,CAACF,eAAe,CAACG,MAAM,CAAC,EAAE,OAAO,EAAE,IAAIC,UAAU,CAACJ,eAAe,CAAC,CAAC,EACvEhB,OAAO,CAACkB,cAAc,CAClB,CAACD,cAAc,CAACE,MAAM,CAAC,EAAE,SAAS,EAAE,IAAIE,YAAY,CAACJ,cAAc,CAAC,CAAC,CAC1E;AACH;AAEA,OAAO,MAAMK,yBAAyB,GAAiB;EACrDC,UAAU,EAAE7B,mBAAmB;EAC/B8B,WAAW,EAAE,OAAO;EACpBC,UAAU,EAAE7B;CACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}