{"ast": null, "code": "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { ZerosLike } from '@tensorflow/tfjs-core';\nimport { complex } from './Complex';\nimport { fill } from './Fill';\nimport { imag } from './Imag';\nimport { real } from './Real';\nexport function zerosLike(args) {\n  const {\n    inputs,\n    backend\n  } = args;\n  const {\n    x\n  } = inputs;\n  if (x.dtype === 'complex64') {\n    const realPart = real({\n      inputs: {\n        input: x\n      },\n      backend\n    });\n    const r = zerosLike({\n      inputs: {\n        x: realPart\n      },\n      backend\n    });\n    const imagPart = imag({\n      inputs: {\n        input: x\n      },\n      backend\n    });\n    const i = zerosLike({\n      inputs: {\n        x: imagPart\n      },\n      backend\n    });\n    const result = complex({\n      inputs: {\n        real: r,\n        imag: i\n      },\n      backend\n    });\n    backend.disposeIntermediateTensorInfo(realPart);\n    backend.disposeIntermediateTensorInfo(r);\n    backend.disposeIntermediateTensorInfo(imagPart);\n    backend.disposeIntermediateTensorInfo(i);\n    return result;\n  } else {\n    return fill({\n      attrs: {\n        shape: x.shape,\n        dtype: x.dtype,\n        value: x.dtype === 'string' ? '' : 0\n      },\n      backend\n    });\n  }\n}\nexport const zerosLikeConfig = {\n  kernelName: ZerosLike,\n  backendName: 'webgl',\n  kernelFunc: zerosLike\n};", "map": {"version": 3, "names": ["ZerosLike", "complex", "fill", "imag", "real", "zerosLike", "args", "inputs", "backend", "x", "dtype", "realPart", "input", "r", "imagPart", "i", "result", "disposeIntermediateTensorInfo", "attrs", "shape", "value", "zerosLikeConfig", "kernelName", "backendName", "kernelFunc"], "sources": ["C:\\tfjs-backend-webgl\\src\\kernels\\ZerosLike.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {KernelConfig, KernelFunc, TensorInfo, ZerosLike, ZerosLikeInputs} from '@tensorflow/tfjs-core';\n\nimport {MathBackendWebGL} from '../backend_webgl';\n\nimport {complex} from './Complex';\nimport {fill} from './Fill';\nimport {imag} from './Imag';\nimport {real} from './Real';\n\nexport function zerosLike(\n    args: {inputs: ZerosLikeInputs, backend: MathBackendWebGL}): TensorInfo {\n  const {inputs, backend} = args;\n  const {x} = inputs;\n\n  if (x.dtype === 'complex64') {\n    const realPart = real({inputs: {input: x}, backend});\n    const r = zerosLike({inputs: {x: realPart}, backend});\n    const imagPart = imag({inputs: {input: x}, backend});\n    const i = zerosLike({inputs: {x: imagPart}, backend});\n\n    const result = complex({inputs: {real: r, imag: i}, backend});\n\n    backend.disposeIntermediateTensorInfo(realPart);\n    backend.disposeIntermediateTensorInfo(r);\n    backend.disposeIntermediateTensorInfo(imagPart);\n    backend.disposeIntermediateTensorInfo(i);\n\n    return result;\n  } else {\n    return fill({\n      attrs: {\n        shape: x.shape,\n        dtype: x.dtype,\n        value: x.dtype === 'string' ? '' : 0\n      },\n      backend\n    });\n  }\n}\n\nexport const zerosLikeConfig: KernelConfig = {\n  kernelName: ZerosLike,\n  backendName: 'webgl',\n  kernelFunc: zerosLike as unknown as KernelFunc\n};\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAiBA,SAA8CA,SAAS,QAAwB,uBAAuB;AAItG,SAAQC,OAAO,QAAO,WAAW;AACjC,SAAQC,IAAI,QAAO,QAAQ;AAC3B,SAAQC,IAAI,QAAO,QAAQ;AAC3B,SAAQC,IAAI,QAAO,QAAQ;AAE3B,OAAM,SAAUC,SAASA,CACrBC,IAA0D;EAC5D,MAAM;IAACC,MAAM;IAAEC;EAAO,CAAC,GAAGF,IAAI;EAC9B,MAAM;IAACG;EAAC,CAAC,GAAGF,MAAM;EAElB,IAAIE,CAAC,CAACC,KAAK,KAAK,WAAW,EAAE;IAC3B,MAAMC,QAAQ,GAAGP,IAAI,CAAC;MAACG,MAAM,EAAE;QAACK,KAAK,EAAEH;MAAC,CAAC;MAAED;IAAO,CAAC,CAAC;IACpD,MAAMK,CAAC,GAAGR,SAAS,CAAC;MAACE,MAAM,EAAE;QAACE,CAAC,EAAEE;MAAQ,CAAC;MAAEH;IAAO,CAAC,CAAC;IACrD,MAAMM,QAAQ,GAAGX,IAAI,CAAC;MAACI,MAAM,EAAE;QAACK,KAAK,EAAEH;MAAC,CAAC;MAAED;IAAO,CAAC,CAAC;IACpD,MAAMO,CAAC,GAAGV,SAAS,CAAC;MAACE,MAAM,EAAE;QAACE,CAAC,EAAEK;MAAQ,CAAC;MAAEN;IAAO,CAAC,CAAC;IAErD,MAAMQ,MAAM,GAAGf,OAAO,CAAC;MAACM,MAAM,EAAE;QAACH,IAAI,EAAES,CAAC;QAAEV,IAAI,EAAEY;MAAC,CAAC;MAAEP;IAAO,CAAC,CAAC;IAE7DA,OAAO,CAACS,6BAA6B,CAACN,QAAQ,CAAC;IAC/CH,OAAO,CAACS,6BAA6B,CAACJ,CAAC,CAAC;IACxCL,OAAO,CAACS,6BAA6B,CAACH,QAAQ,CAAC;IAC/CN,OAAO,CAACS,6BAA6B,CAACF,CAAC,CAAC;IAExC,OAAOC,MAAM;GACd,MAAM;IACL,OAAOd,IAAI,CAAC;MACVgB,KAAK,EAAE;QACLC,KAAK,EAAEV,CAAC,CAACU,KAAK;QACdT,KAAK,EAAED,CAAC,CAACC,KAAK;QACdU,KAAK,EAAEX,CAAC,CAACC,KAAK,KAAK,QAAQ,GAAG,EAAE,GAAG;OACpC;MACDF;KACD,CAAC;;AAEN;AAEA,OAAO,MAAMa,eAAe,GAAiB;EAC3CC,UAAU,EAAEtB,SAAS;EACrBuB,WAAW,EAAE,OAAO;EACpBC,UAAU,EAAEnB;CACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}