{"ast": null, "code": "/**\n * @license\n * Copyright 2018 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { inferShape } from '../tensor_util_env';\nimport { assertNonNull } from '../util';\nimport { makeTensor } from './tensor_ops_util';\n/**\n * Creates rank-6 `tf.Tensor` with the provided values, shape and dtype.\n *\n * The same functionality can be achieved with `tf.tensor`, but in general\n * we recommend using `tf.tensor6d` as it makes the code more readable.\n *\n *  ```js\n * // Pass a nested array.\n * tf.tensor6d([[[[[[1],[2]],[[3],[4]]],[[[5],[6]],[[7],[8]]]]]]).print();\n * ```\n * ```js\n * // Pass a flat array and specify a shape.\n * tf.tensor6d([1, 2, 3, 4, 5, 6, 7, 8], [1, 1, 2, 2, 2, 1]).print();\n * ```\n *\n * @param values The values of the tensor. Can be nested array of numbers,\n *     or a flat array, or a `TypedArray`.\n * @param shape The shape of the tensor. Optional. If not provided,\n *   it is inferred from `values`.\n * @param dtype The data type.\n *\n * @doc {heading: 'Tensors', subheading: 'Creation'}\n */\nexport function tensor6d(values, shape, dtype) {\n  assertNonNull(values);\n  if (shape != null && shape.length !== 6) {\n    throw new Error('tensor6d() requires shape to have six numbers');\n  }\n  const inferredShape = inferShape(values, dtype);\n  if (inferredShape.length !== 6 && inferredShape.length !== 1) {\n    throw new Error('tensor6d() requires values to be number[][][][][][] or ' + 'flat/TypedArray');\n  }\n  if (inferredShape.length === 1 && shape == null) {\n    throw new Error('tensor6d() requires shape to be provided when `values` ' + 'are a flat array');\n  }\n  shape = shape || inferredShape;\n  return makeTensor(values, shape, inferredShape, dtype);\n}", "map": {"version": 3, "names": ["inferShape", "assertNonNull", "makeTensor", "tensor6d", "values", "shape", "dtype", "length", "Error", "inferredShape"], "sources": ["C:\\tfjs-core\\src\\ops\\tensor6d.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2018 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {Tensor6D} from '../tensor';\nimport {inferShape} from '../tensor_util_env';\nimport {TensorLike6D} from '../types';\nimport {DataType} from '../types';\nimport {assertNonNull} from '../util';\nimport {makeTensor} from './tensor_ops_util';\n\n/**\n * Creates rank-6 `tf.Tensor` with the provided values, shape and dtype.\n *\n * The same functionality can be achieved with `tf.tensor`, but in general\n * we recommend using `tf.tensor6d` as it makes the code more readable.\n *\n *  ```js\n * // Pass a nested array.\n * tf.tensor6d([[[[[[1],[2]],[[3],[4]]],[[[5],[6]],[[7],[8]]]]]]).print();\n * ```\n * ```js\n * // Pass a flat array and specify a shape.\n * tf.tensor6d([1, 2, 3, 4, 5, 6, 7, 8], [1, 1, 2, 2, 2, 1]).print();\n * ```\n *\n * @param values The values of the tensor. Can be nested array of numbers,\n *     or a flat array, or a `TypedArray`.\n * @param shape The shape of the tensor. Optional. If not provided,\n *   it is inferred from `values`.\n * @param dtype The data type.\n *\n * @doc {heading: 'Tensors', subheading: 'Creation'}\n */\nexport function tensor6d(\n    values: TensorLike6D,\n    shape?: [number, number, number, number, number, number],\n    dtype?: DataType): Tensor6D {\n  assertNonNull(values);\n  if (shape != null && shape.length !== 6) {\n    throw new Error('tensor6d() requires shape to have six numbers');\n  }\n  const inferredShape = inferShape(values, dtype);\n  if (inferredShape.length !== 6 && inferredShape.length !== 1) {\n    throw new Error(\n        'tensor6d() requires values to be number[][][][][][] or ' +\n        'flat/TypedArray');\n  }\n  if (inferredShape.length === 1 && shape == null) {\n    throw new Error(\n        'tensor6d() requires shape to be provided when `values` ' +\n        'are a flat array');\n  }\n  shape = shape ||\n      inferredShape as [number, number, number, number, number, number];\n  return makeTensor(values, shape, inferredShape, dtype) as Tensor6D;\n}\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAkBA,SAAQA,UAAU,QAAO,oBAAoB;AAG7C,SAAQC,aAAa,QAAO,SAAS;AACrC,SAAQC,UAAU,QAAO,mBAAmB;AAE5C;;;;;;;;;;;;;;;;;;;;;;;AAuBA,OAAM,SAAUC,QAAQA,CACpBC,MAAoB,EACpBC,KAAwD,EACxDC,KAAgB;EAClBL,aAAa,CAACG,MAAM,CAAC;EACrB,IAAIC,KAAK,IAAI,IAAI,IAAIA,KAAK,CAACE,MAAM,KAAK,CAAC,EAAE;IACvC,MAAM,IAAIC,KAAK,CAAC,+CAA+C,CAAC;;EAElE,MAAMC,aAAa,GAAGT,UAAU,CAACI,MAAM,EAAEE,KAAK,CAAC;EAC/C,IAAIG,aAAa,CAACF,MAAM,KAAK,CAAC,IAAIE,aAAa,CAACF,MAAM,KAAK,CAAC,EAAE;IAC5D,MAAM,IAAIC,KAAK,CACX,yDAAyD,GACzD,iBAAiB,CAAC;;EAExB,IAAIC,aAAa,CAACF,MAAM,KAAK,CAAC,IAAIF,KAAK,IAAI,IAAI,EAAE;IAC/C,MAAM,IAAIG,KAAK,CACX,yDAAyD,GACzD,kBAAkB,CAAC;;EAEzBH,KAAK,GAAGA,KAAK,IACTI,aAAiE;EACrE,OAAOP,UAAU,CAACE,MAAM,EAAEC,KAAK,EAAEI,aAAa,EAAEH,KAAK,CAAa;AACpE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}