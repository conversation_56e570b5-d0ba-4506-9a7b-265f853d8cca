{"ast": null, "code": "/**\n * @license\n * Copyright 2022 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { Cumprod } from '@tensorflow/tfjs-core';\nimport { CumOpType } from '../cum_gpu';\nimport { cumImpl } from './Cum_impl';\nexport function cumprod(args) {\n  const {\n    inputs,\n    backend,\n    attrs\n  } = args;\n  const {\n    x\n  } = inputs;\n  const {\n    axis,\n    exclusive,\n    reverse\n  } = attrs;\n  return cumImpl(CumOpType.Prod, x, backend, axis, exclusive, reverse);\n}\nexport const cumprodConfig = {\n  kernelName: Cumprod,\n  backendName: 'webgl',\n  kernelFunc: cumprod\n};", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON>", "CumOpType", "cumImpl", "cump<PERSON>", "args", "inputs", "backend", "attrs", "x", "axis", "exclusive", "reverse", "Prod", "cumprodConfig", "kernelName", "backendName", "kernelFunc"], "sources": ["C:\\tfjs-backend-webgl\\src\\kernels\\Cumprod.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2022 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {Cumprod, CumprodAttrs, CumprodInputs, KernelConfig, KernelFunc, TensorInfo} from '@tensorflow/tfjs-core';\n\nimport {MathBackendWebGL} from '../backend_webgl';\nimport {CumOpType} from '../cum_gpu';\nimport {cumImpl} from './Cum_impl';\n\nexport function cumprod(args: {\n  inputs: CumprodInputs,\n  backend: MathBackendWebGL,\n  attrs: CumprodAttrs\n}): TensorInfo {\n  const {inputs, backend, attrs} = args;\n  const {x} = inputs;\n  const {axis, exclusive, reverse} = attrs;\n\n  return cumImpl(CumOpType.Prod, x, backend, axis, exclusive, reverse);\n}\n\nexport const cumprodConfig: KernelConfig = {\n  kernelName: Cumprod,\n  backendName: 'webgl',\n  kernelFunc: cumprod as unknown as KernelFunc\n};\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAiBA,SAAQA,OAAO,QAA0E,uBAAuB;AAGhH,SAAQC,SAAS,QAAO,YAAY;AACpC,SAAQC,OAAO,QAAO,YAAY;AAElC,OAAM,SAAUC,OAAOA,CAACC,IAIvB;EACC,MAAM;IAACC,MAAM;IAAEC,OAAO;IAAEC;EAAK,CAAC,GAAGH,IAAI;EACrC,MAAM;IAACI;EAAC,CAAC,GAAGH,MAAM;EAClB,MAAM;IAACI,IAAI;IAAEC,SAAS;IAAEC;EAAO,CAAC,GAAGJ,KAAK;EAExC,OAAOL,OAAO,CAACD,SAAS,CAACW,IAAI,EAAEJ,CAAC,EAAEF,OAAO,EAAEG,IAAI,EAAEC,SAAS,EAAEC,OAAO,CAAC;AACtE;AAEA,OAAO,MAAME,aAAa,GAAiB;EACzCC,UAAU,EAAEd,OAAO;EACnBe,WAAW,EAAE,OAAO;EACpBC,UAAU,EAAEb;CACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}