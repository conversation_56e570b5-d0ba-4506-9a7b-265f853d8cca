{"ast": null, "code": "/**\n * @license\n * Copyright 2022 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n// tslint:disable-next-line: no-imports-from-dist\nimport * as tfOps from '@tensorflow/tfjs-core/dist/ops/ops_for_converter';\nimport { getParamValue } from './utils';\nexport const executeOp = function (node, tensorMap, context) {\n  let ops = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : tfOps;\n  switch (node.op) {\n    case 'RaggedGather':\n      {\n        const {\n          outputNestedSplits,\n          outputDenseValues\n        } = ops.raggedGather(getParamValue('paramsNestedSplits', node, tensorMap, context), getParamValue('paramsDenseValues', node, tensorMap, context), getParamValue('indices', node, tensorMap, context), getParamValue('outputRaggedRank', node, tensorMap, context));\n        return outputNestedSplits.concat(outputDenseValues);\n      }\n    case 'RaggedRange':\n      {\n        const {\n          rtNestedSplits,\n          rtDenseValues\n        } = ops.raggedRange(getParamValue('starts', node, tensorMap, context), getParamValue('limits', node, tensorMap, context), getParamValue('splits', node, tensorMap, context));\n        return [rtNestedSplits, rtDenseValues];\n      }\n    case 'RaggedTensorToTensor':\n      {\n        return [ops.raggedTensorToTensor(getParamValue('shape', node, tensorMap, context), getParamValue('values', node, tensorMap, context), getParamValue('defaultValue', node, tensorMap, context), getParamValue('rowPartitionTensors', node, tensorMap, context), getParamValue('rowPartitionTypes', node, tensorMap, context))];\n      }\n    default:\n      throw TypeError(\"Node type \".concat(node.op, \" is not implemented\"));\n  }\n};\nexport const CATEGORY = 'ragged';", "map": {"version": 3, "names": ["tfOps", "getParamValue", "executeOp", "node", "tensorMap", "context", "ops", "arguments", "length", "undefined", "op", "outputNestedSplits", "outputDenseValues", "<PERSON><PERSON><PERSON>", "concat", "rtNestedSplits", "rtDenseValues", "<PERSON><PERSON><PERSON><PERSON>", "raggedTensorToTensor", "TypeError", "CATEGORY"], "sources": ["C:\\tfjs-converter\\src\\operations\\executors\\ragged_executor.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2022 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {Tensor, Tensor1D} from '@tensorflow/tfjs-core';\n// tslint:disable-next-line: no-imports-from-dist\nimport * as tfOps from '@tensorflow/tfjs-core/dist/ops/ops_for_converter';\n\nimport {NamedTensorsMap} from '../../data/types';\nimport {ExecutionContext} from '../../executor/execution_context';\nimport {InternalOpExecutor, Node} from '../types';\n\nimport {getParamValue} from './utils';\n\nexport const executeOp: InternalOpExecutor =\n    (node: Node, tensorMap: NamedTensorsMap, context: ExecutionContext,\n     ops = tfOps): Tensor[] => {\n      switch (node.op) {\n        case 'RaggedGather': {\n          const {\n            outputNestedSplits,\n            outputDenseValues,\n          } =\n              ops.raggedGather(\n                  getParamValue(\n                      'paramsNestedSplits', node, tensorMap, context) as\n                      Tensor[],\n                  getParamValue(\n                      'paramsDenseValues', node, tensorMap, context) as Tensor,\n                  getParamValue('indices', node, tensorMap, context) as Tensor,\n                  getParamValue('outputRaggedRank', node, tensorMap, context) as\n                      number);\n          return outputNestedSplits.concat(outputDenseValues);\n        }\n        case 'RaggedRange': {\n          const {rtNestedSplits, rtDenseValues} = ops.raggedRange(\n              getParamValue('starts', node, tensorMap, context) as Tensor,\n              getParamValue('limits', node, tensorMap, context) as Tensor,\n              getParamValue('splits', node, tensorMap, context) as Tensor);\n          return [rtNestedSplits, rtDenseValues];\n        }\n        case 'RaggedTensorToTensor': {\n          return [ops.raggedTensorToTensor(\n              getParamValue('shape', node, tensorMap, context) as Tensor,\n              getParamValue('values', node, tensorMap, context) as Tensor1D,\n              getParamValue('defaultValue', node, tensorMap, context) as Tensor,\n              getParamValue('rowPartitionTensors', node, tensorMap, context) as\n                  Tensor[],\n              getParamValue('rowPartitionTypes', node, tensorMap, context) as\n                  string[])];\n        }\n        default:\n          throw TypeError(`Node type ${node.op} is not implemented`);\n      }\n    };\n\nexport const CATEGORY = 'ragged';\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAkBA;AACA,OAAO,KAAKA,KAAK,MAAM,kDAAkD;AAMzE,SAAQC,aAAa,QAAO,SAAS;AAErC,OAAO,MAAMC,SAAS,GAClB,SAAAA,CAACC,IAAU,EAAEC,SAA0B,EAAEC,OAAyB,EACxC;EAAA,IAAzBC,GAAG,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAGP,KAAK;EACV,QAAQG,IAAI,CAACO,EAAE;IACb,KAAK,cAAc;MAAE;QACnB,MAAM;UACJC,kBAAkB;UAClBC;QAAiB,CAClB,GACGN,GAAG,CAACO,YAAY,CACZZ,aAAa,CACT,oBAAoB,EAAEE,IAAI,EAAEC,SAAS,EAAEC,OAAO,CACtC,EACZJ,aAAa,CACT,mBAAmB,EAAEE,IAAI,EAAEC,SAAS,EAAEC,OAAO,CAAW,EAC5DJ,aAAa,CAAC,SAAS,EAAEE,IAAI,EAAEC,SAAS,EAAEC,OAAO,CAAW,EAC5DJ,aAAa,CAAC,kBAAkB,EAAEE,IAAI,EAAEC,SAAS,EAAEC,OAAO,CAChD,CAAC;QACnB,OAAOM,kBAAkB,CAACG,MAAM,CAACF,iBAAiB,CAAC;;IAErD,KAAK,aAAa;MAAE;QAClB,MAAM;UAACG,cAAc;UAAEC;QAAa,CAAC,GAAGV,GAAG,CAACW,WAAW,CACnDhB,aAAa,CAAC,QAAQ,EAAEE,IAAI,EAAEC,SAAS,EAAEC,OAAO,CAAW,EAC3DJ,aAAa,CAAC,QAAQ,EAAEE,IAAI,EAAEC,SAAS,EAAEC,OAAO,CAAW,EAC3DJ,aAAa,CAAC,QAAQ,EAAEE,IAAI,EAAEC,SAAS,EAAEC,OAAO,CAAW,CAAC;QAChE,OAAO,CAACU,cAAc,EAAEC,aAAa,CAAC;;IAExC,KAAK,sBAAsB;MAAE;QAC3B,OAAO,CAACV,GAAG,CAACY,oBAAoB,CAC5BjB,aAAa,CAAC,OAAO,EAAEE,IAAI,EAAEC,SAAS,EAAEC,OAAO,CAAW,EAC1DJ,aAAa,CAAC,QAAQ,EAAEE,IAAI,EAAEC,SAAS,EAAEC,OAAO,CAAa,EAC7DJ,aAAa,CAAC,cAAc,EAAEE,IAAI,EAAEC,SAAS,EAAEC,OAAO,CAAW,EACjEJ,aAAa,CAAC,qBAAqB,EAAEE,IAAI,EAAEC,SAAS,EAAEC,OAAO,CACjD,EACZJ,aAAa,CAAC,mBAAmB,EAAEE,IAAI,EAAEC,SAAS,EAAEC,OAAO,CAC/C,CAAC,CAAC;;IAEpB;MACE,MAAMc,SAAS,cAAAL,MAAA,CAAcX,IAAI,CAACO,EAAE,wBAAqB,CAAC;;AAEhE,CAAC;AAEL,OAAO,MAAMU,QAAQ,GAAG,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}