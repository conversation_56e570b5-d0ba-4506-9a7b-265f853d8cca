{"ast": null, "code": "// Is a given value a DOM element?\nexport default function isElement(obj) {\n  return !!(obj && obj.nodeType === 1);\n}", "map": {"version": 3, "names": ["isElement", "obj", "nodeType"], "sources": ["C:/tmsft/node_modules/underscore/modules/isElement.js"], "sourcesContent": ["// Is a given value a DOM element?\nexport default function isElement(obj) {\n  return !!(obj && obj.nodeType === 1);\n}\n"], "mappings": "AAAA;AACA,eAAe,SAASA,SAASA,CAACC,GAAG,EAAE;EACrC,OAAO,CAAC,EAAEA,GAAG,IAAIA,GAAG,CAACC,QAAQ,KAAK,CAAC,CAAC;AACtC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}