{"ast": null, "code": "/**\n * @license\n * Copyright 2018 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nexport class DepthToSpaceProgram {\n  constructor(outputShape, blockSize, dataFormat) {\n    this.variableNames = ['x'];\n    this.outputShape = [];\n    this.outputShape = outputShape;\n    this.blockSize = blockSize;\n    this.dataFormat = dataFormat;\n    this.userCode = `\n    void main() {\n      ivec4 coords = getOutputCoords();\n      int b = coords[0];\n      int h = ${this.getHeightCoordString()};\n      int w = ${this.getWidthCoordString()};\n      int d = ${this.getDepthCoordString()};\n\n      int in_h = h / ${blockSize};\n      int offset_h = imod(h, ${blockSize});\n      int in_w = w / ${blockSize};\n      int offset_w = imod(w, ${blockSize});\n      int offset_d = (offset_h * ${blockSize} + offset_w) *\n        ${this.getOutputDepthSize()};\n      int in_d = d + offset_d;\n\n      float result = ${this.getInputSamplingString()};\n      setOutput(result);\n    }\n  `;\n  }\n  getHeightCoordString() {\n    if (this.dataFormat === 'NHWC') {\n      return `coords[1]`;\n    } else {\n      return `coords[2]`;\n    }\n  }\n  getWidthCoordString() {\n    if (this.dataFormat === 'NHWC') {\n      return `coords[2]`;\n    } else {\n      return `coords[3]`;\n    }\n  }\n  getDepthCoordString() {\n    if (this.dataFormat === 'NHWC') {\n      return `coords[3]`;\n    } else {\n      return `coords[1]`;\n    }\n  }\n  getOutputDepthSize() {\n    if (this.dataFormat === 'NHWC') {\n      return this.outputShape[3];\n    } else {\n      return this.outputShape[1];\n    }\n  }\n  getInputSamplingString() {\n    if (this.dataFormat === 'NHWC') {\n      return `getX(b, in_h, in_w, in_d)`;\n    } else {\n      return `getX(b, in_d, in_h, in_w)`;\n    }\n  }\n}", "map": {"version": 3, "names": ["DepthToSpaceProgram", "constructor", "outputShape", "blockSize", "dataFormat", "variableNames", "userCode", "getHeightCoordString", "getWidthCoordString", "getDepthCoordString", "getOutputDepthSize", "getInputSamplingString"], "sources": ["C:\\tfjs-backend-webgl\\src\\depth_to_space_gpu.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2018 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {GPGPUProgram} from './gpgpu_math';\n\nexport class DepthToSpaceProgram implements GPGPUProgram {\n  variableNames = ['x'];\n  outputShape: number[] = [];\n  userCode: string;\n  blockSize: number;\n  dataFormat: string;\n\n  constructor(\n      outputShape: number[], blockSize: number, dataFormat: 'NHWC'|'NCHW') {\n    this.outputShape = outputShape;\n    this.blockSize = blockSize;\n    this.dataFormat = dataFormat;\n    this.userCode = `\n    void main() {\n      ivec4 coords = getOutputCoords();\n      int b = coords[0];\n      int h = ${this.getHeightCoordString()};\n      int w = ${this.getWidthCoordString()};\n      int d = ${this.getDepthCoordString()};\n\n      int in_h = h / ${blockSize};\n      int offset_h = imod(h, ${blockSize});\n      int in_w = w / ${blockSize};\n      int offset_w = imod(w, ${blockSize});\n      int offset_d = (offset_h * ${blockSize} + offset_w) *\n        ${this.getOutputDepthSize()};\n      int in_d = d + offset_d;\n\n      float result = ${this.getInputSamplingString()};\n      setOutput(result);\n    }\n  `;\n  }\n\n  private getHeightCoordString(): string {\n    if (this.dataFormat === 'NHWC') {\n      return `coords[1]`;\n    } else {\n      return `coords[2]`;\n    }\n  }\n\n  private getWidthCoordString(): string {\n    if (this.dataFormat === 'NHWC') {\n      return `coords[2]`;\n    } else {\n      return `coords[3]`;\n    }\n  }\n\n  private getDepthCoordString(): string {\n    if (this.dataFormat === 'NHWC') {\n      return `coords[3]`;\n    } else {\n      return `coords[1]`;\n    }\n  }\n\n  private getOutputDepthSize(): number {\n    if (this.dataFormat === 'NHWC') {\n      return this.outputShape[3];\n    } else {\n      return this.outputShape[1];\n    }\n  }\n\n  private getInputSamplingString(): string {\n    if (this.dataFormat === 'NHWC') {\n      return `getX(b, in_h, in_w, in_d)`;\n    } else {\n      return `getX(b, in_d, in_h, in_w)`;\n    }\n  }\n}\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAmBA,OAAM,MAAOA,mBAAmB;EAO9BC,YACIC,WAAqB,EAAEC,SAAiB,EAAEC,UAAyB;IAPvE,KAAAC,aAAa,GAAG,CAAC,GAAG,CAAC;IACrB,KAAAH,WAAW,GAAa,EAAE;IAOxB,IAAI,CAACA,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACC,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACC,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACE,QAAQ,GAAG;;;;gBAIJ,IAAI,CAACC,oBAAoB,EAAE;gBAC3B,IAAI,CAACC,mBAAmB,EAAE;gBAC1B,IAAI,CAACC,mBAAmB,EAAE;;uBAEnBN,SAAS;+BACDA,SAAS;uBACjBA,SAAS;+BACDA,SAAS;mCACLA,SAAS;UAClC,IAAI,CAACO,kBAAkB,EAAE;;;uBAGZ,IAAI,CAACC,sBAAsB,EAAE;;;GAGjD;EACD;EAEQJ,oBAAoBA,CAAA;IAC1B,IAAI,IAAI,CAACH,UAAU,KAAK,MAAM,EAAE;MAC9B,OAAO,WAAW;KACnB,MAAM;MACL,OAAO,WAAW;;EAEtB;EAEQI,mBAAmBA,CAAA;IACzB,IAAI,IAAI,CAACJ,UAAU,KAAK,MAAM,EAAE;MAC9B,OAAO,WAAW;KACnB,MAAM;MACL,OAAO,WAAW;;EAEtB;EAEQK,mBAAmBA,CAAA;IACzB,IAAI,IAAI,CAACL,UAAU,KAAK,MAAM,EAAE;MAC9B,OAAO,WAAW;KACnB,MAAM;MACL,OAAO,WAAW;;EAEtB;EAEQM,kBAAkBA,CAAA;IACxB,IAAI,IAAI,CAACN,UAAU,KAAK,MAAM,EAAE;MAC9B,OAAO,IAAI,CAACF,WAAW,CAAC,CAAC,CAAC;KAC3B,MAAM;MACL,OAAO,IAAI,CAACA,WAAW,CAAC,CAAC,CAAC;;EAE9B;EAEQS,sBAAsBA,CAAA;IAC5B,IAAI,IAAI,CAACP,UAAU,KAAK,MAAM,EAAE;MAC9B,OAAO,2BAA2B;KACnC,MAAM;MACL,OAAO,2BAA2B;;EAEtC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}