{"ast": null, "code": "/**\n * @license\n * Copyright 2022 CodeSmith LLC\n *\n * Use of this source code is governed by an MIT-style\n * license that can be found in the LICENSE file or at\n * https://opensource.org/licenses/MIT.\n * =============================================================================\n */\nimport { Layer } from '../../engine/topology';\nimport { serialization, tidy } from '@tensorflow/tfjs-core';\nimport { greater, greaterEqual, max, min } from '@tensorflow/tfjs-core';\nimport { getExactlyOneShape, getExactlyOneTensor } from '../../utils/types_utils';\nimport { ValueError } from '../../errors';\nimport * as K from '../../backend/tfjs_backend';\nimport * as utils from './preprocessing_utils';\nclass CategoryEncoding extends Layer {\n  constructor(args) {\n    super(args);\n    this.numTokens = args.numTokens;\n    if (args.outputMode) {\n      this.outputMode = args.outputMode;\n    } else {\n      this.outputMode = 'multiHot';\n    }\n  }\n  getConfig() {\n    const config = {\n      'numTokens': this.numTokens,\n      'outputMode': this.outputMode\n    };\n    const baseConfig = super.getConfig();\n    Object.assign(config, baseConfig);\n    return config;\n  }\n  computeOutputShape(inputShape) {\n    inputShape = getExactlyOneShape(inputShape);\n    if (inputShape == null) {\n      return [this.numTokens];\n    }\n    if (this.outputMode === 'oneHot' && inputShape[inputShape.length - 1] !== 1) {\n      inputShape.push(this.numTokens);\n      return inputShape;\n    }\n    inputShape[inputShape.length - 1] = this.numTokens;\n    return inputShape;\n  }\n  call(inputs, kwargs) {\n    return tidy(() => {\n      inputs = getExactlyOneTensor(inputs);\n      if (inputs.dtype !== 'int32') {\n        inputs = K.cast(inputs, 'int32');\n      }\n      let countWeights;\n      if (typeof kwargs['countWeights'] !== 'undefined') {\n        if (this.outputMode !== 'count') {\n          throw new ValueError(`countWeights is not used when outputMode !== count.\n              Received countWeights=${kwargs['countWeights']}`);\n        }\n        countWeights = getExactlyOneTensor(kwargs['countWeights']);\n      }\n      const maxValue = max(inputs);\n      const minValue = min(inputs);\n      const greaterEqualMax = greater(this.numTokens, maxValue).bufferSync().get(0);\n      const greaterMin = greaterEqual(minValue, 0).bufferSync().get(0);\n      if (!(greaterEqualMax && greaterMin)) {\n        throw new ValueError('Input values must be between 0 < values <=' + ` numTokens with numTokens=${this.numTokens}`);\n      }\n      return utils.encodeCategoricalInputs(inputs, this.outputMode, this.numTokens, countWeights);\n    });\n  }\n}\n/** @nocollapse */\nCategoryEncoding.className = 'CategoryEncoding';\nexport { CategoryEncoding };\nserialization.registerClass(CategoryEncoding);", "map": {"version": 3, "names": ["Layer", "serialization", "tidy", "greater", "greaterEqual", "max", "min", "getExactlyOneShape", "getExactlyOneTensor", "ValueError", "K", "utils", "CategoryEncoding", "constructor", "args", "numTokens", "outputMode", "getConfig", "config", "baseConfig", "Object", "assign", "computeOutputShape", "inputShape", "length", "push", "call", "inputs", "kwargs", "dtype", "cast", "countWeights", "maxValue", "minValue", "greaterEqualMax", "bufferSync", "get", "greaterMin", "encodeCategoricalInputs", "className", "registerClass"], "sources": ["C:\\tfjs-layers\\src\\layers\\preprocessing\\category_encoding.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2022 CodeSmith LLC\n *\n * Use of this source code is governed by an MIT-style\n * license that can be found in the LICENSE file or at\n * https://opensource.org/licenses/MIT.\n * =============================================================================\n */\n\nimport { LayerArgs, Layer } from '../../engine/topology';\nimport { serialization, Tensor, tidy, Tensor1D, Tensor2D} from '@tensorflow/tfjs-core';\nimport { greater, greaterEqual, max, min} from '@tensorflow/tfjs-core';\nimport { Shape } from '../../keras_format/common';\nimport { getExactlyOneShape, getExactlyOneTensor } from '../../utils/types_utils';\nimport { Kwargs } from '../../types';\nimport { ValueError } from '../../errors';\nimport * as K from '../../backend/tfjs_backend';\nimport * as utils from './preprocessing_utils';\nimport { OutputMode } from './preprocessing_utils';\n\nexport declare interface CategoryEncodingArgs extends LayerArgs {\n  numTokens: number;\n  outputMode?: OutputMode;\n }\n\nexport class CategoryEncoding extends Layer {\n  /** @nocollapse */\n  static className = 'CategoryEncoding';\n  private readonly numTokens: number;\n  private readonly outputMode: OutputMode;\n\n  constructor(args: CategoryEncodingArgs) {\n    super(args);\n    this.numTokens = args.numTokens;\n\n    if(args.outputMode) {\n    this.outputMode = args.outputMode;\n    } else {\n      this.outputMode = 'multiHot';\n    }\n  }\n\n  override getConfig(): serialization.ConfigDict {\n    const config: serialization.ConfigDict = {\n      'numTokens': this.numTokens,\n      'outputMode': this.outputMode,\n    };\n\n    const baseConfig = super.getConfig();\n    Object.assign(config, baseConfig);\n    return config;\n  }\n\n  override computeOutputShape(inputShape: Shape|Shape[]): Shape|Shape[] {\n    inputShape = getExactlyOneShape(inputShape);\n\n    if(inputShape == null) {\n      return [this.numTokens];\n    }\n\n    if(this.outputMode === 'oneHot' && inputShape[inputShape.length - 1] !== 1){\n      inputShape.push(this.numTokens);\n      return inputShape;\n    }\n\n    inputShape[inputShape.length - 1] = this.numTokens;\n    return inputShape;\n  }\n\n  override call(inputs: Tensor|Tensor[], kwargs: Kwargs): Tensor[]|Tensor {\n    return tidy(() => {\n\n        inputs = getExactlyOneTensor(inputs);\n        if(inputs.dtype !== 'int32') {\n          inputs = K.cast(inputs, 'int32');\n      }\n\n        let countWeights: Tensor1D | Tensor2D;\n\n        if((typeof kwargs['countWeights']) !== 'undefined') {\n\n          if(this.outputMode !== 'count') {\n            throw new ValueError(\n              `countWeights is not used when outputMode !== count.\n              Received countWeights=${kwargs['countWeights']}`);\n          }\n\n          countWeights\n            =  getExactlyOneTensor(kwargs['countWeights']) as Tensor1D|Tensor2D;\n        }\n\n        const maxValue = max(inputs);\n        const minValue = min(inputs);\n        const greaterEqualMax = greater(this.numTokens, maxValue)\n                                                    .bufferSync().get(0);\n\n        const greaterMin = greaterEqual(minValue, 0).bufferSync().get(0);\n\n        if(!(greaterEqualMax && greaterMin)) {\n\n          throw new ValueError('Input values must be between 0 < values <='\n            + ` numTokens with numTokens=${this.numTokens}`);\n        }\n\n        return utils.encodeCategoricalInputs(inputs,\n          this.outputMode, this.numTokens, countWeights);\n    });\n  }\n}\n\nserialization.registerClass(CategoryEncoding);\n"], "mappings": "AAAA;;;;;;;;;AAUA,SAAoBA,KAAK,QAAQ,uBAAuB;AACxD,SAASC,aAAa,EAAUC,IAAI,QAA2B,uBAAuB;AACtF,SAASC,OAAO,EAAEC,YAAY,EAAEC,GAAG,EAAEC,GAAG,QAAO,uBAAuB;AAEtE,SAASC,kBAAkB,EAAEC,mBAAmB,QAAQ,yBAAyB;AAEjF,SAASC,UAAU,QAAQ,cAAc;AACzC,OAAO,KAAKC,CAAC,MAAM,4BAA4B;AAC/C,OAAO,KAAKC,KAAK,MAAM,uBAAuB;AAQ9C,MAAaC,gBAAiB,SAAQZ,KAAK;EAMzCa,YAAYC,IAA0B;IACpC,KAAK,CAACA,IAAI,CAAC;IACX,IAAI,CAACC,SAAS,GAAGD,IAAI,CAACC,SAAS;IAE/B,IAAGD,IAAI,CAACE,UAAU,EAAE;MACpB,IAAI,CAACA,UAAU,GAAGF,IAAI,CAACE,UAAU;KAChC,MAAM;MACL,IAAI,CAACA,UAAU,GAAG,UAAU;;EAEhC;EAESC,SAASA,CAAA;IAChB,MAAMC,MAAM,GAA6B;MACvC,WAAW,EAAE,IAAI,CAACH,SAAS;MAC3B,YAAY,EAAE,IAAI,CAACC;KACpB;IAED,MAAMG,UAAU,GAAG,KAAK,CAACF,SAAS,EAAE;IACpCG,MAAM,CAACC,MAAM,CAACH,MAAM,EAAEC,UAAU,CAAC;IACjC,OAAOD,MAAM;EACf;EAESI,kBAAkBA,CAACC,UAAyB;IACnDA,UAAU,GAAGhB,kBAAkB,CAACgB,UAAU,CAAC;IAE3C,IAAGA,UAAU,IAAI,IAAI,EAAE;MACrB,OAAO,CAAC,IAAI,CAACR,SAAS,CAAC;;IAGzB,IAAG,IAAI,CAACC,UAAU,KAAK,QAAQ,IAAIO,UAAU,CAACA,UAAU,CAACC,MAAM,GAAG,CAAC,CAAC,KAAK,CAAC,EAAC;MACzED,UAAU,CAACE,IAAI,CAAC,IAAI,CAACV,SAAS,CAAC;MAC/B,OAAOQ,UAAU;;IAGnBA,UAAU,CAACA,UAAU,CAACC,MAAM,GAAG,CAAC,CAAC,GAAG,IAAI,CAACT,SAAS;IAClD,OAAOQ,UAAU;EACnB;EAESG,IAAIA,CAACC,MAAuB,EAAEC,MAAc;IACnD,OAAO1B,IAAI,CAAC,MAAK;MAEbyB,MAAM,GAAGnB,mBAAmB,CAACmB,MAAM,CAAC;MACpC,IAAGA,MAAM,CAACE,KAAK,KAAK,OAAO,EAAE;QAC3BF,MAAM,GAAGjB,CAAC,CAACoB,IAAI,CAACH,MAAM,EAAE,OAAO,CAAC;;MAGlC,IAAII,YAAiC;MAErC,IAAI,OAAOH,MAAM,CAAC,cAAc,CAAC,KAAM,WAAW,EAAE;QAElD,IAAG,IAAI,CAACZ,UAAU,KAAK,OAAO,EAAE;UAC9B,MAAM,IAAIP,UAAU,CAClB;sCACwBmB,MAAM,CAAC,cAAc,CAAC,EAAE,CAAC;;QAGrDG,YAAY,GACPvB,mBAAmB,CAACoB,MAAM,CAAC,cAAc,CAAC,CAAsB;;MAGvE,MAAMI,QAAQ,GAAG3B,GAAG,CAACsB,MAAM,CAAC;MAC5B,MAAMM,QAAQ,GAAG3B,GAAG,CAACqB,MAAM,CAAC;MAC5B,MAAMO,eAAe,GAAG/B,OAAO,CAAC,IAAI,CAACY,SAAS,EAAEiB,QAAQ,CAAC,CACZG,UAAU,EAAE,CAACC,GAAG,CAAC,CAAC,CAAC;MAEhE,MAAMC,UAAU,GAAGjC,YAAY,CAAC6B,QAAQ,EAAE,CAAC,CAAC,CAACE,UAAU,EAAE,CAACC,GAAG,CAAC,CAAC,CAAC;MAEhE,IAAG,EAAEF,eAAe,IAAIG,UAAU,CAAC,EAAE;QAEnC,MAAM,IAAI5B,UAAU,CAAC,4CAA4C,GAC7D,6BAA6B,IAAI,CAACM,SAAS,EAAE,CAAC;;MAGpD,OAAOJ,KAAK,CAAC2B,uBAAuB,CAACX,MAAM,EACzC,IAAI,CAACX,UAAU,EAAE,IAAI,CAACD,SAAS,EAAEgB,YAAY,CAAC;IACpD,CAAC,CAAC;EACJ;;AAjFA;AACOnB,gBAAA,CAAA2B,SAAS,GAAG,kBAAkB;SAF1B3B,gBAAgB;AAqF7BX,aAAa,CAACuC,aAAa,CAAC5B,gBAAgB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}