{"ast": null, "code": "/*\nCopyright (c) 2011, <PERSON>\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in\nall copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\nTHE SOFTWARE.\n*/\n\n'use strict';\n\nexports.stopwords = require('./stopwords').words;\nexports.abbreviations = require('./abbreviations_en').knownAbbreviations;\nexports.ShortestPathTree = require('./shortest_path_tree');\nexports.LongestPathTree = require('./longest_path_tree');\nexports.DirectedEdge = require('./directed_edge');\nexports.EdgeWeightedDigraph = require('./edge_weighted_digraph');\nexports.Topological = require('./topological');\nexports.StorageBackend = require('./storage');", "map": {"version": 3, "names": ["exports", "stopwords", "require", "words", "abbreviations", "knownAbbreviations", "ShortestPathTree", "LongestPathTree", "DirectedEdge", "EdgeWeightedDigraph", "Topological", "StorageBackend"], "sources": ["C:/tmsft/node_modules/natural/lib/natural/util/index.js"], "sourcesContent": ["/*\nCopyright (c) 2011, <PERSON>\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in\nall copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\nTHE SOFTWARE.\n*/\n\n'use strict'\n\nexports.stopwords = require('./stopwords').words\nexports.abbreviations = require('./abbreviations_en').knownAbbreviations\nexports.ShortestPathTree = require('./shortest_path_tree')\nexports.LongestPathTree = require('./longest_path_tree')\nexports.DirectedEdge = require('./directed_edge')\nexports.EdgeWeightedDigraph = require('./edge_weighted_digraph')\nexports.Topological = require('./topological')\nexports.StorageBackend = require('./storage')\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,YAAY;;AAEZA,OAAO,CAACC,SAAS,GAAGC,OAAO,CAAC,aAAa,CAAC,CAACC,KAAK;AAChDH,OAAO,CAACI,aAAa,GAAGF,OAAO,CAAC,oBAAoB,CAAC,CAACG,kBAAkB;AACxEL,OAAO,CAACM,gBAAgB,GAAGJ,OAAO,CAAC,sBAAsB,CAAC;AAC1DF,OAAO,CAACO,eAAe,GAAGL,OAAO,CAAC,qBAAqB,CAAC;AACxDF,OAAO,CAACQ,YAAY,GAAGN,OAAO,CAAC,iBAAiB,CAAC;AACjDF,OAAO,CAACS,mBAAmB,GAAGP,OAAO,CAAC,yBAAyB,CAAC;AAChEF,OAAO,CAACU,WAAW,GAAGR,OAAO,CAAC,eAAe,CAAC;AAC9CF,OAAO,CAACW,cAAc,GAAGT,OAAO,CAAC,WAAW,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}