{"ast": null, "code": "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { backend_util, Dilation2D } from '@tensorflow/tfjs-core';\nimport { Dilation2DProgram } from '../dilation_gpu';\nimport { reshape } from './Reshape';\nexport function dilation2D(args) {\n  const {\n    inputs,\n    backend,\n    attrs\n  } = args;\n  const {\n    x,\n    filter\n  } = inputs;\n  const {\n    strides,\n    pad,\n    dilations\n  } = attrs;\n  const convInfo = backend_util.computeDilation2DInfo(x.shape, filter.shape, strides, pad, 'NHWC' /* dataFormat */, dilations);\n  let out;\n  const program = new Dilation2DProgram(convInfo);\n  out = backend.runWebGLProgram(program, [x, filter], 'float32');\n  const outReshaped = reshape({\n    inputs: {\n      x: out\n    },\n    backend,\n    attrs: {\n      shape: convInfo.outShape\n    }\n  });\n  backend.disposeIntermediateTensorInfo(out);\n  return outReshaped;\n}\nexport const dilation2DConfig = {\n  kernelName: Dilation2D,\n  backendName: 'webgl',\n  kernelFunc: dilation2D\n};", "map": {"version": 3, "names": ["backend_util", "Dilation2D", "Dilation2DProgram", "reshape", "dilation2D", "args", "inputs", "backend", "attrs", "x", "filter", "strides", "pad", "dilations", "convInfo", "computeDilation2DInfo", "shape", "out", "program", "runWebGLProgram", "outR<PERSON><PERSON><PERSON>", "outShape", "disposeIntermediateTensorInfo", "dilation2DConfig", "kernelName", "backendName", "kernelFunc"], "sources": ["C:\\tfjs-backend-webgl\\src\\kernels\\Dilation2D.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {backend_util, Dilation2D, Dilation2DAttrs, Dilation2DInputs, KernelConfig, KernelFunc, TensorInfo} from '@tensorflow/tfjs-core';\n\nimport {MathBackendWebGL} from '../backend_webgl';\nimport {Dilation2DProgram} from '../dilation_gpu';\nimport {reshape} from './Reshape';\n\nexport function dilation2D(args: {\n  inputs: Dilation2DInputs,\n  attrs: Dilation2DAttrs,\n  backend: MathBackendWebGL\n}) {\n  const {inputs, backend, attrs} = args;\n  const {x, filter} = inputs;\n  const {strides, pad, dilations} = attrs;\n\n  const convInfo = backend_util.computeDilation2DInfo(\n      x.shape as [number, number, number, number],\n      filter.shape as [number, number, number], strides, pad,\n      'NHWC' /* dataFormat */, dilations);\n  let out: TensorInfo;\n\n  const program = new Dilation2DProgram(convInfo);\n  out = backend.runWebGLProgram(program, [x, filter], 'float32');\n\n  const outReshaped =\n      reshape({inputs: {x: out}, backend, attrs: {shape: convInfo.outShape}});\n  backend.disposeIntermediateTensorInfo(out);\n\n  return outReshaped;\n}\n\nexport const dilation2DConfig: KernelConfig = {\n  kernelName: Dilation2D,\n  backendName: 'webgl',\n  kernelFunc: dilation2D as unknown as KernelFunc,\n};\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAiBA,SAAQA,YAAY,EAAEC,UAAU,QAAgF,uBAAuB;AAGvI,SAAQC,iBAAiB,QAAO,iBAAiB;AACjD,SAAQC,OAAO,QAAO,WAAW;AAEjC,OAAM,SAAUC,UAAUA,CAACC,IAI1B;EACC,MAAM;IAACC,MAAM;IAAEC,OAAO;IAAEC;EAAK,CAAC,GAAGH,IAAI;EACrC,MAAM;IAACI,CAAC;IAAEC;EAAM,CAAC,GAAGJ,MAAM;EAC1B,MAAM;IAACK,OAAO;IAAEC,GAAG;IAAEC;EAAS,CAAC,GAAGL,KAAK;EAEvC,MAAMM,QAAQ,GAAGd,YAAY,CAACe,qBAAqB,CAC/CN,CAAC,CAACO,KAAyC,EAC3CN,MAAM,CAACM,KAAiC,EAAEL,OAAO,EAAEC,GAAG,EACtD,MAAM,CAAC,kBAAkBC,SAAS,CAAC;EACvC,IAAII,GAAe;EAEnB,MAAMC,OAAO,GAAG,IAAIhB,iBAAiB,CAACY,QAAQ,CAAC;EAC/CG,GAAG,GAAGV,OAAO,CAACY,eAAe,CAACD,OAAO,EAAE,CAACT,CAAC,EAAEC,MAAM,CAAC,EAAE,SAAS,CAAC;EAE9D,MAAMU,WAAW,GACbjB,OAAO,CAAC;IAACG,MAAM,EAAE;MAACG,CAAC,EAAEQ;IAAG,CAAC;IAAEV,OAAO;IAAEC,KAAK,EAAE;MAACQ,KAAK,EAAEF,QAAQ,CAACO;IAAQ;EAAC,CAAC,CAAC;EAC3Ed,OAAO,CAACe,6BAA6B,CAACL,GAAG,CAAC;EAE1C,OAAOG,WAAW;AACpB;AAEA,OAAO,MAAMG,gBAAgB,GAAiB;EAC5CC,UAAU,EAAEvB,UAAU;EACtBwB,WAAW,EAAE,OAAO;EACpBC,UAAU,EAAEtB;CACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}