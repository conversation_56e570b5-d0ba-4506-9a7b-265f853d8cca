{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.transformArguments = exports.FIRST_KEY_INDEX = void 0;\nexports.FIRST_KEY_INDEX = 1;\nfunction transformArguments(key, id, message, options) {\n  const args = ['XADD', key];\n  if (options?.NOMKSTREAM) {\n    args.push('NOMKSTREAM');\n  }\n  if (options?.TRIM) {\n    if (options.TRIM.strategy) {\n      args.push(options.TRIM.strategy);\n    }\n    if (options.TRIM.strategyModifier) {\n      args.push(options.TRIM.strategyModifier);\n    }\n    args.push(options.TRIM.threshold.toString());\n    if (options.TRIM.limit) {\n      args.push('LIMIT', options.TRIM.limit.toString());\n    }\n  }\n  args.push(id);\n  for (const [key, value] of Object.entries(message)) {\n    args.push(key, value);\n  }\n  return args;\n}\nexports.transformArguments = transformArguments;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "transformArguments", "FIRST_KEY_INDEX", "key", "id", "message", "options", "args", "NOMKSTREAM", "push", "TRIM", "strategy", "strategyModifier", "threshold", "toString", "limit", "entries"], "sources": ["C:/tmsft/node_modules/@redis/client/dist/lib/commands/XADD.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.transformArguments = exports.FIRST_KEY_INDEX = void 0;\nexports.FIRST_KEY_INDEX = 1;\nfunction transformArguments(key, id, message, options) {\n    const args = ['XADD', key];\n    if (options?.NOMKSTREAM) {\n        args.push('NOMKSTREAM');\n    }\n    if (options?.TRIM) {\n        if (options.TRIM.strategy) {\n            args.push(options.TRIM.strategy);\n        }\n        if (options.TRIM.strategyModifier) {\n            args.push(options.TRIM.strategyModifier);\n        }\n        args.push(options.TRIM.threshold.toString());\n        if (options.TRIM.limit) {\n            args.push('LIMIT', options.TRIM.limit.toString());\n        }\n    }\n    args.push(id);\n    for (const [key, value] of Object.entries(message)) {\n        args.push(key, value);\n    }\n    return args;\n}\nexports.transformArguments = transformArguments;\n"], "mappings": "AAAA,YAAY;;AACZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEC,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7DD,OAAO,CAACE,kBAAkB,GAAGF,OAAO,CAACG,eAAe,GAAG,KAAK,CAAC;AAC7DH,OAAO,CAACG,eAAe,GAAG,CAAC;AAC3B,SAASD,kBAAkBA,CAACE,GAAG,EAAEC,EAAE,EAAEC,OAAO,EAAEC,OAAO,EAAE;EACnD,MAAMC,IAAI,GAAG,CAAC,MAAM,EAAEJ,GAAG,CAAC;EAC1B,IAAIG,OAAO,EAAEE,UAAU,EAAE;IACrBD,IAAI,CAACE,IAAI,CAAC,YAAY,CAAC;EAC3B;EACA,IAAIH,OAAO,EAAEI,IAAI,EAAE;IACf,IAAIJ,OAAO,CAACI,IAAI,CAACC,QAAQ,EAAE;MACvBJ,IAAI,CAACE,IAAI,CAACH,OAAO,CAACI,IAAI,CAACC,QAAQ,CAAC;IACpC;IACA,IAAIL,OAAO,CAACI,IAAI,CAACE,gBAAgB,EAAE;MAC/BL,IAAI,CAACE,IAAI,CAACH,OAAO,CAACI,IAAI,CAACE,gBAAgB,CAAC;IAC5C;IACAL,IAAI,CAACE,IAAI,CAACH,OAAO,CAACI,IAAI,CAACG,SAAS,CAACC,QAAQ,CAAC,CAAC,CAAC;IAC5C,IAAIR,OAAO,CAACI,IAAI,CAACK,KAAK,EAAE;MACpBR,IAAI,CAACE,IAAI,CAAC,OAAO,EAAEH,OAAO,CAACI,IAAI,CAACK,KAAK,CAACD,QAAQ,CAAC,CAAC,CAAC;IACrD;EACJ;EACAP,IAAI,CAACE,IAAI,CAACL,EAAE,CAAC;EACb,KAAK,MAAM,CAACD,GAAG,EAAEH,KAAK,CAAC,IAAIH,MAAM,CAACmB,OAAO,CAACX,OAAO,CAAC,EAAE;IAChDE,IAAI,CAACE,IAAI,CAACN,GAAG,EAAEH,KAAK,CAAC;EACzB;EACA,OAAOO,IAAI;AACf;AACAR,OAAO,CAACE,kBAAkB,GAAGA,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}