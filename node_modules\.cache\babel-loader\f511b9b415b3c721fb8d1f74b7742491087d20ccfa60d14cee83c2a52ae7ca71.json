{"ast": null, "code": "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { CropAndResize } from '@tensorflow/tfjs-core';\nimport { CropAndResizeProgram } from '../crop_and_resize_gpu';\nexport const cropAndResize = args => {\n  const {\n    inputs,\n    backend,\n    attrs\n  } = args;\n  const {\n    image,\n    boxes,\n    boxInd\n  } = inputs;\n  const {\n    cropSize,\n    method,\n    extrapolationValue\n  } = attrs;\n  const program = new CropAndResizeProgram(image.shape, boxes.shape, cropSize, method, extrapolationValue);\n  return backend.runWebGLProgram(program, [image, boxes, boxInd], 'float32');\n};\nexport const cropAndResizeConfig = {\n  kernelName: CropAndResize,\n  backendName: 'webgl',\n  kernelFunc: cropAndResize\n};", "map": {"version": 3, "names": ["CropAndResize", "CropAndResizeProgram", "cropAndResize", "args", "inputs", "backend", "attrs", "image", "boxes", "boxInd", "cropSize", "method", "extrapolationValue", "program", "shape", "runWebGLProgram", "cropAndResizeConfig", "kernelName", "backendName", "kernelFunc"], "sources": ["C:\\tfjs-backend-webgl\\src\\kernels\\CropAndResize.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {CropAndResize, CropAndResizeAttrs, CropAndResizeInputs, KernelConfig, KernelFunc, TensorInfo} from '@tensorflow/tfjs-core';\n\nimport {MathBackendWebGL} from '../backend_webgl';\nimport {CropAndResizeProgram} from '../crop_and_resize_gpu';\n\nexport const cropAndResize = (args: {\n  inputs: CropAndResizeInputs,\n  backend: MathBackendWebGL,\n  attrs: CropAndResizeAttrs\n}): TensorInfo => {\n  const {inputs, backend, attrs} = args;\n  const {image, boxes, boxInd} = inputs;\n  const {cropSize, method, extrapolationValue} = attrs;\n\n  const program = new CropAndResizeProgram(\n      image.shape as [number, number, number, number],\n      boxes.shape as [number, number], cropSize, method, extrapolationValue);\n  return backend.runWebGLProgram(program, [image, boxes, boxInd], 'float32');\n};\n\nexport const cropAndResizeConfig: KernelConfig = {\n  kernelName: CropAndResize,\n  backendName: 'webgl',\n  kernelFunc: cropAndResize as unknown as KernelFunc\n};\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAiBA,SAAQA,aAAa,QAAsF,uBAAuB;AAGlI,SAAQC,oBAAoB,QAAO,wBAAwB;AAE3D,OAAO,MAAMC,aAAa,GAAIC,IAI7B,IAAgB;EACf,MAAM;IAACC,MAAM;IAAEC,OAAO;IAAEC;EAAK,CAAC,GAAGH,IAAI;EACrC,MAAM;IAACI,KAAK;IAAEC,KAAK;IAAEC;EAAM,CAAC,GAAGL,MAAM;EACrC,MAAM;IAACM,QAAQ;IAAEC,MAAM;IAAEC;EAAkB,CAAC,GAAGN,KAAK;EAEpD,MAAMO,OAAO,GAAG,IAAIZ,oBAAoB,CACpCM,KAAK,CAACO,KAAyC,EAC/CN,KAAK,CAACM,KAAyB,EAAEJ,QAAQ,EAAEC,MAAM,EAAEC,kBAAkB,CAAC;EAC1E,OAAOP,OAAO,CAACU,eAAe,CAACF,OAAO,EAAE,CAACN,KAAK,EAAEC,KAAK,EAAEC,MAAM,CAAC,EAAE,SAAS,CAAC;AAC5E,CAAC;AAED,OAAO,MAAMO,mBAAmB,GAAiB;EAC/CC,UAAU,EAAEjB,aAAa;EACzBkB,WAAW,EAAE,OAAO;EACpBC,UAAU,EAAEjB;CACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}