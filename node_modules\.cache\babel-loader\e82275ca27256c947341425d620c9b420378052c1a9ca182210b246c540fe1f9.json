{"ast": null, "code": "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the License);\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an AS IS BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { Relu } from '@tensorflow/tfjs-core';\nimport { unaryKernelFunc } from '../utils/unary_utils';\nexport const relu = unaryKernelFunc(Relu, xi => Math.max(0, xi));\nexport const reluConfig = {\n  kernelName: Relu,\n  backendName: 'cpu',\n  kernelFunc: relu\n};", "map": {"version": 3, "names": ["<PERSON><PERSON>", "unaryKernelFunc", "relu", "xi", "Math", "max", "reluConfig", "kernelName", "backendName", "kernelFunc"], "sources": ["C:\\tfjs-backend-cpu\\src\\kernels\\Relu.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the License);\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an AS IS BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {KernelConfig, Relu} from '@tensorflow/tfjs-core';\n\nimport {unaryKernelFunc} from '../utils/unary_utils';\n\nexport const relu = unaryKernelFunc(Relu, (xi) => Math.max(0, xi));\n\nexport const reluConfig: KernelConfig = {\n  kernelName: Relu,\n  backendName: 'cpu',\n  kernelFunc: relu,\n};\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAiBA,SAAsBA,IAAI,QAAO,uBAAuB;AAExD,SAAQC,eAAe,QAAO,sBAAsB;AAEpD,OAAO,MAAMC,IAAI,GAAGD,eAAe,CAACD,IAAI,EAAGG,EAAE,IAAKC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEF,EAAE,CAAC,CAAC;AAElE,OAAO,MAAMG,UAAU,GAAiB;EACtCC,UAAU,EAAEP,IAAI;EAChBQ,WAAW,EAAE,KAAK;EAClBC,UAAU,EAAEP;CACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}