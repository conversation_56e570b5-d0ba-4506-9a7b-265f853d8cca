{"ast": null, "code": "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { Pack, util } from '@tensorflow/tfjs-core';\nimport { concat } from './Concat';\nimport { expandDims } from './ExpandDims';\nexport function pack(args) {\n  const {\n    inputs,\n    backend,\n    attrs\n  } = args;\n  const {\n    axis\n  } = attrs;\n  if (inputs.length === 1) {\n    return expandDims({\n      inputs: {\n        input: inputs[0]\n      },\n      backend,\n      attrs: {\n        dim: axis\n      }\n    });\n  }\n  const shape = inputs[0].shape;\n  const dtype = inputs[0].dtype;\n  inputs.forEach(t => {\n    util.assertShapesMatch(shape, t.shape, 'All tensors passed to stack must have matching shapes');\n    util.assert(dtype === t.dtype, () => 'All tensors passed to stack must have matching dtypes');\n  });\n  const intermediateTensorInfos = [];\n  const expandedTensors = inputs.map(t => {\n    const expandedT = expandDims({\n      inputs: {\n        input: t\n      },\n      backend,\n      attrs: {\n        dim: axis\n      }\n    });\n    intermediateTensorInfos.push(expandedT);\n    return expandedT;\n  });\n  const result = concat({\n    inputs: expandedTensors,\n    backend,\n    attrs: {\n      axis\n    }\n  });\n  intermediateTensorInfos.forEach(t => backend.disposeIntermediateTensorInfo(t));\n  return result;\n}\nexport const packConfig = {\n  kernelName: Pack,\n  backendName: 'webgl',\n  kernelFunc: pack\n};", "map": {"version": 3, "names": ["Pack", "util", "concat", "expandDims", "pack", "args", "inputs", "backend", "attrs", "axis", "length", "input", "dim", "shape", "dtype", "for<PERSON>ach", "t", "assertShapesMatch", "assert", "intermediateTensorInfos", "expandedTensors", "map", "expandedT", "push", "result", "disposeIntermediateTensorInfo", "packConfig", "kernelName", "backendName", "kernelFunc"], "sources": ["C:\\tfjs-backend-webgl\\src\\kernels\\Pack.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {KernelConfig, KernelFunc, Pack, PackAttrs, PackInputs, TensorInfo, util} from '@tensorflow/tfjs-core';\n\nimport {MathBackendWebGL} from '../backend_webgl';\nimport {concat} from './Concat';\nimport {expandDims} from './ExpandDims';\n\nexport function pack(\n    args: {inputs: PackInputs, backend: MathBackendWebGL, attrs: PackAttrs}):\n    TensorInfo {\n  const {inputs, backend, attrs} = args;\n  const {axis} = attrs;\n\n  if (inputs.length === 1) {\n    return expandDims(\n        {inputs: {input: inputs[0]}, backend, attrs: {dim: axis}});\n  }\n\n  const shape = inputs[0].shape;\n  const dtype = inputs[0].dtype;\n\n  inputs.forEach(t => {\n    util.assertShapesMatch(\n        shape, t.shape,\n        'All tensors passed to stack must have matching shapes');\n    util.assert(\n        dtype === t.dtype,\n        () => 'All tensors passed to stack must have matching dtypes');\n  });\n\n  const intermediateTensorInfos: TensorInfo[] = [];\n  const expandedTensors = inputs.map(t => {\n    const expandedT =\n        expandDims({inputs: {input: t}, backend, attrs: {dim: axis}});\n    intermediateTensorInfos.push(expandedT);\n    return expandedT;\n  });\n\n  const result = concat({inputs: expandedTensors, backend, attrs: {axis}});\n\n  intermediateTensorInfos.forEach(\n      t => backend.disposeIntermediateTensorInfo(t));\n\n  return result;\n}\n\nexport const packConfig: KernelConfig = {\n  kernelName: Pack,\n  backendName: 'webgl',\n  kernelFunc: pack as unknown as KernelFunc\n};\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAiBA,SAAkCA,IAAI,EAAqCC,IAAI,QAAO,uBAAuB;AAG7G,SAAQC,MAAM,QAAO,UAAU;AAC/B,SAAQC,UAAU,QAAO,cAAc;AAEvC,OAAM,SAAUC,IAAIA,CAChBC,IAAuE;EAEzE,MAAM;IAACC,MAAM;IAAEC,OAAO;IAAEC;EAAK,CAAC,GAAGH,IAAI;EACrC,MAAM;IAACI;EAAI,CAAC,GAAGD,KAAK;EAEpB,IAAIF,MAAM,CAACI,MAAM,KAAK,CAAC,EAAE;IACvB,OAAOP,UAAU,CACb;MAACG,MAAM,EAAE;QAACK,KAAK,EAAEL,MAAM,CAAC,CAAC;MAAC,CAAC;MAAEC,OAAO;MAAEC,KAAK,EAAE;QAACI,GAAG,EAAEH;MAAI;IAAC,CAAC,CAAC;;EAGhE,MAAMI,KAAK,GAAGP,MAAM,CAAC,CAAC,CAAC,CAACO,KAAK;EAC7B,MAAMC,KAAK,GAAGR,MAAM,CAAC,CAAC,CAAC,CAACQ,KAAK;EAE7BR,MAAM,CAACS,OAAO,CAACC,CAAC,IAAG;IACjBf,IAAI,CAACgB,iBAAiB,CAClBJ,KAAK,EAAEG,CAAC,CAACH,KAAK,EACd,uDAAuD,CAAC;IAC5DZ,IAAI,CAACiB,MAAM,CACPJ,KAAK,KAAKE,CAAC,CAACF,KAAK,EACjB,MAAM,uDAAuD,CAAC;EACpE,CAAC,CAAC;EAEF,MAAMK,uBAAuB,GAAiB,EAAE;EAChD,MAAMC,eAAe,GAAGd,MAAM,CAACe,GAAG,CAACL,CAAC,IAAG;IACrC,MAAMM,SAAS,GACXnB,UAAU,CAAC;MAACG,MAAM,EAAE;QAACK,KAAK,EAAEK;MAAC,CAAC;MAAET,OAAO;MAAEC,KAAK,EAAE;QAACI,GAAG,EAAEH;MAAI;IAAC,CAAC,CAAC;IACjEU,uBAAuB,CAACI,IAAI,CAACD,SAAS,CAAC;IACvC,OAAOA,SAAS;EAClB,CAAC,CAAC;EAEF,MAAME,MAAM,GAAGtB,MAAM,CAAC;IAACI,MAAM,EAAEc,eAAe;IAAEb,OAAO;IAAEC,KAAK,EAAE;MAACC;IAAI;EAAC,CAAC,CAAC;EAExEU,uBAAuB,CAACJ,OAAO,CAC3BC,CAAC,IAAIT,OAAO,CAACkB,6BAA6B,CAACT,CAAC,CAAC,CAAC;EAElD,OAAOQ,MAAM;AACf;AAEA,OAAO,MAAME,UAAU,GAAiB;EACtCC,UAAU,EAAE3B,IAAI;EAChB4B,WAAW,EAAE,OAAO;EACpBC,UAAU,EAAEzB;CACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}