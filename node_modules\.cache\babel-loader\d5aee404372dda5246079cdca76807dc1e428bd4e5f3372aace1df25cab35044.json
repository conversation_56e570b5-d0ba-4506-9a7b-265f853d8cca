{"ast": null, "code": "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { MaxPool } from '../kernel_names';\nimport { maxPoolGrad } from '../ops/max_pool_grad';\nexport const maxPoolGradConfig = {\n  kernelName: MaxPool,\n  inputsToSave: ['x'],\n  outputsToSave: [true],\n  gradFunc: (dy, saved, attrs) => {\n    const [x, y] = saved;\n    const {\n      filterSize,\n      strides,\n      pad\n    } = attrs;\n    return {\n      x: () => maxPoolGrad(dy, x, y, filterSize, strides, pad)\n    };\n  }\n};", "map": {"version": 3, "names": ["MaxPool", "maxPoolGrad", "maxPoolGradConfig", "kernelName", "inputsToSave", "outputsToSave", "grad<PERSON>unc", "dy", "saved", "attrs", "x", "y", "filterSize", "strides", "pad"], "sources": ["C:\\tfjs-core\\src\\gradients\\MaxPool_grad.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {MaxPool, MaxPoolAttrs} from '../kernel_names';\nimport {GradConfig, NamedAttrMap} from '../kernel_registry';\nimport {maxPoolGrad} from '../ops/max_pool_grad';\nimport {Tensor, Tensor4D} from '../tensor';\n\nexport const maxPoolGradConfig: GradConfig = {\n  kernelName: MaxPool,\n  inputsToSave: ['x'],\n  outputsToSave: [true],\n  gradFunc: (dy: Tensor, saved: Tensor[], attrs: NamedAttrMap) => {\n    const [x, y] = saved as [Tensor4D, Tensor4D];\n    const {filterSize, strides, pad} = attrs as unknown as MaxPoolAttrs;\n\n    return {\n      x: () => maxPoolGrad(dy as Tensor4D, x, y, filterSize, strides, pad)\n    };\n  }\n};\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAiBA,SAAQA,OAAO,QAAqB,iBAAiB;AAErD,SAAQC,WAAW,QAAO,sBAAsB;AAGhD,OAAO,MAAMC,iBAAiB,GAAe;EAC3CC,UAAU,EAAEH,OAAO;EACnBI,YAAY,EAAE,CAAC,GAAG,CAAC;EACnBC,aAAa,EAAE,CAAC,IAAI,CAAC;EACrBC,QAAQ,EAAEA,CAACC,EAAU,EAAEC,KAAe,EAAEC,KAAmB,KAAI;IAC7D,MAAM,CAACC,CAAC,EAAEC,CAAC,CAAC,GAAGH,KAA6B;IAC5C,MAAM;MAACI,UAAU;MAAEC,OAAO;MAAEC;IAAG,CAAC,GAAGL,KAAgC;IAEnE,OAAO;MACLC,CAAC,EAAEA,CAAA,KAAMT,WAAW,CAACM,EAAc,EAAEG,CAAC,EAAEC,CAAC,EAAEC,UAAU,EAAEC,OAAO,EAAEC,GAAG;KACpE;EACH;CACD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}