{"ast": null, "code": "// CSV Template Configuration\nexport const CSV_TEMPLATE = {\n  headers: ['Bank reference', 'Narrative', 'Customer reference', 'TRN type', 'Value date', 'Credit amount', 'Debit amount', 'Time', 'Post date', 'Balance'],\n  validationRules: [{\n    field: 'Bank reference',\n    rule: 'required',\n    message: 'Bank reference is required'\n  }, {\n    field: 'Narrative',\n    rule: 'required',\n    message: 'Narrative is required'\n  }, {\n    field: 'TRN type',\n    rule: 'required',\n    message: 'Transaction type is required'\n  }, {\n    field: 'Value date',\n    rule: 'required',\n    message: 'Value date is required'\n  }, {\n    field: 'Value date',\n    rule: 'date',\n    message: 'Value date must be in valid format (DD/MM/YYYY)'\n  }, {\n    field: 'Post date',\n    rule: 'required',\n    message: 'Post date is required'\n  }, {\n    field: 'Post date',\n    rule: 'date',\n    message: 'Post date must be in valid format (DD/MM/YYYY)'\n  }, {\n    field: 'Credit amount',\n    rule: 'number',\n    message: 'Credit amount must be a valid number'\n  }, {\n    field: 'Debit amount',\n    rule: 'number',\n    message: 'Debit amount must be a valid number'\n  }, {\n    field: 'Balance',\n    rule: 'required',\n    message: 'Balance is required'\n  }, {\n    field: 'Balance',\n    rule: 'number',\n    message: 'Balance must be a valid number'\n  }],\n  sampleData: [{\n    'Bank reference': '829471Z01W0W',\n    'Narrative': 'HELLMANN SAUDI ARABIA TFR+',\n    'Customer reference': 'INV241221276270',\n    'TRN type': 'Transfer',\n    'Value date': '31/12/2024',\n    'Credit amount': '5,444.10',\n    'Debit amount': '',\n    'Time': '19:05',\n    'Post date': '31/12/2024',\n    'Balance': '20,784,406.54'\n  }, {\n    'Bank reference': 'LP CPM4057MZ',\n    'Narrative': 'TECHNICAL LINKS SERVICES COMPANY',\n    'Customer reference': '609720',\n    'TRN type': 'Transfer',\n    'Value date': '30/12/2024',\n    'Credit amount': '',\n    'Debit amount': '15,000,000.00',\n    'Time': '12:45',\n    'Post date': '30/12/2024',\n    'Balance': '15,010,179.45'\n  }, {\n    'Bank reference': 'SDR5-00029',\n    'Narrative': 'SEVING THOMAS THDMDIYIL SADAD',\n    'Customer reference': 'NONREF',\n    'TRN type': 'Cheque',\n    'Value date': '01/01/2025',\n    'Credit amount': '31,050.00',\n    'Debit amount': '',\n    'Time': '11:27',\n    'Post date': '30/12/2024',\n    'Balance': '33,922,117.24'\n  }]\n};\nclass CSVProcessingService {\n  // Generate CSV template for download\n  generateTemplate() {\n    const headers = CSV_TEMPLATE.headers.join(',');\n    const rows = CSV_TEMPLATE.sampleData.map(row => CSV_TEMPLATE.headers.map(header => `\"${row[header] || ''}\"`).join(','));\n    return [headers, ...rows].join('\\n');\n  }\n\n  // Download CSV template\n  downloadTemplate() {\n    const csvContent = this.generateTemplate();\n    const blob = new Blob([csvContent], {\n      type: 'text/csv;charset=utf-8;'\n    });\n    const link = document.createElement('a');\n    if (link.download !== undefined) {\n      const url = URL.createObjectURL(blob);\n      link.setAttribute('href', url);\n      link.setAttribute('download', 'bank_statement_template.csv');\n      link.style.visibility = 'hidden';\n      document.body.appendChild(link);\n      link.click();\n      document.body.removeChild(link);\n    }\n  }\n\n  // Parse CSV content\n  async parseCSV(file) {\n    return new Promise((resolve, reject) => {\n      const reader = new FileReader();\n      reader.onload = event => {\n        try {\n          var _event$target;\n          const csv = (_event$target = event.target) === null || _event$target === void 0 ? void 0 : _event$target.result;\n          const rows = this.parseCSVContent(csv);\n          resolve(rows);\n        } catch (error) {\n          reject(error);\n        }\n      };\n      reader.onerror = () => reject(new Error('Failed to read file'));\n      reader.readAsText(file);\n    });\n  }\n  parseCSVContent(csv) {\n    const lines = csv.split('\\n').filter(line => line.trim());\n    if (lines.length < 2) throw new Error('CSV must contain at least a header and one data row');\n    const headers = this.parseCSVLine(lines[0]).map(h => h.trim());\n    const expectedHeaders = ['bank reference', 'narrative', 'customer reference', 'trn type', 'value date', 'credit amount', 'debit amount', 'time', 'post date', 'balance'];\n\n    // Validate headers (case-insensitive)\n    const normalizedHeaders = headers.map(h => h.toLowerCase());\n    const missingHeaders = expectedHeaders.filter(h => !normalizedHeaders.includes(h));\n    if (missingHeaders.length > 0) {\n      throw new Error(`Missing required headers: ${missingHeaders.join(', ')}`);\n    }\n\n    // Create header mapping\n    const headerMap = {};\n    expectedHeaders.forEach(expectedHeader => {\n      const index = normalizedHeaders.findIndex(h => h === expectedHeader);\n      headerMap[expectedHeader.replace(' ', '')] = index;\n    });\n\n    // Parse data rows\n    const dataRows = [];\n    for (let i = 1; i < lines.length; i++) {\n      var _values$headerMap$ban, _values$headerMap$nar, _values$headerMap$cus, _values$headerMap$trn, _values$headerMap$val, _values$headerMap$cre, _values$headerMap$deb, _values$headerMap$tim, _values$headerMap$pos, _values$headerMap$bal;\n      const values = this.parseCSVLine(lines[i]);\n      if (values.length === 0 || values.every(v => !v.trim())) continue; // Skip empty rows\n\n      const row = {\n        bankReference: ((_values$headerMap$ban = values[headerMap.bankreference]) === null || _values$headerMap$ban === void 0 ? void 0 : _values$headerMap$ban.trim()) || '',\n        narrative: ((_values$headerMap$nar = values[headerMap.narrative]) === null || _values$headerMap$nar === void 0 ? void 0 : _values$headerMap$nar.trim()) || '',\n        customerReference: ((_values$headerMap$cus = values[headerMap.customerreference]) === null || _values$headerMap$cus === void 0 ? void 0 : _values$headerMap$cus.trim()) || '',\n        trnType: ((_values$headerMap$trn = values[headerMap.trntype]) === null || _values$headerMap$trn === void 0 ? void 0 : _values$headerMap$trn.trim()) || '',\n        valueDate: ((_values$headerMap$val = values[headerMap.valuedate]) === null || _values$headerMap$val === void 0 ? void 0 : _values$headerMap$val.trim()) || '',\n        creditAmount: ((_values$headerMap$cre = values[headerMap.creditamount]) === null || _values$headerMap$cre === void 0 ? void 0 : _values$headerMap$cre.trim()) || '',\n        debitAmount: ((_values$headerMap$deb = values[headerMap.debitamount]) === null || _values$headerMap$deb === void 0 ? void 0 : _values$headerMap$deb.trim()) || '',\n        time: ((_values$headerMap$tim = values[headerMap.time]) === null || _values$headerMap$tim === void 0 ? void 0 : _values$headerMap$tim.trim()) || '',\n        postDate: ((_values$headerMap$pos = values[headerMap.postdate]) === null || _values$headerMap$pos === void 0 ? void 0 : _values$headerMap$pos.trim()) || '',\n        balance: ((_values$headerMap$bal = values[headerMap.balance]) === null || _values$headerMap$bal === void 0 ? void 0 : _values$headerMap$bal.trim()) || ''\n      };\n      dataRows.push(row);\n    }\n    return dataRows;\n  }\n  parseCSVLine(line) {\n    const result = [];\n    let current = '';\n    let inQuotes = false;\n    for (let i = 0; i < line.length; i++) {\n      const char = line[i];\n      if (char === '\"') {\n        if (inQuotes && line[i + 1] === '\"') {\n          current += '\"';\n          i++; // Skip next quote\n        } else {\n          inQuotes = !inQuotes;\n        }\n      } else if (char === ',' && !inQuotes) {\n        result.push(current);\n        current = '';\n      } else {\n        current += char;\n      }\n    }\n    result.push(current);\n    return result;\n  }\n\n  // Validate CSV data\n  validateCSVData(rows) {\n    const errors = [];\n    rows.forEach((row, index) => {\n      const rowNumber = index + 2; // +2 because index is 0-based and we skip header\n\n      // Validate each field\n      CSV_TEMPLATE.validationRules.forEach(rule => {\n        const fieldValue = this.getFieldValue(row, rule.field);\n        const error = this.validateField(fieldValue, rule, rowNumber);\n        if (error) errors.push(error);\n      });\n\n      // For bank statements, we trust the balance field as it represents the actual account balance\n      // Balance validation is disabled for bank statement imports as the data comes directly from the bank\n      // and may not be in strict chronological order or may include pending transactions\n    });\n    return errors;\n  }\n  getFieldValue(row, field) {\n    switch (field.toLowerCase()) {\n      case 'bank reference':\n        return row.bankReference;\n      case 'narrative':\n        return row.narrative;\n      case 'customer reference':\n        return row.customerReference;\n      case 'trn type':\n        return row.trnType;\n      case 'value date':\n        return row.valueDate;\n      case 'credit amount':\n        return row.creditAmount;\n      case 'debit amount':\n        return row.debitAmount;\n      case 'time':\n        return row.time;\n      case 'post date':\n        return row.postDate;\n      case 'balance':\n        return row.balance;\n      default:\n        return '';\n    }\n  }\n  validateField(value, rule, rowNumber) {\n    switch (rule.rule) {\n      case 'required':\n        if (!value || value.trim() === '') {\n          return {\n            row: rowNumber,\n            field: rule.field,\n            message: rule.message,\n            value\n          };\n        }\n        break;\n      case 'number':\n        if (value.trim() && isNaN(this.parseAmount(value))) {\n          return {\n            row: rowNumber,\n            field: rule.field,\n            message: rule.message,\n            value\n          };\n        }\n        break;\n      case 'date':\n        if (value.trim() && !this.isValidDate(value)) {\n          return {\n            row: rowNumber,\n            field: rule.field,\n            message: rule.message,\n            value\n          };\n        }\n        break;\n      case 'positive':\n        if (value.trim() && this.parseAmount(value) < 0) {\n          return {\n            row: rowNumber,\n            field: rule.field,\n            message: rule.message,\n            value\n          };\n        }\n        break;\n    }\n    return null;\n  }\n  parseAmount(value) {\n    if (!value || value.trim() === '') return 0;\n\n    // Remove currency symbols, commas, and quotes, handle negative values\n    const cleaned = value.replace(/[$,\\s\"]/g, '');\n\n    // Handle negative values (with minus sign or parentheses)\n    const isNegative = cleaned.startsWith('-') || cleaned.startsWith('(') && cleaned.endsWith(')');\n    const numericValue = cleaned.replace(/[-()]/g, '');\n    const amount = parseFloat(numericValue);\n    if (isNaN(amount)) return 0;\n    return isNegative ? -amount : amount;\n  }\n  isValidDate(dateString) {\n    // Handle DD/MM/YYYY format (Saudi bank format)\n    if (dateString.includes('/')) {\n      const parts = dateString.split('/');\n      if (parts.length === 3) {\n        const day = parseInt(parts[0]);\n        const month = parseInt(parts[1]);\n        const year = parseInt(parts[2]);\n\n        // Basic range validation\n        if (day >= 1 && day <= 31 && month >= 1 && month <= 12 && year >= 1900 && year <= 2100) {\n          // Create date object to validate the actual date (handles leap years, etc.)\n          const date = new Date(year, month - 1, day);\n          return date.getFullYear() === year && date.getMonth() === month - 1 && date.getDate() === day;\n        }\n      }\n    }\n\n    // Fallback to original logic\n    const date = new Date(dateString);\n    return date instanceof Date && !isNaN(date.getTime());\n  }\n\n  // Convert CSV rows to transactions\n  convertToTransactions(rows) {\n    return rows.map((row, index) => ({\n      id: `temp_${Date.now()}_${index}`,\n      date: this.formatDate(row.postDate),\n      description: row.narrative,\n      debitAmount: Math.abs(this.parseAmount(row.debitAmount)),\n      // Ensure debit amounts are positive for display\n      creditAmount: Math.abs(this.parseAmount(row.creditAmount)),\n      // Ensure credit amounts are positive for display\n      balance: this.parseAmount(row.balance),\n      reference: row.customerReference,\n      postDate: row.postDate,\n      time: row.time,\n      valueDate: row.valueDate\n    }));\n  }\n  formatDate(dateString) {\n    // Handle DD/MM/YYYY format from bank statements (Saudi bank format)\n    if (dateString.includes('/')) {\n      const parts = dateString.split('/');\n      if (parts.length === 3) {\n        const day = parts[0].padStart(2, '0');\n        const month = parts[1].padStart(2, '0');\n        const year = parts[2];\n\n        // Validate the date parts\n        const dayNum = parseInt(day);\n        const monthNum = parseInt(month);\n        const yearNum = parseInt(year);\n        if (dayNum >= 1 && dayNum <= 31 && monthNum >= 1 && monthNum <= 12 && yearNum >= 1900) {\n          // Convert DD/MM/YYYY to YYYY-MM-DD format\n          return `${year}-${month}-${day}`;\n        }\n      }\n    }\n\n    // Fallback to original logic\n    const date = new Date(dateString);\n    return date.toISOString().split('T')[0];\n  }\n\n  // Helper method to create a sortable datetime from Post date and Time\n  createSortableDateTime(postDate, time) {\n    const formattedDate = this.formatDate(postDate);\n\n    // Handle time format (HH:MM)\n    let timeString = '00:00';\n    if (time && time.trim()) {\n      timeString = time.trim();\n      // Ensure time is in HH:MM format\n      if (timeString.length === 4 && !timeString.includes(':')) {\n        // Convert HHMM to HH:MM\n        timeString = timeString.substring(0, 2) + ':' + timeString.substring(2);\n      }\n    }\n\n    // Combine date and time\n    const dateTimeString = `${formattedDate}T${timeString}:00`;\n    return new Date(dateTimeString);\n  }\n\n  // Process file and generate import summary\n  async processFile(file) {\n    try {\n      const rows = await this.parseCSV(file);\n      const validationErrors = this.validateCSVData(rows);\n      const transactions = this.convertToTransactions(rows);\n\n      // Sort transactions by Post date and time (newest first, matching bank statement order)\n      const sortedTransactions = transactions.sort((a, b) => {\n        const dateA = this.createSortableDateTime(a.postDate || a.date, a.time || '00:00');\n        const dateB = this.createSortableDateTime(b.postDate || b.date, b.time || '00:00');\n        return dateB.getTime() - dateA.getTime(); // Newest first (most recent transaction first)\n      });\n      const totalDebitAmount = transactions.reduce((sum, t) => sum + t.debitAmount, 0);\n      const totalCreditAmount = transactions.reduce((sum, t) => sum + t.creditAmount, 0);\n\n      // The closing balance is from the FIRST transaction (most recent) since bank statements are in reverse chronological order\n      const closingBalance = transactions.length > 0 ? transactions[0].balance : 0;\n\n      // Calculate opening balance from the LAST transaction (oldest)\n      const openingBalance = transactions.length > 0 ? transactions[transactions.length - 1].balance : 0;\n\n      // Calculate daily movement (net change)\n      const dailyMovement = closingBalance - openingBalance;\n      return {\n        fileName: file.name,\n        totalTransactions: transactions.length,\n        totalDebitAmount,\n        totalCreditAmount,\n        closingBalance,\n        openingBalance,\n        dailyMovement,\n        validationErrors,\n        transactions: sortedTransactions,\n        dateRange: {\n          from: transactions.length > 0 ? sortedTransactions[sortedTransactions.length - 1].date : '',\n          to: transactions.length > 0 ? sortedTransactions[0].date : ''\n        }\n      };\n    } catch (error) {\n      throw new Error(`Failed to process ${file.name}: ${error instanceof Error ? error.message : 'Unknown error'}`);\n    }\n  }\n}\nexport const csvProcessingService = new CSVProcessingService();", "map": {"version": 3, "names": ["CSV_TEMPLATE", "headers", "validationRules", "field", "rule", "message", "sampleData", "CSVProcessingService", "generateTemplate", "join", "rows", "map", "row", "header", "downloadTemplate", "csv<PERSON><PERSON>nt", "blob", "Blob", "type", "link", "document", "createElement", "download", "undefined", "url", "URL", "createObjectURL", "setAttribute", "style", "visibility", "body", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "parseCSV", "file", "Promise", "resolve", "reject", "reader", "FileReader", "onload", "event", "_event$target", "csv", "target", "result", "parseCS<PERSON><PERSON>nt", "error", "onerror", "Error", "readAsText", "lines", "split", "filter", "line", "trim", "length", "parseCSVLine", "h", "expectedHeaders", "normalizedHeaders", "toLowerCase", "missingHeaders", "includes", "headerMap", "for<PERSON>ach", "expected<PERSON>eader", "index", "findIndex", "replace", "dataRows", "i", "_values$headerMap$ban", "_values$headerMap$nar", "_values$headerMap$cus", "_values$headerMap$trn", "_values$headerMap$val", "_values$headerMap$cre", "_values$headerMap$deb", "_values$headerMap$tim", "_values$headerMap$pos", "_values$headerMap$bal", "values", "every", "v", "bankReference", "bankreference", "narrative", "customerReference", "customerreference", "trnType", "trntype", "valueDate", "valuedate", "creditAmount", "creditamount", "debitAmount", "debitamount", "time", "postDate", "postdate", "balance", "push", "current", "inQuotes", "char", "validateCSVData", "errors", "rowNumber", "fieldValue", "getFieldValue", "validateField", "value", "isNaN", "parseAmount", "isValidDate", "cleaned", "isNegative", "startsWith", "endsWith", "numericValue", "amount", "parseFloat", "dateString", "parts", "day", "parseInt", "month", "year", "date", "Date", "getFullYear", "getMonth", "getDate", "getTime", "convertToTransactions", "id", "now", "formatDate", "description", "Math", "abs", "reference", "padStart", "day<PERSON>um", "monthNum", "yearNum", "toISOString", "createSortableDateTime", "formattedDate", "timeString", "substring", "dateTimeString", "processFile", "validationErrors", "transactions", "sortedTransactions", "sort", "a", "b", "dateA", "dateB", "totalDebitAmount", "reduce", "sum", "t", "totalCreditAmount", "closingBalance", "openingBalance", "dailyMovement", "fileName", "name", "totalTransactions", "date<PERSON><PERSON><PERSON>", "from", "to", "csvProcessingService"], "sources": ["C:/tmsft/src/services/csvProcessingService.ts"], "sourcesContent": ["import { CSVRow, Transaction, ImportSummary, ValidationError, ValidationRule, CSVTemplate } from '../types';\r\n\r\n// CSV Template Configuration\r\nexport const CSV_TEMPLATE: CSVTemplate = {\r\n  headers: ['Bank reference', 'Narrative', 'Customer reference', 'TRN type', 'Value date', 'Credit amount', 'Debit amount', 'Time', 'Post date', 'Balance'],\r\n  validationRules: [\r\n    { field: 'Bank reference', rule: 'required', message: 'Bank reference is required' },\r\n    { field: 'Narrative', rule: 'required', message: 'Narrative is required' },\r\n    { field: 'TRN type', rule: 'required', message: 'Transaction type is required' },\r\n    { field: 'Value date', rule: 'required', message: 'Value date is required' },\r\n    { field: 'Value date', rule: 'date', message: 'Value date must be in valid format (DD/MM/YYYY)' },\r\n    { field: 'Post date', rule: 'required', message: 'Post date is required' },\r\n    { field: 'Post date', rule: 'date', message: 'Post date must be in valid format (DD/MM/YYYY)' },\r\n    { field: 'Credit amount', rule: 'number', message: 'Credit amount must be a valid number' },\r\n    { field: 'Debit amount', rule: 'number', message: 'Debit amount must be a valid number' },\r\n    { field: 'Balance', rule: 'required', message: 'Balance is required' },\r\n    { field: 'Balance', rule: 'number', message: 'Balance must be a valid number' },\r\n\r\n  ],\r\n  sampleData: [\r\n    {\r\n      'Bank reference': '829471Z01W0W',\r\n      'Narrative': 'HELLMANN SAUDI ARABIA TFR+',\r\n      'Customer reference': 'INV241221276270',\r\n      'TRN type': 'Transfer',\r\n      'Value date': '31/12/2024',\r\n      'Credit amount': '5,444.10',\r\n      'Debit amount': '',\r\n      'Time': '19:05',\r\n      'Post date': '31/12/2024',\r\n      'Balance': '20,784,406.54'\r\n    },\r\n    {\r\n      'Bank reference': 'LP CPM4057MZ',\r\n      'Narrative': 'TECHNICAL LINKS SERVICES COMPANY',\r\n      'Customer reference': '609720',\r\n      'TRN type': 'Transfer',\r\n      'Value date': '30/12/2024',\r\n      'Credit amount': '',\r\n      'Debit amount': '15,000,000.00',\r\n      'Time': '12:45',\r\n      'Post date': '30/12/2024',\r\n      'Balance': '15,010,179.45'\r\n    },\r\n    {\r\n      'Bank reference': 'SDR5-00029',\r\n      'Narrative': 'SEVING THOMAS THDMDIYIL SADAD',\r\n      'Customer reference': 'NONREF',\r\n      'TRN type': 'Cheque',\r\n      'Value date': '01/01/2025',\r\n      'Credit amount': '31,050.00',\r\n      'Debit amount': '',\r\n      'Time': '11:27',\r\n      'Post date': '30/12/2024',\r\n      'Balance': '33,922,117.24'\r\n    }\r\n  ]\r\n};\r\n\r\nclass CSVProcessingService {\r\n  \r\n  // Generate CSV template for download\r\n  generateTemplate(): string {\r\n    const headers = CSV_TEMPLATE.headers.join(',');\r\n    const rows = CSV_TEMPLATE.sampleData.map(row => \r\n      CSV_TEMPLATE.headers.map(header => `\"${row[header] || ''}\"`).join(',')\r\n    );\r\n    \r\n    return [headers, ...rows].join('\\n');\r\n  }\r\n\r\n  // Download CSV template\r\n  downloadTemplate(): void {\r\n    const csvContent = this.generateTemplate();\r\n    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });\r\n    const link = document.createElement('a');\r\n    \r\n    if (link.download !== undefined) {\r\n      const url = URL.createObjectURL(blob);\r\n      link.setAttribute('href', url);\r\n      link.setAttribute('download', 'bank_statement_template.csv');\r\n      link.style.visibility = 'hidden';\r\n      document.body.appendChild(link);\r\n      link.click();\r\n      document.body.removeChild(link);\r\n    }\r\n  }\r\n\r\n  // Parse CSV content\r\n  async parseCSV(file: File): Promise<CSVRow[]> {\r\n    return new Promise((resolve, reject) => {\r\n      const reader = new FileReader();\r\n      \r\n      reader.onload = (event) => {\r\n        try {\r\n          const csv = event.target?.result as string;\r\n          const rows = this.parseCSVContent(csv);\r\n          resolve(rows);\r\n        } catch (error) {\r\n          reject(error);\r\n        }\r\n      };\r\n      \r\n      reader.onerror = () => reject(new Error('Failed to read file'));\r\n      reader.readAsText(file);\r\n    });\r\n  }\r\n\r\n  private parseCSVContent(csv: string): CSVRow[] {\r\n    const lines = csv.split('\\n').filter(line => line.trim());\r\n    if (lines.length < 2) throw new Error('CSV must contain at least a header and one data row');\r\n    \r\n    const headers = this.parseCSVLine(lines[0]).map(h => h.trim());\r\n    const expectedHeaders = [\r\n      'bank reference',\r\n      'narrative', \r\n      'customer reference',\r\n      'trn type',\r\n      'value date',\r\n      'credit amount',\r\n      'debit amount',\r\n      'time',\r\n      'post date',\r\n      'balance'\r\n    ];\r\n    \r\n    // Validate headers (case-insensitive)\r\n    const normalizedHeaders = headers.map(h => h.toLowerCase());\r\n    const missingHeaders = expectedHeaders.filter(h => !normalizedHeaders.includes(h));\r\n    \r\n    if (missingHeaders.length > 0) {\r\n      throw new Error(`Missing required headers: ${missingHeaders.join(', ')}`);\r\n    }\r\n\r\n    // Create header mapping\r\n    const headerMap: Record<string, number> = {};\r\n    expectedHeaders.forEach(expectedHeader => {\r\n      const index = normalizedHeaders.findIndex(h => h === expectedHeader);\r\n      headerMap[expectedHeader.replace(' ', '')] = index;\r\n    });\r\n\r\n    // Parse data rows\r\n    const dataRows: CSVRow[] = [];\r\n    for (let i = 1; i < lines.length; i++) {\r\n      const values = this.parseCSVLine(lines[i]);\r\n      if (values.length === 0 || values.every(v => !v.trim())) continue; // Skip empty rows\r\n      \r\n      const row: CSVRow = {\r\n        bankReference: values[headerMap.bankreference]?.trim() || '',\r\n        narrative: values[headerMap.narrative]?.trim() || '',\r\n        customerReference: values[headerMap.customerreference]?.trim() || '',\r\n        trnType: values[headerMap.trntype]?.trim() || '',\r\n        valueDate: values[headerMap.valuedate]?.trim() || '',\r\n        creditAmount: values[headerMap.creditamount]?.trim() || '',\r\n        debitAmount: values[headerMap.debitamount]?.trim() || '',\r\n        time: values[headerMap.time]?.trim() || '',\r\n        postDate: values[headerMap.postdate]?.trim() || '',\r\n        balance: values[headerMap.balance]?.trim() || ''\r\n      };\r\n      \r\n      dataRows.push(row);\r\n    }\r\n    \r\n    return dataRows;\r\n  }\r\n\r\n  private parseCSVLine(line: string): string[] {\r\n    const result: string[] = [];\r\n    let current = '';\r\n    let inQuotes = false;\r\n    \r\n    for (let i = 0; i < line.length; i++) {\r\n      const char = line[i];\r\n      \r\n      if (char === '\"') {\r\n        if (inQuotes && line[i + 1] === '\"') {\r\n          current += '\"';\r\n          i++; // Skip next quote\r\n        } else {\r\n          inQuotes = !inQuotes;\r\n        }\r\n      } else if (char === ',' && !inQuotes) {\r\n        result.push(current);\r\n        current = '';\r\n      } else {\r\n        current += char;\r\n      }\r\n    }\r\n    \r\n    result.push(current);\r\n    return result;\r\n  }\r\n\r\n  // Validate CSV data\r\n  validateCSVData(rows: CSVRow[]): ValidationError[] {\r\n    const errors: ValidationError[] = [];\r\n    \r\n    rows.forEach((row, index) => {\r\n      const rowNumber = index + 2; // +2 because index is 0-based and we skip header\r\n      \r\n      // Validate each field\r\n      CSV_TEMPLATE.validationRules.forEach(rule => {\r\n        const fieldValue = this.getFieldValue(row, rule.field);\r\n        const error = this.validateField(fieldValue, rule, rowNumber);\r\n        if (error) errors.push(error);\r\n      });\r\n      \r\n      // For bank statements, we trust the balance field as it represents the actual account balance\r\n      // Balance validation is disabled for bank statement imports as the data comes directly from the bank\r\n      // and may not be in strict chronological order or may include pending transactions\r\n    });\r\n    \r\n    return errors;\r\n  }\r\n\r\n  private getFieldValue(row: CSVRow, field: string): string {\r\n    switch (field.toLowerCase()) {\r\n      case 'bank reference': return row.bankReference;\r\n      case 'narrative': return row.narrative;\r\n      case 'customer reference': return row.customerReference;\r\n      case 'trn type': return row.trnType;\r\n      case 'value date': return row.valueDate;\r\n      case 'credit amount': return row.creditAmount;\r\n      case 'debit amount': return row.debitAmount;\r\n      case 'time': return row.time;\r\n      case 'post date': return row.postDate;\r\n      case 'balance': return row.balance;\r\n      default: return '';\r\n    }\r\n  }\r\n\r\n  private validateField(value: string, rule: ValidationRule, rowNumber: number): ValidationError | null {\r\n    switch (rule.rule) {\r\n      case 'required':\r\n        if (!value || value.trim() === '') {\r\n          return { row: rowNumber, field: rule.field, message: rule.message, value };\r\n        }\r\n        break;\r\n        \r\n      case 'number':\r\n        if (value.trim() && isNaN(this.parseAmount(value))) {\r\n          return { row: rowNumber, field: rule.field, message: rule.message, value };\r\n        }\r\n        break;\r\n        \r\n      case 'date':\r\n        if (value.trim() && !this.isValidDate(value)) {\r\n          return { row: rowNumber, field: rule.field, message: rule.message, value };\r\n        }\r\n        break;\r\n        \r\n      case 'positive':\r\n        if (value.trim() && this.parseAmount(value) < 0) {\r\n          return { row: rowNumber, field: rule.field, message: rule.message, value };\r\n        }\r\n        break;\r\n    }\r\n    \r\n    return null;\r\n  }\r\n\r\n  private parseAmount(value: string): number {\r\n    if (!value || value.trim() === '') return 0;\r\n    \r\n    // Remove currency symbols, commas, and quotes, handle negative values\r\n    const cleaned = value.replace(/[$,\\s\"]/g, '');\r\n    \r\n    // Handle negative values (with minus sign or parentheses)\r\n    const isNegative = cleaned.startsWith('-') || (cleaned.startsWith('(') && cleaned.endsWith(')'));\r\n    const numericValue = cleaned.replace(/[-()]/g, '');\r\n    \r\n    const amount = parseFloat(numericValue);\r\n    \r\n    if (isNaN(amount)) return 0;\r\n    \r\n    return isNegative ? -amount : amount;\r\n  }\r\n\r\n  private isValidDate(dateString: string): boolean {\r\n    // Handle DD/MM/YYYY format (Saudi bank format)\r\n    if (dateString.includes('/')) {\r\n      const parts = dateString.split('/');\r\n      if (parts.length === 3) {\r\n        const day = parseInt(parts[0]);\r\n        const month = parseInt(parts[1]);\r\n        const year = parseInt(parts[2]);\r\n        \r\n        // Basic range validation\r\n        if (day >= 1 && day <= 31 && month >= 1 && month <= 12 && year >= 1900 && year <= 2100) {\r\n          // Create date object to validate the actual date (handles leap years, etc.)\r\n          const date = new Date(year, month - 1, day);\r\n          return date.getFullYear() === year && \r\n                 date.getMonth() === month - 1 && \r\n                 date.getDate() === day;\r\n        }\r\n      }\r\n    }\r\n    \r\n    // Fallback to original logic\r\n    const date = new Date(dateString);\r\n    return date instanceof Date && !isNaN(date.getTime());\r\n  }\r\n\r\n  // Convert CSV rows to transactions\r\n  convertToTransactions(rows: CSVRow[]): Transaction[] {\r\n    return rows.map((row, index) => ({\r\n      id: `temp_${Date.now()}_${index}`,\r\n      date: this.formatDate(row.postDate),\r\n      description: row.narrative,\r\n      debitAmount: Math.abs(this.parseAmount(row.debitAmount)), // Ensure debit amounts are positive for display\r\n      creditAmount: Math.abs(this.parseAmount(row.creditAmount)), // Ensure credit amounts are positive for display\r\n      balance: this.parseAmount(row.balance),\r\n      reference: row.customerReference,\r\n      postDate: row.postDate,\r\n      time: row.time,\r\n      valueDate: row.valueDate\r\n    }));\r\n  }\r\n\r\n  private formatDate(dateString: string): string {\r\n    // Handle DD/MM/YYYY format from bank statements (Saudi bank format)\r\n    if (dateString.includes('/')) {\r\n      const parts = dateString.split('/');\r\n      if (parts.length === 3) {\r\n        const day = parts[0].padStart(2, '0');\r\n        const month = parts[1].padStart(2, '0');\r\n        const year = parts[2];\r\n        \r\n        // Validate the date parts\r\n        const dayNum = parseInt(day);\r\n        const monthNum = parseInt(month);\r\n        const yearNum = parseInt(year);\r\n        \r\n        if (dayNum >= 1 && dayNum <= 31 && monthNum >= 1 && monthNum <= 12 && yearNum >= 1900) {\r\n          // Convert DD/MM/YYYY to YYYY-MM-DD format\r\n          return `${year}-${month}-${day}`;\r\n        }\r\n      }\r\n    }\r\n    \r\n    // Fallback to original logic\r\n    const date = new Date(dateString);\r\n    return date.toISOString().split('T')[0];\r\n  }\r\n\r\n  // Helper method to create a sortable datetime from Post date and Time\r\n  private createSortableDateTime(postDate: string, time: string): Date {\r\n    const formattedDate = this.formatDate(postDate);\r\n    \r\n    // Handle time format (HH:MM)\r\n    let timeString = '00:00';\r\n    if (time && time.trim()) {\r\n      timeString = time.trim();\r\n      // Ensure time is in HH:MM format\r\n      if (timeString.length === 4 && !timeString.includes(':')) {\r\n        // Convert HHMM to HH:MM\r\n        timeString = timeString.substring(0, 2) + ':' + timeString.substring(2);\r\n      }\r\n    }\r\n    \r\n    // Combine date and time\r\n    const dateTimeString = `${formattedDate}T${timeString}:00`;\r\n    return new Date(dateTimeString);\r\n  }\r\n\r\n  // Process file and generate import summary\r\n  async processFile(file: File): Promise<ImportSummary> {\r\n    try {\r\n      const rows = await this.parseCSV(file);\r\n      const validationErrors = this.validateCSVData(rows);\r\n      const transactions = this.convertToTransactions(rows);\r\n      \r\n      // Sort transactions by Post date and time (newest first, matching bank statement order)\r\n      const sortedTransactions = transactions.sort((a, b) => {\r\n        const dateA = this.createSortableDateTime(a.postDate || a.date, a.time || '00:00');\r\n        const dateB = this.createSortableDateTime(b.postDate || b.date, b.time || '00:00');\r\n        return dateB.getTime() - dateA.getTime(); // Newest first (most recent transaction first)\r\n      });\r\n      \r\n      const totalDebitAmount = transactions.reduce((sum, t) => sum + t.debitAmount, 0);\r\n      const totalCreditAmount = transactions.reduce((sum, t) => sum + t.creditAmount, 0);\r\n      \r\n      // The closing balance is from the FIRST transaction (most recent) since bank statements are in reverse chronological order\r\n      const closingBalance = transactions.length > 0 ? transactions[0].balance : 0;\r\n      \r\n      // Calculate opening balance from the LAST transaction (oldest)\r\n      const openingBalance = transactions.length > 0 ? transactions[transactions.length - 1].balance : 0;\r\n      \r\n      // Calculate daily movement (net change)\r\n      const dailyMovement = closingBalance - openingBalance;\r\n      \r\n      return {\r\n        fileName: file.name,\r\n        totalTransactions: transactions.length,\r\n        totalDebitAmount,\r\n        totalCreditAmount,\r\n        closingBalance,\r\n        openingBalance,\r\n        dailyMovement,\r\n        validationErrors,\r\n        transactions: sortedTransactions,\r\n        dateRange: {\r\n          from: transactions.length > 0 ? sortedTransactions[sortedTransactions.length - 1].date : '',\r\n          to: transactions.length > 0 ? sortedTransactions[0].date : ''\r\n        }\r\n      };\r\n    } catch (error) {\r\n      throw new Error(`Failed to process ${file.name}: ${error instanceof Error ? error.message : 'Unknown error'}`);\r\n    }\r\n  }\r\n}\r\n\r\nexport const csvProcessingService = new CSVProcessingService(); "], "mappings": "AAEA;AACA,OAAO,MAAMA,YAAyB,GAAG;EACvCC,OAAO,EAAE,CAAC,gBAAgB,EAAE,WAAW,EAAE,oBAAoB,EAAE,UAAU,EAAE,YAAY,EAAE,eAAe,EAAE,cAAc,EAAE,MAAM,EAAE,WAAW,EAAE,SAAS,CAAC;EACzJC,eAAe,EAAE,CACf;IAAEC,KAAK,EAAE,gBAAgB;IAAEC,IAAI,EAAE,UAAU;IAAEC,OAAO,EAAE;EAA6B,CAAC,EACpF;IAAEF,KAAK,EAAE,WAAW;IAAEC,IAAI,EAAE,UAAU;IAAEC,OAAO,EAAE;EAAwB,CAAC,EAC1E;IAAEF,KAAK,EAAE,UAAU;IAAEC,IAAI,EAAE,UAAU;IAAEC,OAAO,EAAE;EAA+B,CAAC,EAChF;IAAEF,KAAK,EAAE,YAAY;IAAEC,IAAI,EAAE,UAAU;IAAEC,OAAO,EAAE;EAAyB,CAAC,EAC5E;IAAEF,KAAK,EAAE,YAAY;IAAEC,IAAI,EAAE,MAAM;IAAEC,OAAO,EAAE;EAAkD,CAAC,EACjG;IAAEF,KAAK,EAAE,WAAW;IAAEC,IAAI,EAAE,UAAU;IAAEC,OAAO,EAAE;EAAwB,CAAC,EAC1E;IAAEF,KAAK,EAAE,WAAW;IAAEC,IAAI,EAAE,MAAM;IAAEC,OAAO,EAAE;EAAiD,CAAC,EAC/F;IAAEF,KAAK,EAAE,eAAe;IAAEC,IAAI,EAAE,QAAQ;IAAEC,OAAO,EAAE;EAAuC,CAAC,EAC3F;IAAEF,KAAK,EAAE,cAAc;IAAEC,IAAI,EAAE,QAAQ;IAAEC,OAAO,EAAE;EAAsC,CAAC,EACzF;IAAEF,KAAK,EAAE,SAAS;IAAEC,IAAI,EAAE,UAAU;IAAEC,OAAO,EAAE;EAAsB,CAAC,EACtE;IAAEF,KAAK,EAAE,SAAS;IAAEC,IAAI,EAAE,QAAQ;IAAEC,OAAO,EAAE;EAAiC,CAAC,CAEhF;EACDC,UAAU,EAAE,CACV;IACE,gBAAgB,EAAE,cAAc;IAChC,WAAW,EAAE,4BAA4B;IACzC,oBAAoB,EAAE,iBAAiB;IACvC,UAAU,EAAE,UAAU;IACtB,YAAY,EAAE,YAAY;IAC1B,eAAe,EAAE,UAAU;IAC3B,cAAc,EAAE,EAAE;IAClB,MAAM,EAAE,OAAO;IACf,WAAW,EAAE,YAAY;IACzB,SAAS,EAAE;EACb,CAAC,EACD;IACE,gBAAgB,EAAE,cAAc;IAChC,WAAW,EAAE,kCAAkC;IAC/C,oBAAoB,EAAE,QAAQ;IAC9B,UAAU,EAAE,UAAU;IACtB,YAAY,EAAE,YAAY;IAC1B,eAAe,EAAE,EAAE;IACnB,cAAc,EAAE,eAAe;IAC/B,MAAM,EAAE,OAAO;IACf,WAAW,EAAE,YAAY;IACzB,SAAS,EAAE;EACb,CAAC,EACD;IACE,gBAAgB,EAAE,YAAY;IAC9B,WAAW,EAAE,+BAA+B;IAC5C,oBAAoB,EAAE,QAAQ;IAC9B,UAAU,EAAE,QAAQ;IACpB,YAAY,EAAE,YAAY;IAC1B,eAAe,EAAE,WAAW;IAC5B,cAAc,EAAE,EAAE;IAClB,MAAM,EAAE,OAAO;IACf,WAAW,EAAE,YAAY;IACzB,SAAS,EAAE;EACb,CAAC;AAEL,CAAC;AAED,MAAMC,oBAAoB,CAAC;EAEzB;EACAC,gBAAgBA,CAAA,EAAW;IACzB,MAAMP,OAAO,GAAGD,YAAY,CAACC,OAAO,CAACQ,IAAI,CAAC,GAAG,CAAC;IAC9C,MAAMC,IAAI,GAAGV,YAAY,CAACM,UAAU,CAACK,GAAG,CAACC,GAAG,IAC1CZ,YAAY,CAACC,OAAO,CAACU,GAAG,CAACE,MAAM,IAAI,IAAID,GAAG,CAACC,MAAM,CAAC,IAAI,EAAE,GAAG,CAAC,CAACJ,IAAI,CAAC,GAAG,CACvE,CAAC;IAED,OAAO,CAACR,OAAO,EAAE,GAAGS,IAAI,CAAC,CAACD,IAAI,CAAC,IAAI,CAAC;EACtC;;EAEA;EACAK,gBAAgBA,CAAA,EAAS;IACvB,MAAMC,UAAU,GAAG,IAAI,CAACP,gBAAgB,CAAC,CAAC;IAC1C,MAAMQ,IAAI,GAAG,IAAIC,IAAI,CAAC,CAACF,UAAU,CAAC,EAAE;MAAEG,IAAI,EAAE;IAA0B,CAAC,CAAC;IACxE,MAAMC,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;IAExC,IAAIF,IAAI,CAACG,QAAQ,KAAKC,SAAS,EAAE;MAC/B,MAAMC,GAAG,GAAGC,GAAG,CAACC,eAAe,CAACV,IAAI,CAAC;MACrCG,IAAI,CAACQ,YAAY,CAAC,MAAM,EAAEH,GAAG,CAAC;MAC9BL,IAAI,CAACQ,YAAY,CAAC,UAAU,EAAE,6BAA6B,CAAC;MAC5DR,IAAI,CAACS,KAAK,CAACC,UAAU,GAAG,QAAQ;MAChCT,QAAQ,CAACU,IAAI,CAACC,WAAW,CAACZ,IAAI,CAAC;MAC/BA,IAAI,CAACa,KAAK,CAAC,CAAC;MACZZ,QAAQ,CAACU,IAAI,CAACG,WAAW,CAACd,IAAI,CAAC;IACjC;EACF;;EAEA;EACA,MAAMe,QAAQA,CAACC,IAAU,EAAqB;IAC5C,OAAO,IAAIC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;MACtC,MAAMC,MAAM,GAAG,IAAIC,UAAU,CAAC,CAAC;MAE/BD,MAAM,CAACE,MAAM,GAAIC,KAAK,IAAK;QACzB,IAAI;UAAA,IAAAC,aAAA;UACF,MAAMC,GAAG,IAAAD,aAAA,GAAGD,KAAK,CAACG,MAAM,cAAAF,aAAA,uBAAZA,aAAA,CAAcG,MAAgB;UAC1C,MAAMpC,IAAI,GAAG,IAAI,CAACqC,eAAe,CAACH,GAAG,CAAC;UACtCP,OAAO,CAAC3B,IAAI,CAAC;QACf,CAAC,CAAC,OAAOsC,KAAK,EAAE;UACdV,MAAM,CAACU,KAAK,CAAC;QACf;MACF,CAAC;MAEDT,MAAM,CAACU,OAAO,GAAG,MAAMX,MAAM,CAAC,IAAIY,KAAK,CAAC,qBAAqB,CAAC,CAAC;MAC/DX,MAAM,CAACY,UAAU,CAAChB,IAAI,CAAC;IACzB,CAAC,CAAC;EACJ;EAEQY,eAAeA,CAACH,GAAW,EAAY;IAC7C,MAAMQ,KAAK,GAAGR,GAAG,CAACS,KAAK,CAAC,IAAI,CAAC,CAACC,MAAM,CAACC,IAAI,IAAIA,IAAI,CAACC,IAAI,CAAC,CAAC,CAAC;IACzD,IAAIJ,KAAK,CAACK,MAAM,GAAG,CAAC,EAAE,MAAM,IAAIP,KAAK,CAAC,qDAAqD,CAAC;IAE5F,MAAMjD,OAAO,GAAG,IAAI,CAACyD,YAAY,CAACN,KAAK,CAAC,CAAC,CAAC,CAAC,CAACzC,GAAG,CAACgD,CAAC,IAAIA,CAAC,CAACH,IAAI,CAAC,CAAC,CAAC;IAC9D,MAAMI,eAAe,GAAG,CACtB,gBAAgB,EAChB,WAAW,EACX,oBAAoB,EACpB,UAAU,EACV,YAAY,EACZ,eAAe,EACf,cAAc,EACd,MAAM,EACN,WAAW,EACX,SAAS,CACV;;IAED;IACA,MAAMC,iBAAiB,GAAG5D,OAAO,CAACU,GAAG,CAACgD,CAAC,IAAIA,CAAC,CAACG,WAAW,CAAC,CAAC,CAAC;IAC3D,MAAMC,cAAc,GAAGH,eAAe,CAACN,MAAM,CAACK,CAAC,IAAI,CAACE,iBAAiB,CAACG,QAAQ,CAACL,CAAC,CAAC,CAAC;IAElF,IAAII,cAAc,CAACN,MAAM,GAAG,CAAC,EAAE;MAC7B,MAAM,IAAIP,KAAK,CAAC,6BAA6Ba,cAAc,CAACtD,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;IAC3E;;IAEA;IACA,MAAMwD,SAAiC,GAAG,CAAC,CAAC;IAC5CL,eAAe,CAACM,OAAO,CAACC,cAAc,IAAI;MACxC,MAAMC,KAAK,GAAGP,iBAAiB,CAACQ,SAAS,CAACV,CAAC,IAAIA,CAAC,KAAKQ,cAAc,CAAC;MACpEF,SAAS,CAACE,cAAc,CAACG,OAAO,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC,GAAGF,KAAK;IACpD,CAAC,CAAC;;IAEF;IACA,MAAMG,QAAkB,GAAG,EAAE;IAC7B,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGpB,KAAK,CAACK,MAAM,EAAEe,CAAC,EAAE,EAAE;MAAA,IAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA;MACrC,MAAMC,MAAM,GAAG,IAAI,CAACzB,YAAY,CAACN,KAAK,CAACoB,CAAC,CAAC,CAAC;MAC1C,IAAIW,MAAM,CAAC1B,MAAM,KAAK,CAAC,IAAI0B,MAAM,CAACC,KAAK,CAACC,CAAC,IAAI,CAACA,CAAC,CAAC7B,IAAI,CAAC,CAAC,CAAC,EAAE,SAAS,CAAC;;MAEnE,MAAM5C,GAAW,GAAG;QAClB0E,aAAa,EAAE,EAAAb,qBAAA,GAAAU,MAAM,CAAClB,SAAS,CAACsB,aAAa,CAAC,cAAAd,qBAAA,uBAA/BA,qBAAA,CAAiCjB,IAAI,CAAC,CAAC,KAAI,EAAE;QAC5DgC,SAAS,EAAE,EAAAd,qBAAA,GAAAS,MAAM,CAAClB,SAAS,CAACuB,SAAS,CAAC,cAAAd,qBAAA,uBAA3BA,qBAAA,CAA6BlB,IAAI,CAAC,CAAC,KAAI,EAAE;QACpDiC,iBAAiB,EAAE,EAAAd,qBAAA,GAAAQ,MAAM,CAAClB,SAAS,CAACyB,iBAAiB,CAAC,cAAAf,qBAAA,uBAAnCA,qBAAA,CAAqCnB,IAAI,CAAC,CAAC,KAAI,EAAE;QACpEmC,OAAO,EAAE,EAAAf,qBAAA,GAAAO,MAAM,CAAClB,SAAS,CAAC2B,OAAO,CAAC,cAAAhB,qBAAA,uBAAzBA,qBAAA,CAA2BpB,IAAI,CAAC,CAAC,KAAI,EAAE;QAChDqC,SAAS,EAAE,EAAAhB,qBAAA,GAAAM,MAAM,CAAClB,SAAS,CAAC6B,SAAS,CAAC,cAAAjB,qBAAA,uBAA3BA,qBAAA,CAA6BrB,IAAI,CAAC,CAAC,KAAI,EAAE;QACpDuC,YAAY,EAAE,EAAAjB,qBAAA,GAAAK,MAAM,CAAClB,SAAS,CAAC+B,YAAY,CAAC,cAAAlB,qBAAA,uBAA9BA,qBAAA,CAAgCtB,IAAI,CAAC,CAAC,KAAI,EAAE;QAC1DyC,WAAW,EAAE,EAAAlB,qBAAA,GAAAI,MAAM,CAAClB,SAAS,CAACiC,WAAW,CAAC,cAAAnB,qBAAA,uBAA7BA,qBAAA,CAA+BvB,IAAI,CAAC,CAAC,KAAI,EAAE;QACxD2C,IAAI,EAAE,EAAAnB,qBAAA,GAAAG,MAAM,CAAClB,SAAS,CAACkC,IAAI,CAAC,cAAAnB,qBAAA,uBAAtBA,qBAAA,CAAwBxB,IAAI,CAAC,CAAC,KAAI,EAAE;QAC1C4C,QAAQ,EAAE,EAAAnB,qBAAA,GAAAE,MAAM,CAAClB,SAAS,CAACoC,QAAQ,CAAC,cAAApB,qBAAA,uBAA1BA,qBAAA,CAA4BzB,IAAI,CAAC,CAAC,KAAI,EAAE;QAClD8C,OAAO,EAAE,EAAApB,qBAAA,GAAAC,MAAM,CAAClB,SAAS,CAACqC,OAAO,CAAC,cAAApB,qBAAA,uBAAzBA,qBAAA,CAA2B1B,IAAI,CAAC,CAAC,KAAI;MAChD,CAAC;MAEDe,QAAQ,CAACgC,IAAI,CAAC3F,GAAG,CAAC;IACpB;IAEA,OAAO2D,QAAQ;EACjB;EAEQb,YAAYA,CAACH,IAAY,EAAY;IAC3C,MAAMT,MAAgB,GAAG,EAAE;IAC3B,IAAI0D,OAAO,GAAG,EAAE;IAChB,IAAIC,QAAQ,GAAG,KAAK;IAEpB,KAAK,IAAIjC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGjB,IAAI,CAACE,MAAM,EAAEe,CAAC,EAAE,EAAE;MACpC,MAAMkC,IAAI,GAAGnD,IAAI,CAACiB,CAAC,CAAC;MAEpB,IAAIkC,IAAI,KAAK,GAAG,EAAE;QAChB,IAAID,QAAQ,IAAIlD,IAAI,CAACiB,CAAC,GAAG,CAAC,CAAC,KAAK,GAAG,EAAE;UACnCgC,OAAO,IAAI,GAAG;UACdhC,CAAC,EAAE,CAAC,CAAC;QACP,CAAC,MAAM;UACLiC,QAAQ,GAAG,CAACA,QAAQ;QACtB;MACF,CAAC,MAAM,IAAIC,IAAI,KAAK,GAAG,IAAI,CAACD,QAAQ,EAAE;QACpC3D,MAAM,CAACyD,IAAI,CAACC,OAAO,CAAC;QACpBA,OAAO,GAAG,EAAE;MACd,CAAC,MAAM;QACLA,OAAO,IAAIE,IAAI;MACjB;IACF;IAEA5D,MAAM,CAACyD,IAAI,CAACC,OAAO,CAAC;IACpB,OAAO1D,MAAM;EACf;;EAEA;EACA6D,eAAeA,CAACjG,IAAc,EAAqB;IACjD,MAAMkG,MAAyB,GAAG,EAAE;IAEpClG,IAAI,CAACwD,OAAO,CAAC,CAACtD,GAAG,EAAEwD,KAAK,KAAK;MAC3B,MAAMyC,SAAS,GAAGzC,KAAK,GAAG,CAAC,CAAC,CAAC;;MAE7B;MACApE,YAAY,CAACE,eAAe,CAACgE,OAAO,CAAC9D,IAAI,IAAI;QAC3C,MAAM0G,UAAU,GAAG,IAAI,CAACC,aAAa,CAACnG,GAAG,EAAER,IAAI,CAACD,KAAK,CAAC;QACtD,MAAM6C,KAAK,GAAG,IAAI,CAACgE,aAAa,CAACF,UAAU,EAAE1G,IAAI,EAAEyG,SAAS,CAAC;QAC7D,IAAI7D,KAAK,EAAE4D,MAAM,CAACL,IAAI,CAACvD,KAAK,CAAC;MAC/B,CAAC,CAAC;;MAEF;MACA;MACA;IACF,CAAC,CAAC;IAEF,OAAO4D,MAAM;EACf;EAEQG,aAAaA,CAACnG,GAAW,EAAET,KAAa,EAAU;IACxD,QAAQA,KAAK,CAAC2D,WAAW,CAAC,CAAC;MACzB,KAAK,gBAAgB;QAAE,OAAOlD,GAAG,CAAC0E,aAAa;MAC/C,KAAK,WAAW;QAAE,OAAO1E,GAAG,CAAC4E,SAAS;MACtC,KAAK,oBAAoB;QAAE,OAAO5E,GAAG,CAAC6E,iBAAiB;MACvD,KAAK,UAAU;QAAE,OAAO7E,GAAG,CAAC+E,OAAO;MACnC,KAAK,YAAY;QAAE,OAAO/E,GAAG,CAACiF,SAAS;MACvC,KAAK,eAAe;QAAE,OAAOjF,GAAG,CAACmF,YAAY;MAC7C,KAAK,cAAc;QAAE,OAAOnF,GAAG,CAACqF,WAAW;MAC3C,KAAK,MAAM;QAAE,OAAOrF,GAAG,CAACuF,IAAI;MAC5B,KAAK,WAAW;QAAE,OAAOvF,GAAG,CAACwF,QAAQ;MACrC,KAAK,SAAS;QAAE,OAAOxF,GAAG,CAAC0F,OAAO;MAClC;QAAS,OAAO,EAAE;IACpB;EACF;EAEQU,aAAaA,CAACC,KAAa,EAAE7G,IAAoB,EAAEyG,SAAiB,EAA0B;IACpG,QAAQzG,IAAI,CAACA,IAAI;MACf,KAAK,UAAU;QACb,IAAI,CAAC6G,KAAK,IAAIA,KAAK,CAACzD,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;UACjC,OAAO;YAAE5C,GAAG,EAAEiG,SAAS;YAAE1G,KAAK,EAAEC,IAAI,CAACD,KAAK;YAAEE,OAAO,EAAED,IAAI,CAACC,OAAO;YAAE4G;UAAM,CAAC;QAC5E;QACA;MAEF,KAAK,QAAQ;QACX,IAAIA,KAAK,CAACzD,IAAI,CAAC,CAAC,IAAI0D,KAAK,CAAC,IAAI,CAACC,WAAW,CAACF,KAAK,CAAC,CAAC,EAAE;UAClD,OAAO;YAAErG,GAAG,EAAEiG,SAAS;YAAE1G,KAAK,EAAEC,IAAI,CAACD,KAAK;YAAEE,OAAO,EAAED,IAAI,CAACC,OAAO;YAAE4G;UAAM,CAAC;QAC5E;QACA;MAEF,KAAK,MAAM;QACT,IAAIA,KAAK,CAACzD,IAAI,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC4D,WAAW,CAACH,KAAK,CAAC,EAAE;UAC5C,OAAO;YAAErG,GAAG,EAAEiG,SAAS;YAAE1G,KAAK,EAAEC,IAAI,CAACD,KAAK;YAAEE,OAAO,EAAED,IAAI,CAACC,OAAO;YAAE4G;UAAM,CAAC;QAC5E;QACA;MAEF,KAAK,UAAU;QACb,IAAIA,KAAK,CAACzD,IAAI,CAAC,CAAC,IAAI,IAAI,CAAC2D,WAAW,CAACF,KAAK,CAAC,GAAG,CAAC,EAAE;UAC/C,OAAO;YAAErG,GAAG,EAAEiG,SAAS;YAAE1G,KAAK,EAAEC,IAAI,CAACD,KAAK;YAAEE,OAAO,EAAED,IAAI,CAACC,OAAO;YAAE4G;UAAM,CAAC;QAC5E;QACA;IACJ;IAEA,OAAO,IAAI;EACb;EAEQE,WAAWA,CAACF,KAAa,EAAU;IACzC,IAAI,CAACA,KAAK,IAAIA,KAAK,CAACzD,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE,OAAO,CAAC;;IAE3C;IACA,MAAM6D,OAAO,GAAGJ,KAAK,CAAC3C,OAAO,CAAC,UAAU,EAAE,EAAE,CAAC;;IAE7C;IACA,MAAMgD,UAAU,GAAGD,OAAO,CAACE,UAAU,CAAC,GAAG,CAAC,IAAKF,OAAO,CAACE,UAAU,CAAC,GAAG,CAAC,IAAIF,OAAO,CAACG,QAAQ,CAAC,GAAG,CAAE;IAChG,MAAMC,YAAY,GAAGJ,OAAO,CAAC/C,OAAO,CAAC,QAAQ,EAAE,EAAE,CAAC;IAElD,MAAMoD,MAAM,GAAGC,UAAU,CAACF,YAAY,CAAC;IAEvC,IAAIP,KAAK,CAACQ,MAAM,CAAC,EAAE,OAAO,CAAC;IAE3B,OAAOJ,UAAU,GAAG,CAACI,MAAM,GAAGA,MAAM;EACtC;EAEQN,WAAWA,CAACQ,UAAkB,EAAW;IAC/C;IACA,IAAIA,UAAU,CAAC5D,QAAQ,CAAC,GAAG,CAAC,EAAE;MAC5B,MAAM6D,KAAK,GAAGD,UAAU,CAACvE,KAAK,CAAC,GAAG,CAAC;MACnC,IAAIwE,KAAK,CAACpE,MAAM,KAAK,CAAC,EAAE;QACtB,MAAMqE,GAAG,GAAGC,QAAQ,CAACF,KAAK,CAAC,CAAC,CAAC,CAAC;QAC9B,MAAMG,KAAK,GAAGD,QAAQ,CAACF,KAAK,CAAC,CAAC,CAAC,CAAC;QAChC,MAAMI,IAAI,GAAGF,QAAQ,CAACF,KAAK,CAAC,CAAC,CAAC,CAAC;;QAE/B;QACA,IAAIC,GAAG,IAAI,CAAC,IAAIA,GAAG,IAAI,EAAE,IAAIE,KAAK,IAAI,CAAC,IAAIA,KAAK,IAAI,EAAE,IAAIC,IAAI,IAAI,IAAI,IAAIA,IAAI,IAAI,IAAI,EAAE;UACtF;UACA,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAACF,IAAI,EAAED,KAAK,GAAG,CAAC,EAAEF,GAAG,CAAC;UAC3C,OAAOI,IAAI,CAACE,WAAW,CAAC,CAAC,KAAKH,IAAI,IAC3BC,IAAI,CAACG,QAAQ,CAAC,CAAC,KAAKL,KAAK,GAAG,CAAC,IAC7BE,IAAI,CAACI,OAAO,CAAC,CAAC,KAAKR,GAAG;QAC/B;MACF;IACF;;IAEA;IACA,MAAMI,IAAI,GAAG,IAAIC,IAAI,CAACP,UAAU,CAAC;IACjC,OAAOM,IAAI,YAAYC,IAAI,IAAI,CAACjB,KAAK,CAACgB,IAAI,CAACK,OAAO,CAAC,CAAC,CAAC;EACvD;;EAEA;EACAC,qBAAqBA,CAAC9H,IAAc,EAAiB;IACnD,OAAOA,IAAI,CAACC,GAAG,CAAC,CAACC,GAAG,EAAEwD,KAAK,MAAM;MAC/BqE,EAAE,EAAE,QAAQN,IAAI,CAACO,GAAG,CAAC,CAAC,IAAItE,KAAK,EAAE;MACjC8D,IAAI,EAAE,IAAI,CAACS,UAAU,CAAC/H,GAAG,CAACwF,QAAQ,CAAC;MACnCwC,WAAW,EAAEhI,GAAG,CAAC4E,SAAS;MAC1BS,WAAW,EAAE4C,IAAI,CAACC,GAAG,CAAC,IAAI,CAAC3B,WAAW,CAACvG,GAAG,CAACqF,WAAW,CAAC,CAAC;MAAE;MAC1DF,YAAY,EAAE8C,IAAI,CAACC,GAAG,CAAC,IAAI,CAAC3B,WAAW,CAACvG,GAAG,CAACmF,YAAY,CAAC,CAAC;MAAE;MAC5DO,OAAO,EAAE,IAAI,CAACa,WAAW,CAACvG,GAAG,CAAC0F,OAAO,CAAC;MACtCyC,SAAS,EAAEnI,GAAG,CAAC6E,iBAAiB;MAChCW,QAAQ,EAAExF,GAAG,CAACwF,QAAQ;MACtBD,IAAI,EAAEvF,GAAG,CAACuF,IAAI;MACdN,SAAS,EAAEjF,GAAG,CAACiF;IACjB,CAAC,CAAC,CAAC;EACL;EAEQ8C,UAAUA,CAACf,UAAkB,EAAU;IAC7C;IACA,IAAIA,UAAU,CAAC5D,QAAQ,CAAC,GAAG,CAAC,EAAE;MAC5B,MAAM6D,KAAK,GAAGD,UAAU,CAACvE,KAAK,CAAC,GAAG,CAAC;MACnC,IAAIwE,KAAK,CAACpE,MAAM,KAAK,CAAC,EAAE;QACtB,MAAMqE,GAAG,GAAGD,KAAK,CAAC,CAAC,CAAC,CAACmB,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;QACrC,MAAMhB,KAAK,GAAGH,KAAK,CAAC,CAAC,CAAC,CAACmB,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;QACvC,MAAMf,IAAI,GAAGJ,KAAK,CAAC,CAAC,CAAC;;QAErB;QACA,MAAMoB,MAAM,GAAGlB,QAAQ,CAACD,GAAG,CAAC;QAC5B,MAAMoB,QAAQ,GAAGnB,QAAQ,CAACC,KAAK,CAAC;QAChC,MAAMmB,OAAO,GAAGpB,QAAQ,CAACE,IAAI,CAAC;QAE9B,IAAIgB,MAAM,IAAI,CAAC,IAAIA,MAAM,IAAI,EAAE,IAAIC,QAAQ,IAAI,CAAC,IAAIA,QAAQ,IAAI,EAAE,IAAIC,OAAO,IAAI,IAAI,EAAE;UACrF;UACA,OAAO,GAAGlB,IAAI,IAAID,KAAK,IAAIF,GAAG,EAAE;QAClC;MACF;IACF;;IAEA;IACA,MAAMI,IAAI,GAAG,IAAIC,IAAI,CAACP,UAAU,CAAC;IACjC,OAAOM,IAAI,CAACkB,WAAW,CAAC,CAAC,CAAC/F,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;EACzC;;EAEA;EACQgG,sBAAsBA,CAACjD,QAAgB,EAAED,IAAY,EAAQ;IACnE,MAAMmD,aAAa,GAAG,IAAI,CAACX,UAAU,CAACvC,QAAQ,CAAC;;IAE/C;IACA,IAAImD,UAAU,GAAG,OAAO;IACxB,IAAIpD,IAAI,IAAIA,IAAI,CAAC3C,IAAI,CAAC,CAAC,EAAE;MACvB+F,UAAU,GAAGpD,IAAI,CAAC3C,IAAI,CAAC,CAAC;MACxB;MACA,IAAI+F,UAAU,CAAC9F,MAAM,KAAK,CAAC,IAAI,CAAC8F,UAAU,CAACvF,QAAQ,CAAC,GAAG,CAAC,EAAE;QACxD;QACAuF,UAAU,GAAGA,UAAU,CAACC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,GAAG,GAAGD,UAAU,CAACC,SAAS,CAAC,CAAC,CAAC;MACzE;IACF;;IAEA;IACA,MAAMC,cAAc,GAAG,GAAGH,aAAa,IAAIC,UAAU,KAAK;IAC1D,OAAO,IAAIpB,IAAI,CAACsB,cAAc,CAAC;EACjC;;EAEA;EACA,MAAMC,WAAWA,CAACvH,IAAU,EAA0B;IACpD,IAAI;MACF,MAAMzB,IAAI,GAAG,MAAM,IAAI,CAACwB,QAAQ,CAACC,IAAI,CAAC;MACtC,MAAMwH,gBAAgB,GAAG,IAAI,CAAChD,eAAe,CAACjG,IAAI,CAAC;MACnD,MAAMkJ,YAAY,GAAG,IAAI,CAACpB,qBAAqB,CAAC9H,IAAI,CAAC;;MAErD;MACA,MAAMmJ,kBAAkB,GAAGD,YAAY,CAACE,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;QACrD,MAAMC,KAAK,GAAG,IAAI,CAACZ,sBAAsB,CAACU,CAAC,CAAC3D,QAAQ,IAAI2D,CAAC,CAAC7B,IAAI,EAAE6B,CAAC,CAAC5D,IAAI,IAAI,OAAO,CAAC;QAClF,MAAM+D,KAAK,GAAG,IAAI,CAACb,sBAAsB,CAACW,CAAC,CAAC5D,QAAQ,IAAI4D,CAAC,CAAC9B,IAAI,EAAE8B,CAAC,CAAC7D,IAAI,IAAI,OAAO,CAAC;QAClF,OAAO+D,KAAK,CAAC3B,OAAO,CAAC,CAAC,GAAG0B,KAAK,CAAC1B,OAAO,CAAC,CAAC,CAAC,CAAC;MAC5C,CAAC,CAAC;MAEF,MAAM4B,gBAAgB,GAAGP,YAAY,CAACQ,MAAM,CAAC,CAACC,GAAG,EAAEC,CAAC,KAAKD,GAAG,GAAGC,CAAC,CAACrE,WAAW,EAAE,CAAC,CAAC;MAChF,MAAMsE,iBAAiB,GAAGX,YAAY,CAACQ,MAAM,CAAC,CAACC,GAAG,EAAEC,CAAC,KAAKD,GAAG,GAAGC,CAAC,CAACvE,YAAY,EAAE,CAAC,CAAC;;MAElF;MACA,MAAMyE,cAAc,GAAGZ,YAAY,CAACnG,MAAM,GAAG,CAAC,GAAGmG,YAAY,CAAC,CAAC,CAAC,CAACtD,OAAO,GAAG,CAAC;;MAE5E;MACA,MAAMmE,cAAc,GAAGb,YAAY,CAACnG,MAAM,GAAG,CAAC,GAAGmG,YAAY,CAACA,YAAY,CAACnG,MAAM,GAAG,CAAC,CAAC,CAAC6C,OAAO,GAAG,CAAC;;MAElG;MACA,MAAMoE,aAAa,GAAGF,cAAc,GAAGC,cAAc;MAErD,OAAO;QACLE,QAAQ,EAAExI,IAAI,CAACyI,IAAI;QACnBC,iBAAiB,EAAEjB,YAAY,CAACnG,MAAM;QACtC0G,gBAAgB;QAChBI,iBAAiB;QACjBC,cAAc;QACdC,cAAc;QACdC,aAAa;QACbf,gBAAgB;QAChBC,YAAY,EAAEC,kBAAkB;QAChCiB,SAAS,EAAE;UACTC,IAAI,EAAEnB,YAAY,CAACnG,MAAM,GAAG,CAAC,GAAGoG,kBAAkB,CAACA,kBAAkB,CAACpG,MAAM,GAAG,CAAC,CAAC,CAACyE,IAAI,GAAG,EAAE;UAC3F8C,EAAE,EAAEpB,YAAY,CAACnG,MAAM,GAAG,CAAC,GAAGoG,kBAAkB,CAAC,CAAC,CAAC,CAAC3B,IAAI,GAAG;QAC7D;MACF,CAAC;IACH,CAAC,CAAC,OAAOlF,KAAK,EAAE;MACd,MAAM,IAAIE,KAAK,CAAC,qBAAqBf,IAAI,CAACyI,IAAI,KAAK5H,KAAK,YAAYE,KAAK,GAAGF,KAAK,CAAC3C,OAAO,GAAG,eAAe,EAAE,CAAC;IAChH;EACF;AACF;AAEA,OAAO,MAAM4K,oBAAoB,GAAG,IAAI1K,oBAAoB,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}