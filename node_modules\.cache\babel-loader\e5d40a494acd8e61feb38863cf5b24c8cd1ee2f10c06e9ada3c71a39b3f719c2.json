{"ast": null, "code": "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { backend_util, MaxPoolGrad } from '@tensorflow/tfjs-core';\nimport { MaxPool2DBackpropProgram } from '../max_pool_backprop_gpu';\nimport { Pool2DProgram } from '../pool_gpu';\nimport { assertNotComplex } from '../webgl_util';\nexport function maxPoolGrad(args) {\n  const {\n    inputs,\n    backend,\n    attrs\n  } = args;\n  const {\n    dy,\n    input,\n    output\n  } = inputs;\n  const x = input;\n  assertNotComplex([input, output], 'maxPoolGrad');\n  const {\n    filterSize,\n    strides,\n    pad,\n    dimRoundingMode\n  } = attrs;\n  const convInfo = backend_util.computePool2DInfo(x.shape, filterSize, strides, 1 /* dilations */, pad, dimRoundingMode);\n  const getPositions = true;\n  const maxPoolPositionsProgram = new Pool2DProgram(convInfo, 'max', getPositions);\n  const maxPoolPositions = backend.runWebGLProgram(maxPoolPositionsProgram, [x], x.dtype);\n  const maxPoolBackPropProgram = new MaxPool2DBackpropProgram(convInfo);\n  const result = backend.runWebGLProgram(maxPoolBackPropProgram, [dy, maxPoolPositions], x.dtype);\n  backend.disposeIntermediateTensorInfo(maxPoolPositions);\n  return result;\n}\nexport const maxPoolGradConfig = {\n  kernelName: MaxPoolGrad,\n  backendName: 'webgl',\n  kernelFunc: maxPoolGrad\n};", "map": {"version": 3, "names": ["backend_util", "MaxPoolGrad", "MaxPool2DBackpropProgram", "Pool2DProgram", "assertNotComplex", "maxPoolGrad", "args", "inputs", "backend", "attrs", "dy", "input", "output", "x", "filterSize", "strides", "pad", "dimRoundingMode", "convInfo", "computePool2DInfo", "shape", "getPositions", "maxPoolPositionsProgram", "maxPoolPositions", "runWebGLProgram", "dtype", "maxPoolBackPropProgram", "result", "disposeIntermediateTensorInfo", "maxPoolGradConfig", "kernelName", "backendName", "kernelFunc"], "sources": ["C:\\tfjs-backend-webgl\\src\\kernels\\MaxPoolGrad.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport {backend_util, KernelConfig, KernelFunc, MaxPoolGrad, MaxPoolGradAttrs, MaxPoolGradInputs, TensorInfo} from '@tensorflow/tfjs-core';\n\nimport {MathBackendWebGL} from '../backend_webgl';\nimport {MaxPool2DBackpropProgram} from '../max_pool_backprop_gpu';\nimport {Pool2DProgram} from '../pool_gpu';\nimport {assertNotComplex} from '../webgl_util';\n\nexport function maxPoolGrad(args: {\n  inputs: MaxPoolGradInputs,\n  backend: MathBackendWebGL,\n  attrs: MaxPoolGradAttrs\n}): TensorInfo {\n  const {inputs, backend, attrs} = args;\n  const {dy, input, output} = inputs;\n  const x = input;\n  assertNotComplex([input, output], 'maxPoolGrad');\n  const {filterSize, strides, pad, dimRoundingMode} = attrs;\n\n  const convInfo = backend_util.computePool2DInfo(\n      x.shape as [number, number, number, number], filterSize, strides,\n      1 /* dilations */, pad, dimRoundingMode);\n  const getPositions = true;\n  const maxPoolPositionsProgram =\n      new Pool2DProgram(convInfo, 'max', getPositions);\n  const maxPoolPositions: TensorInfo =\n      backend.runWebGLProgram(maxPoolPositionsProgram, [x], x.dtype);\n\n  const maxPoolBackPropProgram = new MaxPool2DBackpropProgram(convInfo);\n  const result = backend.runWebGLProgram(\n      maxPoolBackPropProgram, [dy, maxPoolPositions], x.dtype);\n  backend.disposeIntermediateTensorInfo(maxPoolPositions);\n  return result;\n}\n\nexport const maxPoolGradConfig: KernelConfig = {\n  kernelName: MaxPoolGrad,\n  backendName: 'webgl',\n  kernelFunc: maxPoolGrad as unknown as KernelFunc\n};\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAgBA,SAAQA,YAAY,EAA4BC,WAAW,QAAwD,uBAAuB;AAG1I,SAAQC,wBAAwB,QAAO,0BAA0B;AACjE,SAAQC,aAAa,QAAO,aAAa;AACzC,SAAQC,gBAAgB,QAAO,eAAe;AAE9C,OAAM,SAAUC,WAAWA,CAACC,IAI3B;EACC,MAAM;IAACC,MAAM;IAAEC,OAAO;IAAEC;EAAK,CAAC,GAAGH,IAAI;EACrC,MAAM;IAACI,EAAE;IAAEC,KAAK;IAAEC;EAAM,CAAC,GAAGL,MAAM;EAClC,MAAMM,CAAC,GAAGF,KAAK;EACfP,gBAAgB,CAAC,CAACO,KAAK,EAAEC,MAAM,CAAC,EAAE,aAAa,CAAC;EAChD,MAAM;IAACE,UAAU;IAAEC,OAAO;IAAEC,GAAG;IAAEC;EAAe,CAAC,GAAGR,KAAK;EAEzD,MAAMS,QAAQ,GAAGlB,YAAY,CAACmB,iBAAiB,CAC3CN,CAAC,CAACO,KAAyC,EAAEN,UAAU,EAAEC,OAAO,EAChE,CAAC,CAAC,iBAAiBC,GAAG,EAAEC,eAAe,CAAC;EAC5C,MAAMI,YAAY,GAAG,IAAI;EACzB,MAAMC,uBAAuB,GACzB,IAAInB,aAAa,CAACe,QAAQ,EAAE,KAAK,EAAEG,YAAY,CAAC;EACpD,MAAME,gBAAgB,GAClBf,OAAO,CAACgB,eAAe,CAACF,uBAAuB,EAAE,CAACT,CAAC,CAAC,EAAEA,CAAC,CAACY,KAAK,CAAC;EAElE,MAAMC,sBAAsB,GAAG,IAAIxB,wBAAwB,CAACgB,QAAQ,CAAC;EACrE,MAAMS,MAAM,GAAGnB,OAAO,CAACgB,eAAe,CAClCE,sBAAsB,EAAE,CAAChB,EAAE,EAAEa,gBAAgB,CAAC,EAAEV,CAAC,CAACY,KAAK,CAAC;EAC5DjB,OAAO,CAACoB,6BAA6B,CAACL,gBAAgB,CAAC;EACvD,OAAOI,MAAM;AACf;AAEA,OAAO,MAAME,iBAAiB,GAAiB;EAC7CC,UAAU,EAAE7B,WAAW;EACvB8B,WAAW,EAAE,OAAO;EACpBC,UAAU,EAAE3B;CACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}