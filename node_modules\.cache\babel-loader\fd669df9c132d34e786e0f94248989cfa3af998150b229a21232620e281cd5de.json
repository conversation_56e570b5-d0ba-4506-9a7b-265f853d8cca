{"ast": null, "code": "/**\n * @license\n * Copyright 2018 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport { env } from '@tensorflow/tfjs-core';\nimport * as tensorflow from '../data/compiled_api';\nimport { getRegisteredOp } from './custom_op/register';\nimport { getNodeNameAndIndex } from './executors/utils';\nimport * as arithmetic from './op_list/arithmetic';\nimport * as basicMath from './op_list/basic_math';\nimport * as control from './op_list/control';\nimport * as convolution from './op_list/convolution';\nimport * as creation from './op_list/creation';\nimport * as dynamic from './op_list/dynamic';\nimport * as evaluation from './op_list/evaluation';\nimport * as graph from './op_list/graph';\nimport * as hashTable from './op_list/hash_table';\nimport * as image from './op_list/image';\nimport * as logical from './op_list/logical';\nimport * as matrices from './op_list/matrices';\nimport * as normalization from './op_list/normalization';\nimport * as reduction from './op_list/reduction';\nimport * as sliceJoin from './op_list/slice_join';\nimport * as sparse from './op_list/sparse';\nimport * as spectral from './op_list/spectral';\nimport * as string from './op_list/string';\nimport * as transformation from './op_list/transformation';\nexport class OperationMapper {\n  // Singleton instance for the mapper\n  static get Instance() {\n    return this._instance || (this._instance = new this());\n  }\n  // Loads the op mapping from the JSON file.\n  constructor() {\n    const ops = [arithmetic, basicMath, control, convolution, creation, dynamic, evaluation, graph, hashTable, image, logical, matrices, normalization, reduction, sliceJoin, sparse, spectral, string, transformation];\n    const mappersJson = [].concat(...ops.map(op => op.json));\n    this.opMappers = mappersJson.reduce((map, mapper) => {\n      map[mapper.tfOpName] = mapper;\n      return map;\n    }, {});\n  }\n  // Converts the model inference graph from Tensorflow GraphDef to local\n  // representation for TensorFlow.js API\n  transformGraph(graph) {\n    let signature = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    const tfNodes = graph.node;\n    const placeholders = [];\n    const weights = [];\n    const initNodes = [];\n    const nodes = tfNodes.reduce((map, node) => {\n      map[node.name] = this.mapNode(node);\n      if (node.op.startsWith('Placeholder')) {\n        placeholders.push(map[node.name]);\n      } else if (node.op === 'Const') {\n        weights.push(map[node.name]);\n      } else if (node.input == null || node.input.length === 0) {\n        initNodes.push(map[node.name]);\n      }\n      return map;\n    }, {});\n    let inputs = [];\n    const outputs = [];\n    let inputNodeNameToKey = {};\n    let outputNodeNameToKey = {};\n    if (signature != null) {\n      inputNodeNameToKey = this.mapSignatureEntries(signature.inputs);\n      outputNodeNameToKey = this.mapSignatureEntries(signature.outputs);\n    }\n    const allNodes = Object.keys(nodes);\n    allNodes.forEach(key => {\n      const node = nodes[key];\n      node.inputNames.forEach((name, index) => {\n        const [nodeName,, outputName] = getNodeNameAndIndex(name);\n        const inputNode = nodes[nodeName];\n        if (inputNode.outputs != null) {\n          const outputIndex = inputNode.outputs.indexOf(outputName);\n          if (outputIndex !== -1) {\n            const inputName = \"\".concat(nodeName, \":\").concat(outputIndex);\n            // update the input name to use the mapped output index directly.\n            node.inputNames[index] = inputName;\n          }\n        }\n        node.inputs.push(inputNode);\n        inputNode.children.push(node);\n      });\n    });\n    // if signature has not outputs set, add any node that does not have\n    // outputs.\n    if (Object.keys(outputNodeNameToKey).length === 0) {\n      allNodes.forEach(key => {\n        const node = nodes[key];\n        if (node.children.length === 0) {\n          outputs.push(node);\n        }\n      });\n    } else {\n      Object.keys(outputNodeNameToKey).forEach(name => {\n        const [nodeName] = getNodeNameAndIndex(name);\n        const node = nodes[nodeName];\n        if (node != null) {\n          node.signatureKey = outputNodeNameToKey[name];\n          outputs.push(node);\n        }\n      });\n    }\n    if (Object.keys(inputNodeNameToKey).length > 0) {\n      Object.keys(inputNodeNameToKey).forEach(name => {\n        const [nodeName] = getNodeNameAndIndex(name);\n        const node = nodes[nodeName];\n        if (node) {\n          node.signatureKey = inputNodeNameToKey[name];\n          inputs.push(node);\n        }\n      });\n    } else {\n      inputs = placeholders;\n    }\n    let functions = {};\n    if (graph.library != null && graph.library.function != null) {\n      functions = graph.library.function.reduce((functions, func) => {\n        functions[func.signature.name] = this.mapFunction(func);\n        return functions;\n      }, {});\n    }\n    const result = {\n      nodes,\n      inputs,\n      outputs,\n      weights,\n      placeholders,\n      signature,\n      functions\n    };\n    if (initNodes.length > 0) {\n      result.initNodes = initNodes;\n    }\n    return result;\n  }\n  mapSignatureEntries(entries) {\n    return Object.keys(entries || {}).reduce((prev, curr) => {\n      prev[entries[curr].name] = curr;\n      return prev;\n    }, {});\n  }\n  mapNode(node) {\n    // Unsupported ops will cause an error at run-time (not parse time), since\n    // they may not be used by the actual execution subgraph.\n    const mapper = getRegisteredOp(node.op) || this.opMappers[node.op] || {};\n    if (node.attr == null) {\n      node.attr = {};\n    }\n    const newNode = {\n      name: node.name,\n      op: node.op,\n      category: mapper.category,\n      inputNames: (node.input || []).map(input => input.startsWith('^') ? input.slice(1) : input),\n      inputs: [],\n      children: [],\n      inputParams: {},\n      attrParams: {},\n      rawAttrs: node.attr,\n      outputs: mapper.outputs\n    };\n    if (mapper.inputs != null) {\n      newNode.inputParams = mapper.inputs.reduce((map, param) => {\n        map[param.name] = {\n          type: param.type,\n          inputIndexStart: param.start,\n          inputIndexEnd: param.end\n        };\n        return map;\n      }, {});\n    }\n    if (mapper.attrs != null) {\n      newNode.attrParams = mapper.attrs.reduce((map, param) => {\n        const type = param.type;\n        let value = undefined;\n        switch (param.type) {\n          case 'string':\n            value = getStringParam(node.attr, param.tfName, param.defaultValue);\n            if (value === undefined && !!param.tfDeprecatedName) {\n              value = getStringParam(node.attr, param.tfDeprecatedName, param.defaultValue);\n            }\n            break;\n          case 'string[]':\n            value = getStringArrayParam(node.attr, param.tfName, param.defaultValue);\n            if (value === undefined && !!param.tfDeprecatedName) {\n              value = getStringArrayParam(node.attr, param.tfDeprecatedName, param.defaultValue);\n            }\n            break;\n          case 'number':\n            value = getNumberParam(node.attr, param.tfName, param.defaultValue || 0);\n            if (value === undefined && !!param.tfDeprecatedName) {\n              value = getNumberParam(node.attr, param.tfDeprecatedName, param.defaultValue);\n            }\n            break;\n          case 'number[]':\n            value = getNumericArrayParam(node.attr, param.tfName, param.defaultValue);\n            if (value === undefined && !!param.tfDeprecatedName) {\n              value = getNumericArrayParam(node.attr, param.tfDeprecatedName, param.defaultValue);\n            }\n            break;\n          case 'bool':\n            value = getBoolParam(node.attr, param.tfName, param.defaultValue);\n            if (value === undefined && !!param.tfDeprecatedName) {\n              value = getBoolParam(node.attr, param.tfDeprecatedName, param.defaultValue);\n            }\n            break;\n          case 'bool[]':\n            value = getBoolArrayParam(node.attr, param.tfName, param.defaultValue);\n            if (value === undefined && !!param.tfDeprecatedName) {\n              value = getBoolArrayParam(node.attr, param.tfDeprecatedName, param.defaultValue);\n            }\n            break;\n          case 'shape':\n            value = getTensorShapeParam(node.attr, param.tfName, param.defaultValue);\n            if (value === undefined && !!param.tfDeprecatedName) {\n              value = getTensorShapeParam(node.attr, param.tfDeprecatedName, param.defaultValue);\n            }\n            break;\n          case 'shape[]':\n            value = getTensorShapeArrayParam(node.attr, param.tfName, param.defaultValue);\n            if (value === undefined && !!param.tfDeprecatedName) {\n              value = getTensorShapeArrayParam(node.attr, param.tfDeprecatedName, param.defaultValue);\n            }\n            break;\n          case 'dtype':\n            value = getDtypeParam(node.attr, param.tfName, param.defaultValue);\n            if (value === undefined && !!param.tfDeprecatedName) {\n              value = getDtypeParam(node.attr, param.tfDeprecatedName, param.defaultValue);\n            }\n            break;\n          case 'dtype[]':\n            value = getDtypeArrayParam(node.attr, param.tfName, param.defaultValue);\n            if (value === undefined && !!param.tfDeprecatedName) {\n              value = getDtypeArrayParam(node.attr, param.tfDeprecatedName, param.defaultValue);\n            }\n            break;\n          case 'func':\n            value = getFuncParam(node.attr, param.tfName, param.defaultValue);\n            if (value === undefined && !!param.tfDeprecatedName) {\n              value = getFuncParam(node.attr, param.tfDeprecatedName, param.defaultValue);\n            }\n            break;\n          case 'tensor':\n          case 'tensors':\n            break;\n          default:\n            throw new Error(\"Unsupported param type: \".concat(param.type, \" for op: \").concat(node.op));\n        }\n        map[param.name] = {\n          value,\n          type\n        };\n        return map;\n      }, {});\n    }\n    return newNode;\n  }\n  // map the TFunctionDef to TFJS graph object\n  mapFunction(functionDef) {\n    const tfNodes = functionDef.nodeDef;\n    const placeholders = [];\n    const weights = [];\n    let nodes = {};\n    if (tfNodes != null) {\n      nodes = tfNodes.reduce((map, node) => {\n        map[node.name] = this.mapNode(node);\n        if (node.op === 'Const') {\n          weights.push(map[node.name]);\n        }\n        return map;\n      }, {});\n    }\n    const inputs = [];\n    const outputs = [];\n    functionDef.signature.inputArg.forEach(arg => {\n      const [nodeName] = getNodeNameAndIndex(arg.name);\n      const node = {\n        name: nodeName,\n        op: 'Placeholder',\n        inputs: [],\n        inputNames: [],\n        category: 'graph',\n        inputParams: {},\n        attrParams: {\n          dtype: {\n            value: parseDtypeParam(arg.type),\n            type: 'dtype'\n          }\n        },\n        children: []\n      };\n      node.signatureKey = arg.name;\n      inputs.push(node);\n      nodes[nodeName] = node;\n    });\n    const allNodes = Object.keys(nodes);\n    allNodes.forEach(key => {\n      const node = nodes[key];\n      node.inputNames.forEach((name, index) => {\n        const [nodeName,, outputName] = getNodeNameAndIndex(name);\n        const inputNode = nodes[nodeName];\n        if (inputNode.outputs != null) {\n          const outputIndex = inputNode.outputs.indexOf(outputName);\n          if (outputIndex !== -1) {\n            const inputName = \"\".concat(nodeName, \":\").concat(outputIndex);\n            // update the input name to use the mapped output index directly.\n            node.inputNames[index] = inputName;\n          }\n        }\n        node.inputs.push(inputNode);\n        inputNode.children.push(node);\n      });\n    });\n    const returnNodeMap = functionDef.ret;\n    functionDef.signature.outputArg.forEach(output => {\n      const [nodeName, index] = getNodeNameAndIndex(returnNodeMap[output.name]);\n      const node = nodes[nodeName];\n      if (node != null) {\n        node.defaultOutput = index;\n        outputs.push(node);\n      }\n    });\n    const signature = this.mapArgsToSignature(functionDef);\n    return {\n      nodes,\n      inputs,\n      outputs,\n      weights,\n      placeholders,\n      signature\n    };\n  }\n  mapArgsToSignature(functionDef) {\n    return {\n      methodName: functionDef.signature.name,\n      inputs: functionDef.signature.inputArg.reduce((map, arg) => {\n        map[arg.name] = this.mapArgToTensorInfo(arg);\n        return map;\n      }, {}),\n      outputs: functionDef.signature.outputArg.reduce((map, arg) => {\n        map[arg.name] = this.mapArgToTensorInfo(arg, functionDef.ret);\n        return map;\n      }, {})\n    };\n  }\n  mapArgToTensorInfo(arg, nameMap) {\n    let name = arg.name;\n    if (nameMap != null) {\n      name = nameMap[name];\n    }\n    return {\n      name,\n      dtype: arg.type\n    };\n  }\n}\nexport function decodeBase64(text) {\n  const global = env().global;\n  if (typeof global.atob !== 'undefined') {\n    return global.atob(text);\n  } else if (typeof Buffer !== 'undefined') {\n    return new Buffer(text, 'base64').toString();\n  } else {\n    throw new Error('Unable to decode base64 in this environment. ' + 'Missing built-in atob() or Buffer()');\n  }\n}\nexport function parseStringParam(s, keepCase) {\n  const value = Array.isArray(s) ? String.fromCharCode.apply(null, s) : decodeBase64(s);\n  return keepCase ? value : value.toLowerCase();\n}\nexport function getStringParam(attrs, name, def) {\n  let keepCase = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : false;\n  const param = attrs[name];\n  if (param != null) {\n    return parseStringParam(param.s, keepCase);\n  }\n  return def;\n}\nexport function getBoolParam(attrs, name, def) {\n  const param = attrs[name];\n  return param ? param.b : def;\n}\nexport function getNumberParam(attrs, name, def) {\n  const param = attrs[name] || {};\n  const value = param['i'] != null ? param['i'] : param['f'] != null ? param['f'] : def;\n  return typeof value === 'number' ? value : parseInt(value, 10);\n}\nexport function parseDtypeParam(value) {\n  if (typeof value === 'string') {\n    // tslint:disable-next-line:no-any\n    value = tensorflow.DataType[value];\n  }\n  switch (value) {\n    case tensorflow.DataType.DT_FLOAT:\n    case tensorflow.DataType.DT_HALF:\n      return 'float32';\n    case tensorflow.DataType.DT_INT32:\n    case tensorflow.DataType.DT_INT64:\n    case tensorflow.DataType.DT_INT8:\n    case tensorflow.DataType.DT_UINT8:\n      return 'int32';\n    case tensorflow.DataType.DT_BOOL:\n      return 'bool';\n    case tensorflow.DataType.DT_DOUBLE:\n      return 'float32';\n    case tensorflow.DataType.DT_STRING:\n      return 'string';\n    case tensorflow.DataType.DT_COMPLEX64:\n    case tensorflow.DataType.DT_COMPLEX128:\n      return 'complex64';\n    default:\n      // Unknown dtype error will happen at runtime (instead of parse time),\n      // since these nodes might not be used by the actual subgraph execution.\n      return null;\n  }\n}\nexport function getFuncParam(attrs, name, def) {\n  const param = attrs[name];\n  if (param && param.func) {\n    return param.func.name;\n  }\n  return def;\n}\nexport function getDtypeParam(attrs, name, def) {\n  const param = attrs[name];\n  if (param && param.type) {\n    return parseDtypeParam(param.type);\n  }\n  return def;\n}\nexport function getDtypeArrayParam(attrs, name, def) {\n  const param = attrs[name];\n  if (param && param.list && param.list.type) {\n    return param.list.type.map(v => parseDtypeParam(v));\n  }\n  return def;\n}\nexport function parseTensorShapeParam(shape) {\n  if (shape.unknownRank) {\n    return undefined;\n  }\n  if (shape.dim != null) {\n    return shape.dim.map(dim => typeof dim.size === 'number' ? dim.size : parseInt(dim.size, 10));\n  }\n  return [];\n}\nexport function getTensorShapeParam(attrs, name, def) {\n  const param = attrs[name];\n  if (param && param.shape) {\n    return parseTensorShapeParam(param.shape);\n  }\n  return def;\n}\nexport function getNumericArrayParam(attrs, name, def) {\n  const param = attrs[name];\n  if (param) {\n    return ((param.list.f && param.list.f.length ? param.list.f : param.list.i) || []).map(v => typeof v === 'number' ? v : parseInt(v, 10));\n  }\n  return def;\n}\nexport function getStringArrayParam(attrs, name, def) {\n  let keepCase = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : false;\n  const param = attrs[name];\n  if (param && param.list && param.list.s) {\n    return param.list.s.map(v => {\n      return parseStringParam(v, keepCase);\n    });\n  }\n  return def;\n}\nexport function getTensorShapeArrayParam(attrs, name, def) {\n  const param = attrs[name];\n  if (param && param.list && param.list.shape) {\n    return param.list.shape.map(v => {\n      return parseTensorShapeParam(v);\n    });\n  }\n  return def;\n}\nexport function getBoolArrayParam(attrs, name, def) {\n  const param = attrs[name];\n  if (param && param.list && param.list.b) {\n    return param.list.b;\n  }\n  return def;\n}", "map": {"version": 3, "names": ["env", "tensorflow", "getRegisteredOp", "getNodeNameAndIndex", "arithmetic", "basicMath", "control", "convolution", "creation", "dynamic", "evaluation", "graph", "hashTable", "image", "logical", "matrices", "normalization", "reduction", "sliceJoin", "sparse", "spectral", "string", "transformation", "OperationMapper", "Instance", "_instance", "constructor", "ops", "map<PERSON><PERSON><PERSON>", "concat", "map", "op", "json", "opMappers", "reduce", "mapper", "tfOpName", "transformGraph", "signature", "arguments", "length", "undefined", "tfNodes", "node", "placeholders", "weights", "initNodes", "nodes", "name", "mapNode", "startsWith", "push", "input", "inputs", "outputs", "inputNodeNameToKey", "outputNodeNameToKey", "mapSignatureEntries", "allNodes", "Object", "keys", "for<PERSON>ach", "key", "inputNames", "index", "nodeName", "outputName", "inputNode", "outputIndex", "indexOf", "inputName", "children", "signature<PERSON>ey", "functions", "library", "function", "func", "mapFunction", "result", "entries", "prev", "curr", "attr", "newNode", "category", "slice", "inputParams", "attrParams", "rawAttrs", "param", "type", "inputIndexStart", "start", "inputIndexEnd", "end", "attrs", "value", "getStringParam", "tfName", "defaultValue", "tfDeprecatedName", "getStringArrayParam", "getNumberParam", "getNumericArrayParam", "getBoolParam", "getBoolArrayParam", "getTensorShapeParam", "getTensorShapeArrayParam", "getDtypeParam", "getDtypeArrayParam", "getFuncParam", "Error", "functionDef", "nodeDef", "inputArg", "arg", "dtype", "parseDtypeParam", "returnNodeMap", "ret", "outputArg", "output", "defaultOutput", "mapArgsToSignature", "methodName", "mapArgToTensorInfo", "nameMap", "decodeBase64", "text", "global", "atob", "<PERSON><PERSON><PERSON>", "toString", "parseStringParam", "s", "keepCase", "Array", "isArray", "String", "fromCharCode", "apply", "toLowerCase", "def", "b", "parseInt", "DataType", "DT_FLOAT", "DT_HALF", "DT_INT32", "DT_INT64", "DT_INT8", "DT_UINT8", "DT_BOOL", "DT_DOUBLE", "DT_STRING", "DT_COMPLEX64", "DT_COMPLEX128", "list", "v", "parseTensorShapeParam", "shape", "unknownRank", "dim", "size", "f", "i"], "sources": ["C:\\tfjs-converter\\src\\operations\\operation_mapper.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2018 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {DataType, env} from '@tensorflow/tfjs-core';\n\nimport * as tensorflow from '../data/compiled_api';\n\nimport {getRegisteredOp} from './custom_op/register';\nimport {getNodeNameAndIndex} from './executors/utils';\nimport * as arithmetic from './op_list/arithmetic';\nimport * as basicMath from './op_list/basic_math';\nimport * as control from './op_list/control';\nimport * as convolution from './op_list/convolution';\nimport * as creation from './op_list/creation';\nimport * as dynamic from './op_list/dynamic';\nimport * as evaluation from './op_list/evaluation';\nimport * as graph from './op_list/graph';\nimport * as hashTable from './op_list/hash_table';\nimport * as image from './op_list/image';\nimport * as logical from './op_list/logical';\nimport * as matrices from './op_list/matrices';\nimport * as normalization from './op_list/normalization';\nimport * as reduction from './op_list/reduction';\nimport * as sliceJoin from './op_list/slice_join';\nimport * as sparse from './op_list/sparse';\nimport * as spectral from './op_list/spectral';\nimport * as string from './op_list/string';\nimport * as transformation from './op_list/transformation';\nimport {Graph, InputParamValue, Node, OpMapper, ParamValue} from './types';\n\nexport class OperationMapper {\n  private static _instance: OperationMapper;\n\n  private opMappers: {[key: string]: OpMapper};\n\n  // Singleton instance for the mapper\n  public static get Instance() {\n    return this._instance || (this._instance = new this());\n  }\n\n  // Loads the op mapping from the JSON file.\n  private constructor() {\n    const ops = [\n      arithmetic, basicMath, control, convolution, creation, dynamic,\n      evaluation, graph, hashTable, image, logical, matrices, normalization,\n      reduction, sliceJoin, sparse, spectral, string, transformation\n    ];\n    const mappersJson: OpMapper[] = [].concat(...ops.map(op => op.json));\n\n    this.opMappers = mappersJson.reduce<{[key: string]: OpMapper}>(\n        (map, mapper: OpMapper) => {\n          map[mapper.tfOpName] = mapper;\n          return map;\n        },\n        {});\n  }\n\n  // Converts the model inference graph from Tensorflow GraphDef to local\n  // representation for TensorFlow.js API\n  transformGraph(\n      graph: tensorflow.IGraphDef,\n      signature: tensorflow.ISignatureDef = {}): Graph {\n    const tfNodes = graph.node;\n    const placeholders: Node[] = [];\n    const weights: Node[] = [];\n    const initNodes: Node[] = [];\n    const nodes = tfNodes.reduce<{[key: string]: Node}>((map, node) => {\n      map[node.name] = this.mapNode(node);\n      if (node.op.startsWith('Placeholder')) {\n        placeholders.push(map[node.name]);\n      } else if (node.op === 'Const') {\n        weights.push(map[node.name]);\n      } else if (node.input == null || node.input.length === 0) {\n        initNodes.push(map[node.name]);\n      }\n      return map;\n    }, {});\n\n    let inputs: Node[] = [];\n    const outputs: Node[] = [];\n    let inputNodeNameToKey: {[key: string]: string} = {};\n    let outputNodeNameToKey: {[key: string]: string} = {};\n    if (signature != null) {\n      inputNodeNameToKey = this.mapSignatureEntries(signature.inputs);\n      outputNodeNameToKey = this.mapSignatureEntries(signature.outputs);\n    }\n    const allNodes = Object.keys(nodes);\n    allNodes.forEach(key => {\n      const node = nodes[key];\n      node.inputNames.forEach((name, index) => {\n        const [nodeName, , outputName] = getNodeNameAndIndex(name);\n        const inputNode = nodes[nodeName];\n        if (inputNode.outputs != null) {\n          const outputIndex = inputNode.outputs.indexOf(outputName);\n          if (outputIndex !== -1) {\n            const inputName = `${nodeName}:${outputIndex}`;\n            // update the input name to use the mapped output index directly.\n            node.inputNames[index] = inputName;\n          }\n        }\n        node.inputs.push(inputNode);\n        inputNode.children.push(node);\n      });\n    });\n\n    // if signature has not outputs set, add any node that does not have\n    // outputs.\n    if (Object.keys(outputNodeNameToKey).length === 0) {\n      allNodes.forEach(key => {\n        const node = nodes[key];\n        if (node.children.length === 0) {\n          outputs.push(node);\n        }\n      });\n    } else {\n      Object.keys(outputNodeNameToKey).forEach(name => {\n        const [nodeName, ] = getNodeNameAndIndex(name);\n        const node = nodes[nodeName];\n        if (node != null) {\n          node.signatureKey = outputNodeNameToKey[name];\n          outputs.push(node);\n        }\n      });\n    }\n\n    if (Object.keys(inputNodeNameToKey).length > 0) {\n      Object.keys(inputNodeNameToKey).forEach(name => {\n        const [nodeName, ] = getNodeNameAndIndex(name);\n        const node = nodes[nodeName];\n        if (node) {\n          node.signatureKey = inputNodeNameToKey[name];\n          inputs.push(node);\n        }\n      });\n    } else {\n      inputs = placeholders;\n    }\n\n    let functions = {};\n    if (graph.library != null && graph.library.function != null) {\n      functions = graph.library.function.reduce((functions, func) => {\n        functions[func.signature.name] = this.mapFunction(func);\n        return functions;\n      }, {} as {[key: string]: Graph});\n    }\n\n    const result: Graph =\n        {nodes, inputs, outputs, weights, placeholders, signature, functions};\n\n    if (initNodes.length > 0) {\n      result.initNodes = initNodes;\n    }\n\n    return result;\n  }\n\n  private mapSignatureEntries(entries: {[k: string]: tensorflow.ITensorInfo}) {\n    return Object.keys(entries || {})\n        .reduce<{[key: string]: string}>((prev, curr) => {\n          prev[entries[curr].name] = curr;\n          return prev;\n        }, {});\n  }\n\n  private mapNode(node: tensorflow.INodeDef): Node {\n    // Unsupported ops will cause an error at run-time (not parse time), since\n    // they may not be used by the actual execution subgraph.\n    const mapper =\n        getRegisteredOp(node.op) || this.opMappers[node.op] || {} as OpMapper;\n    if (node.attr == null) {\n      node.attr = {};\n    }\n\n    const newNode: Node = {\n      name: node.name,\n      op: node.op,\n      category: mapper.category,\n      inputNames:\n          (node.input ||\n           []).map(input => input.startsWith('^') ? input.slice(1) : input),\n      inputs: [],\n      children: [],\n      inputParams: {},\n      attrParams: {},\n      rawAttrs: node.attr,\n      outputs: mapper.outputs\n    };\n\n    if (mapper.inputs != null) {\n      newNode.inputParams =\n          mapper.inputs.reduce<{[key: string]: InputParamValue}>(\n              (map, param) => {\n                map[param.name] = {\n                  type: param.type,\n                  inputIndexStart: param.start,\n                  inputIndexEnd: param.end\n                };\n                return map;\n              },\n              {});\n    }\n    if (mapper.attrs != null) {\n      newNode.attrParams =\n          mapper.attrs.reduce<{[key: string]: ParamValue}>((map, param) => {\n            const type = param.type;\n            let value = undefined;\n            switch (param.type) {\n              case 'string':\n                value = getStringParam(\n                    node.attr, param.tfName, param.defaultValue as string);\n\n                if (value === undefined && !!param.tfDeprecatedName) {\n                  value = getStringParam(\n                      node.attr, param.tfDeprecatedName,\n                      param.defaultValue as string);\n                }\n                break;\n              case 'string[]':\n                value = getStringArrayParam(\n                    node.attr, param.tfName, param.defaultValue as string[]);\n\n                if (value === undefined && !!param.tfDeprecatedName) {\n                  value = getStringArrayParam(\n                      node.attr, param.tfDeprecatedName,\n                      param.defaultValue as string[]);\n                }\n                break;\n              case 'number':\n                value = getNumberParam(\n                    node.attr, param.tfName,\n                    (param.defaultValue || 0) as number);\n                if (value === undefined && !!param.tfDeprecatedName) {\n                  value = getNumberParam(\n                      node.attr, param.tfDeprecatedName,\n                      param.defaultValue as number);\n                }\n                break;\n              case 'number[]':\n                value = getNumericArrayParam(\n                    node.attr, param.tfName, param.defaultValue as number[]);\n                if (value === undefined && !!param.tfDeprecatedName) {\n                  value = getNumericArrayParam(\n                      node.attr, param.tfDeprecatedName,\n                      param.defaultValue as number[]);\n                }\n                break;\n              case 'bool':\n                value = getBoolParam(\n                    node.attr, param.tfName, param.defaultValue as boolean);\n                if (value === undefined && !!param.tfDeprecatedName) {\n                  value = getBoolParam(\n                      node.attr, param.tfDeprecatedName,\n                      param.defaultValue as boolean);\n                }\n                break;\n              case 'bool[]':\n                value = getBoolArrayParam(\n                    node.attr, param.tfName, param.defaultValue as boolean[]);\n                if (value === undefined && !!param.tfDeprecatedName) {\n                  value = getBoolArrayParam(\n                      node.attr, param.tfDeprecatedName,\n                      param.defaultValue as boolean[]);\n                }\n                break;\n              case 'shape':\n                value = getTensorShapeParam(\n                    node.attr, param.tfName, param.defaultValue as number[]);\n                if (value === undefined && !!param.tfDeprecatedName) {\n                  value = getTensorShapeParam(\n                      node.attr, param.tfDeprecatedName,\n                      param.defaultValue as number[]);\n                }\n                break;\n              case 'shape[]':\n                value = getTensorShapeArrayParam(\n                    node.attr, param.tfName, param.defaultValue as number[][]);\n                if (value === undefined && !!param.tfDeprecatedName) {\n                  value = getTensorShapeArrayParam(\n                      node.attr, param.tfDeprecatedName,\n                      param.defaultValue as number[][]);\n                }\n                break;\n              case 'dtype':\n                value = getDtypeParam(\n                    node.attr, param.tfName, param.defaultValue as DataType);\n                if (value === undefined && !!param.tfDeprecatedName) {\n                  value = getDtypeParam(\n                      node.attr, param.tfDeprecatedName,\n                      param.defaultValue as DataType);\n                }\n                break;\n              case 'dtype[]':\n                value = getDtypeArrayParam(\n                    node.attr, param.tfName, param.defaultValue as DataType[]);\n                if (value === undefined && !!param.tfDeprecatedName) {\n                  value = getDtypeArrayParam(\n                      node.attr, param.tfDeprecatedName,\n                      param.defaultValue as DataType[]);\n                }\n                break;\n              case 'func':\n                value = getFuncParam(\n                    node.attr, param.tfName, param.defaultValue as string);\n                if (value === undefined && !!param.tfDeprecatedName) {\n                  value = getFuncParam(\n                      node.attr, param.tfDeprecatedName,\n                      param.defaultValue as string);\n                }\n                break;\n              case 'tensor':\n              case 'tensors':\n                break;\n              default:\n                throw new Error(\n                    `Unsupported param type: ${param.type} for op: ${node.op}`);\n            }\n            map[param.name] = {value, type};\n            return map;\n          }, {});\n    }\n    return newNode;\n  }\n\n  // map the TFunctionDef to TFJS graph object\n  private mapFunction(functionDef: tensorflow.IFunctionDef): Graph {\n    const tfNodes = functionDef.nodeDef;\n    const placeholders: Node[] = [];\n    const weights: Node[] = [];\n    let nodes: {[key: string]: Node} = {};\n    if (tfNodes != null) {\n      nodes = tfNodes.reduce<{[key: string]: Node}>((map, node) => {\n        map[node.name] = this.mapNode(node);\n        if (node.op === 'Const') {\n          weights.push(map[node.name]);\n        }\n        return map;\n      }, {});\n    }\n    const inputs: Node[] = [];\n    const outputs: Node[] = [];\n\n    functionDef.signature.inputArg.forEach(arg => {\n      const [nodeName, ] = getNodeNameAndIndex(arg.name);\n      const node: Node = {\n        name: nodeName,\n        op: 'Placeholder',\n        inputs: [],\n        inputNames: [],\n        category: 'graph',\n        inputParams: {},\n        attrParams: {dtype: {value: parseDtypeParam(arg.type), type: 'dtype'}},\n        children: []\n      };\n      node.signatureKey = arg.name;\n      inputs.push(node);\n      nodes[nodeName] = node;\n    });\n\n    const allNodes = Object.keys(nodes);\n    allNodes.forEach(key => {\n      const node = nodes[key];\n      node.inputNames.forEach((name, index) => {\n        const [nodeName, , outputName] = getNodeNameAndIndex(name);\n        const inputNode = nodes[nodeName];\n        if (inputNode.outputs != null) {\n          const outputIndex = inputNode.outputs.indexOf(outputName);\n          if (outputIndex !== -1) {\n            const inputName = `${nodeName}:${outputIndex}`;\n            // update the input name to use the mapped output index directly.\n            node.inputNames[index] = inputName;\n          }\n        }\n        node.inputs.push(inputNode);\n        inputNode.children.push(node);\n      });\n    });\n\n    const returnNodeMap = functionDef.ret;\n\n    functionDef.signature.outputArg.forEach(output => {\n      const [nodeName, index] = getNodeNameAndIndex(returnNodeMap[output.name]);\n      const node = nodes[nodeName];\n      if (node != null) {\n        node.defaultOutput = index;\n        outputs.push(node);\n      }\n    });\n\n    const signature = this.mapArgsToSignature(functionDef);\n    return {nodes, inputs, outputs, weights, placeholders, signature};\n  }\n\n  private mapArgsToSignature(functionDef: tensorflow.IFunctionDef):\n      tensorflow.ISignatureDef {\n    return {\n      methodName: functionDef.signature.name,\n      inputs: functionDef.signature.inputArg.reduce(\n          (map, arg) => {\n            map[arg.name] = this.mapArgToTensorInfo(arg);\n            return map;\n          },\n          {} as {[key: string]: tensorflow.ITensorInfo}),\n      outputs: functionDef.signature.outputArg.reduce(\n          (map, arg) => {\n            map[arg.name] = this.mapArgToTensorInfo(arg, functionDef.ret);\n            return map;\n          },\n          {} as {[key: string]: tensorflow.ITensorInfo}),\n    };\n  }\n\n  private mapArgToTensorInfo(\n      arg: tensorflow.OpDef.IArgDef,\n      nameMap?: {[key: string]: string}): tensorflow.ITensorInfo {\n    let name = arg.name;\n    if (nameMap != null) {\n      name = nameMap[name];\n    }\n    return {name, dtype: arg.type};\n  }\n}\n\nexport function decodeBase64(text: string): string {\n  const global = env().global;\n  if (typeof global.atob !== 'undefined') {\n    return global.atob(text);\n  } else if (typeof Buffer !== 'undefined') {\n    return new Buffer(text, 'base64').toString();\n  } else {\n    throw new Error(\n        'Unable to decode base64 in this environment. ' +\n        'Missing built-in atob() or Buffer()');\n  }\n}\n\nexport function parseStringParam(s: []|string, keepCase: boolean): string {\n  const value =\n      Array.isArray(s) ? String.fromCharCode.apply(null, s) : decodeBase64(s);\n  return keepCase ? value : value.toLowerCase();\n}\n\nexport function getStringParam(\n    attrs: {[key: string]: tensorflow.IAttrValue}, name: string, def: string,\n    keepCase = false): string {\n  const param = attrs[name];\n  if (param != null) {\n    return parseStringParam(param.s, keepCase);\n  }\n  return def;\n}\n\nexport function getBoolParam(\n    attrs: {[key: string]: tensorflow.IAttrValue}, name: string,\n    def: boolean): boolean {\n  const param = attrs[name];\n  return param ? param.b : def;\n}\n\nexport function getNumberParam(\n    attrs: {[key: string]: tensorflow.IAttrValue}, name: string,\n    def: number): number {\n  const param = attrs[name] || {};\n  const value =\n      param['i'] != null ? param['i'] : (param['f'] != null ? param['f'] : def);\n  return (typeof value === 'number') ? value : parseInt(value, 10);\n}\n\nexport function parseDtypeParam(value: string|tensorflow.DataType): DataType {\n  if (typeof (value) === 'string') {\n    // tslint:disable-next-line:no-any\n    value = tensorflow.DataType[value as any];\n  }\n  switch (value) {\n    case tensorflow.DataType.DT_FLOAT:\n    case tensorflow.DataType.DT_HALF:\n      return 'float32';\n    case tensorflow.DataType.DT_INT32:\n    case tensorflow.DataType.DT_INT64:\n    case tensorflow.DataType.DT_INT8:\n    case tensorflow.DataType.DT_UINT8:\n      return 'int32';\n    case tensorflow.DataType.DT_BOOL:\n      return 'bool';\n    case tensorflow.DataType.DT_DOUBLE:\n      return 'float32';\n    case tensorflow.DataType.DT_STRING:\n      return 'string';\n    case tensorflow.DataType.DT_COMPLEX64:\n    case tensorflow.DataType.DT_COMPLEX128:\n      return 'complex64';\n    default:\n      // Unknown dtype error will happen at runtime (instead of parse time),\n      // since these nodes might not be used by the actual subgraph execution.\n      return null;\n  }\n}\n\nexport function getFuncParam(\n    attrs: {[key: string]: tensorflow.IAttrValue}, name: string,\n    def: string): string {\n  const param = attrs[name];\n  if (param && param.func) {\n    return param.func.name;\n  }\n  return def;\n}\n\nexport function getDtypeParam(\n    attrs: {[key: string]: tensorflow.IAttrValue}, name: string,\n    def: DataType): DataType {\n  const param = attrs[name];\n  if (param && param.type) {\n    return parseDtypeParam(param.type);\n  }\n  return def;\n}\n\nexport function getDtypeArrayParam(\n    attrs: {[key: string]: tensorflow.IAttrValue}, name: string,\n    def: DataType[]): DataType[] {\n  const param = attrs[name];\n  if (param && param.list && param.list.type) {\n    return param.list.type.map(v => parseDtypeParam(v));\n  }\n  return def;\n}\n\nexport function parseTensorShapeParam(shape: tensorflow.ITensorShape): number[]|\n    undefined {\n  if (shape.unknownRank) {\n    return undefined;\n  }\n  if (shape.dim != null) {\n    return shape.dim.map(\n        dim =>\n            (typeof dim.size === 'number') ? dim.size : parseInt(dim.size, 10));\n  }\n  return [];\n}\n\nexport function getTensorShapeParam(\n    attrs: {[key: string]: tensorflow.IAttrValue}, name: string,\n    def?: number[]): number[]|undefined {\n  const param = attrs[name];\n  if (param && param.shape) {\n    return parseTensorShapeParam(param.shape);\n  }\n  return def;\n}\n\nexport function getNumericArrayParam(\n    attrs: {[key: string]: tensorflow.IAttrValue}, name: string,\n    def: number[]): number[] {\n  const param = attrs[name];\n  if (param) {\n    return ((param.list.f && param.list.f.length ? param.list.f :\n                                                   param.list.i) ||\n            [])\n        .map(v => (typeof v === 'number') ? v : parseInt(v, 10));\n  }\n  return def;\n}\n\nexport function getStringArrayParam(\n    attrs: {[key: string]: tensorflow.IAttrValue}, name: string, def: string[],\n    keepCase = false): string[] {\n  const param = attrs[name];\n  if (param && param.list && param.list.s) {\n    return param.list.s.map((v) => {\n      return parseStringParam(v, keepCase);\n    });\n  }\n  return def;\n}\n\nexport function getTensorShapeArrayParam(\n    attrs: {[key: string]: tensorflow.IAttrValue}, name: string,\n    def: number[][]): number[][] {\n  const param = attrs[name];\n  if (param && param.list && param.list.shape) {\n    return param.list.shape.map((v) => {\n      return parseTensorShapeParam(v);\n    });\n  }\n  return def;\n}\n\nexport function getBoolArrayParam(\n    attrs: {[key: string]: tensorflow.IAttrValue}, name: string,\n    def: boolean[]): boolean[] {\n  const param = attrs[name];\n  if (param && param.list && param.list.b) {\n    return param.list.b;\n  }\n  return def;\n}\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;AAiBA,SAAkBA,GAAG,QAAO,uBAAuB;AAEnD,OAAO,KAAKC,UAAU,MAAM,sBAAsB;AAElD,SAAQC,eAAe,QAAO,sBAAsB;AACpD,SAAQC,mBAAmB,QAAO,mBAAmB;AACrD,OAAO,KAAKC,UAAU,MAAM,sBAAsB;AAClD,OAAO,KAAKC,SAAS,MAAM,sBAAsB;AACjD,OAAO,KAAKC,OAAO,MAAM,mBAAmB;AAC5C,OAAO,KAAKC,WAAW,MAAM,uBAAuB;AACpD,OAAO,KAAKC,QAAQ,MAAM,oBAAoB;AAC9C,OAAO,KAAKC,OAAO,MAAM,mBAAmB;AAC5C,OAAO,KAAKC,UAAU,MAAM,sBAAsB;AAClD,OAAO,KAAKC,KAAK,MAAM,iBAAiB;AACxC,OAAO,KAAKC,SAAS,MAAM,sBAAsB;AACjD,OAAO,KAAKC,KAAK,MAAM,iBAAiB;AACxC,OAAO,KAAKC,OAAO,MAAM,mBAAmB;AAC5C,OAAO,KAAKC,QAAQ,MAAM,oBAAoB;AAC9C,OAAO,KAAKC,aAAa,MAAM,yBAAyB;AACxD,OAAO,KAAKC,SAAS,MAAM,qBAAqB;AAChD,OAAO,KAAKC,SAAS,MAAM,sBAAsB;AACjD,OAAO,KAAKC,MAAM,MAAM,kBAAkB;AAC1C,OAAO,KAAKC,QAAQ,MAAM,oBAAoB;AAC9C,OAAO,KAAKC,MAAM,MAAM,kBAAkB;AAC1C,OAAO,KAAKC,cAAc,MAAM,0BAA0B;AAG1D,OAAM,MAAOC,eAAe;EAK1B;EACO,WAAWC,QAAQA,CAAA;IACxB,OAAO,IAAI,CAACC,SAAS,KAAK,IAAI,CAACA,SAAS,GAAG,IAAI,IAAI,EAAE,CAAC;EACxD;EAEA;EACAC,YAAA;IACE,MAAMC,GAAG,GAAG,CACVvB,UAAU,EAAEC,SAAS,EAAEC,OAAO,EAAEC,WAAW,EAAEC,QAAQ,EAAEC,OAAO,EAC9DC,UAAU,EAAEC,KAAK,EAAEC,SAAS,EAAEC,KAAK,EAAEC,OAAO,EAAEC,QAAQ,EAAEC,aAAa,EACrEC,SAAS,EAAEC,SAAS,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,cAAc,CAC/D;IACD,MAAMM,WAAW,GAAe,EAAE,CAACC,MAAM,CAAC,GAAGF,GAAG,CAACG,GAAG,CAACC,EAAE,IAAIA,EAAE,CAACC,IAAI,CAAC,CAAC;IAEpE,IAAI,CAACC,SAAS,GAAGL,WAAW,CAACM,MAAM,CAC/B,CAACJ,GAAG,EAAEK,MAAgB,KAAI;MACxBL,GAAG,CAACK,MAAM,CAACC,QAAQ,CAAC,GAAGD,MAAM;MAC7B,OAAOL,GAAG;IACZ,CAAC,EACD,EAAE,CAAC;EACT;EAEA;EACA;EACAO,cAAcA,CACV1B,KAA2B,EACa;IAAA,IAAxC2B,SAAA,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAsC,EAAE;IAC1C,MAAMG,OAAO,GAAG/B,KAAK,CAACgC,IAAI;IAC1B,MAAMC,YAAY,GAAW,EAAE;IAC/B,MAAMC,OAAO,GAAW,EAAE;IAC1B,MAAMC,SAAS,GAAW,EAAE;IAC5B,MAAMC,KAAK,GAAGL,OAAO,CAACR,MAAM,CAAwB,CAACJ,GAAG,EAAEa,IAAI,KAAI;MAChEb,GAAG,CAACa,IAAI,CAACK,IAAI,CAAC,GAAG,IAAI,CAACC,OAAO,CAACN,IAAI,CAAC;MACnC,IAAIA,IAAI,CAACZ,EAAE,CAACmB,UAAU,CAAC,aAAa,CAAC,EAAE;QACrCN,YAAY,CAACO,IAAI,CAACrB,GAAG,CAACa,IAAI,CAACK,IAAI,CAAC,CAAC;OAClC,MAAM,IAAIL,IAAI,CAACZ,EAAE,KAAK,OAAO,EAAE;QAC9Bc,OAAO,CAACM,IAAI,CAACrB,GAAG,CAACa,IAAI,CAACK,IAAI,CAAC,CAAC;OAC7B,MAAM,IAAIL,IAAI,CAACS,KAAK,IAAI,IAAI,IAAIT,IAAI,CAACS,KAAK,CAACZ,MAAM,KAAK,CAAC,EAAE;QACxDM,SAAS,CAACK,IAAI,CAACrB,GAAG,CAACa,IAAI,CAACK,IAAI,CAAC,CAAC;;MAEhC,OAAOlB,GAAG;IACZ,CAAC,EAAE,EAAE,CAAC;IAEN,IAAIuB,MAAM,GAAW,EAAE;IACvB,MAAMC,OAAO,GAAW,EAAE;IAC1B,IAAIC,kBAAkB,GAA4B,EAAE;IACpD,IAAIC,mBAAmB,GAA4B,EAAE;IACrD,IAAIlB,SAAS,IAAI,IAAI,EAAE;MACrBiB,kBAAkB,GAAG,IAAI,CAACE,mBAAmB,CAACnB,SAAS,CAACe,MAAM,CAAC;MAC/DG,mBAAmB,GAAG,IAAI,CAACC,mBAAmB,CAACnB,SAAS,CAACgB,OAAO,CAAC;;IAEnE,MAAMI,QAAQ,GAAGC,MAAM,CAACC,IAAI,CAACb,KAAK,CAAC;IACnCW,QAAQ,CAACG,OAAO,CAACC,GAAG,IAAG;MACrB,MAAMnB,IAAI,GAAGI,KAAK,CAACe,GAAG,CAAC;MACvBnB,IAAI,CAACoB,UAAU,CAACF,OAAO,CAAC,CAACb,IAAI,EAAEgB,KAAK,KAAI;QACtC,MAAM,CAACC,QAAQ,GAAIC,UAAU,CAAC,GAAG/D,mBAAmB,CAAC6C,IAAI,CAAC;QAC1D,MAAMmB,SAAS,GAAGpB,KAAK,CAACkB,QAAQ,CAAC;QACjC,IAAIE,SAAS,CAACb,OAAO,IAAI,IAAI,EAAE;UAC7B,MAAMc,WAAW,GAAGD,SAAS,CAACb,OAAO,CAACe,OAAO,CAACH,UAAU,CAAC;UACzD,IAAIE,WAAW,KAAK,CAAC,CAAC,EAAE;YACtB,MAAME,SAAS,MAAAzC,MAAA,CAAMoC,QAAQ,OAAApC,MAAA,CAAIuC,WAAW,CAAE;YAC9C;YACAzB,IAAI,CAACoB,UAAU,CAACC,KAAK,CAAC,GAAGM,SAAS;;;QAGtC3B,IAAI,CAACU,MAAM,CAACF,IAAI,CAACgB,SAAS,CAAC;QAC3BA,SAAS,CAACI,QAAQ,CAACpB,IAAI,CAACR,IAAI,CAAC;MAC/B,CAAC,CAAC;IACJ,CAAC,CAAC;IAEF;IACA;IACA,IAAIgB,MAAM,CAACC,IAAI,CAACJ,mBAAmB,CAAC,CAAChB,MAAM,KAAK,CAAC,EAAE;MACjDkB,QAAQ,CAACG,OAAO,CAACC,GAAG,IAAG;QACrB,MAAMnB,IAAI,GAAGI,KAAK,CAACe,GAAG,CAAC;QACvB,IAAInB,IAAI,CAAC4B,QAAQ,CAAC/B,MAAM,KAAK,CAAC,EAAE;UAC9Bc,OAAO,CAACH,IAAI,CAACR,IAAI,CAAC;;MAEtB,CAAC,CAAC;KACH,MAAM;MACLgB,MAAM,CAACC,IAAI,CAACJ,mBAAmB,CAAC,CAACK,OAAO,CAACb,IAAI,IAAG;QAC9C,MAAM,CAACiB,QAAQ,CAAG,GAAG9D,mBAAmB,CAAC6C,IAAI,CAAC;QAC9C,MAAML,IAAI,GAAGI,KAAK,CAACkB,QAAQ,CAAC;QAC5B,IAAItB,IAAI,IAAI,IAAI,EAAE;UAChBA,IAAI,CAAC6B,YAAY,GAAGhB,mBAAmB,CAACR,IAAI,CAAC;UAC7CM,OAAO,CAACH,IAAI,CAACR,IAAI,CAAC;;MAEtB,CAAC,CAAC;;IAGJ,IAAIgB,MAAM,CAACC,IAAI,CAACL,kBAAkB,CAAC,CAACf,MAAM,GAAG,CAAC,EAAE;MAC9CmB,MAAM,CAACC,IAAI,CAACL,kBAAkB,CAAC,CAACM,OAAO,CAACb,IAAI,IAAG;QAC7C,MAAM,CAACiB,QAAQ,CAAG,GAAG9D,mBAAmB,CAAC6C,IAAI,CAAC;QAC9C,MAAML,IAAI,GAAGI,KAAK,CAACkB,QAAQ,CAAC;QAC5B,IAAItB,IAAI,EAAE;UACRA,IAAI,CAAC6B,YAAY,GAAGjB,kBAAkB,CAACP,IAAI,CAAC;UAC5CK,MAAM,CAACF,IAAI,CAACR,IAAI,CAAC;;MAErB,CAAC,CAAC;KACH,MAAM;MACLU,MAAM,GAAGT,YAAY;;IAGvB,IAAI6B,SAAS,GAAG,EAAE;IAClB,IAAI9D,KAAK,CAAC+D,OAAO,IAAI,IAAI,IAAI/D,KAAK,CAAC+D,OAAO,CAACC,QAAQ,IAAI,IAAI,EAAE;MAC3DF,SAAS,GAAG9D,KAAK,CAAC+D,OAAO,CAACC,QAAQ,CAACzC,MAAM,CAAC,CAACuC,SAAS,EAAEG,IAAI,KAAI;QAC5DH,SAAS,CAACG,IAAI,CAACtC,SAAS,CAACU,IAAI,CAAC,GAAG,IAAI,CAAC6B,WAAW,CAACD,IAAI,CAAC;QACvD,OAAOH,SAAS;MAClB,CAAC,EAAE,EAA4B,CAAC;;IAGlC,MAAMK,MAAM,GACR;MAAC/B,KAAK;MAAEM,MAAM;MAAEC,OAAO;MAAET,OAAO;MAAED,YAAY;MAAEN,SAAS;MAAEmC;IAAS,CAAC;IAEzE,IAAI3B,SAAS,CAACN,MAAM,GAAG,CAAC,EAAE;MACxBsC,MAAM,CAAChC,SAAS,GAAGA,SAAS;;IAG9B,OAAOgC,MAAM;EACf;EAEQrB,mBAAmBA,CAACsB,OAA8C;IACxE,OAAOpB,MAAM,CAACC,IAAI,CAACmB,OAAO,IAAI,EAAE,CAAC,CAC5B7C,MAAM,CAA0B,CAAC8C,IAAI,EAAEC,IAAI,KAAI;MAC9CD,IAAI,CAACD,OAAO,CAACE,IAAI,CAAC,CAACjC,IAAI,CAAC,GAAGiC,IAAI;MAC/B,OAAOD,IAAI;IACb,CAAC,EAAE,EAAE,CAAC;EACZ;EAEQ/B,OAAOA,CAACN,IAAyB;IACvC;IACA;IACA,MAAMR,MAAM,GACRjC,eAAe,CAACyC,IAAI,CAACZ,EAAE,CAAC,IAAI,IAAI,CAACE,SAAS,CAACU,IAAI,CAACZ,EAAE,CAAC,IAAI,EAAc;IACzE,IAAIY,IAAI,CAACuC,IAAI,IAAI,IAAI,EAAE;MACrBvC,IAAI,CAACuC,IAAI,GAAG,EAAE;;IAGhB,MAAMC,OAAO,GAAS;MACpBnC,IAAI,EAAEL,IAAI,CAACK,IAAI;MACfjB,EAAE,EAAEY,IAAI,CAACZ,EAAE;MACXqD,QAAQ,EAAEjD,MAAM,CAACiD,QAAQ;MACzBrB,UAAU,EACN,CAACpB,IAAI,CAACS,KAAK,IACV,EAAE,EAAEtB,GAAG,CAACsB,KAAK,IAAIA,KAAK,CAACF,UAAU,CAAC,GAAG,CAAC,GAAGE,KAAK,CAACiC,KAAK,CAAC,CAAC,CAAC,GAAGjC,KAAK,CAAC;MACrEC,MAAM,EAAE,EAAE;MACVkB,QAAQ,EAAE,EAAE;MACZe,WAAW,EAAE,EAAE;MACfC,UAAU,EAAE,EAAE;MACdC,QAAQ,EAAE7C,IAAI,CAACuC,IAAI;MACnB5B,OAAO,EAAEnB,MAAM,CAACmB;KACjB;IAED,IAAInB,MAAM,CAACkB,MAAM,IAAI,IAAI,EAAE;MACzB8B,OAAO,CAACG,WAAW,GACfnD,MAAM,CAACkB,MAAM,CAACnB,MAAM,CAChB,CAACJ,GAAG,EAAE2D,KAAK,KAAI;QACb3D,GAAG,CAAC2D,KAAK,CAACzC,IAAI,CAAC,GAAG;UAChB0C,IAAI,EAAED,KAAK,CAACC,IAAI;UAChBC,eAAe,EAAEF,KAAK,CAACG,KAAK;UAC5BC,aAAa,EAAEJ,KAAK,CAACK;SACtB;QACD,OAAOhE,GAAG;MACZ,CAAC,EACD,EAAE,CAAC;;IAEb,IAAIK,MAAM,CAAC4D,KAAK,IAAI,IAAI,EAAE;MACxBZ,OAAO,CAACI,UAAU,GACdpD,MAAM,CAAC4D,KAAK,CAAC7D,MAAM,CAA8B,CAACJ,GAAG,EAAE2D,KAAK,KAAI;QAC9D,MAAMC,IAAI,GAAGD,KAAK,CAACC,IAAI;QACvB,IAAIM,KAAK,GAAGvD,SAAS;QACrB,QAAQgD,KAAK,CAACC,IAAI;UAChB,KAAK,QAAQ;YACXM,KAAK,GAAGC,cAAc,CAClBtD,IAAI,CAACuC,IAAI,EAAEO,KAAK,CAACS,MAAM,EAAET,KAAK,CAACU,YAAsB,CAAC;YAE1D,IAAIH,KAAK,KAAKvD,SAAS,IAAI,CAAC,CAACgD,KAAK,CAACW,gBAAgB,EAAE;cACnDJ,KAAK,GAAGC,cAAc,CAClBtD,IAAI,CAACuC,IAAI,EAAEO,KAAK,CAACW,gBAAgB,EACjCX,KAAK,CAACU,YAAsB,CAAC;;YAEnC;UACF,KAAK,UAAU;YACbH,KAAK,GAAGK,mBAAmB,CACvB1D,IAAI,CAACuC,IAAI,EAAEO,KAAK,CAACS,MAAM,EAAET,KAAK,CAACU,YAAwB,CAAC;YAE5D,IAAIH,KAAK,KAAKvD,SAAS,IAAI,CAAC,CAACgD,KAAK,CAACW,gBAAgB,EAAE;cACnDJ,KAAK,GAAGK,mBAAmB,CACvB1D,IAAI,CAACuC,IAAI,EAAEO,KAAK,CAACW,gBAAgB,EACjCX,KAAK,CAACU,YAAwB,CAAC;;YAErC;UACF,KAAK,QAAQ;YACXH,KAAK,GAAGM,cAAc,CAClB3D,IAAI,CAACuC,IAAI,EAAEO,KAAK,CAACS,MAAM,EACtBT,KAAK,CAACU,YAAY,IAAI,CAAY,CAAC;YACxC,IAAIH,KAAK,KAAKvD,SAAS,IAAI,CAAC,CAACgD,KAAK,CAACW,gBAAgB,EAAE;cACnDJ,KAAK,GAAGM,cAAc,CAClB3D,IAAI,CAACuC,IAAI,EAAEO,KAAK,CAACW,gBAAgB,EACjCX,KAAK,CAACU,YAAsB,CAAC;;YAEnC;UACF,KAAK,UAAU;YACbH,KAAK,GAAGO,oBAAoB,CACxB5D,IAAI,CAACuC,IAAI,EAAEO,KAAK,CAACS,MAAM,EAAET,KAAK,CAACU,YAAwB,CAAC;YAC5D,IAAIH,KAAK,KAAKvD,SAAS,IAAI,CAAC,CAACgD,KAAK,CAACW,gBAAgB,EAAE;cACnDJ,KAAK,GAAGO,oBAAoB,CACxB5D,IAAI,CAACuC,IAAI,EAAEO,KAAK,CAACW,gBAAgB,EACjCX,KAAK,CAACU,YAAwB,CAAC;;YAErC;UACF,KAAK,MAAM;YACTH,KAAK,GAAGQ,YAAY,CAChB7D,IAAI,CAACuC,IAAI,EAAEO,KAAK,CAACS,MAAM,EAAET,KAAK,CAACU,YAAuB,CAAC;YAC3D,IAAIH,KAAK,KAAKvD,SAAS,IAAI,CAAC,CAACgD,KAAK,CAACW,gBAAgB,EAAE;cACnDJ,KAAK,GAAGQ,YAAY,CAChB7D,IAAI,CAACuC,IAAI,EAAEO,KAAK,CAACW,gBAAgB,EACjCX,KAAK,CAACU,YAAuB,CAAC;;YAEpC;UACF,KAAK,QAAQ;YACXH,KAAK,GAAGS,iBAAiB,CACrB9D,IAAI,CAACuC,IAAI,EAAEO,KAAK,CAACS,MAAM,EAAET,KAAK,CAACU,YAAyB,CAAC;YAC7D,IAAIH,KAAK,KAAKvD,SAAS,IAAI,CAAC,CAACgD,KAAK,CAACW,gBAAgB,EAAE;cACnDJ,KAAK,GAAGS,iBAAiB,CACrB9D,IAAI,CAACuC,IAAI,EAAEO,KAAK,CAACW,gBAAgB,EACjCX,KAAK,CAACU,YAAyB,CAAC;;YAEtC;UACF,KAAK,OAAO;YACVH,KAAK,GAAGU,mBAAmB,CACvB/D,IAAI,CAACuC,IAAI,EAAEO,KAAK,CAACS,MAAM,EAAET,KAAK,CAACU,YAAwB,CAAC;YAC5D,IAAIH,KAAK,KAAKvD,SAAS,IAAI,CAAC,CAACgD,KAAK,CAACW,gBAAgB,EAAE;cACnDJ,KAAK,GAAGU,mBAAmB,CACvB/D,IAAI,CAACuC,IAAI,EAAEO,KAAK,CAACW,gBAAgB,EACjCX,KAAK,CAACU,YAAwB,CAAC;;YAErC;UACF,KAAK,SAAS;YACZH,KAAK,GAAGW,wBAAwB,CAC5BhE,IAAI,CAACuC,IAAI,EAAEO,KAAK,CAACS,MAAM,EAAET,KAAK,CAACU,YAA0B,CAAC;YAC9D,IAAIH,KAAK,KAAKvD,SAAS,IAAI,CAAC,CAACgD,KAAK,CAACW,gBAAgB,EAAE;cACnDJ,KAAK,GAAGW,wBAAwB,CAC5BhE,IAAI,CAACuC,IAAI,EAAEO,KAAK,CAACW,gBAAgB,EACjCX,KAAK,CAACU,YAA0B,CAAC;;YAEvC;UACF,KAAK,OAAO;YACVH,KAAK,GAAGY,aAAa,CACjBjE,IAAI,CAACuC,IAAI,EAAEO,KAAK,CAACS,MAAM,EAAET,KAAK,CAACU,YAAwB,CAAC;YAC5D,IAAIH,KAAK,KAAKvD,SAAS,IAAI,CAAC,CAACgD,KAAK,CAACW,gBAAgB,EAAE;cACnDJ,KAAK,GAAGY,aAAa,CACjBjE,IAAI,CAACuC,IAAI,EAAEO,KAAK,CAACW,gBAAgB,EACjCX,KAAK,CAACU,YAAwB,CAAC;;YAErC;UACF,KAAK,SAAS;YACZH,KAAK,GAAGa,kBAAkB,CACtBlE,IAAI,CAACuC,IAAI,EAAEO,KAAK,CAACS,MAAM,EAAET,KAAK,CAACU,YAA0B,CAAC;YAC9D,IAAIH,KAAK,KAAKvD,SAAS,IAAI,CAAC,CAACgD,KAAK,CAACW,gBAAgB,EAAE;cACnDJ,KAAK,GAAGa,kBAAkB,CACtBlE,IAAI,CAACuC,IAAI,EAAEO,KAAK,CAACW,gBAAgB,EACjCX,KAAK,CAACU,YAA0B,CAAC;;YAEvC;UACF,KAAK,MAAM;YACTH,KAAK,GAAGc,YAAY,CAChBnE,IAAI,CAACuC,IAAI,EAAEO,KAAK,CAACS,MAAM,EAAET,KAAK,CAACU,YAAsB,CAAC;YAC1D,IAAIH,KAAK,KAAKvD,SAAS,IAAI,CAAC,CAACgD,KAAK,CAACW,gBAAgB,EAAE;cACnDJ,KAAK,GAAGc,YAAY,CAChBnE,IAAI,CAACuC,IAAI,EAAEO,KAAK,CAACW,gBAAgB,EACjCX,KAAK,CAACU,YAAsB,CAAC;;YAEnC;UACF,KAAK,QAAQ;UACb,KAAK,SAAS;YACZ;UACF;YACE,MAAM,IAAIY,KAAK,4BAAAlF,MAAA,CACgB4D,KAAK,CAACC,IAAI,eAAA7D,MAAA,CAAYc,IAAI,CAACZ,EAAE,CAAE,CAAC;;QAEnED,GAAG,CAAC2D,KAAK,CAACzC,IAAI,CAAC,GAAG;UAACgD,KAAK;UAAEN;QAAI,CAAC;QAC/B,OAAO5D,GAAG;MACZ,CAAC,EAAE,EAAE,CAAC;;IAEZ,OAAOqD,OAAO;EAChB;EAEA;EACQN,WAAWA,CAACmC,WAAoC;IACtD,MAAMtE,OAAO,GAAGsE,WAAW,CAACC,OAAO;IACnC,MAAMrE,YAAY,GAAW,EAAE;IAC/B,MAAMC,OAAO,GAAW,EAAE;IAC1B,IAAIE,KAAK,GAA0B,EAAE;IACrC,IAAIL,OAAO,IAAI,IAAI,EAAE;MACnBK,KAAK,GAAGL,OAAO,CAACR,MAAM,CAAwB,CAACJ,GAAG,EAAEa,IAAI,KAAI;QAC1Db,GAAG,CAACa,IAAI,CAACK,IAAI,CAAC,GAAG,IAAI,CAACC,OAAO,CAACN,IAAI,CAAC;QACnC,IAAIA,IAAI,CAACZ,EAAE,KAAK,OAAO,EAAE;UACvBc,OAAO,CAACM,IAAI,CAACrB,GAAG,CAACa,IAAI,CAACK,IAAI,CAAC,CAAC;;QAE9B,OAAOlB,GAAG;MACZ,CAAC,EAAE,EAAE,CAAC;;IAER,MAAMuB,MAAM,GAAW,EAAE;IACzB,MAAMC,OAAO,GAAW,EAAE;IAE1B0D,WAAW,CAAC1E,SAAS,CAAC4E,QAAQ,CAACrD,OAAO,CAACsD,GAAG,IAAG;MAC3C,MAAM,CAAClD,QAAQ,CAAG,GAAG9D,mBAAmB,CAACgH,GAAG,CAACnE,IAAI,CAAC;MAClD,MAAML,IAAI,GAAS;QACjBK,IAAI,EAAEiB,QAAQ;QACdlC,EAAE,EAAE,aAAa;QACjBsB,MAAM,EAAE,EAAE;QACVU,UAAU,EAAE,EAAE;QACdqB,QAAQ,EAAE,OAAO;QACjBE,WAAW,EAAE,EAAE;QACfC,UAAU,EAAE;UAAC6B,KAAK,EAAE;YAACpB,KAAK,EAAEqB,eAAe,CAACF,GAAG,CAACzB,IAAI,CAAC;YAAEA,IAAI,EAAE;UAAO;QAAC,CAAC;QACtEnB,QAAQ,EAAE;OACX;MACD5B,IAAI,CAAC6B,YAAY,GAAG2C,GAAG,CAACnE,IAAI;MAC5BK,MAAM,CAACF,IAAI,CAACR,IAAI,CAAC;MACjBI,KAAK,CAACkB,QAAQ,CAAC,GAAGtB,IAAI;IACxB,CAAC,CAAC;IAEF,MAAMe,QAAQ,GAAGC,MAAM,CAACC,IAAI,CAACb,KAAK,CAAC;IACnCW,QAAQ,CAACG,OAAO,CAACC,GAAG,IAAG;MACrB,MAAMnB,IAAI,GAAGI,KAAK,CAACe,GAAG,CAAC;MACvBnB,IAAI,CAACoB,UAAU,CAACF,OAAO,CAAC,CAACb,IAAI,EAAEgB,KAAK,KAAI;QACtC,MAAM,CAACC,QAAQ,GAAIC,UAAU,CAAC,GAAG/D,mBAAmB,CAAC6C,IAAI,CAAC;QAC1D,MAAMmB,SAAS,GAAGpB,KAAK,CAACkB,QAAQ,CAAC;QACjC,IAAIE,SAAS,CAACb,OAAO,IAAI,IAAI,EAAE;UAC7B,MAAMc,WAAW,GAAGD,SAAS,CAACb,OAAO,CAACe,OAAO,CAACH,UAAU,CAAC;UACzD,IAAIE,WAAW,KAAK,CAAC,CAAC,EAAE;YACtB,MAAME,SAAS,MAAAzC,MAAA,CAAMoC,QAAQ,OAAApC,MAAA,CAAIuC,WAAW,CAAE;YAC9C;YACAzB,IAAI,CAACoB,UAAU,CAACC,KAAK,CAAC,GAAGM,SAAS;;;QAGtC3B,IAAI,CAACU,MAAM,CAACF,IAAI,CAACgB,SAAS,CAAC;QAC3BA,SAAS,CAACI,QAAQ,CAACpB,IAAI,CAACR,IAAI,CAAC;MAC/B,CAAC,CAAC;IACJ,CAAC,CAAC;IAEF,MAAM2E,aAAa,GAAGN,WAAW,CAACO,GAAG;IAErCP,WAAW,CAAC1E,SAAS,CAACkF,SAAS,CAAC3D,OAAO,CAAC4D,MAAM,IAAG;MAC/C,MAAM,CAACxD,QAAQ,EAAED,KAAK,CAAC,GAAG7D,mBAAmB,CAACmH,aAAa,CAACG,MAAM,CAACzE,IAAI,CAAC,CAAC;MACzE,MAAML,IAAI,GAAGI,KAAK,CAACkB,QAAQ,CAAC;MAC5B,IAAItB,IAAI,IAAI,IAAI,EAAE;QAChBA,IAAI,CAAC+E,aAAa,GAAG1D,KAAK;QAC1BV,OAAO,CAACH,IAAI,CAACR,IAAI,CAAC;;IAEtB,CAAC,CAAC;IAEF,MAAML,SAAS,GAAG,IAAI,CAACqF,kBAAkB,CAACX,WAAW,CAAC;IACtD,OAAO;MAACjE,KAAK;MAAEM,MAAM;MAAEC,OAAO;MAAET,OAAO;MAAED,YAAY;MAAEN;IAAS,CAAC;EACnE;EAEQqF,kBAAkBA,CAACX,WAAoC;IAE7D,OAAO;MACLY,UAAU,EAAEZ,WAAW,CAAC1E,SAAS,CAACU,IAAI;MACtCK,MAAM,EAAE2D,WAAW,CAAC1E,SAAS,CAAC4E,QAAQ,CAAChF,MAAM,CACzC,CAACJ,GAAG,EAAEqF,GAAG,KAAI;QACXrF,GAAG,CAACqF,GAAG,CAACnE,IAAI,CAAC,GAAG,IAAI,CAAC6E,kBAAkB,CAACV,GAAG,CAAC;QAC5C,OAAOrF,GAAG;MACZ,CAAC,EACD,EAA6C,CAAC;MAClDwB,OAAO,EAAE0D,WAAW,CAAC1E,SAAS,CAACkF,SAAS,CAACtF,MAAM,CAC3C,CAACJ,GAAG,EAAEqF,GAAG,KAAI;QACXrF,GAAG,CAACqF,GAAG,CAACnE,IAAI,CAAC,GAAG,IAAI,CAAC6E,kBAAkB,CAACV,GAAG,EAAEH,WAAW,CAACO,GAAG,CAAC;QAC7D,OAAOzF,GAAG;MACZ,CAAC,EACD,EAA6C;KAClD;EACH;EAEQ+F,kBAAkBA,CACtBV,GAA6B,EAC7BW,OAAiC;IACnC,IAAI9E,IAAI,GAAGmE,GAAG,CAACnE,IAAI;IACnB,IAAI8E,OAAO,IAAI,IAAI,EAAE;MACnB9E,IAAI,GAAG8E,OAAO,CAAC9E,IAAI,CAAC;;IAEtB,OAAO;MAACA,IAAI;MAAEoE,KAAK,EAAED,GAAG,CAACzB;IAAI,CAAC;EAChC;;AAGF,OAAM,SAAUqC,YAAYA,CAACC,IAAY;EACvC,MAAMC,MAAM,GAAGjI,GAAG,EAAE,CAACiI,MAAM;EAC3B,IAAI,OAAOA,MAAM,CAACC,IAAI,KAAK,WAAW,EAAE;IACtC,OAAOD,MAAM,CAACC,IAAI,CAACF,IAAI,CAAC;GACzB,MAAM,IAAI,OAAOG,MAAM,KAAK,WAAW,EAAE;IACxC,OAAO,IAAIA,MAAM,CAACH,IAAI,EAAE,QAAQ,CAAC,CAACI,QAAQ,EAAE;GAC7C,MAAM;IACL,MAAM,IAAIrB,KAAK,CACX,+CAA+C,GAC/C,qCAAqC,CAAC;;AAE9C;AAEA,OAAM,SAAUsB,gBAAgBA,CAACC,CAAY,EAAEC,QAAiB;EAC9D,MAAMvC,KAAK,GACPwC,KAAK,CAACC,OAAO,CAACH,CAAC,CAAC,GAAGI,MAAM,CAACC,YAAY,CAACC,KAAK,CAAC,IAAI,EAAEN,CAAC,CAAC,GAAGP,YAAY,CAACO,CAAC,CAAC;EAC3E,OAAOC,QAAQ,GAAGvC,KAAK,GAAGA,KAAK,CAAC6C,WAAW,EAAE;AAC/C;AAEA,OAAM,SAAU5C,cAAcA,CAC1BF,KAA6C,EAAE/C,IAAY,EAAE8F,GAAW,EACxD;EAAA,IAAhBP,QAAQ,GAAAhG,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,KAAK;EAClB,MAAMkD,KAAK,GAAGM,KAAK,CAAC/C,IAAI,CAAC;EACzB,IAAIyC,KAAK,IAAI,IAAI,EAAE;IACjB,OAAO4C,gBAAgB,CAAC5C,KAAK,CAAC6C,CAAC,EAAEC,QAAQ,CAAC;;EAE5C,OAAOO,GAAG;AACZ;AAEA,OAAM,SAAUtC,YAAYA,CACxBT,KAA6C,EAAE/C,IAAY,EAC3D8F,GAAY;EACd,MAAMrD,KAAK,GAAGM,KAAK,CAAC/C,IAAI,CAAC;EACzB,OAAOyC,KAAK,GAAGA,KAAK,CAACsD,CAAC,GAAGD,GAAG;AAC9B;AAEA,OAAM,SAAUxC,cAAcA,CAC1BP,KAA6C,EAAE/C,IAAY,EAC3D8F,GAAW;EACb,MAAMrD,KAAK,GAAGM,KAAK,CAAC/C,IAAI,CAAC,IAAI,EAAE;EAC/B,MAAMgD,KAAK,GACPP,KAAK,CAAC,GAAG,CAAC,IAAI,IAAI,GAAGA,KAAK,CAAC,GAAG,CAAC,GAAIA,KAAK,CAAC,GAAG,CAAC,IAAI,IAAI,GAAGA,KAAK,CAAC,GAAG,CAAC,GAAGqD,GAAI;EAC7E,OAAQ,OAAO9C,KAAK,KAAK,QAAQ,GAAIA,KAAK,GAAGgD,QAAQ,CAAChD,KAAK,EAAE,EAAE,CAAC;AAClE;AAEA,OAAM,SAAUqB,eAAeA,CAACrB,KAAiC;EAC/D,IAAI,OAAQA,KAAM,KAAK,QAAQ,EAAE;IAC/B;IACAA,KAAK,GAAG/F,UAAU,CAACgJ,QAAQ,CAACjD,KAAY,CAAC;;EAE3C,QAAQA,KAAK;IACX,KAAK/F,UAAU,CAACgJ,QAAQ,CAACC,QAAQ;IACjC,KAAKjJ,UAAU,CAACgJ,QAAQ,CAACE,OAAO;MAC9B,OAAO,SAAS;IAClB,KAAKlJ,UAAU,CAACgJ,QAAQ,CAACG,QAAQ;IACjC,KAAKnJ,UAAU,CAACgJ,QAAQ,CAACI,QAAQ;IACjC,KAAKpJ,UAAU,CAACgJ,QAAQ,CAACK,OAAO;IAChC,KAAKrJ,UAAU,CAACgJ,QAAQ,CAACM,QAAQ;MAC/B,OAAO,OAAO;IAChB,KAAKtJ,UAAU,CAACgJ,QAAQ,CAACO,OAAO;MAC9B,OAAO,MAAM;IACf,KAAKvJ,UAAU,CAACgJ,QAAQ,CAACQ,SAAS;MAChC,OAAO,SAAS;IAClB,KAAKxJ,UAAU,CAACgJ,QAAQ,CAACS,SAAS;MAChC,OAAO,QAAQ;IACjB,KAAKzJ,UAAU,CAACgJ,QAAQ,CAACU,YAAY;IACrC,KAAK1J,UAAU,CAACgJ,QAAQ,CAACW,aAAa;MACpC,OAAO,WAAW;IACpB;MACE;MACA;MACA,OAAO,IAAI;;AAEjB;AAEA,OAAM,SAAU9C,YAAYA,CACxBf,KAA6C,EAAE/C,IAAY,EAC3D8F,GAAW;EACb,MAAMrD,KAAK,GAAGM,KAAK,CAAC/C,IAAI,CAAC;EACzB,IAAIyC,KAAK,IAAIA,KAAK,CAACb,IAAI,EAAE;IACvB,OAAOa,KAAK,CAACb,IAAI,CAAC5B,IAAI;;EAExB,OAAO8F,GAAG;AACZ;AAEA,OAAM,SAAUlC,aAAaA,CACzBb,KAA6C,EAAE/C,IAAY,EAC3D8F,GAAa;EACf,MAAMrD,KAAK,GAAGM,KAAK,CAAC/C,IAAI,CAAC;EACzB,IAAIyC,KAAK,IAAIA,KAAK,CAACC,IAAI,EAAE;IACvB,OAAO2B,eAAe,CAAC5B,KAAK,CAACC,IAAI,CAAC;;EAEpC,OAAOoD,GAAG;AACZ;AAEA,OAAM,SAAUjC,kBAAkBA,CAC9Bd,KAA6C,EAAE/C,IAAY,EAC3D8F,GAAe;EACjB,MAAMrD,KAAK,GAAGM,KAAK,CAAC/C,IAAI,CAAC;EACzB,IAAIyC,KAAK,IAAIA,KAAK,CAACoE,IAAI,IAAIpE,KAAK,CAACoE,IAAI,CAACnE,IAAI,EAAE;IAC1C,OAAOD,KAAK,CAACoE,IAAI,CAACnE,IAAI,CAAC5D,GAAG,CAACgI,CAAC,IAAIzC,eAAe,CAACyC,CAAC,CAAC,CAAC;;EAErD,OAAOhB,GAAG;AACZ;AAEA,OAAM,SAAUiB,qBAAqBA,CAACC,KAA8B;EAElE,IAAIA,KAAK,CAACC,WAAW,EAAE;IACrB,OAAOxH,SAAS;;EAElB,IAAIuH,KAAK,CAACE,GAAG,IAAI,IAAI,EAAE;IACrB,OAAOF,KAAK,CAACE,GAAG,CAACpI,GAAG,CAChBoI,GAAG,IACE,OAAOA,GAAG,CAACC,IAAI,KAAK,QAAQ,GAAID,GAAG,CAACC,IAAI,GAAGnB,QAAQ,CAACkB,GAAG,CAACC,IAAI,EAAE,EAAE,CAAC,CAAC;;EAE7E,OAAO,EAAE;AACX;AAEA,OAAM,SAAUzD,mBAAmBA,CAC/BX,KAA6C,EAAE/C,IAAY,EAC3D8F,GAAc;EAChB,MAAMrD,KAAK,GAAGM,KAAK,CAAC/C,IAAI,CAAC;EACzB,IAAIyC,KAAK,IAAIA,KAAK,CAACuE,KAAK,EAAE;IACxB,OAAOD,qBAAqB,CAACtE,KAAK,CAACuE,KAAK,CAAC;;EAE3C,OAAOlB,GAAG;AACZ;AAEA,OAAM,SAAUvC,oBAAoBA,CAChCR,KAA6C,EAAE/C,IAAY,EAC3D8F,GAAa;EACf,MAAMrD,KAAK,GAAGM,KAAK,CAAC/C,IAAI,CAAC;EACzB,IAAIyC,KAAK,EAAE;IACT,OAAO,CAAC,CAACA,KAAK,CAACoE,IAAI,CAACO,CAAC,IAAI3E,KAAK,CAACoE,IAAI,CAACO,CAAC,CAAC5H,MAAM,GAAGiD,KAAK,CAACoE,IAAI,CAACO,CAAC,GACZ3E,KAAK,CAACoE,IAAI,CAACQ,CAAC,KACnD,EAAE,EACLvI,GAAG,CAACgI,CAAC,IAAK,OAAOA,CAAC,KAAK,QAAQ,GAAIA,CAAC,GAAGd,QAAQ,CAACc,CAAC,EAAE,EAAE,CAAC,CAAC;;EAE9D,OAAOhB,GAAG;AACZ;AAEA,OAAM,SAAUzC,mBAAmBA,CAC/BN,KAA6C,EAAE/C,IAAY,EAAE8F,GAAa,EAC1D;EAAA,IAAhBP,QAAQ,GAAAhG,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,KAAK;EAClB,MAAMkD,KAAK,GAAGM,KAAK,CAAC/C,IAAI,CAAC;EACzB,IAAIyC,KAAK,IAAIA,KAAK,CAACoE,IAAI,IAAIpE,KAAK,CAACoE,IAAI,CAACvB,CAAC,EAAE;IACvC,OAAO7C,KAAK,CAACoE,IAAI,CAACvB,CAAC,CAACxG,GAAG,CAAEgI,CAAC,IAAI;MAC5B,OAAOzB,gBAAgB,CAACyB,CAAC,EAAEvB,QAAQ,CAAC;IACtC,CAAC,CAAC;;EAEJ,OAAOO,GAAG;AACZ;AAEA,OAAM,SAAUnC,wBAAwBA,CACpCZ,KAA6C,EAAE/C,IAAY,EAC3D8F,GAAe;EACjB,MAAMrD,KAAK,GAAGM,KAAK,CAAC/C,IAAI,CAAC;EACzB,IAAIyC,KAAK,IAAIA,KAAK,CAACoE,IAAI,IAAIpE,KAAK,CAACoE,IAAI,CAACG,KAAK,EAAE;IAC3C,OAAOvE,KAAK,CAACoE,IAAI,CAACG,KAAK,CAAClI,GAAG,CAAEgI,CAAC,IAAI;MAChC,OAAOC,qBAAqB,CAACD,CAAC,CAAC;IACjC,CAAC,CAAC;;EAEJ,OAAOhB,GAAG;AACZ;AAEA,OAAM,SAAUrC,iBAAiBA,CAC7BV,KAA6C,EAAE/C,IAAY,EAC3D8F,GAAc;EAChB,MAAMrD,KAAK,GAAGM,KAAK,CAAC/C,IAAI,CAAC;EACzB,IAAIyC,KAAK,IAAIA,KAAK,CAACoE,IAAI,IAAIpE,KAAK,CAACoE,IAAI,CAACd,CAAC,EAAE;IACvC,OAAOtD,KAAK,CAACoE,IAAI,CAACd,CAAC;;EAErB,OAAOD,GAAG;AACZ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}