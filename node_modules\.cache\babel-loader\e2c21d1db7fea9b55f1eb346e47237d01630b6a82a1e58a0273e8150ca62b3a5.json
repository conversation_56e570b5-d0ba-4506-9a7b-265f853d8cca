{"ast": null, "code": "import _objectSpread from\"C:/tmsft/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";import React,{useState,useCallback}from'react';import{bankAccountService}from'../services/bankAccountService';import{balanceManagementService}from'../services/balanceManagementService';import'./BankAccountManager.css';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";export const BankAccountManager=_ref=>{let{onAccountsUpdated}=_ref;const[accounts,setAccounts]=useState(bankAccountService.getAllAccounts());const[isAddingAccount,setIsAddingAccount]=useState(false);const[editingAccount,setEditingAccount]=useState(null);const[adjustingBalanceAccount,setAdjustingBalanceAccount]=useState(null);const[formData,setFormData]=useState({name:'',accountNumber:'',bankName:'',currency:'USD',currentBalance:'0.00'});const[balanceFormData,setBalanceFormData]=useState({newBalance:'',effectiveDate:new Date().toISOString().split('T')[0],reason:''});const[errors,setErrors]=useState({});const[balanceErrors,setBalanceErrors]=useState({});const[showBalanceHistory,setShowBalanceHistory]=useState({});const refreshAccounts=useCallback(()=>{setAccounts(bankAccountService.getAllAccounts());if(onAccountsUpdated){onAccountsUpdated();}},[onAccountsUpdated]);const resetForm=useCallback(()=>{setFormData({name:'',accountNumber:'',bankName:'',currency:'USD',currentBalance:'0.00'});setErrors({});setIsAddingAccount(false);setEditingAccount(null);},[]);const validateForm=useCallback(data=>{const newErrors={};if(!data.name.trim()){newErrors.name='Account name is required';}if(!data.accountNumber.trim()){newErrors.accountNumber='Account number is required';}else if(accounts.some(acc=>acc.accountNumber===data.accountNumber.trim()&&(!editingAccount||acc.id!==editingAccount.id))){newErrors.accountNumber='Account number already exists';}if(!data.bankName.trim()){newErrors.bankName='Bank name is required';}if(!data.currency.trim()){newErrors.currency='Currency is required';}const balance=parseFloat(data.currentBalance);if(isNaN(balance)){newErrors.currentBalance='Current balance must be a valid number';}return newErrors;},[accounts,editingAccount]);const handleInputChange=useCallback((field,value)=>{setFormData(prev=>_objectSpread(_objectSpread({},prev),{},{[field]:value}));// Clear error for this field when user starts typing\nif(errors[field]){setErrors(prev=>_objectSpread(_objectSpread({},prev),{},{[field]:''}));}},[errors]);const handleAddAccount=useCallback(()=>{setIsAddingAccount(true);setEditingAccount(null);resetForm();},[resetForm]);const handleEditAccount=useCallback(account=>{setEditingAccount(account);setIsAddingAccount(false);setFormData({name:account.name,accountNumber:account.accountNumber,bankName:account.bankName,currency:account.currency,currentBalance:account.currentBalance.toFixed(2)});setErrors({});},[]);const handleSubmit=useCallback(e=>{e.preventDefault();const newErrors=validateForm(formData);if(Object.keys(newErrors).length>0){setErrors(newErrors);return;}const accountData={name:formData.name.trim(),accountNumber:formData.accountNumber.trim(),bankName:formData.bankName.trim(),currency:formData.currency.trim(),currentBalance:parseFloat(formData.currentBalance)};try{if(editingAccount){bankAccountService.updateAccount(editingAccount.id,accountData);}else{bankAccountService.addAccount(accountData);}refreshAccounts();resetForm();}catch(error){console.error('Error saving account:',error);setErrors({submit:'Failed to save account. Please try again.'});}},[formData,validateForm,editingAccount,refreshAccounts,resetForm]);const handleDeleteAccount=useCallback(accountId=>{if(window.confirm('Are you sure you want to delete this account? This action cannot be undone.')){bankAccountService.deleteAccount(accountId);refreshAccounts();}},[refreshAccounts]);// Balance adjustment methods\nconst handleAdjustBalance=useCallback(account=>{setAdjustingBalanceAccount(account);setBalanceFormData({newBalance:account.currentBalance.toFixed(2),effectiveDate:new Date().toISOString().split('T')[0],reason:''});setBalanceErrors({});},[]);const validateBalanceForm=useCallback(data=>{const newErrors={};const balance=parseFloat(data.newBalance);if(isNaN(balance)){newErrors.newBalance='Balance must be a valid number';}if(!data.effectiveDate){newErrors.effectiveDate='Effective date is required';}if(!data.reason.trim()){newErrors.reason='Reason for adjustment is required';}return newErrors;},[]);const handleBalanceSubmit=useCallback(e=>{e.preventDefault();if(!adjustingBalanceAccount)return;const newErrors=validateBalanceForm(balanceFormData);if(Object.keys(newErrors).length>0){setBalanceErrors(newErrors);return;}try{const newBalance=parseFloat(balanceFormData.newBalance);const updatedAccount=balanceManagementService.updateAccountBalance(adjustingBalanceAccount,newBalance,balanceFormData.effectiveDate,balanceFormData.reason);// Update the account in the service\nbankAccountService.updateAccount(updatedAccount.id,{name:updatedAccount.name,accountNumber:updatedAccount.accountNumber,bankName:updatedAccount.bankName,currency:updatedAccount.currency,currentBalance:updatedAccount.currentBalance});refreshAccounts();setAdjustingBalanceAccount(null);setBalanceFormData({newBalance:'',effectiveDate:new Date().toISOString().split('T')[0],reason:''});}catch(error){console.error('Error adjusting balance:',error);setBalanceErrors({submit:'Failed to adjust balance. Please try again.'});}},[adjustingBalanceAccount,balanceFormData,validateBalanceForm,refreshAccounts]);const toggleBalanceHistory=useCallback(accountId=>{setShowBalanceHistory(prev=>_objectSpread(_objectSpread({},prev),{},{[accountId]:!prev[accountId]}));},[]);const formatCurrency=amount=>{return new Intl.NumberFormat('en-US',{style:'currency',currency:'USD'}).format(amount);};return/*#__PURE__*/_jsxs(\"div\",{className:\"bank-account-manager\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"manager-header\",children:[/*#__PURE__*/_jsx(\"h2\",{children:\"Bank Account Management\"}),/*#__PURE__*/_jsx(\"p\",{children:\"Add, edit, and manage your bank accounts\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"accounts-section\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"section-header\",children:[/*#__PURE__*/_jsxs(\"h3\",{children:[\"Your Bank Accounts (\",accounts.length,\")\"]}),/*#__PURE__*/_jsx(\"button\",{type:\"button\",onClick:handleAddAccount,className:\"btn btn-primary\",children:\"Add New Account\"})]}),accounts.length===0?/*#__PURE__*/_jsxs(\"div\",{className:\"empty-state\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"empty-icon\",children:/*#__PURE__*/_jsxs(\"svg\",{width:\"48\",height:\"48\",viewBox:\"0 0 24 24\",fill:\"none\",stroke:\"currentColor\",strokeWidth:\"1\",children:[/*#__PURE__*/_jsx(\"rect\",{x:\"2\",y:\"3\",width:\"20\",height:\"14\",rx:\"2\",ry:\"2\"}),/*#__PURE__*/_jsx(\"line\",{x1:\"2\",y1:\"12\",x2:\"22\",y2:\"12\"})]})}),/*#__PURE__*/_jsx(\"h3\",{children:\"No Bank Accounts\"}),/*#__PURE__*/_jsx(\"p\",{children:\"Get started by adding your first bank account\"})]}):/*#__PURE__*/_jsx(\"div\",{className:\"accounts-grid\",children:accounts.map(account=>/*#__PURE__*/_jsxs(\"div\",{className:\"account-card\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"account-header\",children:[/*#__PURE__*/_jsx(\"h4\",{children:account.name}),/*#__PURE__*/_jsxs(\"div\",{className:\"account-actions\",children:[/*#__PURE__*/_jsx(\"button\",{type:\"button\",onClick:()=>handleAdjustBalance(account),className:\"btn-icon\",title:\"Adjust balance\",children:/*#__PURE__*/_jsx(\"svg\",{width:\"16\",height:\"16\",viewBox:\"0 0 24 24\",fill:\"none\",stroke:\"currentColor\",strokeWidth:\"2\",children:/*#__PURE__*/_jsx(\"path\",{d:\"M12 2v20M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6\"})})}),/*#__PURE__*/_jsx(\"button\",{type:\"button\",onClick:()=>toggleBalanceHistory(account.id),className:\"btn-icon\",title:\"View balance history\",children:/*#__PURE__*/_jsx(\"svg\",{width:\"16\",height:\"16\",viewBox:\"0 0 24 24\",fill:\"none\",stroke:\"currentColor\",strokeWidth:\"2\",children:/*#__PURE__*/_jsx(\"path\",{d:\"M3 3v5h5M3 8l4-4 4 4 8-8\"})})}),/*#__PURE__*/_jsx(\"button\",{type:\"button\",onClick:()=>handleEditAccount(account),className:\"btn-icon\",title:\"Edit account\",children:/*#__PURE__*/_jsxs(\"svg\",{width:\"16\",height:\"16\",viewBox:\"0 0 24 24\",fill:\"none\",stroke:\"currentColor\",strokeWidth:\"2\",children:[/*#__PURE__*/_jsx(\"path\",{d:\"M12 20h9\"}),/*#__PURE__*/_jsx(\"path\",{d:\"M16.5 3.5a2.121 2.121 0 0 1 3 3L7 19l-4 1 1-4L16.5 3.5z\"})]})}),/*#__PURE__*/_jsx(\"button\",{type:\"button\",onClick:()=>handleDeleteAccount(account.id),className:\"btn-icon btn-danger\",title:\"Delete account\",children:/*#__PURE__*/_jsxs(\"svg\",{width:\"16\",height:\"16\",viewBox:\"0 0 24 24\",fill:\"none\",stroke:\"currentColor\",strokeWidth:\"2\",children:[/*#__PURE__*/_jsx(\"polyline\",{points:\"3,6 5,6 21,6\"}),/*#__PURE__*/_jsx(\"path\",{d:\"M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2\"})]})})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"account-details\",children:[/*#__PURE__*/_jsxs(\"p\",{children:[/*#__PURE__*/_jsx(\"strong\",{children:\"Bank:\"}),\" \",account.bankName]}),/*#__PURE__*/_jsxs(\"p\",{children:[/*#__PURE__*/_jsx(\"strong\",{children:\"Account Number:\"}),\" \",account.accountNumber]}),/*#__PURE__*/_jsxs(\"p\",{children:[/*#__PURE__*/_jsx(\"strong\",{children:\"Currency:\"}),\" \",account.currency]}),/*#__PURE__*/_jsxs(\"p\",{children:[/*#__PURE__*/_jsx(\"strong\",{children:\"Current Balance:\"}),\" \",/*#__PURE__*/_jsx(\"span\",{className:\"balance\",children:formatCurrency(account.currentBalance)})]})]})]},account.id))})]}),(isAddingAccount||editingAccount)&&/*#__PURE__*/_jsxs(\"div\",{className:\"account-form-section\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"form-header\",children:/*#__PURE__*/_jsx(\"h3\",{children:editingAccount?'Edit Account':'Add New Account'})}),/*#__PURE__*/_jsxs(\"form\",{onSubmit:handleSubmit,className:\"account-form\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"form-row\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"form-group\",children:[/*#__PURE__*/_jsx(\"label\",{htmlFor:\"account-name\",className:\"form-label\",children:\"Account Name *\"}),/*#__PURE__*/_jsx(\"input\",{id:\"account-name\",type:\"text\",value:formData.name,onChange:e=>handleInputChange('name',e.target.value),className:\"form-input \".concat(errors.name?'error':''),placeholder:\"e.g., Main Operating Account\"}),errors.name&&/*#__PURE__*/_jsx(\"span\",{className:\"form-error\",children:errors.name})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"form-group\",children:[/*#__PURE__*/_jsx(\"label\",{htmlFor:\"account-number\",className:\"form-label\",children:\"Account Number *\"}),/*#__PURE__*/_jsx(\"input\",{id:\"account-number\",type:\"text\",value:formData.accountNumber,onChange:e=>handleInputChange('accountNumber',e.target.value),className:\"form-input \".concat(errors.accountNumber?'error':''),placeholder:\"e.g., **********\"}),errors.accountNumber&&/*#__PURE__*/_jsx(\"span\",{className:\"form-error\",children:errors.accountNumber})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"form-row\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"form-group\",children:[/*#__PURE__*/_jsx(\"label\",{htmlFor:\"bank-name\",className:\"form-label\",children:\"Bank Name *\"}),/*#__PURE__*/_jsx(\"input\",{id:\"bank-name\",type:\"text\",value:formData.bankName,onChange:e=>handleInputChange('bankName',e.target.value),className:\"form-input \".concat(errors.bankName?'error':''),placeholder:\"e.g., First National Bank\"}),errors.bankName&&/*#__PURE__*/_jsx(\"span\",{className:\"form-error\",children:errors.bankName})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"form-group\",children:[/*#__PURE__*/_jsx(\"label\",{htmlFor:\"currency\",className:\"form-label\",children:\"Currency *\"}),/*#__PURE__*/_jsxs(\"select\",{id:\"currency\",value:formData.currency,onChange:e=>handleInputChange('currency',e.target.value),className:\"form-select \".concat(errors.currency?'error':''),children:[/*#__PURE__*/_jsx(\"option\",{value:\"USD\",children:\"USD - US Dollar\"}),/*#__PURE__*/_jsx(\"option\",{value:\"EUR\",children:\"EUR - Euro\"}),/*#__PURE__*/_jsx(\"option\",{value:\"GBP\",children:\"GBP - British Pound\"}),/*#__PURE__*/_jsx(\"option\",{value:\"CAD\",children:\"CAD - Canadian Dollar\"}),/*#__PURE__*/_jsx(\"option\",{value:\"AUD\",children:\"AUD - Australian Dollar\"})]}),errors.currency&&/*#__PURE__*/_jsx(\"span\",{className:\"form-error\",children:errors.currency})]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"form-row\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"form-group\",children:[/*#__PURE__*/_jsx(\"label\",{htmlFor:\"current-balance\",className:\"form-label\",children:\"Current Balance *\"}),/*#__PURE__*/_jsx(\"input\",{id:\"current-balance\",type:\"number\",step:\"0.01\",value:formData.currentBalance,onChange:e=>handleInputChange('currentBalance',e.target.value),className:\"form-input \".concat(errors.currentBalance?'error':''),placeholder:\"0.00\"}),errors.currentBalance&&/*#__PURE__*/_jsx(\"span\",{className:\"form-error\",children:errors.currentBalance})]})}),errors.submit&&/*#__PURE__*/_jsx(\"div\",{className:\"form-error-message\",children:errors.submit}),/*#__PURE__*/_jsxs(\"div\",{className:\"form-actions\",children:[/*#__PURE__*/_jsx(\"button\",{type:\"button\",onClick:resetForm,className:\"btn btn-secondary\",children:\"Cancel\"}),/*#__PURE__*/_jsx(\"button\",{type:\"submit\",className:\"btn btn-primary\",children:editingAccount?'Update Account':'Add Account'})]})]})]}),adjustingBalanceAccount&&/*#__PURE__*/_jsxs(\"div\",{className:\"balance-adjustment-section\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"form-header\",children:[/*#__PURE__*/_jsxs(\"h3\",{children:[\"Adjust Balance - \",adjustingBalanceAccount.name]}),/*#__PURE__*/_jsx(\"p\",{children:\"Update the account balance with an effective date and reason\"})]}),/*#__PURE__*/_jsxs(\"form\",{onSubmit:handleBalanceSubmit,className:\"balance-form\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"form-row\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"form-group\",children:[/*#__PURE__*/_jsx(\"label\",{htmlFor:\"current-balance-display\",className:\"form-label\",children:\"Current Balance\"}),/*#__PURE__*/_jsx(\"input\",{id:\"current-balance-display\",type:\"text\",value:formatCurrency(adjustingBalanceAccount.currentBalance),disabled:true,className:\"form-input disabled\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"form-group\",children:[/*#__PURE__*/_jsx(\"label\",{htmlFor:\"new-balance\",className:\"form-label\",children:\"New Balance *\"}),/*#__PURE__*/_jsx(\"input\",{id:\"new-balance\",type:\"number\",step:\"0.01\",value:balanceFormData.newBalance,onChange:e=>setBalanceFormData(prev=>_objectSpread(_objectSpread({},prev),{},{newBalance:e.target.value})),className:\"form-input \".concat(balanceErrors.newBalance?'error':''),placeholder:\"0.00\"}),balanceErrors.newBalance&&/*#__PURE__*/_jsx(\"span\",{className:\"form-error\",children:balanceErrors.newBalance})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"form-row\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"form-group\",children:[/*#__PURE__*/_jsx(\"label\",{htmlFor:\"effective-date\",className:\"form-label\",children:\"Effective Date *\"}),/*#__PURE__*/_jsx(\"input\",{id:\"effective-date\",type:\"date\",value:balanceFormData.effectiveDate,onChange:e=>setBalanceFormData(prev=>_objectSpread(_objectSpread({},prev),{},{effectiveDate:e.target.value})),className:\"form-input \".concat(balanceErrors.effectiveDate?'error':'')}),balanceErrors.effectiveDate&&/*#__PURE__*/_jsx(\"span\",{className:\"form-error\",children:balanceErrors.effectiveDate})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"form-group\",children:[/*#__PURE__*/_jsx(\"label\",{htmlFor:\"adjustment-reason\",className:\"form-label\",children:\"Reason for Adjustment *\"}),/*#__PURE__*/_jsxs(\"select\",{id:\"adjustment-reason\",value:balanceFormData.reason,onChange:e=>setBalanceFormData(prev=>_objectSpread(_objectSpread({},prev),{},{reason:e.target.value})),className:\"form-select \".concat(balanceErrors.reason?'error':''),children:[/*#__PURE__*/_jsx(\"option\",{value:\"\",children:\"Select a reason...\"}),/*#__PURE__*/_jsx(\"option\",{value:\"Bank reconciliation\",children:\"Bank reconciliation\"}),/*#__PURE__*/_jsx(\"option\",{value:\"Manual correction\",children:\"Manual correction\"}),/*#__PURE__*/_jsx(\"option\",{value:\"Interest adjustment\",children:\"Interest adjustment\"}),/*#__PURE__*/_jsx(\"option\",{value:\"Fee adjustment\",children:\"Fee adjustment\"}),/*#__PURE__*/_jsx(\"option\",{value:\"Opening balance setup\",children:\"Opening balance setup\"}),/*#__PURE__*/_jsx(\"option\",{value:\"Other\",children:\"Other\"})]}),balanceErrors.reason&&/*#__PURE__*/_jsx(\"span\",{className:\"form-error\",children:balanceErrors.reason})]})]}),balanceFormData.reason==='Other'&&/*#__PURE__*/_jsx(\"div\",{className:\"form-row\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"form-group\",children:[/*#__PURE__*/_jsx(\"label\",{htmlFor:\"custom-reason\",className:\"form-label\",children:\"Custom Reason *\"}),/*#__PURE__*/_jsx(\"input\",{id:\"custom-reason\",type:\"text\",placeholder:\"Enter custom reason...\",className:\"form-input\",onChange:e=>setBalanceFormData(prev=>_objectSpread(_objectSpread({},prev),{},{reason:e.target.value}))})]})}),/*#__PURE__*/_jsxs(\"div\",{className:\"adjustment-preview\",children:[/*#__PURE__*/_jsx(\"h4\",{children:\"Adjustment Preview\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"preview-details\",children:[/*#__PURE__*/_jsxs(\"p\",{children:[/*#__PURE__*/_jsx(\"strong\",{children:\"Current Balance:\"}),\" \",formatCurrency(adjustingBalanceAccount.currentBalance)]}),/*#__PURE__*/_jsxs(\"p\",{children:[/*#__PURE__*/_jsx(\"strong\",{children:\"New Balance:\"}),\" \",balanceFormData.newBalance?formatCurrency(parseFloat(balanceFormData.newBalance)):'$0.00']}),/*#__PURE__*/_jsxs(\"p\",{children:[/*#__PURE__*/_jsx(\"strong\",{children:\"Adjustment Amount:\"}),/*#__PURE__*/_jsx(\"span\",{className:\"adjustment-amount \".concat(balanceFormData.newBalance&&parseFloat(balanceFormData.newBalance)-adjustingBalanceAccount.currentBalance>=0?'positive':'negative'),children:balanceFormData.newBalance?(parseFloat(balanceFormData.newBalance)-adjustingBalanceAccount.currentBalance>=0?'+':'')+formatCurrency(parseFloat(balanceFormData.newBalance)-adjustingBalanceAccount.currentBalance):'$0.00'})]})]})]}),balanceErrors.submit&&/*#__PURE__*/_jsx(\"div\",{className:\"form-error-message\",children:balanceErrors.submit}),/*#__PURE__*/_jsxs(\"div\",{className:\"form-actions\",children:[/*#__PURE__*/_jsx(\"button\",{type:\"button\",onClick:()=>setAdjustingBalanceAccount(null),className:\"btn btn-secondary\",children:\"Cancel\"}),/*#__PURE__*/_jsx(\"button\",{type:\"submit\",className:\"btn btn-primary\",children:\"Apply Adjustment\"})]})]})]}),accounts.map(account=>showBalanceHistory[account.id]&&/*#__PURE__*/_jsxs(\"div\",{className:\"balance-history-section\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"form-header\",children:[/*#__PURE__*/_jsxs(\"h3\",{children:[\"Balance History - \",account.name]}),/*#__PURE__*/_jsx(\"button\",{type:\"button\",onClick:()=>toggleBalanceHistory(account.id),className:\"btn btn-secondary\",children:\"Close\"})]}),/*#__PURE__*/_jsx(\"div\",{className:\"balance-history\",children:(()=>{const history=balanceManagementService.getBalanceHistory(account.id);const adjustments=balanceManagementService.getBalanceAdjustments(account.id);if(history.length===0&&adjustments.length===0){return/*#__PURE__*/_jsx(\"div\",{className:\"empty-history\",children:/*#__PURE__*/_jsx(\"p\",{children:\"No balance history available for this account.\"})});}return/*#__PURE__*/_jsx(\"div\",{className:\"history-content\",children:adjustments.length>0&&/*#__PURE__*/_jsxs(\"div\",{className:\"adjustments-section\",children:[/*#__PURE__*/_jsx(\"h4\",{children:\"Balance Adjustments\"}),/*#__PURE__*/_jsx(\"div\",{className:\"adjustments-list\",children:adjustments.map(adjustment=>/*#__PURE__*/_jsxs(\"div\",{className:\"adjustment-item\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"adjustment-header\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"adjustment-date\",children:adjustment.date}),/*#__PURE__*/_jsxs(\"span\",{className:\"adjustment-amount \".concat(adjustment.adjustmentAmount>=0?'positive':'negative'),children:[adjustment.adjustmentAmount>=0?'+':'',formatCurrency(adjustment.adjustmentAmount)]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"adjustment-details\",children:[/*#__PURE__*/_jsxs(\"p\",{children:[/*#__PURE__*/_jsx(\"strong\",{children:\"Reason:\"}),\" \",adjustment.reason]}),/*#__PURE__*/_jsxs(\"p\",{children:[/*#__PURE__*/_jsx(\"strong\",{children:\"Previous Balance:\"}),\" \",formatCurrency(adjustment.previousBalance)]}),/*#__PURE__*/_jsxs(\"p\",{children:[/*#__PURE__*/_jsx(\"strong\",{children:\"New Balance:\"}),\" \",formatCurrency(adjustment.newBalance)]}),/*#__PURE__*/_jsxs(\"p\",{children:[/*#__PURE__*/_jsx(\"strong\",{children:\"Type:\"}),\" \",adjustment.type]})]})]},adjustment.id))})]})});})()})]},\"history-\".concat(account.id)))]});};", "map": {"version": 3, "names": ["React", "useState", "useCallback", "bankAccountService", "balanceManagementService", "jsx", "_jsx", "jsxs", "_jsxs", "BankAccountManager", "_ref", "onAccountsUpdated", "accounts", "setAccounts", "getAllAccounts", "isAddingAccount", "setIsAddingAccount", "editingAccount", "setEditingAccount", "adjustingBalanceAccount", "setAdjustingBalanceAccount", "formData", "setFormData", "name", "accountNumber", "bankName", "currency", "currentBalance", "balanceFormData", "setBalanceFormData", "newBalance", "effectiveDate", "Date", "toISOString", "split", "reason", "errors", "setErrors", "balanceErrors", "setBalanceErrors", "showBalanceHistory", "setShowBalanceHistory", "refreshAccounts", "resetForm", "validateForm", "data", "newErrors", "trim", "some", "acc", "id", "balance", "parseFloat", "isNaN", "handleInputChange", "field", "value", "prev", "_objectSpread", "handleAddAccount", "handleEditAccount", "account", "toFixed", "handleSubmit", "e", "preventDefault", "Object", "keys", "length", "accountData", "updateAccount", "addAccount", "error", "console", "submit", "handleDeleteAccount", "accountId", "window", "confirm", "deleteAccount", "handleAdjustBalance", "validateBalanceForm", "handleBalanceSubmit", "updatedAccount", "updateAccountBalance", "toggleBalanceHistory", "formatCurrency", "amount", "Intl", "NumberFormat", "style", "format", "className", "children", "type", "onClick", "width", "height", "viewBox", "fill", "stroke", "strokeWidth", "x", "y", "rx", "ry", "x1", "y1", "x2", "y2", "map", "title", "d", "points", "onSubmit", "htmlFor", "onChange", "target", "concat", "placeholder", "step", "disabled", "history", "getBalanceHistory", "adjustments", "getBalanceAdjustments", "adjustment", "date", "adjustmentAmount", "previousBalance"], "sources": ["C:/tmsft/src/components/BankAccountManager.tsx"], "sourcesContent": ["import React, { useState, useCallback } from 'react';\r\nimport { BankAccount } from '../types';\r\nimport { bankAccountService } from '../services/bankAccountService';\r\nimport { balanceManagementService } from '../services/balanceManagementService';\r\nimport './BankAccountManager.css';\r\n\r\ninterface BankAccountManagerProps {\r\n  onAccountsUpdated?: () => void;\r\n}\r\n\r\ninterface AccountFormData {\r\n  name: string;\r\n  accountNumber: string;\r\n  bankName: string;\r\n  currency: string;\r\n  currentBalance: string;\r\n}\r\n\r\ninterface BalanceAdjustmentFormData {\r\n  newBalance: string;\r\n  effectiveDate: string;\r\n  reason: string;\r\n}\r\n\r\nexport const BankAccountManager: React.FC<BankAccountManagerProps> = ({ onAccountsUpdated }) => {\r\n  const [accounts, setAccounts] = useState<BankAccount[]>(bankAccountService.getAllAccounts());\r\n  const [isAddingAccount, setIsAddingAccount] = useState(false);\r\n  const [editingAccount, setEditingAccount] = useState<BankAccount | null>(null);\r\n  const [adjustingBalanceAccount, setAdjustingBalanceAccount] = useState<BankAccount | null>(null);\r\n  const [formData, setFormData] = useState<AccountFormData>({\r\n    name: '',\r\n    accountNumber: '',\r\n    bankName: '',\r\n    currency: 'USD',\r\n    currentBalance: '0.00'\r\n  });\r\n  const [balanceFormData, setBalanceFormData] = useState<BalanceAdjustmentFormData>({\r\n    newBalance: '',\r\n    effectiveDate: new Date().toISOString().split('T')[0],\r\n    reason: ''\r\n  });\r\n  const [errors, setErrors] = useState<Record<string, string>>({});\r\n  const [balanceErrors, setBalanceErrors] = useState<Record<string, string>>({});\r\n  const [showBalanceHistory, setShowBalanceHistory] = useState<Record<string, boolean>>({});\r\n\r\n  const refreshAccounts = useCallback(() => {\r\n    setAccounts(bankAccountService.getAllAccounts());\r\n    if (onAccountsUpdated) {\r\n      onAccountsUpdated();\r\n    }\r\n  }, [onAccountsUpdated]);\r\n\r\n  const resetForm = useCallback(() => {\r\n    setFormData({\r\n      name: '',\r\n      accountNumber: '',\r\n      bankName: '',\r\n      currency: 'USD',\r\n      currentBalance: '0.00'\r\n    });\r\n    setErrors({});\r\n    setIsAddingAccount(false);\r\n    setEditingAccount(null);\r\n  }, []);\r\n\r\n  const validateForm = useCallback((data: AccountFormData): Record<string, string> => {\r\n    const newErrors: Record<string, string> = {};\r\n\r\n    if (!data.name.trim()) {\r\n      newErrors.name = 'Account name is required';\r\n    }\r\n\r\n    if (!data.accountNumber.trim()) {\r\n      newErrors.accountNumber = 'Account number is required';\r\n    } else if (accounts.some(acc => \r\n      acc.accountNumber === data.accountNumber.trim() && \r\n      (!editingAccount || acc.id !== editingAccount.id)\r\n    )) {\r\n      newErrors.accountNumber = 'Account number already exists';\r\n    }\r\n\r\n    if (!data.bankName.trim()) {\r\n      newErrors.bankName = 'Bank name is required';\r\n    }\r\n\r\n    if (!data.currency.trim()) {\r\n      newErrors.currency = 'Currency is required';\r\n    }\r\n\r\n    const balance = parseFloat(data.currentBalance);\r\n    if (isNaN(balance)) {\r\n      newErrors.currentBalance = 'Current balance must be a valid number';\r\n    }\r\n\r\n    return newErrors;\r\n  }, [accounts, editingAccount]);\r\n\r\n  const handleInputChange = useCallback((field: keyof AccountFormData, value: string) => {\r\n    setFormData(prev => ({ ...prev, [field]: value }));\r\n    // Clear error for this field when user starts typing\r\n    if (errors[field]) {\r\n      setErrors(prev => ({ ...prev, [field]: '' }));\r\n    }\r\n  }, [errors]);\r\n\r\n  const handleAddAccount = useCallback(() => {\r\n    setIsAddingAccount(true);\r\n    setEditingAccount(null);\r\n    resetForm();\r\n  }, [resetForm]);\r\n\r\n  const handleEditAccount = useCallback((account: BankAccount) => {\r\n    setEditingAccount(account);\r\n    setIsAddingAccount(false);\r\n    setFormData({\r\n      name: account.name,\r\n      accountNumber: account.accountNumber,\r\n      bankName: account.bankName,\r\n      currency: account.currency,\r\n      currentBalance: account.currentBalance.toFixed(2)\r\n    });\r\n    setErrors({});\r\n  }, []);\r\n\r\n  const handleSubmit = useCallback((e: React.FormEvent) => {\r\n    e.preventDefault();\r\n    \r\n    const newErrors = validateForm(formData);\r\n    if (Object.keys(newErrors).length > 0) {\r\n      setErrors(newErrors);\r\n      return;\r\n    }\r\n\r\n    const accountData = {\r\n      name: formData.name.trim(),\r\n      accountNumber: formData.accountNumber.trim(),\r\n      bankName: formData.bankName.trim(),\r\n      currency: formData.currency.trim() as 'SAR' | 'USD' | 'AED',\r\n      currentBalance: parseFloat(formData.currentBalance)\r\n    };\r\n\r\n    try {\r\n      if (editingAccount) {\r\n        bankAccountService.updateAccount(editingAccount.id, accountData);\r\n      } else {\r\n        bankAccountService.addAccount(accountData);\r\n      }\r\n      \r\n      refreshAccounts();\r\n      resetForm();\r\n    } catch (error) {\r\n      console.error('Error saving account:', error);\r\n      setErrors({ submit: 'Failed to save account. Please try again.' });\r\n    }\r\n  }, [formData, validateForm, editingAccount, refreshAccounts, resetForm]);\r\n\r\n  const handleDeleteAccount = useCallback((accountId: string) => {\r\n    if (window.confirm('Are you sure you want to delete this account? This action cannot be undone.')) {\r\n      bankAccountService.deleteAccount(accountId);\r\n      refreshAccounts();\r\n    }\r\n  }, [refreshAccounts]);\r\n\r\n  // Balance adjustment methods\r\n  const handleAdjustBalance = useCallback((account: BankAccount) => {\r\n    setAdjustingBalanceAccount(account);\r\n    setBalanceFormData({\r\n      newBalance: account.currentBalance.toFixed(2),\r\n      effectiveDate: new Date().toISOString().split('T')[0],\r\n      reason: ''\r\n    });\r\n    setBalanceErrors({});\r\n  }, []);\r\n\r\n  const validateBalanceForm = useCallback((data: BalanceAdjustmentFormData): Record<string, string> => {\r\n    const newErrors: Record<string, string> = {};\r\n\r\n    const balance = parseFloat(data.newBalance);\r\n    if (isNaN(balance)) {\r\n      newErrors.newBalance = 'Balance must be a valid number';\r\n    }\r\n\r\n    if (!data.effectiveDate) {\r\n      newErrors.effectiveDate = 'Effective date is required';\r\n    }\r\n\r\n    if (!data.reason.trim()) {\r\n      newErrors.reason = 'Reason for adjustment is required';\r\n    }\r\n\r\n    return newErrors;\r\n  }, []);\r\n\r\n  const handleBalanceSubmit = useCallback((e: React.FormEvent) => {\r\n    e.preventDefault();\r\n    \r\n    if (!adjustingBalanceAccount) return;\r\n\r\n    const newErrors = validateBalanceForm(balanceFormData);\r\n    if (Object.keys(newErrors).length > 0) {\r\n      setBalanceErrors(newErrors);\r\n      return;\r\n    }\r\n\r\n    try {\r\n      const newBalance = parseFloat(balanceFormData.newBalance);\r\n      const updatedAccount = balanceManagementService.updateAccountBalance(\r\n        adjustingBalanceAccount,\r\n        newBalance,\r\n        balanceFormData.effectiveDate,\r\n        balanceFormData.reason\r\n      );\r\n\r\n      // Update the account in the service\r\n      bankAccountService.updateAccount(updatedAccount.id, {\r\n        name: updatedAccount.name,\r\n        accountNumber: updatedAccount.accountNumber,\r\n        bankName: updatedAccount.bankName,\r\n        currency: updatedAccount.currency,\r\n        currentBalance: updatedAccount.currentBalance\r\n      });\r\n\r\n      refreshAccounts();\r\n      setAdjustingBalanceAccount(null);\r\n      setBalanceFormData({\r\n        newBalance: '',\r\n        effectiveDate: new Date().toISOString().split('T')[0],\r\n        reason: ''\r\n      });\r\n    } catch (error) {\r\n      console.error('Error adjusting balance:', error);\r\n      setBalanceErrors({ submit: 'Failed to adjust balance. Please try again.' });\r\n    }\r\n  }, [adjustingBalanceAccount, balanceFormData, validateBalanceForm, refreshAccounts]);\r\n\r\n  const toggleBalanceHistory = useCallback((accountId: string) => {\r\n    setShowBalanceHistory(prev => ({\r\n      ...prev,\r\n      [accountId]: !prev[accountId]\r\n    }));\r\n  }, []);\r\n\r\n  const formatCurrency = (amount: number): string => {\r\n    return new Intl.NumberFormat('en-US', {\r\n      style: 'currency',\r\n      currency: 'USD'\r\n    }).format(amount);\r\n  };\r\n\r\n  return (\r\n    <div className=\"bank-account-manager\">\r\n      <div className=\"manager-header\">\r\n        <h2>Bank Account Management</h2>\r\n        <p>Add, edit, and manage your bank accounts</p>\r\n      </div>\r\n\r\n      <div className=\"accounts-section\">\r\n        <div className=\"section-header\">\r\n          <h3>Your Bank Accounts ({accounts.length})</h3>\r\n          <button\r\n            type=\"button\"\r\n            onClick={handleAddAccount}\r\n            className=\"btn btn-primary\"\r\n          >\r\n            Add New Account\r\n          </button>\r\n        </div>\r\n\r\n        {accounts.length === 0 ? (\r\n          <div className=\"empty-state\">\r\n            <div className=\"empty-icon\">\r\n              <svg width=\"48\" height=\"48\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"1\">\r\n                <rect x=\"2\" y=\"3\" width=\"20\" height=\"14\" rx=\"2\" ry=\"2\" />\r\n                <line x1=\"2\" y1=\"12\" x2=\"22\" y2=\"12\" />\r\n              </svg>\r\n            </div>\r\n            <h3>No Bank Accounts</h3>\r\n            <p>Get started by adding your first bank account</p>\r\n          </div>\r\n        ) : (\r\n          <div className=\"accounts-grid\">\r\n            {accounts.map((account) => (\r\n              <div key={account.id} className=\"account-card\">\r\n                <div className=\"account-header\">\r\n                  <h4>{account.name}</h4>\r\n                  <div className=\"account-actions\">\r\n                    <button\r\n                      type=\"button\"\r\n                      onClick={() => handleAdjustBalance(account)}\r\n                      className=\"btn-icon\"\r\n                      title=\"Adjust balance\"\r\n                    >\r\n                      <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\">\r\n                        <path d=\"M12 2v20M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6\" />\r\n                      </svg>\r\n                    </button>\r\n                    <button\r\n                      type=\"button\"\r\n                      onClick={() => toggleBalanceHistory(account.id)}\r\n                      className=\"btn-icon\"\r\n                      title=\"View balance history\"\r\n                    >\r\n                      <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\">\r\n                        <path d=\"M3 3v5h5M3 8l4-4 4 4 8-8\" />\r\n                      </svg>\r\n                    </button>\r\n                    <button\r\n                      type=\"button\"\r\n                      onClick={() => handleEditAccount(account)}\r\n                      className=\"btn-icon\"\r\n                      title=\"Edit account\"\r\n                    >\r\n                      <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\">\r\n                        <path d=\"M12 20h9\" />\r\n                        <path d=\"M16.5 3.5a2.121 2.121 0 0 1 3 3L7 19l-4 1 1-4L16.5 3.5z\" />\r\n                      </svg>\r\n                    </button>\r\n                    <button\r\n                      type=\"button\"\r\n                      onClick={() => handleDeleteAccount(account.id)}\r\n                      className=\"btn-icon btn-danger\"\r\n                      title=\"Delete account\"\r\n                    >\r\n                      <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\">\r\n                        <polyline points=\"3,6 5,6 21,6\" />\r\n                        <path d=\"M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2\" />\r\n                      </svg>\r\n                    </button>\r\n                  </div>\r\n                </div>\r\n                <div className=\"account-details\">\r\n                  <p><strong>Bank:</strong> {account.bankName}</p>\r\n                  <p><strong>Account Number:</strong> {account.accountNumber}</p>\r\n                  <p><strong>Currency:</strong> {account.currency}</p>\r\n                  <p><strong>Current Balance:</strong> <span className=\"balance\">{formatCurrency(account.currentBalance)}</span></p>\r\n                </div>\r\n              </div>\r\n            ))}\r\n          </div>\r\n        )}\r\n      </div>\r\n\r\n      {(isAddingAccount || editingAccount) && (\r\n        <div className=\"account-form-section\">\r\n          <div className=\"form-header\">\r\n            <h3>{editingAccount ? 'Edit Account' : 'Add New Account'}</h3>\r\n          </div>\r\n          \r\n          <form onSubmit={handleSubmit} className=\"account-form\">\r\n            <div className=\"form-row\">\r\n              <div className=\"form-group\">\r\n                <label htmlFor=\"account-name\" className=\"form-label\">\r\n                  Account Name *\r\n                </label>\r\n                <input\r\n                  id=\"account-name\"\r\n                  type=\"text\"\r\n                  value={formData.name}\r\n                  onChange={(e) => handleInputChange('name', e.target.value)}\r\n                  className={`form-input ${errors.name ? 'error' : ''}`}\r\n                  placeholder=\"e.g., Main Operating Account\"\r\n                />\r\n                {errors.name && <span className=\"form-error\">{errors.name}</span>}\r\n              </div>\r\n\r\n              <div className=\"form-group\">\r\n                <label htmlFor=\"account-number\" className=\"form-label\">\r\n                  Account Number *\r\n                </label>\r\n                <input\r\n                  id=\"account-number\"\r\n                  type=\"text\"\r\n                  value={formData.accountNumber}\r\n                  onChange={(e) => handleInputChange('accountNumber', e.target.value)}\r\n                  className={`form-input ${errors.accountNumber ? 'error' : ''}`}\r\n                  placeholder=\"e.g., **********\"\r\n                />\r\n                {errors.accountNumber && <span className=\"form-error\">{errors.accountNumber}</span>}\r\n              </div>\r\n            </div>\r\n\r\n            <div className=\"form-row\">\r\n              <div className=\"form-group\">\r\n                <label htmlFor=\"bank-name\" className=\"form-label\">\r\n                  Bank Name *\r\n                </label>\r\n                <input\r\n                  id=\"bank-name\"\r\n                  type=\"text\"\r\n                  value={formData.bankName}\r\n                  onChange={(e) => handleInputChange('bankName', e.target.value)}\r\n                  className={`form-input ${errors.bankName ? 'error' : ''}`}\r\n                  placeholder=\"e.g., First National Bank\"\r\n                />\r\n                {errors.bankName && <span className=\"form-error\">{errors.bankName}</span>}\r\n              </div>\r\n\r\n              <div className=\"form-group\">\r\n                <label htmlFor=\"currency\" className=\"form-label\">\r\n                  Currency *\r\n                </label>\r\n                <select\r\n                  id=\"currency\"\r\n                  value={formData.currency}\r\n                  onChange={(e) => handleInputChange('currency', e.target.value)}\r\n                  className={`form-select ${errors.currency ? 'error' : ''}`}\r\n                >\r\n                  <option value=\"USD\">USD - US Dollar</option>\r\n                  <option value=\"EUR\">EUR - Euro</option>\r\n                  <option value=\"GBP\">GBP - British Pound</option>\r\n                  <option value=\"CAD\">CAD - Canadian Dollar</option>\r\n                  <option value=\"AUD\">AUD - Australian Dollar</option>\r\n                </select>\r\n                {errors.currency && <span className=\"form-error\">{errors.currency}</span>}\r\n              </div>\r\n            </div>\r\n\r\n            <div className=\"form-row\">\r\n              <div className=\"form-group\">\r\n                <label htmlFor=\"current-balance\" className=\"form-label\">\r\n                  Current Balance *\r\n                </label>\r\n                <input\r\n                  id=\"current-balance\"\r\n                  type=\"number\"\r\n                  step=\"0.01\"\r\n                  value={formData.currentBalance}\r\n                  onChange={(e) => handleInputChange('currentBalance', e.target.value)}\r\n                  className={`form-input ${errors.currentBalance ? 'error' : ''}`}\r\n                  placeholder=\"0.00\"\r\n                />\r\n                {errors.currentBalance && <span className=\"form-error\">{errors.currentBalance}</span>}\r\n              </div>\r\n            </div>\r\n\r\n            {errors.submit && (\r\n              <div className=\"form-error-message\">\r\n                {errors.submit}\r\n              </div>\r\n            )}\r\n\r\n            <div className=\"form-actions\">\r\n              <button\r\n                type=\"button\"\r\n                onClick={resetForm}\r\n                className=\"btn btn-secondary\"\r\n              >\r\n                Cancel\r\n              </button>\r\n              <button\r\n                type=\"submit\"\r\n                className=\"btn btn-primary\"\r\n              >\r\n                {editingAccount ? 'Update Account' : 'Add Account'}\r\n              </button>\r\n            </div>\r\n          </form>\r\n        </div>\r\n      )}\r\n\r\n      {/* Balance Adjustment Form */}\r\n      {adjustingBalanceAccount && (\r\n        <div className=\"balance-adjustment-section\">\r\n          <div className=\"form-header\">\r\n            <h3>Adjust Balance - {adjustingBalanceAccount.name}</h3>\r\n            <p>Update the account balance with an effective date and reason</p>\r\n          </div>\r\n          \r\n          <form onSubmit={handleBalanceSubmit} className=\"balance-form\">\r\n            <div className=\"form-row\">\r\n              <div className=\"form-group\">\r\n                <label htmlFor=\"current-balance-display\" className=\"form-label\">\r\n                  Current Balance\r\n                </label>\r\n                <input\r\n                  id=\"current-balance-display\"\r\n                  type=\"text\"\r\n                  value={formatCurrency(adjustingBalanceAccount.currentBalance)}\r\n                  disabled\r\n                  className=\"form-input disabled\"\r\n                />\r\n              </div>\r\n\r\n              <div className=\"form-group\">\r\n                <label htmlFor=\"new-balance\" className=\"form-label\">\r\n                  New Balance *\r\n                </label>\r\n                <input\r\n                  id=\"new-balance\"\r\n                  type=\"number\"\r\n                  step=\"0.01\"\r\n                  value={balanceFormData.newBalance}\r\n                  onChange={(e) => setBalanceFormData(prev => ({ ...prev, newBalance: e.target.value }))}\r\n                  className={`form-input ${balanceErrors.newBalance ? 'error' : ''}`}\r\n                  placeholder=\"0.00\"\r\n                />\r\n                {balanceErrors.newBalance && <span className=\"form-error\">{balanceErrors.newBalance}</span>}\r\n              </div>\r\n            </div>\r\n\r\n            <div className=\"form-row\">\r\n              <div className=\"form-group\">\r\n                <label htmlFor=\"effective-date\" className=\"form-label\">\r\n                  Effective Date *\r\n                </label>\r\n                <input\r\n                  id=\"effective-date\"\r\n                  type=\"date\"\r\n                  value={balanceFormData.effectiveDate}\r\n                  onChange={(e) => setBalanceFormData(prev => ({ ...prev, effectiveDate: e.target.value }))}\r\n                  className={`form-input ${balanceErrors.effectiveDate ? 'error' : ''}`}\r\n                />\r\n                {balanceErrors.effectiveDate && <span className=\"form-error\">{balanceErrors.effectiveDate}</span>}\r\n              </div>\r\n\r\n              <div className=\"form-group\">\r\n                <label htmlFor=\"adjustment-reason\" className=\"form-label\">\r\n                  Reason for Adjustment *\r\n                </label>\r\n                <select\r\n                  id=\"adjustment-reason\"\r\n                  value={balanceFormData.reason}\r\n                  onChange={(e) => setBalanceFormData(prev => ({ ...prev, reason: e.target.value }))}\r\n                  className={`form-select ${balanceErrors.reason ? 'error' : ''}`}\r\n                >\r\n                  <option value=\"\">Select a reason...</option>\r\n                  <option value=\"Bank reconciliation\">Bank reconciliation</option>\r\n                  <option value=\"Manual correction\">Manual correction</option>\r\n                  <option value=\"Interest adjustment\">Interest adjustment</option>\r\n                  <option value=\"Fee adjustment\">Fee adjustment</option>\r\n                  <option value=\"Opening balance setup\">Opening balance setup</option>\r\n                  <option value=\"Other\">Other</option>\r\n                </select>\r\n                {balanceErrors.reason && <span className=\"form-error\">{balanceErrors.reason}</span>}\r\n              </div>\r\n            </div>\r\n\r\n            {balanceFormData.reason === 'Other' && (\r\n              <div className=\"form-row\">\r\n                <div className=\"form-group\">\r\n                  <label htmlFor=\"custom-reason\" className=\"form-label\">\r\n                    Custom Reason *\r\n                  </label>\r\n                  <input\r\n                    id=\"custom-reason\"\r\n                    type=\"text\"\r\n                    placeholder=\"Enter custom reason...\"\r\n                    className=\"form-input\"\r\n                    onChange={(e) => setBalanceFormData(prev => ({ ...prev, reason: e.target.value }))}\r\n                  />\r\n                </div>\r\n              </div>\r\n            )}\r\n\r\n            <div className=\"adjustment-preview\">\r\n              <h4>Adjustment Preview</h4>\r\n              <div className=\"preview-details\">\r\n                <p><strong>Current Balance:</strong> {formatCurrency(adjustingBalanceAccount.currentBalance)}</p>\r\n                <p><strong>New Balance:</strong> {balanceFormData.newBalance ? formatCurrency(parseFloat(balanceFormData.newBalance)) : '$0.00'}</p>\r\n                <p><strong>Adjustment Amount:</strong> \r\n                  <span className={`adjustment-amount ${balanceFormData.newBalance && parseFloat(balanceFormData.newBalance) - adjustingBalanceAccount.currentBalance >= 0 ? 'positive' : 'negative'}`}>\r\n                    {balanceFormData.newBalance ? \r\n                      (parseFloat(balanceFormData.newBalance) - adjustingBalanceAccount.currentBalance >= 0 ? '+' : '') + \r\n                      formatCurrency(parseFloat(balanceFormData.newBalance) - adjustingBalanceAccount.currentBalance) : \r\n                      '$0.00'\r\n                    }\r\n                  </span>\r\n                </p>\r\n              </div>\r\n            </div>\r\n\r\n            {balanceErrors.submit && (\r\n              <div className=\"form-error-message\">\r\n                {balanceErrors.submit}\r\n              </div>\r\n            )}\r\n\r\n            <div className=\"form-actions\">\r\n              <button\r\n                type=\"button\"\r\n                onClick={() => setAdjustingBalanceAccount(null)}\r\n                className=\"btn btn-secondary\"\r\n              >\r\n                Cancel\r\n              </button>\r\n              <button\r\n                type=\"submit\"\r\n                className=\"btn btn-primary\"\r\n              >\r\n                Apply Adjustment\r\n              </button>\r\n            </div>\r\n          </form>\r\n        </div>\r\n      )}\r\n\r\n      {/* Balance History Display */}\r\n      {accounts.map(account => \r\n        showBalanceHistory[account.id] && (\r\n          <div key={`history-${account.id}`} className=\"balance-history-section\">\r\n            <div className=\"form-header\">\r\n              <h3>Balance History - {account.name}</h3>\r\n              <button\r\n                type=\"button\"\r\n                onClick={() => toggleBalanceHistory(account.id)}\r\n                className=\"btn btn-secondary\"\r\n              >\r\n                Close\r\n              </button>\r\n            </div>\r\n            \r\n            <div className=\"balance-history\">\r\n              {(() => {\r\n                const history = balanceManagementService.getBalanceHistory(account.id);\r\n                const adjustments = balanceManagementService.getBalanceAdjustments(account.id);\r\n                \r\n                if (history.length === 0 && adjustments.length === 0) {\r\n                  return (\r\n                    <div className=\"empty-history\">\r\n                      <p>No balance history available for this account.</p>\r\n                    </div>\r\n                  );\r\n                }\r\n\r\n                return (\r\n                  <div className=\"history-content\">\r\n                    {adjustments.length > 0 && (\r\n                      <div className=\"adjustments-section\">\r\n                        <h4>Balance Adjustments</h4>\r\n                        <div className=\"adjustments-list\">\r\n                          {adjustments.map(adjustment => (\r\n                            <div key={adjustment.id} className=\"adjustment-item\">\r\n                              <div className=\"adjustment-header\">\r\n                                <span className=\"adjustment-date\">{adjustment.date}</span>\r\n                                <span className={`adjustment-amount ${adjustment.adjustmentAmount >= 0 ? 'positive' : 'negative'}`}>\r\n                                  {adjustment.adjustmentAmount >= 0 ? '+' : ''}{formatCurrency(adjustment.adjustmentAmount)}\r\n                                </span>\r\n                              </div>\r\n                              <div className=\"adjustment-details\">\r\n                                <p><strong>Reason:</strong> {adjustment.reason}</p>\r\n                                <p><strong>Previous Balance:</strong> {formatCurrency(adjustment.previousBalance)}</p>\r\n                                <p><strong>New Balance:</strong> {formatCurrency(adjustment.newBalance)}</p>\r\n                                <p><strong>Type:</strong> {adjustment.type}</p>\r\n                              </div>\r\n                            </div>\r\n                          ))}\r\n                        </div>\r\n                      </div>\r\n                    )}\r\n                  </div>\r\n                );\r\n              })()}\r\n            </div>\r\n          </div>\r\n        )\r\n      )}\r\n    </div>\r\n  );\r\n}; "], "mappings": "6FAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,WAAW,KAAQ,OAAO,CAEpD,OAASC,kBAAkB,KAAQ,gCAAgC,CACnE,OAASC,wBAAwB,KAAQ,sCAAsC,CAC/E,MAAO,0BAA0B,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAoBlC,MAAO,MAAM,CAAAC,kBAAqD,CAAGC,IAAA,EAA2B,IAA1B,CAAEC,iBAAkB,CAAC,CAAAD,IAAA,CACzF,KAAM,CAACE,QAAQ,CAAEC,WAAW,CAAC,CAAGZ,QAAQ,CAAgBE,kBAAkB,CAACW,cAAc,CAAC,CAAC,CAAC,CAC5F,KAAM,CAACC,eAAe,CAAEC,kBAAkB,CAAC,CAAGf,QAAQ,CAAC,KAAK,CAAC,CAC7D,KAAM,CAACgB,cAAc,CAAEC,iBAAiB,CAAC,CAAGjB,QAAQ,CAAqB,IAAI,CAAC,CAC9E,KAAM,CAACkB,uBAAuB,CAAEC,0BAA0B,CAAC,CAAGnB,QAAQ,CAAqB,IAAI,CAAC,CAChG,KAAM,CAACoB,QAAQ,CAAEC,WAAW,CAAC,CAAGrB,QAAQ,CAAkB,CACxDsB,IAAI,CAAE,EAAE,CACRC,aAAa,CAAE,EAAE,CACjBC,QAAQ,CAAE,EAAE,CACZC,QAAQ,CAAE,KAAK,CACfC,cAAc,CAAE,MAClB,CAAC,CAAC,CACF,KAAM,CAACC,eAAe,CAAEC,kBAAkB,CAAC,CAAG5B,QAAQ,CAA4B,CAChF6B,UAAU,CAAE,EAAE,CACdC,aAAa,CAAE,GAAI,CAAAC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CACrDC,MAAM,CAAE,EACV,CAAC,CAAC,CACF,KAAM,CAACC,MAAM,CAAEC,SAAS,CAAC,CAAGpC,QAAQ,CAAyB,CAAC,CAAC,CAAC,CAChE,KAAM,CAACqC,aAAa,CAAEC,gBAAgB,CAAC,CAAGtC,QAAQ,CAAyB,CAAC,CAAC,CAAC,CAC9E,KAAM,CAACuC,kBAAkB,CAAEC,qBAAqB,CAAC,CAAGxC,QAAQ,CAA0B,CAAC,CAAC,CAAC,CAEzF,KAAM,CAAAyC,eAAe,CAAGxC,WAAW,CAAC,IAAM,CACxCW,WAAW,CAACV,kBAAkB,CAACW,cAAc,CAAC,CAAC,CAAC,CAChD,GAAIH,iBAAiB,CAAE,CACrBA,iBAAiB,CAAC,CAAC,CACrB,CACF,CAAC,CAAE,CAACA,iBAAiB,CAAC,CAAC,CAEvB,KAAM,CAAAgC,SAAS,CAAGzC,WAAW,CAAC,IAAM,CAClCoB,WAAW,CAAC,CACVC,IAAI,CAAE,EAAE,CACRC,aAAa,CAAE,EAAE,CACjBC,QAAQ,CAAE,EAAE,CACZC,QAAQ,CAAE,KAAK,CACfC,cAAc,CAAE,MAClB,CAAC,CAAC,CACFU,SAAS,CAAC,CAAC,CAAC,CAAC,CACbrB,kBAAkB,CAAC,KAAK,CAAC,CACzBE,iBAAiB,CAAC,IAAI,CAAC,CACzB,CAAC,CAAE,EAAE,CAAC,CAEN,KAAM,CAAA0B,YAAY,CAAG1C,WAAW,CAAE2C,IAAqB,EAA6B,CAClF,KAAM,CAAAC,SAAiC,CAAG,CAAC,CAAC,CAE5C,GAAI,CAACD,IAAI,CAACtB,IAAI,CAACwB,IAAI,CAAC,CAAC,CAAE,CACrBD,SAAS,CAACvB,IAAI,CAAG,0BAA0B,CAC7C,CAEA,GAAI,CAACsB,IAAI,CAACrB,aAAa,CAACuB,IAAI,CAAC,CAAC,CAAE,CAC9BD,SAAS,CAACtB,aAAa,CAAG,4BAA4B,CACxD,CAAC,IAAM,IAAIZ,QAAQ,CAACoC,IAAI,CAACC,GAAG,EAC1BA,GAAG,CAACzB,aAAa,GAAKqB,IAAI,CAACrB,aAAa,CAACuB,IAAI,CAAC,CAAC,GAC9C,CAAC9B,cAAc,EAAIgC,GAAG,CAACC,EAAE,GAAKjC,cAAc,CAACiC,EAAE,CAClD,CAAC,CAAE,CACDJ,SAAS,CAACtB,aAAa,CAAG,+BAA+B,CAC3D,CAEA,GAAI,CAACqB,IAAI,CAACpB,QAAQ,CAACsB,IAAI,CAAC,CAAC,CAAE,CACzBD,SAAS,CAACrB,QAAQ,CAAG,uBAAuB,CAC9C,CAEA,GAAI,CAACoB,IAAI,CAACnB,QAAQ,CAACqB,IAAI,CAAC,CAAC,CAAE,CACzBD,SAAS,CAACpB,QAAQ,CAAG,sBAAsB,CAC7C,CAEA,KAAM,CAAAyB,OAAO,CAAGC,UAAU,CAACP,IAAI,CAAClB,cAAc,CAAC,CAC/C,GAAI0B,KAAK,CAACF,OAAO,CAAC,CAAE,CAClBL,SAAS,CAACnB,cAAc,CAAG,wCAAwC,CACrE,CAEA,MAAO,CAAAmB,SAAS,CAClB,CAAC,CAAE,CAAClC,QAAQ,CAAEK,cAAc,CAAC,CAAC,CAE9B,KAAM,CAAAqC,iBAAiB,CAAGpD,WAAW,CAAC,CAACqD,KAA4B,CAAEC,KAAa,GAAK,CACrFlC,WAAW,CAACmC,IAAI,EAAAC,aAAA,CAAAA,aAAA,IAAUD,IAAI,MAAE,CAACF,KAAK,EAAGC,KAAK,EAAG,CAAC,CAClD;AACA,GAAIpB,MAAM,CAACmB,KAAK,CAAC,CAAE,CACjBlB,SAAS,CAACoB,IAAI,EAAAC,aAAA,CAAAA,aAAA,IAAUD,IAAI,MAAE,CAACF,KAAK,EAAG,EAAE,EAAG,CAAC,CAC/C,CACF,CAAC,CAAE,CAACnB,MAAM,CAAC,CAAC,CAEZ,KAAM,CAAAuB,gBAAgB,CAAGzD,WAAW,CAAC,IAAM,CACzCc,kBAAkB,CAAC,IAAI,CAAC,CACxBE,iBAAiB,CAAC,IAAI,CAAC,CACvByB,SAAS,CAAC,CAAC,CACb,CAAC,CAAE,CAACA,SAAS,CAAC,CAAC,CAEf,KAAM,CAAAiB,iBAAiB,CAAG1D,WAAW,CAAE2D,OAAoB,EAAK,CAC9D3C,iBAAiB,CAAC2C,OAAO,CAAC,CAC1B7C,kBAAkB,CAAC,KAAK,CAAC,CACzBM,WAAW,CAAC,CACVC,IAAI,CAAEsC,OAAO,CAACtC,IAAI,CAClBC,aAAa,CAAEqC,OAAO,CAACrC,aAAa,CACpCC,QAAQ,CAAEoC,OAAO,CAACpC,QAAQ,CAC1BC,QAAQ,CAAEmC,OAAO,CAACnC,QAAQ,CAC1BC,cAAc,CAAEkC,OAAO,CAAClC,cAAc,CAACmC,OAAO,CAAC,CAAC,CAClD,CAAC,CAAC,CACFzB,SAAS,CAAC,CAAC,CAAC,CAAC,CACf,CAAC,CAAE,EAAE,CAAC,CAEN,KAAM,CAAA0B,YAAY,CAAG7D,WAAW,CAAE8D,CAAkB,EAAK,CACvDA,CAAC,CAACC,cAAc,CAAC,CAAC,CAElB,KAAM,CAAAnB,SAAS,CAAGF,YAAY,CAACvB,QAAQ,CAAC,CACxC,GAAI6C,MAAM,CAACC,IAAI,CAACrB,SAAS,CAAC,CAACsB,MAAM,CAAG,CAAC,CAAE,CACrC/B,SAAS,CAACS,SAAS,CAAC,CACpB,OACF,CAEA,KAAM,CAAAuB,WAAW,CAAG,CAClB9C,IAAI,CAAEF,QAAQ,CAACE,IAAI,CAACwB,IAAI,CAAC,CAAC,CAC1BvB,aAAa,CAAEH,QAAQ,CAACG,aAAa,CAACuB,IAAI,CAAC,CAAC,CAC5CtB,QAAQ,CAAEJ,QAAQ,CAACI,QAAQ,CAACsB,IAAI,CAAC,CAAC,CAClCrB,QAAQ,CAAEL,QAAQ,CAACK,QAAQ,CAACqB,IAAI,CAAC,CAA0B,CAC3DpB,cAAc,CAAEyB,UAAU,CAAC/B,QAAQ,CAACM,cAAc,CACpD,CAAC,CAED,GAAI,CACF,GAAIV,cAAc,CAAE,CAClBd,kBAAkB,CAACmE,aAAa,CAACrD,cAAc,CAACiC,EAAE,CAAEmB,WAAW,CAAC,CAClE,CAAC,IAAM,CACLlE,kBAAkB,CAACoE,UAAU,CAACF,WAAW,CAAC,CAC5C,CAEA3B,eAAe,CAAC,CAAC,CACjBC,SAAS,CAAC,CAAC,CACb,CAAE,MAAO6B,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,uBAAuB,CAAEA,KAAK,CAAC,CAC7CnC,SAAS,CAAC,CAAEqC,MAAM,CAAE,2CAA4C,CAAC,CAAC,CACpE,CACF,CAAC,CAAE,CAACrD,QAAQ,CAAEuB,YAAY,CAAE3B,cAAc,CAAEyB,eAAe,CAAEC,SAAS,CAAC,CAAC,CAExE,KAAM,CAAAgC,mBAAmB,CAAGzE,WAAW,CAAE0E,SAAiB,EAAK,CAC7D,GAAIC,MAAM,CAACC,OAAO,CAAC,6EAA6E,CAAC,CAAE,CACjG3E,kBAAkB,CAAC4E,aAAa,CAACH,SAAS,CAAC,CAC3ClC,eAAe,CAAC,CAAC,CACnB,CACF,CAAC,CAAE,CAACA,eAAe,CAAC,CAAC,CAErB;AACA,KAAM,CAAAsC,mBAAmB,CAAG9E,WAAW,CAAE2D,OAAoB,EAAK,CAChEzC,0BAA0B,CAACyC,OAAO,CAAC,CACnChC,kBAAkB,CAAC,CACjBC,UAAU,CAAE+B,OAAO,CAAClC,cAAc,CAACmC,OAAO,CAAC,CAAC,CAAC,CAC7C/B,aAAa,CAAE,GAAI,CAAAC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CACrDC,MAAM,CAAE,EACV,CAAC,CAAC,CACFI,gBAAgB,CAAC,CAAC,CAAC,CAAC,CACtB,CAAC,CAAE,EAAE,CAAC,CAEN,KAAM,CAAA0C,mBAAmB,CAAG/E,WAAW,CAAE2C,IAA+B,EAA6B,CACnG,KAAM,CAAAC,SAAiC,CAAG,CAAC,CAAC,CAE5C,KAAM,CAAAK,OAAO,CAAGC,UAAU,CAACP,IAAI,CAACf,UAAU,CAAC,CAC3C,GAAIuB,KAAK,CAACF,OAAO,CAAC,CAAE,CAClBL,SAAS,CAAChB,UAAU,CAAG,gCAAgC,CACzD,CAEA,GAAI,CAACe,IAAI,CAACd,aAAa,CAAE,CACvBe,SAAS,CAACf,aAAa,CAAG,4BAA4B,CACxD,CAEA,GAAI,CAACc,IAAI,CAACV,MAAM,CAACY,IAAI,CAAC,CAAC,CAAE,CACvBD,SAAS,CAACX,MAAM,CAAG,mCAAmC,CACxD,CAEA,MAAO,CAAAW,SAAS,CAClB,CAAC,CAAE,EAAE,CAAC,CAEN,KAAM,CAAAoC,mBAAmB,CAAGhF,WAAW,CAAE8D,CAAkB,EAAK,CAC9DA,CAAC,CAACC,cAAc,CAAC,CAAC,CAElB,GAAI,CAAC9C,uBAAuB,CAAE,OAE9B,KAAM,CAAA2B,SAAS,CAAGmC,mBAAmB,CAACrD,eAAe,CAAC,CACtD,GAAIsC,MAAM,CAACC,IAAI,CAACrB,SAAS,CAAC,CAACsB,MAAM,CAAG,CAAC,CAAE,CACrC7B,gBAAgB,CAACO,SAAS,CAAC,CAC3B,OACF,CAEA,GAAI,CACF,KAAM,CAAAhB,UAAU,CAAGsB,UAAU,CAACxB,eAAe,CAACE,UAAU,CAAC,CACzD,KAAM,CAAAqD,cAAc,CAAG/E,wBAAwB,CAACgF,oBAAoB,CAClEjE,uBAAuB,CACvBW,UAAU,CACVF,eAAe,CAACG,aAAa,CAC7BH,eAAe,CAACO,MAClB,CAAC,CAED;AACAhC,kBAAkB,CAACmE,aAAa,CAACa,cAAc,CAACjC,EAAE,CAAE,CAClD3B,IAAI,CAAE4D,cAAc,CAAC5D,IAAI,CACzBC,aAAa,CAAE2D,cAAc,CAAC3D,aAAa,CAC3CC,QAAQ,CAAE0D,cAAc,CAAC1D,QAAQ,CACjCC,QAAQ,CAAEyD,cAAc,CAACzD,QAAQ,CACjCC,cAAc,CAAEwD,cAAc,CAACxD,cACjC,CAAC,CAAC,CAEFe,eAAe,CAAC,CAAC,CACjBtB,0BAA0B,CAAC,IAAI,CAAC,CAChCS,kBAAkB,CAAC,CACjBC,UAAU,CAAE,EAAE,CACdC,aAAa,CAAE,GAAI,CAAAC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CACrDC,MAAM,CAAE,EACV,CAAC,CAAC,CACJ,CAAE,MAAOqC,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,0BAA0B,CAAEA,KAAK,CAAC,CAChDjC,gBAAgB,CAAC,CAAEmC,MAAM,CAAE,6CAA8C,CAAC,CAAC,CAC7E,CACF,CAAC,CAAE,CAACvD,uBAAuB,CAAES,eAAe,CAAEqD,mBAAmB,CAAEvC,eAAe,CAAC,CAAC,CAEpF,KAAM,CAAA2C,oBAAoB,CAAGnF,WAAW,CAAE0E,SAAiB,EAAK,CAC9DnC,qBAAqB,CAACgB,IAAI,EAAAC,aAAA,CAAAA,aAAA,IACrBD,IAAI,MACP,CAACmB,SAAS,EAAG,CAACnB,IAAI,CAACmB,SAAS,CAAC,EAC7B,CAAC,CACL,CAAC,CAAE,EAAE,CAAC,CAEN,KAAM,CAAAU,cAAc,CAAIC,MAAc,EAAa,CACjD,MAAO,IAAI,CAAAC,IAAI,CAACC,YAAY,CAAC,OAAO,CAAE,CACpCC,KAAK,CAAE,UAAU,CACjBhE,QAAQ,CAAE,KACZ,CAAC,CAAC,CAACiE,MAAM,CAACJ,MAAM,CAAC,CACnB,CAAC,CAED,mBACE/E,KAAA,QAAKoF,SAAS,CAAC,sBAAsB,CAAAC,QAAA,eACnCrF,KAAA,QAAKoF,SAAS,CAAC,gBAAgB,CAAAC,QAAA,eAC7BvF,IAAA,OAAAuF,QAAA,CAAI,yBAAuB,CAAI,CAAC,cAChCvF,IAAA,MAAAuF,QAAA,CAAG,0CAAwC,CAAG,CAAC,EAC5C,CAAC,cAENrF,KAAA,QAAKoF,SAAS,CAAC,kBAAkB,CAAAC,QAAA,eAC/BrF,KAAA,QAAKoF,SAAS,CAAC,gBAAgB,CAAAC,QAAA,eAC7BrF,KAAA,OAAAqF,QAAA,EAAI,sBAAoB,CAACjF,QAAQ,CAACwD,MAAM,CAAC,GAAC,EAAI,CAAC,cAC/C9D,IAAA,WACEwF,IAAI,CAAC,QAAQ,CACbC,OAAO,CAAEpC,gBAAiB,CAC1BiC,SAAS,CAAC,iBAAiB,CAAAC,QAAA,CAC5B,iBAED,CAAQ,CAAC,EACN,CAAC,CAELjF,QAAQ,CAACwD,MAAM,GAAK,CAAC,cACpB5D,KAAA,QAAKoF,SAAS,CAAC,aAAa,CAAAC,QAAA,eAC1BvF,IAAA,QAAKsF,SAAS,CAAC,YAAY,CAAAC,QAAA,cACzBrF,KAAA,QAAKwF,KAAK,CAAC,IAAI,CAACC,MAAM,CAAC,IAAI,CAACC,OAAO,CAAC,WAAW,CAACC,IAAI,CAAC,MAAM,CAACC,MAAM,CAAC,cAAc,CAACC,WAAW,CAAC,GAAG,CAAAR,QAAA,eAC/FvF,IAAA,SAAMgG,CAAC,CAAC,GAAG,CAACC,CAAC,CAAC,GAAG,CAACP,KAAK,CAAC,IAAI,CAACC,MAAM,CAAC,IAAI,CAACO,EAAE,CAAC,GAAG,CAACC,EAAE,CAAC,GAAG,CAAE,CAAC,cACzDnG,IAAA,SAAMoG,EAAE,CAAC,GAAG,CAACC,EAAE,CAAC,IAAI,CAACC,EAAE,CAAC,IAAI,CAACC,EAAE,CAAC,IAAI,CAAE,CAAC,EACpC,CAAC,CACH,CAAC,cACNvG,IAAA,OAAAuF,QAAA,CAAI,kBAAgB,CAAI,CAAC,cACzBvF,IAAA,MAAAuF,QAAA,CAAG,+CAA6C,CAAG,CAAC,EACjD,CAAC,cAENvF,IAAA,QAAKsF,SAAS,CAAC,eAAe,CAAAC,QAAA,CAC3BjF,QAAQ,CAACkG,GAAG,CAAEjD,OAAO,eACpBrD,KAAA,QAAsBoF,SAAS,CAAC,cAAc,CAAAC,QAAA,eAC5CrF,KAAA,QAAKoF,SAAS,CAAC,gBAAgB,CAAAC,QAAA,eAC7BvF,IAAA,OAAAuF,QAAA,CAAKhC,OAAO,CAACtC,IAAI,CAAK,CAAC,cACvBf,KAAA,QAAKoF,SAAS,CAAC,iBAAiB,CAAAC,QAAA,eAC9BvF,IAAA,WACEwF,IAAI,CAAC,QAAQ,CACbC,OAAO,CAAEA,CAAA,GAAMf,mBAAmB,CAACnB,OAAO,CAAE,CAC5C+B,SAAS,CAAC,UAAU,CACpBmB,KAAK,CAAC,gBAAgB,CAAAlB,QAAA,cAEtBvF,IAAA,QAAK0F,KAAK,CAAC,IAAI,CAACC,MAAM,CAAC,IAAI,CAACC,OAAO,CAAC,WAAW,CAACC,IAAI,CAAC,MAAM,CAACC,MAAM,CAAC,cAAc,CAACC,WAAW,CAAC,GAAG,CAAAR,QAAA,cAC/FvF,IAAA,SAAM0G,CAAC,CAAC,2DAA2D,CAAE,CAAC,CACnE,CAAC,CACA,CAAC,cACT1G,IAAA,WACEwF,IAAI,CAAC,QAAQ,CACbC,OAAO,CAAEA,CAAA,GAAMV,oBAAoB,CAACxB,OAAO,CAACX,EAAE,CAAE,CAChD0C,SAAS,CAAC,UAAU,CACpBmB,KAAK,CAAC,sBAAsB,CAAAlB,QAAA,cAE5BvF,IAAA,QAAK0F,KAAK,CAAC,IAAI,CAACC,MAAM,CAAC,IAAI,CAACC,OAAO,CAAC,WAAW,CAACC,IAAI,CAAC,MAAM,CAACC,MAAM,CAAC,cAAc,CAACC,WAAW,CAAC,GAAG,CAAAR,QAAA,cAC/FvF,IAAA,SAAM0G,CAAC,CAAC,0BAA0B,CAAE,CAAC,CAClC,CAAC,CACA,CAAC,cACT1G,IAAA,WACEwF,IAAI,CAAC,QAAQ,CACbC,OAAO,CAAEA,CAAA,GAAMnC,iBAAiB,CAACC,OAAO,CAAE,CAC1C+B,SAAS,CAAC,UAAU,CACpBmB,KAAK,CAAC,cAAc,CAAAlB,QAAA,cAEpBrF,KAAA,QAAKwF,KAAK,CAAC,IAAI,CAACC,MAAM,CAAC,IAAI,CAACC,OAAO,CAAC,WAAW,CAACC,IAAI,CAAC,MAAM,CAACC,MAAM,CAAC,cAAc,CAACC,WAAW,CAAC,GAAG,CAAAR,QAAA,eAC/FvF,IAAA,SAAM0G,CAAC,CAAC,UAAU,CAAE,CAAC,cACrB1G,IAAA,SAAM0G,CAAC,CAAC,yDAAyD,CAAE,CAAC,EACjE,CAAC,CACA,CAAC,cACT1G,IAAA,WACEwF,IAAI,CAAC,QAAQ,CACbC,OAAO,CAAEA,CAAA,GAAMpB,mBAAmB,CAACd,OAAO,CAACX,EAAE,CAAE,CAC/C0C,SAAS,CAAC,qBAAqB,CAC/BmB,KAAK,CAAC,gBAAgB,CAAAlB,QAAA,cAEtBrF,KAAA,QAAKwF,KAAK,CAAC,IAAI,CAACC,MAAM,CAAC,IAAI,CAACC,OAAO,CAAC,WAAW,CAACC,IAAI,CAAC,MAAM,CAACC,MAAM,CAAC,cAAc,CAACC,WAAW,CAAC,GAAG,CAAAR,QAAA,eAC/FvF,IAAA,aAAU2G,MAAM,CAAC,cAAc,CAAE,CAAC,cAClC3G,IAAA,SAAM0G,CAAC,CAAC,gFAAgF,CAAE,CAAC,EACxF,CAAC,CACA,CAAC,EACN,CAAC,EACH,CAAC,cACNxG,KAAA,QAAKoF,SAAS,CAAC,iBAAiB,CAAAC,QAAA,eAC9BrF,KAAA,MAAAqF,QAAA,eAAGvF,IAAA,WAAAuF,QAAA,CAAQ,OAAK,CAAQ,CAAC,IAAC,CAAChC,OAAO,CAACpC,QAAQ,EAAI,CAAC,cAChDjB,KAAA,MAAAqF,QAAA,eAAGvF,IAAA,WAAAuF,QAAA,CAAQ,iBAAe,CAAQ,CAAC,IAAC,CAAChC,OAAO,CAACrC,aAAa,EAAI,CAAC,cAC/DhB,KAAA,MAAAqF,QAAA,eAAGvF,IAAA,WAAAuF,QAAA,CAAQ,WAAS,CAAQ,CAAC,IAAC,CAAChC,OAAO,CAACnC,QAAQ,EAAI,CAAC,cACpDlB,KAAA,MAAAqF,QAAA,eAAGvF,IAAA,WAAAuF,QAAA,CAAQ,kBAAgB,CAAQ,CAAC,IAAC,cAAAvF,IAAA,SAAMsF,SAAS,CAAC,SAAS,CAAAC,QAAA,CAAEP,cAAc,CAACzB,OAAO,CAAClC,cAAc,CAAC,CAAO,CAAC,EAAG,CAAC,EAC/G,CAAC,GArDEkC,OAAO,CAACX,EAsDb,CACN,CAAC,CACC,CACN,EACE,CAAC,CAEL,CAACnC,eAAe,EAAIE,cAAc,gBACjCT,KAAA,QAAKoF,SAAS,CAAC,sBAAsB,CAAAC,QAAA,eACnCvF,IAAA,QAAKsF,SAAS,CAAC,aAAa,CAAAC,QAAA,cAC1BvF,IAAA,OAAAuF,QAAA,CAAK5E,cAAc,CAAG,cAAc,CAAG,iBAAiB,CAAK,CAAC,CAC3D,CAAC,cAENT,KAAA,SAAM0G,QAAQ,CAAEnD,YAAa,CAAC6B,SAAS,CAAC,cAAc,CAAAC,QAAA,eACpDrF,KAAA,QAAKoF,SAAS,CAAC,UAAU,CAAAC,QAAA,eACvBrF,KAAA,QAAKoF,SAAS,CAAC,YAAY,CAAAC,QAAA,eACzBvF,IAAA,UAAO6G,OAAO,CAAC,cAAc,CAACvB,SAAS,CAAC,YAAY,CAAAC,QAAA,CAAC,gBAErD,CAAO,CAAC,cACRvF,IAAA,UACE4C,EAAE,CAAC,cAAc,CACjB4C,IAAI,CAAC,MAAM,CACXtC,KAAK,CAAEnC,QAAQ,CAACE,IAAK,CACrB6F,QAAQ,CAAGpD,CAAC,EAAKV,iBAAiB,CAAC,MAAM,CAAEU,CAAC,CAACqD,MAAM,CAAC7D,KAAK,CAAE,CAC3DoC,SAAS,eAAA0B,MAAA,CAAgBlF,MAAM,CAACb,IAAI,CAAG,OAAO,CAAG,EAAE,CAAG,CACtDgG,WAAW,CAAC,8BAA8B,CAC3C,CAAC,CACDnF,MAAM,CAACb,IAAI,eAAIjB,IAAA,SAAMsF,SAAS,CAAC,YAAY,CAAAC,QAAA,CAAEzD,MAAM,CAACb,IAAI,CAAO,CAAC,EAC9D,CAAC,cAENf,KAAA,QAAKoF,SAAS,CAAC,YAAY,CAAAC,QAAA,eACzBvF,IAAA,UAAO6G,OAAO,CAAC,gBAAgB,CAACvB,SAAS,CAAC,YAAY,CAAAC,QAAA,CAAC,kBAEvD,CAAO,CAAC,cACRvF,IAAA,UACE4C,EAAE,CAAC,gBAAgB,CACnB4C,IAAI,CAAC,MAAM,CACXtC,KAAK,CAAEnC,QAAQ,CAACG,aAAc,CAC9B4F,QAAQ,CAAGpD,CAAC,EAAKV,iBAAiB,CAAC,eAAe,CAAEU,CAAC,CAACqD,MAAM,CAAC7D,KAAK,CAAE,CACpEoC,SAAS,eAAA0B,MAAA,CAAgBlF,MAAM,CAACZ,aAAa,CAAG,OAAO,CAAG,EAAE,CAAG,CAC/D+F,WAAW,CAAC,kBAAkB,CAC/B,CAAC,CACDnF,MAAM,CAACZ,aAAa,eAAIlB,IAAA,SAAMsF,SAAS,CAAC,YAAY,CAAAC,QAAA,CAAEzD,MAAM,CAACZ,aAAa,CAAO,CAAC,EAChF,CAAC,EACH,CAAC,cAENhB,KAAA,QAAKoF,SAAS,CAAC,UAAU,CAAAC,QAAA,eACvBrF,KAAA,QAAKoF,SAAS,CAAC,YAAY,CAAAC,QAAA,eACzBvF,IAAA,UAAO6G,OAAO,CAAC,WAAW,CAACvB,SAAS,CAAC,YAAY,CAAAC,QAAA,CAAC,aAElD,CAAO,CAAC,cACRvF,IAAA,UACE4C,EAAE,CAAC,WAAW,CACd4C,IAAI,CAAC,MAAM,CACXtC,KAAK,CAAEnC,QAAQ,CAACI,QAAS,CACzB2F,QAAQ,CAAGpD,CAAC,EAAKV,iBAAiB,CAAC,UAAU,CAAEU,CAAC,CAACqD,MAAM,CAAC7D,KAAK,CAAE,CAC/DoC,SAAS,eAAA0B,MAAA,CAAgBlF,MAAM,CAACX,QAAQ,CAAG,OAAO,CAAG,EAAE,CAAG,CAC1D8F,WAAW,CAAC,2BAA2B,CACxC,CAAC,CACDnF,MAAM,CAACX,QAAQ,eAAInB,IAAA,SAAMsF,SAAS,CAAC,YAAY,CAAAC,QAAA,CAAEzD,MAAM,CAACX,QAAQ,CAAO,CAAC,EACtE,CAAC,cAENjB,KAAA,QAAKoF,SAAS,CAAC,YAAY,CAAAC,QAAA,eACzBvF,IAAA,UAAO6G,OAAO,CAAC,UAAU,CAACvB,SAAS,CAAC,YAAY,CAAAC,QAAA,CAAC,YAEjD,CAAO,CAAC,cACRrF,KAAA,WACE0C,EAAE,CAAC,UAAU,CACbM,KAAK,CAAEnC,QAAQ,CAACK,QAAS,CACzB0F,QAAQ,CAAGpD,CAAC,EAAKV,iBAAiB,CAAC,UAAU,CAAEU,CAAC,CAACqD,MAAM,CAAC7D,KAAK,CAAE,CAC/DoC,SAAS,gBAAA0B,MAAA,CAAiBlF,MAAM,CAACV,QAAQ,CAAG,OAAO,CAAG,EAAE,CAAG,CAAAmE,QAAA,eAE3DvF,IAAA,WAAQkD,KAAK,CAAC,KAAK,CAAAqC,QAAA,CAAC,iBAAe,CAAQ,CAAC,cAC5CvF,IAAA,WAAQkD,KAAK,CAAC,KAAK,CAAAqC,QAAA,CAAC,YAAU,CAAQ,CAAC,cACvCvF,IAAA,WAAQkD,KAAK,CAAC,KAAK,CAAAqC,QAAA,CAAC,qBAAmB,CAAQ,CAAC,cAChDvF,IAAA,WAAQkD,KAAK,CAAC,KAAK,CAAAqC,QAAA,CAAC,uBAAqB,CAAQ,CAAC,cAClDvF,IAAA,WAAQkD,KAAK,CAAC,KAAK,CAAAqC,QAAA,CAAC,yBAAuB,CAAQ,CAAC,EAC9C,CAAC,CACRzD,MAAM,CAACV,QAAQ,eAAIpB,IAAA,SAAMsF,SAAS,CAAC,YAAY,CAAAC,QAAA,CAAEzD,MAAM,CAACV,QAAQ,CAAO,CAAC,EACtE,CAAC,EACH,CAAC,cAENpB,IAAA,QAAKsF,SAAS,CAAC,UAAU,CAAAC,QAAA,cACvBrF,KAAA,QAAKoF,SAAS,CAAC,YAAY,CAAAC,QAAA,eACzBvF,IAAA,UAAO6G,OAAO,CAAC,iBAAiB,CAACvB,SAAS,CAAC,YAAY,CAAAC,QAAA,CAAC,mBAExD,CAAO,CAAC,cACRvF,IAAA,UACE4C,EAAE,CAAC,iBAAiB,CACpB4C,IAAI,CAAC,QAAQ,CACb0B,IAAI,CAAC,MAAM,CACXhE,KAAK,CAAEnC,QAAQ,CAACM,cAAe,CAC/ByF,QAAQ,CAAGpD,CAAC,EAAKV,iBAAiB,CAAC,gBAAgB,CAAEU,CAAC,CAACqD,MAAM,CAAC7D,KAAK,CAAE,CACrEoC,SAAS,eAAA0B,MAAA,CAAgBlF,MAAM,CAACT,cAAc,CAAG,OAAO,CAAG,EAAE,CAAG,CAChE4F,WAAW,CAAC,MAAM,CACnB,CAAC,CACDnF,MAAM,CAACT,cAAc,eAAIrB,IAAA,SAAMsF,SAAS,CAAC,YAAY,CAAAC,QAAA,CAAEzD,MAAM,CAACT,cAAc,CAAO,CAAC,EAClF,CAAC,CACH,CAAC,CAELS,MAAM,CAACsC,MAAM,eACZpE,IAAA,QAAKsF,SAAS,CAAC,oBAAoB,CAAAC,QAAA,CAChCzD,MAAM,CAACsC,MAAM,CACX,CACN,cAEDlE,KAAA,QAAKoF,SAAS,CAAC,cAAc,CAAAC,QAAA,eAC3BvF,IAAA,WACEwF,IAAI,CAAC,QAAQ,CACbC,OAAO,CAAEpD,SAAU,CACnBiD,SAAS,CAAC,mBAAmB,CAAAC,QAAA,CAC9B,QAED,CAAQ,CAAC,cACTvF,IAAA,WACEwF,IAAI,CAAC,QAAQ,CACbF,SAAS,CAAC,iBAAiB,CAAAC,QAAA,CAE1B5E,cAAc,CAAG,gBAAgB,CAAG,aAAa,CAC5C,CAAC,EACN,CAAC,EACF,CAAC,EACJ,CACN,CAGAE,uBAAuB,eACtBX,KAAA,QAAKoF,SAAS,CAAC,4BAA4B,CAAAC,QAAA,eACzCrF,KAAA,QAAKoF,SAAS,CAAC,aAAa,CAAAC,QAAA,eAC1BrF,KAAA,OAAAqF,QAAA,EAAI,mBAAiB,CAAC1E,uBAAuB,CAACI,IAAI,EAAK,CAAC,cACxDjB,IAAA,MAAAuF,QAAA,CAAG,8DAA4D,CAAG,CAAC,EAChE,CAAC,cAENrF,KAAA,SAAM0G,QAAQ,CAAEhC,mBAAoB,CAACU,SAAS,CAAC,cAAc,CAAAC,QAAA,eAC3DrF,KAAA,QAAKoF,SAAS,CAAC,UAAU,CAAAC,QAAA,eACvBrF,KAAA,QAAKoF,SAAS,CAAC,YAAY,CAAAC,QAAA,eACzBvF,IAAA,UAAO6G,OAAO,CAAC,yBAAyB,CAACvB,SAAS,CAAC,YAAY,CAAAC,QAAA,CAAC,iBAEhE,CAAO,CAAC,cACRvF,IAAA,UACE4C,EAAE,CAAC,yBAAyB,CAC5B4C,IAAI,CAAC,MAAM,CACXtC,KAAK,CAAE8B,cAAc,CAACnE,uBAAuB,CAACQ,cAAc,CAAE,CAC9D8F,QAAQ,MACR7B,SAAS,CAAC,qBAAqB,CAChC,CAAC,EACC,CAAC,cAENpF,KAAA,QAAKoF,SAAS,CAAC,YAAY,CAAAC,QAAA,eACzBvF,IAAA,UAAO6G,OAAO,CAAC,aAAa,CAACvB,SAAS,CAAC,YAAY,CAAAC,QAAA,CAAC,eAEpD,CAAO,CAAC,cACRvF,IAAA,UACE4C,EAAE,CAAC,aAAa,CAChB4C,IAAI,CAAC,QAAQ,CACb0B,IAAI,CAAC,MAAM,CACXhE,KAAK,CAAE5B,eAAe,CAACE,UAAW,CAClCsF,QAAQ,CAAGpD,CAAC,EAAKnC,kBAAkB,CAAC4B,IAAI,EAAAC,aAAA,CAAAA,aAAA,IAAUD,IAAI,MAAE3B,UAAU,CAAEkC,CAAC,CAACqD,MAAM,CAAC7D,KAAK,EAAG,CAAE,CACvFoC,SAAS,eAAA0B,MAAA,CAAgBhF,aAAa,CAACR,UAAU,CAAG,OAAO,CAAG,EAAE,CAAG,CACnEyF,WAAW,CAAC,MAAM,CACnB,CAAC,CACDjF,aAAa,CAACR,UAAU,eAAIxB,IAAA,SAAMsF,SAAS,CAAC,YAAY,CAAAC,QAAA,CAAEvD,aAAa,CAACR,UAAU,CAAO,CAAC,EACxF,CAAC,EACH,CAAC,cAENtB,KAAA,QAAKoF,SAAS,CAAC,UAAU,CAAAC,QAAA,eACvBrF,KAAA,QAAKoF,SAAS,CAAC,YAAY,CAAAC,QAAA,eACzBvF,IAAA,UAAO6G,OAAO,CAAC,gBAAgB,CAACvB,SAAS,CAAC,YAAY,CAAAC,QAAA,CAAC,kBAEvD,CAAO,CAAC,cACRvF,IAAA,UACE4C,EAAE,CAAC,gBAAgB,CACnB4C,IAAI,CAAC,MAAM,CACXtC,KAAK,CAAE5B,eAAe,CAACG,aAAc,CACrCqF,QAAQ,CAAGpD,CAAC,EAAKnC,kBAAkB,CAAC4B,IAAI,EAAAC,aAAA,CAAAA,aAAA,IAAUD,IAAI,MAAE1B,aAAa,CAAEiC,CAAC,CAACqD,MAAM,CAAC7D,KAAK,EAAG,CAAE,CAC1FoC,SAAS,eAAA0B,MAAA,CAAgBhF,aAAa,CAACP,aAAa,CAAG,OAAO,CAAG,EAAE,CAAG,CACvE,CAAC,CACDO,aAAa,CAACP,aAAa,eAAIzB,IAAA,SAAMsF,SAAS,CAAC,YAAY,CAAAC,QAAA,CAAEvD,aAAa,CAACP,aAAa,CAAO,CAAC,EAC9F,CAAC,cAENvB,KAAA,QAAKoF,SAAS,CAAC,YAAY,CAAAC,QAAA,eACzBvF,IAAA,UAAO6G,OAAO,CAAC,mBAAmB,CAACvB,SAAS,CAAC,YAAY,CAAAC,QAAA,CAAC,yBAE1D,CAAO,CAAC,cACRrF,KAAA,WACE0C,EAAE,CAAC,mBAAmB,CACtBM,KAAK,CAAE5B,eAAe,CAACO,MAAO,CAC9BiF,QAAQ,CAAGpD,CAAC,EAAKnC,kBAAkB,CAAC4B,IAAI,EAAAC,aAAA,CAAAA,aAAA,IAAUD,IAAI,MAAEtB,MAAM,CAAE6B,CAAC,CAACqD,MAAM,CAAC7D,KAAK,EAAG,CAAE,CACnFoC,SAAS,gBAAA0B,MAAA,CAAiBhF,aAAa,CAACH,MAAM,CAAG,OAAO,CAAG,EAAE,CAAG,CAAA0D,QAAA,eAEhEvF,IAAA,WAAQkD,KAAK,CAAC,EAAE,CAAAqC,QAAA,CAAC,oBAAkB,CAAQ,CAAC,cAC5CvF,IAAA,WAAQkD,KAAK,CAAC,qBAAqB,CAAAqC,QAAA,CAAC,qBAAmB,CAAQ,CAAC,cAChEvF,IAAA,WAAQkD,KAAK,CAAC,mBAAmB,CAAAqC,QAAA,CAAC,mBAAiB,CAAQ,CAAC,cAC5DvF,IAAA,WAAQkD,KAAK,CAAC,qBAAqB,CAAAqC,QAAA,CAAC,qBAAmB,CAAQ,CAAC,cAChEvF,IAAA,WAAQkD,KAAK,CAAC,gBAAgB,CAAAqC,QAAA,CAAC,gBAAc,CAAQ,CAAC,cACtDvF,IAAA,WAAQkD,KAAK,CAAC,uBAAuB,CAAAqC,QAAA,CAAC,uBAAqB,CAAQ,CAAC,cACpEvF,IAAA,WAAQkD,KAAK,CAAC,OAAO,CAAAqC,QAAA,CAAC,OAAK,CAAQ,CAAC,EAC9B,CAAC,CACRvD,aAAa,CAACH,MAAM,eAAI7B,IAAA,SAAMsF,SAAS,CAAC,YAAY,CAAAC,QAAA,CAAEvD,aAAa,CAACH,MAAM,CAAO,CAAC,EAChF,CAAC,EACH,CAAC,CAELP,eAAe,CAACO,MAAM,GAAK,OAAO,eACjC7B,IAAA,QAAKsF,SAAS,CAAC,UAAU,CAAAC,QAAA,cACvBrF,KAAA,QAAKoF,SAAS,CAAC,YAAY,CAAAC,QAAA,eACzBvF,IAAA,UAAO6G,OAAO,CAAC,eAAe,CAACvB,SAAS,CAAC,YAAY,CAAAC,QAAA,CAAC,iBAEtD,CAAO,CAAC,cACRvF,IAAA,UACE4C,EAAE,CAAC,eAAe,CAClB4C,IAAI,CAAC,MAAM,CACXyB,WAAW,CAAC,wBAAwB,CACpC3B,SAAS,CAAC,YAAY,CACtBwB,QAAQ,CAAGpD,CAAC,EAAKnC,kBAAkB,CAAC4B,IAAI,EAAAC,aAAA,CAAAA,aAAA,IAAUD,IAAI,MAAEtB,MAAM,CAAE6B,CAAC,CAACqD,MAAM,CAAC7D,KAAK,EAAG,CAAE,CACpF,CAAC,EACC,CAAC,CACH,CACN,cAEDhD,KAAA,QAAKoF,SAAS,CAAC,oBAAoB,CAAAC,QAAA,eACjCvF,IAAA,OAAAuF,QAAA,CAAI,oBAAkB,CAAI,CAAC,cAC3BrF,KAAA,QAAKoF,SAAS,CAAC,iBAAiB,CAAAC,QAAA,eAC9BrF,KAAA,MAAAqF,QAAA,eAAGvF,IAAA,WAAAuF,QAAA,CAAQ,kBAAgB,CAAQ,CAAC,IAAC,CAACP,cAAc,CAACnE,uBAAuB,CAACQ,cAAc,CAAC,EAAI,CAAC,cACjGnB,KAAA,MAAAqF,QAAA,eAAGvF,IAAA,WAAAuF,QAAA,CAAQ,cAAY,CAAQ,CAAC,IAAC,CAACjE,eAAe,CAACE,UAAU,CAAGwD,cAAc,CAAClC,UAAU,CAACxB,eAAe,CAACE,UAAU,CAAC,CAAC,CAAG,OAAO,EAAI,CAAC,cACpItB,KAAA,MAAAqF,QAAA,eAAGvF,IAAA,WAAAuF,QAAA,CAAQ,oBAAkB,CAAQ,CAAC,cACpCvF,IAAA,SAAMsF,SAAS,sBAAA0B,MAAA,CAAuB1F,eAAe,CAACE,UAAU,EAAIsB,UAAU,CAACxB,eAAe,CAACE,UAAU,CAAC,CAAGX,uBAAuB,CAACQ,cAAc,EAAI,CAAC,CAAG,UAAU,CAAG,UAAU,CAAG,CAAAkE,QAAA,CAClLjE,eAAe,CAACE,UAAU,CACzB,CAACsB,UAAU,CAACxB,eAAe,CAACE,UAAU,CAAC,CAAGX,uBAAuB,CAACQ,cAAc,EAAI,CAAC,CAAG,GAAG,CAAG,EAAE,EAChG2D,cAAc,CAAClC,UAAU,CAACxB,eAAe,CAACE,UAAU,CAAC,CAAGX,uBAAuB,CAACQ,cAAc,CAAC,CAC/F,OAAO,CAEL,CAAC,EACN,CAAC,EACD,CAAC,EACH,CAAC,CAELW,aAAa,CAACoC,MAAM,eACnBpE,IAAA,QAAKsF,SAAS,CAAC,oBAAoB,CAAAC,QAAA,CAChCvD,aAAa,CAACoC,MAAM,CAClB,CACN,cAEDlE,KAAA,QAAKoF,SAAS,CAAC,cAAc,CAAAC,QAAA,eAC3BvF,IAAA,WACEwF,IAAI,CAAC,QAAQ,CACbC,OAAO,CAAEA,CAAA,GAAM3E,0BAA0B,CAAC,IAAI,CAAE,CAChDwE,SAAS,CAAC,mBAAmB,CAAAC,QAAA,CAC9B,QAED,CAAQ,CAAC,cACTvF,IAAA,WACEwF,IAAI,CAAC,QAAQ,CACbF,SAAS,CAAC,iBAAiB,CAAAC,QAAA,CAC5B,kBAED,CAAQ,CAAC,EACN,CAAC,EACF,CAAC,EACJ,CACN,CAGAjF,QAAQ,CAACkG,GAAG,CAACjD,OAAO,EACnBrB,kBAAkB,CAACqB,OAAO,CAACX,EAAE,CAAC,eAC5B1C,KAAA,QAAmCoF,SAAS,CAAC,yBAAyB,CAAAC,QAAA,eACpErF,KAAA,QAAKoF,SAAS,CAAC,aAAa,CAAAC,QAAA,eAC1BrF,KAAA,OAAAqF,QAAA,EAAI,oBAAkB,CAAChC,OAAO,CAACtC,IAAI,EAAK,CAAC,cACzCjB,IAAA,WACEwF,IAAI,CAAC,QAAQ,CACbC,OAAO,CAAEA,CAAA,GAAMV,oBAAoB,CAACxB,OAAO,CAACX,EAAE,CAAE,CAChD0C,SAAS,CAAC,mBAAmB,CAAAC,QAAA,CAC9B,OAED,CAAQ,CAAC,EACN,CAAC,cAENvF,IAAA,QAAKsF,SAAS,CAAC,iBAAiB,CAAAC,QAAA,CAC7B,CAAC,IAAM,CACN,KAAM,CAAA6B,OAAO,CAAGtH,wBAAwB,CAACuH,iBAAiB,CAAC9D,OAAO,CAACX,EAAE,CAAC,CACtE,KAAM,CAAA0E,WAAW,CAAGxH,wBAAwB,CAACyH,qBAAqB,CAAChE,OAAO,CAACX,EAAE,CAAC,CAE9E,GAAIwE,OAAO,CAACtD,MAAM,GAAK,CAAC,EAAIwD,WAAW,CAACxD,MAAM,GAAK,CAAC,CAAE,CACpD,mBACE9D,IAAA,QAAKsF,SAAS,CAAC,eAAe,CAAAC,QAAA,cAC5BvF,IAAA,MAAAuF,QAAA,CAAG,gDAA8C,CAAG,CAAC,CAClD,CAAC,CAEV,CAEA,mBACEvF,IAAA,QAAKsF,SAAS,CAAC,iBAAiB,CAAAC,QAAA,CAC7B+B,WAAW,CAACxD,MAAM,CAAG,CAAC,eACrB5D,KAAA,QAAKoF,SAAS,CAAC,qBAAqB,CAAAC,QAAA,eAClCvF,IAAA,OAAAuF,QAAA,CAAI,qBAAmB,CAAI,CAAC,cAC5BvF,IAAA,QAAKsF,SAAS,CAAC,kBAAkB,CAAAC,QAAA,CAC9B+B,WAAW,CAACd,GAAG,CAACgB,UAAU,eACzBtH,KAAA,QAAyBoF,SAAS,CAAC,iBAAiB,CAAAC,QAAA,eAClDrF,KAAA,QAAKoF,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAChCvF,IAAA,SAAMsF,SAAS,CAAC,iBAAiB,CAAAC,QAAA,CAAEiC,UAAU,CAACC,IAAI,CAAO,CAAC,cAC1DvH,KAAA,SAAMoF,SAAS,sBAAA0B,MAAA,CAAuBQ,UAAU,CAACE,gBAAgB,EAAI,CAAC,CAAG,UAAU,CAAG,UAAU,CAAG,CAAAnC,QAAA,EAChGiC,UAAU,CAACE,gBAAgB,EAAI,CAAC,CAAG,GAAG,CAAG,EAAE,CAAE1C,cAAc,CAACwC,UAAU,CAACE,gBAAgB,CAAC,EACrF,CAAC,EACJ,CAAC,cACNxH,KAAA,QAAKoF,SAAS,CAAC,oBAAoB,CAAAC,QAAA,eACjCrF,KAAA,MAAAqF,QAAA,eAAGvF,IAAA,WAAAuF,QAAA,CAAQ,SAAO,CAAQ,CAAC,IAAC,CAACiC,UAAU,CAAC3F,MAAM,EAAI,CAAC,cACnD3B,KAAA,MAAAqF,QAAA,eAAGvF,IAAA,WAAAuF,QAAA,CAAQ,mBAAiB,CAAQ,CAAC,IAAC,CAACP,cAAc,CAACwC,UAAU,CAACG,eAAe,CAAC,EAAI,CAAC,cACtFzH,KAAA,MAAAqF,QAAA,eAAGvF,IAAA,WAAAuF,QAAA,CAAQ,cAAY,CAAQ,CAAC,IAAC,CAACP,cAAc,CAACwC,UAAU,CAAChG,UAAU,CAAC,EAAI,CAAC,cAC5EtB,KAAA,MAAAqF,QAAA,eAAGvF,IAAA,WAAAuF,QAAA,CAAQ,OAAK,CAAQ,CAAC,IAAC,CAACiC,UAAU,CAAChC,IAAI,EAAI,CAAC,EAC5C,CAAC,GAZEgC,UAAU,CAAC5E,EAahB,CACN,CAAC,CACC,CAAC,EACH,CACN,CACE,CAAC,CAEV,CAAC,EAAE,CAAC,CACD,CAAC,cAAAoE,MAAA,CArDazD,OAAO,CAACX,EAAE,CAsD1B,CAET,CAAC,EACE,CAAC,CAEV,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}